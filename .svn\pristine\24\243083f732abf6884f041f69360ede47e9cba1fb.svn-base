//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBall.h"

#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/Roles/Competitors/RURoleGetTheBall.h"
#include "Match/AI/Roles/Competitors/RURolePenaltyAttack.h"
#include "Match/AI/Roles/Competitors/SSRoleFormation.h"
#include "Match/Ball/SSBall.h"
#include "Match/Camera/SSCameraManager.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUInputPhaseDecision.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/RugbyUnion/Rules/Triggers/RURuleTriggerPenalty.h"
#include "Match/SIFGameObject.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Match/SSRoleFactory.h"
#include "Match/SSSpatialHelper.h"
#include "Match/AI/Actions/RUActionTacklee.h"
#include "Character/RugbyCharacterAnimInstance.h"


//#rc3_legacy_include #include <NMMabAnimationEvents.h>

#include "Character/RugbyPlayerController.h"
#include "RugbyGameInstance.h"

MABRUNTIMETYPE_IMP1( RURolePlayTheBall, SSRole );

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
RURolePlayTheBall::RURolePlayTheBall( SIFGameWorld* game )
: SSRole(game)
, state{}
{
}

//-------------------------------------------------------------------------
// Enter
//-------------------------------------------------------------------------
void RURolePlayTheBall::Enter( ARugbyCharacter* player )
{
	SSRole::Enter( player );
	
	if (state == RoleState::COMPLETE || player != m_pGame->GetGameState()->GetBallHolder())
	{
		state = RoleState::COMPLETE;
		return;
	}

	// GGs JZ not 100% sure if this has resolved the side step issue: https://dev.azure.com/glindagames/NRL%20Rugby%20League/_workitems/edit/2801
	// I have had a hard time getting it to happen consistently, and can't get it to work with this change so hoping it's fix
	m_pPlayer->GetAnimation()->StopAnimation("sidestep_fail_tacklee"); 
	m_pPlayer->GetAnimation()->StopAnimation("sidestep_success_tacklee");
	
	// If we've entered this state there is the liklihood that we are down, ensure we are requesting a getup anim
	// Check which animation to play based on the weights and facing angle, just do facing angle for now see comment in header
	if (/*RandomValue <= WeightPlayBallWhileGettingUp &&*/ IsFacingPlayDirection(45.0f) && m_pGame->GetGameState()->GetHandoverType() == EHandoverType::TACKLE)
	{
		if (m_pPlayer->GetAnimation()->IsAnimationAvailable("play_the_ball_ground"))
		{
			if (!(m_pGame->GetGameSettings().game_settings.game_type == GAME_TRAINING))
			{
				RUGameState* gamestate = player->GetGameWorld()->GetGameState();
				if (gamestate)
				{
					auto ballHolder = gamestate->GetBallHolder();
					if (ballHolder)
					{
						// Check if we are a two man tackle, only do ground play the ball for a one man tackle
						// This is because the getup is apart of the two man tackle animation and could cause an issue if stopped
						RUActionTacklee* tackleAction = ballHolder->GetActionManager()->GetAction<RUActionTacklee>();

						if (!tackleAction)
						{
							UE_LOG(LogTemp, Warning, TEXT("Roleplaytheball tackleAction is null "));
						}
						else
						{
							UE_LOG(LogTemp, Warning, TEXT("Roleplaytheball Tacklers: %d"), tackleAction->GetTackleResult().n_tacklers);
						}

						if (tackleAction && tackleAction->GetTackleResult().n_tacklers <= 1)
						{
							UE_LOG(LogTemp, Warning, TEXT("Roleplaytheball one tackler do play the ball while gettting up"));
							m_pPlayer->GetAnimation()->StopAnimation(GETUP_REQUEST);
							ShouldRotate = true;
							state = RoleState::PLAYTHEBALLGROUND;
						}
					}
				}
			}
		}
	}
	else if(m_pGame->GetGameState()->GetHandoverType() == EHandoverType::TACKLE)
	{
		// Send request for just "getting up"
		m_pPlayer->GetAnimation()->GetStateMachine().SendRequest(GETUP_REQUEST, true);
	}

	player->GetMabAnimationEvent().Add( this, &RURolePlayTheBall::AnimationEvent );

	m_lock_manager.HFLockAll();

	wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
	m_pMovement->SetTargetPosition( GetInitialPosition() );

	if(m_pGame->GetGameState()->GetHandoverType() == EHandoverType::TACKLE && state != RoleState::PLAYTHEBALLGROUND)
	{
		state = RoleState::GETUP;
		m_pMovement->StopAllMovement();
	}
	else if(m_pGame->GetGameState()->GetHandoverType() != EHandoverType::TACKLE)
	{
		state = RoleState::PREPARING;
		if (m_pGame->GetGameState()->GetHandoverType() == EHandoverType::OUTONFULL)
		{
			WarpToWaypoint();
		}
	}
}

//-------------------------------------------------------------------------
// Exit
//-------------------------------------------------------------------------
void RURolePlayTheBall::Exit(bool forced)
{
	ShouldRotate = false;
	animationPlayed = false;
	//Unregister animation event
	m_pPlayer->GetMabAnimationEvent().Remove( this, &RURolePlayTheBall::AnimationEvent );
	m_lock_manager.HFClearLocks();
	SSRole::Exit(forced);
}

//-------------------------------------------------------------------------
// Update
//-------------------------------------------------------------------------
void RURolePlayTheBall::UpdateLogic( const MabTimeStep& game_time_step )
{
	SSRole::UpdateLogic(game_time_step);

	if (m_pPlayer != m_pGame->GetGameState()->GetBallHolder())
	{
		Exit(true);
		return;
	}

	if (state == RoleState::COMPLETE)
	{
		Exit(true);
		return;
	}

	if (state == RoleState::PREPARING)
	{
		wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
		m_pPlayer->GetMovement()->SetFacingFlags(AFFLAG_FACEPLAYDIR);
		m_pPlayer->GetMovement()->SetThrottleAndTargetSpeedByUrgency(BASE_MOVEMENT_URGENCY, AS_SPRINT);

		if (m_pGame->GetGameState()->GetBallHolder() != nullptr)
		{
			FVector a = m_pGame->GetGameState()->GetBallHolder()->GetMovement()->GetCurrentPosition();
			FVector b = m_pGame->GetGameState()->GetPlayRestartPosition();

			const float positionTolerance = 1.0f;
			bool has_reached_play_location = FVector::Dist(a, b) <= positionTolerance;
			if (has_reached_play_location)
			{
				state = RoleState::GETUP;
				m_pGame->GetGameState()->SetHandoverType(EHandoverType::TACKLE);
			}
		}
	}

	if ( state == RoleState::GETUP )
	{
		ShouldRotate = false;
		m_pPlayer->GetMovement()->StopAllMovement();
		m_pPlayer->GetMovement()->SetFacingFlags(AFFLAG_FACEPLAYDIR);

		if (currentWaitDuration <= waitBeforePlayingDuration)
		{
			currentWaitDuration += game_time_step.delta_time;
			return;
		}
		
		bool is_on_feet	= m_pPlayer->GetAnimation()->IsOnFeet();
		wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
		m_pPlayer->GetMovement()->SetTargetPosition( m_pPlayer->GetMabPosition(), true );
		bool in_locomotion	= m_pPlayer->GetAnimation()->HasAnyLocomotionOrIdle();
		bool has_reached_facing = m_pPlayer->GetMovement()->HasReachedFacing();

		if ( is_on_feet && in_locomotion && has_reached_facing )
		{
			
			state = RoleState::PLAYTHEBALL;
		}
	}
	else if (state == RoleState::PLAYTHEBALLGROUND)
	{
		// Rotate the player to face forwards while on the ground
		if(ShouldRotate)
			RotatePlayerToPlayDir();
		// Wait to get up
		if (currentWaitDuration <= 2)
		{
			currentWaitDuration += game_time_step.delta_time;
			return;
		}
		// Play the get up anim and state
		m_pPlayer->GetAnimation()->ChangeStateToPlayTheBall();
		StartPlayAnimation();
		state = RoleState::INHANDS;
	}
	else if(state == RoleState::PLAYTHEBALL)
	{
		if (m_pPlayer->GetAnimation()->IsOnFeet())
		{
			if (m_pPlayer->GetMovement()->HasReachedFacing())
			{
				StartPlayAnimation();
				state = RoleState::INHANDS;
			}
		}
	}
	else if (state == RoleState::INHANDS)
	{
		// we're waiting for the ball release event! and if needed to rotate.
		if (ShouldRotate)
			RotatePlayerToPlayDir();

		// Go through and check if we are standing animation, or if we have played the ball animation,
		// If we are stadning around play the animation, if we have played the animation and have not triggered the release then release.
		// Get the mesh component from the player
		USkeletalMeshComponent* mesh = m_pPlayer->GetMesh();
		if (!mesh) return;

		// Get the animation instance and cast to URugbyCharacterAnimInstance
		URugbyCharacterAnimInstance* animInstance = Cast<URugbyCharacterAnimInstance>(mesh->GetAnimInstance());
		if (!animInstance) return;

		// Get the currently active montage
		UAnimMontage* montage = animInstance->GetCurrentActiveMontage();
		if (!montage) return;

		// Get the current position in the montage timeline
		float position = animInstance->Montage_GetPosition(montage);

		// Access the first slot track
		const FSlotAnimationTrack* activeTrack = montage->SlotAnimTracks.Num() > 0 ? &montage->SlotAnimTracks[0] : nullptr;
		if (!activeTrack) return;

		// Loop through each animation segment in the slot's track
		for (const FAnimSegment& segment : activeTrack->AnimTrack.AnimSegments)
		{
			// Make sure the segment has a valid animation reference
			if (!segment.AnimReference) continue;

			// Calculate the segment's time range within the montage
			float start = segment.StartPos;
			float end = start + segment.AnimReference->GetPlayLength();

			// Check if the current montage time is within this segment
			if (position >= start && position <= end)
			{
				// Get the name of the animation currently playing
				const FString& animName = segment.AnimReference->GetName();
				UE_LOG(LogTemp, Warning, TEXT("Currently playing animation: %s"), *animName);

				// Play the ball animation if we are standing there
				if (animName.Contains(TEXT("wbstand")) && m_pPlayer->GetAnimation()->IsOnFeet())
				{
					// Work around fix for animation notif not triggering release or if we haven't played the ball yet
					if (!animationPlayed)
					{
						state = RoleState::GETUP;
					}
					else
					{
						state = RoleState::RELEASE;
					}
				}
				else if (animName.Contains(TEXT("play_the_ball")))
				{
					animationPlayed = true;
				}
			}
		}
	}
	else if (state == RoleState::RELEASE)
	{
		// #MLW - TODO Awaiting play the ball aniamtions!
		// Currently this just unsets the ball holder, in RURolePlayTheBallReceiver, once there is no ball holder it will assign it to them
		// Depending on how the animation goes, this might work fine? Might need some finessing? Either way, we are relying on the BALL_RELEAESED_EVENT, in UE this relates 
		// to "BallRelease" anim notifs on animation timelines
		ShouldRotate = false;
		animationPlayed = false;
		m_pGame->GetBall()->SetHolder(NULL);
		FVector starting_pos = m_pMovement->GetCurrentPosition();
		FVector offset = FVector(0, 0, 0.1f * -m_pPlayer->GetAttributes()->GetPlayDirection());
		FVector pass_pos = starting_pos + offset;
		m_pGame->GetGameState()->Pass(m_pPlayer, nullptr, pass_pos, PASS_TYPE::PT_PLAYTHEBALL, true);
		m_pGame->GetBall()->StopAllMotion();
		state = RoleState::COMPLETE;
	}
	
//	SSRole::UpdateLogic( game_time_step );
}

//-------------------------------------------------------------------------
// GetFitness
//-------------------------------------------------------------------------
int RURolePlayTheBall::GetFitness(const ARugbyCharacter* player, const SSRoleArea* area)
{
	RUGameState* state = player->GetGameWorld()->GetGameState();
	if ( state->GetPhase() == RUGamePhase::PLAY_THE_BALL )
	{
		if( player == state->GetBallHolder() )
		{
			return 100000;
		}
	}

	return GetPlaymakerFitness( player, area );
}

//-------------------------------------------------------------------------
// IsInterruptable
//-------------------------------------------------------------------------
bool RURolePlayTheBall::IsInterruptable() const
{
	return state == RoleState::COMPLETE;
}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
void RURolePlayTheBall::AnimationEvent(float /*time*/, ERugbyAnimEvent event, size_t /*userdata*/, bool /*bIsBlendingOut = false*/)
{
	if(event == ERugbyAnimEvent::FOOTSTEPS_EVENT || event == ERugbyAnimEvent::IK_TRANSITION_EVENT)
		return;

	wwNETWORK_TRACE_JG("RUActionPass::AnimationEvent: %d event(%s), state(%d), ballholder(%s), aborted(%s)", player->GetAttributes()->GetIndex(), *ENUM_TO_FSTRING(ERugbyAnimEvent, event), state, player == game->GetGameState()->GetBallHolder() ? TEXT("true") : TEXT("false"), aborted ? TEXT("true") : TEXT("false"));

	if (event == ERugbyAnimEvent::BALL_RELEASED_EVENT)
	{
		if (m_pGame->GetGameState()->GetBallHolder() == m_pPlayer && state == RoleState::INHANDS)
		{
			state = RoleState::RELEASE;
		}
		else
		{
			// If we no longer have the ball when we receive this event then we should exit
			//MABBREAKMSG("Somehow, passer no longer has ball.  This shouldn't happen - Exiting");
			Exit(true);
			return;
		}

		m_pPlayer->GetAnimation()->SetUpperBodyOverride(false);
	}
}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
void RURolePlayTheBall::StartPlayAnimation()
{
	MABLOGDEBUG("RURolePlayTheBall::StartPlayAnimation %d", m_pPlayer->GetAttributes()->GetIndex());

	RUPlayerAnimation* player_animation = this->GetPlayer()->GetAnimation();

	if (state == RoleState::PLAYTHEBALLGROUND)
	{
		MabString anim_request("play_the_ball_ground");
		player_animation->PlayAnimation(anim_request.c_str());
	}
	else 
	{
		MabString anim_request("play_the_ball_behind");
		player_animation->PlayAnimation(anim_request.c_str());
	}

}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
void RURolePlayTheBall::RotatePlayerToPlayDir()
{
	auto PlayDir = m_pPlayer->GetAttributes()->GetPlayDirection();
	FRotator CurrentRotation = m_pPlayer->GetActorRotation();
	FVector ForwardDirection;

	switch (PlayDir)
	{
	case ERugbyPlayDirection::NORTH:
		ForwardDirection = FVector(1.0f, 0.0f, 0.0f); 
		break;
	case ERugbyPlayDirection::SOUTH:
		ForwardDirection = FVector(-1.0f, 0.0f, 0.0f); 
		break;
	default:
		return;
	}

	FRotator TargetRotation = ForwardDirection.Rotation();
	TargetRotation.Pitch = CurrentRotation.Pitch;
	TargetRotation.Roll = CurrentRotation.Roll;

	// Use delta time from timestep
	float DeltaSeconds = m_pPlayer->GetGameWorld()->GetSimTime()->GetDeltaTime().ToSeconds();

	// Interpolate Yaw only
	float CurrentYaw = CurrentRotation.Yaw;
	float TargetYaw = TargetRotation.Yaw;

	// Calculate shortest angle between current and target
	float YawDiff = FMath::UnwindDegrees(TargetYaw - CurrentYaw);
	//UE_LOG(LogTemp, Log, TEXT("[PLAYTHEBALLGROUND] YawDiff: %.2f"), YawDiff);

	// How long should the rotate be
	const float TotalRotationDuration = 0.5f; // seconds
	float RotationSpeed = FMath::Abs(YawDiff) / TotalRotationDuration;

	if (FMath::Abs(YawDiff) > 1.0f) // Rotate only if not aligned
	{
		// Turn toward target with max turn rate
		float MaxYawThisFrame = RotationSpeed * DeltaSeconds;
		float NewYaw = CurrentYaw + FMath::Clamp(YawDiff, -MaxYawThisFrame, MaxYawThisFrame);
		FRotator NewRotation = FRotator(CurrentRotation.Pitch, NewYaw, CurrentRotation.Roll);
		m_pPlayer->SetActorRotation(NewRotation);
		//UE_LOG(LogTemp, Log, TEXT("[PLAYTHEBALLGROUND] Yaw: %.2f -> %.2f | Target Yaw: %.2f"), CurrentYaw, NewYaw, TargetYaw);
	}
	else
	{
		// Final snap to avoid jitter, and move to next state
		FRotator FinalRotation = FRotator(CurrentRotation.Pitch, TargetYaw, CurrentRotation.Roll);
		m_pPlayer->SetActorRotation(FinalRotation);
		ShouldRotate = false;
		//UE_LOG(LogTemp, Log, TEXT("[PLAYTHEBALLGROUND] Reached Target Yaw: %.2f, transitioning to INHANDS"), TargetYaw);
	}
}

bool RURolePlayTheBall::IsFacingPlayDirection(float MaxAngleDegrees = 45.0f)
{
	auto PlayDir = m_pPlayer->GetAttributes()->GetPlayDirection();
	FVector PlayerForward = m_pPlayer->GetActorForwardVector().GetSafeNormal();
	FVector DesiredDirection;

	switch (PlayDir)
	{
	case ERugbyPlayDirection::NORTH:
		DesiredDirection = FVector(1, 0, 0); 
		break;
	case ERugbyPlayDirection::SOUTH:
		DesiredDirection = FVector(-1, 0, 0); 
		break;
	default:
		return false;
	}

	DesiredDirection = DesiredDirection.GetSafeNormal();

	// Calculate dot product and angle
	float Dot = FVector::DotProduct(PlayerForward, DesiredDirection);
	float AngleDegrees = FMath::Acos(Dot) * (180.f / PI);

	//UE_LOG(LogTemp, Log, TEXT("Facing Angle: %.2f degrees"), AngleDegrees);

	return AngleDegrees <= MaxAngleDegrees;
}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
void RURolePlayTheBall::WarpToWaypoint()
{
	wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
	m_pPlayer->GetMovement()->SetTargetPosition( GetInitialPosition() );
	m_pPlayer->GetMovement()->SetFacingFlags( AFFLAG_FACEPLAYDIR );
	SSRole::WarpToWaypoint();
}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
FVector RURolePlayTheBall::GetInitialPosition()
{
	return m_pGame->GetGameState()->GetPlayRestartPosition();
}
