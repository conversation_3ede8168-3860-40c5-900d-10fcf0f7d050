//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBallDefender.h"

#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/Roles/Competitors/RURoleGetTheBall.h"
#include "Match/AI/Roles/Competitors/RURolePenaltyAttack.h"
#include "Match/AI/Roles/Competitors/SSRoleFormation.h"
#include "Match/Ball/SSBall.h"
#include "Match/Camera/SSCameraManager.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUInputPhaseDecision.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/RugbyUnion/Rules/Triggers/RURuleTriggerPenalty.h"
#include "Match/SIFGameObject.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Match/SSRoleFactory.h"
#include "Match/SSSpatialHelper.h"
#include "Match/AI/Actions/RUActionTacklee.h"

//#rc3_legacy_include #include <NMMabAnimationEvents.h>

#include "Character/RugbyPlayerController.h"
#include "RugbyGameInstance.h"

MABRUNTIMETYPE_IMP1( RURolePlayTheBallDefender, SSRole );

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
RURolePlayTheBallDefender::RURolePlayTheBallDefender( SIFGameWorld* game )
: SSRole(game)
, state{}
{
}

//-------------------------------------------------------------------------
// Enter
//-------------------------------------------------------------------------
void RURolePlayTheBallDefender::Enter( ARugbyCharacter* player )
{
	SSRole::Enter( player );

	MABASSERT(player == m_pPlayer);
	
	auto ballHolder = m_pGame->GetGameState()->GetBallHolder();
	if (ballHolder == nullptr)
	{
		state = RoleState::COMPLETE;
		Exit(true);
		return;
	}

	UpdateDefenderPosition();

	ggNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);

	m_pMovement->SetFacingActor(ballHolder);

	state = RoleState::MOVING;

	m_lock_manager.HFLockAll();
	m_lock_manager.HFUnlock(HF_CHANGE_PLAYER);
	m_pActionManager->EnableAllActions(false);

}

//-------------------------------------------------------------------------
// Exit
//-------------------------------------------------------------------------
void RURolePlayTheBallDefender::Exit(bool forced)
{
	m_lock_manager.HFClearLocks();
	m_pActionManager->EnableAllActions(true);
	UE_LOG(LogTemp, Warning, TEXT("RURolePlayTheBallDefender: EXIT "));

	warped = false;
	isHuman = false;
	if (m_pPlayer && m_pPlayer->GetHumanPlayer())
	{
		m_pPlayer->GetHumanPlayer()->DisableInput(false);
	}
	SSRole::Exit(forced);
}

//-------------------------------------------------------------------------
// Update
//-------------------------------------------------------------------------
void RURolePlayTheBallDefender::UpdateLogic( const MabTimeStep& game_time_step )
{
	SSRole::UpdateLogic(game_time_step);

	if (m_pGame->GetGameState()->GetPhase() == RUGamePhase::PLAY_THE_BALL && m_pPlayer->GetHumanPlayer())
	{
		m_lock_manager.HFLockAll();
		m_lock_manager.HFUnlock(HF_CHANGE_PLAYER);
		m_pActionManager->EnableAllActions(false);
	}

	// Warp the player into pos, compared to the receiver role and play the ball role this one needs to warp on update
	// as the player doesn't end up in the right spot when just done in enter
	if (m_pGame->GetGameState()->GetHandoverType() != EHandoverType::TACKLE || m_pGame->GetGameState()->IsZeroTackle())
	{
		warp_timer += game_time_step.delta_time.ToSeconds();
		if (warp_timer >= 0.5f && !warped)
		{
			m_pPlayer->GetAnimation()->Reset();
			WarpToWaypoint();
			warped = true;
		}
	}

	if (state == RoleState::MOVING)
	{
		auto ballHolder = m_pGame->GetGameState()->GetBallHolder();
		if (ballHolder != nullptr)
		{
			FVector CurrentPos = m_pMovement->GetCurrentPosition();
			FVector TargetPos = m_pMovement->GetTargetPosition();

			float Tolerance = 1.0f;

			if (CurrentPos.Equals(TargetPos, Tolerance))
			{
				UE_LOG(LogTemp, Warning, TEXT("RURolePlayTheBallDefender: Complete "));

				state = RoleState::COMPLETE;
				return;
			}
			else
			{
				SetDefenderPosition();
			}

			return;
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("RURolePlayTheBallDefender: no ball holder "));
			state = RoleState::COMPLETE;
		}
	}

//	SSRole::UpdateLogic( game_time_step );
}

//-------------------------------------------------------------------------
// GetFitness
//-------------------------------------------------------------------------
int RURolePlayTheBallDefender::GetFitness(const ARugbyCharacter* player, const SSRoleArea* area)
{
	RUGameState* state = player->GetGameWorld()->GetGameState();
	if (state)
	{
		auto ballHolder = state->GetBallHolder();
		if (ballHolder)
		{
			if (player->GetName() == state->GetClosestPlayerToBall(player->GetAttributes()->GetTeam())->GetName())
			{
				UE_LOG(LogTemp, Warning, TEXT("defender SET for %s - %d"), *player->GetName() , player->GetAttributes()->GetDbId());

				return 10000;
			}
			else
			{
				return -100;
			}
		}
	}
	return -100;
}

//-------------------------------------------------------------------------
// IsInterruptable
//-------------------------------------------------------------------------
bool RURolePlayTheBallDefender::IsInterruptable() const
{
	return state == RoleState::COMPLETE;
}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
void RURolePlayTheBallDefender::UpdateDefenderPosition()
{
	m_pMovement->SetThrottleAndTargetSpeedByUrgency(1, ACTOR_SPEED::AS_SPRINT, ACTOR_SPEED::AS_FASTRUN);
	SetDefenderPosition();
}

void RURolePlayTheBallDefender::SetDefenderPosition()
{
	m_pMovement->SetTargetPosition(GetDefenderPosition(), true);
}

FVector RURolePlayTheBallDefender::GetDefenderPosition()
{
	auto ballHolder = m_pGame->GetGameState()->GetBallHolder();
	if (ballHolder)
	{
		// Use the actual PTB player's current position for alignment instead of restart position
		FVector ptb_player_pos = ballHolder->GetMovement()->GetCurrentPosition();
		FVector offset = FVector(0, 0, 2.5f * ballHolder->GetAttributes()->GetPlayDirection());
		m_last_defender_pos = ptb_player_pos + offset;
		//ggNETWORK_TRACE_JG("force 1 SetTargetPosition() for Defender: X: %f Y: %f Z: %f", m_last_defender_pos.X, m_last_defender_pos.Y, m_last_defender_pos.Z);
	}
	return m_last_defender_pos;
}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
//void RURolePlayTheBallDefender::UpdateRejoinPosition()
//{
//	m_pPlayer->GetMovement()->SetFacingActor(m_pGame->GetGameState()->GetBallHolder());
//	m_pMovement->SetThrottleAndTargetSpeedByUrgency(1, ACTOR_SPEED::AS_RUN, ACTOR_SPEED::AS_JOG);
//	FVector currPos = m_pPlayer->GetMovement()->GetCurrentPosition();
//	FVector runbackPos = FVector(currPos.x, currPos.y, currPos.z + 4.0f * -m_pPlayer->GetAttributes()->GetPlayDirection());
//	m_pMovement->SetTargetPosition(runbackPos);
//}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
void RURolePlayTheBallDefender::WarpToWaypoint()
{
	auto ballHolder = m_pGame->GetGameState()->GetBallHolder();
	if (ballHolder)
	{
		FVector passer_pos = ballHolder->GetMovement()->GetCurrentPosition();
		FVector offset = FVector(0, 0, 2.5f * ballHolder->GetAttributes()->GetPlayDirection());
		FVector receive_pos = passer_pos + offset;
		m_pPlayer->GetMovement()->SetFacingFlags(AFFLAG_FACEPLAYDIR);
		m_pMovement->SetTargetPosition(receive_pos, true);
		ggNETWORK_TRACE_JG("force 1 WarpToWaypoint() for Defender: X: %f Y: %f Z: %f", receive_pos.X, receive_pos.Y, receive_pos.Z);

		SSRole::WarpToWaypoint();
	}
}