/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef RU_GAMEPHASEPLAYTHEBALL_H
#define RU_GAMEPHASEPLAYTHEBALL_H

#include "Match/RugbyUnion/RUGameState.h"

class PTBOrigin : public IRUOrigin
{
public:

	FVector GetOrigin() const override { return origin; }
	float GetFacingAngle() const override { return facing_angle; }

	FVector origin = FVector();
	float facing_angle = 0;
};

class RUGamePhasePlayTheBall : public RUGamePhaseHandler
{
	MABRUNTIMETYPE_HEADER(RUGamePhasePlayTheBall);
public:
	RUGamePhasePlayTheBall(SIFGameWorld *ggame);
	virtual ~RUGamePhasePlayTheBall();

	virtual void Enter();
	virtual void Exit();
	virtual void UpdateSimulation( const MabTimeStep& game_time_step );
	virtual void Reset();

	float GetEstimatedTimeTillBallBackInPlay();
private:
	SIFGameWorld	*game;

	PTBOrigin* ptb_origin;

	bool is_handover = false;
};

#endif //RU_GAMEPHASEPLAYTHEBALL_H
