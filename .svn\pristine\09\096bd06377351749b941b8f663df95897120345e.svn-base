/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#include <precompiled.h>

#include "Match/HUD/Marking/RUPlayerIndicator.h"
#include "Match/HUD/RU3DHUDManager.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/AI/Roles/Competitors/RURoleShootForGoal.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuckScrumHalf.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleScrumHalfBack.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleLineOut.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseLineOut.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseMaul.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/HUD/RUHUDUpdaterTraining.h"
#include "Match/RugbyUnion/Rules/Offside/RUOffsideIndicator.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUInputKickInterface.h"
#include "Character/RugbyCharacter.h"
#include "Match/SIFGameWorld.h"
#include "RugbyGameInstance.h"
#include "RugbyEnums.h"

//#include "MabNURBSSpline.h"
//
//static const float MARKER_SIZE_PLAYER = 0.75f;//MARKER_SIZE_PLAYER
//static const float STANDARD_GEO_SIZE = 0.1f;
//// const float PULSE_GEO_SIZE = 0.15f;
//// const float PULSE_INCREASE_MOD = 0.2f;
//static const float ROTATION_SPEED = 2.0f;
//static const float SCALE_TIME = 0.26f;
//static const float SCALE_SIZE = 1.2f;
//static const char marker_geo[] = "marker";
//static const char confidence_geo[] = "confidence";
//static const float FATIGUE_ALPHA = 0.33f;
//static const float MIN_TEAM_CONFIDENCE = 1.2f;

// Let's get our RTT On.
//MABRUNTIMETYPE_IMP1( RUPlayerIndicator, RU3DDynamicMarker )

///-------------------------------------------------------------------------------
/// RUPlayerIndicator - constructor
///-------------------------------------------------------------------------------

RUPlayerIndicator::RUPlayerIndicator(EHumanPlayerSlot num, SIFGameWorld *ggame)
: RU3DDynamicMarker( ggame, 2, STANDARD_GEO_SIZE, 0.0f, true, 128, true/*, PSSG::PShader::PE_SHADER_CULL_CCW*/ )
, last_player(NULL)
, player_num(num)
, confidence_timer(0.0f)
, display_offscreen_marker(true)
, scaling(false)
, scale_timer(0.0f)
, player_fatigue( 0.0f )
//, confidence_indicator( NULL )
{
	CreatePathPointsCircle(MARKER_SIZE_PLAYER);
	GenerateMeshData();
	recalc_geo = true;
	game_world->GetEvents()->player_deleted.Add(this, &RUPlayerIndicator::OnPlayerDeleted);
	confidence_indicator = std::make_unique<RUConfidenceIndicator>(ggame, this);  //MabMemNew( ggame->GetHeap() ) RUConfidenceIndicator( ggame, this );
}

///-------------------------------------------------------------------------------
/// Destructor.
///-------------------------------------------------------------------------------

RUPlayerIndicator::~RUPlayerIndicator()
{
	game_world->GetEvents()->player_deleted.Remove(this, &RUPlayerIndicator::OnPlayerDeleted);

	//MabMemDeleteSafe( confidence_indicator );
}

///-------------------------------------------------------------------------------
/// Handle 'player_deleted' event.
///-------------------------------------------------------------------------------

void RUPlayerIndicator::OnPlayerDeleted(ARugbyCharacter *player)
{
	if(last_player==player)
		last_player = NULL;
}

///-------------------------------------------------------------------------------
/// Update
///-------------------------------------------------------------------------------

void RUPlayerIndicator::Update(const MabTimeStep& game_time_step)
{
	MABUNUSED(game_time_step);
	if (!game_world)
		return;

	SSHumanPlayer* human = game_world->GetHumanPlayer(player_num);

	if (!human)
		return;

	ARugbyCharacter* player = human->IsPlaying() ? human->GetRugbyCharacter() : nullptr;
	
	/*if(player != NULL)
	{
		MABLOGDEBUG("Team confidence: %f", player->GetAttributes()->GetTeam()->GetConfidence());
	}*/
	

	// If this player isn't playing, don't draw the player indicator
	// Shoot for goal have no player marker
	if( player == NULL 
		|| (player->GetRole()->RTTGetType() == RURoleScrumHalfBack::RTTGetStaticType() && !player->GetRole<RURoleScrumHalfBack>()->HasBallControl())
		|| (player->GetRole()->RTTGetType() == RURoleRuckScrumHalf::RTTGetStaticType() && !player->GetRole<RURoleRuckScrumHalf>()->HasBallControl())
		|| (player->GetRole()->RTTGetType() == RURoleLineOut::RTTGetStaticType()))
	{
		SetVisible(false);
		return;
	}

	// More specific checks for kicking for goal.
	if( player == NULL 
		|| game_world->GetGameState()->GetPhase() == CONVERSION
		|| game_world->GetGameState()->GetPhase() == PENALTY_SHOOT_FOR_GOAL)
	{
		bool disable = false;

		// Never display when we're playing fifteens
		// Nick WWS 7s to Womens //if( game_world->GetGameSettings().game_settings.GameModeIsR13())
			disable = true;

		// Only display the indicator for the kicking team.
		if( player->GetRole()->RTTGetType() != RURoleShootForGoal::RTTGetStaticType() )
			disable = true;
		else
		{
			// Only display while we're setting up the kick.
			RURoleShootForGoal* shootRole = player->GetRole<RURoleShootForGoal>();
			if( game_world->GetInputManager()->GetKickInterface()->GetConversionPowerSet() || 
				shootRole->TookQuickConversion() ||
				shootRole->GetKickState() > 7/*KFP_POWER*/ )
				disable = true;
		}

		if(disable)
		{
			SetVisible(false);
			return;
		}
	}
	
#ifdef ENABLE_PRO_MODE
	// TODO DEWALD: I can probably remove the player indicator here for pro mode and our player is in a ruck, scrum, or maul.
	if(game_world->GetGameSettings().game_settings.GetIsAProMode())
	{
		if(game_world->GetGameState()->GetPhase() == RUCK)
		{
			RUGamePhaseRuck* ruckPhase = game_world->GetGameState()->GetPhaseHandler<RUGamePhaseRuck>();
			if(ruckPhase->GetIsProInRuck())
			{
				SetVisible(false);
				return;
			}
		}
		
		else if(game_world->GetGameState()->GetPhase() == MAUL)
		{
			RUGamePhaseMaul* maulPhase = game_world->GetGameState()->GetPhaseHandler<RUGamePhaseMaul>();
			if(maulPhase->GetIsProPlayerInMaul())
			{
				SetVisible(false);
				return;
			}
		}
		
		else if(game_world->GetGameState()->GetPhase() == LINEOUT)
		{
			// Never show our indicator in lineouts, since we can't move
			SetVisible(false);
			return;
			/*RUGamePhaseLineOut* lineoutPhase = game_world->GetGameState()->GetPhaseHandler<RUGamePhaseLineOut>();

			// Only show our player indicator when our pro player is not inside of the lineout
			if(!lineoutPhase->GetIsProPlayerInLineout())
				SetVisible(true);
			else
			{
				SetVisible(false);
				return;
			}*/
		}

		else if(game_world->GetGameState()->GetPhase() == SCRUM)
		{
			// Never show our indicator in scrums, since we can't move
			SetVisible(false);
			return;

			/*RUGamePhaseScrum* scrumPhase = game_world->GetGameState()->GetPhaseHandler<RUGamePhaseScrum>();
			
			bool proInAttackTeam = scrumPhase->GetCurrentScrum().attacking.team->GetProPlayer() != NULL;
			bool settingVisible = false;
			// Attacking team is the pro team
			// Turn the indicator on if we're playing a position outside of the scrum
			if(proInAttackTeam)
				settingVisible = !scrumPhase->GetCurrentScrum().attacking.GetIsProPlayerInScrum();
			else
				settingVisible = !scrumPhase->GetCurrentScrum().defending.GetIsProPlayerInScrum();

			SetVisible(settingVisible);
			if(!settingVisible) return;*/
		}

		else if (game_world->GetGameState()->GetPhase() == DECISION || 
				 game_world->GetGameState()->GetPhase() == DECISION_PENALTY ||
				 game_world->GetGameState()->GetPhase() == ELECT_QUICK_TAP )
		{
			// If we're not the ball holder, don't show the player indicator
			if(game_world->GetGameState()->GetBallHolder() /*&& !SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(game_world->GetGameState()->GetBallHolder())*/)
			{
				SetVisible(false);
				return;
			}
		}

		//when we score a try or playing a try_cutscene, hide the marker.
		else if (game_world->GetGameState()->GetPhase() == TRY_REACTION ||
			game_world->GetGameState()->GetPhase() == TRY_CUTSCENE)
		{
			SetVisible(false);
			return;
		}
	}
	// if ProMode
	// if gamephase == Ruck || Maul || Scrum
	// if OurPro == InsideRuck() || InsideMaul() || InsideScrum()
	// SetVisible(false)
	// return
#endif

	SetVisible( true );
	// Jordan HACK FOR BUILD DAY
	//MABASSERT(player_num == 0);

	if( player != last_player ) 
	{
		// we have a new player selected, start the scale
		scaling = true;
		scale_timer = SCALE_TIME;

		if ( player->GetAttributes()->GetPlayDirection() == ERugbyPlayDirection::NORTH )
		{
			rotation = 1.5f*PI;
			target_rotation = rotation;
		}
		else
		{
			rotation = 0.5f*PI;
			target_rotation = rotation;
		}
	}
	last_player = player;

	scale = 1.0f;
	if( scaling == true ) 
	{
		scale_timer -= game_world->GetSimTime()->GetDeltaTime().ToSeconds();
		if( scale_timer < 0.0f ) {
			scale_timer = 0.0f;
			scaling = false;
		}
		scale = 1.0f + (( scale_timer / SCALE_TIME ) * SCALE_SIZE_PLAYER);
	}

	confidence_indicator->SetVisible( player->GetAttributes()->GetTeam()->GetConfidence() > MIN_TEAM_CONFIDENCE );

// 	if ( is_pulsing )
// 	{
// 		geo_radius += game_time_step.delta_time.ToSeconds() * PULSE_INCREASE_MOD;
// 
// 		if ( geo_radius > PULSE_GEO_SIZE )
// 		{
// 			geo_radius = STANDARD_GEO_SIZE;
// 			is_pulsing = false;
// 		}
// 	}

	player_fatigue = player->GetAttributes()->GetFatigue();
	// This translation will get converted into Unreal format, so needs to be a Mab position.
	translation = player->GetMabPosition();//player->GetMovement()->GetCurrentPosition();
	transforms_dirty = true;

	confidence_indicator->SetPosition( translation );
	confidence_indicator->Update(game_time_step);

	// update the offscreen marker (the player may or may not be offscreen)
	if (game_world->GetHUDUpdater() && game_world->GetWorldId() == WORLD_ID::GAME)
	{
		RUOffsideIndicator* offside_indicator = player->GetOffSideIndicator();
		bool offside = (game_world->GetGameSettings().game_settings.custom_rule_offside && offside_indicator? 
				(offside_indicator->GetOffsideValue() > 0.0f) : false);
		game_world->GetHUDUpdater()->UpdateOffscreenMarker(player_num, GetPlayerColour(), player->GetActorLocation(), offside);
	}

	if (game_world->GetHUDUpdaterTraining() && game_world->GetWorldId() == WORLD_ID::SANDBOX)
	{
		RUOffsideIndicator* offside_indicator = player->GetOffSideIndicator();
		bool offside = (game_world->GetGameSettings().game_settings.custom_rule_offside && offside_indicator? 
				(offside_indicator->GetOffsideValue() > 0.0f) : false);
		game_world->GetHUDUpdaterTraining()->UpdateOffscreenMarker(player_num, GetPlayerColour(), player->GetActorLocation(), offside);
	}
}

void RUPlayerIndicator::SyncUpdate()
{
	MABASSERT(confidence_indicator);
	if (confidence_indicator == nullptr)
	{
		return;
	}

	recalc_geo = true;
	RU3DDynamicMarker::SyncUpdate();

	confidence_indicator->SyncUpdate();
	//GenerateMeshData();
}

void RUPlayerIndicator::SetVertexColours()
{
	const float block_percentage = 1.0f / n_vertices;
	for ( int i = 0; i < n_vertices; ++i )
	{
		DynamicMeshActor->Mesh_Color[i] =  GetPlayerColour();
	
		// Adjust alpha if fatigued
		if ( player_fatigue > i * block_percentage )
			DynamicMeshActor->Mesh_Color[i].A *= FATIGUE_ALPHA;
	}
}

void RUPlayerIndicator::SetVisible( bool visible )
{
	// RussellD : Look into what's happening here!

	////// only markers with players should ever be visible //fixme
	SSHumanPlayer* player = game_world->GetHumanPlayer( player_num );
	if ( !visible || ( player != NULL && player->IsPlaying()) )
	{
		RU3DDynamicMarker::SetVisible( visible );

		if (!visible)
		{
			confidence_indicator->SetVisible(visible);
			confidence_indicator->SyncUpdate();
		}

		if (game_world->GetHUDUpdater())
		{
			// We only want the marker to be visible if we're set to displaying the marker.
			visible = visible && display_offscreen_marker;
			game_world->GetHUDUpdater()->SetOffscreenMarkerVisible(player_num, visible);
		}
	}	
}

void RUPlayerIndicator::SetDisplayOffscreenMarker( bool should_display )
{
	display_offscreen_marker = should_display;
}

const FLinearColor& RUPlayerIndicator::GetPlayerColour() const
{
	return RU3DHUDManager::GetPlayerColour(player_num);
}


void RUPlayerIndicator::UpdateTransform()
{
	RU3DDynamicMarker::UpdateTransform();
	// scale this sucker too
	//render_node->m_matrix = render_node->m_matrix * PSSG::PMatrix4::scale(Vector3(scale, scale, scale));
}

//static const float MARKER_SIZE_PLAYER = 0.75f;
//static const float MIN_RADIUS = MARKER_SIZE_PLAYER + 0.12f;
//static const float MAX_RADIUS = 1.8f;
//static const float MIN_GEO_RADIUS = 0.02f;
//static const float MAX_GEO_RADIUS = 0.16f; MAX_GEO_RADIUS_PLAYER
//static const float RADIUS_INCREASE_MOD = 0.6f;
//static const float ALPHA_DECREASE_MOD = 0.5f;
//static const float SCALE_INCREASE_MOD = 0.3f;

RUConfidenceIndicator::RUConfidenceIndicator( SIFGameWorld *ggame, RUPlayerIndicator* pparent )
: RU3DDynamicMarker( ggame, 2, MIN_GEO_RADIUS_PLAYER, 0.0f, true, 128, true/*, PSSG::PShader::PE_SHADER_CULL_CCW*/ )
, parent_indicator( pparent )
, scale_timer(0.0f)
, current_radius(MIN_RADIUS)
, current_alpha(1.0f)
{
	geo_radius = MIN_GEO_RADIUS_PLAYER;
	CreatePathPointsCircle(MIN_RADIUS);
	GenerateMeshData();
	recalc_geo = true;
}

RUConfidenceIndicator::~RUConfidenceIndicator()
{

}

void RUConfidenceIndicator::Update(const MabTimeStep& game_time_step)
{
	float t = game_time_step.delta_time.ToSeconds();
	
	current_radius += game_time_step.delta_time.ToSeconds() * RADIUS_INCREASE_MOD;
	
	current_alpha -= t * ALPHA_DECREASE_MOD;
	MabMath::Clamp( current_alpha, 0.0f, 1.0f );

	geo_radius += t * SCALE_INCREASE_MOD;
	MabMath::Clamp( geo_radius, MIN_GEO_RADIUS_PLAYER, MAX_GEO_RADIUS_PLAYER);
	
	if ( current_radius > MAX_RADIUS )
	{
		current_radius = MIN_RADIUS;
		current_alpha = 1.0f;
		geo_radius = MIN_GEO_RADIUS_PLAYER;
		//has_pulsed = false;
	}

	//if ( current_radius > MARKER_SIZE_PLAYER && !has_pulsed )
	//{
	//	parent_indicator->Pulse();
	//	has_pulsed = true;
	//}
}

void RUConfidenceIndicator::SyncUpdate()
{
	if (is_visible)
	{
		CreatePathPointsCircle(current_radius);
	}
	RU3DDynamicMarker::SyncUpdate();
}

void RUConfidenceIndicator::SetVertexColours()
{
	for ( int i = 0; i < n_vertices; ++i )
	{
		DynamicMeshActor->Mesh_Color[i] = parent_indicator->GetPlayerColour();
		DynamicMeshActor->Mesh_Color[i].A *= current_alpha;
	}
}