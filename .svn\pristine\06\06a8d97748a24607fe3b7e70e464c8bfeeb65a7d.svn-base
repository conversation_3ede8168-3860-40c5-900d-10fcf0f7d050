/*--------------------------------------------------------------
|        Copyright (C) 1997-2008 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#pragma once

#ifndef NRL_USE_13S_MODE 
#define NRL_USE_13S_MODE 1
#endif
#include "CoreMinimal.h"
#include "Networking/SIFOnlineConstants.h"
#include "Runtime/Engine/Classes/Kismet/KismetMathLibrary.h"
#include "WWUILeaderboard.generated.h"

/// @name forward declarations
/// @{
class UWWUILeaderboardDataRow;
class UWWUILeaderboardPopulatorMessage;
class UWWUILeaderboardPopulator;
class UWWUIListField;
// SIFCommonDebugSettings is apparently loaded in non-debug retail builds too.
//#ifdef ENABLE_DEBUG_KEYS
class SIFCommonDebugSettings;
//#endif
/// @}

DECLARE_DELEGATE_OneParam(LeaderboardDelegate, const UWWUILeaderboardPopulatorMessage&)

#if PLATFORM_WINDOWS && defined ENABLE_STEAM
class SIFSteamLeaderboardRequest;
#endif


/// \class UWWUILeaderboard
///	A representation of a next-gen console Leaderboard.
/// This class will contain both a filter that can cycle between FRIENDS view, OVERALL view, and 
///	MY SCORE view, and it will support caching, to minimise the number of network requests.
/// It's unknown if this class will be extensible for Steam, but the hope is that it will be. 
/// Wii has no supported notion of stats recording, so SIFWiiLeaderboard will be an impossibility.
///	<AUTHOR> Barrett
///
UCLASS()
class RUGBY_API UWWUILeaderboard : public UObject
{
	GENERATED_BODY()

	/// The LeaderboardQueue class should have access to the Leaderboard's private members
	friend class UWWUILeaderboardQueue;

public:
	UWWUILeaderboard();
	/// \enum An enumeration referencing the three different types of leaderboards we're required to have by TCR: 
	///		FRIEND:		A comparison of you vis a vis your friends
	///		OVERALL:	The leaderboard for the entire world
	///		MY_SCORE:	The OVERALL leaderboard above, with your ranking as the midpoint.
	
	enum FILTER
	{
		FRIEND = 0,
#if /*(PLATFORM_XBOXONE) &&*/ ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE
		OVERALL_SEVEN,
		MY_SCORE_SEVEN,
		FRIEND_SEVEN,
#endif
		OVERALL,
		MY_SCORE,
		NUM_FILTERS
	};
	UWWUILeaderboard(
		LeaderboardIDType& _leaderboard_id,
		FString& _title,
		TArray< MabString > _custom_columns,
		UWWUILeaderboardPopulator* _populator,
		TArray< FString >* const _full_col_name_list
		//RULeaderboardFieldFormatter* _field_formatter = NULL
	);

	virtual ~UWWUILeaderboard();

	/// @name Accessor functions
	/// @{ 
	inline FILTER GetFilter() const { return current_filter; }
	inline const LeaderboardIDType& GetID() { return leaderboard_id; }
	inline const FString& GetTitle() const { return title; }
	inline const TArray< FString >& GetFullColumnNameList() const { return full_col_name_list; }
	inline const TArray< LeaderboardColumnDataType >& GetCustomColumns() const { return custom_columns; }

	/// Use this accessor to get the number of "total" entries for a given filter; for a friends filter this will be the number
	///	of your friends (including yourself) that have scored on this leaderboard.
	unsigned int GetNumTotalEntries( FILTER requested_filter = NUM_FILTERS ) const;

	bool changeLeaderboardForFilter(FILTER f,int *newLBId) {
#if  (PLATFORM_WINDOWS  && defined ENABLE_STEAM) || PLATFORM_PS4 || PLATFORM_XBOXONE
		int currentID = FCString::Atoi(UTF8_TO_TCHAR(leaderboard_id.c_str()));
		int reqdID = 0;
		int reqLbid = 0;
		switch (f)
		{
			case FRIEND:
				reqdID = 0/*LEADERBOARD_TABLE::STATS_VIEW_ONLINE_MATCHES*/;
				break;
#if ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE
			case OVERALL_SEVEN:
				reqdID = 1/*STATS_VIEW_ONLINE_SEVENS_MATCHES*/;
				break;
			case MY_SCORE_SEVEN:
				reqdID = 1/*STATS_VIEW_ONLINE_SEVENS_MATCHES*/;
				break;
			case FRIEND_SEVEN:
				reqdID = 1/*STATS_VIEW_ONLINE_SEVENS_MATCHES*/;
				break;
#endif
			case OVERALL:
				reqdID = 0/*STATS_VIEW_ONLINE_MATCHES*/;
				break;
			case MY_SCORE:
				reqdID = 0/*STATS_VIEW_ONLINE_MATCHES*/;
				break;
			default:
				MABBREAKMSG( "Don't send NUM_FILTERS to this function!" );
				return false;
		}

#if PLATFORM_XBOX360
		if(reqdID == 0)
			reqLbid = 0/*STATS_VIEW_ONLINE_MATCHES*/;
		else
			reqLbid = 1/*STATS_VIEW_ONLINE_SEVENS_MATCHES*/;
#else
		reqLbid = reqdID;
#endif
		if (newLBId)
			*newLBId = reqdID;
		return(currentID != reqLbid);
#else
		MABUNUSED(f);
		MABUNUSED(newLBId);
		return false;
#endif
	};
	/// @}

	/// @name Major functions
	/// @{
	/// \brief Request specified stats from this leaderboard
	///	Note: Response will come in the form of a UWWUILeaderboardPopulatorMessage; make sure you're attached!
	///		This will be true regardless of whether or not the requested data requires an asynchronous communication
	///		with the statistics manager.
	///	@param	min_display_index	the smallest index (inclusive) of the data you wish to read from.
	///								Sending -1 (the default) uses the selected_top_row for this leaderboard.
	///								Ignored if filter is MY_SCORE.
	///	@param	filter_in			the filter you wish to read from; passing NUM_FILTERS (the default) 
	///								will make this code read from its set filter
	/// @param	ui_elem_name		Optional - the name of the UI element that the data from this request should be filled by
	/// @param  switch_on_non_default_filter	Optional - specifies whether to switch the current_filter if you send a non-
	///								default filter. Used for My Score functionality.
	///	@return						true if this operation has been resolved synchronously--i.e. by the time this message
	///								returns you will have already processed the message--false if you're waiting on a message
	bool RequestData( int min_display_index = -1, FILTER filter_in = NUM_FILTERS, const FString& ui_elem_name = "", bool switch_on_non_default_filter = true );

	/// \brief Set the player's filter to the next filter, where "next" is defined by the order in enum FILTER above.
	///	Note: This will make a call to RequestData above.
	/// @param	clear_cache		Whether or not to clear the cache on the filter that you're LEAVING
	///	@param	start_from_top	IFF we've already viewed this filter, and this field is false, we
	///							start from the position we were looking at the last time we left this particular leaderboard.
	/// @param	is_sync_out		Optional value to get a boolean out determining whether or not a given operation was able to stay synchronous--no loading message needed if so
	/// @param	ui_elem_name	Optional - the name of the UI element that the data from this request should be filled by
	///	@return	the newly-set filter
	FILTER IncrementFilter( bool clear_cache = false, bool start_from_top = false, bool* const is_sync_out = NULL, const FString& ui_elem_name = "" );

	/// \brief Set the player's filter to the previous filter, where "previous" is defined by the order in enum FILTER above.
	///	Note: This will make a call to RequestData above.
	/// @param	clear_cache		Whether or not to clear the cache on the filter that you're LEAVING
	///	@param	start_from_top	IFF we've already viewed this filter, and this field is false, we
	///							start from the position we were looking at the last time we left this particular leaderboard.
	/// @param	is_sync_out		Optional value to get a boolean out determining whether or not a given operation was able to stay synchronous--no loading message needed if so
	/// @param	ui_elem_name	Optional - the name of the UI element that the data from this request should be filled by
	///	@return	the newly-set filter
	FILTER DecrementFilter( bool clear_cache = false, bool start_from_top = false, bool* const is_sync_out = NULL, const FString& ui_elem_name = "" );
	FILTER NextFilter(int dir);
	/// \brief Set the player's filter to the given value.
	///	Note: This will make a call to RequestData above.
	///	@param	_new_filter		the filter to set the leaderboard to
	/// @param	clear_cache		Whether or not to clear the cache on the filter that you're LEAVING
	///	@param	start_from_top	IFF we've already viewed this filter, and this field is false, we
	///							start from the position we were looking at the last time we left this particular leaderboard.
	/// @param	ui_elem_name	Optional - the name of the UI element that the data from this request should be filled by
	///	@return	the newly-set filter
	FILTER SetFilter( FILTER _new_filter, bool clear_cache = false, bool start_from_top = false, bool* const is_sync_out = NULL, const FString& ui_elem_name = "" );

	/// \brief Clear cache for specified filter
	///	@param	filter_in	The filter to clear the cache for; if set to NUM_FILTERS (the default), it clears all caches.
	virtual void ClearCache( FILTER filter_in = NUM_FILTERS );

	/// \brief Displays a message in the centered status text widget
	///	@param	text		Text to display
	virtual void SetStatusMessage(UWWUIListField* field, FString& text ) const;

	/// \brief Gets the current status message
	///	@return what the status is. Would return a reference, but need to be safe, and return empty string in some cases.
	FString GetStatusMessage(UWWUIListField* field ) const;

	/// \brief Sets the current field formatter.
	//void SetFieldFormatter( RULeaderboardFieldFormatter* _field_formatter ) { field_formatter = _field_formatter; }

	inline bool operator==( const UWWUILeaderboard& that ) { return ( leaderboard_id == that.leaderboard_id ); }
	inline bool operator!=( const UWWUILeaderboard& that ) { return ( leaderboard_id != that.leaderboard_id ); }

protected:
	/// \struct RequestTuple
	///	An internal struct representing a given request.
	struct RequestTuple
	{
		/// The filter associated with this request
		FILTER filter;
		/// The min and max displayed index to get for this request
		int min_display_index;
		int max_display_index;
		FString ui_elem_name;
		/// A filter that's ONLY used (at the moment when you're performing a scroll-based request under
		///	the My Score filter, which resembles an Overall filter.
		FILTER secondary_filter;
		/// Time at which this request has been initiated
		FDateTime init_time;

		RequestTuple( FILTER _filter = FRIEND, int _min_display_index = -1, int _max_display_index = -1, const FString& _ui_elem_name = "", FILTER _secondary_filter = NUM_FILTERS )
			: filter ( _filter ), min_display_index( _min_display_index ), max_display_index ( _max_display_index ), ui_elem_name( _ui_elem_name ), secondary_filter( _secondary_filter ) 
		{
			// Store the time at which this request has been initiated
			//init_time = UKismetMathLibrary::Now();
		}

		MabUInt32 getLeaderboardID();
	};

	// Comparator struct for sorting friends list
	struct FriendSortComparator// : public NonCopyable
	{
		bool operator()( const UWWUILeaderboardDataRow& left, const UWWUILeaderboardDataRow& right );
	};

	/// A couple of helper typedefs to shorten data type names.
	typedef MabMap< int, UWWUILeaderboardDataRow* > RankToRowMap;


	/// \class FilterToRowMap
	/// A class that overwrites some portion of Map functionality with the aim in mind of
	///	simplifying filters: MY_SCORE and OVERALL should both point to the same
	///	set of data.
	class FilterToRowMap : public MabMap< FILTER, RankToRowMap >
	{
	public:
		typedef MabMap< FILTER, RankToRowMap > Type;
		RankToRowMap& operator[]( const FILTER& _key )
		{
			FILTER key( _key );
			FilterConvert( key );
			return Type::operator[]( key );
		}
		Type::iterator find( const FILTER& _key )
		{
			FILTER key( _key );
			FilterConvert( key );
			return Type::find( key );
		}
		Type::const_iterator find( const FILTER& _key ) const
		{
			FILTER key( _key );
			FilterConvert( key );
			return Type::find( key );
		}
		size_t count( const FILTER& _key )
		{
			FILTER key( _key );
			FilterConvert( key );
			return Type::count( key );
		}
		size_t erase( const FILTER& _key )
		{
			FILTER key( _key );
			FilterConvert( key );
			return Type::erase( key );
		}
	private:
		/// Insert not implemented
		/// @{
		Type::iterator insert( Type::iterator i, const Type& pair );
		void insert( Type::iterator start, Type::iterator end );
		std::pair<Type::iterator,bool> insert( const Type& pair );
		/// @}
		void FilterConvert( FILTER& filter_in_out ) const
		{
			if( filter_in_out == MY_SCORE ) filter_in_out = OVERALL;
#if ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE
			if( filter_in_out == MY_SCORE_SEVEN ) filter_in_out = OVERALL_SEVEN;
#endif
		}
	};

	/// \class RequestedRows
	/// An inner class that represents rows that have already been requested *but
	///	are not yet processed*. These will be used as a way to short-circuit the request process so
	///	that rapid scrolling doesn't send a bunch of unnecessary requests.
	/// NOTE: For the time being this class assumes contiguous ranges that will be processed by the system in the order
	///	they were called, i.e. a FIFO operations queue. What that means we can't handle for right now is a 
	///	situation like this:
	///	1. NewRequest( OVERALL, 0 );
	/// 2. NewRequest( OVERALL, 100 );
	/// 3. NewRequest( OVERALL, 200 );
	/// 4. ClearRequest( OVERALL, 100 ); // i.e. from op #2 before #1 is completed.
	template< class Id_type >
	class RequestedRows
	{
	public:
		static const int UN_SET_VALUE = -1;

		RequestedRows( MABMEM_HEAP _heap ) : is_friend_request_in_progress( false ), is_my_score_request_in_progress( false ), overall_min_request_indices( _heap ) {}

		bool HasRequestInProgress( FILTER filter_in, int row_index ) const { 
			
			MABASSERTMSG(row_index >= 0, "This gets used for an array lookup later. The lookup is checked so it is safe to continue.");
			
			switch( filter_in )
			{
			case FRIEND:
#if /*(PLATFORM_XBOXONE) &&*/ ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE
			case FRIEND_SEVEN:
#endif
				return is_friend_request_in_progress;
			case MY_SCORE: 
#if /*(PLATFORM_XBOXONE) &&*/ ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE
			case MY_SCORE_SEVEN:
#endif				
				return is_my_score_request_in_progress;
			case OVERALL:
#if /*(PLATFORM_XBOXONE) &&*/ ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE
			case OVERALL_SEVEN:
#endif	

				for( typename MabMap< Id_type, int >::iterator iter = overall_min_request_indices.begin(); iter != overall_min_request_indices.end(); ++iter )
				{
					iter->second = row_index;
				}
				return ( overall_min_request_indices.size() > 0 );
			default:
				MABBREAKMSG( "Don't send NUM_FILTERS to this function!" );
				return false;
			}
		}

		void Reset() {
			is_friend_request_in_progress = is_my_score_request_in_progress = false;
			overall_min_request_indices.clear();
		}

		/// To be called when you request a new range of leaderboard entries.
		/// @param filter_in		The filter of the request type you're making.
		///	@param min_row_index	The minimum index you're trying to read (for OVERALL only)
		void NewRequest( FILTER filter_in, Id_type message_id = 0, int req_index = 0 )
		{
			switch( filter_in )
			{
			case FRIEND:
#if /*(PLATFORM_XBOXONE) &&*/ ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE
			case FRIEND_SEVEN:
#endif
				MABASSERTMSG( !is_friend_request_in_progress, "There should only be one Friends request at a time!" );
				is_friend_request_in_progress = true;
				break;
			case MY_SCORE:
#if /*(PLATFORM_XBOXONE) &&*/ ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE
			case MY_SCORE_SEVEN:
#endif	
				MABASSERTMSG( !is_my_score_request_in_progress, "There should only be one My Score request at a time!" );
				is_my_score_request_in_progress = true;
				break;
			case OVERALL:
#if /*(PLATFORM_XBOXONE) &&*/ ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE
			case OVERALL_SEVEN:
#endif
				MABASSERTMSG( overall_min_request_indices.find( message_id ) == overall_min_request_indices.end(), "We already know about this requested_min_index!" );
				overall_min_request_indices[ message_id ] = req_index;
				break;
			default:
				MABBREAKMSG( "Do not send NUM_FILTERS to this function!" );
			}
		}

		/// To be called when you've received a new range of leaderboard entries from the system.
		/// @param filter_in		The filter of the request type you've made.
		///	@param min_row_index	The minimum index you've read (for OVERALL only)
		int ClearRequest( FILTER filter_in, Id_type message_id = 0 )
		{
			switch( filter_in )
			{
			case FRIEND:
#if /*(PLATFORM_XBOXONE) &&*/ ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE
			case FRIEND_SEVEN:
#endif
				MABASSERTMSG( is_friend_request_in_progress, "No requested Friends request found!" );
				is_friend_request_in_progress = false;
				return 0;
			case MY_SCORE:
#if /*(PLATFORM_XBOXONE) &&*/ ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE
			case MY_SCORE_SEVEN:
#endif	
				MABASSERTMSG( is_my_score_request_in_progress, "No requested My Score request found!" );
				is_my_score_request_in_progress = false;
				return 0;
			case OVERALL:
#if /*(PLATFORM_XBOXONE) &&*/ ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE
			case OVERALL_SEVEN:
#endif
			{
				typename MabMap< Id_type, int >::iterator msg_find = overall_min_request_indices.find( message_id );
				MABASSERTMSG( ( msg_find != overall_min_request_indices.end() ), "We don't have any requests with this starting index!" );
				
				if( msg_find == overall_min_request_indices.end())
				{
					return 0;
				}

				int to_go_out = msg_find->second;
				overall_min_request_indices.erase( msg_find );
				return to_go_out;
			}
			default:
				MABBREAKMSG( "Unknown filter argument to ClearRequest!" );
				return 0;
			}
		}
	
		bool IsProcessingFriendRequest() const { return is_friend_request_in_progress; }
		bool IsProcessingMyScoreRequest() const { return is_my_score_request_in_progress; }

	private:
		bool is_friend_request_in_progress, is_my_score_request_in_progress;
		/// Map from overlapped id to min_requested_index
		/// Needs to be mutable because it has the potential to change
		///	in HasRequestInProgress, which is and should remain a const
		///	method.
		mutable MabMap< Id_type, int > overall_min_request_indices;
	};

	/// Memory heap
	MABMEM_HEAP heap;

	/// @name main data fields
	/// @{

	///Delegate for leaderboard data calls
	LeaderboardDelegate leaderboardDelegate;

	/// the id of the leaderboard, to be specified by the platform
	const LeaderboardIDType leaderboard_id;

	///	the title of this leaderboard
	const FString title;

	/// The list of custom data columns--everything after ranking and gamer name
	/// NOTE: This might need to become not-a-reference if these aren't maintained anywhere else.
	TArray<LeaderboardColumnDataType> custom_columns;

	/// Pointer to the LeaderboardPopulator that corresponds to this leaderboard
	UWWUILeaderboardPopulator* populator;

	/// A list of ALL the names of the columns
	/// Should be const, but must be initialised by subclasses.
	TArray< FString > full_col_name_list;

	/// A pointer to the field formatter for this particular leaderboard.
	//RULeaderboardFieldFormatter* field_formatter;

	///	the filter that this leaderboard is currently pointing to
	FILTER current_filter;

	/// the TOTAL number of entries in the OVERALL filter; only updated after return of successful stats call
	/// Note that this is different from data_list.size(): if a leaderboard has 8000 entries on it, and we
	///	can only return 100 of those at a time, then the size of data_list will be 100, while this variable
	///	will be 8000.
	unsigned int num_overall_entries;

	/// The number of fields that are displayed by a given leaderboard; updated by Queue
	int display_num;

	/// A map indexed by filter that contains a flag indicating whether a read request has come back
	///	as having NO entries in it. If the flag corresponding to the filter sent in a ReqestData is true,
	///	then we can immediately send back a vector full of empty strings.
	MabMap< FILTER, bool > has_no_results;

	/// \brief The data gathered by RequestData calls.
	/// The first TArray is the listing by row: the index into this vector is held by data_index_mapping below.
	///	The inner TArrays all contain the column-by-column data of each individual row.
	FilterToRowMap row_data;

	/// The top row of the most recent data request
	MabMap< FILTER, unsigned int > selected_top_row;

	/// \brief The rank of the player who's accessed this leaderboard.
	///	This field can only be populated found by a call to ReadStats that includes the player's rank
	unsigned int my_rank;

	/// @}

	/// \brief A function to determine if we're already fulfilling a given request.
	///	If we are, it means we'll drop this request and wait for the outstanding one to resolve.
	bool HasDuplicateRequest(FILTER filter_in, int min_display_index) const { return false;};

	/// \brief A helper function for OVERALL-filter leaderboard requests
	/// Implemented by platform-specific subclasses
	/// NOTE: This is NOT, in this case, some sort of helper function that everyone goes through; "overall" in
	///	this case refers to the filter.
	void OverallDataRequestHelper(int min_requested_index, const RequestTuple& req_tuple) { return;};

	/// \brief A helper function for FRIEND-filter leaderboard requests
	void FriendDataRequestHelper(int min_requested_index, const RequestTuple& req_tuple) { return;};

	/// \brief A helper function for MY_SCORE_filter leaderboard requests
	void MyScoreDataRequestHelper(const RequestTuple& req_tuple) { return; };

#if PLATFORM_WINDOWS && defined ENABLE_STEAM
	// Needed to get access to RequestTuple.
	friend class SIFSteamLeaderboardRequest;
#endif
};

/// \class UWWUILeaderboardQueue
/// The collection of all leaderboards associated with a given title.
///	<AUTHOR> Barrett
///
class UWWUILeaderboardQueue
{
	/// Friending needed to populate display_num below; will not be known until the leaderboard UI is initialised
	friend class UWWUILeaderboardPopulator;

	/// Leaderboards need access to the private members of their parent queue
	friend class UWWUILeaderboard;

public:
	UWWUILeaderboardQueue(UWWUILeaderboardPopulator* _populator = NULL );
	virtual ~UWWUILeaderboardQueue() {}

	virtual bool Initialise() { return true; }
	virtual void Update() {}
	virtual void CleanUp();

	/// Return the leaderboard the player is looking at right now
	inline UWWUILeaderboard* GetCurrentLeaderboard() { return current_leaderboard; }

	/// Return the index of the current leaderboard
	inline unsigned int GetCurrentLeaderboardIndex() const { return (unsigned int) current_leaderboard_index; }

	/// Return the leaderboard by id
	UWWUILeaderboard* GetLeaderboard( const LeaderboardIDType& id_in )
	{
		if (leaderboard_queue.IsValidIndex(0))
		{
			for (UWWUILeaderboard* lbrd : leaderboard_queue)
			{
				if (id_in == lbrd->GetID())
				{
					return lbrd;
				}
			}
		}
		MABBREAKMSG( "Could not find a leaderboard with the given ID!" );
		return NULL;
	}

	/// Return the leaderboard queue
	const TArray< UWWUILeaderboard* >& GetLeaderboardQueue() const { return leaderboard_queue; }
	TArray< UWWUILeaderboard* >& GetLeaderboardQueue() { return leaderboard_queue; }

	/// Clear the caches of all the leaderboards in the queue
	void FlushAllCaches();

	/// Reset all if the queue's leaderboards' filters to FRIENDS state
	void ResetAllFilters();

	/// @name Leaderboard-changing functions
	/// @{

	/// \brief Set the leaderboard to the "next" leaderboard, where the order is set upon initialisation by reading the leaderboards.xml file
	///	@param	clear_cache		Whether or not to clear the cache of the leaderboard you're LEAVING
	///	@param	start_from_top	IFF we've already viewed this leaderboard, and this field is false, we
	///							start from the position we were looking at the last time we left this particular leaderboard.
	/// @param	is_sync_out		Optional value to get a boolean out determining whether or not a given operation was able to stay synchronous--no loading message needed if so
	/// @param	ui_elem_name	Optional - the name of the UI element that the data from this request should be filled by
	/// @return					the new leaderboard
	UWWUILeaderboard* IncrementLeaderboard( bool clear_cache = false, bool start_from_top = false, bool* const is_sync_out = NULL, const FString& ui_elem_name = "" );

	/// \brief Set the leaderboard to the "previous" leaderboard, where the order is set upon initialisation by reading the leaderboards.xml file
	///	@param	clear_cache		Whether or not to clear the cache of the leaderboard you're LEAVING
	///	@param	start_from_top	IFF we've already viewed this leaderboard, and this field is false, we
	///							start from the position we were looking at the last time we left this particular leaderboard.
	/// @param	is_sync_out		Optional value to get a boolean out determining whether or not a given operation was able to stay synchronous--no loading message needed if so
	/// @param	ui_elem_name	Optional - the name of the UI element that the data from this request should be filled by
	/// @return					the new leaderboard
	UWWUILeaderboard* DecrementLeaderboard( bool clear_cache = false, bool start_from_top = false, bool* const is_sync_out = NULL, const FString& ui_elem_name = "" );

	/// \brief Set the leaderboard to a given leaderboard
	///	@param	new_leaderboard	The leaderboard to change to the view to
	///	@param	clear_cache		Whether or not to clear the cache of the leaderboard you're LEAVING
	///	@param	start_from_top	IFF we've already viewed this leaderboard, and this field is false, we
	///							start from the position we were looking at the last time we left this particular leaderboard.
	/// @param	is_sync_out		Optional value to get a boolean out determining whether or not a given operation was able to stay synchronous--no loading message needed if so
	/// @param	ui_elem_name	Optional - the name of the UI element that the data from this request should be filled by
	/// @return					the new leaderboard
	UWWUILeaderboard* SetLeaderboard( UWWUILeaderboard* const new_leaderboard, bool clear_cache = false, bool start_from_top = false, bool* const is_sync_out = NULL, const FString& ui_elem_name = "" );

	/// Set the leaderboard to the leaderboard with the given index
	///	@param	new_leaderboard_index	The index of the leaderboard to change the view to in (the field) leaderboard_queue
	///	@param	clear_cache				Whether or not to clear the cache of the leaderboard you're LEAVING
	///	@param	start_from_top			IFF we've already viewed this leaderboard, and this field is false, we
	///									start from the position we were looking at the last time we left this particular leaderboard.
	/// @param	is_sync_out				Optional value to get a boolean out determining whether or not a given operation was able to stay 
	///									synchronous--no loading message needed if so
	/// @param	ui_elem_name			Optional - the name of the UI element that the data from this request should be filled by
	/// @return							the new leaderboard
	UWWUILeaderboard* SetLeaderboard( unsigned int new_leaderboard_index, bool clear_cache, bool start_from_top = false, bool* const is_sync_out = NULL, const FString& ui_elem_name = "" );

	/// @}

	/* #rc3_legacy*/
	/*
	class UpdateJob {
	public:
		UpdateJob(UWWUILeaderboardQueue *_ctrl) :
			MabWaitableJob("UWWUILeaderboardQueue"),
			ctrl(_ctrl)
		{};
		virtual ~UpdateJob() 
		{
		
			if (getFinished())
				;//printf("GOOD JOB ROBOT\n");
			else
				printf("BAD JOB ROBOT\n");
			
		};
		virtual void Execute(MabThreadContext* context)
		{
			MABUNUSED(context);
			jobStarted = true;
			if (ctrl)
				ctrl->Update();
			jobCompleted = true;
		};
	private:
		UWWUILeaderboardQueue *ctrl;
	};
	*/
protected:
	
	MABMEM_HEAP heap;

	/// Our populator
	UWWUILeaderboardPopulator* populator;

	/// The list of all the leaderboards
	TArray< UWWUILeaderboard* > leaderboard_queue;

	/// Our field formatter.
	//RULeaderboardFieldFormatter field_formatter;

	/// the leaderboard that the player is currently examining
	UWWUILeaderboard* current_leaderboard;

	/// the index of current_leaderboard in the leaderboard_queue
	int current_leaderboard_index;

	/// The number of fields that are displayed by a given leaderboard
	int display_num;

	/// Set the display_num not only of yourself but of all the leaderboards in the queue
	void SetDisplayNum( int _display_num );

	/// Set the Leaderboard populator not only of yourself but of all the leaderboards in the queue
	void SetLeaderboardPopulator( UWWUILeaderboardPopulator* _populator );

	/// \brief Helper function for all methods involving changing leaderboards
	///	@param old_filter		The filter of the previously-selected leaderboard. Our assumption is
	///							that with this call we want to change leaderboard ONLY and fix the filter
	///							to whatever it was previously.
	///	@param start_from_top	IFF we've already viewed this leaderboard, and this field is false, we
	///							start from the position we were looking at the last time we left this particular leaderboard.
	/// @param	is_sync_out		Optional value to get a boolean out determining whether or not a given operation was able to stay synchronous--no loading message needed if so
	UWWUILeaderboard* LeaderboardChangeHelper( UWWUILeaderboard::FILTER old_filter, bool start_from_top, bool* const is_sync_out, const FString& ui_elem_name );
};

/// \class UWWUILeaderboardDataRow
///	An inner class meant to represent a single row of a leaderboard table.
class UWWUILeaderboardDataRow : public MabRuntimeType
{
	MABRUNTIMETYPE_HEADER( UWWUILeaderboardDataRow )
public:
	/// Leaderboards should have access to the private method in this class, To1DVector, but no one else should
	/// UPDATE: We have to do away with this very nice idea, because now that UWWUILeaderboardDataRow and Leaderboards both have 
	///	subclasses, the friending would get too hairy. So we're just making UWWUILeaderboardDataRow a wholly public class. At
	///	least To1DVector is const.
	//friend class UWWUILeaderboard;

	/// Rank is the sorting column for leaderboards--it's this row's rank vis a vis all the other rows.
	/// NOTE: RANKS ARE ALWAYS 1-BASED, not 0-based.
	unsigned int rank;

	/// The name of the player that this this row belongs to.
	FString player_name;

	/// The list of custom strings after rank and player_name in this row.
	TArray< FString > addl_strings;

	UWWUILeaderboardDataRow( unsigned int _rank = 0, FString _player_name = "", TArray< FString > _addl_strings = TArray<FString> () )
		:	rank ( _rank ), player_name( _player_name ), addl_strings( _addl_strings ) {};
	bool Empty() { return ( rank == 0 ) && ( player_name == "" ) && ( addl_strings.Num() == 0 ); }
//protected:
	
	/// Convert this row to a 1D vector of strings
	///	Rank + player_name + add_strings
	/// NOTE: Will APPEND this record to the vector of strings that gets passed in. Repeated calls are how to
	///	make the 2D leaderboard structure inherent in a list of UWWUILeaderboardDataRows into a 1D vector for population.
	virtual void To1DVector( TArray < FString >& vector_out ) const;
};