// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIScreenMainMenu.h"

//Screens
#include "UI/Screens/WWUIScreenControllerAssignment.h"
#include "UI/Screens/WWUIScreenCareerHUB.h"
#include "UI/Screens/WWUIScreenCareerSetup.h"
#include "UI/Screens/WWUIScreenCareerSelectTeam.h"
#include "UI/Screens/WWUIScreenSignIn.h"
#include "UI/Screens//WWUIScreenCustomisePlayer.h"
#include "UI/Screens//WWUIScreenCustomiseSelectTeam.h"
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "UI/Screens/WWUIScreenCompetitionSelect.h"
#include "UI/Screens/WWUIScreenCareerLoad.h"
#include "UI/Screens/WWUIScreenCompetitionLoad.h"

//Components
#include "TextBlock.h"
#include "Button.h"
#include "WWUIFunctionLibrary.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "GameModes/RugbyGameModeBase.h"
#include "WWUILegendBox.h"
#include "CanvasPanelSlot.h"
#include "Rugby/UI/Screens/Modals/WWUIModalWarningMessage.h"
#include "Rugby/UI/Screens/Modals/WWUIModalDeleteFile.h"
#include "WWUIListField.h"
#include "WWUIRichTextBlockWithTranslate.h"

//SIF Helpers
#include "Rugby/Utility/Helpers/RUFranchiseHelpers.h"
#include "Rugby/Utility/Helpers/SIFUIHelpers.h"
#include "Rugby/Utility/Helpers/SIFGameHelpers.h"
#include "Rugby/Utility/Helpers/SIFInGameHelpers.h"
#include "Rugby/Utility/Helpers/SIFPlayerHelpers.h"
#include "Rugby/Utility/Helpers/SIFGeneralHelpersPS4.h"
#include "Rugby/Utility/Helpers/SIFAudioHelpers.h"
#include "Rugby/Utility/Helpers/SIFMatchmakingHelpers.h"
#include "Rugby/Utility/Helpers/SIFNamedValueListWrapper.h"
#include "SIFMissingControllerListener.h"
#include "RUUIDatabaseQueryManager.h"

//Localisation
#include "UI/WWUILocale.h"
#include "WWUITranslationManager.h"
#include "WWUITranslatorStringTable.h"
#include "UI/GeneratedHeaders/WWUIScreenMainMenu_UI_Namespace.h"
#include "StringTable.h"

//Cutscenes
#include "Rugby/Match/Cutscenes/SSCutSceneManager.h"
#include "Rugby/Match/Cutscenes/SSCutSceneTags.h"
#include "Utility/Helpers/SIFNamedValueListWrapper.h"

// FanHub
#include "Rugby/FanHub/WWRugbyFanHubService.h"
#include "Utility/Helpers/SIFRichPresenceHelpers.h"
#include "OnlineExternalUIInterface.h"
#include "DelegateSignatureImpl.inl"
#include "WWUIScreenTeamDetails.h"
#include "WWUIScreenOnlineSearchResults.h"
#include "Match/PlayerProfile/SIFPlayerProfilePropertyDefs.h"
#include "Modals/WWUIModalSwitchFriendPlay.h"
#include "Components/WWUIMainMenuField.h"
#include "Modals/WWUIModalWaiting.h"
#include "Databases/RUGameDatabaseManager.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Database.h"
#include "GameFramework/DefaultPawn.h"
#include "WWUIScreenMatchSettings.h"

#include "Modals/WWUIModalWarningMessageNetworkConnectivity.h"

#if wwDEBUG_SRA
#include "Match/RugbyUnion/Statistics/RUStatsScorer.h"
#endif
#include "Networking/ConnectionManager.h"

#include "drm/MemCheck.h"

#if PLATFORM_WINDOWS
#define MAINMENU_AUTOSIGNIN_ENABLED 1
#else
#define MAINMENU_AUTOSIGNIN_ENABLED 0
#endif

constexpr auto SUBMENUPROPERTYNAME = "SubMenuPanel";
constexpr auto FIRSTSUBMENUANIMATIONNAME = "moveToSubMenu";
constexpr auto SECONDSUBMENUANIMATIONNAME = "moveToSecondSubMenu";
constexpr auto SELECTIONBARSIZERATIO = 3;
#define PROJSTRINGTABLE FName("/Game/UI/Datatables/LocalisationStringtables/English/StringtableProjEnglish.StringtableProjEnglish")

#define UPLOAD_FANHUB_DATA_ON_CREDITS 0

//#if UE_BUILD_SHIPPING
//#define SHOW_CHAMPION_DATA_DIALOGUE_ON_MENU_OPEN 0
//#else
#define SHOW_CHAMPION_DATA_DIALOGUE_ON_MENU_OPEN 0
//#endif

//First functions are new functions to replace functionality from lua that no longer exists - MB

void UWWUIScreenMainMenu::Startup(UWWUIStateScreenData* InData)
{
	m_allowedToProceed = true;
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		m_gameInstance->SetInOnlineArea(false);
	}

#if PLATFORM_SWITCH
	if (SIFApplication::GetApplication())
	{
		SIFApplication::GetApplication()->SignOutSplitScreenPlayers();
	}

	if (PlatformStatusChecker == nullptr)
	{
		PlatformStatusChecker = NewObject<UPlatformStatusChecker>(this);
	}

#endif

#ifdef UI_USING_UMG
	//OnCreate
	UWidget* xbox_one_help = FindChildWidget(WWUIScreenMainMenu_UI::LegendText);
	if (xbox_one_help)
	{
		if (SIFGameHelpers::GAIsPlatformXboxOne())
		{
			xbox_one_help->SetVisibility(ESlateVisibility::Visible);
			xbox_one_help->SetIsEnabled(true);
		}
		else
		{
			//xbox_one_help->SetVisibility(ESlateVisibility::Collapsed);
			//xbox_one_help->SetIsEnabled(false);
		}
	}

	if (SIFGameHelpers::GAGetDisabledTutorials())
	{
		UWidget* tutorials_node = FindChildWidget(WWUIScreenMainMenu_UI::Tutorials);
		tutorials_node->SetVisibility(ESlateVisibility::Collapsed);
		tutorials_node->SetIsEnabled(false);
	}

	//Some of my code to replace focus and variable storage from lua - MB
	//Find the first element of the main menu and set focus
	UVerticalBox* mainMenu = Cast<UVerticalBox>(FindChildWidget(WWUIScreenMainMenu_UI::MainMenu));

	if (mainMenu)
	{
		if (m_gameInstance)
		{
			UWidget* child = mainMenu->HasAnyChildren() ? mainMenu->GetChildAt(0) : nullptr;
			UWWUIListField* listField = child != nullptr ? Cast<UWWUIListField>(child) : nullptr;
			if (listField)
			{
				SetFocusToWidget(listField->GetButtonWidget(), GetMasterPlayerController());
			}
		}
	}
	else
	{
		return;
	}

	//Cache menus
	TArray<UWidget*> widgetArray;
	WidgetTree->GetAllWidgets(widgetArray);

	for (UWidget* widget : widgetArray)
	{
		UVerticalBox* vertBox = Cast<UVerticalBox>(widget);

		if (vertBox)
		{
			menuList.Add(vertBox);
			if (vertBox != mainMenu)
			{
				vertBox->SetVisibility(ESlateVisibility::Collapsed);
			}
		}
	}

	//Show the first submenu
	if (UWWUIListField* focusedListField = Cast<UWWUIListField>(mainMenu->GetChildAt(0)))
	{
		UProperty* submenuProperty = FindField<UProperty>(focusedListField->GetClass(), SUBMENUPROPERTYNAME);
		if (submenuProperty)
		{
			UPanelWidget* Panel = *submenuProperty->ContainerPtrToValuePtr<UPanelWidget*>(focusedListField);
			if (!Panel)
			{
				Panel->SetVisibility(ESlateVisibility::Visible);
			}
		}
	}
#endif

	UWidget* quitButton = FindChildWidget(WWUIScreenMainMenu_UI::QuitGame);
	if (quitButton)
	{
#if PLATFORM_WINDOWS && FALSE //Nick GG - Removeed fro a milestone build as to not break the temp UI // TODO put this back in!
		quitButton->SetVisibility(ESlateVisibility::Visible);
#else
		quitButton->SetVisibility(ESlateVisibility::Collapsed);
#endif

#if WITH_EDITOR
		quitButton->SetVisibility(ESlateVisibility::Collapsed);
#endif
	}

	AdjustMenuForOnline();

	/*
	#if PLATFORM_WINDOWS
		if (UWidget* privateButton = FindChildWidget(WWUIScreenMainMenu_UI::PrivateMatch))
		{
			privateButton->SetVisibility(ESlateVisibility::Collapsed);
		}
	#endif*/

	ConnectionTestSignInFor = ESignInFor::PLAYER_UPLOADS;
#if defined (FANHUB_ENABLED)
	if (SIFApplication::GetApplication())
	{
		UWWRugbyFanHubService* FanHubService = Cast<UWWRugbyFanHubService>(SIFApplication::GetApplication()->GetFanHubService());

		if (FanHubService)
		{
			// #rc3_legacy
			// This replaces some code in AFL for the fan hub.
#if MAINMENU_AUTOSIGNIN_ENABLED
			{
				FwwHttpRequestComplete signInCallback;
				signInCallback.BindUObject(this, &UWWUIScreenMainMenu::SignInSuccessful);

#if PLATFORM_WINDOWS
				// Grab the user name and password stored in the profile and sign in using that.
				SIFPlayerProfile* pProfile = SIFPlayerProfileManager::GetInstance()->GetMasterProfile();
				MABASSERT(pProfile);

				if (pProfile)
				{
					MabObservedValueList* pMVL = pProfile->GetNamedValueList();

					if (pMVL)
					{
						FString SavedSignInUserName = "";
						FString SavedSignInPassword = "";

						const MabNamedValue* StoredUserNameString = pMVL->GetNamedValue(PLAYER_PROFILE_USERNAME);
						const MabNamedValue* StoredPasswordString = pMVL->GetNamedValue(PLAYER_PROFILE_PASSWORD);

						if (StoredPasswordString)
						{
							SavedSignInUserName = UTF8_TO_TCHAR(StoredUserNameString->ToCStr());
						}

						if (StoredPasswordString)
						{
							SavedSignInPassword = UTF8_TO_TCHAR(StoredPasswordString->ToCStr());
						}

						if (!SavedSignInUserName.IsEmpty() && !SavedSignInPassword.IsEmpty())
						{
							FanHubService->SignIn(SavedSignInUserName, SavedSignInUserName, SavedSignInPassword, signInCallback);
						}
						else
						{
#if !UE_BUILD_SHIPPING
							// Default sign in with console auto data.
							FanHubService->SignIn(signInCallback);
#endif
						}
					}
				}
#else
				// Default sign in with console auto data.
				FanHubService->SignIn(signInCallback);
#endif
			}
#endif
			if (FanHubService->GetSearchManager())
			{
				//FanHubService->GetSearchManager()->ClearOptionArray();
			}
			FanHubService->SetSortKey(WWAflFanhubSortableField::ActiveUsers);
			FanHubService->SetSortOrder(-1);
			FanHubService->ClearCacheMaps();
		}
	}
#endif


	//Populate the save file lists.
	//if (!SIFGameHelpers::GAIsConsole())
	{
		PopulateSaveFileLists();
	}

	SetupMenuButtons();

	SIFRichPresenceHelpers::RPSetPresenceMenus();
}

//===============================================================================
//===============================================================================
void UWWUIScreenMainMenu::Shutdown()
{
	if (SIFApplication::GetApplication() && SIFApplication::GetApplication()->GetConnectionManager())
	{
		SIFApplication::GetApplication()->GetConnectionManager()->NetworkConnected.Remove(CheckLiveDataVersionHandle);
	}

	UWWUIFunctionLibrary::StopTimer(regainControllerFocus);

	Super::Shutdown();
}

//===============================================================================
//===============================================================================
void UWWUIScreenMainMenu::PopulateSaveFileLists()
{
	if (UWWUIScrollBox* compScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenMainMenu_UI::CompetitionList)))
	{
		compScrollbox->PopulateAndRefresh();
	}
	if (UWWUIScrollBox* beAProScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenMainMenu_UI::BeAProList)))
	{
		beAProScrollbox->PopulateAndRefresh();
	}
	if (UWWUIScrollBox* careerScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenMainMenu_UI::CareersList)))
	{
		careerScrollbox->PopulateAndRefresh();
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenMainMenu::PlayGoCheck()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		SIFGameWorld* pGameWorld = m_gameInstance->GetActiveGameWorld();

		if (pGameWorld && bIsPlayGo)
		{
			for (int i = 0; i < PlayGoLockedOptions.Num(); i++)
			{
				if (UWWUIListField* field = Cast<UWWUIListField>(FindChildWidget(PlayGoLockedOptions[i])))
				{
					LockOption(field, pGameWorld->GetIsPlayGo());
				}
			}
			bIsPlayGo = pGameWorld->GetIsPlayGo();
		}

		if (UWidget* playGoNotification = FindChildWidget(WWUIScreenMainMenu_UI::PlayGoNotification))
		{
			playGoNotification->SetVisibility(bIsPlayGo ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		}
	}
}

void UWWUIScreenMainMenu::Update(float DeltaTime)
{
	//This sucks but im not really not sure how else to do it, we use this to close the online sub menu as needed
	if (SIFApplication::GetApplication()->CloseSubMenu)
	{
		//We need to do this for both main menu and return to onlien menu
		if (SIFApplication::GetApplication()->SubMenuCloseType == EOnlineSubMenuCloseType::TOnlineMenu ||
			SIFApplication::GetApplication()->SubMenuCloseType == EOnlineSubMenuCloseType::TMainMenu)
		{
			//This will take us back to the online menu
			GoBackMenu(GetOwningPlayer());
		}

		//We only need to do this if we are heading out of the onlien menu
		if (SIFApplication::GetApplication()->SubMenuCloseType == EOnlineSubMenuCloseType::TMainMenu)
		{
			//This will take us back to the main menu
			GoBackMenu(GetOwningPlayer());
		}

		SIFApplication::GetApplication()->SubMenuCloseType = EOnlineSubMenuCloseType::TNone;

		SIFApplication::GetApplication()->CloseSubMenu = false;
	}

	PlayGoCheck();

	// If we were kicked from online, show a message.
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		if (pRugbyGameInstance && pRugbyGameInstance->GetReturnToMenuReason() != ERugbyDisconnectionReason::None)
		{
			FString ReturnTitle;
			FString ReturnBody;
			UConnectionManager::GetDisconnectReasonString(pRugbyGameInstance->GetReturnToMenuReason(), ReturnTitle, ReturnBody);

			if (!ReturnBody.IsEmpty() || !ReturnTitle.IsEmpty())
			{
				pRugbyGameInstance->ShowErrorUIPopup(ReturnTitle, ReturnBody, "");
			}

			if (pRugbyGameInstance->GetReturnToMenuReason() == ERugbyDisconnectionReason::Kicked)
			{
				if (pRugbyGameInstance->GetKickedFromOnline())
				{
					pRugbyGameInstance->SetKickedFromOnline(false);
				}
			}

			pRugbyGameInstance->SetReturnToMenuReason(ERugbyDisconnectionReason::None);
		}

	}

#if PLATFORM_SWITCH
	NexUpdate(DeltaTime);
#endif

}

//Registers screen input functions
#ifdef UI_USING_UMG
void UWWUIScreenMainMenu::RegisterFunctions()
{
	//Screen navigation
	AddInputAction(FString("UI_Back"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenMainMenu::GoBackMenu));

	// We are just gonna sign in for now.
#if PLATFORM_XBOXONE
	AddInputAction("RU_UI_ACTION_SWITCH_USER", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenMainMenu::OnSwitchUser));
#elif PLATFORM_WINDOWS
	// Windows you can sign in/out of fan hub
	AddInputAction("RU_UI_ACTION_CHANGE_STRIP", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenMainMenu::OnSignInPressed));
#endif
	AddInputAction("RU_UI_ACTION_CAREER_DELETE", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenMainMenu::CompetitionOnDeleteButton));

#if wwDEBUG_SRA
	AddInputAction("RU_UI_ACTION_LEFT", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenMainMenu::TestLeft));
	AddInputAction("RU_UI_ACTION_RIGHT", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenMainMenu::TestRight));
#endif
}

//===============================================================================
//===============================================================================
void UWWUIScreenMainMenu::SetupMenuButtons()
{
	//Register button functions
	if (UWWUIListField* quickMatch = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::SingleMens)))
	{
		quickMatch->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::QuickMatchOptionOnClick);
	}
	if (UWWUIListField* sevensQuickMatch = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::SingleWomens)))
	{
		sevensQuickMatch->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::SevensQuickMatchOptionOnClick);
	}
	if (UWWUIListField* credits = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::Credits)))
	{
		credits->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnCreditsClicked);
	}
	if (UWWUIListField* controls = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::Controls)))
	{
		controls->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnControlsClicked);
	}
	if (UWWUIListField* onlineSubmenu = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::PlayOnline)))
	{
		onlineSubmenu->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnlineOption);
	}
	if (UWWUIListField* onlineFifteens = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::OnlineMens)))
	{
		onlineFifteens->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnlineQuickMatchFifteens);
	}
	if (UWWUIListField* onlineSevens = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::OnlineWomens)))
	{
		onlineSevens->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnlineQuickMatchSevens);
	}
	if (UWWUIListField* onlinePrivateFifteens = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::OnlinePrivateMens)))
	{
		onlinePrivateFifteens->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnlinePrivateMatchFifteens);
	}
	if (UWWUIListField* onlinePrivateSevens = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::OnlinePrivateWomens)))
	{
		onlinePrivateSevens->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnlinePrivateMatchSevens);
	}
	if (UWWUIListField* onlinePrivateFifteens = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::OnlineRankedMens)))
	{
		onlinePrivateFifteens->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnlineRankedMatchFifteens);
	}
	if (UWWUIListField* onlinePrivateSevens = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::OnlineRankedWomens)))
	{
		onlinePrivateSevens->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnlineRankedMatchSevens);
	}
	if (UWWUIListField* career = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::NewCareer)))
	{
		career->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::CoachCareerNewOnClick);
	}
	if (UWWUIListField* competition = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::NewCompetition)))
	{
		competition->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::CompetitionNewOnClick);
	}
	if (UWWUIListField* freeRoam = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::RunAround)))
	{
		freeRoam->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::RunAroundOnClick);
	}
	if (UWWUIListField* tutorial = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::Tutorials)))
	{
		tutorial->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::TutorialsOnClick);
	}
	if (UWWUIListField* gameplayOptions = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::Gameplay)))
	{
		gameplayOptions->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnGameplayOptionsClicked);
	}
	if (UWWUIListField* sound = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::Sound)))
	{
		sound->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnSoundClicked);
	}
	if (UWWUIListField* camera = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::CameraAndVisuals)))
	{
		camera->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnCameraSettingsClicked);
	}
	if (UWWUIListField* quit = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::QuitGame)))
	{
		quit->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::QuitOptionOnClick);
	}
	if (UWWUIListField* graphics = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::Graphics)))
	{
		graphics->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::GraphicsOptionOnClick);
	}
	if (UWWUIListField* languages = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::Language)))
	{
		languages->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::LanguageOptionOnClick);
	}
	if (UWWUIListField* leaderboards = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::Leaderboards)))
	{
		leaderboards->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnlineLeaderboardsOnClick);
	}
	if (UWWUIListField* createPlayer = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::CreatePlayer)))
	{
		createPlayer->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::NewPlayerOnClick);
	}
	if (UWWUIListField* editPlayer = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::EditPlayer)))
	{
		editPlayer->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::EditPlayerOnClick);
	}
	if (UWWUIListField* beAPro = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::NewPro)))
	{
		beAPro->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::BeAProOnClick);
	}
	if (UWWUIListField* myUploads = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::PlayersMyUploads)))
	{
		myUploads->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::MyUploadsOnClick);
	}
	if (UWWUIListField* mostPopular = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::PlayersMostPopular)))
	{
		mostPopular->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::MostPopularOnClick);
	}
	if (UWWUIListField* teamMyUploads = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::TeamsMyUploads)))
	{
		teamMyUploads->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::TeamMyUploadsOnClick);
	}
	if (UWWUIListField* teamMostPopular = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::TeamsMostPopular)))
	{
		teamMostPopular->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::TeamMostPopularOnClick);
	}
	//< Register: Customise Teams >
	if (UWWUIListField* customiseButton = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::Customise)))
	{
		customiseButton->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnCustomiseMenuClicked);
	}
	if (UWWUIListField* createFifteensTeam = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::CreateMensTeam)))
	{
		createFifteensTeam->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::NewTeamOnClick);
	}
	if (UWWUIListField* createSevensTeam = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::CreateWomensTeam)))
	{
		createSevensTeam->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::NewSevensTeamOnClick);
	}
	if (UWWUIListField* linkTeams = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::LinkTeams)))
	{
		linkTeams->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::LinkTeamOnClick);
	}
	if (UWWUIListField* customiseSelectTeam = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::EditTeam)))
	{
		customiseSelectTeam->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::EditTeamOnClick);
	}
	if (UWWUIListField* LinkPlayers = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::LinkPlayers)))
	{
		LinkPlayers->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::LinkPlayerOnClick);
	}
	if (UWWUIListField* RevertEditedPlayers = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::RevertEditedPlayers)))
	{
		RevertEditedPlayers->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::ResetPlayersOnClick);
	}
	if (UWWUIListField* RevertEditedTeams = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::RevertEditedTeams)))
	{
		RevertEditedTeams->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::ResetTeamsOnClick);
	}
	if (UWWUIListField* CreateCompetition = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::CreateCompetition)))
	{
		CreateCompetition->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::NewCompetitionOnClick);
	}
	if (UWWUIListField* EditCompetition = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::EditCompetition)))
	{
		EditCompetition->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::EditCompetitionOnClick);
	}
	if (UWWUIListField* CreateKit = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::CreateKit)))
	{
		CreateKit->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::CreateKitOnClick);
	}
	if (UWWUIListField* EditKit = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::EditKit)))
	{
		EditKit->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::EditKitOnClick);
	}
	if (UWWUIListField* LinkKit = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::LinkKit)))
	{
		LinkKit->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::LinkKitOnClick);
	}
	if (UWWUIListField* Fanhub = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::Fanhub)))
	{
		Fanhub->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnFanHubMenuClicked);
	}
	if (UWWUIListField* LiveData = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::FanhubLiveData)))
	{
		LiveData->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::LiveDataOnClick);
	}
#if PLATFORM_SWITCH
	if (UWWUIMainMenuField* PrivateMatch = Cast<UWWUIMainMenuField>(FindChildWidget(WWUIScreenMainMenu_UI::PrivateMatch)))
	{
		PrivateMatch->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnlineSwitchFriendPlayOnClick);
		PrivateMatch->SetTitle(FText::FromString(UWWUITranslationManager::Translate("[ID_FRIENDS_LIST]")));
	}
#endif

#if PLATFORM_PS4 || PLATFORM_XBOXONE || PLATFORM_SWITCH
	//Diverting the display menu to just go straight to the camera and visuals settings on console, since the other two items in the list have been removed
	if (UWWUIListField* Display = Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::Display)))
	{
		Display->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::OnCameraSettingsClicked);
	}
#endif

	for (UVerticalBox* vertBox : menuList)
	{
		for (int i = 0; i < vertBox->GetChildrenCount(); i++)
		{
			if (UWWUIListField* field = Cast<UWWUIListField>(vertBox->GetChildAt(i)))
			{
				UProperty* submenu = FindField<UProperty>(field->GetClass(), SUBMENUPROPERTYNAME);
				if (submenu)
				{
					UPanelWidget* Panel = *submenu->ContainerPtrToValuePtr<UPanelWidget*>(field);
					bool hasSubMenu = false;
					if (Panel != nullptr)
					{
						hasSubMenu = true;

#if PLATFORM_PS4 || PLATFORM_XBOXONE || PLATFORM_SWITCH
						//Diverting the display menu to just go straight to the camera and visuals settings on console, since the other two items in the list have been removed
						if (Panel->GetName().Compare(WWUIScreenMainMenu_UI::DisplaySubmenu) == 0)
						{
							hasSubMenu = false;
						}
#endif
					}

					if (hasSubMenu)
					{
						field->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::ButtonPressed);
					}
					else
					{
						field->GetButtonWidget()->OnClicked.AddDynamic(this, &UWWUIScreenMainMenu::PlayAcceptSoundEffect);
					}
				}
			}
		}
	}
}

struct FFindFocusableWidgetMainMenu
{
	UWidget* FocusableWidget = nullptr;

	void operator()(UWidget* InWidget)
	{
#ifdef UI_LOG_FINDFOCUSABLEWIDGET
		UE_LOG(LogTemp, Warning, TEXT("FFindFocusableWidget %s"), *InWidget->GetName());
#endif

		if (FocusableWidget != nullptr)
			return;

		if (!InWidget->IsVisible())
		{
#ifdef UI_LOG_FINDFOCUSABLEWIDGET
			UE_LOG(LogTemp, Warning, TEXT("- Not visible"));
#endif

			return;
		}

		// UUserWidgets have bIsFocusable
		// SWidget have SupportsKeyboardFocus

		UUserWidget* UserWidget = Cast<UUserWidget>(InWidget);
		if (UserWidget != nullptr)
		{
			if (UserWidget->bIsFocusable)
			{
#ifdef UI_LOG_FINDFOCUSABLEWIDGET
				UE_LOG(LogTemp, Warning, TEXT("- Chosen Widget : %s"), *InWidget->GetName());
#endif

				FocusableWidget = InWidget;
				return;
			}
			else
			{
#ifdef UI_LOG_FINDFOCUSABLEWIDGET
				UE_LOG(LogTemp, Warning, TEXT("- Not focusable"));
#endif
			}
		}
		else
		{
#ifdef UI_LOG_FINDFOCUSABLEWIDGET
			UE_LOG(LogTemp, Warning, TEXT("- Not UserWidget"));
#endif
		}

		if (Cast<UButton>(InWidget))
		{
#ifdef UI_LOG_FINDFOCUSABLEWIDGET
			UE_LOG(LogTemp, Warning, TEXT("- Chosen Widget button : %s"), *InWidget->GetName());
#endif

			FocusableWidget = InWidget;
		}
	}
};

void UWWUIScreenMainMenu::SetFocusForControllers(APlayerController* InController /*= nullptr*/)
{
	//< Set generic focus for all controllers. >
	FSlateApplication::Get().SetAllUserFocusToGameViewport();

	//< Set Master Controller focus to stored focus object. >
	APlayerController* MasterPlayerController = GetMasterPlayerController();
	if (!MasterPlayerController)
	{
		/// If master controller is invalid, revert to super method. 
		Super::SetFocusForControllers(InController);
		return;
	}

	//< Get Master Controller ID. >
	if (!MasterPlayerController->IsLocalController())
		return;

	ULocalPlayer* LocalPlayer = MasterPlayerController->GetLocalPlayer();
	int32 ControllerId = (LocalPlayer != nullptr) ? LocalPlayer->GetControllerId() : INDEX_NONE;
	if (ControllerId < 0)
		return;

	TSharedPtr<SWindow> window;
	FWidgetPath WindowWidgetPath;
	UWidget* focusWidget = nullptr;
	if (FocusWidgetMap.Contains(MasterPlayerController))
	{
		focusWidget = FocusWidgetMap[MasterPlayerController];
	}
	else
	{
		UObjectProperty* ObjectProperty = FindField<UObjectProperty>(GetClass(), FName("firstWidget"));
		if (ObjectProperty)
		{
			UObject* WidgetPtr = ObjectProperty->GetPropertyValue_InContainer(this);
			focusWidget = Cast<UWidget>(WidgetPtr);
		}

		// If we didn't get a default widget search for a fallback
		if (focusWidget == nullptr)
		{

			FFindFocusableWidgetMainMenu Predicate;

			if (WidgetTree)
				WidgetTree->ForEachWidgetAndDescendants(Predicate);

			UWidget* focusableWidget = Predicate.FocusableWidget;

			if (UOBJ_IS_VALID(focusableWidget))
			{

				focusWidget = focusableWidget;
			}
		}

		// If we didn't get a fallback...
		// ScreenTemplate itself usually not focusable so fallback to containing window as per DropIn. 
		// User should still be able to register input, backout of screen etc which is better than nothing

		if (focusWidget == nullptr)
		{
			if (bIsFocusable)
			{
				focusWidget = this;
			}
			else
			{
				window = FSlateApplication::Get().FindWidgetWindow(TakeWidget(), WindowWidgetPath);
			}
		}
	}

	// rip
	if (!UOBJ_IS_VALID(focusWidget) && !window.IsValid())
		return;

	if (focusWidget != nullptr)
		SetFocusToWidget(focusWidget, MasterPlayerController);
	else if (window.IsValid())
		FSlateApplication::Get().SetUserFocus(ControllerId, WindowWidgetPath, EFocusCause::SetDirectly);
}
#endif

//Used went he focus check is done before focus switches
void UWWUIScreenMainMenu::FocusChangedCallback()
{
	FocusChanged(cached_new_focus_widget);
}

//Called from blueprints to allow for menu bar to follow focus
void UWWUIScreenMainMenu::FocusChanged(UWidget* newFocus)
{
	const float defaultRightOffset = -467.9400024f;

	cached_new_focus_widget = newFocus;

	//Find the menu selection bar
	// NRL 2025: GG DJH - We don't use the selection bar anymore
	//if (!menuSelectionBar)
	//{
	//	menuSelectionBar = Cast<UUserWidget>(FindChildWidget(WWUIScreenMainMenu_UI::MainMenuSelectBar));
	//}
	//else
	//{
	//	SIFUIHelpers::MenuSoundMenuMove();
	//}

	if (newFocus /*&& menuSelectionBar*/)
	{
		UVerticalBox* parent = Cast<UVerticalBox>(newFocus->GetParent());
		UVerticalBox* grandparent = nullptr;
		UWWUIScrollBox* subScrollbox = nullptr;

		float offsetFromTop = 0;

		if (parent)
		{
			UCanvasPanelSlot* parentSlot = Cast<UCanvasPanelSlot>(parent->Slot);

			if (parentSlot)
			{
				offsetFromTop = parentSlot->GetOffsets().Top + parent->GetChildIndex(newFocus) * 40;
#if defined(DISABLE_ONLINE)
				//Dealing with the missing online option
				if (offsetFromTop > 200.0f)
				{
					offsetFromTop -= 40;
				}
#endif
			}
		}
		else
		{
			//If no parent is found, it must be a sub scrollbox
			for (TObjectIterator<UVerticalBox> Itr; Itr; ++Itr)
			{
				UVerticalBox* Component = *Itr;

				//Find the vertical box submenu
				if (Component->HasFocusedDescendants())
				{
					grandparent = Component;

					//Find the sub scrollbox
					for (int i = 0; i < grandparent->GetChildrenCount(); i++)
					{
						if (Cast<UWWUIScrollBox>(grandparent->GetChildAt(i)))
						{
							subScrollbox = Cast<UWWUIScrollBox>(grandparent->GetChildAt(i));
							break;
						}
					}
				}
				if (subScrollbox)
					break;
			}

			//If this is a sub scrollbox (i.e. those in the competition save/load lists), find the offset of the scrollbox in the vertical box
			if (grandparent)
			{
				UCanvasPanelSlot* grandparentSlot = Cast<UCanvasPanelSlot>(grandparent->Slot);

				if (grandparentSlot)
				{
					//This gets us to the first slot of the subscrollbox
					offsetFromTop += grandparentSlot->GetOffsets().Top + grandparent->GetChildIndex(subScrollbox) * 40;

					//Now add the offset for the position in the sub scrollbox
					if (subScrollbox)
					{
						int child_index = subScrollbox->GetScrollBox()->GetChildIndex(newFocus);
						offsetFromTop += 40 * child_index;
					}

				}
				else
				{
					UWWUIFunctionLibrary::StopTimer(menuBarTimer);
					menuBarTimer = UWWUIFunctionLibrary::OnTimer(0.01f, FTimerDelegate::CreateUObject(this, &UWWUIScreenMainMenu::FocusChangedCallback));
					return;
				}
			}
			else
			{
				UWWUIFunctionLibrary::StopTimer(menuBarTimer);
				menuBarTimer = UWWUIFunctionLibrary::OnTimer(0.01f, FTimerDelegate::CreateUObject(this, &UWWUIScreenMainMenu::FocusChangedCallback));
				return;
			}
		}

		// NRL 2025: GG DJH - We dont use the selection bar anymore
		//UCanvasPanelSlot* menuBarSlot = Cast<UCanvasPanelSlot>(menuSelectionBar->Slot);
		//FMargin offset = FMargin(0, offsetFromTop - 40, defaultRightOffset, 120);
		//menuBarSlot->SetOffsets(offset);

		if (subScrollbox)
		{
			UpdateHelpText(true);
			return;
		}
	}

	UpdateHelpText();
}

void UWWUIScreenMainMenu::PlaySubmenuAnim(bool playForward, float startPos /* = 0.0f*/)
{
	if (playForward)
	{
		UWWUIFunctionLibrary::PlayAnimation(this, FName(FIRSTSUBMENUANIMATIONNAME), startPos);
		SIFUIHelpers::MenuSoundEnterSubMenu();
	}
	else
	{
		UWWUIFunctionLibrary::PlayAnimation(this, FName(FIRSTSUBMENUANIMATIONNAME),0.1f,1, EUMGSequencePlayMode::Reverse);
		SIFUIHelpers::MenuSoundMenuBack();
	}
}

//===============================================================================
//===============================================================================
// NOTE: Lots of duplicated code between this function and UWWUIScreenMainMenu::ButtonPressed, might be best to extract common code between
void UWWUIScreenMainMenu::ForceOpenMainMenuSubItem(int SubItemIndex)
{

	UWWUIListField* clickedField = nullptr;

	//Main Menu functionality
	UVerticalBox* vertBox = menuList[0];
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (vertBox && pRugbyGameInstance)
	{
		clickedField = Cast<UWWUIListField>(vertBox->GetChildAt(SubItemIndex));
		UE_LOG(LogOnlineGame, Warning, TEXT("UWWUIScreenMainMenu::OnContinueOnlinePlay set menu item"));
	}

	if (clickedField)
	{
		// If we have multiple menus on screen, clicking on a button in a deeper column (0 being deepest)
		// messes up the menuNavigationStack, If in shallower column and clicked deeper column we don't care about navigation
		// just back out.
		
		UPanelWidget* clickedFieldParent = clickedField->GetParent();
		UPanelWidget* menuContainer = Cast<UPanelWidget>(FindChildWidget(WWUIScreenMainMenu_UI::MainMenu));
		UPanelWidget* subMenuContainer = Cast<UPanelWidget>(FindChildWidget(WWUIScreenMainMenu_UI::SubMenuContainer));

		if (columnFocusedNumber == 1) // If we are navigating submenu
		{

			if (clickedFieldParent->GetUniqueID() == menuContainer->GetUniqueID()) {
				return;
			}
		}
		else if (columnFocusedNumber == 2) // If we are navigating second submenu
		{
			UPanelWidget* clickedFieldParent = clickedField->GetParent();
			
			// If we somehow click a main menu button before it's off screen aswell
			if (clickedFieldParent->GetUniqueID() == subMenuContainer->GetUniqueID() || clickedFieldParent->GetUniqueID() == menuContainer->GetUniqueID())  {
				return;
			}
		}

		UButton* button = Cast<UButton>(clickedField->GetButtonWidget());
		if (button)
		{
			menuNavigationStack.Add(clickedField);

			UProperty* submenuProperty = FindField<UProperty>(clickedField->GetClass(), SUBMENUPROPERTYNAME);
			UPanelWidget* Panel;

			if (submenuProperty)
			{
				Panel = *submenuProperty->ContainerPtrToValuePtr<UPanelWidget*>(clickedField);

				if (Panel != nullptr)
				{

					Panel->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
					if (pRugbyGameInstance)
					{
						TArray<UWidget*> menuChildren = Panel->GetAllChildren();

						for (int i = 0; i < menuChildren.Num(); i++)
						{
							if (UWWUIListField* listField = Cast<UWWUIListField>(Panel->GetChildAt(i)))
							{
								SetFocusToWidget(listField->GetButtonWidget(), GetMasterPlayerController());
								UE_LOG(LogOnlineGame, Warning, TEXT("UWWUIScreenMainMenu::OnContinueOnlinePlay Focus online menu"));
								break;
							}
						}
					}
					
				}

				//Play animation of menu
				switch (columnFocusedNumber)
				{
				case 0:
					PlaySubmenuAnim(true);
					break;
				case 1:
					PlaySecondSubmenuAnim(true);
					break;
				}
				columnFocusedNumber++;
				UpdateHelpText();
			}
		}
	}
}



//===============================================================================
//===============================================================================
bool UWWUIScreenMainMenu::OnContinueOnlinePlay(APlayerController* controller)
{
	UE_LOG(LogOnlineGame, Warning, TEXT("UWWUIScreenMainMenu::OnContinueOnlinePlay"));

	// this is a success-only callback as I understand it
	m_allowedToProceed = true;	// release button press actions
	// go to the online sub menu

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->SetInOnlineArea(true);
		pRugbyGameInstance->GetMatchmakingManager()->TryedSessions.Empty();

		if (pRugbyGameInstance->GetConnectionManager())
		{
			pRugbyGameInstance->GetConnectionManager()->StartCheckingConnectionImmediately();
		}
	}

	//Index 5 is the online menu
	ForceOpenMainMenuSubItem(5);

	return true;
}

//===============================================================================
//===============================================================================
bool UWWUIScreenMainMenu::OnPlaySoloMode(APlayerController* controller)
{
	UE_LOG(LogOnlineGame, Warning, TEXT("UWWUIScreenMainMenu::OnPlaySoloMode"));
#if PLATFORM_SWITCH
	SIFApplication::GetApplication()->LogoutAndStopNEXService_Switch();
#endif // PLATFORM_SWITCH
	return true;
}

//===============================================================================
//===============================================================================
void UWWUIScreenMainMenu::ShowStayInOnlineModePopup()
{
	if (SIFApplication::GetApplication()->IsPrimaryPlayerOnline())
	{
		UE_LOG(LogOnlineGame, Warning, TEXT("UWWUIScreenMainMenu::ShowStayInOnlineModePopup We are still online"));
		UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();
		TArray<FModalButtonInfo> ButtonData;

		FWWUIModalDelegate YesDelegate;
		YesDelegate.BindUObject(this, &UWWUIScreenMainMenu::OnContinueOnlinePlay);

		FWWUIModalDelegate NoDelegate;
		NoDelegate.BindUObject(this, &UWWUIScreenMainMenu::OnPlaySoloMode);

		ButtonData.Add(FModalButtonInfo(FText::FromString(UWWUITranslationManager::Translate("[ID_CONTINUE_ONLINE]")), YesDelegate));

		FWWUIModalDelegate returnDelegate;
		ButtonData.Add(FModalButtonInfo(FText::FromString(UWWUITranslationManager::Translate("[ID_PLAY_OFFLINE]")), NoDelegate));

		FString LegendString = "[ID_MAIN_MENU_HELP]";

		modalData->WarningDialogue = "[ID_DO_YOU_WISH_TO_CONTINUE_WITH_MULTIPLAYER]";
		modalData->LegendString = LegendString;
		modalData->ButtonData = ButtonData;

		modalData->CloseOnBackButton = false;
		modalData->CloseOnSelectButton = false;

		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
		if (pRugbyGameInstance)
		{
			pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
		}
	}
	else
	{
		UE_LOG(LogOnlineGame, Warning, TEXT("UWWUIScreenMainMenu::ShowStayInOnlineModePopup We are offline"));
#if PLATFORM_SWITCH
		SIFApplication::GetApplication()->LogoutAndStopNEXService_Switch();
#endif // PLATFORM_SWITCH
	}
}

void UWWUIScreenMainMenu::PlaySecondSubmenuAnim(bool playForward, float startPos /* = 0.0f*/)
{
	if (playForward)
	{
		UWWUIFunctionLibrary::PlayAnimation(this, FName(SECONDSUBMENUANIMATIONNAME), startPos);
		SIFUIHelpers::MenuSoundEnterSubMenu();
	}
	else
	{
		UWWUIFunctionLibrary::PlayAnimation(this, FName(SECONDSUBMENUANIMATIONNAME), 0.1f, 1, EUMGSequencePlayMode::Reverse);
		SIFUIHelpers::MenuSoundMenuBack();
	}
}

//Navigate back a menu
void UWWUIScreenMainMenu::GoBackMenu(APlayerController* playerController)
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (!bInputLocked)
	{
		if (menuNavigationStack.Num() > 0)
		{
			//Move focus to previous menu	
			UWWUIListField* newFocus = menuNavigationStack[menuNavigationStack.Num() - 1];
			if (m_gameInstance && newFocus)
			{
				SetFocusToWidget(newFocus->GetButtonWidget(), GetMasterPlayerController());
#if PLATFORM_WINDOWS
				newFocus->GetButtonWidget()->SetKeyboardFocus();
#endif
				menuNavigationStack.RemoveAt(menuNavigationStack.Num() - 1);
				columnFocusedNumber--;
				UpdateHelpText();

				if (newFocus->IsA<UWWUIMainMenuField>())
				{
					UWWUIMainMenuField* menuField = Cast<UWWUIMainMenuField>(newFocus);
					menuField->SubMenuPanel->SetVisibility(ESlateVisibility::Collapsed);
				}

				//Play animation of menu
				//switch (columnFocusedNumber)
				//{
				//case 1:
				//	PlaySubmenuAnim(false);
				//	break;
				//case 2:
				//	PlaySecondSubmenuAnim(false);
				//	break;
				//}
				// 
				// NRL 2025: GG DJH - We dont use the selection bar anymore
				//Move selection bar
				//for (UVerticalBox* vertBox : menuList)
				//{
				//
				//	if (vertBox && vertBox->HasUserFocusedDescendants(GetMasterPlayerController()))
				//	{
				//		for (int i = 0; i < vertBox->GetChildrenCount(); i++)
				//		{
				//			UWWUIListField* tempRef = Cast<UWWUIListField>(vertBox->GetChildAt(i));
				//			if (tempRef && tempRef->HasUserFocusedDescendants(GetMasterPlayerController()))
				//			{
				//				if (tempRef == Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::Fanhub)))
				//				{
				//#ifdef ENABLE_ANALYTICS
				//					UE_LOG(LogTemp, Warning, TEXT("======================== FanHub menu exited ========================"));
				//
				//					if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
				//					{
				//						pRugbyGameInstance->RegisterFanHubAnalyticsData();
				//					}
				//#endif
				//				}
				//
				//				if (tempRef == FindChildWidget(WWUIScreenMainMenu_UI::Fanhub))
				//				{
				//					m_gameInstance->SetInOnlineArea(false);
				//				}
				//
				//				if (tempRef == Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::PlayOnline)))
				//				{
				//					m_gameInstance->SetInOnlineArea(false);
				//
				//#if PLATFORM_SWITCH
				//					SIFApplication::GetApplication()->LogoutAndStopNEXService_Switch();
				//#endif // PLATFORM_SWITCH
				//				}
				//
				//				SIFApplication::GetApplication()->SetOnlineMode(EOnlineMode::Offline);
				//
				//				
				//				if (menuSelectionBar)
				//				{
				//					UCanvasPanelSlot* bar = Cast<UCanvasPanelSlot>(menuSelectionBar->Slot);
				//					UCanvasPanelSlot* newMenu = Cast<UCanvasPanelSlot>(vertBox->Slot);
				//					if (bar && newMenu)
				//					{
				//						barOffset = newMenu->GetPosition().y - bar->GetSize().y / SELECTIONBARSIZERATIO;
				//#if defined(DISABLE_ONLINE)		
				//					//Dealing with the missing online option. All nested options exist below Online
				//					if ((columnFocusedNumber == 0 && i > 5) || (columnFocusedNumber == 1))
				//					{
				//						barOffset -= 40;
				//					}
				//#endif							
				//							bar->SetPosition(FVector2D(0, barOffset + i * bar->GetSize().y / SELECTIONBARSIZERATIO));
				//						}
				//					}
				//				}
				//			}
				//		}
				//	}
			}
		}
	}
}

//Navigate back a menu
void UWWUIScreenMainMenu::GoBackMenuWithoutAnimation(APlayerController* playerController)
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (!bInputLocked) // If we are not blocked
	{
		if (menuNavigationStack.Num() > 0)
		{
			//Move focus to previous menu	
			UButton* newFocus = menuNavigationStack[menuNavigationStack.Num() - 1]->GetButtonWidget();
			if (m_gameInstance && newFocus)
			{
				SetFocusToWidget(newFocus, GetMasterPlayerController());
#if PLATFORM_WINDOWS
				newFocus->SetKeyboardFocus();
#endif
				menuNavigationStack.RemoveAt(menuNavigationStack.Num() - 1);
				columnFocusedNumber--;
				UpdateHelpText();

				//Play animation of menu
				//switch (columnFocusedNumber)
				//{
				//case 1:
				//	PlaySubmenuAnim(false, 1.0f);
				//	break;
				//case 2:
				//	PlaySecondSubmenuAnim(false, 1.0f);
				//	break;
				//}

				// NRL 2025: GG DJH - We don't use the selection bar anymore
				//Move selection bar
				//for (UVerticalBox* vertBox : menuList)
				//{
				//
				//	if (vertBox && vertBox->HasUserFocusedDescendants(GetMasterPlayerController()))
				//	{
				//		for (int i = 0; i < vertBox->GetChildrenCount(); i++)
				//		{
				//			UWWUIListField* tempRef = Cast<UWWUIListField>(vertBox->GetChildAt(i));
				//			if (tempRef && tempRef->HasUserFocusedDescendants(GetMasterPlayerController()))
				//			{
				//				if (tempRef == Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::Fanhub)))
				//				{
				//#ifdef ENABLE_ANALYTICS
				//					UE_LOG(LogTemp, Warning, TEXT("======================== FanHub menu exited ========================"));
				//
				//					if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
				//					{
				//						pRugbyGameInstance->RegisterFanHubAnalyticsData();
				//					}
				//#endif
				//				}
				//
				//				if (tempRef == FindChildWidget(WWUIScreenMainMenu_UI::Fanhub))
				//				{
				//					m_gameInstance->SetInOnlineArea(false);
				//				}
				//
				//				if (tempRef == Cast<UWWUIListField>(FindChildWidget(WWUIScreenMainMenu_UI::PlayOnline)))
				//				{
				//					m_gameInstance->SetInOnlineArea(false);
				//				}
				//
				//				
				//				//if (menuSelectionBar)
				//				//{
				//				//	UCanvasPanelSlot* bar = Cast<UCanvasPanelSlot>(menuSelectionBar->Slot);
				//				//	UCanvasPanelSlot* newMenu = Cast<UCanvasPanelSlot>(vertBox->Slot);
				//				//	if (bar && newMenu)
				//				//	{
				//				//		barOffset = newMenu->GetPosition().y - bar->GetSize().y / SELECTIONBARSIZERATIO;
				//#if defined(DISABLE_ONLINE)
				//				//		//Dealing with the missing online option. All nested options exist below Online
				//				//		if ((columnFocusedNumber == 0 && i > 5) || (columnFocusedNumber == 1))
				//				//		{
				//				//			barOffset -= 40;
				//				//		}
				//#endif		//
				//				//		bar->SetPosition(FVector2D(0, barOffset + i * bar->GetSize().y / SELECTIONBARSIZERATIO));
				//				//	}
				//				//}
				//			}
				//		}
				//
				//	}
				//}
			}
		}
	}
}


//Added to bind to buttons that need to play the accept sound effect when clicked
void UWWUIScreenMainMenu::PlayAcceptSoundEffect()
{
	if (GetInputEnabled())
	{
		SIFUIHelpers::MenuSoundMenuAccept();
	}
}

//Functionality for moving through sub menus.
// NOTE: Lots of duplicated code between this function and UWWUIScreenMainMenu::ForceOpenMainMenuSubItem, might be best to extract common code between
#ifdef UI_USING_UMG
void UWWUIScreenMainMenu::ButtonPressed()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();


	//#if PLATFORM_WINDOWS
#ifdef ENABLE_STEAMCHECK
	int rand1 = FMath::Rand();
	int rand2 = FMath::Rand();

	if (MemCheck::instance().GetTheBallRolling(rand1, rand2) == ~(rand1 + rand2))
#endif
	{

		if (!m_allowedToProceed)
		{
			//Need to reset this or we cant ever open anothe rmenu if we fail to open the online menu.
			m_allowedToProceed = true;
			return;
		}

		if (GetInputEnabled())
		{
			UWWUIListField* clickedField = nullptr;
			//Main Menu functionality
			for (UVerticalBox* vertBox : menuList)
			{
				if (vertBox && m_gameInstance)
				{
					if (vertBox->HasUserFocusedDescendants(GetMasterPlayerController()))
					{
						for (int i = 0; i < vertBox->GetChildrenCount(); i++)
						{
							if (vertBox->GetChildAt(i)->HasUserFocusedDescendants(GetMasterPlayerController()))
							{
								clickedField = Cast<UWWUIListField>(vertBox->GetChildAt(i));
							}
						}
					}
				}
			}

			if (clickedField)
			{

				// If we have multiple menus on screen, clicking on a button in a deeper column (0 being deepest)
				// messes up the menuNavigationStack, If in shallower column and clicked deeper column we don't care about navigation
				// just back out.

				UPanelWidget* clickedFieldParent = clickedField->GetParent();
				UPanelWidget* menuContainer = Cast<UPanelWidget>(FindChildWidget(WWUIScreenMainMenu_UI::MainMenu));
				UPanelWidget* subMenuContainer = Cast<UPanelWidget>(FindChildWidget(WWUIScreenMainMenu_UI::SubMenuContainer));

				if (columnFocusedNumber == 1) // If we are navigating submenu
				{

					if (clickedFieldParent->GetUniqueID() == menuContainer->GetUniqueID()) {
						return;
					}
				}
				else if (columnFocusedNumber == 2) // If we are navigating second submenu
				{
					UPanelWidget* clickedFieldParent = clickedField->GetParent();

					// If we somehow click a main menu button before it's off screen aswell
					if (clickedFieldParent->GetUniqueID() == subMenuContainer->GetUniqueID() || clickedFieldParent->GetUniqueID() == menuContainer->GetUniqueID()) {
						return;
					}
				}

				UButton* button = Cast<UButton>(clickedField->GetButtonWidget());
				if (button)
				{
					menuNavigationStack.Add(clickedField);
					UProperty* submenuProperty = FindField<UProperty>(clickedField->GetClass(), SUBMENUPROPERTYNAME);
					UPanelWidget* Panel;

					if (submenuProperty)
					{
						Panel = *submenuProperty->ContainerPtrToValuePtr<UPanelWidget*>(clickedField);

						if (Panel != nullptr)
						{
							Panel->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
							if (m_gameInstance)
							{
								TArray<UWidget*> menuChildren = Panel->GetAllChildren();

								for (int i = 0; i < menuChildren.Num(); i++)
								{
									if (UWWUIListField* listField = Cast<UWWUIListField>(Panel->GetChildAt(i)))
									{
										SetFocusToWidget(listField->GetButtonWidget(), GetMasterPlayerController());
										break;
									}
								}

							}
						}
						//Play animation of menu
						//switch (columnFocusedNumber)
						//{
						//case 0:
						//	PlaySubmenuAnim(true);
						//	break;
						//case 1:
						//	PlaySecondSubmenuAnim(true);
						//	break;
						//}
						columnFocusedNumber++;
						UpdateHelpText();
					}
				}
			}
		}
	}
}
#endif

void UWWUIScreenMainMenu::OnControlsClicked()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (GetInputEnabled())
	{
		if (m_gameInstance)
		{
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::Controls);
		}
	}
}

void UWWUIScreenMainMenu::GraphicsOptionOnClick()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (GetInputEnabled())
	{
		if (m_gameInstance)
		{
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::GraphicsSettings);
		}
	}
}

void UWWUIScreenMainMenu::OnGameplayOptionsClicked()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (GetInputEnabled())
	{
		if (m_gameInstance)
		{
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::MatchRulingsSettings);
		}

	}
}

void UWWUIScreenMainMenu::OnSoundClicked()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (GetInputEnabled())
	{
		if (m_gameInstance)
		{
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::SoundSettings);
		}
	}
}

void UWWUIScreenMainMenu::OnCameraSettingsClicked()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (GetInputEnabled())
	{
		if (m_gameInstance)
		{
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::CameraAndVisualsSettings);
		}
	}
}

void UWWUIScreenMainMenu::OnCreditsClicked()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (GetInputEnabled())
	{
		if (m_gameInstance)
		{
#if UPLOAD_FANHUB_DATA_ON_CREDITS
			UWWRugbyFanHubService* FanHubService = Cast<UWWRugbyFanHubService>(m_gameInstance->GetFanHubService());
			if (FanHubService)
			{
				FanHubService->StartPlayerChampionDataUpload();
			}
#else
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::Credits);
#endif
		}
	}
}

void UWWUIScreenMainMenu::OnFanHubClicked()
{

}

void UWWUIScreenMainMenu::OnOnlineClicked()
{
#ifdef NOT_REQD
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (GetInputEnabled())
	{
		if (UWorld* world = GetWorld())
		{
			if (URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(world->GetGameInstance<URugbyGameInstance>()))
			{

			}
		}
	}
#endif
}

//////////////////////////TRANSLATION FROM LUA START///////////////////////////



//Timer functions to replace the OnTimeout function in lua, which takes a string argument with the timer.
//Instead, now each timer is set calling the specific timeout function they use.

void UWWUIScreenMainMenu::TimerBallRolling()
{
	/*
	int temp = SIFGameHelpers::GAGetTheBallRolling(4, 3);

	if temp ~= 7 then
		ProceedToWindow("TitleScreen");
	end

		UISetTimer("BallRolling", UIGetNode("RootMenuWindow/MainMenu"), 1.0);*/
}

void  UWWUIScreenMainMenu::TimerSignIn()
{
	const char* default_profile_name = SIFGameHelpers::GAGetDefaultProfileName();

#if PLATFORM_XBOXONE && defined(GAMECENTRE_ENABLED)
	bool online_enabled = SIFGameHelpers::GAIsSystemOnline();
	bool is_guest = SIFMatchmakingHelpers::IsUserGuest(SIFUIHelpers::GetCurrentMasterControllerIndex());
	if (online_enabled && !is_guest)
	{
		SIFGameHelpers::GAAsyncProfileSignIn();
	}

	if (SIFGameHelpers::GAIsPlatformXboxOne())
	{
		SIFMatchmakingHelpers::ValidUserSignin();
	}
#endif
	UpdateHelpText();
}


void  UWWUIScreenMainMenu::TimerLaunchServerUnavailable()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	//Launch a popup showing the server is unavailable
	if (m_gameInstance)
	{
		UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

		modalData->WarningDialogue = FString(UWWUITranslationManager::Translate("[ID_WW_SERVER_UNAVAILABLE]"));
		modalData->LegendString = FString("[ID_INGAME_HALFTIME_HELP]");
		modalData->CloseOnBackButton = true;
		modalData->CloseOnSelectButton = true;

		TArray<FModalButtonInfo> ButtonData;

		m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
	}
}

void  UWWUIScreenMainMenu::TimerEnableLoad()
{
	bCanLoad = true;
}

void  UWWUIScreenMainMenu::TimerCompetitionLoad()
{
	loading_spokes_node->SetVisibility(ESlateVisibility::Collapsed);
	CompetitionLoadTimerFinished();
}

void  UWWUIScreenMainMenu::TimerCoachLoad()
{
	loading_spokes_node->SetVisibility(ESlateVisibility::Collapsed);
	//CoachLoadTimerFinished();
}

void  UWWUIScreenMainMenu::TimerProLoad()
{
	loading_spokes_node->SetVisibility(ESlateVisibility::Collapsed);
	//ProLoadTimerFinished();
}

void  UWWUIScreenMainMenu::TimerCompNew()
{
	loading_spokes_node->SetVisibility(ESlateVisibility::Collapsed);
	//CompNewTimerFinished();
}

void  UWWUIScreenMainMenu::TimerCoachNew()
{
	loading_spokes_node->SetVisibility(ESlateVisibility::Collapsed);
	//CoachNewTimerFinished();
}

void  UWWUIScreenMainMenu::TimerProNew()
{
	loading_spokes_node->SetVisibility(ESlateVisibility::Collapsed);
	//ProModeNewTimerFinished();
}

//End Timer functions

void UWWUIScreenMainMenu::FadeInCallback()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		m_gameInstance->RequestTransitionHoldFinish(0.5f);

		SIFGameWorld* pGameWorld = m_gameInstance->GetActiveGameWorld();

		if (pGameWorld)
		{
			RUGameEvents* pGameEvents = pGameWorld->GetEvents();

			if (pGameEvents)
			{
				pGameEvents->cutscene_start.Remove(this, &UWWUIScreenMainMenu::FadeInCallback);
			}
		}
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenMainMenu::CheckChampionDataVersion()
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (pRugbyGameInstance)
	{
		//We can just ignore this if we have an invite, we dont use the live data in online mode anyway.
		if (pRugbyGameInstance->GetInviteManager() && pRugbyGameInstance->GetInviteManager()->HasActiveInvite())
		{
			return;
		}

		UConnectionManager* pConnectionManager = pRugbyGameInstance ? pRugbyGameInstance->GetConnectionManager() : nullptr;

		if (pConnectionManager && (pConnectionManager->GetConnectionStatus() == EOnlineServerConnectionStatus::Normal || pConnectionManager->GetConnectionStatus() == EOnlineServerConnectionStatus::Connected))
		{
			// Champion data version check
			UWWFanhubHttpService* pFanhubService = pRugbyGameInstance->GetFanHubService();
			if (pFanhubService)
			{
				pRugbyGameInstance->GetConnectionManager()->NetworkConnected.Remove(CheckLiveDataVersionHandle);
				FwwHttpRequestComplete OnVersionCheckCompleteDelegate = FwwHttpRequestComplete::CreateUObject(this, &UWWUIScreenMainMenu::ChampionDataCheckComplete);
				pFanhubService->GetChampionDataVersion(OnVersionCheckCompleteDelegate);
			}
		}
	}
}

void UWWUIScreenMainMenu::ChampionDataCheckComplete(FString ResponseBody, bool WasSuccessful)
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		// We have been signed out or something, give up immediately!
		if (pRugbyGameInstance->FunhubShouldDropTasks || pRugbyGameInstance->bProfileHasBeenSwitched)
		{
			return;
		}
	}

	if (WasSuccessful)
	{
		UWWFanhubHttpService* pFanhubService = pRugbyGameInstance->GetFanHubService();

		if (pFanhubService)
		{
			TSharedPtr<FJsonObject> pJsonObject = pFanhubService->GetJsonObjFromString(ResponseBody);

			ChampionDataResponseVersion = -1;
			// Get the local version from save data.
			int64 LocalVersion = 0;

			SIFNamedValueListWrapper* pNVL = SIFGameHelpers::GAGetProfileNVL("");

			if (pNVL)
			{
				LocalVersion = pNVL->GetValueInt(PLAYER_PROFILE_CHAMPION_DATA_VERSION);
			}

			if (pJsonObject)
			{
				if (pJsonObject->TryGetNumberField("version", ChampionDataResponseVersion))
				{
					// Toggle legend off and on
					if (ChampionDataResponseVersion > LocalVersion)
					{
#if SHOW_CHAMPION_DATA_DIALOGUE_ON_MENU_OPEN
						pRugbyGameInstance->AttemptShowChampionDataDownloadDialogue(false, ChampionDataResponseVersion, FSimpleDelegate::CreateUObject(this, &UWWUIScreenMainMenu::OnLiveDataDownloadSuccess));
#endif
				}
			}
		}

			// Holds strings we will use in the UI.
			FString UpdateAvailableTitleString = "[ID_LIVE_DATA_UNAVAILABLE]";

			// Holds the time returned by the server.
			FString UpdateTimeString;

			// Get the container for the time.
			UWidget* pLiveDataHorizontalBox = FindChildWidget(WWUIScreenMainMenu_UI::LiveDataHorizontalBoxUpdateTime);
			if (pJsonObject)
			{
				if (pJsonObject->TryGetStringField("updated_at", UpdateTimeString))
				{
					FString LatestUpdateTimeString = UpdateTimeString.LeftChop(9);

					if (LocalVersion >= ChampionDataResponseVersion)
					{
						UpdateAvailableTitleString = "[ID_LIVE_DATA_DATA_UP_TO_DATE_TEXT]";
					}
					else
					{
						UpdateAvailableTitleString = "[ID_LIVE_DATA_UPDATE_AVAILABLE]";
					}

					SetWidgetText(FindChildWidget(WWUIScreenMainMenu_UI::LiveDataLastUpdateTimeText), FText::FromString(LatestUpdateTimeString));

					// Show the container for the time.
					if (pLiveDataHorizontalBox)
					{
						pLiveDataHorizontalBox->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
					}
				}
				else
				{
					// Hide the container with the time.
					if (pLiveDataHorizontalBox)
					{
						pLiveDataHorizontalBox->SetVisibility(ESlateVisibility::Collapsed);
					}
				}
			}
			else
			{
				// Hide the container with the time.
				if (pLiveDataHorizontalBox)
				{
					pLiveDataHorizontalBox->SetVisibility(ESlateVisibility::Collapsed);
				}
			}

			// Update the widget with the correct title.
			SetWidgetText(FindChildWidget(WWUIScreenMainMenu_UI::LiveDataUpdateAvailableTitle), FText::FromString(UWWUITranslationManager::Translate(UpdateAvailableTitleString)));
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenMainMenu::StartChampionDataDownload()
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->AttemptShowChampionDataDownloadDialogue(true, ChampionDataResponseVersion, FSimpleDelegate::CreateUObject(this, &UWWUIScreenMainMenu::OnLiveDataDownloadSuccess));
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenMainMenu::OnLiveDataDownloadSuccess()
{
	FString UpdateAvailableTitleString = "[ID_LIVE_DATA_DATA_UP_TO_DATE_TEXT]";
	// Update the widget which contains the updated time text.
	SetWidgetText(FindChildWidget(WWUIScreenMainMenu_UI::LiveDataUpdateAvailableTitle), FText::FromString(UWWUITranslationManager::Translate(UpdateAvailableTitleString)));
}

//===============================================================================
//===============================================================================
APlayerController* UWWUIScreenMainMenu::GetMasterPlayerController()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		APlayerController* controller = m_gameInstance->GetMasterPlayerController();

		UWidget* widget = GetUserFocusedWidget(controller);

		if (controller == nullptr || !widget || !widget->IsVisible())
		{
			UWWUIFunctionLibrary::StopTimer(regainControllerFocus);
			regainControllerFocus = UWWUIFunctionLibrary::OnTimer(1.0f, FTimerDelegate::CreateUObject(this, &UWWUIScreenMainMenu::RegainControllerFocus));
		}

		return controller;
	}

	return nullptr;
}

//===============================================================================
//===============================================================================
void UWWUIScreenMainMenu::RegainControllerFocus()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		APlayerController* controller = m_gameInstance->GetMasterPlayerController();

		UWidget* widget = GetUserFocusedWidget(controller);

		if (widget && widget->IsVisible())
		{
			return;
		}

		if (controller == nullptr)
		{
			UWWUIFunctionLibrary::StopTimer(regainControllerFocus);
			regainControllerFocus = UWWUIFunctionLibrary::OnTimer(1.0f, FTimerDelegate::CreateUObject(this, &UWWUIScreenMainMenu::RegainControllerFocus));
		}
		else
		{
			UVerticalBox* mainMenu = Cast<UVerticalBox>(FindChildWidget(WWUIScreenMainMenu_UI::MainMenu));

			if (mainMenu)
			{
				if (m_gameInstance)
				{
					UWidget* child = mainMenu->HasAnyChildren() ? mainMenu->GetChildAt(0) : nullptr;
					UWWUIListField* listField = child != nullptr ? Cast<UWWUIListField>(child) : nullptr;
					if (listField)
					{
						SetFocusToWidget(listField->GetButtonWidget(), GetMasterPlayerController());
						GoBackMenuWithoutAnimation(controller);
						GoBackMenuWithoutAnimation(controller);
					}
				}
			}
			else
			{
				return;
			}
		}
	}
}

#ifdef NOT_REQD
void UWWUIScreenMainMenu::DebugStartOnlineMatchmaking()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		//////////////////////////////////////////////////////////////////////////
		// #dewald
		// Since we're missing a bunch of logic to pick controllers and set the type of online, we'll just hack it in here for now.
		//////////////////////////////////////////////////////////////////////////
		bool LanGame = false;
#ifdef WITH_EDITOR
		LanGame = FParse::Param(FCommandLine::Get(), TEXT("LanGame"));
#endif
		EOnlineMode onlineMode = LanGame ? EOnlineMode::LAN : EOnlineMode::Online;
		m_gameInstance->SetOnlineMode(onlineMode);

		// Set up our connected console groups
		m_gameInstance->ClearConnectedConsoles();
		m_gameInstance->AddPlayer(m_gameInstance->GetFirstLocalRugbyPlayerController());

		m_gameInstance->SetMatchmakingMode(EMatchmakingMode::Unranked);
		m_gameInstance->GetMatchmakingManager()->SetLobbyPrivacy(EPrivacyLevel::Public);

		m_gameInstance->BeginMatchMaking();

		//////////////////////////////////////////////////////////////////////////
		// #dewald ^^^^^^^^^^^^^
		//////////////////////////////////////////////////////////////////////////
	}
}

#endif
//===============================================================================
//===============================================================================
#if wwDEBUG_SRA
void UWWUIScreenMainMenu::TestLeft(APlayerController* playerController)
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance && m_gameInstance->GetStatsScorer())
	{
		//m_gameInstance->GetStatsScorer()->OnGameLoaded();
		m_gameInstance->GetStatsScorer()->OnGameStart();
		m_gameInstance->GetStatsScorer()->OnGameEnd();

	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenMainMenu::TestRight(APlayerController* playerController)
{
	OnlineLeaderboardsOnClick();
}
#endif

//===============================================================================
//===============================================================================
void UWWUIScreenMainMenu::AdjustMenuForOnline()
{
#if defined(DISABLE_ONLINE)
	if (UWidget* onlineButton = FindChildWidget(WWUIScreenMainMenu_UI::PlayOnline))
	{
		onlineButton->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (UWidget* FanhubSpacer = FindChildWidget(WWUIScreenMainMenu_UI::FanhubSpacer))
	{
		FanhubSpacer->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (UWidget* FanhubPlayersSpacer = FindChildWidget(WWUIScreenMainMenu_UI::FanhubPlayersSpacer))
	{
		FanhubPlayersSpacer->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (UWidget* FanhubTeamsSpacer = FindChildWidget(WWUIScreenMainMenu_UI::FanhubTeamsSpacer))
	{
		FanhubTeamsSpacer->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (UWidget* CustomiseSpacer = FindChildWidget(WWUIScreenMainMenu_UI::CustomiseSpacer))
	{
		CustomiseSpacer->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (UWidget* OptionsSpacer = FindChildWidget(WWUIScreenMainMenu_UI::OptionsSpacer))
	{
		OptionsSpacer->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (UWidget* CustomisePlayersSpacer = FindChildWidget(WWUIScreenMainMenu_UI::CustomisePlayersSpacer))
	{
		CustomisePlayersSpacer->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (UWidget* CustomiseTeamsSpacer = FindChildWidget(WWUIScreenMainMenu_UI::CustomiseTeamsSpacer))
	{
		CustomiseTeamsSpacer->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (UWidget* CustomiseCompetitionsSpacer = FindChildWidget(WWUIScreenMainMenu_UI::CustomiseCompetitionsSpacer))
	{
		CustomiseCompetitionsSpacer->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (UWidget* CustomiseKitsSpacer = FindChildWidget(WWUIScreenMainMenu_UI::CustomiseKitsSpacer))
	{
		CustomiseKitsSpacer->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (UWidget* DisplaySpacer = FindChildWidget(WWUIScreenMainMenu_UI::DisplaySpacer))
	{
		DisplaySpacer->SetVisibility(ESlateVisibility::Collapsed);
	}
#endif
}

//===============================================================================
//===============================================================================
void UWWUIScreenMainMenu::OnInFocus()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	static IConsoleVariable* CVarShowWomenInCutscenes = IConsoleManager::Get().FindConsoleVariable(TEXT("ww.rugby.ShowWomenInCutscenes"));
	if (CVarShowWomenInCutscenes)
	{
		CVarShowWomenInCutscenes->Set((int)0);
	}

	Super::OnInFocus();
	if (m_gameInstance)
	{
		UConnectionManager* pConnectionManager = m_gameInstance->GetConnectionManager();

		if (pConnectionManager)
		{
			if (pConnectionManager->GetCurrentState() != ERugbyNetworkState::Offline)
			{
				ensureAlwaysMsgf(pConnectionManager->GetCurrentState() == ERugbyNetworkState::Offline, TEXT("We have returned to main menu, but our current connection state is not offline! There is a path that has returned us to main menu without properly setting this state! Speak to Joseph or Justin. Current state is %s"), *pConnectionManager->GetCurrentStateAsString());
				pConnectionManager->GotoState(ERugbyNetworkState::Offline);
			}
		}

		m_gameInstance->SetReadyForInvites(true);

		//m_gameInstance->SetOnlineMode(EOnlineMode::Offline); This shouldnt be needed any more as changign to offline connection state will flip this for us

		PlayGoCheck();

		if (m_gameInstance->GetActiveGameWorld() && m_gameInstance->GetActiveGameWorld()->GetEvents())
			m_gameInstance->GetActiveGameWorld()->GetEvents()->cutscene_start.Add(this, &UWWUIScreenMainMenu::FadeInCallback);

		m_gameInstance->ToggleMainMenuLighting(false);

		bInputLocked = false;

		loading_spokes_node = FindChildWidget(WWUIScreenMainMenu_UI::LoadingSpokes);
		if (loading_spokes_node)
		{
			loading_spokes_node->SetVisibility(ESlateVisibility::Collapsed);
		}

		active_user = Cast<UWidget>(FindChildWidget(WWUIScreenMainMenu_UI::SignInUserOverlay));

		if (SIFGameHelpers::GAIsPlatformPC())
		{
			UWWUIFunctionLibrary::OnTimer(1.0, FTimerDelegate::CreateUObject(this, &UWWUIScreenMainMenu::TimerBallRolling));
		}

		//Make sure we're using the custom database.
		bool okay_to_enter_menu = SIFGameHelpers::GACustomDatabaseIsActive();

		//Set the game mode to normal rugby.Mainly so if someone goes into a competition then returns to a single
		//match they won't accidentally be playing a sevens game

		if (SIFGameHelpers::GAIsAppBuildRC3())
		{
			SIFGameHelpers::GASetGameMode(GAME_MODE::GAME_MODE_RU13);

			SIFGameHelpers::GASetIsAProMode(false);

			//Sets game mode on other screens?
			/* #MB - find how to implement this/if its needed
			CustomisePlayerCommon.ProMode = false;
			CareerHUB.is_sevens = false;
			*/
			SIFGameHelpers::GASetGameLaw(GAME_LAW::GAME_LAW_NORMAL);
			SIFGameHelpers::GASetTrainingOffscreenMarkersToLocal(true);
			SIFGameHelpers::GASetRUHUDUpdaterIsTraining(false);
		}

		SIFGameHelpers::GASetSandboxTickEnabled(true);


		//AssertMessage(okay_to_enter_menu, "CustomDatabaseLoad not called before entering the main menu, if the correct DB is not in place before enting this menu you will have isssues")
		//But Just in case, this should try and recover gracefully
		/*if (!SIFGameHelpers::GACustomDatabaseIsActive())
		{
			SIFGameHelpers::GACustomDatabaseLoad();
		}*/

		if (!SIFGameHelpers::GAIsPlatformXboxOne())
		{
			bIsSignedIn = SIFGameHelpers::GAGetFanHubActive();
		}

		if (bResetMenuOnEntry)
		{
			//DefaultMainMenuSelection(ui_object, parameters);

			bResetMenuOnEntry = false;
			if (bIsSignedIn)
			{
				if (SIFGameHelpers::GAIsPlatformPC() || SIFGameHelpers::GAIsPlatformXboxOne())
				{
					signInTimer = UWWUIFunctionLibrary::OnTimer(1.0, FTimerDelegate::CreateUObject(this, &UWWUIScreenMainMenu::TimerSignIn));
				}
			}
		}


		if (bResetSelectedNode)
		{
			if (m_gameInstance)
			{
				SetFocusToWidget(FindChildWidget(WWUIScreenMainMenu_UI::SingleMatch), GetMasterPlayerController());
				//parameters.selected_node = "RootMenuWindow/MainMenu/RUMenuTemplate/MenuOptions/SingleMatch";
				//MenuOnSelectionChanged(UIGetNode("RootMenuWindow/MainMenu/RUMenuTemplate/MenuOptions"), parameters);
				bResetSelectedNode = false;
			}
		}

		bReversionInProgress = false;

		//Make sure the tournament manager is not active
		SIFGameHelpers::GACompetitionSetCompetitionInActive();
		SIFGameHelpers::GACareerSetCareerInActive();

		//Reset game settings, just in case
		SIFGameHelpers::GAResetGameSettings();

		SIFGameHelpers::GASetTeamFaceGenerationEnabled(false);		//#MB -Stop stuttering, (will remove when more asynchronous!)

		//Set the next window of training field to not be game window
		//ONLINE_GAME_NODE = nil;
		ONLINE_ACCEPT_INVITE = false;
		//SIFGameHelpers::GASetGameType("normal"); This does nothign but call things at times i dont want it too
		//SIFRichPresenceHelpers::RPSetPresenceMenus();
		CheckAndSetDefaults();

		if (SIFGameHelpers::GAIsPlatformPC())
		{
			//disable the mcl and set all controllers to active
			if (USIFMissingControllerListener* mcl = m_gameInstance->GetMissingControllerListener())
			{
				mcl->SetActive(false);
				mcl->SetNoControllersRelevant();
			}
			SIFUIHelpers::ListenToAllControllers();
		}


		if (SIFGameHelpers::GAIsConsole())
		{
			//Get current master controller
			int master_controller = SIFUIHelpers::GetCurrentMasterControllerIndex();

			if (master_controller == -1)
			{
				master_controller = 0;
				SIFApplication::GetApplication()->SetMasterControllerId(0);
			}

			//Tell the MCL to listen to the master controller only
			if (USIFMissingControllerListener* mcl = m_gameInstance->GetMissingControllerListener())
			{
				mcl->SetActive(true);
				mcl->SetControllerRelevant(master_controller, true);

				if (!mcl->IsControllerConnected(master_controller))
				{
					mcl->OnControllerDisconnected(master_controller);
				}
			}
			//Tell the UI to listen to the master controller only
			//SIFUIHelpers::ListenToNoControllers();
			//SIFUIHelpers::ListenToController(master_controller, true);
			SIFUIHelpers::ListenToAllControllers();
		}

		//UISetTooltipStyle("Standard", "Standard");

		//Certain things on the list box need to be locked or unlocked.
		//UIUpdateUnlockablesState();

		//#rc3_legacy_xboxone
#if PLATFORM_XBOXONE
		if (SIFGameHelpers::GAIsPlatformXboxOne())
		{
			if (active_user)
			{
				active_user->SetVisibility(ESlateVisibility::Visible);

				URichTextBlock* gamer_tag = Cast<URichTextBlock>(FindChildOfTemplateWidget(active_user, WWUIScreenMainMenu_UI::SwitchUser));
				FString gamer_tag_text = SIFGameHelpers::GAGetActiveUserName();
				FString textString = UWWUITranslationManager::Translate("[ID_SWITCH_USER]"); // "[A] PLAY AS %s"[PLT_GetString(START_PRESS_START)]
				textString = FString::Printf(*textString, *gamer_tag_text);
				SetWidgetText(gamer_tag, FText::FromString(textString));
			}
		}
		else
#endif
		{
			if (active_user)
			{
				active_user->SetVisibility(ESlateVisibility::Collapsed);
			}
		}

		SIFGameHelpers::GAResumeSandboxGame();

		//#rc3_legacy_xboxone #rc3_legacy_ps4
		//if we're coming from the title screen, turn off the loading overlay
		if (bStart == true && (SIFGameHelpers::GAIsPlatformXboxOne() || SIFGameHelpers::GAIsPlatformPS4() || SIFGameHelpers::GAIsPlatformSwitch()))
		{
			//#MB - hide the saving overlay
			if (!bCheckAndSettingDefaults || !SIFGameHelpers::GAIsPlatformPS4())
			{
				SIFUIHelpers::ShowSavingOverlay(false);
				SIFUIHelpers::ShowLoadingOverlay(false);
				bStart = false;
			}
		}

		//Reset the controller notifications
		if (USIFMissingControllerListener* mcl = m_gameInstance->GetMissingControllerListener())
		{
			mcl->ClearOldNotifications();
		}



		UpdateHelpText();

		//We came from the careerHUB, and we want to save our pro player
		RUCareerModeManager* pCareerModeManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
		if (pCareerModeManager && pCareerModeManager->GetIsProStoredForSaving())
		{
			//UILaunchPopUpByName("SavingProPlayer")
			m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::ModalSavingProPlayer);

			pCareerModeManager->SetIsProStoredForSaving(false);
		}
	}

	if (bTeamDetailsResetTeam)
	{
		ResetTeamsOnClick();
		bTeamDetailsResetTeam = false;
	}

	m_gameInstance->SetInFanhubArea(false);

	if (!m_gameInstance->bProfileHasBeenSwitched)
	{
		CheckChampionDataVersion();
	}

#if PLATFORM_SWITCH
	if (SIFApplication::GetApplication()->GetConnectionManager()->bAskToStayInOnlineMode)
	{
		SIFApplication::GetApplication()->GetConnectionManager()->bAskToStayInOnlineMode = false;
		ShowStayInOnlineModePopup();
	}
#endif


#if PLATFORM_XBOXONE
	if (SIFApplication::GetApplication()->GetGameDatabaseManager()->WasCustomDatabaseUpgraded())
	{
		SIFApplication::GetApplication()->GetGameDatabaseManager()->SaveCustomDatabase();
		SIFApplication::GetApplication()->GetGameDatabaseManager()->ClearCustomDatabaseUpgraded();
	}

#endif

#if !UE_BUILD_SHIPPING
	/*
	if (FParse::Param(FCommandLine::Get(), TEXT("LanAutoStart"))
		&& (FParse::Param(FCommandLine::Get(), TEXT("LanClient")) || FParse::Param(FCommandLine::Get(), TEXT("LanHost"))))
	{
		DebugStartOnlineMatchmaking();
	}*/
#endif
}

//#MB - not sure where this is called from.
void UWWUIScreenMainMenu::SignInSuccessful(FString ResponseBody, bool WasSuccessful)
{
	// Ye old game centre code
	//if (m_gameInstance)
	//{
	//	bIsSignedIn = SIFGameHelpers::GAGetGameCentreActive();
	//	UpdateHelpText();
	//}
	UWWUIFunctionLibrary::StopTimer(signInTimer);

	bIsSignedIn = WasSuccessful;

	if (WasSuccessful)
	{
		if (SIFGameHelpers::GAGetFanHubActive())
		{
			UpdateHelpText();
		}
	}
}

void UWWUIScreenMainMenu::CheckAndSetDefaults()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	bCheckAndSettingDefaults = true;

	SIFNamedValueListWrapper* nvl = SIFGameHelpers::GAGetProfileNVL("");
	//--Only do this if there is a valid profile nvl(ie. not content test)
	if (nvl)
	{
		bool shouldSave = false;

		//--Check if a default team has been set up
		int default_team = nvl->GetValueInt("Default_Team");
		bool no_default_team = default_team == -1;

		// Check if this team still exists
		const RUGameDatabaseManager* const game_database_mgr = m_gameInstance->GetGameDatabaseManager();

		//Clean this up with some null checks
		if (!no_default_team
			&& game_database_mgr
			&& game_database_mgr->GetRL3Database()
			&& game_database_mgr->GetRL3Database()->GetTeamCache()
			&& game_database_mgr->GetRL3Database()->GetTeamCache()->GetRowStart((unsigned short)default_team, false) == NULL)
		{
			no_default_team = true;
		}

		if (no_default_team)
		{
			SIFGameHelpers::SetDefaultTeam();
			shouldSave = true;
		}

		static IConsoleVariable* performanceModeCvar = IConsoleManager::Get().FindConsoleVariable(TEXT("ww.PerformanceMode"));
		if (performanceModeCvar)
		{
			const char* performanceVal = nvl->GetValueString(PLAYER_PROFILE_PERFORMANCE);
			int current = performanceModeCvar->GetInt();
			if (FString(performanceVal).IsEmpty())
			{
				nvl->SetValueInt(PLAYER_PROFILE_PERFORMANCE, current);
			}
			else
			{
				current = FCString::Atoi(*FString(performanceVal));
			}
			performanceModeCvar->Set(current);
		}

		//-- Now check if the default cameras have been set up
		const char* default_camera1 = nvl->GetValueString(PLAYER_PROFILE_CAMERA_ANGLE); //--string(BEHIND_MID)
		const char* default_camera2 = nvl->GetValueString(PLAYER_PROFILE_CAMERA_ANGLE_PRO); //--string(PRO_BEHIND)
		int default_ball_lock = nvl->GetValueInt("pro_camera_ball_lock"); //-- 0 is NONE

		if ((default_camera1 == nullptr || strcmp(default_camera1, "") == 0)
			|| (default_camera2 == nullptr || strcmp(default_camera2, "") == 0))
			//|| default_ball_lock == 0) //This camera property are already set when a new profile is generated, so this is no longer relevant after the c++ port.
		{
			SIFGameHelpers::SetDefaultCameras();
			shouldSave = true;
		}

		if (shouldSave)
		{
#if !PLATFORM_WINDOWS
			SIFUIHelpers::ShowSavingOverlay(true, true);
#endif
			uint32 ControllerID = SIFUIHelpers::GetCurrentMasterControllerIndex();
			SIFGameHelpers::GASaveProfile("", ControllerID);
		}
	}
}

void UWWUIScreenMainMenu::ProceedToPlayerCreator()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		UWWUIStateScreenCustomisePlayerData* inData = NewObject<UWWUIStateScreenCustomisePlayerData>();
		if (inData)
		{
			inData->CustomisePlayerCommonbSetIsCreate = true;
			inData->CustomisePlayerCommonResetModifiedFlag = true;

			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CustomisePlayer, inData);
		}
	}
}

void UWWUIScreenMainMenu::OnOutFocus(bool ShouldOutFocus)
{

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();
	Super::OnOutFocus(ShouldOutFocus);

	UWWUIFunctionLibrary::StopTimer(regainControllerFocus);

	bReversionInProgress = false;
	if (m_gameInstance)
	{
		//m_gameInstance->ToggleMainMenuLighting(true);

		UWWUIFunctionLibrary::StopTimer(signInTimer);
	}
}

void UWWUIScreenMainMenu::UpdateHelpText(bool saveSlot /*= false*/)
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		UWWUIRichTextBlockWithTranslate* helpText = Cast<UWWUIRichTextBlockWithTranslate>(FindChildWidget(WWUIScreenMainMenu_UI::LegendText));
		if (helpText)
		{
			FString localeID = "";
			if (saveSlot)
			{
				localeID = "[ID_CAREER_SELECTION_HELP]";
			}
			else if (columnFocusedNumber == 0)
			{
				if (bIsSignedIn)
				{
					localeID = "[PLT_GetString(MAIN_MENU_HELP_SIGN_OUT)]";
				}
				else
				{
					localeID = "[PLT_GetString(MAIN_MENU_HELP_SIGN_IN)]";
				}
			}
			else
			{
				if (bIsSignedIn)
				{
					localeID = "[PLT_GetString(MAIN_MENU_SUB_HELP_SIGN_OUT)]";
				}
				else
				{
					localeID = "[PLT_GetString(MAIN_MENU_SUB_HELP_SIGN_IN)]";
				}
			}

			// On Xbox we need to printf in the gamertag for the switch users legend
#ifdef PLATFORM_XBOXONE 
			/*const auto OnlineSub = IOnlineSubsystem::Get();
			if (OnlineSub)
			{
				const auto IdentityInterface = OnlineSub->GetIdentityInterface();
				if (IdentityInterface.IsValid())
				{
					const auto LoginStatus = IdentityInterface->GetLoginStatus(m_gameInstance->GetPrimaryAccountControllerIndex());
					if (LoginStatus == ELoginStatus::LoggedIn)
					{
						FString name = IdentityInterface->GetPlayerNickname(m_gameInstance->GetPrimaryAccountControllerIndex());
						//localeID = UWWUITranslationManager::Translate(localeID);
						localeID = FString::Printf(*localeID, *name);
					}
				}
			}*/
#endif

			helpText->SetText(FText::FromString(localeID));
		}
	}
}

//System event functions

bool UWWUIScreenMainMenu::OnSystemEvent(WWUINodeProperty& eventProperty)
{
	FString EventName = eventProperty.GetStringProperty("system_event");

	// Prevent the menu from refreshing the save list when loading a career or competition.
	if (m_loadingCareerComp)
	{
		return false;
	}

	if (EventName == "async_signin_finished")
	{
		AsynSigninFinished();
	}
	else if (EventName == "onallreadwritefinished")
	{
		SIFUIHelpers::ShowSavingOverlay(false);
	}
	else if (EventName == "connection_test_complete")
	{
		ConnectionTestFinished();
	}
	else if (EventName == "onfullmodeunlocked")
	{
		OnFullModeUnlocked();
	}
	else if (EventName == "on_matching_connected")
	{
		OnMatchingConnected();
	}
	else if (EventName == "on_matching_connect_failed")
	{
		OnMatchingConnectFailed();
	}
	else if (EventName == "online_game_manager_initialised")
	{
		OnlineGameManagerInitiliased();
	}
	else if (EventName == "online_game_manager_initialisation_failed")
	{
		OnlineGameManagerInitiliaseFailed();
	}
	else if (EventName == GAME_DB_SAVE_OK_NAME)
	{
		OnGameDBSaveOk();
	}
	else if (EventName == GAME_DB_DELETE_OK_NAME)
	{
		OnGameDBDeleteOk();
	}
	else if (EventName == GAME_DB_DELETE_FAIL_NAME)
	{
		OnGameDBDeleteFail();
	}
	else if (EventName == GAME_DB_LOAD_OK_NAME)
	{
		OnGameDBLoadOk();
	}
	else if (EventName == GAME_DB_COMP_DATA_EXISTENCE_CHECK_COMPLETE
		|| EventName == GAME_DB_CAREER_COACH_DATA_EXISTENCE_CHECK_COMPLETE
		|| EventName == GAME_DB_CAREER_PRO_DATA_EXISTENCE_CHECK_COMPLETE)
	{
		OnCompDBExistenceCheckComplete();
	}
	else if (EventName == "NetworkDisconnectionDetected")
	{
		OnNetworkDisconnectionDetected();
	}
	else if (EventName == "xboxoneuseradded")
	{
		XboneUserAdded();
	}
	return true;
}

void UWWUIScreenMainMenu::AsynSigninFinished()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	//#rc3_legacy_ps4
#if PLATFORM_PS4
	/*  #MB - functionality no longer exists?
	if (!SIFGeneralHelpersPS4::IsProfileConnectedToPSN())
	{
		SIFGameHelpers::GAGameCentreSignOut();
	}
	*/
#endif

	//bIsSignedIn = SIFGameHelpers::GAGetGameCentreActive();     #MB - crashes build when game centre disabled and if statement didnt compile

	/* #MB - dismiss popup
	if (UIIsPopupActive("WWConnectingToServer")) then
		UIDismissPopUpByName("WWConnectingToServer");
	end
	UISetTimerForFrameCount("DismissPopup", UIGetNode("RootMenuWindow/MainMenu"), 30);
	*/

	UpdateHelpText();

	//#rc3_legacy_xboxone #rc3_legacy_ps4
	if (SIFGameHelpers::GAIsPlatformPS4() || SIFGameHelpers::GAIsPlatformXboxOne())
	{
		if (bIsSignedIn)
		{
			ProceedToServerOption();
		}
		else
		{
			if (m_gameInstance)
			{
				UWWUIFunctionLibrary::OnTimer(2, FTimerDelegate::CreateUObject(this, &UWWUIScreenMainMenu::TimerLaunchServerUnavailable));
			}
		}
	}
}

void UWWUIScreenMainMenu::ConnectionTestFinished()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	bool server_available = true;
	FString server_available_string = "true";
	bool available = server_available; // parameters.server_available #MB - find where this is set

	//PS3 seems to dislike my bool value so I'm trying somethin different
	if (SIFGameHelpers::GAIsConsole())
	{
		if (server_available_string.Compare("true"))
		{
			available = true;
		}
		else
		{
			available = false;
		}
	}

	if (available)
	{
		ProceedToSignIn(ConnectionTestSignInFor);
	}
	else
	{
		if (m_gameInstance)
		{
			UWWUIFunctionLibrary::OnTimer(2, FTimerDelegate::CreateUObject(this, &UWWUIScreenMainMenu::TimerLaunchServerUnavailable));
		}
	}
}

void UWWUIScreenMainMenu::OnFullModeUnlocked()
{
	//DefaultFullModeUnlockedHandler();
	//UIUpdateUnlockableStates #MB - need a function to update what in the menu is unlocked.
}

void UWWUIScreenMainMenu::OnMatchingConnected()
{
	/* #MB - dismiss popup
	if UIIsPopupActive("ConnectingToOnlineService") then
		UIDismissPopUpByName("ConnectingToOnlineService")
		end;
		*/
		//ConnectToOnlineServer(MainMenu.ONLINE_GAME_NODE, parameters); #MB - online server connection
}

void UWWUIScreenMainMenu::OnMatchingConnectFailed()
{
	/* #MB - more popup stuff
	UIClearTimer(PopupConnectingToOnlineService.TIMEOUT_NAME);
	PopupConnectingToOnlineService.user_notified = true;
	if (UIIsPopupActive("ConnectingToOnlineService")) then
		UIDismissPopUpByName("ConnectingToOnlineService");
	end;
	UILaunchPopUpByName("ServerConnectFailed")
	*/
#if PLATFORM_PS4
	//SIFMatchmakingHelpers::DisconnectFromMatchingServer();
#endif
}

void UWWUIScreenMainMenu::OnlineGameManagerInitiliased()
{
	if (ONLINE_GAME_NODE != nullptr)
	{
		//MainMenu.ConnectToOnlineServer(MainMenu.ONLINE_GAME_NODE, parameters)
	}
}

void UWWUIScreenMainMenu::OnlineGameManagerInitiliaseFailed()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

		modalData->WarningDialogue = "[ID_MULTIPLAYER_SERVER_CONNECT_FAILED_BODY]";
		modalData->LegendString = "[ID_MAIN_MENU_HELP]";
		modalData->CloseOnBackButton = true;
		modalData->CloseOnSelectButton = true;

		TArray<FModalButtonInfo> ButtonData;

		m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
	}
}

void UWWUIScreenMainMenu::OnGameDBSaveOk()
{
	if (!SIFGameHelpers::GAIsPlatformXboxOne())
	{
		bReversionInProgress = false;
	}

	SIFUIHelpers::ShowSavingOverlay(false);
}

void UWWUIScreenMainMenu::OnGameDBDeleteOk()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	FString ScrollboxName = "";
	FString ListFieldName = "";

	switch (DeletingFromGameModeType)
	{
	case Competition:
	{
		ScrollboxName = WWUIScreenMainMenu_UI::CompetitionList;
		ListFieldName = WWUIScreenMainMenu_UI::NewCompetition;
	}
	break;
	case Career_Pro:
	{
		ScrollboxName = WWUIScreenMainMenu_UI::BeAProList;
		ListFieldName = WWUIScreenMainMenu_UI::NewPro;
	}
	break;
	case Career_Coach:
	{
		ScrollboxName = WWUIScreenMainMenu_UI::CareersList;
		ListFieldName = WWUIScreenMainMenu_UI::NewCareer;
	}
	break;
	default:
		ensure(false); // Delete set up has gone wrong!!
		SIFUIHelpers::ShowSavingOverlay(false);
		return;
	}

	m_gameInstance->DealMenuAction(SCREEN_CANCEL, Screens_UI::WarningWithSlot);
	SIFUIHelpers::ShowSavingOverlay(false);

	if (UWWUIScrollBox* pScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(ScrollboxName)))
	{
		pScrollbox->PopulateAndRefresh();
		if (pScrollbox->GetListLength() > DeleteSlotIndex)
		{
			pScrollbox->GetListField(DeleteSlotIndex)->GetButtonWidget()->SetUserFocus(DeletingPlayerController);
			pScrollbox->GetListField(DeleteSlotIndex)->GetButtonWidget()->SetKeyboardFocus();
		}

		else
		{
			if (UWWUIListField* tempField = Cast<UWWUIListField>(FindChildWidget(ListFieldName)))
			{
				tempField->GetButtonWidget()->SetUserFocus(DeletingPlayerController);
				tempField->GetButtonWidget()->SetKeyboardFocus();
			}
		}
	}

	// Clear deletion setup data.
	DeletingFromGameModeType = GameModeType::Invalid;
	DeleteSlotIndex = -1;
	DeletingPlayerController = nullptr;
}

void UWWUIScreenMainMenu::OnGameDBDeleteFail()
{
	ensureMsgf(false, TEXT("Save Deletion Failed"));

	// Clear deletion setup data.
	DeletingFromGameModeType = GameModeType::Invalid;
	DeleteSlotIndex = -1;
	DeletingPlayerController = nullptr;

	SIFUIHelpers::ShowSavingOverlay(false);
}

void UWWUIScreenMainMenu::OnGameDBLoadOk()
{
	//#rc3_legacy_ui
	//SIFUIHelpers::UpdateUnlockablesState(UIGetNode("RUMenuTemplate/MenuOptions"));
}


void UWWUIScreenMainMenu::OnCompDBExistenceCheckComplete()
{
	if (SIFGameHelpers::GAIsConsole())
	{
		//Populate the save files lists
		/*UIClearPopulatedObjectByPointer(MainMenu.competition_listbox_node);
		UIRefreshPopulatedObjectByPointer(MainMenu.competition_listbox_node);
		UIClearPopulatedObjectByPointer(MainMenu.career_listbox_node);
		UIRefreshPopulatedObjectByPointer(MainMenu.career_listbox_node);
		UIClearPopulatedObjectByPointer(MainMenu.pro_mode_listbox_node);
		UIRefreshPopulatedObjectByPointer(MainMenu.pro_mode_listbox_node);
		UIUpdateUnlockablesState(UIGetNode("RUMenuTemplate/MenuOptions"));

		local root_node = UIGetNode("RootMenuWindow/MainMenu");
		UINodeSetVisible(root_node, true);*/
		PopulateSaveFileLists();
	}
}

void UWWUIScreenMainMenu::OnNetworkDisconnectionDetected()
{
	NetworkDisconnectionDetected();
}

//#rc3_legacy_xboxone
void UWWUIScreenMainMenu::XboneUserAdded()
{
	/* #MB - popup
	UILaunchPopUpByName("XBSignedInAdded")
	*/
}

//Shows the "Are you sure you want to delete" popup when deleting a competition file
//Replaces CompetitionOnAction
void UWWUIScreenMainMenu::CompetitionOnDeleteButton(APlayerController* playerController)
{
	if (!GetInputEnabled()) { return; }

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	/*if (SIFGameHelpers::GAIsConsole())
	{
		if (!SIFGameHelpers::GAIsPlatformXboxOne() && !SIFGameHelpers::GAIsPlatformPS4())
		{
			return;
		}
	}*/

	UWWUIListField* listField = nullptr;
	for (TObjectIterator<UWWUIListField> Itr; Itr; ++Itr)
	{
		// Access the subclass instance with the * or -> operators.
		UWWUIListField* Component = *Itr;
		if (Component->HasFocusedDescendants())
		{
			MABASSERT(!listField);
			listField = Component;
		}
	}

	GameModeType gmType = GameModeType::QuickMatch;
	int slotID = -1;

	if (listField)
	{
		gmType = GameModeType(listField->GetIntProperty("careerMode"));
		slotID = listField->GetIntProperty("slot");
	}

	int32 slot = slotID;

	if (slot == -1 || slot == MAX_int32)
	{
		return;
	}

	/* launch a "delete competition popup" which takes as a parameter, the slot of the competition file being deleted*/
	if (m_gameInstance)
	{
		UWWUIModalDeleteFileData* modalData = NewObject<UWWUIModalDeleteFileData>();

		modalData->LegendString = "[ID_MAIN_MENU_SUB_HELP]";
		modalData->CloseOnBackButton = true;
		modalData->slotNumber = slot;

		TArray<FModalButtonInfoWithSlot> ButtonData;

		FWWUIModalDelegateWithSlot DeleteDelegate;
		FWWUIModalDelegateWithSlot DoNotDeleteDelegate;

		//delete button
		switch (gmType)
		{
		case Invalid:
		{
			return;
		}
		case GameModeType::Competition:
		{
			modalData->WarningDialogue = "[ID_DELETE_COMPETITION_POPUP_TEXT]";
			DeleteDelegate.BindUObject(this, &UWWUIScreenMainMenu::DeleteCompetitionFile);
		}
		break;
		case GameModeType::Career_Pro:
		{
			modalData->WarningDialogue = "[ID_DELETE_PRO_MODE_POPUP_TEXT]";
			DeleteDelegate.BindUObject(this, &UWWUIScreenMainMenu::DeleteProModeFile);
		}
		break;
		case GameModeType::Career_Coach:
		{
			modalData->WarningDialogue = "[ID_DELETE_CAREER_POPUP_TEXT]";
			DeleteDelegate.BindUObject(this, &UWWUIScreenMainMenu::DeleteCoachCareerFile);
		}
		break;
		case GameModeType::QuickMatch:
		default:
		{
			// this should never happen.
			ensure(false);
			modalData->WarningDialogue = "[ID_DELETE_COMPETITION_POPUP_TEXT]";
			DeleteDelegate.BindUObject(this, &UWWUIScreenMainMenu::ClosePopup);
		}
		break;
		}
		ButtonData.Add(FModalButtonInfoWithSlot(FText::FromString("[ID_TEST_POPUP_ACCEPT]"), DeleteDelegate));

		//do not delete button
		DoNotDeleteDelegate.BindUObject(this, &UWWUIScreenMainMenu::ClosePopup);
		ButtonData.Add(FModalButtonInfoWithSlot(FText::FromString("[ID_TEST_POPUP_DECLINE]"), DoNotDeleteDelegate));

		modalData->ButtonData = ButtonData;

		m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningWithSlot, modalData);
	}
}

//CompetitionOnFocus: Hides a competition button if it is empty. 
//Is set up to find a widget for the competition button called "SaveInfoListBox" which contains a text block called "CompName"
//#MB - connect to competition box
bool UWWUIScreenMainMenu::CompetitionOnFocus()
{
	UWidget* parent = FindChildWidget(FString("SaveInfoListBox"));
	if (parent)
	{
		UTextBlock* compNameNode = Cast<UTextBlock>(FindChildOfTemplateWidget(parent, "CompName"));
		//local button_node = UINodeGetChild(ui_object, "SaveInfoListBox")
		//local comp_name_node = UINodeGetChild(button_node, "Competition/CompName")

		//Set the node to being invisible if there's no text.
		if (compNameNode)
		{
			bool set_visible = !compNameNode->Text.ToString().IsEmpty();
			if (set_visible)
			{
				parent->SetVisibility(ESlateVisibility::Visible);
			}
			else
			{
				parent->SetVisibility(ESlateVisibility::Collapsed);
			}
			return true;
		}
	}
	return false;
}

//Hides competition save box when competition not focused
bool UWWUIScreenMainMenu::CompetitionOnFocusLost()
{
	UWidget* saveInfoListBox = FindChildWidget(FString("SaveInfoListBox"));
	if (saveInfoListBox)
	{
		saveInfoListBox->SetVisibility(ESlateVisibility::Collapsed);
		return true;
	}
	return false;
}

//Deletes the competition file at the given slot number
bool UWWUIScreenMainMenu::DeleteCompetitionFile(APlayerController* playerController, int slotNum)
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (playerController == nullptr)
	{
		playerController = GetMasterPlayerController();
	}

	if (slotNum >= 0 /*&& slotNum < competitionList.Num()*/)
	{
		RUCareerModeManager* careerManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
		if (careerManager)
		{
			DeletingFromGameModeType = GameModeType::Competition;
			DeleteSlotIndex = slotNum;
			DeletingPlayerController = playerController;
			SIFUIHelpers::ShowSavingOverlay(true, true);

			careerManager->DeleteCompetitionFile(slotNum);

			// repopulating list etc from system event save callbacks to allow for async saves.
			return true;
		}
	}
	//local popup_parent = UINodeGetParent(UINodeGetParent(UINodeGetParent(UINodeGetParent(UINodeGetParent(ui_object)))));
	//local slot_num_raw = UINodeGetProperty(popup_parent, "slot");
	return false;
}

/////////////////OLD CLICK FUNCTIONS///////////////////////

//Common to all matches Called in button click functions
void UWWUIScreenMainMenu::CommonMatchOptionClick()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();
	//UIMenuEffectsStartTransition(); #MB - menu effect transition

	if (SIFGameHelpers::GAIsPlatformPC())
	{
		if (GetInputEnabled())
		{
			if (m_gameInstance)
			{
				m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::SelectTeams);
			}
		}
	}

	//#rc3_legacy_platform
	if (SIFGameHelpers::GAIsConsole())
	{
		if (m_gameInstance)
		{
			if (GetInputEnabled())
			{
				SIFPlayerHelpers::PMSetPlayerHuman(0, SIFUIHelpers::GetCurrentMasterControllerIndex());
				SIFPlayerHelpers::PMSetPlayerAI(1);

				m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::SelectTeams);
			}
		}
	}
}

//	Setting the quickmatch op
void UWWUIScreenMainMenu::SetOfflineMatchOptions(bool inIs15s)
{
	//Message("MainMenu.QuickMatchOptionOnClick");
	if (inIs15s)
	{
		SIFGameHelpers::GASetGameMode(GAME_MODE::GAME_MODE_RU13);
	}
	else
	{
		SIFGameHelpers::GASetGameMode(GAME_MODE::GAME_MODE_RU13W); // Nick  WWS 7s to Womens //SEVENS);
	}

	SIFApplication::GetApplication()->SetOnlineMode(EOnlineMode::Offline);

	//AssignControllers.GameMode = "quick_match"
	//GASetGameLength(10);
	//TODO: Remove once competition is in its on flownode
	SIFGameHelpers::GASetMainMenuFlowScreen("MainMenu");
	//local assign_controller = UIGetNode("RootMenuWindow/AssignControllers");
	//UINodeSetProperty(assign_controller, "next_window", "TrainingField");*/
	SIFGameHelpers::GALoadProfileMatchSettings();
	//The quick match will have random values when you play it.
	//GARandomiseWeather()				--DUNCAN HATES RANDOM WEATHER(Disabled)

	//This will set the 7s match to 7 minutes as the above profile load will set the game to 10 minutes for both modes
	// Nick  WWS 7s to Womens //
	//if (!inIs15s)
	//{
	//	SIFGameHelpers::GASetGameLength(7);
	//}

	SIFGameHelpers::GASetSubstitutionMode(1); //default to manual
	SIFGameHelpers::GASetQuickMatchTeams();
	
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	
	if (m_gameInstance)
	{
		SIFGameWorld* pGameWorld = m_gameInstance->GetActiveGameWorld();
		if (pGameWorld)
		{
			pGameWorld->SwapSandboxTeams(SIFGameHelpers::GAGetTeam(0), SIFGameHelpers::GAGetTeam(1));
		}
	}
	
	CommonMatchOptionClick();
}

//Was the quickmatch 15s button
void UWWUIScreenMainMenu::QuickMatchOptionOnClick()
{
	if (!GetInputEnabled()) { return; }
	//#if PLATFORM_WINDOWS
#ifdef ENABLE_STEAMCHECK
	int rand1 = FMath::Rand();
	int rand2 = FMath::Rand();

	if (MemCheck::instance().GetTheBallRolling(rand1, rand2) == ~(rand1 + rand2))
#endif
	{
		SetOfflineMatchOptions(true);
	}
}

//Was the quickmatch 7s button
void UWWUIScreenMainMenu::SevensQuickMatchOptionOnClick()
{
	if (!GetInputEnabled()) { return; }

	//#if PLATFORM_WINDOWS
#ifdef ENABLE_STEAMCHECK
	int rand1 = FMath::Rand();
	int rand2 = FMath::Rand();

	if (MemCheck::instance().GetTheBallRolling(rand1, rand2) == ~(rand1 + rand2))
#endif
	{
		SetOfflineMatchOptions(false);
	}
}

/* #MB - only exists in debug now?
void UWWUIScreenMainMenu::AssetPreviewOnClick()
{
	APEnterPreview();
} */

//Was quit button
void UWWUIScreenMainMenu::QuitOptionOnClick()
{
	if (!GetInputEnabled()) { return; }

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	//FGenericPlatformMisc::RequestExit(false);
	/* #MB - none of this functionality exists anymore
	if (IsXbox360())
	{
		if (UIIsUnlocked("FMU_UNLOCKED"))
		{
			if (UIIsUnlocked("SER_NEEDS_NO_SAVE_WARNING"))
			{
				XSLaunchContinueWithoutSaveWarning(parameters.controller_id);
			}
			else
			{
				UILaunchPopUpByName("ExitAppPopup");
			}
		}
		else
		{
			ProceedToNextWindow(ui_object, parameters);
		}
	}
	*/

	if (SIFGameHelpers::GAIsPlatformPC() || SIFGameHelpers::GAIsPlatformXboxOne() || SIFGameHelpers::GAIsPlatformPS4())
	{
		if (m_gameInstance)
		{
			UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

			modalData->WarningDialogue = "[PLT_GetString(EXIT_APP_POPUP_TEXT)]";
			modalData->LegendString = "[ID_MAIN_MENU_HELP]";
			modalData->CloseOnBackButton = true;

			TArray<FModalButtonInfo> ButtonData;

			FWWUIModalDelegate SaveDelegate;
			FWWUIModalDelegate DoNotSaveDelegate;

			//save button
			SaveDelegate.BindUObject(this, &UWWUIScreenMainMenu::QuitGame);
			ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_TEST_POPUP_ACCEPT]"), SaveDelegate));

			//do not save button
			DoNotSaveDelegate.BindUObject(this, &UWWUIScreenMainMenu::ClosePopup);
			ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_TEST_POPUP_DECLINE]"), DoNotSaveDelegate));

			modalData->ButtonData = ButtonData;

			m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
		}
	}

}

bool UWWUIScreenMainMenu::QuitGame(APlayerController* playerController)
{
	FGenericPlatformMisc::RequestExit(false);
	return true;
}

bool UWWUIScreenMainMenu::ClosePopup(APlayerController* playerController)
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		m_gameInstance->DealMenuAction(SCREEN_BACK, "");
		return true;
	}
	return false;
}

bool UWWUIScreenMainMenu::ClosePopup(APlayerController* playerController, int UneededSlotNum)
{
	return ClosePopup(playerController);
}

void UWWUIScreenMainMenu::CompetitionNewOnClick()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (GetInputEnabled())
	{

		//#if PLATFORM_WINDOWS
#ifdef ENABLE_STEAMCHECK
		int rand1 = FMath::Rand();
		int rand2 = FMath::Rand();

		if (MemCheck::instance().GetTheBallRolling(rand1, rand2) == ~(rand1 + rand2))
#endif
		{
			//Check to see if we can save a new competition.
			int new_competition_slot_index = RUFranchiseHelpers::GetLowestAvailableCompetitionSaveSlot();
			if (new_competition_slot_index >= RUFranchiseHelpers::GetMaxNumberOfSavedCompetitions() || new_competition_slot_index < 0)
			{
				if (m_gameInstance)
				{
					UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

					modalData->WarningDialogue = "[PLT_GetString(MAX_NUM_COMPETITIONS_EXCEEDED_TEXT)]";
					modalData->LegendString = "[ID_MAIN_MENU_HELP]";
					modalData->CloseOnBackButton = true;

					TArray<FModalButtonInfo> ButtonData;

					FWWUIModalDelegate SaveDelegate;
					FWWUIModalDelegate DoNotSaveDelegate;

					SaveDelegate.BindLambda([&](APlayerController* playerController)
						{
							SIFUIHelpers::ListenToPrimaryPlayer();
							return true;
						});

					//save button
					ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_POPUP_OK]"), SaveDelegate));

					modalData->ButtonData = ButtonData;

					m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);

					//			TArray<ARugbyPlayerController*> localControllers = m_gameInstance->GetLocalPlayerControllers();
					//
					// 			for (int i = 0; i < localControllers.Num(); i++)
					// 			{
					// 				if (SIFGameHelpers::IsPrimaryLocalPlayerController(localControllers[i]))
					// 				{
					// 					SIFUIHelpers::ListenToController(i, false);
					// 					break;
					// 				}
					// 			}
					SIFUIHelpers::ListenToNoControllers();
				}
				return;
			}

			//UWWUIScreenCareerHUB::gameMode = GameMode::Competition;				#MB - Screen variables
			//UWWUIScreenControllerAssignment::gameMode = GameMode::Competition;
			//UWWUIScreenCareerSelectTeam::NewCompetition = true;
			//UWWUIScreenCareerSetup::CoachCareer = true;
			//UWWUIScreenCareerHUB::CoachCareer = true;

			//if IsXbox360() then
				//	MainMenu.load_object_node = ui_object;
			//MainMenu.loading_spokes_node.visible = true
				//MainMenu.new_save_slot = new_competition_slot_index
				//	UIListenToNoControllers();
			//UISetTimer("CompNew", UIGetNode("RootMenuWindow/MainMenu"), 2.0);
			//else
			RUCareerModeManager* careerModeManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
			if (careerModeManager)
			{
				careerModeManager->SetSaveSlot(new_competition_slot_index);

#ifdef ENABLE_ANALYTICS
				careerModeManager->GetCareerAnalyticsData().AnalyticsSaveSlotData = new_competition_slot_index;
#endif

				SIFGameHelpers::GACompetitionSetSaveSlotNeedsReSelecting(false);

				SIFGameHelpers::GALoadProfileMatchSettings();

				//m_gameInstance->GetCareerModeManager()->SetToursMode(false);
				SIFUIHelpers::MenuEffectsStartTransition();
				//ProceedToNextWindow(ui_object, parameters)

				UWWUIStateScreenCompetitionSelectData* InData = NewObject<UWWUIStateScreenCompetitionSelectData>();
				InData->NewCompetition = true;
				m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::CompetitionSelect, InData);

				//			TArray<ARugbyPlayerController*> localControllers = m_gameInstance->GetLocalPlayerControllers();
				//
				// 			for (int i = 0; i < localControllers.Num(); i++)
				// 			{
				// 				if (SIFGameHelpers::IsPrimaryLocalPlayerController(localControllers[i]))
				// 				{
				// 					SIFUIHelpers::ListenToController(i, false);
				// 					break;
				// 				}
				// 			}
				SIFUIHelpers::ListenToNoControllers();
			}
		}
	}
}

void UWWUIScreenMainMenu::CompNewTimerFinished()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	int master_controller = SIFUIHelpers::GetCurrentMasterControllerIndex();
	SIFUIHelpers::ListenToController(master_controller, true);

	RUCareerModeManager* careerModeManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
	if (careerModeManager)
	{
		careerModeManager->SetSaveSlot(new_save_slot);  //#MB - not sure where new_save_slot is set from
		SIFGameHelpers::GACompetitionSetSaveSlotNeedsReSelecting(false);

		SIFUIHelpers::MenuEffectsStartTransition();
		//ProceedToNextWindow(MainMenu.load_object_node, parameters)
	}
}


void UWWUIScreenMainMenu::CoachCareerNewOnClick()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (GetInputEnabled())
	{

		//#if PLATFORM_WINDOWS
#ifdef ENABLE_STEAMCHECK
		int rand1 = FMath::Rand();
		int rand2 = FMath::Rand();

		if (MemCheck::instance().GetTheBallRolling(rand1, rand2) == ~(rand1 + rand2))
#endif
		{
			if (m_gameInstance)
			{
				int NewCareerSlotIndex = RUFranchiseHelpers::GetLowestAvailableCareerSaveSlot(true);
				if (NewCareerSlotIndex >= RUFranchiseHelpers::GetMaxNumberOfSavedCareers(true) || NewCareerSlotIndex < 0)
				{
					// Popup modal for max careers exceeded
					UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

					modalData->WarningDialogue = "[PLT_GetString(MAX_NUM_CAREERS_EXCEEDED_TEXT)]";
					modalData->LegendString = "[ID_MAIN_MENU_HELP]";
					modalData->CloseOnBackButton = true;

					TArray<FModalButtonInfo> ButtonData;

					FWWUIModalDelegate SaveDelegate;
					FWWUIModalDelegate DoNotSaveDelegate;

					SaveDelegate.BindLambda([&](APlayerController* playerController)
						{
							SIFUIHelpers::ListenToPrimaryPlayer();
							return true;
						});

					//save button
					ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_POPUP_OK]"), SaveDelegate));

					modalData->ButtonData = ButtonData;

					m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);

					//			TArray<ARugbyPlayerController*> localControllers = m_gameInstance->GetLocalPlayerControllers();
					//
					// 			for (int i = 0; i < localControllers.Num(); i++)
					// 			{
					// 				if (SIFGameHelpers::IsPrimaryLocalPlayerController(localControllers[i]))
					// 				{
					// 					SIFUIHelpers::ListenToController(i, false);
					// 					break;
					// 				}
					// 			}
					SIFUIHelpers::ListenToNoControllers();
					return;
				}

				// Check save space for console #CareerNeedImplementation

				RUCareerModeManager* pCareerModeManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
				if (pCareerModeManager)
				{
					pCareerModeManager->SetSaveSlot(NewCareerSlotIndex);

#ifdef ENABLE_ANALYTICS
					pCareerModeManager->GetCareerAnalyticsData().AnalyticsSaveSlotData = NewCareerSlotIndex;
#endif

					SIFGameHelpers::GACareerSetSaveSlotNeedsReSelecting(false, true);

					pCareerModeManager->SetLionsTour(false);

					SIFGameHelpers::GALoadProfileMatchSettings();
				}

				SIFUIHelpers::MenuEffectsStartTransition();

				UWWUIStateScreenCareerSetupData* indata = NewObject<UWWUIStateScreenCareerSetupData>();
				if (indata)
				{
					indata->CareerSetupCoachCareer = true;
					indata->CareerSetupNewCareerMode = true;
				}

				m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::CareerSetup, indata);

				//			TArray<ARugbyPlayerController*> localControllers = m_gameInstance->GetLocalPlayerControllers();
				//
				// 			for (int i = 0; i < localControllers.Num(); i++)
				// 			{
				// 				if (SIFGameHelpers::IsPrimaryLocalPlayerController(localControllers[i]))
				// 				{
				// 					SIFUIHelpers::ListenToController(i, false);
				// 					break;
				// 				}
				// 			}
				SIFUIHelpers::ListenToNoControllers();
			}
		}
	}
}

void UWWUIScreenMainMenu::CoachNewTimerFinished()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	int master_controller = SIFUIHelpers::GetCurrentMasterControllerIndex();
	SIFUIHelpers::ListenToController(master_controller, true);

	RUCareerModeManager* careerModeManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
	if (careerModeManager)
	{
		careerModeManager->SetSaveSlot(new_save_slot);  //#MB - not sure where new_save_slot is set from
		SIFGameHelpers::GACareerSetSaveSlotNeedsReSelecting(false, true);

		SIFUIHelpers::MenuEffectsStartTransition();
		//ProceedToNextWindow(MainMenu.load_object_node, parameters)
	}
}

void UWWUIScreenMainMenu::CompetitionLoadOnClick()
{
	if (!GetInputEnabled()) { return; }

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (!bCanLoad)
	{
		return;
	}

	UWWUIStateScreenCareerHUBData* data = NewObject<UWWUIStateScreenCareerHUBData>();


	//joseph claims this is found by the screen on in focus.
	//UWWUIScreenCareerHUB::gameMode = GameMode::Competition;					//#MB - Screen variables
	//UWWUIScreenControllerAssignment::gameMode = GameMode::Competition;
	//UWWUIScreenCareerHUB::ResetSelection = true;
	//UWWUIScreenCareerHUB::AutoLoad = true;
	//CompetitionLoad.AutoLoad = true; //#MB - no matching script
	//UWWUIScreenCareerSetup::CoachCareer = true;
	//UWWUIScreenCareerHUB::CoachCareer = true;

	UWWUIListField* listField = nullptr;
	for (TObjectIterator<UWWUIListField> Itr; Itr; ++Itr)
	{
		// Access the subclass instance with the * or -> operators.
		UWWUIListField* Component = *Itr;
		if (Component->HasFocusedDescendants())
		{
			MABASSERT(!listField);
			listField = Component;
		}
	}

	if (listField)
	{
		//Set the previous screen
		FString screenName = GetScreenID();
		listField->SetProperty("previous_window", &screenName, UIPropertyType::PROPERTY_TYPE_FSTRING);

		FString game_mode = listField->GetStringProperty("game_mode");
		if (game_mode.Compare("1") == 0)
		{
			SIFGameHelpers::GASetGameMode(GAME_MODE::GAME_MODE_RU13W); // Nick  WWS 7s to Womens //SEVENS);SEVENS);
		}


		int slotNum = listField->GetIntProperty("slot");
		SIFGameHelpers::GASetSandboxTickEnabled(false);
		RUCareerModeManager* careerModeManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
		if (careerModeManager)
		{
			careerModeManager->SetSaveSlot(slotNum);
			SIFGameHelpers::GACompetitionSetSaveSlotNeedsReSelecting(false);
			SIFUIHelpers::MenuEffectsStartTransition();

			m_loadingCareerComp = true;

			UWWUICompetitionLoadInData* competitionLoadInData = NewObject<UWWUICompetitionLoadInData>();
			competitionLoadInData->WWWidget = listField;
			competitionLoadInData->bCoachCareer = true;
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::CompetitionLoad, competitionLoadInData);

			//			TArray<ARugbyPlayerController*> localControllers = m_gameInstance->GetLocalPlayerControllers();
			//
			// 			for (int i = 0; i < localControllers.Num(); i++)
			// 			{
			// 				if (SIFGameHelpers::IsPrimaryLocalPlayerController(localControllers[i]))
			// 				{
			// 					SIFUIHelpers::ListenToController(i, false);
			// 					break;
			// 				}
			// 			}
			SIFUIHelpers::ListenToNoControllers();
		}
	}
}

void UWWUIScreenMainMenu::CompetitionLoadTimerFinished()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	int master_controller = SIFUIHelpers::GetCurrentMasterControllerIndex();
	SIFUIHelpers::ListenToController(master_controller, true);
	if (load_object_node)
	{
		int slot_value = load_object_node->GetIntProperty("slot");   //CopyPropertyToNextWindow(MainMenu.load_object_node, "slot");
		int enumVal = load_object_node->GetIntProperty("slot");
		GameModeType game_mode = GameModeType(enumVal);
		if (game_mode == GameModeType::Competition)
		{
			SIFGameHelpers::GASetGameMode(GAME_MODE::GAME_MODE_RU13W); // Nick  WWS 7s to Womens //SEVENS);SEVENS);
		}

		SIFGameHelpers::GASetSandboxTickEnabled(false);

		SIFGameHelpers::GASetSandboxTickEnabled(false);
		RUCareerModeManager* careerModeManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
		if (careerModeManager)
		{
			careerModeManager->SetSaveSlot(slot_value);
			SIFGameHelpers::GACompetitionSetSaveSlotNeedsReSelecting(false);

			SIFUIHelpers::MenuEffectsStartTransition();
			//ProceedToNextWindow(MainMenu.load_object_node, parameters)
		}
	}
}


void UWWUIScreenMainMenu::CoachCareerLoadOnClick()
{
	if (!GetInputEnabled()) { return; }

	if (!bCanLoad)
	{
		return;
	}

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	//UWWUIScreenCareerHUB::gameMode = GameMode::Career;					//#MB - screen variables
	//UWWUIScreenControllerAssignment::gameMode = GameMode::Career;
	//UWWUIScreenCareerHUB::ResetSelection = true;
	//UWWUIScreenCareerHUB::CoachCareer = true;
	//CareerLoad.AutoLoad = true  #MB - no corresponding script

	UWWUIListField* listField = nullptr;
	for (TObjectIterator<UWWUIListField> Itr; Itr; ++Itr)
	{
		// Access the subclass instance with the * or -> operators.
		UWWUIListField* Component = *Itr;
		if (Component->HasFocusedDescendants())
		{
			MABASSERT(!listField);
			listField = Component;
		}
	}

	if (listField)
	{
		//Set the previous screen
		FString screenName = GetScreenID();
		listField->SetProperty("previous_window", &screenName, UIPropertyType::PROPERTY_TYPE_FSTRING);

		int slotNum = listField->GetIntProperty("slot");

		SIFGameHelpers::GASetSandboxTickEnabled(false);

		RUCareerModeManager* careerModeManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
		if (careerModeManager)
		{
			careerModeManager->SetSaveSlot(slotNum);
			SIFGameHelpers::GACareerSetSaveSlotNeedsReSelecting(false, true);

			m_loadingCareerComp = true;

			SIFUIHelpers::MenuEffectsStartTransition();
			UWWUICareerLoadInData* careerLoadInData = NewObject<UWWUICareerLoadInData>();
			careerLoadInData->WWWidget = listField;
			careerLoadInData->bCoachCareer = true;
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::CareerLoad, careerLoadInData);
			//ProceedToNextWindow(ui_object, parameters)

//			TArray<ARugbyPlayerController*> localControllers = m_gameInstance->GetLocalPlayerControllers();
//
// 			for (int i = 0; i < localControllers.Num(); i++)
// 			{
// 				if (SIFGameHelpers::IsPrimaryLocalPlayerController(localControllers[i]))
// 				{
// 					SIFUIHelpers::ListenToController(i, false);
// 					break;
// 				}
// 			}
			SIFUIHelpers::ListenToNoControllers();
		}
	}
}

void UWWUIScreenMainMenu::CoachLoadTimerFinished()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	int master_controller = SIFUIHelpers::GetCurrentMasterControllerIndex();
	SIFUIHelpers::ListenToController(master_controller, true);

	if (load_object_node)
	{
		int slot_value = load_object_node->GetIntProperty("slot");

		SIFGameHelpers::GASetSandboxTickEnabled(false);

		RUCareerModeManager* careerModeManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
		if (careerModeManager)
		{
			careerModeManager->SetSaveSlot(slot_value);  //#MB - not sure where new_save_slot is set from
			SIFGameHelpers::GACareerSetSaveSlotNeedsReSelecting(false, true);

			SIFUIHelpers::MenuEffectsStartTransition();
			//ProceedToNextWindow(MainMenu.load_object_node, parameters)
		}
	}
}

bool UWWUIScreenMainMenu::CoachCareerOnFocus()
{
	UWidget* parent = FindChildWidget(FString("SaveInfoListBox"));
	if (parent)
	{
		UTextBlock* compNameNode = Cast<UTextBlock>(FindChildOfTemplateWidget(parent, "CompName"));
		//local button_node = UINodeGetChild(ui_object, "SaveInfoListBox")
		//local comp_name_node = UINodeGetChild(button_node, "Competition/CompName")

		//Set the node to being invisible if there's no text.
		if (compNameNode)
		{
			bool set_visible = !compNameNode->Text.ToString().IsEmpty();
			if (set_visible)
			{
				parent->SetVisibility(ESlateVisibility::Visible);
			}
			else
			{
				parent->SetVisibility(ESlateVisibility::Collapsed);
			}
			return true;
		}
	}
	return false;
}

bool UWWUIScreenMainMenu::CoachCareerOnFocusLost()
{
	UWidget* saveInfoListBox = FindChildWidget(FString("SaveInfoListBox"));
	if (saveInfoListBox)
	{
		saveInfoListBox->SetVisibility(ESlateVisibility::Collapsed);
		return true;
	}
	return false;

}

bool UWWUIScreenMainMenu::DeleteCoachCareerFile(APlayerController* playerController, int slotNum)
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (playerController == nullptr)
	{
		playerController = GetMasterPlayerController();
	}

	if (slotNum >= 0 /*&& slotNum < competitionList.Num()*/)
	{
		RUCareerModeManager* careerManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
		if (careerManager)
		{
			DeletingFromGameModeType = GameModeType::Career_Coach;
			DeleteSlotIndex = slotNum;
			DeletingPlayerController = playerController;
			SIFUIHelpers::ShowSavingOverlay(true, true);

			careerManager->DeleteCareerFile(slotNum, true);

			// repopulating list etc from system event save callbacks to allow for async saves.

			return true;
		}
	}
	//local popup_parent = UINodeGetParent(UINodeGetParent(UINodeGetParent(UINodeGetParent(UINodeGetParent(ui_object)))));
	//local slot_num_raw = UINodeGetProperty(popup_parent, "slot");
	return false;
}

//Replaces CoachCareerOnAction
bool UWWUIScreenMainMenu::CoachCareerOnDelete(int slotNum)
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();
	/*if (SIFGameHelpers::GAIsConsole())
	{
		if (!SIFGameHelpers::GAIsPlatformXboxOne() && !SIFGameHelpers::GAIsPlatformPS4())
		{
			return false;
		}
	}*/

	//Delete career popup
	if (m_gameInstance)
	{
		UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

		modalData->WarningDialogue = "[ID_DELETE_CAREER_POPUP_TEXT";
		modalData->LegendString = "[ID_MAIN_MENU_SUB_HELP]";
		modalData->CloseOnBackButton = true;
		modalData->slotNumber = slotNum;

		TArray<FModalButtonInfo> ButtonData;

		FWWUIModalDelegate SaveDelegate;
		FWWUIModalDelegate DoNotSaveDelegate;

		//save button
		ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_POPUP_OK]"), SaveDelegate));
		ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_CANCEL]"), DoNotSaveDelegate));

		modalData->ButtonData = ButtonData;

		m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
	}

	return true;

}

//Be a pro mode Kade WW
void UWWUIScreenMainMenu::BeAProOnClick()
{
	if (!GetInputEnabled()) { return; }

	//#if PLATFORM_WINDOWS
#ifdef ENABLE_STEAMCHECK
	int rand1 = FMath::Rand();
	int rand2 = FMath::Rand();

	if (MemCheck::instance().GetTheBallRolling(rand1, rand2) == ~(rand1 + rand2))
#endif
	{
		URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

		//Check to see if we can save a new competition.
		int new_career_slot_index = RUFranchiseHelpers::GetLowestAvailableCareerSaveSlot(false);
		if (new_career_slot_index >= RUFranchiseHelpers::GetMaxNumberOfSavedCareers(false) || new_career_slot_index < 0)
		{
			//local popup_node = UILaunchPopUpByName("MaxNumProCareersExceeded");
			if (m_gameInstance)
			{
				UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

				modalData->WarningDialogue = "[PLT_GetString(MAX_NUM_PRO_CAREERS_EXCEEDED_TEXT)]";
				modalData->LegendString = "[ID_MAIN_MENU_HELP]";
				modalData->CloseOnBackButton = true;
				modalData->CloseOnSelectButton = true;

				TArray<FModalButtonInfo> ButtonData;

				FWWUIModalDelegate SaveDelegate;
				FWWUIModalDelegate DoNotSaveDelegate;


				SaveDelegate.BindLambda([&](APlayerController* playerController)
					{
						SIFUIHelpers::ListenToPrimaryPlayer();
						return true;
					});

				//save button
				ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_POPUP_OK]"), SaveDelegate));

				modalData->ButtonData = ButtonData;

				m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);

				//			TArray<ARugbyPlayerController*> localControllers = m_gameInstance->GetLocalPlayerControllers();
				//
				// 			for (int i = 0; i < localControllers.Num(); i++)
				// 			{
				// 				if (SIFGameHelpers::IsPrimaryLocalPlayerController(localControllers[i]))
				// 				{
				// 					SIFUIHelpers::ListenToController(i, false);
				// 					break;
				// 				}
				// 			}
				SIFUIHelpers::ListenToNoControllers();
			}
			return;
		}

		//UWWUIScreenCareerHUB::gameMode = GameMode::Career;				//#MB - screen variables
		//UWWUIScreenControllerAssignment::gameMode = GameMode::Career;
		//UWWUIScreenCareerSetup::NewCareerMode = true;
		//UWWUIScreenCareerHUB::CoachCareer = false;
		//UWWUIScreenCareerSetup::CoachCareer = false;

		if (m_gameInstance)
		{
			RUCareerModeManager* pCareerModeManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
			if (pCareerModeManager)
			{
				pCareerModeManager->SetSaveSlot(new_career_slot_index);

#ifdef ENABLE_ANALYTICS
				pCareerModeManager->GetCareerAnalyticsData().AnalyticsSaveSlotData = new_career_slot_index;
#endif

				SIFGameHelpers::GACareerSetSaveSlotNeedsReSelecting(false, false);

				SIFUIHelpers::MenuEffectsStartTransition();

				UWWUIStateScreenCareerSetupData* indata = NewObject<UWWUIStateScreenCareerSetupData>();

				if (indata)
				{
					indata->CareerSetupCoachCareer = false;
					indata->CareerSetupNewCareerMode = true;
				}
				m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::CareerSetup, indata);

				//			TArray<ARugbyPlayerController*> localControllers = m_gameInstance->GetLocalPlayerControllers();
				//
				// 			for (int i = 0; i < localControllers.Num(); i++)
				// 			{
				// 				if (SIFGameHelpers::IsPrimaryLocalPlayerController(localControllers[i]))
				// 				{
				// 					SIFUIHelpers::ListenToController(i, false);
				// 					break;
				// 				}
				// 			}
				SIFUIHelpers::ListenToNoControllers();
			}
		}
	}
}

void UWWUIScreenMainMenu::ProModeNewTimerFinished()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	int master_controller = SIFUIHelpers::GetCurrentMasterControllerIndex();
	SIFUIHelpers::ListenToController(master_controller, true);

	RUCareerModeManager* pCareerModeManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
	if (pCareerModeManager)
	{
		pCareerModeManager->SetSaveSlot(new_save_slot);
		SIFGameHelpers::GACareerSetSaveSlotNeedsReSelecting(false, false);

		SIFUIHelpers::MenuEffectsStartTransition();
		//m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::);
	}
}

void UWWUIScreenMainMenu::ProModeLoadOnClick()
{
	if (!GetInputEnabled()) { return; }

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (!bCanLoad)
	{
		return;
	}


	/*UWWUIScreenCareerHUB::gameMode = GameMode::Career;					//#MB - screen variables
	UWWUIScreenControllerAssignment::gameMode = GameMode::Career;
	UWWUIScreenCareerHUB::ResetSelection = true;
	UWWUIScreenCareerHUB::CoachCareer = false;*/
	//CareerLoad.AutoLoad = true  #MB - no corresponding script

	UWWUIListField* listField = nullptr;
	for (TObjectIterator<UWWUIListField> Itr; Itr; ++Itr)
	{
		// Access the subclass instance with the * or -> operators.
		UWWUIListField* Component = *Itr;
		if (Component->HasFocusedDescendants())
		{
			MABASSERT(!listField);
			listField = Component;
		}
	}

	if (listField)
	{
		//Set the previous screen
		FString screenName = GetScreenID();
		listField->SetProperty("previous_window", &screenName, UIPropertyType::PROPERTY_TYPE_FSTRING);
		int slotNum = listField->GetIntProperty("slot");

		SIFGameHelpers::GASetSandboxTickEnabled(false);

		RUCareerModeManager* pCareerModeManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
		if (pCareerModeManager)
		{
			pCareerModeManager->SetSaveSlot(slotNum);
			SIFGameHelpers::GACareerSetSaveSlotNeedsReSelecting(false, false);

			m_loadingCareerComp = true;

			SIFUIHelpers::MenuEffectsStartTransition();
			UWWUICareerLoadInData* careerLoadInData = NewObject<UWWUICareerLoadInData>();
			careerLoadInData->WWWidget = listField;
			careerLoadInData->bCoachCareer = false;
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::CareerLoad, careerLoadInData);
			//ProceedToNextWindow(ui_object, parameters)
		}
	}
}

void UWWUIScreenMainMenu::ProLoadTimerFinished()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	int master_controller = SIFUIHelpers::GetCurrentMasterControllerIndex();
	SIFUIHelpers::ListenToController(master_controller, true);
	int slot_value = load_object_node->GetIntProperty("slot");

	SIFGameHelpers::GASetSandboxTickEnabled(false);

	RUCareerModeManager* pCareerModeManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
	if (pCareerModeManager)
	{
		pCareerModeManager->SetSaveSlot(slot_value);
		SIFGameHelpers::GACareerSetSaveSlotNeedsReSelecting(false, false);

		SIFUIHelpers::MenuEffectsStartTransition();
		//ProceedToNextWindow(UWWUIScreenMainMenu.load_object_node, parameters)
	}
}

bool UWWUIScreenMainMenu::ProModeOnFocus()
{
	UWidget* parent = FindChildWidget(FString("SaveInfoListBox"));
	if (parent)
	{
		UTextBlock* compNameNode = Cast<UTextBlock>(FindChildOfTemplateWidget(parent, "CompName"));
		//local button_node = UINodeGetChild(ui_object, "SaveInfoListBox")
		//local comp_name_node = UINodeGetChild(button_node, "Competition/CompName")

		//Set the node to being invisible if there's no text.
		if (compNameNode)
		{
			bool set_visible = !compNameNode->Text.ToString().IsEmpty();
			if (set_visible)
			{
				parent->SetVisibility(ESlateVisibility::Visible);
			}
			else
			{
				parent->SetVisibility(ESlateVisibility::Collapsed);
			}
			return true;
		}
	}
	return false;
}

bool UWWUIScreenMainMenu::ProModeOnFocusLost()
{
	UWidget* saveInfoListBox = FindChildWidget(FString("SaveInfoListBox"));
	if (saveInfoListBox)
	{
		saveInfoListBox->SetVisibility(ESlateVisibility::Collapsed);
		return true;
	}
	return false;
}

bool UWWUIScreenMainMenu::ProModeOnAction(int slotNum)
{

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();
	if (SIFGameHelpers::GAIsConsole())
	{
		if (!SIFGameHelpers::GAIsPlatformXboxOne() && !SIFGameHelpers::GAIsPlatformPS4())
		{
			return false;
		}
	}

	/*local popup_node = UILaunchPopUpByName("DeleteProModePopup");*/
	if (m_gameInstance)
	{
		UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

		modalData->WarningDialogue = "[ID_DELETE_PRO_MODE_POPUP_TEXT]";
		modalData->LegendString = "[ID_MAIN_MENU_SUB_HELP]";
		modalData->CloseOnBackButton = true;
		modalData->slotNumber = slotNum;

		TArray<FModalButtonInfo> ButtonData;

		FWWUIModalDelegate SaveDelegate;
		FWWUIModalDelegate DoNotSaveDelegate;

		//save button
		ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_POPUP_OK]"), SaveDelegate));
		ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_CANCEL]"), DoNotSaveDelegate));

		modalData->ButtonData = ButtonData;

		m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
	}
	return true;
}

bool UWWUIScreenMainMenu::DeleteProModeFile(APlayerController* playerController, int slotNum)
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (playerController == nullptr)
	{
		playerController = GetMasterPlayerController();
	}

	if (m_gameInstance)
	{
		if (slotNum >= 0 /*&& slotNum < competitionList.Num()*/)
		{
			RUCareerModeManager* careerManager = m_gameInstance ? m_gameInstance->GetCareerModeManager() : nullptr;
			if (careerManager)
			{
				DeletingFromGameModeType = GameModeType::Career_Pro;
				DeleteSlotIndex = slotNum;
				DeletingPlayerController = playerController;
				SIFUIHelpers::ShowSavingOverlay(true, true);

				careerManager->DeleteCareerFile(slotNum, false);

				// repopulating list etc from system event save callbacks to allow for async saves.

				return true;
			}
		}
	}
	//local popup_parent = UINodeGetParent(UINodeGetParent(UINodeGetParent(UINodeGetParent(UINodeGetParent(ui_object)))));
	//local slot_num_raw = UINodeGetProperty(popup_parent, "slot");
	return false;
}




void UWWUIScreenMainMenu::OnUserCanPlayOnlinePrivateMatchOption()
{
	SIFApplication::GetApplication()->GetMatchmakingManager()->SetupPrivate15s();
	//UINodeSetProperty(OnlineSelectTeams.filtered_teams_list, "team_limit", "RC3_TEAMS_NO_SEVENS")              pass screen data

	/*SIFGameHelpers::GASetTeam(0, MultiplayerLobby.DEFAULT_FIFTEENS_TEAM)   #MB #rc3_legacy_profile - requires profile data
	SIFGameHelpers::GASetTeam(1, MultiplayerLobby.DEFAULT_FIFTEENS_TEAM)*/

	//	Default Game Time for 15's online
	SIFGameHelpers::GASetGameLength(10);

	UWWUIScreenMainMenu::PrivateMatchOptionOnClick();
}

void UWWUIScreenMainMenu::OnUserCanPlayOnlineSevensPrivateMatchOption()
{
	SIFApplication::GetApplication()->GetMatchmakingManager()->SetupPrivate7s();
	//UINodeSetProperty(OnlineSelectTeams.filtered_teams_list, "team_limit", "CUSTOM_SEVENS")                     pass screen data

	/*SIFGameHelpers::GASetTeam(0, MultiplayerLobby.DEFAULT_SEVENS_TEAM)	#MB #rc3_legacy_profile - requires profile data
	SIFGameHelpers::GASetTeam(1, MultiplayerLobby.DEFAULT_SEVENS_TEAM)*/

	//	Default Game Time for 7's online
	SIFGameHelpers::GASetGameLength(7);

	UWWUIScreenMainMenu::PrivateMatchOptionOnClick();
}



void UWWUIScreenMainMenu::OnUserCanPlayOnlineRankedMatchOption()
{
	SIFApplication::GetApplication()->GetMatchmakingManager()->SetupRanked15s();

	//UINodeSetProperty(OnlineSelectTeams.filtered_teams_list, "team_limit", "RC3_TEAMS_NO_SEVENS")              pass screen data

	/*SIFGameHelpers::GASetTeam(0, MultiplayerLobby.DEFAULT_FIFTEENS_TEAM)   #MB #rc3_legacy_profile - requires profile data
	SIFGameHelpers::GASetTeam(1, MultiplayerLobby.DEFAULT_FIFTEENS_TEAM)*/

	SIFGameHelpers::GASetGameLength(10);
	UWWUIScreenMainMenu::RankedMatchOptionOnClick();
}

void UWWUIScreenMainMenu::OnUserCanPlayOnlineSevensRankedMatchOption()
{
	SIFApplication::GetApplication()->GetMatchmakingManager()->SetupRanked7s();

	//UINodeSetProperty(OnlineSelectTeams.filtered_teams_list, "team_limit", "CUSTOM_SEVENS")                     pass screen data

	/*SIFGameHelpers::GASetTeam(0, MultiplayerLobby.DEFAULT_SEVENS_TEAM)	#MB #rc3_legacy_profile - requires profile data
	SIFGameHelpers::GASetTeam(1, MultiplayerLobby.DEFAULT_SEVENS_TEAM)*/
	// Nick  WWS 7s to Womens //
	//SIFGameHelpers::GASetGameLength(7);
	UWWUIScreenMainMenu::RankedMatchOptionOnClick();
}





void UWWUIScreenMainMenu::OnUserCanPlayOnlineQuickMatchOption()
{
	SIFApplication::GetApplication()->GetMatchmakingManager()->Reset();
	SIFApplication::GetApplication()->GetMatchmakingManager()->SetupQuickmatch15s();

	//UINodeSetProperty(OnlineSelectTeams.filtered_teams_list, "team_limit", "RC3_TEAMS_NO_SEVENS");                pass screen data

	/*SIFGameHelpers::GASetTeam(0, MultiplayerLobby.DEFAULT_FIFTEENS_TEAM)		#MB - requires player profile data
	SIFGameHelpers::GASetTeam(1, MultiplayerLobby.DEFAULT_FIFTEENS_TEAM)*/

	SIFGameHelpers::GASetGameLength(10);
	UWWUIScreenMainMenu::OnlineQuickMatchOptionOnClick();
}



void UWWUIScreenMainMenu::OnUserCanPlayOnlineSevensQuickMatchOption()
{
	SIFApplication::GetApplication()->GetMatchmakingManager()->Reset();
	SIFApplication::GetApplication()->GetMatchmakingManager()->SetupQuickmatch7s();
	//UINodeSetProperty(OnlineSelectTeams.filtered_teams_list, "team_limit", "CUSTOM_SEVENS");

	/*SIFGameHelpers::GASetTeam(0, MultiplayerLobby::DEFAULT_SEVENS_TEAM);				#MB - requires player profile data
	SIFGameHelpers::GASetTeam(1, MultiplayerLobby::DEFAULT_SEVENS_TEAM);*/

	// Nick  WWS 7s to Womens //
	//SIFGameHelpers::GASetGameLength(7);
	UWWUIScreenMainMenu::OnlineQuickMatchOptionOnClick();
}


void UWWUIScreenMainMenu::OnlineQuickMatchSevens()
{
	if (!GetInputEnabled()) { return; }

	if (SIFApplication::GetApplication()->GetConnectionManager()->GetConnectionStatus() != EOnlineServerConnectionStatus::Type::NotConnected)
	{
#if PLATFORM_WINDOWS
		OnUserCanPlayOnlineSevensQuickMatchOption();
#else
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		m_allowedToProceed = false;	// hold button press that brought us here (it hasn't happened yet)
		if (pRugbyGameInstance)
		{
			pRugbyGameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanPlayOnline, "OnUserCanPlayOnlineSevensQuickMatchOption", this, true, false);
		}
#endif
	}
	else
	{

		SIFApplication::GetApplication()->ShowErrorUIPopup("[ID_CONNECTION_ERROR]", "[ID_NOTCONNECTEDTOINTERNETCHECKCONNECTIONTRYAGAIN]");
	}
}

void UWWUIScreenMainMenu::OnlineQuickMatchFifteens()
{
	if (!GetInputEnabled()) { return; }

	if (SIFApplication::GetApplication()->GetConnectionManager()->GetConnectionStatus() != EOnlineServerConnectionStatus::Type::NotConnected)
	{
#if PLATFORM_WINDOWS
		OnUserCanPlayOnlineQuickMatchOption();
#else
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		m_allowedToProceed = false;	// hold button press that brought us here (it hasn't happened yet)
		if (pRugbyGameInstance)
		{
			pRugbyGameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanPlayOnline, "OnUserCanPlayOnlineQuickMatchOption", this, true, false);
		}
#endif
	}
	else
	{

		SIFApplication::GetApplication()->ShowErrorUIPopup("[ID_CONNECTION_ERROR]", "[ID_NOTCONNECTEDTOINTERNETCHECKCONNECTIONTRYAGAIN]");
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenMainMenu::OnFanHubMenuClicked()
{
	if (!GetInputEnabled())
	{
		return;
	}

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	m_allowedToProceed = false;	// hold button press that brought us here (it hasn't happened yet)

	//We don't need the privilege check here if the champion data has already been checked.
	if (ChampionDataResponseVersion >= 0)
	{
		// need to get auth token, or we will fail the lotcheck here!
		m_allowedToProceed = false; // true;
		// return;
	}

	if (pRugbyGameInstance)
	{
#if PLATFORM_SWITCH
		pRugbyGameInstance->TryingToStartOnlineMode = true;
		UWWUIModalWaitingData* modalData = NewObject<UWWUIModalWaitingData>();
		modalData->BodyString = "[ID_ONLINE_CONNECTING_WAIT]";

		//Gives our modal a custom tick function to call during its own tick, this allows for actions to take place while the modal is on the screen blocking the main tick
		modalData->CustomTickDelegate.BindUObject(this, &UWWUIScreenMainMenu::NexUpdate);

		pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::ModalWaiting, modalData);

		IsTryingToStartNEX = true;

		if (pRugbyGameInstance->IsPrimaryPlayerOnline())
		{
			UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::OnOnlineClicked() IsPrimaryPlayerOnline() == True"));
			NEXStartUpTimer = 25.0f;
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::OnOnlineClicked() IsPrimaryPlayerOnline() == False"));
			NEXStartUpTimer = 5.0f; //shorten cop out timer so user doesnt unneccesarily wait
		}


		pRugbyGameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanPlayOnline, "ActuallyGotoFanhubMenu", this, true, false);

		//pRugbyGameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanPlayOnline, "ActuallyGotoOnlineOption", this, true, false);
		//PlatformStatusChecker->StartOnlineChecks(playerController, "TryStartNEX", this, true);
#elif PLATFORM_WINDOWS
		ActuallyGotoOnlineOption();
#else
		pRugbyGameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanPlayOnline, "ActuallyGotoOnlineOption", this, true, false);
#endif
	}

#ifdef ENABLE_ANALYTICS
	UE_LOG(LogTemp, Warning, TEXT("======================== FanHub menu entered ========================"));
	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->GetPlayerAnalyticsData().StartFanHubTimer();
	}
#endif

}

//===============================================================================
//===============================================================================
void UWWUIScreenMainMenu::OnCustomiseMenuClicked()
{
#ifdef SHOW_UGC_CREATOR
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->HandleUGCUsernameCheck();
	}
#endif
}

//===============================================================================
//===============================================================================
void UWWUIScreenMainMenu::LiveDataOnClick()
{
	//#if PLATFORM_WINDOWS
#ifdef ENABLE_STEAMCHECK
	int rand1 = FMath::Rand();
	int rand2 = FMath::Rand();

	if (MemCheck::instance().GetTheBallRolling(rand1, rand2) == ~(rand1 + rand2))
#endif
	{
		// Check we have received valid data from the server.
		if (ChampionDataResponseVersion >= 0)
		{
#if PLATFORM_PS4
			// Need to do a network features test. RC4-6516.
			URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
			if (pRugbyGameInstance)
			{
				pRugbyGameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanPlayOnline, "StartChampionDataDownload", this, true, false);
			}
#else
			StartChampionDataDownload();
#endif
		}
		else
		{
			SIFUIHelpers::LaunchWarningPopup("[ID_LIVE_DATA_DOWNLOAD_FAIL]", "[ID_COPYRIGHT_HELP]", TArray<FModalButtonInfo>());
		}
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenMainMenu::OnlineQuickMatchOptionOnClick()
{
	m_allowedToProceed = true;
	if (SIFApplication::GetApplication()->GetConnectionManager()->GetConnectionStatus() != EOnlineServerConnectionStatus::Type::NotConnected)
	{
		if (!SIFGameHelpers::GAIsQuickLoadActive())
		{
			bOnlineProfilesSelected = false;

			//For PC : whichever controller clicked this button should become the master controller.
			//The user can use keyboard or controller for all the menus, but when they enter this screen
			// they need to commit to which one they are using.
#if PLATFORM_WINDOWS
			if (SIFGameHelpers::GAIsPlatformPC())
			{
				SIFUIHelpers::SetCurrentMasterController(0/*#MB - pass controller here, controller_id*/);
			}
#endif

#if PLATFORM_PS4
			if (SIFGameHelpers::GAIsPlatformPS4())
			{
				//SIFGeneralHelpersPS4::SignalAsynchronousMultiplayer(UIGetCurrentMasterController());
			};
#endif

			//Online quick match will have random values when you play it.
			//ONLINE_GAME_NODE = ui_object    #MB - find where this is passed
			ONLINE_ACCEPT_INVITE = false;
			ConnectToOnlineServer(true, false, 0 /*parameters.controller_id*/); // #rc3_legacy_network

			// Old in data for reference
			// UINodeSetProperty(next_window_object, "is_quick_match", "true")
			// UINodeSetProperty(next_window_object, "settings_object", "none")
			// UINodeSetProperty(next_window_object, "search_ct_idx", parameters.controller_id)
		}
	}
	else
	{

		SIFApplication::GetApplication()->ShowErrorUIPopup("[ID_CONNECTION_ERROR]", "[ID_NOTCONNECTEDTOINTERNETCHECKCONNECTIONTRYAGAIN]");
	}
}




void UWWUIScreenMainMenu::OnlinePrivateMatchSevens()
{
	if (!GetInputEnabled()) { return; }

	if (SIFApplication::GetApplication()->GetConnectionManager()->GetConnectionStatus() != EOnlineServerConnectionStatus::Type::NotConnected)
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		m_allowedToProceed = false;	// hold button press that brought us here (it hasn't happened yet)
		if (pRugbyGameInstance)
		{
			pRugbyGameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanPlayOnline, "OnUserCanPlayOnlineSevensPrivateMatchOption", this, true, false);
		}
	}
	else
	{

		SIFApplication::GetApplication()->ShowErrorUIPopup("[ID_CONNECTION_ERROR]", "[ID_NOTCONNECTEDTOINTERNETCHECKCONNECTIONTRYAGAIN]");
	}
}

void UWWUIScreenMainMenu::OnlinePrivateMatchFifteens()
{
	if (!GetInputEnabled()) { return; }

	if (SIFApplication::GetApplication()->GetConnectionManager()->GetConnectionStatus() != EOnlineServerConnectionStatus::Type::NotConnected)
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		m_allowedToProceed = false;	// hold button press that brought us here (it hasn't happened yet)
		if (pRugbyGameInstance)
		{
			pRugbyGameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanPlayOnline, "OnUserCanPlayOnlinePrivateMatchOption", this, true, false);
		}
	}
	else
	{

		SIFApplication::GetApplication()->ShowErrorUIPopup("[ID_CONNECTION_ERROR]", "[ID_NOTCONNECTEDTOINTERNETCHECKCONNECTIONTRYAGAIN]");
	}
}


void UWWUIScreenMainMenu::PrivateMatchOptionOnClick()
{
	m_allowedToProceed = true;

	if (SIFApplication::GetApplication()->GetConnectionManager()->GetConnectionStatus() != EOnlineServerConnectionStatus::Type::NotConnected)
	{
		URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

		if (!SIFGameHelpers::GAIsQuickLoadActive())
		{
			// For PC : whichever controller clicked this button should become the master controller.
			//The user can use keyboard or controller for all the menus, but when they enter this screen
			// they need to commit to which one they are using.
#if PLATFORM_WINDOWS
			if (SIFGameHelpers::GAIsPlatformPC())
			{
				if (m_gameInstance)
				{
					//SIFUIHelpers::SetCurrentMasterController(m_gameInstance->GetMasterPlayerController());
				}
			}
#endif

#if PLATFORM_PS4
			if (SIFGameHelpers::GAIsPlatformPS4())
			{
				//SIFGeneralHelpersPS4::SignalAsynchronousMultiplayer(UIGetCurrentMasterController());
			};
#endif

			bOnlineProfilesSelected = false;
			//ONLINE_GAME_NODE = ui_object      #MB - find this
			ONLINE_ACCEPT_INVITE = false;
			SIFApplication::GetApplication()->SetOnlineMode(EOnlineMode::Online);
			ConnectToOnlineServer(false, false, 0 /*parameters.controller_id*/); // #rc3_legacy_network

			// Old in data for reference
			// UINodeSetProperty(next_window_object, "is_quick_match", "false")
			// UINodeSetProperty(next_window_object, "settings_object", "none")
			// UINodeSetProperty(next_window_object, "search_ct_idx", parameters.controller_id)
		}
	}
	else
	{

		SIFApplication::GetApplication()->ShowErrorUIPopup("[ID_CONNECTION_ERROR]", "[ID_NOTCONNECTEDTOINTERNETCHECKCONNECTIONTRYAGAIN]");
	}
}






void UWWUIScreenMainMenu::OnlineRankedMatchSevens()
{
	if (!GetInputEnabled()) { return; }

	if (SIFApplication::GetApplication()->GetConnectionManager()->GetConnectionStatus() != EOnlineServerConnectionStatus::Type::NotConnected)
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		m_allowedToProceed = false;	// hold button press that brought us here (it hasn't happened yet)
		if (pRugbyGameInstance)
		{
			pRugbyGameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanPlayOnline, "OnUserCanPlayOnlineSevensRankedMatchOption", this, true, false);
		}
	}
	else
	{

		SIFApplication::GetApplication()->ShowErrorUIPopup("[ID_CONNECTION_ERROR]", "[ID_NOTCONNECTEDTOINTERNETCHECKCONNECTIONTRYAGAIN]");
	}
}

void UWWUIScreenMainMenu::OnlineRankedMatchFifteens()
{
	if (!GetInputEnabled()) { return; }

	if (SIFApplication::GetApplication()->GetConnectionManager()->GetConnectionStatus() != EOnlineServerConnectionStatus::Type::NotConnected)
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		m_allowedToProceed = false;	// hold button press that brought us here (it hasn't happened yet)
		if (pRugbyGameInstance)
		{
			pRugbyGameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanPlayOnline, "OnUserCanPlayOnlineRankedMatchOption", this, true, false);
		}
	}
	else
	{

		SIFApplication::GetApplication()->ShowErrorUIPopup("[ID_CONNECTION_ERROR]", "[ID_NOTCONNECTEDTOINTERNETCHECKCONNECTIONTRYAGAIN]");
	}
}

void UWWUIScreenMainMenu::RankedMatchOptionOnClick()
{
	m_allowedToProceed = true;

	if (SIFApplication::GetApplication()->GetConnectionManager()->GetConnectionStatus() != EOnlineServerConnectionStatus::Type::NotConnected)
	{
		URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

		if (!SIFGameHelpers::GAIsQuickLoadActive())
		{
			// For PC : whichever controller clicked this button should become the master controller.
			//The user can use keyboard or controller for all the menus, but when they enter this screen
			// they need to commit to which one they are using.
#if PLATFORM_WINDOWS
			if (SIFGameHelpers::GAIsPlatformPC())
			{
				if (m_gameInstance)
				{
					//SIFUIHelpers::SetCurrentMasterController(m_gameInstance->GetMasterPlayerController());
				}
			}
#endif

#if PLATFORM_PS4
			if (SIFGameHelpers::GAIsPlatformPS4())
			{
				//SIFGeneralHelpersPS4::SignalAsynchronousMultiplayer(UIGetCurrentMasterController());
			};
#endif

			bOnlineProfilesSelected = false;
			//ONLINE_GAME_NODE = ui_object      #MB - find this
			ONLINE_ACCEPT_INVITE = false;

			ConnectToOnlineServer(true, true, 0 /*parameters.controller_id*/); // #rc3_legacy_network

			// Old in data for reference
			// UINodeSetProperty(next_window_object, "is_quick_match", "false")
			// UINodeSetProperty(next_window_object, "settings_object", "none")
			// UINodeSetProperty(next_window_object, "search_ct_idx", parameters.controller_id)
		}
	}
	else
	{

		SIFApplication::GetApplication()->ShowErrorUIPopup("[ID_CONNECTION_ERROR]", "[ID_NOTCONNECTEDTOINTERNETCHECKCONNECTIONTRYAGAIN]");
	}
}




void UWWUIScreenMainMenu::RugbyStoreOptionOnClick()
{
	//RugbyStore.repopulate = true				#MB - come back when rugby store exists
	//ProceedToNextWindow(){
}

void UWWUIScreenMainMenu::HelpAndOptionsOptionOnClick()
{
	//ProceedToNextWindow(){
}

void UWWUIScreenMainMenu::NewCompetitionOnClick()
{
	if (!GetInputEnabled()) { return; }

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (bReversionInProgress)
	{
		return;
	}

	// ##################### REMOVE THIS ONCE THIS HAS BEEN IMPLEMENTED ########

	UWWUIFunctionLibrary::OnTimer(0.01f, FTimerDelegate::CreateLambda([=]() { SetInputEnabled(true); }));

	// ##################### REMOVE THIS ONCE THIS HAS BEEN IMPLEMENTED ########

	if (!SIFGameHelpers::GAIsQuickLoadActive())
	{
		if (!SIFGameHelpers::GACheckDBCompetitionLimit())
		{
			//UILaunchPopUpByName("NewCompetitionNoSpace") 
			if (m_gameInstance)
			{
				UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

				modalData->WarningDialogue = "[ID_CUSTOM_COMPETITION_LIMIT_POPUP_TEXT]";
				modalData->LegendString = "[ID_MAIN_MENU_HELP]";
				modalData->CloseOnBackButton = true;
				modalData->CloseOnSelectButton = true;

				TArray<FModalButtonInfo> ButtonData;

				FWWUIModalDelegate SaveDelegate;
				FWWUIModalDelegate DoNotSaveDelegate;

				//save button
				ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_POPUP_OK]"), SaveDelegate));

				modalData->ButtonData = ButtonData;

				m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
			}
			return;
		}

		// Set the tab window title to 'Create Competition'
		//UIGetNode("RootTabWindow/CustomiseCompTabWindow/Breadcrumb/Text").text_string = "[ID_CREATE_COMPETITION_OPTION_MENU_ITEM]"

		SIFUIHelpers::MenuEffectsStartTransition();

		if (m_gameInstance)
		{
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::CustomiseCompTemplate);
		}
		//ProceedToNextWindow()
	}
}

void UWWUIScreenMainMenu::EditCompetitionOnClick()
{
	if (!GetInputEnabled()) { return; }

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	// ##################### REMOVE THIS ONCE THIS HAS BEEN IMPLEMENTED ########

	UWWUIFunctionLibrary::OnTimer(0.01f, FTimerDelegate::CreateLambda([=]() { SetInputEnabled(true); }));

	// ##################### REMOVE THIS ONCE THIS HAS BEEN IMPLEMENTED ########

	if (bReversionInProgress)
	{
		return;
	}

	if (!SIFGameHelpers::GAIsQuickLoadActive())
	{
		//CustomiseCompLoad.is_delete = false				#MB - requires customise screen

		//Set the tab window title to 'Edit Competition'
		//UIGetNode("RootTabWindow/CustomiseCompTabWindow/Breadcrumb/Text").text_string = "[ID_COMPETITION_EDIT_TITLE]"

		//SIFUIHelpers::MenuEffectsStartTransition();

		if (m_gameInstance)
		{
			if (IsEnoughOptionsForLinked(LinkCheckType::Comp))
			{
				m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::CustomiseCompLoad);
			}
			else
			{
				TArray<FModalButtonInfo> ButtonData;
				ButtonData.Add(FModalButtonInfo("[ID_POPUP_OK]"));
				SIFUIHelpers::LaunchWarningPopup("[ID_NO_AVAILABLE_COMPETITIONS_CUSTOMISE]", "[ID_MAIN_MENU_HELP]", ButtonData);
			}
		}
		//ProceedToNextWindow(){
	}
}

void UWWUIScreenMainMenu::CreateKitOnClick()
{
	if (!GetInputEnabled()) { return; }

	//#if PLATFORM_WINDOWS
#ifdef ENABLE_STEAMCHECK
	int rand1 = FMath::Rand();
	int rand2 = FMath::Rand();

	if (MemCheck::instance().GetTheBallRolling(rand1, rand2) == ~(rand1 + rand2))
#endif
	{
		URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

		if (!SIFGameHelpers::GACheckDBStripLimit())
		{
			//UILaunchPopUpByName("NewTeamNoSpace")
			if (m_gameInstance)
			{
				UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

				modalData->WarningDialogue = "[ID_CUSTOM_KIT_LIMIT_POPUP_TEXT]";
				modalData->LegendString = "[ID_MAIN_MENU_HELP]";
				modalData->CloseOnBackButton = true;
				modalData->CloseOnSelectButton = true;

				TArray<FModalButtonInfo> ButtonData;

				FWWUIModalDelegate SaveDelegate;
				FWWUIModalDelegate DoNotSaveDelegate;

				//save button
				ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_POPUP_OK]"), SaveDelegate));

				modalData->ButtonData = ButtonData;

				m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
			}
			return;
		}

		SIFGameHelpers::GAPECreateNewRUDBPlayer();
		SIFAudioHelpers::PushMusic("event:/music/customise_music");

		SIFUIHelpers::MenuEffectsStartTransition();

		if (m_gameInstance)
		{
			FWWUICutsceneLoadComplete LoadCompleteDelegate = FWWUICutsceneLoadComplete::CreateUObject(this, &UWWUIScreenMainMenu::ProceedToKitCreator);
			m_gameInstance->DealMenuActionAfterCutsceneEvent(CSEVENT_UI_CUSTOMIZE_PLAYER_BODY, LoadCompleteDelegate, SSCutSceneManager::UI_CUTSCENE_LOAD_CUSTOMISATION_PLAYER);

			SIFUIHelpers::MenuSoundScreenTransition();
		}
	}
}

void UWWUIScreenMainMenu::EditKitOnClick()
{
	if (!GetInputEnabled()) { return; }

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		if (IsEnoughOptionsForLinked(LinkCheckType::Kit))
		{
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::CustomiseSelectKit);
		}
		else
		{
			TArray<FModalButtonInfo> ButtonData;
			ButtonData.Add(FModalButtonInfo("[ID_POPUP_OK]"));
			SIFUIHelpers::LaunchWarningPopup("[ID_NO_AVAILABLE_KITS_CUSTOMISE]", "[ID_MAIN_MENU_HELP]", ButtonData);
		}
	}
}

void UWWUIScreenMainMenu::LinkKitOnClick()
{

}

void UWWUIScreenMainMenu::ProceedToKitCreator()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CustomiseKit);
	}
}

void UWWUIScreenMainMenu::NewSevensTeamOnClick()
{
	if (!GetInputEnabled()) { return; }

	//#if PLATFORM_WINDOWS
#ifdef ENABLE_STEAMCHECK
	int rand1 = FMath::Rand();
	int rand2 = FMath::Rand();

	if (MemCheck::instance().GetTheBallRolling(rand1, rand2) == ~(rand1 + rand2))
#endif
	{
		if (bReversionInProgress)
		{
			return;
		}

		NewTeam(true);
	}
}

void UWWUIScreenMainMenu::NewTeamOnClick()
{
	if (!GetInputEnabled()) { return; }

	//#if PLATFORM_WINDOWS
#ifdef ENABLE_STEAMCHECK
	int rand1 = FMath::Rand();
	int rand2 = FMath::Rand();

	if (MemCheck::instance().GetTheBallRolling(rand1, rand2) == ~(rand1 + rand2))
#endif
	{
		if (bReversionInProgress)
		{
			return;
		}

		NewTeam(false);
	}
}

void UWWUIScreenMainMenu::NewTeam(bool sevens)
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (!SIFGameHelpers::GAIsQuickLoadActive())
	{
		if (!SIFGameHelpers::GACheckDBTeamLimit())
		{
			//UILaunchPopUpByName("NewTeamNoSpace")
			if (m_gameInstance)
			{
				UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

				modalData->WarningDialogue = "[ID_CUSTOM_TEAM_LIMIT_POPUP_TEXT]";
				modalData->LegendString = "[ID_MAIN_MENU_HELP]";
				modalData->CloseOnBackButton = true;
				modalData->CloseOnSelectButton = true;

				TArray<FModalButtonInfo> ButtonData;

				FWWUIModalDelegate SaveDelegate;
				FWWUIModalDelegate DoNotSaveDelegate;

				//save button
				ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_POPUP_OK]"), SaveDelegate));

				modalData->ButtonData = ButtonData;

				m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
			}
			return;
		}

		SIFGameHelpers::GASetGameMode(sevens ? GAME_MODE::GAME_MODE_RU13W : GAME_MODE::GAME_MODE_RU13); // Nick  WWS 7s to Womens //
		SIFGameHelpers::GASetTeamFaceGenerationEnabled(true);

		//CustomiseTeamSquad.current_page_id = 0           requires customize page
		SIFUIHelpers::GetQueryManager()->LoadTeamData(-1);

		//< Create new default team. >
		RUDB_TEAM* db_team = SIFUIHelpers::GetQueryManager()->GetTeamData();	//deprecated function   #MB - find a way to replace

		//Give the team some default stats
		//Default strips for Agen
		int32 PrimaryStripID = 1053;
		int32 AlternateStripID = 2053;
		db_team->SetStripId(0, PrimaryStripID);
		db_team->SetStripId(1, AlternateStripID);
		db_team->SetIsCustom(true);

		SIFGameHelpers::GASetCustomTeamCinematicPrimaryStrip(PrimaryStripID);
		SIFGameHelpers::GASetCustomTeamCinematicAlternateStrip(AlternateStripID);

		if (SIFGameHelpers::GAGetGameMode() == GAME_MODE::GAME_MODE_RU13W) // Nick  WWS 7s to Womens // SEVENS)
		{
			db_team->SetR7Exclusive(true);
			db_team->SetGenderPermissionFlags(PLAYER_GENDER_FLAG_FEMALE);
		}
		else
		{
			db_team->SetR7Exclusive(false);
		}

		ProceedToTeamCreator();
	}
}

void UWWUIScreenMainMenu::ProceedToTeamCreator()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	//UIGetNode("RootTabWindow/CustomiseTeamTabWindow/Breadcrumb/Text").text_string = "[ID_CUSTOMISE_CREATE_TEAM]"
	UWWUITeamDetailsScreenData* teamDetailsInData = NewObject<UWWUITeamDetailsScreenData>();
	SIFUIHelpers::MenuEffectsStartTransition();

	if (teamDetailsInData)
	{
		teamDetailsInData->pMainMenuResetTeamData = &bTeamDetailsResetTeam;
	}

	if (m_gameInstance)
	{
		m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::TeamDetails, teamDetailsInData);
	}
}

void UWWUIScreenMainMenu::EditTeamOnClick()
{
	if (!GetInputEnabled()) { return; }

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (bReversionInProgress)
	{
		return;
	}

	if (!SIFGameHelpers::GAIsQuickLoadActive())
	{
		SIFGameHelpers::GASetTeamFaceGenerationEnabled(true);
		SIFUIHelpers::MenuEffectsStartTransition();

		if (m_gameInstance)
		{
			UWWUIStateScreenCustomiseSelectTeamData* pInData = NewObject<UWWUIStateScreenCustomiseSelectTeamData>();
			if (pInData)
			{
				pInData->IsEditSelect = true;
				pInData->BreadCrumbKey = "[ID_CUSTOMISE_EDIT_TEAM]";

				m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CustomiseSelectTeam, pInData);
			}
		}
	}
}

void UWWUIScreenMainMenu::NewPlayerOnClick()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	// --Store current player RUDB_PLAYER ID in CustomiseDetails
	// --local default_player_id = GAPEGetRUDBPlayerID()
	// --CustomisePlayerDetails.DefaultRUDBPlayerID = default_player_id

	if (GetInputEnabled())
	{
		//#if PLATFORM_WINDOWS
#ifdef ENABLE_STEAMCHECK
		int rand1 = FMath::Rand();
		int rand2 = FMath::Rand();

		if (MemCheck::instance().GetTheBallRolling(rand1, rand2) == ~(rand1 + rand2))
#endif
		{
			if (bReversionInProgress)
			{
				return;
			}

			if (!SIFGameHelpers::GAIsQuickLoadActive() && !SIFGameHelpers::GAIsHomeTeamSwitchActive())
			{
				if (!SIFGameHelpers::GACheckDBPlayerLimit())
				{
					//UILaunchPopUpByName("NewPlayerNoSpace")
					if (m_gameInstance)
					{
						UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

						modalData->WarningDialogue = "[ID_CUSTOM_PLAYER_LIMIT_POPUP_TEXT]";
						modalData->LegendString = "[ID_MAIN_MENU_HELP]";
						modalData->CloseOnBackButton = true;
						modalData->CloseOnSelectButton = true;

						TArray<FModalButtonInfo> ButtonData;

						FWWUIModalDelegate SaveDelegate;
						FWWUIModalDelegate DoNotSaveDelegate;

						//save button
						ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_POPUP_OK]"), SaveDelegate));

						modalData->ButtonData = ButtonData;

						m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
					}
					return;
				}


				SIFGameHelpers::GAPECreateNewRUDBPlayer();
				SIFAudioHelpers::PushMusic("event:/music/customise_music");

				SIFUIHelpers::MenuEffectsStartTransition();

				if (m_gameInstance)
				{
					FWWUICutsceneLoadComplete LoadCompleteDelegate = FWWUICutsceneLoadComplete::CreateUObject(this, &UWWUIScreenMainMenu::ProceedToPlayerCreator);
					m_gameInstance->DealMenuActionAfterCutsceneEvent(CSEVENT_UI_CUSTOMIZE_PLAYER_BODY, LoadCompleteDelegate, SSCutSceneManager::UI_CUTSCENE_LOAD_CUSTOMISATION_PLAYER);

					SIFUIHelpers::MenuSoundScreenTransition();
				}
			}
		}
	}
}

void UWWUIScreenMainMenu::EditPlayerOnClick()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (GetInputEnabled())
	{
		if (bReversionInProgress)
		{
			return;
		}

		if (!SIFGameHelpers::GAIsQuickLoadActive())
		{
			SIFUIHelpers::MenuEffectsStartTransition();

			if (m_gameInstance)
			{
				m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::CustomiseEditPlayer);
			}
		}
	}
}

bool UWWUIScreenMainMenu::IsEnoughOptionsForLinked(LinkCheckType linkType)
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();
	RUGameDatabaseManager* game_database_mgr = m_gameInstance->GetGameDatabaseManager();
	if (game_database_mgr)
	{
		MabVector<unsigned short> tempIDList(SIFHEAP_DYNAMIC);
		int32 tempListCount = 0;

		switch (linkType)
		{
		case LinkCheckType::Team:
		{
			if (!game_database_mgr->LoadIdListConditional< RUDB_TEAM >("custom", 1, tempIDList))
			{
				MABBREAKMSG("LoadIdListConditional failed based on the custom field!");
			}
		}
		break;
		case LinkCheckType::Player:
		{
			if (!game_database_mgr->LoadIdListConditional< RUDB_PLAYER >("custom", 1, tempIDList))
			{
				MABBREAKMSG("LoadIdListConditional failed based on the custom field!");
			}
		}
		break;
		case LinkCheckType::Kit:
		{
			if (!game_database_mgr->LoadIdListConditional< RUDB_TEAM_STRIP >("custom", 1, tempIDList))
			{
				MABBREAKMSG("LoadIdListConditional failed based on the custom field!");
			}
		}
		break;
		case LinkCheckType::Comp:
		{
			if (!game_database_mgr->LoadIdListConditional< RUDB_COMP_DEF >("custom", 1, tempIDList))
			{
				MABBREAKMSG("LoadIdListConditional failed based on the custom field!");
			}
		}
		break;
		default:
		{
			MABBREAKMSG("No link type selected!");
		}
		}

		if (tempIDList.size() > 0)
		{
			return true;
		}
	}

	return false;
}

void UWWUIScreenMainMenu::LinkPlayerOnClick()
{
	if (!GetInputEnabled()) { return; }

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		if (IsEnoughOptionsForLinked(LinkCheckType::Player))
		{
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::PlayerLink);
		}
		else
		{
			TArray<FModalButtonInfo> ButtonData;
			ButtonData.Add(FModalButtonInfo("[ID_POPUP_OK]"));
			SIFUIHelpers::LaunchWarningPopup("[ID_NO_AVAILABLE_PLAYERS_CUSTOMISE]", "[ID_MAIN_MENU_HELP]", ButtonData);
		}
	}
}

void UWWUIScreenMainMenu::LinkTeamOnClick()
{
	if (!GetInputEnabled()) { return; }

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		if (IsEnoughOptionsForLinked(LinkCheckType::Team))
		{
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::TeamLink);
		}
		else
		{
			TArray<FModalButtonInfo> ButtonData;
			ButtonData.Add(FModalButtonInfo("[ID_POPUP_OK]"));
			SIFUIHelpers::LaunchWarningPopup("[ID_NO_AVAILABLE_TEAMS_CUSTOMISE]", "[ID_MAIN_MENU_HELP]", ButtonData);
		}
	}
}

void UWWUIScreenMainMenu::LanguageOptionOnClick()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (GetInputEnabled())
	{
		if (m_gameInstance)
		{
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::LanguageSettings);
		}
	}
}

void UWWUIScreenMainMenu::ResetContextualOptionOnClick()
{
	uint32 ControllerID = 0; // #rc3_legacy  parameters.controller_id
	SIFGameHelpers::GAResetContextualHelperValues("");
	SIFGameHelpers::GASaveProfile("", ControllerID);
}

void UWWUIScreenMainMenu::ControlsSelected()
{
	//check if the PC has connected a controller or just the keyboard
	if (SIFGameHelpers::GAIsPlatformPC() && SIFGameHelpers::GAIsControllerPresent())
	{
		//ProceedToWindowForward("ControlsMappingOffensiveXbox360")					#MB - move to xbox controls
		return;
	}

	//ProceedToNextWindow(ui_object, parameters)
}

//Replaces TrainingListOnAction
bool UWWUIScreenMainMenu::TrainingListOnBack()
{
	//#dewald removed and using FOnScreenEnter callback SIFGameHelpers::GARequestCameraTransition("MainMenu");
	return true;
}

void UWWUIScreenMainMenu::TutorialsOnClickCallback()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	SIFGameHelpers::GASetRUHUDUpdaterIsTraining(true);
#if PLATFORM_XBOXONE
	/*
	if (SIFGameHelpers::GAIsPlatformXboxOne() && FString(SIFGameHelpers::GAGetActiveUserName()).IsEmpty())
	{
		return;
	}
	*/
#endif
	if (!SIFGameHelpers::GAIsQuickLoadActive())
	{
		//TrainingField.is_run_around = false;						//#MB - screen data
		//#dewald removed and using FOnScreenEnter callback SIFGameHelpers::GARequestCameraTransition("TrainingSelected");
		SIFUIHelpers::MenuEffectsStartTransition();
		if (m_gameInstance)
		{
			m_gameInstance->EnterTrainingFlow(TrainingPrompt::Tutorial);
		}
		//SIFUIHelpers::FlushMappedInputs(parameters.controller_id, "ACTION_TYPE_ACTION");     //#MB - flush input and set timer 
		SIFGameHelpers::GASetupSandbox();
		//SIFUIHelpers::SetTimer(TrainingField.MSG_DISPLAY_TUTORIAL_MENU, UIGetCurrentWindow(), 0.2)
	}
}

void UWWUIScreenMainMenu::TutorialsOnClick()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (GetInputEnabled())
	{
		//#if PLATFORM_WINDOWS
#ifdef ENABLE_STEAMCHECK
		int rand1 = FMath::Rand();
		int rand2 = FMath::Rand();

		if (MemCheck::instance().GetTheBallRolling(rand1, rand2) == ~(rand1 + rand2))
#endif
		{
			m_gameInstance->RequestTransitionStart(0.5f, FWWUIOnTransitionStartComplete::CreateUObject(this, &UWWUIScreenMainMenu::TutorialsOnClickCallback), true);
		}
	}
}

void UWWUIScreenMainMenu::RulesOnClick()
{
	if (!GetInputEnabled()) { return; }

	SIFUIHelpers::MenuEffectsStartTransition();
	//ProceedToNextWindow(){
}

void UWWUIScreenMainMenu::RunAroundOnClickCallback()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	SIFGameHelpers::GASetRUHUDUpdaterIsTraining(true);
	if (!SIFGameHelpers::GAIsQuickLoadActive())
	{
		//TrainingField.is_run_around = true													//#MB - screen data
		//#dewald removed and using FOnScreenEnter callback SIFGameHelpers::GARequestCameraTransition("TrainingRunAround");
		//ProceedToNextWindow(){
		//UIFlushMappedInputs(parameters.controller_id, "ACTION_TYPE_ACTION");
	}

	if (m_gameInstance)
	{
		m_gameInstance->EnterTrainingFlow(TrainingPrompt::RunAround);
	}
}

void UWWUIScreenMainMenu::RunAroundOnClick()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (GetInputEnabled())
	{
		m_gameInstance->RequestTransitionStart(0.5f, FWWUIOnTransitionStartComplete::CreateUObject(this, &UWWUIScreenMainMenu::RunAroundOnClickCallback), true);
	}
}
void UWWUIScreenMainMenu::CreditsOnClick()
{
	if (!GetInputEnabled()) { return; }

	SIFUIHelpers::MenuEffectsStartTransition();
	//ProceedToNextWindow(){
}

void UWWUIScreenMainMenu::OnlineLeaderboardsOnClick()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	//#MB - temporarily show leaderboards without net code
	if (GetInputEnabled())
	{
		// DH - Moved this to the ARugbyGameState::HandleMatchHasStarted() which gets triggered from the StartMatch call in ARugbyGameModeBase::StartGameFromLobby()
		if (m_gameInstance)
		{
#ifdef ENABLE_ANALYTICS
			m_gameInstance->GetPlayerAnalyticsData().StartLeaderBoardTimer();
#endif
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::Leaderboards);
		}
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenMainMenu::OnlineSwitchFriendPlayOnClick()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	//#MB - temporarily show leaderboards without net code
	if (GetInputEnabled())
	{
		// DH - Moved this to the ARugbyGameState::HandleMatchHasStarted() which gets triggered from the StartMatch call in ARugbyGameModeBase::StartGameFromLobby()
		if (m_gameInstance)
		{
			/*UWWUIModalSwitchFriendPlayData* inData = NewObject<UWWUIModalSwitchFriendPlayData>();
			if (inData)
			{
				inData->bIsFifteens = fifteens;
			}*/

			m_gameInstance->DealMenuAction(ScreenAction::SCREEN_PUSH_MODAL, Screens_UI::ModalSwitchFriendPlay); // , inData);
		}
	}
}

//Replaces MenuItemUnlockStateChanged and customiseunlockstatechanged
void UWWUIScreenMainMenu::SetMenuItemUnlockState(UWidget* menuItem, bool new_unlockable_state)
{
	menuItem->SetIsEnabled(new_unlockable_state);
}

/* replaced by above function
void UWWUIScreenMainMenu::CustomiseUnlockStateChanged(){
local node_name = "NestedListBox/EditCompetition"
local current_node = UINodeGetChild(ui_object, node_name);
UWWUIScreenMainMenu::MenuItemUnlockStateChanged(current_node, parameters);

//node_name = "NestedListBox/DeleteCompetition"
//current_node = UINodeGetChild(ui_object, node_name);
//UWWUIScreenMainMenu::MenuItemUnlockStateChanged(current_node, parameters);

if (parameters.new_unlockable_state == "locked") {
UINodeSetSelectionSuppressMessages(UINodeGetChild(ui_object, "NestedListBox"), "CreateCompetition", true)
}
}*/


//Replaces OnlineModeOnAction
bool UWWUIScreenMainMenu::OnlineModeOnBack()
{
	//Temporarily exclude switch and xbox as they dont have this function yet            #MB #rc3_legacy_xboxone #rc3_legacy_switch #rc3_legacy_matchmaking - switch and xbox match making
#if PLATFORM_PS4
	if (SIFGameHelpers::GAIsConsole())
	{
		//SIFMatchmakingHelpers::DisconnectFromMatchingServer();
		return true;
	}
#endif
	return false;
}

void UWWUIScreenMainMenu::DefaultMainMenuSelection()
{
	UWidget* defaultSelection = FindChildWidget(WWUIScreenMainMenu_UI::SingleMatch);
	if (defaultSelection)
	{
		SetFocusToWidget(defaultSelection, GetMasterPlayerController());
	}
}

void UWWUIScreenMainMenu::ConnectToOnlineServer(bool is_quick_match, bool is_ranked, int32 controller_id)
{/*  #MB #rc3_legacy_matchmaking - matchmaking code
	Message("UWWUIScreenMainMenu::ConnectToOnlineServer");
	if( (IsPS3() && ! GPS3IsProfileConnectedToPSN()) )
	{
		GPS3RaiseConnectDialog();
		else if ((IsPS4() and !GPS4IsProfileConnectedToPSN()))
		{
			UILaunchPopUpByName("MultiplayerConnectionNeeded")//GPS4RaiseConnectDialog()
		}
		else if((IsPS3() and GPS3IsContentAgeRestricted()) )
		{
		GPS3LaunchPS3Dialog("[ID_PS3?AGE_RESTRICTED_MESSAGE]", true)

		else if(((IsPS3() or IsPS4())
			and ! MMGetConnectedToMatchingServerPS3()) ){
			Message("ConnectingToOnlineService");
		UILaunchPopUpByName("ConnectingToOnlineService")
		MMConnectToMatchingServerPS3()
		UWWUIScreenMainMenu::ONLINE_GAME_NODE = ui_object
		else if( IsXbox360() and ! UWWUIScreenMainMenu::online_profiles_selected ){
		local online_enabled = CanControllerAccessOnlineMatches(UIGetCurrentMasterController())
		local is_guest = MMIsUserGuest(UIGetCurrentMasterController())
		if( online_enabled and ! is_guest ){
		UWWUIScreenMainMenu::online_profiles_selected = true
		UWWUIScreenMainMenu::ConnectToOnlineServer(UWWUIScreenMainMenu::ONLINE_GAME_NODE, parameters)
		else if( online_enabled and is_guest ){
		UILaunchPopUpByName("MultiplayerPrivilegeNeeded")
		}
		//else if( IsPC() and ! GAIsFullscreen() ){
		else if( IsPC() and ! GAIsVsyncEnabled() ){
		// No Vsync ? This is required for Online.
		UILaunchPopUpOverride("PCSystemOKPopup", "[ID_POPUP_VSYNC_REQUIRED]")
		else if( IsPC() and ! MMIsConnectedToNetwork() ){
		UILaunchPopUpByName("MultiplayerConnectionNeeded")
		else if( IsXboxOne() and ! UWWUIScreenMainMenu::online_profiles_selected ){
		local online_enabled = CanControllerAccessOnlineMatches(UIGetCurrentMasterController())
		local is_guest = MMIsUserGuest(UIGetCurrentMasterController())
		if( ! online_enabled and ! is_guest ){
		Message("Is User but ! online");

		local main_menu = UIGetNode("RootMenuWindow/MainMenu/RUMenuTemplate");
		ResetMenuPositions(main_menu)
		UILaunchPopUpByName("MultiplayerConnectionNeeded");
		else if( ! online_enabled and is_guest ){
		Message("Is Guest but ! online")
		GAShowSystemAccountPicker();
		else if( online_enabled and ! is_guest ){
		UWWUIScreenMainMenu::online_profiles_selected = true
		UWWUIScreenMainMenu::ConnectToOnlineServer(UWWUIScreenMainMenu::ONLINE_GAME_NODE, parameters)
		}
		else
		if( (UWWUIScreenMainMenu::ONLINE_ACCEPT_INVITE) ){
		MMShowInvitesPS3()
		UWWUIScreenMainMenu::ONLINE_GAME_NODE = nil
		UWWUIScreenMainMenu::ONLINE_ACCEPT_INVITE = false;

		else if((ui_object ~= nil) ){
		GASetTrainingOffscreenMarkersToLocal(false);
		ProceedToNextWindow() {
		UWWUIScreenMainMenu::ONLINE_GAME_NODE = nil
		UWWUIScreenMainMenu::ONLINE_ACCEPT_INVITE = false;

		}
	}
	*/

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (pRugbyGameInstance)
	{
		if (is_ranked)
		{
			pRugbyGameInstance->DropOutAllControllers();

			pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::OnlineSearchResults);
		}
		else
		{
			//< Launch controller drop in / drop out modal. >
			UWWUIStateScreenModalData* pModalInData = NewObject<UWWUIStateScreenModalData>();

			if (pModalInData)
			{
				// Select Delegate
				{
					if (is_quick_match)
					{
						pModalInData->OnSelectDelegate = FWWUIModalDelegate::CreateLambda([pRugbyGameInstance](APlayerController* pPlayerController)
							{
								pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::OnlineSearchResults);
								return true;
							});
					}
					else
					{
						pModalInData->OnSelectDelegate = FWWUIModalDelegate::CreateLambda([pRugbyGameInstance](APlayerController* pPlayerController)
							{
								UWWUIScreenMatchSettingsInData* inData = NewObject<UWWUIScreenMatchSettingsInData>();
								inData->GameMode = "[ID_MULTIPLAYER_PRIVATE_MATCH]";
								pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::MatchSettings, inData);
								return true;
							});
					}
				}

				// Back Delegate
				pModalInData->OnBackDelegate = FWWUIModalDelegate::CreateLambda([pRugbyGameInstance](APlayerController* pPlayerController)
					{
						pRugbyGameInstance->DealMenuAction(SCREEN_BACK, "");
						return true;
					});
			}

			pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::ModalControllerDropIn, pModalInData);
		}
	}
}


//#MB - dont think EULA codeis needed as it is on a different screen now
/*void UWWUIScreenMainMenu::EULAOnClick()
{

	UIMenuEffectsStartTransition()
	EULA.is_view = true;
	ProceedToNextWindow();
}*/

bool UWWUIScreenMainMenu::SetHelpAndOptionsHelptip()
{
	return true;
}

void UWWUIScreenMainMenu::DeleteCompetitionOnClick()
{
	if (!GetInputEnabled()) { return; }

	if (!SIFGameHelpers::GAIsQuickLoadActive())
	{
		//CustomiseCompLoad.is_delete = true			#MB #rc3_legacy - customisation screen params
		SIFUIHelpers::MenuEffectsStartTransition();
		//ProceedToNextWindow(ui_node, parameters)
	}
}

void UWWUIScreenMainMenu::DeleteTeamOnClick()
{
	if (!GetInputEnabled()) { return; }

	if (!SIFGameHelpers::GAIsQuickLoadActive())
	{
		//CustomiseTeamSelectTeam.is_delete = true		#MB #rc3_legacy - customisation screen params
		SIFUIHelpers::MenuEffectsStartTransition();
		//ProceedToNextWindow(ui_node, params)
	}
}

void UWWUIScreenMainMenu::DeletePlayerOnClick()
{
	if (!GetInputEnabled()) { return; }

	if (!SIFGameHelpers::GAIsQuickLoadActive())
	{
		//CustomiseSelectPlayer.is_delete = true		#MB #rc3_legacy - customisations screen params
		SIFUIHelpers::MenuEffectsStartTransition();
		//ProceedToNextWindow(ui_node, parameters)
	}
}

void UWWUIScreenMainMenu::ResetPlayersOnClick()
{
	if (!GetInputEnabled()) { return; }

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if ((!bReversionInProgress))
	{
		//UILaunchPopUpByName("RevertEditedPlayers")
		if (m_gameInstance)
		{
			TArray<FModalButtonInfo> ButtonData;

			FWWUIModalDelegate RevertDelegate;

			// ##################### REMOVE THIS ONCE THIS HAS BEEN IMPLEMENTED ########

			UWWUIFunctionLibrary::OnTimer(0.01f, FTimerDelegate::CreateLambda([=]() { SetInputEnabled(true); }));

			// ##################### REMOVE THIS ONCE THIS HAS BEEN IMPLEMENTED ########

			//save button
			RevertDelegate.BindUObject(this, &UWWUIScreenMainMenu::ResetPlayersConfirm);
			ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_TEST_POPUP_ACCEPT]"), RevertDelegate));

			ButtonData.Add(FModalButtonInfo("[ID_TEST_POPUP_DECLINE]"));

			SIFUIHelpers::LaunchWarningPopup("[ID_CUSTOMISE_REVERT_PLAYERS_POPUP]", "[ID_MAIN_MENU_HELP]", ButtonData, true);
		}
	}
}

void UWWUIScreenMainMenu::ResetTeamsOnClick()
{
	if (!GetInputEnabled()) { return; }

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if ((!bReversionInProgress))
	{
		//UILaunchPopUpByName("RevertEditedTeams")
		if (m_gameInstance)
		{
			TArray<FModalButtonInfo> ButtonData;

			FWWUIModalDelegate RevertDelegate = FWWUIModalDelegate::CreateUObject(this, &UWWUIScreenMainMenu::ResetTeamsConfirm);

			// ##################### REMOVE THIS ONCE THIS HAS BEEN IMPLEMENTED ########

			UWWUIFunctionLibrary::OnTimer(0.01f, FTimerDelegate::CreateLambda([=]() { SetInputEnabled(true); }));

			// ##################### REMOVE THIS ONCE THIS HAS BEEN IMPLEMENTED ########

			//save button
			ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_TEST_POPUP_ACCEPT]"), RevertDelegate));

			ButtonData.Add(FModalButtonInfo("[ID_TEST_POPUP_DECLINE]"));

			SIFUIHelpers::LaunchWarningPopup("[ID_CUSTOMISE_REVERT_TEAMS_POPUP]", "[ID_MAIN_MENU_HELP]", ButtonData, true);
		}
	}
}


bool UWWUIScreenMainMenu::ResetPlayersConfirm(APlayerController* playerController)
{
	if (SIFGameHelpers::GAIsConsole())
	{
		//Hax beceause xboxone saving is the same as pc.
		bReversionInProgress = true;
	}
	SIFGameHelpers::GARemoveAllPlayerLinks();
	SIFGameHelpers::GAResetEditedCustomData(RCDT_PLAYERS, 0);
	SIFGameHelpers::GASaveCustomDatabase();

	SIFGameHelpers::GAFaceRendererClearTextureCache();

	return true;
}


bool UWWUIScreenMainMenu::ResetTeamsConfirm(APlayerController* playerController)
{
	if (SIFGameHelpers::GAIsConsole())
	{
		//Hax beceause xboxone saving is the same as pc.
		bReversionInProgress = true;
	}
	SIFGameHelpers::GARemoveAllTeamLinks();
	SIFGameHelpers::GAResetEditedCustomData(RCDT_TEAM, 0);
	SIFGameHelpers::GASaveCustomDatabase();

	SIFGameHelpers::GAFaceRendererClearTextureCache();

	return true;
}



bool UWWUIScreenMainMenu::CanAccessFanHubArea()
{
	int masterControllerIndex = SIFUIHelpers::GetCurrentMasterControllerIndex();

#if PLATFORM_XBOXONE
	/*!SIFGameHelpers::GAIsSystemOnline())			//#MB #rc3_legacy_xboxone - network code
	{
		//Can! access FanHUB Area: Xbone System is offline
		//UILaunchPopUpByName("MultiplayerConnectionNeeded");				//#MB - launch popup
		return false;
	}*/
#endif
	if (bReversionInProgress)
	{
		//Can! access FanHUB Area: Reversion in progress;
		return false;
	}
	return true;
}

void UWWUIScreenMainMenu::ProceedToSignIn_PLAYER_UPLOADS()
{
	ProceedToSignIn(ESignInFor::PLAYER_UPLOADS);
}


void UWWUIScreenMainMenu::ProceedToSignIn_PLAYER_MOST_POPULAR()
{
	ProceedToSignIn(ESignInFor::PLAYER_MOST_POPULAR);
}

void UWWUIScreenMainMenu::ProceedToSignIn_TEAM_UPLOADS()
{
	ProceedToSignIn(ESignInFor::TEAM_UPLOADS);
}

void UWWUIScreenMainMenu::ProceedToSignIn_TEAM_MOST_POPULAR()
{
	ProceedToSignIn(ESignInFor::TEAM_MOST_POPULAR);
}

void UWWUIScreenMainMenu::MyUploadsOnClick()
{
#if WITH_EDITOR
	ProceedToSignIn_PLAYER_UPLOADS();
#else

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();


	if (UOBJ_IS_VALID(m_gameInstance) && m_gameInstance->IsAnyUserRestricted())
	{
#if !PLATFORM_XBOXONE // Don't do the cache check here on Xbox since we need to do the check to get the system UI
		m_gameInstance->ShowUGCPrivilegeError();
#else
		m_gameInstance->CheckAnyUserRestricted();
#endif
		return;
	}


	if (GetInputEnabled())
	{
		if (m_gameInstance)
		{
			m_gameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanUseUserGeneratedContent, "ProceedToSignIn_PLAYER_UPLOADS", this, false, false);
		}

		SetInputEnabled(false);
	}
#endif
}

void UWWUIScreenMainMenu::MostPopularOnClick()
{
#if WITH_EDITOR
	ProceedToSignIn_PLAYER_MOST_POPULAR();
#else

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();


	if (UOBJ_IS_VALID(m_gameInstance) && m_gameInstance->IsAnyUserRestricted())
	{
#if !PLATFORM_XBOXONE // Don't do the cache check here on Xbox since we need to do the check to get the system UI
		m_gameInstance->ShowUGCPrivilegeError();
#else
		m_gameInstance->CheckAnyUserRestricted();
#endif
		return;
	}

	if (GetInputEnabled())
	{
		if (m_gameInstance)
		{
			m_gameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanUseUserGeneratedContent, "ProceedToSignIn_PLAYER_MOST_POPULAR", this, false, false);
		}

		SetInputEnabled(false);
	}
#endif
}

void UWWUIScreenMainMenu::SearchOnClick()
{
	if (!GetInputEnabled()) { return; }

	if (!bIsSignedIn)
	{
#if defined GAMECENTRE_ENABLED && defined APPBUILD_RUGBY_CHALLENGE_3
		SIFGameHelpers::GAAsyncConnectionTest();
#endif
	}
	else
	{
		//#MB #rc3_legacy_fanhub - fanhub screen data
		//Populates a static list in GameLuavoids  used later to
		//set customisation player and download players
		//GAAsyncPopulateList(false, true, 0);
		//GASearchForPlayers("Brown");
		//ListPlayers.list_to_use = 2;
		//ProceedToWindowForward("SearchUploads");
	}

}


void UWWUIScreenMainMenu::TeamMyUploadsOnClick()
{
#if WITH_EDITOR
	ProceedToSignIn_TEAM_UPLOADS();
#else
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (UOBJ_IS_VALID(m_gameInstance) && m_gameInstance->IsAnyUserRestricted())
	{
#if !PLATFORM_XBOXONE // Don't do the cache check here on Xbox since we need to do the check to get the system UI
		m_gameInstance->ShowUGCPrivilegeError();
#else
		m_gameInstance->CheckAnyUserRestricted();
#endif
		return;
	}

	if (GetInputEnabled())
	{
		if (m_gameInstance)
		{
			m_gameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanUseUserGeneratedContent, "ProceedToSignIn_TEAM_UPLOADS", this, false, false);
		}

		SetInputEnabled(false);
	}
#endif
}

void UWWUIScreenMainMenu::TeamMostPopularOnClick()
{
#if WITH_EDITOR
	ProceedToSignIn_TEAM_MOST_POPULAR();
#else
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();


	if (UOBJ_IS_VALID(m_gameInstance) && m_gameInstance->IsAnyUserRestricted())
	{
#if !PLATFORM_XBOXONE // Don't do the cache check here on Xbox since we need to do the check to get the system UI
		m_gameInstance->ShowUGCPrivilegeError();
#else
		m_gameInstance->CheckAnyUserRestricted();
#endif
		return;
	}

	if (GetInputEnabled())
	{
		if (m_gameInstance)
		{
			m_gameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanUseUserGeneratedContent, "ProceedToSignIn_TEAM_MOST_POPULAR", this, false, false);
		}

		SetInputEnabled(false);
	}
#endif
}

void UWWUIScreenMainMenu::TeamSearchOnClick()
{
	if (!GetInputEnabled()) { return; }

	if (!bIsSignedIn)
	{
#if defined GAMECENTRE_ENABLED && defined APPBUILD_RUGBY_CHALLENGE_3
		SIFGameHelpers::GAAsyncConnectionTest();
#endif
	}
	else
	{
		//GAAsyncPopulateList(false, false, 0);
		/*											#Mb #rc3_legacy_fanhub - fanhub screen params
		ListTeams.list_to_use = 2;
		SearchUploads.is_teams_search = true;
		ProceedToWindowForward("SearchUploads");
		*/
	}
}

void UWWUIScreenMainMenu::SetOptionVisible(UUserWidget* option, bool isVisible)
{
	if (option == nullptr)
	{
		return;
	}

	option->SetIsEnabled(isVisible);

	// We'll also change up the alpha based on if( it is selectable.
	if (isVisible)
	{
		option->SetVisibility(ESlateVisibility::Visible);
	}
	else
	{
		option->SetVisibility(ESlateVisibility::Collapsed);
	}
}

void UWWUIScreenMainMenu::OnSignInPressed(APlayerController* playerController)
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (!bIsSignedIn)
	{
		ProceedToSignIn(ESignInFor::GENERIC);
	}
	else
	{
#if defined (FANHUB_ENABLED)
		if (m_gameInstance)
		{
			UWWRugbyFanHubService* FanHubService = Cast<UWWRugbyFanHubService>(m_gameInstance->GetFanHubService());
			if (FanHubService)
			{
				FanHubService->SignOut();
				bIsSignedIn = false;
				UpdateHelpText();
			}
		}
#endif
	}
}

#if PLATFORM_XBOXONE
void UWWUIScreenMainMenu::OnSwitchUser(APlayerController* playerController)
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	/*if (parameters.action_event == "ACTION_TYPE_CUSTOM" and parameters.custom_action == "RU_UI_ACTION_CAREERMODE_TEAM_AUTOFILL") then
		if (IsXboxOne()) then
			UILaunchPopUpByName("XboxOneSignOut");
		end
	end*/

	if (m_gameInstance)
	{
		UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

		modalData->WarningDialogue = "[ID_SIGN_OUT]";
		modalData->LegendString = "[ID_ASSIGN_CONTROLLER_NEUTRAL_HELP]";
		modalData->CloseOnBackButton = true;
		modalData->CloseOnSelectButton = true;

		TArray<FModalButtonInfo> ButtonData;

		FWWUIModalDelegate yesDelegate;
		FWWUIModalDelegate noDelegate;

		// Yes sign out
		yesDelegate.BindUObject(this, &UWWUIScreenMainMenu::XboxOneSignOutOnYesClicked);
		ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_YES]"), yesDelegate));

		// No don't sign out
		noDelegate.BindUObject(this, &UWWUIScreenMainMenu::ClosePopup);
		ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_NO]"), noDelegate));

		modalData->ButtonData = ButtonData;

		m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
	}
}
#endif

void UWWUIScreenMainMenu::ProceedToSignIn(ESignInFor SignInFor)
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		UWWUIScreenManager* pScreenManager = m_gameInstance->GetUIScreenManager();

		if (pScreenManager)
		{
			if (pScreenManager->FindScreenTemplate(Screens_UI::WarningMessage) != -1)
			{
				ensureMsgf(false, TEXT("This screen cancel closes a no internet connection popup when clicking My Uploads or similar. This is done because the popup was preventing the main menu from being hidden. This is a temporary fix as I do not know what the intended functionality with the popup and sign in."));

				UWWUIFunctionLibrary::OnFrameDelay(1, FTimerDelegate::CreateLambda([]()
					{
						SIFApplication::GetApplication()->DealMenuAction(SCREEN_CANCEL, Screens_UI::WarningMessage);
					}));
			}
		}

		UWWUISignInScreenData* pSignInScreenData = NewObject<UWWUISignInScreenData>();

		if (pSignInScreenData)
		{
			pSignInScreenData->SignInFor = SignInFor;
			pSignInScreenData->bAlreadySignedIn = bIsSignedIn;
			m_gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::SignIn, pSignInScreenData);
		}
	}
}

bool UWWUIScreenMainMenu::IsSignedIn()
{
	if (SIFGameHelpers::GAIsPlatformPC())
	{
		return bIsSignedIn;
	}
#if PLATFORM_PS4
	else if (SIFGameHelpers::GAIsPlatformPS4())
	{
		/*
		if (SIFGeneralHelpersPS4::IsProfileConnectedToPSN() && SIFGameHelpers::GAGetGameCentreActive())
		{
			return true;
		}
		else
		{
			return false;
		}
		*/
	}
#endif
#if PLATFORM_XBOXONE
	else if (SIFGameHelpers::GAIsPlatformXboxOne())
	{
		bool online_enabled = true; // CanControllerAccessOnlineMatches(SIFUIHelpers::GetCurrentMasterControllerIndex())		//#MB - cancontrolleraccessonlinematches is not implemented - matchmaking
		bool is_guest = false;//SIFMatchmakingHelpers::IsUserGuest(SIFUIHelpers::GetCurrentMasterControllerIndex());
		if (online_enabled && !is_guest && SIFGameHelpers::GAGetFanHubActive())
		{
			return true;
		}
		else
		{
			return false;
		}
	}
#endif
	return false;
}

void UWWUIScreenMainMenu::ProceedToServerOption()
{
	if (callback_function != nullptr)
	{
		(this->*callback_function)();
		callback_function = nullptr;
	}
}

void UWWUIScreenMainMenu::XboxOneHelpOnClick()
{
	if (!GetInputEnabled()) { return; }

#if PLATFORM_XBOXONE && defined GAMECENTRE_ENABLED && defined APPBUILD_RUGBY_CHALLENGE_3
	if (SIFGameHelpers::GAIsPlatformXboxOne())
	{
		SIFGameHelpers::GAXboxOneShowHelp();
		//else if((IsPS4()) ){
		//GPS4LaunchPS4WebDialog(UIGetCurrentMasterController(), "http://www.apache.org/");
	}
#endif
}

void UWWUIScreenMainMenu::OnlineOption()
{
	if (!GetInputEnabled()) { return; }


	//#if PLATFORM_WINDOWS
#ifdef ENABLE_STEAMCHECK
	int rand1 = FMath::Rand();
	int rand2 = FMath::Rand();

	if (MemCheck::instance().GetTheBallRolling(rand1, rand2) == ~(rand1 + rand2))
#endif
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		m_allowedToProceed = false;	// hold button press that brought us here (it hasn't happened yet)
		if (pRugbyGameInstance)
		{

#if PLATFORM_SWITCH
			pRugbyGameInstance->TryingToStartOnlineMode = true;
			UWWUIModalWaitingData* modalData = NewObject<UWWUIModalWaitingData>();
			modalData->HeaderString = "[ID_ONLINE]";
			modalData->BodyString = "[ID_ONLINE_CONNECTING_WAIT]";
			//modalData->CanCancelTime = 0.1f; // Time before ability to Cancel is disabled, 0 == no limit
			//modalData->IsLoadingIntoGame = false;

			//Gives our modal a custom tick function to call during its own tick, this allows for actions to take place while the modal is on the screen blocking the main tick
			modalData->CustomTickDelegate.BindUObject(this, &UWWUIScreenMainMenu::NexUpdate);

			pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::ModalWaiting, modalData);

			IsTryingToStartNEX = true;

			if (pRugbyGameInstance->IsPrimaryPlayerOnline())
			{
				UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::OnOnlineClicked() IsPrimaryPlayerOnline() == True"));
				NEXStartUpTimer = 25.0f;
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::OnOnlineClicked() IsPrimaryPlayerOnline() == False"));
				NEXStartUpTimer = 5.0f; //shorten cop out timer so user doesnt unneccesarily wait
			}



			pRugbyGameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanPlayOnline, "TryStartNEX", this, true, false);
			//PlatformStatusChecker->StartOnlineChecks(playerController, "TryStartNEX", this, true);
#elif PLATFORM_WINDOWS
			ActuallyGotoOnlineOption();
#else
			pRugbyGameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanPlayOnline, "ActuallyGotoOnlineOption", this, true, false);
#endif

		}
	}
}


void UWWUIScreenMainMenu::AbortStartingNEX()
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::AbortStartingNEX()"));
	pRugbyGameInstance->DealMenuAction(SCREEN_POP_MENUSCREEN, Screens_UI::ModalWaiting);
	pRugbyGameInstance->Logout_Switch();

	//This is a TRC fail, no need for this message.
/*
	if (gameInstance->IsConnected())
	{
		gameInstance->ShowErrorUIPopup("Online", "Connection failed, please try again soon.");
	}
	else
	{
		gameInstance->ShowErrorUIPopup("Online", "Connection failed, please check your internet connection.");
	}*/

	const auto OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		const auto IdentityInterface = OnlineSub->GetIdentityInterface();
		if (IdentityInterface.IsValid())
		{
			UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::AbortStartingNEX() we dont need matchmaking service as it is handled in login process"));
			//// GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL remove ERICTOTEST matchmaking interface haas changed
			//IdentityInterface->StopMatchMakingService(0);
			//IdentityInterface->ClearOnMatchingServiceLoginCompleteDelegate_Handle(0, OnNEXLoginCompleteDelegateHandle);
		}
	}

	
	CurrentNEXStartUpAttempts = 0;
	pRugbyGameInstance->TryingToStartOnlineMode = false;
	IsTryingToStartNEX = false;
}

void UWWUIScreenMainMenu::TryStartNEX()
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::TryStartNEX()"));
	CurrentNEXStartUpAttempts++;

	//If we try to do online stuff while not connected we end up in a system message loop, not very fun for the user
	if (pRugbyGameInstance->IsPrimaryPlayerOnline())
	{
		const auto OnlineSub = IOnlineSubsystem::Get();
		if (OnlineSub)
		{
			const auto IdentityInterface = OnlineSub->GetIdentityInterface();
			if (IdentityInterface.IsValid())
			{

				// if login is okay and credential type is NEX, then it should be ok now to  proceed next steps
				// if login is failed, then it should be redirected to login function
				// if login is okay but credential type is not NEX, then it should be redirected to login function

				if (IdentityInterface->GetAuthType() == TEXT("nintendo"))
				{
					UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::TryStartNEX() IdentityInterface->GetAuthType() == ELoginType::NEX"));
					if (IdentityInterface->GetLoginStatus(0) == ELoginStatus::LoggedIn)
					{
						UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::TryStartNEX() Logged in to NEX"));
						//FinishNEXStartUp(true);
						OnNexLoginCompleate(0, true, *IdentityInterface->GetUniquePlayerId(0).Get(), FString());
						return;
					}
					else
					{
						UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::TryStartNEX() user not logged in"));
					}
				}
				else
				{
					UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::TryStartNEX() IdentityInterface->GetAuthType() != ELoginType::NEX"));
					UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::TryStartNEX() GetAuthType(): %s"), *IdentityInterface->GetAuthType())
				}
				//ERICTODO redirect to login
				FinishNEXStartUp(false);

				
				
				/*
				OnNEXLoginCompleteDelegateHandle = IdentityInterface->AddOnMatchingServiceLoginCompleteDelegate_Handle(0, FOnMatchingServiceLoginCompleteDelegate::CreateUObject(this, &UWWUIScreenMainMenu::OnNexLoginCompleate));

				if (IdentityInterface->StartMatchMakingService(0))
				{
					UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::TryStartNEX() StartMatchMakingService"));
				}
				else
				{
					//We can only end up here if we are connected to the matching service, looks liek its safe to just continue on with online mode
					UE_LOG(LogTemp, Warning, TEXT("UPlatformStatusChecker::OnLoginComplete @ IdentityInterface->StartMatchMakingService(0) Failed"));
					//UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::TryStartNEX() AbortStartingNEX"));
					//AbortStartingNEX();
					FinishNEXStartUp(true);
				}*/
				//// GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL remove ERICTOTEST matchmaking interface haas changed

			}
		}
	}

}

void UWWUIScreenMainMenu::OnNexLoginCompleate(int32 LocalUserID, bool bWasSuccessful, const FUniqueNetId& UserID, const FString& Error)
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::OnNexLoginCompleate()"));
	if (CheckPendingErrorPopup())
	{
		if (bWasSuccessful)
		{
			UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::OnNexLoginCompleate() bWasSuccessful"));
			StartNexAtNextAppropriateTime = true;
			return;
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::OnNexLoginCompleate() NOT bWasSuccessful -> Show Shop UI"));
			pRugbyGameInstance->DealMenuAction(SCREEN_POP_MENUSCREEN, Screens_UI::ModalWaiting);
			PlatformStatusChecker->ShowStoreUI_Switch();
			return;
		}
	}

	FinishNEXStartUp(bWasSuccessful);
}



void UWWUIScreenMainMenu::FinishNEXStartUp(bool bWasSuccessful)
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::FinishNEXStartUp()"));
	const auto OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		const auto IdentityInterface = OnlineSub->GetIdentityInterface();
		if (IdentityInterface.IsValid())
		{
			UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::FinishNEXStartUp() we dont need to clear this delegate"));
			// we dont need this delegate anymore, so no need to clear it
			// IdentityInterface->ClearOnMatchingServiceLoginCompleteDelegate_Handle(0, OnNEXLoginCompleteDelegateHandle);  //// GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL remove ERICTOTERST matchmaking interface haas changed

		}
	}


	pRugbyGameInstance->CleanupOnlinePrivilegeTask();
	pRugbyGameInstance->DealMenuAction(SCREEN_POP_MENUSCREEN, Screens_UI::ModalWaiting);

	if (bWasSuccessful)
	{
		IsTryingToStartNEX = false;
		UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::FinishNEXStartUp() bWasSuccessful"));
		ActuallyGotoOnlineOption();
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::FinishNEXStartUp() NOT bWasSuccessful"));
		if (!PlatformStatusChecker->GetIsShopUIOpen())
		{
			UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::FinishNEXStartUp() NOT bWasSuccessful -> Show Shop UI"));

			//This should let us push the user to the online menu if they purchas the NOS upgrade.
			//This can lead to pop up spam so we will only do this once and then call PlatformStatusChecker->ResetActionData so we dont end up in a bad loop 
			if (CurrentNEXStartUpAttempts > 1)
			{
				UE_LOG(LogTemp, Warning, TEXT("UWWUIStateScreenMainMenu::FinishNEXStartUp() NOT bWasSuccessful -> Show Shop UI CurrentNEXStartUpAttempts is > 1 we will clear PlatformStatusChecker action data"));
				//Looks like this was forcing the game to loop and call TryStartNEX over and over. We will now just clear the call back and not for TryStartNEX over and over
				PlatformStatusChecker->ResetActionData("Invalid", nullptr);
				CurrentNEXStartUpAttempts = 0;
			}

			//If the shop UI hasn't been open 
			PlatformStatusChecker->ShowStoreUI_Switch();
		}
	}
}

void UWWUIScreenMainMenu::NexUpdate(const float DeltaTime)
{
	if (StartNexAtNextAppropriateTime)
	{
		//We have a pending error pop up so we need to wait for that before we can finish starting NEX and move onto the next screen
		if (!CheckPendingErrorPopup())
		{
			FinishNEXStartUp(true);
			StartNexAtNextAppropriateTime = false;
		}
	}
	else
	{
		//This will fire off if we are not waiting for user input while starting NEX
		if (IsTryingToStartNEX)
		{
			NEXStartUpTimer -= DeltaTime;
			URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
			if (NEXStartUpTimer <= 0.0f || pRugbyGameInstance->SkipSwitchConnectionFailedWaitingForNEXResult)
			{
				pRugbyGameInstance->SkipSwitchConnectionFailedWaitingForNEXResult = false;
				NEXStartUpTimer = 0.0f;
				IsTryingToStartNEX = false;

				//SRA: Taking this out as it's triggering a second, unnessecary privilage check
				/*if (CurrentNEXStartUpAttempts == 1)
				{
					UE_LOG(LogTemp, Warning, TEXT("NEXStartUpTimer is over, Auto retry just once"));
					AbortStartingNEX();
					OnlineOption();
				}
				else*/
				{
					UE_LOG(LogTemp, Warning, TEXT("NEXStartUpTimer is over"));
					AbortStartingNEX();
				}
			}
		}
	}
}


// returns true if there is a pending ErrorPop that still needs to be dealt with
// returns false if there is no pending ErrorPop or the the ErrorPopup has been pushed
bool UWWUIScreenMainMenu::CheckPendingErrorPopup()
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (UOBJ_IS_VALID(pRugbyGameInstance))
	{
		if (pRugbyGameInstance->IsErrorPopupPending())
		{
			// If error still pending we need to keep checking
			if (pRugbyGameInstance->IsErrorPopupPending())
			{
				return true;
			}

		}
	}
	return false;
}

void UWWUIScreenMainMenu::ActuallyGotoFanhubMenu()
{
	UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMainMenu::ActuallyGotoFanhubMenu"));
	ActuallyGotoOnlineOption();
}

void UWWUIScreenMainMenu::ActuallyGotoOnlineOption()
{
	MABLOGDEBUG("UWWUIScreenMainMenu::OnlineOption");

	// this is a success-only callback as I understand it
	m_allowedToProceed = true;	// release button press actions
	// go to the online sub menu

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	pRugbyGameInstance->DealMenuAction(SCREEN_CANCEL, Screens_UI::ModalWaiting);

	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->SetInOnlineArea(true);
		pRugbyGameInstance->GetMatchmakingManager()->TryedSessions.Empty();

		if (pRugbyGameInstance->GetConnectionManager())
		{
			CheckLiveDataVersionHandle = pRugbyGameInstance->GetConnectionManager()->NetworkConnected.AddUFunction(this, FName("CheckChampionDataVersion"));
			pRugbyGameInstance->GetConnectionManager()->StartCheckingConnectionImmediately();
		}
	}

#if !PLATFORM_WINDOWS
	ButtonPressed();
#endif

#if PLATFORM_XBOXONE && defined (GAMECENTRE_ENABLED) && defined APPBUILD_RUGBY_CHALLENGE_3
	if (SIFGameHelpers::GAIsPlatformXboxOne())
	{
		bool online_enabled = true; // CanControllerAccessOnlineMatches(SIFUIHelpers::GetCurrentMasterControllerIndex())   #MB- matchmaking function
		bool is_guest = SIFMatchmakingHelpers::IsUserGuest(SIFUIHelpers::GetCurrentMasterControllerIndex());
		if (!SIFGameHelpers::GAIsSystemOnline())
		{
			//UILaunchPopUpByName("MultiplayerConnectionNeeded")			#MB - launch popup
		}
		else if (!online_enabled && !is_guest)
		{
			//Message("Is User but ! online")
			//UILaunchPopUpByName("MultiplayerConnectionNeeded")			#MB - launch popup
		}
		else if (!online_enabled && is_guest)
		{
			//Message("Is Guest but ! online")
			SIFGameHelpers::GAShowSystemAccountPicker();
		}
	}
#endif
}

bool UWWUIScreenMainMenu::XboxOneSignOutOnYesClicked(APlayerController* playerController)
{
#if PLATFORM_XBOXONE
	SIFGameHelpers::GAXboxOneUserLost(true);
#endif
	return true;
}

void UWWUIScreenMainMenu::NetworkDisconnectionDetected()
{
	//Message("Launching network disconnected.")
	/*															##MB - popup check and close
	if(!UIIsPopupActive("MultiplayerConnectionNeeded"))
	{
		UILaunchPopUpByName("MultiplayerConnectionNeeded");
	}
	*/
}