// Copyright (c) 2016-2017 Wicked Witch Software Pty. Ltd.

#pragma once

#include "UObject/ObjectMacros.h"

//===============================================================================
//===============================================================================
UENUM(BlueprintType)
enum class ERugbyAnim_Request : uint8
{
	None = 0,

	Kick_Kickoff		UMETA(DisplayName = "penalty_kick_kick"), //"kickoff"),
	Kick_LongPunt		UMETA(DisplayName = "long_punt_kick"),
	Kick_Drop			UMETA(DisplayName = "drop_kick"),
	Kick_Grubber		UMETA(DisplayName = "grubber_kick"),
	Kick_Chip			UMETA(DisplayName = "chip_kick"),
	Kick_Box			UMETA(DisplayName = "box_kick"),
	Kick_CentreDrop		UMETA(DisplayName = "center_drop_kick"),
	Kick_QuickPenalty	UMETA(DisplayName = "quick_penalty_kick"),
	Kick_Penalty		UMETA(DisplayName = "penalty_kick_kick"),
};

//===============================================================================
//===============================================================================
UENUM(BlueprintType)
enum class ERugbyAnim_MachineMode : uint8
{
	Cinematics,
	// main: player_cinematicRef|cinematic|FeatherBlend2_1
	// Cinematic and FacialAnims are completely exclusive

	FacialAnims,
	// main:  player_cinematicRef|null|facial_anims|BlendN1
	// entry: player_cinematicRef|null|FeatherBlend2_1
	// null:  always active
	//
	// apply Tackles to all bones
	// if facial_anims_enabled:
	//   apply self to face boness

	Tackles,
	// main:  player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles
	// entry: player_cinematicRef|null|player_tacklesRef|tackle_blend|FeatherBlend2_2
	// null:  player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|null|PassThrough1
	//
	// if null:
	//   apply FullBodyPassing to all bones
	// otherwise:
	//   apply self to all bones
	//   if upper_body_override:
	//	   apply FullBodyPassing to upperbody bones

	FullBodyPasses,
	// main:  player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing
	// entry: same as main (do not blend)
	// null:  player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null|PassThrough1
	//
	// if null:
	//   apply UpperBodyActions to all bones
	// otherwise:
	//	apply self to all bones

	UpperBodyActions,
	// main:  player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions
	// entry: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|FeatherBlend2_1
	// null:  player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null|PassThrough1
	//
	// if null:
	//   apply FullBodyActions to all bones
	// otherwise:
	//   apply FullBodyActions to all bones
	//   apply self to upperbody bones

	FullBodyActions,
	// main:  player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions
	// entry: same as main (do not blend)
	// null:  player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null|PassThrough1
	//
	// if null:
	//   apply Movement to all bones
	// otherwise:
	//   apply self to all bones

	Movement,
	// main:  player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|player_movementRef|Blend2MatchEvents2
	// entry: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|player_movementRef|Blend2MatchEvents4
	// null:  N/A

	DynamicMovement,
	// player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement
	// player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement

	GenericMontage,

	MAX
};

//===============================================================================
//===============================================================================
UENUM(BlueprintType)
enum class ERugbyAnim_Mode_Tackles : uint8
{
	null						UMETA(DisplayName = "null"), //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|null|PassThrough1
	ankle_tap_tacklee			UMETA(DisplayName = "ankle_tap_tacklee"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|ankle_tap_tacklee|tackle"),
	ankle_tap_tackler			UMETA(DisplayName = "ankle_tap_tackler"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|ankle_tap_tackler|tackle"),
	contested_tacklee			UMETA(DisplayName = "contested_tacklee"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee|tackle"),
	contested_tackler			UMETA(DisplayName = "contested_tackler"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle"),
	fend_fail_tacklee			UMETA(DisplayName = "fend_fail_tacklee"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_fail_tacklee|tackle"),
	fend_fail_tackler			UMETA(DisplayName = "fend_fail_tackler"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_fail_tackler|tackle"),
	fend_success_tacklee		UMETA(DisplayName = "fend_success_tacklee"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_success_tacklee|tackle"),
	fend_success_tackler		UMETA(DisplayName = "fend_success_tackler"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_success_tackler|tackle"),
	head_high_tacklee			UMETA(DisplayName = "head_high_tacklee"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|head_high_tacklee|tackle"),
	head_high_tackler			UMETA(DisplayName = "head_high_tackler"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|head_high_tackler|tackle"),
	sidestep_fail_tacklee		UMETA(DisplayName = "sidestep_fail_tacklee"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|tackle"),
	sidestep_fail_tackler		UMETA(DisplayName = "sidestep_fail_tackler"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tackler|tackle"),
	sidestep_success_tacklee	UMETA(DisplayName = "sidestep_success_tacklee"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_success_tacklee|tackle"),
	sidestep_success_tackler	UMETA(DisplayName = "sidestep_success_tackler"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_success_tackler|tackle"),
	standard_fail_tacklee		UMETA(DisplayName = "standard_fail_tacklee"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_fail_tacklee|tackle"),
	standard_fail_tackler		UMETA(DisplayName = "standard_fail_tackler"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_fail_tackler|tackle"),
	standard_success_tacklee	UMETA(DisplayName = "standard_success_tacklee"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tacklee|tackle"),
	standard_success_tackler	UMETA(DisplayName = "standard_success_tackler"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tackler|tackle"),

	standard_fail_two_tacklee		UMETA(DisplayName = "standard_fail_two_tacklee"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_fail_two_tacklee|tackle"),
	standard_fail_two_tackler		UMETA(DisplayName = "standard_fail_two_tackler"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_fail_two_tackler|tackle"),
	standard_success_two_tacklee	UMETA(DisplayName = "standard_success_two_tacklee"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_two_tacklee|tackle"),
	standard_success_two_tackler	UMETA(DisplayName = "standard_success_two_tackler"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_two_tackler|tackle"),

	try_pushed_tacklee			UMETA(DisplayName = "try_pushed_tacklee"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|try_pushed_tacklee|tackle"),
	try_pushed_tackler			UMETA(DisplayName = "try_pushed_tackler"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|try_pushed_tackler|tackle"),
	dive_miss_tackler			UMETA(DisplayName = "dive_miss_tackler"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|dive_miss_tackler|tackle"),
	fast_getup_tackler			UMETA(DisplayName = "fast_getup_tackler"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fast_getup_tackler|ClosestAnim1"),
	Getup						UMETA(DisplayName = "getup"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|getup"),
	Getup_injury				UMETA(DisplayName = "getup_injury"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|getup"),
	play_the_ball_ground		UMETA(DisplayName = "play_the_ball_ground"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|play_the_ball_ground"),
	quick_tap					UMETA(DisplayName = "quick_tap"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|quick_tap|ClosestAnim1"),
	Try							UMETA(DisplayName = "Try"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|try|tackle")
	try_corner					UMETA(DisplayName = "try_corner"), //"player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|try_corner|tackle"),
	MAX
};

//===============================================================================
//===============================================================================
UENUM(BlueprintType)
enum class ERugbyAnim_Mode_UBActionsFBPass : uint8
{	
	NullPassThrough,			//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null|PassThrough1"),
	emotional_reactions,	//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|emotional_reactions"),
	lineout_throw,		//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|lineout_throw"),
	long_pass_moving,		//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_moving"),
	long_pass_standing,	//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_standing"),
	medium_pass,			//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass"),
	play_the_ball,
	MAX
};

//===============================================================================
//===============================================================================

UENUM(BlueprintType)
enum class ERugbyAnim_Mode_UBActions : uint8
{
	//----------------------------------------------------------------------------------------------------------------------------------------------------------------------
	NullPassThrough,		//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null|PassThrough1"),
	Catches,			//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|catches|ContactAnim1"), //this is handled separately
	dummy_passes,		//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes"),
	fumbled_catches,		//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|fumbled_catches|ContactAnim1"), //this is handled separately
	passes,				//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|passes"),
	MAX
};

//===============================================================================
//===============================================================================
UENUM(BlueprintType)
enum class ERugbyAnim_Mode_FullBodyActions : uint8
{
	PlayMontage,			// "if no montage is playing, will just be passthrough
	Null,					//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null|PassThrough1"),
	ContactActions,			//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions"),
	DummyHalf,				//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half"),
	DynamicMoveNoBall,		//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement"),
	DynamicMoveWithBall,	//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement"),
	FeedBall,				//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|feed_ball|wbfeedball01"),
	GetupCelebration,		//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|getup_celebration|ClosestAnim1"),
	GetupNoBall,			//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|noball_getup|ClosestAnim1"),
	GetupWithBall,			//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|withball_getup|ClosestAnim1"),
	KickLeftFoot,			//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot"),
	KickRightFoot,			//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot"),
	Lineout,				//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out"),
	Maul,					//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|maul|maul"),
	NumberEightPickup,		//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|num_eight_pickup|PlaySpeedModifier1"),
	QuickLineout,			//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|quick_lineout|wbqlselfpass03"),
	RefRaiseFlag,			//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|ref_raise_flag|refraiseflag01"),
	Ruck,					//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|ruck|ruck"),
	Scrum,					//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|scrum"),
	Sidestep,				//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|side_step"),
	StayOnGround,			//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|stay_on_ground|wbogs01"),
	Try,					//UMETA(DisplayName = "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|try|Try1"),
	MAX
};

//===============================================================================
//===============================================================================
UENUM(BlueprintType)
enum struct CONTACT_TYPE : uint8
{
	GET_THE_BALL = 0,
	CATCH,
	MISS,		// not explicitly selected, activated based on morpheme graph state.
	FUMBLE,		// not explicitly selected, activated based on morpheme graph state.
	MAX
};

//===============================================================================
//===============================================================================
UENUM(BlueprintType)
enum class CLOSEST_ANIM_TYPE : uint8
{
	GETUP_NOBALL = 0
	,GETUP_WITHBALL
	,GETUP_CELEBRATION_NOBALL
	,GETUP_FAST_NOBALL
	,GETUP_TACKLER_NOBALL
	,GETUP_TACKLER_WITHBALL
	,GETUP_TACKLEE_NOBALL
	,GETUP_TACKLEE_WITHBALL
	,GETUP_QUICKTAP_WITHBALL
	,GETUP_TACKLER_NOBALL_INJURED
	,GETUP_TACKLER_WITHBALL_INJURED
	,GETUP_TACKLEE_NOBALL_INJURED
	,GETUP_TACKLEE_WITHBALL_INJURED
	,MAX
};


//===============================================================================
//===============================================================================
UENUM(BlueprintType)
enum struct RUCKMAULSCRUM_TYPE : uint8
{
	Null = 0,
	RUCK = 1,
	MAUL,
	SCRUM,	
	MAX
};

//===============================================================================
//===============================================================================
UENUM(BlueprintType)
enum struct IMPACT_TYPE : uint8 //used in ruck and tackles. These enums are tied up to datatables.
{
	Null = 0,
	BEHIND = 1,
	FRONT = 2,		
	FUMBLE = 3,		
	LEFT = 4,
	RIGHT = 5,
	BEHIND1 = 6, 
	BEHIND2 = 7, 
	FRONT1 = 8,	 
	LEFT1 = 9,   
	RIGHT1 = 10,  
	FRONT2 = 11,  
	FRONT3 = 12,
	MAX
};

//===============================================================================
//===============================================================================
UENUM(BlueprintType)
enum struct NODE_TYPE : uint8
{
	None = 0,
	Animation,
	ApplyGlobalTime,
	BallPosition,
	Blend2,
	BlendN,
	Cinematic,
	ClosestAnim,
	ContactAnim,
	FeatherBlend2,
	Filter,
	HeadLookIk,
	Idle,
	JoinRuck,
	Mirror,
	Movement,
	PassThrough,
	PhaseOffset,
	Physics,
	PlaySpeedModifier,
	Select,
	SingleFrame,
	StateMachine,
	Switch,
	Tackle,
	Transition,
	TryNode,
	TryTackle,
	TwoBoneIk,
	MAX
};


//===============================================================================
// NOTE: The lack of the struct qualifier allows for accessing arrays using these 
// enums directly and for this to not be forced into using unsigned values however 
// it means that any other UENUM definitions that use any of the  value names in 
// it will cause a compile time error from Unreal due to a name collision.
//===============================================================================
UENUM(BlueprintType)
enum GTB_ACTION
{
	INVALID = -1,
	KICK = 0,
	DIVE,
	PICKUP,
	HIGHBALL_CATCH,
	HIGHBALL_JUMP,
	CATCH,
	CRADLE_DIVE,
	MAX
};

//===============================================================================
//ruck enums to maintain it's states
//===============================================================================
UENUM(BlueprintType)
enum class RuckAnimationState : uint8
{
	InvalidState = 0,
	looseRuck = 1, //ControlParameters|ruck_loose
	joinRuck = 2,	//start of the stateMachine	
	configurationARuck = 3, //ControlParameters|ruck_position
	configurationBRuck = 4, //ControlParameters|ruck_position
	disengageRuckA = 5, //OnRequest "disengage_ruck" //end of the statemachine.
	disengageRuckB = 6, //OnRequest "disengage_ruck" //end of the statemachine.
	RuckContestTackle = 7, //OnRequest == "contest_tackle"
	RuckContestTackleFail = 8, //OnRequest == "contest_tackle_fail"
	RuckContestTackleSuccess = 9, //OnRequest == "contest_tackle_success"
};

//Not used.
UENUM(BlueprintType)
enum class RuckConfigTyp : uint8
{
	eIdle,
	eImpact
};

//===============================================================================
//Maul enums to maintain it's states
//===============================================================================
UENUM(BlueprintType)
enum class MaulAnimationState : uint8
{
	InvalidState,
	JoinMaul = 1,	//start of the stateMachine
	InMaul,
	LeaveMaul,  //end
};


//===============================================================================
//scrum enums to maintain it's states
//===============================================================================
UENUM(BlueprintType)
enum class ScrumAnimationState : uint8
{
	InvalidState,	
	BOUND = 1,		
	CROUCH = 2,//request
	TOUCH = 3,//request
	PAUSED = 4,	
	ENGAGE = 5,//request
	UNBIND = 6,//request
	ENGAGED = 7,
	COLLAPSE = 9,//request		 
	BIND = 10,//request
	DONE,
};


//===============================================================================
//Contested Tackle (Tackler and Tacklee) enums to maintain it's states
//===============================================================================
UENUM(BlueprintType)
	enum class EContestedTackleAnimationState : uint8 //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle|tackler_driven_impact|
{
	InvalidState,
	tackle_driven_impact,	  //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle|tackler_driven_impact
	tackle_driven_engaged,    //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle|tackler_driven_engaged
	tackle_driven_takedown,   //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle|tackler_driven_takedown
	tackle_driven_breakout,   //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle|tackler_driven_breakout
	ground                    //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle|ground		
};

//===============================================================================
//Copied from RC3
//===============================================================================
UENUM(BlueprintType)
enum class DeltaTrajectorySource : uint8
{
	DELTA_TRAJECTORY_NULL,
	DELTA_TRAJECTORY_DESTINATION,
	DELTA_TRAJECTORY_SOURCE,
	DELTA_TRAJECTORY_BLEND
};

//===============================================================================

UENUM(BlueprintType)
enum class UpperBodyActionFBPassSubTypes : uint8
{
	Null,
	medium_pass_left,	
	medium_pass_forward,
	medium_pass_right,	
	medium_pass_r45,
	medium_pass_r135,
	medium_pass_behind,
	medium_pass_l45,
	medium_pass_l135,

	long_pass_left,
	long_pass_forward,
	long_pass_right,
	long_pass_r45,
	long_pass_r135,
	long_pass_behind,
	long_pass_l45,
	long_pass_l135,			

	celebration_standing,
	commiseration_standing,
	celebration_jogging,
	commiseration_jogging,
	celebration_standing_withball,

	ready_to_throw,
	throw_normal,
	raise_to_throw,	

	play_the_ball_behind,
	play_the_ball_ground,
};

UENUM(BlueprintType)
enum class UpperBodyActionUBPassSubTypes : uint8
{
	Null,
	forward,
	left,
	right,
	behind,
	l135,
	l45,
	r135,
	r45,
	high_left,
	high_right,
	leftarm_behind,
	leftarm_forward,
	leftarm_left,
	leftarm_right,
	rightarm_behind,
	rightarm_forward,
	rightarm_left,
	rightarm_right,
	offload_right
};

UENUM(BlueprintType)
enum class UpperBodyPassType : uint8
{
	Null,
	long_pass_standing,
	long_pass,
	medium_pass,
	offload_null,
	offload_falling,
	short_pass,
	MAX
};

//UENUM(BlueprintType)
//enum class IsGroundType : uint8
//{
//	Null,
//	True_Valid, 
//	True_InValid //not used. These are the last frame of valid animation which blends to ground animation...
//};

UENUM(BlueprintType)
enum class TryGroupType : uint8
{
	no_pressure UMETA(DisplayName = "no_pressure"), 
	little_pressure UMETA(DisplayName = "little_pressure"),
	high_pressure UMETA(DisplayName = "high_pressure"),
	near_out_left_side UMETA(DisplayName = "near_out_left_side"),
	near_out_right_side UMETA(DisplayName = "near_out_right_side"),
	force_ball_own_goal UMETA(DisplayName = "force_ball_own_goal"),
	MAX
};


//UENUM(BlueprintType)
//enum class contactVectorNodeTypes : uint8
//{
//	ContactAnim,
//	JoinRuck,
//	Tackle,
//	Try,
//	TryTackle
//};

UENUM(BlueprintType)
enum class FullBodySubType : uint8
{
	Null = 0,
	PENALTY_KICK,
	PLACED_KICK
};

UENUM(BlueprintType)
enum struct wwDB_IDLE_ENUM : uint8
{
	NONE = 0,							//UMETA(DisplayName = ""),
	idle_none,							//UMETA(DisplayName = "NONE"),
	idle_ready_engage,					//UMETA(DisplayName = "READY_ENGAGE"),
	idle_inplay,						//UMETA(DisplayName = "AMBIENT_IN_PLAY"),
	idle_ambient_stoppage,				//UMETA(DisplayName = "AMBIENT_STOPPAGE"),
	idle_dropout_kicker,				//UMETA(DisplayName = "DROPOUT_KICKER"),
	idle_lineout_right,					//UMETA(DisplayName = "LINEOUT"),
	idle_lineout_left,					//UMETA(DisplayName = "LINEOUT"),
	idle_defensive,						//UMETA(DisplayName = "DEFENSIVE"),
	normal_idle,						//UMETA(DisplayName = "NORMAL_IDLE"),
	idle_dummy_half,
	MAX
};

UENUM(BlueprintType)
enum CINEMATIC_MOOD
{
	MOOD_COMMISERATE = 0	UMETA(DisplayName = "COMMISERATE"),
	MOOD_LOW_COMMISERATE	UMETA(DisplayName = "LOW_COMMISERATE"),
	MOOD_NEUTRAL			UMETA(DisplayName = "NEUTRAL"),
	MOOD_LOW_HAPPY			UMETA(DisplayName = "LOW_HAPPY"),
	MOOD_HAPPY				UMETA(DisplayName = "HAPPY"),
	MOOD_NEUTRAL_NO_TALK	UMETA(DisplayName = "NEUTRAL_NO_TALK"),
	MOOD_UNDEFINED			UMETA(DisplayName = "UNDEFINED_MOOD"),
};


UENUM(BlueprintType)
enum class TRY_TACKLE_TYPE : uint8
{
	TTT_SUCCESS_LIKELY = 0,		// Video ref - 80% chance looks like try, 20% chance not
	TTT_SUCCESS_MARGINAL,	// Video ref - 50% chance looks like try, 50% chance not
	TTT_FAIL_LIKELY,		// Video ref - 20% chance looks like try, 80% chance not
	TTT_HELDUP,				// Video ref - try is held up
	TTT_PUSHBACK,			// Ball holder is pushed back by defender around tryline area
	TTT_JUMPOVER, 			// Jumping over the try scorer when it is too late
	TTT_UNKNOWN
};

/// Tackle sub contexts
UENUM(BlueprintType)
enum class TACKLE_DOMINANCE : uint8
{
	TDOM_ATTACK2,		// Tacklee/attacker very dominant in tackle - makes alot of progress
	TDOM_ATTACK1, 		// Tacklee/attacker reasonably dominant in tackle - makes a bit of progress
	TDOM_ATTACK_EQ,		// Tacklee/Tackler have roughly equal impetus in tackle - tackle ends mostly on the spot
	TDOM_DEFENCE1,		// Tackler is reasonably dominant in the tackle - drives tacklee/attacker back a bit
	TDOM_DEFENCE2,		// Tackler is very dominant in the tackle, drives the tacklee/tackler back quite a bit
	TDOM_UNKNOWN
};

UENUM(BlueprintType)
enum class TACKLE_BODY_POSITION : uint8
{
	TBP_ANKLE,			// Tackled around Ankle
	TBP_WAIST,			// Tackled around Waist
	TBP_CHEST,			// Tackled around Chest
	TBP_HEAD,			// Tackled around Head	
	TBP_UNKNOWN			// Unknown Body Position
};

UENUM(BlueprintType)
enum class EMontageCrossDurationData : uint8
{
	eInvalidState,
	eMontageIsNull,	
	eMontageIsNotFound,	
	eMontageHasStopped,
	eMontageCrossedDuration,
	eMontageIsPlaying,
};

