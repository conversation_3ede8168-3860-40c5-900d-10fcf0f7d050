/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef RUDATABASETYPES_H
#define RUDATABASETYPES_H

#include "Mab/Types/MabRuntimeType.h"

/**
* RUDatabaseTypes.h
*
* All the structs representing objects from the RUDatabase
* Such as players and teams.
*
* <AUTHOR>
*/

#define PLAYER_NUM_FITNESS_CATEGORIES	(16)
#define PLAYER_POSITION_CATEGORY_SIZE	(6)

enum FIELD_SIGNAGE
{
	FIELD_STANDARD,
	FIELD_TRANS_TASMAN,
	FIELD_STATE_OF_ORIGIN,
	FIELD_CITY_VS_COUNTRY,
	FIELD_SUPERLEAGUE,
	FIELD_SUPERLEAGUE_GRAND_FINAL,
	FIELD_WORLD_CUP,
	FIELD_WORLD_CLUB,
	FIELD_CHAMPIONSHIP_COMP,
	FIELD_CHAMPIONSHIP_COMP1,
	FIELD_NRL_FINAL_SERIES,
	FIELD_NRL_GRAND_FINAL,
	FIELD_NRL_PREMIERSHIP_FRI_SAT,
	FIELD_NRL_PREMIERSHIP_SUN,
	FIELD_NRL_PREMIERSHIP_MON,
	FIELD_TOYOTA_FINAL_SERIES,
	FIELD_TOYOTA_GRAND_FINAL,
	FIELD_TOYOTA_FRI_SAT,
	FIELD_TOYOTA_SUN,
	FIELD_TOYOTA_MON,
	FIELD_TOYOTA,
	FIELD_NRL_PREMIERSHIP				// Not a valid field signage, used by database to tell the game to change it to PREMIERSHIP_FRI_SAT, PREMIERSHIP_SUN or PREMIERSHIP_MON
};
RUNTIMETYPE_FROM_ALIAS(FIELD_SIGNAGE, int);

/**
// Game types.
*/
enum GAME_TYPE
{
	GAME_TRAINING,
	GAME_MENU,
	GAME_STANDARD,
	GAME_FINAL,
	GAME_NRL_PRELIMINARY_FINAL,
	GAME_NRL_QUARTER_FINAL,
	GAME_NRL_SEMI_FINAL,
	GAME_NRL_GRAND_FINAL,
	GAME_SL_ELIMINATION_PLAYOFF,
	GAME_SL_QUALIFYING_SEMI_FINAL,
	GAME_SL_ELIMINATION_SEMI_FINAL,
	GAME_SL_FINAL_ELIMINATOR,
	GAME_SL_GRAND_FINAL,
	GAME_WORLD_CLUB_CHALLENGE,
	GAME_STATE_OF_ORIGIN,
	GAME_TRANS_TASMAN,
	GAME_TRI_SERIES,
	GAME_INTERNATIONAL,
	GAME_WORLD_CUP
};
RUNTIMETYPE_FROM_ALIAS(GAME_TYPE, int);

/**
// Game Modes.
*/
enum GAME_MODE
{
	GAME_MODE_RU13,
	GAME_MODE_RU13W,		// WJS RLC TODO REMOVE No Sevens games Refer Cole March 2025
	GAME_MODE_MAX
};
RUNTIMETYPE_FROM_ALIAS(GAME_MODE, int);

/**
// Lighting conditions.
*/
enum LIGHTING_CONDITIONS
{
	LIGHTING_CONDITIONS_CLEAR,
	LIGHTING_CONDITIONS_OVERCAST,
	LIGHTING_CONDITIONS_NIGHT,
	LIGHTING_MAX
};
RUNTIMETYPE_FROM_ALIAS(LIGHTING_CONDITIONS, int);

enum TIMEOFDAY
{
	TIME_DAY,
	TIME_EVENING,
	TIME_NIGHT,
	TIME_MAX
};
RUNTIMETYPE_FROM_ALIAS(TIMEOFDAY, int);

enum CROWD_SIZE
{
	CROWD_SIZE_NONE,
	CROWD_SIZE_VERY_SMALL,
	CROWD_SIZE_SMALL,
	CROWD_SIZE_MEDIUM,
	CROWD_SIZE_GOOD,
	CROWD_SIZE_CAPACITY
};
RUNTIMETYPE_FROM_ALIAS(CROWD_SIZE, int);

/// Difficulties
enum DIFFICULTY
{
	DIF_VERYEASY,
	DIF_EASY,
	DIF_NORMAL,
	DIF_HARD,
	DIF_PRO,
	DIF_MAX // Sentinel value
};
RUNTIMETYPE_FROM_ALIAS(DIFFICULTY, int);

/// Law Variations
enum GAME_LAW
{
	GAME_LAW_NORMAL,	// Normal IRB laws
	GAME_LAW_NRC,		// NRC Specific laws
	GAME_LAW_MAX
};
RUNTIMETYPE_FROM_ALIAS(GAME_LAW, int);

/// Law Variations
enum BALANCED_TEAM
{
	BALANCED_TEAM_OFF,
	BALANCED_TEAM_ON
};
RUNTIMETYPE_FROM_ALIAS(BALANCED_TEAM, int);

/// Substitution
enum SUBSTITUTION_MODE
{
	SUB_AUTOMATIC,
	SUB_MANUAL
};
RUNTIMETYPE_FROM_ALIAS(SUBSTITUTION_MODE, int);

/// Match Pace
enum MATCH_PACE
{
	MATCH_PACE_NORMAL,
	MATCH_PACE_FAST
};

enum PLAYER_FRUSTRATION_LEVEL
{
	PFL_NONE = 0,
	PFL_LOW = 1,
	PFL_MEDIUM = 2,
	PFL_FRUSTRATED = 3,
	PFL_LAST_CHANCE = 6
};

// Option identifying different body types, primarily to separate
// the masculine body shape from the feminine.
enum PLAYER_BODY_TYPE
{
	PLAYER_BODY_TYPE_MASCULINE_ADULT = 0,
	PLAYER_BODY_TYPE_FEMININE_ADULT,

	NUM_PLAYER_BODY_TYPES
};

// Separate gender option, aspiring for some separation between gender and 
// physical appearance in the hope that we can remove/lessen the connection later on.
enum PLAYER_GENDER
{
	PLAYER_GENDER_MALE = 0,
	PLAYER_GENDER_FEMALE,

	NUM_PLAYER_GENDER_OPTIONS
};

// Flag version of gender, for testing against masks (such as team permissions)
#define PLAYER_GENDER_AS_FLAG(gender)	(unsigned char)(1 << gender)
enum
{
	PLAYER_GENDER_FLAG_MALE = PLAYER_GENDER_AS_FLAG(PLAYER_GENDER_MALE),
	PLAYER_GENDER_FLAG_FEMALE = PLAYER_GENDER_AS_FLAG(PLAYER_GENDER_FEMALE)
};

#endif	// RUDATABASETYPES_H

