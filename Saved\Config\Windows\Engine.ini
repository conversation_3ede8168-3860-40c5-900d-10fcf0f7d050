[Core.System]
Paths=../../../Engine/Content
Paths=%GAMEDIR%Content
Paths=../../../Engine/Plugins/MorphToolsPlugin/Content
Paths=../../../Engine/Plugins/RMAMirrorAnimation/Content
Paths=../../../Engine/Plugins/wwUITool/Content
Paths=../../../Engine/Plugins/Developer/AnimationSharing/Content
Paths=../../../Engine/Plugins/Editor/GeometryMode/Content
Paths=../../../Engine/Plugins/FX/Niagara/Content
Paths=../../../Engine/Plugins/Experimental/ChaosSolverPlugin/Content
Paths=../../../Engine/Plugins/Experimental/ChaosNiagara/Content
Paths=../../../Engine/Plugins/Experimental/ChaosClothEditor/Content
Paths=../../../Engine/Plugins/Experimental/GeometryCollectionPlugin/Content
Paths=../../../Engine/Plugins/Experimental/MotoSynth/Content
Paths=../../../Engine/Plugins/Experimental/GeometryProcessing/Content
Paths=../../../Engine/Plugins/Experimental/PythonScriptPlugin/Content
Paths=../../../Engine/Plugins/Lumin/MagicLeapPassableWorld/Content
Paths=../../../Engine/Plugins/VirtualProduction/Takes/Content
Paths=../../../Engine/Plugins/Runtime/AudioSynesthesia/Content
Paths=../../../Engine/Plugins/Runtime/OpenXR/Content
Paths=../../../Engine/Plugins/Runtime/OpenXRHandTracking/Content
Paths=../../../Engine/Plugins/Runtime/OpenXREyeTracker/Content
Paths=../../../Engine/Plugins/Runtime/Synthesis/Content
Paths=../../../Engine/Plugins/Runtime/PostSplashScreen/Content
Paths=../../../../NRL/Plugins/FMODStudio/Content
Paths=../../../../NRL/Plugins/wwStadiumTool/Content

[/Script/UnrealEd.UnrealEdEngine]
TemplateMapInfos=(ThumbnailTexture=Texture2D'"/Engine/Maps/Templates/Thumbnails/Default.Default"',Map="/Engine/Maps/Templates/Template_Default")
TemplateMapInfos=(ThumbnailTexture=Texture2D'"/Engine/Maps/Templates/Thumbnails/TimeOfDay.TimeOfDay"',Map="/Engine/Maps/Templates/TimeOfDay_Default")
TemplateMapInfos=(ThumbnailTexture=Texture2D'"/Engine/Maps/Templates/Thumbnails/VR-Basic.VR-Basic"',Map="/Engine/Maps/Templates/VR-Basic")

[/Script/AndroidPlatformEditor.AndroidSDKSettings]
SDKPath=(Path="")
NDKPath=(Path="")
JavaPath=(Path="")

[/Script/UdpMessaging.UdpMessagingSettings]
EnabledByDefault=False
EnableTransport=True
bAutoRepair=True
MaxSendRate=1.000000
AutoRepairAttemptLimit=10
bStopServiceWhenAppDeactivates=True
UnicastEndpoint=0.0.0.0:0
MulticastEndpoint=*********:6666
MessageFormat=CborPlatformEndianness
MulticastTimeToLive=1
EnableTunnel=False
TunnelUnicastEndpoint=
TunnelMulticastEndpoint=

[/Script/LuminPlatformEditor.MagicLeapSDKSettings]
MLSDKPath=(Path="")

