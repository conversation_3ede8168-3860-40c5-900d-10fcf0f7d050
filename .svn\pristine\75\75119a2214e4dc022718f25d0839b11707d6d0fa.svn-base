// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIPopulatorCareerPlayerDraft.h"

//< Rugby >
#include "Rugby/Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3CompetitionPlayerHelper.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DatabaseConstants.h"
#include "RUUIDatabaseQueryManager.h"
#include "Rugby/RugbyGameInstance.h"

//< Widgets >
#include "PanelWidget.h"
#include "WWUIScreenTemplate.h"
#include "ScrollBox.h"
#include "WWUIScrollBox.h"
#include "WWUIListField.h"
#include "Border.h"
#include "Utility/Helpers/SIFUIHelpers.h"
#include "Image.h"
#include "Utility/Helpers/SIFGameHelpers.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/Debug/SIFDebug.h"
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeDebugSettings.h"
								
																									 
UWWUIPopulatorCareerPlayerDraft::UWWUIPopulatorCareerPlayerDraft() :
	last_valid_slot_index(-1),
	captain_player_id(DB_INVALID_ID),
	goalkicker_player_id(DB_INVALID_ID),
	playkicker_player_id(DB_INVALID_ID),
	list_node(nullptr)
{}

UWWUIPopulatorCareerPlayerDraft::~UWWUIPopulatorCareerPlayerDraft()
{}

void UWWUIPopulatorCareerPlayerDraft::Populate(UWidget* widget)
{
	if (inPreConstruct)
		return;

	Clear(widget);

	CareerPlayerDraftFileCreationNodeCallback callbackObject(widget, dataList.ArrayOption);

	CreateNodesFromTemplate(dataList.TemplateName, ordered_players.Num(), &callbackObject);

	if (ScreenRef)
	{
#ifdef UI_USING_UMG
		ScreenRef->StoreChildWidgets();
#else
		if (ScreenRef && ScreenRef->GetStateScreen())
		{
			ScreenRef->GetStateScreen()->StoreChildWidgets();
		}
#endif
	}
}

void UWWUIPopulatorCareerPlayerDraft::Refresh(UWidget* widget)
{
	UScrollBox* listbox = Cast<UScrollBox>(widget);

	if (InData.is_team_customisation)
	{
		RUDB_TEAM* custom_team = SIFUIHelpers::GetQueryManager()->GetTeamData();
		captain_player_id = custom_team->captain_id;
		goalkicker_player_id = custom_team->goal_kicker_id;
		playkicker_player_id = custom_team->play_kicker_id;
	}

	// RC3 Comments
	// Unselect current player
	// Select the new player

	if (listbox)
	{
		// Populate children
		for (size_t i = 0; i < listbox->GetChildrenCount(); ++i)
		{
			const PlayerInfo& player_info = ordered_players[i];
			UWidget* child = listbox->GetChildAt(i);
			MABASSERT(child);

			unsigned int list_index = (unsigned int)(i + 1);

			PopulatePlayerInfo(player_info, child, list_index);
		}
	}
}

void UWWUIPopulatorCareerPlayerDraft::PopulateAndRefresh(UWidget* widget)
{
	UScrollBox* listbox = Cast<UScrollBox>(widget);

	// Get loaded team

	RL3DB_TEAM db_team(InData.team_db_id);

	if (listbox)
	{
		if (InData.force_refresh_prop)
		{
			InData.force_refresh_prop = false;
			GenerateOrderedPlayerList(db_team, InData.is_team_customisation);
			UWWUIFunctionLibrary::SetProperty(RUCDSP_FORCE_REFRESH, &InData.force_refresh_prop, list_node, PROPERTY_TYPE_BOOL);
		}

		// removing this because editing a team, deleting players and then reverting the team would still show player as deleted.
		//if (listbox->GetChildrenCount() == 0)
		//{
			// Load players
			ordered_players.Empty();
			GenerateOrderedPlayerList(db_team, InData.is_team_customisation);

			// Populate
			Populate(widget);
		//}

		Refresh(widget);

		//WWUINodeProperty UpdateEvent = WWUINodeProperty();
		//FString EventName = RUCDSP_LIST_UPDATE;
		//UpdateEvent.SetProperty("system_event", &EventName, PROPERTY_TYPE_FSTRING);

		//ScreenRef->OnSystemEvent(UpdateEvent);
	}
}

void UWWUIPopulatorCareerPlayerDraft::GenerateOrderedPlayerList(RL3DB_TEAM db_team, bool bIsTeamCustomisation)
{
	// Get helper
	RUDBHelperInterface *db_helper = SIFApplication::GetApplication()->GetGameDBHelper();
	RUDB_TEAM *custom_team = NULL;
	MabVector< unsigned short > player_lineup;

	int playersPerTeam = -1;
	// Make blank players at end of lineup, so we can increase the number of players.
	int playersOnTeamIncBench = SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBench();

	bool isR7Team = false;

	// Get all players in display order
	if (bIsTeamCustomisation)
	{
		/// In team customisation... use 'team' supplied by lua query manager...
		custom_team = SIFUIHelpers::GetQueryManager()->GetTeamData();
		custom_team->GetAllPlayersInDisplayOrder(player_lineup, true);


		isR7Team = false; // Nick WWS &s to Womens 13s // custom_team->GetIsR7Exclusive();
		playersPerTeam = isR7Team ? DB_NUM_PLAYERS_PER_TEAM_R7 : DB_NUM_PLAYERS_PER_TEAM;

		// Since the squads can be viewed outside of a game, the game mode may not be set at this stage. So Running the generic GetNumberOfPlayersPerTeamIncBench() will always return 15.
		playersOnTeamIncBench = isR7Team ?
			SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBenchR7() :
			SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBenchR13();
	}
	else
	{
		isR7Team = false; // Nick WWS &s to Womens 13s // db_team.GetIsR7Exclusive();

		db_team.GetAllPlayersInDisplayOrder(player_lineup, true);
		playersPerTeam = isR7Team ? DB_NUM_PLAYERS_PER_TEAM_R7 : DB_NUM_PLAYERS_PER_TEAM;

		playersOnTeamIncBench = isR7Team ?
			SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBenchR7() :
			SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBenchR13();
	}


	while (player_lineup.size() > (size_t)playersOnTeamIncBench && player_lineup[player_lineup.size() - 1] == DB_INVALID_ID)
	{
		player_lineup.pop_back();
	}
	while (player_lineup.size() < (size_t)(playersPerTeam))
	{
		player_lineup.push_back(DB_INVALID_ID);
	}

	// Cache player info
	if (ordered_players.Num() != player_lineup.size())
	{
		ordered_players.Empty();
		for (unsigned int pid = 0; pid < player_lineup.size(); pid++)
		{
			PlayerInfo pinfo;
			pinfo.db_id = DB_INVALID_ID;
			ordered_players.Add(pinfo);
		}
	}

	if (bIsTeamCustomisation)
	{
		for (unsigned int pid = 0; pid < player_lineup.size(); pid++)
		{
			PlayerInfo *pinfo = &ordered_players[pid];
			pinfo->db_id = DB_INVALID_ID;
			pinfo->contract_valid = false;
			pinfo->contract_position = (PLAYER_POSITION)(1 << pid);
			pinfo->num_caps = 0;

			if (player_lineup[pid] != DB_INVALID_ID)
			{
				if (player_lineup[pid] != 1)
				{
					// This is where the RU_PlayerDB_Info struct gets filled in the player info
					RU_PlayerDB_Data* pPlayerData = db_helper->LoadPlayerDBData(player_lineup[pid]);
					pPlayerData->PopulatePlayerInfo(pinfo, isR7Team);
					RL3DB_PLAYER plr(player_lineup[pid]);
					pinfo->num_caps = plr.GetNumCaps();
				}
#if ((PLATFORM_WINDOWS)|| (PLATFORM_PS4) || (PLATFORM_XBOXONE) || PLATFORM_SWITCH) && defined (FANHUB_ENABLED)// && defined APPBUILD_RUGBY_CHALLENGE_3
				else
				{
					//If the db id is 1 then that means it's a custom player uploaded to the server
					// so we need to get the customisation team then use that to get the server id
					// and then get the json string of the player with that sever id. After all that 
					// we populate the RUDB_PLAYER with the json string and set all the pinfo vars using it
					RUDB_PLAYER* server_player;
					//	RUGameCentre& game_centre = RUGameCentre::Get();
					RUDB_TEAM*		customisation_team = NULL;

					URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

					if (pRugbyGameInstance)
					{
						RUUIDatabaseQueryManager* pDatabaseQueryManager = pRugbyGameInstance->GetDbQueryManager();

						if (pDatabaseQueryManager)
						{
							customisation_team = (RUDB_TEAM*)pDatabaseQueryManager->GetTeamData();

							server_player = customisation_team->GetServerRUDBPlayerAtIndex(pid).Get();
							//if (game_centre.GetPlayerById(customisation_team->GetLineup(pid).server_id.c_str(), json_stats))
							if (server_player != NULL)
							{
								pinfo->db_id = 1;
								pinfo->lineup_index = pid;
								pinfo->first_name = server_player->GetFirstName();
								pinfo->last_name = server_player->GetLastName();
								pinfo->club_name = server_player->GetClubName();
								pinfo->age = server_player->GetAge();
								pinfo->overall_rating = server_player->GetOverallRating();

								// Switch up the positions depending on what this team is. Kade might want to look into this since it's server stuff..
								// MULTI_POSITION_CATEGORY_CHANGE
								if (isR7Team)
								{
									pinfo->primary_position = server_player->GetPositionCategoryR7(0);
									pinfo->secondary_position = server_player->GetPositionCategoryR7(1);
								}
								else
								{
									pinfo->primary_position = server_player->GetPositionCategoryR13(0);
									pinfo->secondary_position = server_player->GetPositionCategoryR13(1);
								}
								pinfo->num_caps = 0;
							}
						}
					}
				}
#endif
				pinfo->contract_valid = true;
				pinfo->contract_years = 5;
			}
		}
	}
	else
	{
		RL3CompetitionPlayerHelper *competition_player_helper = SIFApplication::GetApplication()->GetCareerModeManager()->GetCompetitionPlayerHelper();

		// If international team contracts are handled differently.
		bool is_international_team = db_team.IsInternational();

		for (unsigned int pid = 0; pid < player_lineup.size(); pid++)
		{
			PlayerInfo *pinfo = &ordered_players[pid];
			pinfo->db_id = DB_INVALID_ID;
			pinfo->contract_valid = false;

			if (player_lineup[pid] != DB_INVALID_ID)
			{
				db_helper->LoadPlayerDBData(player_lineup[pid])->PopulatePlayerInfo(pinfo, isR7Team);

				if (RU_ContractDB_Data* db_contract = db_helper->LoadContractDBData(db_team.GetDbId(), player_lineup[pid]))
				{

					pinfo->contract_valid = true;
					pinfo->contract_value = db_contract->GetValue();
					pinfo->contract_years = db_contract->GetYears();
					pinfo->contract_position = (PLAYER_POSITION)db_contract->GetPosition();

					RL3DB_PLAYER plr(player_lineup[pid]);
					pinfo->num_caps = plr.GetNumCaps();

					if (!is_international_team)
					{
						pinfo->retiring = competition_player_helper->IsPlayerGoingToRetire(plr) && (pinfo->contract_years <= 1);

						if (pinfo->contract_years == 0)		// Terminated.
						{
							pinfo->db_id = DB_INVALID_ID;
							pinfo->contract_valid = false;
						}
					}
					else
					{
						pinfo->contract_years = 3;
						pinfo->retiring = false;
					}
				}
			}
		}

		/// Now add on signed player contracts and modify the expiring player contracts if they have resigned.

		if (!is_international_team)
		{
			int num_signings = db_team.GetNumSignedPlayers();
			for (int i = 0; i < num_signings; i++)
			{
				RL3DB_CONTRACT contract = db_team.GetSignedPlayerContract(i);

				bool already_in_team = false;
				if (contract.position != 0 && contract.position < player_lineup.size() &&
					(ordered_players[contract.position - 1].contract_valid == false || ordered_players[contract.position - 1].retiring))
				{	// Signed contract was to fill a given slot (contract.position)...
					PlayerInfo *pinfo = &ordered_players[contract.position - 1];

					db_helper->LoadPlayerDBData(contract.index)->PopulatePlayerInfo(pinfo, isR7Team);
					pinfo->contract_value = contract.GetValue();
					pinfo->contract_years = contract.GetYears() + 1;
					pinfo->contract_valid = true;
					pinfo->is_signed = true;
					pinfo->retiring = false;

					RL3DB_PLAYER plr(contract.index);
					pinfo->num_caps = plr.GetNumCaps();

					already_in_team = true;
				}
				else
				{
					/// Check if player already in current lineup, if so replace his slot entry.
					for (unsigned int pid = 0; pid < player_lineup.size(); pid++)
					{
						PlayerInfo *pinfo = &ordered_players[pid];
						if (pinfo->db_id == contract.index)
						{
							already_in_team = true;

							pinfo->contract_valid = true;
							pinfo->contract_value = contract.GetValue();
							pinfo->contract_years = contract.GetYears() + 1;
							pinfo->is_signed = true;
							pinfo->retiring = false;

							RL3DB_PLAYER plr(contract.index);
							pinfo->num_caps = plr.GetNumCaps();

							break;
						}
					}
				}

				if (!already_in_team)
				{
					bool added = false;
					/// Not in team already, or using designated slot, just place in first slot available. (after position 22)
					//int playersOnTeamIncBench = SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBench();

					for (int pid = playersOnTeamIncBench/*NUM_PLAYERS_PER_TEAM_INC_BENCH*/; pid < ordered_players.Num(); pid++)
					{
						PlayerInfo *pinfo = &ordered_players[pid];
						if (ordered_players[pid].db_id == DB_INVALID_ID && !pinfo->contract_valid)
						{
							db_helper->LoadPlayerDBData(contract.index)->PopulatePlayerInfo(pinfo, isR7Team);

							pinfo->contract_value = contract.GetValue();
							pinfo->contract_years = contract.GetYears() + 1;
							pinfo->contract_valid = true;
							pinfo->is_signed = true;
							pinfo->retiring = false;

							RL3DB_PLAYER plr(contract.index);
							pinfo->num_caps = plr.GetNumCaps();

							added = true;
							break;
						}
					}

					/// Failsafe.
					if (!added)
					{
						PlayerInfo pinfo;

						db_helper->LoadPlayerDBData(contract.index)->PopulatePlayerInfo(&pinfo, isR7Team);

						pinfo.contract_value = contract.GetValue();
						pinfo.contract_years = contract.GetYears() + 1;
						pinfo.contract_valid = true;
						pinfo.is_signed = true;

						RL3DB_PLAYER plr(contract.index);
						pinfo.num_caps = plr.GetNumCaps();

						ordered_players.Add(pinfo);
					}
				}
			}
		}
	}

	// Mark recruitable slot.

	last_valid_slot_index = -1;
	for (int i = (int)ordered_players.Num() - 1; i >= 0; i--)
	{
		const PlayerInfo& player_info = ordered_players[i];
		if (player_info.contract_valid)
		{
			last_valid_slot_index = i;
			break;
		}
	}

	//------------------------------------------------------
	// Encode player states as a string to send to lua.

	player_states.Empty();

	for (int i = 0; i < ordered_players.Num(); i++)
	{
		const PlayerInfo& player_info = ordered_players[i];

		if (!player_info.contract_valid)
		{
			player_states.Add(EPlayerState::X);
		}
		else
		{
			if (player_info.is_signed)
			{
				player_states.Add(EPlayerState::Y);
			}
			else if ((player_info.contract_valid && player_info.contract_years <= 1))
			{
				if (player_info.retiring)
					player_states.Add(EPlayerState::R);
				else
					player_states.Add(EPlayerState::X);
			}
			else
			{
				player_states.Add(EPlayerState::O);
			}
		}
	}

	//list_node->SetProperty(RUCDSP_PLAYER_STATES_PROPERTY, player_states); you can now retrive this data from the populator directly...
}

void UWWUIPopulatorCareerPlayerDraft::PopulatePlayerInfo(const PlayerInfo& player_info, UWidget* _row_node, unsigned int list_index)
{
	UWWUIListField* row_node = Cast<UWWUIListField>(_row_node);

	if (!row_node)
	{
		ensure(row_node);
		return;
	}

	int player_db_id = player_info.db_id;
	bool player_retiring = player_info.retiring;
	row_node->SetProperty(RUCDSP_PLAYER_ID, &player_db_id, PROPERTY_TYPE_INT);
	row_node->SetProperty(RUCDSP_PLAYER_LIST_INDEX, &list_index, PROPERTY_TYPE_INT);
	row_node->SetProperty(RUCDSP_PLAYER_SLOT_RETIRING, &player_retiring, PROPERTY_TYPE_BOOL);

	bool is_international_team = false;
	if (!InData.is_team_customisation)
	{
		RL3DB_TEAM db_team(InData.team_db_id);
		// If international team contracts are handled differently.
		is_international_team = db_team.GetRepAreaId() != 0;
	}

	FLinearColor line_colour = FLinearColor::White;
	FLinearColor red_colour = DRAFT_FIELD_COLOR_RED;	// RED

	bool contract_expired = false;

	{
		// If there is no player at this level or if player's contract has expired, then mark the row
		contract_expired = (player_info.contract_valid && player_info.contract_years <= 1);			// N.B this is all done before subtracting 1 from the contract_years.
		row_node->SetProperty(RUCDSP_PLAYER_CONTRACT_EXPIRED, &contract_expired, PROPERTY_TYPE_BOOL);

		// Chose which child node to populate

		if (!player_info.contract_valid)
		{
			line_colour = red_colour;
		}
		else
		{
			if (player_info.is_signed)
			{
				line_colour = DRAFT_FIELD_COLOR_GREEN;		// GREEN.
			}
			else if (contract_expired)
			{
				if (player_info.retiring)
				{
					line_colour = FLinearColor::Blue;
				}
				else
				{
					line_colour = red_colour;
				}
			}
		}

		// Team customisation allows player swaps, colour player to be swapped...
		if (InData.is_team_customisation && InData.player_to_swap == player_info.db_id && InData.player_to_swap != DB_INVALID_ID)
		{
			line_colour = DRAFT_FIELD_COLOR_BLUE;				// BLUE
		}
	}

	UWidget* child_node = NULL;

	int	playersPerTeam = DB_NUM_PLAYERS_PER_TEAM;
	int playersPerTeamOnField = SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamR13();
	int	playersOnTeamIncBench = SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBenchR13();

	if (InData.team_db_id != DB_INVALID_ID)
	{
		// MULTI_POSITION_CATEGORY_CHANGE
		// Helps can't use the helper functions anymore, because it relies on game mode being set, which is no neccessarily true in the main menus
		RL3DB_TEAM db_team(InData.team_db_id);

		playersPerTeam = DB_NUM_PLAYERS_PER_TEAM; // Nick WWS &s to Womens 13s //db_team.GetIsR7Exclusive() ? DB_NUM_PLAYERS_PER_TEAM_R7 : DB_NUM_PLAYERS_PER_TEAM;

		playersPerTeamOnField =  // Nick WWS &s to Womens 13s //db_team.GetIsR7Exclusive() ? SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamR7() :
			SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamR13();

		playersOnTeamIncBench =  // Nick WWS &s to Womens 13s //db_team.GetIsR7Exclusive() ?
			// Nick WWS &s to Womens 13s //SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBenchR7() :
			SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBenchR13();
	}
	else
	{
		bool isSevensGame = false; // Nick  WWS 7s to Womens // SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetGameMode() == GAME_MODE_SEVENS;
		playersPerTeam = isSevensGame ? DB_NUM_PLAYERS_PER_TEAM_R7 : DB_NUM_PLAYERS_PER_TEAM;

		playersPerTeamOnField = isSevensGame ? SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamR7() :
			SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamR13();

		playersOnTeamIncBench = isSevensGame ? SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBenchR7() :
			SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBenchR13();
	}

	if (((int)list_index == playersPerTeam || (int)list_index == playersPerTeamOnField || (int)list_index == playersOnTeamIncBench))
	{
		UImage* pDividerImage = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(row_node, FString(DRAFT_FIELD_IMAGE_DIVIDER)));
		if (pDividerImage)	
		{
			pDividerImage->SetVisibility(ESlateVisibility::Visible);
		}
		else
		{
			ensure(pDividerImage);
		}
	}

	//< Get pointers to the widget components. >
	UWidget* captain_node			= UWWUIFunctionLibrary::FindChildWidget(row_node, FString(DRAFT_FIELD_CAPTAIN));
	UWidget* goalkicker_node = UWWUIFunctionLibrary::FindChildWidget(row_node, FString(DRAFT_FIELD_GOAL_KICKER));
	UWidget* playkicker_node = UWWUIFunctionLibrary::FindChildWidget(row_node, FString(DRAFT_FIELD_PLAY_KICKER));
	UTextBlock* count_node = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(row_node, FString(DRAFT_FIELD_TEXT_NUMBER)));
	UTextBlock* position_node = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(row_node, FString(DRAFT_FIELD_TEXT_POSITION)));							
	UTextBlock* second_node = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(row_node, FString(DRAFT_FIELD_TEXT_SECOND_POSITION)));						
	UTextBlock* name_node = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(row_node, FString(DRAFT_FIELD_TEXT_NAME)));									
	UTextBlock* age_node = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(row_node, FString(DRAFT_FIELD_TEXT_AGE)));									
	UTextBlock* rating_node = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(row_node, FString(DRAFT_FIELD_TEXT_RATING)));								
	UTextBlock* value_node = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(row_node, FString(DRAFT_FIELD_TEXT_VALUE)));								
	UTextBlock* years_node = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(row_node, FString(DRAFT_FIELD_TEXT_YEARS)));								
	UTextBlock* notification_node	= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(row_node, FString(DRAFT_FIELD_TEXT_NOTIFICATION)));

	ensure(captain_node && goalkicker_node && playkicker_node && count_node && position_node && second_node && name_node && age_node && rating_node && value_node && years_node && notification_node);

		// Turn off indicators by default.
	UWWUIFunctionLibrary::SetVisibility(captain_node,		ESlateVisibility::Collapsed);
	UWWUIFunctionLibrary::SetVisibility(goalkicker_node,	ESlateVisibility::Collapsed);
	UWWUIFunctionLibrary::SetVisibility(playkicker_node,	ESlateVisibility::Collapsed);


	// Check if we have a missing player in top 15
	if (!player_info.contract_valid)
	{
		FString notification = "[ID_RECRUIT_PLAYER]";

		if (list_index < 32)		// We're working on 32bit values, so stop overflow.
		{
			//Gets the position depending on game mode
			PLAYER_POSITION position;
			bool is_sevens = false; // Nick WWS 7s to Womens 13s // SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GameModeIsR7();

			if (is_sevens)
			{
				if (list_index <= (unsigned int)SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamR7())
				{
					position = PlayerPositionEnum::GetPlayerPositionFromLineupIndex(list_index - 1);
				}
				else
				{
					position = PP_NONE;
				}
			}
			else
			{
				position = (PLAYER_POSITION)(1 << (list_index - 1));
			}

			if (position <= PP_FULLBACK)
			{
				FString raw_str = UWWUITranslationManager::Translate(FString("[ID_RECRUIT_PLAYER_AT_POSITION]"));
				FString data_str = FString(UTF8_TO_TCHAR(PlayerPositionEnum::GetPlayerPositionText(position)));
				notification = FString::Printf(*raw_str, *data_str);
			}
		}

		/// Only make the first empty slot of the empty slots at the end of the players recruitable.
		bool recruitable = true;
		int tmp_recruite_index = MabMath::Max((int)last_valid_slot_index, playersOnTeamIncBench - 2);
		if ((int)list_index > tmp_recruite_index + 2)		// +2 due to index starting from 1.
		{
			recruitable = false;
			notification = "- -";
		}

		row_node->SetProperty(RUCDSP_PLAYER_SLOT_RECRUITABLE, &recruitable, PROPERTY_TYPE_BOOL);

		// Turn off all children--------------------------------------------------------------------------------------------
		UWWUIFunctionLibrary::SetVisibility(position_node,		false);
		UWWUIFunctionLibrary::SetVisibility(second_node,		false);
		UWWUIFunctionLibrary::SetVisibility(name_node,			false);
		UWWUIFunctionLibrary::SetVisibility(age_node,			false);
		UWWUIFunctionLibrary::SetVisibility(rating_node,		false);
		UWWUIFunctionLibrary::SetVisibility(value_node,			false);
		UWWUIFunctionLibrary::SetVisibility(years_node,			false);

		// Turn on count & notification.------------------------------------------------------------------------------------
		//< setup for count >
		UWWUIFunctionLibrary::SetVisibility(count_node, true);
		UWWUIFunctionLibrary::SetText(count_node, FString::FromInt(list_index));
		UWWUIFunctionLibrary::SetColorAndOpacity(count_node, line_colour);

		//< setup for notification >
		UWWUIFunctionLibrary::SetVisibility(notification_node, true);
		UWWUIFunctionLibrary::SetText(notification_node, UWWUITranslationManager::Translate(notification));
		UWWUIFunctionLibrary::SetColorAndOpacity(notification_node, line_colour);

		return;
	}

	//< Setting the recruitable property to false. >
	int player_slot_recruitable = 0;
	row_node->SetProperty(RUCDSP_PLAYER_SLOT_RECRUITABLE, &player_slot_recruitable, PROPERTY_TYPE_INT);

	// Turn on all children-------------------------------------------------------------------------------------------------
	UWWUIFunctionLibrary::SetVisibility(count_node,			true);
	UWWUIFunctionLibrary::SetVisibility(position_node,		true);
	UWWUIFunctionLibrary::SetVisibility(second_node,		true);
	UWWUIFunctionLibrary::SetVisibility(name_node,			true);
	UWWUIFunctionLibrary::SetVisibility(age_node,			true);
	UWWUIFunctionLibrary::SetVisibility(rating_node,		true);
	UWWUIFunctionLibrary::SetVisibility(value_node,			true);
	UWWUIFunctionLibrary::SetVisibility(years_node,			true);

	//< Turn off notification. >
	UWWUIFunctionLibrary::SetVisibility(notification_node,	false);

	//< Set data on all fields. >-------------------------------------------------------------------------------------------
	//< Set Count data >
	UWWUIFunctionLibrary::SetText(count_node, FString::FromInt(list_index));
	UWWUIFunctionLibrary::SetColorAndOpacity(count_node, line_colour);

	MABLOGDEBUG("The team ID that we're looking at now is: %i", InData.team_db_id);


	//< Set Position Data >
	PLAYER_POSITION primaryPos = player_info.primary_position;
	if (primaryPos <= PP_FULLBACK)
		UWWUIFunctionLibrary::SetText(position_node, UWWUITranslationManager::Translate(FString(PlayerPositionEnum::GetPlayerPositionTextAbbreviated(primaryPos))));
	else
		UWWUIFunctionLibrary::SetText(position_node, FString("- -"));
	UWWUIFunctionLibrary::SetColorAndOpacity(position_node, line_colour);


	//< Set Second Position Data >
	PLAYER_POSITION secondaryPos = player_info.secondary_position;
	if (secondaryPos <= PP_FULLBACK)
		UWWUIFunctionLibrary::SetText(second_node, UWWUITranslationManager::Translate(FString(PlayerPositionEnum::GetPlayerPositionTextAbbreviated(secondaryPos))));
	else
		UWWUIFunctionLibrary::SetText(second_node, FString("- -"));
	UWWUIFunctionLibrary::SetColorAndOpacity(second_node, line_colour);


	FString display_name;
#ifdef ENABLE_GAME_DEBUG_MENU
	if (SIFDebug::GetCareerModeDebugSettings()->GetDisplayIDs())
	{
		FString FirstName = SIFGameHelpers::GAConvertMabStringToFString(player_info.first_name);
		FString LastName = SIFGameHelpers::GAConvertMabStringToFString(player_info.last_name);

		if (!FirstName.IsEmpty())
		{
			display_name = FString::FromInt(player_info.db_id) + ", " + LastName + ", " + FirstName[0] + ".";
		}
		else
		{
			display_name = FString::FromInt(player_info.db_id) + ", " + LastName + ", ";
		}
		
	}
	else
#endif
	{
		FString FirstName = SIFGameHelpers::GAConvertMabStringToFString(player_info.first_name);
		FString LastName = SIFGameHelpers::GAConvertMabStringToFString(player_info.last_name);

		if (!FirstName.IsEmpty())
		{
			display_name = LastName + ", " + FirstName[0] + ".";
		}
		else
		{
			display_name = LastName + ", ";
		}
		
	}

//#if PLATFORM_PS4
	// #rc3_legacy_censor
	// any downloaded names should already be censored
//	//< Consor downloaded player names on PS4 >
//	// player_info.db_id == 1 seems to imply 'downloaded', user the ServerID instead

	MabString displayName = SIFGameHelpers::GAConvertFStringToMabString(display_name);

	if (InData.is_team_customisation && player_info.db_id == 1)
	{
		RUHUDUpdater::CensorPlayerName(displayName);
	}
	else
	{
		RUHUDUpdater::CensorPlayerName(-1, player_info.db_id, displayName);
	}
//#endif

	//< Set Name Data >
	UWWUIFunctionLibrary::SetText(name_node, SIFGameHelpers::GAConvertMabStringToFText(displayName));
	UWWUIFunctionLibrary::SetColorAndOpacity(name_node, line_colour);

	//< Set Age Data >
	UWWUIFunctionLibrary::SetText(age_node, FString::FromInt(player_info.age));
	UWWUIFunctionLibrary::SetColorAndOpacity(age_node, line_colour);

	//< Set Rating Data >
	UWWUIFunctionLibrary::SetText(rating_node, FString::FromInt(player_info.overall_rating));
	UWWUIFunctionLibrary::SetColorAndOpacity(rating_node, line_colour);

	//< Set Career Data if not in customization mode >----------------------------------------------------------------------
	if (!InData.is_team_customisation)
	{
		//< Show career only stat widgets >
		UWWUIFunctionLibrary::SetVisibility(value_node, true);
		UWWUIFunctionLibrary::SetVisibility(years_node, true);

		//< Set Value Data >
		if (!is_international_team)
		{
			SIFUIHelpers::ConvertIntToCurrencyInTextNode(value_node, player_info.contract_value);
		}
		else
			UWWUIFunctionLibrary::SetText(value_node, FString::FromInt(player_info.num_caps));

		UWWUIFunctionLibrary::SetColorAndOpacity(value_node, line_colour);

		//< Set Year Data >
		if (!is_international_team)
		{
			if (contract_expired)
				UWWUIFunctionLibrary::SetText(years_node, player_info.retiring ? UWWUITranslationManager::Translate(FString("[ID_RETIRING]")) : UWWUITranslationManager::Translate(FString("[ID_CONTRACT_EXPIRED_SHORT]")));
			else
				UWWUIFunctionLibrary::SetText(years_node, FString::FromInt(player_info.contract_years - 1));
		}
		else
			UWWUIFunctionLibrary::SetText(years_node, FString(""));

		UWWUIFunctionLibrary::SetColorAndOpacity(years_node, line_colour);
	}
	else
	{
		// Turn off value + years text.
		UWWUIFunctionLibrary::SetVisibility(value_node, false);
		UWWUIFunctionLibrary::SetVisibility(years_node, false);

		// Turn on indicators and get the status
		UWWUIFunctionLibrary::SetVisibility(captain_node,		ESlateVisibility::Collapsed);
		UWWUIFunctionLibrary::SetVisibility(goalkicker_node,	ESlateVisibility::Collapsed);
		UWWUIFunctionLibrary::SetVisibility(playkicker_node,	ESlateVisibility::Collapsed);
		
		//< Active indicators for the current roles. >
		if (player_info.db_id != DB_INVALID_ID)
		{
			if (player_info.db_id == DB_SERVER_PLAYER_ID)
			{
				if (player_info.lineup_index == captain_player_id - 1)		UWWUIFunctionLibrary::SetVisibility(captain_node, true);
				if (player_info.lineup_index == goalkicker_player_id - 1)	UWWUIFunctionLibrary::SetVisibility(goalkicker_node, true);
				if (player_info.lineup_index == playkicker_player_id - 1)	UWWUIFunctionLibrary::SetVisibility(playkicker_node, true);
			}
			else
			{
				if (player_info.db_id == captain_player_id)		UWWUIFunctionLibrary::SetVisibility(captain_node, true);
				if (player_info.db_id == goalkicker_player_id)	UWWUIFunctionLibrary::SetVisibility(goalkicker_node, true);
				if (player_info.db_id == playkicker_player_id)	UWWUIFunctionLibrary::SetVisibility(playkicker_node, true);
			}
		}
	}
}


void UWWUIPopulatorCareerPlayerDraft::UpdateColumnHighlight(int old_selected_column)
{

}

void UWWUIPopulatorCareerPlayerDraft::SortPlayerList(int column_num)
{

}

UWWUIPopulatorCareerPlayerDraft::CareerPlayerDraftFileCreationNodeCallback::CareerPlayerDraftFileCreationNodeCallback(UWidget* containerToPopulate, TArray<FWWUIScreenTemplateDataOption>& inDataOptions) :
	container(),
	dataOptions(inDataOptions),
	ItemIndex(0)
{
	container = Cast<UPanelWidget>(containerToPopulate);

	if (!container)
	{
		FString errorString = containerToPopulate != nullptr ? *containerToPopulate->GetPathName() : FString("NULL");
		//UE_LOG(LogWWUI, Error, TEXT("Cast to scroll box failed while attempting DataFileCreationNodeCallback on node %s"), *errorString);
	}
}

void UWWUIPopulatorCareerPlayerDraft::CareerPlayerDraftFileCreationNodeCallback::Callback(UUserWidget* widget)
{
	container->AddChild(widget);
	ItemIndex++;
}

