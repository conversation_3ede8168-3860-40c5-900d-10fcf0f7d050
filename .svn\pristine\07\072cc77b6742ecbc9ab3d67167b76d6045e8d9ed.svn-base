/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Roles/Officials/SSRoleReferee.h"

#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerLookAt.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/RUPlayerSound.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SIFGameWorld.h"

MABRUNTIMETYPE_IMP1( SSRoleReferee, SSRole );

static const float ADVANTAGE_VOCALS_DELAY = 1.0f;

//-------------------------------------------------------.

// Define a macro for listening for events
#define REGISTER_FOR_EVENT(event_name) \
	m_pGame->GetEvents()->event_name.Add( this, &SSRoleReferee::event_name );

// Define a macro for unlistening for events
#define DEREGISTER_FOR_EVENT(event_name) \
	m_pGame->GetEvents()->event_name.Remove( this, &SSRoleReferee::event_name );


///-------------------------------------------------------------------------
/// Constructor
///-------------------------------------------------------------------------

SSRoleReferee::SSRoleReferee( SIFGameWorld* game ) : SSRole(game), advantage_delay()
{
	advantage_delay.Initialise( game->GetSimTime(), ADVANTAGE_VOCALS_DELAY );
	advantage_delay.SetEnabled(false);
}

///-------------------------------------------------------------------------
/// Called when role starts.
///-------------------------------------------------------------------------

void SSRoleReferee::Enter( ARugbyCharacter* player )
{
	SSRole::Enter( player );

	player->GetActionManager()->EnableAllActions( true );

	//Listen for events
	REGISTER_FOR_EVENT(forward_pass);
	REGISTER_FOR_EVENT(free_kick_awarded);
	REGISTER_FOR_EVENT(full_time);
	REGISTER_FOR_EVENT(first_half_start);
	REGISTER_FOR_EVENT(second_half_start);
	REGISTER_FOR_EVENT(knock_on);
	REGISTER_FOR_EVENT(knock_on_awarded);
	REGISTER_FOR_EVENT(advantage_over);
	REGISTER_FOR_EVENT(advantage_started);
	//REGISTER_FOR_EVENT(commentary_interchange_started);
	REGISTER_FOR_EVENT(try_awarded);
	REGISTER_FOR_EVENT(lineout_faulty_throw_awarded);
	REGISTER_FOR_EVENT(blow_penalty_whistle);
	//REGISTER_FOR_EVENT(rule_trigger_ball_out);
	REGISTER_FOR_EVENT(offside);
	REGISTER_FOR_EVENT(breakdown_holding_on_illegal);
	REGISTER_FOR_EVENT(breakdown_should_release);
	REGISTER_FOR_EVENT(ball_dead_detected);
	REGISTER_FOR_EVENT(perform_interchange);
	//REGISTER_FOR_EVENT(ref_tackle_count_one);
	//REGISTER_FOR_EVENT(ref_tackle_count_two);
	//REGISTER_FOR_EVENT(ref_tackle_count_three);
	//REGISTER_FOR_EVENT(ref_tackle_count_four);
	////REGISTER_FOR_EVENT(ref_tackle_count_five);
	//REGISTER_FOR_EVENT(ref_tackle_count_six);

	advantage_delay.AcknowledgeAll();
	advantage_delay.Reset();
	advantage_delay.SetEnabled(false);
}

///-------------------------------------------------------------------------
/// Called when role exits.
///-------------------------------------------------------------------------

void SSRoleReferee::Exit(bool forced)
{
	SSRole::Exit(forced);

	//UnListen for events
	DEREGISTER_FOR_EVENT(forward_pass);
	DEREGISTER_FOR_EVENT(free_kick_awarded);
	DEREGISTER_FOR_EVENT(full_time);
	DEREGISTER_FOR_EVENT(first_half_start);
	DEREGISTER_FOR_EVENT(second_half_start);
	DEREGISTER_FOR_EVENT(knock_on);
	DEREGISTER_FOR_EVENT(knock_on_awarded);
	DEREGISTER_FOR_EVENT(advantage_over);
	DEREGISTER_FOR_EVENT(advantage_started);
	//DEREGISTER_FOR_EVENT(commentary_interchange_started);
	DEREGISTER_FOR_EVENT(try_awarded);
	DEREGISTER_FOR_EVENT(lineout_faulty_throw_awarded);
	DEREGISTER_FOR_EVENT(blow_penalty_whistle);
	//DEREGISTER_FOR_EVENT(rule_trigger_ball_out);
	DEREGISTER_FOR_EVENT(offside);
	DEREGISTER_FOR_EVENT(breakdown_holding_on_illegal);
	DEREGISTER_FOR_EVENT(breakdown_should_release);
	DEREGISTER_FOR_EVENT(ball_dead_detected);
	DEREGISTER_FOR_EVENT(perform_interchange);

	advantage_delay.AcknowledgeAll();
	advantage_delay.Reset();
	advantage_delay.SetEnabled(false);
}

///-------------------------------------------------------------------------
/// UpdateLogic
///-------------------------------------------------------------------------

void SSRoleReferee::UpdateLogic( const MabTimeStep& game_time_step )
{
	MABASSERT( m_pPlayer != NULL );

	SSRole::UpdateLogic( game_time_step );

	UpdateOfficialParticipation();

	if(advantage_delay.GetNumTimerEventsRaised() > 0)
	{
		advantage_delay.AcknowledgeAll();
		advantage_delay.Reset();
		advantage_delay.SetEnabled(false);

		// RyT - Moved To Callback to stop repeating
// 		player->GetSound()->PlayRefVocal( "advantage" );
	}

	SSTeam *attacking_team = (SSTeam*)m_pGame->GetGameState()->GetAttackingTeam();
	if(attacking_team!=NULL)
	{
		SSEVDSFormationManager *formation_manager = attacking_team->GetFormationManager();

		// Clear any errant NOSLOW flags.
		//if (!m_lock_manager.UFIsLocked(UF_SETFACING))
		//	movement->SetFacingFlags( AFFLAG_FACEMOTION );

		if(formation_manager)
		{
			if (!m_lock_manager.UFIsLocked(UF_SETWAYPOINT))
			{
				wwNETWORK_TRACE_JG("Update Logic");
				formation_manager->DoRefereeMovement(m_pPlayer);
			}

			if ( !m_lock_manager.UFIsLocked( UF_SETFACING ))
			{
				DoFacing();
			}
		}
	}

	/// Look at the ball
	if ( !m_lock_manager.UFIsLocked( UF_SETLOOK ) )
	{
		m_pPlayer->GetLookAt()->LookAtBallHolder();
	}
}

///-------------------------------------------------------------------------
/// UpdateCutScene
///-------------------------------------------------------------------------

void SSRoleReferee::UpdateCutScene( const MabTimeStep& game_time_step )
{
	UpdateLogic(game_time_step);
	SSCutSceneManager *cutscene_manager = m_pGame->GetCutSceneManager();
	cutscene_manager->PostProcessNonActorMovement(m_pPlayer);
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void SSRoleReferee::DoFacing()
{
	m_pPlayer->GetMovement()->SetFacingFlags( AFFLAG_FACEBALL );
	m_pPlayer->GetMovement()->SetFaceMotionSpeed( -1.0f );
}

int SSRoleReferee::GetFitness(const ARugbyCharacter* player, const SSRoleArea* area)
{
	MABUNUSED(area);

	if(player->GetAttributes()->GetTeam()->GetSide()==SIDE_OFFICIALS)
		return 99;
	else
		return 0;
}

void SSRoleReferee::WarpToWaypoint()
{
	wwNETWORK_TRACE_JG("WarpToWaypoint");
	SSEVDSFormationManager *formation_manager = m_pGame->GetGameState()->GetAttackingTeam()->GetFormationManager();
	formation_manager->DoRefereeMovement( m_pPlayer );
	DoFacing();

	SSRole::WarpToWaypoint();

	//reset whistle flag
	m_hasPlayedDeadBallWhistle = false;
}

///-------------------------------------------------------------------------
/// Event handlers
void SSRoleReferee::forward_pass(ARugbyCharacter*, ARugbyCharacter*)
{
	advantage_delay.SetEnabled(true);
}

void SSRoleReferee::free_kick_awarded(ARugbyCharacter*)
{
	m_pPlayer->GetSound()->CreateSoundEffect( "event:/sfx/on_field_referee_whistles/whistle_free_kick" );
}

void SSRoleReferee::full_time()
{
	m_pPlayer->GetSound()->CreateSoundEffect( "event:/sfx/on_field_referee_whistles/whistle_full_time" );
}

void SSRoleReferee::first_half_start()
{
	m_pPlayer->GetSound()->CreateSoundEffect( "event:/sfx/on_field_referee_whistles/whistle_kick_off" );
}

void SSRoleReferee::second_half_start()
{
	m_pPlayer->GetSound()->CreateSoundEffect( "event:/sfx/on_field_referee_whistles/whistle_kick_off" );
}

void SSRoleReferee::knock_on( ARugbyCharacter*, bool, const FVector& )
{
	advantage_delay.SetEnabled(true);
}

void SSRoleReferee::knock_on_awarded()
{
	m_pPlayer->GetSound()->CreateSoundEffect( "event:/sfx/on_field_referee_whistles/whistle_knock_on" );
}

void SSRoleReferee::advantage_over(ARugbyCharacter* offending_player)
{
	MABUNUSED(offending_player);

	// GGS DJH: https://dev.azure.com/glindagames/NRL%20Rugby%20League/_workitems/edit/3218
	//m_pPlayer->GetSound()->PlayRefVocal( "advantage_over" );
}
void SSRoleReferee::advantage_started(ARugbyCharacter* offending_player)
{
	MABUNUSED(offending_player);

	// GGS DJH: https://dev.azure.com/glindagames/NRL%20Rugby%20League/_workitems/edit/3218
	//m_pPlayer->GetSound()->PlayRefVocal( "advantage" );
}

void SSRoleReferee::commentary_interchange_started()
{
	m_pPlayer->GetSound()->CreateSoundEffect( "event:/sfx/on_field_referee_whistles/whistle_time_off_sub" );
}

void SSRoleReferee::try_awarded( )
{
	m_pPlayer->GetSound()->CreateSoundEffect( "event:/sfx/on_field_referee_whistles/whistle_try" );
}

void SSRoleReferee::lineout_faulty_throw_awarded(ARugbyCharacter*, const FVector& )
{
//	player->GetSound()->CreateSoundEffect( "event:/sfx/on_field_referee_whistles/whistle_penalty" );
	m_pPlayer->GetSound()->PlayRefVocal( "not_straight" );
}

void SSRoleReferee::blow_penalty_whistle( PENALTY_REASON reason )
{
	if ( reason != PENALTY_REASON_LINEOUT_FAULT ) // Lineouts are exempt because they are handled with OnLineoutThrow.
	{
		m_pPlayer->GetSound()->CreateSoundEffect( "event:/sfx/on_field_referee_whistles/whistle_penalty" );
	}
}

void SSRoleReferee::rule_trigger_ball_out( ARugbyCharacter*, const FVector&, const FVector&, bool, bool )
{
	m_pPlayer->GetSound()->CreateSoundEffect( "event:/sfx/on_field_referee_whistles/whistle_in_touch" );
}

void SSRoleReferee::offside(ARugbyCharacter*)
{
	advantage_delay.SetEnabled(true);
}

void SSRoleReferee::breakdown_holding_on_illegal( ARugbyCharacter*, ARugbyCharacter* )
{
	m_pPlayer->GetSound()->PlayRefVocal( "holding_on" );
}

void SSRoleReferee::breakdown_should_release( ARugbyCharacter*, ARugbyCharacter* )
{
	m_pPlayer->GetSound()->PlayRefVocal( "release" );
}

void SSRoleReferee::ball_dead_detected( ARugbyCharacter*, const FVector&, bool )
{
	if (!m_hasPlayedDeadBallWhistle)
	{
		m_hasPlayedDeadBallWhistle = true;

		m_pPlayer->GetSound()->CreateSoundEffect("event:/sfx/on_field_referee_whistles/whistle_ball_dead");
	}

}

void SSRoleReferee::perform_interchange( int, RUTeam* )
{
	m_pPlayer->GetSound()->CreateSoundEffect( "event:/sfx/on_field_referee_whistles/whistle_sub_on" );
}

//Brian hack - Referee tackle counts

void SSRoleReferee::ref_tackle_count(int tackle_number)
{

	SIFGameWorld* mWorld = SIFApplication::GetApplication()->GetActiveGameWorld();
	if (!mWorld)
	{
		UE_LOG(LogTemp, Warning, TEXT("SSRoleReferee::ref_tackle_count() mWorld is null"));
		return;
	}
	RUTeam* mTeamOfficials = mWorld->GetOfficialsTeam();
	if (!mTeamOfficials)
	{
		UE_LOG(LogTemp, Warning, TEXT("SSRoleReferee::ref_tackle_count() mTeamOfficials is null"));
		return;
	}

	SIFRugbyCharacterList mCharacterListOfficials = mTeamOfficials->GetPlayers();
	if (mCharacterListOfficials.size() == 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("SSRoleReferee::ref_tackle_count() mCharacterListOfficials is null"));
		return;
	}

	if (mCharacterListOfficials.size() > 0)
	{
		ARugbyCharacter* m_character = mCharacterListOfficials[0];
		if (!m_character)
		{
			UE_LOG(LogTemp, Warning, TEXT("SSRoleReferee::ref_tackle_count() m_character is null"));
			return;
		}
		if (m_character)
		{		
			UE_LOG(LogTemp, Warning, TEXT("playing ref vocal"));
			switch (tackle_number)
			{
			case 1:
				m_character->GetSound()->PlayRefVocal("one");
				break;
			case 2:
				m_character->GetSound()->PlayRefVocal("two");
				break;
			case 3:
				m_character->GetSound()->PlayRefVocal("three");
				break;
			case 4:
				m_character->GetSound()->PlayRefVocal("four");
				break;
			case 5:
				m_character->GetSound()->PlayRefVocal("five");
				break;
			case 6:
				m_character->GetSound()->PlayRefVocal("six");
				break;

			default:
				break;
			}
			//m_character->GetSound()->PlayRefVocal("one");
		}
	
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("the first actor is not a rugby character"));
		}
	}
	else {
		UE_LOG(LogTemp, Warning, TEXT("no actor mated a rugby character"));
	}
}

void SSRoleReferee::tackle_held()
{
	SIFGameWorld* mWorld = SIFApplication::GetApplication()->GetActiveGameWorld();
	if (!mWorld)
	{
		UE_LOG(LogTemp, Warning, TEXT("SSRoleReferee::tackle_held() mWorld is null"));
		return;
	}
	RUTeam* mTeamOfficials = mWorld->GetOfficialsTeam();
	if (!mTeamOfficials)
	{
		UE_LOG(LogTemp, Warning, TEXT("SSRoleReferee::tackle_held() mTeamOfficials is null"));
		return;
	}

	SIFRugbyCharacterList mCharacterListOfficials = mTeamOfficials->GetPlayers();
	if (mCharacterListOfficials.size() == 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("SSRoleReferee::tackle_held() mCharacterListOfficials is null"));
		return;
	}

	if (mCharacterListOfficials.size() > 0)
	{
		ARugbyCharacter* m_character = mCharacterListOfficials[0];
		if (!m_character)
		{
			UE_LOG(LogTemp, Warning, TEXT("SSRoleReferee::tackle_held() m_character is null"));
			return;
		}
		if (m_character)
		{
			UE_LOG(LogTemp, Warning, TEXT("playing ref vocal"));
			//m_character->GetSound()->CreateSoundEffect("event:/sfx/ref_vocals/english/held");
			m_character->GetSound()->PlayRefVocal("held");
		}

		else
		{
			UE_LOG(LogTemp, Warning, TEXT("the first actor is not a rugby character"));
		}
	}
	else {
		UE_LOG(LogTemp, Warning, TEXT("no actor mated a rugby character"));
	}
}

void SSRoleReferee::tackle_high()
{
	SIFGameWorld* mWorld = SIFApplication::GetApplication()->GetActiveGameWorld();
	if (!mWorld)
	{
		UE_LOG(LogTemp, Warning, TEXT("SSRoleReferee::tackle_high() mWorld is null"));
		return;
	}
	RUTeam* mTeamOfficials = mWorld->GetOfficialsTeam();
	if (!mTeamOfficials)
	{
		UE_LOG(LogTemp, Warning, TEXT("SSRoleReferee::tackle_high() mTeamOfficials is null"));
		return;
	}

	SIFRugbyCharacterList mCharacterListOfficials = mTeamOfficials->GetPlayers();
	if (mCharacterListOfficials.size() == 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("SSRoleReferee::tackle_high() mCharacterListOfficials is null"));
		return;
	}

	if (mCharacterListOfficials.size() > 0)
	{
		ARugbyCharacter* m_character = mCharacterListOfficials[0];
		if (!m_character)
		{
			UE_LOG(LogTemp, Warning, TEXT("SSRoleReferee::tackle_high() m_character is null"));
			return;
		}
		if (m_character)
		{
			UE_LOG(LogTemp, Warning, TEXT("playing ref vocal"));
			//m_character->GetSound()->CreateSoundEffect("event:/sfx/ref_vocals/english/held");
			m_character->GetSound()->PlayRefVocal("hightackle");
		}

		else
		{
			UE_LOG(LogTemp, Warning, TEXT("the first actor is not a rugby character"));
		}
	}
	else {
		UE_LOG(LogTemp, Warning, TEXT("no actor mated a rugby character"));
	}
}