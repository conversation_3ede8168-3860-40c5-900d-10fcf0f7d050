[/Script/UnrealEd.EditorPerProjectUserSettings]
bDisplayDocumentationLink=False
bDisplayActionListItemRefIds=False
bAlwaysGatherBehaviorTreeDebuggerData=False
bDisplayBlackboardKeysInAlphabeticalOrder=False
bUseSimplygonSwarm=False
SimplygonServerIP=127.0.0.1
bEnableSwarmDebugging=False
SimplygonSwarmDelay=5000
SwarmNumOfConcurrentJobs=16
SwarmMaxUploadChunkSizeInMB=100
SwarmIntermediateFolder=W:/NRL/Intermediate/Simplygon/
bShowCompilerLogOnCompileError=False
DataSourceFolder=(Path="")
bAnimationReimportWarnings=False
bSCSEditorShowFloor=False
SCSViewportCameraSpeed=4
AssetViewerProfileName=
MaterialQualityLevel=1
PreviewFeatureLevel=3
PreviewPlatformName=None
PreviewShaderFormatName=None
bPreviewFeatureLevelActive=False
PreviewDeviceProfileName=None

[/Script/UnrealEd.LevelEditorPlaySettings]
LaptopScreenResolutions=(Description="Apple MacBook Air 11",Width=1366,Height=768,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Air 13\"",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 13\"",Width=1280,Height=800,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 13\" (Retina)",Width=2560,Height=1600,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 15\"",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 15\" (Retina)",Width=2880,Height=1800,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Generic 14-15.6\" Notebook",Width=1366,Height=768,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="19\" monitor",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="20\" monitor",Width=1600,Height=900,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="22\" monitor",Width=1680,Height=1050,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="21.5-24\" monitor",Width=1920,Height=1080,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="27\" monitor",Width=2560,Height=1440,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch (3rd gen.)",Width=1024,Height=1366,AspectRatio="~3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro3_129")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch (2nd gen.)",Width=1024,Height=1366,AspectRatio="~3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro2_129")
TabletScreenResolutions=(Description="iPad Pro 11-inch",Width=834,Height=1194,AspectRatio="5:7",bCanSwapAspectRatio=True,ProfileName="iPadPro11")
TabletScreenResolutions=(Description="iPad Pro 10.5-inch",Width=834,Height=1112,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro105")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch",Width=1024,Height=1366,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro129")
TabletScreenResolutions=(Description="iPad Pro 9.7-inch",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro97")
TabletScreenResolutions=(Description="iPad (6th gen.)",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPad6")
TabletScreenResolutions=(Description="iPad (5th gen.)",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPad5")
TabletScreenResolutions=(Description="iPad Air 3",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadAir3")
TabletScreenResolutions=(Description="iPad Air 2",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadAir2")
TabletScreenResolutions=(Description="iPad Mini 5",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadMini5")
TabletScreenResolutions=(Description="iPad Mini 4",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadMini4")
TabletScreenResolutions=(Description="LG G Pad X 8.0",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Asus Zenpad 3s 10",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Huawei MediaPad M3",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Microsoft Surface RT",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Microsoft Surface Pro",Width=1080,Height=1920,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="720p (HDTV, Blu-ray)",Width=1280,Height=720,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="1080i, 1080p (HDTV, Blu-ray)",Width=1920,Height=1080,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="4K Ultra HD",Width=3840,Height=2160,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="4K Digital Cinema",Width=4096,Height=2160,AspectRatio="1.90:1",bCanSwapAspectRatio=True,ProfileName="")
GameGetsMouseControl=False
UseMouseForTouch=False
MouseControlLabelPosition=LabelAnchorMode_TopLeft
ViewportGetsHMDControl=False
EnablePIEEnterAndExitSounds=False
PlayInEditorSoundQualityLevel=0
bUseNonRealtimeAudioDevice=False
bPreferToStreamLevelsInPIE=False
NewWindowPosition=(X=-1,Y=-1)
PIEAlwaysOnTop=False
DisableStandaloneSound=False
AdditionalLaunchParameters=
AdditionalLaunchParametersForMobile=
BuildGameBeforeLaunch=PlayOnBuild_Default
LaunchConfiguration=LaunchConfig_Default
PackFilesForLaunch=NoPak
bAutoCompileBlueprintsOnLaunch=True
bLaunchSeparateServer=False
PlayNetMode=PIE_Standalone
RunUnderOneProcess=True
PlayNetDedicated=False
PlayNumberOfClients=1
ServerPort=17777
ClientWindowWidth=640
AutoConnectToServer=True
RouteGamepadToSecondWindow=False
CreateAudioDeviceForEveryPlayer=False
ClientWindowHeight=480
ServerMapNameOverride=
AdditionalServerGameOptions=
AdditionalLaunchOptions=
bShowServerDebugDrawingByDefault=True
ServerDebugDrawingColorTintStrength=0.000000
ServerDebugDrawingColorTint=(R=0.000000,G=0.000000,B=0.000000,A=1.000000)
AdditionalServerLaunchParameters=
ServerFixedFPS=0
NetworkEmulationSettings=(bIsNetworkEmulationEnabled=False,EmulationTarget=Server,CurrentProfile="Custom",OutPackets=(MinLatency=0,MaxLatency=0,PacketLossPercentage=0),InPackets=(MinLatency=0,MaxLatency=0,PacketLossPercentage=0))
LastSize=(X=0,Y=0)
LastExecutedLaunchDevice=WindowsNoEditor@ALEXANDER-PC
LastExecutedLaunchName=ALEXANDER-PC
LastExecutedPIEPreviewDevice=
DeviceToEmulate=
PIESafeZoneOverride=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000)

[/Script/UnrealEd.LevelEditorViewportSettings]
FlightCameraControlExperimentalNavigation=False
MinimumOrthographicZoom=250.000000
bAllowArcballRotate=False
bAllowScreenRotate=False
SnapToSurface=(bEnabled=False,SnapOffsetExtent=0.000000,bSnapRotation=True)
bEnableLayerSnap=False
ActiveSnapLayerIndex=0
PreserveNonUniformScale=False
bUseLegacyPostEditBehavior=False
PreviewMeshes=/Engine/EditorMeshes/ColorCalibrator/SM_ColorCalibrator.SM_ColorCalibrator
BillboardScale=1.000000
TransformWidgetSizeAdjustment=0
bSaveEngineStats=False
MeasuringToolUnits=MeasureUnits_Centimeters
SelectedSplinePointSizeAdjustment=0.000000
SplineLineThicknessAdjustment=0.000000
SplineTangentHandleSizeAdjustment=0.000000
SplineTangentScale=1.000000
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport0",ConfigSettings=(ViewportType=LVT_OrthoYZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,LensFlares=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,Tessellation=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,ModeWidgets=1,Bounds=0,HitProxies=0,PropertyColoration=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,LevelColoration=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,VisualizeLPV=0,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,VisualizeGlobalDistanceField=0,ScreenSpaceAO=1,DistanceFieldAO=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,SingleLayerWaterRefractionFullPrecision=0",GameShowFlagsString="PostProcessing=0,Bloom=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,LensFlares=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,Tessellation=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,ModeWidgets=0,Bounds=0,HitProxies=0,PropertyColoration=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,LevelColoration=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,VisualizeLPV=0,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,VisualizeGlobalDistanceField=0,ScreenSpaceAO=1,DistanceFieldAO=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,SingleLayerWaterRefractionFullPrecision=0",BufferVisualizationMode="",RayTracingDebugVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport1",ConfigSettings=(ViewportType=LVT_Perspective,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=1,Bloom=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,LensFlares=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,Tessellation=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=1,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,ModeWidgets=1,Bounds=0,HitProxies=0,PropertyColoration=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,LevelColoration=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,VisualizeLPV=0,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,VisualizeGlobalDistanceField=0,ScreenSpaceAO=1,DistanceFieldAO=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,SingleLayerWaterRefractionFullPrecision=0",GameShowFlagsString="PostProcessing=1,Bloom=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,LensFlares=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,Tessellation=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=1,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,ModeWidgets=0,Bounds=0,HitProxies=0,PropertyColoration=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,LevelColoration=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,VisualizeLPV=0,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,VisualizeGlobalDistanceField=0,ScreenSpaceAO=1,DistanceFieldAO=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,SingleLayerWaterRefractionFullPrecision=0",BufferVisualizationMode="",RayTracingDebugVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=True,bShowOnScreenStats=False,EnabledStats=,bShowFullToolbar=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport2",ConfigSettings=(ViewportType=LVT_OrthoXZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,LensFlares=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,Tessellation=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,ModeWidgets=1,Bounds=0,HitProxies=0,PropertyColoration=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,LevelColoration=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,VisualizeLPV=0,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,VisualizeGlobalDistanceField=0,ScreenSpaceAO=1,DistanceFieldAO=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,SingleLayerWaterRefractionFullPrecision=0",GameShowFlagsString="PostProcessing=0,Bloom=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,LensFlares=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,Tessellation=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,ModeWidgets=0,Bounds=0,HitProxies=0,PropertyColoration=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,LevelColoration=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,VisualizeLPV=0,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,VisualizeGlobalDistanceField=0,ScreenSpaceAO=1,DistanceFieldAO=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,SingleLayerWaterRefractionFullPrecision=0",BufferVisualizationMode="",RayTracingDebugVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport3",ConfigSettings=(ViewportType=LVT_OrthoXY,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,LensFlares=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,Tessellation=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,ModeWidgets=1,Bounds=0,HitProxies=0,PropertyColoration=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,LevelColoration=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,VisualizeLPV=0,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,VisualizeGlobalDistanceField=0,ScreenSpaceAO=1,DistanceFieldAO=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,SingleLayerWaterRefractionFullPrecision=0",GameShowFlagsString="PostProcessing=0,Bloom=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,LensFlares=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,Tessellation=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,ModeWidgets=0,Bounds=0,HitProxies=0,PropertyColoration=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,LevelColoration=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,VisualizeLPV=0,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,VisualizeGlobalDistanceField=0,ScreenSpaceAO=1,DistanceFieldAO=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,SingleLayerWaterRefractionFullPrecision=0",BufferVisualizationMode="",RayTracingDebugVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True))

[ContentBrowser]
ContentBrowserTab1.SelectedPaths=
ContentBrowserTab1.SourcesExpanded=False
ContentBrowserTab1.Locked=False
ContentBrowserTab1.VerticalSplitter.SlotSize0=0.250000
ContentBrowserTab1.VerticalSplitter.SlotSize1=0.750000
ContentBrowserTab1.FavoriteSplitter.SlotSize0=0.200000
ContentBrowserTab1.FavoriteSplitter.SlotSize1=0.800000
ContentBrowserTab1.FavoriteSplitter.SlotSize2=0.400000
ContentBrowserTab1.ActiveTypeFilters=
ContentBrowserTab1.EnabledTypeFilters=
ContentBrowserTab1.ActiveFrontendFilters=
ContentBrowserTab1.EnabledFrontendFilters=
ContentBrowserTab1.PluginFilters=
ContentBrowserTab1.Favorites.SelectedPaths=
FavoritePaths=
ContentBrowserTab1.CollectionsExpanded=True
ContentBrowserTab1.SelectedCollections=
ContentBrowserTab1.ExpandedCollections=
ContentBrowserTab1.ViewMode=0
ContentBrowserTab1.ThumbnailSizeScale=0.180000
ContentBrowserTab1.CurrentViewType=1

[EditorStartup]
LastLevel=/Game/Maps/ContainerMap

[ModuleFileTracking]
WWUI.TimeStamp=2025.07.02-02.53.29
WWUI.LastCompileMethod=Unknown
AnimationBudgetAllocator.TimeStamp=2025.07.02-02.53.33
AnimationBudgetAllocator.LastCompileMethod=Unknown
Rugby.TimeStamp=2025.07.17-02.26.28
Rugby.LastCompileMethod=External
PakFile.TimeStamp=2025.07.02-02.53.11
PakFile.LastCompileMethod=Unknown
RSA.TimeStamp=2025.07.02-02.53.10
RSA.LastCompileMethod=Unknown
SandboxFile.TimeStamp=2025.07.02-02.53.12
SandboxFile.LastCompileMethod=Unknown
StreamingFile.TimeStamp=2025.07.02-02.53.10
StreamingFile.LastCompileMethod=Unknown
CookedIterativeFile.TimeStamp=2025.07.02-02.53.10
CookedIterativeFile.LastCompileMethod=Unknown
NetworkFile.TimeStamp=2025.07.02-02.53.09
NetworkFile.LastCompileMethod=Unknown
CoreUObject.TimeStamp=2025.07.02-02.53.08
CoreUObject.LastCompileMethod=Unknown
Engine.TimeStamp=2025.07.02-02.54.20
Engine.LastCompileMethod=Unknown
Renderer.TimeStamp=2025.07.02-02.53.31
Renderer.LastCompileMethod=Unknown
AnimGraphRuntime.TimeStamp=2025.07.02-02.53.27
AnimGraphRuntime.LastCompileMethod=Unknown
SlateRHIRenderer.TimeStamp=2025.07.02-02.53.27
SlateRHIRenderer.LastCompileMethod=Unknown
Landscape.TimeStamp=2025.07.02-02.53.46
Landscape.LastCompileMethod=Unknown
RenderCore.TimeStamp=2025.07.02-02.53.07
RenderCore.LastCompileMethod=Unknown
TextureCompressor.TimeStamp=2025.07.02-02.53.39
TextureCompressor.LastCompileMethod=Unknown
AudioEditor.TimeStamp=2025.07.02-02.53.46
AudioEditor.LastCompileMethod=Unknown
PropertyEditor.TimeStamp=2025.07.02-02.54.00
PropertyEditor.LastCompileMethod=Unknown
AnimationModifiers.TimeStamp=2025.07.02-02.53.59
AnimationModifiers.LastCompileMethod=Unknown
XGEController.TimeStamp=2025.07.02-02.53.14
XGEController.LastCompileMethod=Unknown
OodleDataCompressionFormat.TimeStamp=2025.07.02-02.54.01
OodleDataCompressionFormat.LastCompileMethod=Unknown
PlatformCrypto.TimeStamp=2025.07.02-02.53.14
PlatformCrypto.LastCompileMethod=Unknown
PlatformCryptoTypes.TimeStamp=2025.07.02-02.53.12
PlatformCryptoTypes.LastCompileMethod=Unknown
PlatformCryptoOpenSSL.TimeStamp=2025.07.02-02.53.14
PlatformCryptoOpenSSL.LastCompileMethod=Unknown
PythonScriptPluginPreload.TimeStamp=2025.07.02-02.53.14
PythonScriptPluginPreload.LastCompileMethod=Unknown
DesktopPlatform.TimeStamp=2025.07.02-02.53.10
DesktopPlatform.LastCompileMethod=Unknown
AISupportModule.TimeStamp=2025.07.02-02.53.41
AISupportModule.LastCompileMethod=Unknown
NiagaraShader.TimeStamp=2025.07.02-02.53.27
NiagaraShader.LastCompileMethod=Unknown
NiagaraVertexFactories.TimeStamp=2025.07.02-02.53.34
NiagaraVertexFactories.LastCompileMethod=Unknown
VariantManagerContent.TimeStamp=2025.07.02-02.53.57
VariantManagerContent.LastCompileMethod=Unknown
PixWinPlugin.TimeStamp=2025.07.02-02.53.13
PixWinPlugin.LastCompileMethod=Unknown
ChaosCloth.TimeStamp=2025.07.02-02.53.27
ChaosCloth.LastCompileMethod=Unknown
MLSDK.TimeStamp=2025.07.02-02.53.14
MLSDK.LastCompileMethod=Unknown
OnlineSubsystem.TimeStamp=2025.07.02-02.53.08
OnlineSubsystem.LastCompileMethod=Unknown
HTTP.TimeStamp=2025.07.02-02.53.08
HTTP.LastCompileMethod=Unknown
SSL.TimeStamp=2025.07.02-02.53.10
SSL.LastCompileMethod=Unknown
OnlineSubsystemSteam.TimeStamp=2025.07.02-02.53.37
OnlineSubsystemSteam.LastCompileMethod=Unknown
SteamShared.TimeStamp=2025.07.02-02.53.10
SteamShared.LastCompileMethod=Unknown
OnlineSubsystemNULL.TimeStamp=2025.07.02-02.53.38
OnlineSubsystemNULL.LastCompileMethod=Unknown
Sockets.TimeStamp=2025.07.02-02.53.14
Sockets.LastCompileMethod=Unknown
OnlineSubsystemUtils.TimeStamp=2025.07.02-02.53.37
OnlineSubsystemUtils.LastCompileMethod=Unknown
OnlineBlueprintSupport.TimeStamp=2025.07.02-02.53.40
OnlineBlueprintSupport.LastCompileMethod=Unknown
ChunkDownloader.TimeStamp=2025.07.02-02.53.13
ChunkDownloader.LastCompileMethod=Unknown
OpenXRHMD.TimeStamp=2025.07.02-02.54.11
OpenXRHMD.LastCompileMethod=Unknown
OpenXRAR.TimeStamp=2025.07.02-02.54.00
OpenXRAR.LastCompileMethod=Unknown
AugmentedReality.TimeStamp=2025.07.02-02.53.52
AugmentedReality.LastCompileMethod=Unknown
OpenXREyeTracker.TimeStamp=2025.07.02-02.54.05
OpenXREyeTracker.LastCompileMethod=Unknown
OpenXRHandTracking.TimeStamp=2025.07.02-02.54.10
OpenXRHandTracking.LastCompileMethod=Unknown
ExampleDeviceProfileSelector.TimeStamp=
ExampleDeviceProfileSelector.LastCompileMethod=Unknown
WindowsPlatformFeatures.TimeStamp=2025.07.02-02.53.31
WindowsPlatformFeatures.LastCompileMethod=Unknown
GameplayMediaEncoder.TimeStamp=2025.07.02-02.53.37
GameplayMediaEncoder.LastCompileMethod=Unknown
AVEncoder.TimeStamp=2025.07.02-02.53.38
AVEncoder.LastCompileMethod=Unknown
Chaos.TimeStamp=2025.07.02-02.53.15
Chaos.LastCompileMethod=Unknown
PhysXCooking.TimeStamp=2025.07.02-02.53.06
PhysXCooking.LastCompileMethod=Unknown
DirectoryWatcher.TimeStamp=2025.07.02-02.53.12
DirectoryWatcher.LastCompileMethod=Unknown
Settings.TimeStamp=2025.07.02-02.53.11
Settings.LastCompileMethod=Unknown
InputCore.TimeStamp=2025.07.02-02.53.10
InputCore.LastCompileMethod=Unknown
D3D11RHI.TimeStamp=2025.07.02-02.53.27
D3D11RHI.LastCompileMethod=Unknown
TargetPlatform.TimeStamp=2025.07.02-02.53.08
TargetPlatform.LastCompileMethod=Unknown
AllDesktopTargetPlatform.TimeStamp=2025.07.02-02.53.38
AllDesktopTargetPlatform.LastCompileMethod=Unknown
AndroidTargetPlatform.TimeStamp=2025.07.02-02.53.33
AndroidTargetPlatform.LastCompileMethod=Unknown
IOSTargetPlatform.TimeStamp=2025.07.02-02.53.37
IOSTargetPlatform.LastCompileMethod=Unknown
Messaging.TimeStamp=2025.07.02-02.53.11
Messaging.LastCompileMethod=Unknown
LuminTargetPlatform.TimeStamp=2025.07.02-02.53.38
LuminTargetPlatform.LastCompileMethod=Unknown
MacNoEditorTargetPlatform.TimeStamp=2025.07.02-02.53.39
MacNoEditorTargetPlatform.LastCompileMethod=Unknown
MacTargetPlatform.TimeStamp=2025.07.02-02.53.42
MacTargetPlatform.LastCompileMethod=Unknown
MacClientTargetPlatform.TimeStamp=2025.07.02-02.53.40
MacClientTargetPlatform.LastCompileMethod=Unknown
MacServerTargetPlatform.TimeStamp=2025.07.02-02.53.39
MacServerTargetPlatform.LastCompileMethod=Unknown
TVOSTargetPlatform.TimeStamp=2025.07.02-02.53.41
TVOSTargetPlatform.LastCompileMethod=Unknown
WindowsNoEditorTargetPlatform.TimeStamp=2025.07.02-02.53.39
WindowsNoEditorTargetPlatform.LastCompileMethod=Unknown
WindowsTargetPlatform.TimeStamp=2025.07.02-02.53.31
WindowsTargetPlatform.LastCompileMethod=Unknown
WindowsClientTargetPlatform.TimeStamp=2025.07.02-02.53.40
WindowsClientTargetPlatform.LastCompileMethod=Unknown
WindowsServerTargetPlatform.TimeStamp=2025.07.02-02.53.40
WindowsServerTargetPlatform.LastCompileMethod=Unknown
SwitchTargetPlatform.TimeStamp=2025.07.10-02.52.42
SwitchTargetPlatform.LastCompileMethod=Unknown
AudioFormatADPCM.TimeStamp=2025.07.02-02.53.40
AudioFormatADPCM.LastCompileMethod=Unknown
AudioFormatOgg.TimeStamp=2025.07.02-02.53.41
AudioFormatOgg.LastCompileMethod=Unknown
AudioFormatOpus.TimeStamp=2025.07.02-02.53.40
AudioFormatOpus.LastCompileMethod=Unknown
SwitchAudioFormat.TimeStamp=2025.07.10-02.52.44
SwitchAudioFormat.LastCompileMethod=Unknown
TextureFormatASTC.TimeStamp=2025.07.02-02.53.14
TextureFormatASTC.LastCompileMethod=Unknown
ImageWrapper.TimeStamp=2025.07.02-02.53.06
ImageWrapper.LastCompileMethod=Unknown
TextureFormatIntelISPCTexComp.TimeStamp=2025.07.02-02.53.09
TextureFormatIntelISPCTexComp.LastCompileMethod=Unknown
TextureFormatDXT.TimeStamp=2025.07.02-02.53.13
TextureFormatDXT.LastCompileMethod=Unknown
TextureFormatETC2.TimeStamp=2025.07.02-02.53.13
TextureFormatETC2.LastCompileMethod=Unknown
TextureFormatPVR.TimeStamp=2025.07.02-02.53.13
TextureFormatPVR.LastCompileMethod=Unknown
TextureFormatUncompressed.TimeStamp=2025.07.02-02.53.14
TextureFormatUncompressed.LastCompileMethod=Unknown
SwitchTextureFormat.TimeStamp=2025.07.10-02.52.44
SwitchTextureFormat.LastCompileMethod=Unknown
TextureFormatOodle.TimeStamp=2025.07.02-02.53.37
TextureFormatOodle.LastCompileMethod=Unknown
MetalShaderFormat.TimeStamp=2025.07.02-02.53.14
MetalShaderFormat.LastCompileMethod=Unknown
ShaderFormatD3D.TimeStamp=2025.07.02-02.53.14
ShaderFormatD3D.LastCompileMethod=Unknown
ShaderFormatOpenGL.TimeStamp=2025.07.02-02.53.13
ShaderFormatOpenGL.LastCompileMethod=Unknown
ShaderFormatVectorVM.TimeStamp=2025.07.02-02.53.08
ShaderFormatVectorVM.LastCompileMethod=Unknown
VulkanShaderFormat.TimeStamp=2025.07.02-02.53.13
VulkanShaderFormat.LastCompileMethod=Unknown
SwitchShaderFormat.TimeStamp=2025.07.10-02.52.43
SwitchShaderFormat.LastCompileMethod=Unknown
DerivedDataCache.TimeStamp=2025.07.02-02.53.08
DerivedDataCache.LastCompileMethod=Unknown
AssetRegistry.TimeStamp=2025.07.02-02.53.09
AssetRegistry.LastCompileMethod=Unknown
MeshUtilities.TimeStamp=2025.07.02-02.54.09
MeshUtilities.LastCompileMethod=Unknown
MaterialBaking.TimeStamp=2025.07.02-02.53.48
MaterialBaking.LastCompileMethod=Unknown
MeshMergeUtilities.TimeStamp=2025.07.02-02.54.08
MeshMergeUtilities.LastCompileMethod=Unknown
MeshReductionInterface.TimeStamp=2025.07.02-02.53.36
MeshReductionInterface.LastCompileMethod=Unknown
QuadricMeshReduction.TimeStamp=2025.07.02-02.53.32
QuadricMeshReduction.LastCompileMethod=Unknown
ProxyLODMeshReduction.TimeStamp=2025.07.02-02.54.02
ProxyLODMeshReduction.LastCompileMethod=Unknown
SkeletalMeshReduction.TimeStamp=2025.07.02-02.53.59
SkeletalMeshReduction.LastCompileMethod=Unknown
MeshBoneReduction.TimeStamp=2025.07.02-02.53.33
MeshBoneReduction.LastCompileMethod=Unknown
MeshBuilder.TimeStamp=2025.07.02-02.53.37
MeshBuilder.LastCompileMethod=Unknown
KismetCompiler.TimeStamp=2025.07.02-02.53.54
KismetCompiler.LastCompileMethod=Unknown
MovieSceneTools.TimeStamp=2025.07.02-02.54.11
MovieSceneTools.LastCompileMethod=Unknown
Sequencer.TimeStamp=2025.07.02-02.54.05
Sequencer.LastCompileMethod=Unknown
EditorStyle.TimeStamp=2025.07.02-02.53.09
EditorStyle.LastCompileMethod=Unknown
CurveEditor.TimeStamp=2025.07.02-02.53.53
CurveEditor.LastCompileMethod=Unknown
MaterialEditor.TimeStamp=2025.07.02-02.53.48
MaterialEditor.LastCompileMethod=Unknown
Media.TimeStamp=2025.07.02-02.53.12
Media.LastCompileMethod=Unknown
MediaInfo.TimeStamp=2025.07.02-02.53.13
MediaInfo.LastCompileMethod=Unknown
SwitchMediaInfo.TimeStamp=2025.07.10-02.52.45
SwitchMediaInfo.LastCompileMethod=Unknown
Core.TimeStamp=2025.07.02-02.52.56
Core.LastCompileMethod=Unknown
Networking.TimeStamp=2025.07.02-02.53.11
Networking.LastCompileMethod=Unknown
XAudio2.TimeStamp=2025.07.02-02.53.27
XAudio2.LastCompileMethod=Unknown
HeadMountedDisplay.TimeStamp=2025.07.02-02.53.50
HeadMountedDisplay.LastCompileMethod=Unknown
SourceCodeAccess.TimeStamp=2025.07.02-02.53.09
SourceCodeAccess.LastCompileMethod=Unknown
MRMesh.TimeStamp=2025.07.02-02.53.57
MRMesh.LastCompileMethod=Unknown
UnrealEd.TimeStamp=2025.07.02-02.53.56
UnrealEd.LastCompileMethod=Unknown
LandscapeEditorUtilities.TimeStamp=2025.07.02-02.53.34
LandscapeEditorUtilities.LastCompileMethod=Unknown
SlateCore.TimeStamp=2025.07.02-02.53.07
SlateCore.LastCompileMethod=Unknown
Slate.TimeStamp=2025.07.02-02.53.07
Slate.LastCompileMethod=Unknown
SlateReflector.TimeStamp=2025.07.02-02.53.50
SlateReflector.LastCompileMethod=Unknown
UMG.TimeStamp=2025.07.02-02.53.28
UMG.LastCompileMethod=Unknown
UMGEditor.TimeStamp=2025.07.02-02.54.10
UMGEditor.LastCompileMethod=Unknown
AssetTools.TimeStamp=2025.07.02-02.54.04
AssetTools.LastCompileMethod=Unknown
MessageLog.TimeStamp=2025.07.02-02.53.08
MessageLog.LastCompileMethod=Unknown
CollisionAnalyzer.TimeStamp=2025.07.02-02.53.31
CollisionAnalyzer.LastCompileMethod=Unknown
WorkspaceMenuStructure.TimeStamp=2025.07.02-02.53.10
WorkspaceMenuStructure.LastCompileMethod=Unknown
FunctionalTesting.TimeStamp=2025.07.02-02.53.48
FunctionalTesting.LastCompileMethod=Unknown
BehaviorTreeEditor.TimeStamp=2025.07.02-02.54.07
BehaviorTreeEditor.LastCompileMethod=Unknown
GameplayTasksEditor.TimeStamp=2025.07.02-02.54.03
GameplayTasksEditor.LastCompileMethod=Unknown
StringTableEditor.TimeStamp=2025.07.02-02.54.01
StringTableEditor.LastCompileMethod=Unknown
VREditor.TimeStamp=2025.07.02-02.53.45
VREditor.LastCompileMethod=Unknown
Overlay.TimeStamp=2025.07.02-02.53.31
Overlay.LastCompileMethod=Unknown
OverlayEditor.TimeStamp=2025.07.02-02.54.06
OverlayEditor.LastCompileMethod=Unknown
MediaAssets.TimeStamp=2025.07.02-02.53.30
MediaAssets.LastCompileMethod=Unknown
ClothingSystemRuntimeNv.TimeStamp=2025.07.02-02.53.28
ClothingSystemRuntimeNv.LastCompileMethod=Unknown
ClothingSystemEditor.TimeStamp=2025.07.02-02.53.45
ClothingSystemEditor.LastCompileMethod=Unknown
PacketHandler.TimeStamp=2025.07.02-02.53.13
PacketHandler.LastCompileMethod=Unknown
NetworkReplayStreaming.TimeStamp=2025.07.02-02.53.11
NetworkReplayStreaming.LastCompileMethod=Unknown
EnvironmentQueryEditor.TimeStamp=2025.07.02-02.54.04
EnvironmentQueryEditor.LastCompileMethod=Unknown
GameplayCameras.TimeStamp=2025.07.02-02.53.31
GameplayCameras.LastCompileMethod=Unknown
AssetManagerEditor.TimeStamp=2025.07.02-02.54.04
AssetManagerEditor.LastCompileMethod=Unknown
TreeMap.TimeStamp=2025.07.02-02.53.09
TreeMap.LastCompileMethod=Unknown
ContentBrowser.TimeStamp=2025.07.02-02.53.48
ContentBrowser.LastCompileMethod=Unknown
ContentBrowserData.TimeStamp=2025.07.02-02.53.54
ContentBrowserData.LastCompileMethod=Unknown
ToolMenus.TimeStamp=2025.07.02-02.53.08
ToolMenus.LastCompileMethod=Unknown
LevelEditor.TimeStamp=2025.07.02-02.54.11
LevelEditor.LastCompileMethod=Unknown
MainFrame.TimeStamp=2025.07.02-02.54.10
MainFrame.LastCompileMethod=Unknown
HotReload.TimeStamp=2025.07.02-02.53.57
HotReload.LastCompileMethod=Unknown
CommonMenuExtensions.TimeStamp=2025.07.02-02.53.48
CommonMenuExtensions.LastCompileMethod=Unknown
PixelInspectorModule.TimeStamp=2025.07.02-02.53.53
PixelInspectorModule.LastCompileMethod=Unknown
FacialAnimation.TimeStamp=2025.07.02-02.53.33
FacialAnimation.LastCompileMethod=Unknown
FacialAnimationEditor.TimeStamp=2025.07.02-02.54.08
FacialAnimationEditor.LastCompileMethod=Unknown
GameplayTagsEditor.TimeStamp=2025.07.02-02.54.07
GameplayTagsEditor.LastCompileMethod=Unknown
AnimationSharing.TimeStamp=2025.07.02-02.53.39
AnimationSharing.LastCompileMethod=Unknown
NiagaraCore.TimeStamp=2025.07.02-02.53.36
NiagaraCore.LastCompileMethod=Unknown
Niagara.TimeStamp=2025.07.02-02.54.03
Niagara.LastCompileMethod=Unknown
NiagaraEditor.TimeStamp=2025.07.02-02.54.14
NiagaraEditor.LastCompileMethod=Unknown
SignalProcessing.TimeStamp=2025.07.02-02.53.07
SignalProcessing.LastCompileMethod=Unknown
NiagaraAnimNotifies.TimeStamp=2025.07.02-02.53.42
NiagaraAnimNotifies.LastCompileMethod=Unknown
PropertyAccessNode.TimeStamp=2025.07.02-02.54.08
PropertyAccessNode.LastCompileMethod=Unknown
Gauntlet.TimeStamp=2025.07.02-02.53.36
Gauntlet.LastCompileMethod=Unknown
PythonScriptPlugin.TimeStamp=2025.07.02-02.53.59
PythonScriptPlugin.LastCompileMethod=Unknown
MagicLeapARPin.TimeStamp=2025.07.02-02.53.59
MagicLeapARPin.LastCompileMethod=Unknown
TcpMessaging.TimeStamp=2025.07.02-02.53.14
TcpMessaging.LastCompileMethod=Unknown
UdpMessaging.TimeStamp=2025.07.02-02.53.14
UdpMessaging.LastCompileMethod=Unknown
ActorSequence.TimeStamp=2025.07.02-02.53.31
ActorSequence.LastCompileMethod=Unknown
AudioSynesthesiaCore.TimeStamp=2025.07.02-02.53.11
AudioSynesthesiaCore.LastCompileMethod=Unknown
AudioSynesthesia.TimeStamp=2025.07.02-02.53.30
AudioSynesthesia.LastCompileMethod=Unknown
PropertyAccessEditor.TimeStamp=2025.07.02-02.53.52
PropertyAccessEditor.LastCompileMethod=Unknown
SignificanceManager.TimeStamp=2025.07.02-02.53.34
SignificanceManager.LastCompileMethod=Unknown
RuntimePhysXCooking.TimeStamp=2025.07.02-02.53.41
RuntimePhysXCooking.LastCompileMethod=Unknown
SoundFields.TimeStamp=2025.07.02-02.53.42
SoundFields.LastCompileMethod=Unknown
LightPropagationVolumeRuntime.TimeStamp=2025.07.02-02.53.34
LightPropagationVolumeRuntime.LastCompileMethod=Unknown
FMODStudio.TimeStamp=2025.07.16-06.26.52
FMODStudio.LastCompileMethod=Unknown
wwHeadImportEditor.TimeStamp=2025.07.16-06.26.30
wwHeadImportEditor.LastCompileMethod=Unknown
wwStadiumRuntime.TimeStamp=2025.07.16-06.26.26
wwStadiumRuntime.LastCompileMethod=Unknown
wwStadiumTools.TimeStamp=2025.07.16-06.26.30
wwStadiumTools.LastCompileMethod=Unknown
MeshPaintEditorMode.TimeStamp=2025.07.02-02.54.08
MeshPaintEditorMode.LastCompileMethod=Unknown
MeshPaintingToolset.TimeStamp=2025.07.02-02.54.00
MeshPaintingToolset.LastCompileMethod=Unknown
MorphToolsRuntime.TimeStamp=2025.07.02-02.53.36
MorphToolsRuntime.LastCompileMethod=Unknown
RMAMirrorAnimation.TimeStamp=2025.07.02-02.53.57
RMAMirrorAnimation.LastCompileMethod=Unknown
RMAMirrorAnimationEditor.TimeStamp=2025.07.02-02.53.50
RMAMirrorAnimationEditor.LastCompileMethod=Unknown
WWUIEditor.TimeStamp=2025.07.02-02.54.04
WWUIEditor.LastCompileMethod=Unknown
LiveLink.TimeStamp=2025.07.02-02.53.29
LiveLink.LastCompileMethod=Unknown
LiveLinkComponents.TimeStamp=2025.07.02-02.53.57
LiveLinkComponents.LastCompileMethod=Unknown
LiveLinkEditor.TimeStamp=2025.07.02-02.54.10
LiveLinkEditor.LastCompileMethod=Unknown
LiveLinkGraphNode.TimeStamp=2025.07.02-02.53.50
LiveLinkGraphNode.LastCompileMethod=Unknown
LiveLinkMovieScene.TimeStamp=2025.07.02-02.53.33
LiveLinkMovieScene.LastCompileMethod=Unknown
LiveLinkSequencer.TimeStamp=2025.07.02-02.54.06
LiveLinkSequencer.LastCompileMethod=Unknown
OodleNetworkHandlerComponent.TimeStamp=2025.07.02-02.53.40
OodleNetworkHandlerComponent.LastCompileMethod=Unknown
CryptoKeys.TimeStamp=2025.07.02-02.54.09
CryptoKeys.LastCompileMethod=Unknown
CryptoKeysOpenSSL.TimeStamp=2025.07.02-02.53.10
CryptoKeysOpenSSL.LastCompileMethod=Unknown
CurveEditorTools.TimeStamp=2025.07.02-02.54.05
CurveEditorTools.LastCompileMethod=Unknown
MaterialAnalyzer.TimeStamp=2025.07.02-02.54.06
MaterialAnalyzer.LastCompileMethod=Unknown
PluginBrowser.TimeStamp=2025.07.02-02.54.08
PluginBrowser.LastCompileMethod=Unknown
AnimationSharingEd.TimeStamp=2025.07.02-02.54.08
AnimationSharingEd.LastCompileMethod=Unknown
NiagaraEditorWidgets.TimeStamp=2025.07.02-02.54.05
NiagaraEditorWidgets.LastCompileMethod=Unknown
VariantManagerContentEditor.TimeStamp=2025.07.02-02.54.09
VariantManagerContentEditor.LastCompileMethod=Unknown
AutomationUtils.TimeStamp=2025.07.02-02.53.41
AutomationUtils.LastCompileMethod=Unknown
AutomationUtilsEditor.TimeStamp=2025.07.02-02.53.42
AutomationUtilsEditor.LastCompileMethod=Unknown
PlasticSourceControl.TimeStamp=2025.07.02-02.54.01
PlasticSourceControl.LastCompileMethod=Unknown
SourceControl.TimeStamp=2025.07.02-02.53.54
SourceControl.LastCompileMethod=Unknown
PluginUtils.TimeStamp=2025.07.02-02.53.08
PluginUtils.LastCompileMethod=Unknown
ChaosClothEditor.TimeStamp=2025.07.02-02.54.02
ChaosClothEditor.LastCompileMethod=Unknown
ChaosSolverEditor.TimeStamp=2025.07.02-02.54.05
ChaosSolverEditor.LastCompileMethod=Unknown
FieldSystemEngine.TimeStamp=2025.07.02-02.53.31
FieldSystemEngine.LastCompileMethod=Unknown
SubversionSourceControl.TimeStamp=2025.07.02-02.53.13
SubversionSourceControl.LastCompileMethod=Unknown
VisualStudioSourceCodeAccess.TimeStamp=2025.07.02-02.53.13
VisualStudioSourceCodeAccess.LastCompileMethod=Unknown
VisualStudioCodeSourceCodeAccess.TimeStamp=2025.07.02-02.53.13
VisualStudioCodeSourceCodeAccess.LastCompileMethod=Unknown
GeometryCacheEd.TimeStamp=2025.07.02-02.54.06
GeometryCacheEd.LastCompileMethod=Unknown
GeometryCacheSequencer.TimeStamp=2025.07.02-02.54.08
GeometryCacheSequencer.LastCompileMethod=Unknown
GeometryCacheStreamer.TimeStamp=2025.07.02-02.53.14
GeometryCacheStreamer.LastCompileMethod=Unknown
GeometryCache.TimeStamp=2025.07.02-02.53.30
GeometryCache.LastCompileMethod=Unknown
GeometryCacheTracks.TimeStamp=2025.07.02-02.53.30
GeometryCacheTracks.LastCompileMethod=Unknown
GeometricObjects.TimeStamp=2025.07.02-02.53.11
GeometricObjects.LastCompileMethod=Unknown
GeometryAlgorithms.TimeStamp=2025.07.02-02.53.11
GeometryAlgorithms.LastCompileMethod=Unknown
DynamicMesh.TimeStamp=2025.07.02-02.53.10
DynamicMesh.LastCompileMethod=Unknown
MeshConversion.TimeStamp=2025.07.02-02.53.08
MeshConversion.LastCompileMethod=Unknown
RiderSourceCodeAccess.TimeStamp=2025.07.02-02.54.01
RiderSourceCodeAccess.LastCompileMethod=Unknown
UObjectPlugin.TimeStamp=2025.07.02-02.53.14
UObjectPlugin.LastCompileMethod=Unknown
OpenImageDenoise.TimeStamp=2025.07.02-02.53.39
OpenImageDenoise.LastCompileMethod=Unknown
MotoSynth.TimeStamp=2025.07.02-02.53.31
MotoSynth.LastCompileMethod=Unknown
MotoSynthEditor.TimeStamp=2025.07.02-02.54.08
MotoSynthEditor.LastCompileMethod=Unknown
FractureEditor.TimeStamp=2025.07.02-02.54.11
FractureEditor.LastCompileMethod=Unknown
ChaosNiagara.TimeStamp=2025.07.02-02.53.42
ChaosNiagara.LastCompileMethod=Unknown
GeometryCollectionEditor.TimeStamp=2025.07.02-02.53.48
GeometryCollectionEditor.LastCompileMethod=Unknown
GeometryCollectionTracks.TimeStamp=2025.07.02-02.53.29
GeometryCollectionTracks.LastCompileMethod=Unknown
GeometryCollectionSequencer.TimeStamp=2025.07.02-02.54.04
GeometryCollectionSequencer.LastCompileMethod=Unknown
MagicLeapSharedWorld.TimeStamp=2025.07.02-02.53.42
MagicLeapSharedWorld.LastCompileMethod=Unknown
TemplateSequence.TimeStamp=2025.07.02-02.53.30
TemplateSequence.LastCompileMethod=Unknown
ActorLayerUtilities.TimeStamp=2025.07.02-02.53.34
ActorLayerUtilities.LastCompileMethod=Unknown
ActorLayerUtilitiesEditor.TimeStamp=2025.07.02-02.54.10
ActorLayerUtilitiesEditor.LastCompileMethod=Unknown
WWSteamNetworkCommunicationP2P.TimeStamp=2025.07.02-02.53.36
WWSteamNetworkCommunicationP2P.LastCompileMethod=Unknown
AssetTags.TimeStamp=2025.07.02-02.53.41
AssetTags.LastCompileMethod=Unknown
CableComponent.TimeStamp=2025.07.02-02.53.41
CableComponent.LastCompileMethod=Unknown
CustomMeshComponent.TimeStamp=2025.07.02-02.53.42
CustomMeshComponent.LastCompileMethod=Unknown
EditableMesh.TimeStamp=2025.07.02-02.53.43
EditableMesh.LastCompileMethod=Unknown
GooglePAD.TimeStamp=2025.07.02-02.53.42
GooglePAD.LastCompileMethod=Unknown
OpenXREditor.TimeStamp=2025.07.02-02.53.13
OpenXREditor.LastCompileMethod=Unknown
ScreenshotTools.TimeStamp=2025.07.02-02.53.33
ScreenshotTools.LastCompileMethod=Unknown
ProceduralMeshComponent.TimeStamp=2025.07.02-02.53.29
ProceduralMeshComponent.LastCompileMethod=Unknown
ProceduralMeshComponentEditor.TimeStamp=2025.07.02-02.54.00
ProceduralMeshComponentEditor.LastCompileMethod=Unknown
Synthesis.TimeStamp=2025.07.02-02.53.27
Synthesis.LastCompileMethod=Unknown
SynthesisEditor.TimeStamp=2025.07.02-02.54.09
SynthesisEditor.LastCompileMethod=Unknown
TakeMovieScene.TimeStamp=2025.07.02-02.53.33
TakeMovieScene.LastCompileMethod=Unknown
TakeSequencer.TimeStamp=2025.07.02-02.54.00
TakeSequencer.LastCompileMethod=Unknown
LightPropagationVolumeEditor.TimeStamp=2025.07.02-02.54.10
LightPropagationVolumeEditor.LastCompileMethod=Unknown
ContentBrowserClassDataSource.TimeStamp=2025.07.02-02.54.04
ContentBrowserClassDataSource.LastCompileMethod=Unknown
CollectionManager.TimeStamp=2025.07.02-02.53.10
CollectionManager.LastCompileMethod=Unknown
ContentBrowserAssetDataSource.TimeStamp=2025.07.02-02.54.04
ContentBrowserAssetDataSource.LastCompileMethod=Unknown
ContentBrowserFileDataSource.TimeStamp=2025.07.02-02.53.55
ContentBrowserFileDataSource.LastCompileMethod=Unknown
FMODStudioEditor.TimeStamp=2025.07.16-06.26.52
FMODStudioEditor.LastCompileMethod=Unknown
wwHttpModule.TimeStamp=2025.07.16-06.26.26
wwHttpModule.LastCompileMethod=Unknown
RugbyEditor.TimeStamp=2025.07.16-06.30.33
RugbyEditor.LastCompileMethod=Unknown
AudioSynesthesiaEditor.TimeStamp=2025.07.02-02.54.09
AudioSynesthesiaEditor.LastCompileMethod=Unknown
TaskGraph.TimeStamp=2025.07.02-02.53.31
TaskGraph.LastCompileMethod=Unknown
ProfilerService.TimeStamp=2025.07.02-02.53.42
ProfilerService.LastCompileMethod=Unknown
ImageWriteQueue.TimeStamp=2025.07.02-02.53.33
ImageWriteQueue.LastCompileMethod=Unknown
AIModule.TimeStamp=2025.07.02-02.54.00
AIModule.LastCompileMethod=Unknown
NavigationSystem.TimeStamp=2025.07.02-02.53.47
NavigationSystem.LastCompileMethod=Unknown
AITestSuite.TimeStamp=2025.07.02-02.53.33
AITestSuite.LastCompileMethod=Unknown
GameplayDebugger.TimeStamp=2025.07.02-02.53.52
GameplayDebugger.LastCompileMethod=Unknown
AudioMixerXAudio2.TimeStamp=2025.07.02-02.53.27
AudioMixerXAudio2.LastCompileMethod=Unknown
AudioMixerCore.TimeStamp=2025.07.02-02.53.11
AudioMixerCore.LastCompileMethod=Unknown
MessagingRpc.TimeStamp=2025.07.02-02.53.11
MessagingRpc.LastCompileMethod=Unknown
PortalRpc.TimeStamp=2025.07.02-02.53.09
PortalRpc.LastCompileMethod=Unknown
PortalServices.TimeStamp=2025.07.02-02.53.11
PortalServices.LastCompileMethod=Unknown
AnalyticsET.TimeStamp=2025.07.02-02.53.10
AnalyticsET.LastCompileMethod=Unknown
LauncherPlatform.TimeStamp=2025.07.02-02.53.11
LauncherPlatform.LastCompileMethod=Unknown
StreamingPauseRendering.TimeStamp=2025.07.02-02.53.38
StreamingPauseRendering.LastCompileMethod=Unknown
MovieScene.TimeStamp=2025.07.02-02.53.29
MovieScene.LastCompileMethod=Unknown
MovieSceneTracks.TimeStamp=2025.07.02-02.53.51
MovieSceneTracks.LastCompileMethod=Unknown
LevelSequence.TimeStamp=2025.07.02-02.53.51
LevelSequence.LastCompileMethod=Unknown
LiveCoding.TimeStamp=2025.07.02-02.53.15
LiveCoding.LastCompileMethod=Unknown
Documentation.TimeStamp=2025.07.02-02.53.50
Documentation.LastCompileMethod=Unknown
GammaUI.TimeStamp=2025.07.02-02.53.39
GammaUI.LastCompileMethod=Unknown
OutputLog.TimeStamp=2025.07.02-02.54.02
OutputLog.LastCompileMethod=Unknown
ModuleUI.TimeStamp=2025.07.02-02.53.38
ModuleUI.LastCompileMethod=Unknown
Toolbox.TimeStamp=2025.07.02-02.53.13
Toolbox.LastCompileMethod=Unknown
ClassViewer.TimeStamp=2025.07.02-02.53.53
ClassViewer.LastCompileMethod=Unknown
StructViewer.TimeStamp=2025.07.02-02.54.05
StructViewer.LastCompileMethod=Unknown
GraphEditor.TimeStamp=2025.07.02-02.53.49
GraphEditor.LastCompileMethod=Unknown
Kismet.TimeStamp=2025.07.02-02.54.12
Kismet.LastCompileMethod=Unknown
KismetWidgets.TimeStamp=2025.07.02-02.53.54
KismetWidgets.LastCompileMethod=Unknown
Persona.TimeStamp=2025.07.02-02.54.07
Persona.LastCompileMethod=Unknown
AnimGraph.TimeStamp=2025.07.02-02.53.47
AnimGraph.LastCompileMethod=Unknown
AdvancedPreviewScene.TimeStamp=2025.07.02-02.53.56
AdvancedPreviewScene.LastCompileMethod=Unknown
AnimationBlueprintEditor.TimeStamp=2025.07.02-02.54.00
AnimationBlueprintEditor.LastCompileMethod=Unknown
PackagesDialog.TimeStamp=2025.07.02-02.54.01
PackagesDialog.LastCompileMethod=Unknown
DetailCustomizations.TimeStamp=2025.07.02-02.54.13
DetailCustomizations.LastCompileMethod=Unknown
ComponentVisualizers.TimeStamp=2025.07.02-02.53.50
ComponentVisualizers.LastCompileMethod=Unknown
Layers.TimeStamp=2025.07.02-02.53.56
Layers.LastCompileMethod=Unknown
AutomationWindow.TimeStamp=2025.07.02-02.53.50
AutomationWindow.LastCompileMethod=Unknown
AutomationController.TimeStamp=2025.07.02-02.53.50
AutomationController.LastCompileMethod=Unknown
DeviceManager.TimeStamp=2025.07.02-02.53.13
DeviceManager.LastCompileMethod=Unknown
TargetDeviceServices.TimeStamp=2025.07.02-02.53.09
TargetDeviceServices.LastCompileMethod=Unknown
ProfilerClient.TimeStamp=2025.07.02-02.53.10
ProfilerClient.LastCompileMethod=Unknown
SessionFrontend.TimeStamp=2025.07.02-02.53.15
SessionFrontend.LastCompileMethod=Unknown
ProjectLauncher.TimeStamp=2025.07.02-02.53.13
ProjectLauncher.LastCompileMethod=Unknown
SettingsEditor.TimeStamp=2025.07.02-02.53.29
SettingsEditor.LastCompileMethod=Unknown
EditorSettingsViewer.TimeStamp=2025.07.02-02.53.59
EditorSettingsViewer.LastCompileMethod=Unknown
InternationalizationSettings.TimeStamp=2025.07.02-02.53.29
InternationalizationSettings.LastCompileMethod=Unknown
ProjectSettingsViewer.TimeStamp=2025.07.02-02.54.05
ProjectSettingsViewer.LastCompileMethod=Unknown
ProjectTargetPlatformEditor.TimeStamp=2025.07.02-02.53.53
ProjectTargetPlatformEditor.LastCompileMethod=Unknown
Blutility.TimeStamp=2025.07.02-02.54.03
Blutility.LastCompileMethod=Unknown
XmlParser.TimeStamp=2025.07.02-02.53.11
XmlParser.LastCompileMethod=Unknown
UndoHistory.TimeStamp=2025.07.02-02.53.54
UndoHistory.LastCompileMethod=Unknown
DeviceProfileEditor.TimeStamp=2025.07.02-02.53.50
DeviceProfileEditor.LastCompileMethod=Unknown
HardwareTargeting.TimeStamp=2025.07.02-02.53.52
HardwareTargeting.LastCompileMethod=Unknown
LocalizationDashboard.TimeStamp=2025.07.02-02.54.07
LocalizationDashboard.LastCompileMethod=Unknown
LocalizationService.TimeStamp=2025.07.02-02.53.54
LocalizationService.LastCompileMethod=Unknown
MergeActors.TimeStamp=2025.07.02-02.54.06
MergeActors.LastCompileMethod=Unknown
InputBindingEditor.TimeStamp=2025.07.02-02.53.54
InputBindingEditor.LastCompileMethod=Unknown
TimeManagementEditor.TimeStamp=2025.07.02-02.54.01
TimeManagementEditor.LastCompileMethod=Unknown
EditorInteractiveToolsFramework.TimeStamp=2025.07.02-02.53.48
EditorInteractiveToolsFramework.LastCompileMethod=Unknown
TraceInsights.TimeStamp=2025.07.02-02.53.43
TraceInsights.LastCompileMethod=Unknown
TraceServices.TimeStamp=2025.07.02-02.53.11
TraceServices.LastCompileMethod=Unknown
AndroidRuntimeSettings.TimeStamp=2025.07.02-02.53.34
AndroidRuntimeSettings.LastCompileMethod=Unknown
IOSRuntimeSettings.TimeStamp=2025.07.02-02.53.35
IOSRuntimeSettings.LastCompileMethod=Unknown
LuminRuntimeSettings.TimeStamp=2025.07.02-02.53.38
LuminRuntimeSettings.LastCompileMethod=Unknown
SwitchRuntimeSettings.TimeStamp=2025.07.10-02.52.44
SwitchRuntimeSettings.LastCompileMethod=Unknown
WindowsPlatformEditor.TimeStamp=2025.07.02-02.53.41
WindowsPlatformEditor.LastCompileMethod=Unknown
AndroidPlatformEditor.TimeStamp=2025.07.02-02.53.43
AndroidPlatformEditor.LastCompileMethod=Unknown
AndroidDeviceDetection.TimeStamp=2025.07.02-02.53.35
AndroidDeviceDetection.LastCompileMethod=Unknown
IOSPlatformEditor.TimeStamp=2025.07.02-02.53.40
IOSPlatformEditor.LastCompileMethod=Unknown
LuminPlatformEditor.TimeStamp=2025.07.02-02.54.07
LuminPlatformEditor.LastCompileMethod=Unknown
SwitchPlatformEditor.TimeStamp=2025.07.10-02.52.42
SwitchPlatformEditor.LastCompileMethod=Unknown
IntroTutorials.TimeStamp=2025.07.02-02.54.07
IntroTutorials.LastCompileMethod=Unknown
GameProjectGeneration.TimeStamp=2025.07.02-02.53.48
GameProjectGeneration.LastCompileMethod=Unknown
LogVisualizer.TimeStamp=2025.07.02-02.53.53
LogVisualizer.LastCompileMethod=Unknown
ClothPainter.TimeStamp=2025.07.02-02.54.07
ClothPainter.LastCompileMethod=Unknown
SkeletalMeshEditor.TimeStamp=2025.07.02-02.53.48
SkeletalMeshEditor.LastCompileMethod=Unknown
ViewportInteraction.TimeStamp=2025.07.02-02.53.55
ViewportInteraction.LastCompileMethod=Unknown
ViewportSnapping.TimeStamp=2025.07.02-02.54.02
ViewportSnapping.LastCompileMethod=Unknown
PlacementMode.TimeStamp=2025.07.02-02.53.50
PlacementMode.LastCompileMethod=Unknown
ActorPickerMode.TimeStamp=2025.07.02-02.53.57
ActorPickerMode.LastCompileMethod=Unknown
SceneDepthPickerMode.TimeStamp=2025.07.02-02.53.57
SceneDepthPickerMode.LastCompileMethod=Unknown
MeshPaintMode.TimeStamp=2025.07.02-02.53.48
MeshPaintMode.LastCompileMethod=Unknown
LandscapeEditor.TimeStamp=2025.07.02-02.53.46
LandscapeEditor.LastCompileMethod=Unknown
FoliageEdit.TimeStamp=2025.07.02-02.53.48
FoliageEdit.LastCompileMethod=Unknown
VirtualTexturingEditor.TimeStamp=2025.07.02-02.54.04
VirtualTexturingEditor.LastCompileMethod=Unknown
SessionServices.TimeStamp=2025.07.02-02.53.09
SessionServices.LastCompileMethod=Unknown
MorphToolsEditor.TimeStamp=2025.07.02-02.54.05
MorphToolsEditor.LastCompileMethod=Unknown
CameraShakePreviewer.TimeStamp=2025.07.02-02.54.08
CameraShakePreviewer.LastCompileMethod=Unknown
GeometryMode.TimeStamp=2025.07.02-02.53.51
GeometryMode.LastCompileMethod=Unknown
BspMode.TimeStamp=2025.07.02-02.53.57
BspMode.LastCompileMethod=Unknown
TextureAlignMode.TimeStamp=2025.07.02-02.54.10
TextureAlignMode.LastCompileMethod=Unknown
PlanarCut.TimeStamp=2025.07.02-02.53.42
PlanarCut.LastCompileMethod=Unknown
ActorSequenceEditor.TimeStamp=2025.07.02-02.54.05
ActorSequenceEditor.LastCompileMethod=Unknown
LevelSequenceEditor.TimeStamp=2025.07.02-02.53.46
LevelSequenceEditor.LastCompileMethod=Unknown
TemplateSequenceEditor.TimeStamp=2025.07.02-02.54.08
TemplateSequenceEditor.LastCompileMethod=Unknown
GooglePADEditor.TimeStamp=2025.07.02-02.54.06
GooglePADEditor.LastCompileMethod=Unknown
OpenXRInput.TimeStamp=2025.07.02-02.53.55
OpenXRInput.LastCompileMethod=Unknown
TakesCore.TimeStamp=2025.07.02-02.53.54
TakesCore.LastCompileMethod=Unknown
TakeTrackRecorders.TimeStamp=2025.07.02-02.53.30
TakeTrackRecorders.LastCompileMethod=Unknown
TakeRecorderSources.TimeStamp=2025.07.02-02.54.03
TakeRecorderSources.LastCompileMethod=Unknown
TakeRecorder.TimeStamp=2025.07.02-02.54.06
TakeRecorder.LastCompileMethod=Unknown
AutomationWorker.TimeStamp=2025.07.02-02.53.31
AutomationWorker.LastCompileMethod=Unknown
SequenceRecorder.TimeStamp=2025.07.02-02.53.46
SequenceRecorder.LastCompileMethod=Unknown
SequenceRecorderSections.TimeStamp=
SequenceRecorderSections.LastCompileMethod=Unknown
PIEPreviewDeviceProfileSelector.TimeStamp=2025.07.02-02.54.00
PIEPreviewDeviceProfileSelector.LastCompileMethod=Unknown
StatsViewer.TimeStamp=2025.07.02-02.53.52
StatsViewer.LastCompileMethod=Unknown
EditorWidgets.TimeStamp=2025.07.02-02.53.57
EditorWidgets.LastCompileMethod=Unknown
AddContentDialog.TimeStamp=2025.07.02-02.54.00
AddContentDialog.LastCompileMethod=Unknown
WidgetCarousel.TimeStamp=2025.07.02-02.53.08
WidgetCarousel.LastCompileMethod=Unknown
SceneOutliner.TimeStamp=2025.07.02-02.53.53
SceneOutliner.LastCompileMethod=Unknown
HierarchicalLODOutliner.TimeStamp=2025.07.02-02.53.50
HierarchicalLODOutliner.LastCompileMethod=Unknown
Voice.TimeStamp=2025.07.02-02.53.31
Voice.LastCompileMethod=Unknown
MovieSceneCapture.TimeStamp=2025.07.02-02.53.36
MovieSceneCapture.LastCompileMethod=Unknown

[AssetEditorSubsystem]
CleanShutdown=True

[PluginBrowser]
InstalledPlugins=MorphTools
InstalledPlugins=RMAMirrorAnimation
InstalledPlugins=XRVisualization
InstalledPlugins=FMODStudio
InstalledPlugins=wwHeadImport

[RootWindow]
ScreenPosition=X=640.000 Y=336.000
WindowSize=X=1280.000 Y=720.000
InitiallyMaximized=True

[SlateAdditionalLayoutConfig]
Viewport 1.LayoutType=FourPanes2x2
FourPanes2x2.Viewport 1.Percentages0=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages1=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages2=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages3=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Viewport0.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport1.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport2.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport3.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.bIsMaximized=True
FourPanes2x2.Viewport 1.MaximizedViewport=FourPanes2x2.Viewport 1.Viewport1

[DetailCategories]
BP_SunLight_Rugby_C.TransformCommon=True
BP_SunLight_Rugby_C.Rendering=True
BP_SunLight_Rugby_C.Replication=True
BP_SunLight_Rugby_C.Default=True
BP_SunLight_Rugby_C.Collision=True
BP_SunLight_Rugby_C.Input=True
BP_SunLight_Rugby_C.Lights=True
BP_SunLight_Rugby_C.Actor=True
DirectionalLightComponent.TransformCommon=True
DirectionalLightComponent.Light=True
DirectionalLightComponent.Rendering=True
DirectionalLightComponent.Lightmass=True
DirectionalLightComponent.LightShafts=True
DirectionalLightComponent.CascadedShadowMaps=True
DirectionalLightComponent.DistanceFieldShadows=True
DirectionalLightComponent.RayTracing=True
DirectionalLightComponent.AtmosphereAndCloud=True
DirectionalLightComponent.Performance=True
DirectionalLightComponent.LightFunction=True
DirectionalLightComponent.Tags=True
DirectionalLightComponent.Cooking=True
BP_SkyLightActor_Rugby_C.TransformCommon=True
BP_SkyLightActor_Rugby_C.Rendering=True
BP_SkyLightActor_Rugby_C.Replication=True
BP_SkyLightActor_Rugby_C.Collision=True
BP_SkyLightActor_Rugby_C.Input=True
BP_SkyLightActor_Rugby_C.Lights=True
BP_SkyLightActor_Rugby_C.Actor=True
BP_SkySeasons_Rugby_C.TransformCommon=True
BP_SkySeasons_Rugby_C.Source=True
BP_SkySeasons_Rugby_C.Override Settings=True
BP_SkySeasons_Rugby_C.Stadium=True
BP_SkySeasons_Rugby_C.Settings=True
BP_SkySeasons_Rugby_C.Settings Quick=True
BP_SkySeasons_Rugby_C.Parameter=True
BP_SkySeasons_Rugby_C.Sky Textures=True
BP_SkySeasons_Rugby_C.Weather=True
BP_SkySeasons_Rugby_C.Sky=True
BP_SkySeasons_Rugby_C.Moon=True
BP_SkySeasons_Rugby_C.Sun=True
BP_SkySeasons_Rugby_C.Clouds=True
BP_SkySeasons_Rugby_C.Stars=True
BP_SkySeasons_Rugby_C.Main Menu=True
BP_SkySeasons_Rugby_C.Rendering=True
BP_SkySeasons_Rugby_C.Replication=True
BP_SkySeasons_Rugby_C.Collision=True
BP_SkySeasons_Rugby_C.Input=True
BP_SkySeasons_Rugby_C.Dynamic=True
BP_SkySeasons_Rugby_C.SkyDomeBase=True
BP_SkySeasons_Rugby_C.Actor=True
EditorStyleSettings.UserInterface=True
EditorStyleSettings.Accessibility=True
EditorStyleSettings.Colors=True
EditorStyleSettings.Graphs=True
EditorStyleSettings.Output Log=True
EditorExperimentalSettings.HDR=True
EditorExperimentalSettings.Foliage=True
EditorExperimentalSettings.Tools=True
EditorExperimentalSettings.UserInterface=True
EditorExperimentalSettings.Blueprints=True
EditorExperimentalSettings.World=True
EditorExperimentalSettings.Cooking=True
EditorExperimentalSettings.PIE=True
EditorExperimentalSettings.LightingBuilds=True
EditorExperimentalSettings.Core=True
EditorExperimentalSettings.Materials=True
EditorExperimentalSettings.Content Browser=True
EditorSettings.DerivedDataCache=True
EditorSettings.Derived Data Cache S3=True
EditorKeyboardShortcutSettings.AdvancedPreviewScene=True
EditorKeyboardShortcutSettings.AnimGraph=True
EditorKeyboardShortcutSettings.AnimSequenceCurveEditor=True
EditorKeyboardShortcutSettings.AssetEditor=True
EditorKeyboardShortcutSettings.LoadingProfilerCommand=True
EditorKeyboardShortcutSettings.AssetManagerEditorCommands=True
EditorKeyboardShortcutSettings.BlueprintDebugger=True
EditorKeyboardShortcutSettings.CameraShakePreviewer=True
EditorKeyboardShortcutSettings.ClothPainterTools=True
EditorKeyboardShortcutSettings.ClothPainter=True
EditorKeyboardShortcutSettings.SequenceRecorder.Common=True
EditorKeyboardShortcutSettings.LiveLinkClient.Common=True
EditorKeyboardShortcutSettings.TakeRecorder=True
EditorKeyboardShortcutSettings.TakeRecorderSources=True
EditorKeyboardShortcutSettings.GenericCommands=True
EditorKeyboardShortcutSettings.WidgetDesigner=True
EditorKeyboardShortcutSettings.EditorViewport=True
EditorKeyboardShortcutSettings.ContentBrowser=True
EditorKeyboardShortcutSettings.CreatureEditorViewport=True
EditorKeyboardShortcutSettings.GenericCurveEditor=True
EditorKeyboardShortcutSettings.CurveEditorTools=True
EditorKeyboardShortcutSettings.TabCommands=True
EditorKeyboardShortcutSettings.DungeonEditorViewport=True
EditorKeyboardShortcutSettings.FoliageEditMode=True
EditorKeyboardShortcutSettings.GraphEditor=True
EditorKeyboardShortcutSettings.InsightsCommands=True
EditorKeyboardShortcutSettings.LandscapeEditor=True
EditorKeyboardShortcutSettings.LayersView=True
EditorKeyboardShortcutSettings.LevelEditor=True
EditorKeyboardShortcutSettings.LevelEditorModes=True
EditorKeyboardShortcutSettings.LevelSequenceEditor=True
EditorKeyboardShortcutSettings.LevelViewport=True
EditorKeyboardShortcutSettings.MainFrame=True
EditorKeyboardShortcutSettings.MaterialEditor=True
EditorKeyboardShortcutSettings.MemoryProfilerCommand=True
EditorKeyboardShortcutSettings.MeshPaint=True
EditorKeyboardShortcutSettings.MeshPaintingTools=True
EditorKeyboardShortcutSettings.MorphToolsEditorCommands=True
EditorKeyboardShortcutSettings.NetworkingProfilerCommand=True
EditorKeyboardShortcutSettings.NiagaraEditor=True
EditorKeyboardShortcutSettings.PersonaCommon=True
EditorKeyboardShortcutSettings.PlayWorld=True
EditorKeyboardShortcutSettings.RayTracingDebugVisualizationMenu=True
EditorKeyboardShortcutSettings.VisualizationMenu=True
EditorKeyboardShortcutSettings.RugbyEditorTools=True
EditorKeyboardShortcutSettings.Sequencer=True
EditorKeyboardShortcutSettings.ShowFlagsMenu=True
EditorKeyboardShortcutSettings.SplineComponentVisualizer=True
EditorKeyboardShortcutSettings.StandardToolCommands=True
EditorKeyboardShortcutSettings.SystemWideCommands=True
EditorKeyboardShortcutSettings.TimingProfilerCommand=True
EditorKeyboardShortcutSettings.EditorViewportClient=True
EditorKeyboardShortcutSettings.VisualLogger=True
EditorKeyboardShortcutSettings.wwHeadImportEditor=True
EditorKeyboardShortcutSettings.SeatLayout=True
EditorKeyboardShortcutSettings.WWUIEditor=True
LiveCodingSettings.General=True
LiveCodingSettings.Modules=True
EditorLoadingSavingSettings.Startup=True
EditorLoadingSavingSettings.AutoReimport=True
EditorLoadingSavingSettings.Blueprints=True
EditorLoadingSavingSettings.AutoSave=True
EditorLoadingSavingSettings.SourceControl=True
EditorPerProjectUserSettings.DeveloperTools=True
EditorPerProjectUserSettings.AI=True
EditorPerProjectUserSettings.SimplygonSwarm=True
EditorPerProjectUserSettings.HotReload=True
EditorPerProjectUserSettings.Import=True
EditorPerProjectUserSettings.Export=True
EditorPerProjectUserSettings.UnrealAutomationTool=True
EditorPerformanceSettings.EditorPerformance=True
InternationalizationSettingsModel.Internationalization=True
InternationalizationSettingsModel.Time=True
SourceCodeAccessSettings.Accessor=True
SynthesisEditorSettings.Source Effects=True
SynthesisEditorSettings.Submix Effects=True
EditorTutorialSettings.Tutorials=True
VRModeSettings.General=True
VRModeSettings.Cinematics=True
VRModeSettings.World Movement=True
VRModeSettings.UI Customization=True
VRModeSettings.Motion Controllers=True
LevelEditorMiscSettings.Editing=True
LevelEditorMiscSettings.Sound=True
LevelEditorMiscSettings.Levels=True
LevelEditorMiscSettings.Screenshots=True
LevelEditorPlaySettings.PlayInEditor=True
LevelEditorPlaySettings.GameViewportSettings=True
LevelEditorPlaySettings.PlayInNewWindow=True
LevelEditorPlaySettings.PlayInStandaloneGame=True
LevelEditorPlaySettings.Multiplayer Options=True
LevelEditorPlaySettings.PlayOnDevice=True
LevelEditorPlaySettings.MultiplayerOptions=True
OnlinePIESettings.Logins=True
LevelEditorViewportSettings.Controls=True
LevelEditorViewportSettings.LookAndFeel=True
LevelEditorViewportSettings.GridSnapping=True
LevelEditorViewportSettings.Preview=True
PersonaOptions.Preview Scene=True
PersonaOptions.Viewport=True
PersonaOptions.Audio=True
PersonaOptions.Composites and Montages=True
PersonaOptions.Skeleton Tree=True
PersonaOptions.Mesh=True
PersonaOptions.Asset Browser=True
BlueprintEditorSettings.VisualStyle=True
BlueprintEditorSettings.Workflow=True
BlueprintEditorSettings.Experimental=True
BlueprintEditorSettings.Compiler=True
BlueprintEditorSettings.DeveloperTools=True
BlueprintEditorSettings.Performance=True
ContentBrowserSettings.ContentBrowser=True
CurveEditorSettings.Curve Editor=True
SequencerSettings.Keyframing=True
SequencerSettings.General=True
SequencerSettings.Timeline=True
SequencerSettings.Snapping=True
SequencerSettings.CurveEditor=True
SequencerSettings.Playback=True
GraphEditorSettings.GeneralStyle=True
GraphEditorSettings.Splines=True
GraphEditorSettings.PinColors=True
GraphEditorSettings.NodeTitleColors=True
GraphEditorSettings.Tracing=True
GraphEditorSettings.ContextMenu=True
GraphEditorSettings.CommentNodes=True
MaterialEditorSettings.Offline Shader Compilers=True
MaterialEditorSettings.User Interface Domain=True
SkeletalMeshEditorSettings.AnimationPreview=True
TakeRecorderUserSettings.User Settings=True
WidgetDesignerSettings.GridSnapping=True
WidgetDesignerSettings.Dragging=True
WidgetDesignerSettings.Visuals=True
WidgetDesignerSettings.Interaction=True
PythonScriptPluginUserSettings.Python=True
CrashReportsPrivacySettings.Options=True
AnalyticsPrivacySettings.Options=True
AutomationTestSettings.Loading=True
AutomationTestSettings.Automation=True
AutomationTestSettings.Open Asset Tests=True
AutomationTestSettings.PIE Test Maps=True
AutomationTestSettings.MiscAutomationSetups=True
AutomationTestSettings.ExternalTools=True
AutomationTestSettings.Screenshots=True
CrashReporterSettings.CrashReporter=True
GameplayTagsDeveloperSettings.GameplayTags=True
LogVisualizerSettings.VisualLogger=True

[/Script/UnrealEd.EditorExperimentalSettings]
bHDREditor=False
HDREditorNITLevel=160.000000
bProceduralFoliage=False
bEnableLocalizationDashboard=True
bEnableTranslationPicker=False
bEnableFavoriteSystem=False
ConsoleForGamepadLabels=None
bEnableOneFilePerActorSupport=False
bDrawMidpointArrowsInBlueprints=False
bContextMenuChunkAssignments=False
bDisableCookInEditor=False
bSharedCookedBuilds=False
MultiProcessCooking=0
bAllowLateJoinInPIE=False
bAllowVulkanPreview=False
bEnableMultithreadedLightmapEncoding=True
bEnableMultithreadedShadowmapEncoding=True
bUseOpenCLForConvexHullDecomp=False
bAllowPotentiallyUnsafePropertyEditing=False
bFacialAnimationImporter=False
bMobilePIEPreviewDeviceLaunch=False
bTextAssetFormatSupport=False
bEnableLongPathsSupport=False

[DetailCustomWidgetExpansion]
EditorStyleSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
EditorExperimentalSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
EditorSettings=
EditorKeyboardShortcutSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
LiveCodingSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
EditorLoadingSavingSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
EditorPerProjectUserSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
EditorPerformanceSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
InternationalizationSettingsModel=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
SourceCodeAccessSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
SynthesisEditorSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
EditorTutorialSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
VRModeSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
LevelEditorMiscSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
LevelEditorPlaySettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
OnlinePIESettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
LevelEditorViewportSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
PersonaOptions=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
BlueprintEditorSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
ContentBrowserSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
CurveEditorSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
SequencerSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
GraphEditorSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
MaterialEditorSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
SkeletalMeshEditorSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
TakeRecorderUserSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
WidgetDesignerSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
PythonScriptPluginUserSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
CrashReportsPrivacySettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
AnalyticsPrivacySettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
AutomationTestSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
CrashReporterSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
GameplayTagsDeveloperSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,
LogVisualizerSettings=EditorKeyboardShortcutSettings.EditorViewport.ShowTextureResolution,EditorKeyboardShortcutSettings.EditorViewport.ShowTextureScale,EditorKeyboardShortcutSettings.MainFrame.SwitchProject,

[DetailMultiObjectNodeExpansion]
EditorStyleSettings=True
EditorPerProjectUserSettings=True
EditorExperimentalSettings=True
EditorKeyboardShortcutSettings=True
LevelEditorPlaySettings=True
EditorSettings=True
LiveCodingSettings=True
EditorLoadingSavingSettings=True
EditorPerformanceSettings=True
InternationalizationSettingsModel=True
SourceCodeAccessSettings=True
SynthesisEditorSettings=True
EditorTutorialSettings=True
VRModeSettings=True
LevelEditorMiscSettings=True
OnlinePIESettings=True
LevelEditorViewportSettings=True
PersonaOptions=True
BlueprintEditorSettings=True
ContentBrowserSettings=True
CurveEditorSettings=True
SequencerSettings=True
GraphEditorSettings=True
MaterialEditorSettings=True
SkeletalMeshEditorSettings=True
TakeRecorderUserSettings=True
WidgetDesignerSettings=True
PythonScriptPluginUserSettings=True
CrashReportsPrivacySettings=True
AnalyticsPrivacySettings=True
AutomationTestSettings=True
CrashReporterSettings=True
GameplayTagsDeveloperSettings=True
LogVisualizerSettings=True

[DetailPropertyExpansion]
EditorStyleSettings="\"Object.Colors.EditorMainWindowBackgroundOverride\" \"Object.Colors.EditorMainWindowBackgroundOverride.TintColor\" \"Object.Colors.EditorChildWindowBackgroundOverride\" \"Object.Colors.EditorChildWindowBackgroundOverride.TintColor\" \"Object.Graphs.GraphBackgroundBrush\" \"Object.Graphs.GraphBackgroundBrush.TintColor\" "
Object=
EditorExperimentalSettings="\"Object.Blueprints\" "
EditorLoadingSavingSettings="\"Object.AutoReimport\" \"Object.AutoReimport.AutoReimportDirectorySettings\" \"Object.AutoReimport.AutoReimportDirectorySettings.AutoReimportDirectorySettings[0]\" \"Object.AutoReimport.AutoReimportDirectorySettings.AutoReimportDirectorySettings[0].Wildcards\" \"Object.Blueprints\" \"Object.AutoSave\" "
EditorTutorialSettings="\"Object.Tutorials.Categories\" \"Object.Tutorials.Categories.Categories[0]\" \"Object.Tutorials.Categories.Categories[1]\" \"Object.Tutorials.Categories.Categories[2]\" \"Object.Tutorials.Categories.Categories[3]\" \"Object.Tutorials.Categories.Categories[4]\" \"Object.Tutorials.Categories.Categories[5]\" \"Object.Tutorials.Categories.Categories[6]\" \"Object.Tutorials.Categories.Categories[7]\" \"Object.Tutorials.TutorialContexts\" \"Object.Tutorials.TutorialContexts.TutorialContexts[0]\" \"Object.Tutorials.TutorialContexts.TutorialContexts[1]\" \"Object.Tutorials.TutorialContexts.TutorialContexts[2]\" \"Object.Tutorials.TutorialContexts.TutorialContexts[3]\" \"Object.Tutorials.TutorialContexts.TutorialContexts[4]\" \"Object.Tutorials.TutorialContexts.TutorialContexts[5]\" \"Object.Tutorials.TutorialContexts.TutorialContexts[6]\" \"Object.Tutorials.TutorialContexts.TutorialContexts[7]\" \"Object.Tutorials.TutorialContexts.TutorialContexts[8]\" \"Object.Tutorials.TutorialContexts.TutorialContexts[9]\" \"Object.Tutorials.TutorialContexts.TutorialContexts[10]\" "
LevelEditorPlaySettings="\"Object.Multiplayer Options.NetworkEmulationSettings\" \"Object.Multiplayer Options.NetworkEmulationSettings.OutPackets\" \"Object.Multiplayer Options.NetworkEmulationSettings.InPackets\" \"Object.Multiplayer Options.Multiplayer Options|Client\" \"Object.Multiplayer Options.Multiplayer Options|Server\" "
LevelEditorViewportSettings="\"Object.GridSnapping.DecimalGridSizes\" \"Object.GridSnapping.DecimalGridIntervals\" \"Object.GridSnapping.Pow2GridSizes\" \"Object.GridSnapping.Pow2GridIntervals\" \"Object.GridSnapping.CommonRotGridSizes\" \"Object.GridSnapping.DivisionsOf360RotGridSizes\" \"Object.GridSnapping.ScalingGridSizes\" \"Object.Preview.PreviewMeshes\" "
PersonaOptions="\"Object.Preview Scene.Preview Scene|AdditionalMesh\" "
CrashReportsPrivacySettings="\"Object.Options.bSendUnattendedBugReports\" "
AnalyticsPrivacySettings="\"Object.Options.bSendUsageData\" "
AutomationTestSettings="\"Object.Loading.EditorTestModules\" \"Object.Automation.BuildPromotionTest\" \"Object.Automation.BuildPromotionTest.ImportWorkflow\" \"Object.Automation.BuildPromotionTest.ImportWorkflow.Diffuse\" \"Object.Automation.BuildPromotionTest.ImportWorkflow.Normal\" \"Object.Automation.BuildPromotionTest.ImportWorkflow.StaticMesh\" \"Object.Automation.BuildPromotionTest.ImportWorkflow.ReimportStaticMesh\" \"Object.Automation.BuildPromotionTest.ImportWorkflow.BlendShapeMesh\" \"Object.Automation.BuildPromotionTest.ImportWorkflow.MorphMesh\" \"Object.Automation.BuildPromotionTest.ImportWorkflow.SkeletalMesh\" \"Object.Automation.BuildPromotionTest.ImportWorkflow.Animation\" \"Object.Automation.BuildPromotionTest.ImportWorkflow.Sound\" \"Object.Automation.BuildPromotionTest.ImportWorkflow.SurroundSound\" \"Object.Automation.BuildPromotionTest.OpenAssets\" \"Object.Automation.MaterialEditorPromotionTest\" \"Object.Automation.ParticleEditorPromotionTest\" \"Object.Automation.BlueprintEditorPromotionTest\" \"Object.MiscAutomationSetups.TestLevelFolders\" "

[Directories2]
UNR=../../../../NRL/Content/
BRUSH=../../../../NRL/Content/
FBX=../../../../NRL/Content/
FBXAnim=../../../../NRL/Content/
GenericImport=../../../../NRL/Content/
GenericExport=../../../../NRL/Content/
GenericOpen=../../../../NRL/Content/
GenericSave=../../../../NRL/Content/
MeshImportExport=../../../../NRL/Content/
WorldRoot=../../../../NRL/Content/
Level=../../../../NRL/Content/Maps
Project=W:/Engine/

[MessageLog]
LastLogListing=PIE

[/Script/UnrealEd.EditorLoadingSavingSettings]
bSCCUseGlobalSettings=False
bAutomaticallyCheckoutOnAssetModification=False
AutoReimportDirectorySettings=(SourceDirectory="/Game/",MountPoint="",Wildcards=((Wildcard="Localization/*")))
bForceCompilationAtStartup=False
bRestoreOpenAssetTabsOnRestart=False
bMonitorContentDirectories=True
AutoReimportThreshold=3.000000
bAutoCreateAssets=True
bAutoDeleteAssets=True
bDetectChangesOnStartup=True
bPromptBeforeAutoImporting=False
bDeleteSourceFilesWithAssets=False

