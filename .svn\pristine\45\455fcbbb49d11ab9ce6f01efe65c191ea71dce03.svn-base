/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef SS_GAMETIMER_OBSERVER_H
#define SS_GAMETIMER_OBSERVER_H

#include "Match/RugbyUnion/RUTackleHelper.h"
#include "Match/RugbyUnion/Enums/RUPenaltyDecisionEnum.h"
#include "Match/RugbyUnion/Rules/RURuleConsequenceEnum.h"


class SIFGameWorld;
class ARugbyCharacter;
class RUTeam;

/**
	Formerly RL3GameHalfObserver. This class controls when the game half ends, works along side SSGameTimer
	@ported to RU by RobH.
 */

class SSGameTimerHalfObserver
{
	public:
		SSGameTimerHalfObserver( SIFGameWorld* ggame );
		~SSGameTimerHalfObserver();

		void Reset();
		void Update();

		inline bool IsHalfEnded(){ return half_end; }
		inline bool GetTimeExpired(){ return time_expired; }

		void ForceEndOfHalf();
		void ForceEndOfFull();

		inline void SetIgnoreNextScrum(){ ignore_next_scrum = true; }
		inline bool GetIgnoreNextScrum(){ return ignore_next_scrum; }

		inline void SetIgnoreNextDropOut(){ ignore_next_dropout = true; }
		inline bool GetIgnoreNextDropOut(){ return ignore_next_dropout; }

		bool IsHalfEndTriggered() { return triggered_half_end; }

	private:
		void NotifyTimeExpired();
		void NotifyScrum(RUTeam *);
		void NotifyFieldGoal( bool success, const FVector& position );
		void NotifyKickRestart();
		//void NotifyPenalty( PENALTY_DECISION decision );
		void NotifyTryAwarded();
		void NotifyPenaltyTryAwarded();
		void NotifyTryResult( bool, bool, ARugbyCharacter* );
		//void NotifyConversion( bool success, const FVector& position );
		void NotifyCutsceneFinished();
		void NotifyLineOut( FVector /*location*/, bool from_decision);
		void NotifyTouchScrum(FVector /*location*/, bool from_decision);
		void NotifyDropOut();
		void NotifyTackle(const RUTackleResult& tackleResult);
		void BallDead(ARugbyCharacter*, const FVector&, RURuleConsequence consequence);
		void NotifyPenaltyFinished(bool success, const FVector& );

		void HalfEnd();
		void TriggerHalfEnd();

		void FullEnd();
		void TriggerFullEnd();

		SIFGameWorld* game;
		bool time_expired;
		bool half_end;
		bool triggered_half_end;
		bool full_end;
		bool triggered_full_end;
		bool ignore_next_scrum;
		bool ignore_next_dropout;
		bool trigger_half_end_next_frame;

#ifdef ENABLE_SEVENS_MODE
		bool scoredDuringExtraTime;
#endif
};

#endif	// SS_GAMETIMER_OBSERVER_H
