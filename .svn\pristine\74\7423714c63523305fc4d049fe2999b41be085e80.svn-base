/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef RU_CUTSCENE_MANAGER_H
#define RU_CUTSCENE_MANAGER_H

#include "Match/SSScreenWipeManager.h"
#include "Match/SSGameTimer.h"
#include "Match/SSReplaysMk2/SSReplayManager.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURuleConsequenceEnum.h"
#include "Match/RugbyUnion/Enums/SSTeamSideEnum.h"
#include "Match/RugbyUnion/Enums/RUPenaltyDecisionEnum.h"
#include "CutSceneManager.h"
#include "Match/Cutscenes/SSCutSceneTags.h"
#include "Runtime/MovieScene/Public/MovieSceneObjectBindingID.h"
#include "LevelSequence.h"
#include "RugbyGameInstance.h"
#include "Character/RugbyCharacterAnimInstance.h"

class SIFGameWorld;
class ARugbyCharacter;
class MabResourceSet;
class NMMabCinematicAnimation;
class SIFFMODSoundResource;
struct RUHUDDeferredPlayerInfo;
class RUActiveCompetitionBase;
class ULevelSequencePlayer;
class ALevelSequenceActor;
class SSPostEffectSettings;
class UCutScenes;
struct FRugbyMontageInstance;

const float SKIP_HAKA_TIME = 2.f;
const float DEFAULT_SCREEN_WIPE_TIME = 0.3f;
#define SWIPE_DURATION 0.8f

#if PLATFORM_SWITCH
#define DISABLE_NETWORKED_CUTSCENES_REPLAYS	1
#else
#define DISABLE_NETWORKED_CUTSCENES_REPLAYS	0
#endif

DECLARE_MULTICAST_DELEGATE(FCinematicCutsceneStarted);

///-------------------------------------------------------------
/// Class: SSCutSceneManager
///  - Activates SSCutScenes (from events) and keeps track of them.
///-------------------------------------------------------------

class SSCutSceneManager
{
public:

	SSCutSceneManager(SIFGameWorld* ggame);
	virtual ~SSCutSceneManager();

	struct StCinematicElementInfo
	{
		UCutScenes* UCutscenePtr = nullptr;
		FCinematicElementInfo  CinematicElementInfoData;
	};

	inline SIFGameWorld *GetGame(){ return game; }
	void	SyncUpdate();
	///-------------------------------------------------------------------------------
	/// Disable game simulation if running any cutscene (might need to make more specific
	///  if we use cutscenes to run other systems)
	///-------------------------------------------------------------------------------

	bool IsSimulationDisabled();
	
	float	GetMorphemeAnimationInterval();
	void	RunonCutSceneBegin();
	unsigned int GetSFXUID();
	void	Update(float delta_time) { Tick (delta_time); };
	void	SkipCutScene();
	void	StartHalfTimeWalkOff();
	void	StartHalfTimeWalkOn();
	void	StartFullTimeCutScene();
	void	StartCoinTossCutScene();
	void	StartSimulationCutScene();
	void	StartProSentOffCutScene();
	void	ResumeCutsceneFromProSendOff();
#ifdef ENABLE_SEVENS_MODE
	void	OnCoinTossWonLost(RUTeam* winningTeam);
	void	OnCoinTossDecideToKick(RUTeam* kickingTeam);
	void	OnCoinTossDecideDirection(RUTeam* directingTeam, int dir);
	void	OnCoinTossFinished();
#endif
	void	StartNamedCutscene(const char *cutscene_name,const MabMatrix &transform, bool hide_non_actors);
	void	StartTutorialCutscene(bool success, const MabMatrix &transform, bool hide_non_actors);
	bool	IsCinematicRunning(const bool bIgnoreDelay = false);
	bool	CineMaticHasBallProp();
	void	StopAllCutScenes();
	void	PauseAllCutScenes();
	void	ResumeAllCutScenes();
	void	StopAllCutScenesButFirst();
	void	FinishCurrentCutScene();

	// Fns called from callback
	void	OnCutSceneStart();
	void	OnCutSceneEnd();

	bool GetIsAnyCutscenePlaying()
	{
		UCutSceneManager* cutsceneManager = GetCutsceneManager();
		return cutsceneManager->GetIsAnyCutscenePlaying();
	}

	// Delegates called after cutscene start.
	FCinematicCutsceneStarted CinematicCutsceneStartedDelegate;

	/// Clear the cutscene_elements list.
	void	ClearCutSceneElements();
	inline void SetDisableCutSceneSkip(bool val) { local_disable_skip = val; }
	bool	GetDisableCutSceneSkip() { return local_disable_skip; }
	void	UIRequestCameraTransition( const char* destination_window_name );
	void	Reset();
	bool	SkipEnabled();
	void	DisableVisibilityUpdate(){ disable_visibility_update = true; }
#ifdef ENABLE_RUGED
	void DebugGetInfo(MabVector<MabString> &names, MabVector<int> &state_flags,MabVector<float> &times);
#endif

	void	OnCleanupActors();

	/// Enable the player creator cutscene angle system(called from UI)
	void	EnablePlayerCreatorAngleAdjustment(bool enabled);
	/// Set the player creator angle directly.
	void	SetPlayerCreatorAngle(float angle, bool do_blend);
	/// Get the player creator transform.
	FVector &GetPlayerCreatorTransform(){ return player_creator_transform; }
	/// Get the player creator offset.
	FVector &GetPlayerCreatorOffset(){ return player_creator_target_offset; }
	/// Get the player creator transform. (no translation version for lights)
	MabMatrix &GetPlayerCreatorTransformNoTranslation(){ return player_creator_transform_notrans; }
	/// Is player creator active?
	inline bool IsPlayerCreatorActive(){ return player_creator_on; }
	/// Set the player creator camera offset.
    void	SetPlayerCreatorCameraOffset(FVector offset, bool with_height_offset, bool blend=true);
    /// A player has been deleted, inform all loaded cutscenes.
	void	PlayerDeleted(ARugbyCharacter* player);
    /// Set the cup to show during ui screens. (active competition hasn't been setup yet)
	void	SetCompetitionUICup(int competition_db_id);
    /// Set the team which is celebrating (used by cutscene team selection filter).
	void	SetCelebratingTeam(RUTeam *team){ celebrating_team = team; }
    /// Get the team which is celebrating.
	inline RUTeam	*GetCelebratingTeam(){ return celebrating_team; }
	inline SSTEAMSIDE GetCelebratingTeamSide(){ return celebrating_team!=NULL?celebrating_team->GetSide():SIDE_NONE; }
    /// Set the 'focus' player (used by cutscene team selection filter).
	void	SetFocusPlayer(ARugbyCharacter* player, SSTEAMSIDE side){ focus_player[side] = player; }
	inline ARugbyCharacter* GetFocusPlayer(SSTEAMSIDE side){ return focus_player[side]; }
    /// Set/get for the substitution players.
	inline void SetInterchangeType(int new_type) {interchange_type = new_type;}
	inline void	SetGoingOffPlayer(ARugbyCharacter* player){ going_off_player = player; }
	inline void	SetGoingOnPlayer(ARugbyCharacter* player){ going_on_player = player; }

	void	SetupPreKickOffCutscene();
	inline int GetInterchangeType() const {return interchange_type;}
	inline ARugbyCharacter* GetGoingOffPlayer() const { return going_off_player; }
	inline ARugbyCharacter* GetGoingOnPlayer() const { return going_on_player; }	

    /// Set/get the game phase to force the formation manager to use during cutscenes.
	inline void	SetGamePhaseOverride(RUGamePhase phase){ game_phase_override = phase; }
	inline RUGamePhase GetGamePhaseOverride(){ return game_phase_override; }

	/// Role updater for all non-actor players.
	void	UpdateNonCutScenePlayerMovement(ARugbyCharacter* player);

    /// Reposition player so that he isn't getting in the way of a cutscene.
	void	PostProcessNonActorMovement(ARugbyCharacter* player);

    /// Get the center of the main cutscene running.
	FVector GetCutSceneOrigin();

    /// Get the 'match' emotion level - used for fireworks (SIDE_NONE cutscenes).
	int		GetMatchEmotionLevel();
	void	SetHomeTeamSwitchWhenFaded(bool val1, bool val2) { do_team_switch = val1; /*do_team_switch_generic = val2;*/ }
	bool	IsHomeTeamSwitchPending() { return do_team_switch; }
	//void	SetCompHomeTeamSwitchWhenFaded(bool val){ do_comp_team_switch=val; }
	void	SetPlayingHaka (bool playing);
	bool	IsPlayingHaka() {	return playing_haka;	};
	void	StartHakaCutsceneTimer()			{ haka_skip_disable_time = SKIP_HAKA_TIME; }
	void	ClearHakaCutsceneTimer()			{ haka_skip_disable_time = 0.0f; }

    /// Clamp the transform part of the matrix to keep cutscene on pitch.
	void	ClampTransform(MabMatrix &transform, float dist_from_touch);
	inline void SetInjuryReplacement(int player_db_id){ injury_hud_selected_player = player_db_id; }

	/// Setup the grand final celebrations cutscene.
	void	StartGrandFinalCelebrations();

	void	StartTitleIntro();

    /// Can 'team' do the haka?
	bool	CanTeamDoHaka(RUTeam *team);
    /// Get injured player.
	inline ARugbyCharacter* GetInjuredPlayer(){ return injured_player; }
    /// Are cinematics enabled?
	inline bool GetCinematicsEnabled(){ return cinematics_enabled; }
	
	/// Get the cutscene state
	inline int GetCutsceneState(){ return ui_cutscene_state; }

	void	SetupCompetitionHubCutscene();

	/// Work around soak crash, force current cutscene to loop for ever.
	virtual bool ForceCutsceneInfiniteLoop();

	void	ResetTimer();
	
	UCutSceneManager * GetCutsceneManager() { return m_UCutsceneManager; };

	void SetPlayerCreatorCameraTargetAngle(float value);

	void SetCutsceneLoadCompleteDelegate(FWWUICutsceneLoadComplete InDelegate) { OnUICutsceneLoadComplete = InDelegate; }
	void CallUICutsceneLoadComplete();

	bool IsInReplay() const;

	void TickNetworkCutscenes(float delta_time);

	void StopPlayerCreatorRotateSound();
	void ResetPlayerCreatorCameraAngle();
	void UpdatePlayerCreatorAngle(float delta_time, bool canPlayRotationSound = false);

	//GGS SRA: Handles ball handover, after 6th tackle
	void HandoverCutscene();

protected:
	virtual void StartEditingEvent(const MabString &name);
	virtual void StopEditingEvent(const MabString &name);
	//#rc3_legacy virtual void CutScenePreDelete(StMabEVDSCutScene *cutscene);
	//virtual void SetCamera(const FVector& eye, const FVector& focus, float fov, float near_clip_distance, float roll_angle, bool player_creator_offset_enabled);
	//virtual MabEVDSContainer *GetHandyCamShakeContainer();
	virtual bool IsCutSceneFile(const MabString& file_name);
	//	virtual bool ShouldSkip();	
	//virtual void CutSceneStarted(/*StMabEVDSCutScene *cutscene*/);
	virtual void Tick(float delta_time);
	virtual void TimeScrubUpdate(int current_frame, int dest_frame);
	virtual void OnDataModified();

private:
	//void	RegisterEventHandlers();
	void	UpdateProfileSettings();

	void	CalculateMatchEmotionLevel();

	void	SetupPreGameCutScenes();
	void	SetupPlayerCustomisationCutscene(const char *destination_window_name);

	void	LaunchPreTryCutscene(bool success, bool penalty_try, ARugbyCharacter* );
	void	LaunchTryCutscene();
	void	LaunchTMOCutscene();
	void	ConversionCutSceneBegin(bool success);
	void	PenaltyCutSceneBegin(bool success, const FVector &crossed_goal_position);
	void	KickoffCutSceneBegin();
	void	KickoffCutScenefinish( ARugbyCharacter* player, KickContext, KickType type, const FVector& position );
	void	SetEndCutScenePhase(RUGamePhase phase);
	void	LaunchPenaltyCutscene( ARugbyCharacter* offender, ARugbyCharacter* offended_player, const FVector& position, PENALTY_REASON reason, SUSPENSION_RESULT suspension_result );
	void	LaunchMarkAwardedCutscene( const FVector& position, ARugbyCharacter* marking_player);
	void	LaunchRestartOutOnFullCutscene( const FVector& position, ARugbyCharacter* restart_out_causing_player);
	void	LaunchRestartKickOffIntoGoalCutscene( ARugbyCharacter* restart_out_causing_player);

	void	LaunchFourtyTwentyKickAwardedCutscene(const FVector& position, ARugbyCharacter* kicking_player, const KickContext& kickContext);

	void	LaunchFreeKickOutOnFullCutscene( ARugbyCharacter* restart_out_causing_player);
	void	LaunchFreeKickCollected10mCutscene( ARugbyCharacter* restart_out_causing_player);
	void	LaunchFreeKickOffsideKickOffCutscene( ARugbyCharacter* restart_out_causing_player);

	void	LaunchBallDeadCutscene( ARugbyCharacter* last_played_by_player, const FVector& pos, RURuleConsequence consequence );
	void	LaunchMaulHeldUpCutscene(ARugbyCharacter* /*holding_player*/, const FVector& position, bool defense_to_feed);
	void	LaunchKnockonCutscene( );
	void	LineoutSignalled(FVector restart_position, bool from_decision);
	void	TouchScrumSignalled(FVector restart_position, bool from_decision);
	void	StartInjuryCutScene(ARugbyCharacter* injured_player, TACKLE_INJURY_TYPE injury_type);
	void	StartKickForPointsCutscene(RUTeam *team);
	void	DropGoalSuccessCutsceneBegin();
	void	DropOutCutsceneBegin();
	void	ForwardPassCutsceneBegin(RUTeam* offending_team);
	void	AdvantageStart(ARugbyCharacter*);
	void	AdvantageEnd(ARugbyCharacter*);
	void	ReplaySaved(SSReplayBlock*);

	//void	OnFreeKickNot10m(ARugbyCharacter*);

	void	SetupGoldenPoint();

	void	SetupExtraTimeHalfTimeCutscene();
	void	SetupExtraTimeStartCutscene();
	void	SetupFullTimeCutscene();
	void	AddPreKickOffCutScene(SSGT_HALF half);
	void	AddPreKickOffSevensExtraTimeCutScene(SSGT_HALF half);

	void	ConversionCutSceneBegin(bool success, bool is_dropgoal);
	void	SetupInterchange();

	/// Setup the half time cutscene (called from any setup to insert core elements).
	bool	SetCelebratingTeamBasedOnScore();
	
	void LoadBestCutScene(int csevent, bool async, bool is_dual, SSTEAMSIDE team_side, FCutSceneData &CutSceneData);

	//std::unique_ptr<StMabEVDSCutScene> LoadNamedCutScene(const char *filename, bool async, bool is_dual, SSTEAMSIDE team_side);

	void StartLoadedCutScene(
		FCutSceneData &cs,
		int csevent,
		MabMatrix transform,
		bool hide_non_actors,
		bool is_dual,
		SSTEAMSIDE side,
		const char *file_name,
		bool is_non_actor,
		float xclip_min,
		float xclip_max, 
		bool IsRunning );

	bool	IsCutSceneWithTagRunning(int tag);
	
	//StMabEVDSCutScene *GetCutScene(int csevent);

	void	DeregisterExclusionZone();

	float	GetCustomasiationCharacterHeightOffset();


	FVector2D RotatePoint(FVector2D Origin, FVector2D Point, float Angle);

	/// Setup player visibilities...
	void	UpdatePlayerVisibility(bool hide_non_actors);

	void	ClearAllRolesExceptOfficials();


private:
	bool	DoHomeTeamSwitchWhenFaded() { bool rv = do_team_switch; do_team_switch = false; return rv; }
	//bool	DoHomeTeamSwitchWhenFadedToGeneric() { bool rv = do_team_switch_generic; do_team_switch_generic = false; return rv; }
	//bool	DoCompHomeTeamSwitchWhenFaded(){ bool rv = do_comp_team_switch; do_comp_team_switch=false; return rv; }

	/// Event handler for game_events->replay_playback_finish
	void	ReplayFinished();

	/// Calculate angle from 'pos' to the interchange locator for the stadium.
	float	CalculateAngleToInterchangeLocator(FVector pos);

	/// Setup repulsion zones for non actors.
	void	SetupRepulsionZones();

	/// Disable/enable boundry check.
	void	SetEnableBoundryCheck(bool val){ boundry_check_enabled = val; }

	/// While playing the haka we are not allowed to restart the game
	void	EnableRestartGameOption(bool enable) { enable_restart_game_option = enable; }

	/// Stop rendering for 'num_frames' frames.
	void	DisableRendering(int num_frames);

	/// Disable HUD's (2d + 3d) - separated from OnCutSceneStart so can be called for debug.
	void	DisableHUD();
	void	EnableHUD();

	/// Setup background reaction cutscenes.
	void	SetupBGReactionCutscenes(const FVector &position, float positive_z_offset, float negative_z_offset, bool force_x_zero=true);

	/// Set injured player.
	inline void	SetInjuredPlayer(ARugbyCharacter* player){ injured_player = player; }

	FWWUICutsceneLoadComplete OnUICutsceneLoadComplete;

	float LastCutsceneTime = 0.0f;

public:
	float GetLastCutSceneTime() { return LastCutsceneTime; }
	
	///---------------------------------------------------------------

#ifdef ENABLE_RUGED
	//void RestoreCachedFrame(int frame_no);
	//void CacheFrame(int frame_no);
	//bool IsFrameCached(int frame_no);
	//void ClearFrameCache();
#endif

	///--------------------------------------
	/// Cutscene request queue...

	enum CUTSCENE_TYPE {
		CUTSCENE_TYPE_CINEMATIC=0,
		CUTSCENE_TYPE_LOAD_WAIT,
		CUTSCENE_TYPE_LOAD_STOP,
		CUTSCENE_TYPE_REPLAY,
		CUTSCENE_TYPE_CALLBACK,
		CUTSCENE_TYPE_USER_SKIP_POINT,
		CUTSCENE_TYPE_DELAY,
		CUTSCENE_TYPE_SCREEN_WIPE,
		CUTSCENE_TYPE_BACKGROUND_CINEMATIC
	};

	//TeamSelector and SelectorType copied from class Actor : public StMabEVDSCutScene::PropBase, public RUMotionSource in RC3.
	//since the cutscene XML files has these enums.
	enum TeamSelector
	{
		ATTACKING = 0,
		DEFENDING,
		TEAM0,
		TEAM1,
		OFFICIALS,
		CELEBRATING,
		COMMISERATING,
		HAKATEAM,
		NONHAKATEAM
	};

	enum SelectorType
	{
		SHIRTNO = 0,
		BALLHOLDER,
		KICKER,
		LASTSCORER,
		NEAREST,
		TEAMIDX,
		FOCUS_PLAYER,
		GOING_ON_PLAYER,
		GOING_OFF_PLAYER,
		UI_ID,
		CAPTAIN,
		SHIRTNO_ORMAORI,
		HAKA_LEADER,
		INJURED
	};

	class CutsceneCallbackElement;

	typedef bool(*CutsceneCallbackFunction)(SSCutSceneManager*,CutsceneCallbackElement*);
	//typedef bool(*CutsceneMonitorCallbackFunction)(SSCutSceneManager*,CutsceneElement *monitor_element,int state, bool force_end);

	///------------------------------------------------------------------

	class CutsceneElement
	{
	public:
		CutsceneElement();
		virtual ~CutsceneElement(){}

		int				uid;

		CUTSCENE_TYPE	cutscene_type;
		SWIPE_TYPE		swipe_used;
		bool			skipable;

		float			delay;
		bool			disable_simulation;
		bool			wipe_active;

		// async pre-load

		bool			wait_load;
	};


	///---------------------------------------------

	class CutsceneCinematicElement : public CutsceneElement 
	{
	public:
		CutsceneCinematicElement();
		virtual ~CutsceneCinematicElement(){}

		///Called from with in 'CUTSCENE_TYPE_LOAD_WAIT' element processing to start loading of all cinematics belonging to element.
		void PreLoadCutScenes(SSCutSceneManager *manager);
		/// Start loaded cutscenes, and if not loaded load and start them immediately.
		bool StartLoadedCutScenes(SSCutSceneManager *manager);

		/// Cinematics vector accessors.
		inline int	 NumCinematics(){ return (int)cinematics.size(); }
		inline StCinematicElementInfo *GetCinematic(int idx){ return &cinematics[idx]; }

		/// Get 'main' cutscene (first in cinematics)		
		StCinematicElementInfo *GetMainCutScene();

		/// Stop the main cutscene.
		void StopMainCutScene(SSCutSceneManager *manager);

		/// Add a cinematic
		void AddCinematic(int request_id, const MabMatrix &transform, SSTEAMSIDE side);
		/// Add a named cinematic
		void AddNamedCinematic(const char *file_name, const MabMatrix &transform, SSTEAMSIDE side);
		/// Add a background cinematic (non-actors).
		void AddBackgroundCinematic(int request_id, const MabMatrix &transform, SSTEAMSIDE side, float xclip_min, float xclip_max);

		// cinematics

		MabVector < StCinematicElementInfo > cinematics;
		RUTeam			*focus_team;

		RUGamePhase	game_phase_override;
		bool			hide_non_actors;
	};

	///---------------------------------------------

	class CutsceneReplayElement : public CutsceneElement
	{
	public:
		CutsceneReplayElement();
		virtual ~CutsceneReplayElement(){}

		REPLAY_CAMERA_MODE camera_mode;
		float replay_rewind_amount;
		float replay_duration;
		SSReplayBlock* replay;
		REPLAY_TYPE replay_type;

		int		repeat_count;

		///the number of times this replay has played 0 for the first time.
		int		played_count;
	};

	///----------------------------------------------

	class CutsceneCallbackElement : public CutsceneElement
	{
	public:
		CutsceneCallbackElement();
		virtual ~CutsceneCallbackElement(){}

		CutsceneCallbackFunction	callback;
		int							user_info;
		int							callback_state;		// For generic use by callback, initialised  to 0.
		const char					*debug_name;
	};

	///----------------------------------------------

	// HUDInfoMessageCallbackElement, sets hud player info messages during cutscenes
	class HUDInfoMessageCallbackElement : public CutsceneCallbackElement
	{
	public:
		HUDInfoMessageCallbackElement();
		virtual ~HUDInfoMessageCallbackElement(){}
		unsigned int player_db_id;
		RUTeam* team;
		MabString info;
		MabString additional_info;
		ARugbyCharacter* player;
	};

	///----------------------------------------------

	private:
	
	UCutSceneManager* m_UCutsceneManager = nullptr;

	MabVector<CutsceneElement*> cutscene_elements;
	int		last_cutscene_element;
	int		cs_uid;

	CutsceneCinematicElement *last_created_element;

	void StartNextCutsceneElement(bool do_pop = true);
	void RequestElementFinish();
	void RequestStopAllCutscenes();

	/// Helper functions for adding cutscene elements.

	/// Adds a cutscene cinematic element to the list
	void AddCutsceneCinematicElement(RUGamePhase override_phase, bool skipable, SWIPE_TYPE swipe_used, bool hide_non_actors);
	/// Add a cinematic to the last created cutscene element.
	void AddCinematicTLE(int request_id, const MabMatrix &transform, SSTEAMSIDE side);
	/// Add a background cinematic to the last created cutscene element.
	void AddBackgroundCinematicTLE(int request_id, const MabMatrix &transform, SSTEAMSIDE side, float xclip_min, float xclip_max);
	/// Add a named cinematic to the last created cutscene element.
	void AddNamedCinematicTLE(const char *file_name, const MabMatrix &transform, SSTEAMSIDE side);
	/// Adds a callback element to the list
	void AddCutsceneCallbackElement(CutsceneCallbackFunction callback, const char *debug_name, int user_info=-1);
	/// Adds a user skip point element to the list.
	void AddUserSkipPointElement();
	/// Adds an infringement replay element to the list
	void AddInfringementReplayElement( float max_rewind_time, float replay_duration, REPLAY_TYPE type );
	/// Adds a cutscene replay element to the list
	CutsceneReplayElement* AddCutsceneReplayElement( SSReplayBlock* replay, REPLAY_CAMERA_MODE camera_mode, float replay_length, REPLAY_TYPE type, SWIPE_TYPE swipe_used, int num_replays = 1);
	/// Adds a cutscene delay element to the list
	void AddCutsceneDelayElement( int delay );
	/// Adds a cutscene delay element to the list
	void AddCutsceneAsyncPreloadElement(bool wait);
	/// Adds a cutscene async preload element to the list
	void AddCutSceneStopAsyncPreloadElement();
	/// Add a screen wipe (done for first element in sequence).
	void AddScreenWipeElement(SWIPE_TYPE swipe_used = SWIPE_FADE);
	/// Add a background cinematic.
	void AddBGCutsceneCinematicElement(int request_id, const MabMatrix &transform);
	/// Adds a hud player info callback element to the list
	void AddHUDInfoCallbackElement(unsigned int player_db_id, const MabString& additional_info, RUTeam* team, const MabString& info = "", ARugbyCharacter* player = NULL);

	/// Stop all non-primary cutscenes (not first in element).
	void StopDualCutscenes(CutsceneCinematicElement *current);

	/// Cutscene callbacks.
	static bool StartTryCutscenePhaseCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool ConversionBallPlacementCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool StartConversionPhaseCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool StartPreKickOffPhaseCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool StartPostConversionPhaseCallback(SSCutSceneManager *manager, CutsceneCallbackElement*);
	static bool StartKickOffPhaseCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool StartPostGamePhaseCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool StartSimulationPhaseCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
#ifdef ENABLE_SEVENS_MODE
	static bool StartExtraTimeCoinTossPhaseCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);//
	static bool StartPostExtraTimeCoinTossPhaseCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);//
	static bool CoinTossDecisionWindowCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	//static bool CoinTossResultWindowCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
#endif
	static bool FullTimeWindowCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool HalfTimeWindowCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool ProSentOffWindowCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool RestartKickCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool StartFreeKickCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool TeamManagementCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool PenaltyCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool MarkAwardedCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool FourtyTwentyKickAwardedCallback(SSCutSceneManager* manager, CutsceneCallbackElement*);
	static bool RestartOutOnFullCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool RestartKickOffIntoGoalCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool RestartFreeKickR7KickOffPenaltyCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool PreHakaCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool PostHakaCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool CutsceneStartCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool CutsceneEndCallback(SSCutSceneManager *manager, CutsceneCallbackElement*);
	static bool HoldScreenWipeCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool PlayerWalkonCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);
	static bool KnockonCallback(SSCutSceneManager* manager,CutsceneCallbackElement*);
	//static bool StartInjuryCallback(SSCutSceneManager* manager, CutsceneCallbackElement *element);
	static bool StartSubstituteLoadCallback(SSCutSceneManager* manager,CutsceneCallbackElement*);
	static bool ShowInterchangeHUDCallback(SSCutSceneManager* manager,CutsceneCallbackElement*);
	static bool NotifyInterchangeStartedCallback(SSCutSceneManager* manager,CutsceneCallbackElement*);
	static bool DoSubstitutionCallback(SSCutSceneManager* manager,CutsceneCallbackElement*);
	static bool MidSubstitutionCallback(SSCutSceneManager* manager,CutsceneCallbackElement*);
	static bool NotifyMidInterchangeCallback(SSCutSceneManager* manager,CutsceneCallbackElement*);
	static bool StartLineoutCallback(SSCutSceneManager* manager,CutsceneCallbackElement*);
	static bool StartTouchScrumCallback(SSCutSceneManager* manager, CutsceneCallbackElement*);
	static bool SinbinReturnStartCallback(SSCutSceneManager* manager, CutsceneCallbackElement*);
	static bool SentOffEndCallback(SSCutSceneManager* manager, CutsceneCallbackElement*);
	static bool InjuryEndCallback(SSCutSceneManager* manager, CutsceneCallbackElement*);
	static bool PerformRemainingInterchangesCallback(SSCutSceneManager* manager, CutsceneCallbackElement *element);
	static bool StartKickPenaltyShootForGoalCallback(SSCutSceneManager *manager, CutsceneCallbackElement*);
	static bool StartDropoutCallback(SSCutSceneManager *manager, CutsceneCallbackElement*);
	static bool EndDropoutCallback(SSCutSceneManager *manager, CutsceneCallbackElement*);
	static bool PushMusicCallback(SSCutSceneManager* manager, CutsceneCallbackElement *element);
	static bool PopMusicCallback(SSCutSceneManager* manager, CutsceneCallbackElement *element);
	static bool ClearMusicCallback(SSCutSceneManager* manager, CutsceneCallbackElement *element);
	static bool SkipCommentaryCallback(SSCutSceneManager* manager, CutsceneCallbackElement *element);
	static bool BallDeadCallback(SSCutSceneManager* manager, CutsceneCallbackElement* elem);
	static bool SetPreKickOffPhaseCallback(SSCutSceneManager *manager, CutsceneCallbackElement*);
	static bool SetFreeKickPhaseCallback(SSCutSceneManager *manager, CutsceneCallbackElement*);
	static bool SetKickOffKickerCallback(SSCutSceneManager *manager, CutsceneCallbackElement*);
	static bool AwardTryAfterTMOCallback(SSCutSceneManager *manager, CutsceneCallbackElement*);
	static bool CommentaryTMOTryDisallowed(SSCutSceneManager *manager, CutsceneCallbackElement*);
	static bool DisallowTryAfterTMOCallback(SSCutSceneManager *manager, CutsceneCallbackElement*);
	static bool ForwardPassCallback(SSCutSceneManager* manager, CutsceneCallbackElement*);
	static bool SelectInjuryReplacementCallback(SSCutSceneManager *manager, CutsceneCallbackElement *element);
	bool SelectInjuryReplacementCallbackNonStatic(CutsceneCallbackElement *element);
	static bool ShowNextPlayerInfo(SSCutSceneManager* manager, CutsceneCallbackElement*);
	static bool KillAllParticlesCallback(SSCutSceneManager* manager, CutsceneCallbackElement*);
	static bool GrandFinalCelebrationStartCallback(SSCutSceneManager* manager, CutsceneCallbackElement*);
	static bool ProSentOffCallback(SSCutSceneManager *manager,CutsceneCallbackElement*);

	/// Substitution load done callback.
	static void AsyncLoadSubDone(void *user_data, ARugbyCharacter* player, ARugbyCharacter* old_player, bool is_abort);

	///---------------------------------------------------------------
	/// UI cutscene.

	void	UpdateUICutscene();
	static bool UICutsceneStartCallback(SSCutSceneManager* manager, CutsceneCallbackElement*);
	bool	UICutsceneStartCallback();
	bool	IsUIStateReloading(int cs_event);
	static void LoadCustomisationPlayerCallback(void *user_data, ARugbyCharacter* player, ARugbyCharacter* old_player, bool is_abort);

	//#rc3_legacy static void CompHomeTeamSwapAsyncJob(void *user_data, SIFAsyncLoadingThread *load_thread, bool is_abort);//#Nirupam
	//#rc3_legacy void CompHomeTeamSwapAsyncJobNonStatic(SIFAsyncLoadingThread *load_thread, bool is_abort);//#Nirupam

	int					ui_cutscene_state;
	MabVector<int>		requested_ui_cutscene_state;
	MabVector<int>		ui_cutscene_faded_actions;
	int					current_ui_faded_action;

	int		custom_player_db_id;
	int		custom_player_strip_id;
	int		custom_player_team_id;

	MabTime start_time;
	MabTime last_frame_time;

	MabTime timeout;

public:
	/// Mid fade ui-cutscene actions.
	enum {
		UI_CUTSCENE_CHANGE_NO_ACTION=0,

		UI_CUTSCENE_CHANGE_RESTORE_STRIPS,
		UI_CUTSCENE_CHANGE_RESTORE_STRIPS_WAIT,
		UI_CUTSCENE_LOAD_CUSTOMISATION_PLAYER,
		UI_CUTSCENE_LOAD_CUSTOMISATION_PLAYER_WAIT,
		UI_CUTSCENE_LOAD_LIST_TEAMS,
		UI_CUTSCENE_LOAD_LIST_PLAYERS,
		UI_CUTSCENE_LOAD_CUSTOMISATION_PLAYER_LOADED,
		UI_CUTSCENE_DELETE_CUSTOMISATION_PLAYER,
		UI_CUTSCENE_CHANGE_SANDBOX_START,
		UI_CUTSCENE_CHANGE_TRAINING_START,
		UI_CUTSCENE_CHANGE_ONLINE_LOBBY_START,
		UI_CUTSCENE_CHANGE_STOP_ALL_CUTSCENES,
		UI_CUTSCENE_CHANGE_START_ASYNC_LOADING,
		UI_CUTSCENE_CHANGE_LAUNCH_PENDING_LEVEL,

		UI_CUTSCENE_FORCE_PLAYER_LIKENESS,
		UI_CUTSCENE_FORCE_PLAYER_LIKENESS_WAIT
	};
	
	void	SetUICutscene(int cs_event, int mid_fade_action = 0);
	void	ResetUICutscene();
	void	SetUIFadeAction(int mid_fade_action);
	void	SetCustomisationPlayer(int db_id, int strip_id, int team_id);
	
	int		GetCustomisationPlayerId(){ return custom_player_db_id; }

	void CutSceneEnded(UCutScenes* CurrentCutscenes, bool bPause);

	void CutSceneStarted(UCutScenes* CurrentCutscenes);

	void CutScenePaused(UCutScenes* CurrentCutscenes);

	void CutSceneResume(UCutScenes* CurrentCutscenes);

	bool IsRestartEnabled() { return enable_restart_game_option; }

	///---------------------------------------------------------------

	inline int	GetNumElements(){ return (int)cutscene_elements.size(); }

	ALevelSequenceActor* PlaySequence(FCutSceneData* selectedCutSceneData); //made it public to allow playing sequence externally.

	void GetPlayerForCutScene(int &teamno, int &selector, int& shirtno, bool& get_player_on_bench, ARugbyCharacter ** selected_player, TArray<ARugbyCharacter *> CutScene_player_List, int TeamIndex = -1);

	bool IsPlayingLoopCutScene();

	bool IsHideNonActors();		

private:

	//----------------

	class CameraBlend
	{
	public:
		CameraBlend(MabEVDSEvent* event);
		void UpdateData(MabEVDSEvent* event);

		unsigned int	camera_uid;
		float			blend_in_time;
		float			blend_out_time;
		bool			disable_sim;
	};

	//----------------

	CameraBlend* GetCameraBlendInfo(unsigned int cam_uid);


	MabVector<CameraBlend*>			camera_blends;

	//----------------

	/// Enum of available hud displays that can be fired by the HUDEvent StencilEd event
	enum HudDisplays
	{
		HD_HISTORIC_DATA=0
	};

	/// Async-load state.
	enum {
		AST_DONE=0,
		AST_LOADING,
		AST_LOADED,
	};
	
	//----------------

	SIFGameWorld		*game;

	bool				should_skip;
	bool				skip_requested;
	bool				force_finish_current_cutscene;
	bool				force_finish_current_cutscene_skip;

	bool				local_disable_skip;
	int					disable_skip_count;

#ifdef ENABLE_RUGED
	bool		edit_attack_disabled;
	bool		edit_defence_disabled;
	bool		edit_ruged_debug_disabled;
#endif

	//------------------------
	// Animation frame cache..

#ifdef ENABLE_RUGED
	class CachedFrame
	{
	public:
		CachedFrame(int fframe_no, SIFGameWorld *ggame);
		~CachedFrame();
		void	Restore(SIFGameWorld *game);

		int		frame_no;
		void	*data;
	};

	MabVector<CachedFrame*> frame_cache;
#endif

	bool			doing_ui_transition;
	MabString		ui_transition_target;

	bool			cinematics_enabled;
	bool			replays_enabled;

	int				cutscene_profile_idx;
	int				replay_profile_idx;

	bool			request_element_finish;
	bool			request_stop_all_cutscenes;

	int				stop_rendering_count;

	bool			wipe_on_cut;
	bool			enable_placeholder_cutscenes;

	FVector			exclusion_center;
	bool			exclusion_zone_on;
	float			exclusion_range;

	bool			disable_visibility_update;

	FVector			player_creator_target_offset;
	FVector			player_creator_offset;
	bool			player_creator_use_height_offset;

	FRotator			player_creator_camera_target_angle;


	FRotator		player_creator_camera_angle;
	bool			player_creator_on = false;

	FVector		player_creator_transform;
	MabMatrix		player_creator_transform_notrans;

	FMOD::Studio::EventInstance* player_creator_rotate_sound;
	float			player_creator_rotate_sound_timer;

	int				async_wait_state;
	bool			received_ready_signal;

	int				substitute_loading_state;
	bool			received_substitute_ready_signal;

	RUTeam			*celebrating_team;
	ARugbyCharacter	*focus_player[SIDE_NONE+1];

	int				interchange_type;
	ARugbyCharacter	*going_off_player;
	ARugbyCharacter	*going_on_player;
	ARugbyCharacter	*injured_player;
	SSReplayBlock	*advantage_replay;
	bool			advantage_inaffect;
	int				advantage_end_countdown;
	float			advantage_last_started_time;

	TMap<int, FString> last_filename_cache;	

	bool			disable_substitutions;

	/// Half restart information: Used in RestartKick call back.

	SSGT_HALF		next_half;
	
	EXTRATIME		next_extratime_mode;

	// Set if haka is about to be played.
	bool			will_do_haka;
	bool			playing_haka;
	float			haka_commentary_delay;
	float			total_haka_comentary_delay;
	float			haka_skip_disable_time;

	bool			enable_restart_game_option;

	RUGamePhase		game_phase_override;

	int				competition_ui_trophy_id;

	int				match_emotion_level;

	// For debug.
	long			last_debug_key_used;

	//bool			do_team_switch_generic;				// force the team swap to generic dudes

	bool			do_team_switch;						// set to do team switch.

	//bool			do_comp_team_switch;				// set to do team switch for comp.

	bool			load_non_permanent_players;			// set to request load of non-permanent players.

	bool			replay_finished;					// set by replay finish event.

	/// Injury hud.
	int				injury_hud_selected_player;			// Hud sets this.

	/// Skip multiple cutscenes till a replay/skip point is found (or cutscene_elements is empty())
	bool			multi_skip;

	/// Enable/disable boundry check (normally on, apart from walkons).
	bool			boundry_check_enabled;

	/// Disable Tick if set. (reset by SyncUpdate - next frame, apart from network games which just lock out update for 2 sim frames)
	int				next_frame_lock;

	/// HACK: Set when request ui_main_menu, and reset otherwise. Monitored in update, to start main_menu cutscene should it not be running.
	bool			should_be_in_main_menu_hack;

	/// Should background cutscenes be enabled?
	bool			bg_cutscenes_enabled;

	/// Last minute crash prevention, stop multiple ball-deads breaking the cutscene system (OOM)
	bool			have_queued_ball_dead_cutscene;

	/// Keep track of wether hud has been enabled/disabled, for hard-reset.
	bool			cutscene_hud_disabled;

	/// Count number of times UI cutscene element has fired, after NUM_REPEATS_BEFORE_FORCE_LOOP ui cutscenes will loop if they can. (PS3 Soak on main menu crash).
	int				ui_repeat_cutscene_count;

#ifdef ENABLE_GAME_DEBUG_MENU
	SIFDebugDrawPool* debug_key_pool;
#endif

	bool			pro_player_sent_off_during_interchanges; 

	CutsceneCallbackFunction suspended_callback_from_pro_send_off;

	const char *	suspended_callback_from_pro_send_off_debug_name;

	/// Hack method to set the "winning team"--aka the team that should be raising a trophy--to the team that's won the Bledisloe.
	void SetBledisloeWinningTeam( const RUActiveCompetitionBase* active_competition );
	
	//Sequencer stuffs. Added newly for RC4
	//=====================================

	//void InitialiseCutScenePropsDataTable();
	void InitialiseCutSceneEffectsDataTable();	

	bool IsHideBall();

	void CleanUpPlayerCreator(StCinematicElementInfo *ci);

	void ModifySpawnableProp (const FPossessablePropData& propData, ULevelSequence* pSequence, ALevelSequenceActor* pLevelSequenceActor, ULevelSequencePlayer* pSequencePlayer);

	void ModifySpawnableParticleFX (const FPossessableParticleData& particleData, ULevelSequence* pSequence, ALevelSequenceActor* pLevelSequenceActor, ULevelSequencePlayer* pSequencePlayer);

	void PlayerCreatorPlayAnimation (StCinematicElementInfo* ci, float StartPosition = 0.0f );	

	void CutSceneParticleTickEnable (bool bEnable, UCutScenes* CurrentCutscenes);

	//Some Methods
	void InitialiseCutSceneDB();	
	//FCutscenePropRec* GetPropRec(FString PropName);
	void PositionCamera (float time, UCutScenes* CurrentCutscenes);
	TArray <FCutsceneRec*> GetCutsceneRec(int id, int isRc7);
	FCutSceneData* LoadCutScene(FCutSceneData CutsceneDataFromJson, FString FolderName, FString Directory, FString FileName);
	//FPropData UpdateProp (FSerialisePropData *Data, int visible, ARugbyCharacter *attach_actor, int attach_joint, FTransform *unrealTransform, StCinematicElementInfo* CinematicInfoPtr);
	static FVector GetPathPosition(float swing_time_through, FSerialiseTypePath& path, MabNURBSSpline& spline);
	static float GetPathParametricFromDistance(float distance, MabNURBSSpline& spline, float	length, FSerialiseTypePath& path);
	void StopSequencer(StCinematicElementInfo *ci);
	void PauseSequencer(StCinematicElementInfo *ci, CUTSCENE_STATE state = CUTSCENE_STATE::CST_PAUSED );
	TArray <ULevelSequencePlayer*> m_PausedSequence;	

	//Some Members.
	int m_cutsceneRecCount = 0;
	TArray <FCutsceneRec> m_cutsceneRecArray;
	TArray <FCutscenePropRec> m_cutscenePropRecArray; //data from the datatables for all prop info
	TMap<ECutsceneParticleType, FString> m_cutsceneEffectsRecArray; //data from the datatables for all particle effect info
	float m_TimeLineForCamera = 0.0f;
	ARugbyCharacter *m_selected_player = nullptr;
	TArray <ARugbyCharacter*> m_CutScene_player_List;//A temp Array
	FMovieSceneObjectBindingID m_PrevchosenCameraBindingId;
	int m_PrevSelectCameraTimeLine = -1;
	int m_SelectedCameraIndex = 0;
	float m_MainCutSceneLength = -1.0f;
	int CurrentCutsceneRecId = CSEVENT_UNUSED; //Not needed
	bool running_cinematic = false;
	bool IsCreaturesDisabled = false;
	bool showSimulationBeforeFullTime = false;
	bool force_disable_simulation = false;	
	bool IsBackGroundCutsceneInProgress = false;	
	FName m_PlayerCreatorPrevAnimName = "";
	FRugbyMontageInstance* m_PlayerCreatorMontageInstance = nullptr;	

	static RUTeam* s_pRestartTeam;
};


#endif	// RU_CUTSCENE_MANAGER_H
