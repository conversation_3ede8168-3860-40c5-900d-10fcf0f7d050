/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/Rules/Offside/RUOffsideTenMeters.h"

#include "Match/AI/Roles/Competitors/RURoleKickOffKicker.h"
#include "Match/AI/Roles/Competitors/RURoleShootForGoal.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBallDefender.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBallSecondDefender.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SIFGameObject.h"
#include "Match/SIFGameWorld.h"

#if defined BUILD_DEBUG || (defined ENABLE_SIF_DEBUG_DRAW && defined ENABLE_GAME_DEBUG_MENU)
//#rc3_legacy_include #include "SIFDebug.h"
#include "Match/Debug/RUGameDebugSettings.h"
#endif

MABRUNTIMETYPE_IMP1( RUOffsideTenMeters, SSOffside );

RUOffsideTenMeters::RUOffsideTenMeters( SIFGameWorld* ggame, RUGameOffside* ooffside )
: SSOffside( ggame, ooffside )
{
}

void RUOffsideTenMeters::Enter()
{
#if defined ENABLE_SIF_DEBUG_DRAW && defined ENABLE_GAME_DEBUG_MENU
	if(SIFDebug::GetGameDebugSettings()->GetOffsidePlayersVisible())
	{
		SIF_DEBUG_DRAW(SetText(DEBUG_OFFSIDE_PLAYER_KEY - 5, 200, 200, "OFFSIDE_TEN_METERS", MabColour::White));
	}
#endif

	SSOffside::Enter();
}

void RUOffsideTenMeters::Exit()
{
#if defined ENABLE_SIF_DEBUG_DRAW && defined ENABLE_GAME_DEBUG_MENU
	if(SIFDebug::GetGameDebugSettings()->GetOffsidePlayersVisible())
	{
		SIF_DEBUG_DRAW(RemoveText(DEBUG_OFFSIDE_PLAYER_KEY - 5));
	}
#endif

	SSOffside::Exit();
}

void RUOffsideTenMeters::UpdateLogic( const MabTimeStep& delta_game_time )
{
	// in case of a turn over where the phase is unchanged
	if(game->GetGameState()->GetAttackingTeam()->GetSide() != attack.side)
		Enter();

	float offside_line = game->GetGameState()->GetPlayRestartPosition().z;
	attack.offside_line = offside_line;
	defence.offside_line = offside_line - 10.0f * defence.play_direction;
	MabMath::Clamp(defence.offside_line, -FIELD_LENGTH * 0.5f, FIELD_LENGTH * 0.5f);

	UpdateOnside( attack.players, attack.offside_line, false );
	UpdateOnside( defence.players, defence.offside_line, false );
	UpdateOffside( attack.players, attack.offside_line );
	UpdateOffside( defence.players, defence.offside_line );

	SSOffside::UpdateLogic( delta_game_time );
}

void RUOffsideTenMeters::UpdateOffside( const SIFRugbyCharacterList& players, float offside_line ) const
{
	ARugbyCharacter* ballholder = game->GetGameState()->GetBallHolder();

	for ( ARugbyCharacter* player : players )
	{
		MABASSERT( player );
		bool is_ptb_defender = MabCast<RURolePlayTheBallDefender>(player->GetRole());
		bool is_ptb_sdefender = MabCast<RURolePlayTheBallSecondDefender>(player->GetRole());

		// GGS AJ: If player has PTB defender role, clear any existing offside status
		if (is_ptb_defender || is_ptb_sdefender)
		{
			ResetOffsidePlayer(player);
		}
		else if ( !MabCast<RURoleShootForGoal>(player->GetRole()) && !MabCast<RURoleKickOffKicker>(player->GetRole()) && 
			!IsPlayerBehindPos(offside_line, player) && player != ballholder )
		{
			SetPlayerOffside( player, OFFSIDE_REASON_PENALTY, offside_line );
		}
	}
}
