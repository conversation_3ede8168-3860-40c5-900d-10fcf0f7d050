// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIScreenInGameInjury.h"
#include "Utility/Helpers/SIFGameHelpers.h"
#include "Utility/Helpers/SIFInGameHelpers.h"
#include "RugbyGameInstance.h"

// UI
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "UI/GeneratedHeaders/WWUIScreenInGameInjury_UI_Namespace.h"

#include "UI/Populators/WWUIPopulatorInGameSquad.h"

#include "UI/Components/WWUIUserWidgetPlayerProfile.h"
#include "UI/Components/WWUIUserWidgetSquadLayout.h"
#include "WWUIRichTextBlockWithTranslate.h"
#include "WWUIScrollBox.h"
#include "TextBlock.h"
#include "WWUIScreenCareerPlayerProfile.h"
#include "WWUIFunctionLibrary.h"
#include "WWUITranslationManager.h"
#include "WWUIListField.h"
#include "UI/GeneratedHeaders/WWUIScreenCareerTeamSquad_UI_Namespace.h"
#include "UI/Components/WWUIUserWidgetCareerSquadText.h"
#include "ProgressBar.h"
#include "Slate/SlateBrushAsset.h"
#include "SlateTypes.h"
#include "Match/Components/SSHumanPlayer.h"
#include "WidgetTree.h"
#include "Utility/Helpers/SIFUIHelpers.h"

void UWWUIScreenInGameInjury::Startup(UWWUIStateScreenData* _inData)
{
	//< Replicate in data to the injury screen. >
	if (UWWUIScreenInGameInjuryData* _injuryData = Cast<UWWUIScreenInGameInjuryData>(_inData))
	{
		InData = NewObject<UWWUIScreenInGameInjuryData>();
		InData->TeamIndex = _injuryData->TeamIndex;
		InData->DefaultSelectedPlayerID = _injuryData->DefaultSelectedPlayerID;
		InData->PlayerDatabaseID = _injuryData->PlayerDatabaseID;
		InData->PlayerIndex = _injuryData->PlayerIndex;
		InData->IsInjurySubstitution = _injuryData->IsInjurySubstitution;
	}

	help_tip		= Cast<UWWUIRichTextBlockWithTranslate>(	FindChildWidget(WWUIScreenInGameInjury_UI::WWUIRichTextBlockWithTranslate_0));

	UWidget* pProfileScrollBoxTemplate = FindChildWidget(WWUIScreenInGameInjury_UI::BP_PlayerProfileAndScrollboxTemplate);
	if (pProfileScrollBoxTemplate)
	{
		player_stats = Cast<UWWUIUserWidgetPlayerProfile>(FindChildOfTemplateWidget(pProfileScrollBoxTemplate, WWUIScreenInGameInjury_UI::PlayerProfile));
	}
	squad_layout	= Cast<UWWUIUserWidgetSquadLayout>(		FindChildWidget(WWUIScreenInGameInjury_UI::SquadField));

	player_list_box = Cast<UWWUIScrollBox>(	FindChildWidget(WWUIScreenInGameInjury_UI::PlayerScrollBox));
	assistance_text = Cast<UTextBlock>(		FindChildWidget(WWUIScreenInGameInjury_UI::TextAssistance));

	ensure(player_stats && squad_layout && player_list_box && assistance_text);
}

void UWWUIScreenInGameInjury::Shutdown()
{
	EnableUnnecessaryControllers();
	Super::Shutdown();
}

void UWWUIScreenInGameInjury::RegisterFunctions()
{
	AddInputAction("RU_UI_ACTION_CAREERMODE_PLAYER_PROFILE", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenInGameInjury::OpenPlayerProfile));
}

bool UWWUIScreenInGameInjury::OnSystemEvent(WWUINodeProperty& eventParams)
{
	FString system_event = eventParams.GetStringProperty("system_event");
	int selected_index_prop = eventParams.GetIntProperty("selected_index");
	UE_LOG(LogTemp, Display, TEXT("TitleScreen.OnSystemEvent %s"), *system_event);

	if (system_event == "career_squad_list_update")
	{
		int selectedIndex = (selected_index_prop == MAX_int32) ? player_list_box->GetSelectedIndex() : selected_index_prop;

		//Update selected bar
		UWWUIListField* selected_node = player_list_box->GetListField(selectedIndex);
		//local selected_node_anchor = UINodeGetAnchor(selected_node)
		//local hightlight_anchor = UINodeGetAnchor(InGameInjury.player_list_hightlight)
		//hightlight_anchor.y = selected_node_anchor.y
		//UIFXNodeStopAllAnimations(InGameInjury.player_list_hightlight)
		//UIFXNodeSlideAnchorTo(InGameInjury.player_list_hightlight, hightlight_anchor, 0.16667, 0)

		//Get player id from selected node
		selected_player_db_id = UWWUIFunctionLibrary::GetIntProperty("career_squad_player_id", selected_node);
		selected_player_index = UWWUIFunctionLibrary::GetIntProperty("career_squad_player_list_index", selected_node);

		int atlas_index = team_index;

		// Update player stat
		int player_index = jersey_numbers[selected_player_index];

		if (player_stats)
		{
			player_stats->PopulateShortProfile(team_id, injured_player_db_id, selected_player_db_id, selected_player_index);
		}

		//Update squad layout
		squad_layout->SwapPlayer(-1);
		squad_layout->Select(selected_player_index);

		//Update help tip
		UpdateHelpText();

		return true;
	}

	return false;
}

struct FFindFocusableWidget
{
	UWidget* FocusableWidget = nullptr;

	void operator()(UWidget* InWidget)
	{
#ifdef UI_LOG_FINDFOCUSABLEWIDGET
		UE_LOG(LogTemp, Warning, TEXT("FFindFocusableWidget %s"), *InWidget->GetName());
#endif

		if (FocusableWidget != nullptr)
			return;

		if (!InWidget->IsVisible())
		{
#ifdef UI_LOG_FINDFOCUSABLEWIDGET
			UE_LOG(LogTemp, Warning, TEXT("- Not visible"));
#endif

			return;
		}

		// UUserWidgets have bIsFocusable
		// SWidget have SupportsKeyboardFocus

		UUserWidget* UserWidget = Cast<UUserWidget>(InWidget);
		if (UserWidget != nullptr)
		{
			if (UserWidget->bIsFocusable)
			{
#ifdef UI_LOG_FINDFOCUSABLEWIDGET
				UE_LOG(LogTemp, Warning, TEXT("- Chosen Widget : %s"), *InWidget->GetName());
#endif

				FocusableWidget = InWidget;
				return;
			}
			else
			{
#ifdef UI_LOG_FINDFOCUSABLEWIDGET
				UE_LOG(LogTemp, Warning, TEXT("- Not focusable"));
#endif
			}
		}
		else
		{
#ifdef UI_LOG_FINDFOCUSABLEWIDGET
			UE_LOG(LogTemp, Warning, TEXT("- Not UserWidget"));
#endif
		}

		if (Cast<UButton>(InWidget))
		{
#ifdef UI_LOG_FINDFOCUSABLEWIDGET
			UE_LOG(LogTemp, Warning, TEXT("- Chosen Widget button : %s"), *InWidget->GetName());
#endif

			FocusableWidget = InWidget;
		}
	}
};

void UWWUIScreenInGameInjury::SetFocusForControllers(APlayerController* InController /*= nullptr*/)
{
	//< Set generic focus for all controllers. >
	//< Set Master Controller focus to stored focus object. >
	if (RUTeam* team = SIFApplication::GetApplication()->GetActiveGameWorld()->GetTeam(InData->TeamIndex))
	{
		if (SSHumanPlayer* primary_team_player = team->GetHumanPlayer(0))
		{
			MasterPlayerController = primary_team_player->GetPlayerController();
		}
	}

	if (!MasterPlayerController)
	{
		/// If master controller is invalid, revert to super method. 
		Super::SetFocusForControllers(InController);
		return;
	}

	//< Get Master Controller ID. >
	if (!MasterPlayerController->IsLocalController())
		return;

	ULocalPlayer* LocalPlayer = MasterPlayerController->GetLocalPlayer();
	int32 ControllerId = (LocalPlayer != nullptr) ? LocalPlayer->GetControllerId() : INDEX_NONE;
	if (ControllerId < 0)
		return;

	TSharedPtr<SWindow> window;
	FWidgetPath WindowWidgetPath;
	UWidget* focusWidget = nullptr;
	if (FocusWidgetMap.Contains(MasterPlayerController))
	{
		focusWidget = FocusWidgetMap[MasterPlayerController];
	}
	else
	{
		UObjectProperty* ObjectProperty = FindField<UObjectProperty>(GetClass(), FName("firstWidget"));
		if (ObjectProperty)
		{
			UObject* WidgetPtr = ObjectProperty->GetPropertyValue_InContainer(this);
			focusWidget = Cast<UWidget>(WidgetPtr);
		}

		// If we didn't get a default widget search for a fallback
		if (focusWidget == nullptr)
		{

			FFindFocusableWidget Predicate;

			if (WidgetTree)
				WidgetTree->ForEachWidgetAndDescendants(Predicate);

			UWidget* focusableWidget = Predicate.FocusableWidget;

			if (UOBJ_IS_VALID(focusableWidget))
			{

				focusWidget = focusableWidget;
			}
		}

		// If we didn't get a fallback...
		// ScreenTemplate itself usually not focusable so fallback to containing window as per DropIn. 
		// User should still be able to register input, backout of screen etc which is better than nothing

		if (focusWidget == nullptr)
		{
			if (bIsFocusable)
			{
				focusWidget = this;
			}
			else
			{
				window = FSlateApplication::Get().FindWidgetWindow(TakeWidget(), WindowWidgetPath);
			}
		}
	}

	// rip
	if (!UOBJ_IS_VALID(focusWidget) && !window.IsValid())
		return;

	if (focusWidget != nullptr)
		SetFocusToWidget(focusWidget, MasterPlayerController);
	else if (window.IsValid())
		FSlateApplication::Get().SetUserFocus(ControllerId, WindowWidgetPath, EFocusCause::SetDirectly);

	DisableUnnecessaryControllers();
}

void UWWUIScreenInGameInjury::DisableUnnecessaryControllers()
{
	//< Disable all controllers that aren't already disabled apart from our master controller. >
	for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
	{
		if (APlayerController* PlayerController = Cast<APlayerController>(*Iterator))
		{
			if (PlayerController == MasterPlayerController) continue;

			ULocalPlayer* LocalPlayer = PlayerController->GetLocalPlayer();
			int32 ControllerId = (LocalPlayer != nullptr) ? LocalPlayer->GetControllerId() : INDEX_NONE;

			if (SIFUIHelpers::ListeningToController(ControllerId))
			{
				/// Store disabled controller for enabling later.
				DisabledPlayerControllerIDs.Add(ControllerId);
				SIFUIHelpers::ListenToController(ControllerId, false);
			}
		}
	}
}

void UWWUIScreenInGameInjury::EnableUnnecessaryControllers()
{
	//< Enable all controllers stored from earlier. >
	for (int i = DisabledPlayerControllerIDs.Num() -1; i >= 0; i--)
	{
		if (APlayerController* PlayerController = SIFApplication::GetApplication()->GetLocalPlayerControllerFromControllerId(DisabledPlayerControllerIDs[i]))
		{
			if (PlayerController == MasterPlayerController) continue;

			/// Enable controller & remove from array.
			SIFUIHelpers::ListenToController(DisabledPlayerControllerIDs[i], true);
			DisabledPlayerControllerIDs.RemoveAt(i);
		}
	}
}

void UWWUIScreenInGameInjury::TableOnSelectionChange(FString InTableId, int OldIdx, int NewIdx)
{
	UE_LOG(LogTemp, Display, TEXT("WWUIScreenInGameInjury : OnInFocus"));

	// Set common properties for the system event
	WWUINodeProperty UpdateEvent = WWUINodeProperty();
	FString EventName = "career_squad_list_update";
	UpdateEvent.SetProperty("system_event", &EventName, PROPERTY_TYPE_FSTRING);
	UpdateEvent.SetProperty("selected_index", &NewIdx, PROPERTY_TYPE_INT);

	OnSystemEvent(UpdateEvent);

	if (UUserWidget* oldListField = player_list_box->GetListField(OldIdx)) 
	{
		if (UProgressBar* fatigueBar = Cast<UProgressBar>(UWWUIFunctionLibrary::FindChildWidget(UWWUIFunctionLibrary::FindChildWidget(oldListField, WWUIScreenInGameInjury_UI::CareerSquadText), WWUIScreenInGameInjury_UI::FatigueBar)))
			fatigueBar->WidgetStyle.BackgroundImage.TintColor = FSlateColor(FLinearColor(0.283149f, 0.090842f, 0.013702f));
	}
	if (UUserWidget* newListField = player_list_box->GetListField(NewIdx)) 
	{
		if (UProgressBar* fatigueBar = Cast<UProgressBar>(UWWUIFunctionLibrary::FindChildWidget(UWWUIFunctionLibrary::FindChildWidget(newListField, WWUIScreenInGameInjury_UI::CareerSquadText), WWUIScreenInGameInjury_UI::FatigueBar)))
			fatigueBar->WidgetStyle.BackgroundImage.TintColor = FSlateColor(FLinearColor(1.0f, 1.0f, 1.0f));
	}
}

void UWWUIScreenInGameInjury::ExecuteTableFunction(FString InTableId, int InIdx, FString InAction, FString InActionString)
{
	//UpdateScrollbarNextFrame();

	//The currently selected has been requested for the injury substitution.
	//We need to ensure that this is a valid play to substitute with - they player may have already been substituted.
	if (SIFInGameHelpers::IsPlayerAValidSubstitution(selected_player_db_id, team_index))
	{
		SIFInGameHelpers::ApplyInjurySubstitution(selected_player_db_id);
		SIFInGameHelpers::DismissInjurySubstitutionMenu();
	}
}

void UWWUIScreenInGameInjury::OnTimeoutUpdateScrollbar()
{
	InitSquadLayout();
	squad_layout->Select(selected_player_index);
}

void UWWUIScreenInGameInjury::OnTimeoutMaxSubstitutionTime()
{

}

void UWWUIScreenInGameInjury::OnInFocus()
{
	UE_LOG(LogTemp, Display, TEXT("WWUIScreenInGameInjury : OnInFocus"));
	
	//< Configure squad layout object >
	// Nick  WWS 7s to Womens //
	/*
	if (SIFGameHelpers::GAGetGameMode() == 1)
	{
		//UINodeSetVisible(squad_layout, false);
		//UINodeSetVisible(sevens_squad_layout, true);
		total = 13;
	}
	else
	{
	*/
		//UINodeSetVisible(squad_layout, true);
		//UINodeSetVisible(sevens_squad_layout, false);
		total = 24;
	//}

	MABASSERT(InData->TeamIndex != MAX_int32);
	if (InData->TeamIndex != MAX_int32)
	{
		team_index = InData->TeamIndex;
	}

	ideal_substitution_id = InData->DefaultSelectedPlayerID;
	injured_player_db_id = InData->PlayerDatabaseID;
	injured_player_index = InData->PlayerIndex;

	//< Set up callback for running out of time to select a substitution player. >
	UWWUIFunctionLibrary::OnTimer(TIMER_MAX_INJURY_SUB_DURATION, FTimerDelegate::CreateUObject(this, &UWWUIScreenInGameInjury::OnTimeoutMaxSubstitutionTime));

	jersey_numbers.Empty();
	for(int i = 0; i < total; i++)
	{
		jersey_numbers.Add(i + 1);
	}

	// Set property for populator.
	UWWUIPopulatorInGameSquad* populator = Cast<UWWUIPopulatorInGameSquad>(player_list_box->GetPopulator());
	MABASSERT(populator);
	
	bool tmpBool = true;
	populator->SetListNode(player_list_box);

	UWWUIFunctionLibrary::SetProperty("team_index", &team_index, player_list_box, PROPERTY_TYPE_INT);
	UWWUIFunctionLibrary::SetProperty("is_injury", &tmpBool, player_list_box, PROPERTY_TYPE_BOOL);

	player_list_box->PopulateAndRefresh();
	player_list_box->SetSelectedIndex(0, false, EDescendantScrollDestination::IntoView, MasterPlayerController);
	RefreshInjuredPlayerPanel();

	//Squad position layout
	InitSquadLayout();
	UpdateScrollbarNextFrame();

	//UISetTimer(TIMER_HELP_TEXT, ui_node, 0.2)

	if(!returning_from_profile)
	{
		//UISetTimerForFrameCount(SET_DEFAULT_PLAYER, ui_node, 2)
	}
	returning_from_profile = false;

	//UIDisableInGameInput(true);

	//Set the active controller
	//UIListenToNoControllers()
	//UIListenToTeamController(team_index, true)
}

void UWWUIScreenInGameInjury::OpenPlayerProfile(APlayerController* OwningPlayer)
{
	if (SIFInGameHelpers::IsPlayerAValidSubstitution(selected_player_db_id, team_index))
	{
		UWWUIStateScreenCareerPlayerProfileData* inData = NewObject<UWWUIStateScreenCareerPlayerProfileData>();

		inData->PlayerIndex = selected_player_index;
		inData->PlayerDatabaseID = selected_player_db_id;
		inData->TeamID = team_id;
		inData->AtlasID = team_index;
		inData->BreadCrumbOverride = "[ID_MANAGE_TEAM]";
		inData->IsInGame = true;

		SIFApplication::GetApplication()->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CareerPlayerProfile, inData);
	}
}

void UWWUIScreenInGameInjury::RefreshInjuredPlayerPanel()
{
	SIFGameWorld *game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	RUTeam *team = game_world->GetTeam(team_index);
	RUDB_PLAYER *db_player = team->GetDbPlayerById(injured_player_db_id);

	PlayerInfo player_info;

	player_info.db_id = (unsigned short)injured_player_db_id;
	player_info.db_player = db_player;
	player_info.shirt_number = (short)team->GetShirtNumberFromPlayerDbId(player_info.db_id);

	// MULTI_POSITION_CATEGORY_CHANGE
	// Nick WWS 7s to Womens 13s //
	//if (team->GetIsR7ExclusiveTeam())
	//{
	//	player_info.primary_position = db_player->GetPositionCategoryR7(0);
	//	player_info.secondary_position = db_player->GetPositionCategoryR7(1);
	//}
	//else
	//{
		player_info.primary_position = db_player->GetPositionCategoryR13(0);
		player_info.secondary_position = db_player->GetPositionCategoryR13(1);
	//}
	player_info.overall_rating = (short)db_player->GetOverallRating();


	if (UWidget* nodeContainer = UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenInGameInjury_UI::InjuredPlayer))
	{
		//< Apply text color. >
		if (UWWUIUserWidgetCareerSquadText* node = Cast<UWWUIUserWidgetCareerSquadText>(UWWUIFunctionLibrary::FindChildWidget(nodeContainer, WWUIScreenCareerTeamSquad_UI::CareerSquadText)))
		{
			UWWUIPopulatorInGameSquad::SetLineText(this, node, player_info, team_index);
			node->SetSquadTextColor(FLinearColor(0.955974f, 0.162029f, 0.127438f));
			if (UWidget* secondPos = UWWUIFunctionLibrary::FindChildWidget(node, WWUIScreenInGameInjury_UI::TextSecondPosition))
				secondPos->SetVisibility(ESlateVisibility::Hidden);
		}

		//< Hide dividing line. >
		if (UWidget* node = FindChildOfTemplateWidget(nodeContainer, WWUIScreenCareerTeamSquad_UI::ListFieldWithDividerDefault))
		{
			if (UWidget* dividing_line = UWWUIFunctionLibrary::FindChildWidget(node, WWUIScreenInGameInjury_UI::DividingLine))
			{
				dividing_line->SetRenderOpacity(0.0f);
			}
		}
	}
}

void UWWUIScreenInGameInjury::UpdateScrollbarNextFrame()
{
	UWWUIFunctionLibrary::OnFrameDelay(2, FTimerDelegate::CreateUObject(this, &UWWUIScreenInGameInjury::OnTimeoutUpdateScrollbar));
}

void UWWUIScreenInGameInjury::InitSquadLayout()
{
	squad_layout->InitSquadLayout(jersey_numbers);

	//------------------------------------------------------
	//Read encoded player states from game(RUCareerDraftPopulator::Refresh)
	UWWUIPopulatorInGameSquad* populator = Cast<UWWUIPopulatorInGameSquad>(player_list_box->GetPopulator());
	TArray<EPlayerState> player_states = populator->GetPlayerStates();

	for (int i = 0; i < total; i++)
	{
		if (player_states.IsValidIndex(i))
			squad_layout->Mark(i, (uint8)player_states[i]);
	}

	squad_layout->UpdateSinbin();
	squad_layout->Animate(injured_player_index + 1, "squad_injury_flash");
}

void UWWUIScreenInGameInjury::UpdateHelpText()
{
	FString help_string = (SIFInGameHelpers::IsPlayerAValidSubstitution(selected_player_db_id, team_index)) ? "[ID_INGAME_INJURY_HELP]" : "";
	FString assistance_string = "[ID_SUBSTITUTE]";

	help_tip->SetText(help_string);
	UWWUIFunctionLibrary::SetText(assistance_text, UWWUITranslationManager::Translate(assistance_string));
}

void UWWUIScreenInGameInjury::OnPlayerProfileOpened(APlayerController * playerController)
{
	if (selected_player_db_id != 0)
	{
		//Player stats
		//InGamePlayerProfile.player_index = selected_player_index;
		//InGamePlayerProfile.player_db_id = selected_player_db_id;
		//InGamePlayerProfile.team_index = team_index;

		//Stop re - selection of ideal substitute on returning to this screen.
		returning_from_profile = true;

		//ProceedToWindowForward("InGamePlayerProfile");
		return;
	}
}