/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Actions/RUActionTackleBase.h"

#include "Match/AI/Actions/RUActionTackler.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Character/RugbyPlayerController.h"
#include "Character/RugbyCharacter.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameMovement.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUTackleHelper.h"
#include "Match/SIFGameWorld.h"

//#rc3_legacy_include #include "NMMabAnimationNetwork.h"
//#rc3_legacy_include #include "SIFDebug.h"
#include "Utility/Helpers/SIFRumbleHelpers.h"
//#rc3_legacy_include #include <Animation.h>
//#rc3_legacy_include #include <Network.h>
//#rc3_legacy_include #include <NetworkDef.h>
//#rc3_legacy_include #include <StateMachine.h>

#if PLATFORM_WINDOWS || PLATFORM_XBOXONE
#ifdef SUPPRESS_DEPRECATION_WARNINGS
#pragma warning(push)
#pragma warning(disable : 4996)
#endif
#endif

static const char* GETUP_REQUEST = "getup";
static const char* BREAK_OUT_REQUEST = "break_out";
static const char* RELEASE_REQUEST = "release";
static const char* TAKEN_DOWN_REQUEST = "taken_down";
static const char* GROUND_RELEASE_REQUEST = "ground_release";

MABRUNTIMETYPE_IMP1(RUActionTackleBase, RUAction);

//#define ENABLE_TACKLE_LOGGING
#ifdef ENABLE_TACKLE_LOGGING
#define MABLOGDEBUGTACKLE( format, ... ) MABLOGDEBUG( format, __VA_ARGS__ )
#else
#define MABLOGDEBUGTACKLE( format, ... )
#endif

OFFLOAD_META::OFFLOAD_META()
: upright(false)
, falling(false)
, on_ground(false)
, tacklee(NULL)
{
	for(int i=0; i<OA_LAST; ++i)
	{
		can_do[i] = false;
		difficulty[i] = 0.0f;
		offload_angle[i] = 0.0f;
	}
}


RUActionTackleBase::DrivenTackleMotionSource::DrivenTackleMotionSource( const FVector& pposition_offset, float ffacing_angle_offset, IRUOrigin* oorigin )
: position_offset( pposition_offset ),
  facing_angle_offset( ffacing_angle_offset ),
  origin( oorigin ),
  time( 0.0f )
{
	MABASSERT( origin );
}

bool RUActionTackleBase::DrivenTackleMotionSource::GetFrame( MotionFrame& frame ) const
{
	MabMatrix driven_tackle_world = MabMatrix::RotMatrixY( origin->GetFacingAngle() ) * MabMatrix::TransMatrix( origin->GetOrigin() );
	frame.state.current_position = driven_tackle_world.TransformPos( position_offset );
	frame.state.current_facing_angle = origin->GetFacingAngle() + facing_angle_offset;
	frame.time = time;
	return true;
}

void RUActionTackleBase::DrivenTackleMotionSource::Update( float delta_time )
{
	time += delta_time;
}

bool RUActionTackleBase::DrivenTackleMotionSource::ProvidesPositions() const
{
	return true;
}

bool RUActionTackleBase::DrivenTackleMotionSource::ProvidesFacing() const
{
	return true;
}

RUActionTackleBase::RUActionTackleBase( ARugbyCharacter* player )
: RUAction( player ),
  on_ground( false ),
  current_state( TS_PRE_TACKLE ),
  next_state( TS_PRE_TACKLE ),
  tackle_result( NULL ),
  player_animation( NULL ),
  network( NULL ),
  ruck_team_state( NULL )
{
}

void RUActionTackleBase::Initialise( RUTackleResult* result )
{
	tackle_result = result;
	player_animation = m_pPlayer->GetAnimation();
	/*#rc3_legacy
	network = player->GetComponent<NMMabAnimationNetwork>();
	*/
	current_state = TS_PRE_TACKLE;
	next_state = current_state;
	ruck_team_state = NULL;

	m_lock_manager.UFLock( UF_DOANIMGRAPH );
	m_lock_manager.UFLock( UF_SETWAYPOINT );
	m_lock_manager.UFLock( UF_SETLOOK );
	m_lock_manager.UFLock( UF_SETFACING );

	// Inform higher level we're going into PRE_TACKLE
	OnStateTransition(TS_MAX, TS_PRE_TACKLE);
}

void RUActionTackleBase::UpdateState(bool force_state_change)
{
	Transition( next_state, false, force_state_change );
}

/// Can this tackle transition to the new state
bool RUActionTackleBase::CanTransition( TackleState new_state )
{
	return Transition( new_state, true, false );
}

void RUActionTackleBase::RequestTransition( TackleState state )
{
	MABASSERT(state > current_state || state == TS_POST_TACKLE); // Allow multiple calls to POST_TACKLE
	next_state = state;
}

bool RUActionTackleBase::InternalEnter()
{	
	//network->SendRequest( GETUP_REQUEST, false );		
	m_pPlayer->GetAnimation()->GetStateMachine().SendRequest(GETUP_REQUEST, false);
	//Nirupam: Others I have kept commented, as they dont have any mapping animation.
	//network->SendRequest( BREAK_OUT_REQUEST, false );
	//network->SendRequest( RELEASE_REQUEST, false );
	//network->SendRequest( TAKEN_DOWN_REQUEST, false );
	//network->SendRequest( GROUND_RELEASE_REQUEST, false );

	/// Register collidables
	//const static float TACKLE_COLLIDABLE_SIZE = 0.6f;
	//game->GetMovement()->RegisterStaticCollidable( &player->GetMovement()->GetCurrentPosition(), TACKLE_COLLIDABLE_SIZE, player );
	//player->GetMovement()->SetRepulsionEnabled( false );

	return RUAction::InternalEnter();
}

void RUActionTackleBase::InternalExit(bool in_destroy)
{	
    m_pPlayer->GetAnimation()->GetStateMachine().SendRequest(GETUP_REQUEST, true);
	//network->SendRequest( GETUP_REQUEST, true );
	//network->SendRequest( BREAK_OUT_REQUEST, true );
	//network->SendRequest( RELEASE_REQUEST, true );
	//network->SendRequest( TAKEN_DOWN_REQUEST, true );
	//network->SendRequest( GROUND_RELEASE_REQUEST, true );

	/// Unregsiter collidables
	//game->GetMovement()->UnregisterStaticCollidable( player );

	RUAction::InternalExit(in_destroy);
}

bool RUActionTackleBase::Transition(TackleState state, bool test_only, bool force_state_change)
{
	if ( current_state != state )
	{
		// Check entry condition
		char enter_meta_state[128] = { 0 };
		const TackleStateData* enter_meta = GetTackleStateMeta(state);
		if ( !enter_meta )
		{
			MABBREAKMSG( "Unable to to find tackle metadata for" );
			return false;
		}

		if ( enter_meta->animation_state && !force_state_change )
		{			
			strcpy( enter_meta_state, enter_meta->animation_state );
			ReplaceTokens( enter_meta_state, sizeof(enter_meta_state) );
			//if ( !network->IsPartOfCurrentState(enter_meta_state) )
			if ( !m_pPlayer->GetAnimation()->GetStateMachine().IsPartOfCurrentState(enter_meta_state) )
			{
				return false;
			}
		}

		char enter_meta_request[128] = { 0 };
		if ( enter_meta->enter_request )
		{
			if ( next_state == TS_PRE_TACKLE || next_state == TS_START_TACKLE )
			{
				//snprintf( enter_meta_request, sizeof(enter_meta_request), "%s|%s|tackle", RUPlayerAnimation::TACKLES_NODE_PATH, enter_meta->enter_request );
				snprintf(enter_meta_request, sizeof(enter_meta_request), "%s", enter_meta->enter_request);
				ReplaceTokens( enter_meta_request, sizeof(enter_meta_request) );
			}
			else
			{				
				snprintf( enter_meta_request, sizeof(enter_meta_request), "%s", enter_meta->enter_request );
				ReplaceTokens( enter_meta_request, sizeof(enter_meta_request) );
				//if ( !network->IsRequestActive(enter_meta_request) )
				if (!m_pPlayer->GetAnimation()->GetStateMachine().IsTackleRequestActive(enter_meta_request))
				{
					return false;
				}
			}
		}

		if (!test_only && LogicAllowsTransition(current_state, next_state))
		{
			if ( strlen(enter_meta_request) > 0 )
			{
				MABLOGDEBUGTACKLE( "RUActionTackleBase::UpdateState(), player=%d, enter_request='%s'", m_pPlayer->GetAttributes()->GetIndex(), enter_meta_request );
				if ( next_state == TS_START_TACKLE )
				{
#if wwDEBUG_ARB
					ERugbyAnim_Mode_Tackles tackleMode = player->GetAnimation()->GetStateMachine().GetSMTackle()->GetCurrentTackleMode();
					if (tackleMode != ERugbyAnim_Mode_Tackles::null)
					{
						wwDO_NOTHING;
					}
#endif
					//const NodeDef* state_machine_def = network->GetNetworkDef()->findNodeDef( RUPlayerAnimation::TACKLES_NODE_PATH );
					//StateMachine* state_machine = static_cast<StateMachine*>( network->GetNetwork()->findNodeByNodeDef(state_machine_def) );
					//const NodeDef* state = network->GetNetworkDef()->findNodeDef( enter_meta_request );
					//if ( state_machine && state )
					//{
					//	DeltaTrajectorySource dest_traj_src = DELTA_TRAJECTORY_DESTINATION;
					//	if ( player == tackle_result->tacklee )
					//		dest_traj_src = DELTA_TRAJECTORY_BLEND;
					//	if ( tackle_result->tackle_result == TRT_DIVE_MISS && player == tackle_result->tacklers[0] )
					//		dest_traj_src = DELTA_TRAJECTORY_BLEND;
					//
					//	network->ForceTransitionToState( state_machine, state, 0.25f, dest_traj_src );
					//}

					DeltaTrajectorySource dest_traj_src = DeltaTrajectorySource::DELTA_TRAJECTORY_DESTINATION;
					if (m_pPlayer == tackle_result->tacklee)
					{
						dest_traj_src = DeltaTrajectorySource::DELTA_TRAJECTORY_BLEND;
					}
					if (tackle_result->tackle_result == TRT_DIVE_MISS && m_pPlayer == tackle_result->tacklers[0])
					{
						dest_traj_src = DeltaTrajectorySource::DELTA_TRAJECTORY_BLEND;
					}
					
					m_pPlayer->GetAnimation()->GetStateMachine().ForceTransitionToState(RUPlayerAnimation::TACKLES_NODE_PATH, enter_meta_request);
									   					
				}
				else
				{					
					m_pPlayer->GetAnimation()->GetStateMachine().SendRequest(enter_meta_request, true);
				}
			}

			RUGameEvents* game_events = m_pPlayer->GetGameWorld()->GetEvents();
			game_events->tackle_state_changed( *tackle_result, current_state, next_state, m_pPlayer );
			OnStateTransition( current_state, next_state );
			current_state = state;
		}
	}

	return true;
}

const TackleStateData* RUActionTackleBase::GetTackleStateMeta( TackleState state ) const
{
	static const TackleStateData CONTESTED_TACKLE[] =
	{
		{ TS_PRE_TACKLE, NULL, NULL },
		{ TS_START_TACKLE, NULL, "contested[PLAYER_SUFFIX]" },
		{ TS_TRANSITION_TO_BREAKOUT, NULL, "driven_breakout" },
		{ TS_TRANSITION_TO_GROUND, NULL, "driven_takedown" },
		{ TS_IN_TACKLE_ON_GROUND, "ground", NULL },
		{ TS_IN_TACKLE_STANDING, NULL, NULL },
		{ TS_END_TACKLE, NULL, NULL },
		{ TS_RELEASE_STANDING, NULL, NULL },
		{ TS_RELEASE_GROUND, NULL, "getup" },
		{ TS_POST_TACKLE, "contested[PLAYER_SUFFIX]_null", NULL },
		{ TS_MAX, NULL, NULL }
	};

	static const TackleStateData STANDARD_TACKLE[] =
	{
		{ TS_PRE_TACKLE, NULL, NULL },
		{ TS_START_TACKLE, NULL, "[TACKLE_TYPE][PLAYER_SUFFIX]" },
		{ TS_IN_TACKLE_ON_GROUND, "ground", NULL },
		{ TS_TRANSITION_TO_INJURED, NULL, NULL },
		{ TS_INJURED, NULL, NULL },
		{ TS_RELEASE_GROUND, NULL, NULL },
		{ TS_END_TACKLE, NULL, "getup" },
		{ TS_POST_TACKLE, "getup_null", NULL },
		{ TS_MAX, NULL, NULL }
	};

	static const TackleStateData IMPACT_ONLY_TACKLE[] =
	{
		{ TS_PRE_TACKLE, NULL, NULL },
		{ TS_START_TACKLE, NULL, "[TACKLE_TYPE][PLAYER_SUFFIX]" },
		{ TS_TRANSITION_TO_INJURED, NULL, NULL },
		{ TS_INJURED, NULL, NULL },
		{ TS_END_TACKLE, NULL, NULL },
		{ TS_POST_TACKLE, "[TACKLE_TYPE][PLAYER_SUFFIX]_null", NULL },
		{ TS_MAX, NULL, NULL }
	};

	static const TackleStateData* TACKLE_STATE_DATAS_BY_TACKLE_STATE_MACHINE [TSM_UNKNOWN] =
	{
		STANDARD_TACKLE,
		IMPACT_ONLY_TACKLE,
		CONTESTED_TACKLE
	};

	MABASSERTMSG(tackle_result->state_machine != TSM_UNKNOWN, MabString(0,"No state machine set for tacke result:%d",tackle_result->tackle_result).c_str());

	const TackleStateData* tackle_state_data = TACKLE_STATE_DATAS_BY_TACKLE_STATE_MACHINE[tackle_result->state_machine];
	while ( tackle_state_data && tackle_state_data->state != state && tackle_state_data->state != TS_MAX )
	{
		++tackle_state_data;
	}
	MABASSERT( tackle_state_data && tackle_state_data->state != TS_MAX );
	return tackle_state_data && tackle_state_data->state != TS_MAX ? tackle_state_data : NULL;
}

const char* RUActionTackleBase::GetTackleName(ARugbyCharacter* player)
{
	MABASSERT(player);
	/*#rc3_legacy
	MABASSERT( player->GetComponent<NMMabAnimationNetwork>() );
	Network* network = player->GetComponent<NMMabAnimationNetwork>()->GetNetwork();
	*/
	const char* tackle_node_name = NULL;
	/*#rc3_legacy
	Vector<Animation*>::const_iterator i = network->getAnimations().begin();
	while ( i != network->getAnimations().end() && !tackle_node_name )
	{
		const Animation* animation = *i;
		MABASSERT( animation );
		const char* node_name = animation->name();
		for ( int j = (int)(strlen(node_name) - 1); j >= 0; j-- )
		{
			if ( node_name[j] == '|' && node_name[j + 1] == 't' && (node_name[j + 2] == 'e' || node_name[j + 2] == 'r') )
			{
				tackle_node_name = node_name;
			}
		}
		++i;
	}
	*/
	return tackle_node_name;
}

TackleState RUActionTackleBase::GetNextState( TackleState tackle_state )
{
	TackleState ret_next_state = tackle_state;
	switch ( tackle_state )
	{
		case TS_PRE_TACKLE:
			ret_next_state = TS_START_TACKLE;
			break;

		case TS_START_TACKLE:
			if ( tackle_result->taken_down )
			{
				on_ground = true;
			}

			if ( tackle_result->tackle_result == TRT_CONTESTED )
			{
				ret_next_state = TS_IN_TACKLE_STANDING;
			}
			else if ( tackle_result->anim_sequences == TAS_IMPACT )
			{
				ret_next_state = TS_END_TACKLE;
			}
			else if ( tackle_result->anim_sequences & TAS_HELD_LOOP )
			{
				ret_next_state = TS_IN_TACKLE_STANDING;
			}
			else
			{
				ret_next_state = TS_IN_TACKLE_ON_GROUND;
			}
			break;

		case TS_IN_TACKLE_STANDING:
			ret_next_state = TS_RELEASE_STANDING;
			break;

		case TS_IN_TACKLE_ON_GROUND:
			ret_next_state = TS_RELEASE_GROUND;
			break;

		case TS_TRANSITION_TO_INJURED:
			ret_next_state = TS_INJURED;
			break;

		case TS_INJURED:
			ret_next_state = TS_INJURED;
			break;

		case TS_TRANSITION_TO_GROUND:
			on_ground = true;
			ret_next_state = TS_IN_TACKLE_ON_GROUND;
			break;

		case TS_TRANSITION_TO_BREAKOUT:
			ret_next_state = TS_END_TACKLE;
			break;

		case TS_TRANSITION_TO_STRIP_FAIL:
			ret_next_state = TS_IN_TACKLE_STANDING;
			break;

		case TS_TRANSITION_TO_STRIP_SUCCESS:
			ret_next_state = TS_END_TACKLE;
			break;

		case TS_TRANSITION_TO_STRIP_PARTIAL:
			ret_next_state = TS_END_TACKLE;
			break;

		case TS_RELEASE_GROUND:
		{
			//GGS SRA: removing the injury cutscene trigger. Instead, gameplay will continue and the player's stamina is reduced based on the severity of the injury, in RUTackleHelper::SetInjuryStatus
			/*bool injury = tackle_result->IsPlayerInjured(m_pPlayer) && tackle_result->tacklee == m_pPlayer;

			MABASSERTMSG( !injury || m_pGame->GetGameState()->GetBallHolder() == m_pPlayer, "Non Ball Holder can't be injured without the ball!" );

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
			if (injury && m_pGame->GetGameState()->GetBallHolder() == m_pPlayer && m_pGame->GetGameState()->GetPhase() == RUGamePhase::LINEOUT)
			{
				UE_LOG(LogTemp, Display, TEXT("Injury during just before lineout, ignoring the injury")); //#RC4-3688: An additional Lineout check added below to ensure we dont start injury while LineOut already started.
			}
#endif
			if ( injury && m_pGame->GetGameState()->GetBallHolder() == m_pPlayer && m_pGame->GetGameState()->GetPhase()!=RUGamePhase::LINEOUT)
			{
				ret_next_state = TS_INJURED;
			}
			else
			{
				ret_next_state = TS_END_TACKLE;
			}*/

			ret_next_state = TS_END_TACKLE;
			break;
		}

		case TS_RELEASE_STANDING:
			ret_next_state = TS_END_TACKLE;
			break;

		case TS_END_TACKLE:
			ret_next_state = TS_POST_TACKLE;
			break;

		case TS_POST_TACKLE:
			break;

		case TS_MAX:
			break;
	}

	MABASSERT(ret_next_state >= current_state );
	return ret_next_state;
}

void RUActionTackleBase::AssignNextState()
{
	next_state = GetNextState( current_state );
}

MabString RUActionTackleBase::GetStateName( TackleState tackle_state )
{
	switch ( tackle_state )
	{
	case TS_PRE_TACKLE:
	  return "TS_PRE_TACKLE";
	break;

	case TS_START_TACKLE:
	  return "TS_START_TACKLE";
	break;

	case TS_IN_TACKLE_STANDING:
	  return "TS_IN_TACKLE_STANDING";
	break;

	case TS_IN_TACKLE_ON_GROUND:
	  return "TS_IN_TACKLE_ON_GROUND";
	break;

	case TS_TRANSITION_TO_GROUND:
	  return "TS_TRANSITION_TO_GROUND";
	break;

	case TS_TRANSITION_TO_BREAKOUT:
	  return "TS_TRANSITION_TO_BREAKOUT";
	break;

	case TS_TRANSITION_TO_STRIP_SUCCESS:
	  return "TS_TRANSITION_TO_STRIP_SUCCESS";
	break;

	case TS_TRANSITION_TO_STRIP_FAIL:
	  return "TS_TRANSITION_TO_STRIP_FAIL";
	break;

	case TS_TRANSITION_TO_STRIP_PARTIAL:
	  return "TS_TRANSITION_TO_STRIP_PARTIAL";
	break;

	case TS_TRANSITION_TO_INJURED:
	  return "TS_TRANSITION_TO_INJURED";
	break;

	case TS_INJURED:
	  return "TS_INJURED";
	  break;

	case TS_RELEASE_STANDING:
	  return "TS_RELEASE_STANDING";
	break;

	case TS_RELEASE_GROUND:
		return "TS_RELEASE_GROUND";
	break;

	case TS_END_TACKLE:
	  return "TS_END_TACKLE";
	break;

	case TS_POST_TACKLE:
	  return "TS_POST_TACKLE";
	break;

	case TS_MAX:
	  return "TS_MAX";
	break;
  }
  return "UNKNOWN STATE";
}

void RUActionTackleBase::ReplaceTokens(char* buffer, size_t buffer_size)
{
	int index = 0;
	int length = (int)strlen(buffer);
	int token_start = -1;

	while (index < length)
	{
		if (buffer[index] == '[')
		{
			MABASSERT(token_start < 0);
			token_start = index;
		}
		else if (buffer[index] == ']')
		{
			MabString replace = "";
			MabString token = &buffer[token_start + 1];
			token.erase(index - token_start - 1);
			if (token == "TACKLE_TYPE")
			{
				switch ( tackle_result->tackle_result )
				{
					case TRT_STANDARD:
						if (tackle_result->n_tacklers == 1)
							replace = tackle_result->successful ? "standard_success" : "standard_fail";
						else if (tackle_result->n_tacklers == 2) 
						{
							replace = tackle_result->successful ? "standard_success_two" : "standard_fail_two";	
						}
						break;

					case TRT_CONTESTED:
						replace = "driven";
						break;

					case TRT_SIDESTEP:
						replace = tackle_result->successful ? "sidestep_fail" : "sidestep_success";
						break;

					case TRT_FEND2:
						replace = tackle_result->successful ? "fend_fail" : "fend_success";
						break;

					case TRT_TRY:
						replace = "try";
						break;

					case TRT_ANKLE_TAP2:
						replace = "ankle_tap";
						break;

					case TRT_GROUND_GETUP:
						MABBREAK();
						replace = "standard_success";
						break;

					case TRT_HEAD_HIGH2:
						replace = "head_high";
						break;

					case TRT_MULTI_MAN2:
						MABBREAK();
						replace = "standard_success";
						break;

					case TRT_DIVE_MISS:
						replace = "dive_miss";
						break;

					case TRT_TRY_PUSHED:
						replace = "try_pushed";
						break;

					case TRT_TRY_CORNER:
						replace = "try_corner";
						break;

					default:
						replace = "standard_success";
						break;
				}
			}
			else if (token == "PLAYER_SUFFIX")
			{
				if ( !tackle_result->isTackleResultTryType() )
				{
					replace = m_pPlayer == tackle_result->tacklee ? "_tacklee" : "_tackler";
				}
			}

			MABASSERT(length + replace.length() < buffer_size);
			if (length + replace.length() < buffer_size)
			{
				MabString remainder = &buffer[index + 1];
				strcpy(&buffer[token_start], replace.c_str());
				index = (int)(strlen(buffer) - 1);
				strcat(buffer, remainder.c_str());
				token_start = -1;
				length = (int)strlen(buffer);
			}
		}
		index++;
	}
}

void RUActionTackleBase::TriggerImpactRumble( const RUTackleResult& Intackle_result )
{
	ARugbyPlayerController* human_player = Cast<ARugbyPlayerController>(m_pPlayer->GetController());
	if ( human_player )
	{
		float total_tackle_impetus = Intackle_result.actual_tacklers_impetus + Intackle_result.actual_tacklee_impetus;

		const float RUMBLE_GRADES[] = { 0.3f, 0.6f, 1.1f, 10000.0f };
		const char* RUMBLE_PATTERNS[] = { "tackle_light", "tackle_medium", "tackle_heavy", "tackle_super_heavy" };
		int rumble_type = 0;
		while (rumble_type < 3 && RUMBLE_GRADES[rumble_type] < total_tackle_impetus) rumble_type++;

		SIFRumbleHelpers::TriggerRumblePattern( human_player->GetControllerIndex(), RUMBLE_PATTERNS[rumble_type] );
	}
}

#if PLATFORM_WINDOWS || PLATFORM_XBOXONE
#ifdef SUPPRESS_DEPRECATION_WARNINGS
#pragma warning(pop)
#endif
#endif
