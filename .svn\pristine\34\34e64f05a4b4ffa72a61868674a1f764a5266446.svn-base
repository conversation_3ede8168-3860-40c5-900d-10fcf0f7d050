/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef RL3_COMPETITION_TEAM_HELPER_H
#define RL3_COMPETITION_TEAM_HELPER_H

class RL3Database;
class RL3CompetitionPlayerHelper;
class RL3TournamentConstants;
class RUCareerModeManager;

#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DatabaseTypes.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DBTeam.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DBPlayer.h"

#include "RULimits.h"

enum PLAYER_HIRING_AVAILABILITY
{
	PHA_AVAILABLE,
	PHA_ALREADY_TAKEN,
	PHA_BEFORE_ANTI_TAMPERING,
	PHA_CONTRACT_TOO_LONG,
	PHA_UNDER_OFFER
};

enum RL3_POSITION_CATEGORY
{
	PC_FORWARDS = 0,
	PC_BACKS = 1,
	PC_WINGS = 2,
	PC_MAX = 3
};




struct WEIGHTED_CATEGORY
{
	RL_TEAM_FITNESS_CATEGORIES category;
	float weight;

	bool operator<( const WEIGHTED_CATEGORY& other ) const;
	bool operator>( const WEIGHTED_CATEGORY& other ) const;
};

struct WEIGHTED_PLAYER
{
	unsigned short index;
	float weight;

	bool operator<( const WEIGHTED_PLAYER& other ) const;
	bool operator>( const WEIGHTED_PLAYER& other ) const;
};



struct AVAILABLE_PLAYER
{
	unsigned short index;
	bool available;
};

typedef MabVector< WEIGHTED_CATEGORY > CategoryList;
typedef MabVector< WEIGHTED_PLAYER > WeightedPlayerList;
typedef MabVector< AVAILABLE_PLAYER > AvailablePlayerList;
typedef MabVector< int > BreachList;
typedef MabVector< int > SalaryList;




struct RANKED_PLAYER
{
	RANKED_PLAYER();
	RANKED_PLAYER( unsigned short id, float weight );
	RANKED_PLAYER( const RANKED_PLAYER& other );

	unsigned short id;
	float	weight;

	bool operator>( const RANKED_PLAYER& other ) const;
	bool operator<( const RANKED_PLAYER& other ) const;
	bool operator==( const RANKED_PLAYER& other ) const;
	void operator=( const RANKED_PLAYER& other );
};

typedef MabVector< RANKED_PLAYER > CompetitionPlayerList;

struct RANKED_PLAYER2
{
	RANKED_PLAYER2();
	RANKED_PLAYER2( unsigned short id, float weight, int market_value );
	RANKED_PLAYER2( const RANKED_PLAYER2& other );

	unsigned short	index;
	float			weight;
	int				market_value;
	bool			available;

	bool operator>( const RANKED_PLAYER2& other ) const;
	bool operator<( const RANKED_PLAYER2& other ) const;
	bool operator==( const RANKED_PLAYER2& other ) const;
	void operator=( const RANKED_PLAYER2& other );
};

typedef MabVector< RANKED_PLAYER2 > CompetitionPlayerList2;




///----------------------------------------------------------------------------------
/// Class: RL3CompetitionTeamHelper
///----------------------------------------------------------------------------------

class RL3CompetitionTeamHelper
{
public:
	RL3CompetitionTeamHelper(RL3Database *ddatabase, RL3CompetitionPlayerHelper* pplayer_helper, RL3TournamentConstants *constants, RUCareerModeManager *career);
	~RL3CompetitionTeamHelper();

private:
	RL3Database*				database;
	RL3CompetitionPlayerHelper* competition_player_helper;
	RL3TournamentConstants*		tournament_constants;
	RUCareerModeManager*		career_manager;



public:

	/// \brief Counts the number of players in the team that haven't been struck down by illness or out with a judicial charge.
	///
	/// @param team The team we want the number of available players from
	/// @return The number of players who are available to play as of now
	int GetNumAvailablePlayers( RL3DB_TEAM team );

	// Hires players for the team, based on weaknesses found in the team
	// and the corresponding amount of salary we have to spare for each.
	void HirePlayers( RL3DB_TEAM team, const CategoryList& weaknesses, SalaryList& offers );

	// Hires Non-Contracted players for the team
	void HireNonContractedPlayers( RL3DB_TEAM team);

	// The team will endeavour to hire a player to fill the space defined
	// by the category variable.
	// Returns true if an offer was made to a player that had just refused
	// a deal by the same team, false if an offer is made to a new player.
	// @param maximum_offer [in] If this is set to -1, then we aren't
	// asking the player, we're just taking him. Otherwise it is the
	// highest offer we can make to a player.
	bool HirePlayerInCategory( MabVector<unsigned short>& available_player_indexes, RL3DB_TEAM team, WEIGHTED_CATEGORY category, int maximum_offer = -1 );

	// \brief New system, guaranteed to create a contract negotiation for a player for the passed in team and category.
	// - Note this function should only be called on the first day of trading, as the contract_negotiation created will be made 'VOID' if it fails
	// @param ranked_players unsorted list of available players (with weight already calculated based on category)
	// @param team the team doing the hiring
	// @param category the category we're hiring for.
	// @param maximum_offer, our maximum 'offer'.
	void HirePlayerInCategoryNew(CompetitionPlayerList2 &ranked_players, RL3DB_TEAM team, WEIGHTED_CATEGORY category, int maximum_offer);

	// \brief New system, the passed in negotiation IS VOID, find a new player to negotiatiate with. (doesn't save the negotiation)
	// @param ranked_players unsorted list of available players (with weight already calculated based on category)
	// @param out: contract_negotiation the VOID negotiation to be ?re-playerised?
	// @param team the team negotiating.
	void FindNewPlayerForNegotiation(CompetitionPlayerList2 &ranked_players, RL3DB_CONTRACT_NEGOTIATION &contract_negotiation, int negotiation_index, RL3DB_TEAM team);

	/// Works out how much of a boost a team will get if a player signs up
	/// with them and plays in the specified position.
	/// @param team [in] The team that will be receiving the player.
	/// @param player [in] The player that the team is looking at hiring.
	/// @param position [in] The position which the player is being
	/// purchased, and therefore the position that he will be assessed in.
	float GetUsefulnessOfPlayer( RL3DB_TEAM team, RL3DB_PLAYER player, RL_TEAM_FITNESS_CATEGORIES position );

	/// Ranks the categories of the team based on how much the team is in
	/// need of players to fill the roles.
	/// @param weaknesses [out] A list of the categories, ranked in how
	/// deficient the team is in each one.
	/// @param team [in] The team to assess.
	void RankTeamWeaknesses( CategoryList& weaknesses, RL3DB_TEAM team );

	/// \brief Works out whether or not a team's current lineup is able to be
	/// fielded. This takes into account injuries, suspensions, and
	/// secondments to rep or international teams.
	///
	/// @param team The team we want to check the lineup of
	/// @param franchise_mode True if we are in franchise mode
	/// @return True if the first 17 players in the lineup are available to play
	bool IsLineupValid( RL3DB_TEAM team, bool franchise_mode );

	/// \brief works out whether or not a team could possibly field a full team
	/// given its current situation.
	/// @param the The team we want to check
	/// @franchise_mode True if we are in franchise mode
	/// @return false if there is noway this team can field a team of 17 players
	bool CanFieldTeam( RL3DB_TEAM team, bool franchise_mode );

	/// \brief Finds any issues with a teams lineup returns an bitfield that
	/// represents the lineup state
	/// @param team The team we want to check the lineup of
	/// @return no 1 bits if everything is fine
	///         first bit 1 if players are in positions that aren't ideal
	///         second bit 1 if there is no captain in the top 17
	///         third bit 1 if a playkicker is not in the top 17
	///         forth bit 1 if a goalkicker is not in the top 17
	///         fifth bit 1 if a player is injuried
	///         sixth bit 1 if a player is suspended
	///         eigth bit 1 if a player is rep selected
	int FindLineupIssues( RL3DB_TEAM team );

	/// Finds any issues with a teams lineup and attempts to fix them;
	/// @param team The team who lineup we wish to fix
	void FixLineupIssues( RL3DB_TEAM team );

	/// Calculates the team's preferences for the different fitness
	/// categories based on their higher level preferences.
	/// @param preferences [out] A pointer to the first element of an array
	/// of floats of TFC_MAX size. This will be filled with the preferences.
	/// @param team [in] A pointer to the RL3DB_TEAM struct for the team to
	/// generate the preferences for.
	void CalculatePreferences( float* preferences, RL3DB_TEAM team );

	/// Calculates a fitness for a team lineup for a specific player
	/// fitness category.
	/// @param team [in] A pointer to an RL3DB_TEAM, for which the player
	/// lineup is being evaluated for.
	/// @param players [in] A vector of player ids that the lineup will be.
	/// @param category [in] The fitness category to assess the team on.
	/// @return How strong the lineup is in the specified category. Ranges
	/// from 0.0f (weak) to 1.0f (strong).
	float GetLineupFitness( RL3DB_TEAM team, const MabVector< unsigned short >& players, RL_TEAM_FITNESS_CATEGORIES category );

	/// Gets the strengths of every player in the team in a certain fitness
	/// category, scaled by team preferences and sorted.
	/// @param strengths [out] A WeightedPlayerList structure that is
	/// filled with the results of the strength analysis.
	/// @param team [in] A pointer to an RL3DB_TEAM struct representing the
	/// team to analyse.
	/// @param category [in] The category to assess the players by.
	void GetPlayerStrengths( WeightedPlayerList& strengths, RL3DB_TEAM team, RL_TEAM_FITNESS_CATEGORIES category, int num_weeks_to_ignore );

	/// Attempts to hire players for the club.
	/// @param team [in] A pointer to the RL3DB_TEAM structure defining the
	/// team to assess.
	void FindPotentialPurchases(RL3DB_TEAM team);

	/// Calculates how many players will currently be in next years squad.
	int GetNumberOfSignedPlayersForNextYear(RL3DB_TEAM team);

	/// Works out the salary cap for a given team.
	/// @param team [in] A pointer to a RL3DB_TEAM struct representing the
	/// team to evaluate.
	/// @return The value left under the salary cap. A negative number
	/// indicates a breach of that amount.
	int CheckSalaryCap(RL3DB_TEAM team, bool current_year);

	/// Works out the salary cap for a given team.
	/// @param team [in] A pointer to a RL3DB_TEAM struct representing the
	/// team to evaluate.
	/// @return The amount of minimum amount of money a team has still to spend.
	int CheckSalaryFloor(RL3DB_TEAM team, bool current_year);

	/// Gets the strengths of a list of players in a certain fitness
	/// category, scaled by team preferences and sorted.
	/// @param strengths [out] A WeightedPlayerList structure that is
	/// filled with the results of the strength analysis.
	/// @param team [in] A pointer to an RL3DB_TEAM struct representing the
	/// team to analyse.
	/// @param players [in] A vector of player ids to rank.
	/// @param category [in] The category to assess the players by.
	void GetPlayerStrengths( WeightedPlayerList& strengths, RL3DB_TEAM team, RL_TEAM_FITNESS_CATEGORIES category, const AvailablePlayerList& players );

	/// This function generates the preferred lineup for the team, based on
	/// the team's preferences and strengths of the players.
	/// @param lineup [out] A vector to be resized and filled with the
	/// indices of the chosen players.
	/// @param team [in] A pointer to the team to select the players for.
	/// @param generate_bench_players [in] True if you want the four extra
	/// reserves to be generated, false if you just want the starting 13.
	/// @param num_weeks_to_ignore [in] This affects the choice of players.
	/// The function will not choose any players who are unavailable due to
	/// injury or suspension, unless they will become available once the
	/// number of weeks specified has passed. A value of -1 means that all
	/// players will be considered, even those who are unavailable.
	/// @return False if there aren't enough players available to make the
	/// lineup, true otherwise.
	bool GenerateLineup( MabVector< unsigned short >& lineup, RL3DB_TEAM team, bool generate_bench_players = true, int num_weeks_to_ignore = 0 );

	/// This is identical to the other GenerateLineup() function, except it
	/// puts the results directly into the team's players array instead of
	/// into a vector.
	/// @param team [in] A pointer to the team to select the players for.
	/// @param generate_bench_players [in] True if you want the four extra
	/// reserves to be generated, false if you just want the starting 13.
	/// @param num_weeks_to_ignore [in] This affects the choice of players.
	/// The function will not choose any players who are unavailable due to
	/// injury or suspension, unless they will become available once the
	/// number of weeks specified has passed. A value of -1 means that all
	/// players will be considered, even those who are unavailable.
	/// @return False if there aren't enough players available to make the
	/// lineup, true otherwise.
	bool GenerateLineup( RL3DB_TEAM team, bool generate_bench_players = true, int num_weeks_to_ignore = 0 );

	/// Order lineup by position. RU database teams not stored in position order.
	/// Called at start of franchise/comp mode.
	/// @param team [in] A pointer to the team to select the players for.
	/// The function move the first 15 to the start of the lineup (in position order)
	/// If a position is unfilled, GenerateLineup will be called.
	/// Will assign captain and kickers if not already assigned.
	void InitialiseLineup(RL3DB_TEAM team);

	void MatchTeamLineupToDisplayOrder(RL3DB_TEAM team);

	/// Similar to GenerateLinup, expect it drops all other players not in the starting lineup from the team
	/// This is to be used with Rep sides only, do not use with club teams or the franchise teams are going to get screwed
	bool GenerateLineupDropAdditionalPlayers( RL3DB_TEAM team, bool generate_bench_players = true, int num_weeks_to_ignore = 0 );

	/// Keeps the first n number of players and removed the rest from the team
	void RemoveAdditionalPlayers( RL3DB_TEAM team, int max_num_players );

	/// Checks if any of the captains or kickers are not assigned, and
	/// assigns them if not.
	void AssignCaptainAndKickers( RL3DB_TEAM team, bool keep_any_valid_kickers = false, bool allow_roles_as_reserves = false );

	/// Generates a list of the n best players from the team's rep area and
	/// puts the list into the team's players array. This is best used for
	/// international teams. For rep teams, use the
	/// GeneratePlayerListsForRepCompetition instead for better results.
	/// @param team [in / out] A pointer to the RL3DB_TEAM struct for the
	/// team to have a player list generated for it. This team must be
	/// either an international or representative team.
	/// @param franchise_competition_instance [in] The competition that
	/// the player list is being generated for. NULL means that it is for a
	/// one-off game.
	/// @param num_players [in] The number of players to select for the
	/// squad. This defaults to 17. It must be at least 17, and can't be
	/// higher than 40.
	/// @param num_weeks_to_ignore [in] This affects the choice of players.
	/// The function will not choose any players who are unavailable due to
	/// injury or suspension, unless they will become available once the
	/// number of weeks specified has passed.
	/// @return True if enough players were found, false otherwise.
	bool GeneratePlayerListFromRepArea( RL3DB_TEAM team, RL3DB_COMPETITION_INSTANCE franchise_competition_instance, int weeks_to_ignore = 0, int num_players = NUM_PLAYERS_PER_TEAM_INIT + NUM_BENCH_PLAYERS_INIT );

	/// Can club afford to have this player requisitioned?
	bool CanAffordToLosePlayerToInternational(RL3DB_TEAM club_team, RL3DB_PLAYER the_player, RL3DB_COMPETITION_INSTANCE international_competition);

	void GeneratePlayerListForRepTeam( RL3DB_TEAM team, RL3DB_COMPETITION_INSTANCE international_competition, MabVector<unsigned short> &player_db_ids, bool exclude_current );

	/// This function is used specifically for rep competitions. It
	/// attempts to evenly distribute the load of player secondments in an
	/// effort to limit the possibility ot forfeits.
	bool GeneratePlayerListsForRepCompetition( RL3DB_COMPETITION_INSTANCE franchise_competition_instance, int weeks_to_ignore );

	/// The club will make an offer to a player for the specified value and
	/// duration.
	/// @param team [in] A pointer to the RL3DB_TEAM struct belonging to the
	/// team doing the signing.
	/// @param player [in] A pointer to the RL3DB_PLAYER struct to whom the
	/// offer is being made.
	/// @param value [in] The amount of money the offer is for.
	/// @param length [in] The length of the contract offer.
	/// @param force_resolution [in] If true, this will force the player to
	/// make up his mind immediately. If false (or not supplied), the
	/// player will wait before making up his mind.
	/// @return A RL_PLAYER_OFFER_RESULT type. Will usually be POR_PENDING
	/// or POR_TOO_MANY_OFFERS,  but may be POR_REJECTED or POR_ACCEPTED if
	/// force_resolution is set to true.
	RL3_PLAYER_OFFER_RESULT MakeOffer( RL3DB_TEAM team, RL3DB_PLAYER player, int value, int length, bool force_resolution = false );

	/// Works out whether or not an offer will breach the team's salary cap.
	/// @param team [in] A pointer to the RL3DB_TEAM struct belonging to the
	/// team making the offer.
	/// @param player [in] A pointer to the RL3DB_PLAYER struct to whom the
	/// offer is being made.
	/// @param value [in] The amount of money the offer is for.
	/// @param this_season [in] Whether or not the player is to be hired
	/// this season or the next season. Defaults to false. (next season)
	/// @return True if the offer will fall inside the salary cap, false
	/// otherwise.
	bool IsOfferAllowed( RL3DB_TEAM team, bool this_season = false );

	/// Works out whether or not an offer will breach the team's max team size
	bool IsThereRoomInTeamForThisPlayer( RL3DB_TEAM team, RL3DB_PLAYER player, bool this_season = false );

	/// Works out the total salary amount that the team is paying out in a
	/// given year to its players.
	/// @param team [in] A pointer to a RL3DB_TEAM struct representing the
	/// team to evaluate.
	/// @param year [in] The number of years to look ahead when calculating
	/// the salary. Can be between 0 and 2.
	/// @return The value in dollars that the team's aalary bill is.
	unsigned int CalculateSalary( RL3DB_TEAM team, int year = 0 );

	/// This function forces a team to instantly hire players from the unassigned list so that their team has used up
	/// the salary floor
	/// @param team the team to hire players for
	void HireEnoughUnassignedPlayersToUseSalaryFloor( RL3DB_TEAM team );

	/// This function generates a list of ranked players used to pass into HireUnassignedPlayer. (Optimization step).
	void GenerateRankedPlayers(FRANCHISE_REGION franchise_region, MabVector<RANKED_PLAYER> &ranked_players, bool ignore_region=false);

	/// This function finds and hires an unassigned player for a particular team
	/// @param team the team to hire an unassigned player for
	bool HireUnassignedPlayer( RL3DB_TEAM team, bool dont_hire_ncps/*= false*/,  MabVector<RANKED_PLAYER> &ranked_players);

	// Swap players in team's lineup
	void SwapPlayers( unsigned short team_id, int player1_index, int player2_index );

	void CalculateMaximumOffers(SalaryList& offers, RL3DB_TEAM team, const CategoryList& categories, int salary_cap_leeway);

	/// CHEAT! Reduces the total value of player contracts by 'we_need_this_much_more_cash'
	void MassageCurrentContractValuesDownBy(RL3DB_TEAM team, int we_need_this_much_more_cash);

	// Calculate team ranking
	int RecalculateTeamRanking( unsigned short team_id );

	// Calculate team ranking
	int GetTeamAgility(unsigned short team_id);

	// Calculate team ranking
	int GetTeamSpeed(unsigned short team_id);

	// Calculate team ranking
	int GetTeamTackleAbility(unsigned short team_id);

	// Calculate team ranking
	int GetTeamPassAccuracy(unsigned short team_id);

	// Get scrum weight of the team
	int GetScrumWeight( unsigned short team_db_id );

	// Hire a temporary 'free' player to replace an international player on rep duty. (No fee, preferably has same position category)
	bool HireTemporaryPlayer( RL3DB_TEAM team, MabVector<RANKED_PLAYER> &ranked_players, PLAYER_POSITION preferred_position, RL3DB_PLAYER player_being_replaced );

	// Returns a priority value for maintaining the team lineup during career initialisation
	int GetTeamLineupPriority( RL3DB_TEAM team, unsigned short primary_comp = DB_INVALID_ID);

private:

	int GetBestPlayerIndex(CompetitionPlayerList2 &ranked_players, bool first_iteration, RL3DB_TEAM team, int maximum_offer, int &num_players_considered);

	/// This function returns the total value of a list of salaries.
	int GetTotalSalary(const SalaryList& salaries);

	/// Finds all the current salaries of a team
	void GetCurrentSalaries( RL3DB_TEAM team, SalaryList& list );

	/// Finds all salaries that will still be in effect next year
	void GetNextYearsSalaries( RL3DB_TEAM team, SalaryList& list );

	/// Adds all signed salaries of a team to a list
	void GetSignedSalaries( RL3DB_TEAM team, SalaryList& list );

	/// Finds all negotiation salaries that if resolved will be in effect during the current year
	void GetNegotiationSalariesThatCouldAffectCurrentYear( RL3DB_TEAM team, SalaryList& salaries );

	/// Finds all negotiation salaries that if resolved will be in effect next year
	void GetNegotiationSalariesThatCouldAffectNextYear( RL3DB_TEAM team, SalaryList& list );

	/// This function returns the amount of money a team needs to spend to
	/// have reach the legal minimum a team must spend on salaries each year.
	/// If the team has reached the minimum this function returns 0
	int CheckSalaryFloor(RL3DB_TEAM team, const SalaryList& salaries);

	int FindPlayerInLineup( unsigned short player_index, MabVector< unsigned short >& lineup );
	int FindPlayerIndex( unsigned short player_id, const WeightedPlayerList& list );

	RL3_POSITION_CATEGORY GetPositionCategory( int index, int num_forwards, int num_backs, int num_wings );
	int GetPositionOffset( int index, int num_forwards, int num_backs, int num_wings );

	// This function takes a team and a vector of players to choose from.
	// It assembles the best group of players (up to team_size), with the
	// optimal team in the first 17 (in the right positions).
	bool SelectPlayers( MabVector< unsigned short >& lineup, RL3DB_TEAM team, const MabVector< unsigned short >& players, int team_size, int num_weeks_to_ignore = 0 );

	// Finds the number of players that are currently available in the
	// team. This does not take into account any players unavailable for
	// rep or international games, only those injured or sentenced by the
	// judiciary.
	int GetNumAvailablePlayers( RL3DB_TEAM team, int rep_area_to_ignore, int weeks_in_the_future );
};

#endif // RL3_COMPETITION_TEAM_HELPER_H
