/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Actions/RUActionTackler.h"

#include "Match/AI/Actions/RUActionTacklee.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Character/RugbyPlayerController.h"
#include "Character/RugbyCharacter.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseRuck.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Match/SSSpatialHelper.h"

//#rc3_legacy_include #include "NMMabAnimationNetwork.h"
//#rc3_legacy_include #include "SIFDebug.h"
//#rc3_legacy_include #include <NMMabAnimationEvents.h>

const char* TACKLE_JOINT = "tacklerAttach";

//#rc3_legacy static const char* GETUP_REQUEST = "getup";

MABRUNTIMETYPE_IMP1(RUActionTackler, RUActionTackleBase);

//#define ENABLE_TACKLE_LOGGING
#ifdef ENABLE_TACKLE_LOGGING
#define MABLOGDEBUGTACKLE( format, ... ) MABLOGDEBUG( format, __VA_ARGS__ )
#else
#define MABLOGDEBUGTACKLE( format, ... )
#endif

RUActionTackler::RUActionTackler( ARugbyCharacter* player )
: RUActionTackleBase( player )
, tackle_result()
, player_down_time( 0.0f )
, ready_to_tackle( false )
, is_attached( false )
, has_contacted( false )
, tacklee_driven( false )
, attach_pending( false )
, blend_out_trigger_detected( false )
, breakdown_started( false )
, ruck_finish_received( false )
, contest_ball_requested( false )
, time_in_state(0.0f)
{
}

RUActionTackler::~RUActionTackler()
{
}

/// RobH: Stop gap measure to copy tackle result before starting the action.
void RUActionTackler::CopyTackleResult(const RUTackleResult& _tackle_result )
{
	tackle_result = _tackle_result;
}

void RUActionTackler::Enter( const RUTackleResult& _tackle_result )
{
	// If we're running and we try start a collision, ignore it
	tackle_result = _tackle_result;
	SetRuckTeamState( NULL );
	breakdown_started = false;
	ruck_finish_received = false;
	contest_ball_requested = false;
	RUActionTackleBase::Enter();

	player_animation->SetTackleHeight( tackle_result.body_position[tackle_result.GetTacklerIndexFromPlayer(m_pPlayer)] );
	player_animation->SetTackleTryProbability( tackle_result.try_tackle_type );
	player_animation->SetTackleSuccess( tackle_result.successful );
	player_animation->SetTackleDominance( tackle_result.dominance );
	player_animation->SetTackleSidestepType( tackle_result.ss_tackle_type );

#ifdef ENABLE_ANIMATION_DEBUG_SETTINGS
	if ( RUPlayerAnimation::show_tackle_result )
		SIF_DEBUG_DRAW( SetText( 200202, 700, 510, MabString(0, "TacklerAnim: Height %i, Dominance: %i", tackle_result.body_position[tackle_result.GetTacklerIndexFromPlayer(player)], tackle_result.dominance ) ) );
#endif

	time_in_state = 0.0f;
}

bool RUActionTackler::InternalEnter()
{
	ready_to_tackle = false;
	is_attached	= false;
	has_contacted = false;
	on_ground = false;
	blend_out_trigger_detected = false;

	if ( tackle_result.taken_down )
	{
		on_ground = true;
	}

	player_down_time = 0.0f;

	if ( !tackle_result.successful && tackle_result.tackle_result != TRT_FEND2 )
		player_down_time = 0.25f;

	// If tackle is dive_miss, skip to next state (as there is no tacklee action)
	if ( tackle_result.tackle_result == TRT_DIVE_MISS )
	{
		next_state = TS_START_TACKLE;
		tacklee_driven = false;

		// Trigger the tackle event to test for offside in RURuleTriggerPenalty.
		// Otherwise there will be no offside test for a failed tackle.
		// Tacklee fires this event if the tackle was successful.
		RUGameState *game_state = m_pPlayer->GetGameWorld()->GetGameState();
		game_state->Tackle( tackle_result );
	}
	else
	{
		tackle_result.tacklee->GetActionManager()->GetAction<RUActionTacklee>()->RegisterTackler( m_pPlayer );
		tacklee_driven = true;
	}

	RUGameEvents* game_events = m_pGame->GetEvents();
	game_events->breakdown_start.Add(this, &RUActionTackler::OnBreakdownStart);
	game_events->ruck_finished.Add( this, &RUActionTackler::OnRuckFinished);

#if defined ( ENABLE_TWO_MAN_TACKLE ) && ENABLE_TWO_MAN_TACKLE
	if (tackle_result.n_tacklers > 1)
	{
		// check current tackler distance to tacklee.

		float dist = m_pGame->GetSpatialHelper()->GetPlayerToPlayerDistance( tackle_result.tacklee, m_pPlayer);

		if (dist > MAX_TACKLE_DISTANCE * 1.32f)
		{
			
			FVector tackleePosition = tackle_result.tacklee->GetMovement()->GetCurrentPosition();
			RUPlayerMovement* movement = m_pPlayer->GetMovement();
			movement->SetTargetPosition(tackleePosition, true);
			movement->SetWaypointAcceptRange(MAX_TACKLE_DISTANCE * 1.32f);
			movement->SetThrottleAndTargetSpeedByUrgency(1.0f, AS_SPRINT, AS_SPRINT);
		}
		

	}
#endif
	
	RUActionTackleBase::Initialise( &tackle_result );
	RUActionTackleBase::InternalEnter();
	m_pPlayer->GetMabAnimationEvent().Add( this, &RUActionTackler::AnimationEvent );
	return true;
}

void RUActionTackler::Update( const MabTimeStep& game_time_step )
{
	// with the new animations having multiple exit paths and the state change system only handling it
	// having 1, we have to force the state along ourselves
	// JOE: this request situation is ultra specific, as i didn't want to risk changing anything that wasn't part of the problem
	bool force_state_change = blend_out_trigger_detected &&	!tacklee_driven && tackle_result.successful == false && tackle_result.tackle_result == TRT_STANDARD && current_state == TS_END_TACKLE && next_state == TS_POST_TACKLE;

	if ( !force_state_change && (tackle_result.tackle_result == TRT_FEND2 || tackle_result.tackle_result == TRT_SIDESTEP) )
	{
		// As above, but force the state for fends and side-steps if required.
		force_state_change = blend_out_trigger_detected && current_state == TS_END_TACKLE && next_state == TS_POST_TACKLE;
	}
	//SIF_DEBUG_DRAW( SetBox( 80185 + player->GetAttributes()->GetNumber(), player->GetMovement()->GetCurrentPosition(), FVector(0.5f, 0.1f, 0.5f), MabColour::Red));

	if( time_in_state > 20.0f )
	{
		UE_LOG(LogTemp, Display, TEXT("PLAYERS_STUCK_DEBUG: Tackler [%s] [%d] has been in this state (%i) for too long (%f)."), *m_pPlayer->GetName(), m_pPlayer->GetAttributes()->GetIndex(), current_state, time_in_state);		
	}

	/*if( current_state == TS_END_TACKLE && next_state == TS_POST_TACKLE )
	{
		bool tacklee_no_ball = tackle_result.tacklee && (tackle_result.tacklee != game->GetGameState()->GetBallHolder());
		bool tacklee_still_tacklee = tackle_result.tacklee && (tackle_result.tacklee->GetActionManager()->IsActionRunning(ACTION_TACKLEE));

		// If our tacklee no longer has the ball, or they are no longer in the tacklee action, then neither should we?
		if( (tacklee_no_ball || !tacklee_still_tacklee) / *&& time_in_state > 3.0f* / )
		{
			bool _debug = false;
			if(_debug)
			{
				SIFApplication::GetApplication()->GetAppTime()->GetSimulationTimeSource()->Pause( true );
			}

			//_debug = false;

			//if(_debug)
			{
				MABLOGDEBUG("PLAYERS_STUCK_DEBUG: [%d] Tackler was in END state for too long?", player->GetAttributes()->GetIndex());
				force_state_change = true;
			}
		}
	}*/

	UpdateState(force_state_change);

	/// Don't allow player to change out of tackle if they are successful and they are the most recent tackle
	/// Doing so can introduce confusion for the user sometimes as it looked like they made the tackle but then weren't awarded the ruck
	{
		bool is_latest_successful_tackler = tackle_result.successful && tackle_result.tackle_determination_index == m_pGame->GetTackleHelper()->GetMasterTackleDeterminationIndex();

		if ( !breakdown_started || ruck_finish_received )	// Default to unlock unless breakdown has started
			m_lock_manager.HFUnlock( HF_CHANGE_PLAYER );

		if ( !ruck_finish_received )
		{
			if ( breakdown_started ||
				(is_latest_successful_tackler && tackle_result.tacklee && tackle_result.tacklee == m_pGame->GetGameState()->GetBallHolder() && m_pPlayer->GetHumanPlayer() != NULL) )
				m_lock_manager.HFLock( HF_CHANGE_PLAYER );
		}

	}

	// Has the last anim finished?
	if ( current_state == TS_POST_TACKLE )
	{
		player_down_time -= game_time_step.delta_time.ToSeconds();
		if ( player_down_time <= 0.0f )
		{
			//MABLOGDEBUGTACKLE( "(%d,%d) RUTacklerUpdater::Advance informing behaviour complete", tackle_result.tackle_determination_index, player->GetAttributes()->GetIndex() );
			Exit();
		}
	}

	time_in_state += game_time_step.delta_time.ToSeconds();
}


void RUActionTackler::InternalExit(bool in_destroy)
{
	UE_LOG(LogTemp, Display, TEXT("InternalExit Tackler: (%d, %s, %d), Stopping tacklee %d, in_destroy(%d)"), tackle_result.tackle_determination_index, *m_pPlayer->GetName(), m_pPlayer->GetAttributes()->GetIndex(), m_pPlayer->GetAttributes()->GetIndex(), in_destroy );	

	if(in_destroy)
		return;

	#if defined( ENABLE_ROLE_DEBUG_STRINGS )
	if ( player->GetRole() )
	{
		player->GetRole()->SetDebugString( "" );
	}
	#endif

	//SIF_DEBUG_DRAW( RemoveBox(80185 + player->GetAttributes()->GetNumber()) );
	DetachFromTacklee();	
	m_pPlayer->GetAnimation()->GetStateMachine().SendRequest("getup", true );
	contest_ball_requested = false; // Reset here in case

	RUGameEvents* game_events = m_pGame->GetEvents();
	game_events->breakdown_start.Remove(this, &RUActionTackler::OnBreakdownStart);
	game_events->ruck_finished.Remove( this, &RUActionTackler::OnRuckFinished);
	m_pPlayer->GetMabAnimationEvent().Remove( this, &RUActionTackler::AnimationEvent );
	RUActionTackleBase::InternalExit(in_destroy);
}


void RUActionTackler::OnStateTransition( TackleState old_state, TackleState new_state )
{
	MABUNUSED(old_state);
	RUPlayerAttributes *attributes = m_pPlayer->GetAttributes();
	RUPlayerMovement *movement = m_pPlayer->GetMovement();

	MABUNUSED( movement );
	//MABLOGDEBUGTACKLE( "(%d,%d) RUTacklerUpdater::OnStateTransition %s tackler true pos is now %f %f %f, rot %f", tackle_result.tackle_determination_index, attributes->GetIndex(), GetStateName( new_state ).c_str(), movement->GetCurrentPosition().x, movement->GetCurrentPosition().y, movement->GetCurrentPosition().z, movement->GetCurrentFacingAngle() );

	if ( !tacklee_driven )
	{
		RequestTransition(GetNextState(new_state));
	}

	time_in_state = 0.0f;

	switch ( new_state )
	{
	case TS_PRE_TACKLE:
		ready_to_tackle = true;
		break;

	case TS_START_TACKLE:
		{
			// Set the new aggression levels of the tackler
			int tackler_index = tackle_result.GetTacklerIndexFromPlayer( m_pPlayer );
			MABASSERT( tackler_index >= 0 && tackler_index < MAX_TACKLERS);
			attributes->SetAggression( tackle_result.new_tackler_aggression[ tackler_index ] );
			if (tackle_result.tackle_result != TRT_DIVE_MISS)
				SyncToTackleJoint();
			//TriggerImpactRumble( tackle_result );
		}
		break;

	case TS_IN_TACKLE_STANDING:
		MABASSERT( is_attached || !tacklee_driven );
		break;

	case TS_IN_TACKLE_ON_GROUND:
		// for contested tackles switch to normal state machine when it hits the ground
		// to cater for the 2 different ways these tackle types can end
		if ( tackle_result.tackle_result == TRT_CONTESTED )
			tackle_result.state_machine = TSM_SUCCESS;

		//if (SSHumanPlayer* human = player->GetHumanPlayer())
		//	human->AssignBestPlayer();

		break;

	case TS_TRANSITION_TO_GROUND:
		m_pPlayer->GetMovement()->SetMotionSource( NULL );
		break;

	case TS_TRANSITION_TO_BREAKOUT:
		DetachFromTacklee();
		m_pPlayer->GetMovement()->SetMotionSource( NULL );
		break;

	case TS_TRANSITION_TO_STRIP_FAIL:
		break;

	case TS_TRANSITION_TO_STRIP_SUCCESS:
		DetachFromTacklee();
		break;

	case TS_TRANSITION_TO_STRIP_PARTIAL:
		DetachFromTacklee();
		break;

	case TS_END_TACKLE:
	{
		DetachFromTacklee();
		break;
	}

	case TS_POST_TACKLE:
		//if (!contest_ball_requested)
		//	player_down_time = 1.0f;
		//player->GetMovement()->StopAllMovement();
		m_pPlayer->GetMovement()->ResetLean();
		DetachFromTacklee();
		break;

	default:
		break;
	}
}

bool RUActionTackler::AreAllTacklersInRange()
{
	for ( int i = 0; i < tackle_result.n_tacklers; ++i )
	{
		if ( tackle_result.tacklers[i] == NULL )
			continue;

		float dist = m_pGame->GetSpatialHelper()->GetPlayerToPlayerDistance( tackle_result.tacklers[i], tackle_result.tacklee );
		if ( dist > MAX_TACKLE_DISTANCE * 1.32f )
		{
			return false;
		}
	}
	return true;
}


void RUActionTackler::ContinueDetached(bool release_immediately)
{
	tacklee_driven = false;
	DetachFromTacklee();
	if ( release_immediately && current_state < TS_END_TACKLE )
	{
		RequestTransition(TS_END_TACKLE);
	}
}

void RUActionTackler::SyncToTackleJoint()
{
	is_attached = true;
}

void RUActionTackler::DetachFromTacklee()
{
	if ( is_attached )
	{
		is_attached = false;
		// State machine is now running locally, assign the next state to move to
		AssignNextState();
	}
}

/// For the current aggregate, say whether or not the given aggregate is allowed to run
bool RUActionTackler::CanEnterOtherAction( RU_ACTION_INDEX id )
{
	MABUNUSED(id);

	// Not running, can start a new aggregate otherwise can never interrupt
	// a tackle.
	return !IsRunning();
}

/// The player should go to the ground
bool RUActionTackler::ShouldGoToGround()
{
	if ( tacklee_driven )
	{
		// TYRONE : Should never come in here but we will add just in case
		RUActionTacklee* action = tackle_result.tacklee->GetActionManager()->GetAction<RUActionTacklee>();
		if ( action->IsRunning() )
		{
			return action->ShouldGoToGround();
		}

	}
	return false;
}

bool RUActionTackler::IsAnimationRunning( const char* animation ) //not called, dont bother enabling this.
{
	return false; //#rc3_legacy return ( network->IsCurrentState(animation ) );
}

void RUActionTackler::OnBreakdownStart(ARugbyCharacter* breakdown_tacklee)
{
	if ( breakdown_tacklee == NULL )
		return;

	/// Are we the same tackler for this tacklee?
	if (breakdown_tacklee == tackle_result.tacklee )
	{
		/// TYRONE : Now that tackles are re-entrant we need to check that the breakdown tacklees current tackle is running
		/// and the tackle index matches ours
		bool tacklee_running = breakdown_tacklee->GetActionManager()->IsActionRunning( ACTION_TACKLEE );
		bool tackler_running = IsRunning();
		RUTackleResult& tacklee_result = breakdown_tacklee->GetActionManager()->GetAction< RUActionTacklee >()->GetTackleResult();
		RUTackleResult& tackler_result = GetTackleResult();
		bool same_tackle = tacklee_running && tackler_running && tacklee_result.tackle_determination_index == tackler_result.tackle_determination_index;

		if ( same_tackle )
		{
			m_lock_manager.HFLock(HF_CHANGE_PLAYER);
			breakdown_started = true;
		}
	}
}


///-------------------------------------------------------------------------
/// 'ruck_finished' event received.
///-------------------------------------------------------------------------

void RUActionTackler::OnRuckFinished(int /*ruck_id*/)
{
	// check if this is our ruck
	/// TYRONE : Disabled as sometimes the NULL pointer checks are invalid
	// Can't see why this would happen but hey
	//if ( ruck_team_state == NULL )
	//	return;

	//if ( ruck_team_state->ruck_state == NULL )
	//	return;

	//if ( ruck_id != ruck_team_state->ruck_state->ruck_id)
	//	return;

	contest_ball_requested = false; // Reset here in case
	ruck_finish_received = true;
}

void RUActionTackler::AnimationEvent(float /*time*/, ERugbyAnimEvent event, size_t userdata, bool /*IsBlendingOut = false*/)
{
	if ( event == ERugbyAnimEvent::TACKLE_CONTACT_EVENT )
	{
		has_contacted = true;
		TriggerImpactRumble( tackle_result );
	}
	else if ( userdata == RUGameAnimation::FOOTSTEP_BLEND_OUT || userdata == RUGameAnimation::NO_FOOTSTEP_DATA_BLEND_OUT || userdata == RUGameAnimation::ON_GROUND_TO_GETUP_BLEND_OUT )
	{
		// blend out user data trigger
		blend_out_trigger_detected = true;
	}
}

void RUActionTackler::SetContestBallRequested()
{
	contest_ball_requested = true;
}

void RUActionTackler::AbortContestBallRequested()
{
	contest_ball_requested = false;
}
