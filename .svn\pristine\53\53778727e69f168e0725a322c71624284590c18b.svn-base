import random
import csv
import os

# The list of column headers in the correct order for the CSV file.
CSV_HEADERS = [
    "ranking","attack","defence","ruck_ability","maul_ability","scrum_ability","lineout_ability",
    "def_forward_pass_drive","def_forward_contact_offload","def_back_pass_kick","def_back_contact_offload",
    "def_lineout_size","def_lineout_favoured_target","def_ruck_win","def_lineout_win","def_scrum_win",
    "def_maul_win","def_line_width","def_line_depth","def_ruck_commitment","def_pod_option",
    "mid_forward_pass_drive","mid_forward_contact_offload","mid_back_pass_kick","mid_back_contact_offload",
    "mid_lineout_size","mid_lineout_favoured_target","mid_ruck_win","mid_lineout_win","mid_scrum_win",
    "mid_maul_win","mid_line_width","mid_line_depth","mid_ruck_commitment","mid_pod_option",
    "att_forward_pass_drive","att_forward_contact_offload","att_back_pass_kick","att_back_contact_offload",
    "att_lineout_size","att_lineout_favoured_target","att_ruck_win","att_lineout_win","att_scrum_win",
    "att_maul_win","att_line_width","att_line_depth","att_ruck_commitment","att_pod_option",
    "kick_kickoff_short_long","kick_kickoff_left_right","kick_dropout_short_long","kick_dropout_left_right",
    "kick_touch_territory","kick_penalty_touch_goal"
]

def generate_team_stats(ranking):
    """
    Generates a full set of team stats based on an overall ranking.
    """
    if not 5000 <= ranking <= 10000:
        # Allow rankings slightly outside the 5000-10000 range for this specific case
        pass

    def create_stat(base_ranking, variation_percentage=0.05):
        min_val, max_val = 5000, 10000
        normalized_ranking = (base_ranking - min_val) / (max_val - min_val)
        base_stat = min_val + normalized_ranking * (max_val - min_val)
        variation_amount = (max_val - min_val) * variation_percentage
        variation = random.uniform(-variation_amount, variation_amount)
        final_stat = round(base_stat + variation)
        return int(max(min_val, min(max_val, final_stat)))

    stats = {
        "ranking": ranking, "attack": create_stat(ranking), "defence": create_stat(ranking),
        "ruck_ability": create_stat(ranking), "maul_ability": create_stat(ranking),
        "scrum_ability": create_stat(ranking), "lineout_ability": create_stat(ranking),
        "def_forward_pass_drive": create_stat(ranking), "def_forward_contact_offload": create_stat(ranking),
        "def_back_pass_kick": create_stat(ranking), # NOW: 5000-10000 stat
        "def_back_contact_offload": create_stat(ranking), "def_lineout_size": create_stat(ranking),
        "def_lineout_favoured_target": random.choice([0, 5000, 10000]), "def_ruck_win": create_stat(ranking),
        "def_lineout_win": create_stat(ranking), "def_scrum_win": create_stat(ranking), "def_maul_win": create_stat(ranking),
        "def_line_width": create_stat(ranking), "def_line_depth": create_stat(ranking), "def_ruck_commitment": create_stat(ranking),
        "def_pod_option": random.randint(0, 2), "mid_forward_pass_drive": create_stat(ranking),
        "mid_forward_contact_offload": create_stat(ranking), "mid_back_pass_kick": create_stat(ranking), # NOW: 5000-10000 stat
        "mid_back_contact_offload": create_stat(ranking), "mid_lineout_size": create_stat(ranking),
        "mid_lineout_favoured_target": random.choice([0, 5000, 10000]), "mid_ruck_win": create_stat(ranking),
        "mid_lineout_win": create_stat(ranking), "mid_scrum_win": create_stat(ranking), "mid_maul_win": create_stat(ranking),
        "mid_line_width": create_stat(ranking), "mid_line_depth": create_stat(ranking), "mid_ruck_commitment": create_stat(ranking),
        "mid_pod_option": random.randint(0, 2), "att_forward_pass_drive": create_stat(ranking),
        "att_forward_contact_offload": create_stat(ranking), "att_back_pass_kick": create_stat(ranking), # NOW: 5000-10000 stat
        "att_back_contact_offload": create_stat(ranking), "att_lineout_size": create_stat(ranking),
        "att_lineout_favoured_target": random.choice([0, 5000, 10000]), "att_ruck_win": create_stat(ranking),
        "att_lineout_win": create_stat(ranking), "att_scrum_win": create_stat(ranking), "att_maul_win": create_stat(ranking),
        "att_line_width": create_stat(ranking), "att_line_depth": create_stat(ranking), "att_ruck_commitment": create_stat(ranking),
        "att_pod_option": random.randint(0, 2), "kick_kickoff_short_long": create_stat(ranking),
        "kick_kickoff_left_right": create_stat(ranking), "kick_dropout_short_long": create_stat(ranking),
        "kick_dropout_left_right": create_stat(ranking), "kick_touch_territory": create_stat(ranking),
        "kick_penalty_touch_goal": create_stat(ranking)
    }
    return stats

def save_team_to_csv(team_data, filename="rugby_teams.csv"):
    """
    Saves a dictionary of team data to a CSV file.
    """
    file_exists = os.path.isfile(filename)
    with open(filename, mode='a', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=CSV_HEADERS)
        if not file_exists:
            writer.writeheader()
        writer.writerow(team_data)

# --- Main Generation ---

# A list of women's teams and their assigned rankings on the combined scale.
teams_to_generate = [
    # NRLW Teams
    {"name": "Brisbane", "rank": 8300},
    {"name": "Canberra", "rank": 8100},
    {"name": "Canterbury-Bankstown", "rank": 7900},
    {"name": "Cronulla", "rank": 8200},
    {"name": "Gold Coast", "rank": 8500},
    {"name": "Newcastle", "rank": 8600},
    {"name": "New Zealand", "rank": 8250},
    {"name": "North Queensland", "rank": 8000},
    {"name": "Parramatta", "rank": 7850},
    {"name": "St George Illawarra", "rank": 8400},
    {"name": "Sydney", "rank": 8550},
    {"name": "Wests Sydney", "rank": 8050},
    # Women's Super League Teams
    {"name": "Barrow", "rank": 7000},
    {"name": "Huddersfield", "rank": 7100},
    {"name": "Leeds", "rank": 8350},
    {"name": "Leigh", "rank": 7050},
    {"name": "St Helens", "rank": 8300},
    {"name": "Warrington", "rank": 7200},
    {"name": "Wigan", "rank": 7500},
    {"name": "York", "rank": 8450},
    # International Teams
    {"name": "Australia", "rank": 9100},
    {"name": "New Zealand", "rank": 8900},
    {"name": "England", "rank": 8700},
    {"name": "Papua New Guinea", "rank": 8000},
    {"name": "France", "rank": 7700},
    {"name": "Tonga", "rank": 7500},
    {"name": "Fiji", "rank": 7500},
    {"name": "Canada", "rank": 7200},
]

print("--- Generating International Teams ---")

# Loop through the list and generate each team
for team_info in teams_to_generate:
    team_name = team_info["name"]
    team_rank = team_info["rank"]
    
    # Generate the full stat dictionary for the team
    generated_team_data = generate_team_stats(team_rank)
    
    # Save the team to the CSV file
    save_team_to_csv(generated_team_data)
    
    print(f"✅ {team_name} (Ranking: {team_rank}) has been saved.")

print("\n--- All teams have been generated and saved to 'rugby_teams.csv' ---")
