//#rc3_legacy_pch #include <Precompiled.h>
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhasePlayTheBall.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Match/AI/Roles/Competitors/RURoleKickOffKicker.h"
#include "Match/Ball/SSBall.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SIFGameObject.h"
#include "Match/SIFGameWorld.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/SetPlays/SSSetPlayManager.h"
#include "Match/SSRoleFactory.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBall.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBallDefender.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBallSecondDefender.h"
#include "Match/AI/Actions/RUActionTacklee.h"
#include "Match/AI/Actions/RUActionTacklee.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"

#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBallReceiver.h"

MABRUNTIMETYPE_IMP1( RUGamePhasePlayTheBall, RUGamePhaseHandler );

///-------------------------------------------------------------------------
/// Constructor
///-------------------------------------------------------------------------

RUGamePhasePlayTheBall::RUGamePhasePlayTheBall(SIFGameWorld *ggame)
: game(ggame)
//, kicker(NULL)
{}

///-------------------------------------------------------------------------
/// Destructor
///-------------------------------------------------------------------------
RUGamePhasePlayTheBall::~RUGamePhasePlayTheBall()
{
}

///-------------------------------------------------------------------------
/// Have just changed to this phase
///-------------------------------------------------------------------------
#pragma optimize("", off)
void RUGamePhasePlayTheBall::Enter()
{
	RUGameState* state = game->GetGameState();
	if (state)
	{
		if (game->GetStrategyHelper())
		{
			game->GetStrategyHelper()->ClearRoles();
		}

		//game->GetEvents()->play_the_ball_started(); // Disable Set Plahy UI

		state->LockHumanMovement(true);
		state->SetPlayRestartPosition(game->GetBall()->GetCurrentPosition());

		if (game->GetGameState()->IsSixthTackle())
		{
			// Do handover, get our main tackler
			ARugbyCharacter* ballHolder = game->GetGameState()->GetBallHolder();
			// Get tackle result
			RUActionTacklee* tackleAction = ballHolder->GetActionManager()->GetAction<RUActionTacklee>();

			if (tackleAction)
			{
				RUTackleResult& tackleResult = tackleAction->GetTackleResult();
				ARugbyCharacter* tackler = tackleResult.tacklers[0];
				if (tackler)
				{
					game->GetGameState()->SetBallHolder(tackler);
					game->GetGameState()->ResetTackleCount();
				}

				// Call event and signal new ball holder (tackler)- can bind commentary to this?
				game->GetEvents()->six_tackle_handover(tackler);
			}
		}
		else if (game->GetGameState()->GetHandoverType() == EHandoverType::OUTONFULL)
		{
			// GGS JZ handover events can happen on the 5th tackle, such as forward pass, knockon.
			// It can also happens for kicks on the full which should reset to the kick pos, check the 2023 rule book for reference https://www.nrl.com/siteassets/operations/the-game/nrl-international-rules-book-2023.pdf.
			// If kicked on full, do handover where the ball was kicked
			RUTeam* attackingTeam = game->GetGameState()->GetAttackingTeam();
			// We dont care who gets the handover on the new team
			auto closestplayerfunction = game->GetGameState()->GetClosestPlayerToBall(attackingTeam);
			// This should be were the ball was kicked not where the play is at this point.
			game->GetGameState()->SetPlayRestartPosition(game->GetGameState()->GetLastBallHolder()->GetMovement()->GetCurrentPosition());
			// This should be fine as we should do a cutscene on a full kick to reset the play
			game->GetGameState()->SetBallHolder(closestplayerfunction);
			game->GetGameState()->ResetTackleCount();

			// Set the offending player away from the ball as the defender will warp to that pos after cutscene
			BallFreeInfo bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
			ARugbyCharacter* offending_player = bfi.last_player;
			FVector currPos = offending_player->GetMovement()->GetCurrentPosition();
			FVector runbackPos = FVector(currPos.x, currPos.y, currPos.z + 10.0f * -offending_player->GetAttributes()->GetPlayDirection());
			offending_player->GetMovement()->SetCurrentPosition(runbackPos);

			// Should be used to call commentary, etc
			game->GetEvents()->handover();
		}

		// #MLW Is there a better way of disabling human players at this point? 
		//const MabVector<SSHumanPlayer*>& human_players = game->GetHumanPlayers();
		//for (size_t i = 0; i < human_players.size(); ++i)
		//{
		//	SSHumanPlayer* human_player = human_players[i];
		//	human_player->SetAllInputIgnored(true);
		//}
	}
}
#pragma optimize("", on)

///-------------------------------------------------------------------------
/// Have just exited from this game phase.
///-------------------------------------------------------------------------
void RUGamePhasePlayTheBall::Exit()
{
	game->GetGameState()->LockHumanMovement(false);
	game->GetEvents()->play_the_ball_finished();
}

///-------------------------------------------------------------------------
/// Called every frame when this phase is active.
///-------------------------------------------------------------------------
void RUGamePhasePlayTheBall::UpdateSimulation( const MabTimeStep& /*game_time_step*/ )
{
}

void RUGamePhasePlayTheBall::Reset()
{

}

float RUGamePhasePlayTheBall::GetEstimatedTimeTillBallBackInPlay()
{
	/*
	SIFRugbyCharacterList players;
	RUTeam* attacking_team = game->GetGameState()->GetAttackingTeam();
	if ( attacking_team )
	{
		game->GetStrategyHelper()->GetTeamRoles( attacking_team, RURoleKickOffKicker::RTTGetStaticType(), players );

		if (players.empty())
		{
			wwNETWORK_TRACE_JG_DISABLED("GetEstimatedTimeTillBallBackInPlay Players EMPTY");
			return 20.0f;
		}

		// There should only be one
		MABASSERT( players.size() == 1 );

		ARugbyCharacter* kicker = players.front();
		RURoleKickOffKicker* kick_off_kicker = MabCast<RURoleKickOffKicker>( kicker->GetRole() );
		if (kick_off_kicker)
		{
			wwNETWORK_TRACE_JG_DISABLED("GetEstimatedTimeTillBallBackInPlay kick_off_kicker->GetEstimatedTimeTillBallContact()");
			return kick_off_kicker->GetEstimatedTimeTillBallContact();
		}
	}

	wwNETWORK_TRACE_JG_DISABLED("GetEstimatedTimeTillBallBackInPlay Default");
	*/
	return 20.0f;
}