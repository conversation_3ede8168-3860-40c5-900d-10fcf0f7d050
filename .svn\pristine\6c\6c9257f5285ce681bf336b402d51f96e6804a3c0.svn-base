/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef _RUTACKLEHELPER_H
#define _RUTACKLEHELPER_H

#include "Mab/Types/MabString.h"
#include "Match/RugbyUnion/Enums/RUPenaltyDecisionEnum.h"
#include "Match/RugbyUnion/Enums/RUSideStepEnum.h"
#include "Match/RugbyUnion/Enums/RUTackleEnum.h"
#include "Match/RugbyUnion/RUMovementState.h"
#include "Rugby/Animation/RugbyAnimationRecords.h"

#include <array>
#include <list>

/**
 These classes provide various helpers for dealing with tackles
 This includes both helpers for computer based players and also
 for computer AI players

 <AUTHOR> McAuley
*/

// Forward declarations
class RUDatabase;
class RUActionTacklee;

class SIFGameWorld;
class ARugbyCharacter;

// Classwide constants
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
static const int   MAX_TACKLERS = 2;
#else
static const int   MAX_TACKLERS = 1;
#endif
static const float MAX_TACKLE_DISTANCE = 1.4f;
static const float MAX_HUMAN_TACKLE_DISTANCE = 1000.0f;	// Humans can tackle from anywhere
static const float MAX_TACKLE_DISTANCE_SQ = MAX_TACKLE_DISTANCE * MAX_TACKLE_DISTANCE;
static const float MAX_CHARGE_CLOSURE_TIME = 0.7f;
static const float MAX_CHARGE_ATTACKER_DIST = 7.0f;

class RUTackleResult
{
public:
	RUTackleResult();
	virtual ~RUTackleResult();

	/// Reset
	void Reset();
	void GameReset();
	void printState();

	/// the tackle result
	TACKLE_RESULT_TYPE tackle_result;
	/// array of body positions for each tackler
	std::array<TACKLE_BODY_POSITION, MAX_TACKLERS> body_position;
	/// array of directions for each tackler
	std::array<TACKLER_DIRECTION, MAX_TACKLERS> tackler_direction;
	/// array of tackled from directions for each tackler
	std::array<TACKLED_FROM_DIRECTION, MAX_TACKLERS> tackled_from_direction;
	/// which arm was used
	std::array<TACKLER_ARM, MAX_TACKLERS> tackler_arm;
	/// what fend type the tacklee will try
	FEND_TYPE fend_type;

	TACKLE_STATE_MACHINE state_machine;		/// The tackle state machine to use for this tackle
	TACKLE_DOMINANCE dominance;				/// The relative dominance of tacklee to tackler in the tackle

	/// Sub contexts for tackles
	STANDARD_TACKLE_TYPE std_tackle_type;
	SIDESTEP_TACKLE_TYPE ss_tackle_type;
	FEND_TACKLE_TYPE fend_tackle_type;
	TRY_TACKLE_TYPE try_tackle_type;

	float tackle_angle;

	std::array<float, MAX_TACKLERS> tackler_speed;
	std::array<float, MAX_TACKLERS> max_tackler_impetus;
	float actual_tacklers_impetus;
	float tacklee_speed;
	float max_tacklee_impetus;
	float actual_tacklee_impetus;
	std::array<ARugbyCharacter*, MAX_TACKLERS> tacklers;
	int n_tacklers;
	ARugbyCharacter* tacklee;
	std::array<float, MAX_TACKLERS> tackler_aggression;
	float tacklee_aggression;
	std::array<float, MAX_TACKLERS> new_tackler_aggression;
	float new_tacklee_aggression;
	/// Player is allowed to offload in this tackle
	bool can_offload;
	/// Tacklee is taken down to the ground, tackle is complete
	bool taken_down;
	/// Tacklee is held
	bool held;
	/// When will the ball be jolted free
	TACKLEE_DROP_TYPE drop_ball_type;
	/// All players that will be injured in the tackle
	std::array<ARugbyCharacter*, MAX_TACKLERS+1> injured_players;
	/// Players suspended for this tackle
	std::array<SUSPENSION_RESULT, MAX_TACKLERS> tacklers_suspended;
	/// The types of injuries for all injured players
	std::array<TACKLE_INJURY_TYPE, MAX_TACKLERS+1> injury_types;
	TACKLE_PRONE_TYPE tackle_prone_type;
	TACKLE_HEAVY_TYPE tackle_heavy_type;
	/// Was it a successful tackle?
	bool successful;
	/// Is a video ref tackle
	bool is_video_ref;
	/// Is it a try based tackle?
	bool is_try_tackle;
	/// What probability is it that this will result in a try
	float prob_video_ref_try;
	/// A bit flag of all the anim sequences that are allowed
	int anim_sequences;
	// What rate should the animation play at
	float play_rate;
	// Align anim
	TACKLE_ALIGN_TYPE align_anim;

	/// The variation of the animation that has been selected
	int variant_index;

	// Mainly used for debugging when tackles oveRUap in the logs
	int tackle_determination_index;

	/// TYRONE : Added for tackle test bed coverage - not really needed for anything else but store initial movement state when we enter a tackle
	struct MOVEMENT_DATA
	{
		MOVEMENT_DATA() : state(), istate(), params() {}
		MOVEMENT_STATE state;
		MOVEMENT_INTERMEDIATE_STATE istate;
		MOVEMENT_PARAMETERS params;
	};
	MOVEMENT_DATA init_tacklee_movement;
	std::array<MOVEMENT_DATA, MAX_TACKLERS> init_tackler_movement;

	/// Get the relevant tacklee/tackler animation names
	MabString GetTackleeAnimationName( TACKLE_SEQUENCE sequence = TAS_IMPACT ) const;

	MabString GetTacklerAnimationName( TACKLE_SEQUENCE sequence = TAS_IMPACT, int tackler_index = 0 ) const;
	/// Get injury animation name

	MabString GetInjuryAnimationName( ARugbyCharacter* player, bool get_transition, bool on_back ) const;
	/// get the number of possible variations on this basic tackle type
	//int GetNumberOfVariants() const;
	/// find the index of the tackler based on his pointer. Returns 0 (and ASSERTs) if not found.
	int GetTacklerIndexFromPlayer( ARugbyCharacter* player ) const;

	/// returns true if the given player is injured by this tackle
	bool IsPlayerInjured( ARugbyCharacter* player ) const;

	/// returns true if the given player is suspended by this tackle
	bool IsTacklerSuspended( int player_index ) const;

	/// get the injury type for the given injured player, or TIT_NONE if the player isn't injured.
	TACKLE_INJURY_TYPE GetTackleInjuryType( ARugbyCharacter* player ) const;

	/// Check if the tackle result is of try type
	bool isTackleResultTryType();

	//@name Enumerations to string conversions
	//@{
	// Long Names
	static MabString GetTackleTypeString( TACKLE_RESULT_TYPE type );
	MabString GetTackleTypeString();
	static MabString GetTackleBodyPositionString( TACKLE_BODY_POSITION body_position );
	static MabString GetTacklerDirectionString( TACKLER_DIRECTION dir );
	static MabString GetTackledFromDirectionString( TACKLED_FROM_DIRECTION tackled_from );
	static MabString GetTackleStateMachineString( TACKLE_STATE_MACHINE state_machine );
	static MabString GetTackleDominanceString( TACKLE_DOMINANCE dominance );
	static MabString GetStandardTackleTypeString( STANDARD_TACKLE_TYPE standard_tackle_type );
	static MabString GetSideStepTackleTypeString( SIDESTEP_TACKLE_TYPE sidestep_tackle_type );
	static MabString GetFendTackleTypeString( FEND_TACKLE_TYPE fend_tackle_type );
	static MabString GetTryTackleTypeString( TRY_TACKLE_TYPE try_tackle_type );
	static MabString GetTacklerArmString( TACKLER_ARM tackler_arm );
	static MabString GetFendTypeString( FEND_TYPE fend_type );
	static MabString GetTackleeDropTypeString( TACKLEE_DROP_TYPE type );
	static MabString GetTackleProneTypeString( TACKLE_PRONE_TYPE type );
	static MabString GetTackleHeavyTypeString( TACKLE_HEAVY_TYPE type );

	// Mnemonics
	static MabString GetTackleBodyPositionMnemonic( TACKLE_BODY_POSITION body_position );
	static TACKLE_BODY_POSITION GetTackleBodyPositionFromMnemonic( MabString& body_position_mnemonic );
	static MabString GetTacklerDirectionMnemonic( TACKLER_DIRECTION dir );
	static MabString GetTackledFromDirectionMnemonic( TACKLED_FROM_DIRECTION tackled_from );
	static MabString GetTackleSequenceMnemonic( TACKLE_SEQUENCE sequence );
	//@}

	/// get a short string describing this RUTackleResult
	MabString GetShortDebugString();
	/// get a comprehensive string describing this RUTackleResult
	MabString GetDebugString();
	/// this is called when a requested animation doesn't exist - it will fall back to some
	/// default replacement
	void FallBackToMostAppropriateAvailableTackle();

private:
	friend class RUTackleHelper;
	MabString GetBaseTackleAnimationName( TACKLE_SEQUENCE sequence = TAS_IMPACT ) const;
};

/// Meta information struct for various tackle range data
struct RUTackleRangeMeta
{
	RUTackleRangeMeta() :
		in_immediate_tackle_dist( false ),
		in_normal_tackle_range( false ),
		in_charge_attacker_range( false ),
		in_dive_miss_range( true ),
		in_side_step_range( false ) {}

	bool in_immediate_tackle_dist;	// Are we close enough to do an immediate tackle?
	bool in_normal_tackle_range;	// Are we in range to perform a normal tackle
	bool in_charge_attacker_range;	// Are we in range to charge at the tacklee?
	bool in_dive_miss_range;		// Are we in range to do a dive miss?
	bool in_side_step_range;		// Are we close enough to fool him with a sidestep?
};

class RUTackleHelper
{
public:
	RUTackleHelper( SIFGameWorld* game );
	~RUTackleHelper();

	void Reset();
	void GameReset();
	void printState();

	/// Quick test to see if a player is in a position he can tackle from
	bool IsTackleCandidate( ARugbyCharacter* player, bool ignore_interruptable = false );

	/// GetTackleRangeMeta - work out various meta range information
	void GetTackleRangeMeta( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, RUTackleRangeMeta& returned_meta );

	/// Advanced tackle calculator
	bool DetermineTackleResult( ARugbyCharacter* tackler, bool heavy_tackle, SIDESTEP_SIDE sidestep_side, RUTackleResult& result );

	/// Helper functions

	// Should this player do an AI tackle - We use this also for when
	// humans take over an AI player but give them a short grace period for tackling the player
	// This takes a random number, which is used to decide to do offside tackles
	bool ShouldDoAnAITackle( ARugbyCharacter* player, float rand_for_offside_tackle );

	void SetForcedTackleResult( const RUTackleResult* tackle_result );

	/// Construct & return tackle node name
	void TackleNodeName( char* name, int name_len,
						 TACKLE_RESULT_TYPE type, bool tacklee, bool successful=true,
						 TACKLE_BODY_POSITION height= TACKLE_BODY_POSITION::TBP_UNKNOWN, TRY_TACKLE_TYPE try_type= TRY_TACKLE_TYPE::TTT_UNKNOWN,
						 TACKLE_DOMINANCE dominance= TACKLE_DOMINANCE::TDOM_UNKNOWN, SIDESTEP_TACKLE_TYPE sidestep_type= SIDESTEP_TACKLE_TYPE::SSTT_UNKNOWN ) const;
	

#ifdef DEBUG_DIVE_TACKLE
	void DebugTackle( bool debug );
#endif

	int GetMasterTackleDeterminationIndex() const;
	static void ClearMasterTackleDeterminationIndex();

private:
	SIFGameWorld* game;					/// Accessor for the game
	float min_injury_probability;
	bool forced_tackle_result_set;
	RUTackleResult forced_tackle_result;

	TACKLE_BODY_POSITION GetTackleBodyPosition( float tackler_to_tacklee_dist );
	TACKLER_DIRECTION GetTacklerDirection( ARugbyCharacter*  tackler, ARugbyCharacter* tacklee, float project_time = 0.0f );
	TACKLED_FROM_DIRECTION GetTackledFromDirection( ARugbyCharacter* tackler, ARugbyCharacter* player_to_tackle );
	TACKLE_HEAVY_TYPE GetHeavyTackleResult( ARugbyCharacter*  tacklee, ARugbyCharacter* tackler, bool heavy_tackle );
	float GetTackleAngle(ARugbyCharacter*  tackler, ARugbyCharacter* tacklee);
	void GetMaxTackleImpetus( ARugbyCharacter* player, ARugbyCharacter* tacklee, float speed, float aggression, TACKLED_FROM_DIRECTION tfd, bool is_tacklee, bool heavy_tackle, float& max_tackle_impetus );
	bool DetermineTackleResultInternal( ARugbyCharacter* tackler, bool heavy_tackle, SIDESTEP_SIDE sidestep_side, RUTackleResult& result );
	bool WillDropBallInHeavyTackle( ARugbyCharacter* tacklee );
	bool SetInjuryStatus( RUTackleResult& result );
	bool SetSuspensionStatus( RUTackleResult& result );

	/// New tackle types for RU
	bool IsGroundGetup( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, TACKLER_DIRECTION td, TACKLED_FROM_DIRECTION tfd, bool heavy_tackle, RUTackleResult& result );
	bool IsAFend( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, TACKLER_DIRECTION td, TACKLED_FROM_DIRECTION tfd, bool heavy_tackle, RUTackleResult& result );
	bool IsASideStep( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, TACKLER_DIRECTION td, TACKLED_FROM_DIRECTION tfd, SIDESTEP_SIDE sidestep_side, RUTackleResult& result );
	bool IsAContestedTackle( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, TACKLER_DIRECTION td, TACKLED_FROM_DIRECTION tfd, bool heavy_tackle, RUTackleResult& result );
	bool IsATryCornerTackle( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, RUTackleResult& result );
	bool IsATryTackle( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, TACKLER_DIRECTION td, TACKLED_FROM_DIRECTION tfd, bool heavy_tackle, RUTackleResult& result );
	bool IsAnAnkleTap( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, TACKLER_DIRECTION td, TACKLED_FROM_DIRECTION tfd, bool heavy_tackle, RUTackleResult& result );
	bool IsAHeadHigh( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, TACKLER_DIRECTION td, TACKLED_FROM_DIRECTION tfd, bool heavy_tackle, RUTackleResult& result );
	bool IsADivingMiss( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, RUTackleResult& result );

	/// Check if best animation of given tackle type will warp (needs huge alignment)
	bool HasValidTackleAnim( ARugbyCharacter* tacklee, ARugbyCharacter* tackler,
							TACKLE_RESULT_TYPE type, bool successful=true,
							TACKLE_BODY_POSITION height= TACKLE_BODY_POSITION::TBP_UNKNOWN, TRY_TACKLE_TYPE try_type= TRY_TACKLE_TYPE::TTT_UNKNOWN,
							TACKLE_DOMINANCE dominance= TACKLE_DOMINANCE::TDOM_UNKNOWN, SIDESTEP_TACKLE_TYPE sidestep_type= SIDESTEP_TACKLE_TYPE::SSTT_UNKNOWN ) const;

	// Tackle information
	void VerifyAndCullMultipleTacklers( RUTackleResult& result );

	#ifdef BUILD_DEBUG
	bool VerifyMultiManTackleAnimations();
	#endif

	// Order Tacklers in the given result based on tackled from direction, then by left right order for direction
	// This makes animation lookups alot easier for multi person tackles
	void OrderTacklerPlayers( RUTackleResult& result );

	// Tackled from direction lists used for multiple tacklers
	typedef std::list< TACKLED_FROM_DIRECTION > TFD_LIST;

	static bool TFDLengthSort( const RUTackleHelper::TFD_LIST& tfd1, const RUTackleHelper::TFD_LIST& tfd2 );

	void GenerateTackledFromDirectionPermutations( int n_tacklers, const TFD_LIST::iterator& current, const TFD_LIST::iterator& end, std::list< TFD_LIST >& returned_permutations );

	bool WillTackleeReachGoal( ARugbyCharacter* tacklee ) const;
	bool HighClosureRate( ARugbyCharacter* tackler,  ARugbyCharacter* tacklee ) const;

public:
	// Tackle type strings
	static const char* TACKLE_TYPES[];
	static const char* TACKLE_DOMINANCES[];
	static const char* TACKLE_HEIGHTS[];
	static const char* TACKLE_TRY_PROBABILITIES[];
	static const char* TACKLE_SIDESTEP_TYPES[];

private:
#ifdef DEBUG_DIVE_TACKLE
	bool debug_tackle;
#endif
};

#endif // _RUTACKLEHELPER_H

