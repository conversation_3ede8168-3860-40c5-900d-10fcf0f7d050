/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include <Precompiled.h>

#include "Match/Cutscenes/SSCutSceneManager.h"

//Mab
#include "Mab/MabInclude.h"

//Match
#include "Match/Debug/SIFDebug.h"
#include "Match/SIFGameWorld.h"
#include "Match/Effects/SIFEffectSystem.h"
#include "Match/PlayerProfile/SIFPlayerProfileManager.h"
#include "Match/PlayerProfile/SIFPlayerProfilePropertyDefs.h" 
#include "Match/SIFGameContext.h"


#ifdef ENABLE_SOAK_TEST
#include "Utility/Soak/SIFSoakManager.h"
#include "Utility/Soak/SIFSoakPlayerCreate.h"
#endif

//SS
#include "Match/Camera/SSCameraManager.h"
#include "Match/Camera/SSCameraDirector.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/Roles/Competitors/SSRoleCutScene.h"
#include "Match/Debug/EVDSGameService.h"
#include "Match/SSGameTimer.h"
#include "Match/Ball/SSBall.h"
#include "Match/SSPostEffectsManager.h"
#include "Match/SSSpatialHelper.h"
#include "Match/Input/SSInputManager.h"
#include "Match/SSScreenWipeManager.h"
#include "Match/SSMath.h"
#include "Match/SSRole.h"
#include "Match/SSRoleFactory.h"
#include "Match/SSRoleNull.h"
#include "Match/Components/SSHumanPlayer.h"

//RU
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/Debug/RUGameDebugSettings.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/RUMotionSource.h"
#include "Match/RugbyUnion/Enums/RUPlayerPositionEnum.h"
#include "Rugby/Match/Components/RUPlayerAnimation.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Commentary/RUCommentary.h"
#include "Utility/RURandomNumberGenerator.h"
#include "Match/Debug/RUMemoryTags.h"
#include "Match/RugbyUnion/TutorialMode/RUTutorialManager.h"
#include "Match/RugbyUnion/TutorialMode/RUTutorial.h"
#include "Match/HUD/RU3DHUDManager.h"
#include "Match/HUD/RUHUDUpdaterTraining.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/HUD/RUContextualHelper.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUInputPhaseDecision.h"
#include "Match/RugbyUnion/RUGameMovement.h"
#include "Match/RugbyUnion/RUWeatherManager.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUEmotionEngineManager.h"
#include "Match/RugbyUnion/RUShameAndGloryManager.h"
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "Match/RugbyUnion/CompetitionMode/RUActiveCompetition.h"
#include "Match/RugbyUnion/RUSandboxGame.h"
#include "Match/RugbyUnion/RUStadiumManager.h"
#include "Match/RugbyUnion/RUDatabaseConstants.h"
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Tournament.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerLookAt.h"
#include "Match/Components/RUPlayerState.h"
#include "Match/Cutscenes/SSCutSceneTags.h"

#include "Match/SIFGameObject.h"
#include "Match/Debug/SIFCommonDebugSettings.h"

#include "Mab/Types/MabStringList.h"

#include "Rugby/RugbyGameInstance.h"
#include "Rugby/Match/SIFGameInput.h"

#include "Rugby/Utility/Helpers/SIFGameHelpers.h"
#include "Rugby/Utility/Helpers/SIFUIHelpers.h"

#include "Runtime/LevelSequence/Public/LevelSequencePlayer.h"
#include "Runtime/LevelSequence/Public/LevelSequence.h"
#include "Runtime/MovieScene/Public/MovieSceneSequencePlayer.h"
#include "Runtime/LevelSequence/Public/DefaultLevelSequenceInstanceData.h"
#include "Runtime/LevelSequence/Public/LevelSequenceActor.h"
#include "Runtime/Engine/Classes/Engine/DataTable.h"
#include "Runtime/CoreUObject/Public/UObject/ConstructorHelpers.h"

#include "Runtime/Engine/Classes/Camera/CameraActor.h"
#include "Runtime/Engine/Classes/Camera/CameraComponent.h"
#include "PostProcessingRugby.h"

#include "Databases/SqliteMabDatabase.h"
#include "Databases/SqliteMabStatement.h"
#include "Databases/RUGameDatabaseManager.h"

#include "DataTables/CutsceneDescriptor.h"
#include "DataTables/CutsceneProps.h"
#include "DataTables/CutsceneEffects.h"

#include "GameModes/RugbyGameState.h"
#include "Character/RugbyCharacterAnimInstance.h"
#include "Character/RugbyCharacter.h"
#include "Character/RugbyPlayerController.h"

#include "Runtime/MovieScene/Public/MovieSceneObjectBindingID.h"

#if defined(FANHUB_ENABLED)
#include "Rugby/FanHub/WWRugbyFanHubService.h"
#endif

//#include "Niagara/Public/NiagaraComponent.h"
#include "Particles/ParticleSystemComponent.h"

//audio
#include "Match/Audio/RUCrowdAudioReactionManager.h"
#include "Match/Audio/RUCrowdReactionManager.h"

#if (PLATFORM_WINDOWS || PLATFORM_PS4 || PLATFORM_XBOXONE) && defined (GAMECENTRE_ENABLED)
#include "RUGameCentre.h"
#endif

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
#include "Utility/consoleVars.h"
#include "DrawDebugHelpers.h"
#endif

#include "Utility/SIFSoundConstants.h"
#include "Utility/Helpers/SIFAudioHelpers.h"
#include "Utility/TransformUtility.h"

#include "Engine/StaticMeshActor.h"

//customise player
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "UI/Screens//WWUIScreenCustomisePlayer.h"

#include "Runtime/Engine/Classes/Kismet/KismetMathLibrary.h"
#include "../RugbyUnion/RUDBPlayer.h"
#include "Networking/RUNetworkState.h"


#pragma optimize("",off) // WJS RLC 
//#define CUT_SCENE_TIME_LIMIT 3 //3 seconds
//#define FAST_FORWARD_CUTSCENE 4 //4X speed if enabled

const char* HANDY_CAM_SHAKE = "ruged/cutscenes/handycam_shake_export.xml";
const char* PLAYER_ROTATE_SOUND_EVENT = "event:/ui/customise_player/camera_rotate";

// How many times the same cutscene ui type can repeat before forcing loop.
// Work around soak crash, force current cutscene to loop for ever.
const int NUM_REPEATS_BEFORE_FORCE_LOOP = 10;

const int TIMEOUT_MILLIS = 40 * 1000;

const float CUTSCENE_ALLOW_SKIP_TIME = 1.0f;


// When we select players in the cutscenes, follow this priority order
// Highest is most important
const int8 SelectorPriority[]
{
	0, //SHIRTNO = 0,
	0, //BALLHOLDER,
	0, //KICKER,
	0, //LASTSCORER,
	0, //NEAREST,
	0, //TEAMIDX,
	0, //FOCUS_PLAYER,
	0, //GOING_ON_PLAYER,
	0, //GOING_OFF_PLAYER,
	0, //UI_ID,
	9, //CAPTAIN,
	0, //SHIRTNO_ORMAORI,
	10, //HAKA_LEADER,
	0, //INJURED
};

///-------------------------------------------------------------------------------
/// Very important table - mapped to CS_EVENTS
///-------------------------------------------------------------------------------

typedef struct {
	const char* directory;				// Directory to load cutscene from.
	const char* tag_name;				// For debug.
	bool R7Variation;				// is there a special variation for Rugby Sevens?
} TagInfo;

const TagInfo cs_info[] =
{
	{ "",									"UNUSED"							, false} ,	//CSEVENT_UNUSED
	{ "",									"NAMED_CUTSCENE"					, false} ,	//CSEVENT_NAMED_CUTSCENE

	{ "ce_ui_mainmenu",						"UI_MAIN_MENU"						, false} ,	//CSEVENT_UI_MAIN_MENU
	{ "ce_ui_teamselect",					"UI_QUICKMATCH_TEAMSELECT"			, false} ,	//CSEVENT_UI_QUICKMATCH_TEAMSELECT
	{ "ce_ui_controllerselect",				"UI_QUICKMATCH_CONTROLLER_SELECT"	, true} ,	//CSEVENT_UI_QUICKMATCH_CONTROLLER_SELECT
	{ "ce_ui_customiseplayer_body",			"UI_CUSTOMIZE_PLAYER_BODY"			, false} ,	//CSEVENT_UI_CUSTOMIZE_PLAYER_BODY
	{ "ce_ui_customiseplayer_head",			"UI_CUSTOMIZE_PLAYER_HEAD"			, false} ,	//CSEVENT_UI_CUSTOMIZE_PLAYER_HEAD
	{ "ce_ui_competitionselect",			"UI_COMPETITION_SELECT"				, false} ,	//CSEVENT_UI_COMPETITION_SELECT
	{ "ce_ui_comphub",						"UI_COMPETITION_HUB"				, false} ,	//CSEVENT_UI_COMPETITION_HUB
	{ "ce_ui_teamprofile",					"UI_COMPETITION_TEAM_PROFILE"		, false} ,	//CSEVENT_UI_COMPETITION_TEAM_PROFILE
	{ "ce_ui_customiseteam",				"UI_CUSTOMIZE_TEAM"					, false} ,	//CSEVENT_UI_CUSTOMIZE_TEAM
	{ "ce_ui_listteams",					"UI_LIST_TEAMS"						, false} ,	//CSEVENT_UI_LIST_TEAMS

	{ "ce_ui_comp_wcup",					"UI_TROPHY_WORLDCUP"				, false} ,	//CSEVENT_UI_TROPHY_WORLDCUP
	{ "ce_ui_comp_aviva",					"UI_TROPHY_AVIVA"					, false} ,	//CSEVENT_UI_TROPHY_AVIVA
	{ "ce_ui_comp_bled",					"UI_TROPHY_BLED"					, false} ,	//CSEVENT_UI_TROPHY_BLED
	{ "ce_ui_comp_itm",						"UI_TROPHY_ITM"						, false} ,	//CSEVENT_UI_TROPHY_ITM
	{ "ce_ui_comp_ranf",					"UI_TROPHY_RANF"					, false} ,	//CSEVENT_UI_TROPHY_RANF
	{ "ce_ui_comp_gencup1",					"UI_TROPHY_GENCUP1"					, false} ,	//CSEVENT_UI_TROPHY_GENCUP1
	{ "ce_ui_comp_gencup2",					"UI_TROPHY_GENCUP2"					, false} ,	//CSEVENT_UI_TROPHY_GENCUP2
	{ "ce_ui_comp_genplate",				"UI_TROPHY_GENPLATE"				, false} ,	//CSEVENT_UI_TROPHY_GENPLATE
	{ "ce_ui_comp_genshield",				"UI_TROPHY_GENSHIELD"				, false} ,	//CSEVENT_UI_TROPHY_GENSHIELD
	{ "ce_ui_comp_gentrophy",				"UI_TROPHY_GENTROPHY"				, false} ,	//CSEVENT_UI_TROPHY_GENTROPHY
	{ "ce_ui_comp_genbowl",					"UI_TROPHY_GENBOWL"					, false} ,	//CSEVENT_UI_TROPHY_GENBOWL
	{ "ce_ui_comp_nrc",						"UI_TROPHY_NRC"						, false} ,	//CSEVENT_UI_TROPHY_NRC
	{ "ce_ui_comp_acc",						"UI_TROPHY_ACC"						, false} ,	//CSEVENT_UI_TROPHY_ACC
	{ "ce_ui_comp_rabo",					"UI_TROPHY_PRO12"					, false} ,	//CSEVENT_UI_TROPHY_PRO12
	{ "ce_ui_comp_pro",						"UI_TROPHY_PROD2"					, false} ,	//CSEVENT_UI_TROPHY_PROD2
	{ "ce_ui_comp_lions",					"UI_TROPHY_LIONS"					, false} ,	//CSEVENT_UI_TROPHY_LIONS
	{ "ce_ui_comptop14",					"UI_TROPHY_TOP14"					, false} ,	//CSEVENT_UI_TROPHY_TOP14
	{ "ce_ui_comp_gencup3",					"UI_TROPHY_GENCUP3"					, false} ,	//CSEVENT_UI_TROPHY_GENCUP3
	{ "ce_ui_comp_rug",						"UI_TROPHY_SUPER15"					, false} ,	//CSEVENT_UI_TROPHY_SUPER15

	{ "ce_gp_trycelebration",				"TRY_CELEBRATION"					, false} ,	//CSEVENT_TRY_CELEBRATION
	{ "ce_gp_trycommiseration",				"TRY_COMMISERATION"					, false} ,	//CSEVENT_TRY_COMMISERATION
	{ "ce_gp_trycelebrationbg",				"TRY_CELEBRATION_BG"				, false} ,	//CSEVENT_TRY_CELEBRATION_BG
	{ "ce_gp_trycommiserationbg",			"TRY_COMMISERATION_BG"				, false} ,	//CSEVENT_TRY_COMMISERATION_BG

	{ "ce_gp_tryreac",						"TRY_REACTION"						, false} ,	//CSEVENT_TRY_REACTION

	{ "ce_gp_tmo",							"TRY_TMO"							, false} ,	//CSEVENT_TRY_TMO
	{ "ce_gp_trycommiseration",				"TRY_TMO_DISALLOWED"				, false} ,	//CSEVENT_TRY_DISALLOWED

	{ "ce_pg_walkonteam01",					"PREGAME_TEAM1_WALKON"				, true} ,	//CSEVENT_PREGAME_TEAM1_WALKON
	{ "ce_pg_walkonteam02",					"PREGAME_TEAM2_WALKON"				, true} ,	//CSEVENT_PREGAME_TEAM2_WALKON
	{ "ce_pg_ofwalkonteam01",				"PREGAME_TEAM1_ONFIELD_WALKON"		, true} ,	//CSEVENT_PREGAME_TEAM1_ONFIELD_WALKON
	{ "ce_pg_ofwalkonteam02",				"PREGAME_TEAM2_ONFIELD_WALKON"		, true} ,	//CSEVENT_PREGAME_TEAM2_ONFIELD_WALKON
	{ "ce_pg_ofrostadiumpans",				"PREGAME_STADIUM_PAN"				, true} ,	//CSEVENT_PREGAME_STADIUM_PAN

	{ "ce_pg_nationalanthem",				"NATIONAL_ANTHEM"					, true} ,	//CSEVENT_PREGAME_TEAM_NATIONAL_ANTHEM

	{ "ce_gp_genreac_pos",					"GENERIC_REACTION_POSITIVE"			, false} ,	//CSEVENT_GENERIC_REACTION_POSITIVE,
	{ "ce_gp_genreac_neg",					"GENERIC_REACTION_NEGATIVE"			, false} ,	//CSEVENT_GENERIC_REACTION_NEGATIVE,
	{ "ce_gp_genreac_posbg",				"GENERIC_REACTION_POSITIVE_BG"		, true} ,	//CSEVENT_GENERIC_REACTION_POSITIVE_BG,
	{ "ce_gp_genreac_negbg",				"GENERIC_REACTION_NEGATIVE_BG"		, true} ,	//CSEVENT_GENERIC_REACTION_NEGATIVE_BG,

	{ "ce_gp_ballout_pos",					"BALLOUT_POSITIVE"					, false} ,	//CSEVENT_BALLOUT_POSITIVE,
	{ "ce_gp_ballout_neg",					"BALLOUT_NEGATIVE"					, false} ,	//CSEVENT_BALLOUT_NEGATIVE,

	{ "ce_pg_stadiumpans_big",				"PREGAME_TEAM_SELECT"				, true} ,	//CSEVENT_PREGAME_TEAM_SELECT
	{ "ce_pg_stadiumpans_small",			"PREGAME_TEAM_SELECT_SMALL"			, false} ,	//CSEVENT_PREGAME_TEAM_SELECT_SMALL

	{ "ce_ngp_stadiumpan_general_big",		"HALFTIME_PAN"						, false} ,	//CSEVENT_HALFTIME_PAN
	{ "ce_ngp_stadiumpan_general_small",	"HALFTIME_PAN_SMALL"				, false} ,	//CSEVENT_HALFTIME_PAN_SMALL

	{ "ce_gp_prekickforpoints",				"PRE_KICKFORPOINTS"					, true} ,	//CSEVENT_PRE_KICKFORPOINTS
	{ "ce_gp_kfpsuccess",					"KICKFORPOINTS_SUCCESS"				, true} ,	//CSEVENT_KICKFORPOINTS_SUCCESS
	{ "ce_gp_kfpfail",						"KICKFORPOINTS_FAIL"			    , true} ,	//CSEVENT_KICKFORPOINTS_FAIL
	{ "ce_gp_dropkck_success",				"DROPGOAL_SUCCESS"					, false} ,	//CSEVENT_DROPGOAL_SUCCESS
	{ "ce_gp_kfpbg",						"KICKFORPOINTS_BG"					, true} ,	//CSEVENT_KICKFORPOINTS_BG

	{ "ce_gp_prekickoff",					"PRE_KICKOFF"						, false} ,	//CSEVENT_PRE_KICKOFF
	{ "ce_gp_prekickoff",					"PRE_DROPOUT"						, false} ,	//CSEVENT_PRE_DROPOUT

	{ "ce_gp_refsig_penalty",				"REFEREE_PENALTY"					, false} ,	//CSEVENT_REFEREE_PENALTY
	{ "ce_gp_refsig_scrum",					"REFEREE_SCRUM"						, false} ,	//CSEVENT_REFEREE_SCRUM

	{ "ce_gp_sentoff_red",					"RED_CARD"							, false} ,	//CSEVENT_RED_CARD,
	{ "ce_gp_sentoff_yellow",				"YELLOW_CARD"						, false} ,	//CSEVENT_YELLOW_CARD,
	{ "ce_gp_sentoff_2cards",				"DOUBLE_YELLOW_CARD"				, false} ,	//CSEVENT_DOUBLE_YELLOW_CARD,
	{ "ce_gp_tactsub_a",					"TACTICAL_SUBSTITUTION_"			, false} ,	//CSEVENT_TACTICAL_SUBSTITUTION_A,
	{ "ce_gp_tactsub_b",					"TACTICAL_SUBSTITUTION_B"			, false} ,	//CSEVENT_TACTICAL_SUBSTITUTION_B,
	{ "ce_gp_injbtm",						"INJURY_SUB_A_BTM"					, false} ,	//CSEVENT_INJURY_SUBSTITUTION_A_BTM,
	{ "ce_gp_injmid",						"INJURY_SUB_A_MID"					, false} ,	//CSEVENT_INJURY_SUBSTITUTION_A_MID,
	{ "ce_gp_injtop",						"INJURY_SUB_A_TOP"					, false} ,	//CSEVENT_INJURY_SUBSTITUTION_A_TOP,
	{ "ce_gp_injsubbtm",					"INJURY_SUB_B_BTM"					, false} ,	//CSEVENT_INJURY_SUBSTITUTION_B_BTM,
	{ "ce_gp_injsubmid",					"INJURY_SUB_B_MID"					, false} ,	//CSEVENT_INJURY_SUBSTITUTION_B_MID,
	{ "ce_gp_injsubtop",					"INJURY_SUB_B_TOP"					, false} ,	//CSEVENT_INJURY_SUBSTITUTION_B_TOP,
	{ "ce_gp_injsub",						"INJURY_SUB_C"						, false} ,	//CSEVENT_INJURY_SUBSTITUTION_C,

	{ "ce_gp_sentoff_return",				"SINBIN_RETURN"						, false} ,	//CSEVENT_SINBIN_RETURN,

	{ "ce_ngp_teamleavingfield",			"HALFTIME_WALKOFF"					, false} ,	//CSEVENT_HALFTIME_WALKOFF,

	{ "ce_eg_endgamecelebration",			"ENDGAME_WIN"						, false} ,	//CSEVENT_ENDGAME_WIN
	{ "ce_eg_endgamecommiseration",			"ENDGAME_LOSE"						, false} ,	//CSEVENT_ENDGAME_LOSE	   
	{ "ce_eg_endgamecelebrations_b",		"ENDGAME_B"							, false} ,	//CSEVENT_ENDGAME_B
	{ "ce_eg_endgamecelebrations_c",		"ENDGAME_C"							, false} ,	//CSEVENT_ENDGAME_C
	{ "ce_eg_endgamedraw",					"ENDGAME_DRAW"						, false} ,	//CSEVENT_ENDGAME_DRAW
	{ "ce_eg_endgamebackground",			"ENDGAME_BG"						, false} , //CSEVENT_ENDGAME_BACKGROUND,

	{ "ce_eg_extratimecointoss",			"ENDGAME_COINTOSS"					, false} , //CSEVENT_ENDGAME_COINTOSS,
	{ "ce_eg_extratimecointosspostwin",		"ENDGAME_COINTOSS_POST_WIN"			, false} , //CSEVENT_ENDGAME_COINTOSS_POST_WIN,
	{ "ce_eg_extratimecointosspostdec1",	"ENDGAME_COINTOSS_POST_DECISION1"	, false} , //CSEVENT_ENDGAME_COINTOSS_POST_DECISION1,
	{ "ce_eg_extratimecointosspostdec2",	"ENDGAME_COINTOSS_POST_DECISION2"	, false} , //CSEVENT_ENDGAME_COINTOSS_POST_DECISION2,

	{ "ce_eg_extratime",					"ENDGAME_EXTRA_TIME"				, false} ,	//CSEVENT_ENDGAME_EXTRA_TIME

	{ "ce_eg_trophywincptn_wcup",			"TROPHY_WIN_WORLDCUP"				, false} ,	//CSEVENT_TROPHY_WIN_WORLDCUP
	{ "ce_eg_trophywincptn_aviva",			"TROPHY_WIN_AVIVA"					, false} ,	//CSEVENT_TROPHY_WIN_AVIVA
	{ "ce_eg_trophywincptn_bled",			"TROPHY_WIN_BLED"					, false} ,	//CSEVENT_TROPHY_WIN_BLED
	{ "ce_eg_trophywincptn_itm",			"TROPHY_WIN_ITM"					, false} ,	//CSEVENT_TROPHY_WIN_ITM
	{ "ce_eg_trophywincptn_ranf",			"TROPHY_WIN_RANF"					, false} ,	//CSEVENT_TROPHY_WIN_RANF
	{ "ce_eg_trophywincptn_gencup1",		"TROPHY_WIN_GENCUP1"				, false} ,	//CSEVENT_TROPHY_WIN_GENCUP1
	{ "ce_eg_trophywincptn_gencup2",		"TROPHY_WIN_GENCUP2"				, false} ,	//CSEVENT_TROPHY_WIN_GENCUP2
	{ "ce_eg_trophywincptn_genplate",		"TROPHY_WIN_GENPLATE"				, false} ,	//CSEVENT_TROPHY_WIN_GENPLATE
	{ "ce_eg_trophywincptn_genshield",		"TROPHY_WIN_GENSHIELD"				, false} ,	//CSEVENT_TROPHY_WIN_GENSHIELD
	{ "ce_eg_trophywincptn_gentrophy",		"TROPHY_WIN_GENTROPHY"				, false} ,	//CSEVENT_TROPHY_WIN_GENTROPHY
	{ "ce_eg_trophywincptn_genbowl",		"TROPHY_WIN_GENBOWL"				, false} ,	//CSEVENT_TROPHY_WIN_GENBOWL
	{ "ce_eg_trophywincptn_nrc",			"TROPHY_WIN_NRC"					, false} ,	//CSEVENT_TROPHY_WIN_NRC
	{ "ce_eg_trophywincptn_acc",			"TROPHY_WIN_ACC"					, false} ,	//CSEVENT_TROPHY_WIN_ACC
	{ "ce_eg_trophywincptn_rabo",			"TROPHY_WIN_PRO12"					, false} ,	//CSEVENT_TROPHY_WIN_PRO12
	{ "ce_eg_trophywincptn_pro",			"TROPHY_WIN_PROD2"					, false} ,	//CSEVENT_TROPHY_WIN_PROD2
	{ "ce_eg_trophywincptn_lions",			"TROPHY_WIN_LIONS"					, false} ,	//CSEVENT_TROPHY_WIN_LIONS
	{ "ce_eg_trophywincptn_top14",			"TROPHY_WIN_GENTROPHY"				, false} ,	//CSEVENT_TROPHY_WIN_TOP14
	{ "ce_eg_trophywincptn_gencup3",		"TROPHY_WIN_GENCUP3"				, false} ,	//CSEVENT_TROPHY_WIN_GENCUP3
	{ "ce_eg_trophywincptn_rug",			"TROPHY_WIN_SUPER15"				, false} ,	//CSEVENT_TROPHY_WIN_SUPER15

	{ "ce_eg_trophyloss",					"TROPHY_LOSE_A"						, false} ,	//CSEVENT_TROPHY_LOSE_A
	{ "ce_eg_trophywinteam",				"TROPHY_B"							, false} ,	//CSEVENT_TROPHY_B

	{ "ce_tut_win",							"TUTORIAL_WIN"						, false} ,	//CSEVENT_TUTORIAL_WIN
	{ "ce_tut_loss",						"TUTORIAL_LOSE"						, false} ,	//CSEVENT_TUTORIAL_LOSE

	{ "ce_pg_haka",							"PREGAME_HAKA"						, true} ,	//CSEVENT_PREGAME_HAKA

	{ "ce_ui_stadiumpans_tut",				"TUTORIAL_PAN"						, false} ,	//CSEVENT_TUTORIAL_PAN
	{ "ce_ui_stadiumpans",					"UI_PAN"							, false} ,	//CSEVENT_UI_PAN

	{ "ce_gp_tryfireworks",					"FWKS_TRY"							, false} ,	//CSEVENT_FIREWORKS_TRY,
	{ "ce_gp_tryfireworks_ingame",			"FWKS_PRE_TRY"						, false} ,	//CSEVENT_FIREWORKS_PRE_TRY,
	{ "ce_eg_trophyfx_confetti",			"FWKS_TROPHY_WIN_CONFETTI"			, false} ,	//CSEVENT_FIREWORKS_TROPHY_WIN_CONFETTI,
	{ "ce_eg_trophyfx_ground",				"FWKS_TROPHY_WIN_GROUND"			, false} ,	//CSEVENT_FIREWORKS_TROPHY_WIN_GROUND,
	{ "ce_eg_trophyfx_sky",					"FWKS_TROPHY_WIN_SKY"				, false} ,	//CSEVENT_FIREWORKS_TROPHY_WIN_SKY,
	{ "ce_pg_runonfx",						"FWKS_PREGAME"						, false} ,	//CSEVENT_FIREWORKS_PREGAME,

	{ "ce_ui_mainmenu_quickload",			"UI_MAIN_MENU_QUICKLOAD"			, false} ,	//CSEVENT_UI_MAIN_MENU_QUICKLOAD

	{ "ce_ui_endgamecomp_loss",				"UI_ENDGAME_COMP_LOSS"				, false} ,  //CSEVENT_UI_COMPETITION_HUB_LOSS
	{ "ce_ui_endgamecomp_win",				"UI_ENDGAME_COMP_WIN"				, false} ,  //CSEVENT_UI_COMPETITION_HUB_WIN

	{ "ce_ui_title",						"UI_TITLE"							, false} ,	//CSEVENT_UI_TITLE
	{ "ce_gp_genreac_pos",					"MARK_AWARDED"						, false}	//CSEVENT_MARK_AWARDED
};


#define MAX_EMOTION_LEVEL	9
#define MIN_EMOTION_LEVEL	1
#define DEFAULT_EMOTION_LEVEL	5

/// Bit flags for DebugGetInfo: state_flags.
enum
{
	CSDB_RUNNING = 0x1,
	CSDB_DISABLE_SIM = 0x2,
	CSDB_INTERUPTABLE = 0x4,
	CSDB_LOADING = 0x8
};

static const size_t MAX_CUTSCENE_ELEMENTS = 32;
static const char* CUTSCENE_READY_TAG = "cs_sync_ready";
static const int LAST_FILENAME_CACHE_SIZE = 64;
static const float HAKA_COMMENTARY_DELAY = 0.25f;
static const float MAX_HAKA_COMMENTARY_DELAY = 10.0f;
static const float NO_CUTSCENE_SKIP_TIME = 0.3f;

static TAutoConsoleVariable<int32> CVarShowWomenInCutscenes(
	TEXT("ww.rugby.ShowWomenInCutscenes"),
	0,
	TEXT("Toggles the override that forces women to show up in the cutscene"));

///-------------------------------------------------------------------------------
/// SSCutSceneManager: Constructor
///-------------------------------------------------------------------------------

SSCutSceneManager::SSCutSceneManager(SIFGameWorld* ggame)
	:
	cutscene_elements()
	, last_cutscene_element(0)
	, cs_uid(0)
	, last_created_element(NULL)
	, ui_cutscene_state(0)
	, requested_ui_cutscene_state()
	, ui_cutscene_faded_actions()
	, current_ui_faded_action(0)
	, custom_player_db_id(0)
	, custom_player_strip_id(0)
	, custom_player_team_id(-1)
	, camera_blends()
	, game(ggame)
	, should_skip(false)
	, skip_requested(false)
	, force_finish_current_cutscene(false)
	, force_finish_current_cutscene_skip(false)
	, local_disable_skip(false)
	, disable_skip_count(0)
	, doing_ui_transition(false)
	, ui_transition_target()
	, cinematics_enabled(true)
	, replays_enabled(true)
	, cutscene_profile_idx(-1)
	, replay_profile_idx(-1)
	, request_element_finish(false)
	, request_stop_all_cutscenes(false)
	, stop_rendering_count(0)
	, wipe_on_cut(false)
	, enable_placeholder_cutscenes(false)
	, exclusion_center(FVector::ZeroVector)
	, exclusion_zone_on(false)
	, exclusion_range(0.0f)
	, disable_visibility_update(false)
	, player_creator_target_offset(FVector::ZeroVector)
	, player_creator_offset(FVector::ZeroVector)
	, player_creator_camera_target_angle(FRotator(0.0f, 0.0f, 0.0f))
	, player_creator_camera_angle(FRotator(0.0f, 0.0f, 0.0f))
	, player_creator_on(false)
	, player_creator_transform(FVector(0.0f))
	, player_creator_transform_notrans(MabMatrix::IDENTITY)
	, player_creator_rotate_sound(NULL)
	, player_creator_rotate_sound_timer(0.0f)
	, async_wait_state(AST_DONE)
	, substitute_loading_state(AST_DONE)
	, received_substitute_ready_signal(false)
	, celebrating_team(NULL)
	, interchange_type(0)
	, going_off_player(NULL)
	, going_on_player(NULL)
	, injured_player(NULL)
	, advantage_replay(NULL)
	, advantage_inaffect(false)
	, advantage_end_countdown(0)
	, advantage_last_started_time(-1.0f)
	, disable_substitutions(false)
	, next_half(FIRST_HALF)
	, next_extratime_mode(NOT_EXTRA_TIME)
	, will_do_haka(false)
	, playing_haka(false)
	, enable_restart_game_option(true)
	, haka_commentary_delay(0.0f)
	, total_haka_comentary_delay(0.0f)
	, haka_skip_disable_time(0.0f)
	, game_phase_override(RUGamePhase::PLAY)
	, competition_ui_trophy_id(CUPIDX_WORLD)
	, match_emotion_level(0)
	, last_debug_key_used(DEBUG_CUTSCENE_START_KEY)
	, do_team_switch(false)
	//, do_team_switch_generic(false)
	//, do_comp_team_switch(false)
	, load_non_permanent_players(false)
	, replay_finished(false)
	, injury_hud_selected_player(-1)
	, multi_skip(false)
	, boundry_check_enabled(true)
	, next_frame_lock(0)
	, should_be_in_main_menu_hack(false)
	, bg_cutscenes_enabled(true)
	, have_queued_ball_dead_cutscene(false)
	, cutscene_hud_disabled(false)
	, ui_repeat_cutscene_count(0)
#ifdef ENABLE_GAME_DEBUG_MENU
	, debug_key_pool(NULL)
#endif
	, pro_player_sent_off_during_interchanges(false)
	, suspended_callback_from_pro_send_off(NULL)
	, suspended_callback_from_pro_send_off_debug_name("")
	, showSimulationBeforeFullTime(false)
	, running_cinematic(false) //not used...
	//, last_filename("")
{


	// Register event handlers...

	//RegisterEventHandlers();

	// Setup event listeners...

	RUGameEvents* game_events = game->GetEvents();
	MABASSERT(game_events != NULL);

	game_events->try_result.Add(this, &SSCutSceneManager::LaunchPreTryCutscene);
	game_events->try_awarded.Add(this, &SSCutSceneManager::LaunchTryCutscene);  // Try scored
	game_events->video_referee_deliberation.Add(this, &SSCutSceneManager::LaunchTMOCutscene);  // TMO
	game_events->conversion_finish.Add(this, &SSCutSceneManager::ConversionCutSceneBegin);
	game_events->penalty_goal_finish.Add(this, &SSCutSceneManager::PenaltyCutSceneBegin); // penalty finished play cutscene
	game_events->kick_restart_start.Add(this, &SSCutSceneManager::KickoffCutSceneBegin);
	//game_events->kick.Add( this, &SSCutSceneManager::KickoffCutScenefinish);//not needed. Blank function in RC3
	game_events->replay_playback_finish.Add(this, &SSCutSceneManager::ReplayFinished);
	game_events->penalty_detected.Add(this, &SSCutSceneManager::LaunchPenaltyCutscene); // Penalty awarded
	game_events->mark_awarded.Add(this, &SSCutSceneManager::LaunchMarkAwardedCutscene); // Mark awarded
	game_events->fourtytwenty_kick_awarded.Add(this, &SSCutSceneManager::LaunchFourtyTwentyKickAwardedCutscene); // 4020 awarded
	game_events->restart_out_on_full.Add(this, &SSCutSceneManager::LaunchRestartOutOnFullCutscene); // Restart out on full
	game_events->restart_kickoff_into_ingoal.Add(this, &SSCutSceneManager::LaunchRestartKickOffIntoGoalCutscene); // Restart kick into goal
	game_events->rule_trigger_ball_dead.Add(this, &SSCutSceneManager::LaunchBallDeadCutscene); // Ball dead
	game_events->maul_heldup.Add(this, &SSCutSceneManager::LaunchMaulHeldUpCutscene);
	game_events->knock_on_awarded.Add(this, &SSCutSceneManager::LaunchKnockonCutscene);
	game_events->cutscene_half_time.Add(this, &SSCutSceneManager::StartHalfTimeWalkOff);
	game_events->cutscene_full_time.Add(this, &SSCutSceneManager::StartFullTimeCutScene);
	game_events->cutscene_extra_time.Add(this, &SSCutSceneManager::StartFullTimeCutScene);
	game_events->cutscene_simulation.Add(this, &SSCutSceneManager::StartSimulationCutScene);
	game_events->lineout_signalled.Add(this, &SSCutSceneManager::LineoutSignalled);
	game_events->touch_scrum_signalled.Add(this, &SSCutSceneManager::TouchScrumSignalled);
	game_events->cutscene_injury.Add(this, &SSCutSceneManager::StartInjuryCutScene);
	game_events->cutscene_kickforpoints.Add(this, &SSCutSceneManager::StartKickForPointsCutscene);
	game_events->drop_goal_detected.Add(this, &SSCutSceneManager::DropGoalSuccessCutsceneBegin);
	game_events->cutscene_dropout.Add(this, &SSCutSceneManager::DropOutCutsceneBegin);
	game_events->cutscene_forward_pass.Add(this, &SSCutSceneManager::ForwardPassCutsceneBegin);
	game_events->advantage_started.Add(this, &SSCutSceneManager::AdvantageStart);
	game_events->cutscene_pro_sent_off.Add(this, &SSCutSceneManager::StartProSentOffCutScene);
	game_events->restart_kickoff_out_on_the_full.Add(this, &SSCutSceneManager::LaunchFreeKickOutOnFullCutscene); // Restart out on full
	game_events->restart_kickoff_not_10m.Add(this, &SSCutSceneManager::LaunchFreeKickCollected10mCutscene); // Restart not 10m
	game_events->restart_kickoff_offside.Add(this, &SSCutSceneManager::LaunchFreeKickOffsideKickOffCutscene); // Restart offside kick off

	game_events->golden_point_time.Add(this, &SSCutSceneManager::SetupGoldenPoint); // Golden Point

#ifdef ENABLE_SEVENS_MODE
	// Nick  WWS 7s to Womens //if (game->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_SEVENS)
	//{
	//	game_events->cutscene_extra_time_coin_toss_won.Add(this, &SSCutSceneManager::OnCoinTossWonLost);
	//	game_events->cutscene_extra_time_coin_toss_decide_to_kick.Add(this, &SSCutSceneManager::OnCoinTossDecideToKick);
	//	game_events->cutscene_extra_time_coin_toss_decide_direction.Add(this, &SSCutSceneManager::OnCoinTossDecideDirection);
	//	game_events->cutscene_extra_time_coin_toss_finished.Add(this, &SSCutSceneManager::OnCoinTossFinished);
	//}
#endif

	//game_events->advantage_ended.Add( this, &SSCutSceneManager::AdvantageEnd ); //commented in RC3

	/// Clear focus_players.
	for (int i = 0; i <= SIDE_NONE; i++)
	{
		focus_player[i] = NULL;
	}

	/// Reset ui-cutscene handler.
	ResetUICutscene();

	last_filename_cache.Empty();
	last_filename_cache.Reserve((int32)NUM_CSEVENTS);
	FString FileName = "";
	for (int i = 0; i < (int)NUM_CSEVENTS; ++i)
	{
		last_filename_cache.Add(i, FileName);
	}

	//last_filename_cache = (char*) MabMemCalloc(LAST_FILENAME_CACHE_SIZE,NUM_CSEVENTS, heap); //not needed

	//commented in RC3
	//SqliteMabStatement cutscenes_data;
	//SIFApplication::GetApplication()->GetRuDbManager()->PrepareStatement(RU_DB_CUTSCENES, cutscenes_data, "SELECT filename, emotion_level FROM rucs_cutscenes WHERE event_type=%d",1);
	//while(cutscenes_data.Next())
	//{
	//	MABLOGDEBUG("################ %s", cutscenes_data.GetColumnAsChar(0));
	//}

#ifdef ENABLE_RUGED
	edit_attack_disabled = SIFDebug::GetGameDebugSettings()->GetAttackDisabled();
	edit_defence_disabled = SIFDebug::GetGameDebugSettings()->GetDefenceDisabled();
	edit_ruged_debug_disabled = SIFDebug::GetGameDebugSettings()->GetRugEdDebugDisabled();
#endif

	if (SIFPlayerProfileManager::GetInstance() && SIFPlayerProfileManager::GetInstance()->GetMasterProfile())
	{
		const MabObservedValueList* value_list = NULL;
		value_list = SIFPlayerProfileManager::GetInstance()->GetMasterProfile()->GetNamedValueList();

		cutscene_profile_idx = value_list->GetNamedValueIndex(PLAYER_PROFILE_CUTSCENE);
		replay_profile_idx = value_list->GetNamedValueIndex(PLAYER_PROFILE_REPLAY);
	}

	UpdateProfileSettings();

	CalculateMatchEmotionLevel();

	//InitialiseCutScenePropsDataTable(); //we dont need this code, as props are now added to sequencers directly
	InitialiseCutSceneDB();
	InitialiseCutSceneEffectsDataTable();

	m_UCutsceneManager = Cast<UCutSceneManager>(UGameplayStatics::SpawnObject(UCutSceneManager::StaticClass(), ggame->GetGameInstance().GetWorld()));
	m_UCutsceneManager->Initialise(ggame->GetGameInstance().GetWorld(), this);

	ggame->GetGameInstance().PreventObjectGC(m_UCutsceneManager);

	//m_PausedSequence.Empty();

	// Determine whether we should have cutscenes on in a network game.
	if (game->GetGameSettings().game_settings.network_game)
	{
		cinematics_enabled = game->GetGameSettings().game_settings.network_cutscenes;
		replays_enabled = game->GetGameSettings().game_settings.network_replays;

#if DISABLE_NETWORKED_CUTSCENES_REPLAYS
		cinematics_enabled = false;
		replays_enabled = false;
#endif
	}
	/*
	#ifdef ENABLE_GAME_DEBUG_MENU
		SIFDebugDrawHandleManager* handle_mgr = SIFDebug::GetDebugDrawHandleManager();
		debug_key_pool = handle_mgr->AllocatePool(200);				// cleared each frame
		debug_key_pool->AddPrimitives( SIFDD_PATH );
	#endif*/

}


///-------------------------------------------------------------------------------
/// SSCutSceneManager: Destructor
///-------------------------------------------------------------------------------

SSCutSceneManager::~SSCutSceneManager()
{
	//editing = false;
	running_cinematic = false;

	// Remove event listeners.

	RUGameEvents* game_events = game->GetEvents();
	MABASSERT(game_events != NULL);

	game_events->try_result.Remove(this, &SSCutSceneManager::LaunchPreTryCutscene);
	game_events->try_awarded.Remove(this, &SSCutSceneManager::LaunchTryCutscene);  // Try scored
	game_events->video_referee_deliberation.Remove(this, &SSCutSceneManager::LaunchTMOCutscene);  // TMO
	game_events->conversion_finish.Remove(this, &SSCutSceneManager::ConversionCutSceneBegin);
	game_events->penalty_goal_finish.Remove(this, &SSCutSceneManager::PenaltyCutSceneBegin);
	game_events->kick_restart_start.Remove(this, &SSCutSceneManager::KickoffCutSceneBegin);
	//game_events->kick.Remove( this, &SSCutSceneManager::KickoffCutScenefinish);
	game_events->replay_playback_finish.Remove(this, &SSCutSceneManager::ReplayFinished);
	game_events->penalty_detected.Remove(this, &SSCutSceneManager::LaunchPenaltyCutscene); // Penalty awarded
	game_events->mark_awarded.Remove(this, &SSCutSceneManager::LaunchMarkAwardedCutscene); // Mark awarded
	game_events->fourtytwenty_kick_awarded.Remove(this, &SSCutSceneManager::LaunchFourtyTwentyKickAwardedCutscene); // 4020 kick award
	game_events->restart_out_on_full.Remove(this, &SSCutSceneManager::LaunchRestartOutOnFullCutscene); // Restart out on full
	game_events->restart_kickoff_into_ingoal.Remove(this, &SSCutSceneManager::LaunchRestartKickOffIntoGoalCutscene); // Restart kick into goal
	game_events->rule_trigger_ball_dead.Remove(this, &SSCutSceneManager::LaunchBallDeadCutscene); // Ball dead
	game_events->maul_heldup.Remove(this, &SSCutSceneManager::LaunchMaulHeldUpCutscene);
	game_events->knock_on_awarded.Remove(this, &SSCutSceneManager::LaunchKnockonCutscene);
	game_events->cutscene_half_time.Remove(this, &SSCutSceneManager::StartHalfTimeWalkOff);
	game_events->cutscene_full_time.Remove(this, &SSCutSceneManager::StartFullTimeCutScene);
	game_events->cutscene_extra_time.Remove(this, &SSCutSceneManager::StartFullTimeCutScene);
	game_events->cutscene_simulation.Remove(this, &SSCutSceneManager::StartSimulationCutScene);
	game_events->lineout_signalled.Remove(this, &SSCutSceneManager::LineoutSignalled);
	game_events->cutscene_injury.Remove(this, &SSCutSceneManager::StartInjuryCutScene);
	game_events->cutscene_kickforpoints.Remove(this, &SSCutSceneManager::StartKickForPointsCutscene);
	game_events->drop_goal_detected.Remove(this, &SSCutSceneManager::DropGoalSuccessCutsceneBegin);
	game_events->cutscene_dropout.Remove(this, &SSCutSceneManager::DropOutCutsceneBegin);
	game_events->cutscene_forward_pass.Remove(this, &SSCutSceneManager::ForwardPassCutsceneBegin);
	game_events->advantage_started.Remove(this, &SSCutSceneManager::AdvantageStart);
	//game_events->advantage_ended.Remove( this, &SSCutSceneManager::AdvantageEnd );
	game_events->cutscene_pro_sent_off.Remove(this, &SSCutSceneManager::StartProSentOffCutScene);
	game_events->restart_kickoff_out_on_the_full.Remove(this, &SSCutSceneManager::LaunchFreeKickOutOnFullCutscene); // Restart out on full
	game_events->restart_kickoff_not_10m.Remove(this, &SSCutSceneManager::LaunchFreeKickCollected10mCutscene); // Restart not 10m
	game_events->restart_kickoff_offside.Remove(this, &SSCutSceneManager::LaunchFreeKickOffsideKickOffCutscene); // Restart offside kick off

	game_events->golden_point_time.Remove(this, &SSCutSceneManager::SetupGoldenPoint); // Golden Point

#ifdef ENABLE_SEVENS_MODE
	// Nick  WWS 7s to Womens //if (game->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_SEVENS)
	//{
	//	game_events->cutscene_extra_time_coin_toss_won.Remove(this, &SSCutSceneManager::OnCoinTossWonLost);
	//	game_events->cutscene_extra_time_coin_toss_decide_to_kick.Remove(this, &SSCutSceneManager::OnCoinTossDecideToKick);
	//	game_events->cutscene_extra_time_coin_toss_decide_direction.Remove(this, &SSCutSceneManager::OnCoinTossDecideDirection);
	//	game_events->cutscene_extra_time_coin_toss_finished.Remove(this, &SSCutSceneManager::OnCoinTossFinished);
	//}
#endif

	for (MabVector<CameraBlend*>::iterator iter = camera_blends.begin(); iter != camera_blends.end(); ++iter)
	{
		CameraBlend* blend = *iter;
		MabMemDelete(blend);
	}

	camera_blends.clear(); //not used

	//MabMemFree(last_filename_cache); //not used
	last_filename_cache.Empty();

	/*
	#ifdef ENABLE_GAME_DEBUG_MENU
		SIFDebugDrawHandleManager* handle_mgr = SIFDebug::GetDebugDrawHandleManager();
		handle_mgr->ReleasePool(debug_key_pool);
	#endif*/

	/// If i've disabled rendering make sure I re-enable it!
	if (stop_rendering_count > 0)
	{
		//  SIFApplication::GetApplication()->SetSuppressRenderingNoThreadSwitch(false);//#rc3_legacy
		stop_rendering_count = 0;
	}

	if (m_UCutsceneManager)
	{
		game->GetGameInstance().AllowObjectGC(m_UCutsceneManager);
		m_UCutsceneManager->MarkPendingKill();
	}
	//m_PausedSequence.Empty();
}

///-------------------------------------------------------------------------------
/// SyncUpdate
///-------------------------------------------------------------------------------

void SSCutSceneManager::SyncUpdate()
{
	//MabEVDSCutSceneManager::SyncUpdate();

	if (!game->GetGameSettings().game_settings.network_game)
	{
		next_frame_lock = 0;
	}

	// Re-enable rendering if it has been stopped.

	if (stop_rendering_count > 0)
	{
		stop_rendering_count--;
		if (stop_rendering_count == 0)
		{
			//SIFApplication::GetApplication()->SetSuppressRenderingNoThreadSwitch(false);//#rc3_legacy
		}
	}
}

///-------------------------------------------------------------------------------

void SSCutSceneManager::PauseAllCutScenes()
{
	if (cutscene_elements.size())
	{
		MabVector<CutsceneElement*>::iterator cur_iter = cutscene_elements.begin();
		CutsceneElement* current = (*cur_iter);

		if (current && current->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
		{
			CutsceneCinematicElement* cce = (CutsceneCinematicElement*)current;
			for (MabVector<StCinematicElementInfo>::iterator it = cce->cinematics.begin(); it != cce->cinematics.end(); it++)
			{
				StCinematicElementInfo* ci = &(*it);
				PauseSequencer(ci);
			}
		}
	}
}

///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------

void SSCutSceneManager::ResumeAllCutScenes()
{
	if (cutscene_elements.size())
	{
		MabVector<CutsceneElement*>::iterator cur_iter = cutscene_elements.begin();

		CutsceneElement* current = (*cur_iter);

		if (current && current->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
		{
			CutsceneCinematicElement* cce = (CutsceneCinematicElement*)current;

			for (MabVector<StCinematicElementInfo>::iterator it = cce->cinematics.begin(); it != cce->cinematics.end(); it++)
			{
				StCinematicElementInfo* ci = &(*it);

				if (UOBJ_IS_VALID(game->GetGameInstance().GetWorld())
					&& ci && UOBJ_IS_VALID(ci->UCutscenePtr) && UOBJ_IS_VALID(ci->UCutscenePtr->m_SequencePlayer)
					&& (ci->UCutscenePtr->m_SequencePlayer->IsPaused()))
				{
					ci->UCutscenePtr->m_SequencePlayer->Play();
					UE_LOG(LogTemp, Log, TEXT("ResumeSequencer'%s'"), *ci->UCutscenePtr->m_SequencePlayer->GetSequence()->GetName());
				}
			}
		}
	}
}

///-------------------------------------------------------------------------------
/// Stop all cutscenes
///-------------------------------------------------------------------------------

void SSCutSceneManager::StopAllCutScenes()
{
	/*#deleteme
	if (requested_ui_cutscene_state.size())//check if it's non-zero
	{
		requested_ui_cutscene_state.clear();
	}
	ui_cutscene_state = CSEVENT_UNUSED;
	CurrentCutsceneRecId = CSEVENT_UNUSED;

	if (running_cinematic && m_pCurrentLevelSequence && m_pCurrentLevelSequence->IsValidLowLevel() && m_pCurrentLevelSequence->SequencePlayer && m_pCurrentLevelSequence->SequencePlayer->IsPlaying())
	{
		UE_LOG(LogTemp, Log, TEXT("StopAllCutScenes(): CutScene Already playing, need to Stop It"));
		m_pCurrentLevelSequence->SequencePlayer->Stop();
	}

	if (m_SelectedCutSceneData.Num())
	   m_SelectedCutSceneData.Empty();//clearoff if anything in pipeline.

	ClearCutSceneElements();
	*/

	if (cutscene_elements.size())
	{
		MabVector<CutsceneElement*>::iterator cur_iter = cutscene_elements.begin();
		CutsceneElement* current = (*cur_iter);

		if (current && current->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
		{
			CutsceneCinematicElement* cce = (CutsceneCinematicElement*)current;
			for (MabVector<StCinematicElementInfo>::iterator it = cce->cinematics.begin(); it != cce->cinematics.end(); it++)
			{
				StCinematicElementInfo* ci = &(*it);
				StopSequencer(ci);
			}
		}
	}

	// This below code probably needs to be called when cutscene end delegate is called, otherwise data wont be cleared?
	ClearCutSceneElements();

	if (requested_ui_cutscene_state.size())  // check if it's non-zero
	{
		requested_ui_cutscene_state.clear();
	}
	ui_cutscene_state = CSEVENT_UNUSED;
	m_MainCutSceneLength = -1.0f;
}

///-------------------------------------------------------------------------------
void SSCutSceneManager::StopAllCutScenesButFirst()
{
	MabVector<CutsceneElement*>::iterator ce_iter;
	MabVector<CutsceneElement*>::iterator ce_iter_end;
	CutsceneElement* cce = nullptr;
	//ClearCutSceneElements();//commented in RC3
	//MabEVDSCutSceneManager::DeleteAllCutScenes(); //commented in RC3

	// Remove all the cutscenes after the first
	//MabEVDSCutSceneManager::DeleteAllCutScenesButFirst();

	// Remove all the elements associated with those cutscenes.
	if (cutscene_elements.size() > 0)
	{
		/* //commented in RC3
		CutsceneCinematicElement *cce = (CutsceneCinematicElement*)current;
		StMabEVDSCutScene *cs = cce->GetMainCutScene();

		if(cs)
		{
		cutscene_state_changed = true;
		if(current->swipe_used!=SWIPE_NONE && cs->CanStartWipe(SWIPE_DURATION*0.5f) && !wipe_manager->IsWipeRunning())
		{
		wipe_manager->StartWipe(SWIPE_DURATION,current->swipe_used);
		current->wipe_active = true;
		}

		// Stop the cutscene if it activated the wipe.
		if(wipe_manager->IsWipeRunning() && wipe_manager->IsOverHalfway() && current->wipe_active && cs->IsRunning())*/


		// If we only have 1 cutscene and multiple cutscene elements, we dont neccessarily want to remove all but the first one..
		// If the first one is not a cinematic element then our cutscene hasn't started? Or we broke the previous one.
		// Maybe search the vector for the first cinematic element and check where it is?
		//		At the end, we need to back peddle until we dont find a callback of type CUTSCENE_TYPE_CALLBACK
		//		At the start and it's request ID is the same as the current cutscene then remove everything after that one.

		ce_iter = cutscene_elements.begin();
		ce_iter_end = cutscene_elements.end();

		/*MabVector<StMabEVDSCutScene*>::iterator cs_iter = cutscenes.begin();
		StMabEVDSCutScene *cs = (*cs_iter);
		*/
		// We should have a cutscene....
		//MABASSERT(cs);
		int first_cutscene_tag = 0;// cs->GetTag();
		MABLOGDEBUG(MabString(0, "Our first cutscene has a tag of: %i", first_cutscene_tag).c_str());

		cce = nullptr;

		// Search for the first cinematic element
		for (; ce_iter != ce_iter_end; ++ce_iter)
		{
			cce = (*ce_iter);
			if (cce->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
			{
				CutsceneCinematicElement* cce_casted = (CutsceneCinematicElement*)cce;
				StCinematicElementInfo* cinematic = cce_casted->GetCinematic(0);

				// This cinemantic element was requested by our first cutscene!
				if (cinematic->CinematicElementInfoData.request_id == first_cutscene_tag)
				{
					// Make sure...
					//MABASSERT(cinematic->cutscene->GetTag() == first_cutscene_tag);
					//MABLOGDEBUG(MabString(0, "Found our cinematic element for our first cutscene: %s", cinematic->cutscene->GetName().c_str()).c_str());

					//// Check what that cutscenes state is... and what we should expect.
					//if(cinematic->cutscene->GetState() == CST_NONE)
					//{
					//	MABLOGDEBUG("Our first cutscene hasn't started yet!");
					//}
					//else
					//{
					//	MABLOGDEBUG("Our first cutscene has started, so the first element should be a cinematic one...");
					//}
					//break;
				}
			}
		}

		if (cce != NULL)
		{
			// Now delete all element after our previous iterator
			MabVector<CutsceneElement*>::iterator end_iter = cutscene_elements.end() - 1;
			CutsceneElement* end_element = (*end_iter);
			while (cce != end_element)
			{
				MabMemDelete(cutscene_elements.back());
				cutscene_elements.pop_back();

				end_iter = cutscene_elements.end() - 1;
				end_element = (*end_iter);
			}
		}

		/*//commented in RC3
				if(cce != NULL && (CutsceneCinematicElement*)cce)
				{
					// If the first is a cinematic element, remove all after this one.
					//if(iter == cutscene_elements.begin())
					{
						MabVector<CutsceneElement*>::iterator cur_iter = cutscene_elements.end() - 1;
						MabVector<CutsceneElement*>::iterator second_iter = cutscene_elements.begin();

						CutsceneElement *current = (*cur_iter);
						CutsceneElement *second = (*second_iter);
						while(current != second)
						{
							MabMemDelete( cutscene_elements.back() );
							cutscene_elements.pop_back();

							cur_iter = cutscene_elements.end() - 1;
							current = (*cur_iter);
						}
					}
				}
		*/
	}
	/*
	ClearCutSceneElements();*/

}
///-------------------------------------------------------------------------------
/// Stop all cutscenes
///-------------------------------------------------------------------------------

void SSCutSceneManager::FinishCurrentCutScene()
{
	force_finish_current_cutscene = true;
	force_finish_current_cutscene_skip = true;
}

///-------------------------------------------------------------------------------
/// Clear the cutscene_elements list.
///-------------------------------------------------------------------------------

void SSCutSceneManager::ClearCutSceneElements()
{
	while (!cutscene_elements.empty())
	{
		MabMemDelete(cutscene_elements.back());
		cutscene_elements.pop_back();
	}
	cutscene_elements.clear();

	/// And reset any variables that might have been reset by any pending elements.

	disable_substitutions = false;
	have_queued_ball_dead_cutscene = false;
	//running_cinematic = false; //make sure this is set to false.
	local_disable_skip = false;

	if (UOBJ_IS_VALID(m_UCutsceneManager))
	{
		m_UCutsceneManager->CleanupPausedCutscenes();
	}
}


bool SSCutSceneManager::IsSimulationDisabled()
{
	bool IsCutSceneInProgress = false;

	MabVector<CutsceneElement*>::iterator ce_iter;
	MabVector<CutsceneElement*>::iterator ce_iter_end;
	CutsceneElement* cce = nullptr;

	ce_iter = cutscene_elements.begin();
	ce_iter_end = cutscene_elements.end();

	// Search for the first cinematic element
	for (; ce_iter != ce_iter_end; ++ce_iter)
	{
		cce = (*ce_iter);
		if (cce->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
		{
			CutsceneCinematicElement* cce_casted = (CutsceneCinematicElement*)cce;
			for (MabVector<StCinematicElementInfo>::iterator it = cce_casted->cinematics.begin(); it != cce_casted->cinematics.end(); it++)
			{
				StCinematicElementInfo* cinematic = &(*it);
				if ((cinematic->CinematicElementInfoData.state == CST_RUNNING) || (cinematic->CinematicElementInfoData.state == CST_FINISHED))
				{
					IsCutSceneInProgress = true;
					break;
				}
			}
		}
	}

	return force_disable_simulation || IsCutSceneInProgress;
}


///-------------------------------------------------------------------------------
/// Hide ball if cutscene running with 'hide_ball' set.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::IsHideBall() //not used....
{
	MabVector<CutsceneElement*>::iterator ce_iter;
	MabVector<CutsceneElement*>::iterator ce_iter_end;
	CutsceneElement* cce = nullptr;

	ce_iter = cutscene_elements.begin();
	ce_iter_end = cutscene_elements.end();

	// Search for the first cinematic element
	for (; ce_iter != ce_iter_end; ++ce_iter)
	{
		cce = (*ce_iter);
		if (cce->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
		{
			CutsceneCinematicElement* cce_casted = (CutsceneCinematicElement*)cce;
			/*#checkme
			CinematicElementInfo* cinematic = cce_casted->GetCinematic(0);
			if ((cinematic->state == CST_RUNNING) || (cinematic->state == CST_FINISHED))
			{
				return cinematic->HideBall;
			}*/

			for (MabVector<StCinematicElementInfo>::iterator it = cce_casted->cinematics.begin(); it != cce_casted->cinematics.end(); it++)
			{
				StCinematicElementInfo* cinematic = &(*it);
				if ((cinematic->CinematicElementInfoData.state == CST_RUNNING) || (cinematic->CinematicElementInfoData.state == CST_FINISHED))
				{
					return cinematic->CinematicElementInfoData.HideBall;
				}
			}
		}
	}
	return false;
}

///-------------------------------------------------------------------------------
/// Hide non actors if cutscene running with 'hide_non_actors' set.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::IsHideNonActors()
{
	MabVector<CutsceneElement*>::iterator ce_iter;
	MabVector<CutsceneElement*>::iterator ce_iter_end;
	CutsceneElement* cce = nullptr;

	ce_iter = cutscene_elements.begin();
	ce_iter_end = cutscene_elements.end();

	// Search for the first cinematic element
	for (; ce_iter != ce_iter_end; ++ce_iter)
	{
		cce = (*ce_iter);
		if (cce->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
		{
			CutsceneCinematicElement* cce_casted = (CutsceneCinematicElement*)cce;

			for (MabVector<StCinematicElementInfo>::iterator it = cce_casted->cinematics.begin(); it != cce_casted->cinematics.end(); it++)
			{
				StCinematicElementInfo* cinematic = &(*it);
				if ((cinematic->CinematicElementInfoData.state == CST_RUNNING) || (cinematic->CinematicElementInfoData.state == CST_FINISHED))
				{
					return cinematic->CinematicElementInfoData.HideNonActors;
				}
			}
		}
	}

	return false;
}

//===============================================================================
//===============================================================================
void SSCutSceneManager::ModifySpawnableProp(const FPossessablePropData& propData, ULevelSequence* pSequence, ALevelSequenceActor* pLevelSequenceActor, ULevelSequencePlayer* pSequencePlayer)
{
	//FMovieSceneObjectBindingID objectBindingId = FMovieSceneObjectBindingID(propData.Guid, MovieSceneSequenceID::Root, EMovieSceneObjectBindingSpace::Local); // GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL change to new API, 

	const UE::MovieScene::FRelativeObjectBindingID FRelative(propData.Guid, MovieSceneSequenceID::Root);
	FMovieSceneObjectBindingID objectBindingId = FMovieSceneObjectBindingID(FRelative);

	switch (propData.PropsType)
	{
	case ECutscenePropType::Ball: //fall through
	case ECutscenePropType::CaptainsBall:
	{
		FString file_path = game->GetBallFileName();
		MABASSERT(!file_path.IsEmpty());

		UMaterialInterface* BallMaterial = LoadObject<UMaterialInterface>(NULL, *file_path, NULL, LOAD_None, NULL);
		if (BallMaterial != nullptr)
		{
			TArray<UObject*> boundObjList = pSequencePlayer->GetBoundObjects(objectBindingId);
			if (boundObjList.Num() > 0)
			{
				// Convert to an actor
				AStaticMeshActor* pBallMesh = Cast<AStaticMeshActor>(boundObjList[0]);
				if (UOBJ_IS_VALID(pBallMesh) && UOBJ_IS_VALID(pBallMesh->GetStaticMeshComponent()))
				{
					if (BallMaterial != nullptr)
					{
						pBallMesh->GetStaticMeshComponent()->SetMaterial(0, BallMaterial);
						pBallMesh->GetStaticMeshComponent()->LightingChannels.bChannel1 = true;
					}
				}
			}
		}
	}
	break;

	default:
		break;
	}
}

///-------------------------------------------------------------------------------
void SSCutSceneManager::ModifySpawnableParticleFX(const FPossessableParticleData& particleData, ULevelSequence* pSequence, ALevelSequenceActor* pLevelSequenceActor, ULevelSequencePlayer* pSequencePlayer)
{
	auto LoadAsset = [](FMovieSceneObjectBindingID& InBindingId, FString& InAssetPath, ULevelSequencePlayer* InSequencePlayer)
		{
			TArray<UObject*> boundObjList = InSequencePlayer->GetBoundObjects(InBindingId);
			if (boundObjList.Num() > 0)
			{
				// Convert to an actor
				AActor* pActor = Cast<AActor>(boundObjList[0]);
				if (pActor)
				{
					TArray<UActorComponent*>  BPComponents = pActor->BlueprintCreatedComponents;

					for (auto& ParticleActor : BPComponents)
					{
						UParticleSystemComponent* ParticleComponent = Cast<UParticleSystemComponent>(ParticleActor);

						if (ParticleComponent)
						{
							FStringAssetReference assetRef(InAssetPath);
							UParticleSystem* ParticleAsset = Cast<UParticleSystem>(assetRef.TryLoad());

							if (ParticleAsset)
							{
								ParticleComponent->SetTemplate(ParticleAsset);
								ParticleComponent->Activate(false);
								ParticleComponent->bAutoDestroy = true;

								UE_LOG(LogTemp, Log, TEXT("Activating: Particle System  %s"), *InAssetPath);
							}
							else
							{
								ParticleComponent->SetVisibility(false);
							}
							break;
						}

						/*UNiagaraComponent* NiagraComponent = Cast<UNiagaraComponent>(ParticleActor);

						if (NiagraComponent)
						{
							FStringAssetReference assetRef(InAssetPath);
							UNiagaraSystem* NiagraAsset = Cast<UNiagaraSystem>(assetRef.TryLoad());
							if (NiagraAsset)
							{
								NiagraComponent->SetAsset(NiagraAsset);
								NiagraComponent->Activate();
								UE_LOG(LogTemp, Log, TEXT("Activating: Niagra  %s"), *InAssetPath);
							}
							break;
						}*/
					}
				}
			}
		};

	//FMovieSceneObjectBindingID objectBindingId = FMovieSceneObjectBindingID(particleData.Guid, MovieSceneSequenceID::Root, EMovieSceneObjectBindingSpace::Local);  // GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL change to new API,
	const UE::MovieScene::FRelativeObjectBindingID  FRelative(particleData.Guid, MovieSceneSequenceID::Root);
	FMovieSceneObjectBindingID objectBindingId = FMovieSceneObjectBindingID(FRelative);

	FString* PPath = m_cutsceneEffectsRecArray.Find(particleData.ParticleType);

	if (PPath)
	{
		LoadAsset(objectBindingId, *PPath, pSequencePlayer);
	}
}

///-------------------------------------------------------------------------------
/// Update (every simulation step) - Note called after evds events are fired.
///-------------------------------------------------------------------------------


void SSCutSceneManager::Tick(float delta_time)
{
	//MABUNUSED(delta_time);	
	force_disable_simulation = false;

	/// Force disable simulation if anything is queued to be running in sandbox.
	if (!game->IsMatch() && !cutscene_elements.empty())
	{
		force_disable_simulation = true;
	}

	if (!game->GetGameSettings().game_settings.network_game)
	{
		// When a new cutscene is started, lock out tick until next frame.
		if (next_frame_lock)
		{
			//MABLOGDEBUG("SSCutSceneManager::Locked till next frame");
			return;
		}
	}
	else if (next_frame_lock > 0)
	{
		next_frame_lock--;
		return;
	}

	UpdateProfileSettings();

	//simulation_is_disabled = false;

	if (!game->IsMatch())
	{
		/// Stop cutscene reloading during bink video playback.
		/// Not safe to return out of here, as individual cutscenes are run from EVDS system and syncupdates.
		/// Safer to just disable loading.

		bool in_rules_video = false;
		// #rc3_legacy
		/*SIFWindowSystem* const window_system = SIFApplication::GetApplication()->GetWindowSystem();
		if((window_system->GetCurrentWindow() && window_system->GetCurrentWindow()->GetWindow()->GetName() == RugbyUIWindowNames::RUUI_TRAINING_RULES_WINDOW_NAME))
		{
			in_rules_video = true;
		}*/

		UpdateUICutscene();

	}

	/// Set the running_cinematic (pre-update)

	/*for(MabVector<StMabEVDSCutScene*>::iterator iter = cutscenes.begin(); iter != cutscenes.end(); ++iter)
	{
		StMabEVDSCutScene *cs = (*iter);
		if(cs->GetState()==CST_RUNNING && !cs->IsDual() && cs->IsDisabledSimulation())
		{
			running_cinematic = true;
		}
	}
	*/

	SetupRepulsionZones();

	/// Debug: Disable hud every frame (to be sure) if required.
#ifdef ENABLE_SIF_DEBUG_DRAW
	if (SIFDebug::GetCommonDebugSettings()->Is3DHUDDisabled())
	{
		DisableHUD();
	}
#endif

	///--------------------------

	if (request_stop_all_cutscenes)
	{
		StopAllCutScenes();
		request_stop_all_cutscenes = false;
	}

	if (player_creator_on)
	{
		UpdatePlayerCreatorAngle(delta_time);

		if (game->GetPostEffectsManager())
		{
			game->GetPostEffectsManager()->SetDisableDOF();
		}
	}

	/// If commentary system requests it, delay stadium pan exit from management screens.

	if (haka_commentary_delay > 0.0f)
	{
		haka_commentary_delay -= delta_time;
		total_haka_comentary_delay += delta_time;

		if (SIFApplication::GetApplication()->GetCommentarySystem()->ShouldHoldStadiumPan(should_skip))
		{
			haka_commentary_delay = HAKA_COMMENTARY_DELAY;
		}

		if (haka_commentary_delay <= 0.0f || total_haka_comentary_delay > MAX_HAKA_COMMENTARY_DELAY)
		{
			RequestElementFinish();
			total_haka_comentary_delay = 0.0f;
		}

		// -- Disable restart match from the menu
		EnableRestartGameOption(false);
	}

	/// Timer is started at begining off haka cutscene. (StartHakaCutsceneTimer)
	if (haka_skip_disable_time > 0.f)
	{
		haka_skip_disable_time -= delta_time;
		if (haka_skip_disable_time <= 0.f)
			SetPlayingHaka(true);					// Disable skipping until PostHakaCallback.
	}

	///================================================
	/// Show on-screen debug information.
	bool cutscene_state_changed = false;
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
	//#ifdef ENABLE_GAME_DEBUG_MENU
	{
		/*long key = DEBUG_CUTSCENE_START_KEY;
		while(key<last_debug_key_used)
		{
			SIF_DEBUG_DRAW(RemoveText(key));
			key++;
		}
		last_debug_key_used = DEBUG_CUTSCENE_START_KEY;
		key = DEBUG_CUTSCENE_START_KEY;*/


		//#rc3_legacy_debug_cutscene
		//bool debugCutsceneState = CVarCutsceneState.GetValueOnGameThread() > 0 || FParse::Param(FCommandLine::Get(), TEXT("CutsceneState"));
		//bool debugCutsceneName = CVarCutsceneName.GetValueOnGameThread() > 0 || FParse::Param(FCommandLine::Get(), TEXT("CutsceneName"));
		//if( !SIFDebug::GetCutsceneDebugSettings()->GetCutsceneStateVisible() && SIFDebug::GetCutsceneDebugSettings()->GetCutsceneNameVisible())
		//if(debugCutsceneState && debugCutsceneName)
		//{
			/*if (running_cinematic && m_pCurrentLevelSequence && m_pCurrentLevelSequence->IsValidLowLevel() && m_pCurrentLevelSequence->SequencePlayer && m_pCurrentLevelSequence->SequencePlayer->IsPlaying())
			{
				if(GEngine)
				{
					GEngine->AddOnScreenDebugMessage(-1, -1, FColor::Blue, FString::Printf(TEXT("Cutscene: %s"), *m_pCurrentLevelSequence->SequencePlayer->GetSequence()->GetName()));
				}
			}*/
			/*for(MabVector<StMabEVDSCutScene*>::iterator iter = cutscenes.begin(); iter != cutscenes.end(); ++iter)
			{
				StMabEVDSCutScene *cs = (*iter);
				int cs_tag = cs->GetTag()&CSEVENT_LOADING_MASK;

				if(cs->GetState()==CST_RUNNING)
				{
					MabString name = MabStringHelper::Replace(cs->GetName(),"ruged/cutscenes/","");

					GEngine->AddOnScreenDebugMessage(-1, -1, FColor::Blue, FString::Printf(TEXT("Cutscene: %s %s"), cs_tag >= 0 ? cs_info[cs_tag].tag_name : "", name.c_str()));
				}
			}*/
			//}

			/*if( SIFDebug::GetCutsceneDebugSettings()->GetCutsceneStateVisible())
			{
				float y = 10.0f;
				for(MabVector<StMabEVDSCutScene*>::iterator iter = cutscenes.begin(); iter != cutscenes.end(); ++iter)
				{
					StMabEVDSCutScene *cs = (*iter);
					int cs_tag = cs->GetTag()&CSEVENT_LOADING_MASK;

					SETDEBUGTEXTWORLD( key++, FVector(10.0f, y, 0.0f),
						MabString(0, "%s %s : time= %.2f : State:%d Paused:%d DisableSim:%d",
							cs_tag>=0?cs_info[cs_tag].tag_name:"",
							cs->GetName().c_str(),
							cs->GetTime(),
							(int)cs->GetState(),
							cs->IsPaused(),
							cs->IsDisabledSimulation()));

					y += 20.0f;
				}

				if(game->GetWorldId()==WORLD_ID::SANDBOX)
				{
					y += 40.0f;
					{
						int cs_tag = ui_cutscene_state;
						SETDEBUGTEXTWORLD( key++, FVector(10.0f, y, 0.0f), MabString(0, "ui_cutscene_state: %s",cs_tag>=0?cs_info[cs_tag].tag_name:""));
					}
					{
						y += 20.0f;
						SETDEBUGTEXTWORLD( key++, FVector(10.0f, y, 0.0f), MabString(0, "ui_repeat_cutscene_count: %d",ui_repeat_cutscene_count));
					}

					y += 20.0f;
					SETDEBUGTEXTWORLD( key++, FVector(10.0f, y, 0.0f), MabString(0, "ui_requests:"));
					y += 20.0f;
					for(int i=(int)requested_ui_cutscene_state.size()-1;i>=0;i--)
					{
						int cs_tag = requested_ui_cutscene_state[i]&0xffff;
						int action = (requested_ui_cutscene_state[i]>>16)&0xffff;
						SETDEBUGTEXTWORLD( key++, FVector(10.0f, y, 0.0f), MabString(0, "%s : %d",cs_tag>=0?cs_info[cs_tag].tag_name:"",action ));
						y += 20.0f;
					}
				}

				y += 40.0f;

				for(MabVector<CutsceneElement*>::iterator iter = cutscene_elements.begin(); iter != cutscene_elements.end(); ++iter)
				{
					CutsceneElement *current = (*iter);

					switch(current->cutscene_type)
					{
					case CUTSCENE_TYPE_BACKGROUND_CINEMATIC:
					case CUTSCENE_TYPE_CINEMATIC:
						{
							CutsceneCinematicElement *cc = (CutsceneCinematicElement*)current;

							MabString string(0, "CINEMATIC: ");

							if(current->cutscene_type==CUTSCENE_TYPE_BACKGROUND_CINEMATIC)
								string += "(BG) ";

							int num_cutscenes = cc->NumCinematics();
							for(int idx=0;idx<num_cutscenes;idx++)
							{
								CinematicElementInfo *ci = cc->GetCinematic(idx);
								if(ci->request_id!=CSEVENT_NAMED_CUTSCENE)
								{
									string += MabString(0,"%s  ",cs_info[ci->request_id&CSEVENT_LOADING_MASK].tag_name);
								}
								else
								{
									string += MabString(0,"%s  ",ci->filename.c_str());
								}
							}

							SETDEBUGTEXTWORLD( key++, FVector(10.0f, y, 0.0f), string );
						}
						break;

					case CUTSCENE_TYPE_LOAD_WAIT:
						SETDEBUGTEXTWORLD( key++, FVector(10.0f, y, 0.0f), MabString(0, "LOAD_WAIT: "));
						break;

					case CUTSCENE_TYPE_LOAD_STOP:
						SETDEBUGTEXTWORLD( key++, FVector(10.0f, y, 0.0f), MabString(0, "LOAD_STOP: "));
						break;

					case CUTSCENE_TYPE_REPLAY:
						SETDEBUGTEXTWORLD( key++, FVector(10.0f, y, 0.0f), MabString(0, "REPLAY: "));
						break;

					case CUTSCENE_TYPE_CALLBACK:
						{
							CutsceneCallbackElement *cb = (CutsceneCallbackElement*)current;

							SETDEBUGTEXTWORLD( key++, FVector(10.0f, y, 0.0f), MabString(0, "CALLBACK: %s",cb->debug_name));
						}
						break;

					case CUTSCENE_TYPE_USER_SKIP_POINT:
						SETDEBUGTEXTWORLD( key++, FVector(10.0f, y, 0.0f), MabString(0, "USER_SKIP_POINT: "));
						break;

					case CUTSCENE_TYPE_DELAY:
						SETDEBUGTEXTWORLD( key++, FVector(10.0f, y, 0.0f), MabString(0, "DELAY: %f",current->delay));
						break;

					default:
						SETDEBUGTEXTWORLD( key++, FVector(10.0f, y, 0.0f), MabString(0, "UNDEFINED: "));
						break;
					}

					y += 20.0f;
				}
			}

			last_debug_key_used = key;*/
	}
#endif

	/*if( SIFDebug::GetCutsceneDebugSettings()->GetCutSceneCustomisationModeEnabled())
	{
		SetPlayerCreatorAngle(SIFDebug:GetCutsceneDebugSettings()->GetCutSceneCustomisationAngle());
		SetPlayerCreatorCameraOffset(SIFDebug::GetCutsceneDebugSettings()->GetCutSceneCustomisationOffset(), false);
	}*/

	//#endif

	SSCameraManager* cam_mgr = game->GetCameraManager();
	SSScreenWipeManager* wipe_manager = game->GetScreenWipeManager();

	if (cutscene_elements.size() > 0)
	{
		wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Cutscene Elements vector is popuplated.");

		MabVector<CutsceneElement*>::iterator cur_iter = cutscene_elements.begin();
		CutsceneElement* current = (*cur_iter);
		bool element_ended = false;

		wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Iterating over current elements.");

		/// Check if elements have been pushed onto an empty list (if so then start the first element).

		if (current->uid != last_cutscene_element)
		{
			wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Current cutscene is not different from the last cutscene.");

			request_element_finish = false;

			StartNextCutsceneElement(false);
			cutscene_state_changed = true;

			if (cutscene_elements.size() > 0)
			{
				cur_iter = cutscene_elements.begin();
				current = (*cur_iter);
			}
			else
			{
				current = NULL;
				last_cutscene_element = -1;
			}
		}

		if (current != NULL)
		{
			wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Current cutscene is still valid.");

			/// Check if skip is enabled...
			/// N.B. element must be skippable AND must have a skip_point element to go to.
			bool skip_enabled = false;

			//Handles only skipping a cutscene once the fade is full
			if (skip_requested && wipe_manager && wipe_manager->IsOverHalfway())
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Requested cutscene skip and wipe is over halfway.");

				if (current && current->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Current element is a cinematic cutscene. Looping through all the elements and stopping them.");

					CutsceneCinematicElement* cce = (CutsceneCinematicElement*)current;

					for (MabVector<StCinematicElementInfo>::iterator it = cce->cinematics.begin(); it != cce->cinematics.end(); it++)
					{
						StCinematicElementInfo* ci = &(*it);
						//PauseSequencer(ci, CUTSCENE_STATE::CST_PRE_DELETE);
						StopSequencer(ci);
						ci->CinematicElementInfoData.is_looping = false;
					}
				}
				else if (current && current->cutscene_type == CUTSCENE_TYPE_REPLAY)
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Current element is a replay. Telling the replay manager to stop the replay.");

					SSReplayManager* replay_manager = game->GetReplayManager();
					MABASSERT(replay_manager != NULL);

					if (replay_manager != NULL)
					{
						replay_manager->StopReplay();
					}
				}

				skip_requested = false;
				should_skip = true;
			}

			if (current->skipable)
			{
				wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Checking for skip elements in the current cutscene.");

				MabVector<CutsceneElement*>::iterator next_iter = cur_iter;
				++next_iter;

				while (next_iter != cutscene_elements.end())
				{
					if ((*next_iter)->cutscene_type == CUTSCENE_TYPE_USER_SKIP_POINT)
					{
						skip_enabled = true;
					}
					++next_iter;
				}
			}

			// Check for cutscene skipping, and skip + discard all cutscene elements.

			if (skip_enabled)
			{
				wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Skip is enabled, checking human players to see if anyone is playing.");

				bool human_playing = false;
				MabVector<SSHumanPlayer*> hps = game->GetHumanPlayers();
				for (MabVector<SSHumanPlayer*>::const_iterator i = hps.begin(); i != hps.end(); ++i)
				{
					SSHumanPlayer* human_player = *i;
					MABASSERT(human_player != NULL);

					if (human_player->IsPlaying())
					{
						wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Found a playing human.");
						human_playing = true;
						break;
					}
				}

				if (!human_playing && game->GetGameSettings().game_settings.network_game == false)
				{
					wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick There is no human playing and this is not a network game.");

					TArray<ARugbyPlayerController*> controllers = SIFApplication::GetApplication()->GetLocalPlayerControllers();
					size_t max_ctrls = controllers.Num();
					for (size_t idx = 0; idx < max_ctrls; idx++)
					{
						if (controllers[idx] == nullptr)
						{
							continue;
						}
						if (controllers[idx]->GetActionState().IsOn(ERugbyGameAction::SKIP_CUTSCENE) && SkipEnabled())
						{
							should_skip = true;
						}
					}
				}


				// Dewald WW - This is where i want to put the skip text toggle
				//if(human_playing)
				//{
				//	if (game->GetHUDUpdater())
				//		game->GetHUDUpdater()->SetCutSceneSkipTextVisible(true);
				//}
			}
			else
			{
				wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Should skip is now false.");
				should_skip = false;
			}

			wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Checking if the wipe is active: Request Element Finish: %d", request_element_finish);
			if (wipe_manager && wipe_manager->IsWipeRunning() && request_element_finish)
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Wipe is currently active, setting this on the cutscene.");
				current->wipe_active = true;
			}

			// Do cutscene skip...
			wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Checking for cutscene skip: Wipe Running: %d, should skip: %d, request element finish: %d, force finish cutscene skip: %d", wipe_manager && wipe_manager->IsWipeRunning(), should_skip, request_element_finish, force_finish_current_cutscene_skip);

			if (wipe_manager && !wipe_manager->IsWipeRunning() && (should_skip || request_element_finish || force_finish_current_cutscene_skip))
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Passed cutscene skip conditions.");

				if (current->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Current cutscene is cinematic.");

					CutsceneCinematicElement* cce = (CutsceneCinematicElement*)current;
					StCinematicElementInfo* cs = cce->GetMainCutScene();
					if (request_element_finish || (cs->CinematicElementInfoData.state != CST_PRE_DELETE && UOBJ_IS_VALID(cs->UCutscenePtr) && UOBJ_IS_VALID(cs->UCutscenePtr->m_SequencePlayer) &&/*cs->GetTime()*/cs->UCutscenePtr->m_SequencePlayer->GetCurrentTime().AsSeconds() > NO_CUTSCENE_SKIP_TIME))
					{
						wwNETWORK_TRACE_JG("SSCutSceneManager::Tick We have requested element finish, or the cutscene is not in PRE DELETE state and sufficient time has elapsed.");

						if (should_skip && !request_element_finish && !force_finish_current_cutscene_skip)
						{
							wwNETWORK_TRACE_JG("SSCutSceneManager::Tick We will do a multi skip.");
							multi_skip = true;			// Skip all cutscenes till next skip-point/replay or empty cutscene_elements
						}

						force_finish_current_cutscene_skip = false;

						haka_skip_disable_time = 0.0f;	// Have skipped haka, so stop the timer to turn on skip disable for haka.

						if (cs != NULL && !current->swipe_used)
						{
							wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Swipe not used, stopping cutscenes.");

							cce->StopMainCutScene(this);
							StopDualCutscenes(cce);
						}
						else if (current->swipe_used)
						{
							wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Swipe used, starting a wipe.");
							wipe_manager->StartWipe(SWIPE_DURATION, current->swipe_used);
							current->wipe_active = true;
						}
					}
				}
				else if (current->cutscene_type == CUTSCENE_TYPE_REPLAY)
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Current cutscene is replay.");

					if (!request_element_finish)
					{
						wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Have not requested element finish.");

						if (!current->swipe_used)
						{
							wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Swipe not used, stopping replay.");

							game->GetReplayManager()->StopReplay();
							game->GetEvents()->replay_playback_finish();
						}
						else
						{
							wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Swipe used, starting a wipe.");

							CutsceneReplayElement* rpe = (CutsceneReplayElement*)current;
							rpe->repeat_count = 0;

							wipe_manager->StartWipe(SWIPE_DURATION, current->swipe_used);
							current->wipe_active = true;
						}
					}
				}
			}
			should_skip = false;
			wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Done checking skip.");

			// Update current cutscene element.
			wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Updating current element.");

			switch (current->cutscene_type)
			{
			case CUTSCENE_TYPE_LOAD_WAIT:
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Updating current element: CUTSCENE_TYPE_LOAD_WAIT.");
				if (current->wait_load)
				{
					wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Current element is for load wait.");
					element_ended = true;
					//for(MabVector<StMabEVDSCutScene*>::iterator iter = cutscenes.begin(); iter != cutscenes.end(); ++iter)
					//{
					//	StMabEVDSCutScene *cs = (*iter);
					//	/*if(!cs->IsLoaded())
					//	{
					//		element_ended = false;
					//	}*/
					//}

					if (game->GetGameSettings().game_settings.network_game)
					{

						RUNetworkState* net_state = game->GetNetworkState();

						/// Setup synchronization of loading in network game.
						if (element_ended && async_wait_state == AST_LOADING)
						{
							wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Async wait state is currently loading. Element has ended. Setting flag on network state.");

							net_state->SetFlag(CUTSCENE_READY_TAG, true);
							async_wait_state = AST_LOADED;
						}

						element_ended = false;

						if (async_wait_state == AST_LOADED && net_state->GetFlag(CUTSCENE_READY_TAG) != 0)
						{
							wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Async wait state is loaded. Network state does not have cutscene ready flag set. Setting load state to done and ended element to true");
							async_wait_state = AST_DONE;
							element_ended = true;
						}
					}
				}
				else
				{
					wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Setting ended element to true");
					element_ended = true;
				}
			}
			break;

			case CUTSCENE_TYPE_CINEMATIC:
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Current cutscene is cinematic.");

				bool CanStartNextLoopCutScene = false;
				bool bForceBail = false;

				CutsceneCinematicElement* cce = (CutsceneCinematicElement*)current;
				StCinematicElementInfo* cs = nullptr;

				if (cce)
				{
					wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Getting Cinematic Element Info from the Current cinematic element.");
					cs = cce->GetMainCutScene();
				}

				if (cs)
				{
					wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Cinematic Element Info is valid.");

					cutscene_state_changed = true;
					float CurrentTime = 0.0f;
					float TotalTime = 0.0f;
					bool CanStartSwipe = false;

					//store the time, needed for swipe
					if (UOBJ_IS_VALID(cs->UCutscenePtr) && UOBJ_IS_VALID(cs->UCutscenePtr->m_SequencePlayer))
					{
						CurrentTime = cs->UCutscenePtr->m_SequencePlayer->GetCurrentTime().AsSeconds();
						TotalTime = cs->UCutscenePtr->m_SequencePlayer->GetEndTime().AsSeconds();

						LastCutsceneTime = CurrentTime;

						if (LastCutsceneTime > CUTSCENE_ALLOW_SKIP_TIME)
						{
							// Dewald WW - I really want to remove this and move it into the Tick function where it checks if the cutscene is skipable
							if (game->GetHUDUpdater())
							{
								game->GetHUDUpdater()->SetCutSceneSkipTextVisible(true);
							}
						}

						wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Retrieving time from Cinematic Element Info Current Time: %f, Total Time: %f", CurrentTime, TotalTime);
					}

					//add crowd event
					if (cs->CinematicElementInfoData.HasCrowdReaction)
					{
						wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Cinematic Element Info has cutscene reaction.");

						for (auto& CrowdReaction : cs->CinematicElementInfoData.CrowdReactionDataList)
						{
							if ((CurrentTime > CrowdReaction.CrowdReactionTime) && (CrowdReaction.HasPlayedCrowdReaction == false))
							{
								UE_LOG(LogTemp, Log, TEXT("Starting crowd cheer in Cutscene at %.2f Type '%d'"), CurrentTime, CrowdReaction.CrowdReactionType);
								game->GetEmotionEngineManager()->GetCrowdReactionManager()->GetAudioReactionManager()->PlayCrowdReaction((CROWD_AUDIO_REACTION)CrowdReaction.CrowdReactionType, CrowdReaction.CrowdReactionVolume);
								CrowdReaction.HasPlayedCrowdReaction = true;
							}
						}
					}

					//add Commentary event
					if (cs->CinematicElementInfoData.CommentaryEventStartTime >= 0.0f) //negative means no Commentary
					{
						wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Cinematic Element Info Has commentary event.");

						if (CurrentTime > cs->CinematicElementInfoData.CommentaryEventStartTime)
						{
							UE_LOG(LogTemp, Log, TEXT("Starting delayed commentry in Cutscene for Haka at %.2f"), CurrentTime);
							SIFApplication::GetApplication()->GetCommentarySystem()->CutsceneEvent(CommentaryCutsceneEvent::CCE_HAKA_FINISHED); //only for Haka
							cs->CinematicElementInfoData.CommentaryEventStartTime = -1.0f; //reset back to -1.0f so that it doesnot fire again.
						}
					}

					if (cs->CinematicElementInfoData.is_looping && TotalTime > 0.0f && (cs->CinematicElementInfoData.request_id != CSEVENT_UI_TITLE)) //start a looping cutscene before the previous one ends to avoid flicker
					{
						wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Cinematic Element Info looping is true, the total time for this cutscene is above 0 and the request ID is not the title.");

						CanStartSwipe = false;
						//ensureAlways ((TotalTime - CurrentTime) > 0.0f);
						CanStartNextLoopCutScene = (TotalTime - CurrentTime) < 0.1f;// 0.1f is a magic number (0.1 second)

						////This additional logic added for loop cutscenes to ensure non-actors are always hidden. It is because when a cutscene ends, the ULevelSequencePlayer::EnableCinematicMode implicitly sets the pawn to visible by calling APlayerController::SetCinematicMode
						////so even if the player is hidden, it will make it visible. In RC4, we start a new cutscene by pausing the previous one (to avoid flicker) until the new cutscene starts. So even if we hide the actor when we start a cutscene, the endcallback of previous cutscene will make it visible. So had to explicitly set
						////the visibility here. If we remove the cutscene pause, then below code can be removed. #RC4-3453
						//if (cs->CinematicElementInfoData.is_Main) //RC4-3244 has removed pausing hack, so we dont need this code anymore.
						//{
						//	UpdatePlayerVisibility(cs->CinematicElementInfoData.HideNonActors);
						//}

						//RC4-3965: CSEVENT_UI_CUSTOMIZE_TEAM plays the same cutscene in loop, so we can use Unreal loop logic for this instead of using our own. i.e. settings.LoopCount.Value = -1;
						if (cs->CinematicElementInfoData.CutSceneData.CutSceneID == CSEVENT_UI_CUSTOMIZE_TEAM)
						{
							CanStartNextLoopCutScene = false;
						}
					}

					//check if we can start swipe
					else if (UOBJ_IS_VALID(cs->UCutscenePtr) && UOBJ_IS_VALID(cs->UCutscenePtr->m_SequencePlayer))
					{
						//if (cs->CinematicElementInfoData.request_id != CSEVENT_UI_TITLE)
						//{
						CanStartSwipe = CurrentTime >= (TotalTime - (SWIPE_DURATION * 0.5f));
						//}
						//else
						//{
							//CanStartSwipe = CurrentTime >= (TotalTime - (0.05f)); //0.1f is a magic number(0.1 second)
						//}

						wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Cinematic Element Info. CurrentTime '%.3f', TotalTime '%.3f', SwipeTime '%.3f'", CurrentTime, TotalTime, TotalTime - (SWIPE_DURATION * 0.5));
					}

					else if ((UOBJ_IS_VALID(cs->UCutscenePtr) == false) || ((cs->UCutscenePtr) && (UOBJ_IS_VALID(cs->UCutscenePtr->m_SequencePlayer) == false))) //this should not happen, but in case it does, make sure we bail out.
					{
						wwNETWORK_TRACE_JG("SSCutSceneManager::Tick We lost one of our data pointers somehow, forcing a bail.");

						if (!cs->CinematicElementInfoData.is_looping)
						{
							wwNETWORK_TRACE_JG("Forcefully Bailing out from CutScene");
							bForceBail = true;
						}
						else
						{
							if (cs->CinematicElementInfoData.CutSceneData.CutSceneID != CSEVENT_UI_CUSTOMIZE_PLAYER_BODY)
							{
								CanStartNextLoopCutScene = true;
							}
						}
					}

					PlayerCreatorPlayAnimation(cs); //#playerCreator //check if we have anything for player creator

					wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Cinematic Element Info Start Wipe su:%d css:%d wr:%d", current->swipe_used, CanStartSwipe, wipe_manager->IsWipeRunning());
					if (current->swipe_used != SWIPE_NONE && CanStartSwipe && !wipe_manager->IsWipeRunning())
					{
						//#dewald hack for french demo to stop fading for the looping title cutscene
						if (cs->CinematicElementInfoData.request_id != CSEVENT_UI_TITLE) //Note because of this hack, there are other hack for CSEVENT_UI_TITLE to work.
						{
							wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Cinematic Element Info Starting wipe. Setting wipe to be active.");
							//UE_LOG(LogTemp, Log, TEXT("Start Wipe "));
							wipe_manager->StartWipe(SWIPE_DURATION, current->swipe_used);
							current->wipe_active = true;
						}
						else
						{
							if (UOBJ_IS_VALID(cs->UCutscenePtr) && UOBJ_IS_VALID(cs->UCutscenePtr->m_SequencePlayer)) //Note because of this hack, there are other hack for CSEVENT_UI_TITLE to work.
							{
								UE_LOG(LogTemp, Log, TEXT("Looping Back MainMenuCutscene to 4.95seconds "));
								cs->UCutscenePtr->m_SequencePlayer->JumpToSeconds(4.95f); //this is a magic number used to loop back main menu subcutscene. RC4-2793
							}
							else
							{
								bForceBail = true;
							}

						}
					}

					wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Trying to stop main cutscene. Wipe Running: %d Wipe over halfway: %d Wipe Active: %d Cutscene element state: %d", wipe_manager->IsWipeRunning(), wipe_manager->IsOverHalfway(), current->wipe_active, cs->CinematicElementInfoData.state);

					// Stop the cutscene if it activated the wipe.
					if (wipe_manager->IsWipeRunning() && wipe_manager->IsOverHalfway() && current->wipe_active && cs->CinematicElementInfoData.state == CST_RUNNING) //if (!IsCutSceneInProgress)						
					{
						if (!ForceCutsceneInfiniteLoop())
						{
							wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Cutscene is not forcing inf loop. Stop main cutscene.");
							cce->StopMainCutScene(this);
						}
					}
				}//end of 'if (cs)'

				wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Checking reset conditions.");
				wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Cutscene Cinematic Element is valid: %d, Force bail: %d, Looping or can start next loop: %d, state finished or pre delete and wipe is halfway: %d", cs == nullptr, bForceBail, (cs && cs->CinematicElementInfoData.is_looping && CanStartNextLoopCutScene), ((cs && (cs->CinematicElementInfoData.state == CST_FINISHED || cs->CinematicElementInfoData.state == CST_PRE_DELETE)) && (wipe_manager && wipe_manager->IsOverHalfway())));

				//check if we need to end the cutscene
				if (cs == nullptr || bForceBail || (cs && cs->CinematicElementInfoData.is_looping && CanStartNextLoopCutScene) || ((cs && (cs->CinematicElementInfoData.state == CST_FINISHED || cs->CinematicElementInfoData.state == CST_PRE_DELETE)) && (wipe_manager && wipe_manager->IsOverHalfway())))
				{
					//	UE_LOG(LogNetwork, Display, TEXT("Clearing all roles"));
					wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Clearing all roles.");

					ClearAllRolesExceptOfficials();

					if (cs && cs->CinematicElementInfoData.is_looping == false)
					{
						wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Cutscene is not looping, set element ended to true.");
						element_ended = true;
					}

#ifndef DISABLE_REPLAYS

					if (cs && (cs->CinematicElementInfoData.state == CST_FINISHED))
					{
						wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Element Info Data State is finished, getting the replay manager.");

						//#afl_replay
						SSReplayManager* replay_manager = game->GetReplayManager();

						if (replay_manager != nullptr)
						{
							wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Recording key frame.");
							replay_manager->RecordCutKeyFrame();
						}
					}
#endif

					cam_mgr->SetCameraSnap(0);

					// Stop slave cutscenes...
					StopDualCutscenes(cce);

					//start the next loop cutscene.
					if (cs && cs->CinematicElementInfoData.is_looping && CanStartNextLoopCutScene)
					{
						wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Looping cutscene can start next loop.");

						ARugbyPlayerController* playerCtrl = Cast<ARugbyPlayerController>(UGameplayStatics::GetPlayerController(game->GetGameInstance().GetWorld(), 0));

						if (playerCtrl && playerCtrl->IsValidLowLevel())
						{
							wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Found valid player controller.");

							AActor* currCam = Cast<AActor>(playerCtrl->GetDefaultGameCamera());
							AActor* ViewTarget = playerCtrl->GetViewTarget();
							//A Hack for game camera to have a snapshot of last loop cutscene to avoid a flicker.
							if (currCam && ViewTarget && currCam != ViewTarget)
							{
								wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Setting camera location to avoid flicker.");

								currCam->SetActorLocation(ViewTarget->GetActorLocation());
								currCam->SetActorRotation(ViewTarget->GetActorRotation());
							}
						}

						StopSequencer(cs);//stop loop cutscene before starting another

						PlaySequence(&cs->CinematicElementInfoData.CutSceneData);
					}
				}
				else
				{
					wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Checking for valid cinematic cutscene element. Iterating over cinematics.");

					UCutScenes* TempCutscenePtr = nullptr;

					CutsceneCinematicElement* cce_casted = (CutsceneCinematicElement*)cce;
					for (MabVector<StCinematicElementInfo>::iterator CurrentCinematicInfoPtr = cce_casted->cinematics.begin();
						CurrentCinematicInfoPtr != cce_casted->cinematics.end(); CurrentCinematicInfoPtr++)
					{
						wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Checking cinematic.");

						if (CurrentCinematicInfoPtr->UCutscenePtr &&
							CurrentCinematicInfoPtr->UCutscenePtr->m_ActorGUIDData &&
							(CurrentCinematicInfoPtr->UCutscenePtr->m_ActorGUIDData->PlayableCameraGuidList.Num() > 0))
						{
							wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Cinematic is valid.");

							TempCutscenePtr = CurrentCinematicInfoPtr->UCutscenePtr;
							if (TempCutscenePtr && CurrentCinematicInfoPtr->CinematicElementInfoData.is_Main) //get for the 1st cutscene camera always!
							{
								wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Positioning camera.");
								PositionCamera(delta_time, TempCutscenePtr);
							}

						}
					}
				}

				/*
				//#nirupam: I have added this code to switch between cameras in dual cutscene. Commented for now as I could not find scenerio...
				UCutScenes* TempCutscenePtr = nullptr;

				//if (UOBJ_IS_VALID (cs->UCutscenePtr))
				CutsceneCinematicElement* cce_casted = (CutsceneCinematicElement*)cce;
				{
					TArray <UCutScenes*> TempCutscenePtrArray;
					bool foundMatchingCamera = false;

					for (MabVector<StCinematicElementInfo>::iterator CurrentCinematicInfoPtr = cce_casted->cinematics.begin();
						CurrentCinematicInfoPtr != cce_casted->cinematics.end();
						CurrentCinematicInfoPtr++ )
					{
						if ( foundMatchingCamera )
						{
							break; //break from for
						}

						if (CurrentCinematicInfoPtr->UCutscenePtr &&
							CurrentCinematicInfoPtr->UCutscenePtr->m_ActorGUIDData &&
							(CurrentCinematicInfoPtr->UCutscenePtr->m_ActorGUIDData->PlayableCameraGuidList.Num() > 0))
						{

							for ( auto &cameraGuid : CurrentCinematicInfoPtr->UCutscenePtr->m_ActorGUIDData->PlayableCameraGuidList )
							{
								//0.5 is a half second magic number
								if (cameraGuid.TimeLine > 0.5f && FMath::IsNearlyEqual (cameraGuid.TimeLine, m_TimeLineForCamera + delta_time, 0.5f))
								{
									TempCutscenePtr = CurrentCinematicInfoPtr->UCutscenePtr;
									foundMatchingCamera = true;
									break;
								}
							}
						}

					}
				}

				if (TempCutscenePtr) //get for the last one always??
				{
					PositionCamera(delta_time, TempCutscenePtr);
				}
				else //nullptr, so use default camera....
				{
					if (cce_casted->cinematics.size())
					{
						if (cce_casted->cinematics[0].UCutscenePtr &&
							cce_casted->cinematics[0].UCutscenePtr->m_ActorGUIDData &&
							(cce_casted->cinematics[0].UCutscenePtr->m_ActorGUIDData->PlayableCameraGuidList.Num() > 0))
						{
							TempCutscenePtr = cce_casted->cinematics[0].UCutscenePtr;
							PositionCamera(delta_time, TempCutscenePtr);
						}
					}
				}
			}*/

			}//case CUTSCENE_TYPE_CINEMATIC:
			break;

			case CUTSCENE_TYPE_BACKGROUND_CINEMATIC:
				MABBREAKMSG("Should never tick on a CUTSCENE_TYPE_BACKGROUND_CINEMATIC");
				break;


			case CUTSCENE_TYPE_DELAY:
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Cutscene type is delay.");

				current->delay -= delta_time;
				if (current->delay <= 0.0f)
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Delay has reached 0, starting next element.");
					element_ended = true;
				}
			}
			break;

			case CUTSCENE_TYPE_CALLBACK:
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Cutscene type is callback.");

				CutsceneCallbackElement* cce = (CutsceneCallbackElement*)(*cur_iter);
				//MABLOGDEBUG("Cutscene callback (tick): %s",cce->debug_name);
				CutsceneCallbackFunction fptr = cce->callback;
				element_ended = fptr(this, cce);
			}
			break;

			case CUTSCENE_TYPE_SCREEN_WIPE:
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Cutscene type is wipe.");
				if (!wipe_manager->IsWipeRunning() || wipe_manager->IsOverHalfway())
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Wipe is running and is over half way.");
					element_ended = true;
				}
			}
			break;

			case CUTSCENE_TYPE_REPLAY:
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Cutscene type is replay.");
#ifndef INFINITE_REPLAY
				SSReplayManager* replay_manager = game->GetReplayManager();

				if (replay_manager == nullptr)
				{
					break;
				}

				wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Replay manager is valid.");

				CutsceneReplayElement* rpe = (CutsceneReplayElement*)current;

				// RC2: Disable low replay cameras for infringements.
				//game->GetCameraManager()->GetDirector()->SetNoLowReplayCameras(rpe->replay_type==RT_PENALTY || rpe->replay_type==RT_FORWARD_PASS || rpe->replay_type==RT_KNOCK_ON);//#rc3_legacy_replay

				//if(current->swipe_used!=SWIPE_NONE && replay_manager->CanStartSwipe(SWIPE_DURATION*0.5f) && !wipe_manager->IsWipeRunning())
				//{
				//	wipe_manager->StartWipe(SWIPE_DURATION, rpe->repeat_count<=1 ? current->swipe_used : SWIPE_CUT);	// CUT inbetween replays, but not at end.
				//	current->wipe_active = true;
				//}

				wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Checking wipe state: wipe running: %d, over half way: %d, wipe active: %d, replay finished: %d", wipe_manager->IsWipeRunning(), wipe_manager->IsOverHalfway(), current->wipe_active, replay_finished);
				if ((wipe_manager->IsWipeRunning() && wipe_manager->IsOverHalfway() && current->wipe_active) || replay_finished)
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Checking wipe state: Checking replay count: %d, decrementing value after this log. ", rpe->repeat_count);

					rpe->repeat_count--;
					//MABLOGDEBUG("Replay repeat: %d",rpe->repeat_count);
					if (rpe->repeat_count > 0)
					{
						wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Replay count is above zero: %d. Playing replay.", rpe->repeat_count);

						bool replayStarted = replay_manager->PlayReplay(rpe->replay_rewind_amount, rpe->replay_duration, rpe->camera_mode, rpe->replay_type, rpe->played_count++);
						if (replayStarted)
						{
							wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Replay started successfully.");
							request_element_finish = false;
							replay_finished = false;
							rpe->wipe_active = false;
						}
						else if (!replay_finished)
						{
							wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Replay not finished and replay did not start, stopping replay.");
							replay_manager->StopReplay();
							request_element_finish = true;
						}
					}
					else if (!replay_finished)
					{
						wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Replay finished, stopping replay.");
						replay_manager->StopReplay();
						request_element_finish = true;
					}
				}

				wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Checking whether to stop replay.");
				if (request_element_finish)
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Replay stop requested.");

					game->GetEvents()->replay_playback_finish();

					cam_mgr->SetCameraSnap(0);

					cam_mgr->GetDirector()->SetReplay(false);

					replay_manager->StopReplay();
				}

				element_ended = request_element_finish;
#else
				if (request_element_finish)
				{
					SSReplayManager* replay_manager = game->GetReplayManager();
					SSCameraManager* cam_mgr = game->GetCameraManager();
					cam_mgr->SetCameraSnap(0);
					wwNETWORK_TRACE_JG("Playing infintite replay.");
					replay_manager->PlayReplay(current->replay_length, cerp->replay_duration, current->camera_mode, current->replay_type);
				}
#endif
			}
			break;

			default:
				break;
			}

			if (element_ended)
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Element has ended, starting next element.");

				cutscene_state_changed = true;
				StartNextCutsceneElement();
				request_element_finish = false;
			}

			// Set 'last_cutscene_element' so can detect beginning of new cutscenes.

			if (cutscene_elements.size() > 0)
			{
				wwNETWORK_TRACE_JG_DISABLED("SSCutSceneManager::Tick Currently elements is populated, setting last element.");

				cur_iter = cutscene_elements.begin();
				last_cutscene_element = (*cur_iter)->uid;
			}
			else
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::Tick No last cutscene element.");
				last_cutscene_element = -1;
			}
		}
	}
	else
	{
		wwNETWORK_TRACE_JG("SSCutSceneManager::Tick Resetting multi skip and last element.");
		multi_skip = false;
		last_cutscene_element = -1;
	}

	//request_element_finish = false;

	///================================================
	/// Update player visibilities in cutscenes.
	/// But only when we have started or ended a new cutscene

	disable_visibility_update = false;

	/// Check for start of a cutscene.
	game->GetCameraManager()->Overide(false);

	if (player_creator_on)
	{
		ARugbyPlayerController* playerCtrl = Cast<ARugbyPlayerController>(UGameplayStatics::GetPlayerController(game->GetGameInstance().GetWorld(), 0));

		if (playerCtrl && playerCtrl->IsValidLowLevel())
		{
			AActor* currCam = Cast<AActor>(playerCtrl->GetDefaultGameCamera());
			AActor* ViewTarget = playerCtrl->GetViewTarget();
			FTransform UnrealTranform;
			SSCameraManager* camera_manager = game->GetCameraManager();

			FVector CameraLocation = GetPlayerCreatorTransform();

			FRotator LookAtRotation = UKismetMathLibrary::FindLookAtRotation(GetPlayerCreatorTransform(), FVector(0.0f, 0.0f, CameraLocation.Z));

			// Look to the left
			FRotator LookLeftRotator = LookAtRotation + FRotator(0.0f, -90.0f, 0.0f);

			CameraLocation = CameraLocation + (LookLeftRotator.Vector() * player_creator_offset.Y);

			UNREAL_VEC_TO_MAB_VEC_FOR_TRANSLATION(CameraLocation, MabCameraLocation);
			FVector eye = MabCameraLocation;

			// Rotate the focuse around the centre
			FVector2D Origin = FVector2D(0.0f, 0.0f);
			FVector2D Target2D = FVector2D(0.0f, -player_creator_offset.Y);

			//FVector2D Offset = FVector2D(0.0f, player_creator_offset.Y);
			FVector2D CameraDelta = RotatePoint(Origin, Target2D, player_creator_camera_angle.Yaw);

			FVector LookAtLocation = FVector(CameraDelta, player_creator_offset.Z);

			UNREAL_VEC_TO_MAB_VEC_FOR_TRANSLATION(LookAtLocation, MabLookAtLocation);
			FVector focus = FVector(MabLookAtLocation.X, MabCameraLocation.Y, -MabLookAtLocation.Z); // ya.GetTranslation();

			/*if (UOBJ_IS_VALID(it.UCutscenePtr) && UOBJ_IS_VALID(it.UCutscenePtr->m_SequencePlayer)
				&& (it.UCutscenePtr->m_SequencePlayer->IsPlaying()))
			{
				it.CinematicElementInfoData.is_looping = false;
				it.UCutscenePtr->m_SequencePlayer->Pause();
			}*/
			game->GetCameraManager()->Overide(true);
			playerCtrl->SetViewTarget(currCam);
			camera_manager->SetCameraFrom(eye, focus, 100.0f /*focal length*/, 0.0f, 0.0f, false);
		}
	}
	//for(MabVector<StMabEVDSCutScene*>::iterator iter = cutscenes.begin(); iter != cutscenes.end(); ++iter)//#rc3_legacy
	//{
	//	StMabEVDSCutScene *cs = (*iter);
	//	if(cs->GetState()==CST_RUNNING && !cs->IsDual() && cs->IsDisabledSimulation())
	//	{
	//		game->GetCameraManager()->Overide(true);
	//		cs->SetCamera();
	//		if(!running_cinematic)
	//		{
	//			next_frame_lock = 2;		// lock out update till next frame.
	//			DisableRendering(2);
	//		}
	//	}
	//}

	///// Force disable simulation if anything is queued to be running in sandbox.
	if (!game->IsMatch() && !cutscene_elements.empty())
	{
		force_disable_simulation = true;
	}

	if (advantage_end_countdown > 0)
	{
		--advantage_end_countdown;
		if (advantage_end_countdown == 0)
		{
			advantage_inaffect = false;
		}
	}

	force_finish_current_cutscene = false;

	////clear off any paused cutscene in next tick
	////Note: Here pause is just used to avoid camera flicker between cutscenes.
	////so if we need real pause of cutscenes, then dont add those in m_PausedSequence, and handle differently
	//if (m_PausedSequence.Num())
	//{	
	//	for (auto &it : m_PausedSequence)
	//	{
	//		if (UOBJ_IS_VALID(it))
	//		{
	//			if (it->IsPlaying())
	//			{
	//				it->Stop();
	//			}
	//		}			
	//	}
	//	//GetCutsceneManager()->CleanupPausedCutscenes();
	//	m_PausedSequence.Reset();
	//}

	//make sure we reset this data....
	if (cutscene_elements.size() == 0 && m_CutScene_player_List.Num() > 0)
		m_CutScene_player_List.Reset();

#if (!(UE_BUILD_SHIPPING || UE_BUILD_TEST) )	
	bool debugCutsceneInfo = CVarCutsceneName.GetValueOnGameThread() > 0 || FParse::Param(FCommandLine::Get(), TEXT("CutsceneInfo"));
	if (debugCutsceneInfo)
	{
		SIFRugbyCharacterList players = game->GetPlayers();
		for (MabVector<ARugbyCharacter*>::const_iterator i = players.begin(); i != players.end(); ++i)
		{
			ARugbyCharacter* player = *i;
			if (player && player->GetVisible())
			{
				FString PlayerName = player->GetName();
				//just remove whatever it preceeds with. 
				PlayerName.RemoveFromStart("GAME_Player_SIDE_");
				PlayerName.RemoveFromStart("MENU_Player_SIDE_");
				PlayerName.RemoveFromStart("SANDBOX_Player_SIDE_");

				FColor color = FColor::Blue; //official
				//if (player->GetVisible() == false)	color = FColor::Red;
				if (PlayerName.Contains("A_"))	color = FColor::White; //team A
				else if (PlayerName.Contains("B_"))	color = FColor::Green; //team B

				//FVector Scale = player->GetActorScale3D();

				//PlayerName = PlayerName+ " " + FString::SanitizeFloat(Scale.X);

				MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(player->GetBoneWorldPosition("head"), head_pos);

				DrawDebugString(player->GetGameWorld()->GetGameInstance().GetWorld(), head_pos, PlayerName, NULL, color, 0.0f);
			}
		}
	}
#endif
}

///-------------------------------------------------------------------------------
/// Stop rendering for 'num_frames' frames.
///-------------------------------------------------------------------------------

void SSCutSceneManager::DisableRendering(int num_frames)
{
	/*if(stop_rendering_count==0)//#rc3_legacy
	{
		SIFApplication::GetApplication()->SetSuppressRenderingNoThreadSwitch(true);
	}*/
	stop_rendering_count = num_frames;
}

///-------------------------------------------------------------------------------
/// Stop a cutscenes slaves (dual + triple).
///-------------------------------------------------------------------------------

void SSCutSceneManager::StopDualCutscenes(CutsceneCinematicElement* current)
{
	int num_cutscenes = current->NumCinematics();

	for (int idx = 1; idx < num_cutscenes; idx++)			// Skip 'main=0'
	{
		StCinematicElementInfo* ci = current->GetCinematic(idx);
		/*if(ci->cutscene)#rc3_legacy
			StopCutScene(ci->cutscene);
		ci->cutscene = NULL;*/
		StopSequencer(ci);
	}
}

///-------------------------------------------------------------------------------
/// Setup player visibilities...
///-------------------------------------------------------------------------------

void SSCutSceneManager::UpdatePlayerVisibility(bool hide_non_actors)			/// Setup player visibilities...
{
	SSTeam::PLAYER_VISIBILITY team_visibility = hide_non_actors ? SSTeam::PLAYER_VISIBILITY::SHOW_CUTSCENE_ACTORS : SSTeam::PLAYER_VISIBILITY::SHOW_FIELD;

	const std::vector<std::unique_ptr<RUTeam>>& teams = game->GetTeams();
	for (size_t i = 0; i < teams.size(); ++i)
	{
		RUTeam* team = teams[i].get();
		team->UpdateVisibility(team_visibility);
	}

	RUTeam* officials = game->GetOfficialsTeam();
	if (officials)
	{
		officials->UpdateVisibility(team_visibility);
	}
}

///-------------------------------------------------------------------------------
/// Should this 'cs_event' use a place holder 'pan' sequence?
///-------------------------------------------------------------------------------

bool ShouldUsePlaceHolder(int cs_event)
{
	const static int use_place_holder_list[] =
	{
		-1
	};

	int i = 0;
	while (use_place_holder_list[i] != -1)
	{
		if (use_place_holder_list[i] == cs_event)
			return true;
		i++;
	}

	return false;
}

///-------------------------------------------------------------------------------
/// Skip the cutscene.
///-------------------------------------------------------------------------------

void SSCutSceneManager::SkipCutScene()
{
	/*if (running_cinematic && UOBJ_IS_VALID(GetLevelSequence()) && UOBJ_IS_VALID(GetLevelSequence()->SequencePlayer))
	{
		if (GetLevelSequence()->SequencePlayer->IsPlaying())
		{
			GetLevelSequence()->SequencePlayer->Stop();
			MABLOGDEBUG("SSCutSceneManager::SkipCutScene");
		}
	}*/

	// Let the cutscene play for at least a second before allowing a skip.
	if (LastCutsceneTime < CUTSCENE_ALLOW_SKIP_TIME)
	{
		UE_LOG(LogTemp, Log, TEXT("SSCutSceneManager::SkipCutScene Blocked! Not played for more than 1 second. Current time: %f"), LastCutsceneTime);
		return;
	}

	if (cutscene_elements.size())
	{
		UE_LOG(LogTemp, Log, TEXT("SSCutSceneManager::SkipCutScene"));
		MabVector<CutsceneElement*>::iterator cur_iter = cutscene_elements.begin();
		//	
		CutsceneElement* current = (*cur_iter);

		if (current && current->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
		{
			SSScreenWipeManager* wipe_manager = game->GetScreenWipeManager();
			if (wipe_manager)
			{
				wwNETWORK_TRACE_JG("Skipping Cutscene!, wr: %d", wipe_manager->IsWipeRunning());
				if (!wipe_manager->IsWipeRunning())
				{
					wipe_manager->StartWipe(SWIPE_DURATION, current->swipe_used);
				}
			}
		}
	}
	skip_requested = true;
}

///-------------------------------------------------------------------------------
/// Can any of the running cutscenes be skipped (running > 1.0 secs)
///-------------------------------------------------------------------------------

bool SSCutSceneManager::SkipEnabled()
{
#if PLATFORM_WINDOWS && !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
	return true;
#else
	return !playing_haka;			// Must reset this after haka.
#endif
}

///-------------------------------------------------------------------------------
/// RequestElementFinish: Force the current element to finish.
///-------------------------------------------------------------------------------

void SSCutSceneManager::RequestElementFinish()
{
	request_element_finish = true;
}

///-------------------------------------------------------------------------------
/// Event handler for game_events->replay_playback_finish
///-------------------------------------------------------------------------------

void SSCutSceneManager::ReplayFinished()
{
	RequestElementFinish();
	replay_finished = true;
}

///-------------------------------------------------------------------------------
/// Call 'StopAllCutscenes' in the next update. (safe place).
///-------------------------------------------------------------------------------

void SSCutSceneManager::RequestStopAllCutscenes()
{
	request_stop_all_cutscenes = true;
}

///-------------------------------------------------------------------------------
/// GetMorphemeAnimationInterval
///-------------------------------------------------------------------------------

float SSCutSceneManager::GetMorphemeAnimationInterval()
{
	//if(IsEditing() && cutscenes.size()>0)
	//{
	///*	if(cutscenes[0]->IsPaused())
	//	{
	//		return 0;
	//	}*//#rc3_legacy
	//}
	return SIMULATION_INTERVAL;
}

///-------------------------------------------------------------------------------
/// Reset.
///-------------------------------------------------------------------------------

void SSCutSceneManager::Reset()
{
	StopAllCutScenes();
	//DeleteAllCutScenes();

	next_half = FIRST_HALF;
	next_extratime_mode = NOT_EXTRA_TIME;

	disable_substitutions = false;
	do_team_switch = false;
	//do_team_switch_generic = false;
	load_non_permanent_players = false;
	should_be_in_main_menu_hack = false;
	have_queued_ball_dead_cutscene = false;

	going_off_player = NULL;
	going_on_player = NULL;
	injured_player = NULL;

	if (advantage_replay) game->GetReplayManager()->FreeSavedReplay(advantage_replay);
	advantage_replay = NULL;
	advantage_inaffect = false;
	advantage_end_countdown = 0;
	advantage_last_started_time = -1.0f;
	m_MainCutSceneLength = -1.0f;

	if (stop_rendering_count > 0)
	{
		//SIFApplication::GetApplication()->SetSuppressRenderingNoThreadSwitch(false);//#rc3_legacy
		stop_rendering_count = 0;
	}

	// Re-enabled HUD if currently disabled.
	if (cutscene_hud_disabled
		&& (game->GetWorldState() == WORLD_STATE::AWAKENING || game->GetWorldState() == WORLD_STATE::AWAKE))
	{
		EnableHUD();
	}
}

///-------------------------------------------------------------------------------
/// On cutscene start
///-------------------------------------------------------------------------------

void SSCutSceneManager::OnCutSceneStart()
{
	//SIFGameWorld* game = GetGame();//#rc3_legacy

	game->GetEvents()->cutscene_start();

	DisableHUD();
}

///-------------------------------------------------------------------------------
/// On cutscene end
///-------------------------------------------------------------------------------

void SSCutSceneManager::OnCutSceneEnd()
{
	wwNETWORK_TRACE_JG("SSCutSceneManager::OnCutSceneEnd");
	EnableHUD();

	game->GetInputManager()->DebounceAllActionsForAllPlayers();
	game->GetEvents()->cutscene_finish();
}

///-------------------------------------------------------------------------------
/// Disable HUD's (2d + 3d) - separated from OnCutSceneStart so can be called for debug.
///-------------------------------------------------------------------------------

void SSCutSceneManager::DisableHUD()
{
	// No HUD in the menu
	if (game->GetWorldId() != WORLD_ID::MENU)
	{
		game->Get3DHudManager()->SetEnabled(false);
		game->Get3DHudManager()->VisiblePlayerMarkers(false);

		if (game->GetHUDUpdater())
		{
			game->GetHUDUpdater()->SetCutsceneActiveState(true);
			game->GetHUDUpdater()->SetCutSceneSkipTextVisible(false);
		}

		game->GetHUDUpdaterContextual()->Set2DDisplayHidden(true);
		game->Get3DHudManager()->GetContextualHelper()->Activate(false);
	}

	cutscene_hud_disabled = true;
}

///-------------------------------------------------------------------------------
/// Enable HUD's (2d + 3d) - separated from OnCutSceneEnd.
///-------------------------------------------------------------------------------

void SSCutSceneManager::EnableHUD()
{
	// No HUD in the menu
	if (game->GetWorldId() != WORLD_ID::MENU)
	{
		if (game->Get3DHudManager())
		{
			game->Get3DHudManager()->SetEnabled(true);
			game->Get3DHudManager()->VisiblePlayerMarkers(true);
			if (game->Get3DHudManager()->GetContextualHelper())
			{
				game->Get3DHudManager()->GetContextualHelper()->Activate(true);
			}
		}

		if (game->GetHUDUpdater())
		{
			game->GetHUDUpdater()->SetCutsceneActiveState(false);
		}

		if (game->GetHUDUpdaterContextual())
		{
			game->GetHUDUpdaterContextual()->Set2DDisplayHidden(false);
		}

		if (game->GetHUDUpdater())
		{
			game->GetHUDUpdater()->SetCutSceneSkipTextVisible(false);
		}
	}

	cutscene_hud_disabled = false;
}

///-------------------------------------------------------------------------------
/// Update settings from master profile.
///-------------------------------------------------------------------------------

void SSCutSceneManager::UpdateProfileSettings()
{
	// Moved the setting of whether cinematics are enabled to the constructor for networked games, as this cannot change mid match. -JG

	if (!game->GetGameSettings().game_settings.network_game)
	{
		if (SIFPlayerProfileManager::GetInstance() && SIFPlayerProfileManager::GetInstance()->GetMasterProfile())
		{
			const MabObservedValueList* value_list = NULL;
			value_list = SIFPlayerProfileManager::GetInstance()->GetMasterProfile()->GetNamedValueList();
			if (cutscene_profile_idx != -1)
			{
				const MabNamedValue& cse = value_list->GetNamedValue(cutscene_profile_idx);
				cinematics_enabled = cse.ToBoolean();
			}
			if (replay_profile_idx != -1)
			{
				const MabNamedValue& cse = value_list->GetNamedValue(replay_profile_idx);
				replays_enabled = cse.ToBoolean();
			}
		}
	}

#ifdef ENABLE_GAME_DEBUG_MENU
	//#rc3_legacy_debug_cutscene
	/*if( SIFDebug::GetCutsceneDebugSettings()->GetCinematicsDisabled() )
	{
		cinematics_enabled = false;
	}
	if( SIFDebug::GetCutsceneDebugSettings()->GetReplaysDisabled() )
	{
		replays_enabled = false;
	}*/
#endif

#ifdef DISABLE_REPLAYS
	replays_enabled = false;
#endif

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (pRugbyGameInstance)
	{
		replays_enabled &= pRugbyGameInstance->ReplayEnabled;
	}

	if (game->GetGameSettings().game_settings.network_game)
	{
		replays_enabled = false; //Prevent the game desyncing when two sides have different performance settings.
	}

#ifdef DISABLE_CUTSCENES
	cinematics_enabled = false;
#endif

}

///-------------------------------------------------------------------------------
/// StartNextCutsceneElement
///-------------------------------------------------------------------------------

void SSCutSceneManager::StartNextCutsceneElement(bool do_pop)
{
	bool element_finished_immediately;
	wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement");

	do
	{
		wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Doing check to start next cutscene.");

		MabVector<CutsceneElement*>::iterator cur_iter, next_iter;

		element_finished_immediately = false;

		// Delete current element from beginning of cutscene_elements.

		if (do_pop && cutscene_elements.size() > 0)
		{
			wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Do pop is true and we are currently populated with cutscene elements. Removing the first element.");

			// Remove first element...
			cur_iter = cutscene_elements.begin();
			MabMemDelete(*cur_iter);
			cutscene_elements.erase(cur_iter);
		}

		do_pop = true;

		// Process next element on list.

		if (cutscene_elements.size() > 0)
		{
			wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Currently populated with cutscene elements. Selecting the cutscene at the beginning of the iterator.");

			cur_iter = cutscene_elements.begin();
			CutsceneElement* current = (*cur_iter);

			// CINEMATIC

			switch (current->cutscene_type)
			{
			case CUTSCENE_TYPE_LOAD_WAIT:
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Current cutscene type is LoadWait.");
				if (current->wait_load)
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Current cutscene wait load bool is true. Iterating over the remaining cutscenes.");
					bool started_load = false;
					next_iter = cur_iter;
					++next_iter;
					while (next_iter != cutscene_elements.end())
					{
						CutsceneElement* ce = (*next_iter);

						if (ce->cutscene_type == CUTSCENE_TYPE_CINEMATIC || ce->cutscene_type == CUTSCENE_TYPE_BACKGROUND_CINEMATIC)
						{
							wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Found a cinematic or background cutscene, starting preload.");

							CutsceneCinematicElement* cecin = (CutsceneCinematicElement*)ce;
							cecin->PreLoadCutScenes(this);
							started_load = true;
						}
						else if (ce->cutscene_type == CUTSCENE_TYPE_LOAD_STOP)
						{
							wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Found load stop cutscene type, breaking from loop.");
							break;
						}
						++next_iter;
					}

					/// Setup synchronization of loading in network game.
					if (started_load && game->GetGameSettings().game_settings.network_game)
					{
						wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Setting online flag for cutscene load done.");
						async_wait_state = AST_LOADING;
						RUNetworkState* net_state = game->GetNetworkState();
						net_state->SetFlag(CUTSCENE_READY_TAG, false);
					}
				}
				else
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Current cutscene wait load bool is false, setting element finished immediately to true.");
					element_finished_immediately = true;
				}
			}
			break;

			case CUTSCENE_TYPE_BACKGROUND_CINEMATIC:
			case CUTSCENE_TYPE_CINEMATIC:
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Current cutscene type is background or cinematic.");

				if (current->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Clearing all roles for cinematic cutscene.");
					ClearAllRolesExceptOfficials();
				}

				wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Trying to cast to a cinematic cutscene and start it.");
				if (((CutsceneCinematicElement*)current)->StartLoadedCutScenes(this))
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Cinematic cutscene started successfully. Broadcasting start delegate.");
					CinematicCutsceneStartedDelegate.Broadcast();

					/// Set the game phase override for the formation manager.
					this->SetGamePhaseOverride(((CutsceneCinematicElement*)current)->game_phase_override);

					// Look ahead to next element, if a 'CUTSCENE_TYPE_LOAD_WAIT' element and wait_load = false, then
					// start async load of all future cinematics.

					wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Iterating over cutscenes to try and find load wait in the list of future cutscenes.");

					next_iter = cur_iter;
					++next_iter;
					if (next_iter != cutscene_elements.end())
					{
						if ((*next_iter)->cutscene_type == CUTSCENE_TYPE_LOAD_WAIT && (*next_iter)->wait_load == false)
						{
							wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Found a load wait cutscene. Checking for future cinematic cutscenes and preloading them..");
							++next_iter;
							while (next_iter != cutscene_elements.end())
							{
								CutsceneElement* ce = (*next_iter);

								if (ce->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
								{
									CutsceneCinematicElement* cecin = (CutsceneCinematicElement*)ce;
									cecin->PreLoadCutScenes(this);
								}
								else if (ce->cutscene_type == CUTSCENE_TYPE_LOAD_STOP)
								{
									wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Found load stop cutscene.");
									break;
								}

								++next_iter;
							}
						}
					}

					element_finished_immediately = (current->cutscene_type == CUTSCENE_TYPE_BACKGROUND_CINEMATIC);

					wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement If the current cutscene was a background, setting the element finished flag. New value: %d.", element_finished_immediately);
				}
				else
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Couldn't start cutscene, setting element finished flag to true.");
					element_finished_immediately = true;
				}
			}
			break;

			case CUTSCENE_TYPE_SCREEN_WIPE:
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Cutscene is a wipe type.");

				SSScreenWipeManager* wipe_manager = game->GetScreenWipeManager();
				if (wipe_manager && (!wipe_manager->IsWipeRunning() || wipe_manager->IsHeld()))
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Telling wipe manager to start wipe.");
					wipe_manager->StartWipe(SWIPE_DURATION, current->swipe_used);
				}
				else
				{
					// MABLOGDEBUG("Screen wipe element: Skipped");
					wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Skipping wipe. Setting element finished flag.");
					element_finished_immediately = true;
				}
			}
			break;

			case CUTSCENE_TYPE_LOAD_STOP:
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Current type is Load Stop. Setting element finishe flag.");
				element_finished_immediately = true;
			}
			break;

			case CUTSCENE_TYPE_DELAY:
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Cutscene is type delay.");
			}
			break;

			case CUTSCENE_TYPE_REPLAY:
			{
				multi_skip = false;			// Reset multi_skip.
				wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Cutscene is type replay. Setting multi skip to false. Grabbing replay element and manager.");
				CutsceneReplayElement* cerp = (CutsceneReplayElement*)current;
				SSReplayManager* replay_manager = game->GetReplayManager();
				bool replayStarted = true;
				if (cerp->replay == NULL)
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Current replay element does not have a playing replay, starting one.");
					replayStarted = replay_manager->PlayReplay(cerp->replay_rewind_amount, cerp->replay_duration, cerp->camera_mode, cerp->replay_type, cerp->played_count++);
				}
				else
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Current replay element has a saved replay, starting that.");
					replay_manager->PlaySavedReplay(cerp->replay, cerp->camera_mode);
				}

				if (replayStarted)
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Replay started successfully, setting the flag for when we finished and broadcasting delegates.");
					replay_finished = false;
					game->GetCameraManager()->GetDirector()->SetReplay(true);
					game->GetEvents()->replay_playback_start();
				}
				else
				{
					wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Replay failed to start, stopping this element.");
					replay_finished = true;
					element_finished_immediately = true;
					game->GetCameraManager()->GetDirector()->SetReplay(false);
					replay_manager->StopReplay();
				}
			}
			break;

			//case CUTSCENE_TYPE_MONITOR_CALLBACK: //commented in RC3
			//	{
			//		CutsceneMonitorCallbackFunction fptr = (*cur_iter).monitor_callback_element;
			//		element_finished_immediately = fptr(this,&(*cur_iter),CS_MONITOR_INIT, request_element_finish);
			//	}
			//	break;

			case CUTSCENE_TYPE_CALLBACK:
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Current type is callback, grabbing the function and calling it.");
				CutsceneCallbackElement* cecb = (CutsceneCallbackElement*)*cur_iter;
				//MABLOGDEBUG("Cutscene callback (next): %s",cecb->debug_name);
				CutsceneCallbackFunction fptr = cecb->callback;
				element_finished_immediately = fptr(this, cecb);
			}
			break;

			case CUTSCENE_TYPE_USER_SKIP_POINT:
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement Current type is a user skip point, resetting the multi skip flag..");
				multi_skip = false;			// Reset multi_skip.
				element_finished_immediately = true;
			}
			break;

			default:
			{
				wwNETWORK_TRACE_JG("SSCutSceneManager::StartNextCutsceneElement No type found for current cutscene!.");
				element_finished_immediately = true;
				break;
			}
			}
		}

	} while (element_finished_immediately);

}


///-------------------------------------------------------------------------------
/// Clear all roles + reset official roles
///-------------------------------------------------------------------------------

void SSCutSceneManager::ClearAllRolesExceptOfficials()
{
	game->GetStrategyHelper()->ClearRoles();		// SIDE_A + SIDE_B

	/// Reset animation networks.

	SIFRugbyCharacterList players = game->GetPlayers();
	for (MabVector<ARugbyCharacter*>::const_iterator i = players.begin(); i != players.end(); ++i)
	{
		ARugbyCharacter* player = *i;
		/*#rc3_legacy
		NMMabAnimationNetwork *network = player->GetComponent<NMMabAnimationNetwork>();
		if(network)
			network->Reset();
		*/
		{
			player->GetMovement()->StopAllMovement();
			if (player->GetAnimation())
			{
				player->GetAnimation()->Reset();
				player->GetAnimation()->GetStateMachine().Reset();
			}
		}
	}
}

///-------------------------------------------------------------------------------
/// De-register exclusion zone (called when ever a cutscene fininshes).
///-------------------------------------------------------------------------------

void SSCutSceneManager::DeregisterExclusionZone()
{
	if (exclusion_zone_on)
	{
		game->GetMovement()->UnregisterStaticCollidable(this);
		exclusion_zone_on = false;
	}
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

bool SSCutSceneManager::IsCinematicRunning(const bool bIgnoreDelay/* = false*/)
{
	if (IsSimulationDisabled())
		return true;

	if (!cutscene_elements.empty())
	{
		for (MabVector<CutsceneElement*>::iterator it = cutscene_elements.begin(); it != cutscene_elements.end(); it++)
		{
			CutsceneElement* ce = (*it);
			if (ce->cutscene_type == CUTSCENE_TYPE_DELAY && false == bIgnoreDelay)		// If delay element, break.
				break;
			if (ce->cutscene_type == CUTSCENE_TYPE_CINEMATIC || ce->cutscene_type == CUTSCENE_TYPE_REPLAY)
				return true;
		}
	}
	return false;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

bool SSCutSceneManager::CineMaticHasBallProp()
{
	if (!cutscene_elements.empty())
	{
		for (MabVector<CutsceneElement*>::iterator it = cutscene_elements.begin(); it != cutscene_elements.end(); it++)
		{
			CutsceneElement* ce = (*it);
			if (ce->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
			{
				CutsceneCinematicElement* cce = (CutsceneCinematicElement*)ce;

				for (auto& it2 : cce->cinematics)
				{
					if (it2.CinematicElementInfoData.state == CUTSCENE_STATE::CST_RUNNING)
					{
						if (it2.CinematicElementInfoData.HasBallProp == true)
						{
							return true;
						}
					}//if
				}//for				
			}//if
		}//for
	}//if
	return false;
}


///-------------------------------------------------------------------------------
/// CutsceneElement : Constructor
///-------------------------------------------------------------------------------

SSCutSceneManager::CutsceneElement::CutsceneElement()
	: uid(-2)
	, cutscene_type(CUTSCENE_TYPE_USER_SKIP_POINT)
	, swipe_used(SWIPE_FADE)
	, skipable(false)
	, delay(0.0f)
	, disable_simulation(true)
	, wipe_active(false)
	, wait_load(true)
{
}


///-------------------------------------------------------------------------------
/// CutsceneCinematicElement : Constructor
///-------------------------------------------------------------------------------

SSCutSceneManager::CutsceneCinematicElement::CutsceneCinematicElement()
	: CutsceneElement()
	, cinematics()
	, focus_team(NULL)
	, game_phase_override(RUGamePhase::PLAY)
	, hide_non_actors(false)
{
}

///-------------------------------------------------------------------------------
/// Called from with in 'CUTSCENE_TYPE_LOAD_WAIT' element processing to start loading
/// of all cinematics belonging to element.
///-------------------------------------------------------------------------------

void SSCutSceneManager::CutsceneCinematicElement::PreLoadCutScenes(SSCutSceneManager* manager)
{
	bool is_dual = false;
	for (MabVector<StCinematicElementInfo>::iterator it = cinematics.begin(); it != cinematics.end(); it++)
	{
		StCinematicElementInfo* ci = &(*it);

		if (ci->CinematicElementInfoData.request_id != CSEVENT_NAMED_CUTSCENE)
			manager->LoadBestCutScene(ci->CinematicElementInfoData.request_id, true, is_dual, ci->CinematicElementInfoData.side, ci->CinematicElementInfoData.CutSceneData);
		is_dual = true;
	}
}

///-------------------------------------------------------------------------------
/// Start loaded cutscenes, and if not loaded load and start them immediately.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::CutsceneCinematicElement::StartLoadedCutScenes(SSCutSceneManager* manager)
{
	if (this->cutscene_type != CUTSCENE_TYPE_BACKGROUND_CINEMATIC && manager->multi_skip)
	{
		for (MabVector<StCinematicElementInfo>::iterator it = cinematics.begin(); it != cinematics.end(); it++)
		{
			StCinematicElementInfo* ci = &(*it);
			manager->StopSequencer(ci);
			/*if(ci->cutscene)
			{
				manager->DeleteCutScene(ci->cutscene);
			}*/
		}
		return false;
	}

	bool main = true;
	for (MabVector<StCinematicElementInfo>::iterator it = cinematics.begin(); it != cinematics.end(); it++)
	{
		StCinematicElementInfo* ci = &(*it);
		bool isplaying = (ci && UOBJ_IS_VALID(ci->UCutscenePtr) && UOBJ_IS_VALID(ci->UCutscenePtr->m_SequencePlayer) && ci->UCutscenePtr->m_SequencePlayer->IsPlaying());
		manager->StartLoadedCutScene(ci->CinematicElementInfoData.CutSceneData, ci->CinematicElementInfoData.request_id, ci->CinematicElementInfoData.transform, this->hide_non_actors, !main, ci->CinematicElementInfoData.side, TCHAR_TO_ANSI(*ci->CinematicElementInfoData.filename),
			ci->CinematicElementInfoData.is_non_actor, ci->CinematicElementInfoData.xclip_min, ci->CinematicElementInfoData.xclip_max, isplaying);

		/// Setup exclusion zones for non-cutscene players.

		if (/*ci->cutscene &&*/ main)
			main = false;

		/// Don't disable simulation for background cutscenes.
		if (this->cutscene_type == CUTSCENE_TYPE_BACKGROUND_CINEMATIC/* && ci->cutscene*/)
		{
			//ci->cutscene->SetDisableSimulation(false);//#rc3_legacy
			//ci->cutscene->SetHideBall(false);
			ci->CinematicElementInfoData.HideBall = false;
		}
	}

	return true;
}


///-------------------------------------------------------------------------------
/// Add a cinematic
///-------------------------------------------------------------------------------

void SSCutSceneManager::CutsceneCinematicElement::AddCinematic(int request_id, const MabMatrix& transform, SSTEAMSIDE side)
{
	StCinematicElementInfo ci;

	ci.CinematicElementInfoData.request_id = request_id;
	ci.CinematicElementInfoData.filename = "";
	ci.CinematicElementInfoData.transform = transform;
	//ci.cutscene = NULL;
	ci.CinematicElementInfoData.side = side;
	ci.CinematicElementInfoData.is_non_actor = false;
	ci.CinematicElementInfoData.xclip_min = 0.0f;
	ci.CinematicElementInfoData.xclip_max = 0.0f;
	ci.CinematicElementInfoData.HideNonActors = this->hide_non_actors;
	cinematics.push_back(ci);
}

///-------------------------------------------------------------------------------
/// Add a cinematic
///-------------------------------------------------------------------------------

void SSCutSceneManager::CutsceneCinematicElement::AddBackgroundCinematic(int request_id,
	const MabMatrix& transform,
	SSTEAMSIDE side,
	float xclip_min,
	float xclip_max)
{
	StCinematicElementInfo ci;

	ci.CinematicElementInfoData.request_id = request_id;
	ci.CinematicElementInfoData.filename = "";
	ci.CinematicElementInfoData.transform = transform;
	//ci.cutscene = NULL;
	ci.CinematicElementInfoData.side = side;
	ci.CinematicElementInfoData.is_non_actor = true;
	ci.CinematicElementInfoData.xclip_min = xclip_min;
	ci.CinematicElementInfoData.xclip_max = xclip_max;
	ci.CinematicElementInfoData.HideNonActors = this->hide_non_actors;
	cinematics.push_back(ci);
}

///-------------------------------------------------------------------------------
/// Add a named cinematic
///-------------------------------------------------------------------------------

void SSCutSceneManager::CutsceneCinematicElement::AddNamedCinematic(const char* file_name, const MabMatrix& transform, SSTEAMSIDE side)
{
	StCinematicElementInfo ci;

	ci.CinematicElementInfoData.request_id = CSEVENT_NAMED_CUTSCENE;
	ci.CinematicElementInfoData.filename = file_name;
	ci.CinematicElementInfoData.transform = transform;
	//	ci.cutscene = NULL;
	ci.CinematicElementInfoData.side = side;
	ci.CinematicElementInfoData.is_non_actor = false;
	ci.CinematicElementInfoData.xclip_min = 0.0f;
	ci.CinematicElementInfoData.xclip_max = 0.0f;
	ci.CinematicElementInfoData.HideNonActors = this->hide_non_actors;
	cinematics.push_back(ci);
}

///-------------------------------------------------------------------------------
/// Get 'main' cutscene (first in cinematics)
///-------------------------------------------------------------------------------

SSCutSceneManager::StCinematicElementInfo* SSCutSceneManager::CutsceneCinematicElement::GetMainCutScene()
{
	MABASSERT(!cinematics.empty());

	if (cinematics.empty())
		return NULL;

	return &cinematics[0];
}

void SSCutSceneManager::CutsceneCinematicElement::StopMainCutScene(SSCutSceneManager* manager)
{
	MABASSERT(!cinematics.empty());
	if (cinematics.empty())
		return;

	//manager->StopCutScene(cinematics[0].cutscene);
	//cinematics[0].cutscene = NULL;
	manager->StopSequencer(&cinematics[0]);
}

//void SSCutSceneManager::CutsceneCinematicElement::PauseMainCutScene(SSCutSceneManager *manager)
//{
//	MABASSERT(!cinematics.empty());
//	if (cinematics.empty())
//		return;
//	
//	manager->PauseSequencer(&cinematics[0], CUTSCENE_STATE::CST_PRE_DELETE);
//}

///-------------------------------------------------------------------------------
/// CutsceneReplayElement : Constructor
///-------------------------------------------------------------------------------

SSCutSceneManager::CutsceneReplayElement::CutsceneReplayElement()
	: CutsceneElement(),
	camera_mode(REPLAY_CAMERA_MODE_MAIN),
	replay_rewind_amount(0.0f),
	replay_duration(0.0f),
	replay(NULL),
	replay_type(RT_UNKNOWN),
	repeat_count(0),
	played_count(0)
{
}

///-------------------------------------------------------------------------------
/// CutsceneCallbackElement : Constructor
///-------------------------------------------------------------------------------

SSCutSceneManager::CutsceneCallbackElement::CutsceneCallbackElement() : CutsceneElement(), callback(NULL), user_info(0), callback_state(0), debug_name(NULL) {}

///-------------------------------------------------------------------------------
/// Adds a background cinematic - play is started then forgotten about.
///-------------------------------------------------------------------------------

void SSCutSceneManager::AddBGCutsceneCinematicElement(int request_id, const MabMatrix& transform) //This is never called. So no use of it.
{
	MABASSERTMSG(cutscene_elements.size() < MAX_CUTSCENE_ELEMENTS, "Too many cutscene elements added.  Please increase the max size at the top of RL3CutsceneManager.cpp");

	CutsceneCinematicElement* new_cutscene = MabMemNew(SIFHEAP_DYNAMIC) CutsceneCinematicElement();

	new_cutscene->uid = ++cs_uid;
	new_cutscene->cutscene_type = CUTSCENE_TYPE_BACKGROUND_CINEMATIC;
	new_cutscene->disable_simulation = false;
	new_cutscene->skipable = false;
	new_cutscene->swipe_used = SWIPE_NONE;
	new_cutscene->game_phase_override = RUGamePhase::NONE;
	new_cutscene->hide_non_actors = false;

	cutscene_elements.push_back(new_cutscene);

	new_cutscene->AddCinematic(request_id, transform, SIDE_NONE);
}


///-------------------------------------------------------------------------------
/// HUDInfoMessageCallbackElement : Constructor
///-------------------------------------------------------------------------------

SSCutSceneManager::HUDInfoMessageCallbackElement::HUDInfoMessageCallbackElement()
	: CutsceneCallbackElement()
	, player_db_id(0)
	, team(NULL)
	, info()
	, additional_info()
	, player(NULL)
{
}


///-------------------------------------------------------------------------------
/// Adds a hud player info callback element to the list
///-------------------------------------------------------------------------------

void SSCutSceneManager::AddHUDInfoCallbackElement(unsigned int player_db_id, const MabString& additional_info, RUTeam* team, const MabString& info, ARugbyCharacter* player)
{
	MABASSERTMSG(cutscene_elements.size() < MAX_CUTSCENE_ELEMENTS, "Too many cutscene elements added.  Please increase the max size at the top of SSCutsceneManager.cpp");

	HUDInfoMessageCallbackElement* new_hudinfo = MabMemNew(SIFHEAP_DYNAMIC) HUDInfoMessageCallbackElement();

	// cutscene callback params, set with specific params for hte HUD callback
	new_hudinfo->uid = ++cs_uid;
	new_hudinfo->cutscene_type = CUTSCENE_TYPE_CALLBACK;
	new_hudinfo->callback = SSCutSceneManager::ShowNextPlayerInfo;
	new_hudinfo->user_info = 0;
	new_hudinfo->debug_name = "ShowNextPlayerInfoCallback";

	// hud info params
	new_hudinfo->player_db_id = player_db_id;
	new_hudinfo->team = team;
	new_hudinfo->info = info;
	new_hudinfo->additional_info = additional_info;
	new_hudinfo->player = player;

	cutscene_elements.push_back(new_hudinfo);
}


///-------------------------------------------------------------------------------
/// Adds a cutscene cinematic element to the list
///-------------------------------------------------------------------------------

void SSCutSceneManager::AddCutsceneCinematicElement(RUGamePhase override_phase, bool user_skipable, SWIPE_TYPE swipe_used, bool hide_non_actors)
{
	MABASSERTMSG(cutscene_elements.size() < MAX_CUTSCENE_ELEMENTS, "Too many cutscene elements added.  Please increase the max size at the top of RL3CutsceneManager.cpp");

	CutsceneCinematicElement* new_cutscene = MabMemNew(SIFHEAP_DYNAMIC) CutsceneCinematicElement();

	new_cutscene->uid = ++cs_uid;
	new_cutscene->cutscene_type = CUTSCENE_TYPE_CINEMATIC;
	new_cutscene->disable_simulation = true;
	new_cutscene->skipable = user_skipable;
	new_cutscene->swipe_used = swipe_used;
	new_cutscene->game_phase_override = override_phase;
	new_cutscene->hide_non_actors = hide_non_actors;

	last_created_element = new_cutscene;

	cutscene_elements.push_back(new_cutscene);
}

///-------------------------------------------------------------------------------
/// Add a cinematic to the last created cutscene element.
///-------------------------------------------------------------------------------

void SSCutSceneManager::AddCinematicTLE(int request_id, const MabMatrix& transform, SSTEAMSIDE side)
{
	//	UE_LOG(LogNetwork, Display, TEXT("AddCinematicTLE :: Adding requested cutscene ID: %d, for side %d"), request_id, (int32)side);
	GEngine->ForceGarbageCollection(false);

	last_created_element->AddCinematic(request_id, transform, side);
}

///-------------------------------------------------------------------------------
/// Add a 'background' cinematic to the last created cutscene element.
///-------------------------------------------------------------------------------

void SSCutSceneManager::AddBackgroundCinematicTLE(int request_id, const MabMatrix& transform, SSTEAMSIDE side, float xclip_min, float xclip_max)
{
	last_created_element->AddBackgroundCinematic(request_id, transform, side, xclip_min, xclip_max);
}


///-------------------------------------------------------------------------------
/// Add a named cinematic to the last created cutscene element.
///-------------------------------------------------------------------------------
void SSCutSceneManager::AddNamedCinematicTLE(const char* file_name, const MabMatrix& transform, SSTEAMSIDE side)
{
	last_created_element->AddNamedCinematic(file_name, transform, side);
}

///-------------------------------------------------------------------------------
/// Adds a callback element to the list
///-------------------------------------------------------------------------------

void SSCutSceneManager::AddCutsceneCallbackElement(CutsceneCallbackFunction callback, const char* debug_name, int user_info)
{
	//MABASSERTMSG(cutscene_elements.size() < MAX_CUTSCENE_ELEMENTS, "Too many cutscene elements added.  Please increase the max size at the top of RL3CutsceneManager.cpp");

	if (cutscene_elements.size() >= MAX_CUTSCENE_ELEMENTS)
	{
		UE_LOG(LogTemp, Error, TEXT("Too many cutscene elements added.  Please increase the max size at the top of RL3CutsceneManager.cpp"));
	}

	CutsceneCallbackElement* new_cutscene = MabMemNew(SIFHEAP_DYNAMIC) CutsceneCallbackElement();

	new_cutscene->uid = ++cs_uid;
	new_cutscene->cutscene_type = CUTSCENE_TYPE_CALLBACK;
	new_cutscene->callback = callback;
	new_cutscene->user_info = user_info;
	new_cutscene->debug_name = debug_name;

	cutscene_elements.push_back(new_cutscene);
}

///-------------------------------------------------------------------------------
/// Adds a user skip point element to the list.
///-------------------------------------------------------------------------------

void SSCutSceneManager::AddUserSkipPointElement()
{
	MABASSERTMSG(cutscene_elements.size() < MAX_CUTSCENE_ELEMENTS, "Too many cutscene elements added.  Please increase the max size at the top of RL3CutsceneManager.cpp");

	if (cutscene_elements.size() == 0)		// WHY???
		return;

	CutsceneElement* new_cutscene = MabMemNew(SIFHEAP_DYNAMIC) CutsceneElement();

	new_cutscene->uid = ++cs_uid;
	new_cutscene->cutscene_type = CUTSCENE_TYPE_USER_SKIP_POINT;

	cutscene_elements.push_back(new_cutscene);
}

///-------------------------------------------------------------------------------
/// Add a screen wipe (done for first element in sequence).
///-------------------------------------------------------------------------------

void SSCutSceneManager::AddScreenWipeElement(SWIPE_TYPE swipe_used)
{
	MABASSERTMSG(cutscene_elements.size() < MAX_CUTSCENE_ELEMENTS, "Too many cutscene elements added.  Please increase the max size at the top of RL3CutsceneManager.cpp");

	CutsceneElement* new_cutscene = MabMemNew(SIFHEAP_DYNAMIC) CutsceneElement();

	new_cutscene->uid = ++cs_uid;
	new_cutscene->cutscene_type = CUTSCENE_TYPE_SCREEN_WIPE;
	new_cutscene->swipe_used = swipe_used;

	cutscene_elements.push_back(new_cutscene);
}

///-------------------------------------------------------------------------------
/// Adds an infringement replay element to the list
/// Has extra smarts to not add the element if the infringement happened too long ago
/// that we don't have enough replay time or don't care
///-------------------------------------------------------------------------------

void SSCutSceneManager::AddInfringementReplayElement(float max_rewind_time, float replay_duration, REPLAY_TYPE type)
{
	//RussellD : potential future fix if (type == RT_KNOCK_ON)
	{
		/// If the infringement event recorded when advantage was started is too far back in the past (before the reply length)
		/// then we just ignore adding an element
		const static float PRE_INFRINGMENT_BUFFER = 2.5f;
		float advantage_rewind_time = advantage_last_started_time >= 0.0f ? game->GetSimTime()->GetAbsoluteTime().ToSeconds() - advantage_last_started_time + PRE_INFRINGMENT_BUFFER : 1e10f;
		if (advantage_rewind_time < 0 || advantage_rewind_time > max_rewind_time)
		{
			return;
		}

		max_rewind_time = advantage_rewind_time;
	}

	{
		AddScreenWipeElement(SWIPE_WIPE);

		CutsceneReplayElement* elem = AddCutsceneReplayElement(
			NULL, // OLD advantage_inaffect ? advantage_replay : NULL,
			REPLAY_CAMERA_MODE_MAIN,
			max_rewind_time, // OLD advantage_inaffect ? 0.0f : advantage_rewind_time,
			type,
			SWIPE_WIPE
		);

		if (elem != NULL)
			elem->replay_duration = replay_duration;

		AddUserSkipPointElement();
	}
}

///-------------------------------------------------------------------------------
/// Adds a cutscene replay element to the list
///-------------------------------------------------------------------------------

SSCutSceneManager::CutsceneReplayElement* SSCutSceneManager::AddCutsceneReplayElement(SSReplayBlock* replay, REPLAY_CAMERA_MODE camera_mode, float rewind_amount, REPLAY_TYPE type, SWIPE_TYPE swipe_used, int num_replays)
{
	MABASSERTMSG(cutscene_elements.size() < MAX_CUTSCENE_ELEMENTS, "Too many cutscene elements added.  Please increase the max size at the top of RL3CutsceneManager.cpp");

	// If replay is NULL then try and start replay from first play restart event
	if (replay == NULL)
	{
		float event_age = rewind_amount;		//GetAgeOfYoungestEvent( replay_length );
		if (event_age != 0.0f)
			rewind_amount = event_age;
	}
	/*else
	{
		replay->SetType( type );
	}
	*///#rc3_legacy_replay

	SSReplayManager* replay_manager = game->GetReplayManager();
	if (replay_manager == NULL ||
		(replay_manager->GetSecondsOfReplayData() == 0.0f && replay == NULL))
		return NULL;


	CutsceneReplayElement* new_cutscene = MabMemNew(SIFHEAP_DYNAMIC) CutsceneReplayElement();

	new_cutscene->uid = ++cs_uid;
	new_cutscene->cutscene_type = CUTSCENE_TYPE_REPLAY;
	new_cutscene->replay = replay;
	new_cutscene->replay_type = type;
	new_cutscene->replay_rewind_amount = rewind_amount;
	new_cutscene->camera_mode = camera_mode;
	new_cutscene->swipe_used = swipe_used;
	new_cutscene->skipable = true;
	new_cutscene->repeat_count = num_replays;

	MABASSERT((replay == NULL && rewind_amount > 0.0) || (replay != NULL && rewind_amount == 0.0));

	cutscene_elements.push_back(new_cutscene);
	return new_cutscene;
}

///-------------------------------------------------------------------------------
/// Adds a cutscene delay element to the list
///-------------------------------------------------------------------------------

void SSCutSceneManager::AddCutsceneDelayElement(int delay)
{
	const float DELAY_UNIT = 1.0f / 30.0f;

	MABASSERTMSG(cutscene_elements.size() < MAX_CUTSCENE_ELEMENTS, "Too many cutscene elements added.  Please increase the max size at the top of RL3CutsceneManager.cpp");

	CutsceneElement* new_cutscene = MabMemNew(SIFHEAP_DYNAMIC) CutsceneElement();

	new_cutscene->uid = ++cs_uid;
	new_cutscene->cutscene_type = CUTSCENE_TYPE_DELAY;

	new_cutscene->delay = delay * DELAY_UNIT;
	cutscene_elements.push_back(new_cutscene);
}

///-------------------------------------------------------------------------------
/// Adds a cutscene async preload element to the list
///-------------------------------------------------------------------------------

void SSCutSceneManager::AddCutsceneAsyncPreloadElement(bool wait)
{
	MABASSERTMSG(cutscene_elements.size() < MAX_CUTSCENE_ELEMENTS, "Too many cutscene elements added.  Please increase the max size at the top of RL3CutsceneManager.cpp");

	CutsceneElement* new_cutscene = MabMemNew(SIFHEAP_DYNAMIC) CutsceneElement();

	new_cutscene->uid = ++cs_uid;
	new_cutscene->cutscene_type = CUTSCENE_TYPE_LOAD_WAIT;
	new_cutscene->wait_load = wait;

	cutscene_elements.push_back(new_cutscene);
}

///-------------------------------------------------------------------------------
/// Adds a sync-preload 'STOP' element (preload won't load past this).
///-------------------------------------------------------------------------------

void SSCutSceneManager::AddCutSceneStopAsyncPreloadElement()
{
	MABASSERTMSG(cutscene_elements.size() < MAX_CUTSCENE_ELEMENTS, "Too many cutscene elements added.  Please increase the max size at the top of RL3CutsceneManager.cpp");

	CutsceneElement* new_cutscene = MabMemNew(SIFHEAP_DYNAMIC) CutsceneElement();

	new_cutscene->uid = ++cs_uid;
	new_cutscene->cutscene_type = CUTSCENE_TYPE_LOAD_STOP;

	cutscene_elements.push_back(new_cutscene);
}

///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------
void SSCutSceneManager::HandoverCutscene()
{
	if(game->GetScreenWipeManager())
	{
		game->GetScreenWipeManager()->StartWipe(1.0f, SWIPE_WIPE);
	}
	SetupInterchange();
}

///-------------------------------------------------------------------------------
/// Select the best cutscene to load.
///-------------------------------------------------------------------------------

void SSCutSceneManager::LoadBestCutScene(int csevent, bool async, bool is_dual, SSTEAMSIDE team_side, FCutSceneData& CutSceneData)
{
	MABASSERT((csevent & CSEVENT_LOADING_MASK) >= 0 && (csevent & CSEVENT_LOADING_MASK) < NUM_CSEVENTS);

	int csload_event = csevent;

	CutSceneData.CutSceneID = csload_event & CSEVENT_LOADING_MASK;

	/*if (CutSceneData.CutSceneID == CSEVENT_SCREEN_WIPE)
	{
		CutSceneData.CutSceneFileName = "/Game/Rugby/Cutscenes/FadeIn";
		return;
	}*/

	float total_weight = 0.0f;

	int num_cs = 0;

	int IsRugbySeven = 0;

	//char * folderSwitch = (char *)"";

#ifdef ENABLE_SEVENS_MODE
	// Are we playing a R7 game? Does this cutscene have a special variation for R7's?
	// Nick  WWS 7s to Womens //if (SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetGameMode() == GAME_MODE_SEVENS && cs_info[csload_event & CSEVENT_LOADING_MASK].R7Variation)
	//{
		//folderSwitch = (char *)"/rugby_sevens";
	//	IsRugbySeven = 1;
	//}
#endif

	TArray<FCutsceneRec*> pCutsceneRec = GetCutsceneRec(csload_event & CSEVENT_LOADING_MASK, IsRugbySeven);
	TArray <FString> files;

	for (const auto& it : pCutsceneRec)
	{
		if (it->m_sequenceSoftPath.IsValid())
		{
			FString FName = it->m_sequenceSoftPath.GetAssetPathString();
			FName = FName.Replace(TEXT("/Game/Rugby/CutScenes"), TEXT("ruged/cutscenes"));
			FName += ".json";
			//CutsceneDataPtr = evds::construct_cutscene(FName);

			if (game->GetGameSettings().game_settings.network_game)
			{
				if (FName.Contains("cs_kfpsuc_e5_04fsi")
					|| FName.Contains("cs_kfpbg_01")
					|| FName.Contains("cs_genreac_posbg_01")
					|| FName.Contains("cs_plf_e05_02"))
				{
					continue;
				}
			}

			files.Add(FName);
		}
	}

	int emotion_level = DEFAULT_EMOTION_LEVEL;

	bool use_if_exact_match = false;

	TArray <int> exact_match_ids;

	if (game->IsSandbox() && game->GetTutorialManager() && game->GetTutorialManager()->GetCurrentTutorial())
	{
		// There are cutscene variations based on emotion level, but emotion level from within a tutorial don't make any sense.
		// Instead override it with the medal they have in that particular tutorial.
		const RUTutorial* tutorial = game->GetTutorialManager()->GetCurrentTutorial();
		RUTutorialMedal medal = tutorial->GetMedal();
		bool success = game->GetTutorialManager()->GetCurrentTutorial()->GetSuccessState();

		// Map medals [0,1,2,3] to emotion levels [3,5,7,9] if successful
		if (success)
		{
			emotion_level = static_cast<int> (medal) * 2 + 3;
			use_if_exact_match = true;
		}
		// On fail use the middle value of 3
		else
		{
			emotion_level = 3;
		}

		MabMath::Clamp(emotion_level, 0, 9);
	}
	else if (team_side == SIDE_NONE)
	{
		/// For 'SIDE_NONE' - fireworks etc.. Get the emotion level from the match setting.
		emotion_level = GetMatchEmotionLevel();
		use_if_exact_match = true;

#ifdef ENABLE_GAME_DEBUG_MENU
		//#rc3_legacy_debug_cutscene
		/*int debug_emotion_level = SIFDebug::GetCutsceneDebugSettings()->GetCutsceneDebugMatchLevel();
		if (debug_emotion_level > 0)
		{
			emotion_level = debug_emotion_level;
		}*/
#endif
	}
	else if (game->GetEmotionEngineManager() && (team_side == SIDE_A || team_side == SIDE_B))
	{
		int region = (int)game->GetEmotionEngineManager()->GetShameAndGloryManager()->GetShameAndGlory(team_side);
		if (region >= 0 && region < NUM_SHAME_AND_GLORY_REGIONS)
		{
			float t = (float)region / (float)(NUM_SHAME_AND_GLORY_REGIONS - 1);
			emotion_level = MIN_EMOTION_LEVEL + (int)(t * (float)(MAX_EMOTION_LEVEL - MIN_EMOTION_LEVEL));
		}
		/// Force cutscenes to be selected at a specified emotion level.
#ifdef ENABLE_GAME_DEBUG_MENU
		//#rc3_legacy_debug_cutscene
		/*int debug_emotion_level = SIFDebug::GetCutsceneDebugSettings()->GetCutsceneDebugEmotionLevel();
		if (debug_emotion_level > 0)
		{
			emotion_level = debug_emotion_level;
		}*/
#endif
	}

	//MABLOGDEBUG("CutScene (%d): Emotion level = %d",csload_event, emotion_level);	

	FString* last_filename = last_filename_cache.Find(csload_event);

	bool playInOrder = csevent == CSEVENT_UI_MAIN_MENU || csevent == CSEVENT_UI_TITLE;
	TArray<float> cutscene_weights;
	int selected_cutscene = -1;

	if (playInOrder)
	{
		selected_cutscene = 0;
		// We've played a cutscene before. Try to get the next one
		if (last_filename && *last_filename != "")
		{
			for (int i = 0; i < files.Num(); i++)
			{
				FString filename = files[i];
				if (filename == *last_filename)
				{
					selected_cutscene = i + 1;
					if (selected_cutscene >= files.Num())
					{
						selected_cutscene = 0;
					}

					break;
				}
			}
		}
	}
	else
	{
		for (int i = 0; i < files.Num(); i++)
		{
			//MabString fname = *iter;
			//const char *filename = TCHAR_TO_ANSI(*iter);
			FString filename = files[i];
			float weight = 0.0f;
			const char* em_str = strstr(TCHAR_TO_ANSI(*filename), "_e");
			int	 diff = 2;
			char cn;
			if (em_str && strlen(em_str) > 2 && (cn = em_str[2]) >= '0' && cn <= '9')
			{
				int number = (int)(cn - '0');
				MabMath::Clamp(number, 0, 9);			// Make sure its a digit 0-9.

				diff = number - emotion_level;
				diff = (diff >= 0) ? diff : -diff;
			}

			weight = 1.0f / (float)(1 << (diff * 2));		// Exponential decrease probability.

			if (last_filename && (filename == *last_filename))
			{
				weight = 0.0f;
			}

			// If 'SIDE_NONE' then always use exact match (diff==0) 
			if (use_if_exact_match && diff == 0 && weight > 0.0f)
			{
				exact_match_ids.Add(num_cs);
			}
			//MABLOGDEBUG("-- %s : %f",filename, weight);
			cutscene_weights.Add(weight);
			total_weight += weight;
			num_cs++;
		}
	}

	wwNETWORK_TRACE_JG("Choosing cutscene: Number of cutscenes (%d) Exact Matches (%d)", num_cs, exact_match_ids.Num());

	if (exact_match_ids.Num())
	{
		if (exact_match_ids.Num() == 1)
		{
			selected_cutscene = exact_match_ids[0];
		}
		else
		{
			int val = game->GetRNG()->RAND_RANGED_CALL(int, (int)exact_match_ids.Num());
			MabMath::Clamp(val, 0, (int)exact_match_ids.Num() - 1);
			selected_cutscene = exact_match_ids[val];
		}
	}
	else if (num_cs == 1)
	{
		selected_cutscene = 0;
	}
	else if (num_cs > 1)
	{
		float val = game->GetRNG()->RAND_RANGED_CALL(float, total_weight);
		float cw = 0.0f;
		for (int i = 0; i < num_cs; i++)
		{
			cw += cutscene_weights[i];
			if (val <= cw)
			{
				selected_cutscene = i;
				break;
			}
		}

		if (selected_cutscene == -1)	// Just in case.
		{
			selected_cutscene = num_cs - 1;
		}

	}

	//MabMemArrayDelete(cutscene_weights);

	if (selected_cutscene != -1)
	{
		int idx = 0;
		for (const auto& iter : files)
		{
			if (idx == selected_cutscene)
			{
				if (last_filename)
				{
					*last_filename = iter;
				}
				FString FileName = iter;
				CutSceneData.CutSceneIndex = idx;
				CutSceneData.CutSceneFileName = FileName;
				return;
			}
			idx++;
		}
	}

	// WILL CRASH IF GET TO HERE!
	MABBREAKMSG("NO CUTSCENE FOUND!");
}

FCutSceneData* SSCutSceneManager::LoadCutScene(FCutSceneData CutsceneDataFromJson, FString FolderName, FString Directory, FString FileName)
{
	MABBREAKMSG("SSCutSceneManager::LoadCutScene Failed!");

	return nullptr;
}

///-------------------------------------------------------------------------------
/// Select the best cutscene to load.
///-------------------------------------------------------------------------------

//std::unique_ptr<SSCutSceneManager::StMabEVDSCutScene> SSCutSceneManager::LoadNamedCutScene(const char *filename, bool async, bool is_dual, SSTEAMSIDE team_side)
//{
//	MABUNUSED(team_side);
//
//	StMabEVDSCutScene *cs = nullptr;/*= LoadCutScene(filename, async);
//	if(cs == NULL) return NULL;
//	cs->SetTag(CSEVENT_NAMED_CUTSCENE);
//	if(is_dual)
//		cs->SetDual();*/
//
//	return cs;
//}

///-------------------------------------------------------------------------------
/// Start an already loaded cutscene
///   - if it isn't loaded then it is loaded non-async + auto started.
///-------------------------------------------------------------------------------

void SSCutSceneManager::StartLoadedCutScene(FCutSceneData& cs,
	int csevent,
	MabMatrix transform,
	bool hide_non_actors,
	bool is_dual,
	SSTEAMSIDE side,
	const char* file_name,
	bool is_non_actor,
	float xclip_min,
	float xclip_max,
	bool IsRunning)
{
	if (cs.CutSceneID > 0)
	{
		//if (cs.SequenceActor == nullptr)
		if (!IsRunning)
		{
			PlaySequence(&cs);
		}
	}

	/// Cutscene hasn't been loaded/or attempted to be loaded.
	else if (csevent != CSEVENT_NAMED_CUTSCENE)
	{
		LoadBestCutScene(csevent, false, is_dual, side, cs);

		if (cs.CutSceneID > 0)
		{
			PlaySequence(&cs);
		}
		/*else
		{
			MABLOGDEBUG("SSCutSceneManager::StartLoadedCutScene No cutscene to start");
		}*/
	}
	else
	{
		MABBREAKMSG("We should not reach here");
	}
}

///-------------------------------------------------------------------------------
/// Is a cutscene with tag 'tag' currently running (have already started it)
///-------------------------------------------------------------------------------

bool SSCutSceneManager::IsCutSceneWithTagRunning(int tag)
{
	/*for(MabVector<StMabEVDSCutScene*>::iterator iter = cutscenes.begin(); iter != cutscenes.end(); ++iter)
	{
		StMabEVDSCutScene *cs = (*iter);
		if(cs->GetState()==CST_RUNNING && cs->GetTag()==tag)
			return true;
	}*/
	return false;
}

///-------------------------------------------------------------------------------
/// GetCutScene: Find first cutscene with matching 'tag'/'csevent'
///-------------------------------------------------------------------------------

//SSCutSceneManager::StMabEVDSCutScene *SSCutSceneManager::GetCutScene(int csevent) //#rc3_legacy
//{
//	/*for(MabVector<StMabEVDSCutScene*>::iterator iter = cutscenes.begin(); iter != cutscenes.end(); ++iter)
//	{
//		StMabEVDSCutScene *cs = (*iter);
//		if(cs->GetTag()==csevent)
//		{
//			return cs;
//		}
//	}*
//	return NULL;
//}

///-------------------------------------------------------------------------------
/// When actors are CleanUp when switching tutorials, cleanup the player pointers
/// in all cutscenes.
///-------------------------------------------------------------------------------

void SSCutSceneManager::OnCleanupActors() //#rc3_legacy
{
	/*for(MabVector<StMabEVDSCutScene*>::iterator iter = cutscenes.begin(); iter != cutscenes.end(); ++iter)
	{
		StMabEVDSCutScene *cs = (*iter);
		if(cs->IsLoaded())
			cs->OnCleanupActors();
	}*/
}

///-------------------------------------------------------------------------------
/// Time scrub update - called when time is scrubbed back/forward + for every frame
/// during the scrub.
///-------------------------------------------------------------------------------


void SSCutSceneManager::TimeScrubUpdate(int current_frame, int dest_frame)
{
	MABUNUSED(current_frame);
	MABUNUSED(dest_frame);

#ifdef ENABLE_RUGED
	if (current_frame == dest_frame)
	{
		game->CutsceneTimeScrubUpdate();
	}
#endif
}

#ifdef ENABLE_RUGED

///-------------------------------------------------------------------------------
/// Get debug information (fill vectors) for RugEd.
///-------------------------------------------------------------------------------

void SSCutSceneManager::DebugGetInfo(MabVector<MabString>& names, MabVector<int>& state_flags, MabVector<float>& times)
{
	for (MabVector<FCutSceneData*>::iterator iter = cutscenes.begin(); iter != cutscenes.end(); ++iter)
	{
		FCutSceneData* cs = (*iter);
		int flags = 0;

		if (cs->IsRunning())
			flags |= CSDB_RUNNING;

		if (cs->IsDisabledSimulation())
			flags |= CSDB_DISABLE_SIM;

		if (!cs->IsLoaded())
			flags |= CSDB_LOADING;

		names.push_back(cs->GetName());
		times.push_back(cs->GetTime());
		state_flags.push_back(flags);
	}
}

#endif // ENABLE_RUGED


///-------------------------------------------------------------------------------
/// Callback -> when 'evds' data has been modified.
///-------------------------------------------------------------------------------

void SSCutSceneManager::OnDataModified() //#rc3_legacy
{
	//for(MabVector<StMabEVDSCutScene*>::iterator iter = cutscenes.begin(); iter != cutscenes.end(); ++iter)
	//{
	//	StMabEVDSCutScene *cs = (*iter);
	//	//if(cs->GetState()==CST_RUNNING)
	//	//	cs->Tick(cs->GetTimeLine());
	//}
//#ifdef ENABLE_RUGED
//	ClearFrameCache();
//#endif
}


///***************************************************************************************************************************************************
///***************************************************************************************************************************************************
///***************************************************************************************************************************************************

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void SSCutSceneManager::StartEditingEvent(const MabString& name)
{
	MABUNUSED(name);

#ifdef ENABLE_RUGED
	edit_attack_disabled = SIFDebug::GetGameDebugSettings()->GetAttackDisabled();
	edit_defence_disabled = SIFDebug::GetGameDebugSettings()->GetDefenceDisabled();
	edit_ruged_debug_disabled = SIFDebug::GetGameDebugSettings()->GetRugEdDebugDisabled();

	SIFDebug::GetGameDebugSettings()->SetDefenceDisabled(true);
	SIFDebug::GetGameDebugSettings()->SetAttackDisabled(true);
	SIFDebug::GetGameDebugSettings()->SetRugEdDebugDisabled(true);
#endif
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void SSCutSceneManager::StopEditingEvent(const MabString& name)
{
	MABUNUSED(name);

#ifdef ENABLE_RUGED
	SIFDebug::GetGameDebugSettings()->SetDefenceDisabled(edit_attack_disabled);
	SIFDebug::GetGameDebugSettings()->SetAttackDisabled(edit_defence_disabled);
	SIFDebug::GetGameDebugSettings()->SetRugEdDebugDisabled(edit_ruged_debug_disabled);

	//	ClearFrameCache();
#endif
}


///-------------------------------------------------------------------------------
/// Called from StMabEVDSCutScene::PreDelete - game specific stuff for end of cutscene.
///-------------------------------------------------------------------------------
//
//void SSCutSceneManager::CutScenePreDelete(StMabEVDSCutScene *cutscene) //#rc3_legacy
//{
//	MABUNUSED(cutscene);
//	game->GetEffectSystem()->TerminateAll();
//}

///-------------------------------------------------------------------------------
/// Tell the game to use the cutscene camera.
///-------------------------------------------------------------------------------
//#rc3_legacy
//void SSCutSceneManager::SetCamera(
//	const FVector& eye,
//	const FVector& focus,
//	float fov,
//	float near_clip_distance,
//	float roll_angle,
//	bool player_creator_offset_enabled)
//{
//	SSCameraManager *camera_manager = game->GetCameraManager();
//	FVector eye2 = eye;
//	FVector focus2 = focus;
//
//	if(player_creator_offset_enabled)
//	{
//		MabMatrix ya = GetPlayerCreatorTransform();
//		eye2 = ya.TransformPos(eye2);
//		focus2 = ya.TransformPos(focus2);
//	}
//
//#ifdef USING_MAYA_FOCAL_LEN
//	//camera_manager->SetCameraFrom(eye2,focus2, fov, near_clip_distance, roll_angle, true);
//#else
//	camera_manager->SetCameraFrom(eye2,focus2, fov, near_clip_distance, roll_angle);
//#endif
//}

///-------------------------------------------------------------------------------
/// Get container for handycam shake.
///-------------------------------------------------------------------------------

//MabEVDSContainer *SSCutSceneManager::GetHandyCamShakeContainer()
//{
//	/*evds->LoadContainer(HANDY_CAM_SHAKE);
//	MabEVDSContainer* handy_cam = evds->GetContainer(HANDY_CAM_SHAKE);*/
//
//	return handy_cam;
//}

///-------------------------------------------------------------------------------
/// Is 'file_name' a cutscene?
///-------------------------------------------------------------------------------

bool SSCutSceneManager::IsCutSceneFile(const MabString& file_name)
{
	// "ruged/cutscenes/...".
	return (strstr(file_name.c_str(), "ruged/cutscenes") != NULL);
}

///-------------------------------------------------------------------------------
/// Override the player creator cutscene angle (called from UI)
///-------------------------------------------------------------------------------

void SSCutSceneManager::EnablePlayerCreatorAngleAdjustment(bool enabled)
{
	player_creator_on = enabled;
	if (!player_creator_on)
	{
		SetPlayerCreatorAngle(0.0f, true);
		SetPlayerCreatorCameraOffset(FVector::ZeroVector, false, player_creator_use_height_offset);
	}
}

///-------------------------------------------------------------------------------
/// Set the player creator camera offset.
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetPlayerCreatorCameraOffset(FVector offset, bool with_height_offset, bool blend)
{
	player_creator_use_height_offset = with_height_offset;
	player_creator_target_offset = offset;
	if (!blend)
	{
		float height_offset = GetCustomasiationCharacterHeightOffset();
		player_creator_offset = offset + FVector(0.0f, 0.0f, height_offset);
	}
}

///-------------------------------------------------------------------------------
/// Set the player creator camera angle directly.
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetPlayerCreatorAngle(float angle, bool do_blend)
{
	player_creator_camera_target_angle = FRotator(0.0f, angle, 0.0f);

	if (!do_blend)
	{
		player_creator_camera_angle = player_creator_camera_target_angle;
	}
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------
float SSCutSceneManager::GetCustomasiationCharacterHeightOffset()
{
	ARugbyCharacter* player = game->GetCustomisationPlayer();
	if (player)
	{
		PlayerCustomisationInfo CustomisationInfo = player->GetCustomisationInfo();
		float height = CustomisationInfo.m_height;		// 1.0 = DEFAULT_PLAYER_HEIGHT

		return (height - AVERAGE_PLAYER_HEIGHT);
	}

	return 0.0f;
}

///-------------------------------------------------------------------------------
/// Read controller directly to update 'player_creator_camera_angle'
///  Only called when player_creator_on=true.
///-------------------------------------------------------------------------------

void SSCutSceneManager::UpdatePlayerCreatorAngle(float delta_time, bool canPlayRotationSound /*= false*/)
{
	bool play_rotate_player_sound = (player_creator_camera_angle - player_creator_camera_target_angle).Euler().Magnitude() > 0.5f;
	float height_offset = 0.0f;

	if (player_creator_use_height_offset)
	{
		height_offset = GetCustomasiationCharacterHeightOffset();
	}

	//player_creator_offset += ((player_creator_target_offset + height_offset) - player_creator_offset) * 0.25f;

	////SetPlayerCreatorAngle(player_creator_camera_angle);
	//player_creator_camera_angle.Yaw += (player_creator_camera_target_angle - player_creator_camera_angle.Yaw) * 0.25f;

	player_creator_camera_angle = UKismetMathLibrary::RInterpTo(player_creator_camera_angle, player_creator_camera_target_angle, delta_time, 10.0f);

	//UE_LOG(LogTemp, Warning, TEXT("target: %f, actual: %f"), player_creator_camera_target_angle.Yaw, player_creator_camera_angle.Yaw);

	//MabMatrix translate = MabMatrix::TransMatrix(player_creator_offset);
	//player_creator_transform_notrans = MabMatrix::RotMatrixY(MabMath::Deg2Rad(player_creator_camera_angle));
	//player_creator_transform = translate * player_creator_transform_notrans;

	player_creator_offset = UKismetMathLibrary::VInterpTo(player_creator_offset, player_creator_target_offset + FVector(0.0f, 0.0f, height_offset), delta_time, 10.0f);

	FVector2D Origin = FVector2D(0.0f, 0.0f);
	FVector2D Target2D = FVector2D(player_creator_offset.X, 0.0f);

	//FVector2D Offset = FVector2D(0.0f, player_creator_offset.Y);
	FVector2D CameraDelta = RotatePoint(Origin, Target2D, player_creator_camera_angle.Yaw);

	player_creator_transform = FVector(CameraDelta, player_creator_offset.Z);

	//Play sound for rotating custom player
	if (play_rotate_player_sound)
	{
		player_creator_rotate_sound_timer = 0.1f;
	}
	else if (player_creator_rotate_sound)
	{
		player_creator_rotate_sound_timer = -1.0f;//-= delta_time;
	}

	// Update sound
	if (play_rotate_player_sound && !player_creator_rotate_sound && canPlayRotationSound)
	{
		// Play sound if stopped
		SIFAudio* audio = SIFApplication::GetApplication()->GetAudio();
		if (audio)
		{
			player_creator_rotate_sound = SIFAudioHelpers::PlaySoundEvent(PLAYER_ROTATE_SOUND_EVENT);
		}

	}
	else if (player_creator_rotate_sound_timer < 0.0f)
	{
		// Stop sound if slow
		StopPlayerCreatorRotateSound();
	}


}

FVector2D SSCutSceneManager::RotatePoint(FVector2D Origin, FVector2D Point, float Angle)
{
	//Target = Target + player_creator_offset;

	float s = UKismetMathLibrary::DegSin(Angle);
	float c = UKismetMathLibrary::DegCos(Angle);

	Point = Point - Origin;

	float NewX = (Point.X * c) - (Point.Y * s);
	float NewY = (Point.X * s) - (Point.Y * c);

	return FVector2D(NewX + Origin.X, NewY + Origin.Y);
}

///-------------------------------------------------------------------------------
/// Called when a player is deleted.
///  - remove any references in running/pending cutscenes, to this player.
///-------------------------------------------------------------------------------

void SSCutSceneManager::PlayerDeleted(ARugbyCharacter* player)
{
	//for(MabVector<StMabEVDSCutScene*>::iterator iter = cutscenes.begin(); iter != cutscenes.end(); ++iter)
	//{
	//	StMabEVDSCutScene *cs = (*iter);
	//	/*if(cs->IsLoaded())//#rc3_legacy
	//		cs->PlayerDeleted(player);*/
	//}

	/// Clear local player pointers if == player.

	for (int i = 0; i <= SIDE_NONE; i++)
	{
		if (player == focus_player[i])
			focus_player[i] = NULL;
	}

	if (player == going_off_player)
		going_off_player = NULL;
	if (player == going_on_player)
		going_on_player = NULL;
	if (player == injured_player)
		injured_player = NULL;
}

///-------------------------------------------------------------------------------
/// Get the 'match' emotion level - used for fireworks (SIDE_NONE cutscenes).
///-------------------------------------------------------------------------------

int SSCutSceneManager::GetMatchEmotionLevel()
{
	return match_emotion_level;
}

///-------------------------------------------------------------------------------
/// Calculate the match emotion level.
///-------------------------------------------------------------------------------

void SSCutSceneManager::CalculateMatchEmotionLevel()
{
	RUCareerModeManager* career_manager = SIFApplication::GetApplication()->GetCareerModeManager();
	RUActiveCompetitionBase* active_competition = NULL;
	if (career_manager && career_manager->IsActive())
		active_competition = career_manager->GetActiveCompetition();

	if (active_competition)
	{
		if (game->GetGameSettings().weather_settings.time_of_day == TIME_NIGHT)
		{
			match_emotion_level = 1;
		}
		else
		{
			match_emotion_level = 0;
		}

		if (active_competition->IsCurrentMatchGrandFinal())
			match_emotion_level = 4;
		else if (active_competition->IsCurrentMatchSemiFinal())
			match_emotion_level = 3;
		else if (active_competition->IsCurrentMatchInFinalsRound())
			match_emotion_level = 2;

		if (game->GetStadiumManager()->GetUseSmallPans())
			match_emotion_level--;

		MabMath::Clamp(match_emotion_level, 0, 4);
	}
	else
	{
		match_emotion_level = 0;
	}

}

///-------------------------------------------------------------------------------
/// Clamp the transform part of the matrix to keep cutscene on pitch.
///-------------------------------------------------------------------------------

void SSCutSceneManager::ClampTransform(MabMatrix& transform, float dist_from_touch)
{
	FVector pos = transform.TransformPos(FVector::ZeroVector);

	FieldExtents ext = game->GetSpatialHelper()->GetFieldExtents();
	ext.x = ext.x * 0.5f - dist_from_touch;
	ext.y = ext.y * 0.5f - dist_from_touch;

	FVector clamped_pos = pos;
	MabMath::Clamp(clamped_pos.x, -ext.x, ext.x);
	clamped_pos.y = 0.0f;
	MabMath::Clamp(clamped_pos.z, -ext.y, ext.y);

	MabMatrix clamp_transform = MabMatrix::TransMatrix(clamped_pos - pos);
	transform = transform * clamp_transform;
}


///*******************************************************************************************************************************
///*******************************************************************************************************************************
///*******************************************************************************************************************************
///-------------------------------------------------------------------------------
/// Handle 'non-actor' movement for players in SSRoleCutScene.
///-------------------------------------------------------------------------------

void SSCutSceneManager::UpdateNonCutScenePlayerMovement(ARugbyCharacter* player)
{
	if (!player)
		return;

	// Keep formation slot.

	SSEVDSFormationManager* formation_manager = player->GetAttributes()->GetTeam()->GetFormationManager();
	if (!formation_manager)
		return;

	formation_manager->UpdatePlayerSlot(player);

	if (player->GetState()->IsInCutScene())
		return;				// In cutscene so don't do anything more.

	RUPlayerMovement* movement = player->GetMovement();

	if (game_phase_override != RUGamePhase::REACTION_CUTSCENE && game_phase_override != RUGamePhase::TRY_REACTION)
	{
		// Let formation manager set target position.
		formation_manager->DoStandardFormationMovement(player);
	}

	movement->SetRepulsionMode(RUPlayerMovement::REPULSION_AGGRESSIVE);

	//Just face play direction.
	movement->SetFacingFlags(AFFLAG_FACEPLAYDIR);

	//Look at ball holder.
	player->GetLookAt()->LookAtBallHolder();

	// Analyze + reposition players that might be in the way.

	PostProcessNonActorMovement(player);
}

///-------------------------------------------------------------------------------
/// Reposition player so that he isn't getting in the way of a cutscene.
///-------------------------------------------------------------------------------

void SSCutSceneManager::PostProcessNonActorMovement(ARugbyCharacter* player)
{
	RUPlayerMovement* movement = player->GetMovement();

	if (player->GetState()->IsInCutScene())
		return;

	/// Make sure aggressive repulsion is turned on.
	movement->SetRepulsionMode(RUPlayerMovement::REPULSION_AGGRESSIVE);

	/// In addition to the nice repulsion code, do a target repulse.


	FVector target_pos = movement->GetTargetPosition();
	float dist = SSMath::GetXZPointToPointDistance(exclusion_center, target_pos);
	float repulse_dist = exclusion_range;
	if (dist < exclusion_range)
	{
		FVector boundary_pos = exclusion_center + (target_pos - exclusion_center).Unit() * repulse_dist;
		game->GetSpatialHelper()->ClampWaypointToClampFieldExtents(boundary_pos);

		wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
		movement->SetTargetPosition(boundary_pos);
	}

}

///-------------------------------------------------------------------------------
/// Get the cutscene origin (happens to be center of exclusion zone).
///-------------------------------------------------------------------------------

FVector SSCutSceneManager::GetCutSceneOrigin()
{
	return exclusion_center;
}

///-------------------------------------------------------------------------------
/// Scan all for all actors + active camera and setup repulsion zones for these.
///  - Performs additional function of keeping actors inside the field boundaries (+clamp dist)
///	    (By testing bounds + shifting all cutscenes transforms inwards)
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetupRepulsionZones()
{
#if defined(ENABLE_GAME_DEBUG_MENU) && defined(EXCLUSION_DEBUG)
	debug_key_pool->ReleaseAll();
#endif

	if (!IsSimulationDisabled() || game->GetGameSettings().game_settings.network_game)
		return;

	const float RADIUS_EXTEND = 1.0f;
	const float MIN_RADIUS_CUTSCENES = 2.5f;
	const float MAX_RADIUS_CUTSCENES = 25.0f;

	MabVector<FVector> points;

	/// Add active camera to avoidance zones?

	//for(MabVector<StMabEVDSCutScene*>::iterator iter = cutscenes.begin(); iter != cutscenes.end(); ++iter)
	//{
	//	StMabEVDSCutScene *cs = (*iter);
	//	/*if(cs->GetState()==CST_RUNNING && !cs->IsDual() && cs->IsDisabledSimulation())//#rc3_legacy
	//	{
	//		/// Add a point for each camera.
	//		cs->GetCameraPositions(points);
	//	}*/
	//}

	size_t cameras_end_idx = points.size();

	/// Add players to list of avoidance zones.

	SIFRugbyCharacterList players = game->GetAllPlayers();
	for (SIFRugbyCharacterList::iterator iter = players.begin(); iter != players.end(); ++iter)
	{
		/*#rc3_legacy //#rc3_legacy_animation
		ARugbyCharacter* plr = *iter;
		MABASSERT(plr);
		if(plr == NULL) continue;
		RUPlayerState *state = plr->GetState();
		if(state && state->IsInCutScene() && plr->GetGraphicsObject() && !state->IsInBackgroundCutScene())
		{
			/// Get actual position of player.

			FVector player_pos;
			SIFGraphicsHandle handle = plr->GetGraphicsObject();
			PSSG::PNode *node = handle->getFirstChild();
			node->generateGlobalTransform();
			player_pos = PSSGMab::ToMabVector3(node->m_globalMatrix.getTranslation());

			player_pos.y = 0.0f;
			points.push_back(player_pos);
		}
		*/
	}

	if (points.size() < 2)
	{
		if (exclusion_zone_on)
		{
			game->GetMovement()->UnregisterStaticCollidable(this);
			exclusion_zone_on = false;
		}
		return;
	}

	/// Get centroid of all points.

	float	min_x = 200.0f;
	float	max_x = -min_x;
	float	min_z = min_x;
	float	max_z = max_x;

	FVector centroid = FVector::ZeroVector;
	for (unsigned int i = 0; i < points.size(); i++)
	{
		if (i >= cameras_end_idx)
		{
			min_x = MabMath::Min(min_x, points[i].x);
			min_z = MabMath::Min(min_z, points[i].z);
			max_x = MabMath::Max(max_x, points[i].x);
			max_z = MabMath::Max(max_z, points[i].z);
		}
		centroid += points[i];
	}
	centroid /= (float)points.size();

	/// Get max range from centroid.

	float radius = 0.0f;
	for (unsigned int i = 0; i < points.size(); i++)
	{
		FVector diff = centroid - points[i];
		float range = diff.Magnitude();
		if (range > radius)
			radius = range;
	}

	radius += RADIUS_EXTEND;

	//MABASSERT(radius<MAX_RADIUS);
	MabMath::Clamp(radius, MIN_RADIUS_CUTSCENES, MAX_RADIUS_CUTSCENES);

	if (!IsHideNonActors())
	{
		exclusion_zone_on = true;
		exclusion_center = centroid;
		exclusion_range = radius;
		game->GetMovement()->RegisterStaticCollidable(&exclusion_center, &exclusion_range, this);
	}
	else
	{
		if (exclusion_zone_on)
		{
			game->GetMovement()->UnregisterStaticCollidable(this);
			exclusion_zone_on = false;
		}
	}

	/// Calculate if any actors are off field, if so force shift all cutscenes so actors appear on field.

//#define BOUNDS_TEST

	const float MOVEMENT_FACTOR = 0.125f;
	const float CLAMP_RANGE = 4.5f;

	if (boundry_check_enabled && min_x <= max_x)
	{
		FieldExtents extents = game->GetSpatialHelper()->GetFieldExtents();
		extents.x = (extents.x * 0.5f) + CLAMP_RANGE;
		extents.y = (extents.y * 0.5f) + CLAMP_RANGE;

#ifdef BOUNDS_TEST
		extents.y -= 30.0f;
		extents.x -= 10.0f;
#endif

		float move_x = 0.0f;
		float move_z = 0.0f;

		if (min_x < -extents.x && max_x <= extents.x)
			move_x = -(min_x + extents.x);
		if (max_x > extents.x && min_x >= -extents.x)
			move_x = (extents.x - max_x);

		if (min_z < -extents.y && max_z <= extents.y)
			move_z = -(min_z + extents.y);
		if (max_z > extents.y && min_z >= -extents.y)
			move_z = (extents.y - max_z);

		if (move_z != 0.0f || move_x != 0.0f)
		{
			/// Actor is out of field boundaries, move all playing cutscenes so that actors are inside.

			/// Move in fractionally (keeps it smooth IF it happens).
			MabMatrix transform = MabMatrix::TransMatrix(FVector(move_x * MOVEMENT_FACTOR, 0.0f, move_z * MOVEMENT_FACTOR));

			//MABLOGDEBUG("Boundry exceded moving cutscenes by %f,%f",move_x,move_z);

			//for(MabVector<StMabEVDSCutScene*>::iterator iter = cutscenes.begin(); iter != cutscenes.end(); ++iter)
			//{
			//	StMabEVDSCutScene *cs = (*iter);
			//	/*if(cs->GetState()==CST_RUNNING)//#rc3_legacy
			//	{
			//		MabMatrix current = cs->GetUserTransform();
			//		cs->SetTransform(current*transform);
			//	}*/
			//}
		}
	}

#if defined(ENABLE_GAME_DEBUG_MENU) && defined(EXCLUSION_DEBUG)
	const static int CIRCLE_DIVS = 20;
	MabDebugDrawPath output_path;
	SIF_DEBUG_DRAW(CreateCircle(output_path, exclusion_center, exclusion_range, CIRCLE_DIVS));
	SIF_DEBUG_DRAW(SetPath(debug_key_pool->AllocateKey(), output_path, 0.05f, 0.05f, 0.0f, MabColour::Red, MabColour::Red));
#endif
}


///*******************************************************************************************************************************
///*******************************************************************************************************************************
///*******************************************************************************************************************************

///-------------------------------------------------------------------------------
/// Return 'CameraBlend' for given camera uid if it exists.
///-------------------------------------------------------------------------------

SSCutSceneManager::CameraBlend* SSCutSceneManager::GetCameraBlendInfo(unsigned int cam_uid)
{
	for (MabVector<CameraBlend*>::iterator iterator = camera_blends.begin(); iterator != camera_blends.end(); ++iterator)
	{
		CameraBlend* blend = *iterator;
		if (blend->camera_uid == cam_uid)
			return blend;
	}
	return NULL;
}

//////////////////////////////////////////////////////////////////////////
/// camera blends
//////////////////////////////////////////////////////////////////////////

SSCutSceneManager::CameraBlend::CameraBlend(MabEVDSEvent* event)
	: camera_uid(0)
	, blend_in_time(0.0f)
	, blend_out_time(0.0f)
	, disable_sim(false)
{
	//UpdateData(event);
}

void SSCutSceneManager::CameraBlend::UpdateData(MabEVDSEvent* event)
{
	/*camera_uid = event->GetParam<int>( "camera", 0 );
	blend_in_time = event->GetParam<float>( "BlendInTime", 0.0f );
	blend_out_time = event->GetParam<float>( "BlendOutTime", 0.0f );
	disable_sim = event->GetParam<int>( "DisableSimulation", 0 ) == 1;*/
}


///*******************************************************************************************************************************
///*******************************************************************************************************************************
///*******************************************************************************************************************************

///-------------------------------------------------------------------------------
/// Tell the UI that the haka is playing - turn off skip button.
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetPlayingHaka(bool playing)
{
	//// Hide/show the skip button
	if (RUHUDUpdater* hudUpdater = game->GetHUDUpdater())
		hudUpdater->SetHelpTipVisibility(!playing);
	playing_haka = playing;

	EnableRestartGameOption(!playing);
}



///*************************************************************************************************************************************
///*************************************************************************************************************************************
///*************************************************************************************************************************************


const char* UI_CUTSCENE_START_CALLBACK = "UICutsceneStart";

#define ACTION_SHIFT			16
#define MAX_ACTIONS				16

#define GET_EVENT(a)			((a)&0xffff)
#define GET_ACTIONS(a)			(((a)>>ACTION_SHIFT)&0xffff)
#define CONSTRUCT_EVENT(a,b)	( ((a)&0xffff) | (((1<<(b))&0xffff)<<ACTION_SHIFT))

///-------------------------------------------------------------------------------
/// Request the next current ui-cutscene
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetUICutscene(int cs_event, int mid_fade_action)
{
	should_be_in_main_menu_hack = (cs_event == CSEVENT_UI_MAIN_MENU);			// HACK:  I hate this but necessary as time is short.

	if (cs_event == CSEVENT_UI_MAIN_MENU)
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
		// Stop Kade from breaking the game when we go back to the title screen
		if (game != nullptr && pRugbyGameInstance && pRugbyGameInstance->GetCurrentScreenTemplate() && pRugbyGameInstance->GetCurrentScreenTemplate()->GetScreenID() == Screens_UI::MainMenu)
		{
			RUGameSettings& game_settings = game->GetGameSettings();
			const RUDB_TEAM& db_team = game_settings.team_settings[SIDE_A].team;
			int strip_id = db_team.GetStripId(SIDE_A);

			SIFGameHelpers::GASetCustomTeamCinematicPrimaryStrip(strip_id);
			SIFGameHelpers::GASetCustomTeamCinematicAlternateStrip(strip_id);
		}
	}

	UE_LOG(LogTemp, Log, TEXT("SSCutSceneManager::SetUICutscene: cs_event: (%d) "), cs_event);

	int new_event = CONSTRUCT_EVENT(cs_event, mid_fade_action);

	if (requested_ui_cutscene_state.empty())
	{
		requested_ui_cutscene_state.push_back(new_event);
	}
	else
	{
		if (GET_EVENT(requested_ui_cutscene_state[0]) == cs_event)
		{
			/// Add the action.
			requested_ui_cutscene_state[0] |= CONSTRUCT_EVENT(0, mid_fade_action);
		}
		else
		{
			requested_ui_cutscene_state.insert(requested_ui_cutscene_state.begin(), new_event);

			// Remove duplicate patterns...
			// This stops the list growing if the user is going quickly between the same set of screens.
			//  - Also guarantees that any requested screen/csevent will be processed (at least once - a requirement)

			// A pattern one or more instances of the same UI flow.

			int pattern_size = 2;
			while (pattern_size * 2 <= (int)requested_ui_cutscene_state.size())
			{
				bool found_match = true;
				for (int j = 0; j < pattern_size; j++)
				{
					if (requested_ui_cutscene_state[j] != requested_ui_cutscene_state[pattern_size + j])
						found_match = false;
				}

				if (found_match)
				{
					//MABLOGDEBUG("REMOVED PATTERN: %d",pattern_size);

					/// Remove the first instance of the pattern.
					for (int i = 0; i < pattern_size; i++)
					{
						requested_ui_cutscene_state.erase(requested_ui_cutscene_state.begin());
					}
				}
				else
				{
					pattern_size++;
				}
			}
		}
	}
}

///-------------------------------------------------------------------------------
/// Reset the UI cutscene system.
///-------------------------------------------------------------------------------

void SSCutSceneManager::ResetUICutscene()
{
	ui_cutscene_state = CSEVENT_UNUSED;
	requested_ui_cutscene_state.clear();
	ui_cutscene_faded_actions.clear();
	current_ui_faded_action = -1;
	custom_player_db_id = -1;
	custom_player_strip_id = -1;
	custom_player_team_id = -1;
}

///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------
void SSCutSceneManager::SetUIFadeAction(int mid_fade_action)
{
	UE_LOG(LogTemp, Log, TEXT("Setting UI Fade Action: %d"), mid_fade_action);
	ui_cutscene_faded_actions.push_back(mid_fade_action);
}

///-------------------------------------------------------------------------------
/// Cutscene control for UI cutscenes...
///  Controls all loading/activation of UI cutscenes and fading between.
///-------------------------------------------------------------------------------

void SSCutSceneManager::UpdateUICutscene()
{
	/*if(editing)			/// Don't process UI cutscenes if live editing.
		return;*/

		/// HACK: Set when request ui_main_menu, and reset otherwise. Monitored in update, to start main_menu cutscene should it not be running.

	if (should_be_in_main_menu_hack && ui_cutscene_state != CSEVENT_UI_MAIN_MENU)
	{
		if (!IsCinematicRunning() && requested_ui_cutscene_state.empty())
		{
			MABBREAKMSG("No main menu ui cutscene running!! - rerequesting");
			SetUICutscene(CSEVENT_UI_MAIN_MENU);
		}
	}

	//StMabEVDSCutScene *cs = nullptr;
	bool queue_new_element = false;

	/// Remove requests if already in requested state.
	while (!requested_ui_cutscene_state.empty() && requested_ui_cutscene_state.back() == ui_cutscene_state)
	{
		requested_ui_cutscene_state.pop_back();
	}

	if (!requested_ui_cutscene_state.empty())
	{
		int requested_state = GET_EVENT(requested_ui_cutscene_state.back());
		int requested_action = GET_ACTIONS(requested_ui_cutscene_state.back());

		bool allow_change = cutscene_elements.empty();

		if (!allow_change && /*!game->GetScreenWipeManager()->IsWipeRunning() &&*/
			cutscene_elements.front()->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
		{
			CutsceneCinematicElement* cce = (CutsceneCinematicElement*)cutscene_elements.front();
			if (cce->GetCinematic(0)->CinematicElementInfoData.request_id == ui_cutscene_state)
			{
				allow_change = true;
			}
		}

		//MABLOGDEBUG("Allow_change=%s  numel=%d",allow_change?"true":"false",cutscene_elements.size());

		if (allow_change || requested_state == CSEVENT_UNUSED)
		{
			for (int i = 0; i < MAX_ACTIONS; i++)
			{
				if (requested_action & (1 << i))
					ui_cutscene_faded_actions.push_back(i);
			}
			current_ui_faded_action = -1;
			ui_cutscene_state = requested_state;
			requested_ui_cutscene_state.pop_back();
		}
	}

	CutsceneCinematicElement* current = NULL;
	int	current_count = 0;
	int current_cutscene_state = ui_cutscene_state;

	/// Force finish of non-matching cutscene elements + clear out any pending...

	if (current_cutscene_state != CSEVENT_UNUSED)
	{
		// Redirect main-menu cutscene to a camera pan if doing quick load.
		if (current_cutscene_state == CSEVENT_UI_MAIN_MENU && game->GetQuickLoadActive())
		{
			current_cutscene_state = CSEVENT_UI_MAIN_MENU_QUICKLOAD;
		}

		for (MabVector<CutsceneElement*>::iterator it = cutscene_elements.begin(); it != cutscene_elements.end(); ++it)
		{
			CutsceneElement* ce = (*it);
			if (ce->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
			{
				CutsceneCinematicElement* cce = (CutsceneCinematicElement*)ce;
				if (cce->GetMainCutScene() && cce->GetMainCutScene()->CinematicElementInfoData.request_id == current_cutscene_state)
				{
					current = cce;
				}
			}
		}


		if (current && current->GetMainCutScene() /*&& current->GetMainCutScene()->IsLoaded()*/)		// Once current has loaded
		{
			for (MabVector<CutsceneElement*>::iterator it = cutscene_elements.begin(); it != cutscene_elements.end(); )
			{
				CutsceneElement* ce = (*it);

				if (ce && ce->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
				{
					CutsceneCinematicElement* cce = (CutsceneCinematicElement*)ce;
					if (cce && cce->GetCinematic(0)->CinematicElementInfoData.request_id != current_cutscene_state)
					{
						if (cce->GetMainCutScene() && cce->GetMainCutScene()->CinematicElementInfoData.state == CUTSCENE_STATE::CST_RUNNING)
						{
							//if(!ce->wipe_active) //commented in rc3
							//{
							RequestElementFinish();
							//}
							it++;
						}
						else
						{
							MABLOGDEBUG("Delete cinematic element");

							StopSequencer(cce->GetMainCutScene());//DeleteCutScene(cce->GetMainCutScene());

							it = cutscene_elements.erase(it);
							MabMemDelete(cce);
						}
					}
					else
					{
						current_count++;
						it++;
					}
				}
				else
				{
					++it;
				}
			}
		}

	}
	else
	{
		if (!cutscene_elements.empty())
		{
			CutsceneElement* ce = cutscene_elements.front();

			/// Tutorials setup a stadium pan after win/lose cutscene, when this is detected set ui_cutscene_state so it matches.
			if (ce->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
			{
				CutsceneCinematicElement* cce = (CutsceneCinematicElement*)ce;
				if (cce->GetCinematic(0)->CinematicElementInfoData.request_id == CSEVENT_TUTORIAL_PAN)
				{
					current_cutscene_state = ui_cutscene_state = CSEVENT_TUTORIAL_PAN;
					requested_ui_cutscene_state.clear();
				}
			}
		}
	}

	/// Quickload - stop fade from black on boot up.
	static bool from_boot = true;

	if (current == NULL)
	{
		if (current_cutscene_state != CSEVENT_UNUSED && current_cutscene_state >= 0)
		{
			queue_new_element = true;

			if (!from_boot && !force_finish_current_cutscene)
			{
				if (current_cutscene_state != CSEVENT_UI_TITLE) //#dewald hack to stop fades for the title screen
					AddScreenWipeElement(SWIPE_FADE);
			}
		}
	}
	else
	{
		SSCutSceneManager::StCinematicElementInfo* cs = current->GetMainCutScene();



		// if(current->GetMainCutScene() /*&& cs->GetTime()>cs->GetLastEventTime()*0.5f*/ && IsUIStateReloading(current_cutscene_state) && current_count==1 /*||
		// force_finish_current_cutscene*/ )
		//{
		//	queue_new_element = true;
		//}
		//if (current->GetMainCutScene() && !m_UCutsceneManager->NumOfActiveCutScenes && IsUIStateReloading(current_cutscene_state) &&
		//	current_count == 1 /*|| force_finish_current_cutscene*/)
		if (cs && UOBJ_IS_VALID(cs->UCutscenePtr) && UOBJ_IS_VALID(cs->UCutscenePtr->m_SequencePlayer) && (cs->UCutscenePtr->m_SequencePlayer->GetCurrentTime().AsSeconds() > (cs->UCutscenePtr->m_SequencePlayer->GetEndTime().AsSeconds() * 0.5f)) && IsUIStateReloading(current_cutscene_state) && current_count == 1 /*|| force_finish_current_cutscene*/)
		{
			queue_new_element = true;
		}
	}

	if (queue_new_element)
	{
		bool async_load_cutscene = true;

		/// During quickload, always load non-asynchronously.
		if (from_boot || game->GetQuickLoadActive())
		{
			async_load_cutscene = false;
			from_boot = false;
		}
		//MABLOGDEBUG("START NEW ELEMENT: async: %d",async_load_cutscene?1:0);


		AddCutsceneCallbackElement(&SSCutSceneManager::CutsceneStartCallback, "CutsceneStartCallback");
		AddCutsceneCallbackElement(&SSCutSceneManager::UICutsceneStartCallback, UI_CUTSCENE_START_CALLBACK);

		AddCutsceneCinematicElement(RUGamePhase::PLAY, false, SWIPE_FADE, true);
		AddCinematicTLE(current_cutscene_state, MabMatrix::IDENTITY, SIDE_NONE);
		LoadBestCutScene(current_cutscene_state, async_load_cutscene, false, SIDE_NONE, last_created_element->GetCinematic(0)->CinematicElementInfoData.CutSceneData);

		if (current_cutscene_state == CSEVENT_UI_CUSTOMIZE_PLAYER_BODY)
		{
			last_created_element->GetCinematic(0)->CinematicElementInfoData.PlayerCreatorOffsetEnabled = true;
		}
	}
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

bool SSCutSceneManager::ForceCutsceneInfiniteLoop()
{
	// return true only if next cutscene has same path as this.
	//if(cutscenes.size()==2 && game->GetWorldId()==WORLD_ID::SANDBOX)
	//{
	//	// SPLIT BY FILE PATH DEVIDER.
	//	// IF ALL ELEMENTS SAME (EXCLUDING LAST), THEN IN CAN LOOP CURRENT CUTSCENE INDEFINITELY.

	//       const char *name1 = "\0";//cutscenes[0]->GetName().c_str();
	//       const char *name2 = "\0";//cutscenes[1]->GetName().c_str();

	//	MabStringList::StringList name1_path;
	//	MabStringList::ExpandList( name1_path, name1, '/' );

	//	MabStringList::StringList name2_path;
	//	MabStringList::ExpandList( name2_path, name2, '/' );

	//	if(name1_path.size()==name2_path.size())
	//	{
	//		for(unsigned int i=0;i<name1_path.size()-1;i++)
	//		{
	//			if(name1_path[i]!=name2_path[i])
	//			{
	//				ui_repeat_cutscene_count = 0;
	//				return false;
	//			}
	//		}

	//		// Increment counter
	//		ui_repeat_cutscene_count++;
	//		return ui_repeat_cutscene_count > NUM_REPEATS_BEFORE_FORCE_LOOP;
	//	}
	//}

	ui_repeat_cutscene_count = 0;
	return false;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

bool SSCutSceneManager::UICutsceneStartCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	return manager->UICutsceneStartCallback();
}

///-------------------------------------------------------------------------------
/// Non-static callback - Perform all loading tasks whilst holding screen fully faded.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::UICutsceneStartCallback()
{
	//MABLOGDEBUG( " CSS: CutsceneStartCallback" );

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (!pRugbyGameInstance)
	{
		return false;
	}

	/// Have requested the loading of the non permanent players.
	if (load_non_permanent_players)
	{
		load_non_permanent_players = false;
		//RussellD: game->LoadNonPermanentPlayers();
	}

	do
	{
		if (current_ui_faded_action == -1 && !ui_cutscene_faded_actions.empty())
		{
			current_ui_faded_action = ui_cutscene_faded_actions.back();
			ui_cutscene_faded_actions.pop_back();
		}

		switch (current_ui_faded_action)
		{
			/// Strip restore after
		case UI_CUTSCENE_CHANGE_RESTORE_STRIPS:
		{
			if (game->GetScreenWipeManager())
			{
				game->GetScreenWipeManager()->StartFadeFromBlack(DEFAULT_SCREEN_WIPE_TIME);					// Should be fully faded already, if not make it so.
				game->GetScreenWipeManager()->HoldScreenWipe(5.0f);										// Pause wipe.
			}
			RUSubstitutionManager* substitution_manager = game->GetSubstitutionManager();
			substitution_manager->SetStripsToMatchGameSettings(&game->GetGameSettings(), true);

			current_ui_faded_action = UI_CUTSCENE_CHANGE_RESTORE_STRIPS_WAIT;
		}
		return false;

		case UI_CUTSCENE_CHANGE_RESTORE_STRIPS_WAIT:
			if (game->GetSubstitutionManager()->HaveStripChangesCompleted())
			{
				game->GetScreenWipeManager()->StartWipe(1.0f, SWIPE_FADE);			// Continue wipe.
				current_ui_faded_action = -1;
			}
			else
			{
				return false;
			}
			break;


		case UI_CUTSCENE_LOAD_CUSTOMISATION_PLAYER:
			game->GetScreenWipeManager()->HoldScreenWipe(10.0f);					// Pause wipe
			{
				if (!game->GetCustomisationPlayer())
				{
					SIFGameHelpers::GAFaceRendererCancelAllRenders();
					RUTeam* team0 = game->GetTeam(0);
					game->GetSubstitutionManager()->SubstitutePlayer(NULL, custom_player_db_id, custom_player_strip_id, team0, LoadCustomisationPlayerCallback, (void*)this, false);
					current_ui_faded_action = UI_CUTSCENE_LOAD_CUSTOMISATION_PLAYER_WAIT;
				}
				else
				{
					current_ui_faded_action = UI_CUTSCENE_LOAD_CUSTOMISATION_PLAYER_LOADED;
				}
			}
			return false;
		case UI_CUTSCENE_LOAD_CUSTOMISATION_PLAYER_WAIT:
			return false;
#if (PLATFORM_WINDOWS || PLATFORM_PS4 || PLATFORM_XBOXONE || PLATFORM_SWITCH) && defined(FANHUB_ENABLED)
		case UI_CUTSCENE_LOAD_LIST_TEAMS:
		{
			UWWRugbyFanHubService* pFanHub = Cast<UWWRugbyFanHubService>(pRugbyGameInstance->GetFanHubService());

			// Horrible hack to make the transition work, add in a transition so that when we transition out it proceeds correctly.
			pRugbyGameInstance->RequestTransitionStart(0.0f, FWWUICutsceneLoadComplete());
			pRugbyGameInstance->RequestTransitionHoldFinish(0.5f);

			game->GetScreenWipeManager()->StartWipe(1.0f, SWIPE_FADE);

			if (pFanHub && pFanHub->IsActive())
			{
				CallUICutsceneLoadComplete();
				current_ui_faded_action = -1;
			}
			else
			{
				SetUICutscene(CSEVENT_UI_MAIN_MENU);
				current_ui_faded_action = UI_CUTSCENE_DELETE_CUSTOMISATION_PLAYER;
			}
		}
		return false;
		case UI_CUTSCENE_LOAD_LIST_PLAYERS:
		{
			UWWRugbyFanHubService* pFanHub = Cast<UWWRugbyFanHubService>(pRugbyGameInstance->GetFanHubService());

			if (pFanHub && pFanHub->IsActive())
			{
				RUTeam* team0 = game->GetTeam(0);
				game->GetSubstitutionManager()->SubstitutePlayer(NULL, custom_player_db_id, custom_player_strip_id, team0, LoadCustomisationPlayerCallback, (void*)this, false);
				current_ui_faded_action = UI_CUTSCENE_LOAD_CUSTOMISATION_PLAYER_WAIT;
			}
			else
			{
				//SIFWindowSystem *window_system = SIFApplication::GetApplication()->GetWindowSystem();
				// Horrible hack to make the transition work, add in a transition so that when we transition out it proceeds correctly.
				pRugbyGameInstance->RequestTransitionStart(0.0f, FWWUICutsceneLoadComplete());
				pRugbyGameInstance->RequestTransitionHoldFinish(0.5f);
				// This should acutally probably go back to the main menu? #rc3_legacy
				//CallUICutsceneLoadComplete();

				SetUICutscene(CSEVENT_UI_MAIN_MENU);
				current_ui_faded_action = UI_CUTSCENE_DELETE_CUSTOMISATION_PLAYER;
				//window_system->LoadPendingCompleted(SIFWindowSystem::PENDING_WINDOW_CUT_SCENE);
			}
		}
		return false;
#endif
		case UI_CUTSCENE_LOAD_CUSTOMISATION_PLAYER_LOADED:
		{
			game->RequestSandboxEnvironment(SBE_PLAYER_CUSTOMIZE);
			CallUICutsceneLoadComplete();

			// Horrible hack to make the transition work, add in a transition so that when we transition out it proceeds correctly.
			pRugbyGameInstance->RequestTransitionStart(0.5f, FWWUICutsceneLoadComplete());
			pRugbyGameInstance->RequestTransitionHoldFinish(1.0f);

			if (game->GetScreenWipeManager())
			{
				game->GetScreenWipeManager()->StartWipe(0.5f, SWIPE_FADE); // Continue wipe.
			}

			/*SIFWindowSystem *window_system = SIFApplication::GetApplication()->GetWindowSystem();//#rc3_legacy
			window_system->LoadPendingCompleted(SIFWindowSystem::PENDING_WINDOW_CUT_SCENE);//#rc3_legacy
			*/
			current_ui_faded_action = -1;
		}
		break;

		case UI_CUTSCENE_DELETE_CUSTOMISATION_PLAYER:
		{
			//#rc3_legacy
			/*
			if (game->GetCustomisationPlayer())
			{
				game->DeletePlayer(game->GetCustomisationPlayer());
				game->SetCustomisationPlayer(NULL);
			}
			SIFWindowSystem *window_system = SIFApplication::GetApplication()->GetWindowSystem();
			window_system->LoadPendingCompleted(SIFWindowSystem::PENDING_WINDOW_CUT_SCENE);
			*/
			current_ui_faded_action = -1;
		}
		break;

		case UI_CUTSCENE_CHANGE_SANDBOX_START:
		{
			StopAllCutScenes();
			wwNETWORK_TRACE_JG("SSCutSceneManager::UICutsceneStartCallback UI_CUTSCENE_CHANGE_SANDBOX_START");
			OnCutSceneEnd();
		} //intentional?
		case UI_CUTSCENE_CHANGE_ONLINE_LOBBY_START:
		case UI_CUTSCENE_CHANGE_TRAINING_START:
		{
			if (game->GetScreenWipeManager())
			{
				game->GetScreenWipeManager()->StartFadeFromBlack(0.4f);										// Should be fully faded already, if not make it so.
			}

			RUSandboxGame* sandbox_game = SIFApplication::GetApplication()->GetSandboxGame();
			if (sandbox_game)
			{

				//RussellD: 
				//if (current_ui_faded_action == UI_CUTSCENE_CHANGE_ONLINE_LOBBY_START)
				//	sandbox_game->GetGameWorld()->DeleteNonPermanentPlayers();
				sandbox_game->ResetSandbox();
			}
			game->RequestSandboxEnvironment(SBE_TRAINING);

			current_ui_faded_action = -1;
		}
		break;

		case UI_CUTSCENE_CHANGE_START_ASYNC_LOADING:
		{
			//Really why? Is this safe to be moved?
			//game->RequestAsyncLoadStart();
			current_ui_faded_action = -1;
			return true;
		}
		break;

		case UI_CUTSCENE_CHANGE_LAUNCH_PENDING_LEVEL:
		{
			// Launch the level
			//SIFApplication::GetApplication()->GetLevelLauncher()->LaunchPendingLevel();
			current_ui_faded_action = -1;
			return true;
		}
		break;

		/// Strip restore after
		case UI_CUTSCENE_FORCE_PLAYER_LIKENESS:
		{
			//MABLOGDEBUG("UI_CUTSCENE_FORCE_PLAYER_LIKENESS");
			if (game->GetScreenWipeManager())
			{
				game->GetScreenWipeManager()->StartFadeFromBlack(DEFAULT_SCREEN_WIPE_TIME);					// Should be fully faded already, if not make it so.
				game->GetScreenWipeManager()->HoldScreenWipe(50.0f);											// Pause wipe.
			}
			game->SwapToPreferredSandboxTeams();

			current_ui_faded_action = UI_CUTSCENE_FORCE_PLAYER_LIKENESS_WAIT;
		}
		return false;

		case UI_CUTSCENE_FORCE_PLAYER_LIKENESS_WAIT:
		{
			//MABLOGDEBUG("UI_CUTSCENE_FORCE_PLAYER_LIKENESS_WAIT");
			// When we've finished loading new teams
			if (!game->IsSandboxTeamSwapActive())
			{
				MABLOGDEBUG("Detected teams have loaded in cutscenemanager, continue fade now.");
				game->GetScreenWipeManager()->StartWipe(1.0f, SWIPE_FADE);			// Continue wipe.
				current_ui_faded_action = -1;
			}
			else
			{
				return false;
			}

			/*int teams_blended = 0;
			for( int i = 0; i < 2; i ++)
			{
				RUTeam* team = game->GetTeam(i);
				int num_players = team->GetNumPlayersOnField();
				int players_in_cs = 0;
				int players_blended = 0;
				for(int idx=0;idx<num_players;idx++)
				{
					ARugbyCharacter *primary_player = team->GetPlayer(idx);

					if(primary_player->GetState()->IsInCutScene())
					{
						players_in_cs ++;

						URugbyCharacterBlendController* component_cust_player = primary_player->GetComponent<URugbyCharacterBlendController>();
						if(component_cust_player->GetRealtimeBlender().Blended())
						{
							players_blended ++;
						}
					}
				}

				MABLOGDEBUG(MabString(0, "Team %i has %i/%i players blended", i, players_blended, num_players).c_str());

				if( players_blended == players_in_cs )
				{
					teams_blended++;
				}
			}

			if( teams_blended == 2 )
			{
				game->GetScreenWipeManager()->StartWipe(1.0f, SWIPE_FADE);			// Continue wipe.
				current_ui_faded_action = -1;
			}
			else
			{
				return false;
			}*/
		}
		break;

		default:
			current_ui_faded_action = -1;
			break;
		}
	} while (current_ui_faded_action == -1 && !ui_cutscene_faded_actions.empty());

	/// Mid fade, start home team switch - if requested.
	if (DoHomeTeamSwitchWhenFaded())
	{
		GEngine->ForceGarbageCollection(false);
		game->SwapToPreferredSandboxTeams();
		SetHomeTeamSwitchWhenFaded(false, false);

		// Make sure we don't trigger comp teams loading when we do home team loading.
		//SetCompHomeTeamSwitchWhenFaded(false);
	}

	/*
	if (DoCompHomeTeamSwitchWhenFaded())
	{
		bool valid_switch = game->SwapToPreferredSandboxTeams();
		SetCompHomeTeamSwitchWhenFaded(false);

		// If we've started loading, do the fade and hold.
		if( valid_switch && game->IsSandboxTeamSwapActive() )
		{
			game->GetScreenWipeManager()->StartFadeFromBlack(DEFAULT_SCREEN_WIPE_TIME);					// Should be fully faded already, if not make it so.
			game->GetScreenWipeManager()->HoldScreenWipe(50.0f);											// Pause wipe.
		}
	}
	*/

	/*
	if( game->IsSandboxTeamSwapActive())
	{
		MABLOGDEBUG("Still waiting for the teams");
		return false;
	}
	*/

	if (ui_cutscene_state == CSEVENT_UI_MAIN_MENU)
	{
		game->RequestSandboxEnvironment(SBE_MENU);
	}

	return (current_ui_faded_action == -1);
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

bool SSCutSceneManager::IsUIStateReloading(int cs_event)
{
	switch (cs_event)
	{
	case CSEVENT_UI_MAIN_MENU:
	case CSEVENT_UI_COMPETITION_SELECT:
	case CSEVENT_UI_QUICKMATCH_TEAMSELECT:
	case CSEVENT_UI_QUICKMATCH_CONTROLLER_SELECT:
	case CSEVENT_UI_COMPETITION_HUB:
	case CSEVENT_UI_COMPETITION_HUB_WIN:
	case CSEVENT_UI_COMPETITION_HUB_LOSS:
		return true;

	default:
		break;
	}

	return false;
}


///-------------------------------------------------------------------------------
/// Callback for customize player loader.
///-------------------------------------------------------------------------------

void SSCutSceneManager::LoadCustomisationPlayerCallback(void* user_data, ARugbyCharacter* player, ARugbyCharacter* old_player, bool is_abort)
{
	MABUNUSED(old_player);
	SSCutSceneManager* manager = (SSCutSceneManager*)user_data;

	//	MABLOGDEBUG("LoadCustomisationPlayerCallback: %x %x",player,old_player);

	MABASSERT(!is_abort);
	if (!is_abort)
	{
		manager->game->GetCutSceneManager()->SetFocusPlayer(player, SIDE_A);		// Set the focus player for the customisation cutscene.

		MABASSERT(manager->current_ui_faded_action == UI_CUTSCENE_LOAD_CUSTOMISATION_PLAYER_WAIT);
		manager->current_ui_faded_action = UI_CUTSCENE_LOAD_CUSTOMISATION_PLAYER_LOADED;

		// enable real time blender.
		/*#rc3_legacy
		URugbyCharacterBlendController* component = player->GetComponent<URugbyCharacterBlendController>();
		if (component)
		{
			component->GetRealtimeBlender().Enable();
		}
		*/

		// Because of the way the subsitition manager works, the player creator player belonds to team 0 in the game world
		// so picks up on all the team settings from that team. Here we hack some of those settings to make the
		// player look like they are from the correct team

		if (manager->custom_player_team_id != -1)
		{
			RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();

			RUDB_TEAM team;
			RUDB_TEAM_STRIP strip;

			database_manager->LoadData(team, manager->custom_player_team_id);
			database_manager->LoadData(strip, team.GetStripId(0));

			PlayerCustomisationInfo customisation = player->GetCustomisationInfo();

			customisation.Setup(player->GetAttributes()->GetDBPlayer());		// Restore boots etc.. as overriden by team0
			customisation.Setup(team, strip, true);								// And set to correct club strip (minus the boot overrides)

			player->SetCustomisationInfo(customisation);
			player->ApplyCustomisation(true);
		}

		URugbyCharacterAnimInstance* pCharacterAnimInstance = Cast<URugbyCharacterAnimInstance>(player->GetAnimInstance());

		if (pCharacterAnimInstance)
		{
			UE_LOG(LogTemp, Display, TEXT("SSCutSceneManager::LoadCustomisationPlayerCallback Setting player animation override."));
			pCharacterAnimInstance->SetPlayerCreatorOverride(EPlayerCreatorAnimOverrideType::PLAYER_CREATOR);
		}

		//manager->game->AppendPlayer(player);
		manager->game->SetCustomisationPlayer(player);

		manager->custom_player_db_id = -1;
		manager->custom_player_strip_id = -1;
		manager->custom_player_team_id = -1;
	}
}

///-------------------------------------------------------------------------------
/// Set which player to load for pending screen transition (to/from player editor)
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetCustomisationPlayer(int db_id, int strip_id, int team_id)
{
#ifdef ENABLE_SOAK_TEST
	// In 'cp_soak' test to see if we want to override the loaded player... (Generating likenesses for players that haven't got one).
	//#rc3_legacy_soak
	/*SIFApplication *app = SIFApplication::GetApplication();
	if(app->GetSoakManager()->GetCurrentSoakType()=="cp_soak")
	{
		SIFSoakPlayerCreate *current_soak = (SIFSoakPlayerCreate*)app->GetSoakManager()->GetCurrentSoak();
		if(current_soak)
		{
			if( current_soak->GetPlayerDbId() != 0)
			{
				db_id = current_soak->GetPlayerDbId();
			}
		}
	}*/
#endif

	custom_player_db_id = db_id;
	custom_player_strip_id = strip_id;
	custom_player_team_id = team_id;
}

//-------------------------------------------------------------------------------

void SSCutSceneManager::ResetTimer()
{
	start_time = last_frame_time = 0;
	start_time = MabTime::GetCurrentMabTime();
	last_frame_time = start_time;
	timeout = start_time + TIMEOUT_MILLIS;
}

void SSCutSceneManager::SetPlayerCreatorCameraTargetAngle(float value)
{
	player_creator_camera_target_angle.Add(0.0f, value, 0.0f);
	//player_creator_camera_target_angle = FMath::ClampWrap<float>(player_creator_camera_target_angle, 0.0f, 360.0f);
}

void SSCutSceneManager::CallUICutsceneLoadComplete()
{
	if (OnUICutsceneLoadComplete.IsBound())
	{
		OnUICutsceneLoadComplete.Execute();
		OnUICutsceneLoadComplete.Unbind();
	}
}

//-------------------------------------------------------------------------------
//-------------------------------------------------------------------------------
bool SSCutSceneManager::IsInReplay() const
{
	bool is_in_replay = false;

	if (cutscene_elements.size() > 0)
	{
		MabVector<CutsceneElement*>::const_iterator cur_iter = cutscene_elements.begin();
		const CutsceneElement* current = (*cur_iter);

		if (current && current->cutscene_type == CUTSCENE_TYPE_REPLAY)
		{
			is_in_replay = true;
		}
	}

	return is_in_replay;
}

//-------------------------------------------------------------------------------
void SSCutSceneManager::TickNetworkCutscenes(float delta_time)
{
	// Tick the cutscene manually if we are in a network game.
	if (game->GetGameSettings().game_settings.network_game)
	{
		if (cutscene_elements.size() > 0)
		{
			MabVector<CutsceneElement*>::iterator cur_iter = cutscene_elements.begin();
			CutsceneElement* current = (*cur_iter);

			if (current != NULL)
			{
				/// Check if skip is enabled...
				/// N.B. element must be skippable AND must have a skip_point element to go to.
				if (current->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
				{
					CutsceneCinematicElement* cce = (CutsceneCinematicElement*)current;

					for (auto& cs : cce->cinematics)
					{
						UCutScenes* uc = cs.UCutscenePtr;

						if (uc)
						{
							if (uc->m_SequencePlayer)
							{
								uc->m_SequencePlayer->Update(delta_time);
							}
						}
					}
				}
			}
		}
	}
}

void SSCutSceneManager::StopPlayerCreatorRotateSound()
{
	if (player_creator_rotate_sound)
	{
		player_creator_rotate_sound->stop(FMOD_STUDIO_STOP_IMMEDIATE);
		player_creator_rotate_sound->release();
		player_creator_rotate_sound = NULL;
	}
}

void SSCutSceneManager::ResetPlayerCreatorCameraAngle()
{
	player_creator_camera_angle = player_creator_camera_target_angle;
}

//-------------------------------------------------------------------------------
void SSCutSceneManager::GetPlayerForCutScene(int& teamno, int& selector, int& shirtno, bool& get_player_on_bench, ARugbyCharacter** selected_player, TArray<ARugbyCharacter*> CutScene_player_List, int TeamIndex /* = -1*/)
{
	wwNETWORK_TRACE_JG(("GetPlayerForCutScene: Team: %s (%d) Selector: %s (%d) Shirt: %d"), TCHAR_TO_UTF8(*ENUM_TO_FSTRING(TeamSelector, teamno)), teamno, TCHAR_TO_UTF8(*ENUM_TO_FSTRING(SelectorType, selector)), selector, shirtno);

	*selected_player = nullptr;

	auto IsPlayerInCutScene = [&](ARugbyCharacter* selPlayer)
		{
			for (const auto& PList : CutScene_player_List)
			{
				if (PList == selPlayer)
					return true;
			}
			return false;
		};

	RUTeam* teamList = game->GetTeam(0);
	if (TeamIndex == -1)
	{
		switch (teamno)
		{
		case ATTACKING:
			teamList = game->GetGameState()->GetAttackingTeam();
			break;
		case DEFENDING:
			teamList = game->GetGameState()->GetDefendingTeam();
			break;
		case TEAM1:
			if (game->GetNumTeams() > 1)
			{
				teamList = game->GetTeam(1);
			}
			break;
		case OFFICIALS:
			teamList = game->GetOfficialsTeam();
			break;

		case CELEBRATING:
			teamList = this->GetCelebratingTeam();
			if (!teamList)
			{
				teamList = game->GetTeam(0);
			}
			break;

		case COMMISERATING:
			teamList = this->GetCelebratingTeam();
			if (teamList != NULL)
			{
				teamList = (RUTeam*)teamList->GetOppositionTeam();
			}
			else
			{
				teamList = game->GetTeam(1);
			}
			break;

		case HAKATEAM:
			if (!this->CanTeamDoHaka(teamList))
				teamList = game->GetTeam(1);
			break;
		case NONHAKATEAM:
			if (this->CanTeamDoHaka(teamList))
				teamList = game->GetTeam(1);
			break;

		default:
			break;
		}
	}
	else
	{
		if (TeamIndex == 0 || TeamIndex == 1)
		{
			teamList = game->GetTeam(TeamIndex);
		}
		else if (TeamIndex == 2)
		{
			teamList = game->GetOfficialsTeam();
		}
		selector = UI_ID;
	}

	switch (selector)
	{
	case FOCUS_PLAYER:
		if (teamList != NULL)
		{
			*selected_player = this->GetFocusPlayer(teamList->GetSide());
		}
#ifdef ENABLE_ACTOR_DEBUG
		MABLOGDEBUG("FOCUS_PLAYER: 0x%p", *selected_player);
#endif
		break;

	case GOING_ON_PLAYER:
		*selected_player = this->GetGoingOnPlayer();

#ifdef ENABLE_ACTOR_DEBUG
		MABLOGDEBUG("GOING_ON_PLAYER: 0x%p", *selected_player);
#endif
		break;

	case GOING_OFF_PLAYER:
		*selected_player = this->GetGoingOffPlayer();

#ifdef ENABLE_ACTOR_DEBUG
		MABLOGDEBUG("GOING_OFF_PLAYER: 0x%p", *selected_player);
#endif
		break;

	case INJURED:
		*selected_player = this->GetInjuredPlayer();

#ifdef ENABLE_ACTOR_DEBUG
		MABLOGDEBUG("INJURED: 0x%p", *selected_player);
#endif
		break;

	case UI_ID:
		if (teamList != NULL)
		{
			SIFRugbyCharacterList team_players = teamList->GetPlayers();
			int num_players = (int)team_players.size();
			int num_NonBench_players = (int)team_players.size();


			if (CVarShowWomenInCutscenes.GetValueOnGameThread())
			{
				unsigned short womanOverrideDbId;
				if (game->IsMenu() && game->TryGetMenuWomanOverrideDbIdByShirtNumber(teamList->GetSide(), shirtno, womanOverrideDbId))
				{
					*selected_player = teamList->GetPlayerByDbId(womanOverrideDbId);
					if (*selected_player == NULL)
					{
						*selected_player = teamList->GetBenchPlayerByDbId(womanOverrideDbId);
					}
					get_player_on_bench = true;
					UE_LOG(LogTemp, Log, TEXT("Cutscene: found woman matching Team: %i, Selector: %i  shirtNo: %i, %X"), teamno, selector, shirtno, *selected_player);
					break;
				}

				UE_LOG(LogTemp, Log, TEXT("Cutscene: finishing woman Team: %i, Selector: %i  shirtNo: %i, %X"), teamno, selector, shirtno, *selected_player);
			} 
			

			if (*selected_player == NULL)
			{
				for (int i = 0; i < num_players; i++)
				{
					if (team_players[i]->GetState()->GetUIId() == shirtno)
					{
						if (team_players[i] && !IsPlayerInCutScene(team_players[i]))
						{
							*selected_player = team_players[i];
						}
#ifdef ENABLE_ACTOR_DEBUG
						else
						{
							MABLOGDEBUG("UI_ID: Actor already in cutscene!");
						}
#endif
						break;
					}

				}
			}

			/// If not found, then perhaps the player is benched?? (Sandbox world benches a lot of players).
			if (*selected_player == NULL)
			{
				team_players = teamList->GetBenchPlayers();
				num_players = (int)team_players.size();
				for (int i = 0; i < num_players; i++)
				{
					if (team_players[i]->GetState()->GetUIId() == shirtno)
						//&& (!game->IsMenu() || !team_players[i]->GetState()->IsNonPermanent()))
					{
						*selected_player = team_players[i];
						get_player_on_bench = true;
						break;
					}

				}
			}

		}
#ifdef ENABLE_ACTOR_DEBUG
		MABLOGDEBUG("UI_ID: 0x%p (shirt_no=%d)", *selected_player, shirtno);
#endif
		break;

	case CAPTAIN:
		if (teamList != NULL)
		{
			if (teamList->GetCaptain() && !IsPlayerInCutScene(teamList->GetCaptain()))
			{
				*selected_player = teamList->GetCaptain();
			}
		}
#ifdef ENABLE_ACTOR_DEBUG
		MABLOGDEBUG("CAPTAIN: 0x%p", *selected_player);
#endif
		break;

	case HAKA_LEADER:
		if (teamList != NULL)
		{
			/// HAKA_LEADER: Choose either Piri Weepu OR Hosea Gear, if neither available use teamList captain, else 
			/// Choose a NZ player with 
			int best_star_weight = 0;
			MabVector<ARugbyCharacter*> team_players;

			SIFRugbyCharacterList team_players_field = teamList->GetPlayers();
			SIFRugbyCharacterList team_players_bench = teamList->GetBenchPlayers();
			for (int i = 0; i < (int)team_players_field.size(); i++)
			{
				if (!IsPlayerInCutScene(team_players_field[i]))
				{
					team_players.push_back(team_players_field[i]);
				}
			}

			for (int i = 0; i < (int)team_players_bench.size(); i++)
			{
				if (!IsPlayerInCutScene(team_players_bench[i]))
				{
					team_players.push_back(team_players_bench[i]);
				}
			}

			int num_players = (int)team_players.size();
			for (int i = 0; i < num_players; i++)
			{
				int star_weight = 0;
				switch (team_players[i]->GetAttributes()->GetDBPlayer()->GetDbId())
				{
				case DB_PLAYERID_TJ_PERENARA:			star_weight = 5; break;
				case DB_PLAYERID_AARON_SMITH:			star_weight = 4; break;
				case DB_PLAYERID_KIERAN_READ:			star_weight = 3; break;
				case DB_PLAYERID_NEHE_MILNER_SKUDDER:	star_weight = 2; break;
				default:
					break;
				}

				if (star_weight > best_star_weight)
				{
					*selected_player = team_players[i];
					best_star_weight = star_weight;
				}
			}

			MABASSERT(*selected_player);
			if (*selected_player == NULL)
			{
				*selected_player = teamList->GetCaptain();
			}
		}
#ifdef ENABLE_ACTOR_DEBUG
		MABLOGDEBUG("HAKA_LEADER: 0x%p", *selected_player);
#endif
		break;

	case SHIRTNO_ORMAORI:
		if (teamList != NULL)
		{
			/// Setup for haka. 
			/// Polynesians are selected (random)
			/// Then everyone else.

			MabVector<ARugbyCharacter*> polynesians;
			MabVector<ARugbyCharacter*> non_polynesians;

			SIFRugbyCharacterList team_players_field = teamList->GetPlayers();
			int num_players = (int)team_players_field.size();
			for (int i = 0; i < num_players; i++)
			{
				if (!IsPlayerInCutScene(team_players_field[i]))
				{
					if (*selected_player == NULL)
					{
						// Russell : This wasn't working as intended anymore, named ethnicities no longer used, near clarification on what should happen here
						//if (team_players_field[i]->GetAttributes()->GetDBPlayer()->ethnicity == PLAYER_ETHNICITY_POLYNESIAN)
						//{
						//	polynesians.push_back(team_players_field[i]);
						//}
						//else
						{
							non_polynesians.push_back(team_players_field[i]);
						}
					}
				}
			}

			SIFRugbyCharacterList team_players_bench = teamList->GetBenchPlayers();
			num_players = (int)team_players_bench.size();
			for (int i = 0; i < num_players; i++)
			{
				if (!IsPlayerInCutScene(team_players_bench[i]))
				{
					if (*selected_player == NULL)
					{
						// Russell : This wasn't working as intended anymore, named ethnicities no longer used, near clarification on what should happen 
						//if (team_players_bench[i]->GetAttributes()->GetDBPlayer()->ethnicity == PLAYER_ETHNICITY_POLYNESIAN)
						//{
						//	polynesians.push_back(team_players_bench[i]);
						//}
						//else
						{
							non_polynesians.push_back(team_players_bench[i]);

							// WJS RLC don't add any more than max players per team
							if (non_polynesians.size() == game->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam())
								break;
						}
					}
				}
			}

			if (*selected_player == NULL)
			{
				if (!polynesians.empty())
				{
					int rand_index = game->GetRNG()->RAND_RANGED_CALL(int, (int)polynesians.size());
					*selected_player = polynesians[rand_index];
				}
				else if (!non_polynesians.empty())
				{
					int rand_index = game->GetRNG()->RAND_RANGED_CALL(int, (int)non_polynesians.size());
					*selected_player = non_polynesians[rand_index];
				}
			}
		}
#ifdef ENABLE_ACTOR_DEBUG
		MABLOGDEBUG("SHIRTNO_ORMAORI: 0x%p (shirt_no=%d)", *selected_player, shirtno);
#endif
		break;

	case SHIRTNO:
		if (teamList != NULL)
		{
			SIFRugbyCharacterList team_players = teamList->GetPlayers();
			int num_players = (int)team_players.size();
			PLAYER_POSITION wanted_position = PlayerPositionEnum::GetPlayerPositionFromStartingJerseyNumber(shirtno);
			for (int i = 0; i < num_players; i++)
			{
				if (team_players[i] && !IsPlayerInCutScene(team_players[i]) && team_players[i]->GetAttributes()->GetPlayerPosition() == wanted_position)
				{
					*selected_player = team_players[i];
					break;
				}
			}

			/// If not found, then perhaps the player is benched?? (Sandbox world benches a lot of players).
			if (*selected_player == NULL)
			{
				team_players = teamList->GetBenchPlayers();
				num_players = (int)team_players.size();
				for (int i = 0; i < num_players; i++)
				{

					if (team_players[i]->GetAttributes()->GetPlayerPosition() == wanted_position)
					{
						*selected_player = team_players[i];
						get_player_on_bench = true;
						break;
					}
				}
			}
		}
#ifdef ENABLE_ACTOR_DEBUG
		MABLOGDEBUG("SHIRTNO: 0x%p (shirt_no=%d)", *selected_player, shirtno);
#endif
		break;

	case TEAMIDX:
		if (teamList != NULL)
		{
			SIFRugbyCharacterList team_players = teamList->GetPlayers();
			int num_players = (int)team_players.size();
			if (shirtno <= num_players)
			{

				if (!IsPlayerInCutScene(team_players[shirtno - 1]))
				{
					*selected_player = team_players[shirtno - 1];
				}
			}
		}
#ifdef ENABLE_ACTOR_DEBUG
		MABLOGDEBUG("TEAMIDX: 0x%p (shirt_no=%d)", *selected_player, shirtno);
#endif
		break;

	case BALLHOLDER:

		if (game->GetGameState()->GetBallHolder() != NULL && !IsPlayerInCutScene(game->GetGameState()->GetBallHolder()))
		{
			*selected_player = game->GetGameState()->GetBallHolder();
		}
#ifdef ENABLE_ACTOR_DEBUG
		MABLOGDEBUG("BALLHOLDER: 0x%p", *selected_player);
#endif
		break;

	case KICKER:
		if (teamList != NULL)
		{
			bool bIncludeBenchPlayers = (game->IsMatch() == false);

			ARugbyCharacter* GoalKicker = teamList->GetGoalKicker(bIncludeBenchPlayers);

			if (GoalKicker != NULL && !IsPlayerInCutScene(GoalKicker))
			{
				*selected_player = GoalKicker;
			}
		}
#ifdef ENABLE_ACTOR_DEBUG
		MABLOGDEBUG("KICKER: 0x%p", *selected_player);
#endif
		break;

	case LASTSCORER:

		if (game->GetGameState()->GetLastPlayerToScore() != NULL && !IsPlayerInCutScene(game->GetGameState()->GetLastPlayerToScore()))
		{
			*selected_player = game->GetGameState()->GetLastPlayerToScore();
		}
#ifdef ENABLE_ACTOR_DEBUG
		MABLOGDEBUG("LASTSCORER: 0x%p", *selected_player);
#endif
		break;

	case NEAREST:		// Handled afterwards (*selected_player==NULL).
#ifdef ENABLE_ACTOR_DEBUG
		MABLOGDEBUG("NEAREST: 0x%p", *selected_player);
#endif
		break;

	default:
		break;
	}

	if ((*selected_player == NULL || (*selected_player != NULL && IsPlayerInCutScene(*selected_player) == true)) && teamList != NULL)
	{
		if (*selected_player == NULL)
		{
			MABLOGDEBUG("Selected player is null, pick closest player");
		}
		else if ((*selected_player != NULL && IsPlayerInCutScene(*selected_player) == true) && teamList != NULL)
		{

			MABLOGDEBUG("Selected player is already in a cutsccene = %s, uiid=%d, dbid=%d",
				(*selected_player != NULL) ? (*selected_player)->GetAttributes()->GetCombinedName().c_str() : "NULL",
				(*selected_player != NULL) ? (*selected_player)->GetState()->GetUIId() : -1,
				(*selected_player != NULL) ? (*selected_player)->GetAttributes()->GetDbId() : -1);
		}

		FVector position(0, 0, 0);
		//GetPosition(0.0f, position, 0);									// Get the initial position of the actor.
		//position = cutscene->GetTransform().TransformPos(position);		// convert to world coords.

		const float MAX_RANGE = 200.0f;

		SIFRugbyCharacterList team_players = teamList->GetPlayers();
		int num_players = (int)team_players.size();
		float bestrangesq = MAX_RANGE * MAX_RANGE;

		for (int i = 0; i < num_players; i++)
		{
			//the_player = team_players[i];

			FVector delta = team_players[i]->GetMovement()->GetCurrentPosition() - position;
			float rngsq = delta.SquaredMagnitude();

			if (team_players[i] && !IsPlayerInCutScene(team_players[i]) && rngsq < bestrangesq)
			{
				*selected_player = team_players[i];
				bestrangesq = rngsq;
			}
		}

#ifdef ENABLE_ACTOR_DEBUG
		MABLOGDEBUG("USING NEAREST = 0x%p (uiid=%d)", *selected_player, (*selected_player != NULL) ? *selected_player->GetState()->GetUIId() : -1);
#endif
	}

	if (!game->IsMatch())
	{
		if (get_player_on_bench && *selected_player != NULL && teamList != NULL)
		{
			teamList->MoveToField(*selected_player);
			//m_CutSceneOnlyPlayerList.Add(*selected_player); //#checkme //nirupam
		}
	}

	/*
			MABLOGDEBUG("Selected Player = %s, uiid=%d, dbid=%d",
				(*selected_player != NULL) ? (*selected_player)->GetAttributes()->GetCombinedName().c_str() : "NULL",
				(*selected_player != NULL) ? (*selected_player)->GetState()->GetUIId() : -1,
				(*selected_player != NULL) ? (*selected_player)->GetAttributes()->GetDbId() : -1);
	*/

	ensureAlways(IsPlayerInCutScene(*selected_player) == false);
	//ensureAlways(*selected_player != nullptr); //Removing this assert which I added initially for testing. We can have more players in cutscene files than we have in the team: Ex: cs_team2ofro_02 has 16 players, but we have 15 players in team.
	if (*selected_player == nullptr)
	{
		//	MABLOGDEBUG("GetPlayerForCutScene: No Matching player found");

			//some logic to make sure we have some player on the MainMenu cutscene instead of no player just in case everything above fails.
		if (game->IsMenu())
		{
			UE_DEBUG_BREAK();//No Matching player found in MainMenu. Investigate what happened to the team (teamList Players and BenchPlayers)

			if (teamList != NULL)
			{
				ARugbyCharacter* GoalKicker = teamList->GetGoalKicker(true);

				if (GoalKicker != NULL && !IsPlayerInCutScene(GoalKicker))
				{
					*selected_player = GoalKicker;
				}
				else
				{
					SIFRugbyCharacterList team_players = teamList->GetBenchPlayers();
					int num_players = (int)team_players.size();
					ensureAlways(num_players > 0);
					for (int i = 0; i < num_players; i++)
					{
						if (!IsPlayerInCutScene(team_players[i]))
						{
							*selected_player = team_players[i];
							get_player_on_bench = true;
							break;
						}
					}
				}
			}
		}
	}
}//end of void SSCutSceneManager::GetPlayerForCutScene(int &teamno, int &selector, int& shirtno, int& get_player_on_bench)

//-------------------------------------------------------------------------------
float SSCutSceneManager::GetPathParametricFromDistance(float distance, MabNURBSSpline& spline, float	length, FSerialiseTypePath& path)
{
	MabVector <FVector> points = spline.GetCVs();

	if (length > 0.0f && spline.GetCVCount() > 1 && path.normTable.Num() > 0)
	{
		float pos = distance;

		MabMath::Clamp(pos, 0.0f, 0.9999f);

		int tnum = (int)path.normTable.Num() - 1;
		float num = (float)tnum;
		int tablepos = (int)(pos * num);
		float t = (pos - ((float)tablepos / num)) * num;

		return path.normTable[tablepos] + t * (path.normTable[tablepos + 1] - path.normTable[tablepos]);
	}
	return 0.0f;
};

//-------------------------------------------------------------------------------
FVector SSCutSceneManager::GetPathPosition(float swing_time_through, FSerialiseTypePath& path, MabNURBSSpline& spline)
{
	FVector path_eye;
	path_eye = FVector::ZeroVector;
	if (path.spline.controlPoints.Num() > 0)
	{
		if (path.spline.controlPoints.Num() == 1) // if the path has a single point return it
		{
			path_eye = path.spline.controlPoints[0];//*spline->GetCV(0);			
		}
		else
		{
			path_eye = spline.Evaluate(swing_time_through);
		}
	}
	return path_eye;
};

//
////-------------------------------------------------------------------------------
//FPropData SSCutSceneManager::UpdateProp(FSerialisePropData *PropData, int visible, ARugbyCharacter *attach_actor, int attach_joint, FTransform *unrealTransform, StCinematicElementInfo* CinematicInfoPtr)
	//{
//	FPropData PropNameWithTransform;
//
//	if (!PropData)
	//	{
//		UE_LOG(LogTemp, Log, TEXT("Cutscene: Invalid Prop..."));
//		return PropNameWithTransform;
//	}
//
//	//fixme. Need to handle path Actor
//	FVector position(0.f, 0.f, 0.f);
//	FVector rotation(0.f, 0.f, 0.f);//MabVector3(0, y_angle, 0)	
//
//	MABMEM_SCOPED_TAG(MEMTAG_CUTSCENE_PROPS);
//
//	enum {
//		ATTACH_JOINT_BALL = 0,
//		ATTACH_JOINT_LEFTHAND,
//		ATTACH_JOINT_RIGHTHAND,
//		NUM_ATTACH_JOINTS
//	};
//
//	static const char* ATTACH_BONES[NUM_ATTACH_JOINTS] = {
//		"ball",
//		"rightWrist",
//		"leftWrist",
//	};
//
//	FSerialiseTypeGraph xoffset_graph = PropData->X;
//	FSerialiseTypeGraph yoffset_graph = PropData->Y;
//	FSerialiseTypeGraph zoffset_graph = PropData->Z;
//	FSerialiseTypeGraph facing_graph = PropData->facingangle;
//	float path_angle = 0.0f;
//	float y_angle = MabMath::Deg2Rad(facing_graph.get_graph_value(0));
//	position = FVector(xoffset_graph.get_graph_value(0), yoffset_graph.get_graph_value(0), zoffset_graph.get_graph_value(0));
//	rotation = FVector(0, y_angle, 0);
//
//	//MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(FVector(xoffset_graph.get_graph_value(0), yoffset_graph.get_graph_value(0), zoffset_graph.get_graph_value(0)), position);	
//	//MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(FVector(0, y_angle, 0), rotation);
//
//	bool is_visible = true;//fixme running && this->visible;
//	AActor	*object = nullptr;
//
//	//Need to have a better way
//	if (strstr(TCHAR_TO_ANSI(*PropData->model), "ball") && game->GetBall())
	//		{
//		object = game->GetBall()->GetBallObject();
//		if (object)
	//			{
//			object->SetActorTransform(*unrealTransform * object->GetActorTransform());
	//						}
//
//		if ( CinematicInfoPtr )
	//					{
//			CinematicInfoPtr->CinematicElementInfoData.HideBall = false;
//			CinematicInfoPtr->CinematicElementInfoData.HasBallProp = true;
	//					}
	//				}					
//	//else
//	//{
//	//	if (PropData->model.Len() > 0)
//	//	{
//	//		// Add the prop to the game. This needs to be replaced with datatables. Another way is to add the prop directly in the sequencer.
//	//		UE_LOG(LogTemp, Log, TEXT("Cutscene: AddProp: %s"), *PropData->model);
//
//	//		FCutscenePropRec *PropDataPtr = GetPropRec(PropData->model); //get by name of the model. The names are stored in datatables
//
//	//		if (PropDataPtr != nullptr)
//	//		{
//	//			PropNameWithTransform.PropAssetNameWithPath = PropDataPtr->m_PropSoftPath.ToString();
//	//			PropNameWithTransform.PropName = PropDataPtr->m_Propname;
//
//	//			MabMatrix pos = MabMatrix::TransMatrix(position);
//	//			MabMatrix rotate = MabMatrix::RotMatrixY(y_angle);
//	//			MabMatrix m = (rotate * pos);
//
//	//			if (attach_actor != NULL)
//	//			{
//	//				MabMatrix bone_transform = MabMatrix::IDENTITY;
//	//				bool bone_set = false;
//
//	//				//SIFGameObject *attach_object = attach_actor->GetObject();
//
//	//				if (attach_actor)
//	//				{
//	//					//NMMabSkeleton* skeleton = attach_object->GetComponent<NMMabSkeleton>();
//	//					if (attach_joint >= ATTACH_JOINT_BALL && attach_joint < NUM_ATTACH_JOINTS)
//	//					{
//	//						BoneIndex bone_index = attach_actor->FindBone(ATTACH_BONES[attach_joint]);
//	//						if (bone_index != INDEX_NONE)
//	//						{
//	//							bone_transform = attach_actor->GetBoneWorldTransform(bone_index);
//	//							bone_set = true;
//	//						}
//	//					}
//
//	//					if (!bone_set)
//	//					{
//	//						bone_transform = attach_actor->GetMabTransform();
//	//					}
//	//				}					
//	//				m = m * bone_transform;		
//	//			}
//
//	//			FTransform dstTransform;
//	//			TransformUtility::ConvertMabTransformForUnreal(m, dstTransform);
//
//	//			PropNameWithTransform.PropTransform = *unrealTransform * dstTransform;
//	//		}
//
//	//		//just a call for now, does nothing
//	//		//object = game->AddProp(position, rotation, TCHAR_TO_ANSI(*PropData->model), 0xffffffff); //static const unsigned int LIGHTMASK_ALL = 0xffffffff;
//	//	}
//	//}	
//	return PropNameWithTransform;
	//			}

///-------------------------------------------------------------------------------
/// PlaySequence - Play the sequence file
///-------------------------------------------------------------------------------

ALevelSequenceActor* SSCutSceneManager::PlaySequence(FCutSceneData* selectedCutSceneData)
{
	UCutsceneActorData* CutSceneActorsGUIDDataPtr = nullptr;
	StCinematicElementInfo* CurrentCinematicInfoPtr = nullptr;
	ALevelSequenceActor* pLevelSequenceActor = nullptr;
	m_selected_player = nullptr;
	int CutSceneIdx = 0;
	bool bHideAllPlayers = false;
	bool IsPlayerCreatorOn = false;

	//**********************************************
	//Some checks to make sure we proceed correctly
	//**********************************************
	if (nullptr == selectedCutSceneData)
	{
		UE_LOG(LogTemp, Log, TEXT("PlaySequence Method: No cutscene to play. Dont process this request."));
		return pLevelSequenceActor;
	}

	if (selectedCutSceneData->CutSceneID == CSEVENT_UI_CUSTOMIZE_PLAYER_BODY)	//#playerCreator	
	{
		IsPlayerCreatorOn = true;
	}

	//RC4-4073: Hide players if cinematics_enabled is false. Keeping this update limited to these two sequencers for now.
	//Also in SSCutSceneSequencing.cpp, only these two custscenes are shown when cinematics_enabled == false, during the gameplay.
	if ((cinematics_enabled == false) && (selectedCutSceneData->CutSceneID == CSEVENT_PREGAME_TEAM_SELECT_SMALL || selectedCutSceneData->CutSceneID == CSEVENT_PREGAME_TEAM_SELECT))
	{
		bHideAllPlayers = true;
	}


	//some logs
//#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)	
	if (selectedCutSceneData->CutSceneID < NUM_CSEVENTS)
	{
		wwNETWORK_TRACE_JG("      ");
		wwNETWORK_TRACE_JG("**************************CutScene Processing Starts **************************");
		wwNETWORK_TRACE_JG("CutSceneID: %d", selectedCutSceneData->CutSceneID);
	}
	else
	{
		wwNETWORK_TRACE_JG("Invalid CutSceneID %d", selectedCutSceneData->CutSceneID);
		wwNETWORK_TRACE_JG("**************************CutSceneEnds**************************");
		return pLevelSequenceActor;
	}
	//#endif

		//------------------------------------------------------------------------------------------------------------
		//------------------------------------------------------------------------------------------------------------
		//Start loading the sequence
		//1. Get the data from the datatable using the cutscene ID: {GetCutsceneRec()}
		//2. Load the custscene file (sequencer asset) obtained from the datatable: {m_sequenceSoftPath.TryLoad()}
		//3. Get the data asset UIDs (GUIDs for cameras and player characters from the _data files): {m_CutSceneActorsGUIDDataPtr}	
		//-------------------------------------------------------------------------------------------------------------
		//-------------------------------------------------------------------------------------------------------------

	ULevelSequence* pSequence = nullptr;
	TArray<FCutsceneRec*> pCutsceneRec;
	CutSceneActorsGUIDDataPtr = nullptr;
	bool IsSimpleSequencerFile = false;

	////Note: Here pause is just used to avoid camera flicker between cutscenes.
	////so if we need real pause of cutscenes, then dont add those in m_PausedSequence, and handle differently
	//if (m_PausedSequence.Num())
	//{	
	//	for (auto &it : m_PausedSequence)
	//	{
	//		if (UOBJ_IS_VALID(it))
	//		{
	//			if (it->IsPlaying())
	//			{
	//				it->Stop();
	//			}
	//		}
	//	}
	//	m_PausedSequence.Reset();
	//}

	if (selectedCutSceneData->CutSceneID == -1) //if someone wants to play a sequencer which is not from RC3. This reads the file name directly and plays. May not be used ever.
	{
		FString file = selectedCutSceneData->CutSceneFileName;
		FStringAssetReference assetRef(file);
		pSequence = Cast<ULevelSequence>(assetRef.TryLoad());
		IsSimpleSequencerFile = true;
		IsBackGroundCutsceneInProgress = true;
		UE_LOG(LogTemp, Log, TEXT("someone called a cutscene to play manually "));
	}
	else //Must be from RC3. So read the file name from the datatable. These files are from RC3.
	{
		CutSceneIdx = selectedCutSceneData->CutSceneIndex;

		int IsRugbySeven = 0;

#ifdef ENABLE_SEVENS_MODE
		// Are we playing a R7 game? Does this cutscene have a special variation for R7's?
		// Nick  WWS 7s to Womens //if (SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetGameMode() == GAME_MODE_SEVENS && cs_info[selectedCutSceneData->CutSceneID].R7Variation)
		//{
		//	IsRugbySeven = 1;
		//}
#endif

		//get the data from the datatable
		pCutsceneRec = GetCutsceneRec(selectedCutSceneData->CutSceneID, IsRugbySeven);

		if (CutSceneIdx >= 0 && CutSceneIdx < pCutsceneRec.Num() && pCutsceneRec[CutSceneIdx]->m_sequenceSoftPath.IsValid())
		{
			//some more logs
			FString CutSceneName = cs_info[selectedCutSceneData->CutSceneID].tag_name;

			wwNETWORK_TRACE_JG("CutSceneEnum: '%s' CutScene Path: '%s'", TCHAR_TO_UTF8(*CutSceneName), TCHAR_TO_UTF8(*pCutsceneRec[CutSceneIdx]->m_sequenceSoftPath.ToString()));

			// Attempt to load the sequence file via the soft path
			pSequence = Cast<ULevelSequence>(pCutsceneRec[CutSceneIdx]->m_sequenceSoftPath.TryLoad());
			CutSceneActorsGUIDDataPtr = pCutsceneRec[CutSceneIdx]->m_pCutsceneDataAsset;
		}
	}

	//-------------------------------------------------------------------------------------------------------------
	//------------------------------------------------------------------------------------------------------------
	// 1. CutScene is loaded, now get the transform where it needs to start.
	//    Transform position is the location where the current game context is as set by cinematic->transform multiplied by the Origin position embedded in the JSON file as in RC3.
	// 2. Create the Level Sequence Player based on the above transform: {CreateLevelSequencePlayer()}
	// 3. Also set hide_ball if needed for this cutscene
	//------------------------------------------------------------------------------------------------------------
	//-------------------------------------------------------------------------------------------------------------
	CurrentCinematicInfoPtr = nullptr;

	if (pSequence)
	{
		// unsure if required; set up some options for playback
		FMovieSceneSequencePlaybackSettings settings;
		settings.bDisableLookAtInput = true;
		settings.bDisableMovementInput = true;
		////#dewald_cutscene To pause the cutscenes at the end instead of stopping which removes them
		//if (SIFApplication::GetApplication()->GetActiveGameWorld()->GetWorldId() != WORLD_ID::GAME)
		//{
		//	settings.bPauseAtEnd = true;
		//}

		FTransform unrealTransform = FTransform(FRotator(0.0f, 0.0f, 0.0f), FVector(0.0f, 0.0f, 0.0f), FVector(1.0f, 1.0f, 1.0f));
		std::unique_ptr<FSerialiseCutsceneControlData > CutsceneDataPtr;

		if (IsSimpleSequencerFile == false)//not manually created sequnecer file, so it is some RC3 sequencer file. So, We need to read the JSON.
		{
			CutsceneDataPtr = evds::construct_cutscene(selectedCutSceneData->CutSceneFileName); //get the data from JSON file

			MabVector<CutsceneElement*>::iterator ce_iter;
			MabVector<CutsceneElement*>::iterator ce_iter_end;
			CutsceneElement* cce = nullptr;
			ce_iter = cutscene_elements.begin();
			ce_iter_end = cutscene_elements.end();

			bool FoundMatchingCutScene = false;

			// Search for the first cinematic element
			for (; ce_iter != ce_iter_end; ++ce_iter)
			{
				if (true == FoundMatchingCutScene)
				{
					break;
				}

				cce = (*ce_iter);
				if (cce->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
				{
					CutsceneCinematicElement* cce_casted = (CutsceneCinematicElement*)cce;
					for (MabVector<StCinematicElementInfo>::iterator it = cce_casted->cinematics.begin(); it != cce_casted->cinematics.end(); it++)
					{
						StCinematicElementInfo* cinematic = &(*it);

						if (cinematic->CinematicElementInfoData.request_id == selectedCutSceneData->CutSceneID)
						{

							MabMatrix nm = MabMatrix::IDENTITY;
							if (CutsceneDataPtr->Origin.Num())
							{
								float x = -CutsceneDataPtr->Origin[0].x.get_graph_value(0.0f); //negation is done as per RC3
								//ignore Y. Not used in RC3 also. -CutsceneDataPtr->Origin[0].y.get_graph_value(0.0f);			
								float z = -CutsceneDataPtr->Origin[0].z.get_graph_value(0.0f);
								nm.SetTranslation(FVector(x, 0, z));
							}

							MabMatrix transform = nm * cinematic->CinematicElementInfoData.transform;
							FTransform dstTransform;
							TransformUtility::ConvertMabTransformForUnreal(transform, dstTransform);
							unrealTransform = dstTransform;

							cinematic->CinematicElementInfoData.filename = TCHAR_TO_ANSI(*selectedCutSceneData->CutSceneFileName);
							cinematic->CinematicElementInfoData.state = CST_RUNNING;

							if (CutsceneDataPtr->GameControl.Num())
							{
								cinematic->CinematicElementInfoData.HideBall = (CutsceneDataPtr->GameControl[0].hideball) ? true : false;
							}
							cinematic->CinematicElementInfoData.is_Main = (cinematic == cce_casted->GetMainCutScene() ? true : false);

							cinematic->CinematicElementInfoData.CrowdReactionDataList.Empty();

							if (CutsceneDataPtr->CrowdSoundEvent.Num())
							{
								cinematic->CinematicElementInfoData.HasCrowdReaction = true;
								FCrowdReaction TempCrowdReaction;

								for (auto& CrowdReaction : CutsceneDataPtr->CrowdSoundEvent)
								{
									TempCrowdReaction.CrowdReactionTime = CrowdReaction.time;
									TempCrowdReaction.CrowdReactionVolume = CrowdReaction.volume;
									TempCrowdReaction.CrowdReactionType = CrowdReaction.event;
									TempCrowdReaction.HasPlayedCrowdReaction = false;

									if (cinematic->CinematicElementInfoData.CrowdReactionDataList.Num() < MAX_CROWD_REACTION_EVENT)
									{
										cinematic->CinematicElementInfoData.CrowdReactionDataList.Add(TempCrowdReaction);
									}
									else
									{
										UE_DEBUG_BREAK();
										break;
									}
								}
							}
							else
							{
								cinematic->CinematicElementInfoData.HasCrowdReaction = false;
							}

							//only for Haka
							if (CutsceneDataPtr->CommentaryEvent.Num() > 0)
							{
								cinematic->CinematicElementInfoData.CommentaryEventStartTime = CutsceneDataPtr->CommentaryEvent[0].time;
							}
							else
							{
								cinematic->CinematicElementInfoData.CommentaryEventStartTime = -1.0f;
							}

							if (cinematic->CinematicElementInfoData.is_Main) //this is added to avoid overwriting characters of main cutscenes by subcutsenes as players can have same ID
							{
								m_CutScene_player_List.Reset();
							}
							cinematic->CinematicElementInfoData.is_looping = CutsceneDataPtr->TimeJump.Num() > 0 ? true : false;
							CurrentCinematicInfoPtr = cinematic;
							CurrentCinematicInfoPtr->CinematicElementInfoData.cutsceneActorRestoreDataList.Empty(); //make sure we clear before we start.

							//C4-3965 we use unreal loop cutscene only for CSEVENT_UI_CUSTOMIZE_TEAM for now. All other loop cutscenes has to use randomisation of multiple cutscenes, so cant use this logic.
							if (cinematic->CinematicElementInfoData.is_looping && selectedCutSceneData->CutSceneID == CSEVENT_UI_CUSTOMIZE_TEAM)
							{
#if ENGINE_MINOR_VERSION >= 22
								settings.LoopCount = FMovieSceneSequenceLoopCount(); //play in looop.....
								settings.LoopCount.Value = -1;
#else			
								settings.LoopCount = -1; //play in looop.....
#endif
							}

							FoundMatchingCutScene = true;// to break from outer loop
							break;
						}
					}//for inner
				}//if cinematics
			} //for outer
			ensureAlways(CurrentCinematicInfoPtr != nullptr);

		}//if (IsSimpleSequencerFile == false)	


		// Create a sequence player using the selection sequencer file.
		UWorld* pWorld = game->GetGameInstance().GetWorld();

		ULevelSequencePlayer* pSequencePlayer = nullptr;

		if (IsPlayerCreatorOn == false) //#playerCreator
		{
			pSequencePlayer = ULevelSequencePlayer::CreateLevelSequencePlayer(pWorld, pSequence, settings, pLevelSequenceActor);

			if (nullptr != pLevelSequenceActor)
			{
				// Tell the level sequence actor not to tick if we are in a network game, we will tick it manually.
				if (game->GetGameSettings().game_settings.network_game)
				{
					//pLevelSequenceActor->SetActorTickEnabled(false);
					pLevelSequenceActor->PrimaryActorTick.bStartWithTickEnabled = 0; // GGS Nick
				}
			}
		}

		//-------------------------------------------------------------------------------------------------------------
		//------------------------------------------------------------------------------------------------------------
		// 1. Get the players based on the Team Id, Position Id, shirt ID, and assign them to cutscenes at runtime.
		// 2. Check if actor has associated animation. Then make sure we restore the animation state once cutscene ends.
		// 3. Set Ball Holder. SetBallHolder()
		//-------------------------------------------------------------------------------------------------------------
		//------------------------------------------------------------------------------------------------------------
		TArray <FPropData> PropNameWithTransform;

		//if ( pLevelSequenceActor)
		{
			if (pLevelSequenceActor)
			{
				pLevelSequenceActor->bOverrideInstanceData = true;
				// IMPORTANT:
				// UDefaultLevelSequenceInstanceData has been updated in the WW engine branch (same as AFL) to allow us to actually set transform values on sequences at runtime.
				pLevelSequenceActor->DefaultInstanceData = NewObject<UDefaultLevelSequenceInstanceData>(pSequence, "settings");
				if (pLevelSequenceActor->DefaultInstanceData)
				{
					if (UDefaultLevelSequenceInstanceData* pAsDefault = Cast<UDefaultLevelSequenceInstanceData>(pLevelSequenceActor->DefaultInstanceData))
					{
						// An alternative to setting a transform here it to set a TransformOriginActor whose world position will be used as 
						// an offset for the sequence and can then be manipulated after the sequence has been created.
						pAsDefault->TransformOrigin = unrealTransform;
					}
				}
			}

			if (false == IsSimpleSequencerFile)//not manually created sequencer file, so it is some RC3 sequencer file. So, We need to read the JSON.
			{
				if (CutSceneActorsGUIDDataPtr)
				{
					if (UOBJ_IS_VALID(m_UCutsceneManager) && pLevelSequenceActor)
					{
						// Replicated behaviour from AFL, fixes case of creatures somehow being already bound to possessables due to how they were saved.
						// Resulted in a single character sometimes being bound to multiple possessable tracks in the sequence and incorrect playback.
						m_UCutsceneManager->UnbindPossessablesFromSequence(pLevelSequenceActor, CutSceneActorsGUIDDataPtr);
					}

					ARugbyCharacter* pFootballer = nullptr;
					bool IsBallHolderSet = false;
					bool SequencerHasBall = false;

					//check if Ball Has been added to this cutscene file already. If it has, then ignore SetBallHolder Attribute
					for (const FPossessablePropData& propData : CutSceneActorsGUIDDataPtr->PossessablePropDataList)
					{
						if (propData.PropsType == ECutscenePropType::Ball ||
							propData.PropsType == ECutscenePropType::CaptainsBall)
						{
							SequencerHasBall = true;
							break;
						}
					}

					if (!bHideAllPlayers && CutSceneActorsGUIDDataPtr->FootballerDataList.Num())
					{
						/*TArray <FSerialiseKeyActorData> KeyactorAnimList;
						TArray <FSerialisePathActorData> PathactorAnimList;

						for (const auto &it : CutsceneDataPtr->ActorAnim)
						{
							KeyactorAnimList.Add(it.actor);
							PathactorAnimList.Add(it.pathactor);
						}

						for (const auto &it : CutsceneDataPtr->ActorAnim2)
						{
							KeyactorAnimList.Add(it.actor);
							PathactorAnimList.Add(it.pathactor);
						}

						for (const auto &it : CutsceneDataPtr->ActorAnim3)
						{
							KeyactorAnimList.Add(it.actor);
							PathactorAnimList.Add(it.pathactor);
						}*/

						for (int ActorIdx = 0; ActorIdx < CutSceneActorsGUIDDataPtr->FootballerDataList.Num(); ++ActorIdx)
						{
							CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].pAssignedFootballer = nullptr;

							bool  get_player_on_bench = false;

							//set the m_selected_player
							GetPlayerForCutScene(

								//set data
								CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].TeamId,
								CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].PositionId,
								CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].ShirtId,

								//get data. Not used as of now
								get_player_on_bench,

								//Selected player pointer
								&m_selected_player,

								m_CutScene_player_List

							);

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)	
							bool debugCutsceneInfo = CVarCutsceneState.GetValueOnGameThread() > 0 || FParse::Param(FCommandLine::Get(), TEXT("CutsceneInfo"));
							if (debugCutsceneInfo)
							{
								ensureAlways(m_selected_player != nullptr); //if this is null, player will not be visible in the cutscene. So it needs a fix either in code, or the sequencer..
							}
#endif

							if (m_selected_player)
							{
								wwNETWORK_TRACE_JG("SSCutSceneManager::PlaySequence Selected player %d for index %d", m_selected_player->GetAttributes()->GetDbId(), ActorIdx);

								//pAssignedFootballer should have pointer to the actual character in the game. 
								//Note that the pointer is not set when we get the GUID from the datatables in m_pCutsceneDataAsset
								//bind footballers to possessable objects in the sequence so that new instances of default players are not spawned.

								CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].pAssignedFootballer = m_selected_player;
								m_CutScene_player_List.Add(m_selected_player);
							
								FCutsceneDataRestore RestoreData;
								RestoreData.cutsceneActorRestore = m_selected_player;
								RestoreData.isBenchPlayerRestore = get_player_on_bench;

								// set the player visibility and add the player to the cutscene.
								m_selected_player->SetVisible(true);

								// Make sure our body mesh wasn't hidden for the head renders.
								if (m_selected_player->GetBodyMesh())
								{
									m_selected_player->GetBodyMesh()->SetHiddenInGame(false);
								}

								if (m_selected_player->IsPaused())
								{
									m_selected_player->SetPaused(false);
								}

								MABASSERTMSG(CurrentCinematicInfoPtr != NULL, "Something wrong with CutScene CurrentCinematicInfoPtr. Possible memory corruption");

								if (CurrentCinematicInfoPtr && (CurrentCinematicInfoPtr->CinematicElementInfoData.cutsceneActorRestoreDataList.Num() < MAX_CUTSCENE_ACTOR))
								{
									CurrentCinematicInfoPtr->CinematicElementInfoData.cutsceneActorRestoreDataList.Add(RestoreData);
								}
							}

							//set the ball holder here. This would ensure the ballholder player has ball attached to the ball bone.
							//Also this logic will NOT attach ball if Ball Prop already exists in the cutscene 
							if ((CutsceneDataPtr->SetBallHolder.Num()) && (IsBallHolderSet == false) && (SequencerHasBall == false))
							{
								ensureAlways(CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].pAssignedFootballer != nullptr);

								if ((CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].TeamId == CutsceneDataPtr->SetBallHolder[0].actor.team) &&
									(CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].PositionId == CutsceneDataPtr->SetBallHolder[0].actor.selector) &&
									(CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].ShirtId == CutsceneDataPtr->SetBallHolder[0].actor.shirtno) && CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].pAssignedFootballer)
								{
									game->GetGameState()->SetBallHolder(CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].pAssignedFootballer);
									UE_LOG(LogTemp, Display, TEXT("%s Cutscene: Ball AttachToComponent '%s'"), *FString(__func__), *CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].pAssignedFootballer->GetName());
									game->GetBall()->AttachToComponent(CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].pAssignedFootballer->GetMesh(), FAttachmentTransformRules(EAttachmentRule::SnapToTarget, true), "ball");

									UE_LOG(LogTemp, Log, TEXT("CutScene: Ball Holder  %s"), *CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].pAssignedFootballer->GetName());
									IsBallHolderSet = true;
								}
							}

							pFootballer = CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].pAssignedFootballer;

							if (pFootballer)
							{
								if (IsPlayerCreatorOn) //#playerCreator
								{
									unrealTransform.SetScale3D(FVector(pFootballer->GetHeightScale()));
									pFootballer->SetActorTransform(unrealTransform);
									if (game->GetBall()->IsVisible())
									{
										game->GetBall()->SetVisible(false);
									}
									pFootballer->GetState()->AddToCutScene((void*)(CurrentCinematicInfoPtr));  // void used as it is a nested structure, cant forward declare where it's used.
									UpdatePlayerVisibility(CurrentCinematicInfoPtr->CinematicElementInfoData.HideNonActors);
								}
								else
								{
									pFootballer->GetState()->AddToCutScene((void*)(CurrentCinematicInfoPtr), CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].IsPathActor);  // void used as it is a nested structure, cant forward declare where it's used.

									//if (foundMatch) //#nirupam: Removing this check as we want players to be spawned from sequencers and not check if the same exists in JSON...
									{
										//pFootballer->GetMovement()->StopAllMovement();
										pFootballer->OnBeginCinematic();
										// 										if (pFootballer->GetAnimInstance())
										// 										{
										// 											pFootballer->GetAnimInstance()->m_bCinematicsEnabledParam = true;
										// 											pFootballer->GetAnimInstance()->m_bCinematicFacialsParam = true;
										// 										}
										wwNETWORK_TRACE_JG("********CutScene: Playing Animation for ***%s", TCHAR_TO_UTF8(*pFootballer->GetName()));
									}

									if (pLevelSequenceActor)
									{
										// Binds the chosen footballer to the possessable object.
										pSequence->BindPossessableObject(CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].Guid, *pFootballer, pLevelSequenceActor);
									}

									pFootballer->GetName();
									wwNETWORK_TRACE_JG("********CutScene: BindPossessableObject***%s", TCHAR_TO_UTF8(*pFootballer->GetName()));

									if (pFootballer->GetMovement())
									{
										pFootballer->GetMovement()->SetFacingFlags(AFFLAG_FACEMOTION);
									}

									// Need to override the actor facing setting to prevent player from being rotated in radom directions.
									if (pFootballer->GetMovementComponent())
									{
										if (UCharacterMovementComponent* pCharMoveComp = Cast<UCharacterMovementComponent>(pFootballer->GetMovementComponent()))
										{
											pCharMoveComp->bUseControllerDesiredRotation = false;
										}
									}
								}
							}//if (pFootballer)
						}//for (auto actorIdx = 0; actorIdx < actorDataList.Num(); actorIdx++)
					}//if (actorDataList.Num())
				}  // if (CutSceneActorsGUIDDataPtr)				
			}//if (IsSimpleSequencerFile == false)

			if (pSequencePlayer)
			{

				//some code to shorten cutscene length and fast forward it. Enable it only for debugging.
#ifdef CUT_SCENE_TIME_LIMIT//shorten the cutscene
				pSequencePlayer->SetTimeRange(pSequencePlayer->GetStartTime().AsSeconds(), CUT_SCENE_TIME_LIMIT);
#endif

#ifdef FAST_FORWARD_CUTSCENE //skip it fast
				pSequencePlayer->SetPlayRate(FAST_FORWARD_CUTSCENE);
#endif		

				float SequenceLength = pSequencePlayer->GetDuration().AsSeconds();

				ensureAlways(SequenceLength > 0.0f);

				//-------------------------------------------------------------------------------------------------------------------------------------------------
				//-------------------------------------------------------------------------------------------------------------------------------------------------
				// 1. Set Start and end delegates. These are functions which will be called when cutscene starts and ends.
				//    These methods are in UCutSceneManager::SequenceStart(). These are added there to have Unreal manage garbage collection in a better way.
				// 2. Call the Play() method of custscene.
				// 3. Store some global stuffs.
				// 4. Return the ULevelSequencePlayer pointer
				//-------------------------------------------------------------------------------------------------------------------------------------------------
				//-------------------------------------------------------------------------------------------------------------------------------------------------

				if (false == IsSimpleSequencerFile)
				{
					//some code added to ensure backgound cutscene are equal or longer than main cutscene.
					if (CurrentCinematicInfoPtr->CinematicElementInfoData.is_Main)
					{
						m_MainCutSceneLength = SequenceLength;
					}
					else
					{
						//ensureAlways(m_MainCutSceneLength <= SequenceLength);
						//ensureAlways(m_MainCutSceneLength >= 0.0f);
						wwNETWORK_TRACE_JG("m_MainCutSceneLength: '%.2f' Current CutSceneLength: '%.2f' ", m_MainCutSceneLength, SequenceLength);
					}

					CurrentCinematicInfoPtr->CinematicElementInfoData.cutSceneLength = SequenceLength; //debug only

					// call the UCutSceneManager to do create props, and assign the start/end delegates.
					if (UOBJ_IS_VALID(m_UCutsceneManager) && pLevelSequenceActor)
					{
						m_UCutsceneManager->SequenceStart(pSequencePlayer, pLevelSequenceActor, CutSceneActorsGUIDDataPtr,
							PropNameWithTransform, CurrentCinematicInfoPtr->CinematicElementInfoData);

						wwNETWORK_TRACE_JG("********All good. Play the custscene****");

						//some more logs

						FString CutSceneName = cs_info[selectedCutSceneData->CutSceneID].tag_name;
						wwNETWORK_TRACE_JG("CutSceneEnum: %s", TCHAR_TO_UTF8(*CutSceneName));
						FString IsMain = "";

						if (CurrentCinematicInfoPtr->CinematicElementInfoData.is_Main)
						{
							IsMain = "---------MAIN CUTSCENE-----:: ";
						}

						FString LengthInString = FString::SanitizeFloat(SequenceLength, 1);

						FString infoOutput = IsMain + FString("  ID: ") + FString::FromInt(selectedCutSceneData->CutSceneID) + FString(": ") /*+ FString(" Directory: ")*/ + CutSceneName + " Length: " + LengthInString;

						float time = SequenceLength > 10.0f ? 10.0f : SequenceLength; //time till which log will be displayed... doesnot consider skips

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)	
						bool debugCutsceneInfo = CVarCutsceneName.GetValueOnGameThread() > 0 || FParse::Param(FCommandLine::Get(), TEXT("CutsceneName"));

						if (GEngine && debugCutsceneInfo)
						{
							GEngine->AddOnScreenDebugMessage(-1, time, FColor::Cyan, infoOutput);
							GEngine->AddOnScreenDebugMessage(-1, time, FColor::White, pCutsceneRec[CutSceneIdx]->m_sequenceSoftPath.ToString());
						}
#endif				
						wwNETWORK_TRACE_JG("'%s' CutScene Path: '%s' DetailedInfo: '%s' ", TCHAR_TO_UTF8(*IsMain), TCHAR_TO_UTF8(*pCutsceneRec[CutSceneIdx]->m_sequenceSoftPath.ToString()), TCHAR_TO_UTF8(*infoOutput));

					}
					else //should never happen.
					{
						wwNETWORK_TRACE_JG("Something in Cutscene gone wrong. End delegates wont work.");
					}
				}
#ifdef WIPE_USING_SEQUENCER
				if (game->GetScreenWipeManager()->IsRunningSequence()) //make sure we end sequence if it's running.
				{
					game->GetScreenWipeManager()->EndSequence();
				}
#endif

				//play the cut-scene. Play is needed to be called to bind cameras in PositionCamera method
				pSequencePlayer->Play();

				// fix women animation not playing after going back from sandbox level
				if (selectedCutSceneData->CutSceneID == CSEVENT_UI_QUICKMATCH_TEAMSELECT || selectedCutSceneData->CutSceneID == CSEVENT_UI_QUICKMATCH_CONTROLLER_SELECT)
				{
					for (int ActorIdx = 0; ActorIdx < CutSceneActorsGUIDDataPtr->FootballerDataList.Num(); ++ActorIdx)
					{
						ARugbyCharacter* CurrentFixingPlayer = CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx].pAssignedFootballer;
						if (CurrentFixingPlayer == nullptr)
							continue; // skip if we don't have a valid player, otherwise cause nullptr crash below
						USkeletalMeshComponent* CurrentFixingSKM = CurrentFixingPlayer->GetBodyMesh();

						EAnimationMode::Type CurrentAnimationMode = CurrentFixingSKM->GetAnimationMode();
						CurrentFixingSKM->SetAnimationMode(EAnimationMode::Type::AnimationCustomMode);
						CurrentFixingSKM->SetAnimationMode(CurrentAnimationMode);
					}
				}

				// Overloading the ball material at this point. We need to Play the sequence first, otherwise GetBoundObjects will return an empty list.
				if (pSequencePlayer && pLevelSequenceActor)
				{
					if (CutSceneActorsGUIDDataPtr)
					{
						// Modify props as required
						for (const FPossessablePropData& propData : CutSceneActorsGUIDDataPtr->PossessablePropDataList)
						{
							ModifySpawnableProp(propData, pSequence, pLevelSequenceActor, pSequencePlayer);
						}


						// Handle face animations for players, needs to happen after Play()
						for (int ActorIdx = 0; ActorIdx < CutSceneActorsGUIDDataPtr->FootballerDataList.Num(); ++ActorIdx)
						{
							const FFootballerActorData& actorData = CutSceneActorsGUIDDataPtr->FootballerDataList[ActorIdx];
							if (actorData.pAssignedFootballer)
							{
								if (actorData.pAssignedFootballer->GetMovement())
								{
									actorData.pAssignedFootballer->GetMovement()->ResetCutsceneSpeedRollingAverage();
								}

								// if (UOBJ_IS_VALID(actorData.pAssignedFootballer->GetAnimInstance()))
								// {
								// 	// #anim_todo_cinematic: do we need to do something more intelligent here for cutscenes with non-constant play rates?
								// 	actorData.pAssignedFootballer->GetAnimInstance()->TryPlayFaceAnimation(actorData.FaceAnimation, 1.0f, true);
								// }
							}
						}

						//// Modify particles as required
						for (const FPossessableParticleData& particleData : CutSceneActorsGUIDDataPtr->PossessableParticleDataList)
						{
							ModifySpawnableParticleFX(particleData, pSequence, pLevelSequenceActor, pSequencePlayer);
						}
					}
				}
			}//if (pSequencePlayer)

			CutsceneDataPtr.reset(); // delete the object, leaving CutsceneDataPtr empty
			UE_LOG(LogTemp, Log, TEXT("Play Started"));
		}//if ( pLevelSequenceActor)
	}
	return pLevelSequenceActor;
}  // ULevelSequencePlayer* SSCutSceneManager::PlaySequence

//----------------------------------------------------------------------------------------------------------
// PositionCamera method is called in every tick when cutscene is playing. This is only for the Main Cutscene
//-----------------------------------------------------------------------------------------------------------

void SSCutSceneManager::PositionCamera(float time, UCutScenes* CurrentCutscenes)
{
	UCutsceneActorData* CutSceneActorsGUIDDataPtr = CurrentCutscenes->m_ActorGUIDData;

	if (!CutSceneActorsGUIDDataPtr || !CutSceneActorsGUIDDataPtr->PlayableCameraGuidList.Num())
	{
		return; //no camera
	}

	TArray <TArray<FCameraActorData>> GroupTimeSortedCameraGUIDs; //Group cameras based on their timeline. So it's array of camera arrays
	GroupTimeSortedCameraGUIDs.Empty();
	TArray<FCameraActorData > CameraGUIDGroup; //A temp array to fill the GroupTimeSortedCameraGUIDs
	CameraGUIDGroup.Empty();

	if (CutSceneActorsGUIDDataPtr->PlayableCameraGuidList.Num() != 1)
	{
		for (auto Index = 0; Index < CutSceneActorsGUIDDataPtr->PlayableCameraGuidList.Num() - 1; ++Index)
		{
			CameraGUIDGroup.Add(CutSceneActorsGUIDDataPtr->PlayableCameraGuidList[Index]);

			//Check if this index of camera and next index of camera are of the same timeline. If not then we need to add to another group.
			if (CutSceneActorsGUIDDataPtr->PlayableCameraGuidList[Index].TimeLine != CutSceneActorsGUIDDataPtr->PlayableCameraGuidList[Index + 1].TimeLine)
			{
				GroupTimeSortedCameraGUIDs.Add(CameraGUIDGroup);
				CameraGUIDGroup.Empty();
			}

			//This is to make sure we add the last index guy to our array.
			if ((Index + 1) == CutSceneActorsGUIDDataPtr->PlayableCameraGuidList.Num() - 1)
			{
				CameraGUIDGroup.Add(CutSceneActorsGUIDDataPtr->PlayableCameraGuidList[Index + 1]);
			}
		}
	}
	else
	{
		CameraGUIDGroup.Add(CutSceneActorsGUIDDataPtr->PlayableCameraGuidList[0]);
	}

	//add the last data also. This may be the only data if all cameras have same timeline.
	GroupTimeSortedCameraGUIDs.Add(CameraGUIDGroup);

	m_TimeLineForCamera += time;
	int NextTimeLine = 0;

	FMovieSceneObjectBindingID chosenCameraBindingId;

	// Set one of the found camera tracks to be bound to the dummy CameraCut track, this allows multiple camera variants to be present in a single sequence
	// https://udn.unrealengine.com/questions/408133/select-desired-cine-cameras-in-sequencer.html

	TArray<FCameraActorData> ProcessTheCameraGuidList;
	ProcessTheCameraGuidList.Empty();

	if (CutSceneActorsGUIDDataPtr &&
		CutSceneActorsGUIDDataPtr->PlayableCameraGuidList.Num() > 0)
	{
		if (CutSceneActorsGUIDDataPtr->PlayableCameraGuidList.Num() == 1)
		{
			ProcessTheCameraGuidList.Add(CutSceneActorsGUIDDataPtr->PlayableCameraGuidList[0]);
		}
		else
		{
			int len = CutSceneActorsGUIDDataPtr->PlayableCameraGuidList.Num();
			int selected = 0;

			for (auto index = 0; index < GroupTimeSortedCameraGUIDs.Num(); ++index)
			{
				if (GroupTimeSortedCameraGUIDs[index].Num())
				{
					if (GroupTimeSortedCameraGUIDs[index][0].TimeLine <= m_TimeLineForCamera)
					{
						selected = index;
					}
				}
			}
			ProcessTheCameraGuidList = GroupTimeSortedCameraGUIDs[selected];
		}

		if (!ProcessTheCameraGuidList.Num())
		{
			UE_LOG(LogTemp, Log, TEXT("Something wrong in CutScene Camera."));
			return;
		}

		//randomise between the cameras which has same timeline.
		if (ProcessTheCameraGuidList[0].TimeLine != m_PrevSelectCameraTimeLine)
		{
			m_SelectedCameraIndex = game->GetRNG()->RAND_CALL(MabUInt32) % (ProcessTheCameraGuidList.Num());
			m_PrevSelectCameraTimeLine = ProcessTheCameraGuidList[0].TimeLine;
		}

		ensureAlways(m_SelectedCameraIndex < ProcessTheCameraGuidList.Num());

		if (ProcessTheCameraGuidList.Num() && m_SelectedCameraIndex >= ProcessTheCameraGuidList.Num())
		{
			m_SelectedCameraIndex = 0;
			MABBREAK(); //we should not be reaching here...check the logic please...
		}

		// Apparently this is how we get a BindingID from a GUID.

		//FMovieSceneObjectBindingID(ProcessTheCameraGuidList[m_SelectedCameraIndex].Guid, MovieSceneSequenceID::Root, EMovieSceneObjectBindingSpace::Local); // GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL change to new API,
		const UE::MovieScene::FRelativeObjectBindingID FRelative(ProcessTheCameraGuidList[m_SelectedCameraIndex].Guid, MovieSceneSequenceID::Root);
		chosenCameraBindingId = FMovieSceneObjectBindingID(FRelative);

		TArray<UObject*> boundObjList;

		boundObjList.Empty();


		if (m_PrevchosenCameraBindingId != chosenCameraBindingId)
		{
			// Find the chosen camera object. GetBoundObjects will always return an empty list if pSequencePlayer->Play() has not been called yet.			
			boundObjList = CurrentCutscenes->m_SequencePlayer->GetBoundObjects(chosenCameraBindingId);

			if (boundObjList.Num() > 0)
			{
				// Convert to an actor
				AActor* pAsActor = Cast<AActor>(boundObjList[0]);
				if (pAsActor)
				{
					// For some reason SetBinding takes in a list of actors to bind.
					TArray<AActor*> boundActorList = { pAsActor };

					//FMovieSceneObjectBindingID cutCamBinding = FMovieSceneObjectBindingID(CutSceneActorsGUIDDataPtr->DummyCameraGuid,
						//MovieSceneSequenceID::Root, EMovieSceneObjectBindingSpace::Local); // GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL change to new API,
					const UE::MovieScene::FRelativeObjectBindingID FRelativeLocal(CutSceneActorsGUIDDataPtr->DummyCameraGuid,
						MovieSceneSequenceID::Root);
					FMovieSceneObjectBindingID cutCamBinding = FMovieSceneObjectBindingID(FRelativeLocal);

					// Bind the chosen camera actor to the dummy CamerCut track so that it will be used as the current viewport.
					CurrentCutscenes->m_pCurrentLevelSequence->SetBinding(cutCamBinding, boundActorList);

					if (CurrentCutscenes->m_pCurrentLevelSequence->SequencePlayer->IsPlaying())
					{
						//	UE_LOG(LogTemp, Log, TEXT("********** "));
						//	UE_LOG(LogTemp, Log, TEXT("**Cutscene: Switching to a new camera in the cutscene*** "));
						//	UE_LOG(LogTemp, Log, TEXT(" TimeLineElapsed : %.3f"), m_TimeLineForCamera);
						//	UE_LOG(LogTemp, Log, TEXT("********** "));
						m_PrevchosenCameraBindingId = chosenCameraBindingId;
					}
				}
			}
		}
	}
}//end of void SSCutSceneManager::PositionCamera(float time)

///-------------------------------------------------------------------------------

void SSCutSceneManager::CutSceneParticleTickEnable(bool bEnable, UCutScenes* CurrentCutscenes)
{
	UCutsceneActorData* CutSceneActorsGUIDDataPtr = CurrentCutscenes->m_ActorGUIDData;

	if (CutSceneActorsGUIDDataPtr)
	{
		for (const FPossessableParticleData& particleData : CutSceneActorsGUIDDataPtr->PossessableParticleDataList)
		{
			//FMovieSceneObjectBindingID objectBindingId = FMovieSceneObjectBindingID(particleData.Guid, MovieSceneSequenceID::Root, EMovieSceneObjectBindingSpace::Local); // GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL change to new API,
			const UE::MovieScene::FRelativeObjectBindingID FRelative(particleData.Guid, MovieSceneSequenceID::Root);
			FMovieSceneObjectBindingID objectBindingId = FMovieSceneObjectBindingID(FRelative);
			{
				TArray<UObject*> boundObjList = CurrentCutscenes->m_SequencePlayer->GetBoundObjects(objectBindingId);
				if (boundObjList.Num() > 0)
				{
					// Convert to an actor
					AActor* pActor = Cast<AActor>(boundObjList[0]);
					if (pActor)
					{
						TArray<UActorComponent*>  BPComponents = pActor->BlueprintCreatedComponents;

						for (auto& ParticleActor : BPComponents)
						{
							UParticleSystemComponent* ParticleComponent = Cast<UParticleSystemComponent>(ParticleActor);

							if (ParticleComponent)
							{
								pActor->SetActorTickEnabled(bEnable);
								ParticleComponent->SetComponentTickEnabled(bEnable);
							}
						}
					}
				}
			}
		}
	}
}

///-------------------------------------------------------------------------------
/// CutscenePaused
///-------------------------------------------------------------------------------

void SSCutSceneManager::CutScenePaused(UCutScenes* CurrentCutscenes)
{
	CutSceneParticleTickEnable(false, CurrentCutscenes);

	//other stuff if needed
}

///-------------------------------------------------------------------------------
/// CutSceneResume
///-------------------------------------------------------------------------------

void SSCutSceneManager::CutSceneResume(UCutScenes* CurrentCutscenes)
{
	CutSceneParticleTickEnable(true, CurrentCutscenes);

	//other stuff if needed
}

///-------------------------------------------------------------------------------
/// CutsceneEnded
///-------------------------------------------------------------------------------

void SSCutSceneManager::CutSceneEnded(UCutScenes* CurrentCutscenes, bool bPause)
{
	//bPause: Not used

	//if (bPause && CurrentCutscenes)
	//{
	//	m_PausedSequence.AddUnique(CurrentCutscenes->m_SequencePlayer);		
	//}

	MabVector<CutsceneElement*>::iterator ce_iter;
	MabVector<CutsceneElement*>::iterator ce_iter_end;
	CutsceneElement* cce = nullptr;
	ce_iter = cutscene_elements.begin();
	ce_iter_end = cutscene_elements.end();

	bool FoundMatchingCutScene = false;// to break from outer loop

	// Search for the first cinematic element
	for (; ce_iter != ce_iter_end; ++ce_iter)
	{
		if (FoundMatchingCutScene)
			break;

		cce = (*ce_iter);

		if (cce->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
		{
			CutsceneCinematicElement* cce_casted = (CutsceneCinematicElement*)cce;
			for (MabVector<StCinematicElementInfo>::iterator CurrentCinematicInfoPtr = cce_casted->cinematics.begin();
				CurrentCinematicInfoPtr != cce_casted->cinematics.end(); CurrentCinematicInfoPtr++)
			{
				if (CurrentCinematicInfoPtr->UCutscenePtr == CurrentCutscenes)
				{
					//	UE_LOG(LogTemp, Log, TEXT("Ended RestoreDataList: %d"), CurrentCinematicInfoPtr->CinematicElementInfoData.cutsceneActorRestoreDataList.Num());

					CurrentCinematicInfoPtr->CinematicElementInfoData.state = CST_FINISHED;

					for (int idx = 0; idx < CurrentCinematicInfoPtr->CinematicElementInfoData.cutsceneActorRestoreDataList.Num(); idx++)
					{
						ARugbyCharacter* RestorePlayer = CurrentCinematicInfoPtr->CinematicElementInfoData.cutsceneActorRestoreDataList[idx].cutsceneActorRestore;

						if (RestorePlayer)
						{
							if (CurrentCinematicInfoPtr->CinematicElementInfoData.cutsceneActorRestoreDataList[idx].isBenchPlayerRestore)
							{
								if (RestorePlayer->GetAttributes()->GetState() == PS_FIELD)
								{
									wwNETWORK_TRACE_JG("CutSceneEnded: Moving Player '%s' to Bench ", *RestorePlayer->GetName());
									RestorePlayer->GetAttributes()->GetTeam()->MoveToBench(
										RestorePlayer, PS_BENCH);
								}
							}

							if (game->GetBall()->GetHolder() == RestorePlayer)
							{
								wwNETWORK_TRACE_JG("%s CutSceneEnded: Ball DetachFromActor '%s'", UTF8_TO_TCHAR(*FString(__func__)), UTF8_TO_TCHAR(*RestorePlayer->GetName()));
								game->GetBall()->DetachFromActor(FDetachmentTransformRules::KeepWorldTransform);
							}

							RestorePlayer->GetState()->ReleaseFromCutScene(
								&(*CurrentCinematicInfoPtr));  //[pointer of an iterator: operator * returns a reference so doing & on a reference will give you the
							// address].

							if (RestorePlayer->GetAnimInstance() && (!CurrentCinematicInfoPtr->CinematicElementInfoData.is_looping /*|| !bPause*/))
							{
								RestorePlayer->OnEndCinematic();
								wwNETWORK_TRACE_JG("%s Restore '%s'", UTF8_TO_TCHAR(*FString(__func__)), UTF8_TO_TCHAR(*RestorePlayer->GetName()));
							}
						}
					}

					//restore the actors and ball if we are not playing a looping cutscene.
					if (!CurrentCinematicInfoPtr->CinematicElementInfoData.is_looping /*|| !bPause*/)
					{
						if (CurrentCinematicInfoPtr->CinematicElementInfoData.is_Main)
						{
							UpdatePlayerVisibility(false);

							if (game->GetBall())
							{
								game->GetBall()->SetVisible(true);
							}

							DeregisterExclusionZone();
						}
					}
					FoundMatchingCutScene = true;
					break;
				}
			}
		}
	}

	// if (m_CurrentCinematicInfoPtr)//stop the active cutscene
	//{
	//    m_CurrentCinematicInfoPtr->state = CST_DELETE;
	//}

	//
	//// kill sfx between cutscenes, to stop carry over
	//// TODO: this should fade nicely or something
	if (FoundMatchingCutScene)
	{
		if (SIFApplication::GetApplication())
		{
			if (SIFApplication::GetApplication()->GetRUAudio())
			{
				SIFApplication::GetApplication()->GetRUAudio()->StopEventsForCategory(SIF_SOUND_CUTSCENE_CATEGORY);
			}
		}

		wwNETWORK_TRACE_JG("Cutscene Ended: %s", TCHAR_TO_UTF8(*CurrentCutscenes->m_CurrentCinematicInfoData.CutSceneData.CutSceneFileName));

		wwNETWORK_TRACE_JG("**************************CutScene ENDS **************************");
	}
}

//-------------------------------------------------------------------------------
// delegates invoked from sequencers when cutscene starts. Dont call these manually
void SSCutSceneManager::CutSceneStarted(UCutScenes* CurrentCutscenes)
{
	//if (CurrentCutscenes->m_ActorGUIDData->PlayableCameraGuidList.Num())
	{
		MabVector<CutsceneElement*>::iterator ce_iter;
		MabVector<CutsceneElement*>::iterator ce_iter_end;
		CutsceneElement* cce = nullptr;
		ce_iter = cutscene_elements.begin();
		ce_iter_end = cutscene_elements.end();
		bool FoundMatchingCutScene = false;// to break from outer loop

		// Search for the first cinematic element
		for (; ce_iter != ce_iter_end; ++ce_iter)
		{
			if (FoundMatchingCutScene)
				break;

			cce = (*ce_iter);
			if (cce->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
			{
				CutsceneCinematicElement* cce_casted = (CutsceneCinematicElement*)cce;
				for (MabVector<StCinematicElementInfo>::iterator CurrentCinematicInfoPtr = cce_casted->cinematics.begin();
					CurrentCinematicInfoPtr != cce_casted->cinematics.end(); CurrentCinematicInfoPtr++)
				{
					if ((CurrentCinematicInfoPtr->CinematicElementInfoData.CutSceneData.CutSceneID == CurrentCutscenes->m_CurrentCinematicInfoData.CutSceneData.CutSceneID) &&
						(CurrentCinematicInfoPtr->CinematicElementInfoData.CutSceneData.CutSceneIndex == CurrentCutscenes->m_CurrentCinematicInfoData.CutSceneData.CutSceneIndex))
					{
						wwNETWORK_TRACE_JG("Started RestoreDataList: %d NonActorHide '%d' HideBall '%d'", CurrentCinematicInfoPtr->CinematicElementInfoData.cutsceneActorRestoreDataList.Num(), (int)CurrentCinematicInfoPtr->CinematicElementInfoData.HideNonActors, (int)CurrentCinematicInfoPtr->CinematicElementInfoData.HideBall);

						CurrentCinematicInfoPtr->UCutscenePtr = CurrentCutscenes;
						UpdatePlayerVisibility(CurrentCinematicInfoPtr->CinematicElementInfoData.HideNonActors);

						if (game->GetBall()->IsVisible() && CurrentCinematicInfoPtr->CinematicElementInfoData.HideBall && CurrentCinematicInfoPtr->CinematicElementInfoData.is_Main) //Let main cutscene decide whether to show or hide ball
							game->GetBall()->SetVisible(false);

						StCinematicElementInfo* cinematic = cce_casted->GetCinematic(0);

						if (CurrentCutscenes->m_ActorGUIDData->PlayableCameraGuidList.Num() > 0 && CurrentCinematicInfoPtr->CinematicElementInfoData.is_Main)
						{
							m_TimeLineForCamera = 0.0f;

							m_PrevchosenCameraBindingId.SetGuid(FGuid()); //reinitialise

							m_PrevSelectCameraTimeLine = -1;

							PositionCamera(0.0f, CurrentCutscenes);
						}

						FoundMatchingCutScene = true;
						break;
					}
				}
			}
		}
	}

	////override settings from new camera //Nirupam: James O asked me to comment this as it broke Grayscale camera in Training Demo.
	//if (game->IsMenu())
	//{
	//	APostProcessingRugby* pp = nullptr;
	//	TArray<AActor*> actorArray;
	//	UGameplayStatics::GetAllActorsOfClass(game->GetGameInstance().GetWorld(), APostProcessingRugby::StaticClass(), actorArray);
	//	if (actorArray.Num() >= 1)
	//	{
	//		pp = Cast<APostProcessingRugby>(actorArray[0]);
	//	}
	//	
	//	if (pp && pp->IsValidLowLevel())
	//	{

	//		ARugbyPlayerController* playerCtrl = Cast<ARugbyPlayerController>(UGameplayStatics::GetPlayerController(game->GetGameInstance().GetWorld(), 0));

	//		if (playerCtrl && playerCtrl->IsValidLowLevel())
	//		{
	//			ACameraActor* currentCamera = playerCtrl->GetDefaultGameCamera();
	//			UCameraComponent* cameraComp = currentCamera->GetCameraComponent();
	//			cameraComp->PostProcessBlendWeight = 0.0f;
	//			cameraComp->PostProcessSettings = pp->MainMenuPostProcessing->Settings;
	//		}
	//	}
	//}

	//wwNETWORK_TRACE_JG("Cutscene Started: %s",
	//	   *(((StCinematicElementInfo*)(CurrentCutscenes->m_CurrentCinematicInfoPtr))->CutSceneData.CutSceneFileName));
}

///-------------------------------------------------------------------------------
/// InitialiseCutSceneProps - Initialises the cutscene Props data record
///-------------------------------------------------------------------------------

//void SSCutSceneManager::InitialiseCutScenePropsDataTable() //we dont need this code, as props are now added to sequencers directly
//{
//	FCutscenePropRec cutscenePropRec;
//
//	FString sequencePath;
//
//	//m_cutsceneRecArray.Empty();
//
//	const FString SEQUENCE_ASSET_PATH = "/Game/Rugby/DataTables/Props/Props";
//
//	UDataTable* ExcelTable;
//	FString pathName = SEQUENCE_ASSET_PATH;
//
//	ExcelTable = ConstructorHelpersInternal::FindOrLoadObject<UDataTable>(pathName);
//
//	if (ExcelTable)
//	{
//		TArray<FCutscenePropDescriptor> AbilityArray;
//		FString ContextString;
//		TArray<FName> RowNames;
//		RowNames = ExcelTable->GetRowNames();
//		int32 rowcount = 0;
//
//		for (const auto& name : RowNames)
//		{
//			FCutscenePropDescriptor* row = ExcelTable->FindRow<FCutscenePropDescriptor>(name, ContextString);
//			if (row)
//			{
//				AbilityArray.Add(*row);
//			}
//			rowcount++;
//		}		
//
//		for (auto index = 0; index < rowcount; ++index)
//		{
//			cutscenePropRec.m_Propname = AbilityArray[index].PropName;
//			cutscenePropRec.m_typeID = AbilityArray[index].PropType;
//
//			// Create soft object paths to sequence files.
//			cutscenePropRec.m_PropSoftPath.Reset();
//
//			if (cutscenePropRec.m_typeID > ECutscenePropType::None && cutscenePropRec.m_typeID < ECutscenePropType::MAX && AbilityArray[index].PropPath.Len() > 0) //ensure typeID is non-zero and we have a valid path
//			{
//				sequencePath = AbilityArray[index].PropPath;
//				cutscenePropRec.m_PropSoftPath.SetPath(sequencePath);
//				m_cutscenePropRecArray.Add(cutscenePropRec);
//			}
//		}
//	}
//	else
//	{
//		UE_LOG(LogTemp, Log, TEXT("DATA TABLE FAILED TO LOAD"));
//	}
//}

///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------

void SSCutSceneManager::InitialiseCutSceneEffectsDataTable()
{
	FString sequencePath;

	const FString SEQUENCE_ASSET_PATH = "/Game/Rugby/DataTables/Effects/CutSceneEffects";

	UDataTable* ExcelTable;
	FString pathName = SEQUENCE_ASSET_PATH;

	ExcelTable = ConstructorHelpersInternal::FindOrLoadObject<UDataTable>(pathName);

	if (ExcelTable)
	{
		FString ContextString;
		TArray<FName> RowNames;
		RowNames = ExcelTable->GetRowNames();
		int32 rowcount = 0;

		for (const auto& name : RowNames)
		{
			FCutsceneEffectsDescriptor* row = ExcelTable->FindRow<FCutsceneEffectsDescriptor>(name, ContextString);
			if (row)
			{
				m_cutsceneEffectsRecArray.Add(row->EffectType, row->EffectPath);
			}
		}
	}
	else
	{
		UE_LOG(LogTemp, Log, TEXT("InitialiseCutSceneEffectsDataTable: DATA TABLE FAILED TO LOAD"));
	}
}

///-------------------------------------------------------------------------------
/// InitialiseCutSceneDB - Initialises the cutscene data record
///-------------------------------------------------------------------------------

void SSCutSceneManager::InitialiseCutSceneDB()
{
	FCutsceneRec cutsceneRec;
	FString sequencePath;

	m_cutsceneRecArray.Empty();

	const FString SEQUENCE_ASSET_PATH = "/Game/Rugby/DataTables/Animations/DTCutscene";

	UDataTable* ExcelTable;
	FString pathName = SEQUENCE_ASSET_PATH;

	ExcelTable = ConstructorHelpersInternal::FindOrLoadObject<UDataTable>(pathName);

	if (ExcelTable)
	{
		TArray<FCutsceneDescriptor> AbilityArray;
		FString ContextString;
		TArray<FName> RowNames;
		RowNames = ExcelTable->GetRowNames();
		int32 rowcount = 0;

		for (const auto& name : RowNames)
		{
			FCutsceneDescriptor* row = ExcelTable->FindRow<FCutsceneDescriptor>(name, ContextString);
			if (row)
			{
				AbilityArray.Add(*row);
			}
			rowcount++;
		}

		m_cutsceneRecCount = rowcount;

		for (auto index = 0; index < m_cutsceneRecCount; ++index)
		{
			cutsceneRec.m_ID = index;
			cutsceneRec.m_name = AbilityArray[index].cutsceneName;
			cutsceneRec.m_typeID = AbilityArray[index].cutsceneID;
			cutsceneRec.m_IsR7 = AbilityArray[index].IsR7;
			cutsceneRec.m_IsR7Folder = AbilityArray[index].IsR7Folder;

			// Create soft object paths to sequence files.
			cutsceneRec.m_sequenceSoftPath.Reset();

			if (cutsceneRec.m_typeID && AbilityArray[index].sequenceFilename.Len() > 0) //ensure typeID is non-zero and we have a valid path
			{
				sequencePath = "/Game/Rugby/CutScenes/" + AbilityArray[index].sequenceFilename;
				cutsceneRec.m_sequenceSoftPath.SetPath(sequencePath);

				if (cutsceneRec.m_sequenceSoftPath.IsValid())
				{
					FString dataAssetPath = sequencePath + "_data";
					FSoftObjectPath cutsceneDataPath(dataAssetPath);
					if (UObject* pDataObject = cutsceneDataPath.TryLoad())
					{
						cutsceneRec.m_pCutsceneDataAsset = Cast<UCutsceneActorData>(pDataObject);
						if (cutsceneRec.m_pCutsceneDataAsset)
						{
							URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

							if (pRugbyGameInstance)
							{
								pRugbyGameInstance->PreventObjectGC(cutsceneRec.m_pCutsceneDataAsset);
							}

							// Make sure we pick the most important cutscene players first, i.e. captain before random players
							// Since random selection can pick the captain, then when we need the captain it fails due to already being in the cutscene
							const auto predicate = [](const FFootballerActorData& a, const FFootballerActorData& b)
								{

									bool bSamePriority = SelectorPriority[a.PositionId] == SelectorPriority[b.PositionId];

									// If the selector priorities are the same, sort by the GUID so that the list is always sorted by something unique, giving a deterministic order for 
									// online matches.
									if (bSamePriority)
									{
										ensureAlwaysMsgf(a.Guid != b.Guid, TEXT("ERROR!!! The GUIDs for the footballers in this cutscene are the same, this might be fine in many place, but will cause issues with online sync. ERROR!!!"));
										return a.Guid < b.Guid;
									}

									return SelectorPriority[a.PositionId] > SelectorPriority[b.PositionId];
								};

							cutsceneRec.m_pCutsceneDataAsset->FootballerDataList.StableSort(predicate);
						}
					}
				}
				m_cutsceneRecArray.Add(cutsceneRec);
			}
		}
	}
	else
	{
		UE_LOG(LogTemp, Log, TEXT("DATA TABLE FAILED TO LOAD"));
	}
}

/////-------------------------------------------------------------------------------
//FCutscenePropRec* SSCutSceneManager::GetPropRec(FString PropName)
//{
//	FSoftObjectPath PropPath;
//
//	//get the propName
//	int idx = PropName.Find(TEXT("/"), ESearchCase::IgnoreCase, ESearchDir::FromEnd);
//
//	if (idx != INDEX_NONE && idx < PropName.Len())
//	{
//		PropName = PropName.RightChop(idx + 1);
//	}
//
//	//search the prop name in the datatable of prop
//	for (auto PropRecIdx = 0; PropRecIdx < m_cutscenePropRecArray.Num(); ++PropRecIdx)
//	{		
//		if (strstr(TCHAR_TO_ANSI(*m_cutscenePropRecArray[PropRecIdx].m_Propname), TCHAR_TO_ANSI(*PropName)))
//		{
//			PropPath = m_cutscenePropRecArray[PropRecIdx].m_PropSoftPath;
//			return &m_cutscenePropRecArray[PropRecIdx];
//		}
//	}
//	return nullptr; //nothing found, return nullptr
//}

///-------------------------------------------------------------------------------
/// GetCutsceneRec - Gets the data from the datatable as per the id
///-------------------------------------------------------------------------------

TArray<FCutsceneRec*> SSCutSceneManager::GetCutsceneRec(int id, int isRc7)
{
	//An Example of data in the datatable
	//So if 'id' (as passed in the function argument) matches to cutsceneID, return all the records.
	//in below example, if id is 36, then return array of 3 records.
	//also the datatable keeps the data sorted, so once find something not matching break the loop.
	/*
	cutsceneName					cutsceneID	IsR7	sequenceFilename
	-------------------------------------------------------------------------------
	CSEVENT_TRY_TMO					36			0		ce_gp_tmo/cs_tmo_e5_01_jbr
	CSEVENT_TRY_TMO					36			0		ce_gp_tmo/cs_tmo_e5_02_jbr
	CSEVENT_TRY_TMO					36			0		ce_gp_tmo/cs_tmo_e5_03_jbr
	CSEVENT_PREGAME_TEAM1_WALKON	38			1		rugby_sevens/ce_pg_walkonteam01/cs_team1runon_01
	*/


	// WJS RLC ##### Do we need special League cutscenes??
	wwNETWORK_TRACE_JG("GetCutsceneRec Function - cutsceneID : %d", id);
	bool FoundMatch = false;

	TArray<FCutsceneRec*> m_cutsceneRecList;
	if (m_cutsceneRecArray.Num())
	{
		if (id < m_cutsceneRecCount)
		{
			for (auto index = 0; index < m_cutsceneRecArray.Num(); ++index)
			{
				if (m_cutsceneRecArray[index].m_typeID == id)
				{
					if (isRc7) //check if we want R7 files
					{
						if (m_cutsceneRecArray[index].m_IsR7 == 0) //this means, there is no R7 file this cutscene, so pick normal file
						{
							FoundMatch = true;
							m_cutsceneRecList.Add(&(m_cutsceneRecArray[index]));
						}
						else //we have R7 files, so only pick files from R7 folder
						{
							if (m_cutsceneRecArray[index].m_IsR7Folder == 1)
							{
								FoundMatch = true;
								m_cutsceneRecList.Add(&(m_cutsceneRecArray[index]));
							}
						}
					}
					else // we dont want R7 file, so pick only non-R7 folder
					{
						if (m_cutsceneRecArray[index].m_IsR7Folder == 0)
						{
							FoundMatch = true;
							m_cutsceneRecList.Add(&(m_cutsceneRecArray[index]));
						}
					}
				}
				else
				{
					if (FoundMatch)
					{
						break;//the data is sorted in an order. So keep parsing the for loop as long as we found the match. Once we dont find a match, break the for loop.
					}
				}
			}
		}
	}

	return m_cutsceneRecList;
}

///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------
void SSCutSceneManager::PlayerCreatorPlayAnimation(StCinematicElementInfo* cs, float StartPosition /*= 0.0f*/) //#playerCreator
{
	if (cs->CinematicElementInfoData.CutSceneData.CutSceneID == CSEVENT_UI_CUSTOMIZE_PLAYER_BODY)
	{
		ARugbyCharacter* CustomisePlayer = nullptr;

		URugbyCharacterAnimInstance* pAnimInstance = nullptr;

		if (cs->CinematicElementInfoData.cutsceneActorRestoreDataList.Num() > 0)
		{
			CustomisePlayer = cs->CinematicElementInfoData.cutsceneActorRestoreDataList[0].cutsceneActorRestore;

			if (!CustomisePlayer)
			{
				return;
			}

			pAnimInstance = CustomisePlayer->GetAnimInstance();

			if (!pAnimInstance || pAnimInstance->m_bPlayerCreatorAnimOverrideIsNone)
			{
				return;
			}

			/*
			default:ATTRIBUTES (Random Animation from ABP)
			attribute: ATTRIBUTES (Random  Animation from ABP)
			head: PLAYER_CREATOR (pcrtn_bodypose_02 Animation) has face
			face-hair: FACE_HAIR (pcrtn_bodypose_02 Animation) has face
			body: PLAYER_CREATOR (pcrtn_bodypose_02) has face
			assecory: PLAYER_CREATOR (pcrtn_bodypose_02) has face
			tatoo: PLAYER_CREATOR (pcrtn_bodypose_02) has face
			*/

			FName AnimName = "";

			if (pAnimInstance->m_playerCreatorAnimOverride != EPlayerCreatorAnimOverrideType::ATTRIBUTES)
			{
				AnimName = CustomisePlayer->GetGender() == PLAYER_GENDER::PLAYER_GENDER_FEMALE ? "pcrtn_bodypose_fem_02" : "pcrtn_bodypose_02";
				//FString AnimName = /*CustomisePlayer->GetAnimInstance()->m_playerCreatorAnimOverride == EPlayerCreatorAnimOverrideType::FACE_AND_HAIR) ? "nbstand09" :*/"pcrtn_bodypose_02";
			}
			else
			{
				m_PlayerCreatorPrevAnimName = "";
				m_PlayerCreatorMontageInstance = nullptr;
			}

			if (CustomisePlayer && CustomisePlayer->GetAnimation() && m_PlayerCreatorPrevAnimName != AnimName && !AnimName.IsNone())
			{
				UAnimSequence* AnimSequenceptr = nullptr;

				m_PlayerCreatorMontageInstance = nullptr;

				const FRugbyAnimationLibrary* pAnimLib = CustomisePlayer->GetAnimation()->GetAnimationLibrary();

				if (pAnimLib)
				{
					AnimSequenceptr = pAnimLib->GetAnimSequenceByName(AnimName.ToString(), false);

					ensureAlways(AnimSequenceptr != nullptr);

					if (AnimSequenceptr)
					{
						m_PlayerCreatorMontageInstance = CustomisePlayer->GetAnimInstance()->PlayPlayerCreatorAnimation(AnimSequenceptr);

						ensureAlways(m_PlayerCreatorMontageInstance);

						if (m_PlayerCreatorMontageInstance)
						{
							m_PlayerCreatorPrevAnimName = AnimName;
						}
					}
				}
			}
		}//if (cs->CinematicElementInfoData.cutsceneActorRestoreDataList.Num() > 0)
	}//if (cs->CinematicElementInfoData.CutSceneData.CutSceneID == CSEVENT_UI_CUSTOMIZE_PLAYER_BODY)
}

///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------

void SSCutSceneManager::CleanUpPlayerCreator(StCinematicElementInfo* ci)
{
	if (ci->CinematicElementInfoData.CutSceneData.CutSceneID == CSEVENT_UI_CUSTOMIZE_PLAYER_BODY)
	{
		UE_LOG(LogTemp, Log, TEXT("SSCutSceneManager::CleanUpPlayerCreator"));

		if (game->GetBall()->IsVisible())
		{
			game->GetBall()->SetVisible(true);
		}

		if (ci->CinematicElementInfoData.cutsceneActorRestoreDataList.Num())
		{
			ARugbyCharacter* RestorePlayer = ci->CinematicElementInfoData.cutsceneActorRestoreDataList[0].cutsceneActorRestore;

			if (RestorePlayer)
			{
				if (ci->CinematicElementInfoData.cutsceneActorRestoreDataList[0].isBenchPlayerRestore)
				{
					RestorePlayer->GetAttributes()->GetTeam()->MoveToBench(
						RestorePlayer, PS_BENCH);
				}
				RestorePlayer->GetState()->ReleaseFromCutScene(
					&(*ci));  //[pointer of an iterator: operator * returns a reference so doing & on a reference will give you the 							   // address].
			}
		}
		UpdatePlayerVisibility(false);
		m_PlayerCreatorPrevAnimName = "";
	}
}

///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------

void SSCutSceneManager::StopSequencer(StCinematicElementInfo* ci)
{
	if (ci)
	{
		ci->CinematicElementInfoData.is_looping = false;
		ci->CinematicElementInfoData.state = CUTSCENE_STATE::CST_DELETE;

		CleanUpPlayerCreator(ci); //#playerCreator
	}

	if (UOBJ_IS_VALID(game->GetGameInstance().GetWorld())
		&& ci && UOBJ_IS_VALID(ci->UCutscenePtr) && UOBJ_IS_VALID(ci->UCutscenePtr->m_SequencePlayer)
		&& (ci->UCutscenePtr->m_SequencePlayer->IsPlaying() || ci->UCutscenePtr->m_SequencePlayer->IsPaused()))
	{
		UE_LOG(LogTemp, Log, TEXT("StopSequencer"));
		ci->UCutscenePtr->m_SequencePlayer->Stop();
	}
}


///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------

void SSCutSceneManager::PauseSequencer(StCinematicElementInfo* ci, CUTSCENE_STATE state)
{
	if (UOBJ_IS_VALID(game->GetGameInstance().GetWorld())
		&& ci && UOBJ_IS_VALID(ci->UCutscenePtr) && UOBJ_IS_VALID(ci->UCutscenePtr->m_SequencePlayer)
		&& (ci->UCutscenePtr->m_SequencePlayer->IsPlaying()))
	{
		ci->UCutscenePtr->m_SequencePlayer->Pause();
		UE_LOG(LogTemp, Log, TEXT("PauseSequencer '%s'"), *ci->UCutscenePtr->m_SequencePlayer->GetSequence()->GetName());
	}
}


///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------

bool SSCutSceneManager::IsPlayingLoopCutScene()
{
	for (MabVector<CutsceneElement*>::iterator it = cutscene_elements.begin(); it != cutscene_elements.end(); ++it)
	{
		CutsceneElement* ce = (*it);
		if (ce->cutscene_type == CUTSCENE_TYPE_CINEMATIC)
		{
			CutsceneCinematicElement* cce = (CutsceneCinematicElement*)ce;
			if (cce->GetMainCutScene() && cce->GetMainCutScene()->CinematicElementInfoData.is_looping)
			{
				return true;
			}
		}
	}
	return false;
}

#pragma optimize("",on) // WJS RLC 