/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Roles/Competitors/RURoleStandardBallHolder.h"

#include "Match/AI/Actions/RUActionPass.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/Ball/SSBall.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Character/RugbyPlayerController.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUInputKickInterface.h"
#include "Match/RugbyUnion/RUProModeDebugSettings.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Match/SSRoleOptionList.h"
#include "Match/SSSpatialHelper.h"
#include "Utility/RURandomNumberGenerator.h"

#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBallReceiver.h"

//#rc3_legacy_include #include "NMMabAnimationNetwork.h"

#ifdef ENABLE_OSD
#include "Match/Debug/RUGameDebugSettings.h"
#endif

#include "RugbyGameInstance.h"

#define NEW_PRO_REQUEST_LOGIC

MABRUNTIMETYPE_IMP1( RURoleStandardBallHolder, RURoleBaseBallHolder )

//---------------------------------------------------------------------------
// Standard Ball Holder Implementation
//---------------------------------------------------------------------------

RURoleStandardBallHolder::RURoleStandardBallHolder( SIFGameWorld* game ) :
	RURoleBaseBallHolder( game )
#if defined ENABLE_GAME_DEBUG_MENU
	, debug_key_pool(NULL)
#endif
	, can_force_ball( false )
	, current_pro_request( PAR_NONE )
	, wantsTo4020Kick( false )
	, wantsTo2040Kick( false )
{
	/*#if defined ENABLE_GAME_DEBUG_MENU
	SIFDebugDrawHandleManager* handle_mgr = SIFDebug::GetDebugDrawHandleManager();
	debug_key_pool = handle_mgr->AllocatePool(1000);				// cleared each frame
	#endif*/
}

/// Enter this role with the specified player
void RURoleStandardBallHolder::Enter( ARugbyCharacter* player )
{
	RURoleBaseBallHolder::Enter( player );

	//MABLOGDEBUG( "StandardBallHolder Entered on pid %d...", player->GetAttributes()->GetIndex() );
	RecalculateBaseAttackVariables();
	ResetAttackingOptionTimer( true );
	RecalculateKickingVariables();

	m_pGame->GetEvents()->pro_attack_influence_expired.Add(this, &RURoleStandardBallHolder::ResetProRequest);

	OnCollected();
}

/// Workout 4020 / 2040 kick frequency
void RURoleStandardBallHolder::RecalculateKickingVariables()
{
	if (MLW_4020_OVERRIDES())
	{
		// Increase players kick accuracy
		m_pPlayer->GetAttributes()->SetGeneralKickAccuracy(1.0f);
	}

	// If we are a good kicker
	if (m_pPlayer->GetAttributes()->IsPlayKicker() || MLW_4020_OVERRIDES())
	{
		// If we are on 4 or more tackles
		if (m_pGame->GetGameState()->GetTackleCount() >= 4 || MLW_4020_OVERRIDES())
		{
			// Get the frequency for roll
			float freq = m_pGame->GetGameSettings().game_settings.slider_fourtytwenty_kick_frequency;

			// If we are within our 40 meter..
			if (m_pGame->GetStrategyHelper()->BallIsIn4020KickRange(m_pPlayer))
			{
				// Roll against slider freq to see if we want to attempt it
				wantsTo4020Kick = m_pGame->GetRNG()->RAND_RANGED_CALL(float, 1.0f) <= freq;
			}
			else if(m_pGame->GetStrategyHelper()->BallIsIn2040KickRange(m_pPlayer))
			{
				// Roll against slider freq to see if we want to attempt it
				wantsTo2040Kick = m_pGame->GetRNG()->RAND_RANGED_CALL(float, 1.0f) <= freq;
			}
		}
		
	}
}

bool RURoleStandardBallHolder::CanExecuteActionNow( RU_ACTION_INDEX action )
{
	if(m_pActionManager->IsFreeBallAction(action))
		return false;
	return true;
}

/// Advance this role
void RURoleStandardBallHolder::UpdateLogic( const MabTimeStep& game_time_step )
{
	/// Set the agression flags
	if ( !m_lock_manager.UFIsLocked( UF_SETAGGRESSION ) )
	{
		wwNETWORK_TRACE_JG("AccumulateAggression {%4d} %s", __LINE__, __FILE__);
		m_pPlayer->GetAttributes()->AccumulateAggression( 0.6f, m_pPlayer->GetHumanPlayer() != NULL  );
	}

	/// Set the facing flags
	if ( !m_lock_manager.UFIsLocked( UF_SETFACING ) )
	{
		if ( m_pPlayer->GetMovement()->GetCurrentSpeed() < 1.9f )
			m_pPlayer->GetMovement()->SetFacingFlags( AFFLAG_FACEOPPGOALLINE );
		else
			m_pPlayer->GetMovement()->SetFacingFlags( AFFLAG_FACEMOTION );
	}

	/// Work out where the ballholder should be looking
	if ( !m_lock_manager.UFIsLocked( UF_SETLOOK ) )
	{
		DefaultLookBehaviour();
	}

	// Test to see if we are in ingoal. Unfortunate hack, but if we move out of ingoal, need to invalidate can_force_ball.
	if ( can_force_ball )
	{
		// Still in ingoal or anim play will cause us to go out of in goal?
		ERugbyPlayDirection opp_play_dir = m_pPlayer->GetAttributes()->GetOppositionTeam()->GetPlayDirection();
		const static float ANIM_Z_DISTANCE_NEEDED = 0.8f;
		FVector facing_vec;
		FVector player_pos = m_pPlayer->GetMabPosition();
		SSMath::AngleToMabVector3( m_pPlayer->GetMovement()->GetCurrentFacingAngle(), facing_vec );
		FVector projected_anim_placement = player_pos + facing_vec * ANIM_Z_DISTANCE_NEEDED;

		if ( !(m_pGame->GetSpatialHelper()->IsInInGoal( player_pos, opp_play_dir) && m_pGame->GetSpatialHelper()->IsInInGoal( projected_anim_placement, opp_play_dir) ) )
		{
			can_force_ball = false;
		}
	}

	/// Decide what attacking option to perform							
	if ( m_pGame->GetGameState()->GetBallHolder() == m_pPlayer)
	{
		/// If we are human controlled then let all control happen outside of this behaviour
		if ( m_pPlayer->GetHumanPlayer() == NULL )
		{
			// If you comment out this, it will stop the players from scoring. Good for debugging endless gameplay.
			UpdateTryScore();

			// if our last role as play the ball receiver, lets prioritise passing!
			if (m_pPlayer->GetPrevRole() == RURolePlayTheBallReceiver::RTTGetStaticType() && attack_vars.best_player_to_pass_to != nullptr)
			{
				// Force passing, giving the option to prioritse is too slow
				if (m_pPlayer->GetActionManager()->CanUseAction(ACTION_PASS))
				{
					m_pPlayer->GetActionManager()->StartAction<RUActionPass>(attack_vars.best_player_to_pass_to, PT_STANDARD);
					return;
				}
			}

			// wants to 4020
			if (m_pPlayer->GetActionManager()->CanUseAction(ACTION_KICK) && wantsTo4020Kick)
			{
				const FVector& player_pos = m_pPlayer->GetMovement()->GetCurrentPosition();
				RUStrategyPos pos;

				m_pGame->GetStrategyHelper()->FindBest4020KickPos(m_pPlayer, pos);

				FVector kick_to_pos(pos.x, 0.0f, pos.z);
				m_pPlayer->GetRole()->StartActionKickToPosition(KICKTYPE_FOURTYTWENTYKICK, kick_to_pos, true);
				FString msg = "Attempting a 4020 kick!";
				GEngine->AddOnScreenDebugMessage(-1, 6.0f, FColor::Green, *msg);
				return;
			}

			// wants to 2040
			if (m_pPlayer->GetActionManager()->CanUseAction(ACTION_KICK) && wantsTo2040Kick)
			{
				const FVector& player_pos = m_pPlayer->GetMovement()->GetCurrentPosition();
				RUStrategyPos pos;

				m_pGame->GetStrategyHelper()->FindBest2040KickPos(m_pPlayer, pos);

				FVector kick_to_pos(pos.x, 0.0f, pos.z);
				m_pPlayer->GetRole()->StartActionKickToPosition(KICKTYPE_TWENTYFOURTYKICK, kick_to_pos, true);
				FString msg = "Attempting a 2040 kick!";
				GEngine->AddOnScreenDebugMessage(-1, 6.0f, FColor::Green, *msg);
				return;
			}

			// if we're 5th tackle and have received the ball from a receiver, we need to kick it
			if (m_pPlayer->GetPrevRole() != RURolePlayTheBallReceiver::RTTGetStaticType() && (attack_vars.wants_to_kick_for_ground /* || m_pGame->GetGameState()->GetTackleCount() == 2 */))
			{
				// Standard kick for ground
				bool should_kick = !attack_vars.has_broken_defensive_line || (attack_vars.has_broken_defensive_line && !attack_vars.in_clear)
					|| attack_vars.best_player_to_pass_to == nullptr;

				// Normal kick
				if (should_kick && !attack_vars.approx_bh_will_score)
				{
					if (m_pPlayer->GetActionManager()->CanUseAction(ACTION_KICK))
					{
						const FVector& player_pos = m_pPlayer->GetMovement()->GetCurrentPosition();
						RUStrategyPos pos;

						m_pGame->GetStrategyHelper()->FindBestKickForGround(m_pPlayer, pos);

						FVector kick_to_pos(pos.x, 0.0f, pos.z);
						m_pPlayer->GetRole()->StartActionKickToPosition(KICKTYPE_LONGPUNT, kick_to_pos, true);
						FString msg = "Fifth tackle, prioritising kicking for ground!";
						GEngine->AddOnScreenDebugMessage(-1, 3.5f, FColor::Green, *msg);
						return;
					}
				}
			}

			// Gameplay testing, if player role is PROP, focus on running, don't worry about passing, go for ground
			// Prop has greater chance of breaking tackles
			if (MLW_PROP_ROLE_OVERRIDES() && m_pPlayer->GetAttributes()->GetPlayerPosition() & PP_PROP && m_pPlayer->GetPrevRole() != RURolePlayTheBallReceiver::RTTGetStaticType())
			{
				SSRoleOptionList role_options;
				AddDefaultRunOptions(role_options);
				role_options.ExecuteMostEffectiveAction(this);
				return;
			}
			
			float distanceFromOwnGoal = m_pGame->GetStrategyHelper()->GetBallRangeFromOwnGoal(m_pPlayer->GetAttributes()->GetTeam());	

			// GGS DJH: If we are basically in our own in-goal area AND can kick, then we should kick. related to Task #TASK 3175
			if (m_pPlayer->GetActionManager()->CanUseAction(ACTION_KICK) &&  distanceFromOwnGoal <= 0.0f)
			{
				const FVector& player_pos = m_pPlayer->GetMovement()->GetCurrentPosition();
				RUStrategyPos pos;

				m_pGame->GetStrategyHelper()->FindBestGenericKickPos(m_pPlayer, KickType::KICKTYPE_BOXKICK, pos);

				FVector kick_to_pos(pos.x, 0.0f, pos.z);
				m_pPlayer->GetRole()->StartActionKickToPosition(KickType::KICKTYPE_BOXKICK, kick_to_pos, true);
				return;
			}


			// -- GGS DJH: If we are close to turning over (on the 5th tackle) then we should NOT pass, but try and kick the ball to gain distance and keep it alive -> https://dev.azure.com/glindagames/NRL%20Rugby%20League/_workitems/edit/3259
		
			static const float LONG_KICK_DISTANCE = 50.0f; // 50 meters - Greater
			static const float GRUBBER_KICK_DISTANCE = 20.0f; // 20 meters - Less or equal
			static const float JUST_RUN_FOR_IT_DISTANCE = 10.0f; // 10 meters - Less or equal

			float distanceFromOpponentsGoal = m_pGame->GetStrategyHelper()->GetBallRangeFromOwnGoal(m_pPlayer->GetAttributes()->GetOppositionTeam());

			bool bIsCloseToTurnOver = m_pGame->GetGameState()->GetTackleCount() >= 5;
			bool bShouldTryAndKick = false;
			KickType kickType;

			// Love to do this a better way - but this is simple and does the job
			if (distanceFromOpponentsGoal <= JUST_RUN_FOR_IT_DISTANCE || !bIsCloseToTurnOver)
			{
				bShouldTryAndKick = false;
			}
			else if (distanceFromOpponentsGoal <= GRUBBER_KICK_DISTANCE)
			{
				bShouldTryAndKick = true;
				kickType = KickType::KICKTYPE_GRUBBERKICK;

			} 
			else if (distanceFromOpponentsGoal <= LONG_KICK_DISTANCE)
			{
				bShouldTryAndKick = true;
				kickType = KickType::KICKTYPE_CHIPKICK;
			}
			else
			{
				bShouldTryAndKick = true;
				kickType = KickType::KICKTYPE_BOXKICK;
			}


			if (m_pPlayer->GetActionManager()->CanUseAction(ACTION_KICK) && bShouldTryAndKick)
			{
				const FVector& player_pos = m_pPlayer->GetMovement()->GetCurrentPosition();
				RUStrategyPos pos;

				m_pGame->GetStrategyHelper()->FindBestGenericKickPos(m_pPlayer, kickType, pos);

				FVector kick_to_pos(pos.x, 0.0f, pos.z);
				m_pPlayer->GetRole()->StartActionKickToPosition(kickType, kick_to_pos, true);
				return;
			}
			// --- End of Comment ---


			if ( CanUseAttackingOption()  )
			{
				// Add some attacking options
				SSRoleOptionList role_options;

#ifndef NEW_PRO_REQUEST_LOGIC
				// check to see if we're being influenced. If we are, use that as our decision (pro mode stuff)
				// Dewald - WW
				// This is fairly flawed logic, because it depends on CanUseAttackingOption() to be true, causing delays in your pass request
				// It makes more sense, but it's also more frustrating.
				if(current_pro_request != PAI_NONE)
				{
					switch(current_pro_request)
					{
					case PAI_PASS:
						AddDefaultPassOptions( role_options, game_time_step.delta_time.ToSeconds(), 0 );
						break;
					case PAI_KICK_DOWN_FIELD:
						AddKickDownFieldOption( role_options );
						break;
					case PAI_KICK_INTO_TOUCH:
						AddKickForTouchOption( role_options );
						break;
					default:
						break;
					}
				}
				else
#endif
				{
					/// If it's time to use another attacking option then select from
					/// what we think is useful
					RecalculateBaseAttackVariables();
					if(!m_pPlayer->GetAttributes()->GetTeam()->GetPlayers().empty())
					{
						// Add all of our default options
						AddDefaultSideStepOptions( role_options );
						AddDefaultFendOptions( role_options );
						AddDefaultDummyOptions( role_options );
					}

					// Forces the AI kick for touch.
#ifdef FORCE_KICK_FOR_TOUCH
					AddKickForTouchPassOptions( role_options );
#else

					static const float DIST_FROM_TRY_LINE_UNDER_PRESSURE = 10.0f; // 10 meters
					static const float DIST_FROM_TRY_LINE_LIMIT_PASSING = 20.0f; // 20 meters				

					// Always kick if we need to kick, or we're close to our try line
					if (kick_this_possession || distanceFromOwnGoal < DIST_FROM_TRY_LINE_UNDER_PRESSURE)
					{
						AddDefaultKickOptions( attack_vars, role_options );
					}
					
					// Outside of our red-zone, we can just pass to anyone
					if (distanceFromOwnGoal > DIST_FROM_TRY_LINE_LIMIT_PASSING)
					{
						AddDefaultPassOptions( role_options, game_time_step.delta_time.ToSeconds(), 0 );
					}
					// If we're in our own red-zone, limit passing
					else
					{
						const static int MIN_PASS_PRIORITY_OPTION = 40;
						// inside own goal, only go for the good ones
						// GGS DJH: Don't pass in your red zone, just kick - The logic for this is already provided above, commented for future learning :)
						//AddDefaultPassOptions( role_options, game_time_step.delta_time.ToSeconds(), MIN_PASS_PRIORITY_OPTION );
					}

					/// If we should kick for touch on game finished then make sure we do so
					if ( m_pGame->GetStrategyHelper()->ShouldKickForTouchOnGameFinished( m_pPlayer ) )
						AddKickForTouchPassOptions( role_options );
#endif // FORCE_KICK_FOR_TOUCH

				}

				// Use a standard selector
				#ifdef ENABLE_OSD
				{
					/*const*/ static int ropt_ctr = 0;
					RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();

					settings->ClearDebugStrings( RUGameDebugSettings::DP_ROLE_OPTIONS );

					if ( role_options.GetNumOptions() > 0 )
					{
						ropt_ctr++;
						settings->PushDebugString( game, RUGameDebugSettings::DP_ROLE_OPTIONS, MabString( 16, "Update %d", ropt_ctr ).c_str() );
					}

					for( int i = 0; i < role_options.GetNumOptions(); i++ )
					{
						settings->PushDebugString( game,
							RUGameDebugSettings::DP_ROLE_OPTIONS,
							role_options.GetOptionDescriptor( i ).c_str()
						);
					}
				}
				#endif

				//AddDefaultRunOptions( role_options );
				if ( PerformStandardOptionsSelection( role_options ) )
				{
					ResetBaseAttackVariables();
					ResetAttackingOptionTimer( false );
				}


			}

			/// Always try and run in the like a basic ballholder
			{
				// Update the current run option
				SSRoleOptionList role_options;
				AddDefaultRunOptions( role_options );

				role_options.ExecuteMostEffectiveAction( this );
			}

			if ( !m_pPlayer->GetActionManager()->UFIsLocked( UF_SETWAYPOINT ) )
				m_pPlayer->GetMovement()->SetTargetSpeed( m_pPlayer->GetMovement()->GetIdealSpeed( AS_SPRINT ) );
		}
		else
		{
			if ( CanForceBall() )
			{
				// The player is the ballholder and human controlled. If they are in the in-goal area, show the contextual for grounding the ball.
				m_pGame->GetEvents()->ballholder_can_force_ball( m_pPlayer, true );
			}
			else
			{
				// Hide the contextual.
				m_pGame->GetEvents()->ballholder_can_force_ball( m_pPlayer, false );
			}
		}

		RURoleBaseBallHolder::UpdateLogic( game_time_step );
	}
	/// We were the most recent ball holder?
	else
	{
		RUPlayerMovement* movement = m_pPlayer->GetMovement();
		if ( movement->ShouldChangeWaypoint() )
		{
			/// Stand at an offset on the side of the ball that we are already on
			const static float X_OFFSET = 5.0f;
			const static float Z_OFFSET = 2.0f;
			FVector ball_pos = m_pGame->GetBall()->GetCurrentPosition();
			FVector our_pos = movement->GetCurrentPosition();
			FVector target = m_pGame->GetBall()->GetCurrentPosition() + FVector( X_OFFSET * MabMath::Sign( our_pos.x - ball_pos.x ), 0.0f, -Z_OFFSET * m_pPlayer->GetAttributes()->GetPlayDirection() );
			wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
			movement->SetTargetPosition( target );
			movement->SetThrottleAndTargetSpeedByUrgency( 0.8f );
		}
	}

	//int prim_idx = RUGAME_MDD_BASE + 2000;
	//SIF_DEBUG_DRAW( Set3DLine( prim_idx++, player->GetCurrentPosition(), approx_bh_intercept_point,  MabColour::White, MabColour::Green ) );
	//SIF_DEBUG_DRAW( Set3DLine( prim_idx++, approx_bh_intercept_point, approx_bh_intercept_point + FVector( 0.0f, 3.0f, 0.0f ),  MabColour::White, MabColour::Green ) );




#if defined BUILD_DEBUG && defined NEW_PRO_REQUEST_LOGIC && defined ENABLE_GAME_DEBUG_MENU && PLATFORM_WINDOWS
	// Draw the radius that the BH can pass
	debug_key_pool->ReleaseAll();
	int prim_idx = 2000;
	static const int SPACE_INCREMENT = 2;

	/////////
	// Draw the pass calling zone
	/////////
	float rad = SIFDebug::GetProModeDebugSettings()->GetOverridePassReqDistEnabled() ? SIFDebug::GetProModeDebugSettings()->GetPassReqVal() : MAX_DIST_PRO_REQ_PASS;
	// Draw the zone ellipse
	float half_width  = rad * 0.5f;
	float half_height = rad * 0.5f;

	static const int DIVISIONS = 24;
	FVector centre = movement->GetCurrentPosition();

	float angle_div = PI * 2.0f / (float) DIVISIONS;

	for( int j = 0; j < DIVISIONS; j+=SPACE_INCREMENT )
	{
		float ang_from = j * angle_div;
		float ang_to   = ang_from + angle_div;
		FVector from( centre.x + MabMath::Sin( ang_from ) * half_width, 0.0f, centre.z + MabMath::Cos( ang_from ) * half_height );
		FVector to  ( centre.x + MabMath::Sin( ang_to   ) * half_width, 0.0f, centre.z + MabMath::Cos( ang_to   ) * half_height );

		if(SIFDebug::GetProModeDebugSettings()->GetShowCanReqPassEnabled())
			SETDEBUGLINE( prim_idx++, from, to, MabColour::Green, MabColour::Green );
		else
			REMOVEDEBUGLINE( prim_idx++ );
	}

	/////////
	// Draw the kick down field calling zone
	/////////
	rad = SIFDebug::GetProModeDebugSettings()->GetOverrideKickDownFieldReqDistEnabled() ? SIFDebug::GetProModeDebugSettings()->GetKickDownFieldReqVal() : MAX_DIST_PRO_REQ_KICK_DOWN_FIELD;
	// Draw the zone ellipse
	half_width  = rad * 0.5f;
	half_height = rad * 0.5f;

	for( int j = 0; j < DIVISIONS; j+=SPACE_INCREMENT )
	{
		float ang_from = j * angle_div;
		float ang_to   = ang_from + angle_div;
		FVector from( centre.x + MabMath::Sin( ang_from ) * half_width, 0.0f, centre.z + MabMath::Cos( ang_from ) * half_height );
		FVector to  ( centre.x + MabMath::Sin( ang_to   ) * half_width, 0.0f, centre.z + MabMath::Cos( ang_to   ) * half_height );

		if(SIFDebug::GetProModeDebugSettings()->GetShowCanReqKickDownFieldEnabled())
			SETDEBUGLINE( prim_idx++, from, to, MabColour::Green, MabColour::Green );
		else
			REMOVEDEBUGLINE( prim_idx++ );
	}

	/////////
	// Draw the kick for touch calling zone
	/////////
	rad = SIFDebug::GetProModeDebugSettings()->GetOverrideKickForTouchReqDistEnabled() ? SIFDebug::GetProModeDebugSettings()->GetKickForTouchReqVal() : MAX_DIST_PRO_REQ_KICK_FOR_TOUCH;
	// Draw the zone ellipse
	half_width  = rad * 0.5f;
	half_height = rad * 0.5f;

	for( int j = 0; j < DIVISIONS; j+=SPACE_INCREMENT )
	{
		float ang_from = j * angle_div;
		float ang_to   = ang_from + angle_div;
		FVector from( centre.x + MabMath::Sin( ang_from ) * half_width, 0.0f, centre.z + MabMath::Cos( ang_from ) * half_height );
		FVector to  ( centre.x + MabMath::Sin( ang_to   ) * half_width, 0.0f, centre.z + MabMath::Cos( ang_to   ) * half_height );

		if(SIFDebug::GetProModeDebugSettings()->GetShowCanReqKickForTouchEnabled())
			SETDEBUGLINE( prim_idx++, from, to, MabColour::Green, MabColour::Green );
		else
			REMOVEDEBUGLINE( prim_idx++ );
	}

	/////////
	// Draw the pro pass distance
	/////////
	float x_dist = SIFDebug::GetProModeDebugSettings()->GetOverrideProPassDistDistEnabled() ? SIFDebug::GetProModeDebugSettings()->GetProPassDistXVal() : (6.7f *  6.0f);
	float z_dist = SIFDebug::GetProModeDebugSettings()->GetOverrideProPassDistDistEnabled() ? SIFDebug::GetProModeDebugSettings()->GetProPassDistZVal() : (40.0f);
	// Draw the zone ellipse
	half_width  = x_dist * 0.5f;
	half_height = z_dist * 0.5f;

	for( int j = 0; j < DIVISIONS; j+=SPACE_INCREMENT )
	{
		float ang_from = j * angle_div;
		float ang_to   = ang_from + angle_div;
		FVector from( centre.x + MabMath::Sin( ang_from ) * half_width, 0.0f, centre.z + MabMath::Cos( ang_from ) * half_height );
		FVector to  ( centre.x + MabMath::Sin( ang_to   ) * half_width, 0.0f, centre.z + MabMath::Cos( ang_to   ) * half_height );

		if(SIFDebug::GetProModeDebugSettings()->GetDrawProReqPassDistanceEnabled())
			SETDEBUGLINE( prim_idx++, from, to, MabColour::Green, MabColour::Green );
		else
			REMOVEDEBUGLINE( prim_idx++ );
	}

	/////////
	// Draw the normal pass distance
	/////////
	x_dist = (6.7f *  3.0f);
	z_dist = (20.0f);
	// Draw the zone ellipse
	half_width  = x_dist * 0.5f;
	half_height = z_dist * 0.5f;

	for( int j = 0; j < DIVISIONS; j+=SPACE_INCREMENT )
	{
		float ang_from = j * angle_div;
		float ang_to   = ang_from + angle_div;
		FVector from( centre.x + MabMath::Sin( ang_from ) * half_width, 0.0f, centre.z + MabMath::Cos( ang_from ) * half_height );
		FVector to  ( centre.x + MabMath::Sin( ang_to   ) * half_width, 0.0f, centre.z + MabMath::Cos( ang_to   ) * half_height );

		if(SIFDebug::GetProModeDebugSettings()->GetDrawNormalPassDistanceEnabled())
			SETDEBUGLINE( prim_idx++, from, to, MabColour::Green, MabColour::Green );
		else
			REMOVEDEBUGLINE( prim_idx++ );
	}


	// Show how the ball holder is currently influenced
	if(SIFDebug::GetProModeDebugSettings()->GetShowInfluenceEnabled())
	{
		MabMatrix mtx;
		//#rc3_legacy PSSGMab::Set(mtx, player->GetGraphicsObject()->m_globalMatrix);
		MabString buffer;
		buffer += MabString(0, "PAI: %s", RU_PRO_ATTACK_REQUEST_STRINGS[current_pro_request]);

		SIF_DEBUG_DRAW( SetText( 515615, mtx.GetTranslation(), buffer, MabColour::White ) );
	}
	else
	{
		SIF_DEBUG_DRAW( RemoveText( 515615 ) );
	}
#endif
}

/// Exit this role
void RURoleStandardBallHolder::Exit(bool forced)
{
	//player->EnableAggressionIndicator( false );
	can_force_ball = false;

	if(SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro())
	{
		// Make sure we aren't influence anymore, otherwise it will kick in straight away the next time we get the ball
		m_pGame->GetEvents()->pro_attack_influence_expired.Remove(this, &RURoleStandardBallHolder::ResetProRequest);
		ResetProRequest();
	}

	// clear up any human kick hud
	SSHumanPlayer* human = m_pPlayer->GetHumanPlayer();
	if ( human != NULL )
	{
		m_pGame->GetInputManager()->GetKickInterface()->StopKick();
	}

	#ifdef BUILD_DEBUG
	//SIF_DEBUG_DRAW( RemoveText( 515615 ) );
	//player->SetDebugFlags( 0 );
	#endif

	RURoleBaseBallHolder::Exit(forced);
}

/// Get the fitness of the player for the given role
int RURoleStandardBallHolder::GetFitness(const ARugbyCharacter* player, const SSRoleArea*)
{
	SIFGameWorld* game = player->GetGameWorld();
	MABASSERT( game != NULL );

	/// Really we only allow people who are the ball holder to use this role
	if ( game->GetGameState()->GetBallHolder() == player )
		return 75;

	/// TYRONE: BUT - We have this issue where if a ball holder passes, then no-one will be assigned, which can reduce the number of overall
	/// roles that are being assigned and roles to switch over - so we keep the last ball holder as an option and
	/// this now has behaviour to handle what to do when I am the last ball holder
	if ( game->GetGameState()->GetLastBallHolder() == player )
		return 20;

	return UNSELECTABLE_FITNESS;
}

///-------------------------------------------------------------------------
/// IsInterruptable
///-------------------------------------------------------------------------

bool RURoleStandardBallHolder::IsInterruptable() const
{
	if(m_pGame->GetGameState()->GetPhase() == RUGamePhase::RUCK)
	{
		RUActionManager *action_manager = m_pPlayer->GetActionManager();

		if ( action_manager->IsActionRunning( ACTION_TACKLEE ) )
			return false;

		if ( action_manager->IsActionRunning( ACTION_TACKLER ))
			return false;
	}

	return true;
}

void RURoleStandardBallHolder::UpdateTryScore()
{
	/// If we are not AI then don't auto score
	if ( m_pPlayer->GetHumanPlayer() != NULL )
		return;

	// Check if ball is already at try
	if ( m_pGame->GetGameState()->IsTryBallDown() )
		return;

	/// If we are in a position to score a try then do so
	SSSpatialHelper * spatial_helper = m_pGame->GetSpatialHelper();
	ARugbyCharacter* closest_opp_player_by_time = spatial_helper->FindLowestClosingTimePlayerToPlayer( m_pPlayer, &m_pPlayer->GetAttributes()->GetOppositionTeam()->GetPlayers() );

	float lowest_closing_time = FLT_MAX;
	if ( closest_opp_player_by_time != NULL )
		lowest_closing_time = spatial_helper->GetPlayerToPlayerClosingTime( m_pPlayer, closest_opp_player_by_time );

	const static float MIN_ATTEMPT_TRY_DIST = 1.5f;
	const static float MIN_CLOSING_TIME = 1.0f;

	FieldExtents extents = m_pGame->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	extents.y *= 0.5f;
	float dist_to_goal = extents.y - (m_pPlayer->GetMovement()->GetCurrentPosition().z * (float) m_pPlayer->GetAttributes()->GetPlayDirection());

	/// If we are close to the center
	bool opp_close = lowest_closing_time <= MIN_CLOSING_TIME;
	bool over_line = dist_to_goal <= 0.0f;
	bool close_to_line = dist_to_goal < MIN_ATTEMPT_TRY_DIST;

	bool attempt_try = (opp_close && close_to_line) || over_line;

	if ( !attempt_try )
	{
		if ( opp_close && CanForceBall() )
		{
			attempt_try = true;
		}
	}

	if ( attempt_try && m_pPlayer->GetActionManager()->CanUseAction( ACTION_TRY ))
		StartActionTry();
}

/// Tests to see if we should be able to force/ground the ball in own ingoal area.
void RURoleStandardBallHolder::OnCollected()
{
	const BallFreeInfo& ball_free_info = m_pGame->GetStrategyHelper()->GetLastBallFreeInfo();
	const BallCollectInfo& ball_collect_info = m_pGame->GetStrategyHelper()->GetLastBallCollectInfo();

	bool we_collected_ball = ball_collect_info.last_player == m_pPlayer;
	bool opponent_put_ball_in_ingoal = ball_free_info.last_player && ball_free_info.last_player->GetAttributes()->GetTeam() != m_pPlayer->GetAttributes()->GetTeam();

	ERugbyPlayDirection opp_play_dir = m_pPlayer->GetAttributes()->GetOppositionTeam()->GetPlayDirection();
	bool in_ingoal = m_pGame->GetSpatialHelper()->IsInInGoal( m_pPlayer->GetMabPosition(), opp_play_dir );

	bool collected_in_ingoal = m_pGame->GetSpatialHelper()->IsInInGoal( ball_collect_info.pos, opp_play_dir );

	can_force_ball = we_collected_ball && opponent_put_ball_in_ingoal && collected_in_ingoal && in_ingoal;
}

bool RURoleStandardBallHolder::CanForceBall()
{
	return can_force_ball;
}




// *********************************************************************************************
// *********************************************************************************************
// ************************************* PRO REQUEST *******************************************
// *********************************************************************************************
// *********************************************************************************************


// If the BH and pro player is too far apart don't even allow a request
bool RURoleStandardBallHolder::CanMakeProRequest(RU_PRO_ATTACK_REQUEST influence, ARugbyCharacter* proPlayer)
{
	const FVector& player_pos  = m_pPlayer->GetMovement()->GetCurrentPosition();
	const FVector& receiver_pos = proPlayer->GetMovement()->GetCurrentPosition();

	float distance = SSMath::GetXZPointToPointDistance(player_pos, receiver_pos);
	float minDist = 60.0f;

	if(influence == PAR_PASS)
		minDist = MAX_DIST_PRO_REQ_PASS;
	else if (influence == PAR_KICK_DOWN_FIELD)
		minDist = MAX_DIST_PRO_REQ_KICK_DOWN_FIELD;
	else if(influence == PAR_KICK_FOR_TOUCH)
		minDist = MAX_DIST_PRO_REQ_KICK_FOR_TOUCH;

	//Debug overrides
#if defined BUILD_DEBUG && defined NEW_PRO_REQUEST_LOGIC && defined ENABLE_GAME_DEBUG_MENU
	if(influence == PAR_PASS)
		minDist = SIFDebug::GetProModeDebugSettings()->GetOverridePassReqDistEnabled() ? SIFDebug::GetProModeDebugSettings()->GetPassReqVal() : minDist;
	else if (influence == PAR_KICK_DOWN_FIELD)
		minDist = SIFDebug::GetProModeDebugSettings()->GetOverrideKickDownFieldReqDistEnabled() ? SIFDebug::GetProModeDebugSettings()->GetKickDownFieldReqVal() : minDist;
	else if(influence == PAR_KICK_FOR_TOUCH)
		minDist = SIFDebug::GetProModeDebugSettings()->GetOverrideKickForTouchReqDistEnabled() ? SIFDebug::GetProModeDebugSettings()->GetKickForTouchReqVal() : minDist;
#endif

	return distance < minDist;
}

bool RURoleStandardBallHolder::MakeProRequest(RU_PRO_ATTACK_REQUEST influence, ARugbyCharacter* proPlayer)
{
	if(CanMakeProRequest(influence, proPlayer))
	{
		MABASSERT( SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() );
		current_pro_request = influence;

#ifdef NEW_PRO_REQUEST_LOGIC
		// Requesting a pass
		if(influence == PAR_PASS)
			PerformProRequestPass();

		else if (influence == PAR_KICK_DOWN_FIELD)
			PerformProRequestKickDownField();

		else if(influence == PAR_KICK_FOR_TOUCH)
			PerformProRequestKickForTouch();

		return true;
#endif
	}
	else
	{
		MabString title(0);
		if(influence == PAR_PASS)
			title = "[ID_REQUESTPASS]";
		else if (influence == PAR_KICK_DOWN_FIELD)
			title = "[ID_KICKDOWNFIELD]";
		else if(influence == PAR_KICK_FOR_TOUCH)
			title = "[ID_KICKINTOTOUCH]";

		// ERICTOTEST GG WW AFL, the following statement is not indented correctly which causes c++ compile error  -Wmisleading-indentation
		//if(!game->GetHUDUpdater()->IsShowingProRequestFeedback())
		m_pGame->GetHUDUpdater()->SetProRequestFeedbackText( title, "[ID_PRO_REQUEST_FAIL_DISTANCE]", false );
		return false;
	}
}

// When the pro player makes a request, they start a timer for how long they influence the BH
// Once that timer expires an event is fired off running this function
void RURoleStandardBallHolder::ResetProRequest()
{
	MABLOGDEBUG("Resetting the attacking influence of the player");
	current_pro_request = PAR_NONE;
}

#ifdef NEW_PRO_REQUEST_LOGIC
// Our aim for this function is to make the ball holder get the ball to the pro player as quickly as possible.
void RURoleStandardBallHolder::PerformProRequestPass()
{
	ARugbyCharacter* proPlayer = SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer();
	PASS_FAIL_REASON returned_reason;
	bool canPassDirectlyToPro = m_pGame->GetStrategyHelper()->CanCompleteProPassRequest( m_pPlayer,  proPlayer, returned_reason);

	bool wasSuccess = false;
	MabString title(0);
	MabString message(0);


	title = "[ID_REQUESTPASS]";

	// Restrictions were met, the ball holder can get the ball directly to the pro player
	if(canPassDirectlyToPro)
	{
		MABLOGDEBUG( "We can pass straight to our pro player" );
		message = "[ID_SUCCESS]";
		wasSuccess = true;
	}
	else
	{
		MABLOGDEBUG( "We can NOT pass straight to our pro player. Reason: " );
		wasSuccess = false;

		switch (returned_reason)
		{
		break;
			case PF_DIST_BY_CLOSING:
			case PF_DIST_BY_SPEED:
			case PF_OFFLOAD_DISTANCE:
			case PF_DISTANCE_GENERAL:
			case PF_X_DIST:
			case PF_Z_DIST:
			case PF_CLOSING_RATE:
			message += "[ID_PRO_REQUEST_FAIL_DISTANCE]";
		break;
			case PF_IN_FRONT:
			message += "[ID_PRO_REQUEST_FAIL_FORWARD]";
		break;
			case PF_CANT_INTERCEPT:
			message += "[ID_PRO_REQUEST_FAIL_CANT_INTERCEPT]";
		break;
			case PF_PASSER_ANGLE:
			case PF_RECEIVER_ANGLE:
			message += "[ID_PRO_REQUEST_FAIL_ANGLE]";
		break;
			case PF_OBSTRUCTED:
			message += "[ID_PRO_REQUEST_FAIL_OBSTRUCTED]";
		break;
			default:
			case PF_NO_ROLE:
			case PF_PASS_TO_SELF:
			case PF_NO_ARMS:
			message += "[ID_FAIL]";
		break;
		}
	}

	// If the message is already up, don't show it again. Should probably do a timer check to prevent spamming requests
	if(!m_pGame->GetHUDUpdater()->IsShowingProRequestFeedback())
		m_pGame->GetHUDUpdater()->SetProRequestFeedbackText( title, message, wasSuccess );


#if defined BUILD_DEBUG && defined DEBUG_RUN_PRO_REQ_WITH_DISABLED_AI && defined ENABLE_GAME_DEBUG_MENU
	// When we're testing the pro reg while Ai disabled, return here, otherwise it will run logic that wont work
	if( SIFDebug::GetGameDebugSettings()->GetAttackDisabled() && SIFDebug::GetGameDebugSettings()->GetDefenceDisabled())
		return;
#endif

	// We can pass it straight to the pro, go for it.
	if(canPassDirectlyToPro)
	{
		SSRoleOptionList role_options;
		SSGenericRoleOption *option = role_options.AddRoleOption(ROPT_BALLHOLDER_PASS_PLYR, 100, proPlayer);

		if(option)
			option->pass_to_player = proPlayer;

		if ( PerformStandardOptionsSelection( role_options ) )
		{
			ResetBaseAttackVariables();
			ResetAttackingOptionTimer( false );
		}
	}

	// Idealy we'd like to tell the ballholder to attempt to get the ball to the pro player some how.
	// Firstly we should probably set a flag, m_get_ball_to_pro, or something which will re route the decision making slightly to stop the BH from kicking etc
	// In the decision making we can probably check if there is another player close to the pro player and pass to them, plus setting the flag on them again to get the ball to the pro player.
	// We probably shouldn't flag the BH though if the pro is too far away,
	// i.e. if the pro standing back on his try line, and the BH is about to get a try we should just ignore the pro for being stupid
	else
	{
	}
}

void RURoleStandardBallHolder::PerformProRequestKickDownField()
{
	SSRoleOptionList role_options;

	bool wasSuccess = false;
	MabString title(0);
	MabString message(0);
	title = "[ID_KICKDOWNFIELD]";

	if(m_pGame->GetGameState()->GetPhase() != RUGamePhase::PLAY)
	{
		wasSuccess = false;
		message = "[ID_FAIL]";
	}
	else if(m_pActionManager->IsActionRunning( ACTION_TACKLEE ))
	{
		wasSuccess = false;
		message = "[ID_PRO_REQUEST_FAIL_GETTING_TACKLED]";
	}
	else if(m_pActionManager->IsActionRunning( ACTION_KICK ))
	{
		wasSuccess = false;
		message = "[ID_FAIL]";
	}
	else
	{
		wasSuccess = true;
		message = "[ID_SUCCESS]";
	}

#if defined BUILD_DEBUG && defined DEBUG_RUN_PRO_REQ_WITH_DISABLED_AI && defined ENABLE_GAME_DEBUG_MENU
	// When we're testing the pro reg while Ai disabled, return here, otherwise it will run logic that wont work
	if( SIFDebug::GetGameDebugSettings()->GetAttackDisabled() && SIFDebug::GetGameDebugSettings()->GetDefenceDisabled())
		return;
#endif

	if(wasSuccess)
	{
		// Work out what the best kind of kick would be
		AddKickDownFieldOption( role_options );

		// Do it!!!!
		if ( PerformStandardOptionsSelection( role_options ) )
		{
			ResetBaseAttackVariables();
			ResetAttackingOptionTimer( false );
		}
	}

	if(!m_pGame->GetHUDUpdater()->IsShowingProRequestFeedback())
		m_pGame->GetHUDUpdater()->SetProRequestFeedbackText( title, message, wasSuccess );
}

void RURoleStandardBallHolder::PerformProRequestKickForTouch()
{
	SSRoleOptionList role_options;

	bool wasSuccess = false;
	MabString title(0);
	MabString message(0);
	title = "[ID_KICKINTOTOUCH]";

	if(m_pGame->GetGameState()->GetPhase() != RUGamePhase::PLAY)
	{
		wasSuccess = false;
		message = "[ID_FAIL]";
	}
	else if(m_pActionManager->IsActionRunning( ACTION_KICK ))
	{
		wasSuccess = false;
		message = "[ID_FAIL]";
	}
	else if(m_pActionManager->IsActionRunning( ACTION_TACKLEE ))
	{
		wasSuccess = false;
		message = "[ID_PRO_REQUEST_FAIL_GETTING_TACKLED]";
	}
	else
	{
		wasSuccess = true;
		message = "[ID_SUCCESS]";
	}

#if defined BUILD_DEBUG && defined DEBUG_RUN_PRO_REQ_WITH_DISABLED_AI && defined ENABLE_GAME_DEBUG_MENU
	// When we're testing the pro reg while Ai disabled, return here, otherwise it will run logic that wont work
	if( SIFDebug::GetGameDebugSettings()->GetAttackDisabled() && SIFDebug::GetGameDebugSettings()->GetDefenceDisabled())
		return;
#endif

	if(wasSuccess)
	{
		// Work out where we should kick it out
		AddKickForTouchOption( role_options );

		// Do it!!!!
		if ( PerformStandardOptionsSelection( role_options ) )
		{
			ResetBaseAttackVariables();
			ResetAttackingOptionTimer( false );
		}
	}

	if(!m_pGame->GetHUDUpdater()->IsShowingProRequestFeedback())
		m_pGame->GetHUDUpdater()->SetProRequestFeedbackText( title, message, wasSuccess );
}
#endif
