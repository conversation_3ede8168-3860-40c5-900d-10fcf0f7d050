// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.


#include "WWUIPopulatorPlayerLink.h"
#include "WWUIPopulator.h"
#include "RugbyGameInstance.h"
#include "Databases/RUGameDatabaseManager.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RUDatabaseCaches.h"
#include "WidgetTree.h"
#include "Utility/Helpers/SIFGameHelpers.h"
#include "WWUITranslationManager.h"
#include "Image.h"
#include "WWUIListField.h"
#include "Match/HUD/RUHUDUpdater.h"

bool PlayerNameSort(RUDB_PLAYER& player1, RUDB_PLAYER& player2)
{
	int sort = strcmp(player1.GetLastName(), player2.GetLastName());
	if (sort == 0)
		sort = strcmp(player1.GetFirstName(), player2.GetFirstName());

	return sort < 0;
}

//===============================================================================
//===============================================================================

void UWWUIPopulatorPlayerLink::Populate(UWidget* widget)
{
	if (inPreConstruct)
	{
		preConstructOwner = widget;
	}

	Clear(widget);

	int32 NumItems = dataList.ArrayOption.Num();

	MabVector<RUDB_PLAYER> PlayerList(0);
	MabVector<bool> IsLinked(0);

	if (!inPreConstruct)
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		if (pRugbyGameInstance)
		{
			RUGameDatabaseManager* pDatabaseManager = pRugbyGameInstance->GetGameDatabaseManager();

			if (pDatabaseManager)
			{
				if (bUseCustomOnly)
				{
					/*MabVector<unsigned short> PlayerIDList2(0);
					if (!pDatabaseManager->LoadIdListConditional< RUDB_PLAYER >("height", 186, PlayerIDList2))
					{
						MABBREAKMSG("LoadIdListConditional failed based on the custom field!");
					}*/

					MabVector<unsigned short> PlayerIDList(0);
					if (!pDatabaseManager->LoadIdListConditional< RUDB_PLAYER >("custom", 1, PlayerIDList))
					{
						MABBREAKMSG("LoadIdListConditional failed based on the custom field!");
					}

					PlayerList.resize(PlayerIDList.size());
					pDatabaseManager->LoadAllData(PlayerIDList, PlayerList);
				}
				else
				{
					// load the team to get the lineup
					RUDB_TEAM team;
					pDatabaseManager->LoadData(team, TeamDatabaseID);

					// load the players
					PlayerList.resize(team.GetNumLineups());

					for (size_t i = 0; i < PlayerList.size(); ++i)
					{
						pDatabaseManager->LoadData(PlayerList[i], team.GetLineup((int)i).player_id);
					}
				}

				std::sort(PlayerList.begin(), PlayerList.end(), PlayerNameSort);

				//Store if a player is linked.
				for (unsigned int i = 0; i < PlayerList.size(); i++)
				{
					if (PlayerList.at(i).GetDbId() <= DB_LAST_PLAYER_ID)
					{
						IsLinked.push_back(true);
					}
					else
					{
						IsLinked.push_back(false);
					}
				}
			}
		}

		NumItems = PlayerList.size();
	}

	PlayerLinkDataFileCreationNodeCallback CallbackObject(widget, dataList.ArrayOption, PlayerList, IsLinked);

	CreateNodesFromTemplate(dataList.TemplateName, NumItems, &CallbackObject);

	if (ScreenRef)
	{
#ifdef UI_USING_UMG
		ScreenRef->StoreChildWidgets();
#else
		if (ScreenRef && ScreenRef->GetStateScreen())
		{
			ScreenRef->GetStateScreen()->StoreChildWidgets();
		}
#endif
	}
}

//===============================================================================
//===============================================================================

void UWWUIPopulatorPlayerLink::Refresh(UWidget* widget)
{
	// Do Nothing
}

//===============================================================================
//===============================================================================

void UWWUIPopulatorPlayerLink::PopulateAndRefresh(UWidget* widget)
{
	Populate(widget);
}

//===============================================================================
//===============================================================================

UWWUIPopulatorPlayerLink::PlayerLinkDataFileCreationNodeCallback::PlayerLinkDataFileCreationNodeCallback(UWidget* pContainerToPopulate, TArray<FWWUIScreenTemplateDataOption>& InDataOptions, const MabVector<RUDB_PLAYER>& InPlayerList, const MabVector<bool>& InIsLinked) :
	pContainer(),
	DataOptions(InDataOptions),
	CurrentWidgetIndex(0),
	PlayerList(InPlayerList),
	IsLinked(InIsLinked)
{
	pContainer = Cast<UPanelWidget>(pContainerToPopulate);

	if (!pContainer)
	{
		FString errorString = pContainerToPopulate != nullptr ? *pContainerToPopulate->GetPathName() : FString("NULL");
		//UE_LOG(LogWWUI, Error, TEXT("Cast to scroll box failed while attempting DataFileCreationNodeCallback on node %s"), *errorString);
	}
}

//===============================================================================
//===============================================================================

void UWWUIPopulatorPlayerLink::PlayerLinkDataFileCreationNodeCallback::Callback(UUserWidget* pWidget)
{
	UWWUIListField* pListField = Cast<UWWUIListField>(pWidget);

	UWidgetTree* pWidgetTree = pWidget->WidgetTree;

	if (pWidgetTree)
	{
		if (PlayerList.size() > (unsigned int)CurrentWidgetIndex)
		{
			MabString PlayerNameString = MabString(0, "%s, %s", PlayerList[CurrentWidgetIndex].GetLastName(), PlayerList[CurrentWidgetIndex].GetFirstName());

			RUHUDUpdater::CensorPlayerName(NULL, &PlayerList[CurrentWidgetIndex], PlayerNameString);
			
			UTextBlock* pNameText = Cast<UTextBlock>(pWidgetTree->FindWidget("TextName"));

			if (pNameText)
			{
				pNameText->SetText(SIFGameHelpers::GAConvertMabStringToFText(PlayerNameString));
			}

			//------------Pos
			PLAYER_POSITION position = PlayerList[CurrentWidgetIndex].GetPositionCategoryR13(0);
			MabString text_to_set = MabString(position <= PP_NUMBER_EIGHT_LOCK_FORWARD ? PlayerPositionEnum::GetPlayerPositionTextAbbreviated(position) : "- -");

			UTextBlock* pPositionText = Cast<UTextBlock>(pWidgetTree->FindWidget("TextPosition"));

			if (pPositionText)
			{
				pPositionText->SetText(UWWUITranslationManager::Translate(SIFGameHelpers::GAConvertMabStringToFText(text_to_set)));
			}

			//------------Second
			position = PlayerList[CurrentWidgetIndex].GetPositionCategoryR13(1);
			text_to_set = MabString(position <= PP_NUMBER_EIGHT_LOCK_FORWARD ? PlayerPositionEnum::GetPlayerPositionTextAbbreviated(position) : "- -");

			UTextBlock* pSecondPositionText = Cast<UTextBlock>(pWidgetTree->FindWidget("TextSecondPosition"));

			if (pSecondPositionText)
			{
				pSecondPositionText->SetText(UWWUITranslationManager::Translate(SIFGameHelpers::GAConvertMabStringToFText(text_to_set)));
			}

			//------------Age
			UTextBlock* pAgeText = Cast<UTextBlock>(pWidgetTree->FindWidget("TextAge"));

			if (pAgeText)
			{
				pAgeText->SetText(FText::FromString(FString::FromInt(PlayerList[CurrentWidgetIndex].GetAge())));
			}

			//------------Ovr
			UTextBlock* pRatingText = Cast<UTextBlock>(pWidgetTree->FindWidget("TextRating"));

			if (pRatingText)
			{
				pRatingText->SetText(FText::FromString(FString::FromInt(PlayerList[CurrentWidgetIndex].GetOverallRating())));
			}

			bool bIsLinked = IsLinked[CurrentWidgetIndex];

			UImage* pLinkedImage = Cast<UImage>(pWidgetTree->FindWidget("Linked"));

			if (pLinkedImage)
			{
				pLinkedImage->SetVisibility(bIsLinked ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
			}

			if (pListField)
			{
				int32 PlayerID = PlayerList[CurrentWidgetIndex].GetDbId();
				pListField->SetProperty("player_id", &PlayerID, PROPERTY_TYPE_INT);
				pListField->SetProperty("player_index", &CurrentWidgetIndex, PROPERTY_TYPE_INT);
				pListField->SetProperty("is_linked", &bIsLinked, PROPERTY_TYPE_BOOL);
			}
		}
	}

	pContainer->AddChild(pWidget);
	CurrentWidgetIndex++;
}
