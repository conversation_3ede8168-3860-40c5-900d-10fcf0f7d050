
#include "Rugby/Animation/RugbyAnimationStateMachine_FullBodyAction.h"
#include "Rugby/Animation/RugbyAnimationStateMachine.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Animation/RugbyAnimationRecords.h"
#include "Character/RugbyCharacterAnimInstance.h"
#include "Character/RugbyCharacter.h"
#include <Runtime/Engine/Classes/Animation/AnimInstance.h>
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuck.h"

//============================================================================================//============================================================================================
//============================================================================================//============================================================================================
//============================================================================================//============================================================================================

RugbyAnimationStateMachine_FullBodyAction::RugbyAnimationStateMachine_FullBodyAction(RugbyAnimationStateMachineMgr* pSMMgr, RUPlayerAnimation* pOwnerAnimation, ARugbyCharacter* pOwnerPlayer)
	: RugbyAnimationStateMachineBase(pSMMgr, pOwnerAnimation, pOwnerPlayer)
{
	InitialiseFullBodyActionStateMachine();	
}

//==============================================================================================================================================================

RugbyAnimationStateMachine_FullBodyAction::~RugbyAnimationStateMachine_FullBodyAction()
{

}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::SetFullBodyMontageInstance(const FRugbyAnimRecBase* pAnimRec, FAnimMontageInstance* pMontageInstance)
{
	m_FB_Montage.Reset();
	if (pAnimRec && pMontageInstance)
	{
		m_FB_Montage.FB_AnimRec = const_cast<FRugbyAnimRecBase*>(pAnimRec);
		m_FB_Montage.FB_MontageInstance = pMontageInstance;
		m_FB_Montage.FB_MontageUniqueID = pMontageInstance->GetInstanceID();
	}
}

//===============================================================================================================================================================
void RugbyAnimationStateMachine_FullBodyAction::InitialiseFullBodyActionStateMachine()
{
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::Null, this, &RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyNull, &RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyNull, &RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyNull);
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::Ruck, this, &RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyRuck);
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::Scrum, this, &RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyScrum);
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::Maul, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyMaul));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::Try, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyTry));

	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::ContactActions, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyContactActions));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::DummyHalf, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyDummyHalf));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::DynamicMoveNoBall, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyDynamicMoveNoBall));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::DynamicMoveWithBall, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyDynamicMoveWithBall));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::FeedBall, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyFeedBall));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::GetupCelebration, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyGetupCelebration));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::GetupNoBall, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyGetupNoBall));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::GetupWithBall, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyGetupWithBall));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::KickLeftFoot, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyKickLeftFoot));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::KickRightFoot, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyKickRightFoot));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::Lineout, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyLineout));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::NumberEightPickup, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyNumberEightPickup));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::QuickLineout, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyQuickLineout));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::RefRaiseFlag, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyRefRaiseFlag));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::Sidestep, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodySidestep));
	m_FB_StateMachine.AddState(ERugbyAnim_Mode_FullBodyActions::StayOnGround, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, FullBodyStayOnGround));

	m_FB_StateMachine.Initialise(ERugbyAnim_Mode_FullBodyActions::Null);


	// Initialise sub state machines.
	InitialiseKickOffStateMachine(); // Not actually used...
	InitialisePenaltyKickStateMachine();
	InitialiseDummyHalfStateMachine();
	InitialiseLineOutStateMachine();
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::InitialiseKickOffStateMachine() // Not actually used...
{
	m_kickoffStateMachine.AddState(EKickOffState::InvalidState, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, KickOffInvalid));
	m_kickoffStateMachine.AddState(EKickOffState::Place,		this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, KickOffPlace));
	m_kickoffStateMachine.AddState(EKickOffState::StepBack,		this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, KickOffStepBack));
	m_kickoffStateMachine.AddState(EKickOffState::Stand,		this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, KickOffStand));
	m_kickoffStateMachine.AddState(EKickOffState::StepLeft,		this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, KickOffStepLeft));
	m_kickoffStateMachine.AddState(EKickOffState::StepRight,	this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, KickOffStepRight));
	m_kickoffStateMachine.AddState(EKickOffState::Kick,			this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, KickOffKick));

	m_kickoffStateMachine.Initialise(EKickOffState::InvalidState);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::InitialisePenaltyKickStateMachine()
{
	m_penaltyKickStateMachine.AddState(EPenaltyKickState::InvalidState, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, PenaltyKickInvalid));
	m_penaltyKickStateMachine.AddState(EPenaltyKickState::Place,		this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, PenaltyKickPlace));
	m_penaltyKickStateMachine.AddState(EPenaltyKickState::StepBack,		this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, PenaltyKickStepBack));
	m_penaltyKickStateMachine.AddState(EPenaltyKickState::Idle,			this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, PenaltyKickIdle));
	m_penaltyKickStateMachine.AddState(EPenaltyKickState::PickGrass,	this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, PenaltyKickPickGrass));
	m_penaltyKickStateMachine.AddState(EPenaltyKickState::Kick,			this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, PenaltyKickKick));

	m_penaltyKickStateMachine.Initialise(EPenaltyKickState::InvalidState);
}

//Line Out statemachine has 2 substate machine.
//1) applicable to everyone in Lineout, based on their role. They could be lifter or jumper. That is the 'm_LineOutStateMachine' with enums 'ELineOutAnimationState' (FullBody for lifter, for jumper see step 2,3)
//2) applicable to jumpers only as they have their own substates. That is the 'm_LineOutJumperStateMachine' with enum 'ELineOutJumperAnimationState' (UpperBody)
//3) there is a 3rd substate machine for which the state is maintained in variable 'm_JumperFBState'. That basically tells whether the fullbody animation should be eUp, Down, or Idle. (FulllBody)
//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::InitialiseLineOutStateMachine()
{
	m_LineOutStateMachine.AddState(ELineOutAnimationState::InvalidState, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutInvalid));
	m_LineOutStateMachine.AddState(ELineOutAnimationState::JumperLeft, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutJumperLeft));
	m_LineOutStateMachine.AddState(ELineOutAnimationState::JumperRight, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutJumperRight));
	m_LineOutStateMachine.AddState(ELineOutAnimationState::BackLifterUpRight, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutBackLifterUpRight));
	m_LineOutStateMachine.AddState(ELineOutAnimationState::BackLifterDownRight, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutBackLifterDownRight));
	m_LineOutStateMachine.AddState(ELineOutAnimationState::FrontLifterUpRight, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutFrontLifterUpRight));
	m_LineOutStateMachine.AddState(ELineOutAnimationState::FrontLifterDownRight, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutFrontLifterDownRight));
	m_LineOutStateMachine.AddState(ELineOutAnimationState::BackLifterLeftUp, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutBackLifterLeftUp));
	m_LineOutStateMachine.AddState(ELineOutAnimationState::BackLifterLeftDown, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutBackLifterLeftDown));
	m_LineOutStateMachine.AddState(ELineOutAnimationState::FrontLifterLeftUp, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutFrontLifterLeftUp));
	m_LineOutStateMachine.AddState(ELineOutAnimationState::FrontLifterLeftDown, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutFrontLifterLeftDown));
	m_LineOutStateMachine.AddState(ELineOutAnimationState::Rotate180r, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutRotate180r));

	m_LineOutStateMachine.Initialise(ELineOutAnimationState::InvalidState);

	m_LineOutJumperStateMachine.AddState(ELineOutJumperAnimationState::InvalidState, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutJumperInvalid));
	m_LineOutJumperStateMachine.AddState(ELineOutJumperAnimationState::Initial, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutJumperInitial));
	m_LineOutJumperStateMachine.AddState(ELineOutJumperAnimationState::Miss, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutJumperMiss));
	m_LineOutJumperStateMachine.AddState(ELineOutJumperAnimationState::Catch, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutJumperCatch));
	m_LineOutJumperStateMachine.AddState(ELineOutJumperAnimationState::Slapdown, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutJumperSlapdown));
	m_LineOutJumperStateMachine.AddState(ELineOutJumperAnimationState::EmptyDown, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutJumperEmptyDown));
	m_LineOutJumperStateMachine.AddState(ELineOutJumperAnimationState::HoldDown, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutJumperHoldDown));
	m_LineOutJumperStateMachine.AddState(ELineOutJumperAnimationState::PassLeft, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutJumperPassLeft));
	m_LineOutJumperStateMachine.AddState(ELineOutJumperAnimationState::PassRight, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, LineOutJumperPassRight));

	m_LineOutJumperStateMachine.Initialise(ELineOutJumperAnimationState::InvalidState);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::InitialiseDummyHalfStateMachine()
{
	m_DummyHalfStateMachine.AddState(EDummyHalfAnimationState::InvalidState, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, DummyHalfInvalid));
	m_DummyHalfStateMachine.AddState(EDummyHalfAnimationState::Default, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, DummyHalfDefault));
	m_DummyHalfStateMachine.AddState(EDummyHalfAnimationState::Movement, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, DummyHalfMovement));
	m_DummyHalfStateMachine.AddState(EDummyHalfAnimationState::Pass, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_FullBodyAction, DummyHalfPass));

	m_DummyHalfStateMachine.Initialise(EDummyHalfAnimationState::InvalidState);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyNull()
{
	if (m_CurrentFBState != ERugbyAnim_Mode_FullBodyActions::Null)
	{
		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachineMgr::SetFullBodyActions called with NULL action Player '%s'"), *m_pPlayer->GetName());
	}

	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::Null;
	

	if (URugbyCharacterAnimInstance* pAnimInstance = m_pPlayer->GetAnimInstance())
	{
		if (pAnimInstance->IsDoingDummyHalfMovement())
		{
			pAnimInstance->SetMovementType(URugbyCharacterAnimInstance::MOVEMENT_TYPE::GENERAL, SMDefaultBlendOutTime);
		}
	}

	////stop if it's already not stopped
	//if ( m_pPlayer->GetAnimInstance() && m_FB_Montage.FB_MontageInstance )
	//{		
	//	for (auto &MontageInt : m_pPlayer->GetAnimInstance()->MontageInstances)
	//	{
	//		if (MontageInt && (m_FB_Montage.FB_MontageInstance == MontageInt) && m_FB_Montage.FB_MontageInstance->IsActive()) //stop the animation if it's still playing....
	//		{
	//			UE_LOG(LogTemp, Warning, TEXT("RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyNull Stop Full Body for: '%s'"), *m_pPlayer->GetName());
	//			m_FB_Montage.FB_MontageInstance->Stop(m_ForceStopBlendOutTime);
	//			break;
	//		}
	//	}
	//}	
	SetRemainingTransitionTime(m_ForceStopBlendOutTime);
	m_bJustEnteredNull = true;

	//m_FB_Montage.Reset();
	//m_ForceStopBlendOutTime = SMDefaultBlendOutTime;
	m_TypeName = wwDB_DTREQUESTTYPE_ENUM::MAX;
	m_SubNode = "";		
	m_LineOutSide = ELineOutSide::eNull;
	m_JumperFBState = EJumperFBState::eNull;
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyNull(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyNull);
	if (m_bJustEnteredNull)
	{
		////stop if it's already not stopped
		if (m_pPlayer->GetAnimInstance() && m_FB_Montage.FB_MontageInstance)
		{
			bool bMontageStoppedLocally = false;
			for (auto &MontageInt : m_pPlayer->GetAnimInstance()->MontageInstances)
			{
				if (MontageInt && (m_FB_Montage.FB_MontageInstance == MontageInt))
				{
					if (m_FB_Montage.FB_MontageInstance->IsActive()) //stop the animation if it's still playing....
					{
						UE_LOG(LogTemp, Warning, TEXT("RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyNull Stop Full Body for: '%s'"), *m_pPlayer->GetName());
						m_FB_Montage.FB_MontageInstance->Stop(m_ForceStopBlendOutTime, false);
						bMontageStoppedLocally = true;
					}
					break;
				}
			}
			if (!bMontageStoppedLocally)
			{
				// If something else has deliberately ended this animation then it should have its own idea of what the remaining transition time should be
				ensureAlways(m_FB_Montage.FB_MontageInstance);
				if (m_FB_Montage.FB_MontageInstance)
				{
					SetRemainingTransitionTime(m_FB_Montage.FB_MontageInstance->GetBlendTime());
				}
			}
		}

		m_FB_Montage.Reset();
		m_ForceStopBlendOutTime = SMDefaultBlendOutTime;
		m_bJustEnteredNull = false;
	}

	if ((m_pSMMgr->m_FBActions) && (*m_pSMMgr->m_FBActions == false)) //we need to make sure we dont call update if tackle is in progress
	{
		return;
	}

	// This state basically just checks if other states need to be activated.
	if ((m_pRUPlayerAnimation->GetEnterRuckStateVariable()->getValue()> 0.5f) &&
		(m_pSMMgr->GetSMTackle()->GetCurrentTackleMode() == ERugbyAnim_Mode_Tackles::null)) //make sure the tackler is not waiting for getup???? player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tackler_getup	
	{
		UE_LOG(LogTemp, Display, TEXT("OnUpdateFullBodyNull: Entering Ruck '%s'"), *m_pPlayer->GetName());
		m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::Ruck;
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Ruck); //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_ruck durationInTime: 0.5
	}
	/*
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_maul //durationInTime: 0.5
	else if (m_pRUPlayerAnimation->GetMaulStateVariable()->getValue() > 0.5f)
	{
		UE_LOG(LogTemp, Display, TEXT("OnUpdateFullBodyNull: Entering Maul '%s'"), *m_pPlayer->GetName());
		m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::Maul;
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Maul);
	}
	*/
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_try //durationInEvents: 0.5, Node_401_TransitionBlendEvents
	else if (m_pRUPlayerAnimation->GetTryActionVariable()->getValue() > 0.5f) 
	{
		m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::Try;
		UE_LOG(LogTemp, Display, TEXT("OnUpdateFullBodyNull: Entering try '%s'"), *m_pPlayer->GetName());
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Try);
	}

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_dummy_half durationInTime: 0.25
	else if (m_pRUPlayerAnimation->GetDummyHalfVariable()->getValue() > 0.5f)
	{		
		UE_LOG(LogTemp, Display, TEXT("OnUpdateFullBodyNull: Entering DummyHalf '%s'"), *m_pPlayer->GetName());		
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::DummyHalf);
	}
	
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_contact_actions durationInTime: 0.25
	//contact variable is set and used in RURoleGetTheBall.cpp
	else if (m_pRUPlayerAnimation->GetContactVariable()->getValue() > 0.5f )
	{
		UE_LOG(LogTemp, Display, TEXT("OnUpdateFullBodyNull: Entering Contact '%s'"), *m_pPlayer->GetName());
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::ContactActions);
	}	
	//
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_contact_actions1 = Not used since "miss_contact" request is not set
	//

	// Set play call animation disabled for the moment as it doesn't look good.
	// Can re-enable as part of RC4-5693 and look at changing this to use a blendN setup instead of a single animation.
// 	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::CALL_SET_PLAY))
// 	{
// 		const float MIN_DIST_FROM_BOUNDARY = 5.0f;
// 		wwDB_DTREQUESTTYPE_ENUM TempDummyHalfRequestType = wwDB_DTREQUESTTYPE_ENUM::CALL_SET_PLAY;
// 		FName TempSubNode = "both";
// 		FVector playerPos = m_pPlayer->GetMabPosition();
// 		// If close to the edge of the field then only yell towards the center of the field
// 		if (playerPos.x > ((FIELD_WIDTH * 0.5f) - MIN_DIST_FROM_BOUNDARY))
// 		{
// 			TempSubNode = "left";
// 		}
// 		else if (playerPos.x < -((FIELD_WIDTH * 0.5f) - MIN_DIST_FROM_BOUNDARY))
// 		{
// 			TempSubNode = "right";
// 		}
// 		else
// 		{
// 			// Base call direction on how many players are around, no point turning to call to nobody.
// 			int32 playersToLeft = 0, playersToRight = 0;
// 			RUTeam* pTeam = m_pPlayer->GetAttributes() ? m_pPlayer->GetAttributes()->GetTeam() : nullptr;
// 			if (pTeam)
// 			{
// 				for (ARugbyCharacter* pPlayer : pTeam->GetPlayers())
// 				{
// 					if (pPlayer == m_pPlayer || pPlayer->GetRole()->RTTGetType() == RURoleRuck::RTTGetStaticType())
// 					{
// 						continue;
// 					}
// 
// 					FVector otherPlayerPos = pPlayer->GetMabPosition();
// 					if (otherPlayerPos.x > playerPos.x)
// 					{
// 						playersToRight++;
// 					}
// 					else
// 					{
// 						playersToLeft++;
// 					}
// 				}
// 			}
// 
// 			// In case the previous null check failed somehow
// 			if (playersToLeft == playersToRight)
// 			{
// 				TempSubNode = "both";
// 			}
// 			// Only shout in a direction if there are actually players there
// 			else if (playersToLeft < 3)
// 			{
// 				TempSubNode = "right";
// 			}
// 			else if (playersToRight < 3)
// 			{
// 				TempSubNode = "left";
// 			}
// 			else
// 			{
// 				TempSubNode = "both";
// 			}
// 		}
// 
// 		m_TypeName = TempDummyHalfRequestType;
// 		m_SubNode = TempSubNode;
// 		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::DynamicMoveNoBall);
// 		m_pSMMgr->ResetRequestListValue(m_TypeName);
// 		//Select_FBAnimation(false, 1.0f, 0.5f, 0.5f);
// 		return;
// 	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyNull()
{
	m_FB_Montage.Reset();
	m_ForceStopBlendOutTime = SMDefaultBlendOutTime;
	m_bJustEnteredNull = false;
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyRuck(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyRuck);
	m_pSMMgr->GetSMRuckMaulScrum()->m_ruckStateMachine.Update(deltaSecs);

	//added this additional check for now to make sure we exit ruck.
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|ruck_Null durationInTime: 0.5
	if (m_pRUPlayerAnimation->GetEnterRuckStateVariable()->getValue() <= 0.5f && m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Ruck)
	{
		if (m_pSMMgr->GetSMRuckMaulScrum()->m_ruckStateMachine.GetCurrentStateKey() != RuckAnimationState::RuckContestTackleSuccess && 
			m_pSMMgr->GetSMRuckMaulScrum()->m_ruckStateMachine.GetCurrentStateKey() != RuckAnimationState::disengageRuckA && 
			m_pSMMgr->GetSMRuckMaulScrum()->m_ruckStateMachine.GetCurrentStateKey() != RuckAnimationState::disengageRuckB)
		{
			m_ForceStopBlendOutTime = 0.5f;
			m_pSMMgr->GetSMRuckMaulScrum()->SetRuckAnimationState(RuckAnimationState::InvalidState);
			m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
		}
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyScrum(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyScrum);
	m_pSMMgr->GetSMRuckMaulScrum()->m_scrumStateMachine.Update(deltaSecs);
}

//========================//=======================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyMaul()
{
	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s'"), *FString(__func__), *m_SubNode.ToString());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyMaul(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyMaul);
	m_pSMMgr->GetSMRuckMaulScrum()->m_maulStateMachine.Update(deltaSecs);

	//added this additional check for now to make sure we exit maul.
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|maul_Null durationInTime: 0.5
	if (m_pRUPlayerAnimation->GetMaulStateVariable()->getValue() <= 0.5f && m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Maul)
	{
		m_ForceStopBlendOutTime = 0.5f;
		m_pSMMgr->GetSMRuckMaulScrum()->SetMaulAnimationState(MaulAnimationState::InvalidState);
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyMaul()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	m_pSMMgr->GetSMRuckMaulScrum()->m_maulStateMachine.ChangeState(MaulAnimationState::InvalidState);
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyContactActions()
{
	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::ContactActions;
	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s'"), *FString(__func__), *m_SubNode.ToString());
	if (UOBJ_IS_VALID(m_pPlayer) && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
	{
		m_pPlayer->GetAnimInstance()->SetStateHeadLook(*ENUM_TO_FSTRING(ERugbyAnim_Mode_FullBodyActions, ERugbyAnim_Mode_FullBodyActions::ContactActions), EHeadLookFlags::HEAD_LOOK_NECK_MOVEMENT /*| HEAD_LOOK_SPINE_MOVEMENT*/, 0.5f);
	}

	//00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|fumbled_catches_null4
	//01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null1_catches
	SetRemainingTransitionTime(0.15f);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyContactActions(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyContactActions);
	//01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_Null durationInTime: 0.20000000298023224
	if (m_pRUPlayerAnimation->GetContactVariable()->getValue() <= 0.5f)
	{
		m_ForceStopBlendOutTime = 0.20000000298023224f;
		UE_LOG(LogTemp, Display, TEXT("OnUpdateFullBodyContactActions '%s'"), *m_pPlayer->GetName());
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyContactActions()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	if (UOBJ_IS_VALID(m_pPlayer) && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
	{
		m_pPlayer->GetAnimInstance()->ClearStateHeadLook(*ENUM_TO_FSTRING(ERugbyAnim_Mode_FullBodyActions, ERugbyAnim_Mode_FullBodyActions::ContactActions), 0.5f);
	}
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyDummyHalf()
{
	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::DummyHalf;		
	m_DummyHalfStateMachine.ChangeState(EDummyHalfAnimationState::Default);	

	// change movement type
	if (URugbyCharacterAnimInstance* pAnimInstance = m_pPlayer->GetAnimInstance())
	{
		pAnimInstance->SetMovementType(URugbyCharacterAnimInstance::MOVEMENT_TYPE::DUMMY_HALF, 0.25f);
	}
	
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyDummyHalf(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyDummyHalf);
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half_Null durationInTime: 0.25
	if (m_pRUPlayerAnimation->GetDummyHalfVariable()->getValue() <= 0.5f)
	{
		m_ForceStopBlendOutTime = 0.25f;
		UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
	}
	else
	{
		m_DummyHalfStateMachine.Update(deltaSecs);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyDummyHalf()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());	
	m_DummyHalfStateMachine.ChangeState(EDummyHalfAnimationState::InvalidState);
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyDynamicMoveNoBall()
{
	bool bHasCrossSyncEvent = true;
	float durationInEvents = 0.15000000596046448f; //blendInTime

	if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::SPRINT_TO_STAND_FORWARD)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_nb_dynamic_movement
	{
		bHasCrossSyncEvent = false;
	}

	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::DynamicMoveNoBall;

	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s'"), *FString(__func__), *m_SubNode.ToString());

	Select_FBAnimation(false, 1.0f, durationInEvents, 0.5f, bHasCrossSyncEvent);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyDynamicMoveNoBall(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyDynamicMoveNoBall);
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement_movement_passdown durationInTime: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement_movement_passdown1 durationInEvents: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement_movement_passdown10 durationInEvents: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement_movement_passdown13 durationInEvents: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement_movement_passdown14 durationInEvents: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement_movement_passdown15 durationInEvents: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement_movement_passdown16  durationInEvents: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement_movement_passdown17  durationInEvents: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement_movement_passdown18  durationInEvents: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement_movement_passdown19  durationInEvents: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement_movement_passdown20  durationInEvents: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement_movement_passdown21  durationInEvents: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement_movement_passdown22  durationInEvents: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement_movement_passdown9  durationInEvents: 0.5
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
	{
		m_ForceStopBlendOutTime = 0.5f;
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
		UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	}
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyDynamicMoveNoBall()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyDynamicMoveWithBall()
{
	bool bHasCrossSyncEvent = true;
	
	float durationInEvents = 0.15000000596046448f; //blendIn

	if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::SPRINT_TO_STAND_FORWARD)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement_movement_passdown
	{
		bHasCrossSyncEvent = false;
	}

	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::DynamicMoveWithBall;

	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s'"), *FString(__func__), *m_SubNode.ToString());

	Select_FBAnimation (false, 1.0f, durationInEvents, 0.5f, bHasCrossSyncEvent);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyDynamicMoveWithBall (const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyDynamicMoveWithBall);
	/*
	//durationInEvents: 0.5
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement_movement_passdown1
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement_movement_passdown10
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement_movement_passdown11
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement_movement_passdown12
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement_movement_passdown13
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement_movement_passdown2
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement_movement_passdown3
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement_movement_passdown4
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement_movement_passdown5
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement_movement_passdown6
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement_movement_passdown7
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement_movement_passdown8
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement_movement_passdown9
	*/
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
	{
		m_ForceStopBlendOutTime = 0.5f;
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
		UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyDynamicMoveWithBall()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyFeedBall()
{
	const float durationInTime = 0.15000000596046448f; //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_feed_ball

	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::FeedBall;

	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s'"), *FString(__func__), *m_SubNode.ToString());

	Select_FBAnimation(false, 1.0f, durationInTime);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyFeedBall(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyFeedBall);
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|feed_ball_Null durationInTime: 0.15000000596046448
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
	{
		m_ForceStopBlendOutTime = 0.15000000596046448f;
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
		UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyFeedBall()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyGetupCelebration()
{
	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s'"), *FString(__func__), *m_SubNode.ToString());
	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::GetupCelebration;

	m_FB_Montage.Reset();
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_getup_celebration
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_getup_celebration1
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|try_getup_celebration

	float durationInTime = 0.25f;

	FAnimMontageInstance* pMontageInstance = m_pSMMgr->PlayClosestAnimation(CLOSEST_ANIM_TYPE::GETUP_CELEBRATION_NOBALL, durationInTime );

	ensureAlways(pMontageInstance != nullptr);
	if (pMontageInstance != nullptr)
	{
		m_FB_Montage.FB_MontageInstance = pMontageInstance;
		m_FB_Montage.FB_MontageUniqueID = pMontageInstance->GetInstanceID();
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyGetupCelebration(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyGetupCelebration);
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|noball_getup1_Null durationInTime: 0.25
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
	{
		const UAnimSequence* animSeq = URugbyCharacterAnimInstance::GetAnimSequenceFromMontageInstance(m_FB_Montage.FB_MontageInstance);
		m_pPlayer->AnimationNotify(ERugbyAnimEvent::TRY_GETUP_EVENT, 0, animSeq, false); //fire an event as it has a valid startTrigger in the node.
		m_ForceStopBlendOutTime = 0.25f;
		UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);		
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyGetupCelebration()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyGetupNoBall()
{
	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s'"), *FString(__func__), *m_SubNode.ToString());
	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::GetupNoBall;

	m_FB_Montage.Reset();

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_noball_getup
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_withball_getup1
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|try_noball_getup1

	float durationInTime = 0.25f;

	FAnimMontageInstance* pMontageInstance = m_pSMMgr->PlayClosestAnimation(CLOSEST_ANIM_TYPE::GETUP_NOBALL, durationInTime );

	ensureAlways(pMontageInstance != nullptr);
	if (pMontageInstance != nullptr)
	{
		m_FB_Montage.FB_MontageInstance = pMontageInstance;
		m_FB_Montage.FB_MontageUniqueID = pMontageInstance->GetInstanceID();
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyGetupNoBall(const float deltaSecs)
{

}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyGetupNoBall()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyGetupWithBall()
{
	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s'"), *FString(__func__), *m_SubNode.ToString());
	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::GetupWithBall;

	m_FB_Montage.Reset();
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_withball_getup
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_withball_getup2
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|try_withball_getup

	float durationInTime = 0.25f;

	FAnimMontageInstance* pMontageInstance = m_pSMMgr->PlayClosestAnimation (CLOSEST_ANIM_TYPE::GETUP_WITHBALL, durationInTime );

	ensureAlways(pMontageInstance != nullptr);
	if (pMontageInstance != nullptr)
	{
		m_FB_Montage.FB_MontageInstance = pMontageInstance;
		m_FB_Montage.FB_MontageUniqueID = pMontageInstance->GetInstanceID();
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyGetupWithBall(const float deltaSecs)
{

}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyGetupWithBall()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyKickLeftFoot()
{
	float durationInTime = 0.25f;
	float AnimStartTime = 0.0f;
	float PlaySpeed = 1.0f;

	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::KickLeftFoot;

	if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::KICKOFF)//PLACED_KICK_OFF)
	{
		UE_LOG(LogTemp, Display, TEXT("%s->>> Beginning PLACED_KICK_OFF"), *FString(__func__));
		m_kickoffStateMachine.ChangeState(EKickOffState::Place);
	}
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick42
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot3 //durationInTime: 0.4000000059604645
	else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::PENALTY_KICK) 
	{
		UE_LOG(LogTemp, Display, TEXT("%s->>> Beginning PENALTY_KICK"), *FString(__func__));
		m_penaltyKickStateMachine.ChangeState(EPenaltyKickState::Place);
	}

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot10 //durationInTime: 0.0
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick8 durationInTime: 0.0
	else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::QUICK_PENALTY_KICK) 
	{
		UE_LOG(LogTemp, Display, TEXT("%s->>> Beginning QUICK_PENALTY_KICK"), *FString(__func__));
		m_penaltyKickStateMachine.ChangeState(EPenaltyKickState::Idle);
	}
	else
	{
		UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s'"), *FString(__func__), *m_SubNode.ToString());

		if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::LONG_PUNT_KICK )
		{
			if (m_SubNode == "standing") //this anim is too slow
			{
				PlaySpeed = 1.2f;//RC4-3946: Made the long punt kick animation bit fast.
			}

			durationInTime = 0.30000001192092896f;
		}
		else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::BOX_KICK)
		{
			if (m_SubNode == "") //Dummy half box kick or box kick no ball.
			{
				durationInTime = 0.2f;//default				
			}
			else
			{
				durationInTime = 0.25f;
			}
		}
		else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::CENTER_DROP_KICK || m_TypeName == wwDB_DTREQUESTTYPE_ENUM::GRUBBER_KICK)
		{
			durationInTime = 0.20000000298023224f;
		}
		else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::FOOTTAP || m_TypeName == wwDB_DTREQUESTTYPE_ENUM::DROP_KICK)// || m_TypeName == wwDB_DTREQUESTTYPE_ENUM::KICKOFF)
		{
			durationInTime = 0.4000000059604645f;
		}
		else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::CHIP_KICK)
		{
			durationInTime = 0.1899999976158142f;
		}
		
		Select_FBAnimation(false, PlaySpeed, durationInTime, SMDefaultBlendOutTime, false, AnimStartTime);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyKickLeftFoot(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyKickLeftFoot);
	if (m_kickoffStateMachine.GetCurrentStateKey() != EKickOffState::InvalidState)
	{
		m_kickoffStateMachine.Update(deltaSecs);
	}
	else if (m_penaltyKickStateMachine.GetCurrentStateKey() != EPenaltyKickState::InvalidState)
	{
		m_penaltyKickStateMachine.Update(deltaSecs);
	}
	else 
	{
		EMontageCrossDurationData MontageStoppedData;
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData)) //must be for center_drop_out
		{
			UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
			//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot_Null			
			m_ForceStopBlendOutTime = 0.20000000298023224f;
			m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
		}
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyKickLeftFoot()
{
	m_kickoffStateMachine.ChangeState(EKickOffState::InvalidState);
	m_penaltyKickStateMachine.ChangeState(EPenaltyKickState::InvalidState);
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyKickRightFoot()
{
	float durationInTime = 0.25f;
	float AnimStartTime = 0.0f;
	float PlaySpeed = 1.0f;

	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::KickRightFoot;

	if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::KICKOFF) //PLACED_KICK_OFF)
	{
		UE_LOG(LogTemp, Display, TEXT("%s->>> Beginning PLACED_KICK_OFF"), *FString(__func__));
		m_kickoffStateMachine.ChangeState(EKickOffState::Place);
	}
	else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::PENALTY_KICK)
	{
		UE_LOG(LogTemp, Display, TEXT("%s->>> Beginning PENALTY_KICK"), *FString(__func__));
		m_penaltyKickStateMachine.ChangeState(EPenaltyKickState::Place);
	}
	else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::QUICK_PENALTY_KICK)
	{
		UE_LOG(LogTemp, Display, TEXT("%s->>> Beginning QUICK_PENALTY_KICK"), *FString(__func__));
		m_penaltyKickStateMachine.ChangeState(EPenaltyKickState::Idle);
	}
	else
	{
		UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s'"), *FString(__func__), *m_SubNode.ToString());

		if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::LONG_PUNT_KICK )
		{
			if (m_SubNode == "standing") //this anim is too slow
			{
				PlaySpeed = 1.2f;//RC4-3946: Made the long punt kick animation bit fast.
			}			
			durationInTime = 0.30000001192092896f;
		}
		else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::BOX_KICK)
		{
			if (m_SubNode == "") //Dummy half box kick or box kick no ball.
			{
				durationInTime = 0.2f;				
			}
			else
			{
				durationInTime = 0.25f;
			}
		}
		else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::CENTER_DROP_KICK || m_TypeName == wwDB_DTREQUESTTYPE_ENUM::GRUBBER_KICK)
		{
			durationInTime = 0.20000000298023224f;
		}
		else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::FOOTTAP || m_TypeName == wwDB_DTREQUESTTYPE_ENUM::DROP_KICK) // || m_TypeName == wwDB_DTREQUESTTYPE_ENUM::KICKOFF)
		{
			durationInTime = 0.4000000059604645f;
		}
		else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::CHIP_KICK)
		{
			durationInTime = 0.1899999976158142f;
		}

		Select_FBAnimation(false, PlaySpeed, durationInTime, SMDefaultBlendOutTime, false, AnimStartTime);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyKickRightFoot(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyKickRightFoot);
	if (m_kickoffStateMachine.GetCurrentStateKey() != EKickOffState::InvalidState)
	{
		m_kickoffStateMachine.Update(deltaSecs);
	}
	else if (m_penaltyKickStateMachine.GetCurrentStateKey() != EPenaltyKickState::InvalidState)
	{
		m_penaltyKickStateMachine.Update(deltaSecs);
	}
	else 
	{
		EMontageCrossDurationData MontageStoppedData;
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData)) //must be for center_drop_out		
		{
			UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
			//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot_Null1
			m_ForceStopBlendOutTime = 0.20000000298023224f;
			m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
		}
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyKickRightFoot()
{
	m_kickoffStateMachine.ChangeState(EKickOffState::InvalidState);
	m_penaltyKickStateMachine.ChangeState(EPenaltyKickState::InvalidState);
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyLineout()
{
	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::Lineout;

	m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::InvalidState);

	switch (m_TypeName)
	{
		case(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_FRONT_LIFTER):
			{
				m_LineOutStateMachine.ChangeState(ELineOutAnimationState::FrontLifterUpRight);
				break;
			}
		case(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_BACK_LIFTER):
			{
				m_LineOutStateMachine.ChangeState(ELineOutAnimationState::BackLifterUpRight);
				break;
			}
		case(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_TURN180):
			{
				m_LineOutStateMachine.ChangeState(ELineOutAnimationState::Rotate180r);
				break;
			}
		case(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_BACK_LIFTER_LEFT):
			{
				m_LineOutStateMachine.ChangeState(ELineOutAnimationState::BackLifterLeftUp);
				break;
			}
		case(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_FRONT_LIFTER_LEFT):
			{
				m_LineOutStateMachine.ChangeState(ELineOutAnimationState::FrontLifterLeftUp);
				break;
		}
		case(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JUMPER):
			{
				m_LineOutStateMachine.ChangeState(ELineOutAnimationState::JumperRight);
				m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::Initial);
				break;
			}
		case(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JUMPER_LEFT):
			{
				m_LineOutStateMachine.ChangeState(ELineOutAnimationState::JumperLeft);
				m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::Initial);
				break;
			}

		default:
		{
			UE_DEBUG_BREAK();
			m_LineOutStateMachine.ChangeState(ELineOutAnimationState::InvalidState);
			break;
		}			

	}


/*
	if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::LINEOUT_FRONT_LIFTER)
	{
		m_LineOutStateMachine.ChangeState(ELineOutAnimationState::FrontLifterUpRight);
	}
	else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::LINEOUT_BACK_LIFTER)
	{
		m_LineOutStateMachine.ChangeState(ELineOutAnimationState::BackLifterUpRight);
	}
	else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::LINEOUT_TURN180)
	{
		m_LineOutStateMachine.ChangeState(ELineOutAnimationState::Rotate180r);
	}
	else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::LINEOUT_BACK_LIFTER_LEFT)
	{
		m_LineOutStateMachine.ChangeState(ELineOutAnimationState::BackLifterLeftUp);
	}
	else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::LINEOUT_FRONT_LIFTER_LEFT)
	{
		m_LineOutStateMachine.ChangeState(ELineOutAnimationState::FrontLifterLeftUp);
	}
	else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JUMPER)
	{
		m_LineOutStateMachine.ChangeState(ELineOutAnimationState::JumperRight);
		m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::Initial);		
	}
	else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JUMPER_LEFT)
	{
		m_LineOutStateMachine.ChangeState(ELineOutAnimationState::JumperLeft);
		m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::Initial);		
	}	
	else 
	{
		UE_DEBUG_BREAK();
		m_LineOutStateMachine.ChangeState(ELineOutAnimationState::InvalidState);
	}
	//*/
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyLineout(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyLineout);
	RUGameState* game_state = m_pPlayer->GetGameWorld()->GetGameState();
	
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_maul1 //durationInTime: 0.10000000149011612
	if (m_pRUPlayerAnimation->GetMaulStateVariable()->getValue() > 0.5f /*||  (game_state->GetPhase() != RUGamePhase::LINEOUT)*/)
	{				
		m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::InvalidState);
		m_LineOutStateMachine.ChangeState(ELineOutAnimationState::InvalidState);
		m_ForceStopBlendOutTime = 0.10000000149011612f;
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
	}
	else
	{
		m_LineOutStateMachine.Update(deltaSecs);
		m_LineOutJumperStateMachine.Update(deltaSecs);		
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyLineout()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyNumberEightPickup()
{
	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::NumberEightPickup;	
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|num_eight_pickup|PlaySpeedModifier1
	float PlaySpeedValue = 1.2999999523162842f; ////player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|num_eight_pickup|OperatorConst1
	
	//float durationInTime = 0.5f;//blend in //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_num_eight_pickup
	float durationInTime = 0.2f;

	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s' '%s'"), *FString(__func__), *m_SubNode.ToString(), *m_pPlayer->GetName());

	Select_FBAnimation(false, PlaySpeedValue, durationInTime);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyNumberEightPickup(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyNumberEightPickup);
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|num_eight_pickup_Null durationInTime: 0.15000000596046448
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
	{
		UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
		m_ForceStopBlendOutTime = 0.15000000596046448f;
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);		
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyNumberEightPickup()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyQuickLineout()
{
	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::QuickLineout;
	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s' '%s'"), *FString(__func__), *m_SubNode.ToString() , *m_pPlayer->GetName());
	float durationInTime = 0.5f;// blendin //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_quick_lineout
	Select_FBAnimation(false, 1.0f, durationInTime );
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyQuickLineout(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyQuickLineout);
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|quick_lineout_Null durationInTime: 0.5		
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
	{
		UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
		m_ForceStopBlendOutTime = 0.5f;
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);	
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyQuickLineout()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyRefRaiseFlag()
{
	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::RefRaiseFlag;
	
	float durationInTime = 0.25f; //blendin //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_ref_raise_flag

	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s' '%s'"), *FString(__func__), *m_SubNode.ToString(), *m_pPlayer->GetName());

	Select_FBAnimation(false, 1.0f, durationInTime);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyRefRaiseFlag(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyRefRaiseFlag);
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|ref_raise_flag_Null durationInTime: 0.25
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
	{
		UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
		m_ForceStopBlendOutTime = 0.25f;
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyRefRaiseFlag()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodySidestep()
{
	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::Sidestep;

	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s' '%s'"), *FString(__func__), *m_SubNode.ToString(), *m_pPlayer->GetName());

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_side_step
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_side_step1

	float durationInTime = 0.25f; //blendin 
	Select_FBAnimation(false, 1.0f, durationInTime, 1.0f);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodySidestep(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodySidestep);
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|side_step_mixed_movement1 durationInEvents: 1.0
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|side_step_mixed_movement durationInEvents: 1.0

	const float TestFraction = 0.3100000023841858f;

	bool result = false;
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData, TestFraction))
	{
		UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
		m_ForceStopBlendOutTime = 1.0f;
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);		
	}		
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodySidestep()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//========================//=====================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyStayOnGround()
{
	m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::StayOnGround;

	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s' '%s'"), *FString(__func__), *m_SubNode.ToString(), *m_pPlayer->GetName());

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_stay_on_ground
	float durationInTime = 0.25f;

	Select_FBAnimation(true, 1.0f, durationInTime );
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyStayOnGround(const float deltaSecs)
{

}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyStayOnGround()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}


//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyTry()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	m_pSMMgr->SetSelectTryAnimation(true);
}

//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnUpdateFullBodyTry(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateFullBodyTry);
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|try_Null1 durationInEvents: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|try_Null -  Note: This node is indirectly handled. When userdata == 300, we fire a try_getup event which indirectly sets GetTryActionVariable to zero
	if (m_pRUPlayerAnimation->GetTryActionVariable()->getValue() <= 0.5f) 
	{		
		m_ForceStopBlendOutTime = 0.5f;
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
	}
}


//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitFullBodyTry()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	m_pSMMgr->SetSelectTryAnimation(false);
}


//=============================================================================================================================================================
//=============================================================================================================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterKickOffInvalid()
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateKickOffInvalid(const float deltaSecs)
{

}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitKickOffInvalid()
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterKickOffPlace()
{
	m_SubNode = "kickoff_a_right_01";
	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s' '%s'"), *FString(__func__), *m_SubNode.ToString(), *m_pPlayer->GetName());
	Select_FBAnimation();
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateKickOffPlace(const float deltaSecs)
{	
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateKickOffPlace);
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
	{
		m_kickoffStateMachine.ChangeState(EKickOffState::StepBack);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitKickOffPlace()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterKickOffStepBack()
{
	m_SubNode = "kickoff_a_right_02";
	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s' '%s'"), *FString(__func__), *m_SubNode.ToString(), *m_pPlayer->GetName());
	Select_FBAnimation();
}

//===============================================================================
//===============================================================================
//Node_402_Transition: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|placed_kickoff|kickoff_a_right_02_step_right
//Node_402_Transition: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|placed_kickoff|kickoff_a_right_02_step_left

void RugbyAnimationStateMachine_FullBodyAction::OnUpdateKickOffStepBack(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateKickOffStepBack);
	const float TestFraction = 0.949999988079071f;

	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction ( m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData, TestFraction ))
	{
		UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());

		if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::STEP_LEFT))
		{
			m_kickoffStateMachine.ChangeState(EKickOffState::StepLeft);
			m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::STEP_LEFT);
		}
		else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::STEP_RIGHT))
		{
			m_kickoffStateMachine.ChangeState(EKickOffState::StepRight);
			m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::STEP_RIGHT);
		}
		else
		{
			m_kickoffStateMachine.ChangeState(EKickOffState::Stand);
		}		
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitKickOffStepBack()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterKickOffStand()
{
	m_SubNode = "standing_noball";
	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s' '%s'"), *FString(__func__), *m_SubNode.ToString(), *m_pPlayer->GetName());
	Select_FBAnimation();
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateKickOffStand(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateKickOffStand);
	check(m_pSMMgr);

	if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::STEP_LEFT))
	{
		m_kickoffStateMachine.ChangeState(EKickOffState::StepLeft);
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::STEP_LEFT);
	}
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::STEP_RIGHT))
	{
		m_kickoffStateMachine.ChangeState(EKickOffState::StepRight);
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::STEP_RIGHT);
	}
	else //if(m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::KICKOFF_KICK))
	{
		m_kickoffStateMachine.ChangeState(EKickOffState::Kick);
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::KICKOFF_KICK);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitKickOffStand()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterKickOffStepLeft()
{
	m_SubNode = "";
	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s' '%s'"), *FString(__func__), *m_SubNode.ToString(), *m_pPlayer->GetName());
	Select_FBAnimation();
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateKickOffStepLeft(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateKickOffStepLeft);
	if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::KICKOFF_KICK))
	{
		m_kickoffStateMachine.ChangeState(EKickOffState::Kick);
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::KICKOFF_KICK);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitKickOffStepLeft()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterKickOffStepRight()
{
	m_SubNode = "";
	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s' '%s'"), *FString(__func__), *m_SubNode.ToString(), *m_pPlayer->GetName());
	Select_FBAnimation();
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateKickOffStepRight(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateKickOffStepRight);
	if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::KICKOFF_KICK))
	{
		m_kickoffStateMachine.ChangeState(EKickOffState::Kick);
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::KICKOFF_KICK);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitKickOffStepRight()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}


//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterKickOffKick()
{
	m_SubNode = "kickoff_a_right_03";
	UE_LOG(LogTemp, Display, TEXT("%s->>> Subtype '%s' '%s'"), *FString(__func__), *m_SubNode.ToString(), *m_pPlayer->GetName());
	Select_FBAnimation();
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateKickOffKick(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateKickOffKick);
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData ))
	{
		m_kickoffStateMachine.ChangeState(EKickOffState::Stand);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitKickOffKick()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//==============================================================================================================================================================
//==============================================================================================================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnEnterPenaltyKickInvalid()
{
	//UE_LOG(LogTemp, Display, TEXT("%s %s"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdatePenaltyKickInvalid(const float deltaSecs)
{

}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitPenaltyKickInvalid()
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterPenaltyKickPlace()
{
	UE_LOG(LogTemp, Display, TEXT("%s %s"), *FString(__func__), *m_pPlayer->GetName());
	Select_FBAnimation();
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdatePenaltyKickPlace(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdatePenaltyKickPlace);
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
	{
		m_penaltyKickStateMachine.ChangeState(EPenaltyKickState::StepBack);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitPenaltyKickPlace()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterPenaltyKickStepBack()
{
	m_SubNode = "penalty_kick_right_02";
	UE_LOG(LogTemp, Display, TEXT("%s %s"), *FString(__func__), *m_pPlayer->GetName());
	Select_FBAnimation();
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdatePenaltyKickStepBack(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdatePenaltyKickStepBack);
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
	{
		m_penaltyKickStateMachine.ChangeState(EPenaltyKickState::Idle);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitPenaltyKickStepBack()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterPenaltyKickIdle()
{
	m_TypeName = wwDB_DTREQUESTTYPE_ENUM::QUICK_PENALTY_KICK;
	m_SubNode = "idle_right";
	UE_LOG(LogTemp, Display, TEXT("%s %s"), *FString(__func__), *m_pPlayer->GetName());
	Select_FBAnimation(true);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdatePenaltyKickIdle(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdatePenaltyKickIdle);
	if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::PICK_GRASS)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|penalty_kick|idle_right_pikc_grass_right durationInTime: 0.10000000149011612
	{
		m_penaltyKickStateMachine.ChangeState(EPenaltyKickState::PickGrass);
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::PICK_GRASS);
	}
	if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::PENALTY_KICK_KICK)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|penalty_kick|idle_right_penalty_kick_right_03 durationInTime: 0.10000000149011612
	{
		m_penaltyKickStateMachine.ChangeState(EPenaltyKickState::Kick);
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::PENALTY_KICK_KICK);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitPenaltyKickIdle()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterPenaltyKickPickGrass()
{
	m_TypeName = wwDB_DTREQUESTTYPE_ENUM::PICK_GRASS;
	m_SubNode = "pikc_grass_right";
	UE_LOG(LogTemp, Display, TEXT("%s %s"), *FString(__func__), *m_pPlayer->GetName());
	Select_FBAnimation();
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdatePenaltyKickPickGrass(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdatePenaltyKickPickGrass);
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData ))
	{
		m_penaltyKickStateMachine.ChangeState(EPenaltyKickState::Idle);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitPenaltyKickPickGrass()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterPenaltyKickKick()
{
	m_TypeName = wwDB_DTREQUESTTYPE_ENUM::PENALTY_KICK_KICK;
	m_SubNode = "penalty_kick_right_03";
	UE_LOG(LogTemp, Display, TEXT("%s %s"), *FString(__func__), *m_pPlayer->GetName());
	Select_FBAnimation();
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdatePenaltyKickKick(const float deltaSecs)
{
	// Not required, may add to prevent potential NMA
// 	if (!m_FB_Montage.FB_MontageInstance || m_FB_Montage.FB_MontageInstance->IsStopped())
// 	{
// 		m_penaltyKickStateMachine.ChangeState(EPenaltyKickState::InvalidState);
// 	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitPenaltyKickKick()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//==============================================================================================================================================================
//DUMMY HALF SUB STATEMACHINE
//==============================================================================================================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnEnterDummyHalfInvalid()
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateDummyHalfInvalid(const float deltaSecs)
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitDummyHalfInvalid()
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterDummyHalfDefault()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	const float durationInTime = 0.25f; //blendIn
	m_SubNode = "stand_to_crouch"; //defaultNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|stand_to_crouch|shenter01
	m_TypeName = wwDB_DTREQUESTTYPE_ENUM::Null;//this is because this node is not controlled by any request		
	Select_FBAnimation ( false, 1.0f, durationInTime ); //durationInTime: 0.25 player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_dummy_half

	//if (m_FB_Montage.FB_MontageInstance)
	//{
		//m_FB_Montage.FB_MontageInstance->bEnableAutoBlendOut = false;
	//}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateDummyHalfDefault(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateDummyHalfDefault);
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
	{
		UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|stand_to_crouch_movement durationInTime: 0.25
		m_DummyHalfStateMachine.ChangeState(EDummyHalfAnimationState::Movement);		
	}	
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitDummyHalfDefault()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterDummyHalfMovement()
{
	if (m_pRUPlayerAnimation && m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
	{
		if (m_pRUPlayerAnimation && m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
		{			
			if (URugbyCharacterAnimInstance* pAnimInstance = m_pPlayer->GetAnimInstance())
			{
				float durationInTime = 0.25f;
				// Stop any other playing animations at the same rate we want to blend this one in.
				pAnimInstance->StopAnimationGroup(MONTAGE_GROUP::ALL, durationInTime);

				// Change movment type
				//pAnimInstance->SetMovementType(URugbyCharacterAnimInstance::MOVEMENT_TYPE::DUMMY_HALF, durationInTime);
			}
		}	
		else
		{
			UE_DEBUG_BREAK();
			UE_LOG(LogTemp, Display, TEXT("%s Invalid pAnimInstance '%s'"), *FString(__func__), *m_pPlayer->GetName());
		}
	}
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateDummyHalfMovement(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateDummyHalfMovement);
	wwDB_DTREQUESTTYPE_ENUM TempDummyHalfRequestType = wwDB_DTREQUESTTYPE_ENUM::Null;
	FName TempSubNode = "";
	if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::GRAB))  //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_ball_state durationInTime: 0.25
	{
		TempDummyHalfRequestType = wwDB_DTREQUESTTYPE_ENUM::GRAB;		
	}
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::EXIT_DUMMY_HALF)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_exit	durationInTime: 0.25	
	{
		TempDummyHalfRequestType = wwDB_DTREQUESTTYPE_ENUM::EXIT_DUMMY_HALF;
	}
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::GRAB_DIVE_PASS_BACK_LEFT)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_left8 durationInTime: 0.25
	{
		TempDummyHalfRequestType = wwDB_DTREQUESTTYPE_ENUM::GRAB_DIVE_PASS_BACK_LEFT;
	}
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::GRAB_DIVE_PASS_BACK_RIGHT))//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_left3 durationInTime: 0.25
	{
		TempDummyHalfRequestType = wwDB_DTREQUESTTYPE_ENUM::GRAB_DIVE_PASS_BACK_RIGHT;
	}
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::GRAB_DIVE_PASS_BEHIND))	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_left5 durationInTime: 0.25
	{
		TempDummyHalfRequestType = wwDB_DTREQUESTTYPE_ENUM::GRAB_DIVE_PASS_BEHIND;
	}
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::GRAB_DIVE_PASS_LEFT))		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_left durationInTime: 0.25
	{
		TempDummyHalfRequestType = wwDB_DTREQUESTTYPE_ENUM::GRAB_DIVE_PASS_LEFT;
	}
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::GRAB_DIVE_PASS_RIGHT))		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_right durationInTime: 0.25
	{
		TempDummyHalfRequestType = wwDB_DTREQUESTTYPE_ENUM::GRAB_DIVE_PASS_RIGHT;
	}
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::GRAB_PASS_BACK_LEFT))		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_left7 durationInTime: 0.25
	{
		TempDummyHalfRequestType = wwDB_DTREQUESTTYPE_ENUM::GRAB_PASS_BACK_LEFT;
	}
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::GRAB_PASS_BACK_RIGHT))		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_left4 durationInTime: 0.25
	{
		TempDummyHalfRequestType = wwDB_DTREQUESTTYPE_ENUM::GRAB_PASS_BACK_RIGHT;
	}
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::GRAB_PASS_BEHIND))			//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_left6 durationInTime: 0.25
	{
		TempDummyHalfRequestType = wwDB_DTREQUESTTYPE_ENUM::GRAB_PASS_BEHIND;
	}
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::GRAB_PASS_LEFT))			//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_pass_left durationInTime: 0.25
	{
		TempDummyHalfRequestType = wwDB_DTREQUESTTYPE_ENUM::GRAB_PASS_LEFT;
	}
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::GRAB_PASS_RIGHT))			//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_pass_right	 durationInTime: 0.25		
	{
		TempDummyHalfRequestType = wwDB_DTREQUESTTYPE_ENUM::GRAB_PASS_RIGHT;
	}
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::GRAB_RUN))
	{
		TempDummyHalfRequestType = wwDB_DTREQUESTTYPE_ENUM::GRAB_RUN;
		{
			if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetMovementDirectionVariable()->getValue(), -70.0f, 0.0f)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_and_run_left_and_front durationInTime: 0.25
			{
				TempSubNode = "grab_and_run_left_and_front";
			}
			else if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetMovementDirectionVariable()->getValue(), 0.0f, 70.0f)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_and_run_right_and_front  durationInTime: 0.25
			{
				TempSubNode = "grab_and_run_right_and_front";
			}
			else if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetMovementDirectionVariable()->getValue(), 70.0f, 135.0f)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_and_run_right durationInTime: 0.25
			{
				TempSubNode = "grab_and_run_right";
			}
			else if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetMovementDirectionVariable()->getValue(), -135.0f, -70.0f)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_and_run_left durationInTime: 0.25
			{
				TempSubNode = "grab_and_run_left";
			}
			else// Inverted check for between -135 and 135 meaning anything outside that range. //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_and_run_back durationInTime: 0.25			
			{
				TempSubNode = "grab_and_run_back";
			}			
		}
	}

	if (TempDummyHalfRequestType!= wwDB_DTREQUESTTYPE_ENUM::Null)
	{
		UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
		m_TypeName = TempDummyHalfRequestType;
		m_SubNode = TempSubNode;
		m_pSMMgr->ResetRequestListValue(m_TypeName);
		m_DummyHalfStateMachine.ChangeState(EDummyHalfAnimationState::Pass);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitDummyHalfMovement()
{
// 	if (URugbyCharacterAnimInstance* pAnimInstance = m_pPlayer->GetAnimInstance())
// 	{
// 		pAnimInstance->SetMovementType(URugbyCharacterAnimInstance::MOVEMENT_TYPE::GENERAL, SMDefaultBlendInTime);
// 	}

	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterDummyHalfPass()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	
	float durationInTime = 0.25f;

	Select_FBAnimation(false, 1.0f, durationInTime);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateDummyHalfPass(const float deltaSecs)
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitDummyHalfPass()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}


//==============================================================================================================================================================
//LINEOUT SUB STATEMACHINE
//==============================================================================================================================================================


void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutInvalid()
{
	m_JumperFBState = EJumperFBState::eNull;
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutInvalid(const float deltaSecs)
{

}

//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutInvalid()
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutJumperLeft()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutJumperLeft(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateLineOutJumperLeft);
	//02: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|full_body|jumper_idle_jumper_down durationInTime: 0.5
	//destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|full_body|jumper_down|lojmprdown01		

	//00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|full_body|jumper_up_jumper_down durationInTime: 0.0
	//destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|full_body|jumper_down|lojmprdown01		
	if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JMPDOWN))  
	{
		if (m_JumperFBState != EJumperFBState::eDown) //make sure we are not already going down and this is just another duplicate request
		{
			UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
			m_TypeName = wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JMPDOWN;
			m_SubNode = "left"; //fullbody						
			
			float durationInTime = 0.0f; //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|full_body|jumper_up_jumper_down

			if (m_JumperFBState == EJumperFBState::eIdle)
			{
				durationInTime = 0.5f;//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|full_body|jumper_idle_jumper_down
			}

			Select_FBAnimation(false, 1.0f, durationInTime);

			m_JumperFBState = EJumperFBState::eDown;
		}
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JMPDOWN);
	}

	//01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|full_body|jumper_up_jumper_idle	 durationInTime: 0.5
	//destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|full_body|jumper_idle|lojmpridle01
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JMPIDLE)/* || 
		((m_LineOutJumperStateMachine.GetCurrentStateKey() == ELineOutJumperAnimationState::Initial) && (m_JumperFBState == EJumperFBState::eUp) && (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(FB_LineOut_UPMontageInstance, m_pPlayer->GetAnimInstance())))*/)
	{
		if (m_JumperFBState != EJumperFBState::eIdle)  //make sure we are not already in Idle and this is just another duplicate request
		{
			UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());

			m_TypeName = wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JMPIDLE;
			m_SubNode = "left"; //fullbody									
			float durationInTime = 0.5f;
			Select_FBAnimation(true, 1.0f, durationInTime);

			m_JumperFBState = EJumperFBState::eIdle;
		}
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JMPIDLE);
	}
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutJumperLeft()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutJumperRight()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutJumperRight(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateLineOutJumperRight);
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body|jumper_up_jumper_up_idle durationInTime: 0.0
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body|jumper_idle_jumper_down durationInTime: 0.5
	//destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body|jumper_down|lojmprdown01
	if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JMPDOWN))
	{
		if (m_JumperFBState != EJumperFBState::eDown) //make sure we are not already going down and this is just another duplicate request
		{
			m_TypeName = wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JMPDOWN;				
			m_SubNode = "right"; //fullbody			
			UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());

			float durationInTime = 0.0f; 

			if (m_JumperFBState == EJumperFBState::eIdle)
			{
				durationInTime = 0.5f;
			}

			Select_FBAnimation(false, 1.0f, durationInTime);

			m_JumperFBState = EJumperFBState::eDown;
		}
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JMPDOWN);
	}

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body|jumper_up_jumper_idle durationInTime: 0.5
	//destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body|jumper_idle|lojmpridle01
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JMPIDLE)) 
	{
		if (m_JumperFBState != EJumperFBState::eIdle)  //make sure we are not already in Idle and this is just another duplicate request
		{
			m_TypeName = wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JMPIDLE;					
			m_SubNode = "right"; //fullbody			
			UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
			
			float durationInTime = 0.5f;
			Select_FBAnimation(true, 1.0f, durationInTime);

			m_JumperFBState = EJumperFBState::eIdle;
		}
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JMPIDLE);
	}
}

//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutJumperRight()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutBackLifterUpRight() //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_line_out7 durationInTime: 0.25
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	m_SubNode = "default";
	m_LineOutSide = ELineOutSide::eNull;
	float durationInTime = 0.25f;
	Select_FBAnimation(false, 1.0f, durationInTime);
}

//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutBackLifterUpRight(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateLineOutBackLifterUpRight);
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData )) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|back_lifter_up_right_back_lifter_down_right durationInTime: 0.0
	{
		m_LineOutStateMachine.ChangeState(ELineOutAnimationState::BackLifterDownRight);
	}
}

//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutBackLifterUpRight()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutBackLifterDownRight() //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|back_lifter_down_right|lolifbdown01
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	m_TypeName = wwDB_DTREQUESTTYPE_ENUM::LINEOUT_BACK_LIFTER;
	m_SubNode = "";
	m_LineOutSide = ELineOutSide::eNull;
	float durationInTime = 0.0f; //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|back_lifter_up_right_back_lifter_down_right
	Select_FBAnimation(false, 1.0f, durationInTime);	
}

//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutBackLifterDownRight(const float deltaSecs)
{

}

//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutBackLifterDownRight()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutFrontLifterUpRight() //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|front_lifter_up_right|loliffup01 durationInTime: 0.25
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	m_SubNode = "default";
	m_LineOutSide = ELineOutSide::eNull;
	float durationInTime = 0.25f;
	Select_FBAnimation(false, 1.0f, durationInTime);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutFrontLifterUpRight(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateLineOutFrontLifterUpRight);
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|front_lifter_up_right_front_lifter_down_right durationInTime: 0.0
	{
		m_LineOutStateMachine.ChangeState(ELineOutAnimationState::FrontLifterDownRight); //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|front_lifter_down_right|loliffdown01
	}
}

//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutFrontLifterUpRight()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutFrontLifterDownRight() //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|front_lifter_down_right|loliffdown01
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	m_SubNode = "";
	m_LineOutSide = ELineOutSide::eNull;
	float durationInTime = 0.0f;  //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|front_lifter_up_right_front_lifter_down_right
	Select_FBAnimation(false, 1.0f, durationInTime);	
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutFrontLifterDownRight(const float deltaSecs)
{

}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutFrontLifterDownRight()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutBackLifterLeftUp() //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|back_lifter_left|back_lifter_left|back_lifter_down_left|lolifbdown01
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	m_SubNode = "default";
	m_LineOutSide = ELineOutSide::eNull;
	float durationInTime = 0.25f;
	Select_FBAnimation(false, 1.0f, durationInTime);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutBackLifterLeftUp(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateLineOutBackLifterLeftUp);
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))  //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|back_lifter_left|back_lifter_left|back_lifter_up_left_back_lifter_down_left durationInTime: 0.0
	{
		m_LineOutStateMachine.ChangeState(ELineOutAnimationState::BackLifterLeftDown);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutBackLifterLeftUp()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}


//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutBackLifterLeftDown()
{
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|back_lifter_left|back_lifter_left|back_lifter_down_left|lolifbdown01
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	m_SubNode = "";
	m_LineOutSide = ELineOutSide::eNull;
	float durationInTime = 0.0f;
	Select_FBAnimation(false, 1.0f, durationInTime);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutBackLifterLeftDown(const float deltaSecs)
{

}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutBackLifterLeftDown()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutFrontLifterLeftUp() //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|front_lifter_left|front_lifter_left|front_lifter_up_left|loliffup01
{	
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	m_SubNode = "default";
	m_LineOutSide = ELineOutSide::eNull;
	float durationInTime = 0.25f;
	Select_FBAnimation(false, 1.0f, durationInTime);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutFrontLifterLeftUp(const float deltaSecs) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|front_lifter_left|front_lifter_left|front_lifter_up_left_front_lifter_down_left durationInTime: 0.0
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateLineOutFrontLifterLeftUp);
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|front_lifter_left|front_lifter_left|front_lifter_down_left|loliffdown01
	EMontageCrossDurationData MontageStoppedData;
	if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_FB_Montage.FB_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
	{		
		m_LineOutStateMachine.ChangeState(ELineOutAnimationState::FrontLifterLeftDown);
	}
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutFrontLifterLeftUp()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}


//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutFrontLifterLeftDown()
{
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|front_lifter_left|front_lifter_left|front_lifter_down_left|loliffdown01
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	m_SubNode = "";
	m_LineOutSide = ELineOutSide::eNull;
	float durationInTime = 0.0f;
	Select_FBAnimation(false, 1.0f, durationInTime);

}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutFrontLifterLeftDown(const float deltaSecs)
{

}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutFrontLifterLeftDown()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutRotate180r() //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_line_out durationInTime: 0.25
{	
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	m_SubNode = "";
	m_LineOutSide = ELineOutSide::eNull;
	float durationInTime = 0.25f;
	Select_FBAnimation(false, 1.0f, durationInTime);
}

//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutRotate180r(const float deltaSecs)
{

}

//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutRotate180r()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
//===============================================================================
/*
Statemachine for UpperBody of LineOutJumper
player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_line_out1
or
player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_line_out8 durationInTime: 0.25

sourceNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null|PassThrough1
destSubStateNodes: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body
or
destSubStateNodes: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|MirrorTransforms1

requestName: lineout_jumper or requestName: lineout_jumper_left
*/

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutJumperInvalid()
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());	
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutJumperInvalid(const float deltaSecs)
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutJumperInvalid()
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutJumperInitial()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperLeft)
	{
		m_SubNode = "left";
	}
	else if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperRight)
	{
		m_SubNode = "right";
	}
	else
	{
		m_SubNode = "left"; //use some default
		UE_DEBUG_BREAK();
	}

	float durationInTime = 0.10000000149011612f; //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|null_jumper_catch
	float blendoutTime = 0.0f;
	if (m_JumperFBState != EJumperFBState::eUp) //play UP as full body and then play catch upperbody
	{
		m_TypeName = wwDB_DTREQUESTTYPE_ENUM::Null;
		Select_FBAnimation(false, 1.0f, durationInTime, blendoutTime);		
		m_FB_Montage.FB_MontageInstance->bEnableAutoBlendOut = false;//This is required to hold the animation at the last position so that next upperbody animation like catch can play correctly.
		m_JumperFBState = EJumperFBState::eUp;
	}
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutJumperInitial(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateLineOutJumperInitial);
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|null_jumper_miss durationInTime: 0.0
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|null_jumper_miss durationInTime: 0.0
	//destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_miss|FeatherBlend2_1
	if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_MISS))  
	{			
		m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::Miss);	
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_MISS);
	}

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|null_jumper_catch durationInTime: 0.10000000149011612
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|null_jumper_catch durationInTime: 0.10000000149011612
	//destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_catch|FeatherBlend2_1
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_CATCH)) 
	{
		if (m_pRUPlayerAnimation->GetBallCatchInterceptTimeVariable()->getValue() <= 0.3403179943561554f)
		{
			m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::Catch);
		}
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_CATCH); //reset the value anyway.....
	}

	////player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|null_jumper_slapdown durationInTime: 0.0
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|null_jumper_slapdown durationInTime: 0.0
	//destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_slapdown|FeatherBlend2_1
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_SLAPDOWN)) 
	{		
		m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::Slapdown);		
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_SLAPDOWN);
	}

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_catch_jumper_empty_down durationInTime: 0.20000000298023224
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch_jumper_empty_down durationInTime: 0.20000000298023224	
	//destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_empty_down|FeatherBlend2_1
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_CATCH_QUICK_MISS)) 
	{	
		m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::EmptyDown);		
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_CATCH_QUICK_MISS);
	}
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutJumperInitial()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutJumperMiss()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());

	/*
	sourceTransitions
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|null_jumper_miss

	Node: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_miss|FeatherBlend2_1

	sourceNodeA: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body
	sourceNodeB: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_miss|lojmprmis01
	*/

	if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperLeft)
	{
		m_SubNode = "left";
	}
	else if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperRight)
	{
		m_SubNode = "right";
	}
	else
	{
		m_SubNode = "left"; //use some default
		UE_DEBUG_BREAK();
	}

	float durationInTime = 0.20000000298023224f; //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_catch_jumper_miss
	if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_MISS)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|null_jumper_miss
	{
		durationInTime = 0.0f;
	}

	m_TypeName = wwDB_DTREQUESTTYPE_ENUM::LINEOUT_MISS; //upperbody
	Select_FBAnimation(false, 1.0f, durationInTime );
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutJumperMiss(const float deltaSecs)
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutJumperMiss()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutJumperCatch()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	
	/*
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch|FeatherBlend2_1
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_catch|FeatherBlend2_1

	sourceNodeA: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body
	sourceNodeB: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch|catch
	*/

	if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperLeft)
	{
		m_SubNode = "left";
	}
	else if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperRight)
	{
		m_SubNode = "right";
	}
	else
	{
		m_SubNode = "left"; //use some default
		UE_DEBUG_BREAK();
	}

	float durationInTime = 0.10000000149011612f; //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|null_jumper_catch	

	m_TypeName = wwDB_DTREQUESTTYPE_ENUM::LINEOUT_CATCH; //upperbody	

	Select_FBAnimation(false, 1.0f, durationInTime);
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutJumperCatch(const float deltaSecs)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_OnUpdateLineOutJumperCatch);
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|null_jumper_slapdown1 durationInTime: 0.0
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|null_jumper_slapdown1 durationInTime: 0.0
	//destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_slapdown|FeatherBlend2_1
	if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_SLAPDOWN))
	{
		m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::Slapdown);
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_SLAPDOWN);
	}
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_CATCH_QUICK_MISS)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch_jumper_miss1
	{
		m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::EmptyDown);
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_CATCH_QUICK_MISS);
	}

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch_jumper_pass_right
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_hold_down_jumper_pass_right durationInTime: 0.30000001192092896
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_PASS_TOP_RIGHT)) 
	{
		m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::PassRight);
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_PASS_TOP_RIGHT);
	}
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch_jumper_pass_right
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_hold_down_jumper_pass_left durationInTime: 0.30000001192092896
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch_jumper_pass_left durationInTime: 0.30000001192092896
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_PASS_TOP_LEFT))
	{
		m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::PassLeft);
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_PASS_TOP_LEFT);
	}	

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_catch_jumper_miss durationInTime: 0.20000000298023224
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch_jumper_miss durationInTime: 0.20000000298023224
	//DestinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_miss|FeatherBlend2_1
	else if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_CATCH_MISS)) 
	{
		m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::Miss);
		m_pSMMgr->ResetRequestListValue(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_CATCH_MISS);
	}
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutJumperCatch()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutJumperSlapdown()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());	

	/*
	sourceTransitions
	00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|null_jumper_slapdown durationInTime: 0.0
	01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|null_jumper_slapdown1 durationInTime: 0.0

	Node: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_slapdown|FeatherBlend2_1
	*/
	
	//if (m_LineOutSide == ELineOutSide::eLeft)
	if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperLeft)
	{
		m_SubNode = "left";
	}
	else if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperRight)
	{
		m_SubNode = "right";
	}
	else
	{
		m_SubNode = "left"; //use some default
		UE_DEBUG_BREAK();
	}

	float durationInTime = 0.0f;	
	
	m_TypeName = wwDB_DTREQUESTTYPE_ENUM::LINEOUT_SLAPDOWN; //upperbody
	Select_FBAnimation(false, 1.0f, durationInTime);
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutJumperSlapdown(const float deltaSecs)
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutJumperSlapdown()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutJumperEmptyDown()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());		
	//Node: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_empty_down|FeatherBlend2_1
	
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_miss_jumper_empty_down 
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_slapdown_jumper_empty_down
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_pass_left_jumper_empty_down
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_pass_right_jumper_empty_down

	if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperLeft)
	{
		m_SubNode = "left";
	}
	else if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperRight)
	{
		m_SubNode = "right";
	}
	else
	{
		m_SubNode = "left"; //use some default
		UE_DEBUG_BREAK();
	}

	float durationInTime = 0.25f; //for the events

	if (m_pSMMgr->IsRequestActive(wwDB_DTREQUESTTYPE_ENUM::LINEOUT_CATCH_QUICK_MISS)) 
	{
		durationInTime = 0.20000000298023224f; //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_catch_jumper_miss1
	}

	m_TypeName = wwDB_DTREQUESTTYPE_ENUM::LINEOUT_CATCH_QUICK_MISS; //upperbody
	Select_FBAnimation(false, 1.0f, durationInTime);
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutJumperEmptyDown(const float deltaSecs)
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutJumperEmptyDown()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutJumperHoldDown()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());

	//Node: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_hold_down|FeatherBlend2_1	

	m_SubNode = "catch";

	if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperLeft)
	{
		m_TypeName = wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JUMPER_LEFT;
	}
	else if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperRight)
	{
		m_TypeName = wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JUMPER;
	}
	else
	{
		m_TypeName = wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JUMPER_LEFT;
		UE_DEBUG_BREAK();
	}

	float durationInTime = 0.25f; //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_catch_jumper_hold_down

	Select_FBAnimation(false, 1.0f, durationInTime); //upperbody
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutJumperHoldDown(const float deltaSecs)
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutJumperHoldDown()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutJumperPassLeft()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());	

	/*
	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_pass_left|FeatherBlend2_2
	*/

	if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperLeft)
	{
		m_SubNode = "left";
	}
	else if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperRight)
	{
		m_SubNode = "right";
	}
	else
	{
		m_SubNode = "left"; //use some default
		UE_DEBUG_BREAK();
	}

	float durationInTime = 0.30000001192092896f; //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_hold_down_jumper_pass_left

	m_TypeName = wwDB_DTREQUESTTYPE_ENUM::LINEOUT_PASS_TOP_LEFT; //upperbody
	Select_FBAnimation(false, 1.0f, durationInTime );
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutJumperPassLeft(const float deltaSecs)
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutJumperPassLeft()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnEnterLineOutJumperPassRight()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch_jumper_pass_right durationInTime: 0.30000001192092896
	//destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_pass_right|MirrorTransforms1

	if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperLeft)
	{
		m_SubNode = "left";
	}
	else if (m_LineOutStateMachine.GetCurrentStateKey() == ELineOutAnimationState::JumperRight)
	{
		m_SubNode = "right";
	}
	else
	{
		m_SubNode = "left"; //use some default
		UE_DEBUG_BREAK();
	}

	m_TypeName = wwDB_DTREQUESTTYPE_ENUM::LINEOUT_PASS_TOP_RIGHT; //upperbody

	float durationInTime = 0.30000001192092896f; //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_hold_down_jumper_pass_right

	Select_FBAnimation(false, 1.0f, durationInTime);
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnUpdateLineOutJumperPassRight(const float deltaSecs)
{
	//UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::OnExitLineOutJumperPassRight()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================//===============================================================================
//===============================================================================//===============================================================================

void RugbyAnimationStateMachine_FullBodyAction::StateMachine_FB_Update(float time)
{
	
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::StateMachine_Update(float delta)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction);

	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_InternalUpdate);
		m_FB_StateMachine.Update(delta);
	}
	UpdateRemainingTransitionTime(delta);

	//////////////////////////////////////////////////////////////////////////
	// Transition conditions that always need to be checked (breakout transitions)
	//////////////////////////////////////////////////////////////////////////

	/* #MLW -- Is the animation controller responsible for pushing mauls??
	//though not explicitly mentioned, it appears that the contested tackle goes directly to  state. So below logic implements that....
	if (m_pSMMgr->GetSMTackle()->GetCurrentTackleMode() == ERugbyAnim_Mode_Tackles::contested_tacklee || m_pSMMgr->GetSMTackle()->GetCurrentTackleMode() == ERugbyAnim_Mode_Tackles::contested_tackler)
	{
		//19: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee_null1 durationInEvents: 0.5
		//    player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee_null2 durationInEvents: 0.5
		//sourceNode: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle
		//destinationNode: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|null|PassThrough1
		if (m_pRUPlayerAnimation->GetMaulStateVariable()->getValue() > 0.5f) //this logic is added here because the tacklers directly goes into maul state, they never gets called from RURoleMaul...
		{
			QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_FullBodyAction_DirectToMaul);
			UE_LOG(LogTemp, Display, TEXT("FullBodyAction::StateMachine_Update, MaulState is 1, hence setting tackle mode to NULL for '%s'"), *m_pPlayer->GetName());
			m_pSMMgr->GetSMTackle()->m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
			UE_LOG(LogTemp, Display, TEXT("FullBodyAction::StateMachine_Update, MaulState is 1, hence going to maul state directly from contested tackle for '%s'"), *m_pPlayer->GetName());
			m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Maul); //Now transition to MAUL
			m_pSMMgr->GetSMRuckMaulScrum()->m_Ruck_Maul_ShouldPlayAnimationOnStateChange = true;
			m_pSMMgr->GetSMRuckMaulScrum()->SetMaulAnimationState(MaulAnimationState::InMaul);
			return;
		}
	}
	*/
	//when this node is inactive, make sure we are in null state
	if ((m_pSMMgr->m_FBActions) && (*m_pSMMgr->m_FBActions == false))
	{
		//While tackling make sure we are in null state of fullbody statemachine
		if (m_CurrentFBState != ERugbyAnim_Mode_FullBodyActions::Null)
		{
			m_ForceStopBlendOutTime = SMDefaultTackleBlendInTime;
			m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
			UE_LOG(LogTemp, Warning, TEXT("Tackling while fullbody is playing, so FullBody SM to null for '%s'"), *m_pPlayer->GetName());
		}
		m_IsNodeActive = false;
		return;
	}
	m_IsNodeActive = true;


	// Don't do any other checks while there is an active transition.
	if (IsTransitionActive())
	{
		return;
	}

	//////////////////////////////////////////////////////////////////////////
	// All other transition condition checks after this point.
	//////////////////////////////////////////////////////////////////////////
	for (auto& RequestListIndex : m_pSMMgr->m_AnimationRequestNameState)
	{
		if (RequestListIndex.Value == false)
			continue;

		FString RequestName(RequestListIndex.Key);

		wwDB_DTREQUESTTYPE_ENUM requestTypeEnum = NAME_TO_ENUM(wwDB_DTREQUESTTYPE_ENUM, TCHAR_TO_ANSI(*RequestName));		

		//UE_LOG(LogTemp, Display, TEXT("FullBodyAction::StateMachine_Update: Process request '%s' for player '%s' "), *ENUM_TO_FSTRING(wwDB_DTREQUESTTYPE_ENUM, requestTypeEnum), *m_pPlayer->GetName());

		switch (requestTypeEnum)
		{
			//84: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_kick_leftfoot durationInTime: 0.30000001192092896
			//35: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot6 durationInTime: 0.30000001192092896
			//83: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_kick_rightfoot  durationInTime: 0.30000001192092896
			//04: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick49 durationInTime: 0.30000001192092896
		case wwDB_DTREQUESTTYPE_ENUM::LONG_PUNT_KICK:
		{
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				if (m_pRUPlayerAnimation->GetSpeedVariable()->getValue() <= 0.5f)
				{
					m_SubNode = "standing";
				}
				else
				{
					m_SubNode = "running";
				}

				m_TypeName = requestTypeEnum;
				if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetHandednessVariable()->getValue(), -1.0f, 0.0f))
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickLeftFoot); //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|standing_long_punt_kick|MirrorTransforms1
				}
				else
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickRightFoot); //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|standing_long_punt_kick|wbslpk02
				}				
				
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();

			break;
		}

		//61: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half1_box_kick1 durationInTime: 0.25
		//40: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot11 durationInTime: 0.25
		//60: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half1_box_kick durationInTime: 0.25
		//14: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick10 durationInTime: 0.25
		case wwDB_DTREQUESTTYPE_ENUM::BOX_KICK:
		{
			if ((m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::DummyHalf) || (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null))
			{
				m_TypeName = requestTypeEnum;
				
				if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::DummyHalf)				
				{
					m_SubNode = "";
				}
				else
				{
					m_SubNode = "box_kick_from_hands";
				}

				if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetHandednessVariable()->getValue(), -1.0f, 0.0f))
				{
					//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|box_kick_from_ground
					//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|box_kick_from_hands|MirrorTransforms1
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickLeftFoot);
				}
				else
				{
					//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|box_kick_off_ground
					//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|box_kick_from_hands|wbsbxk01
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickRightFoot);
				}				
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;
		}

		//41: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot1	 durationInTime: 0.4000000059604645		
		//28: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot durationInTime: 0.4000000059604645
		case wwDB_DTREQUESTTYPE_ENUM::PLACED_KICK_OFF: //KICKOFF:
		{
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				m_TypeName = requestTypeEnum;
				m_SubNode = "";

				if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetHandednessVariable()->getValue(), -1.0f, 0.0f))
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickLeftFoot); //00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|standing_kickoff|MirrorTransforms1
				}
				else
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickRightFoot); //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|standing_kickoff|SwitchWithEvents2
				}
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;
		}

		//39: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot10 durationInTime: 0.0
		//defaultNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|penalty_kick|idle_right|MirrorTransforms1			
		//13: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick8			
		case wwDB_DTREQUESTTYPE_ENUM::QUICK_PENALTY_KICK:
		{
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				m_TypeName = requestTypeEnum;
				m_SubNode = "idle_right";

				if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetHandednessVariable()->getValue(), -1.0f, 0.0f))
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickLeftFoot);
				}
				else
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickRightFoot);
				}
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;
		}

		//31: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot2 durationInTime: 0.4000000059604645
		//00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick41 durationInTime: 0.4000000059604645
		case wwDB_DTREQUESTTYPE_ENUM::FOOTTAP:
		{
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				m_TypeName = requestTypeEnum;
				m_SubNode = "";

				if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetHandednessVariable()->getValue(), -1.0f, 0.0f))
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickLeftFoot); //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|foot_tap
				}
				else
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickRightFoot); //layer_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|foot_tap
				}
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;
		}

		//32: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot3 durationInTime: 0.4000000059604645
		//01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick42 durationInTime: 0.4000000059604645
		case wwDB_DTREQUESTTYPE_ENUM::PENALTY_KICK:
		{
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				m_TypeName = requestTypeEnum;
				m_SubNode = "penalty_kick_right_01";
				m_bPenaltyKickLeftFoot = FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetHandednessVariable()->getValue(), -1.0f, 0.0f);
				if (m_bPenaltyKickLeftFoot)
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickLeftFoot);//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|penalty_kick|penalty_kick_right_01|MirrorTransforms1
				}
				else
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickRightFoot);
				}
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;
		}

		//33: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot4 durationInTime: 0.20000000298023224
		//02: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick44 durationInTime: 0.20000000298023224
		case wwDB_DTREQUESTTYPE_ENUM::GRUBBER_KICK:
		{
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				m_TypeName = requestTypeEnum;
				m_SubNode = "";
				if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetHandednessVariable()->getValue(), -1.0f, 0.0f))
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickLeftFoot);//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|grubber_kick
				}
				else
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickRightFoot); //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|grubber_kick
				}
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;
		}

		//34: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot5 durationInTime: 0.1899999976158142
		//03: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick46 durationInTime: 0.1899999976158142
		case wwDB_DTREQUESTTYPE_ENUM::CHIP_KICK:
		{
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				m_TypeName = requestTypeEnum;
				m_SubNode = "";
				if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetHandednessVariable()->getValue(), -1.0f, 0.0f))
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickLeftFoot);//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|chip_kick
				}
				else
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickRightFoot); //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|chip_kick
				}
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;
		}

		//36: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot7 durationInTime: 0.4000000059604645
		//05: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick50 durationInTime: 0.4000000059604645
		case wwDB_DTREQUESTTYPE_ENUM::DROP_KICK:
		{
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				m_TypeName = requestTypeEnum;
				if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetHandednessVariable()->getValue(), -1.0f, 0.0f))
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickLeftFoot);//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|drop_kick
				}
				else
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickRightFoot); //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|drop_kick
				}
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;
		}

		//37: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot8 durationInTime: 0.20000000298023224
		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick4 durationInTime: 0.20000000298023224
		case wwDB_DTREQUESTTYPE_ENUM::CENTER_DROP_KICK:
		{
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				m_TypeName = requestTypeEnum;
				if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetHandednessVariable()->getValue(), -1.0f, 0.0f))
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickLeftFoot);//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|center_drop_out
				}
				else
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickRightFoot); //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|center_drop_out
				}
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;
		}

		//38: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot9 durationInTime: 0.15000000596046448
		//...//defaultNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|placed_kickoff|kickoff_a_right_01|MirrorTransforms1

		//07: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick5 durationInTime: 0.15000000596046448
		//..//defaultNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|placed_kickoff|kickoff_a_right_01|wbko01
		case wwDB_DTREQUESTTYPE_ENUM::KICKOFF: //PENALTY_KICK_KICK: //PLACED_KICK_OFF:
		{
			//m_kickoffStateMachine.ChangeState(EKickOffState::Kick);
			//break;
			
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				m_bKickOffLeftFoot = FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetHandednessVariable()->getValue(), -1.0f, 0.0f);
				m_TypeName = requestTypeEnum;
				//m_SubNode = "kickoff_a_right_01";
				//m_SubNode = "penalty_kick_right_03";
				if (m_bKickOffLeftFoot)
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickLeftFoot);
				}
				else
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::KickRightFoot); //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|placed_kickoff
				}
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;
			
		}

		//below commented code moved to kick sub statemachine
		//38: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot9
		//runtimeChildTransitions:	//00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|placed_kickoff|kickoff_a_right_02_step_right
		//runtimeChildTransitions:	//01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|placed_kickoff|kickoff_a_right_02_step_left
		//runtimeChildTransitions:	//05: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|placed_kickoff|standing_noball_step_right
		//runtimeChildTransitions:	//06: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|placed_kickoff|standing_noball_step_left

		//05: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick5
		/*
		runtimeChildTransitions:
									00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|placed_kickoff|kickoff_a_right_02_step_right
									01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|placed_kickoff|kickoff_a_right_02_step_left
									01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|placed_kickoff|standing_noball_step_right
									06: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|placed_kickoff|standing_noball_step_left
		*/
		//
		//case wwDB_DTREQUESTTYPE_ENUM::STEP_RIGHT:
		//case wwDB_DTREQUESTTYPE_ENUM::STEP_LEFT:
		//	if (((m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickLeftFoot) || (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickRightFoot))
		//		&& (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::PLACED_KICK_OFF) &&
		//		((m_SubNode == "kickoff_a_right_02") || (m_SubNode == "standing_noball")))
		//	{
		//		m_SubNode = "";
		//		m_DurationFraction = 0.949999988079071f;
		//		m_TypeName = requestTypeEnum;
		//		m_FB_StateMachine.ReInitialise(m_CurrentFBState);
		//		m_pSMMgr->ResetRequestListValue(RequestName);
		//	}
		//	////else UE_DEBUG_BREAK();
		//	break;

		//38: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot9
		//runtimeChildTransitions:	//03: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|placed_kickoff|step_right_kickoff_a_right_03
		//runtimeChildTransitions:	//04: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|placed_kickoff|step_left_kickoff_a_right_03

		//05: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick5
		//runtimeChildTransitions:	//03: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|placed_kickoff|step_right_kickoff_a_right_03
		//runtimeChildTransitions:	//04: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|placed_kickoff|step_left_kickoff_a_right_03
		//case wwDB_DTREQUESTTYPE_ENUM::KICKOFF_KICK:
		//	if ((m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickLeftFoot) || (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickRightFoot))
		//	{
		//		if ((m_TypeName == wwDB_DTREQUESTTYPE_ENUM::STEP_RIGHT) || (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::STEP_LEFT))
		//		{
		//			m_SubNode = "";
		//			m_TypeName = requestTypeEnum;
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState);
		//			m_pSMMgr->ResetRequestListValue(RequestName);
		//		}
		//		//08: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|placed_kickoff|standing_noball_kickoff_a_right_03
		//		//08: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|placed_kickoff|standing_noball_kickoff_a_right_03
		//		else if (m_SubNode == "standing_noball")
		//		{
		//			m_TypeName = requestTypeEnum;
		//			m_SubNode = "kickoff_a_right_03";
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState);
		//			m_pSMMgr->ResetRequestListValue(RequestName);
		//		}
		//		////else UE_DEBUG_BREAK();
		//	}
				////else UE_DEBUG_BREAK();
		//	break;

		/*sourceTransitions: 39: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot10
									 32: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot3
									 01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick42
		*/
				//runtimeChildTransitions 01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|penalty_kick|idle_right_penalty_kick_right_03
				//							  player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|penalty_kick|idle_right_penalty_kick_right_03
		//case wwDB_DTREQUESTTYPE_ENUM::PENALTY_KICK_KICK:
		//{
		//	if (((m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickLeftFoot) ||
		//		(m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickRightFoot)))// &&
		//		//(m_TypeName == wwDB_DTREQUESTTYPE_ENUM::QUICK_PENALTY_KICK))
		//	{
		//		m_TypeName = requestTypeEnum;
		//		m_SubNode = "penalty_kick_right_03";
		//		m_FB_StateMachine.ReInitialise(m_CurrentFBState);
		//		m_pSMMgr->ResetRequestListValue(RequestName);
		//	}
		//		////else UE_DEBUG_BREAK();
		//	break;
		//}

		/*sourceTransitions:39: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot10
								32: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot3
								01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick42
		*/
		//runtimeChildTransitions	03: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|penalty_kick|idle_right_pikc_grass_right

		//							03: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|penalty_kick|idle_right_pikc_grass_right
		//case wwDB_DTREQUESTTYPE_ENUM::PICK_GRASS:
		//{
		//	if (((m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickLeftFoot) ||
		//		(m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickRightFoot)) &&
		//		(m_SubNode == "idle_right"))
		//	{
		//		m_TypeName = requestTypeEnum;
		//		m_SubNode = "pikc_grass_right";
		//		m_FB_StateMachine.ReInitialise(m_CurrentFBState);
		//		m_pSMMgr->ResetRequestListValue(RequestName);
		//	}
				////else UE_DEBUG_BREAK();
		//	break;
		//}

		//////////////////////////////////////////////
		/*
		durationInTime: 0.15000000596046448
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_nb_dynamic_movement
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_nb_dynamic_movement1 
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_nb_dynamic_movement10
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_nb_dynamic_movement13
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_nb_dynamic_movement14
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_nb_dynamic_movement15
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_nb_dynamic_movement16
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_nb_dynamic_movement17
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_nb_dynamic_movement18
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_nb_dynamic_movement19
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_nb_dynamic_movement20
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_nb_dynamic_movement21
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_nb_dynamic_movement22
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_nb_dynamic_movement9
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_wb_dynamic_movement
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_wb_dynamic_movement1
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_wb_dynamic_movement10
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_wb_dynamic_movement11
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_wb_dynamic_movement12
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_wb_dynamic_movement13
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_wb_dynamic_movement2
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_wb_dynamic_movement3
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_wb_dynamic_movement4
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_wb_dynamic_movement5
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_wb_dynamic_movement6
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_wb_dynamic_movement7
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_wb_dynamic_movement8
		player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|movement_passdown_wb_dynamic_movement9

		destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|wb_dynamic_movement
		destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement
		*/
		case wwDB_DTREQUESTTYPE_ENUM::QUICK_TURN_135L:
		case wwDB_DTREQUESTTYPE_ENUM::QUICK_TURN_135R:
		case wwDB_DTREQUESTTYPE_ENUM::QUICK_TURN_180R:
		case wwDB_DTREQUESTTYPE_ENUM::QUICK_TURN_90L:
		case wwDB_DTREQUESTTYPE_ENUM::QUICK_TURN_90R:
		case wwDB_DTREQUESTTYPE_ENUM::SPRINT_TO_STAND_FORWARD:
		case wwDB_DTREQUESTTYPE_ENUM::STAND_TO_SPRINT_135L:
		case wwDB_DTREQUESTTYPE_ENUM::STAND_TO_SPRINT_135R:
		case wwDB_DTREQUESTTYPE_ENUM::STAND_TO_SPRINT_180R:
		case wwDB_DTREQUESTTYPE_ENUM::STAND_TO_SPRINT_45L:
		case wwDB_DTREQUESTTYPE_ENUM::STAND_TO_SPRINT_45R:
		case wwDB_DTREQUESTTYPE_ENUM::STAND_TO_SPRINT_90L:
		case wwDB_DTREQUESTTYPE_ENUM::STAND_TO_SPRINT_90R:
		case wwDB_DTREQUESTTYPE_ENUM::STAND_TO_SPRINT_FORWARD:
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				m_TypeName = requestTypeEnum;
				if (m_pRUPlayerAnimation->GetWithBallStateVariable()->getValue() <= 0.5f)
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::DynamicMoveNoBall);
				}
				else
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::DynamicMoveWithBall);
				}
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;
			/*
			//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|side_step

			17: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_side_step durationInTime: 0.25
			18: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_side_step1 durationInTime: 0.25
			*/

		case wwDB_DTREQUESTTYPE_ENUM::SIDE_STEP_LEFT:
		case wwDB_DTREQUESTTYPE_ENUM::SIDE_STEP_RIGHT:
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				m_TypeName = requestTypeEnum;
				m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Sidestep);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;

			//81: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_ref_raise_flag durationInTime: 0.25
		case wwDB_DTREQUESTTYPE_ENUM::RAISE_FLAG:
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				m_TypeName = requestTypeEnum;
				m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::RefRaiseFlag);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;

			//73: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_feed_ball durationInTime: 0.15000000596046448
		case wwDB_DTREQUESTTYPE_ENUM::FEED_BALL:
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				m_TypeName = requestTypeEnum;
				m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::FeedBall);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;

			//76: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_stay_on_ground durationInTime: 0.25
		case wwDB_DTREQUESTTYPE_ENUM::STAY_ON_GROUND:
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::ContactActions)
			{
				m_TypeName = requestTypeEnum;
				m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::StayOnGround);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;
		case wwDB_DTREQUESTTYPE_ENUM::GRAB:						//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_ball_state
			/*if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::DummyHalf) //logic moved to dummyhalf statemachine
			{
				m_TypeName = requestTypeEnum;
				m_SubNode = "";
				m_DummyHalfState = EDummyHalfAnimationState::Pass;
				m_FB_StateMachine.ReInitialise(m_CurrentFBState);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			else*/ if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null) //must be player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_num_eight_pickup //durationInTime: 0.5
			{
				m_TypeName = requestTypeEnum;
				m_SubNode = "";
				m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::NumberEightPickup);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;

		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|Null_quick_lineout durationInTime: 0.5
		case wwDB_DTREQUESTTYPE_ENUM::QUICK_THROW_IN_SELF:
		{
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				m_TypeName = requestTypeEnum;
				m_SubNode = "";
				m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::QuickLineout);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
		}
		break;

		//logic moved to dummyhalf statemachine
		//
		//case wwDB_DTREQUESTTYPE_ENUM::EXIT_DUMMY_HALF:			//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_exit			
		//case wwDB_DTREQUESTTYPE_ENUM::GRAB_DIVE_PASS_BACK_LEFT:	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_left8
		//case wwDB_DTREQUESTTYPE_ENUM::GRAB_DIVE_PASS_BACK_RIGHT:	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_left3
		//case wwDB_DTREQUESTTYPE_ENUM::GRAB_DIVE_PASS_BEHIND:		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_left5
		//case wwDB_DTREQUESTTYPE_ENUM::GRAB_DIVE_PASS_LEFT:		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_left
		//case wwDB_DTREQUESTTYPE_ENUM::GRAB_DIVE_PASS_RIGHT:		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_right
		//case wwDB_DTREQUESTTYPE_ENUM::GRAB_PASS_BACK_LEFT:		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_left7
		//case wwDB_DTREQUESTTYPE_ENUM::GRAB_PASS_BACK_RIGHT:		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_left4
		//case wwDB_DTREQUESTTYPE_ENUM::GRAB_PASS_BEHIND:			//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_dive_pass_left6
		//case wwDB_DTREQUESTTYPE_ENUM::GRAB_PASS_LEFT:				//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_pass_left
		//case wwDB_DTREQUESTTYPE_ENUM::GRAB_PASS_RIGHT:			//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_pass_right			
		//	if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::DummyHalf)
		//	{
		//		m_TypeName = requestTypeEnum;
		//		m_SubNode = "";
		//		m_DummyHalfState = EDummyHalfAnimationState::Pass;
		//		m_FB_StateMachine.ReInitialise(m_CurrentFBState);
		//		m_pSMMgr->ResetRequestListValue(RequestName);
		//	}
		//	////else UE_DEBUG_BREAK();
		//	break;

		//case wwDB_DTREQUESTTYPE_ENUM::GRAB_RUN:
		//	if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::DummyHalf)
		//	{
		//		m_TypeName = requestTypeEnum;

		//		if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetMovementDirectionVariable()->getValue(), -70.0f, 0.0f)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_and_run_left_and_front
		//		{
		//			m_SubNode = "grab_and_run_left_and_front";
		//		}
		//		else if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetMovementDirectionVariable()->getValue(), 0.0f, 70.0f)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_and_run_right_and_front
		//		{
		//			m_SubNode = "grab_and_run_right_and_front";
		//		}
		//		else if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetMovementDirectionVariable()->getValue(), 70.0f, 135.0f)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_and_run_right
		//		{
		//			m_SubNode = "grab_and_run_right";
		//		}
		//		else if (FMath::IsWithinInclusive(m_pRUPlayerAnimation->GetMovementDirectionVariable()->getValue(), -135.0f, -70.0f)) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_and_run_left
		//		{
		//			m_SubNode = "grab_and_run_left";
		//		}
		//		else// Inverted check for between -135 and 135 meaning anything outside that range. //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half|movement_grab_and_run_back
		//		{
		//			m_SubNode = "grab_and_run_back";
		//		}
		//		m_DummyHalfState = EDummyHalfAnimationState::Pass;
		//		m_FB_StateMachine.ReInitialise(m_CurrentFBState);
		//		m_pSMMgr->ResetRequestListValue(RequestName);
		//		////else UE_DEBUG_BREAK();
		//	}
		//	////else UE_DEBUG_BREAK();
		//	break;		

		//case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_MISS: //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|null_jumper_miss
		//	if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Lineout)
		//	{
		//		if (m_LineOutJumperState == ELineOutJumperAnimationState::jumper_Initial)					
		//		{
		//			/*
		//			sourceTransitions					
		//			player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|null_jumper_miss

		//			Node: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_miss|FeatherBlend2_1

		//			sourceNodeA: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body
		//			sourceNodeB: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_miss|lojmprmis01
		//			*/
		//			m_LineOutJumperState = ELineOutJumperAnimationState::jumper_miss;
		//			if (m_LineOutSide == ELineOutSide::eLeft)
		//			{
		//				m_SubNode = "left"; //fullbody
		//			}
		//			else
		//			{
		//				m_SubNode = "right"; //fullbody
		//			}
		//			m_TypeName = wwDB_DTREQUESTTYPE_ENUM::Null;
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState); //play full body				

		//			m_TypeName = requestTypeEnum; //upperbody
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState); //play upperbody
		//			m_pSMMgr->ResetRequestListValue(RequestName);
		//		}
		//		////else UE_DEBUG_BREAK();
		//	}
		//	////else UE_DEBUG_BREAK();
		//	break;

		//case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_CATCH_MISS: //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch_jumper_miss
		//	if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Lineout)
		//	{
		//		if (m_LineOutJumperState == ELineOutJumperAnimationState::jumper_catch)
		//		{
		//			m_LineOutJumperState = ELineOutJumperAnimationState::jumper_miss;
		//			/*
		//			player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_miss|FeatherBlend2_1
		//			*/

		//			if (m_LineOutSide == ELineOutSide::eLeft)
		//			{
		//				m_SubNode = "left"; //fullbody
		//			}
		//			else
		//			{
		//				m_SubNode = "right"; //fullbody
		//			}
		//			m_TypeName = wwDB_DTREQUESTTYPE_ENUM::Null;
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState); //play full body		

		//			m_TypeName = requestTypeEnum; //upperbody
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState); //play upperbody
		//			m_pSMMgr->ResetRequestListValue(RequestName);
		//		}
		//		////else UE_DEBUG_BREAK();
		//	}
		//	////else UE_DEBUG_BREAK();
		//	break;

		//case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_CATCH:	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|null_jumper_catch			
		//	if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Lineout)
		//	{
		//		if (m_LineOutJumperState == ELineOutJumperAnimationState::jumper_Initial)
		//		{
		//			m_LineOutJumperState = ELineOutJumperAnimationState::jumper_catch;		//			
		//			/*
		//			inputParam: ControlParameters|intercept_time
		//			lessThanOperation: True
		//			orEqual: True
		//			testValue: 0.3403179943561554
		//			type: Condition_616_ControlParamTest
		//			*/
		//			/*
		//			player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch|FeatherBlend2_1

		//			sourceNodeA: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body
		//			sourceNodeB: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch|catch
		//			*/
		//			if (m_LineOutSide == ELineOutSide::eLeft)
		//			{
		//				m_SubNode = "left"; //fullbody
		//			}
		//			else
		//			{
		//				m_SubNode = "right"; //fullbody
		//			}
		//			m_TypeName = wwDB_DTREQUESTTYPE_ENUM::Null;
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState); //play full body				

		//			m_TypeName = requestTypeEnum; //upperbody
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState); //play upperbody
		//			m_pSMMgr->ResetRequestListValue(RequestName);
		//		}
		//		////else UE_DEBUG_BREAK();
		//	}
		//	////else UE_DEBUG_BREAK();
		//	break;

		//case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_SLAPDOWN:				
		//	if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Lineout)
		//	{
		//		if (m_LineOutJumperState == ELineOutJumperAnimationState::jumper_Initial || 
		//			m_LineOutJumperState == ELineOutJumperAnimationState::jumper_catch )
		//		{
		//			m_LineOutJumperState = ELineOutJumperAnimationState::jumper_slapdown;
		//			/*
		//			sourceTransitions
		//			00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|null_jumper_slapdown
		//			01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|null_jumper_slapdown1

		//			Node: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_slapdown|FeatherBlend2_1
		//			*/
		//			if (m_LineOutSide == ELineOutSide::eLeft)
		//			{
		//				m_SubNode = "left"; //fullbody
		//			}
		//			else
		//			{
		//				m_SubNode = "right"; //fullbody
		//			}
		//			m_TypeName = wwDB_DTREQUESTTYPE_ENUM::Null;
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState); //play full body					

		//			m_TypeName = requestTypeEnum; //upperbody
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState); //play upperbody
		//			m_pSMMgr->ResetRequestListValue(RequestName);
		//		}
		//		////else UE_DEBUG_BREAK();
		//	}
		//	////else UE_DEBUG_BREAK();
		//	break;

		//case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_PASS_TOP_LEFT:
		//	if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Lineout)
		//	{
		//		if (m_LineOutJumperState == ELineOutJumperAnimationState::jumper_catch)
		//		{
		//			m_LineOutJumperState = ELineOutJumperAnimationState::jumper_pass_left;

		//			/*
		//			player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_pass_left|FeatherBlend2_2
		//			*/
		//			if (m_LineOutSide == ELineOutSide::eLeft)
		//			{
		//				m_SubNode = "left"; //fullbody
		//			}
		//			else
		//			{
		//				m_SubNode = "right"; //fullbody
		//			}
		//			m_TypeName = wwDB_DTREQUESTTYPE_ENUM::Null;
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState); //play full body				

		//			m_TypeName = requestTypeEnum; //upperbody
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState); //play upperbody
		//			m_pSMMgr->ResetRequestListValue(RequestName);
		//		}
		//		////else UE_DEBUG_BREAK();
		//	}
		//	////else UE_DEBUG_BREAK();
		//	break;

		//case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_CATCH_QUICK_MISS:		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch_jumper_miss1		
		//	if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Lineout)
		//	{
		//		if (m_LineOutJumperState == ELineOutJumperAnimationState::jumper_catch || 
		//			m_LineOutJumperState == ELineOutJumperAnimationState::jumper_Initial )
		//		{
		//			m_LineOutJumperState = ELineOutJumperAnimationState::jumper_empty_down;
		//			/*

		//			sourceTransitions
		//			00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch_jumper_empty_down
		//			01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch_jumper_miss1			
		//			//other transitions are in event.
		//			Node: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_empty_down|FeatherBlend2_1
		//			*/

		//			if (m_LineOutSide == ELineOutSide::eLeft)
		//			{
		//				m_SubNode = "left"; //fullbody
		//			}
		//			else
		//			{
		//				m_SubNode = "right"; //fullbody
		//			}
		//			m_TypeName = wwDB_DTREQUESTTYPE_ENUM::Null;
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState); //play full body				

		//			m_TypeName = requestTypeEnum; //upperbody
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState); //play upperbody
		//			m_pSMMgr->ResetRequestListValue(RequestName);
		//		}
		//		////else UE_DEBUG_BREAK();
		//	}
		//	////else UE_DEBUG_BREAK();
		//	break;

		////player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body|jumper_up_jumper_up_idle
		////player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body|jumper_idle_jumper_down
		//case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JMPDOWN: 
		//{
		//	if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Lineout)
		//	{
		//		 check for sourceNode:	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body|jumper_up|lojmprup01
		//		//or							player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body|jumper_idle|lojmpridle01

		//		Also check useDestStartFraction
		//		m_TypeName = requestTypeEnum;
		//		if (m_LineOutSide == ELineOutSide::eLeft)
		//		{
		//			m_SubNode = "left"; //fullbody
		//		}
		//		else
		//		{
		//			m_SubNode = "right"; //fullbody
		//		}
		//		m_FB_StateMachine.ReInitialise(m_CurrentFBState); //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body|jumper_down|lojmprdown01
		//		m_pSMMgr->ResetRequestListValue(RequestName);
		//	}
		//	////else UE_DEBUG_BREAK();
		//}
		//break;

		//case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JMPIDLE:		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body|jumper_up_jumper_idle
		//{			
		//	if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Lineout)
		//	{
		//		m_TypeName = requestTypeEnum;					//loop animation.
		//		if (m_LineOutSide == ELineOutSide::eLeft)
		//		{
		//			m_SubNode = "left"; //fullbody
		//		}
		//		else
		//		{
		//			m_SubNode = "right"; //fullbody
		//		}
		//		m_FB_StateMachine.ReInitialise(m_CurrentFBState); //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|full_body|jumper_idle|lojmpridle01
		//		m_pSMMgr->ResetRequestListValue(RequestName);
		//	}
		//	////else UE_DEBUG_BREAK();
		//}
		//break;
		
		//case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_PASS_TOP_RIGHT:		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch_jumper_pass_right
		//	if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Lineout)
		//	{
		//		if (m_LineOutJumperState == ELineOutJumperAnimationState::jumper_catch)
		//		{
		//			m_LineOutJumperState = ELineOutJumperAnimationState::jumper_pass_right;
		//			if (m_LineOutSide == ELineOutSide::eLeft)
		//			{
		//				m_SubNode = "left"; //fullbody
		//			}
		//			else
		//			{
		//				m_SubNode = "right"; //fullbody
		//			}
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState); //play full body				
		//			m_TypeName = requestTypeEnum; //upperbody
		//			m_FB_StateMachine.ReInitialise(m_CurrentFBState); //play upperbody
		//			m_pSMMgr->ResetRequestListValue(RequestName);
		//		}
		//		////else UE_DEBUG_BREAK();
		//	}
		//	////else UE_DEBUG_BREAK();
		//break;	

		case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JUMPER://player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_line_out1 durationInTime: 0.25
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				//m_LineOutJumperState = ELineOutJumperAnimationState::jumper_Initial;
				m_LineOutSide = ELineOutSide::eRight;
				m_TypeName = requestTypeEnum;
				m_SubNode = "";
				m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Lineout);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
			break;

		case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_JUMPER_LEFT: //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_line_out8
		{
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				//m_LineOutJumperState = ELineOutJumperAnimationState::jumper_Initial;
				m_LineOutSide = ELineOutSide::eLeft;
				m_TypeName = requestTypeEnum;
				m_SubNode = "";
				m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Lineout);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
			////else UE_DEBUG_BREAK();
		}
		break;

		case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_BACK_LIFTER_LEFT://player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_line_out9		 durationInTime: 0.25
		case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_FRONT_LIFTER_LEFT: //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_line_out10	durationInTime: 0.25
		if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
		{
			//m_LineOutJumperState = ELineOutJumperAnimationState::jumper_Initial;
			m_TypeName = requestTypeEnum;			
			m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Lineout);
			m_pSMMgr->ResetRequestListValue(RequestName);
		}
		////else UE_DEBUG_BREAK();
		break;

		//............................................................
		//Node: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_line_out6 durationInTime: 0.25
		//destSubStateNodes: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|front_lifter_up_right|loliffup01
		case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_FRONT_LIFTER:

		//Node: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_line_out7 durationInTime: 0.25
		//destSubStateNodes: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|back_lifter_up_right|lolifbup01
		case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_BACK_LIFTER:

		//Node: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_line_out durationInTime: 0.25
		//destSubStateNodes: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|rotate180r|loturn180r01
		case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_TURN180:
		if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Null)
		{
			//m_LineOutJumperState = ELineOutJumperAnimationState::jumper_Initial;
			m_TypeName = requestTypeEnum;
			m_SubNode = "";
			m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Lineout);
			m_pSMMgr->ResetRequestListValue(RequestName);
		}
		////else UE_DEBUG_BREAK();
		break;

		//00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_getup_celebration durationInTime: 0.25
		//01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_getup_celebration1 durationInTime: 0.25
		case wwDB_DTREQUESTTYPE_ENUM::DIVE_TRY_CELEBRATE:
		{
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::StayOnGround ||
				m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::ContactActions)
			{
				m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::GetupCelebration);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		case wwDB_DTREQUESTTYPE_ENUM::DIVE_GETUP:
		{
			if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::StayOnGround ||
				m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::ContactActions)
			{
				//00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_withball_getup durationInTime: 0.25
				//01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_withball_getup2 durationInTime: 0.25
				if (m_pRUPlayerAnimation->GetWithBallStateVariable()->getValue() > 0.5f)
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::GetupWithBall);
				}

				//00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_noball_getup durationInTime: 0.25
				//01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_withball_getup1 durationInTime: 0.25
				else
				{
					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::GetupNoBall);
				}

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		default:
			break;
		}
	}

	//debug only
	if (m_CurrentFBState != m_DebugPrevFBState)
	{
		UE_LOG(LogTemp, Warning, TEXT("StateMachine_Update: FB stateChanged to '%d' from '%d' for: '%s'"), (int) m_CurrentFBState, (int) m_DebugPrevFBState, *m_pPlayer->GetName());
		m_DebugPrevFBState = m_CurrentFBState;
	}
}

//============================================================================================

void RugbyAnimationStateMachine_FullBodyAction::StateMachine_Reset()
{	
	if (m_pPlayer && m_pPlayer->GetAnimInstance())
	{
		m_pPlayer->GetAnimInstance()->StopAnimationGroup(MONTAGE_GROUP::FULL_BODY, 0.0f);
	}
	//UE_LOG(LogTemp, Display, TEXT("%s %s"), *FString(__func__), *m_pPlayer->GetName());		

	m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);

	m_kickoffStateMachine.ChangeState(EKickOffState::InvalidState);
	m_penaltyKickStateMachine.ChangeState(EPenaltyKickState::InvalidState);
	m_DummyHalfStateMachine.ChangeState(EDummyHalfAnimationState::InvalidState);
	m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::InvalidState);
	m_LineOutStateMachine.ChangeState(ELineOutAnimationState::InvalidState);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::StateMachine_ReturnToNullState()
{
	m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
	m_remainingStateTransitionTime = 0.0f;
}

//============================================================================================

void RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationEvent(float time, ERugbyAnimEvent event, size_t userdata)
{	
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_Null1 Type: Node_401_TransitionBlendEvents, eventUserTypeID: 15, durationInEvents: 0.25
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_Null2 Type: Node_401_TransitionBlendEvents, eventUserTypeID: 300, durationInEvents: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_Null3 Type: Node_401_TransitionBlendEvents, eventUserTypeID: 17, durationInEvents: 0.25
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|contact_actions_Null4 Type: Node_401_TransitionBlendEvents, eventUserTypeID: 13, durationInEvents: 0.25
	
	if  (((userdata == 15) || (userdata == 300) || (userdata == 17) || (userdata == 13)) && (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::ContactActions))
	{
		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationEvent ContactActions '%d' for '%s'"), (int)m_CurrentFBState, *m_pPlayer->GetName());

		if (userdata == 300)
		{
			m_ForceStopBlendOutTime = 0.5f;
		}
		else
		{
			m_ForceStopBlendOutTime = 0.25f;
		}

		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
	}

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half1_null - userdata: 57 durationInTime: 0.25
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half1_null1 - userdata: 58 durationInEvents: 0.25
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half1_null2 - userdata: 56 durationInEvents: 0.5
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half1_null3 - userdata: 57 durationInEvents: 0.5 ???? how can same event id be twice for same source & dest
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half1_null4 - userdata: 55 durationInEvents: 1
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half1_null5 - userdata: 54 durationInEvents: 1
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half1_null6 - userdata: 53 durationInEvents: 1
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half1_null7 - userdata: 52 durationInTime: 0.25
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half1_null8 - userdata: 51 durationInEvents: 1
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|dummy_half1_null9 - userdata: 50 durationInEvents: 1
	else if (((userdata >= 50) && (userdata <= 58)) && (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::DummyHalf))
	{
		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationEvent DummyHalf '%d' for '%s'"), (int)m_CurrentFBState, *m_pPlayer->GetName());

		if (userdata == 57 || userdata == 58 || userdata == 52)
		{
			m_ForceStopBlendOutTime = 0.25f;
		}
		else if (userdata == 56)
		{
			m_ForceStopBlendOutTime = 0.5f;
		}
		else
		{
			m_ForceStopBlendOutTime = 1.0f;
		}

		// prevent the animation state machine from re-entering dummy half state incorrectly
		if (m_pRUPlayerAnimation && m_pRUPlayerAnimation->GetDummyHalfVariable())
		{
			m_pRUPlayerAnimation->GetDummyHalfVariable()->setValue(0.0f);
		}
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
	}
	else if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Lineout)
	{
		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out_mixed_movement1 durationInTime: 0.4000000059604645
		if (userdata == 15)
		{
			UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationEvent ID '%d' Lineout '%d' for '%s'"), userdata, (int)m_CurrentFBState, *m_pPlayer->GetName());
			m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::InvalidState);
			m_LineOutStateMachine.ChangeState(ELineOutAnimationState::InvalidState);
			m_ForceStopBlendOutTime = 0.4000000059604645f;
			m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
		}
		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_catch_jumper_hold_down durationInTime: 0.25
		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_catch_jumper_hold_down durationInTime: 0.25
		//destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_hold_down|FeatherBlend2_1
		else if ((userdata == 13) && (m_LineOutJumperStateMachine.GetCurrentStateKey() == ELineOutJumperAnimationState::Catch))
		{	
			UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationEvent ID '%d' Lineout '%d' for '%s'"), userdata, (int)m_CurrentFBState, *m_pPlayer->GetName());
			m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::HoldDown);
		}
		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_miss_jumper_empty_down  durationInTime: 0.25
		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_slapdown_jumper_empty_down durationInTime: 0.25
		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_pass_left_jumper_empty_down durationInTime: 0.25
		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_right|upper_body|jumper_pass_right_jumper_empty_down durationInTime: 0.25
		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_miss_jumper_empty_down durationInTime: 0.25
		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_pass_left_jumper_empty_down durationInTime: 0.25
		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_pass_right_jumper_empty_down durationInTime: 0.25
		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_slapdown_jumper_empty_down  durationInTime: 0.25
		//destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|line_out|lineout_jumper_left|upper_body|jumper_empty_down|FeatherBlend2_1
		else if (((userdata == 13) && (((m_LineOutJumperStateMachine.GetCurrentStateKey() == ELineOutJumperAnimationState::Miss) || (m_LineOutJumperStateMachine.GetCurrentStateKey() == ELineOutJumperAnimationState::Slapdown))))
				||((userdata == 14) && ((m_LineOutJumperStateMachine.GetCurrentStateKey() == ELineOutJumperAnimationState::PassLeft) || (m_LineOutJumperStateMachine.GetCurrentStateKey() == ELineOutJumperAnimationState::PassRight)))) //
		{
			UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationEvent ID '%d' Lineout '%d' for '%s'"), userdata, (int)m_CurrentFBState, *m_pPlayer->GetName());

			m_LineOutJumperStateMachine.ChangeState(ELineOutJumperAnimationState::EmptyDown);		
		}
	}

	else if ((userdata == 14) && (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Try))
	{
		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationEvent Try '%d' for '%s'"), (int)m_CurrentFBState, *m_pPlayer->GetName());

		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|try_getup_celebration durationInTime: 0.25
		if (m_pRUPlayerAnimation->GetTryGetupCelebrateVariable()->getValue() > 0.5f)
		{
			m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::GetupCelebration);
		}
		else
		{
			//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|try_withball_getup durationInTime: 0.25
			if (m_pRUPlayerAnimation->GetWithBallStateVariable()->getValue() > 0.5f)
			{
				m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::GetupWithBall);
			}
			//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|try_noball_getup1 durationInTime: 0.25
			else
			{
				m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::GetupNoBall);
			}
		}
	}

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_mixed_movement35 durationInEvents: 0.25
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot1_mixed_movement  durationInEvents: 0.25	
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot1_mixed_movement1 durationInEvents: 0.25	
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot_mixed_movement durationInEvents: 0.25	
	else if ((userdata == 300 || userdata == 301) && (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickRightFoot || m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickLeftFoot))
	{
		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationEvent Kick '%d' for '%s'"), (int)m_CurrentFBState, *m_pPlayer->GetName());
		m_ForceStopBlendOutTime = 0.25f;
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
	}
}

//============================================================================================

void RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationTransitionNodeEvent(float time, ERugbyAnimEvent event, size_t userdata, const UAnimSequence* /*pAnimSeq*/)
{	
	const UAnimSequence* pAnimSeq = URugbyCharacterAnimInstance::GetAnimSequenceFromMontageInstance(m_FB_Montage.FB_MontageInstance);

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|noball_getup_mixed_movement durationInTime: 0.25
	if ((userdata == 15) && (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::GetupNoBall))
	{		
		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationTransitionNodeEvent GetUpNoBall '%d' for '%s'"), (int)m_CurrentFBState, *m_pPlayer->GetName());
		
		m_pPlayer->AnimationNotify(ERugbyAnimEvent::TRY_GETUP_EVENT, 0, pAnimSeq, false); //fire an event as it has a valid startTrigger in the node.
		m_ForceStopBlendOutTime = 0.25f;
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
	}

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|withball_getup_null durationInTime: 0.25
	else if ((userdata == 16) && (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::GetupWithBall))
	{
		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationTransitionNodeEvent GetUpWithBall '%d' for '%s'"), (int)m_CurrentFBState, *m_pPlayer->GetName());

		m_pPlayer->AnimationNotify(ERugbyAnimEvent::TRY_GETUP_EVENT, 0, pAnimSeq, false); //fire an event as it has a valid startTrigger in the node.
		m_ForceStopBlendOutTime = 0.25f;
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
	}

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|try_Null durationInEvents: 0.5
	else if ((userdata == 300) && (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::Try))
	{	
		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationTransitionNodeEvent Try '%d' for '%s'"), (int)m_CurrentFBState, *m_pPlayer->GetName());
		m_pPlayer->AnimationNotify(ERugbyAnimEvent::TRY_GETUP_EVENT, 0, pAnimSeq, false); //fire an event as it has a valid startTrigger in the node.

		//Below code is commented as it has a side effect, since it may trigger try animation in a loop in OnUpdateFullBodyNull
		//m_ForceStopBlendOutTime = 0.5f;		
		//m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
	}
}

//============================================================================================

//void RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationEnd(FAnimMontageInstance* montageInst)
//{
//	if (m_CurrentFBState != ERugbyAnim_Mode_FullBodyActions::Null) //added for debugonly
//	{
//		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationEnd mode '%d' for '%s'"), (int)m_CurrentFBState, *m_pPlayer->GetName());
//	}
//
//	if (montageInst && m_FB_Montage.FB_MontageInstance == montageInst)
//	{
//		m_FB_Montage.FB_MontageInstance = nullptr;	//set this FB_MontageInstance to null if it has stopped to avoid having any garbage value.
//
//		if ((m_CurrentFBState != ERugbyAnim_Mode_FullBodyActions::Null) && (m_FB_Montage.FB_MontageUniqueID == montageInst->GetInstanceID()))
//		{
//			if ((m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickLeftFoot) || (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickRightFoot))
//			{
//				UE_LOG(LogTemp, Display, TEXT("FullBodyAction::StateMachine_HandleAnimationEnd:: for mode '%d' for '%s' MontInst Pos '%.3f' BlendTime '%.3f'"), (int)m_CurrentFBState, *m_pPlayer->GetName(), montageInst->GetPosition(), montageInst->GetBlendTime());
// 				if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::PLACED_KICK_OFF)
// 				{
//// 					//38: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot9
//// 					////runtimeChildTransitions		//02: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|placed_kickoff|kickoff_a_right_02_standing_noball
//// 					if (m_SubNode == "kickoff_a_right_02")
//// 					{
//// 						m_SubNode = "standing_noball";
//// 						m_FB_StateMachine.ReInitialise(m_CurrentFBState);
//// 					}
//// 
//// 					//38: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot9
//// 					///runtimeChildTransitions		//07: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|placed_kickoff|kickoff_a_right_01_kickoff_a_right_02
//// 
//// 					//05: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick5
//// 												   // player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|placed_kickoff|kickoff_a_right_01_kickoff_a_right_02
//// 					else if (m_SubNode == "kickoff_a_right_01")
//// 					{
//// 						m_SubNode = "kickoff_a_right_02";
//// 						m_FB_StateMachine.ReInitialise(m_CurrentFBState);
//// 					}
// 				}
//				else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::PENALTY_KICK || m_TypeName == wwDB_DTREQUESTTYPE_ENUM::QUICK_PENALTY_KICK)
//				{
//					//32: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot3
//					//runtimeChildTransitions //00:	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|penalty_kick|penalty_kick_right_01_penalty_kick_right_02
//					//01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick42
//					//runtimeChildTransitions //00:	player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|penalty_kick|penalty_kick_right_01_penalty_kick_right_02
//// 					if (m_SubNode == "penalty_kick_right_01")
//// 					{
//// 						m_SubNode = "penalty_kick_right_02";
//// 						m_FB_StateMachine.ReInitialise(m_CurrentFBState);
//// 					}
//// 					//runtimeChildTransitions //02: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|penalty_kick|penalty_kick_right_02_idle_right
//// 					else if (m_SubNode == "penalty_kick_right_02")
//// 					{
//// 						m_SubNode = "idle_right";
//// 						m_FB_StateMachine.ReInitialise(m_CurrentFBState);
//// 					}
//				}
//				else if (m_TypeName == wwDB_DTREQUESTTYPE_ENUM::PICK_GRASS)
//				{
//					//32: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick_rightfoot3			
//					//runtimeChildTransitions //04: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|penalty_kick|pikc_grass_right_idle_right
//					//01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|mixed_movement_kick42
//// 					if (m_SubNode == "pikc_grass_right")
//// 					{
//// 						m_SubNode = "idle_right";
//// 						m_FB_StateMachine.ReInitialise(m_CurrentFBState);
//// 					}
//				}
//
//				//87: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot_Null			durationInTime: 0.20000000298023224
//				/*
//				testNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|center_drop_out
//				*/
//
//				//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot_Null1 durationInTime: 0.20000000298023224
//				else //should be for center_drop_out only...
//				{
//					m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
//				}
//			}
//
//			//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement_movement_passdown
//			//check below node what needs to be done....
//			/*
//			testNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|nb_dynamic_movement|nbspnt2std|nbspnt2std_short
//			type: Condition_617_NodeActive
//			*/			
//			//else if ((m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::DynamicMoveNoBall) ||
//			//	(m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::DynamicMoveWithBall) ||				
//			//	(m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::RefRaiseFlag) ||		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|ref_raise_flag_Null durationInTime: 0.25
//			//	(m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::FeedBall) ||			//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|feed_ball_Null durationInTime: 0.15000000596046448
//			//	(m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::NumberEightPickup) ||	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|num_eight_pickup_Null durationInTime: 0.15000000596046448
//			//	(m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::QuickLineout)) 		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|quick_lineout_Null durationInTime: 0.5					
//			//{
//			//	UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationEnd:: Processing the animation end for mode '%d' for '%s'"), (int)m_CurrentFBState, *m_pPlayer->GetName());
//			//	m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
//			//}
//
//			//else if (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::GetupCelebration)	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|noball_getup1_Null durationInTime: 0.25
//			//{				
//			//	UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationEnd:: Processing the animation end for mode '%d' for '%s'"), (int)m_CurrentFBState, *m_pPlayer->GetName());
//			//	m_pPlayer->AnimationNotify(ERugbyAnimEvent::TRY_GETUP_EVENT, 0); //fire an event as it has a valid startTrigger in the node.
//			//	m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
//			//}
//		
//		}
//	}
//}//void RugbyAnimationStateMachine_FullBodyAction::StateMachine_HandleAnimationEnd(FAnimMontageInstance* montageInst)

//============================================================================================

void RugbyAnimationStateMachine_FullBodyAction::Select_FBAnimation(bool isLoop /*=false*/ , float PlaySpeed /*= 1.0f*/, float BlendInTime /*= SMDefaultBlendInTime*/, float BlendOutTime /*= SMDefaultBlendOutTime*/, bool HasCrossSyncEvent /*= false */, float AnimStartTime /*= 0.0f*/)
{
	// Should null montage instance as the only way to tell if this function has succeeded or not is to test this variable.
	m_FB_Montage.Reset();

	const FRugbyFBTypeRec *AnimTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetFBActionTypeRec(m_CurrentFBState);
	if (nullptr == AnimTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("Select_FBAnimation:TypePtr is null"));
		return;
	}
	TArray <FRugbyFullBodyAnimRec*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : AnimTypePtr->m_animVariants)
	{
		if ((m_TypeName == AnimRec->m_TypeName) &&
			(m_SubNode == AnimRec->m_Subtype))
		{
			tempRec.Add(AnimRec);
		}
	}

	PlaySelected_FBAnimation(tempRec, BlendInTime, isLoop, PlaySpeed, BlendOutTime, HasCrossSyncEvent, AnimStartTime);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_FullBodyAction::ClearMontageInstanceReferences(FAnimMontageInstance* pMontageInstance)
{
	if (nullptr == pMontageInstance)
	{
		return;
	}

	if (pMontageInstance == m_FB_Montage.FB_MontageInstance && false == pMontageInstance->IsPlaying())
	{
		m_FB_Montage.Reset();
	}
}

//============================================================================================

void RugbyAnimationStateMachine_FullBodyAction::PlaySelected_FBAnimation(TArray<FRugbyFullBodyAnimRec*>& Rec, float blendinTime /*= SMDefaultBlendInTime*/, bool LoopAlways /*= false*/, float playRate /*=1.0f*/, float BlendOutTime /*= SMDefaultBlendOutTime*/, bool HasCrossSyncEvent /*= false */, float AnimStartTime /*=0.0f*/)
{
	ensureAlways(Rec.Num() > 0);
	if (Rec.Num())
	{
		TArray<FRugbyFullBodyAnimRec*> usableAnimationList;
		for (auto &SelRec : Rec)
		{
			if (SelRec->m_pAnimSequence)
			{
				usableAnimationList.Add(SelRec);
			}
		}

		ensureAlways(usableAnimationList.Num() > 0);

		if (usableAnimationList.Num() > 0)
		{
			FRugbyFullBodyAnimRec* pAnimRec = nullptr;
			if (usableAnimationList.Num() == 1)
			{
				pAnimRec = usableAnimationList[0];
			}
			else //cases like 
			{
				int RandomValue = FMath::RoundToInt(m_pRUPlayerAnimation->GetGameRandomVariable()->getValue());

				//check if this number is less than number of animations minus 1 and also it's a non-negative
				int SelectedIndex = ((RandomValue < (usableAnimationList.Num() - 1)) && RandomValue >= 0) ? RandomValue : 0;
				pAnimRec = usableAnimationList[SelectedIndex];
				UE_LOG(LogTemp, Display, TEXT("PlaySelected_FBAnimation: RandomValue is used for animation, make sure it's really random '%s' for '%s'"), *(pAnimRec->m_animName), *m_pPlayer->GetName());
			}

			ensureAlways(pAnimRec->m_pAnimSequence != nullptr);
			UE_LOG(LogTemp, Display, TEXT("PlaySelected_FBAnimation: '%s' for '%s'"), *(pAnimRec->m_animName), *m_pPlayer->GetName());
			m_FB_Montage.Reset();
			m_FB_Montage.FB_AnimRec = pAnimRec;
			FAnimMontageInstance* pMontageInstance = m_pSMMgr->PlayStateMachineAnimationMontage(this, pAnimRec, AnimStartTime, blendinTime, LoopAlways, BlendOutTime, playRate , HasCrossSyncEvent );
			ensureAlways(pMontageInstance != nullptr);
			if (pMontageInstance != nullptr)
			{
				if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
				{					
					URugbyCharacterAnimInstance* pAnimInstance = m_pPlayer->GetAnimInstance();
					pAnimInstance->SetIkBitMask(pAnimRec->m_IkBitMask);
					if (pAnimRec->m_IkBitMask != 0)
					{						
						pAnimInstance->SetIkBlendTime(blendinTime, SMDefaultBlendOutTime); //#todo: Check this blend time						
					}
				}

				m_FB_Montage.FB_MontageInstance = pMontageInstance;
				//UE_LOG(LogTemp, Display, TEXT("BlendTime: '%.3f'"), pMontageInstance->GetBlendTime());
				m_FB_Montage.FB_MontageUniqueID = pMontageInstance->GetInstanceID();
			}
		}
		else
		{
			UE_LOG(LogTemp, Display, TEXT("PlaySelected_FBAnimation: Animations are missing."));
		}
	}
	else
	{
		m_CurrentFBState = ERugbyAnim_Mode_FullBodyActions::Null;
		m_FB_StateMachine.ChangeState(ERugbyAnim_Mode_FullBodyActions::Null);
		UE_LOG(LogTemp, Display, TEXT("PlaySelected_FBAnimation: Number of Animation record is zero, resetting the statemachine to NULL"));
	}
}

//============================================================================================

bool RugbyAnimationStateMachine_FullBodyAction::StateMachine_IsAnimationAvailable(const char* request) 
{
	return true;
}

//============================================================================================
//This function is not an idle solution, but it ensures if we do a box kick from ground (after ruck) it doesnot have any blend in time and the animation starts after some offset time to match the idle pose.
//Also it ensures if the IK event is before the animation start time (now default is 0.18), it will use the IK event time as the start time. //RC4-3029
void RugbyAnimationStateMachine_FullBodyAction::StateMachine_FindBoxKickStartPosition(float &OutAnimStartTime, float &OutBlendInTime) //not used...
{
	OutBlendInTime = 0.0f;
	const FRugbyFBTypeRec *AnimTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetFBActionTypeRec(m_CurrentFBState);
	TArray <FRugbyFullBodyAnimRec*> tempRec;
	tempRec.Empty();
	float IkEventTime = 0.0f;
	float BallContactEventTime = 0.0f;
	ERugbyAnimEvent IKEventType = ERugbyAnimEvent::INVALID;
	ERugbyAnimEvent BallContactEventType = ERugbyAnimEvent::INVALID;

	for (auto &AnimRec : AnimTypePtr->m_animVariants)
	{
		if ((m_TypeName == AnimRec->m_TypeName) &&
			(m_SubNode == AnimRec->m_Subtype))
		{
			m_pPlayer->GetAnimInstance()->GetEventTimeFromAnimSequenceFromEventIndex(AnimRec->m_pAnimSequence, 0, ERugbyAnimEvent::IK_TRANSITION_EVENT, IkEventTime, IKEventType); //IkEventTime is from animation end
			m_pPlayer->GetAnimInstance()->GetEventTimeFromAnimSequenceFromEventIndex(AnimRec->m_pAnimSequence, 0, ERugbyAnimEvent::BALL_CONTACT_EVENT, BallContactEventTime, BallContactEventType);

			//these asserts are added so that if the animation is modified, we get notified if the logic would stil work.
			ensureAlways(IKEventType == ERugbyAnimEvent::IK_TRANSITION_EVENT);
			ensureAlways(BallContactEventType == ERugbyAnimEvent::BALL_CONTACT_EVENT);
			ensureAlways(BallContactEventTime < IkEventTime);

			if (IKEventType == ERugbyAnimEvent::IK_TRANSITION_EVENT && BallContactEventTime < IkEventTime && (AnimRec->m_pAnimSequence->SequenceLength - IkEventTime) >= 0.0f)
			{
				OutAnimStartTime = FMath::Min(AnimRec->m_pAnimSequence->SequenceLength - IkEventTime, 0.18f); //0.18f is the start position to match pose with idle anim with zero blendin time.
				break;
			}
		}
	}
}


//============================================================================================

bool RugbyAnimationStateMachine_FullBodyAction::StateMachine_IsDummyHalfPlayingBoxKick() const
{
	return (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickLeftFoot  || m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickRightFoot) && 
		(m_TypeName == wwDB_DTREQUESTTYPE_ENUM::BOX_KICK) && 
		(m_SubNode == "");
}

//============================================================================================

bool RugbyAnimationStateMachine_FullBodyAction::StateMachine_IsPlayingLongPuntStandKick() const
{
	return (m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickLeftFoot || m_CurrentFBState == ERugbyAnim_Mode_FullBodyActions::KickRightFoot) &&
		(m_TypeName == wwDB_DTREQUESTTYPE_ENUM::LONG_PUNT_KICK) &&
		(m_SubNode == "standing");
}

//============================================================================================

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
//===============================================================================
//===============================================================================
FString RugbyAnimationStateMachine_FullBodyAction::GetDebugStateText()
{
	FString subStateString = GetSubStateInfo();
	FString debugString = FString::Printf
	(
		TEXT("FullBody SM:   State: %s  Sub-State: %s\n"),
		*ENUM_TO_FSTRING(ERugbyAnim_Mode_FullBodyActions, m_FB_StateMachine.GetCurrentStateKey()),
		*subStateString
	);
	return debugString;
}

//===============================================================================
//===============================================================================
FString RugbyAnimationStateMachine_FullBodyAction::GetSubStateInfo()
{
	// Only one sub state should be able to be active at a time
	if (m_kickoffStateMachine.GetCurrentStateKey() != EKickOffState::InvalidState)
	{
		return ENUM_TO_FSTRING(EKickOffState, m_kickoffStateMachine.GetCurrentStateKey());
	}
	if (m_penaltyKickStateMachine.GetCurrentStateKey() != EPenaltyKickState::InvalidState)
	{
		return ENUM_TO_FSTRING(EPenaltyKickState, m_penaltyKickStateMachine.GetCurrentStateKey());
	}
	if (m_DummyHalfStateMachine.GetCurrentStateKey() != EDummyHalfAnimationState::InvalidState)
	{
		return ENUM_TO_FSTRING(EDummyHalfAnimationState, m_DummyHalfStateMachine.GetCurrentStateKey());
	}
	if (m_LineOutJumperStateMachine.GetCurrentStateKey() != ELineOutJumperAnimationState::InvalidState)
	{
		return ENUM_TO_FSTRING(ELineOutJumperAnimationState, m_LineOutJumperStateMachine.GetCurrentStateKey());
	}
	if (m_LineOutStateMachine.GetCurrentStateKey() != ELineOutAnimationState::InvalidState)
	{
		return ENUM_TO_FSTRING(ELineOutAnimationState, m_LineOutStateMachine.GetCurrentStateKey());
	}
	return "NONE";
}

#endif
