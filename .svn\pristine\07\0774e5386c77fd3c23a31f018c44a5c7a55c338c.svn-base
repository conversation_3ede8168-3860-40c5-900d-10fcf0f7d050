<html>
<head>
<title>Welcome to FMOD for Unreal</title>
<link rel="stylesheet" href="style/docs.css">
<link rel="stylesheet" href="style/code_highlight.css">
<script type="text/javascript" src="scripts/language-selector.js"></script></head>
<body>
<div class="docs-body">
<div class="manual-toc">
<p>Unreal Integration 2.02</p>
<ul>
<li class="manual-current-chapter manual-active-chapter"><a href="welcome.html">Welcome to FMOD for Unreal</a><ul class="subchapters"><li><a href="welcome-whats-new-202.html">New in FMOD for Unreal 2.02</a></li><li><a href="welcome-whats-new-201.html">New in FMOD for Unreal 2.01</a></li><li><a href="welcome-whats-new-200.html">New in FMOD for Unreal 2.00</a></li></ul></li>
<li><a href="user-guide.html">User Guide</a></li>
<li><a href="settings.html">Settings</a></li>
<li><a href="plugins.html">Plugins</a></li>
<li><a href="niagara.html">Niagara Integration</a></li>
<li><a href="api-reference.html">API Reference</a></li>
<li><a href="blueprint-reference.html">Blueprint Reference</a></li>
<li><a href="platform-specifics.html">Platform Specifics</a></li>
<li><a href="troubleshooting.html">Troubleshooting</a></li>
<li><a href="audiolink.html">AudioLink</a></li>
<li><a href="glossary.html">Glossary</a></li>
</ul>
</div>
<div class="manual-content api">
<h1>1. Welcome to FMOD for Unreal</h1>
<p>FMOD for Unreal is a plugin that allows you to use the FMOD APIs and projects from FMOD Studio in your Unreal game.</p>
<h2 id="supported-unreal-engine-versions"><a href="#supported-unreal-engine-versions">1.1 Supported Unreal Engine Versions</a></h2>
<p>The integration is compatible with public release versions of Unreal Engine 4.25, 4.25+, 4.26, 4.27, 5.0, 5.1, 5.2 and 5.3.</p>
<p>Preview builds are not officially supported however you are able to <a href="user-guide.html#compiling-the-plugin-optional">build the plugin from source</a> yourself.</p>
<h2 id="supported-platforms"><a href="#supported-platforms">1.2 Supported platforms</a></h2>
<p>The integration supports:</p>
<ul>
<li>Windows Desktop</li>
<li>Linux</li>
<li>MacOS</li>
<li>iOS</li>
<li>tvOS</li>
<li>Android</li>
<li>PS4</li>
<li>PS5</li>
<li>Xbox One</li>
<li>Xbox Series X|S</li>
<li>Switch</li>
</ul>
<h2 id="licensing"><a href="#licensing">1.3 Licensing</a></h2>
<p>The integration itself is free, but you must have the appropriate FMOD License to release a title using FMOD Studio with Unreal. For more information about licensing see the FMOD sales page.</p>
<h2 id="whats-new"><a href="#whats-new">1.4 What's New?</a></h2>
<p>This section describes the major changes introduced in each new release. See the <a href="https://fmod.com/docs/2.02/api/welcome-revision-history.html">Detailed Revision History</a> for information regarding each patch release.</p><ul class="docs-body-subchapters"><li><a href="welcome-whats-new-202.html">New in FMOD for Unreal 2.02</a></li>
<li><a href="welcome-whats-new-201.html">New in FMOD for Unreal 2.01</a></li>
<li><a href="welcome-whats-new-200.html">New in FMOD for Unreal 2.00</a></li>
</ul></div>

<p class="manual-footer">Unreal Integration 2.02.20 (2023-12-12). &copy; 2023 Firelight Technologies Pty Ltd.</p>
</body>
</html>

</div>
