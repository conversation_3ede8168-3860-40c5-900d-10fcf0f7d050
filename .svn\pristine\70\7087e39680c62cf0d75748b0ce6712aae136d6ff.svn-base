/*--------------------------------------------------------------
|        Copyright (C) 1997-2012 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

/// THIS FILE IS AUTOMATICALLY GENERATED BY 'CacheTableCodeGenerator'


#pragma once

///-----------------------------------------------------------
/// Debug struct for: rudb_attribute_presets
struct rudb_attribute_presets_row
{
	unsigned short db_id,pad;
	int	fitness;
	int	agility;
	int	speed;
	int	acceleration;
	int	aggression;
	int	tackling;
	int	break_tackle;
	int	passing;
	int	offloading;
	int	general_kicking;
	int	goal_kicking;
	int	catching;
	int	strength;
	int	mental_ability;
	int	jumping;
	int	discipline;
};

///-----------------------------------------------------------
/// Enums for: rudb_attribute_presets

enum {
	CCDB_ATTRIBUTEPRESETS_FITNESS=0,
	CCDB_ATTRIBUTEPRESETS_AGILITY,
	CCDB_ATTRIBUTEPRESETS_SPEED,
	CCDB_ATTRIBUTEPRESETS_ACCELERATION,
	CCDB_ATTRIBUTEPRESETS_AGGRESSION,
	CCDB_ATTRIBUTEPRESETS_TACKLING,
	CCDB_ATTRIBUTEPRESETS_BREAK_TACKLE,
	CCDB_ATTRIBUTEPRESETS_PASSING,
	CCDB_ATTRIBUTEPRESETS_OFFLOADING,
	CCDB_ATTRIBUTEPRESETS_GENERAL_KICKING,
	CCDB_ATTRIBUTEPRESETS_GOAL_KICKING,
	CCDB_ATTRIBUTEPRESETS_CATCHING,
	CCDB_ATTRIBUTEPRESETS_STRENGTH,
	CCDB_ATTRIBUTEPRESETS_MENTAL_ABILITY,
	CCDB_ATTRIBUTEPRESETS_JUMPING,
	CCDB_ATTRIBUTEPRESETS_DISCIPLINE,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_career_draftable_player
struct rudb_career_draftable_player_row
{
	unsigned short db_id,pad;
	unsigned short	player_id;
	char	row_padding[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_career_draftable_player

enum {
	CCDB_CAREERDRAFTABLEPLAYER_PLAYER_ID=0,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_career_inbox
struct rudb_career_inbox_row
{
	unsigned short db_id,pad;
	int	email_id;
	int	data_index;
	unsigned char	read;
	char	replacements[640];
	char	date[16];
	char	row_padding[3];
};

///-----------------------------------------------------------
/// Enums for: rudb_career_inbox

enum {
	CCDB_CAREERINBOX_EMAIL_ID=0,
	CCDB_CAREERINBOX_DATA_INDEX,
	CCDB_CAREERINBOX_READ,
	CCDB_CAREERINBOX_REPLACEMENTS,
	CCDB_CAREERINBOX_DATE,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_career_mode_comp
struct rudb_career_mode_comp_row
{
	unsigned short db_id,pad;
	int	start_date;
	unsigned short	definition_id;
	char	row_padding[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_career_mode_comp

enum {
	CCDB_CAREERMODECOMP_START_DATE=0,
	CCDB_CAREERMODECOMP_DEFINITION_ID,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_career_my_contracts
struct rudb_career_my_contracts_row
{
	unsigned short db_id,pad;
	unsigned short	from_team_id;
	unsigned short	duration;
	unsigned short	value;
	unsigned char	have_read;
	unsigned char	from_club_team;
};

///-----------------------------------------------------------
/// Enums for: rudb_career_my_contracts

enum {
	CCDB_CAREERMYCONTRACTS_FROM_TEAM_ID=0,
	CCDB_CAREERMYCONTRACTS_DURATION,
	CCDB_CAREERMYCONTRACTS_VALUE,
	CCDB_CAREERMYCONTRACTS_HAVE_READ,
	CCDB_CAREERMYCONTRACTS_FROM_CLUB_TEAM,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_career_played_match
struct rudb_career_played_match_row
{
	unsigned short db_id,pad;
	int	home_team_score;
	int	away_team_score;
	int	date;
	unsigned short	home_team_id;
	unsigned short	away_team_id;
};

///-----------------------------------------------------------
/// Enums for: rudb_career_played_match

enum {
	CCDB_CAREERPLAYEDMATCH_HOME_TEAM_SCORE=0,
	CCDB_CAREERPLAYEDMATCH_AWAY_TEAM_SCORE,
	CCDB_CAREERPLAYEDMATCH_DATE,
	CCDB_CAREERPLAYEDMATCH_HOME_TEAM_ID,
	CCDB_CAREERPLAYEDMATCH_AWAY_TEAM_ID,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_career_pro_contract_interest
struct rudb_career_pro_contract_interest_row
{
	unsigned short db_id,pad;
	unsigned short	team_db_id;
	unsigned short	reject_count;
	unsigned short	offer_count;
	unsigned short	frustration_level;
	unsigned short	interest_level;
	char	row_padding[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_career_pro_contract_interest

enum {
	CCDB_CAREERPROCONTRACTINTEREST_TEAM_DB_ID=0,
	CCDB_CAREERPROCONTRACTINTEREST_REJECT_COUNT,
	CCDB_CAREERPROCONTRACTINTEREST_OFFER_COUNT,
	CCDB_CAREERPROCONTRACTINTEREST_FRUSTRATION_LEVEL,
	CCDB_CAREERPROCONTRACTINTEREST_INTEREST_LEVEL,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_career_pro_player_matches
struct rudb_career_pro_player_matches_row
{
	unsigned short db_id,pad;
	int	home_team_score;
	int	away_team_score;
	int	performance;
	int	date;
	unsigned short	comp_inst_id;
	unsigned short	match_inst_id;
	unsigned short	home_team_id;
	unsigned short	away_team_id;
	unsigned short	pro_team_id;
	unsigned char	match_won;
	unsigned char	participated;
};

///-----------------------------------------------------------
/// Enums for: rudb_career_pro_player_matches

enum {
	CCDB_CAREERPROPLAYERMATCHES_HOME_TEAM_SCORE=0,
	CCDB_CAREERPROPLAYERMATCHES_AWAY_TEAM_SCORE,
	CCDB_CAREERPROPLAYERMATCHES_PERFORMANCE,
	CCDB_CAREERPROPLAYERMATCHES_DATE,
	CCDB_CAREERPROPLAYERMATCHES_COMP_INST_ID,
	CCDB_CAREERPROPLAYERMATCHES_MATCH_INST_ID,
	CCDB_CAREERPROPLAYERMATCHES_HOME_TEAM_ID,
	CCDB_CAREERPROPLAYERMATCHES_AWAY_TEAM_ID,
	CCDB_CAREERPROPLAYERMATCHES_PRO_TEAM_ID,
	CCDB_CAREERPROPLAYERMATCHES_MATCH_WON,
	CCDB_CAREERPROPLAYERMATCHES_PARTICIPATED,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_career_pro_player_stats
struct rudb_career_pro_player_stats_row
{
	unsigned short db_id,pad;
	unsigned short	comp_id;
	unsigned short	year;
	unsigned short	satellite_index;
	unsigned short	tackle_attempts;
	unsigned short	successful_tackles;
	unsigned short	points_scored;
	unsigned short	running_meters;
	unsigned short	kicking_meters;
	unsigned char	tries_scored;
	unsigned char	line_breaks;
	unsigned char	offloads;
	unsigned char	offloads_attempted;
	unsigned char	kicks;
	unsigned char	handling_errors;
	unsigned char	goal_attempts;
	unsigned char	successful_goals;
	unsigned char	field_goal_attempts;
	unsigned char	successful_field_goals;
	unsigned char	yellow_cards;
	unsigned char	red_cards;
	unsigned char	injuries;
	unsigned char	penalties_against;
	unsigned char	games_played;
	unsigned char	successful_penalties;
	unsigned char	penalty_attempts;
	unsigned char	ruck_entry;
	unsigned char	contest_win;
	unsigned char	lineout_steal_attempts;
	unsigned char	successful_lineout_steal;
	char	row_padding[3];
};

///-----------------------------------------------------------
/// Enums for: rudb_career_pro_player_stats

enum {
	CCDB_CAREERPROPLAYERSTATS_COMP_ID=0,
	CCDB_CAREERPROPLAYERSTATS_YEAR,
	CCDB_CAREERPROPLAYERSTATS_SATELLITE_INDEX,
	CCDB_CAREERPROPLAYERSTATS_TACKLE_ATTEMPTS,
	CCDB_CAREERPROPLAYERSTATS_SUCCESSFUL_TACKLES,
	CCDB_CAREERPROPLAYERSTATS_POINTS_SCORED,
	CCDB_CAREERPROPLAYERSTATS_RUNNING_METERS,
	CCDB_CAREERPROPLAYERSTATS_KICKING_METERS,
	CCDB_CAREERPROPLAYERSTATS_TRIES_SCORED,
	CCDB_CAREERPROPLAYERSTATS_LINE_BREAKS,
	CCDB_CAREERPROPLAYERSTATS_OFFLOADS,
	CCDB_CAREERPROPLAYERSTATS_OFFLOADS_ATTEMPTED,
	CCDB_CAREERPROPLAYERSTATS_KICKS,
	CCDB_CAREERPROPLAYERSTATS_HANDLING_ERRORS,
	CCDB_CAREERPROPLAYERSTATS_GOAL_ATTEMPTS,
	CCDB_CAREERPROPLAYERSTATS_SUCCESSFUL_GOALS,
	CCDB_CAREERPROPLAYERSTATS_FIELD_GOAL_ATTEMPTS,
	CCDB_CAREERPROPLAYERSTATS_SUCCESSFUL_FIELD_GOALS,
	CCDB_CAREERPROPLAYERSTATS_YELLOW_CARDS,
	CCDB_CAREERPROPLAYERSTATS_RED_CARDS,
	CCDB_CAREERPROPLAYERSTATS_INJURIES,
	CCDB_CAREERPROPLAYERSTATS_PENALTIES_AGAINST,
	CCDB_CAREERPROPLAYERSTATS_GAMES_PLAYED,
	CCDB_CAREERPROPLAYERSTATS_SUCCESSFUL_PENALTIES,
	CCDB_CAREERPROPLAYERSTATS_PENALTY_ATTEMPTS,
	CCDB_CAREERPROPLAYERSTATS_RUCK_ENTRY,
	CCDB_CAREERPROPLAYERSTATS_CONTEST_WIN,
	CCDB_CAREERPROPLAYERSTATS_LINEOUT_STEAL_ATTEMPTS,
	CCDB_CAREERPROPLAYERSTATS_SUCCESSFUL_LINEOUT_STEAL,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_city
struct rudb_city_row
{
	unsigned short db_id,pad;
	unsigned short	country_id;
	char	name[13];
	char	row_padding[1];
};

///-----------------------------------------------------------
/// Enums for: rudb_city

enum {
	CCDB_CITY_COUNTRY_ID=0,
	CCDB_CITY_NAME,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_def_match
struct rudb_comp_def_match_row
{
	unsigned short db_id,pad;
	int	date;
	unsigned short	round_id;
	unsigned short	team_a_id;
	unsigned short	team_b_id;
	unsigned short	stadium_id;
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_def_match

enum {
	CCDB_COMPDEFMATCH_DATE=0,
	CCDB_COMPDEFMATCH_ROUND_ID,
	CCDB_COMPDEFMATCH_TEAM_A_ID,
	CCDB_COMPDEFMATCH_TEAM_B_ID,
	CCDB_COMPDEFMATCH_STADIUM_ID,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_def_round
struct rudb_comp_def_round_row
{
	unsigned short db_id,pad;
	unsigned short	competition_id;
	char	name[29];
	char	row_padding[1];
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_def_round

enum {
	CCDB_COMPDEFROUND_COMPETITION_ID=0,
	CCDB_COMPDEFROUND_NAME,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_def_sat_name
struct rudb_comp_def_sat_name_row
{
	unsigned short db_id,pad;
	unsigned short	competition_id;
	char	name[13];
	char	mnemonic[4];
	char	row_padding[1];
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_def_sat_name

enum {
	CCDB_COMPDEFSATNAME_COMPETITION_ID=0,
	CCDB_COMPDEFSATNAME_NAME,
	CCDB_COMPDEFSATNAME_MNEMONIC,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_def_team
struct rudb_comp_def_team_row
{
	unsigned short db_id,pad;
	unsigned short	competition_id;
	unsigned short	team_id;
	unsigned char	pool_or_conference;
	char	row_padding[3];
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_def_team

enum {
	CCDB_COMPDEFTEAM_COMPETITION_ID=0,
	CCDB_COMPDEFTEAM_TEAM_ID,
	CCDB_COMPDEFTEAM_POOL_OR_CONFERENCE,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_inst
struct rudb_comp_inst_row
{
	unsigned short db_id,pad;
	int	start_date;
	unsigned short	definition_id;
	unsigned short	next_match_index;
	unsigned short	num_rounds;
	unsigned short	num_matches;
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_inst

enum {
	CCDB_COMPINST_START_DATE=0,
	CCDB_COMPINST_DEFINITION_ID,
	CCDB_COMPINST_NEXT_MATCH_INDEX,
	CCDB_COMPINST_NUM_ROUNDS,
	CCDB_COMPINST_NUM_MATCHES,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_inst_match
struct rudb_comp_inst_match_row
{
	unsigned short db_id,pad;
	int	date;
	unsigned short	home_team_id;
	unsigned short	away_team_id;
	unsigned short	stadium_id;
	short	side_a_score;
	short	side_b_score;
	unsigned short	round_id;
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_inst_match

enum {
	CCDB_MATCH_DATE=0,
	CCDB_MATCH_HOME_TEAM_ID,
	CCDB_MATCH_AWAY_TEAM_ID,
	CCDB_MATCH_STADIUM_ID,
	CCDB_MATCH_SIDE_A_SCORE,
	CCDB_MATCH_SIDE_B_SCORE,
	CCDB_MATCH_ROUND_ID,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_inst_player_match_stats
struct rudb_comp_inst_player_match_stats_row
{
	unsigned short db_id,pad;
	float	possession;
	float	territory;
	unsigned short	comp_inst_match_id;
	unsigned short	team_id;
	unsigned char	handling_errors;
	unsigned char	penalties_conceded;
	unsigned char	tries;
	unsigned char	conversions;
	unsigned char	penalties_converted;
	unsigned char	drop_goals;
	char	try_scorer_1_str[30];
	char	try_scorer_2_str[30];
	char	try_scorer_3_str[30];
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_inst_player_match_stats

enum {
	CCDB_COMPINSTPLAYERMATCHSTATS_POSSESSION=0,
	CCDB_COMPINSTPLAYERMATCHSTATS_TERRITORY,
	CCDB_COMPINSTPLAYERMATCHSTATS_COMP_INST_MATCH_ID,
	CCDB_COMPINSTPLAYERMATCHSTATS_TEAM_ID,
	CCDB_COMPINSTPLAYERMATCHSTATS_HANDLING_ERRORS,
	CCDB_COMPINSTPLAYERMATCHSTATS_PENALTIES_CONCEDED,
	CCDB_COMPINSTPLAYERMATCHSTATS_TRIES,
	CCDB_COMPINSTPLAYERMATCHSTATS_CONVERSIONS,
	CCDB_COMPINSTPLAYERMATCHSTATS_PENALTIES_CONVERTED,
	CCDB_COMPINSTPLAYERMATCHSTATS_DROP_GOALS,
	CCDB_COMPINSTPLAYERMATCHSTATS_TRY_SCORER_1_STR,
	CCDB_COMPINSTPLAYERMATCHSTATS_TRY_SCORER_2_STR,
	CCDB_COMPINSTPLAYERMATCHSTATS_TRY_SCORER_3_STR,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_inst_player_removal
struct rudb_comp_inst_player_removal_row
{
	unsigned short db_id,pad;
	int	end_date;
	unsigned short	instance_id;
	unsigned short	player_id;
	unsigned char	type;
	char	row_padding[3];
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_inst_player_removal

enum {
	CCDB_COMPINSTPLAYERREMOVAL_END_DATE=0,
	CCDB_COMPINSTPLAYERREMOVAL_INSTANCE_ID,
	CCDB_COMPINSTPLAYERREMOVAL_PLAYER_ID,
	CCDB_COMPINSTPLAYERREMOVAL_TYPE,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_inst_player_stats
struct rudb_comp_inst_player_stats_row
{
	unsigned short db_id,pad;
	unsigned short	instance_id;
	unsigned short	player_id;
	unsigned short	satellite_index;
	unsigned short	tackle_attempts;
	unsigned short	successful_tackles;
	unsigned short	points_scored;
	unsigned short	running_meters;
	unsigned short	kicking_meters;
	unsigned char	tries_scored;
	unsigned char	line_breaks;
	unsigned char	offloads;
	unsigned char	offloads_attempted;
	unsigned char	kicks;
	unsigned char	handling_errors;
	unsigned char	goal_attempts;
	unsigned char	successful_goals;
	unsigned char	field_goal_attempts;
	unsigned char	successful_field_goals;
	unsigned char	yellow_cards;
	unsigned char	red_cards;
	unsigned char	injuries;
	unsigned char	penalties_against;
	unsigned char	games_played;
	unsigned char	successful_penalties;
	unsigned char	penalty_attempts;
	unsigned char	ruck_entry;
	unsigned char	contest_win;
	unsigned char	lineout_steal_attempts;
	unsigned char	successful_lineout_steal;
	char	row_padding[3];
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_inst_player_stats

enum {
	CCDB_COMPINSTPLAYERSTATS_INSTANCE_ID=0,
	CCDB_COMPINSTPLAYERSTATS_PLAYER_ID,
	CCDB_COMPINSTPLAYERSTATS_SATELLITE_INDEX,
	CCDB_COMPINSTPLAYERSTATS_TACKLE_ATTEMPTS,
	CCDB_COMPINSTPLAYERSTATS_SUCCESSFUL_TACKLES,
	CCDB_COMPINSTPLAYERSTATS_POINTS_SCORED,
	CCDB_COMPINSTPLAYERSTATS_RUNNING_METERS,
	CCDB_COMPINSTPLAYERSTATS_KICKING_METERS,
	CCDB_COMPINSTPLAYERSTATS_TRIES_SCORED,
	CCDB_COMPINSTPLAYERSTATS_LINE_BREAKS,
	CCDB_COMPINSTPLAYERSTATS_OFFLOADS,
	CCDB_COMPINSTPLAYERSTATS_OFFLOADS_ATTEMPTED,
	CCDB_COMPINSTPLAYERSTATS_KICKS,
	CCDB_COMPINSTPLAYERSTATS_HANDLING_ERRORS,
	CCDB_COMPINSTPLAYERSTATS_GOAL_ATTEMPTS,
	CCDB_COMPINSTPLAYERSTATS_SUCCESSFUL_GOALS,
	CCDB_COMPINSTPLAYERSTATS_FIELD_GOAL_ATTEMPTS,
	CCDB_COMPINSTPLAYERSTATS_SUCCESSFUL_FIELD_GOALS,
	CCDB_COMPINSTPLAYERSTATS_YELLOW_CARDS,
	CCDB_COMPINSTPLAYERSTATS_RED_CARDS,
	CCDB_COMPINSTPLAYERSTATS_INJURIES,
	CCDB_COMPINSTPLAYERSTATS_PENALTIES_AGAINST,
	CCDB_COMPINSTPLAYERSTATS_GAMES_PLAYED,
	CCDB_COMPINSTPLAYERSTATS_SUCCESSFUL_PENALTIES,
	CCDB_COMPINSTPLAYERSTATS_PENALTY_ATTEMPTS,
	CCDB_COMPINSTPLAYERSTATS_RUCK_ENTRY,
	CCDB_COMPINSTPLAYERSTATS_CONTEST_WIN,
	CCDB_COMPINSTPLAYERSTATS_LINEOUT_STEAL_ATTEMPTS,
	CCDB_COMPINSTPLAYERSTATS_SUCCESSFUL_LINEOUT_STEAL,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_inst_round
struct rudb_comp_inst_round_row
{
	unsigned short db_id,pad;
	unsigned short	instance_id;
	char	row_padding[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_inst_round

enum {
	CCDB_ROUND_INSTANCE_ID=0,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_inst_team
struct rudb_comp_inst_team_row
{
	unsigned short db_id,pad;
	unsigned short	instance_id;
	unsigned short	team_id;
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_inst_team

enum {
	CCDB_COMPINSTTEAM_INSTANCE_ID=0,
	CCDB_COMPINSTTEAM_TEAM_ID,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_inst_team_result
struct rudb_comp_inst_team_result_row
{
	unsigned short db_id,pad;
	unsigned short	competition_id;
	unsigned short	team_id;
	unsigned short	year;
	unsigned char	finals_result;
	unsigned char	rank;
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_inst_team_result

enum {
	CCDB_COMPINSTTEAMRESULT_COMPETITION_ID=0,
	CCDB_COMPINSTTEAMRESULT_TEAM_ID,
	CCDB_COMPINSTTEAMRESULT_YEAR,
	CCDB_COMPINSTTEAMRESULT_FINALS_RESULT,
	CCDB_COMPINSTTEAMRESULT_RANK,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_inst_team_stats
struct rudb_comp_inst_team_stats_row
{
	unsigned short db_id,pad;
	int	streak_start_date;
	unsigned short	instance_id;
	unsigned short	team_id;
	unsigned short	satellite_index;
	unsigned short	confidence;
	short	match_streak;
	unsigned short	tries_scored;
	unsigned short	tries_conceded;
	unsigned short	points_for;
	unsigned short	points_against;
	unsigned short	penalties_awarded;
	unsigned short	penalties_conceded;
	unsigned short	preliminaries_points;
	unsigned short	finals_points;
	unsigned short	games_played;
	unsigned short	games_won;
	unsigned short	games_lost;
	unsigned short	games_drawn;
	unsigned short	bonus_points;
	unsigned short	byes;
	unsigned short	longest_win_streak;
	unsigned short	longest_lose_streak;
	unsigned short	prelim_points_for;
	unsigned short	prelim_points_against;
	unsigned short	prelim_games_played;
	unsigned short	prelim_games_won;
	unsigned short	prelim_games_lost;
	unsigned short	prelim_games_drawn;
	unsigned short	prelim_bonus_points;
	unsigned short	prelim_byes;
	unsigned short	prelim_tries_scored;
	unsigned short	prelim_tries_conceded;
	unsigned short	prelim_penalties_conceded;
	unsigned short	match_points_for;
	unsigned short	match_points_against;
	unsigned short	match_tries_conceded;
	unsigned short	match_tries_scored;
	unsigned short	match_penalties_conceded;
	unsigned char	player_selected;
	char	row_padding[1];
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_inst_team_stats

enum {
	CCDB_COMPINSTTEAMSTATS_STREAK_START_DATE=0,
	CCDB_COMPINSTTEAMSTATS_INSTANCE_ID,
	CCDB_COMPINSTTEAMSTATS_TEAM_ID,
	CCDB_COMPINSTTEAMSTATS_SATELLITE_INDEX,
	CCDB_COMPINSTTEAMSTATS_CONFIDENCE,
	CCDB_COMPINSTTEAMSTATS_MATCH_STREAK,
	CCDB_COMPINSTTEAMSTATS_TRIES_SCORED,
	CCDB_COMPINSTTEAMSTATS_TRIES_CONCEDED,
	CCDB_COMPINSTTEAMSTATS_POINTS_FOR,
	CCDB_COMPINSTTEAMSTATS_POINTS_AGAINST,
	CCDB_COMPINSTTEAMSTATS_PENALTIES_AWARDED,
	CCDB_COMPINSTTEAMSTATS_PENALTIES_CONCEDED,
	CCDB_COMPINSTTEAMSTATS_PRELIMINARIES_POINTS,
	CCDB_COMPINSTTEAMSTATS_FINALS_POINTS,
	CCDB_COMPINSTTEAMSTATS_GAMES_PLAYED,
	CCDB_COMPINSTTEAMSTATS_GAMES_WON,
	CCDB_COMPINSTTEAMSTATS_GAMES_LOST,
	CCDB_COMPINSTTEAMSTATS_GAMES_DRAWN,
	CCDB_COMPINSTTEAMSTATS_BONUS_POINTS,
	CCDB_COMPINSTTEAMSTATS_BYES,
	CCDB_COMPINSTTEAMSTATS_LONGEST_WIN_STREAK,
	CCDB_COMPINSTTEAMSTATS_LONGEST_LOSE_STREAK,
	CCDB_COMPINSTTEAMSTATS_PRELIM_POINTS_FOR,
	CCDB_COMPINSTTEAMSTATS_PRELIM_POINTS_AGAINST,
	CCDB_COMPINSTTEAMSTATS_PRELIM_GAMES_PLAYED,
	CCDB_COMPINSTTEAMSTATS_PRELIM_GAMES_WON,
	CCDB_COMPINSTTEAMSTATS_PRELIM_GAMES_LOST,
	CCDB_COMPINSTTEAMSTATS_PRELIM_GAMES_DRAWN,
	CCDB_COMPINSTTEAMSTATS_PRELIM_BONUS_POINTS,
	CCDB_COMPINSTTEAMSTATS_PRELIM_BYES,
	CCDB_COMPINSTTEAMSTATS_PRELIM_TRIES_SCORED,
	CCDB_COMPINSTTEAMSTATS_PRELIM_TRIES_CONCEDED,
	CCDB_COMPINSTTEAMSTATS_PRELIM_PENALTIES_CONCEDED,
	CCDB_COMPINSTTEAMSTATS_MATCH_POINTS_FOR,
	CCDB_COMPINSTTEAMSTATS_MATCH_POINTS_AGAINST,
	CCDB_COMPINSTTEAMSTATS_MATCH_TRIES_CONCEDED,
	CCDB_COMPINSTTEAMSTATS_MATCH_TRIES_SCORED,
	CCDB_COMPINSTTEAMSTATS_MATCH_PENALTIES_CONCEDED,
	CCDB_COMPINSTTEAMSTATS_PLAYER_SELECTED,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_mode_state
struct rudb_comp_mode_state_row
{
	unsigned short db_id,pad;
	int	game_difficulty;
	int	game_length;
	int	substitution_mode;
	unsigned short	competition_id;
	unsigned short	ranfurly_team_id;
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_mode_state

enum {
	CCDB_COMPMODESTATE_GAME_DIFFICULTY=0,
	CCDB_COMPMODESTATE_GAME_LENGTH,
	CCDB_COMPMODESTATE_SUBSTITUTION_MODE,
	CCDB_COMPMODESTATE_COMPETITION_ID,
	CCDB_COMPMODESTATE_RANFURLY_TEAM_ID,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_trophy
struct rudb_comp_trophy_row
{
	unsigned short db_id,pad;
	char	name[26];
	char	filename[10];
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_trophy

enum {
	CCDB_COMPTROPHY_NAME=0,
	CCDB_COMPTROPHY_FILENAME,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_contract_negotiation
struct rudb_contract_negotiation_row
{
	unsigned short db_id,pad;
	int	category;
	int	value;
	unsigned short	team_id;
	unsigned short	player_id;
	unsigned short	num_attempts;
	unsigned short	days_of_deliberation_left;
	unsigned short	num_seasons;
	unsigned char	result;
	char	row_padding[1];
};

///-----------------------------------------------------------
/// Enums for: rudb_contract_negotiation

enum {
	CCDB_CONTRACTNEG_CATEGORY=0,
	CCDB_CONTRACTNEG_VALUE,
	CCDB_CONTRACTNEG_TEAM_ID,
	CCDB_CONTRACTNEG_PLAYER_ID,
	CCDB_CONTRACTNEG_NUM_ATTEMPTS,
	CCDB_CONTRACTNEG_DAYS_OF_DELIBERATION_LEFT,
	CCDB_CONTRACTNEG_NUM_SEASONS,
	CCDB_CONTRACTNEG_RESULT,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_country
struct rudb_country_row
{
	unsigned short db_id,pad;
	char	name[17];
	char	abbreviation[5];
	char	flag_filename[15];
	char	row_padding[3];
};

///-----------------------------------------------------------
/// Enums for: rudb_country

enum {
	CCDB_COUNTRY_NAME=0,
	CCDB_COUNTRY_ABBREVIATION,
	CCDB_COUNTRY_FLAG_FILENAME,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_emails
struct rudb_emails_row
{
	unsigned short db_id,pad;
	unsigned char	game_mode;
	unsigned char	career_mode;
	char	type[33];
	char	subject[40];
	char	sender[25];
	char	content[44];
};

///-----------------------------------------------------------
/// Enums for: rudb_emails

enum {
	CCDB_EMAILS_GAME_MODE=0,
	CCDB_EMAILS_CAREER_MODE,
	CCDB_EMAILS_TYPE,
	CCDB_EMAILS_SUBJECT,
	CCDB_EMAILS_SENDER,
	CCDB_EMAILS_CONTENT,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_finals_format
struct rudb_finals_format_row
{
	unsigned short db_id,pad;
	char	_enum[24];
	char	name[32];
};

///-----------------------------------------------------------
/// Enums for: rudb_finals_format

enum {
	CCDB_FINALSFORMAT_ENUM=0,
	CCDB_FINALSFORMAT_NAME,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_franchise_constants
struct rudb_franchise_constants_row
{
	unsigned short db_id,pad;
	float	original_club_factor;
	float	rest_injury_susceptability_decrement;
	int	nrl_salary_cap;
	int	nrl_salary_floor;
	int	sl_salary_cap;
	int	sl_salary_floor;
	float	monthly_spam_chance;
	float	base_sponsorship_multiplier;
	int	minimum_monthly_merchandise;
	int	maximum_monthly_merchandise;
	int	base_non_contracted_player_match_fee_amount;
	int	base_stadium_monthly_rental;
	int	base_suspension_fee;
	int	base_medical_fee;
	int	ticket_price;
	float	home_team_ticket_revenue_share;
	float	sim_successful_tackles_variance;
	float	sim_yellow_card_chance;
	float	sim_red_card_chance;
	float	sim_first_injury_chance;
	float	sim_second_injury_chance;
	unsigned short	suspensions_per_season_allowed;
	unsigned short	suspensions_ever_allowed;
	unsigned short	num_matches_in_half_a_season;
	unsigned short	num_matches_to_clear_suspension;
	unsigned short	max_stat_boost_from_training;
	unsigned short	points_lost_per_day;
	unsigned short	add_to_training_constant;
	unsigned short	contract_deliberation_days;
	unsigned short	max_num_contracted_players;
	unsigned short	max_num_years_idle;
	unsigned short	max_num_games_for_ncp;
	unsigned short	sim_min_penalties;
	unsigned short	sim_max_penalties;
	unsigned short	sim_min_tackles;
	unsigned short	sim_max_tackles;
	unsigned short	sim_min_line_breaks;
	unsigned short	sim_max_line_breaks;
	unsigned short	sim_min_offloads;
	unsigned short	sim_max_offloads;
	unsigned short	sim_min_offloads_attempted;
	unsigned short	sim_min_handling_errors;
	unsigned short	sim_max_handling_errors;
	unsigned short	sim_min_kicks;
	unsigned short	sim_max_kicks;
	unsigned short	sim_min_dummy_runs;
	unsigned short	sim_max_dummy_runs;
	unsigned short	sim_min_kick_metres_gained_per_kick;
	unsigned short	sim_max_kick_meters_gained_per_kick;
	unsigned short	sim_min_run_metres_gained;
	unsigned short	sim_max_run_metres_gained;
};

///-----------------------------------------------------------
/// Enums for: rudb_franchise_constants

enum {
	CCDB_FRANCHISECONSTANTS_ORIGINAL_CLUB_FACTOR=0,
	CCDB_FRANCHISECONSTANTS_REST_INJURY_SUSCEPTABILITY_DECREMENT,
	CCDB_FRANCHISECONSTANTS_NRL_SALARY_CAP,
	CCDB_FRANCHISECONSTANTS_NRL_SALARY_FLOOR,
	CCDB_FRANCHISECONSTANTS_SL_SALARY_CAP,
	CCDB_FRANCHISECONSTANTS_SL_SALARY_FLOOR,
	CCDB_FRANCHISECONSTANTS_MONTHLY_SPAM_CHANCE,
	CCDB_FRANCHISECONSTANTS_BASE_SPONSORSHIP_MULTIPLIER,
	CCDB_FRANCHISECONSTANTS_MINIMUM_MONTHLY_MERCHANDISE,
	CCDB_FRANCHISECONSTANTS_MAXIMUM_MONTHLY_MERCHANDISE,
	CCDB_FRANCHISECONSTANTS_BASE_NON_CONTRACTED_PLAYER_MATCH_FEE_AMOUNT,
	CCDB_FRANCHISECONSTANTS_BASE_STADIUM_MONTHLY_RENTAL,
	CCDB_FRANCHISECONSTANTS_BASE_SUSPENSION_FEE,
	CCDB_FRANCHISECONSTANTS_BASE_MEDICAL_FEE,
	CCDB_FRANCHISECONSTANTS_TICKET_PRICE,
	CCDB_FRANCHISECONSTANTS_HOME_TEAM_TICKET_REVENUE_SHARE,
	CCDB_FRANCHISECONSTANTS_SIM_SUCCESSFUL_TACKLES_VARIANCE,
	CCDB_FRANCHISECONSTANTS_SIM_YELLOW_CARD_CHANCE,
	CCDB_FRANCHISECONSTANTS_SIM_RED_CARD_CHANCE,
	CCDB_FRANCHISECONSTANTS_SIM_FIRST_INJURY_CHANCE,
	CCDB_FRANCHISECONSTANTS_SIM_SECOND_INJURY_CHANCE,
	CCDB_FRANCHISECONSTANTS_SUSPENSIONS_PER_SEASON_ALLOWED,
	CCDB_FRANCHISECONSTANTS_SUSPENSIONS_EVER_ALLOWED,
	CCDB_FRANCHISECONSTANTS_NUM_MATCHES_IN_HALF_A_SEASON,
	CCDB_FRANCHISECONSTANTS_NUM_MATCHES_TO_CLEAR_SUSPENSION,
	CCDB_FRANCHISECONSTANTS_MAX_STAT_BOOST_FROM_TRAINING,
	CCDB_FRANCHISECONSTANTS_POINTS_LOST_PER_DAY,
	CCDB_FRANCHISECONSTANTS_ADD_TO_TRAINING_CONSTANT,
	CCDB_FRANCHISECONSTANTS_CONTRACT_DELIBERATION_DAYS,
	CCDB_FRANCHISECONSTANTS_MAX_NUM_CONTRACTED_PLAYERS,
	CCDB_FRANCHISECONSTANTS_MAX_NUM_YEARS_IDLE,
	CCDB_FRANCHISECONSTANTS_MAX_NUM_GAMES_FOR_NCP,
	CCDB_FRANCHISECONSTANTS_SIM_MIN_PENALTIES,
	CCDB_FRANCHISECONSTANTS_SIM_MAX_PENALTIES,
	CCDB_FRANCHISECONSTANTS_SIM_MIN_TACKLES,
	CCDB_FRANCHISECONSTANTS_SIM_MAX_TACKLES,
	CCDB_FRANCHISECONSTANTS_SIM_MIN_LINE_BREAKS,
	CCDB_FRANCHISECONSTANTS_SIM_MAX_LINE_BREAKS,
	CCDB_FRANCHISECONSTANTS_SIM_MIN_OFFLOADS,
	CCDB_FRANCHISECONSTANTS_SIM_MAX_OFFLOADS,
	CCDB_FRANCHISECONSTANTS_SIM_MIN_OFFLOADS_ATTEMPTED,
	CCDB_FRANCHISECONSTANTS_SIM_MIN_HANDLING_ERRORS,
	CCDB_FRANCHISECONSTANTS_SIM_MAX_HANDLING_ERRORS,
	CCDB_FRANCHISECONSTANTS_SIM_MIN_KICKS,
	CCDB_FRANCHISECONSTANTS_SIM_MAX_KICKS,
	CCDB_FRANCHISECONSTANTS_SIM_MIN_DUMMY_RUNS,
	CCDB_FRANCHISECONSTANTS_SIM_MAX_DUMMY_RUNS,
	CCDB_FRANCHISECONSTANTS_SIM_MIN_KICK_METRES_GAINED_PER_KICK,
	CCDB_FRANCHISECONSTANTS_SIM_MAX_KICK_METERS_GAINED_PER_KICK,
	CCDB_FRANCHISECONSTANTS_SIM_MIN_RUN_METRES_GAINED,
	CCDB_FRANCHISECONSTANTS_SIM_MAX_RUN_METRES_GAINED,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_franchise_def
struct rudb_franchise_def_row
{
	unsigned short db_id,pad;
	int	start_date;
	int	salary_cap;
	float	financial_scale;
	char	name[23];
	char	row_padding[1];
};

///-----------------------------------------------------------
/// Enums for: rudb_franchise_def

enum {
	CCDB_FRANCHISEDEF_START_DATE=0,
	CCDB_FRANCHISEDEF_SALARY_CAP,
	CCDB_FRANCHISEDEF_FINANCIAL_SCALE,
	CCDB_FRANCHISEDEF_NAME,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_golden_point_type
struct rudb_golden_point_type_row
{
	unsigned short db_id,pad;
	char	_enum[23];
	char	row_padding[1];
};

///-----------------------------------------------------------
/// Enums for: rudb_golden_point_type

enum {
	CCDB_GOLDENPOINTTYPE_ENUM=0,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_injuries
struct rudb_injuries_row
{
	unsigned short db_id,pad;
	unsigned short	min_days;
	unsigned short	max_days;
	char	description[29];
	char	row_padding[3];
};

///-----------------------------------------------------------
/// Enums for: rudb_injuries

enum {
	CCDB_INJURIES_MIN_DAYS=0,
	CCDB_INJURIES_MAX_DAYS,
	CCDB_INJURIES_DESCRIPTION,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_lineup
struct rudb_lineup_row
{
	unsigned short db_id,pad;
	int	position_id;
	int	value;
	unsigned short	team_id;
	unsigned short	player_id;
	unsigned short	num_seasons;
	char	row_padding[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_lineup

enum {
	CCDB_LINEUP_POSITION_ID=0,
	CCDB_LINEUP_VALUE,
	CCDB_LINEUP_TEAM_ID,
	CCDB_LINEUP_PLAYER_ID,
	CCDB_LINEUP_NUM_SEASONS,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_lineup_new
struct rudb_lineup_new_row
{
	unsigned short db_id,pad;
	int	player1_position;
	int	player2_position;
	int	player3_position;
	int	player4_position;
	int	player5_position;
	int	player6_position;
	int	player7_position;
	int	player8_position;
	int	player9_position;
	int	player10_position;
	int	player11_position;
	int	player12_position;
	int	player13_position;
	int	player14_position;
	int	player15_position;
	int	player16_position;
	int	player17_position;
	int	player18_position;
	int	player19_position;
	int	player20_position;
	int	player21_position;
	int	player22_position;
	int	player23_position;
	int	player24_position;
	int	player25_position;
	int	player26_position;
	int	player27_position;
	int	player28_position;
	int	player29_position;
	int	player30_position;
	int	player31_position;
	int	player32_position;
	int	player33_position;
	int	player34_position;
	int	player35_position;
	int	player36_position;
	int	player37_position;
	int	player38_position;
	int	player39_position;
	int	player40_position;
	int	player41_position;
	int	player42_position;
	int	player43_position;
	int	player44_position;
	int	player45_position;
	int	player46_position;
	int	player47_position;
	int	player48_position;
	int	player49_position;
	int	player50_position;
	int	player51_position;
	int	player52_position;
	int	player53_position;
	int	player54_position;
	int	player55_position;
	int	player56_position;
	int	player57_position;
	int	player58_position;
	int	player59_position;
	int	player60_position;
	int	player61_position;
	int	player62_position;
	int	player63_position;
	int	player64_position;
	int	player65_position;
	int	player66_position;
	int	player67_position;
	int	player68_position;
	int	player69_position;
	int	player70_position;
	int	player1_value;
	int	player2_value;
	int	player3_value;
	int	player4_value;
	int	player5_value;
	int	player6_value;
	int	player7_value;
	int	player8_value;
	int	player9_value;
	int	player10_value;
	int	player11_value;
	int	player12_value;
	int	player13_value;
	int	player14_value;
	int	player15_value;
	int	player16_value;
	int	player17_value;
	int	player18_value;
	int	player19_value;
	int	player20_value;
	int	player21_value;
	int	player22_value;
	int	player23_value;
	int	player24_value;
	int	player25_value;
	int	player26_value;
	int	player27_value;
	int	player28_value;
	int	player29_value;
	int	player30_value;
	int	player31_value;
	int	player32_value;
	int	player33_value;
	int	player34_value;
	int	player35_value;
	int	player36_value;
	int	player37_value;
	int	player38_value;
	int	player39_value;
	int	player40_value;
	int	player41_value;
	int	player42_value;
	int	player43_value;
	int	player44_value;
	int	player45_value;
	int	player46_value;
	int	player47_value;
	int	player48_value;
	int	player49_value;
	int	player50_value;
	int	player51_value;
	int	player52_value;
	int	player53_value;
	int	player54_value;
	int	player55_value;
	int	player56_value;
	int	player57_value;
	int	player58_value;
	int	player59_value;
	int	player60_value;
	int	player61_value;
	int	player62_value;
	int	player63_value;
	int	player64_value;
	int	player65_value;
	int	player66_value;
	int	player67_value;
	int	player68_value;
	int	player69_value;
	int	player70_value;
	unsigned short	player1_id;
	unsigned short	player2_id;
	unsigned short	player3_id;
	unsigned short	player4_id;
	unsigned short	player5_id;
	unsigned short	player6_id;
	unsigned short	player7_id;
	unsigned short	player8_id;
	unsigned short	player9_id;
	unsigned short	player10_id;
	unsigned short	player11_id;
	unsigned short	player12_id;
	unsigned short	player13_id;
	unsigned short	player14_id;
	unsigned short	player15_id;
	unsigned short	player16_id;
	unsigned short	player17_id;
	unsigned short	player18_id;
	unsigned short	player19_id;
	unsigned short	player20_id;
	unsigned short	player21_id;
	unsigned short	player22_id;
	unsigned short	player23_id;
	unsigned short	player24_id;
	unsigned short	player25_id;
	unsigned short	player26_id;
	unsigned short	player27_id;
	unsigned short	player28_id;
	unsigned short	player29_id;
	unsigned short	player30_id;
	unsigned short	player31_id;
	unsigned short	player32_id;
	unsigned short	player33_id;
	unsigned short	player34_id;
	unsigned short	player35_id;
	unsigned short	player36_id;
	unsigned short	player37_id;
	unsigned short	player38_id;
	unsigned short	player39_id;
	unsigned short	player40_id;
	unsigned short	player41_id;
	unsigned short	player42_id;
	unsigned short	player43_id;
	unsigned short	player44_id;
	unsigned short	player45_id;
	unsigned short	player46_id;
	unsigned short	player47_id;
	unsigned short	player48_id;
	unsigned short	player49_id;
	unsigned short	player50_id;
	unsigned short	player51_id;
	unsigned short	player52_id;
	unsigned short	player53_id;
	unsigned short	player54_id;
	unsigned short	player55_id;
	unsigned short	player56_id;
	unsigned short	player57_id;
	unsigned short	player58_id;
	unsigned short	player59_id;
	unsigned short	player60_id;
	unsigned short	player61_id;
	unsigned short	player62_id;
	unsigned short	player63_id;
	unsigned short	player64_id;
	unsigned short	player65_id;
	unsigned short	player66_id;
	unsigned short	player67_id;
	unsigned short	player68_id;
	unsigned short	player69_id;
	unsigned short	player70_id;
	unsigned char	player1_seasons;
	unsigned char	player2_seasons;
	unsigned char	player3_seasons;
	unsigned char	player4_seasons;
	unsigned char	player5_seasons;
	unsigned char	player6_seasons;
	unsigned char	player7_seasons;
	unsigned char	player8_seasons;
	unsigned char	player9_seasons;
	unsigned char	player10_seasons;
	unsigned char	player11_seasons;
	unsigned char	player12_seasons;
	unsigned char	player13_seasons;
	unsigned char	player14_seasons;
	unsigned char	player15_seasons;
	unsigned char	player16_seasons;
	unsigned char	player17_seasons;
	unsigned char	player18_seasons;
	unsigned char	player19_seasons;
	unsigned char	player20_seasons;
	unsigned char	player21_seasons;
	unsigned char	player22_seasons;
	unsigned char	player23_seasons;
	unsigned char	player24_seasons;
	unsigned char	player25_seasons;
	unsigned char	player26_seasons;
	unsigned char	player27_seasons;
	unsigned char	player28_seasons;
	unsigned char	player29_seasons;
	unsigned char	player30_seasons;
	unsigned char	player31_seasons;
	unsigned char	player32_seasons;
	unsigned char	player33_seasons;
	unsigned char	player34_seasons;
	unsigned char	player35_seasons;
	unsigned char	player36_seasons;
	unsigned char	player37_seasons;
	unsigned char	player38_seasons;
	unsigned char	player39_seasons;
	unsigned char	player40_seasons;
	unsigned char	player41_seasons;
	unsigned char	player42_seasons;
	unsigned char	player43_seasons;
	unsigned char	player44_seasons;
	unsigned char	player45_seasons;
	unsigned char	player46_seasons;
	unsigned char	player47_seasons;
	unsigned char	player48_seasons;
	unsigned char	player49_seasons;
	unsigned char	player50_seasons;
	unsigned char	player51_seasons;
	unsigned char	player52_seasons;
	unsigned char	player53_seasons;
	unsigned char	player54_seasons;
	unsigned char	player55_seasons;
	unsigned char	player56_seasons;
	unsigned char	player57_seasons;
	unsigned char	player58_seasons;
	unsigned char	player59_seasons;
	unsigned char	player60_seasons;
	unsigned char	player61_seasons;
	unsigned char	player62_seasons;
	unsigned char	player63_seasons;
	unsigned char	player64_seasons;
	unsigned char	player65_seasons;
	unsigned char	player66_seasons;
	unsigned char	player67_seasons;
	unsigned char	player68_seasons;
	unsigned char	player69_seasons;
	unsigned char	player70_seasons;
	char	row_padding[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_lineup_new

enum {
	CCDB_LINEUPNEW_PLAYER1_POSITION=0,
	CCDB_LINEUPNEW_PLAYER2_POSITION,
	CCDB_LINEUPNEW_PLAYER3_POSITION,
	CCDB_LINEUPNEW_PLAYER4_POSITION,
	CCDB_LINEUPNEW_PLAYER5_POSITION,
	CCDB_LINEUPNEW_PLAYER6_POSITION,
	CCDB_LINEUPNEW_PLAYER7_POSITION,
	CCDB_LINEUPNEW_PLAYER8_POSITION,
	CCDB_LINEUPNEW_PLAYER9_POSITION,
	CCDB_LINEUPNEW_PLAYER10_POSITION,
	CCDB_LINEUPNEW_PLAYER11_POSITION,
	CCDB_LINEUPNEW_PLAYER12_POSITION,
	CCDB_LINEUPNEW_PLAYER13_POSITION,
	CCDB_LINEUPNEW_PLAYER14_POSITION,
	CCDB_LINEUPNEW_PLAYER15_POSITION,
	CCDB_LINEUPNEW_PLAYER16_POSITION,
	CCDB_LINEUPNEW_PLAYER17_POSITION,
	CCDB_LINEUPNEW_PLAYER18_POSITION,
	CCDB_LINEUPNEW_PLAYER19_POSITION,
	CCDB_LINEUPNEW_PLAYER20_POSITION,
	CCDB_LINEUPNEW_PLAYER21_POSITION,
	CCDB_LINEUPNEW_PLAYER22_POSITION,
	CCDB_LINEUPNEW_PLAYER23_POSITION,
	CCDB_LINEUPNEW_PLAYER24_POSITION,
	CCDB_LINEUPNEW_PLAYER25_POSITION,
	CCDB_LINEUPNEW_PLAYER26_POSITION,
	CCDB_LINEUPNEW_PLAYER27_POSITION,
	CCDB_LINEUPNEW_PLAYER28_POSITION,
	CCDB_LINEUPNEW_PLAYER29_POSITION,
	CCDB_LINEUPNEW_PLAYER30_POSITION,
	CCDB_LINEUPNEW_PLAYER31_POSITION,
	CCDB_LINEUPNEW_PLAYER32_POSITION,
	CCDB_LINEUPNEW_PLAYER33_POSITION,
	CCDB_LINEUPNEW_PLAYER34_POSITION,
	CCDB_LINEUPNEW_PLAYER35_POSITION,
	CCDB_LINEUPNEW_PLAYER36_POSITION,
	CCDB_LINEUPNEW_PLAYER37_POSITION,
	CCDB_LINEUPNEW_PLAYER38_POSITION,
	CCDB_LINEUPNEW_PLAYER39_POSITION,
	CCDB_LINEUPNEW_PLAYER40_POSITION,
	CCDB_LINEUPNEW_PLAYER41_POSITION,
	CCDB_LINEUPNEW_PLAYER42_POSITION,
	CCDB_LINEUPNEW_PLAYER43_POSITION,
	CCDB_LINEUPNEW_PLAYER44_POSITION,
	CCDB_LINEUPNEW_PLAYER45_POSITION,
	CCDB_LINEUPNEW_PLAYER46_POSITION,
	CCDB_LINEUPNEW_PLAYER47_POSITION,
	CCDB_LINEUPNEW_PLAYER48_POSITION,
	CCDB_LINEUPNEW_PLAYER49_POSITION,
	CCDB_LINEUPNEW_PLAYER50_POSITION,
	CCDB_LINEUPNEW_PLAYER51_POSITION,
	CCDB_LINEUPNEW_PLAYER52_POSITION,
	CCDB_LINEUPNEW_PLAYER53_POSITION,
	CCDB_LINEUPNEW_PLAYER54_POSITION,
	CCDB_LINEUPNEW_PLAYER55_POSITION,
	CCDB_LINEUPNEW_PLAYER56_POSITION,
	CCDB_LINEUPNEW_PLAYER57_POSITION,
	CCDB_LINEUPNEW_PLAYER58_POSITION,
	CCDB_LINEUPNEW_PLAYER59_POSITION,
	CCDB_LINEUPNEW_PLAYER60_POSITION,
	CCDB_LINEUPNEW_PLAYER61_POSITION,
	CCDB_LINEUPNEW_PLAYER62_POSITION,
	CCDB_LINEUPNEW_PLAYER63_POSITION,
	CCDB_LINEUPNEW_PLAYER64_POSITION,
	CCDB_LINEUPNEW_PLAYER65_POSITION,
	CCDB_LINEUPNEW_PLAYER66_POSITION,
	CCDB_LINEUPNEW_PLAYER67_POSITION,
	CCDB_LINEUPNEW_PLAYER68_POSITION,
	CCDB_LINEUPNEW_PLAYER69_POSITION,
	CCDB_LINEUPNEW_PLAYER70_POSITION,
	CCDB_LINEUPNEW_PLAYER1_VALUE,
	CCDB_LINEUPNEW_PLAYER2_VALUE,
	CCDB_LINEUPNEW_PLAYER3_VALUE,
	CCDB_LINEUPNEW_PLAYER4_VALUE,
	CCDB_LINEUPNEW_PLAYER5_VALUE,
	CCDB_LINEUPNEW_PLAYER6_VALUE,
	CCDB_LINEUPNEW_PLAYER7_VALUE,
	CCDB_LINEUPNEW_PLAYER8_VALUE,
	CCDB_LINEUPNEW_PLAYER9_VALUE,
	CCDB_LINEUPNEW_PLAYER10_VALUE,
	CCDB_LINEUPNEW_PLAYER11_VALUE,
	CCDB_LINEUPNEW_PLAYER12_VALUE,
	CCDB_LINEUPNEW_PLAYER13_VALUE,
	CCDB_LINEUPNEW_PLAYER14_VALUE,
	CCDB_LINEUPNEW_PLAYER15_VALUE,
	CCDB_LINEUPNEW_PLAYER16_VALUE,
	CCDB_LINEUPNEW_PLAYER17_VALUE,
	CCDB_LINEUPNEW_PLAYER18_VALUE,
	CCDB_LINEUPNEW_PLAYER19_VALUE,
	CCDB_LINEUPNEW_PLAYER20_VALUE,
	CCDB_LINEUPNEW_PLAYER21_VALUE,
	CCDB_LINEUPNEW_PLAYER22_VALUE,
	CCDB_LINEUPNEW_PLAYER23_VALUE,
	CCDB_LINEUPNEW_PLAYER24_VALUE,
	CCDB_LINEUPNEW_PLAYER25_VALUE,
	CCDB_LINEUPNEW_PLAYER26_VALUE,
	CCDB_LINEUPNEW_PLAYER27_VALUE,
	CCDB_LINEUPNEW_PLAYER28_VALUE,
	CCDB_LINEUPNEW_PLAYER29_VALUE,
	CCDB_LINEUPNEW_PLAYER30_VALUE,
	CCDB_LINEUPNEW_PLAYER31_VALUE,
	CCDB_LINEUPNEW_PLAYER32_VALUE,
	CCDB_LINEUPNEW_PLAYER33_VALUE,
	CCDB_LINEUPNEW_PLAYER34_VALUE,
	CCDB_LINEUPNEW_PLAYER35_VALUE,
	CCDB_LINEUPNEW_PLAYER36_VALUE,
	CCDB_LINEUPNEW_PLAYER37_VALUE,
	CCDB_LINEUPNEW_PLAYER38_VALUE,
	CCDB_LINEUPNEW_PLAYER39_VALUE,
	CCDB_LINEUPNEW_PLAYER40_VALUE,
	CCDB_LINEUPNEW_PLAYER41_VALUE,
	CCDB_LINEUPNEW_PLAYER42_VALUE,
	CCDB_LINEUPNEW_PLAYER43_VALUE,
	CCDB_LINEUPNEW_PLAYER44_VALUE,
	CCDB_LINEUPNEW_PLAYER45_VALUE,
	CCDB_LINEUPNEW_PLAYER46_VALUE,
	CCDB_LINEUPNEW_PLAYER47_VALUE,
	CCDB_LINEUPNEW_PLAYER48_VALUE,
	CCDB_LINEUPNEW_PLAYER49_VALUE,
	CCDB_LINEUPNEW_PLAYER50_VALUE,
	CCDB_LINEUPNEW_PLAYER51_VALUE,
	CCDB_LINEUPNEW_PLAYER52_VALUE,
	CCDB_LINEUPNEW_PLAYER53_VALUE,
	CCDB_LINEUPNEW_PLAYER54_VALUE,
	CCDB_LINEUPNEW_PLAYER55_VALUE,
	CCDB_LINEUPNEW_PLAYER56_VALUE,
	CCDB_LINEUPNEW_PLAYER57_VALUE,
	CCDB_LINEUPNEW_PLAYER58_VALUE,
	CCDB_LINEUPNEW_PLAYER59_VALUE,
	CCDB_LINEUPNEW_PLAYER60_VALUE,
	CCDB_LINEUPNEW_PLAYER61_VALUE,
	CCDB_LINEUPNEW_PLAYER62_VALUE,
	CCDB_LINEUPNEW_PLAYER63_VALUE,
	CCDB_LINEUPNEW_PLAYER64_VALUE,
	CCDB_LINEUPNEW_PLAYER65_VALUE,
	CCDB_LINEUPNEW_PLAYER66_VALUE,
	CCDB_LINEUPNEW_PLAYER67_VALUE,
	CCDB_LINEUPNEW_PLAYER68_VALUE,
	CCDB_LINEUPNEW_PLAYER69_VALUE,
	CCDB_LINEUPNEW_PLAYER70_VALUE,
	CCDB_LINEUPNEW_PLAYER1_ID,
	CCDB_LINEUPNEW_PLAYER2_ID,
	CCDB_LINEUPNEW_PLAYER3_ID,
	CCDB_LINEUPNEW_PLAYER4_ID,
	CCDB_LINEUPNEW_PLAYER5_ID,
	CCDB_LINEUPNEW_PLAYER6_ID,
	CCDB_LINEUPNEW_PLAYER7_ID,
	CCDB_LINEUPNEW_PLAYER8_ID,
	CCDB_LINEUPNEW_PLAYER9_ID,
	CCDB_LINEUPNEW_PLAYER10_ID,
	CCDB_LINEUPNEW_PLAYER11_ID,
	CCDB_LINEUPNEW_PLAYER12_ID,
	CCDB_LINEUPNEW_PLAYER13_ID,
	CCDB_LINEUPNEW_PLAYER14_ID,
	CCDB_LINEUPNEW_PLAYER15_ID,
	CCDB_LINEUPNEW_PLAYER16_ID,
	CCDB_LINEUPNEW_PLAYER17_ID,
	CCDB_LINEUPNEW_PLAYER18_ID,
	CCDB_LINEUPNEW_PLAYER19_ID,
	CCDB_LINEUPNEW_PLAYER20_ID,
	CCDB_LINEUPNEW_PLAYER21_ID,
	CCDB_LINEUPNEW_PLAYER22_ID,
	CCDB_LINEUPNEW_PLAYER23_ID,
	CCDB_LINEUPNEW_PLAYER24_ID,
	CCDB_LINEUPNEW_PLAYER25_ID,
	CCDB_LINEUPNEW_PLAYER26_ID,
	CCDB_LINEUPNEW_PLAYER27_ID,
	CCDB_LINEUPNEW_PLAYER28_ID,
	CCDB_LINEUPNEW_PLAYER29_ID,
	CCDB_LINEUPNEW_PLAYER30_ID,
	CCDB_LINEUPNEW_PLAYER31_ID,
	CCDB_LINEUPNEW_PLAYER32_ID,
	CCDB_LINEUPNEW_PLAYER33_ID,
	CCDB_LINEUPNEW_PLAYER34_ID,
	CCDB_LINEUPNEW_PLAYER35_ID,
	CCDB_LINEUPNEW_PLAYER36_ID,
	CCDB_LINEUPNEW_PLAYER37_ID,
	CCDB_LINEUPNEW_PLAYER38_ID,
	CCDB_LINEUPNEW_PLAYER39_ID,
	CCDB_LINEUPNEW_PLAYER40_ID,
	CCDB_LINEUPNEW_PLAYER41_ID,
	CCDB_LINEUPNEW_PLAYER42_ID,
	CCDB_LINEUPNEW_PLAYER43_ID,
	CCDB_LINEUPNEW_PLAYER44_ID,
	CCDB_LINEUPNEW_PLAYER45_ID,
	CCDB_LINEUPNEW_PLAYER46_ID,
	CCDB_LINEUPNEW_PLAYER47_ID,
	CCDB_LINEUPNEW_PLAYER48_ID,
	CCDB_LINEUPNEW_PLAYER49_ID,
	CCDB_LINEUPNEW_PLAYER50_ID,
	CCDB_LINEUPNEW_PLAYER51_ID,
	CCDB_LINEUPNEW_PLAYER52_ID,
	CCDB_LINEUPNEW_PLAYER53_ID,
	CCDB_LINEUPNEW_PLAYER54_ID,
	CCDB_LINEUPNEW_PLAYER55_ID,
	CCDB_LINEUPNEW_PLAYER56_ID,
	CCDB_LINEUPNEW_PLAYER57_ID,
	CCDB_LINEUPNEW_PLAYER58_ID,
	CCDB_LINEUPNEW_PLAYER59_ID,
	CCDB_LINEUPNEW_PLAYER60_ID,
	CCDB_LINEUPNEW_PLAYER61_ID,
	CCDB_LINEUPNEW_PLAYER62_ID,
	CCDB_LINEUPNEW_PLAYER63_ID,
	CCDB_LINEUPNEW_PLAYER64_ID,
	CCDB_LINEUPNEW_PLAYER65_ID,
	CCDB_LINEUPNEW_PLAYER66_ID,
	CCDB_LINEUPNEW_PLAYER67_ID,
	CCDB_LINEUPNEW_PLAYER68_ID,
	CCDB_LINEUPNEW_PLAYER69_ID,
	CCDB_LINEUPNEW_PLAYER70_ID,
	CCDB_LINEUPNEW_PLAYER1_SEASONS,
	CCDB_LINEUPNEW_PLAYER2_SEASONS,
	CCDB_LINEUPNEW_PLAYER3_SEASONS,
	CCDB_LINEUPNEW_PLAYER4_SEASONS,
	CCDB_LINEUPNEW_PLAYER5_SEASONS,
	CCDB_LINEUPNEW_PLAYER6_SEASONS,
	CCDB_LINEUPNEW_PLAYER7_SEASONS,
	CCDB_LINEUPNEW_PLAYER8_SEASONS,
	CCDB_LINEUPNEW_PLAYER9_SEASONS,
	CCDB_LINEUPNEW_PLAYER10_SEASONS,
	CCDB_LINEUPNEW_PLAYER11_SEASONS,
	CCDB_LINEUPNEW_PLAYER12_SEASONS,
	CCDB_LINEUPNEW_PLAYER13_SEASONS,
	CCDB_LINEUPNEW_PLAYER14_SEASONS,
	CCDB_LINEUPNEW_PLAYER15_SEASONS,
	CCDB_LINEUPNEW_PLAYER16_SEASONS,
	CCDB_LINEUPNEW_PLAYER17_SEASONS,
	CCDB_LINEUPNEW_PLAYER18_SEASONS,
	CCDB_LINEUPNEW_PLAYER19_SEASONS,
	CCDB_LINEUPNEW_PLAYER20_SEASONS,
	CCDB_LINEUPNEW_PLAYER21_SEASONS,
	CCDB_LINEUPNEW_PLAYER22_SEASONS,
	CCDB_LINEUPNEW_PLAYER23_SEASONS,
	CCDB_LINEUPNEW_PLAYER24_SEASONS,
	CCDB_LINEUPNEW_PLAYER25_SEASONS,
	CCDB_LINEUPNEW_PLAYER26_SEASONS,
	CCDB_LINEUPNEW_PLAYER27_SEASONS,
	CCDB_LINEUPNEW_PLAYER28_SEASONS,
	CCDB_LINEUPNEW_PLAYER29_SEASONS,
	CCDB_LINEUPNEW_PLAYER30_SEASONS,
	CCDB_LINEUPNEW_PLAYER31_SEASONS,
	CCDB_LINEUPNEW_PLAYER32_SEASONS,
	CCDB_LINEUPNEW_PLAYER33_SEASONS,
	CCDB_LINEUPNEW_PLAYER34_SEASONS,
	CCDB_LINEUPNEW_PLAYER35_SEASONS,
	CCDB_LINEUPNEW_PLAYER36_SEASONS,
	CCDB_LINEUPNEW_PLAYER37_SEASONS,
	CCDB_LINEUPNEW_PLAYER38_SEASONS,
	CCDB_LINEUPNEW_PLAYER39_SEASONS,
	CCDB_LINEUPNEW_PLAYER40_SEASONS,
	CCDB_LINEUPNEW_PLAYER41_SEASONS,
	CCDB_LINEUPNEW_PLAYER42_SEASONS,
	CCDB_LINEUPNEW_PLAYER43_SEASONS,
	CCDB_LINEUPNEW_PLAYER44_SEASONS,
	CCDB_LINEUPNEW_PLAYER45_SEASONS,
	CCDB_LINEUPNEW_PLAYER46_SEASONS,
	CCDB_LINEUPNEW_PLAYER47_SEASONS,
	CCDB_LINEUPNEW_PLAYER48_SEASONS,
	CCDB_LINEUPNEW_PLAYER49_SEASONS,
	CCDB_LINEUPNEW_PLAYER50_SEASONS,
	CCDB_LINEUPNEW_PLAYER51_SEASONS,
	CCDB_LINEUPNEW_PLAYER52_SEASONS,
	CCDB_LINEUPNEW_PLAYER53_SEASONS,
	CCDB_LINEUPNEW_PLAYER54_SEASONS,
	CCDB_LINEUPNEW_PLAYER55_SEASONS,
	CCDB_LINEUPNEW_PLAYER56_SEASONS,
	CCDB_LINEUPNEW_PLAYER57_SEASONS,
	CCDB_LINEUPNEW_PLAYER58_SEASONS,
	CCDB_LINEUPNEW_PLAYER59_SEASONS,
	CCDB_LINEUPNEW_PLAYER60_SEASONS,
	CCDB_LINEUPNEW_PLAYER61_SEASONS,
	CCDB_LINEUPNEW_PLAYER62_SEASONS,
	CCDB_LINEUPNEW_PLAYER63_SEASONS,
	CCDB_LINEUPNEW_PLAYER64_SEASONS,
	CCDB_LINEUPNEW_PLAYER65_SEASONS,
	CCDB_LINEUPNEW_PLAYER66_SEASONS,
	CCDB_LINEUPNEW_PLAYER67_SEASONS,
	CCDB_LINEUPNEW_PLAYER68_SEASONS,
	CCDB_LINEUPNEW_PLAYER69_SEASONS,
	CCDB_LINEUPNEW_PLAYER70_SEASONS,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_pending_lineup
struct rudb_pending_lineup_row
{
	unsigned short db_id,pad;
	int	value;
	unsigned short	team_id;
	unsigned short	player_id;
	unsigned short	num_seasons;
	unsigned char	position;
	char	row_padding[1];
};

///-----------------------------------------------------------
/// Enums for: rudb_pending_lineup

enum {
	CCDB_PENDINGLINEUP_VALUE=0,
	CCDB_PENDINGLINEUP_TEAM_ID,
	CCDB_PENDINGLINEUP_PLAYER_ID,
	CCDB_PENDINGLINEUP_NUM_SEASONS,
	CCDB_PENDINGLINEUP_POSITION,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_pending_lineup_new
struct rudb_pending_lineup_new_row
{
	unsigned short db_id,pad;
	int	player1_position;
	int	player2_position;
	int	player3_position;
	int	player4_position;
	int	player5_position;
	int	player6_position;
	int	player7_position;
	int	player8_position;
	int	player9_position;
	int	player10_position;
	int	player11_position;
	int	player12_position;
	int	player13_position;
	int	player14_position;
	int	player15_position;
	int	player16_position;
	int	player17_position;
	int	player18_position;
	int	player19_position;
	int	player20_position;
	int	player21_position;
	int	player22_position;
	int	player23_position;
	int	player24_position;
	int	player25_position;
	int	player26_position;
	int	player27_position;
	int	player28_position;
	int	player29_position;
	int	player30_position;
	int	player31_position;
	int	player32_position;
	int	player33_position;
	int	player34_position;
	int	player35_position;
	int	player36_position;
	int	player37_position;
	int	player38_position;
	int	player39_position;
	int	player40_position;
	int	player1_value;
	int	player2_value;
	int	player3_value;
	int	player4_value;
	int	player5_value;
	int	player6_value;
	int	player7_value;
	int	player8_value;
	int	player9_value;
	int	player10_value;
	int	player11_value;
	int	player12_value;
	int	player13_value;
	int	player14_value;
	int	player15_value;
	int	player16_value;
	int	player17_value;
	int	player18_value;
	int	player19_value;
	int	player20_value;
	int	player21_value;
	int	player22_value;
	int	player23_value;
	int	player24_value;
	int	player25_value;
	int	player26_value;
	int	player27_value;
	int	player28_value;
	int	player29_value;
	int	player30_value;
	int	player31_value;
	int	player32_value;
	int	player33_value;
	int	player34_value;
	int	player35_value;
	int	player36_value;
	int	player37_value;
	int	player38_value;
	int	player39_value;
	int	player40_value;
	unsigned short	player1_id;
	unsigned short	player2_id;
	unsigned short	player3_id;
	unsigned short	player4_id;
	unsigned short	player5_id;
	unsigned short	player6_id;
	unsigned short	player7_id;
	unsigned short	player8_id;
	unsigned short	player9_id;
	unsigned short	player10_id;
	unsigned short	player11_id;
	unsigned short	player12_id;
	unsigned short	player13_id;
	unsigned short	player14_id;
	unsigned short	player15_id;
	unsigned short	player16_id;
	unsigned short	player17_id;
	unsigned short	player18_id;
	unsigned short	player19_id;
	unsigned short	player20_id;
	unsigned short	player21_id;
	unsigned short	player22_id;
	unsigned short	player23_id;
	unsigned short	player24_id;
	unsigned short	player25_id;
	unsigned short	player26_id;
	unsigned short	player27_id;
	unsigned short	player28_id;
	unsigned short	player29_id;
	unsigned short	player30_id;
	unsigned short	player31_id;
	unsigned short	player32_id;
	unsigned short	player33_id;
	unsigned short	player34_id;
	unsigned short	player35_id;
	unsigned short	player36_id;
	unsigned short	player37_id;
	unsigned short	player38_id;
	unsigned short	player39_id;
	unsigned short	player40_id;
	unsigned char	player1_seasons;
	unsigned char	player2_seasons;
	unsigned char	player3_seasons;
	unsigned char	player4_seasons;
	unsigned char	player5_seasons;
	unsigned char	player6_seasons;
	unsigned char	player7_seasons;
	unsigned char	player8_seasons;
	unsigned char	player9_seasons;
	unsigned char	player10_seasons;
	unsigned char	player11_seasons;
	unsigned char	player12_seasons;
	unsigned char	player13_seasons;
	unsigned char	player14_seasons;
	unsigned char	player15_seasons;
	unsigned char	player16_seasons;
	unsigned char	player17_seasons;
	unsigned char	player18_seasons;
	unsigned char	player19_seasons;
	unsigned char	player20_seasons;
	unsigned char	player21_seasons;
	unsigned char	player22_seasons;
	unsigned char	player23_seasons;
	unsigned char	player24_seasons;
	unsigned char	player25_seasons;
	unsigned char	player26_seasons;
	unsigned char	player27_seasons;
	unsigned char	player28_seasons;
	unsigned char	player29_seasons;
	unsigned char	player30_seasons;
	unsigned char	player31_seasons;
	unsigned char	player32_seasons;
	unsigned char	player33_seasons;
	unsigned char	player34_seasons;
	unsigned char	player35_seasons;
	unsigned char	player36_seasons;
	unsigned char	player37_seasons;
	unsigned char	player38_seasons;
	unsigned char	player39_seasons;
	unsigned char	player40_seasons;
};

///-----------------------------------------------------------
/// Enums for: rudb_pending_lineup_new

enum {
	CCDB_PENDINGLINEUPNEW_PLAYER1_POSITION=0,
	CCDB_PENDINGLINEUPNEW_PLAYER2_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER3_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER4_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER5_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER6_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER7_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER8_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER9_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER10_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER11_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER12_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER13_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER14_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER15_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER16_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER17_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER18_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER19_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER20_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER21_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER22_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER23_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER24_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER25_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER26_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER27_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER28_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER29_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER30_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER31_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER32_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER33_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER34_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER35_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER36_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER37_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER38_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER39_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER40_POSITION,
	CCDB_PENDINGLINEUPNEW_PLAYER1_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER2_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER3_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER4_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER5_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER6_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER7_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER8_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER9_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER10_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER11_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER12_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER13_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER14_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER15_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER16_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER17_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER18_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER19_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER20_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER21_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER22_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER23_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER24_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER25_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER26_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER27_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER28_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER29_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER30_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER31_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER32_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER33_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER34_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER35_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER36_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER37_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER38_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER39_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER40_VALUE,
	CCDB_PENDINGLINEUPNEW_PLAYER1_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER2_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER3_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER4_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER5_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER6_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER7_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER8_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER9_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER10_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER11_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER12_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER13_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER14_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER15_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER16_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER17_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER18_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER19_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER20_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER21_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER22_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER23_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER24_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER25_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER26_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER27_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER28_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER29_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER30_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER31_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER32_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER33_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER34_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER35_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER36_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER37_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER38_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER39_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER40_ID,
	CCDB_PENDINGLINEUPNEW_PLAYER1_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER2_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER3_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER4_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER5_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER6_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER7_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER8_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER9_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER10_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER11_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER12_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER13_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER14_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER15_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER16_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER17_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER18_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER19_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER20_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER21_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER22_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER23_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER24_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER25_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER26_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER27_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER28_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER29_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER30_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER31_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER32_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER33_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER34_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER35_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER36_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER37_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER38_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER39_SEASONS,
	CCDB_PENDINGLINEUPNEW_PLAYER40_SEASONS,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_player_extra
struct rudb_player_extra_row
{
	unsigned short db_id,pad;
	unsigned short	club_team;
	unsigned short	last_club;
	unsigned short	pending_team;
	unsigned short	num_days_injured;
	unsigned short	num_matches_unsuspended;
	unsigned short	suspensions_total;
	unsigned short	suspensions_this_season;
	unsigned short	international_team;
	unsigned short	alternate_club_team;
	unsigned short	market_value;
	unsigned short	replaced_player;
	unsigned char	years_idle;
	unsigned char	contract_pending;
	unsigned char	frustration_level;
	unsigned char	has_been_frustrated;
	unsigned char	send_last_chance_email;
	unsigned char	has_sent_last_chance_email;
	unsigned char	ai_trading_suspended_days;
	unsigned char	num_first_grade_games_last_season;
	unsigned char	days_to_wait;
	unsigned char	injury;
	unsigned char	suspension;
	unsigned char	num_matches_suspended;
	char	row_padding[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_player_extra

enum {
	CCDB_PLREXTRA_CLUB_TEAM=0,
	CCDB_PLREXTRA_LAST_CLUB,
	CCDB_PLREXTRA_PENDING_TEAM,
	CCDB_PLREXTRA_NUM_DAYS_INJURED,
	CCDB_PLREXTRA_NUM_MATCHES_UNSUSPENDED,
	CCDB_PLREXTRA_SUSPENSIONS_TOTAL,
	CCDB_PLREXTRA_SUSPENSIONS_THIS_SEASON,
	CCDB_PLREXTRA_INTERNATIONAL_TEAM,
	CCDB_PLREXTRA_ALTERNATE_CLUB_TEAM,
	CCDB_PLREXTRA_MARKET_VALUE,
	CCDB_PLREXTRA_REPLACED_PLAYER,
	CCDB_PLREXTRA_YEARS_IDLE,
	CCDB_PLREXTRA_CONTRACT_PENDING,
	CCDB_PLREXTRA_FRUSTRATION_LEVEL,
	CCDB_PLREXTRA_HAS_BEEN_FRUSTRATED,
	CCDB_PLREXTRA_SEND_LAST_CHANCE_EMAIL,
	CCDB_PLREXTRA_HAS_SENT_LAST_CHANCE_EMAIL,
	CCDB_PLREXTRA_AI_TRADING_SUSPENDED_DAYS,
	CCDB_PLREXTRA_NUM_FIRST_GRADE_GAMES_LAST_SEASON,
	CCDB_PLREXTRA_DAYS_TO_WAIT,
	CCDB_PLREXTRA_INJURY,
	CCDB_PLREXTRA_SUSPENSION,
	CCDB_PLREXTRA_NUM_MATCHES_SUSPENDED,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_player_link
struct rudb_player_link_row
{
	unsigned short db_id,pad;
	unsigned short	original_player;
	unsigned short	linked_player;
};

///-----------------------------------------------------------
/// Enums for: rudb_player_link

enum {
	CCDB_PLAYERLINK_ORIGINAL_PLAYER=0,
	CCDB_PLAYERLINK_LINKED_PLAYER,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_player_performance_values
struct rudb_player_performance_values_row
{
	unsigned short db_id,pad;
	int	try_cap;
	int	try_val;
	int	running_meters_cap;
	int	running_meters_val;
	int	kicking_meters_cap;
	int	kicking_meters_val;
	int	tackles_cap;
	int	tackles_val;
	int	line_breaks_cap;
	int	line_breaks_val;
	int	offloads_cap;
	int	offloads_val;
	int	handling_errors_cap;
	int	handling_errors_val;
	int	penalties_conceded_cap;
	int	penalties_conceded_val;
	int	miss_tackles_cap;
	int	miss_tackles_val;
	int	yellow_card_cap;
	int	yellow_card_val;
	int	red_card_cap;
	int	red_card_val;
	int	goal_kick_cap;
	int	drop_goal_cap;
};

///-----------------------------------------------------------
/// Enums for: rudb_player_performance_values

enum {
	CCDB_PLAYERPERFORMANCEVALUES_TRY_CAP=0,
	CCDB_PLAYERPERFORMANCEVALUES_TRY_VAL,
	CCDB_PLAYERPERFORMANCEVALUES_RUNNING_METERS_CAP,
	CCDB_PLAYERPERFORMANCEVALUES_RUNNING_METERS_VAL,
	CCDB_PLAYERPERFORMANCEVALUES_KICKING_METERS_CAP,
	CCDB_PLAYERPERFORMANCEVALUES_KICKING_METERS_VAL,
	CCDB_PLAYERPERFORMANCEVALUES_TACKLES_CAP,
	CCDB_PLAYERPERFORMANCEVALUES_TACKLES_VAL,
	CCDB_PLAYERPERFORMANCEVALUES_LINE_BREAKS_CAP,
	CCDB_PLAYERPERFORMANCEVALUES_LINE_BREAKS_VAL,
	CCDB_PLAYERPERFORMANCEVALUES_OFFLOADS_CAP,
	CCDB_PLAYERPERFORMANCEVALUES_OFFLOADS_VAL,
	CCDB_PLAYERPERFORMANCEVALUES_HANDLING_ERRORS_CAP,
	CCDB_PLAYERPERFORMANCEVALUES_HANDLING_ERRORS_VAL,
	CCDB_PLAYERPERFORMANCEVALUES_PENALTIES_CONCEDED_CAP,
	CCDB_PLAYERPERFORMANCEVALUES_PENALTIES_CONCEDED_VAL,
	CCDB_PLAYERPERFORMANCEVALUES_MISS_TACKLES_CAP,
	CCDB_PLAYERPERFORMANCEVALUES_MISS_TACKLES_VAL,
	CCDB_PLAYERPERFORMANCEVALUES_YELLOW_CARD_CAP,
	CCDB_PLAYERPERFORMANCEVALUES_YELLOW_CARD_VAL,
	CCDB_PLAYERPERFORMANCEVALUES_RED_CARD_CAP,
	CCDB_PLAYERPERFORMANCEVALUES_RED_CARD_VAL,
	CCDB_PLAYERPERFORMANCEVALUES_GOAL_KICK_CAP,
	CCDB_PLAYERPERFORMANCEVALUES_DROP_GOAL_CAP,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_player_position
struct rudb_player_position_row
{
	unsigned short db_id,pad;
	char	_enum[38];
	char	name[18];
};

///-----------------------------------------------------------
/// Enums for: rudb_player_position

enum {
	CCDB_PLAYERPOSITION_ENUM=0,
	CCDB_PLAYERPOSITION_NAME,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_player_proportions
struct rudb_player_proportions_row
{
	unsigned short db_id,pad;
	int	arm_length;
	int	shoulder_width;
	int	leg_length;
	int	neck_length;
	int	hip_width;
	int	spine_length;
	char	name[7];
	char	row_padding[1];
};

///-----------------------------------------------------------
/// Enums for: rudb_player_proportions

enum {
	CCDB_PLAYERPROPORTIONS_ARM_LENGTH=0,
	CCDB_PLAYERPROPORTIONS_SHOULDER_WIDTH,
	CCDB_PLAYERPROPORTIONS_LEG_LENGTH,
	CCDB_PLAYERPROPORTIONS_NECK_LENGTH,
	CCDB_PLAYERPROPORTIONS_HIP_WIDTH,
	CCDB_PLAYERPROPORTIONS_SPINE_LENGTH,
	CCDB_PLAYERPROPORTIONS_NAME,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_player_records
struct rudb_player_records_row
{
	unsigned short db_id,pad;
	int	type_id;
	int	competition_id;
	int	year_id;
	int	country_id;
	int	player_id;
	int	value;
	int	range;
	int	num_tests;
	int	year2_id;
	char	player_name[14];
	char	row_padding[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_player_records

enum {
	CCDB_PLAYERRECORDS_TYPE_ID=0,
	CCDB_PLAYERRECORDS_COMPETITION_ID,
	CCDB_PLAYERRECORDS_YEAR_ID,
	CCDB_PLAYERRECORDS_COUNTRY_ID,
	CCDB_PLAYERRECORDS_PLAYER_ID,
	CCDB_PLAYERRECORDS_VALUE,
	CCDB_PLAYERRECORDS_RANGE,
	CCDB_PLAYERRECORDS_NUM_TESTS,
	CCDB_PLAYERRECORDS_YEAR2_ID,
	CCDB_PLAYERRECORDS_PLAYER_NAME,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_preliminaries_format
struct rudb_preliminaries_format_row
{
	unsigned short db_id,pad;
	char	_enum[24];
	char	name[32];
};

///-----------------------------------------------------------
/// Enums for: rudb_preliminaries_format

enum {
	CCDB_PRELIMINARIESFORMAT_ENUM=0,
	CCDB_PRELIMINARIESFORMAT_NAME,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_pro_goals
struct rudb_pro_goals_row
{
	unsigned short db_id,pad;
	char	name[32];
	char	description[38];
	char	compare[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_pro_goals

enum {
	CCDB_PROGOALS_NAME=0,
	CCDB_PROGOALS_DESCRIPTION,
	CCDB_PROGOALS_COMPARE,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_pro_goals_general
struct rudb_pro_goals_general_row
{
	unsigned short db_id,pad;
	int	attribute1;
	int	attribute2;
	int	attribute3;
	unsigned short	r7_lhp_prob;
	unsigned short	r7_lhp_avg_min;
	unsigned short	r7_lhp_avg_max;
	unsigned short	r7_hooker_prob;
	unsigned short	r7_hooker_avg_min;
	unsigned short	r7_hooker_avg_max;
	unsigned short	r7_thp_prob;
	unsigned short	r7_thp_avg_min;
	unsigned short	r7_thp_avg_max;
	unsigned short	r7_shalf_prob;
	unsigned short	r7_shalf_avg_min;
	unsigned short	r7_shalf_avg_max;
	unsigned short	r7_fly_prob;
	unsigned short	r7_fly_avg_min;
	unsigned short	r7_fly_avg_max;
	unsigned short	r7_ic_prob;
	unsigned short	r7_ic_avg_min;
	unsigned short	r7_ic_avg_max;
	unsigned short	r7_fb_prob;
	unsigned short	r7_fb_avg_min;
	unsigned short	r7_fb_avg_max;
	unsigned short	r15_lhp_prob;
	unsigned short	r15_lhp_avg_min;
	unsigned short	r15_lhp_avg_max;
	unsigned short	r15_hooker_prob;
	unsigned short	r15_hooker_avg_min;
	unsigned short	r15_hooker_avg_max;
	unsigned short	r15_thp_prob;
	unsigned short	r15_thp_avg_min;
	unsigned short	r15_thp_avg_max;
	unsigned short	r15_4lock_prob;
	unsigned short	r15_4lock_avg_min;
	unsigned short	r15_4lock_avg_max;
	unsigned short	r15_5lock_prob;
	unsigned short	r15_5lock_avg_min;
	unsigned short	r15_5lock_avg_max;
	unsigned short	r15_bflank_prob;
	unsigned short	r15_bflank_avg_min;
	unsigned short	r15_bflank_avg_max;
	unsigned short	r15_oflank_prob;
	unsigned short	r15_oflank_avg_min;
	unsigned short	r15_oflank_avg_max;
	unsigned short	r15_no8_prob;
	unsigned short	r15_no8_avg_min;
	unsigned short	r15_no8_avg_max;
	unsigned short	r15_shalf_prob;
	unsigned short	r15_shalf_avg_min;
	unsigned short	r15_shalf_avg_max;
	unsigned short	r15_fly_prob;
	unsigned short	r15_fly_avg_min;
	unsigned short	r15_fly_avg_max;
	unsigned short	r15_lwing_prob;
	unsigned short	r15_lwing_avg_min;
	unsigned short	r15_lwing_avg_max;
	unsigned short	r15_ic_prob;
	unsigned short	r15_ic_avg_min;
	unsigned short	r15_ic_avg_max;
	unsigned short	r15_oc_prob;
	unsigned short	r15_oc_avg_min;
	unsigned short	r15_oc_avg_max;
	unsigned short	r15_rwing_prob;
	unsigned short	r15_rwing_avg_min;
	unsigned short	r15_rwing_avg_max;
	unsigned short	r15_fb_prob;
	unsigned short	r15_fb_avg_min;
	unsigned short	r15_fb_avg_max;
	char	name[30];
	char	description_short[35];
	char	description_long[34];
	char	compare[2];
	char	type[13];
	char	row_padding[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_pro_goals_general

enum {
	CCDB_PROGOALSGENERAL_ATTRIBUTE1=0,
	CCDB_PROGOALSGENERAL_ATTRIBUTE2,
	CCDB_PROGOALSGENERAL_ATTRIBUTE3,
	CCDB_PROGOALSGENERAL_R7_LHP_PROB,
	CCDB_PROGOALSGENERAL_R7_LHP_AVG_MIN,
	CCDB_PROGOALSGENERAL_R7_LHP_AVG_MAX,
	CCDB_PROGOALSGENERAL_R7_HOOKER_PROB,
	CCDB_PROGOALSGENERAL_R7_HOOKER_AVG_MIN,
	CCDB_PROGOALSGENERAL_R7_HOOKER_AVG_MAX,
	CCDB_PROGOALSGENERAL_R7_THP_PROB,
	CCDB_PROGOALSGENERAL_R7_THP_AVG_MIN,
	CCDB_PROGOALSGENERAL_R7_THP_AVG_MAX,
	CCDB_PROGOALSGENERAL_R7_SHALF_PROB,
	CCDB_PROGOALSGENERAL_R7_SHALF_AVG_MIN,
	CCDB_PROGOALSGENERAL_R7_SHALF_AVG_MAX,
	CCDB_PROGOALSGENERAL_R7_FLY_PROB,
	CCDB_PROGOALSGENERAL_R7_FLY_AVG_MIN,
	CCDB_PROGOALSGENERAL_R7_FLY_AVG_MAX,
	CCDB_PROGOALSGENERAL_R7_IC_PROB,
	CCDB_PROGOALSGENERAL_R7_IC_AVG_MIN,
	CCDB_PROGOALSGENERAL_R7_IC_AVG_MAX,
	CCDB_PROGOALSGENERAL_R7_FB_PROB,
	CCDB_PROGOALSGENERAL_R7_FB_AVG_MIN,
	CCDB_PROGOALSGENERAL_R7_FB_AVG_MAX,
	CCDB_PROGOALSGENERAL_R15_LHP_PROB,
	CCDB_PROGOALSGENERAL_R15_LHP_AVG_MIN,
	CCDB_PROGOALSGENERAL_R15_LHP_AVG_MAX,
	CCDB_PROGOALSGENERAL_R15_HOOKER_PROB,
	CCDB_PROGOALSGENERAL_R15_HOOKER_AVG_MIN,
	CCDB_PROGOALSGENERAL_R15_HOOKER_AVG_MAX,
	CCDB_PROGOALSGENERAL_R15_THP_PROB,
	CCDB_PROGOALSGENERAL_R15_THP_AVG_MIN,
	CCDB_PROGOALSGENERAL_R15_THP_AVG_MAX,
	CCDB_PROGOALSGENERAL_R15_4LOCK_PROB,
	CCDB_PROGOALSGENERAL_R15_4LOCK_AVG_MIN,
	CCDB_PROGOALSGENERAL_R15_4LOCK_AVG_MAX,
	CCDB_PROGOALSGENERAL_R15_5LOCK_PROB,
	CCDB_PROGOALSGENERAL_R15_5LOCK_AVG_MIN,
	CCDB_PROGOALSGENERAL_R15_5LOCK_AVG_MAX,
	CCDB_PROGOALSGENERAL_R15_BFLANK_PROB,
	CCDB_PROGOALSGENERAL_R15_BFLANK_AVG_MIN,
	CCDB_PROGOALSGENERAL_R15_BFLANK_AVG_MAX,
	CCDB_PROGOALSGENERAL_R15_OFLANK_PROB,
	CCDB_PROGOALSGENERAL_R15_OFLANK_AVG_MIN,
	CCDB_PROGOALSGENERAL_R15_OFLANK_AVG_MAX,
	CCDB_PROGOALSGENERAL_R15_NO8_PROB,
	CCDB_PROGOALSGENERAL_R15_NO8_AVG_MIN,
	CCDB_PROGOALSGENERAL_R15_NO8_AVG_MAX,
	CCDB_PROGOALSGENERAL_R15_SHALF_PROB,
	CCDB_PROGOALSGENERAL_R15_SHALF_AVG_MIN,
	CCDB_PROGOALSGENERAL_R15_SHALF_AVG_MAX,
	CCDB_PROGOALSGENERAL_R15_FLY_PROB,
	CCDB_PROGOALSGENERAL_R15_FLY_AVG_MIN,
	CCDB_PROGOALSGENERAL_R15_FLY_AVG_MAX,
	CCDB_PROGOALSGENERAL_R15_LWING_PROB,
	CCDB_PROGOALSGENERAL_R15_LWING_AVG_MIN,
	CCDB_PROGOALSGENERAL_R15_LWING_AVG_MAX,
	CCDB_PROGOALSGENERAL_R15_IC_PROB,
	CCDB_PROGOALSGENERAL_R15_IC_AVG_MIN,
	CCDB_PROGOALSGENERAL_R15_IC_AVG_MAX,
	CCDB_PROGOALSGENERAL_R15_OC_PROB,
	CCDB_PROGOALSGENERAL_R15_OC_AVG_MIN,
	CCDB_PROGOALSGENERAL_R15_OC_AVG_MAX,
	CCDB_PROGOALSGENERAL_R15_RWING_PROB,
	CCDB_PROGOALSGENERAL_R15_RWING_AVG_MIN,
	CCDB_PROGOALSGENERAL_R15_RWING_AVG_MAX,
	CCDB_PROGOALSGENERAL_R15_FB_PROB,
	CCDB_PROGOALSGENERAL_R15_FB_AVG_MIN,
	CCDB_PROGOALSGENERAL_R15_FB_AVG_MAX,
	CCDB_PROGOALSGENERAL_NAME,
	CCDB_PROGOALSGENERAL_DESCRIPTION_SHORT,
	CCDB_PROGOALSGENERAL_DESCRIPTION_LONG,
	CCDB_PROGOALSGENERAL_COMPARE,
	CCDB_PROGOALSGENERAL_TYPE,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_records
struct rudb_records_row
{
	unsigned short db_id,pad;
	unsigned short	comp_def_id;
	unsigned short	value;
	unsigned short	team_id;
	unsigned short	year;
	unsigned char	record_type;
	char	player_name[32];
	char	row_padding[3];
};

///-----------------------------------------------------------
/// Enums for: rudb_records

enum {
	CCDB_RECORDS_COMP_DEF_ID=0,
	CCDB_RECORDS_VALUE,
	CCDB_RECORDS_TEAM_ID,
	CCDB_RECORDS_YEAR,
	CCDB_RECORDS_RECORD_TYPE,
	CCDB_RECORDS_PLAYER_NAME,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_rep_area
struct rudb_rep_area_row
{
	unsigned short db_id,pad;
	char	name[15];
	char	row_padding[1];
};

///-----------------------------------------------------------
/// Enums for: rudb_rep_area

enum {
	CCDB_REPAREA_NAME=0,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_rep_area_country
struct rudb_rep_area_country_row
{
	unsigned short db_id,pad;
	unsigned short	reparea_id;
	unsigned short	country_id;
};

///-----------------------------------------------------------
/// Enums for: rudb_rep_area_country

enum {
	CCDB_REPAREACOUNTRY_REPAREA_ID=0,
	CCDB_REPAREACOUNTRY_COUNTRY_ID,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_schema_version
struct rudb_schema_version_row
{
	unsigned short db_id,pad;
	int	version_no;
	char	description[17];
	char	row_padding[3];
};

///-----------------------------------------------------------
/// Enums for: rudb_schema_version

enum {
	CCDB_SCHEMAVERSION_DESCRIPTION=0,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_stats_player
struct rudb_stats_player_row
{
	unsigned short db_id,pad;
	int	points_scored;
	int	successful_conversion_attempts;
	int	successful_field_goals;
	int	successful_penalty_goals;
	int	successful_try_attempts;
	unsigned short	competition_id;
	unsigned short	match_id;
	unsigned short	team_id;
	unsigned short	player_id;
};

///-----------------------------------------------------------
/// Enums for: rudb_stats_player

enum {
	CCDB_STATSPLAYER_POINTS_SCORED=0,
	CCDB_STATSPLAYER_SUCCESSFUL_CONVERSION_ATTEMPTS,
	CCDB_STATSPLAYER_SUCCESSFUL_FIELD_GOALS,
	CCDB_STATSPLAYER_SUCCESSFUL_PENALTY_GOALS,
	CCDB_STATSPLAYER_SUCCESSFUL_TRY_ATTEMPTS,
	CCDB_STATSPLAYER_COMPETITION_ID,
	CCDB_STATSPLAYER_MATCH_ID,
	CCDB_STATSPLAYER_TEAM_ID,
	CCDB_STATSPLAYER_PLAYER_ID,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_stats_team
struct rudb_stats_team_row
{
	unsigned short db_id,pad;
	int	score;
	int	opponent_score;
	unsigned short	competition_id;
	unsigned short	match_id;
	unsigned short	team_id;
	char	row_padding[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_stats_team

enum {
	CCDB_STATSTEAM_SCORE=0,
	CCDB_STATSTEAM_OPPONENT_SCORE,
	CCDB_STATSTEAM_COMPETITION_ID,
	CCDB_STATSTEAM_MATCH_ID,
	CCDB_STATSTEAM_TEAM_ID,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_suspension_offences
struct rudb_suspension_offences_row
{
	unsigned short db_id,pad;
	unsigned short	innocent_min;
	unsigned short	innocent_max;
	unsigned short	guilty_min;
	unsigned short	guilty_max;
	char	description[30];
	char	row_padding[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_suspension_offences

enum {
	CCDB_SUSPENSIONOFFENCES_INNOCENT_MIN=0,
	CCDB_SUSPENSIONOFFENCES_INNOCENT_MAX,
	CCDB_SUSPENSIONOFFENCES_GUILTY_MIN,
	CCDB_SUSPENSIONOFFENCES_GUILTY_MAX,
	CCDB_SUSPENSIONOFFENCES_DESCRIPTION,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_team_commentary_name
struct rudb_team_commentary_name_row
{
	unsigned short db_id,pad;
	char	name[22];
	char	row_padding[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_team_commentary_name

enum {
	CCDB_TEAMCOMMENTARYNAME_NAME=0,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_team_link
struct rudb_team_link_row
{
	unsigned short db_id,pad;
	unsigned short	original_team;
	unsigned short	linked_team;
};

///-----------------------------------------------------------
/// Enums for: rudb_team_link

enum {
	CCDB_TEAMLINK_ORIGINAL_TEAM=0,
	CCDB_TEAMLINK_LINKED_TEAM,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_team_records
struct rudb_team_records_row
{
	unsigned short db_id,pad;
	int	type_id;
	int	competition_id;
	int	year;
	int	country_id;
	int	team_id;
	int	opp_team_id;
	int	value;
	int	opp_value;
	int	range;
	int	city_id;
	int	stadium_id;
	int	statistic_value;
	char	team_name[2];
	char	opp_team_name[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_team_records

enum {
	CCDB_TEAMRECORDS_TYPE_ID=0,
	CCDB_TEAMRECORDS_COMPETITION_ID,
	CCDB_TEAMRECORDS_YEAR,
	CCDB_TEAMRECORDS_COUNTRY_ID,
	CCDB_TEAMRECORDS_TEAM_ID,
	CCDB_TEAMRECORDS_OPP_TEAM_ID,
	CCDB_TEAMRECORDS_VALUE,
	CCDB_TEAMRECORDS_OPP_VALUE,
	CCDB_TEAMRECORDS_RANGE,
	CCDB_TEAMRECORDS_CITY_ID,
	CCDB_TEAMRECORDS_STADIUM_ID,
	CCDB_TEAMRECORDS_STATISTIC_VALUE,
	CCDB_TEAMRECORDS_TEAM_NAME,
	CCDB_TEAMRECORDS_OPP_TEAM_NAME,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_ticker_messages
struct rudb_ticker_messages_row
{
	unsigned short db_id,pad;
	unsigned short	player_db_id;
	unsigned char	type;
	unsigned char	player_relevant;
	unsigned char	days_to_expire;
	char	message[32];
	char	replacements[256];
	char	date[16];
	char	row_padding[3];
};

///-----------------------------------------------------------
/// Enums for: rudb_ticker_messages

enum {
	CCDB_TICKERMESSAGES_PLAYER_DB_ID=0,
	CCDB_TICKERMESSAGES_TYPE,
	CCDB_TICKERMESSAGES_PLAYER_RELEVANT,
	CCDB_TICKERMESSAGES_DAYS_TO_EXPIRE,
	CCDB_TICKERMESSAGES_MESSAGE,
	CCDB_TICKERMESSAGES_REPLACEMENTS,
	CCDB_TICKERMESSAGES_DATE,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_ui_team
struct rudb_ui_team_row
{
	unsigned short db_id,pad;
	unsigned short	player_id_1;
	unsigned short	player_id_2;
	unsigned short	player_id_3;
	unsigned short	player_id_4;
};

///-----------------------------------------------------------
/// Enums for: rudb_ui_team

enum {
	CCDB_UITEAM_PLAYER_ID_1=0,
	CCDB_UITEAM_PLAYER_ID_2,
	CCDB_UITEAM_PLAYER_ID_3,
	CCDB_UITEAM_PLAYER_ID_4,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_player_morphtargets
struct rudb_player_morphtargets_row
{
	unsigned short db_id,pad;
	float	eyePositionHeight;
	float	eyePositionWidth;
	float	eyeRotation;
	float	eyeScale;
	float	eyeTopShapeSkew;
	float	eyeBottomShapeSkew;
	float	eyeTopShapeScale;
	float	eyeBottomShapeScale;
	float	eyeDepth;
	float	eyeLidIntensity;
	float	eyeLidHeight;
	float	eyeLidDroop;
	float	mouthSizeWidth;
	float	mouthSizeHeight;
	float	mouthPositionHorizontal;
	float	mouthPositionVertical;
	float	mouthDepth;
	float	mouthSideHeight;
	float	mouthCornersWidth;
	float	mouthCornersHeight;
	float	lipTopWidth;
	float	lipTopHeight;
	float	lipBottomWidth;
	float	lipBottomHeight;
	float	philtrumWidth;
	float	philtrumHeight;
	float	mouthJowl;
	float	browInnerPosHeight;
	float	browInnerPosWidth;
	float	browMiddlePosHeight;
	float	browMiddlePosWidth;
	float	browOuterPosHeight;
	float	browOuterPosWidth;
	float	browInnerDepth;
	float	browMiddleDepth;
	float	browOuterDepth;
	float	browIntensity;
	float	cheekBoneWidth;
	float	cheekBoneHeight;
	float	cheekBoneDepth;
	float	cheekPositionWidth;
	float	cheekPositionHeight;
	float	cheekDepth;
	float	cheekGaunt;
	float	malarFat;
	float	jawSizeHeight;
	float	jawSizeWidth;
	float	jawShapePuffiness;
	float	jawDepth;
	float	masseterSizeHeight;
	float	masseterSizeWidth;
	float	masseterSizeDepth;
	float	underbite;
	float	jowl;
	float	noseSizeHeight;
	float	noseSizeWidth;
	float	noseDepth;
	float	noseRootPositionHeight;
	float	noseRootPositionWidth;
	float	noseRootPositionDepth;
	float	noseBridgePositionHeight;
	float	noseBridgePositionWidth;
	float	noseBridgePositionDepth;
	float	noseBreak;
	float	sinusSize;
	float	noseTipSizeWidth;
	float	noseTipSizeHeight;
	float	noseTipPositionHorizontal;
	float	noseTipPositionVertical;
	float	noseTipHeight;
	float	noseTipDepth;
	float	nostrilPositionHorizontal;
	float	nostrilPositionVertical;
	float	nostrilDepth;
	float	nostrilSize;
	float	noseFill;
	float	septumWidth;
	float	septumHeight;
	float	facialSizeLowerLength;
	float	facialSizeWidth;
	float	cranialWidth;
	float	cranialHeight;
	float	cranialDepth;
	float	templeWidth;
	float	foreheadSizeHeight;
	float	foreheadSizeWidth;
	float	foreheadDepth;
	float	chinSizeWidth;
	float	chinSizeHeight;
	float	chinPositionHorizontal;
	float	chinPositionVertical;
	float	chinDepth;
	float	earScaleWidth;
	float	earScaleHeight;
	float	earRotationClockwiseAnti;
	float	earRotationInOut;
	float	earPositionHorizontal;
	float	earPositionVertical;
	float	earLobe;
	float	ethnicity;
	float	bodyDefinitionUndefined;
	float	bodyDefinitionDefined;
	float	bodyArmsUpperSmall;
	float	bodyArmsUpperLarge;
	float	bodyArmsLowerSmall;
	float	bodyArmsLowerLarge;
	float	bodyLegsUpperSmall;
	float	bodyLegsUpperLarge;
	float	bodyLegsLowerSmall;
	float	bodyLegsLowerLarge;
	float	bodyLegShapeKnockKnee;
	float	bodyLegShapeBowed;
	float	stripTorsoUpperSmall;
	float	stripTorsoUpperLarge;
	float	stripTorsoLowerSmall;
	float	stripTorsoLowerLarge;
	float	stripStomachSmall;
	float	stripStomachLarge;
	float	stripSpineShapeArched;
	float	stripSpineShapeStraight;
	float	stripTrapeziusSmall;
	float	stripTrapeziusLarge;
	float	collarNeckWidthThin;
	float	collarNeckWidthThick;
	float	collarNeckWidthNarrow;
	float	collarNeckWidthWide;
	unsigned short	player_id;
	char	row_padding[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_player_morphtargets

enum {
	CCDB_PLAYERMORPHTARGETS_EYEPOSITIONHEIGHT=0,
	CCDB_PLAYERMORPHTARGETS_EYEPOSITIONWIDTH,
	CCDB_PLAYERMORPHTARGETS_EYEROTATION,
	CCDB_PLAYERMORPHTARGETS_EYESCALE,
	CCDB_PLAYERMORPHTARGETS_EYETOPSHAPESKEW,
	CCDB_PLAYERMORPHTARGETS_EYEBOTTOMSHAPESKEW,
	CCDB_PLAYERMORPHTARGETS_EYETOPSHAPESCALE,
	CCDB_PLAYERMORPHTARGETS_EYEBOTTOMSHAPESCALE,
	CCDB_PLAYERMORPHTARGETS_EYEDEPTH,
	CCDB_PLAYERMORPHTARGETS_EYELIDINTENSITY,
	CCDB_PLAYERMORPHTARGETS_EYELIDHEIGHT,
	CCDB_PLAYERMORPHTARGETS_EYELIDDROOP,
	CCDB_PLAYERMORPHTARGETS_MOUTHSIZEWIDTH,
	CCDB_PLAYERMORPHTARGETS_MOUTHSIZEHEIGHT,
	CCDB_PLAYERMORPHTARGETS_MOUTHPOSITIONHORIZONTAL,
	CCDB_PLAYERMORPHTARGETS_MOUTHPOSITIONVERTICAL,
	CCDB_PLAYERMORPHTARGETS_MOUTHDEPTH,
	CCDB_PLAYERMORPHTARGETS_MOUTHSIDEHEIGHT,
	CCDB_PLAYERMORPHTARGETS_MOUTHCORNERSWIDTH,
	CCDB_PLAYERMORPHTARGETS_MOUTHCORNERSHEIGHT,
	CCDB_PLAYERMORPHTARGETS_LIPTOPWIDTH,
	CCDB_PLAYERMORPHTARGETS_LIPTOPHEIGHT,
	CCDB_PLAYERMORPHTARGETS_LIPBOTTOMWIDTH,
	CCDB_PLAYERMORPHTARGETS_LIPBOTTOMHEIGHT,
	CCDB_PLAYERMORPHTARGETS_PHILTRUMWIDTH,
	CCDB_PLAYERMORPHTARGETS_PHILTRUMHEIGHT,
	CCDB_PLAYERMORPHTARGETS_MOUTHJOWL,
	CCDB_PLAYERMORPHTARGETS_BROWINNERPOSHEIGHT,
	CCDB_PLAYERMORPHTARGETS_BROWINNERPOSWIDTH,
	CCDB_PLAYERMORPHTARGETS_BROWMIDDLEPOSHEIGHT,
	CCDB_PLAYERMORPHTARGETS_BROWMIDDLEPOSWIDTH,
	CCDB_PLAYERMORPHTARGETS_BROWOUTERPOSHEIGHT,
	CCDB_PLAYERMORPHTARGETS_BROWOUTERPOSWIDTH,
	CCDB_PLAYERMORPHTARGETS_BROWINNERDEPTH,
	CCDB_PLAYERMORPHTARGETS_BROWMIDDLEDEPTH,
	CCDB_PLAYERMORPHTARGETS_BROWOUTERDEPTH,
	CCDB_PLAYERMORPHTARGETS_BROWINTENSITY,
	CCDB_PLAYERMORPHTARGETS_CHEEKBONEWIDTH,
	CCDB_PLAYERMORPHTARGETS_CHEEKBONEHEIGHT,
	CCDB_PLAYERMORPHTARGETS_CHEEKBONEDEPTH,
	CCDB_PLAYERMORPHTARGETS_CHEEKPOSITIONWIDTH,
	CCDB_PLAYERMORPHTARGETS_CHEEKPOSITIONHEIGHT,
	CCDB_PLAYERMORPHTARGETS_CHEEKDEPTH,
	CCDB_PLAYERMORPHTARGETS_CHEEKGAUNT,
	CCDB_PLAYERMORPHTARGETS_MALARFAT,
	CCDB_PLAYERMORPHTARGETS_JAWSIZEHEIGHT,
	CCDB_PLAYERMORPHTARGETS_JAWSIZEWIDTH,
	CCDB_PLAYERMORPHTARGETS_JAWSHAPEPUFFINESS,
	CCDB_PLAYERMORPHTARGETS_JAWDEPTH,
	CCDB_PLAYERMORPHTARGETS_MASSETERSIZEHEIGHT,
	CCDB_PLAYERMORPHTARGETS_MASSETERSIZEWIDTH,
	CCDB_PLAYERMORPHTARGETS_MASSETERSIZEDEPTH,
	CCDB_PLAYERMORPHTARGETS_UNDERBITE,
	CCDB_PLAYERMORPHTARGETS_JOWL,
	CCDB_PLAYERMORPHTARGETS_NOSESIZEHEIGHT,
	CCDB_PLAYERMORPHTARGETS_NOSESIZEWIDTH,
	CCDB_PLAYERMORPHTARGETS_NOSEDEPTH,
	CCDB_PLAYERMORPHTARGETS_NOSEROOTPOSITIONHEIGHT,
	CCDB_PLAYERMORPHTARGETS_NOSEROOTPOSITIONWIDTH,
	CCDB_PLAYERMORPHTARGETS_NOSEROOTPOSITIONDEPTH,
	CCDB_PLAYERMORPHTARGETS_NOSEBRIDGEPOSITIONHEIGHT,
	CCDB_PLAYERMORPHTARGETS_NOSEBRIDGEPOSITIONWIDTH,
	CCDB_PLAYERMORPHTARGETS_NOSEBRIDGEPOSITIONDEPTH,
	CCDB_PLAYERMORPHTARGETS_NOSEBREAK,
	CCDB_PLAYERMORPHTARGETS_SINUSSIZE,
	CCDB_PLAYERMORPHTARGETS_NOSETIPSIZEWIDTH,
	CCDB_PLAYERMORPHTARGETS_NOSETIPSIZEHEIGHT,
	CCDB_PLAYERMORPHTARGETS_NOSETIPPOSITIONHORIZONTAL,
	CCDB_PLAYERMORPHTARGETS_NOSETIPPOSITIONVERTICAL,
	CCDB_PLAYERMORPHTARGETS_NOSETIPHEIGHT,
	CCDB_PLAYERMORPHTARGETS_NOSETIPDEPTH,
	CCDB_PLAYERMORPHTARGETS_NOSTRILPOSITIONHORIZONTAL,
	CCDB_PLAYERMORPHTARGETS_NOSTRILPOSITIONVERTICAL,
	CCDB_PLAYERMORPHTARGETS_NOSTRILDEPTH,
	CCDB_PLAYERMORPHTARGETS_NOSTRILSIZE,
	CCDB_PLAYERMORPHTARGETS_NOSEFILL,
	CCDB_PLAYERMORPHTARGETS_SEPTUMWIDTH,
	CCDB_PLAYERMORPHTARGETS_SEPTUMHEIGHT,
	CCDB_PLAYERMORPHTARGETS_FACIALSIZELOWERLENGTH,
	CCDB_PLAYERMORPHTARGETS_FACIALSIZEWIDTH,
	CCDB_PLAYERMORPHTARGETS_CRANIALWIDTH,
	CCDB_PLAYERMORPHTARGETS_CRANIALHEIGHT,
	CCDB_PLAYERMORPHTARGETS_CRANIALDEPTH,
	CCDB_PLAYERMORPHTARGETS_TEMPLEWIDTH,
	CCDB_PLAYERMORPHTARGETS_FOREHEADSIZEHEIGHT,
	CCDB_PLAYERMORPHTARGETS_FOREHEADSIZEWIDTH,
	CCDB_PLAYERMORPHTARGETS_FOREHEADDEPTH,
	CCDB_PLAYERMORPHTARGETS_CHINSIZEWIDTH,
	CCDB_PLAYERMORPHTARGETS_CHINSIZEHEIGHT,
	CCDB_PLAYERMORPHTARGETS_CHINPOSITIONHORIZONTAL,
	CCDB_PLAYERMORPHTARGETS_CHINPOSITIONVERTICAL,
	CCDB_PLAYERMORPHTARGETS_CHINDEPTH,
	CCDB_PLAYERMORPHTARGETS_EARSCALEWIDTH,
	CCDB_PLAYERMORPHTARGETS_EARSCALEHEIGHT,
	CCDB_PLAYERMORPHTARGETS_EARROTATIONCLOCKWISEANTI,
	CCDB_PLAYERMORPHTARGETS_EARROTATIONINOUT,
	CCDB_PLAYERMORPHTARGETS_EARPOSITIONHORIZONTAL,
	CCDB_PLAYERMORPHTARGETS_EARPOSITIONVERTICAL,
	CCDB_PLAYERMORPHTARGETS_EARLOBE,
	CCDB_PLAYERMORPHTARGETS_ETHNICITY,
	CCDB_PLAYERMORPHTARGETS_BODYDEFINITIONUNDEFINED,
	CCDB_PLAYERMORPHTARGETS_BODYDEFINITIONDEFINED,
	CCDB_PLAYERMORPHTARGETS_BODYARMSUPPERSMALL,
	CCDB_PLAYERMORPHTARGETS_BODYARMSUPPERLARGE,
	CCDB_PLAYERMORPHTARGETS_BODYARMSLOWERSMALL,
	CCDB_PLAYERMORPHTARGETS_BODYARMSLOWERLARGE,
	CCDB_PLAYERMORPHTARGETS_BODYLEGSUPPERSMALL,
	CCDB_PLAYERMORPHTARGETS_BODYLEGSUPPERLARGE,
	CCDB_PLAYERMORPHTARGETS_BODYLEGSLOWERSMALL,
	CCDB_PLAYERMORPHTARGETS_BODYLEGSLOWERLARGE,
	CCDB_PLAYERMORPHTARGETS_BODYLEGSHAPEKNOCKKNEE,
	CCDB_PLAYERMORPHTARGETS_BODYLEGSHAPEBOWED,
	CCDB_PLAYERMORPHTARGETS_STRIPTORSOUPPERSMALL,
	CCDB_PLAYERMORPHTARGETS_STRIPTORSOUPPERLARGE,
	CCDB_PLAYERMORPHTARGETS_STRIPTORSOLOWERSMALL,
	CCDB_PLAYERMORPHTARGETS_STRIPTORSOLOWERLARGE,
	CCDB_PLAYERMORPHTARGETS_STRIPSTOMACHSMALL,
	CCDB_PLAYERMORPHTARGETS_STRIPSTOMACHLARGE,
	CCDB_PLAYERMORPHTARGETS_STRIPSPINESHAPEARCHED,
	CCDB_PLAYERMORPHTARGETS_STRIPSPINESHAPESTRAIGHT,
	CCDB_PLAYERMORPHTARGETS_STRIPTRAPEZIUSSMALL,
	CCDB_PLAYERMORPHTARGETS_STRIPTRAPEZIUSLARGE,
	CCDB_PLAYERMORPHTARGETS_COLLARNECKWIDTHTHIN,
	CCDB_PLAYERMORPHTARGETS_COLLARNECKWIDTHTHICK,
	CCDB_PLAYERMORPHTARGETS_COLLARNECKWIDTHNARROW,
	CCDB_PLAYERMORPHTARGETS_COLLARNECKWIDTHWIDE,
	CCDB_PLAYERMORPHTARGETS_PLAYER_ID,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_stadium
struct rudb_stadium_row
{
	unsigned short db_id,pad;
	unsigned short	city_id;
	unsigned short	max_wind_speed;
	unsigned char	exported;
	unsigned char	size;
	char	name[24];
	char	abbreviation[5];
	char	filename[28];
	char	row_padding[1];
};

///-----------------------------------------------------------
/// Enums for: rudb_stadium

enum {
	CCDB_STADIUM_CITY_ID=0,
	CCDB_STADIUM_MAX_WIND_SPEED,
	CCDB_STADIUM_EXPORTED,
	CCDB_STADIUM_SIZE,
	CCDB_STADIUM_NAME,
	CCDB_STADIUM_ABBREVIATION,
	CCDB_STADIUM_FILENAME,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_icon
struct rudb_icon_row
{
	unsigned short db_id,pad;
	int	primary_colour;
	int	secondary_colour;
	unsigned char	exported;
	char	name[26];
	char	icon_file_string[6];
	char	row_padding[3];
};

///-----------------------------------------------------------
/// Enums for: rudb_icon

enum {
	CCDB_ICON_PRIMARY_COLOUR=0,
	CCDB_ICON_SECONDARY_COLOUR,
	CCDB_ICON_EXPORTED,
	CCDB_ICON_NAME,
	CCDB_ICON_ICON_FILE_STRING,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_team_logo
struct rudb_team_logo_row
{
	unsigned short db_id,pad;
	int	primary_logo_colour;
	int	screen_friendly_logo_colour;
	int	splotch_logo_colour;
	char	name[24];
	char	filename[10];
	char	row_padding[2];
};

///-----------------------------------------------------------
/// Enums for: rudb_team_logo

enum {
	CCDB_TEAMLOGO_PRIMARY_LOGO_COLOUR=0,
	CCDB_TEAMLOGO_SCREEN_FRIENDLY_LOGO_COLOUR,
	CCDB_TEAMLOGO_SPLOTCH_LOGO_COLOUR,
	CCDB_TEAMLOGO_NAME,
	CCDB_TEAMLOGO_FILENAME,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_team
struct rudb_team_row
{
	unsigned short db_id,pad;
	int	cash;
	int	firing_fees;
	unsigned short	commentary_name_id;
	unsigned short	strip_id_1;
	unsigned short	strip_id_2;
	unsigned short	strip_id_3;
	unsigned short	logo_id;
	unsigned short	associated_country_id;
	unsigned short	captain_id;
	unsigned short	goal_kicker_id;
	unsigned short	play_kicker_id;
	unsigned short	ranking;
	unsigned short	attack;
	unsigned short	defence;
	unsigned short	ruck_ability;
	unsigned short	maul_ability;
	unsigned short	scrum_ability;
	unsigned short	lineout_ability;
	unsigned short	def_forward_pass_drive;
	unsigned short	def_forward_contact_offload;
	unsigned short	def_back_pass_kick;
	unsigned short	def_back_contact_offload;
	unsigned short	def_lineout_size;
	unsigned short	def_lineout_favoured_target;
	unsigned short	def_ruck_win;
	unsigned short	def_lineout_win;
	unsigned short	def_scrum_win;
	unsigned short	def_maul_win;
	unsigned short	def_line_width;
	unsigned short	def_line_depth;
	unsigned short	def_ruck_commitment;
	unsigned short	mid_forward_pass_drive;
	unsigned short	mid_forward_contact_offload;
	unsigned short	mid_back_pass_kick;
	unsigned short	mid_back_contact_offload;
	unsigned short	mid_lineout_size;
	unsigned short	mid_lineout_favoured_target;
	unsigned short	mid_ruck_win;
	unsigned short	mid_lineout_win;
	unsigned short	mid_scrum_win;
	unsigned short	mid_maul_win;
	unsigned short	mid_line_width;
	unsigned short	mid_line_depth;
	unsigned short	mid_ruck_commitment;
	unsigned short	att_forward_pass_drive;
	unsigned short	att_forward_contact_offload;
	unsigned short	att_back_pass_kick;
	unsigned short	att_back_contact_offload;
	unsigned short	att_lineout_size;
	unsigned short	att_lineout_favoured_target;
	unsigned short	att_ruck_win;
	unsigned short	att_lineout_win;
	unsigned short	att_scrum_win;
	unsigned short	att_maul_win;
	unsigned short	att_line_width;
	unsigned short	att_line_depth;
	unsigned short	att_ruck_commitment;
	unsigned short	kick_kickoff_short_long;
	unsigned short	kick_kickoff_left_right;
	unsigned short	kick_dropout_short_long;
	unsigned short	kick_dropout_left_right;
	unsigned short	kick_touch_territory;
	unsigned short	kick_penalty_touch_goal;
	unsigned short	rep_area_id;
	unsigned short	primary_comp;
	unsigned char	requires_the;
	unsigned char	custom;
	unsigned char	boot_texture_num;
	unsigned char	is_locked;
	unsigned char	def_pod_option;
	unsigned char	mid_pod_option;
	unsigned char	att_pod_option;
	unsigned char	days_till_rep_selection_allowed;
	unsigned char	r7_exclusive;
	unsigned char	permission_flags_gender;
	char	name[26];
	char	short_name[25];
	char	abbrev[4];
	char	row_padding[1];
};

///-----------------------------------------------------------
/// Enums for: rudb_team

enum {
	CCDB_TEAM_CASH=0,
	CCDB_TEAM_FIRING_FEES,
	CCDB_TEAM_COMMENTARY_NAME_ID,
	CCDB_TEAM_STRIP_ID_1,
	CCDB_TEAM_STRIP_ID_2,
	CCDB_TEAM_STRIP_ID_3,
	CCDB_TEAM_LOGO_ID,
	CCDB_TEAM_ASSOCIATED_COUNTRY_ID,
	CCDB_TEAM_CAPTAIN_ID,
	CCDB_TEAM_GOAL_KICKER_ID,
	CCDB_TEAM_PLAY_KICKER_ID,
	CCDB_TEAM_RANKING,
	CCDB_TEAM_ATTACK,
	CCDB_TEAM_DEFENCE,
	CCDB_TEAM_RUCK_ABILITY,
	CCDB_TEAM_MAUL_ABILITY,
	CCDB_TEAM_SCRUM_ABILITY,
	CCDB_TEAM_LINEOUT_ABILITY,
	CCDB_TEAM_DEF_FORWARD_PASS_DRIVE,
	CCDB_TEAM_DEF_FORWARD_CONTACT_OFFLOAD,
	CCDB_TEAM_DEF_BACK_PASS_KICK,
	CCDB_TEAM_DEF_BACK_CONTACT_OFFLOAD,
	CCDB_TEAM_DEF_LINEOUT_SIZE,
	CCDB_TEAM_DEF_LINEOUT_FAVOURED_TARGET,
	CCDB_TEAM_DEF_RUCK_WIN,
	CCDB_TEAM_DEF_LINEOUT_WIN,
	CCDB_TEAM_DEF_SCRUM_WIN,
	CCDB_TEAM_DEF_MAUL_WIN,
	CCDB_TEAM_DEF_LINE_WIDTH,
	CCDB_TEAM_DEF_LINE_DEPTH,
	CCDB_TEAM_DEF_RUCK_COMMITMENT,
	CCDB_TEAM_MID_FORWARD_PASS_DRIVE,
	CCDB_TEAM_MID_FORWARD_CONTACT_OFFLOAD,
	CCDB_TEAM_MID_BACK_PASS_KICK,
	CCDB_TEAM_MID_BACK_CONTACT_OFFLOAD,
	CCDB_TEAM_MID_LINEOUT_SIZE,
	CCDB_TEAM_MID_LINEOUT_FAVOURED_TARGET,
	CCDB_TEAM_MID_RUCK_WIN,
	CCDB_TEAM_MID_LINEOUT_WIN,
	CCDB_TEAM_MID_SCRUM_WIN,
	CCDB_TEAM_MID_MAUL_WIN,
	CCDB_TEAM_MID_LINE_WIDTH,
	CCDB_TEAM_MID_LINE_DEPTH,
	CCDB_TEAM_MID_RUCK_COMMITMENT,
	CCDB_TEAM_ATT_FORWARD_PASS_DRIVE,
	CCDB_TEAM_ATT_FORWARD_CONTACT_OFFLOAD,
	CCDB_TEAM_ATT_BACK_PASS_KICK,
	CCDB_TEAM_ATT_BACK_CONTACT_OFFLOAD,
	CCDB_TEAM_ATT_LINEOUT_SIZE,
	CCDB_TEAM_ATT_LINEOUT_FAVOURED_TARGET,
	CCDB_TEAM_ATT_RUCK_WIN,
	CCDB_TEAM_ATT_LINEOUT_WIN,
	CCDB_TEAM_ATT_SCRUM_WIN,
	CCDB_TEAM_ATT_MAUL_WIN,
	CCDB_TEAM_ATT_LINE_WIDTH,
	CCDB_TEAM_ATT_LINE_DEPTH,
	CCDB_TEAM_ATT_RUCK_COMMITMENT,
	CCDB_TEAM_KICK_KICKOFF_SHORT_LONG,
	CCDB_TEAM_KICK_KICKOFF_LEFT_RIGHT,
	CCDB_TEAM_KICK_DROPOUT_SHORT_LONG,
	CCDB_TEAM_KICK_DROPOUT_LEFT_RIGHT,
	CCDB_TEAM_KICK_TOUCH_TERRITORY,
	CCDB_TEAM_KICK_PENALTY_TOUCH_GOAL,
	CCDB_TEAM_REP_AREA_ID,
	CCDB_TEAM_PRIMARY_COMP,
	CCDB_TEAM_REQUIRES_THE,
	CCDB_TEAM_CUSTOM,
	CCDB_TEAM_BOOT_TEXTURE_NUM,
	CCDB_TEAM_IS_LOCKED,
	CCDB_TEAM_DEF_POD_OPTION,
	CCDB_TEAM_MID_POD_OPTION,
	CCDB_TEAM_ATT_POD_OPTION,
	CCDB_TEAM_DAYS_TILL_REP_SELECTION_ALLOWED,
	CCDB_TEAM_R7_EXCLUSIVE,
	CCDB_TEAM_PERMISSION_FLAGS_GENDER,
	CCDB_TEAM_NAME,
	CCDB_TEAM_SHORT_NAME,
	CCDB_TEAM_ABBREV,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_player
struct rudb_player_row
{
	unsigned short db_id,pad;
	int	dob;
	int	position_category1_id;
	int	position_category2_id;
	int	position_category3_id;
	int	position_category1_r7_id;
	int	position_category2_r7_id;
	int	position_category3_r7_id;
	float	age_intensity;
	float	bleach_intensity;
	float	brow_line_intensity;
	float	crows_feet_intensity;
	float	eye_darkening_intensity;
	float	eyebrow_intensity;
	float	florid_intensity;
	float	freckle_intensity;
	float	frown_line_intensity;
	float	laugh_line_intensity;
	float	lower_mouth_intensity;
	float	mole_intensity;
	float	nose_intensity;
	float	philtrum_intensity;
	float	pimple_intensity;
	float	scar_intensity;
	float	stubble_intensity;
	int	bandage;
	unsigned short	nation;
	unsigned short	ethnicity;
	unsigned short	fitness;
	unsigned short	agility;
	unsigned short	speed;
	unsigned short	acceleration;
	unsigned short	aggressiveness;
	unsigned short	tackle_ability;
	unsigned short	break_tackle_ability;
	unsigned short	pass_accuracy;
	unsigned short	offload_ability;
	unsigned short	general_kick_accuracy;
	unsigned short	goal_kick_accuracy;
	unsigned short	catch_ability;
	unsigned short	strength;
	unsigned short	mental_agility;
	unsigned short	jump_ability;
	unsigned short	discipline;
	unsigned short	influence;
	unsigned short	star_factor;
	unsigned short	player_proportions_id;
	unsigned short	caps;
	unsigned short	head_mesh;
	unsigned short	head_texture;
	unsigned short	hair_mesh;
	unsigned short	hair_texture;
	unsigned short	beard_mesh;
	unsigned short	head_gear_club_mesh;
	unsigned short	head_gear_intl_mesh;
	unsigned short	tattoo_shoulder_l_texture;
	unsigned short	tattoo_shoulder_r_texture;
	unsigned short	tattoo_forearm_l_texture;
	unsigned short	tattoo_forearm_r_texture;
	unsigned short	tattoo_hand_l_texture;
	unsigned short	tattoo_hand_r_texture;
	unsigned short	tattoo_leg_l_texture;
	unsigned short	tattoo_leg_r_texture;
	unsigned char	custom;
	unsigned char	artist_customised;
	unsigned char	m_generic;
	unsigned char	height;
	unsigned char	weight;
	unsigned char	preferred_foot;
	unsigned char	franchise_region;
	unsigned char	rookie;
	unsigned char	license_restrict;
	unsigned char	r7_exclusive;
	unsigned char	skin_texture;
	unsigned char	shirt_style;
	unsigned char	sleeve;
	unsigned char	sock_style;
	unsigned char	eye_texture;
	unsigned char	age_texture;
	unsigned char	bleach_texture;
	unsigned char	brow_line_texture;
	unsigned char	crows_feet_texture;
	unsigned char	eye_darkening_texture;
	unsigned char	eyebrow_texture;
	unsigned char	florid_texture;
	unsigned char	freckle_texture;
	unsigned char	frown_line_texture;
	unsigned char	laugh_line_texture;
	unsigned char	lower_mouth_texture;
	unsigned char	mole_texture;
	unsigned char	nose_texture;
	unsigned char	philtrum_texture;
	unsigned char	pimple_texture;
	unsigned char	scar_texture;
	unsigned char	stubble_texture;
	unsigned char	mouth_guard_texture;
	unsigned char	shoes_club_mesh;
	unsigned char	shoes_club_texture;
	unsigned char	shoes_intl_mesh;
	unsigned char	shoes_intl_texture;
	unsigned char	head_gear_club_style;
	unsigned char	head_gear_club_texture;
	unsigned char	head_gear_intl_style;
	unsigned char	head_gear_intl_texture;
	unsigned char	gender;
	char	first_name[25];
	char	last_name[25];
	char	commentary_name[25];
	char	hair_tint_0[35];
	char	hair_tint_1[35];
	char	hair_tint_2[35];
	char	beard_tint_0[35];
	char	beard_tint_1[35];
	char	beard_tint_2[35];
	char	eyebrow_tint[35];
	char	stubble_tint[35];
	char	mouth_guard_tint_0[35];
	char	mouth_guard_tint_1[35];
	char	mouth_guard_tint_2[35];
	char	shoe_l_club_tint_0[35];
	char	shoe_l_club_tint_1[35];
	char	shoe_l_club_tint_2[35];
	char	shoe_r_club_tint_0[35];
	char	shoe_r_club_tint_1[35];
	char	shoe_r_club_tint_2[35];
	char	shoe_l_intl_tint_0[35];
	char	shoe_l_intl_tint_1[35];
	char	shoe_l_intl_tint_2[35];
	char	shoe_r_intl_tint_0[35];
	char	shoe_r_intl_tint_1[35];
	char	shoe_r_intl_tint_2[35];
	char	head_gear_club_tint_0[35];
	char	head_gear_club_tint_1[35];
	char	head_gear_club_tint_2[35];
	char	head_gear_intl_tint_0[35];
	char	head_gear_intl_tint_1[35];
	char	head_gear_intl_tint_2[35];
	char	sleeves_tint[35];
	char	row_padding[3];
};

///-----------------------------------------------------------
/// Enums for: rudb_player

enum {
	CCDB_PLAYER_DOB=0,
	CCDB_PLAYER_POSITION_CATEGORY1_ID,
	CCDB_PLAYER_POSITION_CATEGORY2_ID,
	CCDB_PLAYER_POSITION_CATEGORY3_ID,
	CCDB_PLAYER_POSITION_CATEGORY1_R7_ID,
	CCDB_PLAYER_POSITION_CATEGORY2_R7_ID,
	CCDB_PLAYER_POSITION_CATEGORY3_R7_ID,
	CCDB_PLAYER_AGE_INTENSITY,
	CCDB_PLAYER_BLEACH_INTENSITY,
	CCDB_PLAYER_BROW_LINE_INTENSITY,
	CCDB_PLAYER_CROWS_FEET_INTENSITY,
	CCDB_PLAYER_EYE_DARKENING_INTENSITY,
	CCDB_PLAYER_EYEBROW_INTENSITY,
	CCDB_PLAYER_FLORID_INTENSITY,
	CCDB_PLAYER_FRECKLE_INTENSITY,
	CCDB_PLAYER_FROWN_LINE_INTENSITY,
	CCDB_PLAYER_LAUGH_LINE_INTENSITY,
	CCDB_PLAYER_LOWER_MOUTH_INTENSITY,
	CCDB_PLAYER_MOLE_INTENSITY,
	CCDB_PLAYER_NOSE_INTENSITY,
	CCDB_PLAYER_PHILTRUM_INTENSITY,
	CCDB_PLAYER_PIMPLE_INTENSITY,
	CCDB_PLAYER_SCAR_INTENSITY,
	CCDB_PLAYER_STUBBLE_INTENSITY,
	CCDB_PLAYER_BANDAGE,
	CCDB_PLAYER_NATION,
	CCDB_PLAYER_ETHNICITY,
	CCDB_PLAYER_FITNESS,
	CCDB_PLAYER_AGILITY,
	CCDB_PLAYER_SPEED,
	CCDB_PLAYER_ACCELERATION,
	CCDB_PLAYER_AGGRESSIVENESS,
	CCDB_PLAYER_TACKLE_ABILITY,
	CCDB_PLAYER_BREAK_TACKLE_ABILITY,
	CCDB_PLAYER_PASS_ACCURACY,
	CCDB_PLAYER_OFFLOAD_ABILITY,
	CCDB_PLAYER_GENERAL_KICK_ACCURACY,
	CCDB_PLAYER_GOAL_KICK_ACCURACY,
	CCDB_PLAYER_CATCH_ABILITY,
	CCDB_PLAYER_STRENGTH,
	CCDB_PLAYER_MENTAL_AGILITY,
	CCDB_PLAYER_JUMP_ABILITY,
	CCDB_PLAYER_DISCIPLINE,
	CCDB_PLAYER_INFLUENCE,
	CCDB_PLAYER_STAR_FACTOR,
	CCDB_PLAYER_PLAYER_PROPORTIONS_ID,
	CCDB_PLAYER_CAPS,
	CCDB_PLAYER_HEAD_MESH,
	CCDB_PLAYER_HEAD_TEXTURE,
	CCDB_PLAYER_HAIR_MESH,
	CCDB_PLAYER_HAIR_TEXTURE,
	CCDB_PLAYER_BEARD_MESH,
	CCDB_PLAYER_HEAD_GEAR_CLUB_MESH,
	CCDB_PLAYER_HEAD_GEAR_INTL_MESH,
	CCDB_PLAYER_TATTOO_SHOULDER_L_TEXTURE,
	CCDB_PLAYER_TATTOO_SHOULDER_R_TEXTURE,
	CCDB_PLAYER_TATTOO_FOREARM_L_TEXTURE,
	CCDB_PLAYER_TATTOO_FOREARM_R_TEXTURE,
	CCDB_PLAYER_TATTOO_HAND_L_TEXTURE,
	CCDB_PLAYER_TATTOO_HAND_R_TEXTURE,
	CCDB_PLAYER_TATTOO_LEG_L_TEXTURE,
	CCDB_PLAYER_TATTOO_LEG_R_TEXTURE,
	CCDB_PLAYER_CUSTOM,
	CCDB_PLAYER_ARTIST_CUSTOMISED,
	CCDB_PLAYER_M_GENERIC,
	CCDB_PLAYER_HEIGHT,
	CCDB_PLAYER_WEIGHT,
	CCDB_PLAYER_PREFERRED_FOOT,
	CCDB_PLAYER_FRANCHISE_REGION,
	CCDB_PLAYER_ROOKIE,
	CCDB_PLAYER_LICENSE_RESTRICT,
	CCDB_PLAYER_R7_EXCLUSIVE,
	CCDB_PLAYER_SKIN_TEXTURE,
	CCDB_PLAYER_SHIRT_STYLE,
	CCDB_PLAYER_SLEEVE,
	CCDB_PLAYER_SOCK_STYLE,
	CCDB_PLAYER_EYE_TEXTURE,
	CCDB_PLAYER_AGE_TEXTURE,
	CCDB_PLAYER_BLEACH_TEXTURE,
	CCDB_PLAYER_BROW_LINE_TEXTURE,
	CCDB_PLAYER_CROWS_FEET_TEXTURE,
	CCDB_PLAYER_EYE_DARKENING_TEXTURE,
	CCDB_PLAYER_EYEBROW_TEXTURE,
	CCDB_PLAYER_FLORID_TEXTURE,
	CCDB_PLAYER_FRECKLE_TEXTURE,
	CCDB_PLAYER_FROWN_LINE_TEXTURE,
	CCDB_PLAYER_LAUGH_LINE_TEXTURE,
	CCDB_PLAYER_LOWER_MOUTH_TEXTURE,
	CCDB_PLAYER_MOLE_TEXTURE,
	CCDB_PLAYER_NOSE_TEXTURE,
	CCDB_PLAYER_PHILTRUM_TEXTURE,
	CCDB_PLAYER_PIMPLE_TEXTURE,
	CCDB_PLAYER_SCAR_TEXTURE,
	CCDB_PLAYER_STUBBLE_TEXTURE,
	CCDB_PLAYER_MOUTH_GUARD_TEXTURE,
	CCDB_PLAYER_SHOES_CLUB_MESH,
	CCDB_PLAYER_SHOES_CLUB_TEXTURE,
	CCDB_PLAYER_SHOES_INTL_MESH,
	CCDB_PLAYER_SHOES_INTL_TEXTURE,
	CCDB_PLAYER_HEAD_GEAR_CLUB_STYLE,
	CCDB_PLAYER_HEAD_GEAR_CLUB_TEXTURE,
	CCDB_PLAYER_HEAD_GEAR_INTL_STYLE,
	CCDB_PLAYER_HEAD_GEAR_INTL_TEXTURE,
	CCDB_PLAYER_GENDER,
	CCDB_PLAYER_FIRST_NAME,
	CCDB_PLAYER_LAST_NAME,
	CCDB_PLAYER_COMMENTARY_NAME,
	CCDB_PLAYER_HAIR_TINT_0,
	CCDB_PLAYER_HAIR_TINT_1,
	CCDB_PLAYER_HAIR_TINT_2,
	CCDB_PLAYER_BEARD_TINT_0,
	CCDB_PLAYER_BEARD_TINT_1,
	CCDB_PLAYER_BEARD_TINT_2,
	CCDB_PLAYER_EYEBROW_TINT,
	CCDB_PLAYER_STUBBLE_TINT,
	CCDB_PLAYER_MOUTH_GUARD_TINT_0,
	CCDB_PLAYER_MOUTH_GUARD_TINT_1,
	CCDB_PLAYER_MOUTH_GUARD_TINT_2,
	CCDB_PLAYER_SHOE_L_CLUB_TINT_0,
	CCDB_PLAYER_SHOE_L_CLUB_TINT_1,
	CCDB_PLAYER_SHOE_L_CLUB_TINT_2,
	CCDB_PLAYER_SHOE_R_CLUB_TINT_0,
	CCDB_PLAYER_SHOE_R_CLUB_TINT_1,
	CCDB_PLAYER_SHOE_R_CLUB_TINT_2,
	CCDB_PLAYER_SHOE_L_INTL_TINT_0,
	CCDB_PLAYER_SHOE_L_INTL_TINT_1,
	CCDB_PLAYER_SHOE_L_INTL_TINT_2,
	CCDB_PLAYER_SHOE_R_INTL_TINT_0,
	CCDB_PLAYER_SHOE_R_INTL_TINT_1,
	CCDB_PLAYER_SHOE_R_INTL_TINT_2,
	CCDB_PLAYER_HEAD_GEAR_CLUB_TINT_0,
	CCDB_PLAYER_HEAD_GEAR_CLUB_TINT_1,
	CCDB_PLAYER_HEAD_GEAR_CLUB_TINT_2,
	CCDB_PLAYER_HEAD_GEAR_INTL_TINT_0,
	CCDB_PLAYER_HEAD_GEAR_INTL_TINT_1,
	CCDB_PLAYER_HEAD_GEAR_INTL_TINT_2,
	CCDB_PLAYER_SLEEVES_TINT,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_comp_def
struct rudb_comp_def_row
{
	unsigned short db_id,pad;
	int	start_date;
	int	salary_cap;
	int	law_variation;
	unsigned short	icon_id;
	unsigned short	trophy_id;
	unsigned short	num_preliminary_rounds;
	unsigned short	num_finals_rounds;
	unsigned short	num_advancing;
	unsigned short	original_id;
	unsigned short	cup_id;
	unsigned short	plate_id;
	unsigned short	bowl_id;
	unsigned short	shield_id;
	unsigned char	excluded;
	unsigned char	custom;
	unsigned char	preliminary_format;
	unsigned char	preliminary_attribute_1;
	unsigned char	preliminary_attribute_2;
	unsigned char	preliminary_attribute_3;
	unsigned char	num_matches_against_each_opponent;
	unsigned char	finals_format;
	unsigned char	preliminary_golden_point_type;
	unsigned char	finals_golden_point_type;
	unsigned char	win_points;
	unsigned char	loss_points;
	unsigned char	draw_points;
	unsigned char	bye_points;
	unsigned char	bonus_points;
	unsigned char	bonus_attacking_threshold;
	unsigned char	bonus_defending_threshold;
	unsigned char	bonus_attacking_is_relative;
	unsigned char	franchise_region;
	unsigned char	competition_type;
	unsigned char	friendly;
	unsigned char	pool_name;
	unsigned char	is_tour;
	unsigned char	r7_exclusive;
	unsigned char	permission_flags_gender;
	char	name[32];
	char	mnemonic[4];
	char	row_padding[3];
};

///-----------------------------------------------------------
/// Enums for: rudb_comp_def

enum {
	CCDB_COMPDEF_START_DATE=0,
	CCDB_COMPDEF_SALARY_CAP,
	CCDB_COMPDEF_LAW_VARIATION,
	CCDB_COMPDEF_ICON_ID,
	CCDB_COMPDEF_TROPHY_ID,
	CCDB_COMPDEF_NUM_PRELIMINARY_ROUNDS,
	CCDB_COMPDEF_NUM_FINALS_ROUNDS,
	CCDB_COMPDEF_NUM_ADVANCING,
	CCDB_COMPDEF_ORIGINAL_ID,
	CCDB_COMPDEF_CUP_ID,
	CCDB_COMPDEF_PLATE_ID,
	CCDB_COMPDEF_BOWL_ID,
	CCDB_COMPDEF_SHIELD_ID,
	CCDB_COMPDEF_EXCLUDED,
	CCDB_COMPDEF_CUSTOM,
	CCDB_COMPDEF_PRELIMINARY_FORMAT,
	CCDB_COMPDEF_PRELIMINARY_ATTRIBUTE_1,
	CCDB_COMPDEF_PRELIMINARY_ATTRIBUTE_2,
	CCDB_COMPDEF_PRELIMINARY_ATTRIBUTE_3,
	CCDB_COMPDEF_NUM_MATCHES_AGAINST_EACH_OPPONENT,
	CCDB_COMPDEF_FINALS_FORMAT,
	CCDB_COMPDEF_PRELIMINARY_GOLDEN_POINT_TYPE,
	CCDB_COMPDEF_FINALS_GOLDEN_POINT_TYPE,
	CCDB_COMPDEF_WIN_POINTS,
	CCDB_COMPDEF_LOSS_POINTS,
	CCDB_COMPDEF_DRAW_POINTS,
	CCDB_COMPDEF_BYE_POINTS,
	CCDB_COMPDEF_BONUS_POINTS,
	CCDB_COMPDEF_BONUS_ATTACKING_THRESHOLD,
	CCDB_COMPDEF_BONUS_DEFENDING_THRESHOLD,
	CCDB_COMPDEF_BONUS_ATTACKING_IS_RELATIVE,
	CCDB_COMPDEF_FRANCHISE_REGION,
	CCDB_COMPDEF_COMPETITION_TYPE,
	CCDB_COMPDEF_FRIENDLY,
	CCDB_COMPDEF_POOL_NAME,
	CCDB_COMPDEF_IS_TOUR,
	CCDB_COMPDEF_R7_EXCLUSIVE,
	CCDB_COMPDEF_PERMISSION_FLAGS_GENDER,
	CCDB_COMPDEF_NAME,
	CCDB_COMPDEF_MNEMONIC,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_team_server_details
struct rudb_team_server_details_row
{
	unsigned short db_id,pad;
	unsigned short	team_id;
	char	created_by[30];
	char	uploaded_by[30];
	char	upload_id[35];
	char	download_id_server[35];
	char	download_id_user[35];
	char	platform_id_creator[32];
	char	platform_id_uploader[32];
	char	row_padding[1];
};

///-----------------------------------------------------------
/// Enums for: rudb_team_server_details

enum {
	CCDB_TEAMSERVERDETAILS_TEAM_ID=0,
	CCDB_TEAMSERVERDETAILS_CREATED_BY,
	CCDB_TEAMSERVERDETAILS_UPLOADED_BY,
	CCDB_TEAMSERVERDETAILS_UPLOAD_ID,
	CCDB_TEAMSERVERDETAILS_DOWNLOAD_ID_SERVER,
	CCDB_TEAMSERVERDETAILS_DOWNLOAD_ID_USER,
	CCDB_TEAMSERVERDETAILS_PLATFORM_ID_CREATOR,
	CCDB_TEAMSERVERDETAILS_PLATFORM_ID_UPLOADER,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_player_server_details
struct rudb_player_server_details_row
{
	unsigned short db_id,pad;
	unsigned short	player_id;
	char	created_by[30];
	char	uploaded_by[30];
	char	upload_id[35];
	char	download_id_server[35];
	char	download_id_user[35];
	char	platform_id_creator[32];
	char	platform_id_uploader[32];
	char	row_padding[1];
};

///-----------------------------------------------------------
/// Enums for: rudb_player_server_details

enum {
	CCDB_PLAYERSERVERDETAILS_PLAYER_ID=0,
	CCDB_PLAYERSERVERDETAILS_CREATED_BY,
	CCDB_PLAYERSERVERDETAILS_UPLOADED_BY,
	CCDB_PLAYERSERVERDETAILS_UPLOAD_ID,
	CCDB_PLAYERSERVERDETAILS_DOWNLOAD_ID_SERVER,
	CCDB_PLAYERSERVERDETAILS_DOWNLOAD_ID_USER,
	CCDB_PLAYERSERVERDETAILS_PLATFORM_ID_CREATOR,
	CCDB_PLAYERSERVERDETAILS_PLATFORM_ID_UPLOADER,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_team_strip
struct rudb_team_strip_row
{
	unsigned short db_id,pad;
	unsigned short	texture_num;
	unsigned short	number_texture_num;
	unsigned char	is_home_strip;
	unsigned char	collar_style;
	unsigned char	primary_r;
	unsigned char	primary_g;
	unsigned char	primary_b;
	unsigned char	secondary_r;
	unsigned char	secondary_g;
	unsigned char	secondary_b;
	unsigned char	tertiary_r;
	unsigned char	tertiary_g;
	unsigned char	tertiary_b;
	unsigned char	primary_percent;
	unsigned char	boot_texture_num;
	unsigned char	boot_primary_r;
	unsigned char	boot_primary_g;
	unsigned char	boot_primary_b;
	unsigned char	boot_secondary_r;
	unsigned char	boot_secondary_g;
	unsigned char	boot_secondary_b;
	unsigned char	boot_tertiary_r;
	unsigned char	boot_tertiary_g;
	unsigned char	boot_tertiary_b;
	unsigned char	custom;
	char	name[27];
	char	created_by[35];
	char	uploaded_by[35];
	char	created_by_id[35];
	char	uploaded_by_id[35];
	char	platform_id_creator[35];
	char	platform_id_uploader[35];
};

///-----------------------------------------------------------
/// Enums for: rudb_team_strip

enum {
	CCDB_TEAMSTRIP_TEXTURE_NUM=0,
	CCDB_TEAMSTRIP_NUMBER_TEXTURE_NUM,
	CCDB_TEAMSTRIP_IS_HOME_STRIP,
	CCDB_TEAMSTRIP_COLLAR_STYLE,
	CCDB_TEAMSTRIP_PRIMARY_R,
	CCDB_TEAMSTRIP_PRIMARY_G,
	CCDB_TEAMSTRIP_PRIMARY_B,
	CCDB_TEAMSTRIP_SECONDARY_R,
	CCDB_TEAMSTRIP_SECONDARY_G,
	CCDB_TEAMSTRIP_SECONDARY_B,
	CCDB_TEAMSTRIP_TERTIARY_R,
	CCDB_TEAMSTRIP_TERTIARY_G,
	CCDB_TEAMSTRIP_TERTIARY_B,
	CCDB_TEAMSTRIP_PRIMARY_PERCENT,
	CCDB_TEAMSTRIP_BOOT_TEXTURE_NUM,
	CCDB_TEAMSTRIP_BOOT_PRIMARY_R,
	CCDB_TEAMSTRIP_BOOT_PRIMARY_G,
	CCDB_TEAMSTRIP_BOOT_PRIMARY_B,
	CCDB_TEAMSTRIP_BOOT_SECONDARY_R,
	CCDB_TEAMSTRIP_BOOT_SECONDARY_G,
	CCDB_TEAMSTRIP_BOOT_SECONDARY_B,
	CCDB_TEAMSTRIP_BOOT_TERTIARY_R,
	CCDB_TEAMSTRIP_BOOT_TERTIARY_G,
	CCDB_TEAMSTRIP_BOOT_TERTIARY_B,
	CCDB_TEAMSTRIP_CUSTOM,
	CCDB_TEAMSTRIP_NAME,
	CCDB_TEAMSTRIP_CREATED_BY,
	CCDB_TEAMSTRIP_UPLOADED_BY,
	CCDB_TEAMSTRIP_CREATED_BY_ID,
	CCDB_TEAMSTRIP_UPLOADED_BY_ID,
	CCDB_TEAMSTRIP_PLATFORM_ID_CREATOR,
	CCDB_TEAMSTRIP_PLATFORM_ID_UPLOADER,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_career_mode_state
struct rudb_career_mode_state_row
{
	unsigned short db_id,pad;
	int	game_difficulty;
	int	game_length;
	int	substitution_mode;
	int	career_mode_date;
	float	pro_progress_club_cp;
	float	pro_progress_club_pk;
	float	pro_progress_club_gk;
	float	pro_progress_int_cp;
	float	pro_progress_int_pk;
	float	pro_progress_int_gk;
	int	career_start_date;
	unsigned short	pro_international_allegiance_id;
	unsigned short	international_team_id;
	unsigned short	club_team_id;
	unsigned short	club_team1_id;
	unsigned short	club_team2_id;
	unsigned short	club_team3_id;
	unsigned short	club_team4_id;
	unsigned short	club_team5_id;
	unsigned short	club_team6_id;
	unsigned short	club_team7_id;
	unsigned short	pro_int_comp0_id;
	unsigned short	pro_int_comp1_id;
	unsigned short	pro_int_comp2_id;
	unsigned short	pro_int_comp3_id;
	unsigned short	pro_int_comp4_id;
	unsigned short	total_pro_club_team_count;
	unsigned short	ranfurly_team_id;
	unsigned short	current_season_match_index;
	unsigned short	franchise_definition_index;
	unsigned short	primary_team_db_index;
	unsigned short	finals_fails;
	unsigned short	player_rep_losing_streak;
	unsigned short	player_losing_streak;
	unsigned short	primary_comp_db_index;
	unsigned short	primary_player_db_id;
	unsigned short	club_contract_tier;
	unsigned short	analytic_id;
	unsigned char	has_new_email;
	unsigned char	auto_player_training;
	unsigned char	auto_player_trading;
	unsigned char	auto_squad_selection;
	unsigned char	is_pro_not_signed;
	unsigned char	is_fired;
	unsigned char	can_be_fired;
	unsigned char	unlimited_salary_enabled;
	unsigned char	rep_performance_warning;
	unsigned char	performance_warning;
	unsigned char	manager_difficulty;
	unsigned char	is_franchise;
	unsigned char	post_competition_mode;
	unsigned char	done_initial_drafting;
	unsigned char	career_game_mode_sevens;
	unsigned char	pro_promoted_club_cp;
	unsigned char	pro_promoted_club_pk;
	unsigned char	pro_promoted_club_gk;
	unsigned char	pro_promoted_int_cp;
	unsigned char	pro_promoted_int_pk;
	unsigned char	pro_promoted_int_gk;
	unsigned char	autosave;
	char	manager_name[32];
};

///-----------------------------------------------------------
/// Enums for: rudb_career_mode_state

enum {
	CCDB_CAREERMODESTATE_GAME_DIFFICULTY=0,
	CCDB_CAREERMODESTATE_GAME_LENGTH,
	CCDB_CAREERMODESTATE_SUBSTITUTION_MODE,
	CCDB_CAREERMODESTATE_CAREER_MODE_DATE,
	CCDB_CAREERMODESTATE_PRO_PROGRESS_CLUB_CP,
	CCDB_CAREERMODESTATE_PRO_PROGRESS_CLUB_PK,
	CCDB_CAREERMODESTATE_PRO_PROGRESS_CLUB_GK,
	CCDB_CAREERMODESTATE_PRO_PROGRESS_INT_CP,
	CCDB_CAREERMODESTATE_PRO_PROGRESS_INT_PK,
	CCDB_CAREERMODESTATE_PRO_PROGRESS_INT_GK,
	CCDB_CAREERMODESTATE_CAREER_START_DATE,
	CCDB_CAREERMODESTATE_PRO_INTERNATIONAL_ALLEGIANCE_ID,
	CCDB_CAREERMODESTATE_INTERNATIONAL_TEAM_ID,
	CCDB_CAREERMODESTATE_CLUB_TEAM_ID,
	CCDB_CAREERMODESTATE_CLUB_TEAM1_ID,
	CCDB_CAREERMODESTATE_CLUB_TEAM2_ID,
	CCDB_CAREERMODESTATE_CLUB_TEAM3_ID,
	CCDB_CAREERMODESTATE_CLUB_TEAM4_ID,
	CCDB_CAREERMODESTATE_CLUB_TEAM5_ID,
	CCDB_CAREERMODESTATE_CLUB_TEAM6_ID,
	CCDB_CAREERMODESTATE_CLUB_TEAM7_ID,
	CCDB_CAREERMODESTATE_PRO_INT_COMP0_ID,
	CCDB_CAREERMODESTATE_PRO_INT_COMP1_ID,
	CCDB_CAREERMODESTATE_PRO_INT_COMP2_ID,
	CCDB_CAREERMODESTATE_PRO_INT_COMP3_ID,
	CCDB_CAREERMODESTATE_PRO_INT_COMP4_ID,
	CCDB_CAREERMODESTATE_TOTAL_PRO_CLUB_TEAM_COUNT,
	CCDB_CAREERMODESTATE_RANFURLY_TEAM_ID,
	CCDB_CAREERMODESTATE_CURRENT_SEASON_MATCH_INDEX,
	CCDB_CAREERMODESTATE_FRANCHISE_DEFINITION_INDEX,
	CCDB_CAREERMODESTATE_PRIMARY_TEAM_DB_INDEX,
	CCDB_CAREERMODESTATE_FINALS_FAILS,
	CCDB_CAREERMODESTATE_PLAYER_REP_LOSING_STREAK,
	CCDB_CAREERMODESTATE_PLAYER_LOSING_STREAK,
	CCDB_CAREERMODESTATE_PRIMARY_COMP_DB_INDEX,
	CCDB_CAREERMODESTATE_PRIMARY_PLAYER_DB_ID,
	CCDB_CAREERMODESTATE_CLUB_CONTRACT_TIER,
	CCDB_CAREERMODESTATE_ANALYTIC_ID,
	CCDB_CAREERMODESTATE_HAS_NEW_EMAIL,
	CCDB_CAREERMODESTATE_AUTO_PLAYER_TRAINING,
	CCDB_CAREERMODESTATE_AUTO_PLAYER_TRADING,
	CCDB_CAREERMODESTATE_AUTO_SQUAD_SELECTION,
	CCDB_CAREERMODESTATE_IS_PRO_NOT_SIGNED,
	CCDB_CAREERMODESTATE_IS_FIRED,
	CCDB_CAREERMODESTATE_CAN_BE_FIRED,
	CCDB_CAREERMODESTATE_UNLIMITED_SALARY_ENABLED,
	CCDB_CAREERMODESTATE_REP_PERFORMANCE_WARNING,
	CCDB_CAREERMODESTATE_PERFORMANCE_WARNING,
	CCDB_CAREERMODESTATE_MANAGER_DIFFICULTY,
	CCDB_CAREERMODESTATE_IS_FRANCHISE,
	CCDB_CAREERMODESTATE_POST_COMPETITION_MODE,
	CCDB_CAREERMODESTATE_DONE_INITIAL_DRAFTING,
	CCDB_CAREERMODESTATE_CAREER_GAME_MODE_SEVENS,
	CCDB_CAREERMODESTATE_PRO_PROMOTED_CLUB_CP,
	CCDB_CAREERMODESTATE_PRO_PROMOTED_CLUB_PK,
	CCDB_CAREERMODESTATE_PRO_PROMOTED_CLUB_GK,
	CCDB_CAREERMODESTATE_PRO_PROMOTED_INT_CP,
	CCDB_CAREERMODESTATE_PRO_PROMOTED_INT_PK,
	CCDB_CAREERMODESTATE_PRO_PROMOTED_INT_GK,
	CCDB_CAREERMODESTATE_AUTOSAVE,
	CCDB_CAREERMODESTATE_MANAGER_NAME,
};


///-----------------------------------------------------------
/// Debug struct for: rurec_records
struct rurec_records_row
{
	unsigned short db_id,pad;
	int	date;
	int	alt_date;
	unsigned short	comp_def_id;
	unsigned short	record_type;
	unsigned short	value;
	unsigned short	alt_value;
	unsigned char	competing_collective;
	unsigned char	competing_timeframe;
	unsigned char	ordinal;
	char	player_name[17];
	char	team[13];
	char	alt_team[16];
	char	location[13];
	char	row_padding[2];
};

///-----------------------------------------------------------
/// Enums for: rurec_records

enum {
	CCDB_RURECRECORDS_DATE=0,
	CCDB_RURECRECORDS_ALT_DATE,
	CCDB_RURECRECORDS_COMP_DEF_ID,
	CCDB_RURECRECORDS_RECORD_TYPE,
	CCDB_RURECRECORDS_VALUE,
	CCDB_RURECRECORDS_ALT_VALUE,
	CCDB_RURECRECORDS_COMPETING_COLLECTIVE,
	CCDB_RURECRECORDS_COMPETING_TIMEFRAME,
	CCDB_RURECRECORDS_ORDINAL,
	CCDB_RURECRECORDS_PLAYER_NAME,
	CCDB_RURECRECORDS_TEAM,
	CCDB_RURECRECORDS_ALT_TEAM,
	CCDB_RURECRECORDS_LOCATION,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_home_stadium
struct rudb_home_stadium_row
{
	unsigned short db_id,pad;
	float	weight;
	unsigned short	team_id;
	unsigned short	stadium_id;
};

///-----------------------------------------------------------
/// Enums for: rudb_home_stadium

enum {
	CCDB_HOMESTADIUM_WEIGHT=0,
	CCDB_HOMESTADIUM_TEAM_ID,
	CCDB_HOMESTADIUM_STADIUM_ID,
};


///-----------------------------------------------------------
/// Debug struct for: rudb_franchise_comp
struct rudb_franchise_comp_row
{
	unsigned short db_id,pad;
	int	start_date;
	unsigned short	franchise_id;
	unsigned short	comp_id;
};

///-----------------------------------------------------------
/// Enums for: rudb_franchise_comp

enum {
	CCDB_FRANCHISECOMP_START_DATE=0,
	CCDB_FRANCHISECOMP_FRANCHISE_ID,
	CCDB_FRANCHISECOMP_COMP_ID,
};


///---------------------------------------------------
/// Cache table indexes
enum {
	CACHE_RUDB_ATTRIBUTE_PRESETS=0,
	CACHE_RUDB_CAREER_DRAFTABLE_PLAYER,
	CACHE_RUDB_CAREER_INBOX,
	CACHE_RUDB_CAREER_MODE_COMP,
	CACHE_RUDB_CAREER_MY_CONTRACTS,
	CACHE_RUDB_CAREER_PLAYED_MATCH,
	CACHE_RUDB_CAREER_PRO_CONTRACT_INTEREST,
	CACHE_RUDB_CAREER_PRO_PLAYER_MATCHES,
	CACHE_RUDB_CAREER_PRO_PLAYER_STATS,
	CACHE_RUDB_CITY,
	CACHE_RUDB_COMP_DEF_MATCH,
	CACHE_RUDB_COMP_DEF_ROUND,
	CACHE_RUDB_COMP_DEF_SAT_NAME,
	CACHE_RUDB_COMP_DEF_TEAM,
	CACHE_RUDB_COMP_INST,
	CACHE_RUDB_COMP_INST_MATCH,
	CACHE_RUDB_COMP_INST_PLAYER_MATCH_STATS,
	CACHE_RUDB_COMP_INST_PLAYER_REMOVAL,
	CACHE_RUDB_COMP_INST_PLAYER_STATS,
	CACHE_RUDB_COMP_INST_ROUND,
	CACHE_RUDB_COMP_INST_TEAM,
	CACHE_RUDB_COMP_INST_TEAM_RESULT,
	CACHE_RUDB_COMP_INST_TEAM_STATS,
	CACHE_RUDB_COMP_MODE_STATE,
	CACHE_RUDB_COMP_TROPHY,
	CACHE_RUDB_CONTRACT_NEGOTIATION,
	CACHE_RUDB_COUNTRY,
	CACHE_RUDB_EMAILS,
	CACHE_RUDB_FINALS_FORMAT,
	CACHE_RUDB_FRANCHISE_CONSTANTS,
	CACHE_RUDB_FRANCHISE_DEF,
	CACHE_RUDB_GOLDEN_POINT_TYPE,
	CACHE_RUDB_INJURIES,
	CACHE_RUDB_LINEUP,
	CACHE_RUDB_LINEUP_NEW,
	CACHE_RUDB_PENDING_LINEUP,
	CACHE_RUDB_PENDING_LINEUP_NEW,
	CACHE_RUDB_PLAYER_EXTRA,
	CACHE_RUDB_PLAYER_LINK,
	CACHE_RUDB_PLAYER_PERFORMANCE_VALUES,
	CACHE_RUDB_PLAYER_POSITION,
	CACHE_RUDB_PLAYER_PROPORTIONS,
	CACHE_RUDB_PLAYER_RECORDS,
	CACHE_RUDB_PRELIMINARIES_FORMAT,
	CACHE_RUDB_PRO_GOALS,
	CACHE_RUDB_PRO_GOALS_GENERAL,
	CACHE_RUDB_RECORDS,
	CACHE_RUDB_REP_AREA,
	CACHE_RUDB_REP_AREA_COUNTRY,
	CACHE_RUDB_SCHEMA_VERSION,
	CACHE_RUDB_STATS_PLAYER,
	CACHE_RUDB_STATS_TEAM,
	CACHE_RUDB_SUSPENSION_OFFENCES,
	CACHE_RUDB_TEAM_COMMENTARY_NAME,
	CACHE_RUDB_TEAM_LINK,
	CACHE_RUDB_TEAM_RECORDS,
	CACHE_RUDB_TICKER_MESSAGES,
	CACHE_RUDB_UI_TEAM,
	CACHE_RUDB_PLAYER_MORPHTARGETS,
	CACHE_RUDB_STADIUM,
	CACHE_RUDB_ICON,
	CACHE_RUDB_TEAM_LOGO,
	CACHE_RUDB_TEAM,
	CACHE_RUDB_PLAYER,
	CACHE_RUDB_COMP_DEF,
	CACHE_RUDB_TEAM_SERVER_DETAILS,
	CACHE_RUDB_PLAYER_SERVER_DETAILS,
	CACHE_RUDB_TEAM_STRIP,
	CACHE_RUDB_CAREER_MODE_STATE,
	CACHE_RUREC_RECORDS,
	CACHE_RUDB_HOME_STADIUM,
	CACHE_RUDB_FRANCHISE_COMP,
};

///-----------------------------------------------------

#define DB_DATABASE_VERSION 332398212
#define DB_SCHEMA_VERSION 2021012110
#define DB_FIRST_PLAYER_ID 1001
#define DB_LAST_PLAYER_ID 6427
#define DB_LAST_TEAM_ID 1192
#define DB_LAST_STRIP_ID 3003
#define DB_LAST_LOGO_ID 1201

