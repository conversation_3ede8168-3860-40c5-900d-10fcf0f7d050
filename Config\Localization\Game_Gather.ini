[CommonSettings]
SourcePath=Content/Localization/Game
DestinationPath=Content/Localization/Game
ManifestName=Game.manifest
ArchiveName=Game.archive
NativeCulture=en
CulturesToGenerate=en
CulturesToGenerate=fr
CulturesToGenerate=it
CulturesToGenerate=es

[GatherTextStep0]
CommandletClass=GatherTextFromAssets
IncludePathFilters=%LOCPROJECTROOT%Content/UI/Datatables/LocalisationStringtables/English/*
ExcludePathFilters=Content/Localization/*
PackageFileNameFilters=*.umap
PackageFileNameFilters=*.uasset
ShouldExcludeDerivedClasses=false
ShouldGatherFromEditorOnlyData=false
SkipGatherCache=false

[GatherTextStep1]
CommandletClass=GenerateGatherManifest

[GatherTextStep2]
CommandletClass=GenerateGatherArchive

[GatherTextStep3]
CommandletClass=GenerateTextLocalizationReport
bWordCountReport=true
WordCountReportName=Game.csv
bConflictReport=true
ConflictReportName=Game_Conflicts.txt

