[OnlineSubsystem]
DefaultPlatformService=Switch
MaxLocalTalkers=4
bHasVoiceEnabled=false

[OnlineSubsystemSwitch]
bEnabled=true
bHasVoiceEnabled=false

[OnlineSubsystemSwitch.TitleFile]
ServiceLabel=0

[Audio]
AudioDeviceModuleName=
AudioMixerModuleName=

; Map Leaderboard (Unreal) names and properties to SceNpScoreBoardIds (Sony Network)
[LeaderboardsSwitch]
+RugbyMatchResults15 = 0
+RugbyMatchResults7 = 1

[/Script/SwitchRuntimeSettings.SwitchRuntimeSettings]
bUseMobileForwardRenderer=False
bUseForwardShading=False
bSupportMobileAndDeferred=False
bUseASTCTextures=False
bUseDXTForUITextures=False
bUseDistanceFields=False
bUseSparseTextures=True
TouchScreenUsage=None
bSupportFourFingerTap=True
JoyConDeadZone=0.200000
JoyConSensitivity=1.000000
ProControllerDeadZone=0.100000
ProControllerSensitivity=1.000000
VibrationAttenuationHF=1.000000
VibrationAttenuationLF=1.000000
InitialMaxControllers=4
InitialMinControllers=1
InitialEnableDualStickControllers=True
InitialEnableSingleStickControllers=False
InitialUseHorizontalSingleStick=True
InitialEnableSixAxisSensors=False
InitialUseRightJoyConForMotion=False
InitialZeroPointDriftMode=0
ProgramId=0x01006f60243d0000
ApplicationVersion=2
ApplicationVersionString=1.0.2
-SupportedLanguages=AmericanEnglish
+SupportedLanguages=AmericanEnglish
+SupportedLanguages=French
+SupportedLanguages=Spanish
+SupportedLanguages=Italian
+LocalizedTitles=(Language=AmericanEnglish,Title="Rugby League",Publisher="Home Entertainment Suppliers P/L")
+LocalizedTitles=(Language=French,Title="Rugby League",Publisher="Home Entertainment Suppliers P/L")
+LocalizedTitles=(Language=Spanish,Title="Rugby League",Publisher="Home Entertainment Suppliers P/L")
+LocalizedTitles=(Language=Italian,Title="Rugby League",Publisher="Home Entertainment Suppliers P/L")
+ParentalControls=FreeCommunication
+AgeRatings=(Organization=ACB,Age=0)
+AgeRatings=(Organization=ClassInd,Age=0)
+AgeRatings=(Organization=ESRB,Age=6)
+AgeRatings=(Organization=OFLC,Age=0)
+AgeRatings=(Organization=PEGI,Age=3)
+AgeRatings=(Organization=Russian,Age=0)
+AgeRatings=(Organization=USK,Age=0)
+AgeRatings=(Organization=PEGIBBFC,Age=3)
+AgeRatings=(Organization=CERO,Age=0)
+AgeRatings=(Organization=GRACGCRB,Age=0)
bGenerateSymbols=False
MainThreadStackSizeKB=64
DebugMainThreadStackSizeKB=80
SystemResourceSizeMB=80
SaveGameSizeKB=57344
SaveGameJournalSizeKB=17408
SaveGameSizeMaxKB=336896
SaveGameSizeMaxMB=329
SaveGameJournalSizeMaxKB=17408
SaveGameJournalSizeMaxMB=17
SaveGameOwnerId=
bShowDataLossConfirmationDialog=True
bRouteGameUserSettingsToSaveGame=False
TempDataSizeKB=0
CacheDataSizeKB=0
CacheDataJournalSizeKB=0
StartupAccountMode=Required
bStartupUserAccountIsOptional=False
bUserAccountSwitchLock=False
LocalCommunicationId=
LdnPassphrase=aoiujkhgteughnbv
FriendPresenceGroupId=
GameServerId=********
GameServerAccessKey=Aex2Iequ
bEnableNEXLibrary=True
ConserveBatteryLife=False
ApplicationErrorCode=
bAllowScreenshots=True
bAllowVideoCapture=True
bAllowCrashReporting=False
bEnableCustomCrashHandling=False
bSuspendWhileInBackground=True
bDemoBuild=False
bAllowAOCInstallWhileRunning=False
bDisableCPUBoostDuringLoad=False
LogoType=LicensedByNintendo
bUseManualLogoHide=False
SystemHelpUrl=
BrowserCallbackUrl=
BrowserCallbackableUrl=
bBindBrowserToUser=False
bShowBrowserPointer=False
MaxNumAudioSources=0
OpusBehavior=Default
MaxConcurrentHardwareDecodes=0
AudioMemoryPoolInitialSizeKB=3072
AudioMemoryPoolIncreaseSizeKB=256
SpatializationPlugin=
ReverbPlugin=
OcclusionPlugin=
CompressionOverrides=(bOverrideCompressionTimes=False,DurationThreshold=5.000000,MaxNumRandomBranches=0,SoundCueQualityIndex=0)
bResampleForDevice=False
MaxSampleRate=0.000000
HighSampleRate=0.000000
MedSampleRate=0.000000
LowSampleRate=0.000000
MinSampleRate=0.000000
CompressionQualityModifier=0.000000
AutoStreamingThreshold=0.000000
GameCardSizeMB=13
GameCardSizeGB=8
GameCardClockRate=0

