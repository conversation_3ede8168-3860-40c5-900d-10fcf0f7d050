// Copyright (c) 2016-2017 Wicked Witch Software Pty. Ltd.

#pragma once

// Set the heap to use for debug data
#if defined ENABLE_DEBUG_HEAP
#define SIF_DEBUG_HEAP MMHEAP_DEBUG
#else
#define SIF_DEBUG_HEAP MMHEAP_PERMANENT_DATA
#endif

// Core
#include "Rugby.h"
#include "CoreMinimal.h"
#include "Engine/GameInstance.h"
#include "Engine/LevelStreamingDynamic.h"
#include "Engine/StreamableManager.h"
#include "OnlineIdentityInterface.h"
#include "OnlineExternalUIInterface.h"

// UI
#include "WWUIGameInstance.h"

//UI string table defines
#define PROJLOCALISATIONSTRINGTABLE FString("/Game/UI/Datatables/LocalisationStringtables/English/StringtableProjEnglish.StringtableProjEnglish")
#define INITLOCALISATIONSTRINGTABLE FString("/Game/UI/Datatables/LocalisationStringtables/English/StringtableInitEnglish.StringtableInitEnglish")
#define GLOBALLOCALISATIONSTRINGTABLE FString("/Game/UI/Datatables/LocalisationStringtables/English/StringtableGlobalEnglish.StringtableGlobalEnglish")
#define FRANCHISELOCALISATIONSTRINGTABLE FString("/Game/UI/Datatables/LocalisationStringtables/English/StringtableFranchiseEnglish.StringtableFranchiseEnglish")
#define DEFAULTLEGENDFONT FString("UIBodyMediumBlue")
#define PRESSSTARTFONT FString("UIHeaderLarge")

//#if PLATFORM_SWITCH
//#define DISABLE_COMMENTARY 1
//#endif

// Sidhe
#include "Match/SIFGraphicsHandle.h" // used in deferred queue
#include "Mab/Mem/MabMemSTLAllocators.h"
#include "Mab/MabDebug.h" //for assert
#include "Mab/Threading/MabMutex.h"//for MabMutex

// Networking
#include "Networking/NetworkEnums.h"
#include "Interfaces/OnlineSessionInterface.h"
#include "OnlineIdentityInterface.h"
#include "OnlineSessionInterface.h"
#include "OnlineSubsystemTypes.h"
#include "OnlineSubsystem.h"
#include "OnlineSessionSettings.h"
#include "OnlineError.h"
#include "Networking/LeaderboardManager.h"
#include "Networking/MatchingManager.h"

// Players
#include "Character/RugbyPlayerController.h"
#include "GameFramework/PlayerState.h"

// Sound
#include "Sound/SoundBank.h"
#include "Match/Audio/RUAudio.h"

//Animation
#include "Animation/RugbyAnimationRecords.h"

//Settings
#include "Match/RugbyUnion/RUGameSettings.h"

#include "SIFApplicationParameters.h"

// Listeners
#include "SIFSoundMessageListener.h"
#include "RURumbleProfileListener.h"
#include "SSCameraMessageListener.h"
#include "Networking/ConnectionManager.h"
#include "Map.h"

//plugins
//#include "wwStadiumRuntime/Public/StadiumTypes.h"

//#afl_replay
#include "ReplayManager.h"
#include "Networking/InviteManager.h"
#include "Mab/Threading/MabSemaphore.h"

#include "RugbyGameInstanceTick.h"
#include "DefaultSaveSettings.h"
#include "UI/WWUIVoipScreen.h"

#define REPLAY_USING_BONES	0

/* #rc3_legacy
#if PLATFORM_WINDOWS
#include "pc/SIFApplicationParameters.h"
#elif PLATFORM_PS3
#include "ps3/SIFApplicationParameters.h"
#include <PS3MabSPURS.h>
#include <sysutil/sysutil_gamecontent.h>
#elif PLATFORM_PS4
#include "ps4/SIFApplicationParameters.h"
#elif PLATFORM_XBOX360
#include "xbox360/SIFApplicationParameters.h"
#elif PLATFORM_XBOXCOMMON
#include "xboxone/SIFApplicationParameters.h"
#elif PLATFORM_IPHONE
#include "iphone/SIFApplicationParameters.h"
#elif PLATFORM_WII
#include "wii/SIFApplicationParameters.h"
#else
#error Please define your platform's SIFApplicationParameters.
#endif
*/

#include "RugbyGameInstance.generated.h"

#define DROPIN_COOLDOWN_MS 500

class URugbyLevelManager;
class UDatabaseManager;
class ARugbyCharacter;
class ASSBall;

class ARugbyGameSession;
class FVariantData;

class FRugbyAnimationLibrary;
class RUUIDatabaseQueryManager;

class UWWFanhubAnalyticsManager;
class UWWFanhubAnalyticsClient;

//class UWWUIVoipScreen;

#if PLATFORM_PS4
typedef TMap<FString, FString> IdUsernameMap;
#endif

// UI
enum ScreenAction;
class UWWUIStateScreenData;
class UUIDataServer;
class UWWUIScreenManager;
class UWWUITransitionManager;
class UWWHUDEventManager;
class UWWUITranslationManager;
class UWWUITranslatorStringTable;
class UWWUITranslatorInputAction;
class UWWUITranslatorBasic;

class UPlatformStatusChecker;

// Common forward declarations
class UFlowControlManager;
class MabControlActionManager;
class SIFWindowSystem;
class SIFResourceManager;
class SIFLevelLauncher;
class SIFUserSetting;
class SIFPhaseController;
class SIFStatisticsHandler;
//class SIFLeaderboardQueue;
class SIFAchievementAwarder;
class SIFUnlockableManager;
class USIFMissingControllerListener;
class SIFSystemKeyboard;
class SIFFullModeUnlocker;
class SIFAchievementChecker;
class RURugbyDollarsChecker;
class MabNetworkManager;
class MabPeerGroupManager;
class UVoipManager;
class SIFNetAddressProviderWrapper;
class RUSandboxGame;
class RUCommentary;
class RUStadiumManager;
class RUStatisticsSystem;
class RUCareerModeManager;
class RUCompetitionCustomisationHelper;
class SSAttractSequenceManager;
class NMMabAnimationRepository;
class SqliteMabDatabase;
class RUGameDatabaseManager;
class SSEventDataSystem;
class MabEVDS;
class SIFNetworkNonGameMessageHandler;
class SIFGamePauseState;
class SIFGameWorld;
class SIFGameObject;
class SIFAppTime;
class SIFSoakManager;
class URUTeamFacesGenerator;
class RUActiveCompetitionBase;
class RUActiveCompetitionBase;
struct RUDB_COMP_INST_MATCH;
class SIFAudio;
class RUAudio;
class RUStatsScorer;
class DeferredTeamSkinInfo;

class SIFBruteForceLightDatabase;
//class SIFGameJobQueue;
class PSSGMabShaderManager;
class SIFViewManager;
class PSSGMabDynamicLightLinkManager;
class PSSGMabAnimation;
class SIFAsyncLoadingThread;
class PSSGMabBindQueue;

// Emotion Engine
class SSCBEventAttrComparison;
class SSCBEventAttribute;
class SSCBEnvironmentCond;
class SSCBEvent;
class SSCBEvent;
class SSCBEventSequence;
class SSCBEventSequences;
class SSCBEventVar;
class SSCBTimingEvent;

class RUCBBrokenTackleEvent;
class RUCBKickEvent;
class RUCBPassEvent;
class RUCBTryEvent;
class RUCBKickAtPostsReadyEvent;
class RUCBLineoutSignalledEvent;
class RUCBRuckEvent;
class RUCBPenaltyEvent;
class RUCBTackleEvent;
class RUCBCatchEvent;
class RUCBCollectedEvent;
class RUCBFumbleEvent;
class RUCBKnockOnEvent;
class RUCBNewPhaseEvent;
class RUCBFistPumpEvent;
class RUCBHistoricalGameStat;
class RUCBHistoricalPlayerStat;
class RUHUDPlayerInfoUpdater;
class RUDBHelperInterface;
class MabCentralTypeDatabase2;
class MabResourceBase;
class UCutSceneManager;
class APostProcessingRugby;

class Cooldown;

// FanHub
class UWWFanhubHttpService;

//String converter macros
#define REMOVE_UNUSED_CHARACTER(str) for (int CharacterCount = 0; CharacterCount < str.Len(); CharacterCount++){int stringChar = str[CharacterCount];	if (stringChar == 129){str.RemoveAt(CharacterCount);}}

// Networking
class ARugbyGameSession;

//Steam
class UWWUISteamOverlayTracker;

#if defined ENABLE_QUICKSTART
class SIFQuickstartFlowNode;
#endif

class URugbyGameWorldSettings; typedef URugbyGameWorldSettings RUGameSettings;
class RUTeam;

struct FDefaultSaveSettings;

enum class EMontageAdvanceThreadMode : uint8
{
	TNonThreaded = 0,
	TSingleParallelThread,
	TTaskGraph
};

UENUM(BlueprintType)
enum class EOnlineSubMenuCloseType : uint8
{
	TOnlineMenu = 0		UMETA(DisplayName = "Go Back To Online Menu"),
	TMainMenu			UMETA(DisplayName = "Go Back To Main Menu"),
	TNone				UMETA(DisplayName = "None")
};

struct FRugbyGameSessionParams
{
	/** Name of session settings are stored with */
	FName SessionName;
	/** LAN Match */
	bool bIsLAN;
	/** Presence enabled session */
	bool bIsPresence;
	/** Id of player initiating lobby */
	TSharedPtr<const FUniqueNetId> UserId;
	/** Current search result choice to join */
	int32 BestSessionIdx;
	/** Session matchmaking mode */
	EMatchmakingMode MatchmakingMode;
	/** Session privacy level */
	EPrivacyLevel PrivacyLevel;
	/** Session Game mode */
	FString GameMode;
	/** Session map name */
	FString MapName;
	/** Session matching hopper */
	FString MatchingHopper;
	/** Session matching timeout */
	float MatchingTimeout;
	/** Session template name */
	FString SessionTemplateName;

	FRugbyGameSessionParams() { Clear(); }

	void Clear()
	{
		SessionName = NAME_None;
		bIsLAN = false;
		bIsPresence = true;
		BestSessionIdx = 0;
		MatchmakingMode = EMatchmakingMode::Unranked;
		PrivacyLevel = EPrivacyLevel::Public;
		GameMode = "MP";
		MapName = "MCG";
		MatchingHopper = "QuickMatch";
		MatchingTimeout = 120.0f;
		SessionTemplateName = "GameSession";
	}
};

USTRUCT()
struct FControllerDisconnectionData
{
	GENERATED_BODY();

	// This is always -1 on windows
	FPlatformUserId PlatformUserId = -1;

	// WeakPtr : We don't want to block destruction/garbage collection
	TWeakObjectPtr<ULocalPlayer> LocalPlayer;

	int PreDisconnectControllerId = 0;
	int PreDisconnectGameUserIndex = 0;
	int PreDisconnectTeamIndex = -1;
	int PreDisconnectHumanPlayerIndex = -1;
	int PreDisconnectHumanPlayerSlot = -1;
	float disconnectTime = -1.0f;

	void Init(FPlatformUserId InPlatformUserId, ULocalPlayer* InLocalPlayer, int InPreDisconnectControllerId, int InPreDisconnectGameUserIndex, int InPreDisconnectTeamIndex, int InPreDisconnectHumanPlayerIndex, int InPreDisconnectHumanPlayerSlot)
	{
		PlatformUserId = InPlatformUserId;
		LocalPlayer = InLocalPlayer;
		PreDisconnectControllerId = InPreDisconnectControllerId;
		PreDisconnectGameUserIndex = InPreDisconnectGameUserIndex;
		PreDisconnectTeamIndex = InPreDisconnectTeamIndex;
		PreDisconnectHumanPlayerIndex = InPreDisconnectHumanPlayerIndex;
		PreDisconnectHumanPlayerSlot = InPreDisconnectHumanPlayerSlot;
	}
};

//#rc3_legacy_pssgdeclarations
namespace PSSG
{
	typedef unsigned int PDatabaseID;
	class PObject;
	class PDatabase;
}

class SIFRichPresence;
// Platform-specific forward declarations
#if PLATFORM_WINDOWS

	#if defined ENABLE_STEAM
		class SIFSteamAchievementAwarder;
		class SIFSteamEventHandler;
		class SteamMabLobbyManager;
		class SIFSteamOnlineEventHandler;
		class SteamMabVoiceManager;
	#else
		class MabLanGameManager;
		class SIFPCOnlineEventHander;
	#endif

#elif PLATFORM_PS4

	class PS4MabNPBasic;
	class PS4MabNPDRM;
	class PS4MabOnlineScoreManager;
	class PS4MabOnlineUserManager;
	class PS4MabOnlineGameManager;
	class SIFPS4SaveDataRequestHandler;
	class SIFPS4AchievementAwarder;
	class SIFPS4CachedOnlineScoreHelper;
	class SIFPS4NPMessageHandler;
	class SIFPS4SessionEventHandler;
	class PS4MabWebAPI;

#elif PLATFORM_XBOXCOMMON
	class XboxOneMabMatchMaking;
	class XboxOneMabUISessionController;
	class SIFXboxOneSessionEventHandler;
	class SIFXboxOneAchievementAwarder;
	class XboxOneMabVoiceManager;
	class SIFXboxOneSaveRequestManager;
	class SIFXboxOnePlayerProfileRequestHandler;
	class SIFXboxOneSaveManager;
	class XboxOneMabUserInfo;
	class XboxOneMabSessionManager;
	class SIFXboxOneSqlRequestHandler;
	class XboxOneMabSystemNotificationHub;

	enum XBOXONE_APP_STATUS
	{
		APP_NORMAL = 0,
		APP_SUSPEND
	};

#endif

// Region identifier (for default teams/logo).
enum RU_GAME_REGION
{
	GAME_REGION_REST_OF_WORLD = 0,
	GAME_REGION_NEW_ZEALAND,
	GAME_REGION_AUSTRALIA,
	GAME_REGION_UNITED_KINGDOM,
	GAME_REGION_SOUTH_AFRICA,
	GAME_REGION_EUROPE,
	GAME_REGION_FRANCE
};

#ifndef ENABLE_NO_LOGS
const TArray<FString> GAME_REGION_NAMES
{
	"Rest of World",
	"New Zealand",
	"Australia",
	"United Kingdom",
	"South Africa",
	"Europe",
	"France"
};
#endif

enum CAREER_SETUP_STATE : uint8;

typedef enum
{
	GameModeId_Menu = 0,
	GameModeId_Match,
	GameModeId_Tutorial

} GameModeId;

const TArray<FString> GAME_MODE_ALIAS
{
	"Menu",
	"Match",
	"Tutorial"
};

enum TrainingPrompt
{
	RunAround,
	Tutorial,
	NetworkRunAround,
	EnteringMatch
};

//////////////////////////////////////////////////////////////////////////
// DELEGATES
//////////////////////////////////////////////////////////////////////////
// Delegate for when we receive RPCs from the server that need to update other systems eg: UI
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnNetworkRefresh, ENetworkRefreshType, RefreshType);
typedef FOnNetworkRefresh::FDelegate FOnNetworkRefreshDelegate;

DECLARE_MULTICAST_DELEGATE_OneParam(FOnLobbyBoolChanged, bool);

// Delegate for dropping controllers in/out
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAttemptControllerDropIn, int, ControllerId, bool, IsDropIn);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnControllerDropIn, bool, IsDropIn);

DECLARE_MULTICAST_DELEGATE_OneParam(FOnUGCRestrictionChanged, bool);

DECLARE_MULTICAST_DELEGATE_OneParam(FOnUGCUsernameCheckComplete, bool);

//Delegate for controller connection changing
DECLARE_MULTICAST_DELEGATE_ThreeParams(FOnControllerConnectionChange, bool, int32, int32);

// Delegate for cutscene load events.
DECLARE_DELEGATE(FWWUICutsceneLoadComplete);

// Delegate for holding transition till database load is complete
DECLARE_DELEGATE_OneParam(FWWUIPendingDBLoadComplete, float);

DECLARE_MULTICAST_DELEGATE_OneParam(FOverlayActivated, bool);

DECLARE_MULTICAST_DELEGATE_OneParam(FOnSystemEventCallBack, FString);
//////////////////////////////////////////////////////////////////////////
// END > DELEGATES
//////////////////////////////////////////////////////////////////////////

USTRUCT()
struct FRugbyCharacterMorphData
{
	GENERATED_BODY()
public:

	TWeakObjectPtr<ARugbyCharacter> m_Character;
	TWeakObjectPtr<USkeletalMeshComponent> m_Component;
	TWeakObjectPtr<UMorphToolsRuntimePreset> m_Preset;

	//ARugbyCharacter* m_Character = nullptr;
	//USkeletalMeshComponent* m_Component = nullptr;
	//UMorphToolsRuntimePreset* m_Preset = nullptr;
};

USTRUCT()
struct FRugbyCharacterMorphResults
{
	GENERATED_BODY()
public:
	int characterID = -1;
	TArray<USkeletalMesh*> fromNonMerged;
	UPROPERTY()
		TArray<USkeletalMesh*> toMerged;
};

USTRUCT()
struct FSkeletalMeshSetData
{
	GENERATED_BODY()
public:

	TWeakObjectPtr<USkeletalMesh> sk;
	TWeakObjectPtr<USkeletalMeshComponent> comp;
	TWeakObjectPtr<AActor> actor;
	//USkeletalMesh* sk;
	//USkeletalMeshComponent* comp;
	//AActor* actor;

	FString SkeltalMeshName = "NULL" ;
	FString SkeltalCompName = "NULL";
	FString ActorName = "NULL";
};

#ifdef ENABLE_ANALYTICS
enum class wwAnalyticsEvent : uint8
{
	wwRU_COMPETITION_START,
	wwRU_COMPETITION_ADVANCE_ROUND,
	wwRU_COMPETITION_FINISH,
	  
	wwRU_CAREER_START,
	wwRU_CAREER_ADVANCE_ROUND,
	wwRU_CAREER_CONTRACTS,

	wwRU_BEAPRO_START,
	wwRU_BEAPRO_ADVANCE_ROUND,
	wwRU_BEAPRO_CONTRACTS,
	  
	wwRU_FANHUB_CLOSED,
	wwRU_ONLINE_OPENED,
	wwRU_ONLINE_STOP_SEARCH,
	wwRU_FREE_ROAM_CLOSED,
	wwRU_LEADERBOARD,
	  
	wwRU_BEFORE_MATCH,
	wwRU_POST_MATCH,

	wwRU_DOWNLOAD_CHAMPION_DATA
};

namespace wwAnalyticsInGameMenuOption
{
	const FString ManageTeam			= FString("manageTeam");
	const FString Controls				= FString("controls");
	const FString Display				= FString("display");
	const FString Sound					= FString("sound");
	const FString ChooseSides			= FString("chooseSides");
	const FString InstantReplay			= FString("instantReplay");
	const FString Restart				= FString("restart");
	const FString Quit					= FString("quit");
	const FString ProGoals				= FString("proGoals");
	const FString Camera				= FString("camera");
	const FString Graphics				= FString("graphics");
	const FString Language				= FString("language");
	const FString SimulateMatch			= FString("simulateMatch");
	const FString MyTeamSquad			= FString("MyTeamSquad");
	const FString OppositionTeamSquad	= FString("OppositionTeamSquad");

}

namespace wwAnalyticsTutorialOption
{
	const FString ScoringATry		= FString("scoringATry");
	const FString SimpleConversion	= FString("simpleConversion");
	const FString Passing			= FString("passing");
	const FString Tackling			= FString("tackling");
	const FString ChangePlayer		= FString("changePlayer");
	const FString LooseBall			= FString("looseBall");
	const FString CutOutPass		= FString("cutOutPass");
	const FString Running			= FString("running");
	const FString AggressiveTackle	= FString("aggressiveTackle");
	const FString PuntKick			= FString("puntKick");
	const FString ChipKick			= FString("chipKick");
	const FString BombKick			= FString("bombKick");
	const FString GrubberKick		= FString("grubberKick");
	const FString DropGoal			= FString("dropGoal");
	const FString KickLooseBall		= FString("kickLooseBall");
	const FString PenaltyForTouch	= FString("penaltyForTouch");
	const FString Ruck				= FString("ruck");
	const FString Lineout			= FString("lineout");
	const FString Scrum				= FString("scrum");
	const FString ContestedTackle	= FString("contestedTackle");
	const FString Maul				= FString("maul");
	const FString KickForTouch		= FString("kickForTouch");
	const FString CancelKicking		= FString("cancelKicking");
	const FString Offload			= FString("offload");
	const FString SideStep			= FString("sideStep");
	const FString Fend				= FString("fend");
	const FString DummyPass			= FString("dummyPass");
	const FString WindyConversion	= FString("windyConversion");
	const FString ContestingTheBall = FString("contestingTheBall");
	const FString QuickLineout		= FString("quickLineout");
	const FString PlayTheBall		= FString("playtheball");
	const FString FourtyTwenty		= FString("fourtytwenty");
	const FString SetPlays			= FString("setplays");
}

namespace wwAnalyticsSetPlayNames
{
	const FString DummySkipPass = FString("Dummy Skip Pass");
	const FString Classic		= FString("Classic");
	const FString CrossKick		= FString("Cross Kick and Wing");
}

enum class wwSetPlayFaceOption : uint8
{
	wwSPFO_NULL,
	wwSPFO_BOTTOM,
	wwSPFO_RIGHT,
	wwSPFO_LEFT,
	wwSPFO_TOP,
};

struct wwSetPlayAnalytic
{
	bool used = false;
	uint32 optionTop = 0;
	uint32 optionLeft = 0;
	uint32 optionRight = 0;
	uint32 optionBottom = 0;
};

struct wwOnlineStallAnalytic
{
	uint32 framesStalled = 0;
	double timeStalled = 0.0;
};

struct wwOnlinePingAnalytic
{
	uint32 playerID = 0;
	uint32 accumulatedPing = 0.0;
	uint32 counter = 0;
};


struct wwPlayerAnalyticsData
{
	// Menus used <Screen Key, bWasUsed>
	TMap<FString, bool> MenuUsedMap;

	// Tutorials played <Screen Key, bWasUsed>
	TMap<FString, bool> TutorialPlayedMap;

	// In Game Options Used <Name, bWasUsed>
	TMap<FString, bool> InGameOptionsUsed;

	// Set Plays Used <Name, bWasUsed>
	TMap<FString, wwSetPlayAnalytic> SetPlaysUsed;

	TArray<wwOnlineStallAnalytic> OnlineStalls;

	TArray<wwOnlinePingAnalytic> OnlinePingData;

	// Fanhub
	uint32 FanHubPlayersDownloaded = 0;
	uint32 FanHubTeamsDownloaded = 0;
	uint32 FanHubPlayersUploaded = 0;
	uint32 FanHubTeamsUploaded = 0;
	uint32 FanHubPagesViewed = 0;
	double FanHubStartTime = 0.0;

	double LeaderBoardStartTime = 0.0;
	double OnlineSearchStartTime = 0.0;
	uint32 OnlineGameMode = 0;
	uint32 NumSearchResults = 0;

	uint32 TutorialsViewed = 0;
	double FreeRoamStartTime = 0.0;

	uint32 SetPlay1Count = 0;
	uint32 SetPlay2Count = 0;
	uint32 SetPlay3Count = 0;
	uint32 InterchangeCount = 0;

	//bool   bDownloadedChampionData = false;

	void Reset()
	{
		MenuUsedMap.Empty();
		InGameOptionsUsed.Empty();
		TutorialPlayedMap.Empty();
		SetPlaysUsed.Empty();
		OnlineStalls.Empty();
		OnlinePingData.Empty();

		FanHubPlayersDownloaded = 0;
		FanHubTeamsDownloaded = 0;
		FanHubPlayersUploaded = 0;
		FanHubTeamsUploaded = 0;
		FanHubPagesViewed = 0;
		FanHubStartTime = 0.0;

		LeaderBoardStartTime = 0.0;
		OnlineSearchStartTime = 0.0;
		OnlineGameMode = 0;

		TutorialsViewed = 0;
		FreeRoamStartTime = 0.0;

		SetPlay1Count = 0;
		SetPlay2Count = 0;
		SetPlay3Count = 0;
		InterchangeCount = 0;

		//bDownloadedChampionData = false;
	}

	bool WasMenuUsed(FString ScreenID) { return MenuUsedMap.FindOrAdd(ScreenID); }
	void MarkMenuUsed(FString ScreenID) { MenuUsedMap.Add(ScreenID, true); }

	bool WasInGameMenuUsed(FString ScreenID) { return InGameOptionsUsed.FindOrAdd(ScreenID); }
	void MarkInGameMenuUsed(FString ScreenID) { InGameOptionsUsed.Add(ScreenID, true); }

	void AddFanHubPlayerDownloaded() { FanHubPlayersDownloaded++; }
	void AddFanHubTeamDownloaded() { FanHubTeamsDownloaded++; }
	void AddFanHubPlayerUploaded() { FanHubPlayersUploaded++; }
	void AddFanHubTeamUploaded() { FanHubTeamsUploaded++; }
	void AddFanHubPagesViewed() { FanHubPagesViewed++; }
	void StartFanHubTimer() { FanHubStartTime = FPlatformTime::Seconds(); }
	void StartLeaderBoardTimer() { LeaderBoardStartTime = FPlatformTime::Seconds(); }

	void StartOnlineSearchTimer() { OnlineSearchStartTime = FPlatformTime::Seconds(); }
	void SetOnlineGameMode(uint32 Val) { OnlineGameMode = Val; }

	void AddTutorialViewed() { TutorialsViewed++; }
	void MarkTutorialViewed(FString TutorialID) { AddTutorialViewed(); TutorialPlayedMap.Add(TutorialID, true); }
	bool WasTutorialViewed(FString TutorialID) { return TutorialPlayedMap.FindOrAdd(TutorialID); }
	void StartFreeRoamTimer() { FreeRoamStartTime = FPlatformTime::Seconds(); }

	void MarkSetPlayUsed(FString name, wwSetPlayFaceOption option = wwSetPlayFaceOption::wwSPFO_NULL)
	{
		wwSetPlayAnalytic counter;
		SetPlaysUsed.RemoveAndCopyValue(name, counter);

		switch (option)
		{
		case wwSetPlayFaceOption::wwSPFO_BOTTOM:
			counter.optionBottom = counter.optionBottom + 1;
			break;
		case wwSetPlayFaceOption::wwSPFO_RIGHT:
			counter.optionRight = counter.optionRight + 1;
			break;
		case wwSetPlayFaceOption::wwSPFO_LEFT:
			counter.optionLeft = counter.optionLeft + 1;
			break;
		case wwSetPlayFaceOption::wwSPFO_TOP:
			counter.optionTop = counter.optionTop + 1;
			break;
		default:
			break;
		}
		counter.used = true;

		SetPlaysUsed.Add(name, counter);
	}

	void AddInterchange() { InterchangeCount++; }

	void AddStall(uint32 frames, double time)
	{
		wwOnlineStallAnalytic stall;
		stall.framesStalled = frames;
		stall.timeStalled = time;
		OnlineStalls.Add(stall);
	}

	void UpdatePlayerPing(uint32 playerID, uint32 timeInMillis)
	{
		for (wwOnlinePingAnalytic& ping : OnlinePingData)
		{
			if (ping.playerID == playerID)
			{
				ping.accumulatedPing += timeInMillis;
				MABASSERT(ping.accumulatedPing < MAX_uint32);
				
				ping.counter++;
				MABASSERT(ping.counter < MAX_uint32);

				return;
			}
		}

		wwOnlinePingAnalytic ping;
		ping.playerID = playerID;
		ping.accumulatedPing = timeInMillis;
		OnlinePingData.Add(ping);
	}

	//bool GetHasDownloadedChampionData() { return bDownloadedChampionData; }
	//void MarkHasDownloadedChampionData() { bDownloadedChampionData = true; }
};

#endif

USTRUCT(BlueprintType)
struct FSavedErrorPopup
{
	GENERATED_BODY()

public:

	FString HeadingString;
	FString ErrorString;
	FName stateName;
	bool allowCancel = false;

	FSavedErrorPopup() {}

	FSavedErrorPopup(FString SavedHeadingString, FString SavedErrorString, FName SavedstateName, bool SavedallowCancel)
	{
		HeadingString = SavedHeadingString;
		ErrorString = SavedErrorString;
		stateName = SavedstateName;
		allowCancel = SavedallowCancel;
	}
};


UCLASS()
class RUGBY_API URugbyGameInstance : public UGameInstance, public IWWUIGameInstance
{
	GENERATED_BODY()
	URugbyGameInstance();

	~URugbyGameInstance();
	
public:
#if WITH_EDITOR
	bool editorDebugScreenLaunch;
	FString editorMapPath;
#endif

//////////////////////////////////////////////////////////////////////////
	// Debug time manipulation.
#if !UE_BUILD_SHIPPING
	bool s_bIsDebugPaused = false;
	bool s_bIsDebugSlow = false;
	void ToggleDebugPause();
	void SetDebugPause(bool paused);
	void ToggleDebugSlow();
private:
	void UpdateDebugTimeDilation();

public:
#endif
//////////////////////////////////////////////////////////////////////////

	virtual void Init() override;
	virtual void Shutdown() override;
	virtual void StartGameInstance() override;
	virtual void OnStart() override;
#if WITH_EDITOR
	virtual FGameInstancePIEResult StartPlayInEditorGameInstance(ULocalPlayer* LocalPlayer, const FGameInstancePIEParameters& Params) override;
#endif
	UFUNCTION(Exec)
	void SetToPlayingState();

	UFUNCTION(Exec)
	void SetToPlayingPostMatchState();

	UFUNCTION(Exec)
	void SetToConnectionWaitingState();
	

	UFUNCTION(Exec)
	void EndConnectionWaitingState(bool ConnectionHasReturned);

	UFUNCTION(Exec)
	void ForceNetworkError();

	bool bTickedPreSync = false;

	bool DeferredTick(float InDeltaSeconds);

	bool PreSyncTick(float InDeltaSeconds);
	bool PostSyncTick(float InDeltaSeconds);

	// Tick Function Declarations
	void TickFunctionTick(float DeltaSeconds, ELevelTick TickType, FRugbyGameInstanceTick& ThisTickFunction);
	void RegisterTickFunctions(bool bRegister);

	/// Update the test App
	void PreSyncUpdate();
	void PostSyncUpdate();

	void PreSyncAsyncUpdate();
	void PostSyncAsyncUpdate();

	void SyncUpdate();

	// Helper function for pausing the sync and async updates, which can lock up the game thread if an async task is doing DB operations.
	void SetPauseSyncAsyncUpdate(bool Val) { bPauseSyncAsyncUpdate = Val; }

	static void UpdateThread(void* data);

	bool IsDemoBuild();

	/// Enters the online flow. (moved here for matchingManager)
	void EnterOnlineFlow(bool isHost);

	UPROPERTY()
	class ARugbyCameraActor* pPrimaryCamera = nullptr;
protected:

	/// Cleanup the Test App
	void Cleanup();

	// Initialise the core systems
	bool InitialiseSystem();

	void SSCBDefineMabCentralInterfaces();

	// Initialise the graphics layer
	bool Initialise3D();

	// Initialise the graphics layer
	bool InitialiseApp();

	//Initialise the Translation Manager
	void InitialiseTranslationManager();

	//#MB - temporary location for PLT translator function
	bool GetPlatformString(const FString& inString, FString& outString);

	/// Initialise the input system.
	void InitialiseInput();

	/// Cleanup the input system
	void CleanupInput();

	/// Initialise the network
	void InitialiseNetwork();

	/// Cleanup the network
	void CleanupNetwork();

	/// Set the initial menu (tutorial) and main game settings
	void InitialiseGameSettings();
	
	/// Helpers to initialise the game settings
	void InitialiseGameSettingsForMatch(RUGameSettings* gameSettings);
	void InitialiseGameSettingsForSandbox(RUGameSettings* gameSettings);
	void InitialiseGameSettingsForMenu(RUGameSettings* gameSettings);

	/// Setup the MabFileSystem search paths
	void InitialiseSearchPaths();

	/// Create a file driver for the global packfile
	void RegisterCommonPackfile();

	/// Create a file driver for the patch packfile
	void RegisterPatchPackfile();

	/// Initialise the profile system.
	void InitialisePlayerProfileManager();

	/// Registers all the classes interested in listening to the newly created profile
	void RegisterProfileListeners();

	/// Initialise the flow control manager.
	void InitialiseFlowControl();

	/// Cleanup the flow control Manager.
	void CleanupFlowControl();

	/// Sets up the initial active flownode
	void ActivateFlowControl();

	/// Enters the intro flow.
	void EnterIntroFlow();

	/// Enters the Quickstart loading flow. When profile loading is complete, will advance to main flow.
#if defined ENABLE_QUICKSTART
	void EnterQuickstartFlow();

	// Also friend in the quickstart flow node so it can call SetDefaultController safely.
	friend class SIFQuickstartFlowNode;
#endif

	/// Enters the online flow.
	//void EnterOnlineFlow(bool isHost);

	void OnPostLoadMap(UWorld* world);
	
	/// Evaluates command line parameters and performs level launch.
	/// \return True if a level was launched.
	bool HandleLevelLaunch();

	/// Evaluates command line parameters and launches content test.
	/// \return True if content test was launched.
	//#rc3_legacy
	//bool HandleContentTestLaunch();

	/// Evaluates command line parameters and launches asset preview.
	/// \return True if asset preview launched.
	//#rc3_legacy
	//bool HandleAssetPreviewLaunch();

	/// Evaluates whether there is a pending invitation, and performs appropriate actions.
	/// \return True if invite was pending.
	bool HandlePendingGameInvites();

	/// Sets a default master controller, if none is set already
	void SetDefaultMasterController();

	/*#rc3_legacy
	/// Listens for Mab Framework messages
	void Update(MabObservable<MAB_FRAMEWORK_MESSAGE>* source, const MAB_FRAMEWORK_MESSAGE& msg);
	
	/// Callback that gets registered to receive notification of Post Database Load events
	static void OnPostDatabaseLoad(const PSSG::PDatabaseID db_id);
	//*/

	virtual void UpdateScriptTriggers();

	private:
		bool bLoading = false;
public:
	bool IsInLoadingState()
	{
		return bLoading;
	}

	bool CanHostPlaytogether = false;
	UFUNCTION(Blueprintpure, Category = "UIScreenManager")
	virtual UWWUIScreenManager* GetUIScreenManager() override { return UIScreenManager; }

	/// Called when the profile loading has been completed. Handles quickstart options such as level launch,
	/// content test and asset preview or advances to the main menu.
	/// Currently public, because it's called from SIFGameHelpers::GAProfileLoadComplete(). Could make
	/// it friend instead.
	void EnterMainFlow();

	void ToMainMenuCallback();

	/// Called to enter the training flow
	void EnterTrainingFlow(TrainingPrompt trainingPrompt);
	void SetTrainingFlowPrompt(TrainingPrompt trainingPrompt);

	static inline FString GetGameModeOption(GameModeId gameMode) { return FString::Printf(TEXT("?game=%s"), *GAME_MODE_ALIAS[gameMode]); }
	void TravelToMap(FString mapUrl, FString options = "");

	URugbyLevelManager* GetLevelManager() { return m_LevelManager; }

	SIFGameWorld* GetActiveGameWorld();
	bool IsActiveGameWorld(SIFGameWorld* gameWorld);
	void ChangeActiveGameWorld(SIFGameWorld* gameWorld, bool delayedAwaken = false);
	void ClearActiveGameWorld();


	/// Calculate which game region console/pc should use.
	void CalculateGameRegion();

	/// Get the game region.
	inline RU_GAME_REGION	GetGameRegion() { return ru_game_region; }

	void LoadRumbleSet();

	/// Return default team 'A' for the game region.
	unsigned short GetDefaultTeamA();

	/// Return default team 'B' for the game region.
	unsigned short GetDefaultTeamB();

	/// Return default womens team for the game region.
	unsigned short GetDefaultWomensTeam();

	//UFUNCTION(Blueprintpure, Category = "UIScreenManager")
	virtual UWWUITransitionManager* GetTransitionManager() override { return TransitionManager; }
	virtual bool AllowScreenInput() override;

	TSubclassOf<ARugbyCharacter> GetDefaultRugbyPlayerClass() { return DefaultRugbyPlayerClass; }
	TSubclassOf<ASSBall> GetDefaultBallClass() { return DefaultBallClass; }
	TSubclassOf<AActor> GetDefaultRuckIndicator() { return DefaultRuckIndicator; }
	TSubclassOf<AActor> GetDefaultSetplayIndicator() { return DefaultSetplayIndicator; }
	TSubclassOf<UWWUIScreenTemplate> GetDefaultWipeScreen() { return DefaultWipeScreen; }

	/// Handlers for the flow nodes to access the Sandbox game during the menu transitions
	RUSandboxGame* InitialiseSandboxGame();
	void WakeSandboxGame();
	void DestroySandboxGame();
	
	bool IsSandboxInitialised() { return m_SandboxGame != nullptr; }
	bool IsSandboxLoaded();
	bool IsSandboxAwake();

	/// Helper function to get various fields out of current competition/career, if one exists.
	///	Leave pointers as NULL if you don't care about the given field.
	///	Pointers will be set to NULL if there is neither an active career nor an active competition.
	void GetCompetitionFields(const RUActiveCompetitionBase** active_competition_out = NULL, const RUDB_COMP_INST_MATCH** next_match_out = NULL, int* current_player_team_id_out = NULL, MabVector< unsigned short >* player_team_id_list_out = NULL) const;
	void GetActiveCompetitionNonConst(RUActiveCompetitionBase*& active_competition_out);

	/// Process the travelling to new screens for the career mode mananger
	void DealCareerSetupScreen(CAREER_SETUP_STATE NewState, UWWUIStateScreenData* InData = nullptr);

	void DealMenuActionAfterCutsceneEvent(int cs_event, FWWUICutsceneLoadComplete LoadCompleteDelegate, int mid_fade_action = 0);

private:
	UPROPERTY()
		TArray<UWWUIStateScreenData*> GuardedInData;
	UPROPERTY()
		UInviteManager* InviteManager = nullptr;

	bool bPauseSyncAsyncUpdate = false;

	FOnSystemEventCallBack SystemEventListner;
public:

	FOnSystemEventCallBack GetSystemEvents()
	{
		return SystemEventListner;
	}

	UInviteManager* GetInviteManager()
	{
		return InviteManager;
	}

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TSubclassOf<class AHeadCaptureActor> HeadRenderClass = nullptr;

	// Debug memory widget.
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TSubclassOf<class UUserWidget> DebugMemoryWidgetClass = nullptr;

#if PLATFORM_WINDOWS && !UE_BUILD_SHIPPING
	PlayerCustomisationInfo CustomiseCopyInfo;
#endif

	/// Deals a menu action to travel to the player creator in pro mode.
	void ProceedToProPlayerCreator(UWWUIStateScreenData* InData = nullptr);

	UFUNCTION()
	void NewSlateUserConnected(int userNum);

	// allow blueprint to triger the WWUI
	UFUNCTION(BlueprintCallable, Category = "WWUI")
		void WWUIInpoint(const FString& InKey, const FString& InEditorKey, const bool InTransition, const bool InRemoveAllScreens = false, UWWUIStateScreenData* InData = nullptr);

	USoundBank* GetSoundBank()
	{
		if (!SoundBank)
		{
			SoundBank = NewObject<USoundBank>();
		}

		return SoundBank;
	}
	UPROPERTY()
	UPlatformStatusChecker* PlatformStatusChecker = nullptr;
	// WW UI interface implementation
	UWWUIStateScreen* GetCurrentStateScreen() { return UOBJ_IS_VALID(UIScreenManager) ? UIScreenManager->GetCurrentStateScreen() : nullptr; }
	UWWUIScreenTemplate* GetCurrentScreenTemplate() { return UOBJ_IS_VALID(UIScreenManager) ? UIScreenManager->GetCurrentScreenTemplate() : nullptr; }
	UWWUIScreenTemplate* GetCurrentScreenTemplateExcept(const FString except) { return UOBJ_IS_VALID(UIScreenManager) ? UIScreenManager->GetCurrentScreenTemplateExcept(except) : nullptr; }
	UWWUIScreenTemplate* GetCurrentNonModalScreenTemplate() { return UOBJ_IS_VALID(UIScreenManager) ? UIScreenManager->GetCurrentNonModalScreenTemplate() : nullptr; }

	UWWUIScreenTemplate * GetWipeScreen() { return WipeScreen; }
	void SetWipeScreen(UWWUIScreenTemplate * inWipeScreen) { WipeScreen = inWipeScreen; }
	
	UDataTable * GetCameraMainDt() { return CameraMainDt; };
	UDataTable * GetCameraGameDt() { return CameraGameDt; };
	UDataTable * GetCameraReplayDt() { return CameraReplayDt; };
	UDataTable * GetCameraSetsDt() { return CameraSetsDt; };
	UDataTable * GetCameraBlendsDt() { return CameraBlendsDt; };
	UDataTable * GetCameraNoiseDt() { return CameraNoiseDt; };

	//UDataTable * GetFifteensSetplaysDt() { return FifteensSetplaysDt; };
	UDataTable * GetThirteensSetplaysDt() { return ThirteensSetplaysDt; };
	//UDataTable * GetFifteensFormationsDt() { return FifteensFormationsDt; };
	UDataTable * GetThirteensFormationsDt() { return ThirteensFormationsDt; };
	UDataTable * GetSevensSetplaysDt() { return SevensSetplaysDt; };
	UDataTable * GetSevensFormationsDt() { return SevensFormationsDt; };

	void AddRuckJoinIndicator(AActor * newIndicator) { RuckJoinIndicators.Add(newIndicator); };
	void ClearRuckJoinIndiactors() { RuckJoinIndicators.Empty(); };

	virtual void RequestStateStartTransitionEnd() override;
	virtual void RequestTransitionStart(const float InDuration, const FWWUIOnTransitionStartComplete& InOnTransitionStartComplete) override;
	virtual void RequestTransitionStart(const float InDuration, const FWWUIOnTransitionStartComplete& InOnTransitionStartComplete, bool bHoldFade);
	virtual void RequestTransitionFinishDefaultTime();
	virtual void RequestTransitionFinish(const float InDuration) override;
	virtual void RequestTransitionHoldFinish(const float InDuration);
	virtual void OnMenuButton(const FString& ScreenID, const FString& InKey) override;
	virtual void OnMenuTile(const FString& ScreenID, const int32 InArrayIndex) override;
	virtual void OnMenuOption(const FString& ScreenID, const int32 InArrayIndex, const EWWUIOptionInput InOptionInput) override;
	virtual void DealMenuAction(const FString& Action, const FString& ActionString, UWWUIStateScreenData* InScreenData = nullptr) override;
	virtual void DealMenuAction(const ScreenAction InIdx, const FString& ActionString, UWWUIStateScreenData* InScreenData = nullptr) override;
	virtual const FWWUIScreenTemplateDataButton GetScreenButton(const FString& InScreenID, const FString& InKey) override;
	virtual const FWWUIScreenTemplateDataButton GetScreenAxis(const FString& InScreenID, const FString& InKey) override;
	virtual void OnSystemEvent(WWUINodeProperty& eventParams);
	virtual void AddScriptTrigger(WWUINodeProperty& eventParams);

	//#afl_replay
	UPROPERTY(BlueprintReadOnly)
		UReplayManager* ReplayManager;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
		TSubclassOf<UReplayManager> ReplayManagerType = UReplayManager::StaticClass();	

	bool ReplayEnabled = true;

	bool TryParseScreenAction(const FString& action, ScreenAction& outValue);
	void OnScreenEnter(FString screenEntered);

	//
	void StartAsyncGameLoad();
	void CompleteAsyncGameLoad();
	FSimpleMulticastDelegate OnAsyncGameLoadComplete;

	void AsyncLoadPSSGSetup();

	static void LoadGameCallback2(void *user_data, SIFAsyncLoadingThread *load_thread, bool is_abort);
	static void LoadGameCallback(void *user_data, SIFAsyncLoadingThread *load_thread, bool is_abort);

	//
	void LoadPressStartResourceSet();

	//
	/// Stops SIFApplication from rendering anything.  Should be used with caution - it is not scope or thread safe.
	void SetSuppressRendering(bool val);
	/// TEMP: Till better solution, but with out thread-switch which causes problems.
	void SetSuppressRenderingNoThreadSwitch(bool val);
		
	// Accessors
	static FStreamableManager&					GetStreamableManager()						{ return GetInstance()->m_StreamableManager; }

	inline UDatabaseManager*					GetDatabaseManager() const					{ return m_DatabaseManager; }

	inline SIFApplicationParameters&			GetApplicationParameters()					{ return app_params; }
	inline SIFAppTime*							GetAppTime() const							{ return app_time; }
	inline UFlowControlManager*					GetFlowControlManager() const				{ return flow_manager; }
	inline SIFLevelLauncher*					GetLevelLauncher() const					{ return level_launcher; }
	inline SIFStatisticsHandler*				GetStatisticsHandler() const				{ return statistics_handler; }
	//inline SIFLeaderboardQueue*					GetLeaderboardQueue() const					{ return leaderboard_queue; }
	inline SIFAchievementChecker*				GetAchievementChecker() const				{ return achievement_checker; }
	inline RURugbyDollarsChecker*				GetRugbyDollarsChecker() const				{ return rugby_dollars_checker; }
	inline RUStatsScorer*						GetStatsScorer() const						{ return stats_scorer; }
	inline MabCentralTypeDatabase2*				GetTypeDatabase() const						{ return type_database; }
	inline RUGameDatabaseManager*				GetGameDatabaseManager() const				{ return gamedb_manager; }
	inline RUStatisticsSystem*					GetStatisticsSystem() const					{ return stats_system; }
	inline RUDBHelperInterface*					GetGameDBHelper() const						{ return game_db_helper; }
	inline RUUIDatabaseQueryManager*			GetDbQueryManager() const					{ return db_query_manager; }
	inline RUCareerModeManager*					GetCareerModeManager() const				{ return career_mode_manager; }
	inline RUCompetitionCustomisationHelper*	GetCompetitionCustomisationHelper() const	{ return competition_customisation_helper; }
	inline RUSandboxGame*						GetSandboxGame() const						{ return m_SandboxGame; }
	inline SIFSoakManager*						GetSoakManager() const						{ return soak_manager; }
	inline MabEVDS*								GetEVDS() const								{ return evds; }

	inline SIFGameWorld*						GetMatchGameWorld() const					{ return m_MatchGameWorld; }
	inline RUGameSettings*						GetMatchGameSettings() 						{ return m_MatchGameSettings; }
	inline SIFGamePauseState*					GetMatchPauseState() const					{ return m_MatchPauseState; }
	inline RUStadiumManager*					GetMatchStadiumManager() const				{ return m_MatchStadiumManager; }

	inline SIFGameWorld*						GetMenuGameWorld() const					{ return m_MenuGameWorld; }
	inline RUGameSettings*						GetMenuGameSettings() 						{ return m_MenuGameSettings; }

	//SIFGameWorld*								GetSandboxGameWorld() const;
	inline RUGameSettings*						GetSandboxGameSettings() 					{ return m_SandboxGameSettings; }
	inline SIFGamePauseState*					GetSandboxPauseState() const				{ return m_SandboxPauseState; }
	inline RUStadiumManager*					GetSandboxStadiumManager() const			{ return m_SandboxStadiumManager; }

	inline SIFResourceManager*					GetResourceManager() const					{ return nullptr; }
	inline SIFPhaseController*					GetPhaseController() const					{ return nullptr; }
	inline SIFWindowSystem*						GetWindowSystem() const						{ return nullptr; }
	inline SIFUserSetting*						GetConsoleSettings() const					{ return nullptr; }
	inline SIFUnlockableManager*				GetUnlockableManager() const				{ return nullptr; }	
	inline SIFAudio*                            GetAudio() const                            { return (SIFAudio*)(ru_audio.Get()); }
	inline RUAudio*                             GetRUAudio() const                          { return ru_audio.Get(); }
	inline SIFFullModeUnlocker*					GetFullModeUnlocker()						{ return nullptr; }
	inline RUCommentary*						GetCommentarySystem()						{ return commentary_system; }
	inline SSAttractSequenceManager*			GetAttractSequenceManager() const			{ return nullptr; }
	inline URUTeamFacesGenerator*				GetTeamFacesGenerator() const				{ return team_faces_generator; }
	inline TSharedPtr<MabThread>				GetCareerDraftingThread() const				{ return drafting_thread; }
	inline TSharedPtr<class FMeshMergeThread>	GetMergeThread() const						{ return merge_thread; }

	inline void									SetCareerDraftingThread(TSharedPtr<MabThread> val) { drafting_thread = val; }

	APostProcessingRugby*						GetPostProcessManager();
	void										ClearPostProcessManager();

#if !defined ENABLE_NO_MABINPUT
	inline MabControlActionManager*				GetUIControlActionManager()					{ return nullptr; }
	inline MabControlActionManager*				GetGameControlActionManager()				{ return nullptr; }
#endif

#if defined ENABLE_DEBUG_KEYS
	inline MabControlActionManager*				GetDebugControlActionManager()				{ return nullptr; }
#endif

	/// Exits the application in a platform independent manner.
	void ExitApp(int return_code = 0, bool force = false);

	// const because we shouldn't be changing this
	const FRugbyAnimationLibrary* GetAnimationLibrary() const { return m_AnimationLibrary.IsValid() ? m_AnimationLibrary.Get() : nullptr; }

	//Window system replacement
	void SetDBLoadPending(bool val = true);
	bool GetDBLoadPending() { return pending_db_activity; }

	//Handle screen transitions waiting until db loads are finished
	UFUNCTION()
	void DBLoadComplete(float InDuration);

	FWWUIPendingDBLoadComplete pendingDBDelegate;

	/*#rc3_legacy
	inline SIFObjectFactory*					GetObjectFactory() const					{ return object_factory; }
	inline SIFResourceManager*					GetResourceManager() const					{ return resource_manager; };
	inline SIFPhaseController*					GetPhaseController() const					{ return phase_controller; };
	inline SIFWindowSystem*						GetWindowSystem() const						{ return window_system; };
	inline SIFUserSetting*						GetConsoleSettings() const					{ return console_settings; }
	inline SIFUnlockableManager*				GetUnlockableManager() const				{ return unlockable_manager; }
	inline SIFAudio*							GetAudio() const							{ return (SIFAudio*)ru_audio; }
	inline RUAudio*								GetRUAudio() const							{ return ru_audio; }*/
	inline SIFSoundMessageListener*				GetSoundMessageListener()					{ return sound_msg_listener; }
	inline SSCameraMessageListener*				GetCameraMessageListener()					{ return camera_msg_listener; }
	inline USIFMissingControllerListener*		GetMissingControllerListener()				{ return missing_controller_listener; }
	inline MabNetworkManager*					GetNetworkManager() const					{ return network_manager; }
	inline MabPeerGroupManager*					GetPeerGroupManager() const					{ return peer_group_manager; }
	inline SIFNetworkNonGameMessageHandler*		GetNonGameMessageHandler() const			{ return non_game_message_handler; }
	/*inline SIFFullModeUnlocker*					GetFullModeUnlocker() const				{ return full_mode_unlocker; }
	inline RUCommentary*						GetCommentarySystem() const					{ return commentary_system; }
	inline SIFSystemKeyboard*					GetSystemKeyboard() const					{ return system_keyboard; }
	inline NMMabAnimationRepository*			GetAnimationRepository() const				{ return animation_repository; }
	inline SSAttractSequenceManager*			GetAttractSequenceManager() const			{ return attract_sequence_manager; }
	inline URUTeamFacesGenerator*				GetTeamFacesGenerator() const				{ return team_faces_generator; }


	#if !defined ENABLE_NO_MABINPUT
	inline MabControlActionManager*				GetUIControlActionManager()					{ return ui_control_action_manager; }
	inline MabControlActionManager*				GetGameControlActionManager()				{ return game_control_action_manager; }
	#endif

	#if defined ENABLE_DEBUG_KEYS
	inline MabControlActionManager*				GetDebugControlActionManager()				{ return debug_control_action_manager; }
	#endif
	
	*/
	inline ULeaderboardManager*					GetLeaderboardManager() const { return leaderboard_manager; }
	inline UMatchingManager *GetMatchmakingManager() const {
		MABASSERT(matchmaking_manager);
		return matchmaking_manager;
	}

	UFUNCTION(BlueprintPure)
	UVoipManager* BP_GetVoipManager() const { return voip_manager; }

	inline UVoipManager* GetVoipManager() const{ return voip_manager; }

	SIFAsyncLoadingThread*						GetAsyncLoadThread() { return async_loading_thread; }
	SIFAsyncLoadingThread*						GetAsyncServerLoadThread() { return async_server_loading_thread; }
	SIFAsyncLoadingThread*						GetAsyncProfileLoadThread() { return async_profile_loading_thread; }

	/// Get the rich presence system pointer
	inline SIFRichPresence*						GetRichPresence() { return rich_presence; }


	//Static instance accessor //RussellD Refer to note below
	static URugbyGameInstance* GetInstance() { return PtrRugbyGameInstance; }
	static URugbyGameInstance* GetApplication() { return PtrRugbyGameInstance; }

	void ToggleMainMenuLighting(bool ShouldTurnOff);

	bool IsLanguageLoaded() { return languageLoaded; }

	bool SwapLocalPlayer(int32 targetIndex, ULocalPlayer* swpapedController);
	bool FastRemoveLocalPlayer(int32 targetIndex);
	//////////////////////////////////////////////////////////////////////////
	// Player Management
	TArray<ARugbyPlayerController*> GetLocalPlayerControllers() const;
	ARugbyPlayerController* GetFirstLocalRugbyPlayerController() const;

	ULocalPlayer* GetLocalPlayerFromControllerId(const int controllerId);

	ARugbyPlayerController* GetLocalPlayerControllerFromControllerId(int controllerId) const;
	ARugbyPlayerController* GetLocalPlayerControllerFromPlayerId(int playerId) const;

	ARugbyPlayerController* GetPlayerControllerFromControllerId(int controllerId) const;
	ARugbyPlayerController* GetPlayerControllerFromPlayerId(int playerId) const;

	//Get and set the unique net id
	TSharedPtr<const FUniqueNetId> GetPrimaryAccountUniqueNetId() { return m_pPrimaryAccountUniqueNetId; };
	void SetPrimaryAccountUniqueNetId(TSharedPtr<const FUniqueNetId> uniqueNetId) { m_pPrimaryAccountUniqueNetId = uniqueNetId; };

	//Helper functions to get the master player as a player, player controller or controller index.
	ULocalPlayer* GetPrimaryAccountPlayer();

	UFUNCTION(BlueprintCallable, Category = "Input")
	APlayerController* GetPrimaryAccountPlayerController();

	int GetPrimaryAccountControllerIndex();

	//Get and set the master controller index, the controller currently leading game interaction
	int GetMasterControllerId() 
	{ 
#if wwDEBUG_ARB
		if (m_masterControllerId < 0)
		{
			m_masterControllerId = 0;
		}
#endif
		return m_masterControllerId; 
	};
	void SetMasterControllerId(int controllerId) { m_masterControllerId = controllerId; };

	//Helper functions for the master controller ID
	APlayerController* GetMasterPlayerController() {
		return GetLocalPlayerControllerFromControllerId(GetMasterControllerId());
	}

	bool GameInDirtyState()
	{
		return false;
	}

	bool IsInFanHub()
	{
		return false;
	}
	// Start task to get user privileges.
	void StartOnlinePrivilegeTask(const IOnlineIdentity::FOnGetUserPrivilegeCompleteDelegate& Delegate, EUserPrivileges::Type Privilege, TSharedPtr< const FUniqueNetId > UserId, bool SilentCheck = false);

	// Do task to get user privileges for specific player controller, useful to test from ui menus
	void DoOnlinePrivilegeTask(const IOnlineIdentity::FOnGetUserPrivilegeCompleteDelegate& Delegate, EUserPrivileges::Type Privilege, APlayerController* PlayerController, bool SilentCheck = false);
	// Do task to get user privileges for specific player controller, useful to test from ui menus
	void DoOnlinePrivilegeTask(const IOnlineIdentity::FOnGetUserPrivilegeCompleteDelegate& Delegate, EUserPrivileges::Type Privilege, ULocalPlayer* PlayerController, bool SilentCheck = false);

	// Common cleanup code for any Privilege task delegate
	void CleanupOnlinePrivilegeTask(bool IsSilentCheck = false);

	// Show approved dialogs for various privileges failures
	void DisplayOnlinePrivilegeFailureDialogs(const FUniqueNetId& UserId, EUserPrivileges::Type Privilege, uint32 PrivilegeResults);
	void ShowErrorUIPopupFanHubConnectionError();
	void ShowUGCPrivilegeError();
	virtual bool IsAnyUserRestricted() override;
	bool IsNonPrimaryUserRestricted();
	bool GetCheckingAllUsers();
	bool GetLastRestrictionCheckFailed();
	int32 GetLastRestrictionCheckFailedPrivilegeResults();
	void CheckAnyUserRestricted();
	void HandleAnyUserCheck(const FUniqueNetId& UserId, EUserPrivileges::Type Privilege, uint32 PrivilegeResults);

	// User restriction check, will check all local players and save the results in their player controllers
	void StartUserRestrictionCheck(EUserPrivileges::Type Privilege);

	void OnUserRestrictionResults(const FUniqueNetId& UserId, EUserPrivileges::Type Privilege, uint32 PrivilegeResults);

	// Checks to see if any local player is User-generated content restricted
	bool bUGCRestricted = true;
	bool bHaveSetUGC = false;
	//THIS IS ONLY SET DURING ONLINE DO NOT USE FOR OFFLINE
	bool IsAnyOnlineControllerUGCRestricted();

	bool bPrimaryProfileWasSwitchedWhileDeactivated = false;
	bool bProfileHasBeenSwitched = false;

	//////////////////////////////////////////////////////////////////////////
	// Core Delegates

	bool ShouldPauseWhenConstrained();

	// Callback to pause the game when the OS has constrained our app.
	void HandleAppWillDeactivate();

	// Callback to un-pause when we reactivate the app again
	void HandleAppHasReactivated();

	// Callback to pause the game when the OS has constrained our app.
	void HandleAppConstrained();

	// Callback to un-pause when we reactivate the app again
	void HandleAppUnconstrained();

#if PLATFORM_XBOXCOMMON
	void CheckXboxControllers();
#endif

	void HandleAppSuspendedCallback();
	// Callback occurs when game being suspended
	void HandleAppSuspended();

	bool StartMatchMaking_Switch();
	//This function handles the actual resume logic, as there was too much to run in resume without causing crashes.
	void HandleAppResumedCallback();

	// Callback occurs when game resuming
	void HandleAppResumed();


	bool LastRestrictedValue = false;
	bool LastRestrictionCheckFailed = false;
	int32 LastRestrictionCheckFailedPrivilegeResults = false;
	bool SomeUserHasUGCRestrictions = false;
	bool NonPrimaryUserIsRestrictions = false;
	bool DoneCheckingAllUsers = false;
	int32 NumSystemUsersToCheck = 0;
	//////////////////////////////////////////////////////////////////////////
	// Networking
public:

	void CheckLicenced();
	bool CheckTeamPlayerControllerDisconnect();
	bool CheckPrimaryPlayerControllerDisconnect();
	bool CheckAllPlayerControllerDisconnect();
	int GetMinConnectedControllerId();
	void CheckControllerDisconnect();
	void DropOutLocalPlayersWithDisconnectedControllers();
	void DropOutAllControllers();
	void CheckPendingInvite();

	void DestroyHeadRenderer();
	void UpdateLoadingState();
	void LockOnlineSessionNoPlayerCount();
	void LockOnlineSession();
	void UpdateOnlineSessionOpenSlots(int32 OpenSlots);
	void UnLockOnlineSession(int32 OpenSlots);
	bool AllPlayersNetReady();

	



	bool ShouldBlockForOnlineMatchEnd();
	bool ShouldBlockForInvite();
	// @return OnlineSession class to use for this player
	TSubclassOf<class UOnlineSession> GetOnlineSessionClass() override;


	//void BeginMatchMaking();
	//bool HostGame(ULocalPlayer* LocalPlayer);
	//bool JoinSession(ULocalPlayer* LocalPlayer, int32 SessionIndexInSearchResults);
	//bool JoinSession(ULocalPlayer* LocalPlayer, const FOnlineSessionSearchResult& SearchResult);
	//bool FindSessions(ULocalPlayer* PlayerOwner, bool bIsDedicatedServer, bool bLANMatch);
	//void FinishJoinSession(EOnJoinSessionCompleteResult::Type Result);

//	void SetPendingInvite(const FRugbyPendingInvite& InPendingInvite);

//	void SendPlayTogetherInvites();
//	void OnPlayTogetherEventReceived(const int32 UserIndex, const TArray<TSharedPtr<const FUniqueNetId>>& UserIdList);
//	void ResetPlayTogetherInfo() { PlayTogetherInfo = FRugbyPlayTogetherInfo(); }

	ARugbyGameSession* GetGameSession() const;
	//void OnSearchSessionsComplete(bool bWasSuccessful);
	//void OnRegisterLocalPlayerComplete(const FUniqueNetId& PlayerId, EOnJoinSessionCompleteResult::Type Result);
	//void OnCreatePresenceSessionComplete(FName SessionName, bool bWasSuccessful);
	//void OnJoinSessionComplete(EOnJoinSessionCompleteResult::Type Result);
	//void OnRegisterJoiningLocalPlayerComplete(const FUniqueNetId& PlayerId, EOnJoinSessionCompleteResult::Type Result);
	//void FinishSessionCreation(EOnJoinSessionCompleteResult::Type Result);
	void TravelLocalSessionFailure(UWorld *World, ETravelFailure::Type FailureType, const FString& ErrorString);

	void UpdateConsoleVarsForMenu(bool bEnteringMenu);
	void InternalTravelToSession(const FName& SessionName);

	bool GetReadyToLoadOnlineClientWorld()
	{
		return ReadyToStartClientWorldLoad;
	}

	bool ReadyToStartClientWorldLoad = false;
	//void SetLobbyPrivacy(EPrivacyLevel InLobbyPrivacyLevel);
	//EPrivacyLevel GetLobbyPrivacy() const { return LobbyPrivacyLevel; }

	// Updates the status of using multiplayer features
	void UpdateUsingMultiplayerFeatures(bool bIsUsingMultiplayerFeatures);

#if PLATFORM_SWITCH
	// Locks or unlocks a session for switch.
	bool SetSessionMatchmakingParticipationStatus(bool bOpen);
#endif

	// Returns true if the passed in local player is signed in and online
	bool IsLocalPlayerOnline(ULocalPlayer* LocalPlayer);

	// Returns true if the primary local player is signed in and online
	bool IsPrimaryPlayerOnline();

	bool GetPrimaryPlayerAutoSignInData(FString& OutAccountId, FString& OutOnlineId);

	UWWFanhubHttpService* GetFanHubService() { return FanHubService; }

	bool FunhubShouldDropTasks = false;

#ifdef ENABLE_ANALYTICS
	UWWFanhubAnalyticsManager* GetAnalyticsManager() { return FanhubAnalyticsManager; }
	UWWFanhubAnalyticsClient* GetAnalyticsClient() { return FanhubAnalyticsClient; }

	TArray<FString>* GetAnalyticsDataLocation() { return m_pAnalyticsDataLocation; }
	void SetAnalyticsDataLocation(TArray<FString>* loc) { m_pAnalyticsDataLocation = loc; }

	uint32 GetAnalyticsID() { return m_analyticsID; }
	void SetAnalyticsID(uint32 id) { m_analyticsID = id; }
#endif

	void PreventObjectGC(UObject* pObject) { PreventGCList.Remove(nullptr); PreventGCList.AddUnique(pObject); }
	void AllowObjectGC(UObject* pObject) { PreventGCList.Remove(pObject); }

	void ClearVOIPOverlay()
	{
		m_voipScreen = nullptr;
	}
private:
	TArray< FSavedErrorPopup> SavedErrorPopups;
	EOnlineMode OnlineMode = EOnlineMode::Offline;
	EPrivacyLevel LobbyPrivacy = EPrivacyLevel::Public;
	EMatchmakingMode MatchingMode = EMatchmakingMode::Ranked;

	FDelegateHandle m_OnJoinSessionCompleteDelegateHandle;
	FDelegateHandle m_OnCreatePresenceSessionCompleteDelegateHandle;
	bool bIsReadyForInvites = false;
public:

	UPROPERTY()
	URUTeamFacesGenerator*				team_faces_generator = nullptr;

	bool IsReadyForInvites()
	{
		return bIsReadyForInvites;
	}

	bool IsReadyToHostPlaytogether()
	{
		return CanHostPlaytogether;
	}

	void SetReadyForInvites(bool newReady)
	{
		bIsReadyForInvites = newReady;
		CanHostPlaytogether = true;
	}

	bool JoinSession(ULocalPlayer* LocalPlayer, int32 SessionIndexInSearchResults);
	bool JoinSession(ULocalPlayer* LocalPlayer, const FOnlineSessionSearchResult& SearchResult);	

	void OnCreatePresenceSessionComplete(FName SessionName, bool bWasSuccessful);
	void FinishJoinSession(EOnJoinSessionCompleteResult::Type Result);

	void OnJoinSessionComplete(EOnJoinSessionCompleteResult::Type Result);
	void FinishSessionCreation(EOnJoinSessionCompleteResult::Type Result);

	FString GetCachedSessionURL() { return CachedSessionURL; };

	void Logout_Switch();
	void LogoutAndStopNEXService_Switch();
	void OnStartSession(bool FromInvite = false);
	void OnEndSession(bool FromInvite = false);
	bool TryingToStartOnlineMode = false;
	EOnlineSubMenuCloseType SubMenuCloseType = EOnlineSubMenuCloseType::TNone;
	bool CloseSubMenu = false;
	bool WaitingForHostCheck = true;
	bool HostIsAlive = false;
	void HideOnlineScreens();

	bool IsErrorPopupPending()
	{
		return bShowErrorPopup /*|| bErrorPopupOnScreen || (ErrorPopupCount > 0)*/;
	}

	void CloseAllSubMenuFromCareerHUB()
	{
		CloseSubMenu = true;
		SubMenuCloseType = EOnlineSubMenuCloseType::TMainMenu;
	}
	
	void CloseAllSubMenuFromOnlineMode()
	{
		CloseSubMenu = true;
		SubMenuCloseType = EOnlineSubMenuCloseType::TMainMenu;
	}

	void ReturnToOnlineSubMenu()
	{
		CloseSubMenu = true;
		SubMenuCloseType = EOnlineSubMenuCloseType::TOnlineMenu;
	}


	class UFriendPlayManager* CurrentFriendPlayManager = nullptr;


	void SetFriendPlayManager(class UFriendPlayManager* newFriendPlayManager)
	{
		CurrentFriendPlayManager = newFriendPlayManager;
	}


	void HostListen();
	void HostSession_Switch();
	void JoinSession_Switch(FName SessionName);
	bool HostGame(ULocalPlayer* LocalPlayer, bool PrivateMatch = false);
	int32 GetNumConnectedPlayers()
	{
		return 1;
	}

	void SetMatchmakingMode(EMatchmakingMode newMatchingMode)
	{ 
		MatchingMode = newMatchingMode;
	};

	EMatchmakingMode GetMatchmakingMode()
	{ 
		return MatchingMode;
	};
	
	void SetOnlineMode(EOnlineMode newMode)
	{
		OnlineMode = newMode;

		UE_LOG(LogTemp, Display, TEXT("URugbyGameInstance::SetOnlineMode Setting online mode to: %d new game settings network game state: %d"), OnlineMode, OnlineMode != EOnlineMode::Offline);

		RUGameSettings* settings = GetMatchGameSettings();
		settings->game_settings.network_game = OnlineMode != EOnlineMode::Offline;

		if (OnlineMode == EOnlineMode::Online)
		{
	
		}

		if (OnlineMode == EOnlineMode::Offline)
		{

		}
	};

	EOnlineMode GetOnlineMode()
	{
		return OnlineMode;
	};

	void SetLobbyPrivacy(EPrivacyLevel newLobbyPrivacy)
	{
		LobbyPrivacy = newLobbyPrivacy;

		GetMatchGameSettings()->game_settings.private_match = (LobbyPrivacy != EPrivacyLevel::Public);
	};

	EPrivacyLevel GetLobbyPrivacy()
	{
		return LobbyPrivacy;
	}

	void SaveErrorUIPopup(const FString& HeadingString, const FString& ErrorString, bool allowCancel = true);
	void CheckSavedErrorPopups();

	bool CheckPrivlageResultFlag(uint32 vlaues, uint32 flag)
	{
		return ((vlaues & flag) == flag);
	}


	FString GetPrivlageResultsString(uint32 vlaues)
	{
		FString FoundString = "";

		if (CheckPrivlageResultFlag(vlaues, (uint32)IOnlineIdentity::EPrivilegeResults::AccountTypeFailure))
		{
			FoundString += "AccountTypeFailure | ";
		}

		if (CheckPrivlageResultFlag(vlaues, (uint32)IOnlineIdentity::EPrivilegeResults::RequiredSystemUpdate))
		{
			FoundString += "RequiredSystemUpdate | ";
		}

		if (CheckPrivlageResultFlag(vlaues, (uint32)IOnlineIdentity::EPrivilegeResults::RequiredPatchAvailable))
		{
			FoundString += "RequiredPatchAvailable | ";
		}

		if (CheckPrivlageResultFlag(vlaues, (uint32)IOnlineIdentity::EPrivilegeResults::AgeRestrictionFailure))
		{
			FoundString += "AgeRestrictionFailure | ";
		}

		if (CheckPrivlageResultFlag(vlaues, (uint32)IOnlineIdentity::EPrivilegeResults::UserNotFound))
		{
			FoundString += "UserNotFound | ";
		}

		if (CheckPrivlageResultFlag(vlaues, (uint32)IOnlineIdentity::EPrivilegeResults::GenericFailure))
		{
			FoundString += "GenericFailure | ";
		}

		if (CheckPrivlageResultFlag(vlaues, (uint32)IOnlineIdentity::EPrivilegeResults::NetworkConnectionUnavailable))
		{
			FoundString += "NetworkConnectionUnavailable | ";
		}

		if (CheckPrivlageResultFlag(vlaues, (uint32)IOnlineIdentity::EPrivilegeResults::ChatRestriction))
		{
			FoundString += "ChatRestriction | ";
		}

		if (CheckPrivlageResultFlag(vlaues, (uint32)IOnlineIdentity::EPrivilegeResults::UGCRestriction))
		{
			FoundString += "UGCRestriction | ";
		}

		if (CheckPrivlageResultFlag(vlaues, (uint32)IOnlineIdentity::EPrivilegeResults::OnlinePlayRestricted))
		{
			FoundString += "OnlinePlayRestricted";
		}

		return FoundString;
	}
	// Creates a new online privilege check object. Does an online check first.
	void CreateNewOnlinePrivilegeTask(EUserPrivileges::Type Privilege, FString InOnCheckSuccessDelegate, UObject* TargetObject, bool ShouldShowStoreUI = false, bool AllowProgress = true, bool silentCheck = false);

	UFUNCTION()
	void OnlinePrivilegeCheckFinished();

	TWeakObjectPtr<UObject> OnlineCheckTargetObject;

	UPROPERTY()
	FString OnlineCheckSuccessString;

	UPROPERTY()
	bool OnlineCheckShouldShowStoreUI = false;

	UPROPERTY()
	bool OnlineCheckAllowProgress = true;

	EUserPrivileges::Type OnlineCheckPrivilege = EUserPrivileges::Type::CanPlayOnline;

	// Display platform specific signin screen eg PS4's external/OS PSN login screen. Returns true if displayed.
	bool DisplayPrimaryPlayerOnlineSignin(const FOnLoginUIClosedDelegate& Delegate);

	bool DisplayPlayerOnlineSignin(const FUniqueNetId& UserId, const FOnLoginUIClosedDelegate& Delegate);

	bool ShowLoginUI(const int ControllerIndex, bool bShowOnlineOnly, bool bShowSkipButton, bool UseLastSendControllerEventsGamepadId /*= false*/, const FOnLoginUIClosedDelegate& Delegate /*= FOnLoginUIClosedDelegate()*/);
	// Display platform specific signin screen eg PS4's external/OS PSN login screen. Returns true if displayed.
	bool DisplayLocalPlayerOnlineSignin(ULocalPlayer* LocalPlayer, const FOnLoginUIClosedDelegate& Delegate);

	// Returns true if the passed in local player is signed in
	bool IsLocalPlayerSignedIn(ULocalPlayer* LocalPlayer);

	// Returns true if the primary local player is signed in
	bool IsPrimaryPlayerSignedIn();

	bool CheckPrivilegeResultFlag(uint32 vlaues, uint32 flag)
	{
		return ((vlaues & flag) == flag);
	}

	bool bShowErrorPopup;

	UPROPERTY()
		UWWUIStateScreenModalData* CurrentErrorPopupData;

	bool NeedToBlockErrorModalRemoval = false;

	/** Transient properties of a session during game creation/matchmaking */
	FRugbyGameSessionParams CurrentSessionParams;
	/** Current host settings */
	TSharedPtr<class FRugbyOnlineSessionSettings> HostSettings;
	void SetHostSetting(TSharedPtr<class FRugbyOnlineSessionSettings> NewHostSettings)
	{
		HostSettings = NewHostSettings;
	}
	/** Current search settings */
	TSharedPtr<class FRugbyOnlineSearchSettings> SearchSettings;

	bool GetShuttingDownOnlineSession()
	{
		return ShuttingDownOnlineSession;
	}

	void SetShuttingDownOnlineSession(bool shutdown)
	{
		ShuttingDownOnlineSession = shutdown;
	}



	bool DoInitalConnectionCheck = true;

	void SetPresenceForLocalPlayers(const FString& StatusStr, const FVariantData& PresenceData);

private:
//	FRugbyPendingInvite PendingInvite;

//	FRugbyPlayTogetherInfo PlayTogetherInfo;
	// Privacy level of the lobby (public, friends only, invites only.)
	//EPrivacyLevel LobbyPrivacyLevel;
	// Current online mode of the game (offline, LAN, or online)
	//EOnlineMode OnlineMode;
	// Current matchmaking mode of the game (ranked, unranked)
	//EMatchmakingMode MatchmakingMode;
	//bool bIsMatchMaking;

	// True whene we are in the fanhub, used for the connection checking.
	bool bFanHubMode;
	// Handle to various registered delegates
	FDelegateHandle TravelLocalSessionFailureDelegateHandle;
	//FDelegateHandle OnJoinSessionCompleteDelegateHandle;
	//FDelegateHandle OnSearchSessionsCompleteDelegateHandle;

	//FDelegateHandle OnCreatePresenceSessionCompleteDelegateHandle;

	bool ShuttingDownOnlineSession = false;

	// Holds a cached value for the URL we travel to when joining a session through URugbyGameInstance::InternalTravelToSession.
	// This means we don't have to get it again.
	FString CachedSessionURL;

	UPROPERTY()
	TArray<UObject*> PreventGCList;

	UPROPERTY()
	TArray<UPlatformStatusChecker*> OnlinePrivilegeChecks;

	UPROPERTY()
	UWWFanhubHttpService* FanHubService;

	//#ifdef ENABLE_ANALYTICS
	UPROPERTY()
	UWWFanhubAnalyticsManager* FanhubAnalyticsManager;

	UPROPERTY()
	UWWFanhubAnalyticsClient* FanhubAnalyticsClient;

	//Do not make this a Uproperty, we don't want to serialise to disk.
	TArray<FString>* m_pAnalyticsDataLocation; //Location to copy the analytics data from (analytics client).

	uint32 m_analyticsID;
	//#endif //ENABLE_ANALYTICS

	UPROPERTY()
	UConnectionChecker * ConnectionChecker;

	bool NetworkErrorHandled = false;
	bool HasInitalConnection = false;

private:
	//RussellD This is not going to work long-term, makes for a nightmare testing multiplayer games which each have different game instances, but share a application.
	static URugbyGameInstance* PtrRugbyGameInstance;
	void StoreGameInstance() { PtrRugbyGameInstance = this; }
	//

	// RussellD: RC3 variables, investigate cleaning these up
	bool initialised;
	bool running;
	//

	// Whether the user has an active license to play the game
	bool bIsLicensed;

	//Keep track of if the active login has changed.
	bool LoginChanged = false;

	// Delegate for callbacks to Tick
	FTickerDelegate TickDelegate;

	// Handle to various registered delegates
	FDelegateHandle TickDelegateHandle; // https://forums.unrealengine.com/development-discussion/c-gameplay-programming/52553-gameinstance-managers-and-where-are-the-ticks

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		TSubclassOf<ARugbyCharacter> DefaultRugbyPlayerClass;

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		TSubclassOf<ASSBall> DefaultBallClass;

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		TSubclassOf<AActor> DefaultRuckIndicator;

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		TSubclassOf<AActor> DefaultSetplayIndicator;

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		TSubclassOf<UWWUIScreenTemplate> DefaultWipeScreen;

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		UWWUIScreenTemplate * WipeScreen;

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		UDataTable * CameraMainDt;

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		UDataTable * CameraGameDt;

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		UDataTable * CameraReplayDt;

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		UDataTable * CameraSetsDt;

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		UDataTable * CameraBlendsDt;

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		UDataTable * CameraNoiseDt;

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		UDataTable * ThirteensSetplaysDt;

	// WJS RLC Not Needed UPROPERTY(EditDefaultsOnly, Category = "Defaults")
	// WJS RLC Not Needed UDataTable* FifteensFormationsDt;

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		UDataTable* ThirteensFormationsDt;

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		UDataTable * SevensSetplaysDt;

	UPROPERTY(EditDefaultsOnly, Category = "Defaults")
		UDataTable * SevensFormationsDt;

	UPROPERTY()
		TArray<AActor *> RuckJoinIndicators;
	
	UPROPERTY(config)
		FString IntroMapPath;
	UPROPERTY(config)
		FString MenuMapPath;
	UPROPERTY(config)
		FString SandboxMapPath;
		
	UPROPERTY()
		URugbyLevelManager* m_LevelManager;

	UPROPERTY()
		UDatabaseManager* m_DatabaseManager;	
	
	// WW UI interface implementation
	static const FString TransitionManagerScreenID;

	UPROPERTY()
		UWWUITranslationManager* translationManager;
	UPROPERTY()
		UWWUITranslatorStringTable* ProjTranslator;
	UPROPERTY()
		UWWUITranslatorStringTable* InitTranslator;
	UPROPERTY()
		UWWUITranslatorStringTable* FranchiseTranslator;
	UPROPERTY()
		UWWUITranslatorStringTable* GlobalTranslator;
	UPROPERTY()
		UWWUITranslatorInputAction* InputTranslator;
	UPROPERTY()
		UWWUITranslatorBasic* PlatformTranslator;
	UPROPERTY()
		UWWUITranslatorBasic* TutorialTranslator;


	UPROPERTY()
		UUIDataServer* 					UIDataServer;
	UPROPERTY()
		UWWUIScreenManager* 			UIScreenManager;
	UPROPERTY()
		UWWUITransitionManager* 		TransitionManager;

	UPROPERTY()
		USoundBank* SoundBank;

	/// Functional managers that the game instance owns.
	UPROPERTY()
		UFlowControlManager*			flow_manager = nullptr;

	UPROPERTY()
		APostProcessingRugby*		postProcess_manager;

	FStreamableManager					m_StreamableManager;


	/// Application configuration parameters object.
	SIFApplicationParameters			app_params;
	SIFAppTime*							app_time;
	SIFLevelLauncher*					level_launcher;
	SIFStatisticsHandler*				statistics_handler;
	//SIFLeaderboardQueue*				leaderboard_queue;
	SIFAchievementChecker*				achievement_checker;
	RURugbyDollarsChecker*				rugby_dollars_checker;
	RUStatsScorer*						stats_scorer;
	MabCentralTypeDatabase2*			type_database;
	RUGameDatabaseManager*				gamedb_manager;
	RUStatisticsSystem*					stats_system;
	RUDBHelperInterface*				game_db_helper;
	RUCareerModeManager*				career_mode_manager;
	RUCompetitionCustomisationHelper*	competition_customisation_helper;
	RUUIDatabaseQueryManager*			db_query_manager;
	SIFSoakManager*						soak_manager;
	MabEVDS*							evds;

	/// Active Game World
	SIFGameWorld*						m_ActiveGameWorld;

	/// Match Instance Managers
	SIFGameWorld*						m_MatchGameWorld; // DH - creating the worlds here for now to see if it works with the flow, instead of having to do FetchResource("SIFTutorialGameWorld")) etc
	UPROPERTY()	URugbyGameWorldSettings*	m_MatchGameSettings;
	SIFGamePauseState*					m_MatchPauseState;
	RUStadiumManager*					m_MatchStadiumManager;

	/// Menu Instance Managers
	SIFGameWorld*						m_MenuGameWorld;
	UPROPERTY()	URugbyGameWorldSettings*	m_MenuGameSettings;

	/// Sandbox Instance Managers
	RUSandboxGame*						m_SandboxGame;
	//SIFGameWorld*						m_SandboxGameWorld; // RussellD : Trialing hosting the sandbox game world in the sandbox game state
	UPROPERTY()	URugbyGameWorldSettings*	m_SandboxGameSettings;
	SIFGamePauseState*					m_SandboxPauseState;
	RUStadiumManager*					m_SandboxStadiumManager;

	/// Some animation data that we cache at startup
	TUniquePtr<FRugbyAnimationLibrary>  m_AnimationLibrary;
	TUniquePtr<RUAudio>                 ru_audio;

	RUCommentary*						commentary_system;
	SIFRichPresence*					rich_presence;


	// Voip Screen
private:
	UPROPERTY()
	UWWUIVoipScreen* m_voipScreen = nullptr;

public:
	UWWUIVoipScreen* GetVoipScreen();
	void SetVoipScreen(UWWUIScreenTemplate* InVoipScreen);
	void SetVoipScreen(UWWUIVoipScreen* InVoipScreen) { m_voipScreen = InVoipScreen; };

	void HandleVoipScreenMatchReady(bool InMatchReady);
	void VoipScreenTick(float deltaTime);
	void VoipScreenViewGamerCard(APlayerController* _PlayerController);
	void VoipScreenMuteUser(APlayerController* _PlayerController);
	void VoipScreenKickUser(APlayerController* _PlayerController);
	void VoipScreenVoipListSetFocus(APlayerController* _PlayerController);
	void VoipScreenVoipListRemoveFocus();
	void VoipScreenUpdateVoipListSetFocus(float axisValue, APlayerController* _PlayerController);
	void VoipScreenShowHideLegend(bool InShowLegends);
	void VoipScreenOverrideReadyStates(bool InOverride, bool InReadyState, ARugbyPlayerState* InPlayerState);
	void VoipScreenSetTeamHeaderDetails(bool inSetTeamHeaderDetails);
	VoipScreenCanMutePlayerResult VoipScreenCanMuteUser();
	// Voip Screen

private:

	bool languageLoaded = false;

	// BlendNThread
	TSharedPtr<class FJobThreadManager>		job_thread_manager	= nullptr;
	TSharedPtr<class FMeshMergeThread>		merge_thread		= nullptr;

	TSharedPtr<MabThread>	drafting_thread;

	//Window system replacements
	bool pending_db_activity = false;

	/*#rc3_legacy
	SIFObjectFactory*					object_factory;
	SIFWindowSystem*					window_system;
	SIFResourceManager*					resource_manager;
	SIFPhaseController*					phase_controller;
	SIFUserSetting*						console_settings;*/
	SIFUnlockableManager*				unlockable_manager;
	SIFSoundMessageListener*			sound_msg_listener;
	SSCameraMessageListener*			camera_msg_listener;
	RURumbleProfileListener*			rumble_msg_listener;
	/*RUHUDPlayerInfoUpdater*			player_info_updater;
	SIFFullModeUnlocker*				full_mode_unlocker;
	RUAudio*							ru_audio;*/
	UPROPERTY()
	USIFMissingControllerListener*		missing_controller_listener;
	//SIFSystemKeyboard*				system_keyboard;
	//NMMabAnimationRepository* 		animation_repository;
	MabNetworkManager*					network_manager;
	MabPeerGroupManager*				peer_group_manager;
	SIFNetworkNonGameMessageHandler*	non_game_message_handler;

	UPROPERTY()
	class UUnrealTransport*				unreal_transport;
	/*SIFNetAddressProviderWrapper*		net_address_provider;
	SSAttractSequenceManager*			attract_sequence_manager;

	#if !defined ENABLE_NO_MABINPUT
	MabControlActionManager*			ui_control_action_manager;
	MabControlActionManager*			game_control_action_manager;
	#endif

	#ifdef ENABLE_DEBUG_KEYS
	MabControlActionManager*			debug_control_action_manager;
	#endif

	
	//*/
	UPROPERTY() ULeaderboardManager*				leaderboard_manager;
	UPROPERTY() ULeaderboardManager*				leaderboard_manager_7s;
	UPROPERTY() UMatchingManager*					matchmaking_manager;
	UPROPERTY() UVoipManager*						voip_manager;

	SIFAsyncLoadingThread*				async_loading_thread;
	SIFAsyncLoadingThread*				async_server_loading_thread;
	SIFAsyncLoadingThread*				async_profile_loading_thread;

	RU_GAME_REGION	ru_game_region;
	
	bool	switching_screen_mode;
	bool	loaded_pressed_start_resources;

	bool bHoldingFade;

	// Unique net ID for primary account holder
	TSharedPtr<const FUniqueNetId> m_pPrimaryAccountUniqueNetId = nullptr;

	// Stored when we suspend the game and used to check to see if anyone stole our gamepad while we were suspended.
#if PLATFORM_XBOXCOMMON && 0
	Windows::Xbox::Input::IGamepad^ m_primaryUserGamepad = nullptr;
#endif

	// Master controller index, leads UI interaction
	int m_masterControllerId = 0;

	MabMutex script_triggers_lock;
	struct ScriptTrigger
	{
		WWUINodeProperty parameters;
	};
	MabVector<ScriptTrigger> script_trigger_list;

	/*
	
	#rc3_legacy_pssg
	MabAtomic::Int32 suppress_rendering;
	PSSGMabBindQueue* bind_queue_ri;

	#rc3_legacy_threading
	void* render_thread_id;
	/// The async update thread
	MabThread* update_thread;
	*/

	FRugbyGameInstanceTick PrimaryTickFunction;

	/// Notify the update thread of a new frame.
	MabSemaphore signal_update = MabSemaphore(0, 1);
	/// Notify the main thread of a finished update.
	MabSemaphore signal_finished = MabSemaphore(0, 1);

	class FODWorkerThread* CurrentInnerThread = nullptr;
	class FRunnableThread* CurrentThread = nullptr;

public:
	inline SIFAchievementAwarder* GetAchievementAwarder() const { return achievement_awarder; }

private:
	SIFAchievementAwarder* achievement_awarder;

#if PLATFORM_WINDOWS

public:
	/// Allows the application to turn of and on the display of the mouse cursor (through WindowsAPI call)
	void SetMouseCursorActive(bool active);
	
	#ifdef ENABLE_STEAM
		inline SteamMabLobbyManager* GetOnlineGameManager() const { return steam_lobby_manager; }
		inline SIFSteamOnlineEventHandler* GetSessionEventHander() const { return online_event_handler; }
		inline SteamMabVoiceManager* GetVoiceManager() const { return steam_voice_manager; }	
		
	#else
		inline MabLanGameManager* GetOnlineGameManager() { return nullptr; } //#rc3_legacy { return pc_lan_game_manager; }
	#endif
		
		FOverlayActivated& GetSteamOverlayDelegate() { return steam_overlay_activated_delegate; }

		bool SaveDefaultSettingsToFile(FDefaultSaveSettings saveObject, const FString Filename);
		FDefaultSaveSettings LoadDefaultSettingsFromFile(const FString Filename, bool & loadSuccesful);

//#if (WITH_EDITOR == 0)
#ifdef ENABLE_STEAMCHECK
	void SteamStatCheck();
#endif

	

protected:
	bool InitialisePCSubsystems();

	void CleanupPCSubsystems();

private:
	/// Check whether the current user is allowed to play this game
	bool CheckParentalControlsAccess();

	bool mouse_active; //< Is the mouse currently being displayed

	#ifdef ENABLE_STEAM
		SIFSteamEventHandler* steam_event_handler;
		SteamMabLobbyManager* steam_lobby_manager;
		SIFSteamOnlineEventHandler* online_event_handler;
		SteamMabVoiceManager* steam_voice_manager;
	#else
		//#rc3_legacy MabLanGameManager* pc_lan_game_manager;
		//#rc3_legacy SIFPCOnlineEventHander* online_event_handler;
	#endif
	
	UWWUISteamOverlayTracker * steam_overlay_tracker;
	FOverlayActivated steam_overlay_activated_delegate;



#elif PLATFORM_PS4

public:
	/// Get the SPURS instance.
	//inline PS4MabSPURS& GetSPURS() const { return *spurs; }

	inline PS4MabOnlineScoreManager *GetOnlineScoreManager() { return ps4_online_score_manager; }
	inline PS4MabOnlineUserManager* GetOnlineUserManager() { return ps4_online_user_manager; }
	inline PS4MabOnlineGameManager* GetOnlineGameManager() { return ps4_online_game_manager; }

	inline SIFPS4CachedOnlineScoreHelper *GetCachedOnlineScoreHelper() { return ps4_cached_online_score_helper; }

	/// Gets the NPBasic object.
	inline PS4MabNPBasic* GetPS4MabNPBasic() { return ps4_np_basic_container; }

	/// Gets the NPDRM container object.
	inline PS4MabNPDRM* GetPS4MabNPDRM() { return ps4_np_drm_container; }

	/// Get the Session Event Handler
	inline SIFPS4SessionEventHandler* GetSessionEventHander() { return ps4_session_event_handler; }

	/// Returns whether the system UI is up
	inline bool IsSystemUIActive() { return system_ui_is_active; }

protected:
	/// Initialise the PS3 specific subsystems
	void InitialisePS4Subsystems();

	/// Cleanup the PS3 specific subsystems
	void CleanupPS4Subsystems();

	/// Handles a file error on the PS3
	void DoPS4FileErrorScreen(int error_code = 0/*CELL_GAME_ERRDIALOG_BROKEN_EXIT_HDDGAME*/);

	/// Handles exiting
	void DoPS4Exit();

	/// Error callback for the PS3 streaming packfile driver
	static void StreamingFSFileErrorCheckCallback(void* userdata);

	/// Error callback for when a bind error occurs when loading a profile
	/// @param	bind_error	OR'd value containing the bind errors associated with the load
	/// @return	Return a PS3MabSaveData::BindErrorCondition value that will
	///			instruct the utility on how to proceed with the bind error
	static int OnLoadDataBindError(unsigned int bind_error);

private:
	//PS3MabSPURS* spurs;
	PS4MabNPBasic * ps4_np_basic_container;
	PS4MabNPDRM* ps4_np_drm_container;

	SIFPS4SaveDataRequestHandler* ps4_pp_req_handler;

	// We store in a boolean if the system UI is drawing.
	bool system_ui_is_active;

	PS4MabOnlineUserManager *ps4_online_user_manager;
	PS4MabOnlineScoreManager *ps4_online_score_manager;
	PS4MabOnlineGameManager *ps4_online_game_manager;
	SIFPS4CachedOnlineScoreHelper *ps4_cached_online_score_helper;
	SIFPS4NPMessageHandler *ps4_message_handler;
	SIFPS4SessionEventHandler* ps4_session_event_handler;
	PS4MabWebAPI *ps4_webapi;

	bool exit_request;
	bool file_error_exit_request;
	pthread_t main_thread_id;
	volatile bool ps4_exit_in_progress;

#elif PLATFORM_SWITCH

public:
	/// Get the SPURS instance.
	//inline SwitchMabSPURS& GetSPURS() const { return *spurs; }

	/*inline SwitchMabOnlineScoreManager *GetOnlineScoreManager() { return Switch_online_score_manager; }
	inline SwitchMabOnlineUserManager* GetOnlineUserManager() { return Switch_online_user_manager; }
	inline SwitchMabOnlineGameManager* GetOnlineGameManager() { return Switch_online_game_manager; }

	inline SIFSwitchCachedOnlineScoreHelper *GetCachedOnlineScoreHelper() { return Switch_cached_online_score_helper; }

	/// Gets the NPBasic object.
	inline SwitchMabNPBasic* GetSwitchMabNPBasic() { return Switch_np_basic_container; }

	/// Gets the NPDRM container object.
	inline SwitchMabNPDRM* GetSwitchMabNPDRM() { return Switch_np_drm_container; }

	/// Get the Session Event Handler
	inline SIFSwitchSessionEventHandler* GetSessionEventHander() { return Switch_session_event_handler; }*/

	/// Returns whether the system UI is up
	//inline bool IsSystemUIActive() { return system_ui_is_active; }

protected:
	/// Initialise the PS3 specific subsystems
	//void InitialiseSwitchSubsystems();

	///// Cleanup the PS3 specific subsystems
	//void CleanupSwitchSubsystems();

	///// Handles a file error on the PS3
	//void DoSwitchFileErrorScreen(int error_code = 0/*CELL_GAME_ERRDIALOG_BROKEN_EXIT_HDDGAME*/);

	///// Handles exiting
	//void DoSwitchExit();

	///// Error callback for the PS3 streaming packfile driver
	//static void StreamingFSFileErrorCheckCallback(void* userdata);

	///// Error callback for when a bind error occurs when loading a profile
	///// @param	bind_error	OR'd value containing the bind errors associated with the load
	///// @return	Return a PS3MabSaveData::BindErrorCondition value that will
	/////			instruct the utility on how to proceed with the bind error
	//static int OnLoadDataBindError(unsigned int bind_error);

private:
	//PS3MabSPURS* spurs;
	//SwitchMabNPBasic * Switch_np_basic_container;
	//SwitchMabNPDRM* Switch_np_drm_container;

	SIFSwitchSaveDataRequestHandler* Switch_pp_req_handler;

	// We store in a boolean if the system UI is drawing.
	bool system_ui_is_active;

	/*SwitchMabOnlineUserManager *Switch_online_user_manager;
	SwitchMabOnlineScoreManager *Switch_online_score_manager;
	SwitchMabOnlineGameManager *Switch_online_game_manager;
	SIFSwitchCachedOnlineScoreHelper *Switch_cached_online_score_helper;
	SIFSwitchNPMessageHandler *Switch_message_handler;
	SIFSwitchSessionEventHandler* Switch_session_event_handler;
	SwitchMabWebAPI *Switch_webapi;*/

	bool exit_request;
	bool file_error_exit_request;
	//pthread_t main_thread_id;
	volatile bool Switch_exit_in_progress;

#elif PLATFORM_XBOXCOMMON && 0//#rc3_legacy_xboxone

public:
	
	void InitialiseXboxOneSubsystems();	
	void UnRegisterXboxOneSubsystems();
	/*
	inline XboxOneMabMatchMaking* GetMatchMakingManager() { return xboxone_matchMaking; }
	inline XboxOneMabUISessionController* GetUIEventController() {return ui_session_controller;}
	inline SIFXboxOneSessionEventHandler* GetSessionEventHander() { return session_event_handler; }
	inline XboxOneMabVoiceManager* GetVoiceManager() { return voice_manager; }
	*/
	inline SIFXboxOnePlayerProfileRequestHandler* GetPlayerProfileRequestHandler() { return xboxone_pp_req_handler; }
	inline XboxOneMabUserInfo* GetXboxOneUserInfo() { return xboxone_user_info; }
	inline SIFXboxOneSaveManager* GetSaveManager() { return save_manager; }
	
	/// Get the Save Request Manager for starting save/loads
	inline SIFXboxOneSqlRequestHandler* GetSqlRequestHandler() { return xboxone_sql_req_handler; }
	inline SIFXboxOneSaveRequestManager* GetSaveRequestManager() { return xboxone_request_manager; }
	//inline void SetAppStatus(XBOXONE_APP_STATUS suspended) {xboxone_app_status = suspended;}
	

	void OnSuspending(Platform::Object^ sender, Windows::ApplicationModel::SuspendingEventArgs^ args);
	void OnResuming(Platform::Object^ sender, Platform::Object^ args);
	void OnExiting(Platform::Object^ sender, Platform::Object^ args);
	void startSavingGameState();

	Windows::ApplicationModel::Activation::ActivationKind GetXboxOneLaunchMode() { return m_AppLaunchMode; }
	void SetXboxOneLaunchMode(Windows::ApplicationModel::Activation::ActivationKind mode) { m_AppLaunchMode = mode;}

protected:

private:
	XboxOneMabMatchMaking * xboxone_matchMaking;
	XboxOneMabUISessionController* ui_session_controller;
	SIFXboxOneSessionEventHandler* session_event_handler;
	XboxOneMabVoiceManager* voice_manager;
	SIFXboxOneSaveRequestManager* xboxone_request_manager;
	SIFXboxOnePlayerProfileRequestHandler* xboxone_pp_req_handler;
	SIFXboxOneSaveManager* save_manager;
	XboxOneMabUserInfo* xboxone_user_info;
	SIFXboxOneSqlRequestHandler* xboxone_sql_req_handler;
	XboxOneMabSystemNotificationHub* system_notifier;

	XBOXONE_APP_STATUS xboxone_app_status;

	volatile BOOL              m_requestSuspendRendering;
	BOOL                       m_isGPUSuspended;
	LONG                       m_numPendingAsyncOps;
	Windows::ApplicationModel::SuspendingDeferral^ m_suspendingDeferral;
	Windows::ApplicationModel::Activation::ActivationKind m_AppLaunchMode;

#endif

public:
	/// The Base Environment
	/// RussellD: Should we consider splitting this off into its own class, or deleting it?

	/*#rc3_legacy
	void CreateBaseEnvironment();
	void CleanupBaseEnvironment();
	void DeleteBaseEnvironment();
	SIFGameJobQueue*							GetEnvironmentJobQueue() const;
	SIFBruteForceLightDatabase*					GetEnvironmentLightDatabase() const;
	PSSGMabShaderManager*						GetEnvironmentShaderManager() const;
	SIFViewManager*								GetEnvironmentViewManager() const;
	PSSGMabDynamicLightLinkManager*				GetEnvironmentLightLinkManager() const;
	PSSGMabAnimation*							GetEnvironmentPssgAnimationManager() const;
	SIFPSSGUtil::SIFPSSGClonedShaderManager*	GetEnvironmentClonedShaderManager() const;
	*/
	
	/// The Deferred Queue
	/// RussellD: Should we consider splitting this off into its own class?

	/// Process all deferred queue entries. (SyncUpdate only!)
	void UpdateDeferredQueue();

#if 0 //#rc3_legacy
	void DeferredFree(PSSG::PObject* obj, bool add_front = false);
	void DeferredFree(MabResourceBase *resource, bool add_front = false);
	void DeferredAddChild(PSSG::PNode* parent, PSSG::PNode* node, unsigned int light_link_mask);
	void DeferredAddToRoot(SIFGraphicsHandle node, unsigned int light_link_mask);
	void DeferredTeamSkin(DeferredTeamSkinInfo *info);
	void DeferredPlayerDelete(ARugbyCharacter* player, SIFGameWorld *game);

	class DeferredQueueEntry
	{
	private:
		enum ENTRY_TYPE
		{
			ADDCHILD = 0,
			POBJECT_DELETE = 1,
			RESOURCE_DELETE = 2,
			TEAM_SKIN = 3,
			PLAYER_DELETE = 4
		};

		// check values for pointers cleared with MABMEM_DEBUG_FREE_INDICATOR or MABMEM_DEBUG_DESTROYHEAP_INDICATOR
#if (PLATFORM_PS4) || (PLATFORM_XBOXCOMMON)
		static const size_t BAD_OBJECT_VALUE = 0xb0b0b0b0b0b0b0b0;	// Attempt at fix for WW ticket 13551.
		static const size_t BAD_OBJECT_VALUE2 = 0xcdcdcdcdcdcdcdcd;
#else
		static const size_t BAD_OBJECT_VALUE = 0xb0b0b0b0;			// Attempt at fix for ticket 48439.
		static const size_t BAD_OBJECT_VALUE2 = 0xcdcdcdcd;
#endif

		DeferredQueueEntry(void *object, void *parent, unsigned int arg1, ENTRY_TYPE entry_type) :
			object(object), parent(parent), arg1(arg1), entry_type(entry_type)
		{
			MABASSERT((size_t)object != BAD_OBJECT_VALUE && (size_t)object != BAD_OBJECT_VALUE2);
		}

	public:
		static DeferredQueueEntry AddChild(PSSG::PNode *node, PSSG::PNode *parent, unsigned int light_link_mask)
		{
			return DeferredQueueEntry(node, parent, light_link_mask, ADDCHILD);
		}
		static DeferredQueueEntry Delete(PSSG::PObject *node)
		{
			return DeferredQueueEntry(node, NULL, 0, POBJECT_DELETE);
		}
		static DeferredQueueEntry Delete(MabResourceBase *resource)
		{
			return DeferredQueueEntry(resource, NULL, 0, RESOURCE_DELETE);
		}
		static DeferredQueueEntry TeamSkin(DeferredTeamSkinInfo *info)
		{
			return DeferredQueueEntry(info, NULL, 0, TEAM_SKIN);
		}
		static DeferredQueueEntry DeletePlayer(ARugbyCharacter* player, SIFGameWorld *game, bool remove_from_lists)
		{
			return DeferredQueueEntry(player, game, remove_from_lists ? 0 : 1, PLAYER_DELETE);
		}

		void Process();

	private:
		void			*object;
		void			*parent;
		unsigned int	arg1;
		ENTRY_TYPE		entry_type;
	};
#endif

	static void DeferredFunctionCall(FSimpleDelegate func);
	static void UpdateDeferredFunctionCallQueue();


private:
	/*#rc3_legacy
	MabVector<DeferredQueueEntry>	deferred_list;
	MabMutex						queue_access_mutex;
	*/

	static TQueue<FSimpleDelegate>	queue_deferred_function_calls;

	//////////////////////////////////////////////////////////////////////////
	// Networking
	//////////////////////////////////////////////////////////////////////////
private:
	//store the max number of players in a session whenever we create of join a session
	int32 MaxPlayersinSession;

	bool bFlushedThisFrame = false;
public:
	FOnNetworkRefresh OnNetworkRefresh;
	// Delegate is fired if a player state changes its ready up state.
	FOnLobbyBoolChanged OnPlayerStateReadyUp;
	// Delegate is fired whenever a console loads a match.
	FSimpleMulticastDelegate OnConsoleLoadedMatch;
	// Delegate is fired when a player state changes their ready state. Param is true if all players are ready or false otherwise.
	FOnLobbyBoolChanged OnAllPlayerStatesReadyChanged;
	// Delegate is fired whenever all consoles have loaded a match.
	FSimpleMulticastDelegate OnAllConsolesLoadedMatch;
	// Delegate is fired when SomeUserHasUGCRestrictions changes
	FOnUGCRestrictionChanged OnUGCRestrictionChanged;

	FOnUGCUsernameCheckComplete OnUGCUsernameCheckComplete;
#ifdef SHOW_UGC_CREATOR
	// Delegate is fired when UGC username check complete
	void HandleUGCUsernameCheck();
#if PLATFORM_PS4
	void UGCUsernameCheckComplete(const IdUsernameMap& ResultMap, bool bWasSuccessful, FString ErrorString);
#endif
	bool GetUGCUsernameCheckDone() { return bUGCUsernameCheckDone; }

	bool bUGCUsernameCheckDone = false;
#endif

	void ClearNetworkDelegates();

	// Will flush the network manager if we haven't already this frame.
	void ConditionallyFlushNetworkManager();

	FORCEINLINE int32 GetSessionMaxPlayers() const { return MaxPlayersinSession; }
	void SetSessionMaxPlayers(int32 mp) {
		MaxPlayersinSession = mp;
	};
	//////////////////////////////////////////////////////////////////////////
	// END > Networking
	//////////////////////////////////////////////////////////////////////////

	//////////////////////////////////////////////////////////////////////////
	// Team and console grouping
	//////////////////////////////////////////////////////////////////////////
public:
	// Managing logged in players, and their groupings
	void AddPlayer(ARugbyPlayerController* NewPlayer);

	//////////////////////////////////////////////////////////////////////////
	// END > Team and console grouping
	//////////////////////////////////////////////////////////////////////////

	//////////////////////////////////////////////////////////////////////////
	// User login
	//////////////////////////////////////////////////////////////////////////
private:
	// Local player login status when the system is suspended
	TArray<ELoginStatus::Type> LocalPlayerOnlineStatus;

	// Show messaging and punt to welcome screen
	void HandleSignInChangeMessaging();
	// OSS delegates to handle
	void HandleUserLoginChanged(int32 GameUserIndex, ELoginStatus::Type PreviousLoginStatus, ELoginStatus::Type LoginStatus, const FUniqueNetId& UserId);
public:
	//////////////////////////////////////////////////////////////////////////
	// END > User login
	//////////////////////////////////////////////////////////////////////////

	//////////////////////////////////////////////////////////////////////////
	// Playgo/Chunk Install
	//////////////////////////////////////////////////////////////////////////
public:
	// are we playing before we have downloaded all the chunks?
	bool IsPlayingChunkInstall();

	// Check's to see if we have installed all the chunks or not
	void UpdateChunkInstall();
private:
	// Whether this game has been fully loaded/installed
	bool bHasChunksLeft = false;
public:
	//////////////////////////////////////////////////////////////////////////
	// END > Playgo/Chunk Install
	//////////////////////////////////////////////////////////////////////////

	//////////////////////////////////////////////////////////////////////////
	// Drop in/out
	//////////////////////////////////////////////////////////////////////////
private:
	void OnLoginComplete(int32 LocalUserNum, bool bWasSuccessful, const FUniqueNetId& UserId, const FString& Error);
	FDelegateHandle OnLoginCompleteDelegateHandle;
	FOnAttemptControllerDropIn OnAttemptDropInDelegate;
	FOnControllerDropIn DropInDelegate;
	FOnControllerConnectionChange OnControllerConnectionChangeDelegate;
	int PendingControllerId;

public:




	void AttemptDropInOutController(int32 controllerID);
	bool AttemptDropInController(int32 controllerID);
	void AttemptDropOutController(int32 controllerID);
	void ConditionallyDropInPlayer(const int ControllerId, const bool bCanShowUI);
	void DropInPlayer(const int ControllerId);
	bool IsUniqueIdOnline(const FUniqueNetId& UniqueId) const;
	void HandleLoginUIClosedAndReady(TSharedPtr<const FUniqueNetId> UniqueId, const int UserIndex, const FOnlineError& Error = FOnlineError());
	int CalculateDefaultConnectionIndex(int ControllerId);
	int FindUnusedConnectionIndex(ARugbyPlayerController * joiningPlayer);
	void OnUserCanPlay(const FUniqueNetId& UserId, EUserPrivileges::Type Privilege, uint32 PrivilegeResults);
	FOnAttemptControllerDropIn* GetOnAttemptDropInDelegate() { return &OnAttemptDropInDelegate; }
	FOnControllerDropIn* GetDropInDelegate() { return &DropInDelegate; }
	FOnControllerConnectionChange* GetOnControllerConnectionChangeDelegate() { return &OnControllerConnectionChangeDelegate; }

	void SetLoginChanged(bool newlogin)
	{
		LoginChanged = newlogin;
	}

	bool GetLoginChanged()
	{
		return LoginChanged;
	}

private:
	// Actually drop out a player, should not be called externally. Call AttemptDropOutController() instead.
	void DropOutPlayer(const int ControllerId);

	//////////////////////////////////////////////////////////////////////////
	// END > Drop in/out
	//////////////////////////////////////////////////////////////////////////

public:
	//Handle exiting to title screen code
	void SetResetSandboxFlag(bool val);
	bool GetResetSandboxFlag() { return reset_sandbox_instead; };

	void SetAutoStartGame();
	bool GetAutoStartGame() { return auto_start_game; };
	void ClearAutoStartGame();


	bool reset_sandbox_instead = false;
	bool auto_start_game = false;

	void UpdateNetworkTickables(float DeltaTime);

	TSharedPtr< const FUniqueNetId > GetUniqueNetIdFromControllerId(const int ControllerId);
	void RemoveExistingLocalPlayer(ULocalPlayer* ExistingPlayer);
	void RemoveSplitScreenPlayers();
	void SignOutSplitScreenPlayers();

	//////////////////////////////////////////////////////////////////////////
	//Controller disconnection data functions
	FControllerDisconnectionData* GetControllerDisconnectionDataByLocalPlayer(ULocalPlayer* LocalPlayer);

	void SetReturingToMenu(bool GoingBackToMenu)
	{
		ReturningToMainMenu = GoingBackToMenu;
	}

	UConnectionManager*	GetConnectionManager()
	{
		return ConnectionManager;
	}

	int32 RemotePlayerCount = 0;


	int32 GetRemotePlayerCount()
	{
		return RemotePlayerCount;
	}

	void EnableRealTimeMultiPlayer()
	{
		UE_LOG(LogOnlineGame, Warning, TEXT("EnableRealTimeMultiPlayer()"));
		UpdateUsingMultiplayerFeatures(true);
	}

	void DisableRealTimeMultiPlayer()
	{
		UE_LOG(LogOnlineGame, Warning, TEXT("DisableRealTimeMultiPlayer()"));
		UpdateUsingMultiplayerFeatures(false);
	}

	bool LastMultiplayerFeatures = false;

	int ToggleControllerIdDisconnectionValue(int Value);
	TArray<FControllerDisconnectionData> GetControllerDisconnectionData();
	FControllerDisconnectionData* GetControllerDisconnectionDataByPlatformUserId(int32 PlatformUserId);
	void AddControllerDisconnectionData(FControllerDisconnectionData* InControllerDisconnectionData);
	void RemoveControllerDisconnectionData(ULocalPlayer* LocalPlayer);
	//Removes invalid controller disconnection data
	void RemoveInvalidControllerDisconnectionData();

private:
	UPROPERTY()
	UConnectionManager* ConnectionManager = nullptr;
	bool ReturningToMainMenu = false;
	/** Controller to ignore for pairing changes. -1 to skip ignore. */
	int32 IgnorePairingChangeForControllerId;
	// Callback to handle controller connection changes.
	void HandleControllerConnectionChange(bool bIsConnection, int32 Unused, int32 GameUserIndex);
	// Callback to handle controller pairing changes.
	FReply OnPairingUsePreviousProfile();
	// Callback to handle controller pairing changes.
	FReply OnPairingUseNewProfile();
	// Callback to handle controller pairing changes.
	void HandleControllerPairingChanged(int GameUserIndex, const FControllerPairingChangedUserInfo PreviousUser, const FControllerPairingChangedUserInfo NewUser);


	// Handle confirming the controller disconnected dialog.
	FReply OnControllerReconnectConfirm();

#if PLATFORM_SWITCH
	bool bDelayedControllerCheckInProgress = false;
	float switchControllerDelay = 0;
	bool controllerDisconnected = false;
#endif
	TArray<FControllerDisconnectionData> LocalPlayerControllerDisconnectionData;
	TArray<FControllerDisconnectionData> SuspendControllerData;
	TArray<FControllerDisconnectionData> ConnectedControllers;
	TArray<TPair<ULocalPlayer *, FControllerDisconnectionData>> DisconnectedPlayerData;

	private:
	FString OnlineDebugKey = "";

	public:

	FString GetLanKeyValue()
	{
		return OnlineDebugKey;
	}

	void SetLanKeyValue(FString NewKey)
	{

	}
	// Error handling
	FName errorDismissState;
	EOnlineServerConnectionStatus::Type	CurrentConnectionStatus; // Last connection status that was passed into the HandleNetworkConnectionStatusChanged hander
	void ShowErrorUIPopup(const FString& HeadingText, const FString& ErrorText, FString screenName = FString(""), bool allowCancel = true, bool savePopup = false);
	void ShowErrorUIPopupControllerDisconnectedSideSelect();
	void ShowErrorUIPopupControllerDisconnected(bool IsPrimaryPlayerDisconnected);
	bool IsDisplayingControllerDisconnected();
	void CheckForProfanity(FString _Text, bool& _Result);
	void RemoveInvalidCharacters(FString& _Text);
	void OnControllerDisconnectedInputKey(const FInputKeyEventArgs& EventArgs);
	UFUNCTION()
		bool HandleErrorModalDismiss(APlayerController* PlayerController);

	bool HandleControllerDisconnectedModalDismiss(APlayerController* PlayerController);
	void SetInOnlineArea(bool Val) { bInOnlineArea = Val; }
	bool GetInOnlineArea() { return bInOnlineArea; }

	void SetInFanhubArea(bool Val) { bInFanhubArea = Val; }
	bool GetInFanhubArea() { return bInFanhubArea; }

	void SetKickedFromOnline(bool Val) { bKickedFromOnline = Val; }
	bool GetKickedFromOnline() { return bKickedFromOnline; }

	void SetReturnToMenuReason(ERugbyDisconnectionReason Val) { m_returningToMenuReason = Val; }
	ERugbyDisconnectionReason GetReturnToMenuReason() { return m_returningToMenuReason; }

	private:
	//void NetworkError(UWorld *World, UNetDriver *NetDriver, ENetworkFailure::Type FailureType, const FString& ErrorString);
	void HandleNetworkConnectionStatusChanged(const FString&, EOnlineServerConnectionStatus::Type LastConnectionStatus, EOnlineServerConnectionStatus::Type ConnectionStatus);
	void HandleSessionFailure(const FUniqueNetId& NetId, ESessionFailure::Type FailureType);

	bool bInOnlineArea = false;
	bool bInFanhubArea = false;
	TMap<int32, Cooldown*> DropInCooldown;
	bool bKickedFromOnline = false;

	ERugbyDisconnectionReason m_returningToMenuReason;

#if WITH_EDITOR && !UE_BUILD_SHIPPING
public:
	UFUNCTION(Exec)
	void DebugForceLineOut();
	UFUNCTION(Exec)
	void DebugForceConversion();
	UFUNCTION(Exec)
	void DebugForceKickOff();
	UFUNCTION(Exec)
	void DebugForceDropOut();
	UFUNCTION(Exec)
	void DebugForceScrum();
	UFUNCTION(Exec)
	void DebugForceFreeKick();
	UFUNCTION(Exec)
	void DebugForcePenaltyKickTouch();
	UFUNCTION(Exec)
	void DebugForcePenaltyKickGoal();
	UFUNCTION(Exec)
	void DebugForcePenaltyTapAndGo();
	UFUNCTION(Exec)
	void DebugForcePenaltyAwarded();
	UFUNCTION(Exec)
	void DebugClearRolesAndActions();
#endif //WITH_EDITOR && !UE_BUILD_SHIPPING

#if WITH_EDITOR && !UE_BUILD_SHIPPING
public:
	UFUNCTION(Exec)
		void DebugStartMemLeakDect();
	UFUNCTION(Exec)
		void DebugStopMemLeakDect();
	UFUNCTION(Exec)
		void DebugWriteMemLeakReport();
#endif //WITH_EDITOR && !UE_BUILD_SHIPPING

public:
	void BootClientWindows();


public:
	TArray<FRugbyCharacterMorphData> m_MorphDataList;

	int m_CurrentMorphCount = 0;

	void UpdateMorphDataList();

	uint32 OnlinePrivilegeScreenUniqueId = -1;

public:

	TArray<FSkeletalMeshSetData> m_SkeletalMeshSetDataList;
	TArray< TWeakObjectPtr<USkeletalMesh> > m_SkeletalMeshSetDataListSk;
	TArray< TWeakObjectPtr<USkeletalMeshComponent> > m_SkeletalMeshSetDataListComp;
	TArray< TWeakObjectPtr<AActor> > m_SkeletalMeshSetDataListActor;

	TMap<FString, int> m_AssetCounter;

	void AddMeshList(FSkeletalMeshSetData a_Data);

	UFUNCTION(Exec)
	void PrintMeshList();
	bool AbortLoadForInvite = false;


#ifdef ENABLE_ANALYTICS
	// Analytics
public:
	void RegisterOptionsAnalyticsSnapshot();
	void RegisterFanHubAnalyticsData();
	void RegisterLeaderBoardAnalyticsData();
	void RegisterOnlineSearchAnalyticsData(bool bSearchSuccess, bool bJoinSuccess, bool bHostingSuccess, bool bCancelled);

	void RegisterFreeRoamAnalyticsData();
	void RegisterPostMatchAnalyticsData();

	void AddSetPlayDetails(FString namedChecked, TSharedPtr<FJsonObject> obj);

	void RegisterChampionDataDowloadData();

	void RegisterGenericAnalyticsSnapshot(TSharedPtr<FJsonObject> obj, uint32 EventID, bool bDoAnalyticsDataReset = true);

	bool StartSendingAnalyticsData();
	void ReadFromAnalyticsData();
	void WriteToAnalyticsData();
	wwPlayerAnalyticsData& GetPlayerAnalyticsData() { return PlayerAnalyticsData; }

private:
	wwPlayerAnalyticsData PlayerAnalyticsData;
#endif

#if !WW_BUILD_SUBMISSION
	void DebugCheckMemoryUsage();

	UUserWidget* pDebugMemoryWidget = nullptr;
#endif

public:
	// Champion Data
	/// This will show the champion data dialogue when opening the menu for the first time. If bForce is true, dialogue will show even if it is the second time.
	void AttemptShowChampionDataDownloadDialogue(bool bForce, int32 NewVersion, FSimpleDelegate ChampionDataDownloadSuccessfulDelegate = FSimpleDelegate());


public:
	bool GetIsVirtualKeyboardShowing()
	{
		return IsVirtualKeyboardShowingThisFrame;
	}

	TMap<uint32, USkeletalMesh*>& GetMorphMergeCache() { return MorphMergeCache; }
	TMap<uint32, USkeletalMesh*>& GetMeshMergeCache() { return MorphMergeCache; }
private:
	bool IsVirtualKeyboardShowingThisFrame = false;
	bool IsApplicationDeactivated = false;
	bool isSuspended = false;

	FTimerHandle suspendHandle;
	FTimerHandle resumeHandle;
	FTimerHandle controllerDisconnectHandle;
	FTimerHandle loginChangeHandle;

	UPROPERTY()
	TMap<uint32, USkeletalMesh*> MorphMergeCache;

	UPROPERTY()
	TMap<uint32, USkeletalMesh*> MeshMergeCache;

public:
	bool SkipSwitchConnectionFailedWaitingForNEXResult = false;

private:
	bool inDelayedControllerDisconnectNotificationLoop = false;
	bool controllerReconnectedSkipDisconnectLoop = false;
};

typedef URugbyGameInstance SIFApplication;
