// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIPopulatorCareerRecruit.h"
#include "WWUIListField.h"
#include "Image.h"
#include "Border.h"


/*--------------------------------------------------------------
|        Copyright (C) 1997-2012 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/


//#include "Precompiled.h"
//
//#include "MabUINode.h"
//#include "MabStreamerXML2.h"
//#include "MabStreamMemoryResource.h"
//#include "MabUITextLine.h"
//#include "MabUIInheritDimensionsParameters.h"
//#include "MabTranslationManager.h"
//
//#include "SIFUIConstants.h"
//#include "SIFUIHelpers.h"
//#include "SIFDebug.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "Rugby/RugbyGameInstance.h"
//#include "RUPlayerPositionEnum.h"
//#include "RUUIPartialListBox.h"
//#include "RUDBTeam.h"
//#include "RUDBPlayer.h"
//#include "RUUIDatabaseLuaQueryManager.h"
//#include "RUGameDatabaseManager.h"
//#include "RUPlayerAttributesHelper.h"
//#include "RUCareerModeDebugSettings.h"
//#include "RL3CompetitionTeamHelper.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RL3Conversion/RULicensedPlayerIdHandler.h"
#include "Rugby/Match/HUD/RUHUDUpdater.h"
#include "Rugby/Utility/Helpers/SIFUIHelpers.h"
#include "Rugby/Utility/Helpers/SIFGameHelpers.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Database.h"

#include "WWUIScrollBox.h"
#include "CoreMinimal.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3CompetitionTeamHelper.h"

#include "RUUIDatabaseQueryManager.h"

const char RUCRP_COLUMN_POS_STR[] = "Column0/Pos";
const char RUCRP_COLUMN_SECOND_STR[] = "Column1/Second";
const char RUCRP_COLUMN_NAME_STR[] = "Column2/Name";
const char RUCRP_COLUMN_AGE_STR[] = "Column3/Age";
const char RUCRP_COLUMN_RATING_STR[] = "Column4/Rating";
const char RUCRP_COLUMN_VALUE_STR[] = "Column5/Value";
const char RUCRP_COLUMN_CLUB_STR[] = "Column6/Club";

const char RECRUIT_POS_STR[] = "TextPosition";
const char RECRUIT_SECOND_STR[] = "TextSecondPosition";
const char RECRUIT_NAME_STR[] = "TextName";
const char RECRUIT_AGE_STR[] = "TextAge";
const char RECRUIT_RATING_STR[] = "TextRating";
const char RECRUIT_VALUE_STR[] = "TextValue";
const char RECRUIT_CLUB_STR[] = "TextClub";
const char RECRUIT_TEXT_CONTAINER_STR[] = "BorderTextContainer";



typedef enum
{
	RUCRP_COLUMN_POS,
	RUCRP_COLUMN_SECOND,
	RUCRP_COLUMN_NAME,
	RUCRP_COLUMN_AGE,
	RUCRP_COLUMN_RATING,
	RUCRP_COLUMN_VALUE,
	RUCRP_COLUMN_CLUB,

	RUCRP_COLUMN_NUM
} RUCRP_COLUMNS;

const int RUCRP_COLUMN_HIGHLIGHT_NUM = (int)RUCRP_COLUMN_NUM;



//--------------------------------------------------------------------------------------

struct PlayerListSortPredicate
{
	MABNONASSIGNABLE(PlayerListSortPredicate)
public:
	PlayerListSortPredicate(RUCRP_COLUMNS _sort_column, bool _asc_order)
		: sort_column(_sort_column)
		, asc_order(_asc_order)
	{}

	/// Convert string with special characters back in to straight ascii...
	inline void LatinConvert(unsigned char *name1) const
	{
		static char unicode195_lookup[] = {		// 195,128+index
			'a','a','a','a','a','a','?','c',	// 128-135		// uppercase
			'e','e','e','e','i','i','i','i',	// 136-143
			'?','n','o','o','o','o','o','?',	// 144-151
			'o','u','u','u','u','y','?','s',	// 152-159
			'a','a','a','a','a','a','?','c',	// 160-167		// lowercase
			'e','e','e','e','i','i','i','i',	// 168-175
			'?','n','o','o','o','o','o','?',	// 176-183
			'o','u','u','u','u','y',' ','s'		// 184-191
		};

		// http://www.utf8-chartable.de/unicode-utf8-table.pl?utf8=dec

		int idx = 0;
		unsigned char c;
		while ((c = name1[idx]) != 0)
		{
			if (c == 195 || c == 226 || c == 194)
			{
				int j = idx + 1;
				while (name1[j] != 0)
				{
					name1[j - 1] = name1[j];
					j++;
				}
				name1[j - 1] = 0;

				if (c == 195)
				{
					c = name1[idx];
					if (c >= 128 && c < 192)
					{
						c = (unsigned char)unicode195_lookup[c - 128];
					}
					else
						c = '?';

					name1[idx] = c;
				}
				else if (c == 194)
				{
					c = '?';
				}
			}
			idx++;
		}
	}

	bool operator()(const UWWUIPopulatorCareerRecruit::PlayerInfo* player1, const UWWUIPopulatorCareerRecruit::PlayerInfo* player2) const
	{
		bool result = player1->overall_rating > player2->overall_rating;
		if (player1->overall_rating == player2->overall_rating)
			result = player1->db_id > player2->db_id;

		switch (sort_column)
		{
		case RUCRP_COLUMN_POS:
			if (player1->primary_position != player2->primary_position)
				result = player1->primary_position > player2->primary_position;
			else
				return result;	// If both values are same, sort based on overall rating
			break;
		case RUCRP_COLUMN_SECOND:
			if (player1->secondary_position != player2->secondary_position)
				result = player1->secondary_position > player2->secondary_position;
			else
				return result;	// If both values are same, sort based on overall rating
			break;
		case RUCRP_COLUMN_NAME:
		{
			const int NAME_SIZE = 20;
			unsigned char name1[NAME_SIZE];
			unsigned char name2[NAME_SIZE];

			strncpy((char*)name1, player1->last_name.c_str(), NAME_SIZE - 1);
			name1[NAME_SIZE - 1] = 0;
			strncpy((char*)name2, player2->last_name.c_str(), NAME_SIZE - 1);
			name2[NAME_SIZE - 1] = 0;

			LatinConvert(name1);
			LatinConvert(name2);

			int sres = strcmp((char*)name1, (char*)name2);
			if (sres != 0)
				result = sres > 0;
			else
			{
				sres = strcmp(player1->first_name.c_str(), player2->first_name.c_str());
				if (sres != 0)
					result = sres > 0;
				else
					return result;
			}
		}
		break;
		case RUCRP_COLUMN_AGE:
			if (player1->age != player2->age)
				result = player1->age < player2->age;
			else
				return result;	// If both values are same, sort based on overall rating
			break;
		case RUCRP_COLUMN_RATING:
			if (player1->overall_rating != player2->overall_rating)
				result = player1->overall_rating < player2->overall_rating;
			break;
		case RUCRP_COLUMN_VALUE:
			if (player1->market_value != player2->market_value)
				result = player1->market_value < player2->market_value;
			else
				return result;	// If both values are same, sort based on overall rating
			break;
		case RUCRP_COLUMN_CLUB:
		{
			int sres = strcmp(player1->club_name.c_str(), player2->club_name.c_str());
			if (sres != 0)
				result = sres > 0;
			else
				return result;
		}
		break;

		default:
			MABBREAKMSG("Unhandle column");
			break;
		}

		// Flip result for ascending or descending
		if (!asc_order)
			result = !result;

		return result;
	}

	RUCRP_COLUMNS sort_column;
	bool asc_order;
};


//--------------------------------------------------------------------------------------

UWWUIPopulatorCareerRecruit::UWWUIPopulatorCareerRecruit() :
	player_arrays(nullptr)			   ,
	list_node(nullptr)				   ,
	prev_starting_index(-1)		   ,
	prev_selected_index(-1)		   ,
	starting_index(0)			   ,
	selected_index(0)			   ,
	player_filter(PR_FILTER_ALL)   ,
	compare_player_db_id(0)		   ,
	selected_column(-1)			   ,
	num_draftable_players(0)	   ,
	pScrollBoxWidget(nullptr)		,
	FocusOnRepopulate(true)
{
}

UWWUIPopulatorCareerRecruit::~UWWUIPopulatorCareerRecruit()
{
	if (player_arrays)
	{
		MabMemDeleteSafe(player_arrays);
	}
	UWWUIFunctionLibrary::StopTimer(TimerHandleDelayScrollboxOffset);
}

void UWWUIPopulatorCareerRecruit::Clear(UWidget* node)
{
	MABUNUSED(node);
	// NB: Don't do any listbox resetting here!! This will cause a selection changed event which will then fire off a populate straight after this!

	if (player_arrays)
	{
		MabMemDelete(player_arrays);
		player_arrays = NULL;
	}
}

void UWWUIPopulatorCareerRecruit::Populate(UWidget* widget)
{
	pScrollBoxWidget = widget;

	list_node = Cast<UScrollBox>(widget);
	if (!list_node)
	{
		ensure(list_node);
		return;
	}

	int32 num_items = 0;
	if (player_arrays)
	{
		InData.full_list_length = player_arrays->filtered_players.size();

		if (InData.full_list_length <= PARTIAL_POPULATE_THRESHOLD)
		{
			// list is small enough to just populate all
			num_items = InData.full_list_length;
		}
		else
		{
			num_items = (FMath::Min<int32>(InData.full_list_length, PARTIAL_POPULATE_TOTAL_ELEMENTS));
		}
	}

	if ((size_t)num_items != list_node->GetChildrenCount() || !player_arrays )
	{
		if (inPreConstruct)
		{
			return;
		}

		if (!player_arrays)
		{
			player_arrays = MabMemNew(/*SIFHEAP_PSSG*/ 0) PlayerInfoArrays();
		}

		//Can't use clear because it will delete our player_arrays
		//Clear(widget);
		PurgeChildren(widget);

		CareerRecruitCreationNodeCallback callbackObject(widget/*, dataList.ArrayOption*/);
		if (InData.starting_index < 0) InData.starting_index = 0;
		CreateNodesFromTemplate(dataList.TemplateName, num_items, &callbackObject, InData.starting_index);

		if (ScreenRef)
		{
#ifdef UI_USING_UMG
			ScreenRef->StoreChildWidgets();
#else
			if (ScreenRef && ScreenRef->GetStateScreen())
			{
				ScreenRef->GetStateScreen()->StoreChildWidgets();
			}
#endif
		}
	}

	// Reset index
	InData.starting_index = 0;
	InData.selected_index = 0;
	selected_index = 0;
	SelectFirstElement = true;
	prev_starting_index = -1;
	prev_selected_index = -1;
}

void UWWUIPopulatorCareerRecruit::PopulateAndRefresh(UWidget* widget)
{
	SelectFirstElement = false;
	select_index_at_end_of_populate_and_refresh = -1;

	list_node = Cast<UScrollBox>(widget);
	if (!list_node)
	{
		ensure(list_node);
		return;
	}


	if (!player_arrays)
	{
		Populate(widget);
	}

	// Get team id we're recruiting for.
	int db_id = InData.team_id;

	// Are we in team customisation?
	//InData.is_customisation;

	// Get unassigned players

	if (list_node->GetChildrenCount() == 0 || player_arrays->unassigned_players.size() == 0)
	{
		GenerateUnassignedPlayerList();
	}

	// Check if filter has changed
	PLAYER_RECRUIT_FILTER old_player_filter = player_filter;
	int player_filter_int = InData.current_filter_idx;
	player_filter = (PLAYER_RECRUIT_FILTER)player_filter_int;

	// Repopulate if filter changed
	if (list_node->GetChildrenCount() == 0 || old_player_filter != player_filter || (InData.SearchName != SearchName))
	{
		// Filter players
		FilterPlayerList();

		// Populate
		Populate(widget);

		// Set properties on the node for lua use
		{
			//node->SetProperty(RUCRP_NUM_PLAYERS_LISTED, MabString(0, "%d", player_arrays->filtered_players.size()));
			InData.num_players_listed = player_arrays->filtered_players.size();
			//node->SetProperty(RUCRP_NUM_PLAYERS_DRAFTABLE, MabString(0, "%d", num_draftable_players));
			InData.num_players_draftable = num_draftable_players;
			//MabNamedValueList system_event_params;
			//system_event_params.SetValue<const char*>(SIFUI_LUA_SYSTEM_EVENT_PARAM, RUCRP_FILTER_UPDATE);
			//SIFApplication::GetApplication()->GetWindowSystem()->OnSystemEvent(system_event_params);
		}
	}

	Refresh(widget);

	if (FocusOnRepopulate)
	{
		if (SelectFirstElement)
		{
			FocusListField(0);
		}
		else if (select_index_at_end_of_populate_and_refresh != -1)
		{
			FocusListField(select_index_at_end_of_populate_and_refresh);
		}
	}
	else
	{
		FocusOnRepopulate = true;
	}
	// Set common properties for the system event
	//MabNamedValueList system_event_params;
	//system_event_params.SetValue<const char*>(SIFUI_LUA_SYSTEM_EVENT_PARAM, RUCRP_LIST_UPDATE);
	//SIFApplication::GetApplication()->GetWindowSystem()->OnSystemEvent(system_event_params);

}

void UWWUIPopulatorCareerRecruit::Refresh(UWidget* widget)
{
	starting_index = InData.starting_index;

	MABASSERT(starting_index >= 0);

	selected_index = InData.selected_index;

	ensure(selected_index >= 0);
	ensure(selected_index <= list_node->GetChildrenCount());
	
	// Get properties from ui node
	int old_compare_player_db_id = compare_player_db_id;
	//MabStringHelper::ToInt(node->GetProperty(RUCRP_COMPARE_PLAYER_ID)->get<MabString>(), compare_player_db_id);
	compare_player_db_id = InData.compare_player_id;

	int old_selected_column = selected_column;
	//MabStringHelper::ToInt(node->GetProperty(RUCRP_SELECTED_COLUMN)->get<MabString>(), selected_column);
	selected_column = InData.selected_column;

	//MabStringHelper::ToInt(node->GetProperty(RUCRP_FORCE_SELECT_PLAYER_ID)->get<MabString>(), select_player_db_id);
	int select_player_db_id = InData.force_select_player_id;

	//MabStringHelper::ToInt(node->GetProperty(RUCRP_SORT_COLUMN)->get<MabString>(), sorted_column);
	int sorted_column = InData.sort_column;
	
	//MabStringHelper::ToInt(node->GetProperty(RUCRP_FORCE_ALTERNATE_PLAYER_ID)->get<MabString>(), alternate_player_db_id);
	int alternate_player_db_id = InData.force_alternate_player_id;

	// Check if we have to sort
	if (sorted_column != -1)
	{
		// Sort the ordered_players according to the column,
		SortPlayerList(sorted_column);
		//node->SetProperty(RUCRP_SORT_COLUMN, MabString("-1"));
		InData.sort_column = -1;

		// Force refresh
		prev_starting_index = -1;
	}

	// Check if we are forced to select some player
	if (select_player_db_id != SQLITEMAB_INVALID_ID)
	{
		// Reset the starting and selected index, so that the selected player is highlighted
		if (!SelectPlayer(select_player_db_id) && alternate_player_db_id != SQLITEMAB_INVALID_ID)
		{
			// The player no longer is in list use the supplied alternate.
			SelectPlayer(alternate_player_db_id);
		}

		//node->SetProperty(RUCRP_FORCE_SELECT_PLAYER_ID, MabString(0, "%d", SQLITEMAB_INVALID_ID));
		InData.force_select_player_id = 0;

		// Force refresh
		prev_starting_index = -1;
	}

	// update the scroll bar
	int32 NumOnScreenFields = FMath::Min<int32>(list_node->GetChildrenCount(), VISIBLE_LIST_ELEMENTS);
	if (InData.pScrollbar)
	{
		InData.pScrollbar->SetData(NumOnScreenFields, InData.full_list_length, InData.starting_index, FIELD_HEIGHT);
		InData.pScrollbar->ManualUpdateScrollbar(list_node->GetScrollOffset());
	}
	

	// Refresh row info if necessary
	if (prev_starting_index == starting_index)
	{
		// If list dint change & selected rows dint change, then update column highlight or highlight compared player

		if (prev_selected_index == selected_index)
		{
			// Update the column highlight
			//UpdateColumnHighlight(old_selected_column);

			// Update the compare player
			UpdateComparePlayerHighlight(old_compare_player_db_id);
			return;
		}

		UpdateSelectedPlayerHighlight();
	}
	else
	{
		// Refresh the displayed list

		//list_node->SetNumSelectableItems((int)player_arrays->filtered_players.size());

		// Need to make sure that there's no way we can exceeds the bounds of the array.
		if (starting_index + PARTIAL_POPULATE_TOTAL_ELEMENTS > player_arrays->filtered_players.size())
		{
			// This should be a very rare case.
			starting_index = (int)(player_arrays->filtered_players.size() - PARTIAL_POPULATE_TOTAL_ELEMENTS);
			starting_index = FMath::Max(0, starting_index);
			InData.starting_index = starting_index;
		}


		// Populate children
		for (size_t i = 0; i < list_node->GetChildrenCount(); ++i)
		{
			const PlayerInfo* db_player = player_arrays->filtered_players[starting_index + i];

			// Set info for lua
			UWidget* pField = list_node->GetChildAt(i);
			if (!pField)
			{
				ensure(pField);
				return;
			}
			UWWUIListField* child = Cast<UWWUIListField>(pField);
			if (!child)
			{
				ensure(child);
				return;
			}
			//child->SetProperty(RUCRP_PLAYER_ID, MabString(0, "%d", db_player->db_id));
			int32 db_id = db_player->db_id;
			child->SetProperty(RUCRP_PLAYER_ID, &db_id, PROPERTY_TYPE_INT);

			// set child name and idx
			child->SetIdx(i);
			FString childName = TableId + "_" + FString::FromInt(i);
			child->RenameWidget(childName);

			PopulatePlayerInfo(db_player, pField, (int)i == selected_index);
		}
	}

	prev_starting_index = starting_index;
	prev_selected_index = selected_index;
}

void UWWUIPopulatorCareerRecruit::SetFocusOnRepopulate(bool InFocus) 
{
	FocusOnRepopulate = InFocus;
};

UWWUIPopulatorCareerRecruit::CareerRecruitCreationNodeCallback::CareerRecruitCreationNodeCallback(UWidget* container_to_populate)
	: item_index(0)
{
	container = Cast<UPanelWidget>(container_to_populate);
}

void UWWUIPopulatorCareerRecruit::CareerRecruitCreationNodeCallback::Callback(UUserWidget* widget)
{
	//MabString string_buffer(0, "CareerPlayerRecruit_%d", item_index++);
	//widget->SetName(string_buffer);
	container->AddChild(widget);
	item_index++;
}



void UWWUIPopulatorCareerRecruit::GenerateUnassignedPlayerList()
{
	// Get helper
	RUDBHelperInterface *db_helper = SIFApplication::GetApplication()->GetGameDBHelper();
	RUCareerModeManager *manager = SIFApplication::GetApplication()->GetCareerModeManager();

	RL3Database *database = manager->GetRL3Database();
	if (!database)
	{
		ensure(database);
		return;
	}

	MabVector<unsigned short> unassigned_player_ids;

	bool valid_team = false;
	bool is_international = false;
	bool r7_exclusive = false;
	unsigned char gender_permissions_mask = ~0;

	RL3DB_TEAM db_team(InData.team_id);
	if (InData.team_id != 0)
	{
		valid_team = true;
		r7_exclusive = db_team.GetIsR7Exclusive();
		is_international = db_team.IsInternational();
		gender_permissions_mask = db_team.GetGenderPermissionFlags();
	}

	if (InData.is_customisation)
	{
		MabVector< unsigned short > player_lineup;
		if (InData.is_link)
		{
			if (InData.link_player_id != DB_INVALID_ID)
			{
				RL3DB_PLAYER link_player(InData.link_player_id);
				gender_permissions_mask = PLAYER_GENDER_AS_FLAG(link_player.GetGender());
				r7_exclusive = link_player.GetPositionCategory(0) == PP_NONE;
				r7_exclusive |= link_player.GetGender() == PLAYER_GENDER_FEMALE;
			}
		}
		else
		{
			RUDB_TEAM *custom_team = SIFUIHelpers::GetQueryManager()->GetTeamData();
			r7_exclusive = custom_team->GetIsR7Exclusive();
			gender_permissions_mask = custom_team->GetGenderPermissionFlags();
			custom_team->GetAllPlayersInDisplayOrder(player_lineup, true);
		}

		int lineup_size = (int)player_lineup.size();

		unsigned char player_gender_flag = PLAYER_GENDER_FLAG_MALE;

		int num_players = database->GetTotalNumPlayers();
		for (int pidx = 0; pidx < num_players; pidx++)
		{
			RL3DB_PLAYER player = database->GetPlayerByIndex(pidx);

			unsigned short player_db_id = player.GetDbId();

			/// Don't consider referees
			if (player.GetPositionCategory(0) == PP_NONE && player.GetPositionCategory(3) == PP_NONE)
				continue;

			/// Don't consider players who don't have a position in the relevant game mode
			if (valid_team)
			{
				if ((!r7_exclusive && player.GetPositionCategory(0) == PP_NONE)
					|| (r7_exclusive && player.GetPositionCategory(3) == PP_NONE))
					continue;
			}

			// Don't add if restricted, or a referee or already in customised team...
			if (RUCareerModeManager::IsPlayerExcluded(player_db_id))
				continue;

			player_gender_flag = PLAYER_GENDER_AS_FLAG(player.GetGender());
			if ((player_gender_flag & gender_permissions_mask) != player_gender_flag)
				continue;

			bool already_in_team = false;
			for (int i = 0; i < lineup_size; i++)
			{
				if (player_lineup[i] == player_db_id)
				{
					already_in_team = true;
					break;
				}
			}

			if (!already_in_team || InData.is_link)
			{
				if ((unsigned short)InData.link_player_id != player_db_id)
				{
					unassigned_player_ids.push_back(player_db_id);
				}
			}
		}
	}
	else
	{
		if (!is_international)
		{	// Club team...
			manager->GetHireablePlayerIDs(unassigned_player_ids, db_team);
		}
		else
		{// International team

			// Set the current international competition...
			international_competition = SIFApplication::GetApplication()->GetCareerModeManager()->GetRepSelectionCompetition();

			manager->GetCompetitionTeamHelper()->GeneratePlayerListForRepTeam(db_team, international_competition, unassigned_player_ids, true);
		}
	}

	// Get all players in display order
//	MabVector< int > unassigned_player_ids;
//	manager->GetUnassignedPlayerIDs(unassigned_player_ids, false, true);

	num_draftable_players = (int)unassigned_player_ids.size();

	// Cache player info
	if (unassigned_player_ids.size() != player_arrays->unassigned_players.size())
	{
		player_arrays->unassigned_players.clear();
		for (MabVector< unsigned short >::iterator id_it = unassigned_player_ids.begin(); id_it != unassigned_player_ids.end(); ++id_it)
		{

			PlayerInfo pinfo;
			db_helper->LoadPlayerDBData(*id_it)->PopulatePlayerInfo(&pinfo, r7_exclusive);

			RL3DB_PLAYER plr(*id_it);

			if (!is_international)
				pinfo.market_value = manager->CalculatePlayersMarketValueByID(*id_it);
			else
				pinfo.market_value = plr.GetNumCaps();

			pinfo.is_rookie = plr.GetIsRookie();
			pinfo.is_custom = plr.GetIsCustom();

			player_arrays->unassigned_players.push_back(pinfo);
		}
	}
	else
	{
		MabVector<PlayerInfo>::iterator player_it = player_arrays->unassigned_players.begin();
		for (MabVector< unsigned short >::iterator id_it = unassigned_player_ids.begin();
			id_it != unassigned_player_ids.end() && player_it != player_arrays->unassigned_players.end();
			++id_it, ++player_it)
		{
			//if(*id_it != player_it->db_id)
			{
				db_helper->LoadPlayerDBData(*id_it)->PopulatePlayerInfo(&(*player_it), r7_exclusive);
				RL3DB_PLAYER plr(*id_it);

				if (!is_international)
					player_it->market_value = manager->CalculatePlayersMarketValueByID(player_it->db_id);
				else
					player_it->market_value = plr.GetNumCaps();
			}
		}
	}
}

// Filter player list
void UWWUIPopulatorCareerRecruit::FilterPlayerList()
{
	PLAYER_POSITION position_filter = SIFApplication::GetApplication()->GetCareerModeManager()->GetFilterPosition(player_filter);

	// Filter the unassigned_players
	player_arrays->filtered_players.clear();
	for (MabVector<PlayerInfo>::iterator player_it = player_arrays->unassigned_players.begin(); player_it != player_arrays->unassigned_players.end(); ++player_it)
	{
		if (player_filter == PR_FILTER_ALL
				|| (player_filter == PR_FILTER_ROOKIES && player_it->is_rookie)
				|| (player_filter == PR_FILTER_CUSTOM && player_it->is_custom)
				|| (position_filter != PP_NONE && (position_filter & player_it->primary_position) != 0))
		{
			player_arrays->filtered_players.push_back(&(*player_it));
		}
	}

	MabVector<PlayerInfo*> search_players;

	// ToUpper() needs to be called on FText to work with international characters.
	InData.SearchName = FText::FromString(InData.SearchName).ToUpper().ToString();

	MabString search_name_mab = SIFGameHelpers::GAConvertFStringToMabString(InData.SearchName);

	if (!InData.SearchName.IsEmpty())
	{
		for (int playeridx = 0; playeridx < player_arrays->filtered_players.size(); playeridx++)
		{
			RL3DB_PLAYER player(player_arrays->filtered_players[playeridx]->db_id);

			MabString playerName = player.GetName();

			if (strstr(MabStringHelper::ToUpper(player.GetName()).c_str(), MabStringHelper::ToUpper(search_name_mab).c_str()) != NULL)
			{
				search_players.push_back(player_arrays->filtered_players[playeridx]);
			}
		}

		player_arrays->filtered_players = search_players;
	}

	SearchName = InData.SearchName;
}

// Sort player list based on sorted_column
void UWWUIPopulatorCareerRecruit::SortPlayerList(int column_num)
{
	// Convert column index to column enum
	RUCRP_COLUMNS column = RUCRP_COLUMN_NAME;
	{
		// Get 1st row node
//		MabUINode* row_node = list_node->GetChildByIndex(0);
//		MabUINode* row_node_selected = row_node->GetChildByIndex(0);

		// Get column node name
		MabString column_name(0, "%s%d", RUCRP_COLUMN_TAG, column_num);

		if (MabStringHelper::StartsWith(RUCRP_COLUMN_POS_STR, column_name))
			column = RUCRP_COLUMN_POS;
		else if (MabStringHelper::StartsWith(RUCRP_COLUMN_SECOND_STR, column_name))
			column = RUCRP_COLUMN_SECOND;
		else if (MabStringHelper::StartsWith(RUCRP_COLUMN_NAME_STR, column_name))
			column = RUCRP_COLUMN_NAME;
		else if (MabStringHelper::StartsWith(RUCRP_COLUMN_AGE_STR, column_name))
			column = RUCRP_COLUMN_AGE;
		else if (MabStringHelper::StartsWith(RUCRP_COLUMN_RATING_STR, column_name))
			column = RUCRP_COLUMN_RATING;
		else if (MabStringHelper::StartsWith(RUCRP_COLUMN_VALUE_STR, column_name))
			column = RUCRP_COLUMN_VALUE;
		else if (MabStringHelper::StartsWith(RUCRP_COLUMN_CLUB_STR, column_name))
			column = RUCRP_COLUMN_CLUB;
		else
			MABBREAKMSG("Unhandled column name");
	}

	TArray<PlayerInfo*> FilteredPlayers;

	for (int32 i = 0; i < player_arrays->filtered_players.size(); i++)
	{
		FilteredPlayers.Add(player_arrays->filtered_players[i]);
	}

	// Sort the list
	int asc_order = InData.sort_ascending;
	//bStringHelper::ToInt(list_node->GetProperty(RUCRP_SORT_ASCENDING)->get<MabString>(), asc_order);
	Algo::Sort(FilteredPlayers, PlayerListSortPredicate(column, asc_order == 1));

	player_arrays->filtered_players.clear();

	for (auto& CurrentFileteredPlayer : FilteredPlayers)
	{
		player_arrays->filtered_players.push_back(CurrentFileteredPlayer);
	}
}


// Populate each line with player info
void UWWUIPopulatorCareerRecruit::PopulatePlayerInfo(const UWWUIPopulatorCareerRecruit::PlayerInfo*	player_info,
	UWidget*			row_node,
	bool				selected)
{
	MABASSERT(player_info && row_node);

	// Get child node that should be populated
	//MabUINode* node = NULL;
	//
	//MabUINode* selected_node = row_node->GetChildByContext(RUCRP_SELECTED_TEXT);
	//MabUINode* unselected_node = row_node->GetChildByContext(RUCRP_UNSELECTED_TEXT);
	//MabUINode* selected_marked_node = row_node->GetChildByContext(RUCRP_SELECTED_MARKED_TEXT);
	//MabUINode* unselected_marked_node = row_node->GetChildByContext(RUCRP_UNSELECTED_MARKED_TEXT);
	//selected_node->SetVisible(false);
	//unselected_node->SetVisible(false);
	//selected_marked_node->SetVisible(false);
	//unselected_marked_node->SetVisible(false);

	//// If there is no player at this level or if player's contract has expired, then mark the row
	bool marked = (unsigned short)compare_player_db_id == player_info->db_id;

	//// Chose which child node to populate
	//if (selected)
	//{
	//	node = marked ? selected_marked_node : selected_node;
	//}
	//else
	//{
	//	node = marked ? unselected_marked_node : unselected_node;
	//}
	//node->SetVisible(true);

	FLinearColor TextColor = FLinearColor::White;
	if (marked)
	{
		TextColor = FLinearColor::Red;
	}

	/// Mark players who can't be choosen for international duty.

	RL3DB_TEAM db_team(InData.team_id);
	bool elligible = true;
	if (!InData.is_customisation && db_team.IsInternational())
	{
		RL3CompetitionTeamHelper *team_helper = SIFApplication::GetApplication()->GetCareerModeManager()->GetCompetitionTeamHelper();
		RL3DB_PLAYER player((unsigned short)player_info->db_id);

		// Check if players club team can afford to loose any more players...
		if (player.GetClubTeamId() != DB_INVALID_ID)
		{
			RL3DB_TEAM club_team(player.GetClubTeamId());
			elligible = team_helper->CanAffordToLosePlayerToInternational(club_team, player, international_competition);
		}
		if (elligible && player.GetAlternateClubTeamId() != DB_INVALID_ID)
		{
			RL3DB_TEAM club_team(player.GetAlternateClubTeamId());
			elligible = team_helper->CanAffordToLosePlayerToInternational(club_team, player, international_competition);
		}
	}

	// Show/Hide restricted icon (licensing).
	UImage* pRestrictedIcon = Cast<UImage>(ScreenRef->FindChildOfTemplateWidget(row_node, RECRUIT_RESTRICTED_ICON_STR));
	if (pRestrictedIcon)
	{
		if (RULicensedPlayerIdHandler::GetIsPlayersTransferLimitedByLicense((unsigned short)player_info->db_id, InData.team_id))
		{
			//row_node->GetChildByContext("RestrictedIcon")->SetVisible(true);
			pRestrictedIcon->SetVisibility(ESlateVisibility::Visible);
		}
		else
		{
			//row_node->GetChildByContext("RestrictedIcon")->SetVisible(false);
			pRestrictedIcon->SetVisibility(ESlateVisibility::Hidden);
		}
	}
	else
	{
		ensure(pRestrictedIcon);
	}
	

	//unselected_node->SetColour(elligible ? MabColour::White : MabColour::Grey);		// Set node colour for unselected node.
	///row_node->SetProperty( RUCRP_SELECTABLE, MabString(0, "%d", elligible?1:0) );		// Tell lua via properties.
	if (!elligible)
	{
		TextColor = FLinearColor(0.5f, 0.5f, 0.5f);	// Grey
	}

	UBorder* pTextContainer = Cast<UBorder>(ScreenRef->FindChildOfTemplateWidget(row_node, RECRUIT_TEXT_CONTAINER_STR));
	if (pTextContainer)
	{
		pTextContainer->SetContentColorAndOpacity(TextColor);
	}
	else
	{
		ensure(pTextContainer);
	}

	bool is_international = InData.team_id != 0 ? db_team.IsInternational() : false;

	// DO NOT NEED
	// Highlight column
	//MabUINode* column_highlight = NULL;
	//for (size_t i = 0; i < RUCRP_COLUMN_NUM; ++i)
	//{
	//	MabString column_highlight_node_name(0, "%s%d/%s", RUCRP_COLUMN_TAG, i, RUCRP_COLUMN_BG);
	//	column_highlight = node->GetChildByContext(column_highlight_node_name);
	//	column_highlight->SetVisible(i == (size_t)selected_column ? true : false);
	//}


	// Populate info

	//MabUINode* child_node = NULL;


	UTextBlock* pTextBlock;
	pTextBlock = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(row_node, FString(RECRUIT_POS_STR)));
	if (pTextBlock)
	{
		//MabCast<MabUITextLine>(child_node)->SetText(PlayerPositionEnum::GetPlayerPositionTextAbbreviated(player_info->primary_position));
		pTextBlock->SetText(FText::FromString(UWWUITranslationManager::Translate(PlayerPositionEnum::GetPlayerPositionTextAbbreviated(player_info->primary_position))));
	}

	pTextBlock = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(row_node, FString(RECRUIT_SECOND_STR)));
	if (pTextBlock)
	{
		//MabCast<MabUITextLine>(child_node)->SetText(PlayerPositionEnum::GetPlayerPositionTextAbbreviated(player_info->secondary_position));
		pTextBlock->SetText(FText::FromString(UWWUITranslationManager::Translate(PlayerPositionEnum::GetPlayerPositionTextAbbreviated(player_info->secondary_position))));
	}

	FString display_name;

#ifdef ENABLE_GAME_DEBUG_MENU
	if (SIFDebug::GetCareerModeDebugSettings()->GetDisplayIDs())
	{
		FString FirstName = SIFGameHelpers::GAConvertMabStringToFString(player_info->first_name);
		FString LastName = SIFGameHelpers::GAConvertMabStringToFString(player_info->last_name);

		if (!FirstName.IsEmpty())
		{
			display_name = FString::FromInt(player_info->db_id) + ", " + LastName + ", " + FirstName[0] + ".";
		}
		else
		{
			display_name = FString::FromInt(player_info->db_id) + ", " + LastName + ", ";
		}
		
	}
	else
#endif
	{
		FString FirstName = SIFGameHelpers::GAConvertMabStringToFString(player_info->first_name);
		FString LastName = SIFGameHelpers::GAConvertMabStringToFString(player_info->last_name);

		if (!FirstName.IsEmpty())
		{
			display_name = LastName + ", " + FirstName[0] + ".";
		}
		else
		{
			display_name = LastName + ", " ;
		}
		
	}
	// #rc3_legacy_censor
	// any downloaded names should already be censored
	MabString displayName = SIFGameHelpers::GAConvertFStringToMabString(display_name);
	RUHUDUpdater::CensorPlayerName(-1, player_info->db_id, displayName);

	//child_node = node->GetChildByContext(RECRUIT_NAME_STR);
	//MabCast<MabUITextLine>(child_node)->SetText(display_name);
	pTextBlock = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(row_node, FString(RECRUIT_NAME_STR)));
	if (pTextBlock)
	{
		pTextBlock->SetText(SIFGameHelpers::GAConvertMabStringToFText(displayName));
	}
	
	//child_node = node->GetChildByContext(RECRUIT_AGE_STR);
	//MabCast<MabUITextLine>(child_node)->SetText(MabString(0, "%d", player_info->age));
	pTextBlock = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(row_node, FString(RECRUIT_AGE_STR)));
	if (pTextBlock)
	{
		pTextBlock->SetText(SIFGameHelpers::GAConvertMabStringToFText(MabString(0, "%d", player_info->age)));
	}

	//child_node = node->GetChildByContext(RECRUIT_RATING_STR);
	//MabCast<MabUITextLine>(child_node)->SetText(MabString(0, "%d", player_info->overall_rating));
	pTextBlock = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(row_node, FString(RECRUIT_RATING_STR)));
	if (pTextBlock)
	{
		pTextBlock->SetText(SIFGameHelpers::GAConvertMabStringToFText(MabString(0, "%d", player_info->overall_rating)));
	}

	//child_node = node->GetChildByContext(RECRUIT_VALUE_STR);
	//if (is_international)
	//	MabCast<MabUITextLine>(child_node)->SetText(MabString(0, "%d", player_info->market_value));		// Market value here is actually 'caps'
	//else
	//	SIFUIHelpers::ConvertIntToCurrencyInTextNode(child_node, player_info->market_value);
	pTextBlock = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(row_node, FString(RECRUIT_VALUE_STR)));
	if (pTextBlock)
	{
		if (is_international)
		{
			pTextBlock->SetText(SIFGameHelpers::GAConvertMabStringToFText(MabString(0, "%d", player_info->market_value)));		// Market value here is actually 'caps'
		}
		else
		{
			SIFUIHelpers::ConvertIntToCurrencyInTextNode(pTextBlock, player_info->market_value);
		}
	}

	//child_node = node->GetChildByContext(RECRUIT_CLUB_STR);
	//MabCast<MabUITextLine>(child_node)->SetText(player_info->club_name);
	pTextBlock = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(row_node, FString(RECRUIT_CLUB_STR)));
	if (pTextBlock)
	{
		pTextBlock->SetText(SIFGameHelpers::GAConvertMabStringToFText(player_info->club_name));
	}
}


// Reset the starting and selected index, so that the selected player is highlighted
bool UWWUIPopulatorCareerRecruit::SelectPlayer(int selected_player_db_id)
{
	// Find where the selected player is in the list
	int current_selected_player_idx = 0;

	// Fixed case causing crash where there is no filtered players.
	if (player_arrays->filtered_players.size() <= current_selected_player_idx)
	{
		return false;
	}

	while (player_arrays->filtered_players[current_selected_player_idx]->db_id != selected_player_db_id)
	{
		current_selected_player_idx++;

		if ((size_t)current_selected_player_idx >= player_arrays->filtered_players.size())
		return false;
	}

	// Get new starting index & selected index to rehighlight the previously selected item
	starting_index = current_selected_player_idx - PARTIAL_POPULATE_PADDING;
	if (starting_index + (int)list_node->GetChildrenCount() > (int)player_arrays->filtered_players.size())
	{
		starting_index = (int)(player_arrays->filtered_players.size() - list_node->GetChildrenCount());
	}
	else if (starting_index < 0)
	{
		starting_index = 0;
	}
	selected_index = current_selected_player_idx - starting_index;

	InData.starting_index = starting_index;

	MABASSERT(selected_index < (int)list_node->GetChildrenCount());

	//RUUIPartialListBox* list_node = MabCast<RUUIPartialListBox>(list_node);
	//list_node->SetStartingIndex(starting_index);
	//list_node->SetSelectedIndex(selected_index);

	//list_node->SelectNodeByIndex(selected_index);
	select_index_at_end_of_populate_and_refresh = selected_index;
	SelectFirstElement = false;

	return true;
}


// Done in screen
//void UWWUIPopulatorCareerRecruit::UpdateColumnHighlight(int old_selected_column)
//{


	//if (old_selected_column == selected_column)
	//	return;

	//// Column highlight moved: Disable old column highlight & enable new one
	//UWidget* column_highlight;
	//for (size_t i = 0; i < list_node->GetChildrenCount(); ++i)
	//{
	//	UWidget* row_child = list_node->GetChildAt(i);
	//	MABASSERT(row_child);

	//	for (size_t j = 0; j < row_child->GetChildrenCount(); ++j)
	//	{
	//		UWidget* child = row_child->GetChildAt(j);
	//		MABASSERT(child);

	//		if (child->IsVisible())
	//		{
	//			column_highlight = child->GetChildByContext(MabString(0, "%s%d/%s", RUCRP_COLUMN_TAG, old_selected_column, RUCRP_COLUMN_BG));
	//			if (column_highlight)
	//				column_highlight->SetVisible(false);

	//			column_highlight = child->GetChildByContext(MabString(0, "%s%d/%s", RUCRP_COLUMN_TAG, selected_column, RUCRP_COLUMN_BG));
	//			if (column_highlight)
	//				column_highlight->SetVisible(true);
	//		}
	//	}
	//}
//}

void UWWUIPopulatorCareerRecruit::UpdateComparePlayerHighlight(int old_compare_player_db_id)
{
	if (old_compare_player_db_id == compare_player_db_id)
		return;

	for (size_t i = 0; i < list_node->GetChildrenCount(); ++i)
	{
		// Get player
		const PlayerInfo* db_player = player_arrays->filtered_players[starting_index + i];

		// Repopulate old & new compare_player_db_id
		if (old_compare_player_db_id == db_player->db_id || compare_player_db_id == db_player->db_id)
			PopulatePlayerInfo(db_player, list_node->GetChildAt(i), (int)i == selected_index);
	}
}


void UWWUIPopulatorCareerRecruit::UpdateSelectedPlayerHighlight()
{
	// Unselect current player
	{
		const PlayerInfo* db_player = player_arrays->filtered_players[starting_index + prev_selected_index];

		//MabUINode* child = list_node->GetChildByIndex(prev_selected_index);
		//MABASSERT(child);
		//PopulatePlayerInfo(db_player, child, false);
		UWidget* child = list_node->GetChildAt(prev_selected_index);
		if (child)
		{
			PopulatePlayerInfo(db_player, child, false);
		}
		else
		{
			ensure(child);
		}
	}


	// Select the new player
	{
		const PlayerInfo* db_player = player_arrays->filtered_players[starting_index + selected_index];
		//MabUINode* child = list_node->GetChildByIndex(selected_index);
		//MABASSERT(child);
		//PopulatePlayerInfo(db_player, child, true);
		UWidget* child = list_node->GetChildAt(selected_index);
		if (child)
		{
			PopulatePlayerInfo(db_player, child, false);
		}
		else
		{
			ensure(child);
		}
	}
}

void UWWUIPopulatorCareerRecruit::PurgeChildren(UWidget * widget)
{
	UPanelWidget* panelWidget = Cast<UPanelWidget>(widget);
	if (panelWidget)
	{
		if (panelWidget->GetChildrenCount() > 0)
		{
			while (panelWidget->GetChildrenCount() > 0)
			{
				panelWidget->GetChildAt(panelWidget->GetChildrenCount() - 1)->ConditionalBeginDestroy();
				panelWidget->RemoveChildAt(panelWidget->GetChildrenCount() - 1);
			}
		}
	}
}


void UWWUIPopulatorCareerRecruit::FocusListField(int32 fieldIdx)
{
	SelectFirstElement = false;
#ifdef UI_USING_UMG
	UWWUIListField* field = Cast<UWWUIListField>(list_node->GetChildAt(fieldIdx));

	if (field)
	{
		// Skip over any collapsed fields
		if (field->Visibility != ESlateVisibility::Collapsed)
		{
			UButton* button = Cast<UButton>(field->GetButtonWidget());
			if (button)
			{
				ScreenRef->SetFocusToWidget(button, UGameplayStatics::GetPlayerController(GetWorld(), 0));
				UScrollBox* list_node_lambda = list_node;
				RecruitPopulatorSetupData* pLambdaInData = &InData;
				TimerHandleDelayScrollboxOffset = UWWUIFunctionLibrary::OnTimer(KINDA_SMALL_NUMBER, FTimerDelegate::CreateLambda([this, fieldIdx, list_node_lambda, pLambdaInData, button]()
				{
					int32 ScrollOffset = 0;
					if (list_node_lambda->GetChildrenCount() == PARTIAL_POPULATE_TOTAL_ELEMENTS)
					{
						// if partial populating
						if (fieldIdx <= PARTIAL_POPULATE_PADDING)
						{
							// if at top of padding, scroll focused element to top of list
							ScrollOffset = fieldIdx;
							
						}
						else if (fieldIdx >= PARTIAL_POPULATE_TOTAL_ELEMENTS - PARTIAL_POPULATE_PADDING - 2)
						{
							// if at bottom of padding, scroll focused element to bottom of list
							ScrollOffset = fieldIdx - VISIBLE_LIST_ELEMENTS + 1;
						}
						list_node_lambda->SetScrollOffset(ScrollOffset * FIELD_HEIGHT);
						ScreenRef->SetFocusToWidget(button, UGameplayStatics::GetPlayerController(GetWorld(), 0));
						
					}
				}
				));
			}
		}
	}
#endif
}

//------------------------------------------------------------------------------------------------------------------
// remove and add stuff

void UWWUIPopulatorCareerRecruit::RemoveFieldAt(int32 i)
{
	if (!pScrollBoxWidget)
	{
		return;
	}

	UPanelWidget* panelWidget = Cast<UPanelWidget>(pScrollBoxWidget);
	if (panelWidget)
	{
		panelWidget->RemoveChildAt(i);
	}
}

void UWWUIPopulatorCareerRecruit::AddFieldAt(int32 i)
{
	if (!pScrollBoxWidget)
	{
		return;
	}

	CareerRecruitCreationNodeCallback callbackObject(pScrollBoxWidget/*, dataList.ArrayOption*/);
	CreateNodesFromTemplate(dataList.TemplateName, 1, &callbackObject, i);
}


	//------------------------------------------------------------------------------------------------------------------