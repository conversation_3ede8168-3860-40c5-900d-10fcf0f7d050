#pragma once
 
#include "Misc/DateTime.h"
#include "ProModePopulatedInterface.generated.h"
 
USTRUCT(BlueprintType)
struct FBeAProMenuPopulationPack
{
	GENERATED_BODY()
	UPROPERTY(BlueprintReadOnly)
	FText Year;
	UPROPERTY(BlueprintReadOnly)
	FText Competition;
	UPROPERTY(BlueprintReadOnly)
	FText Round;
	UPROPERTY(BlueprintReadOnly)
	FText NextMatchHomeTeam;
	UPROPERTY(BlueprintReadOnly)
	FText NextMatchAwayTeam;
	UPROPERTY(BlueprintReadOnly)
	FText ClubTeam;
	UPROPERTY(BlueprintReadOnly)
	FText InternationalTeam;
	UPROPERTY(BlueprintReadOnly)
	FText ProName;
	UPROPERTY(BlueprintReadOnly)
	FDateTime LastModificationTime;
	UPROPERTY(BlueprintReadOnly)
	int SaveIndex;
};

UINTERFACE(MinimalAPI, Blueprintable)
class UProModePopulatedInterface : public UInterface
{
	GENERATED_BODY()
};

class IProModePopulatedInterface
{
	GENERATED_BODY()
 
public:
	UFUNCTION(BlueprintCallable, BlueprintImplementableEvent)
		void OnProPopulated(
			UPanelWidget* InfoPanelRoot,
			FBeAProMenuPopulationPack Data
		);
};