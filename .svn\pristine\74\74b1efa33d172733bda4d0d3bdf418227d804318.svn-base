/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef RUTUTORIALTACKLE_H
#define RUTUTORIALTACKLE_H

#include "Match/RugbyUnion/TutorialMode/RUTutorial.h"

class RUTackleResult;

class RUTutorialTackle : public RUTutorial
{
public:
	RUTutorialTackle(SIFGameWorld* game);
	virtual ~RUTutorialTackle();

	// Perform logic
	virtual void Update(float delta_time);

	// Check pass/fail
	virtual bool HasSucceeded();
	virtual bool HasFailed(); 

	// More...
	virtual void Initialise();
	virtual void Start();
	virtual void Finish();
	virtual void CleanUp();	

	bool IsStageComplete( RUStageNum stage_num );
	bool IsStageFailed( RUStageNum stage_num );
private:

	void TackleEnded( const RUTackleResult& tackle_result );
	void TackleeOnGround( const RUTackleResult& tackle_result );
	bool bTackleeOnGround = false;

	void CalculateMedal();
	FVector tackle_pos;

	ARugbyCharacter* player_with_ball;
};

#endif