/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Actions/RUActionTacklee.h"

#include "Match/AI/Actions/RUActionPass.h"
#include "Match/AI/Actions/RUActionTackler.h"
#include "Match/Ball/SSBall.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Debug/RUGameDebugSettings.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseRuck.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Match/SSPlayerFilter.h"
#include "Match/SSRole.h"
#include "Match/SSSpatialHelper.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Utility/RURandomNumberGenerator.h"

//#rc3_legacy_include #include "Animation.h"
//#rc3_legacy_include #include <Animation.ipp> // animation inlines
//#rc3_legacy_include #include <EventTrack.h> // animation
//#rc3_legacy_include #include <NMMabAnimationEvents.h>
//#rc3_legacy_include #include <NMMabAnimationEvents.h>
//#rc3_legacy_include #include <NMMabAnimationNetwork.h>

static const char* RIGHT_ARM_FREE = "RightArmFree";
static const char* LEFT_ARM_FREE = "LeftArmFree";

#define ENABLE_RUCK_FROM_TACKLE

constexpr const float DEFAULT_TACKLE_FAIL_EXIT_SPEED = 2.6f; // Speed the player is running at if a tackle fails
constexpr const float MAX_HELD_LOOP_TIME = 1.0f;
constexpr const float HELD_DOWN_TIME_TOO_LONG = 2.0f;
constexpr const float HELD_DOWN_TIME_MIN = 0.8f;
constexpr const float HEAD_HEIGHT = 0.6f; // How far off the ground the head is to transition to tacklee on ground state

MABRUNTIMETYPE_IMP1(RUActionTacklee, RUActionTackleBase);

//#define ENABLE_TACKLE_LOGGING

#ifdef ENABLE_TACKLE_LOGGING
	#if PLATFORM_PS4
	#define MABLOGDEBUGTACKLE MABLOGDEBUG
	#else
	#define MABLOGDEBUGTACKLE( format, ... ) MABLOGDEBUG( format, __VA_ARGS__ )
	#endif
#else
#define MABLOGDEBUGTACKLE( format, ... )
#endif

void RUActionTacklee::SetDrivenTackleTakenDown( bool ddriven_tackle_taken_down )
{
	driven_tackle_taken_down = ddriven_tackle_taken_down;
}

bool RUActionTacklee::IsDrivenTackleTakenDown() const
{
	return driven_tackle_taken_down;
}

RUActionTacklee::RUActionTacklee( ARugbyCharacter* player )
: RUActionTackleBase( player )
, tackle_result()
, tacklers()
, broken_out( false )
, strip_attempted( false )
, ball_stripped( false )
, player_reset( false )
, skip_get_up( false )
, exit_anim_time_remaining( 0.0f )
, anim_time_played( 0.0f )
, notified_tackle_ended( false )
, notified_tackle_successful_update_count( 0 )
, time_at_last_successful_notify( 0.0f )
, pending_successful_notification( false )
, notify_success_timer()
, tackle_completed_successfully( false )
, wait_for_getup_before_notify( false )
, notified_pending_suspension( false )
, tackle_fail_exit_speed( 0.0f )
, time_at_tackle_start( 0.0f )
, pass_delay_timer()
, injury_timer()
, knocked_pass_timer()
, tackler_count_since_start( 0 )
, unparent_on_getup( false )
, cando_fastptb( false )
, is_being_restarted( false )
, updated_go_to_ground_agg_diff( false )
, go_to_ground_agg_diff( 0.0f )
, notified_breakdown_possible( false )
, notified_breakdown_start( false )
, has_contacted( false )
, has_contacted_this_tackle( false )
, has_contacted_ground(false)
, updated_held_down_agg_diff( false )
, held_down_agg_diff( 0.0f )
, held_down_ai_time( 0.0f )
, held_down_start_time( 0.0f )
, held_down_too_long_notified( false )
, drop_time( 0.0f )
, drop_type( DT_NONE )
, drop_player( NULL )
, driven_tackle_taken_down(false)
, time_in_state(0.0f)
{
	tackle_fail_exit_speed	= DEFAULT_TACKLE_FAIL_EXIT_SPEED;
	notified_tackle_successful_update_count = 0;
	notified_tackle_ended = false;
	pass_delay_timer.Reset( m_pGame->GetSimTime(), 1.0f );
	time_at_last_successful_notify = m_pGame->GetSimTime()->GetAbsoluteTime();
	tackler_count_since_start = 0;
	unparent_on_getup = false;
}

void RUActionTacklee::Restart( const RUTackleResult& Intackle_result )
{
	MABLOGDEBUGTACKLE( "(%d,%d) Tacklee is restarting on pid", Intackle_result.tackle_determination_index, m_pPlayer->GetAttributes()->GetIndex() );

	int curr_tackler_count_since_start = tackler_count_since_start;
	bool curr_has_contacted = has_contacted;
	NotifyRestartPending();
	// Inform all tacklers to continue on their own
	DetachTacklers( true );
	Exit();
	int curr_notified_tackle_successful_update_count = notified_tackle_successful_update_count;
	Enter(Intackle_result);

	is_being_restarted = true;
	tackler_count_since_start = curr_tackler_count_since_start + 1;

	/// If we had already contacted since the first entry - then preserve this state
	if ( curr_has_contacted )
		has_contacted = true;

	if ( curr_notified_tackle_successful_update_count > 0 )
	{
		MABLOGDEBUGTACKLE( "(%d,%d) Making sure that tackle is not notfied again!", Intackle_result.tackle_determination_index, m_pPlayer->GetAttributes()->GetIndex() );
		notified_tackle_successful_update_count = curr_notified_tackle_successful_update_count;
	}
}

void RUActionTacklee::Enter( const RUTackleResult& _tackle_result )
{
	tackle_result = _tackle_result;
	SetRuckTeamState( NULL );

	/// Do not notify a second breakdown possible if we are restarting
	if ( !is_being_restarted ) {
		notified_breakdown_possible = false;
	}

	notified_breakdown_start = false;
	has_contacted = false;
	has_contacted_this_tackle = false;
	has_contacted_ground = false;
	driven_tackle_taken_down = false;

	RUActionTackleBase::Enter();
	if ( player_animation )
	{
		player_animation->SetTackleHeight( tackle_result.body_position[0] );
		player_animation->SetTackleTryProbability( tackle_result.try_tackle_type );
		player_animation->SetTackleSuccess( tackle_result.successful );
		player_animation->SetTackleDominance( tackle_result.dominance );
		player_animation->SetTackleSidestepType( tackle_result.ss_tackle_type );


#ifdef ENABLE_ANIMATION_DEBUG_SETTINGS
		if ( RUPlayerAnimation::show_tackle_result )
			SIF_DEBUG_DRAW( SetText( 100101, 700, 500, MabString(0, "TackleeAnim: Height %i, Dominance: %i", tackle_result.body_position[0], tackle_result.dominance ) ) );
#endif
	}

	time_in_state = 0.0f;
}
#pragma optimize("", off)
bool RUActionTacklee::InternalEnter()
{
	MABLOGDEBUGTACKLE("\n\n\nTacklee entering tacklee at time %f", m_pGame->GetSimTime()->GetAbsoluteTime().ToSeconds());

	RUGameState *game_state = m_pPlayer->GetGameWorld()->GetGameState();
	RUPlayerMovement *movement = m_pPlayer->GetMovement();
	RUPlayerAttributes *attributes = m_pPlayer->GetAttributes();

	game_state->Tackle( tackle_result );

	// If the tackle type does not require any participation
	// from the tacklee then quit out
	if ( tackle_result.tackle_result == TRT_TRY && tackle_result.try_tackle_type == TRY_TACKLE_TYPE::TTT_JUMPOVER )
	{
		return false;
	}

	notified_pending_suspension = false;
	time_at_tackle_start = m_pGame->GetSimTime()->GetAbsoluteTime();

	// reset the variable - it can be set again next time stop is going to be called on a restart
	is_being_restarted = false;

	tackler_count_since_start = 1;
	unparent_on_getup = false;
	cando_fastptb = true;

	tackle_fail_exit_speed = movement->GetCurrentSpeed() * 0.7f;
	MabMath::ClampLower( tackle_fail_exit_speed, movement->GetIdealSpeed( AS_SLOWJOG ) );

	/// we want to start off with 0 time remaining so we can move into the tackle animation
	exit_anim_time_remaining = 0.0f;

	current_state = TS_PRE_TACKLE;
	next_state = TS_START_TACKLE;
	strip_attempted = false;
	ball_stripped = false;
	on_ground = false;
	notified_tackle_successful_update_count = 0;
	notified_tackle_ended = false;
	pending_successful_notification = false;
	broken_out = false;
	skip_get_up = false;
	player_reset = false;
	tacklers.clear();
	wait_for_getup_before_notify = false;

	// Initialise the buddy joint rotation/position as soon as we enter
	// MabString anim = tackle_result.GetTackleeAnimationName( TAS_IMPACT ); #MLW NOT USED??

	// Get hold of the impact touch down anims
	MabVariant try_start;
	MabVariant try_end;
	MabVariant bh_end_frame;

	MABLOGDEBUGTACKLE("(%d,%d)-!- Tackle Sequences: %d", tackle_result.tackle_determination_index, attributes->GetIndex(), tackle_result.anim_sequences );
	MABLOGDEBUGTACKLE("(%d,%d)-!- Taken Down: %d", tackle_result.tackle_determination_index, attributes->GetIndex(), tackle_result.taken_down );
	MABUNUSED( attributes );

	m_pPlayer->GetMabAnimationEvent().Add( this, &RUActionTacklee::AnimationEvent );

	// Set this initially to the same value as the instantaneous result
	// It may change over the course of the tackle
	tackle_completed_successfully = tackle_result.successful;
	if ( tackle_completed_successfully )
	{
		OnTackleSuccessful( 0.6f + m_pGame->GetRNG()->RAND_RANGED_CALL(float, 0.01f ) );
	}

	pass_delay_timer.Reset( m_pGame->GetSimTime(), 0.3f );

	// #MLW In regards to tackles, do we want to lessen the chance of balls being dropped..? 
	// Seems like rugby league is more about holding the ball through the tackle?
	// If the player is going to drop the ball at a random time or at impact then set it here
	drop_time = -1.0f;   // Do not drop
	drop_player = NULL;  // Drop to free ball
	drop_type = DT_NONE; // Do not drop

	if ( !tackle_result.IsPlayerInjured(m_pPlayer) )
	{
		if ( tackle_result.drop_ball_type == TDT_RANDOM )
		{
			DropBall( 0.3f + m_pGame->GetRNG()->RAND_RANGED_CALL(float, 0.4f) );
		}
		else if ( tackle_result.drop_ball_type == TDT_AT_IMPACT )
		{
			DropBall( 0.3f );
		}
	}

	// If the tacklee is currently passing then force a hit on the
	// ball if necessary
	if ( m_pPlayer->GetActionManager()->IsActionRunning(ACTION_PASS) )
	{
		knocked_pass_timer.Reset( m_pGame->GetSimTime(), 0.1f );
	}
	else
	{
		knocked_pass_timer.SetEnabled(false);
	}

	/// Reset the last try tackle validity game states
	game_state->SetTryScoredInTackle( false );

	/// Reset the bool that tells us if we have updated the go to ground value
	updated_go_to_ground_agg_diff = false;
	updated_held_down_agg_diff = false;
	held_down_too_long_notified = false;

	RUActionTackleBase::Initialise(&tackle_result);
	RUActionTackleBase::InternalEnter();
	return true;
}
#pragma optimize("", on)
void RUActionTacklee::Update( const MabTimeStep& game_time_step )
{
	RUGameState *game_state = m_pPlayer->GetGameWorld()->GetGameState();
	//SIF_DEBUG_DRAW( SetBox( 80085 + player->GetAttributes()->GetNumber(), player->GetMovement()->GetCurrentPosition(), FVector(0.5f, 0.1f, 0.5f), MabColour::Green));

	if (time_in_state > 20.0f)
	{
		UE_LOG(LogTemp, Display, TEXT("PLAYERS_STUCK_DEBUG: Tacklee [%s] [%d] has been in this state (%i) for too long (%f)."), *m_pPlayer->GetName(), m_pPlayer->GetAttributes()->GetIndex(), current_state, time_in_state);
	}

	// Force exit of tackle if we're no longer the ball holder
	bool force_update = false;
	if ( game_state->GetBallHolder() != m_pPlayer)
	{
		if(current_state == TS_IN_TACKLE_STANDING && !pending_successful_notification )
		{
			// contested tackle continue through to the ground
			if (tackle_result.tackle_result == TRT_CONTESTED)
			{
				SetDrivenTackleTakenDown(true);
				RequestTransition( TS_TRANSITION_TO_GROUND );
			}
			else
			{
				RequestTransition( TS_RELEASE_STANDING );
			}
		}

		// Dewald WW - occasionally a tackleeee just stands still in the middle of the field?
		/*if(current_state == TS_END_TACKLE)
		{
			// CHECK IF WE HAVE NO TACKLERS?!
			if( tacklers.size() == 0 && time_in_state > 3.0f )
			{
				bool _debug = false;
				if(_debug)
				{
					SIFApplication::GetApplication()->GetAppTime()->GetSimulationTimeSource()->Pause( true );
				}

				//_debug = false;

				//if(_debug)
				{
					MABLOGDEBUG("PLAYERS_STUCK_DEBUG: [%d] Tacklee was in END state for too long?", player->GetAttributes()->GetIndex());
					RequestTransition( TS_POST_TACKLE );
					force_update = true;
				}
			}
		}*/
	}

	RUActionTackleBase::UpdateState(force_update);

	if ( notified_tackle_successful_update_count > 0 )
	{
		notified_tackle_successful_update_count++;
	}

	UpdateBreakdownPossible();
	UpdateDropBall( game_time_step.delta_time.ToSeconds() );

	#if defined( ENABLE_ROLE_DEBUG_STRINGS )
	player->GetRole()->SetDebugString( this->GetStateName( current_state ) );
	#endif

	time_in_state += game_time_step.delta_time.ToSeconds();
}

void RUActionTacklee::InternalExit(bool in_destroy)
{
	MABLOGDEBUG( "InternalExit Tacklee: (%d,%d), Stopping tacklee %d, in_destroy(%d)", tackle_result.tackle_determination_index, m_pPlayer->GetAttributes()->GetIndex(), m_pPlayer->GetAttributes()->GetIndex(), in_destroy );
	UE_LOG(LogTemp, Display, TEXT("InternalExit Tacklee: '%s'"), *m_pPlayer->GetName());

	if(in_destroy)
		return;

	RUGameState *game_state = m_pGame->GetGameState();
	tackler_count_since_start = 0;

	// Remove animtion handler
	m_pPlayer->GetMabAnimationEvent().Remove( this, &RUActionTacklee::AnimationEvent );

	//SIF_DEBUG_DRAW( RemoveBox(80085 + player->GetAttributes()->GetNumber()) );

	// If we were supposed to drop the ball then make sure it happens, unless we're in a ruck.
	if ( tackle_result.drop_ball_type != TDT_NONE && game_state->GetBallHolder() == m_pPlayer && game_state->IsGameInStandardPlay() && m_pGame->GetGameState()->GetPhase() != RUGamePhase::RUCK )
	{
		DoDropBall();
	}

	if ( pending_successful_notification )
	{
		NotifyTackleSuccessful();
	}

	// if we are being interrupted early, make sure tacklers know to detach themselves
	for ( Tacklers::iterator i = tacklers.begin(); i != tacklers.end(); ++i )
	{
		ARugbyCharacter* tackler = *i;
		RUActionTackler* tackler_action = tackler->GetActionManager()->GetAction<RUActionTackler>();
		if ( tackler_action && tackler_action->IsRunning() )
		{
			tackler_action->Exit();
		}
	}

	// Reset this thing
	notified_tackle_successful_update_count = 0;

	RUActionTackleBase::InternalExit(in_destroy);

	m_pGame->GetEvents()->tacklee_action_exit(m_pPlayer);

	#if defined( ENABLE_ROLE_DEBUG_STRINGS )
	if ( player->GetRole() )
	{
		player->GetRole()->SetDebugString( "" );
	}
	#endif
}

void RUActionTacklee::RegisterTackler( ARugbyCharacter* pPlayer ) // #MLW NOT USED??
{
	// Ensure this tackler has not already registered
	MABASSERT( std::find(tacklers.begin(), tacklers.end(), pPlayer) == tacklers.end() );
	tacklers.push_back( pPlayer );
}

bool RUActionTacklee::LogicAllowsTransition( TackleState old_state, TackleState new_state )
{
	MABUNUSED(old_state);

	if ( new_state == TS_START_TACKLE )
	{
		MABLOGDEBUGTACKLE( "(%d,%d) RUTackleeUpdater::AdvanceLogic TS_PRE_TACKLE AdvanceMotion", tackle_result.tackle_determination_index, m_pPlayer->GetAttributes()->GetIndex() );

		// Are all the tacklers ready?
		int num_ready = 0;
		for ( Tacklers::iterator I = tacklers.begin(); I != tacklers.end(); ++I )
		{
			RUActionTackler* updater = (*I)->GetActionManager()->GetAction<RUActionTackler>();

			// If the tackler is ready increase num ready
			if ( updater->IsReadyToTackle() )
			{
				num_ready++;
			}
		}

		MABASSERT( num_ready <= tackle_result.n_tacklers );

		// Start the tackle if we have enough tacklers
		if ( num_ready != tackle_result.n_tacklers )
			return false;
	}

	// Wait for driven tackles to be resolved and transitioned to the ground
	// before allowing transition to TS_TRANSITION_TO_GROUND.
	if ( new_state == TS_TRANSITION_TO_GROUND )
	{
		if ( tackle_result.tackle_result == TRT_CONTESTED )
		{
			if ( !IsDrivenTackleTakenDown() )
			{
				return false;
			}
		}
	}

	// First ensure all tacklers are happy with this transition
	for (size_t i = 0; i < tacklers.size(); i++)
	{
		if(tacklers[i]->GetActionManager()->IsActionRunning(ACTION_TACKLER) &&
			!tacklers[i]->GetActionManager()->GetAction<RUActionTackler>()->CanTransition(new_state))
		{
			return false;
		}
	}

	// Now check other cases
	if ( old_state == TS_IN_TACKLE_ON_GROUND && new_state == TS_RELEASE_GROUND )
	{
		// While we're in the ruck, don't allow us to get up?
		/*RUGameState *game_state = player->GetGame()->GetGameState();
		if(game_state->GetPhase() == RUGamePhase::RUCK)
		{
			RUGamePhaseRuck* ruckPhase = game_state->GetPhaseHandler<RUGamePhaseRuck>();

			// If we're part of the currently active ruck, stop us from standing up, because it look stupid, especially in sevens.
			// Only restrict this when we're waiting for the ball release, otherwise contesting breaks.
			if(ruckPhase->GetState() == RSP_STATE_WAIT_RELEASE && ruckPhase->GetTacklee() == this->player)
			{
				return false;
			}
		}*/

		/*if( tacklers.size() && time_in_state >= 5.0f )
		{
			MABLOGDEBUG("TACKLEE has been in this state for way too long! Should we let them stand up now?");
		}*/

		// Players getting stuck on the ground while in this state.
		/*if( time_in_state >= 10.0f )
		{
			bool validRuckState = ruck_team_state != NULL && ruck_team_state->ruck_state != NULL;
			//tacklers.size() == 0
			MABLOGDEBUG("PLAYERS_STUCK_DEBUG: [%d] Tacklee has been in TS_IN_TACKLE_ON_GROUND for %f. notified_breakdown_start: %s, valid ruck state: %s, ruck state time after ball release: %f, has tacklers: %s, All positions free? %s",
				player->GetAttributes()->GetIndex(),
				time_in_state,
				notified_breakdown_start ? "true" : "false",
				validRuckState ? "true" : "false",
				validRuckState ? (ruck_team_state->ruck_state->time_in_state_after_release) : (0.0f),
				tacklers.size() > 0 ? "true" : "false",
				ruck_team_state->ruck_state->AreAllPositionsFreeNow(true) ? "true" : "false"
				);
			return true;
		}*/

		if ( /*notified_breakdown_start && */ruck_team_state != NULL && ruck_team_state->ruck_state != NULL && ruck_team_state->ruck_state->IsRunning() )
		{
			/*if( tacklers.size() == 0 && ruck_team_state->ruck_state->time_in_state_after_release >= 5.0f )
			{
				MABLOGDEBUG("PLAYERS_STUCK_DEBUG: [%d] The Ruck state is still going?! The ruck has been running for %.2f after ball release", player->GetAttributes()->GetIndex(), ruck_team_state->ruck_state->time_in_state_after_release);
				return true;
			}*/

			return false;
		}


		/*if ( ruck_team_state != NULL && ruck_team_state->ruck_state != NULL )
		{
			return ruck_team_state->ruck_state->IsBallReleased();
		}*/

		/// Check that we have a ruck team state and positions are not free
		//if ( ruck_team_state != NULL && ruck_team_state->ruck_state != NULL && ruck_team_state->ruck_state->IsRunning() )
		//{
			// Dewald WW - Seems like theres an issue where the ruck is still "running" but it's not, so the player gets stuck on the ground.
			// Debugging ticket 11490
			/*bool positionsAreFree = ruck_team_state->ruck_state->AreAllPositionsFreeNow();
			bool allowTrans = false;

			if( positionsAreFree &&
				ruck_team_state->ruck_state->defending.commital.bound.size() == 0 &&
				ruck_team_state->ruck_state->attacking.commital.bound.size() == 0)
			{
				MABBREAKMSG("Is the player stuck on the ground? Set allowTrans to true!");
				allowTrans = false;
			}
			return allowTrans;*/
			//return false;
		//}
	}

	return true;
}

void RUActionTacklee::OnStateTransition(TackleState old_state, TackleState new_state)
{
	MABUNUSED(old_state);
	RUPlayerMovement *movement = m_pPlayer->GetMovement();
	RUPlayerAttributes *attributes = m_pPlayer->GetAttributes();

	RUGameState *game_state = m_pGame->GetGameState();

	RequestTransition(GetNextState(new_state));

	time_in_state = 0.0f;

	switch ( new_state )
	{
		case TS_MAX:
		case TS_PRE_TACKLE:
			break;

		case TS_START_TACKLE:
		{
			MABLOGDEBUGTACKLE( "(%d,%d) RUTackleeUpdater::AdvanceLogic TS_START_TACKLE AdvanceMotion", tackle_result.tackle_determination_index, attributes->GetIndex() );
			// Adjust the tacklee
			attributes->SetAggression( tackle_result.new_tacklee_aggression );
			movement->SetFacingFlags( AFFLAG_FACETARGANGLE );
			movement->SetTargetFacingAngle( movement->GetCurrentFacingAngle() );
			break;
		}

		case TS_IN_TACKLE_STANDING:
		{
			bool near_try_line = MabMath::Fabs(movement->GetCurrentPosition().z) > (m_pGame->GetSpatialHelper()->GetFieldExtentsExcludingInGoals().y / 2.0f - 1.0f);
			if ( tackle_result.anim_sequences & TAS_TAKEN_DOWN )
			{
				MABLOGDEBUGTACKLE( "RUActionTacklee::AdvanceLogic: tackle_action->StateTransition( TS_TRANSITION_TO_GROUND )" );
				if ( tackle_result.tackle_result == TRT_CONTESTED )
				{
					SetDrivenTackleTakenDown( true ); // #MLW Changed this so we always take tackles to the ground, so we can no longer enter maul
													  // Could potentially tap in here for multi-person contested tackles that aren't taken to ground?
													   
													  
					// if we are already offloading out of this tackle then, pretend it is not contested
					//if (game_state->GetBallHolder() != player || near_try_line || player->GetActionManager()->IsActionRunning(ACTION_PASS) || tackle_result.drop_ball_type != TDT_NONE)
					//	SetDrivenTackleTakenDown(true);
				}
				// request to go to ground, if a maul forms in time it will override this
				RequestTransition( TS_TRANSITION_TO_GROUND );
			}
			else
			{
				MABLOGDEBUGTACKLE( "RUActionTacklee::AdvanceLogic: tackle_action->StateTransition( TS_RELEASE_STANDING )" );
				RequestTransition( TS_RELEASE_STANDING );
				if (game_state->GetBallHolder() == m_pPlayer)
				{
					OnTackleSuccessful(0.0f);
				}
			}
			break;
		}

		case TS_IN_TACKLE_ON_GROUND:
		{
			// for contested tackles switch to normal state machine when it hits the ground
			// to cater for the 2 different ways these tackle types can end
			if ( tackle_result.tackle_result == TRT_CONTESTED )
				tackle_result.state_machine = TSM_SUCCESS;

			if ( tackle_result.drop_ball_type == TDT_AT_HIT_GROUND )
			{
				DropBall( 0.001f );
			}

#if ENABLE_SUCCESSFUL_ANKLE_TAPS 
			
			// #MLW ANKLE TAPS Will need to handle pile-on logic here
			
			if (tackle_result.tackle_result == TRT_ANKLE_TAP2)
			{
				DetachTacklers(true);
			}
#endif

			held_down_start_time = m_pGame->GetSimTime()->GetAbsoluteTime().ToSeconds();

			if ( m_pPlayer->GetGameWorld()->GetGameState()->GetBallHolder() == m_pPlayer )
			{
				/// Abort any passes or kicks in progress
				if ( m_pPlayer->GetActionManager()->IsActionRunning(ACTION_PASS) )
				{
					m_pPlayer->GetActionManager()->GetAction( ACTION_PASS )->Exit();
				}

				if ( m_pPlayer->GetActionManager()->IsActionRunning(ACTION_KICK) )
				{
					m_pPlayer->GetActionManager()->GetAction( ACTION_KICK )->Exit();
				}

				SetBreakdownStart( true );
			}

			m_pPlayer->GetMovement()->SetMotionSource( NULL );
			for ( Tacklers::iterator i = tacklers.begin(); i != tacklers.end(); ++i )
			{
				ARugbyCharacter* tackler = *i;
				MABASSERT( tackler );
				tackler->GetMovement()->SetMotionSource( NULL );
			}

			break;
		}

		case TS_TRANSITION_TO_INJURED:
			break;

		case TS_INJURED:
		{
			if ( m_pGame->GetGameState()->GetPhase() != RUGamePhase::TRY_REACTION && m_pGame->GetGameState()->GetPhase() != RUGamePhase::TRY_CUTSCENE && m_pGame->GetGameState()->GetPhase() != RUGamePhase::SIMULATION)
			{
				MABASSERT( m_pGame->GetGameState()->GetBallHolder() == m_pPlayer );
				DetachTacklers( false );
				m_pGame->GetEvents()->injury_started();
				injury_timer.SetEnabled( false );
				game_state->Injury( tackle_result.tacklee, tackle_result.injury_types[0] );
				MABLOGDEBUGTACKLE( "(%d,%d) Now player is in injured state", tackle_result.tackle_determination_index, attributes->GetIndex() );
			}
			break;
		}

		case TS_TRANSITION_TO_GROUND:
		{
			MABLOGDEBUGTACKLE( "(%d,%d) Going to ground!", tackle_result.tackle_determination_index, attributes->GetIndex() );
			OnTackleSuccessful();
			m_pPlayer->GetMovement()->SetMotionSource( NULL );
			break;
		}

		case TS_TRANSITION_TO_BREAKOUT:
		{
			game_state->BrokenTackle( tackle_result );
			tackle_completed_successfully = false;
			broken_out = true;
			m_pPlayer->GetMovement()->SetMotionSource( NULL );
			break;
		}

		case TS_TRANSITION_TO_STRIP_SUCCESS:
		{
			MABLOGDEBUGTACKLE( "(%d,%d) Transitioning to strip success", tackle_result.tackle_determination_index, attributes->GetIndex() );
			// player is the tackler/stripper
			game_state->StripResult( tackle_result, m_pPlayer, SR_SUCCESS );
			break;
		}

		case TS_TRANSITION_TO_STRIP_FAIL:
		{
			MABLOGDEBUGTACKLE( "(%d,%d) Strip failed!", tackle_result.tackle_determination_index, attributes->GetIndex() );
			// player is the tackler/stripper
			game_state->StripResult( tackle_result, m_pPlayer, SR_FAIL );
			break;
		}

		case TS_TRANSITION_TO_STRIP_PARTIAL:
		{
			MABLOGDEBUGTACKLE( "(%d,%d) Transitioning to strip partial", tackle_result.tackle_determination_index, attributes->GetIndex() );
			break;
		}

		case TS_RELEASE_GROUND:
		case TS_RELEASE_STANDING:
		{
			MABLOGDEBUGTACKLE( "(%d,%d) Releasing from the tackle!", tackle_result.tackle_determination_index, attributes->GetIndex() );
			NotifyTackleEnded();
			if ( tackle_completed_successfully )
			{
				OnTackleSuccessful();
			}
			break;
		}

		case TS_END_TACKLE:
		{
			if ( tackle_completed_successfully )
			{
				OnTackleSuccessful();
			}

			DetachTacklers( true );
			break;
		}

		case TS_POST_TACKLE:
		{
			if ( tackle_completed_successfully )
			{
				OnTackleSuccessful();
			}

			// If animations are different, then its the first time we're updating, reset player details
			if ( !player_reset )
			{
				if ( !tackle_completed_successfully )
				{
					// Reset player speed, since tackle failed
					movement->SetCurrentSpeed( tackle_fail_exit_speed );
				}
				else
				{
					movement->StopAllMovement();
				}

				// Ensure players are in the correct position and orientation
				if ( !skip_get_up )
				{
					//exit_anim_frames_remaining = PlayAnimation( anim_name, true );
					exit_anim_time_remaining = 0.0f;

					//MABLOGDEBUGTACKLE( "(%d,%d) POSTTACKLE exit_anim_frames_remaining for animation %s is %d" , tackle_result.tackle_determination_index, player->GetIndex(), anim_name.c_str() ,exit_anim_frames_remaining );

				}
				else
				{
					exit_anim_time_remaining = 0.0f;
				}

				player_reset = true;
			}

			movement->ResetLean();
			NotifyTackleEnded();
			Exit();
		}
		break;
	}

	AdvanceTacklers(new_state);
}

void RUActionTacklee::AdvanceTacklers( TackleState state )
{
	for ( Tacklers::iterator I = tacklers.begin(); I != tacklers.end(); ++I )
	{
		// Advance the state
		RUActionTackler* tackler_action = (*I)->GetActionManager()->GetAction<RUActionTackler>();
		if ( tackler_action )
		{
			tackler_action->RequestTransition(state);
			tackler_action->UpdateState(false);
		}
	}
}

bool RUActionTacklee::CanTackleAgain()
{
	// If we're not running, we can tackle
	if ( !IsRunning() )
	{
		return true;
	}

	// Don't allow contested tackles to be overridden.
	if ( tackle_result.tackle_result == TRT_CONTESTED )
	{
		return false;
	}

	/*#rc3_legacy
	// Only allow ankle taps to be overridden if the tacklee is upright.
	NMMabAnimationNetwork* animation_network = player->GetComponent<NMMabAnimationNetwork>();
	bool is_upright = animation_network->IsInDurationEvent(ERugbyAnimEvent::UPRIGHT_DURATION_EVENT);*/
	bool is_upright = m_pPlayer->IsInAnimDurationEvent(ERugbyAnimEvent::UPRIGHT_DURATION_EVENT);
	if ( tackle_result.tackle_result == TRT_ANKLE_TAP2 && !is_upright )
	{
		return false;
	}

	// If he's breaking out, we can be tackled again
	if ( broken_out )
	{
		MABLOGDEBUGTACKLE("CanTackleAgain: Broken out!");
		return true;
	}

	// If the last tackle was some time ago then allow them to be tackled again
	const static int MAX_TACKLE_RENTRANTS = 3;
	if ( tackler_count_since_start >= MAX_TACKLE_RENTRANTS ) {
		return false;
	}

	/// If the last tackler hasn't contacted yet then we can't either
	if ( !has_contacted_this_tackle )
		return false;

	// If the current tackle is a try tackle then no
	// GGS SRA: Altering this check due to the change of how injuries are handled
	if ( tackle_result.is_try_tackle || tackle_result.is_video_ref /*|| tackle_result.IsPlayerInjured(tackle_result.tacklee)*/ )
	{
		MABLOGDEBUGTACKLE("CanTackleAgain: Injured!");
		return false;
	}

	// TYRONE : We allow secondary/subsequent tackles to occur if the tackle is upright
	if ( tackle_completed_successfully && !is_upright )
		return false;

	if ( notified_breakdown_start )
		return false;

	// Can't tackle again if a strip is in progress
	if ( ball_stripped || current_state == TS_TRANSITION_TO_STRIP_FAIL )
		return false;

	// If we're standing and being tackled around the ankles or waist, we can be tackled again
	if ( (tackle_result.body_position[0] == TACKLE_BODY_POSITION::TBP_ANKLE || tackle_result.body_position[0] == TACKLE_BODY_POSITION::TBP_WAIST) && !on_ground )
		return true;

	if ( tackle_result.tackle_result != TRT_SIDESTEP )
	{
		return true;
	}

	return false;
}

void RUActionTacklee::DetachTacklers( bool release_immediately )
{
	MABLOGDEBUGTACKLE( "(%d,%d) Detaching tacklers and %sreleasing immediately!", tackle_result.tackle_determination_index, m_pPlayer->GetAttributes()->GetIndex(), release_immediately ? "" : "*NOT* " );
	for ( Tacklers::iterator I = tacklers.begin(); I != tacklers.end(); ++I )
	{
		RUActionTackler* action = (*I)->GetActionManager()->GetAction<RUActionTackler>();
		action->ContinueDetached(release_immediately);
	}
	tacklers.clear();
}

// Determines if the tacklee can offload the ball at this point in time
bool RUActionTacklee::CanOffloadNow( int dir )
{
	if ( m_pGame->GetGameState()->GetBallHolder() != m_pPlayer )
		return false;

	if ( !IsRunning() )
		return true;

	if ( notified_breakdown_start )
		return false;

	/// TYRONE : Seems some tackle are still allowing offloads very late or other conditions are allowing it - so abort them here
	if ( has_contacted_ground )
		return false;

	/// This only applies in rugby league - no concept of held in rugby
	#if defined RUGBY_LEAGUE_GAME
	if ( notified_tackle_successful_update_count > 0 )
		return false;
	#endif

	if ( tackle_result.drop_ball_type != TDT_NONE )
		return false;

	if ( this->drop_type != DT_NONE )
		return false;

	// If the player is on the ground we should not release the ball
	//if (player->GetAnimation()->IsPlayerOnGround())
	//	return false;

	if (tackle_result.tackle_result == TRT_CONTESTED || tackle_result.tackle_result == TRT_HEAD_HIGH2 )
		return false;

	// Can't offload if we're injured
	if (tackle_result.IsPlayerInjured( m_pPlayer ))
		return false;

	// Check that offloads havEn't been disabled in game settings
	if ( !m_pGame->GetGameSettings().game_settings.custom_rule_offloads )
		return false;

	// We are allowed to offload either while we are in the tackle or we are in the impact
	// provided can_offload is true and we are not on the ground

	/// This only applies in rugby league - no concept of held in rugby
	#if defined RUGBY_LEAGUE_GAME
	return (!notified_tackle_ended) &&
		   (
			IsBreakingOut()
			||
			((current_state == TS_PRE_TACKLE || current_state == TS_START_TACKLE || current_state == TS_IN_TACKLE_STANDING || current_state == TS_TRANSITION_TO_STRIP_SUCCESS || current_state == TS_TRANSITION_TO_STRIP_FAIL || current_state == TS_TRANSITION_TO_STRIP_PARTIAL ) && tackle_result.can_offload)
		   );
	#else

	OFFLOAD_META meta;
	GetOffloadMeta( meta );

	bool can_off_load_in_requested_dir =
		/* Left  */  (dir == 1  &&  meta.can_do[OA_LEFT] ) ||
		/* Right */  (dir == -1 &&  meta.can_do[OA_RIGHT]) ||
		/* Either */ (dir == 0  && (meta.can_do[OA_LEFT]   || meta.can_do[OA_RIGHT]) );

	return can_off_load_in_requested_dir;

	#endif
}

/// Controls on whether someone can offload or not and probability of success
/// See P:\RU\Reference\Doc\Offloadprobs.xlsx
void RUActionTacklee::GetOffloadMeta( OFFLOAD_META& meta )
{
	meta.tacklee = m_pPlayer;
	meta.can_do[OA_LEFT]		= meta.can_do[OA_RIGHT]		= meta.can_do[OA_BOTH]		= false;
	meta.difficulty[OA_LEFT]	= meta.difficulty[OA_RIGHT] = meta.difficulty[OA_BOTH]	= 0.0f;

	// Base conditions if tackle is not running - then we cannot offload (offload means pass in a tackle)
	if ( !IsRunning() )
	{
		wwNETWORK_TRACE_JG("RUActionTacklee::GetOffloadMeta They Running");
		return;
	}

	// If we are still blending in from locomotion then we can offload
	if ( m_pPlayer->GetAnimation()->HasAnyLocomotionOrIdle() || current_state == TS_PRE_TACKLE || current_state == TS_PRE_TACKLE )
	{
		meta.can_do[OA_LEFT]		= meta.can_do[OA_RIGHT]		= meta.can_do[OA_BOTH]		= true;
		meta.difficulty[OA_LEFT]	= meta.difficulty[OA_RIGHT] = meta.difficulty[OA_BOTH]	= 0.0f;

		wwNETWORK_TRACE_JG("RUActionTacklee::GetOffloadMeta Pass Free %d | %d", current_state, m_pPlayer->GetAnimation()->HasAnyLocomotionOrIdle());
		return;
	}


	bool left_arm_free = false;
	bool right_arm_free = false;
	GetFreeArms(left_arm_free, right_arm_free);

	// Find out what the players standing/falling state is
	const bool is_upright = m_pPlayer->IsInAnimDurationEvent(ERugbyAnimEvent::UPRIGHT_DURATION_EVENT);
	const bool is_falling = m_pPlayer->IsInAnimDurationEvent(ERugbyAnimEvent::FALLING_DURATION_EVENT);
	const bool is_on_ground = m_pPlayer->IsInAnimDurationEvent(ERugbyAnimEvent::ON_GROUND_DURATION_EVENT)  || m_pPlayer->GetAnimation()->GetStateMachine().IsPartOfCurrentState( "ground" );

	// Get the players offload ability
	const float offload_ability = m_pPlayer->GetAttributes()->GetOffloadAbility();
	constexpr const float MIN_OFFLOAD_ABILITY_FALLING_ONE_ARM	= 0.75f;
	constexpr const float MIN_OFFLOAD_ABILITY_FALLING_BOTH_ARMS	= 0.50f;

	wwNETWORK_TRACE_JG("RUActionTacklee::GetOffloadMeta upright: %d, falling: %d, on_ground: %d, left_arm_free: %d, right_arm_free: %d, offload_ability: %f",
		is_upright, is_falling, is_on_ground, left_arm_free, right_arm_free, offload_ability);

	if ( is_upright )
	{
		meta.can_do[OA_LEFT]		= left_arm_free;
		meta.can_do[OA_RIGHT]		= right_arm_free;
		meta.can_do[OA_BOTH]		= left_arm_free && right_arm_free;
		meta.difficulty[OA_LEFT]	= 0.4f;
		meta.difficulty[OA_RIGHT]	= 0.4f;
		meta.difficulty[OA_BOTH]	= 0.2f;
	}
	else if ( is_falling )
	{
		meta.can_do[OA_LEFT]		= left_arm_free  && offload_ability >= MIN_OFFLOAD_ABILITY_FALLING_ONE_ARM;
		meta.can_do[OA_RIGHT]		= right_arm_free && offload_ability >= MIN_OFFLOAD_ABILITY_FALLING_ONE_ARM;
		meta.can_do[OA_BOTH]		= left_arm_free && right_arm_free && offload_ability >= MIN_OFFLOAD_ABILITY_FALLING_BOTH_ARMS;
		meta.difficulty[OA_LEFT]	= 0.6f;
		meta.difficulty[OA_RIGHT]	= 0.6f;
		meta.difficulty[OA_BOTH]	= 0.3f;
		/// On ground conditions
	}
	else if ( is_on_ground )
	{
		meta.can_do[OA_LEFT]		= false;
		meta.can_do[OA_RIGHT]		= false;
		meta.can_do[OA_BOTH]		= false; /// TODO : Limit to max time past when duration event first went on
		meta.difficulty[OA_LEFT]	= 0.6f;
		meta.difficulty[OA_RIGHT]	= 0.6f;
		meta.difficulty[OA_BOTH]	= 0.3f;
	}
	else
	{
		// No tracks for upright/onground/falling - so assume
	}

	meta.upright = is_upright;
	meta.falling = is_falling;
	meta.on_ground = is_on_ground;

	//MABASSERT( upright || falling || onground );

	/// Check to make sure that there is meta data in the tackle anim
	#ifdef ENABLE_APP_ASSERTS
	bool duration_event_available = upright || falling || on_ground;
	if ( !duration_event_available )
	{
		/*#rc3_legacy
		NMMabAnimationNetwork* network = player->GetComponent<NMMabAnimationNetwork>();
		const Vector< Animation* >&animations = network->GetNetwork()->getAnimations();
		bool found = false;
		for( size_t i = 0; i < animations.size(); i++ )
		{
			Animation* anim = animations[i];
			const char* anim_name_token = strrchr( anim->name(), '|');
			if ( anim_name_token && strncmp( anim_name_token + 1, "te", 2 ) == 0 )
			{
				found = true;
				//MABASSERTMSG( duration_event_available, MabString( 128, "No upright, falling or onground duration event for tackle anim:\n%s", anim_name_token + 1 ).c_str() );
			}
		}

		if ( !found )
		{
			//MABASSERT( duration_event_available );
		}
		bool DO_IT = false;
		if ( DO_IT )
		{
			SIFApplication::GetApplication()->GetAppTime()->GetSimulationTimeSource()->Pause( true );
		}
		*/
	}
	#endif
}


void RUActionTacklee::GetFreeArms(bool& left_arm_free, bool& right_arm_free)
{
	left_arm_free = false;
	right_arm_free = false;

	if (m_pPlayer && m_pPlayer->GetAnimation() && m_pPlayer->GetAnimation()->GetStateMachine().GetSMTackle())
	{
		m_pPlayer->GetAnimation()->GetStateMachine().GetSMTackle()->TackleeGetFreeArms(left_arm_free, right_arm_free);
	}

	//constexpr const float FREE_ARMS_TRIM_LENGTH = 0.3f;

	// the animation for offloading takes a little while to play so the last
	// FREE_ARMS_TRIM_LENGTH seconds of free armness are discounted as not being avialable

	//player->GetAnimation()->GetAnimationLibrary()->GetDurationEventTime(record.m_pAnimSequence, LEFT_ARM_FREE, eventStartTime, eventEndTime);//Touching_Down

	//// Find out what arms are presently free (without the trimming)
	//const Animation* left_arm_free_animation = network->FindAnimationInDurationEvent( LEFT_ARM_FREE );
	//const Animation* right_arm_free_animation = network->FindAnimationInDurationEvent( RIGHT_ARM_FREE );

	//bool local_left_arm_free = false;
	//bool local_right_arm_free = false;


	//// check the events in this anim to see if the current time is within the trim length
	//if (left_arm_free_animation)
	//{
	//	const EventTrack* left_arm_event_track = left_arm_free_animation->findEventTrackByName(LEFT_ARM_FREE);
	//	int num_left_arm_free_events = left_arm_event_track->getNumEvents();
	//	for (int i = 0; i < num_left_arm_free_events; ++i)
	//	{
	//		if (left_arm_free_animation->previousPhase() < left_arm_event_track->getEvent(i)->start())
	//			continue;
	//		if (left_arm_free_animation->previousPhase() <
	//			left_arm_event_track->getEvent(i)->start() + left_arm_event_track->getEvent(i)->length() - FREE_ARMS_TRIM_LENGTH)
	//		{
	//			local_left_arm_free = true;
	//			break;
	//		}
	//	}
	//}


	//// check the events in this anim to see if the current time is within the trim length
	//if (right_arm_free_animation)
	//{
	//	const EventTrack* right_arm_event_track = right_arm_free_animation->findEventTrackByName(RIGHT_ARM_FREE);
	//	int num_right_arm_free_events = right_arm_event_track->getNumEvents();
	//	for (int i = 0; i < num_right_arm_free_events; ++i)
	//	{
	//		if (right_arm_free_animation->previousPhase() < right_arm_event_track->getEvent(i)->start())
	//			continue;
	//		if (right_arm_free_animation->previousPhase() <
	//			right_arm_event_track->getEvent(i)->start() + right_arm_event_track->getEvent(i)->length() - FREE_ARMS_TRIM_LENGTH)
	//		{
	//			local_right_arm_free = true;
	//			break;
	//		}
	//	}
	//}

	//// switch hands for mirrored anims
	//left_arm_free = (local_left_arm_free && !left_arm_free_animation->mirrored()) || (local_right_arm_free && right_arm_free_animation->mirrored());
	//right_arm_free = (local_right_arm_free && !right_arm_free_animation->mirrored()) || (local_left_arm_free && left_arm_free_animation->mirrored());

}



// Is the tackler breaking out of the current tackle
bool RUActionTacklee::IsBreakingOut()
{
	return IsRunning() && (current_state == TS_TRANSITION_TO_BREAKOUT || (!tackle_completed_successfully && !tackle_result.taken_down));
}

void RUActionTacklee::OnTackleSuccessful( float delay_amount )
{
	if ( tackle_result.drop_ball_type != TDT_NONE )
		return;

	MABLOGDEBUGTACKLE( "(%d,%d) SUCCESSFUL Tackle called", tackle_result.tackle_determination_index, m_pPlayer->GetAttributes()->GetIndex() );

	if ( !pending_successful_notification )
	{
		MABLOGDEBUGTACKLE( "(%d,%d) Notification setup Tackle called", tackle_result.tackle_determination_index, m_pPlayer->GetAttributes()->GetIndex() );
		if ( !IsPrudentToWaitForSuccessfulNotification() )
		{
			notify_success_timer.Reset( m_pGame->GetSimTime(), delay_amount );
			MABLOGDEBUGTACKLE( "(%d,%d) notify_success_timer.Reset", tackle_result.tackle_determination_index, m_pPlayer->GetAttributes()->GetIndex() );
			pending_successful_notification = true;
		}
	}
	else
	{
		MABLOGDEBUGTACKLE( "(%d,%d) Unable to setup notification - Notification already pending", tackle_result.tackle_determination_index, m_pPlayer->GetAttributes()->GetIndex() );
	}

	tackle_completed_successfully = true;
}

void RUActionTacklee::UpdateTackleSuccess()
{
	// The ball is not detached at the end of video ref tackles, even though
	// some tackles have the tacklee losing the ball. If the tacklee gets back
	// up then the ball lerps back into his hands, so end the tackle a little early
	// in that case with the code below.
	const float TRY_VIDEO_REF_EARLY_EXIT = 1.0f;
	bool video_ref_early_exit = false;
	if( tackle_result.is_video_ref )
	{
		video_ref_early_exit = exit_anim_time_remaining - TRY_VIDEO_REF_EARLY_EXIT <= 0.0f;
	}

	if ( !pending_successful_notification && !video_ref_early_exit )
		return;

	if ( (notify_success_timer.GetNumTimerEventsRaised() > 0 && !notified_pending_suspension) || video_ref_early_exit )
	{
		NotifyTackleSuccessful( video_ref_early_exit );
		notify_success_timer.SetEnabled(false);
	}
}

bool RUActionTacklee::NotifyTackleSuccessful( bool video_ref_exit )
{
	MABASSERT( tackle_completed_successfully );

	SIFGameWorld *game = m_pPlayer->GetGameWorld();
	RUGameState *game_state = game->GetGameState();

	// Want to start a play of the ball a little after this happens
	if ( (game_state->GetBallHolder() == m_pPlayer
		&& tackle_completed_successfully
		&& notified_tackle_successful_update_count == 0
		&& tackle_result.drop_ball_type == TDT_NONE
		//&& !(game_state->GetAttackingTeam()->GetAggregateStrategyManager()->GetAggregateStrategy( AGG_PLAYTHEBALL_STRATEGY )->IsRunning() ||
		//	 game_state->GetDefendingTeam()->GetAggregateStrategyManager()->GetAggregateStrategy( AGG_PLAYTHEBALL_STRATEGY )->IsRunning())
			 )
			 || video_ref_exit
		)
	{
		MABLOGDEBUGTACKLE( "(%d,%d) SUCCESSFUL Tackle notification", tackle_result.tackle_determination_index, m_pPlayer->GetAttributes()->GetIndex() );

		for( int i = 0; i < tackle_result.n_tacklers; i++ )
		{
			if ( tackle_result.IsTacklerSuspended( i ) )
			{
				/// Disabling suspension notification from here, moved to penalty cutscene flow. RobH
				//game_state->NotifySuspension( tackle_result.tacklers[i], tackle_result.tacklers_suspended[i] );

				notified_pending_suspension = true;
			}
		}

		// Stop a pass action if it is running
		RUActionPass* pass_action = m_pPlayer->GetActionManager()->GetAction<RUActionPass>();
		if( pass_action != NULL )
		{
			if ( pass_action->IsRunning() )
				pass_action->Exit();
		}
		game_state->Held( tackle_result );

		game->GetEvents()->save_tackle_replay();

		//NotifyStatsTacklersSucceeded( tackle_result );
		//GETFIELD()->NotifyTackle( tackle_result.tackler_pids[0], tackle_result.tacklee_pid );
		notified_tackle_successful_update_count++;
		pending_successful_notification = false;
		time_at_last_successful_notify = game->GetSimTime()->GetAbsoluteTime().ToSeconds();
		return true;
	}

	return false;
}

bool RUActionTacklee::NotifyTackleEnded()
{
	// For the achievement THE CATT MEMORIAL we need to notified the end of the tackle
	if ( !tackle_result.successful && m_pPlayer->GetGameWorld()->GetGameState()->GetBallHolder() == m_pPlayer && m_pPlayer->GetAttributes()->GetTeam()->GetHumanPlayer(0) != NULL )
	{
		m_pGame->GetEvents()->tackle_missed (tackle_result);
		return false;
	}

	if ( m_pPlayer->GetGameWorld()->GetGameState()->GetBallHolder() == m_pPlayer && tackle_completed_successfully && !notified_tackle_ended )
	{
		//GGS SRA: Adding in this check so the game does not immediately end the half in overtime after a tackle in the try zone
		if (m_pGame->GetGameState()->GetPhase() != TRY_REACTION)
		{
			m_pGame->GetEvents()->tackle_ended(tackle_result);
		}
		notified_tackle_ended = true;
		return true;
	}
	return false;
}

// This is called either as a result of the human pressing the
// strip ball button(s) in a tackle or if the computer is trying to strip the ball from the tackle
bool RUActionTacklee::AttemptStripBall()
{
//	if ( RURuleControlUnit::IsPlaySuspended() )
//		return false;

	if ( !IsRunning() )
		return false;

	// Can't attempt a strip a minimum amount of time before the tackle starts
	MabTime time_since_start = m_pGame->GetSimTime()->GetAbsoluteTime() - time_at_tackle_start;
	const float MIN_TIME_BEFORE_STRIP = 0.2f;
	if ( time_since_start < MIN_TIME_BEFORE_STRIP )
		return false;

	// Can only attempt to strip once
	if ( strip_attempted )
		return false;

	// We can only attempt a strip ball when the
	// tacklee is in the held state and the initial tackle
	// has the can strip ball set to true
	if ( tackle_completed_successfully )
		return false;

	if ( notified_tackle_successful_update_count > 0 )
		return false;

	// If we are not in a standing held state the ignore or at the start
	// We can strip from the start because there is a further check below ensuring that
	// the strip ball anim sequence is present
	if ( current_state != TS_IN_TACKLE_STANDING && current_state != TS_START_TACKLE )
		return false;

	// Make sure the tacklee still has the ball
	if ( m_pPlayer->GetGameWorld()->GetGameState()->GetBallHolder() != m_pPlayer )
		return false;

	if ( m_pPlayer->GetActionManager()->IsActionRunning( ACTION_PASS ) )
		return false;

	// If the tackle result does not allow break outs then ignore
	if ( !(tackle_result.anim_sequences & TAS_STRIP_BALL) )
		return false;

	// See if an strip ball can occur 25% of the time
	strip_attempted = true;
	const float PROB_STRIP = 0.25f;
	int slider_value = 0;
	bool can_strip;

	if ( slider_value == -10 ) {
		// NO ball stripping
		can_strip = false;
	} else if ( slider_value == 10 ) {
		// always strip
		can_strip = true;
	} else {
		// random whether strip can occur
		float probability_strip;
		if ( slider_value == 0 ) {
			probability_strip = PROB_STRIP;
		} else if ( slider_value < 0 ) {
			probability_strip = ((float)(10 + slider_value)) / 10.0f * PROB_STRIP;
		} else { // ( slider_value > 0 )
			probability_strip = PROB_STRIP + ((float)slider_value) / 10.0f * ( 1.0f - PROB_STRIP );
		}
		can_strip = m_pGame->GetRNG()->RAND_CALL(float) < probability_strip;
	}

	MABLOGDEBUGTACKLE( "(%d,%d) RUActionTacklee::AttemptStripBall() ********* STRIPBALL", tackle_result.tackle_determination_index, m_pPlayer->GetAttributes()->GetIndex() );

	// Do the strip
	if ( can_strip )
	{
		// Do not allow a strip if we cannot change possession
		const float PROB_FULL_SUCCESS = 0.7f;

		// random whether strip is fully or partially successful
		float probability_full_success;
		if ( slider_value == 0 ) {
			probability_full_success = PROB_FULL_SUCCESS;
		} else if ( slider_value < 0 ) {
			probability_full_success = ((float)(10 + slider_value)) / 10.0f * PROB_FULL_SUCCESS;
		} else { // ( slider_value > 0 )
			probability_full_success = PROB_FULL_SUCCESS + ((float)slider_value) / 10.0f * ( 1.0f - PROB_FULL_SUCCESS );
		}

		bool full_success = m_pGame->GetRNG()->RAND_CALL(float) < probability_full_success;

		if ( !full_success )
		{
			RequestTransition( TS_TRANSITION_TO_STRIP_PARTIAL );
		}
		else
		{
			RequestTransition( TS_TRANSITION_TO_STRIP_SUCCESS );
		}
		ball_stripped = true;
	}
	else
	{
		RequestTransition( TS_TRANSITION_TO_STRIP_FAIL );
	}

	return can_strip;
}

// This is called either as a result of the human pressing the
// aggression button in a tackle or if the computer is trying to break out of a tackle
bool RUActionTacklee::AttemptBreakOut()
{
	MABLOGDEBUGTACKLE( "Breakout Attempt" );

	// We can only attempt a break out when the
	// tacklee is in the held state and the initial tackle
	// has the can break out set to true
	if ( !tackle_completed_successfully )
		return false;

	//if ( notified_tackle_successful_update_count > 0 )
	//	return false;

	// If we are not in a standing held state the ignore
	if ( current_state != TS_IN_TACKLE_STANDING )
		return false;

	// If the tackle result does not allow break outs then ignore
	if ( !(tackle_result.anim_sequences & TAS_BREAK_OUT) )
		return false;

	// if a tackler is cancelled for some reason, we cant have the tacklee trying to attempt a breakout
	if ( tacklers.size() == 0 )
		return false;

	#define ENABLE_AUTO_BREAKOUT
	#ifndef ENABLE_AUTO_BREAKOUT
	// See if the current aggression values allow a breakout to occur
	const float BREAKOUT_THRESHHOLD = 0.3f;

	ARugbyCharacter* primary_tackler = tackle_result.tacklers[0];

	if ( (player->GetAttributes()->GetAggression() - primary_tackler->GetAttributes()->GetAggression()) < BREAKOUT_THRESHHOLD )
		return false;
	#endif

	MABLOGDEBUGTACKLE( "(%d,%d) RUActionTacklee::AttemptBreakOut() ********* BREAKOUT", tackle_result.tackle_determination_index, m_pPlayer->GetAttributes()->GetIndex() );

	// Do the breakout
	RequestTransition( TS_TRANSITION_TO_BREAKOUT );
	return true;
}

bool RUActionTacklee::CanPassNow()
{
	if ( !IsRunning() )
		return true;

	// GGS SRA: Removing this check due to the change of how injuries are handled
	//if ( tackle_result.IsPlayerInjured(m_pPlayer) )
	//	return false;

	if ( m_pPlayer->GetGameWorld()->GetGameState()->GetBallHolder() != m_pPlayer )
		return false;

	/// This only applies in rugby league - no concept of held in rugby
	#if defined RUGBY_LEAGUE_GAME
	if ( notified_tackle_successful_update_count > 0 )
		return false;
	#endif

	if ( tackle_result.drop_ball_type != TDT_NONE )
		return false;

	if ( this->drop_type != DT_NONE )
		return false;

	if ( m_pPlayer->GetHumanPlayer() )
		return true;

	//return (pass_delay_timer.GetNumTimerEventsRaised() > 0);
	return true;
}

/// For the current Action, say whether or not the given Action is allowed to run
bool RUActionTacklee::CanEnterOtherAction( RU_ACTION_INDEX id )
{
//	// If play is suspended then do not allow
//	if ( id == ACTION_TACKLEE && (RURuleControlUnit::IsPlaySuspended() || RURuleControlUnit::GetPendingTrigger() != NULL) )
//		return false;

	// Prevent any tacklers from starting on the same update as a successful notification
	// (This will result in 2 PTB's happening)
	/// REMOVED AS BELIVED NO LONGER RELEVANT FOR RU AND WAS PREVENTING IM SOME CASES LEGITIMTAE TACKLES
	//if ( id == ACTION_TACKLEE &&
	//	 notified_tackle_successful_update_count == 1 &&
	//	((game->GetSimTime()->GetAbsoluteTime() - time_at_last_successful_notify) < (SIMULATION_INTERVAL * 2.0f)) )
	//{
	//	return false;
	//}

	const static float MIN_SIDESTEP_TACKLE_DIST = 0.5f;
	if ( !IsRunning() )
	{
		/// Special case for preventing sidesteps from going right through the middle of other players
		if ( m_pPlayer->GetActionManager()->IsActionRunning( ACTION_SIDE_STEP ) && id == ACTION_TACKLEE)
		{
			bool tackler_in_range = false;
			/// Find closest player that can tackle on opposition
			RLP_FILTERPARAMETERS params;
			RLPResultList players;

			params.filters = RLP_FILTER_TEAM | RLP_FILTER_MAX_DIST;
			params.team = (RUTeam*)m_pPlayer->GetAttributes()->GetTeam()->GetOppositionTeam();
			params.max_dist = MIN_SIDESTEP_TACKLE_DIST;
			const FVector& player_pos = m_pPlayer->GetMovement()->GetCurrentPosition();
			params.max_dist_x_ref = player_pos.x;
			params.max_dist_z_ref = player_pos.z;

			m_pGame->GetPlayerFilters()->GetPlayerPlayerDistanceSort()->SetReferencePlayer(m_pPlayer);
			m_pPlayer->GetGameWorld()->GetFilteredPlayerList (players, params, m_pGame->GetPlayerFilters()->GetPlayerPlayerDistanceSort() );

			RLPResultList::iterator it;
			for( it = players.begin(); it != players.end(); ++it )
			{
				ARugbyCharacter* test_player = it->player;
				if ( test_player->GetActionManager()->CanUseAction( ACTION_TACKLER ) )
				{
					tackler_in_range = true;
					break;
				}
			}

			return tackler_in_range;
		}

		return true;
	}

	if ( id == ACTION_PASS && !CanPassNow() )
		return false;

	// Can use pass if we can offload
	if ( id == ACTION_PASS && !CanOffloadNow() )
		return false;

	// Allow fends to happen after barreling over or though a player
	if ( id == ACTION_FEND )
	{
		TACKLE_RESULT_TYPE trt = tackle_result.tackle_result;
		return trt == TRT_SIDESTEP && CanTackleAgain();
	}

	// Can use tacklee if player can be tackled again
	if ( id == ACTION_TACKLEE )
	{
		if ( !CanTackleAgain() )
			return false;
	}

	if ( id == ACTION_TRY && ShouldAllowScoreTryToRun()  )
	{
		return true;
	}

	// If we are running then nothing else but a pass or another tackle can happen
	// (Exceptions happen above)
	if ( !(id == ACTION_PASS || id == ACTION_TACKLEE) )
		return false;

	return true;
}

MabString RUActionTacklee::GetOffloadMetaDebugString()
{
//#rc3_legacy
//	char buffer[1024];
//	char minibuf[256];
//
//	memset( buffer, 0, sizeof( buffer ) );
//
//	bool left_arm_free, right_arm_free;
//	GetFreeArms( left_arm_free, right_arm_free );
//
//	MabStringHelper::Sprintf( minibuf, "Left arm free: %s\n", left_arm_free ? "true" : "false" );
//	MabStringHelper::Strcat( buffer, minibuf );
//
//	MabStringHelper::Sprintf( minibuf, "Right arm free: %s\n", right_arm_free ? "true" : "false" );
//	MabStringHelper::Strcat( buffer, minibuf );
//
//	bool upright  = network->IsInDurationEvent( PLAYER_UPRIGHT );
//	bool falling  = network->IsInDurationEvent( PLAYER_FALLING );
//	bool onground = network->IsInDurationEvent( PLAYER_ONGROUND ) || network->IsPartOfCurrentState( "ground" );
//
//	MabStringHelper::Sprintf( minibuf, "Is upright: %s\n", upright ? "true" : "false" );
//	MabStringHelper::Strcat( buffer, minibuf );
//
//	MabStringHelper::Sprintf( minibuf, "Is falling: %s\n", falling ? "true" : "false" );
//	MabStringHelper::Strcat( buffer, minibuf );
//
//	MabStringHelper::Sprintf( minibuf, "Is on ground: %s\n", onground ? "true" : "false" );
//	MabStringHelper::Strcat( buffer, minibuf );
//
//	return buffer;
	return "";
}

void RUActionTacklee::DropBall(float drop_time_in_frames, ARugbyCharacter* player, DROP_TYPE type )
{
	this->drop_time = drop_time_in_frames;
	this->drop_player = player;
	this->drop_type = type;
}

void RUActionTacklee::UpdateDropBall(float delta_time)
{
	if ( drop_type != DT_NONE )
	{
		if ( drop_time > 0.0f )
		{
			drop_time -= delta_time; // Decrement frames till drop
		}
		else if ( m_pPlayer->GetGameWorld()->GetGameState()->GetBallHolder() == m_pPlayer )
		{
			DoDropBall();
		}
	}
}

void RUActionTacklee::DoDropBall()
{
	// If play is suspended from try or tmo, then skip this
	if ( m_pGame->GetRules()->IsPlaySuspended() )
		return;

	//// if we are doing a fast PTB, or have kicked into PTB, then we must not drop the ball
	//if ( player->GetRole() && player->GetRole()->RTTSubClassOf( RURolePTBPlayer::RTTGetStaticType() ) )
	//{
	//	return;
	//}

	MABASSERT( drop_type != DT_NONE );

	RUGameState *game_state = m_pGame->GetGameState();
	RUPlayerMovement *movement = m_pPlayer->GetMovement();

	//-------------------------------------------------------------
	// Handle drop ball from a tackle
	//-------------------------------------------------------------

	if ( this->drop_type == DT_FROM_TACKLE )
	{
		MABASSERT(game_state->GetBallHolder() == m_pPlayer);
		// Call a knock on on the ball appropriate for the drop ball type
		game_state->SetBallHolder( NULL );

		// Determine which way the ball will be knocked on and how far
		FVector current_head_position = movement->GetCurrentPosition();
		FVector momentum_vec = m_pPlayer->GetMovement()->GetCurrentVelocity();

		float lose_ball_angle = movement->GetCurrentFacingAngle();

		switch( tackle_result.drop_ball_type )
		{
		case TDT_AT_IMPACT:
		case TDT_RANDOM:
			{
				bool lost_left = false, lost_right = false, lost_forward = false, lost_back = false;
				if ( tackle_result.tackled_from_direction[0] == TFD_FRONT ) {
					if ( tackle_result.body_position[0] == TACKLE_BODY_POSITION::TBP_WAIST ) {
						if ( m_pGame->GetRNG()->RAND_CALL(float) < 0.5f )
							lost_left = true;
						else
							lost_right = true;
					} else {
						if ( m_pGame->GetRNG()->RAND_CALL(float) < 0.7f )
							lost_back = true;
						else
							lost_forward = true;
					}
				} else if ( tackle_result.tackled_from_direction[0] == TFD_BEHIND ) {
					lost_forward = true;
				} else if ( tackle_result.tackled_from_direction[0] == TFD_SIDE_LEFT ) {
					lost_right = true;
				} else if ( tackle_result.tackled_from_direction[0] == TFD_SIDE_RIGHT ) {
					lost_left = true;
				} else {
					lost_forward = true;
				}

				// Now work out the angle
				float add_angle = 0.0f;
				if ( lost_left )    add_angle  =  (MabMath::Deg2Rad( 45.0f ) + MabMath::Deg2Rad( m_pGame->GetRNG()->RAND_RANGED_CALL(float, 90.0f ) ) );
				if ( lost_right )   add_angle  = -(MabMath::Deg2Rad( 45.0f ) + MabMath::Deg2Rad( m_pGame->GetRNG()->RAND_RANGED_CALL(float, 90.0f ) ) );
				if ( lost_forward ) add_angle  = MabMath::Deg2Rad( m_pGame->GetRNG()->RAND_RANGED_CALL(float, 90.0f ) ) - MabMath::Deg2Rad( 45.0f );
				if ( lost_back )    add_angle  = MabMath::Deg2Rad( m_pGame->GetRNG()->RAND_RANGED_CALL(float, 180.0f - 45.0f ) ) + MabMath::Deg2Rad( m_pGame->GetRNG()->RAND_RANGED_CALL(float, 90.0f ) );

				lose_ball_angle = movement->GetCurrentFacingAngle() + add_angle;
			}
			break;
		case TDT_AT_HIT_GROUND:
			{
				// Work out the momentum angle
				lose_ball_angle = SSMath::CalculateAngle( momentum_vec );
				lose_ball_angle += (-MabMath::Deg2Rad( 22.5f ) + MabMath::Deg2Rad( m_pGame->GetRNG()->RAND_RANGED_CALL(float, 45.0f ) ) );
			}
			break;
		default:
			break;
		}

		game_state->HandlingError( m_pPlayer, true, lose_ball_angle, 0.5f );

		//-------------------------------------------------------------
		// Handle strip ball "drop"
		//-------------------------------------------------------------
	}
	else if ( this->drop_type == DT_FROM_STRIP )
	{

		// TYRONE : If the bal has been stripped to ground - then we assume
		// that it must pass through the strippers hands so set this state.
		// This ensures that the tacke count gets reset
		if ( drop_player == NULL )
			game_state->SetBallHolder( tackle_result.tacklers[0] );

		// Set the ball holder pid to the indicated pid
		game_state->SetBallHolder( drop_player );

		// Notify the strip when the ball gets dropped - not at the start of the strip (like it was previously)
		// Have split this logic out below into drop back/forward
		// DoDropBall is called for partial success with drop_player == null
		// it is also called for success with drop_player == tackler
		// the success and fail strip results are captured with the state transition, so we only capture the partial cases here

		// If we drop the ball then knock it on
		if ( drop_player == NULL )
		{
			float angle = m_pGame->GetRNG()->RAND_RANGED_CALL(float, 2 * PI );

			if ( angle >= 0.5f * PI && angle <= 1.5f * PI )
			{
				// lost backwards, since angle is rotation applied to upfield vector
				game_state->StripResult( tackle_result, tackle_result.tacklers[0], SR_DROP_BACK );
			}
			else
			{
				// lost forwards
				game_state->StripResult( tackle_result, tackle_result.tacklers[0], SR_DROP_FORWARD );
			}

			// TODO: Need to detect when a strip results in a penalty and send this result to StripResult

			game_state->HandlingError( m_pPlayer, true, angle, 0.5f );
		}
		DetachTacklers( true );
	}
}

void RUActionTacklee::AbortConflictingActions()
{
	for ( int i = 0; i < ACTION_LAST; i++ )
	{
		// Abort all but the pass behaviours
		if ( i == ACTION_PASS )
			continue;

		/// Kicks can continue up until the point of contact
		if ( i == ACTION_KICK )
			continue;

		// Do not abort any other actions if the type has no impact on the tacklee
		if ( tackle_result.tackle_result == TRT_TRY && tackle_result.try_tackle_type == TRY_TACKLE_TYPE::TTT_JUMPOVER )
			continue;

		// Don't abort try behaviours
		RUAction* action = m_pPlayer->GetActionManager()->GetAction( (RU_ACTION_INDEX)i );
		if ( action && action->IsRunning() )
			action->Exit();
	}
}

void RUActionTacklee::UpdateKnockedPass()
{
	if ( knocked_pass_timer.GetNumTimerEventsRaised() > 0 )
	{
		if ( m_pPlayer->GetActionManager()->IsActionRunning( ACTION_PASS ) )
		{

			RUActionPass* pass_action = m_pPlayer->GetActionManager()->GetAction<RUActionPass>();
			ARugbyCharacter* primary_tackler = tackle_result.tacklers[0];

			if ( primary_tackler ) {
				float tackler_angle = SSMath::CalculateAngle( primary_tackler->GetMovement()->GetCurrentVelocity() );
				float hit_size = tackle_result.actual_tacklee_impetus;
				MabMath::Clamp( hit_size, 0.0f, 1.0f );
				pass_action->InformHit( tackler_angle, hit_size );
			}
		}
		knocked_pass_timer.SetEnabled(false);
	}
}

bool RUActionTacklee::IsTackleCompleted()
{
	return notified_tackle_ended;
}

float RUActionTacklee::GetAnimTimeLeft()
{
	// This is used for try_tackes, which only have an entry
	MABASSERT( IsRunning() && ( current_state == TS_PRE_TACKLE || current_state == TS_START_TACKLE || current_state == TS_TRANSITION_TO_GROUND || current_state == TS_IN_TACKLE_ON_GROUND ) );
	return exit_anim_time_remaining.ToSeconds();
}

bool RUActionTacklee::IsPrudentToWaitForSuccessfulNotification()
{
	RUPlayerMovement *movement = m_pPlayer->GetMovement();
	RUPlayerAttributes *attributes = m_pPlayer->GetAttributes();

	// If they might be injured then wait
	// GGS SRA: Removing this check due to the change of how injuries are handled
	//if ( tackle_result.GetTackleInjuryType( m_pPlayer ) != TIT_NONE )
	//	return true;

	// We don't need to stop if the tackle is in it's dying stages
	if ( current_state == TS_RELEASE_STANDING || current_state == TS_RELEASE_GROUND || current_state == TS_TRANSITION_TO_INJURED ||
		 current_state == TS_TRANSITION_TO_INJURED || current_state == TS_INJURED ||
		 current_state == TS_END_TACKLE || current_state == TS_POST_TACKLE )
		 return false;

	// We want to wait till the end of driven and multiman tackles
	if ( tackle_result.tackle_result == TRT_CONTESTED || tackle_result.tackle_result == TRT_MULTI_MAN2 )
		return true;

	// We want to wait if they're going to be thrown to ground
	if ( current_state == TS_IN_TACKLE_STANDING
		|| (current_state == TS_TRANSITION_TO_GROUND
		&& ( tackle_result.anim_sequences & TAS_TAKEN_DOWN )) )
		return true;

	// If the end point of our tackle is going to take us into one of the following situations
	// then delay a larger amount:
	// Close to a try or is a try
	// Close to being taken out
	// Close to being held in goal

	// Work out the current anim end point
	FVector end_point = movement->GetCurrentPosition();

	//NMMabAnimationReference anim_reference;
	//if (current_state < TS_START_TACKLE)
	//	anim_reference = NMMabAnimationRepository::GetAnimationReference(tackle_result.GetTackleeAnimationName().c_str());
	//else
	//	anim_reference = player->GetAnimation()->GetBaseAnimation().seq_0_ref;
	//if (anim_reference.IsValid())
	//{
	//	const NiQuatTransform& anim_final_transform = anim_reference.GetAnimation()->GetAnimation()->GetFinishAccumulatedTransform();
	//	NiQuatTransform final_transform = player->GetAnimation()->GetAnimationBlender()->GetAccumulatedTransform().HierApply(anim_final_transform);
	//	const NiPoint3& position = final_transform.GetTranslate();
	//	end_point = FVector(position.x, 0.0f, position.z);
	//}
	MABLOGDEBUGTACKLE( "(%d,%d) Predicted Tackle EndPoint: %.3f %.3f", tackle_result.tackle_determination_index, attributes->GetIndex(), end_point.x, end_point.z );

	// Now check the end point for the various cases
	FieldExtents try_extents = m_pGame->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	FieldExtents full_extents = m_pGame->GetSpatialHelper()->GetFieldExtents();
	float dist_to_opp_goal = try_extents.y/2.0f - (end_point.z * float(attributes->GetPlayDirection()));
	float dist_to_own_goal = (end_point.z * float(attributes->GetPlayDirection())) + try_extents.y/2.0f;

	if ( dist_to_opp_goal < 2.7f || dist_to_own_goal < 2.7f ||
		 MabMath::Fabs( end_point.x ) > ((try_extents.x/2.0f) - 1.5f ) ||
		 MabMath::Fabs( end_point.z ) > ((full_extents.y/2.0f) - 1.5f ) )
	{
		return true;
	}

	// If the tackle is a try saving/making tackle then we should wait also
	if ( tackle_result.is_try_tackle )
		return true;

	return false;
}

bool RUActionTacklee::ShouldGoToGround()
{
	RUPlayerAttributes *attributes = m_pPlayer->GetAttributes();
	RUPlayerMovement *movement = m_pPlayer->GetMovement();

	// They should not be allowed to go to ground if they are over the goal line
	// (because it is bad for the tackler)
	FieldExtents extents = m_pGame->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	float dist_to_goal = extents.y/2.0f - (movement->GetCurrentPosition().z * float(attributes->GetPlayDirection()));

	if ( dist_to_goal < 0.2f )
	{
		return false;
	}

	/// Find out who the primary tackler is - if there is none then we do not go to ground
	ARugbyCharacter* primary_tackler = tackle_result.tacklers[0];
	if ( primary_tackler == NULL )
		return false;

	/// Work out what the aggression difference should be if required
	if ( !updated_go_to_ground_agg_diff )
	{
		go_to_ground_agg_diff = 1.0f + m_pGame->GetRNG()->RAND_RANGED_CALL(float, 1.8f );
		updated_go_to_ground_agg_diff = true;
	}

	/// If the tackler has more aggression than we do then
	float aggression_diff = (primary_tackler->GetAttributes()->GetAggression() - attributes->GetAggression());
	if ( aggression_diff > go_to_ground_agg_diff )
		return true;

	return false;
}

bool RUActionTacklee::CanDoFastPTB()
{
	return cando_fastptb && on_ground;
}

float RUActionTacklee::GetDropBallTime( const MabString& animation_name )
{
	MABUNUSED( animation_name );
	const int RELEASE_FRAME = 10;
	return RELEASE_FRAME * SIMULATION_INTERVAL;
}

bool RUActionTacklee::IsTouchingDown()
{
//	const char* TOUCHING_DOWN_DURATION_EVENT = "touching_down";
//	return network->IsInDurationEvent( TOUCHING_DOWN_DURATION_EVENT );	
	return m_pPlayer->IsInAnimDurationEvent(ERugbyAnimEvent::TOUCHING_DOWN_DURATION_EVENT);
}

bool RUActionTacklee::WouldBeBeneficialToSupportTacklee()
{
	return CanOffloadNow();
}

bool RUActionTacklee::CanAwardTry()
{
	return !(current_state == TS_TRANSITION_TO_STRIP_SUCCESS || current_state == TS_TRANSITION_TO_STRIP_FAIL || current_state == TS_TRANSITION_TO_STRIP_PARTIAL );
}

bool RUActionTacklee::ShouldAttemptToTackle()
{
	if ( m_pGame->GetRules()->IsPlaySuspended() )
		return false;

	// if we are not running then return true
	if ( !IsRunning() )
		return true;

	bool can_tackle = CanTackleAgain();

	if ( !can_tackle )
		return false;

	/// AI to determine if we should tackle the ball holder again
	return true;
}

bool RUActionTacklee::ShouldAllowScoreTryToRun()
{
	// If the player is breaking out then allow a try score
	if ( current_state  == TS_TRANSITION_TO_BREAKOUT )
		return true;

	return false;
}

void RUActionTacklee::AnimationEvent(float /*time*/, ERugbyAnimEvent event, size_t /*userdata*/, bool /*IsBlendingOut = false*/)
{
	if ( event == ERugbyAnimEvent::ON_GROUND_DURATION_EVENT )
	{
		m_pPlayer->GetGameWorld()->GetEvents()->tacklee_on_ground( tackle_result );
		has_contacted_ground = true;
		SetBreakdownStart();
	}

	else if ( event == ERugbyAnimEvent::TACKLE_CONTACT_EVENT )
	{
		has_contacted = true;
		has_contacted_this_tackle = true;
		if ( m_pPlayer->GetActionManager()->IsActionRunning(ACTION_KICK) )
		{
			m_pPlayer->GetActionManager()->GetAction( ACTION_KICK )->Exit();
		}

		const float DECREASE_STAMINA_TACKLE_IMPACT = 0.035f;
		const float DECREASE_CAPPED_STAMINA_MULT = 0.3f;

		const float MAXIMUM_STAMINA_LOSS_MULTIPLIER = 5.0f;
		const float MINIMUM_STAMINA_LOSS_MULTIPLIER = 0.2f;
		const float BASE_STAMINA_VALUE = 0.60f;

		if (tackle_result.tacklee)
		{
			if (tackle_result.injured_players[0] == tackle_result.tacklee)
			{
				MABLOGDEBUG("Tacklee was injured");
			}

			//GGS SRA: if the tacklee was injured (only person who can be at this point), their stamina was already drained during SetInjuryStatus, so early out
			if (tackle_result.injured_players[0] != tackle_result.tacklee)
			{
				float str = tackle_result.tacklee->GetAttributes()->GetStrength();

				float stamina_difference = str - BASE_STAMINA_VALUE;
				float stamina_loss_multiplier = 1.0f;
				if (stamina_difference > 0)
				{
					stamina_loss_multiplier -= (stamina_difference / (1 - BASE_STAMINA_VALUE)) * (1.0f - MINIMUM_STAMINA_LOSS_MULTIPLIER);
				}
				else if (stamina_difference < 0)
				{
					stamina_loss_multiplier += (-stamina_difference / BASE_STAMINA_VALUE) * (MAXIMUM_STAMINA_LOSS_MULTIPLIER - 1.0f);
				}

				tackle_result.tacklee->GetAttributes()->DecreaseStamina(stamina_loss_multiplier * DECREASE_STAMINA_TACKLE_IMPACT, DECREASE_STAMINA_TACKLE_IMPACT * DECREASE_CAPPED_STAMINA_MULT);
			}
		}

		if (tackle_result.tacklers[0])
		{
			float str = tackle_result.tacklers[0]->GetAttributes()->GetStrength();

			float stamina_difference = str - BASE_STAMINA_VALUE;
			float stamina_loss_multiplier = 1.0f;
			if (stamina_difference > 0)
			{
				stamina_loss_multiplier -= (stamina_difference / (1 - BASE_STAMINA_VALUE)) * (1.0f - MINIMUM_STAMINA_LOSS_MULTIPLIER);
			}
			else if (stamina_difference < 0)
			{
				stamina_loss_multiplier += (-stamina_difference / BASE_STAMINA_VALUE) * (MAXIMUM_STAMINA_LOSS_MULTIPLIER - 1.0f);
			}

			tackle_result.tacklers[0]->GetAttributes()->DecreaseStamina(stamina_loss_multiplier * DECREASE_STAMINA_TACKLE_IMPACT, DECREASE_STAMINA_TACKLE_IMPACT * DECREASE_CAPPED_STAMINA_MULT);
		}

		if (tackle_result.tackle_result == TRT_CONTESTED && !driven_tackle_taken_down)
		{
			const static float TIME_SINCE_LINEOUT = 0.7f;
			RUGameState* game_state = m_pGame->GetGameState();
			bool lineout_just_happened = game_state->GetTimeSinceLineout() < TIME_SINCE_LINEOUT;
			float angle_diff = MabMath::Fabs(MabMath::AngleDelta(m_pPlayer->GetMovement()->GetCurrentFacingAngle(), m_pPlayer->GetAttributes()->GetTeam()->GetPlayAngle()));
			if (angle_diff < CONTESTED_TACKLE_MAX_ANGLE || lineout_just_happened )
				m_pGame->GetGameState()->TackleContested( tackle_result );
			else
				SetDrivenTackleTakenDown(true);
		}

		TriggerImpactRumble( tackle_result );
		UpdateBreakdownPossible();
	}

	else if ( event == ERugbyAnimEvent::TACKLE_RELEASE_BALL_EVENT )
	{
		if ( tackle_result.is_try_tackle && m_pPlayer == m_pGame->GetGameState()->GetBallHolder() )
		{
			// Kill pass action if tackle releases ball first
			// NOTE: Exiting action, causes issues, in RUActionPass::AnimationEvent is registered with listener
			RUActionPass* pass_action = static_cast<RUActionPass*>(m_pPlayer->GetActionManager()->GetAction(ACTION_PASS));
			if ( pass_action->IsRunning() )
				pass_action->Abort();

			drop_type = DT_FROM_TACKLE;
			// If play is suspended from try or tmo, then skip this
			if ( !m_pGame->GetRules()->IsPlaySuspended() )
			{
				DoDropBall();
			}
		}
	}
}

/// Set the tackle ball state if applicable
void RUActionTacklee::SetBreakdownStart( bool /*force*/ )
{
	bool is_ball_holder = m_pPlayer == m_pGame->GetGameState()->GetBallHolder();
	
	// GGS SRA: Altering this check due to the change of how injuries are handled
	//bool injury = tackle_result.IsPlayerInjured( m_pPlayer );

	if ( notified_breakdown_start || !is_ball_holder || !tackle_result.successful /*|| injury*/ )
		return;

	// Make sure the ball is in play?
	ASSBall* ball = m_pGame->GetBall();

	bool in_pass = m_pPlayer->GetActionManager()->IsActionRunning( ACTION_PASS );
	bool ball_dropped = tackle_result.drop_ball_type != TDT_NONE;

	if ( is_ball_holder && !ball_dropped && !in_pass && ball->IsValidRuckPosition() )
	{
		MABLOGDEBUG( "RUActionTacklee::SetBreakdownStart(): %d, ball.y(%f)", m_pPlayer->GetAttributes()->GetIndex(), ball->GetCurrentPosition().y );
		notified_breakdown_start = true;

		// stop players from selecting this player while he lies on the ground under a ruck
		m_lock_manager.HFLock(HF_CHANGE_PLAYER);
		//if (SSHumanPlayer* human = player->GetHumanPlayer())
		//	human->AssignBestPlayer();

		#ifdef ENABLE_OSD
		RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
		MabString debug_str( 64, "Breakdown Start" );
		settings->PushDebugString( game, RUGameDebugSettings::DP_CHANGE_PLAYER, debug_str.c_str() );
		#endif

		//m_pGame->GetGameState()->BreakdownStart( m_pPlayer );
		// We're entering PTB now, lock passing as we've taken the tackle to the ground
		// Without this lock there's a chance you can successfully pass during the PTB transition
		//m_lock_manager.HFLock(HF_PASS);
		m_pGame->GetEvents()->play_the_ball_start( m_pPlayer );
	}
}

void RUActionTacklee::UpdateBreakdownPossible( bool force )
{
	// Notify if a breakdown is possible
	#ifdef ENABLE_OSD
	/*const*/ static int NOTIFICATION_NUM = 0;
	#endif

	wwNETWORK_TRACE_JG("RUActionTacklee::UpdateBreakdownPossible force: %d", force);

	if ( !notified_breakdown_possible )
	{
		const bool falling			= m_pPlayer->IsInAnimDurationEvent(ERugbyAnimEvent::FALLING_DURATION_EVENT);
		const bool onGround			= m_pPlayer->IsInAnimDurationEvent(ERugbyAnimEvent::ON_GROUND_DURATION_EVENT);
		const bool isBallHolder	= m_pPlayer == m_pGame->GetGameState()->GetBallHolder();
		const bool inPass			= m_pPlayer->GetActionManager()->IsActionRunning( ACTION_PASS );

		wwNETWORK_TRACE_JG("RUActionTacklee::UpdateBreakdownPossible falling: %d, on_ground: %d, is_ball_holder: %d, in_pass: %d", force, on_ground, isBallHolder, inPass);

		if ( (isBallHolder && (falling || onGround) && !inPass) || force )
		{
			wwNETWORK_TRACE_JG("RUActionTacklee::UpdateBreakdownPossible(), breakdown possible %d", m_pPlayer->GetAttributes()->GetIndex() );
			m_pGame->GetGameState()->BreakdownPossible( m_pPlayer );

			#ifdef ENABLE_OSD
			RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
			settings->PushDebugString( game, RUGameDebugSettings::DP_TACKLE, MabString( 24, "Brkdwn poss: %d", NOTIFICATION_NUM++ ).c_str() );
			#endif

			notified_breakdown_possible = true;
		}
	}
}
