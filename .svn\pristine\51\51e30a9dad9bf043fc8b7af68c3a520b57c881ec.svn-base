// Copyright (c) 2016-2017 Wicked Witch Software Pty. Ltd.

#pragma once

#include "CoreMinimal.h"

// For converting the enums to strings
#include "Runtime/CoreUObject/Public/UObject/UObjectGlobals.h"
#include "Runtime/CoreUObject/Public/UObject/Package.h"

#include "RugbyEnums.generated.h"

#define stringify(name) # name
#define ENUM_TO_FSTRING(Enum, Val)  EnumToString( (stringify(Enum)) , Val)
#define ENUM_TO_CHAR(Enum, Val)  TCHAR_TO_ANSI(*EnumToString( (stringify(Enum)) , Val))

#define NAME_TO_ENUM(EnumType, ValueName) GetEnumFromName<EnumType>( stringify(EnumType), ValueName)

//===============================================================================
//===============================================================================
template <class T>
inline FString EnumToString(const FString& enumName, T value)
{
	const UEnum* pEnum = FindObject<UEnum>(ANY_PACKAGE, *enumName, true);
	if (pEnum == nullptr) return TEXT("null");
	return pEnum->GetNameStringByValue(static_cast<int32>(value));
}

//===============================================================================
//===============================================================================
template <typename EnumType>
static FORCEINLINE EnumType GetEnumFromName(const FString& EnumName, const FName& SearchName)
{
	UEnum* Enum = FindObject<UEnum>(ANY_PACKAGE, *EnumName, true);
	int32 enumIndex = INDEX_NONE;
	if (Enum)
	{
		// Find index of enum first, attempting to get value directly can result in getting INDEX_NONE returned as a correct result.
		// Getting the index first and returning the 
		enumIndex = Enum->GetIndexByName(SearchName);
		if (enumIndex != INDEX_NONE)
		{
			return (EnumType)Enum->GetValueByIndex(enumIndex); // convert index to value
		}

		return (EnumType)Enum->GetMaxEnumValue();
	}
	
	checkf(false, TEXT("Failed to find a matching enum value for %s::%s"), *EnumName, *SearchName.ToString());
	return EnumType::MAX; // This particular line of code is bad and could fail very easily if the passed in enum does not have a "MAX" value, thus the hard fail above.
}

//-----  From SSEVDSFormationEnum.h  -----------------------------------------//

UENUM()
enum class ERugbyFormationGamePhase : uint8
{
	STANDARDPLAY,                      // "StandardPlay"
	KICKOFF,                           // "KICKOFF"
	FREEBALL,                          // "FREEBALL"
	SCRUM,                             // "SCRUM"
	CONVERSION,                        // "CONVERSION"
	RUCK,                              // "RUCK"
	LINEOUT_RUCK,                      // "LINEOUT_RUCK"
	LINEOUT_MAUL,                      // "LINEOUT_MAUL"
	LINEOUT,                           // "LINEOUT"
	MAUL,                              // "MAUL"
	DROPOUT,                           // "DROPOUT"
	PENALTY,                           // "PENALTY"
	TEN_M_KICK,                        // "10_M_KICK"
	TEN_M_TAP,                         // "10_M_TAP"
	TUTORIALS,                         // "TUTORIAL_MODE"
	CUTSCENE,                          // "CUTSCENE",
	HALFTIME,                          // "HALFTIME",
	FULLTIME,                          // "FULLTIME",
	PRE_KICKOFF,                       // "PRE-KICKOFF",
	TRY_REACTION,                      // "TRY-REACTION"
	TRY_CUTSCENE,                      // "TRY-CUTSCENE"
	REACTION_CUTSCENE,                 // "REACTION-CUTSCENE"
	PRE_KICKFORPOINTS,                 // "PRE-KICKFORPOINTS"
	PRE_DROPOUT,                       // "PRE-DROPOUT"
	QUICK_TAP_PENALTY,                 // "QUICK_TAP_PENALTY"
	QUICK_LINEOUT,                     // "QUICK_LINEOUT"
	DECIDE_LINEOUT_NUMBERS,            // "DECIDE_LINEOUT_NUMBERS"
	RUGBY_SEVENS_EXTRA_TIME_COIN_TOSS, // "RUGBY_SEVENS_EXTRA_TIME_COIN_TOSS"
	SIMULATION,                        // "SIMULATION",
	PENALTY_SHOOT_FOR_GOAL,            // "PENALTY_SHOOT_FOR_GOAL",
	SETPLAY,						   // "SETPLAY",
	PLAY_THE_BALL,					   // "PLAY_THE_BALL"
};

//-----  From SSEVDSFormationManager.cpp  ------------------------------------//

UENUM()
enum class ERugbyFormationPlayerMask : uint8

{
	ALL,               // "ALL"
	P1,                // "P1"
	P2,                // "P2"
	P3,                // "P3"
	P4,                // "P4"
	P5,                // "P5"
	P6,                // "P6"
	P7,                // "P7"
	P8,                // "P8"
	P9,                // "P9"
	P10,               // "P10"
	P11,               // "P11"
	P12,               // "P12"
	P13,               // "P13"
	//P14,             // "P14"
	//P15,             // "P15"
	FORWARDS,          // "Forwards"
	BACKS,             // "Backs"
	REFEREE,           // "Referee"
	TWOPOD_1,          // "Two-Pod_1"
	TWOPOD_2,          // "Two-Pod_2"
	THREEPOD_1,        // "Three-Pod_1"
	THREEPOD_2,        // "Three-Pod_2"
	THREEPOD_3,        // "Three-Pod_3"
	BACKSTHENFORWARDS, // "Backs then Forwards"
	FORWARDSTHENBACKS, // "Forwards then Backs"
	WING,              // "Winger"
	SETPLAYGTB,              // "Winger"
	BALL_HOLDER,
};

UENUM()
enum class ERugbyFormationTeamMode : uint8
{
	ATTACK,  // "Attack"
	DEFENCE, // "Defence"
	BOTH,    // "Both"
};

UENUM()
enum class ERugbyFormationLineType : uint8
{
	BIASMINSPACING, // "BiasMinSpacing"
	NORMALIZED,     // "Normalized"
	MARKOPPONENT,   // "MarkOpponent"
	MINSPACING,     // "MinSpacing"
	OPENBLIND,      // "Standard OpenBlind"
};

UENUM()
enum class ERugbyFormationUrgencyMode : uint8
{
	MINMAX,          // "Min / Max"
	XDIST_FROM_BALL, // "X Dist from ball"
	PARTICIPATION,   // "By participation level"
};

UENUM()
enum class ERugbyFormationZoneType : uint8
{
	STANDARD,
	OPENSIDE_BIAS
};

UENUM()
enum class ERugbyFormationZonePositioningMode : uint8
{
	SPOKE,          // "Spoke"
	RANDOM,         // "Random"
	PLAYERPOSITION, // "PlayerPosition"
};

UENUM()
enum class ERugbyFormationZoneMovementType : uint8
{
	NORMAL,   // "Normal"
	POD,      // "Pod"
	PENDULUM, // "Pendulum"
	REFEREE,  // "Referee"
};

//#rc3_legacy
//UENUM()
//enum class ERugbyFormationStrategyType : uint8
//{
//	ALL, // "--ALL--"
//	ONE, // "Strategy:1"
//	TWO, // "Strategy:2"
//};

UENUM()
enum class ERugbyFormationGroupStrategyType : uint8
{
	NOT_SET,   // "Not set"
	FORWARDS,  // "Forwards"
	BACKS,     // "Backs"
	BACK3,     // "Back 3"
	BREAKDOWN, // "Breakdown"
};

UENUM()
enum class ERugbyFormationPlayerSortStrategy : uint8
{
	RANDOM,             // "Random"
	FAN_OPENBLIND,      // "OpenBlind - Left To Right"
	LINEAR_INSIDETOOUT, // "Linear - Inside to Out"
};

UENUM()
enum class ERugbySetplayAction : int8
{
	NOTHING,				//Default value will do nothing at the point
	PASS,					//Will pass to the player in catch pass state
	PASSVIA,				//Will pass to the player via another player
	DUMMYPASS,				//Will dummy pass to the player in bait pass state
	KICKPUNT,				//Will kick to the player in catch kick state with a long punt
	KICKCHIP,				//Will kick to the player in catch kick state with a chip
	CATCHPASS,				//Will wait to catch the ball from a pass
	BAITPASS,				//Will play the animation for expecting a pass, but won't receive it
	CATCHKICK,				//Will wait to catch the ball from a kick
	KICKGOAL,				//Will attempt to kick a field goal
	DECISION,				//Will wait for the player to decide an action
};

UENUM()
enum class ERugbyFormationRole : int8
{
	ALL = -1,              // added by jamesg, also sometimes means invalid (bleh)

	UNUSED,                // "UNUSED"
	FORMATION,             // "Formation"
	KICKOFFKICKER,         // "KickOffKicker"
	MARKDEFEND,            // "MarkDefend"
	BALLHOLDER,            // "BallHolder"
	GETTHEBALL,            // "GetTheBall"
	FORCEBALL,             // "ForceTheBall"
	LINEOUT,               // "LineOut"
	LINEOUT_THROWER,       // "LineOutThrower"
	RUCK,                  // "Ruck"
	RUCK_SCRUMHALF,        // "RuckScrumHalf"
	SCRUM,                 // "Scrum"
	SCRUM_HALFBACK,        // "ScrumHalfBack"
	SUPPORT,               // "Support"
	SHOOT_FOR_GOAL,        // "ShootForGoal"
	PENALTY_ATTACK,        // "PenaltyAttack"
	PENALTY_DEFENCE,       // "PenaltyDefence"
	TAP_RESTART,           // "TapRestart"
	TUTORIAL,              // "Tutorial"
	NULL_ROLE,             // "RoleNull"
	UNUSED_TWO,            // "RoleUnusedTwo"
	FULLBACK,              // "Fullback"
	RIGHT_WING_DEFEND,     // "Right-Wing-defend"
	LEFT_WING_DEFEND,      // "Left-Wing-defend"
	LINEOUT_RECEIVER,      // "LineOutReceiver"
	TRY_REACTION,          // "TryReaction"
	KICKOFF_CHASER,        // "KickOffChaser"
	REACTION_CUTSCENE,     // "ReactionCutscene"
	MAUL,                  // "Maul"
	MAUL_HALFBACK,         // "MaulHalfback"
	RUCKDEFEND,            // "RuckDefend"
	RUCKSENDRUNNER,        // "RuckSendRunner"
	QUICK_LINEOUT_THROWER, // "QuickLineOutThrower"
	SETPLAY,			    // "SetPlay"
	SETPLAY_SCRUM_HALF,		// "SetPlayScrumHalf"
	SETPLAY_PTB_RECEIVER,	// "SetPlayPTBReceiver"
	PLAY_THE_BALL,			// "PlayTheBall"
	PLAY_THE_BALL_RECEIVER,	// "PlayTheBallReceiver"
	PLAY_THE_BALL_DEFENDER,	// "PlayTheBallDefender"
	PLAY_THE_BALL_SECOND_DEFENDER,
};

//Enum used to tag setplay roles with what kind of fitness check they use
UENUM()
enum class ERugbySetplayFitnessCategories : int8
{
	DISTANCE,
	KICKING,
	SPEED,
	STAMINA,
	LEFTWING,
	RIGHTWING,
	FLYHALF,
	INSIDECENTER,
	OUTSIDECENTER,
	SCRUMHALF,
	FULLBACK
};

//-----  From RUPlayerAnimation.h  -------------------------------------------//

/// Idle groups used by formations as default idles
UENUM()
enum class ERugbyFormationIdleGroup : uint8
{
	NONE = 0,             // "None"
	READY_ENGAGE,     // "Ready to engage"
	AMBIENT_IN_PLAY,  // "In play"
	AMBIENT_STOPPAGE, // "In stoppage"
	DROPOUT_KICKER,   // "Dropout kicker"
	SUPPORT,          // "Support"
	LINEOUT,          // "Lineout"
	DEFENSIVE,        // "Defensive"
	COUNT
};

//-----  From RUGameState.h  -------------------------------------------------//

UENUM()
enum class ERugbyFormationTarget : uint8
{
	TRACKBALL,                     // "Track Ball"
	INHERIT,                       // "Inherit"
	RUCK_CENTER,                   // "Ruck Centre"
	GTB_PLAYER,                    // "GTB Player"
	RESTART_POSITION,              // "Restart position"
	ATTACKLASTFEET,                // "Last feet Attack"
	DEFENCELASTFEET,               // "Last feet Defence"
	LINEOUT_END,                   // "Lineout end",
	ORIGIN,                        // "origin(0,0)"
	RESTART_CENTERED,              // "Restart pos x centred"
	TRACKBALL_VEL,                 // "Track Ball with velocity"
	TRACKBALL_VEL_Z,               // "Track Ball with velocity z"
	PENALTY_SHOT_DEFENSIVE_ORIGIN, // "Penalty shot defensive origin"
	SCRUM_ORIGIN,                  // "Scrum origin"
	TRACKBALL_VEL_Z_CENTERED,      // "Track Ball with velocity z - x centered"
	POD_LEFT,                      // "Pod Left"
	POD_MIDDLE,                    // "Pod Middle"
	POD_RIGHT,                     // "Pod Right"
	SETPLAY_RUCK_CENTER,	       // "Center of the ruck, but saved for use through the set play."
	SETPLAY_PTB_RECEIVER,		   // "PTB Receiver saved for use through the set play."
	COUNT
};

//-----  From SIFGameInput.h  ------------------------------------------------//

/// Input actions for the game
UENUM()
enum class ERugbyGameAction : int16
{
	//INVALID = 0					// added by jamesg

	UP = 0 							// "Up"
	,DOWN							// "Down"
	,LEFT							// "Left"
	,RIGHT							// "Right"
	,PAUSE							// "Pause"
	,ANALOG_X						// "Analog_X"
	,ANALOG_Y						// "Analog_Y"
	,ANALOG_X2						// "Analog_X2"
	,ANALOG_Y2						// "Analog_Y2"

	,ACTION							// "Action"
	,ACTION_SECONDARY				// "Action_Secondary"

	,FEND							// "Fend"
	,SIDESTEP						// "Sidestep"

	,PASS_LEFT_AUTO_DETERMINE		// "Pass_Left_Auto"
	,PASS_RIGHT_AUTO_DETERMINE		// "Pass_Right_Auto"
	,PASS_LEFT_SINGLE				// "Pass_Left_Single"
	,PASS_RIGHT_SINGLE				// "Pass_Right_Single"
	,PASS_EXTEND_OPT1				// "Pass_Extend_Opt1"
	,PASS_EXTEND_OPT2				// "Pass_Extend_Opt2"
	,PASS_EXTEND_OPT3				// "Pass_Extend_Opt3"

	,SPRINT						// "Sprint"

	,STARTKICK_CHIPGRUBBER			// "Start_Kick_Chip_Grubber"
	,STARTKICK_PUNTGOAL				// "Start_Kick_Punt_Goal"
	,KICK_GRUBBER					// "Kick_Grubber"
	,KICK_CHIP						// "Kick_Chip"
	,KICK_PUNT						// "Kick_Punt"
	,KICK_FIELD						// "Kick_Field"

	,INCREMENT_USER_STRATEGY		// "Increment_User_Strategy"

	,TACKLE_STANDARD				// "Tackle_Standard"
	,TACKLE_HEAVY					// "Tackle_Heavy"

	,CHANGE_PLAYER					// "Change_Player"

	,LB_MARK						// "Catch_Pickup"
	,LB_DIVE						// "Dive"
	,LB_KICK						// "Kick"

	,RUCK_JOIN						// "Join_Ruck"
	,RUCK_JOIN_AGGRESSIVE			// "Join_Ruck_Aggressive"
	,JOIN_RUCK_LOOSE				// "Join_Ruck_Loose"
	,RUCK_CLEANOUT					// "Ruck_Cleanout"

	,SWITCH_WIDE_CAMERA				// "Switch_Wide_Camera"

	,INCREMENT_SETPLAY_PLAYERS		// "Increment_Setplay_Players"
	,DECREMENT_SETPLAY_PLAYERS		// "Decrement_Setplay_Players"

	,DEBUG_TOGGLE1					// "Debug_Toggle1"
	,DEBUG_TOGGLE2					// "Debug_Toggle2"

	,LINEOUT_SELECT_JUMPER_FRONT	// "LineoutSelectJumpFront"
	,LINEOUT_SELECT_JUMPER_MIDDLE	// "LineoutSelectJumpMiddle"
	,LINEOUT_SELECT_JUMPER_BACK		// "LineoutSelectJumpBack"
	,LINEOUT_MOVE_JUMPER_CLOSER	// "LineoutMoveCloser"
	,LINEOUT_MOVE_JUMPER_FURTHER	// "LineoutMoveFurther"
	,LINEOUT_PASS					// "LineoutPass"
	,LINEOUT_THROW					// "LineoutThrow"
	,LINEOUT_SWITCH_HIGH_CAMERA		// "LineoutSwitchHighCamera"

	,PICK_GRASS						// "PickGrass"

	,SCRUM_ENGAGE					// "ScrumEngage"
	,SCRUM_NO_EIGHT					// "ScrumNoEight"
	,SCRUM_RELEASE_SCRUM_HALF		// "ScrumReleaseScrumHalf"

	,RUCK_HALF_PICKUP				// "RuckHalfPickup"
	,RUCK_HALF_BOX_KICK				// "RuckHalfBoxKick"

	,STARTKICK_RESTART				// "Start_Kick_Restart"

	,LINEOUT_RUN					// "LineoutRun"
	,LINEOUT_MAUL				// "LineoutMaul"

	,MAUL_HAND_OFF				// "MaulHandoff"

	,PASS_AUTO_DUMMY			// "DummyPass"

	,LINEOUT_SELECT_NEXT_JUMPER	// "LineoutSelectNextJumper"
	,LINEOUT_SELECT_PREVIOUS_JUMPER	// "LineoutSelectPreviousJumper"

	,CHANGE_PLAYER_LEFT			// "Change_Player_Left"
	,CHANGE_PLAYER_RIGHT			// "Change_Player_Right"

	,DECISION_TOP					// "DecisionTop"
	,DECISION_LEFT					// "DecisionLeft"
	,DECISION_RIGHT					// "DecisionRight"
	,DECISION_BOTTOM				// "DecisionBottom"

	,PLAYER_CREATOR_ROTATE_LEFT		// "Rotate_PlayerCreator_Left"
	,PLAYER_CREATOR_ROTATE_RIGHT	// "Rotate_PlayerCreator_Right"

	,STRATEGY_UP					// "Strategy_up"
	,STRATEGY_DOWN					// "Strategy_down"
	,STRATEGY_LEFT				// "Strategy_left"
	,STRATEGY_RIGHT					// "Strategy_right"

	,SIDESTEP_KB_LEFT				// "Sidestep_KB_Left"
	,SIDESTEP_KB_RIGHT				// "Sidestep_KB_Right"

	,LINEOUT_THROW_SHORT			// "LineoutThrowShort"
	,LINEOUT_THROW_LONG				// "LineoutThrowLong"

	,LINEOUT_SLAPDOWN				// "LineoutSlapdown"

	,LINEOUT_SELECT_JUMPER			// "LineoutSelectJumper"
	,LINEOUT_MOVE_JUMPER			// "LineoutMoveJumper"

	,LINEOUT_THROW_NEAR			// "LineoutThrowNear"
	,LINEOUT_THROW_MID				// "LineoutThrowMid"
	,LINEOUT_THROW_FAR				// "LineoutThrowFar"

	,LINEOUT_JUMP					// "LineoutJump"

	,MAUL_PUSH						// "MaulPush"

	,SCRUM_PUSH_LEFT				// "ScrumPushLeftSide"
	,SCRUM_PUSH_RIGHT				// "ScrumPushRightSide"

	,SPRINT_ALT						// "SprintAlternate"
	,SPRINT_ALT2					// "SprintAlternate2"

	,UP_ALT						// "UpAlt"
	,DOWN_ALT					// "DownAlt"
	,LEFT_ALT						// "LeftAlt"
	,RIGHT_ALT						// "RightAlt"

	,SKIP_CUTSCENE					// "SkipCutScene"
	,STRATEGY_ALT1					// "Strategy_AltButton"
	,FORCE_BALL						// "ForceBall"

	,RUCK_LEAVE					// "Ruck_Leave"
	,RUCK_CONTEST_BALL			// "Breakdown_Contest_Ball"

	,LINEOUT_QUICK_THROW			// "QuickLineoutThrow" - only used in a tutorial?

	,SCRUM_NUMBER_EIGHT_PICKUP		// "ScrumNumberEightPickup"

	,THREE_PLAYER_LINEOUT			// "ThreePlayerLineout"
	,FIVE_PLAYER_LINEOU			// "FivePlayerLineout"
	,SEVEN_PLAYER_LINEOUT			// "SevenPlayerLineout"

	,COIN_TOSS_OPT_1				// "CoinTossOpt1"
	,COIN_TOSS_OPT_2				// "CoinTossOpt2"

	,RUCK_JOIN_RUCK_HALF			// "Ruck_Join_Ruck_Half"
	,RUCK_LEAVE_RUCK_HALF			// "Ruck_Leave_Ruck_Half"

	,CALL_FOR_PASS					// "Call_For_Pass"
	,CALL_FOR_KICK_DOWN_FIELD		// "Call_For_Kick_Down_Field"
	,CALL_FOR_KICK_INTO_TOUCH		// "Call_For_Kick_Into_Touch"

	,LOCK_ONTO_BALL					// "LockOntoBall"

	,CONVERSION_QUICK_KICK				// "Quick_Conversion"
		
	, RUCK_HALF_SET_PLAYS			// "RuckHalfSetPlays"
	, ANALOG_L						// left stick no direction
	, ANALOG_R						// right stick no direction

	//////////////////////////////////////////////////////////////////////////
	// Dummy actions to allow UI to work correctly with actions that require the right thumb-stick
	//////////////////////////////////////////////////////////////////////////
	, UI_FEND
	, UI_SIDESTEP_LEFT
	, UI_SIDESTEP_RIGHT
	, UI_PASS_AUTO_DUMMY
	//////////////////////////////////////////////////////////////////////////

	, PLAY_THE_BALL_SET_PLAYS					// #122 "PlayTheBallSetPlays"

	//////////////////////////////////////////////////////////////////////////
	// Debug Start
	//////////////////////////////////////////////////////////////////////////
#ifdef ENABLE_DEBUG_KEYS
	,DEBUG_START
	
		
	,DEBUG_LOOK_BUTTON
	,DEBUG_SWITCH_CAMERA
	,DEBUG_PLAY_REPLAY
	,DEBUG_PLAY_COMMENTARY
	,DEBUG_FREECAM_MOVE_X
	,DEBUG_FREECAM_MOVE_Y
	,DEBUG_FREECAM_LOOK_X
	,DEBUG_FREECAM_LOOK_Y

	,DEBUG_MENU_TOGGLE
	,DEBUG_MENU_UP
	,DEBUG_MENU_DOWN
	,DEBUG_MENU_LEFT
	,DEBUG_MENU_RIGHT
	,DEBUG_MENU_NEXT_PAGE
	,DEBUG_MENU_PREV_PAGE
	,DEBUG_MENU_NEXT_INDEX
	,DEBUG_MENU_PREV_INDEX
	,DEBUG_MENU_SAVE
	,DEBUG_MENU_LOAD
	,DEBUG_MENU_SLOW
	,DEBUG_MENU_FAST
	,DEBUG_MENU_A

	,DEBUG_INGAME_NEXT_PLAYER
	,DEBUG_INGAME_PREV_PLAYER

	,DEBUG_CROWD_REACTION

	,DEBUG_PAUSE
	,DEBUG_STEP_FRAME
	,DEBUG_RESET_TIMESCALE
	,DEBUG_INCREMENT_LABEL_DISPLAY
	,DEBUG_DECREMENT_LABEL_DISPLAY
	,DEBUG_INCREMENT_TEAM_PAGE
	,DEBUG_TOGGLE_TEAM_STATE
	,DEBUG_TOGGLE_PLAYER_STATE
	,DEBUG_TOGGLE_GAME_STATE
	,DEBUG_TOGGLE_FREECAM
	,DEBUG_TOGGLE_OFFSIDE_STATE
	,DEBUG_TOGGLE_FORMATION_STATE
	,DEBUG_TOGGLE_ORIGINS
	,DEBUG_TOGGLE_COLLIDABLES
	,DEBUG_TOGGLE_PARTICIPATION

	,DEBUG_DISABLE_DEFENCE
	,DEBUG_DISABLE_ATTACK

	,DEBUG_GAME_SWITCH_WINDOW

	,DEBUG_INCREMENT_SETPLAY
	,DEBUG_DECREMENT_SETPLAY
	,DEBUG_START_SETPLAY

	,DEBUG_TWO_BONE_TACKLE
	,DEBUG_TWO_BONE_DRAG

	,DEBUG_DISABLE_TACKLE

	,DEBUG_SWITCH_ATTACKING_TEAM
	,DEBUG_TOGGLE_WAYPOINTS

	,DEBUG_PENALISE_BALLHOLDER
	,DEBUG_RUMBLE_DEBUG

	,DEBUG_BALLHOLDER_TOGGLE
	,DEBUG_FBG_ATTACKER_TOGGLE
	,DEBUG_CHANGE_DIFFICULTY

	,DEBUG_SOUND_A
	,DEBUG_SOUND_B
	,DEBUG_SOUND_C
	,DEBUG_SOUND_D

	,DEBUG_GIVE_PRO_BALL

	// RC4 added
	, DEBUG_RESTART_MATCH
	, DEBUG_QUIT_MATCH
	, DEBUG_FORCE_LINEOUT
	, DEBUG_FORCE_SCRUM
	, DEBUG_RELOAD_CAMERAS
	, DEBUG_FORCE_CRASH


	, DEBUG_END
#endif
	//////////////////////////////////////////////////////////////////////////
	// Debug End
	//////////////////////////////////////////////////////////////////////////
	// Putting here to avoid messing up the context_helper_settings.json
	,RU_GAME_ACTIONS_MAX

	, JOIN_RUCK = RUCK_JOIN 			// This had to be done since ruged\tutorials\rucks_advanced.json has this name, and if we dont use JSON parser gives error TEXT("JsonValueToUProperty - Unable import enum %s from string value %s for property %s")
	, JOIN_RUCK_AGGRESSIVE = RUCK_JOIN_AGGRESSIVE //// This had to be done since ruged\tutorials\rucks_advanced.json has this name, and if we dont use JSON parser gives error TEXT("JsonValueToUProperty - Unable import enum %s from string value %s for property %s")
};
constexpr const size_t RUGBY_GAME_ACTION_COUNT = size_t(ERugbyGameAction::RU_GAME_ACTIONS_MAX);

//-----  From SIFDebugInput.h  ------------------------------------------------//
//#ifdef ENABLE_DEBUG_KEYS
/*
UENUM()
enum class ERugbyDebugAction
{
	// Debug keys
	DEBUG_LOOK_BUTTON,
	DEBUG_SWITCH_CAMERA,
	DEBUG_PLAY_REPLAY,
	DEBUG_PLAY_COMMENTARY,
	DEBUG_FREECAM_MOVE_X,
	DEBUG_FREECAM_MOVE_Y,
	DEBUG_FREECAM_LOOK_X,
	DEBUG_FREECAM_LOOK_Y,

	DEBUG_MENU_TOGGLE,
	DEBUG_MENU_UP,
	DEBUG_MENU_DOWN,
	DEBUG_MENU_LEFT,
	DEBUG_MENU_RIGHT,
	DEBUG_MENU_NEXT_PAGE,
	DEBUG_MENU_PREV_PAGE,
	DEBUG_MENU_NEXT_INDEX,
	DEBUG_MENU_PREV_INDEX,
	DEBUG_MENU_SAVE,
	DEBUG_MENU_LOAD,
	DEBUG_MENU_SLOW,
	DEBUG_MENU_FAST,
	DEBUG_MENU_A,

	DEBUG_INGAME_NEXT_PLAYER,
	DEBUG_INGAME_PREV_PLAYER,

	DEBUG_TOGGLE1,
	DEBUG_TOGGLE2,

	DEBUG_CROWD_REACTION,

	DEBUG_PAUSE,
	DEBUG_STEP_FRAME,
	DEBUG_RESET_TIMESCALE,
	DEBUG_INCREMENT_LABEL_DISPLAY,
	DEBUG_DECREMENT_LABEL_DISPLAY,
	DEBUG_INCREMENT_TEAM_PAGE,
	DEBUG_TOGGLE_TEAM_STATE,
	DEBUG_TOGGLE_PLAYER_STATE,
	DEBUG_TOGGLE_GAME_STATE,
	DEBUG_TOGGLE_FREECAM,
	DEBUG_TOGGLE_OFFSIDE_STATE,
	DEBUG_TOGGLE_FORMATION_STATE,
	DEBUG_TOGGLE_ORIGINS,
	DEBUG_TOGGLE_COLLIDABLES,
	DEBUG_TOGGLE_PARTICIPATION,

	DEBUG_DISABLE_DEFENCE,
	DEBUG_DISABLE_ATTACK,

	DEBUG_GAME_SWITCH_WINDOW,

	DEBUG_INCREMENT_SETPLAY,
	DEBUG_DECREMENT_SETPLAY,
	DEBUG_START_SETPLAY,

	DEBUG_TWO_BONE_TACKLE,
	DEBUG_TWO_BONE_DRAG,

	DEBUG_DISABLE_TACKLE,

	DEBUG_SWITCH_ATTACKING_TEAM,
	DEBUG_TOGGLE_WAYPOINTS,

	DEBUG_PENALISE_BALLHOLDER,
	DEBUG_RUMBLE_DEBUG,

	DEBUG_BALLHOLDER_TOGGLE,
	DEBUG_FBG_ATTACKER_TOGGLE,
	DEBUG_CHANGE_DIFFICULTY,

	DEBUG_SOUND_A,
	DEBUG_SOUND_B,
	DEBUG_SOUND_C,
	DEBUG_SOUND_D,

	DEBUG_GIVE_PRO_BALL,

	// RC4 added
	DEBUG_RESTART_MATCH,
	DEBUG_QUIT_MATCH,
	DEBUG_FORCE_LINEOUT,
	DEBUG_FORCE_SCRUM,		

	DEBUG_ACTIONS_MAX
};
constexpr const size_t RUGBY_DEBUG_ACTION_COUNT = size_t(ERugbyDebugAction::DEBUG_ACTIONS_MAX);*/
//#endif

//-----  from SSPlayDirection.h  ---------------------------------------------//

UENUM()
enum class ERugbyPlayDirection : int8
{
	SOUTH = -1,
	UNKNOWN = 0,
	NORTH = 1
};

//-----  From RUGameState.h  -------------------------------------------------//

UENUM()
enum class ERugbyAnimEvent : int8
{
	INVALID = -1,

	BALL_CONTACT_EVENT,				///< fired when a player contacts the ball during a contact animation
	END_EVENT,						///< fired when a player reaches the end of an accumulated animation
	BALL_RELEASED_EVENT,			///< fired when a player released the ball from a pass or kick
	BLEND_OUT_EVENT,				///< fired when some animations blend out
	BALL_KICK_EVENT,				///< fired when the ball connects with the foot (and ball contact has been used in anim)
	BALL_DROP_EVENT,				///< fired when a player releases the ball prior to kicking
	TRY_GETUP_EVENT,				///< fired when getting up from try
	GOT_UP_EVENT,					///< fired after getting up (from pickup)
	END_EMOTIONAL_REACTION_EVENT,	///< fired after emotional reaction animation
	TACKLE_CONTACT_EVENT,			
	TACKLE_RELEASE_BALL_EVENT,		
	THROW_TO_SELF_ENDED_EVENT,		
	FOOT_TAP_END_EVENT,				
	RIGHT_ARM_FREE_DURATION_EVENT,	
	LEFT_ARM_FREE_DURATION_EVENT,	
	TOUCHING_DOWN_DURATION_EVENT,	///< fired while ball is touching down
	UPRIGHT_DURATION_EVENT,			///< fired while a tackled player is upright
	FALLING_DURATION_EVENT,			
	ON_GROUND_DURATION_EVENT,		
	ON_RIGHTFOOT_DURATION_EVENT,	
	ON_LEFTFOOT_DURATION_EVENT,	
	ON_SLIDING_DURATION_EVENT,		
	WB_180_DYNAMIC_END_EVENT,		///< used by RUPlayerMovement
	WB_135_DYNAMIC_END_EVENT,		///< used by RUPlayerMovement
	WB_90_DYNAMIC_END_EVENT,		///< used by RUPlayerMovement
	NB_180_DYNAMIC_END_EVENT,		///< used by RUPlayerMovement
	NB_135_DYNAMIC_END_EVENT,		///< used by RUPlayerMovement
	NB_90_DYNAMIC_END_EVENT,		///< used by RUPlayerMovement
	E180_DYNAMIC_END_EVENT,
	QUICK_TURN_180_END_EVENT,
	RUCK_CONTACT_EVENT,
	EXIT_TRACK,
	EXIT,	
	FOOTSTEPS_EVENT, // I don't think we want these here, I think they should be there own notify types, or sync markers
	IK_TRANSITION_EVENT,
	PARTICLE_EVENT,
	BALL_TRAVEL,
	RAG_DOLL,
	ATTACH_TO_TACKLEE,
	SDS,
	HELDUP,
	PASSEND,
	COUNT
};

//----------------------------------------------------------------------------//

UENUM()
enum class EHumanPlayerSlot : int8
{
	FIRST_HUMAN = 0
	,SECOND_HUMAN
	,THIRD_HUMAN
	,FOURTH_HUMAN
	,FIFTH_HUMAN
	,SIXTH_HUMAN
	,SEVENTH_HUMAN
	,EIGHTH_HUMAN

	,NUM_HUMANS
	,INVALID_HUMAN = -1
};


//GGS SRA: Adding this to track the different reasons why an interchange could fail
UENUM()
enum class EValidInterchangeReason : uint8
{
	VIR_VALID = 0,
	VIR_ALREADY_SWAPPED,
	VIR_LIMIT_REACHED,
	VIR_18TH_NOT_READY,
	VIR_MAX
};