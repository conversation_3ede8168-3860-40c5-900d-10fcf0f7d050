// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#pragma once

#include "CoreMinimal.h"
#include "WWUIScreenTemplate.h"
#include "WWUIScrollBox.h"
#include "WWUIPopulator.h"
#include "WWUIScreenLeaderboards.generated.h"

#ifndef NRL_USE_13S_MODE 
#define NRL_USE_13S_MODE 1
#endif

class UWWUILeaderboardPopulator;
class ULeaderboardManager;

/**
 * 
 */
UCLASS()
class RUGBY_API UWWUIScreenLeaderboards : public UWWUIScreenTemplate
{
	GENERATED_BODY()
	
	enum OnlineLeaderboardState
	{
		OLS_MyRankFifteens = 0,
#if !NRL_USE_13S_MODE
		OLS_MyRankSevens,
#endif
		OLS_FriendsFifteens,
#if !NRL_USE_13S_MODE

		OLS_FriendsSevens,
#endif
		OLS_TopFifteens,
#if !NRL_USE_13S_MODE
		OLS_TopSevens,
#endif
		OLS_Max
	};

	enum OnlineLeaderboardScreenState
	{
		OLSS_Init = 0,
		OLSS_focused,
		OLSS_Tick,
		OLSS_Main,
		OLSS_Loading,
		OLSS_Exit
	};

	enum OnlineLeaderBoardLegend
	{
		OLBL_Gamertag_Back,
		OLBL_Help,
		OLBL_Back
	};

public:
	virtual void Startup(UWWUIStateScreenData* InData = nullptr) override;
	virtual void Update(float DeltaTime) override;
	virtual void RegisterFunctions() override;
	UFUNCTION()
	void ClearAllData(APlayerController* playerController);
	UFUNCTION()
	void AddDebugData(APlayerController* playerController);
	//virtual void OnInFocus() override;
	virtual void OnEnterScreen() override;
	virtual void OnOutFocus(bool ShouldOutFocus) override;
	virtual void ExecuteTableFunction(FString InTableId, int InIdx, FString InAction, FString InActionString) override;
	//virtual void TableOnSelectionChange(FString InTableId, int OldIdx, int NewIdx) override;

	void NavigateBack(APlayerController* playerController);
	void OnTabLeft(APlayerController* playerController);
	void OnTabRight(APlayerController* playerController);
	void ChangeState(int32 InDir);
	UFUNCTION()
	void OnLeaderboardDataReady(bool bWasSuccessful);

	void RepopulateLeaderboardList();

	void ShowHelp(APlayerController* pPlayerController);

	void OnRotationInput(float AxisValue, APlayerController* OwningPlayer);
	void ResetAxisTrigger();
	bool m_canTriggerAxis = false;
	FTimerHandle m_triggerAxisHandle;

#if PLATFORM_XBOXONE
	void OnViewGamerCard(int32 CurrentPlayerIndex);
	void ShowGamerCard(APlayerController* OwningPlayer, const TSharedPtr<const FUniqueNetId> requesteeNetID);
#endif

	// If a request comes back we need to set the focus for when we reconnect the controller.
	virtual void SetFocusForControllers(APlayerController* InController = nullptr) override;

private:


	FTimerHandle LeaderboardUpdateDelayHandle;

	void UpdateFilterText();
	void RequestLeaderboardData();

	UWWUIScrollBox * Leaderboard = nullptr;
	UWWUILeaderboardPopulator* OnlineLeaderboardPopulator = nullptr;
	OnlineLeaderboardState CurrentLeaderboardState = OLS_FriendsFifteens;
	OnlineLeaderboardScreenState CurrentScreenState = OLSS_Init;
	ULeaderboardManager *leaderboardManager;

	//	Mattt H - This is to show the gamercards for xbox one console. Value default in startup
	bool ShowGamerCards;
};
