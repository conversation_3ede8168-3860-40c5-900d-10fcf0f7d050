
//Class to handle tackle Animations: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles

#pragma once

#include "Rugby.h"
#include "CoreMinimal.h"
#include "Animation/RugbyAnimationEnums.h"
#include "DataTables/wwDTRequestTypeEnum.h"
#include "DataTables/wwDTBlendNTypeEnum.h"
#include "RugbyEnums.h"
#include "Mab/Types/MabMatrix.h"
#include "Rugby/BasicStateMachine.h"
#include "Animation/RugbyAnimationStateMachineBase.h"
#include "Match/RugbyUnion/Enums/RUTackleEnum.h"
#include "Queue.h"

class RugbyAnimationStateMachineMgr;
class RUPlayerAnimation;
class ARugbyCharacter;
class UAnimSequence;
struct FAnimMontageInstance;
struct FBlendNMontage;
struct FRugbyTackleBlendAnimRecord;

//===============================================================================
//===============================================================================

class RugbyAnimationStateMachine_Tackles : public RugbyAnimationStateMachineBase
{
public:
	RugbyAnimationStateMachine_Tackles(RugbyAnimationStateMachineMgr* pSMMgr, RUPlayerAnimation* pOwnerAnimation, ARugbyCharacter* pOwnerPlayer);

	~RugbyAnimationStateMachine_Tackles();

	virtual void StateMachine_Reset();

	void ChangeStateToPlayTheBallGround();

	virtual void StateMachine_ReturnToNullState();

	//virtual void StateMachine_HandleAnimationEnd(FAnimMontageInstance* montageInst);

	virtual void StateMachine_HandleAnimationEvent(float time, ERugbyAnimEvent event, size_t userdata);

	virtual void StateMachine_HandleAnimationTransitionNodeEvent(float time, ERugbyAnimEvent event, size_t userdata, const UAnimSequence* pAnimSeq) {} //nothing to do...

	virtual void StateMachine_Update(float delta);

	virtual void StateMachine_PostAnimUpdate(float delta);

	virtual bool StateMachine_IsAnimationAvailable(const char* request) { return !IsTransitionActive(); }

	virtual bool IsNodeActive() { return m_IsNodeActive; }

	virtual void ClearMontageInstanceReferences(FAnimMontageInstance* pMontageInstance) override;

	BasicStateMachine<ERugbyAnim_Mode_Tackles, RugbyAnimationStateMachine_Tackles> m_TackleStateMachine;

	bool IsInTackle() const { return ERugbyAnim_Mode_Tackles::null != m_CurrentModeTackles; }

	void TackleeGetFreeArms(bool& LeftArmFree, bool& RightArmFree);

	ERugbyAnim_Mode_Tackles GetCurrentTackleMode() const { return m_CurrentModeTackles; }
	
	bool IsTackleAnimOnGround() const { return m_TackleAnimationStateIsGround; }

	void StateMachine_SelectTackleNode();

	TQueue<ERugbyAnim_Mode_Tackles> m_TacklesModeQueue;

	bool HasValidTryTackleAnim(TACKLE_RESULT_TYPE &type, TRY_TACKLE_TYPE &try_type, ARugbyCharacter* tacklee, ARugbyCharacter* tackler);

	bool IsInSuccessfulAnkleTap() const { return m_IsSuccessfulAnkleTap 
		&& (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::ankle_tap_tacklee || m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::ankle_tap_tackler); }


	EContestedTackleAnimationState GetContestedTackleState() const { return m_ContestedTackleStateMachine.GetCurrentStateKey(); }

private:
	FRugbyTackleBlendAnimRecord* PickBestTackleAnimation(const TArray<FRugbyTackleBlendAnimRecord*>& contactAnimationList, FVector contactVector);
	FRugbyTackleBlendAnimRecord* PickBestSupportTacklerAnimation(const TArray<FRugbyTackleBlendAnimRecord*>& contactAnimationList, FVector contactVector, FString supportTacklerAnimName);


private:
	void InitialiseTackleStateMachine();	

	BASIC_STATEMACHINE_DECLARE_STATE (NullTackle);
	bool	m_bJustEnteredNull = false;

	BASIC_STATEMACHINE_DECLARE_STATE (AnkleTapTacklee);
	BASIC_STATEMACHINE_DECLARE_STATE (AnkleTapTackler);
	BASIC_STATEMACHINE_DECLARE_STATE (ContestedTacklee);
	BASIC_STATEMACHINE_DECLARE_STATE (ContestedTackler);
	BASIC_STATEMACHINE_DECLARE_STATE (FendFailTacklee);
	BASIC_STATEMACHINE_DECLARE_STATE (FendFailTackler);
	BASIC_STATEMACHINE_DECLARE_STATE (FendSuccessTacklee);
	BASIC_STATEMACHINE_DECLARE_STATE (FendSuccessTackler);
	BASIC_STATEMACHINE_DECLARE_STATE (HeadHighTacklee);
	BASIC_STATEMACHINE_DECLARE_STATE (HeadHighTackler);
	BASIC_STATEMACHINE_DECLARE_STATE (SidestepFailTacklee);
	BASIC_STATEMACHINE_DECLARE_STATE (SidestepFailTackler);
	BASIC_STATEMACHINE_DECLARE_STATE (SidestepSuccessTacklee);
	BASIC_STATEMACHINE_DECLARE_STATE (SidestepSuccessTackler);
	BASIC_STATEMACHINE_DECLARE_STATE (StandardFailTacklee);
	BASIC_STATEMACHINE_DECLARE_STATE (StandardFailTackler);
	BASIC_STATEMACHINE_DECLARE_STATE (StandardSuccessTacklee);
	BASIC_STATEMACHINE_DECLARE_STATE (StandardSuccessTackler);
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE

	BASIC_STATEMACHINE_DECLARE_STATE (StandardFailTwoTacklee);
	BASIC_STATEMACHINE_DECLARE_STATE (StandardFailTwoTackler);
	BASIC_STATEMACHINE_DECLARE_STATE (StandardSuccessTwoTacklee);
	BASIC_STATEMACHINE_DECLARE_STATE (StandardSuccessTwoTackler);

#endif
	BASIC_STATEMACHINE_DECLARE_STATE (TryPushedTacklee);
	BASIC_STATEMACHINE_DECLARE_STATE (TryPushedTackler);
	BASIC_STATEMACHINE_DECLARE_STATE (DiveMissTackler);
	BASIC_STATEMACHINE_DECLARE_STATE (FastGetupTackler);
	BASIC_STATEMACHINE_DECLARE_STATE (GetupTackle);
	BASIC_STATEMACHINE_DECLARE_STATE (GetupTackleInjury);
	BASIC_STATEMACHINE_DECLARE_STATE (GetupPlayTheBall);
	BASIC_STATEMACHINE_DECLARE_STATE (QuickTapTackle);
	BASIC_STATEMACHINE_DECLARE_STATE (TryTackle);
	BASIC_STATEMACHINE_DECLARE_STATE (TryCornerTackle);
	

	BasicStateMachine<EContestedTackleAnimationState, RugbyAnimationStateMachine_Tackles> m_ContestedTackleStateMachine;

	void InitialiseContestedTackleStateMachine();

	void PlayContestedTackleAnimation ( FName SubType,	bool LoopAlways = false, float BlendInTime = SMDefaultBlendInTime);

	BASIC_STATEMACHINE_DECLARE_STATE(ContestedTackleInvalidState);
	BASIC_STATEMACHINE_DECLARE_STATE(ContestedTackleImpact);
	BASIC_STATEMACHINE_DECLARE_STATE(ContestedTackleEngaged);
	BASIC_STATEMACHINE_DECLARE_STATE(ContestedTackleTakeDown);
	BASIC_STATEMACHINE_DECLARE_STATE(ContestedTackleBreakOut);
	BASIC_STATEMACHINE_DECLARE_STATE(ContestedTackleGround);

	void PlaySelectedTackleAnimation(TArray<FRugbyTackleBlendAnimRecord*>& Rec, float BlendInTime = SMDefaultBlendInTime, bool LoopAlways = false);

	float calculateAnimationStartOffset(const FVector& contactVector, const FRugbyTackleBlendAnimRecord* pAnimRec) const;

	const FRugbyTackleBlendAnimRecord* StateMachine_GetTryTackleAnimation(TArray<FRugbyTackleBlendAnimRecord*> ContactAnimationList, FVector& tackler_contact_vector) const;

	void StateMachine_UpdatePickTryTackles();	

	bool m_TackleAnimationStateIsGround = false;

	ERugbyAnim_Mode_Tackles m_CurrentModeTackles = ERugbyAnim_Mode_Tackles::null;

	struct StTackle_usableAnimationList
	{
		ERugbyAnim_Mode_Tackles tackleType = ERugbyAnim_Mode_Tackles::null;
		TArray<FRugbyTackleBlendAnimRecord*> usableRecList;
	} m_usableTackleAnimRecs;

	struct StTackle_CurrentTackleMontage
	{
		StTackle_CurrentTackleMontage ()
		{ 
			Reset(); 
		}

		void Reset (void) 
		{
			pMontageInstance = nullptr;
			pTackleAnimRecord = nullptr;
			montageUniqueID = 0;
		}

		FAnimMontageInstance* pMontageInstance = nullptr; //keep a track of tackle montage to handle end events in a better way. 		
		const FRugbyTackleBlendAnimRecord * pTackleAnimRecord = nullptr; // keep a track of which animation was played.
		unsigned int montageUniqueID = 0;

	} m_tackleMontageData;

	
	FString m_NewTackleMetaData;

	FRugbyTackleBlendAnimRecord* m_SelectedTackleBlend_RecordByContactNode = nullptr;

	bool m_IsNodeActive = true; //always true....

	float m_HighTackleTimer = 0.0f;

	bool m_IsSuccessfulAnkleTap = false;
	
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
public:
	virtual FString GetDebugStateText() override;
	FString GetSubStateInfo();
#endif

};

