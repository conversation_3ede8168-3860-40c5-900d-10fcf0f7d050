<?xml version="1.0" encoding="utf-8"?>
<DatabaseDef xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <tables>
    <TableDef>
      <name>rudb_attribute_presets</name>
      <row_size>68</row_size>
      <columns>
        <ColumnDef>
          <column_name>fitness</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>agility</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>speed</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>acceleration</column_name>
          <offset>16</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>aggression</column_name>
          <offset>20</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tackling</column_name>
          <offset>24</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>break_tackle</column_name>
          <offset>28</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>passing</column_name>
          <offset>32</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>offloading</column_name>
          <offset>36</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>general_kicking</column_name>
          <offset>40</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>goal_kicking</column_name>
          <offset>44</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>catching</column_name>
          <offset>48</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>strength</column_name>
          <offset>52</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mental_ability</column_name>
          <offset>56</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>jumping</column_name>
          <offset>60</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>discipline</column_name>
          <offset>64</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_career_draftable_player</name>
      <row_size>8</row_size>
      <columns>
        <ColumnDef>
          <column_name>player_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_career_inbox</name>
      <row_size>672</row_size>
      <columns>
        <ColumnDef>
          <column_name>email_id</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>data_index</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>read</column_name>
          <offset>12</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>replacements</column_name>
          <offset>13</offset>
          <size>640</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>date</column_name>
          <offset>653</offset>
          <size>16</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_career_mode_comp</name>
      <row_size>12</row_size>
      <columns>
        <ColumnDef>
          <column_name>start_date</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>definition_id</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_career_my_contracts</name>
      <row_size>12</row_size>
      <columns>
        <ColumnDef>
          <column_name>from_team_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>duration</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>value</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>have_read</column_name>
          <offset>10</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>from_club_team</column_name>
          <offset>11</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_career_played_match</name>
      <row_size>20</row_size>
      <columns>
        <ColumnDef>
          <column_name>home_team_score</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>away_team_score</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>date</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>home_team_id</column_name>
          <offset>16</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>away_team_id</column_name>
          <offset>18</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_career_pro_contract_interest</name>
      <row_size>16</row_size>
      <columns>
        <ColumnDef>
          <column_name>team_db_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>reject_count</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>offer_count</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>frustration_level</column_name>
          <offset>10</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>interest_level</column_name>
          <offset>12</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_career_pro_player_matches</name>
      <row_size>32</row_size>
      <columns>
        <ColumnDef>
          <column_name>home_team_score</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>away_team_score</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>performance</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>date</column_name>
          <offset>16</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>comp_inst_id</column_name>
          <offset>20</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>match_inst_id</column_name>
          <offset>22</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>home_team_id</column_name>
          <offset>24</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>away_team_id</column_name>
          <offset>26</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_team_id</column_name>
          <offset>28</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>match_won</column_name>
          <offset>30</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>participated</column_name>
          <offset>31</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_career_pro_player_stats</name>
      <row_size>44</row_size>
      <columns>
        <ColumnDef>
          <column_name>comp_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>year</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>satellite_index</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tackle_attempts</column_name>
          <offset>10</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>successful_tackles</column_name>
          <offset>12</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>points_scored</column_name>
          <offset>14</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>running_meters</column_name>
          <offset>16</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>kicking_meters</column_name>
          <offset>18</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tries_scored</column_name>
          <offset>20</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>line_breaks</column_name>
          <offset>21</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>offloads</column_name>
          <offset>22</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>offloads_attempted</column_name>
          <offset>23</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>kicks</column_name>
          <offset>24</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>handling_errors</column_name>
          <offset>25</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>goal_attempts</column_name>
          <offset>26</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>successful_goals</column_name>
          <offset>27</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>field_goal_attempts</column_name>
          <offset>28</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>successful_field_goals</column_name>
          <offset>29</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>yellow_cards</column_name>
          <offset>30</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>red_cards</column_name>
          <offset>31</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>injuries</column_name>
          <offset>32</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>penalties_against</column_name>
          <offset>33</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>games_played</column_name>
          <offset>34</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>successful_penalties</column_name>
          <offset>35</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>penalty_attempts</column_name>
          <offset>36</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>ruck_entry</column_name>
          <offset>37</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>contest_win</column_name>
          <offset>38</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>lineout_steal_attempts</column_name>
          <offset>39</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>successful_lineout_steal</column_name>
          <offset>40</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_city</name>
      <row_size>20</row_size>
      <columns>
        <ColumnDef>
          <column_name>country_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>6</offset>
          <size>13</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_def_match</name>
      <row_size>16</row_size>
      <columns>
        <ColumnDef>
          <column_name>date</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>round_id</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_a_id</column_name>
          <offset>10</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_b_id</column_name>
          <offset>12</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stadium_id</column_name>
          <offset>14</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_def_round</name>
      <row_size>36</row_size>
      <columns>
        <ColumnDef>
          <column_name>competition_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>6</offset>
          <size>29</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_def_sat_name</name>
      <row_size>24</row_size>
      <columns>
        <ColumnDef>
          <column_name>competition_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>6</offset>
          <size>13</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mnemonic</column_name>
          <offset>19</offset>
          <size>4</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_def_team</name>
      <row_size>12</row_size>
      <columns>
        <ColumnDef>
          <column_name>competition_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_id</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pool_or_conference</column_name>
          <offset>8</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_inst</name>
      <row_size>16</row_size>
      <columns>
        <ColumnDef>
          <column_name>start_date</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>definition_id</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>next_match_index</column_name>
          <offset>10</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_rounds</column_name>
          <offset>12</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_matches</column_name>
          <offset>14</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_inst_match</name>
      <row_size>20</row_size>
      <columns>
        <ColumnDef>
          <column_name>date</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>home_team_id</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>away_team_id</column_name>
          <offset>10</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stadium_id</column_name>
          <offset>12</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>side_a_score</column_name>
          <offset>14</offset>
          <size>2</size>
          <type>3</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>side_b_score</column_name>
          <offset>16</offset>
          <size>2</size>
          <type>3</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>round_id</column_name>
          <offset>18</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_inst_player_match_stats</name>
      <row_size>112</row_size>
      <columns>
        <ColumnDef>
          <column_name>possession</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>territory</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>comp_inst_match_id</column_name>
          <offset>12</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_id</column_name>
          <offset>14</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>handling_errors</column_name>
          <offset>16</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>penalties_conceded</column_name>
          <offset>17</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tries</column_name>
          <offset>18</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>conversions</column_name>
          <offset>19</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>penalties_converted</column_name>
          <offset>20</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>drop_goals</column_name>
          <offset>21</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>try_scorer_1_str</column_name>
          <offset>22</offset>
          <size>30</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>try_scorer_2_str</column_name>
          <offset>52</offset>
          <size>30</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>try_scorer_3_str</column_name>
          <offset>82</offset>
          <size>30</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_inst_player_removal</name>
      <row_size>16</row_size>
      <columns>
        <ColumnDef>
          <column_name>end_date</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>instance_id</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_id</column_name>
          <offset>10</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>type</column_name>
          <offset>12</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_inst_player_stats</name>
      <row_size>44</row_size>
      <columns>
        <ColumnDef>
          <column_name>instance_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_id</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>satellite_index</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tackle_attempts</column_name>
          <offset>10</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>successful_tackles</column_name>
          <offset>12</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>points_scored</column_name>
          <offset>14</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>running_meters</column_name>
          <offset>16</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>kicking_meters</column_name>
          <offset>18</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tries_scored</column_name>
          <offset>20</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>line_breaks</column_name>
          <offset>21</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>offloads</column_name>
          <offset>22</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>offloads_attempted</column_name>
          <offset>23</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>kicks</column_name>
          <offset>24</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>handling_errors</column_name>
          <offset>25</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>goal_attempts</column_name>
          <offset>26</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>successful_goals</column_name>
          <offset>27</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>field_goal_attempts</column_name>
          <offset>28</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>successful_field_goals</column_name>
          <offset>29</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>yellow_cards</column_name>
          <offset>30</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>red_cards</column_name>
          <offset>31</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>injuries</column_name>
          <offset>32</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>penalties_against</column_name>
          <offset>33</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>games_played</column_name>
          <offset>34</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>successful_penalties</column_name>
          <offset>35</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>penalty_attempts</column_name>
          <offset>36</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>ruck_entry</column_name>
          <offset>37</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>contest_win</column_name>
          <offset>38</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>lineout_steal_attempts</column_name>
          <offset>39</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>successful_lineout_steal</column_name>
          <offset>40</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_inst_round</name>
      <row_size>8</row_size>
      <columns>
        <ColumnDef>
          <column_name>instance_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_inst_team</name>
      <row_size>8</row_size>
      <columns>
        <ColumnDef>
          <column_name>instance_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_id</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_inst_team_result</name>
      <row_size>12</row_size>
      <columns>
        <ColumnDef>
          <column_name>competition_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_id</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>year</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>finals_result</column_name>
          <offset>10</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>rank</column_name>
          <offset>11</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_inst_team_stats</name>
      <row_size>84</row_size>
      <columns>
        <ColumnDef>
          <column_name>streak_start_date</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>instance_id</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_id</column_name>
          <offset>10</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>satellite_index</column_name>
          <offset>12</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>confidence</column_name>
          <offset>14</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>match_streak</column_name>
          <offset>16</offset>
          <size>2</size>
          <type>3</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tries_scored</column_name>
          <offset>18</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tries_conceded</column_name>
          <offset>20</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>points_for</column_name>
          <offset>22</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>points_against</column_name>
          <offset>24</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>penalties_awarded</column_name>
          <offset>26</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>penalties_conceded</column_name>
          <offset>28</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>preliminaries_points</column_name>
          <offset>30</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>finals_points</column_name>
          <offset>32</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>games_played</column_name>
          <offset>34</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>games_won</column_name>
          <offset>36</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>games_lost</column_name>
          <offset>38</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>games_drawn</column_name>
          <offset>40</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bonus_points</column_name>
          <offset>42</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>byes</column_name>
          <offset>44</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>longest_win_streak</column_name>
          <offset>46</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>longest_lose_streak</column_name>
          <offset>48</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>prelim_points_for</column_name>
          <offset>50</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>prelim_points_against</column_name>
          <offset>52</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>prelim_games_played</column_name>
          <offset>54</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>prelim_games_won</column_name>
          <offset>56</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>prelim_games_lost</column_name>
          <offset>58</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>prelim_games_drawn</column_name>
          <offset>60</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>prelim_bonus_points</column_name>
          <offset>62</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>prelim_byes</column_name>
          <offset>64</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>prelim_tries_scored</column_name>
          <offset>66</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>prelim_tries_conceded</column_name>
          <offset>68</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>prelim_penalties_conceded</column_name>
          <offset>70</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>match_points_for</column_name>
          <offset>72</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>match_points_against</column_name>
          <offset>74</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>match_tries_conceded</column_name>
          <offset>76</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>match_tries_scored</column_name>
          <offset>78</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>match_penalties_conceded</column_name>
          <offset>80</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_selected</column_name>
          <offset>82</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_mode_state</name>
      <row_size>20</row_size>
      <columns>
        <ColumnDef>
          <column_name>game_difficulty</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>game_length</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>substitution_mode</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>competition_id</column_name>
          <offset>16</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>ranfurly_team_id</column_name>
          <offset>18</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_trophy</name>
      <row_size>40</row_size>
      <columns>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>4</offset>
          <size>26</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>filename</column_name>
          <offset>30</offset>
          <size>10</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_contract_negotiation</name>
      <row_size>24</row_size>
      <columns>
        <ColumnDef>
          <column_name>category</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>value</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_id</column_name>
          <offset>12</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_id</column_name>
          <offset>14</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_attempts</column_name>
          <offset>16</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>days_of_deliberation_left</column_name>
          <offset>18</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_seasons</column_name>
          <offset>20</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>result</column_name>
          <offset>22</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_country</name>
      <row_size>44</row_size>
      <columns>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>4</offset>
          <size>17</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>abbreviation</column_name>
          <offset>21</offset>
          <size>5</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>flag_filename</column_name>
          <offset>26</offset>
          <size>15</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_emails</name>
      <row_size>148</row_size>
      <columns>
        <ColumnDef>
          <column_name>game_mode</column_name>
          <offset>4</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>career_mode</column_name>
          <offset>5</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>type</column_name>
          <offset>6</offset>
          <size>33</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>subject</column_name>
          <offset>39</offset>
          <size>40</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sender</column_name>
          <offset>79</offset>
          <size>25</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>content</column_name>
          <offset>104</offset>
          <size>44</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_finals_format</name>
      <row_size>60</row_size>
      <columns>
        <ColumnDef>
          <column_name>enum</column_name>
          <offset>4</offset>
          <size>24</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>28</offset>
          <size>32</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_franchise_constants</name>
      <row_size>148</row_size>
      <columns>
        <ColumnDef>
          <column_name>original_club_factor</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>rest_injury_susceptability_decrement</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>nrl_salary_cap</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>nrl_salary_floor</column_name>
          <offset>16</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sl_salary_cap</column_name>
          <offset>20</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sl_salary_floor</column_name>
          <offset>24</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>monthly_spam_chance</column_name>
          <offset>28</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>base_sponsorship_multiplier</column_name>
          <offset>32</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>minimum_monthly_merchandise</column_name>
          <offset>36</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>maximum_monthly_merchandise</column_name>
          <offset>40</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>base_non_contracted_player_match_fee_amount</column_name>
          <offset>44</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>base_stadium_monthly_rental</column_name>
          <offset>48</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>base_suspension_fee</column_name>
          <offset>52</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>base_medical_fee</column_name>
          <offset>56</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>ticket_price</column_name>
          <offset>60</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>home_team_ticket_revenue_share</column_name>
          <offset>64</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_successful_tackles_variance</column_name>
          <offset>68</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_yellow_card_chance</column_name>
          <offset>72</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_red_card_chance</column_name>
          <offset>76</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_first_injury_chance</column_name>
          <offset>80</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_second_injury_chance</column_name>
          <offset>84</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>suspensions_per_season_allowed</column_name>
          <offset>88</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>suspensions_ever_allowed</column_name>
          <offset>90</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_matches_in_half_a_season</column_name>
          <offset>92</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_matches_to_clear_suspension</column_name>
          <offset>94</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>max_stat_boost_from_training</column_name>
          <offset>96</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>points_lost_per_day</column_name>
          <offset>98</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>add_to_training_constant</column_name>
          <offset>100</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>contract_deliberation_days</column_name>
          <offset>102</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>max_num_contracted_players</column_name>
          <offset>104</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>max_num_years_idle</column_name>
          <offset>106</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>max_num_games_for_ncp</column_name>
          <offset>108</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_min_penalties</column_name>
          <offset>110</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_max_penalties</column_name>
          <offset>112</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_min_tackles</column_name>
          <offset>114</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_max_tackles</column_name>
          <offset>116</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_min_line_breaks</column_name>
          <offset>118</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_max_line_breaks</column_name>
          <offset>120</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_min_offloads</column_name>
          <offset>122</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_max_offloads</column_name>
          <offset>124</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_min_offloads_attempted</column_name>
          <offset>126</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_min_handling_errors</column_name>
          <offset>128</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_max_handling_errors</column_name>
          <offset>130</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_min_kicks</column_name>
          <offset>132</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_max_kicks</column_name>
          <offset>134</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_min_dummy_runs</column_name>
          <offset>136</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_max_dummy_runs</column_name>
          <offset>138</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_min_kick_metres_gained_per_kick</column_name>
          <offset>140</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_max_kick_meters_gained_per_kick</column_name>
          <offset>142</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_min_run_metres_gained</column_name>
          <offset>144</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sim_max_run_metres_gained</column_name>
          <offset>146</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_franchise_def</name>
      <row_size>40</row_size>
      <columns>
        <ColumnDef>
          <column_name>start_date</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>salary_cap</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>financial_scale</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>16</offset>
          <size>23</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_golden_point_type</name>
      <row_size>28</row_size>
      <columns>
        <ColumnDef>
          <column_name>enum</column_name>
          <offset>4</offset>
          <size>23</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_injuries</name>
      <row_size>40</row_size>
      <columns>
        <ColumnDef>
          <column_name>min_days</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>max_days</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>description</column_name>
          <offset>8</offset>
          <size>29</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_lineup</name>
      <row_size>20</row_size>
      <columns>
        <ColumnDef>
          <column_name>position_id</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>value</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_id</column_name>
          <offset>12</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_id</column_name>
          <offset>14</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_seasons</column_name>
          <offset>16</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_lineup_new</name>
      <row_size>776</row_size>
      <columns>
        <ColumnDef>
          <column_name>player1_position</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player2_position</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player3_position</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player4_position</column_name>
          <offset>16</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player5_position</column_name>
          <offset>20</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player6_position</column_name>
          <offset>24</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player7_position</column_name>
          <offset>28</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player8_position</column_name>
          <offset>32</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player9_position</column_name>
          <offset>36</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player10_position</column_name>
          <offset>40</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player11_position</column_name>
          <offset>44</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player12_position</column_name>
          <offset>48</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player13_position</column_name>
          <offset>52</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player14_position</column_name>
          <offset>56</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player15_position</column_name>
          <offset>60</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player16_position</column_name>
          <offset>64</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player17_position</column_name>
          <offset>68</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player18_position</column_name>
          <offset>72</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player19_position</column_name>
          <offset>76</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player20_position</column_name>
          <offset>80</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player21_position</column_name>
          <offset>84</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player22_position</column_name>
          <offset>88</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player23_position</column_name>
          <offset>92</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player24_position</column_name>
          <offset>96</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player25_position</column_name>
          <offset>100</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player26_position</column_name>
          <offset>104</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player27_position</column_name>
          <offset>108</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player28_position</column_name>
          <offset>112</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player29_position</column_name>
          <offset>116</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player30_position</column_name>
          <offset>120</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player31_position</column_name>
          <offset>124</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player32_position</column_name>
          <offset>128</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player33_position</column_name>
          <offset>132</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player34_position</column_name>
          <offset>136</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player35_position</column_name>
          <offset>140</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player36_position</column_name>
          <offset>144</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player37_position</column_name>
          <offset>148</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player38_position</column_name>
          <offset>152</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player39_position</column_name>
          <offset>156</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player40_position</column_name>
          <offset>160</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player41_position</column_name>
          <offset>164</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player42_position</column_name>
          <offset>168</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player43_position</column_name>
          <offset>172</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player44_position</column_name>
          <offset>176</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player45_position</column_name>
          <offset>180</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player46_position</column_name>
          <offset>184</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player47_position</column_name>
          <offset>188</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player48_position</column_name>
          <offset>192</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player49_position</column_name>
          <offset>196</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player50_position</column_name>
          <offset>200</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player51_position</column_name>
          <offset>204</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player52_position</column_name>
          <offset>208</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player53_position</column_name>
          <offset>212</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player54_position</column_name>
          <offset>216</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player55_position</column_name>
          <offset>220</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player56_position</column_name>
          <offset>224</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player57_position</column_name>
          <offset>228</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player58_position</column_name>
          <offset>232</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player59_position</column_name>
          <offset>236</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player60_position</column_name>
          <offset>240</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player61_position</column_name>
          <offset>244</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player62_position</column_name>
          <offset>248</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player63_position</column_name>
          <offset>252</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player64_position</column_name>
          <offset>256</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player65_position</column_name>
          <offset>260</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player66_position</column_name>
          <offset>264</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player67_position</column_name>
          <offset>268</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player68_position</column_name>
          <offset>272</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player69_position</column_name>
          <offset>276</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player70_position</column_name>
          <offset>280</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player1_value</column_name>
          <offset>284</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player2_value</column_name>
          <offset>288</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player3_value</column_name>
          <offset>292</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player4_value</column_name>
          <offset>296</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player5_value</column_name>
          <offset>300</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player6_value</column_name>
          <offset>304</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player7_value</column_name>
          <offset>308</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player8_value</column_name>
          <offset>312</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player9_value</column_name>
          <offset>316</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player10_value</column_name>
          <offset>320</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player11_value</column_name>
          <offset>324</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player12_value</column_name>
          <offset>328</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player13_value</column_name>
          <offset>332</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player14_value</column_name>
          <offset>336</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player15_value</column_name>
          <offset>340</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player16_value</column_name>
          <offset>344</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player17_value</column_name>
          <offset>348</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player18_value</column_name>
          <offset>352</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player19_value</column_name>
          <offset>356</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player20_value</column_name>
          <offset>360</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player21_value</column_name>
          <offset>364</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player22_value</column_name>
          <offset>368</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player23_value</column_name>
          <offset>372</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player24_value</column_name>
          <offset>376</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player25_value</column_name>
          <offset>380</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player26_value</column_name>
          <offset>384</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player27_value</column_name>
          <offset>388</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player28_value</column_name>
          <offset>392</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player29_value</column_name>
          <offset>396</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player30_value</column_name>
          <offset>400</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player31_value</column_name>
          <offset>404</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player32_value</column_name>
          <offset>408</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player33_value</column_name>
          <offset>412</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player34_value</column_name>
          <offset>416</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player35_value</column_name>
          <offset>420</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player36_value</column_name>
          <offset>424</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player37_value</column_name>
          <offset>428</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player38_value</column_name>
          <offset>432</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player39_value</column_name>
          <offset>436</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player40_value</column_name>
          <offset>440</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player41_value</column_name>
          <offset>444</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player42_value</column_name>
          <offset>448</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player43_value</column_name>
          <offset>452</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player44_value</column_name>
          <offset>456</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player45_value</column_name>
          <offset>460</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player46_value</column_name>
          <offset>464</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player47_value</column_name>
          <offset>468</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player48_value</column_name>
          <offset>472</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player49_value</column_name>
          <offset>476</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player50_value</column_name>
          <offset>480</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player51_value</column_name>
          <offset>484</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player52_value</column_name>
          <offset>488</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player53_value</column_name>
          <offset>492</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player54_value</column_name>
          <offset>496</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player55_value</column_name>
          <offset>500</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player56_value</column_name>
          <offset>504</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player57_value</column_name>
          <offset>508</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player58_value</column_name>
          <offset>512</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player59_value</column_name>
          <offset>516</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player60_value</column_name>
          <offset>520</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player61_value</column_name>
          <offset>524</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player62_value</column_name>
          <offset>528</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player63_value</column_name>
          <offset>532</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player64_value</column_name>
          <offset>536</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player65_value</column_name>
          <offset>540</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player66_value</column_name>
          <offset>544</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player67_value</column_name>
          <offset>548</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player68_value</column_name>
          <offset>552</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player69_value</column_name>
          <offset>556</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player70_value</column_name>
          <offset>560</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player1_id</column_name>
          <offset>564</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player2_id</column_name>
          <offset>566</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player3_id</column_name>
          <offset>568</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player4_id</column_name>
          <offset>570</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player5_id</column_name>
          <offset>572</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player6_id</column_name>
          <offset>574</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player7_id</column_name>
          <offset>576</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player8_id</column_name>
          <offset>578</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player9_id</column_name>
          <offset>580</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player10_id</column_name>
          <offset>582</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player11_id</column_name>
          <offset>584</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player12_id</column_name>
          <offset>586</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player13_id</column_name>
          <offset>588</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player14_id</column_name>
          <offset>590</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player15_id</column_name>
          <offset>592</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player16_id</column_name>
          <offset>594</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player17_id</column_name>
          <offset>596</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player18_id</column_name>
          <offset>598</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player19_id</column_name>
          <offset>600</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player20_id</column_name>
          <offset>602</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player21_id</column_name>
          <offset>604</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player22_id</column_name>
          <offset>606</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player23_id</column_name>
          <offset>608</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player24_id</column_name>
          <offset>610</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player25_id</column_name>
          <offset>612</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player26_id</column_name>
          <offset>614</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player27_id</column_name>
          <offset>616</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player28_id</column_name>
          <offset>618</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player29_id</column_name>
          <offset>620</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player30_id</column_name>
          <offset>622</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player31_id</column_name>
          <offset>624</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player32_id</column_name>
          <offset>626</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player33_id</column_name>
          <offset>628</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player34_id</column_name>
          <offset>630</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player35_id</column_name>
          <offset>632</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player36_id</column_name>
          <offset>634</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player37_id</column_name>
          <offset>636</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player38_id</column_name>
          <offset>638</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player39_id</column_name>
          <offset>640</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player40_id</column_name>
          <offset>642</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player41_id</column_name>
          <offset>644</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player42_id</column_name>
          <offset>646</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player43_id</column_name>
          <offset>648</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player44_id</column_name>
          <offset>650</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player45_id</column_name>
          <offset>652</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player46_id</column_name>
          <offset>654</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player47_id</column_name>
          <offset>656</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player48_id</column_name>
          <offset>658</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player49_id</column_name>
          <offset>660</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player50_id</column_name>
          <offset>662</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player51_id</column_name>
          <offset>664</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player52_id</column_name>
          <offset>666</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player53_id</column_name>
          <offset>668</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player54_id</column_name>
          <offset>670</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player55_id</column_name>
          <offset>672</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player56_id</column_name>
          <offset>674</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player57_id</column_name>
          <offset>676</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player58_id</column_name>
          <offset>678</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player59_id</column_name>
          <offset>680</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player60_id</column_name>
          <offset>682</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player61_id</column_name>
          <offset>684</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player62_id</column_name>
          <offset>686</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player63_id</column_name>
          <offset>688</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player64_id</column_name>
          <offset>690</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player65_id</column_name>
          <offset>692</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player66_id</column_name>
          <offset>694</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player67_id</column_name>
          <offset>696</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player68_id</column_name>
          <offset>698</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player69_id</column_name>
          <offset>700</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player70_id</column_name>
          <offset>702</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player1_seasons</column_name>
          <offset>704</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player2_seasons</column_name>
          <offset>705</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player3_seasons</column_name>
          <offset>706</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player4_seasons</column_name>
          <offset>707</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player5_seasons</column_name>
          <offset>708</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player6_seasons</column_name>
          <offset>709</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player7_seasons</column_name>
          <offset>710</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player8_seasons</column_name>
          <offset>711</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player9_seasons</column_name>
          <offset>712</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player10_seasons</column_name>
          <offset>713</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player11_seasons</column_name>
          <offset>714</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player12_seasons</column_name>
          <offset>715</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player13_seasons</column_name>
          <offset>716</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player14_seasons</column_name>
          <offset>717</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player15_seasons</column_name>
          <offset>718</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player16_seasons</column_name>
          <offset>719</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player17_seasons</column_name>
          <offset>720</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player18_seasons</column_name>
          <offset>721</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player19_seasons</column_name>
          <offset>722</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player20_seasons</column_name>
          <offset>723</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player21_seasons</column_name>
          <offset>724</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player22_seasons</column_name>
          <offset>725</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player23_seasons</column_name>
          <offset>726</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player24_seasons</column_name>
          <offset>727</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player25_seasons</column_name>
          <offset>728</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player26_seasons</column_name>
          <offset>729</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player27_seasons</column_name>
          <offset>730</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player28_seasons</column_name>
          <offset>731</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player29_seasons</column_name>
          <offset>732</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player30_seasons</column_name>
          <offset>733</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player31_seasons</column_name>
          <offset>734</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player32_seasons</column_name>
          <offset>735</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player33_seasons</column_name>
          <offset>736</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player34_seasons</column_name>
          <offset>737</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player35_seasons</column_name>
          <offset>738</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player36_seasons</column_name>
          <offset>739</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player37_seasons</column_name>
          <offset>740</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player38_seasons</column_name>
          <offset>741</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player39_seasons</column_name>
          <offset>742</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player40_seasons</column_name>
          <offset>743</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player41_seasons</column_name>
          <offset>744</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player42_seasons</column_name>
          <offset>745</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player43_seasons</column_name>
          <offset>746</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player44_seasons</column_name>
          <offset>747</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player45_seasons</column_name>
          <offset>748</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player46_seasons</column_name>
          <offset>749</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player47_seasons</column_name>
          <offset>750</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player48_seasons</column_name>
          <offset>751</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player49_seasons</column_name>
          <offset>752</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player50_seasons</column_name>
          <offset>753</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player51_seasons</column_name>
          <offset>754</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player52_seasons</column_name>
          <offset>755</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player53_seasons</column_name>
          <offset>756</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player54_seasons</column_name>
          <offset>757</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player55_seasons</column_name>
          <offset>758</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player56_seasons</column_name>
          <offset>759</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player57_seasons</column_name>
          <offset>760</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player58_seasons</column_name>
          <offset>761</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player59_seasons</column_name>
          <offset>762</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player60_seasons</column_name>
          <offset>763</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player61_seasons</column_name>
          <offset>764</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player62_seasons</column_name>
          <offset>765</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player63_seasons</column_name>
          <offset>766</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player64_seasons</column_name>
          <offset>767</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player65_seasons</column_name>
          <offset>768</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player66_seasons</column_name>
          <offset>769</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player67_seasons</column_name>
          <offset>770</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player68_seasons</column_name>
          <offset>771</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player69_seasons</column_name>
          <offset>772</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player70_seasons</column_name>
          <offset>773</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_pending_lineup</name>
      <row_size>16</row_size>
      <columns>
        <ColumnDef>
          <column_name>value</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_id</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_id</column_name>
          <offset>10</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_seasons</column_name>
          <offset>12</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>position</column_name>
          <offset>14</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_pending_lineup_new</name>
      <row_size>444</row_size>
      <columns>
        <ColumnDef>
          <column_name>player1_position</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player2_position</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player3_position</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player4_position</column_name>
          <offset>16</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player5_position</column_name>
          <offset>20</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player6_position</column_name>
          <offset>24</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player7_position</column_name>
          <offset>28</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player8_position</column_name>
          <offset>32</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player9_position</column_name>
          <offset>36</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player10_position</column_name>
          <offset>40</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player11_position</column_name>
          <offset>44</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player12_position</column_name>
          <offset>48</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player13_position</column_name>
          <offset>52</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player14_position</column_name>
          <offset>56</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player15_position</column_name>
          <offset>60</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player16_position</column_name>
          <offset>64</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player17_position</column_name>
          <offset>68</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player18_position</column_name>
          <offset>72</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player19_position</column_name>
          <offset>76</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player20_position</column_name>
          <offset>80</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player21_position</column_name>
          <offset>84</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player22_position</column_name>
          <offset>88</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player23_position</column_name>
          <offset>92</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player24_position</column_name>
          <offset>96</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player25_position</column_name>
          <offset>100</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player26_position</column_name>
          <offset>104</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player27_position</column_name>
          <offset>108</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player28_position</column_name>
          <offset>112</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player29_position</column_name>
          <offset>116</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player30_position</column_name>
          <offset>120</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player31_position</column_name>
          <offset>124</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player32_position</column_name>
          <offset>128</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player33_position</column_name>
          <offset>132</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player34_position</column_name>
          <offset>136</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player35_position</column_name>
          <offset>140</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player36_position</column_name>
          <offset>144</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player37_position</column_name>
          <offset>148</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player38_position</column_name>
          <offset>152</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player39_position</column_name>
          <offset>156</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player40_position</column_name>
          <offset>160</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player1_value</column_name>
          <offset>164</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player2_value</column_name>
          <offset>168</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player3_value</column_name>
          <offset>172</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player4_value</column_name>
          <offset>176</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player5_value</column_name>
          <offset>180</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player6_value</column_name>
          <offset>184</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player7_value</column_name>
          <offset>188</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player8_value</column_name>
          <offset>192</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player9_value</column_name>
          <offset>196</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player10_value</column_name>
          <offset>200</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player11_value</column_name>
          <offset>204</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player12_value</column_name>
          <offset>208</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player13_value</column_name>
          <offset>212</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player14_value</column_name>
          <offset>216</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player15_value</column_name>
          <offset>220</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player16_value</column_name>
          <offset>224</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player17_value</column_name>
          <offset>228</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player18_value</column_name>
          <offset>232</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player19_value</column_name>
          <offset>236</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player20_value</column_name>
          <offset>240</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player21_value</column_name>
          <offset>244</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player22_value</column_name>
          <offset>248</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player23_value</column_name>
          <offset>252</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player24_value</column_name>
          <offset>256</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player25_value</column_name>
          <offset>260</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player26_value</column_name>
          <offset>264</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player27_value</column_name>
          <offset>268</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player28_value</column_name>
          <offset>272</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player29_value</column_name>
          <offset>276</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player30_value</column_name>
          <offset>280</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player31_value</column_name>
          <offset>284</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player32_value</column_name>
          <offset>288</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player33_value</column_name>
          <offset>292</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player34_value</column_name>
          <offset>296</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player35_value</column_name>
          <offset>300</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player36_value</column_name>
          <offset>304</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player37_value</column_name>
          <offset>308</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player38_value</column_name>
          <offset>312</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player39_value</column_name>
          <offset>316</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player40_value</column_name>
          <offset>320</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player1_id</column_name>
          <offset>324</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player2_id</column_name>
          <offset>326</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player3_id</column_name>
          <offset>328</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player4_id</column_name>
          <offset>330</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player5_id</column_name>
          <offset>332</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player6_id</column_name>
          <offset>334</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player7_id</column_name>
          <offset>336</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player8_id</column_name>
          <offset>338</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player9_id</column_name>
          <offset>340</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player10_id</column_name>
          <offset>342</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player11_id</column_name>
          <offset>344</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player12_id</column_name>
          <offset>346</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player13_id</column_name>
          <offset>348</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player14_id</column_name>
          <offset>350</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player15_id</column_name>
          <offset>352</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player16_id</column_name>
          <offset>354</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player17_id</column_name>
          <offset>356</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player18_id</column_name>
          <offset>358</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player19_id</column_name>
          <offset>360</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player20_id</column_name>
          <offset>362</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player21_id</column_name>
          <offset>364</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player22_id</column_name>
          <offset>366</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player23_id</column_name>
          <offset>368</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player24_id</column_name>
          <offset>370</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player25_id</column_name>
          <offset>372</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player26_id</column_name>
          <offset>374</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player27_id</column_name>
          <offset>376</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player28_id</column_name>
          <offset>378</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player29_id</column_name>
          <offset>380</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player30_id</column_name>
          <offset>382</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player31_id</column_name>
          <offset>384</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player32_id</column_name>
          <offset>386</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player33_id</column_name>
          <offset>388</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player34_id</column_name>
          <offset>390</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player35_id</column_name>
          <offset>392</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player36_id</column_name>
          <offset>394</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player37_id</column_name>
          <offset>396</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player38_id</column_name>
          <offset>398</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player39_id</column_name>
          <offset>400</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player40_id</column_name>
          <offset>402</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player1_seasons</column_name>
          <offset>404</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player2_seasons</column_name>
          <offset>405</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player3_seasons</column_name>
          <offset>406</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player4_seasons</column_name>
          <offset>407</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player5_seasons</column_name>
          <offset>408</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player6_seasons</column_name>
          <offset>409</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player7_seasons</column_name>
          <offset>410</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player8_seasons</column_name>
          <offset>411</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player9_seasons</column_name>
          <offset>412</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player10_seasons</column_name>
          <offset>413</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player11_seasons</column_name>
          <offset>414</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player12_seasons</column_name>
          <offset>415</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player13_seasons</column_name>
          <offset>416</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player14_seasons</column_name>
          <offset>417</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player15_seasons</column_name>
          <offset>418</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player16_seasons</column_name>
          <offset>419</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player17_seasons</column_name>
          <offset>420</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player18_seasons</column_name>
          <offset>421</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player19_seasons</column_name>
          <offset>422</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player20_seasons</column_name>
          <offset>423</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player21_seasons</column_name>
          <offset>424</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player22_seasons</column_name>
          <offset>425</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player23_seasons</column_name>
          <offset>426</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player24_seasons</column_name>
          <offset>427</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player25_seasons</column_name>
          <offset>428</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player26_seasons</column_name>
          <offset>429</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player27_seasons</column_name>
          <offset>430</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player28_seasons</column_name>
          <offset>431</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player29_seasons</column_name>
          <offset>432</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player30_seasons</column_name>
          <offset>433</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player31_seasons</column_name>
          <offset>434</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player32_seasons</column_name>
          <offset>435</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player33_seasons</column_name>
          <offset>436</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player34_seasons</column_name>
          <offset>437</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player35_seasons</column_name>
          <offset>438</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player36_seasons</column_name>
          <offset>439</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player37_seasons</column_name>
          <offset>440</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player38_seasons</column_name>
          <offset>441</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player39_seasons</column_name>
          <offset>442</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player40_seasons</column_name>
          <offset>443</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_player_extra</name>
      <row_size>40</row_size>
      <columns>
        <ColumnDef>
          <column_name>club_team</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>last_club</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pending_team</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_days_injured</column_name>
          <offset>10</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_matches_unsuspended</column_name>
          <offset>12</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>suspensions_total</column_name>
          <offset>14</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>suspensions_this_season</column_name>
          <offset>16</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>international_team</column_name>
          <offset>18</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>alternate_club_team</column_name>
          <offset>20</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>market_value</column_name>
          <offset>22</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>replaced_player</column_name>
          <offset>24</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>years_idle</column_name>
          <offset>26</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>contract_pending</column_name>
          <offset>27</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>frustration_level</column_name>
          <offset>28</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>has_been_frustrated</column_name>
          <offset>29</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>send_last_chance_email</column_name>
          <offset>30</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>has_sent_last_chance_email</column_name>
          <offset>31</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>ai_trading_suspended_days</column_name>
          <offset>32</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_first_grade_games_last_season</column_name>
          <offset>33</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>days_to_wait</column_name>
          <offset>34</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>injury</column_name>
          <offset>35</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>suspension</column_name>
          <offset>36</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_matches_suspended</column_name>
          <offset>37</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_player_link</name>
      <row_size>8</row_size>
      <columns>
        <ColumnDef>
          <column_name>original_player</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>linked_player</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_player_performance_values</name>
      <row_size>100</row_size>
      <columns>
        <ColumnDef>
          <column_name>try_cap</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>try_val</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>running_meters_cap</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>running_meters_val</column_name>
          <offset>16</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>kicking_meters_cap</column_name>
          <offset>20</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>kicking_meters_val</column_name>
          <offset>24</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tackles_cap</column_name>
          <offset>28</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tackles_val</column_name>
          <offset>32</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>line_breaks_cap</column_name>
          <offset>36</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>line_breaks_val</column_name>
          <offset>40</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>offloads_cap</column_name>
          <offset>44</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>offloads_val</column_name>
          <offset>48</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>handling_errors_cap</column_name>
          <offset>52</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>handling_errors_val</column_name>
          <offset>56</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>penalties_conceded_cap</column_name>
          <offset>60</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>penalties_conceded_val</column_name>
          <offset>64</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>miss_tackles_cap</column_name>
          <offset>68</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>miss_tackles_val</column_name>
          <offset>72</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>yellow_card_cap</column_name>
          <offset>76</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>yellow_card_val</column_name>
          <offset>80</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>red_card_cap</column_name>
          <offset>84</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>red_card_val</column_name>
          <offset>88</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>goal_kick_cap</column_name>
          <offset>92</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>drop_goal_cap</column_name>
          <offset>96</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_player_position</name>
      <row_size>60</row_size>
      <columns>
        <ColumnDef>
          <column_name>enum</column_name>
          <offset>4</offset>
          <size>38</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>42</offset>
          <size>18</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_player_proportions</name>
      <row_size>36</row_size>
      <columns>
        <ColumnDef>
          <column_name>arm_length</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoulder_width</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>leg_length</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>neck_length</column_name>
          <offset>16</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>hip_width</column_name>
          <offset>20</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>spine_length</column_name>
          <offset>24</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>28</offset>
          <size>7</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_player_records</name>
      <row_size>56</row_size>
      <columns>
        <ColumnDef>
          <column_name>type_id</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>competition_id</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>year_id</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>country_id</column_name>
          <offset>16</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_id</column_name>
          <offset>20</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>value</column_name>
          <offset>24</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>range</column_name>
          <offset>28</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_tests</column_name>
          <offset>32</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>year2_id</column_name>
          <offset>36</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_name</column_name>
          <offset>40</offset>
          <size>14</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_preliminaries_format</name>
      <row_size>60</row_size>
      <columns>
        <ColumnDef>
          <column_name>enum</column_name>
          <offset>4</offset>
          <size>24</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>28</offset>
          <size>32</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_pro_goals</name>
      <row_size>76</row_size>
      <columns>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>4</offset>
          <size>32</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>description</column_name>
          <offset>36</offset>
          <size>38</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>compare</column_name>
          <offset>74</offset>
          <size>2</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_pro_goals_general</name>
      <row_size>264</row_size>
      <columns>
        <ColumnDef>
          <column_name>attribute1</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>attribute2</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>attribute3</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_lhp_prob</column_name>
          <offset>16</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_lhp_avg_min</column_name>
          <offset>18</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_lhp_avg_max</column_name>
          <offset>20</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_hooker_prob</column_name>
          <offset>22</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_hooker_avg_min</column_name>
          <offset>24</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_hooker_avg_max</column_name>
          <offset>26</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_thp_prob</column_name>
          <offset>28</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_thp_avg_min</column_name>
          <offset>30</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_thp_avg_max</column_name>
          <offset>32</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_shalf_prob</column_name>
          <offset>34</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_shalf_avg_min</column_name>
          <offset>36</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_shalf_avg_max</column_name>
          <offset>38</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_fly_prob</column_name>
          <offset>40</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_fly_avg_min</column_name>
          <offset>42</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_fly_avg_max</column_name>
          <offset>44</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_ic_prob</column_name>
          <offset>46</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_ic_avg_min</column_name>
          <offset>48</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_ic_avg_max</column_name>
          <offset>50</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_fb_prob</column_name>
          <offset>52</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_fb_avg_min</column_name>
          <offset>54</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_fb_avg_max</column_name>
          <offset>56</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_lhp_prob</column_name>
          <offset>58</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_lhp_avg_min</column_name>
          <offset>60</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_lhp_avg_max</column_name>
          <offset>62</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_hooker_prob</column_name>
          <offset>64</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_hooker_avg_min</column_name>
          <offset>66</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_hooker_avg_max</column_name>
          <offset>68</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_thp_prob</column_name>
          <offset>70</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_thp_avg_min</column_name>
          <offset>72</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_thp_avg_max</column_name>
          <offset>74</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_4lock_prob</column_name>
          <offset>76</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_4lock_avg_min</column_name>
          <offset>78</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_4lock_avg_max</column_name>
          <offset>80</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_5lock_prob</column_name>
          <offset>82</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_5lock_avg_min</column_name>
          <offset>84</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_5lock_avg_max</column_name>
          <offset>86</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_bflank_prob</column_name>
          <offset>88</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_bflank_avg_min</column_name>
          <offset>90</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_bflank_avg_max</column_name>
          <offset>92</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_oflank_prob</column_name>
          <offset>94</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_oflank_avg_min</column_name>
          <offset>96</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_oflank_avg_max</column_name>
          <offset>98</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_no8_prob</column_name>
          <offset>100</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_no8_avg_min</column_name>
          <offset>102</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_no8_avg_max</column_name>
          <offset>104</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_shalf_prob</column_name>
          <offset>106</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_shalf_avg_min</column_name>
          <offset>108</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_shalf_avg_max</column_name>
          <offset>110</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_fly_prob</column_name>
          <offset>112</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_fly_avg_min</column_name>
          <offset>114</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_fly_avg_max</column_name>
          <offset>116</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_lwing_prob</column_name>
          <offset>118</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_lwing_avg_min</column_name>
          <offset>120</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_lwing_avg_max</column_name>
          <offset>122</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_ic_prob</column_name>
          <offset>124</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_ic_avg_min</column_name>
          <offset>126</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_ic_avg_max</column_name>
          <offset>128</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_oc_prob</column_name>
          <offset>130</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_oc_avg_min</column_name>
          <offset>132</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_oc_avg_max</column_name>
          <offset>134</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_rwing_prob</column_name>
          <offset>136</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_rwing_avg_min</column_name>
          <offset>138</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_rwing_avg_max</column_name>
          <offset>140</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_fb_prob</column_name>
          <offset>142</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_fb_avg_min</column_name>
          <offset>144</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r15_fb_avg_max</column_name>
          <offset>146</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>148</offset>
          <size>30</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>description_short</column_name>
          <offset>178</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>description_long</column_name>
          <offset>213</offset>
          <size>34</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>compare</column_name>
          <offset>247</offset>
          <size>2</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>type</column_name>
          <offset>249</offset>
          <size>13</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_records</name>
      <row_size>48</row_size>
      <columns>
        <ColumnDef>
          <column_name>comp_def_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>value</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_id</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>year</column_name>
          <offset>10</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>record_type</column_name>
          <offset>12</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_name</column_name>
          <offset>13</offset>
          <size>32</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_rep_area</name>
      <row_size>20</row_size>
      <columns>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>4</offset>
          <size>15</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_rep_area_country</name>
      <row_size>8</row_size>
      <columns>
        <ColumnDef>
          <column_name>reparea_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>country_id</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_schema_version</name>
      <row_size>28</row_size>
      <columns>
        <ColumnDef>
          <column_name>version_no</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>description</column_name>
          <offset>8</offset>
          <size>17</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_stats_player</name>
      <row_size>32</row_size>
      <columns>
        <ColumnDef>
          <column_name>points_scored</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>successful_conversion_attempts</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>successful_field_goals</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>successful_penalty_goals</column_name>
          <offset>16</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>successful_try_attempts</column_name>
          <offset>20</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>competition_id</column_name>
          <offset>24</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>match_id</column_name>
          <offset>26</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_id</column_name>
          <offset>28</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_id</column_name>
          <offset>30</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_stats_team</name>
      <row_size>20</row_size>
      <columns>
        <ColumnDef>
          <column_name>score</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>opponent_score</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>competition_id</column_name>
          <offset>12</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>match_id</column_name>
          <offset>14</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_id</column_name>
          <offset>16</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_suspension_offences</name>
      <row_size>44</row_size>
      <columns>
        <ColumnDef>
          <column_name>innocent_min</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>innocent_max</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>guilty_min</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>guilty_max</column_name>
          <offset>10</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>description</column_name>
          <offset>12</offset>
          <size>30</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_team_commentary_name</name>
      <row_size>28</row_size>
      <columns>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>4</offset>
          <size>22</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_team_link</name>
      <row_size>8</row_size>
      <columns>
        <ColumnDef>
          <column_name>original_team</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>linked_team</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_team_records</name>
      <row_size>56</row_size>
      <columns>
        <ColumnDef>
          <column_name>type_id</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>competition_id</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>year</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>country_id</column_name>
          <offset>16</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_id</column_name>
          <offset>20</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>opp_team_id</column_name>
          <offset>24</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>value</column_name>
          <offset>28</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>opp_value</column_name>
          <offset>32</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>range</column_name>
          <offset>36</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>city_id</column_name>
          <offset>40</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stadium_id</column_name>
          <offset>44</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>statistic_value</column_name>
          <offset>48</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_name</column_name>
          <offset>52</offset>
          <size>2</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>opp_team_name</column_name>
          <offset>54</offset>
          <size>2</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_ticker_messages</name>
      <row_size>316</row_size>
      <columns>
        <ColumnDef>
          <column_name>player_db_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>type</column_name>
          <offset>6</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_relevant</column_name>
          <offset>7</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>days_to_expire</column_name>
          <offset>8</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>message</column_name>
          <offset>9</offset>
          <size>32</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>replacements</column_name>
          <offset>41</offset>
          <size>256</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>date</column_name>
          <offset>297</offset>
          <size>16</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_ui_team</name>
      <row_size>12</row_size>
      <columns>
        <ColumnDef>
          <column_name>player_id_1</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_id_2</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_id_3</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_id_4</column_name>
          <offset>10</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_player_morphtargets</name>
      <row_size>512</row_size>
      <columns>
        <ColumnDef>
          <column_name>eyePositionHeight</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eyePositionWidth</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eyeRotation</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eyeScale</column_name>
          <offset>16</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eyeTopShapeSkew</column_name>
          <offset>20</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eyeBottomShapeSkew</column_name>
          <offset>24</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eyeTopShapeScale</column_name>
          <offset>28</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eyeBottomShapeScale</column_name>
          <offset>32</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eyeDepth</column_name>
          <offset>36</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eyeLidIntensity</column_name>
          <offset>40</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eyeLidHeight</column_name>
          <offset>44</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eyeLidDroop</column_name>
          <offset>48</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mouthSizeWidth</column_name>
          <offset>52</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mouthSizeHeight</column_name>
          <offset>56</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mouthPositionHorizontal</column_name>
          <offset>60</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mouthPositionVertical</column_name>
          <offset>64</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mouthDepth</column_name>
          <offset>68</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mouthSideHeight</column_name>
          <offset>72</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mouthCornersWidth</column_name>
          <offset>76</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mouthCornersHeight</column_name>
          <offset>80</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>lipTopWidth</column_name>
          <offset>84</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>lipTopHeight</column_name>
          <offset>88</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>lipBottomWidth</column_name>
          <offset>92</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>lipBottomHeight</column_name>
          <offset>96</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>philtrumWidth</column_name>
          <offset>100</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>philtrumHeight</column_name>
          <offset>104</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mouthJowl</column_name>
          <offset>108</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>browInnerPosHeight</column_name>
          <offset>112</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>browInnerPosWidth</column_name>
          <offset>116</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>browMiddlePosHeight</column_name>
          <offset>120</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>browMiddlePosWidth</column_name>
          <offset>124</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>browOuterPosHeight</column_name>
          <offset>128</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>browOuterPosWidth</column_name>
          <offset>132</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>browInnerDepth</column_name>
          <offset>136</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>browMiddleDepth</column_name>
          <offset>140</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>browOuterDepth</column_name>
          <offset>144</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>browIntensity</column_name>
          <offset>148</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>cheekBoneWidth</column_name>
          <offset>152</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>cheekBoneHeight</column_name>
          <offset>156</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>cheekBoneDepth</column_name>
          <offset>160</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>cheekPositionWidth</column_name>
          <offset>164</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>cheekPositionHeight</column_name>
          <offset>168</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>cheekDepth</column_name>
          <offset>172</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>cheekGaunt</column_name>
          <offset>176</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>malarFat</column_name>
          <offset>180</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>jawSizeHeight</column_name>
          <offset>184</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>jawSizeWidth</column_name>
          <offset>188</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>jawShapePuffiness</column_name>
          <offset>192</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>jawDepth</column_name>
          <offset>196</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>masseterSizeHeight</column_name>
          <offset>200</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>masseterSizeWidth</column_name>
          <offset>204</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>masseterSizeDepth</column_name>
          <offset>208</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>underbite</column_name>
          <offset>212</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>jowl</column_name>
          <offset>216</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseSizeHeight</column_name>
          <offset>220</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseSizeWidth</column_name>
          <offset>224</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseDepth</column_name>
          <offset>228</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseRootPositionHeight</column_name>
          <offset>232</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseRootPositionWidth</column_name>
          <offset>236</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseRootPositionDepth</column_name>
          <offset>240</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseBridgePositionHeight</column_name>
          <offset>244</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseBridgePositionWidth</column_name>
          <offset>248</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseBridgePositionDepth</column_name>
          <offset>252</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseBreak</column_name>
          <offset>256</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sinusSize</column_name>
          <offset>260</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseTipSizeWidth</column_name>
          <offset>264</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseTipSizeHeight</column_name>
          <offset>268</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseTipPositionHorizontal</column_name>
          <offset>272</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseTipPositionVertical</column_name>
          <offset>276</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseTipHeight</column_name>
          <offset>280</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseTipDepth</column_name>
          <offset>284</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>nostrilPositionHorizontal</column_name>
          <offset>288</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>nostrilPositionVertical</column_name>
          <offset>292</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>nostrilDepth</column_name>
          <offset>296</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>nostrilSize</column_name>
          <offset>300</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>noseFill</column_name>
          <offset>304</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>septumWidth</column_name>
          <offset>308</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>septumHeight</column_name>
          <offset>312</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>facialSizeLowerLength</column_name>
          <offset>316</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>facialSizeWidth</column_name>
          <offset>320</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>cranialWidth</column_name>
          <offset>324</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>cranialHeight</column_name>
          <offset>328</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>cranialDepth</column_name>
          <offset>332</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>templeWidth</column_name>
          <offset>336</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>foreheadSizeHeight</column_name>
          <offset>340</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>foreheadSizeWidth</column_name>
          <offset>344</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>foreheadDepth</column_name>
          <offset>348</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>chinSizeWidth</column_name>
          <offset>352</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>chinSizeHeight</column_name>
          <offset>356</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>chinPositionHorizontal</column_name>
          <offset>360</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>chinPositionVertical</column_name>
          <offset>364</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>chinDepth</column_name>
          <offset>368</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>earScaleWidth</column_name>
          <offset>372</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>earScaleHeight</column_name>
          <offset>376</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>earRotationClockwiseAnti</column_name>
          <offset>380</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>earRotationInOut</column_name>
          <offset>384</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>earPositionHorizontal</column_name>
          <offset>388</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>earPositionVertical</column_name>
          <offset>392</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>earLobe</column_name>
          <offset>396</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>ethnicity</column_name>
          <offset>400</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bodyDefinitionUndefined</column_name>
          <offset>404</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bodyDefinitionDefined</column_name>
          <offset>408</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bodyArmsUpperSmall</column_name>
          <offset>412</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bodyArmsUpperLarge</column_name>
          <offset>416</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bodyArmsLowerSmall</column_name>
          <offset>420</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bodyArmsLowerLarge</column_name>
          <offset>424</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bodyLegsUpperSmall</column_name>
          <offset>428</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bodyLegsUpperLarge</column_name>
          <offset>432</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bodyLegsLowerSmall</column_name>
          <offset>436</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bodyLegsLowerLarge</column_name>
          <offset>440</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bodyLegShapeKnockKnee</column_name>
          <offset>444</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bodyLegShapeBowed</column_name>
          <offset>448</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stripTorsoUpperSmall</column_name>
          <offset>452</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stripTorsoUpperLarge</column_name>
          <offset>456</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stripTorsoLowerSmall</column_name>
          <offset>460</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stripTorsoLowerLarge</column_name>
          <offset>464</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stripStomachSmall</column_name>
          <offset>468</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stripStomachLarge</column_name>
          <offset>472</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stripSpineShapeArched</column_name>
          <offset>476</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stripSpineShapeStraight</column_name>
          <offset>480</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stripTrapeziusSmall</column_name>
          <offset>484</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stripTrapeziusLarge</column_name>
          <offset>488</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>collarNeckWidthThin</column_name>
          <offset>492</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>collarNeckWidthThick</column_name>
          <offset>496</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>collarNeckWidthNarrow</column_name>
          <offset>500</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>collarNeckWidthWide</column_name>
          <offset>504</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_id</column_name>
          <offset>508</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_stadium</name>
      <row_size>68</row_size>
      <columns>
        <ColumnDef>
          <column_name>city_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>max_wind_speed</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>exported</column_name>
          <offset>8</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>size</column_name>
          <offset>9</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>10</offset>
          <size>24</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>abbreviation</column_name>
          <offset>34</offset>
          <size>5</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>filename</column_name>
          <offset>39</offset>
          <size>28</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_icon</name>
      <row_size>48</row_size>
      <columns>
        <ColumnDef>
          <column_name>primary_colour</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>secondary_colour</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>exported</column_name>
          <offset>12</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>13</offset>
          <size>26</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>icon_file_string</column_name>
          <offset>39</offset>
          <size>6</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_team_logo</name>
      <row_size>52</row_size>
      <columns>
        <ColumnDef>
          <column_name>primary_logo_colour</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>screen_friendly_logo_colour</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>splotch_logo_colour</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>16</offset>
          <size>24</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>filename</column_name>
          <offset>40</offset>
          <size>10</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_team</name>
      <row_size>204</row_size>
      <columns>
        <ColumnDef>
          <column_name>cash</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>firing_fees</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>commentary_name_id</column_name>
          <offset>12</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>strip_id_1</column_name>
          <offset>14</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>strip_id_2</column_name>
          <offset>16</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>strip_id_3</column_name>
          <offset>18</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>logo_id</column_name>
          <offset>20</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>associated_country_id</column_name>
          <offset>22</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>captain_id</column_name>
          <offset>24</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>goal_kicker_id</column_name>
          <offset>26</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>play_kicker_id</column_name>
          <offset>28</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>ranking</column_name>
          <offset>30</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>attack</column_name>
          <offset>32</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>defence</column_name>
          <offset>34</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>ruck_ability</column_name>
          <offset>36</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>maul_ability</column_name>
          <offset>38</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>scrum_ability</column_name>
          <offset>40</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>lineout_ability</column_name>
          <offset>42</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>def_forward_pass_drive</column_name>
          <offset>44</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>def_forward_contact_offload</column_name>
          <offset>46</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>def_back_pass_kick</column_name>
          <offset>48</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>def_back_contact_offload</column_name>
          <offset>50</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>def_lineout_size</column_name>
          <offset>52</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>def_lineout_favoured_target</column_name>
          <offset>54</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>def_ruck_win</column_name>
          <offset>56</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>def_lineout_win</column_name>
          <offset>58</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>def_scrum_win</column_name>
          <offset>60</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>def_maul_win</column_name>
          <offset>62</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>def_line_width</column_name>
          <offset>64</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>def_line_depth</column_name>
          <offset>66</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>def_ruck_commitment</column_name>
          <offset>68</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mid_forward_pass_drive</column_name>
          <offset>70</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mid_forward_contact_offload</column_name>
          <offset>72</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mid_back_pass_kick</column_name>
          <offset>74</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mid_back_contact_offload</column_name>
          <offset>76</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mid_lineout_size</column_name>
          <offset>78</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mid_lineout_favoured_target</column_name>
          <offset>80</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mid_ruck_win</column_name>
          <offset>82</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mid_lineout_win</column_name>
          <offset>84</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mid_scrum_win</column_name>
          <offset>86</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mid_maul_win</column_name>
          <offset>88</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mid_line_width</column_name>
          <offset>90</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mid_line_depth</column_name>
          <offset>92</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mid_ruck_commitment</column_name>
          <offset>94</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>att_forward_pass_drive</column_name>
          <offset>96</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>att_forward_contact_offload</column_name>
          <offset>98</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>att_back_pass_kick</column_name>
          <offset>100</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>att_back_contact_offload</column_name>
          <offset>102</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>att_lineout_size</column_name>
          <offset>104</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>att_lineout_favoured_target</column_name>
          <offset>106</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>att_ruck_win</column_name>
          <offset>108</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>att_lineout_win</column_name>
          <offset>110</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>att_scrum_win</column_name>
          <offset>112</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>att_maul_win</column_name>
          <offset>114</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>att_line_width</column_name>
          <offset>116</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>att_line_depth</column_name>
          <offset>118</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>att_ruck_commitment</column_name>
          <offset>120</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>kick_kickoff_short_long</column_name>
          <offset>122</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>kick_kickoff_left_right</column_name>
          <offset>124</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>kick_dropout_short_long</column_name>
          <offset>126</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>kick_dropout_left_right</column_name>
          <offset>128</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>kick_touch_territory</column_name>
          <offset>130</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>kick_penalty_touch_goal</column_name>
          <offset>132</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>rep_area_id</column_name>
          <offset>134</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>primary_comp</column_name>
          <offset>136</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>requires_the</column_name>
          <offset>138</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>custom</column_name>
          <offset>139</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>boot_texture_num</column_name>
          <offset>140</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>is_locked</column_name>
          <offset>141</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>def_pod_option</column_name>
          <offset>142</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mid_pod_option</column_name>
          <offset>143</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>att_pod_option</column_name>
          <offset>144</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>days_till_rep_selection_allowed</column_name>
          <offset>145</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_exclusive</column_name>
          <offset>146</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>permission_flags_gender</column_name>
          <offset>147</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>148</offset>
          <size>26</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>short_name</column_name>
          <offset>174</offset>
          <size>25</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>abbrev</column_name>
          <offset>199</offset>
          <size>4</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_player</name>
      <row_size>1348</row_size>
      <columns>
        <ColumnDef>
          <column_name>dob</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>position_category1_id</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>position_category2_id</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>position_category3_id</column_name>
          <offset>16</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>position_category1_r7_id</column_name>
          <offset>20</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>position_category2_r7_id</column_name>
          <offset>24</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>position_category3_r7_id</column_name>
          <offset>28</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>age_intensity</column_name>
          <offset>32</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bleach_intensity</column_name>
          <offset>36</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>brow_line_intensity</column_name>
          <offset>40</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>crows_feet_intensity</column_name>
          <offset>44</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eye_darkening_intensity</column_name>
          <offset>48</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eyebrow_intensity</column_name>
          <offset>52</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>florid_intensity</column_name>
          <offset>56</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>freckle_intensity</column_name>
          <offset>60</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>frown_line_intensity</column_name>
          <offset>64</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>laugh_line_intensity</column_name>
          <offset>68</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>lower_mouth_intensity</column_name>
          <offset>72</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mole_intensity</column_name>
          <offset>76</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>nose_intensity</column_name>
          <offset>80</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>philtrum_intensity</column_name>
          <offset>84</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pimple_intensity</column_name>
          <offset>88</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>scar_intensity</column_name>
          <offset>92</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stubble_intensity</column_name>
          <offset>96</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bandage</column_name>
          <offset>100</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>nation</column_name>
          <offset>104</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>ethnicity</column_name>
          <offset>106</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>fitness</column_name>
          <offset>108</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>agility</column_name>
          <offset>110</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>speed</column_name>
          <offset>112</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>acceleration</column_name>
          <offset>114</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>aggressiveness</column_name>
          <offset>116</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tackle_ability</column_name>
          <offset>118</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>break_tackle_ability</column_name>
          <offset>120</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pass_accuracy</column_name>
          <offset>122</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>offload_ability</column_name>
          <offset>124</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>general_kick_accuracy</column_name>
          <offset>126</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>goal_kick_accuracy</column_name>
          <offset>128</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>catch_ability</column_name>
          <offset>130</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>strength</column_name>
          <offset>132</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mental_agility</column_name>
          <offset>134</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>jump_ability</column_name>
          <offset>136</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>discipline</column_name>
          <offset>138</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>influence</column_name>
          <offset>140</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>star_factor</column_name>
          <offset>142</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_proportions_id</column_name>
          <offset>144</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>caps</column_name>
          <offset>146</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>head_mesh</column_name>
          <offset>148</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>head_texture</column_name>
          <offset>150</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>hair_mesh</column_name>
          <offset>152</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>hair_texture</column_name>
          <offset>154</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>beard_mesh</column_name>
          <offset>156</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>head_gear_club_mesh</column_name>
          <offset>158</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>head_gear_intl_mesh</column_name>
          <offset>160</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tattoo_shoulder_l_texture</column_name>
          <offset>162</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tattoo_shoulder_r_texture</column_name>
          <offset>164</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tattoo_forearm_l_texture</column_name>
          <offset>166</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tattoo_forearm_r_texture</column_name>
          <offset>168</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tattoo_hand_l_texture</column_name>
          <offset>170</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tattoo_hand_r_texture</column_name>
          <offset>172</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tattoo_leg_l_texture</column_name>
          <offset>174</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tattoo_leg_r_texture</column_name>
          <offset>176</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>custom</column_name>
          <offset>178</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>artist_customised</column_name>
          <offset>179</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>m_generic</column_name>
          <offset>180</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>height</column_name>
          <offset>181</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>weight</column_name>
          <offset>182</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>preferred_foot</column_name>
          <offset>183</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>franchise_region</column_name>
          <offset>184</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>rookie</column_name>
          <offset>185</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>license_restrict</column_name>
          <offset>186</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_exclusive</column_name>
          <offset>187</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>skin_texture</column_name>
          <offset>188</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shirt_style</column_name>
          <offset>189</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sleeve</column_name>
          <offset>190</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sock_style</column_name>
          <offset>191</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eye_texture</column_name>
          <offset>192</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>age_texture</column_name>
          <offset>193</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bleach_texture</column_name>
          <offset>194</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>brow_line_texture</column_name>
          <offset>195</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>crows_feet_texture</column_name>
          <offset>196</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eye_darkening_texture</column_name>
          <offset>197</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eyebrow_texture</column_name>
          <offset>198</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>florid_texture</column_name>
          <offset>199</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>freckle_texture</column_name>
          <offset>200</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>frown_line_texture</column_name>
          <offset>201</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>laugh_line_texture</column_name>
          <offset>202</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>lower_mouth_texture</column_name>
          <offset>203</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mole_texture</column_name>
          <offset>204</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>nose_texture</column_name>
          <offset>205</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>philtrum_texture</column_name>
          <offset>206</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pimple_texture</column_name>
          <offset>207</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>scar_texture</column_name>
          <offset>208</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stubble_texture</column_name>
          <offset>209</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mouth_guard_texture</column_name>
          <offset>210</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoes_club_mesh</column_name>
          <offset>211</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoes_club_texture</column_name>
          <offset>212</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoes_intl_mesh</column_name>
          <offset>213</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoes_intl_texture</column_name>
          <offset>214</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>head_gear_club_style</column_name>
          <offset>215</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>head_gear_club_texture</column_name>
          <offset>216</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>head_gear_intl_style</column_name>
          <offset>217</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>head_gear_intl_texture</column_name>
          <offset>218</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>gender</column_name>
          <offset>219</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>first_name</column_name>
          <offset>220</offset>
          <size>25</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>last_name</column_name>
          <offset>245</offset>
          <size>25</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>commentary_name</column_name>
          <offset>270</offset>
          <size>25</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>hair_tint_0</column_name>
          <offset>295</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>hair_tint_1</column_name>
          <offset>330</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>hair_tint_2</column_name>
          <offset>365</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>beard_tint_0</column_name>
          <offset>400</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>beard_tint_1</column_name>
          <offset>435</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>beard_tint_2</column_name>
          <offset>470</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>eyebrow_tint</column_name>
          <offset>505</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stubble_tint</column_name>
          <offset>540</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mouth_guard_tint_0</column_name>
          <offset>575</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mouth_guard_tint_1</column_name>
          <offset>610</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mouth_guard_tint_2</column_name>
          <offset>645</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoe_l_club_tint_0</column_name>
          <offset>680</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoe_l_club_tint_1</column_name>
          <offset>715</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoe_l_club_tint_2</column_name>
          <offset>750</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoe_r_club_tint_0</column_name>
          <offset>785</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoe_r_club_tint_1</column_name>
          <offset>820</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoe_r_club_tint_2</column_name>
          <offset>855</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoe_l_intl_tint_0</column_name>
          <offset>890</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoe_l_intl_tint_1</column_name>
          <offset>925</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoe_l_intl_tint_2</column_name>
          <offset>960</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoe_r_intl_tint_0</column_name>
          <offset>995</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoe_r_intl_tint_1</column_name>
          <offset>1030</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shoe_r_intl_tint_2</column_name>
          <offset>1065</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>head_gear_club_tint_0</column_name>
          <offset>1100</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>head_gear_club_tint_1</column_name>
          <offset>1135</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>head_gear_club_tint_2</column_name>
          <offset>1170</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>head_gear_intl_tint_0</column_name>
          <offset>1205</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>head_gear_intl_tint_1</column_name>
          <offset>1240</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>head_gear_intl_tint_2</column_name>
          <offset>1275</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>sleeves_tint</column_name>
          <offset>1310</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_comp_def</name>
      <row_size>100</row_size>
      <columns>
        <ColumnDef>
          <column_name>start_date</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>salary_cap</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>law_variation</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>icon_id</column_name>
          <offset>16</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>trophy_id</column_name>
          <offset>18</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_preliminary_rounds</column_name>
          <offset>20</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_finals_rounds</column_name>
          <offset>22</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_advancing</column_name>
          <offset>24</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>original_id</column_name>
          <offset>26</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>cup_id</column_name>
          <offset>28</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>plate_id</column_name>
          <offset>30</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bowl_id</column_name>
          <offset>32</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>shield_id</column_name>
          <offset>34</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>excluded</column_name>
          <offset>36</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>custom</column_name>
          <offset>37</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>preliminary_format</column_name>
          <offset>38</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>preliminary_attribute_1</column_name>
          <offset>39</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>preliminary_attribute_2</column_name>
          <offset>40</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>preliminary_attribute_3</column_name>
          <offset>41</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>num_matches_against_each_opponent</column_name>
          <offset>42</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>finals_format</column_name>
          <offset>43</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>preliminary_golden_point_type</column_name>
          <offset>44</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>finals_golden_point_type</column_name>
          <offset>45</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>win_points</column_name>
          <offset>46</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>loss_points</column_name>
          <offset>47</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>draw_points</column_name>
          <offset>48</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bye_points</column_name>
          <offset>49</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bonus_points</column_name>
          <offset>50</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bonus_attacking_threshold</column_name>
          <offset>51</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bonus_defending_threshold</column_name>
          <offset>52</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>bonus_attacking_is_relative</column_name>
          <offset>53</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>franchise_region</column_name>
          <offset>54</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>competition_type</column_name>
          <offset>55</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>friendly</column_name>
          <offset>56</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pool_name</column_name>
          <offset>57</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>is_tour</column_name>
          <offset>58</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>r7_exclusive</column_name>
          <offset>59</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>permission_flags_gender</column_name>
          <offset>60</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>61</offset>
          <size>32</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>mnemonic</column_name>
          <offset>93</offset>
          <size>4</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_team_server_details</name>
      <row_size>236</row_size>
      <columns>
        <ColumnDef>
          <column_name>team_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>created_by</column_name>
          <offset>6</offset>
          <size>30</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>uploaded_by</column_name>
          <offset>36</offset>
          <size>30</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>upload_id</column_name>
          <offset>66</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>download_id_server</column_name>
          <offset>101</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>download_id_user</column_name>
          <offset>136</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>platform_id_creator</column_name>
          <offset>171</offset>
          <size>32</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>platform_id_uploader</column_name>
          <offset>203</offset>
          <size>32</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_player_server_details</name>
      <row_size>236</row_size>
      <columns>
        <ColumnDef>
          <column_name>player_id</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>created_by</column_name>
          <offset>6</offset>
          <size>30</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>uploaded_by</column_name>
          <offset>36</offset>
          <size>30</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>upload_id</column_name>
          <offset>66</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>download_id_server</column_name>
          <offset>101</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>download_id_user</column_name>
          <offset>136</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>platform_id_creator</column_name>
          <offset>171</offset>
          <size>32</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>platform_id_uploader</column_name>
          <offset>203</offset>
          <size>32</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_team_strip</name>
      <row_size>268</row_size>
      <columns>
        <ColumnDef>
          <column_name>texture_num</column_name>
          <offset>4</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>number_texture_num</column_name>
          <offset>6</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>is_home_strip</column_name>
          <offset>8</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>collar_style</column_name>
          <offset>9</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>primary_r</column_name>
          <offset>10</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>primary_g</column_name>
          <offset>11</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>primary_b</column_name>
          <offset>12</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>secondary_r</column_name>
          <offset>13</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>secondary_g</column_name>
          <offset>14</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>secondary_b</column_name>
          <offset>15</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tertiary_r</column_name>
          <offset>16</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tertiary_g</column_name>
          <offset>17</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>tertiary_b</column_name>
          <offset>18</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>primary_percent</column_name>
          <offset>19</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>boot_texture_num</column_name>
          <offset>20</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>boot_primary_r</column_name>
          <offset>21</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>boot_primary_g</column_name>
          <offset>22</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>boot_primary_b</column_name>
          <offset>23</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>boot_secondary_r</column_name>
          <offset>24</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>boot_secondary_g</column_name>
          <offset>25</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>boot_secondary_b</column_name>
          <offset>26</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>boot_tertiary_r</column_name>
          <offset>27</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>boot_tertiary_g</column_name>
          <offset>28</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>boot_tertiary_b</column_name>
          <offset>29</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>custom</column_name>
          <offset>30</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>name</column_name>
          <offset>31</offset>
          <size>27</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>created_by</column_name>
          <offset>58</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>uploaded_by</column_name>
          <offset>93</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>created_by_id</column_name>
          <offset>128</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>uploaded_by_id</column_name>
          <offset>163</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>platform_id_creator</column_name>
          <offset>198</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>platform_id_uploader</column_name>
          <offset>233</offset>
          <size>35</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_career_mode_state</name>
      <row_size>156</row_size>
      <columns>
        <ColumnDef>
          <column_name>game_difficulty</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>game_length</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>substitution_mode</column_name>
          <offset>12</offset>
          <size>4</size>
          <type>1</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>career_mode_date</column_name>
          <offset>16</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_progress_club_cp</column_name>
          <offset>20</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_progress_club_pk</column_name>
          <offset>24</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_progress_club_gk</column_name>
          <offset>28</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_progress_int_cp</column_name>
          <offset>32</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_progress_int_pk</column_name>
          <offset>36</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_progress_int_gk</column_name>
          <offset>40</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>career_start_date</column_name>
          <offset>44</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_international_allegiance_id</column_name>
          <offset>48</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>international_team_id</column_name>
          <offset>50</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>club_team_id</column_name>
          <offset>52</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>club_team1_id</column_name>
          <offset>54</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>club_team2_id</column_name>
          <offset>56</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>club_team3_id</column_name>
          <offset>58</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>club_team4_id</column_name>
          <offset>60</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>club_team5_id</column_name>
          <offset>62</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>club_team6_id</column_name>
          <offset>64</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>club_team7_id</column_name>
          <offset>66</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_int_comp0_id</column_name>
          <offset>68</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_int_comp1_id</column_name>
          <offset>70</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_int_comp2_id</column_name>
          <offset>72</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_int_comp3_id</column_name>
          <offset>74</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_int_comp4_id</column_name>
          <offset>76</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>total_pro_club_team_count</column_name>
          <offset>78</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>ranfurly_team_id</column_name>
          <offset>80</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>current_season_match_index</column_name>
          <offset>82</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>franchise_definition_index</column_name>
          <offset>84</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>primary_team_db_index</column_name>
          <offset>86</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>finals_fails</column_name>
          <offset>88</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_rep_losing_streak</column_name>
          <offset>90</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_losing_streak</column_name>
          <offset>92</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>primary_comp_db_index</column_name>
          <offset>94</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>primary_player_db_id</column_name>
          <offset>96</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>club_contract_tier</column_name>
          <offset>98</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>analytic_id</column_name>
          <offset>100</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>has_new_email</column_name>
          <offset>102</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>auto_player_training</column_name>
          <offset>103</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>auto_player_trading</column_name>
          <offset>104</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>auto_squad_selection</column_name>
          <offset>105</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>is_pro_not_signed</column_name>
          <offset>106</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>is_fired</column_name>
          <offset>107</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>can_be_fired</column_name>
          <offset>108</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>unlimited_salary_enabled</column_name>
          <offset>109</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>rep_performance_warning</column_name>
          <offset>110</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>performance_warning</column_name>
          <offset>111</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>manager_difficulty</column_name>
          <offset>112</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>is_franchise</column_name>
          <offset>113</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>post_competition_mode</column_name>
          <offset>114</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>done_initial_drafting</column_name>
          <offset>115</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>career_game_mode_sevens</column_name>
          <offset>116</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_promoted_club_cp</column_name>
          <offset>117</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_promoted_club_pk</column_name>
          <offset>118</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_promoted_club_gk</column_name>
          <offset>119</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_promoted_int_cp</column_name>
          <offset>120</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_promoted_int_pk</column_name>
          <offset>121</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>pro_promoted_int_gk</column_name>
          <offset>122</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>autosave</column_name>
          <offset>123</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>manager_name</column_name>
          <offset>124</offset>
          <size>32</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rurec_records</name>
      <row_size>84</row_size>
      <columns>
        <ColumnDef>
          <column_name>date</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>alt_date</column_name>
          <offset>8</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>comp_def_id</column_name>
          <offset>12</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>record_type</column_name>
          <offset>14</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>value</column_name>
          <offset>16</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>alt_value</column_name>
          <offset>18</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>competing_collective</column_name>
          <offset>20</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>competing_timeframe</column_name>
          <offset>21</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>ordinal</column_name>
          <offset>22</offset>
          <size>1</size>
          <type>4</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>player_name</column_name>
          <offset>23</offset>
          <size>17</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team</column_name>
          <offset>40</offset>
          <size>13</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>alt_team</column_name>
          <offset>53</offset>
          <size>16</size>
          <type>5</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>location</column_name>
          <offset>69</offset>
          <size>13</size>
          <type>5</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_home_stadium</name>
      <row_size>12</row_size>
      <columns>
        <ColumnDef>
          <column_name>weight</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>0</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>team_id</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>stadium_id</column_name>
          <offset>10</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
    <TableDef>
      <name>rudb_franchise_comp</name>
      <row_size>12</row_size>
      <columns>
        <ColumnDef>
          <column_name>start_date</column_name>
          <offset>4</offset>
          <size>4</size>
          <type>6</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>franchise_id</column_name>
          <offset>8</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
        <ColumnDef>
          <column_name>comp_id</column_name>
          <offset>10</offset>
          <size>2</size>
          <type>2</type>
        </ColumnDef>
      </columns>
    </TableDef>
  </tables>
</DatabaseDef>