// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIScreenDebugWindow.h"
#include "RugbyGameInstance.h"
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "UI/GeneratedHeaders/WWUIScreenDebugWindow_UI_Namespace.h"
#include "Match/SSGameTimer.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/SIFGameWorld.h"
#include "RugbyGameInstance.h"
#include "TimerManager.h"
#include "UI/Populators/WWUIPopulatorDebugOptions.h"

//Components
#include "EditableText.h"
#include "ComboBoxString.h"
#include "TextBlock.h"
#include "WWUIScrollBox.h"
#include "UI/DebugCutsceneActor.h"
#include "ScrollBox.h"
#include "CheckBox.h"
#include "Animation/AnimSequence.h"

//Specific widget names
#define CUTSCENEIDWIDGET FString("CUTSCENE ID")
#define SUBCUTSCENEWIDGET FString("SUBCUTSCENE")
#define CUTSCENENAMEWIDGET FString("CUTSCENE NAME")
#define CAMERAINDEXWIDGET FString("CAMERA INDEX")
#define NUMBEROFCAMERASWIDGET FString("NUMBER OF CAMERAS")
#define OPTIONVALUEWIDGET "OPTIONVALUE"
#define ANIMATIONID FString("ANIMATION ID")
#define PLAYINGTACKLES FString("PLAY TACKLES")

bool UWWUIScreenDebugWindow::screenOpen = false;
TMap<FString, void *> UWWUIScreenDebugWindow::DebugOptionBindings = TMap<FString, void *>();
EDebugTabs UWWUIScreenDebugWindow::currentTabIndex = EDebugTabs::CUTSCENES;

void UWWUIScreenDebugWindow::Startup(UWWUIStateScreenData* InData)
{
	if (GetWorld())
	{
		cutsceneActor = Cast<ADebugCutsceneActor>(GetWorld()->SpawnActor(ADebugCutsceneActor::StaticClass()));
	}

	PopulateDebugOptions();
}

//===============================================================================
//===============================================================================
void UWWUIScreenDebugWindow::Shutdown()
{
	if (UOBJ_IS_VALID(cutsceneActor))
	{
		cutsceneActor->StopDebugAnimations();
	}
	cutsceneActor = nullptr;
}

void UWWUIScreenDebugWindow::PopulateDebugOptions()
{
	//Populate the debug options from DebugOptions.h
	if (UWWUIScrollBox * optionsScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenDebugWindow_UI::DebugOptionsScrollbox)))
	{
		if (UWWUIPopulatorDebugOptions * debugPopulator = Cast<UWWUIPopulatorDebugOptions>(optionsScrollbox->GetPopulator()))
		{
			TArray<FWWUIScreenTemplateDataOption> newDataOptions;

			TArray<DebugOption> DebugOptionsList;
			switch (currentTabIndex)
			{
			case EDebugTabs::CUTSCENES:
				DebugOptionsList = CutsceneDebugOptionsList;
				break;
			case EDebugTabs::ANIMATIONS:
				DebugOptionsList = AnimationDebugOptionsList;
				break;
			case EDebugTabs::RULES:
				DebugOptionsList = RulesDebugOptionsList;
				break;
			case EDebugTabs::RECORDING:
				DebugOptionsList = RecordingDebugOptionsList;
				break;
			case EDebugTabs::CAREER:
				DebugOptionsList = CareerDebugOptionsList;
				break;			
			case EDebugTabs::PRO:
				DebugOptionsList = ProDebugOptionsList;
				break;
			case EDebugTabs::KICK:
				DebugOptionsList = KickDebugOptionsList;
				break;
			case EDebugTabs::EMOTION:
				DebugOptionsList = EmotionDebugOptionsList;
				break;
			}

			for (int i = 0; i < DebugOptionsList.Num(); i++)
			{
				FWWUIScreenTemplateDataOption newOption;
				newOption.Title = DebugOptionsList[i].DebugName;
				switch (DebugOptionsList[i].widgetType)
				{
				case WidgetType::EDITABLETEXT:
					newOption.TemplateName = EDITABLETEXTWIDGET;
					break;
				case WidgetType::TEXT:
					newOption.TemplateName = TEXTWIDGET;
					break;
				case WidgetType::DROPDOWN:
					newOption.TemplateName = COMBOBOXWIDGET;
					break;
				case WidgetType::TICKBOX:
					newOption.TemplateName = TICKBOXWIDGET;
					break;
				}

				newDataOptions.Add(newOption);
			}

			debugPopulator->SetDebugOptions(newDataOptions, DebugOptionBindings);
			debugPopulator->Populate(Cast<UWidget>(optionsScrollbox->GetScrollBox()));

			//Update the cutscene info
			UpdateDebugDataUI();

			//Show mouse
			if (GetWorld())
			{
				if (GetWorld()->GetGameInstance<URugbyGameInstance>())
				{
					APlayerController * primaryPlayerController = GetWorld()->GetGameInstance<URugbyGameInstance>()->GetMasterPlayerController();

					if (primaryPlayerController)
					{
						primaryPlayerController->bShowMouseCursor = true;
						primaryPlayerController->bEnableClickEvents = true;
						primaryPlayerController->bEnableMouseOverEvents = true;
					}

					//Set keyboard focus to first widget
					if (optionsScrollbox->GetListLength() > 0)
					{
						optionsScrollbox->FocusFirstListField(SIFApplication::GetApplication()->GetMasterPlayerController());
					}
				}
			}
		}

		//Populate the playback speed
		//Playback speed needs a bit of translation before it is passed over.
		if (UWidget * playbackSpeedParent = FindChildWidget(FString("PLAYBACK SPEED")))
		{
			if (UComboBoxString * playbackSpeedWidget = Cast< UComboBoxString>(FindChildOfTemplateWidget(playbackSpeedParent, OPTIONVALUEWIDGET)))
			{
				for (int i = 0; i < PlaybackSpeeds.Num(); i++)
				{
					playbackSpeedWidget->AddOption(FString::SanitizeFloat(PlaybackSpeeds[i]));
				}
				playbackSpeedWidget->SetSelectedOption("1");
			}
		}
	}
}

void UWWUIScreenDebugWindow::AddToOptionsBindings(FString key, void * val)
{
	DebugOptionBindings.Add(key, val);
}

bool UWWUIScreenDebugWindow::IsScreenOpen()
{
	return screenOpen;
}

bool UWWUIScreenDebugWindow::IsAnimationDebugScreenOpen()
{
	return screenOpen && currentTabIndex == EDebugTabs::ANIMATIONS;
}

void UWWUIScreenDebugWindow::Update(float DeltaTime)
{
}

void UWWUIScreenDebugWindow::RegisterFunctions()
{
#ifdef ENABLE_DEBUG_KEYS
	AddInputAction(FString("RU_DEBUG_MENU_HIDE"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenDebugWindow::HideWindow));
	AddInputAction(FString("RU_DEBUG_MENU_TOGGLE"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenDebugWindow::CloseScreen), false, true);
	AddInputAction(FString("RU_DEBUG_MENU_NEXT_PAGE"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenDebugWindow::TabRight));
	AddInputAction(FString("RU_DEBUG_MENU_PREV_PAGE"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenDebugWindow::TabLeft));
#endif
}

void UWWUIScreenDebugWindow::HideWindow(APlayerController * playerController)
{
	if(isHidden)
	{
		UWWUIFunctionLibrary::PlayAnimation(this, FName("Minimise"), 0, 1, EUMGSequencePlayMode::Reverse);
	}
	else
	{
		UWWUIFunctionLibrary::PlayAnimation(this, FName("Minimise"));
	}
	isHidden = !isHidden;
}

void UWWUIScreenDebugWindow::CloseScreen(APlayerController * playerController)
{
	if (GetWorld())
	{		
		if (GetWorld()->GetGameInstance<URugbyGameInstance>())
		{
			APlayerController * primaryPlayerController = GetWorld()->GetGameInstance<URugbyGameInstance>()->GetMasterPlayerController();

			if (primaryPlayerController)
			{
				primaryPlayerController->bShowMouseCursor = false;
				primaryPlayerController->bEnableClickEvents = false;
				primaryPlayerController->bEnableMouseOverEvents = false;
			}
			//Rename all the existing widgets to stop a rename error in populator
			if (UWWUIScrollBox * optionsScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenDebugWindow_UI::DebugOptionsScrollbox)))
			{
				int renameInt = 0;
				if (UScrollBox * scrollbox = optionsScrollbox->GetScrollBox())
				{
					for (int i = 0; i < scrollbox->GetChildrenCount(); i++)
					{
						if (UWidget * childwidget = scrollbox->GetChildAt(i))
						{					
							while (!childwidget->Rename(*TEXT("garbage") + *FString::FromInt(renameInt), nullptr, REN_Test))
							{
								renameInt++;
							}
							childwidget->Rename(*TEXT("garbage") + *FString::FromInt(renameInt));
						}
					}
				}
			}

			GetGameInstance<URugbyGameInstance>()->DealMenuAction(SCREEN_CANCEL, Screens_UI::DebugWindow);
		}
	}
}

void UWWUIScreenDebugWindow::TabRight(APlayerController * playerController)
{
	int newIdx = (int)currentTabIndex + 1;
	if (newIdx >= (int)EDebugTabs::MAX)
		newIdx = 0;

	currentTabIndex = static_cast<EDebugTabs>(newIdx);
	PopulateDebugOptions();
}

void UWWUIScreenDebugWindow::TabLeft(APlayerController * playerController)
{
	int newIdx = (int)currentTabIndex - 1;
	if (newIdx < 0)
		newIdx = (int)EDebugTabs::MAX - 1;

	currentTabIndex = static_cast<EDebugTabs>(newIdx);
	PopulateDebugOptions();
}

void UWWUIScreenDebugWindow::OnInFocus()
{
	screenOpen = true;

	//Pause the game timer
	if (GetWorld()->GetGameInstance<URugbyGameInstance>()->GetActiveGameWorld())
	{
		SSGameTimer * gameTimer = GetWorld()->GetGameInstance<URugbyGameInstance>()->GetActiveGameWorld()->GetGameTimer();
		gameTimer->SetGameRunning(false);
	}

	//Stop the ai
	SSEVDSFormationManager::SetAIRunning(false);
}

void UWWUIScreenDebugWindow::OnOutFocus(bool ShouldOutFocus)
{
	screenOpen = false;

	//Unpause the game timer
	if (GetWorld()->GetGameInstance<URugbyGameInstance>()->GetActiveGameWorld())
	{
		SSGameTimer * gameTimer = GetWorld()->GetGameInstance<URugbyGameInstance>()->GetActiveGameWorld()->GetGameTimer();
		gameTimer->SetGameRunning(true);
	}

	//Enable the AI
	SSEVDSFormationManager::SetAIRunning(true);
}

void UWWUIScreenDebugWindow::NavigateBack(APlayerController* playerController)
{
	//Move back a screen
	if (UWorld* world = GetWorld())
	{
		if (URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(world->GetGameInstance()))
		{
			gameInstance->DealMenuAction(SCREEN_CANCEL, Screens_UI::DebugWindow);
		}
	}
}

//Function called by the widgets when their value is changed. Takes in the widget name and the new value as strings.
void UWWUIScreenDebugWindow::UpdateOption(FString OptionTitle, FString OptionResult)
{
	//All options with integer results
	if (IntegerOptions.Contains(OptionTitle))
	{
		//Try converting the string into an int
		int newVal = FCString::Atoi(*OptionResult);

		if (void ** memoryAddress =  DebugOptionBindings.Find(OptionTitle))
		{
			if (int * intAddress = (int*)(*memoryAddress))
			{
				*intAddress = newVal;
			}
		}
	}

	//All options with string results
	if (StringOptions.Contains(OptionTitle))
	{
		if (void ** memoryAddress = DebugOptionBindings.Find(OptionTitle))
		{
			if (FString * stringAddress = (FString*)(*memoryAddress))
			{
				*stringAddress = OptionResult;
			}
		}
	}

	//All options with float results
	if (FloatOptions.Contains(OptionTitle))
	{
		//Try converting the string into an int
		float newVal = FCString::Atof(*OptionResult);

		if (void ** memoryAddress = DebugOptionBindings.Find(OptionTitle))
		{
			if (float * floatAddress = (float*)(*memoryAddress))
			{
				*floatAddress = newVal;
			}
		}
	}

	//All options with integer results
	if (BoolOptions.Contains(OptionTitle))
	{
		//Try converting the string into an int
		int newVal = FCString::Atoi(*OptionResult);

		if (void ** memoryAddress = DebugOptionBindings.Find(OptionTitle))
		{
			if (bool * intAddress = (bool*)(*memoryAddress))
			{
				*intAddress = newVal;
			}
		}
	}

	//Option specific calls
	//This is where you can add any code that needs to be run when specific widget values change

	//Playback speed needs a bit of translation before it is passed over.
	if (OptionTitle.Contains("PLAYBACK SPEED"))
	{
		//Get array position selected
		int arrayPos = FCString::Atoi(*OptionResult);

		//Convert the array address into the corresponding float
		float newPlaybackSpeed = 1;
		if (PlaybackSpeeds.IsValidIndex(arrayPos))
		{
			newPlaybackSpeed = PlaybackSpeeds[arrayPos];
		}
	
		if (void ** memoryAddress = DebugOptionBindings.Find(OptionTitle))
		{
			if (float * floatAddress = (float*)(*memoryAddress))
			{
				*floatAddress = newPlaybackSpeed;
			}
		}
	}

	if (currentTabIndex == EDebugTabs::CUTSCENES)
	{
		//notify the cutscene manager of a change
		if (UOBJ_IS_VALID(cutsceneActor))
		{
			cutsceneActor->UpdateCutscene(true);
			cutsceneActor->UpdateAnimation(false);
		}
	}
	else if (currentTabIndex == EDebugTabs::ANIMATIONS)
	{
		//notify the cutscene manager of a change
		if (UOBJ_IS_VALID(cutsceneActor))
		{
			cutsceneActor->UpdateCutscene(false);
			cutsceneActor->UpdateAnimation(true);
		}
	}

	//Refresh the cutscene info on a timer to allow for the first loop to run and fill all data fields with updated info
	UWWUIFunctionLibrary::OnTimer(0.01f, FTimerDelegate::CreateUObject(this, &UWWUIScreenDebugWindow::UpdateDebugDataUI));
}

//Function to update all the option info when a value changes
void UWWUIScreenDebugWindow::UpdateDebugDataUI()
{
	//loop through integer widgets
	for (int i = 0; i < IntegerOptions.Num(); i++)
	{
		//Get the value of the option
		int IntegerOptionValue = 0;
		bool BoolOptionValue = false;
		if (void ** memoryAddress = DebugOptionBindings.Find(IntegerOptions[i]))
		{
			if (int * intAddress = (int*)(*memoryAddress))
			{
				IntegerOptionValue = *intAddress;
			}
		}

		if (UWidget * integerWidgetParent = FindChildWidget(IntegerOptions[i]))
		{
			//If the widget is a text block or editable text, update the text
			if (UTextBlock * integerTextBlock = Cast<UTextBlock>(FindChildOfTemplateWidget(integerWidgetParent, OPTIONVALUEWIDGET)))
			{
				integerTextBlock->SetText(FText::FromString(FString::FromInt(IntegerOptionValue)));
			}
			else if (UEditableText * integerEditableText = Cast<UEditableText>(FindChildOfTemplateWidget(integerWidgetParent, OPTIONVALUEWIDGET)))
			{
				integerEditableText->SetText(FText::FromString(FString::FromInt(IntegerOptionValue)));
			}
			else if (UCheckBox * integerCheckBox = Cast<UCheckBox>(FindChildOfTemplateWidget(integerWidgetParent, OPTIONVALUEWIDGET)))
			{
				integerCheckBox->SetIsChecked(BoolOptionValue);
			}
		}
	}

	//loop through float widgets
	for (int i = 0; i < FloatOptions.Num(); i++)
	{
		//Get the value of the option
		int FloatOptionValue = 0;
		if (void ** memoryAddress = DebugOptionBindings.Find(FloatOptions[i]))
		{
			if (float * floatAddress = (float*)(*memoryAddress))
			{
				FloatOptionValue = *floatAddress;
			}
		}
		if (UWidget * FloatWidgetParent = FindChildWidget(FloatOptions[i]))
		{
			//If the widget is a text block or editable text, update the text
			if (UTextBlock * FloatTextBlock = Cast<UTextBlock>(FindChildOfTemplateWidget(FloatWidgetParent, OPTIONVALUEWIDGET)))
			{
				FloatTextBlock->SetText(FText::FromString(FString::SanitizeFloat(FloatOptionValue)));
			}
			else if (UEditableText * FloatEditableText = Cast<UEditableText>(FindChildOfTemplateWidget(FloatWidgetParent, OPTIONVALUEWIDGET)))
			{
				FloatEditableText->SetText(FText::FromString(FString::SanitizeFloat(FloatOptionValue, 3)));
			}
		}
	}

	if (currentTabIndex == EDebugTabs::CUTSCENES)
	{
		//Update ths subcutscene list
		UpdateSubcutsceneList();

		//Update camera info
		UpdateCutsceneCameraUI();
	}
	else if (currentTabIndex == EDebugTabs::ANIMATIONS)
	{
		UpdateAnimationList();
	}
}

void UWWUIScreenDebugWindow::UpdateAnimationList()
{
	UAnimSequence* AnimPtr = nullptr;

	int AnimIndex = -1;
	if (GetDebugOptionValue(ANIMATIONID, AnimIndex))
	{
		if (const FRugbyAnimationLibrary* pAnimLib = cutsceneActor->GetAnimLibrary())
		{

			bool bPlayingTackles = false;
			if (GetDebugOptionValue(PLAYINGTACKLES, bPlayingTackles))
			{
				if (bPlayingTackles)
				{
					if (const FRugbyTackleBlendAnimRecord* pTackleRec = pAnimLib->GetTackleAnimRec(AnimIndex))
					{
						AnimPtr = pTackleRec->m_pAnimSequence;
					}
				}
				else
				{
					AnimPtr = pAnimLib->GetAnimSequenceByIndex(AnimIndex);
				}
			}
		}

		if (AnimPtr)
		{
			FString animNameString = AnimPtr->GetName();
			UWidget * AnimNameParent = FindChildWidget(FString("ANIMATION NAME"));

			UTextBlock * AniNameText = Cast<UTextBlock>(FindChildOfTemplateWidget(AnimNameParent, OPTIONVALUEWIDGET));

			if (AniNameText)
			{
				//Add the new sub cutscenes to the list
				AniNameText->SetText(FText::FromString(animNameString));
			}
		}
	}
}

//Function used to find all the sub cutscenes available for the current cutscene and display them in the combo box
void UWWUIScreenDebugWindow::UpdateSubcutsceneList()
{
	//Get the value of the cutscene index
	int cutsceneIndex = 0;
	if (void ** memoryAddress = DebugOptionBindings.Find(CUTSCENEIDWIDGET))
	{
		if (int * intAddress = (int*)(*memoryAddress))
		{
			cutsceneIndex = *intAddress;
		}
	}

	//Get the value of the current selected sub cutscene
	int subCutsceneIndex = 0;
	if (void ** memoryAddress = DebugOptionBindings.Find(SUBCUTSCENEWIDGET))
	{
		if (int * intAddress = (int*)(*memoryAddress))
		{
			subCutsceneIndex = *intAddress;
		}
	}

	//Update sub cutscene list
	if (UWidget * subcutsceneParent = FindChildWidget(SUBCUTSCENEWIDGET))
	{
		UComboBoxString * CutsceneSubNameList = Cast<UComboBoxString>(FindChildOfTemplateWidget(subcutsceneParent, OPTIONVALUEWIDGET));

		UWidget * cutsceneNameParent = FindChildWidget(CUTSCENENAMEWIDGET);
		UTextBlock * CutsceneNameText = Cast<UTextBlock>(FindChildOfTemplateWidget(cutsceneNameParent, OPTIONVALUEWIDGET));

		//Clear previous subcutscene list
		if (CutsceneSubNameList)
		{
			CutsceneSubNameList->ClearOptions();

			//Get all cutscenes matching the index
			if (cutsceneActor)
			{
				GAME_MODE gameMode = GAME_MODE::GAME_MODE_RU13; // Nick  WWS 7s to Womens // GetWorld()->GetGameInstance<URugbyGameInstance>()->GetMatchGameSettings()->game_settings.GetGameMode();
				TArray<FCutsceneRec*> cutsceneList = cutsceneActor->GetCutsceneRec(cutsceneIndex, 0); // Nick  WWS 7s to Womens // gameMode == GAME_MODE::GAME_MODE_SEVENS);
				int cutsceneIndexAddon = 0;

				auto FindCutSceneFileName = [&](auto &FileName) //this strips off the path and extracts the fileName
				{
					int where = 0;

					if (FileName.FindLastChar('/', where)) //find the file name....
					{
						FileName.RemoveAt(0, where + 1);
					}
					else
					{
						UE_DEBUG_BREAK();//check what is this filename...
					}
				};
				
				FString CutSceneFileName;

				for (FCutsceneRec *cutscene : cutsceneList)
				{
					if (cutscene)
					{
						//CutsceneSubNameList->AddOption(cutscene->m_name + FString::FromInt(cutsceneIndexAddon));
						CutSceneFileName = cutscene->m_sequenceSoftPath.GetAssetPathString();
						FindCutSceneFileName(CutSceneFileName);
						CutsceneSubNameList->AddOption(CutSceneFileName);
						cutsceneIndexAddon++;
					}
				}
				if (cutsceneList.IsValidIndex(subCutsceneIndex))
				{
					CutSceneFileName = cutsceneList[subCutsceneIndex]->m_sequenceSoftPath.GetAssetPathString();
					FindCutSceneFileName(CutSceneFileName);

					//CutsceneSubNameList->SetSelectedOption(cutsceneList[subCutsceneIndex]->m_name + FString::FromInt(subCutsceneIndex));
					CutsceneSubNameList->SetSelectedOption(CutSceneFileName);

					if (CutsceneNameText)
					{
						//Add the new sub cutscenes to the list
						CutsceneNameText->SetText(FText::FromString(cutsceneList[subCutsceneIndex]->m_name));
					}
				}
				else if (cutsceneList.IsValidIndex(0))
				{
					//CutsceneSubNameList->SetSelectedOption(cutsceneList[0]->m_name + FString::FromInt(0));
					CutSceneFileName = cutsceneList[0]->m_sequenceSoftPath.GetAssetPathString();
					FindCutSceneFileName(CutSceneFileName);

					if (CutsceneNameText)
					{
						//Add the new sub cutscenes to the list
						CutsceneNameText->SetText(FText::FromString(cutsceneList[0]->m_name));
					}
				}
			}
		}
	}
}

//Function to update the camera info
void UWWUIScreenDebugWindow::UpdateCutsceneCameraUI()
{
	//Update the number of cameras
	//Get the widget
	int numberOfCams = 0;
	if (UWidget * numberOfCamerasParent = FindChildWidget(NUMBEROFCAMERASWIDGET))
	{
		UTextBlock * numberOfCamerasText = Cast<UTextBlock>(FindChildOfTemplateWidget(numberOfCamerasParent, OPTIONVALUEWIDGET));

		//Get the value of number of cameras
		if (void ** memoryAddress = DebugOptionBindings.Find(NUMBEROFCAMERASWIDGET))
		{
			if (int * intAddress = (int*)(*memoryAddress))
			{
				numberOfCamerasText->SetText(FText::FromString(FString::FromInt(*intAddress)));
				numberOfCams = *intAddress;
			}
		}
	}

	//Update current camera (default to 0 if out of bounds)
	//Get the widget
	if (UWidget * cameraIdParent = FindChildWidget(CAMERAINDEXWIDGET))
	{
		UEditableText * cameraIdEditableText = Cast<UEditableText>(FindChildOfTemplateWidget(cameraIdParent, OPTIONVALUEWIDGET));

		//Get the value of number of cameras
		if (void ** memoryAddress = DebugOptionBindings.Find(CAMERAINDEXWIDGET))
		{
			if (int * intAddress = (int*)(*memoryAddress))
			{
				if (*intAddress < 0 || *intAddress > numberOfCams)
				{
					*intAddress = 0;
				}

				cameraIdEditableText->SetText(FText::FromString(FString::FromInt(*intAddress)));
			}
		}
	}
}
