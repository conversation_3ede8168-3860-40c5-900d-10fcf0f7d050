/*--------------------------------------------------------------
|        Copyright (C) 1997-2008 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#include "WWUILeaderboardPopulator.h"
#include "Runtime/Core/Public/Math/Color.h"
#include "Match/SIFUIConstants.h"
#include "WWUIScrollbox.h"
#include "WWUIListField.h"
#include "WWUITranslationManager.h"
#include "Image.h"
#include "Networking/SIFOnlineConstants.h"
#include "RugbyGameInstance.h"
//#include "SIFLanguage.h"
//#include "SIFPlayerProfileManager.h"
#include "Utility/Helpers/SIFUIHelpers.h"
#include "UI/WWUILeaderboard.h"
#include "LevelData/SIFLevelLauncher.h"
#include "UI/GeneratedHeaders/WWUIScreenLeaderboards_UI_Namespace.h"
#include "PanelWidget.h"
#include "ScrollBox.h"
#include "Networking/LeaderboardManager.h"

const TArray<FName> OnlineLeaderboardListHeaderNames =
{
	"TextRank",
	"TextGamerTag",
	"TextGamesPlayed",
	"TextWins",
	"TextDraws",
	"TextLosses",
	"TextReliability",
	"TextPoints"
};
#ifndef NRL_USE_13S_MODE 
#define NRL_USE_13S_MODE 1
#endif
#define SCORESPERPAGE 15
#define MAX_RELIABLE_VALUE (100)
#define MAX_COLUMN_VALUE (9999)
#define MAX_SCORE_VALUE  (9999999)

#if PLATFORM_XBOXONE
//#include "xboxone/SIFXboxoneLeaderboard.h"
//#include "XboxOneMabMatchMaking.h"
#endif


#if (PLATFORM_PS4)
//#include "PS4MabOnlineUserManager.h"
//#include "ps4/SIFPS4Leaderboard.h"
#endif

#if PLATFORM_WINDOWS && defined ENABLE_STEAM
//#include "steam_api.h"
//#include "pc/SIFSteamLeaderboard.h"
#endif

// Widget names for easy reference
// Note that this enum is ordered on the basis of the order of the data transmitted via the StatisticsMessages--
//	for a given row, a given slice of the vector will be ordered PLACING, NAME, and then as many scores as are 
//	appropriate for the given leaderboard. A change to this enum's order should force a change to the order
//	that the data is sent in the StatsMessage.
enum LEADERBOARD_COLUMN_VALUES
{
	LBW_RANK = 0,
	LBW_GAMERTAG,
	LBW_WINS,
	LBW_DRAWS,
	LBW_LOSSES,
	LBW_RELIABILITY,
	LBW_POINTS,

	LBW_MAX
};
// This array of text children must match the above enum
TArray<const char*> LEADERBOARD_COLUMN_NAMES =
{
	"Rank",
	"GamerTag",
	"Wins",
	"Draws",
	"Losses",
	"Reliability",
	"Points"
};

const int UWWUILeaderboardPopulator::MAX_NUM_COLUMNS = (int)LEADERBOARD_COLUMN_NAMES.Num();

//const MabColour HEADER_ROW_COLOUR(0.07f,0.122f,0.152f);
const FLinearColor INACTIVE_TEXT_COLOUR(1.0f, 1.0f, 1.0f);
const FLinearColor SELECTED_TEXT_COLOUR(0.0f, 0.0f, 0.0f);
const FLinearColor ACTIVE_ROW_COLOUR(1.0f, 1.0f, 1.0f);
const FLinearColor INACTIVE_ODD_ROW_COLOUR(0.0f,0.0f,0.0f);
const FLinearColor INACTIVE_EVEN_ROW_COLOUR(0.0f,0.0f,0.0f);

const FLinearColor RELIABILITY_EXCELLENT_COLOUR(0.1714f,0.6445f,0.0212f);
const FLinearColor RELIABILITY_GOOD_COLOUR(0.8308f,0.5776f,0.0168f);
const FLinearColor RELIABILITY_POOR_COLOUR(0.1274f,0.1274f,0.1274f);

const float INACTIVE_ARROW_ALPHA = 0.25f;
const float ACTIVE_ARROW_ALPHA = 1.0f;

// Constants - UI objects
static const char LEADERBOARD_HEADER_SUBENTRY_NAME[] = "LeaderboardHeaderItem";
static const char LEADERBOARD_HEADER_UI_CONTAINER_NAME[] = "LeaderboardHeading";
static const char LEADERBOARD_HIGHLIGHTS_CONTAINER_NAME[] = "Highlights";
//static const char LEADERBOARD_TYPE_TITLE[] = "../../LBType/Title";
static const char LEADERBOARD_TYPE_FILTER[] = "../Filter/filter_bar_mid/Name";
static const char LEADBEROARD_ARROW_LABEL_NAME[] = "LBArrows";
const int UP_ARROW_IDX = 0;
const int DOWN_ARROW_IDX = 1;

// Constants - Filter names
//static const char LEADERBOARD_FILTER_ID[] = "[ID_LEADERBOARDS_FILTER_TITLE]";
static const char LEADERBOARD_FILTER_FRIENDS_ID[] = "[ID_LEADERBOARDS_FILTER_FRIENDS]";
static const char LEADERBOARD_FILTER_OVERALL_ID[] = "[ID_LEADERBOARDS_FILTER_OVERALL]";
static const char LEADERBOARD_FILTER_MYSCORE_ID[] = "[ID_LEADERBOARDS_FILTER_MYSCORE]";

#if ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE
 
static const char LEADERBOARD_SEVEN_FILTER_FRIENDS_ID[] = "[ID_LEADERBOARDS_FILTER_FRIENDS_SEVEN]";
static const char LEADERBOARD_SEVEN_FILTER_OVERALL_ID[] = "[ID_LEADERBOARDS_FILTER_OVERALL_SEVEN]";
static const char LEADERBOARD_SEVEN_FILTER_MYSCORE_ID[] = "[ID_LEADERBOARDS_FILTER_MYSCORE_SEVEN]";

static const char LEADERBOARD_FIFTEEN_FILTER_FRIENDS_ID[] = "[ID_LEADERBOARDS_FILTER_FRIENDS_FIFTEEN]";
static const char LEADERBOARD_FIFTEEN_FILTER_OVERALL_ID[] = "[ID_LEADERBOARDS_FILTER_OVERALL_FIFTEEN]";
static const char LEADERBOARD_FIFTEEN_FILTER_MYSCORE_ID[] = "[ID_LEADERBOARDS_FILTER_MYSCORE_FIFTEEN]";

#endif

// Constants - Load failure message
static const char LEADERBOARD_LOAD_FAILED_ID[] = "[ID_LEADERBOARD_LOAD_FAILED]";

// Constants - parameter names.
static const char MIN_SELECTABLE_INDEX_NAME[] = "MIN_SELECTABLE_INDEX";
static const char CURR_SELECTED_INDEX_NAME[] = "CURRENT_SELECTED_INDEX";
static const char CURR_DATA_INDEX_NAME[] = "CURRENT_DATA_INDEX";
static const char NUM_ROWS_NAME[] = "NUM_ROWS";
static const char NUM_COLUMNS_NAME[] = "NUM_COLUMNS";
static const char CONTROLLER_ID_NAME[] = "CONTROLLER_ID";
static const char IS_DISCONTINUOUS_LIST_NAME[] = "IS_DISCONTINUOUS_LIST";
static const char CONTAINER_INITIALISED[] = "CONTAINER_INITIALISED";

#ifdef ENABLE_NO_STATS
// Constant - Not translated b/c should only be seen in dev.
static const char NO_STATS_STRING[] = "No-stats mode turned on. Stats reads not possible.";
#endif

static const int TEXT_INDEX = 1;

UWWUILeaderboardPopulator::UWWUILeaderboardPopulator()
	: UWWUIPopulator(),
	lbrd_queue()
{
}

UWWUILeaderboardPopulator::UWWUILeaderboardPopulator(UWWUILeaderboardQueue* const _lbrd_queue ) 
	:	UWWUIPopulator( ),
		lbrd_queue( _lbrd_queue )
{
	no_data_text_map[ UWWUILeaderboard::FRIEND ] = "[ID_LEADERBOARD_NO_DATA_FRIEND]";
	no_data_text_map[ UWWUILeaderboard::OVERALL ] = "[ID_LEADERBOARD_NO_DATA_OVERALL]";
	no_data_text_map[ UWWUILeaderboard::MY_SCORE ] = "[ID_LEADERBOARD_NO_DATA_MY_SCORE]";

#if (PLATFORM_PS4)
	no_data_text_map[ UWWUILeaderboard::FRIEND_SEVEN ] = "[ID_LEADERBOARD_NO_DATA_FRIEND]";
	no_data_text_map[ UWWUILeaderboard::OVERALL_SEVEN ] = "[ID_LEADERBOARD_NO_DATA_OVERALL]";
	no_data_text_map[ UWWUILeaderboard::MY_SCORE_SEVEN ] = "[ID_LEADERBOARD_NO_DATA_MY_SCORE]";
#endif

	lbrd_queue->SetLeaderboardPopulator( this );

}

UWWUILeaderboardPopulator::~UWWUILeaderboardPopulator(void)
{
	lbrd_queue = nullptr;
}

void UWWUILeaderboardPopulator::Refresh(UWidget* widget)
{
	UScrollBox* scrollBox = Cast<UScrollBox>(widget);

	if (scrollBox)
	{
		if (scrollBox->GetChildrenCount() > 0)
		{
			Clear(widget);
		}

		Populate(widget);
	}
	else
	{
		FString errorString = widget != nullptr ? *widget->GetPathName() : FString("NULL");
		UE_LOG(LogTemp, Error, TEXT("Cast to scroll box failed while attempting to PopulateAndRefresh on node %s"), *errorString);
	}
}

void UWWUILeaderboardPopulator::Populate(UWidget* node)
{
	if (inPreConstruct)
	{
		Super::Populate(node);
		return;
	}

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		ULeaderboardManager* pLeaderboardManager = pRugbyGameInstance->GetLeaderboardManager();

		if (pLeaderboardManager)
		{
			TArray<TSharedPtr<FRugbyLeaderboardRow>> CurrentLeaderboardList = pLeaderboardManager->GetLeaderboardValues();
			
			if(b_ShowDebugValues)
			{
				for(int i = 0; i < CurrentLeaderboardList.Num(); ++i)
				{
					CurrentLeaderboardList[i]->score = MAX_SCORE_VALUE + 1;
					CurrentLeaderboardList[i]->id = MAX_COLUMN_VALUE + 1;
					CurrentLeaderboardList[i]->wins = MAX_COLUMN_VALUE + 1;
					CurrentLeaderboardList[i]->draws = MAX_COLUMN_VALUE + 1;
					CurrentLeaderboardList[i]->losses = MAX_COLUMN_VALUE + 1;
					CurrentLeaderboardList[i]->reliability = MAX_int32;
				}
			}

			OnlineLeaderboardNodeCreationCallback callbackObject(node, dataList.ArrayOption, CurrentLeaderboardList);

			int numberOfLeaderboardIndexes = CurrentLeaderboardList.Num();
			CreateNodesFromTemplate(dataList.TemplateName, numberOfLeaderboardIndexes, &callbackObject);
		}
	}
}

void UWWUILeaderboardPopulator::PopulateAndRefresh(UWidget* widget)
{
	if (UWWUIScrollBox * node = Cast<UWWUIScrollBox>(widget))
	{
#if PLATFORM_XBOX360 || PLATFORM_PS3 || PLATFORM_WINDOWS || (PLATFORM_PS4) || (PLATFORM_XBOXONE)

		MABASSERT(node);

		if (lbrd_queue && lbrd_queue->GetCurrentLeaderboard())
			SetFilterLabelString(node, lbrd_queue->GetCurrentLeaderboard()->GetFilter());

		// Check if it's a container and it's initialised.
		if (GetContainerInitialised(node) == CONT_NOT_INITIALISED)
		{
			Populate(node);
		}
#else
		MABUNUSED(node);
#endif
	}
}

bool UWWUILeaderboardPopulator::ShouldDeferPopulationEvent(UWWUIScrollBox* node, void* userdata /*= NULL*/ )
{
	// Check if it's a container and it's initialised.
	return (GetContainerInitialised(node) == CONT_NOT_INITIALISED); 
}

// \brief Fills out a UI object with all the info it's supposed to be populated with
// @param object	the UI object to populate
void UWWUILeaderboardPopulator::FilloutDataLines(UWWUIScrollBox* container, const TArray<FString>& data_container )
{
	if(container && container->ScreenRef)
	{
		// Get some reqd local variables.
		int min_selectable_index = container->GetIntProperty( MIN_SELECTABLE_INDEX_NAME );
		int current_selected_index = container->GetIntProperty( CURR_SELECTED_INDEX_NAME );
		int num_columns = container->GetIntProperty( NUM_COLUMNS_NAME );

		
	#if PLATFORM_PS4
		if (num_columns != LEADERBOARD_COLUMN_NAMES.Num())
	#else
		if (num_columns > LEADERBOARD_COLUMN_NAMES.Num())
	#endif
			num_columns = LEADERBOARD_COLUMN_NAMES.Num();

		// Get the header object.
		UPanelWidget* header_object = Cast<UPanelWidget>(container->ScreenRef->FindChildWidget(FString(LEADERBOARD_HEADER_UI_CONTAINER_NAME)));
		if (header_object)
		{
			header_object->SetVisibility(ESlateVisibility::Visible); // show, now we're putting info into them
			UWidget* first_header_node = container->ScreenRef->FindChildWidget(FString(LEADERBOARD_HEADER_SUBENTRY_NAME) + "_0");
		}

		// Loop over all members
		for (int line_index = 0; line_index < (int)container->GetListLength(); ++line_index)
		{
			UWWUIListField* object = container->GetListField((size_t)line_index);

			// check we have an entry for this line
			if (!object)
			{
				return;
			}

			FLinearColor row_colour = INACTIVE_ODD_ROW_COLOUR;

			// Discovering which colour to set this row to--colour selected row differently than non-selected
			//	NOTE: Off by one--technically the size of the min_selectable_index--for header lines
			if (line_index == current_selected_index + min_selectable_index)
			{
				row_colour = ACTIVE_ROW_COLOUR; // Active row text colour
			}
			else
			{
				if (line_index % 2 == 0)
					row_colour = INACTIVE_EVEN_ROW_COLOUR; // Inactive row text colour
			}

			// Set the row colour
			/*   #MB - set the colour for the row if needed
			MabUINode* row = row_listbox->GetChildByIndex(line_index);
			row->SetColour(row_colour);
			*/
			/*SIFUIHelpers::NodeSetColourOnChild( row, 0, row_colour );
			SIFUIHelpers::NodeSetColourOnChild( row, 1, row_colour );
			SIFUIHelpers::NodeSetColourOnChild( row, 2, row_colour );*/

			// This is the index of the first element of data for this row
			size_t base_data_index = (line_index)* num_columns;

			// Check if we've run out of data
			bool zero_out_row = (base_data_index >= data_container.Num());

			//MABASSERTMSG( zero_out_row || ( data_container.Num() >= base_data_index + num_columns ), "We have an uneven data row--we need more columns of data than we have for this row!" );

			// get the current locale and create a formatter so we can format numbers.
			/*MabLocaleInterface* current_locale = SIFLanguage::GetCurrentLocale();				//#MB - Add formatter  
			MabLocaleNumberFormat number_format = current_locale->GetNumberFormat();
			number_format.decimal.num_decimal_digits = 0;
			MabLocaleStringFormatter formatter;*/

			// Iterate through the name column--gamertag, in the case of the 360--and the three possible score columns
			for (int i = 0; i < (int)num_columns; ++i)
			{
				// Get the given widget and specify that it's a text widget
				UTextBlock* text_widget = Cast<UTextBlock>(object->ScreenRef->FindChildOfTemplateWidget(object, FString(LEADERBOARD_COLUMN_NAMES[i])));
				FString text;
				MABASSERT(text_widget);

				FLinearColor text_colour = INACTIVE_TEXT_COLOUR;
				if (line_index == current_selected_index + min_selectable_index)
					text_colour = SELECTED_TEXT_COLOUR;

				//	Ensure that:
				//	a) we're not zeroing out the row
				//	b) we expect there to be data in this column--i.e. if this column only has one entry we don't care about LBW_SCORE2 and LBW_SCORE3;
				//	c) the data has been successfully populated--should be caught by size checks above
				if (!zero_out_row && num_columns > i && (int)data_container.Num() > (line_index)* num_columns + i)
				{
					if (data_container.IsValidIndex(base_data_index + i))
					{
						const FString &text_data_value = data_container[base_data_index + i];

						// convert the data value to an int and format it with thousands seperators(if applicable for the column type)
						if (i == LBW_WINS || i == LBW_DRAWS || i == LBW_LOSSES || i == LBW_POINTS)
						{
							int number_data_value = 0;
							MabTypeConverter<const FString, int>::Convert(text_data_value, number_data_value);
							// Use the current locale and format the number
							text = FString::FromInt(number_data_value);    //#MB - add formatter call if needed
						}
						else
						{
							// default formatting, plain text
							text = text_data_value;
						}

						// check for special case formatting for reliablilty
						if (i == LBW_RELIABILITY)
						{
							int number_data_value = 0;
							MabTypeConverter<const FString, int>::Convert(text_data_value, number_data_value);

							// add the percentage to the end
							text += "%";
							// set the colour (selected state is irrelevant)
							if (number_data_value < 60)
							{
								text_colour = RELIABILITY_POOR_COLOUR;
							}
							else if (number_data_value < 80)
							{
								text_colour = RELIABILITY_GOOD_COLOUR;
							}
							else
							{
								text_colour = RELIABILITY_EXCELLENT_COLOUR;
							}
						}
					}
				}
				else {
					text = "";
				}

	#ifdef _DEBUG
				if (!text.empty() && i == 1)
				{
					MABLOGDEBUG("Setting values on row %d: column(%d / %s), text(%s)",
						line_index, i, LEADERBOARD_COLUMN_NAMES[i], text.c_str());
				}
	#endif

				if (text_widget)
				{
					text_widget->SetText(FText::FromString(text));
					//text_widget->SetColour(text_colour);			//#MB - set text colour if needed
				}
			}
		}
		
	}
}

// Create the header line for the leaderboard table
void UWWUILeaderboardPopulator::CreateHeaderLine(UWWUIScrollBox *object, int num_columns)
{
	UTextBlock* text_widget = NULL;
	FString text;

	UWWUILeaderboard *leaderboard = lbrd_queue->GetCurrentLeaderboard();
	const TArray<FString>& columns = leaderboard->GetFullColumnNameList();
	if (num_columns > LEADERBOARD_COLUMN_NAMES.Num())
	{
		MABLOGDEBUG("Original num_columns=%d",num_columns);
		num_columns = LEADERBOARD_COLUMN_NAMES.Num();
	}
	//MABLOGDEBUG(FString(0,"Num Columns: %d", num_columns).c_str());
	MABASSERTMSG(num_columns == (int)columns.Num(), "There should always be the same number of header strings as there are columns!");

	// Go through each column and set the header!
	for ( unsigned int col_id = 0; col_id < (unsigned int)num_columns; ++col_id )
	{
		text_widget = Cast<UTextBlock>( object->ScreenRef->FindChildWidget(FString(LEADERBOARD_COLUMN_NAMES[col_id])));
		MABASSERT( text_widget );
		TArray<FStringFormatArg> args;
		args.Add(FStringFormatArg(columns[col_id]));
		text = FString::Format(TEXT("%s"), args);
		//MABLOGDEBUG(FString(0,"Got column for #%d, writing label to: %s", col_id, columns[col_id]));
		if (text_widget)
		{
			text_widget->SetText(FText::FromString(text));
			text_widget->SetVisibility(ESlateVisibility::Visible);
			//text_widget->SetColour(HEADER_ROW_COLOUR);
		}
	}
}

// Display the loading message
void UWWUILeaderboardPopulator::PopulateWithText( UWWUIScrollBox* container, const FString& /*message_in*/ )
{
	//UTextBlock *widget;

	// Dim the arrows of the listbox while in this function.
	DimArrows( container, true, true );

	// Set the row colour
	/*				#MB - set colour if needed
	MabUINode* row_listbox = SIFUIHelpers::GetUINode( context.c_str() );
	MabUINode* row;

	for(size_t i = 0; i < container->GetNumChildren(); i++) {
		obj = container->GetChildByIndex(i);

		// set the colour on the row backgrounds
		row = row_listbox->GetChildByIndex( i );
		MabColour line_col = INACTIVE_ODD_ROW_COLOUR;
		if (i % 2 == 0)
			line_col = INACTIVE_EVEN_ROW_COLOUR;
		row->SetColour(line_col);
		SIFUIHelpers::NodeSetColourOnChild( row, 0, line_col );		
		SIFUIHelpers::NodeSetColourOnChild( row, 1, line_col );	
		SIFUIHelpers::NodeSetColourOnChild( row, 2, line_col );	

		// Set the text in each column
		for(int j = 0; j < MAX_NUM_COLUMNS; j++)
		{
			widget = MabCast< MabUITextInterface >(obj->GetChildByIndex(j));
			if (widget)
			{
				if(i % 2 == 1 && j == TEXT_INDEX) {
					widget->SetText( message_in );
				}
				else
				{
					widget->SetText("");
				}
				widget->SetColour(INACTIVE_TEXT_COLOUR);
			}
		}

	}
	*/
}

// Sets the filter title. Refactored here for convenience.
void UWWUILeaderboardPopulator::SetFilterLabelString( UWWUIScrollBox* populated_object, UWWUILeaderboard::FILTER filter )
{
	// Work out what text we want to set
	int count = 0;
	FString text("");

#if (ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE)
	switch(filter)
	{
	case UWWUILeaderboard::FRIEND:
		text = LEADERBOARD_FIFTEEN_FILTER_FRIENDS_ID;
		count = 1;
		break;
	case UWWUILeaderboard::OVERALL:
		text = LEADERBOARD_FIFTEEN_FILTER_OVERALL_ID;
		count = 3;
		break;
	case UWWUILeaderboard::MY_SCORE:
		text = LEADERBOARD_FIFTEEN_FILTER_MYSCORE_ID;
		count = 2;
		break;
	case UWWUILeaderboard::FRIEND_SEVEN:
		text = LEADERBOARD_SEVEN_FILTER_FRIENDS_ID;
		count = 4;
		break;
	case UWWUILeaderboard::OVERALL_SEVEN:
		text = LEADERBOARD_SEVEN_FILTER_OVERALL_ID;
		count = 6;
		break;
	case UWWUILeaderboard::MY_SCORE_SEVEN:
		text = LEADERBOARD_SEVEN_FILTER_MYSCORE_ID;
		count = 5;
		break;
	default:
		MABBREAKMSG("THIS IS NOT A VALID FILTER!");
	}


#else
	switch(filter)
	{
	case UWWUILeaderboard::FRIEND:
		text = LEADERBOARD_FILTER_FRIENDS_ID;
		count = 1;
		break;
	case UWWUILeaderboard::OVERALL:
		text = LEADERBOARD_FILTER_OVERALL_ID;
		count = 3;
		break;
	case UWWUILeaderboard::MY_SCORE:
		text = LEADERBOARD_FILTER_MYSCORE_ID;
		count = 2;
		break;
	default:
		MABBREAKMSG("THIS IS NOT A VALID FILTER!");
	}
#endif
	// Get the node and set the text
	UTextBlock *filter_node = Cast<UTextBlock>(populated_object->ScreenRef->FindChildWidget(FString(LEADERBOARD_TYPE_FILTER)));
	MABASSERT(filter_node != NULL);
	if (filter_node != NULL )
	{
		filter_node->SetText(FText::FromString(text));
	}

	UTextBlock *count_node = Cast<UTextBlock>(populated_object->ScreenRef->FindChildWidget(FString("Count")));
	MABASSERT(count_node != NULL);
	if (count_node != NULL )
	{
		FString ct;
		TArray<FStringFormatArg> args;
		args.Add(FStringFormatArg(count));
		args.Add(FStringFormatArg(UWWUILeaderboard::NUM_FILTERS));
		ct = FString::Format(TEXT("%d/%d"), args);
		count_node->SetText(FText::FromString(ct));
	}
}

// Scroll up or down the current leaderboard
void UWWUILeaderboardPopulator::Scroll(UWWUIScrollBox* object, const FString& direction )
{
	// No scrolling permitted if we have no data
	if ( object->GetListLength() == 0 ) return;

	// HACK: These values should be replaced with some kind of link to the file that knows about them, MabUIActionTranslator.h.
	// CJS: these should not be static
	static const FString MABUI_DOWN;					//#MB - find the direction names
	static const FString MABUI_UP;
	static const FString MABUI_PAGEDOWN;
	static const FString MABUI_PAGEUP;

	// Setup required data.
	int current_data_index_delta = 0, current_selected_index_delta = 0;
	int current_selected_index = object->GetIntProperty( CURR_SELECTED_INDEX_NAME );
	int current_data_index = object->GetIntProperty( CURR_DATA_INDEX_NAME );
	int num_rows = object->GetIntProperty( NUM_ROWS_NAME );
	int top_index = (object->GetBoolProperty( IS_DISCONTINUOUS_LIST_NAME )) ? 1 : 0;

	// This should be a switch statement, but we can't get the enum from MabUIActionTranslator.h, and switches don't work on strings.
	if ( direction == MABUI_DOWN )
	{
		current_data_index_delta = current_selected_index_delta = 1;
	} else if ( direction == MABUI_UP )
	{
		current_data_index_delta = current_selected_index_delta = -1;
	} else if ( direction == MABUI_PAGEDOWN )
	{
		if ( current_selected_index == num_rows - 1 )
		{
			// In the case when we're on the bottom row and want to page down, keep the current_selected_index the same and
			//	just move down to the next group of num_rows entries.
			current_data_index_delta = num_rows;
		} else {
			// In the case when we're NOT on the bottom row, just scroll down to the bottom row.
			current_data_index_delta = current_selected_index_delta = num_rows - 1 - current_selected_index;
		}
	} else if ( direction == MABUI_PAGEUP )
	{
		if ( current_selected_index == top_index )
		{
			// In the case when we're on the top row and want to page up, keep the current_selected_index the same and
			//	just move up to the next group of num_rows entries.
			current_data_index_delta = -1 * num_rows;
		} else {
			// In the case when we're NOT on the top row, just scroll up to the top row.
			current_data_index_delta = current_selected_index_delta = -1 * current_selected_index;
		}
	} else {
		MABBREAKMSG( "Unfamiliar direction string!" );
	}

	current_data_index += current_data_index_delta;

	if( current_data_index < 0 ) {
		current_data_index = current_selected_index = 0;	// Set both fields to the top entry...
		current_selected_index_delta = 0;					// And zero out the delta.
	} else if( (unsigned int) current_data_index >= lbrd_queue->GetCurrentLeaderboard()->GetNumTotalEntries() ) {
		current_data_index = lbrd_queue->GetCurrentLeaderboard()->GetNumTotalEntries() - 1; //off-by-one b/c of index vs. size
		MabMath::ClampLower(current_data_index, 0);
		current_selected_index = MabMath::Min( current_data_index, num_rows - 1 );
		MabMath::ClampLower(current_selected_index, 0);
		current_selected_index_delta = 0;					// Zero out the delta.
	} 

	current_selected_index += current_selected_index_delta;

	// By default....
	MABASSERTMSG( current_data_index - current_selected_index >= 0, "Leaderboards code; current_data_index and current_selected_index do not have the standard relationship!" );
	int start_index = current_data_index - current_selected_index;

	// If we've come out of the top of the list of visible leaderboard entries
	if ( current_selected_index < top_index )
	{
		current_selected_index -= current_data_index_delta;
		
		// If we're discontinuous--the top_index check is cheaper than the more logical 
		//	( object->GetNamedValue( IS_DISCONTINUOUS_LIST_NAME )->ToBoolean() )--
		//	and we're attempting to move UP from the #3 location to the #2--that is, if
		//	we're getting rid of the gap between 1 and everything else--then we need
		//	to change the starting index for our request up to 0.
		if ( top_index == 1 && current_data_index == 1 )
		{
			// any change to current_data_index? maybe.
			start_index = 0;
		} else {
			start_index = current_data_index;
		}
	} else 
	// If we've come out of the bottom of the list of visible leaderboard entries	
	if ( current_selected_index >= num_rows )
	{
		current_selected_index -= current_data_index_delta;
		start_index = current_data_index - num_rows + 1;
	}

	// Sync data on the listbox.
	object->SetProperty(CURR_SELECTED_INDEX_NAME, &current_selected_index, PROPERTY_TYPE_INT);
	object->SetProperty(CURR_DATA_INDEX_NAME, &current_data_index, PROPERTY_TYPE_INT);

	last_request_origin = SCROLL;

	// Only put up the Loading text if RequestData returns false, i.e. it requires an asynch write
	UWWUILeaderboard::FILTER tmp_filter = ( lbrd_queue->GetCurrentLeaderboard()->GetFilter() == UWWUILeaderboard::MY_SCORE ) ? UWWUILeaderboard::OVERALL : UWWUILeaderboard::NUM_FILTERS;
#if (ENABLE_SEVENS_MODE  && !NRL_USE_13S_MODE)
	if(tmp_filter == UWWUILeaderboard::NUM_FILTERS)
		tmp_filter = ( lbrd_queue->GetCurrentLeaderboard()->GetFilter() == UWWUILeaderboard::MY_SCORE_SEVEN ) ? UWWUILeaderboard::OVERALL_SEVEN : UWWUILeaderboard::NUM_FILTERS;
#endif 
	if ( !lbrd_queue->GetCurrentLeaderboard()->RequestData( start_index, tmp_filter, object->GetName(), false ) ) 
	{
		SetLoadingStatus(object);
	}	
}

// Change the current filter
void UWWUILeaderboardPopulator::ChangeFilter( UWWUIScrollBox* object, int direction )
{
	// Reset to the top of the list when we switch filters
	int tempZero = 0;
	object->SetProperty(CURR_DATA_INDEX_NAME, &tempZero, PROPERTY_TYPE_INT);
	object->SetProperty(CURR_SELECTED_INDEX_NAME, &tempZero, PROPERTY_TYPE_INT);
	bool is_sync = true;
	last_request_origin = FILTER;
	if( lbrd_queue && lbrd_queue->GetCurrentLeaderboard() )
	{
#if (PLATFORM_PS4) || (PLATFORM_XBOX360) || (PLATFORM_XBOXONE) || (PLATFORM_PS3) || ((PLATFORM_WINDOWS) && defined (ENABLE_STEAM))
		int newLBId = 0;
		UWWUILeaderboard::FILTER f = lbrd_queue->GetCurrentLeaderboard()->NextFilter(direction);
		if (lbrd_queue->GetCurrentLeaderboard()->changeLeaderboardForFilter(f,&newLBId))
			lbrd_queue->SetLeaderboard(newLBId,true,true,&is_sync,object->GetName());
#endif
		if (direction > 0)
			lbrd_queue->GetCurrentLeaderboard()->IncrementFilter( false, true, &is_sync, object->GetName() );
		else
			lbrd_queue->GetCurrentLeaderboard()->DecrementFilter( false, true, &is_sync, object->GetName() );
	}
	if ( !is_sync ) 
		SetLoadingStatus(object);

	// Setup title for magic things.
	if( lbrd_queue && lbrd_queue->GetCurrentLeaderboard() )
		SetFilterLabelString( object, lbrd_queue->GetCurrentLeaderboard()->GetFilter() );
}

#if PLATFORM_XBOXONE

void UWWUILeaderboardPopulator::QueryGamercard( UWWUIScrollBox* object )
{

	//#MB - rc3_legacy_xbone
	// Sanity check to make sure we're in 360

	//FString user_id = Cast<SIFXboxOneLeaderboard>( lbrd_queue->GetCurrentLeaderboard() )->GetXUIDFromIndex(object->GetIntProperty(CURR_DATA_INDEX_NAME));
	//MABLOGDEBUG("selected xuid is: %s" ,user_id);
	//SIFApplication::GetApplication()->GetMatchMakingManager()->ShowGameCard(&user_id);
}


#endif


#if (PLATFORM_PS4)

// Sets the leaderboard's controller_index that controls this run of the leaderboards
void UWWUILeaderboardPopulator::SetControllerIndex(  UWWUIScrollBox* object, int _controller_id )
{
	int controller_id = -1;
	
	// If we already have one...
	if (object->GetIntProperty(CONTROLLER_ID_NAME))
	{
		controller_id = object->GetIntProperty(CONTROLLER_ID_NAME);
	}

	if ( controller_id != _controller_id ) 
	{
		object->SetProperty(CONTROLLER_ID_NAME, &_controller_id, PROPERTY_TYPE_INT);

	} else {
		object->SetProperty(CONTROLLER_ID_NAME, &_controller_id, PROPERTY_TYPE_INT);
	}
}

#endif

void UWWUILeaderboardPopulator::Update(const UWWUILeaderboardPopulatorMessage& msg )
{
	if (GetWorld() && GetWorld()->GetGameInstance<URugbyGameInstance>() && GetWorld()->GetGameInstance<URugbyGameInstance>()->GetWindowSystem())
	{
		UE_LOG(LogNetworkNonGame, Display, TEXT("Leaderboard: CANCEL TIMER"));
		//GetWorld()->GetGameInstance<URugbyGameInstance>()->GetWindowSystem()->ClearTimer("leaderboard_load_timeout");

		// Get the container from the message.
		UWWUIScrollBox* container = Cast<UWWUIScrollBox>(ScreenRef->FindChildWidget(msg.GetUIElemName()));

		if (container)
		{
			// Check first of all to make sure that the message we're receiving is the message that we want--the player
			//	might have moved on to another leaderboard or filter in the meantime
			if (msg.GetLeaderboardID() != lbrd_queue->GetCurrentLeaderboard()->GetID() || msg.GetFilter() != lbrd_queue->GetCurrentLeaderboard()->GetFilter())
			{
				MABLOGDEBUG("This is not the leaderboard/filter you're looking for!");

				// Also, the message indicates there has been a loading failure
				if (!msg.GetResult())
				{
					// Clear all entries and hide highlights
					if (container)
					{
						PopulateWithText(container, "");
					}
					// Set load failure status
					FString translated = UWWUITranslationManager::Translate(LEADERBOARD_LOAD_FAILED_ID);
					lbrd_queue->GetCurrentLeaderboard()->SetStatusMessage(container->GetListField(0), translated);   //#MB - change this to refernce the status object
				}

				return;
			}

			int num_entries = msg.GetMax() - msg.GetMin() + 1; // Min and Max are inclusive

			// Construct a temporary container.
			TArray< FString > new_data = msg.GetData();
			int sizeofdata = (int)new_data.Num();

			// Safety net, merged from Shatter.
			if (container == NULL)
			{
				MABBREAKMSG("The Leaderboard container you are trying to fill does not exsist. It is either not defined in data or not currently loaded");
				return;
			}

#ifdef ENABLE_FIRST_PLACE_RANK_DISPLAYED
			TArray< FString > tmp_first_place_strings;
			bool has_first_place_entry = lbrd_queue->GetCurrentLeaderboard()->GetTopRankStrings(&tmp_first_place_strings);

			// It's considered a discontinuous list if the minimum index ever isn't 0.
			container->SetValue<bool>(IS_DISCONTINUOUS_LIST_NAME, (msg.GetMin() != 0));

			// Need to act if this message includes the top-ranked field AND we're not already accounting 
			//	for first place in the min/filter
			if (has_first_place_entry && sizeofdata >= 0 && (msg.GetFilter() == SIFXbox360Leaderboard::FRIEND || msg.GetMin() != 0))
			{
				MABASSERTMSG(sizeofdata >= (int)tmp_first_place_strings.Num(), "This is a very odd situation where this message is giving back more than one string entry but fewer than the number of strings for the first-place entry.");
				TArray< FString >::iterator data_iter = new_data.begin();
				// Since we'll be replacing the same number of elements no reason to change size
				for (TArray< FString >::const_iterator first_place_iter = tmp_first_place_strings.begin(); first_place_iter != tmp_first_place_strings.end(); ++first_place_iter, ++data_iter);
				new_data.erase(new_data.begin(), data_iter);
				new_data.insert(new_data.begin(), tmp_first_place_strings.begin(), tmp_first_place_strings.end());
				MABASSERTMSG((int)new_data.Num() == sizeofdata, "Something has gone unexpectedly wrong in replacing the first few strings of this record with the first-place record!");
				//num_entries += 1;
			}
#endif

			// The message indicates there has been a loading failure
			if (!msg.GetResult())
			{
				MABASSERTMSG(sizeofdata == 0, "Leaderboard populator message indicates failure but there is some data available?");
				// Clear all entries and hide highlights
				if (container)
				{
					PopulateWithText(container, "");
				}
				// Set load failure status
				FString translated = UWWUITranslationManager::Translate(LEADERBOARD_LOAD_FAILED_ID);
				lbrd_queue->GetCurrentLeaderboard()->SetStatusMessage(container->GetListField(0), translated);   //#MB - change this to refernce the status object
				return;
			}

			// No data available
			if (sizeofdata == 0 && container)
			{
				// All entries are empty
				PopulateWithText(container, "");
				//// Hide all highlights
				//HideHighlights(container);
				// No-data message is now set in a new separate status text widget
				FString translated = UWWUITranslationManager::Translate(no_data_text_map[msg.GetFilter()]);
				lbrd_queue->GetCurrentLeaderboard()->SetStatusMessage(container->GetListField(0), translated);   //#MB - change this to refernce the status object
				return;
			}
			// Data available: clear status message
			FString translated = "";
			lbrd_queue->GetCurrentLeaderboard()->SetStatusMessage(container->GetListField(0), translated);      //#MB - change this to refernce the status object

			int current_data_index = container->GetIntProperty(CURR_DATA_INDEX_NAME);

			bool upper_dim = false;
			bool lower_dim = false;
			if (current_data_index == 0)
			{
				upper_dim = true;
			}
			if (current_data_index == (int)MabMath::Max(lbrd_queue->GetCurrentLeaderboard()->GetNumTotalEntries() - 1, 0u))
			{
				lower_dim = true;
			}
			DimArrows(container, upper_dim, lower_dim);

			int tempRef = (sizeofdata / num_entries);
			container->SetProperty(NUM_COLUMNS_NAME, &tempRef, PROPERTY_TYPE_INT);

			int controller_id = SIFUIHelpers::GetCurrentMasterControllerIndex();
			// Changed so that if (by some fluke) the leaderboards container doesn't have a controller ID, we have something safe in there
			if (container->GetIntProperty(CONTROLLER_ID_NAME))
				controller_id = container->GetIntProperty(CONTROLLER_ID_NAME);

			// We need to potentially shore up our selected_index, because we might have scrolled past it
			unsigned int potential_max_num = lbrd_queue->GetCurrentLeaderboard()->GetNumTotalEntries();
			int present_data_index = container->GetIntProperty(CURR_DATA_INDEX_NAME);
			if (potential_max_num != 0)
			{
				int anotherTempRef = (int)MabMath::Min((int)potential_max_num - 1, present_data_index);
				container->SetProperty(CURR_DATA_INDEX_NAME, &anotherTempRef, PROPERTY_TYPE_INT);
				//Prepare to show friend leaderboard cache if required
#if (ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE)
				bool tempBool = true;
				if ((lbrd_queue->GetCurrentLeaderboard()->GetFilter() == UWWUILeaderboard::FRIEND || lbrd_queue->GetCurrentLeaderboard()->GetFilter() == UWWUILeaderboard::FRIEND_SEVEN) && container)
					container->SetProperty(CONTAINER_INITIALISED, &tempBool, PROPERTY_TYPE_BOOL);
#endif
			}

			bool haveProcessedData = false;
			// MY SCORE/FRIENDS ONLY: Need to SET the currently-selected index if we're on the My Score filter, 
			// but only if we've just switched to it
			int currentFilter = lbrd_queue->GetCurrentLeaderboard()->GetFilter();
			if ((currentFilter == UWWUILeaderboard::FRIEND
				|| currentFilter == UWWUILeaderboard::MY_SCORE
#if (ENABLE_SEVENS_MODE  && !NRL_USE_13S_MODE)
				|| currentFilter == UWWUILeaderboard::FRIEND_SEVEN
				|| currentFilter == UWWUILeaderboard::MY_SCORE_SEVEN
#endif
				)
				&& last_request_origin != SCROLL)
			{
				// All this means at the moment is that we need to make OUR index the selected one.
				// Go through the data entries trying to find my rank
#if (PLATFORM_PS4)
				MABUNUSED(controller_id);
				//PS4OnlineUser my_psn_user_id;
				//MABVERIFY(MabCast< SIFPS4LeaderboardQueue >(lbrd_queue)->GetUserManager().GetLocalNetworkUser(my_psn_user_id));			//#rc3_legacy_PS4
#elif (PLATFORM_WINDOWS) && defined (ENABLE_STEAM)
				MABUNUSED(controller_id);
				CSteamID my_steam_id = SteamUser()->GetSteamID();
#elif (PLATFORM_XBOXONE)
				MABUNUSED(controller_id);
				FString my_xuid;
				//SIFApplication::GetApplication()->GetMatchMakingManager()->GetMyID(my_xuid);   #rc3_legacy_xbone
#endif

				// Get the number of rows and columns off of the listbox.
				int num_rows = container->GetIntProperty(NUM_ROWS_NAME);
				int num_columns = container->GetIntProperty(NUM_COLUMNS_NAME);
#if PLATFORM_PS4
				num_columns = 8;	// always 8 input columns
#endif

				unsigned int i = 0;
				for (i = msg.GetMin(); i <= msg.GetMax(); ++i)
				{
					// Check to see if this rank is my rank
#if PLATFORM_PS4
			//TODO: see if this entry has the same PSN ID as our player
					if (0/*my_psn_user_id == MabCast< SIFPS4Leaderboard >(lbrd_queue->GetCurrentLeaderboard())->GetNPIDFromIndex(i)*/)		//#rc3_legacy_PS4
#elif PLATFORM_WINDOWS && defined (ENABLE_STEAM)
					if (MabCast< SIFSteamLeaderboard >(lbrd_queue->GetCurrentLeaderboard())->GetSteamIDFromIndex(i).GetStaticAccountKey() == my_steam_id.GetStaticAccountKey())
#elif PLATFORM_XBOXONE
			//TODO: see if this entry has the same XBOX User ID
					if (0/*MabCast< SIFXboxOneLeaderboard >(lbrd_queue->GetCurrentLeaderboard())->GetXUIDFromIndex(i) == my_xuid*/)  //rc3_legacy_xbone

#else
			// Expand this out for other platforms, other leaderboards.
					if (i - 1 < i)
#endif
					{
						// If it is, then you know the new selected data index
						container->SetProperty(CURR_DATA_INDEX_NAME, &i, PROPERTY_TYPE_INT);

						// In this case, the currently_selected_index might not be able to be the top entry because
						//	it's too close to the bottom; if your score is in next to last place, you want to see
						//	the scores of the other people on this leaderboard. That's the basic motivation of the computation below.
						int tempNumRef = (int)MabMath::Max(0, (MabMath::Min(num_entries, num_rows) - (num_entries - ((int)i - (int)msg.GetMin()))));
						container->SetProperty(CURR_SELECTED_INDEX_NAME, &tempNumRef, PROPERTY_TYPE_INT);

						// We want to get rid of all the data in the message and trim it down to just the bit we care about
						haveProcessedData = true;
						new_data.Reset();

						// We need to remove all data that is outside of our stated range -> Clamped to 0 (will only happen if we have fewer 
						// entries than rows).
						int tmp_top_index = MabMath::Min((int)i, num_entries + (int)msg.GetMin() - num_rows);
						MabMath::ClampLower(tmp_top_index, (int)msg.GetMin());

						// Fill out our temporary data object.
						int j = 0;
						int start = (tmp_top_index - msg.GetMin()) * num_columns;
						int end = (tmp_top_index - (int)msg.GetMin() + MabMath::Min(num_entries, num_rows)) * num_columns;
						if (end > (int)msg.GetData().Num())
							end = (int)msg.GetData().Num() / num_columns * num_columns;
						int colPos = 0;
						for (j = start; j < end; ++j, ++colPos)
						{
							//MABLOGDEBUG("ELEMENT[%d]='%s'",j,msg.GetData().at( j ).c_str());
#if PLATFORM_PS4
							if (colPos == 2)
								continue;	// skip GameType column for display
#endif					
							if(msg.GetData().IsValidIndex(j))
							{
								new_data.Add(msg.GetData()[j]);
								if (colPos >= num_columns)
									colPos = 0;
							}
						}

						break;
					}
				}
				if (currentFilter == UWWUILeaderboard::MY_SCORE
#if (ENABLE_SEVENS_MODE  && !NRL_USE_13S_MODE)
					|| currentFilter == UWWUILeaderboard::MY_SCORE_SEVEN
#endif
					)
					MABLOGDEBUG("I dont have any score yet");
				//			MABASSERTMSG( i <=  msg.GetMax(), "A score corresponding to your XUID doesn't appear to be present in the list of scores extracted from a MY_SCORES call!" );
			}
#if PLATFORM_PS4
			if (currentFilter == UWWUILeaderboard::OVERALL
				|| currentFilter == UWWUILeaderboard::OVERALL_SEVEN
				|| last_request_origin == SCROLL
				|| !haveProcessedData
				)
			{
				// trim unwanted columns
				int num_rows = container->GetIntProperty(NUM_ROWS_NAME);
				int num_columns = 8;//container->GetProperty( NUM_COLUMNS_NAME )->get<int>();
				unsigned int i = 0;
				for (i = msg.GetMin(); i <= msg.GetMax(); ++i)
				{
					new_data.Reset();
					int tmp_top_index = MabMath::Min((int)i, num_entries + (int)msg.GetMin() - num_rows);
					MabMath::ClampLower(tmp_top_index, (int)msg.GetMin());
					int j = 0;
					int start = (tmp_top_index - msg.GetMin()) * num_columns;
					int end = (tmp_top_index - (int)msg.GetMin() + MabMath::Min(num_entries, num_rows)) * num_columns;
					if (end > (int)msg.GetData().Num())
						end = (int)msg.GetData().Num() / num_columns * num_columns;
					int colPos = 0;
					for (j = start; j < end; ++j, ++colPos)
					{
						//MABLOGDEBUG("ELEMENT[%d]='%s'",j,msg.GetData().at( j ).c_str());
						if (colPos == 2)
							continue;	// skip GameType column for display
						if (msg.GetData().IsValidIndex(j))
						{
							new_data.Add(msg.GetData()[j]);
							if (colPos >= num_columns)
								colPos = 0;
						}
					}
					break;	// now we have new_data
				}
			}
#endif
			// Can only call this safely if we've initialised our UI objects.
			if (container && GetContainerInitialised(container) == CONT_INITIALISED)
			{
				FilloutDataLines(container, new_data);
			}
		}
	}
}

// Returns whether this object 'has been initialised'
UWWUILeaderboardPopulator::CONTAINER_STATE UWWUILeaderboardPopulator::GetContainerInitialised( UWWUIScrollBox* object )
{
	if (object->GetListLength() == 0 ||
		!object->GetBoolProperty(CONTAINER_INITIALISED) ||
		(object->GetBoolProperty(CONTAINER_INITIALISED) && !object->GetBoolProperty(CONTAINER_INITIALISED)))
	{
		return CONT_NOT_INITIALISED;
	}

	return CONT_INITIALISED;
}

// Helper function to (un)dim the arrows on the leaderboard screen.
void UWWUILeaderboardPopulator::DimArrows( UWWUIScrollBox* top_level_obj, bool up_arrow_dim, bool down_arrow_dim )
{
	if (ScreenRef)
	{
		// Because we know the UI standard for this thing, we get the parent, and then get the arrows.
		UPanelWidget* lb_container = Cast<UPanelWidget>(top_level_obj->GetParent());

		if (!lb_container)
		{
			MABBREAKMSG("NO CONTAINER FOR POPULATED OBJECT");
			return;
		}

		// Get the leaderboard container, and then the arrow label.
		const UPanelWidget* arrow_label = Cast<UPanelWidget>(ScreenRef->FindChildOfTemplateWidget(Cast<UWidget>(lb_container), FString(LEADBEROARD_ARROW_LABEL_NAME)));

		if (!arrow_label)
		{
			MABBREAKMSG("NO ARROW LABEL_FOUND");
			return;
		}

		// Get whether we should dim or not
		float up_alpha = up_arrow_dim ? INACTIVE_ARROW_ALPHA : ACTIVE_ARROW_ALPHA;
		float down_alpha = down_arrow_dim ? INACTIVE_ARROW_ALPHA : ACTIVE_ARROW_ALPHA;

		// Get and subsequently set the colours.
		Cast<UImage>(arrow_label->GetChildAt(UP_ARROW_IDX))->ColorAndOpacity.A = up_alpha;
		Cast<UImage>(arrow_label->GetChildAt(DOWN_ARROW_IDX))->ColorAndOpacity.A = down_alpha;
	}
}

// \brief Fills out a UI object with all the info it's supposed to be populated with
// @param object	the UI object to populate
void UWWUILeaderboardPopulator::UWWUILeaderboardPopulatorCallbackObject::Callback(UUserWidget* widget)
{
	// If there's a name override set, use it here.
	/*if ( name_override != "" )
	{
		FString name;
		name.Printf(0, "%s_%d", name_override, item_idx);
		object->SetName( name ); 
	}*/

	// Add the button to the parent container if not done already
	UScrollBox* scrollBox = container->GetScrollBox();
	UPanelWidget* scrollBoxPanel = Cast<UPanelWidget>(scrollBox);
	scrollBoxPanel->AddChild(widget);

	// Increment the item being created.
	++item_idx;
}

/// Sets loading text status
void UWWUILeaderboardPopulator::SetLoadingStatus(UWWUIScrollBox* object)
{
	MABASSERT(object);
	MABLOGMSG(LOGCHANNEL_NETWORK,LOGTYPE_INFO,"Leaderboard: SET TIMER");
	//SIFApplication::GetApplication()->GetWindowSystem()->SetTimer("leaderboard_load_timeout",object,10.0);

	// Classic implementation: set loading text directly in each entry
	//PopulateWithText( object, SIFLanguage::GetCurrentLocale()->GetLoadingTranslation() );

	// New implementation: set status in central status text widget
	//lbrd_queue->GetCurrentLeaderboard()->SetStatusMessage( object->GetParent(), SIFLanguage::GetCurrentLocale()->GetLoadingTranslation() + "..." ); //#MB - loading status

	// Whenever loading, display no other entries and hide all highlights
	PopulateWithText(object, ""); 
}

const TArray< FString >& UWWUILeaderboardPopulator::GetLoadingLine( unsigned int total_num_columns ) const
{
	MABASSERTMSG( total_num_columns > TEXT_INDEX + 1, "Cannot pass this as a total_num_columns, we need to set the second column to the loading text!" );
	if ( total_num_columns < TEXT_INDEX + 1 ) return loading_line;
	//const FString tmp_loading_trans = FInternationalization::Get().GetCurrentLocale()->GetLoadingTranslation();
	//loading_line[TEXT_INDEX] = tmp_loading_trans;
	//loading_line.resize( total_num_columns, "" );
	return loading_line;
}


#if defined BUILD_DEBUG
FString UWWUILeaderboardPopulatorMessage::ToString() const
{
	FString to_go_out;
	to_go_out += FString( 0, "Filter enum value: %d\n", static_cast<int>( filter ) );
	to_go_out += FString( 0, "Leaderboard id: %ull\n", leaderboard_id );
	to_go_out += FString( "Data:\n" );
	for( TArray< FString >::const_iterator iter = data.begin(); iter != data.end(); ++iter )
	{
		to_go_out += FString( 0, "\t%s\n", iter->c_str() );
	}
	return to_go_out;
}
#endif

UWWUILeaderboardPopulator::OnlineLeaderboardNodeCreationCallback::OnlineLeaderboardNodeCreationCallback(UWidget* containerToPopulate, TArray<FWWUIScreenTemplateDataOption>& inDataOptions, TArray<TSharedPtr<FRugbyLeaderboardRow>> InLeaderboardList) :
	container(),
	dataOptions(inDataOptions)
{
	container = Cast<UScrollBox>(containerToPopulate);
	CurrentWidget = 0;

	LeaderboardList = InLeaderboardList;

	if (!container)
	{
		FString errorString = containerToPopulate != nullptr ? *containerToPopulate->GetPathName() : FString("NULL");
		UE_LOG(LogTemp, Error, TEXT("Cast to scroll box failed while attempting DataFileCreationNodeCallback on node %s"), *errorString);
	}
}

void UWWUILeaderboardPopulator::OnlineLeaderboardNodeCreationCallback::Callback(UUserWidget* widget)
{
	UWidgetTree* widgetTree = widget->WidgetTree;

	unsigned int currentChild = container->GetChildrenCount();

	TSharedPtr<FRugbyLeaderboardRow> leaderboardRow = LeaderboardList[CurrentWidget];

	// FString Rank;
	// FString PlayerName;
	// const TSharedPtr<const FUniqueNetId> PlayerId;
	// int32 score = 0;
	// int32 id = 0;
	// int32 wins = 0;
	// int32 draws = 0;
	// int32 losses = 0;
	// int32 reliability = 0;

	FString tempText;
	int tempVal = 0;
	UWidget* tmp = widgetTree->FindWidget(OnlineLeaderboardListHeaderNames[OnlineLeaderboardIndex::OLI_Rank]);
	UTextBlock* tBlock = Cast<UTextBlock>(tmp);
	if (tBlock)
	{
		tempText = leaderboardRow->Rank;
		tBlock->SetText(FText::FromString(tempText));
	}

	tmp = widgetTree->FindWidget(OnlineLeaderboardListHeaderNames[OnlineLeaderboardIndex::OLI_Gamertag]);
	tBlock = Cast<UTextBlock>(tmp);
	if (tBlock)
	{
		tempText = leaderboardRow->PlayerName;

		//LEADERBOARD DEBUGGING CODE - REMOVE ME FOR GAME RELEASE
		//FString tempString;
		//leaderboardRow->Debug_WriteReliabilityToString(tempString);
		//tempText.Append(TEXT(" "));
		//tempText.Append(tempString);
		///////////

		tBlock->SetText(FText::FromString(tempText));
	}

	tmp = widgetTree->FindWidget(OnlineLeaderboardListHeaderNames[OnlineLeaderboardIndex::OLI_Played]);
	tBlock = Cast<UTextBlock>(tmp);
	if (tBlock)
	{
		tempVal = leaderboardRow->played() > MAX_COLUMN_VALUE ? MAX_COLUMN_VALUE : leaderboardRow->played();
		tempText = FString::FromInt(tempVal >= 0 ? tempVal : 0);
		tBlock->SetText(FText::FromString(tempText));
	}

	tmp = widgetTree->FindWidget(OnlineLeaderboardListHeaderNames[OnlineLeaderboardIndex::OLI_Won]);
	tBlock = Cast<UTextBlock>(tmp);
	if (tBlock)
	{
		tempVal = leaderboardRow->wins > MAX_COLUMN_VALUE ? MAX_COLUMN_VALUE : leaderboardRow->wins;
		tempText = FString::FromInt(tempVal >= 0 ? tempVal : 0);
		tBlock->SetText(FText::FromString(tempText));
	}

	tmp = widgetTree->FindWidget(OnlineLeaderboardListHeaderNames[OnlineLeaderboardIndex::OLI_Lost]);
	tBlock = Cast<UTextBlock>(tmp);
	if (tBlock)
	{
		tempVal = leaderboardRow->losses > MAX_COLUMN_VALUE ? MAX_COLUMN_VALUE : leaderboardRow->losses;
		tempText = FString::FromInt(tempVal >= 0 ? tempVal : 0);
		tBlock->SetText(FText::FromString(tempText));
	}

	tmp = widgetTree->FindWidget(OnlineLeaderboardListHeaderNames[OnlineLeaderboardIndex::OLI_Drawn]);
	tBlock = Cast<UTextBlock>(tmp);
	if (tBlock)
	{
		tempVal = leaderboardRow->draws > MAX_COLUMN_VALUE ? MAX_COLUMN_VALUE : leaderboardRow->draws;
		tempText = FString::FromInt(tempVal >= 0 ? tempVal : 0);
		tBlock->SetText(FText::FromString(tempText));
	}

	//tmp = widgetTree->FindWidget(OnlineLeaderboardListHeaderNames[OnlineLeaderboardIndex::OLI_For]);
	//tBlock = Cast<UTextBlock>(tmp);
	//if (tBlock)
	//{
	//	tempText = FString::FromInt(leaderboardRow->pointsFor);
	//	tBlock->SetText(FText::FromString(tempText));
	//}

	//tmp = widgetTree->FindWidget(OnlineLeaderboardListHeaderNames[OnlineLeaderboardIndex::OLI_Against]);
	//tBlock = Cast<UTextBlock>(tmp);
	//if (tBlock)
	//{
	//	tempText = FString::FromInt(leaderboardRow->pointsAgainst);
	//	tBlock->SetText(FText::FromString(tempText));
	//}

	//tmp = widgetTree->FindWidget(OnlineLeaderboardListHeaderNames[OnlineLeaderboardIndex::OLI_Percentage]);
	//tBlock = Cast<UTextBlock>(tmp);
	//if (tBlock)
	//{
	//	if (leaderboardRow->pointsAgainst != 0)
	//	{
	//		float pointPercentage = ((float)(leaderboardRow->pointsFor) / (float)(leaderboardRow->pointsAgainst)) * 100.0f;

	//		tempText = FString::Printf(TEXT("%.2f %%"), pointPercentage);
	//		tBlock->SetText(FText::FromString(tempText));
	//	}
	//	else
	//	{
	//		tempText = FString::Printf(TEXT("--- %%"));
	//		tBlock->SetText(FText::FromString(tempText));
	//	}
	//}

	tmp = widgetTree->FindWidget(OnlineLeaderboardListHeaderNames[OnlineLeaderboardIndex::OLI_Reliability]);
	tBlock = Cast<UTextBlock>(tmp);
	if (tBlock)
	{
		tempText = FString::FromInt(leaderboardRow->GetReliabilityAsPercentage() > MAX_RELIABLE_VALUE ? MAX_RELIABLE_VALUE : leaderboardRow->GetReliabilityAsPercentage());
		// Add the percent symbol.
		tempText.Append("%");
		//FString tempString;
		//leaderboardRow->Debug_WriteReliabilityToString(tempString);
		//tempText.Append(TEXT(" "));
		//tempText.Append(tempString);
		tBlock->SetText(FText::FromString(tempText));
	}

	tmp = widgetTree->FindWidget(OnlineLeaderboardListHeaderNames[OnlineLeaderboardIndex::OLI_Points]);
	tBlock = Cast<UTextBlock>(tmp);
	if (tBlock)
	{
		tempText = FText::AsNumber(leaderboardRow->score > MAX_SCORE_VALUE ? MAX_SCORE_VALUE : leaderboardRow->score).ToString();
		tBlock->SetText(FText::FromString(tempText));
	}

	container->AddChild(widget);

	++CurrentWidget;
}
