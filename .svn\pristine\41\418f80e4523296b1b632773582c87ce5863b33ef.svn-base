/*/--------------------------------------------------------------
|        Copyright (C) 1997-2008 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef _SIF_UI_CONSTANTS
#define _SIF_UI_CONSTANTS

#include "Mab/Types/MabColour.h"

/**
 * This header contains various constants used throughout the UI 
 * in SIF.
 *
 * <AUTHOR>
 */

/// \name Window Names 
/// @{
	/// Root menu window name
//#rc3_legacy_ui // DH - once the UI tool is in, we need to replace these values with the tool generated header.
namespace RugbyUIWindowNames
{
	const char SIFUI_ROOT_MENU_WINDOW_NAME[] = "RootMenuWindow";

	/// Root input consumption (window that does nothing safely) window name
	const char SIFUI_ROOT_INPUT_EAT_WINDOW_NAME[] = "RootInputEatWindow";

	/// Root empty window (for use when we just want to send focus away from the currently selected window).
	const char SIFUI_ROOT_EMPTY_WINDOW_NAME[] = "RootEmptyWindow";

	/// Root popup window name
	const char SIFUI_ROOT_POPUP_WINDOW_NAME[] = "RootPopupWindow";

	// Pointer cursor currently only supported on PC
	/// Root cursor window name
	const char SIFUI_ROOT_CURSOR_WINDOW_NAME[] = "RootCursorWindow";

	/// Mouse Cursor window name
	const char SIF_CURSOR_WINDOW_NAME[] = "CursorWindow";

	/// Root Tooltip window name.
	const char SIF_TT_WINDOW_NAME[] = "RootTooltipWindow";

	/// Root Background window name.
	const char SIFUI_BACKGROUND_MENU_WINDOW_NAME[] = "RootBackgroundWindow";

	/// Root overlay window name.
	const char SIFUI_ROOT_OVERLAY_WINDOW_NAME[] = "RootOverlayWindow";

	/// ROot Tab window name.
	const char SIFUI_TAB_MENU_WINDOW_NAME[] = "RootTabWindow";

	/// Root Home Backing window name.
	const char SIFUI_HOME_BACKING_WINDOW_NAME[] = "RootHomeBackingWindow";

	/// Game window name
	//const char SIFUI_GAME_WINDOW_NAME[] = "GameWindow";
	const char SIFUI_GAME_WINDOW_NAME[] = "InGameHUD";

	/// In-game menu
	const char SIFUI_IN_GAME_MENU_WINDOW_NAME[] = "InGameMenu";

	/// Splash title window name (aka Press Start Screen)
	const char SIFUI_SPLASH_TITLE_WINDOW_NAME[] = "TitleScreen";

	const char SIFUI_TRAINING_WINDOW_NAME[] = "TrainingField";

	const char SIFUI_MULTIPLAYER_TRAINING_WINDOW_NAME[] = "MultiplayerTrainingField";

	const char SIFUI_TEAM_MANAGEMENT_WINDOW_NAME[] = "TeamLineup";

	const char SIFUI_RUNON_WINDOW_NAME[] = "RunOnWindow";

	/// Match rule settings + default/favourite team window.
	const char SIFUI_MATCH_RULINGS_SETTINGS_WINDOW_NAME[] = "MatchRulingsSettings";
	const char SIFUI_CAMERA_AND_VISUALS_SETTINGS_WINDOW_NAME[] = "CameraAndVisualsSettings";
	const char SIFUI_GRAPHICS_SETTINGS_WINDOW_NAME[] = "GraphicsSettings";
	const char SIFUI_CONTROLS_WINDOW_NAME[] = "Controls";
	const char SIFUI_SOUND_SETTINGS_WINDOW_NAME[] = "SoundSettings";
	const char SIFUI_LANGUAGE_SETTINGS_WINDOW_NAME[] = "LanguageSettings";

	/// Profile loading menu node
	const char SIFUI_PROFILE_LOAD_WINDOW_NAME[] = "ProfileLoad";

#ifdef CHARACTER_CREATOR_BUILD
	const char SIFUI_MAIN_MENU_WINDOW_NAME[] = "CharacterCreator";//
#else
	/// Main menu window name
	const char SIFUI_MAIN_MENU_WINDOW_NAME[] = "MainMenu";//"CharacterCreator";//
	const char SIFUI_CAREER_SETUP_WINDOW_NAME[] = "CareerSetup";//"CharacterCreator";//
	const char SIFUI_CUSTOMISE_EDIT_PLAYER_WINDOW_NAME[] = "CustomiseEditPlayer";//"CharacterCreator";//
#endif
	/// Loading window name
	const char SIFUI_LOADING_WINDOW_NAME[] = "LoadingScreen";

	const char SIFUI_LOADING_MAIN_WINDOW_NAME[] = "Loading";

	/// End of game menu name
	const char SIFUI_END_GAME_MENU_NAME[] = "EndGameMenu";

	/// End of Game Scores Screen name
	const char SIFUI_END_GAME_SCORES_NAME[] = "EndGameScores";

	/// Copyright window name
	const char SIFUI_COPYRIGHT_WINDOW_NAME[] = "Copyright";

	/// Trublu logo screen
	const char SIFUI_TRUBLU_LOGO_WINDOW_NAME[] = "TruBluLogoSplash";

	/// In-Game Standard Background menu name
	const char SIFUI_IN_GAME_STANDARD_BACKGROUND_NAME[] = "InGameMenuStdBG";

	/// Initial load / XBLA Logo window name
	const char SIFUI_INIT_LOAD_LOGO_WINDOW_NAME[] = "InitialLoadLogo";

	/// The "join an invited game" window where we do all the associated logic.
	const char SIFUI_JOIN_INVITED_GAME_WINDOW_NAME[] = "JoinInviteWindow";

	/// This is a blank/black window that takes focus for a small amount of time if the game requires some loading.
	const char RUUI_LOAD_PENDING_WINDOW_NAME[] = "LoadPendingWindow";

	const char RUUI_SELECT_TEAMS_WINDOW_NAME[] = "SelectTeams";

	const char RUUI_ASSIGN_CONTROLLERS_WINDOW_NAME[] = "AssignControllers";
	const char RUII_INGAME_ASSIGN_CONTROLLERS_WINDOW_NAME[] = "InGameAssignControllers";

	const char RUUI_CUSTOMISE_DATA_RESET_WINDOW_NAME[] = "CustomiseDataReset";

	const char RUUI_CUSTOMISE_TEAM_SELECT_TEAM_WINDOW_NAME[] = "CustomiseSelectTeam";

	const char RUUI_CUSTOMISE_PLAYER_DETAILS_WINDOW_NAME[] = "CustomisePlayerDetails";

	const char RUUI_CUSTOMISE_SELECT_PLAYER_WINDOW_NAME[] = "CustomiseSelectPlayer";

	const char RUUI_CUSTOMISE_EDIT_PLAYER_WINDOW_NAME[] = "CustomiseEditPlayer";

	const char RUUI_CUSTOMISE_PLAYER_STANDARD_IDENTIFIER[] = "CustomisePlayer";		// All customise player screens should start/contain this.

	const char RUUI_CUSTOMISE_KIT_STANDARD_IDENTIFIER[] = "CustomiseKit";		// All customise player screens should start/contain this.

	const char SIFUI_MULTIPLAYER_LOBBY_WINDOW_NAME[] = "MultiplayerLobby";

	const char SIFUI_ONLINE_SEARCH_RESULTS_WINDOW_NAME[] = "OnlineSearchResults";	// Renamed from "MultiplayerSearchResults" due to screen name length restriction

	const char RUUI_CUSTOMISE_PLAYER_BODY_WINDOW_NAME[] = "CustomisePlayerBody";

	const char RUUI_CUSTOMISE_PLAYER_HEAD_WINDOW_NAME[] = "CustomisePlayerHead";

	const char RUUI_CUSTOMISE_PLAYER_SHOE_WINDOW_NAME[] = "CustomisePlayerShoe";

	const char RUUI_CUSTOMISE_PLAYER_HEADSKULLNECK_WINDOW_NAME[] = "CustomisePlayerHeadSkullNeck";

	const char RUUI_CUSTOMISE_PLAYER_ATTRIBUTES_WINDOW_NAME[] = "CustomisePlayerAttributes";

	const char RUUI_CUSTOMISE_PLAYER_FACEHAIR_WINDOW_NAME[] = "CustomisePlayerFaceHair";

	const char RUUI_CUSTOMISE_PLAYER_ACCESSORIES_WINDOW_NAME[] = "CustomisePlayerAccessories";

	const char RUUI_CUSTOMISE_KIT_KIT_WINDOW_NAME[] =	"CustomiseKitKit";
	const char RUUI_CUSTOMISE_KIT_BOOTS_WINDOW_NAME[] = "CustomiseKitBoots";

	const char RUUI_COMPETITION_SELECT_WINDOW_NAME[] = "CompetitionSelect";

	const char RUUI_COMPETITION_COMPETITION_INITIAL_SAVE_WINDOW_NAME[] = "CompetitionInitialSave";
	const char RUUI_COMPETITION_CAREER_INITIAL_SAVE_WINDOW_NAME[] = "CareerInitialSave";

	const char RUUI_COMPETITION_HUB_WINDOW_NAME[] = "CareerHUB";

	const char RUUI_COMPETITION_TEAM_MANAGEMENT_WINDOW_NAME[] = "CompetitionTeamManagement";

	const char RUUI_COMPETITION_TEAM_SELECT_WINDOW_NAME[] = "CompetitionSelectTeams";

	const char RUUI_INGAME_TROPHY_CEREMONY_WINDOW_NAME[] = "MatchCompFinals";

	const char RUUI_ONLINE_TRAINING_LOBBY_WINDOW_NAME[] = "OnlineTrainingLobby";

	const char RUUI_ONLINE_SELECT_TEAMS_WINDOW_NAME[] = "OnlineSelectTeams";

	const char RUUI_IN_GAME_TEAM_MANAGEMENT_WINDOW_NAME[] = "TeamManagement";

	const char RUUI_IN_GAME_INJURY_WINDOW_NAME[] = "InGameInjury";

	const char RUUI_CAREER_SETUP_WINDOW_NAME[] = "CareerSetup";

	const char RUUI_CAREER_SELECT_TEAM_WINDOW_NAME[] = "CareerSelectTeam";

#ifdef ENABLE_PRO_MODE
	const char RUUI_PRO_MODE_SETUP_WINDOW_NAME[] = "ProModeSetup";

	const char RUUI_PRO_MODE_SELECT_TEAM_WINDOW_NAME[] = "ProModeSelectTeam";
#endif

#ifdef APPBUILD_RUGBY_CHALLENGE_3
	const char RUUI_LIST_PLAYERS_WINDOW_NAME[] = "ListPlayers";
	const char RUUI_LIST_TEAMS_WINDOW_NAME[] = "ListTeams";
	const char RUUI_TATTOO_WINDOW_NAME[] = "CustomisePlayerTattoo";
	const char RUUI_SEARCH_PLAYERS_WINDOW_NAME[] = "SearchUploads";
#endif

#ifdef ENABLE_CONTENT_TEST
	/// Content Test menu window name
	const char SIFUI_CT_MENU_NAME[] = "ContentTestMenu";

	/// Content Test In-Test window name
	const char SIFUI_CT_WINDOW_NAME[] = "ContentTestWindow";
#endif

#ifdef ENABLE_ASSET_PREVIEW
	// Asset Preview menu name
	const char SIFUI_AP_WINDOW_NAME[] = "AssetPreviewWindow";
#endif

#ifdef ENABLE_FONT_TEST
	// Font Test menu name
	const char SIFUI_FT_WINDOW_NAME[] = "FontTestMenu";
#endif


	const char RUUI_TRAINING_FIELD_WINDOW_NAME[] = "TrainingField";
	const char RUUI_TRAINING_RUNAROUND_WINDOW_NAME[] = "TrainingRunAround";
	const char RUUI_TRAINING_SELECTED_WINDOW_NAME[] = "TrainingSelected";

	const char SIFUI_COMPETITION_STANDINGS_WINDOW_NAME[] = "CompetitionStandings";
	const char SIFUI_COMPETITION_SIMULATE_MATCH_WINDOW_NAME[] = "CompetitionSimulateMatch";
	const char SIFUI_PRO_CAREER_POST_MATCH_WINDOW_NAME[] = "ProCareerPostMatch";

	const char RUUI_RUGBY_STORE_MOVIE_WINDOW_NAME[] = "RugbyStoreMovie";
	const char RUUI_TRAINING_RULES_WINDOW_NAME[] = "TrainingRules";





	const char RUUI_RUGBY_STORE_WINDOW_NAME[] = "RugbyStore";
	const char RUUI_START_ASYNC_LOADING_WINDOW_NAME[] = "StartAsyncLoading";
	const char RUUI_LAUNCH_PENDING_LEVEL_WINDOW_NAME[] = "LaunchPendingLevel";
	const char RUUI_CUSTOMISE_TEAM_DETAILS_WINDOW_NAME[] = "TeamDetails"; //"CustomiseTeamDetails"; //#RC4-3603
	const char RUUI_CUSTOMISE_SELECT_KIT_WINDOW_NAME[] = "CustomiseSelectKit"; 
}

/// @}

/// \name UI Object Parameters
/// @{

	// Play Animation object param -Kade WW
	const char WW_OBJECT_PLAYANIM_PARAM[] = "playanimation";

	/// On Create object parameter.
	const char SIFUI_OBJECT_ONCREATE_PARAM[] = "oncreate";

	/// Action object parameter
	const char SIFUI_OBJECT_ONACTION_PARAM[] = "onaction";

	/// Focus object parameter
	const char SIFUI_OBJECT_ONFOCUS_PARAM[] = "onfocus";

	/// Focus lost object parameter
	const char SIFUI_OBJECT_ONFOCUSLOST_PARAM[] = "onfocuslost";

	/// Selection changed object parameter
	const char SIFUI_OBJECT_ONSELECTIONCHANGED_PARAM[] = "onselectionchanged";

	/// Items updated object parameter
	const char SIFUI_OBJECT_ONITEMSUPDATED_PARAM[] = "onlistitemchanged";

	/// Value changed object parameter
	const char SIFUI_OBJECT_ONVALUECHANGED_PARAM[] = "onvaluechanged";

	/// Click object parameter
	const char SIFUI_OBJECT_ONCLICK_PARAM[] = "onclick";

	/// Mouse click
	const char SIFUI_OBJECT_ONMOUSECLICK_PARAM[] = "onmouseclick";

	/// Mouse over/out
	const char SIFUI_OBJECT_ONMOUSEOVER_PARAM[] = "onmouseover";
	const char SIFUI_OBJECT_ONMOUSEOUT_PARAM[] = "onmouseout";
	
	/// Mouse widget click/over/out
	const char SIFUI_WIDGET_ONMOUSEOVER_PARAM[] = "onmouseover";
	const char SIFUI_WIDGET_ONMOUSEOUT_PARAM[] = "onmouseout";
	const char SIFUI_WIDGET_ONMOUSECLICK_PARAM[] = "onmouseclick";

	/// Arrow Widgets
	const char SIFUI_WIDGET_ONARROWENABLE_PARAM[] = "onarrowenable";
	const char SIFUI_WIDGET_ONARROWDISABLE_PARAM[] = "onarrowdisable";

	///	Timeout object parameter
	const char SIFUI_OBJECT_ONTIMEOUT_PARAM[] = "ontimeout";

	/// Window-enter object parameter
	const char SIFUI_OBJECT_WINDOW_ENTER_PARAM[] = "onwindowenter";

	/// Window-exit object parameter
	const char SIFUI_OBJECT_WINDOW_EXIT_PARAM[] = "onwindowexit";

	/// Window transition-on object parameter
	const char SIFUI_OBJECT_WINDOW_TRANSITION_ONSCREEN_PARAM[] = "ontransitiononscreen";

	/// Window transition-off object parameter
	const char SIFUI_OBJECT_WINDOW_TRANSITION_OFFSCREEN_PARAM[] = "ontransitionoffscreen";
	
	/// Video end parameter.
	const char SIFUI_VIDEO_END_PARAM[] = "on_video_end";

	/// Template parameter object parameter
	const char SIFUI_OBJECT_TEMPLATE_PARAM[] = "template_resource";

	/// Populated by object parameter
	const char SIFUI_OBJECT_POPULATED_PARAM[] = "populated_by";

	/// Animation related.
	const char SIFUI_ONANIMATIONEND_PARAM[] = "onanimationend";
	const char SIFUI_ONANIMATIONLOOP_PARAM[] = "onanimationloop";
	const char SIFUI_ONANIMATIONEVENT_PARAM[] = "onanimationevent";
	const char SIFUI_ANIMATION_EVENT_TYPE_VAL[] = "event_type";
	const char SIFUI_ANIMATION_EVENT_NAME_VAL[] = "event_name";
	const char SIFUI_ANIMATION_EVENT_ANIM_NAME_VAL[] = "animation_name";
/// @}

	/// achievement populator template ID
	const char SIF_WII_UI_ACHIEVEMENTS_ENTRY_ID[] = "wii_achievements_entry";


/// \name Lua Function Parameters
/// @{
	/// Action event parameter
	const char SIFUI_LUA_ACTION_EVENT_PARAM[] = "action_event";

	/// Controller id parameter
	const char SIFUI_LUA_CONTROLLER_ID_PARAM[] = "controller_id";

	/// Custom action parameter 
	const char SIFUI_LUA_CUSTOM_ACTION_ID_PARAM[] = "custom_action"; 

	/// Controller id parameter for system events--to prevent overwriting UI-level Lua globals with system event globals.
	const char SIFUI_LUA_CONTROLLER_ID_PARAM_SYS_EVENT[] = "SE_controller_id";

	/// This object parameter
	const char SIFUI_LUA_THIS_OBJECT_PARAM[] = "this_object";

	/// This widget parameter
	const char SIFUI_LUA_THIS_WIDGET_PARAM[] = "this_widget";

	/// Processed parameter
	const char SIFUI_LUA_PROCESSED_PARAM[] = "processed";

	/// Focus object parameter
	const char SIFUI_LUA_FOCUS_OBJECT_PARAM[] = "focus_object";

	/// Focus object context parameter
	const char SIFUI_LUA_FOCUS_CONTEXT_PARAM[] = "focus_context";

	/// UI message object during an event handler
	const char SIFUI_LUA_MESSAGE_OBJECT_PARAM[] = "ui_message";

	/// Timer event paramter
	const char SIFUI_LUA_TIMER_EVENT_PARAM[] = "timer_event";

	/// System event parameter
	const char SIFUI_LUA_SYSTEM_EVENT_PARAM[] = "system_event";

	/// Extra system event details
	const char SIFUI_LUA_EXTRA_DETAILS_PARAM[] = "extra_details";

	/// Profile changed parameter
	const char SIFUI_LUA_PROFILE_CHANGED_PARAM[] = "profile_change_type";

	const char SIFUI_LUA_PROFILE_NAME[] = "profile_name";
	const char SIFUI_LUA_PROFILE_CONTROLLER_INDEX[] = "profile_controller_idx";

	/// Whether the profile that triggered the event was the master profile
	const char SIFUI_LUA_PROFILE_IS_MASTER[] = "profile_is_master";

	/// Whether the profile that triggered the event has just been copied from the default profile
	const char SIFUI_LUA_PROFILE_IS_DEFAULT[] = "profile_is_default";

	/// Whether a given operation was successful
	const char SIFUI_LUA_OPERATION_SUCCESSFUL[] = "success";

	/// Default return parameter for menu lua
	const char SIFUI_LUA_DEFAULT_RETURN_PARAM_VAL[] = "none";

	/// Default number of reserved entries for UI event's NVL
	const int SIFUI_EVENT_NUM_RESERVED_ENTRIES = 3;

	/// Selected node parameter
	const char SIFUI_LUA_SELECTED_NODE_PARAM[] = "selected_node";

	/// Previous selected node parameter
	const char SIFUI_LUA_PREVIOUS_NODE_PARAM[] = "previous_node";
/// @}

/// \name Miscellaneous constants
/// @{
	/// System Popup Identifier for populators, etc.
	const char SIFUI_SYSTEM_POPUP_ID[] = "system_popup";

	/// Leaderboard entry identifier for populators etc...
	const char SIFUI_LEADERBOARD_ENTRY_ID[] = "leaderboard_entry";

	// Populator for PC controllers
	const char SIFUI_CONTROLLERS_ENTRY_ID[] = "controllers_entry";

	/// System Popup name for instancers
	const char SIFUI_SYSTEM_POPUP_TYPE_NAME[] = "System";

	/// Default popup resolution code
	const char SIFUI_DEFAULT_POPUP_RESOLUTION_CODE[] = "default";

	/// Popup Button Identifier
	const char SIFUI_POPUP_BUTTON_NAME[] = "Button";

	/// Profile value parameter name
	const char SIFUI_SETTINGS_PROFILE_VALUE_PARAM[] = "profile_value";

	/// These are the supported values for the profile player label options.
	const char SIFUI_PROFILE_PLAYER_LABEL_OFF[]		= "OFF";
	const char SIFUI_PROFILE_PLAYER_LABEL_NAME[]	= "NAME";
	const char SIFUI_PROFILE_PLAYER_LABEL_POSITION[]= "POSITION";

	/// Colour for highlighted ui elements in SIF as a string.
	const char SIFUI_HIGHLIGHT_COL_STRING[] = "1.0,0.25,0.25";

	/// System keyboard.
	const char SIFUI_SYSTEM_KEYBOARD_FINISHED[] = "on_system_keyboard_finished";
	const char SIFUI_SYSTEM_KEYBOARD_TEXT[] = "text";

#ifdef ENABLE_CONTENT_TEST
	/// Leaderboard entry identifier for populators etc...
	const char SIFUI_CT_MENU_ID[] = "ct_menu_entry";

	/// Popup Button Identifier
	const char SIFUI_CT_BUTTON_NAME[] = "CTButton";
#endif

	/// Tooltip prefix for automatic translation
	const char SIFUI_TT_TRANSLATION_PREFIX[] = "ID_tip_";

	/// Image name for 'none' with setting from lua
	const char SIFUI_NO_IMAGE_NAME[] = "none";

	/// Name for Window System for messages
	const char SIFUI_WINDOWSYSTEM_CONTEXT[] = "WINDOWSYSTEM";

	/// Loading screen font resource name
	const char SIFUI_LOADING_FONT_RESOURCE_NAME[] = "ru_18_b_ds_console";

	/// Default value for Safe Zone.
#if PLATFORM_WINDOWS
	const float SIFUI_DEFAULT_SAFE_ZONE = 0.85f;
/*#elif PLATFORM_XBOX360                  #MB - xbox360 platform no longer defined
	const float SIFUI_DEFAULT_SAFE_ZONE = 0.85f;*/
#else
	const float SIFUI_DEFAULT_SAFE_ZONE = 0.85f;	
#endif

	/// Multiplayer populator names.
	const char SIFUI_MULTIPLAYER_SEARCH_POPULATOR_NAME[] = "multiplayer_search";
	const char SIFUI_LOBBY_MEMBER_POPULATOR_NAME[] = "lobby_populator";
	const char SIFUI_MUTING_MEMBER_POPULATOR_NAME[] = "muting_ui_populator";

	// Competition finals populator names.
	const char RUUI_COMPETITION_FINALS_ROUND[] = "competition_finals_round";

	/// RU specific populator
	// Populator for the training menu
	const char RUUI_TUTORIAL_MENU_POPULATOR_NAME[] = "tutorial_menu_populator";
	// Select team screen populators
	const char RUUI_TEAM_LIST_POPULATOR_NAME[] = "team_list_populator";
	const char RUUI_SELECT_TEAM_INFO_POPULATOR_NAME[] = "select_team_info_populator";
	// Tutorial rolodex populator
	const char RUUI_TUTORIAL_ROLODEX_POPULATOR_NAME[] = "tutorial_rolodex_populator";
	// Player detail populator
	const char RUUI_PLAYER_DETAIL_POPULATOR_NAME[] = "player_detail_populator";
	// Tutorial category populator
	const char RUUI_TUTORIAL_CATEGORY_POPULATOR_NAME[] = "tutorial_category_populator";
	// Match team profile populator.
	const char RUUI_MATCH_TEAM_PROFILE_POPULATOR_NAME[] = "match_team_profile_populator";
	// HUB team profile populator.
	const char RUUI_HUB_TEAM_PROFILE_POPULATOR_NAME[] = "hub_team_profile_populator";
	// Settings team name populator.
	const char RUUI_SETTINGS_TEAM_NAME_POPULATOR_NAME[] = "settings_team_name_populator";
	// Custom competition template populator.
	const char RUUI_CUSTOM_COMP_TEMPLATE_POPULATOR_NAME[] = "custom_comp_template_populator";
	// Custom competition logo populator.
	const char RUUI_CUSTOM_COMP_LOGO_POPULATOR_NAME[] = "custom_comp_logo_populator";
	// Custom competition trophy populator.
	const char RUUI_CUSTOM_COMP_TROPHY_POPULATOR_NAME[] = "custom_comp_trophy_populator";
	// Team select list populator.
	const char RUUI_TEAM_SELECT_LIST_POPULATOR_NAME[] = "team_select_list_populator";
	// Competition team list populator.
	const char RUUI_COMP_TEAM_LIST_POPULATOR_NAME[] = "comp_team_list_populator";
	// Rugby store populator
	const char RUUI_RUGBY_STORE_POPULATOR_NAME[] = "rugby_store_populator";
	// Hair style populator (customise player)
	const char RUUI_PLAYERCREATOR_HAIRSTYLE_POPULATOR_NAME[] = "playercreator_hair_style_populator";
	// Career stats populator
	const char RUUI_CAREER_STATS_POPULATOR_NAME[] = "career_stats_populator";
	// Career inbox email populator.
	const char RUUI_CAREER_INBOX_EMAIL_POPULATOR[] = "career_inbox_email_populator";
	// Career contract populator.
	const char RUUI_CAREER_MY_CONTRACTS_CONTRACT_POPULATOR[] = "career_my_contracts_contract_populator";
	// Career contract content populator.
	const char RUUI_CAREER_MY_CONTRACTS_CONTRACT_CONTENT_POPULATOR[] = "career_my_contracts_contract_content_populator";
	// Career player stats populator
	const char RUUI_CAREER_PLAYER_STATS_POPULATOR_NAME[] = "career_player_stats_populator";
	// Career team stats populator
	const char RUUI_CAREER_TEAM_STATS_POPULATOR_NAME[] = "career_team_stats_populator";
	// Career team stats populator
	const char RUUI_CAREER_TOP10_POPULATOR_NAME[] = "career_top10_populator";
	// Career stats populator (new)
	const char RUUI_CAREER_NEW_STATS_POPULATOR_NAME[] = "new_career_stats_populator";
	// In game player attributes populator
	const char RUUI_INGAME_PLAYER_ATTRIBUTES_POPULATOR_NAME[] = "ingame_player_attributes_populator";
	// In game team squad populator
	const char RUUI_INGAME_SQUAD_POPULATOR_NAME[] = "ingame_squad_populator";
	// In game team profile populator
	const char RUUI_INGAME_TEAM_PROFILE_POPULATOR_NAME[] = "ingame_team_profile_populator";
	// In game injured player populator.
	const char RUUI_INGAME_INJURED_PLAYER_POPULATOR_NAME[] = "ingame_injured_player_populator";

	// Competition select populator
	const char RUUI_COMP_HUB_TEAM_MANAGEMENT_POPULATOR_NAME[] = "comp_hub_team_management_populator";
	const char RUUI_COMPETITION_SELECT_POPULATOR_NAME[] = "competition_select_populator";
	const char RUUI_COMPETITION_TEAM_SELECT_POPULATOR_NAME[] = "competition_team_select_populator";
	const char RUUI_COMPETITION_HUB_POPULATOR_NAME[] = "competition_hub_match_info_populator";
	const char RUUI_COMPETITION_STANDINGS_POPULATOR_NAME[] = "competition_standings_populator";
	const char RUUI_COMPETITION_FIXTURES_POPULATOR_NAME[] = "competition_fixtures_populator";
	const char RUUI_COMPETITION_FINALS_POPULATOR_NAME[] = "competition_finals_populator";
	const char RUUI_COMPETITION_LESSER_MATCH_POPULATOR_NAME[] = "competition_lesser_match_populator";
	const char RUUI_COMPETITION_STATS_POPULATOR_NAME[] = "competition_statistics_populator";
	const char RUUI_COMPETITION_RECORDS_POPULATOR_NAME[] = "competition_records_populator";

	// Custom competition populator.
	const char RUUI_CUSTOM_COMP_POOL_POPULATOR_NAME[] = "custom_comp_pool_populator";
	const char RUUI_CUSTOM_COMP_POOL_TEAM_POPULATOR_NAME[] = "custom_comp_pool_team_populator";
	const char RUUI_COMP_NUM_OF_TEAMS_POPULATOR_NAME[] = "custom_comp_format_num_of_teams_populator";
	const char RUUI_COMP_PLAY_EACH_POPULATOR_NAME[] = "custom_comp_format_play_each_populator";
	const char RUUI_COMP_FINALS_ADVANCE_A_POPULATOR_NAME[] = "custom_comp_finals_advance_a_populator";
	const char RUUI_COMP_FINALS_ADVANCE_B_POPULATOR_NAME[] = "custom_comp_finals_advance_b_populator";
	const char RUUI_COMP_FINALS_VARIANT_POPULATOR_NAME[] = "custom_comp_finals_variant_populator";
	const char RUUI_COMP_DRAW_POINTS_POPULATOR_NAME[] = "custom_comp_draw_points_populator";

	// Attribute populator
	const char RUUI_CAREER_PLAYER_ATTRIBUTES_POPULATOR_NAME[] = "player_attributes_populator";
	const char RUUI_POST_MATCH_PRO_PLAYER_ATTRIBUTES_POPULATOR_NAME[] = "post_match_pro_player_attributes_populator";//player_attributes_populator

	// Customise Player
	const char RUUI_CUSTOMISE_EDIT_COMP_POPULATOR_NAME[] = "customise_edit_competition";
	const char RUUI_CUSTOMISE_EDIT_TEAM_POPULATOR_NAME[] = "customise_edit_team";
	const char RUUI_CUSTOMISE_EDIT_PLAYER_POPULATOR_NAME[] = "customise_edit_player";
	const char RUUI_CUSTOMISE_DELETE_PLAYER_POPULATOR_NAME[] = "customise_delete_player_populator";

	const char RUUI_CUSTOM_COMP_ROUND_ROBIN_ID[] = "round_robin_id";
	const char RUUI_CUSTOM_COMP_NUM_OF_TEAMS_ID[] = "num_of_teams";
	const char RUUI_CUSTOM_COMP_NUM_OF_POOLS_ID[] = "num_of_pools";
	const char RUUI_CUSTOM_COMP_PLAY_EACH_ID[] = "play_each";
	const char RUUI_CUSTOM_COMP_ADVANCE_A[] = "advance_a";
	const char RUUI_CUSTOM_COMP_ADVANCE_B[] = "advance_b";
	const char RUUI_CUSTOM_COMP_NUMERIC_VALUE[] = "numeric_value";

	const char RUUI_CUSTOM_COMP_ROUND_ROBIN_SERIES[] = "series";
	const char RUUI_CUSTOM_COMP_ROUND_ROBIN_POOLS[] = "pools";
	const char RUUI_CUSTOM_COMP_ROUND_ROBIN_LEAGUE[] = "league";
	const char RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE[] = "satellite";
	
	// Career / Player Drafting/recruiting
	const char RUUI_CAREER_DRAFT_SQUAD_POPULATOR_NAME[] = "career_draft_populator";
	const char RUUI_CAREER_RECRUIT_POPULATOR_NAME[] = "career_recruit_populator";
	const char RUUI_CAREER_OFFER_SALARY_POPULATOR_NAME[] = "career_offer_salary_populator";
	const char RUUI_HUB_STANDINGS_POPULATOR_NAME[] = "hub_standings_populator";
	const char RUUI_HUB_BLEDISLOE_STANDINGS_POPULATOR_NAME[] = "hub_bledisloe_standings_populator";
	const char RUUI_HUB_NEXT_MATCH_POPULATOR_NAME[] = "hub_next_match_populator";
	const char RUUI_CAREER_SQUAD_POPULATOR_NAME[] = "career_squad_populator";
	const char RUUI_CAREER_SCHEDULE_POPULATOR_NAME[] = "career_calendar_populator";
	const char RUUI_CAREER_SCHEDULE_MATCH_INFO_POPULATOR_NAME[] = "career_schedule_match_info_populator";
	const char RUUI_COMPETITION_MAIN_MENU_LISTBOX_POPULATOR_NAME[] = "competition_main_menu_listbox_populator";
	const char RUUI_CAREER_MAIN_MENU_LISTBOX_POPULATOR_NAME[] = "career_main_menu_listbox_populator";
	const char RUUI_CAREER_COACH_MAIN_MENU_LISTBOX_POPULATOR_NAME[] = "career_coach_main_menu_listbox_populator"; 

	const char RUUI_HUB_CURRENT_PRO_GOALS_POPULATOR_NAME[] = "hub_current_pro_goals_populator";
	const char RUUI_IN_GAME_CURRENT_PRO_GOALS_POPULATOR_NAME[] = "ingame_current_pro_goals_populator";
	const char RUUI_CURRENT_PRO_REWARDS_POPULATOR_NAME[] = "current_pro_rewards_populator";

	// Populators for the post match career screen
	const char RUUI_CAREER_POST_MATCH_POPULATOR_NAME[] = "career_post_match_populator";
	const char RUUI_CAREER_POST_MATCH_PERF_POPULATOR_NAME[] = "career_post_match_perf_populator";
	const char RUUI_CAREER_POST_MATCH_TEAM_POPULATOR_NAME[] = "career_post_match_team_populator";
	const char RUUI_CAREER_POST_MATCH_DETAILS_POPULATOR_NAME[] = "career_post_match_details_populator";

#ifdef ENABLE_PRO_MODE
	// Pro mode populators
	const char RUUI_CAREER_PRO_MAIN_MENU_LISTBOX_POPULATOR_NAME[] = "career_pro_main_menu_listbox_populator";
	const char RUUI_ALLEGIANCE_LISTBOX_POPULATOR_NAME[] = "allegiance_populator";

	// My Pro screen stuff
	const char RUUI_MY_PRO_INTEREST_POPULATOR_NAME[] = "my_pro_interest_populator";
	const char RUUI_MY_PRO_PERFORMANCE_POPULATOR_NAME[] = "my_pro_performance_populator";
#endif

#ifdef APPBUILD_RUGBY_CHALLENGE_3
	const char RUUI_LIST_PLAYERS_LISTBOX_POPULATOR_NAME[] = "list_players_populator";
	const char RUUI_LIST_TEAMS_LISTBOX_POPULATOR_NAME[] = "list_teams_populator";
#endif

	const char RUUI_EDIT_PLAYERS_POPULATOR_NAME[] = "customise_edit_players";
	const char RUUI_PLAYER_LINK_POPULATOR_NAME[] = "player_link_populator";
	const char RUUI_TEAM_LINK_POPULATOR_NAME[] = "link_teams_populator";
	const char RUUI_SEARCH_PLAYERS_POPULATOR_NAME[] = "customise_search_players";

	// Team Creator
	const char RUUI_TEAM_CREATOR_STRIP_POPULATOR_NAME[] = "team_creator_strip_populator";
	const char RUUI_TEAM_CREATOR_EMBLEM_POPULATOR_NAME[] = "team_creator_emblem_populator";
	const char RUUI_TEAM_CREATOR_COMMENTARY_NAME_POPULATOR_NAME[] = "team_creator_commentary_name_populator";
	const char RUUI_TEAM_CREATOR_COUNTRY_POPULATOR_NAME[] = "team_creator_country_populator";
	const char RUUI_TEAM_CREATOR_SQUAD_POPULATOR_NAME[] = "team_creator_squad_populator";

	// Player management template.
	const char	RUUI_PLAYER_MANAGEMENT_INJURED_TEXTURE[]			= "team_lineup_playerbox_injured";
	const char	RUUI_PLAYER_MANAGEMENT_SUSPENDED_TEXTURE[]			= "team_lineup_playerbox_suspended";
	const char	RUUI_PLAYER_MANAGEMENT_FOCUS_TEXTURE[]				= "team_lineup_playerbox_on";
	const char	RUUI_PLAYER_MANAGEMENT_NO_FOCUS_TEXTURE[]			= "team_lineup_playerbox_off";
	const char	RUUI_PLAYER_MANAGEMENT_FATIGUE_GOOD_TEXTURE[]		= "player_fatigue_bar_green";
	const char	RUUI_PLAYER_MANAGEMENT_FATIGUE_GOOD_LEFT_CAP[]		= "player_fatigue_bar_green_left";
	const char	RUUI_PLAYER_MANAGEMENT_FATIGUE_GOOD_RIGHT_CAP[]		= "player_fatigue_bar_green_right";
	const char	RUUI_PLAYER_MANAGEMENT_FATIGUE_AVERAGE_TEXTURE[]	= "player_fatigue_bar_yellow";
	const char	RUUI_PLAYER_MANAGEMENT_FATIGUE_AVERAGE_LEFT_CAP[]	= "player_fatigue_bar_yellow_left";
	const char	RUUI_PLAYER_MANAGEMENT_FATIGUE_AVERAGE_RIGHT_CAP[]	= "player_fatigue_bar_yellow_right";
	const char	RUUI_PLAYER_MANAGEMENT_FATIGUE_BAD_TEXTURE[]		= "player_fatigue_bar_red";
	const char	RUUI_PLAYER_MANAGEMENT_FATIGUE_BAD_LEFT_CAP[]		= "player_fatigue_bar_red_left";
	const char	RUUI_PLAYER_MANAGEMENT_FATIGUE_BAD_RIGHT_CAP[]		= "player_fatigue_bar_red_right";
	const float	RUUI_PLAYER_MANAGEMENT_FATIGUE_MAX_BAD				= 0.25f;
	const float	RUUI_PLAYER_MANAGEMENT_FATIGUE_MAX_AVERAGE			= 0.6f;

	// Ingame team management populators.
	const char RUUI_TEAM_MANAGEMENT_POPULATOR_NAME[] = "team_management_populator";
	const char RUUI_TEAM_MANAGEMENT_IS_INJURY_SUBSTITUTION[] = "is_injury_substitution";
	const char RUUI_TEAM_MANAGEMENT_SELECTED_PLAYER_ID[] = "default_selected_player_id";
	const char RUUI_TEAM_MANAGEMENT_INJURY_TEAM_INDEX[] = "team_management_team_index";

	// Halftime / Fulltime stat screen populator.
	const char RUUI_MATCH_STATS_PRIMARY_POPULATOR_NAME[] = "match_stats_primary_populator";
	const char RUUI_MATCH_STATS_SECONDARY_POPULATOR_NAME[] = "match_stats_secondary_populator";
	const char RUUI_MATCH_STATS_PRO_POPULATOR_NAME[] = "match_stats_pro_populator";
	const char RUUI_MATCH_DETAIL_STATS_POPULATOR_NAME[] = "match_detail_stats_populator";

	// These are the system events for the Halftime / Fulltime stat screen.
	const char RUUI_MATCH_STATS_PRIMARY_FINISHED[] = "match_stats_primary_finished";
	const char RUUI_MATCH_STATS_SECONDARY_FINISHED[] = "match_stats_secondary_finished";
	const char RUUI_MATCH_STATS_PRO_FINISHED[] = "match_stats_pro_finished";
	const char RUUI_MATCH_DETAIL_STATS_FINISHED[] = "match_detail_stats_finished";

	const char RUUI_STADIUM_POPULATOR_NAME[] = "stadium_populator";
	const char RUUI_STADIUM_POPULATOR_REFRESHED[] = "stadium_populator_refreshed";

	const char RUUI_CAMERA_SETTINGS_POPULATOR_NAME[] = "camera_settings_populator";

	const int RUUI_NUM_CONTEXT_ENTRIES = 3;

	/// This is the id field -- it corresponds to a competition id -- indicating the player wishes to select from the list of custom teams.
	const int RUUI_CUSTOM_TEAMS_LIST_ID = 0;
	const int RUUI_INTERNATIONAL_TEAMS_LIST_ID = 1;
	const int RUUI_TOUR_TEAMS_LIST_ID = 2;
	const int RUUI_CUSTOM_TEAMS_DELETE_LIST_ID = 3;
	const int RUUI_CUSTOM_EDIT_PLAYERS_LIST_ID = 4;
	const int RUUI_CUSTOM_SEARCH_TEAM_LIST_ID = 5;
	const int RUUI_CUSTOM_TEAMS_SEVENS = 6;
	const int RUUI_CUSTOM_EDIT_PLAYERS_SEVENS_LIST_ID = 7;
	const int RUUI_CUSTOM_UNASSIGNED_PLAYERS = 8;

	/// @}


	/// \name Asset path constants
	/// @{
	const char RUUI_LOGO_PATH[] = "/Game/Rugby/cmn_con/ui/Logos/%s/team_logo.team_logo";
	const char RUUI_LOGO_SCHEDULE_PATH[] = "/Game/Rugby/cmn_con/ui/Logos/%s/schedule_team_logo.schedule_team_logo";
	const char RUUI_LOGO_HUD_PATH[] = "/Game/Rugby/cmn_con/ui/Logos/%s/hud_team_logo.hud_team_logo";
	/// @}

/// \name Sound constants
/// @{
	/// Slider bar value increase.
	const char RU_SOUND_SLIDER_BAR_VALUE_INCREASE[]	= "event:/ui/settings/settings_right";
	/// Slider bar value decrease.
	const char RU_SOUND_SLIDER_BAR_VALUE_DECREASE[]	= "event:/ui/settings/settings_left";
	/// Open popup.
	const char RU_SOUND_POPUP_SOUND_OPEN[]			= "event:/ui/help_tips/open_popup";
	/// Close popup.
	const char RU_SOUND_POPUP_SOUND_CLOSE[]			= "event:/ui/help_tips/close_popup";
	/// Pause menu launch.
	const char RU_SOUND_PAUSE_MENU_LAUNCH[]			= "event:/ui/pause_menu/pause_game";
	/// Pause menu dismiss.
	const char RU_SOUND_PAUSE_MENU_DISMISS[]		= "event:/ui/pause_menu/resume_game";
	/// HUD Rolodex advances to next step.
	const char RU_SOUND_ROLODEX_STEP_ADVANCE[]		= "event:/ui/tutorials/instruction_advance_positive";
	/// Tutorial demonstration.
	const char RU_SOUND_TUTORIAL_DEMONSTRATION[]	= "event:/ui/tutorials/start_demo";
	/// Tutorial practive mode.
	const char RU_SOUND_TUTORIAL_PRACTIVE_MODE[]	= "event:/ui/tutorials/start_practice_mode";
	/// Tutorial passed.
	const char RU_SOUND_TUTORIAL_PASSED[]			= "event:/ui/tutorials/passed";
	/// Tutorial failed.
	const char RU_SOUND_TUTORIAL_FAILED[]			= "event:/ui/tutorials/failed";
	/// HUD Replay swipe begin.
	const char RU_SOUND_SWIPE_BEGIN[]				= "event:/hud/screen_swipe/swipe_on";
	/// HUD Replay swipe ends.
	const char RU_SOUND_SWIPE_END[]					= "event:/hud/screen_swipe/swipe_off";
	/// HUD info panels appears.
	const char RU_SOUND_INFO_PANEL_APPEARS[]		= "event:/hud/info_panels/panel_open";
	/// HUD info panels hides.
	const char RU_SOUND_INFO_PANEL_HIDES[]			= "event:/hud/info_panels/panel_close";
	/// Used for X360 Select Save Device popup when it cannot launch
	static const char RU_SOUND_MENU_ERROR[]		= "event:/ui/Global/MenuError";
	/// Used when the player achieves a medal from a tutorial.
	const char RU_SOUND_TUTORIAL_MEDAL_AWARDED[]		= "event:/ui/tutorials/medal_award";
	/// Rugby Store. Used when the user's credit counts up.
	const char RU_SOUND_RUGBY_STORE_COUNTING_UP[]	= "event:/ui/rugby_store/credit_count";
	/// Rugby Store. Used when the user's credit counts up while they are in the training field.
	const char RU_SOUND_RUGBY_STORE_COUNTING_UP_TRAINING[]	= "event:/ui/tutorials/cash_count";
	/// Rugby Store. Used when a purchase is made.
	const char RU_SOUND_RUGBY_STORE_PURCHASE[]		= "event:/ui/rugby_store/item_purchase";
	/// HUD Overtime started. Played whenever the match HUD timer turns to its overtime colour.
	const char RU_SOUND_MATCH_OVERTIME_STARTED[]	= "event:/hud/score_timer/added_time";
	/// HUD Impact text. Played whenever the impact text appears.
	const char RU_SOUND_MATCH_IMPACT_TEXT[]			= "event:/hud/score_timer/notice";
	const char RU_SOUND_PRO_REQUEST_FEEDBACK_TEXT[]	= "event:/hud/score_timer/notice";
	const char RU_SOUND_PRO_GOAL_FEEDBACK_TEXT[]	= "event:/hud/score_timer/notice";
	/// Sub menu enter
	const char RU_SOUND_SUB_MENU_ENTER[]			= "event:/ui/main_menu/enter_sub_menu";
	/// Back parent
	const char RU_SOUND_BACK_PARENT[]			= "event:/ui/main_menu/back_parent";
	/// Menu move
	const char RU_SOUND_MENU_MOVE[]				= "event:/ui/main_menu/move";
/// @}

		// These are the OLD colors we set text to in our list fields:
	static const FLinearColor RU_UI_ORANGE_COLOUR = FLinearColor(0.8308f,0.2423f,0.0000f, 1.0f);
	//static const FLinearColor RU_UI_GREEN_COLOUR = FLinearColor(0.235f, 0.710f, 0.263f, 1.0f); //This is what changes the player team highlighting -Kade
	static const FLinearColor RU_UI_GREEN_COLOUR = FLinearColor(0.0f, 0.6f, 0.0f, 1.0f); // making more readable
	static const FLinearColor RU_UI_BLUE_COLOUR = FLinearColor(0.084376f, 0.3185469f, 0.610496f, 1.0f); //Default color of field text

		// These are the colors we set text to in our list fields:
	static const FLinearColor RU_UI_COLOR_DEFAULT = FLinearColor(0.082283f, 0.3185469f, 0.610496f, 1.0f); // blue
	static const FLinearColor RU_UI_COLOR_DEFAULT_FOCUSED = FLinearColor(1.0f, 1.0f, 1.0f, 1.0f); // white

	static const FLinearColor RU_UI_COLOR_PLAYER = FLinearColor(0.822786f, 0.2704982f, 0.040915f, 1.0f); // orange, same as selected field overlay (big orange bar)
	static const FLinearColor RU_UI_COLOR_PLAYER_FOCUSED = FLinearColor(0.2663557f, 0.0865005f, 0.0122865f, 1.0f); // dark orange

	static const FLinearColor RU_UI_COLOR_WINNER = FLinearColor(0.0065121f, 0.0953075f, 0.3049874f, 1.0f); // dark blue
	static const FLinearColor RU_UI_COLOR_WINNER_FOCUSED = FLinearColor(0.0065121f, 0.0953075f, 0.3049874f, 1.0f); // dark blue (dupelicate of above right now but may be changed later so should be kept)

#endif
