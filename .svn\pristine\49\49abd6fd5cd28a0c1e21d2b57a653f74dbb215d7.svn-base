/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef RUPASSACTION_H
#define RUPASSACTION_H

#include "Mab/Time/MabTimeSource.h"
#include "Mab/Time/MabTimer.h"
#include "Match/AI/Actions/RUAction.h"
#include "Match/AI/Actions/RUActionTackleBase.h"
#include "Match/RugbyUnion/Enums/RUPassTypeEnum.h"
#include "Match/SIFObjectLists.h"
#include "Match/SSRole.h"

#define MAX_PASS_TIME 1.0f
#define MAX_PASS_PLAYERS 4


/// Standard back line pass distances and spacing
const float STANDARD_PASS_DIST = 5.0f;// 7.5f; 
const float COMPRESSED_PASS_DIST	= 3.5f;
const float WIDE_PASS_DIST = 6.7f; //10.0f;

const float STANDARD_LINE_SPACING_DIST = 7.5f; 
const float COMPRESSED_LINE_SPACING_DIST = 3.5f;
const float WIDE_LINE_SPACING_DIST = 10.0f;

const float LONG_PASS_DIST			= 15.0f;
const float MEDIUM_PASS_DIST		= 6.0f;//5.0f;
const float MAX_PASS_DIST			= WIDE_PASS_DIST * 3.0f;//WIDE_PASS_DIST * 4;
const float MAX_PRO_REQ_PASS_DIST	= WIDE_PASS_DIST * 8.0f;//WIDE_PASS_DIST * 4;
const float MAX_OFFLOAD_DISTANCE	= 8.0f;//9.0f;
const float MAX_PRO_REQ_OFFLOAD_DISTANCE	= 16.0f;//9.0f;

// Pass distances (in metres).
const float MAX_PASS_X_DISTANCE			= MAX_PASS_DIST;
const float MAX_PASS_Z_DISTANCE			= 20.0f;

// Specifically for pro requests
const float MAX_PRO_REQ_PASS_X_DISTANCE	= MAX_PRO_REQ_PASS_DIST;
const float MAX_PRO_REQ_PASS_Z_DISTANCE	= 40.0f;

const float MAX_PASS_Z_FORWARD_DISTANCE	= 0.3f;

const float LONG_PASS_SPEED				= 20.0f;
const float MEDIUM_PASS_SPEED			= 15.0f;
const float SHORT_PASS_SPEED			= 10.0f;
const float OFFLOAD_PASS_SPEED			= 5.0f;

const int MIN_KICK_ROLE_PASS_PRIORITY = 70;

/**
 Basic Pass Behaviour

 <AUTHOR> McAuley
*/

class ARugbyCharacter;
class PassAnimation;
class NMMabAnimationNetwork;
struct OFFLOAD_META;

struct OFFLOAD_ARM_META
{
	float difficulty;
};

struct PASS_META
{
	float difficulty;
	float max_pass_distance;
	float max_pass_distance_by_closing;
	float max_pass_distance_by_speed;
	float max_pass_distance_by_tackle;

	float time_till_tackle_contact;
	float pre_tackle_contact_pct;

	OFFLOAD_META offload_meta;
	OFFLOAD_ARM_META offload_arm_meta[OA_LAST];
	int n_valid_offload_arms;
	OFFLOAD_ARM selected_offload_arm;
};

class RUActionPass : public RUAction
{
	MABRUNTIMETYPE_HEADER(RUActionPass);
public:
	//@name Setup & Construction //@{
	RUActionPass( ARugbyCharacter* player );
	virtual ~RUActionPass();

	/// Generic pass to pid
	void Enter( ARugbyCharacter* target_player, PASS_TYPE type, RLROLE_INTERCEPT_TYPE intercept_override = RLRIT_RUN_DONT_OVERRIDE );
	/// Generic pass to position
	void Enter( const FVector &target_position, PASS_TYPE type );
	/// Start a pass in the specified direction, skipping the specificed number of players
	void Enter( int pass_direction, int players_to_skip, PASS_TYPE type, RLROLE_INTERCEPT_TYPE intercept_override = RLRIT_RUN_DONT_OVERRIDE );

	/// Sentinel values for the players to skip
	static const int RUPASS_SKIP_AUTOSELECT = -1;
	static const int RUPASS_SKIP_AUTOSELECT_WIDE = -2;
	static const int RUPASS_SKIP_PLAYMAKER = -3;

	//@name Status functions //@{
	virtual bool	IsInterruptable()	const { return !m_is_running; }
	///bool	HasPassStarted()	{ return state >= PASS_WAITPICKUP; }
	bool	CanBeTackled();
	//@}

	//@name Execution //@{
	// Applies 'this' behavior in seeking the target
	void Update( const MabTimeStep& game_time_step );
	//@}

	static void CalculateReceiversInPassDirectionNoModify( ARugbyCharacter* passer, int p_pass_direction, TArray<ARugbyCharacter*>& returned_receivers, int& pass_inclusion_role_count );
	static void CalculateReceiversInPassDirection( ARugbyCharacter* passer, int p_pass_direction, TArray<ARugbyCharacter*>& returned_receivers, int& pass_inclusion_role_count, bool debug_on = false, SIFDebugDrawPool* debug_key_pool = NULL, bool allow_state_modify = true, bool force_pass_to_backs = false);

	/// Get the pid of the given receiver from the list
	static ARugbyCharacter* GetReceiverFromList( const TArray<ARugbyCharacter*>& player_list, int index );
	static ARugbyCharacter* GetBestReceiverFromList( ARugbyCharacter* passer, const TArray<ARugbyCharacter*>& player_list, int selection_type );
	static ARugbyCharacter* GetBestReceiverInDirection(const FVector& passer_pos, const SIFRugbyCharacterList& player_list, int dir, int ideal_pass_dist = STANDARD_PASS_DIST);

	//static int TryToFindPass( ARugbyCharacter* passer, int p_players_to_skip, int p_pass_direction, bool limit_x_distance, int desperation, int pass_preferred = -1 );

	/// For the current aggregate, say whether or not the given aggregate is allowed to run
	bool CanEnterOtherAction(RU_ACTION_INDEX id) override;

	/// For actions that require the ball, can we prewind the action?
	/// i.e. Can we cache the action before the player receives the ball and then execute once we receive it
	virtual bool CanPrewindAction() const { return !IsRunning(); }

	/// Pass can be informed by anything that it is being disrupted
	/// takes a direction the hit is coming from (used to calculate the direction of the potential knockon)
	/// hit size is 0.0f to 1.0f, with 1.0f being the biggest hit possible (ie: ball will have highest chance of being lost)
	virtual void InformHit( float angle, float hit_size );

	/// Get the number of frames until the ball is released
	float GetTimeTillRelease();

	/// Certain role types (primarily kicks
	static const MabTypeID* GetPassInclusionRoleTypes();
	static int GetPassInclusionRoleTypesCount();
	static bool IsAPassInclusionRole( MabTypeID type );
	static bool IsAnOffload( ARugbyCharacter* player );

	#ifdef BUILD_DEBUG
	/// Pass debug facilities

	/// Turn on pass debugging
	static void SetPassViewDebug( bool enable );
	#endif

	/// Get short class name for on screen debug display.
	const char* GetShortClassName() const override { return "Pass"; }

	float GetApproxPassDistance();

	static void GetPassMeta( ARugbyCharacter* passer, ARugbyCharacter* target, const FVector& target_pos, PASS_META& pass_meta, PASS_TYPE pass_type = PT_UNKNOWN, bool forProRequest = false );

	void Abort();

	bool IsADummyPass() const { return params.type == PT_DUMMY; }

	MabString GetOffloadPassMetaDebugString();

	ARugbyCharacter* GetTargetPlayer() const { return params.target_player; }

protected:
	/// Setup for animation transitions and blending, rotational or positional tweening, are handled at
	/// this point.
	bool InternalEnter() override;

	/// Any cleanup should occur here.
	void InternalExit(bool in_destroy) override;

	/// Called on the enter of this action so any conflicting actions can be stopped
	void AbortConflictingActions() override;

	/// Pass to the play maker
	bool PassToPlayMaker();

private:
	void AnimationEvent(float time, ERugbyAnimEvent event, size_t userdata, bool bIsBlendingOut = false);

	/// calculate the angle from us to the target (this isnt relative to our facing angle)
	float CalculateAngleToTarget();

	/// Core pass variables
	class ActionParams {
	public:
		ActionParams()
			: type( PT_UNKNOWN )
			, target_offset( FVector::ZeroVector )
			, target_player( NULL )
			, intercept_override_type( RLRIT_RUN_DONT_OVERRIDE )
		{}

		PASS_TYPE type;
		/// Pass target details - either a player, or just a vector
		FVector target_offset;
		ARugbyCharacter* target_player;
		RLROLE_INTERCEPT_TYPE intercept_override_type;
	};
	ActionParams params;

	/// A sorter for the pass receivers
	struct ReceiverSorter
	{
		float pass_x_ref = 0.0f;
		bool operator()(const ARugbyCharacter* a, const ARugbyCharacter* b) const;
	};

	typedef enum {
		PASS_INITIAL,
		PASS_WAIT_PICKUP, // If we don't have the ball when the pass is starting
		PASS_INHANDS,
		PASS_INAIR,
		PASS_WAIT_ANIMATION,
		PASS_WAIT_REJOIN,
		PASS_COMPLETE } PASS_STATE;
	PASS_STATE state;

	bool UpdateStateInitial();
	bool UpdateStateWaitPickup();

	void DoPickup();

	bool UpdateStateInHands();
	bool UpdateStateInAir();

	void UpdateBallReleaseChecks( float& hit_magnitude, float delta_time );

	bool UpdateStateWaitAnimation();
	bool UpdateStateWaitRejoin();
	bool UpdateStateComplete();

	void InitialisePassType( PASS_TYPE type );

	float CalculatePassQuality();

	bool IsLineoutPass() const		{ return params.type == PT_LINEOUT || params.type == PT_SLAPDOWN; }

	bool IsOffloadAllowed() const;
	bool WillPassLookUglyOrUnrealsiticOnRelease();


	/// direction we are passing in
	int direction;

	/// chosen animation details
	PassAnimation* chosen_base_anim;
	MabString chosen_actual_anim;

	// keep track of what we have done through the animation process
	const MabLockStepTimeSource* game_time;
	MabTime ball_pickup_time;
	MabTime ball_release_time;
	MabTime anim_finish_time;

	FVector rotated_ball_joint;

	// Hit details from a tackle (or whatever)
	float hit_magnitude; // Value from 0 to 1 rating the size of the hit 0.0, means no effect
	float time_since_release_prevented;
	bool current_release_ball_check;

	float pass_quality;

	void CalculateBallJointPos( FVector& relative_joint, float target_angle_radians );

	void StartInterceptor();
	void StopInterceptor();
	void StartPassAnticipation();
	void StopPassAnticipation();
	void StartDistracted();

	// Starts the actual pass animation - given an approximate target to work out animation to use
	bool StartPassAnimation( const FVector& approx_target );

	// Timer that creates a little delay for the passer so that they
	// don't react instantly after making the pass
	MabTimer rejoin_action_timer;
	PASS_META pass_meta;

	/// Dummy pass details
	ARugbyCharacter* dummied_defender;
	ARugbyCharacter* dummied_pass_to_player;
	float dummy_penalty_time;
	void CalculateDummyPassResult();

	bool aborted;

	MabString chosen_offload_anim;
};

#endif
