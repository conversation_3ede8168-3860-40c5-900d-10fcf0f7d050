//#rc3_legacy_pch #include "Precompiled.h"

#include "Match/AI/Roles/Competitors/SetPlays/RURoleSetplayPlayTheBallReceiver.h"

#include "Match/AI/Roles/Competitors/SetPlays/RURoleSetPlay.h"
#include "Match/AI/SetPlays/SSSetPlayManager.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/Roles/Competitors/RURoleGetTheBall.h"
#include "Match/AI/Roles/Competitors/RURolePenaltyAttack.h"
#include "Match/AI/Roles/Competitors/SSRoleFormation.h"
#include "Match/Ball/SSBall.h"
#include "Match/Camera/SSCameraManager.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUInputPhaseDecision.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/RugbyUnion/Rules/Triggers/RURuleTriggerPenalty.h"
#include "Match/SIFGameObject.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Match/SSRoleFactory.h"
#include "Match/SSSpatialHelper.h"

//#rc3_legacy_include #include <NMMabAnimationEvents.h>

#include "Character/RugbyPlayerController.h"
#include "RugbyGameInstance.h"

MABRUNTIMETYPE_IMP1(RURoleSetplayPlayTheBallReceiver, RURoleSetplay);

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
RURoleSetplayPlayTheBallReceiver::RURoleSetplayPlayTheBallReceiver(SIFGameWorld* game)
	: RURoleSetplay(game)
	, state{}
	, m_last_receiver_pos(FVector())
	, m_last_passer_pos(FVector())
{
}

//-------------------------------------------------------------------------
// Enter
//-------------------------------------------------------------------------
void RURoleSetplayPlayTheBallReceiver::Enter(ARugbyCharacter* player)
{
	RURoleSetplay::Enter(player);

	MABASSERT(player == m_pPlayer);

	SetReceiverPosition();

	wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);

	if (m_pGame->GetGameState()->GetBallHolder() == player)
		state = RoleState::BALL_RECEIVED;
	else
		state = RoleState::MOVING;

	// Will need this for pickup anims
	m_pPlayer->GetMabAnimationEvent().Add(this, &RURoleSetplayPlayTheBallReceiver::AnimationEvent);
}

//-------------------------------------------------------------------------
// Exit
//-------------------------------------------------------------------------
void RURoleSetplayPlayTheBallReceiver::Exit(bool forced)
{
	//Unregister animation event
	m_pPlayer->GetMabAnimationEvent().Remove(this, &RURoleSetplayPlayTheBallReceiver::AnimationEvent);
	RURoleSetplay::Exit(forced);
}

///-------------------------------------------------------------------------
/// Update
///-------------------------------------------------------------------------

void RURoleSetplayPlayTheBallReceiver::UpdateLogic(const MabTimeStep& game_time_step)
{
	RURoleSetplay::UpdateLogic(game_time_step);

	// Move the player into receiving position
	if (state == RoleState::MOVING)
	{
		float targ_dist = m_pMovement->GetTargetDistance();
		//float passer_distance = (m_pMovement->GetCurrentPosition() - GetPasserPosition()).Magnitude();
		// AJ this is a temporary fix for the role getting stuck on moving and not quite at the right spot.
		// We want to be close enough to target but not on top of the passer
		if (targ_dist <= 3.0f)// && passer_distance > 0.7f)
		{
			state = RoleState::WAITING_FOR_RELEASE;
			bWaypointReached = true;
		}
		return;
	}

	// Waiting for the player who is playing the ball to release the ball
	if (state == RoleState::WAITING_FOR_RELEASE)
	{
		if (!m_pGame->GetGameState()->GetBallHolder())
		{
			RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
			ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
			if (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				animation->PlayAnimation(PICKUP_ANIM);
				// Quick hack since we don't have animations yet, just give the ball to the receiver once it is free
				m_pGame->GetGameState()->SetBallHolder(m_pPlayer, true);
				state = RoleState::PICKING_UP;
			}
		}
		return;
	}

	if (state == RoleState::PICKING_UP)
	{
		// Waiting
		return;
	}

	if (state == RoleState::BALL_RECEIVED)
	{
		if (SetplayManager && !SetplayManager->HasSetplayStarted() && SetplayManager->ArePlayersInPosition())
		{
			//Start the setplay and update role
			SetplayManager->SetSetplayStarted(true);
			m_ActionState = ERugbySetplayAction::PASS;
			bWaypointHasAction = true;
			m_pGame->GetGameState()->PickedUp(m_pPlayer, PUC_FROM_RUCK);
			RURoleSetplay::UpdateLogic(game_time_step);
			return;
		}

		if (!SetplayManager->HasSetplayStarted())
		{
			// Abort if players aren't in position
			SetplayManager->AbortSetplay();
		}

		state = RoleState::DONE;
		m_pGame->GetGameState()->SetPhase(RUGamePhase::PLAY);
	}

}

///-------------------------------------------------------------------------
/// GetFitness
///-------------------------------------------------------------------------
int RURoleSetplayPlayTheBallReceiver::GetFitness(const ARugbyCharacter* player, const SSRoleArea* area)
{
	if (FString(player->GetRole()->GetShortClassName()).Compare(FString("PTBR")) == 0)
		return 10000;

	return 0;
}

///-------------------------------------------------------------------------
/// IsInterruptable
///-------------------------------------------------------------------------
bool RURoleSetplayPlayTheBallReceiver::IsInterruptable() const
{
	if (state != RoleState::DONE)
		return false;

	return RURoleSetplay::IsInterruptable();
}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
void RURoleSetplayPlayTheBallReceiver::AnimationEvent(float /*time*/, ERugbyAnimEvent event, size_t /*userdata*/, bool /*bIsBlendingOut = false*/)
{
	if (event == ERugbyAnimEvent::BLEND_OUT_EVENT && state == RoleState::PICKING_UP)
	{
		state = RoleState::BALL_RECEIVED;
	}
}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
void RURoleSetplayPlayTheBallReceiver::SetReceiverPosition()
{
	m_pMovement->SetThrottleAndTargetSpeedByUrgency(1, ACTOR_SPEED::AS_FASTRUN, ACTOR_SPEED::AS_FASTWALK);
	SetPasserPosition();
	m_pMovement->SetTargetPosition(GetReceiverPosition(), true);
}

FVector RURoleSetplayPlayTheBallReceiver::GetReceiverPosition()
{
	auto ballHolder = m_pGame->GetGameState()->GetBallHolder();
	if (ballHolder)
	{
		FVector offset = FVector(0, 0, 1.5f * -ballHolder->GetAttributes()->GetPlayDirection());
		m_last_receiver_pos = GetPasserPosition() + offset;
	}
	return m_last_receiver_pos;
}

FVector RURoleSetplayPlayTheBallReceiver::GetPasserPosition()
{
	return m_last_passer_pos;
}

void RURoleSetplayPlayTheBallReceiver::SetPasserPosition()
{
	auto ballHolder = m_pGame->GetGameState()->GetBallHolder();
	if (ballHolder)
	{
		m_last_passer_pos = ballHolder->GetMovement()->GetCurrentPosition();
	}
}