#pragma once

// More info for usage:
// https://docs.unrealengine.com/en-us/Programming/Development/Tools/ConsoleManager

#include "StadiumToolsConsoleVars.h"

//////////////////////////////////////////////////////////////////////////
// Debugging
//////////////////////////////////////////////////////////////////////////
static TAutoConsoleVariable<int32> DropInlevel(
	TEXT("vp.dropin"),
	0,
	TEXT("Allows drop in of controllers"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> DropOutlevel(
	TEXT("vp.dropout"),
	0,
	TEXT("Allows drop out of controllers"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarPCInfo(
	TEXT("ww.PCInfo"),
	0,
	TEXT("Displays the player controller info"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarGIState(
	TEXT("ww.GIState"),
	0,
	TEXT("Displays game instance state"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarInitialKickoffTeam(
	TEXT("ww.InitialKickoffTeam"),
	-1,
	TEXT("Which side to start kick off"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarMovementTarget(
	TEXT("ww.MovementTarget"),
	0,
	TEXT("Draw the movement target of the player"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarMovementDebug(
	TEXT("ww.MovementDebug"),
	0,
	TEXT("Prints information about a players movement state"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarSkipSplashScreens(
	TEXT("ww.SkipSplashScreens"),
	0,
	TEXT("Skips all the splash screens"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarQuickStart(
	TEXT("ww.QuickStart"),
	0,
	TEXT("Skips all intro flows, and goes straight into a stadium"),
	ECVF_Cheat);

// -GameWorldInfo
static TAutoConsoleVariable<int32> CVarGameWorldInfo(
	TEXT("ww.GameWorldInfo"),
	0,
	TEXT("Displays information for SIFGameWorld instances"),
	ECVF_Cheat);

// -DefaultHomeTeam
static TAutoConsoleVariable<int32> CVarDefaultHomeTeam(
	TEXT("ww.DefaultHomeTeam"),
	0,
	TEXT("Sets the default home team to use by setting database ID"),
	ECVF_Cheat);

// -DefaultAwayTeam
static TAutoConsoleVariable<int32> CVarDefaultAwayTeam(
	TEXT("ww.DefaultAwayTeam"),
	0,
	TEXT("Sets the default away team to use by setting database ID"),
	ECVF_Cheat);

// -NoRumble
static TAutoConsoleVariable<int32> CVarNoRumble(
	TEXT("ww.NoRumble"),
	0,
	TEXT("Toggles rumble on or off"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarNextTackleAnkleTap(
	TEXT("ww.NextTackleAnkleTap"),
	0,
	TEXT("Forces next tackle to result in an ankle tap."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarNextTackleHigh(
	TEXT("ww.NextTackleHigh"),
	0,
	TEXT("Forces next tackle to result in a high tackle with no penalty."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarNextTackleYellowCard(
	TEXT("ww.NextTackleYellowCard"),
	0,
	TEXT("Forces next tackle to result in a yellow card."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarNextCollectKnockOn(
	TEXT("ww.NextCollectKnockOn"),
	0,
	TEXT("Force next catch or collect to be a knock on."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarShowCollidables(
	TEXT("ww.ShowCollidables"),
	0,
	TEXT("Shows bounds of registered collisions."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarShowGamePhase(
	TEXT("ww.GamePhase"),
	0,
	TEXT("Prints the current game phase to screen."),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// Set Plays
//////////////////////////////////////////////////////////////////////////
static TAutoConsoleVariable<int32> CVarSetPlayDebug(
	TEXT("ww.SetPlays"),
	0,
	TEXT("Show debug for setplays"),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// Passing
//////////////////////////////////////////////////////////////////////////

static TAutoConsoleVariable<int32> CVarInterceptDebug(
	TEXT("ww.InterceptDebug"),
	0,
	TEXT("Displays debugging for intercepting the ball."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarSetplayInterceptDebug(
	TEXT("ww.SetplayInterceptDebug"),
	0,
	TEXT("Displays debugging for setplay intercepting the ball."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarPerfectPassQuality(
	TEXT("ww.PerfectPassQuality"),
	0,
	TEXT("Disables the random offset applied to intercept location of ball."),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// AI
//////////////////////////////////////////////////////////////////////////

static TAutoConsoleVariable<int32> CVarDisableTackle(
	TEXT("ww.ai.disableTackles"),
	0,
	TEXT("Removes the AIs abaility to tackle"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarDisableAttackingTeam(
	TEXT("ww.ai.disableAttackingTeam"),
	0,
	TEXT("Disables formation AI updates for the attacking team."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarDisableDefendingTeam(
	TEXT("ww.ai.disableDefendingTeam"),
	0,
	TEXT("Disables formation AI updates for the attacking team"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarDisableDefendingTeamExceptFullback(
	TEXT("ww.ai.disableDefendingTeamExceptFullback"),
	0,
	TEXT("Disables formation AI updates for the attacking team except for the fullback"),
	ECVF_Cheat);

//This console var is commented out to enable to new fullback a.i. by default
// static TAutoConsoleVariable<int32> CVarEnableNewFullbackAI(
// 	TEXT("ww.ai.EnableNewFullbackAI"),
// 	0,
// 	TEXT("Turns on the new fullback A.I. logic"),
// 	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarEnableNewFullbackAILogging(
	TEXT("ww.ai.EnableNewFullbackAILogging"),
	0,
	TEXT("Turns on the new fullback A.I. logic logging"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarFendLogicState(
	TEXT("ww.ai.FendLogicState"),
	0,
	TEXT("State 0 is old fend logic. State 1 is the hold fend logic. State 2 is the exact timing logic. "),
	ECVF_Cheat);

//Disable AI (Not Game Breaking)
static TAutoConsoleVariable<int32> CVarDisableAI_NGB(
	TEXT("ww.ai.disableAI_NGB"),
	0,
	TEXT("Disables AI without breaking the game flow"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarShowTargetPosition(
	TEXT("ww.ai.showTargetPosition"),
	0,
	TEXT("Render target position"),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// Formations
//////////////////////////////////////////////////////////////////////////
static TAutoConsoleVariable<int32> CVarFormationVisibleDef(
	TEXT("ww.formations.showDef"),
	0,
	TEXT("Display debug lines for defending team."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarFormationVisibleAtt(
	TEXT("ww.formations.showAtt"),
	0,
	TEXT("Display debug lines for attacking team."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarRoleDebug(
	TEXT("ww.Roles"),
	0,
	TEXT("Show roles of all players"),
	ECVF_Cheat);
 
//////////////////////////////////////////////////////////////////////////
// Training Level
//////////////////////////////////////////////////////////////////////////
static TAutoConsoleVariable<int32> CVarTrainingCreatures(
	TEXT("ww.trainingCreatures"), 
	0, 
	TEXT("Number of creatures to spawn in training"),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// Networking
//////////////////////////////////////////////////////////////////////////
// -SessionInfo
static TAutoConsoleVariable<int32> CVarSessionInfo(
	TEXT("wwnet.sessionInfo"),
	0,
	TEXT("Shows all info for current session"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarLoadTraining(
	TEXT("wwnet.loadTraining"),
	0,
	TEXT("Load into the training level for multiplayer"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarControllerPossessIndex(
	TEXT("wwnet.contPossessIndex"),
	0,
	TEXT("From which index to start possessing creatures."),
	ECVF_Cheat);
static TAutoConsoleVariable<int32> CVarReplicateBallBounce(
	TEXT("wwnet.RepBallBounce"),
	0,
	TEXT("Enable/Disable Ball Bounce Replication."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarReplicationTracing(
	TEXT("wwnet.RepTrace"),
	0,
	TEXT("Enable/Disable Replication Tracing."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarUseStateReplication(
	TEXT("wwnet.StateRep"),
	0,
	TEXT("Enable/Disable State Machine Based Replication."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarSetBallNetworkSmoothingAmount(
	TEXT("wwnet.BallInterpoAmount"),
	300,
	TEXT("Sets The Amount Of Ball Interpolation Back Time(Milliseconds)"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarBallExtrapolationLimit	(
	TEXT("wwnet.BallExtrapolationLimit"),
	500,
	TEXT("Sets The Ball Extrapolation Limit(Units)"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarSetFootballerNetworkSmoothingAmount(
	TEXT("wwnet.PlayerInterpoAmount"),
	400,
	TEXT("Sets The Amount Of Footballer Interpolation Back Time(Milliseconds)"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarFootballerallExtrapolationLimit(
	TEXT("wwnet.PlayerExtrapolationLimit"),
	800,
	TEXT("Sets The Player Extrapolation Limit(Units)"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarPlayerCustomNetworkSmoothing(
	TEXT("wwnet.UseCustomPlayerNetworkSmoothing"),
	1,
	TEXT("Use Custom Player Network Smoothing"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarShowNetworkSmoothingDebug(
	TEXT("wwnet.ShowNetworkSmoothingDebug"),
	1,
	TEXT("Show Network Smoothing Debug"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarLanGameAIOnly(
	TEXT("wwnet.AIOnly"),
	0,
	TEXT("Sets the game to be an AI only game or not"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarDisableAIStateMachine(
	TEXT("wwnet.DontUpdateAI"),
	0,
	TEXT("Toggle Updating AI on or off"),
	ECVF_Cheat);
static TAutoConsoleVariable<int32> CVarDisableUmpireAIStateMachine(
	TEXT("wwnet.DontUpdateUmpireAI"),
	0,
	TEXT("Toggle Updating Umpire AI On Or Off"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarBasicMovement(
	TEXT("wwnet.BasicMovement"),
	0,
	TEXT("Toggle Updating Umpire AI On Or Off"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarBasicLocationReplication(
	TEXT("wwnet.BasicLocationReplication"),
	0,
	TEXT("Toggle Basic Location Replication"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarReplicateBallMovement(
	TEXT("wwnet.ReplicateBallMovement"),
	0,
	TEXT("Toggle Replicate Ball Movement"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarNetworkIdles(
	TEXT("wwnet.NetworkIdles"),
	0,
	TEXT("Toggle Network Idle Animations"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarBallTimeDilation(
	TEXT("wwnet.BallTimeDilation"),
	0,
	TEXT("Toggle Ball Time Dilation"),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// Cutscenes
//////////////////////////////////////////////////////////////////////////
static TAutoConsoleVariable<int32> CVarNoCutscenes(
	TEXT("ww.NoCutscenes"),
	0,
	TEXT("Toggle Updating Umpire AI On Or Off"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarCutsceneState(
	TEXT("ww.CutsceneState"),
	0,
	TEXT("Toggle displaying cutscene state"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarCutsceneName(
	TEXT("ww.CutsceneName"),
	0,
	TEXT("Toggle displaying cutscene name"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarCutsceneSkipReason(
	TEXT("ww.CutsceneSkipReason"),
	0,
	TEXT("Logs out the skippability of cutscene reasons"),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// Cameras
//////////////////////////////////////////////////////////////////////////
static TAutoConsoleVariable<int32> CVarCameraDirection(
	TEXT("ww.CameraDirection"),
	0,
	TEXT("Displays the camera direction NORTH/SOUTH"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarDebugCameraLevel(
	TEXT("ww.DebugCameraLevel"),
	0,
	TEXT("level of debugging camera information, LEVEL_NONE = 0, LEVEL_TEXT = 1, LEVEL_ACTIVE = 2, LEVEL_ALL = 3, LEVEL_TRAILS = 4"),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// Rugby Ball
//////////////////////////////////////////////////////////////////////////
static TAutoConsoleVariable<int32> CVarGetBallFreeInfo(
	TEXT("ww.GetBallFreeInfo"),
	0,
	TEXT("Displays the information of the current free ball."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarBallExtrapolationPath(
	TEXT("ww.BallExtrapolationPath"),
	0,
	TEXT("Draws the path of the ball when extrapolated"),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// Animations
//////////////////////////////////////////////////////////////////////////
// -CVarAnimationName

static TAutoConsoleVariable<int32> CVarAnimationName(
	TEXT("ww.AnimationName"),
	0,
	TEXT("Toggle displaying Animation name"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarRootMotion(
	TEXT("ww.RootMotion"),
	0,
	TEXT("Toggle displaying the root motion for an animation"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarStateMachineDebug(
	TEXT("ww.animdebug.StateMachine"),
	0,
	TEXT("Show current state machine state data"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarNotifyStateDebug(
	TEXT("ww.animdebug.NotifyStates"),
	0,
	TEXT("Display currently active notify states and remaining durations"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarAnimationStateDebug(
	TEXT("ww.AnimationStateDebug"),
	0,
	TEXT("Toggle Animation State Debug Info"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarDummyHalfAnimState(
	TEXT("ww.AnimationDummyHalfInfo"),
	0,
	TEXT("Toggle Animation State Debug Info for Attacking Dummy Half"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarAnimDebugGetTheBall(
	TEXT("ww.animdebug.GetTheBall"),
	0,
	TEXT("Show debug info for get the ball state"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarAnimForceGetTheBallAction(
	TEXT("ww.animdebug.ForceGetTheBallAction"),
	-1,
	TEXT("Try to force a particular get the ball animation type. Will fallback to pickup in case of dead ball."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarAnimGTBContactVectors(
	TEXT("ww.animdebug.GTBContactVectors"),
	0,
	TEXT("Try to force a particular get the ball animation type. Will fallback to pickup in case of dead ball."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarAnimDebugIK(
	TEXT("ww.animdebug.IK"),
	0,
	TEXT("Draw IK targets"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarAnimDebugForceTry(
	TEXT("ww.animdebug.ForceTry"),
	-1,
	TEXT("Force a try animation based on datatable index"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarAnimShowRoot(
	TEXT("ww.animdebug.ShowRoot"),
	0,
	TEXT("Draw an axis at each players root bone"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarAnimShowTacklerAttach(
	TEXT("ww.animdebug.ShowTacklerAttach"),
	0,
	TEXT("Draw an axis at each players tackler attach bone"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarAnimTackleUseRootMotionOnAlignEnd(
	TEXT("ww.animdebug.TackleUseRootMotionOnAlignEnd"),
	1,
	TEXT("Apply root motion for any remaining delta time after the tackle alignment should have ended part way through an update."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarHeadLookDebug(
	TEXT("ww.animdebug.Head"),
	0,
	TEXT("Toggle displaying Head debug lines"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarShowStateMachineTransitions(
	TEXT("ww.animdebug.showActiveTransitions"),
	0,
	TEXT("Displays a cage around players who have an active state machine transition"),
	ECVF_Cheat);


//////////////////////////////////////////////////////////////////////////
// Rucks/Mauls/Scrums
//////////////////////////////////////////////////////////////////////////
static TAutoConsoleVariable<int32> CVarRuckDebug(
	TEXT("ww.RuckDebug"),
	0,
	TEXT("Toggle displaying ruck debug lines"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarMaulDebug(
	TEXT("ww.MaulDebug"),
	0,
	TEXT("Toggle displaying Maul debug lines"),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// OffSide
//////////////////////////////////////////////////////////////////////////

static TAutoConsoleVariable<int32> CVarOffsideDebug(
	TEXT("ww.OffSide"),
	0,
	TEXT("0-Off, 1-OffsideType, Debug Line, , 2-Offside and PlayerRole, 3-Offside and Participation Level, 4 - Draw line"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarScrumOffsideDebug(
	TEXT("ww.ScrumOffsideDebug"),
	0,
	TEXT("Displays debugging for setplay intercepting the ball."),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// IdleData
//////////////////////////////////////////////////////////////////////////

static TAutoConsoleVariable<int32> CVarIdleDebug(
	TEXT("ww.Idle"),
	0,
	TEXT("Toggle displaying IdleData"),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// Injury
//////////////////////////////////////////////////////////////////////////

static TAutoConsoleVariable<int32> CInjuryDebug(
	TEXT("ww.InjuryDebug"),
	0,
	TEXT("Toggle displaying Injury"),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// Lineout
//////////////////////////////////////////////////////////////////////////

static TAutoConsoleVariable<int32> CLineOutCatchDebug(
	TEXT("ww.LineOutCatch"),
	0,
	TEXT("Show LineOut catcher data"),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// ForwardPass
//////////////////////////////////////////////////////////////////////////

static TAutoConsoleVariable<int32> CForwardPassDebug(
	TEXT("ww.ForwardPass"),
	0,
	TEXT("Next Pass Forward data"),
	ECVF_Cheat);


//////////////////////////////////////////////////////////////////////////
// GloryCam
//////////////////////////////////////////////////////////////////////////

static TAutoConsoleVariable<int32> CVarGloryCamera(
	TEXT("ww.GloryCamera"),
	0,
	TEXT("Force Turn On Glory Cam"),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// Replay
//////////////////////////////////////////////////////////////////////////

static TAutoConsoleVariable<int32> CVarReplayInPauseMenu(
	TEXT("ww.Replay"),
	0,
	TEXT("Replay in Pause Menu"),
	ECVF_Cheat);


static TAutoConsoleVariable<int32> CVarReplayDebugEnabled(
	TEXT("ww.ReplayDebugEnabled"),
	0,
	TEXT("Show debug info for specific player in replay"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarReplayDebugPlayerId(
	TEXT("ww.ReplayDebugPlayerId"),
	26,
	TEXT("ID of player to show replay info for."),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarReplayDebugTeam(
	TEXT("ww.ReplayDebugTeam"),
	0,
	TEXT("Team of player that ID links to."),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// Stamina
//////////////////////////////////////////////////////////////////////////
static TAutoConsoleVariable<int32> CVarShowMaxStaminaLoss(
	TEXT("ww.ShowMaxStaminaLoss"),
	0,
	TEXT("Shows a red bar in the team management screen indicating players maximum stamina lost in the match so far."),
	ECVF_Cheat);

//////////////////////////////////////////////////////////////////////////
// Career
//////////////////////////////////////////////////////////////////////////
static TAutoConsoleVariable<int32> CVarMaximiseProgressCaptain(
	TEXT("ww.MaximiseProgressCaptain"),
	0,
	TEXT("Boost player progress towards positions after each match to max"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarMaximiseProgressGoalKick(
	TEXT("ww.MaximiseProgressGoalKick"),
	0,
	TEXT("Boost player progress towards positions after each match to max"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarMaximiseProgressPlayKick(
	TEXT("ww.MaximiseProgressPlayKick"),
	0,
	TEXT("Boost player progress towards positions after each match to max"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarSkipToYear(
	TEXT("ww.SkipToYear"),
	0,
	TEXT("Simulate to the entered year"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarWinAllGames(
	TEXT("ww.WinAllGames"),
	0,
	TEXT("Win all matches in career"),
	ECVF_Cheat);

static TAutoConsoleVariable<int32> CVarEnableSimulationStats(
	TEXT("ww.EnableSimulationStats"),
	0,
	TEXT("Allow the player to gain stats while simulating"),
	ECVF_Cheat);

static TAutoConsoleVariable<float> CVarTwoManTackleTutorialPositionX(
	TEXT("gg.TwoManTackleTutorialPositionX"),
	0.0f,
	TEXT("Position of the second supportive tackler in horizontal direction"),
	ECVF_Cheat);

static TAutoConsoleVariable<float> CVarTwoManTackleTutorialPositionY(
	TEXT("gg.TwoManTackleTutorialPositionY"),
	0.0f,
	TEXT("Position of the second supportive tackler in horizontal direction"),
	ECVF_Cheat);

static TAutoConsoleVariable<bool> CVarTwoManTackleTutorialEnable(
	TEXT("gg.TwoManTackleTutorialEnable"),
	0,
	TEXT("Enable second support tackler in tutorial demo"),
	ECVF_Cheat);
