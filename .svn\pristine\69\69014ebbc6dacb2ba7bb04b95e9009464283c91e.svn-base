/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "SIFMatchmakingHelpers.h"

#if PLATFORM_WINDOWS
#include "Mab/MabInclude.h"

#include "SIFUIHelpers.h"
#include "Match/RugbyUnion/RUDatabaseTypes.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUDBPlayer.h"
#include "Match/RugbyUnion/RUDBTeam.h"
#include "GameFramework/GameStateBase.h"

/*#rc3_legacy_include
#include <MabNetworkManager.h>
#include <MabNetOnlineClientDetails.h>

#ifdef ENABLE_STEAM
#include <SteamMabLobbyManager.h>
#include <SteamMabVoiceManager.h>
#else
#include <MabLanGameManager.h>
#endif

#include "SIFNetworkConstants.h"
*/

#ifdef ENABLE_STEAM
const int NUM_SLOTS_DEFAULT = NET_NUM_PLAYERS;
#endif

//const size_t MAX_SEARCH_RESULTS = 10;
//const char* MATCHMAKING_VERSION = "MMVer";
//const char* MATCHMAKING_GAMEMODE = "GameMode";

// Create a game based on the local computers name
#ifdef NOT_REQD
bool SIFMatchmakingHelpers::CreateGame(int /*controller_idx*/, bool private_match)
{
	SetMatchPrivateToGameSettings( private_match );

	/*#rc3_legacy_online
#ifdef ENABLE_STEAM
	return SIFApplication::GetApplication()->GetOnlineGameManager()->CreateLobby(
		private_match ? k_ELobbyTypeFriendsOnly : k_ELobbyTypeInvisible, NUM_SLOTS_DEFAULT);
#else
	MABUNUSED(private_match);
	SIFApplication::GetApplication()->GetOnlineGameManager()->HostGame(getenv("COMPUTERNAME"));
	return true;
#endif
	*/

	// Potentially the new flow - JG
	return StartGenericMatchmaking(private_match, EMatchmakingMode::Unranked);
}
#endif

#if defined ENABLE_STEAM
// Run after a lobby has been created, to do any settings
void SIFMatchmakingHelpers::PostCreateGame(const CSteamID& lobby_id)
{
	MABASSERT(lobby_id.IsValid());
	if(lobby_id.IsValid() == false) return;

	const char* game_mode_text = SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetGameMode() == GAME_MODE_RU13W ? "1" : "0"; // Nick  WWS 7s to Womens // SEVENS ? "1" : "0";

	SteamMatchmaking()->SetLobbyData(lobby_id, 
		MATCHMAKING_VERSION, SIFApplicationParameters::GetInstance()->build_number.c_str());
	SteamMatchmaking()->SetLobbyData(lobby_id, 
		MATCHMAKING_GAMEMODE, game_mode_text);
	SIFApplication::GetApplication()->GetVoiceManager()->SetRecording(true);
	SIFApplication::GetApplication()->GetApplicationParameters().disable_nclient_stuff = true;
}
#endif

#ifdef NOT_REQD
bool SIFMatchmakingHelpers::CreateGameFromUISettings( int controller_index, MabUINode* /*settings_object*/ )
{
	// This should really get settings from the UI Object, but for now is unimplemented.
	return CreateGame( controller_index, false );
}
#endif

#ifdef NOT_REQD
// Join the specified game
bool SIFMatchmakingHelpers::JoinGame(int /*controller_index*/, int index)
{
	/*#rc3_legacy_online
#ifdef ENABLE_STEAM
	return SIFApplication::GetApplication()->GetOnlineGameManager()->JoinLobby(index);
#else
	SIFApplication::GetApplication()->GetOnlineGameManager()->JoinGame(index);
	return true;
#endif
	*/
	return false;
}
#endif

#ifdef ENABLE_STEAM
// Set the initial lobby data
void SIFMatchmakingHelpers::PostJoinGame(const CSteamID& lobby_id)
{
	int game_mode = 0;
	MabStringHelper::ToInt(MabString(SteamMatchmaking()->GetLobbyData(lobby_id, MATCHMAKING_GAMEMODE)), game_mode);
	SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.SetGameMode(static_cast<GAME_MODE>(game_mode));
	SIFApplication::GetApplication()->GetVoiceManager()->SetRecording(true);
	SIFApplication::GetApplication()->GetApplicationParameters().disable_nclient_stuff = true;
}
#endif

#ifdef NOT_REQD
// Cancel the game if we're the host
void SIFMatchmakingHelpers::LeaveGame()
{
	/*#rc3_legacy_online
#ifdef ENABLE_STEAM
	SIFApplication::GetApplication()->GetVoiceManager()->SetRecording(false);
	SIFApplication::GetApplication()->GetOnlineGameManager()->LeaveLobby();
	SIFApplication::GetApplication()->GetNetworkManager()->LeaveGame();
	SIFApplication::GetApplication()->GetApplicationParameters().disable_nclient_stuff = false;
#else
	MabLanGameManager* manager = SIFApplication::GetApplication()->GetOnlineGameManager();
	if (manager->IsHost())
		manager->CancelGame();
#endif
	*/
}
#endif

#ifdef NOT_REQD
bool SIFMatchmakingHelpers::SearchForGame(int /*controller_idx*/)
{
	/*#rc3_legacy_online
#ifdef ENABLE_STEAM
	SteamMatchmaking()->AddRequestLobbyListStringFilter(MATCHMAKING_VERSION, 
		SIFApplicationParameters::GetInstance()->build_number.c_str(), k_ELobbyComparisonEqual);
	SteamMatchmaking()->AddRequestLobbyListNumericalFilter(MATCHMAKING_GAMEMODE, 
		static_cast<int>(SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetGameMode()), k_ELobbyComparisonEqual);
	SIFApplication::GetApplication()->GetOnlineGameManager()->SearchLobby(1, MAX_SEARCH_RESULTS);
	return true;
#else
	// Start searching for games
	SIFApplication::GetApplication()->GetOnlineGameManager()->SearchGames();
	return true;
#endif
	*/

	// Potentially the new flow - JG
	return StartGenericMatchmaking(false, EMatchmakingMode::Unranked);
}
#endif

#ifdef NOT_REQD
bool SIFMatchmakingHelpers::SearchForGameFromUISettings( int controller_index, MabUINode* /*settings_object*/ )
{
	// This should really get settings from the UI Object, but for now is unimplemented.
	return SearchForGame(controller_index);
}
#endif

//bool SIFMatchmakingHelpers::IsHost()
//{
//	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
//	
//	if (pRugbyGameInstance)
//	{
//		UWorld* pCurrentWorld = pRugbyGameInstance->GetWorld();
//
//		if (pCurrentWorld)
//		{
//			AGameStateBase* pRugbyGameState = pCurrentWorld->GetGameState();
//
//			if (pRugbyGameState)
//			{
//				return pRugbyGameState->HasAuthority();
//			}
//		}
//	}
//	// Are we the host?
//	//return SIFApplication::GetApplication()->GetOnlineGameManager()->IsHost();
//	return false;
//}

bool SIFMatchmakingHelpers::IsConnectedToNetwork()
{
#ifdef ENABLE_STEAM
	return SteamUser()->BLoggedOn();
#else
	return false;
#endif
}

#ifdef NOT_REQD
int SIFMatchmakingHelpers::GetFirstGoodResult()
{
#ifdef ENABLE_STEAM
	int result = -1;
	MABLOGDEBUG("SIFMatchmakingHelpers::GetFirstGoodResult:");

	// Steam searches are returned in order of nearest, to furthest.
	// This considered we should return the first valid lobby in the array.
	// ABROWN
	SteamMabLobbyManager* lobby_manager = SIFApplication::GetApplication()->GetOnlineGameManager();
	for (size_t i = 0; i < lobby_manager->GetNumSearchResults(); ++i)
	{
		CSteamID lobby_id;
		if(lobby_manager->GetSearchResult(i, lobby_id) && lobby_id.IsValid())
		{
			if(result < 0) result = i;
		}
		MABLOGDEBUG("    --Found: %d", i);
	}

	MABLOGDEBUG("    --Result: %d", result);
	return result;
#else
	// TODO Should use QoS data, but for now just returns 0.
	return 0;
#endif
}
#endif

#ifdef NOT_REQD
/// Determines the number of members for a game.
int SIFMatchmakingHelpers::GetNumberOfPlayers()
{
	size_t num_members = 0;

#ifdef ENABLE_STEAM
	const CSteamID& lobby = SIFApplication::GetApplication()->GetOnlineGameManager()->GetLobby();
	num_members = SteamMatchmaking()->GetNumLobbyMembers(lobby);
#endif

	return (int)num_members;
}
#endif


MabString SIFMatchmakingHelpers::GetPlayerHandle(int node_id, bool local_player)
{
	MabString player_name = "";

#ifdef ENABLE_STEAM
	MabNetworkManager* network_manager = SIFApplication::GetApplication()->GetNetworkManager();
	if(node_id == network_manager->GetLocalNodeId() || local_player)
		player_name = SteamFriends()->GetPersonaName();
	else
	{
		const MabNetOnlineClientDetails* client_details = network_manager->GetPeerInfo((MabNodeId)node_id);
		if(client_details != NULL)
		{
			MabUInt64 steam_id = client_details->GetDetails().local_address.steam_id;
			player_name = SteamFriends()->GetFriendPersonaName(steam_id);
		}
	}
#else
	// LLOYD TODO: Fill out the datas.
	MABUNUSED(node_id);
	MABUNUSED(local_player);
#endif	

	// The name may have escape characters, so we need to escape the string so it displays properly.
	player_name = SIFUIHelpers::EscapeText( player_name.c_str() );

	return player_name;
}

#ifdef ENABLE_STEAM
/// Sets the user's muted state (No voice impl for PC yet).
bool SIFMatchmakingHelpers::SetMuted( unsigned int /*ct_idx*/, const char* username, bool muted )
{
	SIFApplication* application = SIFApplication::GetApplication();

	// In order to mute a player, we must translate the username to a valid node ID.
	// If a valid node ID for that username can not be found, then we can't apply the mute.
	// Check muting of the other player.
	bool node_found = false;
	MabNodeId node_id = 0;
	MabVariant steam_id_string = username;
	CSteamID steam_id( static_cast<uint64>(steam_id_string.ToInt64()) );
	if ( SteamUser()->GetSteamID() ==  steam_id )
	{
		MabNetworkManager* network_manager = application->GetNetworkManager();
		node_id = network_manager->GetLocalNodeId();
		node_found = true;
	}
	else
	{
		MabNetworkManager* network_manager = application->GetNetworkManager();
		const MabNetOnlineClientDetails* client_details = network_manager->GetPeerInfo( 0 );
		if ( client_details && CSteamID(client_details->GetDetails().local_address.steam_id) == steam_id )
		{
			node_id = network_manager->GetPeerNodeId( 0 );
			node_found = true;
		}
	}

	// Only attempt to set the mute if we've found a valid node ID.
	if ( node_found )
	{
		application->GetVoiceManager()->SetMute( node_id, muted );
	}
	return node_found;
}

/// Gets the players muting state
bool SIFMatchmakingHelpers::GetMuted( unsigned int /*ct_idx*/, const char* username )
{
	SIFApplication* application = SIFApplication::GetApplication();

	// In order to get a mute state, we must translate the username to a valid node ID.
	// If a valid node ID for that username can not be found, then we can't apply the mute.
	// Check muting of the other player.
	bool node_found = false;
	MabNodeId node_id = 0;
	MabVariant steam_id_string = username;
	CSteamID steam_id( static_cast<uint64>(steam_id_string.ToInt64()) );
	if ( SteamUser()->GetSteamID() ==  steam_id )
	{
		MabNetworkManager* network_manager = application->GetNetworkManager();
		node_id = network_manager->GetLocalNodeId();
		node_found = true; 
	}
	else
	{
		MabNetworkManager* network_manager = application->GetNetworkManager();
		const MabNetOnlineClientDetails* client_details = network_manager->GetPeerInfo( 0 );
		if ( client_details && CSteamID(client_details->GetDetails().local_address.steam_id) == steam_id )
		{
			node_id = network_manager->GetPeerNodeId( 0 );
			node_found = true;
		}
	}

	// Only attempt to set the mute if we've found a valid node ID.
	if ( node_found )
	{
		return application->GetVoiceManager()->GetMute( node_id );
	}
	return false;
}

/// Returns whether there is and kind of invite pending - this is used to short circuit the 
/// press start screen, and kick us into the correct flow.
bool SIFMatchmakingHelpers::GetInvitePending()
{
	return SIFApplication::GetApplication()->GetOnlineGameManager()->GetLobbyIdToJoin().IsValid();
}

/// Joins the pending invite.
bool SIFMatchmakingHelpers::JoinInvitedGame()
{
	return SIFApplication::GetApplication()->GetOnlineGameManager()->JoinInvitedSession();
}

/// Launches the invite dialog.
void SIFMatchmakingHelpers::LaunchInviteDialog(int /*controller_idx*/)
{
	SteamFriends()->ActivateGameOverlay( "LobbyInvite" );
}

/// Returns the controller that was invited.
int SIFMatchmakingHelpers::GetInvitedController()
{
	//Not applicable on PC
	return -1;
}
/// Clears an invite that was pending. Could be because the user is underage.
void SIFMatchmakingHelpers::CancelPendingInvite()
{
	SIFApplication::GetApplication()->GetOnlineGameManager()->ClearPendingInvite();
}
#else
/// Stub function for PC without STEAM
bool SIFMatchmakingHelpers::GetInvitePending()
{
	return false;
}
#endif

#ifdef ENABLE_STEAM
#include "RUGameDatabaseManager.h"
// Check database is unchanged
bool SIFMatchmakingHelpers::DatabaseHasBeenModified()
{
	// Pre-hashed database info
	static const unsigned short ONLINE_PLAYER_STATS_FIFTEENS[NUM_PLAYERS_PER_TEAM_INIT][5] =
	{
		{2900, 3500, 7600, 2500, 3400},
		{5400, 4500, 8400, 4300, 3400},
		{5000, 4300, 5600, 5700, 3700},
		{7100, 6700, 9000, 8400, 6000},
		{5100, 3700, 4600, 6600, 5400},

		{7900, 7900, 8100, 8700, 7300},
		{6100, 6900, 8200, 8000, 4500},
		{5200, 4300, 8600, 8400, 6200},
		{7000, 6500, 5700, 6600, 8600},
		{7100, 6600, 7700, 8100, 7100},

		{8100, 7700, 8600, 8700, 6600},
		{7300, 7200, 7000, 7700, 7000},
		{6300, 3100, 5100, 8900, 6000},
		{8600, 7700, 9100, 7200, 6600},
		{8800, 9100, 8800, 7900, 7300}
	};

	static const unsigned short ONLINE_PLAYER_STATS_SEVENS[NUM_PLAYERS_PER_TEAM_INIT][5] =
	{
		{7150, 6350, 8100, 4300, 6800},
		{6700, 7450, 8100, 8100, 6350},
		{8750, 8300, 7350, 9200, 6800},
		{8100, 7350, 6350, 7450, 7550},
		{9850, 9950, 7350, 4850, 6900},

		{9050, 7000, 6250, 8950, 6600},
		{8750, 8950, 6800, 8950, 7650},
		{6800, 6900, 7900, 8850, 5850},
		{7900, 7450, 4550, 6250, 8100},
		{9850, 8200, 7550, 5400, 6900},

		{7350, 7550, 6800, 5600, 6350},
		{7450, 8100, 9300, 7800, 8550},
		{8200, 9050, 9300, 5300, 7450},
		{6050, 7250, 6900, 7450, 7650},
		{6900, 6700, 6050, 7000, 6900}
	};

	RUGameDatabaseManager *database = SIFApplication::GetApplication()->GetGameDatabaseManager();
	RUDB_TEAM online_team_fifteens;
	MABVERIFYMSG(database->LoadData(online_team_fifteens, NETWORK_STATS_FIFTEENS_TEAM_ID), "Failed to load RUDB_TEAM");

	RUDB_TEAM online_team_sevens;
	MABVERIFYMSG(database->LoadData(online_team_sevens, NETWORK_STATS_SEVENS_TEAM_ID), "Failed to load RUDB_TEAM");
	
	// Check player data
	for (int i=0; i<NUM_PLAYERS_PER_TEAM_INIT; ++i)
	{
		RUDB_PLAYER online_player;
		MABVERIFYMSG(database->LoadData(online_player, online_team_fifteens.GetLineup(i).player_id), "Failed to load RUDB_PLAYER");

		if( ONLINE_PLAYER_STATS_FIFTEENS[i][0] != online_player.speed ||
			ONLINE_PLAYER_STATS_FIFTEENS[i][1] != online_player.acceleration ||
			ONLINE_PLAYER_STATS_FIFTEENS[i][2] != online_player.break_tackle_ability ||
			ONLINE_PLAYER_STATS_FIFTEENS[i][3] != online_player.tackle_ability ||
			ONLINE_PLAYER_STATS_FIFTEENS[i][4] != online_player.pass_accuracy )
			return true;

 		/*MABLOGDEBUG("{%u, %u, %u, %u, %u},",
 			online_player.speed,
 			online_player.acceleration,
 			online_player.break_tackle_ability,
 			online_player.tackle_ability,
 			online_player.pass_accuracy);*/
	}

	// Check player data
	for (int i=0; i<NUM_PLAYERS_PER_TEAM_INIT; ++i)
	{
		RUDB_PLAYER online_player;
		MABVERIFYMSG(database->LoadData(online_player, online_team_sevens.GetLineup(i).player_id), "Failed to load RUDB_PLAYER");

		if( ONLINE_PLAYER_STATS_SEVENS[i][0] != online_player.speed ||
			ONLINE_PLAYER_STATS_SEVENS[i][1] != online_player.acceleration ||
			ONLINE_PLAYER_STATS_SEVENS[i][2] != online_player.break_tackle_ability ||
			ONLINE_PLAYER_STATS_SEVENS[i][3] != online_player.tackle_ability ||
			ONLINE_PLAYER_STATS_SEVENS[i][4] != online_player.pass_accuracy )
			return true;

 		/*MABLOGDEBUG("{%u, %u, %u, %u, %u},",
 			online_player.speed,
 			online_player.acceleration,
 			online_player.break_tackle_ability,
 			online_player.tackle_ability,
 			online_player.pass_accuracy);*/
	}

	return false;
}

// Gets the players unique steam ID.
MabString SIFMatchmakingHelpers::GetCurrentSteamId()
{
	return MabString(0, "%llu", SteamUser()->GetSteamID().ConvertToUint64());
}

/// Checks if invite is to the local session
bool SIFMatchmakingHelpers::IsInviteToLocalSession()
{
	SteamMabLobbyManager* lobby_manager = SIFApplication::GetApplication()->GetOnlineGameManager();
	return lobby_manager->GetLobby() == lobby_manager->GetLobbyIdToJoin();
}

#endif
#endif