/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseRuck.h"

#include "Match/AI/Actions/RUActionTacklee.h"
#include "Match/AI/Actions/RUActionTackler.h"
#include "Match/AI/Formations/SSEVDSFormationConstants.h"
#include "Match/AI/Formations/SSEVDSFormationEnum.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuck.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuckScrumHalf.h"
#include "Match/Ball/SSBall.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/RUPlayerState.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Debug/RUGameDebugSettings.h"
#include "Match/HUD/RU3DHUDManager.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameMovement.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURulesDebugSettings.h"
#include "Match/RugbyUnion/TutorialMode/RUTutorialManager.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSGameTimer.h"
#include "Match/SSMath.h"
#include "Match/SSPlayerFilter.h"
#include "Match/SSSpatialHelper.h"
#include "Utility/RURandomNumberGenerator.h"

//#rc3_legacy_include #include "NMMabAnimationNetwork.h"
//#rc3_legacy_include #include "RURuckHUD.h"
#include "Match/Debug/SIFDebug.h"
//#rc3_legacy_include #include "SIFMenuHelpers.h"
//#rc3_legacy_include #include "SIFWindowSystem.h"

#include "Character/RugbyCharacter.h"
#include "RugbyGameInstance.h"
#include "Match/HUD/RU3DHUDManager.h"
#include "Match/HUD/Marking/RUSetPieceIndicator.h"
#ifdef ENABLE_PRO_MODE
#include "Match/SSRoleFactory.h"
#include "Match/AI/Roles/Competitors/SSRoleFormation.h"
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#endif
#include "Match/SSTeam.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Match/SSStreamPacker.h"

#ifdef ENABLE_DEBUG_KEYS
//#include "MabControlActionManager.h"
#endif

MABRUNTIMETYPE_IMP1( RUGamePhaseRuck, RUGamePhaseHandler );

static const char* RUCK_ATTACK_FORMATION_NAME  = "Ruck: Attack";
static const char* RUCK_DEFENCE_FORMATION_NAME = "Ruck: Defence";

//#define ENABLE_VERBOSE_RUCK_DEBUG

//#define ENABLE_DEF_TEAM_WIN_RUCKS
//#ifdef ENABLE_HOME_TEAM_ALWAYS_WIN_RUCKS
//#define ENABLE_SETPLAY_DEBUG

#define DISABLE_RUCKHUD

// JOE: Ismael has added this to disable AI	ruck. It is a debug option in the menu - To be deleted at the end
bool disable_ai_ruck = false;

const float RUCK_USE_IT_TIME = 5.0f;	// Rules change, Ref calls "Use It" and the ball has to be played within 5 seconds.
const static float BALL_TRAVEL_LINE_OFFSET = 2.5f; // Maximum distance the travel line of the ball is allowed to be to the sideline.

#define FULLBACK_MODE_TOGGLE_PERCENT 35.0

#ifdef ENABLE_OSD
#define LOGRUCKDEBUG( stmt ) SIFDebug::GetGameDebugSettings()->PushDebugString( game, RUGameDebugSettings::DP_BREAKDOWN, stmt );
#else
#define LOGRUCKDEBUG( stmt )
#endif

//----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
//----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

constexpr const RUZoneLocationBindState BOUND_BIND_STATES[] = {
			{ "", "1" },
			{ "1", "23" },
			{ "12", "3" },
			{ "13", "2" },
			{ "123", "4" },
			{ "1234", "56" },
			{ "12345", "6" },
			{ "12346", "5" },
			{ "123456", "78" },
			{ "1234567", "8" },
			{ "1234568", "7" },
			{ "12345678", "" }
};


constexpr const RUZoneLocationBindState BOUND_BIND_STATES_R7[] =
{
	{ "", "1" },
	{ "1", "2" },
	{ "2", "1" },
	{ "12", "" },
	{ "1", "" },
	{ "2", "" },
};

constexpr const RUZoneLocationType BOUND_LOCATION_RANK[] = { RUCKLOC_HALFBACK, RUCKLOC_BOUND, RUCKLOC_PREJOIN, ZONELOC_NULL };

constexpr const RUZoneLocationBindState HALFBACK_BIND_STATES[] =
{
	{ "", "1" },
	{ "1", "" }
};

constexpr const RUZoneLocationType HALFBACK_LOCATION_RANK[] = { ZONELOC_NULL };

constexpr const RUZoneLocationBindState PREJOIN_BIND_STATES[] = {
	{ "", "34" },
	{ "3", "24" },
	{ "4", "35" },
	{ "23", "4" },
	{ "34", "25" },
	{ "45", "3" },
	{ "234", "5" },
	{ "345", "2" },
	{ "2345", "16" },
	{ "23456", "1" },
	{ "12345", "6" },
	{ "23456", "1" },
	{ "123456", "" },

	// These do not allow joins but permit unbinds to happen in a slightly more random order
	{ "2345", "" }
};

constexpr const RUZoneLocationBindState PREJOIN_BIND_STATES_R7[] = {
	{ "", "12" },
	{ "1", "2" },
	{ "2", "1" },
	{ "12", "" },
	{ "21", "" },
};

//----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
//----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

///-------------------------------------------------------------------------
/// Constructor
///-------------------------------------------------------------------------

RUGamePhaseRuck::RUGamePhaseRuck(SIFGameWorld *ggame)
: game(ggame)
, setplay_time(0.0f)
, setplay_state(RSP_STATE_NONE)
, slow_ruck_event_fired(false)
, current_ruck_winner( NULL )
, ruck_states()
, curr_state(0)
, next_ruck_index(0)
, power_contest()
, current_time_step(MabTime(0),MabTime(0),0)
, ruck_hud( NULL )
, n_debug_text(0)
, n_debug_lines(0)
{
	// Construct ruck states for each team
	ruck_states.reserve( MAX_CONCURRENT_RUCKS );
	ruck_states.clear();
	for( size_t i = 0; i < MAX_CONCURRENT_RUCKS; i++ )
	{
		ruck_states.push_back(std::make_unique<RURuckState>());
		ruck_states_ptr.push_back(ruck_states.back().get());
		//#rc3_legacy ruck_states.push_back( game->GetObjectDatabase()->Allocate<RURuckState>() );
	}

	RUGameEvents* events = game->GetEvents();
	events->breakdown_start.Add(this, &RUGamePhaseRuck::OnBreakdownStart);
	events->breakdown_end.Add(this, &RUGamePhaseRuck::OnBreakdownEnd);
	events->ruck_player_joined.Add( this, &RUGamePhaseRuck::OnRuckPlayerJoined );
	events->ruck_player_left.Add( this, &RUGamePhaseRuck::OnRuckPlayerLeft );
	events->team_assignments_changed.Add( this, &RUGamePhaseRuck::OnTeamAssignmentsChanged );
	events->ruck_scrum_ball_picked_up.Add( this, &RUGamePhaseRuck::OnBallPickedupFromRuck );
	events->half_time.Add(this, &RUGamePhaseRuck::HalfOver);
	events->full_time.Add(this, &RUGamePhaseRuck::HalfOver);
	events->phase_changed.Add(this, &RUGamePhaseRuck::OnPhaseChanged );
	events->breakdown_player_contesting_ball.Add(this, &RUGamePhaseRuck::OnPlayerContestingBall);
}

///-------------------------------------------------------------------------
/// Destructor
///-------------------------------------------------------------------------

RUGamePhaseRuck::~RUGamePhaseRuck()
{
	RUGameEvents* events = game->GetEvents();
	events->breakdown_start.Remove(this, &RUGamePhaseRuck::OnBreakdownStart);
	events->breakdown_end.Remove(this, &RUGamePhaseRuck::OnBreakdownEnd);
	events->ruck_player_joined.Remove( this, &RUGamePhaseRuck::OnRuckPlayerJoined );
	events->ruck_player_left.Remove( this, &RUGamePhaseRuck::OnRuckPlayerLeft );
	events->team_assignments_changed.Remove( this, &RUGamePhaseRuck::OnTeamAssignmentsChanged );
	events->ruck_scrum_ball_picked_up.Remove( this, &RUGamePhaseRuck::OnBallPickedupFromRuck );
	events->half_time.Remove(this, &RUGamePhaseRuck::HalfOver);
	events->full_time.Remove(this, &RUGamePhaseRuck::HalfOver);
	events->phase_changed.Remove(this, &RUGamePhaseRuck::OnPhaseChanged );
	events->breakdown_player_contesting_ball.Remove(this, &RUGamePhaseRuck::OnPlayerContestingBall);

	//#rc3_legacy
	//for( size_t i = 0; i < MAX_CONCURRENT_RUCKS; i++ )
	//	game->GetObjectDatabase()->Destroy<RURuckState>( ruck_states[i] );
}


///-------------------------------------------------------------------------
/// Event handler 'half_time' + 'full_time'.
///-------------------------------------------------------------------------

void RUGamePhaseRuck::HalfOver()
{
	if ( game->GetGameState()->GetPhaseHandler( RUGamePhase::RUCK ) != NULL )
		game->GetGameState()->GetPhaseHandler( RUGamePhase::RUCK )->Reset();
}


#ifdef BUILD_DEBUG
const char* RUGamePhaseRuck::GetRuckJoinString( RUZoneJoinType join_type )
{
	static const char* STATE_STR[] = {
		"RUCK_JOIN_NONE",
		"RUCK_JOIN_NORMAL",
		"RUCK_JOIN_AGGRESSIVE",
	};

	return STATE_STR[ join_type ];
}
#endif

///-------------------------------------------------------------------------
/// Have just changed to this phase
///-------------------------------------------------------------------------

RURuckTeamState& RUGamePhaseRuck::GetCurrentAttackingTeamState()
{
	MABASSERT( curr_state >= 0 && curr_state < (int) MAX_CONCURRENT_RUCKS );
	return ruck_states[curr_state]->attacking;
}

RURuckTeamState& RUGamePhaseRuck::GetCurrentDefendingTeamState()
{
	MABASSERT( curr_state >= 0 && curr_state < (int) MAX_CONCURRENT_RUCKS );
	return ruck_states[curr_state]->defending;
}


int RUGamePhaseRuck::GetNextRuck()
{

	/// Find the next available ruck state entry
	int new_state = -1;
	int state_idx = curr_state;
	for( size_t i = 0; i < ruck_states.size(); i++, state_idx = (state_idx+1) % ruck_states.size() )
	{
		if ( !ruck_states[i]->running ) {
			new_state = (int)i;
			break;
		}
	}

	MABASSERT( new_state >= 0 );

	MABLOGDEBUG(MabString(0, "RUGamePhaseRuck::GetNextRuck - %d", new_state).c_str());

	return new_state;
}

void RUGamePhaseRuck::StartNewRuck(int new_ruck_index)
{
	MABLOGDEBUG(MabString(0, "RUGamePhaseRuck::StartNewRuck - %d", new_ruck_index).c_str());
	if (new_ruck_index < 0)
	{
		MABASSERTMSG(new_ruck_index < 0, "InValid next_ruck_index. That could be because previous rucks are still running and not cleared.");
		return;
	}

	ruck_states[new_ruck_index]->Enter( game, new_ruck_index );
	curr_state = new_ruck_index;
	current_ruck_winner = NULL;
	attack_runners.clear();

	ruck_states[new_ruck_index]->attacking.ruck_state = ruck_states[curr_state].get();
	ruck_states[new_ruck_index]->defending.ruck_state = ruck_states[curr_state].get();

	// When we're playing a pro career game, this will make sure that our pro player doesn't get 'sucked' into this set play.
	// If they want to participate, they need to press A/B to join, or X to become scrum ruck half
	// The only Time this is ignored, is if they are actually playing as a scrum ruck half.
	RUCareerModeManager* proModeMan = SIFApplication::GetApplication()->GetCareerModeManager();
	bool isProMode = proModeMan->IsActive() && proModeMan->GetIsCareerModePro();
	if(isProMode)
	{
		ARugbyCharacter* pro_player = SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer();
		if(pro_player != NULL)
		{
			if( pro_player->GetAttributes()->GetPlayerPosition() & PP_SCRUM_HALF)
			{
				MABLOGDEBUG("Our pro player is playing as as scrum ruck half!");
			}
			else
			{
				MABLOGDEBUG("Our pro player is not playing as a scrum ruck half");
				pro_player->GetAttributes()->nextIgnoredRole = RURoleRuckScrumHalf::RTTGetStaticType();
			}
		}
	}
}

float RUGamePhaseRuck::GetCurrentRuckGateZ( ERugbyPlayDirection play_dir )
{
	return GetCurrentRuck().GetGateOrigin( play_dir )->GetOrigin().z;
}

#ifdef ENABLE_PRO_MODE
// Checks to see if our pro was first in the ruck
bool RUGamePhaseRuck::GetIsProFirstInRuck()
{
	RUCareerModeManager* careerManager = SIFApplication::GetApplication()->GetCareerModeManager();

	// Can't be in the ruck if we're not playing pro match
	if(!careerManager->IsActive() || !careerManager->GetIsCareerModePro())
		return false;

	// If our pro player is in the attacking team, then check if they are the breakdown holder
	if(GetCurrentRuck().attacking.team->GetProPlayer() != NULL)
	{
		if(careerManager->IsProPlayer(GetCurrentRuck().breakdown_holder))
			return true;
	}
	// If our pro player is in the defending team, check if they are a tackler
	else if(GetCurrentRuck().defending.team->GetProPlayer() != NULL)
	{
		if(GetCurrentRuck().tacklers.size() > 0)
		{
			for(SIFRugbyCharacterList::const_iterator i = GetCurrentRuck().tacklers.begin(); i != GetCurrentRuck().tacklers.end(); ++i)
			{
				if(careerManager->IsProPlayer((*i)))
					return true;
			}
		}
	}

	return false;
}

// Just checks if the pro is currently in the ruck
bool RUGamePhaseRuck::GetIsProInRuck()
{
	RUCareerModeManager* careerManager = SIFApplication::GetApplication()->GetCareerModeManager();

	// Can't be in the ruck if we're not playing pro match
	if(!careerManager->IsActive() || !careerManager->GetIsCareerModePro())
		return false;

	// HES #12868, in the event that a player gets tackled out as the clock goes into full time, the rucks may not be set up.
	if( GetCurrentRuck().attacking.team == NULL )
		return false;

	const RuckCommital& attackingCommital = GetCurrentRuck().attacking.commital;
	const RuckCommital& defendingCommital = GetCurrentRuck().defending.commital;

	// Quick check to see if the pro is in the attacking team
	if(GetCurrentRuck().attacking.team->GetProPlayer() != NULL)
	{
		// If this was the formation of the ruck, then the first place our pro can be in would be the ballholder
		if(careerManager->IsProPlayer(GetCurrentRuck().breakdown_holder))
			return true;

		// Check all the currently bound players in the attacking side of the ruck
		for(SIFRugbyCharacterList::const_iterator i = attackingCommital.bound.begin(); i != attackingCommital.bound.end(); ++i)
		{
			// This is our pro, early out now.
			if(careerManager->IsProPlayer((*i)))
				return true;
		}
	}

	// Quick check to see if the pro is in the defending team
	else if(GetCurrentRuck().defending.team->GetProPlayer() != NULL)
	{
		// If this was the formation of the ruck, then the first place our pro can be in would be a tackler
		if(GetCurrentRuck().tacklers.size() > 0)
		{
			for(SIFRugbyCharacterList::const_iterator i = GetCurrentRuck().tacklers.begin(); i != GetCurrentRuck().tacklers.end(); ++i)
			{
				if(careerManager->IsProPlayer((*i)))
					return true;
			}
		}

		// Now check the defending team commital
		for(SIFRugbyCharacterList::const_iterator i = defendingCommital.bound.begin(); i != defendingCommital.bound.end(); ++i)
		{
			// This is our pro, early out now.
			if(careerManager->IsProPlayer((*i)))
				return true;
		}
	}

	return false;

	//return isProInRuck;

	//bool tackleOrTacklee = false;
	// Only do the check if we're in a pro match
	/*if(careerManager->IsActive() && careerManager->GetIsCareerModePro())
	{
		// If our pro was a tackler, check if one of the tacklers was our pro player
		if(GetCurrentRuck().tacklers.size() > 0)
		{
			for(SIFRugbyCharacterList::const_iterator i = GetCurrentRuck().tacklers.begin(); i != GetCurrentRuck().tacklers.end(); ++i)
			{
				if(careerManager->IsProPlayer((*i)))
				{
					tackleOrTacklee = true;
					break;
				}
			}
		}

		if(GetCurrentRuck().breakdown_holder != NULL && careerManager->IsProPlayer(GetCurrentRuck().breakdown_holder))
			tackleOrTacklee = true;
	}
	*/
	//return tackleOrTacklee;
}
#endif

bool RUGamePhaseRuck::GetCanAttackingTeamContest()
{
	// Cannot contest when the ruch has been formed
	if(GetCurrentRuck().valid_ruck_formed_time != MabTime(0))
		return false;

	// For the time being, just check if the ruck has been formed, if so, then we cannot contest. Later we'll do more specific checks if need be.
	return true;

	// We can only contest the ball when we have the initial tacklers
	//RURuckTeamState& attacking_team = GetCurrentAttackingTeamState();
	//return attacking_team.GetCommittedPlayerCount() <= 2;


	// Cannot contest when the ruch has been formed
	//if(GetCurrentRuck().valid_ruck_formed_time != MabTime(0))
		//return false;
}

bool RUGamePhaseRuck::GetCanDefendingTeamContest()
{
	// Cannot contest when the ruch has been formed
	if(GetCurrentRuck().valid_ruck_formed_time != MabTime(0))
		return false;
	// For the time being, just check if the ruck has been formed, if so, then we cannot contest. Later we'll do more specific checks if need be.
	return true;


	/*// Cannot contest when the ruch has been formed
	if(GetCurrentRuck().valid_ruck_formed_time != MabTime(0))
		return false;

	// We can only contest the ball when we have the initial tacklers
	RURuckTeamState& attacking_team = GetCurrentAttackingTeamState();
	return attacking_team.GetCommittedPlayerCount() <= 2;*/


	// Only contest if the attacking team has 1 player joined
	//RURuckTeamState& attacking_team = GetCurrentAttackingTeamState();
	//return attacking_team.GetCommittedPlayerCount() <= 2;
}

int GetImportanceBand( float importance )
{
	if ( importance <= 0.5f )
		return 0;
	if( importance <= 0.7f )
		return 1;

	return 2;
}

void RUGamePhaseRuck::Enter()
{
	//MABLOGDEBUG("**************** RUGamePhaseRuck::Enter");
	ruck_use_it_timer.Initialise( game->GetSimTime(), RUCK_USE_IT_TIME );
	ruck_use_it_timer.SetEnabled( false );

	StartNewRuck(next_ruck_index);

	if (!ruck_states[curr_state]->running)
	{
		setplay_state = RSP_STATE_FINISHED;
		Exit();
		MABBREAKMSG("RUGamePhaseRuck Entered but no BreakdownStart event occured. Phase aborting.");
		return;
	}

	setplay_state = RSP_STATE_INITIAL;

	// Reset the last pass direction so passes are not biased when we start a new ruck
	game->GetGameState()->SetLastPassDirection( 0.0f );

	setplay_time = 0.0f;
	slow_ruck_event_fired = false;

	// Init teams
	RURuckTeamState& attacking_team = GetCurrentAttackingTeamState();
	RURuckTeamState& defending_team = GetCurrentDefendingTeamState();

	attacking_team.team = game->GetGameState()->GetAttackingTeam();
	defending_team.team = game->GetGameState()->GetDefendingTeam();
	attacking_team.ruck = this;
	defending_team.ruck = this;

	// Init the ruck configuration
	ApplyDefaultRuckConfiguration( game, GetCurrentRuck(), attacking_team, true );
	ApplyDefaultRuckConfiguration( game, GetCurrentRuck(), defending_team, false );

	// Set formation targets
	RUGameState* game_state = game->GetGameState();
	game_state->SetFormationTarget( ERugbyFormationTarget::RUCK_CENTER,		attacking_team.ruck_state->GetRuckOrigin() );
	game_state->SetFormationTarget( ERugbyFormationTarget::ATTACKLASTFEET,	attacking_team.GetGateOrigin() );
	game_state->SetFormationTarget( ERugbyFormationTarget::DEFENCELASTFEET,	defending_team.GetGateOrigin() );

	GetCurrentRuck().UpdateGate( 0.0f );

	// Re-init power bar, might change due to power decay
	InitPowerContest();

	/// By default add one normal request for each ruck
	InitRuckAI(attacking_team);
	InitRuckAI(defending_team);

	/// Setup default numbers of players for ruck commital areas
	MABLOGDEBUG("Setting commital count for attacking forwards to: %i", (int)attacking_team.team->GetStrategy().GetRuckCommittalCount());
	MABLOGDEBUG("Setting commital count for defending forwards to: %i", (int)defending_team.team->GetStrategy().GetRuckCommittalCount());
	attacking_team.team->GetFormationManager()->OverrideAreaNumPlayers( RUCK_ZONE_NAME, 1/*(int)attacking_team.team->GetStrategy().GetRuckCommittalCount()*/, ERugbyFormationRole::RUCK, RUCK_ATTACK_FORMATION_NAME );
	defending_team.team->GetFormationManager()->OverrideAreaNumPlayers( RUCK_ZONE_NAME, (int)defending_team.team->GetStrategy().GetRuckCommittalCount(), ERugbyFormationRole::RUCK, RUCK_DEFENCE_FORMATION_NAME );

	game->GetEvents()->ruck_formed( GetCurrentRuck().breakdown_holder, &GetCurrentRuck().tacklers );

	//Offensive team sometimes will bring the fullback into line.
	float rand = game->GetRNG()->RAND_RANGED_CALL(float, 100);
	if (rand <= FULLBACK_MODE_TOGGLE_PERCENT)
	{
		RUTeam * offensive_team = game->GetGameState()->GetAttackingTeam();
		if (offensive_team)
		{
			offensive_team->GetStrategy().CycleFullbackOption();
		}
	}


#ifdef ENABLE_PRO_MODE
	RUCareerModeManager* proModeMan = SIFApplication::GetApplication()->GetCareerModeManager();
	bool isProMode = proModeMan->IsActive() && proModeMan->GetIsCareerModePro();
	// Check if our pro enters the ruck during pro play
	if(isProMode)
		SetRuckHumanPlayersAfterProJoinOrLeave();
#endif
}

// We could change some values here for pro mode to wait on the player to join first? - Dewald WW
// We should probably check if the player *should* join, are they in a position to join the ruck?
// if not, make the AI join quicker.
// e.g. if the pro player is a full back we may let the AI join quicker, if the pro player is a loose prop,
// we'll expect them to join first (keep 1 spot free for x seconds) before the AI taking over.
float RUGamePhaseRuck::GetNextAIJoinTime( bool is_secondary_add )
{
	/// Min/max AI join times
	const static float AI_JOIN_TIME[][2] = { { 0.5f, 1.0f }, { 0.3f, 0.5f }, { 0.13f, 0.23f }, { 0.1f, 0.16f }, {0.0f, 0.05f } };
	const static float AI_SECOND_JOIN_TIME_ADD[] = { 1.5f, 1.0f, 0.65f, 0.3f, 0.08f };
	int n_join_times = sizeof( AI_JOIN_TIME ) / (sizeof( float ) * 2);

	int difficulty = game->GetGameSettings().difficulty;
	MABVERIFY( difficulty < n_join_times );

	float min = AI_JOIN_TIME[ difficulty ][0];
	float max = AI_JOIN_TIME[ difficulty ][1];

	float next_join_time = min + game->GetRNG()->RAND_RANGED_CALL( float, max - min );

	if ( is_secondary_add )
		next_join_time += AI_SECOND_JOIN_TIME_ADD[ difficulty ];

	return next_join_time;
}

float RUGamePhaseRuck::GetNextAIContestTime()
{
	/// Min/max AI contest times
	const static float AI_CONTEST_TIME[][2] = { { 0.5f, 1.0f }, { 0.3f, 0.5f }, { 0.13f, 0.23f }, { 0.1f, 0.16f }, {0.0f, 0.05f } };
	int n_contest_times = sizeof( AI_CONTEST_TIME ) / (sizeof( float ) * 2);

	int difficulty = game->GetGameSettings().difficulty;
	MABVERIFY( difficulty < n_contest_times );

	float min = AI_CONTEST_TIME[ difficulty ][0];
	float max = AI_CONTEST_TIME[ difficulty ][1];

	float next_contest_time = min + game->GetRNG()->RAND_RANGED_CALL( float, max - min );

	return next_contest_time;
}

RURuckState& RUGamePhaseRuck::GetCurrentRuck()
{
	return *ruck_states[ curr_state ];
}

void RUGamePhaseRuck::OnBreakdownStart(ARugbyCharacter* breakdown_holder )
{
	MABLOGDEBUG("**************** RUGamePhaseRuck::OnBreakdownStart: %d", breakdown_holder->GetAttributes()->GetIndex() );

	next_ruck_index = GetNextRuck();

	if (next_ruck_index < 0)
	{
		MABASSERTMSG(next_ruck_index < 0, "InValid next_ruck_index in RUGamePhaseRuck::OnBreakdownStart ");
		return;
	}


	RURuckTeamState& attacking_team = ruck_states[next_ruck_index]->attacking;
	RURuckTeamState& defending_team = ruck_states[next_ruck_index]->defending;
	RURuckState& ruck_state = *ruck_states[ next_ruck_index ];

	attacking_team.ruck_state = ruck_states[next_ruck_index].get();
	defending_team.ruck_state = ruck_states[next_ruck_index].get();

	/// Find out if the current holder is part of a tackle situation - if
	RUActionManager* action_manager = breakdown_holder->GetActionManager();
	SIFRugbyCharacterList tacklers;
	if ( action_manager->IsActionRunning( ACTION_TACKLEE ) )
	{
		RUActionTacklee* tacklee_action = action_manager->GetAction<RUActionTacklee>();
		RUTackleResult& tackle_result = tacklee_action->GetTackleResult();
		for( int i = 0; i < tackle_result.n_tacklers; i++ )
			if (i < MAX_TACKLERS)
				tacklers.push_back( tackle_result.tacklers[i] );
	}

	/// Notify tacklers and tacklee that they are part of a ruck
	ruck_state.breakdown_holder = breakdown_holder;

	if ( ruck_state.breakdown_holder->GetActionManager()->IsActionRunning( ACTION_TACKLEE ) )
	{
		ruck_state.breakdown_holder->GetActionManager()->GetAction<RUActionTacklee>()->SetRuckTeamState( &attacking_team );
		ruck_state.tacklers = tacklers;
		for( size_t i = 0; i < ruck_state.tacklers.size(); ++i )
			ruck_state.tacklers[i]->GetActionManager()->GetAction<RUActionTackler>()->SetRuckTeamState( &defending_team );
	}

	/// Add these players to the ruck
	attacking_team.commital.Clear();
	attacking_team.commital.Reset();
	defending_team.commital.Clear();
	defending_team.commital.Reset();

#ifdef ENABLE_PRO_MODE
	RUCareerModeManager* proModeMan = SIFApplication::GetApplication()->GetCareerModeManager();
	bool isProMode = proModeMan->IsActive() && proModeMan->GetIsCareerModePro();
	// Check if our pro enters the ruck during pro play
	if(isProMode)
	{
		if(proModeMan->IsProPlayer(breakdown_holder))
		{
			MABLOGDEBUG("Our pro just entered the ruck! (he was the tacklee)");
			isProInRuck = true;
			//game->GetEvents()->ruck_pro_joined_ruck(breakdown_holder);
		}


		for( size_t i = 0; i < ruck_state.tacklers.size(); ++i )
		{
			if(isProInRuck) break;
			if( proModeMan->IsProPlayer(ruck_state.tacklers[i]) )
			{
				MABLOGDEBUG("Our pro just entered the ruck (he was a tackler)!");
				isProInRuck = true;
				//game->GetEvents()->ruck_pro_joined_ruck(ruck_state.tacklers[i]);
			}
		}
	}
#endif
}

void RUGamePhaseRuck::OnBreakdownEnd( ARugbyCharacter* /*breakdown_holder*/ )
{
	MABLOGDEBUG("**************** RUGamePhaseRuck::BreakdownEnd");
	//AbortRuck();
}

void RUGamePhaseRuck::UpdateStateInitial()
{
	/// We don't want our formation to change during the ruck so lock it in
	//game->GetGameState()->GetAttackingTeam()->GetFormationManager()->DisableFormationChangeDuringPhase();
	//game->GetGameState()->GetDefendingTeam()->GetFormationManager()->DisableFormationChangeDuringPhase();

	UpdatePreJoin();

	// wait fro scrum halfs to be assigned
	SIFRugbyCharacterList halfback_players;
	game->GetStrategyHelper()->GetTeamRoles( game->GetGameState()->GetAttackingTeam(), RURoleRuckScrumHalf::RTTGetStaticType(), halfback_players );
	game->GetStrategyHelper()->GetTeamRoles( game->GetGameState()->GetDefendingTeam(), RURoleRuckScrumHalf::RTTGetStaticType(), halfback_players );

	int ruck_halfs = 0;
	// find ruckhalfs only from my ruck
	for (ARugbyCharacter* halfback_player : halfback_players)
		if (curr_state == halfback_player->GetRole<RURoleRuckScrumHalf>()->GetRuckIndex())
			ruck_halfs++;

	MABASSERTMSG(ruck_halfs <= 2, "Too many RuckScrumHalfs for this Ruck o_O");

	if (ruck_halfs >= 2)
	{
		setplay_state = RSP_STATE_RUNNING;

		SetRuckHumanPlayers();

		// Init HUD state
		SetUpDefaultHUD( GetCurrentRuck() );

		game->GetEvents()->ruck_start(curr_state);
	}
}

bool RUGamePhaseRuck::UpdatePreJoin()
{
	/// Start the pre-joining behaviour
	RURuckTeamState *team_ruck_states [2] = { &GetCurrentAttackingTeamState(), &GetCurrentDefendingTeamState() };
	int added = 0;
	RUZonePosition* returned_entry;
	ARugbyCharacter* returned_player;

	for( int i = 0; i < 2; i++ ) {
		RURuckTeamState* ruck_state = team_ruck_states[i];

		if ( (disable_ai_ruck && ruck_state->team->GetHumanPlayer(0) == NULL) || !ruck_state->ruck_state )
			continue;

		SIFRugbyCharacterList halfback_players;
		game->GetStrategyHelper()->GetTeamRoles( ruck_state->team, RURoleRuckScrumHalf::RTTGetStaticType(), halfback_players );
		if (ruck_state->locations[ RUCKLOC_HALFBACK ].GetBestJoinOption( halfback_players, returned_entry, returned_player, RUCK_JOIN_NONE ) )
		{
			if (returned_player->GetRole<RURoleRuckScrumHalf>()->GetRuckIndex() == -1)
			{
				//MABLOGDEBUG( "RUGamePhaseRuck::UpdatePreJoin: scrumhalf(%d)", returned_player->GetAttributes()->GetIndex() );
				UE_LOG(LogTemp, Display, TEXT("RUGamePhaseRuck::UpdatePreJoin: scrumhalf : '%s'"), *returned_player->GetName());
				returned_entry->AssignPlayer( returned_player );
			}
		}

		SIFRugbyCharacterList ruck_players;
		game->GetStrategyHelper()->GetTeamRoles( ruck_state->team, RURoleRuck::RTTGetStaticType(), ruck_players );

		while( ruck_state->locations[ RUCKLOC_PREJOIN ].GetBestJoinOption( ruck_players, returned_entry, returned_player, RUCK_JOIN_NONE ) )
		{
			this->ruck_states[curr_state]->initialised = true;
			returned_entry->AssignPlayer( returned_player );
			added++;
			UE_LOG(LogTemp, Display, TEXT("RUGamePhaseRuck::UpdatePreJoin: role Ruck : '%s'"), *returned_player->GetName());
		}
	}

	//MABLOGDEBUG( MabString( 64, "%d players added to ruck", added ).c_str() );
	return added > 0;
}

void RUGamePhaseRuck::OnRuckPlayerJoined( ARugbyCharacter* player, const FVector& impact_velocity, RUZoneJoinType )
{
	MABUNUSED( impact_velocity );
	MABASSERT( player != NULL );
	RUZonePositionAssigned* pos_ass = player->GetPosAss();
	MABASSERT( pos_ass != NULL );
	RUZonePosition* entry = pos_ass->GetPosition();
	MABASSERT( entry != NULL );
	//MABLOGDEBUG( MabString( 128, "Ruck player joined %s, %0.1fm/s, Jersey: %i", entry->join_type == RUCK_JOIN_AGGRESSIVE ? "aggressive" : "normal", impact_velocity.Magnitude(), player->GetAttributes()->GetNumber() ).c_str() );

	// Put this in for safety.  Hopefully it's okay. - Charles.
	if ( !entry )
	{
		return;
	}

#ifdef ENABLE_PRO_MODE
	RUCareerModeManager* proModeMan = SIFApplication::GetApplication()->GetCareerModeManager();
	bool isProMode = proModeMan->IsActive() && proModeMan->GetIsCareerModePro();
	// Check if our pro enters the ruck during pro play
	if(isProMode)
	{
		if(proModeMan->IsProPlayer(player))
		{
			MABLOGDEBUG("Our pro just entered the ruck!");
			isProInRuck = true;
			game->GetEvents()->ruck_pro_joined_ruck(player);
			SetRuckHumanPlayersAfterProJoinOrLeave();
		}
	}
#endif

	// Calculate the impact the player joining has on the ruck. Pass the entry since we already got it.
	CalculatePlayerImpact( player, entry, impact_velocity );

	// apply some fatigue
	RUZoneJoinType join_type = pos_ass->GetPosition()->join_type;
	const float RUCK_JOIN_FATIGUE[RUCK_JOIN_MAX] = { 0.0f, 0.070f, 0.10f };
	static const float DECREASE_CAP_MULTIPLIER = 0.3f;
	player->GetAttributes()->DecreaseStamina( RUCK_JOIN_FATIGUE[join_type], RUCK_JOIN_FATIGUE[join_type] * DECREASE_CAP_MULTIPLIER );

	RURuckTeamState* ruck_team_state = (RURuckTeamState*) entry->GetUserData();
	MABASSERT(ruck_team_state != NULL);
	if (!ruck_team_state)
	{
		return;
	}
	RURuckState* ruck_state = ruck_team_state->ruck_state;

	

	if ( entry->join_type == RUCK_JOIN_NORMAL || entry->join_type == RUCK_JOIN_AGGRESSIVE )
	{
		MABASSERT( std::find( ruck_team_state->commital.bound.begin(), ruck_team_state->commital.bound.end(), player ) == ruck_team_state->commital.bound.end() );
		ruck_team_state->commital.Update();
		MABASSERT( std::find( ruck_team_state->commital.bound.begin(), ruck_team_state->commital.bound.end(), player ) != ruck_team_state->commital.bound.end() );

		if (!ruck_state->first_player_joined)
			ruck_state->first_team_in_ruck = ruck_team_state->team;
		ruck_state->first_player_joined = true;
	}

	/// Assign first players in ruck
	if ( player->GetAttributes()->GetTeam() == game->GetGameState()->GetAttackingTeam() && ruck_state->first_attacker_at_ruck == NULL )
		ruck_state->first_attacker_at_ruck = player;

	if ( player->GetAttributes()->GetTeam() == game->GetGameState()->GetDefendingTeam() && ruck_state->first_defender_at_ruck == NULL )
		ruck_state->first_defender_at_ruck = player;

	if(player->GetAttributes()->GetTeam() == game->GetGameState()->GetAttackingTeam() && ruck_team_state->commital.bound.size() >= 2)
		game->GetEvents()->ruck_contest_ability_change(true, false); // Attacking team can no longer contest

	// Set the time that the a valid ruck was formed
	if ( ruck_state->valid_ruck_formed_time == MabTime(0) && ruck_state->first_defender_at_ruck != NULL && ruck_state->first_attacker_at_ruck != NULL )
	{
		// Tell our context to get rid of the contest button now since we've formed a ruck
		game->GetEvents()->ruck_contest_ability_change(true, false); // Attacking team can no longer contest
		game->GetEvents()->ruck_contest_ability_change(false, false); // Defending team can no longer contest
		ruck_state->valid_ruck_formed_time = current_time_step.abs_time;
		LOGRUCKDEBUG( "Valid ruck formed now");
	}

//#if !(PLATFORM_PS3) && !(PLATFORM_XBOX360)
	// Apply some dirt to players entering the ruck
	const float DIRT_INCREMENT_PER_RUCK = 0.01f;
	player->GetState()->IncrementDirtLevel( DIRT_INCREMENT_PER_RUCK );
//#endif

	//GetCurrentRuck().teams_in.push_back( ruck_team_state );

	// If we're in a tutorial, don't apply joins for the defending (AI) team.
	if ( (game->GetGameSettings().game_settings.game_type == GAME_TRAINING || game->GetGameSettings().game_settings.game_type == GAME_MENU) && ruck_team_state != &GetCurrentAttackingTeamState() )
		return;

	if ( setplay_state == RSP_STATE_INITIAL || setplay_state == RSP_STATE_RUNNING )
	{
		ruck_team_state->commital.IncrementBoundHighwater();
		power_contest.ApplyJoinImpact(// Nick WWS 7s to Womens 13s //game->GetGameSettings().game_settings.GameModeIsR7()
			false, join_type, ruck_team_state == &GetCurrentAttackingTeamState(), impact_velocity, (int)ruck_team_state->commital.GetTotalBound(), (int)ruck_team_state->commital.GetTotalBoundHighwater() );
	}
}

void RUGamePhaseRuck::OnRuckPlayerLeft( ARugbyCharacter* player )
{
	MABASSERT( player != NULL );
	RUZonePositionAssigned* pos_ass = player->GetPosAss();
	MABASSERT( pos_ass != NULL );
	RUZonePosition* entry = pos_ass->GetPosition();
	//MABASSERT( entry != NULL );

	if ( entry == NULL )
		return;

	// Check if our pro leaves the ruck during pro play
#ifdef ENABLE_PRO_MODE
	RUCareerModeManager* proModeMan = SIFApplication::GetApplication()->GetCareerModeManager();
	bool isProMode = proModeMan->IsActive() && proModeMan->GetIsCareerModePro();
	if(isProMode)
	{
		if(proModeMan->IsProPlayer(player))
		{
			MABLOGDEBUG("Our pro just left the ruck! Add that blue circle thing around him");
			isProInRuck = false;
			SetRuckHumanPlayersAfterProJoinOrLeave();

			//SSRole* role = game->GetRoleFactory()->Instance(SSRoleFormation::RTTGetStaticType());
			//player->SetRole(role, true);

			player->GetActionManager()->HFUnlockAll();
			//game->GetEvents()->ruck_pro_leave_ruck(player);
		}
	}
#endif

	RURuckTeamState* ruck_team_state = (RURuckTeamState*) entry->GetUserData();
	MABASSERT( ruck_team_state != NULL );
	MABASSERT( ruck_team_state->ruck_state != NULL );

	/// Remove from the relevant lists if found
	//SIFRugbyCharacterList::iterator it = std::find( ruck_team_state->players_in_ruck.begin(), ruck_team_state->players_in_ruck.end(), player );
	//if ( it != ruck_team_state->players_in_ruck.end() )
	//{
	//	ruck_team_state->players_in_ruck.erase( it );
	//}
	ruck_team_state->commital.Update();
//	To avoid this break pointer, temp.
//	MABASSERT( std::find( ruck_team_state->commital.bound.begin(), ruck_team_state->commital.bound.end(), player ) == ruck_team_state->commital.bound.end() );
	if(std::find( ruck_team_state->commital.bound.begin(), ruck_team_state->commital.bound.end(), player ) == ruck_team_state->commital.bound.end())
		return;

	/// If all ruck team players have left from both teams then we can notify the tackler/tacklees that they can get up
	RURuckState* ruck_state = ruck_team_state->ruck_state;

	if ( ruck_state->attacking.commital.bound.empty() && ruck_state->defending.commital.bound.empty() )
	{
		RUActionManager* action_manager = NULL;

		//MABASSERT( ruck_state->breakdown_holder );

		if ( ruck_state->breakdown_holder )
		{
			action_manager = ruck_state->breakdown_holder->GetActionManager();

			if ( action_manager && action_manager->IsActionRunning( ACTION_TACKLEE ) )
			{
				ruck_state->breakdown_holder->GetActionManager()->GetAction<RUActionTacklee>()->SetRuckTeamState( NULL );
				for( size_t i = 0; i < ruck_state->tacklers.size(); ++i )
					ruck_state->tacklers[i]->GetActionManager()->GetAction<RUActionTackler>()->SetRuckTeamState( NULL );
			}
		}
	}
}

void RUGamePhaseRuck::ApplyDefaultRuckConfiguration( SIFGameWorld *game, RURuckState& ruck_state, RURuckTeamState& ruck_team_state, bool is_attack )
{
	/// Clean out all ruck states
	for( size_t i = 0; i < ruck_team_state.locations.size(); i++ )
	{
		ruck_team_state.locations[i].positions.clear();
	}

	ruck_team_state.ruck_state = &ruck_state;

	///-----------------------------------------
	/// Prejoin positions
	///-----------------------------------------

	RUZoneLocation* location = &ruck_team_state.locations[ RUCKLOC_PREJOIN ];

	//const static float HALF_BACK_OFFSET = -1.6f; //dont enable
	//const static float PREJOIN_X_OFFSET = 1.2f;
	//const static float PREJOIN_Z_OFFSET = 0.0f;

	const static float ATTACK_HALF_BACK_OFFSET = -0.5f;
	const static float DEFENCE_HALF_BACK_OFFSET = -1.4f;

	const static float ATTACK_PREJOIN_X_OFFSET = 2.9f;
	const static float ATTACK_PREJOIN_Z_BASE = -3.2f;
	const static float ATTACK_PREJOIN_Z_OFFSET = -0.2f;

	const static float DEFENCE_PREJOIN_X_OFFSET = 2.9f;
	const static float DEFENCE_PREJOIN_Z_BASE = -1.5f;
	const static float DEFENCE_PREJOIN_Z_OFFSET = 0.0f;

	IRUOrigin* gate_origin = ruck_state.GetGateOrigin( ruck_team_state.team->GetPlayDirection() );
	IRUOrigin* ruck_origin = ruck_state.GetRuckOrigin();

	FVector half_back_origin( 0.0f, 0.0f, is_attack ? ATTACK_HALF_BACK_OFFSET : DEFENCE_HALF_BACK_OFFSET );
	/// Half back
	int idx = 0;

	FVector ball_pos = game->GetBall()->GetCurrentPosition();

	/// On attack iterate either side of ruck fanning out from the scrum half like a V
	if ( is_attack )
	{
		int pos_x_players = 3;
		int neg_x_players = 3;
		/*if(game->GetGameSettings().game_settings.GameModeIsR7()) //dont enable
		{
			pos_x_players = 2;
			neg_x_players = 2;
		}*/

		for ( int i = 3; i >= 1; --i )
		{
			FVector pos = half_back_origin + FVector( -ATTACK_PREJOIN_X_OFFSET * i, 0.0f, ATTACK_PREJOIN_Z_BASE + ATTACK_PREJOIN_Z_OFFSET * i );
			if ( game->GetSpatialHelper()->IsJointOutSides( pos + ball_pos ) )
			{
				++pos_x_players;
				--neg_x_players;
			}
		}

		for ( int i = 1; i <= 3; ++i)
		{
			FVector pos = half_back_origin + FVector( +ATTACK_PREJOIN_X_OFFSET * i, 0.0f, ATTACK_PREJOIN_Z_BASE + ATTACK_PREJOIN_Z_OFFSET * i );
			if ( game->GetSpatialHelper()->IsJointOutSides( pos + ball_pos ) )
			{
				--pos_x_players;
				++neg_x_players;
			}
		}

		MABASSERT( pos_x_players >= 0 && neg_x_players >= 0 );

		// if playing south swap
		if ( ruck_team_state.team->GetPlayDirection() == ERugbyPlayDirection::SOUTH )
		{
			int temp = pos_x_players;
			pos_x_players = neg_x_players;
			neg_x_players = temp;
		}

		for( int i = neg_x_players; i >= 1; i-- )
		{
			FVector pos = half_back_origin + FVector( -ATTACK_PREJOIN_X_OFFSET * i, 0.0f, ATTACK_PREJOIN_Z_BASE + ATTACK_PREJOIN_Z_OFFSET * i );
			location->positions.push_back( RUZonePosition( gate_origin, ruck_team_state.team, &ruck_team_state, location, idx++, pos, 0.0f, RUCKLOC_PREJOIN ) );
		}
		for( int i = 1; i <= pos_x_players; i++ )
		{
			FVector pos = half_back_origin + FVector( +ATTACK_PREJOIN_X_OFFSET * i, 0.0f, ATTACK_PREJOIN_Z_BASE + ATTACK_PREJOIN_Z_OFFSET * i );
			location->positions.push_back( RUZonePosition( gate_origin, ruck_team_state.team, &ruck_team_state, location, idx++, pos, 0.0f, RUCKLOC_PREJOIN ) );
		}
	}
	else
	{
		int pos_x_players = 3;
		int neg_x_players = 3;
		/*if(game->GetGameSettings().game_settings.GameModeIsR7()) //dont enable
		{
			pos_x_players = 2;
			neg_x_players = 2;
		}*/

		for ( int i = 3; i >= 1; --i )
		{
			FVector pos = half_back_origin + FVector( -DEFENCE_PREJOIN_X_OFFSET * i, 0.0f, DEFENCE_PREJOIN_Z_BASE + DEFENCE_PREJOIN_Z_OFFSET * i );
			if ( game->GetSpatialHelper()->IsJointOutSides( pos + ball_pos ) )
			{
				++pos_x_players;
				--neg_x_players;
			}
		}

		for ( int i = 1; i <= 3; ++i)
		{
			FVector pos = half_back_origin + FVector( +DEFENCE_PREJOIN_X_OFFSET * i, 0.0f, DEFENCE_PREJOIN_Z_BASE + DEFENCE_PREJOIN_Z_OFFSET * i );
			if ( game->GetSpatialHelper()->IsJointOutSides( pos + ball_pos ) )
			{
				--pos_x_players;
				++neg_x_players;
			}
		}

		MABASSERT( pos_x_players >= 0 && neg_x_players >= 0 );

		// if playing south swap
		if ( ruck_team_state.team->GetPlayDirection() == ERugbyPlayDirection::SOUTH )
		{
			int temp = pos_x_players;
			pos_x_players = neg_x_players;
			neg_x_players = temp;
		}

		for( int i = neg_x_players; i >= 1; i-- )
		{
			FVector pos = half_back_origin + FVector( -DEFENCE_PREJOIN_X_OFFSET * i, 0.0f, DEFENCE_PREJOIN_Z_BASE + DEFENCE_PREJOIN_Z_OFFSET * i );
			location->positions.push_back( RUZonePosition( gate_origin, ruck_team_state.team, &ruck_team_state, location, idx++, pos, 0.0f, RUCKLOC_PREJOIN ) );
		}
		for( int i = 1; i <= pos_x_players; i++ )
		{
			FVector pos = half_back_origin + FVector( +DEFENCE_PREJOIN_X_OFFSET * i, 0.0f, DEFENCE_PREJOIN_Z_BASE + DEFENCE_PREJOIN_Z_OFFSET * i );
			location->positions.push_back( RUZonePosition( gate_origin, ruck_team_state.team, &ruck_team_state, location, idx++, pos, 0.0f, RUCKLOC_PREJOIN ) );
		}
	}
	// Nick WWS 7s to Womens 13s //
	/*
	if(game->GetGameSettings().game_settings.GameModeIsR7())
	{
		APPLY_POSITIONCONFIG( (*location), PREJOIN_BIND_STATES_R7, RUCKLOC_PREJOIN, BOUND_LOCATION_RANK, "PreJoin", false, true );
	}
	else
	{*/
		APPLY_POSITIONCONFIG( (*location), PREJOIN_BIND_STATES, RUCKLOC_PREJOIN, BOUND_LOCATION_RANK, "PreJoin", false, true );
	//}

	///-----------------------------------------
	/// Halfback ruck positions
	///-----------------------------------------

	idx = 0;
	location = &ruck_team_state.locations[ RUCKLOC_HALFBACK ];
	location->positions.push_back( RUZonePosition( gate_origin, ruck_team_state.team , &ruck_team_state, location, idx++, half_back_origin, 0.0f, RUCKLOC_HALFBACK ) );
	APPLY_POSITIONCONFIG( (*location), HALFBACK_BIND_STATES, RUCKLOC_HALFBACK, HALFBACK_LOCATION_RANK, "HalfBack", true, false );

	///-----------------------------------------
	/// Committed ruck positions
	///-----------------------------------------

	location = &ruck_team_state.locations[ RUCKLOC_BOUND ];

	const FVector ruck_attack_team_position_offsets[] = {
		FVector(-0.123f,	0.0f, -0.725f),
		FVector(0.598f, 0.0f, -1.367f),
		FVector(-0.518f,	0.0f, -1.318f),
		FVector(0.059f, 0.0f, -1.913f),
		FVector(-0.607f,	0.0f, -1.851f),
		FVector(0.939f, 0.0f, -1.793f),
		FVector(-0.378f,	0.0f, -2.339f),
		FVector(0.444f, 0.0f, -2.46f)
	};
	const float ruck_attack_team_angle_offsets[] = { 0.13963f, -0.3316f, 0.01745f, -0.0873f, -0.1047f, -0.2618f, -0.03491f, -0.0873f };

	const FVector ruck_defense_team_position_offsets[] = {
		FVector(0.074f,	0.0f, -1.172f),
		FVector(0.598f,	0.0f, -1.443f),
		FVector(-0.174f,	0.0f, -1.754f),
		FVector(0.216f,	0.0f, -2.225f),
		FVector(0.89f,	0.0f, -2.084f),
		FVector(-0.685f, 0.0f, -2.194f),
		FVector(-0.105f,	0.0f, -2.673f),
		FVector(0.563f,	0.0f, -2.829f)
	};
	const float ruck_defense_team_angle_offsets[] = { 0.17453f, 0.0f, -0.0698f, 0.0f, -0.0873f, 0.06981f, -0.0524f, -0.0349f };

	const FVector* ruck_team_position_offsets = (is_attack)? ruck_attack_team_position_offsets : ruck_defense_team_position_offsets;
	const float* angle_offsets = (is_attack)? ruck_attack_team_angle_offsets : ruck_defense_team_angle_offsets;

	FVector ruck_offset(0.0f,0.0f,0.0f);

	idx = 0;

	// Nick WWS 7s to Womens 13s //
	/*
	if(game->GetGameSettings().game_settings.GameModeIsR7())
	{
		location->positions.push_back( RUZonePosition( ruck_origin, ruck_team_state.team, &ruck_team_state, location, idx, ruck_team_position_offsets[idx] + ruck_offset, angle_offsets[idx], RUCKLOC_BOUND, NULL, 1 ) ); idx++;
		location->positions.push_back( RUZonePosition( ruck_origin, ruck_team_state.team, &ruck_team_state, location, idx, ruck_team_position_offsets[idx] + ruck_offset, angle_offsets[idx], RUCKLOC_BOUND, NULL, 2 ) ); idx++;
		location->positions.push_back( RUZonePosition( ruck_origin, ruck_team_state.team, &ruck_team_state, location, idx, ruck_team_position_offsets[idx] + ruck_offset, angle_offsets[idx], RUCKLOC_BOUND, NULL, 2 ) ); idx++;

		/// Add links
		location->positions[1].AddSideLink( &location->positions[0]);

		// Work out ruck join order
		APPLY_POSITIONCONFIG( (*location), BOUND_BIND_STATES_R7, RUCKLOC_BOUND, BOUND_LOCATION_RANK, "Bound", true, false );
	}
	else
	{*/
		location->positions.push_back( RUZonePosition( ruck_origin, ruck_team_state.team, &ruck_team_state, location, idx, ruck_team_position_offsets[idx] + ruck_offset, angle_offsets[idx], RUCKLOC_BOUND, NULL, 1 ) ); idx++;
		location->positions.push_back( RUZonePosition( ruck_origin, ruck_team_state.team, &ruck_team_state, location, idx, ruck_team_position_offsets[idx] + ruck_offset, angle_offsets[idx], RUCKLOC_BOUND, NULL, 2 ) ); idx++;
		location->positions.push_back( RUZonePosition( ruck_origin, ruck_team_state.team, &ruck_team_state, location, idx, ruck_team_position_offsets[idx] + ruck_offset, angle_offsets[idx], RUCKLOC_BOUND, NULL, 2 ) ); idx++;
		location->positions.push_back( RUZonePosition( ruck_origin, ruck_team_state.team, &ruck_team_state, location, idx, ruck_team_position_offsets[idx] + ruck_offset, angle_offsets[idx], RUCKLOC_BOUND, NULL, 3 ) ); idx++;
		location->positions.push_back( RUZonePosition( ruck_origin, ruck_team_state.team, &ruck_team_state, location, idx, ruck_team_position_offsets[idx] + ruck_offset, angle_offsets[idx], RUCKLOC_BOUND, NULL, 3 ) ); idx++;
		location->positions.push_back( RUZonePosition( ruck_origin, ruck_team_state.team, &ruck_team_state, location, idx, ruck_team_position_offsets[idx] + ruck_offset, angle_offsets[idx], RUCKLOC_BOUND, NULL, 3 ) ); idx++;
		location->positions.push_back( RUZonePosition( ruck_origin, ruck_team_state.team, &ruck_team_state, location, idx, ruck_team_position_offsets[idx] + ruck_offset, angle_offsets[idx], RUCKLOC_BOUND, NULL, 3 ) ); idx++;
		location->positions.push_back( RUZonePosition( ruck_origin, ruck_team_state.team, &ruck_team_state, location, idx, ruck_team_position_offsets[idx] + ruck_offset, angle_offsets[idx], RUCKLOC_BOUND, NULL, 3 ) ); idx++;

		/// Add links
		location->positions[0].AddSideLink( &location->positions[2]);
		location->positions[0].AddBackwardLink( &location->positions[3]);
		location->positions[0].AddBackwardLink( &location->positions[4]);

		location->positions[1].AddSideLink( &location->positions[0]);
		location->positions[1].AddBackwardLink( &location->positions[4]);
		location->positions[1].AddBackwardLink( &location->positions[5]);

		location->positions[2].AddBackwardLink( &location->positions[3]);
		location->positions[2].AddBackwardLink( &location->positions[6]);

		location->positions[3].AddSideLink( &location->positions[6]);
		location->positions[4].AddSideLink( &location->positions[3]);
		location->positions[5].AddSideLink( &location->positions[4]);

		

		// Work out ruck join order
		APPLY_POSITIONCONFIG( (*location), BOUND_BIND_STATES, RUCKLOC_BOUND, BOUND_LOCATION_RANK, "Bound", true, false );
	//}

	for( size_t i = 0; i < ruck_team_state.locations.size(); i++ )
	{
		MABVERIFY( ruck_team_state.locations[i].Validate() );
	}

	//// Print out debug
	//for( size_t i = 0; i < ruck_team_state.locations.size(); i++ )
	//{
	//	RUZoneLocation& loc = ruck_team_state.locations[i];
	//	MABLOGDEBUG( loc.name );
	//	for( size_t j = 0; j < loc.positions.size(); j++ )
	//	{
	//		RUZonePosition& pos = loc.positions[j];
	//		MABLOGDEBUG( MabString( 128,"%c,%0.4f,%0.4f,%d", pos.bind_index, pos.offset.x, pos.offset.z, pos.priority ).c_str() );
	//	}
	//}

}


void RUGamePhaseRuck::AbortRuck()
{
	if(game->GetGameState()->GetPhaseHandler() == this)
    {
		game->GetGameState()->SetPhase(RUGamePhase::PLAY);
    }

	Reset();

	setplay_state = RSP_STATE_FINISHED;
}

///-------------------------------------------------------------------------
/// Have just exited from this game phase.
///-------------------------------------------------------------------------

void RUGamePhaseRuck::Exit()
{

	//MABLOGDEBUG("**************** RUGamePhaseRuck::Exit");
	ClearDebug();
	RUGameState* game_state = game->GetGameState();
	game_state->SetFormationTarget( ERugbyFormationTarget::RUCK_CENTER, NULL );
	game_state->SetFormationTarget( ERugbyFormationTarget::ATTACKLASTFEET,	NULL );
	game_state->SetFormationTarget( ERugbyFormationTarget::DEFENCELASTFEET,	NULL );

	/// Reset the number of ruck players committed for the formation
	for( int i = 0; i < 2; i++ )
	{
		RUTeam* team = game->GetTeam( i );
		if (team)
		{
			team->GetFormationManager()->OverrideAreaNumPlayers(RUCK_ZONE_NAME, -1, ERugbyFormationRole::RUCK, RUCK_ATTACK_FORMATION_NAME);
			team->GetFormationManager()->OverrideAreaNumPlayers(RUCK_ZONE_NAME, -1, ERugbyFormationRole::RUCK, RUCK_DEFENCE_FORMATION_NAME);
			team->GetFormationManager()->OverrideAreaNumPlayers(SEND_RUNNER_ZONE_NAME, -1, ERugbyFormationRole::RUCKSENDRUNNER, RUCK_ATTACK_FORMATION_NAME);
		}
	}	
	game->Get3DHudManager()->GetSetPieceIndicator()->SetVisible(false);

	/// VERY IMPORTANT: Do garbage collection on lua heap on exiting ruck, stop gradual filling of lua heap and eventual OOM.
	//#rc3_legacy SIFApplication::GetApplication()->GetWindowSystem()->GetLuaInterpreter()->ForceGarbageCollection();
}

///-------------------------------------------------------------------------
/// Called every frame when this phase is active.
///-------------------------------------------------------------------------

void RUGamePhaseRuck::UpdateSimulation( const MabTimeStep& game_time_step )
{

	current_time_step = game_time_step;

	/// Update the number of people committing to the ruck
	GetCurrentAttackingTeamState().commital.Update();
	GetCurrentDefendingTeamState().commital.Update();

	switch(setplay_state)
	{
		case RSP_STATE_INITIAL:			UpdateStateInitial();					break;
		case RSP_STATE_RUNNING:			UpdateStateRunning( game_time_step );	break;
		case RSP_STATE_WAIT_RELEASE:	UpdateStateWaitRelease();				break;
		case RSP_STATE_FINISHED:
			// Waiting for the game_phase to change...
			break;
		default:
			break;
	}

	setplay_time += game_time_step.delta_time.ToSeconds();

	//Catch getting stuck in a ruck
	if (setplay_time > 20)
	{
		MABASSERTMSG(false, "CONTACT MITCHELL/AARON - stuck in a ruck");

		//Attempt to find a player to pass the ball to.
		ARugbyCharacter * bail_out_player = game->GetGameState()->GetAttackingTeam()->GetPlayerByPosition(PLAYER_POSITION::PP_SCRUM_HALF); 
		if (!bail_out_player)
		{
			bail_out_player = game->GetGameState()->GetAttackingTeam()->GetHumanPlayer(0)->GetRugbyCharacter();
		}

		//If no player is found, reset to to scrum.
		if (!bail_out_player)
		{
			//Reset to scrum
			// Generate resync data
			MabStreamMemory stream_buffer(SIFHEAP_DYNAMIC);
			{
				SSStreamPacker stream_packer(stream_buffer);
				game->NetGenerateReSync(stream_packer);
				stream_buffer.Reset();
			}

			// Run resync
			{
				SSStreamPacker stream_packer(stream_buffer);
				game->NetLoadReSync(stream_packer);
			}
		}
		else
		{
			//Otherwise give the ball to the player.
			game->GetGameState()->PickedUp(bail_out_player, PUC_FROM_RUCK);
		}
	}

	// If we're still in the phase, update debug. If phase changes this update will occur post Exit() call.
	if ( game->GetGameState()->GetPhase() == RUGamePhase::RUCK )
		UpdateDebug();


}

#ifdef ENABLE_PRO_MODE
void RUGamePhaseRuck::RequestRuckJoin( RUTeam* team, RUZoneJoinType join_type, bool proRequest )
#else
void RUGamePhaseRuck::RequestRuckJoin( RUTeam* team, RUZoneJoinType join_type )
#endif
{
	/// If we have attempted to contest the ball then we should just abort/ignore this request
	MABASSERT( GetCurrentAttackingTeamState().team && GetCurrentDefendingTeamState().team );
	RURuckTeamState& updated_state = team == GetCurrentAttackingTeamState().team ? GetCurrentAttackingTeamState() : GetCurrentDefendingTeamState();
	if ( team == GetCurrentDefendingTeamState().team && updated_state.ruck_state->first_contest_ball_time != MabTime(0) )
		return;

	const RuckCommital& commital = GetCurrentRuck().GetCommital(team);
	size_t maxCount = // Nick WWS 7s to Womens 13s //game->GetGameSettings().game_settings.GameModeIsR7() ? MAX_PLAYERS_PER_SIDE_IN_RUCK_R7 : 
		MAX_PLAYERS_PER_SIDE_IN_RUCK;

	if (commital.GetTotalCommittingOrBound() == maxCount)
		return;

	updated_state.outstanding_join_requests.push_back( join_type );
#ifdef ENABLE_PRO_MODE
	if(proRequest)
	{
		updated_state.outstanding_pro_join_request = true;
		ARugbyCharacter* pro_player = SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer();
		if(pro_player != NULL)
		{
			pro_player->GetAttributes()->nextIgnoredRole = 0;
		}
	}
#endif

	/// If there are more ruck requests than there are players who are currently assigned to bound - normal or aggressive

	// This can be called before the ruck phase updates so we need to update
	GetCurrentAttackingTeamState().commital.Update();
	GetCurrentDefendingTeamState().commital.Update();

	game->GetEvents()->ruck_join_requested( team, join_type );

	if(proRequest)
		game->GetEvents()->ruck_pro_joining_ruck(team->GetProPlayer());

	// Use our R7 const instead of R15 const

	if ( commital.GetTotalCommittingOrBound() >= commital.GetTotalRuckPlayers() && commital.GetTotalRuckPlayers() < maxCount )
	{
		team->GetFormationManager()->OverrideAreaNumPlayers( RUCK_ZONE_NAME, (int)(commital.GetTotalRuckPlayers() + 1), ERugbyFormationRole::RUCK );
	}
}

#ifdef ENABLE_PRO_MODE
void RUGamePhaseRuck::RequestRuckLeave( RUTeam* team, bool proRequest )
#else
void RUGamePhaseRuck::RequestRuckLeave( RUTeam* team )
#endif
{
	MABASSERT( GetCurrentAttackingTeamState().team && GetCurrentDefendingTeamState().team );
	RURuckTeamState& updated_state = team == GetCurrentAttackingTeamState().team ? GetCurrentAttackingTeamState() : GetCurrentDefendingTeamState();

	size_t minBoundCount = 1;

#ifdef ENABLE_PRO_MODE
	// While playing a pro game, we may only have 1 person bound, i.e. our pro player, so if minBoundCount == 1 we'll never be ale to leave
	// Is this a bug in R7 in general? Should we allow leaving with just 1 player there?
	if(game->GetGameSettings().game_settings.GetIsAProMode() && team->GetProPlayer() != NULL)
		minBoundCount = 1;
#endif

	if ( updated_state.commital.GetTotalBound() > minBoundCount )
	{
		updated_state.outstanding_leave_requests++;
#ifdef ENABLE_PRO_MODE
		if(proRequest)
			updated_state.outstanding_pro_leave_request = true;
#endif
	}
}

void RUGamePhaseRuck::RequestContestBall( RUTeam* team )
{
	MABASSERT( GetCurrentAttackingTeamState().team && GetCurrentDefendingTeamState().team );
	RURuckTeamState& updated_state = team == GetCurrentAttackingTeamState().team ? GetCurrentAttackingTeamState() : GetCurrentDefendingTeamState();
	updated_state.oustanding_contest_ball_requests++;
	updated_state.time_since_last_contest = 0.0f;
}

void RUGamePhaseRuck::SetHoldOntoContestedBallState( RUTeam* team, bool holding_on )
{
	MABASSERT( GetCurrentAttackingTeamState().team && GetCurrentDefendingTeamState().team );
	RURuckTeamState& updated_state = team == GetCurrentAttackingTeamState().team ? GetCurrentAttackingTeamState() : GetCurrentDefendingTeamState();
	updated_state.contest_hold_on = holding_on;
}

void RUGamePhaseRuck::UpdateStateRunning( const MabTimeStep& time_step )
{
	// Dewald WW - This was causing issues where the ball floats in the air.
#if 0
	SSBall* ball = game->GetBall();

	// Place ball on ruck centroid to prevent camera spazzing out a tad.
	if ( ball->GetHolder() != NULL )
		ball->SetHolder( NULL );

	// Stop the ball from rolling free of the ruck.
	ball->StopAllMotion();
#endif

	const float LONG_RUCK_TIME_LIMIT = 5.0f;
	if( !slow_ruck_event_fired && setplay_time > LONG_RUCK_TIME_LIMIT )
	{
		slow_ruck_event_fired = true;
		game->GetEvents()->ruck_slow();
	}

//	if()

	UpdateContestBallTacklerGetToFeet();
	UpdatePreJoin();
	UpdateJoining( time_step );
	UpdateContestBall( time_step );
	UpdatePowerContest( time_step );
}

void RUGamePhaseRuck::UpdateJoining( const MabTimeStep& time_step )
{
	/// Formation should be bringing players into a position where the can join
	/// For the AI, we want to work out which players should/shouldn't join
	RURuckTeamState *team_ruck_states[2] = { &GetCurrentAttackingTeamState(), &GetCurrentDefendingTeamState() };

	for( int i = 0; i < 2; i++ )
	{
		RURuckTeamState* ruck_state = team_ruck_states[i];
		bool team_is_ai = ruck_state->team->GetNumHumanPlayers() == 0;

		if ( !ruck_state->ruck_state )
		{
			MABLOGDEBUG( "Ruck has already ended! Why are players still trying to join?!!" );
			continue;
		}

		/// Work out whether we need to add more people to the ruck
#ifdef ENABLE_PRO_MODE
		// Piggie back this for Pro Mode to make our team mates join in since we can't tell them to
		if ( team_is_ai || ruck_state->team->GetGame()->GetGameSettings().game_settings.GetIsAProMode() )
#else
		if ( team_is_ai )
#endif
		{
			// Is AI disabled for rucks? (please have a look to the top of this file)
			if ( disable_ai_ruck && ruck_state->team->GetHumanPlayer(0) == NULL )
				continue;

			bool is_secondary_add;
			RUZoneJoinType join_type = ShouldAIJoin(*ruck_state, time_step, is_secondary_add );\

			// Use our R7 const instead of R15 const
			size_t maxCount = // Nick WWS 7s to Womens 13s //game->GetGameSettings().game_settings.GameModeIsR7() ? MAX_PLAYERS_PER_SIDE_IN_RUCK_R7 : 
				MAX_PLAYERS_PER_SIDE_IN_RUCK;
			if ( join_type != RUCK_JOIN_NONE && ruck_state->GetCommittedPlayerCount() < (int) maxCount )
			{
				ruck_state->time_till_next_ai_join = GetNextAIJoinTime( is_secondary_add );
				ruck_state->ai_join_requests.pop_front();
				RequestRuckJoin( ruck_state->team, join_type );
			}
		}

#ifdef ENABLE_PRO_MODE
		// A pro player requested a join/leave.
		ARugbyCharacter* pro_player = SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer();
		if((ruck_state->outstanding_pro_join_request) && pro_player != NULL)
		{
			// Force the player to take on a role of ruck (he'll probably be in a formation role at this stage
			SSRole* role = game->GetRoleFactory()->Instance(RURoleRuck::RTTGetStaticType());

			// Make sure we dont double assign?
			if(pro_player->GetRole() != role)
				pro_player->SetRole(role, true);
		}
#endif

		// Process join requests
		SIFRugbyCharacterList players;
		game->GetStrategyHelper()->GetTeamRoles( ruck_state->team, RURoleRuck::RTTGetStaticType(), players );


		SIFRugbyCharacterList::iterator it;
		std::deque< RUZoneJoinType >::iterator join_it;

		for( join_it = ruck_state->outstanding_join_requests.begin(); join_it != ruck_state->outstanding_join_requests.end(); )
		{
			RUZoneJoinType join_type = *join_it;
			RUZoneLocationType target_location_type = GetTargetLocationTypeFromJoinType( join_type );
			MABASSERT( target_location_type == RUCKLOC_BOUND );
			RUZoneLocation& config = ruck_state->locations[ target_location_type ];

			RUZonePosition *returned_entry;
			ARugbyCharacter* returned_player;
			/// If we got a best result then assign it
#ifdef ENABLE_PRO_MODE
			if ( config.GetBestJoinOption( players, returned_entry, returned_player, join_type, ruck_state->outstanding_pro_join_request ) )
			{
				// Stop looking for the pro
				ruck_state->outstanding_pro_join_request = false;
#else
			if ( config.GetBestJoinOption( players, returned_entry, returned_player, join_type ) )
			{
#endif
				RUZonePositionAssigned* pos_ass = returned_player->GetPosAss();
				MABASSERT( pos_ass != NULL );
				RUZonePosition* current_entry = pos_ass->GetPosition();

				if ( current_entry && current_entry->player != NULL && current_entry != returned_entry )
					current_entry->AssignPlayer( NULL );

				returned_entry->AssignPlayer( returned_player, join_type );
				MABLOGDEBUG("RUGamePhaseRuck [%s](%d) Assign ruck position", ( i == 0 ? "A" : "D"), returned_player->GetAttributes()->GetIndex());

				ruck_state->commital.Update();
				join_it = ruck_state->outstanding_join_requests.erase( join_it );
			}
			else
			{
				//++join_it;
				ruck_state->outstanding_join_requests.clear();
				ruck_state->outstanding_pro_join_request = false;
				break;
			}
		}

		// Trying to fix up an NMA for the tackler becoming a contester. If the contest button is pressed after we have requested ruck joins
		// the tackler will stand up, but then wont have a position to join. Instead of restricting the tackler from standing up
		// we'll force some of the players who are committing to leave instead. This will allow (hopefully) our tackler to take a position instead.
		bool nextLeaveCheckForContesterJoin = false;
		if( i == 1 )
		{
			MABASSERT( GetCurrentRuck().breakdown_holder );
			if( GetCurrentRuck().breakdown_holder != NULL )
			{
				if( GetCurrentRuck().breakdown_holder->GetActionManager()->IsActionRunning(ACTION_TACKLEE) )
				{
					RUActionTacklee* action_tacklee = GetCurrentRuck().breakdown_holder->GetActionManager()->GetAction<RUActionTacklee>();
					if( action_tacklee != NULL )
					{
						ARugbyCharacter* tackler = action_tacklee->GetTackleResult().tacklers[0];
						if ( tackler )
						{
							if(tackler->GetRole()->RTTGetType() == RURoleRuck::RTTGetStaticType())
							{
								auto currentState = tackler->GetRole<RURoleRuck>()->GetState();
								if(currentState == RURoleRuck::RoleState::CONTESTING_BALL_GETUP)
								{
									int totalCommitting = (int)ruck_state->commital.GetTotalCommitting();
									if (totalCommitting - 1 > 0)
									{
										MABLOGDEBUG("Making room for our contesting player. %s, (J:%i), (I:%i)", tackler->GetAttributes()->GetCombinedName().c_str(), tackler->GetAttributes()->GetNumber(), tackler->GetAttributes()->GetIndex());
										ruck_state->outstanding_leave_requests = totalCommitting - 1;
										ruck_state->outstanding_join_requests.push_back( RUCK_JOIN_NORMAL );
										nextLeaveCheckForContesterJoin = true;
									}
								}
							}
						}
					}
				}
			}
		}

		// Process leave requests
		while( ruck_state->outstanding_leave_requests > 0 )
		{
			ARugbyCharacter* player;
			RUZonePosition* position;
			RUZoneLocation& bound_config = ruck_state->locations[ RUCKLOC_BOUND ];
#ifdef ENABLE_PRO_MODE
			if ( bound_config.GetBestLeaveOption( players, position, player, ruck_state->outstanding_pro_leave_request ) )
			{
#else
			if ( bound_config.GetBestLeaveOption( players, position, player ) )
			{
#endif
				//if(game->GetGameSettings().game_settings.IsProPlayer(player))
				//	player->GetActionManager()->HFLockAll();

				MABLOGDEBUG("RUGamePhaseRuck [%s] %s, (J:%i), (I:%i) Leave ruck position", ( i == 0 ? "A" : "D"), player->GetAttributes()->GetCombinedName().c_str(), player->GetAttributes()->GetNumber(), player->GetAttributes()->GetIndex());
				RUGameEvents* events = player->GetGameWorld()->GetEvents();
				events->player_cleared_position( player, position );

				// Our pro player is actually leaving.
				if(ruck_state->outstanding_pro_leave_request && SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(player))
				{
					ruck_state->outstanding_pro_leave_request = false;
					events->ruck_pro_leave_ruck(player);
				}

				// We were having trouble finding a spot for our contesting player, we forced someone to leave for us.
				if(nextLeaveCheckForContesterJoin)
				{
					RUActionTacklee* action_tacklee = GetCurrentRuck().breakdown_holder->GetActionManager()->GetAction<RUActionTacklee>();
					ARugbyCharacter* tackler = action_tacklee->GetTackleResult().tacklers[0];

					if(tackler)
					{
						MABLOGDEBUG("See if we can find a spot for our contesting player. %s, (J:%i), (I:%i)", tackler->GetAttributes()->GetCombinedName().c_str(), tackler->GetAttributes()->GetNumber(), tackler->GetAttributes()->GetIndex());
						RUZonePositionAssigned* pos_ass = tackler->GetPosAss();
						RUZonePosition* current_ruck_position = pos_ass->GetPosition();

						if (!current_ruck_position)
						{
							// position not assigned? then assign a bound position
							RUZonePosition *returned_entry;
							ARugbyCharacter* returned_player;
							SIFRugbyCharacterList only_one_players;
							only_one_players.push_back(tackler);

							RUZoneJoinType join_type = RUCK_JOIN_NORMAL;
							RUZoneLocationType target_location_type = RUCKLOC_BOUND;

							RUZoneLocation& config = ruck_state->locations[ target_location_type ];
							if ( config.GetBestJoinOption( only_one_players, returned_entry, returned_player, join_type ) )
							{
								returned_entry->AssignPlayer( returned_player, join_type );
								MABLOGDEBUG("RUGamePhaseRuck Found position for contester. %s, (J:%i), (I:%i)", tackler->GetAttributes()->GetCombinedName().c_str(), tackler->GetAttributes()->GetNumber(), tackler->GetAttributes()->GetIndex());
								ruck_state->commital.Update();
								nextLeaveCheckForContesterJoin = false;
							}
							else
							{
								MABLOGDEBUG("Couldn't find a place for our contesting player. %s, (J:%i), (I:%i)", tackler->GetAttributes()->GetCombinedName().c_str(), tackler->GetAttributes()->GetNumber(), tackler->GetAttributes()->GetIndex());
							}
						}
					}
				}
			}
			--ruck_state->outstanding_leave_requests;
		}

		nextLeaveCheckForContesterJoin = false;
	}
}

void RUGamePhaseRuck::UpdateContestBallTacklerGetToFeet()
{
	RURuckTeamState& defending_team = GetCurrentDefendingTeamState();
	RURuckState& current_ruck = GetCurrentRuck();

	if(current_ruck.breakdown_holder == NULL)
		return;
	/// If there is a tackler who can get to their feet then
	/// Try and get them to do so
	if ( defending_team.oustanding_contest_ball_requests && current_ruck.breakdown_holder->GetActionManager()->IsActionRunning( ACTION_TACKLEE ) )
	{
		// check the ruck commital so far, if they have some dudes already binding and almost bound then they should be the
		// contesters rather than the tackler
		if (defending_team.commital.GetTotalBound() >= 1)
		{
			/*RUZoneLocation& bound_config = defending_team.locations[ RUCKLOC_BOUND ];
			ARugbyCharacter* first_bound_player = bound_config.positions[0].player;

			if(!first_bound_player->GetRole<RURoleRuck>()->GetContestBallRequested())
			{
				MABLOGDEBUG("RUGamePhaseRuck (%d) Assign first bound as ruck contester", first_bound_player->GetAttributes()->GetIndex());
				first_bound_player->GetRole<RURoleRuck>()->SetContestBallRequested( true );
			}*/
			return;
		}
		else
		{
			RUActionTacklee* action_tacklee = current_ruck.breakdown_holder->GetActionManager()->GetAction<RUActionTacklee>();
			ARugbyCharacter* tackler = action_tacklee->GetTackleResult().tacklers[0];
			if ( tackler && tackler->GetActionManager()->IsActionRunning( ACTION_TACKLER ) )
			{
				RUActionTackler* action_tackler = tackler->GetActionManager()->GetAction<RUActionTackler>();
				TackleState tackle_state = action_tackler->GetState();
				if ( tackle_state == TS_IN_TACKLE_ON_GROUND )
				{
					if (!action_tackler->ContestBallRequested())
					{
						action_tackler->SetContestBallRequested();

						UE_LOG(LogTemp, Warning, TEXT("RUGamePhaseRuck (%d) Assign tackler as ruck contester '%s'"), tackler->GetAttributes()->GetIndex(), *tackler->GetName());
						

						RUZonePositionAssigned* pos_ass = tackler->GetPosAss();
						if(!pos_ass)
						{
							MABLOGDEBUG("RUGamePhaseRuck Tackler doesn't have ass entry");
						}
						else
						{
							/*RUZonePosition* current_entry = pos_ass->GetPosition();
							if (current_entry == nullptr)
							{
								MABLOGDEBUG("RUGamePhaseRuck Tackler has ass entry (While also having current_entry as null??)");
							}
							else
							{
								MABLOGDEBUG("RUGamePhaseRuck Tackler has ass entry (%c)", current_entry->GetBindIndex());
							}
							MABUNUSED(current_entry);*/
						}

						// add an extra role for this guy to grab
						// new logic
						//int newAreaNum = (int)(defending_team.team->GetStrategy().GetRuckCommittalCount() + 1);

						// old logic
						RuckCommital& defending_commital = defending_team.commital;
						int newAreaNum = defending_commital.GetTotalRuckPlayers() + 1;
						defending_team.team->GetFormationManager()->OverrideAreaNumPlayers( RUCK_ZONE_NAME, newAreaNum, ERugbyFormationRole::RUCK );
					}
				}
			}
		}
	}
}

void RUGamePhaseRuck::UpdateContestBallAI( const MabTimeStep& time_step )
{
	// If we're restricting turnovers, we should probably not allow the defending team to contest the ball
	bool canTurnOver = game->GetGameTimer()->IsTurnoverAvailable();

	RURuckTeamState& attacking_team = GetCurrentAttackingTeamState();
	RURuckTeamState& defending_team = GetCurrentDefendingTeamState();

	if(!canTurnOver)
	{
		defending_team.contest_decision = RURuckTeamState::WONT_CONTEST;
		return;
	}

	const RuckCommital& attacking_commital = attacking_team.commital;
	const RuckCommital& defending_commital = defending_team.commital;

	/// Find out if tacklee is isolated
	RLPResultList			nearest_attackers;
	RLP_FILTERPARAMETERS	filter_params;

	// Setup the filter to find all players close to the player
	ARugbyCharacter* breakdown_holder = attacking_team.ruck_state->breakdown_holder;
	filter_params.filters = RLP_FILTER_TEAM | RLP_FILTER_EXCLUDE_PLAYER;
	filter_params.team					= attacking_team.team;
	filter_params.exclude_player		= breakdown_holder;
	game->GetFilteredPlayerList( nearest_attackers, filter_params, game->GetPlayerFilters()->GetPlayerBallDistanceSort());

	const static float PLAYER_IS_ISOLATED_MIN_DIST = 8.0f;
	float nearest_attacker_dist = nearest_attackers.empty() ? 1e10f : SSMath::GetXZPointToPointDistance( nearest_attackers[0].player->GetMovement()->GetCurrentPosition(), game->GetBall()->GetCurrentPosition() );
	bool holder_is_isolated = nearest_attackers.empty() || nearest_attacker_dist > PLAYER_IS_ISOLATED_MIN_DIST;

	/// Handle attacking team
	if ( attacking_team.team->GetNumHumanPlayers() == 0 && attacking_team.contest_decision == RURuckTeamState::CONTEST_UNDECIDED )
	{
		// We have a defender on us attacking the ball
		const static float SHOULD_HOLD_ON_NORMAL = 0.2f;
		const static float SHOULD_HOLD_ON_ISOLATED = 0.5f;
		if ( attacking_commital.GetTotalBound() == 0 && defending_commital.GetTotalBound() >= 0 )
		{
			float prob_hold_on = holder_is_isolated ? SHOULD_HOLD_ON_ISOLATED : SHOULD_HOLD_ON_NORMAL;
			attacking_team.contest_decision = game->GetRNG()->RAND_RANGED_CALL(float, 1.0f ) < prob_hold_on ? RURuckTeamState::WILL_CONTEST : RURuckTeamState::WONT_CONTEST;
		}
		/// Just need to set this once
		attacking_team.contest_hold_on = attacking_team.contest_decision == RURuckTeamState::WILL_CONTEST;
	}

	// Handle defending team
	if ( defending_team.team->GetNumHumanPlayers() == 0 )
	{
		const static float SHOULD_CONTEST_WHILE_LEGAL_NORMAL = 0.35f;
		const static float SHOULD_CONTEST_WHILE_LEGAL_ISOLATED = 0.8f;
		const static float SHOULD_CONTEST_WHILE_ILLEGAL = 0.1f;

		// If we are there before the opposition and can still contest then do so
		if ( attacking_commital.GetTotalBound() == 0 && defending_commital.GetTotalBound() >= 0 )
		{
			if ( defending_team.contest_decision == RURuckTeamState::CONTEST_UNDECIDED )
			{
				float prob_contest = holder_is_isolated ? SHOULD_CONTEST_WHILE_LEGAL_ISOLATED : SHOULD_CONTEST_WHILE_LEGAL_NORMAL;
				defending_team.contest_decision = game->GetRNG()->RAND_RANGED_CALL(float, 1.0f ) < prob_contest ? RURuckTeamState::WILL_CONTEST : RURuckTeamState::WONT_CONTEST;
			}
		}
		else if ( attacking_commital.GetTotalBound() > 0 && defending_commital.GetTotalBound() > 0 )
		{
			if ( defending_team.contest_decision == RURuckTeamState::WILL_CONTEST )
			{
				defending_team.contest_decision = game->GetRNG()->RAND_RANGED_CALL(float, 1.0f ) < SHOULD_CONTEST_WHILE_ILLEGAL ? RURuckTeamState::WILL_CONTEST : RURuckTeamState::WONT_CONTEST;
			}
		}
	}

	// Dewald WW - Debug always contest
	//defending_team.contest_decision = RURuckTeamState::WILL_CONTEST;

	defending_team.time_till_next_ai_contest -= time_step.delta_time.ToSeconds();
	if ( defending_team.contest_decision == RURuckTeamState::WILL_CONTEST && defending_team.time_till_next_ai_contest <= 0.0f )
	{
		RequestContestBall( defending_team.team );
		defending_team.time_till_next_ai_contest = GetNextAIContestTime();
	}
}

void RUGamePhaseRuck::OnPlayerContestingBall( ARugbyCharacter* player, int ruck_index )
{
	// only current ruck will care about ball contesting, ignore any late ones
	if (ruck_index != curr_state)
		return;

	RURuckTeamState& defending_team = GetCurrentDefendingTeamState();

	defending_team.contesting_ball_player = player;

	const static int MAX_PRE_CONTEST_BALL_REQUESTS = 4;
	MabMath::ClampUpper( defending_team.oustanding_contest_ball_requests, MAX_PRE_CONTEST_BALL_REQUESTS );
}

void RUGamePhaseRuck::UpdateContestBall( const MabTimeStep& time_step )
{
	MABUNUSED(time_step);

	// Check if we're restricting the turn over
	//bool canTurnOver = game->GetGameTimer()->IsTurnoverAvailable();

	RURuckTeamState& defending_team = GetCurrentDefendingTeamState();
	RURuckState& current_ruck = GetCurrentRuck();
	current_ruck.should_release_grace_time -= time_step.delta_time.ToSeconds();

	MABASSERT( current_ruck.breakdown_holder != NULL );

	/// If the ball holder has changed then bail
	if ( current_ruck.breakdown_holder != game->GetGameState()->GetBallHolder() )
	{
		if ( defending_team.contesting_ball_player && defending_team.contesting_ball_player->GetRole() && defending_team.contesting_ball_player->GetRole()->RTTGetType() == RURoleRuck::RTTGetStaticType() )
		{
			RURoleRuck* ruck_role = defending_team.contesting_ball_player->GetRole<RURoleRuck>();
			if ( ruck_role->IsContestingBall() )
				game->GetEvents()->breakdown_stopped_contesting( defending_team.contesting_ball_player, current_ruck.breakdown_holder );
		}
		return;
	}

	// Contest ball AI
	UpdateContestBallAI( time_step );	// AI will submit requests to either contest or it will set the hold on flag like a human

	/// Find out who is contesting on the defending team
	ARugbyCharacter* contesting_defender = NULL;
	ARugbyCharacter* requested_contesting_defender = NULL;

	// Not allowed to turn over, lets act like the defending team didnt request a contest.
	//if(!canTurnOver)
	//	defending_team.oustanding_contest_ball_requests = 0;

	if (defending_team.oustanding_contest_ball_requests > 0 && !current_ruck.breakdown_contest_called)
	{
		SIFRugbyCharacterList defending_ruck_players;
		game->GetStrategyHelper()->GetTeamRoles( defending_team.team, RURoleRuck::RTTGetStaticType(), defending_ruck_players );

		for( SIFRugbyCharacterList::iterator i = defending_ruck_players.begin(); i != defending_ruck_players.end(); ++i )
		{
			ARugbyCharacter* player = *i;

			RURoleRuck* ruck_role = player->GetRole<RURoleRuck>();
			if ( ruck_role->GetRuckIndex() != -1 && ruck_role->GetRuckIndex() != defending_team.ruck_state->ruck_id )
				continue;

			// If the player is already contesting, it means that they stood up to contest while there were already too many players commiting
			// Since it was then forced to go straight into the contesting state, we never had an entry.
			if(ruck_role->IsContestingBall())
			{
				requested_contesting_defender = player;
			}
			// check if this player has been requested to contest the ball
			else if (ruck_role->GetContestBallRequested())
			{
				// check if this player has been assigned a position in the ruck
				RUZonePositionAssigned* pos_ass = player->GetPosAss();
				RUZonePosition* current_ruck_position = pos_ass->GetPosition();

				requested_contesting_defender = player;

				if (!current_ruck_position)
				{
					// position not assigned? then assign a bound position
					RUZonePosition *returned_entry;
					ARugbyCharacter* returned_player;
					SIFRugbyCharacterList only_one_players;
					only_one_players.push_back(player);

					RUZoneJoinType join_type = RUCK_JOIN_NORMAL;
					RUZoneLocationType target_location_type = RUCKLOC_BOUND;

					RUZoneLocation& config = defending_team.locations[ target_location_type ];
					if ( config.GetBestJoinOption( only_one_players, returned_entry, returned_player, join_type ) )
					{
						returned_entry->AssignPlayer( returned_player, join_type );
						MABLOGDEBUG("RUGamePhaseRuck (%d) Assign contester position", player->GetAttributes()->GetIndex());
						defending_team.commital.Update();
					}

				}
			}

			// if ( !current_ruck.ruck_centre_locked || game->GetSpatialHelper()->GetPlayerToPointDistance( player, current_ruck.ruck_centre ) > 1.4f )
			// Too far away from the ruck, don't play the contest_tackle animation.
			// TODO: enter in and out of contesting from rucking state

		}
	}

	if (!requested_contesting_defender &&
		!defending_team.contesting_ball_player &&
		defending_team.oustanding_contest_ball_requests > 0 &&
		!current_ruck.breakdown_contest_called)
	{
		// check the ruck commital so far, if they have some dudes already binding and almost bound then they should be the
		// contesters rather than the tackler
		if (defending_team.commital.GetTotalBound() >= 1)
		{
			// if you dont have a tackle contester and contesting is still available and you want it
			// then check if there is someone binding into the ruck who is available to contest
			RUZoneLocation& bound_config = defending_team.locations[ RUCKLOC_BOUND ];

			ARugbyCharacter* first_bound_player = bound_config.positions[0].player;
			if (first_bound_player)
			{
				bool use_tackler = false;
				/// Woa, if our tackler was told to contest as well, we want to stop one of them! Otherwise we'll have 2 guys, 1 contest.
				RUActionTacklee* action_tacklee = current_ruck.breakdown_holder->GetActionManager()->GetAction<RUActionTacklee>();
				ARugbyCharacter* tackler = action_tacklee->GetTackleResult().tacklers[0];
				if ( tackler && tackler->GetActionManager()->IsActionRunning( ACTION_TACKLER ) )
				{
					RUActionTackler* action_tackler = tackler->GetActionManager()->GetAction<RUActionTackler>();
					if(action_tackler->ContestBallRequested())
					{

						// Crap he already has the ruck role?!
						if(tackler->GetRole()->RTTGetType() == RURoleRuck::RTTGetStaticType())
						{
							auto ruck_state = tackler->GetRole<RURoleRuck>()->GetState();
							if(ruck_state == RURoleRuck::RoleState::CONTESTING_BALL_GETUP)
							{
								use_tackler = true;
							}
							else
							{
								/// Stop it!
								tackler->GetRole<RURoleRuck>()->SetContestBallRequested( false );
							}
						}

						if(!use_tackler)
							action_tackler->AbortContestBallRequested();
					}
				}

				if(!use_tackler)
				{
					MABLOGDEBUG("RUGamePhaseRuck (%d) Assign first bound as ruck contester", first_bound_player->GetAttributes()->GetIndex());
					first_bound_player->GetRole<RURoleRuck>()->SetContestBallRequested( true );
				}
				else
				{
					MABLOGDEBUG("RUGamePhaseRuck Keeping tackler as assigned ruck contester");
				}
			}
		}
	}

	// check if we've lost our contesting player
	if (defending_team.contesting_ball_player)
	{
		if (!defending_team.contesting_ball_player->GetRole() || defending_team.contesting_ball_player->GetRole()->RTTGetType() != RURoleRuck::RTTGetStaticType() )
		{
			defending_team.contesting_ball_player = NULL;
		}
	}

	contesting_defender = defending_team.contesting_ball_player;
	defending_team.time_since_last_contest += time_step.delta_time.ToSeconds();

	bool attacker_holding_on = current_ruck.attacking.contest_hold_on;
	float time_since_first_contest		= current_ruck.first_contest_ball_time == MabTime(0) ? -1.0f : (time_step.abs_time - current_ruck.first_contest_ball_time).ToSeconds();
	float time_since_valid_ruck_formed	= current_ruck.valid_ruck_formed_time  == MabTime(0) ? -1.0f : (time_step.abs_time - current_ruck.valid_ruck_formed_time) .ToSeconds();
	bool illegal_to_contest = time_since_valid_ruck_formed >= 0.0f || current_ruck.first_attacker_at_ruck != NULL;
	bool illegal_to_hold = false;
	MABUNUSED( time_since_first_contest );
	MABUNUSED( time_since_valid_ruck_formed );
	MABUNUSED( illegal_to_contest );
	MABUNUSED( illegal_to_hold );

	RUGameEvents* events = game->GetEvents();
	RURandomNumberGenerator* rng = game->GetRNG();

//#define ENABLE_RUCK_CONTEST_DEBUG
#ifdef ENABLE_RUCK_CONTEST_DEBUG
	RURuckTeamState& attacking_team = GetCurrentAttackingTeamState();
	const float text_x = 20.0f;
	float text_y = 200.0f;
	const float LINE_INC = 12.0f;
	SIF_DEBUG_DRAW(SetText(SIFDebugDrawSingleFrameHandle(SIFDD_TEXT), text_x, text_y, MabString( 64, "%20s: %d", "Contest Requests", defending_team.oustanding_contest_ball_requests ).c_str() ) ); text_y += LINE_INC;
	SIF_DEBUG_DRAW(SetText(SIFDebugDrawSingleFrameHandle(SIFDD_TEXT), text_x, text_y, MabString( 64, "%20s: %0.1f", "Time Since First", time_since_first_contest ).c_str() ) ); text_y += LINE_INC;
	SIF_DEBUG_DRAW(SetText(SIFDebugDrawSingleFrameHandle(SIFDD_TEXT), text_x, text_y, MabString( 64, "%20s: %0.1f", "Time Since Valid", time_since_valid_ruck_formed ).c_str() ) ); text_y += LINE_INC;
	SIF_DEBUG_DRAW(SetText(SIFDebugDrawSingleFrameHandle(SIFDD_TEXT), text_x, text_y, MabString( 64, "%20s: %s", "Holding On", attacker_holding_on ? "True" : "False" ).c_str() ) ); text_y += LINE_INC;
	SIF_DEBUG_DRAW(SetText(SIFDebugDrawSingleFrameHandle(SIFDD_TEXT), text_x, text_y, MabString( 64, "%20s: %s", "Ill Holding On", illegal_to_hold ? "True" : "False" ).c_str() ) ); text_y += LINE_INC;
	SIF_DEBUG_DRAW(SetText(SIFDebugDrawSingleFrameHandle(SIFDD_TEXT), text_x, text_y, MabString( 64, "%20s: %s", "Ill Should Release", illegal_to_contest ? "True" : "False" ).c_str() ) ); text_y += LINE_INC;
	SIF_DEBUG_DRAW(SetText(SIFDebugDrawSingleFrameHandle(SIFDD_TEXT), text_x, text_y, MabString( 64, "%20s: %s", "Can Att Contest", GetCanAttackingTeamContest() ? "True" : "False" ).c_str() ) ); text_y += LINE_INC;
	SIF_DEBUG_DRAW(SetText(SIFDebugDrawSingleFrameHandle(SIFDD_TEXT), text_x, text_y, MabString( 64, "%20s: %s", "Can Def Contest", GetCanDefendingTeamContest() ? "True" : "False" ).c_str() ) ); text_y += LINE_INC;

	SIF_DEBUG_DRAW(SetText(SIFDebugDrawSingleFrameHandle(SIFDD_TEXT), text_x, text_y, MabString( 64, "%20s", "----------------" ).c_str() ) ); text_y += LINE_INC;
	SIF_DEBUG_DRAW(SetText(SIFDebugDrawSingleFrameHandle(SIFDD_TEXT), text_x, text_y, MabString( 64, "%20s: %d", "Att Join Requests", attacking_team.outstanding_join_requests.size() ).c_str() ) ); text_y += LINE_INC;
	SIF_DEBUG_DRAW(SetText(SIFDebugDrawSingleFrameHandle(SIFDD_TEXT), text_x, text_y, MabString( 64, "%20s: %d", "Def Join Requests", defending_team.outstanding_join_requests.size() ).c_str() ) ); text_y += LINE_INC;
	SIF_DEBUG_DRAW(SetText(SIFDebugDrawSingleFrameHandle(SIFDD_TEXT), text_x, text_y, MabString( 64, "%20s: %i", "Def Bound", defending_team.commital.GetTotalBound() ).c_str() ) ); text_y += LINE_INC;
	SIF_DEBUG_DRAW(SetText(SIFDebugDrawSingleFrameHandle(SIFDD_TEXT), text_x, text_y, MabString( 64, "%20s: %i", "Att Bound", attacking_team.commital.GetTotalBound() ).c_str() ) ); text_y += LINE_INC;

	RUCareerModeManager* careerManager = SIFApplication::GetApplication()->GetCareerModeManager();
	if(careerManager->IsActive() && careerManager->GetIsCareerModePro())
	{
		SIF_DEBUG_DRAW(SetText(SIFDebugDrawSingleFrameHandle(SIFDD_TEXT), text_x, text_y, MabString( 64, "%20s: %s", "Pro 1st in ruck", GetIsProInRuck() ? "True" : "False" ).c_str() ) ); text_y += LINE_INC;
		SIF_DEBUG_DRAW(SetText(SIFDebugDrawSingleFrameHandle(SIFDD_TEXT), text_x, text_y, MabString( 64, "%20s: %s", "Pro in ruck", GetIsProInRuck() ? "True" : "False" ).c_str() ) ); text_y += LINE_INC;
	}
#endif

	/// Process attempts to contest the ball
	if ( contesting_defender )
	{
		// Probability control variables for contesting tackles
		const static float PROB_CONTEST_SUCCESS_NO_HOLD = 0.2f;
		const static float PROB_CONTEST_SUCCESS_HOLD    = 0.02f;
		const static float PROB_PENALISED_CONTEST_WHILE_RUCK_FORMED = 0.25f;
		const static float PROB_PENALISED_HOLD_WHILE_CONTESTED = 0.4f;

		float prob_attempt_success = 0.0f;
		bool contest_succeeded = false;

		if ( defending_team.oustanding_contest_ball_requests > 0 )
		{
			LOGRUCKDEBUG( MabString( 128, "%d requests, holding_on = %s", defending_team.oustanding_contest_ball_requests, attacker_holding_on ? "true" : "false" ).c_str() )
		}

		// We've run out of time to contest here, and we have no more requests left over, so stop the player from contesting.
		// This triggers logic in the Ruck role class to play the failed animation etc.
		const static float NON_CONTEST_TIME = 0.3f;
		if ( defending_team.oustanding_contest_ball_requests == 0 && defending_team.time_since_last_contest > NON_CONTEST_TIME )
		{
			if ( contesting_defender->GetRole() && contesting_defender->GetRole()->RTTGetType() == RURoleRuck::RTTGetStaticType() )
			{
				RURoleRuck* ruck_role = contesting_defender->GetRole<RURoleRuck>();
				if ( ruck_role->IsContestingBall() )
				{
					events->breakdown_stopped_contesting( contesting_defender, current_ruck.breakdown_holder );


					// The following lines of code will re-enable ruck joining. Although im not entirely sure that we want that.

					// Reset our forwards area back to default. Im trying to stop our contester going back to the Forwards zone...
					//int newAreaNum = (int)(defending_team.team->GetStrategy().GetRuckCommittalCount());
					//defending_team.team->GetFormationManager()->OverrideAreaNumPlayers( RUCK_ZONE_NAME, newAreaNum, FMROLE_RUCK );

					// This will allow us to add players back into the ruck again
					//current_ruck.first_contest_ball_time = MabTime(0);
					//current_ruck.breakdown_contest_called = false;

					// we no longer have that contesting player
					defending_team.contesting_ball_player = NULL;
					contesting_defender = NULL;

					// Reset our contest requests
					//defending_team.oustanding_contest_ball_requests = 0;
				}
			}
		}

		while( defending_team.oustanding_contest_ball_requests > 0 )
		{
			/// Attempt to contest the ball
			prob_attempt_success = attacker_holding_on ? PROB_CONTEST_SUCCESS_HOLD : PROB_CONTEST_SUCCESS_NO_HOLD;
			contest_succeeded = rng->RAND_RANGED_CALL(float, 1.0f ) < prob_attempt_success;
			illegal_to_hold = attacker_holding_on && time_since_first_contest >= 0.0f;

			MABLOGDEBUG( MabString( 128, "[Contesting] %d contest req left, ill_hold=%d, ill_contest=%d, t since first contest=%0.2f, t since valid ruck=%0.2f",
				defending_team.oustanding_contest_ball_requests,
				illegal_to_hold,
				illegal_to_contest,
				time_since_first_contest,
				time_since_valid_ruck_formed ).c_str() );

			if ( game->GetGameSettings().game_settings.game_type == GAME_TRAINING && game->GetTutorialManager()->IsTutorialRunning() &&
				game->GetTutorialManager()->GetTutorialType() == RUTutorialType::CONTESTING_THE_BALL )
			{
				contest_succeeded = true;
				illegal_to_contest = false;
			}

			// Dewald WW - Debug always contest
			//contest_succeeded = true;
			//illegal_to_contest = false;

			/// Process the attempt and outcomes
			if ( illegal_to_contest )
			{
				if ( current_ruck.should_release_called && current_ruck.should_release_grace_time < 0.0f && rng->RAND_RANGED_CALL(float, 1.0f ) < PROB_PENALISED_CONTEST_WHILE_RUCK_FORMED )
				{
					/// If holding on illegal has been called this will take precedence
					if ( !(current_ruck.should_release_illegal_called || current_ruck.holding_on_illegal_called ) )
					{
						events->breakdown_should_release_illegal( contesting_defender, current_ruck.breakdown_holder );
						current_ruck.should_release_illegal_called = true;
						defending_team.oustanding_contest_ball_requests = 0;
						MABLOGDEBUG( MabString( 64, "breakdown_should_release_illegal" ).c_str() );
					}
				}
				else
				{
					/// Should only fire should release once
					if ( !(current_ruck.should_release_illegal_called || current_ruck.should_release_called || current_ruck.holding_on_illegal_called ) )
					{
						events->breakdown_should_release( contesting_defender, current_ruck.breakdown_holder );
						current_ruck.should_release_called = true;
						const static float SHOULD_RELEASE_MIN_TIME = 0.2f;
						const static float SHOULD_RELEASE_MAX_TIME = 0.35f;
						current_ruck.should_release_grace_time = SHOULD_RELEASE_MIN_TIME + game->GetRNG()->RAND_RANGED_CALL(float, SHOULD_RELEASE_MAX_TIME - SHOULD_RELEASE_MIN_TIME );
						// Clear out pending requests - as they need time to react once they have been warned
						defending_team.oustanding_contest_ball_requests = 0;
						MABLOGDEBUG( MabString( 64, "breakdown_should_release" ).c_str() );
						defending_team.commital.Update();
					}
				}
			}
			else if ( contest_succeeded )
			{
				RUGameState* game_state = game->GetGameState();

				events->breakdown_turnover( current_ruck.breakdown_holder, contesting_defender );
				events->breakdown_turnover_controller( current_ruck.defending.human_player_controller_index );

				MABLOGDEBUG( MabString( 64, "breakdown_turnover" ).c_str() );

				ruck_states[curr_state]->OnBallWasReleased();
				events->ruck_finished(curr_state);
				//game_state->BreakdownFinish(NULL);
				game_state->SetPhase( RUGamePhase::PLAY );
				setplay_state = RSP_STATE_FINISHED;
				defending_team.oustanding_contest_ball_requests = 0;
			}
			else if ( illegal_to_hold )
			{
				if ( rng->RAND_RANGED_CALL(float, 1.0f ) < PROB_PENALISED_HOLD_WHILE_CONTESTED )
				{
					if ( !current_ruck.holding_on_illegal_called )
					{
						events->breakdown_holding_on_illegal( current_ruck.breakdown_holder, contesting_defender );
						MABLOGDEBUG( MabString( 64, "breakdown_holding_on_illegal" ).c_str() );
						current_ruck.holding_on_illegal_called = true;
						defending_team.oustanding_contest_ball_requests = 0;
					}
				}
			}
			else if ( attacker_holding_on )
			{
				if ( !(current_ruck.holding_on_illegal_called || current_ruck.holding_on_called) )
				{
					events->breakdown_holding_on( current_ruck.breakdown_holder, contesting_defender );
					MABLOGDEBUG( MabString( 64, "breakdown_holding_on" ).c_str() );
					current_ruck.holding_on_called = true;
					defending_team.oustanding_contest_ball_requests = 0;
				}
			}

			/// Set the first contest ball time
			if ( time_since_first_contest < 0.0f )
			{
				if ( !( current_ruck.breakdown_contest_called ))
				{
					events->breakdown_contest( current_ruck.breakdown_holder, contesting_defender );
					current_ruck.breakdown_contest_called = true;
				}

				current_ruck.first_contest_ball_time = time_step.abs_time;
				time_since_first_contest = (time_step.abs_time - current_ruck.first_contest_ball_time).ToSeconds();

				/// Here we cancel any players already committing to a bound position so we can actually "get out" of the ruck in a reasonable time
				const RuckCommital& commital = GetCurrentRuck().GetCommital( contesting_defender->GetAttributes()->GetTeam() );
				int total_over_front_row_on_way_in = (int)(commital.GetTotalCommitting() - 3);

				if ( total_over_front_row_on_way_in > 0 )
				{
					GetCurrentDefendingTeamState().outstanding_leave_requests = total_over_front_row_on_way_in;
					GetCurrentDefendingTeamState().outstanding_join_requests.clear();
				}

				// TODO: Do we want to abort contest attempts if we already have players bound behind us?
			}

			// Dewald WW - Sanity, don't want it to go < 0, at times it goes below 0, which causes line 1795 to fail, and the contest animation loops infinitely.
			//if(defending_team.oustanding_contest_ball_requests > 0)
				--defending_team.oustanding_contest_ball_requests;
		}
	}
}

EHumanPlayerSlot RUGamePhaseRuck::GetAttackingTeamPlayerIndex()
{
	RURuckTeamState& attacking_team = GetCurrentAttackingTeamState();
	return attacking_team.human_player_index;
}

EHumanPlayerSlot RUGamePhaseRuck::GetDefendingTeamPlayerIndex()
{
	RURuckTeamState& defending_team = GetCurrentDefendingTeamState();
	return defending_team.human_player_index;
}

// Set up the HUD with sensible defaults.
void RUGamePhaseRuck::SetUpDefaultHUD( RURuckState& /*ruck_state*/ )
{
	EHumanPlayerSlot attacking_controller_index = GetAttackingTeamPlayerIndex();
	EHumanPlayerSlot defending_controller_index = GetDefendingTeamPlayerIndex();
	
	game->Get3DHudManager()->GetSetPieceIndicator()->SetColours( attacking_controller_index, defending_controller_index );
	game->Get3DHudManager()->GetSetPieceIndicator()->SetVisible(true);
}

void RUGamePhaseRuck::InitPowerContest()
{
	RURuckTeamState& attacking_team = GetCurrentAttackingTeamState();
	MABASSERT( attacking_team.ruck_state );

	RURuckState* ruck_state = attacking_team.ruck_state ? attacking_team.ruck_state : ruck_states[curr_state].get();
	MABASSERT( ruck_state );

	RUActionManager* action_manager = NULL;
	MABASSERT( ruck_state->breakdown_holder );

	if ( ruck_state->breakdown_holder )
	{
		action_manager = ruck_state->breakdown_holder->GetActionManager();
		MABASSERT( action_manager );
	}

	float impetus_ratio = 1.0f;
	if ( action_manager && action_manager->IsActionRunning( ACTION_TACKLEE ) )
	{
		const RUTackleResult& result = action_manager->GetAction<RUActionTacklee>()->GetTackleResult();
		impetus_ratio = (result.actual_tacklee_impetus / result.actual_tacklers_impetus);
		#ifdef ENABLE_OSD
				RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
				settings->PushDebugString( game, RUGameDebugSettings::DP_TACKLE, MabString( 24, "" ).c_str() );
				settings->PushDebugString( game, RUGameDebugSettings::DP_TACKLE, MabString( 24, "" ).c_str() );
				settings->PushDebugString( game, RUGameDebugSettings::DP_TACKLE, MabString( 24, "" ).c_str() );
				settings->PushDebugString( game, RUGameDebugSettings::DP_TACKLE, MabString( 24, "" ).c_str() );
				settings->PushDebugString( game, RUGameDebugSettings::DP_TACKLE, MabString( 24, "Tkl Imp A/D: %0.2f/%0.2f = %0.2f", result.actual_tacklee_impetus, result.actual_tacklers_impetus, impetus_ratio ).c_str() );
		#endif
	}

	const static float MIN_IMPETUS_CLAMP = 0.5f;
	const static float MAX_IMPETUS_CLAMP = 1.5f;

	MabMath::Clamp( impetus_ratio, MIN_IMPETUS_CLAMP, MAX_IMPETUS_CLAMP );

	// Set the initial power contest setting based on relative tackle impetus
	const float EVEN_START = 0.5f;
	static volatile float ATTACK_START_BIAS = 0.3f;

#ifdef FREEZE_RUCKS
	float power_bar = EVEN_START;			// 1.0 is equal weighting but give slight edge to attacking team
#else
	float power_bar = impetus_ratio - EVEN_START + ATTACK_START_BIAS;			// 1.0 is equal weighting but give slight edge to attacking team
	MabMath::Clamp( power_bar, 0.38f, 0.75f );
#endif //#ifdef FREEZE_RUCKS

	/// Set the initial power bar velocity based on the impetus ratio
	const static float SLIDER_DEFENCE_BIAS_IMPETUS_VEL_DEFENCE_FULL = -0.10f;
	const static float SLIDER_DEFENCE_BIAS_IMPETUS_VEL_ATTACK_FULL  =  0.20f;

	const static float SLIDER_ATTACK_BIAS_IMPETUS_VEL_DEFENCE_FULL  =  0.20f;
	const static float SLIDER_ATTACK_BIAS_IMPETUS_VEL_ATTACK_FULL	  =  0.40f;

	float impetus_pct = (impetus_ratio - MIN_IMPETUS_CLAMP) / (MAX_IMPETUS_CLAMP - MIN_IMPETUS_CLAMP);

	float slider_val = game->GetGameSettings().game_settings.slider_ruck_tackle_bonus;

	// Nick WWS 7s to Womens 13s //
	//if (game->GetGameSettings().game_settings.GameModeIsR7())
    //{
	//	slider_val *= 1.2f;
    //}

	MabMath::Clamp(slider_val, 0.0f, 1.0f);

	float impetus_vel_defence_full	= MabMath::Lerp( SLIDER_DEFENCE_BIAS_IMPETUS_VEL_DEFENCE_FULL, SLIDER_ATTACK_BIAS_IMPETUS_VEL_DEFENCE_FULL, slider_val );
	float impetus_vel_attack_full	= MabMath::Lerp( SLIDER_DEFENCE_BIAS_IMPETUS_VEL_ATTACK_FULL,  SLIDER_ATTACK_BIAS_IMPETUS_VEL_ATTACK_FULL,  slider_val );

	float power_bar_vel = MabMath::Lerp( impetus_vel_defence_full, impetus_vel_attack_full, impetus_pct );

#ifdef ENABLE_OSD
	RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
	settings->PushDebugString( game, RUGameDebugSettings::DP_TACKLE, MabString( 24, "Power Bar  : %0.2f, %0.2f", power_bar,power_bar_vel ).c_str() );
#endif

	power_contest.StartPowerContest(power_bar, power_bar_vel);
}

const static float TURNOVER_WIN_TIME = 3.0f;
void RUGamePhaseRuck::UpdatePowerContest( const MabTimeStep& time_step )
{
	RURuckTeamState& attacking_team = GetCurrentAttackingTeamState();
	RURuckTeamState& defending_team = GetCurrentDefendingTeamState();

	// about this update if we are in the wrong state, the contesting ball stuff can make this happen
	if (setplay_state == RSP_STATE_FINISHED)
		return;

	// Testing the pro player team winning when joining
	/*ARugbyCharacter* playa = SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer();
	if(playa)
	{
		if(playa->GetRole()->RTTGetType() == RURoleRuck::RTTGetStaticType())
		{
			int ruck_state = playa->GetRole<RURoleRuck>()->GetState();
			if(ruck_state == 3) //STATE_JOINING
			{
				if( attacking_team.team == playa->GetAttributes()->GetTeam() )
				{
					EndPowerContest( attacking_team.team );
				}
				else
				{
					EndPowerContest( defending_team.team );
				}
			}
		}
	}*/

	//MABASSERT( attacking_team.ruck_state );

	bool canTurnOver = game->GetGameTimer()->IsTurnoverAvailable();

	const static float SETPLAY_WAIT_TIME = 0.8f;
	if(setplay_time>SETPLAY_WAIT_TIME)
	{
		/// TYRONE : Bias ruck wins heavily towards the first 3 players committing to the ruck as apparently 91% of rucks
		/// are decided based on the 3 players
		/// Subsequent players will have no impact on acceleration
		RURuckState* ruck = attacking_team.ruck_state ? attacking_team.ruck_state : ruck_states[curr_state].get();
		MABASSERT( ruck );

		int attack_players  = (int)attacking_team.commital.bound.size();
		int defence_players = (int)defending_team.commital.bound.size();
		const static int MAX_ACCEL_PLAYERS_CONSIDERED = 3 + 1;	/// Extra one is tackler or tacklee

		/// Add tacklers and breakdown holder
		if ( ruck->breakdown_holder != NULL )
			attack_players++;
		defence_players += (int)ruck->tacklers.size();

		MabMath::ClampUpper( attack_players,  MAX_ACCEL_PLAYERS_CONSIDERED );
		MabMath::ClampUpper( defence_players, MAX_ACCEL_PLAYERS_CONSIDERED );

		// If we're not allowed a turn over, then basically make it impossible for the attacking team to lose here.
		if(!canTurnOver)
			attack_players += MAX_ACCEL_PLAYERS_CONSIDERED * 2;

		float committed_player_diff = (float) (attack_players - defence_players);
		float ruck_ability_diff     = attacking_team.team->GetDbTeam().GetNormalisedRuckAbility() - defending_team.team->GetDbTeam().GetNormalisedRuckAbility();

		// Give the attacking team a bonus for now, since we're not adjusting the winning values
		const static float PLAYER_ACCEL_DIFF = 0.1f;
		const static float RUCK_ABILITY_MODIFIER = 0.4f;		// 40% either way of player accel

		/// Apply power bar acceleration to current players
		float power_bar_accel = (float) (committed_player_diff * PLAYER_ACCEL_DIFF);

		/// +/- RUCK_ABILITY_MULT
#ifdef FREEZE_RUCKS
		MABUNUSED(ruck_ability_diff);
		power_bar_accel = 0.0f;
#else
		power_bar_accel *= (1.0f + MabMath::Sign( committed_player_diff ) * ruck_ability_diff * RUCK_ABILITY_MODIFIER);
#endif //#ifdef FREEZE_RUCKS

		// If we've hit the peak, shift to the next phase
		const static float HOLD_TIME = 0.0f;
		if (!power_contest.Update(time_step, power_bar_accel)  && setplay_time > HOLD_TIME )
		{
			// Define this to freeze the ruck (better than disabling attack/defense logic, good for testing formations in rucks.)
#ifdef FREEZE_RUCKS
			// Would have won the ruck at this stage
#else
			RUTeam* winning_team = power_contest.GetPowerbarPosition() >= 1.0f ? attacking_team.team : defending_team.team;
			EndPowerContest( winning_team );
#endif //#ifdef FREEZE_RUCKS
		}
	}

	// Teleport the ball to this location
	/*FVector ball_start = GetCurrentRuck().GetRuckOrigin()->GetOrigin();


	// Now work out the pop location
	FVector attack_exit = game->GetGameState()->GetFormationTarget( GSFMT_ATTACKLASTFEET )->GetOrigin();
	FVector defend_exit = game->GetGameState()->GetFormationTarget( GSFMT_DEFENCELASTFEET )->GetOrigin();

	FVector winning_exit = power_contest.GetPowerbarPosition() >= 1.0f ? attack_exit : defend_exit;


	SETDEBUGLINE( 80087344, ball_start, winning_exit, MabColour::DarkBlue, MabColour::DarkBlue );


	RUTeam* winning_team = power_contest.GetPowerbarPosition() >= 1.0f ? attacking_team.team : defending_team.team;

	FVector gate_exit = GetCurrentRuck().GetGateOrigin( winning_team->GetPlayDirection() )->GetOrigin() + FVector(0.0f, BALL_RADIUS, -(float)winning_team->GetPlayDirection() * 0.4f);

	SETDEBUGLINE( 80087344, ball_start, gate_exit, MabColour::Magenta, MabColour::Magenta );*/
}

bool RUGamePhaseRuck::GetExpectedTurnover()
{
	/// Returns true if a turnover is expected (team that took ball in is likely to lose)
	RUTeam* winning_team;
	float win_time = GetExpectedTimeBallClear( winning_team );
	return win_time < TURNOVER_WIN_TIME && winning_team != GetCurrentAttackingTeamState().team;
}

void RUGamePhaseRuck::GetRuckDefensiveGate( FVector& returned_left, FVector& returned_right )
{
	RURuckState& current_ruck = GetCurrentRuck();
	ERugbyPlayDirection defending_play_dir = game->GetGameState()->GetDefendingTeam()->GetPlayDirection();
	FVector defensive_origin = current_ruck.GetGateOrigin( defending_play_dir )->GetOrigin();
	float half_width = MabMath::Fabs(current_ruck.gate.left - current_ruck.gate.right) * 0.5f;

	returned_left.x = defending_play_dir == ERugbyPlayDirection::NORTH ? defensive_origin.x + half_width : defensive_origin.x - half_width;
	returned_left.y = 0.0f;
	returned_left.z = defensive_origin.z;

	returned_right.x = defending_play_dir == ERugbyPlayDirection::SOUTH ? defensive_origin.x + half_width : defensive_origin.x - half_width;
	returned_right.y = 0.0f;
	returned_right.z = defensive_origin.z;
}

struct RuckCentreSort
{
	RuckCentreSort( const FVector& ruck_centre ) : ruck_centre( ruck_centre ) {}
	inline float GetDistToGate( const ARugbyCharacter* a )
	{
		return SSMath::GetXZPointToPointDistance( a->GetMovement()->GetCurrentPosition(), ruck_centre );
	}
	bool operator() ( const ARugbyCharacter* a, const ARugbyCharacter* b )
	{
		return GetDistToGate( a ) < GetDistToGate( b );
	}
	FVector ruck_centre;
};

RUZoneJoinType RUGamePhaseRuck::ShouldAIJoin( RURuckTeamState& ruck_state, const MabTimeStep& time_step, bool& was_secondary_add )
{
	/// Make sure base AI requests are fired off
	ruck_state.time_till_next_ai_join -= time_step.delta_time.ToSeconds();
	was_secondary_add = false;

	if ( ruck_state.time_till_next_ai_join > 0.0f )
		return RUCK_JOIN_NONE;

	/// If we don't have any request - see if we should try and add some more
	if ( ruck_state.ai_join_requests.empty() )
	{
		/// No initial request - just monitor what opposition is doing and try and respond
		const RuckCommital& our_commital   = ruck_state.ruck_state->GetCommital( ruck_state.team );
		const RuckCommital& their_commital = ruck_state.ruck_state->GetCommital( static_cast<RUTeam*>(ruck_state.team->GetOppositionTeam()) );

		const static float MIN_TRY_CONTEST_TIME = 1.0f;
		const static int MAX_EXTRA_TO_COMMIT = 3;
		if ( ruck_state.should_commit && their_commital.GetTotalCommittingOrBound() >= our_commital.GetTotalCommittingOrBound() && power_contest.GetTimeTillBallRelease() > MIN_TRY_CONTEST_TIME )
		{
			// Always try and commit at least one more than what they have
			int n_extra_to_commit_over_theirs = game->GetRNG()->RAND_RANGED_CALL( int, MAX_EXTRA_TO_COMMIT + 1 );
			int n_total_to_commit = (int)(their_commital.GetTotalCommittingOrBound() + n_extra_to_commit_over_theirs);

			// Use our R7 const instead of R15 const
			size_t maxCount = // Nick WWS 7s to Womens 13s //game->GetGameSettings().game_settings.GameModeIsR7() ? MAX_PLAYERS_PER_SIDE_IN_RUCK_R7 : 
				MAX_PLAYERS_PER_SIDE_IN_RUCK;
			if ( n_total_to_commit > (int) maxCount )
				n_total_to_commit = (int) maxCount;

			int n_extra_to_add = (int)(n_total_to_commit - our_commital.GetTotalCommittingOrBound());

			/// Get the list of non bound players sorted by dist from gate so we can choose sensibly what type of add we should do
			SIFRugbyCharacterList players, non_bound_players;
			game->GetStrategyHelper()->GetTeamRoles( ruck_state.team, RURoleRuck::RTTGetStaticType(), players );
			SIFRugbyCharacterList::iterator it;

			/// Sort players by distance from ruck centre
			for( it = players.begin(); it != players.end(); ++it )
			{
				ARugbyCharacter* player = *it;
				RUZonePositionAssigned* zpa = player->GetPosAss();
				MABASSERT( zpa != NULL );
				if ( (zpa && zpa->GetPosition() && zpa->GetPosition()->GetLocation() != RUCKLOC_BOUND) || zpa == NULL )
					non_bound_players.push_back( player );
			}
			FVector gate_origin = ruck_state.GetGateOrigin()->GetOrigin();
			std::sort( non_bound_players.begin(), non_bound_players.end(), RuckCentreSort( gate_origin ) );

			for( int i = 0; i < n_extra_to_add; i++ )
			{
				RUZoneJoinType join_type;

				/// If we still have players to bind then select a sensible order
				if ( i < (int) non_bound_players.size() )
				{
					ARugbyCharacter* next_likely_player = non_bound_players[i];
					float dist_from_gate = SSMath::GetXZPointToPointDistance( gate_origin, next_likely_player->GetMovement()->GetCurrentPosition() );
					static const float JOIN_HEAVY_MAX_DIST = 7.0f;
					join_type = dist_from_gate < JOIN_HEAVY_MAX_DIST ? RUCK_JOIN_AGGRESSIVE : RUCK_JOIN_NORMAL;
				}
				else
				{
					join_type = game->GetRNG()->RAND_CALL( float ) < ruck_state.importance_to_win ? RUCK_JOIN_NORMAL : RUCK_JOIN_AGGRESSIVE;
				}

				ruck_state.ai_join_requests.push_back( join_type );
			}
		}

		was_secondary_add = true;
	}

	if ( !ruck_state.ai_join_requests.empty() )
		return ruck_state.ai_join_requests.front();

	return RUCK_JOIN_NONE;
}

void RUGamePhaseRuck::EndPowerContest( RUTeam* winning_team )
{
	current_ruck_winner = winning_team;
	ruck_use_it_timer.Reset();
	ruck_use_it_timer.SetEnabled( true );
	RUGameState *game_state = game->GetGameState();
	ASSBall* ball = game->GetBall();

	// Potentially changed side, so update attacking team
	game_state->SetAttackingTeam(winning_team);

	game_state->SetBallHolder(NULL);

	setplay_state = RSP_STATE_WAIT_RELEASE;
	UpdateAttackRunnerAssignments(winning_team);

	const float POP_LIMIT = 2.3f;

	// Teleport the ball to this location
	FVector ball_start = GetCurrentRuck().GetRuckOrigin()->GetOrigin();
	ball_start.y = BALL_RADIUS;
	ball->SetPositionAbsolute(ball_start);


	// Now work out the pop location
	FVector ball_exit = GetCurrentRuck().GetGateOrigin( winning_team->GetPlayDirection() )->GetOrigin()
							+ FVector(0.0f, 0.0f, -(float)winning_team->GetPlayDirection() * 0.4f);
	ball_exit.y = BALL_RADIUS;


		//game->GetGameState()->GetFormationTarget( GSFMT_ATTACKLASTFEET )->GetOrigin() :
		//game->GetGameState()->GetFormationTarget( GSFMT_DEFENCELASTFEET )->GetOrigin());
	//GetCurrentRuck().GetGateOrigin( winning_team->GetPlayDirection() )->GetOrigin() + FVector(0.0f, BALL_RADIUS, -(float)winning_team->GetPlayDirection() * 0.4f);

	// If we are close to the sideline, move the ball travel line to be out slightly
	// from the sideline so that the player that gathers it does not go into touch immediately.
	MabMath::Clamp( ball_exit.x, -HALF_FIELD_WIDTH + BALL_TRAVEL_LINE_OFFSET, HALF_FIELD_WIDTH - BALL_TRAVEL_LINE_OFFSET );

	// Direction toward the exit
	FVector direction = ball_exit - ball_start;
	float distance = direction.Magnitude();
	direction.Normalise();

	// So if our distance is too far away, clamp it.
	if(distance >= POP_LIMIT)
	{
		MABLOGDEBUG(MabString(0, "Distance was too far (%.2f), so we're clamping it to %.2f", distance, POP_LIMIT).c_str());
		ball_exit = ball_start + (direction * POP_LIMIT);
		direction = ball_exit - ball_start;
		distance = direction.Magnitude();
		direction.Normalise();
	}

	/// Set the travel time based on the speed of power bar
	// No idea what this does.
	float travel_time = power_contest.GetTimeTillBallRelease();
	MabMath::Clamp(travel_time, 0.0f, 3.0f);

	MABLOGDEBUG(MabString(0, "RUGamePhaseRuck::EndPowerContest -> Placing ball at (%.2f, %.2f, %.2f), popping out at (%.2f, %.2f, %.2f)",
		ball_start.x, ball_start.y, ball_start.z,
		ball_exit.x, ball_exit.y, ball_exit.z).c_str());
	MABLOGDEBUG(MabString(0, "RUGamePhaseRuck::EndPowerContest -> Dist placed away: %.2f", distance).c_str());

	// HES reporting a bug where the ball pops out 5-7 meters away from the ruck
	MABASSERT(distance < POP_LIMIT + 0.01f);

#ifdef DEBUG_RUCK_BALL_POP_OUT
	// Forcibly pause the game
	if(distance >= POP_LIMIT)
	{
		SIFDebug::force_game_pause = true;
	}
#endif

	game->GetEvents()->ruck_ball_released(winning_team, ball_exit, travel_time);

	/// If the team is not the same as the team that took the ball in then
	/// notify a possession change
	bool notify_posession_change = GetCurrentAttackingTeamState().team != winning_team;
	if ( notify_posession_change )
		game->GetEvents()->possession_change(winning_team);

	ball->CalculateTravelHeightData( travel_time, 0.15f);
	ball->SetTravelRotationSpeed( FVector::ZeroVector );
	ball->SetTravelLine( ball_start, ball_exit, travel_time, false );
	
	game->Get3DHudManager()->GetSetPieceIndicator()->SetVisible( false );

	/// Make formations update as soon as we have a result
	RURuckTeamState& attacking_team = GetCurrentAttackingTeamState();
	RURuckTeamState& defending_team = GetCurrentDefendingTeamState();

#ifdef ENABLE_PRO_MODE
	// When we're in pro mode, and the ruck 'finishes' we need to reset our player we're controlling since we set it to the ruck half which may not be our pro.
	if(game->GetGameSettings().game_settings.GetIsAProMode())
	{
		attacking_team.team->AssignBestPlayer();
		defending_team.team->AssignBestPlayer();
	}
#endif

	IRUOrigin* attacking_team_gate_origin = ruck_states[curr_state]->GetGateOrigin( attacking_team.team->GetPlayDirection() );
	IRUOrigin* defending_team_gate_origin = ruck_states[curr_state]->GetGateOrigin( defending_team.team->GetPlayDirection() );

	game_state->SetFormationTarget( ERugbyFormationTarget::ATTACKLASTFEET,	attacking_team.team == winning_team ? attacking_team_gate_origin : defending_team_gate_origin );
	game_state->SetFormationTarget( ERugbyFormationTarget::DEFENCELASTFEET,	attacking_team.team == winning_team ? defending_team_gate_origin : attacking_team_gate_origin );
}


/// Get the expected time until the ruck will be won and the expected winning team
float RUGamePhaseRuck::GetExpectedTimeBallClear( RUTeam*& winning_team )
{
	const static float MAX_TIME = 10.0f;
	float time = MAX_TIME;
	winning_team = NULL;

	/// If we haven't yet released the ball - estimate how long it will be before the power bar is won
	if ( setplay_state < RSP_STATE_WAIT_RELEASE)
	{
		winning_team = power_contest.GetPowerbarVelocity() < 0.0f? GetCurrentDefendingTeamState().team : GetCurrentAttackingTeamState().team;
		time = power_contest.GetTimeTillBallRelease();
	}
	else
	{
		/// Otherwise just use the travel line time
		time = game->GetBall()->GetRemainingTravelLineTime();
		winning_team = game->GetGameState()->GetAttackingTeam();
		MabMath::ClampLower( time, 0.0f );
	}

	MabMath::ClampUpper( time, MAX_TIME );

	return time;
}

void RUGamePhaseRuck::UpdateStateWaitRelease()
{
	UpdatePreJoin();
	UpdateAttackRunnerAssignments( current_ruck_winner );

	RUGameState *game_state = game->GetGameState();

	if(game_state->GetBallHolder()!=NULL)
	{
		RUGameEvents* game_events = game->GetEvents();
		ruck_states[curr_state]->OnBallWasReleased();
		game_events->ruck_finished(curr_state);
		game_state->SetPhase(RUGamePhase::PLAY);
		setplay_state = RSP_STATE_FINISHED;
	}

#ifdef ENABLE_GAME_DEBUG_MENU
	if ( SIFDebug::GetRulesDebugSettings()->ExperimentalUseItRuckRuleEnabled() )
	{
		if ( ruck_use_it_timer.GetNumTimerEventsRaised() > 0 )
		{
			setplay_state = RSP_STATE_FINISHED;
			Exit();

			// Go to scrum.
			RUGameState* state = game->GetGameState();
			RUTeam* restart_team = reinterpret_cast<RUTeam*>( state->GetAttackingTeam()->GetOppositionTeam() );
			if ( restart_team )
			{
				state->SetPlayRestartTeam( restart_team );
				state->SetAttackingTeam( restart_team );
			}

			state->SetPhase( RUGamePhase::SCRUM );
		}
	}
#endif
}


void RUGamePhaseRuck::OnBallPickedupFromRuck(ARugbyCharacter*, PickedUpContext context)
{
	if(context != PUC_FROM_RUCK)
		return;

	if (setplay_state == RSP_STATE_WAIT_RELEASE)
	{
		ruck_states[curr_state]->OnBallWasReleased();
		game->GetEvents()->ruck_finished(curr_state);
		game->GetGameState()->SetPhase(RUGamePhase::PLAY);
		setplay_state = RSP_STATE_FINISHED;
	}
}

void RUGamePhaseRuck::OnPhaseChanged()
{
	// Reset any rucks when we haven't returned to standard play - this stops a whole pile of unbind asserts when ruck roles exit
	RUGameState* game_state = game->GetGameState();
	RUGamePhase phase = game_state->GetPhase();
	RUGamePhase previous_phase = game_state->GetPreviousPhase();
	if ( (previous_phase == RUGamePhase::RUCK || previous_phase == RUGamePhase::PLAY) && !(phase == RUGamePhase::PLAY || phase == RUGamePhase::RUCK) )
		Reset();
}

void RUGamePhaseRuck::ReassignPlayerPosition(ARugbyCharacter* player)
{
	RURuckTeamState& ruck_state = player->GetAttributes()->GetTeam() == GetCurrentAttackingTeamState().team? GetCurrentAttackingTeamState() : GetCurrentDefendingTeamState();
	RUZonePosition *new_position;
	ruck_state.locations[ RUCKLOC_PREJOIN ].GetClosestPosition( player, new_position );
	RUZonePositionAssigned* position_assigned = player->GetPosAss();
	RUZonePosition* old_position = position_assigned->GetPosition();
	if (!new_position)
		return;
	if (old_position)
		old_position->AssignPlayer( NULL );
	if (old_position != new_position)
		new_position->AssignPlayer( player );
}


RUTeam* RUGamePhaseRuck::GetCurrentRuckAttackingTeam()
{
	return GetCurrentRuck().attacking.team;
}


void RUGamePhaseRuck::UpdateDebug()
{
#if 0
		long id = *(long*)this;
		int index = 0;
		float xStart = 5.0f;
		const float X_BUFFER = 100.0f;
		float xOffset = 0.0f;

		int ruck_count = 0;
		for(int r = 0; r < (int)ruck_states.size(); r ++)
		{
			RURuckState* ruck = ruck_states[ r ];

			if(ruck != NULL && ruck->ruck_id != -1)
			{
				MabColour colour = MabColour::White;
				if(ruck->ruck_id == GetCurrentRuck().ruck_id)
				{
					colour = MabColour::Green;
				}

				xOffset = xStart + X_BUFFER * ruck_count;

				SETDEBUGTEXTWORLDCOL( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "Tackler count: %i", ruck->tacklers.size()), colour );
				for(int i = 0; i < (int)ruck->tacklers.size(); i++)
				{
					SETDEBUGTEXTWORLDCOL( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "- Tackler: %s", (ruck->tacklers[i] != NULL ? ruck->tacklers[i]->GetAttributes()->GetCombinedName().c_str() : "None")), colour );
				}
				SETDEBUGTEXTWORLDCOL( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "Tacklee: %s", (ruck->breakdown_holder != NULL ? ruck->breakdown_holder->GetAttributes()->GetCombinedName().c_str() : "None")), colour );
				int attBoundCount = 0, defBoundCount = 0;

				SETDEBUGTEXTWORLDCOL( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "Att bound (%s):", ( ruck->breakdown_holder != NULL ? ruck->breakdown_holder->GetAttributes()->GetTeam()->GetDbTeam().GetName() : "None") ).c_str(), colour );
				for( size_t i = 0; i < ruck->attacking.locations.size(); i++ )
				{
					if ( !(ruck->attacking.locations[i].bind_state == NULL || strlen( ruck->attacking.locations[i].bind_state->config ) == 0 )  )
					{
						SETDEBUGTEXTWORLDCOL( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "	- %s", (ruck->attacking.locations[i].name) ), colour );

						for( size_t p = 0; p < ruck->attacking.locations[i].positions.size(); p++ )
						{
							if(ruck->attacking.locations[i].positions[p].player)
								SETDEBUGTEXTWORLDCOL( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "	-- (%i) %s", ruck->attacking.locations[i].positions[p].player->GetAttributes()->GetNumber(), ruck->attacking.locations[i].positions[p].player->GetAttributes()->GetCombinedName().c_str() ), colour );
						}
						attBoundCount ++;
					}
				}

				SETDEBUGTEXTWORLDCOL( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "Def bound (%s):", ((ruck->tacklers.size() > 0 && ruck->tacklers[0] != NULL) ? ruck->tacklers[0]->GetAttributes()->GetTeam()->GetDbTeam().GetName() : "None") ).c_str(), colour );
				for( size_t i = 0; i < ruck->defending.locations.size(); i++ )
				{
					if ( !(ruck->defending.locations[i].bind_state == NULL || strlen( ruck->defending.locations[i].bind_state->config ) == 0 )  )
					{
						SETDEBUGTEXTWORLDCOL( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "	- %s", (ruck->defending.locations[i].name) ), colour );

						for( size_t p = 0; p < ruck->defending.locations[i].positions.size(); p++ )
						{
							if(ruck->defending.locations[i].positions[p].player)
								SETDEBUGTEXTWORLDCOL( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "	-- (%i) %s", ruck->defending.locations[i].positions[p].player->GetAttributes()->GetNumber(), ruck->defending.locations[i].positions[p].player->GetAttributes()->GetCombinedName().c_str() ), colour );
						}
						defBoundCount ++;
					}
				}

				SETDEBUGTEXTWORLDCOL( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), "-----------------------", colour );
				SETDEBUGTEXTWORLDCOL( id + index++, FVector(xStart, 75.0f + (index * 20.0f), 0.0f), MabString(0, "Rucks: %i", ruck_count), colour );

				//SETDEBUGTEXTWORLDCOL( id + index++, FVector(5.0f, 150.0f + (index * 20.0f), 0.0f), MabString(0, "Att bound count: %i", attBoundCount) );
				//SETDEBUGTEXTWORLDCOL( id + index++, FVector(5.0f, 150.0f + (index * 20.0f), 0.0f), MabString(0, "Def bound count: %i", defBoundCount) );

				ruck_count ++;
			}
		}

		if(index > n_debug_text)
			n_debug_text = index;

		//SETDEBUGTEXTWORLD( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "Is pro in ruck: %s", (GetIsProInRuck() ? "Yes" : "No")) );
		/*SETDEBUGTEXTWORLD( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "Tackler count: %i", GetCurrentRuck().tacklers.size()) );
		for(int i = 0; i < (int)GetCurrentRuck().tacklers.size(); i++)
		{
			SETDEBUGTEXTWORLD( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "- Tackler: %s", (GetCurrentRuck().tacklers[i] != NULL ? GetCurrentRuck().tacklers[i]->GetAttributes()->GetCombinedName().c_str() : "None")) );
		}
		SETDEBUGTEXTWORLD( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "Tacklee: %s", (GetCurrentRuck().breakdown_holder != NULL ? GetCurrentRuck().breakdown_holder->GetAttributes()->GetCombinedName().c_str() : "None")) );
		int attBoundCount = 0, defBoundCount = 0;

		SETDEBUGTEXTWORLD( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "Att bound (%s):", GetCurrentRuck().breakdown_holder->GetAttributes()->GetTeam()->GetShortName().c_str()).c_str() );
		for( size_t i = 0; i < GetCurrentRuck().attacking.locations.size(); i++ )
		{
			if ( !(GetCurrentRuck().attacking.locations[i].bind_state == NULL || strlen( GetCurrentRuck().attacking.locations[i].bind_state->config ) == 0 )  )
			{
				SETDEBUGTEXTWORLD( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "	- %s", (GetCurrentRuck().attacking.locations[i].name) ) );

				for( size_t p = 0; p < GetCurrentRuck().attacking.locations[i].positions.size(); p++ )
				{
					if(GetCurrentRuck().attacking.locations[i].positions[p].player)
					SETDEBUGTEXTWORLD( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "	-- (%i) %s", GetCurrentRuck().attacking.locations[i].positions[p].player->GetAttributes()->GetNumber(), GetCurrentRuck().attacking.locations[i].positions[p].player->GetAttributes()->GetCombinedName().c_str() ) );
				}
				attBoundCount ++;
			}
		}

		SETDEBUGTEXTWORLD( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "Def bound (%s):", GetCurrentRuck().tacklers[i]->GetAttributes()->GetTeam()->GetShortName().c_str()).c_str() );
		for( size_t i = 0; i < GetCurrentRuck().defending.locations.size(); i++ )
		{
			if ( !(GetCurrentRuck().defending.locations[i].bind_state == NULL || strlen( GetCurrentRuck().defending.locations[i].bind_state->config ) == 0 )  )
			{
				SETDEBUGTEXTWORLD( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "	- %s", (GetCurrentRuck().defending.locations[i].name) ) );

				for( size_t p = 0; p < GetCurrentRuck().defending.locations[i].positions.size(); p++ )
				{
					if(GetCurrentRuck().defending.locations[i].positions[p].player)
					SETDEBUGTEXTWORLD( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), MabString(0, "	-- (%i) %s", GetCurrentRuck().defending.locations[i].positions[p].player->GetAttributes()->GetNumber(), GetCurrentRuck().defending.locations[i].positions[p].player->GetAttributes()->GetCombinedName().c_str() ) );
				}
				defBoundCount ++;
			}
		}

		SETDEBUGTEXTWORLD( id + index++, FVector(xOffset, 75.0f + (index * 20.0f), 0.0f), "-----------------------" );

		//SETDEBUGTEXTWORLD( id + index++, FVector(5.0f, 150.0f + (index * 20.0f), 0.0f), MabString(0, "Att bound count: %i", attBoundCount) );
		//SETDEBUGTEXTWORLD( id + index++, FVector(5.0f, 150.0f + (index * 20.0f), 0.0f), MabString(0, "Def bound count: %i", defBoundCount) );

		n_debug_text = index;*/

#endif

#if 0

	const int BASE = 99477;
	for( int i = 0; i < game->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam(); ++i )	SIF_DEBUG_DRAW( Remove3DLine( BASE + i ));
	for( SIFRugbyCharacterList::iterator it = attack_runners.begin(); it != attack_runners.end(); ++it )
	{
		ARugbyCharacter* player = *it;
		SETDEBUGLINE( BASE + player->GetAttributes()->GetIndex(), player->GetMovement()->GetCurrentPosition(), player->GetMovement()->GetCurrentPosition() + FVector::Y_AXIS * 2.0F, MabColour::Orange, MabColour::Orange );
	}

	RURuckTeamState& attacking_team = GetCurrentAttackingTeamState();
	RURuckTeamState& defending_team = GetCurrentDefendingTeamState();

	//long id = *(long*)this;
	//int index = 0;

	SETDEBUGTEXTWORLD( id + index++, FVector(5.0f, 320.0f, 0.0f), MabString(0, "Power-- : %f", power_bar) );
	SETDEBUGTEXTWORLD( id + index++, FVector(5.0f, 360.0f, 0.0f), MabString(0, "APlayers: %d", attacking_team.commital.bound.size() ));
	SETDEBUGTEXTWORLD( id + index++, FVector(5.0f, 370.0f, 0.0f), MabString(0, "DPlayers: %d", defending_team.commital.bound.size() ));

//	SETDEBUGTEXTWORLD( id + index++, FVector(5.0f, 380.0f, 0.0f), MabString(0, "firstteam: %s", GetCurrentRuck().first_team_in ? GetCurrentRuck().first_team_in == &attacking_team ? "Attack!" : "Defence" : "None" ));

//	MabVector<RURuckTeamState*>* teams_in = &GetCurrentRuck().teams_in;

//	for ( size_t i = 0; i < teams_in->size(); ++i)
//		SETDEBUGTEXTWORLD( id + index++, FVector(5.0f, 390.0f + i * 10.0f, 0.0f), MabString(0, "%d in : %s", i+1, teams_in->at(i) == &attacking_team ? "Attack!" : "Defence" ));

	n_debug_text = index;

	static const float LEFT			= 100.0f;
	static const float TOP			= 100.0f;
	static const float WIDTH		= 25.0f;
	static const float HEIGHT		= 150.0f;
	static const float LINE_OVERLAP = 20.0f;

	static const float RIGHT	= LEFT + WIDTH;
	static const float BOTTOM	= TOP + HEIGHT;

	SIF_DEBUG_DRAW(Set2DRect(
		SIFDebugDrawSingleFrameHandle(SIFDD_2DRECT), NULL,
		MabDebugDraw::Point2D( LEFT, TOP ),
		MabDebugDraw::Point2D( RIGHT, TOP + HEIGHT * (1.0f - power_bar) ),
		MabColour::Red
		)
		);

	SIF_DEBUG_DRAW(Set2DRect(
		SIFDebugDrawSingleFrameHandle(SIFDD_2DRECT), NULL,
		MabDebugDraw::Point2D( LEFT, BOTTOM - HEIGHT * power_bar ),
		MabDebugDraw::Point2D( RIGHT, BOTTOM ),
		MabColour::Green
		)
		);


	#define BASE 75484711
	int idx = BASE;

	const static float LENGTH = 5.0f;
	RURuckState& rs = GetCurrentRuck();
	MabRect<float>& gate = rs.gate;
	FVector& ruck_origin = rs.ruck_centre;
	SIF_DEBUG_DRAW( Set3DLine( idx++, ruck_origin, ruck_origin + FVector::Y_AXIS * 3.0f, MabColour::Blue, MabColour::Blue ) );

	SIF_DEBUG_DRAW( Set3DLine( idx++, FVector( gate.left, 0.0f, ruck_origin.z - LENGTH ), FVector( gate.left, 0.0f, ruck_origin.z + LENGTH ), MabColour::Blue, MabColour::Blue ) );
	SIF_DEBUG_DRAW( Set3DLine( idx++, FVector( gate.right, 0.0f, ruck_origin.z - LENGTH ), FVector( gate.right, 0.0f, ruck_origin.z + LENGTH ), MabColour::Blue, MabColour::Blue ) );
	SIF_DEBUG_DRAW( Set3DLine( idx++, FVector( ruck_origin.x - LENGTH, 0.0f, gate.bottom ), FVector( ruck_origin.x + LENGTH, 0.0f, gate.bottom ), MabColour::Green, MabColour::Green) );
	SIF_DEBUG_DRAW( Set3DLine( idx++, FVector( ruck_origin.x - LENGTH, 0.0f, gate.top ), FVector( ruck_origin.x + LENGTH, 0.0f, gate.top ), MabColour::Green, MabColour::Green) );


	FVector origins[2];
	origins[0] = rs.GetGateOrigin( rs.attacking.team->GetPlayDirection() )->GetOrigin();
	origins[1] = rs.GetGateOrigin( rs.defending.team->GetPlayDirection() )->GetOrigin();

	SIF_DEBUG_DRAW( Set3DLine( idx++, origins[0], origins[0] + FVector::Y_AXIS * 2.0f, MabColour::Green, MabColour::Green) );
	SIF_DEBUG_DRAW( Set3DLine( idx++, origins[1], origins[1] + FVector::Y_AXIS * 2.0f, MabColour::Red, MabColour::Red) );
	SIF_DEBUG_DRAW( Set3DLine( idx++, ruck_origin, ruck_origin + FVector::Y_AXIS * 2.0f, MabColour::Orange, MabColour::Orange) );

	n_debug_lines = idx;



	/// Display the anchor locations
	//MABUNUSED( defending_team );
	//RUPositionConfig* ruck_configs[] = { &attacking_team.locations[ RUCKLOC_PREJOIN ], &defending_team.locations[ RUCKLOC_PREJOIN ],
	//									 &attacking_team.locations[ RUCKLOC_BOUND ], &defending_team.locations[ RUCKLOC_BOUND ],
	//									 &attacking_team.locations[ RUCKLOC_HALFBACK ], &defending_team.locations[ RUCKLOC_HALFBACK ]
	//};
	//MabColour colours[] = { MabColour::White, MabColour::Black, MabColour::Red, MabColour::Blue, MabColour::Orange, MabColour::Yellow, MabColour::Green, MabColour::Cyan };
	//for( int i = 0; i < 6; i++ )
	//{
	//	RUPositionConfig* config = ruck_configs[i];
	//	for( size_t j = 0; j < config->positions.size(); j++ )
	//	{
	//		RUPositionEntry& entry = config->positions[j];
	//		FVector anchor = entry.GetOrigin();
	//		anchor.y = 0.0f;
	//		SIF_DEBUG_DRAW( Set3DLine( idx++, anchor, anchor + FVector(0.0f,2.0f,0.0f), colours[i], colours[i] ) );
	//		SIF_DEBUG_DRAW( SetText( idx++, anchor + FVector(0.0f, 2.1f, 0.0f ), MabString( 8, "%c", entry.join_index ).c_str() ));
	//	}
	//}



	/// Display the ruck requests
	//RuckState *ruck_states [2] = { &attacking_team, &defending_team };
	//MabString requests;
	//requests.reserve( 128 );

	//for( int i = 0; i < 2; i++ )
	//{
	//	requests = "";
	//
	//	RuckState* ruck_state = ruck_states[i];
	//	for( size_t j = 0; j < ruck_state->outstanding_join_requests.size(); j++ )
	//	{
	//		requests += STATE_STR[ ruck_state->outstanding_join_requests[j] ];
	//		requests += "\n";
	//	}

	//	FVector centre;
	//	float half_width;
	//	GetGate( ruck_state->team, centre, half_width );
	//	SIF_DEBUG_DRAW( SetText( BASE + 5 + i, centre + FVector(0.0f,2.0f,0.0f), requests.c_str() ) );
	//}

#endif
}

void RUGamePhaseRuck::ClearDebug()
{
#if 0
	long id = *(long*)this;
	for (int i = 0; i < n_debug_text; ++i)
		SIF_DEBUG_DRAW( RemoveText( id + i ) );
#endif

#if 0
	long id = *(long*)this;
	for (int i = 0; i < n_debug_text; ++i)
		SIF_DEBUG_DRAW( RemoveText( id + i ) );

	// Slightly different format of keys
	for (int i = BASE; i < n_debug_lines; ++i)
		SIF_DEBUG_DRAW( Remove3DLine( i ) );
#endif
}

RUZoneLocationType RUGamePhaseRuck::GetTargetLocationTypeFromJoinType( RUZoneJoinType join_type )
{
	RUZoneLocationType target_location_type = ZONELOC_NULL;
	if ( join_type == RUCK_JOIN_NORMAL || join_type == RUCK_JOIN_AGGRESSIVE )
		target_location_type = RUCKLOC_BOUND;
	else {
		MABBREAK();
	}

	return target_location_type;
}

void RUGamePhaseRuck::Reset()
{
#ifdef ENABLE_PRO_MODE
	isProInRuck = false;
	RUCareerModeManager* proModeMan = SIFApplication::GetApplication()->GetCareerModeManager();
	bool isProMode = proModeMan && proModeMan->IsActive() && proModeMan->GetIsCareerModePro();
	if(isProMode)
		SetRuckHumanPlayersAfterProJoinOrLeave();
#endif

	for( size_t i = 0; i < ruck_states.size(); i++ )
	{
		if (ruck_states[i])
		{
			ruck_states[i]->Reset(true);
		}
	}

	// When closing the application while in a match, the OverrideAreaNumPlayers below causes a crash dump to occur. When we terminate the sandbox world will be null. so check that here.
	if (!SIFApplication::GetApplication()->GetSandboxGame())
	{
		return;
	}

	//Can we null check things?
	if (game && game->GetWorldState() != WORLD_STATE::FREEING && game->GetWorldState() != WORLD_STATE::FREED)
	{
		/// Reset the number of ruck players committed for the formation
		for (int i = 0; i < 2; i++)
		{
			RUTeam* team = game->GetTeam(i);

			//Can we null check things?
			if (team)
			{
				//Can we null check things?
				if (team->GetFormationManager())
				{
					team->GetFormationManager()->OverrideAreaNumPlayers(RUCK_ZONE_NAME, -1, ERugbyFormationRole::RUCK, RUCK_ATTACK_FORMATION_NAME);
					team->GetFormationManager()->OverrideAreaNumPlayers(RUCK_ZONE_NAME, -1, ERugbyFormationRole::RUCK, RUCK_DEFENCE_FORMATION_NAME);
				}
			}
		}
	}
}

void RUGamePhaseRuck::CalculatePlayerImpact( const ARugbyCharacter* player, const RUZonePosition* player_entry, const FVector& impact_velocity )
{
	static const int MAX_NUM_LINKS = 2;
	for (int i = 0; i < MAX_NUM_LINKS; ++i)
	{
		RUZonePosition* forward_link = player_entry->GetForwardLink(i);
		RUZonePosition* side_link = player_entry->GetSideLink(i);
		RUZonePosition* back_link = player_entry->GetBackwardLink(i);

		if (forward_link && forward_link->player)
			ApplyImpactAnimation( forward_link, player, impact_velocity );

		if (side_link && side_link->player)
			ApplyImpactAnimation( side_link, player, impact_velocity );

		if (back_link && back_link->player)
			ApplyImpactAnimation( back_link, player, impact_velocity );
	}
}

void RUGamePhaseRuck::ApplyImpactAnimation( const RUZonePosition* target_player, const ARugbyCharacter* joining_player, const FVector& impact_velocity )
{
	RUZonePositionAssigned* pos_ass = joining_player->GetPosAss();
	MABASSERT( pos_ass != NULL );
	RUZonePosition* entry = pos_ass->GetPosition();
	MABASSERT( entry != NULL );

	const float relative_impact_size = FVector::DotProduct( impact_velocity.Unit(), (target_player->GetOrigin() - joining_player->GetMovement()->GetCurrentPosition()).Unit());

	//FVector impulse_dir = -(target_player->GetOrigin() - entry->GetOrigin()).Unit();
	FVector impulse_dir = -(target_player->player->GetMovement()->GetCurrentPosition() - entry->player->GetMovement()->GetCurrentPosition()).Unit();

	float impulse_angle = SSMath::CalculateAngle( impulse_dir );
	float facing_angle = joining_player->GetMovement()->GetCurrentFacingAngle();
	float entry_angle = MabMath::Rad2Deg( MabMath::AngleDelta( impulse_angle, facing_angle ) );

	//long id = (long)target_player->player;
	//SIF_DEBUG_DRAW( Set3DLine( id++, target_player->GetOrigin() + FVector(0.0, 1.0f,0.0f), target_player->GetOrigin() - impact_vector  + FVector(0.0, 1.0f,0.0f), MabColour::Red, MabColour::Green ) );
	//SIF_DEBUG_DRAW( Set3DLine( id++, entry->GetOrigin() + FVector(0.0, 1.0f,0.0f), entry->GetOrigin() + impulse_dir + FVector(0.0, 1.0f,0.0f), MabColour::Blue, MabColour::Yellow ) );
	//SIF_DEBUG_DRAW( Set3DLine( id++, entry->GetOrigin() + FVector(0.0, 1.0f,0.0f), entry->GetOrigin() + impact_velocity  + FVector(0.0, 1.0f,0.0f), MabColour::Orange, MabColour::Orange ) );

	const float impact_size = MabMath::Fabs( relative_impact_size ) * impact_velocity.ApproxMagnitude() * 0.25f;
	//MABLOGDEBUG("front impulse angle: %f, strength: %f, approx_mag: %f", entry_angle, impact_size, impact_velocity.ApproxMagnitude());

	static const char* IMPACT_ANIMATION_REQUEST = "impact";

	RUPlayerAnimation* animation = target_player->player->GetAnimation();
	animation->SetVariable( animation->GetImpactDirectionVariable(), entry_angle );
	animation->SetVariable(animation->GetImpactStrengthVariable(), 0.0f );
	// JOE: not doing impacts for now till i get the rest of rucks sorted
	MABUNUSED(impact_size);
	
	ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
	
	//if ( animation->IsAnimationAvailable( IMPACT_ANIMATION_REQUEST ) ) //#rc3_legacy_animation. Rewritten this to check for statemachine before calling
	if (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Ruck)
	{
		animation->PlayAnimation(IMPACT_ANIMATION_REQUEST);
	}
}

bool RUGamePhaseRuck::IsStandardPlay()
{
	return true;
}


void RUGamePhaseRuck::OnTeamAssignmentsChanged(RUTeam* /*new_team*/, SSHumanPlayer* /*human_player*/)
{
	//Skip this logic in be a pro as the player is reselected elsewhere
	if (SIFApplication::GetApplication()->GetCareerModeManager() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro())
	{
		return;
	}

	if (game->GetGameState()->GetPhase() == RUGamePhase::RUCK && setplay_state != RSP_STATE_NONE)
	{
		// Need to do the following chunk of logic to determine if we have an appropriate ruck half for our team when we switch controllers. When you remove controller index 0 from a team, and add another controller, it assigns that controller, but it would have assigned a player other than the ruck half to the new controller. Here we just double check the players on the team, and if we have the ruck half assigned to at least one of the human players.
		RURuckTeamState& attacking_team = GetCurrentAttackingTeamState();
		RURuckTeamState& defending_team = GetCurrentDefendingTeamState();


		SIFRugbyCharacterList attacking_ruck_halves;
		SIFRugbyCharacterList defending_ruck_halves;
		game->GetStrategyHelper()->GetTeamRoles( game->GetGameState()->GetAttackingTeam(), RURoleRuckScrumHalf::RTTGetStaticType(), attacking_ruck_halves );
		game->GetStrategyHelper()->GetTeamRoles( game->GetGameState()->GetDefendingTeam(), RURoleRuckScrumHalf::RTTGetStaticType(), defending_ruck_halves );
		MABASSERT(attacking_ruck_halves.size() == 1);
		MABASSERT(defending_ruck_halves.size() == 1);

		int attacking_ruck_half_count = 0;
		// find ruckhalfs only from my ruck
		for (MabVector<ARugbyCharacter*>::iterator i = attacking_ruck_halves.begin(); i != attacking_ruck_halves.end(); ++i)
			if (curr_state == (*i)->GetRole<RURoleRuckScrumHalf>()->GetRuckIndex())
				attacking_ruck_half_count++;
		MABASSERTMSG(attacking_ruck_half_count <= 1, "Too many RuckScrumHalfs for this Ruck o_O");

		int defending_ruck_half_count = 0;
		// find ruckhalfs only from my ruck
		for (ARugbyCharacter* defending_ruck_half : defending_ruck_halves)
			if (curr_state == defending_ruck_half->GetRole<RURoleRuckScrumHalf>()->GetRuckIndex())
				defending_ruck_half_count++;
		MABASSERTMSG(defending_ruck_half_count <= 1, "Too many RuckScrumHalfs for this Ruck o_O");




		//////////
		// Attacking side
		if(attacking_team.team->GetNumHumanPlayers() > 0)
		{
			bool valid_human_found = false;
			for (int i = 0; i < attacking_team.team->GetNumHumanPlayers(); ++i)
			{
				if (attacking_team.team->GetHumanPlayer(i)->GetRugbyCharacter())
				{
					valid_human_found = true;
					break;
				}
			}

			// This team has human players, but for some reason none of them were assigned to a ruck half??
			if(!valid_human_found ) 
			{
				for (int i = 0; i < attacking_team.team->GetNumHumanPlayers(); ++i)
				{
					if (attacking_team.team->GetHumanPlayer(i)->GetRugbyCharacter())
					{
						if (attacking_ruck_halves.size() > 0)
						{
							attacking_team.team->GetHumanPlayer(i)->SetRugbyCharacter(attacking_ruck_halves[0]);
						}
					}
				}
			}
		}

		//////////
		// Defending side
		if(defending_team.team->GetNumHumanPlayers() > 0)
		{
			bool valid_human_found = false;
			for (int i = 0; i < defending_team.team->GetNumHumanPlayers(); ++i)
			{
				if (defending_team.team->GetHumanPlayer(i)->GetRugbyCharacter() && (defending_team.team->GetHumanPlayer(i)->GetRugbyCharacter()->GetRole()->RTTGetType() == RURoleRuckScrumHalf::RTTGetStaticType()))
				{
					valid_human_found = true;
					break;
				}
			}

			// This team has human players, but for some reason none of them were assigned to a ruck half??
			if(!valid_human_found && (!SIFApplication::GetApplication()->GetCareerModeManager() || !SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro()))		//Added checks to see if in be a pro mode, as no player is needed as the scrum half in be a pro
			{
				for (int i = 0; i < defending_team.team->GetNumHumanPlayers(); ++i)
				{
					if (defending_team.team->GetHumanPlayer(i)->GetRugbyCharacter())
					{
						if (defending_ruck_halves.size() > 0)
						{
							defending_team.team->GetHumanPlayer(i)->SetRugbyCharacter(defending_ruck_halves[0]);
						}
					}
				}
			}
		}

		SetRuckHumanPlayers();
		// when a player changes teams, update our ruck hud
		SetUpDefaultHUD( GetCurrentRuck() );
	}
}

// Similar to the normal human settings function, except this only gets called when a pro player joins or leaves a ruck
void RUGamePhaseRuck::SetRuckHumanPlayersAfterProJoinOrLeave()
{
	/*MABASSERTMSG(SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro(), "We shouldn't run this if we're not in a pro game");

#ifdef ENABLE_PRO_MODE
	if(!game->GetGameSettings().game_settings.GetIsAProMode()) return;
#endif

	RURuckTeamState& attacking_team = GetCurrentAttackingTeamState();
	RURuckTeamState& defending_team = GetCurrentDefendingTeamState();

	int attacking_player_index = -1;
	int defending_player_index = -1;

	int attacking_player_controller_index = -1;
	int defending_player_controller_index = -1;

	// When our pro is inside the ruck, we need to set our human player index
#ifdef ENABLE_PRO_MODE
	if(isProInRuck)
	{
		// Our pro player is on the attacking team
		if(attacking_team.team->GetProPlayer())
		{
			attacking_player_index				= attacking_team.team->GetProPlayer()->GetHumanPlayer()->GetIndex();
			attacking_player_controller_index	= attacking_team.team->GetProPlayer()->GetHumanPlayer()->GetControllerIndex();
		}
		// Just double check that they are in fact on the defending team
		else if(defending_team.team->GetProPlayer())
		{
			defending_player_index				= defending_team.team->GetProPlayer()->GetHumanPlayer()->GetIndex();
			defending_player_controller_index	= defending_team.team->GetProPlayer()->GetHumanPlayer()->GetControllerIndex();
		}
		else
		{
			MABBREAKMSG("We're trying to set the human player for our ruck during a pro game, but we can't find our pro on either side?");
		}

		//game->Get3DHudManager()->GetSetPieceIndicator()->SetVisible(true);
	}
	else
	{
		//game->Get3DHudManager()->GetSetPieceIndicator()->SetVisible(false);
	}
#endif

	game->Get3DHudManager()->GetSetPieceIndicator()->SetColours( attacking_player_controller_index, defending_player_controller_index );

	// Set the index values
	attacking_team.human_player_index = attacking_player_index;
	attacking_team.human_player_controller_index = attacking_player_controller_index;

	defending_team.human_player_index = defending_player_index;
	defending_team.human_player_controller_index = defending_player_controller_index;*/
}

void RUGamePhaseRuck::SetRuckHumanPlayers()
{
	RURuckTeamState& attacking_team = GetCurrentAttackingTeamState();
	RURuckTeamState& defending_team = GetCurrentDefendingTeamState();

	EHumanPlayerSlot attacking_player_index = EHumanPlayerSlot::INVALID_HUMAN;
	EHumanPlayerSlot defending_player_index = EHumanPlayerSlot::INVALID_HUMAN;

	int attacking_player_controller_index = -1;
	int defending_player_controller_index = -1;
	
#ifdef ENABLE_PRO_MODE
	// Check if we're playing a Pro Mode game - Dewald WW
	bool isProMode = game->GetGameSettings().game_settings.GetIsAProMode();
	if( isProMode && SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayerOnField() )
	{
		ARugbyCharacter* pro_player = SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer();
		if(attacking_team.team == pro_player->GetAttributes()->GetTeam())
		{
			MABLOGDEBUG("Pro player is on the attacking side");

			if(attacking_team.team->GetHumanPlayer(0))
			{
				attacking_player_index = attacking_team.team->GetHumanPlayer(0)->GetPlayerSlot();
				attacking_player_controller_index = attacking_team.team->GetHumanPlayer(0)->GetControllerIndex();
			}
		}
		else
		{
			MABLOGDEBUG("Pro player is on the defending side");

			if(defending_team.team->GetHumanPlayer(0))
			{
				defending_player_index = defending_team.team->GetHumanPlayer(0)->GetPlayerSlot();
				defending_player_controller_index = defending_team.team->GetHumanPlayer(0)->GetControllerIndex();
			}
		}
	}
	else
#endif
	{
		for (int i = 0; i < attacking_team.team->GetNumHumanPlayers(); ++i)
		{
			if (attacking_team.team->GetHumanPlayer(i)->GetRugbyCharacter() && (attacking_team.team->GetHumanPlayer(i)->GetRugbyCharacter()->GetRole()->RTTGetType() == RURoleRuckScrumHalf::RTTGetStaticType()))
			{
				attacking_player_index = attacking_team.team->GetHumanPlayer(i)->GetPlayerSlot();
				attacking_player_controller_index = attacking_team.team->GetHumanPlayer(i)->GetControllerIndex();
				break;
			}
		}

		for (int i = 0; i < defending_team.team->GetNumHumanPlayers(); ++i)
		{
			if (defending_team.team->GetHumanPlayer(i)->GetRugbyCharacter() && (defending_team.team->GetHumanPlayer(i)->GetRugbyCharacter()->GetRole()->RTTGetType() == RURoleRuckScrumHalf::RTTGetStaticType()))
			{
				defending_player_index = defending_team.team->GetHumanPlayer(i)->GetPlayerSlot();
				defending_player_controller_index = defending_team.team->GetHumanPlayer(i)->GetControllerIndex();
				break;
			}
		}
	}

	//  Set up our indices
	attacking_team.human_player_index = attacking_player_index;
	attacking_team.human_player_controller_index = attacking_player_controller_index;

	defending_team.human_player_index = defending_player_index;
	defending_team.human_player_controller_index = defending_player_controller_index;
}

void RUGamePhaseRuck::InitRuckAI( RURuckTeamState &team_state )
{
	team_state.importance_to_win = team_state.team->GetTurnOverImportance();
	team_state.should_commit = game->GetRNG()->RAND_CALL( float ) < team_state.importance_to_win;


	const static int COMMIT_PLAYER_COUNTS[][2] = { { 0, 3 }, { 1, 4 }, { 3, 7 } };
	int win_importance  = GetImportanceBand( team_state.importance_to_win );
	int ai_commit  = COMMIT_PLAYER_COUNTS[ win_importance ] [0] + game->GetRNG()->RAND_RANGED_CALL( int, COMMIT_PLAYER_COUNTS[ win_importance ][1] - COMMIT_PLAYER_COUNTS[ win_importance  ][0] + 1 );

	team_state.time_till_next_ai_join = GetNextAIJoinTime( false );

	/// Work out attack commit
	int ai_commit_normal     = game->GetRNG()->RAND_RANGED_CALL( int, ai_commit + 1);

	/// Push normal requests for both teams
	team_state.outstanding_join_requests.clear();
	team_state.ai_join_requests.clear();

	if ( team_state.team->GetNumHumanPlayers() == 0 )
		team_state.outstanding_join_requests.push_back( RUCK_JOIN_NORMAL );
	else
	{
		// Add request based on team strategy urgency
		//RUTeamStrategy::RUCK_URGENCY_OPTION ruo = team_state.team->GetStrategy().GetRuckUrgencyOption();
		//const static int DEFAULT_COMMIT_COUNTS[3]		= { 1,		2,		4		}; // low, normal, high
		//const static float COMMIT_PROB_AGGRESSIVE[3]	= { 0.1f,	0.3f,	0.6f	};
		//
		//int n_commit = DEFAULT_COMMIT_COUNTS[ ruo ];
		//for( int i = 0; i < n_commit; i++ )
		//{
		//	RUCK_JOIN_TYPES join_type = game->GetRNG()->RandFloat(1.0f) < COMMIT_PROB_AGGRESSIVE[ ruo ] ? RUCK_JOIN_AGGRESSIVE : RUCK_JOIN_NORMAL;
		//	team_state.outstanding_join_requests.push_back( join_type );
		//}
	}

	team_state.outstanding_leave_requests = 0;
	team_state.oustanding_contest_ball_requests = 0;
	team_state.contest_hold_on = false;

	/// Add the ai join requests
	for( int i = 0; i < ai_commit; ++i )
	{
		team_state.ai_join_requests.push_back( i < ai_commit_normal ? RUCK_JOIN_NORMAL : RUCK_JOIN_AGGRESSIVE );
	}

}

void RUGamePhaseRuck::UpdateAttackRunnerAssignments( RUTeam* winning_team )
{
	if ( winning_team == NULL )
		return;

	/// Free up any ruck players to move into the defensive line - for the defending team
	GetCurrentAttackingTeamState().commital.Update();
	GetCurrentDefendingTeamState().commital.Update();
	RUTeam* losing_team = static_cast< RUTeam* >( winning_team->GetOppositionTeam() );
	const RuckCommital& losing_commital = GetCurrentRuck().GetCommital( losing_team );
	losing_team->GetFormationManager()->OverrideAreaNumPlayers( RUCK_ZONE_NAME, (int)(losing_commital.GetTotalBound() - 1), ERugbyFormationRole::RUCK ); // We subtract one because the first to ruck area has one ruck role also

	/// Free up any ruck players to move into the send runner line - for the attacking team
	const RuckCommital& winning_commital = GetCurrentRuck().GetCommital( winning_team );
	winning_team->GetFormationManager()->OverrideAreaNumPlayers( RUCK_ZONE_NAME, (int)(winning_commital.GetTotalBound() - 1), ERugbyFormationRole::RUCK ); // We subtract one because the first to ruck area has one ruck role also
	winning_team->GetFormationManager()->OverrideAreaNumPlayers( SEND_RUNNER_ZONE_NAME, 	(int)(winning_commital.GetPeakTotalRuckPlayers() - winning_commital.GetTotalBound()), ERugbyFormationRole::RUCKSENDRUNNER );
}

/// Functor for sorting runner distances
struct RunnerXDistSort
{
	RunnerXDistSort( const FVector& ball_pos ) : ball_pos( ball_pos ) {}
	inline float GetXDelta( const ARugbyCharacter* a ) { return MabMath::Fabs( a->GetMovement()->GetCurrentPosition().x - ball_pos.x ); }
	bool operator() ( const ARugbyCharacter* a, const ARugbyCharacter* b )
	{
		return GetXDelta( a ) < GetXDelta( b );
	}
	FVector ball_pos;
};

void RUGamePhaseRuck::GetAttackRunners( SIFRugbyCharacterList& returned_players, ERugbyPlayDirection play_dir, const FVector& ball_pos, bool on_left ) const
{
	const static float MAX_RUNNER_DIST_AWAY = 13.0f;

	for ( SIFRugbyCharacterList::const_iterator iter = attack_runners.begin(); iter != attack_runners.end(); ++iter )
	{
		// Find a player to the side of the ball holder.
		ARugbyCharacter* player = *iter;
		const FVector& player_position = player->GetMovement()->GetCurrentPosition();
		bool player_is_on_left = (MabMath::Sign( player_position.x - ball_pos.x ) * play_dir) > 0.0f;
		bool player_in_front = (player_position.z - ball_pos.z) * play_dir > 0.0f;

		if ( player_in_front || player->GetMovement()->GetMotionSource() != NULL )
			continue;
		if ( (ball_pos - player->GetMovement()->GetCurrentPosition()).Magnitude() > MAX_RUNNER_DIST_AWAY )
			continue;

		//MABASSERT( player->GetRole()->RTTGetType() == RURoleRuckSendRunner::RTTGetStaticType() );

		if ( (player_is_on_left && on_left) || (!player_is_on_left && !on_left) )
			returned_players.push_back( player );
	}

	// Sort the lists by x distance from ball
	std::sort( returned_players.begin(), returned_players.end(), RunnerXDistSort( ball_pos ) );
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

#if PLATFORM_WINDOWS || PLATFORM_XBOX360 || PLATFORM_XBOXONE
#pragma warning( disable :4355 )
#endif

RURuckState::RURuckState()
: game( NULL )
, gate()
, ruck_width(0.0f)
, ruck_height(0.0f)
, ruck_radius(0.0f)
, ruck_coll_centre(FVector::ZeroVector)
, breakdown_holder( NULL )
, tacklers()
, first_team_in_ruck(NULL)
, first_attacker_at_ruck(NULL)
, first_defender_at_ruck(NULL)
, first_contest_ball_time( MabTime( 0 ) )
, valid_ruck_formed_time( MabTime( 0 ) )
, should_release_called( false )
,should_release_grace_time(0.0)
, should_release_illegal_called( false )
, holding_on_called( false )
, holding_on_illegal_called( false )
, breakdown_contest_called( false )
, ruck_centre( FVector::ZeroVector)
, ruck_centre_locked( false )
, last_ruck_centre_update_time( 0.0f )
, ruck_id(-1)
//,gate_origin()
, ruck_origin( this )
, attacking()
, defending()
, first_player_joined( false )
, running( false )
, initialised( false )
, time_in_state_after_release( 0.0f )
, ball_has_been_released( false )
{
	gate_origin[0] = MabMemNew( MabMemGetDefaultObjectHeap( this ) ) RURuckState::GateOrigin( this, ERugbyPlayDirection::NORTH );
	gate_origin[1] = MabMemNew( MabMemGetDefaultObjectHeap( this ) ) RURuckState::GateOrigin( this, ERugbyPlayDirection::SOUTH );
	//teams_in.reserve(10);
}

RURuckState::~RURuckState()
{
	MabMemDelete(gate_origin[0]);
	MabMemDelete(gate_origin[1]);
}

void RURuckState::Destroy()
{
	// TODO : Do I need to destroy remove from the database?
}

const static float UNINIT_GATE_VAL = 1e5f;
#ifdef ENABLE_RUGED
const static bool UPDATE_RUGED_FAST = false;
#endif

void RURuckState::Enter( SIFGameWorld* game_, int ruck_state_id)
{
	Reset( false );

	game = game_;
	ruck_id = ruck_state_id;
	running = true;

#ifdef ENABLE_RUGED
	if ( UPDATE_RUGED_FAST )
		game->SetRugEdDebugUpdateInterval( SIMULATION_INTERVAL );
#endif
}

void RURuckState::Update( float delta_time )
{
	// Keep track of how long the state has been running after the ball was released.
	if ( initialised && running && ball_has_been_released )
		time_in_state_after_release += delta_time;

	/// Update our running status
	/// If all rucks have no config then abort
	if ( initialised && running )
	{
		if( attacking.AreAllPositionsFreeNow(/*true*/) && defending.AreAllPositionsFreeNow(/*true*/) )
			Exit();
	}

	if ( initialised && running )
	{
		// Suspected potential NMA to happen here. We might have some players stuck on the ground.
		if( ball_has_been_released && time_in_state_after_release > 7.0f )
		{
			if( (int)attacking.commital.GetTotalBound() == 0 && (int)defending.commital.GetTotalBound() == 0)
			{
				//MABBREAKMSG( "NMA detector! Looks like we may have some player stuck on the ground? Forcing an exit on this ruck state." );
				attacking.AreAllPositionsFreeNow(true);
				defending.AreAllPositionsFreeNow(true);

				Exit();
			}
		}



		/*if(	defending.commital.GetTotalBound() == 0 &&
			attacking.commital.GetTotalBound() == 0 )
		{
			if(time_in_state_after_release > 3.0f)
			{
				MABASSERT(ball_has_been_released); // Make sure the balls' been released.
				MABLOGDEBUG("PLAYERS_STUCK_DEBUG: We've been in this state for a while, with no-one bound. Early out.");
				MABBREAKMSG("Do we have players stuck on the ground? (ball released, no-one bound) - Dewald Debugging this");
				bool earlyOut = false;
#if PLATFORM_XBOXONE
				// On Xbox One we just exit, because this is the only platform we have this issue on...
				earlyOut = true;
#else

#endif // PLATFORM_XBOXONE
				// IF this happens on other platforms.... we can set this to true for some more debugging.
				if(earlyOut)
				{
					// Debugging
					MABLOGDEBUG("Attacking positions free?");
					attacking.AreAllPositionsFreeNow( true );

					MABLOGDEBUG("Defending positions free?");
					defending.AreAllPositionsFreeNow( true );

					Exit();
				}
			}
		}*/
	}

	if ( !running )
		return;

#ifdef ENABLE_GAME_DEBUG_MENU
	disable_ai_ruck = SIFDebug::GetGameDebugSettings()->GetDisableRuckAI();
#endif

	UpdateGate( delta_time );
}

void RURuckState::Exit()
{
	Reset( true );
#ifdef ENABLE_RUGED
	if ( game && UPDATE_RUGED_FAST ) game->SetRugEdDebugUpdateInterval( -1.0f );
#endif
}

void RURuckState::Reset( bool clear_ruck_participants )
{
	if ( clear_ruck_participants )
	{
		tacklers.clear();
		breakdown_holder = NULL;
	}

	if (game && game->GetMovement())
	{
		game->GetMovement()->UnregisterStaticCollidable(this);
	}

	gate.left = gate.right = gate.top = gate.bottom = UNINIT_GATE_VAL;
	ruck_width = ruck_height = ruck_radius = 0.0f;
	ruck_coll_centre = FVector::ZeroVector;
	first_team_in_ruck = NULL;
	first_attacker_at_ruck = NULL;
	first_defender_at_ruck = NULL;
	first_contest_ball_time = MabTime( 0 );
	valid_ruck_formed_time = MabTime( 0 );
	should_release_called = false;
	should_release_grace_time = 0.0f;
	should_release_illegal_called = false;
	holding_on_called = false;
	holding_on_illegal_called = false;
	breakdown_contest_called = false;
	ruck_centre = FVector::ZeroVector;
	ruck_centre_locked = false;
	last_ruck_centre_update_time = 0.0f;
	attacking.Reset( clear_ruck_participants );
	defending.Reset( clear_ruck_participants );
	first_player_joined = false;
	running = false;
	initialised = false;

	// debugging
	time_in_state_after_release = 0.0f;
	ball_has_been_released = false;
}

//Debugging Ticket 11490
bool RURuckState::AreAllPositionsFreeNow( bool _debug )
//bool RURuckState::AreAllPositionsFreeNow()
{
	return attacking.AreAllPositionsFreeNow( _debug ) && defending.AreAllPositionsFreeNow( _debug );
	//return attacking.AreAllPositionsFreeNow() && defending.AreAllPositionsFreeNow();
}

void RURuckState::UpdateGate( float delta_time )
{
	/// Update the gate

	/// Create a list of all players in the ruck
	SIFRugbyCharacterList test;
	int playersPerTeam = game->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();

	test.reserve( playersPerTeam/*NUM_PLAYERS_PER_TEAM*/ * 2 );
	test.insert( test.end(), attacking.commital.bound.begin(), attacking.commital.bound.end() );
	test.insert( test.end(), defending.commital.bound.begin(), defending.commital.bound.end() );

	// Including the tackler(s) and tacklee in the rect often looks really weird.
	// E.g. the shoulder charge tackle causes the ruck to form in the middle of the two players.
	/*if ( breakdown_holder )
		test.push_back( breakdown_holder );
	if ( !tacklers.empty() )
	{
		for (SIFRugbyCharacterList::iterator i = tacklers.begin(); i != tacklers.end(); ++i)
		{
			ARugbyCharacter* player = (*i);
			if (player->GetActionManager()->IsActionRunning(ACTION_TACKLER))
				test.push_back(player);
		}
	}*/


	//---------------------------------------------------
	/// Work out the current gate dimensions
	//---------------------------------------------------
	MabRect< float > curr_gate;
	game->GetSpatialHelper()->GetPlayerGroupXZBounds( test, curr_gate, true );

	/// Now apply this to the new gate if dimensions have changed within a tolerance
	bool gate_initialised = gate.left != UNINIT_GATE_VAL;
	bool ruck_centre_initialised = gate_initialised;

	const static float MIN_GATE_MOVEMENT = 0.2f;
	if (   MabMath::Fabs(gate.top    - curr_gate.top   )  > MIN_GATE_MOVEMENT
		|| MabMath::Fabs(gate.bottom - curr_gate.bottom)  > MIN_GATE_MOVEMENT
		|| MabMath::Fabs(gate.left   - curr_gate.left  )  > MIN_GATE_MOVEMENT
		|| MabMath::Fabs(gate.right  - curr_gate.right )  > MIN_GATE_MOVEMENT
		|| !gate_initialised )
	{
		gate_initialised = true;
		gate = curr_gate;
	}

	/// Set the ruck centre if it has not been initialised
	if ( !ruck_centre_initialised )
	{
		ruck_centre.Set( (gate.left + gate.right) * 0.5f, 0.0f, (gate.bottom + gate.top) * 0.5f );
		last_ruck_centre_update_time = 0.0f;
		game->GetMovement()->RegisterStaticCollidable( &ruck_coll_centre, &ruck_radius, this );
		ruck_centre_initialised = true;
	}

	FVector current_centre( ruck_centre.x, 0.0f, (gate.bottom + gate.top) * 0.5f );
	if ( !ruck_centre_locked )
	{
		// Work out current centroid
		float dist_from_current = (current_centre - ruck_centre).Magnitude();

		/// Wait for the centre of the ruck to stop moving a certain amount in a certain time
		const static float MAX_RUCK_CENTRE_MOVE = 0.6f;
		const static float MAX_RUCK_CENTRE_TIME = 0.3f;

		/// Once the first player has joined the ruck - we *MUST* lock so that all other binding players have the same origin
		if ( first_player_joined  )
			ruck_centre_locked = true;
		else if ( last_ruck_centre_update_time < MAX_RUCK_CENTRE_TIME && dist_from_current > MAX_RUCK_CENTRE_MOVE )
		{
			ruck_centre = current_centre;
			last_ruck_centre_update_time = 0.0f;
		} else if ( last_ruck_centre_update_time >= MAX_RUCK_CENTRE_TIME ) {
			ruck_centre_locked = true;
		}

		if ( !ruck_centre_locked )
			last_ruck_centre_update_time += delta_time;
	}

	/// Update collidable for ruck rect
	ruck_coll_centre = 	FVector( (gate.left + gate.right) * 0.5f, 0.0f, (gate.bottom + gate.top) * 0.5f );
	ruck_height = MabMath::Fabs( gate.bottom - gate.top );
	ruck_width  = MabMath::Fabs( gate.right - gate.left );
	ruck_radius = MabMath::Max( ruck_height, ruck_width ) * 0.5f;
}

const RuckCommital& RURuckState::GetCommital( RUTeam* team )
{
	const RURuckTeamState& ruck_team_state = attacking.team == team ? attacking : defending;
	return ruck_team_state.commital;
}

// Debugging ticket 11490
bool RURuckTeamState::AreAllPositionsFreeNow( bool _debug )
//bool RURuckTeamState::AreAllPositionsFreeNow()
{
	if(_debug)
	{
		MABLOGDEBUG("Check %s AreAllPositionsFreeNow on ruck %i:", (this == &ruck_state->attacking ? "attacking" : "defending"), ruck_state->ruck_id);
	}

	for( size_t i = 0; i < locations.size(); i++ )
	{
		if(_debug)
		{
			if(locations[i].bind_state != NULL)
			{
				MABLOGDEBUG("locations[%i].bind_state->config: %s", i, locations[i].bind_state->config);

				for(int j = 0; j < (int)locations[i].positions.size(); j++)
				{
					ARugbyCharacter* player = locations[i].positions[j].player;
					if(player != NULL)
					{
						MABLOGDEBUG("		locations[%i].positions[%i].player: %s (J:%i) (I:%i)", i, j, player->GetAttributes()->GetCombinedName().c_str(), player->GetAttributes()->GetNumber(), player->GetAttributes()->GetIndex());
					}
				}
			}
		}

		/// It is free if the state os currently NULL or there are no slots assigned to the config
		if ( !(locations[i].bind_state == NULL || strlen( locations[i].bind_state->config ) == 0 )  )
			return false;
	}

	return true;
}

RURuckTeamState::RURuckTeamState():
	commital( this ),
	team( NULL ),
	ruck( NULL ),
	ruck_state( NULL ),
	outstanding_join_requests(0),
	ai_join_requests(0),
	outstanding_leave_requests(0),
	oustanding_contest_ball_requests(0),
	time_since_last_contest(0.0f),
	contest_hold_on(false),
	importance_to_win( 0.0f ),
	time_till_next_ai_join( 0.0f ),
	should_commit( false ),
	human_player_index(EHumanPlayerSlot::INVALID_HUMAN),
	human_player_controller_index(-1),
	locations(),
	contesting_ball_player(NULL)
#ifdef ENABLE_PRO_MODE
	,
	outstanding_pro_join_request(false),
	outstanding_pro_leave_request(false)
#endif
{
}

IRUOrigin* RURuckTeamState::GetGateOrigin()
{
	MABASSERT( team != NULL );
	MABASSERT( ruck_state != NULL );
	return ruck_state->GetGateOrigin( team->GetPlayDirection() );
}

RURuckState::GateOrigin::GateOrigin( RURuckState* ruck_state, ERugbyPlayDirection play_dir )
: ruck_state( ruck_state )
, play_dir( play_dir )
{
}

FVector RURuckState::GateOrigin::GetOrigin() const
{
	float z_pos = play_dir == ERugbyPlayDirection::NORTH ? ruck_state->gate.bottom : ruck_state->gate.top;
	float x_pos = (ruck_state->gate.left + ruck_state->gate.right) * 0.5f;
	FVector result( x_pos, 0.0f, z_pos );
	return result;
};

RURuckState::RuckOrigin::RuckOrigin( RURuckState* ruck_state )
: ruck_state( ruck_state )
{
}

FVector RURuckState::RuckOrigin::GetOrigin() const
{
	return ruck_state->ruck_centre;
}

void RURuckTeamState::Reset( bool clear_ruck_participants )
{
	MABUNUSED( clear_ruck_participants );
#ifdef ENABLE_PRO_MODE
	outstanding_pro_join_request = false;
	outstanding_pro_leave_request = false;
#endif
	outstanding_join_requests.clear();
	ai_join_requests.clear();
	outstanding_leave_requests = 0;
	oustanding_contest_ball_requests = 0;
	time_since_last_contest = 0.0f;
	contest_hold_on = false;
	importance_to_win = 0.0f;
	time_till_next_ai_join = 0.0f;
	time_till_next_ai_contest = 0.0f;
	should_commit = false;
	contest_decision = CONTEST_UNDECIDED;
	human_player_index = EHumanPlayerSlot::INVALID_HUMAN;
	ruck_state = NULL;
	contesting_ball_player = NULL;

	for( int i = 0; i < RUCKLOC_COUNT; i++ )
	{
		locations[i].Reset();
	}

	commital.Reset();
	commital.Clear();
}


void RURuckPowerContest::StartPowerContest(float start_position, float start_velocity)
{
	power_bar_position = start_position;
	power_bar_velocity = start_velocity;
}


void RURuckPowerContest::ApplyJoinImpact(bool sevens, const RUZoneJoinType join_type, bool is_attacking_team, const FVector &impact_velocity, int committed_player_count, int bound_highwater_count )
{
	MABUNUSED(committed_player_count);

	/// Update the power bar to reflect the join
	static const float POS_IMPULSE_ARRAY[ RUCK_JOIN_MAX ] = { 0.0f, 0.09f, 0.15f };
	static const float VEL_IMPULSE_ARRAY[ RUCK_JOIN_MAX ] = { 0.0f, 0.05f, 0.075f };
	const int MAX_COMMITTED_PLAYER_COUNT = 10;
	/// NOTE First entry in list is tackler so set that to 1.0 also
	MABASSERT( committed_player_count <= MAX_COMMITTED_PLAYER_COUNT );
	MABASSERT( bound_highwater_count <= MAX_COMMITTED_PLAYER_COUNT );
	MABASSERT( !impact_velocity.ContainsNaN() );
	MABASSERT( join_type >= RUCK_JOIN_NONE && join_type <= RUCK_JOIN_AGGRESSIVE );

	// Different multipliers for differnt game modes
	static const float COMMITTED_PLAYER_COUNT_MULT_R15[ MAX_COMMITTED_PLAYER_COUNT ] = { 1.0f, 1.0f, 1.0f, 0.8f, 0.7f, 0.65f, 0.65f, 0.6f, 0.6f, 0.6f };
	static const float COMMITTED_PLAYER_COUNT_MULT_R7[ MAX_COMMITTED_PLAYER_COUNT ]  = { 2.0f, 1.75f };

	MABASSERT( committed_player_count >= 1 && committed_player_count < MAX_COMMITTED_PLAYER_COUNT );
	MABASSERT( bound_highwater_count >= 1 && bound_highwater_count < MAX_COMMITTED_PLAYER_COUNT );

	const static float MAX_IMPACT_VELOCITY = 10.0f;
	const static float IMPACT_VELOCITY_VARIANCE = 0.25f;

	/// Apply position and velocity changes in the right direction
	float SIDE_MULT = is_attacking_team? 1.0f : -1.0f;
	float JOIN_NUMBER_MULT = 0.0f;

	MabMath::Clamp(bound_highwater_count, 1, MAX_COMMITTED_PLAYER_COUNT);

	if (sevens)
		JOIN_NUMBER_MULT = COMMITTED_PLAYER_COUNT_MULT_R7[ bound_highwater_count - 1 ];
	else
		JOIN_NUMBER_MULT = COMMITTED_PLAYER_COUNT_MULT_R15[ bound_highwater_count - 1 ];

	/// Add a contribution from the impact velocity (scaled up/down by variance from middle impact velocity)
	float impact_vel_mult = impact_velocity.Magnitude() / MAX_IMPACT_VELOCITY;
	MabMath::Clamp( impact_vel_mult, 0.0f, 1.0f );
	impact_vel_mult = 1.0f + ((impact_vel_mult - 0.5f) * 2.0f * IMPACT_VELOCITY_VARIANCE);	// Normalise in range (1 - IMPACT_VELOCITY_VARIANCE) to (1 + IMPACT_VELOCITY_VARIANCE)

	
	//HES 11330 plus various other reports indicate that the ruck is too hard to win in tutorials.	
	if (SIFApplication::GetApplication()->GetActiveGameWorld()->GetWorldId() == WORLD_ID::SANDBOX)
	{
		float const TUTORIAL_MULTIPLIER = 1.5f;
		if (is_attacking_team)
		{
			impact_vel_mult *= TUTORIAL_MULTIPLIER;
		}
		else
		{
			impact_vel_mult *= 0.1f;
		}
	}


#ifdef FREEZE_RUCKS
	impact_vel_mult = 0.0f;
#endif // FREEZE_RUCKS

	power_bar_position		+= POS_IMPULSE_ARRAY[ join_type ] * impact_vel_mult * SIDE_MULT * JOIN_NUMBER_MULT;
	power_bar_velocity		+= VEL_IMPULSE_ARRAY[ join_type ] * impact_vel_mult * SIDE_MULT * JOIN_NUMBER_MULT;
}


bool RURuckPowerContest::Update( const MabTimeStep& time_step, float power_bar_accel )
{
	/// Always want to make sure that we have at least some positive acceleration to move the bar in favour of the team currently winning
	const static float MIN_ACCEL = 0.07f;
	if ( MabMath::Feq( power_bar_accel, 0.0f ) )
		power_bar_accel = MIN_ACCEL * (power_bar_position >= 0.5f ? 1.0f : -1.0f);

	/// Now apply acceleration
	power_bar_velocity = power_bar_velocity + power_bar_accel * time_step.delta_time.ToSeconds();
	power_bar_position = power_bar_position + power_bar_velocity * time_step.delta_time.ToSeconds();
	MabMath::Clamp(power_bar_position, 0.0f, 1.0f);

#ifdef FREEZE_RUCKS
	power_bar_position = 0.5f;
#endif

	// If we've hit the peak, shift to the next phase
	if( power_bar_position >= 1.0f || power_bar_position <= 0.0f)
	{
		return false;
	}
	return true;
}


float RURuckPowerContest::GetTimeTillBallRelease()
{
	float time = 0.0f;
	if ( power_bar_velocity < 0.0f ) // Defending team expected to win
	{
		time = power_bar_position / -power_bar_velocity;
	} else if ( power_bar_velocity > 0.0f ){	// Attacking team expected to win
		time = (1.0f - power_bar_position) / power_bar_velocity;
	}

	MabMath::ClampLower( time, 0.0f );

	/// Add on the ball expected ball release time
	/// JOE: WTF? this seems pretty weird

	float MAX_TRAVEL_TIME = 1.0f;
	float travel_time = MAX_TRAVEL_TIME - MabMath::Fabs( power_bar_velocity );
	MabMath::Clamp( travel_time, 0.3f, 1.0f );
	time += travel_time;

	return time;
}

RuckCommital::RuckCommital( RURuckTeamState* ruck_team_state ) : ruck_team_state( ruck_team_state )
{
	Clear();
	Reset();
}

size_t RuckCommital::GetTotalRuckPlayers() const
{
	return non_committing.size() + committing.size() + bound.size();
}

size_t RuckCommital::GetTotalCommitting() const
{
	return committing.size();
}

size_t RuckCommital::GetTotalBound() const
{
	return bound.size();
}

void RuckCommital::IncrementBoundHighwater()
{
	bound_high_water++;
}

size_t RuckCommital::GetTotalBoundHighwater() const
{
	return bound_high_water;
}

size_t RuckCommital::GetTotalCommittingOrBound() const
{
	return committing.size() + bound.size();
}

size_t RuckCommital::GetPeakTotalRuckPlayers() const
{
	return peak_total;
}

void RuckCommital::Clear()
{	
	if (SIFApplication::GetApplication() && SIFApplication::GetApplication()->GetActiveGameWorld())
	{
		int playersPerTeam = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();
		non_committing.reserve(playersPerTeam/*NUM_PLAYERS_PER_TEAM*/);
		committing.reserve(playersPerTeam/*NUM_PLAYERS_PER_TEAM*/);
		bound.reserve(playersPerTeam/*NUM_PLAYERS_PER_TEAM*/);
	}

	non_committing.clear();
	committing.clear();
	bound.clear();
}

void RuckCommital::Reset()
{
	peak_total = 0;
	bound_high_water = 0;
}

void RuckCommital::Update()
{
	Clear();

	if(ruck_team_state->ruck_state == NULL)
		return;

	RUTeam* team = ruck_team_state->team;
	SIFGameWorld* game = team->GetGame();

	SIFRugbyCharacterList players;
	game->GetStrategyHelper()->GetTeamRoles( team, RURoleRuck::RTTGetStaticType(), players );
	SIFRugbyCharacterList::iterator it;

	for( it = players.begin(); it != players.end(); ++it )
	{
		ARugbyCharacter* player = *it;
		RURoleRuck* ruck_role = player->GetRole<RURoleRuck>();

		// Only pick players from the current ruck
		if ( ruck_role->GetRuckIndex() != -1 && ruck_role->GetRuckIndex() != ruck_team_state->ruck_state->ruck_id )
			continue;

		RUZonePositionAssigned* zpa = player->GetPosAss();
		MABASSERT( zpa != NULL );
		if ( zpa && zpa->GetPosition() && zpa->GetPosition()->GetLocation() == RUCKLOC_BOUND )
		{

			if ( ruck_role->IsBound() )
			{
				bound.push_back( player );
				//MABLOGDEBUG("RuckCommital::Update bound=%d state=%d",bound.size(),ruck_role->GetState()); //sorry u are spamming too much
			}
			else
			{
				committing.push_back( player );
				//MABLOGDEBUG("RuckCommital::Update committing=%d state=%d",committing.size(),ruck_role->GetState()); //sorry u are spamming too much
			}
		} 
        else
        {
			non_committing.push_back( player );
			//MABLOGDEBUG("RuckCommital::Update non_committing=%d",non_committing.size()); //sorry u are spamming too much
		}
	}

	peak_total = MabMath::Max( peak_total, players.size() );
}
