/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/Rules/Triggers/RURuleTriggerPlayTheBall.h"

#include "Match/Ball/SSBall.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/SIFGameWorld.h"

MABRUNTIMETYPE_IMP1(RURuleTriggerPlayTheBall, RURuleTrigger);

RURuleTriggerPlayTheBall::RURuleTriggerPlayTheBall(RURules* rules)
: RURuleTrigger(rules)
{
}

RURuleTriggerPlayTheBall::~RURuleTriggerPlayTheBall()
{
}

void RURuleTriggerPlayTheBall::AttachMonitors()
{
	RUGameEvents* events = game->GetEvents();
	//MABASSERTMSG(events,"we need events here");
	events->play_the_ball_start.Add(this, &RURuleTriggerPlayTheBall::StartPlayTheBall);
}

void RURuleTriggerPlayTheBall::DeattachMonitors()
{
	RUGameEvents* events = game->GetEvents();
	if (events)
	{
		events->play_the_ball_start.Remove(this, &RURuleTriggerPlayTheBall::StartPlayTheBall);
	}
}

void RURuleTriggerPlayTheBall::Enter()
{
}

void RURuleTriggerPlayTheBall::Update(float)
{
	game->GetGameState()->SetPlayRestartPosition(game->GetBall()->GetCurrentPosition());
	rules->StartConsequence(consequence);
	rules->SuspendPlay( false, "RURuleTriggerPlayTheBall::Update");
}

void RURuleTriggerPlayTheBall::StartPlayTheBall(ARugbyCharacter* holder)
{
#ifdef ENABLE_DEBUG_RULE_MONITORING
	MABLOGMSG(LOGCHANNEL_DEBUG, LOGTYPE_INFO, "RulesTrigger Call: RURuleTriggerPlayTheBall::StartPlayTheBall");
#endif

	if (holder != game->GetGameState()->GetBallHolder())
	{
		return;
	}

	consequence = RUC_PLAY_THE_BALL;

	rules->SetTrigger(this);
}

void RURuleTriggerPlayTheBall::Reset()
{

}
