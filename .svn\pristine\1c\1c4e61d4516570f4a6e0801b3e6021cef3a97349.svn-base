/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef _RUOFFSIDEMARKER_H_
#define _RUOFFSIDEMARKER_H_
#include "Match/SIFGameWorld.h"
#include "Mab/Time/MabTimeStep.h"
#include "Match/HUD/RU3DHUDElement.h"
class ARugbyCharacter;
class ARugbyMarkerActor;

class RUOffsideMarker : public RU3DHUDElement
{

public:

	RUOffsideMarker(SIFGameWorld *ggame);
	virtual ~RUOffsideMarker();

	virtual void Update(const MabTimeStep& /*game_time_step*/) {};
	virtual void SyncUpdate(bool enabled);

	void ActivateWithPlayer( ARugbyCharacter* _player );
	void Deactivate();
	bool GetActive() const { return player != NULL; }
	void SetPlayerAlpha( float alpha );
	void SetColor();
	void InitMaterialInstance();
	ARugbyCharacter* GetPlayer() { return player; }

private:

	ARugbyCharacter * player;
	UMaterialInstanceDynamic * OffsideMaterialInst = nullptr;
};


#endif