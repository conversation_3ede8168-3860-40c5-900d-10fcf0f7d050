#include "MemCheck.h"
#include "Rugby.h"

//#ifdef wwSTEAM_ENABLED
#if PLATFORM_WINDOWS
//#if 0


//#include "zlib-1.2.8/zlib.h"
#include <stdint.h>

MemCheck MemCheck::s_instance;

MemCheck::MemCheck():m_handle(NULL)
{

}

MemCheck& MemCheck::instance()
{
	return MemCheck::s_instance;
}

MemCheck::~MemCheck()
{
	MemoryFreeLibrary(m_handle);
}

bool MemCheck::LoadModule(const unsigned char* source, int sourceLen)
{
	unsigned char* dest = NULL;
	int destLen = 0;
	if(!UncompressStream(source,sourceLen,dest,destLen))
	{
		return false;
	}

	m_handle = MemoryLoadLibrary(dest,destLen);
	if(m_handle==NULL)
	{
		delete[]dest;
		return false;
	}

	return true;
}

typedef int (*getTheBallRollingProc)(int, int);

int MemCheck::GetTheBallRolling(int a,int b)
{
	return ~(a + b); // GGS Nick Remove for now...
#ifndef ENABLE_STEAMCHECK
//#if !UE_BUILD_SHIPPING
	return ~(a + b);
#endif

	if (m_handle)
	{
		getTheBallRollingProc func = (getTheBallRollingProc)MemoryGetProcAddress(m_handle, "getTheBallRolling");
		if (!func)
		{
			if (~(a + b) == 172231)
			{
				return 172232;
			}
		}
		return func ? ~(func(a, b)) : 172231;
	}
	else
	{
		if (~(a + b) == 172231)
		{
			return 172232;
		}
	}

	return 172231;
	
}

bool MemCheck::UncompressStream(const unsigned char* source, int sourceLen, unsigned char*& dest,int& destLen)
{
	int32_t uncompressedlen = 0;
	if( sourceLen<sizeof(uncompressedlen) )
	{
		return false;
	}

	memcpy(&uncompressedlen, source, sizeof(uncompressedlen));

	dest = new unsigned char[uncompressedlen];

	//uLongf l = uncompressedlen;
	//if (Z_OK == uncompress(dest, &l,source+sizeof(uncompressedlen), sourceLen))
	//{
	//	destLen = l;
	//	return true;
	//}
	if (FCompression::UncompressMemory(NAME_Zlib, dest, uncompressedlen, source + sizeof(uncompressedlen), sourceLen))
	{
		destLen = uncompressedlen;
		return true;
	}

	delete[]dest;
	return false;
}

#endif