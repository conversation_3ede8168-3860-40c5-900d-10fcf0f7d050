// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIPopulatorCustomiseSearch.h"

#include "Rugby/RugbyGameInstance.h"

#include "Rugby/UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"

// RC3 Stuff
#include "Match/RugbyUnion/RUDBPlayer.h"
#include "Match/RugbyUnion/RUDBTeam.h"
#include "Databases/RUGameDatabaseManager.h"
#include "Match/RugbyUnion/RUDatabaseConstants.h"
#include "Match/SIFUIConstants.h"

#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Database.h"

#include "Rugby/UI/Populators/WWUIPopulatorCustomiseEditPlayers.h"

// Unreal Widgets
#include "ScrollBox.h"
#include "HorizontalBox.h"
#include "TextBlock.h"
#include "WidgetTree.h"

// WW Widgets
#include "WWUIListField.h"
#include "WWUITranslationManager.h"
#include "Utility/Helpers/SIFGameHelpers.h"


const int PRIMARY_POSITION = 0;
const int ALL_POSITIONS = 0;

bool player_search_sort_name(RUDB_PLAYER& player1, RUDB_PLAYER& player2)
{
	int sort = strcmp(player1.GetLastName(), player2.GetLastName());
	if (sort == 0)
		sort = strcmp(player1.GetFirstName(), player2.GetFirstName());

	return sort < 0;
}


void UWWUIPopulatorCustomiseSearch::Populate(UWidget* widget)
{
	Clear(widget);
	//node->PurgeChildren();

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		RUGameDatabaseManager* database_manager = pRugbyGameInstance->GetGameDatabaseManager();

		if (search_name.IsEmpty())
		{
			ResultsFound = false;
			return;
		}

		MabVector<RUDB_PLAYER> player_list(SIFHEAP_DYNAMIC);

		MabVector<unsigned short> player_id_list(SIFHEAP_DYNAMIC);
		int list_size = 0;
		PopulateSearchPlayers(player_id_list, list_size, (PLAYER_RECRUIT_FILTER)filter_position);

		ResultsFound = true;
		if (list_size == 0)
		{
			ResultsFound = false;
			return;
		}


		player_list.resize(player_id_list.size());

		if (database_manager)
		{
			database_manager->LoadAllData(player_id_list, player_list);

			std::sort(player_list.begin(), player_list.end(), player_search_sort_name);
		}

		TArray<RUDB_PLAYER> PlayerList;

		for (unsigned int i = 0; i < player_list.size(); i++)
		{
			PlayerList.Add(player_list[i]);
		}

		CustomiseSearchCreationNodeCallback callbackObject(widget, PlayerList);

		CreateNodesFromTemplate(dataList.TemplateName, PlayerList.Num(), &callbackObject);

		//if (!inPreConstruct)
		//{
		//	SelectDefaultPlayer(widget);
		//}

	}
}




void UWWUIPopulatorCustomiseSearch::Refresh(UWidget* widget)
{

	/*MabString buffer;
	int filter_position;
	int matching_count = 0;

	buffer = node->GetProperty("filter_position")->get<MabString>();

	MabStringHelper::ToInt(buffer, filter_position);
	MABLOGDEBUG("Filter position for search box = %d", filter_position);*/

	int matching_count = 0;

	UScrollBox* pScrollBox = Cast<UScrollBox>(widget);
	if (!pScrollBox)
	{
		ensureMsgf(pScrollBox, TEXT("pScrollBox is nullptr"));
		return;
	}
	for (int i = 0; i < pScrollBox->GetChildrenCount(); i++)
	{
		/*int player_id = 0;
		MabUINode *child;

		child = node->GetChildByIndex(i);

		if (child->GetProperty("player_id") != NULL)
		{
			buffer = child->GetProperty("player_id")->get<MabString>();
			MabStringHelper::ToInt(buffer, player_id);
		}*/

		UWWUIListField* pListField = Cast<UWWUIListField>(pScrollBox->GetChildAt(i));

		if (!pListField)
		{
			ensureMsgf(pListField, TEXT("pListField is nullptr"));
			return;
		}

		RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();
		RL3Database* database = database_manager->GetRL3Database();

		RL3DB_PLAYER player = database->GetPlayer(pListField->GetIntProperty("player_id"));
		//RL3DB_PLAYER player = database->GetPlayer(player_id);

		// #TODO the players are already filtered by PopulateSearchPlayers(). Filtering here is redundant but may be part of a more performant solution where we do not repopulate when changing filters.
		
		bool is_enabled = SIFApplication::GetApplication()->GetCareerModeManager()->GetFilterPosition((PLAYER_RECRUIT_FILTER)filter_position) == player.GetPositionCategory(PRIMARY_POSITION);

		if (filter_position == ALL_POSITIONS)
			is_enabled = true;

		if (is_enabled)
			++matching_count;

		pListField->SetVisibility((is_enabled) ? (ESlateVisibility::Visible) : (ESlateVisibility::Collapsed));
	}
}


void UWWUIPopulatorCustomiseSearch::PopulateSearchPlayers(MabVector<unsigned short>& player_id_list, int& list_size, PLAYER_RECRUIT_FILTER in_filter_position)
{
	RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();
	RL3Database* database = database_manager->GetRL3Database();

	int num_players = database->GetTotalNumPlayers();

	MabString search_name_mab = SIFGameHelpers::GAConvertFStringToMabString(search_name);

	PLAYER_POSITION ConvertedPosition = SIFApplication::GetApplication()->GetCareerModeManager()->GetFilterPosition(in_filter_position);

	for (int playeridx = 0; playeridx < num_players; playeridx++)
	{
		RL3DB_PLAYER player = database->GetPlayerByIndex(playeridx);

		MabString playerName = player.GetName();

		if (strstr(MabStringHelper::ToUpper(player.GetName()).c_str(), MabStringHelper::ToUpper(search_name_mab).c_str()) != NULL)
		{
			bool is_enabled = ConvertedPosition == (int32)player.GetPositionCategory(PRIMARY_POSITION);
			//bool is_custom = in_filter_position == CUSTOM_PLAYERS;

			if (in_filter_position == ALL_POSITIONS)
			{
				is_enabled = true;
			}

			if (is_enabled)
			{
				++list_size;

#ifndef CHARACTER_CREATOR_BUILD
				player_id_list.push_back(player.GetDbId());
#else
				if (player.GetIsCustom())
					player_id_list.push_back(player.GetDbId());
#endif
			}
		}
	}
}


UWWUIPopulatorCustomiseSearch::CustomiseSearchCreationNodeCallback::CustomiseSearchCreationNodeCallback(UWidget* containerToPopulate, TArray<RUDB_PLAYER>& inPlayerList) :
	player_list(inPlayerList)
{
	container = Cast<UScrollBox>(containerToPopulate);

	if (!container)
	{
		FString errorString = containerToPopulate != nullptr ? *containerToPopulate->GetPathName() : FString("NULL");
		UE_LOG(LogTemp, Error, TEXT("Cast to scroll box failed while attempting DataFileCreationNodeCallback on node %s"), *errorString);
	}
}


void UWWUIPopulatorCustomiseSearch::CustomiseSearchCreationNodeCallback::Callback(UUserWidget* widget)
{
	int32 i = container->GetChildrenCount();
	UWidgetTree* widgetTree = widget->WidgetTree;

	UWWUIListField* pListField = Cast<UWWUIListField>(widget);

	if (pListField && widgetTree)
	{
		UHorizontalBox* pHorizontalBox = Cast<UHorizontalBox>(widgetTree->FindWidget("HorizontalBoxText"));
		if (pHorizontalBox)
		{
			if (player_list.IsValidIndex(i))
			{
				int32 PositionIndex = 0;

				for (int j = 0; j < pHorizontalBox->GetChildrenCount(); j++)
				{
					ECustomiseEditPlayerFieldItem CurrentItem = (ECustomiseEditPlayerFieldItem)j;

					UTextBlock* pTextBlock = Cast<UTextBlock>(pHorizontalBox->GetChildAt(j));

					if (!pTextBlock && j == (int32)ECustomiseEditPlayerFieldItem::NAME)
					{
						pTextBlock = Cast<UTextBlock>(widgetTree->FindWidget("TextName"));
					}

					FString ItemText = "";

					switch (CurrentItem)
					{
					case ECustomiseEditPlayerFieldItem::SECOND_POSITION:
					{
						PositionIndex = 1;
					}
					// FALLS THROUGH
					case ECustomiseEditPlayerFieldItem::POSITION:
					{
						// MULTI_POSITION_CATEGORY_CHANGE
						//player_info.primary_position<=PP_FULLBACK ? PlayerPositionEnum::GetPlayerPositionTextAbbreviated(  player_info.primary_position ) : "- -"
						PLAYER_POSITION primary_position = (player_list[i].gender == PLAYER_GENDER_MALE) ? player_list[i].GetPositionCategoryR13(PositionIndex) : player_list[i].GetPositionCategoryR7(PositionIndex);
						ItemText = primary_position <= PP_NUMBER_EIGHT_LOCK_FORWARD ? PlayerPositionEnum::GetPlayerPositionTextAbbreviated(primary_position) : "- -";
						ItemText = UWWUITranslationManager::Translate(ItemText);
					}
					break;
					case ECustomiseEditPlayerFieldItem::NAME:
					{
						ItemText = UTF8_TO_TCHAR(player_list[i].GetLastName()) + FString(", ") + UTF8_TO_TCHAR(player_list[i].GetFirstName());
						REMOVE_UNUSED_CHARACTER(ItemText);

					}
					break;
					case ECustomiseEditPlayerFieldItem::AGE:
					{
						ItemText = FString::FromInt(player_list[i].GetAge());
					}
					break;
					case ECustomiseEditPlayerFieldItem::RATING:
					{
						ItemText = FString::FromInt(player_list[i].GetOverallRating());
					}
					break;
					}

					if (pTextBlock)
					{
						pTextBlock->SetText(FText::FromString(ItemText));
					}
				}

				int32 DbID = player_list[i].GetDbId();
				pListField->SetProperty("player_id", &DbID, PROPERTY_TYPE_INT);
				DbID += 1;
				pListField->SetProperty("player_index", &DbID, PROPERTY_TYPE_INT);
			}
			else
			{
				for (int j = 0; j < pHorizontalBox->GetChildrenCount(); j++)
				{
					ECustomiseEditPlayerFieldItem CurrentItem = (ECustomiseEditPlayerFieldItem)i;

					UTextBlock* pTextBlock = Cast<UTextBlock>(pHorizontalBox->GetChildAt(j));

					int32 DbID = 0;
					pListField->SetProperty("player_id", &DbID, PROPERTY_TYPE_INT);

					pListField->SetVisibility(ESlateVisibility::Collapsed);
				}
			}
		}
	}

	container->AddChild(widget);
}