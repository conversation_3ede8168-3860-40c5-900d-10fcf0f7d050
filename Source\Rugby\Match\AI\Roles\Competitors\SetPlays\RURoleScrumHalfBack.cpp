/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleScrumHalfBack.h"

#include "Match/AI/Actions/RUActionPass.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/RUZonePosition.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleScrum.h"
#include "Match/Ball/SSBall.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerLookAt.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Character/RugbyPlayerController.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseScrum.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SIFGameInput.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Match/SSSpatialHelper.h"
#include "Utility/RURandomNumberGenerator.h"
#include "Mab/Time/MabTimer.h"

//#rc3_legacy_include #include "NMMabAnimationEvents.h"
//#rc3_legacy_include #include "SIFDebug.h"
//#rc3_legacy_include #include <NMMabAnimationNetwork.h>

#include "Character/RugbyPlayerController.h"
#include "Character/RugbyCharacter.h"
#include "RugbyGameInstance.h"

#ifdef ENABLE_PRO_MODE
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#endif
#include "Match/Components/SSHumanPlayer.h"
#include "Match/AI/SetPlays/SSSetPlayManager.h"
#include "Match/RugbyUnion/RUInputKickInterface.h"
#include "Match/Input/SSInputManager.h"
#include "RURoleRuckSendRunner.h"


MABRUNTIMETYPE_IMP1( RURoleScrumHalfBack, SSRole );

#if defined( ENABLE_ROLE_DEBUG_STRINGS )
static const char* RoleState::STRINGS[RURoleScrumHalfBack::RoleState::LAST] =
{
	"PREBIND",		// Players are before the bind sequence moving into position to start binding
	"WAITING",		// Waiting for scrum to finish engaging
	"FEEDING",		// Waits for player input to feed ball/or if ai controlled", feeds
	"FOLLOWING",	// Following the scrum.
	"CROUCH",		// Crouching", about to do something
	"BALL_FREE",	// Collecting ball/Marking opposing halfback
	"PICKUP_WAIT",	// Wait for pickup to complete
	"BOXKICK_PICKUP_WAIT", // Wait to pickup the ball for a boxkick
	"BOXKICK_WAIT",	// Wait for boxkick to complete
	"PASS",			// Pass the ball
	"PASS_WAIT",	// Wait for pass to complete
	"NUM_EIGHT_PICKUP",	// Do number eight pickup.

	"DONE"
};
#endif

///-------------------------------------------------------------------------
/// Constructor
///-------------------------------------------------------------------------

RURoleScrumHalfBack::RURoleScrumHalfBack( SIFGameWorld* game )
: SSRole(game)
, role_options()
, notified_collect( false )
, state{}
, state_timer(0.0f)
, animation_finished_notified( false )
, scrum_down_received(false)
, scrum_start_received( false )
, ball_in_received( false )
, reset_received( false )
, box_kick_selected( false )
, is_doing_number_eight_pickup( false )
, has_finished_num_eight_pickup_animation( false )
, scrum_phase(NULL)
{
}

///-------------------------------------------------------------------------
/// Enter
///-------------------------------------------------------------------------

void RURoleScrumHalfBack::Enter( ARugbyCharacter* player )
{
	scrum_down_received = false;
	ball_in_received = false;
	scrum_start_received = false;
	reset_received = false;
	notified_collect = false;
	box_kick_selected = false;

	state_timer = 0.0f;

	RUGameEvents* game_events = m_pGame->GetEvents();
	game_events->scrum_start.Add( this, &RURoleScrumHalfBack::ScrumStart );
	game_events->scrum_down.Add( this, &RURoleScrumHalfBack::ScrumDown );
	game_events->scrum_ball_in.Add( this, &RURoleScrumHalfBack::BallIn );
	game_events->scrum_ball_out.Add( this, &RURoleScrumHalfBack::BallOut );
	game_events->scrum_ball_release_possible.Add( this, &RURoleScrumHalfBack::BallReleasePossible );
	game_events->scrum_finish.Add( this, &RURoleScrumHalfBack::ScrumFinish);
	game_events->scrum_reset.Add( this, &RURoleScrumHalfBack::ScrumReset);
	game_events->pass.Add( this, &RURoleScrumHalfBack::BallPassed );

	scrum_phase = (RUGamePhaseScrum*) m_pGame->GetGameState()->GetPhaseHandler(RUGamePhase::SCRUM);

	player->GetMabAnimationEvent().Add( this, &RURoleScrumHalfBack::AnimationEvent );
	player->GetAnimation()->SetDefaultIdleGroup( ERugbyFormationIdleGroup::READY_ENGAGE );

	SSRole::Enter( player );
	player->GetMovement()->SetWaypointAcceptRange( -1.0f );

	// Lock everything except passes
	m_lock_manager.HFLockAll();
	m_lock_manager.HFUnlock( HF_PASS );

	if ( player->GetAttributes()->GetTeam() == m_pGame->GetGameState()->GetPlayRestartTeam() )
		m_pGame->GetGameState()->SetBallHolder( player );	// TYRONE : Set on enter so assign best player has a reference

	// don't tackle me
	player->GetActionManager()->EnableAction( ACTION_TACKLEE, false );

	// Set to human player
	SET_CHANGEPLAYER_SECTION( m_pGame, "SCRUMHB" );
	SSHumanPlayer* penalty_decision_human = m_pGame->GetGameState()->GetPenaltyDecisionHuman();
	RUGamePhase previous_phase = m_pGame->GetGameState()->GetPreviousPhase();
	if ( (previous_phase == RUGamePhase::DECISION_PENALTY || previous_phase == RUGamePhase::DECISION) && player->GetAttributes()->GetTeam() == m_pGame->GetGameState()->GetPlayRestartTeam() &&
		 penalty_decision_human != NULL && penalty_decision_human->GetTeam() == m_pGame->GetGameState()->GetPlayRestartTeam())
	{
		RUCareerModeManager* proModeMan = SIFApplication::GetApplication()->GetCareerModeManager();
		bool isProMode = proModeMan->IsActive() && proModeMan->GetIsCareerModePro();

		// Check if we're playing Pro Mode, if so, we can only assign this player if they are our player
		bool shouldReassign = true;
		if(isProMode && penalty_decision_human->GetRugbyCharacter() != NULL && !proModeMan->IsProPlayer(penalty_decision_human->GetRugbyCharacter()))
			shouldReassign = false;

		if(shouldReassign)
		{
			// Remove the existing human from this player
			if ( player->GetHumanPlayer() )
				player->GetHumanPlayer()->SetRugbyCharacter(nullptr);

			penalty_decision_human->SetRugbyCharacter( player );
		}
	}
	else
		player->GetAttributes()->GetTeam()->GetHumanSelector().AssignHumanToPlayer( player );
	SET_CHANGEPLAYER_SECTION( m_pGame, NULL );


	// Check to see if our pro player is not this player, if so... unassign our pro player as the human player, prevents us from running around in the background.
	/*if(player->GetAttributes()->GetTeam()->GetProPlayer() != NULL && player->GetAttributes()->GetTeam()->GetProPlayer() != player)
	{
		SSHumanPlayer* human_player = player->GetAttributes()->GetTeam()->GetProPlayer()->GetHumanPlayer();
		if ( human_player )
			human_player->SetRugbyCharacter(nullptr);
	}*/

	m_pMovement->SetAvoidanceEnabled( false );
	m_pMovement->SetRepulsionEnabled( false );

	animation_finished_notified = false;

	is_doing_number_eight_pickup = false;
	has_finished_num_eight_pickup_animation = false;

	if (player && player->GetAttributes() && player->GetAttributes()->GetTeam())
	{
		player->GetAttributes()->GetTeam()->GetStrategy().SetBreakdownStrategyStarted(false);
	}

	state = RoleState::PREBIND;
}

///-------------------------------------------------------------------------
/// Exit
///-------------------------------------------------------------------------

void RURoleScrumHalfBack::Exit(bool forced)
{
	RUGameEvents* game_events = m_pGame->GetEvents();
	game_events->scrum_start.Remove( this, &RURoleScrumHalfBack::ScrumStart );
	game_events->scrum_down.Remove( this, &RURoleScrumHalfBack::ScrumDown);
	game_events->scrum_ball_in.Remove( this, &RURoleScrumHalfBack::BallIn );
	game_events->scrum_ball_out.Remove( this, &RURoleScrumHalfBack::BallOut );
	game_events->scrum_ball_release_possible.Remove( this, &RURoleScrumHalfBack::BallReleasePossible );
	game_events->scrum_finish.Remove( this, &RURoleScrumHalfBack::ScrumFinish);
	game_events->scrum_reset.Remove( this, &RURoleScrumHalfBack::ScrumReset);
	game_events->pass.Remove( this, &RURoleScrumHalfBack::BallPassed );

	m_pPlayer->GetMabAnimationEvent().Remove( this, &RURoleScrumHalfBack::AnimationEvent );
	RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
	animation->SetVariable( animation->GetDummyHalfVariable(), 0.0f );
	animation->ResetToDefaultIdleGroup();

	m_pPlayer->GetActionManager()->EnableAction( ACTION_TACKLEE, true );

	RUZonePositionAssigned* pos_ass = m_pPlayer->GetPosAss();
	if ( pos_ass != NULL )
		pos_ass->Clear();

	m_pPlayer->GetLookAt()->Reset();

	m_pMovement->SetAvoidanceEnabled( true );
	m_pMovement->SetRepulsionEnabled( true );

	if ( forced )
	{
		state = RoleState::DONE;
	}

	SSRole::Exit(forced);
}

void RURoleScrumHalfBack::AnimationEvent(float /*time*/, ERugbyAnimEvent event, size_t userdata, bool /*bIsBlendingOut = false*/)
{
	if ( is_doing_number_eight_pickup )
	{
		wwNETWORK_TRACE_JG("RURoleRuckScrumHalf::AnimationEvent %s event = %d userdata = %d", TCHAR_TO_UTF8(*m_pPlayer->GetName()), (int)event, userdata);

		if ( event == ERugbyAnimEvent::BALL_CONTACT_EVENT )
		{
			wwNETWORK_TRACE_JG("RURoleRuckScrumHalf::AnimationEvent event == ERugbyAnimEvent::BALL_CONTACT_EVENT");
			const static float ATTACH_TRANS_TIME = 0.13f;
			const static float ATTACH_ROT_TIME = 0.4f;
			m_pGame->GetBall()->SetBallAttachTime( ATTACH_TRANS_TIME, ATTACH_ROT_TIME );

			m_pGame->GetGameState()->SetBallHolder(m_pPlayer);
		}

		if ( event == ERugbyAnimEvent::BLEND_OUT_EVENT && userdata == 200 )
		{
			wwNETWORK_TRACE_JG("RURoleRuckScrumHalf::AnimationEvent Player ID: %d, event == ERugbyAnimEvent::BLEND_OUT_EVENT", m_pPlayer->GetAttributes()->GetDbId());
			const FVector& scrum_center = scrum_phase->GetCurrentScrum().scrum_centre;

			float direction_x = 1.0f;
			float direction_z = 1.0f;

			if ( m_pPlayer->GetAttributes()->GetPlayDirection() == ERugbyPlayDirection::SOUTH )
			{
				direction_x = -1.0f;
				direction_z = -1.0f;
			}

			FVector eight_target = scrum_center;
			eight_target.x -= (3.0f * direction_x);
			eight_target.z -= (1.0f * direction_z);

			RUMotionInterpolation* eight_interp = MabMemNew( MabMemGetDefaultObjectHeap( this ) ) RUMotionInterpolation( m_pPlayer->GetMovement() );
			eight_interp->Start( eight_target, 0.0f, false );
			MotionFrame& frame = eight_interp->InsertFrame( 0.75f );
			frame.state.current_position = eight_target;

			m_pPlayer->GetMovement()->SetMotionSource( eight_interp, true );

			has_finished_num_eight_pickup_animation = true;
		}

		return;
	}

	if ( event == ERugbyAnimEvent::BALL_CONTACT_EVENT )
	{
		NotifyCollected();
	}

	if ( event == ERugbyAnimEvent::BLEND_OUT_EVENT && userdata == 200 )
	{
		animation_finished_notified = true;
	}
}

void RURoleScrumHalfBack::NotifyCollected()
{
	m_pMovement->SetAvoidanceEnabled(true);
	m_pMovement->SetRepulsionEnabled(true);

	const static float ATTACH_TRANS_TIME = 0.13f;
	const static float ATTACH_ROT_TIME = 0.4f;
	m_pGame->GetBall()->SetBallAttachTime(ATTACH_TRANS_TIME, ATTACH_ROT_TIME);

	m_pGame->GetGameState()->PickedUp(m_pPlayer, PUC_FROM_SCRUM);
	notified_collect = true;
}

/* WJS RLC Not used
bool RURoleScrumHalfBack::BreakEarly()
{
	FVector current_pos = player->GetMovement()->GetCurrentPosition();
	static const FVector POSITION_OFFSET = FVector( 10.0f, 0.0f, -7.0f );
	wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
	player->GetMovement()->SetTargetPosition( current_pos + POSITION_OFFSET );

	movement->SetAvoidanceEnabled( true );
	movement->SetRepulsionEnabled( true );
	state = RoleState::DONE;

	return true;
}
//*/


void RURoleScrumHalfBack::ScrumStart( RUTeam* /*feeding_team*/, ARugbyCharacter* /*feeding_player*/ )
{
	scrum_start_received = true;
}


///-------------------------------------------------------------------------
/// 'scrum_down' event received.
///-------------------------------------------------------------------------
void RURoleScrumHalfBack::ScrumDown()
{
	scrum_down_received = true;
}

// Clean up.
void RURoleScrumHalfBack::ScrumFinish()
{
	// If we're resetting then ignore this
	if ( reset_received )
		return;

	// If we're in the middle of the pass let it finish before we exit our role
	if ( state != RoleState::PASS_WAIT && state != RoleState::BOXKICK_PICKUP_WAIT && state != RoleState::BOXKICK_WAIT)
		state = RoleState::DONE;

	state_timer = 0.0f;
}

void RURoleScrumHalfBack::ScrumReset(RUTeam*, ScrumResetContext /*reset_context*/)
{
	state_timer = 0.0f;
	RUZonePositionAssigned* pos_ass = m_pPlayer->GetPosAss();
	if ( pos_ass && pos_ass->CanUnbindNow() )
		pos_ass->Clear();

	reset_received = true;
	scrum_down_received = false;
	ball_in_received = false;
	scrum_start_received = false;

	state = RoleState::PREBIND;
}

void RURoleScrumHalfBack::BallIn(RUTeam* /*feeding_team*/, ARugbyCharacter* /*feeding_player*/)
{
	ball_in_received = true;
}

void RURoleScrumHalfBack::BallReleasePossible(RUTeam*)
{
	if (state < RoleState::CROUCH)
	{
		m_pPlayer->GetMovement()->SetMotionSource(NULL);
		state = RoleState::CROUCH;
		state_timer = 0.0f;
	}
}

void RURoleScrumHalfBack::BallOut(RUTeam* team)
{
	if (state < RoleState::CROUCH)
	{
		m_pPlayer->GetMovement()->SetMotionSource(NULL);
		state = RoleState::CROUCH;
		state_timer = 0.0f;

		// if we are the defender, then release the locks
		// and let the human go nutts
		if (team != m_pPlayer->GetAttributes()->GetTeam())
		{
			m_pMovement->SetAvoidanceEnabled( true );
			m_pMovement->SetRepulsionEnabled( true );
			m_lock_manager.HFClearLocks();
		}

		if (m_pGame)
		{
			const static float PICKUP_TIME = 5.0f;
			start_pickup_timer.Reset(m_pGame->GetSimTime(), PICKUP_TIME);
		}
	}
}


///-------------------------------------------------------------------------
/// Update
///-------------------------------------------------------------------------
void RURoleScrumHalfBack::UpdateLogic( const MabTimeStep& game_time_step )
{
	MABASSERT( m_pPlayer != NULL );

	if ( state >= RoleState::FEEDING )
		m_pPlayer->GetLookAt()->LookAtBallHolder();
	else
		m_pPlayer->GetLookAt()->LookAtNone();

	SETDEBUGSTRING( RoleState::STRINGS[state] );

	if ( is_doing_number_eight_pickup )
	{
		UpdateNumEightPickup();
		return;
	}

	// ########################
	// WJS RLC Debug
	static RoleState lastState = RoleState::LAST;

	if (state != lastState)
	{
		UE_LOG(LogTemp, Display, TEXT("###### RURoleScrumHalfBack::UpdateLogic ***%d"), state);
		lastState = state;
	}
	// ########################

	switch(state)
	{
		case RoleState::PREBIND:				UpdateStatePrebind();			break;
		case RoleState::WAITING:				UpdateStateWaiting();			break;
		case RoleState::FEEDING:				UpdateStateFeeding();			break;
		case RoleState::FOLLOWING:				UpdateStateFollowing();			break;
		case RoleState::CROUCH:					UpdateStateCrouch();			break;
		case RoleState::BALL_FREE:				UpdateStateBallFree();			break;
		case RoleState::PICKUP:					UpdateStatePickup();			break;
		case RoleState::PICKUP_WAIT:			UpdateStatePickupWait();		break;
		case RoleState::BOXKICK_PICKUP_WAIT:	UpdateStateBoxKickPickupWait();	break;
		case RoleState::BOXKICK_WAIT:			UpdateStateBoxKickWait();		break;
		case RoleState::PASS:					UpdateStatePass();				break;
		case RoleState::PASS_WAIT:				UpdateStatePassWait();			break;
		case RoleState::DONE:					UpdateStateDone();				break;
	}

	state_timer += game_time_step.delta_time.ToSeconds();

	//// Clear any errant NOSLOW flags.
	//if (!m_lock_manager.UFIsLocked(UF_SETFACING))
	//	movement->SetFacingFlags( AFFLAG_FACEMOTION );

	SSRole::UpdateLogic( game_time_step );
}

bool RURoleScrumHalfBack::UpdateStatePrebind()
{
	RUZonePositionAssigned* pos_ass = m_pPlayer->GetPosAss();
	MABASSERT( pos_ass != NULL );
	RUZonePosition* pos = pos_ass->GetPosition();

	if ( pos != NULL )
	{
		wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
		m_pPlayer->GetMovement()->SetTargetPosition( pos->GetOrigin(), true );
		m_pPlayer->GetMovement()->SetThrottleAndTargetSpeedByUrgency(0.8f, AS_SPRINT, AS_FASTWALK);
	}

	if (!m_lock_manager.UFIsLocked(UF_SETFACING))
	{
		m_pMovement->SetFacingFlags( AFFLAG_FACEPOS );
		m_pMovement->SetFacingPosition( scrum_phase->GetCurrentScrum().GetScrumOrigin()->GetOrigin() );
		m_pMovement->SetFaceMotionSpeed( -1.0f );
	}

	if ( scrum_start_received )
	{
		state = RoleState::WAITING;
		return false;
	}

	return true;
}

bool RURoleScrumHalfBack::UpdateStateWaiting()
{
	RUZonePositionAssigned* pos_ass = m_pPlayer->GetPosAss();
	MABASSERT( pos_ass != NULL );
	RUZonePosition* pos = pos_ass->GetPosition();

	if ( pos != NULL )
	{
		wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
		m_pPlayer->GetMovement()->SetTargetPosition( pos->GetOrigin(), true );
		m_pPlayer->GetMovement()->SetThrottleAndTargetSpeedByUrgency(0.8f, AS_SPRINT, AS_FASTWALK);
	}

	if( scrum_down_received )
	{
		state = RoleState::FEEDING;
		return false;
	}

	return true;
}

bool RURoleScrumHalfBack::UpdateStateFeeding()
{
	//// Any logic for the feeding state goes here
	//if ( !m_lock_manager.UFIsLocked( UF_SETFACING ) )
	//{
	//	movement->SetFacingPosition(player->GetGame()->GetGameState()->GetPlayRestartPosition());
	//	movement->SetFacingFlags(AFFLAG_FACEPOS);
	//}

	//if ( !m_lock_manager.UFIsLocked( UF_SETWAYPOINT ) )
	//	movement->SetTargetSpeed( movement->GetIdealSpeed(AS_WALK) );

	if( ball_in_received )
	{
		state = RoleState::FOLLOWING;
		RUScrumState current_scrum = scrum_phase->GetCurrentScrum();
		if (m_pPlayer->GetAttributes()->GetTeam() == current_scrum.attacking.team)
			m_pPlayer->GetMovement()->SetMotionSource( MabMemNew( MabMemGetDefaultObjectHeap( this ) ) ScrumPackMotionSource( scrum_phase, scrum_phase->GetCurrentScrum().GetScrumOrigin(), m_pPlayer->GetAttributes()->GetPlayDirection(), m_pPlayer->GetMovement()->GetCurrentPosition(), m_pPlayer->GetAttributes()->GetTeam() == current_scrum.attacking.team) );
		return false;
	}

	return true;
}

bool RURoleScrumHalfBack::UpdateStateFollowing()
{
	// Just set up facing
	static const float HALF_BACK_BALL_FACE_OFFSET = 0.6f;
	FVector face_position = m_pGame->GetBall()->GetCurrentPosition();
	face_position.z += HALF_BACK_BALL_FACE_OFFSET * (float)m_pPlayer->GetAttributes()->GetPlayDirection();

	if ( !m_lock_manager.UFIsLocked( UF_SETFACING ) )
	{
		m_pMovement->SetFacingPosition( face_position );
		m_pMovement->SetFacingFlags( AFFLAG_FACEPOS );
	}

	// Look towards the ball-ish
	m_pPlayer->GetLookAt()->LookAtPosition( face_position );

	return true;
}


bool RURoleScrumHalfBack::UpdateStateCrouch()
{
	// Defending team skips straight
	if ( m_pPlayer->GetAttributes()->GetTeam() == m_pGame->GetGameState()->GetDefendingTeam() )
	{
		//player->GetMovement()->StopAllMovement();
		state = RoleState::DONE;
		return true;
	}

	// check for an early box kick press, passes will still override
	SSHumanPlayer* human_player = m_pPlayer->GetHumanPlayer();
	if (human_player && human_player->GetPassExtendDisplaySide() == 0 && human_player->IsOn( ERugbyGameAction::RUCK_HALF_BOX_KICK ))
		box_kick_selected = true;

	// Maks sure our waypoint is updated. If the scrum ran out of time we might not be at the back.
	if ( !m_lock_manager.UFIsLocked( UF_SETWAYPOINT ))
	{
		RUScrumState current_scrum = scrum_phase->GetCurrentScrum();

		FVector Receiver;
		MabMatrix::MatrixMultiply(Receiver, FVector(0.0f, 0.0f, 3.1f * -m_pPlayer->GetAttributes()->GetPlayDirection()), MabMatrix::RotMatrixY(current_scrum.scrum_angle));

		FVector target_pos = current_scrum.GetScrumOrigin()->GetOrigin() + Receiver;

		wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
		m_pMovement->SetTargetPosition( target_pos, true );

		const static float URGENCY = 1.0f;
		m_pMovement->SetThrottleAndTargetSpeedByUrgency( URGENCY );
	}

	if ( m_pMovement->GetTargetDistance() < 0.3f )
	{				
		//if ( animation->GetDummyHalfVariable()->monitored() )#rc3_legacy_animation  Rewritten this to check for statemachine before calling
		RUPlayerAnimation* anim = m_pPlayer->GetAnimation();
		ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = anim->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
		if ((CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null) || (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::DummyHalf))		
		{
			m_pPlayer->GetMovement()->StopAllMovement();
			RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
			animation->SetVariable( animation->GetDummyHalfVariable(), 1.0f );
			state = RoleState::BALL_FREE;
			return false;
		}
	}

	return true;
}

bool RURoleScrumHalfBack::UpdateStateBallFree()
{
	// check for an early box kick press, passes will still override
	SSHumanPlayer* human_player = m_pPlayer->GetHumanPlayer();
	RUTeam * team = (m_pPlayer && m_pPlayer->GetAttributes()) ? m_pPlayer->GetAttributes()->GetTeam() : nullptr;
	SSEVDSFormationManager * formationManager = team ? team->GetFormationManager() : nullptr;
	SSSetPlayManager * setplayManager = formationManager ? formationManager->GetSetplayManager() : nullptr;
	if (human_player && human_player->GetPassExtendDisplaySide() == 0 && human_player->IsOn( ERugbyGameAction::RUCK_HALF_BOX_KICK ))
		box_kick_selected = true;

	const static float SCRUM_WAIT_BALL_FREE_TIME = 0.0f;

	if(state_timer > SCRUM_WAIT_BALL_FREE_TIME)
	{
		if ( human_player )
		{
			
			//If the modifier is held down, lock all other options
			if (human_player->IsOn(ERugbyGameAction::RUCK_HALF_SET_PLAYS) && setplayManager && setplayManager->AreSetplaysEnabled() && !team->GetStrategy().IsBreakdownStrategyStarted())
			{
				int setPlayIndex = -1;

				//If the modifier key (LT) is held down, update the contextual hud
				if (m_pGame && m_pGame->GetEvents())
				{
					m_pGame->GetEvents()->context_hud_modified(true);
				}

				//Add set play calls here #MB
				if (human_player->IsReleased(ERugbyGameAction::DECISION_TOP))
				{
					setPlayIndex = 0;
				}
				else if (human_player->IsReleased(ERugbyGameAction::DECISION_LEFT))
				{
					setPlayIndex = 1;
				}
				else if (human_player->IsReleased(ERugbyGameAction::DECISION_RIGHT))
				{
					setPlayIndex = 2;
				}
				else if (human_player->IsReleased(ERugbyGameAction::DECISION_BOTTOM))
				{
					setPlayIndex = 3;
				}

				//Start setplay if an index is selected
				if (setPlayIndex != -1)
				{
					FSerialiseFormation* setplay = setplayManager->GetSetplayByIndex(setPlayIndex);
					if (setplay)
					{
						FString setplayName = setplay->name;
						human_player->GetTeam()->GetFormationManager()->GetSetplayManager()->StartSetplayByName(setplayName, human_player);

						if (formationManager)
							formationManager->EnableFormationChangeDuringPhase();

						state = RoleState::DONE;

						//Turn off modifier as it could be hiding contextual hud
						if (m_pGame && m_pGame->GetEvents())
						{
							m_pGame->GetEvents()->context_hud_modified(false);
							m_pGame->GetEvents()->set_play_started(setPlayIndex, setplayName, m_pPlayer);
						}

						// 						ARugbyCharacter* pFirstReceiver = human_player->GetTeam()->GetFormationManager()->GetSetplayFirstReceiver();
						// 						if (pFirstReceiver && pFirstReceiver->GetAnimation())
						// 						{
						// 							pFirstReceiver->GetAnimation()->GetStateMachine().SendRequest("CALL_SET_PLAY");
						// 						}

						return true;
					}
				}
				
			}
			else
			{
				// GGS League: Box kick not in league 
				///// Human scoot
				//if (m_pPlayer->GetHumanPlayer()->GetInputVector().ApproxMagnitude() > MOVE_MAXTARGDIST / 3.0f)
				//{
				//	RUPlayerAnimation* animation = m_pPlayer->GetAnimation();

				//	//if (animation->IsAnimationAvailable(ANIM_PICKUP_RUN)) //#rc3_legacy_animation. Rewritten this to check for statemachine before calling

				//	ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
				//	if (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::DummyHalf)
				//	{
				//		float scoot_angle = MabMath::Rad2Deg(-MabMath::AngleDelta(m_pPlayer->GetAttributes()->GetTeam()->GetPlayAngle(), SSMath::CalculateAngle(m_pPlayer->GetHumanPlayer()->GetInputVector())));
				//		animation->SetVariable(animation->GetMovementDirectionVariable(), scoot_angle);
				//		animation->PlayAnimation(ANIM_PICKUP_RUN);
				//		state = RoleState::DONE;
				//		return true;
				//	}
				//}
				//else 
				
				// Pass
				if (human_player->GetCachedRoleOption().option_type == ROPT_BALLHOLDER_PASS_DIR ||
					human_player->GetCachedRoleOption().option_type == ROPT_BALLHOLDER_PASS_PLYR ||
					human_player->GetCachedRoleOption().option_type == ROPT_BALLHOLDER_PASS_POS ||
					human_player->GetCachedRoleOption().option_type == ROPT_BALLHOLDER_PASS_PLAYMAKER)
				{
					// Perform a pass
					human_player->GetCachedRoleOption().pass_type = PT_DUMMYHALF;
					state = RoleState::PASS;
					human_player->UpdateCachedOption();
				}
				/*/// Box kick
				else if (human_player && human_player->GetPassExtendDisplaySide() == 0 && human_player->IsReleased(ERugbyGameAction::RUCK_HALF_BOX_KICK))
				{
					RUStrategyPos pos;
					m_pGame->GetStrategyHelper()->FindBestBoxKickPos(m_pPlayer, pos);

					FVector kick_to_pos(pos.x, 0.0f, pos.z);

					if (m_pPlayer->GetActionManager()->CanUseAction(ACTION_KICK))
					{
						m_pPlayer->GetRole()->StartActionKickToPosition(KICKTYPE_BOXKICK, kick_to_pos, true);

						return true;
					}
				}
				else if (human_player->GetOnDuration(ERugbyGameAction::RUCK_HALF_BOX_KICK) > 0.25f)
				{
					RUInputKickInterface* kick_interface = m_pGame->GetInputManager()->GetKickInterface();
					m_pPlayer->GetActionManager()->HFUnlock(HF_KICK);
					kick_interface->StartKick(m_pPlayer, KICKTYPE_BOXKICK);
					human_player->ResetKickActionTimer();
					state = RoleState::BOXKICK_WAIT;
					return true;
				}*/

				// GGS AJ: Hide contextual hud when not holding button
				if (m_pGame && m_pGame->GetEvents())
				{
					m_pGame->GetEvents()->context_hud_modified(false);
				}
			}
			/// Just pick the ball up
			if (start_pickup_timer.GetNumTimerEventsRaised() > 0)
			{
				RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
				//if (animation->IsAnimationAvailable(ANIM_PICKUP)) //#rc3_legacy_animation. Rewritten this to check for statemachine before calling

				ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
				if (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::DummyHalf || CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null) //dummy half or number eight
				{
					animation->PlayAnimation(ANIM_PICKUP);
					state = RoleState::PICKUP_WAIT;
					return true;
				}
			}
		}
		else
		{
			const float SETPLAY_CHANCE = 0.5f;
			float rng = m_pGame->GetRNG()->RAND_CALL(float);

			if (rng < SETPLAY_CHANCE && setplayManager->AreSetplaysEnabled())
			{
				int setPlayIndex = m_pGame->GetRNG()->RAND_RANGED_CALL(int, 3) + 1;


				//Start setplay if an index is selected
				if (setPlayIndex != -1 && setplayManager)
				{
					FSerialiseFormation* setplay = setplayManager->GetSetplayByIndex(setPlayIndex);
					if (setplay)
					{
						FString setplayName = setplay->name;
						m_pPlayer->GetAttributes()->GetTeam()->GetFormationManager()->GetSetplayManager()->StartSetplayByName(setplayName, nullptr);

						if (formationManager)
							formationManager->EnableFormationChangeDuringPhase();

						state = RoleState::DONE;

						//Turn off modifier as it could be hiding contextual hud
						if (m_pGame && m_pGame->GetEvents())
						{
							m_pGame->GetEvents()->context_hud_modified(false);
							m_pGame->GetEvents()->set_play_started(setPlayIndex, setplayName, m_pPlayer);
						}

						// 						ARugbyCharacter* pFirstReceiver = human_player->GetTeam()->GetFormationManager()->GetSetplayFirstReceiver();
						// 						if (pFirstReceiver && pFirstReceiver->GetAnimation())
						// 						{
						// 							pFirstReceiver->GetAnimation()->GetStateMachine().SendRequest("CALL_SET_PLAY");
						// 						}

						return true;
					}
				}
			}
			else
			{
				int scoot = ShouldScoot();
				if (scoot != 0)
				{
					RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
					//if (animation->IsAnimationAvailable(ANIM_PICKUP_RUN)) //#rc3_legacy_animation. Rewritten this to check for statemachine before calling

					ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
					if (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::DummyHalf)
					{
						// Calculate angle to run on
						float angle = m_pPlayer->GetAttributes()->GetTeam()->GetPlayAngle();
						angle += (float)scoot * (PI / 8);

						// Set the target positon
						FVector target;
						SSMath::AngleToMabVector3(angle, target);
						wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
						m_pMovement->SetTargetPosition(m_pMovement->GetCurrentPosition() + (target * 10.0f), true);

						animation->SetVariable(animation->GetMovementDirectionVariable(), float(scoot * (PI / 8.0f)));
						animation->PlayAnimation(ANIM_PICKUP_RUN);
						state = RoleState::DONE;
						return true;
					}
				}
				else
				{
					AddDummyHalfPassOptions(role_options);
					if (role_options.GetNumOptions() > 0)
						state = RoleState::PASS;
					else
					{
						RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
						//if (animation->IsAnimationAvailable(ANIM_PICKUP))//#rc3_legacy_animation. Rewritten this to check for statemachine before calling

						ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
						if (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::DummyHalf || CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null) //dummyhalf or number eight
						{
							animation->PlayAnimation(ANIM_PICKUP);
							state = RoleState::DONE;
							return true;
						}
					}
				}
			}
		}
	}

	return true;
}

int RURoleScrumHalfBack::ShouldScoot()
{
	// TODO : Need AI logic for whether the dummy half should scoot or not
	FVector position = m_pMovement->GetCurrentPosition();

	// If we are close to the touchline the don't scoot (or we might go over it
	FieldExtents extents = m_pGame->GetSpatialHelper()->GetFieldExtents();
	float dist_to_closest_touch = (extents.x / 2.0f) - MabMath::Fabs( position.x );
	const float MIN_DIST_TO_TOUCH_TO_ALLOW = 3.6f;

	if ( dist_to_closest_touch < MIN_DIST_TO_TOUCH_TO_ALLOW )
		return 0;

	/// 0 = pass, 1 = scoot
	RUTeam* team = m_pPlayer->GetAttributes()->GetTeam();
	TEAM_SLIDER_FIELD field_pos = m_pGame->GetSpatialHelper()->GetTeamDBFieldPosition( team, m_pPlayer->GetMovement()->GetCurrentPosition() );

	float scoot_prob = team->GetDbTeam().GetNormalisedScrumWinSlider( field_pos );
	if ( m_pGame->GetRNG()->RAND_RANGED_CALL(float, 1.0f) < scoot_prob )
	{
		return m_pGame->GetRNG()->RAND_RANGED_CALL(float, 1.0f) < 0.5f ? 1 : -1;
	}

	return 0;
}

bool RURoleScrumHalfBack::UpdateStatePickup()
{

	SETDEBUGSTRING("PICKUP");

	// you should be crouched
	RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
	if (animation != NULL)
	{
		animation->SetVariable(animation->GetDummyHalfVariable(), 1.0f);
	}

	//RUPlayerAnimation* animation = player->GetAnimation();

	//if (animation->IsAnimationAvailable(ANIM_PICKUP)) //#rc3_legacy_animation. Rewritten this to check for statemachine before calling
	ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
	if (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::DummyHalf || CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null) //dummy half or should be number eight
	{
		animation->PlayAnimation(ANIM_PICKUP);
		state = RoleState::PICKUP_WAIT;
	}
	return true;
}

bool RURoleScrumHalfBack::UpdateStatePickupWait()
{
	// Advance to next state?
	if ( m_pGame->GetGameState()->GetBallHolder() == m_pPlayer )
		state = RoleState::DONE;
	return true;
}


bool RURoleScrumHalfBack::UpdateStateBoxKickPickupWait()
{
	//if ( game->GetGameState()->GetBallHolder() == player )
	state = RoleState::BOXKICK_WAIT;
	return true;
}

bool RURoleScrumHalfBack::UpdateStateBoxKickWait()
{

	SSHumanPlayer * human = m_pPlayer->GetHumanPlayer();

	if (m_pGame->GetGameState()->GetBallHolder() == m_pPlayer)
	{
		state = RoleState::DONE;
	}
	else if (start_pickup_timer.GetNumTimerEventsRaised() > 0) //incase we try to pass while holding box kick aim, we need to bail out and avoid NMA
	{
		state = RoleState::PICKUP;
	}
	else if (human && (human->GetCachedRoleOption().option_type == ROPT_BALLHOLDER_PASS_DIR ||
		human->GetCachedRoleOption().option_type == ROPT_BALLHOLDER_PASS_PLYR ||
		human->GetCachedRoleOption().option_type == ROPT_BALLHOLDER_PASS_POS ||
		human->GetCachedRoleOption().option_type == ROPT_BALLHOLDER_PASS_PLAYMAKER))
	{
		// Fire it right now
		human->GetCachedRoleOption().pass_type = PT_DUMMYHALF;
		state = RoleState::PASS;
		human->UpdateCachedOption();
		return true;
	}
	return true;
}

bool RURoleScrumHalfBack::UpdateStatePass()
{
	if ( m_pPlayer->GetHumanPlayer() == NULL )
	{
		MABASSERT( role_options.GetNumOptions() > 0 );
		role_options.Clear();
		AddDummyHalfPassOptions( role_options );
		MABASSERT( role_options.GetNumOptions() > 0 );
	}

	// If for whatever reason we have already started a pass then allow this to pass through
	// Otherwise start the pass ourselves
	if ( m_pPlayer->GetActionManager()->IsActionRunning( ACTION_PASS ) || (role_options.ExecuteMostEffectiveAction(this) ) )
	{
		//MABLOGDEBUG(  "DummyHalf: Passing" );
		state = RoleState::PASS_WAIT;
		state_timer = 0.0f;
		role_options.Clear();
		return false;
	}
	else
	{
		RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
		//if (animation->IsAnimationAvailable(ANIM_PICKUP))//#rc3_legacy_animation. Rewritten this to check for statemachine before calling

		ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
		if (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::DummyHalf || CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null) //dummyhalf or number eight
		{
			animation->PlayAnimation(ANIM_PICKUP);
			state = RoleState::PICKUP_WAIT;
		}
		// Abort, we can't find anybody to pass to...
		//MABLOGDEBUG(  "RURoleRuckScrumHalf: PASS Aborted, switching to PICKUP" );
		//state = RoleState::PICKUP_WAIT;
	}
	return true;
}

bool RURoleScrumHalfBack::UpdateStatePassWait()
{
	// WJS RLC Changed time out from 5 secons to .1 seconds
	float PASS_WAIT_TIME_OUT = 0.1f;

	// Then wait until the ball has been released before advancing state
	if ((state_timer > PASS_WAIT_TIME_OUT || animation_finished_notified) && notified_collect && (m_pGame->GetGameState()->GetBallHolder() != m_pPlayer || !m_pPlayer->GetActionManager()->IsActionRunning( ACTION_PASS )) )
		state = RoleState::DONE;

	return true;
}

bool RURoleScrumHalfBack::UpdateStateDone()
{
	// Reset throttle and target speed
	//if ( !m_lock_manager.UFIsLocked( UF_SETWAYPOINT ))
		//movement->SetThrottleAndTargetSpeedByUrgency( 0.75f );

	m_pMovement->SetAvoidanceEnabled(false);
	m_pMovement->SetRepulsionEnabled(false);
	return true;
}

/// Can we initiate a prewound pass now
bool RURoleScrumHalfBack::CanExecuteActionNow( RU_ACTION_INDEX action )
{
	if ( action == ACTION_PASS )
		return m_pGame->GetGameState()->GetBallHolder() == m_pPlayer || state == RoleState::PASS;

	return SSRole::CanExecuteActionNow( action );
}

bool RURoleScrumHalfBack::IsRoleOptionStillValid( const SSGenericRoleOption* option )
{
	switch ( option->option_type )
	{
		case ROPT_BALLHOLDER_PASS_DIR:
		case ROPT_BALLHOLDER_PASS_POS:
		case ROPT_BALLHOLDER_PASS_PLYR:
		case ROPT_BALLHOLDER_PASS_PLAYMAKER:
			return true;
			break;
		default:
			return SSRole::IsRoleOptionStillValid( option );
			break;
	}

}

///-------------------------------------------------------------------------
/// GetFitness
///-------------------------------------------------------------------------

int RURoleScrumHalfBack::GetFitness(const ARugbyCharacter* player, const SSRoleArea*)
{
	if(player->GetAttributes()->GetPlayerPosition() & PP_SCRUM_HALF)
		return 100;
	if(player->GetAttributes()->GetPlayerPosition() & PP_FLY_HALF_STAND_OFF)
		return 80;
	if(player->GetAttributes()->GetPlayerPosition() & PP_BACK)
		return 50;

	return 0;
}

void RURoleScrumHalfBack::AddDummyHalfPassOptions( SSRoleOptionList& role_options_to_add_to )
{
	const float DIST_TO_BE_CONSIDERED_CLOSE = 5.0f;

	// If we are close to the sideline then we want to pass in field (or run)
	FieldExtents extents = m_pGame->GetSpatialHelper()->GetFieldExtents();
	FVector player_pos = m_pMovement->GetCurrentPosition();
	bool close_to_sideline = MabMath::Fabs( MabMath::Fabs( player_pos.x ) - (extents.x/2.0f) ) < DIST_TO_BE_CONSIDERED_CLOSE;

	if ( close_to_sideline )
	{
		// Work out who we should pass it to
		TArray<ARugbyCharacter*> receivers;
		// Pass in the opposite direction to the side of the field that we are on
		int pass_direction = (int) MabMath::Sign( -player_pos.x );
		int pass_inclusion_roles;
		RUActionPass::CalculateReceiversInPassDirection( m_pPlayer, pass_direction, receivers, pass_inclusion_roles );

		// Just pass to the first player in the list if their is one
		ARugbyCharacter* pass_target_player = RUActionPass::GetBestReceiverFromList( m_pPlayer, receivers, RUActionPass::RUPASS_SKIP_AUTOSELECT );
		if ( pass_target_player != nullptr && m_pPlayer->GetActionManager()->CanUseAction( ACTION_PASS ) )
		{
			SSGenericRoleOption* option = role_options_to_add_to.AddRoleOption(ROPT_BALLHOLDER_PASS_PLYR, 80, pass_target_player);
			option->pass_to_player = pass_target_player;
			option->pass_type = PT_DUMMYHALF;
		}
	}
	else
	{
		// Normal field behaviour

		// Choose ONLY best pass option if we're inside our own 20
		//
		float distanceFromOwnGoal = m_pGame->GetStrategyHelper()->GetBallRangeFromOwnGoal(m_pPlayer->GetAttributes()->GetTeam());
		if (distanceFromOwnGoal > 25.0f)
		{
			wwNETWORK_TRACE_JG("RURoleScrumHalfBack::AddDummyHalfPassOptions AddPriorityPassOptions");
			const int MIN_PASS_PRIORITY = 20;
			AddPriorityPassOptions( role_options_to_add_to, MIN_PASS_PRIORITY, true, PT_DUMMYHALF );
		}

		wwNETWORK_TRACE_JG("RURoleScrumHalfBack::AddDummyHalfPassOptions AddBestPassOption");
		/// Second add of priority pass options for kick roles that we don't want to perform standard checks on
		AddBestPassOption( role_options_to_add_to, PT_DUMMYHALF );
	}
}

///-------------------------------------------------------------------------
/// returns true if we're interruptable, false if we're not
///-------------------------------------------------------------------------

bool RURoleScrumHalfBack::IsInterruptable() const
{
	if ( is_doing_number_eight_pickup )
	{
		return false;
	}

	return state == RoleState::DONE;
}

// custom warp
void RURoleScrumHalfBack::WarpToWaypoint()
{
	RUZonePositionAssigned* pos_ass = m_pPlayer->GetPosAss();
	MABASSERT( pos_ass != NULL );

	RUZonePosition* pos = pos_ass->GetPosition();

	if (pos != NULL)
	{
		wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
		m_pPlayer->GetMovement()->SetTargetPosition(pos->GetOrigin(), true);

		m_pMovement->SetFacingFlags(AFFLAG_FACEPOS);
		m_pMovement->SetFacingPosition(scrum_phase->GetCurrentScrum().GetScrumOrigin()->GetOrigin());
		m_pMovement->SetFaceMotionSpeed(-1.0f);
		SSRole::WarpToWaypoint();
	}
	//else
	//{
	//	UE_DEBUG_BREAK();
	//}
}


RURoleScrumHalfBack::ScrumPackMotionSource::ScrumPackMotionSource( RUGamePhaseScrum* scrum_phase, IRUOrigin* origin, ERugbyPlayDirection play_dir, const FVector initial_offset, bool attacking )
: scrum_phase( scrum_phase )
, origin( origin )
, local_time( 0.0f )
, play_dir( play_dir )
, initial_offset( initial_offset )
, attacking( attacking )
, target_y_offset( 0.0f )
, target_position( initial_offset )
{

}

bool RURoleScrumHalfBack::ScrumPackMotionSource::GetFrame( MotionFrame& frame ) const
{
	frame.time = local_time;
	frame.state.current_position = target_position;

	static const float HALF_BACK_BALL_FACE_OFFSET = 0.6f;
	FVector face_position = scrum_phase->GetGame()->GetBall()->GetCurrentPosition();
	face_position.z += HALF_BACK_BALL_FACE_OFFSET * (float)play_dir;

	frame.state.current_facing_angle = SSMath::CalcAngleFromPoints( frame.state.current_position , face_position );

	return true;
}

void RURoleScrumHalfBack::ScrumPackMotionSource::Update( float delta_time )
{
	local_time += delta_time;

	RUScrumState current_scrum = scrum_phase->GetCurrentScrum();

	float ball_position = current_scrum.ball_state;
	MabMath::Clamp( ball_position, -5.0f, 5.0f );

	// Check if we are winning
	bool we_are_winning = false;
	if ( attacking )
		we_are_winning = ball_position > 0.0f;
	else
		we_are_winning = ball_position < 0.0f;

	// Determine if we need to account for wheeling
	bool scrum_wheeling = false;
	if ( MabMath::Fabs(current_scrum.scrum_angle) > ( PI / 8.0f ) && !we_are_winning )
		scrum_wheeling = true;

	// Direction the scrum is facing
	float scrum_direction = (float)current_scrum.attacking.team->GetPlayDirection();

	// How far away the two halfbacks will be offset
	static const float HALF_BACK_FOLLOWING_OFFSET = 0.15f;

	// Scale to convert ball_state to a half circle.
	static const float BALL_STATE_SCALE = 12.0f;
	float y_distance = (ball_position * scrum_direction ) / BALL_STATE_SCALE;
	MabMath::Clamp( y_distance, -0.35f, 0.35f );
	y_distance += HALF_BACK_FOLLOWING_OFFSET * (float)play_dir;

	static const float WHEELED_POSITION = 0.3f;
	if ( scrum_wheeling )
		y_distance = WHEELED_POSITION * play_dir;

	const static float y_offset_convergence_time = 2.5f;
	const static float y_offset_convergence_amount = 0.75f; // Don't set to 1 as this won't really work
	/// from = lerp(to, from, pow(base, dt / convergence));  where base = 1 - convergence_amount
	target_y_offset = MabMath::Lerp( y_distance, target_y_offset, MabMath::Pow( 1.0f - y_offset_convergence_amount, delta_time / y_offset_convergence_time ) );

	// How far away from the scrum the halfback will stand
	static const float HALF_BACK_FOLLOWING_DISTANCE = 3.4f;

	// Calculate where the halfback would stand in relation to the ball/scrum
	float hypot = HALF_BACK_FOLLOWING_DISTANCE;
	static const float HALF_BACK_FOLLOWING_DISTANCE_R7 = 2.0f;
	bool isSevensGame = false;// Nick  WWS 7s to Womens //SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetGameMode() == GAME_MODE_SEVENS;
	if(isSevensGame)
		hypot = HALF_BACK_FOLLOWING_DISTANCE_R7;
	float tri_y = MabMath::Sin( target_y_offset * PI  ) * hypot;

	// Inverted
	tri_y *= -1;

	float tri_x = MabMath::Sqrt( hypot * hypot - tri_y * tri_y ) * scrum_direction;

	// Clamp to get movement edges closer to scrum
	static const float X_CLAMP = 2.5f;
	static const float Y_CLAMP = 3.4f;
	MabMath::Clamp( tri_x, -X_CLAMP, X_CLAMP);
	MabMath::Clamp( tri_y, -Y_CLAMP, Y_CLAMP);

	FVector target_pos;
	MabMatrix::MatrixMultiply(target_pos, FVector(tri_x, 0.0f, tri_y), MabMatrix::RotMatrixY(current_scrum.scrum_angle));

	const static float target_convergence_time = 1.5f;
	const static float target_convergence_amount = 0.97f; // Don't set to 1 as this won't really work
	target_position = MabMath::Lerp( origin->GetOrigin() + target_pos, target_position, MabMath::Pow( 1.0f - target_convergence_amount, delta_time / target_convergence_time ) );
}

void RURoleScrumHalfBack::DoNumberEightPickup()
{
	is_doing_number_eight_pickup = true;
	ball_in_received = true;

	state = RoleState::NUM_EIGHT_PICKUP;
	m_pGame->GetEvents()->number_eight_pickup( m_pPlayer );

	RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
	//static const char* ANIM_PICKUP_RUN = "grab";

	//if ( animation->IsAnimationAvailable( ANIM_PICKUP_RUN ) )
	//#rc3_legacy_animation. Rewritten this to check for statemachine before calling

	ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
	if (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::DummyHalf || CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null) //dummy half or number eight
	{
		animation->PlayAnimation( "grab" );
	}
}

bool RURoleScrumHalfBack::UpdateNumEightPickup()
{
	UpdateStateFollowing();

	bool human_wants_to_go_elsewhere = false;
	SSHumanPlayer* human = m_pPlayer->GetHumanPlayer();
	/// For human players - we allow them to do a scoot around the ruck
	/// Pass or box kick from the ruck
	if ( human != NULL )
	{
		FVector input_vector = human->GetInputVector();
		// If the input vector is fairly large, switch into scoot mode
		human_wants_to_go_elsewhere = input_vector.ApproxMagnitude() > MOVE_MAXTARGDIST / 3.0f;
	}

	if ( is_doing_number_eight_pickup && (m_pPlayer->GetMovement()->GetMotionSource() == NULL || human_wants_to_go_elsewhere) && has_finished_num_eight_pickup_animation )
	{
		is_doing_number_eight_pickup = false;	// Catch the case of the player not passing, end of motion source reached.
		state = RoleState::DONE;
		m_pGame->GetEvents()->number_eight_pickup_done( m_pPlayer );

		m_pPlayer->GetMovement()->SetMotionSource( NULL );
	}

	return false;
}

void RURoleScrumHalfBack::BallPassed( ARugbyCharacter* passer, ARugbyCharacter*, const FVector&, PASS_TYPE, bool )
{
	if ( passer != m_pPlayer )
	{
		return;
	}

	if ( is_doing_number_eight_pickup )
	{
		// Player has passed, set motion source to NULL to avoid warp.
		m_pPlayer->GetMovement()->ForceMotionSourceLock();
		m_pPlayer->GetMovement()->SetMotionSource( NULL );

		is_doing_number_eight_pickup = false;
		state = RoleState::DONE;
	}
}
