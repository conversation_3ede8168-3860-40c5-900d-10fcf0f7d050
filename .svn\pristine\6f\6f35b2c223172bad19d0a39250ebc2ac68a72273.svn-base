/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Roles/Competitors/RURoleBaseBallHolder.h"

#include "Match/AI/Actions/RUActionPass.h"
#include "Match/AI/Actions/RUActionTacklee.h"
#include "Match/AI/Actions/RUActionTackler.h"
#include "Match/AI/Formations/SSEVDSFormationConstants.h"
#include "Match/AI/Formations/SSEVDSFormationEnum.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/Ball/SSBall.h"
#include "Match/Camera/SSCameraManager.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerLookAt.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/RUPlayerState.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Match/PlayerProfile/SIFPlayerProfileManager.h"
#include "Match/PlayerProfile/SIFPlayerProfileManager.h"
#include "Match/PlayerProfile/SIFPlayerProfilePropertyDefs.h"
#include "Match/RugbyUnion/RUBlackboard.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUMovementState.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SIFGameObject.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Match/SSPlayerFilter.h"
#include "Match/SSSpatialHelper.h"
#include "Utility/RURandomNumberGenerator.h"

#include "RugbyGameInstance.h"

#ifdef ENABLE_GAME_DEBUG_MENU
#include "Match/Debug/SIFDebug.h"
#include "Match/Debug/RUGameDebugSettings.h"
#endif

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
#include "Utility/consoleVars.h"
#include "DrawDebugHelpers.h"
#endif

//--------------------------------------------------------------------------
// Linebreak motion source/behaviour
//--------------------------------------------------------------------------

class RUMotionSupportRun : public RUMotionSource
{
	MABRUNTIMETYPE_HEADER(RUMotionSupportRun);
public:
	RUMotionSupportRun( ARugbyCharacter* player, const FVector& target_position, float urgency ) :
		RUMotionSource()
		,player( player )
		, target_position( target_position )
		,curr_pos(FVector::ZeroVector)
		, local_time( 0.0f )
		, urgency( urgency )
	{
		float curr_speed = player->GetMovement()->GetCurrentSpeed();
		const static float MIN_ADD_ENTRY_SPEED = 1.0f;
		float min_entry_speed = curr_speed + MIN_ADD_ENTRY_SPEED;
		MabMath::ClampUpper( min_entry_speed, player->GetMovement()->GetMaxSpeed() );
		wwNETWORK_TRACE_JG_DISABLED("RUMotionSupportRun");
		player->GetMovement()->SetCurrentVelocity( player->GetMovement()->GetCurrentVelocity().Unit() * min_entry_speed );
		//:jb maybe this if zero isn't good: curr_pos = player->GetMovement()->GetMovementState().current_position;
	}

	virtual ~RUMotionSupportRun()
	{
		//SIF_DEBUG_DRAW( Remove3DLine( 943922333 + player->GetAttributes()->GetIndex() ) );
		player->GetMovement()->ForceWaypointChange();
	}

	/// Get the current frame from the motion source
	/// Returns false if there are no more frames, true if there are frames remaining
	virtual bool GetFrame( MotionFrame& frame ) const
	{
		// Interesting issue occuring on Xbox One when a player is set to PRE JOIN for rucks. Seems like the current position is NaN, reached_target would then be true, doesn't LOOK like it breaks anything - But if we can figure out why this happens that would be good!
		MABASSERT(FINITE(curr_pos.x) &&FINITE(curr_pos.y) &&FINITE(curr_pos.z) &&
			!ISNAN(curr_pos.x) && !ISNAN(curr_pos.y) && !ISNAN(curr_pos.z));
		float dist = SSMath::GetXZPointToPointDistance( curr_pos, target_position );
		const static float DIST = 1.0f;
		bool reached_target = dist < DIST;

		if ( reached_target )
		{
			player->GetMovement()->ForceWaypointChange();
			//SIF_DEBUG_DRAW( Remove3DLine( 943922333 + player->GetAttributes()->GetIndex() ) );
			return false;
		} else {
			//SETDEBUGLINE( 943922333 + player->GetAttributes()->GetIndex(), player->GetMovement()->GetCurrentPosition(), target_position, MabColour::Blue, MabColour::Green );
		}

		/// Exit conditions
		ARugbyCharacter* ball_holder = player->GetGameWorld()->GetGameState()->GetBallHolder();
		if ( !ball_holder )
			return false;

		float dist_ahead = (curr_pos.z - player->GetGameWorld()->GetBall()->GetCurrentPosition().z) * player->GetAttributes()->GetPlayDirection();
		const static float MAX_DIST_AHEAD_NON_TACKLE = -2.0f;
		const static float MAX_DIST_AHEAD_TACKLE = -2.0f;
		if ( dist_ahead > MAX_DIST_AHEAD_NON_TACKLE )
			return false;

		if ( ball_holder && ball_holder->GetActionManager()->IsActionRunning( ACTION_TACKLEE ) && dist_ahead > MAX_DIST_AHEAD_TACKLE )
			return false;

		/// Good to go!
		frame.time = local_time;
		frame.state.current_position = curr_pos;
		return true;
	}


	virtual void Update( float delta_time )
	{
		local_time += delta_time;

		RUPlayerMovement* movement = player->GetMovement();

		/// Let AI overboost like humans can when performing a line break run
		bool is_ai = player->GetAttributes()->GetTeam()->GetNumHumanPlayers() == 0;
		if ( is_ai )
		{
			const static float FULL_THROTTLE[DIF_MAX] = { 1.0f, 1.0f, 1.1f, 1.15f, 1.20f };
			movement->SetAccelerationThrottle( FULL_THROTTLE[ player->GetGameWorld()->GetGameSettings().difficulty ] );
		}

		// To get the same kind of motion we apply the same locomotion update with a modified target
		MOVEMENT_PARAMETERS params = movement->GetMovementParameters();
		MOVEMENT_INTERMEDIATE_STATE istate = movement->GetMovementIntermediateState();
		MOVEMENT_STATE state = movement->GetMovementState();

		/// Project a point from our current tangent out a distance and then use this as a locomotion guide
		params.target_position = target_position;
		movement->SetThrottleAndTargetSpeedByUrgency( urgency, AS_SPRINT );

		/// Apply the locomotion and work out how far we moved
		movement->ApplyLocomotion( params, state, istate, delta_time, 0.0f );
		curr_pos = state.current_position;
	}


	/// Query interface for the type of motion information that this motion source provides
	virtual bool ProvidesPositions() const { return true; }
	virtual bool ProvidesFacing() const { return false; }
private:
	ARugbyCharacter* player;
	FVector target_position;
	FVector curr_pos;
	float local_time;
	float urgency;
};


MABRUNTIMETYPE_IMP1(RUMotionSupportRun, RUMotionSource);

MABRUNTIMETYPE_IMP1( RURoleBaseBallHolder, RURoleSupportBase )

RURoleBaseBallHolder::RURoleBaseBallHolder( SIFGameWorld* game )
: RURoleSupportBase( game ),
  attack_vars(),
  no_players_obstructing_progress( false ),
  last_obstruction_update_time( -1 ),
  use_attacking_option_timer(),
  special_anim_end_time( 0.0f ),
  last_update_time_with_ball( 0.0f ),
  time_with_ball( 0.0f ),
  look_side_timer(),
  look_side(0),
  last_offload_prob_calc_tackle_index(-1),
  base_ai_offload_desire( 0.0f ),
  base_ai_offload_rand( 0.0f ),
  next_support_update_time( 0.0f ),
  kick_this_possession(false),
  sprint_cam_on( false )
{
}

void RURoleBaseBallHolder::Enter( ARugbyCharacter* player )
{
	RURoleSupportBase::Enter( player );

	ResetBaseAttackVariables();
	//MABLOGDEBUG( "BaseBallHolder Entered on pid %d...", player->GetAttributes()->GetIndex() );
	// Make them have an option of doing a slightly quicker first option

	look_side_timer.Reset( m_pGame->GetSimTime(), 1.0f, false );
	look_side = 0;
	last_obstruction_update_time = MabTime( 0.0f );
	no_players_obstructing_progress = false;
	special_anim_end_time = MabTime( 0.0f );

	// Set the player name text to be on if this player is human controlled
	if ( player->GetHumanPlayer() != NULL )
	{
		//player->SetDisplayName( true );
	}

	time_with_ball = 0.0f;
	last_update_time_with_ball = m_pGame->GetSimTime()->GetAbsoluteTime(); // Force an update by using current - a bit
	last_offload_prob_calc_tackle_index = -1;
	base_ai_offload_desire = 0.0f;
	base_ai_offload_rand = 0.0f;
	sprint_cam_on = false;
	next_support_update_time = 0.0f;
	UpdateTimeWithBall( last_update_time_with_ball );

	TEAM_SLIDER_FIELD field_pos = m_pGame->GetSpatialHelper()->GetTeamDBFieldPosition( player->GetAttributes()->GetTeam(), player->GetMovement()->GetCurrentPosition() );
	kick_this_possession = false;
	if ( player->GetAttributes()->GetPlayerPosition() & PP_BACK )
	{
		float kick_prob = player->GetAttributes()->GetTeam()->GetDbTeam().GetNormalisedBackPassKickSlider( field_pos );

		// Addressing HES ticket where there are too many kicks in R7 games
		// Nick WWS 7s to Womens 13s //
		//if(m_pGame->GetGameSettings().game_settings.GameModeIsR7())
		//{
		//	kick_prob *= 0.5f;
		//}

		kick_this_possession = m_pGame->GetRNG()->RAND_RANGED_CALL(float, 1.0f ) < kick_prob;
	}


	RUGameEvents* events = m_pGame->GetEvents();
	events->tackle.Add( this, &RURoleBaseBallHolder::OnTackle );
	events->line_break.Add( this, &RURoleBaseBallHolder::OnLineBreak );


	// We're holding onto the ball now, someone support us!
	// During sevens's matches we force a support role area for the ball holder
	// Dewald WW - I've fixed up the sevens formations so that pods are used etc. So this might not be needed anymore
	//if(game->GetGameSettings().game_settings.GameModeIsR7())
	//	player->GetAttributes()->GetTeam()->GetFormationManager()->OverrideAreaNumPlayers( "BHsupport", 1, FMROLE_SUPPORT );
}

void RURoleBaseBallHolder::EndSprintCamIfActive()
{
	if ( sprint_cam_on )
	{
		m_pGame->GetEvents()->change_to_camera( GAME_CAM_INVALID );
		sprint_cam_on = false;
		m_pPlayer->GetMovement()->SetSprintModeEnabled( false );
	}
}

void RURoleBaseBallHolder::OnTackle( const RUTackleResult& result )
{
	if ( result.successful )
	{
		EndSprintCamIfActive();
		// During sevens's matches we force a support role area for the ball holder
		// Nick WWS 7s to Womens 13s //
		//if(m_pGame->GetGameSettings().game_settings.GameModeIsR7())
		//	m_pPlayer->GetAttributes()->GetTeam()->GetFormationManager()->OverrideAreaNumPlayers( "BHsupport", 1, ERugbyFormationRole::SUPPORT );
	}
}

void RURoleBaseBallHolder::OnLineBreak( ARugbyCharacter* /*player*/, LINE_BREAK_TYPE /*break_type*/ )
{
	int humans_our_team		= m_pPlayer->GetAttributes()->GetTeam()->GetNumHumanPlayers();
	int humans_their_team	= m_pPlayer->GetAttributes()->GetOppositionTeam()->GetNumHumanPlayers();

	int	profile_idx = 0;
	bool cinematics_enabled = false;
	bool glory_cam_on = false;

	if (SIFPlayerProfileManager::GetInstance() && SIFPlayerProfileManager::GetInstance()->GetMasterProfile())
	{
		const MabObservedValueList* value_list = NULL;
		value_list = SIFPlayerProfileManager::GetInstance()->GetMasterProfile()->GetNamedValueList();

		if (value_list)
		{
			profile_idx = value_list->GetNamedValueIndex(PLAYER_PROFILE_GLORY_CAM);
		}
	}

	if (SIFPlayerProfileManager::GetInstance() && SIFPlayerProfileManager::GetInstance()->GetMasterProfile())
	{
		const MabObservedValueList* value_list = NULL;
		value_list = SIFPlayerProfileManager::GetInstance()->GetMasterProfile()->GetNamedValueList();
		if (profile_idx != -1 && value_list)
		{
			const MabNamedValue& cse = value_list->GetNamedValue(profile_idx);
			glory_cam_on = cse.ToBoolean();
		}
	}

	/// Turn on glory cam
	//bool glory_cam_on = SIFPlayerProfileManager::GetInstance()->GetMasterProfile()->GetNamedValueList()->GetNamedValue(PLAYER_PROFILE_GLORY_CAM)->ToBoolean();
	bool one_human_playing_ai = m_pPlayer->GetHumanPlayer() != NULL && humans_our_team == 1 && humans_their_team == 0;
	bool ai_vs_ai = humans_our_team == 0 && humans_their_team == 0;

	FCameraSets*  current_camera_set = nullptr;

	if (m_pGame->GetCameraManager()) m_pGame->GetCameraManager()->GetCameraSet(current_camera_set);	

	if (current_camera_set)
	{
		static const char* BEHIND_MID_CAMERA_NAME = "BEHIND_MID";
		static const char* BEHIND_CLOSE_CAMERA_NAME = "BEHIND_CLOSE";		
		bool is_behind_mid_cam = MabStringHelper::StringCompareIgnoreCase(current_camera_set->name, BEHIND_MID_CAMERA_NAME) == 0;
		bool is_behind_close_cam = MabStringHelper::StringCompareIgnoreCase(current_camera_set->name, BEHIND_CLOSE_CAMERA_NAME) == 0;
		bool is_network_game = m_pGame->GetGameSettings().game_settings.network_game;
		bool is_match = m_pGame->IsMatch();
		bool bForceTurnOnGloryCam = false; //debug only when speed is not gained by player

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
		bool debugInfo = CVarGloryCamera.GetValueOnGameThread();
		if (GEngine && debugInfo)
		{
			bForceTurnOnGloryCam = true;
		}
#endif

		/// Only when glory cam is on, we are running fast when we make the line break AND
		/// only AI vs AI or human vs AI
		if (!sprint_cam_on 
			&& is_match 
			&& !is_network_game 
			&& (is_behind_mid_cam || is_behind_close_cam) 
			&& glory_cam_on 
			&& ((m_pPlayer->GetMovement()->GetCurrentSpeed() >= m_pPlayer->GetMovement()->GetIdealSpeed(AS_FASTRUN)) || bForceTurnOnGloryCam ) 
			&& (ai_vs_ai || one_human_playing_ai))
		{
			SetSprintCamTensionBySpeed();
			m_pGame->GetEvents()->change_to_camera(GAME_CAM_SPRINTING);			
			m_pPlayer->GetMovement()->SetSprintModeEnabled(true);
			sprint_cam_on = true;
		}
	}


	// During sevens's matches we force a support role area for the ball holder
	// Dewald WW - I've fixed up the sevens formations so that pods are used etc. So this might not be needed anymore
	//if(game->GetGameSettings().game_settings.GameModeIsR7())
	//	player->GetAttributes()->GetTeam()->GetFormationManager()->OverrideAreaNumPlayers( "BHsupport", 1, FMROLE_SUPPORT );
}

/// Exit this role
void RURoleBaseBallHolder::Exit(bool forced)
{
	//player->SetDisplayName( false );
	RURoleSupportBase::Exit(forced);
	EndSprintCamIfActive();

	RUGameEvents* events = m_pGame->GetEvents();
	events->tackle.Remove( this, &RURoleBaseBallHolder::OnTackle );
	events->line_break.Remove( this, &RURoleBaseBallHolder::OnLineBreak );


	EndSpecialAnimation();
}

void RURoleBaseBallHolder::UpdateLogic( const MabTimeStep& game_time_step )
{
	RURoleSupportBase::UpdateLogic( game_time_step );
	UpdateTimeWithBall( game_time_step.abs_time );
	UpdateSpecialAnimations();
	UpdateDebug();
	UpdateSupportRunners( game_time_step );

	/// Sprint camera logic
	if ( sprint_cam_on )
	{
		SetSprintCamTensionBySpeed();
	}
}

struct AttackRunnerEntry
{
	float max_attack;
	int max_runners;
};

//--------------------------------------------------------------------
//
//--------------------------------------------------------------------
void RURoleBaseBallHolder::UpdateSupportRunners( const MabTimeStep& game_time_step )
{
	/// Update rate
	static const float SUPPORT_UPDATE_FREQ = 0.25f;

	next_support_update_time -= game_time_step.delta_time.ToSeconds();

	if ( next_support_update_time > 0.0f )
		return;

	static const AttackRunnerEntry attack_runners[] = { { 0.5f, 0 }, { 0.75f, 1 }, { 0.9f, 2 }, { 1.1f, 3 } };
	static const int N_ATTACK_ENTRIES = 4;
	float team_attack = m_pPlayer->GetAttributes()->GetTeam()->GetDbTeam().GetNormalisedAttack();

	int MAX_RUNNERS = 0;
	for( int a = 0; a < N_ATTACK_ENTRIES; a++ )
	{
		const AttackRunnerEntry& are = attack_runners[a];
		if ( team_attack <= are.max_attack )
		{
			MAX_RUNNERS = are.max_runners;
			break;
		}
	}

	/// Iterate over all high participation players and see if they have a good gap that they can run into
	const static float FWD_TIME_Z_DIST_BIAS = -1.0f;
	RUStrategyPos strat_pos;

	/// Check to see if there are presently more than the requested number of runners active
	const SIFRugbyCharacterList& ordered_participating_players = m_pPlayer->GetAttributes()->GetTeam()->GetOrderedParticipatingPlayers();
	int n = MabMath::Min( MAX_RUNNERS, (int) ordered_participating_players.size() );
	int j = 0, i = 0;

	int existing_count = 0;
	for( i = 0, j = 0; i < (int) ordered_participating_players.size() && j < n; i++ )
	{
		ARugbyCharacter* support_player = ordered_participating_players[i];
		if ( support_player->GetMovement()->GetMotionSource() && support_player->GetMovement()->GetMotionSource()->RTTGetType() == RUMotionSupportRun::RTTGetStaticType() )
			existing_count++;
	}

	if ( existing_count >= MAX_RUNNERS )
		return;


	/// Iterate over all support players and see if they can find a gap to run into
	for( i = 0, j = 0; i < (int) ordered_participating_players.size() && j < n; i++ )
	{
		/// Exit conditions for current player
		ARugbyCharacter* support_player = ordered_participating_players[ i ];

		/// Cannot be ballholder
		if ( support_player == m_pPlayer )
			continue;

		/// Cannot have motion locked
		if ( support_player->GetActionManager()->UFIsLocked( UF_SETWAYPOINT ) )
			continue;

		/// Can't be a human player
		if ( support_player->GetHumanPlayer() != NULL )
			continue;

		/// If we are ahead of the ball holder then don't consider us
		if ( ((support_player->GetMovement()->GetCurrentPosition().z - m_pPlayer->GetMovement()->GetCurrentPosition().z) * m_pPlayer->GetAttributes()->GetPlayDirection() ) > 0.0f )
			continue;

		/// If we aren't in a high participation state than we're not supporting
		if ( support_player->GetState()->GetParticipationLevel() == PL_OBSERVING )
			continue;

		/// If we're not presently moving forward
		const static float MIN_FORWARD_VEL = 0.f;
		if ( (support_player->GetMovement()->GetCurrentVelocity().z * m_pPlayer->GetAttributes()->GetPlayDirection()) < MIN_FORWARD_VEL)
			continue;


		/// Find the best gap for us to run into
		m_pStrategyHelper->FindBestGap( support_player, strat_pos, FWD_TIME_Z_DIST_BIAS, false );
		FVector intercept_target( strat_pos.x, 0.0f, strat_pos.z );

		OPP_INTERCEPT_RESULT intercept_result = { NULL, FVector::ZeroVector, 0.0f, 0.0f };
		OPP_INTERCEPT_RESULT_LIST intercept_results;
		OPP_INTERCEPT_PARAMS params;
		const static float MIN_INTERCEPT_SPEED = 2.5f;
		const static float MIN_PLAYER_SPEED = 2.5f;
		params.min_intercept_speed = MIN_INTERCEPT_SPEED;
		params.min_player_speed = MIN_PLAYER_SPEED;

		RLPResultList result_list;
		RLP_FILTERPARAMETERS filter_params;
		filter_params.filters = RLP_FILTER_TEAM;
		filter_params.team = m_pPlayer->GetAttributes()->GetOppositionTeam();

		m_pGame->GetPlayerFilters()->GetPlayerPlayerDistanceSort()->SetReferencePlayer( support_player );
		m_pGame->GetFilteredPlayerList( result_list, filter_params, m_pGame->GetPlayerFilters()->GetPlayerPlayerDistanceSort() );

		/// Find out where we will be intercepted along this line
		bool intercepted = m_pGame->GetStrategyHelper()->FindOppositionInterceptPoint( support_player, result_list, intercept_target, intercept_result, &params, &intercept_results );

		if ( intercepted )
		{
			/// Work out the z relative position where we will get intercepted
			float intercept_diff = (intercept_result.intercept_point.z - intercept_result.intercepting_player->GetMovement()->GetCurrentPosition().z) * m_pPlayer->GetAttributes()->GetPlayDirection();
			const static float MIN_DIST_CONSIDERED = -1.0f;
			bool good_to_attempt_support_run = intercept_diff > MIN_DIST_CONSIDERED;

			/// If the gap that we have to run through is a certain ratio of our dist to the mid point then we can maybe squeeze through it
			if ( intercept_results.size() >= 2 )
			{
				float play_dir = (float) m_pPlayer->GetAttributes()->GetPlayDirection();
				OPP_INTERCEPT_RESULT& a = intercept_results[0];
				OPP_INTERCEPT_RESULT& b = intercept_results[1];
				const FVector& a_pos = a.intercepting_player->GetMovement()->GetCurrentPosition();
				const FVector& b_pos = b.intercepting_player->GetMovement()->GetCurrentPosition();
				const FVector& s_pos = support_player->GetMovement()->GetCurrentPosition();
				float a_z = (a_pos.z - s_pos.z) * play_dir;
				float b_z = (b_pos.z - s_pos.z) * play_dir;
				FVector half_way = (a_pos + b_pos) * 0.5f;
				float dist_supp_to_half_way = SSMath::GetXZPointToPointDistance( s_pos, half_way );
				float dist_def_to_half_way = SSMath::GetXZPointToPointDistance( a_pos, half_way );
				float ratio = dist_supp_to_half_way / dist_def_to_half_way;
				const static float MIN_ATTEMPT_RATIO = 1.1f;

				good_to_attempt_support_run |= a_z > 0.0f && b_z > 0.0f && ratio < MIN_ATTEMPT_RATIO;
			}

			FVector delta = intercept_result.intercept_point - support_player->GetMovement()->GetCurrentPosition();
			if ( good_to_attempt_support_run && delta.Magnitude() > 0.0f )
			{
				float intercept_point_mag = delta.Magnitude();
				static const float RUN_THROUGH = 7.0f;
				static const float MAX_DIST = 8.0f;
				intercept_point_mag += RUN_THROUGH;
				MabMath::ClampUpper( intercept_point_mag, MAX_DIST );
				intercept_target = support_player->GetMovement()->GetCurrentPosition() + delta.Unit() * intercept_point_mag;
				m_pGame->GetSpatialHelper()->ClampWaypointToSideLine( intercept_target );

				const static float URGENCY = 0.97f;
				RUMotionSupportRun *lb_motion = MabMemNew(MabMemGetDefaultObjectHeap(this) )  RUMotionSupportRun( support_player, intercept_target, URGENCY );
				support_player->GetMovement()->SetMotionSource( lb_motion );
				break;
			}
		}
		j++;
	}

	next_support_update_time = SUPPORT_UPDATE_FREQ;
}

/// Advance this role in cutscene.
void RURoleBaseBallHolder::UpdateCutScene( const MabTimeStep& game_time_step )
{
	SSRole::UpdateLogic(game_time_step);
	m_pGame->GetCutSceneManager()->PostProcessNonActorMovement(m_pPlayer);
}

//static const char* SPECIAL_SUB_ANIMS[] = { "WBPARM" };

bool RURoleBaseBallHolder::IsSpecialAnimationPlaying() const
{
//RUPORT
//	if ( !player->GetAnimation()->IsSubAnimationPlaying() )
//		return false;
//
//	// Check to see if the current sub anim is one of ours
//	int n_sub_anims = sizeof( SPECIAL_SUB_ANIMS ) / sizeof( const char* );
//
//	for( int i = 0; i < n_sub_anims; ++i )
//	{
//		char* name = player->GetAnimation()->GetSubAnimation().seq_0_ref.GetAnimationName();
//		if ( strncmp( name, SPECIAL_SUB_ANIMS[i], strlen( SPECIAL_SUB_ANIMS[i] ) ) == 0 )
//			return true;
//	}
	return false;
}

void RURoleBaseBallHolder::UpdateSpecialAnimations()
{
//RUPORT
//	if ( player != game->GetBallHolder() )
//		return;
//
//	// The anim has been played and stopped
//	if ( special_anim_end_time.ToFrames() == -2 )
//		return;
//
//	bool non_run_actions_in_use = false;
//
//	RUActionManager* action_mgr = player->GetActionManager();
//	for ( int i = 0; i < ACTION_LAST; i++ )
//	{
//		if ( action_mgr->IsActionInUse( (RU_ACTION_INDEX) i ) && !RUActionManager::IsRunAction( (RU_ACTION_INDEX) i ) ) {
//			non_run_actions_in_use = true;
//			break;
//		}
//	}
//
//	// Check if we want to kill the animation
//	if ( (special_anim_end_time.ToFrames() >= 0 && game->GetSimulationTime() >= special_anim_end_time) || non_run_actions_in_use )
//	{
//		EndSpecialAnimation();
//		return;
//	}
//
//	// Detect if we need to or can play
//	if ( player->GetAnimation()->IsSubAnimationPlaying() || IsSpecialAnimationPlaying() )
//		return;
//
//	// Now detect animations and play as necessary
//	const PlayInfo& play_info = game->GetStrategyHelper()->GetPlayInfo();
//
//	/// Play a pumping fist sub anim if we're confident we're going to score
//	/// and there was a great build up
//	const float MIN_DIST_TO_TRIGGER_FIST_PUMP = 5.0f;
//	const float MIN_DIST_TO_TRYLINE = 6.0f;
//	FieldExtents extents = game->GetFieldExtentsExcludingInGoals(); extents.y *= 0.5f;
//
//	float dist_from_goal_line = extents.y - (player->GetCurrentPosition().z * player->GetPlayDirection());
//	if ( play_info.bh_likely_to_score && play_info.bh_lowest_opp_dist > MIN_DIST_TO_TRIGGER_FIST_PUMP && dist_from_goal_line < MIN_DIST_TO_TRYLINE  )
//	{
//		// Work out if it was a good build up
//		int n_passes_and_tackles = play_info.n_passes + play_info.n_tackles;
//		const static int MIN_PASSES_AND_TACKLE = 8;
//		const static float MIN_METRES_GAINED = 20.0f;
//
//		if ( n_passes_and_tackles >= MIN_PASSES_AND_TACKLE && play_info.excitement > 0.8f && play_info.metres_gained > MIN_METRES_GAINED )
//		{
//			MabString anim_name;
//			SIFGameObjectAnimation::SelectAnimationVariant( "WBPARM", anim_name );
//
//			RUSubAnimationDescription sub_anim( anim_name.c_str() );
//			sub_anim.tween_duration = SIMULATION_INTERVAL * 3.0f;
//			//sub_anim.priority = 1;
//			sub_anim.synchronize = true;
//			player->GetAnimation()->SetSubAnimation( sub_anim );
//			special_anim_end_time = game->GetSimulationTime() + SSSimTime( sub_anim.seq_0_ref.GetAnimation()->GetDuration() );
//		}
//	}
}

void RURoleBaseBallHolder::EndSpecialAnimation()
{
//RUPORT
//	if ( !IsSpecialAnimationPlaying() )
//		return;
//
//	// Clear current sub animation and ease out
//	RUSubAnimationDescription sub_animation_description;
//	sub_animation_description.tween_duration = SIMULATION_INTERVAL * 5.0f;
//	player->GetAnimation()->SetSubAnimation( sub_animation_description );
//	special_anim_end_time = SSSimTime( -2 );
}

void RURoleBaseBallHolder::ResetAttackingOptionTimer( bool /*on_role_entry*/ )
{
	MABASSERT( m_pGame != NULL );

	/// With higher difficulty - the AI can make decisions faster than they did before

	float mental_agility = m_pPlayer->GetAttributes()->GetMentalAgility();
	DIFFICULTY difficulty = m_pGame->GetGameSettings().difficulty;
	MABASSERT( difficulty >= DIF_VERYEASY && difficulty < DIF_MAX );

	/// Used as a guide - http://www.humanbenchmark.com/tests/reactiontime/stats.php
	/// 0.11 0.35
	const static float PCT_VARIANCE = 0.25f;	// Can vary up or down 25%
	const static float REACTION_TIMES_BY_DIFFICULTY[DIF_MAX][2] =
		{ { 0.50f, 1.00f },		// VERY EASY - Best mental agility to worst median
		  { 0.20f, 0.50f },		// EASY		 - Best mental agility to worst median
		  { 0.11f, 0.35f },		// NORMAL	 - Best mental agility to worst median
		  { 0.06f, 0.25f },		// HARD		 - Best mental agility to worst median
		  { 0.06f, 0.18f }		// VERY HARD - Best mental agility to worst median
		};
	const static float DECISION_TIMES_BY_DIFFICULTY[DIF_MAX][2] =
		{ { 0.50f, 1.00f },		// VERY EASY - Best mental agility to worst median
		  { 0.45f, 0.70f },		// EASY		 - Best mental agility to worst median
		  { 0.40f, 0.60f },		// NORMAL	 - Best mental agility to worst median
		  { 0.35f, 0.50f },		// HARD		 - Best mental agility to worst median
		  { 0.30f, 0.40f }		// VERY HARD - Best mental agility to worst median
	};
	MABASSERT( mental_agility >= 0.0f && mental_agility <= 1.0f );

	bool is_reaction_time = false && attack_vars.is_under_pressure;// TYRONE : Disabled
	float best_decision_time_for_difficulty  = is_reaction_time ? REACTION_TIMES_BY_DIFFICULTY[difficulty][0] : DECISION_TIMES_BY_DIFFICULTY[difficulty][0];
	float worst_decision_time_for_difficulty = is_reaction_time ? REACTION_TIMES_BY_DIFFICULTY[difficulty][1] : DECISION_TIMES_BY_DIFFICULTY[difficulty][1];

	float base_decision_time_by_mental_agility = MabMath::Lerp( worst_decision_time_for_difficulty, best_decision_time_for_difficulty, mental_agility );
	float variance_mult = 1.0f + (m_pGame->GetRNG()->RAND_RANGED_CALL(float, PCT_VARIANCE ) * 2.0f - PCT_VARIANCE);
	float reaction_time = base_decision_time_by_mental_agility * variance_mult;

	use_attacking_option_timer.Reset( m_pGame->GetSimTime(), reaction_time, false );
}

bool RURoleBaseBallHolder::CanUseAttackingOption()
{
	return use_attacking_option_timer.GetNumTimerEventsRaised() > 0;
}

void RURoleBaseBallHolder::DefaultLookBehaviour()
{
//RUPORT
	RUTeam* team = m_pPlayer->GetAttributes()->GetTeam();
	MABASSERT( team != NULL );

	RUTeam* opposition_team = m_pPlayer->GetAttributes()->GetOppositionTeam();
	MABASSERT( opposition_team != NULL );

	// Look at the player that is closest to me
	ARugbyCharacter* closest_opposition_player = m_pGame->GetSpatialHelper()->FindClosestPlayer( m_pPlayer, &opposition_team->GetPlayers() );

	// No opposition in sandbox
	if ( !closest_opposition_player )
		return;

	// Work out if the player is in front of or behind me
	float player_face_angle = m_pPlayer->GetMovement()->GetCurrentFacingAngle();

	FVector player_to_closest = closest_opposition_player->GetMovement()->GetCurrentPosition() - m_pPlayer->GetMovement()->GetCurrentPosition();
	float player_to_closest_angle = SSMath::CalculateAngle( player_to_closest.x, player_to_closest.z );
	float angle_delta = MabMath::AngleDelta( player_face_angle, player_to_closest_angle );
	float dist_to_player = (m_pPlayer->GetMovement()->GetCurrentPosition() - closest_opposition_player->GetMovement()->GetCurrentPosition()).Magnitude();

	// Check to see if player is in front
	if ( MabMath::Fabs(angle_delta) <= PI / 2 )
	{
		/// If they are too far away then don't look at them
		if ( dist_to_player > 15.0f )
		{
			m_pPlayer->GetLookAt()->LookAtNone();
		/// Otherwise look at them
		}
		else
		{
			m_pPlayer->GetLookAt()->LookAtPlayer( closest_opposition_player );
		}
	/// If they are behind us then
	}
	else
	{
		/// If they are far away then don't look at them
		if ( dist_to_player > 7.0f )
		{
			m_pPlayer->GetLookAt()->LookAtNone();
		}
		else
		{
			/// Look left right because they are chasing
			/// Do not look left if close to the elft sideline and right if close to the
			/// right sideline
			const FVector& player_pos = m_pPlayer->GetMovement()->GetCurrentPosition();
			bool close_to_sideline = MabMath::Fabs( MabMath::Fabs(player_pos.x) - (FIELD_WIDTH / 2.0f) ) < 3.5f;
			bool close_to_left_sideline  = close_to_sideline && (team->GetLeft() * player_pos.x) > 0.0f;
			bool close_to_right_sideline = close_to_sideline && (team->GetRight() * player_pos.x) > 0.0f;

			if ( look_side_timer.GetNumTimerEventsRaised() > 0 )
			{
				if ( look_side == 0 )
				{
					if ( !close_to_left_sideline )
					{
						m_pPlayer->GetLookAt()->LookLeft();
					}
					else
					{
						m_pPlayer->GetLookAt()->LookRight();
					}
				}
				else if ( look_side == 1 )
				{
					m_pPlayer->GetLookAt()->LookAtNone();
				}
				else if ( look_side == 2 )
				{
					if ( !close_to_right_sideline )
					{
						m_pPlayer->GetLookAt()->LookRight();
					}
				}
				else
				{
					m_pPlayer->GetLookAt()->LookAtNone();
				}

				look_side_timer.Reset( m_pGame->GetSimTime(), 0.8f, false );
				look_side = (look_side + 1) % 4;
			}
		}
	}
}

/// Perform base calculations when changing attacking option
// (that can be time consuming but we want to reuse the data)
void RURoleBaseBallHolder::RecalculateBaseAttackVariables()
{
	CalculateBaseAttackVariables( attack_vars );
}

/// Reset these variables to not calculated
void RURoleBaseBallHolder::ResetBaseAttackVariables()
{
	attack_vars.Reset();
}

RURoleBaseBallHolder* EXCLUDE_BH_ROLE = NULL;
float EXCLUDE_PASS_DESIRE = 0.0f;
float EXCLUDE_PASS_RAND = 0.0f;
bool ExcludePlayersWeDontWantToPassTo( ARugbyCharacter* player_to_pass_to )
{
	 bool returnval = EXCLUDE_BH_ROLE->WantsToPassToPlayer( player_to_pass_to, EXCLUDE_PASS_DESIRE, EXCLUDE_PASS_RAND );

	 // Reset their hacks back to defaults?
	 //EXCLUDE_BH_ROLE = NULL;
	 //EXCLUDE_PASS_DESIRE = 0.0f;
	 //EXCLUDE_PASS_RAND = 0.0f;

	 return returnval;
};

void RURoleBaseBallHolder::AddDefaultPassOptions( SSRoleOptionList& role_options_to_add_to, float /*frame_delta*/, int min_priority )
{
	RUTeam* team = m_pPlayer->GetAttributes()->GetTeam();
	MABASSERT( team != NULL );

	RUStrategyHelper* strategy_helper = m_pGame->GetStrategyHelper();
	MABASSERT( strategy_helper != NULL );

	// Limit AI offloading based on the difficulty level
	bool is_offload = RUActionPass::IsAnOffload( m_pPlayer );
	//bool inside_20 = strategy_helper->GetBallRangeFromOwnGoal( team ) < 25.0f;
	TEAM_SLIDER_FIELD field_pos = m_pGame->GetSpatialHelper()->GetTeamDBFieldPosition( team, m_pPlayer->GetMovement()->GetCurrentPosition() );

	//RUPORT
	float pass_desire = 1.0f;
	float pass_rand = 0.0f;
	if ( is_offload )
	{
		RUActionTacklee* tacklee_action = m_pPlayer->GetActionManager()->GetAction<RUActionTacklee>();
		bool can_offload = tacklee_action->CanOffloadNow();
		if (!can_offload)
			return;

		int this_tackle_index = tacklee_action->GetTackleResult().tackle_determination_index;
		if ( this_tackle_index != last_offload_prob_calc_tackle_index )
		{
			// set base offload desire on game difficulty, in difficult games the AI offloads heaps
			const static float MAX_ATTEMPTED_OFFLOAD_DIFFICULTIES[DIF_MAX] = { 0.15f, 0.3f, 0.6f, 0.85f, 1.15f };
																	 //{ 0.15f, 0.3f, 1.0f, 1.2f, 1.45f };
			base_ai_offload_desire = MAX_ATTEMPTED_OFFLOAD_DIFFICULTIES[ m_pGame->GetGameSettings().difficulty ];

			/*if(game->GetGameSettings().game_settings.GameModeIsR7())
				base_ai_offload_desire *= 1.5f;*/

			// adjust the offload desire based on the teams history and style
			float team_offload_likelyhood = 1.0f;
			if ( m_pPlayer->GetAttributes()->GetPlayerPosition() & PP_FORWARD )
				team_offload_likelyhood = team->GetDbTeam().GetNormalisedForwardContactOffloadSlider( field_pos );
			else
				team_offload_likelyhood = team->GetDbTeam().GetNormalisedBackContactOffloadSlider( field_pos );
			base_ai_offload_desire *= team_offload_likelyhood;

			/// Now modify by the offload global slider
			if ( m_pGame->GetGameSettings().game_settings.game_type != GAME_TRAINING && m_pGame->GetGameSettings().game_settings.game_type != GAME_MENU)
			{
				float slider_val = m_pGame->GetGameSettings().game_settings.slider_offload_frequency;
				// Nick WWS 7s to Womens 13s //
				//if (m_pGame->GetGameSettings().game_settings.GameModeIsR7())
				//	slider_val *= 0.4f;

				base_ai_offload_desire *= slider_val;
			}

			/// Reduce offload probability for subsequent tackles in chain
			const static float OFFLOAD_MULTIPLIERS[] = { 1.0f, 0.5f, 0.3f, 0.2f };
			const static int N_OFFLOAD_MULTIPLIERS = sizeof( OFFLOAD_MULTIPLIERS ) / sizeof( float );
			MABUNUSED(OFFLOAD_MULTIPLIERS);
			MABUNUSED(N_OFFLOAD_MULTIPLIERS);
			MABASSERT( tacklee_action->GetTackleReEntryCount() > 0 && tacklee_action->GetTackleReEntryCount() < N_OFFLOAD_MULTIPLIERS );
			float reentry_offload_mult = OFFLOAD_MULTIPLIERS[ tacklee_action->GetTackleReEntryCount() - 1 ];
			base_ai_offload_desire *= reentry_offload_mult;

			base_ai_offload_rand = m_pGame->GetRNG()->RAND_RANGED_CALL(float, 1.0f );
			last_offload_prob_calc_tackle_index = this_tackle_index;
		}

		//// Initialise per frame ai offload desire to base offload desire
		pass_desire = base_ai_offload_desire;
		pass_rand = base_ai_offload_rand;
	}

	wwNETWORK_TRACE_JG("attack_vars.is_under_pressure: %d, attack_vars.approx_bh_will_score: %d, attack_vars.best_player_to_pass_to != NULL: %d", attack_vars.is_under_pressure, attack_vars.approx_bh_will_score, attack_vars.best_player_to_pass_to != NULL);


	// Add in the default pass options - assume that the base attack variables setup has been called
	// Just try and pass to the most appropriate player for now
	if ( (attack_vars.is_under_pressure && !attack_vars.approx_bh_will_score) &&
		attack_vars.best_player_to_pass_to != NULL && m_pPlayer->GetActionManager()->CanUseAction(ACTION_PASS) &&
		WantsToPassToPlayer( attack_vars.best_player_to_pass_to, pass_desire, pass_rand ) )
	{
		// WJS RLC ###### Remove hard coded 8, 
		// Changed form 8 to 6 Get from settings??
		//int min_players_behind = 8;
		int min_players_behind = 6;


		// WJS RLC Todo remove , Sevens games not required refer Cole
#ifdef ENABLE_SEVENS_MODE
		// Default value for min players is already 8, but if we're playing an R7 game, thats obviously too much - Dewald WW
		// Nick  WWS 7s to Womens //
		//if(m_pGame->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_SEVENS)
		//	min_players_behind = 3;
#endif
		bool receiver_has_broken_defensive_line = strategy_helper->HasBrokenThroughDefensiveLine( attack_vars.best_player_to_pass_to, min_players_behind );

		wwNETWORK_TRACE_JG("receiver_has_broken_defensive_line: %d, attack_vars.has_broken_defensive_line: %d", receiver_has_broken_defensive_line, attack_vars.has_broken_defensive_line);


		// Check to see if we have broken the defensive line - if we have
		// check to see if the player we are passing to has also - if they have then pass them
		// the ball otherwise don't
		if ( !attack_vars.has_broken_defensive_line || (attack_vars.has_broken_defensive_line && receiver_has_broken_defensive_line) )
		{
			// Plus only pass to them when their pass priority is a certain amount above the
			// ball holder's!

			// This number has a rather significant effect on the feel of the game!
			const static float MIN_REQUIRED_RECEIVER_FORWARD_PROGRESS = -2.3f;
			FVector approx_intercept_point;

			OPP_INTERCEPT_RESULT opp_intercept_result = { NULL, FVector::ZeroVector, 0.0f, 0.0f };
			float receiver_forward_prog = strategy_helper->FindForwardProgressInterceptPoint( attack_vars.best_player_to_pass_to, opp_intercept_result );
			wwNETWORK_TRACE_JG("receiver_forward_prog: %f, attack_vars.approx_bh_forward_prog: %f", receiver_forward_prog, attack_vars.approx_bh_forward_prog);
			approx_intercept_point = opp_intercept_result.intercept_point;
			float receiver_forward_prog_differential = receiver_forward_prog - attack_vars.approx_bh_forward_prog;
			bool receiver_will_make_required_forward_progress = receiver_forward_prog_differential > MIN_REQUIRED_RECEIVER_FORWARD_PROGRESS;

			wwNETWORK_TRACE_JG("receiver_forward_prog_differential: %f, approx_bh_will_score: %d, receiver_will_make_required_forward_progress: %d", receiver_forward_prog_differential, attack_vars.approx_bh_will_score, receiver_will_make_required_forward_progress);

			// Check to see if the ball holder will make it over the line - if not then do not pass it!
			if ( !attack_vars.approx_bh_will_score && receiver_will_make_required_forward_progress )
			{
				wwNETWORK_TRACE_JG("Add Pass Option {%4d} %s", __LINE__, __FILE__);
				SSGenericRoleOption *option = role_options_to_add_to.AddRoleOption(ROPT_BALLHOLDER_PASS_PLYR, attack_vars.best_player_to_pass_to_effectiveness, m_pPlayer);
				if(option)
				{
					option->pass_to_player = attack_vars.best_player_to_pass_to;
				}
			}
		}
	}

	// If we get to this point, we're just running along, and the following code will check if we should pass yet.
	if ( !attack_vars.approx_bh_will_score && !attack_vars.has_broken_defensive_line  )
	{
		// What does the ball holder consider as minimum priority to pass to?
		// Use our pass priority as a guide
		int bh_min_priority = 0;
		RUBlackBoard& blackboard = team->GetBlackBoard();

		const MabVariant* val = blackboard.GetEntry( RUBB_ATTRIB_PASS_PRIORITY, m_pPlayer );
		if ( val )
			bh_min_priority = val->ToInt();

		// Make the ball holders priority drop over time so they don't hold it for too long
		// But only if they will not make lots of forward progress
		const float FORWARD_PROG_PUSH_ON_DIST = 8.0f;
		if ( attack_vars.approx_bh_forward_prog < FORWARD_PROG_PUSH_ON_DIST )
		{
			// Now we subtract a factor for the time we have held the ball.
			// This ensures that the player will tend to pass the ball a bit more before getting tackled
			const static float PP_DROP_PER_SECOND_HELD = 8.0f;
			// Make passing more frequent in R7
			//if(game->GetGameSettings().game_settings.GameModeIsR7())
			//	PP_DROP_PER_SECOND_HELD = 2.0f;

			bh_min_priority -= (int) (time_with_ball * PP_DROP_PER_SECOND_HELD);
		}

		min_priority = (int) MabMath::Max( (float) min_priority, (float) bh_min_priority );
		EXCLUDE_BH_ROLE = this;	/// TYRONE : YUCK - but out of time!!!
		EXCLUDE_PASS_DESIRE = pass_desire;
		EXCLUDE_PASS_RAND = pass_rand;

		wwNETWORK_TRACE_JG("RURoleBaseBallHolder::AddDefaultPassOptions AddPriorityPassOptions");
		AddPriorityPassOptions( role_options_to_add_to, min_priority, true, PT_UNKNOWN, ExcludePlayersWeDontWantToPassTo );
	}
}

void RURoleBaseBallHolder::AddDefaultSideStepOptions( SSRoleOptionList& role_options_to_add_to )
{
	bool close_to_sideline = MabMath::Fabs( (FIELD_WIDTH / 2.0f) - MabMath::Fabs(m_pPlayer->GetMovement()->GetCurrentPosition().x) ) < 4.0f;
	if ( close_to_sideline )
	{
		return;
	}

	// If the AI is not running very fast then don't do a sidestep
	const static float MIN_AI_SIDESTEP_SPEED = 1.5f;
	if ( m_pPlayer->GetMovement()->GetCurrentSpeed() < MIN_AI_SIDESTEP_SPEED )
		return;

	// If the closest player is behind us then do not add this as an option.
	if ( attack_vars.closest_opp_player_by_time != NULL )
	{
		bool player_is_behind_me = !SSMath::AmIBehind( m_pPlayer, attack_vars.closest_opp_player_by_time );
		if ( player_is_behind_me )
		{
			return;
	}
	}

	if ( attack_vars.is_in_proximity_action_range && m_pPlayer->GetActionManager()->CanUseAction(ACTION_SIDE_STEP) )
	{
		role_options_to_add_to.AddRoleOption(ROPT_BALLHOLDER_SIDESTEP,50,m_pPlayer);		// SSS_CLEVER?
	}
}

void RURoleBaseBallHolder::AddDefaultFendOptions( SSRoleOptionList& role_options_to_add_to )
{
	// Add in the default sidestep options - assume that the base attack variables setup has been called
	if ( attack_vars.is_in_proximity_action_range && m_pPlayer->GetActionManager()->CanUseAction(ACTION_FEND) )
	{
		role_options_to_add_to.AddRoleOption(ROPT_BALLHOLDER_FEND,50,m_pPlayer);
	}
}

void RURoleBaseBallHolder::AddDefaultShoulderBargeOptions( SSRoleOptionList& role_options_to_add_to )
{
	// If the closest player is behind us then do not add this as an option
	if ( attack_vars.closest_opp_player_by_time != NULL )
	{
		bool player_is_behind_me = !SSMath::AmIBehind( m_pPlayer, attack_vars.closest_opp_player_by_time );
		if ( player_is_behind_me )
		{
			return;
		}
	}

	if ( attack_vars.is_in_proximity_action_range && m_pPlayer->GetActionManager()->CanUseAction(ACTION_FEND) )
	{
		role_options_to_add_to.AddRoleOption(ROPT_BALLHOLDER_SHOULDER_BARGE,50,m_pPlayer);
	}
}

void RURoleBaseBallHolder::AddDefaultDummyOptions( SSRoleOptionList& role_options_to_add_to )
{
	// If the closest player is behind us then do not add this as an option
	if ( attack_vars.closest_opp_player_by_time != NULL )
	{
		bool player_is_behind_me = !SSMath::AmIBehind( m_pPlayer, attack_vars.closest_opp_player_by_time );
		if ( player_is_behind_me )
		{
			return;
		}
	}

	if ( attack_vars.is_in_proximity_action_range && attack_vars.best_player_to_pass_to != NULL && !attack_vars.approx_bh_will_score &&
		!m_pPlayer->GetActionManager()->IsActionRunning( ACTION_TACKLEE ) &&
		 m_pPlayer->GetActionManager()->CanUseAction( ACTION_PASS ) )
	{
		int dummy_side = attack_vars.best_player_to_pass_to->GetMovement()->GetCurrentPosition().x > m_pPlayer->GetMovement()->GetCurrentPosition().x ? 1 : -1;

		SSGenericRoleOption *option = role_options_to_add_to.AddRoleOption(ROPT_BALLHOLDER_DUMMY,50,m_pPlayer);
		if(option)
			option->pass_dir = dummy_side;
	}
}


void RURoleBaseBallHolder::AddDefaultRunOptions( SSRoleOptionList& role_options_to_add_to )
{
	/// Add the current run options
	bool allow_lb_run = false;
	if ( m_pPlayer->GetActionManager()->CanUseAction( ACTION_BHRUN ) && (!attack_vars.has_broken_defensive_line || !allow_lb_run) )
	{
		role_options_to_add_to.AddRoleOption(ROPT_BALLHOLDER_GENERIC_RUN, 50, m_pPlayer);
	}

	if ( allow_lb_run && m_pPlayer->GetActionManager()->CanUseAction( ACTION_LBRUN ) && attack_vars.has_broken_defensive_line )
	{
		role_options_to_add_to.AddRoleOption(ROPT_BALLHOLDER_LINEBREAK_RUN, 50, m_pPlayer);
	}
}

void RURoleBaseBallHolder::UpdateTimeWithBall( const MabTime& abs_game_time )
{
	// Update the amount of time we have had with the ball
	if ( abs_game_time > last_update_time_with_ball )
	{
		// If we have the ball now
		if ( m_pPlayer && m_pGame->GetGameState()->GetBallHolder() == m_pPlayer )
		{
			if ( time_with_ball < 0.0f )
			{
				time_with_ball = 0.0f;
			}
			else
			{
				time_with_ball += (abs_game_time - last_update_time_with_ball).ToSeconds();

				// During sevens's matches we force a support role area for the ball holder
				// Nick WWS 7s to Womens 13s //
				//if(m_pGame->GetGameSettings().game_settings.GameModeIsR7())
				//{
					// Ok, we can tell them to back off now
				//	if(time_with_ball >= 10.0f)
				//		m_pPlayer->GetAttributes()->GetTeam()->GetFormationManager()->OverrideAreaNumPlayers( "BHsupport", 0, ERugbyFormationRole::SUPPORT );
				//}
			}
		}
		else
		{
			// We don't have the ball now
			time_with_ball = -1.0f;
		}
	}

	// Update the control time
	last_update_time_with_ball = abs_game_time;
}

bool RURoleBaseBallHolder::GetArePlayersObstructingForwardProgress()
{
	if ( last_obstruction_update_time.ToSeconds() != m_pGame->GetSimTime()->GetAbsoluteTime().ToSeconds() )
	{
		RLPResultList result;
		m_pGame->GetStrategyHelper()->GetPlayersObstructingForwardProgress( m_pPlayer, ACTION_TACKLER, result  );

		/// If there is no-one in this space then we *really* don't want to pass
		no_players_obstructing_progress = ( result.empty() );
		last_obstruction_update_time = m_pGame->GetSimTime()->GetAbsoluteTime();
	}

	return !no_players_obstructing_progress;
}

void RURoleBaseBallHolder::UpdateDebug()
{
#ifdef ENABLE_GAME_DEBUG_MENU
	const int RUPLAYER_MDD_KEY = 19990000;

	if ( SIFDebug::GetGameDebugSettings()->GetBallholderDebugEnabled() )
	{
		MabString str( 1024,
			"can_change_time			  : %0.2f\n"
			"is_under_pressure            : %s\n"
			"is_an_offload                : %s\n"
			"is_in_proximity_action_range : %s\n"
			"has_broken_defensive_line    : %s\n"
			"in_clear                     : %s\n"
			"approx_bh_will_score         : %s\n"
			"approx_bh_forward_prog       : %0.1f\n"
			"time_with_ball               : %0.1f\n",
			use_attacking_option_timer.GetTimeout(),
			(attack_vars.is_under_pressure ? "true" : ""),
			(RUActionPass::IsAnOffload( m_pPlayer ) ? "true" : ""),
			(attack_vars.is_in_proximity_action_range ? "true" : ""),
			(attack_vars.has_broken_defensive_line ? "true" : ""),
			(attack_vars.in_clear ? "true" : ""),
			(attack_vars.approx_bh_will_score ? "true" : "" ),
			(attack_vars.approx_bh_forward_prog),
			(time_with_ball)
			);

		const FVector& player_pos = m_pPlayer->GetMovement()->GetCurrentPosition();
		//#rc3_legacy_debug_draw  		SETDEBUGTEXTWORLD( RUPLAYER_MDD_KEY + 0, player_pos, str );

		FVector y_off( 0.0f, 0.05f, 0.0f );
		//SETDEBUGLINE( RUPLAYER_MDD_KEY + 1, player_pos + y_off, closest_opp_player_by_time->GetMovement()->GetCurrentPosition()   + y_off, MabColour::Red, MabColour::Red );
		//SETDEBUGLINE( RUPLAYER_MDD_KEY + 2, player_pos + y_off, closest_opponent_by_distance->GetMovement()->GetCurrentPosition() + y_off, MabColour( 1.0f, 0.6f, 0.0f ), MabColour( 1.0f, 0.6f, 0.0f ) );
//#rc3_legacy_debug_draw  		SETDEBUGLINE( RUPLAYER_MDD_KEY + 3, player_pos + y_off, (attack_vars.best_player_to_pass_to != NULL) ? (attack_vars.best_player_to_pass_to->GetMovement()->GetCurrentPosition()			+ y_off) : (player_pos + y_off), MabColour::Green, MabColour::Green );
		//SETDEBUGLINE( RUPLAYER_MDD_KEY + 5, player_pos + y_off, approx_bh_intercept_point			+ y_off, 	MabColour::Cyan, MabColour::Cyan );
//#rc3_legacy_debug_draw  		SETDEBUGLINE( RUPLAYER_MDD_KEY + 6, player_pos + y_off, player_pos + FVector( 0.0f, y_off.y, attack_vars.approx_bh_forward_prog * player->GetAttributes()->GetPlayDirection() ), MabColour::Blue, MabColour::Blue );
	} 
	else 
	{
		SIF_DEBUG_DRAW( RemoveText( RUPLAYER_MDD_KEY + 0 ) );
		const int MAX_LINES = 6;
		for( int i = 1; i < MAX_LINES; i++ )
		{
			SIF_DEBUG_DRAW( Remove3DLine( RUPLAYER_MDD_KEY + i ) );
		}
	}
#endif
}

bool RURoleBaseBallHolder::WantsToPassToPlayer( ARugbyCharacter* pass_to_player, float pass_desire, float pass_rand )
{
	// Only limit if we can offload now
	PASS_META pass_meta;
	RUActionPass::GetPassMeta( m_pPlayer, pass_to_player, FVector::ZeroVector, pass_meta );

	if ( pass_meta.difficulty >= 1.0f )
		return false;

	const float OFFLOAD_DESIRE_DIFFICULTY_AFFECT = 0.5f;
	float ai_pass_desire = pass_desire;
	ai_pass_desire *= (1.0f - (pass_meta.difficulty * OFFLOAD_DESIRE_DIFFICULTY_AFFECT));

	bool ai_wants_to_pass = pass_rand < ai_pass_desire;
	return ai_wants_to_pass;
}

void RURoleBaseBallHolder::SetSprintCamTensionBySpeed()
{	
	const static float MIN_SPEED = 5.0f;
	const static float MAX_SPEED = 40.0f * 1000.0f / 3600.0f;
	float t = (m_pPlayer->GetMovement()->GetCurrentSpeed() - MIN_SPEED) / (MAX_SPEED - MIN_SPEED);
	MabMath::Clamp(t, 0.0f, 1.0f);
	const static float MIN_TENSION = 12.0f;
	const static float MAX_TENSION = 15.0f;
	float new_tension = MabMath::Lerp(MIN_TENSION, MAX_TENSION, t);
	//handles[0]->SetProperty("EyeTension", new_tension);	
	bool EyeTensionUpdated = m_pGame->GetCameraManager()->SetGloryCamEyeTension(new_tension);

	//UE_LOG(LogTemp, Display, TEXT("SetSprintCamTensionBySpeed new_tension: '%3f' '%d'"), new_tension, (int) EyeTensionUpdated);
	ensureAlways(EyeTensionUpdated);

//#if 0 //#rc3_legacy_camera
//	/// Adjust sprint cam spring tension based on speed
//	MabEVDS* evds = SIFApplication::GetApplication()->GetEVDS();
//	const auto container = evds->GetContainer(CAMERA_SETTINGS_FILE_PATH);
//	MabEVDSEventHandles handles;
//	container->GetEventsByTypeAndName( "CreateMovementType", handles, "GameCamSprint" );
//	MABASSERT( handles.size() == 1 );
//	if ( !handles.empty() )
//	{
//		const static float MIN_SPEED = 5.0f;
//		const static float MAX_SPEED = 40.0f * 1000.0f / 3600.0f;
//		float t = (player->GetMovement()->GetCurrentSpeed() - MIN_SPEED) / (MAX_SPEED - MIN_SPEED);
//		MabMath::Clamp( t, 0.0f, 1.0f );
//		const static float MIN_TENSION = 12.0f;
//		const static float MAX_TENSION = 15.0f;
//		float new_tension = MabMath::Lerp( MIN_TENSION, MAX_TENSION, t );
//		handles[0]->SetProperty( "EyeTension", new_tension );
//	}
//#endif
}
