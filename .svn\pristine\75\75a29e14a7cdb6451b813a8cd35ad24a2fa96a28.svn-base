/*--------------------------------------------------------------
|        Copyright (C) 1997-2012 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#include "WWUIProModeMainMenuListboxPopulator.h"
#include "Databases/RUGameDatabaseManager.h"
#include "Match/SIFUIConstants.h"
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "WWUIScreenTemplate.h"
#include "RugbyGameInstance.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Database.h"
#include "UI/GeneratedHeaders/WWUIScreenMainMenu_UI_Namespace.h"
#include "UI/Screens/WWUIScreenMainMenu.h"

//Components
#include "Button.h"
#include "Widget.h"
#include "WidgetTree.h"
#include "RichTextBlock.h"
#include "Utility/Helpers/SIFGameHelpers.h"
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "UI/Screens/Components/WWUIMainMenuField.h"
#include "WWUITranslationManager.h"
#include "UI/Interfaces/ProModePopulatedInterface.h"

//#if PLATFORM_XBOX360
//#pragma optimize( "", off )
//#endif



//#ifdef ENABLE_PRO_MODE

UWWUIProModeMainMenuListboxPopulator::BaseListboxChildCallback::BaseListboxChildCallback( UWidget * listbox_node_, const MabVector<ProModeInfoStruct >& career_info_list_ )
	: listbox_node( listbox_node_ )
	, pro_mode_info_list( career_info_list_ )
	, list_index( 0u )
{
}

void UWWUIProModeMainMenuListboxPopulator::MainMenuListboxChildCallback::Callback( UUserWidget* new_node )
{

	// GG DJH: Moved Widget visual setup to be handled by the widget itself, Blueprint sided

	UWWUIListField* wwField = Cast<UWWUIListField>(new_node);
	if (!wwField->Implements<UProModePopulatedInterface>())
	{
		MABBREAKMSG("Widget does not implement Pro Mode Interface!");
		return;
	}

	UButton* button_node = wwField->GetButtonWidget();
	if( button_node == NULL )
	{
		MABBREAKMSG("Widget does not implement GetButtonWidget() function or button is null!");
		return;
	}

	UWWUIScreenManager* pScreenManager = SIFApplication::GetApplication()->GetUIScreenManager();
	if (!pScreenManager)
	{
		ensure(pScreenManager);
		return;
	}
	int32 MainMenuScreenIndex = pScreenManager->FindScreenTemplate(Screens_UI::MainMenu);
	UWWUIScreenMainMenu * mainMenuRef = Cast<UWWUIScreenMainMenu>(pScreenManager->GetScreenTemplate(MainMenuScreenIndex));
	if (mainMenuRef)
	{
		button_node->OnClicked.AddDynamic(mainMenuRef, &UWWUIScreenMainMenu::ProModeLoadOnClick);
	}
	else
	{
		ensure(mainMenuRef);
	}

	const ProModeInfoStruct& info_struct = pro_mode_info_list[ list_index ];

	const size_t career_slot_index = info_struct.slot_index;
	//const MabString career_slot_str( 0, "%u", career_slot_index );
	//const MabString career_one_based_slot_str( 0, "%u", career_slot_index + 1 );

	const MabDate& last_modification_date = info_struct.additional_info.last_modification_time;

	FBeAProMenuPopulationPack beAProData;
	beAProData.ClubTeam = FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.club_team));
	beAProData.InternationalTeam = FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.international_team));
	beAProData.Competition = FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.competition_name));
	beAProData.Round = FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.round_string));
	beAProData.NextMatchHomeTeam = FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.next_match_home_team));
	beAProData.NextMatchAwayTeam = FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.next_match_away_team));
	beAProData.Year = FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.year_string));
	beAProData.ProName = FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.pro_name));
	beAProData.LastModificationTime = FDateTime(last_modification_date.GetYear(), last_modification_date.GetMonth(), last_modification_date.GetDayNumber(), last_modification_date.GetHour(), last_modification_date.GetMinute(), last_modification_date.GetSeconds());
	beAProData.SaveIndex = (int)career_slot_index;

	const FName InfoPanelWidgetName = FName("BeAProInfoPanel");
	UWidget* infoPanel = mainMenuRef->FindChildWidget(InfoPanelWidgetName); // DJH: Should be a UUserWidget - if not, this will crash later on.

	IProModePopulatedInterface::Execute_OnProPopulated(wwField, (UUserWidget*)infoPanel, beAProData);

	// Set the text of the main button to be Load Career n, where n corresponds to the slot.
	//int32 childIdx;
	//UTextBlock * button_text_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(button_node, FName(*MAIN_MENU_BUTTON_TEXT_NODE_NAME), childIdx));
	//if( button_text_node == NULL )
	//{
	//	MABBREAK();
	//	return;
	//}
	
	//FString title = FString::Printf(TEXT("%s | %s"),
	//	*pro_name,
	//	*date_text
	//);
	//pMainMenuField->SetTitle( FText::FromString(title));

	// Process the listbox with all the additional info on it.
	//UUserWidget* addl_info_listbox_node_userwidget = Cast<UUserWidget>(UWidgetTree::FindWidgetChild(button_node, FName(*ADDL_INFO_LISTBOX_NODE_NAME), childIdx));
	//UPanelWidget* addl_info_listbox_node = Cast<UPanelWidget>(addl_info_listbox_node_userwidget->GetRootWidget());
	//if( addl_info_listbox_node == NULL )
	//{
	//	MABBREAK();
	//	return;
	//}

	//Handle invalid data from xbox OFFLINE mode
	//#if PLATFORM_XBOXONE
	//UBorder* blue_border = Cast<UBorder>(UWidgetTree::FindWidgetChild(button_node, FName(*ADDL_INFO_BLUE_BORDER_NAME), childIdx));
	//if (blue_border)
	//{
	//	if (info_struct.additional_info.club_team.empty() &&
	//		info_struct.additional_info.competition_name.empty() &&
	//		info_struct.additional_info.international_team.empty() &&
	//		info_struct.additional_info.next_match_away_team.empty() &&
	//		info_struct.additional_info.next_match_home_team.empty() &&
	//		info_struct.additional_info.round_string.empty() &&
	//		info_struct.additional_info.year_string.empty() &&
	//		info_struct.additional_info.pro_name.empty())
	//	{
	//		addl_info_listbox_node->SetRenderOpacity(0.0f);
	//		blue_border->SetRenderOpacity(0.0f);
	//	}
	//	else
	//	{
	//		addl_info_listbox_node->SetRenderOpacity(1.0f);
	//		blue_border->SetRenderOpacity(1.0f);
	//	}
	//}
	//#endif 

	// Set the career string.
	//UTextBlock* career_name_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*COMPETITION_NAME_NODE_NAME), childIdx));
	//if( career_name_node == NULL )
	//{
	//	MABBREAK();
	//	return;
	//}
	//career_name_node->SetText(FText::FromString(FString(info_struct.additional_info.year_string.c_str()) + " " + FString(UTF8_TO_TCHAR(info_struct.additional_info.competition_name.c_str()))));
	//
	//// Check to see if the career is completed.
	//const MabString COMPETITION_COMPLETE_STRING = "[ID_COMPETITION_COMPLETE]";
	//if( info_struct.additional_info.round_string == COMPETITION_COMPLETE_STRING )
	//{
	//	// Set the round string.
	//	UTextBlock * round_string_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*ROUND_STRING_NODE_NAME), childIdx));
	//	if( round_string_node == NULL )
	//	{
	//		MABBREAK();
	//		return;
	//	}
	//	round_string_node->SetText( FText::FromString("") );
	//
	//	// Set the text to the completed string.
	//	UTextBlock * home_team_name_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*HOME_TEAM_NODE_NAME), childIdx));
	//	if( home_team_name_node == NULL )
	//	{
	//		MABBREAK();
	//		return;
	//	}
	//	home_team_name_node->SetText(FText::FromString(COMPETITION_COMPLETE_STRING.c_str()));
	//
	//
	//	UWidget * versus_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*VERSUS_TEAM_NODE_NAME), childIdx));
	//	versus_node->SetVisibility(ESlateVisibility::Hidden);
	//}
	//else
	//{
	//	// Set the round string.
	//	UTextBlock* round_string_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*ROUND_STRING_NODE_NAME), childIdx));
	//	if( round_string_node == NULL )
	//	{
	//		MABBREAK();
	//		return;
	//	}
	//	round_string_node->SetText(FText::FromString(FString(UTF8_TO_TCHAR(info_struct.additional_info.round_string.c_str())) + " -"));
	//
	//
	//	// Set the home team name
	//	UTextBlock* home_team_name_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*HOME_TEAM_NODE_NAME), childIdx));
	//	if( home_team_name_node == NULL )
	//	{
	//		MABBREAK();
	//		return;
	//	}
	//	home_team_name_node->SetText(FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.next_match_home_team)));
	//
	//	UWidget* versus_node = Cast<UWidget>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*VERSUS_TEAM_NODE_NAME), childIdx));
	//	versus_node->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	//
	//	// Set the away team name
	//	UTextBlock* const away_team_name_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*AWAY_TEAM_NODE_NAME), childIdx));
	//	if( away_team_name_node == NULL )
	//	{
	//		MABBREAK();
	//		return;
	//	}
	//	away_team_name_node->SetText(FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.next_match_away_team)));
	//}
	//
	//// Set the user team string.
	//UTextBlock* club_user_team_string_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*CLUB_TEAM_NODE_NAME), childIdx));
	//if( club_user_team_string_node == NULL )
	//{
	//	MABBREAK();
	//	return;
	//}
	//FString ClubUserTeamString = SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.club_team) + " (" + SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.pro_name) + ")";
	//club_user_team_string_node->SetText(FText::FromString(ClubUserTeamString));
	//
	//UTextBlock* international_user_team_string_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*INTERNATIONAL_TEAM_NODE_NAME), childIdx));
	//if( international_user_team_string_node == NULL )
	//{
	//	MABBREAK();
	//	return;
	//}
	//
	//if ( info_struct.additional_info.international_team != "-" )
	//{
	//	international_user_team_string_node->SetText(FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.international_team)));
	//}
	//else
	//{
	//	// No international team. Hide the "&" node and international team name.
	//	UTextBlock* ampersand_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*AMPERSAND_NODE_NAME), childIdx));
	//	if ( !ampersand_node )
	//	{
	//		MABBREAK();
	//		return;
	//	}
	//	ampersand_node->SetVisibility(ESlateVisibility::Hidden);
	//	international_user_team_string_node->SetVisibility(ESlateVisibility::Hidden);
	//}
	//
	//// Set the last modification time string.
	//UTextBlock* last_modification_time_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*LAST_MODIFICATION_TIME_NODE_NAME), childIdx));
	//if( last_modification_time_node == NULL )
	//{
	//	MABBREAK();
	//	return;
	//}
	//
	//const MabDate& last_modification_date = info_struct.additional_info.last_modification_time;
	//const MabString date_text( 0, RUCareerModeManager::DATE_FORMAT_STRING, last_modification_date.GetDayNumber(), last_modification_date.GetMonth(), last_modification_date.GetYear(), last_modification_date.GetHour(), last_modification_date.GetMinute() );
	//last_modification_time_node->SetText(FText::FromString(date_text.c_str()));

	// Now handle the button node itself.
	FString newName = wwField->GetName();
	const MabString listbox_index_str( 0, "%d", list_index );

	newName += listbox_index_str.c_str();
	//wwField->Rename(*newName);
	int slotNum = (int)career_slot_index;
	wwField->SetProperty(COMPETITION_SLOT_PROPERTY_NAME, &slotNum, UIPropertyType::PROPERTY_TYPE_INT);

	int gm = (int)GameModeType::Career_Pro;
	wwField->SetProperty(CAREER_MODE_PROPERTY_NAME, &gm, UIPropertyType::PROPERTY_TYPE_INT);

	Cast<UPanelWidget>(listbox_node)->AddChild(new_node);

	if (list_index == pro_mode_info_list.size() - 1)
	{
		Cast<UPanelWidget>(listbox_node)->GetChildAt(list_index)->SetNavigationRule(EUINavigation::Down, EUINavigationRule::Explicit, FName(*WWUIScreenMainMenu_UI::NewPro));
		Cast<UPanelWidget>(listbox_node)->GetChildAt(0)->SetNavigationRule(EUINavigation::Up, EUINavigationRule::Explicit, FName(*WWUIScreenMainMenu_UI::NewPro));
	}

	if ((list_index + 1) % 2 == 0)
	{
		new_node->SetPadding(FMargin(0.0f, 0.0f, 0.0f, 10.0f));
	}

	++list_index;

}


UWWUIProModeMainMenuListboxPopulator::UWWUIProModeMainMenuListboxPopulator()
	: game_database_mgr(nullptr)
	, population_node(NULL)
{
}

UWWUIProModeMainMenuListboxPopulator::UWWUIProModeMainMenuListboxPopulator( const RUGameDatabaseManager& game_database_mgr_ )
	: game_database_mgr( &game_database_mgr_ )
	, population_node( NULL )
{
}

UWWUIProModeMainMenuListboxPopulator::~UWWUIProModeMainMenuListboxPopulator()
{
}

void UWWUIProModeMainMenuListboxPopulator::Refresh( UWidget* /*node*/)
{
}

bool UWWUIProModeMainMenuListboxPopulator::ReversePredicate( const BaseListboxChildCallback::ProModeInfoStruct& left, const BaseListboxChildCallback::ProModeInfoStruct& right )
{
	return( left.additional_info.last_modification_time > right.additional_info.last_modification_time );
}

void UWWUIProModeMainMenuListboxPopulator::Populate( UWidget* node)
{
	Clear(node);

	if (!inPreConstruct)
	{
		if (node == NULL)
		{
			MABBREAK();
			return;
		}

		if (!game_database_mgr)
		{
			game_database_mgr = GetWorld()->GetGameInstance<URugbyGameInstance>()->GetGameDatabaseManager();
		}

		MabVector< BaseListboxChildCallback::ProModeInfoStruct> pro_mode_info_list;
		pro_mode_info_list.reserve(game_database_mgr->MAX_NUM_CAREER_PRO_SAVES);

		for (size_t i = 0u; i < game_database_mgr->MAX_NUM_CAREER_PRO_SAVES; ++i)
		{
			if (game_database_mgr->GetCareerDatabaseExists((int)i, false))
			{
				RUGameDatabaseManager::CareerProAdditionalInfo addl_info;
				MABVERIFY(game_database_mgr->GetCareerProAdditionalInfo((int)i, addl_info));
				BaseListboxChildCallback::ProModeInfoStruct this_struct(i, addl_info);
				pro_mode_info_list.push_back(this_struct);
			}
		}

		std::sort(pro_mode_info_list.begin(), pro_mode_info_list.end(), ReversePredicate);

		UWWUIProModeMainMenuListboxPopulator::MainMenuListboxChildCallback callbackObject(node, pro_mode_info_list);

		CreateNodesFromTemplate(dataList.TemplateName, pro_mode_info_list.size(), &callbackObject);

		if (ScreenRef)
		{
#ifdef UI_USING_UMG
			ScreenRef->StoreChildWidgets();
#else
			if (ScreenRef && ScreenRef->GetStateScreen())
			{
				ScreenRef->GetStateScreen()->StoreChildWidgets();
			}
#endif
		}

		population_node = node;
	}
}

void UWWUIProModeMainMenuListboxPopulator::PopulateAndRefresh(UWidget* node)
{
	if (!inPreConstruct)
	{
		Populate(node);
	}
}