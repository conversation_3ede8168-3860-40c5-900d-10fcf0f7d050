//#rc3_legacy_pch #include "Precompiled.h"

#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBallReceiver.h"

#include "Match/AI/Roles/Competitors/SetPlays/RURoleSetPlay.h"
#include "Match/AI/SetPlays/SSSetPlayManager.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/Roles/Competitors/RURoleGetTheBall.h"
#include "Match/AI/Roles/Competitors/RURolePenaltyAttack.h"
#include "Match/AI/Roles/Competitors/SSRoleFormation.h"
#include "Match/Ball/SSBall.h"
#include "Match/Camera/SSCameraManager.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUInputPhaseDecision.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/RugbyUnion/Rules/Triggers/RURuleTriggerPenalty.h"
#include "Match/SIFGameObject.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Match/SSRoleFactory.h"
#include "Match/SSSpatialHelper.h"

//#rc3_legacy_include #include <NMMabAnimationEvents.h>

#include "Character/RugbyPlayerController.h"
#include "RugbyGameInstance.h"

MABRUNTIMETYPE_IMP1( RURolePlayTheBallReceiver, SSRole );

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
RURolePlayTheBallReceiver::RURolePlayTheBallReceiver( SIFGameWorld* game )
: SSRole(game)
, state{}
, m_last_receiver_pos(FVector())
, m_last_passer_pos(FVector())
, m_set_play_manager(nullptr)
, m_set_play_started(false)
{
}

//-------------------------------------------------------------------------
// Enter
//-------------------------------------------------------------------------
void RURolePlayTheBallReceiver::Enter(ARugbyCharacter* player)
{
	SSRole::Enter(player);

	MABASSERT(player == m_pPlayer);

	auto ballHolder = m_pGame->GetGameState()->GetBallHolder();
	
	if (ballHolder == nullptr)
	{
		state = RoleState::DONE;
		Exit(true);
		return;
	}

	UpdateReceivePosition();

	if (m_pPlayer->GetAttributes()->GetTeam()->GetFormationManager())
		m_set_play_manager = m_pPlayer->GetAttributes()->GetTeam()->GetFormationManager()->GetSetplayManager();
	else
		m_set_play_manager = m_pGame->GetFirstHumanPlayer()->GetTeam()->GetFormationManager()->GetSetplayManager()

	wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);

	state = RoleState::MOVING;

	m_lock_manager.HFLockAll();
	
	// Will need this for pickup anims
	m_pPlayer->GetMabAnimationEvent().Add(this, &RURolePlayTheBallReceiver::AnimationEvent);

	// Warp the player into pos as the cutscene will hide this
	if (m_pGame->GetGameState()->GetHandoverType() == EHandoverType::OUTONFULL)
	{
		WarpToWaypoint();
	}
}

//-------------------------------------------------------------------------
// Exit
//-------------------------------------------------------------------------
void RURolePlayTheBallReceiver::Exit(bool forced)
{
	//Unregister animation event
	m_pPlayer->GetMabAnimationEvent().Remove(this, &RURolePlayTheBallReceiver::AnimationEvent);

	m_lock_manager.HFClearLocks();

	SSRole::Exit(forced);
}

///-------------------------------------------------------------------------
/// Update
///-------------------------------------------------------------------------

void RURolePlayTheBallReceiver::UpdateLogic( const MabTimeStep& game_time_step )
{
	SSRole::UpdateLogic(game_time_step);

	UpdateHumanPlayerToPositionBehind();

	UpdateCheckSetPlays();
	
	// Move the player into receiving position
	if( state == RoleState::MOVING)
	{
		float targ_dist = m_pMovement->GetTargetDistance();
		float passer_distance = (m_pMovement->GetCurrentPosition() - GetPasserPosition()).Magnitude();

		// We want to be close enough to target but not on top of the passer
		if (targ_dist <= 0.8f && passer_distance > 0.2f)
		{
			state = RoleState::WAITING_FOR_RELEASE;
		}
		else
		{
			UpdateReceivePosition();
		}

		return;
	}

	// Waiting for the player who is playing the ball to release the ball
	if (state == RoleState::WAITING_FOR_RELEASE)
	{
		if (!m_pGame->GetGameState()->GetBallHolder())
		{
			RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
			ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
			if (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				animation->PlayAnimation(PICKUP_ANIM);
				// Quick hack since we don't have animations yet, just give the ball to the receiver once it is free
				m_pGame->GetGameState()->SetBallHolder(m_pPlayer, true);
				state = RoleState::PICKING_UP;
			}
		}
		return;
	}

	if (state == RoleState::PICKING_UP)
	{
		// Waiting
		return;
	}

	if (state == RoleState::BALL_RECEIVED)
	{
		if (m_set_play_started)
		{
			// Abort set play if receiver gets ball before set play is ready
			if (!m_set_play_manager->ArePlayersInPosition())
			{
				m_set_play_manager->AbortSetplay();
				m_set_play_started = false;
			}
		}

		m_pGame->GetGameState()->SetPhase(RUGamePhase::PLAY);
		
		state = RoleState::DONE;

		return;
	}
}

///-------------------------------------------------------------------------
/// GetFitness
///-------------------------------------------------------------------------
int RURolePlayTheBallReceiver::GetFitness(const ARugbyCharacter* player, const SSRoleArea* area)
{
	// See SSEVDFormationManager PLAY_THE_BALL as the fitness is set there.
	return 0;
}

///-------------------------------------------------------------------------
/// IsInterruptable
///-------------------------------------------------------------------------
bool RURolePlayTheBallReceiver::IsInterruptable() const
{
	if (m_set_play_started)
		return true;

	return state == RoleState::DONE;
}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
void RURolePlayTheBallReceiver::AnimationEvent(float /*time*/, ERugbyAnimEvent event, size_t /*userdata*/, bool /*bIsBlendingOut = false*/)
{
	if (event == ERugbyAnimEvent::BLEND_OUT_EVENT && state == RoleState::PICKING_UP)
	{
		state = RoleState::BALL_RECEIVED;
	}
}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
void RURolePlayTheBallReceiver::UpdateReceivePosition()
{
	m_pMovement->SetThrottleAndTargetSpeedByUrgency(1, ACTOR_SPEED::AS_SPRINT, ACTOR_SPEED::AS_FASTRUN);
	SetReceiverPosition();
}

//-------------------------------------------------------------------------
// GGS JZ Updating the human player position to move them back offensivly,
// this only occurs when two controllers are on the same team.
//-------------------------------------------------------------------------
void RURolePlayTheBallReceiver::UpdateHumanPlayerToPositionBehind()
{
	auto human_players = m_pGame->GetHumanPlayers();
	for (int i = 0; i < human_players.size(); i++)
	{
		if (human_players[i]->GetRugbyCharacter() != nullptr && m_pPlayer != nullptr)
		{
			if (m_pGame->GetGameState()->GetPhase() == RUGamePhase::PLAY_THE_BALL && human_players[i]->GetTeam() == m_pGame->GetGameState()->GetAttackingTeam() && human_players[i]->GetRugbyCharacter() != m_pPlayer)
			{
				human_players[i]->GetRugbyCharacter()->GetMovement()->SetFacingActor(m_pGame->GetGameState()->GetBallHolder());
				human_players[i]->GetRugbyCharacter()->GetMovement()->SetThrottleAndTargetSpeedByUrgency(1, ACTOR_SPEED::AS_RUN, ACTOR_SPEED::AS_JOG);
				FVector currPos = m_pPlayer->GetMovement()->GetCurrentPosition();
				FVector runbackPos = FVector(currPos.x, currPos.y, currPos.z - 4.0f * human_players[i]->GetRugbyCharacter()->GetAttributes()->GetPlayDirection());
				human_players[i]->GetRugbyCharacter()->GetMovement()->SetTargetPosition(runbackPos);
			}
		}
	}
}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
void RURolePlayTheBallReceiver::WarpToWaypoint()
{
	m_pPlayer->GetMovement()->SetFacingFlags(AFFLAG_FACEPLAYDIR);
	SetReceiverPosition();
	SSRole::WarpToWaypoint();
}

void RURolePlayTheBallReceiver::SetReceiverPosition()
{
	m_pMovement->SetTargetPosition( GetReceiverPosition(), true);
}

FVector RURolePlayTheBallReceiver::GetReceiverPosition()
{
	auto ballHolder = m_pGame->GetGameState()->GetBallHolder();
	if (ballHolder)
	{
		FVector offset = FVector(0, 0, 1.0f * -ballHolder->GetAttributes()->GetPlayDirection());
		m_last_receiver_pos = GetPasserPosition() + offset;
	}
	return m_last_receiver_pos;
}

FVector RURolePlayTheBallReceiver::GetPasserPosition()
{
	auto ballHolder = m_pGame->GetGameState()->GetBallHolder();
	if (ballHolder)
	{
		m_last_passer_pos = ballHolder->GetMovement()->GetCurrentPosition();
	}
	return m_last_passer_pos;
}

void RURolePlayTheBallReceiver::UpdateCheckSetPlays()
{
	if (m_pGame->GetGameState()->GetAttackingTeam()->GetNumHumanPlayers() == 0)
		return;

	SSHumanPlayer* human = m_pGame->GetHumanPlayer();

	if (human != NULL)
	{		//If the modifier is held down, lock all other options
		if (human->IsOn(ERugbyGameAction::PLAY_THE_BALL_SET_PLAYS) && m_set_play_manager && m_set_play_manager->AreSetplaysEnabled() && m_pGame->GetGameState()->GetBallHolder())
		{
			int setPlayIndex = -1;

			//If the modifier key (LT) is held down, update the contextual hud
			if (m_pGame && m_pGame->GetEvents())
			{
				m_pGame->GetEvents()->context_hud_modified(true);
			}

			//Add set play calls here
			if (human->GetPassExtendDisplaySide() == 0 && human->IsReleased(ERugbyGameAction::DECISION_TOP))
			{
				setPlayIndex = 0;
			}
			else if (human->GetPassExtendDisplaySide() == 0 && human->IsReleased(ERugbyGameAction::DECISION_LEFT))
			{
				setPlayIndex = 1;
			}
			else if (human->GetPassExtendDisplaySide() == 0 && human->IsReleased(ERugbyGameAction::DECISION_RIGHT))
			{
				setPlayIndex = 2;
			}
			else if (human->GetPassExtendDisplaySide() == 0 && human->IsReleased(ERugbyGameAction::DECISION_BOTTOM))
			{
				setPlayIndex = 3;
			}

			//Start setplay if an index is selected
			if (setPlayIndex != -1)
			{
				FSerialiseFormation* setplay = m_set_play_manager->GetSetplayByIndex(4);
				if (setplay)
				{
					FString setplayName = setplay->name;
					human->GetTeam()->GetFormationManager()->GetSetplayManager()->StartSetplayByName(setplayName, human);

					//Turn off modifier as it could be hiding contextual hud
					if (m_pGame && m_pGame->GetEvents())
					{
						m_pGame->GetEvents()->context_hud_modified(false);
						m_pGame->GetEvents()->set_play_started(setPlayIndex, setplayName, m_pPlayer);
					}

					m_set_play_started = true;
				}
				return;
			}
		}
		else
		{
			//Modifier not held down
			if (m_pGame && m_pGame->GetEvents())
			{
				m_pGame->GetEvents()->context_hud_modified(false);
				return;
			}
		}
	}
}