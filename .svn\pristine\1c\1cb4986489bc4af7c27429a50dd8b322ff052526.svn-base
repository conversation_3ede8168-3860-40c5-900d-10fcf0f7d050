/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/Input/SSInputManager.h"

#include "Match/AI/Actions/RUActionChargeAttacker.h"
#include "Match/AI/Actions/RUActionFend.h"
#include "Match/AI/Actions/RUActionPass.h"
#include "Match/AI/Actions/RUActionPassAnticipation.h"
#include "Match/AI/Actions/RUActionTacklee.h"
#include "Match/AI/Actions/RUActionTackler.h"
#include "Match/AI/Roles/Competitors/RURoleGetTheBall.h"
#include "Match/AI/Roles/Competitors/RURoleStandardBallHolder.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuckScrumHalf.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleScrumHalfBack.h"
#include "Match/Ball/SSBall.h"
#include "Match/Camera/SSCameraManager.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/RUPlayerState.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Match/Debug/RUGameDebugSettings.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/Input/RUPassExtendInterface.h"
#include "Match/RugbyUnion/Enums/RUPassInputCheckEnum.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseExtraTimeToss.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseLineOut.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseMaul.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseRuck.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseScrum.h"
#include "Match/RugbyUnion/RUGameGetToBall.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUInputKickInterface.h"
#include "Match/RugbyUnion/RUInputPhaseDecision.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/RUTeamStrategy.h"
#include "Match/SIFGameObject.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Match/SSRole.h"
#include "Match/SSSpatialHelper.h"
#include "Utility/RURandomNumberGenerator.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleMaulHalfback.h"
#include "Utility/consoleVars.h"

#include "Match/Debug/SIFDebug.h"
//#rc3_legacy_include #include <MabControlActionManager.h>

#ifdef ENABLE_PRO_MODE
#include "Match/HUD/RU3DHUDManager.h"
//#rc3_legacy_include #include "RUContextualHelper.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuck.h"
#include "Match/SSRoleFactory.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/Components/RUPlayerAnimation.h"
#endif

#if PLATFORM_WII
#include "SIFWiiControllerHelper.h"
#include <MabGCController.h>
#endif

#include "Character/RugbyPlayerController.h"
#include "Character/RugbyCharacter.h"
#include "RugbyGameInstance.h"


//const float CSTICK_DEADZONE = 0.5f;
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
#include "Utility/Helpers/SIFGameHelpers.h"
#include "RugbyEnums.h"
#include "Utility/consoleVars.h"
#endif

#include "Utility/Helpers/SIFGameHelpers.h"
#include "Match/HUD/RUContextualHelper.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleSetplay.h"
#include <Match/RugbyUnion/PhaseHandlers/RUGamePhasePlayTheBall.h>

SSInputManager::SSInputManager( MabControlActionManager* ccontrol_action_manager, SIFGameWorld* ggame, RU3DHUDManager* hud_manager )
: control_action_manager( ccontrol_action_manager ),
  game( ggame ),
  kick_interface( NULL ),
  decision_interface( NULL ),
  /*user_strategy_handler( NULL ),*/
  controller_helper( NULL ),
  pass_extend_interface( NULL ),
  begin_update_time( 0.0f )
{
	//MABASSERT( control_action_manager != NULL );//#nirupam commented this for now.
	MABASSERT( game != NULL );
	//MABUNUSED(hud_manager);

	unsigned int heap_id		= MabMemGetDefaultObjectHeap(this);
	kick_interface				= MabMemNew(heap_id) RUInputKickInterface		( game, hud_manager );
	decision_interface			= MabMemNew(heap_id) RUInputPhaseDecision		( game->GetGameContext() );
	pass_extend_interface		= MabMemNew(heap_id) RUPassExtendInterface		( game );

#if PLATFORM_WII
	controller_helper = SIFApplication::GetApplication()->GetWiiControllerHelper();
	for( int i = 0; i < 4; ++i )
	{
		dummy_passing[i] = false;
		sidestepping[i] = false;
		fending[i] = false;
		gesture_timers[i] = 0.0f;
	}
#else
	controller_helper = NULL;
#endif

	//user_strategy_handler = MabMemNew( SIFHEAP_PERMANENT_GAME ) RL3UserStrategyHandler( ggame );
}

SSInputManager::~SSInputManager()
{
	MabMemDelete( kick_interface );
	//MabMemDelete( user_strategy_handler );
	MabMemDelete( decision_interface );
	MabMemDelete( pass_extend_interface );
}

void SSInputManager::Reset()
{
	// Clear any deferred consequences so that when we reset we don't get random rules consequences occurring just after the reset.
	// Was previously causing some weird issues with changing tutorial demos at specific timings.
	if (decision_interface)
	{
		decision_interface->ClearDeferredConsequenceTimer();
	}
}

void SSInputManager::GameReset()
{
	begin_update_time = 0.0f;
}

void SSInputManager::UpdateSimulation( const MabTimeStep& delta_time_step )
{
	if (game == nullptr)
		return;

//	USIFGameFlowNode *game_flow = static_cast<USIFGameFlowNode*> (SIFApplication::GetApplication()->GetFlowControlManager()->FetchNode(SIF_GAME_FLOWNAME));
//	MABASSERT(game_flow != NULL);  // should always be the game flownode anyway ;)

#if PLATFORM_WII
	for( int i = 0; i < 4; ++i )
	{
		gesture_timers[i] -= delta_time_step.delta_time;
		gesture_timers[i] = MabMath::Max( gesture_timers[i], 0.0f );
	}
#endif

//	if ( game_flow->IsGamePaused() )
//		return;
#ifdef ENABLE_GAME_DEBUG_MENU
	const size_t INPUT_DEBUG_BUFFER_SIZE = 1024;
	char buffer[INPUT_DEBUG_BUFFER_SIZE];
	char* buf_ptr = buffer;
	buf_ptr[0] = '\0';
#endif

	for ( SSHumanPlayer* human : game->GetHumanPlayers() )
	{
		MABASSERT(human != nullptr );

		// Input must always be updated for any playing human
		if (human->IsPlaying())
		{
			// Force an update of the input state here, this is required at low frame rates,
			// as we can have two simsteps happen without a render occuring.
			human->UpdateInput( delta_time_step.delta_time.ToSeconds() );
			human->UpdateSimulation( delta_time_step );

			#ifdef ENABLE_GAME_DEBUG_MENU
			if ( SIFDebug::GetGameDebugSettings()->GetInputActionDebugEnabled() )  
			{
				for( int i = 0; i < (int)ERugbyGameAction::RU_GAME_ACTIONS_MAX; i++ )
				{
					bool pressed  = human->IsPressed( (ERugbyGameAction) i );
					bool released = human->IsReleased( (ERugbyGameAction) i );
					bool on       = human->IsOn( (ERugbyGameAction) i );

					if ( pressed || released || on ) 
					{
						const UEnum* EnumPtr = FindObject<UEnum>(ANY_PACKAGE, TEXT("ERugbyGameAction"), true);
						FString enumString = FString("Invalid");
						if (EnumPtr)
						{
							enumString = EnumPtr->GetEnumName((int32)i);
						}


						int written = snprintf( buf_ptr, sizeof( buffer ) - (buf_ptr - buffer), "%d %-32ls %d %d %d %0.3f\n", human->GetControllerIndex(), *enumString, pressed, released, on, human->GetOnDuration( (ERugbyGameAction) i ) );
						if ( written > 0 ) buf_ptr += written;
					}
				}
			}
			#endif
		}

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
		if (!UpdateDebug(human))
#endif
		{
			if (human->GetTeam() != nullptr)
			{
				//if (!game->GetCutSceneManager()->IsSimulationDisabled())
				if (!game->GetCutSceneManager()->IsCinematicRunning())
				{
					bool movementControlsConsumed = UpdateSelection(human);

					if (human->GetRugbyCharacter() && human->GetRugbyCharacter()->GetRole())
					{
						if (!movementControlsConsumed)
						{
							// If the stick/dpad hasn't been used by player selection, update movement
							UpdateMovement(human);
						}

						UpdateAggression(human);

						UpdateSprinting(human);
						UpdateTackling(human);

						if (human->GetTeam() == game->GetGameState()->GetAttackingTeam())
						{
							// If the try score logic has started, don't process any other keys (lets the user mash any key to score a try)
							UpdateTryScore(human);

							UpdateKicking(human);
							UpdateSidestep(human);
							UpdateFending(human);

							UpdatePassing(human);

							UpdateCamera(human);


							UpdateProWithoutBall(human);
						}
						else
						{
							UpdateDefenceLine(human);
						}

						UpdateProCameraLockBall(human);

						RUGamePhase currPhase = game->GetGameState()->GetPhase();

						// Update rucking behaviour
						if (currPhase == RUGamePhase::RUCK)
						{
							UpdateRuckControls(human);
						}

						// Update mauling behaviour
						if (currPhase == RUGamePhase::MAUL)
						{
							UpdateMaulControls(human);
						}

						if (currPhase == RUGamePhase::EXTRA_TIME_TOSS)
						{
							UpdateExtraTimeCoinTossControls(human);
						}

						if (game->GetGameState()->GetPhase() == RUGamePhase::PLAY_THE_BALL)
						{
							UpdatePlayTheBallPreloadPass(human);
						}

						// WJS RLC Not Needed??
						// Update scrumming behaviour
						// if (currPhase == RUGamePhase::SCRUM)
						// {
						// 	UpdateScrumControls(human);
						// }

						if ((currPhase == RUGamePhase::DECISION || currPhase == RUGamePhase::DECISION_PENALTY || currPhase == RUGamePhase::ELECT_QUICK_TAP)
							&& decision_interface->GetDecisionMaker() == human && !game->GetCutSceneManager()->IsCinematicRunning())
						{
							UpdateDecisionControls(human);
						}

						if (game->GetStrategyHelper()->IsBallReallyFree())
						{
							UpdateLooseBall(human);
						}

						UpdateStrategyControls(human);
					}
				}

				human->UpdatePassExtendDisplay();
			}
		}

		// TYRONE : Only check if human is playing for skipping cutscenes as in some instances
		// I suspect roles or humans may not be assigned which has cause frustration
		// According to rob disable simulation is the best thing to check for replays or cutscenes being active
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
		if (CVarCutsceneSkipReason.GetValueOnAnyThread() != 0 || FParse::Param(FCommandLine::Get(), TEXT("CutsceneSkipReason")))
		{
			if (human->GetTeam() == nullptr)
			{
				UE_LOG(LogTemp, Log, TEXT("Cutscene Skip: NO - This human is not playing. RC3 Legacy?"));
			}
			if (!game->GetCutSceneManager()->SkipEnabled())
			{
				UE_LOG(LogTemp, Log, TEXT("Cutscene Skip: NO - GetCutSceneManager()->SkipEnabled() == false (haka is playing?)"));
			}
			if (!game->GetCutSceneManager()->IsCinematicRunning())
			{
				UE_LOG(LogTemp, Log, TEXT("Cutscene Skip: NO - GetCutSceneManager()->IsCinematicRunning() == false"));
			}
		}
#endif
		wwNETWORK_TRACE_JG_DISABLED("SSInputManager::UpdateSimulation team: %d, se: %d, cr: %d", human->GetTeam() != nullptr, game->GetCutSceneManager()->SkipEnabled(), game->GetCutSceneManager()->IsCinematicRunning());
		if(human->GetTeam() != nullptr && game->GetCutSceneManager()->SkipEnabled() && game->GetCutSceneManager()->IsCinematicRunning() && game->GetWorldId() == WORLD_ID::GAME)
		{
			UpdateSkipCutscene(human);
		}
	}


	#ifdef ENABLE_GAME_DEBUG_MENU
	if ( SIFDebug::GetGameDebugSettings()->GetInputActionDebugEnabled() )
	{
		#define HANDLE 55557777
		SIF_DEBUG_DRAW( SetText( HANDLE, 20, 300, buffer ) );
	}
	#endif


	// Kick Interface needs delta without the time scale adjustments so the input can move in slowmo
	float kick_interface_delta = 0.0f;

	if ( !MabMath::Feq(game->GetSimTime()->GetTimeScale(), 0.0f) )
		kick_interface_delta = delta_time_step.delta_time.ToSeconds() / game->GetSimTime()->GetTimeScale();

	kick_interface->UpdateSimulation( kick_interface_delta );
	decision_interface->UpdateSimulation( delta_time_step );
	pass_extend_interface->UpdateSimulation( delta_time_step.delta_time.ToSeconds() );
//	user_strategy_handler->UpdateSimulation( delta_time );
}

/*void SSInputManager::SetUpdateDelay( float time )
{
	MabRealTimeSource* real_time = SIFApplication::GetApplication()->GetAppTime()->GetRealTimeSource();
	begin_update_time = real_time->GetAbsoluteTime().ToSeconds() + time;
}*/


bool SSInputManager::UpdateTryScore( SSHumanPlayer* human_player )
{
	ARugbyCharacter* player = human_player->GetRugbyCharacter();
	RUGameState *game_state = game->GetGameState();
	SSSpatialHelper *spatial_helper = game->GetSpatialHelper();

	bool try_action_on = false;

	MABASSERT(player);
	if(!player)
	{
		MABLOGDEBUG("SSInputManager::UpdateTryScore NO PLAYER");
		return(false);
	}

	if ( game_state->GetBallHolder() == player || player->GetActionManager()->CanPrewindAction( ACTION_TRY ) )
	{
		ERugbyPlayDirection dir = player->GetAttributes()->GetPlayDirection();
		ATTACK_PLAYZONE zone = spatial_helper->GetAttackPlayZone( dir, player->GetMovement()->GetCurrentPosition().z );

		static const float MIN_TIME_SINCE_PHASE_CHANGE_TO_APPLY = 2.5f;
		const float TRY_TRIGGER_DISTANCE = 3.0f;
		bool close_to_attempt_try   = spatial_helper->GetAttackPlayZone( dir, player->GetMovement()->GetCurrentPosition().z + (TRY_TRIGGER_DISTANCE * dir) ) == ATTACK_TRY_ZONE;
		bool close_to_try_zone	= zone == ATTACK_TRY_ZONE || (close_to_attempt_try && game_state->GetTimeSincePhaseChange() > MIN_TIME_SINCE_PHASE_CHANGE_TO_APPLY);

		bool can_force_ball = false;
		RURoleStandardBallHolder* ball_holder_role = MabCast<RURoleStandardBallHolder>( human_player->GetRugbyCharacter()->GetRole() );
		if ( ball_holder_role )
		{
			can_force_ball = ball_holder_role->CanForceBall();
		}

		if ( !close_to_try_zone && can_force_ball )
		{
			// Test for grounding the ball in own goal area.
			zone = spatial_helper->GetAttackPlayZone( dir, player->GetMovement()->GetCurrentPosition().z );
			close_to_attempt_try = spatial_helper->GetAttackPlayZone( dir, player->GetMovement()->GetCurrentPosition().z + (TRY_TRIGGER_DISTANCE * dir) ) == ATTACK_INGOAL_ZONE;
			if ( zone == ATTACK_INGOAL_ZONE || (close_to_attempt_try && game_state->GetTimeSincePhaseChange() > MIN_TIME_SINCE_PHASE_CHANGE_TO_APPLY) )
			{
				try_action_on = human_player->IsPressed( ERugbyGameAction::FORCE_BALL );
			}
		}

		if ( close_to_try_zone )
		{
			try_action_on = human_player->IsPressed( ERugbyGameAction::DECISION_TOP )
					|| human_player->IsPressed( ERugbyGameAction::DECISION_BOTTOM )
					|| human_player->IsPressed( ERugbyGameAction::DECISION_LEFT )
					|| human_player->IsPressed( ERugbyGameAction::DECISION_RIGHT );
		}
	}

	if ( try_action_on
		&& (player->GetActionManager()->CanPrewindAction( ACTION_TRY ) || player->GetActionManager()->CanUseAction( ACTION_TRY ))
		&& game_state->IsGameInStandardPlay()
		&& !player->GetActionManager()->HFIsLocked( HF_TRY ) )
	{
		player->GetRole()->StartActionTry();
		return true;
	}

	return false;
}

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
bool SSInputManager::UpdateDebug(SSHumanPlayer* human_player)
{
	MABUNUSED(human_player);
	if (human_player->IsPressed(ERugbyGameAction::DEBUG_RELOAD_CAMERAS))
	{
		SIFApplication::GetApplication()->GetActiveGameWorld()->GetCameraManager()->ReloadCameras();
		return true;
	}
#ifdef ENABLE_DEBUG_KEYS
	/*
	if (human_player->IsPressed(ERugbyGameAction::DEBUG_RESTART_MATCH))
	{
		SIFGameHelpers::GARestartGame();
		return true;
	}
	else if (human_player->IsPressed(ERugbyGameAction::DEBUG_QUIT_MATCH))
	{
		SIFGameHelpers::GAExitGame();
		return true;
	}
	else if (human_player->IsPressed(ERugbyGameAction::DEBUG_FORCE_LINEOUT))
	{
		game->GetGameState()->ForceRandomLineout();
		return true;
	}
	else if (human_player->IsPressed(ERugbyGameAction::DEBUG_FORCE_SCRUM))
	{
		game->GetGameState()->ForceRandomScrum();
		return true;
	}
	*/

#endif

	return false;
}
#endif

void SSInputManager::UpdateAggression(SSHumanPlayer* human_player)
{
	MABUNUSED(human_player);
	//ARugbyCharacter* player = human_player->GetPlayer();
	//if (player
	//	&& !player->HFIsLocked(HF_AGGRESSION))
	//{
	//	if (human_player->IsPressed(RL3_SPRINT))
	//	{
	//		player->GetAttributes()->AddAggression(AGGRESSION_ADD_PER_TAP);
	//	}
	//	else if (human_player->IsOn(RL3_SPRINT))
	//	{
	//		player->GetAttributes()->MaintainAgression();
	//	}
	//}
}

///-------------------------------------------------------------------------------------
/// Pass detection meta data
///-------------------------------------------------------------------------------------

const int PASS_DIR_RANDOM = -2;
typedef struct {
	ERugbyGameAction game_action, game_action2;	// Actions associated with pass
	PASS_INPUT_CHECK_TYPE check_type;					// The type of precondition check to perform
	int pass_dir;								// The resulting pass direction
	int pass_skip;								// The resulting number of players to skip
	PASS_TYPE pass_type;						// The resulting type of pass
} PassActionEntry;

/// Control structures for pass activation
const PassActionEntry entries[] = {
	/// Auto determine passes disabled - now only single pass

	//{ RU_PASS_LEFT_AUTO_DETERMINE,  ERugbyGameActionS_MAX, CHECK_RELEASED_NO_EXTEND,  1, RUActionPass::RUPASS_SKIP_AUTOSELECT, PT_STANDARD },
	//{ RU_PASS_RIGHT_AUTO_DETERMINE, ERugbyGameActionS_MAX, CHECK_RELEASED_NO_EXTEND, -1, RUActionPass::RUPASS_SKIP_AUTOSELECT, PT_STANDARD },
	{ ERugbyGameAction::PASS_LEFT_SINGLE,			ERugbyGameAction::RU_GAME_ACTIONS_MAX, CHECK_RELEASED_NO_EXTEND,  1, 0,									  PT_STANDARD },
	{ ERugbyGameAction::PASS_RIGHT_SINGLE,			ERugbyGameAction::RU_GAME_ACTIONS_MAX, CHECK_RELEASED_NO_EXTEND, -1, 0,									  PT_STANDARD },
	/// Dummy on release disabled due to usability feedback
	//{ RU_PASS_LEFT_SINGLE,			ERugbyGameActionS_MAX, CHECK_RELEASED_NO_SKIP,   1, RUActionPass::RUPASS_SKIP_AUTOSELECT, PT_DUMMY },
	//{ RU_PASS_RIGHT_SINGLE,			ERugbyGameActionS_MAX, CHECK_RELEASED_NO_SKIP,  -1, RUActionPass::RUPASS_SKIP_AUTOSELECT, PT_DUMMY },
	{ ERugbyGameAction::PASS_AUTO_DUMMY,			ERugbyGameAction::RU_GAME_ACTIONS_MAX, CHECK_GESTURE,			  PASS_DIR_RANDOM, RUActionPass::RUPASS_SKIP_AUTOSELECT, PT_DUMMY },
	{ ERugbyGameAction::PASS_AUTO_DUMMY,			ERugbyGameAction::RU_GAME_ACTIONS_MAX, CHECK_PRESSED,			  PASS_DIR_RANDOM, RUActionPass::RUPASS_SKIP_AUTOSELECT, PT_DUMMY },

	/// Pass skip - by holding button down
	{ ERugbyGameAction::PASS_LEFT_SINGLE,			ERugbyGameAction::RU_GAME_ACTIONS_MAX, CHECK_EXTEND_1,	  1, 1,								PT_STANDARD },
	{ ERugbyGameAction::PASS_LEFT_SINGLE,			ERugbyGameAction::RU_GAME_ACTIONS_MAX, CHECK_EXTEND_2,	  1, 2,								PT_STANDARD },
	{ ERugbyGameAction::PASS_LEFT_SINGLE,			ERugbyGameAction::RU_GAME_ACTIONS_MAX, CHECK_EXTEND_3,	  1, 3,								PT_STANDARD },
	{ ERugbyGameAction::PASS_RIGHT_SINGLE,			ERugbyGameAction::RU_GAME_ACTIONS_MAX, CHECK_EXTEND_1,	 -1, 1,								PT_STANDARD },
	{ ERugbyGameAction::PASS_RIGHT_SINGLE,			ERugbyGameAction::RU_GAME_ACTIONS_MAX, CHECK_EXTEND_2,	 -1, 2,								PT_STANDARD },
	{ ERugbyGameAction::PASS_RIGHT_SINGLE,			ERugbyGameAction::RU_GAME_ACTIONS_MAX, CHECK_EXTEND_3,	 -1, 3,								PT_STANDARD },

	/// Pass skip - extend with face buttons
	{ ERugbyGameAction::PASS_EXTEND_OPT1,			ERugbyGameAction::PASS_LEFT_SINGLE, CHECK_EXTEND,	  1, 1,									  PT_STANDARD },
	{ ERugbyGameAction::PASS_EXTEND_OPT2,			ERugbyGameAction::PASS_LEFT_SINGLE, CHECK_EXTEND,	  1, 2,									  PT_STANDARD },
	{ ERugbyGameAction::PASS_EXTEND_OPT3,			ERugbyGameAction::PASS_LEFT_SINGLE, CHECK_EXTEND,	  1, 3,									  PT_STANDARD },
	{ ERugbyGameAction::PASS_EXTEND_OPT1,			ERugbyGameAction::PASS_RIGHT_SINGLE,CHECK_EXTEND,	 -1, 1,									  PT_STANDARD },
	{ ERugbyGameAction::PASS_EXTEND_OPT2,			ERugbyGameAction::PASS_RIGHT_SINGLE,CHECK_EXTEND,	 -1, 2,									  PT_STANDARD },
	{ ERugbyGameAction::PASS_EXTEND_OPT3,			ERugbyGameAction::PASS_RIGHT_SINGLE,CHECK_EXTEND,	 -1, 3,									  PT_STANDARD },

	/// Pass play maker
	{ ERugbyGameAction::PASS_LEFT_SINGLE,			ERugbyGameAction::PASS_RIGHT_SINGLE,CHECK_BOTH_ON,	 0, RUActionPass::RUPASS_SKIP_PLAYMAKER, PT_STANDARD }	// Pass to playmaker
};
const size_t N_PASS_ENTRIES = sizeof( entries ) / sizeof( PassActionEntry );

void SSInputManager::UpdatePassing( SSHumanPlayer* human_player )
{
	// Need to deal with prewind also
	ARugbyCharacter* player = human_player->GetRugbyCharacter();

	bool can_pass_since_freeball = WasHumanExpectingBall( human_player );

	if ( player != NULL
		&& can_pass_since_freeball
		&& human_player->CanPassNow()
		&& player->GetRole()
		&& !player->GetActionManager()->HFIsLocked(HF_PASS)
		&& game->GetGameState()->IsGameInStandardPlay()
		&& human_player->GetTeam() == game->GetGameState()->GetAttackingTeam()
		&& ( player->GetActionManager()->CanPrewindAction( ACTION_PASS ) || player->GetActionManager()->CanUseAction( ACTION_PASS ) ) )
	{
		int pass_extend_display_side = human_player->GetPassExtendDisplaySide();

		// Check to see if we need to bring up the pass extend hud
		if ( pass_extend_display_side != 0 && !player->GetActionManager()->IsActionRunning( ACTION_PASS ) &&
			(player == game->GetGameState()->GetBallHolder() || player->GetRole()->RTTGetType() == RURoleRuckScrumHalf::RTTGetStaticType() || player->GetRole()->RTTGetType() == RURoleScrumHalfBack::RTTGetStaticType())
			 && !pass_extend_interface->IsActive() )
		{
			pass_extend_interface->StartExtend( human_player );

			// Also start pass anticipation actions for all nominated receivers
			const auto& receivers = human_player->GetPassExtendReceivers(human_player->GetPassExtendDisplaySide(), true);

			for (int i = 0; i < receivers.Num(); ++i)
			{
				ARugbyCharacter* receiver = receivers[i];
				verify(receiver != nullptr);

				if (receiver->GetActionManager()->CanUseAction( ACTION_PASS_ANTICIPATION ))
				{
					receiver->GetRole()->StartActionPassAnticipation(human_player->GetRugbyCharacter(), pass_extend_interface, float(human_player->GetPassExtendDisplaySide()), i, receivers.Num(), RUActionPassAnticipation::AM_LOCKED_TO_PASSER);
				}
			}
		}

		/// Loop over all registered pass actions and check preconditions for activation
		size_t i;
		bool activated = false;
		for( i = 0; i < N_PASS_ENTRIES; i++ ) {
			const PassActionEntry &entry = entries[i];

			switch ( entry.check_type ) {
				case CHECK_RELEASED: activated = human_player->IsReleased( entry.game_action ); break;
				case CHECK_RELEASED_NO_EXTEND: activated = human_player->IsReleased( entry.game_action ) &&
															!pass_extend_interface->IsActive() &&
															human_player->GetOnDuration( entry.game_action ) <= MIN_PASS_EXTEND_DEPRESS_TIME; break;
				case CHECK_RELEASED_NO_SKIP: activated = human_player->IsReleased( entry.game_action ) && pass_extend_interface->IsActive(); break;
				case CHECK_PRESSED:  activated = human_player->IsPressed ( entry.game_action ); break;
				case CHECK_EXTEND:
				case CHECK_BOTH_ON:
					activated = (human_player->IsPressed( entry.game_action )  && human_player->IsOn( entry.game_action2 )) ||
								(human_player->IsPressed( entry.game_action2 ) && human_player->IsOn( entry.game_action ));
				; break;
				case CHECK_EXTEND_1:
				case CHECK_EXTEND_2:
				case CHECK_EXTEND_3:
					{
						float skip_time_multiplier = (float) (entry.check_type - CHECK_EXTEND_1) + 1.0f;
						float skip_start_time = skip_time_multiplier * MIN_PASS_EXTEND_DEPRESS_TIME;
						float skip_end_time = entry.check_type < CHECK_EXTEND_3 ? skip_start_time + MIN_PASS_EXTEND_DEPRESS_TIME : 1e10f;
						float on_time = human_player->GetOnDuration( entry.game_action );
						activated = human_player->IsReleased( entry.game_action ) &&
							pass_extend_interface->IsActive() &&
							MabMath::InRangeInclusive( on_time, skip_start_time, skip_end_time );
					}
					break;
				case CHECK_GESTURE:
					activated = (human_player->IsGestureActive( entry.game_action ));
					break;
				default:	MABBREAKMSG( "Invalid check type" ); break;
			}

			if ( activated )
				break;
		}

		/// If a pass has been activated then trigger it
		if ( activated )
		{
			const PassActionEntry &entry = entries[i];

			/// TYRONE : Prevent dummy passes from triggering too frequently - in response to dummy pass on-line exploit raised by HES
			if ( entry.pass_type == PT_DUMMY )
			{
				const static float MINIMUM_TIME_BETWEEN_DUMMIES = 0.7f;
				bool minimum_dummy_time_passed = human_player->GetTimeSinceLastDummyPass().ToSeconds() > MINIMUM_TIME_BETWEEN_DUMMIES;
				if ( !minimum_dummy_time_passed )
					return;
			}

			/// Get the pass direction and number of players to skip
			int entry_pass_dir = entry.pass_dir;
			int pass_dir = entry_pass_dir * player->GetAttributes()->GetPlayDirection();
			int pass_skip = entry.pass_skip;

			// if kicking activated stop the kick
			// if kicking then only fire a normal pass as there is a controller conflict to bring up the pass extend
			// as a face button is being held down for the kick.
			bool was_kicking = false;

			if ( kick_interface->IsKickActive() && kick_interface->GetKicker() == player )
			{
				kick_interface->StopKick();
				was_kicking = true;
				pass_skip = -1;
			}

			#if defined ENABLE_OSD
			RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
			MabString activation_string( 256, "%-25s %-25s %-20s %2d %2d %-10s",
				ERugbyGameActionS_STRINGS[entry.game_action] + 8,
				ERugbyGameActionS_STRINGS[entry.game_action2] + 8,
				PASS_INPUT_CHECK_TYPE_STRINGS[entry.check_type] + 6,
				entry.pass_dir,
				entry.pass_skip,
				PASS_TYPE_STRINGS[entry.pass_type]
			);
			settings->PushDebugString( game, RUGameDebugSettings::DP_PASS, activation_string.c_str() );
			#endif

			// Normal pass
			if ( (entry.check_type != CHECK_EXTEND &&
				  entry.check_type != CHECK_EXTEND_1 &&
				  entry.check_type != CHECK_EXTEND_2 &&
				  entry.check_type != CHECK_EXTEND_3 ) || was_kicking )
			{
				player->GetRole()->StartActionPass( pass_dir, pass_skip, entry.pass_type );
				human_player->SetKickInputIgnored();
				if ( entry.pass_type == PT_DUMMY )
					human_player->ResetDummyPassTimer();
			}
			else
			{
				// Extend pass - pass to selected player
				bool force_recalc = player == game->GetGameState()->GetBallHolder() && (!pass_extend_interface->IsActive() || (entry.check_type == CHECK_EXTEND && pass_extend_display_side != pass_dir));

				const auto& receivers = human_player->GetPassExtendReceivers( pass_dir, force_recalc );
				MABASSERT( entry.pass_skip >= 0 );
				ARugbyCharacter* receiver = entry.pass_skip < receivers.Num() ? receivers[entry.pass_skip] : nullptr;

				// For holding down the extend button we want to stop at the last non NULL receiver so check for this case
				if ( receiver == nullptr && (entry.check_type >= CHECK_EXTEND_1 && entry.check_type <= CHECK_EXTEND_3 ) )
				{
					for ( int skip = entry.pass_skip - 1; skip >= 0 && receiver == nullptr; skip-- )
					{
						if ( skip < receivers.Num() )
						{
							receiver = receivers[ skip ];
							break;
						}
					}
				}

				if ( receiver != nullptr )
				{
					player->GetRole()->StartActionPass( receiver, entry.pass_type );
					human_player->SetKickInputIgnored();
					static int count = 0;
					MABUNUSED(count);		// For ps3 - ENABLE_NO_LOGS
					MABLOGDEBUG( MabString( 64, "Starting pass %d to %d", count++, receiver->GetAttributes()->GetIndex() ).c_str() );
				}
				else
				{
					// They still requested one to skip some players so do it
					player->GetRole()->StartActionPass( pass_dir, entry.pass_skip, entry.pass_type );
					human_player->SetKickInputIgnored();
				}
			}

			human_player->ResetPassExtend();
			human_player->ResetPassTimer( MIN_PASS_EXTEND_DEPRESS_TIME );
			human_player->SetInputIgnored( ERugbyGameAction::PASS_LEFT_SINGLE );
			human_player->SetInputIgnored( ERugbyGameAction::PASS_RIGHT_SINGLE );
		}
	}
}

void SSInputManager::UpdateProWithoutBall( SSHumanPlayer* human_player )
{
	MABUNUSED(human_player);

#ifdef ENABLE_PRO_MODE
	if(game->GetGameSettings().game_settings.GetIsAProMode())
	{
		ARugbyCharacter* player = human_player->GetRugbyCharacter();
		RUGameState* game_state = game->GetGameState();
		ARugbyCharacter* bh_player = game_state->GetBallHolder();

		if(!game->GetStrategyHelper()->CanProPlayerRequestAction())
			return;

		RURoleStandardBallHolder* standard_bh = NULL;
		SSRole* bh_role = bh_player->GetRole();
		MabTypeID bhRoleType = bh_role->RTTGetType();
		// Double check to see if the ball holder is the standard ball holder role
		if( bhRoleType == RURoleStandardBallHolder::RTTGetStaticType() )
		{
			standard_bh = game_state->GetBallHolder()->GetRole<RURoleStandardBallHolder>();
		}

		//RU3DHUDManager* hud_manager = game->Get3DHudManager();
		//RUContextualHelper* helper = hud_manager->GetContextualHelper();

		/*if(player == NULL)
		{
			//helper->SetState( CS_NONE, NULL, true );
			//helper->SetState( CS_NONE, NULL, false );
			return;
		}

		// Early out if the human player is not the pro player, this should never happen however.
		if(!SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(player))
		{
			//helper->SetState( CS_NONE, NULL, true );
			//helper->SetState( CS_NONE, NULL, false );
			return;
		}

		// Early out if the pro player is benched.
		if(!SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayerOnField())
		{
			//helper->SetState( CS_NONE, NULL, true );
			//helper->SetState( CS_NONE, NULL, false );
			return;
		}

		if(bh_player == NULL)
		{
			//helper->SetState( CS_NONE, NULL, true );
			//helper->SetState( CS_NONE, NULL, false );
			return;
		}

		// Don't run the AI when we are the ball holder... wtf
		if(player == bh_player)
		{
			//helper->SetState( CS_NONE, NULL, true );
			//helper->SetState( CS_NONE, NULL, false );
			return;
		}

		SSRole* player_role = player->GetRole();
		SSRole* bh_role = bh_player->GetRole();

		RURoleStandardBallHolder* standard_bh = NULL;
		MabTypeID bhRoleType = bh_role->RTTGetType();
		// Double check to see if the ball holder is the standard ball holder role
		if( bhRoleType == RURoleStandardBallHolder::RTTGetStaticType() )
		{
			standard_bh = game_state->GetBallHolder()->GetRole<RURoleStandardBallHolder>();
		}

		if(standard_bh == NULL)
		{
			//helper->SetState( CS_NONE, NULL, true );
			//helper->SetState( CS_NONE, NULL, false );
			return;
		}

		// The ballholder is tackled on ground, generally in a ruck?
		if(bh_player->GetActionManager()->IsActionRunning(ACTION_TACKLEE))
		{
			RUActionTacklee* tackleeAction = bh_player->GetActionManager()->GetAction<RUActionTacklee>();
			if(tackleeAction->GetState() == TS_IN_TACKLE_ON_GROUND)
			{
				//helper->SetState( CS_NONE, NULL, true );
				//helper->SetState( CS_NONE, NULL, false );
				return;
			}
			else if(game_state->GetPhase() == RUGamePhase::RUCK)
			{
				//helper->SetState( CS_NONE, NULL, true );
				//helper->SetState( CS_NONE, NULL, false );
				return;
			}
			// If the balholder can't actually do anything right now while getting tackled. Don't allow requests?
			/ *if(!bh_player->GetActionManager()->CanUseAction(ACTION_PASS) ||
				!bh_player->GetActionManager()->CanUseAction(ACTION_PREPARE_KICK) ||
				!bh_player->GetActionManager()->CanUseAction(ACTION_KICK))
			{
				{
					helper->SetState( CS_NONE, NULL, true );
					helper->SetState( CS_NONE, NULL, false );
					return;
				}
			}* /
		}

		// We're getting tackled!
		if(player->GetActionManager()->IsActionRunning(ACTION_TACKLEE))
		{
			//helper->SetState( CS_NONE, NULL, true );
			//helper->SetState( CS_NONE, NULL, false );
			return;
		}

		// We're bound to a ruck
		if(player_role->RTTGetType() == RURoleRuck::RTTGetStaticType())
		{
			RURoleRuck* ruck_role = MabCast<RURoleRuck>( player_role );
			if(ruck_role->IsBound())
			{
				//helper->SetState( CS_NONE, NULL, true );
				//helper->SetState( CS_NONE, NULL, false );
				return;
			}
		}*/

		// So far so good, we're allowed to request.
		//helper->SetState( CS_PRO_WITHOUT_BALL_ATT, human_player, true );
		//helper->SetState( CS_NONE, NULL, false );

		if ( human_player->IsPressed( ERugbyGameAction::CALL_FOR_PASS ) )
		{
			// return true if the request was successful
			if(standard_bh->MakeProRequest(PAR_PASS, player))
			{
				MABLOGDEBUG( "CALLING FOR PASS" );
				game->GetEvents()->pro_requested_pass(); // <-- Plays the audio for request

				// TODO... when/if we get animations
				//RUPlayerAnimation* anim = player->GetAnimation();

				//if(anim->IsAnimationAvailable("foottap"))
				//{
				//	anim->PlayAnimation("foottap");
				//}

				player->GetAttributes()->AddPassRequest();
			}
		}

		else if ( human_player->IsPressed( ERugbyGameAction::CALL_FOR_KICK_DOWN_FIELD ) )
		{
			// return true if the request was successfull
			if(standard_bh->MakeProRequest(PAR_KICK_DOWN_FIELD, player))
			{
				MABLOGDEBUG( "CALLING FOR KICK DOWN FIELD" );
				game->GetEvents()->pro_requested_kick_down_field(); // <-- Plays the audio for request

				// TODO... when/if we get animations
				//RUPlayerAnimation* anim = player->GetAnimation();

				//if(anim->IsAnimationAvailable("foottap"))
				//{
				//	anim->PlayAnimation("foottap");
				//}
			}
		}

		else if ( human_player->IsPressed( ERugbyGameAction::CALL_FOR_KICK_INTO_TOUCH ) )
		{
			// return true if the request was successfull
			if(standard_bh->MakeProRequest(PAR_KICK_FOR_TOUCH, player))
			{
				MABLOGDEBUG( "CALLING FOR KICK INTO TOUCH" );
				game->GetEvents()->pro_requested_kick_for_touch(); // <-- Plays the audio for request

				// TODO... when/if we get animations
				//RUPlayerAnimation* anim = player->GetAnimation();

				//if(anim->IsAnimationAvailable("foottap"))
				//{
				//	anim->PlayAnimation("foottap");
				//}
			}
		}
	}
#endif
}


void SSInputManager::UpdateRuckControls( SSHumanPlayer* human_player )
{
	MABASSERT( game->GetGameState()->GetPhase() == RUGamePhase::RUCK );
	RUGamePhaseRuck* ruck_phase = game->GetGameState()->GetPhaseHandler<RUGamePhaseRuck>();

#ifdef ENABLE_PRO_MODE
	if(game->GetGameSettings().game_settings.GetIsAProMode())
	{
		//#rc3_legacy
		// Check what our context state is at this point, is there an easier way?
		switch (game->Get3DHudManager()->GetContextualHelper()->GetPrimaryState()->GetState())
		{
			// RUCK HAS FORMED DURING A PRO MODE GAME
		case CS_RUCK_FORMED_PRO_MODE:

			// Quick bind
			if ( human_player->IsPressed(ERugbyGameAction::JOIN_RUCK ) )
			{
				MABLOGDEBUG("UpdateRuckControls::Player wants to join the ruck, quick bind");
				if (ruck_phase->GetAttackingTeamPlayerIndex() == human_player->GetPlayerSlot() || ruck_phase->GetDefendingTeamPlayerIndex() == human_player->GetPlayerSlot())
				{
					ruck_phase->RequestRuckJoin( human_player->GetRugbyCharacter()->GetAttributes()->GetTeam(), RUCK_JOIN_NORMAL, true );
				}
			}
			// heavy bind
			else if ( human_player->IsPressed(ERugbyGameAction::JOIN_RUCK_AGGRESSIVE ) )
			{
				MABLOGDEBUG("UpdateRuckControls::Player wants to join the ruck, heavy bind");
				if (ruck_phase->GetAttackingTeamPlayerIndex() == human_player->GetPlayerSlot() || ruck_phase->GetDefendingTeamPlayerIndex() == human_player->GetPlayerSlot())
				{
					ruck_phase->RequestRuckJoin( human_player->GetRugbyCharacter()->GetAttributes()->GetTeam(), RUCK_JOIN_AGGRESSIVE, true );
				}
			}
			// become ruck half
			else if ( human_player->IsPressed(ERugbyGameAction::RUCK_JOIN_RUCK_HALF ) )
			{
				MABLOGDEBUG("UpdateRuckControls::Player wants to take the role as ruck half");
				// Find the existing ruck half
				SIFRugbyCharacterList scrum_rucks;
				game->GetStrategyHelper()->GetTeamRoles( human_player->GetTeam(), RURoleRuckScrumHalf::RTTGetStaticType(), scrum_rucks );

				// tell the existing ruck half to take normal ruck duties
				for(SIFRugbyCharacterList::const_iterator i = scrum_rucks.begin(); i != scrum_rucks.end(); ++i)
				{
					(*i)->SetRole(game->GetRoleFactory()->Instance(RURoleRuck::RTTGetStaticType()), true);
				}

				// Now we make ourselves the ruck half
				//human_player->GetPlayer()->GetActionManager()->HFLock(HF_MOVEMENT);
				SSRole* role = game->GetRoleFactory()->Instance(RURoleRuckScrumHalf::RTTGetStaticType());
				human_player->GetRugbyCharacter()->SetRole(role, true);

				//game->GetEvents()->ruck_pro_join_half_ruck(human_player->GetPlayer());
			}

			break;

		case CS_RUCK_FORMED_PRO_MODE_NO_CONTEST:

			if ( human_player->IsPressed(ERugbyGameAction::RUCK_LEAVE ) )
			{
				if (ruck_phase->GetAttackingTeamPlayerIndex() == human_player->GetPlayerSlot() || ruck_phase->GetDefendingTeamPlayerIndex() == human_player->GetPlayerSlot())
					ruck_phase->RequestRuckLeave( human_player->GetRugbyCharacter()->GetAttributes()->GetTeam(), true );
			}

			break;

			// OUR PRO IS THE RUCK HALF YO
		case CS_RUCK_FORMED_PRO_MODE_RUCK_HALF:

			if ( human_player->IsPressed(ERugbyGameAction::RUCK_LEAVE_RUCK_HALF ) )
			{
				MABLOGDEBUG("UpdateRuckControls::Player wants to leave the role as ruck half");

				// Get all the ruck players
				//SIFRugbyCharacterList ruck_players;
				//game->GetStrategyHelper()->GetTeamRoles( human_player->GetTeam(), RURoleRuck::RTTGetStaticType(), ruck_players );

				// Swap ourselves with the closest ruck player?

				//human_player->GetPlayer()->GetActionManager()->HFLockAll();
				human_player->GetRugbyCharacter()->GetAttributes()->nextIgnoredRole = RURoleRuckScrumHalf::RTTGetStaticType();

				SSRole* role = game->GetRoleFactory()->Instance(RURoleRuck::RTTGetStaticType());
				human_player->GetRugbyCharacter()->SetRole(role, true);

				game->GetEvents()->ruck_pro_leave_half_ruck(human_player->GetRugbyCharacter());
			}

			break;
			// OUR PRO IS INSIDE THE RUCK, BUT NOT AS THE RUCK HALF
		case CS_RUCK_FORMED_PRO_MODE_NON_RUCK_HALF:

			if ( human_player->IsPressed(ERugbyGameAction::RUCK_LEAVE ) )
			{
				MABLOGDEBUG("UpdateRuckControls::Player wants to leave the ruck");
				if (ruck_phase->GetAttackingTeamPlayerIndex() == human_player->GetPlayerSlot() || ruck_phase->GetDefendingTeamPlayerIndex() == human_player->GetPlayerSlot())
				{
					//human_player->GetPlayer()->GetActionManager()->HFUnlockAll();
					ruck_phase->RequestRuckLeave( human_player->GetRugbyCharacter()->GetAttributes()->GetTeam(), true );
					//game->GetEvents()->ruck_pro_leave_ruck(human_player->GetPlayer());
				}
			}

			break;
		default:
			break;
		}
	}
	else
#endif
	{
		// TODO possibly? : HF Lock for rucking
		// Aiming controls

		constexpr const int MAP_SIZE = 2;
		constexpr const int ACTION_MAP[MAP_SIZE][2] = {
			{ size_t(ERugbyGameAction::RUCK_JOIN),			RUCK_JOIN_NORMAL },
			{ size_t(ERugbyGameAction::RUCK_JOIN_AGGRESSIVE),	RUCK_JOIN_AGGRESSIVE }
		};

		for( int i = 0; i < MAP_SIZE; i++ )
		{
			/// Any player has control over entering players into the ruck
			if ( human_player->IsPressed( (ERugbyGameAction) ACTION_MAP[i][0] ) )
			{
				if (ruck_phase->GetAttackingTeamPlayerIndex() == human_player->GetPlayerSlot() || ruck_phase->GetDefendingTeamPlayerIndex() == human_player->GetPlayerSlot())
					ruck_phase->RequestRuckJoin( human_player->GetRugbyCharacter()->GetAttributes()->GetTeam(), (RUZoneJoinType) ACTION_MAP[i][1] );
			}
		}

		/// The player who controls the power battle is
		///    If only 1 human player, that human player
		///    If more than one - cycle to the next human player

		if ( human_player->IsPressed( ERugbyGameAction::RUCK_LEAVE ) )
		{
			if (ruck_phase->GetAttackingTeamPlayerIndex() == human_player->GetPlayerSlot() || ruck_phase->GetDefendingTeamPlayerIndex() == human_player->GetPlayerSlot())
				ruck_phase->RequestRuckLeave( human_player->GetRugbyCharacter()->GetAttributes()->GetTeam() );
		}
	}

	if(ruck_phase->GetCanDefendingTeamContest())
	{
		// Contesting the ruck controls
		if ( human_player->IsPressed( ERugbyGameAction::RUCK_CONTEST_BALL ) )
		{
			if (ruck_phase->GetDefendingTeamPlayerIndex() == human_player->GetPlayerSlot())
				ruck_phase->RequestContestBall( human_player->GetRugbyCharacter()->GetAttributes()->GetTeam() );
		}
	}

	if(ruck_phase->GetCanAttackingTeamContest())
	{
		if (ruck_phase->GetAttackingTeamPlayerIndex() == human_player->GetPlayerSlot() )
			ruck_phase->SetHoldOntoContestedBallState( human_player->GetRugbyCharacter()->GetAttributes()->GetTeam(), human_player->IsOn( ERugbyGameAction::RUCK_CONTEST_BALL ) );
	}
}


void SSInputManager::UpdateMaulControls( SSHumanPlayer* human_player )
{
	MABASSERT( game->GetGameState()->GetPhase() == RUGamePhase::MAUL );

	constexpr const int MAP_SIZE = 1;
	constexpr const int ACTION_MAP[MAP_SIZE][2] = {
		{ size_t(ERugbyGameAction::RUCK_JOIN),	RUCK_JOIN_NORMAL }
	};

	RUGamePhaseMaul* maul_phase = game->GetGameState()->GetPhaseHandler<RUGamePhaseMaul>();
	bool proGame = SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro();

	for( int i = 0; i < MAP_SIZE; i++ )
	{
		/// Any player has control over entering players into the ruck
		if ( human_player->IsPressed( (ERugbyGameAction) ACTION_MAP[i][0] ) )
		{
			// Pro games, we want the pro to join the maul
			if(proGame)
			{
				maul_phase->RequestMaulJoinProPlayer( SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer()->GetAttributes()->GetTeam(), (RUZoneJoinType) ACTION_MAP[i][1] );
			}
			// Otherwise handle it as normal, and check if we're in control of the ruck, etc.
			else
			{
				if (human_player->GetPlayerSlot() == maul_phase->GetAttackingTeamPlayerIndex() || human_player->GetPlayerSlot() == maul_phase->GetDefendingTeamPlayerIndex())
					maul_phase->RequestMaulJoin( human_player->GetRugbyCharacter()->GetAttributes()->GetTeam(), (RUZoneJoinType) ACTION_MAP[i][1] );
			}
		}
	}

	// attempt to handoff, the game phase will say if this is a valid action
	if ( human_player->IsPressed( ERugbyGameAction::MAUL_HAND_OFF ) )
		maul_phase->AttemptHandOff( human_player->GetRugbyCharacter()->GetAttributes()->GetTeam() );


}

void SSInputManager::UpdateExtraTimeCoinTossControls( SSHumanPlayer* /*human_player*/ )
{
	/*MABASSERT( game->GetGameState()->GetPhase() == RUGamePhase::EXTRA_TIME_TOSS );
	RUGamePhaseExtraTimeToss* phase = game->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();

	// Dont want to enable input if the phase is controlled by the CPU/AI
	if(phase->IsDecisionMakerCPU())
		return;

	if( human_player->IsOn( RU_DECISION_LEFT ) )
	{
		MABLOGDEBUG("Coin Toss Phase Input pressing decision left");
		phase->ChooseLeft();
		return;
	}

	if( human_player->IsOn( RU_DECISION_RIGHT ) )
	{
		MABLOGDEBUG("Coin Toss Phase Input pressing decision right");
		phase->ChooseRight();
		return;
	}

	// Cant get these to work T_T
	if( human_player->IsOn( RU_COIN_TOSS_OPT_1 ) )
	{
		MABLOGDEBUG("Coin Toss Phase Input pressing Option 1");
		return;
	}

	if( human_player->IsOn( RU_COIN_TOSS_OPT_2 ) )
	{
		MABLOGDEBUG("Coin Toss Phase Input pressing Option 2");
		return;
	}*/
}

//void SSInputManager::UpdateLineoutControls( SSHumanPlayer* human_player )
//{
//	MABASSERT( game->GetGameState()->GetPhase() == RUGamePhase::LINEOUT );
//
//	// TYRONE : At request of HES to stop controls firing off early when player spams skip through cutscenes
//	const static float MIN_WAIT_BEFORE_CONTROL_TIME = 1.5f;
//	if ( game->GetGameState()->GetTimeSincePhaseChange() < MIN_WAIT_BEFORE_CONTROL_TIME )
//		return;
//
//	RUGamePhaseLineOut* lineout_phase = game->GetGameState()->GetPhaseHandler<RUGamePhaseLineOut>();
//	int team_index = human_player->GetTeam()->GetIndex();
//	SSHumanPlayer* human_controlling_lineout = lineout_phase->GetControllingHuman( team_index );
//
//	// Make sure the human player able to control the lineout is the first one at the team
//	if ( human_player != human_controlling_lineout )
//		return;
//
//	/*if( human_player->IsPressed(RU_LINEOUT_SELECT_JUMPER_FRONT) )
//	{
//		lineout_phase->SelectFrontJumper(team_index);
//	}
//	if( human_player->IsPressed(RU_LINEOUT_SELECT_JUMPER_MIDDLE) )
//	{
//		lineout_phase->SelectMiddleJumper(team_index);
//	}
//	if( human_player->IsPressed(RU_LINEOUT_SELECT_JUMPER_BACK) )
//	{
//		lineout_phase->SelectBackJumper(team_index);
//	}*/
//
//	//RC4-2400: Below code is commented since the Gamepad_LeftY (thumbstick) doesnot behave like On/Off buton as we may never hit the deadzone,  
//	//so ideally cannot be used in below logic. Instead we can just get the thumbstick direction and move the formation.
//	//Also the logic for LINEOUT_MOVE_JUMPER_FURTHER and LINEOUT_MOVE_JUMPER_CLOSER is redundant when we dont monitor the on/off of thumbstick.
//	
//	//	if ( human_player->IsOn(ERugbyGameAction::LINEOUT_MOVE_JUMPER) )
//	{
//		int direction = human_player->GetLeftAnalogYDirection();
//		
//		if (direction!=0)
//		{
//			lineout_phase->MoveFormation(team_index, direction);
//		}
//	}
//
//	/*if ( human_player->IsOn(ERugbyGameAction::LINEOUT_MOVE_JUMPER_FURTHER) )
//	{
//		lineout_phase->MoveFormation(team_index, -1);
//	}
//
//	if ( human_player->IsOn(ERugbyGameAction::LINEOUT_MOVE_JUMPER_CLOSER) )
//	{
//		lineout_phase->MoveFormation(team_index, 1);
//	}*/
//
//	//if ( human_player->IsOn(ERugbyGameAction::LINEOUT_SELECT_JUMPER) )
//	if (human_player->GetRightAnalogYDirection()!=0)
//	{
//		int direction = human_player->GetRightAnalogYDirectionDiscrete();
//		
//		if (direction)
//		{
//			lineout_phase->SelectJumper(team_index, direction);
//		}
//	}
//
//	if ( human_player->IsReleased(ERugbyGameAction::LINEOUT_SELECT_NEXT_JUMPER) )
//	{
//		lineout_phase->SelectJumper(team_index, 1);
//	}
//
//	if ( human_player->IsReleased(ERugbyGameAction::LINEOUT_SELECT_PREVIOUS_JUMPER) )
//	{
//		lineout_phase->SelectJumper(team_index, -1);
//	}
//
// /*	if ( human_player->IsOn(RU_PASS_RIGHT_AUTO_DETERMINE) )
//	{
//		lineout_phase->MoveFormation(team_index, 1);
//	}
//
//	if ( human_player->IsOn(RU_PASS_LEFT_AUTO_DETERMINE) )
//	{
//		lineout_phase->MoveFormation(team_index, -1);
//	}*/
//
//	if ( human_player->IsPressed(ERugbyGameAction::LINEOUT_PASS) )
//	{
//		lineout_phase->Jump(team_index);
//		lineout_phase->PassTheBall(team_index);
//		//lineout_phase->TriggerMaulFromLineout(team_index, true);//debug mauls
//	}
//
//	if (human_player->IsPressed(ERugbyGameAction::LINEOUT_JUMP) )
//	{
//		lineout_phase->Jump(team_index);
////		lineout_phase->TriggerMaulFromLineout(team_index, true);//debug mauls
//	}
//
//	if (human_player->IsPressed(ERugbyGameAction::LINEOUT_MAUL) )
//	{
//		lineout_phase->Jump(team_index);
//		lineout_phase->TriggerMaulFromLineout(team_index, true);
//	}
//
//	if ( lineout_phase->GetAttackingTeamIndex() == team_index &&
//		 (human_player->IsPressedLocal(ERugbyGameAction::LINEOUT_THROW_NEAR) || human_player->IsPressedLocal(ERugbyGameAction::LINEOUT_THROW_MID) || human_player->IsPressedLocal(ERugbyGameAction::LINEOUT_THROW_FAR) ) )
//	{
//		lineout_phase->LockVisualPosition();
//	}
//
//	if ( human_player->IsPressed(ERugbyGameAction::LINEOUT_THROW_NEAR) )
//	{
//		THROW_TYPE throw_type = GetLineoutThrowType(human_player);
//		lineout_phase->IntendToThrowTheBall(team_index, TP_NEAR, throw_type, human_player->GetLastMinigameResult());
//	}
//
//	if ( human_player->IsPressed(ERugbyGameAction::LINEOUT_THROW_MID) )
//	{
//		THROW_TYPE throw_type = GetLineoutThrowType(human_player);
//		lineout_phase->IntendToThrowTheBall(team_index, TP_MID, throw_type, human_player->GetLastMinigameResult());
//	}
//
//	if ( human_player->IsPressed(ERugbyGameAction::LINEOUT_THROW_FAR) )
//	{
//		THROW_TYPE throw_type = GetLineoutThrowType(human_player);
//		lineout_phase->IntendToThrowTheBall(team_index, TP_FAR, throw_type, human_player->GetLastMinigameResult());
//	}
//}

//------------------------------------------------------------------------------------------

void SSInputManager::UpdatePlayTheBallPreloadPass(SSHumanPlayer* human_player)
{

	RUGamePhasePlayTheBall* ptb_phase_handler = game->GetGameState()->GetPhaseHandler<RUGamePhasePlayTheBall>();

	if (human_player->IsOn(ERugbyGameAction::PASS_LEFT_AUTO_DETERMINE))
	{
		ptb_phase_handler->PreloadPass(RUGamePhasePlayTheBall::EPreloadPassDirection::LEFT);
	}
	else if (human_player->IsOn(ERugbyGameAction::PASS_RIGHT_AUTO_DETERMINE))
	{
		ptb_phase_handler->PreloadPass(RUGamePhasePlayTheBall::EPreloadPassDirection::RIGHT);
	}
	else
	{
		ptb_phase_handler->CancelPreloadPass();
	}


	if (ptb_phase_handler->IsPassPreloaded() == false)
	{
		return;
	}

	// If we are preloading a pass, we want to get all possible receivers to anticipate the pass

	ARugbyCharacter* player = human_player->GetRugbyCharacter();

	TArray<ARugbyCharacter*> receiver_candidates;
	int pass_inclusion_roles;
	RUActionPass::CalculateReceiversInPassDirectionNoModify(player, ptb_phase_handler->GetPreloadPassDirection(), receiver_candidates, pass_inclusion_roles);

	for (int i = 0; i < receiver_candidates.Num(); i++)
	{
		ARugbyCharacter* receiver = receiver_candidates[i];
		verify(receiver != nullptr);

		if (receiver->GetActionManager()->CanUseAction(RU_ACTION_INDEX::ACTION_PASS_ANTICIPATION))
		{
			receiver->GetRole()->StartActionPassAnticipation(
				player,
				pass_extend_interface,
				ptb_phase_handler->GetPreloadPassDirection(),
				i,
				receiver_candidates.Num(),
				RUActionPassAnticipation::AM_LOCKED_TO_PASSER
			);
		}
	}

	// Get last valid candidate in the index, iterate from the furthest player to the closest
	ARugbyCharacter* best_receiver = nullptr;
	for (int first_candidate_index = receiver_candidates.Num() - 1; first_candidate_index >= 0; first_candidate_index--)
	{
		ARugbyCharacter* candidate = receiver_candidates[first_candidate_index];
		if (candidate != nullptr)
		{
			best_receiver = candidate;
			break;
		}
	}

	// If we still didn't find a receiver, try 1 final time to get one
	if (best_receiver == nullptr)
	{
		const SIFRugbyCharacterList& player_list = player->GetAttributes()->GetTeam()->GetPlayers();
		const FVector& player_position = player->GetMovement()->GetCurrentPosition();
		ARugbyCharacter* candidate = RUActionPass::GetBestReceiverInDirection(player_position, player_list, ptb_phase_handler->GetPreloadPassDirection());
		
		best_receiver = candidate;

	}

	ptb_phase_handler->SetBestPreloadReceiver(best_receiver); // Can be nullptr if no good candidates were found

}

THROW_TYPE SSInputManager::GetLineoutThrowType (SSHumanPlayer* /*human_player*/) const
{
	THROW_TYPE throw_type;

	//if ( human_player->IsOn (RU_LINEOUT_THROW_SHORT) )
	//{
	//	throw_type = THROW_SHORT;
	//}
	//else
	//if ( human_player->IsOn (RU_LINEOUT_THROW_LONG) )
	//{
	//	throw_type = THROW_LONG;
	//}
	//else
	//{
		throw_type = THROW_NORMAL;
	//}

	return throw_type;
}

void SSInputManager::UpdateScrumControls( SSHumanPlayer* human_player )
{
	MABASSERT( game->GetGameState()->GetPhase() == RUGamePhase::SCRUM );

	// Crouch, pause, engage
	constexpr const int MAP_SIZE = 6;
	constexpr const int ACTION_MAP[MAP_SIZE][2] = {
		{ size_t(ERugbyGameAction::SCRUM_ENGAGE),					RUGamePhaseScrum::SCRUM_ACTION_ENGAGE },
		{ size_t(ERugbyGameAction::SCRUM_NO_EIGHT),				RUGamePhaseScrum::SCRUM_ACTION_NO_EIGHT },
		{ size_t(ERugbyGameAction::SCRUM_RELEASE_SCRUM_HALF),		RUGamePhaseScrum::SCRUM_ACTION_SCRUM_HALF },
		{ size_t(ERugbyGameAction::PASS_RIGHT_AUTO_DETERMINE),	RUGamePhaseScrum::SCRUM_ACTION_PASS },
		{ size_t(ERugbyGameAction::PASS_LEFT_AUTO_DETERMINE),		RUGamePhaseScrum::SCRUM_ACTION_PASS }
	};

	for( int i = 0; i < MAP_SIZE; i++ )
	{
		RUGamePhaseScrum* scrum_phase = game->GetGameState()->GetPhaseHandler<RUGamePhaseScrum>();
		/// Any player has control over entering players into the ruck
		if ( human_player->IsPressed( (ERugbyGameAction) ACTION_MAP[i][0] ) )
		{
			// Pulse/different types of joinins
			scrum_phase->ExecuteScrumAction( human_player, (RUScrumEngageAction) ACTION_MAP[i][1] );
		}
	}
}

void SSInputManager::UpdateDecisionControls( SSHumanPlayer* human_player )
{
	if( human_player->IsOn( ERugbyGameAction::DECISION_TOP ) )
	{
		decision_interface->HandleDecision( ERugbyGameAction::DECISION_TOP );
		return;
	}
	if( human_player->IsOn( ERugbyGameAction::DECISION_RIGHT ) )
	{
		decision_interface->HandleDecision( ERugbyGameAction::DECISION_RIGHT );
		return;
	}
	if( human_player->IsOn( ERugbyGameAction::DECISION_BOTTOM ) )
	{
		decision_interface->HandleDecision( ERugbyGameAction::DECISION_BOTTOM );
		return;
	}
	if( human_player->IsOn( ERugbyGameAction::DECISION_LEFT ) )
	{
		decision_interface->HandleDecision( ERugbyGameAction::DECISION_LEFT );
		return;
	}
}

void SSInputManager::UpdateTackling( SSHumanPlayer* human_player )
{
	ARugbyCharacter* player = human_player->GetRugbyCharacter();
	MABASSERT(player);
	RUGameState* game_state = game->GetGameState();
	RUActionManager *action_manager = NULL;
	if (player)
		action_manager = player->GetActionManager();

	if ( player
		&& game_state->GetBallHolder()
		&& game_state->GetBallHolder()->GetAttributes()->GetTeam() != player->GetAttributes()->GetTeam()
		&& player->GetRole()
		&& !action_manager->HFIsLocked(HF_TACKLE) )
	{
		bool heavy_tackle = human_player->IsOn(ERugbyGameAction::TACKLE_HEAVY);
		bool tackle = human_player->IsOn(ERugbyGameAction::TACKLE_STANDARD) || heavy_tackle;
		bool action_running = player->GetActionManager()->IsActionRunning(ACTION_CHARGE_ATTACKER);
		const static float MOVING_AWAY_FROM_BALL_MIN_TIME = 0.25f;
		const static float MIN_CHARGE_AUTO_TIME = 0.25f;
		bool is_moving_away_from_ball = human_player->IsMovingAwayFromBall( MOVING_AWAY_FROM_BALL_MIN_TIME );
		bool cancel_charge_attacker = action_running &&
			(is_moving_away_from_ball || (!is_moving_away_from_ball && human_player->GetTimeSinceLastPlayerChange() > MIN_CHARGE_AUTO_TIME && !human_player->HasTackleActivatedSinceLastPlayerChange() ) );

		if ( tackle )
		{
			// Check if we should start charging
			RUTackleRangeMeta range_meta;
			game->GetTackleHelper()->GetTackleRangeMeta( game->GetGameState()->GetBallHolder(), player, range_meta );

			// If the tackle button is down and we're within 5 meters, charge the ball holder
			if (range_meta.in_charge_attacker_range
				&& player->GetActionManager()->CanUseAction(ACTION_CHARGE_ATTACKER)
				&& !action_manager->HFIsLocked( HF_MOVEMENT ) ) // We can't charge if out movement is locked
			{
				player->GetRole()->StartActionChargeAttacker( game_state->GetBallHolder() );
				human_player->SetTackleActivatedSinceLastPlayerChange( true );
			}

			// Queue up a tackle
			SSGenericRoleOption& cached_role_option = human_player->GetCachedRoleOption();
			cached_role_option.Create( ROPT_DEFEND_TACKLE, 0, player );
			cached_role_option.heavy_tackle = heavy_tackle;
		}
		else if ( cancel_charge_attacker  )
		{
			// Cancel a charge thats happening
			player->GetActionManager()->GetAction<RUActionChargeAttacker>()->Exit();
		}

		// check for strip if tackling
		if( player->GetActionManager()->IsActionRunning(ACTION_TACKLER) )
		{
			//// check if the action is firing
			//if( human_player->IsPressed( RU_SPRINT ) )
			//{
			//	ARugbyCharacter* tacklee = player->GetActionManager()->GetAction<RUActionTackler>()->GetTacklee();
			//	// tacklee should always be defined, but check anyway
			//	if( tacklee )
			//	{
			//		tacklee->GetActionManager()->GetAction<RUActionTacklee>()->AttemptStripBall();
			//	}
			//}
		}
	}

	// Should we try break out?
	//if ( human_player->IsPressed( RU_SPRINT ) )
	//{
	//	if (player->GetActionManager()->IsActionRunning( ACTION_TACKLEE ) )
	//		player->GetActionManager()->GetAction<RUActionTacklee>()->AttemptBreakOut();
	//}
}

void SSInputManager::UpdateFending (SSHumanPlayer* human_player)
{
	ARugbyCharacter* player = human_player->GetRugbyCharacter();
	RUActionManager* action_manager = player->GetActionManager();

	// should we start the fending action?
	if (CVarFendLogicState.GetValueOnGameThread() != RUActionFend::EFendLogic::EXACT_TIMING || !player->GetAttributes()->IsFendOnCooldown())
	{
		if ((human_player->IsStartingFendingGesture() || human_player->IsPressed(ERugbyGameAction::FEND)) && action_manager->CanUseAction(ACTION_FEND))
		{
			player->GetRole()->StartActionFend();
		}
	}
}

void SSInputManager::UpdateSprinting( SSHumanPlayer* human_player )
{
	MABUNUSED(human_player);

	//ARugbyCharacter* player = human_player->GetPlayer();
	//if( player != NULL )
	//{
	//	if( human_player->IsOn( RL3_SPRINT ) )
	//	{
	//		human_player->GetPlayer()->GetSprinting()->ApplySprint();
	//	}
	//}
}

void SSInputManager::UpdateUserStrategySelection( SSHumanPlayer* human_player )
{
	MABUNUSED(human_player);

//	const float AUTO_SELECT_TIME = 0.4f;
//
//	int controller_index = human_player->GetControllerIndex();
//
//	if ( user_strategy_handler->IsDisplaying( human_player->GetTeam() ) ) {
//		if ( control_action_manager->GetInput( controller_index, RL3_INCREMENT_USER_STRATEGY ).Pressed() )
//			user_strategy_handler->Increment( human_player->GetPlayer()->GetTeam(), human_player );
//		else if ( control_action_manager->GetInput( controller_index, RL3_INCREMENT_USER_STRATEGY ).On() &&
//			control_action_manager->GetInput( controller_index, RL3_INCREMENT_USER_STRATEGY ).GetOnTime() >= AUTO_SELECT_TIME )
//		{
//			user_strategy_handler->SelectAuto( human_player->GetTeam(), human_player );
//		}
//
//#if PLATFORM_WII
//		bool using_basic = SIFApplication::GetApplication()->GetWiiControllerHelper()->IsBasicGameConfigActive(human_player->GetControllerIndex());
//
//		if (!using_basic && !control_action_manager->GetInput( controller_index, RL3_INCREMENT_USER_STRATEGY).On())
//		{
//
//			if ( control_action_manager->GetInput( controller_index, RL3_UP).Pressed() )
//			{
//				user_strategy_handler->SelectIndex( human_player->GetTeam(), human_player, 0 );
//			}
//			else if ( control_action_manager->GetInput( controller_index, RL3_DOWN).Pressed() )
//			{
//				user_strategy_handler->SelectIndex( human_player->GetTeam(), human_player, 1 );
//			}
//			else if ( control_action_manager->GetInput( controller_index, RL3_LEFT).Pressed() )
//			{
//				user_strategy_handler->SelectIndex( human_player->GetTeam(), human_player, 2 );
//			}
//			else if ( control_action_manager->GetInput( controller_index, RL3_RIGHT).Pressed() )
//			{
//				user_strategy_handler->SelectIndex( human_player->GetTeam(), human_player, 3 );
//			}
//		}
//#endif
//	} else {
//		if ( control_action_manager->GetInput( controller_index, RL3_INCREMENT_USER_STRATEGY ).Pressed() )
//			user_strategy_handler->Display( human_player->GetTeam(), human_player );
//	}
}

void SSInputManager::UpdateMovement( SSHumanPlayer* human_player )
{
	ARugbyCharacter* player = human_player->GetRugbyCharacter();
	if ( !player )
		return;

	RUPlayerMovement *movement = player->GetMovement();
	RUActionManager* action_mgr = player->GetActionManager();

	if (!action_mgr->HFIsLocked(HF_MOVEMENT ) && !action_mgr->UFIsLocked(UF_SETWAYPOINT))
	{
		const FVector& input_vec = human_player->GetInputVector();
		FVector target_pos = player->GetMovement()->GetCurrentPosition() + input_vec;

		movement->SetAccelerationThrottle( human_player->GetThrottle() );

		const static float MIN_THROTTLE_SPEED_MULTIPLIER = 0.3f;
		const static float MAX_THROTTLE_SPEED_MULTIPLIER = 1.0f;
		float speed_throttle_modifier = MabMath::Lerp( MIN_THROTTLE_SPEED_MULTIPLIER, MAX_THROTTLE_SPEED_MULTIPLIER, human_player->GetThrottle() );

		/// If the input vector drops straight to zero - we want to account for residual velocity
		/// otherwise the requested target will be the same as the current position
		const static float VELOCITY_EXTRPOLATE_TIME = 0.3f;
		if ( MabMath::Feq( input_vec.Magnitude(), 0.0f ) )
		{
			target_pos += player->GetMovement()->GetCurrentVelocity() * VELOCITY_EXTRPOLATE_TIME;
			speed_throttle_modifier = 0.0f;
		}

		//SETDEBUGLINE( ********, player->GetMovement()->GetCurrentPosition(), target_pos, MabColour::Blue, MabColour::Red );

		// temp to not move the dude
		//target_pos = player->GetMovement()->GetCurrentPosition();


		FieldExtents extents = game->GetSpatialHelper()->GetClampFieldExtents();
		MabMath::Clamp(target_pos.x, -extents.x, extents.x);
		MabMath::Clamp(target_pos.z, -extents.y, extents.y);

		const static float MIN_DEFENCE_Z_OFFSET = 10.0f;
		const static float MIN_ATTACK_Z_OFFSET = 1.5f;
		/// If requested, prevent humans from encroaching on the ball holder
		if ( game->GetGameState()->IsHumanMovementLocked() )
		{
			//Don't clamp the position of the scrum half during a scrum, so that they can stand beside the scrum.
			if (player->GetAttributes()->GetPlayerPosition() != PP_SCRUM_HALF || game->GetGameState()->GetPhase() != SCRUM)
			{
				float offside_z;

				/// If we're on attack allow them to move a little bit ahead of the play restart position
				if (game->GetGameState()->GetAttackingTeam() == player->GetAttributes()->GetTeam())
					offside_z = game->GetGameState()->GetPlayRestartPosition().z + player->GetAttributes()->GetPlayDirection() * MIN_ATTACK_Z_OFFSET;
				else
					/// On defence - we must be minimum dist behind play restart pos
					offside_z = game->GetGameState()->GetPlayRestartPosition().z - player->GetAttributes()->GetPlayDirection() * MIN_DEFENCE_Z_OFFSET;

				if (MabMath::Fabs(offside_z) < FIELD_LENGTH * 0.5f) {// Don't clamp if this position is behind the goal line
					if (player->GetAttributes()->GetPlayDirection() == ERugbyPlayDirection::NORTH)
						MabMath::ClampUpper(target_pos.z, offside_z);
					else
						MabMath::ClampLower(target_pos.z, offside_z);
				}
			}
		}

		wwNETWORK_TRACE_JG("SSInputManager::UpdateMovement Phase: %d, IsHumanMovementLocked: %d", game->GetGameState()->GetPhase(), game->GetGameState()->IsHumanMovementLocked());

		// gross hacks, for lineout and scrum the behaviour of setting HumanMovementLocked
		// is to stop any movement at all
		if ((game->GetGameState()->GetPhase() != RUGamePhase::LINEOUT /*&& game->GetGameState()->GetPhase() != RUGamePhase::SCRUM*/)
			|| !game->GetGameState()->IsHumanMovementLocked() || game->GetGameState()->GetPhase() == RUGamePhase::DECISION_PENALTY)
		{
			wwNETWORK_TRACE_JG("SSInputManager::UpdateMovement SetTargetPosition");
			movement->SetTargetPosition(target_pos);
		}

		const static float MAX_NON_SPRINT_PCT = 0.8f;

		float target_speed = movement->GetMaxSpeed() * speed_throttle_modifier;
		//MABLOGDEBUG(MabString(0, "target_speed: %f, speed_throttle_modifier: %f", target_speed, speed_throttle_modifier).c_str());
		if ( !(human_player->IsOn(ERugbyGameAction::SPRINT) || human_player->IsOn(ERugbyGameAction::SPRINT_ALT) || human_player->IsOn( ERugbyGameAction::SPRINT_ALT2 )) )
		{
			MabMath::ClampUpper( target_speed, movement->GetMaxSpeed() * MAX_NON_SPRINT_PCT );
			//MABLOGDEBUG(MabString(0, "not sprinting... clamp target_speed: %f", target_speed).c_str());
		}
		else
		{
			const static float FULL_THROTTLE = 1.20f;
			movement->SetAccelerationThrottle( FULL_THROTTLE );

			// This is to fix up the issue where the player will continue running with the sprint button held and the movement stick not held
			if(speed_throttle_modifier > 0.0f)
			{
				target_speed = movement->GetMaxSpeed();
			}

			//MABLOGDEBUG(MabString(0, "sprinting... target_speed: %f", target_speed).c_str());
		}

		// Control top speed also for player
		movement->SetTargetSpeed( target_speed );
	}
}


void SSInputManager::UpdateSidestep( SSHumanPlayer* human_player )
{
	MABUNUSED(human_player);

	ARugbyCharacter* player = human_player->GetRugbyCharacter();
	RUGameState *gamestate = game->GetGameState();

	if ( !player )
		return;

	RUActionManager* action_manager = player->GetActionManager();
	const bool able_to_sidestep = !action_manager->HFIsLocked(HF_SIDESTEP)
		&& action_manager->CanUseAction(ACTION_SIDE_STEP)
		&& player == gamestate->GetBallHolder()
		&& (human_player->IsGestureActive(ERugbyGameAction::SIDESTEP) || human_player->IsPressed( ERugbyGameAction::SIDESTEP_KB_RIGHT ) || human_player->IsPressed( ERugbyGameAction::SIDESTEP_KB_LEFT ))
		&& gamestate->IsGameInStandardPlay()
		&& player->GetActionManager()->CanPrewindAction(ACTION_SIDE_STEP)
		&& MabMath::Fabs(player->GetMovement()->GetCurrentPosition().x) < game->GetSpatialHelper()->GetFieldExtents().x / 2.0f
		&& MabMath::Fabs(player->GetMovement()->GetCurrentPosition().z) < game->GetSpatialHelper()->GetFieldExtents().y / 2.0f;
	;
	if ( able_to_sidestep )
	{
		SSRole* role = player->GetRole();
		SIDESTEP_SIDE step_side = SSS_UNKNOWN;

		if ( human_player->IsGestureActive(ERugbyGameAction::SIDESTEP) )
		{
			step_side = human_player->IsGestureRight() ? SSS_RIGHT : SSS_LEFT;
			human_player->SignalGestureProcessed();
		}
		else if ( human_player->IsPressed( ERugbyGameAction::SIDESTEP_KB_RIGHT ) )
			step_side = SSS_RIGHT;
		else
			step_side = SSS_LEFT;

		role->StartActionSideStep( step_side );
	}
}

void SSInputManager::UpdateSkipCutscene( SSHumanPlayer* human_player )
{
	wwNETWORK_TRACE_JG("SSInputManager::UpdateSkipCutscene");
	RUTeam* celebrator = game->GetCutSceneManager()->GetCelebratingTeam();
	bool can_skip = ((celebrator == NULL || celebrator == human_player->GetTeam() || celebrator->GetHumanPlayer(0) == NULL) && !game->GetCutSceneManager()->GetDisableCutSceneSkip());

	wwNETWORK_TRACE_JG("SSInputManager::UpdateSkipCutscene can_skip: %d, bp: %d", can_skip, human_player->IsReleased(ERugbyGameAction::SKIP_CUTSCENE));

	if (human_player->IsReleased(ERugbyGameAction::SKIP_CUTSCENE))
	{
		if (can_skip)
		{
			wwNETWORK_TRACE_JG("Skipping Cutscene!");
			game->GetCutSceneManager()->SkipCutScene();
		}
		else if (!can_skip)
		{
			game->GetHUDUpdater()->SetOnlineCutSceneWaitTextVisible(true);

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
			if (CVarCutsceneSkipReason.GetValueOnAnyThread() != 0 || FParse::Param(FCommandLine::Get(), TEXT("CutsceneSkipReason")))
			{
				if (celebrator != NULL)
				{
					UE_LOG(LogTemp, Log, TEXT("Cutscene Skip: NO - celebrator is NULL"));
				}
				if (celebrator != human_player->GetTeam())
				{
					UE_LOG(LogTemp, Log, TEXT("Cutscene Skip: NO - celebrator is not on your team"));
				}
				if (celebrator->GetHumanPlayer(0) != NULL)
				{
					UE_LOG(LogTemp, Log, TEXT("Cutscene Skip: NO - celebrator human is not NULL"));
				}
			}
#endif
		}
	}
}

void SSInputManager::UpdateKicking(SSHumanPlayer* human_player)
{
	// If a kick button is held down for less than this
	// amount of time, a kick is done instantly. If its held longer,
	// bullet time is activated
	const float AUTOKICK_BUTTON_TIME = 0.25f;
	// Minimum amount of time between each kick action
	// This is used by the loose ball logic, so if you go for a catch, you don't immediately start a kick because
	// you're still holding down the catch button when you player retrieves the ball
	const float MINIMUM_TIME_BETWEEN_KICKS = 0.5f;

	// If we don't have an RU player, or we're 4 meters from the try line, don't kick
	ARugbyCharacter* player = human_player->GetRugbyCharacter();
	if (!player || (player->GetMovement()->GetCurrentPosition().z * player->GetAttributes()->GetTeam()->GetPlayDirection() > (FIELD_LENGTH / 2.0f) - 4.0f))
		return;

	// Check the minimum amount of time as passed since we last processed a kick button
	bool minimum_kick_time_passed = human_player->GetTimeSinceLastKickAction() > MINIMUM_TIME_BETWEEN_KICKS;

	// If a defensive player is spamming the tackle button, we ust make sure that they were expecting the ball and don't do accidental kicks
	const static float MIN_TIME_IN_POSSESSION_KICK		= 0.25f;
	const static float MIN_TIME_IN_POSSESSION_TURNOVER	= 0.55f;
	bool can_kick_since_freeball = WasHumanExpectingBall( human_player );
	bool ball_contested_and_won_recently = false;
	const BallCollectInfo& bci = game->GetStrategyHelper()->GetLastBallCollectInfo();

	can_kick_since_freeball = can_kick_since_freeball && !ball_contested_and_won_recently;

	/// Additional checks
	if ( game->GetGameState()->GetBallHolder() == player )
	{
		RUStrategyHelper* sh = game->GetStrategyHelper();
		const BallFreeInfo& bfi = sh->GetLastBallFreeInfo();
		bool was_successful_intercept = bfi.event == BFE_PASS && bfi.event_success && bfi.last_player->GetAttributes()->GetTeam() != player->GetAttributes()->GetTeam();
		if ( bfi.WasBallLoose() || was_successful_intercept )
			can_kick_since_freeball = can_kick_since_freeball || player->GetState()->GetTimeInPossession() > MIN_TIME_IN_POSSESSION_KICK;

		if ( bci.event == BCE_BREAKDOWN_TUROVER && player->GetState()->GetTimeInPossession() < MIN_TIME_IN_POSSESSION_TURNOVER )
			can_kick_since_freeball = false;
	}

	RUGameState *gamestate = game->GetGameState();
	bool has_ball = gamestate->GetBallHolder() == player;
	//bool is_intercepting = player->GetActionManager()->IsActionRunning( ACTION_INTERCEPT );

	if ( can_kick_since_freeball
		&& minimum_kick_time_passed
		&& !kick_interface->IsKickActive()
		&& (has_ball)
		&& (human_player->GetRugbyCharacter()->GetActionManager()->CanUseAction(ACTION_KICK))
		&& !human_player->GetRugbyCharacter()->GetActionManager()->HFIsLocked( HF_KICK )
		&& ( gamestate->IsGameInStandardPlay() ) )	//|| gamestate->GetState() == GAME_STATE_PENALTY_KICK_FOR_TOUCH ) )
	{
		// check chip
		if (has_ball && human_player->GetOnDuration(ERugbyGameAction::KICK_CHIP) > AUTOKICK_BUTTON_TIME) 
		{
			if (player->GetPrevRole() == RURoleScrumHalfBack::RTTGetStaticType() || player->GetPrevRole() == RURoleMaulHalfback::RTTGetStaticType()) //#RC4-3664: If we were in scrum/maul half before we became ball holder, do a box kick instead of long punt.
			{
				kick_interface->StartKick(player, KICKTYPE_BOXKICK);
			}
			else
			{
			kick_interface->StartKick(player, KICKTYPE_UPANDUNDER);
			}

			human_player->ResetKickActionTimer();
			return;
		}
		else if (human_player->IsReleased(ERugbyGameAction::KICK_CHIP))
		{
			RUStrategyPos pos;
			KickType new_type = game->GetStrategyHelper()->FindQuickKickPos(KICKTYPE_CHIPKICK, player, pos);
			FVector target_pos(pos.x, 0.0f, pos.z);

			player->GetRole()->StartActionKickToPosition(new_type, target_pos, true);
			human_player->ResetKickActionTimer();
			return;
		}

		// check punt
		if (has_ball && human_player->GetOnDuration(ERugbyGameAction::KICK_PUNT) > AUTOKICK_BUTTON_TIME)
		{
			kick_interface->StartKick(player, KICKTYPE_LONGPUNT);
			human_player->ResetKickActionTimer();
			return;
		}
		else if (human_player->IsReleased(ERugbyGameAction::KICK_PUNT))
		{
			RUStrategyPos pos;
			KickType new_type = game->GetStrategyHelper()->FindQuickKickPos(KICKTYPE_LONGPUNT, player, pos);
			FVector target_pos(pos.x, 0.0f, pos.z);

			player->GetRole()->StartActionKickToPosition(new_type, target_pos, true);
			human_player->ResetKickActionTimer();
			return;
		}

		// check drop kick
		if (has_ball && human_player->GetOnDuration(ERugbyGameAction::KICK_FIELD) > AUTOKICK_BUTTON_TIME)
		{
			kick_interface->StartKick(player, KICKTYPE_DROPGOAL);
			human_player->ResetKickActionTimer();
			return;
		}
		else if (human_player->IsReleased(ERugbyGameAction::KICK_FIELD))
		{
			RUStrategyPos pos;
			KickType new_type = game->GetStrategyHelper()->FindQuickKickPos(KICKTYPE_DROPGOAL, player, pos);
			FVector target_pos(pos.x, 0.0f, pos.z);

			player->GetRole()->StartActionKickToPosition(new_type, target_pos, true);
			human_player->ResetKickActionTimer();
			return;
		}

		// check grubber
		if (has_ball && human_player->GetOnDuration(ERugbyGameAction::KICK_GRUBBER) > AUTOKICK_BUTTON_TIME)
		{
			kick_interface->StartKick(player, KICKTYPE_GRUBBERKICK);
			human_player->ResetKickActionTimer();
			return;
		}
		else if (human_player->IsReleased(ERugbyGameAction::KICK_GRUBBER))
		{
			RUStrategyPos pos;
			KickType new_type = game->GetStrategyHelper()->FindQuickKickPos(KICKTYPE_GRUBBERKICK, player, pos);
			FVector target_pos(pos.x, 0.0f, pos.z);

			player->GetRole()->StartActionKickToPosition(new_type, target_pos, true);
			human_player->ResetKickActionTimer();
			return;
		}
	}
}

void SSInputManager::UpdateLooseBall(SSHumanPlayer* human_player)
{
	MABASSERT(human_player);
	ARugbyCharacter* player = human_player->GetRugbyCharacter();
	RUGameState* game_state = game->GetGameState();

	if (game_state->GetBallHolder() == NULL
		&& game_state->IsGameInStandardPlay()
		&& game->GetGameGTB()->IsActive())
	{
		if (player->GetRole() && player->GetRole()->RTTGetType() == RURoleGetTheBall::RTTGetStaticType())
		{
			RURoleGetTheBall* gtb_role = player->GetRole< RURoleGetTheBall >();
			if (gtb_role->IsInForceActionRange())
			{
				if (human_player->IsPressed(ERugbyGameAction::LB_DIVE))
					gtb_role->ForceAction(GTB_ACTION::DIVE);
				else if (human_player->IsPressed(ERugbyGameAction::LB_KICK))
					gtb_role->ForceAction(GTB_ACTION::KICK);
				else if (human_player->IsPressed(ERugbyGameAction::LB_MARK))
					gtb_role->ForceAction(GTB_ACTION::HIGHBALL_JUMP);
			}
		}
		else if (player->GetRole() && player->GetRole()->RTTGetType() == RURoleSetplay::RTTGetStaticType())
		{
			if (human_player->IsPressed(ERugbyGameAction::LB_MARK))
				player->GetActionManager()->GetAction<RUActionGetTheBall>()->ForceAction(GTB_ACTION::HIGHBALL_JUMP);
		}
	}

//	MABUNUSED(human_player);
//
//	const float LOOSE_BALL_GROUND_DISTANCE = 7.0f;
//	const float LOOSE_BALL_AIR_DISTANCE = 10.0f;
//
//	ARugbyCharacter* player = human_player->GetPlayer();
//	//bool action_running = player->GetActionManager()->IsActionInUse( ACTION_GET_TO_BALL );
//	bool ball_on_full = game->GetBall()->IsOnTheFull();
//
//	RUGameState *game_state = game->GetGameState();
//	SSRole* role = player->GetRole();
//
//	if (game_state->GetBallHolder() == NULL
//		&& game_state->IsGameInStandardPlay()
//		&& game->GetStrategyHelper()->IsBallReallyFree()
//		&& !player->GetActionManager()->HFIsLocked(HF_LOOSEBALL)
//		&& ((ball_on_full && game->GetSpatialHelper()->GetPlayerToBallDistance(player) < LOOSE_BALL_AIR_DISTANCE)
//		|| game->GetSpatialHelper()->GetPlayerToBallDistance(player) < LOOSE_BALL_GROUND_DISTANCE)
//		&& role != NULL )
//	{
//#if PLATFORM_WII
//		bool using_basic = SIFApplication::GetApplication()->GetWiiControllerHelper()->IsBasicGameConfigActive(human_player->GetControllerIndex());
//#else
//		//bool using_basic = false;
//#endif
//
//		FVector input_vector = human_player->GetInputVector();
//		// when input is close start auto-pilot
//		if ( input_vector.Magnitude() > MOVE_MAXTARGDIST / 3.0f &&
//			 player->GetActionManager()->CanUseAction(ACTION_LOOSE_BALL_JUMPCATCH) &&
//			 player->GetActionManager()->CanUseAction(ACTION_LOOSE_BALL_PICKUP) )
//		{
//			RUPlayerMovement* movement = player->GetMovement();
//			float input_angle = SSMath::CalculateAngle( input_vector );
//			float ball_angle = SSMath::CalculateAngle( game->GetBall()->GetCurrentPosition() - movement->GetCurrentPosition() );
//			float delta_angle_ball = MabMath::Fabs(MabMath::AngleDelta( input_angle, ball_angle ));
//			if( delta_angle_ball < BREAKOUT_ANGLE )
//			{
//				if(	player->GetActionManager()->CanUseAction(ACTION_LOOSE_BALL_JUMPCATCH) )
//				{
//					player->GetRole()->StartActionJumpCatch();
//					if(!player->GetActionManager()->IsActionRunning(ACTION_LOOSE_BALL_JUMPCATCH))
//						player->GetRole()->StartActionGroundPickup();
//
//					//game->GetGameGTB()->RegisterHumanAttempt( human_player );
//				}
//			}
//		}
//
//		// Check if free ball input has been pressed
//		if (human_player->IsPressed(RU_LB_DIVE))
//		{
//			if( !ball_on_full )
//			{
//				//human_player->GetCachedRoleOption().Create(ROPT_NONBALLHOLDER_FBK , 0, player );
//				//free_ball_action_pressed = true;
//
//				if ( !player->GetActionManager()->IsActionRunning( ACTION_LOOSE_BALL_DIVE ) &&
//					player->GetActionManager()->CanUseAction ( ACTION_LOOSE_BALL_DIVE ) )
//					role->StartActionDive();
//			}
//			// We reset this, so if a human is is mashing free ball controls and then gets the ball, they don't immediately go into kick
//			human_player->ResetKickActionTimer();
//		}
//		else if (human_player->IsPressed(RU_LB_KICK))
//		{
///*			human_player->GetCachedRoleOption().Create( ROPT_NONBALLHOLDER_DIVE, 0, player );
//			free_ball_action_pressed = true;	*/
//
//			if( !ball_on_full )
//			{
//				if ( !player->GetActionManager()->IsActionRunning( ACTION_LOOSE_BALL_KICK ) &&
//					player->GetActionManager()->CanUseAction ( ACTION_LOOSE_BALL_KICK ) )
//						player->GetRole()->StartActionFreeBallKick();
//			}
//			// We reset this, so if a human is is mashing free ball controls and then gets the ball, they don't immediately go into kick
//			human_player->ResetKickActionTimer();
//		}
//	}
}

bool SSInputManager::UpdateSelection( SSHumanPlayer* human_player )
{
	const float CHANGE_PLAYER_TIME = 0.1f;

	ARugbyCharacter* player = human_player->GetRugbyCharacter();
	if (player != nullptr && (player == game->GetGameState()->GetBallHolder() || player->GetActionManager()->HFIsLocked(HF_CHANGE_PLAYER)))
		return false;

	SET_CHANGEPLAYER_SECTION( game, "CP-BEST" );
	if ( human_player->IsPressed(ERugbyGameAction::CHANGE_PLAYER) )
	{
		human_player->AssignBestPlayer(game);
	}
	SET_CHANGEPLAYER_SECTION( game, NULL );

	if( human_player->IsOn( ERugbyGameAction::CHANGE_PLAYER_LEFT ) && human_player->IsOn( ERugbyGameAction::CHANGE_PLAYER_RIGHT ) )
	{
		SET_CHANGEPLAYER_SECTION( game, "CP-FULLBCK" );
		human_player->AssignFullbackPlayer(game);
		human_player->SetInputIgnored(ERugbyGameAction::CHANGE_PLAYER_LEFT);
		human_player->SetInputIgnored(ERugbyGameAction::CHANGE_PLAYER_RIGHT);
		SET_CHANGEPLAYER_SECTION( game, NULL );
	}
	else if (player != nullptr)
	{
		bool cp_left_on_long_enough = human_player->IsOn(ERugbyGameAction::CHANGE_PLAYER_LEFT) && human_player->GetOnDuration(ERugbyGameAction::CHANGE_PLAYER_LEFT) > CHANGE_PLAYER_TIME;
		bool cp_right_on_long_enough = human_player->IsOn(ERugbyGameAction::CHANGE_PLAYER_RIGHT) && human_player->GetOnDuration(ERugbyGameAction::CHANGE_PLAYER_RIGHT) > CHANGE_PLAYER_TIME;

		if (cp_left_on_long_enough || human_player->IsReleased(ERugbyGameAction::CHANGE_PLAYER_LEFT))
		{
			SET_CHANGEPLAYER_SECTION(game, "CP-LEFT");
			human_player->AssignNextPlayer(ERugbyPlayDirection::NORTH);
			human_player->SetInputIgnored(ERugbyGameAction::CHANGE_PLAYER_LEFT);
			SET_CHANGEPLAYER_SECTION(game, NULL);
		}
		else if (cp_right_on_long_enough || human_player->IsReleased(ERugbyGameAction::CHANGE_PLAYER_RIGHT))
		{
			SET_CHANGEPLAYER_SECTION(game, "CP-RIGHT");
			human_player->AssignNextPlayer(ERugbyPlayDirection::SOUTH);
			human_player->SetInputIgnored(ERugbyGameAction::CHANGE_PLAYER_RIGHT);
			SET_CHANGEPLAYER_SECTION(game, NULL);
		}
	}

	return false;
}

void SSInputManager::UpdateDefenceLine(SSHumanPlayer *human_player)
{
	MABUNUSED(human_player);

#if PLATFORM_WII
	if( ( !game->GetStrategyHelper()->GameIsInStandardPlay() && game->GetState() != GAME_STATE_SCRUM && game->GetState() != GAME_STATE_PENALTY_TAP_RESTART ) || game->IsFrozen() )
		return;

	if (user_strategy_handler->IsDisplaying( human_player->GetTeam() ) )
		return;

	bool left = human_player->IsPressed(RL3_LEFT);
	bool right = human_player->IsPressed(RL3_RIGHT);
	bool up = human_player->IsOn(RL3_UP);
	bool down = human_player->IsOn(RL3_DOWN);
	RL3Team* team = human_player->GetTeam();
	RL3TeamLayout* layout = team ? team->GetTeamLayout() : nullptr;

	if (!SIFApplication::GetApplication()->GetWiiControllerHelper()->IsNunchukGameConfigActive(human_player->GetControllerIndex())
		&& !SIFApplication::GetApplication()->GetWiiControllerHelper()->IsGamecubeGameConfigActive(human_player->GetControllerIndex()))
		return;

	if (!hud_manager || !layout)
	{
		return;
	}

	// Left and right push the defensive hints.
	// NOTE: This is reversed because when the player presses LEFT, we want the line to go RIGHT as the controls are inverted
	//       due to the defense playing down the field
	if (left)
	{
		layout->SetUserDefensiveHint(RL3TeamLayout::DEFENSIVE_HINT_RIGHT);
		hud_manager->DisplayDefensiveLine(human_player->GetControllerIndex(), RL3TeamLayout::DEFENSIVE_HINT_RIGHT);		
	}
	else if (right)
	{
		layout->SetUserDefensiveHint(RL3TeamLayout::DEFENSIVE_HINT_LEFT);
		hud_manager->DisplayDefensiveLine(human_player->GetControllerIndex(), RL3TeamLayout::DEFENSIVE_HINT_LEFT);
	}

	// Up and down expand and contract the line
	if (up)
	{
		layout->SetUserDefensiveHint(RL3TeamLayout::DEFENSIVE_HINT_NONE);
		hud_manager->DisplayDefensiveLine(human_player->GetControllerIndex(), RL3TeamLayout::DEFENSIVE_HINT_NONE);
	}
	else if (down)
	{
		layout->SetUserDefensiveHint(RL3TeamLayout::DEFENSIVE_HINT_CENTER);
		hud_manager->DisplayDefensiveLine(human_player->GetControllerIndex(), RL3TeamLayout::DEFENSIVE_HINT_CENTER);
	}

	/*bool heavy_tackle = human_player->IsPressed(RL3_TACKLE_HEAVY);
	bool tackle = human_player->IsPressed(RL3_TACKLE_STANDARD) || heavy_tackle;
	if (tackle)
	{
		ARugbyCharacter* player = human_player->GetLinePlayer();
		if (player->GetRole())
		{
			MABASSERT(player->GetRole()->RTTSubClassOf(RL3RoleBaseDefend::RTTGetStaticType()));
			RL3RoleBaseDefend* role = MabCast<RL3RoleBaseDefend>(player->GetRole());
			if (role)
				role->Charge(heavy_tackle);
		}
	}

	// Update the closest defending player
	MabVector<ARugbyCharacter*> players = team->GetPlayers();
	std::sort(players.begin(), players.end(), ClosureSort(team->GetGame()));

	ARugbyCharacter* controlled_player = human_player->GetLinePlayer();
	ARugbyCharacter* best_player = NULL;
	for (size_t i = 0; i < players.size(); i++)
	{
		if (players[i]->GetRole() && players[i]->GetRole()->RTTSubClassOf(RL3RoleBaseDefend::RTTGetStaticType()))
		{
			best_player = players[i];
			break;
		}
	}

	// If we a new best player,
	if (best_player && best_player != controlled_player)
	{
		controlled_player = best_player;

		MABASSERT(controlled_player != NULL);
		human_player->SetLinePlayer(controlled_player);
	}*/

#endif
}

void SSInputManager::UpdateProCameraLockBall( SSHumanPlayer* human_player )
{
	
	// Dont even try this without a human player silly.
	if(!human_player)
		return;

	// Use this later on -> RU_LOCK_ONTO_BALL
	switch (game->GetCameraManager()->GetLockBallMode())
	{
	case PCBL_NONE:
		{
			game->GetCameraManager()->SetLookAtBall(false);
			break;
		}
	case PCBL_BUTTON_HOLD:
		{
			// Check if we're holding on to the lock ball button
			if ( human_player->IsOn(ERugbyGameAction::LOCK_ONTO_BALL) )
			{
				MABLOGDEBUG("Look at the ball!");
				game->GetCameraManager()->SetLookAtBall(true);
			}
			// If we're not, we dont want to lock onto the ball
			else
			{
				game->GetCameraManager()->SetLookAtBall(false);
			}
			break;
		}
		// Check if we press it
	case PCBL_BUTTON_TOGGLE:
		{
			if ( human_player->IsPressed(ERugbyGameAction::LOCK_ONTO_BALL ) )
			{
				// Invert the option
				game->GetCameraManager()->SetLookAtBall(!game->GetCameraManager()->GetLookAtBall());
			}
			break;
		}
		// Always. Do it always.
	case PCBL_ALWAYS:
		{
			game->GetCameraManager()->SetLookAtBall(true);
			break;
		}
	default:
		{
			break;
		}
	}

}

void SSInputManager::UpdateCamera( SSHumanPlayer* human_player )
{
	MABUNUSED(human_player);

	//if ( human_player->IsPressed( RL3_SWITCH_WIDE_CAMERA ) )
	//{
	//	// If PTB is running, switch to the wide camera
	//	if (game->GetAttackingTeam()->GetAggregateStrategyManager()->GetAggregateStrategy(AGG_PLAYTHEBALL_STRATEGY)->IsRunning() || game->GetState() == GAME_STATE_SCRUM )
	//	{
	//		game->GetCameraManager()->SwitchWidePTBCamera();
	//	}

	//	if( game->GetState() == GAME_STATE_KICK_OFF || game->GetState() == GAME_STATE_CENTER_DROP_OUT )
	//	{
	//		game->GetCameraManager()->SwitchWideKickRestartCamera();
	//	}

	//	if( game->GetState() == GAME_STATE_PENALTY )
	//	{
	//		game->GetCameraManager()->SwitchWidePenaltyDecideCamera();
	//	}
	//}
}

#if PLATFORM_WII

bool SSInputManager::CanPerformGesture( int controller_index )
{
	// If we're not in game, return true
	if ( SIFApplication::GetApplication()->GetFlowControlManager()->GetActiveNode()->GetName() != SIF_GAME_FLOWNAME )
	{
		return true;
	}

	if( controller_index >= 0 && controller_index <= 3 )
	{
		// if we're a wiimote, check the timer isn't still counting down
		return ( gesture_timers[ controller_index ] <= 0.0f );
	}
	// if we're not a wiimote, we can always perform actions
	return true;
}

void SSInputManager::SetGesturePerformed( int controller_index )
{
	if( controller_index >= 0 && controller_index <= 3 )
	{
		gesture_timers[ controller_index ] = MIN_GESTURE_TIME;
	}
}

#endif

void SSInputManager::UpdateStrategyControls( SSHumanPlayer* human_player )
{
	bool alt_on = human_player->IsOn(ERugbyGameAction::STRATEGY_ALT1);
	EHumanPlayerSlot human_player_idx = human_player->GetPlayerSlot();
	RUTeam* team = human_player->GetTeam();
	RUTeamStrategy& team_strategy = team->GetStrategy();
	RUHUDUpdaterUserStrategy* hud_updater = game->GetHUDUpdaterUserStrategy( team->GetSide() );
	bool processed = true;

	/// Check for double tap of alt for a reset
	if ( human_player->IsPressed( ERugbyGameAction::STRATEGY_ALT1 ) )
	{
		const static float MAX_DOUBLE_TAP_TIME = 0.3f;
		float time_since_last_tap = human_player->GetTimeSinceLastStrategyResetTap().ToSeconds();
		if ( time_since_last_tap < MAX_DOUBLE_TAP_TIME )
		{
			human_player->SetInputIgnored( ERugbyGameAction::STRATEGY_ALT1 );
			team_strategy.ResetAllOptions();
			hud_updater->Show( RUHUDUpdaterUserStrategy::OT_HIDE, EHumanPlayerSlot::FIRST_HUMAN );
		}

		human_player->ResetLastStrategyResetTapTimer();
	}

	// Primary
	if ( human_player->IsOn(ERugbyGameAction::STRATEGY_UP) && !alt_on )
	{
		// Line depth cycle
		if ( !hud_updater->Show( RUHUDUpdaterUserStrategy::OT_LINE_DEPTH, human_player_idx ) )
			team_strategy.CycleLineDepthOption();
	}
	else if ( human_player->IsOn(ERugbyGameAction::STRATEGY_DOWN) && !alt_on ) {
		// Spacing cycle
		if ( !hud_updater->Show( RUHUDUpdaterUserStrategy::OT_LINE_SPACING, human_player_idx ) )
			team_strategy.CycleLineSpacingOption();
	}
	else if ( human_player->IsReleased(ERugbyGameAction::STRATEGY_LEFT) && !alt_on ) {
		// Send runner
		if (team_strategy.IsBreakdownStrategyAvailable())
		{
			hud_updater->Show(RUHUDUpdaterUserStrategy::OT_SEND_RUNNER_LEFT, human_player_idx);
			team_strategy.SendRunnerLeft();
		}
	}
	else if ( human_player->IsReleased(ERugbyGameAction::STRATEGY_RIGHT) && !alt_on ) {
		// Send runner
		if (team_strategy.IsBreakdownStrategyAvailable())
		{
			hud_updater->Show(RUHUDUpdaterUserStrategy::OT_SEND_RUNNER_RIGHT, human_player_idx);
			team_strategy.SendRunnerRight();
		}
	}

	// Secondary
	else if ( human_player->IsReleased(ERugbyGameAction::STRATEGY_UP) && alt_on ) {
		// Pod Cycle
		if ( !hud_updater->Show( RUHUDUpdaterUserStrategy::OT_POD, human_player_idx ) )
			team_strategy.CyclePodOption();
	}
	else if ( human_player->IsReleased(ERugbyGameAction::STRATEGY_DOWN) && alt_on ) {
		// Full back cycle
		if ( !hud_updater->Show( RUHUDUpdaterUserStrategy::OT_FULLBACK, human_player_idx ) )
			team_strategy.CycleFullbackOption();
	}
	else if ( human_player->IsOn(ERugbyGameAction::STRATEGY_LEFT) && alt_on ) {
		// Ruck Urgency
		if ( !hud_updater->Show( RUHUDUpdaterUserStrategy::OT_RUCK_URGENCY, human_player_idx ) )
			team_strategy.CycleRuckUrgencyOption();
	}
	else if ( human_player->IsReleased(ERugbyGameAction::STRATEGY_RIGHT) && alt_on ) {
		// Kick prepare
		if (team_strategy.IsBreakdownStrategyAvailable())
		{
			hud_updater->Show(RUHUDUpdaterUserStrategy::OT_PREPARE_KICK, human_player_idx);
			team_strategy.PrepareKick();
		}
	}
	else {
		processed = false;
	}

	/// Ensure that the user has to depress these actions again
	if ( processed )
	{
		human_player->SetInputIgnored( ERugbyGameAction::STRATEGY_UP );
		human_player->SetInputIgnored( ERugbyGameAction::STRATEGY_DOWN );
		human_player->SetInputIgnored( ERugbyGameAction::STRATEGY_LEFT );
		human_player->SetInputIgnored( ERugbyGameAction::STRATEGY_RIGHT );
	}
}

void SSInputManager::DebounceAllActionsForAllPlayers()
{
	for (SSHumanPlayer* human : game->GetHumanPlayers())
	{
		human->SetAllInputIgnored();
	}
}

bool SSInputManager::WasHumanExpectingBall( SSHumanPlayer* human_player ) const
{
	/// This is all meant to prevent accidental actions while button spamming when the ball goes free
	/// We cater for 2 concepts
	///   Time we have been in possession before we can release ball
	///   Time since ball we free before we can release ball
	/// In 2 circumstances
	///   When possession loss is intentional
	///   When possession loss is not intentional

	ARugbyCharacter* player = human_player->GetRugbyCharacter();
	if ( game->GetGameState()->GetBallHolder() && game->GetGameState()->GetBallHolder() == player )
	{
		RUStrategyHelper* sh = game->GetStrategyHelper();
		const BallFreeInfo& bfi = sh->GetLastBallFreeInfo();

		bool was_successful_intercept = bfi.event == BFE_PASS && bfi.event_success && bfi.last_player->GetAttributes()->GetTeam() != player->GetAttributes()->GetTeam();

		if ( bfi.WasBallLoose() || was_successful_intercept )
		{
			const static float MIN_TIME_FREE_TO_EXPECT = 0.8f;
			float time_since_ball_was_free = game->GetSimTime()->GetAbsoluteTime().ToSeconds() - bfi.abs_game_time;
			bool was_expecting_the_ball = (bfi.last_human == human_player && bfi.event != BFE_FUMBLE && bfi.event != BFE_KNOCKON) || (time_since_ball_was_free > MIN_TIME_FREE_TO_EXPECT);

			return was_expecting_the_ball;
		}
	}

	return true;
}
