/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Roles/Competitors/RURoleShootForGoal.h"

#include "Match/Ball/SSBall.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerLookAt.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/RUPlayerSound.h"
#include "Match/Components/RUPlayerState.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Character/RugbyPlayerController.h"
#include "Match/HUD/RU3DHUDManager.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUInputKickInterface.h"
#include "Match/RugbyUnion/RUKickHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSGameTimer.h"
#include "Match/SSMath.h"
#include "Match/SSSpatialHelper.h"
#include "Utility/RURandomNumberGenerator.h"
#include "Match/HUD/RU3DHUDManager.h"
#include "Match/HUD/Marking/RUConversionTee.h"
#include "Match/HUD/Marking/RUTeeArrows.h"
#include "Match/HUD/Marking/RUWindIndicator.h"
#include "Match/Audio/SIFFMODUtil.h"
//#rc3_legacy_include #include <NMMabAnimationEvents.h>
//#rc3_legacy_include #include <NMMabAnimationNetwork.h>
//#rc3_legacy_include #include <Network.h>
//#rc3_legacy_include #include <NetworkDef.h>
//#rc3_legacy_include #include <NodeDef.h>

#include "Character/RugbyPlayerController.h"
#include "Character/RugbyCharacterAnimInstance.h"
#include "RugbyGameInstance.h"

#include "Match/Components/SSHumanPlayer.h"

// the degree to which poor accuracy affects power - ie, the maximum proportion that power is reduced by a terrible accuracy
#define POWER_ACCURACY_EFFECT 0.8f

// how far to the left/right the kick will go if the player has terrible accuracy (in degrees)
#define BAD_ACCURACY_RANGE 30.0f

//nodes names for kick off animation sequence.
static const char* KICK_OFF_SETUP_ANIM_REQ	= "quick_penalty_kick";
static const char* RIGHT_KICK_OFF_ANIM_REQ	= "penalty_kick_kick";
static const char* CONVERSION_R7_KICK		= "kickoff";
static const char* SFG_RIGHT_KICK_ANIM		= "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|penalty_kick";
static const char* SFG_LEFT_KICK_ANIM		= "player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_leftfoot|penalty_kick";
static const char* BALL_NODE_NAME			= "ball";

static const float INTERPERLATE_RATE		= 0.2f;
static const float TEE_MOVEMENT_MODIFIER	= 1.0f;
static const float BALL_PLACE_TRANSPARENCY	= 0.85f;

static const float Z_INFIELD_SETUP_STEP		= 5.0f;

static const int NUM_CONTEXTUAL_2D_DATA		= 2;

static const float WALK_TIME_OUT			= 4.0f; // 4 secs, takes around 1.28 seconds to walk to our spot.
static const float QUICK_CONVERSION_TIME_OUT= 2.0f; // 2 secs
static const float BUTTON_LOCK_TIME			= 0.75f;

#if defined BUILD_DEBUG 
static const char* KFP_STATE_STRINGS[] =
{
	"KFP_IDLE",
	"KFP_BALL_PLACE",
	"KFP_WALKTO",
	"KFP_SETUP",
	"KFP_AIM",
	"KFP_AIM_ACCURACY",
	"KFP_RUNUP",
	"KFP_POWER",
	"KFP_BALLKICKED",
	"KFP_COMPLETE"
};
#endif

static const long DEBUG_TEXT_KEY = 120000;

MABRUNTIMETYPE_IMP1( RURoleShootForGoal, SSRole )

/// Constructs a new aimed kick role.
RURoleShootForGoal::RURoleShootForGoal( SIFGameWorld* game )
:SSRole(game)
, kfp_state(KFP_IDLE)
, max_strike_position(0.0f, 0.0f, 0.0f)
, strike_position(0.0f, 0.0f, 0.0f)
, strike_target(0.0f, 0.0f, 0.0f)
, strike_angle(0.0f)
, kick_anim_started(false)
, first_animation_done(false)
, kick_timer()
, walk_timer()
, quick_conversion_timer()
, commentary_time_wasting_timer()
, target_angle(0.0f)
, kick_distance_to_goals(0.0f)
, shoot_type(SHOOTFORGOAL_NONE)
, kick_accuracy(0.0f)
, kick_power(0.0f)
, handness_index(0)
, ball_kicked(false)
, ball_tint( MabColour::White )
, ball_place_z_vel( 0.0f )
, kicker_offset( FVector::ZeroVector )
, button_lock()
, ball_slide_sound(NULL)
, is_place_kick(true)
, runup_animation_triggered(false)
, took_quick_conversion(false)
, done_walkto_warp(false)
{
	kick_timer.Initialise( game->GetSimTime(), KICK_TIME_OUT );
	walk_timer.Initialise( game->GetSimTime(), WALK_TIME_OUT );
	quick_conversion_timer.Initialise( game->GetSimTime(), QUICK_CONVERSION_TIME_OUT );
	commentary_time_wasting_timer.Initialise( game->GetSimTime(), COMMENTARY_KICK_SLOW_TIME);

	button_lock.Initialise( game->GetSimTime(), BUTTON_LOCK_TIME );
	button_lock.SetEnabled( false );
}

/// Destroys the role.
RURoleShootForGoal::~RURoleShootForGoal()
{
}

/// Enter this role with the specified player.
void RURoleShootForGoal::Enter( ARugbyCharacter* player )
{
	SSRole::Enter( player );

	if(player->GetState()->IsInCutScene())
		return;

	runup_animation_triggered = false;

	// If we're playing an R15 game, then set the conversion logic up for place kicks.
	is_place_kick = true; // Nick WWS &s to Womens // m_pGame->GetGameSettings().game_settings.GameModeIsR13();

	shoot_type = SHOOTFORGOAL_NONE;
	kfp_state = KFP_IDLE;
	done_walkto_warp = false;

	ball_kicked = false;
	kick_accuracy = 0.0f;
	kick_power = 0.0f;
	strike_angle = 0.0f;

	player->GetMovement()->StopAllMovement();
	player->GetMovement()->ResetLean();
	m_pGame->GetGameState()->SetBallHolder( NULL );

	m_lock_manager.HFLockAll();
	m_lock_manager.HFUnlock( HF_KICK );

	// If the team has human players...
	SET_CHANGEPLAYER_SECTION( m_pGame, "SHOOTFG" );

	SSHumanPlayer* shoot_for_goal_human = m_pGame->GetGameState()->GetShootForGoalHuman();
	// Remove the existing human from this player
	if ( player->GetHumanPlayer() )
		player->GetHumanPlayer()->SetRugbyCharacter(nullptr);

	if(SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() &&
		SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro() &&
		SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer() != NULL)
	{
		// Is the kicker our pro player?
		if(SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(player))
		{
			if (shoot_for_goal_human != NULL && shoot_for_goal_human->GetTeam() != nullptr && shoot_for_goal_human->GetTeam() == player->GetAttributes()->GetTeam())
			{
				shoot_for_goal_human->SetRugbyCharacter(player);
			}
			else
			{
				player->GetAttributes()->GetTeam()->GetHumanSelector().AssignHumanToPlayer(player);
			}
		}
	}
	else
	{
		// Set up the human player for the kicker.
		if (shoot_for_goal_human != NULL && shoot_for_goal_human->GetTeam() != nullptr && shoot_for_goal_human->GetTeam() == player->GetAttributes()->GetTeam())
		{
			shoot_for_goal_human->SetRugbyCharacter(player);
		}
		else
		{
			player->GetAttributes()->GetTeam()->GetHumanSelector().AssignHumanToPlayer(player);
		}
	}

	// Clear all input
	SSHumanPlayer* human_player = player->GetHumanPlayer();
	if ( human_player != NULL )
	{
		MABLOGDEBUG("We have a human player for the Shoot for Goal Role");
		human_player->ResetActions();
	}
	else
	{
		MABLOGDEBUG("We do NOT have a human player for the Shoot for Goal Role");
	}

	SET_CHANGEPLAYER_SECTION( m_pGame, NULL );

	first_animation_done = false;

	handness_index = player->GetAttributes()->GetDBPlayer()->prefered_foot == 1u ? 1.0f : -1.0f;
	// Don't support more than 2 footed players - you never know.
	MABASSERT(handness_index == -1.0f || handness_index == 1.0f);

	//#rc3_legacy
	//NMMabAnimationNetwork* network = player->GetComponent<NMMabAnimationNetwork>();
	//const char* anim_name = handness_index == 1.0f ? SFG_RIGHT_KICK_ANIM : SFG_LEFT_KICK_ANIM;
	//const NodeDef* node_def = network->GetNetworkDef()->findNodeDef( anim_name );
	//NMP::Vector3 contact_vector( NMP::Vector3::InitZero );
	//if(node_def != NULL)
	//{
	//	int node_id = node_def->index();
	//	network->GetNetwork()->generateContactVectors( &node_id, &contact_vector, NULL, 1, BALL_NODE_NAME, "Footsteps" );

	FVector contact_vector (FVector::ZeroVector);

	const FRugbyAnimationLibrary* pAnimLibrary = player->GetAnimation()->GetAnimationLibrary();

	if (pAnimLibrary && pAnimLibrary->GetRightPenaltyKickContact())
	{
		if (handness_index == 1.0f)
		{
			contact_vector = pAnimLibrary->GetRightPenaltyKickContact()->translation;
		}
		else
		{
			contact_vector = pAnimLibrary->GetLeftPenaltyKickContact()->translation;
		}
	}
	
	contact_vector *= -1.0f;
	
	kicker_offset.x = contact_vector.x;
	kicker_offset.z = contact_vector.z;

	//Register animation event
	player->GetMabAnimationEvent().Add( this, &RURoleShootForGoal::AnimationEvent );

	// Make sure we're ready to start the kick
	MABASSERT( kfp_state == KFP_IDLE );

	// figure out whether this is a conversion or penalty goal
	AutoSetShootType();

	// Make sure we disable button presses to stop premature kicking.
	button_lock.SetEnabled( true );

	/// Hide player info hud.
	if (m_pGame->GetHUDUpdater())
		m_pGame->GetHUDUpdater()->HideHUDInfo();

	TransitionToState( KFP_BALL_PLACE );

	// For non place kicks, give the ball to the player, and tell them to idle
	if(!is_place_kick)
	{
		m_pGame->GetGameState()->SetBallHolder( player );
		player->GetAnimation()->SetIdleGroup(ERugbyFormationIdleGroup::DROPOUT_KICKER);

		wwNETWORK_TRACE_JG("SetParticipationLevel {%4d} %s", __LINE__, __FILE__);
		// Hack, since participation level affects a players' idle radius, the player would occasionally not move to their waypoint
		player->GetState()->SetParticipationLevel(PL_MAX);
	}

	m_pGame->GetEvents()->kick_at_posts_ready( player, is_place_kick );

	// Notify the context that we can start a conversion kick now
	m_pGame->GetEvents()->quick_conversion_start( player, is_place_kick );

	kick_timer.SetEnabled( true );
	kick_timer.Reset();

	walk_timer.SetEnabled( true );
	walk_timer.Reset();

	quick_conversion_timer.SetEnabled( true );
	quick_conversion_timer.Reset();

	commentary_time_wasting_timer.SetEnabled(true);
	commentary_time_wasting_timer.Reset();


	//during the ball placement the wind indicator could be on the wrong side of the ball
	//this aims to resolve that by updating which direction it should be facing
	ERugbyPlayDirection play_dir = player->GetAttributes()->GetPlayDirection();
	m_pGame->Get3DHudManager()->GetWindIndicator()->SetPlayDir(play_dir);


	m_pGame->GetEvents()->team_assignments_changed.Add( this, &RURoleShootForGoal::OnTeamAssignmentsChanged );
}

/// Exit this role.
void RURoleShootForGoal::Exit(bool forced)
{
	took_quick_conversion = false;
	done_walkto_warp = false;
	kfp_state = KFP_COMPLETE;
	//MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "RURoleShootForGoal::Exit() on pid %d", player->GetHumanPlayer()->GetIndex() );
	if( m_lock_manager.UFIsLocked( UF_DOANIMGRAPH ) )
		//this stops the player from freezing on the last kick animation
		m_lock_manager.UFUnlock(UF_DOANIMGRAPH);

	m_lock_manager.UFUnlock(UF_DOMOTION);

	//Unregister animation event
	m_pPlayer->GetMabAnimationEvent().Remove( this, &RURoleShootForGoal::AnimationEvent );
	
	m_pGame->Get3DHudManager()->GetConversionTee()->SetVisible( false );
	m_pGame->Get3DHudManager()->GetTeeArrows()->SetVisible( false );

	if (m_pGame->GetHUDUpdater())
		m_pGame->GetHUDUpdater()->SetCountdownClockVisible(false);

	// set ball transparency
	m_pGame->GetBall()->SetTransparency( 1.0f );
	m_pGame->GetBall()->SetColourTint( MabColour::White );
	ball_place_z_vel = 0.0f;

	kick_timer.SetEnabled( false );
	walk_timer.SetEnabled( false );
	quick_conversion_timer.SetEnabled( false );
	commentary_time_wasting_timer.SetEnabled(false);

	if (bPendingRestore)
	{
		/// Now set their visibility
		RUTeam* team = m_pPlayer->GetAttributes()->GetTeam();
		m_pGame->SetPlayersVisible(true, team); //restore all players
		bPendingRestore = false;
	}

	if(!forced)		// During cleanup don't call SetPlayersVisible.
	{
		SetPlayersVisible( true, false );
	}


	// if human clear kick incase of early out
	SSHumanPlayer* human = m_pPlayer->GetHumanPlayer();
	if( human != NULL )
	{
		RUInputKickInterface* kick_interface = m_pGame->GetInputManager()->GetKickInterface();
		kick_interface->StopKick();
		SetupHelpText();
	}

	// To be sure no sounds a lost
	if(ball_slide_sound)
	{
		//#rc3_legacy 
		ball_slide_sound->stop(FMOD_STUDIO_STOP_IMMEDIATE);
		ball_slide_sound->release();
		ball_slide_sound = NULL;
	}

	m_pGame->GetEvents()->team_assignments_changed.Remove( this, &RURoleShootForGoal::OnTeamAssignmentsChanged );

	SSRole::Exit(forced);
}


/// Advance this role.
void RURoleShootForGoal::UpdateLogic( const MabTimeStep& game_time_step )
{
	//SIF_DEBUG_DRAW(SetText( DEBUG_TEXT_KEY, 200, 200, MabString(0, "State: %s", KFP_STATE_STRINGS[kfp_state]).c_str(), MabColour::White));

	switch ( kfp_state )
	{
		case KFP_IDLE:
			{
			}
		break;


		case KFP_BALL_PLACE:
			{
				CheckQuickConversion();
				SSHumanPlayer* human = m_pPlayer->GetHumanPlayer();
				RUPlayerSound* sound = m_pPlayer->GetSound();

				/// TYRONE : Conversion attack players have been moved so that they are relative to restart position
				/// so that they can be ina  good position to chase unsuccessful penalty shots or bounce off posts
				/// SO we need to not show them while we are moving the kick place position

				// We hide the players for place kicks, since we are moving the ball around
				if(is_place_kick)
					SetPlayersVisible( false, false );

				if ( human != NULL )
				{
					// check for selection
					// But only during place kicks, we don't want the player to skip during non place kicks
					if ( is_place_kick && human->IsPressed( ERugbyGameAction::STARTKICK_RESTART ) && button_lock.GetNumTimerEventsRaised() > 1 )
					{
						TransitionToState( KFP_WALKTO );

						if(ball_slide_sound)
						{
							//#rc3_legacy 
							ball_slide_sound->stop(FMOD_STUDIO_STOP_IMMEDIATE);
							ball_slide_sound->release();
							ball_slide_sound = NULL;
						}
						break;
					}

					// check for input and move the tee appropriately
					ERugbyPlayDirection play_dir = m_pPlayer->GetAttributes()->GetPlayDirection();
					float strike_pos_z = strike_position.z;

					FVector camera_forward_direction = human->GetCameraVector();
					camera_forward_direction.y = 0.0f;
					camera_forward_direction.Normalise();
					FVector camera_forward_basis = m_pGame->GetClosestBasisVector( camera_forward_direction );

					FVector ball_vec( 0,0, float(play_dir) );
					MabQuaternion camera_rot = MabQuaternion::Between( ball_vec, camera_forward_direction );
					FVector input_vec = human->GetInputVector();
					input_vec = camera_rot.Transform( input_vec );
					FVector ball_pos = m_pGame->GetBall()->GetCurrentPosition();

					if ( camera_forward_basis.x != 0.0f )
					{
						input_vec = FVector( input_vec.z, 0.0f, input_vec.x ) * -1.0f;

						if ( play_dir == ERugbyPlayDirection::NORTH && ball_pos.x < 0.0f )
							input_vec *= -1.0f;
						else if ( play_dir == ERugbyPlayDirection::SOUTH && ball_pos.x > 0.0f )
							input_vec *= -1.0f;
					}

					// Really don't care about moving the ball around if it's not a place kick
					if ( is_place_kick )
					{
						float move = 0.0f;
						if ( input_vec.Magnitude() > 0.0f )
						{
							move = input_vec.z;
							if(!ball_slide_sound)
								ball_slide_sound = sound->CreateSoundEffect("event:/hud/aim_kicks/place_ball_slide", false);
						}
						else
						{
							ball_place_z_vel = 0.0f;
							if(ball_slide_sound)
							{
								//#rc3_legacy 
								ball_slide_sound->stop(FMOD_STUDIO_STOP_IMMEDIATE);
								ball_slide_sound->release();
								ball_slide_sound = NULL;
							}
						}

						//// Debug Info
						//ball_pos.y = 0.2f;
						//FVector target_ang = ball_pos + input_vec;
						//SIF_DEBUG_DRAW(Set3DLine(200756, ball_pos, target_ang, MabColour::Blue, MabColour::Blue ));

						float t = INTERPERLATE_RATE * game_time_step.delta_time.ToSeconds();

						float current_vel = move * TEE_MOVEMENT_MODIFIER;
						ball_place_z_vel = MabMath::Lerp( ball_place_z_vel, current_vel, t );

						strike_pos_z += ball_place_z_vel * game_time_step.delta_time.ToSeconds();

						if ( play_dir == ERugbyPlayDirection::NORTH )
						{
							if ( strike_pos_z > max_strike_position.z )
							{
								strike_pos_z = max_strike_position.z;
								ball_place_z_vel = 0.0f;
							}
						}
						else
						{
							if ( strike_pos_z < max_strike_position.z )
							{
								strike_pos_z = max_strike_position.z;
								ball_place_z_vel = 0.0f;
							}
						}
					}

					// make sure distance isn't further than max kick + 5m
					FVector temp_strike_pos = strike_position;
					temp_strike_pos.z = strike_pos_z;

					FVector target = FVector( 0.0f, 0.0f, m_pGame->GetSpatialHelper()->GetFieldZPosition( play_dir, FIELD_OPP_TRY_LINE ) );
					float temp_kick_dist = ( temp_strike_pos - target ).ApproxMagnitude();

					float const MAX_KICK_DISTANCE = 55.0f;

					float max_kick_dist = (MAX_KICK_DISTANCE + Z_INFIELD_SETUP_STEP);
					if ( temp_kick_dist < max_kick_dist )
						strike_position = temp_strike_pos;

					
					m_pGame->Get3DHudManager()->GetTeeArrows()->SetPosition( strike_position );
					m_pGame->Get3DHudManager()->GetTeeArrows()->SetPlayerColour( m_pGame->Get3DHudManager()->GetPlayerColour( human->GetPlayerSlot() ) );
					SetKickingTee();
					
					m_pGame->Get3DHudManager()->GetWindIndicator()->SetPosition( strike_position );
					m_pGame->Get3DHudManager()->GetWindIndicator()->SetRotation( target_angle );

					MabMatrix rot = MabMatrix::RotMatrixY( target_angle );
					FVector standing_offset = strike_position;

					if(is_place_kick)
					{
						// During place kicks, the kicker is standing a little to the side of the strike position
						standing_offset = rot.TransformPos( kicker_offset );

						m_pPlayer->GetMovement()->SetCurrentPosition( strike_position + standing_offset );
						wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
						m_pPlayer->GetMovement()->SetTargetPosition( strike_position + standing_offset, true );
					}
					else
					{
						// For drop kicks (sevens) we'll just stand a few steps back
						FVector strikeVector = ( target - temp_strike_pos );
						strikeVector.Normalise();
						standing_offset = strike_position - strikeVector * 2.0f;

						//SIF_DEBUG_DRAW(Set3DLine(2003460, strike_position, strike_position + strikeVector, MabColour::Green, MabColour::Green ));
						//SIF_DEBUG_DRAW(Set3DLine(2003461, strike_position, standing_offset, MabColour::Red, MabColour::Red ));

						m_pPlayer->GetMovement()->SetCurrentPosition( standing_offset );
						wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
						m_pPlayer->GetMovement()->SetTargetPosition( strike_position, true );
					}

					m_pPlayer->GetMovement()->SetCurrentSpeed(0.0f);


					// measure dist between ball and target
					float kick_dist = (strike_position - target).ApproxMagnitude();

					// dist was hard coded to 45.
					float RED_LIMIT = 90.0f;

					float kick_dist_one_third = RED_LIMIT * 0.333f;
					float kick_dist_two_third = RED_LIMIT * 0.666f;

					if ( kick_dist > kick_dist_two_third )
					{
						float t = ( kick_dist - kick_dist_two_third ) / kick_dist_one_third;
						MabMath::ClampUpper(t, 1.0f);
						ball_tint = MabMath::Lerp( MabColour::Yellow, MabColour::Red, t );
					}
					else if ( kick_dist > kick_dist_one_third )
					{
						float t = ( kick_dist - kick_dist_one_third ) / kick_dist_one_third;
						ball_tint = MabMath::Lerp( MabColour::White, MabColour::Yellow, t );
					}
					else
						ball_tint = MabColour::White;

					// Tint the ball based on the distance we've moved it - but only in place kicks
					if(is_place_kick)
						m_pGame->GetBall()->SetColourTint( ball_tint );
					// We should have all the values set up now, so we can skip.
					else
					{
						TransitionToState( KFP_WALKTO );
					}
				}
				else
				{
					// Since we're not human, we still want to set the tee angle so that the camera looks at the posts.
					ERugbyPlayDirection play_dir = m_pPlayer->GetAttributes()->GetPlayDirection();
					FVector target( 0.0f, 0.0f, m_pGame->GetSpatialHelper()->GetFieldZPosition( play_dir, FIELD_OPP_TRY_LINE ) );

					FVector aim_vector = target - strike_position;
					target_angle = SSMath::CalculateAngle( aim_vector );

					m_pGame->GetInputManager()->GetKickInterface()->SetConversionTeeAngle( target_angle );


					TransitionToState( KFP_WALKTO );
				}
			}
		break;


		case KFP_WALKTO:
			{
				CheckQuickConversion();

				// Look at the target
				m_pPlayer->GetMovement()->SetFacingFlags( AFFLAG_FACETARGANGLE );
				m_pPlayer->GetMovement()->SetCurrentFacingAngle( target_angle );
				m_pPlayer->GetMovement()->SetTargetFacingAngle( target_angle );

				// Double check to see if we've reached our waypoint.
				// place kicks - will walk from strike_positions, to strike_position + offset
				// non place kicks - will walk from strike_position + offset to strike_position
				if ( m_pPlayer->GetMovement()->HasReachedWaypoint()
					// Catching an NMA here
					|| ( !is_place_kick && walk_timer.GetNumTimerEventsRaised() > 0 ))
				{
					if( !is_place_kick && walk_timer.GetNumTimerEventsRaised() > 0 )
					{
						MABBREAKMSG( "RURoleShootForGoal:: Catching an NMA, we've been standing still for too long?" );
					}

					RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
					//if (animation->IsAnimationAvailable (KICK_OFF_SETUP_ANIM_REQ))//#rc3_legacy_animation. Rewritten this to check for statemachine before calling

					ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
					if (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null)
					{											
						animation->SetVariable( animation->GetHandednessVariable(), handness_index );
						TransitionToState( KFP_SETUP );
						break;
					}
				}
			}
		break;

		/// Entry logic for the actually kicking after the preliminary stuff has been set up. We either transition to aiming for humans, or just the run up for AI.
		case KFP_SETUP:
			{
				SSHumanPlayer* human_player = m_pPlayer->GetHumanPlayer();
				if (human_player && !took_quick_conversion)
				{
					TransitionToState(KFP_AIM);
				}
				else
				{
					TransitionToState(KFP_RUNUP);
				}
			}
		break;

		/// Update loop for aiming, once we press the A button we will go to the Runup state to make our dude run toward the ball
		case KFP_AIM:
			{
				SSHumanPlayer* human = m_pPlayer->GetHumanPlayer();
				if ( human == NULL || took_quick_conversion )
				{
					break;
				}

				if( human->IsPressed( ERugbyGameAction::STARTKICK_RESTART ) )
				{
					TransitionToState( KFP_AIM_ACCURACY );
				}
			}
		break;

		// This handles how accurate we will be with the angle we eye balled
		case KFP_AIM_ACCURACY:
			{
				SSHumanPlayer* human = m_pPlayer->GetHumanPlayer();
				if ( human == NULL || took_quick_conversion )
				{
					break;
				}

				RUPlayerAnimation* animation = m_pPlayer->GetAnimation();

				// Draw a line to our target, sanity check
				//SIF_DEBUG_DRAW(Set3DLine(200759, strike_position, strike_target, MabColour::Green, MabColour::Red ));

				ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
				bool PlaceKickAnimationAvailable = false; //animation->IsAnimationAvailable( RIGHT_KICK_OFF_ANIM_REQ )

				if ( CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::KickLeftFoot ||
					 CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::KickRightFoot )
				{
					PlaceKickAnimationAvailable = true;
				}

				//if( human->IsPressed( ERugbyGameAction::STARTKICK_RESTART ) &&
					//((is_place_kick && animation->IsAnimationAvailable( RIGHT_KICK_OFF_ANIM_REQ )) ||	// Doing a place kick, and our kick off animation is ready //#rc3_legacy_animation. Rewritten this to check for statemachine before calling
					//(!is_place_kick && animation->IsAnimationAvailable( "kickoff" ))))				// Not doing a place kick, and our drop kick animation is ready //#rc3_legacy_animation. Rewritten this to check for statemachine before calling

				if ( human->IsPressed(ERugbyGameAction::STARTKICK_RESTART) &&
					((is_place_kick && PlaceKickAnimationAvailable) ||										// Doing a place kick, and our kick off animation is ready
					(!is_place_kick && (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null))))	// Not doing a place kick, and our drop kick animation is ready
				{
					RUInputKickInterface* kick_interface = m_pGame->GetInputManager()->GetKickInterface();
					strike_angle = kick_interface->GetKickAngle();

					float kick_accuracy_interface = kick_interface->GetConversionAccuracy();
					float kick_similarity = kick_interface->GetConversionSimilarity();

					float kick_accuracy_modifier = kick_accuracy_interface * kick_similarity;

					MABUNUSED(kick_accuracy_interface);
					MABUNUSED(kick_similarity);

					MABLOGDEBUG("Kick accuracy modifier is: %f", kick_accuracy_modifier);
					MABLOGDEBUG("Strike angle before kick angle accuracy: %f", strike_angle);
					// Use the parabola formula y = 2x^2 to give an accuracy effect of -2 to -2, so that the penalty of going away from the 50% mark too far is doubled
					// Fabs is used as on one of the value of x in the parabola equation to negate the sign so that we can still determine if the original accuracy is negative or positive
					float accuracy_value = 1.5 * kick_accuracy_modifier;// *MabMath::Fabs(kick_accuracy_modifier);
					float accuracy_effect =  -1.0f * MabMath::Deg2Rad( BAD_ACCURACY_RANGE * accuracy_value );
					MABLOGDEBUG("Accuracy Effect is: %f", accuracy_effect);
					strike_angle += accuracy_effect;
					MABLOGDEBUG("Strike angle after kick angle accuracy: %f", strike_angle);

					// Uncomment this to repeat this step
					//TransitionToState( KFP_AIM_ACCURACY );

#if USING_CLIENT_SIDE_MINIGAMES
					TransitionAfterSetAccuracy();
#else
					TransitionToState(KFP_RUNUP);
#endif
				}

			}
			break;

		/// Handle the run up logic. Probably dont actually need anything here anymore.
		case KFP_RUNUP:
			{
#if USING_CLIENT_SIDE_MINIGAMES

			// If we are a human doing the runup, we need to check we have kicked the ball. Quick conversions and non-humans will check in the power state.
			if (m_pGame->GetGameSettings().game_settings.network_game)
			{
				// If we have a human and we didn't do a quick conversion, transition to the power stage.
				if (!took_quick_conversion && m_pPlayer->GetHumanPlayer())
				{
					CheckBallKicked();
					break;
				}
			}
#endif
			TransitionToState(KFP_POWER);
			}
		break;

		/// Update loop for power.
		case KFP_POWER:
			{
				SSHumanPlayer* human = m_pPlayer->GetHumanPlayer();

				// If we're doing a sevens conversion kick, we haven't kicked in the animation yet
				// Play the animation now, and the animation event should kick in

				if ((!is_place_kick &&
					(!runup_animation_triggered && (m_pGame->GetInputManager()->GetKickInterface()->GetConversionPowerSet() || took_quick_conversion))) ||

					// CPU needs to run animation now
					(human == NULL))
				{					
					// Make sure we can trigger the anim					
					RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
					
					//if (animation->IsAnimationAvailable(CONVERSION_R7_KICK) && !runup_animation_triggered) //#rc3_legacy_animation. Rewritten this to check for statemachine before calling
					ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();					
					if ((CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null ) && !runup_animation_triggered )
					{
						m_pGame->GetEvents()->shoot_for_goal_complete(m_pPlayer);

						animation->PlayAnimation(CONVERSION_R7_KICK);

						runup_animation_triggered = true;
					}
				}

#if USING_CLIENT_SIDE_MINIGAMES
				// If we are a human doing the power setting, then once it is set we need to start the runup. Quick conversions and non-humans have already done it.
				if (m_pGame->GetGameSettings().game_settings.network_game)
				{
					if (((!runup_animation_triggered && (m_pGame->GetInputManager()->GetKickInterface()->GetConversionPowerSet() || took_quick_conversion))) && human)
					{
						TransitionToState(KFP_RUNUP);
						break;
					}
				}
	
#endif
				CheckBallKicked();
			}
		break;


		case KFP_BALLKICKED:
			{			
			}
		break;


		case KFP_COMPLETE:
			{			
			}
		break;


		default:
			{
				MABBREAKMSG("Trying to run logic for a state phase that has not yet been defined.");
			}
		break;
	}


	SSRole::UpdateLogic( game_time_step );


	// Timers dont show up in tutorials... bug? Its in the final release of RC2 as well...

	// Make a commentator yell at us for taking too long
	if(commentary_time_wasting_timer.GetNumTimerEventsRaised() > 0)
	{
		m_pGame->GetEvents()->commentary_kick_slow();
		commentary_time_wasting_timer.Acknowledge();
		commentary_time_wasting_timer.SetEnabled(false);
	}

	// Stop our quick conversion ability here...
	if ( quick_conversion_timer.GetNumTimerEventsRaised() > 0)
	{
		// Double check we're in the walk state still
		if (kfp_state == KFP_WALKTO)
		{
			m_pGame->GetEvents()->quick_conversion_stop(m_pPlayer, is_place_kick);
		}
	}

	// When we've taken too long to kick, change the state to the next state
	if ( kick_timer.GetNumTimerEventsRaised() > 0 )
	{
		// make the kick
		if ( kfp_state < KFP_WALKTO )
		{
			TransitionToState( KFP_WALKTO );
		}
		// Go to accuracy
		else if ( kfp_state < KFP_AIM_ACCURACY )
		{
			// Notify the context that we can no longer take a quick kick
			m_pGame->GetEvents()->quick_conversion_stop( m_pPlayer, is_place_kick );

			// Hide the conversion aim indicator
			m_pGame->GetInputManager()->GetKickInterface()->SetVisibilityConversionAimIndicator( false );
			TransitionToState( KFP_AIM_ACCURACY );
		}
		// Go to power
		else if ( kfp_state < KFP_RUNUP )
		{
			// play kick idle animation
			RUPlayerAnimation*	animation = m_pPlayer->GetAnimation();

			ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
			bool PlaceKickAnimationAvailable = false; //animation->IsAnimationAvailable( RIGHT_KICK_OFF_ANIM_REQ )

			if (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::KickLeftFoot ||
				CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::KickRightFoot)
			{
				PlaceKickAnimationAvailable = true;
			}
			
			//if ((is_place_kick && animation->IsAnimationAvailable( RIGHT_KICK_OFF_ANIM_REQ )) ||	// Doing a place kick, and our kick off animation is ready //#rc3_legacy_animation. Rewritten this to check for statemachine before calling
			//	(!is_place_kick && animation->IsAnimationAvailable( "kickoff" )))					// Not doing a place kick, and our drop kick animation is ready //#rc3_legacy_animation. Rewritten this to check for statemachine before calling
			if ((is_place_kick && PlaceKickAnimationAvailable) ||	// Doing a place kick, and our kick off animation is ready
				(!is_place_kick && CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null)) // Not doing a place kick, and our drop kick animation is ready
			{
				// If they time out we still need to store the strike angle
				RUInputKickInterface* kick_interface = m_pGame->GetInputManager()->GetKickInterface();
				kick_interface->CalculateConversionAccuracy(); // forces accuracy to stop, the same as when you pressed the action button
				strike_angle = kick_interface->GetKickAngle();

				float kick_accuracy_interface = kick_interface->GetConversionAccuracy();
				float kick_similarity = kick_interface->GetConversionSimilarity();


				MABLOGDEBUG("Ran out of time, finishing off our conversion angle with an similarity of: %0.4f, and an accuracy of %0.4f", kick_similarity, kick_accuracy_interface);

				float kick_accuracy_modifier = kick_accuracy_interface * kick_similarity;
				float accuracy_value = 2 * kick_accuracy_modifier * MabMath::Fabs( kick_accuracy_modifier );
				float accuracy_effect =  -1.0f * MabMath::Deg2Rad( BAD_ACCURACY_RANGE * accuracy_value );
				strike_angle += accuracy_effect;

				// Hide the conversion aim indicator
				m_pGame->GetInputManager()->GetKickInterface()->SetVisibilityConversionAimIndicator( false );

#if USING_CLIENT_SIDE_MINIGAMES
				TransitionAfterSetAccuracy();
#else
				TransitionToState(KFP_RUNUP);
#endif
			}
		}
	}


	//SETDEBUGMARKER(20089l, strike_position, MabColour::Blue);
}

/// Get the fitness of the player for the given behaviour.
int RURoleShootForGoal::GetFitness(const ARugbyCharacter* player, const SSRoleArea*)
{
	if ( player == player->GetAttributes()->GetTeam()->GetGoalKicker() )
		return 99;

	return 0;
}

void RURoleShootForGoal::CheckBallKicked()
{
	// This bool will be set from the animation event, presumably when the animation reaches the point where it kicks the ball.
	if (ball_kicked)
	{
		MABLOGDEBUG("Kicked!");

		FVector kick_vector(FVector::ZeroVector);

		m_pGame->GetInputManager()->GetKickInterface()->StopConversionKick();

		if (m_pPlayer->GetHumanPlayer() && !took_quick_conversion)
		{
			const float CONVERSION_POWER_SCALE = 0.8f;	//Added a scale to reduce the power of the kick
			kick_power = m_pGame->GetInputManager()->GetKickInterface()->GetConversionPower() *CONVERSION_POWER_SCALE;

			MABLOGDEBUG("Kicking with power: %f", kick_power);
			//kick_accuracy = game->GetInputManager()->GetKickInterface()->GetConversionAccuracy();
			m_pGame->GetInputManager()->GetKickInterface()->StopKick();

			// Use the parabola formula y = 2x^2 to give an accuracy effect of -2 to -2, so that the penalty of going away from the 50% mark too far is doubled
			// Fabs is used as on one of the value of x in the parabola equation to negate the sign so that we can still determine if the original accuracy is negative or positive
			//float accuracy_value = 2 * kick_accuracy * MabMath::Fabs( kick_accuracy );
			//float accuracy_effect =  -1.0f * MabMath::Deg2Rad( BAD_ACCURACY_RANGE * accuracy_value );
			//strike_angle += accuracy_effect;

			// adjust power by accuracy - if kick is 100% accurate ( kick_acc == 0 ), power will not be reduced.
			//kick_power -= kick_power * POWER_ACCURACY_EFFECT * MabMath::Fabs( kick_accuracy );

			FVector kick_apex = RUKickHelper::GetGoalKickApexPos(m_pPlayer->GetGameWorld(), m_pPlayer, strike_position, kick_power, false);

			// Build rotation matrix
			MabMatrix m = MabMatrix::RotMatrixY(strike_angle);
			// Rotate the apex
			FVector adjusted_apex;
			MabMatrix::MatrixMultiply(adjusted_apex, (kick_apex - strike_position), m);

			kick_vector = strike_position + adjusted_apex;

			//MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "Debug: strike_angle: %f, Power: %f, Accuracy: %f", strike_angle, kick_power, kick_accuracy);
		}
		else
		{
			kick_vector = CalcCompStrikeVector();
		}

		// Calculate a strike target and angle from the apex.
		FVector ball_position = m_pGame->GetBall()->GetCurrentPosition();
		FVector apex_delta = kick_vector - ball_position;

		// SIF_DEBUG_DRAW(Set3DLine(200757, ball_position, apex_delta, MabColour::Cyan, MabColour::Cyan ));
		//(Set3DLine(200758, ball_position, ball_position + apex_delta, MabColour::Magenta, MabColour::Magenta ));

		strike_target = apex_delta * 2.0f + ball_position;
		strike_target.y = 0.0f;

		//SETDEBUGLINE(3421243, ball_position, strike_target, MabColour::Yellow, MabColour::Yellow );
		//SIF_DEBUG_DRAW(Set3DLine(200759, ball_position, strike_target, MabColour::Green, MabColour::Green ));

		//SETDEBUGMARKER(20089l, strike_target, MabColour::Blue);

		//MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "RURoleShootForGoal::UpdateLogic about to call place kick position to %f %f %f at angle %f", strike_target.x, strike_target.y, strike_target.z, strike_angle);

		// Calculate total velocity required for kick

		float kick_distance = (strike_target - m_pGame->GetBall()->GetCurrentPosition()).Magnitude();

		float wind = 0.0f;
		float totalVelocity = 0.0f;
		m_pGame->GetBall()->GetKickStrengthDetails(KICKTYPE_PLACEKICK, kick_distance, false, totalVelocity, wind);

		MabQuaternion pre_kick_rot;
		m_pGame->GetBall()->SetupBallDropRotation(strike_angle, totalVelocity, KICKTYPE_PLACEKICK, pre_kick_rot);
		m_pGame->GetBall()->SetPreKickRotation(pre_kick_rot);

		m_pGame->GetGameState()->SetBallHolder(NULL);
		m_pGame->GetGameState()->Kick(m_pPlayer, KICKTYPE_PLACEKICK, totalVelocity, strike_angle, 0.0f);

		MABLOGDEBUG("Kicked with velocity: %f", totalVelocity);

		TransitionToState(KFP_BALLKICKED);
	}
}

void RURoleShootForGoal::TransitionAfterSetAccuracy()
{
	if (m_pGame->GetGameSettings().game_settings.network_game)
	{
		TransitionToState(KFP_POWER);
	}
	else
	{
		TransitionToState(KFP_RUNUP);
	}
}

// Auto-set the shoot type - base version sets to conversion, overridden in SSRolePenaltyKick
void RURoleShootForGoal::AutoSetShootType()
{
	// This role has not been overriden as of yet, so set based on gamephase.
	if ( m_pGame->GetGameState()->GetPhase() == RUGamePhase::PENALTY_SHOOT_FOR_GOAL )
	{
		MABLOGDEBUG("This shoot for goal type is PENALTY");
		SetShootType( SHOOTFORGOAL_PENALTY );
	}
	else
	{
		MABLOGDEBUG("This shoot for goal type is CONVERSION");
		SetShootType( SHOOTFORGOAL_CONVERSION );
	}
}




















void RURoleShootForGoal::TransitionToState( KFP_STATE new_state )
{
	// Restart timer
	kick_timer.Reset();
	commentary_time_wasting_timer.Reset();

	// generally we will hide the clock, but a couple of states will turn this on
	SSHumanPlayer* human = m_pPlayer->GetHumanPlayer();
	RUHUDUpdater * hudUpdater = m_pGame->GetHUDUpdater();
	if (human && hudUpdater)
		hudUpdater->SetCountdownClockVisible(false);

#if defined BUILD_DEBUG 
	MABLOGDEBUG("Transitioning from state %s, to state %s.", KFP_STATE_STRINGS[kfp_state], KFP_STATE_STRINGS[new_state]);
#endif
	kfp_state = new_state;

	switch ( new_state )
	{
			case KFP_IDLE:
				{
				}
		break;
			case KFP_BALL_PLACE: // KICK_BALL_PLACE
				{
					strike_position = m_pGame->GetGameState()->GetPlayRestartPosition();

					// R7 specific, don't care about the tee.
					//if(!is_place_kick)

					if ( human != NULL )
					{
						// Check if we have a hud to display the timer
						if (hudUpdater)
						{
							hudUpdater->SetCountdownClockVisible(true);
							hudUpdater->StartCountdownClock(KICK_TIME_OUT);
						}


						max_strike_position = strike_position;

						if ( shoot_type == SHOOTFORGOAL_CONVERSION )
							max_strike_position.z = ( ( FIELD_LENGTH * 0.5f ) - Z_INFIELD_SETUP_STEP ) * m_pPlayer->GetAttributes()->GetTeam()->GetPlayDirection();

						// Only show the tee for place kicks
						if(is_place_kick)
						{
							m_pGame->GetBall()->SetTransparency( BALL_PLACE_TRANSPARENCY );
							m_pGame->Get3DHudManager()->GetTeeArrows()->SetPosition( strike_position );
							m_pGame->Get3DHudManager()->GetTeeArrows()->SetVisible( true );
						}

						// Should we change the cam here for non place kicks?
						m_pGame->GetEvents()->change_to_camera(GAME_CAM_KICK_FOR_POINTS_SETUP);

						if(m_pGame->GetGameSettings().weather_settings.wind_strength > 0.0f)
						{
							m_pGame->Get3DHudManager()->GetWindIndicator()->FadeIn();
						}
					}

					// Only show the tee for place kicks
					
					if(is_place_kick)
						m_pGame->Get3DHudManager()->GetConversionTee()->SetVisible( true );

					// We'll still call this for non place kicks, because it does some imporant angle calcs
					SetKickingTee();

					// If human start kick aim HUD
					//SSHumanPlayer* human = player->GetHumanPlayer();
					//if ( human != NULL )
						m_pGame->GetInputManager()->GetKickInterface()->StartConversionTeeSetup( m_pPlayer, strike_position, target_angle, is_place_kick );

					// Non place kicks, we'll walk up to the strike position for effect
					/*if(!is_place_kick)
					{
						MabMatrix rot = MabMatrix::RotMatrixY( target_angle );
						FVector standing_offset = rot.TransformPos( kicker_offset );
						player->GetMovement()->SetThrottleAndTargetSpeedByUrgency( 0.3f, AS_WALK, AS_IDLE );
						player->GetMovement()->SetCurrentPosition( strike_position - standing_offset );
						player->GetMovement()->SetTargetPosition( strike_position, true );
					}*/
				}
		break;
			case KFP_WALKTO:
				{
					// Reset our walk timer here
					walk_timer.Reset();
					quick_conversion_timer.Reset();

					// Set up the restart position for our kicker to have a valid waypoint etc.
					m_pGame->GetGameState()->SetPlayRestartPosition( strike_position );

					// So i'm pretty sure this is a bug in the original code. Or at least it's wrong.
					// It currently sets the target position as the strike pos + offset. But it's already it's current position, so there will be no walking.
					// Fixed it up for non place kicks in the update loop for KFP_WALKTO
					// For place kicks we could do the same thing, but in DoFacing we should set the target to strike position, and in the update set the target to strike position + offset
					DoFacing();

					// This will cause a warp to our target position.
					SetPlayersVisible( true, true );

					if(!is_place_kick)
					{
						// Stop the multiple warps caused by the formation manager.
						done_walkto_warp = true;

						// Set our new target waypoint
						wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
						m_pMovement->SetTargetPosition( strike_position, true );
						//movement->SetWaypointAcceptRange( ONSIDE_WAYPOINT_ACCEPT_RANGE );
						m_pMovement->SetThrottleAndTargetSpeedByUrgency( 0.95f, AS_WALK );

						wwNETWORK_TRACE_JG("SetParticipationLevel {%4d} %s", __LINE__, __FILE__);
						m_pPlayer->GetState()->SetParticipationLevel(PL_MAX);
					}
					else //for place kick we dont want the visiblity for all players except kicker, the visibility will be restored in Exit.
					{
						/// Now set their visibility
						RUTeam* team = m_pPlayer->GetAttributes()->GetTeam();
						m_pGame->SetPlayersVisible(false, team); //hide all players												
						m_pPlayer->SetActorHiddenInGame(false); //show only kicker
						bPendingRestore = true;
					}

					//m_lock_manager.UFLock(UF_DOMOTION);
					//m_lock_manager.UFLock(UF_DOANIMGRAPH);

					// Switch our camera to the default camera used
					if(human != NULL)
					{
						m_pGame->GetEvents()->change_to_camera(GAME_CAM_INVALID);
					}
					// AI has a special camera now, because they are special
					else
					{
						m_pGame->GetEvents()->change_to_camera(GAME_CAM_CONVERSION_KICK_AI);
					}

					// Reset our ball tint and transparent, and get rid of the tee arrows for place kicks
					if(is_place_kick)
					{
						// Remove the Tee movement arrows
						m_pGame->Get3DHudManager()->GetTeeArrows()->SetVisible( false );

						ball_tint = MabColour::White;
						m_pGame->GetBall()->SetTransparency( 1.0f );
						m_pGame->GetBall()->SetColourTint( MabColour::White );
					}
				}
		break;
			case KFP_SETUP:
				{
					// Still run this for non place kicks, it contains some important variable setting
					SetKickingTee();

					// Only set the tee up if we're doing a place kick
					if(is_place_kick)
					{
						// play kick idle animation						
						RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
						animation->SetVariable( animation->GetHandednessVariable(), handness_index );
						animation->PlayAnimation(KICK_OFF_SETUP_ANIM_REQ);						
					}

					// I dont think this needs to be called? - Dewald WW
					// It does now.... I'm highjacking this to get rid of the quick conversion kick context - Dewald WW
					//game->GetEvents()->shoot_for_goal_setup( player, shoot_type );
				}
		break;

		/// This state will set up everything needed for the conversion angle HUD
			case KFP_AIM:
				{
					ERugbyPlayDirection play_direction = m_pPlayer->GetAttributes()->GetPlayDirection();
					FVector post_position = FVector( 0, 0, m_pGame->GetSpatialHelper()->GetFieldZPosition( play_direction, FIELD_OPP_TRY_LINE ) );
					kick_distance_to_goals = (post_position - m_pGame->GetBall()->GetCurrentPosition()).Magnitude();

					// Notify the context that we can no longer take a quick kick
					m_pGame->GetEvents()->quick_conversion_stop( m_pPlayer, is_place_kick );

					// Turn on the clock if we are the human
					if (human && !took_quick_conversion && hudUpdater)
					{
						m_pGame->GetHUDUpdater()->SetCountdownClockVisible(true);
						m_pGame->GetHUDUpdater()->StartCountdownClock(KICK_TIME_OUT);
					}

					// If human start kick aim HUD
					if (human != NULL) 
					{
						m_pGame->GetInputManager()->GetKickInterface()->StartConversionAim(m_pPlayer, strike_position, target_angle, kick_distance_to_goals, is_place_kick);
					}

					// fire the event to tell the HUD to display the allowed key inputs
					// I dont think this needs to be called? - Dewald WW
					m_pGame->GetEvents()->shoot_for_goal_aim( m_pPlayer );
					MABASSERT ( shoot_type != SHOOTFORGOAL_NONE );
				}
		break;

		// This state is for changing how well we aimed by eye
			case KFP_AIM_ACCURACY:
				{
					// Turn on the clock if we are the human
					if (human && !took_quick_conversion && hudUpdater)
					{
						m_pGame->GetHUDUpdater()->SetCountdownClockVisible(true);
						m_pGame->GetHUDUpdater()->StartCountdownClock(KICK_TIME_OUT);
					}

					// If human start kick accuracy
					if (human != NULL) 
					{
						m_pGame->GetInputManager()->GetKickInterface()->StartConversionAccuracy(m_pPlayer, strike_position, kick_distance_to_goals);
					}
				}
		break;

		/// This state will basically just set up the player animation to start running in toward the ball
			case KFP_RUNUP:
				{
					// Hide the conversion angle indicator
					m_pGame->GetInputManager()->GetKickInterface()->SetVisibilityConversionAngleIndicator( false );

					if(is_place_kick)
					{
						m_pGame->GetEvents()->shoot_for_goal_complete( m_pPlayer );

						RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
						animation->PlayAnimation(RIGHT_KICK_OFF_ANIM_REQ);
						runup_animation_triggered = true;
					}

					// Hide the wind indicator
					m_pGame->GetInputManager()->GetKickInterface()->SetVisibilityWindIndicator( false );
				}
		break;

		/// This state will set up everything needed for the kick power HUD stuff
			case KFP_POWER:
				{
					if (human != NULL && !took_quick_conversion) 
					{
						m_pGame->GetInputManager()->GetKickInterface()->StartConversionPower(m_pPlayer, strike_position, kick_distance_to_goals);
					}
				}
		break;

		/// Ball kicked state will get transitioned into after the animation event fires off and all kicking values have been set.
			case KFP_BALLKICKED:
				{
					m_pGame->GetEvents()->ball_kicked();

					// RC4-4720 - Removing this since it feels like a bug to users.
//					// if there is very little time left on the clock, we cancel it out
//					// as it doesnt make sense to go back to a kick restart.
// 					if ( game->GetGameSettings().game_settings.game_type != GAME_TRAINING && game->GetGameSettings().game_settings.game_type != GAME_MENU)
// 					{
// 						SSGameTimer* gt = game->GetGameTimer();
// 						float time_remaining = gt->GetTimeRemaining();
// 						if ( time_remaining < ( 1.0f / 60.0f * 15.0f ) )
// 							gt->ForceTimeout();
// 					}					
				}
		break;
			case KFP_COMPLETE:
				{
				}
		break;
		default:
			MABBREAKMSG("Trying to transition to a state that we have not yet handled.");
			break;
	}

	SetupHelpText();
}

void RURoleShootForGoal::SetupHelpText()
{
	if ( m_pGame->GetGameSettings().game_settings.game_type == GAME_TRAINING || m_pGame->GetGameSettings().game_settings.game_type == GAME_MENU)
		return;

	SSHumanPlayer* human = m_pPlayer->GetHumanPlayer();
	if (human == nullptr)
	{
		return;
	}

	if (human && !human->IsNetworkPlayer() && !took_quick_conversion )
	{
		MabString text;

		switch ( kfp_state )
		{
			case KFP_BALL_PLACE:
				{
					text = "[ID_CONVERSION_PLACING_TIP]";
				}
				break;

			case KFP_AIM:
				{
					text = "[ID_CONVERSION_AIMING_TIP]";
				}
				break;

			case KFP_AIM_ACCURACY:
				{
					text = "[ID_CONVERSION_ACCURACY_TIP]";
				}
				break;

			case KFP_POWER:
				{
					text = "[ID_CONVERSION_RUNUP_TIP]";
				}
				break;

			default:
				{
					text = "";
				}
				break;

		}
		m_pGame->GetHUDUpdater()->SetKickForPointsHelp(text);
		m_pGame->GetHUDUpdater()->SetConversionHelpVisible(kfp_state);
	}
	else
	{
		// otherwise, kill the message, just in case it got on somehow
		// like changing out of the human player
		m_pGame->GetHUDUpdater()->SetKickForPointsHelp("");
		m_pGame->GetHUDUpdater()->SetConversionHelpVisible(KFP_COMPLETE);
	}
}

// The higher the difficulty, the less the player accuracy will be affected.
// VE	, E		, H		, VH	, P
const float ANGLE_DIFFICULTY_MOD[] = { 0.30f, 0.25f	, 0.20f	, 0.15f	, 0.10f };
/**-----------------------------------------------------------------------------------------------------
	@name	CalcCompStrikeVector()
	@desc 	Calculates the computer players kick
*/
FVector RURoleShootForGoal::CalcCompStrikeVector()
{
	// calc how hard the kick is based on the angle
	float perfect_angle = m_pPlayer->GetAttributes()->GetTeam()->GetPlayAngle();
	float angle_delta = MabMath::AngleDelta( target_angle, perfect_angle );

	// Based on our difficulty we get a angle diff mod to reduce the players' kick accuracy
	float difficult_angle = ANGLE_DIFFICULTY_MOD[m_pGame->GetGameSettings().difficulty];

	float kick_diff = MabMath::Lerp( 0.0f, difficult_angle, MabMath::Abs(angle_delta) );

	float player_kick_ability = m_pPlayer->GetAttributes()->GetGoalKickAccuracy();
	// If we took a quick conversion, we need to suck more.
	if(took_quick_conversion)
	{
		player_kick_ability *= 0.5f;
	}
	// select suitable kick angle and strike control pos as if the comp had chosen them
	float kick_ability = (player_kick_ability - kick_diff) * 10.0f;
	MabMath::Clamp(kick_ability, 0.0f, 10.0f);

	float kickie_kickie = m_pGame->GetRNG()->RAND_RANGED_CALL( float, ( 10.0f - kick_ability ) * 2.0f );
	float kickie_dir	= m_pGame->GetRNG()->RAND_RANGED_CALL(float, 1.0f );
	float strike_control_pos = ( (kickie_kickie / 10.0f) * (( kickie_dir  > 0.5f) ? 1.0f : -1.0f) );

	//strike_control_pos = 0.0f; //<-- Causes a perfect kick through the middle of the posts. Doesn't take wind into account though...

	// cap the value if necessary
	MabMath::Clamp(strike_control_pos, -1.0f, 1.0f);

	bool calc_wind_adjustment = false;
	if(!took_quick_conversion)
	{
		// AI will take wind into consideration on difficulties higher than normal
		calc_wind_adjustment = m_pGame->GetGameSettings().difficulty > DIF_NORMAL;
	}

	// find ideal kick apex
	FVector kick_apex = RUKickHelper::GetGoalKickApexPos( m_pPlayer->GetGameWorld(), m_pPlayer, strike_position, -1.0f, calc_wind_adjustment );
	float kick_angle = SSMath::CalcAngleFromPoints( strike_position, kick_apex );

	// Calculate new angle based on strike control offset from apex
	FVector apex_delta = kick_apex - strike_position;
	strike_angle = MabMath::Deg2Rad ( 12.0f * strike_control_pos + MabMath::Rad2Deg( kick_angle ) );

	// Adjust apex based one new angle
	MabMatrix::MatrixMultiply( kick_apex, apex_delta, MabMatrix::RotMatrixY( MabMath::AngleDelta( kick_angle, strike_angle ) ) );
	kick_apex += strike_position;

	MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "RURoleShootForGoal::CalcCompStrikeVector "
		"angle_delta: %f "
		"player_kick_ability: %f "
		"difficult_angle: %f "
		"kick_diff: %f "
		"kick_ability: %f "
		"strike_control_pos: %f "
		"kick_angle: %f "
		"strike_angle: %f"
		"calc_wind_adjustment: %s",
		angle_delta,
		player_kick_ability,
		difficult_angle,
		kick_diff,
		kick_ability,
		strike_control_pos,
		kick_angle,
		strike_angle,
		(calc_wind_adjustment ? "TRUE" : "FALSE"));

	//SETDEBUGLINE(3421242, strike_position, kick_apex, MabColour::Red, MabColour::Red );

	return kick_apex;
}

void RURoleShootForGoal::SetKickingTee()
{
	ERugbyPlayDirection play_dir = m_pPlayer->GetAttributes()->GetPlayDirection();
	FVector target( 0.0f, 0.0f, m_pGame->GetSpatialHelper()->GetFieldZPosition( play_dir, FIELD_OPP_TRY_LINE ) );

	FVector aim_vector = target - strike_position;
	target_angle = SSMath::CalculateAngle( aim_vector );

	// Tells the kick interface what our new angle is for the camera to look at.
	m_pGame->GetInputManager()->GetKickInterface()->SetConversionTeeAngle( target_angle );

	// The following logic is only important for place kicks
	if(is_place_kick)
	{
		// Set ball position
		float ball_tilt = 0.7f * PI;

		// rotate ball ball_tilt around x axis, then target_angle around y axis
		MabMatrix ball_rot_matrix_y = MabMatrix::RotMatrixY( target_angle );
		MabMatrix ball_rot_matrix = MabMatrix::RotMatrix( ball_tilt, target_angle, 0.0f );

		FVector ball_offset(0, 0.16f, 0.036f);
		ball_offset = ball_rot_matrix_y.TransformPos( ball_offset );

		FVector ball_pos = strike_position + ball_offset;

		m_pGame->GetBall()->SetPositionAbsolute( ball_pos );
		m_pGame->GetBall()->SetRotationAbsolute( ball_rot_matrix );

		m_pGame->Get3DHudManager()->GetConversionTee()->SetPosition( strike_position );
		m_pGame->Get3DHudManager()->GetConversionTee()->SetRotation( target_angle );
	}
}

void RURoleShootForGoal::AnimationEvent(float time, ERugbyAnimEvent event, size_t userdata, bool /*bIsBlendingOut = false*/)
{
	if ( event == ERugbyAnimEvent::BALL_KICK_EVENT )
	{
		ball_kicked = true;
	}
}

/// Show all players that may be affected by moving the strike pos
void RURoleShootForGoal::SetPlayersVisible( bool enabled, bool warp )
{
	/// Must set our team and ref as they are part of the attack formation
	ARugbyCharacter* referee = NULL;

	if ( m_pGame->GetOfficialsTeam() != NULL )
		referee = m_pGame->GetOfficialsTeam()->GetPlayerByPosition( PP_REFEREE );

	RUTeam* team = m_pPlayer->GetAttributes()->GetTeam();

	/// If we have been requested then warp everyone to their waypoint
	if ( warp )
	{
		m_pGame->GetStrategyHelper()->WarpAllPlayersToWaypoints( team );

		if ( referee != NULL )
			referee->GetRole()->WarpToWaypoint();
	}

	/// Now set their visibility
	m_pGame->SetPlayersVisible( enabled, team );

	if ( referee != NULL )
		referee->SetVisible( enabled );
}

void RURoleShootForGoal::WarpToWaypoint()
{
	if( !is_place_kick && kfp_state == KFP_WALKTO)
	{
		if( done_walkto_warp )
			return;
	}

	DoFacing();
	SSRole::WarpToWaypoint();
}

// Since this gets called on a formation update. Our player will snap to this position in a frame/warp to where they start
// Which means, talking toward a target needs to be done in the WALK TO state
void RURoleShootForGoal::DoFacing()
{
	MabMatrix rot = MabMatrix::RotMatrixY( target_angle );
	FVector standing_offset = rot.TransformPos( kicker_offset );

	RUPlayerMovement* plr_movement = m_pPlayer->GetMovement();

	// This makes sure that we're standing in the correct starting position
	if(is_place_kick)
	{
		// We could probably change this to strike_position, and then in the WALK_TO update set the target to strike_position + standing_offset
		wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
		plr_movement->SetTargetPosition( strike_position + standing_offset, true );
	}
	else
	{
		// This will snap our dude in the offset position, and then in the WALK_TO update he will walk to the strike positions
		//plr_movement->SetTargetPosition( strike_position + standing_offset, true );
		ERugbyPlayDirection play_dir = m_pPlayer->GetAttributes()->GetPlayDirection();
		FVector target( 0.0f, 0.0f, m_pGame->GetSpatialHelper()->GetFieldZPosition( play_dir, FIELD_OPP_TRY_LINE ) );

		// For drop kicks (sevens) we'll just stand a few steps back
		FVector strikeVector = ( target - strike_position );
		strikeVector.Normalise();
		standing_offset = strike_position - strikeVector * 2.0f;

		//SIF_DEBUG_DRAW(Set3DLine(2003460, strike_position, strike_position + strikeVector, MabColour::Green, MabColour::Green ));
		//SIF_DEBUG_DRAW(Set3DLine(2003461, strike_position, standing_offset, MabColour::Red, MabColour::Red ));

		wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
		plr_movement->SetTargetPosition( standing_offset, true );
	}

	//SETDEBUGMARKER(900001, strike_position + standing_offset, MabColour::Red);

	plr_movement->SetFacingFlags( AFFLAG_FACETARGANGLE );
	plr_movement->SetCurrentFacingAngle( target_angle );
	plr_movement->SetTargetFacingAngle( target_angle );
}

void RURoleShootForGoal::SetInitialMovement()
{
	RUPlayerMovement *plr_movement = m_pPlayer->GetMovement();
	RUPlayerAttributes *plr_attribs = m_pPlayer->GetAttributes();

	strike_position = m_pGame->GetGameState()->GetPlayRestartPosition();

	// get the kick direction
	strike_angle = plr_attribs->GetTeam()->GetPlayAngle();

	// Face the kick direction.
	plr_movement->SetFacingFlags( AFFLAG_FACEPLAYDIR );
	plr_movement->SetFaceMotionSpeed( plr_movement->GetIdealSpeed( AS_WALK ) );

	// Walk to kickoff...
	wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
	float offset = 2.0f;
	m_pPlayer->GetMovement()->SetTargetPosition( strike_position + FVector( 0.0f, 0.0f, -offset * m_pPlayer->GetAttributes()->GetPlayDirection() ), true);
}

void RURoleShootForGoal::CheckQuickConversion()
{
	// Cannot quick convert in place kicks
	if(is_place_kick) return;

	SSHumanPlayer* human = m_pPlayer->GetHumanPlayer();

	// Don't quick convert yet
	if(!human) return;

	// Can't quick convert in other phases than these
	if(kfp_state != KFP_BALL_PLACE && kfp_state != KFP_WALKTO) return;

	if ( human->IsPressed( ERugbyGameAction::CONVERSION_QUICK_KICK ) )
	{
		took_quick_conversion = true;

		// Please stop moving
		m_pPlayer->GetMovement()->StopAllMovement();

		// New strike position
		strike_position = m_pPlayer->GetMovement()->GetCurrentPosition();

		// Notify the context that we can no longer take a quick kick
		m_pGame->GetEvents()->quick_conversion_stop( m_pPlayer, is_place_kick );

		TransitionToState( KFP_RUNUP );
	}
}

void RURoleShootForGoal::OnTeamAssignmentsChanged(RUTeam* new_team, SSHumanPlayer* human_player)
{
	MABUNUSED(new_team);
	MABUNUSED(human_player);
	RUInputKickInterface* kick_interface = m_pGame->GetInputManager()->GetKickInterface();
	bool kick_ui_active = kick_interface && kick_interface->IsKickActive();
	MABUNUSED(kick_ui_active);

	// reestablish who is controlling this player
	m_pPlayer->GetAttributes()->GetTeam()->GetHumanSelector().AssignHumanToPlayer( m_pPlayer );

	// check if we need the kick stuff for a new human (or the same one again)
	if (m_pPlayer->GetHumanPlayer())
	{
		// Depending on our state we'll re-enable some UI.
		switch ( kfp_state )
		{
		case KFP_IDLE : {}break;
		case KFP_BALL_PLACE : {}break;
		case KFP_WALKTO : {}break;
		case KFP_SETUP : {}break;

		case KFP_AIM :
			{
				if(kick_interface)
				{
					kick_interface->SetVisibilityConversionAimIndicator( true );
				}
			}
			break;

		case KFP_AIM_ACCURACY:
			{
				if(kick_interface)
				{
					kick_interface->SetActiveConversionAngleIndicator( true );
					kick_interface->SetVisibilityConversionAngleIndicator( true );
				}
			}
			break;

		case KFP_RUNUP : {}break;
		case KFP_POWER :
			{
				// Don't bring back the UI if we've already triggered the runup animation since we're already locked in
				if(kick_interface && !runup_animation_triggered)
				{
					kick_interface->SetVisibilityConversionPowerIndicator( true );
					kick_interface->SetActiveConversionPowerIndicator( true );
				}
			}
			break;
		case KFP_BALLKICKED : {}break;
		case KFP_COMPLETE : {}break;
		default : {}break;
		}

		// Enable the clock
		if(m_pGame->GetHUDUpdater())
		{
			m_pGame->GetHUDUpdater()->SetCountdownClockVisible(true);
		}
	}
	// hide the clock if changing to AI
	else
	{
		// Just turn it all off.
		if(kick_interface)
		{
			kick_interface->SetActiveConversionAngleIndicator( false );
			kick_interface->SetVisibilityConversionAngleIndicator( false );

			kick_interface->SetVisibilityConversionAimIndicator( false );

			kick_interface->SetVisibilityConversionPowerIndicator( false );
			kick_interface->SetActiveConversionPowerIndicator( false );
		}

		// Remove the clock
		if(m_pGame->GetHUDUpdater())
		{
			m_pGame->GetHUDUpdater()->SetCountdownClockVisible(false);
		}
	}

	// Fix the help text
	SetupHelpText();
}
