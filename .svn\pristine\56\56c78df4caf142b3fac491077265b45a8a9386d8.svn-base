/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/
#ifndef PLAYERPOSITIONENUM_H
#define PLAYERPOSITIONENUM_H

#include "Mab/Types/MabRuntimeType.h"

#define PLAYERNUM_TO_BIT(n) (1<<(n-1))

// WJS RLC Saved union positions
/// Player positions (bit masks).
///// typedef enum
///// {
///// 	PP_LOOSEHEAD_PROP		= 0x0001,	//Player number 1		// Note: these are in order so that PP_? = 1<< 'shirt number'
///// 	PP_HOOKER				= 0x0002,	//Player number 2
///// 	PP_TIGHTHEAD_PROP		= 0x0004,	//Player number 3
///// 	PP_NUMBER_FOUR_LOCK		= 0x0008,	//Player number 4
///// 	PP_NUMBER_FIVE_LOCK		= 0x0010,	//Player number 5
///// 	PP_BLINDSIDE_FLANKER	= 0x0020,	//Player number 6		// WJS RLC Dropped in League??
///// 	PP_OPENSIDE_FLANKER		= 0x0040,	//Player number 7		// WJS RLC Dropped in League??
///// 	PP_NUMBER_EIGHT			= 0x0080,	//Player number 8		// WJS RLC Dropped in League??
///// 	PP_SCRUM_HALF			= 0x0100,	//Player number 9		// WJS RLC Dropped in League??
///// 	PP_FLY_HALF				= 0x0200,	//Player number 10
///// 	PP_LEFTWING				= 0x0400,	//Player number 11
///// 	PP_INSIDE_CENTER		= 0x0800,	//Player number 12
///// 	PP_OUTSIDE_CENTER		= 0x1000,	//Player number 13
///// 	PP_RIGHTWING			= 0x2000,	//Player number 14
///// 	PP_FULLBACK				= 0x4000,	//Player number 15
///// 
///// 	PP_FRONTROWER		= (PP_LOOSEHEAD_PROP|PP_HOOKER | PP_TIGHTHEAD_PROP),
///// 	PP_SECONDROWER		= (PP_NUMBER_FIVE_LOCK_SECOND_ROW_ELEVEN | PP_NUMBER_FOUR_LOCK_SECOND_ROW_TWELVE),
///// 	PP_BACKROWER		= (PP_BLINDSIDE_FLANKER | PP_OPENSIDE_FLANKER | PP_NUMBER_EIGHT_LOCK_FORWARD),
///// 	PP_HALFBACK			= (PP_SCRUM_HALF | PP_FLY_HALF_STAND_OFF),
///// 	PP_LOCK				= PP_SECONDROWER,
///// 	PP_FIVEEIGHTH		= PP_FLY_HALF_STAND_OFF,														// For the kiwi's
///// 	PP_WING				= (PP_LEFTWING | PP_RIGHTWING),
///// 	PP_CENTER			= (PP_INSIDE_CENTER_LEFTCENTRE | PP_OUTSIDE_CENTER_RIGHTCENTRE),
///// 	PP_FLANKER			= (PP_BLINDSIDE_FLANKER | PP_OPENSIDE_FLANKER),
///// 	PP_MIDFIELD			= (PP_FLY_HALF_STAND_OFF | PP_CENTER),
///// 	PP_BACKFIVE			= (PP_CENTER | PP_WING | PP_FULLBACK),
///// 	PP_OUTSIDE_BACK		= (PP_OUTSIDE_CENTER_RIGHTCENTRE | PP_WING | PP_FULLBACK),
///// 
///// 	PP_FORWARD			= ( PP_FRONTROWER | PP_SECONDROWER | PP_BACKROWER),
///// 	PP_BACK				= ( PP_HALFBACK | PP_WING | PP_INSIDE_CENTER_LEFTCENTRE | PP_OUTSIDE_CENTER_RIGHTCENTRE | PP_FULLBACK ),
///// 
///// 
///// 	PP_REFEREE			= 0x8000,
///// 	PP_TOUCHJUDGE_LEFT  = 0x10000,
///// 	PP_TOUCHJUDGE_RIGHT = 0x20000,
///// 	PP_TOUCHJUDGE_NORTH = 0x40000,
///// 	PP_TOUCHJUDGE_SOUTH = 0x80000,
///// 	PP_NONE				= 0x100000, //so that when ordered in lists by their position, subs will be at back
///// 	PP_SUB				= PP_NONE,
///// 
///// } PLAYER_POSITION_OLD_RU15;

/*
Here’s a comparison of Rugby Union player positions to their Rugby League equivalents:

1. Forwards:
   - Loosehead Prop (RU)	= Prop (RL)
   - Hooker (RU)			= Hooker (RL)
   - Tighthead Prop (RU)	= Prop (RL)
   - Lock (Second Row) (RU) = Lock (RL)
   - Blindside Flanker (RU) = Back Row (RL)
   - Openside Flanker (RU)	= Back Row (RL)
   - Number 8 (RU)			= Lock / Back Row (RL)

2. Backs:
   - Scrum-Half (RU)		= Halfback (RL)
   - Fly-Half (RU)			= Stand-off (RL)
   - Inside Centre (RU)		= Centre (RL)
   - Outside Centre (RU)	= Centre (RL)
   - Wing (RU)				= Wing (RL)
   - Fullback (RU)			= Fullback (RL)

In Rugby League, the positions are generally more simplified, and there are fewer players 
(13 in League vs. 15 in Union). For instance, in Union, the back row is often split into blindside, 
openside, and number 8, whereas in League, it’s just generally referred to as the "back row."


//*/

/*
	1 to 10
	2 to 9
	3 to 8

	 4 to 12
	 5 to ll

	 8 to 13
	 9 to 7
	 10 to 6

	 11 to 5
	 12 to 4
	 14 to 2
	 13 to 3
	 15 to 1

	 6 and 7 are not used in league
	 those roles are shared with 
	 Second Row (RL 11 and RL 12) and the Loose Forward (RL 13)
//*/

typedef enum
{	
	PP_FULLBACK								= PLAYERNUM_TO_BIT(1),
	PP_RIGHTWING							= PLAYERNUM_TO_BIT(2),
	PP_OUTSIDE_CENTER_RIGHTCENTRE			= PLAYERNUM_TO_BIT(3),
	PP_INSIDE_CENTER_LEFTCENTRE				= PLAYERNUM_TO_BIT(4),
	PP_LEFTWING								= PLAYERNUM_TO_BIT(5),
	PP_FLY_HALF_STAND_OFF					= PLAYERNUM_TO_BIT(6),			// Stand off / five eighth
	PP_SCRUM_HALF							= PLAYERNUM_TO_BIT(7),
	PP_TIGHTHEAD_PROP						= PLAYERNUM_TO_BIT(8),			
	PP_HOOKER								= PLAYERNUM_TO_BIT(9),			
	PP_LOOSEHEAD_PROP						= PLAYERNUM_TO_BIT(10),			
	PP_NUMBER_FIVE_LOCK_SECOND_ROW_ELEVEN	= PLAYERNUM_TO_BIT(11),			// WJS RLC TODO Rename to second row something or lock something??	These were left long to help convert the roles but should be given their shorter name
	PP_NUMBER_FOUR_LOCK_SECOND_ROW_TWELVE	= PLAYERNUM_TO_BIT(12),			// WJS RLC TODO Rename to second row something or lock something??	These were left long to help convert the roles but should be given their shorter name	
	PP_NUMBER_EIGHT_LOCK_FORWARD			= PLAYERNUM_TO_BIT(13),			// WJS RLC Lock forward also known as LOOSE_FORWARD??

	PP_FRONTROWER	= (PP_LOOSEHEAD_PROP | PP_HOOKER | PP_TIGHTHEAD_PROP),
	PP_SECONDROWER	= (PP_NUMBER_FIVE_LOCK_SECOND_ROW_ELEVEN | PP_NUMBER_FOUR_LOCK_SECOND_ROW_TWELVE), 
	PP_BACKROWER	= (PP_NUMBER_EIGHT_LOCK_FORWARD | PP_INSIDE_CENTER_LEFTCENTRE | PP_OUTSIDE_CENTER_RIGHTCENTRE),
	PP_HALFBACK		= (PP_SCRUM_HALF ),
	///PP_LOCK			= PP_SECONDROWER,
	PP_FIVEEIGHTH	= PP_FLY_HALF_STAND_OFF,													// For the kiwi's
	PP_WING			= (PP_LEFTWING | PP_RIGHTWING),
	PP_CENTER		= (PP_INSIDE_CENTER_LEFTCENTRE | PP_OUTSIDE_CENTER_RIGHTCENTRE),

	// WJS RLC Do we need to rethink/code Flankers
	// as chatgpt says
	// Second Row (11 and 12) resemble the blindside flanker in Union.
	// Loose Forward(13) is similar to the openside flanker in Union.
	//PP_BLINDSIDE_FLANKER	= PP_SECONDROWER,
	//PP_OPENSIDE_FLANKER		= PP_NUMBER_EIGHT_LOCK_FORWARD,
	//PP_FLANKER = (PP_BLINDSIDE_FLANKER | PP_OPENSIDE_FLANKER),

	PP_FORWARD	= (PP_FRONTROWER | PP_SECONDROWER | PP_NUMBER_EIGHT_LOCK_FORWARD),				// 6 players
	PP_BACK		= (PP_FULLBACK | PP_WING | PP_CENTER | PP_FLY_HALF_STAND_OFF | PP_SCRUM_HALF),	// 7 players

	PP_PROP = (PP_LOOSEHEAD_PROP | PP_TIGHTHEAD_PROP),

	PP_REFEREE = 0x8000,
	PP_TOUCHJUDGE_LEFT = 0x10000,
	PP_TOUCHJUDGE_RIGHT = 0x20000,
	PP_TOUCHJUDGE_NORTH = 0x40000,
	PP_TOUCHJUDGE_SOUTH = 0x80000,
	PP_NONE = 0x100000, //so that when ordered in lists by their position, subs will be at back
	PP_SUB = PP_NONE,

} PLAYER_POSITION;

/// Reflect the PLAYER_POSITION enum as an int
RUNTIMETYPE_FROM_ALIAS(PLAYER_POSITION, int);

namespace PlayerPositionEnum
{
	/// 1 based so can be used with jersey numbers
	PLAYER_POSITION GetPlayerPositionFromStartingJerseyNumber( int starting_no, int forceGameMode );
	PLAYER_POSITION GetPlayerPositionFromStartingJerseyNumber( int starting_no );

	PLAYER_POSITION GetPlayerPositionFromLineupIndex( int index );

	PLAYER_POSITION GetPlayerPositionFromLineupIndexSevensOnly( int index );

	/// Get the player position as a number from 1 to 13 (0 if not an on-field position)
	/// (ie the jersey number of the player who would have started in this position)
	int GetStartingJerseyNumberFromPlayerPosition( PLAYER_POSITION p );

	/// Get the index of the position 0->7 from the position
	int GetPositionIndexFromPlayerPositionR7( PLAYER_POSITION p );
	/// Get the index of the position 0->15 from the position
	int GetPositionIndexFromPlayerPositionR13( PLAYER_POSITION p );
	/// Get the index of the position 0->7 or 0->15 from the position depending on the current gametype
	int GetPositionIndexFromPlayerPosition( PLAYER_POSITION p );

	/// Get a text representation of the player position
	/// NOTE: this can and probably should rather be retrieved from the RL3Database if being displayed to the end user
	const char* GetPlayerPositionText( PLAYER_POSITION p );

	const char* GetPlayerPositionTextFull( PLAYER_POSITION p );

	/// get the abbreviated version of the player text
	const char* GetPlayerPositionTextAbbreviated( PLAYER_POSITION p );
}

#endif
