
//###########################################################################################
//Statemachine methods for tackle_blend
//###########################################################################################

////player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|

#include "RugbyAnimationStateMachine_Tackles.h"
#include "Animation/RugbyAnimationStateMachine.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Animation/RugbyAnimationRecords.h"
#include "Match/RugbyUnion/Enums/RUActionIndexEnum.h"
#include "Match/AI/Actions/RUActionTacklee.h"
#include "Match/AI/Actions/RUActionTackler.h"
#include "Match/Components/RUActionManager.h"
#include "Character/RugbyCharacterAnimInstance.h"
#include "Character/RugbyCharacter.h"
#include "Rugby/Match/SSSpatialHelper.h"
#include <Runtime/Engine/Classes/Animation/AnimInstance.h>

#include "DrawDebugHelpers.h"
#include "RugbyGameInstance.h"
#include "Runtime/Core/Public/Logging/LogMacros.h"
#include "Utility/consoleVars.h"

#if wwDEBUG_ARB
#include "DrawDebugHelpers.h"
#endif

//==============================================================================================================================================================

#define IS_GETUP_FROM_STRUGGLE()		(UOBJ_IS_VALID(m_pPlayer) && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()) && m_pPlayer->GetAnimInstance()->m_bStruggleActive)
#define GETUP_DEFAULT_BLEND_TIME		(0.25f)
#define GETUP_FROM_STRUGGLE_BLEND_TIME	(0.0f)
#define HEADHIGH_TACKLE_QUICKTAP_WAIT_TIME	(3.5f) //wait for 3.5 seconds (before autoselecting quick tap getup).

//==============================================================================================================================================================

RugbyAnimationStateMachine_Tackles::RugbyAnimationStateMachine_Tackles(RugbyAnimationStateMachineMgr* pSMMgr, RUPlayerAnimation* pOwnerAnimation, ARugbyCharacter* pOwnerPlayer)
	: RugbyAnimationStateMachineBase(pSMMgr, pOwnerAnimation, pOwnerPlayer)
{
	InitialiseTackleStateMachine();
	InitialiseContestedTackleStateMachine();
}

//==============================================================================================================================================================

RugbyAnimationStateMachine_Tackles::~RugbyAnimationStateMachine_Tackles()
{

}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_Tackles::InitialiseTackleStateMachine()
{
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::null, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, NullTackle));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::ankle_tap_tacklee, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, AnkleTapTacklee));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::ankle_tap_tackler, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, AnkleTapTackler));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::contested_tacklee, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, ContestedTacklee));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::contested_tackler, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, ContestedTackler));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::fend_fail_tacklee, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, FendFailTacklee));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::fend_fail_tackler, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, FendFailTackler));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::fend_success_tacklee, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, FendSuccessTacklee));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::fend_success_tackler, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, FendSuccessTackler));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::head_high_tacklee, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, HeadHighTacklee));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::head_high_tackler, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, HeadHighTackler));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::sidestep_fail_tacklee, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, SidestepFailTacklee));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::sidestep_fail_tackler, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, SidestepFailTackler));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::sidestep_success_tacklee, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, SidestepSuccessTacklee));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::sidestep_success_tackler, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, SidestepSuccessTackler));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::standard_fail_tacklee, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, StandardFailTacklee));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::standard_fail_tackler, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, StandardFailTackler));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::standard_success_tacklee, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, StandardSuccessTacklee));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::standard_success_tackler, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, StandardSuccessTackler));
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::standard_success_two_tackler, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, StandardSuccessTwoTackler));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::standard_success_two_tacklee, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, StandardSuccessTwoTacklee));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::standard_fail_two_tacklee, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, StandardFailTwoTacklee));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::standard_fail_two_tackler, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, StandardFailTwoTackler));
#endif
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::try_pushed_tacklee, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, TryPushedTacklee));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::try_pushed_tackler, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, TryPushedTackler));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::dive_miss_tackler, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, DiveMissTackler));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::fast_getup_tackler, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, FastGetupTackler));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::Getup, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, GetupTackle));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::play_the_ball_ground, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, GetupPlayTheBall));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::Getup_injury, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, GetupTackleInjury));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::quick_tap, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, QuickTapTackle));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::Try, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, TryTackle));
	m_TackleStateMachine.AddState(ERugbyAnim_Mode_Tackles::try_corner, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, TryCornerTackle));
	m_TackleStateMachine.Initialise(ERugbyAnim_Mode_Tackles::null);
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::InitialiseContestedTackleStateMachine()
{
	m_ContestedTackleStateMachine.AddState(EContestedTackleAnimationState::InvalidState, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, ContestedTackleInvalidState));
	m_ContestedTackleStateMachine.AddState(EContestedTackleAnimationState::tackle_driven_impact, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, ContestedTackleImpact));
	m_ContestedTackleStateMachine.AddState(EContestedTackleAnimationState::tackle_driven_engaged, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, ContestedTackleEngaged));
	m_ContestedTackleStateMachine.AddState(EContestedTackleAnimationState::tackle_driven_takedown, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, ContestedTackleTakeDown));
	m_ContestedTackleStateMachine.AddState(EContestedTackleAnimationState::tackle_driven_breakout, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, ContestedTackleBreakOut));
	m_ContestedTackleStateMachine.AddState(EContestedTackleAnimationState::ground, this, BASIC_STATEMACHINE_FUNCTION_LIST(RugbyAnimationStateMachine_Tackles, ContestedTackleGround));
	m_ContestedTackleStateMachine.Initialise(EContestedTackleAnimationState::InvalidState);
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================
void RugbyAnimationStateMachine_Tackles::OnEnterNullTackle()
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_RugbyAnimationStateMachine_Tackles_OnEnterNullTackle)
	ERugbyAnim_Mode_Tackles TailItem;
	m_TacklesModeQueue.Peek(TailItem);

	if (TailItem == m_CurrentModeTackles)
	{
		m_TacklesModeQueue.Pop(); //pop whatever u have.
	}

	m_CurrentModeTackles = ERugbyAnim_Mode_Tackles::null;	
	m_TackleAnimationStateIsGround = false;
	m_NewTackleMetaData.Reset();

	m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_getup));	
	m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_driven_takedown));	
	m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_driven_breakout));	

	m_bJustEnteredNull = true;
	SetRemainingTransitionTime(m_ForceStopBlendOutTime);

	if (UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
	{
		m_pPlayer->GetAnimInstance()->EndStruggle(SMDefaultBlendOutTime);
	}

	//UE_LOG(LogTemp, Warning, TEXT("StateMachine_Tackle_Null for: '%s'"), *m_pPlayer->GetName());
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateNullTackle(const float deltaSecs)
{
	if (m_bJustEnteredNull)
	{
		//stop if it's already not stopped
		if (m_pPlayer->GetAnimInstance() && m_tackleMontageData.pMontageInstance)
		{
			bool bMontageStoppedLocally = false;
			for (auto &MontageInt : m_pPlayer->GetAnimInstance()->MontageInstances)
			{
				if (MontageInt && (m_tackleMontageData.pMontageInstance == MontageInt))
				{
					if (m_tackleMontageData.pMontageInstance->IsActive()) //stop the animation if it's still playing....
					{
						UE_LOG(LogTemp, Warning, TEXT("RugbyAnimationStateMachine_FullBodyAction::OnEnterFullBodyNull Stop Full Body for: '%s'"), *m_pPlayer->GetName());
						m_tackleMontageData.pMontageInstance->Stop(m_ForceStopBlendOutTime);
					}
					break;
				}
			}
			if (!bMontageStoppedLocally)
			{
				ensureAlways(m_tackleMontageData.pMontageInstance);
				if (m_tackleMontageData.pMontageInstance)
				{
					SetRemainingTransitionTime(m_tackleMontageData.pMontageInstance->GetBlendTime());
				}
			}
		}
		m_tackleMontageData.Reset();
		m_ForceStopBlendOutTime = SMDefaultBlendOutTime;
		m_bJustEnteredNull = false;
	}
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnExitNullTackle()
{
	//UE_LOG(LogTemp, Display, TEXT("OnExitNullTackle for '%s'"), *m_pPlayer->GetName());
	m_tackleMontageData.Reset();
	m_ForceStopBlendOutTime = SMDefaultBlendOutTime;
	m_bJustEnteredNull = false;
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterAnkleTapTacklee()
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_RugbyAnimationStateMachine_Tackles_OnEnterAnkleTapTacklee)
	UE_LOG(LogTemp, Display, TEXT("OnEnterAnkleTapTacklee '%s' "), *m_pPlayer->GetName());
	
	bool LoopAlways = false;

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::ankle_tap_tacklee);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterAnkleTapTacklee:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

#if ENABLE_SUCCESSFUL_ANKLE_TAPS
	m_IsSuccessfulAnkleTap = m_pRUPlayerAnimation->GetTackle_success_var();
#else
	m_IsSuccessfulAnkleTap = false;
#endif

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if (m_pRUPlayerAnimation->GetTackle_dominance_var() != AnimRec->m_Dominance)
			continue;

		if (m_IsSuccessfulAnkleTap)
		{
			if (AnimRec->m_SubType != "success")
				continue;
		}
		else
		{
			if (AnimRec->m_SubType == "success")
				continue;
		}

		tempRec.Add(AnimRec);
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateAnkleTapTacklee(const float deltaSecs)
{
	if (m_IsSuccessfulAnkleTap)
	{
		if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
		{
			bool *GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);

			EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;

			if ((m_TackleAnimationStateIsGround == false) && RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
			{
				m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup); //add to monitor if it doesnot have already.			

				//in the meantime attempt to fall on ground
				m_TackleAnimationStateIsGround = true;

				if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
				{
					m_pPlayer->GetAnimInstance()->BeginStruggle(m_CurrentModeTackles != ERugbyAnim_Mode_Tackles::ankle_tap_tacklee);
				}
			}

			else if (GetUpState && (*GetUpState == true) && (m_TackleAnimationStateIsGround == true)) //if we have a getup request
			{
				m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup);
			}
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitAnkleTapTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitAnkleTapTacklee for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterAnkleTapTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterAnkleTapTackler '%s' "), *m_pPlayer->GetName());	
	
	bool LoopAlways = false;

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::ankle_tap_tackler);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterAnkleTapTackler:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

#if ENABLE_SUCCESSFUL_ANKLE_TAPS
	m_IsSuccessfulAnkleTap = m_pRUPlayerAnimation->GetTackle_success_var();
#else
	m_IsSuccessfulAnkleTap = false;
#endif

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if (m_pRUPlayerAnimation->GetTackle_dominance_var() != AnimRec->m_Dominance)
			continue;

		if (m_IsSuccessfulAnkleTap)
		{
			if (AnimRec->m_SubType != "success")
				continue;
		}
		else
		{
			if (AnimRec->m_SubType == "success")
				continue;
		}

		tempRec.Add(AnimRec);
	}

	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateAnkleTapTackler(const float deltaSecs)
{
	//the bail out happens in event handler.
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitAnkleTapTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitAnkleTapTackler for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterContestedTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterContestedTacklee '%s' "), *m_pPlayer->GetName());
	m_ContestedTackleStateMachine.ChangeState(EContestedTackleAnimationState::tackle_driven_impact);
	/*
	//tacklee_driven_impact: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee|tackle|tacklee_driven_impact|
	m_SubType mapping:
	1 -> tacklee_driven_impact
	2 -> tacklee_driven_engaged
	3 -> tacklee_driven_takedown
	4 -> tacklee_driven_breakout
	5 -> ground
	*/	
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateContestedTacklee(const float deltaSecs)
{
	bool *GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee_null2 durationInEvents: 0.5
	if (m_pRUPlayerAnimation->GetMaulStateVariable()->getValue() > 0.5f)
	{
		UE_LOG(LogTemp, Display, TEXT("OnUpdateContestedTacklee, MaulState is 1, setting tackle mode to NULL for '%s'"), *m_pPlayer->GetName());
		//m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);		
		//This logic is implemented in RugbyAnimationStateMachine_FullBodyAction class where it monitors the tackle state and resets the tackle state to null, before forcing to maul state....		
	}

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee_getup durationInTime: 0.0
	else if (m_pSMMgr->m_AnimationRequestMonitorList.Contains(SM_getup) && GetUpState && (*GetUpState == true))
	{
		if (m_pRUPlayerAnimation->GetMaulStateVariable()->getValue() < 0.5f) /*inputParam: ControlParameters | maul_state,lessThanOperation : True, orEqual : FalsetestValue : 0.5*/
		{
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup);
		}
	}
	else
	{
		m_ContestedTackleStateMachine.Update(deltaSecs);
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitContestedTacklee()
{
	if (m_pActiveBlendN)
	{
		// #anim_todo_blend_times
		m_pActiveBlendN->Stop(SMDefaultBlendOutTime);
	}
	m_pActiveBlendN = nullptr;	

	m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_driven_takedown));	
	m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_driven_breakout));	

	m_ContestedTackleStateMachine.ChangeState(EContestedTackleAnimationState::InvalidState);

	UE_LOG(LogTemp, Display, TEXT("OnExitContestedTacklee for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterContestedTackler() //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle 
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterContestedTackler '%s' "), *m_pPlayer->GetName());
	m_ContestedTackleStateMachine.ChangeState(EContestedTackleAnimationState::tackle_driven_impact);

	/*
	//tackler_driven_impact: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle|tackler_driven_impact|
	m_SubType mapping:
	1 -> tacklee_driven_impact
	2 -> tacklee_driven_engaged
	3 -> tacklee_driven_takedown
	4 -> tacklee_driven_breakout
	5 -> ground
	*/
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateContestedTackler(const float deltaSecs)
{	
	bool *GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);

	//20: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler_null1 durationInEvents: 0.5
	if (m_pRUPlayerAnimation->GetMaulStateVariable()->getValue() > 0.5f)
	{
		UE_LOG(LogTemp, Display, TEXT("OnUpdateContestedTackler, MaulState is 1, setting tackle mode to NULL for '%s'"), *m_pPlayer->GetName());
		//This logic is implemented in RugbyAnimationStateMachine_FullBodyAction class where it monitors the tackle state and resets the tackle state to null, before forcing to maul state....		
		//m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
	}
	else if (m_pSMMgr->m_AnimationRequestMonitorList.Contains(SM_getup) && GetUpState && (*GetUpState == true))
	{
		//01. player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler_getup durationInTime: 0.0
		if ((m_pRUPlayerAnimation->GetMaulStateVariable()->getValue() < 0.5f) && /*inputParam: ControlParameters | maul_state,lessThanOperation : True, orEqual : FalsetestValue : 0.5*/
			(m_pRUPlayerAnimation->GetEnterRuckStateVariable()->getValue() <= 0.5f)) /*inputParam: ControlParameters|enter_ruck_state, lessThanOperation: True, orEqual: True testValue: 0.5*/ //ruck, maul and OnRequest
		{
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup);
		}
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler_fast_getup_tackler durationInTime: 0.0
		else if (m_pRUPlayerAnimation->GetEnterRuckStateVariable()->getValue() > 0.5f)
		{
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::fast_getup_tackler);
		}
	}
	else
	{
		m_ContestedTackleStateMachine.Update(deltaSecs);
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitContestedTackler()
{
	if (m_pActiveBlendN)
	{
		// #anim_todo_blend_times
		m_pActiveBlendN->Stop(SMDefaultBlendOutTime);
	}
	m_pActiveBlendN = nullptr;

	m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_driven_takedown));	
	m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_driven_breakout));	
	
	m_ContestedTackleStateMachine.ChangeState(EContestedTackleAnimationState::InvalidState);

	UE_LOG(LogTemp, Display, TEXT("OnExitContestedTackler for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterFendFailTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterFendFailTacklee '%s' "), *m_pPlayer->GetName());

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_fail_tacklee|tackle	

	bool LoopAlways = false;

	if (m_TackleAnimationStateIsGround == true)
	{
		LoopAlways = true;
	}

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::fend_fail_tacklee);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterFendFailTacklee:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	bool GrdType = (m_TackleAnimationStateIsGround == true) ? true : false;

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if (GrdType == AnimRec->m_IsGround)
		{
			tempRec.Add(AnimRec);
		}
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateFendFailTacklee(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		bool *GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);

		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_fail_tacklee|eq_front|impact_ground
		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;

		if ((m_TackleAnimationStateIsGround == false) && RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			m_TackleAnimationStateIsGround = true;
			m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup);

			if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
			{
				m_pPlayer->GetAnimInstance()->BeginStruggle(m_CurrentModeTackles != ERugbyAnim_Mode_Tackles::fend_fail_tacklee);
			}
		}
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_fail_tacklee_getup durationInTime: 0.0
		else if ((m_TackleAnimationStateIsGround == true) && GetUpState && (*GetUpState == true)) //if we have a getup request
		{
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup);
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitFendFailTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitFendFailTacklee for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterFendFailTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterFendFailTackler '%s' "), *m_pPlayer->GetName());

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_fail_tackler|tackle	
	bool LoopAlways = false;
	if (m_TackleAnimationStateIsGround == true)
	{
		LoopAlways = true;
	}

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::fend_fail_tackler);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterFendFailTackler:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	bool GrdType = (m_TackleAnimationStateIsGround == true) ? true : false;

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if (GrdType == AnimRec->m_IsGround)
		{
			tempRec.Add(AnimRec);
		}
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateFendFailTackler(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		bool *GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_fail_tackler|eq_front|impact_ground
		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;
		if ((m_TackleAnimationStateIsGround == false) && RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			m_TackleAnimationStateIsGround = true;
			m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup);

			if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
			{
				m_pPlayer->GetAnimInstance()->BeginStruggle(m_CurrentModeTackles != ERugbyAnim_Mode_Tackles::fend_fail_tackler);
			}
		}
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_fail_tackler_getup durationInTime: 0.0
		else if ((m_TackleAnimationStateIsGround == true) && GetUpState && (*GetUpState == true))
		{
			if (m_pRUPlayerAnimation->GetEnterRuckStateVariable()->getValue() > 0.5f) //Added for RC4 based on #RC4-3693
			{
				m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::fast_getup_tackler);
			}
			else
			{
				m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup);
			}
		}
	}
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnExitFendFailTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitFendFailTackler for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterFendSuccessTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterFendSuccessTacklee '%s' "), *m_pPlayer->GetName());

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_success_tacklee|tackle
	
	bool LoopAlways = false;

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::fend_success_tacklee);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterFendSuccessTacklee:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		tempRec.Add(AnimRec);
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateFendSuccessTacklee(const float deltaSecs)
{	
	//nothing...
	//bail out happens in event handler
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitFendSuccessTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitFendSuccessTacklee for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterFendSuccessTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterFendSuccessTackler '%s' "), *m_pPlayer->GetName());

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_success_tackler|tackle
	
	bool LoopAlways = false;

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::fend_success_tackler);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterFendSuccessTackler:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		tempRec.Add(AnimRec);
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateFendSuccessTackler(const float deltaSecs)
{	
	//nothing...
	//bail out happens in event handler		
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitFendSuccessTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitFendSuccessTackler for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterHeadHighTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterHeadHighTacklee '%s' "), *m_pPlayer->GetName());

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|head_high_tacklee|tackle
	
	bool LoopAlways = false;

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::head_high_tacklee);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterHeadHighTacklee:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		tempRec.Add(AnimRec);
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);

	m_HighTackleTimer = 0.0f;
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateHeadHighTacklee(const float deltaSecs)
{
	m_HighTackleTimer += deltaSecs;

	bool *GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);
	
	EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;

	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{		
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))			
		{
			////player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|head_high_tacklee_getup1 durationInTime: 0.0
			if (m_pRUPlayerAnimation->GetWithBallStateVariable()->getValue() > 0.5f)
			{
				UE_LOG(LogTemp, Display, TEXT("OnUpdateHeadHighTacklee for '%s' Time: '%.3f'"), *m_pPlayer->GetName(), m_HighTackleTimer);

				if (m_HighTackleTimer > HEADHIGH_TACKLE_QUICKTAP_WAIT_TIME) //this logic to added to avoid going to quick tap too early when the headhigh tacklee animations are too short...
				{
					m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::quick_tap);
					m_HighTackleTimer = 0.0f;
				}
				else
				{
					if (m_TackleAnimationStateIsGround == false)
					{
						m_TackleAnimationStateIsGround = true;

						m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup);

						if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
						{
							m_pPlayer->GetAnimInstance()->BeginStruggle(false);
						}
					}
				}
			}
			else//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|head_high_tacklee_getup durationInTime: 0.0
			{
				m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup);
			}
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitHeadHighTacklee()
{
	m_HighTackleTimer = 0.0f;
	UE_LOG(LogTemp, Display, TEXT("OnExitHeadHighTacklee for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterHeadHighTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterHeadHighTacklee '%s' "), *m_pPlayer->GetName());

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|head_high_tackler|tackle
	
	bool LoopAlways = false;

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::head_high_tackler);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterHeadHighTacklee:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		tempRec.Add(AnimRec);
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateHeadHighTackler(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|head_high_tackler_null durationInTime: 0.10000000149011612
		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))			
		{
			//we dont need getup for this tackle, as they dont depend on 'getup' request, so if a stray getup request is there, reset it...			
			m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_getup));
			m_ForceStopBlendOutTime = 0.10000000149011612f;
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitHeadHighTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitHeadHighTackler for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterSidestepFailTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterSidestepFailTacklee '%s' "), *m_pPlayer->GetName());

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|tackle
	//tackle_sidestep_type_variable
	/*
	enum SIDESTEP_TACKLE_TYPE {
	SSTT_FOOLED,			/// Player was fooled by the sidestep, cleanly beaten - tackle was not successful
	SSTT_PARTIAL_FAIL,		/// Player was partially fooled by the sidestep - manages to grasp player a bit, but tackle is unscuccessful
	SSTT_PARTIAL_SUCCESS,	/// Player was partially fooled by the sidestep - manages to grasp player a bit, but tackle is successful
	SSTT_NOT_FOOLED,		/// Player was not fooled by the sidestep - successful tackle
	SSTT_UNKNOWN
	*/
	/*
	connectedNode: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|aaa
	sourceNodes
	00: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|aaa
	01: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|aaa1
	02: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|partial_success
	03: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|not_fooled
	weightParam: ControlParameters|tackle_sidestep_type
	*/

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::sidestep_fail_tacklee);

	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterSidestepFailTacklee:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	bool LoopAlways = false;

	if (m_TackleAnimationStateIsGround == true)
	{
		m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup); //add to monitor if it doesnot have already.	
		LoopAlways = true;
	}

	if ((m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_FOOLED) ||
		(m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_PARTIAL_FAIL))
	{
		LoopAlways = true;
		UE_DEBUG_BREAK(); //this code should not execute
	}

	bool GrdType = (m_TackleAnimationStateIsGround == true) ? true : false;

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
			/*
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_front1|impact|tesslnfoimp03
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_front1|ground|tesslnfoimp03
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_back1|impact|tessldnfoimp02
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_back1|ground|tessldnfoimp02
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_right1|impact|tessldnfoimp04
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_right1|ground|tessldnfoimp04
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_left1|impact|tessldnfoimp03
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_left1|ground|tessldnfoimp03
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_front2|impact|tesslnfoimp02
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_front2|ground|tesslnfoimp02
			*/
		if ((((m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_PARTIAL_SUCCESS) && (AnimRec->m_SubType == "partial_success")) ||
			/*
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_front|impact|tessrnfoimp02
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_front|ground|tessrnfoimp02
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_back|impact|tesueqfbwimp01
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_back|ground|tesueqfbwimp01
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_right|impact|tessrnfoimp01
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_right|ground|tessrnfoimp01
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_left|impact|tessrnfoimp01
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_left|ground|tessrnfoimp01
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_front3|ground|testruggle01
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_front3|ground|tesslnfoimp01
			*/
			((m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_NOT_FOOLED) && (AnimRec->m_SubType == "not_fooled")) ||

			/*player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|aaa
			*/
			((m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_FOOLED) && (AnimRec->m_SubType == "fooled")) || //this should not get executed
			/*
			player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|aaa1
			*/
			((m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_PARTIAL_FAIL) && (AnimRec->m_SubType == "partial_failed"))  //this should not get executed
			)
			&&
			(AnimRec->m_IsGround == GrdType)
			)//if bracket		   
		{
			tempRec.Add(AnimRec); //should only be for SSTT_NOT_FOOLED and SSTT_PARTIAL_SUCCESS			
		}
		else
		{
			UE_LOG(LogTemp, Display, TEXT("No animation found, reset to null"));
		}
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}


//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateSidestepFailTacklee(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		bool *GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);

		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee|eq_front|impact_ground
		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;
		if ((m_TackleAnimationStateIsGround == false) && RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup);

			m_TackleAnimationStateIsGround = true;

			if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
			{
				m_pPlayer->GetAnimInstance()->BeginStruggle(m_CurrentModeTackles != ERugbyAnim_Mode_Tackles::sidestep_fail_tacklee);
			}
		}
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee_getup durationInTime: 0.0
		else if ((m_TackleAnimationStateIsGround == true) && GetUpState && (*GetUpState == true)) //if we have a getup request
		{
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup);
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitSidestepFailTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitSidestepFailTacklee for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterSidestepFailTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterSidestepFailTackler '%s' "), *m_pPlayer->GetName());

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tackler|tackle
	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::sidestep_fail_tackler);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterSidestepFailTackler:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}
	
	bool LoopAlways = false;

	if (m_TackleAnimationStateIsGround == true)
	{
		m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup); //add to monitor if it doesnot have already.	
		LoopAlways = true;
	}
	
	if ((m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_FOOLED) ||
		(m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_PARTIAL_FAIL))
	{
		LoopAlways = true;
		UE_DEBUG_BREAK(); //this code should not execute
	}

	bool GrdType = (m_TackleAnimationStateIsGround == true) ? true : false;

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if ((((m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_PARTIAL_SUCCESS) && (AnimRec->m_SubType == "partial_success")) ||		
			((m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_NOT_FOOLED) && (AnimRec->m_SubType == "not_fooled")) ||			
			((m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_FOOLED) && (AnimRec->m_SubType == "fooled")) || //this should not get executed			
			((m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_PARTIAL_FAIL) && (AnimRec->m_SubType == "partial_failed"))  //this should not get executed
			)
			&&
			(AnimRec->m_IsGround == GrdType)
			)//if bracket			
		{
			tempRec.Add(AnimRec); //should only be for SSTT_NOT_FOOLED and SSTT_PARTIAL_SUCCESS			
		}
		else
		{
			UE_LOG(LogTemp, Display, TEXT("No animation found, reset to null"));
		}
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateSidestepFailTackler(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		bool *GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);

		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tackler|eq_front|impact_ground		
		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;
		if ((m_TackleAnimationStateIsGround == false) && RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup);

			m_TackleAnimationStateIsGround = true;

			if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
			{
				m_pPlayer->GetAnimInstance()->BeginStruggle(m_CurrentModeTackles != ERugbyAnim_Mode_Tackles::sidestep_fail_tackler);
			}
		}
		//07. player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tackler_getup durationInTime: 0.0
		else if ((m_TackleAnimationStateIsGround == true) && GetUpState && (*GetUpState == true)) //if we have a getup request
		{
			if (m_pRUPlayerAnimation->GetEnterRuckStateVariable()->getValue() > 0.5f) //Added for RC4 based on #RC4-3693
			{
				m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::fast_getup_tackler);
			}
			else
			{
				m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup);
			}
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitSidestepFailTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitSidestepFailTackler for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterSidestepSuccessTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterSidestepSuccessTacklee '%s' "), *m_pPlayer->GetName());

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_success_tacklee|tackle

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::sidestep_success_tacklee);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterSidestepSuccessTacklee:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	bool LoopAlways = false;

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if ((
			(m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_FOOLED) &&
			(AnimRec->m_SubType == "fooled")
			)
			||
			(
			(m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_PARTIAL_FAIL) &&
				(AnimRec->m_SubType == "partial_failed")
				)
			)//if bracket
			 //other possibilities for tackle_sidestep_type_variable like SSTT_PARTIAL_FAIL and SSTT_FOOLED should not be set, as that doesnot have any associated animation. It always uses animation 'aaa' which is essentially a junk animation.
		{
			tempRec.Add(AnimRec); //add only SSTT_NOT_FOOLED and SSTT_PARTIAL_SUCCESS

		}
		else
		{
			UE_LOG(LogTemp, Display, TEXT("No animation found, reset to null"));
		}
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
	m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup); //add to monitor if it doesnot have already.	
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateSidestepSuccessTacklee(const float deltaSecs)
{	
	//nothing...
	//bail out happens in event handler
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitSidestepSuccessTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitSidestepSuccessTacklee for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterSidestepSuccessTackler()
{

	UE_LOG(LogTemp, Display, TEXT("OnEnterSidestepSuccessTackler '%s' "), *m_pPlayer->GetName());

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_success_tackler|tackle

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::sidestep_success_tackler);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterSidestepSuccessTackler:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	bool LoopAlways = false;

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if ((
			(m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_FOOLED) &&
			(AnimRec->m_SubType == "fooled")
			)
			||
			(
			(m_pRUPlayerAnimation->GetTackleSidestepTypeVariable()->getValue() == SIDESTEP_TACKLE_TYPE::SSTT_PARTIAL_FAIL) &&
				(AnimRec->m_SubType == "partial_failed")
				)
			)//if bracket
		   //other possibilities for tackle_sidestep_type_variable like SSTT_PARTIAL_FAIL and SSTT_FOOLED should not be set, as that doesnot have any associated animation. It always uses animation 'aaa' which is essentially a junk animation.
		{
			tempRec.Add(AnimRec); //add only SSTT_NOT_FOOLED and SSTT_PARTIAL_SUCCESS			
		}
		else
		{
			UE_LOG(LogTemp, Display, TEXT("No animation found, reset to null"));
		}
	}
	UE_LOG(LogTemp, Display, TEXT("OnEnterSidestepSuccessTackler '%s' "), *m_pPlayer->GetName());
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
	m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup); //add to monitor if it doesnot have already.	
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateSidestepSuccessTackler(const float deltaSecs)
{	
    //nothing...
	//bail out happens in event handler
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitSidestepSuccessTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitSidestepSuccessTackler for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterStandardFailTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterStandardFailTacklee '%s' "), *m_pPlayer->GetName());

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::standard_fail_tacklee);

	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("StateMachine_Tackle_standard_fail_tacklee:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	bool LoopAlways = false;

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if ((m_pRUPlayerAnimation->GetTackle_dominance_var() == AnimRec->m_Dominance) &&
			(m_pRUPlayerAnimation->GettackleHeightVar() == AnimRec->m_ImpactArea))
		{
			tempRec.Add(AnimRec);
		}
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
	m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup); //add to monitor if it doesnot have already.	
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateStandardFailTacklee(const float deltaSecs)
{	
    //nothing...
	//bail out happens in event handler
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitStandardFailTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitStandardFailTacklee for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterStandardFailTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterStandardFailTackler '%s' "), *m_pPlayer->GetName());

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::standard_fail_tackler);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterStandardFailTackler:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	bool LoopAlways = false;

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if ((m_pRUPlayerAnimation->GetTackle_dominance_var() == AnimRec->m_Dominance) &&
			(m_pRUPlayerAnimation->GettackleHeightVar() == AnimRec->m_ImpactArea))
		{
			tempRec.Add(AnimRec);
		}
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
	m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup); //add to monitor if it doesnot have already.	
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateStandardFailTackler(const float deltaSecs)
{	
    //nothing...
	//bail out happens in event handler
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitStandardFailTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitStandardFailTackler for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterStandardSuccessTacklee() //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tacklee|tackle
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterStandardSuccessTacklee '%s' "), *m_pPlayer->GetName());

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::standard_success_tacklee);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterStandardSuccessTacklee:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	bool LoopAlways = false;

	if (m_TackleAnimationStateIsGround == true)
	{
		LoopAlways = true;
	}

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	//Get Ground animation if needed. Needs blending to be done, so i have marked the ground animation as invalid as it will appear to be standiing. 
	//Also weightParam: ControlParameters|one is always true. See player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tackler|ankle_eq_front1|ground|Blend2MatchEvents1
	bool GrdType = (m_TackleAnimationStateIsGround == true) ? true : false;

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if ((m_pRUPlayerAnimation->GetTackle_dominance_var() == AnimRec->m_Dominance) &&  //to choose between a2, a1, eq, d1, d2
			(m_pRUPlayerAnimation->GettackleHeightVar() == AnimRec->m_ImpactArea) && //to choose between ankle, waist and chest			
			(GrdType == AnimRec->m_IsGround))
		{
			tempRec.Add(AnimRec);
		}
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateStandardSuccessTacklee(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		bool *GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);

		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;

		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tacklee|ankle_eq_front|impact_ground
		if ((m_TackleAnimationStateIsGround == false) && RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup); //add to monitor if it doesnot have already.			

			//in the meantime attempt to fall on ground
			m_TackleAnimationStateIsGround = true;

			if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
			{
				m_pPlayer->GetAnimInstance()->BeginStruggle(m_CurrentModeTackles != ERugbyAnim_Mode_Tackles::standard_success_tacklee);
			}
		}

		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tacklee_getup durationInTime: 0.0
		else if (GetUpState && (*GetUpState == true) && (m_TackleAnimationStateIsGround == true)) //if we have a getup request
		{
			if (m_pPlayer->GetAttributes()->WasInvolvedInInjuryTackle())
			{
				m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup_injury);
			}
			else
			{
				m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup);
			}
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitStandardSuccessTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitStandardSuccessTacklee for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterStandardSuccessTackler() //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tackler|tackle
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterStandardSuccessTackler '%s' "), *m_pPlayer->GetName());

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::standard_success_tackler);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterStandardSuccessTackler:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	bool LoopAlways = false;
	if (m_TackleAnimationStateIsGround == true)
	{
		LoopAlways = true;
	}

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	//Get Ground animation if needed. Needs blending to be done, so i have marked the ground animation as invalid as it will appear to be standing.  weightParam: ControlParameters|one is always true. See player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tacklee|ankle_eq_front1|ground|Blend2MatchEvents1
	bool GrdType = (m_TackleAnimationStateIsGround == true) ? true : false;

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if ((m_pRUPlayerAnimation->GetTackle_dominance_var() == AnimRec->m_Dominance) && //to choose between a2, a1, eq, d1, d2
			(m_pRUPlayerAnimation->GettackleHeightVar() == AnimRec->m_ImpactArea) &&  //to choose between ankle, waist and chest			
			(GrdType == AnimRec->m_IsGround))
		{
			tempRec.Add(AnimRec);
		}
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateStandardSuccessTackler(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		//tackler going to ruck
		bool *GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);

		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;

		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tacklee|ankle_eq_front|impact_ground
		if ((m_TackleAnimationStateIsGround == false) && RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup); //add to monitor if it doesnot have already.			
			//in the meantime attempt to fall on ground
			m_TackleAnimationStateIsGround = true;

			if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
			{
				m_pPlayer->GetAnimInstance()->BeginStruggle(m_CurrentModeTackles != ERugbyAnim_Mode_Tackles::standard_success_tackler);
			}
		}
		else if ((m_TackleAnimationStateIsGround == true) && GetUpState && (*GetUpState == true)) //if we have a getup request
		{
			if (m_pRUPlayerAnimation->GetEnterRuckStateVariable()->getValue() > 0.5f) //entering a ruck, do fast getup
			{
				m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::fast_getup_tackler); //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tackler_getup1 durationInTime: 0.0
			}
			else
			{
				if (m_pPlayer->GetAttributes()->WasInvolvedInInjuryTackle())
				{
					m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup_injury); //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tackler_getup durationInTime: 0.0
				}
				else
				{
					m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup); //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tackler_getup durationInTime: 0.0
				}
			}
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitStandardSuccessTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitStandardSuccessTackler for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE

void RugbyAnimationStateMachine_Tackles::OnEnterStandardFailTwoTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterStandardFailTwoTacklee '%s' "), *m_pPlayer->GetName());

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::standard_fail_two_tacklee);

	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Warning, TEXT("StateMachine_Tackle_standard_fail_two_tacklee:TackleTypePtr is null"));
		UE_LOG(LogTemp, Warning, TEXT("TackleTypePtr is null for '%s' , no entry in data table found for two man tackle?"), *m_pPlayer->GetName());
		UE_DEBUG_BREAK();
		return;
	}

	bool LoopAlways = false;

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		tempRec.Add(AnimRec);

		// dominance and impact area variations are not used for two man tackle for now, save the best for next release
		/*
		if ((m_pRUPlayerAnimation->GetTackle_dominance_var() == AnimRec->m_Dominance) &&
			(m_pRUPlayerAnimation->GettackleHeightVar() == AnimRec->m_ImpactArea))
		{
			tempRec.Add(AnimRec);
		}*/
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
	m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup); //add to monitor if it doesnot have already.	
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateStandardFailTwoTacklee(const float deltaSecs)
{	
    //nothing...
	//bail out happens in event handler
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitStandardFailTwoTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitStandardFailTwoTacklee for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterStandardFailTwoTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterStandardFailTwoTackler '%s' "), *m_pPlayer->GetName());

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::standard_fail_two_tackler);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterStandardFailTackler:TackleTypePtr is null , two tackler animation not found in data table?"));
		UE_DEBUG_BREAK();
		return;
	}

	bool LoopAlways = false;

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		tempRec.Add(AnimRec);
		
		// dominance and impact area variations are not used for two man tackle for now, save the best for next release
		/*
		if ((m_pRUPlayerAnimation->GetTackle_dominance_var() == AnimRec->m_Dominance) &&
			(m_pRUPlayerAnimation->GettackleHeightVar() == AnimRec->m_ImpactArea))
		{
			tempRec.Add(AnimRec);
		}
		*/
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
	m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup); //add to monitor if it doesnot have already.	
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateStandardFailTwoTackler(const float deltaSecs)
{	
    //nothing...
	//bail out happens in event handler
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitStandardFailTwoTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitStandardFailTwoTackler for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterStandardSuccessTwoTacklee() //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tacklee|tackle
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterStandardSuccessTwoTacklee '%s' "), *m_pPlayer->GetName());

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::standard_success_two_tacklee);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterStandardSuccessTacklee:TackleTypePtr is null, two tacklee animation not found in data table?"));
		UE_DEBUG_BREAK();
		return;
	}

	bool LoopAlways = false;

	if (m_TackleAnimationStateIsGround == true)  //TODO?
	{
		LoopAlways = true;
	}

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	//Get Ground animation if needed. Needs blending to be done, so i have marked the ground animation as invalid as it will appear to be standiing. 
	//Also weightParam: ControlParameters|one is always true. See player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tackler|ankle_eq_front1|ground|Blend2MatchEvents1
	bool GrdType = (m_TackleAnimationStateIsGround == true) ? true : false;

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		tempRec.Add(AnimRec);

		// TODO dominance and impact area variations are not used for two man tackle for now, What about IsGround?
		/**
		if ((m_pRUPlayerAnimation->GetTackle_dominance_var() == AnimRec->m_Dominance) &&  //to choose between a2, a1, eq, d1, d2
			(m_pRUPlayerAnimation->GettackleHeightVar() == AnimRec->m_ImpactArea) && //to choose between ankle, waist and chest			
			(GrdType == AnimRec->m_IsGround))
		{
			tempRec.Add(AnimRec);
		}
		*/
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateStandardSuccessTwoTacklee(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		bool *GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);

		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;


		// TODO might need to check what's the condition for struggle loop anim ?
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tacklee|ankle_eq_front|impact_ground
		if ((m_TackleAnimationStateIsGround == false) && RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup); //add to monitor if it doesnot have already.			

			//in the meantime attempt to fall on ground
			m_TackleAnimationStateIsGround = true;

			if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
			{
				//TODO here is how to start a struggle loop, 
				m_pPlayer->GetAnimInstance()->BeginStruggle(m_CurrentModeTackles != ERugbyAnim_Mode_Tackles::standard_success_two_tacklee);
			}
		}

		// now we can play GetUp anim.
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tacklee_getup durationInTime: 0.0
		else if (GetUpState && (*GetUpState == true) && (m_TackleAnimationStateIsGround == true)) //if we have a getup request
		{
			if (m_pPlayer->GetAttributes()->WasInvolvedInInjuryTackle())
			{
				m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup_injury);
			}
			else
			{
				m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup);
			}
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitStandardSuccessTwoTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitStandardSuccessTwoTacklee for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterStandardSuccessTwoTackler() //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tackler|tackle
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterStandardSuccessTwoTackler '%s' "), *m_pPlayer->GetName());

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::standard_success_two_tackler);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterStandardSuccessTwoTackler:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	bool LoopAlways = false;
	if (m_TackleAnimationStateIsGround == true)
	{
		LoopAlways = true;
	}

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	//Get Ground animation if needed. Needs blending to be done, so i have marked the ground animation as invalid as it will appear to be standing.  weightParam: ControlParameters|one is always true. See player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tacklee|ankle_eq_front1|ground|Blend2MatchEvents1
	bool GrdType = (m_TackleAnimationStateIsGround == true) ? true : false;

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		tempRec.Add(AnimRec);
		//TODO might need to check IsGround ?
		/**
		if ((m_pRUPlayerAnimation->GetTackle_dominance_var() == AnimRec->m_Dominance) && //to choose between a2, a1, eq, d1, d2
			(m_pRUPlayerAnimation->GettackleHeightVar() == AnimRec->m_ImpactArea) &&  //to choose between ankle, waist and chest			
			(GrdType == AnimRec->m_IsGround))
		{
			tempRec.Add(AnimRec);
		}
		*/
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateStandardSuccessTwoTackler(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		//tackler going to ruck
		bool *GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);

		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;

		// TODO here it defines what is stuggle loop condition . 
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tacklee|ankle_eq_front|impact_ground
		if ((m_TackleAnimationStateIsGround == false) && RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup); //add to monitor if it doesnot have already.			
			//in the meantime attempt to fall on ground
			m_TackleAnimationStateIsGround = true;

			if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
			{
				m_pPlayer->GetAnimInstance()->BeginStruggle(m_CurrentModeTackles != ERugbyAnim_Mode_Tackles::standard_success_two_tackler);
			}
		}
		else if ((m_TackleAnimationStateIsGround == true) && GetUpState && (*GetUpState == true)) //if we have a getup request
		{
			if (m_pRUPlayerAnimation->GetEnterRuckStateVariable()->getValue() > 0.5f) //entering a ruck, do fast getup
			{
				m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::fast_getup_tackler); //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tackler_getup1 durationInTime: 0.0
			}
			else
			{
				if (m_pPlayer->GetAttributes()->WasInvolvedInInjuryTackle())
				{
					m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup_injury); //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tackler_getup durationInTime: 0.0
				}
				else
				{
					m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup); //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tackler_getup durationInTime: 0.0
				}
			}
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitStandardSuccessTwoTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitStandardSuccessTwoTackler for '%s'"), *m_pPlayer->GetName());
}

#endif

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterTryPushedTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterTryPushedTacklee '%s' "), *m_pPlayer->GetName());

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::try_pushed_tacklee);

	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterTryPushedTacklee:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	bool LoopAlways = false;

	if (m_TackleAnimationStateIsGround == true)
	{
		LoopAlways = true;
	}

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	//Get Ground animation if needed. Needs blending to be done, so i have marked the ground animation as invalid as it will appear to be standing.  weightParam: ControlParameters|one is always true. See player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tacklee|ankle_eq_front1|ground|Blend2MatchEvents1
	bool GrdType = (m_TackleAnimationStateIsGround == true) ? true : false;

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if ((m_pRUPlayerAnimation->GetTackle_dominance_var() == AnimRec->m_Dominance) && (AnimRec->m_IsGround == GrdType))
		{
			tempRec.Add(AnimRec);
		}
	}

	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateTryPushedTacklee(const float deltaSecs)
{

	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		bool *GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);

		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|try_pushed_tacklee|pushed_a2_front1|impact_ground
		if ((m_TackleAnimationStateIsGround == false) && RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData) && (m_usableTackleAnimRecs.usableRecList.Num() == 0))
		{
			m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup);

			m_TackleAnimationStateIsGround = true;

			if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
			{
				m_pPlayer->GetAnimInstance()->BeginStruggle(m_CurrentModeTackles != ERugbyAnim_Mode_Tackles::try_pushed_tacklee);
			}
		}

		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|try_pushed_getup durationInTime: 0.0
		else if (GetUpState && (*GetUpState == true) && (m_TackleAnimationStateIsGround == true)) //if we have a getup request
		{
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup);
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitTryPushedTacklee()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitTryPushedTacklee for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterTryPushedTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterTryPushedTackler '%s' "), *m_pPlayer->GetName());

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::try_pushed_tackler);

	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterTryPushedTackler:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}	
	
	bool LoopAlways = false;

	if (m_TackleAnimationStateIsGround == true)
	{
		LoopAlways = true;
	}

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	bool GrdType = (m_TackleAnimationStateIsGround == true) ? true: false;

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if ((m_pRUPlayerAnimation->GetTackle_dominance_var() == AnimRec->m_Dominance) && (AnimRec->m_IsGround == GrdType))
		{
			tempRec.Add(AnimRec);
		}
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnUpdateTryPushedTackler(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		bool *GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);

		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;

		if ((m_TackleAnimationStateIsGround == false) && RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup);

			m_TackleAnimationStateIsGround = true;

			if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
			{
				m_pPlayer->GetAnimInstance()->BeginStruggle(m_CurrentModeTackles != ERugbyAnim_Mode_Tackles::try_pushed_tackler);
			}
		}

		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|try_pushed_tackler_getup	 durationInTime: 0.0
		else if (GetUpState && (*GetUpState == true) && (m_TackleAnimationStateIsGround == true)) //if we have a getup request
		{
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup);
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitTryPushedTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitTryPushedTackler for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterDiveMissTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnEnterDiveMissTackler '%s' "), *m_pPlayer->GetName());

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::dive_miss_tackler);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterDiveMissTackler:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}	

	bool LoopAlways = false;

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		tempRec.Add(AnimRec);
	}
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateDiveMissTackler(const float deltaSecs)
{	
	//bail out happens in event handler
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitDiveMissTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitDiveMissTackler for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================
/*
sourceTransitions
00: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler_fast_getup_tackler
01: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tackler_getup1
*/
void RugbyAnimationStateMachine_Tackles::OnEnterFastGetupTackler()
{
	m_CurrentModeTackles = ERugbyAnim_Mode_Tackles::fast_getup_tackler;

	UE_LOG(LogTemp, Display, TEXT("OnEnterFastGetupTackler '%s' "), *m_pPlayer->GetName());

	m_tackleMontageData.Reset();

	FAnimMontageInstance* pMontageInstance = m_pSMMgr->PlayClosestAnimation(CLOSEST_ANIM_TYPE::GETUP_FAST_NOBALL,
		IS_GETUP_FROM_STRUGGLE() ? GETUP_FROM_STRUGGLE_BLEND_TIME : GETUP_DEFAULT_BLEND_TIME);

	ensureAlways(pMontageInstance != nullptr);
	if (pMontageInstance)
	{
		m_tackleMontageData.pMontageInstance = pMontageInstance;
		m_tackleMontageData.montageUniqueID = pMontageInstance->GetInstanceID();
	}	

	if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
	{
		// #anim_todo_blend_times
		m_pPlayer->GetAnimInstance()->EndStruggle(SMDefaultBlendOutTime);
	}
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateFastGetupTackler(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|getup_null2	 durationInTime: 0.25
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachineMgr::OnUpdateFastGetupTackler, setting tackle mode to NULL from '%d' for '%s'"), (int)m_CurrentModeTackles, *m_pPlayer->GetName());

			//we dont need getup for these tackle states, as they dont depend on 'getup' request, so if a stray getup request is there, reset it...	
			m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_getup));

			m_ForceStopBlendOutTime = 0.25f;
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
		}
	}
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnExitFastGetupTackler()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitFastGetupTackler for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterGetupTackle() //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|getup
{
	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|getup
	// 
	//sourceTransitions:
	//00: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee_getup -> ONRequest Getup. See exit of the corresponding tackle.
	//01: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler_getup -> ONRequest Getup. See exit of the corresponding tackle.
	//02: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_fail_tacklee_getup -> ONRequest Getup. See exit of the corresponding tackle.
	//03: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_fail_tackler_getup -> ONRequest Getup. See exit of the corresponding tackle.
	//04: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_success_tackler_getup -> userdata == 302 See RugbyAnimationStateMachine_Tackles::AnimationEvent
	//05: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|head_high_tacklee_getup -> Condition_603_CrossedDurationFraction,  ControlParameters|withball_state< 0.5
	//06: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tacklee_getup -> OnRequest Getup  See exit of the corresponding tackle.
	//07: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_fail_tackler_getup -> OnRequest Getup  See exit of the corresponding tackle.
	//08: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_success_tackler_getup-> userdata == 302  See RugbyAnimationStateMachine_Tackles::AnimationEvent
	//09: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_fail_tackler_getup -> userdata == 302  See RugbyAnimationStateMachine_Tackles::AnimationEvent
	//10: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tacklee_getup -> OnRequest Getup  See exit of the corresponding tackle.
	//11: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_success_tackler_getup  -> OnRequest Getup  See exit of the corresponding tackle.
	//12: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|try_pushed_getup -> OnRequest Getup  See exit of the corresponding tackle.
	//13: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|try_pushed_tackler_getup -> OnRequest Getup  See exit of the corresponding tackle.
	//
	
	UE_LOG(LogTemp, Warning, TEXT("OnEnterGetupTackle for: '%s'"), *m_pPlayer->GetName());

	bool bGettingUpFromTwoManTackle = false;

	if (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::standard_success_two_tacklee ||
		m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::standard_success_two_tackler ||
		m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::standard_fail_two_tacklee ||
		m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::standard_fail_two_tackler)
	{
		bGettingUpFromTwoManTackle = true;
	}
	
	m_CurrentModeTackles = ERugbyAnim_Mode_Tackles::Getup;

	m_tackleMontageData.Reset();

#if defined ENABLE_TWO_MAN_TACKLE && defined ENABLE_TWO_MAN_TACKLE
	
	if (bGettingUpFromTwoManTackle)
	{
		UE_LOG(LogTemp, Warning, TEXT("OnEnterGetupTackle: multi tackle for %s. so we will not play getup anim!"), *m_pPlayer->GetName());
		
		m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
	}
	else
	{
#endif
		
	FAnimMontageInstance* pMontageInstance = nullptr;

	if (m_pRUPlayerAnimation->GetWithBallStateVariable()->getValue())
	{
		pMontageInstance = m_pSMMgr->PlayClosestAnimation(CLOSEST_ANIM_TYPE::GETUP_TACKLER_WITHBALL,
			IS_GETUP_FROM_STRUGGLE() ? GETUP_FROM_STRUGGLE_BLEND_TIME : GETUP_DEFAULT_BLEND_TIME);
	}
	else
	{
		pMontageInstance = m_pSMMgr->PlayClosestAnimation(CLOSEST_ANIM_TYPE::GETUP_TACKLER_NOBALL,
			IS_GETUP_FROM_STRUGGLE() ? GETUP_FROM_STRUGGLE_BLEND_TIME : GETUP_DEFAULT_BLEND_TIME);
	}

	ensureAlways(pMontageInstance != nullptr);
	if (pMontageInstance)
	{
		m_tackleMontageData.pMontageInstance = pMontageInstance;
		m_tackleMontageData.montageUniqueID = pMontageInstance->GetInstanceID();
	}

	m_tackleMontageData.montageUniqueID = m_tackleMontageData.pMontageInstance->GetInstanceID();

#if defined ENABLE_TWO_MAN_TACKLE && defined ENABLE_TWO_MAN_TACKLE
	}
#endif
	
	m_pSMMgr->m_AnimationRequestMonitorList.Remove(SM_getup);

	m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_getup));

	if ( m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
	{
		// #anim_todo_blend_times
		m_pPlayer->GetAnimInstance()->EndStruggle(SMDefaultBlendOutTime);
	}
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateGetupTackle(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|getup_null1 durationInTime: 0.25
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			//we dont need getup for these tackle states, as they dont depend on 'getup' request, so if a stray getup request is there, reset it...
			bool *GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);

			if (m_pSMMgr->m_AnimationRequestMonitorList.Contains(SM_getup) && GetUpState && (*GetUpState == true))
			{
				m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_getup));
			}
			m_ForceStopBlendOutTime = 0.25f;
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitGetupTackle()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitGetupTackle for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterGetupTackleInjury() //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|getup
{
		UE_LOG(LogTemp, Warning, TEXT("OnEnterGetupTackleInjury for: '%s'"), *m_pPlayer->GetName());

	m_CurrentModeTackles = ERugbyAnim_Mode_Tackles::Getup_injury;

	m_tackleMontageData.Reset();

	FAnimMontageInstance* pMontageInstance = nullptr;

	if (m_pRUPlayerAnimation->GetWithBallStateVariable()->getValue())
	{
		pMontageInstance = m_pSMMgr->PlayClosestAnimation(CLOSEST_ANIM_TYPE::GETUP_TACKLER_WITHBALL_INJURED,
			IS_GETUP_FROM_STRUGGLE() ? GETUP_FROM_STRUGGLE_BLEND_TIME : GETUP_DEFAULT_BLEND_TIME);
	}
	else
	{
		pMontageInstance = m_pSMMgr->PlayClosestAnimation(CLOSEST_ANIM_TYPE::GETUP_TACKLER_NOBALL_INJURED,
			IS_GETUP_FROM_STRUGGLE() ? GETUP_FROM_STRUGGLE_BLEND_TIME : GETUP_DEFAULT_BLEND_TIME);
	}

	ensureAlways(pMontageInstance != nullptr);
	if (pMontageInstance)
	{
		m_tackleMontageData.pMontageInstance = pMontageInstance;
		m_tackleMontageData.montageUniqueID = pMontageInstance->GetInstanceID();
	}

	m_tackleMontageData.montageUniqueID = m_tackleMontageData.pMontageInstance->GetInstanceID();

	m_pSMMgr->m_AnimationRequestMonitorList.Remove(SM_getup);

	m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_getup));

	if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
	{
		// #anim_todo_blend_times
		m_pPlayer->GetAnimInstance()->EndStruggle(SMDefaultBlendOutTime);

		m_pPlayer->GetAttributes()->SetInvolvedInInjuryTackle(false); //GGS SRA: resetting the status here
	}
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateGetupTackleInjury(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|getup_null1 durationInTime: 0.25
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			//we dont need getup for these tackle states, as they dont depend on 'getup' request, so if a stray getup request is there, reset it...
			bool* GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_getup);

			if (m_pSMMgr->m_AnimationRequestMonitorList.Contains(SM_getup) && GetUpState && (*GetUpState == true))
			{
				m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_getup));
			}
			m_ForceStopBlendOutTime = 0.25f;
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitGetupTackleInjury()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitGetupTackleInjury for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterGetupPlayTheBall() //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|getup
{
	UE_LOG(LogTemp, Warning, TEXT("OnEnterGetupPlayTheBall for: '%s'"), *m_pPlayer->GetName());

	bool bIsGettingUpFromTwoManTackle = false;
	if (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::standard_success_two_tacklee ||
		m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::standard_success_two_tackler ||
		m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::standard_fail_two_tacklee ||
		m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::standard_fail_two_tackler)
	{
		bIsGettingUpFromTwoManTackle = true;
	}
	
	m_CurrentModeTackles = ERugbyAnim_Mode_Tackles::play_the_ball_ground;

	m_tackleMontageData.Reset();
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
	if (bIsGettingUpFromTwoManTackle)
	{
		
		UE_LOG(LogTemp, Warning, TEXT("OnEnterGetupPlayTheBall: multi tackle for %s. so we will not play getup anim!"), *m_pPlayer->GetName());
		
		m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
	}
	else
	{
#endif
	FAnimMontageInstance* pMontageInstance = nullptr;

	if (m_pRUPlayerAnimation->GetWithBallStateVariable()->getValue())
	{
		pMontageInstance = m_pSMMgr->PlayClosestAnimation(CLOSEST_ANIM_TYPE::GETUP_TACKLER_WITHBALL,
			IS_GETUP_FROM_STRUGGLE() ? GETUP_FROM_STRUGGLE_BLEND_TIME : GETUP_DEFAULT_BLEND_TIME);
	}
	else
	{
		pMontageInstance = m_pSMMgr->PlayClosestAnimation(CLOSEST_ANIM_TYPE::GETUP_TACKLER_NOBALL,
			IS_GETUP_FROM_STRUGGLE() ? GETUP_FROM_STRUGGLE_BLEND_TIME : GETUP_DEFAULT_BLEND_TIME);
	}

	ensureAlways(pMontageInstance != nullptr);
	if (pMontageInstance)
	{
		m_tackleMontageData.pMontageInstance = pMontageInstance;
		m_tackleMontageData.montageUniqueID = pMontageInstance->GetInstanceID();
	}

	m_tackleMontageData.montageUniqueID = m_tackleMontageData.pMontageInstance->GetInstanceID();

#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
}
#endif
	
	m_pSMMgr->m_AnimationRequestMonitorList.Remove(SM_play_the_ball_getup);

	m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_play_the_ball_getup));

	if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
	{
		// #anim_todo_blend_times
		m_pPlayer->GetAnimInstance()->EndStruggle(SMDefaultBlendOutTime);
	}
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateGetupPlayTheBall(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|getup_null1 durationInTime: 0.25
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			//we dont need getup for these tackle states, as they dont depend on 'getup' request, so if a stray getup request is there, reset it...
			bool* GetUpState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_play_the_ball_getup);

			if (m_pSMMgr->m_AnimationRequestMonitorList.Contains(SM_play_the_ball_getup) && GetUpState && (*GetUpState == true))
			{
				m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_play_the_ball_getup));
			}
			m_ForceStopBlendOutTime = 0.25f;
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitGetupPlayTheBall()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitGetupPlayTheBall for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterQuickTapTackle()
{
	m_CurrentModeTackles = ERugbyAnim_Mode_Tackles::quick_tap;

	UE_LOG(LogTemp, Display, TEXT("OnEnterQuickTapTackle '%s' "), *m_pPlayer->GetName());
	/* 
	sourceNodes
		00: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|quick_tap|PlaySpeedModifier1
		01: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|quick_tap|PlaySpeedModifier2
		02: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|quick_tap|PlaySpeedModifier3
		03: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|quick_tap|MirrorTransforms1
	*/

	m_tackleMontageData.Reset();

	/*
	Parent: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|quick_tap|PlaySpeedModifier1
	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|quick_tap|Operator2Float1
	inputParamA: ControlParameters|half
	inputParamB: ControlParameters|quick_tap
	operation: 5.   Despite how this looks in RC3 code operation 5 is MAX, not MIN. The operation values appear to have an offset of 1 which can seen in other nodes that use the 0 operator.
	I believe the intention is to have the get up animation play slowly to allow a bit more time for the user to make an input decision, 
	after which play rate is returned to 1. (See RURoleTapRestart::UpdateLogic)
	*/

	float PlaySpeed = FMath::Max(0.5f, m_pRUPlayerAnimation->GetQuickTapVariable()->getValue());
	float BlendInTime = 0.5f; //effectively it's 1.0 sec as it plays at half speed.
	float BlendOutTime = 1.0f; //blendout for struggle (keeping a long blend duration to allow it blend smoother)
	FAnimMontageInstance* pMontageInstance = m_pSMMgr->PlayClosestAnimation(CLOSEST_ANIM_TYPE::GETUP_QUICKTAP_WITHBALL,
		BlendInTime, false, PlaySpeed);

	ensureAlways(pMontageInstance != nullptr);
	if (pMontageInstance)
	{
		m_tackleMontageData.pMontageInstance = pMontageInstance;
		m_tackleMontageData.montageUniqueID = pMontageInstance->GetInstanceID();
	}	

	m_pSMMgr->m_AnimationRequestMonitorList.Remove(SM_getup);

	if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
	{
		// #anim_todo_blend_times
		m_pPlayer->GetAnimInstance()->EndStruggle(BlendOutTime);
	}
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateQuickTapTackle(const float deltaSecs)
{	
	if (m_tackleMontageData.pMontageInstance)
	{
		float PlaySpeed = FMath::Max(0.5f, m_pRUPlayerAnimation->GetQuickTapVariable()->getValue());
		m_tackleMontageData.pMontageInstance->SetPlayRate(PlaySpeed);
	}
	//nothing...
	//bail out happens in event handler	
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitQuickTapTackle()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitQuickTapTackle for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterTryTackle()
{
	FName Subtype = ""; //1 is tacklee, 2 is tackler..

	m_CurrentModeTackles = ERugbyAnim_Mode_Tackles::Try;

	UE_LOG(LogTemp, Display, TEXT("OnEnterTryTackle '%s' "), *m_pPlayer->GetName());	

	RUActionTacklee* tacklee_action = m_pPlayer->GetActionManager()->GetAction<RUActionTacklee>();
	RUActionTackler* tackler_action = m_pPlayer->GetActionManager()->GetAction<RUActionTackler>();
	MABASSERT(tacklee_action || tackler_action);

	ARugbyCharacter* tacklee = nullptr;
	ARugbyCharacter* tackler = nullptr;

	if (tacklee_action && tacklee_action->IsRunning() && tacklee_action->GetTackleResult().tacklee == m_pPlayer)
	{
		Subtype = "Tacklee";
	}
	else if (tackler_action && tackler_action->IsRunning() && tackler_action->GetTackleResult().tacklers[0] == m_pPlayer)
	{
		Subtype = "Tackler";
	}
	else
	{
		UE_DEBUG_BREAK();
		return;
	}

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::Try);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnter_StateMachine_Tackle_Try:TackleTypePtr is null"));
		return;
	}
	
	bool LoopAlways = false;

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	TRY_TACKLE_TYPE tryProbability = ( TRY_TACKLE_TYPE ) (FMath::RoundToInt ( m_pRUPlayerAnimation->GetTryProbabilityVariable()->getValue()));

	ensureAlways ((tryProbability >= TRY_TACKLE_TYPE::TTT_SUCCESS_LIKELY) && (tryProbability <= TRY_TACKLE_TYPE::TTT_HELDUP));

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if ((AnimRec->m_SubType == Subtype) && (AnimRec->m_TryTackleType == tryProbability))
		{
			tempRec.Add(AnimRec);
		}
	}
	
	
	PlaySelectedTackleAnimation(tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateTryTackle(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|try_null  //durationInTime: 0.10000000149011612
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachineMgr::OnUpdateTryTackle, setting tackle mode to NULL from '%d' for '%s'"), (int)m_CurrentModeTackles, *m_pPlayer->GetName());

			//we dont need getup for these tackle states, as they dont depend on 'getup' request, so if a stray getup request is there, reset it...	
			m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_getup));
			m_ForceStopBlendOutTime = 0.10000000149011612f;
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitTryTackle()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitTryTackle for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterTryCornerTackle()
{
	m_CurrentModeTackles = ERugbyAnim_Mode_Tackles::try_corner;

	UE_LOG(LogTemp, Display, TEXT("OnEnterTryCornerTackle '%s' "), *m_pPlayer->GetName());

	FName Subtype = ""; //1 is tacklee, 2 is tackler..

	RUActionTacklee* tacklee_action = m_pPlayer->GetActionManager()->GetAction<RUActionTacklee>();
	RUActionTackler* tackler_action = m_pPlayer->GetActionManager()->GetAction<RUActionTackler>();
	MABASSERT(tacklee_action || tackler_action);

	ARugbyCharacter* tacklee = nullptr;
	ARugbyCharacter* tackler = nullptr;

	if (tacklee_action && tacklee_action->IsRunning() && tacklee_action->GetTackleResult().tacklee == m_pPlayer)
	{
		Subtype = "Tacklee";
	}
	else if (tackler_action && tackler_action->IsRunning() && tackler_action->GetTackleResult().tacklers[0] == m_pPlayer)
	{
		Subtype = "Tackler";
	}
	else
	{
		UE_DEBUG_BREAK();
		return;
	}

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::try_corner);
	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnter_StateMachine_Tackle_try_corner:TackleTypePtr is null"));
		return;
	}

	bool LoopAlways = false;

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if (AnimRec->m_SubType == Subtype)
		{
			tempRec.Add(AnimRec);
		}
	}	

	PlaySelectedTackleAnimation (tempRec, SMDefaultBlendInTime, LoopAlways);
}

//----------------------------------------------------------------------------------------------------
void RugbyAnimationStateMachine_Tackles::OnUpdateTryCornerTackle(const float deltaSecs)
{	
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|try_corner_null //durationInTime: 0.10000000149011612
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))			
		{
			UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachineMgr::OnUpdateTryCornerTackle, setting tackle mode to NULL from '%d' for '%s'"), (int)m_CurrentModeTackles, *m_pPlayer->GetName());

			//we dont need getup for these tackle states, as they dont depend on 'getup' request, so if a stray getup request is there, reset it...	
			m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_getup));
			m_ForceStopBlendOutTime = 0.10000000149011612f;
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitTryCornerTackle()
{
	UE_LOG(LogTemp, Display, TEXT("OnExitTryCornerTackle for '%s'"), *m_pPlayer->GetName());
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::PlayContestedTackleAnimation(
	FName	SubType,
	bool LoopAlways /*= false*/, float BlendInTime /*= SMDefaultBlendInTime*/)
{
	ERugbyAnim_Mode_Tackles ContestedTackleType = ERugbyAnim_Mode_Tackles::null;

	if (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::contested_tacklee)
	{
		ContestedTackleType = ERugbyAnim_Mode_Tackles::contested_tacklee;
	}
	else if (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::contested_tackler)
	{
		ContestedTackleType = ERugbyAnim_Mode_Tackles::contested_tackler;
	}
	else
	{
		UE_DEBUG_BREAK();
		UE_LOG(LogTemp, Display, TEXT("PlayContestedTackleAnimation: Invalid TacleType"));
		return;
	}

	const FRugbyTackleTypeRecord *TackleTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetTackleTypeRec(ContestedTackleType);

	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnterContestedTacklee:TackleTypePtr is null"));
		UE_DEBUG_BREAK();
		return;
	}

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if (AnimRec->m_SubType == SubType)			
		{
			tempRec.Add(AnimRec);
		}
	}

	PlaySelectedTackleAnimation(tempRec, BlendInTime, LoopAlways);
}

//==========================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterContestedTackleInvalidState()
{
	//nothing
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnUpdateContestedTackleInvalidState(const float deltaSecs)
{
	//nothing
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitContestedTackleInvalidState()
{
	//nothing
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnEnterContestedTackleImpact()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());

	//defaultNode: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee|tackle|tacklee_driven_impact|Tackle1	
	FName SubType = "impact";

	PlayContestedTackleAnimation(SubType);
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnUpdateContestedTackleImpact(const float deltaSecs)
{	
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee|tackle|tacklee_driven_impact_tacklee_driven_engaged durationInTime: 0.10000000149011612	
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle|tackler_driven_impact_tackler_driven_engaged durationInTime: 0.25

		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
			// player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee|tackle|tacklee_driven_engaged|Movement1
			m_ContestedTackleStateMachine.ChangeState(EContestedTackleAnimationState::tackle_driven_engaged);
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitContestedTackleImpact()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	//nothing
}

//==========================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterContestedTackleEngaged()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	if (m_pRUPlayerAnimation && UOBJ_IS_VALID(m_pPlayer) && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
	{
	
		float durationInTime = 0.25f; //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle|tackler_driven_impact_tackler_driven_engaged
		
		if (m_TackleStateMachine.GetCurrentStateKey() == ERugbyAnim_Mode_Tackles::contested_tacklee) 
		{
			durationInTime = 0.10000000149011612f; //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee|tackle|tacklee_driven_impact_tacklee_driven_engaged
		}
		URugbyCharacterAnimInstance* pAnimInstance = m_pPlayer->GetAnimInstance();

		pAnimInstance->StopAnimationGroup(MONTAGE_GROUP::ALL, durationInTime);

		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee|tackle|tacklee_driven_engaged|BlendNMatchEvents1
		wwDB_DTBLENDNTYPE_ENUM tackleBlend = m_TackleStateMachine.GetCurrentStateKey() == ERugbyAnim_Mode_Tackles::contested_tackler ? wwDB_DTBLENDNTYPE_ENUM::DRIVEN_TACKLER : wwDB_DTBLENDNTYPE_ENUM::DRIVEN_TACKLEE;
		m_pActiveBlendN = pAnimInstance->CreateBlendN(tackleBlend, m_pRUPlayerAnimation->GetAnimationLibrary(), URugbyCharacterAnimInstance::FULL_BODY_TACKLE_SLOT_NAME);
		if (m_pActiveBlendN)
		{			
			m_pActiveBlendN->Start(durationInTime);
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnUpdateContestedTackleEngaged(const float deltaSecs)
{
	bool *BreakOutState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_driven_breakout);
	bool *TakeDownstate = m_pSMMgr->m_AnimationRequestNameState.Find(SM_driven_takedown);

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee|tackle|tacklee_driven_engaged_tacklee_driven_breakout durationInTime: 0.10000000149011612
	if (m_pSMMgr->m_AnimationRequestMonitorList.Contains(SM_driven_breakout) && BreakOutState && (*BreakOutState == true))
	{
		//durationInTime: 0.10000000149011612
		//destinationNode: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee|tackle|tacklee_driven_breakout|Tackle1
		m_ContestedTackleStateMachine.ChangeState(EContestedTackleAnimationState::tackle_driven_breakout);
	}
	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee|tackle|tacklee_driven_engaged_tacklee_driven_takedown durationInTime: 0.10000000149011612
	else if (m_pSMMgr->m_AnimationRequestMonitorList.Contains(SM_driven_takedown) && TakeDownstate && (*TakeDownstate == true))
	{
		//durationInTime: 0.10000000149011612
		//destinationNode: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee|tackle|tacklee_driven_takedown|Tackle1
		m_ContestedTackleStateMachine.ChangeState(EContestedTackleAnimationState::tackle_driven_takedown);
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitContestedTackleEngaged()
{
	if (m_pActiveBlendN)
	{
		m_pActiveBlendN->Stop(SMDefaultBlendOutTime);
	}
	m_pActiveBlendN = nullptr;

	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//==========================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterContestedTackleTakeDown()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle|tackler_driven_engaged_tackler_driven_takedown durationInTime: 0.10000000149011612
	
	m_pSMMgr->m_AnimationRequestMonitorList.Remove(SM_driven_takedown);

	bool *TakeDownstate = m_pSMMgr->m_AnimationRequestNameState.Find(SM_driven_takedown);
	if (TakeDownstate && (*TakeDownstate == true))
	{
		m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_driven_takedown));
	}	

	FName SubType = "takedown";

	PlayContestedTackleAnimation(SubType, false, 0.10000000149011612f);
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnUpdateContestedTackleTakeDown(const float deltaSecs)
{
	if (!m_usableTackleAnimRecs.usableRecList.Num()) //wait for tackle alignment to happen in postanim
	{
		EMontageCrossDurationData MontageStoppedData = EMontageCrossDurationData::eInvalidState;
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee|tackle|tacklee_driven_takedown_ground durationInTime: 0.0
		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle|tackler_driven_takedown_ground durationInTime: 0.0
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_tackleMontageData.pMontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance())) //destinationNode: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee|tackle|ground|Blend2MatchEvents1
			{
				m_pPlayer->GetAnimInstance()->BeginStruggle(m_CurrentModeTackles != ERugbyAnim_Mode_Tackles::contested_tackler);
			}
			m_ContestedTackleStateMachine.ChangeState(EContestedTackleAnimationState::ground);
		}
	}
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitContestedTackleTakeDown()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	//nothing
}

//==========================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterContestedTackleBreakOut()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle|tackler_driven_engaged_tackler_driven_breakout durationInTime: 0.10000000149011612	
	m_pSMMgr->m_AnimationRequestMonitorList.Remove(SM_driven_breakout);

	bool *BreakOutState = m_pSMMgr->m_AnimationRequestNameState.Find(SM_driven_breakout);
	if (BreakOutState && (*BreakOutState == true))
	{
		m_pSMMgr->ResetRequestListValue(const_cast <FString&> (SM_driven_breakout));
	}

	FName SubType = "breakout";
	PlayContestedTackleAnimation(SubType, false, 0.10000000149011612f);
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnUpdateContestedTackleBreakOut(const float deltaSecs)
{
	//nothing
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitContestedTackleBreakOut()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	//nothing
}

//==========================================================================

void RugbyAnimationStateMachine_Tackles::OnEnterContestedTackleGround()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	
	//FName SubType = "ground"; //use player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle|ground|trstruggle01	

	m_pSMMgr->m_AnimationRequestMonitorList.AddUnique(SM_getup);

	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler|tackle|tackler_driven_takedown_ground durationInTime: 0.0
	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee|tackle|tacklee_driven_takedown_ground durationInTime: 0.0
	//PlayContestedTackleAnimation(SubType, true, 0.0f); //handled in AnimGraph
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnUpdateContestedTackleGround(const float deltaSecs)
{
	//nothing
}

//----------------------------------------------------------------------------------------------------

void RugbyAnimationStateMachine_Tackles::OnExitContestedTackleGround()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	//nothing
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================
//end of statemachine

//=========================================================================================================================================================================
//=========================================================================================================================================================================
//
//some generic tackle Utilities
//
//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::PlaySelectedTackleAnimation(TArray<FRugbyTackleBlendAnimRecord*>& Rec, float BlendInTime, bool LoopAlways)
{
#if wwDEBUG_ARB
	if (IsTransitionActive())
	{
		wwDO_NOTHING;
	}
#endif

	m_usableTackleAnimRecs.usableRecList.Reset();
	//it is necessary to set here because the tackle_usableAnimationList is just added to an array here and processed next frame. So if in the same frame
	//someone overwrites the mode tackle, then animation end event will process it incorrectly.
	m_usableTackleAnimRecs.tackleType = m_CurrentModeTackles; 

	bool IsContactNode = false;	

	ensureAlways (Rec.Num() > 0);

	if (Rec.Num())
	{
		//TArray<FRugbyTackleBlendAnimRecord*> usableAnimationList;
		for (auto &SelRec : Rec)
		{
			if (SelRec->m_pAnimSequence)
			{
				m_usableTackleAnimRecs.usableRecList.Add(SelRec);
				if (SelRec->m_animatedContactList.Num())
				{
					IsContactNode = true; //dont break here, as we still need tackle_usableAnimationList to get populated...
				}
			}
		}

		ensureAlways(m_usableTackleAnimRecs.usableRecList.Num() > 0);

		if (m_usableTackleAnimRecs.usableRecList.Num() > 0)
		{			
			if (IsContactNode) //find bestindex for contact Node.... for rest use Index 0 since the caller of this method would already have picked the best.
			{				
				UE_LOG(LogTemp, Display, TEXT("PlaySelectedTackleAnimation: is of type 'RugNode_140_Tackle' or 'RugNode_160_TryTackle Node' for Player '%s'"), *m_pPlayer->GetName());
				return; //well, let this get handled by StateMachine_SelectTackleNode or StateMachine_UpdatePickTryTackles
			}
			
			FRugbyTackleBlendAnimRecord* pAnimRec = m_usableTackleAnimRecs.usableRecList[0];//Use Index 0 since the caller of this method would already have picked the best.

			//ensureAlways(m_tackle_usableAnimation.tackle_usableAnimationList.Num() == 1);
			
			UE_LOG(LogTemp, Display, TEXT("PlaySelectedTackleAnimation: '%s' for '%s' TotalNodes: '%d'"), *(pAnimRec->m_stateMachinePath), *m_pPlayer->GetName(), m_usableTackleAnimRecs.usableRecList.Num());

			ensureAlways(pAnimRec->m_pAnimSequence != nullptr);

			m_tackleMontageData.Reset();

			m_tackleMontageData.pTackleAnimRecord = pAnimRec;

			FAnimMontageInstance* pMontageInstance = m_pSMMgr->PlayStateMachineAnimationMontage(this, pAnimRec, 0.0f, BlendInTime, LoopAlways);

			ensureAlways(pMontageInstance != nullptr);

			if (pMontageInstance)
			{
				m_tackleMontageData.pMontageInstance = pMontageInstance;
				m_tackleMontageData.montageUniqueID = pMontageInstance->GetInstanceID();
			}

			m_usableTackleAnimRecs.usableRecList.Reset();//make sure we clear this as we have already processed..
		}
		else
		{
			UE_LOG(LogTemp, Display, TEXT("PlaySelectedTackleAnimation: Animations are missing."));
		}
	}
	else
	{
		m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
		UE_LOG(LogTemp, Warning, TEXT("PlaySelectedTackleAnimation: Number of Animation record is zero, resetting the statemachine to NULL"));
	}
}

//===============================================================================
//===============================================================================
FRugbyTackleBlendAnimRecord* RugbyAnimationStateMachine_Tackles::PickBestTackleAnimation(const TArray<FRugbyTackleBlendAnimRecord*>& contactAnimationList, FVector contactVector)
{
	FRugbyTackleBlendAnimRecord* pBestTackleRec = nullptr;
	float bestProjection = -MAX_FLT; // always need to find an animation
	contactVector.Normalize();

	for (int32 i = 0; i < contactAnimationList.Num(); ++i)
	{
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
		if (ShouldSkipCurrentAnimation(contactAnimationList[i]))
			continue;
#endif
		FVector tackleDirection = contactAnimationList[i]->m_staticContact.translation;
		tackleDirection.Normalize();

		float projection = FVector::DotProduct(contactVector, tackleDirection);
		if (projection > bestProjection)
		{
			pBestTackleRec = contactAnimationList[i];
			bestProjection = projection;
		}
	}
	return pBestTackleRec;
}

bool RugbyAnimationStateMachine_Tackles::ShouldSkipCurrentAnimation(FRugbyTackleBlendAnimRecord* selectedContactAnimation) const
{
	bool bShouldSkip = false;
	for (ChosenTackleAnimations& preselectedTackleData : RUPlayerAnimation::chosen_tackle_animations)
	{
		if (RUPlayerAnimation::chosen_tackle_animations[0].bestTackleRecord->m_animName.Compare(selectedContactAnimation->m_animName) == 0)
		{
			// this is a preselected tackle animation, so we can skip it.
			UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_Tackles::PickBestTackleAnimation: Skipping preselected tackle animation '%s'"), *selectedContactAnimation->m_animName);
			bShouldSkip = true;
			break;
		}
	}
	return bShouldSkip;
}


FRugbyTackleBlendAnimRecord* RugbyAnimationStateMachine_Tackles::PickBestSupportTacklerAnimation(const TArray<FRugbyTackleBlendAnimRecord*>& contactAnimationList, FVector contactVector, FString supportTacklerAnimName)
{
	// we have support tackler animation from back, front and left, so we can pick the best based on their projection.
	// anim asset name requirement: prefix should be the same. suffix can be _left _back or _right
	FRugbyTackleBlendAnimRecord* pBestTackleRec = nullptr;
	float bestProjection = -MAX_FLT; // always need to find an animation
	contactVector.Normalize();

	for (int32 i = 0; i < contactAnimationList.Num(); ++i)
	{
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
		if (ShouldSkipCurrentAnimation(contactAnimationList[i]))
			continue;
#endif
		if (!contactAnimationList[i]->m_animName.Contains(supportTacklerAnimName.RightChop(3)))
			continue;
		FVector tackleDirection = contactAnimationList[i]->m_staticContact.translation;
		tackleDirection.Normalize();

		float projection = FVector::DotProduct(contactVector, tackleDirection);
		if (projection > bestProjection)
		{
			pBestTackleRec = contactAnimationList[i];
			bestProjection = projection;
		}
	}
	UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_Tackles::PickBestSupportTacklerAnimation: Best animation '%s' with projection %f for support tackler '%s'"), 
		*pBestTackleRec->m_animName, bestProjection, *supportTacklerAnimName);
	return pBestTackleRec;
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================
//{'nodeMatchType': 'RugNode_140_Tackle'}
void RugbyAnimationStateMachine_Tackles::StateMachine_SelectTackleNode()
{
	if (!m_usableTackleAnimRecs.usableRecList.Num())
		return;

	// Get list of usable tackles with animation contact data
	TArray<FRugbyTackleBlendAnimRecord*> tackle_ContactAnimationList;
	for (FRugbyTackleBlendAnimRecord* pUsableAnimRec : m_usableTackleAnimRecs.usableRecList)
	{
		if (pUsableAnimRec && (pUsableAnimRec->m_animatedContactList.Num() > 0))
		{
			tackle_ContactAnimationList.Add(pUsableAnimRec);
		}
	}
	
	RUActionTacklee* pTackleeAction = m_pPlayer->GetActionManager()->GetAction<RUActionTacklee>();
	RUActionTackler* pTacklerAction = m_pPlayer->GetActionManager()->GetAction<RUActionTackler>();
	MABASSERT(pTackleeAction || pTacklerAction);

	TACKLE_ALIGN_TYPE alignAnimType = TAT_ALIGN_BOTH;
	bool bAlign = true;
	bool bRotateTacklee = true;
	bool bHasStruggle = false;

	ARugbyCharacter* pTacklee = nullptr;
	ARugbyCharacter* pTackler = nullptr;
	ARugbyCharacter* pSupportTackler = nullptr;
	if (pTackleeAction && pTackleeAction->IsRunning() && pTackleeAction->GetTackleResult().tacklee == m_pPlayer)
	{
		MABASSERT(pTackleeAction->GetTackleResult().tacklers[0]);
		pTacklee = m_pPlayer;
		pTackler = pTackleeAction->GetTackleResult().tacklers[0];
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
		if (pTackleeAction->GetTackleResult().tacklers.size() > 1)
		{
			pSupportTackler = pTackleeAction->GetTackleResult().tacklers[1];
		}
#endif
		alignAnimType = pTackleeAction->GetTackleResult().align_anim;
		bAlign = (alignAnimType == TAT_ALIGN_BOTH || alignAnimType == TAT_ALIGN_TACKLEE);
		bRotateTacklee = pTackleeAction->GetTackleResult().tackle_result != TRT_CONTESTED || pTackleeAction->GetState() == TS_START_TACKLE;
		bHasStruggle = (pTackleeAction->GetTackleResult().anim_sequences & TAS_GROUND_STRUGGLE) == TAS_GROUND_STRUGGLE;
	}
	else if (pTacklerAction && pTacklerAction->IsRunning() && pTacklerAction->GetTackleResult().tacklers[0] == m_pPlayer)
	{
		MABASSERT(pTacklerAction->GetTackleResult().tacklee);
		pTacklee = pTacklerAction->GetTackleResult().tacklee;
		pTackler = m_pPlayer;
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
		if (pTacklerAction->GetTackleResult().tacklers.size() > 1)
		{
			pSupportTackler = pTacklerAction->GetTackleResult().tacklers[1];
		}
#endif
		alignAnimType = pTacklerAction->GetTackleResult().align_anim;
		bAlign = (alignAnimType == TAT_ALIGN_BOTH || alignAnimType == TAT_ALIGN_TACKLER);
		bRotateTacklee = pTacklerAction->GetTackleResult().tackle_result != TRT_CONTESTED || pTacklerAction->GetState() == TS_START_TACKLE;
		bHasStruggle = (pTacklerAction->GetTackleResult().anim_sequences & TAS_GROUND_STRUGGLE) == TAS_GROUND_STRUGGLE
						&& pTacklerAction->GetTackleResult().tackle_result != TRT_ANKLE_TAP2;
	}
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
	else if (pTacklerAction && pTacklerAction->IsRunning() && pTacklerAction->GetTackleResult().tacklers.size() > 1 && pTacklerAction->GetTackleResult().tacklers[1] == m_pPlayer)
	{
		MABASSERT(pTacklerAction->GetTackleResult().tacklee);
		pTacklee = pTacklerAction->GetTackleResult().tacklee;
		pTackler = pTacklerAction->GetTackleResult().tacklers[0];
		pSupportTackler = m_pPlayer;
		alignAnimType = pTacklerAction->GetTackleResult().align_anim;
		bAlign = (alignAnimType == TAT_ALIGN_BOTH || alignAnimType == TAT_ALIGN_TACKLER);
		bRotateTacklee = pTacklerAction->GetTackleResult().tackle_result != TRT_CONTESTED || pTacklerAction->GetState() == TS_START_TACKLE;
		bHasStruggle = (pTacklerAction->GetTackleResult().anim_sequences & TAS_GROUND_STRUGGLE) == TAS_GROUND_STRUGGLE
						&& pTacklerAction->GetTackleResult().tackle_result != TRT_ANKLE_TAP2;
	}
#endif
	if (pTacklee && pTackler)
	{
		FVector contact_vector = pTacklee->GetMabTransform().InverseTransformPosition(pTackler->GetMabTransform().GetTranslation());
		UE_LOG(LogTemp, Display, TEXT("PlaySelectedTackleAnimation: '%s' Contact vector for main tackler: %s"), *pTacklee->GetName(), *contact_vector.ToString());
		if (contact_vector.Equals(FVector::ZeroVector, KINDA_SMALL_NUMBER))
		{
			UE_LOG(LogTemp, Display, TEXT("PlaySelectedTackleAnimation: '%s' Contact vector is zero, setting to tacklee play direction"), *pTacklee->GetName());
			contact_vector = FVector(0, 0, ((float)pTacklee->GetAttributes()->GetPlayDirection()) * 0.25f);
		}
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
		if (pSupportTackler != nullptr && m_pPlayer == pSupportTackler)
		{
			contact_vector = pTacklee->GetMabTransform().InverseTransformPosition(pSupportTackler->GetMabTransform().GetTranslation());
			UE_LOG(LogTemp, Display, TEXT("PlaySelectedTackleAnimation: '%s' Contact vector for support tackler is '%s'"), *pSupportTackler->GetName(), *contact_vector.ToString());
			if (contact_vector.Equals(FVector::ZeroVector, KINDA_SMALL_NUMBER))
			{
				UE_LOG(LogTemp, Display, TEXT("PlaySelectedTackleAnimation: '%s' Contact vector is zero, setting to tacklee play direction"), *pTacklee->GetName());
				contact_vector = FVector(0, 0, ((float)pSupportTackler->GetAttributes()->GetPlayDirection()) * 0.25f);
			}
		}
#endif
		const FRugbyTackleBlendAnimRecord* pBestTackleAnimRec = nullptr;
		bool animation_already_chosen = false;

		// Check for cached animation decisions for this tackler/tacklee. If an animation has already been chosen, use its matching one.
		for (ChosenTackleAnimations& preselectedTackleData : RUPlayerAnimation::chosen_tackle_animations)
		{
			if (preselectedTackleData.tacklee == pTacklee || preselectedTackleData.tackler == pTackler)
			{
				// A tackle animation is already chosen for this pair. Use the previously chosen animation index so tackler and tacklee match.
				const FRugbyTackleBlendAnimRecord* pOtherPlayerTackleAnimRec = preselectedTackleData.bestTackleRecord;
				ensureAlways(pOtherPlayerTackleAnimRec);
				if (pOtherPlayerTackleAnimRec != nullptr) //this code should never execute, if it does then check your datatable. Also you would have got an assert already
				{
					// Make sure chosen anims match up (first sequence of letters is always tr for tackler and te for tacklee for tackle animations).
					FString matchingTackleAnimName = pOtherPlayerTackleAnimRec->m_animName;
					if (matchingTackleAnimName.IsValidIndex(1))
					{
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
#endif
						{
							matchingTackleAnimName[1] == 'r' ? matchingTackleAnimName[1] = 'e' : matchingTackleAnimName[1] = 'r';
						}
					}

					// Verify the tacklee and tackler have matching animations.										
					bool bMatchFound = false;
					for (int32 index = 0; index < m_usableTackleAnimRecs.usableRecList.Num(); ++index)
					{
						const FRugbyTackleBlendAnimRecord* pCurrTackleRec = m_usableTackleAnimRecs.usableRecList[index];
						if (pCurrTackleRec == nullptr || pOtherPlayerTackleAnimRec == pCurrTackleRec)
						{
							continue;
						}

						if (pCurrTackleRec->m_animName == matchingTackleAnimName &&
							pCurrTackleRec->m_bMirrored == pOtherPlayerTackleAnimRec->m_bMirrored)
						{
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
							// check if its duplicated.
							FRugbyTackleBlendAnimRecord* pCurrTackleRecord = m_usableTackleAnimRecs.usableRecList[index];
							if (ShouldSkipCurrentAnimation(pCurrTackleRecord))
							{
								break;
							}
#endif
							pBestTackleAnimRec = pCurrTackleRec;
							bMatchFound = true;
							animation_already_chosen = true;
							break;
						}
					}
					ensureAlways(true == bMatchFound);
				}

				break;
			}
		}
		
		if (!animation_already_chosen)
		{
			// Pick closest angle animation based on contact vector
			pBestTackleAnimRec = PickBestTackleAnimation(tackle_ContactAnimationList, contact_vector);

			// Add an entry to the stored animation choices vector.
			ChosenTackleAnimations tackle_anim_pair;
			tackle_anim_pair.tacklee = pTacklee;
			tackle_anim_pair.tackler = pTackler;
			tackle_anim_pair.bestTackleRecord = pBestTackleAnimRec;
			RUPlayerAnimation::chosen_tackle_animations.Add(tackle_anim_pair);
		}
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
		if (m_pPlayer == pSupportTackler)
		{
			// choose best animation direction for support tackler.
			pBestTackleAnimRec = PickBestSupportTacklerAnimation(tackle_ContactAnimationList, contact_vector, pBestTackleAnimRec->m_animName);
		}
#endif

		float startTime = calculateAnimationStartOffset(contact_vector, pBestTackleAnimRec);

		ensureAlways(pBestTackleAnimRec && pBestTackleAnimRec->m_pAnimSequence);
		if (pBestTackleAnimRec == nullptr || pBestTackleAnimRec->m_pAnimSequence == nullptr)
		{
			UE_LOG(LogTemp, Display, TEXT("PlaySelectedTackleAnimation: '%s' Failed to find a tackle animation of type: '%d' from list of %d"), *m_pPlayer->GetName(), (int)m_CurrentModeTackles, m_usableTackleAnimRecs.usableRecList.Num());
			m_usableTackleAnimRecs.usableRecList.Reset();
			return;
		}

		m_CurrentModeTackles = m_usableTackleAnimRecs.tackleType; //overwrite it here before we play the animation.....

		UE_LOG(LogTemp, Display, TEXT("PlaySelectedTackleAnimation: '%s' for '%s' Anim: '%s' StartTime '%.3f' Tackle Type: '%d'"), *(pBestTackleAnimRec->m_stateMachinePath), *m_pPlayer->GetName(), *pBestTackleAnimRec->m_animName, startTime, (int)m_CurrentModeTackles);
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
		bool bEnableDebugAnimationName = CVarTwoManTackleAnimationName.GetValueOnAnyThread();
		if (bEnableDebugAnimationName)
		{
			if (GEngine)
			{
				GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Red, FString::Printf(TEXT("PlaySelectedTackleAnimation: '%s' for '%s'"), *m_pPlayer->GetName(), *pBestTackleAnimRec->m_animName));
			}
			FVector textOffset(0.0f, 100.0f, 200.0f);
			DrawDebugString(SIFApplication::GetApplication()->GetWorld(), textOffset, *pBestTackleAnimRec->m_animName, m_pPlayer, FColor::White, 3.0f);
		}
#endif
		m_tackleMontageData.Reset();
		m_tackleMontageData.pTackleAnimRecord = pBestTackleAnimRec;

		float blendDuration = m_pSMMgr->GetSMFullBodyActions()->StateMachine_IsPlayingLongPuntStandKick() ? 0.2f : SMDefaultTackleBlendInTime; // 0.10000000149011612f //RC4-3946			
		float blendOutDuration = bHasStruggle ? 0.0f : SMDefaultBlendOutTime;

		FAnimMontageInstance* pMontageInstance = m_pSMMgr->PlayStateMachineAnimationMontage(this, pBestTackleAnimRec, startTime, blendDuration, false, blendOutDuration);
		ensureAlways(pMontageInstance != nullptr);
		if (pMontageInstance)
		{
			m_tackleMontageData.pMontageInstance = pMontageInstance;
			m_tackleMontageData.montageUniqueID = pMontageInstance->GetInstanceID();
		}
		else
		{
			UE_LOG(LogTemp, Display, TEXT("PlaySelectedTackleAnimation: '%s' somehow failed to play tackle animation: %s"), *m_pPlayer->GetName(), *pBestTackleAnimRec->m_animName);
		}

		if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
		{
			if (bAlign)
			{
				ARugbyCharacter* pOtherCharacter = pTacklee;
				bool isTackler = true;
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
				if (pSupportTackler != nullptr)  // if we enter a two man tackle
				{
					
					if (m_pPlayer == pTackler || m_pPlayer == pSupportTackler)  // current player is tackler.
					{
						pOtherCharacter = pTacklee;
						isTackler = true;
					}
					else // this is tacklee
					{
						pOtherCharacter = pTackler;
						isTackler = false;
					}
				}
				else  // one man tackle
#endif
				{
					if (m_pPlayer != pTackler)
					{
						pOtherCharacter = pTackler;
						isTackler = false;
					}
				}

				m_pPlayer->GetAnimInstance()->BeginAlignToCharacter(pOtherCharacter, isTackler, bRotateTacklee, pMontageInstance, contact_vector, pBestTackleAnimRec->m_staticContact.time); //0.25f is blend
			}
			else
			{
				m_pPlayer->GetAnimInstance()->BeginAlignToCharacter(NULL, false, true, pMontageInstance, contact_vector, pBestTackleAnimRec->m_staticContact.time);
			}
		}

		m_usableTackleAnimRecs.usableRecList.Reset();//make sure we clear this as we have already processed..
	}
}//StateMachine_SelectTackleNode

//=========================================================================================================================================================================
//=========================================================================================================================================================================
float RugbyAnimationStateMachine_Tackles::calculateAnimationStartOffset(const FVector& contactVector, const FRugbyTackleBlendAnimRecord* pAnimRec) const
{
	float animationStartOffset = 0.0f;	
	float contactTime = 0.0f;
	float startTime = 0.0f;

	ensureAlways(pAnimRec != nullptr);

	if (!pAnimRec)
		return animationStartOffset;

	// #anim_todo_blend_times: extract from mopheme state machine and associate with tackle record.
	//const float blendDuration = 0.25f; //0.10000000149011612f ??
	
	if (pAnimRec->m_animatedContactList.Num() > 0)
	{
		contactTime = ((pAnimRec->m_staticContact.time - SMDefaultBlendInTime) > 0.0f) ? (pAnimRec->m_staticContact.time - SMDefaultBlendInTime) : 0.0f; //float contactTime = NMP::maximum(0.0f, m_tackleDef->getContactTime(nodeIndex) - m_tackleDef->getBlendDuration());		
		
		float tacklerToTackleeDistance = contactVector.Magnitude();
		float tacklerAttachToTackleeDistance = 0.0f;
		const FRugbyAnimationLibrary* AnimLib = m_pRUPlayerAnimation->GetAnimationLibrary();

		for (auto &AnimContact : pAnimRec->m_animatedContactList)
		{
			tacklerAttachToTackleeDistance = AnimContact.translation.Magnitude();

			startTime = AnimContact.time;

			if (tacklerToTackleeDistance > tacklerAttachToTackleeDistance)
			{
				break;
			}
		}
		animationStartOffset = FMath::Clamp(startTime, 0.0f, contactTime);
	}
	return animationStartOffset;
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================
// Pick a try-tackle animation from tackle nodedef 
const FRugbyTackleBlendAnimRecord* RugbyAnimationStateMachine_Tackles::StateMachine_GetTryTackleAnimation(TArray<FRugbyTackleBlendAnimRecord*> ContactAnimationList, FVector& tackler_contact_vector) const
{
	const FRugbyTackleBlendAnimRecord *BestTryRecord = nullptr;

	float best_cost = -1.0f;
	
	// Check all the source nodes and find the best try anim
	for (auto &tryTackleAnim : ContactAnimationList)
	{
		// Get alignment cost
		FVector direction = tackler_contact_vector;
		direction.Normalise();

		FVector tackleDirection = tryTackleAnim->m_staticContact.translation;
		tackleDirection.Normalise();
		float cost = direction.Dot(tackleDirection);

		// If tackled and ball will fall in goal area, boost the cost
		//MABASSERT(try_tackle_node_def->canTouchDown());

		FVector try_start_vector = m_pPlayer->GetMabTransform().TransformPos(tryTackleAnim->m_tryStartVectors);
		FVector try_end_vector = m_pPlayer->GetMabTransform().TransformPos(tryTackleAnim->m_tryEndVectors);

		bool in_goal = false;

		cost = m_pRUPlayerAnimation->CalcTryVectorCost(try_start_vector, try_end_vector, in_goal);		

		if (in_goal)
		{
			cost += 1.0f;
		}

		// Choose trytackles which dont fall in field (so ruck can happen on them)
		if (cost > best_cost)
		{
			best_cost = cost;
			BestTryRecord = tryTackleAnim;
		}
	}

	return BestTryRecord;
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::StateMachine_UpdatePickTryTackles()
{
	if (!m_usableTackleAnimRecs.usableRecList.Num())
	{
		return;
	}

	TArray<FRugbyTackleBlendAnimRecord*> tackle_ContactAnimationList;
	for (FRugbyTackleBlendAnimRecord* pUsableAnimRec : m_usableTackleAnimRecs.usableRecList)
	{
		if (pUsableAnimRec && (pUsableAnimRec->m_animatedContactList.Num() > 0))
		{
			tackle_ContactAnimationList.Add(pUsableAnimRec);
		}
	}

	RUActionTacklee* pTackleeAction = m_pPlayer->GetActionManager()->GetAction<RUActionTacklee>();
	RUActionTackler* pTacklerAction = m_pPlayer->GetActionManager()->GetAction<RUActionTackler>();
	MABASSERT(pTackleeAction || pTacklerAction);

	TACKLE_ALIGN_TYPE align_anim = TAT_ALIGN_BOTH;
	bool bAlign = true;
	bool bRotateTacklee = true;

	ARugbyCharacter* pTacklee = nullptr;
	ARugbyCharacter* pTackler = nullptr;

	if (pTackleeAction && pTackleeAction->IsRunning() && pTackleeAction->GetTackleResult().tacklee == m_pPlayer)
	{
		MABASSERT(pTackleeAction->GetTackleResult().tacklers[0]);
		pTacklee = m_pPlayer;
		pTackler = pTackleeAction->GetTackleResult().tacklers[0];
		align_anim = pTackleeAction->GetTackleResult().align_anim;
		bAlign = (align_anim == TAT_ALIGN_BOTH || align_anim == TAT_ALIGN_TACKLEE);
		bRotateTacklee = pTackleeAction->GetTackleResult().tackle_result != TRT_CONTESTED || pTackleeAction->GetState() == TS_START_TACKLE;
	}
	else if (pTacklerAction && pTacklerAction->IsRunning() && pTacklerAction->GetTackleResult().tacklers[0] == m_pPlayer)
	{
		MABASSERT(pTacklerAction->GetTackleResult().tacklee);
		pTacklee = pTacklerAction->GetTackleResult().tacklee;
		pTackler = m_pPlayer;
		align_anim = pTacklerAction->GetTackleResult().align_anim;
		bAlign = (align_anim == TAT_ALIGN_BOTH || align_anim == TAT_ALIGN_TACKLER);
		bRotateTacklee = pTacklerAction->GetTackleResult().tackle_result != TRT_CONTESTED || pTacklerAction->GetState() == TS_START_TACKLE;
	}

	if (pTacklee && pTackler)
	{
		FVector contact_vector = pTacklee->GetMabTransform().InverseTransformPosition(pTackler->GetMabTransform().GetTranslation());

		const FRugbyTackleBlendAnimRecord* pBestAnimRec = nullptr;
		bool animation_already_chosen = false;
		// Check for cached animation decisions for this tackler/tacklee. If an animation has already been chosen, use its matching one.
		for (ChosenTackleAnimations& preselectedTackleData : RUPlayerAnimation::chosen_tackle_animations)
		{
			if (preselectedTackleData.tacklee == pTacklee || preselectedTackleData.tackler == pTackler)
			{
				// A tackle animation is already chosen for this pair. Use the previously chosen animation index so tackler and tacklee match.
				const FRugbyTackleBlendAnimRecord* pOtherPlayerTackleAnimRec = preselectedTackleData.bestTackleRecord;
				if (pOtherPlayerTackleAnimRec)
				{
					// Make sure chosen anims match up (first sequence of letters is always tr for tackler and te for tacklee for tackle animations).
					FString matching_tackle_anim_name = pOtherPlayerTackleAnimRec->m_animName;
					if (matching_tackle_anim_name.IsValidIndex(1))
					{
						matching_tackle_anim_name[1] == 'r' ? matching_tackle_anim_name[1] = 'e' : matching_tackle_anim_name[1] = 'r';
					}

					// Verify the tacklee and tackler have matching animations.										
					for (FRugbyTackleBlendAnimRecord* pCurrAnimRec : m_usableTackleAnimRecs.usableRecList)
					{
						if (pCurrAnimRec->m_animName == matching_tackle_anim_name &&
							pCurrAnimRec->m_bMirrored == pOtherPlayerTackleAnimRec->m_bMirrored)
						{
							pBestAnimRec = pCurrAnimRec;
							break;
						}
					}

					ensureAlways(pBestAnimRec != nullptr);

					animation_already_chosen = true;
					break;
				}
			}
		}

		if (!animation_already_chosen)
		{
			pBestAnimRec = StateMachine_GetTryTackleAnimation(tackle_ContactAnimationList, contact_vector);

			// Add an entry to the stored animation choices vector.
			ChosenTackleAnimations tackle_anim_pair;
			tackle_anim_pair.tacklee = pTacklee;
			tackle_anim_pair.tackler = pTackler;
			tackle_anim_pair.bestTackleRecord = pBestAnimRec;
			RUPlayerAnimation::chosen_tackle_animations.Add(tackle_anim_pair);
		}

		float startTime = calculateAnimationStartOffset(contact_vector, pBestAnimRec);
		m_CurrentModeTackles = m_usableTackleAnimRecs.tackleType; //overwrite it here before we play the animation.....

		if (pBestAnimRec)
		{
			UE_LOG(LogTemp, Display, TEXT("StateMachine_UpdatePickTryTackles: '%s' for '%s' StartTime '%.3f' Tackle Type: '%d'"), *(pBestAnimRec->m_stateMachinePath), *m_pPlayer->GetName(), startTime, (int)m_CurrentModeTackles);
			ensureAlways(pBestAnimRec->m_pAnimSequence != nullptr);

			m_tackleMontageData.Reset();

			m_tackleMontageData.pTackleAnimRecord = pBestAnimRec;
				
			FAnimMontageInstance* pMontageInstance = m_pSMMgr->PlayStateMachineAnimationMontage(this, pBestAnimRec, startTime, SMDefaultBlendInTime, false);
			ensureAlways(pMontageInstance != nullptr);
			if (pMontageInstance)
			{
				m_tackleMontageData.pMontageInstance = pMontageInstance;
				m_tackleMontageData.montageUniqueID = pMontageInstance->GetInstanceID();
			}

			if (m_pPlayer && UOBJ_IS_VALID(m_pPlayer->GetAnimInstance()))
			{
				if (bAlign)
				{
					ARugbyCharacter* pOtherCharacter = pTacklee;
					bool isTackler = true;
					if (m_pPlayer != pTackler)
					{
						pOtherCharacter = pTackler;
						isTackler = false;
					}
					m_pPlayer->GetAnimInstance()->BeginAlignToCharacter(pOtherCharacter, isTackler, bRotateTacklee, pMontageInstance, contact_vector, pBestAnimRec->m_staticContact.time); //0.25f is blend
				}
				else
				{
					m_pPlayer->GetAnimInstance()->BeginAlignToCharacter(NULL, false, true, pMontageInstance, contact_vector, pBestAnimRec->m_staticContact.time);
				}
			}
		}

		m_usableTackleAnimRecs.usableRecList.Reset();//make sure we clear this as we have already processed..
	}
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::StateMachine_Reset()
{
	m_TacklesModeQueue.Empty();
	m_TackleAnimationStateIsGround = false;
	m_NewTackleMetaData.Reset();

	//UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_Tackles::StateMachine_Reset Stopping Montage '%s'"), *m_pPlayer->GetName());	
	if (m_pPlayer && m_pPlayer->GetAnimInstance())
	{
		m_pPlayer->GetAnimInstance()->StopAnimationGroup(MONTAGE_GROUP::TACKLE, 0.0f);
	}

	m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
	m_ContestedTackleStateMachine.ChangeState(EContestedTackleAnimationState::InvalidState);
}

void RugbyAnimationStateMachine_Tackles::ChangeStateToPlayTheBallGround()
{
	m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::play_the_ball_ground);
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_Tackles::StateMachine_ReturnToNullState()
{
	m_TacklesModeQueue.Empty();
	m_TackleAnimationStateIsGround = false;
	m_NewTackleMetaData.Reset();
	m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::StateMachine_Update(float delta)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_RugbyAnimationStateMachineMgr_Update_Tackles);
	//check if we have any pending tackle request.	
	if (!m_TacklesModeQueue.IsEmpty())
	{
		ERugbyAnim_Mode_Tackles TailItem;
		m_TacklesModeQueue.Peek(TailItem);

		//valid range check
		if (TailItem > ERugbyAnim_Mode_Tackles::null && TailItem < ERugbyAnim_Mode_Tackles::MAX)
		{
			m_CurrentModeTackles = TailItem; //set the tackle state
			m_TackleStateMachine.ChangeStateOrReinitialise(TailItem);
			
			m_TacklesModeQueue.Pop(); //pop whatever u have.
		}
	}

	m_TackleStateMachine.Update(delta);
	UpdateRemainingTransitionTime(delta);
}


//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::StateMachine_PostAnimUpdate(float delta)
{
	
	if (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::fast_getup_tackler ||
		m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::Getup ||
		m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::play_the_ball_ground ||
		m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::quick_tap ||
		m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::null )
		
	{
		return;
	}

	//{'nodeMatchType': 'RugNode_160_TryTackle'}
	else if (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::Try ||
		m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::try_corner)
	{
		StateMachine_UpdatePickTryTackles();
	}

	//{'nodeMatchType': 'RugNode_140_Tackle'}
	/*
	try_pushed_tackler
	try_pushed_tacklee
	standard_success_tackler
	standard_success_tacklee
	standard_fail_tackler
	standard_fail_tacklee
	sidestep_success_tackler
	sidestep_success_tacklee
	sidestep_fail_tackler
	sidestep_fail_tacklee
	head_high_tackler
	head_high_tacklee
	fend_success_tackler
	fend_success_tacklee
	fend_fail_tackler
	fend_fail_tacklee
	dive_miss_tackler
	contested_tackler
	contested_tacklee
	ankle_tap_tackler
	ankle_tap_tacklee
	*/
	else
	{
		StateMachine_SelectTackleNode();
	}	
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_Tackles::ClearMontageInstanceReferences(FAnimMontageInstance* pMontageInstance)
{
	if (nullptr == pMontageInstance)
	{
		return;
	}

	if (pMontageInstance == m_tackleMontageData.pMontageInstance && false == pMontageInstance->IsPlaying())
	{
		m_tackleMontageData.Reset();
	}
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::StateMachine_HandleAnimationEvent(float time, ERugbyAnimEvent event, size_t userdata)
{
	if (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::null)
		return;

	//***************************************************************************************************
	//destinationNode: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|getup
	//***************************************************************************************************

	//getup event states.
	if ((userdata == 302) &&
		((m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::fend_success_tackler) ||		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_success_tackler_getup durationInTime: 0.25
		(m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::sidestep_success_tackler) ||	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_success_tackler_getup durationInTime: 0.25
		(m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::standard_fail_tackler)))		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_fail_tackler_getup durationInTime: 0.25
	{	
		m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::Getup);
	}

	//***************************************************************************************************
	//destinationNode: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|null|PassThrough1
	//***************************************************************************************************
	
	else if (userdata == 15)
	{		
		if (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::Getup ||			//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|getup_null durationInTime: 0.25
			m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::fast_getup_tackler) //08: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|getup_null3 durationInTime: 0.25
		{
			UE_LOG(LogTemp, Display, TEXT("AnimationEvent: Tackle Null called as mode is getup for '%s'"), *m_pPlayer->GetName());			
			m_ForceStopBlendOutTime = 0.25f;
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
		}	
	}	

	//***************************************************************************************************
	//destinationNode: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|null|PassThrough1
	//***************************************************************************************************
	//todo: durationInEvents: 0.5
	else if (userdata == 300)
	{
		if ((m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::dive_miss_tackler)			//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|dive_miss_tackler_null durationInTime: 0.5
			|| (!m_IsSuccessfulAnkleTap && m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::ankle_tap_tacklee)			//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|ankle_tap_tacklee_null durationInEvents: 0.5
			|| (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::ankle_tap_tackler)			//11: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|ankle_tap_tackler_null durationInEvents: 0.5
			|| (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::fend_success_tacklee)		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_success_tacklee_null durationInEvents: 0.5
			|| (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::fend_success_tackler)		//01: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_success_tackler_null durationInEvents: 0.5
			|| (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::sidestep_success_tacklee)	//15: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_success_tacklee_null  durationInEvents: 0.5
			|| (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::sidestep_success_tackler)	//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_success_tackler_null  durationInEvents: 0.5
			|| (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::standard_fail_tacklee)		//10: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_fail_tacklee_null  durationInEvents: 0.5
			|| (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::standard_fail_tackler)		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|standard_fail_tackler_null durationInEvents: 0.5
			)
		{
			UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachineMgr::AnimationEvent for userdata '%d' setting tackle mode to NULL from '%d' for '%s'"), userdata, (int)m_CurrentModeTackles, *m_pPlayer->GetName());
			m_ForceStopBlendOutTime = 0.5f;
			m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
		}
	}	
	
	//***************************************************************************************************
	//destinationNode: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|null|PassThrough1
	//***************************************************************************************************
	//durationInEvents: 0.25

	else if ((userdata == 301) && 
		((m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::fend_success_tacklee)			//33: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_success_tacklee_null1 durationInTime: 0.25
			|| (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::fend_success_tackler)		//34: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|fend_success_tackler_null1 durationInTime: 0.25
			|| (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::sidestep_success_tacklee))) //38: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|sidestep_success_tacklee_null1 durationInTime: 0.25
	{
		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachineMgr::AnimationEvent for userdata '%d' setting tackle mode to NULL from '%d' for '%s'"), userdata, (int)m_CurrentModeTackles, *m_pPlayer->GetName());
		m_ForceStopBlendOutTime = 0.25f;
		m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
	}

	//***************************************************************************************************
	//destinationNode: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|null|PassThrough1
	//***************************************************************************************************

	else if ((userdata == 500) && 
		((m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::contested_tacklee) || //23: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tackler_null durationInTime: 0.5
		(m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::contested_tackler))) //player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|contested_tacklee_null durationInTime: 0.5
	{
		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachineMgr::AnimationEvent for userdata '%d' setting tackle mode to NULL from '%d' for '%s'"), userdata, (int)m_CurrentModeTackles, *m_pPlayer->GetName());
		m_ForceStopBlendOutTime = 0.5f;
		m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);		
	}

	//***************************************************************************************************
	//destinationNode: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|null|PassThrough1
	//***************************************************************************************************
	//durationInEvents: 0.25	
	else if ((userdata == 16) && (m_CurrentModeTackles == ERugbyAnim_Mode_Tackles::quick_tap)) //10: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|quick_tap_getup durationInTime: 0.25
	{
		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachineMgr::AnimationEvent for userdata '%d' setting tackle mode to NULL from '%d' for '%s'"), userdata, (int)m_CurrentModeTackles, *m_pPlayer->GetName());
		m_ForceStopBlendOutTime = 0.25f;
		m_TackleStateMachine.ChangeState(ERugbyAnim_Mode_Tackles::null);
	}
}

//=========================================================================================================================================================================
//=========================================================================================================================================================================

void RugbyAnimationStateMachine_Tackles::TackleeGetFreeArms ( bool& LeftArmFree, bool& RightArmFree )
{
	constexpr const float FREE_ARMS_TRIM_LENGTH = 0.3f;

	LeftArmFree = false;
	RightArmFree = false;

	if (m_tackleMontageData.pMontageInstance && m_tackleMontageData.pMontageInstance->IsPlaying() && m_tackleMontageData.pTackleAnimRecord)
	{
		// the animation for offloading takes a little while to play so the last
		// FREE_ARMS_TRIM_LENGTH seconds of free armness are discounted as not being avialable
		bool local_left_arm_free	= m_pPlayer->IsInAnimDurationEvent(ERugbyAnimEvent::LEFT_ARM_FREE_DURATION_EVENT, FREE_ARMS_TRIM_LENGTH);
		bool local_right_arm_free	= m_pPlayer->IsInAnimDurationEvent(ERugbyAnimEvent::RIGHT_ARM_FREE_DURATION_EVENT, FREE_ARMS_TRIM_LENGTH);

		if (false == m_tackleMontageData.pTackleAnimRecord->m_bMirrored)
		{
			LeftArmFree = local_left_arm_free;
			RightArmFree = local_right_arm_free;
		}
		else
		{
			LeftArmFree = local_right_arm_free;
			RightArmFree = local_left_arm_free;
		}

		//#todo: check if multiple events of same type can exist
// 		if (m_Tackle_Montage.Tackle_Blend_AnimRecord && m_Tackle_Montage.Tackle_Blend_AnimRecord->m_rightArmFreeEventTime.HasDurationEvent)
// 		{
// 			ensureAlways(m_Tackle_Montage.Tackle_Blend_AnimRecord->m_rightArmFreeEventTime.StartTime >= 0.0f);
// 
// 			if (m_Tackle_Montage.Tackle_MontageInstance->GetPreviousPosition() >= m_Tackle_Montage.Tackle_Blend_AnimRecord->m_rightArmFreeEventTime.StartTime)
// 			{
// 				if (m_Tackle_Montage.Tackle_MontageInstance->GetPreviousPosition() < m_Tackle_Montage.Tackle_Blend_AnimRecord->m_rightArmFreeEventTime.EndTime - FREE_ARMS_TRIM_LENGTH)
// 				{
// 					local_left_arm_free = true;
// 				}
// 			}
// 		}
// 
// 		if (m_Tackle_Montage.Tackle_Blend_AnimRecord && m_Tackle_Montage.Tackle_Blend_AnimRecord->m_leftArmFreeEventTime.HasDurationEvent)
// 		{
// 			ensureAlways(m_Tackle_Montage.Tackle_Blend_AnimRecord->m_leftArmFreeEventTime.StartTime >= 0.0f);
// 
// 			if (m_Tackle_Montage.Tackle_MontageInstance->GetPreviousPosition() >= m_Tackle_Montage.Tackle_Blend_AnimRecord->m_leftArmFreeEventTime.StartTime)
// 			{
// 				if (m_Tackle_Montage.Tackle_MontageInstance->GetPreviousPosition() < m_Tackle_Montage.Tackle_Blend_AnimRecord->m_leftArmFreeEventTime.EndTime - FREE_ARMS_TRIM_LENGTH)
// 				{
// 					local_right_arm_free = true;					
// 				}
// 			}
// 		}
// 
// 		//// switch hands for mirrored anims
// 		LeftArmFree = (local_left_arm_free && !m_Tackle_Montage.Tackle_Blend_AnimRecord->m_bMirrored) || (local_right_arm_free && m_Tackle_Montage.Tackle_Blend_AnimRecord->m_bMirrored);
// 		RightArmFree = (local_right_arm_free && !m_Tackle_Montage.Tackle_Blend_AnimRecord->m_bMirrored) || (local_left_arm_free && m_Tackle_Montage.Tackle_Blend_AnimRecord->m_bMirrored);
	}
}


//=========================================================================================================================================================================
//=========================================================================================================================================================================

bool RugbyAnimationStateMachine_Tackles::HasValidTryTackleAnim(TACKLE_RESULT_TYPE &type, TRY_TACKLE_TYPE &try_type, ARugbyCharacter* tacklee, ARugbyCharacter* tackler)
{
	//works only for {'nodeMatchType': 'RugNode_160_TryTackle'}

	float warp_cost = FLT_MAX;
	TRY_TACKLE_TYPE tryProbability = TRY_TACKLE_TYPE::TTT_UNKNOWN;

	ensureAlways(tacklee != nullptr && tackler != nullptr);

	if ((tacklee == nullptr) || (tackler == nullptr) || (m_pRUPlayerAnimation == nullptr) || (m_pRUPlayerAnimation->GetAnimationLibrary() == nullptr))
	{
		return false;
	}

	const FRugbyTackleTypeRecord *TackleTypePtr = nullptr;

	if (type == TACKLE_RESULT_TYPE::TRT_TRY)
	{
		TackleTypePtr = tacklee->GetAnimation()->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::Try);

		tryProbability = try_type;

		//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|try|tackle
		ensureAlways((tryProbability >= TRY_TACKLE_TYPE::TTT_SUCCESS_LIKELY) && (tryProbability <= TRY_TACKLE_TYPE::TTT_HELDUP));
	}
	else if ( type == TACKLE_RESULT_TYPE::TRT_TRY_CORNER)
	{
		TackleTypePtr = tacklee->GetAnimation()->GetAnimationLibrary()->GetTackleTypeRec(ERugbyAnim_Mode_Tackles::try_corner);
	}
	else
	{
		UE_DEBUG_BREAK(); //unknown type
		return false;
	}
	
	ensureAlways(TackleTypePtr != nullptr);

	if (nullptr == TackleTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("HasValidTryTackleAnim:TackleTypePtr is null"));
		return false;
	}

	float animStartInTime = 0.0f;
	bool LoopAlways = false;

	TArray <FRugbyTackleBlendAnimRecord*> tempRec;

	tempRec.Empty();	

	for (auto &AnimRec : TackleTypePtr->m_animVariants)
	{
		if (type == TACKLE_RESULT_TYPE::TRT_TRY)
		{
			if ((AnimRec->m_SubType == "Tacklee") && (AnimRec->m_TryTackleType == tryProbability)) //find for tacklee
			{
				tempRec.Add(AnimRec);
			}
		}
		else if (type == TACKLE_RESULT_TYPE::TRT_TRY_CORNER)
		{
			if (AnimRec->m_SubType == "Tacklee") //tacclee only 1: tacklee
			{
				tempRec.Add(AnimRec);
			}
		}
	}

	TArray<FRugbyTackleBlendAnimRecord*> tackle_ContactAnimationList;

	for (auto &tryTackleList : tempRec)
	{
		if (tryTackleList && (tryTackleList->m_animatedContactList.Num() > 0))
		{
			tackle_ContactAnimationList.Add(tryTackleList);
		}
	}

	FVector contact_vector = tacklee->GetMabTransform().InverseTransformPosition(tackler->GetMabTransform().GetTranslation());

	const FRugbyTackleBlendAnimRecord* pAnimRec = StateMachine_GetTryTackleAnimation(tackle_ContactAnimationList, contact_vector);

	MABASSERT(pAnimRec);
	if (pAnimRec == nullptr)
	{
		return false;
	}
	
	FVector try_start_vector = tacklee->GetMabTransform().TransformPos(pAnimRec->m_tryStartVectors);
	FVector try_end_vector = tacklee->GetMabTransform().TransformPos(pAnimRec->m_tryEndVectors);

	if (tacklee->GetGameWorld()->GetSpatialHelper()->IsInField(try_start_vector)) //WillTryTackleFallInField
	{
		return false;
	}

	bool in_goal = false;

	warp_cost = m_pRUPlayerAnimation->CalcTryVectorCost(try_start_vector, try_end_vector, in_goal);

	const float TACKLE_WARP_THRESHOLD = 0.5f;

	return warp_cost > TACKLE_WARP_THRESHOLD;	
}

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
//===============================================================================
//===============================================================================
FString RugbyAnimationStateMachine_Tackles::GetDebugStateText()
{
	FString subStateString = GetSubStateInfo();
	FString debugString = FString::Printf
	(
		TEXT("Tackle SM:   State: %s  Sub-State: %s\n"),
		*ENUM_TO_FSTRING(ERugbyAnim_Mode_Tackles, m_TackleStateMachine.GetCurrentStateKey()),
		*subStateString
	);
	return debugString;
}

//===============================================================================
//===============================================================================
FString RugbyAnimationStateMachine_Tackles::GetSubStateInfo()
{
	if (m_ContestedTackleStateMachine.GetCurrentStateKey() != EContestedTackleAnimationState::InvalidState)
	{
		return ENUM_TO_FSTRING(EContestedTackleAnimationState, m_ContestedTackleStateMachine.GetCurrentStateKey());
	}
	return "NONE";
}
#endif

//=========================================================================================================================================================================
//=========================================================================================================================================================================
//
//void RugbyAnimationStateMachine_Tackles::StateMachine_HandleAnimationEnd(FAnimMontageInstance* montageInst) //only for some debug logs
//{	
//	/*if (m_Tackle_Montage.Tackle_MontageInstance == montageInst)
//	{
//		m_Tackle_Montage.Tackle_MontageInstance = nullptr;
//
//		if (m_CurrentModeTackles != ERugbyAnim_Mode_Tackles::null  && m_Tackle_Montage.Tackle_MontageUniqueID == montageInst->GetInstanceID())
//		{
//			UE_LOG(LogTemp, Display, TEXT("Tackles::StateMachine_HandleAnimationEnd:: mode  '%d' for '%s' MontInst Pos '%.3f'"), (int)m_CurrentModeTackles, *m_pPlayer->GetName(), montageInst->GetPosition());
//		}
//	}*/
//}


//-------------------------------------------------------------------------------------
//dont add code below
//-------------------------------------------------------------------------------------
//quick notes:
/*
ankle_tap_tacklee:		ends by event 300 set to null, entry via ForceTransitionToState, no getup
ankle_tap_tackler:		ends by event 300 set to null, entry via ForceTransitionToState, no getup
contested_tacklee		has getup. Entry via ForceTransitionToState. State internal transition via request
contested_tackler		has getup. Entry via ForceTransitionToState. State internal transition via request
fend_fail_tacklee:		goes to ground once main animation is done, and then goes to getup. entry via ForceTransitionToState.
fend_fail_tackler:		goes to ground once main animation is done, and then goes to getup. entry via ForceTransitionToState.
fend_success_tacklee:	ends by event 300 or 301 set to null, entry via ForceTransitionToState, no getup
fend_success_tackler:	ends by event 300 or 301 set to null, event 302 to getup, entry via ForceTransitionToState
head_high_tacklee:		animation ends set to quicktap if with ball, or getup if no ball, entry via ForceTransitionToState
head_high_tackler:		animation ends set to null, entry via ForceTransitionToState
sidestep_fail_tacklee:	goes to ground once main animation is done, and then goes to getup. entry via ForceTransitionToState
sidestep_fail_tackler:	goes to ground once main animation is done, and then goes to getup. entry via ForceTransitionToState
sidestep_success_tacklee: ends by event 300 or 301 set to null, no getup, entry via ForceTransitionToState
sidestep_success_tackler: ends by event 300 set to null,Event 302 to getup. entry via ForceTransitionToState
standard_fail_tacklee:	ends by event 300 set to null,  no getup entry via ForceTransitionToState
standard_fail_tackler:	ends by event 300 set to null, Event 302 to getup. entry via ForceTransitionToState
standard_success_tacklee: goes to ground once main animation is done, and then goes to getup. entry via ForceTransitionToState
standard_success_tackler: goes to ground once main animation is done, and then goes to getup if no ruck else fast getup if ruck. entry via ForceTransitionToState
try_pushed_tacklee:		goes to ground once main animation is done, and then goes to getup. entry via ForceTransitionToState
try_pushed_tackler:		goes to ground once main animation is done, and then goes to getup. entry via ForceTransitionToState
dive_miss_tackler:		ends by event 300 set to null, no getup entry via ForceTransitionToState
fast_getup_tackler:		ends by event 15 or animation complete, entry -> from standard success if ruck, from contested tackler if ruck. 
Getup:					ends by event 15 or animation complete. Entry from contested_tacklee, contested_tackler, fend_fail_tacklee, fend_fail_tackler, fend_success_tackler, head_high_tacklee, sidestep_fail_tacklee, sidestep_fail_tackler, sidestep_success_tackler, standard_fail_tackler, standard_success_tacklee, standard_success_tackler, try_pushed, try_pushed_tackler
quick_tap:				ends by event 16 set to null, entry via head_high_tacklee
Try:					ends by animation end, entry via ForceTransitionToState
try_corner:				ends by animation end, entry via ForceTransitionToState
*/
//-------------------------------------------------------------------------------------