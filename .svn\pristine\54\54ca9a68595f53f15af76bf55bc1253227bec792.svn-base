#ifndef RUROLEPLAYTHEBALLRECEIVER_H
#define RUR<PERSON><PERSON>LAYTHEBALLRECEIVER_H

#include "Mab/Time/MabTimer.h"
#include "Match/SSRole.h"

class NMMabAnimationNetwork;
class SSSetPlayManager;

/**
	Play the ball receiver
	Position the closest player behind the tacklee entering the play the ball role
	This role is assigned through SSEVDSFormationManager
	See PLAY_THE_BALL SPECIFIC in SSEVDFormationManager if role assignment needs to change
*/

class RURolePlayTheBallReceiver : public SSRole
{
	MABRUNTIMETYPE_HEADER(RURolePlayTheBallReceiver);

public:

	enum class RoleState
	{
		MOVING,					// move into position
		WAITING_FOR_RELEASE,    // waiting for the ball holder to release the ball
		PICKING_UP,				// Picking up the ball
		BALL_RECEIVED,			// Ball has been received
		DONE,					// player moves to general play as the ball holder
	};

	RURolePlayTheBallReceiver( SIFGameWorld* game );

	/// Enter this role with the specified player.
	void Enter(ARugbyCharacter* player) override;

	/// Exit this role.
	void Exit(bool forced) override;

	/// Advance this role.
	void UpdateLogic(const MabTimeStep& game_time_step) override;

	/// Get the fitness of the player for the given behaviour
	static int GetFitness(const ARugbyCharacter* player, const SSRoleArea* area);

	/// returns true if we're interruptible, false if we're not
	bool IsInterruptable() const override;

	const char* GetShortClassName() const override { return "PTBR"; }

	void AnimationEvent(float time, ERugbyAnimEvent event, size_t userdata, bool bIsBlendingOut = false);

	void UpdateCheckSetPlays();

private:

	FVector GetPasserPosition();

	FVector GetReceiverPosition();

	void SetReceiverPosition();

	void UpdateReceivePosition();

	void UpdateHumanPlayerToPositionBehind();

	void WarpToWaypoint();

	RoleState state;

	FVector m_last_passer_pos;

	FVector m_last_receiver_pos;

	SSSetPlayManager* m_set_play_manager;

	bool m_set_play_started;

	bool animationPlayed;

	float animation_timer = 0;

	const char* PICKUP_ANIM = "grab";
};

#endif //RUROLEPLAYTHEBALLRECEIVER_H
