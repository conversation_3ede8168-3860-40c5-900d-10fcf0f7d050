#ifndef RUROLEPLAYTHEBALLSECONDDEFENDER_H
#define RUR<PERSON><PERSON><PERSON>YTHEBALLSECONDDEFENDER_H

#include "Mab/Time/MabTimer.h"
#include "Match/SSRole.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBallDefender.h"

class NMMabAnimationNetwork;

/**
	Play the ball defender role, will be assigned to the defending player involved in the tackle
	Player will stand and defend in front of play the ball player until play is resumed
*/

class RURolePlayTheBallSecondDefender : public RURolePlayTheBallDefender
{
	MABRUNTIMETYPE_HEADER(RURolePlayTheBallSecondDefender);

public:

	RURolePlayTheBallSecondDefender( SIFGameWorld* game );

	/// Enter this role with the specified player.
	void Enter(ARugbyCharacter* player) override;

	/// Get the fitness of the player for the given behaviour
	static int GetFitness(const ARugbyCharacter* player, const SSRoleArea* area);

	const char* GetShortClassName() const override { return "PTB2ndDef"; }

private:
	FVector GetDefenderPosition() override;

	void WarpToWaypoint() override;
};

#endif //RUROLEPLAYTHEBALLDEFENDER_H
