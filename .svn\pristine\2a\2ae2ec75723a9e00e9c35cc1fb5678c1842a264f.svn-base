/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Roles/Competitors/RURoleKickOffKicker.h"

#include "Mab/MabRandDistributions.h"
#include "Match/Ball/SSBall.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerLookAt.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/RUPlayerState.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUInputKickInterface.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Utility/RURandomNumberGenerator.h"
#include "Character/RugbyCharacter.h"
#include "Character/RugbyCharacterAnimInstance.h"
#include "Character/RugbyPlayerController.h"
#include "RugbyGameInstance.h"

#include "Runtime/Engine/Classes/Animation/AnimSequence.h"

#ifdef ENABLE_PRO_MODE
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#endif
#include "Match/RugbyUnion/RUInputPhaseDecision.h"

// The size of the arc moved for each move to run up position animation.
static const float RADIANS_PER_SIDE_STEP	= 0.174533f;
static const float KICKOFF_OFFSET			= 1.6f;
static const float DROPOUT_OFFSET			= 3.5f;
static const FVector LOOK_OFFSET( 0.0f, 2.0f, 0.0f );

MABRUNTIMETYPE_IMP1( RURoleKickOffKicker, SSRole );

//nodes names for kick off animation sequence.

static const char* STANDING_KICK_OFF_REQUEST = "kickoff";// "penalty_kick_kick"; // Nick WWS Set Kick Kickoff //"kickoff";
static const char* CENTER_DROP_KICK_REQUEST		= "center_drop_kick";
static const char* FREEKICK_KICK_REQUEST		= "long_punt_kick";//"freeball_kick";
static const char* LONG_PUNT_KICK_REQUEST		= "long_punt_kick";
static const FString KOK_RIGHT_KICK_ANIM				= "wbko03"; // Nick WWS Set Kick Kickoff //"kickoff";/*"player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions|kick_rightfoot|standing_kickoff|*/"wbkof01";

RURoleKickOffKicker::RURoleKickOffKicker( SIFGameWorld* game )
: SSRole(game),
  kick_pos( 0.0f, 0.0f, 0.0f ),
  kick_type( KICKTYPE_NONE ),
  state( KICK_OFF_SETUP ),
  ball_kicked( false ),
  run_up_started( false ),
  run_up_timer(0.0f),
  kick_angle( 0.0f ),
  kick_power( 0.0f ),
  kick_timer(),
  commentary_time_wasting_timer()
{
	kick_timer.Initialise( game->GetSimTime(), KICK_TIME_OUT );
	commentary_time_wasting_timer.Initialise( game->GetSimTime(), COMMENTARY_KICK_SLOW_TIME);
}

/// returns true if we're interuptable, false if we're not
bool RURoleKickOffKicker::IsInterruptable() const
{
	return state == KICK_OFF_DONE || m_pGame->GetGameState()->GetPhase() == RUGamePhase::PLAY;
}

void RURoleKickOffKicker::Enter( ARugbyCharacter* player )
{
	SSRole::Enter( player );

	m_pGame->GetGameState()->SetBallHolder( player );		// TYRONE : We set this on enter so that assign best player has the orrect reference

	if (player->GetState()->IsInCutScene())
		return;

	SET_CHANGEPLAYER_SECTION( m_pGame, "KO-KICKER" );

	SSHumanPlayer* penalty_decision_human = m_pGame->GetGameState()->GetPenaltyDecisionHuman();
	const RUGamePhase prev_phase = m_pGame->GetGameState()->GetPreviousPhase();

	if ( (prev_phase == RUGamePhase::DECISION_PENALTY || prev_phase == RUGamePhase::DECISION || prev_phase == RUGamePhase::PRE_DROPOUT)
		 && player->GetAttributes()->GetTeam() == m_pGame->GetGameState()->GetPlayRestartTeam()
		 && penalty_decision_human != nullptr && penalty_decision_human->GetTeam() == m_pGame->GetGameState()->GetPlayRestartTeam() )
	{
		// Remove the existing human from this player
		if (player->GetHumanPlayer() != nullptr)
		{
			player->GetHumanPlayer()->SetRugbyCharacter(nullptr);
		}

		RUCareerModeManager* proModeMan = SIFApplication::GetApplication()->GetCareerModeManager();
		const bool isProMode = proModeMan->IsActive() && proModeMan->GetIsCareerModePro();

		// If we're playing Pro Mode, we can only assign this player if they are our player
		if (!isProMode || proModeMan->IsProPlayer(player))
		{
			penalty_decision_human->SetRugbyCharacter(player);
		}
	}
	else
	{
		player->GetAttributes()->GetTeam()->GetHumanSelector().AssignHumanToPlayer( player );
	}

	SET_CHANGEPLAYER_SECTION( m_pGame, NULL );

	player->GetAnimation()->SetIdleGroup(ERugbyFormationIdleGroup::DROPOUT_KICKER);


	if(SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() &&
		SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro() &&
		SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayerOnField())
	{
		// Our player is the kicker
		if(SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(player))
		{
			// By default the cam gets set to non kicker.
			if(m_pGame->GetGameState()->GetKickRestartKickType() == KICKTYPE_KICKOFF)
			{
				m_pGame->GetEvents()->change_to_kick_camera(KICKTYPE_KICKOFF);
			}
		}
		// Our player is not the kicker
		else
		{
			// In pro games, when the AI decides to take a penalty kick, it doesn't go through the kick interface, so it never specifically sets the camera.
			// The camera get set above when the ballholder gets set, but the camera will be an invalid (-1) cam, so default cam.
			// We want to set it to our outside cam, if our pro player is not the kicker.
			// P.s. who knows why the penalty touch kicker is given a Kick Off Kicker role...
			// - Dewald WW
			if(m_pGame->GetGameState()->GetKickRestartKickType() == KICKTYPE_PENALTYPUNT)
			{
				m_pGame->GetEvents()->change_to_kick_camera(KICKTYPE_PENALTYPUNT_PRO_OUTSIDE);
			}
		}
	}

	TransitionToState( KICK_OFF_SETUP );

	//Register animation event
	player->GetMabAnimationEvent().Add( this, &RURoleKickOffKicker::AnimationEvent );

	m_pGame->GetEvents()->commentary_kick_off_wait_for_ref(player);
	m_pGame->GetEvents()->kick_restart_run_up.Add( this, &RURoleKickOffKicker::OnKickRunUp );
	m_pGame->GetEvents()->team_assignments_changed.Add( this, &RURoleKickOffKicker::OnTeamAssignmentsChanged );

	kick_timer.Initialise( m_pGame->GetSimTime(), KICK_TIME_OUT );
	//commentary_time_wasting_timer.Initialise( m_pGame->GetSimTime(), COMMENTARY_KICK_SLOW_TIME);

	run_up_started = false;
	run_up_timer = 0.0f;
}


void RURoleKickOffKicker::Exit(bool forced)
{
	MABASSERT( m_pPlayer != NULL );

	m_pPlayer->GetActionManager()->EnableAllActions( true );
	wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
	m_pPlayer->GetMovement()->SetTargetPosition( m_pPlayer->GetMovement()->GetCurrentPosition() );

	m_pPlayer->GetAnimation()->ResetToDefaultIdleGroup();

	m_lock_manager.UFUnlock(UF_DOMOTION);
	m_lock_manager.HFClearLocks();

	// hide the clock, incase it hasn't been hidden already
	if (m_pGame->GetHUDUpdater())
		m_pGame->GetHUDUpdater()->SetCountdownClockVisible(false);

	//Unregister animation event
	m_pPlayer->GetMabAnimationEvent().Remove( this, &RURoleKickOffKicker::AnimationEvent );

	m_pGame->GetEvents()->kick_restart_run_up.Remove( this, &RURoleKickOffKicker::OnKickRunUp );
	m_pGame->GetEvents()->team_assignments_changed.Remove( this, &RURoleKickOffKicker::OnTeamAssignmentsChanged );

	// if human clear kick incase of early out
	if (m_pPlayer->GetHumanPlayer() != nullptr)
	{
		RUInputKickInterface* kick_interface = m_pGame->GetInputManager()->GetKickInterface();
		kick_interface->StopKick();
	}

	SSRole::Exit(forced);
}
//
//
//void RURoleKickOffKicker::SetKickType( KickType type )
//{
//	kick_type = type;
//
//	//check ball holder is the kicker
//	MABASSERT( game->GetGameState()->GetBallHolder()->GetRole()->RTTGetType() == RURoleKickOffKicker::RTTGetStaticType() );
//
//	SSHumanPlayer* human_player = player->GetHumanPlayer();
//	if ( human_player == NULL )
//	{
//		// Kick the ball left or right within a specific angle range.
//		if ( kick_type == KICKTYPE_KICKOFF || kick_type == KICKTYPE_PLACEKICK || kick_type == KICKTYPE_DROPKICK || kick_type == KICKTYPE_FREEKICK )
//		{
//			float left_right = game->GetRNG()->RAND_CALL(float) < 0.5f ? 1.0f : -1.0f;
//			float angle_deg	= 15.0f + game->GetRNG()->RAND_RANGED_CALL(float, 20.0f );
//			kick_angle += MabMath::Deg2Rad( angle_deg * left_right );
//			//MABLOGDEBUG(MabString(32, "cpu_kick_angle = %f", cpu_kick_angle).c_str());
//		}
//	}
//}
//

void RURoleKickOffKicker::UpdateLogic( const MabTimeStep& game_time_step )
{
	MABASSERT( m_pPlayer != NULL );

	RUPlayerMovement* plr_movement = m_pPlayer->GetMovement();
	RUPlayerAnimation* animation = m_pPlayer->GetAnimation();

	FVector kick_direction( 0.0f, 0.0f, 1.0f );
	FVector target_pos;
	MabMatrix::MatrixMultiply(target_pos, kick_direction, MabMatrix::RotMatrixY(kick_angle));

	kick_direction = target_pos;

	switch ( state )
	{
		case KICK_OFF_SETUP:
		{
			if (kick_type == KickType::KICKTYPE_KICKOFF)
			{
				MabString animation_name;
				GetAnimationToPlay(kick_type, animation_name);

				MABASSERT(animation_name.size() > 0);

				MABLOGDEBUG(MabString(32, "Attempting Animation: %s", animation_name.c_str()).c_str());

				ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
				if (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null)
				{
					MABLOGDEBUG(MabString(32, "Started Animation: %s", animation_name.c_str()).c_str());
					float handedness = m_pPlayer->GetAttributes()->GetDBPlayer()->prefered_foot == 1u ? 1.0f : -1.0f;
					MABASSERT(handedness == -1.0f || handedness == 1.0f);
					animation->SetVariable(animation->GetHandednessVariable(), handedness);
					animation->PlayAnimation(animation_name.c_str());
					m_lock_manager.UFLock(UF_DOMOTION);
					m_lock_manager.UFLock(UF_DOANIMGRAPH);
					//TransitionToState(KICK_OFF_KICK);
				}


				if (plr_movement->HasReachedWaypoint() && plr_movement->HasReachedFacing())
				{
					if (m_pPlayer->GetHumanPlayer() == nullptr)
					{
						kick_timer.Initialise(m_pGame->GetSimTime(), AI_KICK_PAUSE);
						TransitionToState(KICK_OFF_AI_AIM);
					}
					else
					{
						TransitionToState(KICK_OFF_WAIT_FOR_INPUT);
					}
				}
			}
			else
			{
				constexpr const float URGENCY = 0.3f;
				plr_movement->SetThrottleAndTargetSpeedByUrgency(URGENCY, AS_FASTWALK, AS_IDLE);

				plr_movement->SetFacingFlags(AFFLAG_FACETARGANGLE);
				plr_movement->SetTargetFacingAngle(kick_angle);
				plr_movement->SetFaceMotionSpeed(-1.0f);

				// Walk to kickoff...
				m_pPlayer->GetMovement()->SetMinTargetChangeDistance(0.01f);

				const float offset = kick_type == KICKTYPE_KICKOFF ? KICKOFF_OFFSET : DROPOUT_OFFSET;
				wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
				m_pPlayer->GetMovement()->SetTargetPosition(kick_pos + FVector(0.0f, 0.0f, -offset * m_pPlayer->GetAttributes()->GetPlayDirection()), true);

				if (plr_movement->HasReachedWaypoint() && plr_movement->HasReachedFacing())
				{
					if (m_pPlayer->GetHumanPlayer() == nullptr)
					{
						kick_timer.Initialise(m_pGame->GetSimTime(), AI_NO_PAUSE);
						TransitionToState(KICK_OFF_AI_AIM);
					}
					else
					{
						TransitionToState(KICK_OFF_WAIT_FOR_INPUT);
					}
				}
			}
		
		}
		break;

		case KICK_OFF_WAIT_FOR_INPUT:
			{
				// Look towards where we are kicking
			if (kick_type == KickType::KICKTYPE_KICKOFF)
			{
				RUPlayerLookAt* look_at = m_pPlayer->GetLookAt();
				FVector kick_angle_vect;
				SSMath::AngleToMabVector3(m_pGame->GetInputManager()->GetKickInterface()->GetKickAngle(), kick_angle_vect);
				//look_at->LookAtPosition( plr_movement->GetCurrentPosition() + kick_angle_vect + LOOK_OFFSET );
			}
			else
			{
				// Look towards where we are kicking
				RUPlayerLookAt* look_at = m_pPlayer->GetLookAt();
				FVector kick_angle_vect;
				SSMath::AngleToMabVector3(m_pGame->GetInputManager()->GetKickInterface()->GetKickAngle(), kick_angle_vect);
				look_at->LookAtPosition(plr_movement->GetCurrentPosition() + kick_angle_vect + LOOK_OFFSET);
			}
			}
			break;

		case KICK_OFF_AI_AIM:
			{

			if (kick_type == KickType::KICKTYPE_KICKOFF)
			{
				if (kick_timer.GetNumTimerEventsRaised() == 0)
					return;

				TransitionToState(KICK_OFF_KICK);
			}
			else
			{
				if (kick_timer.GetNumTimerEventsRaised() == 0)
					return;

				if (!plr_movement->HasReachedFacing())
				{
					MABLOGDEBUG("Current Facing: %f, Target Facing: %f", plr_movement->GetCurrentFacingAngle(), plr_movement->GetTargetFacingAngle());
					return;
				}

				MabString animation_name;
				GetAnimationToPlay(kick_type, animation_name);

				MABASSERT(animation_name.size() > 0);

				MABLOGDEBUG(MabString(32, "Attempting Animation: %s", animation_name.c_str()).c_str());

				//if (animation->IsAnimationAvailable(animation_name.c_str()))//#rc3_legacy_animation. Rewritten this to check for statemachine before calling

				ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
				if (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null)
				{
					MABLOGDEBUG(MabString(32, "Started Animation: %s", animation_name.c_str()).c_str());
					float handedness = m_pPlayer->GetAttributes()->GetDBPlayer()->prefered_foot == 1u ? 1.0f : -1.0f;
					MABASSERT(handedness == -1.0f || handedness == 1.0f);
					animation->SetVariable(animation->GetHandednessVariable(), handedness);
					animation->PlayAnimation(animation_name.c_str());
					m_lock_manager.UFLock(UF_DOMOTION);
					m_lock_manager.UFLock(UF_DOANIMGRAPH);
					TransitionToState(KICK_OFF_KICK);
				}
			}

			}
			break;

		case KICK_OFF_KICK:
			if ( ball_kicked )
			{
				DoKick();
				TransitionToState( KICK_OFF_DONE );
			}
			break;

		case KICK_OFF_DONE:
			break;

		default:
			MABBREAK();
	}

	SSRole::UpdateLogic( game_time_step );

	if(commentary_time_wasting_timer.GetNumTimerEventsRaised() > 0)
	{
		//fire off a commentary event indicating that this has taken
		//a damn long time so far.
		m_pGame->GetEvents()->commentary_kick_slow();
		commentary_time_wasting_timer.Acknowledge();
		commentary_time_wasting_timer.SetEnabled(false);
	}

	// check timer
	if ( kick_timer.GetNumTimerEventsRaised() > 0 )
	{
		// make the kick
		if ( state == KICK_OFF_WAIT_FOR_INPUT )
		{
			// only for human players
			SSHumanPlayer* human = m_pPlayer->GetHumanPlayer();
			if ( human != NULL )
				m_pGame->GetInputManager()->GetKickInterface()->StopKick();

			m_pGame->GetEvents()->commentary_kick_timed_out();

			TransitionToState( KICK_OFF_AI_AIM );
		}

		// hide the clock
		if (m_pGame->GetHUDUpdater())
			m_pGame->GetHUDUpdater()->SetCountdownClockVisible(false);
	}

	if ( run_up_started )
		run_up_timer += game_time_step.delta_time.ToSeconds();
}

void RURoleKickOffKicker::DoKick()
{
	RUPlayerMovement *plr_movement = m_pPlayer->GetMovement();

	MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "KickOff... Kick angle: %f, strength: %f", kick_angle, kick_power);

	m_pGame->GetGameState()->Kick( m_pPlayer, kick_type, kick_power, kick_angle, 0.0f );
	ball_kicked = true;

	plr_movement->SetCurrentSpeed( 0.0f );
	plr_movement->SetTargetSpeed( plr_movement->GetIdealSpeed(AS_RUN) );

	//Clear the marking player
	if (SIFApplication::GetApplication())
	{
		SIFGameWorld * gameWorld = SIFApplication::GetApplication()->GetActiveGameWorld();
		if (gameWorld && gameWorld->GetGameState())
		{
			gameWorld->GetGameState()->SetMarkingRugbyCharacter(nullptr);
		}
	}
}

void RURoleKickOffKicker::CalculateKick()
{
	SSHumanPlayer* human_player = m_pPlayer->GetHumanPlayer();
	if ( human_player )
	{
		kick_angle = m_pGame->GetInputManager()->GetKickInterface()->GetKickAngle();
		kick_power = m_pGame->GetInputManager()->GetKickInterface()->GetKickStrength();
	}
	else
	{
		// Work out the strength required for the AI to go this distance.
		float short_kick_strength = MIN_KICKOFF_KICK_DIST / MAX_KICKOFF_KICK_DIST;
		float long_kick_strength = 0.35f + m_pGame->GetRNG()->RAND_RANGED_CALL(float, 0.50f);

		// Select based on game state
		RUStrategyHelper* strategy_helper = m_pGame->GetStrategyHelper();
		MABASSERT( strategy_helper != NULL );

		// If penalty kick for touch AI should do that
		if ( m_pGame->GetGameState()->GetPhase() == RUGamePhase::PENALTY_KICK_FOR_TOUCH )
		{
			RUStrategyPos for_touch_kick_pos;
			strategy_helper->FindBestKickForTouchOnFullPos( m_pPlayer, for_touch_kick_pos);

			FVector position(for_touch_kick_pos.x - m_pPlayer->GetMovement()->GetCurrentPosition().x, 0.0f, for_touch_kick_pos.z - m_pPlayer->GetMovement()->GetCurrentPosition().z );
			float kick_dist = position.ApproxMagnitude();

			kick_angle = MabMath::GetAngle( position.x, position.z );
			float wind = 0.0f;
			// Get the kick strength details
			m_pGame->GetBall()->GetKickStrengthDetails( kick_type, kick_dist, true, kick_power, wind );
		}
		else
		{
			RUTeam* team = m_pPlayer->GetAttributes()->GetTeam();
			kick_power = strategy_helper->ShouldAttemptShortRestart( team ) ? short_kick_strength : long_kick_strength;

			float dis, dir;
			if ( m_pGame->GetGameState()->GetPhase() == RUGamePhase::DROPOUT )
				team->GetDbTeam().GetNormalisedFavouredDropoutSliders(dis, dir);
			else
				team->GetDbTeam().GetNormalisedFavouredKickoffSliders(dis, dir);

			float left_right = m_pGame->GetRNG()->RAND_CALL(float) < dir ? -1.0f : 1.0f;
			static const float BASE_ANGLE		= 5.0f;
			static const float ANGLE_VARIANCE	= 25.0f;
			float angle_deg	= BASE_ANGLE + m_pGame->GetRNG()->RAND_RANGED_CALL(float, ANGLE_VARIANCE );
			kick_angle = MabMath::Deg2Rad( angle_deg * left_right ) + m_pPlayer->GetAttributes()->GetTeam()->GetPlayAngle();

			/// Work out where the ball will end up once wind is applied and ensure that we do a reasonable kick

			ASSBall* ball = m_pGame->GetBall();
			FVector wind_displacement = ball->CalculateWindDisplacement( kick_power * MAX_KICKOFF_KICK_DIST, KICKOFF_KICK_BASE_ANGLE );

			FVector target_offset_no_wind;
			SSMath::AngleToMabVector3( kick_angle, target_offset_no_wind );
			target_offset_no_wind *= (kick_power * MAX_KICKOFF_KICK_DIST);
			FVector target_offset_with_wind = target_offset_no_wind + wind_displacement;


			/// Make sure that we go the minimum distance when the wind is high on kickoffs
			if ( m_pGame->GetGameState()->GetPhase() == RUGamePhase::KICK_OFF )
			{
				float forward_dist_travelled = target_offset_with_wind.z * m_pPlayer->GetAttributes()->GetPlayDirection();
				static const float TEN_METRES = 10.0f;
				if ( forward_dist_travelled < TEN_METRES)
				{
					static const float DEFAULT_DIST_PAST_10 = 2.0f;
					static const float VARIANCE_WORST_KICK_ABILITY = DEFAULT_DIST_PAST_10 * 1.6f;
					static const float VARIANCE_BEST_KICK_ABILITY  = DEFAULT_DIST_PAST_10 * 1.0f;
					float variance = MabMath::Lerp( VARIANCE_WORST_KICK_ABILITY, VARIANCE_BEST_KICK_ABILITY, m_pPlayer->GetAttributes()->GetGeneralKickAccuracy() );
					/// Work out a new z based on kicker ability and scale the vector appropriately
					float kick_z_dist = MabRandGaussian( *m_pGame->GetRNG(), TEN_METRES + DEFAULT_DIST_PAST_10, variance );

					/// Account for wind influence and play direction
					/// This may seem yuck but without a function to give me kick distance with wind magnitde,
					/// it was the quickest way to ensure kicks go far enough!!!
					static const float INCREMENT_MULTIPLIER = 0.25f;
					static const int MAX_ITERATIONS = 10;
					float new_kick_z = kick_z_dist * m_pPlayer->GetAttributes()->GetPlayDirection();
					for( int i = 0; i < MAX_ITERATIONS && (target_offset_with_wind.z * m_pPlayer->GetAttributes()->GetPlayDirection()) < MabMath::Fabs( new_kick_z ); i++ )
					{
						target_offset_no_wind *= (1.0f + INCREMENT_MULTIPLIER);
						MabMath::Clamp( target_offset_no_wind.x, -FIELD_WIDTH * 0.5f, FIELD_WIDTH * 0.5f );
						target_offset_with_wind = target_offset_no_wind + ball->CalculateWindDisplacement( target_offset_no_wind.Magnitude(), KICKOFF_KICK_BASE_ANGLE );
					}
				}
			}

			/// make sure that the target is within the field of play
			FVector player_pos = m_pPlayer->GetMovement()->GetCurrentPosition();
			FVector world_target_pos_with_wind = player_pos + target_offset_with_wind;

			if ( !m_pGame->GetStrategyHelper()->IsPointInFieldOfPlay( world_target_pos_with_wind.x, world_target_pos_with_wind.z, true ) )
			{
				const float SIDELINE_BUFFER_KICK = 5.0f;
				// bring power down until ball lands in field of play.
				float new_kick_x = FIELD_WIDTH * 0.5f - SIDELINE_BUFFER_KICK;
				float multiplier = new_kick_x / target_offset_no_wind.x;

				target_offset_with_wind   = target_offset_no_wind * multiplier;
				wind_displacement = ball->CalculateWindDisplacement( target_offset_with_wind.Magnitude(), KICKOFF_KICK_BASE_ANGLE );
				target_offset_no_wind = target_offset_with_wind - wind_displacement;
			}

			float wind = 0.0f;
			// Get the kick strength details
			m_pGame->GetBall()->GetKickStrengthDetails( kick_type, target_offset_no_wind.Magnitude(), true, kick_power, wind );
		}
	}
}


int RURoleKickOffKicker::GetFitness(const ARugbyCharacter* player, const SSRoleArea* area)
{
	//If this is a kick for touch after a mark, take the marking player.
	if (SIFApplication::GetApplication())
	{
		SIFGameWorld * gameWorld = SIFApplication::GetApplication()->GetActiveGameWorld();
		if (gameWorld && gameWorld->GetGameState())
		{
			if (player == gameWorld->GetGameState()->GetMarkingRugbyCharacter())
			{
				return 1000;
			}
		}
	}
	return GetPlaymakerFitness( player, area );
}

int RURoleKickOffKicker::CalculateKickOffSideSteps( float kkick_angle ) const
{
	MABASSERT( MabMath::Fabs(kick_angle) <= 2.0f * PI );
	MABASSERT( m_pPlayer != NULL );
	return m_pPlayer->GetAttributes()->GetPlayDirection() == ERugbyPlayDirection::NORTH ? static_cast<int>( kkick_angle / RADIANS_PER_SIDE_STEP ) : static_cast<int>( (kkick_angle - PI) / RADIANS_PER_SIDE_STEP );
}

void RURoleKickOffKicker::TransitionToState( KICK_OFF_STATE sstate )
{
	state = sstate;

	switch( state )
	{
	case KICK_OFF_SETUP:
		{
			SSHumanPlayer* human = m_pPlayer->GetHumanPlayer();
			if ( human != NULL )
				human->ResetActions();

			SetInitialMovement();

			//
			// Stop stepping the animation graph during a place kick when playing
			// animations.
			//
			m_pPlayer->GetActionManager()->EnableAllActions( false );
			m_pPlayer->GetActionManager()->AbortAllActions();
			m_pPlayer->GetActionManager()->EnableAction(ACTION_KICK, true);

			//disable all actions for current human controlled kicker, including changing players
			m_lock_manager.HFLockAll();

			if(m_pPlayer!=m_pGame->GetGameState()->GetBallHolder())
			{
				m_pGame->GetGameState()->SetBallHolder(m_pPlayer);
			}
		}
		break;
	case KICK_OFF_WAIT_FOR_INPUT:
		{
			if (m_pPlayer->GetHumanPlayer() && m_pGame->GetHUDUpdater())
			{
				m_pGame->GetHUDUpdater()->SetCountdownClockVisible(true);
				m_pGame->GetHUDUpdater()->StartCountdownClock(KICK_TIME_OUT);
			}

			if (kick_type != KickType::KICKTYPE_KICKOFF)
			{
				kick_timer.Reset();
				commentary_time_wasting_timer.Reset();
			}

			// only for human players
			SSHumanPlayer* human = m_pPlayer->GetHumanPlayer();
			if ( human != NULL )
			{
				m_lock_manager.HFUnlock(HF_KICK);
				m_pGame->GetInputManager()->GetKickInterface()->StartKick( human->GetRugbyCharacter(), kick_type );
			}
		}
		break;

	case KICK_OFF_KICK:
		{
			m_pGame->GetGameState()->KickRestartRunUp();
		}
		break;
	case KICK_OFF_AI_AIM:
		{
			if (kick_type != KickType::KICKTYPE_KICKOFF)
			{
				//kick_timer.Initialise(m_pGame->GetSimTime(), AI_KICK_PAUSE);
			}
			CalculateKick();

			m_pPlayer->GetMovement()->SetFacingFlags( AFFLAG_FACETARGANGLE );
			m_pPlayer->GetMovement()->SetTargetFacingAngle( kick_angle );
		}
		break;

	case KICK_OFF_DONE:
		{
			m_pGame->GetGameState()->KickRestartFinished();
		}
		break;
	}
}

void RURoleKickOffKicker::GetAnimationToPlay(KickType kkick_type, MabString& animation_name)
{
	switch (kkick_type)
	{
	case KICKTYPE_DROPKICK:
		animation_name = MabString(CENTER_DROP_KICK_REQUEST);
		break;
	case KICKTYPE_FREEKICK:
		animation_name = MabString(FREEKICK_KICK_REQUEST);
		break;
	case KICKTYPE_KICKOFF:
		animation_name = MabString(STANDING_KICK_OFF_REQUEST);
		break;
	default:
		ensureMsgf(false, TEXT("Kick off animation type %d is not a valid option for kickoff, returning default value."), (uint32)kkick_type);
	case KICKTYPE_PENALTYPUNT:
	case KICKTYPE_LONGPUNT:
		animation_name = MabString(LONG_PUNT_KICK_REQUEST);
		break;
	}
}

void RURoleKickOffKicker::AnimationEvent(float /*time*/, ERugbyAnimEvent event, size_t /*userdata*/, bool /*bIsBlendingOut = false*/)
{
	if ( event == ERugbyAnimEvent::BALL_KICK_EVENT )
	{
		ball_kicked = true;

		if ( state == KICK_OFF_WAIT_FOR_INPUT )
			TransitionToState( KICK_OFF_DONE );
	}
}

void RURoleKickOffKicker::WarpToWaypoint()
{
	// Idles get reset in warp to waypoint so set up again...
	SetInitialMovement();
	SSRole::WarpToWaypoint();
	m_pPlayer->GetAnimation()->SetIdleGroup(ERugbyFormationIdleGroup::DROPOUT_KICKER);
}

float RURoleKickOffKicker::GetEstimatedTimeTillBallContact()
{
	float time = 20.0f;

	if (m_KickOffEventTime < 0.0f )
	{
		ERugbyAnimEvent EventType = ERugbyAnimEvent::INVALID;
		const FRugbyAnimationLibrary* pAnimLib = m_pPlayer->GetAnimation()->GetAnimationLibrary();
		UAnimSequence* AnimSequenceptr = nullptr;
		if (pAnimLib)
		{
			AnimSequenceptr = pAnimLib->GetAnimSequenceByName(KOK_RIGHT_KICK_ANIM, false);
			if (m_pPlayer->GetAnimInstance() && AnimSequenceptr)
			{
				m_pPlayer->GetAnimInstance()->GetEventTimeFromAnimSequenceFromEventIndex(AnimSequenceptr, 0, ERugbyAnimEvent::BALL_KICK_EVENT, m_KickOffEventTime, EventType); //get the event time (from end)
				m_KickOffEventTime = AnimSequenceptr->GetPlayLength() - m_KickOffEventTime; //get the event start time.
			}
		}
	}

	wwNETWORK_TRACE_JG("GetEstimatedTimeTillBallContact rus:%d, koet: %f", run_up_started, m_KickOffEventTime);
	if (run_up_started && m_KickOffEventTime >= 0.0f)
	{
		time = m_KickOffEventTime - run_up_timer;
		MabMath::Clamp(time, 0.0f, 20.0f);
		//MABLOGDEBUG("RURoleKickOffKicker::GetEstimatedTimeTillBallContact::Time till ball contact: %f", time);
	}	

	//#rc3_legacy
	//if (run_up_started)
	//{
	//NMMabAnimationNetwork* network = player->GetComponent<NMMabAnimationNetwork>();
	//const NodeDef* node_def = network->GetNetworkDef()->findNodeDef( KOK_RIGHT_KICK_ANIM );
	//
	//if(node_def != NULL)
	//{
	//	int node_id = node_def->index();
	//	network->GetNetworkDef()->getNodeEventTime(node_id, RUGameAnimation::BALL_KICK_EVENT, time );
	//
	//	time = time - run_up_timer;
	//	MabMath::Clamp(time, 0.0f, 20.0f);
	//}
	//}
	
	return time;
}

void RURoleKickOffKicker::OnKickRunUp()
{
	wwNETWORK_TRACE_JG("OnKickRunUp");
	run_up_started = true;
}


void RURoleKickOffKicker::OnTeamAssignmentsChanged(RUTeam* /*new_team*/, SSHumanPlayer* /*human_player*/)
{
	RUInputKickInterface* kick_interface = m_pGame->GetInputManager()->GetKickInterface();
	bool kick_ui_active = kick_interface->IsKickActive();

	// reestablish who is controlling this player
	m_pPlayer->GetAttributes()->GetTeam()->GetHumanSelector().AssignHumanToPlayer( m_pPlayer );

	// check if we need the kick stuff for a new human (or the same one again)
	if (m_pPlayer->GetHumanPlayer())
	{
		//if(!kick_ui_active)
		kick_interface->StartKick(m_pPlayer, kick_type);

		if(m_pGame->GetHUDUpdater())
			m_pGame->GetHUDUpdater()->SetCountdownClockVisible(true);
	}
	// hide the clock if changing to AI
	else
	{
		// stop the old kick ui, if it was running
		if (kick_ui_active)
			kick_interface->StopKick();

		if(m_pGame->GetHUDUpdater())
			m_pGame->GetHUDUpdater()->SetCountdownClockVisible(false);
	}
}

void RURoleKickOffKicker::SetInitialMovement()
{
	RUPlayerMovement *plr_movement = m_pPlayer->GetMovement();
	RUPlayerAttributes *plr_attribs = m_pPlayer->GetAttributes();

	kick_pos = m_pGame->GetGameState()->GetPlayRestartPosition();

	// get the kick direction
	kick_angle = plr_attribs->GetTeam()->GetPlayAngle();

	// reset the kick type
	if ( m_pGame->GetGameState()->GetPhase() == RUGamePhase::DROPOUT )
		kick_type = KICKTYPE_DROPKICK;
	else //for penalty kick and kick off kick.
		kick_type = m_pGame->GetGameState()->GetKickRestartKickType();

	// Face the kick direction.
	plr_movement->SetFacingFlags( AFFLAG_FACEPLAYDIR );
	plr_movement->SetFaceMotionSpeed( plr_movement->GetIdealSpeed( AS_WALK ) );

	// Walk to kickoff...
	float offset = kick_type == KICKTYPE_KICKOFF ? KICKOFF_OFFSET : DROPOUT_OFFSET;
	wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
	m_pPlayer->GetMovement()->SetTargetPosition( kick_pos + FVector( 0.0f, 0.0f, -offset * m_pPlayer->GetAttributes()->GetPlayDirection() ), true);
}
