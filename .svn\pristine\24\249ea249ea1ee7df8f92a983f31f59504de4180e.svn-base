/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef _RU_SUBSTITUTION_MANAGER_H_
#define _RU_SUBSTITUTION_MANAGER_H_

#include "Match/RugbyUnion/RUPlayerFactory.h"
#include "Match/RugbyUnion/RUGameSettings.h" //This should not be here - ABROWN
#include "Mab/MabInclude.h"
#include "Match/SIFObjectLists.h"
#include "Queue.h"
//#rc3_legacy_include #include <MabLockedQueue.h>


class SIFGameWorld;
class ARugbyCharacter;
class RUTeam;
class SIFAsyncLoadingThread;
class MabTextureResourceFile;
class URugbyGameWorldSettings; typedef URugbyGameWorldSettings RUGameSettings;

struct RUDB_TEAM_STRIP;


///-----------------------------------------------------------------
/// \class RUInterchangeEvent
/// An 'pending' interchange event
/// <AUTHOR>
///-----------------------------------------------------------------

enum RU_INTERCHANGE_EVENT_TYPE {
	RU_INTERCHANGE_INJURY,				// a player is injured
	RU_INTERCHANGE_SINBIN_OFF,			// a player is sin-binned
	RU_INTERCHANGE_SINBIN_RETURN,		// a player returns from the sin bin
	RU_INTERCHANGE_SENTOFF,				// a player is sent off for the rest of the game (perhaps longer)
	RU_INTERCHANGE_EXCHANGE,			// a player on field is exchanged with a player on bench
	RU_INTERCHANGE_LINEUP_RESET,		// line up reset has occurred, and need to reload player.
	RU_INTERCHANGE_NUM_TYPES
};

enum RU_INTERCHANGER_EVENT_STATE {
	INTC_PENDING=0,					// Pending
	INTC_PROCESSING,				// Player is being loaded will change to INTC_DONE when loading complete.
	INTC_DONE,						// done.
	INTC_CANCELLED					// cancelled.
};


class RUInterchangeEvent
{
public:
	RUInterchangeEvent(RUTeam *team, ARugbyCharacter *primary_player, int primary, int secondary, int ev_type)
		: team(team)
		, primary_player(primary_player)
		, primary(primary)
		, secondary(secondary)
		, event_type(ev_type)
		, uid(-1)
		, state(INTC_PENDING)
	{
	}
	RUInterchangeEvent(const RUInterchangeEvent *original)
	{
		team = original->team;
		primary_player = original->primary_player;
		primary = original->primary;
		secondary = original->secondary;
		event_type = original->event_type;
		uid = original->uid;
		state = INTC_PENDING;
	}

	inline RUTeam *GetTeam() const { return team; }
	inline RU_INTERCHANGER_EVENT_STATE GetState(){ return state; }
	inline void SetState(RU_INTERCHANGER_EVENT_STATE new_state){ state = new_state; if(new_state==INTC_DONE) primary_player = NULL; }

	inline int GetPrimary() const { return primary; }
	inline int GetSecondary() const { return secondary; }
	inline int GetUID() const { return uid; }
	inline int GetType() const { return event_type; }
	inline ARugbyCharacter* GetPrimaryPlayer() const { return primary_player; }
	inline void SetUID(int new_uid){ uid = new_uid; }

	inline void SetPrimaryPlayer(ARugbyCharacter* player){ primary_player = player; }

	///----------------------

private:
	RUTeam				*team;
	ARugbyCharacter		*primary_player;

	int					primary;			// The database id of player being sub'd (will be deleted and swapped with new player).
	int					secondary;			// The database id of the new player.
	int					event_type;
	int					uid;

	RU_INTERCHANGER_EVENT_STATE	state;
};


///-----------------------------------------------------------------
/// \class DeferredTeamSkinInfo
/// Data storage class for passing to deferred functions.
/// <AUTHOR>
///-----------------------------------------------------------------

class DeferredTeamSkinInfo
{
public:
	//DeferredTeamSkinInfo(RUTeam *team) : team(team), csinfo(), textures()
	DeferredTeamSkinInfo(RUTeam *team) : team(team), textures()
	{}

	RUTeam *team;
	PlayerCustomisationInfo csinfo;
	MabVector<MabTextureResourceFile*> textures;
};

///-----------------------------------------------------------------
/// \class RUSubstitutionManager
/// Controls the swapping of players during substitutions
/// <AUTHOR>
///-----------------------------------------------------------------

class RUSubstitutionManager
{
	friend class RUInterchangeEvent;

	static const float FATIGUE_RED;
	static const float FATIGUE_YELLOW;

	typedef void (*LoadSubPlayerCallback)(void *user_data, ARugbyCharacter* player, ARugbyCharacter* old_player, bool is_abort);

	// RussellD: So, I've removed the player loading from the substitution, but still need to wait for any pending customisations
	enum SUBS_STATE
	{
		SUB_STATE_INIT = 0,
		SUB_STATE_STYLING_WAIT,
		SUB_STATE_ABORTED
	};
	/* Formerly,
	enum SUBS_STATE
	{
		SUB_STATE_INIT = 0,
		SUB_STATE_LOADING,
		SUB_STATE_LOADED_SYNC,
		SUB_STATE_LOADED_STYLE_WAIT,
		SUB_STATE_ABORTED
	};
	//*/

	enum LOAD_ALL_STATE
	{
		LOAD_ALL_COMPLETE=0,
		LOAD_ALL_ASYNC,
		LOAD_ALL_NON_ASYNC
	};

	/// Class: SubstitutionQueueEntry
	///		- Entry into substitution_queue, (currently loading players).
	class SubstitutionQueueEntry
	{
	public:
		SubstitutionQueueEntry(ARugbyCharacter* player, int db_id, int strip_id, RUTeam *team, LoadSubPlayerCallback callback, void *user_data, bool do_swap ) :
			db_id(db_id), strip_id(strip_id), player(player), team(team), callback(callback), callback_data(user_data),
			state(SUB_STATE_INIT), do_swap(do_swap), style_wait(0), new_player(NULL)
		{}

		int					db_id;						// The database id of the new player.
		int					strip_id;
		ARugbyCharacter			*player;				// The player being sub'd (will be deleted and swapped with new player).
		RUTeam					*team;					// The players team.
		LoadSubPlayerCallback	callback;
		void					*callback_data;
		int					state;
		bool					do_swap;
		int					style_wait;					// counter for number of frames waiting for style to load.

		ARugbyCharacter			*new_player;			// The player that is being loaded.
	};

	/// Class: InterchangeCallBackData
	///		- Callback data for interchanges.
	class InterchangeCallBackData
	{
	public:
		InterchangeCallBackData(LoadSubPlayerCallback cb, void *ud, RUInterchangeEvent *event) : callback(cb), user_data(ud), event(event){}

		LoadSubPlayerCallback	callback;
		void					*user_data;
		RUInterchangeEvent		*event;
	};

	/// Class: AsyncLoadData
	///		- user_data for AsyncLoadPlayerJob
	class AsyncLoadData
	{
	public:
		AsyncLoadData() :
		game(NULL), team(NULL), db_player(NULL), position(PP_LOOSEHEAD_PROP), entry(NULL)//, cs_info()
		{}
	public:
		SIFGameWorld			*game;
		RUTeam					*team;
		RUDB_PLAYER				*db_player;

		PLAYER_POSITION			position;

		SubstitutionQueueEntry	*entry;
		PlayerCustomisationInfo	cs_info;
	};

	/// Struct: SinBin
	///		- A single entry in the sinbin.
	struct SinBin
	{
		SinBin() : player(NULL), m_timeOff(0), m_timeOfPenalty(0), m_timeServed(0) { }
		SinBin(ARugbyCharacter* player, int timeOff, float timeOfPenalty)
			: player(player)
			, m_timeOff(timeOff)
			, m_timeServed(0)
			, m_timeOfPenalty(timeOfPenalty)
		{ }

		ARugbyCharacter* player;

		float		m_timeOfPenalty;
		int			m_timeOff;
		float		m_timeServed;

		void UpdateTimeServed(const float halfLength, const float firstHalfTotalTime)
		{
			m_timeServed = firstHalfTotalTime - m_timeOfPenalty;
			// Update the time of the penalty to account for penalty
			m_timeOfPenalty = halfLength; 
		}
		const float GetThisHalfEndTime() const 											{ return m_timeOfPenalty + (m_timeOff - m_timeServed); }
	};

	typedef MabVector<SinBin> SinBins;

	/// Class: PendingStripChangeQueueEntry
	///		- Entry in strip_change_queue.
	class PendingStripChangeQueueEntry
	{
	public:
		//PendingStripChangeQueueEntry(RUTeam *team, MABMEM_HEAP heap_id) : team(team), csinfo(), heap_id(heap_id)
		PendingStripChangeQueueEntry(RUTeam *team, MABMEM_HEAP heap_id) : team(team), heap_id(heap_id)
		{}
		RUTeam						*team;
		PlayerCustomisationInfo		csinfo;
		MABMEM_HEAP					heap_id;
	};

	/// Struct: StripSwap
	///		- Entry into 'strip_swap_queue'
	struct StripSwap
	{
		StripSwap()
			: manager(NULL)
			//, game_settings_source() ABROWN HACK
			, game_settings_destination(NULL)
			, is_comp_mode(false)
			, team0_strip_id(0)
			, team1_strip_id(0)
		{}
		RUSubstitutionManager* manager;
		//RUGameSettings	game_settings_source;
		RUGameSettings* game_settings_destination;
		// Competition/Career mode, use these...
		bool			is_comp_mode;
		int				team0_strip_id;
		int				team1_strip_id;
	};


	/// Class: AsyncLoadUIData
	///		-
	class AsyncLoadUIData
	{
	public:
		//#rc3_legacy AsyncLoadUIData() : game(NULL), strip(NULL), socks(NULL), numbers(NULL), csinfo(), callback(NULL), user_data(NULL)
		AsyncLoadUIData() : game(NULL), strip_id(-1), player_id(-1), callback(NULL), user_data(NULL)
		{}
	public:
		SIFGameWorld			*game;

		//MabTextureResourceFile	*strip;
		//MabTextureResourceFile	*socks;
		//MabTextureResourceFile	*numbers;
		int32 strip_id;
		int32 player_id;

		PlayerCustomisationInfo	csinfo;

		RUSubstitutionManager::LoadSubPlayerCallback	callback;
		void					*user_data;

	};


	//***************************************************************************

public:
	RUSubstitutionManager(const RUGameSettings& gameSettings, SIFGameWorld *game_world);
	~RUSubstitutionManager();

	/// Reset the substitution manager.
	void	Reset();
	void	GameReset();

	void	UpdateSimulation();
	void	SyncUpdate();

	/// Handle 'player_deleted' event.
	void OnPlayerDeleted(ARugbyCharacter* player);

	/// Get the number of interchanges that a team has completed (pending interchanges don't count yet)
	int GetNumInterchangesCompleted( const RUTeam *team);

	/// Get the number of remaining interchange events available to a team (counting finished and pending events against the alotted total)
	int GetNumRemainingInterchanges( const RUTeam* team, bool include_pending=true );

	/// Get the maximum number of interchanges available to a team
	int GetMaxInterchangeEvents();

	/// Gets the best player for replacing the given player
	int GetBestReplacingPlayer(ARugbyCharacter *leaving_field );

	/// Get the number of players in sinbin from the specified team side. Queried by the HUD manager
	int GetNumPlayerInSinbin( SSTEAMSIDE team_side );

	/// Add a new interchange event, does checking if allowed, removes other pending events that conflict
	/// returns true if event added.
	/// GG SRA: Now returns the interchange reason if it failed, valid if it works
	EValidInterchangeReason AddInterchangeEvent( const RUInterchangeEvent &event);

	/// Query the number of queued events
	int GetNumQueuedEvents();

	/// Query the number of queued exchange events for a specific side
	int GetNumQueuedEvents( RUTeam* team );

	/// Get the team that the event refers to.
	RUTeam *GetQueuedEventTeam(int event_index);

	/// Get an event uid
	RUInterchangeEvent* GetQueuedEventByUID( int event_uid ) const;

	/// Get the pending_idx'th pending event.
	RUInterchangeEvent *GetQueuedEvent(int pending_idx);

	/// Get the highest priority pending event. (Injury > tactical etc..)
	RUInterchangeEvent *GetHighestPriorityPendingEvent();

	/// Get the current + replacement player db id's.
	bool	GetQueuedEventPlayerIds(int event_index, int *current_player_db_id, int *replacement_player_db_id);

	// Checks to see if there is an event queued up to send off our pro player
	bool	GetIsProPlayerSendOffEventQueued();

	/// Get all substitutions matching a given type.
	void	GetSubstitutionsMatchingEventType( MabVector< RUInterchangeEvent* >& substitution_list_out, RU_INTERCHANGE_EVENT_TYPE event_type ) const;


	/// Perform the pending interchange and remove from queue.
	void PerformPendingInterchange(RUInterchangeEvent *entry,  LoadSubPlayerCallback callback, void *user_data, bool do_team_swap=true);

	static void PerformPendingCallback(void *user_data, ARugbyCharacter* player, ARugbyCharacter* old_player, bool is_abort);

	/// Fire off all pending substitutions at half-time.
	void	StartAllPendingInterchanges(bool is_async);

	/// Have all pending substitutions completed?
	bool	HavePendingInterchangesCompleted();

	/// Manage interchanges for ai teams.
	void	UpdateAIInterchanges();

	void	RollOverSinBinTimers();

	/// Is player in sinbin?
	bool	IsPlayerInSinBin(int player_db_id, int team_idx) const;

	/// Get how long this player has left in the sin bin
	float	GetSecondsRemainingInSinBin(int player_db_id, int team_idx) const;

	/// Does team have a sinbinned player
	bool	HasPlayerInSinBin(int team_idx) const;

	/// Check to see if the player has been injured
	bool	IsPlayerInjured(int player_db_id, int team_idx) const;

	/// Check to see if the player has been exchanged
	bool	IsPlayerExchanged(int player_db_id, int team_idx) const;

	/// Has the player been in sentoff?
	bool	IsPlayerSentOff(int player_db_id, int team_idx) const;

	/// Does team have a sent off player
	bool	HasPlayerSentOff(int team_idx) const;

	int NumPlayersSentOff(int team_idx) const;
	/// Is player on the field?
	bool	IsPlayerOnField(int player_db_id, int team_idx) const;

	/// Has player already been substuted off? (can't be sub'd on again\ - except for specific rules)
	bool	AlreadySubstituted(int player_db_id, int team_idx) const;

	/// Is player pending any 'non-cancellable' actions.
	bool	PlayerPendingSendOffOrInjury(int player_db_id, int team_idx) const;

	/// Retrieves all the finished interchange events for a specific player.
	/// Interchange events that haven't completed yet will not be added to this list.
	void	GetPlayerFinishedInterchangeEvents( MabVector< RU_INTERCHANGE_EVENT_TYPE >& player_interchange_events, int player_db_id, int team_index ) const;

	/// Retrieves all the 'pending' interchange events for a specific player.
	void	GetPlayerPendingInterchangeEvents( MabVector< RU_INTERCHANGE_EVENT_TYPE >& player_interchange_events, int player_db_id, int team_index ) const;

	/// Queue a tactical substitution of 'player' for the substitute with db_index='db_id'.
	///  returns false if failed (too many pending, or already pending)
	/// returns the reason why it failed, or valid if it's good
	EValidInterchangeReason	QueueTacticalSubsitution(RUTeam *team,ARugbyCharacter* player, int db_id);

	/// Do a substitution (replace player with the with db_id player) as soon as loaded (async)
	void	SubstitutePlayer(ARugbyCharacter* player, int db_id,  int strip_id, RUTeam *team, LoadSubPlayerCallback callback, void *user_data, bool do_swap = true );

	/// Perform the swapping of a player onto the field to replace another player
	void	SwapPlayerOntoField(ARugbyCharacter* entering_field, ARugbyCharacter* leaving_field, PlayerState leaving_state = PS_BENCH);

	/// Can player (should be on bench) be used as a replacement for an injured player.
	bool	CanBeInjurySubdOn(int db_id, int team_idx);

	// Checks the sin bin for players returning, outside the update loop.
	void	ForceCheckReturningSinBins();


private:
	/// Process substitution queue. (Update + SyncUpdate)
	void	ProcessSubstitutionQueue(bool sync_update);

	/// Check sin bin for players that have timed out, if so queue up for return
	void	CheckReturningSinBins();

	/// Callback from load thread (load a player).
	//static void AsyncLoadPlayerJob(void *user_data, SIFAsyncLoadingThread *load_thread, bool is_abort);

	/// Callback for clearing the pending queue. (loading all players pending).
	static void DoAllPendingCallback(void *user_data, ARugbyCharacter* player, ARugbyCharacter* old_player, bool is_abort );

#ifdef ENABLE_GAME_DEBUG_MENU
	void	ShowDebugInfo();
#endif

	///**************************************************************************
	/// UI Player loading for icons.
public:
	/// Load a 'partial' player - very few components loaded (used for face generation).
	void LoadUIPlayer(PlayerCustomisationInfo *cs_info, LoadSubPlayerCallback callback, int32 player_id, void *user_data );
	/// Delete a player loaded with 'LoadPlayer'
	void	DeleteLoadedPlayer(ARugbyCharacter* player);

private:
	/// Callback from load thread (load a UI player - for icon rendering).
	static void	AsyncLoadUIPlayerJob(void *user_data, ARugbyCharacter* player, ARugbyCharacter* old_player, bool is_abort);


	///**************************************************************************
	/// Strip swapping.
public:
	/// Are any strip changes pending?
	bool	HaveStripChangesCompleted();

	/// Set the team strips to those setup in game_settings.
	void	SetStripsToMatchGameSettings(RUGameSettings *game_settings, bool asynchronous, bool is_career_hub=true);

	static void DeferredTeamSkin(DeferredTeamSkinInfo *info);

	/// Swap the team strip to the strip specified - asynchronous, completes automatically.	(Adds job to strip_change_queue).
	void	SwapTeamStrip(RUTeam *team, int strip_db_id);

private:
	/// Callback from load thread (load a strip).
	static void AsyncLoadStripJob( void *user_data, SIFAsyncLoadingThread *load_thread, bool is_abort);

	/// Setup by SetStripsToMatchGameSettings, calls SwapTeamStrip.
	static void StripSwapJob( void *user_data, SIFAsyncLoadingThread *load_thread, bool is_abort);

	void	TestStripSwap();

	///***************************************************************************

private:
	const RUGameSettings& mGameSettings;

	SIFGameWorld	*game;
	MabVector<SubstitutionQueueEntry*> substitution_queue; // Formerly loading_queue
	MabVector<RUInterchangeEvent*> interchanges;
	LOAD_ALL_STATE	load_all_state;

	SinBins			sin_bins;

	int		current_interchange_event_id;
	float	last_ai_interchange_update_time;

	MabVector<PendingStripChangeQueueEntry*> strip_change_queue;
	PendingStripChangeQueueEntry	*current_strip_change;

#if PLATFORM_XBOX360
	MabLockedQueue<StripSwap> strip_swap_queue;
#else
	//#rc3_legacy MabLockedQueue<StripSwap*> strip_swap_queue;
	TQueue<StripSwap*> strip_swap_queue;
#endif

	long	last_debug_key_used;

	//float pro_player_performance;
	//float time_since_last_applied;
};


#endif	// _RU_SUBSTITUTION_MANAGER_H_
