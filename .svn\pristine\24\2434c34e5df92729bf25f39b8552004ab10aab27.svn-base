#pragma once
 
#include "Misc/DateTime.h"
#include "CompetitionModePopulatedInterface.generated.h"
 
USTRUCT(BlueprintType)
struct FCompetitionMenuPopulationPack
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly)
	FText Competition;
	UPROPERTY(BlueprintReadOnly)
	FText Round;
	UPROPERTY(BlueprintReadOnly)
	FText NextMatchHomeTeam;
	UPROPERTY(BlueprintReadOnly)
	FText NextMatchAwayTeam;
	UPROPERTY(BlueprintReadOnly)
	int NumberOfUserTeams;
	UPROPERTY(BlueprintReadOnly)
	FDateTime LastModificationTime;
	UPROPERTY(BlueprintReadOnly)
	int SaveIndex;
};

UINTERFACE(MinimalAPI, Blueprintable)
class UCompetitionModePopulatedInterface : public UInterface
{
	GENERATED_BODY()
};

class ICompetitionModePopulatedInterface
{
	GENERATED_BODY()
 
public:
	UFUNCTION(BlueprintCallable, BlueprintImplementableEvent)
		void OnCompetitionPopulated(
			UUserWidget* InfoPanelRoot,
			FCompetitionMenuPopulationPack Data
		);
};