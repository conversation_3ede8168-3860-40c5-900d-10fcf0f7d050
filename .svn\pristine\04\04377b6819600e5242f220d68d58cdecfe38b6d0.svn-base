/*--------------------------------------------------------------
|        Copyright (C) 1997-2007 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/SIFGameWorld.h"
#include "Rugby.h"
#include "SignageActor.h"
#include "Databases/RUGameDatabaseManager.h"
#include "Databases/SqliteMabStatement.h"
#include "Mab/Central/MabCentralTypeDatabase2.h"
#include "Mab/Resources/MabGlobalResourceSet.h"
#include "Mab/Streams/MabStream.h"
#include "Mab/Types/MabNamedValueList.h"
#include "Match/AI/Actions/RUAction.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/Roles/Officials/SSRoleReferee.h"
#include "Match/AI/Roles/Officials/SSRoleTouchJudge.h"
#include "Match/AI/SetPlays/SSSetPlayManager.h"
#include "Match/AI/SetPlays/SSSetPlayManager.h"
#include "Match/Agents/SIFHumanAgent.h"
#include "Match/Ball/SSBall.h"
#include "Match/Camera/SSCameraManager.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUGameRumbleManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerFacePose.h"
#include "Match/Components/RUPlayerLookAt.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/RUPlayerParticleSystems.h"
#include "Match/Components/RUPlayerSound.h"
#include "Match/Components/RUPlayerState.h"
#include "Character/Component/RugbyCharacterStyleController.h"
#include "Character/RugbyPlayerController.h"
#include "Components/SSHumanPlayer.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Match/Debug/RUCBDebugService.h"
#include "Match/Debug/RUDebugService.h"
#include "Match/Debug/RUGameDebugSettings.h"
#include "Match/Debug/RUMemoryTags.h"
#include "Match/Debug/RUProfilerService.h"
#include "Match/Effects/SIFEffectSystem.h"
#include "Match/HUD/RU3DHUDManager.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/HUD/RUHUDUpdaterContextual.h"
#include "Match/HUD/RUHUDUpdaterReplay.h"
#include "Match/HUD/RUHUDUpdaterTraining.h"
#include "Match/Input/SSInputManager.h"
#include "Match/PlayerProfile/SIFPlayerProfileManager.h"
#include "Match/PlayerProfile/SIFPlayerProfilePropertyDefs.h"
#include "Match/RUAsyncLoadingEnable.h"
#include "Match/RugbyUnion/CompetitionMode/OLD_RU/RUCompetitionModeManager.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Database.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Tournament.h"
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "Match/RugbyUnion/ContextBucket/RUCBGameStats2Bucket.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseRuck.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseScrum.h"
#include "Match/RugbyUnion/RUDBTeam.h"
#include "Match/RugbyUnion/RUDatabaseConstants.h"
#include "Match/RugbyUnion/RUEmotionEngineManager.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameGetToBall.h"
#include "Match/RugbyUnion/RUGameLocomotion.h"
#include "Match/RugbyUnion/RUGameMovement.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUInputKickInterface.h"
#include "Match/RugbyUnion/RULineoutRater.h"
#include "Match/RugbyUnion/RUMatchIntensityManager.h"
#include "Match/RugbyUnion/RUMemProfiler.h"
#include "Match/RugbyUnion/RUPlayerFactory.h"
#include "Match/RugbyUnion/RUPlayerResource.h"
#include "Match/RugbyUnion/RUSandboxGame.h"
#include "Match/RugbyUnion/RUStadiumFactory.h"
#include "Match/RugbyUnion/RUStadiumManager.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUSubstitutionManager.h"
#include "Match/RugbyUnion/RUTackleHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/RUTeamConfidenceHandler.h"
#include "Match/RugbyUnion/RUTeamFacesGenerator.h"
#include "Match/RugbyUnion/RUWeatherManager.h"
#include "Match/RugbyUnion/Rules/Offside/RUOffsideIndicator.h"
#include "Match/RugbyUnion/Rules/RURuleTriggerEnum.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/RugbyUnion/Statistics/RUStatisticsSystem.h"
#include "Match/RugbyUnion/Statistics/RUStatsTracker.h"
#include "Match/RugbyUnion/TutorialMode/RUTutorialManager.h"
#include "Match/SIFGameContext.h"
#include "Match/SIFGamePauseState.h"
#include "Match/SIFTimeSource.h"
#include "Match/SSColourHelper.h"
#include "Match/SSGameTimer.h"
#include "Match/SSGameTimer.h"
#include "Match/SSMath.h"
#include "Match/SSPlayerFilter.h"
#include "Match/SSPostEffectsManager.h"
#include "Match/SSReplaysMk2/SSReplayManager.h"
#include "Match/SSRole.h"
#include "Match/SSRoleFactory.h"
#include "Match/SSRoleNull.h"
#include "Match/SSScreenWipeManager.h"
#include "Match/SSSpatialHelper.h"
#include "Match/Audio/RUGameWorldAudio.h"
#include "Match/HUD/RUContextualHelper.h"
#include "Utility/RUNetworkStateChecker.h"
#include "Utility/RURandomNumberGenerator.h"
#include "Utility/Helpers/SIFGameHelpers.h"
#include "Utility/Helpers/SIFMatchmakingHelpers.h"
#include "Networking/RUNetworkState.h"
#include "Networking/VoipManager.h"
#include "Networking/NetworkEnums.h"

#include "ARugbyProceduralMeshActor.h"
#include "ProceduralMeshComponent.h"
#include "RugbyMarkerActor.h"

#include "Engine/Engine.h"
#include "GameModes/RugbyGameState.h"
#include "GameModes/RugbyGameModeBase.h"
#include "Kismet/GameplayStatics.h"
#include "RugbyLevelManager.h"
#include "GameFramework/Controller.h"
#include "Match/SIFUIConstants.h"
#include "Utility/Helpers/SIFUIHelpers.h"
#include "EngineUtils.h"
#include "Audio/RUCrowdReactionManager.h"
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "SIFAchievementChecker.h"
#include "SIFFlowConstants.h"
#include "FlowNodes/TrainingFlowNode.h"
#include "FlowNodes/FlowControlManager.h"
#include "PlatformServices/SIFXboxOneSaveRequestManager.h"
#include "RugbyPauseHandler.h"
#include "Networking/UnrealTransportReliable.h"
#include "Character/JobThread.h"
#include "Character/MeshMergeThread.h"
/*#rc3_legacy_include
#include "MabBATSContext.h"
#include "MabControlActionManager.h"
#include "MabControllerManager.h"
#include "MabMemDebugDatabase.h"
#include "MabProfiler.h"
#include "MabResourceSet.h"
#include "Match/SIFGameJobQueue.h"
#include "NMMabCharacterController.h"
#include "NMMabResource.h"
#include "PSSGDatabase.h"
#include "PSSGLinkResolver.h"
#include "PSSGMabAnimation.h"
#include "PSSGMabDatabaseResourceFile.h"
#include "PSSGMabDebugDraw.h"
#include "PSSGMabLightDatabase.h"
#include "PSSGMabNodeTraversal.h"
#include "PSSGMabShaderManager.h"
#include "PSSGMabTraversals.h"
#include "RUAnimationDebugSettings.h"
#include "RUCrowdManager.h"
#include "RUCutsceneDebugSettings.h"
#include "RUGameWorldAudio.h"
#include "RUNetworkReplay.h"
#include "RUNetworkState.h"
#include "RUPSSGCameraEffects.h"
#include "RUPSSGJumboTron.h"
#include "RugbyCharacterBlendController.h"
#include "RURugbyDollarsChecker.h"*/
#include "Match/RugbyUnion/Statistics/RUStatsScorer.h"
/*#include "SIFAmbientPointCloud.h"
#include "SIFAsyncLoadingThread.h"
#include "SIFAsyncRenderingThread.h"
#include "Match/Debug/SIFCommonDebugSettings.h"
#include "SIFDebug.h"
#include "SIFDebugInput.h"
#include "LevelData/SIFLevelLauncher.h"
#include "SIFLevelPostSettings.h"
#include "SIFMatchmakingHelpers.h"
#include "SIFMenuHelpers.h"
#include "SIFPSSGParticleSystemsManager.h"
#include "SIFPSSGShaderParameters.h"
#include "SIFPSSGTraversals.h"
#include "SIFPSSGTraversals.h"
#include "SIFPSSGUtil.h"
#include "SIFParticleEffectComponentHandler.h"
#include "SIFRenderUtil.h"
#include "SIFResourceManager.h"
#include "SIFRumbleEffectComponentHandler.h"
#include "SIFShadowMap.h"
#include "SIFShadowMap.h"
#include "SIFShadowSettings.h"*/
#ifdef ENABLE_SOAK_TEST
#include "Utility/Soak/SIFSoakManager.h"
#endif
/*#include "SIFSoundEffectComponentHandler.h"
#include "SIFStatisticsHandler.h"
#include "SIFTimeEffectComponentHandler.h"
#include "SIFUIHelpers.h"
#include "SIFUIInputAdapter.h"
#include "SIFViewManager.h"
#include "SIFWindow.h"
#include "SIFWindowSystem.h"
#include "SSAttractSequenceManager.h"
#include "SSCutSceneTags.h"
#include "SqliteMabDatabase.h"
#include "simpleAnimRegistry.h"
#include <MabCamera.h>
#include <MabClosure.h>
#include <MabLuaInterpreter.h>*/
#include "Mab/Net/MabNetworkManager.h"
/* #include <MabObjectResource.h>
#include <MabResourceFactory.h>
#include <MabStackTrace.h>
#include <MabUIControllerAdapter.h>
#include <NMMabAnimationEvents.h>
#include <NMMabAnimationNetwork.h>
#include <NMMabAnimationRepository.h>
#include <NMMabAnimationWorld.h>
#include <PSSG.h>
*/

#include "Character/RugbyPlayerController.h"
#include "RugbyGameInstance.h"
#include "RugbyCameraActor.h"
#include "RugbyAsyncTaskQueue.h"
#include "RugbyAsyncTasks.h"

#include "wwStadiumRuntime/Public/Crowdbase.h"
#include "wwStadiumRuntime/Public/SkyDomeBase.h"
//#include "wwStadiumRuntime/Public/FieldBase.h"
#include "FieldRugby.h"
#include "wwStadiumRuntime/Public/StadiumBase.h"
#include "wwStadiumRuntime/Public/SignageActor.h"
#include "PostProcessingRugby.h"
#include "DatabaseManager.h"

//Include debug window screen for debug settings
#include "UI/Screens/WWUIScreenDebugWindow.h"

#include "Utility/consoleVars.h"
#include <thread>
//#define ENABLE_REPLAY_IN_SANDBOX
//#define DOING_REPLAY_TEST

#if (PLATFORM_WINDOWS)
//LLOYD: Temporary
//#rc3_legacy_include #include <MabLanGameManager.h>
#endif

#if (PLATFORM_PS4)
//#rc3_legacy_ps4 #include <PS4MabOnlineGameManager.h>
#endif

#if (PLATFORM_XBOXONE)
//#rc3_legacy_xboxone #include "XboxOneMabUserInfo.h"
//#rc3_legacy_xboxone #include "SIFXboxOneSaveRequestManager.h"
#endif

#ifdef ENABLE_CG_SKINNING
#include "PSSGTraversalConvertToCgSkinning.h"
#include "PSSGCgSkinParameterSource.h"
#endif

#ifndef DISABLE_COMMENTARY
#include "Commentary/RUCommentary.h"
#endif

#ifdef ENABLE_RU_TEST_BED
#include "Match/RugbyUnion/TestBed/RUTestBedManager.h"
#if PLATFORM_WINDOWS
#include "SIFCommandLine.h"
static const char* RU_TESTBEDS_ARGNAME = "runtestbed";
#endif
#endif

#ifdef ENABLE_RUGED
#include "RUStatsTestManager.h"
#endif

#define EXPLICIT_OBJECT_DATABASE_UPDATES
#define CUSTOM_TEAM_CROWD_FLAG_CLOTHES_TEXTURE_NUM (262)

// Uncomment to enable team faces generation test.
//#define ENABLE_TEAM_FACES_GENERATION_TEST

// Uncomment to swap all customisation data when swapping sandbox/menu teams
#define ENABLE_FULL_TEAM_SWAP

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
#define ENABLE_NETWORK_FRAME_INFO	1
#else
#define ENABLE_NETWORK_FRAME_INFO	0
#endif

enum
{
	ASYNC_LOAD_NONE = 0,
	ASYNC_LOAD_REQUESTED,
	ASYNC_LOAD_DEFERED_START
};

/// Home team loader states.
enum
{
	TS_IDLE = 0,
	TS_INIT,
	TS_DELETE,
	TS_DELETE_WAIT,
	TS_REPOPULATE_ASYNC,
	TS_REPOPULATE_DONE,
	TS_INIT_QUICKLOAD,
	TS_QUICKLOAD_WAIT,
	TS_QUICKLOAD_FORMATIONS
};

TAutoConsoleVariable<int32> CVarForceExternalRateUpdateEveryFrame(TEXT("ww.ForceExternalRateUpdateEveryFrame"), 0, TEXT("Whether to force the external rate settings to update every frame."));

TAutoConsoleVariable<int32> CVarPrintTickInfo(TEXT("ww.PrintTickInfo"), 0, TEXT("When set to 1 will print information about the pre and post sync tick to the log."));

extern TAutoConsoleVariable<int32> CVarMontageAdvanceThreadPriority;


// TODO : Replace with actual code to select appropriate officials team from the database
#define OFFICIALS_DATABASE_INDEX 3

const char* EFFECT_SYSTEM_DEFINITIONS_RESOURCE = "EffectDefinitions";

// Number of frames to delay a 'DelayedAwakenInstance' - stops ProcessBuffer running out of memory on PS3.
static const int DELAYED_AWAKEN_DELAY = 3;

// Number of frames to delay a checking the initial states. We have to wait a couple of frames for the world to awaken.
static const int DELAYED_INITIAL_STATE_DELAY = 30;

static const char *TRAINING_GROUND_POST_EFFECTS_FILE = "environments/effects/post_settings_training.xml";
static const char *TRAINING_GROUND_SHADOW_SETTINGS_FILE = "environments/effects/shadow_settings_training.xml";
static const char *UI_CUSTOMIZE_PLAYER_POSTEFFECTS = "environments/effects/post_settings_customise_player.xml";

const char *SIFUI_NETWORK_GAME_SUSPENDED = "game_suspended";

extern TAutoConsoleVariable<int32> CVarMontageAdvanceThreadMethod;

//#rc3_legacy MABRUNTIMETYPE_IMP1( SIFGameWorld, MabResourceBase );

//static MabTime ang_time( 0.0f );
//static MabTime ang_time2( 0.0f );

namespace SIFCgSkinning
{

	void Initialise()
	{
#ifdef ENABLE_CG_SKINNING
		// FIXME: Move somewhere
		// Register the skin matrix source
		PShaderParameterSourceGroup::registerShaderParameterSourceGroup(PCgSkinParameterSource::s_boneMatrices);

		// Set up the skin matrix source
		PCgSkinParameterSource::s_boneMatrices.addSource(PCgSkinParameterSource::s_cgSkinParameterSource);
#endif
	}

	void Convert(PSSG::PDatabase* database)
	{
#ifdef ENABLE_CG_SKINNING
		// Convert all of the scenes in the database to use Cg for skinning
		PTraversalConvertToCgSkinning convertToCgSkinningTraversal;
		for (PListIterator sceneIterator(database->getSceneList()); sceneIterator; sceneIterator.next())
		{
			PNode *root = (PNode *)sceneIterator.data();
			if (!root)
				continue;
			convertToCgSkinningTraversal.traverseDepthFirst(*root);
			if (convertToCgSkinningTraversal.getResult() != PE_RESULT_NO_ERROR)
			{
				MABBREAKMSG("Unable to convert shaders to Cg skinning shaders");
			}
		}
#endif
		MABUNUSED(database);
	}
}

//struct SIFClearShaderLights
//{
//	PSSG_PRIVATE_ASSIGNMENT_OPERATOR(SIFClearShaderLights)
//
//public:
//	void visit(PSSG::PRenderInstance& render_instance)
//	{
//		PSSG::PShaderInstance* shader_instance = render_instance.getShaderInstance();
//		if( shader_instance != NULL )
//			SIFUberShaderRegistry::ClearShaderLights(*shader_instance);
//	}
//};



// Team ids for frontend.
unsigned short FRONTEND_TEAM1_ID = 0;

// Fade time for going into async-load.
const float ASYNC_LOAD_FADE_TIME = 0.5f;



const TArray<SIFGameWorld::PlayerOverride> MENU_WOMEN_OVERRIDES[SSTEAMSIDE::SIDE_NONE] =
{
	{ // SIDE_A
		{ 12, 1, -1, 5501 },
		{ 13, 2, -2, 5502 },
		{ 14, 3, -3, 5503 },
		{ 15, 4, -4, 5504 },
		{ 16, 5, -5, 5505 },
		{ 17, 6, -6, 5506 },
		{ 18, 7, -7, 5507 },
		{ 19, 8, -1, 5501 },
		{ 20, 9, -2, 5502 },
		{ 21, 10, -3, 5503 },
		{ 22, 11, -4, 5504 },
		{ 23, 12, -5, 5505 },
		{ 24, 13, -6, 5506 }
	},
	{ // SIDE_B
		{ 12, 1, -1, 5601 },
		{ 13, 2, -2, 5602 },
		{ 14, 3, -3, 5603 },
		{ 15, 4, -4, 5604 },
		{ 16, 5, -5, 5605 },
		{ 17, 6, -6, 5606 },
		{ 18, 7, -7, 5607 },
		{ 19, 8, -1, 5601 },
		{ 20, 9, -2, 5602 },
		{ 21, 10, -3, 5603 },
		{ 22, 11, -4, 5604 },
		{ 23, 12, -5, 5605 },
		{ 24, 13, -6, 5606 }
	},
	{ // SIDE_OFFICIALS
	}
};

//#define REPULSION_TEST

#ifdef REPULSION_TEST
static FVector test_repulsion_pos = FVector::ZeroVector;
static float test_repulsion_range = 10.0f;
#endif


///-------------------------------------------------------------------------------
/// SIFGameWorld: Constructor
///-------------------------------------------------------------------------------
#if PLATFORM_WINDOWS || PLATFORM_XBOX360 || PLATFORM_XBOXONE
#pragma warning(disable:4355) // Disable warning about using this in initialiser - queue_of_death requires this
#endif

SIFGameWorld::SIFGameWorld(URugbyGameInstance& gameInstance,
	RUGameSettings& gameSettings,
	//MabControlActionManager *control_action_manager,
	//NMMabAnimationRepository* repository,
	MabLockStepTimeSource* simulation_time,
	WORLD_ID wworld_id)
	: //MabNonCopyable(),
		  //MabResourceBase(hheap, resource_name, auto_destroy)
	mGameInstance(gameInstance)
	, game_settings(gameSettings)
	, isRendering()
	, simulation_time(simulation_time)
	//#rc3_legacy , stadium_node(NULL)
	//#rc3_legacy , pssg_stadium_id()
	//#rc3_legacy , nodes_added_to_scene()
	//#rc3_legacy , queue_add_child(),
	, unused_time(0.0f)
	, active_camera(NULL)
	, hud_updater(NULL)
	, hud_updater_contextual(NULL)
	//#rc3_legacy , hud_updater_strategy()
	, replay_hud_updater(NULL)
	, stadium_manager(NULL)
	, game_stats2bucket(NULL)
	, rumble_manager(NULL)
	, emotion_engine_manager(NULL)
	, relight_scene(false)
	, do_mem_log_print(false)
	, reenabled_rendering(false)
	, world_id(wworld_id)
	, world_state(WORLD_STATE::FREED)
	, async_loading_started(false)
	//#rc3_legacy_pssg ,node_clean_up()
	, settings_path("")
	, shadow_settings_path("")
	, delayed_awaken(0)
	, delayed_initial_state_check(DELAYED_INITIAL_STATE_DELAY)
	, initial_state_checked(false)
	, texture_swaps()
	, load_pending_test_counter(-1)
	, async_load_start_requested(0)
	, non_permanent_players_loaded(false)
	, sandbox_environment_setting(SBE_MENU)
	, requested_sandbox_environment_setting(SBE_MENU)
	, customisation_player(NULL)
	, using_training_lights(false)
	, pseudo_competition_id(0)
	, restart_reload_players(0)
	, crowd_manager(nullptr)
	, sky_manager(nullptr)
	, stadium_overloading_manager(nullptr)
	, field_manager(nullptr)
	, m_quickLoadActive(false)
	, m_quickLoadDelayedTeamSwapRequest(TEAM_SWAP_REQUEST::TEAM_SWAP_NONE)
	, m_bForceReloadTeam(false)
	, m_teamSwapActive(false)
	, world_is_revealing(false)
	, world_is_revealed(false)
	, world_is_allocated(false)
	, m_isLoading(false)
	, m_loadCompletionEstimate(0)
	, m_asyncTaskQueue()
	, m_gameWorldSetupState(SETUP_IDLE)
	, ball(NULL)
	, camera_manager(NULL)
	, m_isPlayGo(true)
	, network_manager(NULL)
	, network_state(NULL)
	, random_number_generator(NULL)
	, events(nullptr)
	, LastFrameStep(0, MabTime(0.f), 0)
{
	hud_updater_strategy[0] = NULL;
	hud_updater_strategy[1] = NULL;

	m_characterMapChunks.Init(nullptr, SIDE_NONE);
	m_characterMapInitialRevealCallback.Init(FDelegateHandle(), SIDE_NONE);

	//#rc3_legacy heap = MabMemGetDefaultObjectHeap(this);
}

///-------------------------------------------------------------------------------
/// SIFGameWorld: Destructor
///-------------------------------------------------------------------------------

SIFGameWorld::~SIFGameWorld()
{
	// Nothing to do, the game world resource is de-allocated in FreeAllocate()
	if (SIFApplication::GetApplication() && SIFApplication::GetApplication()->GetAchievementChecker())
		SIFApplication::GetApplication()->GetAchievementChecker()->RemoveGameEvents(this);
	if (SIFApplication::GetApplication() && SIFApplication::GetApplication()->GetStatsScorer())
		SIFApplication::GetApplication()->GetStatsScorer()->TermGameEvents(this);

}

///-------------------------------------------------------------------------------
/// SIFGameWorld: Destructor
///-------------------------------------------------------------------------------
FString SIFGameWorld::GetWorldIdAsString()
{
	switch (world_id)
	{
		case WORLD_ID::GAME:
		return FString(TEXT("GAME"));
		case WORLD_ID::SANDBOX:
		return FString(TEXT("SANDBOX"));
		case WORLD_ID::MENU:
		return FString(TEXT("MENU"));
	}
	return FString(TEXT("UNKNOWN"));
}

void SIFGameWorld::InitialiseNetworking()
{
#ifdef wwDEBUG_TG
	// Stop creating the network objects when testing with trents networking.
	return;
#endif

	if (game_settings.game_settings.network_game)
	{
		network_manager = SIFApplication::GetApplication()->GetNetworkManager();
		network_state = MabMemNew(heap) RUNetworkState(this);
	}
}

void SIFGameWorld::DestroyNetworking()
{
	if (network_state)
	{
		MabMemDelete(network_state);
		network_state = nullptr;
	}

	network_manager = nullptr;
}

///-------------------------------------------------------------------------------
/// Initialises all the common actors and assets that are used by the
/// game, such as the stadium, field etc and an classes required in both sandbox and
/// main game worlds.  Is called in the constructor
///-------------------------------------------------------------------------------

void SIFGameWorld::InitialiseCommon()
{
	MABMEM_PROFILE_SECTION_START(profile1, "InitializeCommon");

	RL3Database *rl3_database = SIFApplication::GetApplication()->GetGameDatabaseManager()->GetRL3Database();
	rl3_database->Lock();

	// Create the RU game events.
	//MABASSERTMSG(!events,"why do we already have events here?");
	// :jb clear the ptr first so that we can detect this condition when we do game->GetEvents()
	events = nullptr;
	events = std::make_unique<RUGameEvents>();

	player_filters = std::make_unique<RUPlayerFilters>(this);

	if (!IsMatch())
	{
		// Patch the player resource now
		//#rc3_legacy_db_patching
		//if(RUPlayerResource* player_resource =
		//	MabCast<RUPlayerResource>(MabGlobalResourceSet::FetchResource("player")))
		//{
		//	// Wait until the player database has been patched.
		//	// If its been patched it won't be in the list
		//	while( SIFPSSGUtil::DatabasePatcher::PatchingDatabase( player_resource->GetDatabaseId() ) &&
		//		!SIFApplication::GetApplication()->GetAsyncLoadThread()->Aborting())
		//	{
		//		MabCore::Sleep(1);
		//	}
		//
		//	player_resource->Process();
		//}
	}

	CalculatePseudoCompetitionId();

	//#rc3_legacy camera_effects = MabMemNew(heap) RUPSSGCameraEffects(heap);

	substitution_manager = std::make_unique<RUSubstitutionManager>(game_settings, this);

	// init stadium manager BEFORE stadium factory and you set up lighting on the menu stadium.
	if (IsMatch())
	{
		stadium_manager = SIFApplication::GetApplication()->GetMatchStadiumManager();
	}
	else
	{
		stadium_manager = SIFApplication::GetApplication()->GetSandboxStadiumManager();
	}

	stadium_manager->Initialise(nullptr, game_settings, this->pseudo_competition_id);

	{
		locomotion = std::make_unique<RUGameLocomotion>();
		lineout_rater = std::make_unique<RULineoutRater>(*this);
	}

	{
		// Create the game object database
		//#rc3_legacy object_database = std::make_unique<SIFGameObjectDatabase>();
		// start the role factory
		role_factory = std::make_unique<SSRoleFactory>(this);

		//#rc3_legacy ambient_point_cloud = MabMemNew(heap) SIFAmbientPointCloud(*this, heap, world_id);

		//#rc3_legacy GetLightLinkManager()->SetAmbientPointCloud(ambient_point_cloud->GetCloud());
	}

	rl3_database->Unlock();

	{
		//overriding the default world for the main menu, can remove later, should be a performance boost on Switch.
		//If HES asks, Paul told me to - SRA
#if PLATFORM_SWITCH
		if (IsMenu())
		{
			RUStadiumManager* st_mgr = GetStadiumManager();
			if (st_mgr)
			{
				st_mgr->SetStadiumModelFileName("/Game/Maps/Stadiums/TRAININGFIELD");
				UE_LOG(LogTemp, Log, TEXT("Overriding the menu map stadium"));
			}
		}
#endif

		// create the stadium factory and load up the first stadium to create a base PSSG scene to clone objects into
		//#rc3_legacy
		//RUStadiumFactory stadium_factory;
		//stadium_factory.CreateStadiums(this, game_settings, GetLightDatabase(), heap, stadium_resources);

		MabFilePath asset_path("");
		RUStadiumManager::ConstructStadiumPath(this, game_settings, asset_path);

		FString uniqueInstancePrefix;

		switch (world_id)
		{
			case WORLD_ID::GAME:
			uniqueInstancePrefix = FString("MatchLevel");
			break;
			case WORLD_ID::MENU:
			uniqueInstancePrefix = FString("MenuLevel");
			break;
			case WORLD_ID::SANDBOX:
			uniqueInstancePrefix = FString("SandboxLevel");
			break;
			default:
			uniqueInstancePrefix = FString("MysteryLevel");
			break;
		}

		URugbyLevelChunk* stadiumLevelChunk = GetGameInstance().GetLevelManager()->CreateLevelChunk(SIFGameHelpers::GAConvertMabStringToFString(asset_path.GetPath()), true, uniqueInstancePrefix);
		if (UOBJ_IS_VALID(stadiumLevelChunk))
		{
			AddStadiumChunk(stadiumLevelChunk);
		}
		else
		{
			MABBREAKMSG("Stadium sub level was null.")
		}

		if (IsMenu())
		{
			RUStadiumManager* st_mgr = GetStadiumManager();
			if (st_mgr)
			{
				st_mgr->SetStadiumModelFileName("");
				UE_LOG(LogTemp, Log, TEXT("Removing the override for the menu map stadium"));
			}
		}

	}

	{
		// Create and initialise the particle system manager.
		//#rc3_legacy
		//particles_manager = MabMemNew(heap) SIFPSSGParticleSystemsManager(GetSceneDatabaseId(), this, heap);
		//particles_manager->Initialise();
		//
		//shader_parameters = MabMemNew(heap) SIFPSSGShaderParameters();
		//
		// dewald - we now inialise the networking when switching worlds
		// == InitialiseNetworking()
		/*if (game_settings.game_settings.network_game)
		{
			network_manager = SIFApplication::GetApplication()->GetNetworkManager();
			network_state = MabMemNew(heap) RUNetworkState(this);
		}*/
	}

	{
		game_context = std::make_unique<SIFGameContext>(*this, SIFApplication::GetApplication()->GetCareerModeManager());

		//#rc3_legacy GetJobQueue()->SetContext( game_context );

		//#rc3_legacy CreateEffectSystem();

		//#afl_replay
		if (GetWorldId() == WORLD_ID::GAME)
		{
			events->game_restart.Add(this, &SIFGameWorld::InitialiseReplayManager);
		}

		// Create the rules
		rules = std::make_unique<RURules>(this);
		rules->EnableConsequences(true);
		rules->EnableTriggers(RURT_MAINGAMERULES);

		spatial_helper = std::make_unique<SSSpatialHelper>(this);
		movement = std::make_unique<RUGameMovement>(GetSpatialHelper());

		// Create the gamestate.
		game_state = std::make_unique<RUGameState>(this);

		strategy_helper = std::make_unique<RUStrategyHelper>(this);
		tackle_helper = std::make_unique<RUTackleHelper>(this);
		game_gtb = std::make_unique<RUGameGetToBall>(this);
		team_confidence_handler = std::make_unique<RUTeamConfidenceHandler>(this);
	}

	rl3_database->Lock();

	{
		screen_wipe_manager = std::make_unique <SSScreenWipeManager>(this);

		// Create the cutscene manager

		//#rc3_legacy
		cutscene_manager = std::make_unique<SSCutSceneManager>(this);


		//// Create the SIFView for the game. (returns pointer to MabCamera2 to use)
		//// HACK to get into game - more elegant way to avoid concurrent access to the render interface here is needed
		//#rc3_legacy
		//static bool first = true;
		//if( first )
		//{
		//	camera = GetViewManager()->EnableRUView(*game_context); // this will destroy views (and postfx), so can't run while one world is rendering
		//	first = false;
		//}
		//else
		//	camera = GetViewManager()->GetCurrentView()->Camera();
		camera = GetWorldCamera();
	}

	{
		// Create the camera manager.		

		camera_manager = MabMemNew(0) SSCameraManager(this, camera/*#rc3_legacy, stadium_manager->GetStadiumEVDSEvent()*/);
	}

	{
		if (IsMatch())
		{
			emotion_engine_manager = MabMemNew(heap) RUEmotionEngineManager(this);
			// Attach commentary
#ifndef DISABLE_COMMENTARY
			SIFApplication::GetApplication()->GetCommentarySystem()->AttachGameWorld(this);
#endif
		}
	}

	InitialiseAnimation();

	// Get the tournament manager and, if it's been created, register it.
	RUCareerModeManager* const career_mode_mgr = SIFApplication::GetApplication()->GetCareerModeManager();
	if (career_mode_mgr && career_mode_mgr->IsActive()) career_mode_mgr->OnGameWorldInitialised(GetEvents());

	rumble_manager = MabMemNew(heap) RUGameRumbleManager(this);

	world_audio = std::make_unique<RUGameWorldAudio>(this, SIFApplication::GetApplication()->GetRUAudio());

	rl3_database->Unlock();

	//Bind the debug options to the debug screen
	if (world_id == WORLD_ID::GAME)
	{
		UWWUIScreenDebugWindow::AddToOptionsBindings(FString("LOCK TIME OF DAY"), &bLockTime);
		UWWUIScreenDebugWindow::AddToOptionsBindings(FString("TIME OF DAY"), &timeOfDayOverride);
	}

	{
		events->game_restart.Add(this, &SIFGameWorld::ResetGraphicsObjects);
		events->cutscene_start.Add(this, &SIFGameWorld::OnCutsceneStart);
		events->cutscene_finish.Add(this, &SIFGameWorld::OnCutsceneEnd);
	}


	m_pPauseHandler = GetGameInstance().GetWorld()->SpawnActor<ARugbyPauseHandler>(ARugbyPauseHandler::StaticClass());
	if (m_pPauseHandler)
	{
		m_pPauseHandler->PrimaryActorTick.TickGroup = TG_PostPhysics;
		GetGameInstance().PreventObjectGC(m_pPauseHandler);
	}
}

///-------------------------------------------------------------------------------
/// Called after all async-loading has finished - SHOULD BE DEPRACTED.
///-------------------------------------------------------------------------------
void SIFGameWorld::InitialisePostLoad()
{
	SIFApplication *app = SIFApplication::GetApplication();

	//#rc3_legacy camera_effects->Initialise(GetSceneDatabaseId(), this);

	//#rc3_legacy jumbotrons = MabMemNew(heap) RUPSSGJumboTrons(this, heap);

	// Load post effects settings.

	//settings_path = GetStadiumManager()->GetPostEffectsFileName().c_str();
	//shadow_settings_path = GetStadiumManager()->GetShadowSettingsFileName().c_str();

	//#rc3_legacy post_effects_manager = MabMemNew(heap) SSPostEffectsManager(heap, this, (SIFLevelPostSettings*)GetViewManager()->GetLevelPostSettings() );	

	// Load the screen wipe.
	LoadScreenWipe();

	// This HUD init is delayed until here as it needs to happen after UI nodes have been attached
	InitialiseHUD();

	// SIFShadowMap::Initialize() uses the render interface and is not safe to run during game world resource loading
	// differed queue code should be able to remove this and allow us to dynamically add/remove actors without traversing the lighting each time
	// Load the lights from the stadium.
	/*#rc3_legacy
	{
		MABMEM_SCOPED_TAG(MEMTAG_GAMEWORLD_7);

		PSSG::PDatabaseWriteLock env_lock(GetSceneDatabaseId());
		PSSG::PDatabase* env_database = env_lock.getDatabase();
		PSSG::PListIterator scenes(env_database->getSceneList());
		PSSG::PRootNode* visual_scene_root = (PSSG::PRootNode*)scenes.data();		// get first scene root for rendering

		GetLightDatabase()->Rebuild(*visual_scene_root);

		// removed as causes both particle system managers to have same objects (crash alert!)
		//particles_manager->FindAndBindDatabaseSystems(env_database);
	}*/

	// Initialise markers that need renderer full initialised
	if (hud3d)
	{
		hud3d->PostInitialisationLoad();
	}

	/*#rc3_legacy
	{
		/// This depends on the ShadowMap and Animation stuff having been created.
		MABMEM_SCOPED_TAG(MEMTAG_CROWD);

		/// Only create a crowd if there
		if( world_id == WORLD_ID::GAME )
		{
			random_number_generator->SetAssertIfUsed(false);

			if(game_settings.game_settings.network_game)
				random_number_generator->LockToThread(MabThread::GetCurrent());

			crowd_manager = MabMemNew(heap) RUCrowdManager( heap, this );
			crowd_manager->Initialise();
#if PLATFORM_WINDOWS
			crowd_manager->SetEnabled( SIFApplication::GetApplication()->GetApplicationParameters().crowd_enabled );
#endif

			if(game_settings.game_settings.network_game)
				random_number_generator->LockToThread(NULL);

			random_number_generator->SetAssertIfUsed(true);

			app->GetEnvironmentViewManager()->SetCrowdManager(crowd_manager);
		}
	}*/

	//Debug code to hide things for aproval screenshots
	if (CVarRemoveForScreenshots.GetValueOnGameThread())
	{
		TArray<AActor*> tempactors;
		UGameplayStatics::GetAllActorsOfClass(GetGameInstance().GetWorld(), AARugbyProceduralMeshActor::StaticClass(), tempactors);
		for (int i = 0; i < tempactors.Num(); i++)
		{
			AARugbyProceduralMeshActor* rpma = Cast<AARugbyProceduralMeshActor>(tempactors[i]);
			if (UOBJ_IS_VALID(rpma) && UOBJ_IS_VALID(rpma->mesh))
			{
				rpma->mesh->bRenderInMainPass = false;
			}
		}
		UGameplayStatics::GetAllActorsOfClass(GetGameInstance().GetWorld(), ARugbyMarkerActor::StaticClass(), tempactors);
		for (int i = 0; i < tempactors.Num(); i++)
		{
			ARugbyMarkerActor* rma = Cast<ARugbyMarkerActor>(tempactors[i]);
			if (UOBJ_IS_VALID(rma))
			{
				UStaticMeshComponent* mesh = Cast<UStaticMeshComponent>(rma->GetComponentByClass(UStaticMeshComponent::StaticClass()));
				if (UOBJ_IS_VALID(mesh))
				{
					mesh->bRenderInMainPass = false;
				}
			}
		}

		if (CVarRemoveForScreenshots.GetValueOnGameThread() == 1)
		{
			if (UOBJ_IS_VALID(ball) && UOBJ_IS_VALID(ball->GetBallMesh()))
			{
				ball->GetBallMesh()->bRenderInMainPass = false;
			}

			//hide players and shadows
			for (ARugbyCharacter* player : all_players)
			{
				for (int i = 0; i < player->ListOfMeshs.Num(); i++)
				{
					player->ListOfMeshs[i]->SetVisibility(false);
					player->ListOfMeshs[i]->bCastDynamicShadow = false;
				}
			}
		}
	}

}

ARugbyCharacter* SIFGameWorld::GetCustomisationPlayer()
{
	ARugbyCharacter* ReturnPlayer = nullptr;

	if (UOBJ_IS_VALID(customisation_player))
	{
		TArray<UActorComponent*> AllComponents = customisation_player->GetComponentsByClass(UMeshComponent::StaticClass());

		for (int32 ComponentIndex = 0; ComponentIndex < AllComponents.Num(); ComponentIndex++)
		{
			UMeshComponent* MeshComponent = Cast<UMeshComponent>(AllComponents[ComponentIndex]);
			if (MeshComponent->IsRegistered())
			{
				MeshComponent->LightingChannels.bChannel0 = true;
				MeshComponent->LightingChannels.bChannel2 = false;
			}
		}

		ReturnPlayer = customisation_player;
	}

	return ReturnPlayer;
}

///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------
void SIFGameWorld::ResetHudIndicators()
{
	//#rc3_legacy
	if (hud3d)
	{
		hud3d->DestroyHudIndicators();
		hud3d->CreateHudIndicators();
	}
}

///-------------------------------------------------------------------------------
/// Initialise the 3D-HUD.
///-------------------------------------------------------------------------------

void SIFGameWorld::Initialise3DHUD( /*MabControlActionManager *control_action_manager*/)
{
	// Create the HUD updaters.
	random_number_generator->SetAssertIfUsed(false);
	hud3d = MabMemNew(heap) RU3DHUDManager(this/*#rc3_legacy, GetSceneDatabaseId(), control_action_manager*/);
	random_number_generator->SetAssertIfUsed(true);

	// TODO: Vaughan once skill level is accessable uncomment this to stop the contextual helper in harder diffs
	// activate contextual helper if training ground or skill level is school boy
	//if ( game_settings.game_settings.game_type == GAME_TRAINING || game_settings.difficulty == DIF_SCHOOLBOY )

	// Enable Contextual help
	if (hud3d)
	{
		hud3d->GetContextualHelper()->SetEnabled(true);
		hud3d->GetContextualHelper()->Activate(true);
	}

	//MabString hud_context = game_settings.game_settings.game_type == GAME_TRAINING ? "RootMenuWindow/TrainingField" : "RootMenuWindow/GameWindow";
	//hud_updater_contextual = MabMemNew(heap) RUHUDUpdaterContextual( hud_context.c_str(), control_action_manager );
}


///-------------------------------------------------------------------------------
/// Initialise the HUD.
///-------------------------------------------------------------------------------

void SIFGameWorld::InitialiseHUD()
{
	if (game_settings.game_settings.game_type == GAME_TRAINING)
	{
		hud_updater = new RUHUDUpdaterTraining(*game_context.get(), "RootMenuWindow/TrainingField");
	}
	else
	{
		hud_updater = new RUHUDUpdater(*game_context.get(), "RootMenuWindow/GameWindow");
	}

	//replay_hud_updater = MabMemNew(heap) RUHUDUpdaterReplay( *game_context, "RootMenuWindow/ReplayWindow" );

	FString hud_context = game_settings.game_settings.game_type == GAME_TRAINING ? "RootMenuWindow/TrainingField" : "RootMenuWindow/GameWindow";
	hud_updater_contextual = new RUHUDUpdaterContextual(hud_context);

	hud_updater_strategy[0] = new RUHUDUpdaterUserStrategy(hud_context, GetTeam(SIDE_A));
	hud_updater_strategy[1] = new RUHUDUpdaterUserStrategy(hud_context, GetTeam(SIDE_B));
}
///-------------------------------------------------------------------------------
/// Initialises all the classes/assets only used in the sandbox game, but not the
/// assets which are changed between tutorials, such as players and the ball
///-------------------------------------------------------------------------------

void SIFGameWorld::InitialiseSandbox(MabControlActionManager *control_action_manager)
{
	// Create a game timer, but reset it immediately so it doesn't run.
	game_timer = std::make_unique<SSGameTimer>(this, game_settings.game_settings.game_length);
	game_timer->Reset();

	// Create a tutorial manager
	tutorial_manager = std::make_unique<RUTutorialManager>(this);
}

///-------------------------------------------------------------------------------
/// Initialises all the classes/assets only used in the sandbox game, but not the
/// assets which are changed between tutorials, such as players and the ball
///-------------------------------------------------------------------------------

void SIFGameWorld::InitialiseMenu()
{
	// Create a game timer, but reset it immediately so it doesn't run.
	game_timer = std::make_unique<SSGameTimer>(this, game_settings.game_settings.game_length);
	game_timer->Reset();
}

///-------------------------------------------------------------------------------
/// Initialises all the additional classes/assets that the sandbox game does not use
///-------------------------------------------------------------------------------

void SIFGameWorld::InitialiseMain()
{
	// Create the game timer
	game_timer = std::make_unique<SSGameTimer>(this, game_settings.game_settings.game_length);
	// Create the statistics tracker for tracking multi-phase/meters gained etc, stats
	stats_tracker = std::make_unique<RUStatsTracker>(this);
}

///-------------------------------------------------------------------------------
/// Initialises the weather based on the game settings and stadium
///-------------------------------------------------------------------------------
void SIFGameWorld::InitialiseWeather()
{
	//#nirupam_hack testing wind indicator
	if (SIFApplication::GetApplication()->GetOnlineMode() == EOnlineMode::Offline)
	{
		game_settings.weather_settings.wind_strength = MabMath::Fabs(random_number_generator->RAND_RANGED_CALL(float, 1.5f) - 0.5f) * 1;
	}
	//#rc3_legacy
	//////////////////////////////////////////////////////////////////////////
	// Setup Weather

	random_number_generator->SetAssertIfUsed(false);

	//// Work out the wind for the selected stadium.
	//// Make it less likely to get maximum wind.
	unsigned short db_id = GetStadiumManager()->GetIDFromAbbreviation(game_settings.stadium_settings.GetStadiumAbbreviation());

	float max_wind_strength = 0.0f;

	if (db_id != DB_INVALID_ID)
	{
		RUStadiumManager* pStadiumManager = GetStadiumManager();

		if (pStadiumManager)
		{
			const RUDB_STADIUM* stadium = pStadiumManager->GetStadiumFromID(db_id);

			if (stadium)
			{
				max_wind_strength = stadium->max_wind_speed;
			}
		}
	}

	max_wind_strength *= 0.667f; // HAX! Reduce wind strength by approx 50%. According to Duncan, adjusting each stadium individually is overkill, needs to be reduced across the board.

	game_settings.weather_settings.wind_strength = MabMath::Fabs(random_number_generator->RAND_RANGED_CALL(float, 1.5f) - 0.5f) * max_wind_strength;

	//// Work out a random wind direction.
	MabMatrix::MatrixMultiply(game_settings.weather_settings.wind_direction, FVector(0, 0, 1), MabMatrix::RotMatrixY(random_number_generator->RAND_RANGED_CALL(float, PI * 2)));

	//// setup the rest of the weather here....

	//random_number_generator->SetAssertIfUsed(true);

	//// Create the weather manager and apply the current settings.
	weather_manager = new RUWeatherManager(*game_context);
	weather_manager->SetWeatherSettings(game_settings.weather_settings);

	//// HACK. The only stadium that is covered, so force rain particles off no matter the weather settings.
	//bool is_forsyth = strcmp(stadium->GetAbbreviation(), "fors") == 0;
	//weather_manager->ForceDisableRainParticles(is_forsyth);
}

static void RegisterStatsScorer(const MabVector< SSHumanPlayer* >& human_players, SIFGameWorld& game_world)
{
	// Register the stats scorer.
	//
	// Get the master controller index.
	SIFPlayerProfile* master_profile = SIFPlayerProfileManager::GetInstance()->GetMasterProfile();
	if (master_profile)
	{
		// Now get the stats scorer.
		if (RUStatsScorer* const stats_scorer = SIFApplication::GetApplication()->GetStatsScorer())
		{
			if (stats_scorer)
			{
				for (MabVector< SSHumanPlayer* >::const_iterator iter = human_players.begin(); iter != human_players.end(); ++iter)
				{
					SSHumanPlayer* const this_player = *iter;

					// If the player is a valid human, and (for now) he's the start-pressing controller....
					if (this_player != NULL && this_player->GetTeam() != NULL && this_player->GetControllerIndex() == master_profile->GetControllerIndex())
					{
						stats_scorer->AttachToGameWorld(this_player->GetControllerIndex(), game_world);
						//SIFApplication::GetApplication()->GetStatisticsHandler()->RegisterScorer(this_player->GetControllerIndex(), stats_scorer);
					}
				}
			}
		}
	}
}

static void UnregisterStatsScorer(const MabVector< SSHumanPlayer* >& human_players)
{
	// Unregister the stats scorer.

	if (RUStatsScorer* const stats_scorer = SIFApplication::GetApplication()->GetStatsScorer())
	{
		if (stats_scorer)
		{
			stats_scorer->RemoveFromGameWorld();
		}
	}

	// Get the master controller index.
	//SIFPlayerProfile* master_profile = SIFPlayerProfileManager::GetInstance()->GetMasterProfile();
	//if (master_profile)
	//{
	//	for (MabVector< SSHumanPlayer* >::const_iterator iter = human_players.begin(); iter != human_players.end(); ++iter)
	//	{
	//		SSHumanPlayer* const this_player = *iter;

	//		// If the player is a valid human, and (for now) he's the start-pressing controller....
	//		if (this_player != NULL && this_player->GetControllerIndex() == master_profile->GetControllerIndex())
	//		{
	//			//SIFApplication::GetApplication()->GetStatisticsHandler()->UnregisterScorer(this_player->GetControllerIndex());
	//		}
	//	}
	//}

}

#if 0 //#rc3_legacy
void SIFGameWorld::NotifyToUpdateController(bool is_suspended)
{
	if (game_settings.game_settings.network_game)
	{
		MabNamedValueList event_parameters;
		event_parameters.SetValue<const char*>(SIFUI_LUA_SYSTEM_EVENT_PARAM, "on_suspend");
		event_parameters.SetValue< int >(SIFUI_NETWORK_GAME_SUSPENDED, is_suspended);
		SIFApplication::GetApplication()->GetWindowSystem()->OnSystemEvent(event_parameters);
	}
}
#endif

///-------------------------------------------------------------------------------
/// Initialises the actors based on the game settings, teams/player numbers
///-------------------------------------------------------------------------------

void SIFGameWorld::InitialiseActors()
{
	random_number_generator->SetAssertIfUsed(false);
	if (game_settings.game_settings.network_game) random_number_generator->LockToThread(MabThread::GetCurrent());

	// Create and init the ball
	InitialiseBall();

	// Create the teams.
	InitialiseTeams();

	// Create the footballer levels.
	InitialiseFootballerMaps();
}

void SIFGameWorld::InitialiseReplayManager()
{
	if (replay_manager == NULL)
	{
		//Initialise replay after run-on anims... (Possibly after the haka if it appears)
		replay_manager = std::make_unique<SSReplayManager>(this);

		if (replay_manager != nullptr)
		{
			replay_manager->Reset();
		}
	}
}

///-------------------------------------------------------------------------------
/// Cleans up any actors created through InitialiseActors()
///-------------------------------------------------------------------------------
void SIFGameWorld::CleanupActors()
{
	cutscene_manager->OnCleanupActors();

	// clear teams and players for the human players
	SET_CHANGEPLAYER_SECTION(this, "CLEANUPACTR");

	for (SSHumanPlayer* human : GetHumanPlayers())
	{
		human->SetTeam(nullptr);
		human->SetRugbyCharacter(nullptr);
	}

	SET_CHANGEPLAYER_SECTION(this, NULL);

	// remove player roles / actions
	for (size_t i = 0; i < all_players.size(); i++)
	{
		if (!UOBJ_IS_VALID(all_players[i]))
		{
			continue;
		}

		all_players[i]->ClearRole();
		all_players[i]->GetActionManager()->AbortAllActions();
	}

	// destroy players
	for (size_t i = 0; i < all_players.size(); ++i)
	{
		if (!UOBJ_IS_VALID(all_players[i]))
		{
			continue;
		}

		all_players[i]->TerminateRugbyCharacteristics();
		//all_players[i]->Destroy();
	}

	all_players.clear();
	players.clear();

	DestroyCharacterMapChunk(SIDE_A);
	DestroyCharacterMapChunk(SIDE_B);
	DestroyCharacterMapChunk(SIDE_OFFICIALS);

	// remove all teams
	while (!teams.empty())
	{
		teams.back().reset();
		teams.pop_back();
	}
	teams.clear();

	// Clear up the memory used by the officials.
	officials.reset(nullptr);

	// Deregister stats scoring for network games.
#if (PLATFORM_PS4)
		// this seems to happen before the async stats request returns
		// and UnregisterAllScorers() comes along later anyway so...
		//UnregisterStatsScorer( human_players );
#else
	UnregisterStatsScorer(human_players);
#endif

	GEngine->ForceGarbageCollection(false);
}


///-------------------------------------------------------------------------------
/// Cleans up any actors created through InitialiseActors()
///-------------------------------------------------------------------------------
void SIFGameWorld::CleanupMostActors()
{
	cutscene_manager->OnCleanupActors();

	// clear teams and players for the human players
	//SET_CHANGEPLAYER_SECTION(this, "CLEANUPACTR");

	//for (SSHumanPlayer* human : GetHumanPlayers())
	//{
	//	human->SetTeam(nullptr);
	//	human->SetRugbyCharacter(nullptr);
	//}

	//SET_CHANGEPLAYER_SECTION(this, NULL);

	// remove player roles / actions
	for (size_t i = 5; i < all_players.size(); i++)
	{
		all_players[i]->ClearRole();
		all_players[i]->GetActionManager()->AbortAllActions();
	}

	// destroy players
	for (size_t i = 5; i < all_players.size(); ++i)
	{
		all_players[i]->TerminateRugbyCharacteristics();
		//all_players[i]->Destroy();
	}

	//all_players.clear();
	//players.clear();

	//DestroyCharacterMapChunk(SIDE_A);
	//DestroyCharacterMapChunk(SIDE_B);
	//DestroyCharacterMapChunk(SIDE_OFFICIALS);

	// remove all teams
	//while (!teams.empty())
	//{
	//	teams.back().reset();
	//	teams.pop_back();
	//}
	//teams.clear();

	// Clear up the memory used by the officials.
	officials.reset(nullptr);

	GEngine->ForceGarbageCollection(false);
}

///-------------------------------------------------------------------------------
/// FullGameRestart() - call to restart the game (FULLY, from Pause menu)
/// TODO:: MUST BE CALLED FROM SyncUpdate!
///-------------------------------------------------------------------------------

void SIFGameWorld::FullGameRestart()
{
	MABMEM_SCOPED_TAG(MEMTAG_FULLGAME_RESTART);

	MABLOGDEBUG("FullGameRestart:");
	/// Clear all pending loads + allow current to finish.
	//#rc3_legacy SIFApplication::GetApplication()->GetAsyncLoadThread()->Abort();

	//#rc3_legacy SIFApplication::GetApplication()->SetSuppressRenderingNoThreadSwitch(true);
	reenabled_rendering = false;

	SSGameTimer *timer = GetGameTimer();

	//NetworkHardReset();

	GetCutSceneManager()->Reset();			// Has to be called explicitly (was part of RestartGame - removed due to tutorial issues).
	RestartGame();
	if (timer)
	{
		timer->ResetTotalTime();
		timer->SetExtraTimeMode(NOT_EXTRA_TIME);
		timer->SetHalfLength(GetGameSettings().game_settings.game_length, NOT_EXTRA_TIME);
	}
	GetCutSceneManager()->SetupPreKickOffCutscene();		// Has to be called for restart (game entry has different cutscene flow).
}

///-------------------------------------------------------------------------------
/// RestartGame.
///		Initialize all game state. (Apart from cutscenes).
///-------------------------------------------------------------------------------

void SIFGameWorld::RestartGame()
{

	MABLOGDEBUG("SIFGameWorld::RestartGame worldId=%d", this->GetWorldId());
#ifdef ENABLE_GAME_DEBUG_MENU
	SIFDebug::GetGameDebugSettings()->ClearDebugStrings();
#endif

	RUGameSettings::RU_GAME_SETTINGS& settings = game_settings.game_settings;

	if (events)
	{
		events->game_reset();
	}

	//Reset time sources.
	if (simulation_time)
	{
		simulation_time->SetStepParameters(SIMULATION_RATE);
		simulation_time->SetTargetTimeScale(1.0f);
		simulation_time->SetTimeScale(1.0f);
		simulation_time->SetCurrentStep(MabTimeStep(0, SIMULATION_INTERVAL, 0));
		//UGameplayStatics::SetGlobalTimeDilation(this->GetGameInstance().GetWorld(), 1.0f);
	}

	// Sandbox games should not worry about setting if the session handler
	// should display disconnect popups because this will be handled by the
	// multiplayer_training_field.
	if (IsMatch())
	{
		//Enable network popups if network game
		MABLOGDEBUG("SIFGameWorld::RestartGame - %i", static_cast<int>(settings.network_game));
		//#rc3_legacy SIFMatchmakingHelpers::SetSessionHandlerDisconnectionPopups(settings.network_game);
	}

	if (world_audio)
	{
		world_audio->Reset();
	}

	if (game_timer != 0)
		game_timer->ResetTotalTime();

	if (GetInputManager())
	{
		GetInputManager()->Reset();
	}

	if (game_state)
	{
		game_state->Reset();
	}

	if (network_state && game_settings.game_settings.network_game)
	{
		network_state->Reset();
	}

	ResetContextBucket();

	if (ball)
	{
		ball->Reset();
	}

	random_number_generator->SetAssertIfUsed(false);

	if (strategy_helper)
	{
		strategy_helper->ClearRoles();
		strategy_helper->Reset();
	}
	random_number_generator->SetAssertIfUsed(true);

	MABASSERTMSG(teams.size() == 2, "There are too many teams in this list! Did we run InitialiseTeams() too many times? Stop pushing back teams!");

	// Set the sides for the teams involved.
	for (unsigned int i = 0; i < teams.size(); ++i)
	{
		auto& team = teams[i];
		team->Reset();
		team->SetSide(i);
		int dir = 0;	// there are probably only 2 directions, but may be four teams for some reason, :jb
		if (i > 0)
		{
			dir = 1;
		}
		team->SetPlayDirection(settings.initial_play_direction[dir]);
	}

	// Load the Lineout rater for the team
	if (teams.size() > 1)
	{
		lineout_rater->Init(teams[0].get(), teams[1].get());
	}

	// Set the side for the officials.
	if (officials != NULL)
	{
		officials->SetSide(SIDE_OFFICIALS);
	}

	// Set the attacking team.
	if (game_state)
	{
		game_state->SetAttackingTeam(teams[settings.initial_kickoff_team].get());
	}

	// Reset substitution manager.
	substitution_manager->Reset();

	// Reset the teams

	const int RESTART_RELOAD_DELAY = 8;

	restart_reload_players = 0;
	for (auto& team : teams)
	{
		team->Reset();
		if (team->ResetPlayersToStartingLineup() && IsMatch())
		{
			restart_reload_players = RESTART_RELOAD_DELAY;
		}
	}

	// Reset the officials.
	if (officials != NULL)
	{
		officials->Reset();
	}


	// Reset game get to ball cache
	GetGameGTB()->Reset();

	RebuildPlayerList();

	random_number_generator->SetAssertIfUsed(false);
	ResetActors(true);

	tackle_helper->Reset();

	// Don't do this in training mode. Appears to lock up flow. TODO: Find out why?
	SET_CHANGEPLAYER_SECTION(this, "GAMERESTRT");
	if (IsMatch())
	{
		// Assign the human players to RL players
		for (SSHumanPlayer* human : GetHumanPlayers())
		{
			if (human->IsPlaying())
			{
				human->Reset();
				human->SetBufferedInput(SSPackedInput());
				human->AssignBestPlayer(true);
			}
		}
	}
	SET_CHANGEPLAYER_SECTION(this, NULL);

	if (!IsMatch())
	{
#ifdef ENABLE_RULES_IN_TRAINING
		rules->EnableTriggers(RURT_MAINGAMERULES);
#else
		rules->EnableTriggers(RURT_GLOBALRESET);
#endif
		if (game_state)
		{
			game_state->SetPhase(RUGamePhase::PLAY);
		}

		//		SIFGameObject* ball_holder = teams[0]->GetPlayer(0);
		//		game_state->SetBallHolder(ball_holder, true);
	}
	else
	{
		// enable correct triggers for main game ( global reset should always be off cause it is only for training field )
		rules->EnableTriggers(settings.enabled_triggers & ~RURT_GLOBALRESET);

		if (game_state)
		{
			game_state->SetPhase(RUGamePhase::PRE_GAME);
		}
	}

	//Run a few updates to make sure camera & animation system is in a good position before game starts
	//MabPauseableRealTimeSource *ts_app = SIFApplication::GetApplication()->GetAppTime()->GetAppTimeSource();
	//MabLockStepTimeSource *ts_sim = SIFApplication::GetApplication()->GetAppTime()->GetSimulationTimeSource();

	//ts_app->ResetStepIterator();
	////ts_app->NextDeltaTimeStep();
	//MabTimeStep time_step( ts_sim->GetAbsoluteTime(), ts_sim->GetDeltaTime(), ts_sim->GetAbsoluteStepCount() );

	//ts_app->ResetStepIterator();


	for (auto& team : teams)
	{
		if (team->GetFormationManager() != NULL)
		{
			team->GetFormationManager()->Reset();
			wwNETWORK_TRACE_JG_DISABLED("SIFGameWorld RestartGame");
			team->GetFormationManager()->UpdateLogic(1.0f);
			team->GetFormationManager()->MovePlayersToKickOffPositions();
		}
	}

	random_number_generator->SetAssertIfUsed(true);

	if (stats_tracker != 0)
	{
		stats_tracker->Reset();
	}

	// Reset all our pro goal progress
	//if(SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro())
		//SIFApplication::GetApplication()->GetCareerModeManager()->GetProGoalManager()->ResetAllProgress();

	// Reset all our match stats
	SIFApplication::GetApplication()->GetStatisticsSystem()->ResetCurrentMatchStats();

	SetOfficialsKickOffPositions();

	rules->Reset();
#ifndef CHARACTER_CREATOR_BUILD
	// Reset hud updater
	if (hud_updater)				hud_updater->RestartGame();
	if (hud_updater_strategy[0])	hud_updater_strategy[0]->RestartGame();
	if (hud_updater_strategy[1])	hud_updater_strategy[1]->RestartGame();
#endif
	events->game_restart();
	events->match_restart();

	if (emotion_engine_manager && emotion_engine_manager->GetCrowdReactionManager())
	{
		emotion_engine_manager->GetCrowdReactionManager()->RestartGame();
	}


#ifndef DISABLE_COMMENTARY
	if (SIFApplication::GetApplication()->GetCommentarySystem())
		SIFApplication::GetApplication()->GetCommentarySystem()->Reset();
#endif

	if (replay_manager != NULL)
	{
		replay_manager->Reset();
	}

#ifndef CHARACTER_CREATOR_BUILD
	Get3DHudManager()->SetEnabled(true);
	//#rc3_legacy
	/*if (settings.game_type != GAME_TRAINING)
		GetHUDUpdater()->SetPlaceholderTextVisible( false );*/

		//#rc3_legacy Get3DHudManager()->GetContextualHelper()->Activate( true );
		//#rc3_legacy hud_updater_contextual->Reset();
#endif
#ifdef ENABLE_LIVELINK
	EnableLiveLink(false);
#endif

	SetOfficialsRoles();

	GetTeam(0)->GetFormationManager()->UpdatePassPriorities(PASS_PRI_ALL, true);
	GetTeam(1)->GetFormationManager()->UpdatePassPriorities(PASS_PRI_ALL, true);

	if (settings.network_game)
	{
		RUTackleHelper::ClearMasterTackleDeterminationIndex();
		spatial_helper->Reset();
		team_confidence_handler->Reset();
		//network_manager->
		//camera_manager->
		//camera->Reset();
		//hud3d->HardReset();
		//active_camera->Reset();
		//cutscene_manager->Reset();
		//post_effects_manager->Reset();
		//stadium_manager->
		//weather_manager->
		//screen_wipe_manager->Reset();
		//rumble_manager->
		//crowd_manager->HardReset();
		//jumbotrons->Reset();
		//camera_effects->Reset()
		//node_clean_up.clear();
		//texture_swaps.clear();
	}

	if (IsMatch())
	{
		SetupPlayerTickPaused();
	}
	else
	{
		ResetPlayerTickPaused();
	}
}

///-------------------------------------------------------------------------------
/// ResetActors - reset the players.
///-------------------------------------------------------------------------------

//#define RESET_COMPONENT(player, component_t) \
//	{\
//		component_t* _comp = player->GetComponent<component_t>();\
//		if(_comp != NULL) _comp->Reset();\
//	}

//#define RESET_COMPONENT2(player, component_t, is_restart) \
//	{\
//	component_t* _comp = player->GetComponent<component_t>();\
//	if(_comp != NULL) _comp->Reset(is_restart);\
//}


void SIFGameWorld::ResetActors(bool is_restart)
{
	// Reset all player roles and animations
	for (ARugbyCharacter* player : players)
	{
		MabTypeID prev_role = player->GetRole() ? player->GetRole()->RTTGetType() : MABTYPE_UNKNOWN;
		player->ClearRole();

		// Make sure to set officials back to previous roles.
		if (player->GetAttributes()->GetTeam() == GetOfficialsTeam() && prev_role != SSRoleNull::RTTGetStaticType())
			player->SetRole(role_factory->Instance(prev_role));

		player->GetActionManager()->AbortAllActions();
		player->GetActionManager()->EnableAllActions(true);

		// Reset all of the components
		if (player->GetAttributes()) player->GetAttributes()->Reset();
		if (player->GetMovement()) player->GetMovement()->Reset();
		if (player->GetLookAt()) player->GetLookAt()->Reset();
		if (player->GetState()) player->GetState()->Reset(is_restart);
		if (player->GetPosAss()) player->GetPosAss()->Reset();
		if (player->GetAnimation()) player->GetAnimation()->Reset();

		/*#rc3_legacy
		RESET_COMPONENT(player, RUPlayerAttributes);
		RESET_COMPONENT(player, RUPlayerMovement);
		//RESET_COMPONENT(player, RUActionManager);
		//RESET_COMPONENT(player, RUPlayerParticleSystems);
		//RESET_COMPONENT(player, RUPlayerSound);
		RESET_COMPONENT(player, RUPlayerLookAt);
		RESET_COMPONENT2(player, RUPlayerState, is_restart);
		RESET_COMPONENT(player, RUZonePositionAssigned);
		//RESET_COMPONENT(player, URugbyCharacterBlendController);
		//RESET_COMPONENT(player, NMMabSkeleton);
		//RESET_COMPONENT(player, NMMabCharacterController);
		//#rc3_legacy RESET_COMPONENT(player, NMMabAnimationNetwork);
		//RESET_COMPONENT(player, NMMabAnimationEvents);
		RESET_COMPONENT(player, RUPlayerAnimation);*/
	}
}

//#undef RESET_COMPONENT
//#undef RESET_COMPONENT2

///-------------------------------------------------------------------------------
/// Update - Main game 'tick'
///-------------------------------------------------------------------------------

//static int step_count = 0;
// #rc4_note real_time_step not used to update the simulation
void SIFGameWorld::PreSyncUpdate(const MabTimeStep& real_time_step)
{
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
	FString gameWorldDebugText = FString("SIFGameWorld: ");
	if (CVarGameWorldInfo.GetValueOnAnyThread() > 0 || FParse::Param(FCommandLine::Get(), TEXT("GameWorldInfo")))
	{
		switch (world_id)
		{
			case WORLD_ID::GAME:
			gameWorldDebugText += TEXT("WORLD_ID::GAME\n");
			break;
			case WORLD_ID::SANDBOX:
			gameWorldDebugText += TEXT("WORLD_ID::SANDBOX\n");
			break;
			case WORLD_ID::MENU:
			gameWorldDebugText += TEXT("WORLD_ID::MENU\n");
			break;
			default:
			gameWorldDebugText += TEXT("UNKNOWN WORLD!!!!!\n");
			break;
		}

		gameWorldDebugText += IsAwake() ? TEXT("	Awake\n") : TEXT("	Asleep\n");
		gameWorldDebugText += IsVisible() ? TEXT("	Visible\n") : TEXT("	Invisible\n");
		gameWorldDebugText += world_is_allocated ? TEXT("	Allocated\n") : TEXT("	Unallocated\n");
	}
#endif

	if (CVarPrintTickInfo.GetValueOnAnyThread()) UE_LOG(LogTemp, Display, TEXT("SIFGameWorld::PreSyncUpdate"));

	if (!IsAwake())
	{
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
		if (CVarGameWorldInfo.GetValueOnAnyThread() > 0 || FParse::Param(FCommandLine::Get(), TEXT("GameWorldInfo")))
		{
			UE_LOG(LogTemp, Log, TEXT("%s"), *gameWorldDebugText);
			GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::White, gameWorldDebugText);
		}
#endif
		return;
	}

	const MabReal real_delta = real_time_step.delta_time.ToSeconds();

	if (m_allowVoipDelayed)
	{
		m_allowVoipTimer += real_delta;
		if (m_allowVoipTimer > 1.0f)
		{
			if (SIFApplication::GetApplication()->GetVoipManager())
			{
				SIFApplication::GetApplication()->GetVoipManager()->UnmuteAllTalkers();
			}
			m_allowVoipDelayed = false;
			m_allowVoipTimer = 0.0f;
		}
	}

	//#rc3_legacy
	//if ( network_manager )
	//{
	//	RU_NETWORK_REPLAY_CHECK( GetSimTime()->GetAbsoluteStepCount(), this );
	//	RU_NETWORK_REPLAY_READ( GetSimTime()->GetAbsoluteStepCount(), network_manager );
	//}

	//#rc3_legacy GetViewManager()->Update( real_delta );
	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_Update_rumble_manager_Update)
			rumble_manager->Update();
	}

	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_Update_world_audio_Update)
			world_audio->Update();
	}

	// if training level update the tutorial manager
	if (game_settings.game_settings.game_type == GAME_TRAINING)
	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_Update_tutorial_manager_Update)
			// update the sandbox specific classes
			tutorial_manager->Update(real_delta);
	}

	//Update simulation for network games
	if (game_settings.game_settings.network_game)
	{
		//Hack to pause time on first load ABROWN
		if (!simulation_time->IsPaused())
		{
			simulation_time->Pause(true);
		}

		// static int32 FrameCounterJG = 0;

		//UE_LOG(LogTemp, Display, TEXT("SIFGameWorld::PreSyncUpdate DEBUG JOSEPH Updating simulation for %d, %d"), simulation_time->GetAbsoluteStepCount(), FrameCounterJG);
		//FrameCounterJG++;

		//for (size_t i = 0; i < players.size(); i++)
		//{
		//	FVector PlayerPosition = players[i]->GetMovement()->GetCurrentPosition();
		//	FVector BonePosition = players[i]->GetBoneWorldPosition("ball");

		//	UE_LOG(LogTemp, Display, TEXT("ID: %s, %u, X: %.20f, Y: %.20f, Z: %.20f"), *players[i]->GetFullName(), players[i]->GetAttributes()->GetDbId(), PlayerPosition.X, PlayerPosition.Y, PlayerPosition.Z);
		//	UE_LOG(LogTemp, Display, TEXT("ID: %s, %u, Bone position X: %.20f, Y: %.20f, Z: %.20f"), *players[i]->GetFullName(), players[i]->GetAttributes()->GetDbId(), BonePosition.X, BonePosition.Y, BonePosition.Z);
		//}

		// Check if should update this frame
		should_update_sim = true;
		if (network_state)
		{
			should_update_sim = false;

			ARugbyGameState* rugbyGameState = Cast<ARugbyGameState>(SIFApplication::GetApplication()->GetWorld()->GetGameState());
			if (rugbyGameState)
			{
				if (rugbyGameState->IsGameWorldAllReady())
				{
					should_update_sim = network_state->CheckSync(simulation_time->GetAbsoluteStepCount());
					network_state->SendSync(simulation_time->GetAbsoluteStepCount(), real_delta);
					network_state->Update();

					// If we are running in single threaded mode, we need to flush any packets we just queued up so they are sent off.
					SIFApplication::GetApplication()->ConditionallyFlushNetworkManager();
				}
			}
		}

		// Continue to update the simulation during post match screens after other players leave, otherwise cameras will freeze.
		bDoMatchOverSim = false;
		if (game_timer && game_timer->IsMatchOver() && !game_timer->IsRunning())
		{
			// Need to also check this otherwise the game can desync during the post match cutscenes. Will result in a minor hitch when the first player disconnects.
			if (SIFApplication::GetApplication()->GetConnectionManager()->GetCurrentState() == ERugbyNetworkState::Playing_PostMatch)
			{
				bDoMatchOverSim = true;
			}
		}

		//Run simulation update
		if (should_update_sim || bDoMatchOverSim)
		{
			wwNETWORK_TRACE_JG("SIFGameWorld::PreSyncUpdate Updating simulation for %d", simulation_time->GetAbsoluteStepCount());
			UE_LOG(LogDesync, Display, TEXT("SIFGameWorld::PreSyncUpdate Updating simulation for %d"), simulation_time->GetAbsoluteStepCount());
			// Step time source.
			simulation_time->Step();
			MabTimeStep sim_step(simulation_time->GetAbsoluteTime(),
				simulation_time->GetDeltaTime(),
				simulation_time->GetAbsoluteStepCount());

			if (sim_step.step_count == LastFrameStep.step_count)
			{
				UE_LOG(LogDesync, Display, TEXT("SIFGameWorld::PreSyncUpdate Duplicate Step"));
			}

			random_number_generator->LockToThread(MabThread::GetCurrent());
			PreSyncUpdateSimulation(sim_step);
			LastFrameStep = sim_step;
			random_number_generator->LockToThread(0);

		}
		else
		{
			for (size_t i = 0; i < all_players.size(); i++)
			{
				ARugbyCharacter* current_player = all_players[i];

				if (UOBJ_IS_VALID(current_player))
				{
					// Grab all the skinned mesh components, and loop through them, casting to their type.
					TArray<UActorComponent*> primitive_components = current_player->GetComponentsByClass(USkinnedMeshComponent::StaticClass());
					for (UActorComponent* primitive_actor_components : primitive_components)
					{
						USkinnedMeshComponent* current_mesh = CastChecked<USkinnedMeshComponent>(primitive_actor_components);

						if (current_mesh)
						{
							if (UOBJ_IS_VALID(current_mesh))
							{
								current_mesh->SetShouldForceUpdateSkip(true);
							}
						}
					}
				}
			}
		}
	}
	//Update simulation for regular game
	else
	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_Update_simulation_time_PreSyncUpdateSimulation);

		bSimulationUpdateThisFrame = false;
		
		while (simulation_time->NextDeltaTimeStep())
		{
			// We need to be assured that we have completed a full simulation step (pre AND post)
			// before starting a new one. This is only true where there are multiple steps for the 
			// current time delta.
			if (bSimulationUpdateThisFrame)
			{
				PostSyncUpdateSimulation(LastFrameStep);
			}

			LastFrameStep = simulation_time->GetDeltaTimeStep();
			PreSyncUpdateSimulation(LastFrameStep);
			bSimulationUpdateThisFrame = true;
		}
	}

#if ENABLE_NETWORK_FRAME_INFO
	if (game_settings.game_settings.network_game || SIFApplication::GetApplication()->GetOnlineMode() != EOnlineMode::Offline)
	{
		ShowDebugNetworkInfo();
	}
#endif

	// If we didn't flush the outbound packets after sync, do it now.
	SIFApplication::GetApplication()->ConditionallyFlushNetworkManager();
}


void SIFGameWorld::PostSyncUpdate(const MabTimeStep& real_time_step)
{
	if (CVarPrintTickInfo.GetValueOnAnyThread()) UE_LOG(LogTemp, Display, TEXT("SIFGameWorld::PostSyncUpdate"));

	if (!IsAwake())
	{
		return;
	}

	if (network_state && game_settings.game_settings.network_game)
	{
		//Run simulation update
		if (should_update_sim || bDoMatchOverSim)
		{
			wwNETWORK_TRACE_JG("SIFGameWorld::PostSyncUpdateUpdating simulation for %d", simulation_time->GetAbsoluteStepCount());

			MabTimeStep sim_step(simulation_time->GetAbsoluteTime(),
				simulation_time->GetDeltaTime(),
				simulation_time->GetAbsoluteStepCount());

			random_number_generator->LockToThread(MabThread::GetCurrent());
			PostSyncUpdateSimulation(sim_step);
			random_number_generator->LockToThread(0);
		}
	}
	else
	{
		if (bSimulationUpdateThisFrame)
		{
			PostSyncUpdateSimulation(LastFrameStep);
		}
		else if (replay_manager)
		{
			QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Replay_Manager_Update);
			replay_manager->UpdateReplay(0.0f);
		}
	}

	// Update the effect system
	//#rc3_legacy effect_system->ProcessEffectInstances( simulation_time->GetDeltaTime().ToSeconds(), real_delta );


	//Debug menu settings
	//commenting this as it doesnot work. This is just a debugger for time of day
	/*if (world_id == WORLD_ID::GAME)
	{
		if (UWWUIScreenDebugWindow::IsAnimationDebugScreenOpen() && sky_manager)
		{
			if (!bLockTime && FMath::Abs(sky_manager->GetCurrentTOD() - timeOfDayOverride) < 0.05)
			{
				timeOfDayOverride = sky_manager->GetCurrentTOD();
			}
			sky_manager->PauseTime = bLockTime;
			sky_manager->CurrentTime = timeOfDayOverride;
			sky_manager->EditorSetTimeOfDay();
		}
	}*/


#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
	FString gameWorldDebugText = FString("SIFGameWorld: ");
	// Debugging our current session
	if (CVarGameWorldInfo.GetValueOnAnyThread() > 0 || FParse::Param(FCommandLine::Get(), TEXT("GameWorldInfo")))
	{
		gameWorldDebugText += FString::Printf(TEXT("Players: %i\n"), all_players.size());
		for (unsigned int i = 0; i < all_players.size(); i++)
		{
			gameWorldDebugText += FString::Printf(TEXT("	Player (%i): %s.....int: %i\n"), i, *all_players[i]->GetName(), all_players[i]->m_testInt);
		}

		/*
		// Debugging all the rugby characters in the UWorld.
		int count = 0;
		TActorIterator<ARugbyCharacter> ActorItr(GetGameInstance().GetWorld());
		for (ActorItr; ActorItr; ++ActorItr)
		{
			ARugbyCharacter *rugbyPlayer = *ActorItr;
			gameWorldDebugText += FString::Printf(TEXT("	Player (%i): (%s)%s.....int: %i\n"), count, *rugbyPlayer->GetPathName() / **rugbyPlayer->GetName()* /, *rugbyPlayer->repName, rugbyPlayer->m_testInt);
			count++;
		}
		gameWorldDebugText += FString::Printf(TEXT("Players: %i"), count);*/

		GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::White, gameWorldDebugText);
	}
#endif

	if (world_id == WORLD_ID::MENU || world_id == WORLD_ID::SANDBOX)
	{
		if (m_isPlayGo && GetGameInstance().IsValidLowLevel())
		{
			GetGameInstance().UpdateChunkInstall();
			m_isPlayGo = GetGameInstance().IsPlayingChunkInstall();
		}
	}
}

///-------------------------------------------------------------------------------
/// UpdateSimulation - Update game simulation (15Hz)
///-------------------------------------------------------------------------------

//const int WINDOW = 120;
//static int head = 0;
//static int tail = 0;
//static int sample_count = 0;
//static float samples[WINDOW];
//static volatile float SCALE = 1000.0f;

void SIFGameWorld::PreSyncUpdateSimulation(const MabTimeStep& time_step)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation);

	if (CVarPrintTickInfo.GetValueOnAnyThread()) UE_LOG(LogTemp, Display, TEXT("SIFGameWorld::PreSyncUpdateSimulation"));

	bTickedPreSyncSim = true;

	bool bSkipUniqueConnectionsCheck = false;
#ifdef WITH_EDITOR
	if (FParse::Param(FCommandLine::Get(), TEXT("AllowSinglePlayer")))
	{
		bSkipUniqueConnectionsCheck = true;
	}
#endif

#if defined ONLINE_ALLOWSINGLEPLAYER
	bSkipUniqueConnectionsCheck = true; //AllowSinglePlayer
#endif

	if (SIFApplication::GetApplication()->GetWorld()
		&& SIFApplication::GetApplication()->GetWorld()->GetNetMode() != ENetMode::NM_Client
		&& SIFApplication::GetApplication()->GetOnlineMode() == EOnlineMode::Online
		&& SIFApplication::GetApplication()->GetRemotePlayerCount() == 0
		&& SIFApplication::GetApplication()->GetConnectionManager()->GetCurrentState() == ERugbyNetworkState::Playing
		&& !bSkipUniqueConnectionsCheck)
	{
		return;
	}

	UWorld* YourGameWorld = SIFApplication::GetApplication()->GetWorld(); // Set this somehow from another UObject or pass it in as an argument or parameter

	//MABASSERT(random_number_generator->GetAssertIfUsed() || !game_settings.game_settings.network_game);
	random_number_generator->SetAssertIfUsed(false);

	//Bail because the world is dead
	if (!UOBJ_IS_VALID(YourGameWorld))
	{
		return;
	}

	const float delta_time = time_step.delta_time.ToSeconds();
	float unscaled_delta_time = 0.0f;

	if (simulation_time->GetTimeScale() != 0.0f)
	{
		unscaled_delta_time = delta_time / simulation_time->GetTimeScale();
	}

	if (network_state && game_settings.game_settings.network_game)
	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Networking_Update);
		network_state->UpdateSimulation(time_step);

	}

	//UE_LOG(LogTemp, Warning, TEXT("Setting Params for frame %u"), GFrameCounter);

	if (world_id == WORLD_ID::GAME)
	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_SetAnimParams);

		UpdateCharacterExternalRateOptimization(delta_time, time_step.step_count);
	}

#if !WW_BUILD_SUBMISSION
	{
		if (world_id == WORLD_ID::GAME && game_settings.game_settings.network_game)
		{
			QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_NetworkLogging);

			LogAndCheckNetworkState(delta_time, time_step.step_count);
		}
	}
#endif

	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Locomation_Calculate);
		// Optional possible optimization for CalculateLocomotionWeight being ran more than once per frame.
		for (size_t i = 0; i < players.size(); i++)
		{
			if (players[i]->GetAnimInstance())
			{
				players[i]->GetAnimInstance()->CalculateLocomotionWeight();
			}
		}
	}

	/// Process cutscenes + screen wipe, but with a max delta to stop big jumps.

	//static const float MAX_DELTA_TIME = SIMULATION_INTERVAL*2.0f;
	//if(unscaled_delta_time<=MAX_DELTA_TIME) ////#nirupam: dont enable. This will mess up timer in cutscenes
	{

		if (cutscene_manager)
		{
			QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_cutscene_manager_Update)
				cutscene_manager->Update(delta_time);
		}

		//	if (SIFApplication::GetApplication()->GetEVDS())
		//	{
		//		SIFApplication::GetApplication()->GetEVDS()->Update( time_step, true );
		//	}
		if (GetScreenWipeManager())
		{
			QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_GetScreenWipeManager_UpdateSimulation)
				GetScreenWipeManager()->UpdateSimulation(unscaled_delta_time);
		}
	}
	//else
	//{
	////	MabTimeStep step(0, MAX_DELTA_TIME, 0);
	//	if (cutscene_manager)
	//	{
	//		cutscene_manager->Update( MAX_DELTA_TIME);
	//	}
	////	if (SIFApplication::GetApplication()->GetEVDS())
	////	{
	////		SIFApplication::GetApplication()->GetEVDS()->Update( step, true );
	////	}
	////	if (screen_wipe_manager)
	////	{
	////		screen_wipe_manager->UpdateSimulation(MAX_DELTA_TIME);
	////	}
	//	}

	//#rc3_legacy Moved to after game update has been done to prevent ball from being recorded in the wrong location.
// 	if (replay_manager)
// 	{
// 		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Replay_Manager_Update);
// 		replay_manager->UpdateReplay(delta_time);
// 	}
	//#rc3_legacy
	//if (SIFApplication::GetApplication()->GetSandBoxAsyncTickEnabled())
	{
		if (input_manager)
		{
			QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_input_manager_UpdateSimulation);
			input_manager->UpdateSimulation(time_step);
		}

		if (substitution_manager)
		{
			QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_substitution_manager_UpdateSimulation);
			substitution_manager->UpdateSimulation();
		}
	}

	//#rc3_legacy
	if (emotion_engine_manager &&
		game_settings.game_settings.game_type != GAME_TRAINING &&
		game_settings.game_settings.game_type != GAME_MENU)
	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_emotion_engine_manager_Update)
			//#rc3_legacy MabProfilerMarker marker( "Emotion Engine" );
			//#rc3_legacy MAB_PROFILE_SECTION_START(profile3, "Emotion Engine");
			emotion_engine_manager->Update(time_step);
		//#rc3_legacy MAB_PROFILE_SECTION_END(profile3);
	}

	//#rc3_legacy
	disable_simulation = false;
	if (cutscene_manager)
	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_CheckCutscene);
		simulation_time->bCutSceneInProgress = disable_simulation = cutscene_manager->IsCinematicRunning();// IsSimulationDisabled();	
	}

#ifdef wwDEBUG_TG
#ifdef DISABLE_WORLD_FOR_CLIENT
	if (GetGameInstance().GetWorld()->GetNetMode() != ENetMode::NM_Client)
#endif
#endif
	{
		bool dont_update = false;
		//
		//if(disable_simulation) 
		//{	/// Cutscene editing, only update if 'play' or dragging the time bar.
		//	if(cutscene_manager->IsEditing() && cutscene_manager->GetMorphemeAnimationInterval()<=0.0f) //we dont have live update
		//		dont_update = true;
		//}
		//

		dont_update = SIFApplication::GetApplication()->GetActiveGameWorld() != this;

		if (!dont_update)
		{
			{
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_game_gtb_Update);
				game_gtb->Update(time_step);
			}

			{
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Update_Team_Logic);
				//#rc3_legacy MabProfilerMarker marker( "AI Update");
				for (size_t i = 0; i < teams.size(); i++)
				{
					wwNETWORK_TRACE_JG("SIFGameWorld STAT_Rugby_SIFGameWorld_UpdateSimulation_Update_Team_Logic");
					teams[i]->UpdateLogic(delta_time);
				}
			}

			{
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Actions_Update);
				for (size_t action_index = 0; action_index < ACTION_LAST; action_index++)
				{
					for (size_t i = 0; i < players.size(); i++)
					{
						RUAction* action = players[i]->GetActionManager()->GetAction((RU_ACTION_INDEX)action_index);

						if (action && action->IsRunning())
						{
							action->Update(time_step);
						}
					}
				}
			}

			{
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Update_Players);

				IConsoleVariable* RemoveForScreenshots = IConsoleManager::Get().FindConsoleVariable(TEXT("ww.RemoveForScreenshots"));
				bool debugRemovePlayers = false;
				if (RemoveForScreenshots)
				{
					debugRemovePlayers = RemoveForScreenshots->GetInt() == 1;
				}

				if (GetWorldId() != WORLD_ID::SANDBOX || SIFApplication::GetApplication()->GetMasterPlayerController())
				{
					wwNETWORK_TRACE_JG("SIFGameWorld STAT_Rugby_SIFGameWorld_UpdateSimulation_Update_Players");
					for (size_t i = 0; i < players.size(); i++)
					{
						players[i]->GetActionManager()->InvalidateCache();

						if (!disable_simulation)
						{
							if (players[i]->GetRole())
							{
								players[i]->GetRole()->UpdateLogic(time_step);
							}
						}
						else
						{
							//this logic calls role UpdateCutScene if a player is not in cutscene, and if cutscene has not already hidden all actors intentionally.
							if (players[i]->GetRole() && !players[i]->GetState()->IsInCutScene() && (!(GetCutSceneManager()->IsCinematicRunning() && GetCutSceneManager()->IsHideNonActors())))
								players[i]->GetRole()->UpdateCutScene(time_step);
						}

						if (debugRemovePlayers)
						{
							players[i]->SetVisible(false);
						}
					}
				}
			}

			//if(IsMatch()){
			//	SetupPlayerTickPaused();
			//}

			{
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_movement_Update);
				movement->Update(mGameInstance.GetWorld());
			}

			{
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_UpdateLogicPostPlayerUpdate);
				for (size_t i = 0; i < teams.size(); i++)
				{
					teams[i]->UpdateLogicPostPlayerUpdate();
				}
			}

			{
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_UpdateLogicPostPlayerUpdate2);
				for (size_t i = 0; i < teams.size(); i++)
				{
					teams[i]->UpdateLogicPostPlayerUpdate2();
				}
			}

			if (game_state)
			{
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_game_state_UpdateSimulation);
				game_state->UpdateSimulation(time_step);
			}

			if (rules)
			{
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_rules_Update);
				rules->Update(time_step);
			}

			if (!disable_simulation)
			{
				if (strategy_helper)
				{
					QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_strategy_helper_Update);
					strategy_helper->Update(delta_time);
				}

				if (team_confidence_handler)
				{
					QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_team_confidence_handler_Update);
					team_confidence_handler->Update(delta_time);
				}
			}

			if (role_factory)
			{
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_role_factory_PurgeReleasedRoles);
				role_factory->PurgeReleasedRoles();
			}

			// Updates all the components, this includes motion etc
			{
				//#rc3_legacy MabProfilerMarker marker( "Object Database" );

				{
					QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Update_All_Players)
						//-----------------------------------------------
						// Update - only active player components.
						for (ARugbyCharacter* player : all_players)
						{
							if (player->GetVisible() || game_settings.game_settings.network_game)
							{
								wwNETWORK_TRACE_JG("SIFGameWorld::UpdateSimulation Starting update for player ID: %d", player->GetAttributes()->GetDbId());

								player->GetAttributes()->Update(*game_context, delta_time);
								player->GetMovement()->Update(*game_context, delta_time);
								player->GetSound()->Update(*game_context, delta_time);
								player->GetLookAt()->Update(*game_context, delta_time);
								player->GetState()->Update(*game_context, delta_time);
								player->GetPosAss()->Update(*game_context, delta_time);
								player->GetOffSideIndicator()->Update(*game_context, delta_time);
								//#rc3_legacy player->GetComponent<RUPlayerFacePose>()->Update(*game_context, delta_time);
								player->GetAnimation()->Update(*game_context, delta_time);
							}
						}
				}

				//object_database->Update(game_context, delta_time);
				//This code added here because we dont have object_database->Update, so we need to call those specific stuff explicitly
				RUGamePhaseRuck* ruckPhase = nullptr;
				if (game_state)
				{
					ruckPhase = static_cast<RUGamePhaseRuck*> (game_state->GetPhaseHandler(RUCK)); //ruck_phase = static_cast<RUGamePhaseRuck*>(game->GetGameState()->GetPhaseHandler(RUCK));
				}

				if (ruckPhase)
				{
					QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_RuckStatePtr_Update)
						for (auto &RuckStatePtr : ruckPhase->GetRuckStates())
						{
							if (RuckStatePtr)
							{
								RuckStatePtr->Update(delta_time);
							}
						}
				}

				//This code added here because we dont have object_database->Update, so we need to call those specific stuff explicitly
				RUGamePhaseScrum* scrumPhase = nullptr;
				if (game_state)
				{
					scrumPhase = static_cast<RUGamePhaseScrum*> (game_state->GetPhaseHandler(SCRUM));
				}

				if (scrumPhase)
				{
					QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_ScrumStatePtr_Update)
						for (auto &ScrumStatePtr : scrumPhase->GetScrumStates())
						{
							if (ScrumStatePtr)
							{
								ScrumStatePtr->Update(delta_time);
							}
						}
				}
			}

			{
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Animation_Update);
				PreSyncUpdateCharacterAnimation(delta_time);
			}
		}
	}	

	random_number_generator->SetAssertIfUsed(true);
}

void SIFGameWorld::PostSyncUpdateSimulation(const MabTimeStep& time_step)
{
	// Make sure we ticked the presync.
	if (!bTickedPreSyncSim)
	{
		return;
	}

	QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_Update_simulation_time_PostSyncUpdateSimulation);

	if (CVarPrintTickInfo.GetValueOnAnyThread()) UE_LOG(LogTemp, Display, TEXT("SIFGameWorld::PostSyncUpdateSimulation"));

	random_number_generator->SetAssertIfUsed(false);

	const float delta_time = time_step.delta_time.ToSeconds();
	float unscaled_delta_time = 0.0f;

	if (simulation_time->GetTimeScale() != 0.0f)
	{
		unscaled_delta_time = delta_time / simulation_time->GetTimeScale();
	}

	bool dont_update = false;
	//
	//if(disable_simulation) 
	//{	/// Cutscene editing, only update if 'play' or dragging the time bar.
	//	if(cutscene_manager->IsEditing() && cutscene_manager->GetMorphemeAnimationInterval()<=0.0f) //we dont have live update
	//		dont_update = true;
	//}
	//

	dont_update = SIFApplication::GetApplication()->GetActiveGameWorld() != this;

	if (!dont_update)
	{
		{
			QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Animation_Update);
			PostSyncUpdateCharacterAnimation(delta_time);

			// Clear the arrays for the next time round.
			TaskGraphCompletionEvents.Empty();
			JobCompletionEvents.Empty();
		}

		if (ball)
		{
			QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_ball_Update2);
			ball->Update(delta_time);
		}

		bool hudOnTop = false;
		UWWUIScreenTemplate* screen_template = SIFApplication::GetApplication()->GetUIScreenManager()->GetCurrentScreenTemplate();
		if (screen_template &&
			(screen_template->GetScreenID() == RugbyUIWindowNames::SIFUI_GAME_WINDOW_NAME
			|| screen_template->GetScreenID() == RugbyUIWindowNames::SIFUI_TRAINING_WINDOW_NAME
			|| screen_template->GetScreenID() == RugbyUIWindowNames::RUUI_ONLINE_SELECT_TEAMS_WINDOW_NAME
			|| screen_template->GetScreenID() == RugbyUIWindowNames::SIFUI_MULTIPLAYER_LOBBY_WINDOW_NAME))
		{
			hudOnTop = true;
		}

		//#rc3_legacy
		//if(replay_manager) replay_manager->UpdatePostAnim();
		//
		if (hudOnTop || game_settings.game_settings.network_game)
		{
			if (hud3d && !disable_simulation)
			{
				// Huds run at full framerate
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_hud3d_Update);
				hud3d->Update(time_step);
			}

			if (hud_updater && game_settings.game_settings.game_type != GAME_TRAINING && game_settings.game_settings.game_type != GAME_MENU)
			{
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_hud_updater_Update);
				hud_updater->Update(unscaled_delta_time);

				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_hud_updater_Update_hud_updater_strategy);
				hud_updater_strategy[0]->Update(unscaled_delta_time);

				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_hud_updater_Update_hud_updater_strategy_2);
				hud_updater_strategy[1]->Update(unscaled_delta_time);
			}

			if (!disable_simulation)
			{
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_spatial_helper_Update)
					//replay_hud_updater->Update(unscaled_delta_time);
					if (hud_updater_contextual)
					{
						hud_updater_contextual->Update(unscaled_delta_time);
					}
			}
		}

		if (replay_manager)
		{
			QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Replay_Manager_Update);
			replay_manager->UpdateReplay(delta_time);
		}

		if (spatial_helper && !disable_simulation)
		{
			//// update the offside players
			spatial_helper->Update();
		}

		///------------------------------------------
		/// Save game state for replay (DONE LAST)
		if (game_settings.game_settings.game_type == GAME_TRAINING)
		{
			if (hud_updater)
			{
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_tutorial_manager_UpdateSimulation)
					// update the sandbox specific classes
					tutorial_manager->UpdateSimulation(delta_time);

				hud_updater->Update(delta_time);
				hud_updater_strategy[0]->Update(delta_time);
				hud_updater_strategy[1]->Update(delta_time);
			}
		}
		else
		{
			if (!disable_simulation)
			{
				QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_tutorial_manager_UpdateSimulation)
					// update the main game specific classes
					if (game_timer)
					{
						game_timer->Update(delta_time);
					}

				if (stats_tracker)
				{
					stats_tracker->Update(delta_time);
				}

				if (replay_manager != NULL)
				{
					replay_manager->UpdateRecord(delta_time);
				}
			}
		}
	}

	// Camera system always runs at full update time (Moved to after players have been updated).	
	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_UpdateCamera);
		UpdateCamera(time_step);
	}

	if (cutscene_manager)
	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_cutscene_manager_Update);
		cutscene_manager->TickNetworkCutscenes(delta_time);
	}


#ifdef ENABLE_SOAK_TEST
	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_SoakTest);
		/// Update soak system
		SIFApplication::GetApplication()->GetSoakManager()->OnUpdateSimulation(time_step);
	}
#endif
	///-----------------------------------------------
	/// When testing replays in RugEd need to send every data every sim-step.

	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Random_Reset);
		random_number_generator->SetPrevSeed();

		random_number_generator->SetAssertIfUsed(true);
	}

	// Reset the ticked flag.
	bTickedPreSyncSim = false;
}

void SIFGameWorld::UpdateCamera(const MabTimeStep& time_step, bool updateIfPaused /* = false */)
{
	const float delta_time = time_step.delta_time.ToSeconds();
	float unscaled_delta_time = 0.0f;
	if (simulation_time->GetTimeScale() != 0.0f)
		unscaled_delta_time = delta_time / simulation_time->GetTimeScale();

	bool update_if_paused = game_settings.game_settings.network_game || updateIfPaused;

	MAB_PROFILE_SECTION_START(profile13, "Camera Update");
	if (camera_manager)
	{
		camera_manager->Update(unscaled_delta_time, update_if_paused);
	}
	MAB_PROFILE_SECTION_END(profile13);
}

///-------------------------------------------------------------------------------
/// Do all animation updates for a simulation update.
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::DoAnimationUpdates(float delta_time)
{
	if (animation_manager)
	{
		MAB_PROFILE_SECTION_START(profile2, "PrePhysicsAnimation");
		animation_manager->Update(delta_time, GetViewManager()->GetCurrentView()->Camera(), true);
		animation_manager->PrePhysicsUpdate(delta_time, GetViewManager()->GetCurrentView()->Camera(), true, true);
		MAB_PROFILE_SECTION_END(profile2);
	}


#ifdef ENABLE_LIVELINK
	UpdateLiveLink();
#endif
}
#endif

///-------------------------------------------------------------------------------
/// For cutscene editing time scrubbing.
///-------------------------------------------------------------------------------

#ifdef ENABLE_RUGED
void SIFGameWorld::CutsceneTimeScrubUpdate()
{
	object_database->Update(game_context, SIMULATION_INTERVAL);
	animation_manager->PrePhysicsUpdate(SIMULATION_INTERVAL, NULL, false, true);
	animation_manager->PostPhysicsUpdate(SIMULATION_INTERVAL, true);
}
#endif

///-------------------------------------------------------------------------------
/// UpdateDebugService
///		- Every 'RUDEBUG_UPDATE_INTERVAL' seconds send debug information to RugEd.
///-------------------------------------------------------------------------------

#if defined ENABLE_RUGED
enum
{
	RUGED_DEBUGFLAGS_PAUSED = 0x1,
};

void SIFGameWorld::SetRugEdDebugUpdateInterval(float interval)
{
	if (interval < 0.0f)
		ruged_debug_update_interval = RUDEBUG_UPDATE_INTERVAL;
	else
		ruged_debug_update_interval = interval;
}

void SIFGameWorld::UpdateDebugService(float delta_game_time)
{
	if (!IsAwake())
		return;

	static int last_sime_time_step_sent = -1;
	int this_sim_time_step = GetSimTime()->GetAbsoluteStepCount();
	debug_update_time += delta_game_time;

	while (debug_update_time > ruged_debug_update_interval && this_sim_time_step != last_sime_time_step_sent)
	{
		debug_update_time = 0;	// -= RUDEBUG_UPDATE_INTERVAL;

#ifndef DOING_REPLAY_TEST
		MabBATSContext *bats_context = SIFDebug::GetBATSContext();
		if (bats_context != NULL)
		{
			RUDebugService *service = (RUDebugService*)bats_context->GetService("RUDEBUG");
			if (service != NULL)
			{
				if (!simulation_time->IsPaused() &&
					!SIFDebug::GetGameDebugSettings()->GetRugEdPaused() &&
					!SIFDebug::GetGameDebugSettings()->GetRugEdDebugDisabled())
				{
					SendRugEdState(service, false);
					last_sime_time_step_sent = this_sim_time_step;;
				}
			}
		}
#endif
	}

	profile_update_time += delta_game_time;
	while (profile_update_time > RUPROFILE_UPDATE_INTERVAL)
	{
		profile_update_time = 0;

		MabBATSContext *bats_context = SIFDebug::GetBATSContext();
		if (bats_context != NULL)
		{
			RUProfilerService *pr_service = (RUProfilerService*)bats_context->GetService("RUPROFILER");
			if (pr_service != NULL)
			{
				pr_service->SendProfile();
			}
		}
	}
}

///---------------------------------------------------------------------------
/// Set up sidcomms messages + send to RugEd (now StencilEd)
///---------------------------------------------------------------------------

void SIFGameWorld::SendRugEdState(RUDebugService *service, bool is_replay_check)
{
	service->SetIsReplayCheck(is_replay_check);

	//-----------------------------------------------------------------
	// Output player debug information to RugEd.

	for (size_t i = 0; i < players.size(); i++)
	{
		service->SendPlayerInfo((int)i, players[i], this);
	}

	//-----------------------------------------------------------------
	// Output strategy debug information to RugEd.

	for (size_t i = 0; i < teams.size(); i++)
	{
		RUTeam *team = teams[i];
		if (team->GetFormationManager() != NULL)
		{
			team->GetFormationManager()->SendDebugInfo(service);
			team->GetBlackBoard().SendDebugInfo(service, i);
		}
	}

	{
		FVector bp = ball->GetCurrentPosition();
		MabVector<float> bvalues;

		bvalues.push_back(bp.x);
		bvalues.push_back(bp.y);
		bvalues.push_back(bp.z);

		service->SendBallPosition(bvalues);
	}

	// Send game phase name.
	service->SendGamePhase(game_state->GetPhaseName(game_state->GetPhase()));

	// Send cutscene debug info.
	service->SendCutSceneInfo(this);

	// Send 'debug' positions (used by camera system).
	service->SendDebugPositions(ruged_positions, num_ruged_positions);
	num_ruged_positions = 0;


	int replay_frame_id = -1;
	//if(is_replay_check)
	//{
	//	replay_frame_id = -2;
	//}
	//else
	//{
	//	if(replay_system!=NULL)
	//	{
	//		replay_frame_id = (int)replay_system->GetHighestFrame();
	//	}
	//}

	// Send this last.
	service->IncrementFrame(0.0f, simulation_time->GetAbsoluteStepCount(), replay_frame_id, random_number_generator->GetPrevSeed());

	//	service->SetIsReplayCheck(false);
}

///---------------------------------------------------------------------------
/// RugEd has sent a text command - process it.
///  	Commands have no white space and are separated by ';'
///		arguments are separated by ','
///---------------------------------------------------------------------------

void SIFGameWorld::RugEdCommand(const MabString& debug_command)
{
	random_number_generator->SetAssertIfUsed(false);

	MabStringList::StringList commands;
	MabStringList::ExpandList(commands, debug_command.c_str(), ';');

	MabStringList::StringList::iterator iterator;
	for (iterator = commands.begin(); iterator != commands.end(); ++iterator)
	{
		MabString command = (*iterator);
		if (command.length() > 0)
		{
			MabStringList::StringList arguments;
			MabStringList::ExpandList(arguments, command.c_str(), ',');
			MabStringList::StringList::iterator arg_iterator;

			if (arguments.size() > 0)
			{
				if (arguments[0] == "pauseon")
				{
					SIFDebug::GetGameDebugSettings()->SetRugEdPaused(true);
				}
				else if (arguments[0] == "pauseoff")
				{
					SIFDebug::GetGameDebugSettings()->SetRugEdPaused(false);
				}
				else if (arguments[0] == "attackon")
				{
					SIFDebug::GetGameDebugSettings()->SetTackleDisabled(false);
				}
				else if (arguments[0] == "attackoff")
				{
					SIFDebug::GetGameDebugSettings()->SetTackleDisabled(true);
				}
				else if (arguments[0] == "forceformation")
				{
					if (arguments.size() == 2)
					{
						teams[0]->GetFormationManager()->ForceFormation(arguments[1].c_str(), 1);
					}
					else
					{
						teams[0]->GetFormationManager()->ForceFormation("None", 1);
					}
				}
				else if (arguments[0] == "pickupball")
				{
					if (human_players.size() > 0)
					{
						SIFGameObject *plr = human_players[0]->GetPlayer();
						if (plr)
						{
							game_state->SetBallHolder(plr);

							game_state->SetAttackingTeam(plr->GetAttributes()->GetTeam());
						}
					}
				}
				else if (arguments[0] == "setbh" && arguments.size() == 2)
				{
					int bh = atoi(arguments[1].c_str());
					SIFDebug::GetGameDebugSettings()->SetBallHolder(bh);
				}
				else if (arguments[0] == "setup" && arguments.size() == 2)
				{
					SIFDebug::GetGameDebugSettings()->SetupSituation(arguments[1], this);
				}
				else if (arguments[0] == "safeareaoff")
				{
					SIFDebug::GetCommonDebugSettings()->SetSafeAreaEnabled(false);
				}
				else if (arguments[0] == "safeareaon")
				{
					SIFDebug::GetCommonDebugSettings()->SetSafeAreaEnabled(true);
				}
				else
				{
					MABLOGDEBUG("Unknown RugEd debug command: %s", arguments[0].c_str());
				}
			}
		}
	}

	random_number_generator->SetAssertIfUsed(true);
}

///-------------------------------------------------------------------------------
/// Call to show an 'X' + name in RugEd.
///-------------------------------------------------------------------------------

void SIFGameWorld::AddRugEdDebugPosition(const char *name, FVector &position)
{
	int i = 0;
	while (i < num_ruged_positions)
	{
		if (strcmp(ruged_positions[i].name, name) == 0)
		{
			ruged_positions[i].x = position.x;
			ruged_positions[i].y = position.y;
			ruged_positions[i].z = position.z;

			return;
		}
		i++;
	}

	if (num_ruged_positions < MAX_RUGED_POSITIONS)
	{
		ruged_positions[i].x = position.x;
		ruged_positions[i].y = position.y;
		ruged_positions[i].z = position.z;

		strncpy(ruged_positions[num_ruged_positions].name, name, RUGED_POSITION_NAME_SIZE - 1);
		ruged_positions[num_ruged_positions].name[RUGED_POSITION_NAME_SIZE - 1] = 0;

		num_ruged_positions++;
	}
}

///-------------------------------------------------------------------------------
/// Called after StencilEd (DebugViewer) replay message, send game state to compare against what is
/// in StencilEd...
///-------------------------------------------------------------------------------

void SIFGameWorld::SendPostReplayState()
{
	MabBATSContext *bats_context = SIFDebug::GetBATSContext();
	if (bats_context != NULL)
	{
		RUDebugService *service = (RUDebugService*)bats_context->GetService("RUDEBUG");
		if (service != NULL)
		{
			SendRugEdState(service, true);
		}
	}
}


#endif

///-------------------------------------------------------------------------------
/// Get player 'index'
///-------------------------------------------------------------------------------

ARugbyCharacter* SIFGameWorld::GetPlayer(int index) const
{
	int playersOnTeamIncBench = 0;
	playersOnTeamIncBench = GetGameSettings().game_limits.GetNumberOfPlayersPerTeamIncBench();
	if (officials != NULL)
	{
		MABASSERT(index >= 0 && index <= (playersOnTeamIncBench/*NUM_PLAYERS_PER_TEAM_INC_BENCH*/ * (int)teams.size() + (int)(officials->GetPlayers().size())));
	}
	else
	{
		MABASSERT(index >= 0 && index <= (playersOnTeamIncBench/*NUM_PLAYERS_PER_TEAM_INC_BENCH*/ * (int)teams.size()));
	}
	for (size_t i = 0; i < players.size(); i++)
	{
		if (players[i]->GetAttributes()->GetIndex() == index)
			return players[i];
	}

	return NULL;
}

///-------------------------------------------------------------------------------
/// Get player based off of their database index, will return NULL if the index is not a currently created player
///-------------------------------------------------------------------------------

ARugbyCharacter* SIFGameWorld::GetPlayerFromDB(int db_id) const
{
	MABASSERT(db_id >= 0);
	for (size_t i = 0; i < players.size(); i++)
	{
		if (players[i]->GetAttributes()->GetDBPlayer()->GetDbId() == db_id)
			return players[i];
	}

	return NULL;
}

///-------------------------------------------------------------------------------
/// Rebuild the list of players on the field
///-------------------------------------------------------------------------------

void SIFGameWorld::RebuildPlayerList()
{
	// Calculate the number of players from all of the teams.
	unsigned int num_players = 0;
	for (size_t i = 0; i < teams.size(); ++i)
	{
		num_players += (int)teams[i]->GetPlayers().size();
	}

	// Add in the players from the officials 'team'.
	if (officials != NULL)
	{
		num_players += (int)officials->GetPlayers().size();
	}

	// Reserve space for the players and officials.
	players.reserve(num_players);

	// Populate the player list with all of the players from the teams.
	players = teams[0]->GetPlayers();
	for (size_t i = 1; i < teams.size(); ++i)
	{
		players.insert(players.end(), teams[i]->GetPlayers().begin(), teams[i]->GetPlayers().end());
	}

	// Add the players from the officials.
	if (officials != NULL)
	{
		players.insert(players.end(), officials->GetPlayers().begin(), officials->GetPlayers().end());
	}

	// Update the players 'index' so it truely reflects the players index in to this->players[].
	for (size_t i = 0; i < players.size(); ++i)
	{
		players[i]->GetAttributes()->SetIndex((int)i);
	}

	GetGameGTB()->Initialise();
}



///-------------------------------------------------------------------------------
/// Fill in 'comp_ids' with all the competitions a team is part of.
///-------------------------------------------------------------------------------

void GetTeamCompetitions(unsigned short team_id, MabVector<unsigned short> &comp_ids, const RUGameDatabaseManager& game_database_mgr)
{
	MabString statement = MabString(0, "SELECT competition_id FROM RUDB_COMP_DEF_TEAM WHERE team_id=%d", team_id);

	SqliteMabStatement result;
	if (game_database_mgr.RawProcessSqlStatement(result, statement.c_str()))
	{
		while (result.Next())
		{
			comp_ids.push_back(result.GetColumnAsUShort(0));
		}
	}
}

///-------------------------------------------------------------------------------
/// Reload the ball gfx.
///-------------------------------------------------------------------------------

void SIFGameWorld::LoadBallMaterial(int TeamId)
{
	checkf(ball != nullptr, TEXT("Ball has not been initialised, in SIFGameWorld::LoadBallGraphics"));

	UWorld* unrealWorld = mGameInstance.GetWorld();
	if (UOBJ_IS_VALID(unrealWorld))
	{
		if (UOBJ_IS_VALID(ball) && UOBJ_IS_VALID(ball->GetBallMesh()))
		{
			FString file_path = GetBallFileName(TeamId);
			MABASSERT(!file_path.IsEmpty());

			UMaterialInterface* BallMaterial = LoadObject<UMaterialInterface>(NULL, *file_path, NULL, LOAD_None, NULL);

			if (BallMaterial != nullptr)
			{
				UMaterialInstanceDynamic* BallInstanceDynamic = UMaterialInstanceDynamic::Create(BallMaterial, ball);

				ball->GetBallMesh()->SetMaterial(0, BallInstanceDynamic);
				ball->GetBallMesh()->LightingChannels.bChannel1 = true;
			}
		}
	}
}

///-------------------------------------------------------------------------------
/// Create and Initialize the ball.
///-------------------------------------------------------------------------------
void SIFGameWorld::InitialiseBall()
{
	if (ball == NULL)
	{
		UWorld* unrealWorld = mGameInstance.GetWorld();
		ball = (ASSBall*)unrealWorld->SpawnActor(mGameInstance.GetDefaultBallClass());
		ball->SetActorHiddenInGame(true);
		ball->Init(this);
	}
	else
	{
		game_state->SetBallHolder(NULL);
	}

	LoadBallMaterial();

	// Set up the wind for the ball
	SetupWind(game_settings.weather_settings.wind_direction, game_settings.weather_settings.wind_strength);
}

///-------------------------------------------------------------------------------
/// Load the texture for the replay screen wipe
///-------------------------------------------------------------------------------


void SIFGameWorld::LoadScreenWipe()
{
	int comp_db_id = this->pseudo_competition_id;
	unsigned short icon_id = 1035; // This is gross. The generic logo was added later, so the id is way off. Pref to change this to 1001.
	if (comp_db_id != 0)
	{
		RL3DB_COMPETITION_DEFINITION comp_def((unsigned short)comp_db_id);
		icon_id = comp_def.GetIconId();
	}
	RUDB_ICON icon;
	SIFApplication::GetApplication()->GetGameDatabaseManager()->LoadData(icon, icon_id);

	if (screen_wipe_manager.get())
	{
		screen_wipe_manager->LoadScreenWipe(icon);
	}

	/*const char *SWIPE_AVIVA   = "avi";
	const char *SWIPE_GENERIC = "gen";
	const char *SWIPE_ITM     = "itm";
	const char *SWIPE_LIONS   = "lio";
	const char *SWIPE_PROD2   = "prod2";
	const char *SWIPE_RABO    = "rdp";
	const char *SWIPE_SUPER15 = "rug";
	const char *SWIPE_TOP14   = "top";
	const char *SWIPE_WRC     = "wrc";
	const char *SWIPE_NRC     = "nrc";
	const char *SWIPE_SWS     = "sws";
	const char *SWIPE_WSC     = "wsc";
	const char *SWIPE_ACC     = "acc";
	const char *SWIPE_QUAD    = "qua";

	const char *swipe_name = SWIPE_GENERIC;
	//swipe_name = SWIPE_ITM;				//#mb - Uncomment this line to manually set the texture for the screen wipe.

	if(GetWorldId()!=WORLD_ID::SANDBOX)
	{
		switch(this->pseudo_competition_id)			// Note:LoadScreenWipe() must be called after InitialiseBall()
		{
		case DB_COMPID_AVIVA:			swipe_name = SWIPE_AVIVA;	break;
		case DB_COMPID_TOP14:			swipe_name = SWIPE_TOP14;	break;
		case DB_COMPID_SUPER15:			swipe_name = SWIPE_SUPER15; break;
		case DB_COMPID_WORLDCUP:		swipe_name = SWIPE_WRC; break;
		case DB_COMPID_ITM:				swipe_name = SWIPE_ITM; break;
		case DB_COMPID_LIONS_TOUR:		swipe_name = SWIPE_LIONS; break;
		case DB_COMPID_PRO_D2:			swipe_name = SWIPE_PROD2; break;
		case DB_COMPID_RABODIRECTPRO:	swipe_name = SWIPE_RABO; break;

		case DB_COMPID_NRC:				swipe_name = SWIPE_NRC; break;
		case DB_COMPID_SEVENS_SERIES:	swipe_name = SWIPE_SWS; break;
		case DB_COMPID_SEVENS:			swipe_name = SWIPE_WSC; break;
		case DB_COMPID_ACC:				swipe_name = SWIPE_ACC; break;

		case DB_COMPID_QUADNATIONS:		swipe_name = SWIPE_QUAD; break;
		default:
			break;
		}
	}

	screen_wipe_manager->LoadScreenWipe(swipe_name);*/
}


///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------
void SIFGameWorld::CalculatePseudoCompetitionId()
{
	if (IsMatch())
	{
		RL3DatabaseScopedLock	database_lock;			// Lock database.

		const RUCareerModeManager* const career_manager = SIFApplication::GetApplication()->GetCareerModeManager();
		RUActiveCompetitionBase* active_competition = NULL;
		if (career_manager && career_manager->IsActive())
		{
			active_competition = career_manager->GetActiveCompetition();
		}

		const RUGameDatabaseManager* const game_database_mgr = SIFApplication::GetApplication()->GetGameDatabaseManager();
		if (game_database_mgr == NULL)
		{
			MABBREAK();
			return;
		}

		unsigned short competition_id = 0;
		unsigned short team_id[2];

		for (int team_idx = 0; team_idx < 2; team_idx++)
		{
			team_id[team_idx] = GetGameSettings().team_settings[team_idx].team.GetDbId();
		}

		if (active_competition)
		{
			competition_id = (unsigned short)active_competition->GetDefinitionId();
		}
		else
		{
			// Compare competition lists for each team, and find first matching competition which both teams participate in.

			/*MabVector<unsigned short> team0_comps;
			MabVector<unsigned short> team1_comps;

			GetTeamCompetitions(team_id[0],team0_comps, *game_database_mgr);
			GetTeamCompetitions(team_id[1],team1_comps, *game_database_mgr);

			for(unsigned int i=0;i<team0_comps.size();i++)
			{
				unsigned short compi = team0_comps[i];
				for(unsigned int j=0;j<team1_comps.size();j++)
				{
					if(compi==team1_comps[j] && compi!=DB_COMPID_EURO_RUGBY_CLUB)		// DB_COMPID_EURO_RUGBY_CLUB isn't licensed but screws up this check if not ignored.
					{
						competition_id = compi;
						i = (int)team0_comps.size();
						j = (int)team1_comps.size();
					}
				}
			}*/
		}

		MABLOGDEBUG("pseudo_competition_id = %d", competition_id);
		this->pseudo_competition_id = competition_id;
	}
}

///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------
void SIFGameWorld::ResetPlayerTickPaused()
{
	MabVector<std::unique_ptr<RUTeam>>::const_iterator team_iterator;
	int playersPerTeam = GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();

	for (team_iterator = teams.begin(); team_iterator != teams.end(); ++team_iterator)
	{
		if ((*team_iterator)->GetSide() == SSTEAMSIDE::SIDE_OFFICIALS)
		{
			continue;
		}
		const SIFRugbyCharacterList &team_players = (*team_iterator)->GetPlayers();
		for (int i = 0; i < (int)team_players.size(); ++i)
		{
			ARugbyCharacter* player = team_players[i];
			player->SetPaused(false);
		}

		const SIFRugbyCharacterList &bench_players = (*team_iterator)->GetBenchPlayers();
		for (int i = 0; i < (int)bench_players.size(); ++i)
		{
			ARugbyCharacter* player = bench_players[i];
			player->SetPaused(false);
		}
	}
}

///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------
void SIFGameWorld::SetupPlayerTickPaused()
{
	MabVector<std::unique_ptr<RUTeam>>::const_iterator team_iterator;
	int playersPerTeam = GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();

	for (team_iterator = teams.begin(); team_iterator != teams.end(); ++team_iterator)
	{
		if ((*team_iterator)->GetSide() == SSTEAMSIDE::SIDE_OFFICIALS)
		{
			continue;
		}
		const SIFRugbyCharacterList &team_players = (*team_iterator)->GetPlayers();
		for (int i = 0; i < (int)team_players.size(); ++i)
		{
			ARugbyCharacter* player = team_players[i];
			player->SetPaused(false);
		}

		const SIFRugbyCharacterList &bench_players = (*team_iterator)->GetBenchPlayers();
		for (int i = 0; i < (int)bench_players.size(); ++i)
		{
			ARugbyCharacter* player = bench_players[i];
			player->SetPaused(false);
		}
	}
}

//===============================================================================
//===============================================================================
class FParallelPlayerAnimationTask
{
	ARugbyCharacter* Player;
	float DeltaSeconds;

public:
	FParallelPlayerAnimationTask(ARugbyCharacter* InPlayer, float InDeltaSeconds)
		: Player(InPlayer)
		, DeltaSeconds(InDeltaSeconds)
	{
	}

	FORCEINLINE TStatId GetStatId() const
	{
		RETURN_QUICK_DECLARE_CYCLE_STAT(FParallelPlayerAnimationTask, STATGROUP_TaskGraphTasks);
	}
	FORCEINLINE static ENamedThreads::Type GetDesiredThread()
	{
		return ENamedThreads::AnyHiPriThreadHiPriTask;
	}

	static FORCEINLINE ESubsequentsMode::Type GetSubsequentsMode()
	{
		return ESubsequentsMode::TrackSubsequents;
	}

	void DoTask(ENamedThreads::Type CurrentThread, const FGraphEventRef& MyCompletionGraphEvent)
	{
		if (CurrentThread != ENamedThreads::GameThread)
		{
			GInitRunaway();
		}

		if (UOBJ_IS_VALID(Player))
		{
			Player->DoThreadedAnimationUpdateWork(DeltaSeconds);
		}
	}
};

//===============================================================================
//===============================================================================
class FParallelPlayerAnimationJob
{
	ARugbyCharacter* Player;
	float DeltaSeconds;

public:
	FParallelPlayerAnimationJob(ARugbyCharacter* InPlayer, float InDeltaSeconds)
		: Player(InPlayer)
		, DeltaSeconds(InDeltaSeconds)
	{
	}

	FORCEINLINE TStatId GetStatId() const
	{
		RETURN_QUICK_DECLARE_CYCLE_STAT(FParallelPlayerAnimationJob, STATGROUP_TaskGraphTasks);
	}

	void DoJob()
	{
		if (UOBJ_IS_VALID(Player))
		{
			Player->DoThreadedAnimationUpdateWork(DeltaSeconds);
		}
	}
};

//===============================================================================
//===============================================================================
class FParallelPlayerAnimationCompletionTask
{
	ARugbyCharacter* Player;
	float DeltaSeconds;

public:
	FParallelPlayerAnimationCompletionTask(ARugbyCharacter* InPlayer, float InDeltaSeconds)
		: Player(InPlayer)
		, DeltaSeconds(InDeltaSeconds)
	{
	}

	FORCEINLINE TStatId GetStatId() const
	{
		RETURN_QUICK_DECLARE_CYCLE_STAT(FParallelPlayerAnimationCompletionTask, STATGROUP_TaskGraphTasks);
	}
	static ENamedThreads::Type GetDesiredThread()
	{
		return ENamedThreads::GameThread;
	}

	static FORCEINLINE ESubsequentsMode::Type GetSubsequentsMode()
	{
		return ESubsequentsMode::TrackSubsequents;
	}

	void DoTask(ENamedThreads::Type CurrentThread, const FGraphEventRef& MyCompletionGraphEvent)
	{
		if (CurrentThread != ENamedThreads::GameThread)
		{
			GInitRunaway();
		}

		if (UOBJ_IS_VALID(Player))
		{
			Player->CompleteAnimationUpdateOnGameThread(DeltaSeconds);
		}
	}
};

void SIFGameWorld::PreSyncUpdateCharacterAnimation(float delta_time)
{
	if (CVarPrintTickInfo.GetValueOnAnyThread()) UE_LOG(LogTemp, Display, TEXT("SIFGameWorld::PreSyncUpdateCharacterAnimation"));

	// Get the thread mode we should be using.
	EMontageAdvanceThreadMode ThreadMode = (EMontageAdvanceThreadMode)CVarMontageAdvanceThreadMethod.GetValueOnAnyThread();

	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Animation_Update_Setup_And_LaunchTasks);

		// Animation update logic added here in order to better mimic RC3 behaviour.
		int32 PlayerCount = 0;

		for (ARugbyCharacter* player : all_players)
		{
			URugbyCharacterAnimInstance* player_anim_instance = player->GetAnimInstance();
			
			if (player_anim_instance)
			{
				// We can skip updating animation if we aren't visible or have URO on. This flag will tell us if we ended up processing the animation.
				player_anim_instance->ResetUpdateAnimationThisFrameFlag();

				// This flag shows if we started any multithreaded work. Reset it now.
				player_anim_instance->ResetWaitingOnMultithreadedWorkFlag();
			}

			if ((player->GetVisible() || game_settings.game_settings.network_game) && !player->IsPaused() && !player->GetState()->IsInCutScene() && (!(GetCutSceneManager()->IsCinematicRunning() && GetCutSceneManager()->IsHideNonActors())))
			{
				if (!game_settings.game_settings.network_game)
				{
					//player->GetMesh()->SetShouldForceUpdateSkip(false);
					player->GetMesh()->VisibilityBasedAnimTickOption = EVisibilityBasedAnimTickOption::AlwaysTickPoseAndRefreshBones;
				}

				wwNETWORK_TRACE_JG_DISABLED("SIFGameWorld::UpdateSimulation Checking animation and anim instance for player ID: %d", player->GetAttributes()->GetDbId());

				RUPlayerAnimation* player_animation = player->GetAnimation();

				if (player_animation && UOBJ_IS_VALID(player_anim_instance))
				{
					//wwNETWORK_TRACE_JG("SIFGameWorld::UpdateSimulation Starting animation update for player ID: %d", player->GetAttributes()->GetDbId());

					// This is the final RC3 step for non threaded mode.
					if (ThreadMode == EMontageAdvanceThreadMode::TNonThreaded)
					{
						QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Animation_Process_Tasks_On_Game_Thread);

						// RC3 Animation updates
						player_animation->GetStateMachine().Update(delta_time);
						player->UpdateRootMotion(delta_time);
						player_anim_instance->UpdateAlignment(delta_time);
					}
					else if (ThreadMode == EMontageAdvanceThreadMode::TTaskGraph)
					{
						player_animation->GetStateMachine().Update(delta_time);

						QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Animation_Update_Launch_Tasks);

						wwNETWORK_TRACE_JG("SIFGameWorld::PreSyncUpdateCharacterAnimation Adding completion event for player ID: %d", player->GetAttributes()->GetDbId());
						TaskGraphCompletionEvents.Add(TGraphTask<FParallelPlayerAnimationTask>::CreateTask().ConstructAndDispatchWhenReady(player, delta_time), player);

						//// set up a task to run on the game thread to accept the results
						//FGraphEventArray Prerequistes;
						//Prerequistes.Add(ParallelAnimationTask);

						//TaskGraphCompletionEvents.Add(TGraphTask<FParallelPlayerAnimationCompletionTask>::CreateTask(&Prerequistes).ConstructAndDispatchWhenReady(player, delta_time));
					}
					else if (ThreadMode == EMontageAdvanceThreadMode::TSingleParallelThread)
					{
						JobCompletionEvents.Add(TThreadedJob<FParallelPlayerAnimationJob>::CreateJob().ConstructAndDispatchWhenReady(player, delta_time), player);
					}
				}
				else
				{
					UE_LOG(LogTemp, Warning, TEXT("SIFGameWorld::UpdateSimulation Player in match getting updated has no animation or anim instance!!!"));
					ensure(player_animation && UOBJ_IS_VALID(player_anim_instance));
				}
			}
			else
			{
				if (!game_settings.game_settings.network_game)
				{
					player->GetMesh()->VisibilityBasedAnimTickOption = EVisibilityBasedAnimTickOption::OnlyTickPoseWhenRendered;
					//player->GetMesh()->SetShouldForceUpdateSkip(true);
				}
			}

			PlayerCount++;
		}
	}
}

void SIFGameWorld::PostSyncUpdateCharacterAnimation(float delta_time)
{
	if (CVarPrintTickInfo.GetValueOnAnyThread()) UE_LOG(LogTemp, Display, TEXT("SIFGameWorld::PostSyncUpdateCharacterAnimation"));

	// Get the thread mode we should be using.
	EMontageAdvanceThreadMode ThreadMode = (EMontageAdvanceThreadMode)CVarMontageAdvanceThreadMethod.GetValueOnAnyThread();

	// This section only applies if we are doing multithreaded animation work. We need to grab the players we started to update in the above loop, and finalize their animation once the work is completed.
	if (ThreadMode == EMontageAdvanceThreadMode::TTaskGraph)
	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Animation_Update_Wait_For_Tasks);

		for (auto& CurrentPair : TaskGraphCompletionEvents)
		{
			FGraphEventRef& TaskEvent = CurrentPair.Key;
			ARugbyCharacter* Player = CurrentPair.Value;

			wwNETWORK_TRACE_JG("SIFGameWorld::PostSyncUpdateCharacterAnimation Checking task graph event completion.");

			if (TaskEvent.IsValid() && !TaskEvent->IsComplete())
			{
				FTaskGraphInterface::Get().WaitUntilTaskCompletes(TaskEvent, ENamedThreads::GameThread);
			}

			if (UOBJ_IS_VALID(Player))
			{
				wwNETWORK_TRACE_JG("SIFGameWorld::PostSyncUpdateCharacterAnimation Finalizing completion event for player ID: %d", Player->GetAttributes()->GetDbId());
				Player->CompleteAnimationUpdateOnGameThread(delta_time);
			}

			TaskEvent.SafeRelease();
		}
	}

	// This section only applies if we are doing multithreaded animation work. We need to grab the players we started to update in the above loop, and finalize their animation once the work is completed.
	if (ThreadMode == EMontageAdvanceThreadMode::TSingleParallelThread)
	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_Animation_Update_Wait_For_Tasks);

		for (auto& CurrentPair : JobCompletionEvents)
		{
			FJobEventRef& JobEvent = CurrentPair.Key;
			ARugbyCharacter* Player = CurrentPair.Value;

			while (!JobEvent->IsComplete())
			{
				if (FJobThreadManager::IsValid())
				{
					FJobThreadManager::Get().ProcessNextJob();
				}
			}

			if (UOBJ_IS_VALID(Player))
			{
				Player->CompleteAnimationUpdateOnGameThread(delta_time);
			}
		}
	}

	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_AnimationEventProcessing);
		for (size_t i = 0; i < players.size(); i++)
		{
			//Process the animation notify queue.
			players[i]->ProcessAnimationNotifyQueue();
		}
	}

	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_PostAnimation_Update)
			//-----------------------------------------------
			// PostAnimationUpdate
			for (ARugbyCharacter* player : all_players)
			{
				if (player->GetVisible() || game_settings.game_settings.network_game)
				{
					wwNETWORK_TRACE_JG_DISABLED("SIFGameWorld::UpdateSimulation Starting movement post animation update for player ID: %d", player->GetAttributes()->GetDbId());
					player->GetMovement()->PostAnimationUpdate(*game_context, delta_time);
				}
			}
	}

	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_PostAnimation_Update2)
			RUPlayerAnimation::ClearTackleAnimationPairings();	// Clear any cached tackle node decisions.
		for (ARugbyCharacter* player : all_players)
		{
			if (player->GetVisible() || game_settings.game_settings.network_game)
			{
				player->GetAnimation()->PostAnimationUpdate(*game_context, delta_time);
			}
		}
	}

	//#rc3_legacy animation_manager->PostPhysicsUpdate( delta_time, true );

	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_SIFGameWorld_UpdateSimulation_PostAnimationAlignmentUpdate)
			//-----------------------------------------------
			//PostAnimationAlignmentUpdate
			for (ARugbyCharacter* player : all_players)
			{
				if (player->GetVisible() || game_settings.game_settings.network_game)
				{
					player->GetMovement()->PostAnimationAlignmentUpdate();
				}
			}
	}
}

static TAutoConsoleVariable<int32> CVarOverridePlayerAnimationUpdateRate(TEXT("ww.OverridePlayerAnimationUpdateRate"), 0, TEXT("If greater than 0 will set the animation update rate of all players."));

static TAutoConsoleVariable<float> CVarBestAnimationPlayRateDistanceToBall(TEXT("ww.BestAnimationPlayRateDistanceToBall"),
	500.f, 
	TEXT("Distance a player should be to the ball to play animations at the best update rate."));

static TAutoConsoleVariable<float> CVarNormalAnimationPlayRateDistanceToBall(TEXT("ww.NormalAnimationPlayRateDistanceToBall"),
	1000.f,
	TEXT("Distance a player should be to the ball to play animations at the normal update rate."));

static TAutoConsoleVariable<float> CVarLowAnimationPlayRateDistanceToBall(TEXT("ww.LowAnimationPlayRateDistanceToBall"),
	1500.f,
	TEXT("Distance a player should be to the ball to play animations at the low update rate."));

void SIFGameWorld::UpdateCharacterExternalRateOptimization(float delta_time, uint64 step_count)
{
	//int32 Counters[4];
	//Counters[0] = 0;
	//Counters[1] = 0;
	//Counters[2] = 0;
	//Counters[3] = 0;

	for (size_t i = 0; i < all_players.size(); i++)
	{
		ARugbyCharacter* current_player = all_players[i];

		if (UOBJ_IS_VALID(current_player))
		{
			// Grab the params and stored values for our rate optimizations.
			FExternalAnimRateOptimizationData& anim_rate_opt_data = current_player->ExternalAnimRateOptData;

			//anim_rate_opt_data.FrameOffset = 0;

			// Add to our accumulated delta.
			anim_rate_opt_data.AccumulatedDeltaTime += delta_time;

			// Whether we should not tick this animation. Mainly due to the player not being visible. For online, only if the player is not on the field. Overriden by bForceTick if that is true.
			bool bSkipAnimationTick = true;

			// Skip the animation tick for players not on the field.
			for (int j = 0; j < players.size(); j++)
			{
				if (current_player == players[j])
				{
					bSkipAnimationTick = false;
					break;
				}
			}

			// If a player is invisible in a non network game, don't need to tick them.
			if (!bSkipAnimationTick && !current_player->GetVisible() && !game_settings.game_settings.network_game)
			{
				bSkipAnimationTick = true;
			}

			const bool bForceTick = GetCutSceneManager()->IsCinematicRunning()
				|| current_player->HasAnimRootMotion()
				|| CVarForceExternalRateUpdateEveryFrame.GetValueOnAnyThread(); // Intended to be debug toggle only.

			wwNETWORK_TRACE_JG("SIFGameWorld::UpdateCharacterExternalRateOptimization bForceTick: %d, IsCinematicRunning: %d, HasAnimRootMotion: %d, CVarForceExternalRateUpdateEveryFrame: %d", bForceTick, GetCutSceneManager()->IsCinematicRunning(), current_player->HasAnimRootMotion(), CVarForceExternalRateUpdateEveryFrame.GetValueOnAnyThread());


			// If we want to force a tick for the above reasons, this should take precendence over the optimization of skipping the animation tick.
			if (bForceTick)
			{
				bSkipAnimationTick = false;
			}

			const bool bTickThisFrame = (((((step_count + anim_rate_opt_data.FrameOffset) % anim_rate_opt_data.TickRate) == 0)) || bForceTick) && (!bSkipAnimationTick);
		
			anim_rate_opt_data.SkippedTicks = bTickThisFrame ? 0 : (anim_rate_opt_data.SkippedTicks + 1);

			// ERO component params.
			const bool bSkippingTicks = anim_rate_opt_data.TickRate > 1;
			const float InterpolateAlpha = FMath::Clamp(1.f / static_cast<float>(anim_rate_opt_data.TickRate - anim_rate_opt_data.SkippedTicks), 0.f, 1.f);

			// Grab all the skinned mesh components, and loop through them, casting to their type.
			TArray<UActorComponent*> primitive_components = current_player->GetComponentsByClass(USkinnedMeshComponent::StaticClass());
			for (UActorComponent* primitive_actor_components : primitive_components)
			{
				USkinnedMeshComponent* current_mesh = CastChecked<USkinnedMeshComponent>(primitive_actor_components);

				if (current_mesh)
				{
					if (UOBJ_IS_VALID(current_mesh))
					{
						//current_mesh->EnableExternalTickRateControl(true);

						// Make sure we actually tick the component. It might be disabled if we had a halt this frame.
						current_mesh->SetShouldForceUpdateSkip(false);

						// Setup external tick.
						current_mesh->EnableExternalInterpolation(bSkippingTicks && !bForceTick);
						current_mesh->EnableExternalUpdate(bTickThisFrame);
						current_mesh->EnableExternalEvaluationRateLimiting(bSkippingTicks);
						current_mesh->SetExternalDeltaTime(anim_rate_opt_data.AccumulatedDeltaTime);

						current_mesh->SetExternalInterpolationAlpha(InterpolateAlpha);
					}
				}
			}

			wwNETWORK_TRACE_JG("ID: %s, bForceTick: %d, bSkipAnimationTick: %d, bTickThisFrame: %d, AccumulatedDeltaTime: %f, SkippedTicks: %d, FrameOffset: %d, anim_rate_opt_data.TickRate: %d : %d", TCHAR_TO_UTF8(*current_player->GetFullName()), bForceTick, bSkipAnimationTick, bTickThisFrame, anim_rate_opt_data.AccumulatedDeltaTime, anim_rate_opt_data.SkippedTicks, anim_rate_opt_data.FrameOffset, anim_rate_opt_data.TickRate, anim_rate_opt_data.TickRate > 1);

			// Reset or carry over total delta time.
			anim_rate_opt_data.AccumulatedDeltaTime = bTickThisFrame ? 0.0f : anim_rate_opt_data.AccumulatedDeltaTime;

			PARTICIPATION_LEVEL player_participation = current_player->GetState()->GetParticipationLevel();
			//Counters[(int32)player_participation] += 1;

			// Update tick rate for the next time we tick.
			if (bTickThisFrame)
			{
				if (bForceTick)
				{
					anim_rate_opt_data.TickRate = 1;
				}
				else
				{
					const float PlayerDistToBallSq = (current_player->GetActorLocation() - GetBall()->GetActorLocation()).SizeSquared();

					const float BestAnimPlayRateDistSq = CVarBestAnimationPlayRateDistanceToBall.GetValueOnGameThread() * CVarBestAnimationPlayRateDistanceToBall.GetValueOnGameThread();

					const float NormAnimPlayRateSq = CVarNormalAnimationPlayRateDistanceToBall.GetValueOnGameThread() * CVarNormalAnimationPlayRateDistanceToBall.GetValueOnGameThread();

					const float LowAnimPlayRateSq = CVarLowAnimationPlayRateDistanceToBall.GetValueOnGameThread() * CVarLowAnimationPlayRateDistanceToBall.GetValueOnGameThread();

					if (PlayerDistToBallSq < BestAnimPlayRateDistSq)
					{
						anim_rate_opt_data.TickRate = 1;
					}
					else if (PlayerDistToBallSq < NormAnimPlayRateSq)
					{
						anim_rate_opt_data.TickRate = 2;
					}
					else if(PlayerDistToBallSq < LowAnimPlayRateSq)
					{
						anim_rate_opt_data.TickRate = 3;
					}
					else
					{
						anim_rate_opt_data.TickRate = 4;
					}

					if (CVarOverridePlayerAnimationUpdateRate.GetValueOnGameThread() > 0)
					{
						anim_rate_opt_data.TickRate = CVarOverridePlayerAnimationUpdateRate.GetValueOnGameThread();
					}
				}
			}
		}
	}

	//UE_LOG(LogTemp, Display, TEXT("PL_LINEOUT_PARTICIPATING: %f, PL_PARTICPATING: %f, PL_INTERESTED: %f, PL_OBSERVING: %f")
	//	, ((float)Counters[0] / (float)players.size()) * 100.0f
	//	, ((float)Counters[1] / (float)players.size()) * 100.0f
	//	, ((float)Counters[2] / (float)players.size()) * 100.0f
	//	, ((float)Counters[3] / (float)players.size()) * 100.0f)
}

static constexpr int32 NUM_PLAYER_FVECTORS_HASHED = 2;

void SIFGameWorld::LogAndCheckNetworkState(float delta_time, uint32 step_count)
{
	// Buffer for writing game state into.
	TArray<uint8> Buffer;
	const int32 PlayerMemoryAllocated = players.size() * sizeof(FVector) * NUM_PLAYER_FVECTORS_HASHED;
	const int32 OtherMemoryAllocated = sizeof(FVector) + sizeof(MabUInt32);
	Buffer.Reserve(PlayerMemoryAllocated + OtherMemoryAllocated);
	FMemoryWriter Writer = FMemoryWriter(Buffer);

	wwNETWORK_TRACE_JG_ALWAYS("Players Positions:");

	static uint32 lastTimeStep = 0;

	for (size_t i = 0; i < players.size(); i++)
	{
		FVector PlayerPosition = players[i]->GetMovement()->GetCurrentPosition();
		// round up the float to 0.1f
		PlayerPosition.X = FMath::RoundToFloat(PlayerPosition.X * 10.0f) / 10.0f;
		PlayerPosition.Y = FMath::RoundToFloat(PlayerPosition.Y * 10.0f) / 10.0f;
		PlayerPosition.Z = FMath::RoundToFloat(PlayerPosition.Z * 10.0f) / 10.0f;
		FVector BonePosition = players[i]->GetBoneWorldPosition("ball");
		FString TransformString = players[i]->GetActorTransform().ToString();
		FString ExternalRateString = players[i]->ExternalAnimRateOptData.ToString();

		Writer << PlayerPosition;
		//Writer << BonePosition; // This can cause a false desync at the end of a cutscene, if the bones go crazy.
		Writer << ExternalRateString;

		wwNETWORK_TRACE_JG("ID: %s, %u, Role: %s, X: %.20f, Y: %.20f, Z: %.20f", TCHAR_TO_UTF8(*players[i]->GetFullName()), players[i]->GetAttributes()->GetDbId(), players[i]->GetRole()->GetShortClassName(), PlayerPosition.X, PlayerPosition.Y, PlayerPosition.Z);
		wwNETWORK_TRACE_JG("ID: %s, %u, Bone position X: %.20f, Y: %.20f, Z: %.20f", TCHAR_TO_UTF8(*players[i]->GetFullName()), players[i]->GetAttributes()->GetDbId(), BonePosition.X, BonePosition.Y, BonePosition.Z);
		wwNETWORK_TRACE_JG("ID: %s, %u, Unreal Transform: %s", TCHAR_TO_UTF8(*players[i]->GetFullName()), players[i]->GetAttributes()->GetDbId(), TCHAR_TO_UTF8(*TransformString));
		wwNETWORK_TRACE_JG("ID: %s, %u, External Rate Data: %s", TCHAR_TO_UTF8(*players[i]->GetFullName()), players[i]->GetAttributes()->GetDbId(), TCHAR_TO_UTF8(*ExternalRateString));

		UE_LOG(LogNetwork, Warning, TEXT("SIFGameWorld::LogAndCheckNetworkState step: %d, Player ID:  %u, X: %.20f, Y: %.20f, Z: %.20f"), step_count, players[i]->GetAttributes()->GetDbId(), PlayerPosition.X, PlayerPosition.Y, PlayerPosition.Z);
		UE_LOG(LogNetwork, Warning, TEXT("SIFGameWorld::LogAndCheckNetworkState step: %d, Player ID:  %u, External Rating String: %s"), step_count,  players[i]->GetAttributes()->GetDbId(), *ExternalRateString);
		
		// FString posString = FString::Printf(TEXT("ID: %u, Role: %s, X: %f, Y: %f, Z: %f"), players[i]->GetFullName(), players[i]->GetAttributes()->GetDbId(), UTF8_TO_TCHAR(players[i]->GetRole()->GetShortClassName()), PlayerPosition.X, PlayerPosition.Y, PlayerPosition.Z);
		// MabString posMabString = SIFGameHelpers::GAConvertFStringToMabString(posString);
#if defined(ENABLE_SYNCD_RAND_TRACKING) && WRITE_POS_TO_FILE
		 // pos_file << posMabString;
		 // pos_file << "\n";
#endif
	}

	FVector CurrentBallPosition = ball->GetCurrentPosition();
	// round up to 0.1f
	CurrentBallPosition.X = FMath::RoundToFloat(CurrentBallPosition.X * 10.0f) / 10.0f;
	CurrentBallPosition.Y = FMath::RoundToFloat(CurrentBallPosition.Y * 10.0f) / 10.0f;
	CurrentBallPosition.Z = FMath::RoundToFloat(CurrentBallPosition.Z * 10.0f) / 10.0f;

	wwNETWORK_TRACE_JG("Ball Pos for step %d: X: %.20f, Y: %.20f, Z: %.20f", step_count, CurrentBallPosition.X, CurrentBallPosition.Y, CurrentBallPosition.Z);
	
	// FString ballString = FString::Printf(TEXT("Ball Pos for step %d: X: %.20f, Y: %.20f, Z: %.20f"), step_count, CurrentBallPosition.X, CurrentBallPosition.Y, CurrentBallPosition.Z);
#if defined(ENABLE_SYNCD_RAND_TRACKING) && WRITE_POS_TO_FILE
	// pos_file << ballMabString;
	// pos_file << "\n";
#endif

	Writer << CurrentBallPosition;
	UE_LOG(LogNetwork, Warning, TEXT("SIFGameWorld::LogAndCheckNetworkState Ball Pos for step %d: X: %.20f, Y: %.20f, Z: %.20f"), step_count, CurrentBallPosition.X, CurrentBallPosition.Y, CurrentBallPosition.Z);

	MabUInt32 CurrentSeed = GetRNG()->GetSeed();
	UE_LOG(LogNetwork, Warning, TEXT("SIFGameWorld::LogAndCheckNetworkState Current Seed: %u"), CurrentSeed);
	Writer << CurrentSeed;

	if (lastTimeStep != step_count)
	{
		uint32 hash = FCrc::MemCrc32(Buffer.GetData(), Buffer.Num());

		wwNETWORK_TRACE_JG_ALWAYS("Hash for this step: %u", hash);

		if (network_state)
		{
			UE_LOG(LogNetwork, Display, TEXT("SIFGameWorld::UpdateSimulation Registering step hash: %u for step: %d, delta time: %f"), hash, step_count, delta_time);
			network_state->SendHash(step_count, hash);
		}

		lastTimeStep = step_count;
	}
	else
	{
		UE_LOG(LogNetwork, Display, TEXT("SIFGameWorld::LogAndCheckNetworkState Duplicate step count detected for step: %d"), lastTimeStep);
	}
}

void SIFGameWorld::ShowDebugNetworkInfo()
{
#if ENABLE_NETWORK_FRAME_INFO

	static int32 net_frame_counter = 0;
	static int32 net_frame_counter_per_second = 0;
	static int32 net_frame_counter_per_minute = 0;

	static int32 net_network_frame_counter = 0;
	static int32 net_network_frame_counter_per_second = 0;
	static int32 net_network_frame_counter_per_minute = 0;

	static double net_ping_counter_per_second = 0.0;
	static double net_ping_counter_per_minute = 0.0;

	static double net_frame_count_start_time = FPlatformTime::Seconds();
	static double net_per_second_start_time = FPlatformTime::Seconds();
	static double net_per_minute_start_time = FPlatformTime::Seconds();

	static int32 net_halt_counter = 0;
	static int32 net_halt_counter_per_second = 0;
	static int32 net_halt_counter_per_minute = 0;

	net_frame_counter++;
	net_frame_counter_per_second++;
	net_frame_counter_per_minute++;

	//---- FPS
	{
		FString NetAvgFPSString = "Avg FPS:" + FString::FromInt(net_frame_counter / (FPlatformTime::Seconds() - net_frame_count_start_time));
		GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Green, NetAvgFPSString);

		FString NetFPSString = "FPS:" + FString::FromInt(net_frame_counter_per_second / (FPlatformTime::Seconds() - net_per_second_start_time));
		GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Green, NetFPSString);

		FString NetFPMString = "FPM:" + FString::FromInt(net_frame_counter_per_minute / (FPlatformTime::Seconds() - net_per_minute_start_time));
		GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Green, NetFPMString);
	}

	// In game info.
	if (game_settings.game_settings.network_game)
	{
		//Run simulation update
		if (should_update_sim || bDoMatchOverSim)
		{
			net_network_frame_counter++;
			net_network_frame_counter_per_second++;
			net_network_frame_counter_per_minute++;
		}
		else
		{
			net_halt_counter++;
			net_halt_counter_per_second++;
			net_halt_counter_per_minute++;
		}

		//---- Network FPS
		FString NetAvgFPSString = "Network Avg FPS:" + FString::FromInt(net_network_frame_counter / (FPlatformTime::Seconds() - net_frame_count_start_time)) + "\n";
		GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Black, NetAvgFPSString);

		FString NetFPSString = "Network FPS:" + FString::FromInt(net_network_frame_counter_per_second / (FPlatformTime::Seconds() - net_per_second_start_time));
		GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Black, NetFPSString);

		FString NetFPMString = "Network FPM:" + FString::FromInt(net_network_frame_counter_per_minute / (FPlatformTime::Seconds() - net_per_minute_start_time));
		GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Black, NetFPMString);

		//---- Halt Rate
		float halt_amount = (float)net_halt_counter / (float)FMath::Max(net_frame_counter, 1);
		FString NetHaltPercent = FString::Printf(TEXT("Network Avg Halt Percent: %0.2f%%\n"), halt_amount * 100.0f);
		GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Yellow, NetHaltPercent);

		halt_amount = (float)net_halt_counter_per_second / (float)FMath::Max(net_frame_counter_per_second, 1);
		NetHaltPercent = FString::Printf(TEXT("Network Halt Percent Per Second: %0.2f%%"), halt_amount * 100.0f);
		GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Yellow, NetHaltPercent);

		halt_amount = (float)net_halt_counter_per_minute / (float)FMath::Max(net_frame_counter_per_minute, 1);
		NetHaltPercent = FString::Printf(TEXT("Network Halt Percent Per Minute: %0.2f%%"), halt_amount * 100.0f);
		GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Yellow, NetHaltPercent);
	}

	MabNetworkManager* pNetworkManager = SIFApplication::GetApplication()->GetNetworkManager();

	if (pNetworkManager)
	{
		//---- Ping
		MabNetTime rtt = pNetworkManager->GetRoundTripTime();

		net_ping_counter_per_second += rtt;
		net_ping_counter_per_minute += rtt;

		FString NetPing = FString::Printf(TEXT("\nNetwork Ping: %f"), rtt * 1000.0f);
		GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Purple, NetPing);

		double avg_ping = net_ping_counter_per_second / FMath::Max(net_frame_counter_per_second, 1);
		NetPing = FString::Printf(TEXT("Network Ping Per Second: %f"), avg_ping * 1000.0f);
		GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Purple, NetPing);

		avg_ping = net_ping_counter_per_minute / FMath::Max(net_frame_counter_per_minute, 1);
		NetPing = FString::Printf(TEXT("Network Ping Per Minute: %f"), avg_ping * 1000.0f);
		GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Purple, NetPing);

		//---- Reset Triggers
		if ((FPlatformTime::Seconds() - net_per_second_start_time) > 1.0)
		{
			net_frame_counter_per_second = 0;
			net_halt_counter_per_second = 0;
			net_per_second_start_time = FPlatformTime::Seconds();
			net_ping_counter_per_second = 0.0;
			net_network_frame_counter_per_second = 0;
		}

		if ((FPlatformTime::Seconds() - net_per_minute_start_time) > 60.0)
		{
			net_frame_counter_per_minute = 0;
			net_halt_counter_per_minute = 0;
			net_per_minute_start_time = FPlatformTime::Seconds();
			net_ping_counter_per_minute = 0.0;
			net_network_frame_counter_per_minute = 0;
		}

		if (pNetworkManager->GetTransport())
		{
			const MabTransportStats* pTransportStats = pNetworkManager->GetTransport()->GetTransportStats();

			if (pTransportStats)
			{
				FString BytesIn = FString::Printf(TEXT("Network Bytes In Per Second: %u"), pTransportStats->per_second.bytes_in);
				GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Cyan, BytesIn);

				FString BytesOut = FString::Printf(TEXT("Network Bytes Out Per Second: %u"), pTransportStats->per_second.bytes_out);
				GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Cyan, BytesOut);
			}

			if (Cast<UUnrealTransportReliable>(pNetworkManager->GetTransport()))
			{
				GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Red, Cast<UUnrealTransportReliable>(pNetworkManager->GetTransport())->GetRoundTripInfoAsString());
			}

			if (network_state)
			{
				GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Orange, network_state->GetStateAsString());
			}
		}
	}
#endif
}

///-------------------------------------------------------------------------------
/// Get the file name for the ball - based on teams playing/competition etc...
///-------------------------------------------------------------------------------
FString SIFGameWorld::GetBallFileName(int teamID)
{
	const FString BALL_GENERIC = FString("m_ball_generic");

	// International team specific overrides
	const FString BALL_ALLBLACKS = FString("m_ball_allblacks");
	const FString BALL_WALLABIES = FString("m_ball_wallabies");
	const FString BALL_ENGLAND = FString("m_ball_eng");
	const FString BALL_SPRINGBOKS = FString("m_ball_saf");

	// Generic international
	const FString BALL_INTERNATIONAL = FString("m_ball_int");

	// Super rugby
	const FString BALL_SUPER15 = FString("m_ball_sru");
	const FString BALL_SUPER15_AU = FString("m_ball_sru_au");
	const FString BALL_SUPER15_NZ = FString("m_ball_sru_nz");
	const FString BALL_SUPER15_SA = FString("m_ball_sru_sa");

	// Various world wide comps
	const FString BALL_WORLDCUP = FString("m_ball_wc");
	const FString BALL_QUADNATIONS = FString("m_ball_quad");
	//const FString BALL_TRINATIONS_NZ = FString("ball_trinations_nz");
	const FString BALL_PAC = FString("m_ball_pac");

	// Northern hemispehere
	const FString BALL_AVIVA = FString("m_ball_avi");
	const FString BALL_TOP14 = FString("m_ball_fr1");
	const FString BALL_PROD2 = FString("m_ball_fr2");
	const FString BALL_ELITE_14 = FString("m_ball_eur");
	const FString BALL_ENA = FString("m_ball_ena");
	const FString BALL_ERU = FString("m_ball_eru");
	const FString BALL_ENC = FString("m_ball_enc");

	// Southern hemisphere
	const FString BALL_CURRIE_CUP = FString("m_ball_cur");
	const FString BALL_ITM = FString("m_ball_mit");
	const FString BALL_SAC = FString("m_ball_sac");
	const FString BALL_AFC = FString("m_ball_afc");
	const FString BALL_AUC = FString("m_ball_auc");

	// Sevens
	const FString BALL_7C = FString("m_ball_7c");
	const FString BALL_7S = FString("m_ball_7s");

	// Not used
	const FString BALL_LIONS = FString("m_ball_lions");


	RL3DatabaseScopedLock	database_lock;			// Lock database.

	const RUCareerModeManager* const career_manager = SIFApplication::GetApplication()->GetCareerModeManager();
	RUActiveCompetitionBase* active_competition = NULL;

	if (career_manager && career_manager->IsActive() && career_manager->GetPrimaryCompDefinitionId() != 0)
	{
		active_competition = career_manager->GetActiveCompetition();
	}

	FString ball_file_name = BALL_GENERIC;

	const RUGameDatabaseManager* const game_database_mgr = SIFApplication::GetApplication()->GetGameDatabaseManager();
	if (game_database_mgr == NULL)
	{
		MABBREAK();
		return FString();
	}

	if (IsMatch())
	{
		/// If both teams are specific to a particular competition, then use that competitions ball.

		int  country_id = stadium_manager->GetStadiumCountryDbId();
		unsigned short competition_id = this->pseudo_competition_id;

		/// If both teams are specific to a particular competition, then use that competitions ball.

		unsigned short team_id[2];
		bool at_home[2];
		bool is_international[2];
		bool is_super15[2];

		for (int team_idx = 0; team_idx < 2; team_idx++)
		{
			team_id[team_idx] = GetGameSettings().team_settings[team_idx].team.GetDbId();
			at_home[team_idx] = stadium_manager->IsTeamAtHomeStadium(team_id[team_idx]);
			RUDB_TEAM::GetTeamType(team_id[team_idx], &is_international[team_idx], &is_super15[team_idx]);
		}


		MABLOGDEBUG("Ball competition Id = %d", competition_id);
		MABLOGDEBUG("Ball country Id = %d", country_id);

		switch (competition_id)
		{
			case DB_COMPID_WORLDCUP:
			{
				ball_file_name = BALL_WORLDCUP;
				break;
			}
			case DB_COMPID_SUPER15:
			{
				switch (country_id)
				{
					case DB_COUNTRYID_AUSTRALIA:
					ball_file_name = BALL_SUPER15_AU;
					break;
					case DB_COUNTRYID_NEWZEALAND:
					ball_file_name = BALL_SUPER15_NZ;
					break;
					case DB_COUNTRYID_SOUTHAFRICA:
					ball_file_name = BALL_SUPER15_SA;
					break;

					default:
					ball_file_name = BALL_SUPER15;
					break;
				}
				break;
			}
			case DB_COMPID_QUADNATIONS:
			{
				if (team_id[0] == DB_TEAMID_NZ)
				{
					ball_file_name = BALL_ALLBLACKS;
				}
				else if (team_id[0] == DB_TEAMID_AUSTRALIA)
				{
					ball_file_name = BALL_WALLABIES;
				}
				//else if (team_id[0] == DB_TEAMID_SOUTHAFRICA)
				//{
				//	ball_file_name = BALL_SPRINGBOKS;
				//}
				else
				{
					ball_file_name = BALL_QUADNATIONS;
				}
				break;
			}
			case DB_COMPID_EURONATIONS:
			{
				ball_file_name = BALL_ENA;
				break;
			}
			case DB_COMPID_EURO_RUGBY_CLUB:
			{
				ball_file_name = BALL_ERU;
				break;
			}
			case DB_COMPID_AVIVA:
			{
				ball_file_name = BALL_AVIVA;
				break;
			}
			/*case DB_COMPID_TOP14:
			{
				ball_file_name = BALL_TOP14;
				break;
			}*/
			case DB_COMPID_RABODIRECTPRO: //elite 14
			{
				ball_file_name = BALL_ELITE_14;
				break;
			}
			case DB_COMPID_PRO_D2:
			{
				ball_file_name = BALL_PROD2;
				break;
			}
			case DB_COMPID_EURONATIONS_CUP:
			{
				ball_file_name = BALL_ENC;
				break;
			}
			case DB_COMPID_PACIFIC_CUP:
			{
				ball_file_name = BALL_PAC;
				break;
			}
			case DB_COMPID_AFRICAN_CUP:
			{
				ball_file_name = BALL_AFC;
				break;
			}
			case DB_COMPID_SOUTH_AMERICAN_CUP:
			{
				ball_file_name = BALL_SAC;
				break;
			}
			case DB_COMPID_SEVENS:
			{
				ball_file_name = BALL_7C;
				break;
			}
			case DB_COMPID_SEVENS_SERIES:
			{
				ball_file_name = BALL_7S;
				break;
			}
			case DB_COMPID_ACC:
			{
				ball_file_name = BALL_CURRIE_CUP;
				break;
			}
			case DB_COMPID_NRC:
			{
				ball_file_name = BALL_AUC;
				break;
			}
			case DB_COMPID_ITM:
			{
				ball_file_name = BALL_ITM;
				break;
			}
			case DB_COMPID_LIONS_TOUR:
			case DB_COMPID_ENDOFYEAR_TOUR:
			default:
			if (is_international[0] && is_international[1])
			{	// Both teams international.
				// removed INTERNATIONAL ball as per ticket #1944 requested
				// ball_file_name = BALL_INTERNATIONAL;
			}
			break;
		}

		/// Override ball if not in competition + allblacks/wallabies at home.

		if (active_competition == NULL)
		{
			/// All Blacks at home.
			if ((team_id[0] == DB_TEAMID_NZ && at_home[0]) || (team_id[1] == DB_TEAMID_NZ && at_home[1]))
			{
				ball_file_name = BALL_ALLBLACKS;
			}
			/// Wallabies at home.
			if ((team_id[0] == DB_TEAMID_AUSTRALIA && at_home[0]) || (team_id[1] == DB_TEAMID_AUSTRALIA && at_home[1]))
			{
				ball_file_name = BALL_WALLABIES;
			}
			/// Wallabies at home.
			//if ((team_id[0] == DB_TEAMID_SOUTHAFRICA && at_home[0]) || (team_id[1] == DB_TEAMID_SOUTHAFRICA && at_home[1]))
			//{
			//	ball_file_name = BALL_SPRINGBOKS;
			//}
		}

		/// Override to Lions ball if at any point Lions playing Australia - request of HES
		//bool is_lions_vs_australia = (team_id[0] == DB_TEAMID_AUSTRALIA && team_id[1] == DB_TEAMID_LIONS) || (team_id[1] == DB_TEAMID_AUSTRALIA && team_id[0] == DB_TEAMID_LIONS);
		//if (is_lions_vs_australia)
		//	ball_file_name = BALL_LIONS;

		// Dewald WW -
		// Override to England, ALL home matches for England (@Twickenham) needs to use this ball - request of HES
		if ((team_id[0] == DB_TEAMID_ENGLAND && at_home[0]) || (team_id[1] == DB_TEAMID_ENGLAND && at_home[1]))
		{
			ball_file_name = BALL_ENGLAND;
		}
		//if ((team_id[0] == DB_TEAMID_ENGLAND_R7 && at_home[0]) || (team_id[1] == DB_TEAMID_ENGLAND_R7 && at_home[1]))
		//{
		//	ball_file_name = BALL_ENGLAND;
		//}
	}
	else
	{	/// Sandbox or Menu world.

		// Based on what team is set to the favourite in teh frontend

		//	Any ITM team = ITM ball
		//	Any Aviva team = Aviva ball
		//	Any Top14 team = Top14 ball
		//	Super 15 Au teams = Super15Au ball
		//	Super 15 NZ teams = super15NZ ball
		//	Super 15 SA teams = super15SA ball

		//	Australia = au ball
		//	NZ = all blacks ball
		//	ALL OTHER INTERNATIONALS = worldcup ball

		//	Any other team = genericball (lomu allstars, custom teams, team lomu, Magners league, etc)

		unsigned short team_id = GetGameSettings().team_settings[SIDE_A].team.GetDbId();

		if (teamID > 0) //if someone forces to load ball for a particular team, ex: competition screen is loaded, in the cutscene the team doesot change but the strip changes, so this logic would match ball with SIDE A of the strip.
			team_id = teamID;

		/// All Blacks at home.
		if (team_id == DB_TEAMID_NZ)
		{
			ball_file_name = BALL_ALLBLACKS;
		}
		else if (team_id == DB_TEAMID_AUSTRALIA)
		{
			ball_file_name = BALL_WALLABIES;
		}
		else if (team_id == DB_TEAMID_ENGLAND)
		{
			ball_file_name = BALL_ENGLAND;
		}
		//else if (team_id == DB_TEAMID_SOUTHAFRICA)
		//{
		//	ball_file_name = BALL_SPRINGBOKS;
		//}
		else
		{
			MabVector<unsigned short> team0_comps;
			GetTeamCompetitions(team_id, team0_comps, *game_database_mgr);

			if (team0_comps.size() > 0)
			{
				int competition_id = team0_comps[0];
				MABLOGDEBUG("Ball competition Id = %d", competition_id);

				switch (competition_id)
				{
					case DB_COMPID_ITM:
					ball_file_name = BALL_ITM;
					break;

					case DB_COMPID_AVIVA:
					ball_file_name = BALL_AVIVA;
					break;

					/*case DB_COMPID_TOP14:
					ball_file_name = BALL_TOP14;
					break;
					*/
					case DB_COMPID_PRO_D2:
					ball_file_name = BALL_PROD2;
					break;

					case DB_COMPID_RABODIRECTPRO: //elte14
					ball_file_name = BALL_ELITE_14;
					break;

					case DB_COMPID_SUPER15:
					{
						short country_id = -1;
						short home_stadium_id = -1;
						short city_id = -1;

						/// Work out teams home country...

						MabString statement = MabString(0, "SELECT stadium_id FROM RUDB_HOME_STADIUM WHERE team_id=%d", team_id);
						SqliteMabStatement result;

						if (game_database_mgr->RawProcessSqlStatement(result, statement.c_str()))
						{
							while (result.Next())
							{
								home_stadium_id = result.GetColumnAsUShort(0);
								break;
							}
						}

						if (home_stadium_id != -1)
						{
							statement = MabString(0, "SELECT city_id FROM RUDB_STADIUM WHERE id=%d", home_stadium_id);

							if (game_database_mgr->RawProcessSqlStatement(result, statement.c_str()))
							{
								while (result.Next())
								{
									city_id = result.GetColumnAsUShort(0);
									break;
								}
							}
						}

						if (city_id != -1)
						{
							statement = MabString(0, "SELECT country_id FROM RUDB_CITY WHERE id=%d", city_id);

							if (game_database_mgr->RawProcessSqlStatement(result, statement.c_str()))
							{
								while (result.Next())
								{
									country_id = result.GetColumnAsUShort(0);
									break;
								}
							}
						}

						switch (country_id)
						{
							case DB_COUNTRYID_AUSTRALIA:
							ball_file_name = BALL_SUPER15_AU;
							break;
							case DB_COUNTRYID_NEWZEALAND:
							ball_file_name = BALL_SUPER15_NZ;
							break;
							case DB_COUNTRYID_SOUTHAFRICA:
							ball_file_name = BALL_SUPER15_SA;
							break;

							default:
							ball_file_name = BALL_SUPER15;
							break;
						}
					}
					break;

					case DB_COMPID_WORLDCUP:
					ball_file_name = BALL_WORLDCUP;
					break;

					case DB_COMPID_ACC:
					ball_file_name = BALL_CURRIE_CUP;
					break;

					default:
					break;
				}
			}

		}
	}

	// MaterialInstanceConstant'/Game/Rugby/cmn_con/prop/ball/material/m_ball_avi.m_ball_avi'
	return FString::Printf(TEXT("/Game/Rugby/cmn_con/prop/ball/material/%s.%s"), *ball_file_name, *ball_file_name);
}

///-------------------------------------------------------------------------------
/// Setup the wind.
///-------------------------------------------------------------------------------

void SIFGameWorld::SetupWind(FVector direction, float wind_strength)
{
	// the wind_amount is an arbitrary parameter, and we have it buffed so it will convert the km/h
	// into a sensible m/s value (thus it is at a minimum 0.27777777 - 1000/3600 km/h to m/s)
	// and we have a bit extra to control the amount of effect the wind has.

	const float MIN_WIND_EFFECT = 5; //  the minimum wind velocity to have much of a wind effect
	const float MAX_WIND_EFFECT = 30; // wind velocity above which the wind just has a fixed coefficient effect

	const float MIN_WIND_COEFF = 0.22f; // coefficient to use at MIN_WIND_EFFCT
	const float MAX_WIND_COEFF = 0.35f; // coefficient to use at MAX_WIND_EFFECT

	float percent_between_coeff = (wind_strength - MIN_WIND_EFFECT) / (MAX_WIND_EFFECT - MIN_WIND_EFFECT);
	MabMath::Clamp(percent_between_coeff, 0.0f, 1.0f);

	MabMath::Clamp(wind_strength, MIN_WIND_EFFECT, MAX_WIND_EFFECT);
	FVector wind_velocity = direction * wind_strength;

	if (UOBJ_IS_VALID(ball))
	{
		ball->GetExtrapolationParameters().wind_velocity = wind_velocity;
		ball->GetExtrapolationParameters().wind_amount = (MAX_WIND_COEFF - MIN_WIND_COEFF) * percent_between_coeff + MIN_WIND_COEFF;
	}
}

#ifdef ENABLE_NETWORK_STATE_CHECKER
void SIFGameWorld::PopulateNetworkStateCheck()
{
	NET_STATE_CHECK_SCOPE(world);

	MabUInt32 rnd = GetRNG()->GetSeed();
	NET_STATE_CHECK_WRITE(rnd);
	NET_STATE_CHECK_CHILD(ball);

	for (SIFRugbyCharacterList::iterator iter = players.begin(); iter != players.end(); iter++)
	{
		NET_STATE_CHECK_CHILD(*iter);
	}
}
#endif

///-------------------------------------------------------------------------------
/// AddToScene
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
PSSG::PNode* SIFGameWorld::AddToScene(const char *resource_name, unsigned int light_link_mask, SIFPSSGUtil::SIFPSSGSceneCloner* cloner)
{
	PSSG::PDatabaseWriteLock env_lock(this->GetSceneDatabaseId());
	PSSG::PDatabase* env_database = env_lock.getDatabase();

	PSSG::PListIterator scenes(env_database->getSceneList());

	PSSGMabDatabaseResourceFile* resource = MabCast<PSSGMabDatabaseResourceFile>(MabGlobalResourceSet::FetchResource(resource_name));
	//MABLOGDEBUG("AddToScene: %s -> resource = 0x%p", resource_name, resource);
	MABASSERT(resource);
	if (!resource)
		return NULL;

	PSSG::PDatabaseWriteLock resource_lock(resource->GetDatabaseId());
	PSSG::PDatabase* database = resource_lock.getDatabase();

	PSSG::PDatabaseListableIterator<PSSG::PRootNode> it(*database);

	return AddToScene(&*it, light_link_mask, cloner);
}

PSSG::PNode* SIFGameWorld::AddToScene(const char *resource_name, PSSG::PNode* parent, unsigned int light_link_mask, SIFPSSGUtil::SIFPSSGSceneCloner* cloner)
{
	PSSG::PDatabaseWriteLock env_lock(this->GetSceneDatabaseId());
	PSSG::PDatabase* env_database = env_lock.getDatabase();

	PSSG::PListIterator scenes(env_database->getSceneList());

	PSSGMabDatabaseResourceFile* resource = MabCast<PSSGMabDatabaseResourceFile>(MabGlobalResourceSet::FetchResource(resource_name));
	//MABLOGDEBUG("AddToScene: %s -> resource = 0x%p", resource_name, resource);
	MABASSERT(resource);
	if (!resource)
		return NULL;

	PSSG::PDatabaseWriteLock resource_lock(resource->GetDatabaseId());
	PSSG::PDatabase* database = resource_lock.getDatabase();

	PSSG::PDatabaseListableIterator<PSSG::PRootNode> it(*database);

	return AddToScene(&*it, parent, light_link_mask, cloner);
}

PSSG::PNode* SIFGameWorld::AddToScene(PSSG::PNode* asset, unsigned int light_link_mask, SIFPSSGUtil::SIFPSSGSceneCloner* cloner)
{
	PSSG::PDatabaseWriteLock env_lock(this->GetSceneDatabaseId());
	PSSG::PDatabase* env_database = env_lock.getDatabase();

	PSSG::PListIterator scenes(env_database->getSceneList());

	PSSG::PNode* graphics_node = NULL;
	if (cloner)
	{
		cloner->SetDatabases(&asset->getDBase(), env_database);
		graphics_node = SIFPSSGUtil::DowngradedClone<PSSG::PNode>(asset, env_database, *cloner);
	}
	else
	{
		graphics_node = SIFPSSGUtil::DowngradedClone<PSSG::PNode>(asset, env_database);
	}

	DeferredAddChild((PSSG::PRootNode*)scenes.data(), graphics_node, light_link_mask);

	return graphics_node;
}

PSSG::PNode* SIFGameWorld::AddToScene(PSSG::PNode* asset, PSSG::PNode* parent, unsigned int light_link_mask, SIFPSSGUtil::SIFPSSGSceneCloner* cloner)
{
	PSSG::PDatabaseWriteLock env_lock(this->GetSceneDatabaseId());
	PSSG::PDatabase* env_database = env_lock.getDatabase();

	PSSG::PNode* graphics_node = NULL;
	if (cloner)
	{
		cloner->SetDatabases(&asset->getDBase(), env_database);
		graphics_node = SIFPSSGUtil::DowngradedClone<PSSG::PNode>(asset, env_database, *cloner);
	}
	else
	{
		graphics_node = SIFPSSGUtil::DowngradedClone<PSSG::PNode>(asset, env_database);
	}

	DeferredAddChild(parent, graphics_node, light_link_mask);

	return graphics_node;
}

void SIFGameWorld::RemoveFromScene(PSSG::PNode* node)
{
	// Add creates so should remove destroy?
	DeferredFree(node);
}

PSSG::PRootNode* SIFGameWorld::GetSceneRootNode() const
{
	PSSG::PDatabaseWriteLock env_lock(this->GetSceneDatabaseId());
	PSSG::PDatabase* env_database = env_lock.getDatabase();
	PSSG::PListIterator scenes(env_database->getSceneList());
	return (PSSG::PRootNode*)scenes.data();
}
#endif

///-------------------------------------------------------------------------------
/// Add A prop to the game world
///-------------------------------------------------------------------------------


AActor* SIFGameWorld::AddProp(const FVector &_position, const FVector &_rotation, const char *resource_name, unsigned int light_link_mask)
{
	//#RC3_legacy_prop
	//PSSG::PNode* graphics_node = AddToScene( resource_name, light_link_mask );
	//// Create the game object
	//SIFGameObject* object = object_database->Allocate<SIFGameObject>( this, MabMemGetDefaultObjectHeap(this) );
	//object->SetGraphicsObject( graphics_node );

	//if ( !object )
	//{
	//	MABLOGDEBUG("Adding prop failed resource %s", resource_name);
	//	return NULL;
	//}

	//MabMatrix pos = MabMatrix::TransMatrix(_position);
	//MabMatrix rot = MabMatrix::RotMatrix( _rotation.x, _rotation.y, _rotation.z );
	//MabMatrix transform = pos * rot;
	//object->SetTransform(transform);

	//props.push_back( object );
	//return object;

	return nullptr;
}


///-------------------------------------------------------------------------------
/// Remove prop to the game world
///-------------------------------------------------------------------------------

void SIFGameWorld::RemoveProp(AActor* object)
{
	//SIFRugbyPropList::const_iterator iter = props.begin();

	//for ( ; iter != props.end(); ++iter )
	//{
	//	if ( (*iter) == object )
	//	{
	//		props.erase( iter );
	//		break;
	//	}
	//}

#if 0 //#rc3_legacy
	SIFRugbyPropList::iterator iter = std::find(props.begin(), props.end(), object);

	MABASSERT(iter != props.end());
	if (iter != props.end())
	{
		props.erase(iter);
		object->Destroy();
	}
#endif
}

///-------------------------------------------------------------------------------
/// Attempt to ReSync net game via data received
///-------------------------------------------------------------------------------
void SIFGameWorld::NetLoadReSync(SSStreamPacker& data)
{
	random_number_generator->SetAssertIfUsed(false);

	MABLOGDEBUG("NetLoadResync!");

	APlayerController* PC = SIFApplication::GetApplication()->GetMasterPlayerController();

	PC->ConsoleCommand("Log LogTemp on", true);
	PC->ConsoleCommand("Log LogTemp Display", true);

	// Set seed first
	MabUInt32 random_seed;
	data.Unpack(random_seed);
	random_number_generator->SetSeed(random_seed);

	// Restart game
	// This function in RC3 doesn't do anything.
	//NetworkHardReset();
	//RestartGame();

	// Set the time stuff
	float time = 0.0f;
	MabUInt32 step = 0;
	const MabTime delta(SIMULATION_INTERVAL);
	data.Unpack(time);
	data.Unpack(step);
	simulation_time->SetCurrentStep(MabTimeStep(time, delta, step));
	//if (game_settings.game_settings.network_game) simulation_time->Pause(true); //Just in case

	auto UnpackPlayer = [&data, step, this](ARugbyCharacter* current_player)
	{
		FVector UnpackedPosition;
		data.Unpack(UnpackedPosition);
		current_player->GetMovement()->SetCurrentPosition(UnpackedPosition);

		FVector UnpackedVelocity;
		data.Unpack(UnpackedVelocity);
		current_player->GetMovement()->SetCurrentVelocity(UnpackedVelocity);

		current_player->GetMovement()->SetTargetPosition(FVector(0.0f), true);

		current_player->GetState()->SetParticipationLevel((PARTICIPATION_LEVEL)0);
		current_player->GetState()->SetParticipationRating(0.0f);

		current_player->GetAnimation()->Reset();

		current_player->GetAttributes()->Reset();

		current_player->ClearAnimNotifyQueue();

		MabMatrix m;

		uint32 step_int32 = (uint32)step;

		if (current_player->GetAnimation()->GetBallTransform(m))
		{
			FVector to_translation = m.GetTranslation();
			//wwNETWORK_TRACE_JG("SIFGameWorld::NetLoadReSync Player ID: %d, X: %f, Y: %f, Z: %f", current_player->GetAttributes()->GetDbId(), to_translation.X, to_translation.Y, to_translation.Z);
		}
		UE_LOG(LogNetworkNonGame, Warning, TEXT("SIFGameWorld::NetLoadReSync step: %d, World Pos Before: Player ID: %d, X: %f, Y: %f, Z: %f"), step_int32, current_player->GetAttributes()->GetDbId(), current_player->GetActorLocation().X, current_player->GetActorLocation().Y, current_player->GetActorLocation().Z);

		const FVector worldPosition = TransformUtility::ConvertScaledToUnreal(current_player->GetMovement()->GetCurrentPosition());
		current_player->SetActorLocation(worldPosition);
		current_player->GetAnimation()->PostAnimationUpdate(*game_context, 0.0f);
		UE_LOG(LogNetworkNonGame, Warning, TEXT("SIFGameWorld::NetLoadReSync step: %d, World Pos After: Player ID: %d, X: %f, Y: %f, Z: %f"), step_int32, current_player->GetAttributes()->GetDbId(), current_player->GetActorLocation().X, current_player->GetActorLocation().Y, current_player->GetActorLocation().Z);
		if (current_player->GetAnimation()->GetBallTransform(m))
		{
			FVector to_translation = m.GetTranslation();
			//wwNETWORK_TRACE_JG("SIFGameWorld::NetLoadReSync Player ID: %d, X: %f, Y: %f, Z: %f", current_player->GetAttributes()->GetDbId(), to_translation.X, to_translation.Y, to_translation.Z);
		}
	};

	//Team data
	for (size_t i = 0; i < teams.size(); ++i)
	{
		int play_dir;
		bool is_attacking;
		data.Unpack(play_dir);
		data.Unpack(is_attacking);
		teams[i]->SetPlayDirection((ERugbyPlayDirection)play_dir);
		RUTeam* dumb_pointer_team = teams[i].get();
		if (is_attacking) game_state->SetAttackingTeam(dumb_pointer_team);

		for (auto& current_player : teams[i]->players)
		{
			UnpackPlayer(current_player);
		}
	}

	for (auto& current_player : officials->players)
	{
		UnpackPlayer(current_player);
	}

	UWorld* YourGameWorld = SIFApplication::GetApplication()->GetWorld(); // Set this somehow from another UObject or pass it in as an argument or parameter

	for (TObjectIterator<USkeletalMeshComponent> Itr; Itr; ++Itr)
	{
		// Filter out objects not contained in the target world.
		if (Itr->GetWorld() != YourGameWorld)
		{
			continue;
		}

		USkeletalMeshComponent* pComponent = *Itr;


		pComponent->bEnableUpdateRateOptimizations = false;
		pComponent->EnableExternalUpdate(true);
		pComponent->SetExternalDeltaTime(0.0f);

		pComponent->TickPose(0.0f, false);
		pComponent->TickAnimation(0.0f, false);
		pComponent->RefreshBoneTransforms(nullptr);
	}

	//Team data
	for (size_t i = 0; i < teams.size(); ++i)
	{
		for (auto& current_player : teams[i]->players)
		{
			MabMatrix m;

			if (current_player->GetAnimation()->GetBallTransform(m))
			{
				FVector to_translation = m.GetTranslation();
				//wwNETWORK_TRACE_JG("SIFGameWorld::NetLoadReSync Player ID: %d, X: %f, Y: %f, Z: %f", current_player->GetAttributes()->GetDbId(), to_translation.X, to_translation.Y, to_translation.Z);
			}
		}
	}
	//check(false);  // GG WW AFL RUGBY ERICTOTEST why????
	

	//Ball Position
	FVector ball_pos;
	data.Unpack(ball_pos);

	wwNETWORK_TRACE_JG("SIFGameWorld::NetLoadReSync Ball Pos: X: %f, Y: %f, Z: %f", ball_pos.X, ball_pos.Y, ball_pos.Z);
	UE_LOG(LogNetworkNonGame, Warning, TEXT("SIFGameWorld::NetLoadReSync step: %d, Ball Pos in resync packet:  X: %f, Y: %f, Z: %f"), (uint32)step, ball_pos.X, ball_pos.Y, ball_pos.Z);

	if (UOBJ_IS_VALID(ball))
	{
		ball->SetPosition(ball_pos);
		UE_LOG(LogNetworkNonGame, Warning, TEXT("SIFGameWorld::NetLoadReSync step: %d, Ball Pos after setPosition:  X: %f, Y: %f, Z: %f"), (uint32)step, ball->GetCurrentPosition().X, ball->GetCurrentPosition().Y, ball->GetCurrentPosition().Z);

		// GetSpatialHelper()->ClampWaypointToSideLine(ball_pos, 5.0f);
		// GetSpatialHelper()->ClampWaypointToTryLine(ball_pos, 5.0f);

		MabMatrix final_transform = MabMatrix::IDENTITY;
		ball->SetMabTransform(final_transform);
		UE_LOG(LogNetworkNonGame, Warning, TEXT("SIFGameWorld::NetLoadReSyncstep: %d, Ball Pos After SetMabTransform:  X: %f, Y: %f, Z: %f"), (uint32)step, ball->GetCurrentPosition().X, ball->GetCurrentPosition().Y, ball->GetCurrentPosition().Z);

		ball->SetBallAttachTime(0.0f, 0.0f);
		//UE_LOG(LogNetworkNonGame, Warning, TEXT("SIFGameWorld::NetLoadReSyncstep: %d, Ball Pos After SetBallAttachTime:  X: %f, Y: %f, Z: %f"), (uint32)step, ball->GetCurrentPosition().X, ball->GetCurrentPosition().Y, ball->GetCurrentPosition().Z);

	}

	// Other
	SIFApplication::GetApplication()->GetStatisticsSystem()->UnpackCurrentGame(data);
	game_timer->Unpack(data);
	hud_updater->ReSyncStats();

	spatial_helper->Update();

	// Throw the bad children into a scrum
	// game_state->SetPlayRestartPosition(ball_pos); //Force scrum at old ball_pos
	// game_state->SetPlayRestartTeam(game_state->GetDefendingTeam());
	// game_state->SetPhase(RUGamePhase::SCRUM);

	random_number_generator->SetAssertIfUsed(true);
}

void SIFGameWorld::NetGenerateReSync(SSStreamPacker& data)
{
	//Core data
	data.Pack(random_number_generator->GetSeed());
	data.Pack(simulation_time->GetAbsoluteTime().ToSeconds());
	data.Pack(simulation_time->GetAbsoluteStepCount());// + NETWORK_MAX_FRAME_DELAY); //Push forward to avoid any late packets?

	//Team Data
	for (size_t i = 0; i < teams.size(); ++i)
	{
		data.Pack((int)teams[i]->GetPlayDirection());
		bool attacking_team = game_state->GetAttackingTeam() == teams[i].get();
		data.Pack(attacking_team);

		// Pack Player Positions
		for (auto& current_player : teams[i]->players)
		{
			data.Pack(current_player->GetMovement()->GetCurrentPosition());
			data.Pack(current_player->GetMovement()->GetCurrentVelocity());
		}
	}

	for (auto& current_player : officials->players)
	{
		data.Pack(current_player->GetMovement()->GetCurrentPosition());
		data.Pack(current_player->GetMovement()->GetCurrentVelocity());
	}

	//Ball Position
	if (UOBJ_IS_VALID(ball))
	{
		data.Pack(ball->GetCurrentPosition(true));
	}
	else
	{
		data.Pack(FVector());
	}

	//Other
	SIFApplication::GetApplication()->GetStatisticsSystem()->PackCurrentGame(data);
	game_timer->Pack(data);
}

///-------------------------------------------------------------------------------
/// InitialiseTeams
///-------------------------------------------------------------------------------

void SIFGameWorld::InitialiseTeams()
{
	MABASSERT(game_settings.num_active_teams >= 1); //#rc3_legacy added by jamesg
	MABASSERT(game_settings.num_active_teams <= 2); //#rc3_legacy added by jamesg

	// Create the required teams.
	MABASSERTMSG(teams.size() == 0, "Why do you have teams here now");
	for (int i = 0; i < game_settings.num_active_teams; i++)
	{
		MABASSERT(game_settings.team_settings[i].team.GetDbId() != SQLITEMAB_INVALID_ID);

		teams.push_back(std::make_unique<RUTeam>(i, game_settings.team_settings[i], this));

		if (i == 0)
		{
			teams.back()->SetSide(SIDE_A);
			teams.back()->SetHomeTeam(true);
		}
		else
		{
			teams.back()->SetSide(SIDE_B);
			teams.back()->SetHomeTeam(false);
		}
	}

	/// Select a random officials team (except top14)

	if (IsMatch())
	{
		officials = std::make_unique<RUTeam>(SIDE_OFFICIALS, game_settings.team_settings[SIDE_OFFICIALS], this);
		officials->SetSide(SIDE_OFFICIALS);

		/// Select most contrasting strip for referee team...

		teams[0]->SetCurrentStripId(game_settings.team_settings[0].strip_index);
		teams[1]->SetCurrentStripId(game_settings.team_settings[1].strip_index);
		const RUDB_TEAM_STRIP *team_a_strip = &teams[0]->GetDbTeamStrip();
		const RUDB_TEAM_STRIP *team_b_strip = &teams[1]->GetDbTeamStrip();

		officials->SetCurrentStripId(0);
		const RUDB_TEAM_STRIP *strip = &officials->GetDbTeamStrip();

		float home_contrast_a = SSColourHelper::RateStripContrast(strip, team_a_strip);
		float home_contrast_b = SSColourHelper::RateStripContrast(strip, team_b_strip);
		float home_contrast = MabMath::Min(home_contrast_a, home_contrast_b);

		officials->SetCurrentStripId(1);
		strip = &officials->GetDbTeamStrip();

		float away_contrast_a = SSColourHelper::RateStripContrast(strip, team_a_strip);
		float away_contrast_b = SSColourHelper::RateStripContrast(strip, team_b_strip);
		float away_contrast = MabMath::Min(away_contrast_a, away_contrast_b);

		if (home_contrast > away_contrast)
		{
			officials->SetCurrentStripId(0);
		}
	}
	else
	{
		officials = NULL;
	}

	// Set the attacking team.
	game_state->SetAttackingTeam(teams.front().get());
}

///-------------------------------------------------------------------------------
/// Assign the players numbers.
///-------------------------------------------------------------------------------

void SIFGameWorld::AssignPlayerNumbers()
{
	//// Set the numbers and positions for the players in the teams.

	if (IsMatch())			// No need to do this as RUSandboxGame::SetupActors does this,
	{						// also this bit overrides defaults positions which screws up ui-cutscenes (can't find specific players).
		MabVector<std::unique_ptr<RUTeam>>::const_iterator team_iterator;
		int playersPerTeam = GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();

		for (team_iterator = teams.begin(); team_iterator != teams.end(); ++team_iterator)
		{
			const SIFRugbyCharacterList &team_players = (*team_iterator)->GetPlayers();
			for (int i = 0; i < (int)team_players.size(); ++i)
			{
				ARugbyCharacter* player = team_players[i];
				player->GetAttributes()->SetNumber(i + 1);

				player->GetAttributes()->SetPlayerPosition(PlayerPositionEnum::GetPlayerPositionFromStartingJerseyNumber(i + 1));

				player->GetAttributes()->SetActive(true);
			}

			const SIFRugbyCharacterList &bench_players = (*team_iterator)->GetBenchPlayers();
			for (int i = 0; i < (int)bench_players.size(); ++i)
			{
				ARugbyCharacter* player = bench_players[i];
				player->GetAttributes()->SetNumber(playersPerTeam/*NUM_PLAYERS_PER_TEAM*/ + i + 1);
				player->GetAttributes()->SetActive(false);
			}
		}
	}

	if (IsMenu())
	{
		MabVector<std::unique_ptr<RUTeam>>::iterator team_iterator;
		for (team_iterator = teams.begin(); team_iterator != teams.end(); ++team_iterator)
		{
			SSTEAMSIDE side = (*team_iterator)->GetSide();
			if (side >= SSTEAMSIDE::SIDE_NONE)
			{
				continue;
			}

			const TArray<PlayerOverride>& teamOverrides = MENU_WOMEN_OVERRIDES[side];

			for (int overrideIndex = 0; overrideIndex < teamOverrides.Num(); ++overrideIndex)
			{
				PlayerOverride playerOverride = teamOverrides[overrideIndex];

				ARugbyCharacter* player = (*team_iterator)->GetPlayerByDbId(playerOverride.OverrideDbId);
				if (player)
				{
					player->GetAttributes()->SetNumber(playerOverride.OverrideShirtNumber);

					PlayerCustomisationInfo customisation = player->GetCustomisationInfo();
					customisation.m_shirtNumber = player->GetAttributes()->GetNumber();
					player->SetCustomisationInfo(customisation);
					player->ApplyCustomisation(true);
				}
			}
		}
	}

	// Set the positions for the officials.
	if (officials != NULL)
	{
		PLAYER_POSITION positions[] = { PP_REFEREE, PP_TOUCHJUDGE_LEFT, PP_TOUCHJUDGE_RIGHT, PP_TOUCHJUDGE_NORTH, PP_TOUCHJUDGE_SOUTH };

		const SIFRugbyCharacterList& team_players = officials->GetPlayers();
		for (int j = 0; j < (int)team_players.size(); ++j)
		{
			ARugbyCharacter* player = team_players[j];
			player->GetAttributes()->SetNumber(j + 1);
			player->GetAttributes()->SetPlayerPosition(positions[j]);
			player->GetAttributes()->SetActive(true);

			if (j == 0)
				player->SetRole(player->GetGameWorld()->GetRoleFactory()->Instance(SSRoleReferee::RTTGetStaticType()));
			else
				player->SetRole(player->GetGameWorld()->GetRoleFactory()->Instance(SSRoleTouchJudge::RTTGetStaticType()));
		}
	}
}

///-------------------------------------------------------------------------------
/// Set the roles for the officials.
///-------------------------------------------------------------------------------

void SIFGameWorld::SetOfficialsRoles()
{
	if (officials != NULL)
	{
		const SIFRugbyCharacterList& team_players = officials->GetPlayers();
		for (int j = 0; j < (int)team_players.size(); ++j)
		{
			ARugbyCharacter* player = team_players[j];
			if (j == 0)
				player->SetRole(player->GetGameWorld()->GetRoleFactory()->Instance(SSRoleReferee::RTTGetStaticType()));
			else
				player->SetRole(player->GetGameWorld()->GetRoleFactory()->Instance(SSRoleTouchJudge::RTTGetStaticType()));
		}
	}
}

///-------------------------------------------------------------------------------
/// Create the human-players (SSHumanPlayer's)
///-------------------------------------------------------------------------------

void SIFGameWorld::CreateHumanPlayers()
{
	SET_CHANGEPLAYER_SECTION(this, "CREATEHUMN");

	size_t localPlayerCount = (size_t)EHumanPlayerSlot::NUM_HUMANS;

	MabNodeId local_node_id = SIFApplication::GetApplication()->GetNetworkManager()->GetLocalNodeId();

	if (human_players.capacity() < localPlayerCount)
		human_players.reserve(localPlayerCount);

	SSHumanPlayer* human_player = nullptr;

	for (unsigned int i = 0; i < localPlayerCount; ++i)
	{
		EHumanPlayerSlot player_slot = static_cast<EHumanPlayerSlot>(i);

		int team_index = game_settings.human_player_settings[(int)player_slot].team;
		int player_index = game_settings.human_player_settings[(int)player_slot].player_id;

		int controller_index = game_settings.human_player_settings[i].controller_id;
		int peer_id = game_settings.human_player_settings[i].peer_id;

		//!If peer_id is not local id, then this is a network player, so set the controller to - 1
		if (game_settings.game_settings.network_game && peer_id != local_node_id)
		{
			controller_index = -1;
			//player_index = -1;
		}

		//URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		//if (pRugbyGameInstance)
		//{
		//	ARugbyPlayerController* pController = pRugbyGameInstance->GetPlayerControllerFromPlayerId(player_index);

		//	if (pController == nullptr)
		//	{
		//		for (TActorIterator<APlayerState> ActorItr(SIFApplication::GetApplication()->GetWorld()); ActorItr; ++ActorItr)
		//		{
		//			// Same as with the Object Iterator, access the subclass instance with the * or -> operators.
		//			APlayerState *pPlayerState = *ActorItr;

		//			if (pPlayerState && pPlayerState->PlayerId == player_index)
		//			{
		//				FString Error;
		//				FURL PlayerURL(NULL, *FString(""), TRAVEL_Absolute);
		//				ULocalPlayer* pNewPlayer = NewObject<ULocalPlayer>(pRugbyGameInstance->GetEngine(), pRugbyGameInstance->GetEngine()->LocalPlayerClass);
		//				APlayerController* pNewController = pRugbyGameInstance->GetWorld()->SpawnPlayActor(pNewPlayer, ROLE_SimulatedProxy, PlayerURL, pPlayerState->UniqueId, Error, 1);
		//				pNewController->PlayerState = pPlayerState;
		//				break;
		//			}
		//		}
		//	}
		//}

		if (game_settings.game_settings.network_game)
		{
			URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
			if (pRugbyGameInstance)
			{
				ARugbyPlayerController* pController = pRugbyGameInstance->GetPlayerControllerFromPlayerId(player_index);

				if (pController == nullptr)
				{
					for (TActorIterator<APlayerState> ActorItr(SIFApplication::GetApplication()->GetWorld()); ActorItr; ++ActorItr)
					{
						// Same as with the Object Iterator, access the subclass instance with the * or -> operators.
						APlayerState *pPlayerState = *ActorItr;

						if (pPlayerState && pPlayerState->PlayerId == player_index)
						{
							ARugbyPlayerController* controller = pRugbyGameInstance->GetWorld()->SpawnActor<ARugbyPlayerController>(ARugbyPlayerController::StaticClass());
							controller->PlayerState = pPlayerState;
							controller->bIsRugbyFakeOnlineController = true;
						}
					}
				}
			}
		}

		if (human_players.size() == i)
		{
			human_player = MabMemNew(heap) SSHumanPlayer(this, player_slot, player_index);
			MABASSERT(human_player != nullptr);

			human_players.push_back(human_player);
		}
		else if (human_players.size() < i)
		{
			checkf(false, TEXT("The size of human players should never be less than i."));
			break;
		}
		else
		{
			human_players[i]->SetPlayerIndex(player_index);
			human_player = human_players[i];
		}

		if (team_index != -1)
		{
			RUTeam* team = GetTeam(team_index);
			MABASSERT(team != NULL);
			//team->AppendHumanPlayer( human_player );
			human_player->SetTeam(team);
		}
		else
		{
			human_player->SetTeam(NULL);
			human_player->SetRugbyCharacter(nullptr);
		}
	}

	SET_CHANGEPLAYER_SECTION(this, NULL);
}

///-------------------------------------------------------------------------------
/// Enable all players who are going to be playing to control a player whilst loading.
///-------------------------------------------------------------------------------

void SIFGameWorld::SetupHumanPlayersForLoading(bool is_tutorial)
{
	MABASSERT(GetWorldId() == WORLD_ID::SANDBOX);

	RUGameSettings *match_settings = SIFApplication::GetApplication()->GetMatchGameSettings();
	RUTeam* team = GetTeam(0);
	MABASSERT(team != NULL);

	const int MAX_PLAYERS_IN_SANDBOX = is_tutorial ? 1 : 4;
	const int LOCAL_PLAYERS_MAX = match_settings->game_settings.network_game ? 4 : 8;
	const int LOCAL_PLAYERS_START = 0; // match_settings->game_settings.network_game ? SIFMatchmakingHelpers::GetLocalClientTeamIndex() * LOCAL_PLAYERS_MAX : 0;

	int players_assigned = 0;

	/// Assign controller indices for all players from game settings
	for (int i = 0; i < NUM_HUMAN_PLAYERS; ++i)
	{
		SSHumanPlayer* human_player = human_players[i];

		//#rc3_legacy_controller int controller_index = settings->human_player_settings[i].controller_index;
		int player_index = match_settings->human_player_settings[i].player_id;
		int team_index = match_settings->human_player_settings[i].team;

		/// Assign to a the training team if they are playing
		/*#rc3_legacy_controller
		if(controller_index >= starting_human_index &&
			controller_index < starting_human_index + MAX_PLAYERS_IN_TRAINING &&
			team_index != -1 )
		//*/
		if (i >= LOCAL_PLAYERS_START &&
			i < LOCAL_PLAYERS_START + LOCAL_PLAYERS_MAX &&
			players_assigned < MAX_PLAYERS_IN_SANDBOX &&
			team_index != -1)
		{
			human_player->SetPlayerIndex(player_index);
			human_player->SetTeam(team);
			players_assigned++;
		}
		else
		{
			human_player->SetPlayerIndex(-1);
			human_player->SetTeam(NULL);
		}
	}

	// TYRONE : If it is an AI vs AI game, we still want to assign the first usable controller
	if (players_assigned == 0)
	{
		//#rc3_legacy_controller int master_controller_id = SIFApplication::GetApplication()->GetWindowSystem()->GetCurrentMasterController();
		int master_player_id = SIFUIHelpers::GetCurrentMasterPlayerIndex();

		/// Try and match the master controller to the human player that is presently allocated this controller
		/// so that their colours match
		SSHumanPlayer* human_player_on_master_controller = human_players[0];	// Default to first player if we cannot find it

		for (int i = 0; i < NUM_HUMAN_PLAYERS; i++)
		{
			if (match_settings->human_player_settings[i].player_id == master_player_id)
			{
				human_player_on_master_controller = human_players[i];
				break;
			}
		}

		if (human_player_on_master_controller)
		{
			human_player_on_master_controller->SetPlayerIndex(master_player_id);
			human_player_on_master_controller->SetTeam(team);
		}
	}

	team->UnassignHumanPlayers();

	/// Make sure the ball holder has a human ssigned to them
	FVector reference_pos;
	if (GetGameState()->GetBallHolder())
	{
		team->GetHumanSelector().AssignHumanToPlayer(GetGameState()->GetBallHolder(), true);
		reference_pos = GetGameState()->GetBallHolder()->GetMovement()->GetCurrentPosition();
	}
	else
	{
		reference_pos = GetBall()->GetCurrentPosition();
	}

	/// Assign the rest like it was a phase change
	team->AssignHumanPlayersOnPhaseChange(reference_pos, false);
}

///-------------------------------------------------------------------------------
/// Reset the human players for the sandbox game. (AwakenInstance)
///-------------------------------------------------------------------------------

void SIFGameWorld::ResetSandboxHumanPlayers()
{
	for (int i = 0; i < NUM_HUMAN_PLAYERS; ++i)
	{
		SSHumanPlayer* human_player = human_players[i];
		int player_index = game_settings.human_player_settings[i].player_id;
		human_player->SetPlayerIndex(player_index);
		human_player->SetTeam(NULL);
	}
}

///-------------------------------------------------------------------------------
/// Get number of teams.
///-------------------------------------------------------------------------------

int SIFGameWorld::GetNumTeams() const
{
	return (int)teams.size();
}

///-------------------------------------------------------------------------------
/// Get team 'index'
///-------------------------------------------------------------------------------

RUTeam* SIFGameWorld::GetTeam(int index) const
{
	MABASSERT(index >= 0 && index < (int)teams.size());
	if (index >= 0 && index < (int)teams.size())
	{
		MABASSERT(teams[index] != NULL);
		if (teams[index] != NULL)
		{
			return teams[index].get();
		}
	}
	return nullptr;
}

///-------------------------------------------------------------------------------
/// Get team based off of their database index, will return NULL if the index is not a current team
///-------------------------------------------------------------------------------
RUTeam*	SIFGameWorld::GetTeamFromDB(int db_id) const
{
	MABASSERT(db_id >= 0);
	for (size_t i = 0; i < teams.size(); i++)
	{
		if (teams[i]->GetDbTeam().GetDbId() == db_id)
			return teams[i].get();
	}

	return NULL;
}

///-------------------------------------------------------------------------------
/// Get team - from team_side.
///-------------------------------------------------------------------------------

RUTeam* SIFGameWorld::GetTeam(SSTEAMSIDE team_side) const
{
	return team_side != SIDE_NONE ? GetTeam(static_cast<int>(team_side)) : NULL;
}

///-------------------------------------------------------------------------------
/// Set the active camera
///-------------------------------------------------------------------------------

void SIFGameWorld::SetCamera(ARugbyCameraActor* ccamera)
{
	active_camera = ccamera;
	//active_camera->Update(0.0f); //fixme
}

///-------------------------------------------------------------------------------
/// Get the active camera
///-------------------------------------------------------------------------------

ARugbyCameraActor* SIFGameWorld::GetCamera() const
{
	ensureMsgf(active_camera != nullptr, TEXT("No Camera Actor!"));
	return active_camera;
}

/*
void SIFGameWorld::AssignPlayerControllerToTeam(ARugbyPlayerController* player, RUTeam* team)
{
	int playerId = player->GetPlayerIndex();

	RUTeam* oldTeam = m_PlayerTeamAssignments.FindRef(playerId);

	if (team == oldTeam)
		return;

	if (oldTeam != nullptr)
		oldTeam->RemoveHumanPlayer(player);

	if (team != nullptr)
	{
		m_PlayerTeamAssignments.Emplace(playerId, team);
		team->AppendHumanPlayer(player);
	}
	else
	{
		m_PlayerTeamAssignments.Remove(playerId);
	}

	GetEvents()->team_assignments_changed(team, player);
}

RUTeam* SIFGameWorld::GetTeamAssignmentForPlayerController(const ARugbyPlayerController* player) const
{
	// DH - During server travel and breakpointing, the playerstate can be in the middle of migration, resulting in a crash
	if (UOBJ_IS_VALID(player->PlayerState))
	{
		int playerId = player->GetPlayerIndex();

		return m_PlayerTeamAssignments.FindRef(playerId);
	}

	return nullptr;
}

TArray<ARugbyPlayerController*> SIFGameWorld::GetAssignedPlayerControllers() const
{
	TArray<ARugbyPlayerController*> result;

	for (ARugbyPlayerController* pc : mGameInstance.GetLocalPlayerControllers())
	{
		if (pc && GetTeamAssignmentForPlayerController(pc) != nullptr)
		{
			result.Add(pc);
		}
	}

	return result;
}

ARugbyPlayerController* SIFGameWorld::GetFirstAssignedPlayerController() const
{
	ARugbyPlayerController* result = nullptr;

	for (ARugbyPlayerController* pc : mGameInstance.GetLocalPlayerControllers())
	{
		if (pc && GetTeamAssignmentForPlayerController(pc) != nullptr)
		{
			result = pc;
			break;
		}
	}

	return result;
}

ARugbyPlayerController* SIFGameWorld::GetAssignedPlayerControllerFromControllerId(int controllerId) const
{
	ARugbyPlayerController* result = nullptr;

	ARugbyPlayerController* pc = mGameInstance.GetLocalPlayerControllerFromControllerId(controllerId);
	if (pc && GetTeamAssignmentForPlayerController(pc) != nullptr)
	{
		result = pc;
	}

	return result;
}

ARugbyPlayerController* SIFGameWorld::GetAssignedPlayerControllerFromPlayerId(int playerId) const
{
	ARugbyPlayerController* result = nullptr;

	ARugbyPlayerController* pc = mGameInstance.GetLocalPlayerControllerFromPlayerId(playerId);
	if (pc && GetTeamAssignmentForPlayerController(pc) != nullptr)
	{
		result = pc;
	}

	return result;
}
*/


///-------------------------------------------------------------------------------
/// GetHumanPlayers
///-------------------------------------------------------------------------------

const MabVector<SSHumanPlayer*>& SIFGameWorld::GetHumanPlayers() const
{
	return human_players;
}

///-------------------------------------------------------------------------------
/// GetHumanPlayer
///-------------------------------------------------------------------------------

SSHumanPlayer* SIFGameWorld::GetHumanPlayer(EHumanPlayerSlot playerNumber) const
{
	checkf(playerNumber < EHumanPlayerSlot::NUM_HUMANS, TEXT("requesting player number above NUM_HUMANS"));
	return (unsigned int)playerNumber < human_players.size() ? human_players.at((unsigned int)playerNumber) : nullptr;
}

///-------------------------------------------------------------------------------
/// GetHumanPlayer
///-------------------------------------------------------------------------------

SSHumanPlayer* SIFGameWorld::GetHumanPlayerFromPlayerIndex(int playerIndex) const
{
	checkf(playerIndex >= 256, TEXT("Unexpected value used for player index, should be 256 or greater."));

	SSHumanPlayer* result = nullptr;

	if (playerIndex < 0)
	{
		return result;
	}

	for (size_t i = 0; i < human_players.size(); ++i)
	{
		SSHumanPlayer* human_player = human_players[i];
		if (human_player && (human_player->GetPlayerIndex() == playerIndex))
		{
			result = human_player;
			break;
		}
	}

	return result;
}

///-------------------------------------------------------------------------------
/// GetFirstActiveHumanPlayer
///-------------------------------------------------------------------------------

SSHumanPlayer* SIFGameWorld::GetFirstHumanPlayer() const
{
	if (!world_is_allocated)
	{
		return nullptr;
	}

	MabVector<SSHumanPlayer*>::const_iterator it = human_players.cbegin();
	return it != human_players.cend() ? *it : nullptr;
}

///-------------------------------------------------------------------------------
/// GetNumActiveHumanPlayers
///-------------------------------------------------------------------------------

int SIFGameWorld::GetNumActiveHumanPlayers() const
{
	if (!world_is_allocated)
	{
		return 0;
	}

	int count = 0;

	for (size_t i = 0; i < human_players.size(); ++i)
	{
		if (human_players[i]->IsPlaying())
		{
			count++;
		}
	}

	return count;
}

///-------------------------------------------------------------------------------
/// GetFirstActiveHumanPlayer
///-------------------------------------------------------------------------------

SSHumanPlayer* SIFGameWorld::GetFirstActiveHumanPlayer() const
{
	if (!world_is_allocated)
	{
		return nullptr;
	}

	SSHumanPlayer* result = nullptr;
	SSHumanPlayer* human_player;

	for (size_t i = 0; i < human_players.size(); ++i)
	{
		human_player = human_players[i];
		if (human_player->IsPlaying())
		{
			result = human_player;
		}
	}

	return result;
}


#ifdef ENABLE_OSD
void SIFGameWorld::SetChangePlayerSection(const char* section_name)
{
	for (size_t i = 0; i < human_players.size(); ++i)
	{
		SSHumanPlayer* human_player = human_players[i];

		if (!human_player->IsPlaying())
			continue;

		human_player->SetChangePlayerSection(section_name);
	}
}
#endif

///-------------------------------------------------------------------------------
/// GetFilteredPlayerList -
///-------------------------------------------------------------------------------

void SIFGameWorld::GetFilteredPlayerList(RLPResultList& result_list, RLP_FILTERPARAMETERS& filter_params, RLP_SORTER* sorter) const
{
	// Cannot use the Ref filter and the team filter at once.
	MABASSERT((filter_params.filters & RLP_FILTER_INCLUDE_REFANDTJ) == 0 || (filter_params.filters & RLP_FILTER_TEAM) == 0);

	// Create the filter function.
	RLP_FILTER_FUNC player_filter;
	player_filter.filter_params = &filter_params;
	player_filter.result_list = &result_list;
	result_list.sorter = sorter;

	// Initialise the player filter function based on the given input.
	player_filter.Initialise();
	result_list.clear();

	static SIFGameObject* last_player = NULL;

	for (ARugbyCharacter* player : players)
	{
		last_player = player;
		MABASSERT(player != NULL);
		player_filter(player);
	}

	if (sorter != NULL)
	{
		sorter->init();
		std::sort(result_list.begin(), result_list.end());
	}
}

//******************************************************************************************************************
//******************************************************************************************************************
//******************************************************************************************************************

///-------------------------------------------------------------------------------
/// LoadingAnimResource
///-------------------------------------------------------------------------------


void SIFGameWorld::InitialiseAnimation(void)
{
	//#rc3_legacy 
	/*
	NMMabUpdateMode UPDATE_MODE = NMMAB_UPDATE_MODE_JOB_QUEUE;

	animation_resource = MabCast<NMMabResource>( MabGlobalResourceSet::FetchResource(RUGameAnimation::ANIMATION_NETWORK_RESOURCE) );
	animation_resource->AddRef();

	animation_manager = MabMemNew(heap) NMMabAnimationWorld( heap, repository, GetJobQueue(), NULL, UPDATE_MODE );
	animation = MabMemNew(heap) RUGameAnimation( animation_resource->GetNetworkDef(), game_settings.game_settings.network_game );
	*/

	animation = std::make_unique<RUGameAnimation>(this, nullptr, false); //Also see RugbyAnimationRecords.cpp
}


///-------------------------------------------------------------------------------
/// Creates the SIFEffectSystem object and registers all its components
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::CreateEffectSystem()
{
	effect_system = MabMemNew(heap) SIFEffectSystem(heap);
	effect_system->RegisterComponentHandler(MabMemNew(heap) SIFParticleEffectComponentHandler(game_context));
	effect_system->RegisterComponentHandler(MabMemNew(heap) SIFRumbleEffectComponentHandler());
	effect_system->RegisterComponentHandler(MabMemNew(heap) SIFSoundEffectComponentHandler());
	effect_system->RegisterComponentHandler(MabMemNew(heap) SIFTimeEffectComponentHandler(simulation_time));

	effect_system->LoadDefinitionsFromResource(EFFECT_SYSTEM_DEFINITIONS_RESOURCE);
}
#endif

///-------------------------------------------------------------------------------
/// SyncUpdate
///-------------------------------------------------------------------------------

void SIFGameWorld::SyncUpdate()
{
	MABMEM_SCOPED_TAG(MEMTAG_GAMEWORLD_SYNCUPDATE);

	m_asyncTaskQueue.Update();

	if (m_gameWorldSetupState != SETUP_IDLE)
	{
		GameWorldSetupUpdate();

		if (m_isLoading && IsMatch())
		{
			EstimateLoadCompletion();
		}
	}

	if (!HasAllocated())
		return;

	// Awaken this game world after a delay... DelayedAwakenInstance()
	if (delayed_awaken > 0)
	{
		delayed_awaken--;
		if (delayed_awaken == 0)
			AwakenInstance();
	}

	if (!IsAwake())
		return;

	// Enable database access during sync update.
	RL3Database *rl3_database = SIFApplication::GetApplication()->GetGameDatabaseManager()->GetRL3Database();
	rl3_database->Lock();

	// Delayed reload of players on restart.

	if (restart_reload_players > 0)
	{
		if (restart_reload_players > 1)
		{
			restart_reload_players--;
			if (restart_reload_players == 1)
			{
				MABLOGDEBUG("StartAllPendingInterchanges");
				substitution_manager->StartAllPendingInterchanges(true);
			}
		}
		else
		{
			if (substitution_manager->HavePendingInterchangesCompleted())
			{
				MABLOGDEBUG("RestartPlayerReload");

				restart_reload_players = 0;

				for (unsigned int i = 0; i < teams.size(); ++i)
				{
					teams[i]->RestartPlayerReload();
				}
				RestartGame();
			}
		}
	}

	/// When RequestAsyncLoadStart() has been called, start asynchronous-game loading safely.
	if (async_load_start_requested)
	{
		if (async_load_start_requested == ASYNC_LOAD_REQUESTED)
		{
			// Keep the fade on, other systems can override otherwise and create a glitch.
			/*#rc3_legacy_wipe
			GetScreenWipeManager()->StartFadeFromBlack(ASYNC_LOAD_FADE_TIME);
			GetScreenWipeManager()->HoldScreenWipe(3.0f);
			*/

			// Wait for strip change (back to ui teams) before commencing.
			if (substitution_manager->HaveStripChangesCompleted() && !IsSandboxTeamSwapActive())
			{
				OnAsyncLoadingStart();
				async_load_start_requested = ASYNC_LOAD_DEFERED_START;
			}
			else
			{
				MABLOGDEBUG("Waiting for strip change!");
			}
		}
		else if (async_load_start_requested == ASYNC_LOAD_DEFERED_START)
		{
			/*#rc3_legacy_wipe
			GetScreenWipeManager()->StartFadeFromBlack(ASYNC_LOAD_FADE_TIME);
			*/

			//#rc3_legacy_async
			/*MabClosureBase* start_async_load_closure = MakeClosure(SIFApplication::GetApplication(), &SIFApplication::StartAsyncGameLoad);
			PSSGMabGfx::DeferredFunctionCall(start_async_load_closure);*/

			URugbyGameInstance& gameInstance = GetGameInstance();
			FSimpleDelegate func = FSimpleDelegate();
			func.BindUObject(&gameInstance, &URugbyGameInstance::StartAsyncGameLoad);
			URugbyGameInstance::DeferredFunctionCall(func);

			async_load_start_requested = ASYNC_LOAD_NONE;
		}
	}

	// Online automatic quit if opponent (or you have) has disconnected.
	if (!initial_state_checked)
	{
		--delayed_initial_state_check;
		if (delayed_initial_state_check <= 0)
		{
			ProcessInitialState();
			initial_state_checked = true;
		}
	}

	//{
	//	if (ball)
	//	{
	//		ball->SyncTransformToAsyncTransform();
	//	}

	//	for (size_t i = 0; i < all_players.size(); ++i)
	//	{
	//		ARugbyCharacter *player = all_players[i];

	//		if (player)
	//		{
	//			player->SyncTransformToAsyncTransform();
	//			player->SyncVisibleToAsyncVisible();
	//		}
	//	}
	//}

	//#rc3_legacy #define SYNCPROFILE( code, label ) { MabProfilerMarker marker( "SIFGameWorld::SyncUpdate() - " #label ); code; }

	MAB_PROFILE_SECTION_START(profile1, "SIFGameWorld::SyncUpdate");

	SetSandboxEnvironment(requested_sandbox_environment_setting);

	//#rc3_legacy SYNCPROFILE( camera_manager->SyncUpdate(), "Camera manager" );

	/// Call SyncUpdate on all player state's (Updates dirt levels etc...)
	{
		for (size_t i = 0; i < all_players.size(); ++i)
		{
			ARugbyCharacter *player = all_players[i];
			if (player->GetVisible())
			{
				player->GetState()->SyncUpdate();
			}
		}

		//#rc3_legacy
		//MABMEM_SET_TAG(MEMTAG_GAMEWORLD_SYNCUPDATE_2);
		//SYNCPROFILE( object_database->SyncUpdate(game_context), "Object database" );
		//MABMEM_SET_TAG(MEMTAG_GAMEWORLD_SYNCUPDATE_3);
		//SYNCPROFILE( cutscene_manager->SyncUpdate(), "Cutscene manager" );
		//MABMEM_SET_TAG(MEMTAG_GAMEWORLD_SYNCUPDATE_4);
		//SYNCPROFILE( object_database->SyncUpdateGraphicsTransforms(), "Graphics transforms" );
		//MABMEM_SET_TAG(MEMTAG_GAMEWORLD_SYNCUPDATE_5);
		//SYNCPROFILE( animation_manager->SyncUpdate(), "Animation manager" );
		//MABMEM_SET_TAG(MEMTAG_GAMEWORLD_SYNCUPDATE_6);
		//SYNCPROFILE( hud3d->SyncUpdate(), "Hud" );
		ensure(hud3d); //changed assertion to once only, as it will trigger every frame :( -MG
		if (hud3d)
		{
			hud3d->SyncUpdate();
		}
		//MABMEM_SET_TAG(MEMTAG_GAMEWORLD_SYNCUPDATE_7);
		//SYNCPROFILE( particles_manager->SyncUpdate(game_context), "Particle manager" );
		//MABMEM_SET_TAG(MEMTAG_GAMEWORLD_SYNCUPDATE_8);
		//SYNCPROFILE( shader_parameters->SyncUpdate(), "Shader manager" );
		//MABMEM_SET_TAG(MEMTAG_GAMEWORLD_SYNCUPDATE_9);
		//SYNCPROFILE( cutscene_manager->SyncUpdateCameras(), "Cutscene manager" );
		//MABMEM_SET_TAG(MEMTAG_GAMEWORLD_SYNCUPDATE_10);
		//SYNCPROFILE( GetViewManager()->SyncUpdate(), "View manager" );

		//if(SIFApplication::GetApplication()->IsRenderingSuppressed())
		//{
		//	if(relight_scene)
		//	{
		//		relight_scene = false;
		//		RelightScene();
		//	}
		//}

		//#rc3_legacy
		//SYNCPROFILE( weather_manager->SyncUpdate(), "Weather manager" );
		substitution_manager->SyncUpdate();
		if (hud_updater) hud_updater->SyncUpdate();
		//SYNCPROFILE( GetScreenWipeManager()->SyncUpdate(), "Screen wipe manager" );
		//SYNCPROFILE( GetPostEffectsManager()->SyncUpdate(), "Post effects manager" );
	}

	rl3_database->Unlock();

	MAB_PROFILE_SECTION_END(profile1);
}

void SIFGameWorld::GameWorldSetupUpdate()
{
	/*{//temp debug
		const int32 numPlayers = GetAllPlayers().size();
		int counter = 0;
		for (int32 i = 0; i < numPlayers; i++)
		{
			ARugbyCharacter* thePlayer = GetAllPlayers()[i];
			switch (thePlayer->m_footballerLoadStage)
			{
				case EFootballerLoadStage::MeshMerge:
				case EFootballerLoadStage::MeshMerge_Complete:
				case EFootballerLoadStage::MorphMerge:
				case EFootballerLoadStage::MorphMerge_Complete:
				case EFootballerLoadStage::ShadowMerge:
				case EFootballerLoadStage::ShadowMerge_Complete:
					{
						counter++;
						break;
					}
			}
		}
		GEngine->AddOnScreenDebugMessage(50001, 10.0f, FColor::White, FString::Printf(TEXT("Running Mesh merge for %i characters"), counter));
		GEngine->AddOnScreenDebugMessage(50002, 10.0f, FColor::White, FString::Printf(TEXT("Running Mesh merge: QUEUED %i Morphs"), GetGameInstance().m_MorphDataList.Num()));

	}*/
	m_mergingManager.m_currentTimer--;

	switch (m_gameWorldSetupState)
	{
		case STADIUM_LOAD:
		{
			m_loadStartTime = FPlatformTime::Seconds();
			UE_LOG(LogTemp, Warning, TEXT("Started Loading..."));

			if (AsyncLoadStadiumMaps())
			{
				m_gameWorldSetupState = STADIUM_LOAD_WAIT;
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("SETUP_ERROR: AsyncLoadStadiumMaps"));
				m_gameWorldSetupState = SETUP_ERROR;
			}
			break;
		}
		case STADIUM_LOAD_WAIT:
		{
			if (!AreStadiumMapsLoaded())
			{
				break;
			}

			// Probe if we have anything to do
			UWorld* pGameWorld = GetGameInstance().GetWorld();
			if (pGameWorld)
			{
				pGameWorld->UpdateLevelStreaming();
				bool bWorkToDo = (pGameWorld->IsVisibilityRequestPending() || IsAsyncLoading());

				if (bWorkToDo)
				{
					break;
				}
			}

			InitialisePostLoad();

			//this might need to be moved to async
			FindMatchGraphicsObjects();
			GenerateMatchGraphics();

			if (IsMenu())
			{
				// Start quickload.
				m_quickLoadActive = true;

				// Alert the game that it can continue
				OnLoadingComplete();
			}

			// Fill the teams with players.
			m_gameWorldSetupState = TEAMS_LOAD;

			UE_LOG(LogTemp, Warning, TEXT("Stadium Load took %f seconds"), FPlatformTime::Seconds() - m_loadStartTime);
			m_loadStartTime = FPlatformTime::Seconds();

#if PLATFORM_SWITCH
			GEngine->ForceGarbageCollection(false);
#endif
			// Intentional fall through to TEAMS_LOAD
		}
		case TEAMS_LOAD:
		{
			if (AsyncLoadFootballerMaps())
			{
				m_gameWorldSetupState = TEAMS_LOAD_WAIT;
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("SETUP_ERROR: AsyncLoadFootballerMaps"));
				m_gameWorldSetupState = SETUP_ERROR;
			}
			break;
		}
		case TEAMS_LOAD_WAIT:
		{
			if (!AreFootballerMapsLoaded())
			{
				break;
			}
#if LOW_MEMORY_MODE
			//unload most of the sandbox guys
			if (IsMatch()) 
			{
				SIFGameWorld* sandbox = GetGameInstance().GetSandboxGame()->GetGameWorld();
				sandbox->CleanupMostActors();
			}
#endif
#if PLATFORM_SWITCH
			GEngine->ForceGarbageCollection(false);
#endif

			m_gameWorldSetupState = TEAMS_INITIALISE;

			UE_LOG(LogTemp, Warning, TEXT("Teams Load took %f seconds"), FPlatformTime::Seconds() - m_loadStartTime);
			m_loadStartTime = FPlatformTime::Seconds();

			// Intentional fall through to TEAMS_INITIALISE
		}
		case TEAMS_INITIALISE:
		{
			// Check that our morph list is empty. At this point we haven't started any morphs so there shouldn't be any there.
			{
				if (SIFApplication::GetApplication()->m_CurrentMorphCount != 0)
				{
					UE_LOG(LogTemp, Warning, TEXT("SIFGameWorld::GameWorldSetupUpdate Found non-zero current morph count! Current Morph Count: %d"), SIFApplication::GetApplication()->m_CurrentMorphCount);
					SIFApplication::GetApplication()->m_CurrentMorphCount = 0;
				}

				if (SIFApplication::GetApplication()->m_MorphDataList.Num() > 0)
				{
					UE_LOG(LogTemp, Warning, TEXT("SIFGameWorld::GameWorldSetupUpdate Found entries in the morph data list! Number of entries: %d"), SIFApplication::GetApplication()->m_MorphDataList.Num());
					SIFApplication::GetApplication()->m_MorphDataList.Empty();
				}
			}

			InitialiseFootballers();

			if (IsMatch())
			{
				DisablePlayers();
				int32 numPlayers = GetAllPlayers().size();
				for (int32 i = 0; i < numPlayers; i++)
				{
					ARugbyCharacter* thePlayer = GetAllPlayers()[i];

					if (thePlayer && thePlayer->IsValidLowLevel())
					{
						thePlayer->SetActorTickEnabled(false);
						thePlayer->GetMesh()->SetComponentTickEnabled(false);
					}
				}
			}

			m_gameWorldSetupState = CHARACTER_CUSTOMISATIONS_LOAD;
			break;
		}
		case TEAMS_SWAP:
		{
			// Entry point for swapping teams and recustomising
#if defined (ENABLE_FULL_TEAM_SWAP)
			DoFullSwapSandboxTeams();
#else
			DoSwapSandboxTeams();
#endif
			m_gameWorldSetupState = CHARACTER_CUSTOMISATIONS_LOAD;
			break;
		}
		case CHARACTER_CUSTOMISATIONS_LOAD:
		{
			if (!IsMatch()) // The match game world will customise its players through RugbyAsyncTasks
			{
				for (unsigned int i = 0; i < all_players.size(); ++i)
				{
					all_players[i]->ApplyCustomisation(true);
				}
			}

			m_gameWorldSetupState = CHARACTER_CUSTOMISATIONS_LOAD_WAIT;

#if PLATFORM_SWITCH
			GEngine->ForceGarbageCollection(false);
#endif
			break;
		}
		case CHARACTER_CUSTOMISATIONS_LOAD_WAIT:
		{
			bool hasPendingCustomisations = false;

			if (!IsMatch())
			{
				for (unsigned int i = 0; i < all_players.size(); ++i)
				{
					if (all_players[i]->HasUnappliedCustomisations())
					{
						all_players[i]->ApplyCustomisation(true);
					}

					if (all_players[i]->HasPendingCustomisation())
					{
						hasPendingCustomisations = true;
					}
			
					//if (IsMatch() && hasPendingCustomisations == false && all_players[i]->m_footballerLoadStage < EFootballerLoadStage::MorphMerge && m_mergingManager.m_currentTimer <= 0)
					//{
					//	m_mergingManager.m_currentTimer = m_mergingManager.m_TimerCounter;
					//	all_players[i]->StartMorphMerge();
					//	UE_LOG(LogTemp, Warning, TEXT("Starting player Merge %s"), *all_players[i]->GetName());
					//	GEngine->AddOnScreenDebugMessage(50000, 10.0f, FColor::White, FString::Printf(TEXT("Starting player Merge %s"), *all_players[i]->GetName()));
					//}
				}	
			}
			else
			{
				hasPendingCustomisations = !m_asyncTaskQueue.IsQueueEmpty();
			}

			if (hasPendingCustomisations)
			{
				break;
			}
			m_gameWorldSetupState = CHARACTER_CUSTOMISATIONS_INITIALISE;
#if PLATFORM_SWITCH
			GEngine->ForceGarbageCollection(false);
#endif

			break;
		}
		case CHARACTER_CUSTOMISATIONS_INITIALISE:
		{
			/* Test code to delay loading a little longer
			static int test = 0;
			if (test++ > 120)
			{
				test = 0;
				m_gameWorldSetupState = SETUP_COMPLETED;
			}
			*/

			m_gameWorldSetupState = CHARACTER_FACE_RENDER_WAIT;

			UE_LOG(LogTemp, Warning, TEXT("Customisations Load took %f seconds"), FPlatformTime::Seconds() - m_loadStartTime);
			m_loadStartTime = FPlatformTime::Seconds();
			break;
		}
		case CHARACTER_MESH_MERGE_START:
		{
			if (IsMatch())
			{

				if (m_mergingManager.m_currentTimer > 0)
				{
					break;
				}

				//for (int32 i = m_mergingManager.m_currentCharacter; i < numPlayers; i++)
				{
					ARugbyCharacter* thePlayer = GetAllPlayers()[m_mergingManager.m_currentCharacter];
					if (thePlayer->m_footballerLoadStage < EFootballerLoadStage::MorphMerge)
					{
						UE_LOG(LogMerge, Warning, TEXT("Starting player Merge %s"), *thePlayer->GetName());
						GEngine->AddOnScreenDebugMessage(50000, 10.0f, FColor::White, FString::Printf(TEXT("Starting player Merge %s"), *thePlayer->GetName()));

						thePlayer->m_footballerLoadStage = EFootballerLoadStage::MorphMerge;
						thePlayer->StartMorphMerge();
						//thePlayer->CreateShadowMesh();
						//thePlayer->MergeMeshs();
#if PLATFORM_SWITCH
						if (thePlayer->GetAnimInstance())
						{
							thePlayer->GetAnimInstance()->m_bIsInMenu = IsMenu();
						}
#endif
						m_mergingManager.m_currentTimer = m_mergingManager.m_TimerCounter;
					}
					//break;
				}

				const int32 numPlayers = GetAllPlayers().size();
				if (++m_mergingManager.m_currentCharacter < numPlayers)
				{
					break;
				}
				m_gameWorldSetupState = CHARACTER_MESH_MERGE_WAIT;

			}
			else
			{
				m_gameWorldSetupState = CHARACTER_MESH_MERGE_WAIT;
			}
		}
		break;
		case CHARACTER_MESH_MERGE_WAIT:
		{
#if ENABLE_MORPH_MERGE
			UE_LOG(LogMerge, Warning, TEXT("CHARACTER_MESH_MERGE_WAIT"));

#if PLATFORM_SWITCH && false
			FSwitchPlatformMisc::SetCPUBoostModeEnabled(true);
#endif

			URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
			if (pRugbyGameInstance)
			{
				TSharedPtr<FMeshMergeThread> pMergeThread = pRugbyGameInstance->GetMergeThread();
				if (pMergeThread)
				{
					if (!pMergeThread->ProcessResultsOnGameThread())
					{
						break;
					}
				}

				if (pRugbyGameInstance->m_MorphDataList.Num() != 0)
				{
					UE_LOG(LogMerge, Warning, TEXT("pRugbyGameInstance->m_MorphDataList.Num() != 0"));
					break;
				}

				if (SIFApplication::GetApplication()->AbortLoadForInvite)
				{
					break;
				}

				pRugbyGameInstance->m_CurrentMorphCount = 0;
			}
#endif
			m_gameWorldSetupState = SETUP_COMPLETED;

#if PLATFORM_SWITCH && false
			FSwitchPlatformMisc::SetCPUBoostModeEnabled(false);
#endif

			UE_LOG(LogMerge, Warning, TEXT("SETUP_COMPLETED"));

			UE_LOG(LogMerge, Warning, TEXT("Morph Merge took %f seconds"), FPlatformTime::Seconds() - m_loadStartTime);
			m_loadStartTime = FPlatformTime::Seconds();
		}
		break;
		case CHARACTER_FACE_RENDER_WAIT:
		{
			URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

			if (IsMatch())
			{
				if (pRugbyGameInstance)
				{
					URUTeamFacesGenerator* pTeamFacesGenerator = pRugbyGameInstance->GetTeamFacesGenerator();

					if (pTeamFacesGenerator && !pTeamFacesGenerator->IsCurrentlyRendering())
					{
#if PLATFORM_SWITCH
						GEngine->ForceGarbageCollection(false);
#endif

						bool DidMerge = false;
						int32 MorphCounter = 0;
//#if ENABLE_MORPH_MERGE		
//						for (TObjectIterator<USkeletalMeshComponent> Itr; Itr; ++Itr)
//						{
//							if (SIFApplication::GetApplication()->AbortLoadForInvite)
//							{
//								UE_LOG(LogMerge, Warning, TEXT("AbortLoadForInvite"));
//								DidMerge = false;
//								MorphCounter = 0;
//								//Lest just clear all this out just in case we got an abort
//								SIFApplication::GetApplication()->m_MorphDataList.Empty();
//								SIFApplication::GetApplication()->m_CurrentMorphCount = 0;
//								break;
//							}
//
//							USkeletalMeshComponent* pComponent = *Itr;
//
//							// Make sure the mesh isn't gonna get itself killed (or worse expelled).
//							if (pComponent && pComponent->IsValidLowLevel())
//							{
//								// Check that the mesh actually has a mesh to merge.
//								if (pComponent->SkeletalMesh != nullptr)
//								{
//									// Try get the owner of the component.
//									if (pComponent->GetOuter())
//									{
//										// Check that the owner is a player in the match game world.
//										ARugbyCharacter* pOwner = Cast<ARugbyCharacter>(pComponent->GetOuter());
//
//										if (pOwner)
//										{
//											if (pOwner->GetGameWorld())
//											{
//												if (pOwner->GetGameWorld()->IsMatch())
//												{
//													DidMerge = true;
//													// Merge away.
//													pOwner->MorphMergeComponent(pComponent);
//													MorphCounter++;
//													UE_LOG(LogMerge, Warning, TEXT("MorphMergeComponent()"));
//												}
//												else
//												{
//													UE_LOG(LogMerge, Warning, TEXT("we are in a match"));
//												}
//											}
//											else
//											{
//												UE_LOG(LogMerge, Warning, TEXT("world is broken"));
//											}
//										}
//										else
//										{
//											UE_LOG(LogMerge, Warning, TEXT("owner is no good"));
//										}
//									}
//									else
//									{
//										UE_LOG(LogMerge, Warning, TEXT("pComponent no owner"));
//									}
//								}
//								else
//								{
//									//UE_LOG(LogTemp, Warning, TEXT("pComponent no mesh"));
//								}
//							}
//							else
//							{
//								UE_LOG(LogMerge, Warning, TEXT("pComponent not valid"));
//							}
//						}
//
//#endif

						m_gameWorldSetupState = CHARACTER_MESH_MERGE_WAIT;

						UE_LOG(LogTemp, Warning, TEXT("Face Renders Load took %f seconds"), FPlatformTime::Seconds() - m_loadStartTime);
						UE_LOG(LogMerge, Warning, TEXT("m_CurrentMorphCount: %i "), pRugbyGameInstance->m_CurrentMorphCount);
						UE_LOG(LogMerge, Warning, TEXT("MorphCounter: %i"), MorphCounter);
						UE_LOG(LogMerge, Warning, TEXT("Starting Morph Merge."));

						if (DidMerge)
						{
							UE_LOG(LogMerge, Warning, TEXT("Did Merge"));
						}
						else
						{
							UE_LOG(LogMerge, Warning, TEXT("Did Not Merge"));
						}

						m_loadStartTime = FPlatformTime::Seconds();
						pTeamFacesGenerator->DestroyHeadRenderer();

						// Delete all the cached morph merged components which we kept cached.
						//if (IsMatch())
						//{
						//	TArray<USkeletalMesh*> UsedMeshes;
						//	for (unsigned int i = 0; i < all_players.size(); ++i)
						//	{
						//		ARugbyCharacter* character = all_players[i];

						//		if (character)
						//		{
						//			UsedMeshes.AddUnique(character->GetBodyMesh()->SkeletalMesh);
						//		}
						//	}

						//	for (auto& pCurrentMesh : SIFApplication::GetApplication()->GetMorphMergeCache())
						//	{
						//		TArray<UObject*> ReferredToObjects;				//req outer, ignore archetype, recursive, ignore transient
						//		FReferenceFinder ObjectReferenceCollector(ReferredToObjects, pCurrentMesh.Value, false, false, false, false);
						//		ObjectReferenceCollector.FindReferences(pCurrentMesh.Value);

						//		if (ReferredToObjects.Num() == 1 && !UsedMeshes.Contains(pCurrentMesh.Value))
						//		{
						//			pCurrentMesh.Value->MarkPendingKill();
						//		}
						//	}

						//	SIFApplication::GetApplication()->GetMorphMergeCache().Empty();
						//}
					}
				}
				break;
			}

			m_gameWorldSetupState = SETUP_COMPLETED;
			// Intentional fall through to SETUP_COMPLETED
		}
		case SETUP_COMPLETED:
		{
#if PLATFORM_SWITCH
			GEngine->ForceGarbageCollection(false);
#endif
			/**/
			if (world_id == WORLD_ID::GAME)
			{
				SIFPlayerProfileManager *profile_manager = SIFPlayerProfileManager::GetInstance();
				bool b = false;
				bool s = false;
				if (profile_manager && profile_manager->GetMasterProfile())
				{
					const MabObservedValueList* profile_nvl = profile_manager->GetMasterProfile()->GetNamedValueList();
					if (profile_nvl)
					{
						b = profile_nvl->GetNamedValue(PLAYER_PROFILE_EXTRA1)->ToBoolean();
						s = profile_nvl->GetNamedValue(PLAYER_PROFILE_EXTRA2)->ToBoolean();
					}
				}

				for (unsigned int i = 0; i < all_players.size(); ++i)
				{
					ARugbyCharacter* character = all_players[i];
					if (character->GetAnimInstance())
					{
						character->GetAnimInstance()->headScale = b ? FVector(2.0f) : (s ? FVector(0.5f) : FVector(1.0f));
					}
				}
			}
			/**/

			if (!m_quickLoadActive && !m_teamSwapActive)
			{
				OnLoadingComplete();
			}
			else
			{
				if (m_quickLoadDelayedTeamSwapRequest != TEAM_SWAP_REQUEST::TEAM_SWAP_NONE)
				{
					//bool useCareerTeams = m_quickLoadDelayedTeamSwapRequest == TEAM_SWAP_REQUEST::TEAM_SWAP_CAREER;
					m_quickLoadDelayedTeamSwapRequest = TEAM_SWAP_REQUEST::TEAM_SWAP_NONE;

					int newHomeTeam = -1;
					int newAwayTeam = -1;

					if (GetPreferredSandboxTeams(newHomeTeam, newAwayTeam))
					{
						SwapSandboxTeams(newHomeTeam, newAwayTeam);
						break;
					}
				}
			}

			TriggerDelayedTransition();

			m_quickLoadActive = false;
			m_teamSwapActive = false;

			if (!world_is_revealed && !world_is_revealing)
			{
				HideWorld();
			}

			//RussellD: Consider adding additional setup before COMPLETED with.
			// BUT Don't touch this unless you understand the team swap code, which is a little janky
			/* Probe if we have anything to do
			UWorld* pGameWorld = GetGameInstance().GetWorld();
			if (pGameWorld)
			{
				pGameWorld->UpdateLevelStreaming();
				bool bWorkToDo = (pGameWorld->IsVisibilityRequestPending() || IsAsyncLoading());
				if (bWorkToDo)
				{
					break;
				}
			}
			//*/

			m_gameWorldSetupState = CHARACTER_INITIALISE_REPLAY;

			break;
		}
		case CHARACTER_INITIALISE_REPLAY:
		{
#ifndef DISABLE_REPLAYS

			//#afl_replay
			// Setup all players for replays
			if (GetWorldId() == WORLD_ID::GAME)
			{
				for (size_t i = 0; i < all_players.size(); i++)
				{
					if (!all_players[i]->IsValidLowLevel() || !all_players[i]->IsA(ARugbyCharacter::StaticClass()))
					{
						continue;
					}

					ARugbyCharacter* rugbyPlayer = Cast<ARugbyCharacter>(all_players[i]);
					rugbyPlayer->InitialiseReplay();
				}

				if (UOBJ_IS_VALID(ball))
				{
					ball->InitialiseReplay();
				}
			}

#endif

			m_gameWorldSetupState = SETUP_IDLE;

			break;
		}
		case SETUP_CANCELLED:
		{
			OnLoadingCancelled();
			m_gameWorldSetupState = SETUP_IDLE;
			break;
		}
		case SETUP_ERROR:
		{
			MABBREAKMSG("Error has been encountered during setup!");
			break;
		}
		default:
		{
			MABBREAKMSG("Unknown setup state!");
			break;
		}
	}

}

void SIFGameWorld::EstimateLoadCompletion()
{
	const int c_StadiumPortion = 7;
	const int c_TeamsPortion = 3;
	const int c_CustomisationPortion = 30;
	const int c_MorphMergePortion = 30;
	const int c_HeadShotGeneration = 30;
	const int c_StartingPercentage = 100
		- (c_StadiumPortion
		+ c_TeamsPortion
		+ c_CustomisationPortion
		+ c_MorphMergePortion
		+ c_HeadShotGeneration);

	int percentageComplete = c_StartingPercentage;

	if (m_gameWorldSetupState > STADIUM_LOAD_WAIT)
	{
		percentageComplete += c_StadiumPortion;
	}
	else if (m_gameWorldSetupState >= STADIUM_LOAD_WAIT)
	{
		//Check progress through stadium

		int stadiumChunksCount = stadium_chunks.size();
		int stadiumChunksCompleted = 0;

		for (MabVector<URugbyLevelChunk*>::reverse_iterator iter = stadium_chunks.rbegin(); iter != stadium_chunks.rend(); ++iter)
		{
			URugbyLevelChunk* pChunk = *iter;
			if (UOBJ_IS_VALID(pChunk) && pChunk->IsLoaded())
			{
				++stadiumChunksCompleted;
			}
		}

		percentageComplete += stadiumChunksCount == 0 ? c_StadiumPortion :
			FMath::Max(0, FMath::FloorToInt((float)c_StadiumPortion * (float)stadiumChunksCompleted / (float)stadiumChunksCount));
	}

	if (m_gameWorldSetupState > TEAMS_LOAD_WAIT)
	{
		percentageComplete += c_TeamsPortion;
	}
	else if (m_gameWorldSetupState >= TEAMS_LOAD_WAIT)
	{
		// Check progress through teams

		int teamChunksCount = m_characterMapChunks.Num();
		int teamChunksCompleted = 0;

		for (int i = 0; i < teamChunksCount; ++i)
		{
			URugbyLevelChunk* pChunk = m_characterMapChunks[i];
			if (UOBJ_IS_VALID(pChunk) && pChunk->IsLoaded())
			{
				++teamChunksCompleted;
			}
		}

		percentageComplete += teamChunksCount == 0 ? c_TeamsPortion :
			FMath::Max(0, FMath::FloorToInt((float)c_TeamsPortion * (float)teamChunksCompleted / (float)teamChunksCount));
	}

	if (//m_gameWorldSetupState > CHARACTER_CUSTOMISATIONS_LOAD_WAIT && 
		m_gameWorldSetupState > CHARACTER_MESH_MERGE_WAIT)
	{
		percentageComplete += c_CustomisationPortion + c_MorphMergePortion;
	}
	else if (m_gameWorldSetupState >= CHARACTER_CUSTOMISATIONS_LOAD_WAIT)
	{
		// Check progress through characters

		int playersCount = all_players.size();
		int playersCustomised = 0;
		int playersMorphed = 0;

		for (int i = 0; i < playersCount; ++i)
		{
			ARugbyCharacter* pCharacter = all_players[i];

			if (!UOBJ_IS_VALID(pCharacter))
			{
				continue;
			}

			if (pCharacter->HasPendingCustomisation())
			{
				continue;
			}

			++playersCustomised;

			if (pCharacter->m_ElementsLeftToMorphMerge < 1 && m_gameWorldSetupState >= CHARACTER_MESH_MERGE_WAIT)
			{
				++playersMorphed;
			}
		}

		if (playersCount == 0)
		{
			percentageComplete += c_CustomisationPortion + c_MorphMergePortion;
		}
		else
		{
			percentageComplete +=
				FMath::Max(0, FMath::FloorToInt((float)c_CustomisationPortion * (float)playersCustomised / (float)playersCount));

			percentageComplete +=
				FMath::Max(0, FMath::FloorToInt((float)c_MorphMergePortion * (float)playersMorphed / (float)playersCount));
		}
	}

	// Photos
	URUTeamFacesGenerator *pGenerator = mGameInstance.GetTeamFacesGenerator();

	if (!IsMatch() || !pGenerator || m_gameWorldSetupState > CHARACTER_FACE_RENDER_WAIT)
	{
		percentageComplete += c_HeadShotGeneration;
	}
	else
	{
		for (int i = 0; i < 2; ++i)
		{
			const FRugbyTeamSettings& teamSettings = game_settings.team_settings[i];
			int playersCount = teamSettings.lineup.size();
			int playersCompleted = 0;

			for (int j = 0; j < playersCount; ++j)
			{
				if (pGenerator->IsPlayerInCache(
					(int32)teamSettings.lineup[j].GetDbId(),
					(int32)teamSettings.team.GetDbId(),
					i))
				{
					++playersCompleted;
				}
			}

			percentageComplete += playersCount == 0 ? (c_HeadShotGeneration >> 1) :
				FMath::Max(0, FMath::FloorToInt((float)(c_HeadShotGeneration >> 1) * (float)playersCompleted / (float)playersCount));
		}
	}

	m_loadCompletionEstimate = percentageComplete;
	//UE_LOG(LogTemp, Log, TEXT("Load completion estimate: %d"), m_loadCompletionEstimate);
}

void SIFGameWorld::AbortWorldLoad()
{
	FreeAllocated();

	async_load_start_requested = ASYNC_LOAD_NONE;
	async_loading_started = false;
}




int SIFGameWorld::GetLoadCompletionEstimate()
{
	if (!m_isLoading)
	{
		if (!world_is_allocated)
		{
			return 0;
		}

		return 100;
	}

	return FMath::Clamp(m_loadCompletionEstimate, 0, 99);
}

void SIFGameWorld::DisablePlayers()
{

	//This should stop menu players from updating animations

	int32 numPlayers = GetAllPlayers().size();

	for (int32 i = 0; i < numPlayers; i++)
	{
		ARugbyCharacter* thePlayer = GetAllPlayers()[i];

		if (thePlayer && thePlayer->IsValidLowLevel())
		{
			if (thePlayer->GetAnimInstance())
			{
				thePlayer->GetAnimInstance()->ShouldSleep = true;
			}

			TArray<UActorComponent*> MeshList = GetAllPlayers()[i]->GetComponentsByClass(USkeletalMeshComponent::StaticClass());

			for (int32 MeshIndex = 0; MeshIndex < MeshList.Num(); MeshIndex++)
			{
				USkeletalMeshComponent* MeshPart = Cast< USkeletalMeshComponent>(MeshList[MeshIndex]);

				if (MeshPart)
				{
					MeshPart->SetComponentTickEnabled(false);
				}
			}
		}
	}
}

void SIFGameWorld::EnablePlayers()
{

	int32 numPlayers = GetAllPlayers().size();

	for (int32 i = 0; i < numPlayers; i++)
	{
		ARugbyCharacter* thePlayer = GetAllPlayers()[i];

		//Im hoping that this will stop bench players from updating
		if (thePlayer->GetAttributes()->GetPlayerPosition() > PP_REFEREE)
		{
			continue;
		}

		if (thePlayer && thePlayer->IsValidLowLevel())
		{
			if (thePlayer->GetAnimInstance())
			{
				thePlayer->GetAnimInstance()->ShouldSleep = false;
			}

			TArray<UActorComponent*> MeshList = GetAllPlayers()[i]->GetComponentsByClass(USkeletalMeshComponent::StaticClass());

			for (int32 MeshIndex = 0; MeshIndex < MeshList.Num(); MeshIndex++)
			{
				USkeletalMeshComponent* MeshPart = Cast< USkeletalMeshComponent>(MeshList[MeshIndex]);

				if (MeshPart)
				{
					MeshPart->SetComponentTickEnabled(true);
				}
			}
		}

	}
}

//===============================================================================
//===============================================================================
void SIFGameWorld::SetUnrealActorsPaused(const bool bEnabled)
{
	if (bEnabled)
	{
		PauseUnrealActors();
	}
	else
	{
		ResumeUnrealActors();
	}
}

//===============================================================================
//===============================================================================
void SIFGameWorld::PauseUnrealActors()
{
	//NOTE: This doesn't work as it prevent the UI from processing some inputs.
	//UGameplayStatics::SetGamePaused(mGameInstance.GetWorld(), true);

	// Unpause players
	const SIFRugbyCharacterList& playerList = GetAllPlayers();//we want to pause all actors here including bench
	for (int32 i = 0; i < playerList.size(); i++)
	{
		if (ARugbyCharacter* pCharacter = playerList[i])
		{
			pCharacter->SetPaused(true);
		}
	}

	if (cutscene_manager)
	{
		cutscene_manager->PauseAllCutScenes(); //this pauses the level sequence actors or the cutscenes.
	}

	// #todo: other in-game actors that should be resumed (e.g. flags)
	wwDO_NOTHING;
}

//===============================================================================
//===============================================================================
void SIFGameWorld::ResumeUnrealActors()
{
	//UGameplayStatics::SetGamePaused(mGameInstance.GetWorld(), false);

	// Pause players
	const SIFRugbyCharacterList& playerList = GetAllPlayers();//we want to resume all players on the field, excluding bench
	for (int32 i = 0; i < playerList.size(); i++)
	{
		if (ARugbyCharacter* pCharacter = playerList[i])
		{
			pCharacter->SetPaused(false);
		}
	}

	if (cutscene_manager)
	{
		cutscene_manager->ResumeAllCutScenes(); //this resumes the level sequence actors or the cutscenes.
	}

	// #todo: other in-game actors that should be paused (e.g. flags)
	wwDO_NOTHING;
}

///-------------------------------------------------------------------------------
/// DeferredFree
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::DeferredFree(PSSG::PNode* node, bool add_front)
{
	// Make sure we delete from nodes_added_to_scene!

	MabVector<SceneNodeEntry>::iterator iter2 = nodes_added_to_scene.begin();
	while (iter2 != nodes_added_to_scene.end())
	{
		SceneNodeEntry *entry = &(*iter2);
		if (entry->node == node)
		{
			nodes_added_to_scene.erase(iter2);
			break;
		}
		else
		{
			++iter2;
		}
	}

	// call app
	SIFApplication::GetApplication()->DeferredFree(node, add_front);
}
#endif

///-------------------------------------------------------------------------------
/// DeferredAddChild
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::DeferredAddChild(PSSG::PNode* parent, PSSG::PNode* node, unsigned int light_link_mask)
{
	if (world_is_revealed)
	{
		SIFApplication::GetApplication()->DeferredAddChild(parent, node, light_link_mask);
	}

	// NOTE (Owen): When parent is NULL it is assumed the intent is to detach the node from the scene graph.
	if (parent)
	{
		SceneNodeEntry entry(node, parent, light_link_mask);
		nodes_added_to_scene.push_back(entry);
	}
	else
	{
		// remove from the lighting list
		MabVector<SceneNodeEntry>::iterator iter2 = nodes_added_to_scene.begin();
		while (iter2 != nodes_added_to_scene.end())
		{
			SceneNodeEntry *entry = &(*iter2);
			if (entry->node == node)
			{
				nodes_added_to_scene.erase(iter2);
				break;
			}
			else
			{
				++iter2;
			}
		}
	}
}
#endif

///-------------------------------------------------------------------------------
/// DeferredAddToRoot
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::DeferredAddToRoot(SIFGraphicsHandle node, unsigned int light_link_mask)
{
	DeferredAddChild(GetSceneRootNode(), node, light_link_mask);
}
#endif

///-------------------------------------------------------------------------------
/// Render the scene
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::Render()
{
	if (!IsAwake())
	{
		return;
	}
	//isRendering.Lock();
	MABMEM_SCOPED_TAG(MEMTAG_GAMEWORLD_RENDER);

	// Update global ambient light shader parameter
	//{
	//	PSSG::PLightNode* ambient_light;
	//	GetLightDatabase()->getNearestLightsOfType(Vectormath::Aos::Vector3(0,0,0), PSSG::PLightNode::PE_LIGHTTYPE_AMBIENT, &ambient_light, 1);
	//	if (ambient_light)
	//	{
	//		Vectormath::Aos::Vector4 light_color = ambient_light->getIntensity() * ambient_light->getColor();
	//		PSSG::PSidheUbershader::setAmbientGlobal(light_color);
	//	}
	//}

	{

#ifdef ENABLE_SIF_DEBUG_DRAW
		if (ambient_point_cloud)
			ambient_point_cloud->DebugDraw(*GetViewManager()->GetDebugCamera()->GetPSSGCamera());
#endif
	}

	GetViewManager()->Render(game_context);

#if defined ENABLE_SIF_DEBUG_DRAW
	SIFDebug::GetDebugDraw()->Draw3D(GetViewManager()->GetDebugCamera());
#endif
	//isRendering.Unlock();
}
#endif

///-------------------------------------------------------------------------------
/// Get the post effect settings.
///-------------------------------------------------------------------------------
//
//SIFLevelPostSettings *SIFGameWorld::GetPostEffectSettings()
//{
//	return (SIFLevelPostSettings*)GetViewManager()->GetLevelPostSettings();
//}

///-------------------------------------------------------------------------------
/// Get the 'SIFView' camera.
///-------------------------------------------------------------------------------

ARugbyCameraActor* SIFGameWorld::GetWorldCamera() const
{
	// #rc3_legacy
	//const SIFView* view = GetViewManager()->GetCurrentView();
	//if (view) return view->Camera();

	URugbyGameInstance* gameInstance = SIFApplication::GetApplication();

	//Because we save the primary camera off in the game instance its best to use that, if some how that fails then we will try spawn one and use that.
	//This will stop broken cameras getting set on the world.
	if (UOBJ_IS_VALID(gameInstance->pPrimaryCamera))
	{
		return gameInstance->pPrimaryCamera;
	}
	else
	{
		//#FirstLocalPlayerControllerCamera Won't work without connected input device.
		ARugbyPlayerController* player = Cast<ARugbyPlayerController>(UGameplayStatics::GetPlayerController(mGameInstance.GetWorld(), 0));

		if (player && player->IsValidLowLevel())
		{
			player->SpawnCamera();

			if (UOBJ_IS_VALID(gameInstance->pPrimaryCamera))
			{
				return gameInstance->pPrimaryCamera;
			}
		}
	}


	return nullptr;
}

///-------------------------------------------------------------------------------
/// Move the ref + touch judges to their start positions...
///-------------------------------------------------------------------------------

void SIFGameWorld::SetOfficialsKickOffPositions()
{
	if (officials != NULL)
	{
		SIFRugbyCharacterList team_players = officials->GetPlayers();

		FieldExtents extents = GetSpatialHelper()->GetFieldExtents();
		extents.x *= 0.5f;

		for (size_t p = 0; p < team_players.size(); ++p)
		{
			ARugbyCharacter* player = team_players[p];
			RUPlayerMovement *plr_movement = player->GetMovement();
			FVector target_pos(0, 0, 0);

			switch (player->GetAttributes()->GetPlayerPosition())
			{
				case PP_TOUCHJUDGE_LEFT:
				target_pos.x = -extents.x;
				target_pos.z = 5.0f;			// TEMP. Get out of the cutscene!
				break;
				case PP_TOUCHJUDGE_RIGHT:
				target_pos.x = extents.x;
				target_pos.z = 5.0f;			// TEMP. Get out of the cutscene!
				break;
				case PP_TOUCHJUDGE_NORTH:
				target_pos.x = 0.0f;
				target_pos.z = extents.y;			// TEMP. Get out of the cutscene!
				break;
				case PP_TOUCHJUDGE_SOUTH:
				target_pos.x = 0.0f;
				target_pos.z = -extents.y;			// TEMP. Get out of the cutscene!
				break;

				default:
				break;
			}

			float tf = plr_movement->GetTargetFacingAngle();

			player->GetActionManager()->AbortAllActions();
			plr_movement->StopAllMovement();

			plr_movement->SetCurrentPosition(target_pos);
			wwNETWORK_TRACE_JG_DISABLED("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
			plr_movement->SetTargetPosition(target_pos);
			plr_movement->SetTargetFacingAngle(tf);
			plr_movement->SetCurrentFacingAngle(tf);
		}
	}
}


void SIFGameWorld::ResetContextBucket()
{
	if (emotion_engine_manager)
	{
		emotion_engine_manager->Reset();
		emotion_engine_manager->GetContextBucket()->ClearEvents();
	}

#ifdef ENABLE_RUGED
	MabBATSContext *bats_context = SIFDebug::GetBATSContext();
	RUCBDebugService *rucb_service = NULL;

	if (bats_context != NULL)
		rucb_service = (RUCBDebugService*)bats_context->GetService("RUCBDEBUG");

	if (rucb_service != NULL)
		rucb_service->CreateNewBucket(0);
#endif
}




///***********************************************************************************************************************************************
///***********************************************************************************************************************************************
///***********************************************************************************************************************************************



///-------------------------------------------------------------------------------
/// SIFVisitorUpdateLights: passed as argument to traverseRenderInstances(...)
///   - Traverses through all of the PRenderInstances of a node, and calls
///     UpdateLights(..) on each instance.
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
struct SIFVisitorUpdateLights
{
	PSSG_PRIVATE_ASSIGNMENT_OPERATOR(SIFVisitorUpdateLights)

		SIFBruteForceLightDatabase *lights;

public:
	SIFVisitorUpdateLights(SIFBruteForceLightDatabase *llights) : lights(llights) {}

	void visit(PSSG::PRenderInstance& render_instance)
	{
		PSSGMabRenderInstance *re = PSSG::PSSGCast<PSSGMabRenderInstance>(&render_instance);
		if (re != NULL)
		{
			re->UpdateLights(lights);
		}
	}
};
#endif

///-------------------------------------------------------------------------------
/// Create a light-node.
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
PSSG::PLightNode* SIFGameWorld::CreateLight()
{
	PSSG::PDatabaseWriteLock env_lock(GetSceneDatabaseId());
	PSSG::PDatabase *env_database = env_lock.getDatabase();
	PSSG::PListIterator scenes(env_database->getSceneList());
	//PSSG::PRootNode* visual_scene_root = (PSSG::PRootNode*)scenes.data();		// get first scene root for rendering

	PSSG::PLightNode *light = env_database->createObject<PSSG::PLightNode>(PSSG::PDatabaseUniqueNameHelper(*env_database, "sgwlight"));
	if (light != NULL)
	{
		light->setColor(1.0f, 0.0f, 0.0f);
		light->setType(PSSG::PLightNode::PE_LIGHTTYPE_POINT);
		light->setIntensity(5.0f);

		SIFShadowMap::SetShadowCaster(*light, false);
	}
	return light;
}
#endif

///-------------------------------------------------------------------------------
/// Add a light node to the scene.
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::AddLight(PSSG::PLightNode *light, unsigned int light_link_mask)
{
	GetLightDatabase()->AddLight(light, light_link_mask);
	RelightScene();
}
#endif

///-------------------------------------------------------------------------------
/// Remove a light node from the scene.
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::RemoveLight(PSSG::PLightNode *light)
{
	GetLightDatabase()->RemoveLight(light);
	RelightScene();
	MabMemDelete(light);
}
#endif

///-------------------------------------------------------------------------------
/// Re-light the scene.
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::RelightScene()
{
	PSSG::PDatabaseWriteLock env_lock(GetSceneDatabaseId());
	PSSG::PDatabase* env_database = env_lock.getDatabase();
	PSSG::PListIterator scenes(env_database->getSceneList());
	PSSG::PRootNode* visual_scene_root = (PSSG::PRootNode*)scenes.data();		// get first scene root for rendering
	SIFVisitorUpdateLights relight(GetLightDatabase());
	SIFPSSGTraversals::traverseRenderInstances(relight, *(PSSG::PNode*)visual_scene_root);
}
#endif

///-------------------------------------------------------------------------------
/// Relight the scene in the next SyncUpdate.
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::SetLightsModified()
{
	relight_scene = true;
}
#endif

///-------------------------------------------------------------------------------
/// Called from UI when the team line up pages have been completed.
///-------------------------------------------------------------------------------

void SIFGameWorld::TeamLineupComplete()
{
	//#rc3_legacy
	//cutscene_manager->SetDisableCutSceneSkip(false);
}


///-------------------------------------------------------------------------------
/// Force a restart kick.
///-------------------------------------------------------------------------------

void SIFGameWorld::RestartKick()
{
	RUGameState* state = GetGameState();
	//RUGameEvents* events = GetEvents();

	state->SetKickRestartKickType(KICKTYPE_KICKOFF);
	state->SetPhase(RUGamePhase::KICK_OFF);

	MabString message(32, "Taken by %s", state->GetAttackingTeam()->GetDbTeam().GetName());
	GetHUDUpdater()->SetScreenMessage("[ID_KICK_OFF]", "[ID_KICK_OFF]", message.c_str(), 1.0f, 3.0f);

	events.get()->commentary_restart(state->GetBallHolder(), GetBall()->GetCurrentPosition());
	events.get()->restart_on_the_full(GetGameState()->GetBallHolder());
	events.get()->kick_restart();
	events.get()->kick_restart_start();

	rules->EnableTriggers(RURT_MAINGAMERULES);
}

///-------------------------------------------------------------------------------
/// Triggers and async load for a player.
///-------------------------------------------------------------------------------

void SIFGameWorld::ApplyCustomisationAsync(ARugbyCharacter* player, const PlayerCustomisationInfo& csinfo, bool do_customisation)
{
	m_asyncTaskQueue.Add(new RugbyAsyncTask::CustomisePlayerTask(player, csinfo, do_customisation));
}

///-------------------------------------------------------------------------------
/// Hide/un-hide all players.
///-------------------------------------------------------------------------------

void SIFGameWorld::SetPlayersVisible(bool visible, RUTeam* team)
{
	const SIFRugbyCharacterList& player_list = team == NULL ? players : team->GetPlayers();
	for (SIFRugbyCharacterList::const_iterator iter = player_list.begin(); iter != player_list.end(); ++iter)
	{
		ARugbyCharacter* player = *iter;
		if (player->GetAttributes()->IsActive())
			player->SetActorHiddenInGame(!visible);
	}

	cutscene_manager->DisableVisibilityUpdate();
}

///-------------------------------------------------------------------------------
/// Enable/Disable in game facial animations for all players.
///-------------------------------------------------------------------------------
void SIFGameWorld::SetAllPlayerFacials(bool enabled)
{
	for (SIFRugbyCharacterList::const_iterator iter = players.begin(); iter != players.end(); ++iter)
	{
		ARugbyCharacter* player = *iter;
		RUPlayerAnimation* player_animation = player->GetAnimation();
		if (player_animation)
			player_animation->SetVariable(player_animation->GetFacialAnimationVariable(), enabled ? 1.0f : 0.0f);
	}
}

///-------------------------------------------------------------------------------
/// Allocate the game world - MabResource interface implementation
///-------------------------------------------------------------------------------

bool SIFGameWorld::Allocate(MABMEM_HEAP /*heap*/, bool /*asynchronous*/, FSimpleDelegate onLoadingComplete)
{
	if (world_is_allocated)
	{
		UE_LOG(LogTemp, Log, TEXT("Cannot Allocate SIFGameWorld because it has already been allocated"));
		return false;
	}

#if defined(ENABLE_SYNCD_RAND_TRACKING) && WRITE_POS_TO_FILE
	if (world_id == WORLD_ID::GAME)
	{
		if (SIFMatchmakingHelpers::IsHost())
		{
			pos_file.open("W:/posA.txt", std::fstream::out | std::fstream::trunc);
			//rand_file.close();
		}
		else
		{
			pos_file.open("W:/posB.txt", std::fstream::out | std::fstream::trunc);
			//rand_file.close();
		}
	}
#endif

	world_state = WORLD_STATE::ALLOCATING;

	m_isLoading = true;
	m_loadCompletionEstimate = 0;

	if (onLoadingComplete.IsBound())
	{
		m_onLoadingComplete.Add(onLoadingComplete);
	}

	//*****************************************************
	// RussellD : MOVED THE BELOW CODE FROM THE CONTRUCTOR!

	// Reset time sources
	simulation_time->SetCurrentStep(MabTimeStep(0, SIMULATION_INTERVAL, 0));
	simulation_time->SetAccumulationMode(game_settings.game_settings.network_game ? MabLockStepTimeSource::ACCUMULATE_LOCK_STRICT : MabLockStepTimeSource::ACCUMULATE_VARIABLE);

	// Setup RNG
	random_number_generator = MabMemNew(heap) RURandomNumberGenerator();
	random_number_generator->SetSeed(game_settings.initial_random_seed);

#ifdef ENABLE_SYNCD_RAND_TRACKING
	random_number_generator->match_world = IsMatch();
#endif // ENABLE_SYNCD_RAND_TRACKING

	if (!game_settings.game_settings.network_game) random_number_generator->SetAssertIfUsed(false); //Stop asserts on menu world.
	random_number_generator->SetAssertOnNoThreadLock(game_settings.game_settings.network_game);
	if (game_settings.game_settings.network_game) random_number_generator->LockToThread(MabThread::GetCurrent());
	UE_LOG(LogNetwork, Display, TEXT("Initializing with random seed %d"), game_settings.initial_random_seed);

	// setup all the classes with commonality between sandbox and main game
	InitialiseCommon();

	// setup the weather for the game	
	InitialiseWeather();

	switch (game_settings.game_settings.game_type)
	{
		case GAME_TRAINING:
		InitialiseSandbox(nullptr); //#rc3_legacy InitialiseSandbox( control_action_manager );
		break;
		case GAME_MENU:
		InitialiseMenu();
		break;
		default:
		InitialiseMain();
		break;
	}

	// Set up the part of the HUD that can be setup this early, before UI nodes are attached
	//#rc3_legacy 
	Initialise3DHUD( /*control_action_manager*/);
	input_manager = std::make_unique<SSInputManager>(nullptr, this, hud3d);// MabMemNew(heap) SSInputManager(control_action_manager, this, hud3d);

	if (game_settings.game_settings.network_game) random_number_generator->LockToThread(0);

#ifdef ENABLE_AUTOLOAD_GAME_SETTINGS
	SIFDebug::GetGameDebugSettings()->LoadConfig();
#endif

	// RussellD : MOVED THE ABOVE CODE FROM THE CONTRUCTOR!
	//*****************************************************


	if (game_settings.game_settings.game_type == GAME_TRAINING || game_settings.game_settings.game_type == GAME_MENU)
	{
		RL3DatabaseScopedLock database_lock;

		//#rc3_legacy
		// int index = SIFUIHelpers::GetCurrentMasterController();
		// if(index!=-1)
		// 	game_settings.human_player_settings[0].controller_index = index;

		game_settings.num_active_teams = 2;

		//SIFApplication *app = SIFApplication::GetApplication();
		//game_settings.team_settings[0].LoadTeam(app->GetDefaultTeamA());
		//game_settings.team_settings[1].LoadTeam(app->GetDefaultTeamB());

		m_bForceReloadTeam = false;

		int teamA; int teamB;
		GetPreferredSandboxTeams(teamA, teamB);

		if (game_settings.game_settings.game_type == GAME_MENU)
		{
			TMap<int, unsigned short> teamOverrideMaps[2];

			for (int i = 0; i < 2; ++i)
			{
				const TArray<PlayerOverride>& teamOverrides = MENU_WOMEN_OVERRIDES[i];

				for (int j = 0; j < teamOverrides.Num(); ++j)
				{
					teamOverrideMaps[i].Add(
						teamOverrides[j].LineupIndex,
						teamOverrides[j].OverrideDbId);
				}
			}

			game_settings.team_settings[0].LoadTeam(teamA, teamOverrideMaps[0]);
			game_settings.team_settings[1].LoadTeam(teamB, teamOverrideMaps[1]);
		}
		else
		{
			game_settings.team_settings[0].LoadTeam(teamA);
			game_settings.team_settings[1].LoadTeam(teamB);
		}

		RUCareerModeManager *career_manager = SIFApplication::GetApplication()->GetCareerModeManager();
		bool in_career = career_manager && career_manager->IsActive();
		if (!in_career)
		{
			game_settings.NormaliseStats();
		}
	}

	// creates the teams/players/balls which may or may not be required,
	// dependent on changing game settings - sandbox handles this itself
	InitialiseActors();

	//Network game triggers
	if (game_settings.game_settings.network_game)
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		if (pRugbyGameInstance)
		{
			UWorld* pCurrentWorld = pRugbyGameInstance->GetWorld();

			if (pCurrentWorld)
			{
				ARugbyGameState* pRugbyGameState = Cast<ARugbyGameState>(pCurrentWorld->GetGameState());
				if (pRugbyGameState)
				{
					// Voip triggers for network games.
					// Enable voip at first and second half.
					GetEvents()->first_half_start.Add(pRugbyGameState, &ARugbyGameState::EnableVoip);
					GetEvents()->second_half_start.Add(pRugbyGameState, &ARugbyGameState::EnableVoip);

					// Disable voip at half time and game end.
					GetEvents()->half_time.Add(pRugbyGameState, &ARugbyGameState::DisableVoip);
					GetEvents()->finish.Add(pRugbyGameState, &ARugbyGameState::DisableVoip);
				}

				//Switch works a bit different, for Switch we use this to control friend play joinablitly
#if PLATFORM_SWITCH == 0
				//real time multilayer										
				// EnableRealTimeMultiPlayer at first and second half.
				GetEvents()->first_half_start.Add(pRugbyGameInstance, &URugbyGameInstance::EnableRealTimeMultiPlayer);
				GetEvents()->second_half_start.Add(pRugbyGameInstance, &URugbyGameInstance::EnableRealTimeMultiPlayer);

				// DisableRealTimeMultiPlayer at half time and game end.
				GetEvents()->half_time.Add(pRugbyGameInstance, &URugbyGameInstance::DisableRealTimeMultiPlayer);
				GetEvents()->full_time.Add(pRugbyGameInstance, &URugbyGameInstance::DisableRealTimeMultiPlayer);

#endif  // PLATFORM_SWITCH





			}
		}
	}

	m_gameWorldSetupState = STADIUM_LOAD;

	world_is_allocated = true;
	world_state = WORLD_STATE::ALLOCATED;

	return true;
}

///-------------------------------------------------------------------------------
/// De-allocate the game world - MabResource interface implementation
///-------------------------------------------------------------------------------

void SIFGameWorld::FreeAllocated()
{
	if (!world_is_allocated)
	{
		UE_LOG(LogTemp, Log, TEXT("Cannot FreeAllocate SIFGameWorld because it hasn't been allocated yet"));
		return;
	}

	// If we have a timer running to finalise a wake, we really don't want this firing any more.
	if (WorldReadyForPlayHandle.IsValid())
	{
		SIFApplication::GetApplication()->GetTimerManager().ClearTimer(WorldReadyForPlayHandle);
	}

	// Delete all the cached mesh merged components which we kept cached.
	if (IsMatch())
	{
		for (auto& pCurrentMesh : SIFApplication::GetApplication()->GetMeshMergeCache())
		{
			pCurrentMesh.Value->MarkPendingKill();
		}

		SIFApplication::GetApplication()->GetMeshMergeCache().Empty();
	}

	// Delete all the cached mesh merged components which we kept cached.
	if (IsMatch())
	{
		for (auto& pCurrentMesh : SIFApplication::GetApplication()->GetMorphMergeCache())
		{
			pCurrentMesh.Value->MarkPendingKill();
		}

		SIFApplication::GetApplication()->GetMorphMergeCache().Empty();
	}

	delayed_awaken = 0;

	world_state = WORLD_STATE::FREEING;

	//#rc3_legacy RU_NETWORK_REPLAY_FINISH_GAME();

	SIFApplication *application = SIFApplication::GetApplication();

	m_asyncTaskQueue.AbortAll();

	/*#rc3_legacy

	/// Clear all pending loads + allow current to finish.
	application->GetAsyncLoadThread()->Abort();

	// Re-enable rendering if booted out of game quickly.
	if(!reenabled_rendering)
	{
		SIFApplication::GetApplication()->SetSuppressRenderingNoThreadSwitch(false);
		reenabled_rendering = true;
	}

	SetGraphicsNodesAttached(true);				// If world is disabled, re-attach temporarily.

	//RUTeamFacesGenerator *tf_generator = application->GetTeamFacesGenerator();
	//tf_generator->OnGameWorldFree(this);

	*/


	if (game_settings.game_settings.network_game)
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		if (pRugbyGameInstance)
		{
			UWorld* pCurrentWorld = pRugbyGameInstance->GetWorld();

			if (pCurrentWorld)
			{
				ARugbyGameState* pRugbyGameState = Cast<ARugbyGameState>(pCurrentWorld->GetGameState());
				if (pRugbyGameState)
				{
					// Voip triggers for network games.
					// Enable voip at first and second half.
					GetEvents()->first_half_start.Remove(pRugbyGameState, &ARugbyGameState::EnableVoip);
					GetEvents()->second_half_start.Remove(pRugbyGameState, &ARugbyGameState::EnableVoip);

					// Disable voip at half time and game end.
					GetEvents()->half_time.Remove(pRugbyGameState, &ARugbyGameState::DisableVoip);
					GetEvents()->finish.Remove(pRugbyGameState, &ARugbyGameState::DisableVoip);
				}


#if PLATFORM_SWITCH == 0
				//real time multilayer										
	// EnableRealTimeMultiPlayer at first and second half.
				GetEvents()->first_half_start.Remove(pRugbyGameInstance, &URugbyGameInstance::EnableRealTimeMultiPlayer);
				GetEvents()->second_half_start.Remove(pRugbyGameInstance, &URugbyGameInstance::EnableRealTimeMultiPlayer);

				// DisableRealTimeMultiPlayer at half time and game end.
				GetEvents()->half_time.Remove(pRugbyGameInstance, &URugbyGameInstance::DisableRealTimeMultiPlayer);
				GetEvents()->full_time.Remove(pRugbyGameInstance, &URugbyGameInstance::DisableRealTimeMultiPlayer);

#endif  // PLATFORM_SWITCH

			}
		}
	}

	if (UOBJ_IS_VALID(m_pPauseHandler))
	{
		m_pPauseHandler->Uninitialise();

		GetGameInstance().AllowObjectGC(m_pPauseHandler);
		if (m_pPauseHandler->IsRooted())
		{
			m_pPauseHandler->MarkPendingKill();
		}
	}
	m_pPauseHandler = nullptr;

	random_number_generator->SetAssertIfUsed(false);

	// update queue on app
	application->UpdateDeferredQueue();
#if PLATFORM_XBOX360
	application->UpdateDeferredQueue();
#endif

	crowd_manager = nullptr;
	sky_manager = nullptr;
	stadium_overloading_manager = nullptr;
	field_manager = nullptr;

	if (m_signage_actor_async_spawn_handle.IsValid())
	{
		m_signage_actor_async_spawn_handle->CancelHandle();
		m_signage_actor_async_spawn_handle.Reset();
	}

	/*#rc3_legacy
	PSSG::PDatabaseWriteLock level_write_lock(GetSceneDatabaseId());
	// Iterate through the databases clearing all lights from the shaders
	//PSSG::PListIterator scenes(level_database->getSceneList());
	//PSSG::PRootNode* visual_scene_root = (PSSG::PRootNode*)scenes.data();

	//SIFClearShaderLights clear_shaders;
	//SIFPSSGTraversals::traverseRenderInstances( clear_shaders, *visual_scene_root );


	*/

	cutscene_manager->StopAllCutScenes();

	screen_wipe_manager->CleanUpWipeManager();

	game_state->Reset();

	//Because of the reasons below, I have moved the cleanup to here, where we still have players. -JG
	//This will call RUGamePhase::OnPhaseChanged() then Reset then complain there are no teams in this world.
	//I have added a check to RUGamePhase::Reset to not check teams when we are freeing.
	//But potentially this game_state cleanup should happen earlier when we have teams?
	game_state->Cleanup();

	/*#rc3_legacy

	UndoAllTextureSwaps();

	*/

	CleanupActors();

	SIFApplication::GetApplication()->UpdateDeferredQueue();
#if PLATFORM_XBOX360			// XBox360 double defers, so need to call twice.
	SIFApplication::GetApplication()->UpdateDeferredQueue();
#endif

	/*#rc3_legacy

	// Clear any pending attached particle systems
	particles_manager->ClearSystemAttachQueues();
	particles_manager->ClearAllParticles();

	*/

	RUCareerModeManager* const career_mode_mgr = SIFApplication::GetApplication()->GetCareerModeManager();
	if (career_mode_mgr && career_mode_mgr->IsActive()) career_mode_mgr->OnGameWorldDestroyed(GetEvents());

	// Clean up & release RUStadiumManager
	GetStadiumManager()->Cleanup();

	// HACK - Network play pauses the sim time, so we should un pause it. ABROWN
	if (game_settings.game_settings.network_game)
	{
		simulation_time->Pause(false);
	}

	// HACK: reset some settings as we exit game
	game_settings.game_settings.is_grand_final = false;
	game_settings.game_settings.extra_time_enabled = false;

	// If any of the following destructor's require resources to be released they must
	// have been dealt with before here i.e. cutscene_manager calls StopAllCutScenes() prior
	// to this then calls destructor after
	// cleanup queue on app
	SIFApplication::GetApplication()->UpdateDeferredQueue();
#if PLATFORM_XBOX360			// XBox360 double defers, so need to call twice.
	SIFApplication::GetApplication()->UpdateDeferredQueue();
#endif

	/*#rc3_legacy
	MabMemDelete(world_audio);*/
	MabMemDelete(rumble_manager);
	rumble_manager = nullptr;
	/*#rc3_legacy
	MabMemDelete(substitution_manager);
	MabMemDelete(team_confidence_handler);*/

	//#rc3_legacy
	//MabMemDelete(cutscene_manager);
	cutscene_manager.reset();

	//Going to reset this here, hoping this will cover the memory corruption i have.
	role_factory.reset();

	MabMemDelete(weather_manager);
	weather_manager = nullptr;
	/*MabMemDelete(effect_system);
	MabMemDelete(role_factory);

	GetPssgAnimationManager()->Cleanup();

	MabMemDelete(shader_parameters);
	MabMemDelete(jumbotrons);
	MabMemDelete(ambient_point_cloud);
	MabMemDeleteSafe(crowd_manager);
	MabMemDelete(particles_manager);
	MabMemDelete(game_context);	// Note: Anything deleted below must not depend on SIFGameContext!
	MabMemDelete(camera_effects);
	MabMemDelete(stats_tracker);
	*/
	MabMemDelete(camera_manager);
	camera_manager = nullptr;
	/*#rc3_legacy
	MabMemDelete(input_manager);
	MabMemDelete(strategy_helper);
	MabMemDelete(tackle_helper);
	MabMemDelete(game_gtb);
#ifdef ENABLE_RU_TEST_BED
	MabMemDelete(test_bed_manager);
#endif
	MabMemDelete(tutorial_manager);
	MabMemDelete(game_timer);
	MabMemDelete(game_state);
	MabMemDelete(movement);
	MabMemDelete(spatial_helper);*/
	/*MabMemDelete(rules);
	MabMemDelete(post_effects_manager);
	*/

	// Clean up the network state.
	MabMemDelete(network_state);
	network_state = nullptr;

	screen_wipe_manager.reset();

	game_state.reset();
	input_manager.reset();

	MabMemDeleteSafe(game_stats2bucket);
	MabMemDelete(emotion_engine_manager);

#ifdef ENABLE_RUGED
	MabMemDelete(stats_test_manager);
#endif

	replay_manager.reset();

	MabMemDeleteSafe(hud3d);
	MabMemDeleteSafe(hud_updater);
	MabMemDeleteSafe(hud_updater_contextual);
	MabMemDeleteSafe(hud_updater_strategy[0]);
	MabMemDeleteSafe(hud_updater_strategy[1]);
	/*#rc3_legacy
	MabMemDeleteSafe(replay_hud_updater);
	MabMemDeleteSafe(replay_manager);

	MabMemDelete(animation_manager);
	MabMemDelete(animation);
	MabMemDelete(locomotion);
	MabMemDelete(lineout_rater);
	animation_resource->RemoveRef();
	animation_resource = NULL;
	*/

	URugbyLevelManager* levelManager = application->GetLevelManager();
	for (MabVector<URugbyLevelChunk*>::reverse_iterator iter = stadium_chunks.rbegin(); iter != stadium_chunks.rend(); ++iter)
	{
		URugbyLevelChunk* subLevel = *iter;
		if (UOBJ_IS_VALID(subLevel))
		{
			subLevel->m_OnLevelShow.Clear();
			levelManager->DestroyLevelChunk(subLevel);
		}
	}
	stadium_chunks.clear();


	if (ball)
	{
		ball->Destroy();
		ball = NULL;
	}

	/*#rc3_legacy
	for(MabVector<PSSG::PNode*>::reverse_iterator iter = node_clean_up.rbegin(); iter != node_clean_up.rend(); ++iter)
	{
		RemoveFromScene(*iter);
	}
	*/

	SIFApplication::GetApplication()->UpdateDeferredQueue();
#if PLATFORM_XBOX360			// XBox360 double defers, so need to call twice.
	SIFApplication::GetApplication()->UpdateDeferredQueue();
#endif

	/*#rc3_legacy
	node_clean_up.clear();

	level_write_lock.release();

	//MABLOGDEBUG("-------------- FreeAllocated ------------------");
	//PrintSceneGraph();
	//MABLOGDEBUG("");
	//PSSGMabGfx::PrintLoadedDatabases();
	//MABLOGDEBUG("-----------------------------------------------");

	//MABLOGDEBUG("-------------- STADIUM_RESOURCES ------------------");
	//for(MabVector<MabResourceBase*>::iterator iter=stadium_resources.begin(); iter!=stadium_resources.end(); ++iter)
	//{
	//	MabResourceBase* temp = *iter;
	//	MABLOGDEBUG("(%d) %s %s ", temp->GetRefCount() ,temp->GetName(), temp->GetObjectName());
	//}
	//MABLOGDEBUG("-------------- STADIUM_RESOURCES (END) ------------------");

	for(MabVector<MabResourceBase*>::iterator iter=stadium_resources.begin(); iter!=stadium_resources.end(); ++iter)
	{
		MabResourceBase* temp = *iter;
//		MABLOGDEBUG("Removing: (%d) %s %s ", temp->GetRefCount() ,temp->GetName(), temp->GetObjectName());
		temp->RemoveRef();
	}
	stadium_resources.clear();

	*/

	// Initialise game settings for next game

	RL3Database *rl3_database = SIFApplication::GetApplication()->GetGameDatabaseManager()->GetRL3Database();
	rl3_database->Lock();
	game_settings.Reset(random_number_generator->GetSeed());
	rl3_database->Unlock();

	SIFApplication::GetApplication()->GetAchievementChecker()->RemoveGameEvents(this);


	MabMemDelete(random_number_generator);
	/*#rc3_legacy
	MabMemDelete(object_database);
	MabMemDelete(player_filters);

	SIFApplication::GetApplication()->GetRugbyDollarsChecker()->RemoveGameEvents(this);

#ifndef DISABLE_COMMENTARY
	// Detach commentary
	application->GetCommentarySystem()->AttachGameWorld(NULL);
#endif

	*/

	//#afl_replay
	if (GetWorldId() == WORLD_ID::GAME)
	{
		events->game_restart.Remove(this, &SIFGameWorld::InitialiseReplayManager);
	}

	events->game_restart.Remove(this, &SIFGameWorld::ResetGraphicsObjects);

	events->cutscene_start.Remove(this, &SIFGameWorld::OnCutsceneStart);
	events->cutscene_finish.Remove(this, &SIFGameWorld::OnCutsceneEnd);

	/*#rc3_legacy

	//events->DebugPrint();
	MabMemDelete(events);			// Must delete last.

	//#endif

//	SIFApplication::GetApplication()->GetEVDS()->FreeAllContainers();		// Shouldn't need.
	application->GetWindowSystem()->GetLuaInterpreter()->ForceGarbageCollection();

	*/

	/// Perge unused strings from string pool.
	MabGlobalStringPool::GetInstance()->PurgeUnusedStrings();

	async_load_start_requested = ASYNC_LOAD_NONE;
	async_loading_started = false;

	world_is_allocated = false;

	world_is_revealing = false;
	world_is_revealed = false;

	world_state = WORLD_STATE::FREED;
	initial_state_checked = false;

	m_gameWorldSetupState = SETUP_IDLE;
}


///-------------------------------------------------------------------------------
/// Detach/attach all graphics nodes for this world.
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::SetGraphicsNodesAttached(bool enabled)
{
	MABUNUSED(enabled);

	if (enabled != world_is_visible)
	{
		SIFApplication *app = SIFApplication::GetApplication();

		MABLOGDEBUG("SetGraphicsNodesAttached: %u", (unsigned int)nodes_added_to_scene.size());

		if (enabled)
		{
			for (MabVector<SceneNodeEntry>::iterator it = nodes_added_to_scene.begin(); it != nodes_added_to_scene.end(); ++it)
			{
				SceneNodeEntry *entry = &(*it);

				if (!(entry->node == main_menu_lights && using_training_lights) && !(entry->node == training_lights && !using_training_lights))
				{
					// Attach node to scene graph
					app->DeferredAddChild(entry->parent, entry->node, entry->light_link_mask);
				}
			}
		}
		else
		{
			for (MabVector<SceneNodeEntry>::reverse_iterator it = nodes_added_to_scene.rbegin(); it != nodes_added_to_scene.rend(); ++it)
			{
				SceneNodeEntry *entry = &(*it);

				if (!(entry->node == main_menu_lights && using_training_lights) && !(entry->node == training_lights && !using_training_lights))
				{
					// Detach node from scene graph
					app->DeferredAddChild(NULL, entry->node, entry->light_link_mask);
				}

			}
		}

		world_is_visible = enabled;
	}
}
#endif


bool SIFGameWorld::AsyncLoadStadiumMaps()
{
	URugbyLevelManager* pLevelManager = GetGameInstance().GetLevelManager();

	bool result = true;

	for (MabVector<URugbyLevelChunk*>::reverse_iterator iter = stadium_chunks.rbegin(); iter != stadium_chunks.rend(); ++iter)
	{
		URugbyLevelChunk* footballerLevelChunk = *iter;
		if (!UOBJ_IS_VALID(footballerLevelChunk))
		{
			result = false;
			continue;
		}

		pLevelManager->LoadLevelChunk(footballerLevelChunk);
	}

	return result;
}

bool SIFGameWorld::AreStadiumMapsLoaded()
{
	for (MabVector<URugbyLevelChunk*>::reverse_iterator iter = stadium_chunks.rbegin(); iter != stadium_chunks.rend(); ++iter)
	{
		URugbyLevelChunk* footballerLevelChunk = *iter;
		if (UOBJ_IS_VALID(footballerLevelChunk))
		{
			if (!footballerLevelChunk->IsLoaded())
			{
				if (footballerLevelChunk->GetLevelStreaming() && footballerLevelChunk->GetLevelStreaming()->GetCurrentState() == ULevelStreaming::ECurrentState::FailedToLoad)
				{
					UE_LOG(LogTemp, Warning, TEXT("SIFGameWorld::AreStadiumMapsLoaded Waiting on a level chunk that failed to load!"));
					ensureMsgf(footballerLevelChunk->GetLevelStreaming()->GetCurrentState() == ULevelStreaming::ECurrentState::FailedToLoad,
						TEXT("We are curretly waiting on a level chunk which failed to load. This usually means the chunk is loaded by something else. This can cause issues hosting online where it times out waiting for the sandbox load."));
					return false;
				}
				else
				{
					return false;
				}
			}
		}
	}

	return true;
}

bool SIFGameWorld::InitialiseFootballerMaps()
{
	// Kick off an async level load of rugby characters to store
	bool result_side_a = InitialiseFootballerMap(SIDE_A);
	check(result_side_a);
	bool result_side_b = InitialiseFootballerMap(SIDE_B);
	check(result_side_b);
	bool result_side_o = InitialiseFootballerMap(SIDE_OFFICIALS);
	check(result_side_o);

	return result_side_a && result_side_b && result_side_o;
}

bool SIFGameWorld::AsyncLoadFootballerMaps()
{
	// Kick off an async level load of rugby characters to store
	bool result_side_a = AsyncLoadFootballerMap(SIDE_A);
	check(result_side_a);
	bool result_side_b = AsyncLoadFootballerMap(SIDE_B);
	check(result_side_b);
	bool result_side_o = AsyncLoadFootballerMap(SIDE_OFFICIALS);
	check(result_side_o);

	return result_side_a && result_side_b && result_side_o;
}

bool SIFGameWorld::AreFootballerMapsLoaded()
{
	if (UOBJ_IS_VALID(m_characterMapChunks[SIDE_A]) && (!m_characterMapChunks[SIDE_A]->IsLoaded() || !m_characterMapChunks[SIDE_A]->IsVisible()))
		return false;

	if (UOBJ_IS_VALID(m_characterMapChunks[SIDE_B]) && (!m_characterMapChunks[SIDE_B]->IsLoaded() || !m_characterMapChunks[SIDE_B]->IsVisible()))
		return false;

	if (UOBJ_IS_VALID(m_characterMapChunks[SIDE_OFFICIALS]) && (!m_characterMapChunks[SIDE_OFFICIALS]->IsLoaded() || !m_characterMapChunks[SIDE_OFFICIALS]->IsVisible()))
		return false;

	return true;
}

bool SIFGameWorld::InitialiseFootballerMap(SSTEAMSIDE side)
{
	URugbyLevelManager* pLevelManager = GetGameInstance().GetLevelManager();

	FString mapURL = FString("/Game/Maps/FootballerMap");
	FString worldIdString = GetWorldIdAsString();

	FString sideString = ENUM_TO_FSTRING(SSTEAMSIDE, side);
	FString uniqueInstancePrefix = FString::Printf(TEXT("%s_%s"), *worldIdString, *sideString);

	URugbyLevelChunk* footballerLevelChunk = pLevelManager->CreateLevelChunk(mapURL, false, uniqueInstancePrefix);
	if (UOBJ_IS_VALID(footballerLevelChunk))
	{
		SetCharacterMapChunk(side, footballerLevelChunk);

		UE_LOG(LogTemp, Log, TEXT("InitialiseFootballerMap created chunk for side %s: '%s, %s'"), *sideString, *mapURL, *uniqueInstancePrefix);
		return true;
	}

	UE_LOG(LogTemp, Log, TEXT("InitialiseFootballerMap failed to create chunk for side %s"), *sideString);
	return false;
}

bool SIFGameWorld::AsyncLoadFootballerMap(SSTEAMSIDE side)
{
	URugbyLevelManager* pLevelManager = GetGameInstance().GetLevelManager();

	FString sideString = ENUM_TO_FSTRING(SSTEAMSIDE, side);

	URugbyLevelChunk* footballerLevelChunk = m_characterMapChunks[side];
	if (UOBJ_IS_VALID(footballerLevelChunk))
	{
		pLevelManager->LoadLevelChunk(footballerLevelChunk, FRugbyLevelChunkDelegate::CreateRaw(this, &SIFGameWorld::OnFootballerMapLoaded, side));
		UE_LOG(LogTemp, Log, TEXT("AsyncLoadFootballerMap loaded chunk for side %s"), *sideString);
		return true;
	}

	UE_LOG(LogTemp, Log, TEXT("AsyncLoadFootballerMap failed to load chunk for side %s"), *sideString);
	return false;
}

void SIFGameWorld::OnFootballerMapLoaded(URugbyLevelChunk* footballerLevelChunk, SSTEAMSIDE side)
{
	if (!UOBJ_IS_VALID(footballerLevelChunk))
	{
		UE_LOG(LogTemp, Log, TEXT("OnFootballerMapLoaded, level chunk loaded but not valid for side %s"), ENUM_TO_CHAR(SSTEAMSIDE, side));
		return;
	}

	if (footballerLevelChunk->IsVisible())
	{
		OnFootballerMapShown(footballerLevelChunk, side);
	}
	else
	{
		m_characterMapInitialRevealCallback[side] =
			footballerLevelChunk->m_OnLevelShow.Add(FRugbyLevelChunkDelegate::CreateRaw(this, &SIFGameWorld::OnFootballerMapShown, side));
		footballerLevelChunk->Reveal();
	}
}

void SIFGameWorld::OnFootballerMapShown(URugbyLevelChunk* footballerLevelChunk, SSTEAMSIDE side)
{
	if (!UOBJ_IS_VALID(footballerLevelChunk))
	{
		UE_LOG(LogTemp, Fatal, TEXT("OnLoadingCreaturesComplete failed. No footballerLevelChunk found."));
		return;
	}

	FDelegateHandle& intialRevealCallback = m_characterMapInitialRevealCallback[side];
	if (intialRevealCallback.IsValid())
	{
		footballerLevelChunk->m_OnLevelShow.Remove(intialRevealCallback);
		intialRevealCallback.Reset();
	}

	FString worldIdString = GetWorldIdAsString();
	FString sideString = ENUM_TO_FSTRING(SSTEAMSIDE, side);
	UE_LOG(LogTemp, Log, TEXT("OnLoadingCreaturesComplete for %s in %s."), *sideString, *worldIdString);

	FString folderName = FString::Printf(TEXT("%s/Footballers/%s"), *worldIdString, *sideString);

	ULevelStreamingDynamic* levelStream = footballerLevelChunk->GetLevelStreaming();
	ULevel* loadedLevel = levelStream->GetLoadedLevel();
	TArray<AActor*> levelActors = loadedLevel->Actors;

	// Grab our footballer list from our array depending on the side we're loading.
	TArray<ARugbyCharacter*>* footballerList = &m_mapFootballers[(int)side];
	footballerList->Empty();

	for (int i = 0; i < levelActors.Num(); ++i)
	{
		ARugbyCharacter* rugbyPlayer = Cast<ARugbyCharacter>(levelActors[i]);
		if (rugbyPlayer)
		{
			FString UniqueName = FString::Printf(TEXT("%s_Player_%s_%i"), *worldIdString, *sideString, i);
#if WITH_EDITOR
			rugbyPlayer->SetFolderPath(FName(*folderName));
			rugbyPlayer->SetActorLabel(UniqueName);
#endif
			rugbyPlayer->Rename(*UniqueName, rugbyPlayer->GetOuter(), REN_ForceNoResetLoaders);
			rugbyPlayer->SetGameWorld(this);
			rugbyPlayer->SetVisible(false); // Disable visibility so that they aren't shown straight away when we show the level

			// Cache it in our list
			footballerList->Add(rugbyPlayer);
		}
	}
}

void SIFGameWorld::InitialiseFootballers()
{
	FString worldIdString = GetWorldIdAsString();
	UE_LOG(LogTemp, Log, TEXT("OnLoadingAllFootballersComplete in %s."), *worldIdString);
	// Create an instance of the player factory, used to populate teams with players.
	RUPlayerFactory factory(this);

	// Populate the teams with players.
	factory.PopulateTeams(teams);

	// If the game requires officials, populate the officials team with players.
	if (officials != NULL)
	{
		officials->SetSide(SIDE_OFFICIALS);
		factory.PopulateTeam(officials);
	}

	// Add the players from the teams to the game world player list.
	RebuildPlayerList();

	// Assign the players numbers (positions)
	AssignPlayerNumbers();

	// Create the human players.
	//#rc3_legacy if (!SIFApplication::GetApplication()->GetAttractSequenceManager()->IsInAttractMode())
	//{
	CreateHumanPlayers();
	//}

	if (game_settings.game_settings.network_game) random_number_generator->LockToThread(0);

	random_number_generator->SetAssertIfUsed(true);

	// Register stats scoring for network games.
	RegisterStatsScorer(human_players, *this);

}

//===============================================================================
//===============================================================================
void SIFGameWorld::InitialisePauseHandler(SIFAppTime* pAppTime, SIFGamePauseState* pPauseState)
{
	if (UOBJ_IS_VALID(m_pPauseHandler))
	{
		m_pPauseHandler->Initialise(pAppTime, pPauseState, this);
	}
}

ARugbyCharacter* SIFGameWorld::GetPlayerFromFootballerMap(int index, SSTEAMSIDE side) const
{
	MABASSERT((int)side < SSTEAMSIDE::SIDE_NONE);
	MABASSERT(index < m_mapFootballers[(int)side].Num());

	return m_mapFootballers[(int)side][index];
}

void SIFGameWorld::OnLoadingComplete()
{
	m_isLoading = false;

	m_onLoadingComplete.Broadcast();
	m_onLoadingComplete.Clear();
}

void SIFGameWorld::OnLoadingCancelled()
{
	m_isLoading = false;

	m_onLoadingCancelled.Broadcast();
	m_onLoadingCancelled.Clear();
}

/*
void SIFGameWorld::SetLevelChunksVisible(bool visible)
{
	for (MabVector<URugbyLevelChunk*>::reverse_iterator iter = stadium_chunks.rbegin(); iter != stadium_chunks.rend(); ++iter)
	{
		if (visible)
		{
			(*iter)->Reveal();
		}
		else
		{
			(*iter)->Hide();
		}
	}

//	for (int32 i = 0; i < m_characterMapChunks.Num(); i++)
//	{
//		if (m_characterMapChunks[i])
//		{
//			if (visible)
//			{
//				m_characterMapChunks[i]->Reveal();
//			}
//			else
//			{
//				m_characterMapChunks[i]->Hide();
//			}
//		}
//	}

	world_is_revealed = visible;
}
*/

void SIFGameWorld::HideWorld()
{
	for (MabVector<URugbyLevelChunk*>::reverse_iterator iter = stadium_chunks.rbegin(); iter != stadium_chunks.rend(); ++iter)
	{
		(*iter)->Hide();
	}

	if (!m_quickLoadActive && !m_teamSwapActive && !m_isLoading)
	{
		for (int32 i = 0; i < m_characterMapChunks.Num(); i++)
		{
			if (m_characterMapChunks[i])
			{
				m_characterMapChunks[i]->Hide();
			}
		}
	}

	world_is_revealing = false;
	world_is_revealed = false;

	GEngine->ForceGarbageCollection(false);
}

void SIFGameWorld::RevealWorld()
{
	world_is_revealing = true;

	for (MabVector<URugbyLevelChunk*>::reverse_iterator iter = stadium_chunks.rbegin(); iter != stadium_chunks.rend(); ++iter)
	{
		(*iter)->Reveal();
	}

	if (!m_quickLoadActive && !m_teamSwapActive && !m_isLoading)
	{
		for (int32 i = 0; i < m_characterMapChunks.Num(); i++)
		{
			if (m_characterMapChunks[i])
			{
				m_characterMapChunks[i]->Reveal();
			}
		}
	}

	// Just in case the above chunks are all already shown
	OnLevelChunkRevealed(nullptr);
}

void SIFGameWorld::OnLevelChunkRevealed(URugbyLevelChunk* pLevelChunk)
{
	if (!world_is_revealing || world_is_revealed)
	{
		return;
	}

	for (MabVector<URugbyLevelChunk*>::reverse_iterator iter = stadium_chunks.rbegin(); iter != stadium_chunks.rend(); ++iter)
	{
		if (!(*iter)->IsVisible())
		{
			return;
		}
	}

	if (!m_quickLoadActive && !m_teamSwapActive)
	{
		for (int32 i = 0; i < m_characterMapChunks.Num(); i++)
		{
			if (m_characterMapChunks[i] && m_characterMapChunks[i]->IsValidLowLevel() && !m_characterMapChunks[i]->IsVisible())
			{
				return;
			}
		}
	}

	OnWorldRevealed();
}

void SIFGameWorld::OnWorldRevealed()
{
	world_is_revealed = true;

	FTimerDelegate TimerCallback;

	TimerCallback.BindLambda([this]()
	{
		FinishWaking();
	});

	// IF WE ALREADY HAVE A TIMER LETS CLEAR IT OUT!!
	if (WorldReadyForPlayHandle.IsValid())
	{
		SIFApplication::GetApplication()->GetTimerManager().ClearTimer(WorldReadyForPlayHandle);
	}

	SIFApplication::GetApplication()->GetTimerManager().SetTimer(WorldReadyForPlayHandle, TimerCallback, 1.5f, false);
}

void SIFGameWorld::CancelSkyDomeUpdates(bool cancel)
{
	if (sky_manager.IsValid())
	{
		if (cancel)
		{
			sky_manager->CancelSkyDomeLightingDelayed();
		}
		else
		{
			sky_manager->ClearSkyDomeUpdateLock();
		}
	}
}

void SIFGameWorld::FindMatchGraphicsObjects()
{
	UE_LOG(LogTemp, Display, TEXT("Gettings Graphics Objects"));

	//get references
	sky_manager = nullptr;
	crowd_manager = nullptr;
	stadium_overloading_manager = nullptr;
	field_manager = nullptr;

	for (unsigned int i = 0; i < stadium_chunks.size(); i++)
	{
		if (!UOBJ_IS_VALID(stadium_chunks[i]))
		{
			continue;
		}

		if (stadium_chunks[i]->GetLevelStreaming() && stadium_chunks[i]->GetLevelStreaming()->GetLoadedLevel())
		{
			TArray<AActor*> actors = stadium_chunks[i]->GetLevelStreaming()->GetLoadedLevel()->Actors;
			for (int q = 0; q < actors.Num(); q++)
			{
				if (!UOBJ_IS_VALID(actors[q]))
				{
					continue;
				}

				if (actors[q]->IsA<ACrowdbase>())
				{
					crowd_manager = Cast<ACrowdbase>(actors[q]);
					continue;
				}

				if (actors[q]->IsA<ASkyDomeBase>())
				{
					sky_manager = Cast<ASkyDomeBase>(actors[q]);
					if (sky_manager.IsValid())
					{
						if (IsMatch())
						{
							//Add the skydome update to the screen swipe
							events->screen_wipe_end.Add(this, &SIFGameWorld::RequestUpdateSkyDomeLightingDelayed);
						}

						if (IsMenu())
						{
							sky_manager->ForceSkyDomeTexture = 2;
						}
						sky_manager->StreamingManagerRef = &URugbyGameInstance::GetStreamableManager();
					}

					continue;
				}

				if (actors[q]->IsA<AFieldRugby>())
				{
					field_manager = Cast<AFieldRugby>(actors[q]);
#if GRASS_ONLY_IN_MAIN_MENU
					field_manager->ShouldSpawnGrass = IsMenu();
					field_manager->CullDistance = 500.0f;//lowering the max distance for the grass to prevent lots being on screen at once
					//field_manager->MinLodForGrass = 1;//lowering the max distance for the grass to prevent lots being on screen at once
#endif
					continue;
				}

				if (actors[q]->IsA<AStadiumBase>())
				{
					stadium_overloading_manager = Cast<AStadiumBase>(actors[q]);
					//if (stadium_overloading_manager)
					//{
					//	stadium_overloading_manager->SetupStadiumFromActors(actors);
					//}
					continue;
				}
			}
		}
	}

	if (sky_manager.IsValid() && stadium_overloading_manager.IsValid())
	{
		sky_manager->StadiumReference = stadium_overloading_manager.Get();
	}

	if (!crowd_manager.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to find crowd base"));
	}
	if (!sky_manager.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to find sky dome"));
	}
	if (!field_manager.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to find field Base"));
	}
	if (!stadium_overloading_manager.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to find Stadium Base"));
	}
}

void SIFGameWorld::GenerateMatchGraphics()
{
	if (IsSandbox())
	{
		if (sky_manager.IsValid())
		{
			sky_manager->EditorSetTimeOfDay();
		}
		return;
	}


	bool settingsRaining = GetGameSettings().weather_settings.raining;
	EWeatherType weather = EWeatherType::Clear;
	if (settingsRaining)
	{
		weather = EWeatherType::HeavyRain;
	}

	//override this for the time being
	//weather = EWeatherType::Clear;

	SetupMatchGraphics();

	//apply settings
	if (crowd_manager.IsValid())
	{
		//Texture2D'/Game/Rugby/cmn_con/Crowd/Texture/avi_bath/t_crowd_atlas.t_crowd_atlas'
		//Texture2D'/Game/Rugby/cmn_con/prop/flag/texture/t_flag_nz/t_flag_d.t_flag_d'

		UDatabaseManager* dbManager = URugbyGameInstance::GetInstance()->GetDatabaseManager();

		//UTexture2D* homeCrowdTex = nullptr;
		//UTexture2D* awayCrowdTex = nullptr;
		//UTexture2D* homeCrowdFlagTex = nullptr;
		//UTexture2D* awayCrowdFlagTex = nullptr;
		FString homeCrowdTex;
		FString awayCrowdTex;
		FString homeCrowdFlagTex;
		FString awayCrowdFlagTex;

		{
			RUTeam* team = GetTeam(SIDE_A);
			unsigned short ClothesTextureNum = team->GetDbTeamStrip().GetTextureNum();

			// If the team is custom, use a default flag texture instead.
			if (team->GetDbTeam().IsCustom())
			{
				ClothesTextureNum = CUSTOM_TEAM_CROWD_FLAG_CLOTHES_TEXTURE_NUM;
			}

			FCharacterAssetTextureClothes* teamDBRef = dbManager->GetCharacterClothesTextureRec(ClothesTextureNum);
			if (teamDBRef)
			{
				FString folderAbev = teamDBRef->ClothesDiffuseTexture;
				folderAbev.Split("\\", &folderAbev, nullptr);
				FString crowdpath = FString("/Game/Rugby/cmn_con/prop/Crowd/Texture/") + folderAbev;
				homeCrowdTex = (crowdpath + "/t_crowd_atlas.t_crowd_atlas");
				//FStringAssetReference crowdTexAssetRef(crowdpath + "/t_crowd_atlas");
				//homeCrowdTex = Cast<UTexture2D>(crowdTexAssetRef.TryLoad());

				crowdpath = FString("/Game/Rugby/cmn_con/prop/flag/texture/") + folderAbev;
				homeCrowdFlagTex = (crowdpath + "/t_flag_d.t_flag_d");
				//crowdTexAssetRef = (crowdpath + "/t_flag_d");
				//homeCrowdFlagTex = Cast<UTexture2D>(crowdTexAssetRef.TryLoad());
			}
		}
		{
			RUTeam* team = GetTeam(SIDE_B);

			unsigned short ClothesTextureNum = team->GetDbTeamStrip().GetTextureNum();

			// If the team is custom, use a default flag texture instead.
			if (team->GetDbTeam().IsCustom())
			{
				ClothesTextureNum = CUSTOM_TEAM_CROWD_FLAG_CLOTHES_TEXTURE_NUM;
			}

			FCharacterAssetTextureClothes* teamDBRef = dbManager->GetCharacterClothesTextureRec(ClothesTextureNum);
			if (teamDBRef)
			{
				FString folderAbev = teamDBRef->ClothesDiffuseTexture;
				folderAbev.Split("\\", &folderAbev, nullptr);
				FString crowdpath = FString("/Game/Rugby/cmn_con/prop/Crowd/Texture/") + folderAbev;
				awayCrowdTex = (crowdpath + "/t_crowd_atlas.t_crowd_atlas");
				//FStringAssetReference crowdTexAssetRef(crowdpath + "/t_crowd_atlas");
				//awayCrowdTex = Cast<UTexture2D>(crowdTexAssetRef.TryLoad());

				crowdpath = FString("/Game/Rugby/cmn_con/prop/flag/texture/") + folderAbev;
				awayCrowdFlagTex = (crowdpath + "/t_flag_d.t_flag_d");
				//crowdTexAssetRef = (crowdpath + "/t_flag_d");
				//awayCrowdFlagTex = Cast<UTexture2D>(crowdTexAssetRef.TryLoad());
			}
		}

		int densitySelector = GetGameSettings().crowd_settings.crowd_size;

		crowd_manager->CrowdDensityIndexSelector = densitySelector;
		//crowd_manager->BuildInstanceData();
		//crowd_manager->SetupCrowdData(homeCrowdTex, awayCrowdTex, homeCrowdFlagTex, homeCrowdFlagTex, (int)weather, 0.5f, false);
		crowd_manager->SetupCrowdDataAsync(&URugbyGameInstance::GetStreamableManager(), homeCrowdTex, awayCrowdTex, homeCrowdFlagTex, awayCrowdFlagTex, (int)weather, 0.5f, false);

		crowd_manager->SetCrowdVisiblity(false, true);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to spawn crowd"));
	}

	if (IsMenu())
	{
		return;
	}

	if (stadium_overloading_manager.IsValid())
	{
		for (unsigned int i = 0; i < stadium_chunks.size(); i++)
		{
			if (!UOBJ_IS_VALID(stadium_chunks[i]))
			{
				continue;
			}

			if (stadium_chunks[i]->GetLevelStreaming() && stadium_chunks[i]->GetLevelStreaming()->GetLoadedLevel())
			{
				TArray<AActor*> actors = stadium_chunks[i]->GetLevelStreaming()->GetLoadedLevel()->Actors;
				stadium_overloading_manager->SetupStadiumFromActors(actors);
			}
		}
	}

	if (stadium_manager)
	{
		FStadiumOverloadPaths overloadPaths;
		overloadPaths.m_BannerPathPrefix = overloadPaths.m_FieldPathPrefix = "/Game/Rugby/cmn_con/prop/";

		overloadPaths.m_Banner = SIFGameHelpers::GAConvertMabStringToFString(stadium_manager->GetSignageFileName());

		overloadPaths.m_GoalPostAd = overloadPaths.m_BannerPathPrefix + SIFGameHelpers::GAConvertMabStringToFString(stadium_manager->GetPostsFileName());

		overloadPaths.m_FieldAd = overloadPaths.m_FieldPathPrefix + SIFGameHelpers::GAConvertMabStringToFString(stadium_manager->GetMarkingsFileName());

		overloadPaths.m_Advertising_Group0 = SIFGameHelpers::GAConvertMabStringToFString(stadium_manager->GetAdvertisingGroup0FileName());
		overloadPaths.m_Advertising_Group0 += stadium_manager->FindFileName(overloadPaths.m_Advertising_Group0);

		overloadPaths.m_Advertising_Group1 = SIFGameHelpers::GAConvertMabStringToFString(stadium_manager->GetAdvertisingGroup1FileName());
		overloadPaths.m_Advertising_Group1 += stadium_manager->FindFileName(overloadPaths.m_Advertising_Group1);

		overloadPaths.m_Advertising_Group2 = SIFGameHelpers::GAConvertMabStringToFString(stadium_manager->GetAdvertisingGroup2FileName());
		overloadPaths.m_Advertising_Group2 += stadium_manager->FindFileName(overloadPaths.m_Advertising_Group2);


		overloadPaths.m_BannerPathPrefix.ReplaceCharInline('\\', '/');
		overloadPaths.m_Banner.ReplaceCharInline('\\', '/');
		overloadPaths.m_GoalPostAd.ReplaceCharInline('\\', '/');
		overloadPaths.m_FieldAd.ReplaceCharInline('\\', '/');

		if (stadium_overloading_manager.IsValid())
		{
			stadium_overloading_manager->ApplyOverLoadsUsingCurrentWorld(overloadPaths);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("overloading, Failed to set up stadium"));
		}

		if (field_manager.IsValid())
		{
			field_manager->OverloadFieldTextures(overloadPaths);

			FString signageObjPath = overloadPaths.m_BannerPathPrefix.LeftChop(5) + overloadPaths.m_Banner;

			signageObjPath.ReplaceCharInline('\\', '/');


			//This is needed to be able to load the blueprint class, without _C it didnt seem to want to load into a UObject so not too sure why this is needed
			signageObjPath += "_C";

			//bassicly this
			//UBlueprintGeneratedClass* SpawnActor = Cast<UBlueprintGeneratedClass>(StaticLoadObject(UObject::StaticClass(), NULL, TEXT("/Game/Rugby/cmn_con/prop/signage/ocea_signage/ocea_gen_signage.ocea_gen_signage_C")));


			TSoftObjectPtr<UBlueprintGeneratedClass> SignageActorPath;
			SignageActorPath = signageObjPath;

			FStreamableManager* StreamingManager = &URugbyGameInstance::GetStreamableManager();

			TArray<FSoftObjectPath> ItemsToStream;
			ItemsToStream.AddUnique(SignageActorPath.ToSoftObjectPath());

			m_signage_actor_async_spawn_handle = StreamingManager->RequestAsyncLoad(ItemsToStream, [this, SignageActorPath]()
			{
				if (field_manager.IsValid()) //Because this is an async request we need to check again inside here that the field manager is still valid
				{
					UBlueprintGeneratedClass* SpawnActor = SignageActorPath.Get();
					if (SpawnActor)
					{
						FTransform SpawnTransform = FTransform(FVector::ZeroVector);
						field_manager->FieldSignageActor = Cast<ASignageActor>(UGameplayStatics::BeginDeferredActorSpawnFromClass(GetGameInstance().GetWorld(), SpawnActor, SpawnTransform));

						if (UOBJ_IS_VALID(field_manager->FieldSignageActor))
						{
							field_manager->FieldSignageActor->FinishSpawning(SpawnTransform);
							field_manager->FieldSignageActor->SetActorHiddenInGame(true);
						}
						else
						{
							ensure(false);
							UE_LOG(LogTemp, Error, TEXT("overloading, Cant Spwan Field Signage %s"), *SignageActorPath.GetLongPackageName());
						}
					}
					else
					{
						ensure(false);
						UE_LOG(LogTemp, Error, TEXT("overloading, Cant find Field Signage %s"), *SignageActorPath.GetLongPackageName());
					}
				}
			});
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("overloading, Failed to set up field"));
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("overloading, No data avaliable"));
	}


}

void SIFGameWorld::SetupMatchGraphics()
{

	//get data
	int settingsTimeOfDay = GetGameSettings().weather_settings.time_of_day;
	bool settingsRaining = GetGameSettings().weather_settings.raining;
	int settingsLighting = GetGameSettings().weather_settings.lighting_conditions;
	if (settingsLighting == LIGHTING_CONDITIONS_NIGHT)
	{
		settingsTimeOfDay = TIME_NIGHT;
	}
	FString time = "12:00";
	EWeatherType weather = EWeatherType::Clear;

	switch (settingsTimeOfDay)
	{
		case TIME_DAY:
		time = "15:00";
		break;
		case TIME_EVENING:
		time = "17:00";
		break;
		case TIME_NIGHT:
		time = "20:00";
		break;
	}

	if (settingsRaining)
	{
		weather = EWeatherType::HeavyRain;
	}
	else
	{
		switch (settingsLighting)
		{
			case LIGHTING_CONDITIONS_CLEAR:
			weather = EWeatherType::Clear;
			break;
			case LIGHTING_CONDITIONS_OVERCAST:
			weather = EWeatherType::OverCast;
			break;
		}
	}

	//override this for the time being
	//weather = EWeatherType::Clear;

#if PLATFORM_XBOXONE
//Don't update on xbox if the user has logged out, as this caused a rendering thread crash.
	if (!SIFApplication::GetApplication() || SIFApplication::GetApplication()->GetLoginChanged())
	{
		return;
	}
#endif

	if (sky_manager.IsValid())
	{

		int realMatchLength = GetGameSettings().game_limits.GetActualGameLength();
		int gameLength = GetGameSettings().game_settings.game_length;

		//sky_manager->QuaterLengthSelections = { (float)gameLength, (float)realMatchLength };
		//sky_manager->MatchLength = realMatchLength / 60.0f;
		sky_manager->MatchLength = gameLength;

		//EWeatherType

		sky_manager->SetUpMatchInfo(0, (int)weather, time, IsMenu());

		if (IsMenu())
		{
			sky_manager->EditorSetTimeOfDay();
			sky_manager->PauseTime = true;
		}


		sky_manager->IsOnMainMenu = IsMenu();

	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to set time and weather info"));
	}
}

void SIFGameWorld::ShowMatchGraphics()
{
	if (crowd_manager.IsValid())
	{
		crowd_manager->SetCrowdVisiblity(true, true);
	}
	if (field_manager.IsValid() && UOBJ_IS_VALID(field_manager->FieldSignageActor))
	{
		field_manager->FieldSignageActor->SetActorHiddenInGame(false);
	}
}

void SIFGameWorld::OnCutsceneStart()
{
	ResetPlayerTickPaused();

#if PLATFORM_SWITCH == 0
	static IConsoleVariable* DynamicResToggle = IConsoleManager::Get().FindConsoleVariable(TEXT("r.DynamicRes.OperationMode"));
	if (DynamicResToggle)
	{
		DynamicResToggle->Set(0);
	}
#endif

	if (sky_manager.IsValid())
	{
		sky_manager->ShouldUpdateTime = false;
	}

	if (crowd_manager.IsValid())
	{
		crowd_manager->CutsceneStarted();
	}

	if (field_manager.IsValid())
	{
		field_manager->RunGrassFovResponce = true;
	}

	//morph targets always on for cutscenes
	IConsoleVariable* EnableMorphTargets = IConsoleManager::Get().FindConsoleVariable(TEXT("r.EnableMorphTargets"));
	if (EnableMorphTargets)
	{
		if (IsMatch())
		{
			EnableMorphTargets->Set(1);
		}
	}

}
void SIFGameWorld::OnCutsceneEnd()
{
	SetupPlayerTickPaused();

#if PLATFORM_SWITCH == 0
	static IConsoleVariable* DynamicResToggle = IConsoleManager::Get().FindConsoleVariable(TEXT("r.DynamicRes.OperationMode"));
	if (DynamicResToggle)
	{
		DynamicResToggle->Set(2);
	}
#endif

	if (sky_manager.IsValid())
	{
		sky_manager->ShouldUpdateTime = true;
	}

	if (crowd_manager.IsValid())
	{
		crowd_manager->CutsceneEnded();
	}

	if (field_manager.IsValid())
	{
		field_manager->RunGrassFovResponce = false;
	}

	static IConsoleVariable* performanceModeCvar = IConsoleManager::Get().FindConsoleVariable(TEXT("ww.PerformanceMode"));

	//turn off morph targets for gameplay if performance mode is not 2
	IConsoleVariable* EnableMorphTargets = IConsoleManager::Get().FindConsoleVariable(TEXT("r.EnableMorphTargets"));
	if (EnableMorphTargets)
	{
		if (IsMatch())
		{
#if PLATFORM_SWITCH
			bool enabled = false;
#else
			bool enabled = performanceModeCvar->GetInt() <= 1;
#endif
			//EnableMorphTargets->Set(bEnteringMenu ? 1 : enabled);
			EnableMorphTargets->Set(enabled);
		}
	}

}

void SIFGameWorld::SetFootprintsEnabled(bool bEnabled)
{
	if (field_manager.IsValid())
	{
		field_manager->FootprintsEnabled = bEnabled;
	}
}

void SIFGameWorld::ResetGraphicsObjects()
{

#if PLATFORM_XBOXONE
	//Don't update on xbox if the user has logged out, as this caused a rendering thread crash.
	if (!SIFApplication::GetApplication() || SIFApplication::GetApplication()->GetLoginChanged())
	{
		if (sky_manager.IsValid())
		{
			sky_manager->CancelSkyDomeLightingDelayed();
		}

		return;
	}
#endif

	if (field_manager.IsValid())
	{
		field_manager->ClearFootprintTexture();
	}

	SetupMatchGraphics();

	RequestUpdateSkyDomeLightingDelayed();
}

void SIFGameWorld::RequestUpdateSkyDomeLightingDelayed()
{
	if (sky_manager.IsValid())
	{
		sky_manager->UpdateSkyDomeLightingDelayed();
	}
}

void SIFGameWorld::PauseGraphicsObjects(bool IsPausing)
{
	if (field_manager.IsValid())
	{
		field_manager->FootprintsEnabled = !IsPausing;
	}

	if (sky_manager.IsValid())
	{
		sky_manager->PauseTime = IsPausing;
	}

	if (crowd_manager.IsValid())
	{
		float playbackSpeed = IsPausing ? 0.0f : 1.0f;
		crowd_manager->SetAnimationSpeedMultiplier(playbackSpeed);
	}
}

#if 0 //#rc3_legacy
/**
* Instantiate
*
* Instantiates an instance of SIFGameWorld
*/
MabResourceBase* SIFGameWorldInstancer::Instantiate(MABMEM_HEAP heap, MabResourceBase* /*target_object*/, const MabString& parameters) const
{
	MabNamedValueList named_value_list(parameters, "|", 10, heap);

	// Ensure we have a name and a phase
	if (named_value_list.GetNamedValue("name") == NULL || named_value_list.GetNamedValue("phase") == NULL)
		return NULL;

	// Extract name and phase
	MabString name = named_value_list.GetNamedValue("name")->ToString();
	MabString phase = named_value_list.GetNamedValue("phase")->ToString();

	// Extract auto delete flag
	const MabNamedValue* auto_delete_nv = named_value_list.GetNamedValue("auto_delete");
	bool auto_delete = (auto_delete_nv ? auto_delete_nv->ToBoolean() : false);

	// Select the appropriate game settings
	WORLD_ID world_id;
	SIFApplication* sif_application = SIFApplication::GetApplication();
	RUGameSettings* game_settings;
	if (phase == "MenuPhase")
	{
		game_settings = sif_application->GetSandboxGameSettings();
		world_id = WORLD_ID::SANDBOX;
	}
	else if (phase == "GamePhase")
	{
		game_settings = sif_application->GetMatchGameSettings();
		world_id = WORLD_ID::GAME;
		// Uncomment this to replay a previously recorded network game.
		/*
		RU_NETWORK_REPLAY_START_REPLAYING_GAME(
			"/app_home/tools/RUNetworkReplayServer/UNKNOWN__2011.07.04.1135__192.168.10.65",
			"/app_home/tools/RUNetworkReplayServer/UNKNOWN__2011.07.04.1135__192.168.10.65.check",
			game_settings
		);
		*/
	}
	else
		return NULL; // Cannot instantiate a world for an unknown phase

	// Create the game world for the phase specified
	SIFGameWorld* game_world = MabMemNew(heap) SIFGameWorld(heap, name.c_str(), auto_delete,
		*game_settings,
		sif_application->GetGameControlActionManager(),
		sif_application->GetAnimationRepository(),
		sif_application->GetAppTime()->GetSimulationTimeSource(world_id),
		world_id
	);
	RU_NETWORK_REPLAY_CONNECT_PEERS(game_world->GetNetworkState());
	return game_world;
}
#endif

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void SIFGameWorld::SleepInstance()
{
	if (world_state == WORLD_STATE::AWAKENING || world_state == WORLD_STATE::AWAKE)
	{
		// If we have a timer running to finalise a wake, we really don't want this firing any more.
		if (WorldReadyForPlayHandle.IsValid())
		{
			SIFApplication::GetApplication()->GetTimerManager().ClearTimer(WorldReadyForPlayHandle);
		}

		world_state = WORLD_STATE::SLEEPING;

		DisablePlayers();
		if (cutscene_manager)
		{
			cutscene_manager->Reset();
		}
		//clear the roles before we delete the animations.
		GetStrategyHelper()->ClearRoles();

		for (SSHumanPlayer* human : GetHumanPlayers())
		{
			//human->SetTeam(nullptr);
			human->SetRugbyCharacter(nullptr);
		}

		for (size_t i = 0; i < all_players.size(); i++)
		{
			// #dewald_garbage_collection adding these checks in for now as we get garbage collected between levels
			if (!all_players[i]->IsValidLowLevel() || !all_players[i]->IsA(ARugbyCharacter::StaticClass()))
			{
				continue;
			}

			//okay, now we dont need them. So hide.
			all_players[i]->SetActorHiddenInGame(true);
			all_players[i]->SetActorTickEnabled(false);
			all_players[i]->SetReplicateMovement(false);

			RUPlayerFactory::DeletePlayerAnimation(all_players[i], NULL/*#rc3_legacy animation_manager*/, this);
			RUPlayerSound* player_sound = all_players[i]->GetSound();
			if (player_sound)
			{
				player_sound->StopSounds();
			}
		}

		if (ball && ball->IsValidLowLevel())
		{
			ball->SetActorHiddenInGame(true);
		}

		if (hud3d)
		{
			hud3d->Disable();
		}

		rumble_manager->SetEnabled(false);

		//#rc3_legacy
		SIFApplication::GetApplication()->GetAchievementChecker()->RemoveGameEvents(this);
		// SIFApplication::GetApplication()->GetRugbyDollarsChecker()->RemoveGameEvents(this);
		hud_updater_strategy[0]->Cleanup();
		hud_updater_strategy[1]->Cleanup();

		// DH - Resetting the humans here so we reset the inputs, timings, actions from the previous gameworld.
		for (SSHumanPlayer* human : GetHumanPlayers())
		{
			human->Reset();
		}

		ClearDelayedTransition();

		HideWorld(); //SetLevelChunksVisible(false); //#rc3_legacy SetGraphicsNodesAttached(false);

		m_onWorldSleep.Broadcast(GetWorldId());
	}

	world_state = WORLD_STATE::ASLEEP;
}

///-------------------------------------------------------------------------------
/// Awaken this world after a short delay.
///-------------------------------------------------------------------------------

void SIFGameWorld::DelayedAwakenInstance()
{
	if (!world_is_allocated)
	{
		MABBREAKMSG("World cannot be awoken when it isn't allocated.");
		return;
	}

	if (world_state != WORLD_STATE::AWAKENING && world_state != WORLD_STATE::AWAKE)
	{
		delayed_awaken = DELAYED_AWAKEN_DELAY;
	}
}

///-------------------------------------------------------------------------------
///	Call when restarting sandbox world after being in game.
///  - Fixes up any required pointers to (deleted) UI elements etc...
/// Must be called from sync update.
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::printGameState()
{
#ifdef _DEBUG
	char buf[256] = "";
	int l = 0;

	MABUNUSED(buf);
	MABUNUSED(l);
	MABLOGDEBUG("SIFGameWorld::printGameState GAME BEGIN");
	MABLOGDEBUG("MasterTackleDeterminationIndex=%d", GetTackleHelper()->GetMasterTackleDeterminationIndex());
	simulation_time->printState();
	//animation_resource
	//object_database
	//game_context
	//ambient_point_cloud
	//particles_manager
	network_manager->printState();
	network_state->printState();
	//effect_system
	//animation_manager
	//stadium_resources
	//stadium_node
	game_settings.printState();
	ball->printState();
	SIFRugbyCharacterList::iterator pit;
	for (pit = players.begin(); pit != players.end(); ++pit)
		;//pit->printState();
	for (pit = all_players.begin(); pit != all_players.end(); ++pit)
		;//(*pit)->printState();
	//props
	Teams::iterator tit;
	for (tit = teams.begin(); tit != teams.end(); ++tit)
		(*tit)->printState();
	officials->printState();
	//events
	locomotion->printState();
	lineout_rater->printState();
	//animation
	movement->printState();
	game_gtb->printState();
	//role_factory
	//camera_manager
	//camera
	for (int i = 0; i != (int)human_players.size(); i++)
		human_players[i]->printState();
	//input_manager
	strategy_helper->printState();
	tackle_helper->printState();
	game_state->printState();
	spatial_helper->printState();
	game_timer->printState();
	//random_number_generator
	//hud3d
	//active_camera
	//hud_updater
	//hud_updater_contextual
	//hud_updater_strategy
	//replay_hud_updater
	//stats_tracker
	//rules
	//tutorial_manager
	//cutscene_manager
	//post_effects_manager
	//replay_manager
	//stadium_manager
	//weather_manager
	//team_confidence_handler
	//substitution_manager
	//screen_wipe_manager
	//rumble_manager
	//world_audio
	//game_stats2bucket
	//emotion_engine_manager
	//crowd_manager
	//shader_parameters
	//jumbotrons
	//camera_effects
	//node_clean_up
	//texture_swaps
	//player_filters
	//customisation_player
	//main_menu_lights
	//training_lights
	//ball_resource
	//hard_reset_players
	//hard_reset_repository
	//hard_reset_control_action_manager
	MABLOGDEBUG("stlock=%d statdium=%d heap=%d unused=%f relight=%d print=%d render=%d world=%d visible=%d awake=%d load=%d",
		false, pssg_stadium_id, heap, unused_time, relight_scene,
		do_mem_log_print, reenabled_rendering, world_id, world_is_revealed, world_is_awake,
		async_loading_started);
	MABLOGDEBUG("sets=%s shadow=%s delay=%d check=%d init=%d switch=%d count=%d pend=%d req=%d perm=%d sand=%d req=%d train=%d",
		settings_path.c_str(), shadow_settings_path.c_str(), delayed_awaken,
		delayed_initial_state_check, initial_state_checked, team_switch_state,
		team_switch_counter, load_pending_test_counter, async_load_start_requested,
		non_permanent_players_loaded, sandbox_environment_setting, requested_sandbox_environment_setting,
		using_training_lights);
	MABLOGDEBUG("comp=%d reload=%d q=%d q2=%d q3=%d hard=%d nodes=%u",
		pseudo_competition_id, restart_reload_players, quick_load_active, quick_load_send_ui_message,
		quick_load_requested_home_team, is_hard_reset,
		(unsigned int)nodes_added_to_scene.size()
	);
	MABLOGDEBUG("SIFGameWorld::printGameState GAME END");
#endif
}
#endif

void SIFGameWorld::AwakenInstance()
{
	if (!world_is_allocated)
	{
		MABBREAKMSG("World cannot be awoken when it isn't allocated.");
		return;
	}

	if (world_state != WORLD_STATE::AWAKENING && world_state != WORLD_STATE::AWAKE)
	{
		world_state = WORLD_STATE::AWAKENING;
		random_number_generator->SetAssertIfUsed(false);
		if (game_settings.game_settings.network_game) random_number_generator->LockToThread(MabThread::GetCurrent());

		RevealWorld(); //SetLevelChunksVisible(true); //#rc3_legacy SetGraphicsNodesAttached(true);					// Attach all nodes in this world to the scene.

		if (game_settings.game_settings.network_game) random_number_generator->LockToThread(0);
		random_number_generator->SetAssertIfUsed(true);

		//FinishWaking(); //Moved to OnWorldRevealed
	}
}

void SIFGameWorld::FinishWaking()
{
	if (world_state == WORLD_STATE::AWAKE || world_state != WORLD_STATE::AWAKENING)
		return;

	world_state = WORLD_STATE::AWAKE;

	SIFApplication *application = SIFApplication::GetApplication();

	random_number_generator->SetAssertIfUsed(false);
	if (game_settings.game_settings.network_game) random_number_generator->LockToThread(MabThread::GetCurrent());

	if (hud3d)
	{
		hud3d->Enable();
	}

	EnablePlayers();
	//#rc3_legacy GetPssgAnimationManager()->FlushPhaseDeferredAnimations();

	application->UpdateDeferredQueue();		// Must do it now or lights don't get updated.
#if PLATFORM_XBOX360							// XBox360 double defers, so need to call twice.
	application->UpdateDeferredQueue();
#endif

	/*#rc3_legacy
	{
		PSSG::PDatabaseWriteLock env_lock(GetSceneDatabaseId());
		PSSG::PDatabase* env_database = env_lock.getDatabase();
		PSSG::PListIterator scenes(env_database->getSceneList());
		PSSG::PRootNode* visual_scene_root = (PSSG::PRootNode*)scenes.data();		// get first scene root for rendering

		GetLightDatabase()->Rebuild(*visual_scene_root);
	}

	GetViewManager()->GetLayeredRendering()->SetCameraEffects(camera_effects);
	GetViewManager()->GetLayeredRendering()->SetJumboTrons(jumbotrons);

	camera_effects->SetOcclusionManager(GetViewManager()->GetLayeredRendering()->GetOcclusionManager());*/

	// Called to late, all render nodes are added to the static_render_nodes list.
	//GetLightLinkManager()->InitialiseRootNodeHierarchyLights( visual_scene_root, PSSGMabLightDatabase::LIGHTMASK_ALL );

	/// Setup animation components for players now.
	for (size_t i = 0; i < all_players.size(); i++)
	{
		// #dewald_garbage_collection adding these checks in for now as we get garbage collected between levels
		if (!all_players[i]->IsValidLowLevel() || !all_players[i]->IsA(ARugbyCharacter::StaticClass()))
		{
			continue;
		}

		all_players[i]->SetActorTickEnabled(true);
		all_players[i]->SetReplicateMovement(false);

		RUPlayerFactory::SetupPlayerAnimation(all_players[i], NULL/*#rc3_legacy animation_manager*/, this);
	}

	for (auto& team : teams)
	{
		team->UpdateVisibility(SSTeam::PLAYER_VISIBILITY::SHOW_FIELD);
	}

	if (officials)
	{
		officials->UpdateVisibility(SSTeam::PLAYER_VISIBILITY::SHOW_FIELD);
	}

	if (ball && ball->IsValidLowLevel())
	{
		ball->SetActorHiddenInGame(false);
	}

	bool nwGame = IsMatch() && game_settings.game_settings.network_game;

	//So lets restart the game? Every time? No matter what? Should we do it again if we are in a match?
	//Im going to remove this as it sucks and crashes the game, oh and we will do it again if we are in a match too so that sucks, no wonder the game runs so bad!
	//RestartGame();

	//#rc3_legacy GetGameState()->SetBallHolder( GetFirstHumanPlayer()->GetPlayer(), true );
	AssignPlayerNumbers();

	//#rc3_legacy application->SetSuppressRenderingNoThreadSwitch(true);
	reenabled_rendering = false;

	//#rc3_legacy
	//LoadPostEffectSettings(settings_path);
	//LoadShadowSettings(shadow_settings_path);

	if (IsMatch())
	{
		//Now if we are in a match lets restart the game again? o.O
		// Restart game. Sandbox handles this itself
		RestartGame();

		if (GetWorldId() == WORLD_ID::GAME)
		{
			UE_LOG(LogNetworkNonGame, Display, TEXT("Broadcasting Game World Ready Event!"));
			m_onGameWorldReady.Broadcast();
		}

		//#rc3_legacy
		/*if (nwGame)
		printGameState();*/

		/// HACK!!!! - Moved from USIFGameFlowNode::Enter (need to setup a delayed function call really)

		MabString target_window_name(0, "%s/%s", RugbyUIWindowNames::SIFUI_ROOT_MENU_WINDOW_NAME, RugbyUIWindowNames::SIFUI_RUNON_WINDOW_NAME);
		if (SIFApplication::GetApplication())
		{
			SIFApplication::GetApplication()->WWUIInpoint(InPoints_UI::InGameHud, InPoints_UI::InGameHud, true, true);
			m_allowVoipDelayed = true;
			m_allowVoipTimer = 0.0f;
			SIFApplication::GetApplication()->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::RunOnWindow);
		}

		// Inform the game state manager that a level has been launched
		// - Moved from USIFGameFlowNode::Enter (This clears the level launch data (so has to be postponed for async loading))
		//#rc3_legacy_levellaunch application->GetLevelLauncher()->LevelLaunched();

#if DESTROY_SANDBOX_BEFORE_MATCH
		SIFApplication::GetApplication()->DestroySandboxGame();
#endif
	}
	else if (IsMenu())
	{
		async_loading_started = false;

		RestartGame();

		for (auto& team : teams)
		{
			while (team->GetPlayers().size() != 0u)
				team->MoveToBench(team->GetPlayers().front(), PS_BENCH);

			team->ClearHumanPlayers();
		}

		if (GetGameState())
		{
			GetGameState()->SetBallHolder(NULL);
		}
	}
	else //if (IsSandbox())
	{
		async_loading_started = false;

		RestartGame();
		ResetSandboxHumanPlayers();
	}

	// Belt and braces, make sure no one has paused the game accidentally
	this->simulation_time->Pause(false);

	//#rc3_legacy_face_gen RUTeamFacesGenerator *tf_generator = application->GetTeamFacesGenerator();
	if (!IsMatch())
	{
		//#rc3_legacy_face_gen tf_generator->SetGameWorld(this);
		SetSandboxEnvironment(SBE_MENU, true);
	}

#ifdef ENABLE_TEAM_FACES_GENERATION_TEST
	{
		RUTeamFacesGenerator *generator = SIFApplication::GetApplication()->GetTeamFacesGenerator();
		if (generator)
		{
			generator->ShowAtlas(generator->GetTextureAtlas(0));
			generator->RequestTeamRender(0, 1001);
		}
	}
#endif

	if (rumble_manager)
	{
		rumble_manager->SetEnabled(true);
	}

	// Set the world game to the achievement checker
	if (application && application->GetAchievementChecker())
	{
		application->GetAchievementChecker()->InitGameEvents(this);
	}
	if (application && application->GetStatsScorer())
	{
		application->GetStatsScorer()->InitGameEvents(this);
	}

	// ... and the rugby dollars checker
	//#rc3_legacy application->GetRugbyDollarsChecker()->InitGameEvents(this);

	//Inform world audio the world has awoken.
	world_audio->OnWorldAwake();

	if (game_settings.game_settings.network_game) random_number_generator->LockToThread(0);
	random_number_generator->SetAssertIfUsed(true);

	//#rc3_legacy GetScreenWipeManager()->StartFadeFromBlack(0.5f);
	//#rc3_legacy GetScreenWipeManager()->HoldScreenWipe(0.5f);

	//#rc3_legacy SIFShadowMap::Singleton()->UpdateBakedMap();

	m_onWorldAwaken.Broadcast(GetWorldId());

	if (IsMatch())
	{
		application->RequestTransitionFinish(0.5f);
	}
}

///-------------------------------------------------------------------------------
/// Return pointer to the hud_updater.
///-------------------------------------------------------------------------------

RUHUDUpdater* SIFGameWorld::GetHUDUpdater()
{
	return MabCast<RUHUDUpdater>(hud_updater);
}

///-------------------------------------------------------------------------------
/// Return pointer to training hud updater.
///-------------------------------------------------------------------------------

RUHUDUpdaterTraining* SIFGameWorld::GetHUDUpdaterTraining()
{
	return MabCast<RUHUDUpdaterTraining>(hud_updater);
}

///-------------------------------------------------------------------------------
/// Add stadium sub level to list.
///-------------------------------------------------------------------------------

void SIFGameWorld::AddStadiumChunk(URugbyLevelChunk* subLevel)
{
	MABASSERTMSG(subLevel != nullptr, "Adding nullptr as stadium sub level");
	if (UOBJ_IS_VALID(subLevel))
	{
		stadium_chunks.push_back(subLevel);
		subLevel->m_OnLevelShow.Add(FRugbyLevelChunkDelegate::CreateRaw(this, &SIFGameWorld::OnLevelChunkRevealed));
	}
}

///-------------------------------------------------------------------------------
/// Set character sub level in array.
///-------------------------------------------------------------------------------

void SIFGameWorld::SetCharacterMapChunk(SSTEAMSIDE side, URugbyLevelChunk* chunk)
{
	if ((int)side >= m_characterMapChunks.Num())
	{
		MABBREAKMSG("Team side too large!");
		return;
	}

	URugbyLevelChunk*& subLevel = m_characterMapChunks[side];

	if (chunk == subLevel)
	{
		return;
	}

	DestroyCharacterMapChunk(side);

	subLevel = chunk;

	if (UOBJ_IS_VALID(subLevel))
	{
		subLevel->m_OnLevelShow.Add(FRugbyLevelChunkDelegate::CreateRaw(this, &SIFGameWorld::OnLevelChunkRevealed));
	}
}

///-------------------------------------------------------------------------------
/// Destroy character sub level in array.
///-------------------------------------------------------------------------------

void SIFGameWorld::DestroyCharacterMapChunk(SSTEAMSIDE side)
{
	URugbyLevelChunk*& subLevel = m_characterMapChunks[side];

	if (!UOBJ_IS_VALID(subLevel))
	{
		return;
	}

	subLevel->m_OnLevelShow.Clear();

	URugbyLevelManager* levelManager = URugbyGameInstance::GetInstance()->GetLevelManager();
	levelManager->DestroyLevelChunk(subLevel);

	subLevel = nullptr;
}

///-------------------------------------------------------------------------------
/// Add pssg node to clean up list.
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::AddPssgNodeForDeletion(PSSG::PNode *node)
{
	node_clean_up.push_back(node);
}
#endif

///-------------------------------------------------------------------------------
/// Print out the visible scene graph.
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::PrintSceneGraph()
{
	PSSG::PDatabaseWriteLock env_lock(this->GetSceneDatabaseId());
	PSSG::PDatabase* env_database = env_lock.getDatabase();
	PSSG::PListIterator scenes(env_database->getSceneList());
	PSSG::PRootNode* visual_scene_root = (PSSG::PRootNode*)scenes.data();

	PFlatTraversalPrint printScene(true);
	printScene.traverseDepthFirst(*visual_scene_root, &PTraversalContext::create);
	printScene.Print();
}
#endif

///-------------------------------------------------------------------------------
/// Called from lua, to start async loading.
///-------------------------------------------------------------------------------

void SIFGameWorld::RequestAsyncLoadStart()
{
	if (async_load_start_requested == ASYNC_LOAD_NONE)
	{
		RUGameSettings* localGame_settings = SIFApplication::GetApplication()->GetMatchGameSettings();

		// This was added when we were playing sevens game modes in normal play with R15 teams. So we had to
		// reload the teams if they were first loaded for R15, and then needed R7 line ups...
		// Possibly not needed anymore, since game mode is now set before a competition is started, so we should have no conflicts.
		// Dewald - WW
		//int team0LoadedFor = game_settings->team_settings[SIDE_A].teamLoadForMode;
		//int gameLoadedIn = (int)game_settings->game_settings.GetGameMode();
		//
		//if(team0LoadedFor != gameLoadedIn)
		//{
		//	game_settings->team_settings[ SIDE_A ].LoadTeam( game_settings->team_settings[SIDE_A].team.GetDbId() );
		//	game_settings->team_settings[ SIDE_B ].LoadTeam( game_settings->team_settings[SIDE_B].team.GetDbId() );
		//}
		///////////////////////////////////////////////////////////////////////////////////////////

		// Set up crowd here.
		localGame_settings->CalculateCrowdSize();

		// Set up officials here?
		localGame_settings->SetupOfficials();

		// Start generating faces too
		SIFGameHelpers::GARequestGameSettingsFaceRender();

		if (!localGame_settings->game_settings.network_game)			// Don't fade and restore strips if network game.
		{
			/*#rc3_legacy_wipe
			GetScreenWipeManager()->StartFadeFromBlack(ASYNC_LOAD_FADE_TIME);
			GetScreenWipeManager()->HoldScreenWipe(3.0f);
			*/

			/// Reset team strips after selection.
//#if !PLATFORM_SWITCH // Skipping team swapping for Switch
			SIFGameHelpers::GARestoreTeamStrips();
//#endif
		}

		async_load_start_requested = ASYNC_LOAD_REQUESTED;
	}
}

///-------------------------------------------------------------------------------
/// Called on starting async-loading (before phase change)
///  - Delete all non-permanent players etc...
///-------------------------------------------------------------------------------

void SIFGameWorld::OnAsyncLoadingStart()
{
	SIFApplication *application = SIFApplication::GetApplication();

	async_loading_started = true;

	//! Network game handles this flow in earlier, so this is not needed.
	bool network_game = application->GetMatchGameSettings()->game_settings.network_game;
	if (network_game) return;

	//#rc3_legacy_rumble GetRumbleManager()->SetEnabled(false);

	random_number_generator->SetAssertIfUsed(false);

	GetCutSceneManager()->StopAllCutScenes();
	GetCutSceneManager()->OnCutSceneEnd();

	/// Clear all pending loads + allow current to finish.
	//application->GetAsyncLoadThread()->Abort();

	// Delete non-permanent players
	//#rc3_legacy : RussellD : DeleteNonPermanentPlayers();
	RebuildPlayerList();

	/// Force AO to re-update after player deletions + before render. (NO LONGER NEEDED)
	//GetViewManager()->SyncUpdate();

	/// Set off the training field...

	RUSandboxGame* sandbox_game = application->GetSandboxGame();
	if (sandbox_game)
	{
		sandbox_game->ResetSandbox();
	}

	SetupHumanPlayersForLoading();

	SetSandboxEnvironment(SBE_TRAINING, true);

	//MABLOGDEBUG("-------------- OnAsyncLoadingStart ------------------");
	//PrintSceneGraph();
	//MABLOGDEBUG("");
	//PSSGMabGfx::PrintLoadedDatabases();
	//MABLOGDEBUG("-----------------------------------------------");

	//#rc3_legacy_rumble GetRumbleManager()->SetEnabled(true);

	random_number_generator->SetAssertIfUsed(true);
}

///-------------------------------------------------------------------------------
/// Request sandbox env.
///-------------------------------------------------------------------------------

void SIFGameWorld::RequestSandboxEnvironment(SANDBOX_ENVIRONMENT setting)
{
	requested_sandbox_environment_setting = setting;
}

///-------------------------------------------------------------------------------
/// Set the sandbox environment (weather/post-post effects etc..)
///-------------------------------------------------------------------------------

void SIFGameWorld::SetSandboxEnvironment(SANDBOX_ENVIRONMENT setting, bool force)
{
	if (setting != sandbox_environment_setting || force)
	{
		sandbox_environment_setting = setting;
		requested_sandbox_environment_setting = setting;

		game_settings.weather_settings.raining = false;
		game_settings.weather_settings.wind_direction = FVector(0, 0, 1);
		game_settings.weather_settings.wind_strength = 1.0f;
		//#rc3_legacy_weather weather_manager->SetWeatherSettings(game_settings.weather_settings);

		int tod = RUStadiumManager::WEATOD_NIGHT;
		if (setting != SBE_MENU)
		{
			tod = RUStadiumManager::WEATOD_SUNNY;
		}

		//#rc3_legacy_evds camera_effects->ResetAndReload( stadium_manager->GetStadiumEVDSEvent(), tod);

		char *HIDE_TRAINING = (char *)"hide_training";
		char *HIDE_MAIN = (char *)"hide_main_menu";

		//#rc3_legacy_post_effects
		/*if(setting==SBE_MENU)
		{
			RestorePostEffectSettings();
			RestoreShadowSettings();
		}
		else if(setting==SBE_TRAINING)
		{
			LoadPostEffectSettings(TRAINING_GROUND_POST_EFFECTS_FILE);
			LoadShadowSettings(TRAINING_GROUND_SHADOW_SETTINGS_FILE);
		}
		else if(setting==SBE_PLAYER_CUSTOMIZE)
		{
			LoadPostEffectSettings(UI_CUSTOMIZE_PLAYER_POSTEFFECTS);
			LoadShadowSettings(TRAINING_GROUND_SHADOW_SETTINGS_FILE);
		}*/

		//#rc3_legacy_pssg
		/*SIFPSSGUtil::SwitchNodes(GetSceneRootNode(), setting==SBE_MENU ? HIDE_TRAINING : HIDE_MAIN, NULL);
		SIFPSSGUtil::SwitchNodes(GetSceneRootNode(), NULL, setting==SBE_MENU ? HIDE_MAIN : HIDE_TRAINING);

		SetLightSource(setting==SBE_TRAINING);*/
	}
}

///-------------------------------------------------------------------------------
/// Get the scene database id for this world.
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
PSSG::PDatabaseID SIFGameWorld::GetSceneDatabaseId() const
{
	return SIFApplication::GetApplication()->GetEnvironmentDatabaseId();
}

SIFGameJobQueue* SIFGameWorld::GetJobQueue() const
{
	return SIFApplication::GetApplication()->GetEnvironmentJobQueue();
}

SIFBruteForceLightDatabase*	SIFGameWorld::GetLightDatabase() const
{
	return SIFApplication::GetApplication()->GetEnvironmentLightDatabase();
}

PSSGMabShaderManager* SIFGameWorld::GetShaderManager()
{
	return SIFApplication::GetApplication()->GetEnvironmentShaderManager();
}

SIFViewManager* SIFGameWorld::GetViewManager() const
{
	return SIFApplication::GetApplication()->GetEnvironmentViewManager();
}

PSSGMabDynamicLightLinkManager* SIFGameWorld::GetLightLinkManager()
{
	return SIFApplication::GetApplication()->GetEnvironmentLightLinkManager();
}

PSSGMabAnimation* SIFGameWorld::GetPssgAnimationManager()
{
	return SIFApplication::GetApplication()->GetEnvironmentPssgAnimationManager();
}

SIFPSSGUtil::SIFPSSGClonedShaderManager *SIFGameWorld::GetEnvironmentClonedShaderManager()
{
	return SIFApplication::GetApplication()->GetEnvironmentClonedShaderManager();
}
#endif

///-------------------------------------------------------------------------------
/// Automatic invite received function - called from SyncUpdate().
///-------------------------------------------------------------------------------

void SIFGameWorld::ProcessInitialState()
{
	// The player may have received an invite while they were loading into the match.
	// We couldn't cancel out of the async load, so this is the first opportunity we have
	// to bump them to the invite screen.
#if defined ENABLE_STEAM || (PLATFORM_PS4) || (PLATFORM_XBOXONE)
	if (world_id == WORLD_ID::GAME && SIFMatchmakingHelpers::GetInvitePending())
	{
#if PLATFORM_XBOXONE
		//SIFMatchmakingHelpers::MMConsumeInvitation();
#endif
		SIFMatchmakingHelpers::ProceedToInvitedSession();
		return;
	}
#endif

#if (PLATFORM_XBOXONE) && 0
	SIFXboxOneSaveRequestManager* const request_manager = SIFApplication::GetApplication()->GetSaveRequestManager();
	if (IsMatch() &&
		request_manager->LoggedOutOfLoadingGame())
	{
		RUCareerModeManager* const career_mode_mgr = SIFApplication::GetApplication()->GetCareerModeManager();
		if (career_mode_mgr != NULL && career_mode_mgr->IsActive())
		{
			career_mode_mgr->EndCareerMode();
		}

		request_manager->ResetLoggedOutOfLoadingGame();
		bool flowScreenSet = SIFGameHelpers::GASetMainMenuFlowScreen(RugbyUIWindowNames::SIFUI_SPLASH_TITLE_WINDOW_NAME);
		SIFApplication::GetApplication()->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::Loading);
		SIFGameHelpers::GAExitGame(flowScreenSet);
		return;
	}
#endif
	// The player may have signed out of their profile, if this is the case then we need to exit the game
	// A new profile would require a new save device to be set.
}

///-------------------------------------------------------------------------------
/// Switch the UI light source. (MUST BE CALLED FROM SYNCUPDATE!)
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::SetLightSource(bool training)
{
	if (training != using_training_lights)
	{
		SIFApplication *app = SIFApplication::GetApplication();

		for (MabVector<SceneNodeEntry>::iterator it = nodes_added_to_scene.begin(); it != nodes_added_to_scene.end(); ++it)
		{
			SceneNodeEntry *entry = &(*it);

			if (entry->node == main_menu_lights)
			{	// Detach/Attach main menu lights from scene graph
				app->DeferredAddChild(training ? NULL : entry->parent, entry->node, entry->light_link_mask);
			}
			if (entry->node == training_lights)
			{	// Attach training lights to scene graph
				app->DeferredAddChild(training ? entry->parent : NULL, entry->node, entry->light_link_mask);
			}
		}

		app->UpdateDeferredQueue();		// Must do it now or lights don't get updated.
#if PLATFORM_XBOX360					// XBox360 double defers, so need to call twice.
		app->UpdateDeferredQueue();
#endif

		{
			PSSG::PDatabaseWriteLock env_lock(GetSceneDatabaseId());
			PSSG::PDatabase* env_database = env_lock.getDatabase();
			PSSG::PListIterator scenes(env_database->getSceneList());
			PSSG::PRootNode* visual_scene_root = (PSSG::PRootNode*)scenes.data();		// get first scene root for rendering

			GetLightDatabase()->Rebuild(*visual_scene_root);
		}

		using_training_lights = training;
	}
}
#endif

///-------------------------------------------------------------------------------
/// Creates animations for a node, deferring until the world is visible if necessary.
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
PSSGMabAnimationController* SIFGameWorld::CreateAnimations(PSSG::PNode* node, SIFPSSGUtil::SIFPSSGSceneCloner& cloner, PSSG::PTimeController* override_time_controller)
{
	PSSGMabAnimation* animation = GetPssgAnimationManager();
	PDatabaseID source_db_id = cloner.GetSourceDatabase()->getID();
	PDatabaseID dest_db_id = cloner.GetDestinationDatabase()->getID();

	if (world_is_revealed)
	{
		return animation->CreateAnimations(node, source_db_id, dest_db_id, cloner, override_time_controller);
	}
	else
	{
		return animation->CreatePhaseDeferredAnimations(node, source_db_id, dest_db_id, cloner, override_time_controller);
	}
}
#endif // #rc3_legacy

///-------------------------------------------------------------------------------
/// Delete player.
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SIFGameWorld::DeletePlayer(ARugbyCharacter* player)
{
	SIFApplication::GetApplication()->DeferredPlayerDelete(player, this);
}
#endif // #rc3_legacy

///-------------------------------------------------------------------------------
/// Safely delete a player.
///  - Must be called from sync-update.
///-------------------------------------------------------------------------------
/*
void SIFGameWorld::DeletePlayerNow(ARugbyCharacter* player, bool destroy_player)
{
	//MABLOGDEBUG("DeletePlayerNow: %p", player);

	bool is_ui_player = player->GetMovement() == NULL;	// TYRONE : Apparently UI players will generate delete player now messages, we don't want this

	// Remove player from any cutscenes.
	cutscene_manager->PlayerDeleted(player);

	// Tell camera manager (just in case any cameras are targeting this player).
	camera_manager->OnPlayerDeleted(player);

	// Remove any references in players team.
	if(RUPlayerAttributes* attributes = player->GetAttributes())
	{
		// Remove our pro player from career (if pro check done in function)
		SIFApplication::GetApplication()->GetCareerModeManager()->OnPlayerDeleted(player);

		if(RUTeam *team = attributes->GetTeam())
		{
			team->RemovePlayer(player);
		}
	}

	// If human is controlling then stop.
	SSHumanPlayer *human = player->GetHumanPlayer();
	if(human!=NULL)
	{
		human->SetRugbyCharacter(nullptr);
	}

	if(GetGameState()->GetBallHolder()==player)
	{
		GetGameState()->SetBallHolder(NULL);			// Might have to find another player...
	}

	// Abort all actions on other players, as these can have references to 'player'
	if ( !is_ui_player )
	{
		for (MabVector<ARugbyCharacter*>::iterator iter = all_players.begin(); iter != all_players.end(); ++iter)
		{
			ARugbyCharacter *plr = *iter;
			plr->GetActionManager()->AbortAllActions();
		}
	}

	/// Delete the animation components regardless (need to do this for restart game)
	if(!destroy_player)
	{
		//#rc3_legacy RUPlayerFactory::DeletePlayerAnimation(player, animation_manager, this);
		RUPlayerFactory::DeletePlayerAnimation(player, NULL, this);
	}

	if(destroy_player)
	{
		//#rc3_legacy_pssg
		//  // Undo any texture swaps applied to the player.
		//  PSSG::PNode *gfx = player->GetGraphicsObject();
		//  UndoTextureSwaps(gfx);
		//

		// Destroy player. (DeferredFree's the players gfx object).
		player->Destroy();
	}

	// Remove from 'players' and 'all_players'.
	MabVector<ARugbyCharacter*>::iterator iter = std::find(players.begin(),players.end(),player);
	if(iter != players.end())
		players.erase(iter);

	iter = std::find(all_players.begin(),all_players.end(),player);
	if(iter!=all_players.end())
	{
		all_players.erase(iter);
		MABLOGDEBUG("SIFGameWorld::DeletePlayerNow DELETE Player -> %u",(unsigned int)all_players.size());
	}

	// Update the players 'index' so it truely reflects the players index in to this->players[].
	for ( size_t i = 0; i < players.size(); ++i )
	{
		players[i]->GetAttributes()->SetIndex((int)i);
	}

	if ( !is_ui_player )
		GetEvents()->player_deleted(player);

#if 0
	/// Search for dangling pointers.
	if(GetWorldId()==WORLD_ID::GAME)
	{
		int num_found,num_found_main;
		unsigned int addresses[128];
		unsigned int mallocs[128];
		int tags[128];

		//MABLOGDEBUG("DeletePlayerNow: %x",player);

		MABLOGDEBUG("Pointer search: begin");
		num_found_main = MabMemCoreSearch((unsigned int)player, heap, addresses, tags,mallocs, 128);
		MABLOGDEBUG("Num found = %d",num_found_main);
		for(int i=0;i<num_found_main;i++)
		{
			MABLOGDEBUG("Address %x: tag %d : malloc: %x",addresses[i],tags[i], mallocs[i]);
		}
		MABLOGDEBUG("Pointer search: end");

		MABLOGDEBUG("Pointer search (smallblocks): begin");
		/// Search for a value (pointer to stale data for instance) in a heap - small blocks version.
		num_found = MabMemCoreSBSearch((unsigned int)player, heap, addresses, tags, 128);
		MABLOGDEBUG("Num found = %d",num_found);
		for(int i=0;i<num_found;i++)
		{
			MABLOGDEBUG("Address %x: tag %d",addresses[i],tags[i]);
		}
		MABLOGDEBUG("Pointer search (smallblocks): end");

		if(num_found_main>1)
		{
			object_database->DebugShowPoolMemoryInfo();
			MabMemDebugDatabase::ListAllocationsStillAllocedSince( 0, heap, NULL, NULL );
		}
	}
#endif

}
*/

///-------------------------------------------------------------------------------
/// Straight swap, one player for another (used in substitution).
///-------------------------------------------------------------------------------
/*
void SIFGameWorld::SwapPlayerInLists(ARugbyCharacter *player, ARugbyCharacter *replacement)
{
	SIFRugbyCharacterList::iterator iter;

	/// If replacement already in all_players, first remove it.
	iter = std::find(all_players.begin(),all_players.end(),replacement);
	if(iter!=all_players.end())
	{
		all_players.erase(iter);
		MABLOGDEBUG("SIFGameWorld::SwapPlayerInLists DELETE Player -> %u",(unsigned int)all_players.size());
	}

	/// If replacement already in players, first remove it.
	iter = std::find(players.begin(),players.end(),replacement);
	if(iter!=players.end())
		players.erase(iter);

	/// Replace 'player' with 'replacement'.
	std::replace(players.begin(),players.end(), player, replacement);
	std::replace(all_players.begin(),all_players.end(), player, replacement);

	replacement->GetAttributes()->SetIndex( player->GetAttributes()->GetIndex() );
}
*/

void SIFGameWorld::SwapPlayerInActiveList(ARugbyCharacter* existing, ARugbyCharacter* replacement)
{
	SIFRugbyCharacterList::iterator iter;

	/// If replacement already in players, first remove it.
	iter = std::find(players.begin(), players.end(), replacement);
	if (iter != players.end())
		players.erase(iter);

	/// Replace 'player' with 'replacement'.
	std::replace(players.begin(), players.end(), existing, replacement);

	replacement->GetAttributes()->SetIndex(existing->GetAttributes()->GetIndex());
}

///-------------------------------------------------------------------------------
/// Add a player to the game world. 'all_players'
///-------------------------------------------------------------------------------

void SIFGameWorld::AppendPlayer(ARugbyCharacter* player)
{
	if (std::find(all_players.begin(), all_players.end(), player) == all_players.end())
	{
		all_players.push_back(player);
		//MABLOGDEBUG("SIFGameWorld::AppendPlayer ADD Player -> %d", all_players.size());
	}
}

#if 0 //#rc3_legacy

///-------------------------------------------------------------------------------
/// Load a post effects file.
///-------------------------------------------------------------------------------

void SIFGameWorld::LoadPostEffectSettings(const MabString &file_name)
{
	SIFLevelPostSettings *level_settings = (SIFLevelPostSettings*)GetViewManager()->GetLevelPostSettings();
	level_settings->Load(file_name);
	if (post_effects_manager != NULL)
	{
		post_effects_manager->ResetBaseSettings();
	}
}

///-------------------------------------------------------------------------------
/// Restore the post effects settings.
///-------------------------------------------------------------------------------

void SIFGameWorld::RestorePostEffectSettings()
{
	SIFLevelPostSettings *level_settings = (SIFLevelPostSettings*)GetViewManager()->GetLevelPostSettings();
	level_settings->Load(settings_path);
	if (post_effects_manager != NULL)
	{
		post_effects_manager->ResetBaseSettings();
	}
}

///-------------------------------------------------------------------------------
/// Load a shadow settings file.
///-------------------------------------------------------------------------------

void SIFGameWorld::LoadShadowSettings(const MabString &file_name)
{
	SIFShadowSettingsList *settings_list = NULL;
	if (MabObjectResource* settings_resource = MabCast<MabObjectResource>(MabGlobalResourceSet::FetchResource("ShadowSettings")))
	{
		settings_list = MabCast<SIFShadowSettingsList>(settings_resource->GetObject());
		settings_list->Clear();		//??
		settings_list->Load(file_name);
	}
	MABASSERTMSG(settings_list, "Could not find shadow settings resource");
}

///-------------------------------------------------------------------------------
/// Restore the shadow effects settings.
///-------------------------------------------------------------------------------

void SIFGameWorld::RestoreShadowSettings()
{
	LoadShadowSettings(shadow_settings_path);
}


///-------------------------------------------------------------------------------
/// All texture swaps have to be recorded, due to pssg and mab keeping separate reference counts.
///-------------------------------------------------------------------------------

void SIFGameWorld::RegisterTextureSwap(PSSG::PNode *node, const char *node_name, const char* param_name)
{
	TextureSwapRecord record(node, node_name, param_name);
	texture_swaps.push_back(record);
}

///-------------------------------------------------------------------------------
/// Undo all texture swaps on a node and then remove records.
/// Must be called during sync-update.
///-------------------------------------------------------------------------------

void SIFGameWorld::UndoTextureSwaps(PSSG::PNode *node)
{
	for (MabVector<TextureSwapRecord>::iterator it = texture_swaps.begin(); it != texture_swaps.end(); )
	{
		TextureSwapRecord record = *it;
		if (record.node == node)
		{
			RUPlayerFactory::SwapTexture(node, NULL, record.node_name, record.param_name, this);
			it = texture_swaps.erase(it);
		}
		else
		{
			++it;
		}
	}
}

///-------------------------------------------------------------------------------
/// UndoAllTextureSwaps
///-------------------------------------------------------------------------------

void SIFGameWorld::UndoAllTextureSwaps()
{
	for (MabVector<TextureSwapRecord>::iterator it = texture_swaps.begin(); it != texture_swaps.end(); it++)
	{
		TextureSwapRecord record = *it;
		RUPlayerFactory::SwapTexture(record.node, NULL, record.node_name, record.param_name, this);
	}
	texture_swaps.clear();
}

///-------------------------------------------------------------------------------
/// Remove a resource from the stadium resources list.
///-------------------------------------------------------------------------------

void SIFGameWorld::RemoveFromResourceList(MabResourceBase* resource)
{
	MabVector<MabResourceBase*>::iterator it = std::find(stadium_resources.begin(), stadium_resources.end(), resource);
	if (it != stadium_resources.end())
	{
		stadium_resources.erase(it);
	}
}
#endif

#if 0 //#rc3_legacy
///-------------------------------------------------------------------------------
/// Start async loading of non-permanent players.
///-------------------------------------------------------------------------------

void SIFGameWorld::LoadNonPermanentPlayers()
{
	if (!non_permanent_players_loaded)
	{
		RUPlayerFactory::AsyncLoadNonPermanentPlayers(this, GetTeam(SIDE_A), GetGameSettings());
		RUPlayerFactory::AsyncLoadNonPermanentPlayers(this, GetTeam(SIDE_B), GetGameSettings());
		non_permanent_players_loaded = true;
	}
}

///-------------------------------------------------------------------------------
/// Delete non-permanent players
///-------------------------------------------------------------------------------

void SIFGameWorld::DeleteNonPermanentPlayers()
{
	SIFApplication *application = SIFApplication::GetApplication();

	MabVector<ARugbyCharacter*> remove_list;
	for (MabVector<ARugbyCharacter*>::iterator iter = all_players.begin(); iter != all_players.end(); ++iter)
	{
		ARugbyCharacter* player = *iter;
		RUPlayerState *state = player->GetState();
		if (state->IsNonPermanent())
		{
			application->DeferredPlayerDelete(player, this);
			remove_list.push_back(player);
		}
	}

	for (MabVector<ARugbyCharacter*>::iterator iter = remove_list.begin(); iter != remove_list.end(); ++iter)
	{
		ARugbyCharacter* player = *iter;
		MABLOGDEBUG("delete non-permanent player: 0x%p", player);
		DeletePlayerNow(player, false);			// Just removes all references, destroy handled by DeferredPlayerDelete.
	}

	non_permanent_players_loaded = false;
}
#endif



///*************************************************************************************************************************
///*************************************************************************************************************************
///*************************************************************************************************************************

FVector SIFGameWorld::GetClosestBasisVector(const FVector& test_vector) const
{
	const FVector north(0.0f, 0.0f, +1.0f);
	const FVector east(+1.0f, 0.0f, 0.0f);
	const FVector south(0.0f, 0.0f, -1.0f);
	const FVector west(-1.0f, 0.0f, 0.0f);

	constexpr const float ANGLE_NORMAL_CAMERA = 45.0f;
	constexpr const float ANGLE_SIDELINE_CAMERA = 20.0f;

	constexpr const char* SIDELINE_CAMERA_NAME = "SIDELINE";

	// For our pro mode cameras that are locked onto the ball, we want to run in the direction of the camera, not this 90' stuff...
	const MabString& cam_type = GetCameraManager()->GetSetCameraType();

	const bool is_pro_cam = cam_type.find("PRO") != std::string::npos;
	if (is_pro_cam && GetCameraManager()->GetReallyLookAtBall())
		return test_vector;

	// Fix for the player's movement changing 90 deg when running close to the sideline with the sideline camera.
	// The maths goes a bit weird, a smaller angle works better.
	const bool use_sideline_camera = (cam_type == SIDELINE_CAMERA_NAME) && (GetGameState()->GetPhase() != RUGamePhase::CONVERSION && GetGameState()->GetPhase() != RUGamePhase::PENALTY_SHOOT_FOR_GOAL);
	const float angle = use_sideline_camera ? ANGLE_SIDELINE_CAMERA : ANGLE_NORMAL_CAMERA;

	const float angle_delta = FMath::Cos(FMath::DegreesToRadians(angle));

	if (FVector::DotProduct(test_vector, north) >= angle_delta) return north;
	if (FVector::DotProduct(test_vector, south) >= angle_delta) return south;
	if (FVector::DotProduct(test_vector, west) >= angle_delta) return west;
	if (FVector::DotProduct(test_vector, east) >= angle_delta) return east;

	return test_vector;
}

FVector2D SIFGameWorld::GetClosestBasisVector(const FVector2D& test_vector) const
{
	FVector result = GetClosestBasisVector(FVector(test_vector.x, 0.0f, test_vector.y));
	return FVector2D(result.x, result.z);
}

///*************************************************************************************************************************
///*************************************************************************************************************************
///*************************************************************************************************************************

//#define DISABLE_HOME_TEAM_SWITCH

bool SIFGameWorld::SwapToPreferredSandboxTeams()
{
	MABASSERT(!IsMatch());

	if (m_quickLoadActive || m_teamSwapActive)
	{
		m_quickLoadDelayedTeamSwapRequest = TEAM_SWAP_REQUEST::TEAM_SWAP_DEFAULT; //(useCareerTeams ? TEAM_SWAP_REQUEST::TEAM_SWAP_CAREER : TEAM_SWAP_REQUEST::TEAM_SWAP_DEFAULT);
		return true;
	}

	bool teamSwapStarted = false;

	int newHomeTeam = -1;
	int newAwayTeam = -1;

	bool bShouldSwap = GetPreferredSandboxTeams(newHomeTeam, newAwayTeam);

	if (bShouldSwap)
	{
		teamSwapStarted = SwapSandboxTeams(newHomeTeam, newAwayTeam);
	}
	else
	{
		TriggerDelayedTransition();
	}

	return teamSwapStarted;
}

bool SIFGameWorld::NeedsSwapToPreferredSandboxTeams()
{
	int newHomeTeam = -1;
	int newAwayTeam = -1;
	return GetPreferredSandboxTeams(newHomeTeam, newAwayTeam);
}

bool SIFGameWorld::GetPreferredSandboxTeams(int& homeTeam, int& awayTeam)
{
	// This should pick the home team in the current comp...
	int team0_db_id = (int)game_settings.team_settings[0].team.GetDbId();
	int new_team0_db_id = team0_db_id;

	// This should get the opposition in the current match...
	int team1_db_id = (int)game_settings.team_settings[1].team.GetDbId();
	int new_team1_db_id = team1_db_id;

#if TEAM_LIKENESS_IN_COMP_UI
	RUCareerModeManager* career_manager = SIFApplication::GetApplication()->GetCareerModeManager();

	// We requested to load career teams
	if (career_manager != nullptr && career_manager->IsActive())
	{
		// Finished a competition. So we need to make sure we've loaded the winning/losing teams.
		if (career_manager->IsCareerModeOver() && !career_manager->IsInFranchise())
		{
			// Only care about the first team, since this is all the cutscene will show.
			new_team0_db_id = SIFGameHelpers::GACompetitionGetWinningTeamId();

			// If the player doesn't control the winners, find an alternative.
			if (!career_manager->GetIsTeamPlayerControlled(new_team0_db_id))
			{
				new_team0_db_id = career_manager->GetTeamIdForLastPlayerTeamMatch();
			}
		}
		else
		{
			RL3_SEASON_MATCH current_match = career_manager->GetCurrentMatch();
			RL3DB_COMPETITION_INSTANCE competition = career_manager->GetTournament()->GetCompetition(current_match.GetCompIndex());

			unsigned short home_team_id = competition.GetTeam(current_match.GetMatch().GetHomeTeam()).index;
			unsigned short away_team_id = competition.GetTeam(current_match.GetMatch().GetAwayTeam()).index;

			bool home_controlled = career_manager->IsTeamPlayerControlled(home_team_id);

			if (home_controlled)
			{
				new_team0_db_id = home_team_id;
				new_team1_db_id = away_team_id;
			}
			else
			{
				new_team0_db_id = away_team_id;
				new_team1_db_id = home_team_id;
			}
		}
	}
	// We requested to just load generic teams
	//else if (GetCutSceneManager()->DoHomeTeamSwitchWhenFadedToGeneric())
	//{
	//	new_team0_db_id = DB_TEAMID_WICKED_WITCH;
	//	new_team1_db_id = DB_TEAMID_TRU_BLU;
	//}
	// Check what our favourite team are in our profile, and load that one.
	else
#endif
	{
		SIFPlayerProfileManager *plr_prof_manager = SIFPlayerProfileManager::GetInstance();
		SIFPlayerProfile *master_profile = plr_prof_manager != nullptr ? plr_prof_manager->GetMasterProfile() : nullptr;
		if (master_profile)
		{
			const MabObservedValueList* value_list = NULL;
			value_list = master_profile->GetNamedValueList();
			int profile_idx = value_list->GetNamedValueIndex(PLAYER_PROFILE_DEFAULT_TEAM);
			if (profile_idx != -1)
			{
				const MabNamedValue& cse = value_list->GetNamedValue(profile_idx);
				new_team0_db_id = cse.ToInt();

				// Check if this team still exists
				const RUGameDatabaseManager* const game_database_mgr = SIFApplication::GetApplication()->GetGameDatabaseManager();
				if (!game_database_mgr->GetRL3Database()->GetIsValid() || game_database_mgr->GetRL3Database()->GetTeamCache()->GetRowStart((unsigned short)new_team0_db_id, false) == NULL)
				{
					new_team0_db_id = SIFApplication::GetApplication()->GetDefaultTeamA();
				}
			}
		}
		/* Code to test fave team swapping while the profile manager doesn't work
		else
		{
			const int rand_team_array[] =
			{
				DB_TEAMID_NZ,
				DB_TEAMID_AUSTRALIA,
				DB_TEAMID_SOUTHAFRICA,
				DB_TEAMID_ENGLAND,
				DB_TEAMID_FRANCE
			};
			static int i = 0;
			i = ++i % 5;

			new_team0_db_id = rand_team_array[i];
		}
		*/

		if (new_team0_db_id != team0_db_id && new_team0_db_id != -1)
		{
			new_team1_db_id = GetUIOppositionTeam(new_team0_db_id);
		}

		// #dewald running out of time, i hate having to do this here. But, time.
		// If the bug is fixed where the team settings 
		UFlowControlManager* flow_manager = mGameInstance.GetFlowControlManager();
		UFlowNode* activeNode = flow_manager->GetActiveNode();

		if (UTrainingFlowNode* flow_node = Cast<UTrainingFlowNode>(activeNode))
		{
			if (flow_node->IsEnteringMatch())
			{
				new_team0_db_id = (int)mGameInstance.GetMatchGameSettings()->team_settings[0].team.GetDbId();
				new_team1_db_id = (int)mGameInstance.GetMatchGameSettings()->team_settings[1].team.GetDbId();
			}
		}
	}

	homeTeam = new_team0_db_id;
	awayTeam = new_team1_db_id;

	return ((new_team0_db_id != team0_db_id && new_team0_db_id != -1) ||
		(new_team1_db_id != team1_db_id && new_team1_db_id != -1) || m_bForceReloadTeam);
}

bool SIFGameWorld::SwapSandboxTeams(int homeTeam, int awayTeam)
{
	MABASSERT(m_gameWorldSetupState == SETUP_IDLE || m_gameWorldSetupState == SETUP_COMPLETED);

	m_bForceReloadTeam = false;

	if (IsMenu())
	{
		TMap<int, unsigned short> teamOverrideMaps[2];

		for (int i = 0; i < 2; ++i)
		{
			const TArray<PlayerOverride>& teamOverrides = MENU_WOMEN_OVERRIDES[i];

			for (int j = 0; j < teamOverrides.Num(); ++j)
			{
				teamOverrideMaps[i].Add(
					teamOverrides[j].LineupIndex,
					teamOverrides[j].OverrideDbId);
			}
		}

		game_settings.team_settings[0].LoadTeam((unsigned short)homeTeam, teamOverrideMaps[0]);
		game_settings.team_settings[1].LoadTeam((unsigned short)awayTeam, teamOverrideMaps[1]);
	}
	else
	{
		game_settings.team_settings[0].LoadTeam((unsigned short)homeTeam);
		game_settings.team_settings[1].LoadTeam((unsigned short)awayTeam);
	}

	game_settings.game_settings.change_favourite_team = true;

	RUCareerModeManager *career_manager = SIFApplication::GetApplication()->GetCareerModeManager();
	bool in_career = career_manager && career_manager->IsActive();
	if (IsSandbox() && !in_career)
	{
		game_settings.NormaliseStats();
	}

	m_teamSwapActive = true;
	m_gameWorldSetupState = TEAMS_SWAP;

	LoadBallMaterial();

	return m_teamSwapActive;
}

void SIFGameWorld::DoSwapSandboxTeams()
{
	for (int i = 0; i < game_settings.num_active_teams; i++)
	{
		if (game_settings.team_settings[i].team.GetDbId() != SQLITEMAB_INVALID_ID)
		{
			UE_LOG(LogTemp, Display, TEXT("%s->DoSwapSandboxTeams() Setting team %i to %i"),
				*GetWorldIdAsString(), i, (int)game_settings.team_settings[i].team.GetDbId());

			teams[i]->SetDbTeam(game_settings.team_settings[i]);
			//teams[i]->SetCurrentStripId(game_settings.team_settings[i].strip_index);
		}
	}

	PlayerCustomisationInfo csinfo;
	for (unsigned int i = 0; i < teams.size(); ++i)
	{
		RUTeam* team = teams[i].get();

		SIFRugbyCharacterList teamPlayers = team->GetPlayers();
		teamPlayers.insert(teamPlayers.end(), teams[i]->GetBenchPlayers().begin(), teams[i]->GetBenchPlayers().end());

		const RUDB_TEAM&		db_team = team->GetDbTeam();
		const RUDB_TEAM_STRIP&	db_strip = team->GetDbTeamStrip();

		for (unsigned int j = 0; j < teamPlayers.size(); ++j)
		{
			csinfo = teamPlayers[j]->GetCustomisationInfo();
			csinfo.Setup(db_team, db_strip);
			teamPlayers[j]->SetCustomisationInfo(csinfo);
		}
	}

	LoadBallMaterial();
}

void SIFGameWorld::DoFullSwapSandboxTeams()
{
	// Invalidate active lists
	players.clear();

	// Invalidate spare in the faces generator
	URUTeamFacesGenerator* pFaceGenerator = GetGameInstance().GetTeamFacesGenerator();
	if (pFaceGenerator != nullptr)
	{
		if (pFaceGenerator->IsGameWorld(this))
		{
			pFaceGenerator->SetUiPlayerDirty();
		}
	}

	// Clear old players, and set the new db teams
	for (int sideIdx = 0; sideIdx < game_settings.num_active_teams; sideIdx++)
	{
		if (game_settings.team_settings[sideIdx].team.GetDbId() != SQLITEMAB_INVALID_ID)
		{
			UE_LOG(LogTemp, Display, TEXT("%s->DoSwapSandboxTeams() Setting team %i to %i"),
				*GetWorldIdAsString(), sideIdx, (int)game_settings.team_settings[sideIdx].team.GetDbId());

			teams[sideIdx]->RemoveAllPlayers();
			teams[sideIdx]->SetDbTeam(game_settings.team_settings[sideIdx]);
			//teams[i]->SetCurrentStripId(game_settings.team_settings[i].strip_index);
		}
	}

	// Re-initialize players and assign to teams
	PlayerCustomisationInfo csinfo;
	for (int sideIdx = 0; sideIdx < teams.size(); ++sideIdx)
	{
		RUTeam* team = teams[sideIdx].get();

		const RUDB_TEAM&		db_team = team->GetDbTeam();
		const RUDB_TEAM_STRIP&	db_strip = team->GetDbTeamStrip();

		if (sideIdx >= SSTEAMSIDE::SIDE_NONE)
		{
			break;
		}

		const GAME_MODE game_mode = team->GetIsR7ExclusiveTeam() ? GAME_MODE_RU13W : GAME_MODE_RU13; // Nick WWS change 7s to Womens
		const int playersOnTeam = (game_mode == GAME_MODE_RU13) ?
			game_settings.game_limits.GetNumberOfPlayersPerTeamR13() : game_settings.game_limits.GetNumberOfPlayersPerTeamR13();

		int mapFootballerCount = m_mapFootballers[(int)sideIdx].Num();

		for (int lineupIdx = 0; lineupIdx < mapFootballerCount; ++lineupIdx)
		{
			ARugbyCharacter* player = m_mapFootballers[(int)sideIdx][lineupIdx];

			if (!UOBJ_IS_VALID(player))
			{
				UE_LOG(LogTemp, Warning, TEXT("SIFGameWorld::DoFullSwapSandboxTeams Invalid player at index: %d on side: %d"), lineupIdx, sideIdx);
				continue;
			}

			RUDB_PLAYER* db_player = nullptr;
			PLAYER_POSITION	player_position = PP_NONE;

			bool isSpare = lineupIdx >= team->GetNumPlayers();
			if (isSpare)
			{
				db_player = team->GetSpareDbPlayer();
				player_position = PP_NONE;

				if (player->GetAttributes())
				{
					player->GetAttributes()->Initialise(team, db_player, player_position);
					player->GetAttributes()->SetNumber(0);
				}

				csinfo = player->GetCustomisationInfo();
				csinfo.Setup(db_team, db_strip);
				csinfo.m_shirtNumber = 0;
			}
			else
			{
				SSTEAMSIDE side = team->GetSide();

				db_player = &team->GetDbPlayerByIndex(lineupIdx);
				player_position = team->GetDbPlayerPositionByIndex(lineupIdx);
				/*player_position = lineupIdx < playersOnTeam ? PlayerPositionEnum::GetPlayerPositionFromStartingJerseyNumber(lineupIdx + 1, game_mode) : PP_NONE;*/

				if (player->GetAttributes())
				{
					player->GetAttributes()->Initialise(team, db_player, player_position);
					if (side != SIDE_OFFICIALS)
					{
						if (IsMenu())
						{
							SIFGameWorld::PlayerOverride overridePlayerData;
							if (TryGetMenuWomanOverrideByLineupIndex(side, lineupIdx, overridePlayerData))
							{
								player->GetAttributes()->SetNumber(overridePlayerData.OverrideShirtNumber);
								player->GetState()->SetUIId(overridePlayerData.OverrideUIId);
							}
							else
							{
								player->GetAttributes()->SetNumber(lineupIdx + 1);
							}
						}
						else
						{
							player->GetAttributes()->SetNumber(lineupIdx + 1);
						}
					}
				}

				csinfo.Setup(db_player, db_team, db_strip);
				csinfo.m_shirtNumber =
					(side != SIDE_OFFICIALS) ?
					player->GetAttributes()->GetNumber() :
					0;
			}

			player->SetCustomisationInfo(csinfo);

			team->AppendPlayer(player, true);
		}
	}

	// Rebuild the active lists
	RebuildPlayerList();

	// Reload the ball material if needed for the new teams
	LoadBallMaterial();
}

bool SIFGameWorld::TryGetMenuWomanOverrideDbIdByShirtNumber(SSTEAMSIDE side, int shirtNumber, unsigned short& outDbId) const
{
	MABASSERT(world_id == WORLD_ID::MENU);

	if (side >= SSTEAMSIDE::SIDE_NONE)
	{
		return false;
	}

	bool result = false;

	const TArray<PlayerOverride>& teamOverrides = MENU_WOMEN_OVERRIDES[side];

	for (int overrideIndex = 0; overrideIndex < teamOverrides.Num(); ++overrideIndex)
	{
		if (teamOverrides[overrideIndex].OverrideShirtNumber == shirtNumber)
		{
			outDbId = teamOverrides[overrideIndex].OverrideDbId;
			result = true;
			break;
		}
	}

	return result;
}

bool SIFGameWorld::TryGetMenuWomanOverrideByLineupIndex(SSTEAMSIDE side, int lineupIndex, PlayerOverride& outPlayerOverride) const
{
	MABASSERT(world_id == WORLD_ID::MENU);

	if (side >= SSTEAMSIDE::SIDE_NONE)
	{
		return false;
	}

	bool result = false;

	const TArray<PlayerOverride>& teamOverrides = MENU_WOMEN_OVERRIDES[side];

	for (int overrideIndex = 0; overrideIndex < teamOverrides.Num(); ++overrideIndex)
	{
		if (teamOverrides[overrideIndex].LineupIndex == lineupIndex)
		{
			outPlayerOverride = teamOverrides[overrideIndex];
			result = true;
			break;
		}
	}

	return result;
}

///-------------------------------------------------------------------------------
/// Given team_id, return a random opposition team, in the same competition class.
///-------------------------------------------------------------------------------

int SIFGameWorld::GetUIOppositionTeam(int team_id)
{
	int default_id = SIFApplication::GetApplication()->GetDefaultTeamB();

	const RUGameDatabaseManager* const game_database_mgr = SIFApplication::GetApplication()->GetGameDatabaseManager();

	//hack code to force a team pairing RC4-3340
	/*
	Team ID:
	NZ 1001
	AUS 1002
	ENG 1006
	SA 1016

	Pairing:
	AUS-NZ
	ENG-SA
	*/
	switch (team_id)
	{
		case 1051:
		return 1043;
		case 1018:
		return 1033;
		case 1016:
		return 1013;
		case 1013:
		return 1027;
		case 1025:
		return 1017;
		default:
		break;
	}

#ifdef ENABLE_GAME_DEBUG_MENU
	//#rc3_legacy_debug //maybe cutscene debug?
	/*int override_id = SIFDebug::GetCutsceneDebugSettings()->GetUIOppositionTeamDbId();
	if(override_id>1000)
	{
		MabString statement = MabString(0, "SELECT name FROM RUDB_TEAM WHERE id=%d",override_id);

		if( game_database_mgr == NULL )
		{
			MABBREAK();
			return SQLITEMAB_INVALID_ID;
		}

		SqliteMabStatement result;
		if(game_database_mgr->RawProcessSqlStatement(result, statement.c_str()))
		{
			if(result.Next())
				return override_id;			// Have found valid entry in teams - so can use...
		}
	}*/
#endif

	// YUK, NASTY....
	// Table of team db_id groupings, to find a suitable opponent.
	static const int groups[] = {
		1001, 1022,			// Start, finish	(If your favourite team is in this group, then opponent will be selected at random from this group)
		//1023, 1037,			// Start, finish
		//1038, 1051,			// ..,..
		//1076, 1099,
		//1100, 1105,
		//1108, 1109,
		//1135, 1143,
		1148, 1149,
		//1183, 1185,
		//1199, 1211,
		1213, 1216,
		//#rc3_no_trublu 1218, 1219,		// Tru Blu have been removed, so WW team will be matched up against a random team
		-2,
	};

	int *ptr = (int*)groups;
	while ((*ptr) != -2)
	{
		int start = *(ptr++);
		int end = *(ptr++);

		if (team_id >= start && team_id <= end)
		{
			int range = (end - start) + 1;
			int opponent_id = team_id + MabMath::RandInt(range);

			if (opponent_id == team_id)
			{
				opponent_id++;
			}

			if (opponent_id > end)
				opponent_id = start;

			if (opponent_id == DB_TEAMID_OFFICIALS_1)		// officials 1 is in middle of range 1001-1022!
			{
				opponent_id++;
				if (opponent_id == team_id)
					opponent_id = default_id;
			}

			// Check if team selected was valid.
			if (game_database_mgr->GetRL3Database()->GetTeamCache()->GetRowStart((unsigned short)opponent_id, false) == NULL)
			{
				MABLOGDEBUG("GetUIOppositionTeam has selected a non existant team: favourite=%d, selected(bad)=%d", team_id, opponent_id);
				MABBREAK();
				return default_id;
			}

			return opponent_id;
		}
	}

	int opponent_id = SIFGameHelpers::GAGetRandomOpponentFromCompetition(team_id);

	if (opponent_id != DB_INVALID_ID)
		return opponent_id;

	return default_id;
}

///-------------------------------------------------------------------------------
// Starts any loading of assets if required by the window change.
// Returns true if loading is required, a callback will be triggered once load is complete.
///-------------------------------------------------------------------------------

#if 0 //#rc3_legacy
bool SIFGameWorld::StartUILoadPendingIfRequired(const MabString& from_window, const MabString& to_window)
{
	/// Force lua garbage collection on every window change (really good thing to do here)
	SIFApplication::GetApplication()->GetWindowSystem()->GetLuaInterpreter()->ForceGarbageCollection();

	// If we're coming from the rulings settings, or profile load window, and going to the main menu, do the check
	// Or if we're coming from the competition selection screen, going into the careers hub, do the check
	if (((from_window == SIFUI_MATCH_RULINGS_SETTINGS_WINDOW_NAME || from_window == SIFUI_PROFILE_LOAD_WINDOW_NAME) && to_window == SIFUI_MAIN_MENU_WINDOW_NAME)/* ||
	(from_window == RUUI_COMPETITION_COMPETITION_INITIAL_SAVE_WINDOW_NAME && to_window == RUUI_COMPETITION_HUB_WINDOW_NAME)*/
	//|| (from_window == "CompetitionSaveAndQuit" && to_window == "MainMenu") // Left the career HUB, need to switch back to our home teams
	//|| (from_window == "CareerSaveAndQuit" && to_window == "MainMenu") // Left the career HUB, need to switch back to our home teams
		)
	{
		/// HACK: Quickload Load the 'pressed start' resource set.
		SIFApplication::GetApplication()->LoadPressStartResourceSet();

		/// Switch teams only when going from profile load ('PressStart') or from rules settings menu.
		if (GetRequiresSetHomeTeam())
		{
			// Go to stadium pan straight away.
			GetCutSceneManager()->SetUICutscene(CSEVENT_UI_PAN);

			GetCutSceneManager()->SetHomeTeamSwitchWhenFaded(true, false);

			//SetHomeTeam();
			return true;
		}
		GetCutSceneManager()->SetUICutscene(CSEVENT_UI_MAIN_MENU);
	}
	// We're going into the career HUB. Load our team that's playing.
	/*else if((from_window == "CompetitionLoad" && to_window == "CareerHUB")
		|| (from_window == "CareerLoad" && to_window == "CareerHUB")
		|| (from_window == "CompetitionInitialSave" && to_window == "CareerHUB")
		)
	{
		GetCutSceneManager()->SetUICutscene(CSEVENT_UI_PAN);
		GetCutSceneManager()->SetHomeTeamSwitchWhenFaded(true, true);

		//SetHomeTeam();
		return true;
	}*/
	/// Player customisation to/from cutscene setup.
	else if ((MabStringHelper::StartsWith(from_window, "CustomisePlayer", false)
#ifdef APPBUILD_RUGBY_CHALLENGE_3
		|| from_window == RUUI_LIST_PLAYERS_WINDOW_NAME
#endif
		) &&
		(to_window == SIFUI_MAIN_MENU_WINDOW_NAME || to_window == SIFUI_SPLASH_TITLE_WINDOW_NAME ||
		to_window == RUUI_CUSTOMISE_SELECT_PLAYER_WINDOW_NAME ||
		to_window == SIFUI_JOIN_INVITED_GAME_WINDOW_NAME ||
		to_window == RUUI_CUSTOMISE_DATA_RESET_WINDOW_NAME
#ifdef APPBUILD_RUGBY_CHALLENGE_3
		|| to_window == RUUI_SEARCH_PLAYERS_WINDOW_NAME
		|| to_window == RUUI_CUSTOMISE_EDIT_PLAYER_WINDOW_NAME
#endif
#ifdef ENABLE_PRO_MODE
		|| to_window == RUUI_CAREER_SETUP_WINDOW_NAME
		|| to_window == RUUI_CAREER_SELECT_TEAM_WINDOW_NAME
		|| to_window == RUUI_PRO_MODE_SETUP_WINDOW_NAME
		|| to_window == RUUI_PRO_MODE_SELECT_TEAM_WINDOW_NAME
#endif
		))
	{
		GetCutSceneManager()->SetUICutscene(CSEVENT_UI_MAIN_MENU, SSCutSceneManager::UI_CUTSCENE_DELETE_CUSTOMISATION_PLAYER);
		return true;
	}
	else if (from_window == RUUI_LOAD_PENDING_WINDOW_NAME
		&& to_window == SIFUI_SPLASH_TITLE_WINDOW_NAME)
	{
		SIFWindowSystem *window_system = SIFApplication::GetApplication()->GetWindowSystem();
		window_system->CancelLoadPending();
		GetCutSceneManager()->ClearCutSceneElements();
		GetCutSceneManager()->SetUICutscene(CSEVENT_UI_MAIN_MENU);
		return true;
	}
	else if ((to_window == RUUI_CUSTOMISE_PLAYER_DETAILS_WINDOW_NAME && from_window == SIFUI_MAIN_MENU_WINDOW_NAME) ||
		(from_window == RUUI_CUSTOMISE_SELECT_PLAYER_WINDOW_NAME && to_window == RUUI_CUSTOMISE_PLAYER_DETAILS_WINDOW_NAME) ||
		(strstr(from_window.c_str(), RUUI_CUSTOMISE_PLAYER_STANDARD_IDENTIFIER) && to_window == SIFUI_MAIN_MENU_WINDOW_NAME) ||
		(strstr(from_window.c_str(), RUUI_CUSTOMISE_PLAYER_STANDARD_IDENTIFIER) && to_window == SIFUI_JOIN_INVITED_GAME_WINDOW_NAME) ||
		(strstr(from_window.c_str(), RUUI_CUSTOMISE_PLAYER_STANDARD_IDENTIFIER) && to_window == RUUI_CUSTOMISE_DATA_RESET_WINDOW_NAME) ||
		(from_window == RUUI_CUSTOMISE_DATA_RESET_WINDOW_NAME && to_window == RUUI_CUSTOMISE_PLAYER_DETAILS_WINDOW_NAME)
#ifdef ENABLE_PRO_MODE
		// Fixes up the customisation player being set with the new menu flow of going through be a pro mode setup
		|| (from_window == RUUI_PRO_MODE_SETUP_WINDOW_NAME && to_window == RUUI_CUSTOMISE_PLAYER_DETAILS_WINDOW_NAME) // Coming from pro setup (deprecated)?
		|| (from_window == RUUI_PRO_MODE_SELECT_TEAM_WINDOW_NAME && to_window == RUUI_CUSTOMISE_PLAYER_DETAILS_WINDOW_NAME) // Coming from team selection, for pro career we can go back from team select to character creator
		|| (from_window == RUUI_CAREER_SETUP_WINDOW_NAME && to_window == RUUI_CUSTOMISE_PLAYER_DETAILS_WINDOW_NAME) // From the career setup (new flow for pro career)
		|| (from_window == RUUI_CAREER_SELECT_TEAM_WINDOW_NAME && to_window == RUUI_CUSTOMISE_PLAYER_DETAILS_WINDOW_NAME) // From the career team selection
		|| (from_window == RUUI_COMPETITION_HUB_WINDOW_NAME/*SIFUI_MAIN_MENU_WINDOW_NAME*/ && to_window == RUUI_CUSTOMISE_PLAYER_ACCESSORIES_WINDOW_NAME)
#endif
#ifdef APPBUILD_RUGBY_CHALLENGE_3
		//|| from_window == RUUI_LOAD_PENDING_WINDOW_NAME && to_window == RUUI_LIST_PLAYERS_WINDOW_NAME
		//|| from_window == RUUI_SEARCH_PLAYERS_WINDOW_NAME && to_window == RUUI_LIST_PLAYERS_WINDOW_NAME
		|| (from_window == RUUI_CUSTOMISE_EDIT_PLAYER_WINDOW_NAME && to_window == RUUI_CUSTOMISE_PLAYER_DETAILS_WINDOW_NAME) // Coming from pro setup (deprecated)?
#endif
		)
	{
		GetCutSceneManager()->SetUICutscene(CSEVENT_UI_CUSTOMIZE_PLAYER_BODY, SSCutSceneManager::UI_CUTSCENE_LOAD_CUSTOMISATION_PLAYER);
		return true;
	}
	else if (to_window == RUUI_INGAME_TROPHY_CEREMONY_WINDOW_NAME && from_window != RUUI_LOAD_PENDING_WINDOW_NAME)
	{
		GetCutSceneManager()->StartGrandFinalCelebrations();
		return true;
	}
	else if (to_window == SIFUI_SPLASH_TITLE_WINDOW_NAME)
	{
		// Movies have finished, now enable synclocking of async load thread (stops pausing during bink videos).
		SIFApplication::GetApplication()->GetAsyncLoadThread()->SetSyncLockPause(false);
	}
#ifdef APPBUILD_RUGBY_CHALLENGE_3
	if ((to_window == RUUI_LIST_TEAMS_WINDOW_NAME && from_window == SIFUI_MAIN_MENU_WINDOW_NAME)
		/*|| (to_window == RUUI_LIST_TEAMS_WINDOW_NAME && from_window == RUUI_CUSTOMISE_TEAM_WINDOW_NAME)*/)
	{
		GetCutSceneManager()->ResetTimer();
		GetCutSceneManager()->SetUICutscene(CSEVENT_UI_CUSTOMIZE_TEAM, SSCutSceneManager::UI_CUTSCENE_LOAD_LIST_TEAMS);
		return true;
	}
	else if ((to_window == RUUI_LIST_PLAYERS_WINDOW_NAME && from_window == SIFUI_MAIN_MENU_WINDOW_NAME)
		|| (from_window == RUUI_SEARCH_PLAYERS_WINDOW_NAME && to_window == RUUI_LIST_PLAYERS_WINDOW_NAME))
	{
		GetCutSceneManager()->ResetTimer();
		GetCutSceneManager()->SetUICutscene(CSEVENT_UI_CUSTOMIZE_PLAYER_BODY, SSCutSceneManager::UI_CUTSCENE_LOAD_LIST_PLAYERS);
		return true;
	}
	else if (to_window == SIFUI_MAIN_MENU_WINDOW_NAME && from_window == RUUI_LOAD_PENDING_WINDOW_NAME)
	{
		//GetCutSceneManager()->SetUICutscene(CSEVENT_UI_MAIN_MENU, SSCutSceneManager::UI_CUTSCENE_DELETE_CUSTOMISATION_PLAYER);
		SIFWindowSystem *window_system = SIFApplication::GetApplication()->GetWindowSystem();

		if (window_system->in_cancel_load)
			return true;

	}
#endif

	if (quick_load_active && from_window == SIFUI_MAIN_MENU_WINDOW_NAME && from_window != RUUI_LOAD_PENDING_WINDOW_NAME)
	{
		quick_load_send_ui_message = true;
		return true;
	}

	return false;
}
#endif

void SIFGameWorld::OnScreenEnter(FString screenEntered)
{
	if (screenEntered.Compare(Screens_UI::ProfileLoad) == 0)
	{
		return;
	}

	if ((IsMenu() || IsSandbox()) && screenEntered.Compare(Screens_UI::TitleScreen) != 0)
	{
		bool delayCameraTransition = false;

		if ((screenEntered.Compare(Screens_UI::CareerPlayerRecruiting) == 0)
			|| (screenEntered.Compare(Screens_UI::ProfileLoad) == 0))//this is slightly hacky version to force reloading the team when we add/remove player links or reload team when we enter the game for 1st time.
		{
			m_bForceReloadTeam = true;
		}

		if (m_teamSwapActive || m_quickLoadActive)
		{
			GetCutSceneManager()->SetUICutscene(CSEVENT_UI_PAN);
			delayCameraTransition = true;
		}
		else if (allow_team_reload)
		{
			if (screenEntered.Compare(Screens_UI::MainMenu) == 0
				|| screenEntered.Compare(Screens_UI::CustomiseSelectTeam) == 0 // For reload, if you edit a team that is your favourite team
				//|| screenEntered.Compare(Screens_UI::CareerHUB) == 0 // Not ideal, but necessary to reload the teams at the win/lose screen //JO- Commenting out as per Russell's comment for RC4-4516
				//|| screenEntered.Compare(Screens_UI::TrainingField) == 0
				)
			{
				int newHomeTeam = -1; int newAwayTeam = -1;
				if (GetPreferredSandboxTeams(newHomeTeam, newAwayTeam))
				{
#if LIMITED_GAME_WORLD_TEAM_SWITCHING
					allow_team_reload = false;
#endif

					GetCutSceneManager()->SetUICutscene(CSEVENT_UI_PAN);
					GetCutSceneManager()->SetHomeTeamSwitchWhenFaded(true, false);
					delayCameraTransition = true;
				}
			}
		}

		//Request the ui to fade in
		/*if (SIFApplication::GetApplication() && SIFApplication::GetApplication()->GetCurrentScreenTemplate())
		{
			SIFApplication::GetApplication()->GetCurrentScreenTemplate()->FadeIn();
		}*/

		if (delayCameraTransition)
		{
			m_delayedTransitionScreen.Add(screenEntered);
			return;
		}
	}

	//Request new cutscene
	if (GetCutSceneManager())
	{
		GetCutSceneManager()->UIRequestCameraTransition(TCHAR_TO_ANSI(*screenEntered));
	}

	ClearDelayedTransition();
}

void SIFGameWorld::TriggerDelayedTransition()
{
	if (!IsAwake())
	{
		ClearDelayedTransition();
		return;
	}

	if (GetCutSceneManager() && !GetCutSceneManager()->IsHomeTeamSwitchPending())
	{
		for (auto& CurrentDelayedScreen : m_delayedTransitionScreen)
		{
			GetCutSceneManager()->UIRequestCameraTransition(TCHAR_TO_ANSI(*CurrentDelayedScreen));
		}

		ClearDelayedTransition();
	}
}

void SIFGameWorld::ClearDelayedTransition()
{
	m_delayedTransitionScreen.Empty();
}

///-------------------------------------------------------------------------------
#ifdef ENABLE_LIVELINK

#define DDKEY_LIVELINK_BASE		((long)this+54)
#define DDKEY_LIVELINK_MAX		100

// Enable/Disable Livelink
void SIFGameWorld::EnableLiveLink(bool enable)
{
	if (enable)
	{
		if (livelink_inst == NULL)
		{
			// Create livelink instance
			livelink_inst = MabMemNew(heap) LiveLinkInstance();

			// Cache player's network to livelink for hookup
			livelink_inst->clearNetworkInstances();
			for (size_t i = 0; i < players.size(); ++i)
			{
				/*#rc3_legacy
				Network* network = all_players[i]->GetComponent<NMMabAnimationNetwork>()->GetNetwork();
				livelink_inst->addNetworkInstance( network, players[i]->GetAttributes()->GetCombinedName().c_str() );
				*/
			}
		}
	}
	else
	{
		if (livelink_inst)
		{
			// Clear debug menu flag
#if defined ENABLE_ANIMATION_DEBUG_SETTINGS
			RUAnimationDebugSettingsResetLiveLink();
#endif

			// Clear cached network instances
			livelink_inst->clearNetworkInstances();

			// Delete instance
			MabMemDeleteSafe(livelink_inst);
			livelink_inst = NULL;

			// Stop display player names
			for (size_t i = 0; i < DDKEY_LIVELINK_MAX; ++i)
			{
				SIF_DEBUG_DRAW(RemoveText(DDKEY_LIVELINK_BASE + i));
			}
		}
	}
}

// Update livelink
void SIFGameWorld::UpdateLiveLink()
{
	if (!livelink_inst)
		return;

	// Display player names
	for (size_t i = 0; i < players.size(); ++i)
	{
		ARugbyCharacter* player = players[i];
		SETDEBUGTEXTWORLD(DDKEY_LIVELINK_BASE + i, player->GetMovement()->GetCurrentPosition() + FVector(0, 2.4, 0), player->GetAttributes()->GetLastName());
	}

	// Update
	livelink_inst->update();
}

#endif
///-------------------------------------------------------------------------------

