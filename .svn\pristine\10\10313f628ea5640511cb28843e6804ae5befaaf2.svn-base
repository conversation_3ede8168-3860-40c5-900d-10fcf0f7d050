/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/Rules/RURuleConsequences.h"

#include "Match/AI/Formations/SSEVDSFormationEnum.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/Ball/SSBall.h"
#include "Match/Camera/SSCameraDirector.h"
#include "Match/Camera/SSCameraManager.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/HUD/RUHumanPlayerColours.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURuleTriggerEnum.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/RugbyUnion/Statistics/RUStatisticsSystem.h"
#include "Match/SIFGamePauseState.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSGameTimerHalfObserver.h"
#include "Match/SSSpatialHelper.h"
#include <Rugby/Utility/Helpers/SIFGameHelpers.h>

//#rc3_legacy_include #include "SIFUIHelpers.h"


RURuleConsequences::RURuleConsequences(SIFGameWorld* game) : game(game)
{
	// Roy, changed the switch statement to an array of function pointers due
	// to a gradual decline in frame rate as the switch statement got bigger
	handlers.fill(nullptr);

	handlers[RUC_HALF_TIME]					= &RURuleConsequences::HalfTime;
	handlers[RUC_FULL_TIME]					= &RURuleConsequences::FullTime;
	handlers[RUC_TRY_SUCCESS]				= &RURuleConsequences::TryScoreSuccess;
	handlers[RUC_22_DROP_OUT]				= &RURuleConsequences::TwentyTwoDropOut;
	
	
	handlers[RUC_SCRUM]						= &RURuleConsequences::StartScrum;

	// WJS RLC Will we need this in the end? What other ways are scrums triggered
	handlers[RUC_TOUCH_SCRUM]				= &RURuleConsequences::StartTouchScrum;  
	
	// WJS RLC ##### TODO REMOVE
	handlers[RUC_LINEOUT]					= &RURuleConsequences::StartLineout;



	handlers[RUC_REKICK]					= &RURuleConsequences::ReKick;
	handlers[RUC_PENALTY_KICK_FOR_GOAL]		= &RURuleConsequences::PenaltyKickGoal;
	handlers[RUC_PENALTY_KICK_FOR_TOUCH]	= &RURuleConsequences::PenaltyKickTouch;
	handlers[RUC_PENALTY_TAP]				= &RURuleConsequences::PenaltyTapAndGo;
	handlers[RUC_FREE_KICK]					= &RURuleConsequences::StartFreeKick;
	handlers[RUC_CONVERSION_SUCCESS]		= &RURuleConsequences::ConversionSuccess;
	handlers[RUC_CONVERSION_FAILURE]		= &RURuleConsequences::ConversionFailure;
	handlers[RUC_PENALTY_GOAL_SUCCESS]		= &RURuleConsequences::PenaltyGoalSuccess;
	handlers[RUC_PENALTY_GOAL_FAILURE]		= &RURuleConsequences::PenaltyGoalFailure;
	handlers[RUC_DROP_GOAL_SUCCESS]			= &RURuleConsequences::DropGoalSuccess;
	handlers[RUC_DROP_GOAL_FAILURE]			= &RURuleConsequences::DropGoalFailure;
	handlers[RUC_RUCK]						= &RURuleConsequences::RuckFormed;
	handlers[RUC_MAUL]						= &RURuleConsequences::PlayTheBall; // &RURuleConsequences::MaulFormed; Disable maul from forming
	handlers[RUC_CONTINUE_PLAY]				= &RURuleConsequences::ContinuePlay;
	handlers[RUC_RESTART_FROM_PLAY]			= &RURuleConsequences::RestartFromPlay;
	handlers[RUC_LINEOUT_FROM_DECISION]     = &RURuleConsequences::StartLineOutFromDecision;
	handlers[RUC_SELECT_THREE_PLAYER_LINEOUT] = &RURuleConsequences::SelectThreePlayerLineout;
	handlers[RUC_SELECT_FIVE_PLAYER_LINEOUT] = &RURuleConsequences::SelectFivePlayerLineout;
	handlers[RUC_SELECT_SEVEN_PLAYER_LINEOUT] = &RURuleConsequences::SelectSevenPlayerLineout;
	handlers[RUC_PENALTY_QUICK_TAP]			= &RURuleConsequences::PenaltyTapAndGo;
	handlers[RUC_PLAY_THE_BALL]				= &RURuleConsequences::PlayTheBall;
}

RURuleConsequences::~RURuleConsequences()
{
}

void RURuleConsequences::StartConsequence(RURuleConsequence consequence)
{
#ifdef ENABLE_DEBUG_RULE_MONITORING
	MABLOGMSG(LOGCHANNEL_DEBUG, LOGTYPE_INFO, "RulesTrigger Call: RURuleConsequences::StartConsequence");
#endif
	if(handlers[consequence] != NULL)
		(*this.*handlers[consequence])();
}

void RURuleConsequences::ContinuePlay()
{
	game->GetGameState()->SetPhase( RUGamePhase::PLAY );
}


void RURuleConsequences::StartTouchScrum()
{
	/// Fire an event to let others know a touch scrum will now begin
	FVector position = game->GetGameState()->GetPlayRestartPosition();
	MabMath::Clamp(position.x, -FIELD_WIDTH * 0.5f + 20.0f, FIELD_WIDTH * 0.5f - 20.0f);
	MabMath::Clamp(position.z, -FIELD_LENGTH * 0.5f + 10.0f, FIELD_LENGTH * 0.5f - 10.0f);
	game->GetGameState()->SetPlayRestartPosition(position);

	bool wasFromKickOff = false;	
	BallFreeInfo ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();
	KickType kick_type = (KickType)ball_free_info.sub_type;
	if (kick_type == KICKTYPE_KICKOFF)
	{
		wasFromKickOff = true;
	}
	
	if (!wasFromKickOff)
	{
		game->GetGameState()->SetHandoverType(EHandoverType::INTOUCH);
	}

	game->GetEvents()->touch_scrum_signalled(game->GetGameState()->GetPlayRestartPosition(), false); // In StartTouchScrum()
	/// Game logic moved to SSCutSceneManager::StartTouchScrumCallback()
}


void RURuleConsequences::StartLineout()
{
	/// Fire an event to let others know a lineout will now begin
	game->GetEvents()->pre_lineout_signalled();
	game->GetEvents()->lineout_signalled( game->GetGameState()->GetPlayRestartPosition(), false ); // In StartLineout()

	/// Game logic moved to SSCutSceneManager::StartLineoutCallback()
}

void RURuleConsequences::StartLineOutFromDecision()
{
	game->GetEvents()->pre_lineout_signalled();
	game->GetEvents()->lineout_signalled( game->GetGameState()->GetPlayRestartPosition(), true ); // in StartLineOutFromDecision()
}

void RURuleConsequences::SelectThreePlayerLineout()
{
	SelectLineoutPlayers( RUTeamStrategy::LONO_3 );
}

void RURuleConsequences::SelectFivePlayerLineout()
{
#ifdef FORCE_3_MAN_LINEOUT
	SelectLineoutPlayers( RUTeamStrategy::LONO_3 );
#else
	SelectLineoutPlayers( RUTeamStrategy::LONO_5 );
#endif // FORCE_3_MAN_LINEOUT
}

void RURuleConsequences::SelectSevenPlayerLineout()
{
#ifdef FORCE_3_MAN_LINEOUT
	SelectLineoutPlayers( RUTeamStrategy::LONO_3 );
#else
	SelectLineoutPlayers( RUTeamStrategy::LONO_7 );
#endif // FORCE_3_MAN_LINEOUT
}

void RURuleConsequences::SelectLineoutPlayers( int enum_val )
{
	game->GetGameState()->GetPlayRestartTeam()->GetStrategy().SetLineoutNumbersOption( (RUTeamStrategy::LINEOUT_NUMBERS_OPTION) enum_val );
	game->GetEvents()->lineout_numbers_changed( enum_val );
	game->GetGameState()->SetPhase( RUGamePhase::LINEOUT );
}

//************************************
// called when there was a successful try scored
//************************************

void RURuleConsequences::TryScoreSuccess()
{
	RUGameState* state = game->GetGameState();

	ARugbyCharacter* last_ballholder = state->GetLastBallHolder();

	// Abort all actions on the player that scored the try.
	// If an animation that puts the ball down was played, this is probably the last ballholder.
	// Otherwise, if the player retained the ball, they'll be the current ballholder.
	if ( last_ballholder )
	{
		last_ballholder->GetActionManager()->AbortAllActions();
	}
	else
	{
		ARugbyCharacter* ballholder = state->GetBallHolder();

		if ( ballholder )
		{
			ballholder->GetActionManager()->AbortAllActions();
		}
	}

	MabString teamName = state->GetAttackingTeam()->GetDbTeam().GetName();
	RUHUDUpdater::CensorTeamName(&state->GetAttackingTeam()->GetDbTeam(), teamName);

	// XXX :jb UNTRANSLATED
	MabString message(32, "%s successfully make a try", teamName.c_str());
	game->GetHUDUpdater()->SetScreenMessage("[ID_TRY_SCORED]", "[ID_TRY_SCORED]", message.c_str(), 1.0f, 3.0f );
	game->GetEvents()->try_awarded( );
}

//************************************
// called when there was a successful conversion
//************************************

void RURuleConsequences::ConversionSuccess()
{
	RUGameState* state = game->GetGameState();

	MabString playerName = state->GetLastKicker()->GetAttributes()->GetDBPlayer()->GetLastName();

	RUHUDUpdater::CensorPlayerName(&state->GetLastKicker()->GetAttributes()->GetTeam()->GetDbTeam(),state->GetLastKicker()->GetAttributes()->GetDBPlayer(),playerName);
	// XXX :jb untranslated
	MabString message(32, "%s is successful", playerName.c_str());
	game->GetHUDUpdater()->SetScreenMessage("[ID_CONVERSION]", "[ID_CONVERSION]", message.c_str(), 1.0f, 3.0f );

	// do not call this event as this event is what the trigger for this consequence is looking for!!!
	//game->GetEvents()->conversion(true, game->GetBall()->GetCurrentPosition());
	//RestartKick();
}

//************************************
// called when there was a failed conversion
//************************************

void RURuleConsequences::ConversionFailure()
{
	RUGameState* state = game->GetGameState();
	MabString playerName =  state->GetLastKicker()->GetAttributes()->GetDBPlayer()->GetLastName();
	RUHUDUpdater::CensorPlayerName(&state->GetLastKicker()->GetAttributes()->GetTeam()->GetDbTeam(),state->GetLastKicker()->GetAttributes()->GetDBPlayer(),playerName);
	// XXX :jb untranslated
	MabString message(32, "%s misses", playerName.c_str());
	game->GetHUDUpdater()->SetScreenMessage("[ID_CONVERSION]", "[ID_CONVERSION]", message.c_str(), 1.0f, 3.0f );

	// do not call this event as this event is what the trigger for this consequence is looking for!!!
	//game->GetEvents()->conversion(false, game->GetBall()->GetCurrentPosition());
	//RestartKick();
}

//************************************
// called when a 22 drop out is required
//************************************

void RURuleConsequences::TwentyTwoDropOut()
{
	if (game->GetHUDUpdater())
		game->GetHUDUpdater()->SetImpactText("[ID_DEAD_BALL]");

	game->GetEvents()->cutscene_dropout();
}

//************************************
// called when a scrum is required
//************************************

void RURuleConsequences::StartScrum()
{
	RUGameState* state = game->GetGameState();

	if (game->GetGameState()->IsFifthTackle())
	{
		if (game->GetGameState()->IsNearFieldEdge(game->GetGameState()->GetLastBallHolder()->GetMovement()->GetCurrentPosition(), 10.0f, 20.0f))
		{
			// Toos close to side lines move restart position
			FVector new_restart_pos = game->GetGameState()->ClampAwayFromFieldEdge(game->GetGameState()->GetPlayRestartPosition(), 10.0f, 20.0f);
			game->GetGameState()->SetPlayRestartPosition(new_restart_pos);
		}
		game->GetGameState()->Handover();
		return;
	}

	FVector position = state->GetPlayRestartPosition();

	if (game->GetGameState()->IsNearFieldEdge(position, 10.0f, 20.0f))
	{
		// Toos close to side lines move restart position
		FVector new_restart_pos = game->GetGameState()->ClampAwayFromFieldEdge(position, 10.0f, 20.0f);
		game->GetGameState()->SetPlayRestartPosition(new_restart_pos);
	}
	else
	{
		state->SetPlayRestartPosition( position );
	}

	state->SetPhase(RUGamePhase::SCRUM);

	if(!game->GetGameTimer()->ScrumCancelledByHalfOver())
	{
		MabString teamName = state->GetAttackingTeam()->GetDbTeam().GetShortName();
		RUHUDUpdater::CensorTeamName(&state->GetAttackingTeam()->GetDbTeam(), teamName);

		MabString message(32, "%s", teamName.c_str());

		FLinearColor colour;
#ifdef USE_TEAM_COLOURS_FOR_IMPACT_TEXT
		MabColour mabColour = MabColour::Zero;
		state->GetAttackingTeam()->GetDbTeam().GetPrimaryColour(mabColour);
		colour = FLinearColor(mabColour.r, mabColour.g, mabColour.b, mabColour.a);
#else
		SSHumanPlayer* human = state->GetAttackingTeam()->GetHumanPlayer(0);
		if(human) colour = human->IsNetworkPlayer() ? RU_REMOTE_PLAYER_COLOURS[ human->GetIndex() ] : RU_LOCAL_PLAYER_COLOURS[ human->GetIndex() ];
#endif
		//if (game->GetHUDUpdater())
		//	game->GetHUDUpdater()->SetImpactText("[ID_SCRUM_SET_BY]", SIFGameHelpers::GAConvertMabStringToFString(message), colour, RU_IMPACT_ANIMATION_SLIDE_IN_FADE);
	}

	game->GetEvents()->scrum(state->GetAttackingTeam());
}

//************************************
// called when play will be restarted from kickoff
//************************************

void RURuleConsequences::RestartKick()
{
	game->RestartKick();
}

//************************************
// called when a kick from a set play, (22, kickoff) has been requested to be redone
//************************************

void RURuleConsequences::ReKick()
{
	BallFreeInfo ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();
	KickType kick_type = (KickType) ball_free_info.sub_type;
	MABASSERT(kick_type == KICKTYPE_DROPKICK || kick_type == KICKTYPE_KICKOFF);

	// If the kick type is none you're going to have a very bad time.
	// This is possible if the ball is kicked out on the full and the opposition catches it, then passes it to a player (quick lineout?)
	//if( kick_type == KICKTYPE_NONE )
	//{
		kick_type = KICKTYPE_DROPKICK;
	//}


	RUGameState* state = game->GetGameState();
	state->SetKickRestartKickType( kick_type );

	if(kick_type == KICKTYPE_DROPKICK)
	{
		state->SetPlayRestartPosition( ball_free_info.pos );
	}
	else
	{
		state->SetPlayRestartPosition( FVector::ZeroVector );
	}

	state->SetPlayRestartTeam(ball_free_info.last_player->GetAttributes()->GetTeam());

	if(kick_type == KICKTYPE_DROPKICK)
	{
		state->SetPhase(RUGamePhase::DROPOUT);
	}
	else
	{
		state->SetPhase(RUGamePhase::KICK_OFF);
	}
}

//************************************
// called when a free kick has been awarded
//************************************

void RURuleConsequences::StartFreeKick()
{
	RUGameState* state = game->GetGameState();

	// Impact text/popup handling etc, will be taken care off in RUHUDUpdater
	/*MabString message(32, "Taken by %s", state->GetAttackingTeam()->GetDbTeam().GetName());
	game->GetHUDUpdater()->SetScreenMessage("[ID_FREE_KICK]", "[ID_FREE_KICK]", message.c_str(), 1.0f, 3.0f );

	if (game->GetHUDUpdater())
		game->GetHUDUpdater()->SetImpactText("[ID_FREE_KICK]");*/

	state->SetKickRestartKickType( KICKTYPE_FREEKICK );
	state->SetPhase( RUGamePhase::FREE_KICK );

	FVector position = state->GetPlayRestartPosition();
	MabMath::Clamp(position.x, -FIELD_WIDTH * 0.5f + 10.0f, FIELD_WIDTH * 0.5f - 10.0f);
	state->SetPlayRestartPosition(position);

	game->GetEvents()->free_kick_awarded(nullptr);
}

//************************************
// called when a ruck is formed
//************************************
// ** OLD RUCK CALL, NO LONGER IN USE! **
// ** NOTE We potentially could use some
// ** of this in ankle tackles or when
// ** players slide to pick up the ball
// ** to create a pile-on/tackle situation.
// ** Look into the 'breakdown' in tackles
void RURuleConsequences::RuckFormed()
{
	if (game)
	{
		if (RUGameState* state = game->GetGameState())
		{
			if (state->GetPhase() == RUGamePhase::LINEOUT)
			{
				state->SetPhase(RUGamePhase::LINEOUT_RUCK);
				MABBREAKMSG("Tyrone very interested in what this does?");
			}
			else {
				if (game->GetGameState()->GetPhase() == RUGamePhase::PLAY)
					game->GetGameState()->GetAttackingTeam()->GetFormationManager()->OverrideAreaNumPlayers("Halfback", 0, ERugbyFormationRole::FORMATION);

				state->SetPhase(RUGamePhase::RUCK);
			}
		}
	}
}

//************************************
// called when play the ball is called 
// from a tackle
//************************************
void RURuleConsequences::PlayTheBall()
{
	if (game)
	{
		if (RUGameState* state = game->GetGameState())
		{
			state->IncrementTackleCount();

			state->SetPhase(RUGamePhase::PLAY_THE_BALL);
			return;
		}
	}
}

//************************************
// called when a maul is formed
//************************************

void RURuleConsequences::MaulFormed()
{
	RUGameState* state = game->GetGameState();
	if(state->GetPhase() == RUGamePhase::LINEOUT)
	{
		state->SetPhase(RUGamePhase::LINEOUT_MAUL);
		MABBREAKMSG( "Joe very interested in what this does?");
	}
	else
		state->SetPhase(RUGamePhase::MAUL);
}

//************************************
// called in the mini-game for retrieving a ball without using a lineout/ball dead to handle triggers for these
//************************************

void RURuleConsequences::RestartFromPlay()
{
	RUGameState* state = game->GetGameState();
	ARugbyCharacter* last_ball_holder = state->GetLastBallHolder();
	if ( last_ball_holder )
	{
		SSHumanPlayer* human = last_ball_holder->GetHumanPlayer();
		if (human)
		{
			human->ClearCachedOption();
		}

		// Clear any active actions to catch any that are locked up
		RUActionManager* action_manager = last_ball_holder->GetActionManager();
		if ( action_manager )
			action_manager->AbortAllActions();
	}

	if (state->GetBallHolder() == NULL)
	{
		
		game->GetCameraManager()->SetCameraSnap(0);
		state->SetBallHolder(state->GetLastBallHolder(), true);

		// Clamp the player who gets the ball to the field
		FieldExtents extents = game->GetSpatialHelper()->GetClampFieldExtents();
		FVector curr_pos = last_ball_holder->GetMovement()->GetCurrentPosition();
		MabMath::Clamp( curr_pos.x, -extents.x, extents.x );
		MabMath::Clamp( curr_pos.y, -extents.y, extents.y );
		last_ball_holder->GetMovement()->SetCurrentPosition( curr_pos );
	}

	game->GetInputManager()->DebounceAllActionsForAllPlayers();

	game->GetRules()->SuspendPlay(false, "From Sandbox Custom ball out");
}

//************************************
// called when a drop goal went through the posts
//************************************

void RURuleConsequences::DropGoalSuccess()
{
	game->GetEvents()->drop_goal(true, game->GetBall()->GetCurrentPosition());
	game->GetEvents()->commentary_drop_goal_result(true, game->GetBall()->GetCurrentPosition());
	game->GetEvents()->drop_goal_detected();
}

void RURuleConsequences::PenaltyGoalSuccess()
{
	RUGameState* state = game->GetGameState();
	MabString playerName = state->GetLastKicker()->GetAttributes()->GetDBPlayer()->GetLastName();
	RUHUDUpdater::CensorPlayerName(&state->GetLastKicker()->GetAttributes()->GetTeam()->GetDbTeam(),game->GetGameState()->GetLastKicker()->GetAttributes()->GetDBPlayer(),playerName);
	//XXX :jb untranslated
	MabString message( 32, "%s is successful", playerName.c_str() );
	game->GetHUDUpdater()->SetScreenMessage( "[ID_PENALTY_GOAL]", "[ID_PENALTY_GOAL]", message.c_str(), 1.0f, 3.0f );
}

void RURuleConsequences::PenaltyGoalFailure()
{
	game->GetGameState()->SetPhase( RUGamePhase::PLAY );
}

//************************************
// called when a drop goal does not go through the posts
//************************************

void RURuleConsequences::DropGoalFailure()
{
	game->GetEvents()->drop_goal(false, game->GetBall()->GetCurrentPosition());
	game->GetEvents()->commentary_drop_goal_result(false, game->GetBall()->GetCurrentPosition());
//	TwentyTwoDropOut();		// Removed - What happens if ball doesn't go in to touch????
}

//************************************
// called at halftime
//************************************

void RURuleConsequences::HalfTime()
{
}

//************************************
// called at full time
//************************************

void RURuleConsequences::FullTime()
{
}

//************************************
// penalty awarded, player opted to take a kick at goal for 3 points
//************************************

void RURuleConsequences::PenaltyKickGoal()
{
	RUGameState* state = game->GetGameState();

	RUTeam *attacking_team = state->GetPlayRestartTeam();
	state->SetAttackingTeam(attacking_team);

	MabString teamName = attacking_team->GetDbTeam().GetName();
	RUHUDUpdater::CensorTeamName(&attacking_team->GetDbTeam(), teamName);

	// XXX :jb untranslated
	MabString message(32, "Penalty taken by %s", teamName.c_str());
	game->GetHUDUpdater()->SetScreenMessage("[ID_PENALTY_KICK_GOAL]", "[ID_PENALTY_KICK_GOAL]", message.c_str(), 1.0f, 3.0f );

	game->GetRules()->SuspendPlay(false, "RURuleConsequences::PenaltyKickGoal");

	game->GetHUDUpdaterContextual()->Stop2DContextualDisplay();

	
	game->GetCameraManager()->SetCameraSnap();
	state->SetPhase(RUGamePhase::PRE_PENALTY_SHOOT_FOR_GOAL);
	game->GetEvents()->cutscene_kickforpoints(attacking_team);
}

//************************************
// penalty awarded, player opted to take a kick for touch
//************************************

void RURuleConsequences::PenaltyKickTouch()
{
	RUGameState* state = game->GetGameState();

	MabString teamName = state->GetAttackingTeam()->GetDbTeam().GetName();
	RUHUDUpdater::CensorTeamName(&state->GetAttackingTeam()->GetDbTeam(), teamName);

	// XXX :jb untranslated
	MabString message(32, "Penalty taken by %s", teamName.c_str());
	game->GetHUDUpdater()->SetScreenMessage("[ID_PENALTY_KICK_TOUCH]", "[ID_PENALTY_KICK_TOUCH]", message.c_str(), 1.0f, 3.0f );

	// Make sure we don't have people in a ruck or something else annoying.
	game->GetStrategyHelper()->ClearRoles(game->GetTeam(0));
	game->GetStrategyHelper()->ClearRoles(game->GetTeam(1));
	game->GetGameState()->SetKickRestartKickType( KICKTYPE_PENALTYPUNT );

	game->GetRules()->SuspendPlay(false, "RURuleConsequences::PenaltyKickTouch");
	state->SetPhase( RUGamePhase::PENALTY_KICK_FOR_TOUCH );

	
	game->GetCameraManager()->SetCameraSnap();
}

//************************************
// penalty awarded, player opted to tap and go
//************************************

void RURuleConsequences::PenaltyTapAndGo()
{
	RUGameState* state = game->GetGameState();

	MabString teamName = state->GetAttackingTeam()->GetDbTeam().GetName();
	RUHUDUpdater::CensorTeamName(&state->GetAttackingTeam()->GetDbTeam(), teamName);

	// XXX :jb untranslated
	MabString message(32, "Penalty taken by %s", teamName.c_str());
	game->GetHUDUpdater()->SetScreenMessage("[ID_PENALTY_TAP]", "[ID_PENALTY_TAP]", message.c_str(), 1.0f, 3.0f );

	//state->SetBallHolder(state->GetPlayRestartTeam()->GetPlayKicker());

	// Make sure we don't have people in a ruck or something else annoying.
	//game->GetStrategyHelper()->ClearRoles(game->GetTeam(0));
	//game->GetStrategyHelper()->ClearRoles(game->GetTeam(1));

	game->GetRules()->SuspendPlay(false, "RURuleConsequences::PenaltyTapAndGo");

	if ( state->GetPhase() == RUGamePhase::ELECT_QUICK_TAP )
	{
		state->SetPhase( RUGamePhase::QUICK_TAP_PENALTY );
	}
	else
	{
		// Reset the pro player, because the decision maker could still be in control
		if(game->GetGameSettings().game_settings.GetIsAProMode())
		{
			game->GetTeam(0)->AssignBestPlayer();
			game->GetTeam(1)->AssignBestPlayer();
		}

		state->SetPhase( RUGamePhase::PENALTY_TAP_RESTART );
	}
}
