
/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Actions/RUActionPass.h"

#include "Match/AI/Actions/RUActionInterceptBall.h"
#include "Match/AI/Actions/RUActionPassAnticipation.h"
#include "Match/AI/Actions/RUActionTackleBase.h"
#include "Match/AI/Actions/RUActionTacklee.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/Ball/SSBall.h"
#include "Match/Camera/SSCameraManager.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerLookAt.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Debug/RUGameDebugSettings.h"
#include "Match/PlayerProfile/SIFPlayerProfileConstants.h"
#include "Match/RugbyUnion/RUBlackboard.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Match/SSPlayerFilter.h"
#include "Match/SSRole.h"
#include "Match/SSSpatialHelper.h"
#include "Utility/RURandomNumberGenerator.h"

//#rc3_legacy_include #include "NMMabAnimationEvents.h"
//#rc3_legacy_include #include "NMMabAnimationNetwork.h"
//#rc3_legacy_include #include "SIFDebug.h"
//#rc3_legacy_include #include <MabNetworkManager.h>
//#rc3_legacy_include #include <Tackle.h>

#include "RugbyGameInstance.h"
#include "Rugby/Character/RugbyCharacterAnimInstance.h"

#ifdef ENABLE_GAME_DEBUG_MENU
#include "Match/Debug/RUGameDebugSettings.h"
#endif
#include "../Roles/Competitors/SetPlays/RURoleRuckSendRunner.h"
#include "Character/RugbyCharacter.h"
#include "../SetPlays/SSSetPlayManager.h"

//Debug include
/*#ifdef WITH_EDITOR
#include "DrawDebugHelpers.h"
#endif*/

#define CENTRE_PASS_ANGLE_RANGE	MabMath::Deg2Rad( 35.0f )
// TEMP - until we get animations that allow us to pass behind
//#define SIDE_PASS_ANGLE_RANGE	MabMath::Deg2Rad( 155.0f )
#define SIDE_PASS_ANGLE_RANGE	MabMath::Deg2Rad( 180.0f )

#ifdef ENABLE_GAME_DEBUG_MENU
const unsigned int DEBUG_PASS_MARKER_KEY = 4573488;
#endif

#define NO_ROTATION 1e10f

static const float IMPOSSIBLE_DIFFICULTY = 1e10f;

//static const MabTypeID pass_inclusion_types[] = { RURoleTouchKick::RTTGetStaticType(), RURoleAttackingKick::RTTGetStaticType(), RURoleInGoalKick::RTTGetStaticType(), RURoleUpAndUnderKick::RTTGetStaticType(), RURoleFieldGoalKick::RTTGetStaticType() };
//static const int n_pass_inclusion_types = sizeof( pass_inclusion_types ) / sizeof( MabTypeID );

class PassAnimation
{
public:
	const char* base_name;
	float speed;
	// if the passer was facing 0 degrees, this is the relative angle that the ball will leave his hands
	// aka best angle for the pass to look good.
	float best_angle;
};

struct PassAnimations
{
	float distance;
	PassAnimation animations [16];
} ;

// GENERIC Pass
static PassAnimations standard_animations[] =
{
	{
		0.0f,
		{
			{ "medium_pass_forward", MEDIUM_PASS_SPEED, 0.0f },
			{ "medium_pass_l45", MEDIUM_PASS_SPEED, PI / 4.0f },
			{ "medium_pass_left", MEDIUM_PASS_SPEED, PI / 2.0f },
			{ "medium_pass_l135", MEDIUM_PASS_SPEED, 3.0f * PI / 4.0f },
			{ "medium_pass_r45", MEDIUM_PASS_SPEED, -PI / 4.0f },
			{ "medium_pass_right", MEDIUM_PASS_SPEED, -PI / 2.0f },
			{ "medium_pass_r135", MEDIUM_PASS_SPEED, -3.0f * PI / 4.0f },
			{ "medium_pass_behind", MEDIUM_PASS_SPEED, PI },
			{ NULL, 0.0f, 0.0f }
		}
	},
	{
		MEDIUM_PASS_DIST,
		{
			{ "medium_pass_forward", MEDIUM_PASS_SPEED, 0.0f },
			{ "medium_pass_l45", MEDIUM_PASS_SPEED, PI / 4.0f },
			{ "medium_pass_left", MEDIUM_PASS_SPEED, PI / 2.0f },
			{ "medium_pass_l135", MEDIUM_PASS_SPEED, 3.0f * PI / 4.0f },
			{ "medium_pass_r45", MEDIUM_PASS_SPEED, -PI / 4.0f },
			{ "medium_pass_right", MEDIUM_PASS_SPEED, -PI / 2.0f },
			{ "medium_pass_r135", MEDIUM_PASS_SPEED, -3.0f * PI / 4.0f },
			{ "medium_pass_behind", MEDIUM_PASS_SPEED, PI },
			{ NULL, 0.0f, 0.0f }
		}
	},
	{
		LONG_PASS_DIST,
		{
			{ "long_pass_forward", LONG_PASS_SPEED, 0.0f },
			{ "long_pass_l45", LONG_PASS_SPEED, PI / 4.0f },
			{ "long_pass_left", LONG_PASS_SPEED, PI / 2.0f },
			{ "long_pass_l135", LONG_PASS_SPEED, 3.0f * PI / 4.0f },
			{ "long_pass_r45", LONG_PASS_SPEED, -PI / 4.0f },
			{ "long_pass_right", LONG_PASS_SPEED, -PI / 2.0f },
			{ "long_pass_r135", LONG_PASS_SPEED, -3.0f * PI / 4.0f },
			{ "long_pass_behind", LONG_PASS_SPEED, PI },
			{ NULL, 0.0f, 0.0f }
		}
	},
	{
		MAX_PASS_DIST,
		{
			{ NULL, 0.0f, 0.0f }
		}
	}
};

// OFFLOAD PASS
static PassAnimations offload_animations[] =
{
	{
		0.0f,
		{
			{ "forward", OFFLOAD_PASS_SPEED, 0.0f },
			{ "left", OFFLOAD_PASS_SPEED, PI / 2.0f },
			{ "behind", OFFLOAD_PASS_SPEED, PI },
			{ "right", OFFLOAD_PASS_SPEED, -PI / 2.0f },
			{ NULL, 0.0f, 0.0f }
		}
	},
	{
		MAX_OFFLOAD_DISTANCE,
		{
			{ NULL, 0.0f, 0.0f }
		}
	}
};

// DUMMY PASS
static PassAnimations dummy_animations[] =
{
	{
		0.0f,
		{
			{ "dummy_pass_forward", SHORT_PASS_SPEED, 0.0f },
			{ "dummy_pass_left", SHORT_PASS_SPEED, PI / 2.0f },
			{ "dummy_pass_right", SHORT_PASS_SPEED, -PI / 2.0f },
			{ NULL, 0.0f, 0.0f }
		}
	},
	{
		MAX_PASS_DIST,
		{
			{ NULL, 0.0f, 0.0f }
		}
	}
};

// DUMMY HALF PASS
static PassAnimations dummyhalf_animations[] =
{
	{
		0.0f,
		{
			{ "grab_pass_left", SHORT_PASS_SPEED, PI / 2.0f },
			{ "grab_pass_right", SHORT_PASS_SPEED, -PI / 2.0f },
			{ NULL, 0.0f, 0.0f }
		}
	},
	{
		MEDIUM_PASS_DIST,
		{
			{ "grab_pass_left", MEDIUM_PASS_SPEED, PI / 2.0f },
			{ "grab_pass_right", MEDIUM_PASS_SPEED, -PI / 2.0f },
			{ "grab_pass_back_left", MEDIUM_PASS_SPEED, 3.0f * PI / 4.0f },
			{ "grab_pass_back_right", MEDIUM_PASS_SPEED, -3.0f * PI / 4.0f },
			{ "grab_pass_behind", MEDIUM_PASS_SPEED, PI },
			{ NULL, 0.0f, 0.0f }
		}
	},
	{
		LONG_PASS_DIST,
		{
			{ "grab_dive_pass_left", LONG_PASS_SPEED, PI / 2.0f },
			{ "grab_dive_pass_right", LONG_PASS_SPEED, -PI / 2.0f },
			{ "grab_dive_pass_back_left", LONG_PASS_SPEED, 3.0f * PI / 4.0f },
			{ "grab_dive_pass_back_right", LONG_PASS_SPEED, -3.0f * PI / 4.0f },
			{ "grab_dive_pass_behind", LONG_PASS_SPEED, PI },
			{ NULL, 0.0f, 0.0f }
		}
	},
	{
		MAX_PASS_DIST,
		{
			{ NULL, 0.0f, 0.0f }
		}
	}
};

// LINEOUT PASS

static PassAnimations lineout_animations[] =
{
	{
		1.0f,
		{
			{ "lineout_pass_top_left", SHORT_PASS_SPEED, PI / 2.0f },
			{ "lineout_pass_top_right", SHORT_PASS_SPEED, -PI / 2.0f },
			{ NULL, 0.0f, 0.0f }
		}
	},
	{
		MEDIUM_PASS_DIST,
		{
			{ "lineout_pass_top_left", MEDIUM_PASS_SPEED, PI / 2.0f },
			{ "lineout_pass_top_right", MEDIUM_PASS_SPEED, -PI / 2.0f },
			{ NULL, 0.0f, 0.0f }
		}
	},
	{
		MAX_PASS_DIST,
		{
			{ NULL, 0.0f, 0.0f }
		}
	}
};

// SLAPDOWN PASS

static PassAnimations slapdown_animations[] =
{
	{
		1.0f,
		{
			{ "lineout_slapdown", SHORT_PASS_SPEED, PI / 2.0f },
			{ "lineout_slapdown", SHORT_PASS_SPEED, -PI / 2.0f },
			{ NULL, 0.0f, 0.0f }
		}
	},
	{
		MEDIUM_PASS_DIST,
		{
			{ "lineout_slapdown", SHORT_PASS_SPEED, PI / 2.0f },
			{ "lineout_slapdown", SHORT_PASS_SPEED, -PI / 2.0f },
			{ NULL, 0.0f, 0.0f }
		}
	},
	{
		MAX_PASS_DIST,
		{
			{ NULL, 0.0f, 0.0f }
		}
	}
};

MABRUNTIMETYPE_IMP1(RUActionPass, RUAction);

bool RUActionPass::IsAPassInclusionRole( MabTypeID type )
{
	MABUNUSED(type);
	return true;
	// TODO
	/*
	bool is_pass_inclusion_role = false;
	for( int r = 0; r < n_pass_inclusion_types && !is_pass_inclusion_role; r++ ) {
		is_pass_inclusion_role = type == pass_inclusion_types[r];
	}
	return is_pass_inclusion_role;
	*/
}

bool RUActionPass::IsAnOffload( ARugbyCharacter* player )
{
	RUActionManager* action_mgr = player->GetActionManager();
	if ( action_mgr->IsActionRunning( ACTION_TACKLEE ) )
	{
		RUActionTacklee* tacklee_action = action_mgr->GetAction<RUActionTacklee>();
		return tacklee_action->HasContactedAtLeastOnceInChain();
	}

	return false;
}

RUActionPass::RUActionPass( ARugbyCharacter* player )
: RUAction( player )
, params()
, state( PASS_INITIAL )
, direction( 0 )
, chosen_base_anim( NULL )
, chosen_actual_anim()
, game_time( NULL )
, ball_pickup_time( 0.0f )
, ball_release_time( 0.0f )
, anim_finish_time( 0.0f )
, rotated_ball_joint( 0.0f, 0.0f, 0.0f )
, hit_magnitude( 0.0f )
, pass_quality( 0.0f )
, rejoin_action_timer()
, dummied_defender( NULL )
, dummied_pass_to_player( NULL )
, dummy_penalty_time( 0.0f )
, aborted( false )
{
}

RUActionPass::~RUActionPass()
{
#ifdef ENABLE_GAME_DEBUG_MENU
	SIF_DEBUG_DRAW(RemoveBox(DEBUG_PASS_MARKER_KEY));
#endif
}

void RUActionPass::InitialisePassType( PASS_TYPE type )
{
	MABASSERT(type != PT_LAST);
	MABASSERT(type != PT_UNKNOWN);
	// Initialise the pass type - will set to offload if applicable
	if( type == PT_STANDARD && IsAnOffload( m_pPlayer ) )
	{
		params.type = PT_OFFLOAD;
	}
	else
	{
		params.type = type;
	}
}

void RUActionPass::Enter( ARugbyCharacter* target_player, PASS_TYPE type, RLROLE_INTERCEPT_TYPE intercept_override )
{
	MABASSERT(type != PT_LAST);
	MABASSERT(type != PT_UNKNOWN);
	MABASSERTMSG(m_pPlayer != target_player, "Why are you trying to pass the ball to yourself?");
	InitialisePassType( type );
	params.target_player = target_player;
	params.target_offset = target_player->GetMovement()->GetCurrentPosition() - m_pPlayer->GetMovement()->GetCurrentPosition();
	params.intercept_override_type = intercept_override;
	rejoin_action_timer.SetEnabled(false);
	pass_quality = 0.0f;
	direction = (int) MabMath::Sign( params.target_offset.x );

	game_time = m_pGame->GetSimTime();

#ifdef ENABLE_OSD
	RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
	MabString activation_string( 256, "PASS: %-10s pid=%2d type=%s", "Player",
		target_player != NULL ? target_player->GetAttributes()->GetIndex() : -1,
		PASS_TYPE_STRINGS[params.type]
		);
	settings->PushDebugString( game, RUGameDebugSettings::DP_PASS, activation_string.c_str() );
	MABLOGDEBUG( activation_string.c_str() );
#endif

	/// We prevent weird looking contact type passes from happening here
	const static float CLOSE_DISTANCE = 1.5f;
	bool in_tackle_and_close = false;
	if ( m_pPlayer->GetActionManager()->IsActionRunning( ACTION_TACKLEE ) )
	{
		RUActionTacklee* tacklee_action = m_pPlayer->GetActionManager()->GetAction<RUActionTacklee>();
		if ( tacklee_action->GetTackleResult().tacklers[0] )
		{
			float dist_to_tackler = SSMath::GetXZPointToPointDistance( tacklee_action->GetTackleResult().tacklers[0]->GetMovement()->GetCurrentPosition(), m_pPlayer->GetMovement()->GetCurrentPosition() );
			in_tackle_and_close = dist_to_tackler < CLOSE_DISTANCE;
		}
	}

	if ( params.type == PT_OFFLOAD || in_tackle_and_close )
	{
		if ( !IsOffloadAllowed() )
		{
			//SETDEBUGLINE( 34873487, player->GetMovement()->GetCurrentPosition(), target_player->GetMovement()->GetCurrentPosition(), MabColour::Red, in_tackle_and_close ? MabColour::Red : MabColour::Orange );
			return;
		}
	}

	//SETDEBUGLINE( 34873487, player->GetMovement()->GetCurrentPosition(), target_player->GetMovement()->GetCurrentPosition(), MabColour::Blue, MabColour::Green );
	StartPassAnticipation();

	RUAction::Enter();
}

void RUActionPass::Enter( const FVector &target_position, PASS_TYPE type )
{
#ifdef ENABLE_OSD
	RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
	MabString activation_string( 256, "PASS: %-10s pos=%.1f,%.1f type=%s", "Position",
		target_position.x, target_position.z,
		PASS_TYPE_STRINGS[params.type]
	);
	settings->PushDebugString( game, RUGameDebugSettings::DP_PASS, activation_string.c_str() );
	MABLOGDEBUG( activation_string.c_str() );
#endif

	MABASSERT(type != PT_LAST);
	MABASSERT(type != PT_UNKNOWN);
	InitialisePassType( type );
	params.target_player = NULL;
	params.target_offset = target_position;
	params.intercept_override_type = RLRIT_RUN_DONT_OVERRIDE;
	direction = (int) MabMath::Sign( params.target_offset.x );

	game_time = m_pGame->GetSimTime();

	if ( params.type == PT_OFFLOAD )
	{
		if ( !IsOffloadAllowed() )
		{
			return;
		}
	}

	RUAction::Enter();
}

void RUActionPass::CalculateDummyPassResult()
{
	// Initialise dummied variables
	dummied_pass_to_player = NULL;
	dummied_defender = NULL;
	dummy_penalty_time = 0.0f;

	int dummy_pass_direction = 0;
	#define OFFSET_FROM_DIRECTION( player, direction ) FVector( (float) direction * 5.0f, 0.0f, float(player->GetAttributes()->GetPlayDirection()) * -2.0f	)
	/// If there is no opposition team then just dummy in a random direction
	RUTeam* opp_team = m_pPlayer->GetAttributes()->GetOppositionTeam();
	if ( opp_team == NULL || opp_team->GetPlayers().empty() )
	{
		dummy_pass_direction = m_pGame->GetRNG()->RAND_RANGED_CALL(float, 1.0f ) < 0.5f ? -1 : 1;
		FVector pass_offset = OFFSET_FROM_DIRECTION( m_pPlayer, dummy_pass_direction );
		Enter( pass_offset, PT_DUMMY );
		return;
	}

	/// Find the best player to pass to in both directions
	const int LEFT_DIR  = +1;
	const int RIGHT_DIR = -1;
	TArray<ARugbyCharacter*> receivers_right, receivers_left;
	int pass_inclusion_role_count;
	CalculateReceiversInPassDirection( m_pPlayer, RIGHT_DIR, receivers_right, pass_inclusion_role_count );
	CalculateReceiversInPassDirection( m_pPlayer, LEFT_DIR,  receivers_left,  pass_inclusion_role_count  );

	/// Find the player who is most likely to intercept us first - this is the dummied player
	FVector intercept_target =  m_pGame->GetStrategyHelper()->GetPlayerInterceptTarget( m_pPlayer );

	OPP_INTERCEPT_RESULT intercept_result = { NULL, FVector::ZeroVector, 0.0f, 0.0f };
	OPP_INTERCEPT_RESULT_LIST intercept_results;
	OPP_INTERCEPT_PARAMS intercept_params;
	const static float MIN_INTERCEPT_SPEED = 2.5f;
	const static float MIN_PLAYER_SPEED = 2.5f;
	intercept_params.min_intercept_speed	= MIN_INTERCEPT_SPEED;
	intercept_params.min_player_speed		= MIN_PLAYER_SPEED;

	RLPResultList result_list;
	RLP_FILTERPARAMETERS filter_params;
	filter_params.filters = RLP_FILTER_TEAM;
	filter_params.team = m_pPlayer->GetAttributes()->GetOppositionTeam();

	m_pGame->GetPlayerFilters()->GetPlayerPlayerDistanceSort()->SetReferencePlayer( m_pPlayer );
	m_pGame->GetFilteredPlayerList( result_list, filter_params, m_pGame->GetPlayerFilters()->GetPlayerPlayerDistanceSort() );

	m_pGame->GetStrategyHelper()->FindOppositionInterceptPoint( m_pPlayer, result_list, intercept_target, intercept_result, &intercept_params, &intercept_results );

	float dummied_intercept_time = 0.0f;
	dummied_defender = NULL;
	if ( !intercept_results.empty() )
	{
		dummied_defender = intercept_results.front().intercepting_player;
		dummied_intercept_time = intercept_results.front().intercept_time;
	} else {
		dummied_defender = m_pGame->GetSpatialHelper()->FindClosestPlayerToBall( &m_pPlayer->GetAttributes()->GetTeam()->GetOppositionTeam()->GetPlayers() );
		dummied_intercept_time = m_pGame->GetSpatialHelper()->GetPlayerToPlayerClosingTime( m_pPlayer, dummied_defender );
	}

	// Now that we have a list of receivers left, right and a player to dummy to - we can work out what pass direction makes the most sense to dummy in
	float defender_x_delta = dummied_defender->GetMovement()->GetCurrentPosition().x - m_pPlayer->GetMovement()->GetCurrentPosition().x;
	int defender_dir = defender_x_delta > 0.0f ? LEFT_DIR : RIGHT_DIR;

	/// The best dummy is to pass to the same side that the opposition is on as they should be committed to running on your outside
	/// as long as their are people to pass to in that direction
	if ( defender_dir == LEFT_DIR && receivers_left.Num() > 0 )
		dummy_pass_direction = LEFT_DIR;
	else if ( defender_dir == RIGHT_DIR && receivers_right.Num() > 0 )
		dummy_pass_direction = RIGHT_DIR;
	else
	{
		/// We have valid receivers on both sides - dummy to receiver that matches our x velocity direction
		if ( receivers_left.Num() > 0 && receivers_right.Num() > 0 )
		{
			dummy_pass_direction = int(FMath::Sign(m_pPlayer->GetMovement()->GetCurrentVelocity().x));

			/// If we're standing still - dummy in field
			if ( dummy_pass_direction == 0 )
				dummy_pass_direction = m_pPlayer->GetMovement()->GetCurrentPosition().x > 0.0f ? RIGHT_DIR : LEFT_DIR;
		}
		else if ( receivers_left.Num() == 0 && receivers_right.Num() == 0 )
		{
			/// No receivers - dummy in field
			dummy_pass_direction = m_pPlayer->GetMovement()->GetCurrentPosition().x > 0.0f ? RIGHT_DIR : LEFT_DIR;
		}
		else
		{
			/// Receivers on  one side of us - dummy to that side
			dummy_pass_direction = receivers_right.Num() > 0 ? RIGHT_DIR : LEFT_DIR;
		}
	}

	/// Now that we have our dummy direction, find out who we were probably dummying to
	if ( dummy_pass_direction == RIGHT_DIR && receivers_right.Num() > 0 )
		dummied_pass_to_player = receivers_right[0];
	else if ( dummy_pass_direction == LEFT_DIR && receivers_left.Num() > 0 )
		dummied_pass_to_player = receivers_left[0];

	/// Work out how good this dummy pass is
	float dummy_quality = 0.0f;

	// Function of:
	//   How close the intercepting player is in terms of time
	//	 If pass recipient is on the same side of the passer as the defender then they have a much higher chance of being fooled
	if ( dummied_pass_to_player )
	{
		float target_x_delta = dummied_pass_to_player->GetMovement()->GetCurrentPosition().x - m_pPlayer->GetMovement()->GetCurrentPosition().x;
		int target_dir = target_x_delta > 0.0f ? LEFT_DIR : RIGHT_DIR;

		bool defender_on_same_side_as_target = defender_dir == target_dir;

		if ( defender_on_same_side_as_target )
		{
			/// Work out the dummy quality using a table
			/// Tuple of - min dummy time, max_dummy time, quality at min, quality at max
			const static float DUMMY_QUALITY_TIMES[][4] = { { 0.35f, 0.5f, 0.7f, 1.0f }, { 0.5f, 0.9f, 1.0f, 0.2f } };
			for( size_t i = 0; i < 2; i++ )
			{
				float min_time = DUMMY_QUALITY_TIMES[i][0];
				float max_time = DUMMY_QUALITY_TIMES[i][1];
				float quality_at_min_time = DUMMY_QUALITY_TIMES[i][2];
				float quality_at_max_time = DUMMY_QUALITY_TIMES[i][3];
				if ( MabMath::InRangeInclusive( dummied_intercept_time, min_time, max_time ) )
				{
					float t = (dummied_intercept_time - min_time) / ( max_time - min_time );
					dummy_quality = MabMath::Lerp( quality_at_min_time, quality_at_max_time, t );
					break;
				}
			}
		}

		/// Now work out distraction time based on the dummy quality and the mental agility of the defender
		float defender_mental_agility = dummied_defender->GetAttributes()->GetMentalAgility();
		const static float DEFNDER_MENTAL_AGILITY_MUTLIPLIER_MIN = 1.0f;
		const static float DEFNDER_MENTAL_AGILITY_MUTLIPLIER_MAX = 0.5f;

		/// Set a multiplier based on how good the defender is
		float multiplier = MabMath::Lerp( DEFNDER_MENTAL_AGILITY_MUTLIPLIER_MIN, DEFNDER_MENTAL_AGILITY_MUTLIPLIER_MAX, defender_mental_agility );
		dummy_quality *= multiplier;

		/// If the defender is behind us then we shouldn't be fooled by the dummy
		bool defender_behind_us = ((m_pPlayer->GetMovement()->GetCurrentPosition().z - dummied_defender->GetMovement()->GetCurrentPosition().z) * float(m_pPlayer->GetAttributes()->GetPlayDirection())) > 0.0f;
		if ( defender_behind_us )
			dummy_quality = 0.0f;

		// If we are dummying behind our facing direction then this has a much lower probabiity of success
		float rel_angle_to_dummied_player =
			MabMath::Fabs( MabMath::AngleDelta( m_pPlayer->GetMovement()->GetCurrentFacingAngle(), SSMath::CalcAngleFromPoints( m_pPlayer->GetMovement()->GetCurrentPosition(), dummied_pass_to_player->GetMovement()->GetCurrentPosition() ) ) );
		const static float MAX_PASS_ANGLE_NO_PENALTY = 130.0f;
		const static float ANGLE_PENALTY_MULTIPLIER = 0.25f;
		if ( rel_angle_to_dummied_player > MabMath::Deg2Rad( MAX_PASS_ANGLE_NO_PENALTY ) )
			dummy_quality *= ANGLE_PENALTY_MULTIPLIER;
	}

	const static float MIN_DISTRACT_TIME = 0.50f;
	const static float MAX_DISTRACT_TIME = 1.2f;

	dummy_penalty_time = dummy_quality > 0.0f ? MabMath::Lerp( MIN_DISTRACT_TIME, MAX_DISTRACT_TIME, dummy_quality ) : 0.0f;

	//https://jira.wicked-witch.com.au:8443/browse/RC4-6042
	//In discussion with Cole, we've decided this mechanic has an overall negative affect on player experience
	//so from here on it will be purely visual when used on a human player, and only apply the dummy effect to A.I. controller players.
	//We are also halving the amount of time players suffer the dummy effect if they are on a human controlled team, even if they aren't
	//actively being controlled at the time.
	if (dummied_defender)
	{
		if (dummied_defender->GetHumanPlayer())
		{
			dummy_penalty_time = 0.0f;
		}
		else if (dummied_defender->GetAttributes() &&
			dummied_defender->GetAttributes()->GetTeam() &&
			dummied_defender->GetAttributes()->GetTeam()->GetNumHumanPlayers() > 0)
		{
			dummy_penalty_time /= 2.0f;
		}
	}


	/// Execute the dummy pass
	if ( dummied_pass_to_player )
		Enter( dummied_pass_to_player, PT_DUMMY );
	else {
		FVector pass_offset = OFFSET_FROM_DIRECTION( m_pPlayer, dummy_pass_direction );
		Enter( pass_offset, PT_DUMMY );
	}

#ifdef ENABLE_GAME_DEBUG_MENU
	//#rc3_legacy_debug_draw 
	/*static bool DEBUG_ON = false;
	static bool DEBUG_PAUSE = false;
	if ( DEBUG_ON )
	{
		if ( DEBUG_PAUSE )
		{
			game->GetSimTimeNonConst()->Pause( true );
		}

		int PRIM_IDX = 1399881100;
		if ( dummied_pass_to_player )
		{
			SETDEBUGLINE( PRIM_IDX+1, player->GetMovement()->GetCurrentPosition(), dummied_pass_to_player->GetMovement()->GetCurrentPosition(), MabColour::Cyan, MabColour::Cyan );
		} 
		else 
		{
			SIF_DEBUG_DRAW( Remove3DLine( PRIM_IDX + 1 ) );
		}
		if ( dummied_defender )
		{
			SETDEBUGLINE( PRIM_IDX+2, player->GetMovement()->GetCurrentPosition(), dummied_defender->GetMovement()->GetCurrentPosition(), MabColour::Orange, MabColour::Orange );
		} 
		else 
		{
			SIF_DEBUG_DRAW( Remove3DLine( PRIM_IDX + 2) );

		}
		SETDEBUGTEXTWORLD( PRIM_IDX+3, player->GetMovement()->GetCurrentPosition() + FVector( 0.0f, 2.0f, 0.0f ), MabString( 64, "IT=%0.2f\nDQ=%0.2f\nPT=%0.2f", dummied_intercept_time, dummy_quality, dummy_penalty_time ).c_str() );
	}*/
#endif
}

void RUActionPass::Enter( int pass_direction, int players_to_skip, PASS_TYPE pass_type, RLROLE_INTERCEPT_TYPE intercept_override )
{
	MABASSERT(pass_type != PT_LAST);
	MABASSERT(pass_type != PT_UNKNOWN);

	/// calculate which player is available on our team to be passed to
	InitialisePassType( pass_type );

	/// If we've been requested to pass to the play maker then do so
	if ( players_to_skip == RUPASS_SKIP_PLAYMAKER )
	{
		bool passed = PassToPlayMaker();
		if ( !passed ) Exit();
		return;
	}

	/// For dummy passers - we need to be smart about who we "select" to dummy to
	if ( pass_type == PT_DUMMY )
	{
		CalculateDummyPassResult();
		return;
	}

	direction = pass_direction;

	int n_pass_inclusion_roles;
	TArray<ARugbyCharacter*> receivers;
	

	//Remove the send runner players if doing a normal pass from the ruck
	if (intercept_override != RLROLE_INTERCEPT_TYPE::RLRIT_FROM_SEND_RUNNER && pass_type == PASS_TYPE::PT_DUMMYHALF)
	{
		CalculateReceiversInPassDirection(m_pPlayer, pass_direction, receivers, n_pass_inclusion_roles, false, NULL, true, true);
	}
	else
	{
		CalculateReceiversInPassDirection(m_pPlayer, pass_direction, receivers, n_pass_inclusion_roles);
	}

#ifdef ENABLE_OSD
	{
		RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
		MabString activation_string( 256, "PASS: %-10s dir=%d skip=%d type=%s", "Direction",
			pass_direction,
			players_to_skip,
			PASS_TYPE_STRINGS[params.type]
		);
		settings->PushDebugString( game, RUGameDebugSettings::DP_PASS, activation_string.c_str() );
		MABLOGDEBUG( activation_string.c_str() );
	}
#endif

	ARugbyCharacter* chosen_player = NULL;

	/// If we are not requesting to skip any players (a short pass) and there are pass inclusion roles, then
	/// we really want it to go to the pass inclusion role
	if ( players_to_skip == 0 && n_pass_inclusion_roles > 0 )
		players_to_skip = RUPASS_SKIP_AUTOSELECT;

	if ( players_to_skip >= 0 ) // Skip players specified
		chosen_player = GetReceiverFromList( receivers, params.type == PT_OFFLOAD ? 0 : players_to_skip );
	else
		chosen_player = GetBestReceiverFromList( m_pPlayer, receivers, players_to_skip );

	/// Offloads cannot go too far
	if ( chosen_player != NULL && params.type == PT_OFFLOAD && m_pGame->GetSpatialHelper()->GetPlayerToPlayerDistance( chosen_player, m_pPlayer ) > MAX_OFFLOAD_DISTANCE )
		chosen_player = NULL;

	/// Special case - if the pass is intended to be a long pass out wide and there is no-one there then
	// we try and find the best player from the list that is close to a long pass distance
	RUPlayerMovement *mov_chosen_player = (chosen_player!=NULL) ? chosen_player->GetMovement() : NULL;
	RUPlayerMovement *mov_player        = m_pPlayer->GetMovement();

	if ( chosen_player == NULL && players_to_skip == RUPASS_SKIP_AUTOSELECT_WIDE )
	{
		chosen_player = GetBestReceiverFromList( m_pPlayer, receivers, RUPASS_SKIP_AUTOSELECT );
		const float MIN_PASS_X_DIST_FOR_SKIP = 4.0f;

		if ( chosen_player )
		{
			float x_delta = mov_chosen_player->GetCurrentPosition().x - mov_player->GetCurrentPosition().x;

			MABASSERTMSG( pass_direction == -1 && x_delta <= 0.0f, "Illegal pass, was meant to go right, but was passed left!" );
			MABASSERTMSG( pass_direction == 1 && x_delta >= 0.0f, "Illegal pass, was meant to go left, but was passed right!" );

			if ( MabMath::Fabs( x_delta ) < MIN_PASS_X_DIST_FOR_SKIP )
				chosen_player = NULL;
		}
	}

	/// call the required pass start!
	if ( chosen_player != NULL )
	{
		MABLOGDEBUG("RUActionPass::Enter %d Throw to player(%d)", m_pPlayer->GetAttributes()->GetIndex(), chosen_player->GetAttributes()->GetIndex());
		Enter( chosen_player, params.type, intercept_override );
	}
	else
	{
		MABLOGDEBUG("RUActionPass::Enter %d Throw to location", m_pPlayer->GetAttributes()->GetIndex() );

		// If it fell through from a best selection pass, then clamp to the lower limit
		if ( players_to_skip == RUPASS_SKIP_AUTOSELECT_WIDE )
			players_to_skip = 1;
		else
			MabMath::ClampLower( players_to_skip, 0 );

		// Choose a suitable position to throw to when we can't find anyone
		const static float X_OFFSET_PER_SKIP = 5.0f;
		const static float Z_OFFSET_PER_SKIP = 1.0f;
		FVector offset;
		offset.y = 0.0f;
		offset.x = X_OFFSET_PER_SKIP * pass_direction * (float) (players_to_skip + 1);
		offset.z = Z_OFFSET_PER_SKIP *					(float) (players_to_skip + 1);

		// MabMath::Clamp the x_offset to the largest x_offset
		MabMath::Clamp( offset.x, -MAX_PASS_X_DISTANCE, MAX_PASS_X_DISTANCE );
		MabMath::ClampUpper( offset.z, MAX_PASS_Z_DISTANCE );
		offset.z *= -float(m_pPlayer->GetAttributes()->GetPlayDirection());

		// Clamp it so offloads don't go too far
		float max_pass_dist = params.type == PT_OFFLOAD ? MAX_OFFLOAD_DISTANCE : MAX_PASS_DIST;
		if ( offset.Magnitude() > max_pass_dist )
			offset = offset.Unit() * max_pass_dist;

		/// Provide a bit of randomization on the position
		const static float MAX_DIST_VARIANCE = 0.2f;	// 20% variance up/down
		const static float ANGLE_VARIANCE = 8.0f;	// 8 degree variance up/down
		float dist_multiplier = 1.0f + (m_pGame->GetRNG()->RAND_RANGED_CALL(float, 2.0f ) - 1.0f) * MAX_DIST_VARIANCE;
		float ang_multiplier  = (m_pGame->GetRNG()->RAND_RANGED_CALL(float, 2.0f ) - 1.0f) * ANGLE_VARIANCE;

		MABLOGDEBUG( "Chosen ang multiplier: %0.2f", ang_multiplier );
		FVector receiver;
		MabMatrix::MatrixMultiply(receiver, offset, MabMatrix::RotMatrixY(MabMath::Deg2Rad(ang_multiplier)));
		offset = receiver * dist_multiplier;

		FVector chosen_position = mov_player->GetCurrentPosition() + offset;

		if ( pass_type == PT_OFFLOAD )
		{
			float x_diff = chosen_position.x - mov_player->GetCurrentPosition().x;

			// Ensure that offloads are done in the same direction we said we were going to pass to.
			if ( (pass_direction == -1 && x_diff <= 0.0f) || (pass_direction == 1 && x_diff >= 0.0f) )
			{
				// If we get to here, the ball will be passed in the wrong direction.
				// Eg, left bumper pressed, but no valid passes to the left, so ball is passed right.
				return;
			}
		}

		// Make sure the position is in the field
		FieldExtents extents = m_pGame->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();

		const float DIST_FROM_SIDELINE = 1.0f;
		extents.x /= 2.0f;
		float curr_dist_from_sideline = extents.x - MabMath::Fabs( mov_player->GetCurrentPosition().x );

		if ( curr_dist_from_sideline > DIST_FROM_SIDELINE )
		{
			extents.x -= DIST_FROM_SIDELINE;
			MabMath::Clamp( chosen_position.x, -extents.x, extents.x );
		}
		offset = chosen_position - m_pPlayer->GetMovement()->GetCurrentPosition();

		Enter( offset, params.type );
	}

	// TODO:
	//MABLOGMSG ( LOGCHANNEL_GAME, LOGTYPE_INFO, "RUPassBaseAction::Enter %d (%d players, direction %d)", player->GetIndex(), players_to_skip, pass_direction );
}

void RUActionPass::AnimationEvent(float /*time*/, ERugbyAnimEvent event, size_t /*userdata*/, bool /*bIsBlendingOut = false*/)
{
	// jamesg - this seems to exist only to silence the debug log message
	if ( event == ERugbyAnimEvent::FOOTSTEPS_EVENT || event == ERugbyAnimEvent::IK_TRANSITION_EVENT )
		return;

	wwNETWORK_TRACE_JG("RUActionPass::AnimationEvent: %d event(%s), state(%d), ballholder(%s), aborted(%s)", m_pPlayer->GetAttributes()->GetIndex(), *ENUM_TO_FSTRING(ERugbyAnimEvent, event), state, m_pPlayer == m_pGame->GetGameState()->GetBallHolder() ? TEXT("true") : TEXT("false"), aborted ? TEXT("true") : TEXT("false"));

	if ( aborted )
		return;

	if ( event == ERugbyAnimEvent::BALL_RELEASED_EVENT )
	{
		// Make sure that we pass through the pickup state
		//MABASSERT(params.type == PT_SLAPDOWN);
		//MABASSERT(params.type == PT_LINEOUT);
		if ( m_pGame->GetGameState()->GetBallHolder() == m_pPlayer )
			state = PASS_INAIR;
		else
		{
			// If we no longer have the ball when we receive this event then we should exit
			MABBREAKMSG( "Somehow, passer no longer has ball.  This shouldn't happen - Exiting" );
			Exit();
			return;
		}

		m_pPlayer->GetAnimation()->SetUpperBodyOverride(false);
	}
}

bool RUActionPass::InternalEnter()
{
	MABLOGDEBUG( "RUActionPass::InternalEnter: %d", m_pPlayer->GetAttributes()->GetIndex() );
	MABASSERT( direction != 0 );	// Tyrone - this must be set no matter how the pass enters as IsOffloadAllowed needs it to work!

	// Prevent passing if we're trying to enter too late into a tackle.
	// We could reach this point from button spamming at the end of a tackle.
	if (m_pGame->GetGameState()->GetPhase() == PLAY_THE_BALL)
	{
		aborted = true;
		return true;
	}

	//MABASSERT( params.type == PT_DUMMYHALF || IsLineoutPass() || game->GetGameState()->GetBallHolder() == player );

	//Register animation event
	m_pPlayer->GetMabAnimationEvent().Add( this, &RUActionPass::AnimationEvent );

	MABASSERT( params.target_player == NULL || params.target_player->GetRole() != NULL );
	MABASSERT( params.type > PT_UNKNOWN && params.type < PT_LAST );

	hit_magnitude = 0.0f;
	time_since_release_prevented = -1.0f;	/// < 0 means that we have never been prevented
	current_release_ball_check = true;

	// Prevent any waypointing from occurring
	if ( params.type != PT_DUMMY )
		m_lock_manager.UFLock( UF_SETWAYPOINT );

	m_lock_manager.UFLock( UF_SETFACING );
	m_lock_manager.UFLock( UF_SETLOOK );

	// Reset/Invalidate times - very useful for tracking down bugs
	ball_pickup_time = ball_release_time = anim_finish_time = MabTime();

	state = PASS_INITIAL;

	if ( params.type == PT_DUMMYHALF )
		m_pPlayer->GetLookAt()->LookAtNone();
	else if ( m_pPlayer != m_pGame->GetGameState()->GetBallHolder() )
		m_pPlayer->GetLookAt()->LookAtBall();
	else if ( params.target_player != NULL )
		m_pPlayer->GetLookAt()->LookAtPlayer( params.target_player );
	else
		m_pPlayer->GetLookAt()->LookAtPosition( m_pPlayer->GetMovement()->GetCurrentPosition() + params.target_offset );

	aborted = false;

	return true;
}

void RUActionPass::InformHit( float /*angle*/, float hit_size )
{
	// if we havent started passing, then the pass cant really fail yet.
	MABASSERT( IsRunning() );

	// Hit Size is a rating between 0 and 1 of the strength of the hit
	// Angle is the angle that the hit came in from
	hit_magnitude = hit_size;
}
#pragma optimize("", off)
void RUActionPass::AbortConflictingActions()
{
	for ( int i = 0; i < ACTION_LAST; i++ )
	{
		// Abort all but the tacklee behaviours
		if ( i == ACTION_TACKLEE && !IsLineoutPass() )
			continue;

		if ( i == ACTION_SIDE_STEP )
			continue;

		if ( params.type == PT_DUMMY && RUActionManager::IsRunAction( (RU_ACTION_INDEX) i ) )
			continue;

		if ( i == ACTION_JUMPER_JUMP && IsLineoutPass() )
			continue;

		// Abort all but the tacklee behaviours
		RUAction *action = m_pPlayer->GetActionManager()->GetAction( (RU_ACTION_INDEX) i );

		if ( action && action->IsRunning() )
			action->Exit();
	}
}
#pragma optimize("", on)
void RUActionPass::InternalExit(bool in_destroy)
{
	if(in_destroy)
		return;

	MABLOGDEBUG( "RUActionPass::InternalExit: %d", m_pPlayer->GetAttributes()->GetIndex() );
	MABASSERT( IsRunning() );

	RUPlayerAnimation* player_animation = m_pPlayer->GetAnimation();
	player_animation->SetUpperBodyOverride(false);

	m_pPlayer->GetMabAnimationEvent().Remove( this, &RUActionPass::AnimationEvent );

	#if defined( ENABLE_ROLE_DEBUG_STRINGS )
	if( player->GetRole() )
		//when exiting game this crashes since player role is NULL
		player->GetRole()->SetDebugString( "" );
	#endif

//#ifdef ENABLE_OSD
//	RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
//	MabString exit_string( 256, "Exiting pass on pid %2d", player->GetAttributes()->GetIndex() );
//	settings->PushDebugString( game, RUGameDebugSettings::DP_PASS, exit_string.c_str() );
//#endif

	// if we started an intercept, and we've still got the ball, abort the intercept
	if ( params.type != PT_DUMMY && params.target_player != NULL && state < PASS_INAIR )
		StopInterceptor();

	StopPassAnticipation();

	// Just in case, restore this
	m_lock_manager.UFUnlock( UF_SETWAYPOINT );

#ifdef ENABLE_GAME_DEBUG_MENU
	SIF_DEBUG_DRAW(RemoveBox(DEBUG_PASS_MARKER_KEY));
#endif

#ifdef ENABLE_OSD
	RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
	settings->PushDebugStringCol( game, RUGameDebugSettings::DP_PASS, MabColour::Cyan, "--------------------------------------------" );
#endif
}

void RUActionPass::Update( const MabTimeStep& game_time_step )
{
	MABUNUSED(game_time_step);

	MABASSERTMSG( IsRunning(), "SHOULD NOT HAVE ADVANCE CALLED ON THIS ACTION UPDATER IF ITS NOT ACTIVE! TELL CRAIG" );

	if ( aborted )
	{
		Exit();
		return;
	}

	UpdateBallReleaseChecks( hit_magnitude, game_time_step.delta_time.ToSeconds() );

	switch( state ) {
		case PASS_INITIAL:			if ( UpdateStateInitial() )			break;
		case PASS_WAIT_PICKUP:		if ( UpdateStateWaitPickup() )		break;
		case PASS_INHANDS:			if ( UpdateStateInHands() )			break;
		case PASS_INAIR:			if ( UpdateStateInAir() )			break;
		case PASS_WAIT_ANIMATION:	if ( UpdateStateWaitAnimation() )	break;
		case PASS_WAIT_REJOIN:		if ( UpdateStateWaitRejoin() )		break;
		default:
		break;
	}
}

bool RUActionPass::UpdateStateInitial()
{
	MABLOGDEBUG( "RUActionPass::UpdateStateInitial: %d", m_pPlayer->GetAttributes()->GetIndex() );

	// Work out the likely target of the animation
	FVector target_pos = params.target_player != NULL ? params.target_player->GetMovement()->GetCurrentPosition() : (m_pPlayer->GetMovement()->GetCurrentPosition() + params.target_offset);
	// TODO: we need to do a little more calculation here as the target position of the ball changes once the
	// ball leaves the hands of the passing play and the intercepting player takes control of the ball, animation rotation is 'off'

	// Start the pass animation
	if (!StartPassAnimation( target_pos ))
		return true;

	// TODO: When playing a pro mode game, we should check if this pass is going to the pro player after a request, and if so up the pass Quality to 1.0f

	pass_quality = CalculatePassQuality();

	/// Additional logic for dummy passes
	if ( params.type == PT_DUMMY )
	{
		// tell commentary!
		m_pGame->GetEvents()->commentary_dummy_pass( m_pPlayer );
		m_pGame->GetEvents()->dummy_pass( m_pPlayer );

		// aggression hit
		// RUPORT:
		//player->SetAggression( player->GetAggression() * 0.65f );
		StartDistracted();
		state = PASS_WAIT_ANIMATION;

		return true;
	}

	// Set the player to run in a fixed path for a standard pass
	if ( params.type == PT_STANDARD )
	{
		RUPlayerMovement* mov_player = m_pPlayer->GetMovement();
		FVector target_delta = mov_player->GetTargetPosition() - mov_player->GetCurrentPosition();
		const float PROJECT_DISTANCE = 4.0f;
		FVector new_target = mov_player->GetCurrentPosition() + target_delta.Unit() * PROJECT_DISTANCE;
		// Clamp within the bounds of the field
		FieldExtents fe = m_pGame->GetSpatialHelper()->GetFieldExtents();
		fe.x /= 2.0f; fe.y /= 2.0f;
		const float BOUNDARY_BUFFER = 0.8f;
		fe.x -= BOUNDARY_BUFFER; fe.y -= BOUNDARY_BUFFER;
		MabMath::Clamp( new_target.x, -fe.x, +fe.x );
		MabMath::Clamp( new_target.z, -fe.y, +fe.y );
		wwNETWORK_TRACE_JG("RUActionPass");
		mov_player->SetTargetPosition( new_target );
		mov_player->SetTargetSpeed( mov_player->GetCurrentSpeed() );
	}

#ifdef ENABLE_GAME_DEBUG_MENU
	//#rc3_legacy_debug_camera
	/*
	if ( SIFDebug::GetGameDebugSettings()->GetPassViewDebugEnabled() )
	{
		game->GetSimTimeNonConst()->Pause( true );
		SIFDebug::GetGameDebugSettings()->SetPlayerStateVisible( true );
		SIFDebug::GetGameDebugSettings()->SetLabelDisplayMode( RUGameDebugSettings::DEBUG_LDM_ACTION_RUNNING );
		const float EYE_OFFSET = 5.0f;
		FVector eye = player->GetMovement()->GetCurrentPosition() - (FVector( target_pos.x, 0.0f, target_pos.z ) - player->GetMovement()->GetCurrentPosition()).Unit() * EYE_OFFSET;
		MabColour alpha_white = MabColour::White;
		alpha_white.SetAlpha(0.1f);
		SETDEBUGMARKER( DEBUG_PASS_MARKER_KEY, target_pos, alpha_white );
		eye.y = 2.0f;
		FVector focus = player->GetMovement()->GetCurrentPosition();
		eye.y = 1.5f;
		game->GetCameraManager()->SetCameraFrom(eye, focus, 32.0f, 1.0f, 0.0f);
		//game->GetCamera()->SetEye( eye );
		//game->GetCamera()->SetFocus( focus );
		//SIFDebug::GetGameDebugSettings()->SetFreecamEnabled( true );
	}
	*/
	//game->GetSimTimeNonConst()->Pause( true );
#endif

	state = PASS_WAIT_PICKUP;
	return false;
}

void RUActionPass::DoPickup()
{
	// If this is the pickup frame and we don't have the ball, pickup the ball
	if ( m_pGame->GetGameState()->GetBallHolder() != m_pPlayer )
	{
		const static float ATTACH_TRANS_TIME = 0.13f;
		const static float ATTACH_ROT_TIME = 0.4f;
		m_pGame->GetBall()->SetBallAttachTime( ATTACH_TRANS_TIME, ATTACH_ROT_TIME );

		m_pGame->GetGameState()->SetBallHolder( m_pPlayer, false );

		PickedUpContext picked_up_context = PUC_NONE;
		RUGamePhase current_phase = m_pGame->GetGameState()->GetPhase();

		switch ( current_phase )
		{
			case RUGamePhase::RUCK:
				picked_up_context = PUC_FROM_RUCK;
				break;
			case RUGamePhase::SCRUM:
				picked_up_context = PUC_FROM_SCRUM;
				break;
			case RUGamePhase::MAUL:
				picked_up_context = PUC_FROM_MAUL;
				break;
			default:
				break;
		}

		// Firing this event now will set up the possession change correctly from a ruck
		m_pGame->GetEvents()->ruck_scrum_ball_picked_up( m_pPlayer, picked_up_context );
	}

	state = PASS_INHANDS;

	if ( params.type != PT_DUMMY )
		StartInterceptor();
}

bool RUActionPass::UpdateStateWaitPickup()
{
	MABLOGDEBUG( "RUActionPass::UpdateStateWaitPickup: %d", m_pPlayer->GetAttributes()->GetIndex() );

	MABASSERT( params.type != PT_DUMMY );
	//#if defined( ENABLE_ROLE_DEBUG_STRINGS )
	//player->GetRole()->SetDebugString( MabString( 128, "WAITPICKUP\ncurrsim:%d\npickup :%d\nto go  :%d",
	//									game->GetSimulationTime().ToFrames(), ball_pickup_time.ToFrames(), (ball_pickup_time - game->GetSimulationTime()).ToFrames() ) );
	//#endif

	if ( params.type == PT_DUMMYHALF )
		m_pPlayer->GetLookAt()->LookAtNone();
	else if ( m_pPlayer != m_pGame->GetGameState()->GetBallHolder() )
		m_pPlayer->GetLookAt()->LookAtBall();
	else if ( params.target_player != NULL )
		m_pPlayer->GetLookAt()->LookAtPlayer( params.target_player );
	else
		m_pPlayer->GetLookAt()->LookAtPosition( m_pPlayer->GetMovement()->GetCurrentPosition() + params.target_offset );

	if ( game_time->GetAbsoluteTime() >= ball_pickup_time )
	{
		DoPickup();

		return false;
	}

	return true;
}

bool RUActionPass::UpdateStateInHands()
{
	//MABLOGDEBUG( "RUActionPass::UpdateStateInHands: %d", player->GetAttributes()->GetIndex() );

	MABASSERT( params.type != PT_DUMMY );

	#if defined( ENABLE_ROLE_DEBUG_STRINGS )
	//player->GetRole()->SetDebugString( MabString( 128, "INHANDS\ncurrsim:%d\nrelease:%d\nto go  :%d",
	//	game->GetSimulationTime().ToFrames(), ball_release_time.ToFrames(), (ball_release_time -game->GetSimulationTime()).ToFrames() ) );
	#endif

	// if we still have the ball
	MABASSERT( game_time->GetAbsoluteTime() >= ball_pickup_time );
	MABASSERT( m_pGame->GetGameState()->GetBallHolder() == m_pPlayer );

	if (m_pGame->GetGameState()->GetBallHolder() != m_pPlayer)
	{
		state = PASS_COMPLETE;
		Exit();
		return true;
	}

	//MABASSERTMSG( (ball_release_time - ball_pickup_time).ToFrames() > 0 || params.type == PT_DUMMY, "PTB/Dummy Half and others may break as they check to see if they are holding the ball - must hold for at least one frame" );
	// TODO: at the moment the turn direction is 'shortest distance'
	// this doesnt work when the receiver is crossing the line that determines which way we turn
	// solutions are: 1) select turn direction in advance and stick to it, or 2) only get the angle of target once
	//

	// rotate the passer as best we can so the ball doesnt change direction too sharply when
	// it leaves his hands

	// RUPORT:
	if ( chosen_base_anim->best_angle != NO_ROTATION && !m_pPlayer->GetActionManager()->UFIsLocked( UF_DOMOTION ) ) {
		RUPlayerMovement* movement = m_pPlayer->GetMovement();
		float current_facing_angle	= movement->GetCurrentFacingAngle();
		float expected_pass_angle	= chosen_base_anim->best_angle + current_facing_angle;
		float angle_to_target		= CalculateAngleToTarget();

		float time_left				= (ball_release_time - game_time->GetAbsoluteTime()).ToSeconds();
		float amount_to_turn		= MabMath::AngleDelta( expected_pass_angle, angle_to_target );

		float change_this_update	= amount_to_turn;				// RobH - was getting time_left==0 put temp fix in.
		if(time_left>0.0f)
			change_this_update	= amount_to_turn / time_left;

		// clamp to a maximum turn rate of x degrees per second
		const static float MAX_DEG_CHANGE_PER_SECOND = 360.0f;
		MabMath::Clamp( change_this_update, 0.0f, ( MabMath::Deg2Rad( MAX_DEG_CHANGE_PER_SECOND ) / SIMULATION_RATE ) );

		movement->SetCurrentFacingAngle( current_facing_angle );
		movement->SetTargetFacingAngle( current_facing_angle + change_this_update );
		movement->SetFacingFlags( AFFLAG_FACETARGANGLE );
		// RUPORT:
		//movement->SetFaceMotionTransistionSpeed( player->GetIdealSpeed( RLAS_SPRINT ) + 0.1f );
	}

	/* State transition now handled in the animation event
	if ( game->GetSimulationTime() >= ball_release_time )
	{
		state = PASS_INAIR;
		return false;
	}
	*/

	if ( params.type == PT_DUMMYHALF )
		m_pPlayer->GetLookAt()->LookAtNone();
	else if ( params.target_player != NULL )
		m_pPlayer->GetLookAt()->LookAtPlayer( params.target_player );
	else
		m_pPlayer->GetLookAt()->LookAtPosition( m_pPlayer->GetMovement()->GetCurrentPosition() + params.target_offset );

	return true;
}

bool RUActionPass::WillPassLookUglyOrUnrealsiticOnRelease()
{
	/// Don't allow passes that are directly behind the player unless they are really close
	bool in_tackle = m_pPlayer->GetActionManager()->IsActionRunning( ACTION_TACKLEE );

	if ( in_tackle )
	{
		//RUActionTacklee* tacklee_action = player->GetActionManager()->GetAction<RUActionTacklee>();
		//if ( tacklee_action->HasContactedThisTackle() )
		//
		/// Actually the player can reorient at any time in tackle so apply this always
			const FVector& player_pos = m_pPlayer->GetMovement()->GetCurrentPosition();
			FVector target_pos = params.target_player != NULL ? params.target_player->GetMovement()->GetCurrentPosition() : params.target_offset;
			float pass_angle_deg = MabMath::Rad2Deg( MabMath::Fabs( MabMath::AngleDelta( m_pPlayer->GetMovement()->GetCurrentFacingAngle(), SSMath::CalcAngleFromPoints( player_pos, target_pos ) ) ) );
			float targ_distance = SSMath::GetXZPointToPointDistance( player_pos, target_pos );

			const static float BEHIND_PASS_ANGLE = 145.0f;
			const static float MAX_OFFLOAD_DIST_MULT = 0.5f;
			if ( pass_angle_deg > BEHIND_PASS_ANGLE && targ_distance > MAX_OFFLOAD_DISTANCE * MAX_OFFLOAD_DIST_MULT )
			{
				return true;
			}
		//}
	}

	return false;
}

bool RUActionPass::UpdateStateInAir()
{
	MABASSERT( params.type != PT_DUMMY );

	#if defined( ENABLE_ROLE_DEBUG_STRINGS )
	player->GetRole()->SetDebugString( MabString( 128, "INAIR" ) );
	#endif

	/// Stop ball releases if our arms are locked or the pass will look really ugly or unrealistic

	bool bad_looking_pass = WillPassLookUglyOrUnrealsiticOnRelease();

	bool lineout_passes = params.type != PT_LINEOUT && params.type != PT_SLAPDOWN;

	/// Offload chance time since prevented - 0.0 is very harsh 1.0 - very lenient
	float slider_val = m_pGame->GetGameSettings().game_settings.slider_offload_chance;
	// Nick WWS 7s to Womens 13s //
	//if (m_pGame->GetGameSettings().game_settings.GameModeIsR7())
		//slider_val *= 0.4f;

	if ( !lineout_passes && ((time_since_release_prevented > slider_val && slider_val < PLAYER_PROFILE_OFFLOAD_CHANCE_MAX) || bad_looking_pass) )
	{
		MABLOGDEBUG( "RUActionPass::UpdateStateInAir: %d Tackler prohibits release exiting, blp%s, tsrp=%0.2f", m_pPlayer->GetAttributes()->GetIndex(), bad_looking_pass ? "true" : "false",  time_since_release_prevented );
		StopPassAnticipation();
		StopInterceptor();
		Exit();
		return true;
	}

	MABLOGDEBUG( "RUActionPass::UpdateStateInAir: %d Advance ball is being released", m_pPlayer->GetAttributes()->GetIndex() );

	// add one to offload for this player/team if it is an offload
	bool hit_affects_pass = hit_magnitude > 0.0f && (m_pGame->GetRNG()->RAND_CALL(float) < hit_magnitude);

	RUActionInterceptBall* receiver_intercept = NULL;
	if ( params.target_player != NULL )
		receiver_intercept = params.target_player->GetActionManager()->GetAction<RUActionInterceptBall>();

	/// Work out the quality of the pass
	const static float FAILURE_RATES[][2] =
	{
		{ 0.0f, 0.50f },
		{ 0.5f, 0.30f },
		{ 0.85f, 0.05f },
		{ 1.0f, 0.0f }
	};
	static const int N_FAILURE_RATES = 4;
	float failure_rate = -1.0f;
	for( int i = 0; i < (N_FAILURE_RATES-1); i++ )
	{
		if ( MabMath::InRangeInclusive( pass_quality, FAILURE_RATES[i][0], FAILURE_RATES[i+1][0] ) )
		{
			float t = (pass_quality - FAILURE_RATES[i][0]) / (FAILURE_RATES[i+1][0] - FAILURE_RATES[i][0]);
			failure_rate = MabMath::Lerp( FAILURE_RATES[i][1], FAILURE_RATES[i+1][1], t );

			/// Convert to success rate, scale by gameplay slider and the reconvert back to failure rate
			if ( m_pGame->GetGameSettings().game_settings.game_type != GAME_TRAINING && m_pGame->GetGameSettings().game_settings.game_type != GAME_MENU)
			{
				float success_rate = 1.0f - failure_rate;
				RUGameSettings* game_settings = SIFApplication::GetApplication()->GetMatchGameSettings();

				float offload_success_rate = game_settings->game_settings.slider_offload_success_rate;

				float pass_success_rate = game_settings->game_settings.slider_pass_success_rate;
				// Nick WWS 7s to Womens 13s //
				//if(game_settings->game_settings.GameModeIsR7())
				//	pass_success_rate *= 1.1f;

				success_rate *= (params.type == PT_OFFLOAD ? offload_success_rate : pass_success_rate);
				MabMath::Clamp( success_rate, 0.0f, 1.0f );
				failure_rate = 1.0f - success_rate;
			}
			break;
		}
	}
	MABASSERT( failure_rate != -1.0f );
	bool is_failed_pass = m_pGame->GetRNG()->RAND_RANGED_CALL(float, 1.0f ) < failure_rate;

#ifdef ENABLE_GAME_DEBUG_MENU
	bool PAUSE_ON_FAILIURE_RATE_CALC = false;
	bool DISPLAY_PASS_DETAILS = false;
	if ( PAUSE_ON_FAILIURE_RATE_CALC )
	{
		m_pGame->GetSimTimeNonConst()->Pause( true );
	}
	//#rc3_legacy_debug_Draw 
	/*if ( DISPLAY_PASS_DETAILS )
	{
		SETDEBUGTEXTWORLD( 23728120, player->GetMovement()->GetCurrentPosition(), MabString( 64, "DF=%0.2f, AC=%0.2f\nPQ=%0.2f, FR=%0.2f\nfailed=%s",
			pass_meta.difficulty, player->GetAttributes()->GetPassAccuracy(),
			pass_quality, failure_rate, is_failed_pass ? "yes" : "no" ).c_str()
			);
	}*/
#ifdef ENABLE_OSD
	RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
	MabString pass_success_string( 64, "DF=%0.2f AC=%0.2f PQ=%0.2f FR=%0.2f failed=%s tklhit=%s",
		pass_meta.difficulty, player->GetAttributes()->GetPassAccuracy(),
		pass_quality, failure_rate, is_failed_pass ? "yes" : "no", hit_affects_pass ? "yes" : "no" );
	settings->PushDebugString( game, RUGameDebugSettings::DP_PASS, pass_success_string.c_str() );
#endif

#endif

	MABASSERT( m_pGame->GetGameState()->GetBallHolder() == m_pPlayer );
	if (m_pGame->GetGameState()->GetBallHolder() == m_pPlayer && m_pPlayer != NULL)
	{
		UE_LOG(LogTemp, Error, TEXT("RUActionPass::UpdateStateInAir: current ball holder is %s, this will be cleared on the ball but not the game state."), *m_pPlayer->GetName());
	}
	m_pGame->GetBall()->SetHolder(NULL);

	if ( is_failed_pass )
	{
		MABLOGDEBUG( "RUActionPass::UpdateStateInAir: %d Advance switching to failed pass", m_pPlayer->GetAttributes()->GetIndex() );

		// if we had an interceptor, stop him as this isn't a travel line ball
		StopInterceptor();
		FVector intended_target_offset = params.target_player ? (params.target_player->GetMovement()->GetCurrentPosition() - m_pPlayer->GetMovement()->GetCurrentPosition()) : params.target_offset;

		//SETDEBUGLINE( *********, player->GetMovement()->GetCurrentPosition(), player->GetMovement()->GetCurrentPosition() + intended_target_offset, MabColour::Green, MabColour::Green );
		//if ( params.target_player )
		//{
		//	SETDEBUGLINE( *********, params.target_player->GetMovement()->GetCurrentPosition(), params.target_player->GetMovement()->GetCurrentPosition() + FVector::Y_AXIS * 2.0f, MabColour::Yellow, MabColour::Yellow );
		//}
		//else
		//{
		//	SETDEBUGLINE( *********, player->GetMovement()->GetCurrentPosition() + params.target_offset, player->GetMovement()->GetCurrentPosition() + params.target_offset + FVector::Y_AXIS * 2.0f, MabColour::Yellow, MabColour::Yellow );
		//}

		const static float MAX_DIST_PCT_DOWN = 0.2f;
		const static float MAX_DIST_PCT_UP = 0.1f;
		const static float MAX_ANG_VAR = 25.0f;
		const static float MAX_DIST_ADD = 3.0f;
		float dist_modifier = MabMath::Lerp( 1.0f - MAX_DIST_PCT_DOWN, 1.0f + MAX_DIST_PCT_UP, m_pGame->GetRNG()->RAND_RANGED_CALL(float, 1.0f ) );
		float ang_modifier = MabMath::Lerp( -MAX_ANG_VAR, +MAX_ANG_VAR, m_pGame->GetRNG()->RAND_RANGED_CALL(float, 1.0f ) );

		MabMatrix::MatrixMultiply(intended_target_offset, intended_target_offset, MabMatrix::RotMatrixY(MabMath::Deg2Rad(ang_modifier)));

		FVector dist_add = intended_target_offset * (dist_modifier - 1.0f);
		if ( dist_add.Magnitude() > MAX_DIST_ADD  )
			dist_add = dist_add.Unit() * MAX_DIST_ADD;
		intended_target_offset += dist_add;

		// Limit really deep/bad passes that are a large variance away from play direction as this is frustrating
		const static float MAX_PASS_BACK_ANGLE = 135.0f;
		float pass_angle = SSMath::CalculateAngle( intended_target_offset );
		float play_angle = m_pPlayer->GetAttributes()->GetTeam()->GetPlayAngle();
		float angle_diff = MabMath::AngleDelta( play_angle, pass_angle );
		if ( MabMath::Fabs( angle_diff ) > MabMath::Deg2Rad( MAX_PASS_BACK_ANGLE ) )
		{
			float sign = MabMath::Sign( angle_diff );
			pass_angle = sign * MabMath::Deg2Rad( MAX_PASS_BACK_ANGLE );
			pass_angle += play_angle;
			float mag = intended_target_offset.Magnitude();
			SSMath::AngleToMabVector3( pass_angle, intended_target_offset );
			intended_target_offset *= mag;
		}

		//SETDEBUGLINE( 120348724, player->GetMovement()->GetCurrentPosition(), player->GetMovement()->GetCurrentPosition() + intended_target_offset, MabColour::Red, MabColour::Red );

		FVector target_pos = m_pPlayer->GetMovement()->GetCurrentPosition() + intended_target_offset;

		m_pGame->GetGameState()->Pass( m_pPlayer, params.target_player, target_pos, params.type, false );
		m_pGame->GetBall()->Pass(target_pos);

	}
	else if ( params.target_player != NULL && !hit_affects_pass && receiver_intercept && receiver_intercept->IsRunning() )
	{
		FVector throw_dir = params.target_offset + m_pPlayer->GetMovement()->GetCurrentPosition();

		/// Need to notify the game of a pass
		m_pGame->GetGameState()->Pass( m_pPlayer, params.target_player, throw_dir, params.type, true );

		// notify the receiver that the ball has been released (so he can start the travel line this frame)
		// if the receiver is running a pass anticipation aggregate, then notify that else notify the intercept ball aggregate
		receiver_intercept->NotifyBallPassed();

		MABLOGDEBUG( "RUActionPass::UpdateStateInAir: Setting ball receiver %d", params.target_player->GetAttributes()->GetIndex() );
	}
	else
	{
		MABLOGDEBUG( "RUActionPass::UpdateStateInAir: Advance switching to freeball" );
		// if we had an interceptor, stop him as this isn't a travel line ball
		StopInterceptor();

		// pass in a direction (free ball)
		FVector target_throw_offset = FVector::ZeroVector;
		FVector target_pos = params.target_player != NULL ? params.target_player->GetMovement()->GetCurrentPosition() : params.target_offset + m_pPlayer->GetMovement()->GetCurrentPosition();

		target_throw_offset = target_pos - m_pPlayer->GetMovement()->GetCurrentPosition();
		// Don't make it be thrown forward for the players play direction
		if ( ( target_throw_offset.z * float(m_pPlayer->GetAttributes()->GetPlayDirection()) ) > 0 )
			target_throw_offset.z = 0.0f;

		// Now make any adjustments if the passer has been hit since the pass started, but before
		// the ball is released
		if ( hit_magnitude > 0.0f )
		{
			MABLOGDEBUG( "RUActionPass::UpdateStateInAir: %d Hit on pass with mag %0.2f", m_pPlayer->GetAttributes()->GetIndex(), hit_magnitude );

			// Now perform some adjustments
			// MabMath::Clamp the pass distance to 60% of what it was
			const float DISTANCE_MODIFIER = 0.6f;
			target_throw_offset *= DISTANCE_MODIFIER;

			// MabMath::Clamp the distance to a maxium of the off load distance also
			const float MAX_HIT_PASS_DISTANCE = MAX_OFFLOAD_DISTANCE * 0.7f;
			float pass_mag = target_throw_offset.ApproxMagnitude();

			if ( pass_mag > MAX_HIT_PASS_DISTANCE )
				target_throw_offset = (target_throw_offset.Unit() * MAX_HIT_PASS_DISTANCE);

			// Now alter the direction that the pass goes in based on the magitude of the hit
			const float MAX_DEVIATION = 30.0f; // In Degrees
			const float MIN_DEVIATION = 5.0f;

			MABASSERT( hit_magnitude >= 0.0f && hit_magnitude <= 1.0f );
			float this_deviation_range = MIN_DEVIATION + ( MAX_DEVIATION - MIN_DEVIATION ) * hit_magnitude;

			// Make it between -this_deviation_range and +this_deviation_range
			float this_deviation = m_pGame->GetRNG()->RAND_RANGED_CALL(float, this_deviation_range * 2.0f ) - this_deviation_range;

			// Now rotate the target
			MabMatrix::MatrixMultiply(target_throw_offset, target_throw_offset, MabMatrix::RotMatrixY(MabMath::Deg2Rad(this_deviation)));

			target_pos = m_pPlayer->GetMovement()->GetCurrentPosition() + target_throw_offset;
		}

		m_pGame->GetGameState()->Pass( m_pPlayer, params.target_player, target_pos, params.type, false );
		m_pGame->GetBall()->Pass(target_pos);
	}

	RUPlayerMovement* movement = m_pPlayer->GetMovement();

	if ( params.target_player != NULL ) {
		m_pPlayer->GetLookAt()->LookAtPlayer( params.target_player );
		movement->SetFacingFlags( AFFLAG_FACEACTOR );
		movement->SetFacingActor( params.target_player );
	} else {
		m_pPlayer->GetLookAt()->LookAtBall();

		movement->SetFacingFlags( AFFLAG_FACEBALL );
	}
	movement->SetFaceMotionSpeed( -1.0f );

	state = PASS_WAIT_ANIMATION;
	return true;
}

bool RUActionPass::UpdateStateWaitAnimation()
{
	#if defined( ENABLE_ROLE_DEBUG_STRINGS )
	//player->GetRole()->SetDebugString( MabString( 128, "WAITANIM\ncurrsim:%d\nrelease:%d\nto go  :%d",
	//	game->GetSimulationTime().ToFrames(), anim_finish_time.ToFrames(), (anim_finish_time - game->GetSimulationTime()).ToFrames() ) );
	#endif

	if ( params.target_player != NULL )
		m_pPlayer->GetLookAt()->LookAtPlayer( params.target_player );
	else
		m_pPlayer->GetLookAt()->LookAtBall();

	// The lineout must wait the end of the animation and then, we're done
	if (  params.type == PT_LINEOUT )
	{
		if ( game_time->GetAbsoluteTime() > anim_finish_time )
		{
			state = PASS_COMPLETE;
			Exit();
		}

		return true;
	}
	else // If it is a dummy pass or human controlled, abort now so they can continue running
	{
		// Check if the animation is finished, or we're human then continue
		if ( game_time->GetAbsoluteTime() < anim_finish_time && !m_pPlayer->GetHumanPlayer() )
			return true;

		if (m_pPlayer->GetSetplayManager() &&
			m_pPlayer->GetSetplayManager()->isPlayerRunningSetplay(m_pPlayer))
			return true;

		if ( params.type == PT_DUMMY || m_pPlayer->GetHumanPlayer() )
		{
			Exit();
			state = PASS_COMPLETE;
			return true;
		}
		else
		{
			rejoin_action_timer.Reset( game_time, 0.1f );
		}

		state = PASS_WAIT_REJOIN;
		return true;
	}
}

bool RUActionPass::UpdateStateWaitRejoin()
{
	/* RUPORT
	#if defined( ENABLE_ROLE_DEBUG_STRINGS )
	player->GetRole()->SetDebugString( MabString( 128, "WAITREJOIN" ) );
	#endif
	*/
	MABASSERT( game_time->GetAbsoluteTime() >= anim_finish_time );

	// Face and look at where the ball is
	RUPlayerMovement* movement = m_pPlayer->GetMovement();
	movement->SetFacingFlags( AFFLAG_FACEBALL );
	/* RUPORT:
	movement->SetFaceMotionTransistionSpeed( player->GetIdealSpeed( RLAS_RUN ) - 0.1f );
	*/
	m_pPlayer->GetLookAt()->LookAtBallHolder();
	movement->SetTargetSpeed( 0.0f );

	// We also rejoin the action if their is a ball holder, and they are in front of us (toward opp goal line)
	bool bh_in_front_of_me = m_pGame->GetGameState()->GetBallHolder() && SSMath::AmIBehind( m_pPlayer, m_pGame->GetGameState()->GetBallHolder() );

	if ( bh_in_front_of_me || rejoin_action_timer.GetNumTimerEventsRaised() > 0 )
	{
		state = PASS_COMPLETE;
		Exit();
	}

	return true;
}

void RUActionPass::StartInterceptor()
{
	MABASSERT( params.type != PT_DUMMY );

	if ( params.target_player != NULL && params.target_player->GetActionManager()->CanUseAction( ACTION_INTERCEPT ) )
	{
		// start intercept on the target player
		MABLOGDEBUG( "RUActionPass::StartInterceptor: %d: Starting intercept on %d (upd=%s)", m_pPlayer->GetAttributes()->GetIndex(), params.target_player->GetAttributes()->GetIndex(), params.target_player->GetRole()->RTTGetClassName() );
		MABASSERT( params.target_player->GetRole() );

		/// TYRONE : Crudely assume that we want to kick for touch if we are AI and have been requested to hangback
		// So crude.
		bool kick_for_touch_on_receipt = params.intercept_override_type == RLRIT_HANGBACK &&
											m_pPlayer->GetHumanPlayer() == NULL &&
											(m_pPlayer->GetAttributes()->GetTeam()->GetNumHumanPlayers() == 0 || SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetIsAProMode());

#ifdef FORCE_KICK_FOR_TOUCH
	kick_for_touch_on_receipt = true;
#endif

		params.target_player->GetRole()->StartActionIntercept( m_pPlayer, params.intercept_override_type, pass_quality, kick_for_touch_on_receipt, direction );
	} else if ( params.target_player != NULL ) {
		MABLOGDEBUG( "RUActionPass::StartInterceptor: %d: Attempted starting intercept on %d (upd=%s) Failed!!!!", m_pPlayer->GetAttributes()->GetIndex(), params.target_player->GetAttributes()->GetIndex(), params.target_player->GetRole()->RTTGetClassName() );
	}
}

void RUActionPass::StopInterceptor()
{
	MABASSERT( params.type != PT_DUMMY );
	if ( params.target_player != NULL )
	{
		// start intercept on the target player
		MABLOGDEBUG( "RUActionPass::StopInterceptor: %d: Stopping intercept on %d", m_pPlayer->GetAttributes()->GetIndex(), params.target_player->GetAttributes()->GetIndex() );
		RUAction* action = params.target_player->GetActionManager()->GetAction(ACTION_INTERCEPT);
		MABASSERT( action );
		if ( action->IsRunning() )
			action->Exit();
	}
}

void RUActionPass::StartPassAnticipation()
{
	if ( params.target_player != NULL )
	{
		//:jb let's not have anyone waste time debugging this any more :-(
		//see RUActionPassAnticipation.cpp
		//MABASSERT( params.target_player->GetActionManager()->CanUseAction( ACTION_PASS_ANTICIPATION ) );
	}

	if ( params.target_player != NULL && params.target_player->GetActionManager()->CanUseAction( ACTION_PASS_ANTICIPATION ) && params.intercept_override_type != RLRIT_HANGBACK )
	{
		// start intercept on the target player
		MABLOGDEBUG( "RUActionPass::StartPassAnticipation: %d: Starting anticipation on %d (upd=%s)", m_pPlayer->GetAttributes()->GetIndex(), params.target_player->GetAttributes()->GetIndex(), params.target_player->GetRole()->RTTGetClassName() );
		MABASSERT( params.target_player->GetRole() );

		float pass_side = MabMath::Sign( params.target_player->GetMovement()->GetCurrentPosition().x - m_pPlayer->GetMovement()->GetCurrentPosition().x );
		params.target_player->GetRole()->StartActionPassAnticipation( m_pPlayer, NULL, pass_side, 0, 1, RUActionPassAnticipation::AM_ANTICIPATE );
	}
}

void RUActionPass::StopPassAnticipation()
{
	if ( params.target_player != NULL )
	{
		// start intercept on the target player
		MABLOGDEBUG( "RUActionPass::StopPassAnticipation: %d: Stopping pass anticipation on %d", m_pPlayer->GetAttributes()->GetIndex(), params.target_player->GetAttributes()->GetIndex() );
		RUAction* action = params.target_player->GetActionManager()->GetAction(ACTION_PASS_ANTICIPATION);
		MABASSERT( action );
		if ( action->IsRunning() )
			action->Exit();
	}
}


float RUActionPass::CalculateAngleToTarget()
{
	FVector dir_to_target = params.target_offset;
	if ( params.target_player != NULL ) {
		dir_to_target = params.target_player->GetMovement()->GetCurrentPosition() - m_pPlayer->GetMovement()->GetCurrentPosition();
	}
	return SSMath::CalculateAngle( dir_to_target );
}

float RUActionPass::GetApproxPassDistance()
{
	FVector approx_target = params.target_player != NULL ? params.target_player->GetMovement()->GetCurrentPosition() : ( m_pPlayer->GetMovement()->GetCurrentPosition() + params.target_offset);
	return ( m_pPlayer->GetMovement()->GetCurrentPosition() - approx_target ).ApproxMagnitude();
}

bool RUActionPass::StartPassAnimation( const FVector& approx_target )
{
	MABUNUSED(approx_target);

	MABLOGDEBUG( "RUActionPass::StartPassAnimation %d", m_pPlayer->GetAttributes()->GetIndex() );

	// based on the distance and direction, choose an appropriate animation and play
	float dist_to_target = GetApproxPassDistance();
	MabMath::ClampUpper( dist_to_target, MAX_PASS_DIST );

	RUPlayerAnimation* player_animation = this->GetPlayer()->GetAnimation();

	// get the angle to the target
	float angle_to_target = MabMath::AngleDelta( m_pPlayer->GetMovement()->GetCurrentFacingAngle(), SSMath::CalcAngleFromPoints( m_pPlayer->GetMovement()->GetCurrentPosition(), approx_target ) );

	MABLOGDEBUG( "Facing: %0.2f", m_pPlayer->GetMovement()->GetCurrentFacingAngle() );
	MABLOGDEBUG( "Angle to target: %0.2f", angle_to_target );
	MABLOGDEBUG( "Pass distance: %0.2f", dist_to_target );

	// Select the pass animation list
	PassAnimations* animations = NULL;

	/// Reset the chosen anim
	chosen_base_anim = NULL;

	switch ( params.type )
	{
		case PT_STANDARD:	animations = standard_animations;	break;
		case PT_OFFLOAD:	animations = offload_animations;	break;
		case PT_DUMMY:		animations = dummy_animations;		break;
		case PT_DUMMYHALF:	animations = dummyhalf_animations;	break;
		case PT_LINEOUT:	animations = lineout_animations;	break;
		case PT_SLAPDOWN:	animations = slapdown_animations;	break;
		default:
			MABBREAKMSG( "Bad pass type" );
			animations = standard_animations;
		break;
	}

	// Determine animation to use based on distance
	int animation_index = 0;
	while ( animations[animation_index + 1].distance < dist_to_target )
	{
		animation_index++;
	}

	GetPassMeta( m_pPlayer, params.target_player, params.target_offset, pass_meta );

	// Chose the animation based on angle
	int best_animation = 0;
	float best_angle_to_target_difference = FLT_MAX;
	int i = 0;
	while ( animations[animation_index].animations[i].base_name )
	{
		PassAnimation& animation = animations[animation_index].animations[i];
		float angle_to_target_difference = MabMath::Fabs(MabMath::AngleDelta( angle_to_target, animation.best_angle ));
		if ( angle_to_target_difference < best_angle_to_target_difference )
		{
			best_animation = i;
			best_angle_to_target_difference = angle_to_target_difference;
		}
		++i;
	}

	chosen_base_anim = &animations[animation_index].animations[best_animation];

	MABASSERT( chosen_base_anim != NULL );
	MABASSERT( strlen( chosen_base_anim->base_name ) > 0 );

	MabString anim_request(chosen_base_anim->base_name);

	if ( chosen_base_anim )
	{
		if ( anim_request.length() > 0 )
		{
			// offloads have a few extra params
			if (params.type == PT_OFFLOAD)
			{
				RUActionTacklee* tackle_action = m_pPlayer->GetActionManager()->GetAction<RUActionTacklee>();
				if (tackle_action && tackle_action->IsRunning())
				{
					anim_request = MabString(0, "offload_%s%s%s",
						(pass_meta.offload_meta.falling ? "falling_" : ""),
						(pass_meta.selected_offload_arm == OA_BOTH ? "" : (pass_meta.selected_offload_arm == OA_LEFT ? "leftarm_" : "rightarm_")),
						chosen_base_anim->base_name);
				}
				else
				{
					anim_request = MabString(0, "offload_%s", chosen_base_anim->base_name);
				}

				chosen_offload_anim = anim_request;

				//if (!player_animation->IsAnimationAvailable(anim_request.c_str()))
				//	return false;

				RUPlayerAnimation* anim = m_pPlayer->GetAnimation();

				ERugbyAnim_Mode_UBActionsFBPass fullBodyPassingState = anim->GetStateMachine().GetSMUpperBodyActions()->GetUBActionsFBPassState();
				bool bFullBodyPassing = fullBodyPassingState == ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving ||
					fullBodyPassingState == ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing || fullBodyPassingState == ERugbyAnim_Mode_UBActionsFBPass::medium_pass;
				if (bFullBodyPassing)
				{
					UE_LOG(LogTemp, Display, TEXT("StateMachineMgr::StartPassAnimation: Cant pass PT_OFFLOAD already trying to pass the ball."));
					return false;
				}

				ERugbyAnim_Mode_UBActions CurrentUBActionSMState = anim->GetStateMachine().GetSMUpperBodyActions()->GetUBActionsActionState();
				bool isNodeActive = anim->GetStateMachine().GetSMUpperBodyActions()->IsNodeActive();

				//NOT of NullPassThrough, Catches, dummy_passes
				//for offload_forward, offload_left, offload_behind, offload_right, dummy_pass_forward, dummy_pass_left, dummy_pass_right
				if (!((CurrentUBActionSMState == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
					(CurrentUBActionSMState == ERugbyAnim_Mode_UBActions::Catches) ||
					(CurrentUBActionSMState == ERugbyAnim_Mode_UBActions::dummy_passes))) //#rc3_legacy_animation. Rewritten this to check for statemachine before calling
				{
					UE_LOG(LogTemp, Display, TEXT("StateMachineMgr::StartPassAnimation: Cant pass PT_OFFLOAD as UBActions is not in Null, Catch or dummy_passes State"));
					return false;
				}				

				#ifdef ENABLE_OSD
				RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
				MabString activation_string( 256, "OFFLOAD: anim=%s", anim_request.c_str() );
				settings->PushDebugString( game, RUGameDebugSettings::DP_PASS, activation_string.c_str() );
				#endif

			}			
			else if (params.type == PT_DUMMYHALF)
			{
				RUPlayerAnimation* anim = m_pPlayer->GetAnimation();

				ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = anim->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();

				//for grab_pass_left, grab_pass_right, grab_pass_back_left, grab_pass_back_right, grab_pass_behind, grab_dive_pass_left, grab_dive_pass_right, grab_dive_pass_back_left, grab_dive_pass_back_right, grab_dive_pass_behind
				if (CurrentFullBodySMState != ERugbyAnim_Mode_FullBodyActions::DummyHalf) //#rc3_legacy_animation. Rewritten this to check for statemachine before calling
				{
					UE_LOG(LogTemp, Display, TEXT("StateMachineMgr::StartPassAnimation: Cant pass PT_DUMMYHALF as it's not in Dummy State"));
					return false;
				}
			}
			else if ((params.type == PT_LINEOUT) || (params.type == PT_SLAPDOWN))
			{
				RUPlayerAnimation* anim = m_pPlayer->GetAnimation();

				ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = anim->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();

				//for lineout_pass_top_left, lineout_pass_top_right, lineout_slapdown
				if (CurrentFullBodySMState != ERugbyAnim_Mode_FullBodyActions::Lineout) //#rc3_legacy_animation. Rewritten this to check for statemachine before calling
				{
					UE_LOG(LogTemp, Display, TEXT("StateMachineMgr::StartPassAnimation: Cant pass PT_LINEOUT or PT_SLAPDOWN as it's not in LineOut State"));
					return false;
				}
			}
			else if (params.type == PT_DUMMY)
			{
				RUPlayerAnimation* anim = m_pPlayer->GetAnimation();

				ERugbyAnim_Mode_UBActions CurrentUBActionSMState = anim->GetStateMachine().GetSMUpperBodyActions()->GetUBActionsActionState();

				if (!((CurrentUBActionSMState == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
					(CurrentUBActionSMState == ERugbyAnim_Mode_UBActions::Catches) ||
					(CurrentUBActionSMState == ERugbyAnim_Mode_UBActions::dummy_passes))) //#rc3_legacy_animation. Rewritten this to check for statemachine before calling
				{
					UE_LOG(LogTemp, Display, TEXT("StateMachineMgr::StartPassAnimation: Cant pass PT_DUMMY as UBActions is not in Null, Catch or dummy_passes State"));
					return false;
				}
			}
			else if (params.type == PT_STANDARD)
			{
				RUPlayerAnimation* anim = m_pPlayer->GetAnimation();

				if (anim->GetStateMachine().GetSMUpperBodyActions()->IsMediumPassLongPassNodeActive() == false)
				{
					UE_LOG(LogTemp, Display, TEXT("StateMachineMgr::StartPassAnimation: Cant pass Medium or long as UBActions is not in Null, Catch or dummy_passes State"));
					return false;
				}
			}
			else //this else should not happen
			{
				//if (!player_animation->IsAnimationAvailable(anim_request.c_str()))
				{
					return false;
				}
			}

			#ifdef ENABLE_OSD
			RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
			MabString activation_string( 256, "Starting pass: anim=%s", anim_request.c_str() );
			settings->PushDebugString( game, RUGameDebugSettings::DP_PASS, activation_string.c_str() );
			MABLOGDEBUG( "RUActionPass::StartPassAnimation: %d, Activation(%s)", player->GetAttributes()->GetIndex(), activation_string.c_str() );
			#endif

			player_animation->PlayAnimation( anim_request.c_str() );

			//we dont want this to be set to true if we are playing a fullbody action animation.
			if (m_pPlayer->GetAnimation()->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState() == ERugbyAnim_Mode_FullBodyActions::Null)
			{
				player_animation->SetUpperBodyOverride(true);
			}

			MABLOGDEBUG( "Chosen pass animation: %s", anim_request.c_str() );
		}
	}

	
	if ( !IsLineoutPass() )
	{ // are these really needed?
		RUGameAnimation* game_animation = m_pPlayer->GetGameWorld()->GetAnimation();
		const RUGameAnimation::PICKUP_ANIMATION_DATA* pickup_animation_data = game_animation->FindPickupAnimationData( anim_request.c_str() );

		if (pickup_animation_data)
		{
			MABASSERT( pickup_animation_data );

			ball_pickup_time = MabTime(game_time->GetAbsoluteTime() + pickup_animation_data->contact_time);
			anim_finish_time = MabTime(game_time->GetAbsoluteTime() + pickup_animation_data->anim_duration);
			ball_release_time = MabTime(game_time->GetAbsoluteTime() + pickup_animation_data->release_time);
			MABASSERT(ball_release_time > ball_pickup_time || params.type == PT_DUMMY);
			MABASSERT(anim_finish_time > ball_release_time || params.type == PT_DUMMY);
		}
		else
		{
			MABBREAKMSG("pickup_animation_data is NULL ");
		}
	}
	else
	{
		RUGameAnimation* game_animation = m_pPlayer->GetGameWorld()->GetAnimation();
		const char* anim_name = params.type == PT_LINEOUT ? "jumper_pass_left" : "jumper_slapdown";
		const RUGameAnimation::PICKUP_ANIMATION_DATA* pickup_animation_data = game_animation->FindPickupAnimationData(anim_name);

		if (pickup_animation_data)
		{
			MABASSERT( pickup_animation_data );
			anim_finish_time = MabTime(game_time->GetAbsoluteTime() + pickup_animation_data->anim_duration);
			ball_pickup_time = MabTime(game_time->GetAbsoluteTime() + pickup_animation_data->contact_time);
			ball_release_time = MabTime(game_time->GetAbsoluteTime() + pickup_animation_data->release_time);
		}
		else
		{
			MABBREAKMSG("pickup_animation_data is NULL ");
		}
	}	

	return true;
}

void RUActionPass::CalculateReceiversInPassDirectionNoModify( ARugbyCharacter* passer, int p_pass_direction, TArray<ARugbyCharacter*>& returned_receivers, int& pass_inclusion_role_count )
{
	CalculateReceiversInPassDirection( passer, p_pass_direction, returned_receivers, pass_inclusion_role_count, false, NULL, false );
}

void RUActionPass::CalculateReceiversInPassDirection( ARugbyCharacter* passer, int p_pass_direction, TArray<ARugbyCharacter*>& returned_receivers, int& pass_inclusion_role_count, bool debug_on, SIFDebugDrawPool* debug_key_pool, bool allow_state_modify, bool force_pass_to_backs)
{
	// Update the pass priorities so they are current
	MABASSERT( passer != NULL );
	MABASSERT( passer->GetAttributes()->GetTeam() != NULL );
//	MABASSERT( passer->GetAttributes()->GetTeam()->GetStrategy() != NULL );

	if ( allow_state_modify )
		passer->GetAttributes()->GetTeam()->GetFormationManager()->UpdatePassPriorities( PASS_PRI_ALL, true );

	// Get all of the players in the direction specified then filter out which ones do not apply
	// As we find them - add them to the list

	RUTeam* team			= passer->GetAttributes()->GetTeam();
	FVector passer_pos	= passer->GetMovement()->GetCurrentPosition();
	SIFGameWorld* game		= passer->GetGameWorld();

	// get the id of the player down the line
	RLPResultList			potential_receivers;
	RLP_FILTERPARAMETERS	filter_params;

	pass_inclusion_role_count = 0;

	// Setup the filter to find all players close to the player
	filter_params.filters = RLP_FILTER_X_CUTOFF | RLP_FILTER_Z_CUTOFF | RLP_FILTER_TEAM | RLP_FILTER_MAX_X_DIST | RLP_FILTER_MAX_Z_DIST | RLP_FILTER_EXCLUDE_PLAYER;

	filter_params.team					= team;
	filter_params.x_cutoff_position		= passer_pos.x;
	filter_params.x_cutoff_direction	= (float) -p_pass_direction;
	filter_params.z_cutoff_position		= passer_pos.z;
	filter_params.z_cutoff_direction	= passer->GetAttributes()->GetPlayDirection();
	filter_params.max_x_dist			= MAX_PASS_X_DISTANCE;
	filter_params.max_x_ref				= passer_pos.x;
	filter_params.max_z_dist			= MAX_PASS_Z_DISTANCE;
	filter_params.max_z_ref				= passer_pos.z;
	filter_params.exclude_player		= passer;

	// DEBUG CODE TO SHOW THE SEARCH AREA
	/*{
		FVector box_centre( passer_pos.x + p_pass_direction * MAX_PASS_X_DISTANCE * 0.5f, 1.0f, passer_pos.z - passer->GetAttributes()->GetPlayDirection() * MAX_PASS_Z_DISTANCE * 0.5f );
		FVector box_size( MAX_PASS_X_DISTANCE, 2.0f, MAX_PASS_Z_DISTANCE * 2.0f );

#ifdef BUILD_DEBUG
		SIF_DEBUG_DRAW( SetBox( 749, box_centre, box_size, MabColour( 1.0f, 0.0f, 0.0f, 0.15f ) ) );
#endif
	}*/

	/// Get hold of all of these players
	game->GetFilteredPlayerList( potential_receivers, filter_params, game->GetPlayerFilters()->GetPlayerBallDistanceSort());

	//Remove the forwards if it is being forced to pass to ther backs.
	if (force_pass_to_backs && potential_receivers.size() > 0)
	{
		RLPResultList::iterator iter;

		RLP_RESULT fallback_forward = potential_receivers.at(0);

		for (iter = potential_receivers.begin(); iter != potential_receivers.end(); )
		{
			ARugbyCharacter* current_player = (*iter).player;
			if ((current_player->GetAttributes()->GetPlayerPosition()&PP_BACK) == 0)
			{
				iter = potential_receivers.erase(iter);
			}
			else
			{
				++iter;
			}
		}

		if (potential_receivers.size() == 0)
			potential_receivers.push_back(fallback_forward);
	}


/*#ifdef WITH_EDITOR
	for (int i = 0; i < potential_receivers.size(); i++)
	{
		FVector pos = potential_receivers[i].player->GetMabPosition();
		MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(pos, worldPos);
		DrawDebugSphere(
			potential_receivers[i].player->GetWorld(),
			worldPos,
			25,
			8,
			FColor::Red,
			false,
			2.0f
		);
	}
#endif*/

	// Work out approximately where the good receivers be and find the closest players to each
	// of those positions that have not already been found

	static const float STANDARD_PASS_ANGLE = MabMath::Deg2Rad( 10.0f );

	// This is a vector which represents passing at the specified angle

	float standard_pass_dist = passer->GetAttributes()->GetTeam()->GetStrategy().GetPassSpacing();
	FVector pass_offset_vec;
	MabMatrix::MatrixMultiply(pass_offset_vec, FVector(((float)p_pass_direction) * standard_pass_dist, 0.0f, 0.0f), MabMatrix::RotMatrixY(STANDARD_PASS_ANGLE * MabMath::Sign((float)p_pass_direction) * MabMath::Sign((float)passer->GetAttributes()->GetPlayDirection())));
	//#define BASE 8283541
	//SETDEBUGLINE( BASE + 0, passer->GetMovement()->GetCurrentPosition() + pass_offset_vec * 0,  passer->GetMovement()->GetCurrentPosition() + pass_offset_vec * 1, MabColour::Black, MabColour::Black );
	//SETDEBUGLINE( BASE + 1, passer->GetMovement()->GetCurrentPosition() + pass_offset_vec * 1,  passer->GetMovement()->GetCurrentPosition() + pass_offset_vec * 2, MabColour::Yellow, MabColour::Yellow );
	//SETDEBUGLINE( BASE + 2, passer->GetMovement()->GetCurrentPosition() + pass_offset_vec * 2,  passer->GetMovement()->GetCurrentPosition() + pass_offset_vec * 3, MabColour::Black, MabColour::Black );
	//SETDEBUGLINE( BASE + 3, passer->GetMovement()->GetCurrentPosition() + pass_offset_vec * 3,  passer->GetMovement()->GetCurrentPosition() + pass_offset_vec * 4, MabColour::Yellow, MabColour::Yellow );

	FVector expect_pass_receiver_pos;
	FVector last_receiver_pos = passer_pos;
	//RUBlackBoard& blackboard = passer->GetAttributes()->GetTeam()->GetBlackBoard();

	/// Find the MAX_PASS_PLAYERS most appropriate players
	for( int i = 0; i < MAX_PASS_PLAYERS; i++ )
	{
		// Generate expect pass position
		expect_pass_receiver_pos = passer_pos + pass_offset_vec * ((float)(i+1));

		// Find the closest player to this position
		game->GetPlayerFilters()->GetPlayerDistanceSort()->SetReferencePoint( expect_pass_receiver_pos );
		//#define BASE_DEBUG_LINE 750
		//SETDEBUGMARKER( BASE_DEBUG_LINE + i, expect_pass_receiver_pos + FVector( 0.0f, 0.5f, 0.0f ), MabColour::Gray( (float) (i + 1) / (float) MAX_PASS_PLAYERS ) );

		potential_receivers.sorter = game->GetPlayerFilters()->GetPlayerDistanceSort();
		potential_receivers.sorter->init();
		std::sort( potential_receivers.begin(), potential_receivers.end() );

		RLPResultList::iterator iter, best_iter;
		float best_pass_priority = 10000.0f;
		ARugbyCharacter* best_pass_player = nullptr;
		float MIN_DIST_FROM_PASS = standard_pass_dist;
		int receiver_count = 0;

		// Get the closest player on the pass direction side of the last player used
		for( iter = potential_receivers.begin(); iter != potential_receivers.end(); ++iter, ++receiver_count )
		{
			ARugbyCharacter* current_player = static_cast<ARugbyCharacter*>(iter->player);
			if ( // Look at either the closest player or anyone inside the prescribed x distance
				(receiver_count == 0 || MabMath::Fabs(current_player->GetMovement()->GetCurrentPosition().x - expect_pass_receiver_pos.x) < MIN_DIST_FROM_PASS)
				&& current_player->GetActionManager()->CanUseAction( ACTION_INTERCEPT )
				&& game->GetStrategyHelper()->CanPassBallBetween( passer, current_player )
				)
			{
				float pass_priority;
				//const MabVariant* nv_pass_priority = blackboard.GetEntry( RUBB_ATTRIB_PASS_PRIORITY, current_player );
				//MABASSERT( nv_pass_priority != NULL );
				//if (!nv_pass_priority)
				//	continue;
				//pass_priority = nv_pass_priority->ToInt();

				/// Work out a new pass priority on proximity to angle and distance from
				FVector delta_vec_player = (current_player->GetMovement()->GetCurrentPosition() - passer->GetMovement()->GetCurrentPosition());
				FVector delta_vec_expected = (current_player->GetMovement()->GetCurrentPosition() - expect_pass_receiver_pos);
				float dist_away = delta_vec_expected.Magnitude();
				float angle_offset = MabMath::Rad2Deg( MabMath::ACos( pass_offset_vec.Unit().Dot( delta_vec_player.Unit() ) ) );
				const static float ANGLE_OFFSET_INFLUENCE = 0.1f;
				const static float START_ANGLE_OFFSET = 45.0f;
				const static float DIST_OFFSET_MULT = 1.0f;
				pass_priority = DIST_OFFSET_MULT * dist_away;
				if ( angle_offset > START_ANGLE_OFFSET)
					pass_priority += ANGLE_OFFSET_INFLUENCE * angle_offset;

//#ifdef WITH_EDITOR
//						FVector playerPos = current_player->GetMabPosition();
//						MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(playerPos, playerWorldPos);
//						MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(expect_pass_receiver_pos, world_expect_pass_receiver_pos);
//						DrawDebugLine(
//							potential_receivers[i].player->GetWorld(),
//							playerWorldPos,
//							world_expect_pass_receiver_pos,
//							FColor::Red,
//							false,
//							2.0f,
//							10
//						);
//#endif

				if ( pass_priority < best_pass_priority )
				{
					best_pass_priority = pass_priority;
					best_pass_player = current_player;
					best_iter = iter;
					last_receiver_pos = current_player->GetMovement()->GetCurrentPosition();
				}
			}
		}

		// If we did find a valid receiver then add it to the list
		if ( best_pass_player != nullptr )
		{
			returned_receivers.Add( best_pass_player );

			// Remove all potential receivers on the x inside of this player
			float best_player_x_offset = MabMath::Fabs( best_pass_player->GetMovement()->GetCurrentPosition().x - passer_pos.x );
			for( iter = potential_receivers.begin(); iter != potential_receivers.end();  )
			{
				ARugbyCharacter* current_player = (*iter).player;
				float current_player_x_offset = MabMath::Fabs( current_player->GetMovement()->GetCurrentPosition().x - passer_pos.x);

				if ( current_player_x_offset <= best_player_x_offset )
					iter = potential_receivers.erase( iter );
				else
					++iter;
			}
		}
	}

	/// Final sort of receivers based on offset
	ReceiverSorter sorter;
	sorter.pass_x_ref = passer->GetMovement()->GetCurrentPosition().x;
	Algo::Sort(returned_receivers, sorter);

	MABUNUSED( debug_on );
	MABUNUSED( debug_key_pool );
#ifdef ENABLE_GAME_DEBUG_MENU
	//#rc3_legacy_debug_draw 
	/*if ( debug_on )
	{
		static MabColour RECEIVER_ORDER_COLOURS[] = { MabColour::Red, MabColour::Green, MabColour::Blue, MabColour::Yellow };
		SIFRugbyCharacterList::iterator it;
		int idx = 0;
		FVector player_pos = passer->GetMovement()->GetCurrentPosition();
		for( it = returned_receivers.begin(); it != returned_receivers.end(); ++it, idx++ )
		{
			ARugbyCharacter* receiver = *it;
			FVector receiver_pos = receiver->GetMovement()->GetCurrentPosition();
			MabColour col2 = RECEIVER_ORDER_COLOURS[ idx ];
			FVector offset = p_pass_direction == -1 ? FVector::ZeroVector : FVector::Y_AXIS * 1.0f;
			SETDEBUGLINE( 1000 + idx/ *debug_key_pool->AllocateKey()* /, player_pos + offset, receiver_pos + offset, col2, col2 );
			SIF_DEBUG_DRAW( SetText( 2000 + idx/ *bug_key_pool->AllocateKey()* /, (player_pos + receiver_pos) * 0.5f + offset, MabString( 8, "%d", idx + 1 ).c_str(), col2 ) );
		}

		static MabColour DEFAULT_PASS_POS_COLOURS[] = { MabColour::Black, MabColour::White, MabColour::Black, MabColour::White };
		for( int i = 0; i < MAX_PASS_PLAYERS; i++ )
		{
			 SETDEBUGLINE( 3000 + idx + i/ *debug_key_pool->AllocateKey()* /, passer_pos + pass_offset_vec * (float)(i), passer_pos + pass_offset_vec * (float)(i+1), DEFAULT_PASS_POS_COLOURS[i], DEFAULT_PASS_POS_COLOURS[i] );
		}
	}*/
#endif

}

bool RUActionPass::ReceiverSorter::operator()(const ARugbyCharacter* a, const ARugbyCharacter* b) const
{
	const float a_x_offset = FMath::Abs(a->GetMovement()->GetCurrentPosition().x - pass_x_ref);
	const float b_x_offset = FMath::Abs(b->GetMovement()->GetCurrentPosition().x - pass_x_ref);

	return a_x_offset < b_x_offset;
}

void RUActionPass::CalculateBallJointPos( FVector& relative_joint, float target_angle_radians )
{
	// rotate by our current rotation
	MabMatrix rot_matrix = MabMatrix::RotMatrixY( target_angle_radians );
	MabMatrix::MatrixMultiply( rotated_ball_joint, relative_joint, rot_matrix );
}

/// For the current action, say whether or not the given action is allowed to run
bool RUActionPass::CanEnterOtherAction( RU_ACTION_INDEX id )
{
	/// If a pass is not running then allow any action to start
	if ( !IsRunning() )
		return true;

	/// Can't do a sidestep or fend or try while passing
	if ( id == ACTION_SIDE_STEP || id == ACTION_FEND || id == ACTION_TRY )
		return false;

	/// Cannot try and pickup the ball during a running pass
	if ( RUActionManager::IsFreeBallAction( id ) )
		return false;

	// Can't start a new run action while we are passing
	if ( RUActionManager::IsRunAction( id ) )
		return true;

	// Cannot start another pass while we are already passing
	if ( id == ACTION_PASS )
		return false;

	/// Only allowed to start a tackle under certain circumstances
	if ( id == ACTION_TACKLEE && !CanBeTackled() )
		return false;

	/// Can't start a kick while passing
	if ( id == ACTION_KICK )
		return false;

	return true;
}

bool RUActionPass::CanBeTackled()
{
	return (params.type == PT_DUMMY) || (state < PASS_INAIR);
}

float RUActionPass::GetTimeTillRelease()
{
	MABBREAKMSG( "Do we need this method anymore? ball_release_time is not set now until anim event fires so may be inaccurate" );
	float time = (ball_release_time - game_time->GetAbsoluteTime()).ToSeconds();
	MabMath::ClampLower( time, 0.0f );
	return time;
}

void RUActionPass::StartDistracted()
{
	// If there is no oppositio team (e.g we're in a tutorial then don't worry
	if ( m_pPlayer->GetGameWorld()->GetTeams().size() == 1 )
		return;

	if ( dummy_penalty_time > 0.0f && dummied_defender != NULL )
		dummied_defender->GetRole()->StartActionDistracted( params.target_player, dummy_penalty_time );
}

ARugbyCharacter* RUActionPass::GetReceiverFromList( const TArray<ARugbyCharacter*>& player_list, int index )
{
	if (index >= player_list.Num())
		return nullptr;

	return player_list[index];
}

ARugbyCharacter* RUActionPass::GetBestReceiverFromList( ARugbyCharacter* passer, const TArray<ARugbyCharacter*>& receivers, int selection_type )
{
	// Get the player with the highest pass priority (this is the best option)
	MABASSERT( selection_type <= RUPASS_SKIP_AUTOSELECT );
	MABASSERT( passer != NULL );
	RUBlackBoard& blackboard = passer->GetAttributes()->GetTeam()->GetBlackBoard();
	int best_pass_priority = -1000;
	ARugbyCharacter* chosen_player = nullptr;

	for ( int i = 0; i < receivers.Num(); ++i )
	{
		ARugbyCharacter* receiver = receivers[i];

		// Only select wider out players for the wide skip
		if ( i == 0 && selection_type == RUPASS_SKIP_AUTOSELECT_WIDE )
			continue;

		if ( receiver == nullptr )
			continue;

		const MabVariant* nv_pass_priority = blackboard.GetEntry( RUBB_ATTRIB_PASS_PRIORITY, receiver );
		MABASSERT( nv_pass_priority != nullptr );
		const int pass_priority = nv_pass_priority->ToInt();
		if ( chosen_player == nullptr || pass_priority > best_pass_priority )
		{
			best_pass_priority = pass_priority;
			chosen_player = receiver;
		}
	}

	return chosen_player;
}

ARugbyCharacter* RUActionPass::GetBestReceiverInDir(FVector& passer_pos, SIFRugbyCharacterList& receivers, int pass_dir, int ideal_pass_dist)
{
	auto FindBestBackwardReceiver = [&](int checkDir) -> ARugbyCharacter*
	{
		ARugbyCharacter* bestReceiver = nullptr;
		float bestScore = -FLT_MAX;

		for (ARugbyCharacter* receiver : receivers)
		{
			if (!receiver) continue;

			FVector receiver_pos = receiver->GetMovement()->GetCurrentPosition();
			float dx = receiver_pos.X - passer_pos.X;
			float teamDir = static_cast<float>(receiver->GetAttributes()->GetPlayDirection());

			// Only consider receivers who are *behind* the passer in the direction of play
			if (dx * teamDir <= 0)
			{
				// Require that receiver is also in the intended dir of the pass
				if (dx * checkDir * teamDir <= 0) 
					continue;

				float distance = FVector::Dist2D(receiver_pos, passer_pos);
				float score = -FMath::Abs(distance - ideal_pass_dist);

				// We dont want the pass to go to the passer or anyone too close
				if (distance < STANDARD_PASS_DIST)
					continue;

				if (score > bestScore)
				{
					bestScore = score;
					bestReceiver = receiver;
				}
			}
		}
		return bestReceiver;
	};

	ARugbyCharacter* bestReceiver = FindBestBackwardReceiver(pass_dir);
	if (bestReceiver) return bestReceiver;

	// Try the other direction as fallback
	return FindBestBackwardReceiver(-pass_dir);
}

struct PassPriorityGreater
{
	PassPriorityGreater( RUBlackBoard* bb ) : bb( bb ) {}

	bool operator()( ARugbyCharacter*& left, ARugbyCharacter*& right )
	{
		MabVariant* left_pri  = bb->GetEntry( RUBB_ATTRIB_PASS_PRIORITY, left );
		MabVariant* right_pri = bb->GetEntry( RUBB_ATTRIB_PASS_PRIORITY, right );

		if ( !left_pri )
			return true;

		if ( !right_pri )
			return false;

		return left_pri->ToInt() > right_pri->ToInt();
	}

	RUBlackBoard* bb;
};

//extern const char* fail_pass_reason;
struct PlayMakerPassSortEntry
{
	float rating;
	ARugbyCharacter* player;
};

bool PlayMakerPassSort( const PlayMakerPassSortEntry& a, const PlayMakerPassSortEntry& b )
{
	return a.rating < b.rating;
}

bool RUActionPass::PassToPlayMaker()
{
	/// Find the play maker on our team that we can pass to
	m_pPlayer->GetAttributes()->GetTeam()->GetFormationManager()->UpdatePassPriorities( PASS_PRI_ALL, true );

	SIFRugbyCharacterList play_makers, other_good_kickers;
	m_pPlayer->GetAttributes()->GetTeam()->GetSuggestedPlayMakers( play_makers, other_good_kickers );

	/// Now we find the one with the highest pass priority that we can pass to and pass to them
	//std::sort( other_good_kickers.begin(), other_good_kickers.end(), PassPriorityGreater( &player->GetAttributes()->GetTeam()->GetBlackBoard() ) );

	play_makers.insert( play_makers.end(), other_good_kickers.begin(), other_good_kickers.end() );

	ARugbyCharacter* target = NULL;
	typedef MabVector< PlayMakerPassSortEntry > PlayMakerPassSortEntries;
	PlayMakerPassSortEntries ordered_playmakers;

	for( size_t i = 0; i < play_makers.size(); i++ )
	{
		const static float MAX_DIST_PREFER_PASS = 16.0f;
		float rating = (float) i;
		float dist_to_ball = SSMath::GetXZPointToPointDistance( play_makers[i]->GetMovement()->GetCurrentPosition(), m_pGame->GetBall()->GetCurrentPosition( ) );
		if ( dist_to_ball > MAX_DIST_PREFER_PASS )
			rating += dist_to_ball;
		PlayMakerPassSortEntry entry;
		entry.rating = rating;
		entry.player = play_makers[i];
		ordered_playmakers.push_back( entry );
	}

	std::sort( ordered_playmakers.begin(), ordered_playmakers.end(), PlayMakerPassSort );

	//MabString fail_text;

	for( size_t i = 0; i < ordered_playmakers.size(); i++ )
	{
		ARugbyCharacter* play_maker = ordered_playmakers[i].player;
		/// Cannot pass to ourself!
		if ( m_pPlayer == play_maker )
		{
			//fail_text += MabString( 16, "%d", play_makers[i]->GetAttributes()->GetTeamIndex() + 1 );
			//fail_text += "myself ";
			continue;
		}

		PASS_FAIL_REASON pfr;
		bool can_pass = m_pGame->GetStrategyHelper()->CanPassBallBetween( m_pPlayer, play_maker, pfr );
		if ( can_pass || (!can_pass && pfr == PF_OBSTRUCTED) )
		{
			target = play_maker;
			break;
		} else {
			//fail_text += MabString( 16, "%d", play_makers[i]->GetAttributes()->GetTeamIndex() + 1 );
			//fail_text += fail_pass_reason;
			//fail_text += " ";
		}
	}

	//if ( target != NULL && !play_makers.empty() && target != play_makers[0] )
	//	game->GetSimTimeNonConst()->Pause( true );

	if ( target != NULL )
	{
		//#ifdef ENABLE_OSD
		//		RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
		//		MabString activation_string( 256, "PASS to playmaker found number %d from ", target->GetAttributes()->GetTeamIndex() + 1 );
		//		for( size_t i = 0; i < play_makers.size(); i++ )
		//		{
		//			ARugbyCharacter* pp = play_makers[i];
		//			activation_string += MabString( 16, "%d", pp->GetAttributes()->GetTeamIndex() + 1 );
		//			if ( i < play_makers.size() - 1 )
		//				activation_string += ",";
		//		}
		//		settings->PushDebugString( game, RUGameDebugSettings::DP_PASS, activation_string.c_str() );
		//		fail_text = MabString( "Players failed " ) + fail_text;
		//		settings->PushDebugString( game, RUGameDebugSettings::DP_PASS, fail_text.c_str() );
		//		MABLOGDEBUG( activation_string.c_str() );
		//#endif
		Enter( target, params.type, RLRIT_HANGBACK );
		return true;
	} else {
		//#ifdef ENABLE_OSD
		//		RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
		//		MabString activation_string( 256, "PASS to playmaker failed" );
		//		settings->PushDebugString( game, RUGameDebugSettings::DP_PASS, activation_string.c_str() );
		//		settings->PushDebugString( game, RUGameDebugSettings::DP_PASS, fail_text.c_str() );
		//		MABLOGDEBUG( activation_string.c_str() );
		//#endif
	}

	return false;
}

#include "Mab/MabRandDistributions.h"

float RUActionPass::CalculatePassQuality()
{
	/// Get current passer accuracy
	float accuracy = m_pPlayer->GetAttributes()->GetPassAccuracy();
	float difficulty = pass_meta.difficulty;
	MabMath::Clamp( difficulty, 0.0f, 1.0f );

	const static float MEAN_BEST_ZERO_DIFFICULTY	= 0.95f;
	const static float VAR_BEST_ZERO_DIFFICULTY	= 0.05f;
	const static float MEAN_WORST_ZERO_DIFFICULTY	= 0.80f;
	const static float VAR_WORST_ZERO_DIFFICULTY	= 0.25f;

	const static float MEAN_WORST_FULL_DIFFICULTY	= 0.40f;
	const static float VAR_WORST_FULL_DIFFICULTY	= 0.20f;
	const static float MEAN_BEST_FULL_DIFFICULTY	= 0.75f;
	const static float VAR_BEST_FULL_DIFFICULTY	= 0.15f;

	float mean_zero_difficulty = MabMath::Lerp( MEAN_WORST_ZERO_DIFFICULTY, MEAN_BEST_ZERO_DIFFICULTY, accuracy );
	float mean_full_difficulty = MabMath::Lerp( MEAN_WORST_FULL_DIFFICULTY, MEAN_BEST_FULL_DIFFICULTY, accuracy );
	float mean_for_pass_difficulty = MabMath::Lerp( mean_zero_difficulty, mean_full_difficulty, difficulty );

	float var_zero_difficulty = MabMath::Lerp( VAR_WORST_ZERO_DIFFICULTY, VAR_BEST_ZERO_DIFFICULTY, accuracy );
	float var_full_difficulty = MabMath::Lerp( VAR_WORST_FULL_DIFFICULTY, VAR_BEST_FULL_DIFFICULTY, accuracy );
	float var_for_pass_difficulty = MabMath::Lerp( var_zero_difficulty, var_full_difficulty, difficulty );

	float rand_pass_quality = MabRandGaussian( *m_pGame->GetRNG(), mean_for_pass_difficulty, var_for_pass_difficulty );

	/// We don't want dummy passes to be of low quality as this is frustrating
	if ( params.type == PT_DUMMYHALF )
	{
		const static float INCREASE_DHALF_PASS_QUALITY = 0.85f;
		rand_pass_quality = MabMath::Lerp(rand_pass_quality, 1.0f, INCREASE_DHALF_PASS_QUALITY );
	}

	const static float FIXED_PASS_QUALITY = -1.0f;
	return FIXED_PASS_QUALITY < 0.0f ? rand_pass_quality : FIXED_PASS_QUALITY;
}

void RUActionPass::GetPassMeta( ARugbyCharacter* passer, ARugbyCharacter* target, const FVector& supplied_target_pos, PASS_META& pass_meta, PASS_TYPE pass_type, bool forProRequest)
{
	wwNETWORK_TRACE_JG("RUActionPass::GetPassMeta Passer: %u Supplied Target Pos: %s, pass type: %d, forProRequest: %d", passer->GetAttributes()->GetDbId(), TCHAR_TO_UTF8(*supplied_target_pos.ToString()), pass_type, forProRequest);
	if ( pass_type == PT_UNKNOWN )
	{
		if ( RUActionPass::IsAnOffload( passer ) )
			pass_type = PT_OFFLOAD;
		else
			pass_type = PT_STANDARD;
	}

	///// If we are moving away from the player then limit the maximum pass distance
	const FVector& player_pos = passer->GetMovement()->GetCurrentPosition();
	FVector target_pos = target != NULL ? target->GetMovement()->GetCurrentPosition() : supplied_target_pos;
	FVector target_vel = target != NULL ? target->GetMovement()->GetCurrentVelocity() : FVector::ZeroVector;
	FVector player_vel = passer->GetMovement()->GetCurrentVelocity();

	const static float MIN_CLOSING = -7.0f;
	float MAX_PASS_DIST_AT_MIN_CLOSING = (forProRequest ? MAX_PRO_REQ_OFFLOAD_DISTANCE : MAX_OFFLOAD_DISTANCE);

	pass_meta.difficulty = 0.0f;
	pass_meta.n_valid_offload_arms = 2;
	pass_meta.selected_offload_arm = OA_BOTH;
	pass_meta.max_pass_distance_by_closing	= forProRequest ? MAX_PRO_REQ_PASS_DIST : MAX_PASS_DIST;
	pass_meta.max_pass_distance_by_speed	= forProRequest ? MAX_PRO_REQ_PASS_DIST : MAX_PASS_DIST;
	pass_meta.max_pass_distance_by_tackle	= forProRequest ? MAX_PRO_REQ_PASS_DIST : MAX_PASS_DIST;
	pass_meta.max_pass_distance				= forProRequest ? MAX_PRO_REQ_PASS_DIST : MAX_PASS_DIST;

	wwNETWORK_TRACE_JG("RUActionPass::GetPassMeta player_pos: %s Target Pos: %s", TCHAR_TO_UTF8(*player_pos.ToString()), TCHAR_TO_UTF8(*target_pos.ToString()),
		TCHAR_TO_UTF8(*target_vel.ToString()), TCHAR_TO_UTF8(*player_vel.ToString()));

	/// Check pass distance by closing rate
	if ( target != NULL )
	{
		float closure_rate_passer_on_receiver_vel		= SSMath::GetXZClosureRate( player_pos, player_vel, target_pos, target_vel );
		float closure_rate_passer_on_receiver_no_vel	= SSMath::GetXZClosureRate( player_pos, player_vel, target_pos, FVector::ZeroVector );
		float closure_rate_passer_on_receiver = MabMath::Max( closure_rate_passer_on_receiver_vel, closure_rate_passer_on_receiver_no_vel );
		if ( closure_rate_passer_on_receiver < 0.0f )
		{
			float t = closure_rate_passer_on_receiver / MIN_CLOSING;
			MabMath::Clamp( t, 0.0f, 1.0f );
			pass_meta.max_pass_distance_by_closing = MabMath::Lerp( MAX_PASS_DIST, MAX_PASS_DIST_AT_MIN_CLOSING, t );
		}

		wwNETWORK_TRACE_JG("RUActionPass::GetPassMeta Passer: %u closure_rate_passer_on_receiver_vel: %f, closure_rate_passer_on_receiver_no_vel: %f, closure_rate_passer_on_receiver: %f, pass_meta.max_pass_distance_by_closing: %f",
			target->GetAttributes()->GetDbId(), closure_rate_passer_on_receiver_vel, closure_rate_passer_on_receiver_no_vel, closure_rate_passer_on_receiver, pass_meta.max_pass_distance_by_closing);
	}

	/// If the players are too far away then do not attempt a pass
	/// We decrease maximum pass distance based on how fast we are running right now also
	const static float MAX_SPEED_FOR_PASS = 11.0f;
	float t = (passer->GetMovement()->GetCurrentSpeed() / MAX_SPEED_FOR_PASS);
	MabMath::Clamp( t, 0.0f, 1.0f );
	const static float CLAMP_TO_MAX = 0.5f;
	if ( t < CLAMP_TO_MAX ) t = 0.0f;
	const static float MIN_PASS_DIST_BASED_ON_SPEED = MAX_PASS_DIST * 0.75f;
	pass_meta.max_pass_distance_by_speed = MabMath::Lerp( MAX_PASS_DIST, MIN_PASS_DIST_BASED_ON_SPEED, t );

	FVector face_vec;
	SSMath::AngleToMabVector3( passer->GetMovement()->GetCurrentFacingAngle(), face_vec );
	const static float MIN_X_VEC = 0.20f;
	FVector targ_delta = target_pos - player_pos;
	const static float MIN_SPEED_APPLY_ACROSS_FACE = 3.0f;
	bool across_face = passer->GetMovement()->GetCurrentSpeed() >= MIN_SPEED_APPLY_ACROSS_FACE && MabMath::Fabs( face_vec.x ) > MIN_X_VEC && MabMath::Sign( targ_delta.x ) != MabMath::Sign( face_vec.x );

	wwNETWORK_TRACE_JG("RUActionPass::GetPassMeta pass_meta.max_pass_distance_by_speed: %f, t: %f, face_vec: %s, targ_delta: %s, across_face: %d",
		pass_meta.max_pass_distance_by_speed, t, TCHAR_TO_UTF8(*face_vec.ToString()), TCHAR_TO_UTF8(*targ_delta.ToString()), across_face);
	/// If the passing players is being tackled then they cannot pass greater
	/// than the offload distance
	bool in_tackle = passer->GetActionManager()->IsActionRunning(ACTION_TACKLEE);

	pass_meta.time_till_tackle_contact = -1.0f;
	pass_meta.pre_tackle_contact_pct = -1.0f;

	if ( in_tackle )
	{
		/// Check distance
		const int OFFLOAD_ANGLE_COUNT = 4;
		const static float OFFLOAD_ANGLE_DIST_PCT_LIMITS[OFFLOAD_ANGLE_COUNT][2] = { { 0.0f, 1.0f }, { 90.0f, 0.95f }, { 145.0f, 0.70f }, { 165.0f, 0.45f } };
		float pass_angle_dist_multiplier = 1.0f;

		if ( target != NULL )
		{
			float pass_angle_deg = MabMath::Rad2Deg( MabMath::Fabs( MabMath::AngleDelta( passer->GetMovement()->GetCurrentFacingAngle(), SSMath::CalcAngleFromPoints( player_pos, target_pos ) ) ) );
			for ( int i = 0; i < (OFFLOAD_ANGLE_COUNT-1); i++ )
			{
				if ( MabMath::InRangeInclusive( pass_angle_deg, OFFLOAD_ANGLE_DIST_PCT_LIMITS[i][0], OFFLOAD_ANGLE_DIST_PCT_LIMITS[i+1][0] ) )
				{
					t = (pass_angle_deg - OFFLOAD_ANGLE_DIST_PCT_LIMITS[i][0]) / (OFFLOAD_ANGLE_DIST_PCT_LIMITS[i+1][0] - OFFLOAD_ANGLE_DIST_PCT_LIMITS[i][0]);
					MabMath::Clamp( t, 0.0f, 1.0f );
					pass_angle_dist_multiplier = MabMath::Lerp( OFFLOAD_ANGLE_DIST_PCT_LIMITS[i][1], OFFLOAD_ANGLE_DIST_PCT_LIMITS[i+1][1], t );

					wwNETWORK_TRACE_JG("RUActionPass::GetPassMeta InTackle i: %d, pass_angle: %f, t: %f, pass_angle_dist_multiplier: %f",
						i, pass_angle_deg, t, pass_angle_dist_multiplier);
					break;
				}
			}
		}

		/// If the pass is before the tackler has made contact - check to see how long it will be before contact
		/// and adjust the maximum pass distance accordingly
		static const float MAX_PASS_DIST_PRE_CONTACT_PCT = 0.45f;
		static const float MIN_TIME_TILL_CONTACT = 0.12f;
		static const float MAX_TIME_TILL_CONTACT = 0.50f;
		float max_pass_dist_pre_contact = MAX_PASS_DIST * MAX_PASS_DIST_PRE_CONTACT_PCT;

		
		if (passer->GetAnimation() && passer->GetAnimInstance() && passer->GetAnimation()->GetStateMachine().GetSMTackle() && passer->GetAnimation()->GetStateMachine().GetSMTackle()->IsInTackle())
		{
			float time_till_contact = passer->GetAnimInstance()->GetTimeTillContact();
			pass_meta.time_till_tackle_contact = time_till_contact;
			float pct = 0.0f;
			if ( time_till_contact >= 0.0f )
			{
				pct = (time_till_contact - MIN_TIME_TILL_CONTACT) / (MAX_TIME_TILL_CONTACT - MIN_TIME_TILL_CONTACT);
				MabMath::Clamp( pct, 0.0f, 1.0f );
				max_pass_dist_pre_contact = MabMath::Lerp( (forProRequest ? MAX_PRO_REQ_OFFLOAD_DISTANCE : MAX_OFFLOAD_DISTANCE), MAX_PASS_DIST * MAX_PASS_DIST_PRE_CONTACT_PCT, pct );
			}
			pass_meta.pre_tackle_contact_pct = pct;

			wwNETWORK_TRACE_JG("RUActionPass::GetPassMeta InTackle time_till_contact: %f, pct: %f, max_pass_dist_pre_contact: %f",
				time_till_contact, pct, max_pass_dist_pre_contact);
		}
		pass_meta.max_pass_distance_by_tackle = RUActionPass::IsAnOffload( passer ) ? (forProRequest ? MAX_PRO_REQ_OFFLOAD_DISTANCE : MAX_OFFLOAD_DISTANCE) : max_pass_dist_pre_contact;
		pass_meta.max_pass_distance_by_tackle *= pass_angle_dist_multiplier;

		wwNETWORK_TRACE_JG("RUActionPass::GetPassMeta InTackle pass_meta.max_pass_distance_by_tackle: %f", pass_meta.max_pass_distance_by_tackle);
		/// If we are passing in the opposite direction to our velocity then we want to drop the max pass dist down drastically
	}

	if ( across_face )
	{
		//const static float MAX_ACROSS_FACE_DISTANCE = MAX_PASS_DIST * 0.75f;
		//MabMath::ClampUpper( pass_meta.max_pass_distance_by_tackle, MAX_ACROSS_FACE_DISTANCE );
		//static float MAX_ACROSS_FACE_DISTANCE = 13.0f;
		MabMath::ClampUpper( pass_meta.max_pass_distance_by_tackle, (forProRequest ? MAX_PRO_REQ_OFFLOAD_DISTANCE : MAX_OFFLOAD_DISTANCE) );
	}

	/// Clamp pass distance to the lowest available distance
	MabMath::ClampUpper( pass_meta.max_pass_distance, pass_meta.max_pass_distance_by_tackle );
	MabMath::ClampUpper( pass_meta.max_pass_distance, pass_meta.max_pass_distance_by_speed );
	MabMath::ClampUpper( pass_meta.max_pass_distance, pass_meta.max_pass_distance_by_closing );

	//MABASSERT( pass_meta.max_pass_distance <= MAX_PASS_DIST );

	/// Now work out pass difficulty
	if ( pass_type == PT_OFFLOAD )
	{
		/// Check can offload in required direction / what arm is free
		RUActionTacklee* tacklee_action = passer->GetActionManager()->GetAction<RUActionTacklee>();
		tacklee_action->GetOffloadMeta( pass_meta.offload_meta );

		/// See if any of the available arms allow us to offload in this direction and what difficulty they are
		float face_angle = passer->GetMovement()->GetCurrentFacingAngle();
		float tacklee_to_player_angle = SSMath::CalcAngleFromPoints( player_pos, target_pos );
		float angle_delta = MabMath::AngleDelta( face_angle, tacklee_to_player_angle );


		wwNETWORK_TRACE_JG("RUActionPass::GetPassMeta face_angle: %f, tacklee_to_player_angle: %f, angle_delta: %f",
			face_angle, tacklee_to_player_angle, angle_delta);


		/// Work out which of the offload options can be used
		struct OFFLOAD_ANGLE_ENTRY
		{
			float min_angle;
			float max_angle;
			float difficulty_add;
		};

		/// These structures are triples of min,max angles and difficulty addition for each pass arm
		static const OFFLOAD_ANGLE_ENTRY DIFF_MOD_LEFT[] =
		{
			// +ve rot is left, -ve rot is right
			{ -MabMath::Deg2Rad( 20.0f ),  MabMath::Deg2Rad( 110.0f ), 0.0f },
			{  MabMath::Deg2Rad( 100.0f ), MabMath::Deg2Rad( 160.0f ), 0.4f },
			{ -PI, +PI, 0.2f }
		};
		static const OFFLOAD_ANGLE_ENTRY DIFF_MOD_RIGHT[] =
		{
			// +ve rot is left, -ve rot is right
			{ -MabMath::Deg2Rad( 110.0f ),  MabMath::Deg2Rad( 20.0f ),  0.0f },
			{ -MabMath::Deg2Rad( 160.0f ), -MabMath::Deg2Rad( 100.0f ), 0.4f },
			{ -PI, +PI, 0.2f }
		};
		static const OFFLOAD_ANGLE_ENTRY DIFF_MOD_BOTH[] =
		{
			// +ve rot is left, -ve rot is right
			{ -MabMath::Deg2Rad( 125.0f ),  +MabMath::Deg2Rad( 125.0f ), 0.0f },
			{ -MabMath::Deg2Rad( 160.0f ),   MabMath::Deg2Rad( 160.0f ), 0.2f },
			{ -PI, +PI, 0.3f }
		};
		static const size_t N_ANGLE_ENTRIES[OA_LAST] = { 3, 3, 3 };

		for( int i = 0; i < OA_LAST; i++ )
		{
			/// Iterate over supplied angle ranges for offload arms and apply
			pass_meta.offload_arm_meta[i].difficulty = IMPOSSIBLE_DIFFICULTY;
			if (!pass_meta.offload_meta.can_do[i])
			{
				wwNETWORK_TRACE_JG("RUActionPass::GetPassMeta Can't do pass: %d", i);
				continue;
			}

			const OFFLOAD_ANGLE_ENTRY *ANGLE_ENTRIES = NULL;
			switch( i )
			{
			case OA_LEFT:	ANGLE_ENTRIES = DIFF_MOD_LEFT;	break;
			case OA_RIGHT:	ANGLE_ENTRIES = DIFF_MOD_RIGHT;	break;
			case OA_BOTH:	ANGLE_ENTRIES = DIFF_MOD_BOTH;	break;
			default:
				break;
			}
			MABASSERT( ANGLE_ENTRIES != NULL );
			pass_meta.offload_arm_meta[i].difficulty = pass_meta.offload_meta.difficulty[i];

			wwNETWORK_TRACE_JG("RUActionPass::GetPassMeta i: %d, pass_meta.offload_arm_meta[i].difficulty: %f",
				i, pass_meta.offload_arm_meta[i].difficulty);

			for( size_t j = 0; j < N_ANGLE_ENTRIES[i]; j++ )
			{
				float min_angle = ANGLE_ENTRIES[j].min_angle;
				float max_angle = ANGLE_ENTRIES[j].max_angle;
				MABASSERT( min_angle < max_angle );
				if ( MabMath::InRangeInclusive( angle_delta, min_angle, max_angle ) )
				{
					pass_meta.offload_arm_meta[i].difficulty += ANGLE_ENTRIES[j].difficulty_add;
					wwNETWORK_TRACE_JG("RUActionPass::GetPassMeta i: %d, ANGLE_ENTRIES[j].difficulty_add: %f",
						i, ANGLE_ENTRIES[j].difficulty_add);
					break;
				}
			}
		}

		float lowest_difficulty = 1e20f;
		pass_meta.selected_offload_arm = OA_LAST;
		pass_meta.n_valid_offload_arms = 0;
		for( int i = 0; i < OA_LAST; i++ )
		{
			wwNETWORK_TRACE_JG("Checking Arm: %d Difficulty: %f Lowest: %f", i, pass_meta.offload_arm_meta[i].difficulty, lowest_difficulty);

			if (MabMath::InRangeInclusive(pass_meta.offload_arm_meta[i].difficulty, 0.0f, 0.999f))
			{
				pass_meta.n_valid_offload_arms++;
			}

			/// Find the lowest difficulty arm
			if ( pass_meta.offload_arm_meta[i].difficulty < lowest_difficulty )
			{
				wwNETWORK_TRACE_JG("Checking Difficulty Arm: %d", i);
				lowest_difficulty = pass_meta.offload_arm_meta[i].difficulty;
				pass_meta.selected_offload_arm = (OFFLOAD_ARM) i;
				pass_meta.difficulty = lowest_difficulty;
			}
		}

		if ( pass_meta.n_valid_offload_arms == 0 )
			pass_meta.difficulty = 1.0f;

	} else {
		float difficulty_by_speed = 0.0f;
		float difficulty_by_pre_tackle = 0.0f;
		if ( in_tackle ) {
			/// Tackle anim has started but we have yet to contact player
			const static float BASE_TACKLE_DIFFICULTY = 0.3f;
			difficulty_by_pre_tackle = BASE_TACKLE_DIFFICULTY;
		}

		const static float MAX_DIFFICULTY_SPEED = 10.0f;
		const static float MIN_DIFFICULTY_SPEED = 5.0f;
		const static float MAX_DIFFICULTY_AT_SPEED = 0.3f;
		float difficulty_by_speed_t = (passer->GetMovement()->GetCurrentSpeed() - MIN_DIFFICULTY_SPEED) / (MAX_DIFFICULTY_SPEED - MIN_DIFFICULTY_SPEED);
		MabMath::Clamp( difficulty_by_speed_t, 0.0f, 1.0f );
		difficulty_by_speed = difficulty_by_speed_t * MAX_DIFFICULTY_AT_SPEED;

		pass_meta.difficulty = difficulty_by_pre_tackle + difficulty_by_speed;
	}

	/// Outputs - max pass dist + difficulty of pass
}

void RUActionPass::Abort()
{
	MABLOGDEBUG( "RUActionPass:Abort: %d", m_pPlayer->GetAttributes()->GetIndex() );
	aborted = true;
}

MabString RUActionPass::GetOffloadPassMetaDebugString()
{
	char buffer[1024];
	char minibuf[256];

	memset( buffer, 0, sizeof( buffer ) );

	MabStringHelper::Sprintf( minibuf, "Num arms free: %d\n", pass_meta.n_valid_offload_arms);
	MabStringHelper::Strcat( buffer, minibuf );

	MabString selected_offload_arm;
	switch ( pass_meta.selected_offload_arm )
	{
	case OA_BOTH:
		selected_offload_arm = "both arms";
		break;
	case OA_LEFT:
		selected_offload_arm = "left arm";
		break;
	case OA_RIGHT:
		selected_offload_arm = "right arm";
		break;
	case OA_LAST:
		break;
	}

	MabStringHelper::Sprintf( minibuf, "Selected offload arm: %s\n", selected_offload_arm.c_str() );
	MabStringHelper::Strcat( buffer, minibuf );

	MabStringHelper::Sprintf( minibuf, "Difficulty: %0.2f\n", pass_meta.difficulty );
	MabStringHelper::Strcat( buffer, minibuf );

	MabStringHelper::Sprintf( minibuf, "Offload arm difficulty: %0.2f\n", pass_meta.offload_arm_meta->difficulty );
	MabStringHelper::Strcat( buffer, minibuf );

	MabStringHelper::Sprintf( minibuf, "Max distance: %0.2f\n", pass_meta.max_pass_distance );
	MabStringHelper::Strcat( buffer, minibuf );

	MabStringHelper::Sprintf( minibuf, "Can offload: %s\n", pass_meta.offload_meta.can_do[pass_meta.selected_offload_arm] ? "true" : "false" );
	MabStringHelper::Strcat( buffer, minibuf );

	MabStringHelper::Sprintf( minibuf, "Is falling: %s\n", pass_meta.offload_meta.falling ? "true" : "false" );
	MabStringHelper::Strcat( buffer, minibuf );

	MabStringHelper::Sprintf( minibuf, "Offload angle: %0.2f\n", pass_meta.offload_meta.offload_angle[pass_meta.selected_offload_arm] );
	MabStringHelper::Strcat( buffer, minibuf );

	if ( chosen_offload_anim.length() )
	{
		MabStringHelper::Sprintf( minibuf, "Chosen offload anim: %s", chosen_offload_anim.c_str() );
		MabStringHelper::Strcat( buffer, minibuf );
	}

	MABASSERT( strlen( buffer ) < 1024 );

	return buffer;
}

bool RUActionPass::IsOffloadAllowed() const
{
	const static int LEFT = +1;
	const static int RIGHT = -1;
	//const static float HALF_PI = PI * 0.5f;
	const static float TWO_PI = PI * 2.0f;

	PASS_META local_meta;
	GetPassMeta( m_pPlayer, params.target_player, params.target_offset, local_meta, PT_OFFLOAD );

	// Fast fail, if the difficulty is impossible, don't do the offload.
	// Usually this value maps 0 -> 1.
	if ( local_meta.difficulty >= IMPOSSIBLE_DIFFICULTY )
	{
		return false;
	}

	float facing_angle = m_pPlayer->GetMovement()->GetCurrentFacingAngle();

	if ( facing_angle < 0.0f )
	{
		// Convert to a positive angle.
		facing_angle = TWO_PI - abs( facing_angle );
	}

	bool player_facing_forwards = MabMath::IsInRange( facing_angle, 0.0f, HALF_PI ) || MabMath::IsInRange( facing_angle, PI + HALF_PI, TWO_PI );

	//SETDEBUGLINE( 1268349, player->GetMovement()->GetCurrentPosition(), player->GetMovement()->GetCurrentPosition() + FVector::X_AXIS * LEFT * 5.0f, MabColour::Blue, MabColour::Blue );
	//SETDEBUGLINE( 1268350, player->GetMovement()->GetCurrentPosition(), player->GetMovement()->GetCurrentPosition() + FVector::X_AXIS * RIGHT * 5.0f, MabColour::Red, MabColour::Red );
	//SETDEBUGLINE( 1268351, player->GetMovement()->GetCurrentPosition(), player->GetMovement()->GetCurrentPosition() + FVector::Z_AXIS * 5.0f, MabColour::Yellow, MabColour::Yellow );
	//SETDEBUGLINE( 1268352, player->GetMovement()->GetCurrentPosition(), player->GetMovement()->GetCurrentPosition() - FVector::Z_AXIS * 5.0f, MabColour::Green, MabColour::Green );
	//SETDEBUGLINE( 1268353, player->GetMovement()->GetCurrentPosition(), player->GetMovement()->GetCurrentPosition() + FVector::Z_AXIS * MabMatrix::RotMatrixY( player->GetMovement()->GetCurrentFacingAngle() ) * 2.0f, player_facing_forwards ? MabColour::Cyan : MabColour::Orange, player_facing_forwards ? MabColour::Cyan : MabColour::Orange );

// 	MABLOGDEBUG( "Facing angle: %0.2f", facing_angle );
// 	MABLOGDEBUG( "Facing forwards: %i", player_facing_forwards );
	MABLOGDEBUG( "direction: %i", direction);
	MABLOGDEBUG( "Selected offload arm: %i", local_meta.selected_offload_arm);

	// Don't let players offload in the opposite direction to their arm, eg, using the left arm to pass the ball right.
	// Reason for this is that these passes are sometimes impossible (unless you have no spine/shoulder) as their arm goes back around their spine.
	// TLDR: Stop players from making physically impossible passes.
	if ( player_facing_forwards )
	{
		if ( direction == RIGHT && local_meta.selected_offload_arm == OA_LEFT )
		{
			MABLOGDEBUG( "Forbidden offload. Left arm passing right but we are facing forward." );
			return false;
		}

		if ( direction == LEFT && local_meta.selected_offload_arm == OA_RIGHT )
		{
			MABLOGDEBUG( "Forbidden offload. Right arm passing left but we are facing forward." );
			return false;
		}
	}
	else // Directions are reversed if the player is facing backwards, just like a mirror.
	{
		if( direction == RIGHT && local_meta.selected_offload_arm == OA_RIGHT )
		{
			MABLOGDEBUG( "Forbidden offload. Right arm passing right but we are facing backwards." );
			return false;
		}

		if ( direction == LEFT && local_meta.selected_offload_arm == OA_LEFT )
		{
			MABLOGDEBUG( "Forbidden offload. Left arm passing left but we are facing backwards." );
			return false;
		}
	}

	return true;
}

void RUActionPass::UpdateBallReleaseChecks( float &Inhit_magnitude, float delta_time )
{
	bool tackle_prohibits_release = false;

	bool tacklee_can_offload_now = true;
	bool is_offload_allowed = true;
	Inhit_magnitude = 0.0f;

	if ( m_pPlayer->GetActionManager()->IsActionRunning( ACTION_TACKLEE ) )
	{
		RUActionTacklee* tacklee_action = m_pPlayer->GetActionManager()->GetAction<RUActionTacklee>();
		tacklee_can_offload_now = tacklee_action->CanOffloadNow();
		is_offload_allowed = IsOffloadAllowed();
		tackle_prohibits_release = !(tacklee_can_offload_now && is_offload_allowed);

		/// TYRONE : Diabled hits on tackles
		//if ( tacklee_action->HasContactedAtLeastOnceInChain() )
		//{
		//	const RUTackleResult& result = tacklee_action->GetTackleResult();
		//	if ( result.tacklers[0] )
		//	{
		//		const static float HIT_MAGNITUDE_MULTIPLIER = 0.3f;
		//		Inhit_magnitude = result.actual_tacklers_impetus * HIT_MAGNITUDE_MULTIPLIER;
		//		MabMath::Clamp( Inhit_magnitude, 0.0f, 1.0f );
		//	}
		//}
	}

	bool this_release_ball_check = !tackle_prohibits_release;

	if ( !this_release_ball_check )
		if ( time_since_release_prevented < 0.0f )
			time_since_release_prevented = 0.0f;
		else
			time_since_release_prevented += delta_time;
	else
		time_since_release_prevented = -1.0f;

	current_release_ball_check = !tackle_prohibits_release;

	//SETDEBUGLINE( 34873488, player->GetMovement()->GetCurrentPosition() + FVector( -0.05f, 0.0f, 0.0f ), player->GetMovement()->GetCurrentPosition() + FVector::Y_AXIS * 5.0f + FVector( -0.05f, 0.05f, 0.0f ), !tacklee_can_offload_now ? MabColour::Orange : MabColour::Gray( 0.5f ), !tacklee_can_offload_now ? MabColour::Orange : MabColour::Gray( 0.5f ) );
	//SETDEBUGLINE( 34873489, player->GetMovement()->GetCurrentPosition() + FVector( +0.05f, 0.0f, 0.0f ), player->GetMovement()->GetCurrentPosition() + FVector::Y_AXIS * 5.0f + FVector( +0.05f, 0.00f, 0.0f ), !is_offload_allowed ? MabColour::Yellow : MabColour::Gray( 0.5f ),  !is_offload_allowed ? MabColour::Yellow : MabColour::Gray( 0.5f ) );
	//SETDEBUGTEXTWORLD( 34873490, player->GetMovement()->GetCurrentPosition() + FVector::Y_AXIS * 2.5f, MabString( 32, "%0.2f %0.3f", time_since_release_prevented, Inhit_magnitude ).c_str() );
}
