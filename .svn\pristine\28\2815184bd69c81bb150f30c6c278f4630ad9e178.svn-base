//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBallSecondDefender.h"

#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/Roles/Competitors/RURoleGetTheBall.h"
#include "Match/AI/Roles/Competitors/RURolePenaltyAttack.h"
#include "Match/AI/Roles/Competitors/SSRoleFormation.h"
#include "Match/Ball/SSBall.h"
#include "Match/Camera/SSCameraManager.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUInputPhaseDecision.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/RugbyUnion/Rules/Triggers/RURuleTriggerPenalty.h"
#include "Match/SIFGameObject.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Match/SSRoleFactory.h"
#include "Match/SSSpatialHelper.h"
#include "Match/AI/Actions/RUActionTacklee.h"

//#rc3_legacy_include #include <NMMabAnimationEvents.h>

#include "Match/AI/Roles/Competitors/RURoleFullback.h"


#include "Character/RugbyPlayerController.h"
#include "RugbyGameInstance.h"

MABRUNTIMETYPE_IMP1( RURolePlayTheBallSecondDefender, SSRole );

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
RURolePlayTheBallSecondDefender::RURolePlayTheBallSecondDefender( SIFGameWorld* game )
: RURolePlayTheBallDefender(game)
{
}

//-------------------------------------------------------------------------
// Enter
//-------------------------------------------------------------------------
void RURolePlayTheBallSecondDefender::Enter(ARugbyCharacter* player)
{
	SSRole::Enter(player);
	MABASSERT(player == m_pPlayer);

	auto ballHolder = m_pGame->GetGameState()->GetBallHolder();
	if (ballHolder == nullptr)
	{
		state = RoleState::COMPLETE;
		Exit(true);
		return;
	}
	//// --- Role assignment based on zone WIP, when assigning back to formation role it does not go to formation instead runs at play the ball ---
	// Get the direction our team is attacking (NORTH or SOUTH)
	//auto PlayDir = m_pPlayer->GetAttributes()->GetPlayDirection();
	//
	//// Determine which X value is our try line based on play direction
	//const float tryLineX = (PlayDir == ERugbyPlayDirection::NORTH) ? -5000.0f : 5000.0f;
	//
	//// Get ball holder's X position
	//const float ballX = ballHolder->GetActorLocation().X;
	//
	//// Calculate distance from our own try line
	//const float distFromTryLine = FMath::Abs(ballX - tryLineX);
	//
	//// Thresholds (in Unreal units): 20m = 2000, 80m = 8000
	//const float goalZoneThreshold = 2000.0f;
	//const float attackingZoneThreshold = 8000.0f;
	//
	//// --- Role assignment based on zone ---
	//if (distFromTryLine <= goalZoneThreshold)
	//{
	//	// Always assign role (critical zone)
	//	UE_LOG(LogTemp, Warning, TEXT("Defensive Goal Area: = %.2f meters"), distFromTryLine);
	//}
	//else if (distFromTryLine < attackingZoneThreshold)
	//{
	//	UE_LOG(LogTemp, Warning, TEXT("MidField Goal Area: = %.2f meters"), distFromTryLine);
	//	// 70% chance to keep role
	//	if (FMath::FRand() > 1.0f)
	//	{
	//		UE_LOG(LogTemp, Warning, TEXT("No 2nd Defender"));
	//
	//		SSRole* new_role = m_pGame->GetRoleFactory()->Instance(SSRoleFormation::RTTGetStaticType());
	//		SSRole* old_role = player->GetRole();
	//
	//		MabTypeID old_role_id = 0;
	//		if (old_role != nullptr) old_role_id = old_role->RTTGetType();
	//		MabTypeID new_role_id = new_role->RTTGetType();
	//		
	//		//player->ClearRole();
	//
	//		player->SetRole(std::move(new_role));
	//		m_pGame->GetEvents()->player_role_change(player, old_role_id, new_role_id);
	//
	//		//state = RoleState::COMPLETE;
	//		//Exit(true);
	//		return;
	//	}
	//	else
	//	{
	//		UE_LOG(LogTemp, Warning, TEXT("Yes 2nd Defender"));
	//	}
	//}

	// --- Proceed with standard behavior ---
	UpdateDefenderPosition();
	wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);

	m_pMovement->SetFacingActor(ballHolder);
	state = RoleState::MOVING;

	m_lock_manager.HFLockAll();
	m_lock_manager.HFUnlock(HF_CHANGE_PLAYER);
	m_pActionManager->EnableAllActions(false);

}

//-------------------------------------------------------------------------
// GetFitness
//-------------------------------------------------------------------------
int RURolePlayTheBallSecondDefender::GetFitness(const ARugbyCharacter* player, const SSRoleArea* area)
{
	RUGameState* state = player->GetGameWorld()->GetGameState();
	if (state)
	{
		ARugbyCharacter* closest = state->GetClosestDefenderToTackler();
		if (closest)
		{
			if (closest->GetName() == player->GetName())
			{
				UE_LOG(LogTemp, Warning, TEXT("RURolePlayTheBallSecondDefender: 2nd defender SET for %s"), *player->GetName());
				return 10000;
			}
			else
			{
				return -100;
			}
		}
	}
	return -100;
}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
FVector RURolePlayTheBallSecondDefender::GetDefenderPosition()
{
	auto ballHolder = m_pGame->GetGameState()->GetBallHolder();
	if (ballHolder)
	{
		// Use the actual PTB player's current position for alignment instead of restart position to align with defender
		FVector ptb_player_pos = ballHolder->GetMovement()->GetCurrentPosition();
		FVector offset = FVector(0, 0, 4.5f * ballHolder->GetAttributes()->GetPlayDirection());
		m_last_defender_pos = ptb_player_pos + offset;
	}
	return m_last_defender_pos;
}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
void RURolePlayTheBallSecondDefender::WarpToWaypoint()
{
	auto ballHolder = m_pGame->GetGameState()->GetBallHolder();
	if (ballHolder)
	{
		FVector passer_pos = ballHolder->GetMovement()->GetCurrentPosition();
		FVector offset = FVector(0, 0, 4.5f * ballHolder->GetAttributes()->GetPlayDirection());
		FVector receive_pos = passer_pos + offset;
		m_pPlayer->GetMovement()->SetFacingFlags(AFFLAG_FACEPLAYDIR);
		m_pMovement->SetTargetPosition(receive_pos, true);
		SSRole::WarpToWaypoint();
	}
}