/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/Debug/RUGameDebugSettings.h"

//#rc3_legacy_include #include <Network.h>
//#include "Mab/Lua/MabLuaAutoBinder.h" //#rc3_legacy_lua

#include "Match/SIFGameContext.h"
#include "Match/Debug/SIFDebug.h"
#include "SIFDebugMenu.h"
//#rc3_legacy_include #include "SIFDebugDrawKeys.h"
//#rc3_legacy_include #include "SIFGameFlowNode.h"
#include "Match/SIFGamePauseState.h"
#include "Match/AI/Actions/RUAction.h"
//#rc3_legacy_include #include "RUCrowdManager.h"
//#rc3_legacy_include #include "RUCrowdCameraFlashes.h"
//#rc3_legacy_include #include "RUCrowdFlags.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/RugbyUnion/RUGameState.h"
//#rc3_legacy_include #include "SIFFlowConstants.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SSRole.h"

//#rc3_legacy
//#include "Match/Components/RUActionManager.h"
//#include "Match/RugbyUnion/Enums/RUPlayerPositionEnum.h"
////#rc3_legacy_include #include "PSSGMabVectormath.h"
//#include <MabCentralTypeDatabase.h>
//#include <MabLuaTypeDatabase.h>
//#include "Mab/Utility/MabTranslationManager.h"
//#include <MabController.h>
//#include "Match/Camera/SSCameraManager.h"
//#include "Match/SSRoleNull.h"
//#include "Match/SSRoleFactory.h"
#include "Match/SSSpatialHelper.h"
//#include "Match/AI/Formations/SSEVDSFormationManager.h"
//#include "NMMabAnimationNetwork.h"
#include "Utility/RURandomNumberGenerator.h"
//#include "Match/HUD/RU3DHUDManager.h"
////#rc3_legacy_include #include "RUContextualHelper.h"
//#include "Match/RugbyUnion/RUEmotionEngineManager.h"
//#include "Match/SSReplaysMk2/SSReplayManager.h"
//#include "Match/Components/RUPlayerState.h"
//#include "Match/Components/RUPlayerLookAt.h"
//#include "Match/Components/RUPlayerAnimation.h"
//#include "Match/RUAsyncLoadingEnable.h"
//#include "Match/RugbyUnion/RUGameGetToBall.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
//#include "Match/RugbyUnion/RUWeatherManager.h"
//#include "Match/SSGameTimer.h"
//#include "Match/RugbyUnion/RUStrategyHelper.h"
//#include "Match/SSMath.h"
//#include "Match/AI/Actions/RUActionTacklee.h"
//#include "Match/RugbyUnion/Statistics/RUStatisticsSystem.h"
#include "Match/Ball/SSBall.h"
//#include "Match/AI/Actions/RUActionPass.h"
//#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuckScrumHalf.h"
//#include "Match/RugbyUnion/Rules/Offside/RUOffsideIndicator.h"
//#include "Match/Debug/RUMemoryTags.h"
//
//#include "Match/RugbyUnion/RUGameEvents.h"
//#include "Match/RugbyUnion/Rules/RURulesDebugSettings.h"
#include "Match/RugbyUnion/RUGameSettings.h"
//
#ifdef ENABLE_PRO_MODE
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#endif
//#include "Match/Components/SSHumanPlayer.h"
//

#include "Match/SIFGameWorld.h"
#include "RugbyGameInstance.h"

#include "DebugOptions.h"
#include "UI/Screens/WWUIScreenDebugWindow.h"
#include "Mab/Types/MabRuntimeType.h"
#include "Match\SIFGameWorld.h"
#include "Match\RugbyUnion\RUWeatherManager.h"

#ifdef ENABLE_GAME_DEBUG_MENU

static const float TOP    = 32;
static const float LEFT   = 32;
static const float RIGHT  = 640 - 32;
static const float HEIGHT = 12;

// Implement for serialisation.

//#rc3_legacy MABRUNTIMETYPE_IMP1( RUGameDebugSettings, MabObject )

// -------------------------------------------- Local Constants --------------------------------------------

static const char* DEBUG_MENU_NAME_GAME = "SIF Game Debug Settings";
static const char* DEBUG_MENU_PATH = "ui/menu/game_debug_options.xml";
static const char* DEBUG_GAME_STATE_DISPLAY_NAME = "show_game_state";
static const char* DEBUG_PLAYER_STATE_DISPLAY_NAME = "show_player_state";
static const char* DEBUG_PLAYER_WAYPOINTS_DISPLAY_NAME = "show_player_waypoints";
static const char* DEBUG_TEAM_STATE_DISPLAY_NAME = "show_team_state";
static const char* DEBUG_COLLIDABLES_DISPLAY_NAME = "show_collidables";
static const char* DEBUG_ORIGINS_DISPLAY_NAME = "show_origins";

static const char* DEBUG_ATT_FORMATIONS_DISPLAY_NAME = "show_formations_def";
static const char* DEBUG_DEF_FORMATIONS_DISPLAY_NAME = "show_formations_att";
static const char* DEBUG_ALL_FORMATIONS_DISPLAY_NAME = "show_formations_all";

static const char* DEBUG_PASS_DEBUG_DISPLAY_NAME = "pass_view_debug";
static const char* DEBUG_INTERCEPT_DEBUG_DISPLAY_NAME = "intercept_view_debug";
static const char* DEBUG_SLOT_DEBUG_DISPLAY_NAME = "slot_view_debug";
static const char* DEBUG_EXCITEMENT_DEBUG_DISPLAY_NAME = "excitement_view_debug";
static const char* DEBUG_PARTICIPATION_LEVEL_DEBUG_DISPLAY_NAME = "participation_debug";
static const char* DEBUG_FIELD_LINES_DISPLAY_NAME = "field_lines_debug";
static const char* DEBUG_PAUSED = "paused";
static const char* DEBUG_PAGE_DISPLAY_NAME = "debug_page";
static const char* DEBUG_LABEL_MODE_DISPLAY_NAME = "debug_label_mode";
static const char* DEBUG_ATTACK_DISABLED_DISPLAY_NAME = "attack_disabled";
static const char* DEBUG_DEFENCE_DISABLED_DISPLAY_NAME = "defence_disabled";
static const char* DEBUG_DISABLE_RUGED_DEBUG_INFO = "ruged_debug_disabled";
static const char* DEBUG_DISABLE_AI_IN_GAME = "disable_ai";
static const char* DEBUG_OFFSIDE_PLAYERS_NAME = "show_offside_players";
static const char* DEBUG_OFFSIDE_TEAMS_NAME = "set_teams_offside";
static const char* DEBUG_GTB_LEVEL_NAME = "GTB_debug_level";
static const char* DEBUG_BALL_STATE_NAME = "show_ball_state";
static const char* DEBUG_CAMERA_LEVELS_NAME = "debug_camera_level";
static const char* DEBUG_CAMERA_REPLAY_CAMS = "replay_cams";
static const char* DEBUG_ADVANCE_TEST_BED = "advance_to_next_testbed";
static const char* DEBUG_INPUT_ACTION_NAME = "input_action_debug";
static const char* DEBUG_GESTURES_NAME = "show_gesture_state";
static const char* DEBUG_THROW_IN_NAME = "show_throw_in_debug";
static const char* DEBUG_LINEOUT_CATCH_NAME = "show_lineout_catch_debug";
static const char* DEBUG_LINEOUT_SLOWMOTION_NAME = "disable_lineout_slowmotion";
static const char* DEBUG_LINEOUT_REPEAT_FORCED = "lineout_repeat_forced";
static const char* DEBUG_RUCK_DISABLE_AI = "disable_ruck_ai";
static const char* DEBUG_BALLHOLDER_INFO = "ball_holder_debug";
static const char* DEBUG_FBG_RECEIVER = "fbg_receiver_debug";
static const char* DEBUG_FBG_ATTACKER = "fbg_attacker_debug";

static const char* DEBUG_TUTORIAL_DEMO_RECORD = "tutorial_demo_record";
static const char* DEBUG_TUTORIAL_DEMO_PLAYBACK = "tutorial_demo_playback";
static const char* DEBUG_TUTORIAL_DEMO_SAVE = "tutorial_demo_save";
static const char* DEBUG_TUTORIAL_DEMO_EXPORT = "tutorial_demo_export";

static const char* DEBUG_CROWD_ENABLED_DISPLAY_NAME = "crowd_enabled";
static const char* DEBUG_CROWD_CAMERA_FLASHES_ENABLED_DISPLAY_NAME = "crowd_camera_flashes_enabled";
static const char* DEBUG_CROWD_FLAGS_ENABLED_DISPLAY_NAME = "crowd_flags_enabled";
static const char* DEBUG_CROWD_DENSITY_DISPLAY_NAME = "crowd_density";

static const char* DEBUG_GAME_MODE_ENABLED_DISPLAY_NAME = "debug_game_mode";
static const char* DEBUG_GAME_MODE_DISPLAY_NAME = "game_mode";

#ifdef ENABLE_PRO_MODE
static const char* DEBUG_PRO_MODE_DISPLAY_NAME = "debug_pro_mode";
static const char* DEBUG_PRO_MODE_ENABLED_DISPLAY_NAME = "pro_mode_enabled";
static const char* DEBUG_PRO_ID_DISPLAY_NAME = "pro_id";
#endif

static const char* DEBUG_ACTOR_INPUT_ACCEL_TIME = "actor_input_accel_time";

static const char* DEBUG_SETPLAYDEBUG_ENABLED_DISPLAY_NAME = "setplay_debug";

static const char* DEBUG_TACKLE_DISABLED_DISPLAY_NAME = "tackle_disabled";

static const char* DEBUG_BALL_HOLDER_DISPLAY_NAME = "set_ball_holder";
static const char* DEBUG_FORCE_SITUATION_NAME = "set_situation";

static const char* DEBUG_CONTEXT_HELP_DISABLED_DISPLAY_NAME = "context_help_disabled";
static const char* DEBUG_POSTEFFECTS_MANAGER_ENABLED_DISPLAY_NAME = "posteffects_manager_enabled";
static const char* DEBUG_SIMULATE_NETWORK_RESYNC = "simulate_network_resync";

static const char* DEBUG_INGAME_RAIN_DISTANCE_MODIFIER_NAME = "ingame_rain_distance_modifier";

static const char* DEBUG_HIDE_ALL_PLAYERS = "hide_all_players";
static const char* DEBUG_DISABLE_UNIFORM_SCALING = "uniform_scaling_disabled";

static const char* DEBUG_INTERCHANGE_STATE_DISPLAY_NAME = "show_interchange_state";

static const char* DEBUG_FORCE_SCORE_DISPLAY_NAME = "force_score_50_0";
static const char* DEBUG_FORCE_TIMEOUT_DISPLAY_NAME = "force_timeout";
static const char* DEBUG_RUN_EXTRA_TIME_DISPLAY_NAME = "run_extra_time";

static const char* FORCE_SITUATION_NAMES[] = {
	"lineout",
	"conversion",
	"kickoff",
	"dropout"
};

static const char* CAMERA_DEBUG_LEVELS[] = {
	"none",
	"text",
	"active",
	"all",
	"trails"
};

static const char* GTB_DEBUG_LEVELS[] = {
	"none",
	"text",
	"all"
};


//static const char* DEBUG_SAFE_ZONE_NAME = "safe zone size";
//static const char* DEBUG_FULL_MODE_NAME = "full mode";

// Constructor - register named values.
RUGameDebugSettings::RUGameDebugSettings( /*SIFGameWorld* ggame*/ )
//: game( ggame )
: attack_disabled( false )
, defence_disabled( false )
, tackle_disabled( false)
, ruged_debug_disabled( false )
, ai_disabled( false )
, switching_enable_disable_tackle( false )
, switching_attacking_team( false )
, give_pro_ball( false )
, setplay_debug_enabled( false )
, set_ball_holder(-1)
, last_ball_holder(-1)
, gtb_debug_level(0)
, camera_level(0)
, set_situation(0)
, last_set_situation(0)
, game_state_visible( false )
, player_state_visible( false )
, player_waypoints_visible( true )
, team_state_visible( false )
, collidables_visible( false )
, origins_visible( false )
, att_formations_visible( false )
, def_formations_visible( false )
, all_formations_visible( false )
, pass_view_debug( false )
, intercept_view_debug( false )
, slot_view_debug( false )
, excitement_view_debug( false )
, participation_level_debug( false )
, field_lines_debug( false )
, off_side_players_visible( false )
, teams_all_offside( false )
, bfi_visible( false )
, advance_testbed( false )
, replay_cams_enabled( false )
, cutscene_state_visible( false )
, interchange_state_visible( false )
, post_effects_manager_enabled( true )
, label_display_mode(DEBUG_LDM_WP)
, axes( 0 )
, lines( 0 )
, texts( 0 )
, rectangles( 0 )
, foreground( 1.0f, 1.0f, 1.0f, 1.0f )
, background( 0.0f, 0.3f, 0.3f, 0.2f )
, input_action_debug(false)
, gestures_debug(false)
, throwin_debug(false)
, lineout_catch_debug(false)
, lineout_slowmotion_debug(false)
, lineout_repeat_forced(false)
, ruck_disable_ai(false)
, ball_holder_debug(false)
, fbg_receiver_debug(false)
, fbg_attacker_debug(false)
, actor_input_vector_acceleration_time( INPUT_VECTOR_ACCEL_TIME_TO_MAX )
, update_count( 0 )
, update_freq( 30 )
, current_page( DP_ROLE_ASSIGNMENT )
, current_setplay (0)
, setplay_restart (false)
, ruged_paused (false)
, tutorial_demo_record(false)
, tutorial_demo_save(false)
, tutorial_demo_playback(false)
, tutorial_demo_export(false)
, crowd_enabled(true)
, crowd_flags_enabled(true)
, crowd_camera_flashes_enabled(true)
, crowd_density(50)
, context_help_disabled(false)
, simulate_network_resync(false)
, cutscene_mode(DC_PLACEHOLDER)
, ingame_rain_distance_modifier(0.5f)
, hide_all_players(false)
, disable_uniform_scaling(false)
, force_timeout(false)
, force_score_50_0(false)
, run_extra_time(false)
, debug_game_mode(false)
, game_mode(0)
#ifdef ENABLE_PRO_MODE
, debug_pro_mode(false)
, pro_mode_enabled(false)
#endif
{
//	MABASSERT( game != NULL );
	//#rc3_legacy SIFDebug::GetDebugMenu()->AddMenu(DEBUG_MENU_NAME_GAME, this, DEBUG_MENU_PATH);
	InitializePresetOptions();

	/// Set debug string options
	for( int i = 0; i < DP_LAST; i++ )
	{
		debug_string_pages[i].append = false;
		debug_string_pages[i].page_size = -1;
		debug_string_pages[i].page_name = MabString( 32, "Team Debug Page %d", i );
	}
	
	// Pass page setup for append
	debug_string_pages[DP_ROLE_ASSIGNMENT].append = false;
	debug_string_pages[DP_ROLE_ASSIGNMENT].page_size = 30;
	debug_string_pages[DP_ROLE_ASSIGNMENT].page_name = "Role Assignment";

	debug_string_pages[DP_RULES].append = true;
	debug_string_pages[DP_RULES].page_size = 30;
	debug_string_pages[DP_RULES].page_name = "Rules";

	debug_string_pages[DP_PASS].append = true;
	debug_string_pages[DP_PASS].page_size = 20;
	debug_string_pages[DP_PASS].page_name = "Pass Debug Page";

	debug_string_pages[DP_KNOCKON].append = true;
	debug_string_pages[DP_KNOCKON].page_size = 45;
	debug_string_pages[DP_KNOCKON].page_name = "Knockon/Adv Debug Page";

	debug_string_pages[DP_CHANGE_PLAYER].append = true;
	debug_string_pages[DP_CHANGE_PLAYER].page_size = 45;
	debug_string_pages[DP_CHANGE_PLAYER].page_name = "Change Player Debug Page";

	debug_string_pages[DP_INTERCEPT].append = false;
	debug_string_pages[DP_INTERCEPT].page_size = 20;
	debug_string_pages[DP_INTERCEPT].page_name = "Intercept Debug Page";

	debug_string_pages[DP_ROLE_OPTIONS].append = true;
	debug_string_pages[DP_ROLE_OPTIONS].page_size = 45;
	debug_string_pages[DP_ROLE_OPTIONS].page_name = "Ball Holder Role Options";

	debug_string_pages[DP_BREAKDOWN].append = true;
	debug_string_pages[DP_BREAKDOWN].page_size = 20;
	debug_string_pages[DP_BREAKDOWN].page_name = "Breakdown Debug Page";

	debug_string_pages[DP_SHAME_AND_GLORY].append = true;
	debug_string_pages[DP_SHAME_AND_GLORY].page_size = 20;
	debug_string_pages[DP_SHAME_AND_GLORY].page_name = "Shame & Glory Debug Page";

	debug_string_pages[DP_TEAMSTATS].append = true;
	debug_string_pages[DP_TEAMSTATS].page_size = 30;
	debug_string_pages[DP_TEAMSTATS].page_name = "Team Stats Page";

	debug_string_pages[DP_PLAY_INFO].page_name = "Play Info";
	debug_string_pages[DP_PLAY_INFO].page_size = 45;
	debug_string_pages[DP_PLAY_INFO].append = true;

	debug_string_pages[DP_CROWD_REACTION].page_name = "Crowd Reaction";
	debug_string_pages[DP_CROWD_REACTION].page_size = 45;
	debug_string_pages[DP_CROWD_REACTION].append = true;

	debug_string_pages[DP_SCRUM].page_name = "Scrums";
	debug_string_pages[DP_SCRUM].page_size = 45;
	debug_string_pages[DP_SCRUM].append = true;

	// Pass page setup for append
	debug_string_pages[DP_TACKLE].page_size = 30;
	debug_string_pages[DP_TACKLE].page_name = "Tackle Debug Page";
/*
#if defined ENABLE_GAME_DEBUG_MENU
	SIFDebugDrawHandleManager* handle_mgr = SIFDebug::GetDebugDrawHandleManager();
	debug_key_pool = handle_mgr->AllocatePool(200);				// cleared each frame
#endif*/

	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_RECORD_DEMO), &tutorial_demo_record);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_PLAYBACK_DEMO), &tutorial_demo_playback);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_SAVE_DEMO), &tutorial_demo_save);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_GLOBAL_DEMO_EXPORT), &tutorial_demo_export);
}

//---------------------------------------------------------------------------
void RUGameDebugSettings::InitializePresetOptions()
{
#ifdef GESTURE_DEBUG
	gestures_debug = true;
#endif

#ifdef DEBUG_LINEOUT
	throwin_debug = true;
	lineout_catch_debug = true;
	SIFDebug::EnableDebugKeyProcessing (true);
	//lineout_slowmotion_debug = true;
#endif

#ifdef ENABLE_NO_AI_RUCK
	ruck_disable_ai = true;
#endif

#ifdef DISABLE_AI_DEFENCE
	ai_disabled = true;
#endif 
}

// Destructor - unregister from menu if we attached to it.
RUGameDebugSettings::~RUGameDebugSettings()
{
	//#rc3_legacy SIFDebug::GetDebugMenu()->RemoveMenu(DEBUG_MENU_NAME_GAME);

#if defined ENABLE_GAME_DEBUG_MENU
	//	debug_key_pool->ReleaseAll();				// THIS IS WRONG!! Try exiting the game properly using this code!
	//	MabMemDelete( debug_key_pool );
	// SIFDebugDrawHandleManager* handle_mgr = SIFDebug::GetDebugDrawHandleManager();
	// handle_mgr->ReleasePool(debug_key_pool);		// Handle manager handles the 'deleting' of pools.
#endif
}

// Provide the necessary MabCentral glue for serialization.
void RUGameDebugSettings::DefineMabCentralInterfaces()
{
	/* #rc3_legacy
	/// Define a type for the settings object
	MabObjectType settings_type = MabCentralTypeDatabase::DefineType( RUGameDebugSettings::RTTGetStaticClassName(), "SIF game debug options" );

	// define variables

	MabCentralTypeDatabase::DefineTypeLuaAttribute(settings_type, DEBUG_PAUSED, "bool", "GetPaused", "SetPaused");

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_GAME_STATE_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, game_state_visible));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_ATTACK_DISABLED_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, attack_disabled));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_DEFENCE_DISABLED_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, defence_disabled));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_TACKLE_DISABLED_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, tackle_disabled));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_DISABLE_RUGED_DEBUG_INFO, "bool", offsetof(RUGameDebugSettings, ruged_debug_disabled));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_DISABLE_AI_IN_GAME, "bool", offsetof(RUGameDebugSettings, ai_disabled));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_PLAYER_STATE_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, player_state_visible));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_PLAYER_WAYPOINTS_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, player_waypoints_visible));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_TEAM_STATE_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, team_state_visible));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_COLLIDABLES_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, collidables_visible));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_ORIGINS_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, origins_visible));

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_ATT_FORMATIONS_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, att_formations_visible));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_DEF_FORMATIONS_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, def_formations_visible));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_ALL_FORMATIONS_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, all_formations_visible));

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_INTERCHANGE_STATE_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, interchange_state_visible));

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_POSTEFFECTS_MANAGER_ENABLED_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, post_effects_manager_enabled));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_CAMERA_LEVELS_NAME, "int", offsetof(RUGameDebugSettings, camera_level));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_CAMERA_REPLAY_CAMS, "bool", offsetof(RUGameDebugSettings, replay_cams_enabled));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_GTB_LEVEL_NAME, "int", offsetof(RUGameDebugSettings, gtb_debug_level));

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_SETPLAYDEBUG_ENABLED_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, setplay_debug_enabled));

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_BALL_STATE_NAME, "bool", offsetof(RUGameDebugSettings, bfi_visible));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_PASS_DEBUG_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, pass_view_debug));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_INTERCEPT_DEBUG_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, intercept_view_debug));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_SLOT_DEBUG_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, slot_view_debug));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_EXCITEMENT_DEBUG_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, excitement_view_debug));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_PARTICIPATION_LEVEL_DEBUG_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, participation_level_debug));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_FIELD_LINES_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, field_lines_debug));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_OFFSIDE_PLAYERS_NAME, "bool", offsetof(RUGameDebugSettings, off_side_players_visible));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_OFFSIDE_TEAMS_NAME, "bool", offsetof(RUGameDebugSettings, teams_all_offside));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_ADVANCE_TEST_BED, "bool", offsetof(RUGameDebugSettings, advance_testbed));

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_PAGE_DISPLAY_NAME, "int", offsetof(RUGameDebugSettings, current_page));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_LABEL_MODE_DISPLAY_NAME, "int", offsetof(RUGameDebugSettings, label_display_mode));

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_BALL_HOLDER_DISPLAY_NAME, "int", offsetof(RUGameDebugSettings, set_ball_holder));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_FORCE_SITUATION_NAME, "int", offsetof(RUGameDebugSettings, set_situation));

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_ACTOR_INPUT_ACCEL_TIME, "float", offsetof(RUGameDebugSettings, actor_input_vector_acceleration_time));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_INPUT_ACTION_NAME, "bool", offsetof(RUGameDebugSettings, input_action_debug));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_GESTURES_NAME, "bool", offsetof(RUGameDebugSettings, gestures_debug));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_THROW_IN_NAME, "bool", offsetof(RUGameDebugSettings, throwin_debug));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_LINEOUT_CATCH_NAME, "bool", offsetof(RUGameDebugSettings, lineout_catch_debug));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_LINEOUT_SLOWMOTION_NAME, "bool", offsetof(RUGameDebugSettings, lineout_slowmotion_debug));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_LINEOUT_REPEAT_FORCED, "bool", offsetof(RUGameDebugSettings, lineout_repeat_forced));

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_RUCK_DISABLE_AI, "bool", offsetof(RUGameDebugSettings, ruck_disable_ai));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_BALLHOLDER_INFO, "bool", offsetof(RUGameDebugSettings, ball_holder_debug));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_FBG_RECEIVER, "bool", offsetof(RUGameDebugSettings, fbg_receiver_debug));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_FBG_ATTACKER, "bool", offsetof(RUGameDebugSettings, fbg_attacker_debug));
	
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_TUTORIAL_DEMO_RECORD, "bool", offsetof(RUGameDebugSettings, tutorial_demo_record));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_TUTORIAL_DEMO_PLAYBACK, "bool", offsetof(RUGameDebugSettings, tutorial_demo_playback));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_TUTORIAL_DEMO_SAVE, "bool", offsetof(RUGameDebugSettings, tutorial_demo_save));	
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_TUTORIAL_DEMO_EXPORT, "bool", offsetof(RUGameDebugSettings, tutorial_demo_export));

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_CROWD_ENABLED_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, crowd_enabled));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_CROWD_CAMERA_FLASHES_ENABLED_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, crowd_camera_flashes_enabled));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_CROWD_FLAGS_ENABLED_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, crowd_flags_enabled));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_CROWD_DENSITY_DISPLAY_NAME, "int", offsetof(RUGameDebugSettings, crowd_density));

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_CONTEXT_HELP_DISABLED_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, context_help_disabled));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_SIMULATE_NETWORK_RESYNC, "bool", offsetof(RUGameDebugSettings, simulate_network_resync));

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_INGAME_RAIN_DISTANCE_MODIFIER_NAME, "float", offsetof(RUGameDebugSettings, ingame_rain_distance_modifier));

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_HIDE_ALL_PLAYERS, "bool", offsetof(RUGameDebugSettings, hide_all_players));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_DISABLE_UNIFORM_SCALING, "bool", offsetof(RUGameDebugSettings, disable_uniform_scaling));

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_FORCE_SCORE_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, force_score_50_0));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_FORCE_TIMEOUT_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, force_timeout));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_RUN_EXTRA_TIME_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, run_extra_time));
	
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_GAME_MODE_ENABLED_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, debug_game_mode));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_GAME_MODE_DISPLAY_NAME, "int", offsetof(RUGameDebugSettings, game_mode));

#ifdef ENABLE_PRO_MODE
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_PRO_MODE_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, debug_pro_mode));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_PRO_MODE_ENABLED_DISPLAY_NAME, "bool", offsetof(RUGameDebugSettings, pro_mode_enabled));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_PRO_ID_DISPLAY_NAME, "int", offsetof(RUGameDebugSettings, pro_id));
#endif
	*/
}

// Registers the Lua methods associated with this object.
void RUGameDebugSettings::RegisterLuaMethods()
{
	// Debug menu interface functions
	//#rc3_legacy_debug_lua
	/*MABLUA_METHOD_DEF1( DMSetPaused, void, RUGameDebugSettings, SetPaused, bool );
	MabLuaTypeDatabase::RegisterMethod( "SetPaused", "void", "bool", MabLuaMethodPtr::FromPTMF(&DMSetPaused) );
	MABLUA_METHOD_DEF( DMGetPaused, bool, RUGameDebugSettings, GetPaused );
	MabLuaTypeDatabase::RegisterMethod( "GetPaused", "bool", "void", MabLuaMethodPtr::FromPTMF(&DMGetPaused) );*/
}


void RUGameDebugSettings::RegisterMenuConstraints( SIFDebugMenu* debug_menu )
{
	//#rc3_legacy debug_menu->RegisterVariableEnum( RUGameDebugSettings::RTTGetStaticClassName(), DEBUG_FORCE_SITUATION_NAME, false, FORCE_SITUATION_NAMES );
	//#rc3_legacy debug_menu->RegisterVariableEnum( RUGameDebugSettings::RTTGetStaticClassName(), DEBUG_CAMERA_LEVELS_NAME, false, CAMERA_DEBUG_LEVELS );
	//#rc3_legacy debug_menu->RegisterVariableEnum( RUGameDebugSettings::RTTGetStaticClassName(), DEBUG_GTB_LEVEL_NAME, false, GTB_DEBUG_LEVELS );
}

void RUGameDebugSettings::InitialiseGame()
{
}

void RUGameDebugSettings::CleanupGame()
{
}


// Load RU's debug menu configuration from a string (Passed from RugEd).
void RUGameDebugSettings::LoadRUDebugConfigFromString(const MabString &config_string)
{
	//#rc3_legacy_debug_menu SIFDebug::GetDebugMenu()->LoadConfigFromString(this, config_string);
}

MabString RUGameDebugSettings::SaveRUDebugConfigToString()
{
	return MabString(""); //#rc3_legacy_debug_menu SIFDebug::GetDebugMenu()->SaveConfigToString(this);
}

void RUGameDebugSettings::LoadConfig()
{
	//#rc3_legacy_debug_menu SIFDebug::GetDebugMenu()->LoadConfig( this, DEBUG_MENU_PATH );
}

void RUGameDebugSettings::IncrementLabelDisplayMode()
{
	int new_mode = (int) label_display_mode + 1;
	if ( new_mode >= DEBUG_LDM_MAX ) new_mode = (DEBUG_LDM) 0;
	if ( new_mode < 0        ) new_mode = (DEBUG_LDM) (DEBUG_LDM_MAX - (int) 1);
	label_display_mode = (DEBUG_LDM) new_mode;
}

void RUGameDebugSettings::DecrementLabelDisplayMode()
{
	int new_mode = (int) label_display_mode - 1;
	if ( new_mode >= DEBUG_LDM_MAX ) new_mode = (DEBUG_LDM) 0;
	if ( new_mode < 0        ) new_mode = (DEBUG_LDM) (DEBUG_LDM_MAX - (int) 1);
	label_display_mode = (DEBUG_LDM) new_mode;
}

void RUGameDebugSettings::SetLabelDisplayMode( DEBUG_LDM new_mode )
{
	MABASSERT( new_mode > DEBUG_LDM_NONE && new_mode < DEBUG_LDM_MAX );
	label_display_mode = new_mode;
}

void RUGameDebugSettings::Update(SIFGameWorld *game)
{
#if defined ENABLE_OSD
	MABMEM_SET_TAG(MEMTAG_DEBUGA);

	// Make sure the debug page name cycles
	if ( current_page >= DP_LAST ) current_page = (DP_NUMBER) 0;
	if ( current_page < 0        ) current_page = (DP_NUMBER) (DP_LAST - (int) 1);

	if ( label_display_mode >= DEBUG_LDM_MAX ) label_display_mode = (DEBUG_LDM) 0;
	if ( label_display_mode < 0        ) label_display_mode = (DEBUG_LDM) (DEBUG_LDM_MAX - (int) 1);

	update_count++;
	
	Clear();
	UpdateGameState(game);
	UpdateTeamStats(game);
	UpdateTeamState(game);
	UpdatePlayerState(game);
	UpdateSetPlayDebug(game);
	UpdateBallHolderDebug(game);
	UpdateForceSituation(game);
	UpdateSwitchEnableDisableTackle(game);
	UpdateSwitchAttackingTeam(game);
	UpdateGiveProBallDebug(game);
	UpdateCrowd(game);
	UpdateContextHelp(game);
	UpdateSimulateNetworkResync(game);
	UpdateGameMode();
#ifdef ENABLE_PRO_MODE
	UpdateProMode();
#endif

	UpdateIngameRainModifier(game);

	if(force_timeout)
	{
		game->GetGameTimer()->ForceTimeout();
		force_timeout = false;
	}

	if(force_score_50_0)
	{
		RUStatisticsSystem *stats = SIFApplication::GetApplication()->GetStatisticsSystem();
		stats->ForceTeamScore(SIDE_A,50);
		stats->ForceTeamScore(SIDE_B,0);
		force_score_50_0 = false;
	}

	// force extra time.
	if(run_extra_time)
	{
		RUStatisticsSystem *stats = SIFApplication::GetApplication()->GetStatisticsSystem();
		stats->ForceTeamScore(SIDE_A,10);
		stats->ForceTeamScore(SIDE_B,10);

		SIFDebug::GetRulesDebugSettings()->ForceExtraTime();

		wwNETWORK_TRACE_JG("Skip Cutscene {%4d} %s", __LINE__, __FILE__);
		game->GetCutSceneManager()->SkipCutScene();
		game->GetCutSceneManager()->StartHalfTimeWalkOn();

		// Force the full time cutscene again
		game->GetEvents()->cutscene_full_time();

		run_extra_time = false;
	}

	if (field_lines_debug)
	{
		FieldExtents half_extents = game->GetSpatialHelper()->GetFieldExtents() * 0.5f;
		FieldExtents half_extents_excluding = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals() * 0.5f;
		FieldExtents half_extents_clamp = game->GetSpatialHelper()->GetClampFieldExtents();


		SETDEBUGLINE(SIFDebugDrawSingleFrameHandle(), FVector(half_extents.x, 0.05f, half_extents.y), FVector(half_extents.x, 0.05f, -half_extents.y), MabColour::Red, MabColour::Red);
		SETDEBUGLINE(SIFDebugDrawSingleFrameHandle(), FVector(-half_extents.x, 0.05f, half_extents.y), FVector(-half_extents.x, 0.05f, -half_extents.y), MabColour::Red, MabColour::Red);
		SETDEBUGLINE(SIFDebugDrawSingleFrameHandle(), FVector(-half_extents.x, 0.05f, half_extents.y), FVector(half_extents.x, 0.05f, half_extents.y), MabColour::Red, MabColour::Red);
		SETDEBUGLINE(SIFDebugDrawSingleFrameHandle(), FVector(-half_extents.x, 0.05f, -half_extents.y), FVector(half_extents.x, 0.05f, -half_extents.y), MabColour::Red, MabColour::Red);

		SETDEBUGLINE(SIFDebugDrawSingleFrameHandle(), FVector(-half_extents.x, 0.05f, half_extents_excluding.y), FVector(half_extents.x, 0.05f, half_extents_excluding.y), MabColour::Yellow, MabColour::Yellow);
		SETDEBUGLINE(SIFDebugDrawSingleFrameHandle(), FVector(-half_extents.x, 0.05f, -half_extents_excluding.y), FVector(half_extents.x, 0.05f, -half_extents_excluding.y), MabColour::Yellow, MabColour::Yellow);


		SETDEBUGLINE(SIFDebugDrawSingleFrameHandle(), FVector(half_extents_clamp.x, 0.05f, half_extents_clamp.y), FVector(half_extents_clamp.x, 0.05f, -half_extents_clamp.y), MabColour::Blue, MabColour::Blue);
		SETDEBUGLINE(SIFDebugDrawSingleFrameHandle(), FVector(-half_extents_clamp.x, 0.05f, half_extents_clamp.y), FVector(-half_extents_clamp.x, 0.05f, -half_extents_clamp.y), MabColour::Blue, MabColour::Blue);
		SETDEBUGLINE(SIFDebugDrawSingleFrameHandle(), FVector(-half_extents_clamp.x, 0.05f, half_extents_clamp.y), FVector(half_extents_clamp.x, 0.05f, half_extents_clamp.y), MabColour::Blue, MabColour::Blue);
		SETDEBUGLINE(SIFDebugDrawSingleFrameHandle(), FVector(-half_extents_clamp.x, 0.05f, -half_extents_clamp.y), FVector(half_extents_clamp.x, 0.05f, -half_extents_clamp.y), MabColour::Blue, MabColour::Blue);

	}
#endif
}

///-------------------------------------------------------------------------
/// Create a game situation (eg. lineout/conversion etc...)
///-------------------------------------------------------------------------

void RUGameDebugSettings::SetupSituation( MabString &situation_name, SIFGameWorld *game )
{
#if WITH_EDITOR && !UE_BUILD_SHIPPING
	RUGameState *game_state = game->GetGameState();

	if(situation_name=="lineout")
	{
		game_state->DebugForceLineOut();
	}
	else if(situation_name=="conversion")
	{
		game_state->DebugForceConversion();
	}
	else if(situation_name=="kickoff")
	{
		game_state->DebugForceKickOff();
	}
	else if(situation_name=="dropout")
	{
		game_state->DebugForceDropOut();
	}
	else if(situation_name=="scrum")
	{
		game_state->DebugForceScrum();
	}
	else if(situation_name=="free_kick")
	{
		game_state->DebugForceFreeKick();
	}
	else if(situation_name=="penalty_touch")
	{
		game_state->DebugForcePenaltyKickTouch();
	}
	else if(situation_name=="penalty_goal")
	{
		game_state->DebugForcePenaltyKickGoal();
	}
	else if(situation_name=="penalty_tap")
	{
		game_state->DebugForcePenaltyTapAndGo();
	}
	else if(situation_name=="penalty_awarded")
	{
		game_state->DebugForcePenaltyAwarded();
	}
#endif // WITH_EDITOR && !UE_BUILD_SHIPPING
}

///-------------------------------------------------------------------------
/// 
///-------------------------------------------------------------------------

void RUGameDebugSettings::UpdateForceSituation(SIFGameWorld *game)
{
	if(set_situation!=last_set_situation)
	{
		MabString name = FORCE_SITUATION_NAMES[set_situation];
		SetupSituation(name,game);
	}
	last_set_situation = set_situation;
}

///-------------------------------------------------------------------------
/// Switch enable/disable tackle
///-------------------------------------------------------------------------

void RUGameDebugSettings::UpdateSwitchEnableDisableTackle(SIFGameWorld * /*game*/)
{
	if( !switching_enable_disable_tackle )
		return;

	switching_enable_disable_tackle = false;

	if (tackle_disabled)
	{
		MABLOGDEBUG("Tackle enabled.");
	}
	else
	{
		MABLOGDEBUG("Tackle disabled.");
	}

	tackle_disabled = !tackle_disabled;
}

///-------------------------------------------------------------------------
/// Switch attacking team
///-------------------------------------------------------------------------

void RUGameDebugSettings::UpdateSwitchAttackingTeam(SIFGameWorld *game)
{
	if( !switching_attacking_team )
		return;

	switching_attacking_team = false;

	RUGameState* game_state = game->GetGameState();
	if( game_state->GetPhase() != RUGamePhase::PLAY )
		return;

	RUTeam* defending_team = game_state->GetDefendingTeam();
	if( !defending_team )
		return;
	const SIFRugbyCharacterList& defending_players = defending_team->GetPlayers();
	int player_index = MabMath::RandInt((int)defending_players.size());
	game_state->SetBallHolder(defending_players[player_index]);
}

void RUGameDebugSettings::UpdateGiveProBallDebug(SIFGameWorld *game)
{
	RUCareerModeManager *cman = SIFApplication::GetApplication()->GetCareerModeManager();
	if(cman->IsActive() && cman->GetIsCareerModePro())
	{
		if( !give_pro_ball )
			return;

		RUGameState* game_state = game->GetGameState();
		if( game_state->GetPhase() != RUGamePhase::PLAY )
			return;

		game_state->SetBallHolder(SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer());
	}

	give_pro_ball = false;
}

///-------------------------------------------------------------------------
/// Update the ball holder 'override' - clears roles + actions of all players
/// (done when set_ball_holder changes).
/// Also resets the camera to 'prefered' default camera and sets the game phase
/// to RUGamePhase::PLAY.
///-------------------------------------------------------------------------

void RUGameDebugSettings::UpdateBallHolderDebug(SIFGameWorld *game)
{
	const SIFRugbyCharacterList& players = game->GetPlayers();

	if ( set_ball_holder < -1)	set_ball_holder = (int)(players.size()-1);
	if ( set_ball_holder >= (int)players.size())  set_ball_holder = -1;

	if(set_ball_holder!=last_ball_holder && set_ball_holder!=-1)
	{
		ARugbyCharacter* player = players[set_ball_holder];
		RUGameState *game_state = game->GetGameState();

		game->GetRNG()->SetAssertIfUsed(false);

#if WITH_EDITOR && !UE_BUILD_SHIPPING
		game_state->DebugClearRolesAndActions();
#endif // WITH_EDITOR && !UE_BUILD_SHIPPING

		game_state->SetBallHolder(player,true);
		game_state->SetAttackingTeam(player->GetAttributes()->GetTeam());
		game_state->SetPhase(RUGamePhase::PLAY);

		game->GetRNG()->SetAssertIfUsed(true);
	}

	last_ball_holder = set_ball_holder;
}

///-------------------------------------------------------------------------
/// Set the ball holder (actual set is done in UpdateBallHolderDebug)
///-------------------------------------------------------------------------

void RUGameDebugSettings::SetBallHolder( int new_value)
{
	set_ball_holder = new_value;
	last_ball_holder = -1;
}

///-------------------------------------------------------------------------
/// Set pause for RugEd: Had to separate due to normal pause dependency on flow nodes.
///-------------------------------------------------------------------------

void RUGameDebugSettings::SetRugEdPaused(bool paused)
{
	ruged_paused = paused;
}

bool RUGameDebugSettings::GetRugEdPaused()
{
	return ruged_paused;
}


///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameDebugSettings::SetPaused( bool paused)
{
	SIFGamePauseState* pause_state = SIFApplication::GetApplication()->GetMatchPauseState();
	pause_state->PauseGame( paused, SIFGamePauseState::PAUSE_DEBUG );
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

bool RUGameDebugSettings::GetPaused()
{
	SIFGamePauseState* pause_state = SIFApplication::GetApplication()->GetMatchPauseState();
	return pause_state->IsGamePaused();
}

///-------------------------------------------------------------------------
/// Draw the setplay debug menu/information
///-------------------------------------------------------------------------

void RUGameDebugSettings::UpdateSetPlayDebug(SIFGameWorld *game)
{
	MABUNUSED(game);
}

///-------------------------------------------------------------------------
/// Increment the current selected set play.
///-------------------------------------------------------------------------

void RUGameDebugSettings::IncrementSetPlay()
{
	if( setplay_debug_enabled )
	{
		current_setplay++;
	}
}

///-------------------------------------------------------------------------
/// Decrement the current selected set play.
///-------------------------------------------------------------------------

void RUGameDebugSettings::DecrementSetPlay()
{
	if( setplay_debug_enabled )
	{
		current_setplay--;
	}

}

///-------------------------------------------------------------------------
/// Start the currently selected set play.
///-------------------------------------------------------------------------

void RUGameDebugSettings::StartSetPlay()
{
	if( setplay_debug_enabled )
	{
		setplay_restart = true;
	}
}




void RUGameDebugSettings::UpdateGameState(SIFGameWorld *game)
{
#if defined ENABLE_OSD
	if ( game_state_visible && !game->GetTeams().empty() )
	{
		RUGameState *gamestate = game->GetGameState();

		Colour( MabColour::White, MabColour(0.0f, 0.0f, 0.4f, 0.2f) );
		Rectangle( 8 );
		//Print( "Sim Step: %d\n", game->GetSimulationTime().ToFrames() );
		Print( "%s\n", gamestate->GetPhaseName() );
		if ( game->GetGameState()->GetAttackingTeam() != NULL )
		{
			SSEVDSFormationManager* mgr = game->GetGameState()->GetAttackingTeam()->GetFormationManager();
			Print( "Attack : %s (%s)\n", mgr->GetCurrentFormationName(), mgr->GetCurrentFormationXDirection() == 1 ? "Left" : "Right" );
		}

		if ( game->GetGameState()->GetDefendingTeam() != NULL )
		{
			SSEVDSFormationManager* mgr = game->GetGameState()->GetDefendingTeam()->GetFormationManager();
			Print( "Defence: %s (%s)\n", mgr->GetCurrentFormationName(), mgr->GetCurrentFormationXDirection() == 1 ? "Left" : "Right" );
		}

		//Print( "Last Trigger    : %s\n", SSRuleControlUnit::last_trigger.c_str() );
		//Print( "Last Consequence: %s\n", SSRuleControlUnit::last_consequence.c_str() );
		
		if(game->GetNumTeams()>0)
			Print( "Confidence 0(On %s): %0.3f", gamestate->GetAttackingTeam() == game->GetTeam(0) ? "Attack " : "Defense", game->GetTeam(0)->GetConfidence() );	
		if(game->GetNumTeams()>1)
			Print( "Confidence 1(On %s): %0.3f", gamestate->GetAttackingTeam() == game->GetTeam(1) ? "Attack " : "Defense", game->GetTeam(1)->GetConfidence() );

		Print( "Difficulty: %d", game->GetGameSettings().difficulty + 1 );
		
		// Print the expected time until the ball comes back
		Print( "Est Time Till Play: %0.1f", gamestate->GetEstimatedTimeTillBallBackInPlay() );
		SSReplayManager *replay_manager = game->GetReplayManager();

		if( replay_manager )
		{
			if( replay_manager->GetManagerState() == SSReplayManager::PLAYBACK_STATE )
				Print( "Playback Progress: %0.3f / %0.3f", replay_manager->GetPlayBlockProgress(), replay_manager->GetPlayBlockDuration() );
			else
				Print( "Record Progress: %0.3f / %0.3f", replay_manager->GetRecordBlockProgress(), replay_manager->GetRecordBlockProgress() );
			MabString string;
			string.reserve( 128 );
			replay_manager->BuildDebugString(string);
			Print( string.c_str() );
		}
	}
#endif
}


#if defined ENABLE_OSD && defined ENABLE_GAME_DEBUG_MENU
static const char* PASS_PRIORITY_STRINGS[PASS_PRIORITY_LAST] = 
{
	"PRI_ROLE",
	"PRI_OVERLAP",
	"PRI_SPEED",
	"PRI_BRKTKL",
	"PRI_BUSTOVER",
	"PRI_FORWARD_SPEED",
	"PRI_LAST_PASS_DIR",
	"PRI_CLOSURE",
	"PRI_LINE_BREAK",
	"PRI_FORMATION",
	"PRI_PRO_REQUEST",
	"PRI_PRO_PLAYER",
	"PRI_INTO_OPEN"
};
#endif

void RUGameDebugSettings::UpdatePlayerState(SIFGameWorld *game)
{
#if defined ENABLE_OSD

	#if defined ENABLE_DEBUG_HEAP && defined ENABLE_GAME_DEBUG_MENU
	debug_key_pool->ReleaseAll();
	#endif

	if ( !player_state_visible || game->GetTeams().empty() )
		return;

	const SIFRugbyCharacterList& players = game->GetPlayers();
	MabColour col = foreground;

	// Iterate over all of the actors in the game
	for( size_t i = 0; i < players.size(); i++ )
	{
		ARugbyCharacter* player = players[i];
		SSRole* role = player->GetRole();
		col = foreground;

		#if defined ENABLE_DEBUG_HEAP && defined ENABLE_GAME_DEBUG_MENU
		if ( player_waypoints_visible )
		{
			// Draw 
			FVector facing_vector   = player->GetMovement()->GetCurrentPosition() + FVector::Z_AXIS * MabMatrix::RotMatrixY( player->GetMovement()->GetCurrentFacingAngle() );
			FVector movement_vector = player->GetMovement()->GetCurrentPosition() + player->GetMovement()->GetCurrentVelocity();
			FVector offset( 0.0f, 0.2f, 0.0f );

			SETDEBUGLINE(debug_key_pool->AllocateKey(), player->GetMovement()->GetCurrentPosition(), player->GetMovement()->GetTargetPosition(), MabColour::Red, MabColour::Blue);
			SETDEBUGLINE(debug_key_pool->AllocateKey(), player->GetMovement()->GetCurrentPosition() + offset, facing_vector + offset,   MabColour::Yellow, MabColour::Yellow);
			//SETDEBUGLINE(BASE_ID + (long)d++, player->GetMovement()->GetCurrentPosition() + offset, movement_vector + offset, MabColour::Green, MabColour::Green);

			// Offside line in Rucks
			/*if(game->GetGameState()->GetPhase() == RUGamePhase::RUCK)
			{
				FVector offSideLeft(-80.0f, 0.0f, 0.0f);
				FVector offSideRight(80.0f, 0.0f, 0.0f);
				SETDEBUGLINE(debug_key_pool->AllocateKey(), offSideLeft, offSideRight,   MabColour::Yellow, MabColour::Yellow);
			}*/
		}
		#endif		

		// select appropriate text for the label display mode
		MabString buffer;
		DEBUG_LDM display_mode = GetLabelDisplayMode();

		if( display_mode == DEBUG_LDM_BEHAVIOUR)
		{
			// behaviour role
			if ( role )
				buffer += MabString(128, "%s\n", role->GetShortClassName());
			else
				buffer += "NULL\n" ;

			// TEMP - Pass Priority
			//if ( player ) 
			//{
			//	RUBlackBoard& blackboard = game->GetTeam( player->GetAttributes()->GetTeamSide() )->GetBlackBoard();
			//	const MabNamedValue* val = blackboard.GetEntry( player, RUBB_ATTRIB_PASS_PRIORITY );
			//	if ( val )
			//		buffer += MabString("PP:%d",val->ToInt());
			//}
			
		}
		else if ( display_mode == DEBUG_LDM_ACTION_RUNNING ) 
		{
			if (role)
			{
				buffer += MabString( 128, "(%d): %s\n", player->GetAttributes()->GetIndex(), role->GetShortClassName() );
				int n_added = 0;
				// Iterate over all of the aggregates and see if they are running
				for( int i = 0; i < ACTION_LAST; i++ ) {
					if ( player->GetActionManager()->IsActionRunning( (RU_ACTION_INDEX) i ) ) {
						if ( n_added > 0 ) buffer += ",";
						buffer += player->GetActionManager()->GetAction( (RU_ACTION_INDEX) i )->GetShortClassName();
						n_added++;
					}
				}
				if ( n_added > 0 ) buffer += "\n";

				#ifdef ENABLE_ROLE_DEBUG_STRINGS
				buffer += role->GetDebugString();
				//buffer += " : ";
				buffer += "\n";
				#endif
			}
			else
			{
				buffer += MabString( 128, "(%d): NULL\n", player->GetAttributes()->GetIndex() );
			}
		} 		
		else if( display_mode == DEBUG_LDM_NAME && player )
		{
			RUPlayerAttributes *attribs = player->GetAttributes();

			// name
			//const SSDB_PLAYER* data = player->GetDbPlayer();
			buffer += MabString( 128, "%d:%s [%s]", attribs->GetIndex(), attribs->GetCombinedName().c_str(), PlayerPositionEnum::GetPlayerPositionTextAbbreviated( attribs->GetPlayerPosition() ) );
		}
		/*else if( this->GetLabelDisplayMode() == DEBUG_LDM_INPUT && player && role )
		{
#if 0
			//sprintf( buf[0], "%d:%s", i, role->GetRecognitionContextTemp() );
			if ( IsHumanControlled( player->GetIndex() ) ) {
				int hid = RLHumanPlayerManager::GetRLInstance()->GetHumanPlayerIDForPID(  player->GetIndex() );
				RLHumanPlayerData *data = RLHumanPlayerManager::GetRLInstance()->GetRLHumanPlayerData( hid );
				sprintf( buf[0], "%d:%s", i, data->GetRecognitionContext().c_str() );
				MabControlActionRecognitionManager &manager = data->GetRecognitionManager();
				const MabControlActionRecognitionManager::EventQueue *input_events = manager.GetEventState();
				MabControlActionRecognitionManager::EventQueue::const_iterator it;

				// Read all of the events and print them out
				int i = 1;
				for( it = input_events->begin(); it != input_events->end(); ++it ) {
					const MabControlActionRecognitionEvent &event = *it;
					sprintf( buf[i], "%s(%d) - %.3f", event.event_name.c_str(), event.is_master_event, MabTimeScaled::GetAbsoluteRealTime() - event.event_real_time );
					MABASSERT( i < MAX_LINES );
					if ( (i < MAX_LINES) )
						break;
					i++;
				}
			}
#endif
	#ifdef  ENABLE_RUN_OFF_SIDE_DEBUG
			int hf_locks = 0;
			int uf_locks = 0;
			int agg_enable = 0;
			int i;
			for( i = HF_MOVEMENT; i < HF_LAST; i++ )
			{
				if ( player_role->HFIsLocked( i ) )
					hf_locks |= (1 << i);
			}

			for( i = UF_DOMOTION; i <= UF_SETAGGRESSION; i++ ) {
				if ( player->UFIsLocked( i ) )
					uf_locks |= (1 << i);
			}


			for( i = ACTION_PASS_role; i <
				ACTION_role_MAX; i++ ) {
					if ( player->IsAggregateEnabled(
						i ) )
						agg_enable |= (1 << i);
				}

				bool motion_is_locked = false;
				if ( IsHumanControlled(
					player->GetIndex() ) ) {
						int hid = RLHumanPlayerManager::GetRLInstance()->GetHumanPlayerIDForPID(
							player->GetIndex() );
						RLHumanPlayerData *data = RLHumanPlayerManager::GetRLInstance()->GetRLHumanPlayerData( hid );
						motion_is_locked =
							data->GetLockPlayerMotion();
					}

					sprintf( buf[0], "L%d,HF%d,UF%d,AE%d",
						motion_is_locked, hf_locks, uf_locks, agg_enable );
					strcpy( buf[1],
						role->GetShortNameTemp() );			

			// which aggregates are running
			buf[2][0] = 0;
			int n_added = 0;
			// Iterate over all of the aggregates and see if they are running
				for( int i = 0; i < ACTION_role_MAX; i++												) {
					if ( actor->GetAggregate( i ) &&
						actor->GetAggregate( i )->IsRunning() ) {
							if ( n_added > 0 )
								strcat( buf[0], "," );
							strcat( buf[2],																actor->GetAggregate( i )->GetName().c_str() );
							n_added++;
						}
				}
	#endif
		}*/
		else if( display_mode == DEBUG_LDM_ANIM )
		{
			const int ANIM_STR_MAX_SIZE = 2048;
			char anim_buf[ANIM_STR_MAX_SIZE];

			/*#rc3_legacy
			player->GetComponent<NMMabAnimationNetwork>()->GetActiveNodeString( anim_buf, ANIM_STR_MAX_SIZE );
			*/
			//buffer += anim_buf;


			unsigned int null_terminate_pos = 0;
			int null_value = 10;
			for( unsigned int i = 0; i < 2048; ++i )
			{
				MabString temp = "ANIMATION";
				MabString temp2 = "";
				for(unsigned int test = i; test < i + temp.size(); ++test)
					temp2 += anim_buf[test];

				if( temp.compare( temp2 ) == 0 )
				{
					for( unsigned j = i; j > null_terminate_pos; --j)
					{
						int val1 = int(anim_buf[j]);
						if( val1 == null_value )
						{
							null_terminate_pos = j;

							for(unsigned int k = j; k < i + 1; ++k)
								buffer += anim_buf[k];
							break;
						}
					}
				}
			}
		}
		else if( this->GetLabelDisplayMode() == DEBUG_LDM_TIREDNESS )
		{
			RUPlayerAttributes* attribs = player->GetAttributes();
			const char* tired_levels[] = { "Buggered", "Tired", "Not tired", "TL_LAST" };			
			buffer += MabString(256, "%s\n%0.3f / %0.3f / %0.3f", 
				tired_levels[attribs->GetTiredness()],
				attribs->GetFatigue(),
				attribs->GetCappedMaxStamina(),
				attribs->GetMaxStamina());
		}
		//else if ( this->GetLabelDisplayMode() == DEBUG_LDM_ANIM2 )
		//{
		//	const int ANIM_STR_MAX_SIZE = 2048;
		//	char anim_buf[ANIM_STR_MAX_SIZE];

		//	player->GetComponent<NMMabAnimationNetwork>()->GetStateMachineString( anim_buf, ANIM_STR_MAX_SIZE );
		//	buffer += anim_buf;
		//	// animation
		//	//SSAnimationGraph* animation_graph = player->GetAnimation()->GetAnimationGraph();
		//	//MABASSERT( animation_graph != NULL );
		//	//buffer += MabString( 64, "(%d) %s\n", player->GetIndex(), StrAnimation(animation_graph) );
		//	//buffer += MabString( 64, "v=%.02f\n", animation_graph->GetVelocity() );
		//	//buffer += MabString( 64, "f=%.02f\n", animation_graph->GetFacingDelta() / 180.0f );
		//}
		/*else if( this->GetLabelDisplayMode() == DEBUG_LDM_CONTROL)
		{
			// control stats

			//int humanControlled = this->IsHumanControlled( i ) ? 1 :0;				
			//int controller = -1;
			//int hag = -1;
			//if( humanControlled )
			//{
			//	RLHumanPlayerData* data = RLHumanPlayerManager::GetRLInstance()->GetRLHumanPlayerDataByPID( i );
			//	controller = data->GetController();
			//}

			//if ( GetRLPlayer( i )->GetRLPlayerrole() )
			float base_max_speed = 0.0f;
			if ( player )
				base_max_speed = player->GetBaseMaxSpeed();

			buffer += MabString( 128, "%d:TS%.2f SP%.2f BS%.2f", i, player->GetTargetSpeed(), player->GetCurrentSpeed(), base_max_speed );
		}*/	
		else if( this->GetLabelDisplayMode() == DEBUG_LDM_WP)
		{			
			RUPlayerMovement *movement = player->GetMovement();
			//RUPlayerAttributes *attribs = player->GetAttributes();

			NMMabAnimationNetwork* animation_network = player->GetComponent<NMMabAnimationNetwork>();
/*
			if ( player->GetAttributes()->IsActive() )
			{
				MabCamera2* cam = player->GetGame()->GetCamera();
				float near_clip = cam->GetNearClip();
				MabCameraViewWindow view_wind = cam->GetViewWindow();
				float disttocam = (cam->GetEye() - player->GetTransform().GetTranslation()).Magnitude();
				float wnd_ht = ( view_wind.y * 2.0f * disttocam ) / near_clip;
				float wnd_wd = ( view_wind.x * 2.0f * disttocam ) / near_clip;

				float wnd_area = wnd_ht * wnd_wd;
				float player_area = 2.0f * 2.0f;

				float lod_calc1 = wnd_area / player_area;
				int lod = 0;
				if ( lod_calc1 > 20 )	lod = 1;
				if ( lod_calc1 > 50 )	lod = 2;
				buffer += MabString( 512,"d(%.2f), wnd(%.2f,%.2f,%.2f), calc(%.2f)\nLOD%d", disttocam, wnd_ht, wnd_wd, wnd_area, lod_calc1, lod );
				//SIF_DEBUG_DRAW( SetText( (long)player, player->GetMovement()->GetCurrentPosition(), dgb.c_str())  );
			}
*/

			buffer += MabString( 512, "\nROLE:%2d %s-%s\nMODE:%s\nSPD :%0.1f -> %0.1f\nWGHT:%0.1f from %0.1f, LOD%d", 
				player->GetAttributes()->GetIndex(), player->GetRole()->GetShortClassName(), 
			#ifdef ENABLE_ROLE_DEBUG_STRINGS
				player->GetRole()->GetDebugString().c_str(), 
			#else
				"--",
			#endif
				movement->GetMotionSource() != NULL ? "MotSrc" : "Locomotion", 
				movement->GetCurrentSpeed(), movement->GetTargetSpeed(),
				player->GetAnimation()->GetLocomotionWeight(), animation_network->GetNetwork()->GetMovementWeight(),
				animation_network->GetNetwork()->getAnimationSet() );

			//buffer += MabString( 512, "%2d %s\n%s",
			//	player->GetAttributes()->GetIndex(), player->GetRole()->GetShortClassName(), 
			//	movement->GetMotionSource() != NULL ? "MotSrc" : "Loc");

			int n_added = 0;
			// Iterate over all of the aggregates and see if they are running
			for( int i = 0; i < ACTION_LAST; i++ ) {
				if ( player->GetActionManager()->IsActionRunning( (RU_ACTION_INDEX) i ) ) {
					if ( n_added > 0 ) buffer += ","; else buffer += "\n";
					buffer += player->GetActionManager()->GetAction( (RU_ACTION_INDEX) i )->GetShortClassName();
					n_added++;
				}
			}
		}
		else if ( this->GetLabelDisplayMode() == DEBUG_LDM_URGENCY )
		{
			RUPlayerMovement *movement = player->GetMovement();

			if ( movement->GetMotionSource() == NULL )
			{
				col = MabMath::Lerp( MabColour::Yellow, MabColour::Red, player->GetState()->GetLastUrgency() );
				buffer += MabString( 512, "%s%d", game->GetGameState()->GetAttackingTeam() == player->GetAttributes()->GetTeam() ? "A" : "D", (int) (player->GetState()->GetLastUrgency() * 100.0f) );
			}

		}
		else if( this->GetLabelDisplayMode() == DEBUG_LDM_FORMATION)
		{			
			SSEVDSFormationManager* formation_manager = player->GetAttributes()->GetTeam()->GetFormationManager();
			SSRoleArea* area;
			if ( formation_manager && (area = formation_manager->GetPlayerArea(player)) != NULL ) {
				buffer += MabString( 256, "T%d I%d S%d\n%s\n%s", player->GetAttributes()->GetTeamIndex() + 1, player->GetAttributes()->GetIndex(), formation_manager->GetPlayerSlotIdx( player ), area->GetName(), role->GetShortClassName() );
			}

			//buffer += MabString( 128, "Nme: %s (%d)\nSpd: %0.2f (%.1f)\nAcl: %0.2f", player->GetAttributes()->GetLastName(), player->GetAttributes()->GetIndex(), player->GetAttributes()->GetSpeed(), player->GetMovement()->GetMaxSpeed(), player->GetAttributes()->GetAcceleration() );
		}
		/*else if( this->GetLabelDisplayMode() == DEBUG_LDM_POSITIONS && player )
		{
			// side, pid, position
			buffer += MabString( 128, "%c%d%s", player->GetTeamSide() == SIDE_A ? 'A' : 'B', player->GetIndex(),	GetPlayerPositionTextAbbreviated( player->GetPlayerPosition() ).c_str());
			//if( GetTeam( player->GetTeamSide() )->GetAssignedKicker() == player->GetIndex() )
			//	strcat(buf[0],":K");

			buffer += MabString( 128, role->RTTGetClassName() );

			// which aggregates are running
			// Iterate over all of the aggregates and see if they are running
			#pragma message("TODO: Enable Aggregate Status info")
			//for( int i = 0; i < ACTION_UPDATER_MAX; i++ ) {
			//	if ( actor->GetAggregate( i ) && actor->GetAggregate( i )->IsRunning() ) {
			//		if ( n_added > 0 )
			//			strcat( buf[0], "," );
			//		strcat( buf[2], actor->GetAggregate( i )->GetName().c_str() );
			//		n_added++;
			//	}
			}
		}*/
		else if ( display_mode == DEBUG_LDM_DEBUGSTRINGS && player ) 
		{
			// debug text as set by roles
			if ( role )
			{
				#ifdef ENABLE_ROLE_DEBUG_STRINGS
				buffer += MabString( 128, "%d: %s", player->GetAttributes()->GetIndex(), role->GetDebugString().c_str());
				#else
				buffer += MabString( 128, "%d:release build - no debug info", i );
				#endif
			}
			else
				buffer += MabString( 128, "%d:no role", player->GetAttributes()->GetIndex() );
		} 
		else if ( GetLabelDisplayMode() == DEBUG_LDM_LOOKAT_MODE ) 
		{
			// look at mode
			buffer += MabString( 128, "%d ", player->GetAttributes()->GetIndex() );
			if ( role )
				buffer += player->GetLookAt()->GetLookAtString();
			else
				buffer += MabString( 128, "no role" );
			
			//using namespace GamebryoTypeConverter;
			//Axis( AsMab(player->GetHeadJoint()->GetWorldTranslate()), AsMab(player->GetHeadJoint()->GetWorldTransform()), MabColour::Cyan );
			//Axis( AsMab(player->GetNeckJoint()->GetWorldTranslate()), AsMab(player->GetNeckJoint()->GetWorldTransform()), MabColour::Magenta );
		}
		else if ( GetLabelDisplayMode() == DEBUG_LDM_PSTATS && player ) 
		{
			// stats - currently only aggression
			//buffer += MabString( 128, "%0.2f", player->GetAggression() );

			buffer += MabString(128, "A%0.2f ST%0.2f C%0.2f", player->GetAttributes()->GetAggression(), player->GetAttributes()->GetStamina(), player->GetAttributes()->GetCappedMaxStamina() );
			//sprintf( buf[0], "A%0.2f SP%0.2f SR%d MS%0.2f MPS%0.2f CMPS%0.2f", player->GetAggression(), player->GetCurrentSpeed(), player->GetRLDBPlayer()->speed, player->GetMaxSpeed(), player->Player::GetMaxSpeed(), player->GetIdealSpeed( RLAS_SPRINT ) );
		} else if ( GetLabelDisplayMode() == DEBUG_LDM_PASS && player ) {
			// Display the pass priorities
			
			if ( player->GetAttributes()->GetTeam() == game->GetGameState()->GetAttackingTeam() )
			{
				buffer += MabString( 8, "%d", player->GetAttributes()->GetIndex() );
				ARugbyCharacter* bh = game->GetGameState()->GetBallHolder();
				if ( bh != NULL )
				{
					extern const char* fail_pass_reason;
					bool can_pass = game->GetStrategyHelper()->CanPassBallBetween( bh, player );
					col = can_pass ? foreground : MabColour::Orange;
					buffer += MabString( 32, "%-4.4s %0.1f\n", "CL", SSMath::GetXZClosureRate( bh->GetMovement()->GetCurrentPosition(), bh->GetMovement()->GetCurrentVelocity(), player->GetMovement()->GetCurrentVelocity(), FVector::ZeroVector ) );
					buffer += MabString( 32, "%-4.4s %s\n", "CP", fail_pass_reason );

					/// Draw lines for available offload arms
					RUActionManager* action_mgr = player->GetActionManager();
					if ( action_mgr->IsActionRunning( ACTION_TACKLEE ) )
					{
						OFFLOAD_META offload_meta;
						RUActionTacklee* tacklee_action = action_mgr->GetAction<RUActionTacklee>();
						tacklee_action->GetOffloadMeta( offload_meta );

						const FVector& pos = player->GetMovement()->GetCurrentPosition();
						FVector left_vec;
						SSMath::AngleToMabVector3( player->GetMovement()->GetCurrentFacingAngle() + PI * 0.5f, left_vec );
						const static float HEIGHT = 1.6f;

						if ( offload_meta.can_do[OA_LEFT] )
						{
							SIF_DEBUG_DRAW( Set3DLine( debug_key_pool->AllocateKey(), pos + FVector(0,HEIGHT,0), pos + FVector( +left_vec.x, HEIGHT, +left_vec.z ), MabColour::Green, MabColour::Green ) );
						}
						if ( offload_meta.can_do[OA_RIGHT] )
						{
							SIF_DEBUG_DRAW( Set3DLine( debug_key_pool->AllocateKey(), pos + FVector(0,HEIGHT,0), pos + FVector( -left_vec.x, HEIGHT, -left_vec.z ), MabColour::Red, MabColour::Red ) );
						}

						/// Display the offload likelihood from the team sliders
						float team_offload_likelyhood = 1.0f;
						RUTeam* team = player->GetAttributes()->GetTeam();
						TEAM_SLIDER_FIELD field_pos = game->GetSpatialHelper()->GetTeamDBFieldPosition( team, player->GetMovement()->GetCurrentPosition() );
						if ( player->GetAttributes()->GetPlayerPosition() & PP_FORWARD )
							team_offload_likelyhood = team->GetDbTeam().GetNormalisedForwardContactOffloadSlider( field_pos );
						else
							team_offload_likelyhood = team->GetDbTeam().GetNormalisedBackContactOffloadSlider( field_pos );

						buffer += MabString( 32, "%-4.4s %0.1f%%\n", "TO", team_offload_likelyhood * 100.0f );
					}

					int n_added = 0;
					// Iterate over all of the aggregates and see if they are running
					for( int i = 0; i < ACTION_LAST; i++ ) {
						if ( player->GetActionManager()->IsActionRunning( (RU_ACTION_INDEX) i ) ) {
							if ( n_added > 0 ) buffer += ",";
							buffer += player->GetActionManager()->GetAction( (RU_ACTION_INDEX) i )->GetShortClassName();
							n_added++;
						}
					}
					if ( n_added > 0 ) buffer += "\n";
				}

				if ( bh != player && bh )
				{
					PASS_META pass_meta;
					RUActionPass::GetPassMeta( bh, player, FVector::ZeroVector, pass_meta );
					MABUNUSED( pass_meta );
					buffer += MabString( 32, "%-4.4s %0.2f %0.2f\n", "TK", pass_meta.time_till_tackle_contact, pass_meta.pre_tackle_contact_pct );
				}

				if ( player == bh || player->GetRole()->RTTGetType() == RURoleRuckScrumHalf::RTTGetStaticType() )
				{
					for( size_t g = 0; g < 2; g++ )
					{
						int n_pass_inclusion_roles;
						SIFRugbyCharacterList receivers;
						int dir = g == 0 ? -1 : 1;
						RUActionPass::CalculateReceiversInPassDirection( player, dir, receivers, n_pass_inclusion_roles, true, debug_key_pool );
					}
				}

				// Apparently the PASS_PRIORITY_STRINGS array breaks on PS3, so no pass debugging there.
				int* pass_priorities = player->GetState()->GetPassPriorities();
				int sum = 0;

				for( int i = 0; i < PASS_PRIORITY_LAST; i++ )
				{
					if ( pass_priorities[i] != 0 )
						buffer += MabString( 32, "%-8.8s %d\n", PASS_PRIORITY_STRINGS[i] + 4, pass_priorities[i] );

					sum += pass_priorities[i];
				}

				if ( sum != 0 )
					buffer += MabString( 32, "%-8.8s %d", "Total", sum );
				
				// Show the pass request level as well
				if(SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(player))
					buffer += MabString( 32, "\nPR: %0.2f", player->GetAttributes()->GetPassRequestLevel() );
			}
		} 
		else if ( GetLabelDisplayMode() == DEBUG_LDM_OFFSIDE && player ) {

				// Draw the offside indicator values for the player
				RUOffsideIndicator* indicator = player->GetComponent<RUOffsideIndicator>();
				col = indicator->GetOffsideValue() <= 0.0f ? foreground : MabColour::Orange;
				buffer += MabString( 128, "%d %s\n%0.2f", player->GetAttributes()->GetIndex(), OFFSIDE_REASON_STATE_STRINGS[ indicator->GetReason() ], indicator->GetOffsideValue() );
		}
		else if ( display_mode == DEBUG_LDM_ACTIONS_AVAILABLE ) 
		{
			SSHumanPlayer* human_player = NULL;
			human_player = player->GetHumanPlayer();

			if (role && human_player != NULL)
			{
				// Iterate over all of the aggregates and see if they are running
				for( int i = 0; i < ACTION_LAST; i++ ) 
				{
					if( player->GetActionManager()->GetAction((RU_ACTION_INDEX) i) != NULL )
					{
						buffer += player->GetActionManager()->GetAction( (RU_ACTION_INDEX) i )->GetShortClassName();

						if ( player->GetActionManager()->CanUseAction( (RU_ACTION_INDEX) i ) )
						{
							buffer += " - YES\n";
						}
						else
						{
							buffer += " - NO\n";
						}
					}
				}

#ifdef ENABLE_ROLE_DEBUG_STRINGS
				buffer += role->GetDebugString();
				//buffer += " : ";
				buffer += "\n";
#endif
			}
		} 
		else if ( display_mode == DEBUG_LDM_UPDATER_FUNCTIONALITY_AVAILABLE ) 
		{
			SSHumanPlayer* human_player = NULL;
			human_player = player->GetHumanPlayer();
			if (human_player != NULL)
			{
				// Iterate over all of the aggregates and see if they are running
				for( int i = 0; i < UF_LAST; i++ ) 
				{
#if defined ENABLE_GAME_DEBUG_MENU
					buffer += UPDATER_FUNCTIONALITY_STRINGS[i];
#endif

					if ( player->GetActionManager()->HFIsLocked( (UPDATER_FUNCTIONALITY) i ) )
					{
						buffer += " - YES\n";
					}
					else
					{
						buffer += " - NO\n";
					}
				}
			}
		} 
		else if ( display_mode == DEBUG_LDM_HUMAN_FUNCTIONALITY_AVAILABLE ) 
		{
			SSHumanPlayer* human_player = NULL;
			human_player = player->GetHumanPlayer();
			if (human_player != NULL)
			{
				// Iterate over all of the aggregates and see if they are running
				for( int i = 0; i < HF_LAST; i++ ) 
				{
#if defined ENABLE_GAME_DEBUG_MENU
					buffer += UPDATER_FUNCTIONALITY_STRINGS[i];
#endif

					if ( player->GetActionManager()->HFIsLocked( (HUMAN_FUNCTIONALITY) i ) )
					{
						buffer += " - YES\n";
					}
					else
					{
						buffer += " - NO\n";
					}
				}
			}
		} 
		
		//MABLOGDEBUG("%s", buffer.c_str());
		
		Print(player, col, "%s", buffer.c_str());

		/*MabString test;

		if(SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(player))
			test  += MabString(128, "\n\n\n PR: %0.2f", player->GetAttributes()->GetPassRequestLevel());
		else
			test  += MabString(128, "\n\n\n PR: n/a");

		Print(player, col, "%s", test.c_str());*/
	}
#endif
}

const char* GetFieldPosStr( TEAM_SLIDER_FIELD field_pos )
{
	switch( field_pos )
	{
	case TEAM_SLIDER_ATT: return "ATT"; break;
	case TEAM_SLIDER_DEF: return "DEF"; break;
	case TEAM_SLIDER_MID: return "MID"; break;
	default:
		return "???";
		break;
	}
}

/// Update Team Statistics page
void RUGameDebugSettings::UpdateTeamStats(SIFGameWorld *game)
{
	static RUTeam* TEAM_0 = NULL;
	static RUTeam* TEAM_1 = NULL;
	static TEAM_SLIDER_FIELD TEAM_0_FIELD_POS = TEAM_SLIDER_ATT;
	static TEAM_SLIDER_FIELD TEAM_1_FIELD_POS = TEAM_SLIDER_DEF;

	RUTeam* team_0_now = game->GetTeam(0);
	RUTeam* team_1_now = game->GetTeam(1);
	TEAM_SLIDER_FIELD team_0_field_pos_now = game->GetSpatialHelper()->GetTeamDBFieldPosition( team_0_now, game->GetBall()->GetCurrentPosition() );
	TEAM_SLIDER_FIELD team_1_field_pos_now = game->GetSpatialHelper()->GetTeamDBFieldPosition( team_1_now, game->GetBall()->GetCurrentPosition() );

	if ( team_0_now != TEAM_0 || team_1_now != TEAM_1 || (team_0_field_pos_now != TEAM_0_FIELD_POS) || (team_1_field_pos_now != TEAM_1_FIELD_POS) )
	{
		TEAM_0 = team_0_now;
		TEAM_1 = team_1_now;
		TEAM_0_FIELD_POS = team_0_field_pos_now;
		TEAM_1_FIELD_POS = team_1_field_pos_now;

		ClearDebugStrings( DP_TEAMSTATS );

		const RUDB_TEAM* db_team_0 = TEAM_0 ? &TEAM_0->GetDbTeam() : NULL;
		const RUDB_TEAM* db_team_1 = TEAM_1 ? &TEAM_1->GetDbTeam() : NULL;
		MABUNUSED( db_team_0 );
		MABUNUSED( db_team_1 );

		PushDebugString( game, DP_TEAMSTATS, MabString( 128, "%-20.20s %3s       %3s", "", TEAM_0 ? GetFieldPosStr( TEAM_0_FIELD_POS ) : "", TEAM_1 ? GetFieldPosStr( TEAM_1_FIELD_POS ) : "" ).c_str() );
		PushDebugString( game, DP_TEAMSTATS, MabString( 128, "%-20.20s %3s       %3s", "", TEAM_0 ? TEAM_0->GetDbTeam().GetMnemonic() : "", TEAM_1 ? TEAM_1->GetDbTeam().GetMnemonic() : "" ).c_str() );
		#define TEAMSTAT( Stat ) PushDebugString( game, DP_TEAMSTATS, MabString( 128, "%-20.20s %3d       %3d", #Stat,\
			(int)((db_team_0 ? db_team_0->GetNormalised##Stat() : -1.0f) * 100.0f),\
			(int)((db_team_1 ? db_team_1->GetNormalised##Stat() : -1.0f) * 100.0f) ).c_str()\
		)
		#define TEAMSTATFP( Stat ) PushDebugString( game, DP_TEAMSTATS, MabString( 128, "%-20.20s %3d       %3d", #Stat,\
			(int)((db_team_0 ? db_team_0->GetNormalised##Stat( TEAM_0_FIELD_POS ) : -1.0f) * 100.0f),\
			(int)((db_team_1 ? db_team_1->GetNormalised##Stat( TEAM_1_FIELD_POS ) : -1.0f) * 100.0f) ).c_str()\
		)

		TEAMSTAT( WillingnessToAttack );
		TEAMSTAT( Attack );
		TEAMSTAT( Defense );
		TEAMSTAT( Overall );
		TEAMSTAT( RuckAbility );
		TEAMSTAT( MaulAbility );
		TEAMSTAT( ScrumAbility );
		TEAMSTAT( LineoutAbility );
		TEAMSTAT( RunKickSlider );
		TEAMSTAT( LineoutQuicknessSlider );
		TEAMSTAT( RuckCommitmentSlider );
		TEAMSTAT( DefensiveLineSpeedSlider );

		TEAMSTATFP( ForwardPassDriveSlider );
		TEAMSTATFP( ForwardContactOffloadSlider );
		TEAMSTATFP( BackPassKickSlider );
		TEAMSTATFP( BackContactOffloadSlider );
	}
}

void RUGameDebugSettings::UpdateTeamState(SIFGameWorld *game)
{
#if defined ENABLE_OSD
	if ( !team_state_visible || game->GetTeams().empty() )
		return;

	/// Display the current page
	StringPage &page = debug_string_pages[ current_page ];
	
#ifdef SET_SCREEN_WIDTH
	float x = SET_SCREEN_WIDTH / 2.0f;
#else
	float x = TOP;
#endif

	float y = TOP;

	// Print page header
	SIF_DEBUG_DRAW( SetText(DEBUG_TEXTS_MDD_KEY + texts++, x, y, page.page_name.c_str(), MabColour::White) );
	y += HEIGHT;
	SIF_DEBUG_DRAW( SetText(DEBUG_TEXTS_MDD_KEY + texts++, x, y, "----------------------------------", MabColour::White) );
	y += HEIGHT;

	for( long i = 0; i < (long)page.strings.size(); i++ ) 
	{
		StringEntry& dat = page.strings[i];
		SIF_DEBUG_DRAW( SetText(DEBUG_TEXTS_MDD_KEY + texts++, x, y, dat.string.c_str(), dat.col) );
		y += HEIGHT;
	}
#endif
}

void RUGameDebugSettings::UpdateCrowd(SIFGameWorld *game)
{
	//#rc3_legacy_debug _crowd
	/*ACrowdbase* crowd_manager = game->GetCrowdManager();

	if( !crowd_manager )
		return;

	if( crowd_enabled != crowd_manager->IsEnabled() )
	{
		crowd_manager->SetEnabled( crowd_enabled );
	}

	RUCrowdCameraFlashes* camera_flashes = crowd_manager->GetCameraFlashes();

	if( camera_flashes && crowd_camera_flashes_enabled != camera_flashes->IsEnabled() )
	{
		camera_flashes->SetEnabled( crowd_camera_flashes_enabled );
	}

	RUCrowdFlags* crowd_flags = crowd_manager->GetFlags();

	if( crowd_flags && crowd_flags_enabled != crowd_flags->IsEnabled() )
	{
		crowd_flags->SetEnabled( crowd_flags_enabled );
	}

	MabMath::Clamp( crowd_density, 0, 100 );

	if( static_cast<unsigned int>( crowd_density ) != crowd_manager->GetDensity() )
	{
		crowd_manager->SetDensity( crowd_density );
	}*/
	}

#ifdef ENABLE_PRO_MODE
void RUGameDebugSettings::UpdateGameMode()
{
	if(!debug_game_mode)
		return;

	RUGameSettings& gameSettings = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings();

	MabMath::Clamp( game_mode, 0, 1 );

	if( game_mode != (int)gameSettings.game_settings.GetGameMode() )
	{
		gameSettings.game_settings.SetGameMode(GAME_MODE::GAME_MODE_RU13); // Nick  WWS 7s to Womens // game_mode == 0 ? GAME_MODE_RU13 : GAME_MODE_RU13W );
	}
}
#endif

#ifdef ENABLE_PRO_MODE
void RUGameDebugSettings::UpdateProMode()
{
	RUCareerModeManager* proModeMan = SIFApplication::GetApplication()->GetCareerModeManager();

	if( !proModeMan->IsActive() )
		return;

	if( !proModeMan->GetIsCareerModePro() )
		return;

	RUGameSettings* gameSettings = SIFApplication::GetApplication()->GetMatchGameSettings();

	if( !gameSettings )
		return;

	if(!debug_pro_mode)
		return;

	if( pro_mode_enabled != gameSettings->game_settings.GetIsAProMode() )
	{
		gameSettings->game_settings.SetIsAProMode(pro_mode_enabled);
	}
		
	if( pro_id != proModeMan->GetProID() )
	{
		proModeMan->SetProID( pro_id );
	}
}
#endif

void RUGameDebugSettings::UpdateIngameRainModifier(SIFGameWorld* game_world)
{
	if (!game_world->GetCutSceneManager()->IsCinematicRunning())
	{
		game_world->GetWeatherManager()->SetRainDistanceModifier(ingame_rain_distance_modifier);
	}
}

void RUGameDebugSettings::UpdateContextHelp(SIFGameWorld *game)
{
	/// Commented out as nothing seems to be enabling it!
	MABUNUSED(game);
	//game->Get3DHudManager()->GetContextualHelper()->SetEnabled(context_help_disabled);
	//if ( game->Get3DHudManager()->GetContextualHelper()->SetEnabled() != context_help_disabled )
	//{
	//	game->Get3DHudManager()->GetContextualHelper()->SetEnabled( !context_help_disabled );
	//	game->Get3DHudManager()->GetContextualHelper()->Activate( !context_help_disabled );
	//}
}

#if 0 //#rc3_legacy
void RUGameDebugSettings::UpdateSimulateNetworkResync(SIFGameWorld* game)
{
	if(!simulate_network_resync)
		return;

	// Generate resync data
	MabStreamMemory stream_buffer(SIFHEAP_DYNAMIC);
	{
		SSStreamPacker stream_packer(stream_buffer);
		game->NetGenerateReSync(stream_packer);
		stream_buffer.Reset();
	}
	
	// Run resync
	{
		SSStreamPacker stream_packer(stream_buffer);
		game->NetLoadReSync(stream_packer);
		simulate_network_resync = false;
	}
}
#endif //#rc3_legacy

void RUGameDebugSettings::IncrementTeamPage()
{
	current_page = (DP_NUMBER) (current_page + 1);
}

/**
// Draw an axis.
//
// @param translation
//	The world space translation to apply to the axis.
//
// @param rotation
//	The world space rotation to apply to the axis.
//
// @param colour
//	The colour to draw the origin of the axis (blends out to blue, green, and 
//	red along the z, y, and x axes respectively).
*/
void RUGameDebugSettings::Axis( const FVector& translation, const MabQuaternion& rotation, const MabColour& colour )
{	
#if defined ENABLE_OSD
	SIF_DEBUG_DRAW( Set3DLine(DEBUG_AXES_MDD_KEY + axes++, translation, translation + rotation.Transform(FVector(0.0f, 0.0f, 0.2f)), colour, MabColour::Blue) );
	SIF_DEBUG_DRAW( Set3DLine(DEBUG_AXES_MDD_KEY + axes++, translation, translation + rotation.Transform(FVector(0.0f, 0.2f, 0.0f)), colour, MabColour::Green) );
	SIF_DEBUG_DRAW( Set3DLine(DEBUG_AXES_MDD_KEY + axes++, translation, translation + rotation.Transform(FVector(0.2f, 0.0f, 0.0f)), colour, MabColour::Red) );
#endif
}

/**
// Set the current foreground and background colours used for displaying debug
// text.
//
// @param fforeground
//	The foreground colour to set.
//
// @param bbackground
//	The background colour to set.
*/
void RUGameDebugSettings::Colour( const MabColour& fforeground, const MabColour& bbackground )
{
#if defined ENABLE_OSD
	foreground = fforeground;
	background = bbackground;
#endif
}

/**
// Draw a background rectangle that covers the next \e height number of lines.
//
// @param height
//	The number of lines to make the rectangle cover up.
*/
void RUGameDebugSettings::Rectangle( int height )
{
#if defined ENABLE_OSD
	++texts;
	SIF_DEBUG_DRAW( Set2DRect(DEBUG_RECTANGLES_MDD_KEY + rectangles, NULL, MabPoint<float>(LEFT - 2, TOP + texts * HEIGHT - HEIGHT / 4.0f), MabPoint<float>(RIGHT + 2, TOP + (texts + height) * HEIGHT + HEIGHT / 4.0f), background) );
	++rectangles;
#endif
}

/**
// Print to MabDebugDraw::SetText().
//
// @param format
//	A printf style format string that describes the text to display.
//
// @param ...
//	Parameters as specified by \e format.
*/
void RUGameDebugSettings::Print( const char* format, ... )
{
#if defined ENABLE_OSD
	MABASSERT( format != NULL );

	char buffer [2048];
	va_list args;
	va_start( args, format );
	int size = vsnprintf( buffer, sizeof(buffer), format, args );
	va_end( args );
	buffer[sizeof(buffer) - 1] = '\0';

	float x = LEFT;
	float y = TOP + texts * HEIGHT;
	const char* start = buffer;
	const char* end = buffer + size;
	while ( start != end )
	{
		const char* finish = std::find( start, end, '\n' );
		MabString text;
		text.assign( start, finish );
		SIF_DEBUG_DRAW( SetText(DEBUG_TEXTS_MDD_KEY + texts, x, y, text, foreground) );		
		++texts;
		y += HEIGHT;

		start = finish;
		while ( *start == '\n' )
		{
			++start;
		}
	}
#endif
}


/**
// Print at a world space position.
//
// @param position
//	The position in world space to print at.
//
// @param format
//	A printf style format string that describes the text to display.
//	
*/
void RUGameDebugSettings::Print( const FVector& position, const char* format, ... )
{
#if defined ENABLE_OSD
	MABASSERT( format != NULL );

	char buffer [2048];
	va_list args;
	va_start( args, format );
	vsprintf( buffer, format, args );
	va_end( args );
	buffer[sizeof(buffer) - 1] = '\0';

	SIF_DEBUG_DRAW( SetText( DEBUG_TEXTS_MDD_KEY + texts, position, buffer, foreground ) );
	++texts;
#endif
}


/**
// Print at the world space position of an SIFGameObject.
//
// @param player
//	The SIFGameObject to print the text at the world space position of (null will
//	be quietly ignored).
//
// @param format
//	A printf style format string that describes the text to display.
//	
*/
void RUGameDebugSettings::Print( const ARugbyCharacter* player, const MabColour& col, const char* format, ... )
{
#if defined ENABLE_OSD
	MABASSERT( format != NULL );

	if ( player != NULL )
	{
		char buffer [2048];
		va_list args;
		va_start( args, format );
		vsprintf( buffer, format, args );
		va_end( args );
		buffer[sizeof(buffer) - 1] = '\0';

		MabMatrix mtx;
		//#rc3_legacy PSSGMab::Set(mtx,player->GetGraphicsObject()->m_globalMatrix);

		SIF_DEBUG_DRAW( SetText( DEBUG_TEXTS_MDD_KEY + texts, mtx.GetTranslation(), buffer, col ) );
		++texts;
	}
#endif
}


/**
// Clear axes, rectangles, lines, and printed text.
*/
void RUGameDebugSettings::Clear()
{
#if defined ENABLE_OSD
	static const int MAX_AXES = 100;
	static const int MAX_LINES = 100;
	static const int MAX_TEXTS = 100;
	static const int MAX_RECTS = 100;
	if( update_count % update_freq == 0 ) {
		for ( int i = 0; i < MAX_AXES; ++i )
		{
			SIF_DEBUG_DRAW( Remove3DLine(DEBUG_AXES_MDD_KEY + i) );
		}

		for ( int i = 0; i < MAX_LINES; ++i )
		{
			SIF_DEBUG_DRAW( RemoveText(DEBUG_LINES_MDD_KEY + i) );
		}

		for ( int i = 0; i < MAX_TEXTS; ++i )
		{
			SIF_DEBUG_DRAW( RemoveText(DEBUG_TEXTS_MDD_KEY + i) );
		}

		for ( int i = 0; i < MAX_RECTS; ++i )
		{
			SIF_DEBUG_DRAW( Remove2DRect(DEBUG_RECTANGLES_MDD_KEY + i) );
		}
	}

	axes = 0;
	lines = 0;
	texts = 0;
	rectangles = 0;
#endif
}

void RUGameDebugSettings::ClearDebugStrings( DP_NUMBER page_no )
{
	#if defined ENABLE_OSD
	if (page_no == DP_LAST)
	{
		for (int i = 0; i < DP_LAST; i++)
			ClearDebugStrings((DP_NUMBER)i);
	}
	else
	{
		StringPage &page = debug_string_pages[page_no];
	
		page.strings.clear();
	}
	#endif
}

void RUGameDebugSettings::PushDebugString( SIFGameWorld *game, DP_NUMBER page_no, const char* format, ... )
{
	va_list ap;
	va_start(ap, format);
	PushDebugStringCol( game, page_no, MabColour::White, format, ap );
	va_end(ap);
}

void RUGameDebugSettings::IncrementDifficulty( SIFGameWorld* game )
{
	if ( game == NULL )
		return;

	RUGameSettings& settings = game->GetGameSettings();
	settings.difficulty = (DIFFICULTY) ((settings.difficulty + 1) % DIF_MAX);
}

/// Push paged debug strings in
void RUGameDebugSettings::PushDebugStringCol( SIFGameWorld *game, DP_NUMBER page_no, const MabColour& col, const char* format, ... )
{
	#if defined ENABLE_OSD
	StringPage &page = debug_string_pages[page_no];

	MabTime time_now = game->GetSimTime()->GetAbsoluteTime();
	if ( !page.append && page.last_update_time != time_now )
	{
		// Clear
		page.strings.clear();
	}
	page.last_update_time = game->GetSimTime()->GetAbsoluteTime();

	MABASSERT( format != NULL );

	char buffer [1024];
	va_list args;
	va_start( args, format );
	int size = vsnprintf( buffer, sizeof(buffer), format, args );
	va_end( args );
	buffer[size] = '\0';

	/// Add the string
	StringEntry dat;
	dat.string = buffer;
	dat.col = col;
	page.strings.push_back( dat );

	if ( page.append )
	{
		while( page.strings.size() > (size_t) page.page_size )
			page.strings.pop_front();
	}

	#endif
}

//const char* SSGameDebugSettings::StrAccumulationMode( int accumulation_mode ) const
//{
//	const char* accumulation_mode_string = "unknown";
//
//	switch ( accumulation_mode )
//	{
//		case ACCUMULATION_ALIGN_ROOT:
//			accumulation_mode_string = "align";
//			break;
//
//		case ACCUMULATION_ACCUMULATE_ROOT:
//			accumulation_mode_string = "accu";
//			break;
//
//		case ACCUMULATION_IGNORE_ROOT:
//			accumulation_mode_string = "igno";
//			break;
//
//		case ACCUMULATION_ANIMATE_ROOT:
//			accumulation_mode_string = "anim.";
//			break;
//
//		default:
//			MABBREAK();
//			break;
//	}
//
//	return accumulation_mode_string;
//}
//
//const char* SSGameDebugSettings::StrAnimation( SSAnimationGraph* animation_graph ) const
//{
//	return "none";
//}

#endif
