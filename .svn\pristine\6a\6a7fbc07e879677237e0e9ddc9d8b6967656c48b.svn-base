
#include "Rugby/Animation/RugbyAnimationStateMachine_UpperBodyActions.h"
#include "Rugby/Animation/RugbyAnimationStateMachine.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Animation/RugbyAnimationRecords.h"
#include "Character/RugbyCharacter.h"
#include "Character/RugbyCharacterAnimInstance.h"

#include <Runtime/Engine/Classes/Animation/AnimInstance.h>

//============================================================================================//============================================================================================
//============================================================================================//============================================================================================
//============================================================================================//============================================================================================

RugbyAnimationStateMachine_UpperBodyActions::RugbyAnimationStateMachine_UpperBodyActions(RugbyAnimationStateMachineMgr* pSMMgr, RUPlayerAnimation* pOwnerAnimation, ARugbyCharacter* pOwnerPlayer)
	: RugbyAnimationStateMachineBase(pSMMgr, pOwnerAnimation, pOwnerPlayer)
{
	InitialiseFullBodyPassingStateMachine(); //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing

	InitialiseUpperBodyActionStateMachine(); //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions	
}

//==============================================================================================================================================================

RugbyAnimationStateMachine_UpperBodyActions::~RugbyAnimationStateMachine_UpperBodyActions()
{

}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_UpperBodyActions::ClearMontageInstanceReferences(FAnimMontageInstance* pMontageInstance)
{
	if (nullptr == pMontageInstance)
	{
		return;
	}

	if (pMontageInstance == m_UBAction_MontageInstance.UBAction_MontageInstance && false == pMontageInstance->IsPlaying())
	{
		m_UBAction_MontageInstance.UBAction_MontageInstance = nullptr;
	}

	if ((pMontageInstance == m_UBPass_MontageInstance.UBPass_MontageInstance || pMontageInstance == m_UBPass_MontageInstance.m_pPassSecondaryInstance) && 
		false == pMontageInstance->IsPlaying())
	{
		m_UBPass_MontageInstance.Reset();
	}
}

//===============================================================================================================================================================
/*
player_cinematicRef|null|player_tacklesRef|tackle_blend|FeatherBlend2_2
sourceNodeA: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles
sourceNodeB: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing
weightParam: player_cinematicRef|null|player_tacklesRef|tackle_blend|smooth
*/
void RugbyAnimationStateMachine_UpperBodyActions::InitialiseFullBodyPassingStateMachine()
{
	m_FB_UBActionsPassStateMachine.AddState(ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough, this, &RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBPass_Update, &RugbyAnimationStateMachine_UpperBodyActions::OnEnter_nullPassThrough, &RugbyAnimationStateMachine_UpperBodyActions::OnExit_nullPassThrough);
	m_FB_UBActionsPassStateMachine.AddState(ERugbyAnim_Mode_UBActionsFBPass::emotional_reactions, this, &RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBPass_Update, &RugbyAnimationStateMachine_UpperBodyActions::OnEnter_emotional_reactions, &RugbyAnimationStateMachine_UpperBodyActions::OnExit_emotional_reactions);
	m_FB_UBActionsPassStateMachine.AddState(ERugbyAnim_Mode_UBActionsFBPass::lineout_throw, this, &RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBPass_Update, &RugbyAnimationStateMachine_UpperBodyActions::OnEnter_lineout_throw, &RugbyAnimationStateMachine_UpperBodyActions::OnExit_lineout_throw);
	m_FB_UBActionsPassStateMachine.AddState(ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving, this, &RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBPass_Update, &RugbyAnimationStateMachine_UpperBodyActions::OnEnter_long_pass_moving, &RugbyAnimationStateMachine_UpperBodyActions::OnExit_long_pass_moving);
	m_FB_UBActionsPassStateMachine.AddState(ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing, this, &RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBPass_Update, &RugbyAnimationStateMachine_UpperBodyActions::OnEnter_long_pass_standing, &RugbyAnimationStateMachine_UpperBodyActions::OnExit_long_pass_standing);
	m_FB_UBActionsPassStateMachine.AddState(ERugbyAnim_Mode_UBActionsFBPass::medium_pass, this, &RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBPass_Update, &RugbyAnimationStateMachine_UpperBodyActions::OnEnter_medium_pass, &RugbyAnimationStateMachine_UpperBodyActions::OnExit_medium_pass);
	m_FB_UBActionsPassStateMachine.AddState(ERugbyAnim_Mode_UBActionsFBPass::play_the_ball, this, &RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBPass_Update, &RugbyAnimationStateMachine_UpperBodyActions::OnEnter_playtheball, &RugbyAnimationStateMachine_UpperBodyActions::OnExit_playtheball);

	m_FB_UBActionsPassStateMachine.Initialise(ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough);
}

//===============================================================================================================================================================
/*
player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|FeatherBlend2_1

sourceNodeA: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|player_gameplayRef|full_body_actions
sourceNodeB: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions
weightParam: ControlParameters|one
*/

void RugbyAnimationStateMachine_UpperBodyActions::InitialiseUpperBodyActionStateMachine()
{
	m_UBActionsStateMachine.AddState(ERugbyAnim_Mode_UBActions::NullPassThrough, this, &RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBAction_Update, &RugbyAnimationStateMachine_UpperBodyActions::OnEnter_UBNullPassThrough, &RugbyAnimationStateMachine_UpperBodyActions::OnExit_UBNullPassThrough);
	m_UBActionsStateMachine.AddState(ERugbyAnim_Mode_UBActions::Catches, this, &RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBAction_Update, &RugbyAnimationStateMachine_UpperBodyActions::OnEnter_Catches, &RugbyAnimationStateMachine_UpperBodyActions::OnExit_Catches);
	m_UBActionsStateMachine.AddState(ERugbyAnim_Mode_UBActions::dummy_passes, this, &RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBAction_Update, &RugbyAnimationStateMachine_UpperBodyActions::OnEnter_dummy_passes, &RugbyAnimationStateMachine_UpperBodyActions::OnExit_dummy_passes);
	m_UBActionsStateMachine.AddState(ERugbyAnim_Mode_UBActions::fumbled_catches, this, &RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBAction_Update, &RugbyAnimationStateMachine_UpperBodyActions::OnEnter_fumbled_catches, &RugbyAnimationStateMachine_UpperBodyActions::OnExit_fumbled_catches);
	m_UBActionsStateMachine.AddState(ERugbyAnim_Mode_UBActions::passes, this, &RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBAction_Update, &RugbyAnimationStateMachine_UpperBodyActions::OnEnter_passes, &RugbyAnimationStateMachine_UpperBodyActions::OnExit_passes);

	m_UBActionsStateMachine.Initialise(ERugbyAnim_Mode_UBActions::NullPassThrough);
}

//===============================================================================================================================================================
#pragma optimize("", off)
void RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBPass_Update(float time)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_UpperBodyActions_StateMachine_UBPass_Update)

	if (m_bJustEnteredNullUBPass)
	{		
		if (m_pPlayer->GetAnimInstance() && m_UBPass_MontageInstance.UBPass_MontageInstance)
		{
			for (auto &MontageInt : m_pPlayer->GetAnimInstance()->MontageInstances)
			{
				if (MontageInt && (m_UBPass_MontageInstance.UBPass_MontageInstance == MontageInt) && m_UBPass_MontageInstance.UBPass_MontageInstance->IsActive()) //stop the animation if it's still playing....
				{
					UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_UpperBodyActions::OnEnter_nullPassThrough  Stopping Montage '%s'"), *m_pPlayer->GetName());
					m_UBPass_MontageInstance.UBPass_MontageInstance->Stop(m_ForceStopBlendOutTime);
					if (m_UBPass_MontageInstance.m_pPassSecondaryInstance)
					{
						m_UBPass_MontageInstance.m_pPassSecondaryInstance->Stop(m_ForceStopBlendOutTime);
					}
					break;
				}
			}
		}

		m_bJustEnteredNullUBPass = false;
		m_ForceStopBlendOutTime = SMDefaultBlendOutTime;
		m_UBPass_MontageInstance.Reset();
	}

	//check if we got something to process, if we do, just process the request and clearoff that name from the array		
	for (auto& RequestListIndex : m_pSMMgr->m_AnimationRequestNameState)
	{
		if (RequestListIndex.Value == false)
			continue;

		FString RequestName(RequestListIndex.Key);		
		wwDB_DTREQUESTTYPE_ENUM requestTypeEnum = NAME_TO_ENUM(wwDB_DTREQUESTTYPE_ENUM, TCHAR_TO_ANSI(*RequestName));

		switch (requestTypeEnum)
		{
			//LineOut Throw	
			//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|lineout_throw|ready_to_throw_throw_normal
			case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_THROW:
			{
				if ((m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::lineout_throw) &&
					(m_CurrentUBActionFBPassesSubType == UpperBodyActionFBPassSubTypes::ready_to_throw))
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::throw_normal;
					UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_UpperBodyActions::LINEOUT_THROW  '%s'"), *m_pPlayer->GetName());
					//durationInTime: 0.25
					m_FB_UBActionsPassStateMachine.ReInitialise(ERugbyAnim_Mode_UBActionsFBPass::lineout_throw);
					m_pSMMgr->ResetRequestListValue(RequestName);
				}
				//else
				//{
				//	UE_DEBUG_BREAK();
				//}
			}
			break;

			//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null1_lineout_throw
			case wwDB_DTREQUESTTYPE_ENUM::LINEOUT_THROWER:
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough)
				{
					//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|lineout_throw|raise_to_throw|lothrowraise
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::raise_to_throw;
					UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_UpperBodyActions::LINEOUT_THROWER  '%s'"), *m_pPlayer->GetName());
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::lineout_throw);
					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
			break;

			//#todo: Combine to groups instead of case for all...
			/*Medium Pass

			00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_left|MirrorTransforms1
			01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_forward|wbrmpf01
			02: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_right|Switch1
			03: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_r45|wbrmp45r01
			04: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_r135|wbrmp135r01
			05: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_behind|wbrmp180r01
			06: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_l45|MirrorTransforms1
			07: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_l135|MirrorTransforms1
			*/
			//Note: Below 3 nodes will never happen as ControlParameters|full_body_passing is always 1.
			//18: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass_null 
			//19: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass_null1
			//24: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_moving_null

			case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_BEHIND: //00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_passes10
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough ) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::medium_pass_behind;
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::medium_pass);
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}									
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_FORWARD: //02: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass_forward
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::medium_pass_forward;
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::medium_pass);
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}						
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_LEFT: //01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass_left
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::medium_pass_left;
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::medium_pass);
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}							
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_R135: //07: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass2
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::medium_pass_r135;
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::medium_pass);
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}						
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_R45: //05: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass1
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::medium_pass_r45;
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::medium_pass);
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}						
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_RIGHT: //03: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass_right
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::medium_pass_right;
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::medium_pass);
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}							
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_L45: //05: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::medium_pass_l45;
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::medium_pass);
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}						
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_L135: //08: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass3
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::medium_pass_l135;
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::medium_pass);
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}							
			}
			break;

				//Long Pass
				/*
				00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_moving|long_pass_left|MirrorTransforms1
				01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_moving|long_pass_forward|wbrslpf01
				02: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_moving|long_pass_right|wbrslp90r01
				03: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_moving|long_pass_r45|wbrslp45r01
				04: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_moving|long_pass_r135|wbrslp135r01
				05: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_moving|long_pass_behind|wbrslp180r01
				06: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_moving|long_pass_l45|MirrorTransforms1
				07: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_moving|long_pass_l135|MirrorTransforms1
				*/

				/*
				00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_standing|long_pass_right|MirrorTransforms1
				01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_standing|long_pass_forward|wblpf01
				02: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_standing|long_pass_left|wblpl01
				03: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_standing|long_pass_behind|wblpb01
				*/

			case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_BEHIND:
			{				
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::long_pass_behind;

					if (m_pRUPlayerAnimation->GetSpeedVariable()->getValue() > 0.5f) //17: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass15
					{
						m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving);
					}
					else
					{
						m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing); //23: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|Null_passes3
					}
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}							
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_FORWARD:
			{				
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::long_pass_forward;

					if (m_pRUPlayerAnimation->GetSpeedVariable()->getValue() > 0.5f) //15: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass13
					{
						m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving);
					}
					else
					{
						m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing); //21: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|Null_passes1
					}
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}						
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_L135: //09: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass8
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::long_pass_l135;
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving);
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_L45: //12: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass11
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::long_pass_l45;
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving);
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}							
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_LEFT:
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::long_pass_left;
					if (m_pRUPlayerAnimation->GetSpeedVariable()->getValue() > 0.5f) //16: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass14
					{
						m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving);
					}
					else
					{
						m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing); //22: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|Null_passes2
					}
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}							
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_R135: //10: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass9
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::long_pass_r135;
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving);
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}				
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_R45: //11: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass10
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::long_pass_r45;
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving);
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_RIGHT:
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::long_pass_right;
					if (m_pRUPlayerAnimation->GetSpeedVariable()->getValue() > 0.5f) //14: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass12
					{
						m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving);
					}
					else
					{
						m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing); //20: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|Null_passes
					}
					m_pSMMgr->ResetRequestListValue(RequestName);					
				}							
			}	
			break;

			case wwDB_DTREQUESTTYPE_ENUM::CELEBRATE:
			{	
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					if (m_pRUPlayerAnimation->GetWithBallStateVariable()->getValue() > 0.5f)
					{
						//34: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_emotional_reactions
						m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::celebration_standing_withball;
					}
					else if (m_pRUPlayerAnimation->GetSpeedVariable()->getValue() > 0.5f)
					{
						//26: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null1_emotional_reactions
						m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::celebration_jogging;
					}
					else
					{
						//30: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null1_emotional_reactions3
						m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::celebration_standing;
					}

					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::emotional_reactions);
					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::COMMISERATE:
			{		
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					if (m_pRUPlayerAnimation->GetSpeedVariable()->getValue() > 0.5f)
					{
						//27: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null1_emotional_reactions1
						m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::commiseration_jogging;
					}
					else
					{
						//29: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null1_emotional_reactions2
						m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::commiseration_standing;
					}
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::emotional_reactions);
					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
			break;

			//33: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|emotional_reactions_null
			case wwDB_DTREQUESTTYPE_ENUM::STOP_EMOTIONAL_REACTION:
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::emotional_reactions)
				{
					m_ForceStopBlendOutTime = 0.5f;
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough);
					m_pSMMgr->ResetRequestListValue(RequestName);
				}								
			}	
			break;
			
			case wwDB_DTREQUESTTYPE_ENUM::PLAY_THE_BALL_BEHIND:
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::play_the_ball_behind;
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::play_the_ball);
					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
			break;

			case wwDB_DTREQUESTTYPE_ENUM::PLAY_THE_BALL_GROUND:
			{
				if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //ControlParameters|full_body_passing is always 1, so not added here..
				{
					m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::play_the_ball_ground;
					m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::play_the_ball);
					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
			break;
			
			default:
			break;
		}
	}

	//#todo: Move this to OnUpdate emotional_reactions, once it's implemented
	if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::emotional_reactions) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|emotional_reactions_null1
	{
		const float TestFraction = 0.949999988079071f;
		EMontageCrossDurationData MontageStoppedData;
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction (m_UBPass_MontageInstance.UBPass_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData, TestFraction))
		{
			UAnimSequence* pAnimSeq = const_cast<UAnimSequence*> (URugbyCharacterAnimInstance::GetAnimSequenceFromMontageInstance(m_UBPass_MontageInstance.UBPass_MontageInstance));
			
			m_pPlayer->AnimationNotify(ERugbyAnimEvent::END_EMOTIONAL_REACTION_EVENT, 0, pAnimSeq, false); //fire an event as it has a valid startTrigger in the node.
			m_ForceStopBlendOutTime = 0.25f;
			m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough);
			UE_LOG(LogTemp, Display, TEXT("%s emotional_reactions '%s'"), *FString(__func__), *m_pPlayer->GetName());
		}		
	}
	else if ((m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving) || //13: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass1_null1
		(m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing) || //25: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_moving_null1
		(m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::medium_pass)) //04: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass_left_null
	{
		EMontageCrossDurationData MontageStoppedData;
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_UBPass_MontageInstance.UBPass_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			m_ForceStopBlendOutTime = SMDefaultPass2BlendInTime;
			m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough);
		}
	}
	else if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::play_the_ball)
	{
		EMontageCrossDurationData MontageStoppedData;
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_UBPass_MontageInstance.UBPass_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			m_ForceStopBlendOutTime = SMDefaultPass2BlendInTime;
			m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough);
		}
	}
	else if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::lineout_throw) 
	{
		EMontageCrossDurationData MontageStoppedData;
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_UBPass_MontageInstance.UBPass_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			if (m_CurrentUBActionFBPassesSubType == UpperBodyActionFBPassSubTypes::raise_to_throw) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|lineout_throw|raise_to_throw_ready_to_throw
			{
				m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::ready_to_throw; //loop animation
				//durationInTime: 0.25
				m_FB_UBActionsPassStateMachine.ReInitialise(ERugbyAnim_Mode_UBActionsFBPass::lineout_throw); //call the lineout throw with data ready to throw...
			}
			else if (m_CurrentUBActionFBPassesSubType == UpperBodyActionFBPassSubTypes::throw_normal) ////player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|lineout_throw_null1
			{
				m_ForceStopBlendOutTime = 0.25f;
				m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough);
			}
		}
	}
}//void RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBPass_Update(float time)
#pragma optimize("", on)
//============================================================================================

//All exits are dummy for now.

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnExit_nullPassThrough()
{
	m_bJustEnteredNullUBPass = false;
	m_ForceStopBlendOutTime = SMDefaultBlendOutTime;
	m_UBPass_MontageInstance.Reset();
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnExit_emotional_reactions()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnExit_lineout_throw()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnExit_long_pass_moving()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnExit_long_pass_standing()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnExit_medium_pass()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnEnter_nullPassThrough()
{	
	if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough)
	{
		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_UpperBodyActions::OnEnter_nullPassThrough for '%s'"), *m_pPlayer->GetName());
	}

	m_CurrentUBActionFBPasses = ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough;

	m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::Null;

	//if (m_pPlayer->GetAnimInstance() && m_UBPass_MontageInstance.UBPass_MontageInstance)
	//{
	//	for (auto &MontageInt : m_pPlayer->GetAnimInstance()->MontageInstances)
	//	{
	//		if (MontageInt && (m_UBPass_MontageInstance.UBPass_MontageInstance == MontageInt) && m_UBPass_MontageInstance.UBPass_MontageInstance->IsActive()) //stop the animation if it's still playing....
	//		{
	//			UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_UpperBodyActions::OnEnter_nullPassThrough  Stopping Montage '%s'"), *m_pPlayer->GetName());
	//			m_UBPass_MontageInstance.UBPass_MontageInstance->Stop(m_ForceStopBlendOutTime);
	//			break;
	//		}
	//	}
	//}

	//m_ForceStopBlendOutTime = SMDefaultBlendOutTime;
	//
	//m_UBPass_MontageInstance.Reset();

	m_bJustEnteredNullUBPass = true;
	
}
//============================================================================================

void RugbyAnimationStateMachine_UpperBodyActions::OnEnter_emotional_reactions()
{
	m_CurrentUBActionFBPasses = ERugbyAnim_Mode_UBActionsFBPass::emotional_reactions;
	
	const FRugbyUpperBodyActionsFBPassTypeRecord *AnimTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetFBPassTypeRec(ERugbyAnim_Mode_UBActionsFBPass::emotional_reactions);
	if (nullptr == AnimTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnter_emotional_reactions:TypePtr is null"));
		return;
	}
	TArray <FRugbyUpperBodyActionsFBPassAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : AnimTypePtr->m_animVariants)
	{
		if (m_CurrentUBActionFBPassesSubType == AnimRec->m_SubType)
		{
			tempRec.Add(AnimRec);
		}
	}

	UE_LOG(LogTemp, Display, TEXT("OnEnter_emotional_reactions->>> Subtype '%d'"), (int)m_CurrentUBActionFBPassesSubType);

	float BlendIn = 0.25f;
	PlaySelectedUB_FBPassAnimation(tempRec, BlendIn);
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnEnter_lineout_throw()
{
	m_CurrentUBActionFBPasses = ERugbyAnim_Mode_UBActionsFBPass::lineout_throw;
//	m_CurrentUBActionFBPassesSubType = Subtype;

	const FRugbyUpperBodyActionsFBPassTypeRecord *AnimTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetFBPassTypeRec(ERugbyAnim_Mode_UBActionsFBPass::lineout_throw);
	if (nullptr == AnimTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("StateMachine_UpperBody_FBPass_LineoutThrow:TypePtr is null"));
		return;
	}
	TArray <FRugbyUpperBodyActionsFBPassAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : AnimTypePtr->m_animVariants)
	{			
		if (m_CurrentUBActionFBPassesSubType == AnimRec->m_SubType)
		{
			tempRec.Add(AnimRec);
		}
	}

	UE_LOG(LogTemp, Display, TEXT("StateMachine_UpperBody_FBPass_LineoutThrow->>> Subtype '%d'"), (int)m_CurrentUBActionFBPassesSubType);

	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|lineout_throw|raise_to_throw_ready_to_throw
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|lineout_throw|ready_to_throw_throw_normal
	//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null1_lineout_throw
	float BlendIn = 0.25f; 
	PlaySelectedUB_FBPassAnimation(tempRec, BlendIn);
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnEnter_long_pass_moving()
{
	//Note: ControlParameters|full_body_passing is never set, so we will not use that.
	m_CurrentUBActionFBPasses = ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving;
	//m_CurrentUBActionFBPassesSubType = Subtype;

	const FRugbyUpperBodyActionsFBPassTypeRecord *AnimTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetFBPassTypeRec(ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving);
	if (nullptr == AnimTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("StateMachine_UpperBody_FBPass_LongPassMoving:TypePtr is null"));
		return;
	}
	TArray <FRugbyUpperBodyActionsFBPassAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : AnimTypePtr->m_animVariants)
	{
		if (m_CurrentUBActionFBPassesSubType == AnimRec->m_SubType)
		{
			tempRec.Add(AnimRec);
		}
	}

	UE_LOG(LogTemp, Display, TEXT("StateMachine_UpperBody_FBPass_LongPassMoving->>> Subtype '%d'"), (int)m_CurrentUBActionFBPassesSubType);
	PlaySelectedUB_FBPassAnimation(tempRec, SMDefaultPass2BlendInTime);
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnEnter_long_pass_standing()
{
	//Note: ControlParameters|full_body_passing is never set, so we will not use that.
	m_CurrentUBActionFBPasses = ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing;
	//m_CurrentUBActionFBPassesSubType = Subtype;

	const FRugbyUpperBodyActionsFBPassTypeRecord *AnimTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetFBPassTypeRec(ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing);
	if (nullptr == AnimTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("StateMachine_UpperBody_FBPass_LongPassStanding:TypePtr is null"));
		return;
	}
	TArray <FRugbyUpperBodyActionsFBPassAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : AnimTypePtr->m_animVariants)
	{
		if (m_CurrentUBActionFBPassesSubType == AnimRec->m_SubType)
		{
			tempRec.Add(AnimRec);
		}
	}

	UE_LOG(LogTemp, Display, TEXT("StateMachine_UpperBody_FBPass_LongPassStanding->>> Subtype '%d'"), (int)m_CurrentUBActionFBPassesSubType);
	PlaySelectedUB_FBPassAnimation(tempRec, SMDefaultPass2BlendInTime);
}
#pragma optimize("", off)
//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnEnter_medium_pass()
{
	//Note: ControlParameters|full_body_passing is never set, so we will not use that.
	m_CurrentUBActionFBPasses = ERugbyAnim_Mode_UBActionsFBPass::medium_pass;

	//m_CurrentUBActionFBPassesSubType = Subtype;

	const FRugbyUpperBodyActionsFBPassTypeRecord *AnimTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetFBPassTypeRec(ERugbyAnim_Mode_UBActionsFBPass::medium_pass);
	if (nullptr == AnimTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("StateMachine_UpperBody_FBPass_MediumPass:TypePtr is null"));
		return;
	}
	TArray <FRugbyUpperBodyActionsFBPassAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : AnimTypePtr->m_animVariants)
	{
		if (m_CurrentUBActionFBPassesSubType == AnimRec->m_SubType)
		{
			tempRec.Add(AnimRec);
		}
	}

	UE_LOG(LogTemp, Display, TEXT("StateMachine_UpperBody_FBPass_MediumPass->>> Subtype '%d'"), (int)m_CurrentUBActionFBPassesSubType);
	PlaySelectedUB_FBPassAnimation(tempRec, SMDefaultPass2BlendInTime);
}
#pragma optimize("", on)
//============================================================================================

void RugbyAnimationStateMachine_UpperBodyActions::StateMachine_Update(float delta)
{	
	QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_UpperBodyActions_StateMachine_StateMachine_Update)
	//m_FB_UBActionsPassStateMachine.Update(delta);
	//m_UBActionsStateMachine.Update(delta);
	
	if (m_pSMMgr->m_FBPass)
	{	
		if (*m_pSMMgr->m_FBPass == true)
		{
			m_FB_UBActionsPassStateMachine.Update(delta);
		}
		else
		{
			m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough);
		}
		//function: PlaySelectedUB_FBPassAnimation checks the tackle state and sets uppperbody/fullbody
	}	
	
	if (m_pSMMgr->m_UBActions)
	{		
		if (*m_pSMMgr->m_UBActions == true)
		{
			m_UBActionsStateMachine.Update(delta);
		}
		else
		{
			m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::NullPassThrough);
		}
		//function: PlaySelectedUB_ActionsAnimation checks the fullbody action state and sets uppperbody/fullbody
	}

	UpdateRemainingTransitionTime(delta);
	//ensureAlways(((*m_pSMMgr->m_FBPass == true) && (*m_pSMMgr->m_UBActions == true)) == false);
}

//============================================================================================

void RugbyAnimationStateMachine_UpperBodyActions::StateMachine_Reset()
{
	if (m_pPlayer && m_pPlayer->GetAnimInstance())
	{
		m_pPlayer->GetAnimInstance()->StopAnimationGroup(MONTAGE_GROUP::UPPER_BODY, 0.0f);
		m_pPlayer->GetAnimInstance()->StopAnimationGroup(MONTAGE_GROUP::PASS, 0.0f);
	}
	m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::NullPassThrough);
	m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough);
	m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::Null;
	m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::Null;	
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_UpperBodyActions::StateMachine_ReturnToNullState()
{
	m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::NullPassThrough);
	m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough);
	m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::Null;
	m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::Null;
}

//============================================================================================

void RugbyAnimationStateMachine_UpperBodyActions::StateMachine_HandleAnimationEvent(float time, ERugbyAnimEvent event, size_t userdata)
{	
	if (
		((userdata == 15) && ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) || //07: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_pass_left_null2
							(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::passes ))) //17: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|medium_pass_null1
		||
		((userdata == 13) && ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::fumbled_catches) || //02: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|fumbled_catches_null2
							(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))) //04: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|catches_null2
	   ) 
		{
			m_ForceStopBlendOutTime = 0.20000000298023224f;
			m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::NullPassThrough); //durationInTime: 0.20000000298023224????
		}	
}

//============================================================================================

//void RugbyAnimationStateMachine_UpperBodyActions::StateMachine_HandleAnimationEnd(FAnimMontageInstance* montageInst)
//{	
	//not used anymore
	//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough) //debug code
	//{
	//	UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_UpperBodyActions::StateMachine_HandleAnimationEnd:: Pass mode  '%d' for '%s'"), (int)m_CurrentUBActionFBPasses, *m_pPlayer->GetName());
	//	//if (m_UBPass_MontageInstance.UBPass_MontageInstance == nullptr) //debug code
	//	//	UE_DEBUG_BREAK();
	//}

	//
	//if (m_UBPass_MontageInstance.UBPass_MontageInstance == montageInst)
	//{
	//	m_UBPass_MontageInstance.UBPass_MontageInstance = nullptr;//set this UBPass_MontageInstance to null if it has stopped to avoid having any garbage value.

	//	if ( m_UBPass_MontageInstance.UBPass_MontageUniqueID == montageInst->GetInstanceID())
	//	{
	//		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_UpperBodyActions::StateMachine_HandleAnimationEnd:: montageInst Position '%.3f' for '%s'"), montageInst->GetPosition(), *m_pPlayer->GetName());
	//		if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::lineout_throw)
	//		{
	//			if (m_CurrentUBActionFBPassesSubType == UpperBodyActionFBPassSubTypes::raise_to_throw) //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|lineout_throw|raise_to_throw_ready_to_throw
	//			{
	//				m_CurrentUBActionFBPassesSubType = UpperBodyActionFBPassSubTypes::ready_to_throw; //loop animation
	//				m_FB_UBActionsPassStateMachine.ReInitialise(ERugbyAnim_Mode_UBActionsFBPass::lineout_throw); //call the lineout throw with data ready to throw...
	//			}
	//			else if (m_CurrentUBActionFBPassesSubType == UpperBodyActionFBPassSubTypes::throw_normal)
	//			{
	//				m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough);
	//			}
	//		}
	//		else if ((m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving) || //13: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass1_null1
	//			(m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing) || //25: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_moving_null1
	//			(m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::medium_pass)) //04: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass_left_null
	//		{
	//			m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough);
	//		}
	//		//player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|emotional_reactions_null1
	//		else if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::emotional_reactions)
	//		{
	//			m_pPlayer->AnimationNotify(ERugbyAnimEvent::END_EMOTIONAL_REACTION_EVENT, 0); //fire an event as it has a valid startTrigger in the node.
	//			m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough);
	//		}

	//	}
	//	/*else //debug code
	//	{
	//		if (m_UBPass_MontageInstance.UBPass_MontageInstance && m_UBPass_MontageInstance.UBPass_MontageInstance->IsStopped())
	//		{
	//			UE_DEBUG_BREAK();
	//		}
	//	}*/
	//}

	//if (m_CurrentUBActions != ERugbyAnim_Mode_UBActions::NullPassThrough) //debug code
	//{
	//	UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_UpperBodyActions::StateMachine_HandleAnimationEnd:: PassAction mode  '%d' for '%s'"), (int)m_CurrentUBActions, *m_pPlayer->GetName());
	//	//if (m_UBAction_MontageInstance.UBAction_MontageInstance == nullptr) //debug code
	//	//	UE_DEBUG_BREAK();
	//}

	//if (m_UBAction_MontageInstance.UBAction_MontageInstance == montageInst)
	//{
	//	m_UBAction_MontageInstance.UBAction_MontageInstance = nullptr;//set this UBPass_MontageInstance to null if it has stopped to avoid having any garbage value.

	//	if (m_UBAction_MontageInstance.UBAction_MontageUniqueID == montageInst->GetInstanceID())
	//	{
	//		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_UpperBodyActions::StateMachine_HandleAnimationEnd:: montageInst Position  '%.3f' for '%s'"), montageInst->GetPosition(), *m_pPlayer->GetName());
	//		//06: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_pass_left_null1
	//		//18: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|medium_pass_null2
	//		if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes)
	//			|| (m_CurrentUBActions == ERugbyAnim_Mode_UBActions::passes))
	//		{
	//			m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::NullPassThrough);
	//		}
	//	}
	//}
//}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::PlaySelectedUB_FBPassAnimation(TArray<FRugbyUpperBodyActionsFBPassAnimRecord*>& Rec, float BlendInTime /*= 0.2f*/, bool /*LoopAlways*/)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_UpperBodyActions_PlaySelectedUB_FBPassAnimation);

	ensureAlways(Rec.Num() > 0);
	if (Rec.Num())
	{
		TArray<FRugbyUpperBodyActionsFBPassAnimRecord*> usableAnimationList;
		for (auto &SelRec : Rec)
		{
			if (SelRec->m_pAnimSequence)
			{
				usableAnimationList.Add(SelRec);
			}
		}

		ensureAlways(usableAnimationList.Num() > 0);

		if (usableAnimationList.Num() > 0)
		{
			FRugbyUpperBodyActionsFBPassAnimRecord* pAnimRec = nullptr;
			if (usableAnimationList.Num() == 1)
			{
				pAnimRec = usableAnimationList[0];
			}
			else //cases like player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_left|MirrorTransforms1 and emotional reaction.
			{
				int RandomValue = FMath::RoundToInt(m_pRUPlayerAnimation->GetGameRandomVariable()->getValue());

				//check if this number is less than number of animations minus 1 and also it's a non-negative
				int SelectedIndex = ((RandomValue < (usableAnimationList.Num()-1)) && RandomValue >=0 ) ? RandomValue : 0;
			
				pAnimRec = usableAnimationList[SelectedIndex];
			}

			ensureAlways(pAnimRec->m_pAnimSequence != nullptr);
			UE_LOG(LogTemp, Display, TEXT("PlaySelectedUB_FBPassAnimation: Anim '%s' for '%s'"), *(pAnimRec->m_animName), *m_pPlayer->GetName());

			m_UBPass_MontageInstance.Reset();
			m_UBPass_MontageInstance.UBPass_AnimRec = pAnimRec;

			FAnimMontageInstance* pMontageInstance = m_pSMMgr->PlayStateMachineAnimationMontage(this, pAnimRec, 0.0f, BlendInTime, pAnimRec->m_loopAlways);
			ensureAlways(pMontageInstance != nullptr);
			if (pMontageInstance)
			{
				m_UBPass_MontageInstance.UBPass_MontageInstance = pMontageInstance;
				m_UBPass_MontageInstance.UBPass_MontageUniqueID = pMontageInstance->GetInstanceID();
			}

			// If this animation has a matching upper body pass animation we should play that too
			if (const FRugbyUpperBodyActionsUBPassTypeRecord* pUpperBodyPassRecList = m_pRUPlayerAnimation->GetAnimationLibrary()->GetUpperBodyTypeRec(ERugbyAnim_Mode_UBActions::passes))
			{
				for (FRugbyUpperBodyActionsUBPassAnimRecord* pUpperBodyRec : pUpperBodyPassRecList->m_animVariants)
				{
					if (pUpperBodyRec->m_pAnimSequence == pAnimRec->m_pAnimSequence && pUpperBodyRec->m_bMirrored == pAnimRec->m_bMirrored)
					{
						UE_LOG(LogTemp, Display, TEXT("PlaySelectedUB_FBPassAnimation: Playing dedicated upper body pass to compliment full body"), *(pAnimRec->m_animName), *m_pPlayer->GetName());
						pMontageInstance = m_pSMMgr->PlayStateMachineAnimationMontage(this, pUpperBodyRec, 0.0f, BlendInTime, pUpperBodyRec->m_loopAlways);
						if (pMontageInstance)
						{
							m_UBPass_MontageInstance.m_pPassSecondaryInstance = pMontageInstance;
							break;
						}
					}
				}
			}

			ensureAlways(m_UBPass_MontageInstance.UBPass_MontageInstance != nullptr);
		}
		else
		{
			UE_LOG(LogTemp, Display, TEXT("PlaySelectedUB_FBPassAnimation: Animations are missing."));
		}
	}
	else
	{			
		m_FB_UBActionsPassStateMachine.ChangeState(ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough);
		UE_LOG(LogTemp, Display, TEXT("PlaySelectedUB_FBPassAnimation: Number of Animation record is zero, resetting the statemachine to NULL"));
	}
}

//============================================================================================//============================================================================================
//============================================================================================//============================================================================================
//============================================================================================//============================================================================================

//============================================================================================

//All exits are dummy for now.

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnExit_UBNullPassThrough()
{
	m_bJustEnteredNullUBAction = false;
	m_ForceStopBlendOutTime = SMDefaultBlendOutTime;
	m_UBAction_MontageInstance.Reset();
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnExit_Catches()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
	if (m_pPlayer->GetAnimInstance())
	{
		m_pPlayer->GetAnimInstance()->StopAnimationsOnSlot(URugbyCharacterAnimInstance::UPPER_BODY_SLOT_NAME, 0.2f);
	}
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnExit_dummy_passes()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnExit_fumbled_catches()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnExit_passes()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//============================================================================================
//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnExit_playtheball()
{
	UE_LOG(LogTemp, Display, TEXT("%s '%s'"), *FString(__func__), *m_pPlayer->GetName());
}

//===============================================================================
//===============================================================================
void RugbyAnimationStateMachine_UpperBodyActions::SetUpperBodyMontageInstance(FAnimMontageInstance* pMontageInstance)
{
	m_UBAction_MontageInstance.Reset();
	if(pMontageInstance && pMontageInstance->IsValid() && UOBJ_IS_VALID(pMontageInstance->Montage))
	{
		m_UBAction_MontageInstance.UBAction_MontageInstance = pMontageInstance;
		m_UBAction_MontageInstance.UBAction_MontageUniqueID = pMontageInstance->GetInstanceID();
	}
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnEnter_UBNullPassThrough()
{
	if (m_CurrentUBActions != ERugbyAnim_Mode_UBActions::NullPassThrough)
	{
		UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_UpperBodyActions::OnEnter_UBNullPassThrough for '%s'"), *m_pPlayer->GetName());
	}

	m_CurrentUBActions = ERugbyAnim_Mode_UBActions::NullPassThrough;

	m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::Null;		

	//if (m_pPlayer->GetAnimInstance() && m_UBAction_MontageInstance.UBAction_MontageInstance)
	//{
	//	for (auto &MontageInt : m_pPlayer->GetAnimInstance()->MontageInstances)
	//	{
	//		if (MontageInt && (m_UBAction_MontageInstance.UBAction_MontageInstance == MontageInt) && m_UBAction_MontageInstance.UBAction_MontageInstance->IsActive()) //stop the animation if it's still playing....
	//		{
	//			UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_UpperBodyActions::OnEnter_UBNullPassThrough  Stopping Montage '%s'"), *m_pPlayer->GetName());
	//			m_UBAction_MontageInstance.UBAction_MontageInstance->Stop(m_ForceStopBlendOutTime);
	//			break;
	//		}
	//	}
	//}

	//m_ForceStopBlendOutTime = SMDefaultBlendOutTime;
	//m_UBAction_MontageInstance.Reset();		

	m_bJustEnteredNullUBAction = true;
}
//============================================================================================

void RugbyAnimationStateMachine_UpperBodyActions::OnEnter_Catches()
{
	m_CurrentUBActions = ERugbyAnim_Mode_UBActions::Catches;
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnEnter_dummy_passes()
{	
	m_CurrentUBActions = ERugbyAnim_Mode_UBActions::dummy_passes;

	const FRugbyUpperBodyActionsUBPassTypeRecord *AnimTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetUpperBodyTypeRec(ERugbyAnim_Mode_UBActions::dummy_passes);
	if (nullptr == AnimTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnter_dummy_passes:TypePtr is null"));
		return;
	}
	TArray <FRugbyUpperBodyActionsUBPassAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : AnimTypePtr->m_animVariants)
	{
		if (m_CurrentUBActionSubType == AnimRec->m_SubType && m_CurrentUBActionPassType == AnimRec->m_PassType )
		{
			tempRec.Add(AnimRec);
		}
	}

	UE_LOG(LogTemp, Display, TEXT("OnEnter_dummy_passes->>> Subtype '%d'"), (int)m_CurrentUBActionSubType);
	PlaySelectedUB_ActionsAnimation(tempRec, SMDefaultPass2BlendInTime);
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnEnter_fumbled_catches()
{
	m_CurrentUBActions = ERugbyAnim_Mode_UBActions::fumbled_catches;
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnEnter_passes()
{
	m_CurrentUBActions = ERugbyAnim_Mode_UBActions::passes;

	const FRugbyUpperBodyActionsUBPassTypeRecord *AnimTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetUpperBodyTypeRec(ERugbyAnim_Mode_UBActions::passes);
	if (nullptr == AnimTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("OnEnter_passes:TypePtr is null"));
		return;
	}
	TArray <FRugbyUpperBodyActionsUBPassAnimRecord*> tempRec;
	tempRec.Empty();

	for (auto &AnimRec : AnimTypePtr->m_animVariants)
	{
		if (m_CurrentUBActionSubType == AnimRec->m_SubType && m_CurrentUBActionPassType == AnimRec->m_PassType)
		{
			tempRec.Add(AnimRec);
		}
	}
	float BlendInTime = SMDefaultPass1BlendInTime;

	if (m_CurrentUBActionPassType == UpperBodyPassType::long_pass || m_CurrentUBActionPassType == UpperBodyPassType::long_pass_standing || m_CurrentUBActionPassType == UpperBodyPassType::medium_pass)
	{
		BlendInTime = SMDefaultPass2BlendInTime;
	}

	UE_LOG(LogTemp, Display, TEXT("OnEnter_passes->>> Subtype '%d'"), (int)m_CurrentUBActionSubType);
	PlaySelectedUB_ActionsAnimation(tempRec, BlendInTime);
}

//============================================================================================
// Although this is similar to passing OnEnter, split it off in case we need extra control
// MLW Note - We only have one type of play the ball sub type: play_the_ball_behind,
// Would we need others? left, right? TBD
//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::OnEnter_playtheball()
{
	m_CurrentUBActionFBPasses = ERugbyAnim_Mode_UBActionsFBPass::play_the_ball;

	const FRugbyUpperBodyActionsFBPassTypeRecord* AnimTypePtr = m_pRUPlayerAnimation->GetAnimationLibrary()->GetFBPassTypeRec(m_CurrentUBActionFBPasses);
	if (nullptr == AnimTypePtr)
	{
		UE_LOG(LogTemp, Display, TEXT("StateMachine_UpperBody_FBPass_play_the_ball:TypePtr is null"));
		return;
	}
	TArray <FRugbyUpperBodyActionsFBPassAnimRecord*> tempRec;
	tempRec.Empty();

	// Check if the player is injured to determine which animation to play.
	const FString SlowAnimName = TEXT("play_the_ball_fwd_getup_slow");
	if (m_pPlayer && m_pPlayer->GetAttributes() && m_pPlayer->GetAttributes()->IsInjured())
	{
		// Player is injured, find and play only the slow animation.
		for (auto& AnimRec : AnimTypePtr->m_animVariants)
		{
			if (AnimRec && AnimRec->m_pAnimSequence && AnimRec->m_pAnimSequence->GetName() == SlowAnimName)
			{
				tempRec.Add(AnimRec);
				break;
			}
		}
	}
	else
	{
		// Player is not injured, gather all standard animations and exclude the slow one.
		for (auto& AnimRec : AnimTypePtr->m_animVariants)
		{
			if (m_CurrentUBActionFBPassesSubType == AnimRec->m_SubType)
			{
				tempRec.Add(AnimRec);
			}
		}

		tempRec.RemoveAll([&](FRugbyUpperBodyActionsFBPassAnimRecord* Rec)
			{
				return Rec && Rec->m_pAnimSequence && Rec->m_pAnimSequence->GetName() == SlowAnimName;
			});
	}

	UE_LOG(LogTemp, Display, TEXT("StateMachine_UpperBody_FBPass_play_the_ball->>> Subtype '%d'"), (int)m_CurrentUBActionFBPassesSubType);

	PlaySelectedUB_FBPassAnimation(tempRec, SMDefaultPass2BlendInTime);
}

//============================================================================================
void RugbyAnimationStateMachine_UpperBodyActions::PlaySelectedUB_ActionsAnimation(TArray<FRugbyUpperBodyActionsUBPassAnimRecord*>& Rec, float BlendInTime/*=0.2f*/ , bool /*LoopAlways*/)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_UpperBodyActions_PlaySelectedUB_ActionsAnimation)
	ensureAlways(Rec.Num() > 0); //we should never get this assert, if we get, then check pass anim wont play for that scenerio
	if (Rec.Num())
	{
		TArray<FRugbyUpperBodyActionsUBPassAnimRecord*> usableAnimationList;
		for (auto &SelRec : Rec)
		{
			if (SelRec->m_pAnimSequence)
			{
				usableAnimationList.Add(SelRec);
			}
		}

		ensureAlways(usableAnimationList.Num() > 0);

		if (usableAnimationList.Num() > 0)
		{
			FRugbyUpperBodyActionsUBPassAnimRecord* pAnimRec = nullptr;
			if (usableAnimationList.Num() == 1)
			{
				pAnimRec = usableAnimationList[0];
			}
			else //cases like player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_left|MirrorTransforms1
			{
				int RandomValue = FMath::RoundToInt(m_pRUPlayerAnimation->GetGameRandomVariable()->getValue());

				//check if this number is less than number of animations minus 1 and also it's a non-negative
				int SelectedIndex = ((RandomValue < (usableAnimationList.Num() - 1)) && RandomValue >= 0) ? RandomValue : 0;
				pAnimRec = usableAnimationList[SelectedIndex];
			}

			//if we are playing a fullbody already, this must be upperbody only
// 			if (m_pSMMgr->GetSMFullBodyActions()->GetFullBodyActionState() != ERugbyAnim_Mode_FullBodyActions::Null || 
// 				m_pSMMgr->GetSMTackle()->GetCurrentTackleMode() != ERugbyAnim_Mode_Tackles::null)
// 			{			
// 				pAnimRec->m_bUpperBody = true;
// 			}
// 			else
// 			{
// 				// This is a hack to ensure upperbody slot also plays the same animation as fullbody to avoid any issue like interruption...
// 				pAnimRec->m_bUpperBody = true;
// 				m_pSMMgr->PlayStateMachineAnimationMontage(pAnimRec, 0.0f, SMDefaultBlendInTime, pAnimRec->m_loopAlways);
// 				
// 				pAnimRec->m_bUpperBody = false;
// 			}

			ensureAlways(pAnimRec->m_pAnimSequence != nullptr);
			UE_LOG(LogTemp, Display, TEXT("PlaySelectedUB_ActionsAnimation: '%s' for '%s'"), *(pAnimRec->m_animName), *m_pPlayer->GetName());
			m_UBAction_MontageInstance.Reset();
			FAnimMontageInstance* pMontageInstance = m_pSMMgr->PlayStateMachineAnimationMontage(this, pAnimRec, 0.0f, BlendInTime, pAnimRec->m_loopAlways);
			ensureAlways(pMontageInstance != nullptr);
			if (pMontageInstance)
			{
				m_UBAction_MontageInstance.UBAction_MontageInstance = pMontageInstance;
				m_UBAction_MontageInstance.UBAction_MontageUniqueID = pMontageInstance->GetInstanceID();
			}
		}
		else
		{
			UE_LOG(LogTemp, Display, TEXT("PlaySelectedUB_ActionsAnimation: Animations are missing."));
		}
	}
	else
	{		
		m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::NullPassThrough);
		UE_LOG(LogTemp, Display, TEXT("PlaySelectedUB_ActionsAnimation: Number of Animation record is zero, resetting the statemachine to NULL"));
	}
}

//============================================================================================

void RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBAction_Update(float time)
{		
	QUICK_SCOPE_CYCLE_COUNTER(STAT_Rugby_UpperBodyActions_StateMachine_UBAction_Update)

	if (m_bJustEnteredNullUBAction)
	{
		if (m_pPlayer->GetAnimInstance() && m_UBAction_MontageInstance.UBAction_MontageInstance)
		{
			for (auto &MontageInt : m_pPlayer->GetAnimInstance()->MontageInstances)
			{
				if (MontageInt && (m_UBAction_MontageInstance.UBAction_MontageInstance == MontageInt) && m_UBAction_MontageInstance.UBAction_MontageInstance->IsActive()) //stop the animation if it's still playing....
				{
					UE_LOG(LogTemp, Display, TEXT("RugbyAnimationStateMachine_UpperBodyActions::OnEnter_UBNullPassThrough  Stopping Montage '%s'"), *m_pPlayer->GetName());
					m_UBAction_MontageInstance.UBAction_MontageInstance->Stop(m_ForceStopBlendOutTime);
					break;
				}
			}
		}
		m_bJustEnteredNullUBAction = false;
		m_ForceStopBlendOutTime = SMDefaultBlendOutTime;
		m_UBAction_MontageInstance.Reset();
	}

	//check if we got something to process, if we do, just process the request and clearoff the array		
	for (auto& RequestListIndex : m_pSMMgr->m_AnimationRequestNameState)
	{
		if (RequestListIndex.Value == false)
			continue;

		FString RequestName(RequestListIndex.Key);

		wwDB_DTREQUESTTYPE_ENUM requestTypeEnum = NAME_TO_ENUM(wwDB_DTREQUESTTYPE_ENUM, TCHAR_TO_ANSI(*RequestName));

		//todo: combine some of the cases
		switch (requestTypeEnum)
		{

		//08: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null1_dummy_pass_left
		//11: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_dummy_passes
		//14: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_dummy_passes3
		case wwDB_DTREQUESTTYPE_ENUM::DUMMY_PASS_LEFT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::fumbled_catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::left;
				m_CurrentUBActionPassType = UpperBodyPassType::Null;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::dummy_passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//09: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null1_dummy_pass_forward
		//12: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_dummy_passes1
		//15: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_dummy_passes4
		case wwDB_DTREQUESTTYPE_ENUM::DUMMY_PASS_FORWARD:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::fumbled_catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::forward;
				m_CurrentUBActionPassType = UpperBodyPassType::Null;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::dummy_passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//10: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null1_dummy_pass_right
		//13: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_dummy_passes2
		//16: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_dummy_passes5
		case wwDB_DTREQUESTTYPE_ENUM::DUMMY_PASS_RIGHT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::fumbled_catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::right;
				m_CurrentUBActionPassType = UpperBodyPassType::Null;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::dummy_passes);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//19: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null1_short_pass_behind_right
		//23: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes4
		//63: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes18
		case wwDB_DTREQUESTTYPE_ENUM::SHORT_PASS_BEHIND:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::behind;
				m_CurrentUBActionPassType = UpperBodyPassType::short_pass;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//20: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null1_short_pass_right
		//24: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes5
		//64: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes19
		case wwDB_DTREQUESTTYPE_ENUM::SHORT_PASS_RIGHT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::right;
				m_CurrentUBActionPassType = UpperBodyPassType::short_pass;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//21: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null1_short_pass_forward
		//25: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes6
		//65: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes20
		case wwDB_DTREQUESTTYPE_ENUM::SHORT_PASS_FORWARD:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::forward;
				m_CurrentUBActionPassType = UpperBodyPassType::short_pass;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//22: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null1_short_pass_left
		//26: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes7
		//66: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes21
		case wwDB_DTREQUESTTYPE_ENUM::SHORT_PASS_LEFT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::left;
				m_CurrentUBActionPassType = UpperBodyPassType::short_pass;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//27: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes8
		//53: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes8
		case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_HIGH_LEFT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::high_left;
				m_CurrentUBActionPassType = UpperBodyPassType::medium_pass;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//28: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes9
		//54: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes9
		case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_HIGH_RIGHT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::high_right;
				m_CurrentUBActionPassType = UpperBodyPassType::medium_pass;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//29: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes10
		//55: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes10
		case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_L135:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::medium_pass) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::l135;
					m_CurrentUBActionPassType = UpperBodyPassType::medium_pass;
					m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//30: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes11
		//56: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes11
		case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_R135:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::medium_pass) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::r135;
					m_CurrentUBActionPassType = UpperBodyPassType::medium_pass;
					m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//31: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes12
		//57: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes12
		case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_R45:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::medium_pass) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::r45;
					m_CurrentUBActionPassType = UpperBodyPassType::medium_pass;
					m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//32: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes13
		//58: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes13
		case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_L45:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::medium_pass) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::l45;
					m_CurrentUBActionPassType = UpperBodyPassType::medium_pass;
					m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//33: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes14
		//59: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes14
		case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_RIGHT:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::medium_pass) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::right;
					m_CurrentUBActionPassType = UpperBodyPassType::medium_pass;
					m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//34: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes15
		//60: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes15
		case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_FORWARD:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::medium_pass) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::forward;
					m_CurrentUBActionPassType = UpperBodyPassType::medium_pass;
					m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//35: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes16
		//61: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes16
		case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_LEFT:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::medium_pass) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::left;
					m_CurrentUBActionPassType = UpperBodyPassType::medium_pass;
					m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//36: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes17
		//62: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes17
		case wwDB_DTREQUESTTYPE_ENUM::MEDIUM_PASS_BEHIND:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::medium_pass) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::behind;
					m_CurrentUBActionPassType = UpperBodyPassType::medium_pass;
					m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//37: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes18
		//45: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes
		//70: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes29
		//71: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes26
		case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_BEHIND:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving && m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					if (m_pRUPlayerAnimation->GetSpeedVariable()->getValue() > 0.5f)
					{
						m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::behind;
						m_CurrentUBActionPassType = UpperBodyPassType::long_pass;
						m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
					}
					else
					{
						m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::behind;
						m_CurrentUBActionPassType = UpperBodyPassType::long_pass_standing;
						m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

					}
					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//38: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes19
		//46: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes1
		//69: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes28
		//72: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes27
		case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_LEFT:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving && m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					if (m_pRUPlayerAnimation->GetSpeedVariable()->getValue() > 0.5f)
					{
						m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::left;
						m_CurrentUBActionPassType = UpperBodyPassType::long_pass;
						m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

					}
					else
					{
						m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::left;
						m_CurrentUBActionPassType = UpperBodyPassType::long_pass_standing;
						m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

					}
					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//39: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes20
		//47: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes2
		//68: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes27
		//73: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes28
		case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_FORWARD:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving && m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					if (m_pRUPlayerAnimation->GetSpeedVariable()->getValue() > 0.5f)
					{
						m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::forward;
						m_CurrentUBActionPassType = UpperBodyPassType::long_pass;
						m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

					}
					else
					{
						m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::right;
						m_CurrentUBActionPassType = UpperBodyPassType::long_pass_standing;
						m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

					}
					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//40: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes21
		//48: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes3
		//67: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes26
		//74: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes29
		case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_RIGHT:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving && m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					if (m_pRUPlayerAnimation->GetSpeedVariable()->getValue() > 0.5f)
					{
						m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::right;
						m_CurrentUBActionPassType = UpperBodyPassType::long_pass;
						m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
					}
					else
					{
						m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::right;
						m_CurrentUBActionPassType = UpperBodyPassType::long_pass_standing;
						m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
					}
					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//41: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes22
		//49: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes4
		case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_L45:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving && m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::l45;
					m_CurrentUBActionPassType = UpperBodyPassType::long_pass;
					m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//42: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes23
		//50: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes5
		case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_R45:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving && m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::r45;
					m_CurrentUBActionPassType = UpperBodyPassType::long_pass;
					m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//43: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes24
		//51: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes6
		case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_R135:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving && m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::r135;
					m_CurrentUBActionPassType = UpperBodyPassType::long_pass;
					m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//44: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes25
		//52: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes7
		case wwDB_DTREQUESTTYPE_ENUM::LONG_PASS_L135:
		{
			//if (m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_moving && m_CurrentUBActionFBPasses != ERugbyAnim_Mode_UBActionsFBPass::long_pass_standing) //#todo: Not sure if this is correct. This is added since FBPass SM can also process the same request
			{
				if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
					(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
				{
					m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::l135;
					m_CurrentUBActionPassType = UpperBodyPassType::long_pass;
					m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

					m_pSMMgr->ResetRequestListValue(RequestName);
				}
			}
		}
		break;

		//75: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes
		//122: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes53
		//123: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes54
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_BEHIND:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::behind;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_null;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//76: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes1
		//121: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes52
		//124: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes55
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_LEFT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::left;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_null;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//77: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes70
		//120: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes51
		//125: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes56
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_FORWARD:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::forward;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_null;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//78: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes71
		//119: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes50
		//126: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes57
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_RIGHT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::right;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_null;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//79: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes72
		//118: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes49
		//127: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes58
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_FALLING_RIGHTARM_BEHIND:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::rightarm_behind;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_falling;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//80: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes73
		//117: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes48
		//128: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes59
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_FALLING_RIGHTARM_LEFT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::rightarm_left;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_falling;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//81: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes74
		//116: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes47
		//129: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes60
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_FALLING_RIGHTARM_FORWARD:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::rightarm_forward;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_falling;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//82: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes75
		//115: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes46
		//130: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes61
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_FALLING_RIGHTARM_RIGHT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::rightarm_right;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_falling;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//83: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes76
		//114: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes45
		//131: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes62
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_FALLING_BEHIND:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::behind;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_falling;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//84: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes77
		//113: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes44
		//132: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes63
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_FALLING_LEFT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::left;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_falling;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//85: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes78
		//112: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes43
		//133: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes64
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_FALLING_FORWARD:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::forward;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_falling;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//86: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes79
		//111: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes42
		//134: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes65
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_FALLING_RIGHT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::right;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_falling;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//87: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes80
		//110: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes41
		//135: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes66
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_RIGHTARM_BEHIND:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::rightarm_behind;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_null;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//88: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes81
		//109: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes40
		//136: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes67
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_RIGHTARM_LEFT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::rightarm_left;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_null;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//89: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes82
		//108: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes39
		//137: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes68
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_RIGHTARM_FORWARD:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::rightarm_forward;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_null;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//90: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes83
		//107: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes38
		//138: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes69
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_RIGHTARM_RIGHT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::rightarm_right;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_null;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//91: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes84
		//106: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes37
		//139: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes70
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_FALLING_LEFTARM_BEHIND:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::leftarm_behind;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_falling;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//92: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes85
		//105: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes36
		//140: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes71
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_FALLING_LEFTARM_LEFT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::leftarm_left;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_falling;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//93: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes86
		//104: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes35
		//141: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes72
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_FALLING_LEFTARM_FORWARD:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::leftarm_forward;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_falling;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//94: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes87
		//103: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes34
		//142: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes73
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_FALLING_LEFTARM_RIGHT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::leftarm_right;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_falling;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//95: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes88
		//102: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes33
		//143: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes74
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_LEFTARM_RIGHT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::leftarm_right;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_null;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//96: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes89
		//101: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes32
		//144: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes75
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_LEFTARM_FORWARD:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::leftarm_forward;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_null;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//97: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes90
		//100: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes31
		//145: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes76
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_LEFTARM_LEFT:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::leftarm_left;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_null;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//98: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_passes91
		//99: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes30
		//146: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_passes_passes77
		case wwDB_DTREQUESTTYPE_ENUM::OFFLOAD_LEFTARM_BEHIND:
		{
			if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) ||
				(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes))
			{
				m_CurrentUBActionSubType = UpperBodyActionUBPassSubTypes::leftarm_behind;
				m_CurrentUBActionPassType = UpperBodyPassType::offload_null;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::passes);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//some special cases handled for catches. This is needed to maintain the statemachine.
		//00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|fumbled_catches_null1 durationInTime: 0.0
		//03: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|catches_null1  durationInTime: 0.0
		case wwDB_DTREQUESTTYPE_ENUM::STOP_CATCH:
		{
			//if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::fumbled_catches) || //#todo
			//	(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
			{
				m_ForceStopBlendOutTime = 0.0f;
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::NullPassThrough);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null1_fumbled_catches
		case wwDB_DTREQUESTTYPE_ENUM::FUMBLED_CATCH:
		{
			if (m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough)
			{
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::fumbled_catches);

				m_pSMMgr->ResetRequestListValue(RequestName);
			}
		}
		break;

		//05: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null1_catches			
		case wwDB_DTREQUESTTYPE_ENUM::CATCH:
			if (m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough)
			{
				m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::Catches);
				if (m_pPlayer && m_pPlayer->GetAnimInstance())
				{
					m_pPlayer->GetAnimInstance()->BeginCatchAnticipation(0.15f);
				}
				m_pSMMgr->ResetRequestListValue(RequestName);
			}
	
		default:
			break;
		}
	}//end of for (auto& RequestListIndex : m_pSMMgr->m_AnimationRequestNameState)
	
	//#todo: Move below code to Update method of each statemachine.
	//147: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|fumbled_catches_null durationInTime: 0.20000000298023224
	if (((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches) && (m_pRUPlayerAnimation->GetCatchVariable()->getValue() <= 0.5f)) ||
		//148: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|fumbled_catches_null3  durationInTime: 0.20000000298023224
		((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::fumbled_catches) && (m_pRUPlayerAnimation->GetFumbledCatchVariable()->getValue() <= 0.5f)))
	{
		m_ForceStopBlendOutTime = 0.20000000298023224f;
		m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::NullPassThrough);
	}
	else if (m_CurrentUBActions == ERugbyAnim_Mode_UBActions::NullPassThrough)
	{
		//149: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|fumbled_catches_null4
		if (m_pRUPlayerAnimation->GetCatchVariable()->getValue() > 0.5f)
		{
			m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::Catches);
		}
		//150: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|null_catches
		else if (m_pRUPlayerAnimation->GetFumbledCatchVariable()->getValue() > 0.5f)
		{
			m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::fumbled_catches);
		}
	}
	//06: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|dummy_pass_left_null1 durationInTime: 0.20000000298023224
	//18: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions|medium_pass_null2 durationInTime: 0.20000000298023224
	else if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
			(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::passes))
	{
		EMontageCrossDurationData MontageStoppedData;
		if (RugbyAnimationStateMachineMgr::EvaluateCrossedDurationFraction(m_UBAction_MontageInstance.UBAction_MontageInstance, m_pPlayer->GetAnimInstance(), MontageStoppedData))
		{
			m_ForceStopBlendOutTime = 0.20000000298023224f;
			m_UBActionsStateMachine.ChangeState(ERugbyAnim_Mode_UBActions::NullPassThrough);
		}
	}
}//void RugbyAnimationStateMachine_UpperBodyActions::StateMachine_UBPass_Update(float time)

//============================================================================================

bool RugbyAnimationStateMachine_UpperBodyActions::IsMediumPassLongPassNodeActive()
{
	if (m_pSMMgr->m_FBPass)
	{
		if (m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough)
		{
			return true;
		}
	}
	else if (m_pSMMgr->m_UBActions)
	{
		if ((m_CurrentUBActions == ERugbyAnim_Mode_UBActions::dummy_passes) ||
			(m_CurrentUBActions == ERugbyAnim_Mode_UBActions::Catches))
		{
			return true;
		}
	}

	return false;
}

//============================================================================================

bool RugbyAnimationStateMachine_UpperBodyActions::IsReadyForLineOutThrow()
{
	if ((m_CurrentUBActionFBPasses == ERugbyAnim_Mode_UBActionsFBPass::lineout_throw) &&
		(m_CurrentUBActionFBPassesSubType == UpperBodyActionFBPassSubTypes::ready_to_throw))
	{
		return true;
	}
	return false;
}

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
//===============================================================================
//===============================================================================
FString RugbyAnimationStateMachine_UpperBodyActions::GetDebugStateText()
{

	FString debugString = FString::Printf
	(
		TEXT("FB UpperBody SM:   State: %s  \n"),
		*ENUM_TO_FSTRING(ERugbyAnim_Mode_UBActionsFBPass, m_FB_UBActionsPassStateMachine.GetCurrentStateKey())
	);
	debugString += FString::Printf
	(
		TEXT("UpperBody SM:   State: %s  \n"),
		*ENUM_TO_FSTRING(ERugbyAnim_Mode_UBActions, m_UBActionsStateMachine.GetCurrentStateKey())
	);
	return debugString;
}
#endif

//============================================================================================

//
//Comments for reference. Dont add any code below this line.
//

//============================================================================================
//================================LINEOUTS====================================================
//============================================================================================

/*
sourceNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null|PassThrough1
destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|lineout_throw
inverted: False
requestName: lineout_thrower
type: Condition_601_OnRequest


destSubStateNodes: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|lineout_throw|raise_to_throw|lothrowraise
loop: False

==============================================
testFraction: 1.0
type: Condition_603_CrossedDurationFraction

destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|lineout_throw|ready_to_throw|lothrowready
durationInTime: 0.25
sourceNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|lineout_throw|raise_to_throw|lothrowraise

loop: True

==============================================

inverted: False
requestName: lineout_throw
type: Condition_601_OnRequest

sourceNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|lineout_throw|ready_to_throw|lothrowready

destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|lineout_throw|throw_normal|lothrow
*/

//============================================================================================
//================================Medium_Pass=================================================
//============================================================================================


/*
Node: 00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass
sourceNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null|PassThrough1

destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass

destSubStateNodes
00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_l45|MirrorTransforms1

Parent: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_l45|MirrorTransforms1
Node: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_l45|wbrmp45r01

conditions
00
inverted: False
requestName: medium_pass_l45
type: Condition_601_OnRequest
01
inputParam: ControlParameters|full_body_passing
lessThanOperation: False
orEqual: False
testValue: 0.5
type: Condition_616_ControlParamTest

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
Node 01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass1

sourceNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null|PassThrough1

destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass

destSubStateNodes
00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_r45|wbrmp45r01

conditions
00
inverted: False
requestName: medium_pass_r45
type: Condition_601_OnRequest
01
inputParam: ControlParameters|full_body_passing
lessThanOperation: False
orEqual: False
testValue: 0.5
type: Condition_616_ControlParamTest

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
Node: 02: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass2

sourceNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null|PassThrough1

destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass

destSubStateNodes
00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_r135|wbrmp135r01

conditions
00
inverted: False
requestName: medium_pass_r135
type: Condition_601_OnRequest
01
inputParam: ControlParameters|full_body_passing
lessThanOperation: False
orEqual: False
testValue: 0.5
type: Condition_616_ControlParamTest

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

Node 03: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass3
sourceNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null|PassThrough1
destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass
destSubStateNodes
00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_l135|MirrorTransforms1

conditions
00
inverted: False
requestName: medium_pass_l135
type: Condition_601_OnRequest
01
inputParam: ControlParameters|full_body_passing
lessThanOperation: False
orEqual: False
testValue: 0.5
type: Condition_616_ControlParamTest


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

Node 04: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass_forward

sourceNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null|PassThrough1
destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass
destSubStateNodes
00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_forward|wbrmpf01
conditions
00
inverted: False
requestName: medium_pass_forward
type: Condition_601_OnRequest
01
inputParam: ControlParameters|full_body_passing
lessThanOperation: False
orEqual: False
testValue: 0.5
type: Condition_616_ControlParamTest

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

Node 05: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass_left
sourceNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null|PassThrough1
destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass
destSubStateNodes
00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_left|MirrorTransforms1


Type: Node_131_Switch
Parent: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_left|MirrorTransforms1
List animation nodes
Children
connectedNodes
00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_left|wbrmp90r01
01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_left|wbrmp90r02
02: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_left|wbrmp90r03
03: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_left|wbrmp90r04
evaluateAtEndOfAnimation: True
weightParam: ControlParameters|game_random

breakoutTransition: True
conditions
00
inverted: False
requestName: medium_pass_left
type: Condition_601_OnRequest
01
inputParam: ControlParameters|full_body_passing
lessThanOperation: False
orEqual: False
testValue: 0.5
type: Condition_616_ControlParamTest


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
06: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass_right

sourceNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null|PassThrough1
destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass
destSubStateNodes
00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_right|Switch1

Parent: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass
List animation nodes
Children
connectedNodes
00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_right|wbrmp90r01
01: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_right|wbrmp90r02
02: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_right|wbrmp90r03
03: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_right|wbrmp90r04
evaluateAtEndOfAnimation: True
weightParam: ControlParameters|game_random
sourceTransitions
00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_medium_pass_right


conditions
00
inverted: False
requestName: medium_pass_right
type: Condition_601_OnRequest
01
inputParam: ControlParameters|full_body_passing
lessThanOperation: False
orEqual: False
testValue: 0.5
type: Condition_616_ControlParamTest


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
07: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null_passes10

sourceNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null|PassThrough1

destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass
destSubStateNodes
00: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass|medium_pass_behind|wbrmp180r01

conditions
00
inverted: False
requestName: medium_pass_behind
type: Condition_601_OnRequest
01
inputParam: ControlParameters|full_body_passing
lessThanOperation: False
orEqual: False
testValue: 0.5
type: Condition_616_ControlParamTest

////////////////////////////////////////////////////////////
//end of node
Node: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass_null
sourceNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass
destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null|PassThrough1
conditions
inputParam: ControlParameters|full_body_passing
lessThanOperation: True
orEqual: False
testValue: 0.5
type: Condition_616_ControlParamTest

////////////////////////////////////////////////////////////
//end of node
Node: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|medium_pass_null1
sourceNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|long_pass_moving
destinationNode: player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing|null|PassThrough1

inputParam: ControlParameters|full_body_passing
lessThanOperation: True
orEqual: False
testValue: 0.5
type: Condition_616_ControlParamTest

*/
