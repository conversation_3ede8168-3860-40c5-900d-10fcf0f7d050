// Copyright (c) 2016-2017 Wicked Witch Software Pty. Ltd.

#include "RugbyGameSession.h"
#include "OnlineSubsystemSessionSettings.h"
#include "Runtime/Engine/Classes/Kismet/GameplayStatics.h"
#include "OnlineSubsystem.h"
#include "OnlineSessionSettings.h"

#include "Utility/consoleVars.h"
#include "Networking/VoipManager.h"
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"

#ifndef BUILD_NUMBER
#define BUILD_NUMBER 0
#endif

namespace
{
	const FString CustomMatchKeyword("Custom");
}

ARugbyGameSession::ARugbyGameSession(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	if (!HasAnyFlags(RF_ClassDefaultObject))
	{
		URugbyGameInstance* GameInstance = Cast<URugbyGameInstance>(GetWorld()->GetGameInstance());
		if (GameInstance)
		{
			// We store the game instance values of our settings to our local pointer.
			CurrentSessionParams = &GameInstance->CurrentSessionParams;
			HostSettings = &GameInstance->HostSettings;
		}

		bReplicates = true;

		OnFindFriendSessionCompleteDelegate = FOnFindFriendSessionCompleteDelegate::CreateUObject(this, &ARugbyGameSession::OnFindFriendSessionComplete);
		OnCancelMatchmakingCompleteDelegate = FOnCancelMatchmakingCompleteDelegate::CreateUObject(this, &ARugbyGameSession::OnCancelMatchmakingComplete);
		OnMatchmakingCompleteDelegate = FOnMatchmakingCompleteDelegate::CreateUObject(this, &ARugbyGameSession::OnMatchmakingComplete);

		OnCreateSessionCompleteDelegate = FOnCreateSessionCompleteDelegate::CreateUObject(this, &ARugbyGameSession::OnCreateSessionComplete);
		OnDestroySessionCompleteDelegate = FOnDestroySessionCompleteDelegate::CreateUObject(this, &ARugbyGameSession::OnDestroySessionComplete);

		OnFindSessionsCompleteDelegate = FOnFindSessionsCompleteDelegate::CreateUObject(this, &ARugbyGameSession::OnFindSessionsComplete);
		OnJoinSessionCompleteDelegate = FOnJoinSessionCompleteDelegate::CreateUObject(this, &ARugbyGameSession::OnJoinSessionComplete);

		OnUpdateSessionCompleteDelegate = FOnUpdateSessionCompleteDelegate::CreateUObject(this, &ARugbyGameSession::OnUpdateSessionComplete);

		OnStartSessionCompleteDelegate = FOnStartSessionCompleteDelegate::CreateUObject(this, &ARugbyGameSession::OnStartOnlineGameComplete);

		FindFriendsDone = FOnReadFriendsListComplete::CreateUObject(this, &ARugbyGameSession::OnFoundFriends);

		IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
		if (OnlineSub)
		{
			IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();

			//Sessions->OnConnectionStateCallback.AddUObject(this, &ARugbyGameSession::OnConnectionCheckComplete);
			//// GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL remove ERICTOTEST OnlineSession interface haas changed
		}
	}
}

void ARugbyGameSession::RegisterPlayer(APlayerController* NewPlayer, const TSharedPtr<const FUniqueNetId>& UniqueId, bool bWasFromInvite)
{
	if (NewPlayer != NULL)
	{
		// Set the player's ID.

		check(NewPlayer->PlayerState);

		if (!bDontIncrementPlayerCount)
		{
			NewPlayer->PlayerState->PlayerId = GetNextPlayerID();
			bDontIncrementPlayerCount = false;
		}

		NewPlayer->PlayerState->SetUniqueId(UniqueId);
		NewPlayer->PlayerState->RegisterPlayerWithSession(bWasFromInvite);
	}
}
/**
* Delegate fired when a session start request has completed
*
* @param SessionName the name of the session this callback is for
* @param bWasSuccessful true if the async action completed without error, false if there was an error
*/
void ARugbyGameSession::OnStartOnlineGameComplete(FName InSessionName, bool bWasSuccessful)
{
	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
		if (Sessions.IsValid())
		{
			Sessions->ClearOnStartSessionCompleteDelegate_Handle(OnStartSessionCompleteDelegateHandle);
		}
	}
	// XXX switch aparently needs to know
	MABLOGDEBUG("ARugbyGameSession::OnStartOnlineGameComplete tell lobbyGameNode this ahppened");
}

void ARugbyGameSession::HandleMatchHasStarted()
{
	// start online game locally and wait for completion
	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
		if (Sessions.IsValid())
		{
			UE_LOG(LogOnlineGame, Warning, TEXT("Starting session %s on server"), *FName(CurrentSessionParams->SessionName).ToString());
			OnStartSessionCompleteDelegateHandle = Sessions->AddOnStartSessionCompleteDelegate_Handle(OnStartSessionCompleteDelegate);
			Sessions->StartSession(CurrentSessionParams->SessionName);
		}
	}
}

void ARugbyGameSession::HandleMatchHasEnded()
{
	// start online game locally and wait for completion
	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
		if (Sessions.IsValid())
		{
			// tell the clients to end
			for (FConstPlayerControllerIterator It = GetWorld()->GetPlayerControllerIterator(); It; ++It)
			{
				/*ARugbyPlayerController* PC = Cast<ARugbyPlayerController>(*It);
				if (PC && !PC->IsLocalPlayerController())
				{
					PC->ClientEndOnlineGame();
				}*/
			}

			// server is handled here
			UE_LOG(LogOnlineGame, Warning, TEXT("Ending session %s on server"), *FName(CurrentSessionParams->SessionName).ToString());
			Sessions->EndSession(CurrentSessionParams->SessionName);
		}
	}
}

bool ARugbyGameSession::IsBusy() const
{
	if (HostSettings->IsValid() || SearchSettings.IsValid())
	{
		return true;
	}

	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
		if (Sessions.IsValid())
		{
			EOnlineSessionState::Type GameSessionState = Sessions->GetSessionState(CurrentSessionParams->SessionName);
			EOnlineSessionState::Type PartySessionState = Sessions->GetSessionState(NAME_PartySession);
			if (GameSessionState != EOnlineSessionState::NoSession || PartySessionState != EOnlineSessionState::NoSession)
			{
				return true;
			}
		}
	}

	return false;
}

EOnlineAsyncTaskState::Type ARugbyGameSession::GetSearchResultStatus(int32& SearchResultIdx, int32& NumSearchResults)
{
	SearchResultIdx = 0;
	NumSearchResults = 0;

	if (SearchSettings.IsValid())
	{
		if (SearchSettings->SearchState == EOnlineAsyncTaskState::Done)
		{
			SearchResultIdx = CurrentSessionParams->BestSessionIdx;
			NumSearchResults = SearchSettings->SearchResults.Num();
		}
		return SearchSettings->SearchState;
	}

	return EOnlineAsyncTaskState::NotStarted;
}

const TArray<FOnlineSessionSearchResult>& ARugbyGameSession::GetSearchResults() const
{
	if (SearchSettings.IsValid())
	{
		return SearchSettings->SearchResults;
	}
	return EmptySearchArray;
}


void ARugbyGameSession::OnCreateSessionComplete(FName InSessionName, bool bWasSuccessful)
{
	UE_LOG(LogOnlineGame, Warning, TEXT("OnCreateSessionComplete %s bSuccess: %d"), *InSessionName.ToString(), bWasSuccessful);

	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
		Sessions->ClearOnCreateSessionCompleteDelegate_Handle(OnCreateSessionCompleteDelegateHandle);
	}

	OnCreatePresenceSessionComplete().Broadcast(InSessionName, bWasSuccessful);
}

void ARugbyGameSession::OnDestroySessionComplete(FName InSessionName, bool bWasSuccessful)
{
	UE_LOG(LogOnlineGame, Warning, TEXT("OnDestroySessionComplete %s bSuccess: %d"), *InSessionName.ToString(), bWasSuccessful);

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->GetVoipManager()->ShutdownVoipService();
	}

	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
		Sessions->ClearOnDestroySessionCompleteDelegate_Handle(OnDestroySessionCompleteDelegateHandle);

		HostSettings = NULL;
	}
}

void ARugbyGameSession::DumpMatchingParams(FRugbyGameSessionParams* p)
{
#if !UE_BUILD_SHIPPING
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpMatchingParams SessionTemplateName='%s'"), *p->SessionTemplateName);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpMatchingParams MatchingHopper='%s'"), *p->MatchingHopper);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpMatchingParams SessionName='%s'"), *p->SessionName.ToString());
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpMatchingParams bIsLAN=%d"), p->bIsLAN);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpMatchingParams bIsPresence=%d"), p->bIsPresence);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpMatchingParams UserId="), *p->UserId->ToString());
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpMatchingParams MatchmakingMode=%d"), p->MatchmakingMode);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpMatchingParams PrivacyLevel=%d"), p->PrivacyLevel);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpMatchingParams GameMode='%s'"), *p->GameMode);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpMatchingParams MapName='%s'"), *p->MapName);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpMatchingParams MatchingTimeout=%f"), p->MatchingTimeout);
	//BestSessionIdx
#endif
}

void ARugbyGameSession::DumpSearchSettings(FOnlineSearchSettings* s)
{
#if !UE_BUILD_SHIPPING
	TArray<FName> keys;
	s->SearchParams.GetKeys(keys);
	keys.Sort();
	//FName, FOnlineSessionSearchParam
	for (FName& k : keys)
	{
		FOnlineSessionSearchParam* v = s->SearchParams.Find(k);
		UE_LOG(LogTemp, Display, TEXT("ARugbyGameSession::DumpSearchSettings '%s'='%s'"), *k.ToString(), *v->ToString());
	}
#endif
}

void ARugbyGameSession::DumpSessionSettings(TSharedPtr<FRugbyOnlineSessionSettings> s)
{
#if !UE_BUILD_SHIPPING
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings NumPublicConnections=%d"), s->NumPublicConnections);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings NumPrivateConnections=%d"), s->NumPrivateConnections);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bShouldAdvertise=%d"), s->bShouldAdvertise);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bAllowJoinInProgress=%d"), s->bAllowJoinInProgress);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bIsLANMatch=%d"), s->bIsLANMatch);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bIsDedicated=%d"), s->bIsDedicated);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bUsesStats=%d"), s->bUsesStats);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bAllowInvites=%d"), s->bAllowInvites);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bUsesPresence=%d"), s->bUsesPresence);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bAllowJoinViaPresence=%d"), s->bAllowJoinViaPresence);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bAllowJoinViaPresenceFriendsOnly=%d"), s->bAllowJoinViaPresenceFriendsOnly);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bAntiCheatProtected=%d"), s->bAntiCheatProtected);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings BuildUniqueId=%d"), s->BuildUniqueId);


	//FSessionSettings Settings;
	//FName, FOnlineSessionSetting
	TArray<FName> keys;
	s->Settings.GetKeys(keys);
	keys.Sort();
	for (FName& k : keys)
	{
		FOnlineSessionSetting* v = s->Settings.Find(k);
		UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings '%s'='%s'"), *k.ToString(), *v->ToString());
	}
#endif
}


void ARugbyGameSession::DumpSessionSettings(FRugbyOnlineSessionSettings& s)
{
#if !UE_BUILD_SHIPPING
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings NumPublicConnections=%d"), s.NumPublicConnections);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings NumPrivateConnections=%d"), s.NumPrivateConnections);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bShouldAdvertise=%d"), s.bShouldAdvertise);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bAllowJoinInProgress=%d"), s.bAllowJoinInProgress);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bIsLANMatch=%d"), s.bIsLANMatch);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bIsDedicated=%d"), s.bIsDedicated);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bUsesStats=%d"), s.bUsesStats);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bAllowInvites=%d"), s.bAllowInvites);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bUsesPresence=%d"), s.bUsesPresence);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bAllowJoinViaPresence=%d"), s.bAllowJoinViaPresence);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bAllowJoinViaPresenceFriendsOnly=%d"), s.bAllowJoinViaPresenceFriendsOnly);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings bAntiCheatProtected=%d"), s.bAntiCheatProtected);
	UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings BuildUniqueId=%d"), s.BuildUniqueId);


	//FSessionSettings Settings;
	//FName, FOnlineSessionSetting
	TArray<FName> keys;
	s.Settings.GetKeys(keys);
	keys.Sort();
	for (FName& k : keys)
	{
		FOnlineSessionSetting* v = s.Settings.Find(k);
		UE_LOG(LogTemp, Warning, TEXT("ARugbyGameSession::DumpSessionSettings '%s'='%s'"), *k.ToString(), *v->ToString());
	}
#endif
}

void ARugbyGameSession::DumpSearchSession(TSharedPtr<class FRugbyOnlineSearchSettings> s)
{
#if !UE_BUILD_SHIPPING
	//FOnlineSearchSettings QuerySettings;
	DumpSearchSettings(&(s->QuerySettings));
#endif
}

FString ARugbyGameSession::GetOnlineDebugKey()
{
	//Copy from AFL
	FString tempOnlineDebugKey;
	FParse::Value(FCommandLine::Get(), TEXT("LanKey="), tempOnlineDebugKey);

	if (!tempOnlineDebugKey.IsEmpty())
	{
		SIFApplication::GetApplication()->SetLanKeyValue(tempOnlineDebugKey);
	}

	return SIFApplication::GetApplication()->GetLanKeyValue();
}

bool ARugbyGameSession::HostSession(TSharedPtr<const FUniqueNetId> UserId, FName InSessionName, const FString& GameType, const FString& MapName, bool bIsLAN, bool bIsPresence, EMatchmakingMode MatchmakingMode, EPrivacyLevel PrivacyLevel, uint8 RuleType)
{
	IOnlineSubsystem* const OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		CurrentSessionParams->Clear();
		URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(GetWorld()->GetGameInstance());

#if PLATFORM_XBOXONE
		FString SessionTypeName = MatchmakingMode == EMatchmakingMode::Ranked ? XBOXONE_RANKEDMATCH_TEMPLATE_NAME : XBOXONE_UNRANKEDMATCH_TEMPLATE_NAME;
		FString SessionHopperName = MatchmakingMode == EMatchmakingMode::Ranked ? XBOXONE_RANKEDMATCH_HOPPER_NAME : XBOXONE_UNRANKEDMATCH_HOPPER_NAME;
		if (gameInstance->GetLobbyPrivacy() == EPrivacyLevel::InviteOnly)
		{
			SessionTypeName = XBOXONE_PRIVATEMATCH_TEMPLATE_NAME;
			SessionHopperName = XBOXONE_PRIVATEMATCH_HOPPER_NAME;
		}
		CurrentSessionParams->SessionTemplateName = SessionTypeName;

		CurrentSessionParams->MatchingHopper = FString("QuickMatch");
#else
		CurrentSessionParams->SessionTemplateName = MatchmakingMode == EMatchmakingMode::Ranked ? FString("RankedMatchSession") : FString("QuickMatchSession");
		CurrentSessionParams->MatchingHopper = MatchmakingMode == EMatchmakingMode::Ranked ? FString("RankedMatch") : FString("QuickMatch");
#endif
		SessionName = InSessionName;
		CurrentSessionParams->SessionName = InSessionName;
		CurrentSessionParams->bIsLAN = bIsLAN;
		CurrentSessionParams->bIsPresence = true;
		CurrentSessionParams->UserId = UserId;


		CurrentSessionParams->MatchmakingMode = MatchmakingMode;


		//This needs to be public for Steam if we are a private match
		CurrentSessionParams->PrivacyLevel = PrivacyLevel;

		CurrentSessionParams->GameMode = GameType;
		//CurrentSessionParams->MapName = MapName;

		CurrentSessionParams->MatchingTimeout = 120.0f;


		IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
		if (Sessions.IsValid() && CurrentSessionParams->UserId.IsValid())
		{
			MaxPlayers = (CurrentSessionParams->MatchmakingMode == EMatchmakingMode::Ranked ? RANKED_NUM_PLAYERS : DEFAULT_NUM_PLAYERS);

			(*HostSettings) = MakeShareable(new FRugbyOnlineSessionSettings(CurrentSessionParams->bIsLAN, CurrentSessionParams->bIsPresence, MaxPlayers, PrivacyLevel != EPrivacyLevel::Public));

			BuildHostSettings();

			(*HostSettings)->Set(SETTING_GAMEMODE, CurrentSessionParams->GameMode, EOnlineDataAdvertisementType::ViaOnlineService);
			(*HostSettings)->Set(SETTING_MATCHING_TIMEOUT, CurrentSessionParams->MatchingTimeout, EOnlineDataAdvertisementType::ViaOnlineService);
#if PLATFORM_SWITCH

			(*HostSettings)->Set(SEARCH_MATCHMODE, (int32)CurrentSessionParams->MatchmakingMode, EOnlineDataAdvertisementType::ViaOnlineService, 0);
			(*HostSettings)->Set(SEARCH_PRIVACYLEVEL, (int32)CurrentSessionParams->PrivacyLevel, EOnlineDataAdvertisementType::ViaOnlineService, 1);

			/*if (CurrentSessionParams->PrivacyLevel == EPrivacyLevel::Public)
			{
			(*HostSettings)->Set(SEARCH_INPROGRESS, 0, EOnlineDataAdvertisementType::ViaOnlineService,2);
			(*HostSettings)->Set(SEARCH_NETWORK, SEARCH_NETWORK_VALUE, EOnlineDataAdvertisementType::ViaOnlineService,3);
			(*HostSettings)->Set(SEARCH_OPEN_SLOTS, MaxPlayers, EOnlineDataAdvertisementType::ViaOnlineService,4);
			(*HostSettings)->Set(SEARCH_NETWORK_BUILD_NUMBER, SEARCH_BUILD, EOnlineDataAdvertisementType::ViaOnlineService,5);
			}*/
#else
			(*HostSettings)->Set(SETTING_MAPNAME, CurrentSessionParams->MapName, EOnlineDataAdvertisementType::ViaOnlineService);
			(*HostSettings)->Set(SETTING_MATCHING_HOPPER, CurrentSessionParams->MatchingHopper, EOnlineDataAdvertisementType::DontAdvertise);

			(*HostSettings)->Set(SETTING_SESSION_TEMPLATE_NAME, CurrentSessionParams->SessionTemplateName, EOnlineDataAdvertisementType::DontAdvertise);

#if PLATFORM_WINDOWS
			(*HostSettings)->Set(SEARCH_MATCHMODE, (int32)MatchmakingMode, EOnlineDataAdvertisementType::ViaOnlineService);
#else
			(*HostSettings)->Set(SEARCH_MATCHMODE, (int32)CurrentSessionParams->MatchmakingMode, EOnlineDataAdvertisementType::ViaOnlineService);
#endif  //PLATFORM_WINDOW	

			(*HostSettings)->Set(SEARCH_PRIVACYLEVEL, (int32)CurrentSessionParams->PrivacyLevel, EOnlineDataAdvertisementType::ViaOnlineService);

			//if (CurrentSessionParams->PrivacyLevel == EPrivacyLevel::Public)
			{
				(*HostSettings)->Set(SEARCH_NETWORK, SEARCH_NETWORK_VALUE, EOnlineDataAdvertisementType::ViaOnlineService);
				//I don t like this but for now we will start the session locked and only unlock it once the lobby is valid, this should stop clients from tryign to join the lobby as the map is loading and failing to join.
				(*HostSettings)->Set(SEARCH_INPROGRESS, SEARCH_INPROGRESSS_VALUE, EOnlineDataAdvertisementType::ViaOnlineService);
				(*HostSettings)->Set(SEARCH_OPEN_SLOTS, MaxPlayers, EOnlineDataAdvertisementType::ViaOnlineService);
				(*HostSettings)->Set(SEARCH_NETWORK_BUILD_NUMBER, SEARCH_BUILD, EOnlineDataAdvertisementType::ViaOnlineService);
				(*HostSettings)->Set(SEARCH_RUGBY_RULES, RuleType == 0 ? SEARCH_15s_RULE_VALUE : SEARCH_7s_RULE_VALUE, EOnlineDataAdvertisementType::ViaOnlineService);
			}

#if !UE_BUILD_SHIPPING && PLATFORM_WINDOWS

			if (UOBJ_IS_VALID(gameInstance))
			{
				(*HostSettings)->Set(SEARCH_LANKEY, GetOnlineDebugKey(), EOnlineDataAdvertisementType::ViaOnlineService);
			}
#endif //!UE_BUILD_SHIPPING

#if PLATFORM_XBOXONE
			(*HostSettings)->Set(SEARCH_XBOX_LIVE_HOPPER_NAME, CurrentSessionParams->MatchingHopper, EOnlineDataAdvertisementType::DontAdvertise);
			(*HostSettings)->Set(SEARCH_XBOX_LIVE_SESSION_TEMPLATE_NAME, SessionTypeName, EOnlineDataAdvertisementType::DontAdvertise);
			FString Keyword = CUSTOMMATCHKEYWORD + SEARCH_BUILD + (RuleType == 0 ? SEARCH_15s_RULE_VALUE : SEARCH_7s_RULE_VALUE);
			(*HostSettings)->Set(SEARCH_KEYWORDS, Keyword, EOnlineDataAdvertisementType::ViaOnlineService);
#endif //!UE_BUILD_SHIPPING

#if PLATFORM_WINDOWS
			// On Switch, we don't have room for this in the session data (and it's not used anyway when searching), so there's no need to add it.
			// Can be readded if the buffer size increases.
			(*HostSettings)->Set(SEARCH_KEYWORDS, CUSTOMMATCHKEYWORD, EOnlineDataAdvertisementType::ViaOnlineService);
#endif

#endif		// !SWITCH

			if (UOBJ_IS_VALID(gameInstance))
			{
				gameInstance->SetSessionMaxPlayers(MaxPlayers);
			}

#if PLATFORM_WINDOWS
			//(*HostSettings)->bUsesPresence = false;
#endif
			OnCreateSessionCompleteDelegateHandle = Sessions->AddOnCreateSessionCompleteDelegate_Handle(OnCreateSessionCompleteDelegate);
			DumpMatchingParams(CurrentSessionParams);
			DumpSessionSettings(*HostSettings);
			gameInstance->SetHostSetting(*HostSettings);
			return Sessions->CreateSession(*CurrentSessionParams->UserId, CurrentSessionParams->SessionName, *(*HostSettings));
		}
	}
	return false;
}

bool ARugbyGameSession::BeginQuickMatchSearch(TSharedPtr<const FUniqueNetId> UserId, FName InSessionName, const FString& GameType, const FString& MapName, bool bIsLAN, bool bIsPresence, EMatchmakingMode MatchmakingMode, EPrivacyLevel PrivacyLevel, uint8 RuleType)
{
	URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(GetWorld()->GetGameInstance());
	bQuickmatchSearchRequestCanceled = false;
	bUsedInputToCancelQuickmatchSearch = false;

	auto Sessions = Online::GetSessionInterface();
	if (!Sessions.IsValid())
	{
		UE_LOG(LogOnline, Warning, TEXT("Quick match is not supported: couldn't find online session interface."));
		return false;
	}
	CurrentSessionParams->Clear();
	//FString GSName = RUGAMESESSIONNAME;
	SessionName = RUGAMESESSIONNAME;//FName(*GSName);
	CurrentSessionParams->SessionName = SessionName;
	CurrentSessionParams->bIsLAN = bIsLAN;
	CurrentSessionParams->bIsPresence = bIsPresence;
	CurrentSessionParams->UserId = UserId;
	CurrentSessionParams->MatchmakingMode = MatchmakingMode;
	CurrentSessionParams->PrivacyLevel = PrivacyLevel;
	CurrentSessionParams->GameMode = GameType;
	CurrentSessionParams->MapName = MapName;

#if PLATFORM_XBOXONE
	FString SessionTypeName = MatchmakingMode == EMatchmakingMode::Ranked ? XBOXONE_RANKEDMATCH_TEMPLATE_NAME : XBOXONE_UNRANKEDMATCH_TEMPLATE_NAME;
	FString SessionHopperName = MatchmakingMode == EMatchmakingMode::Ranked ? XBOXONE_RANKEDMATCH_HOPPER_NAME : XBOXONE_UNRANKEDMATCH_HOPPER_NAME;
	if (gameInstance->GetLobbyPrivacy() == EPrivacyLevel::InviteOnly)
	{
		SessionTypeName = XBOXONE_PRIVATEMATCH_TEMPLATE_NAME;
		SessionHopperName = XBOXONE_PRIVATEMATCH_HOPPER_NAME;
	}
	CurrentSessionParams->SessionTemplateName = SessionTypeName;

	CurrentSessionParams->MatchingHopper = FString("QuickMatch");
#else
	CurrentSessionParams->SessionTemplateName = FString("GameSession");
	CurrentSessionParams->MatchingHopper = FString("QuickMatch");
#endif

	CurrentSessionParams->MatchingTimeout = 120.0f;

	MaxPlayers = (CurrentSessionParams->MatchmakingMode == EMatchmakingMode::Ranked ? RANKED_NUM_PLAYERS : DEFAULT_NUM_PLAYERS);

	gameInstance->SetSessionMaxPlayers(MaxPlayers);

	FRugbyOnlineSessionSettings NewHostSettings(CurrentSessionParams->bIsLAN, CurrentSessionParams->bIsPresence, MaxPlayers);

	//===============================================================Start Host Section====================================================================================================
	//(*HostSettings) = MakeShareable(new FRugbyOnlineSessionSettings(CurrentSessionParams->bIsLAN, CurrentSessionParams->bIsPresence, MaxPlayers));
	// TODO: We could probably break this up into its own build matchmaking/privacy settings, and expand this function to build out the rest of the host settings.

	NewHostSettings.bAllowJoinInProgress = true;

	if (CurrentSessionParams->MatchmakingMode == EMatchmakingMode::Ranked)
	{
		NewHostSettings.bShouldAdvertise = true;
		NewHostSettings.bAllowInvites = false;
		NewHostSettings.bAllowJoinViaPresence = false;
		NewHostSettings.bAllowJoinViaPresenceFriendsOnly = false;
	}
	else
	{
		switch (CurrentSessionParams->PrivacyLevel)
		{
			// Anyone can join via matchmaking, invites, or presence join
		case EPrivacyLevel::Public:
		{
			NewHostSettings.bShouldAdvertise = true;
			NewHostSettings.bAllowInvites = true;
			NewHostSettings.bAllowJoinViaPresence = true;
			NewHostSettings.bAllowJoinViaPresenceFriendsOnly = false;
		}
		break;
		// Only friends can join your lobby via presence, or you invite them
		case EPrivacyLevel::Friends:
			NewHostSettings.bShouldAdvertise = true;
			NewHostSettings.bAllowJoinViaPresenceFriendsOnly = true;
			break;
			// People can only join if they get an invite.
		case EPrivacyLevel::InviteOnly:
		{
			NewHostSettings.bShouldAdvertise = false;
			NewHostSettings.bAllowInvites = true;
			NewHostSettings.bAllowJoinViaPresence = false;
			NewHostSettings.bAllowJoinViaPresenceFriendsOnly = false;
		}
		break;
		default:
			break;
		}
	}

	NewHostSettings.Set(SETTING_GAMEMODE, CurrentSessionParams->GameMode, EOnlineDataAdvertisementType::ViaOnlineService);
	NewHostSettings.Set(SETTING_MATCHING_TIMEOUT, CurrentSessionParams->MatchingTimeout, EOnlineDataAdvertisementType::ViaOnlineService);

	NewHostSettings.Set(SEARCH_MATCHMODE, (int32)CurrentSessionParams->MatchmakingMode, EOnlineDataAdvertisementType::ViaOnlineService, 0);
	NewHostSettings.Set(SEARCH_PRIVACYLEVEL, (int32)CurrentSessionParams->PrivacyLevel, EOnlineDataAdvertisementType::ViaOnlineService, 1);
	NewHostSettings.Set(SEARCH_INPROGRESS, 0, EOnlineDataAdvertisementType::ViaOnlineService, 2);
	NewHostSettings.Set(SEARCH_NETWORK_BUILD_NUMBER, SEARCH_BUILD, EOnlineDataAdvertisementType::ViaOnlineService, 3);
	NewHostSettings.Set(SEARCH_RUGBY_RULES, RuleType == 0 ? SEARCH_15s_RULE_VALUE : SEARCH_7s_RULE_VALUE, EOnlineDataAdvertisementType::ViaOnlineService, 4);


#if PLATFORM_XBOXONE

	if (CurrentSessionParams->PrivacyLevel == EPrivacyLevel::Public)
	{
		NewHostSettings.Set(SEARCH_OPEN_SLOTS, MaxPlayers, EOnlineDataAdvertisementType::ViaOnlineService, 4);
		NewHostSettings.Set(SEARCH_NETWORK, SEARCH_NETWORK_VALUE, EOnlineDataAdvertisementType::ViaOnlineService, 5);
	}

	NewHostSettings.Set(SEARCH_XBOX_LIVE_HOPPER_NAME, CurrentSessionParams->MatchingHopper, EOnlineDataAdvertisementType::DontAdvertise);
	NewHostSettings.Set(SEARCH_XBOX_LIVE_SESSION_TEMPLATE_NAME, SessionTypeName, EOnlineDataAdvertisementType::DontAdvertise);
	NewHostSettings.Set(SEARCH_KEYWORDS, CUSTOMMATCHKEYWORD + FString::FromInt(SEARCH_BUILD), EOnlineDataAdvertisementType::ViaOnlineService);
#endif //!UE_BUILD_SHIPPING



	//=======================================================================End Host Section================================================================================================

	//========================================================================Start Search Section===========================================================================================
	SearchSettings = MakeShareable(new FRugbyOnlineSearchSettings(bIsLAN, bIsPresence));


	SearchSettings->QuerySettings.Set(SEARCH_MATCHMODE, (int32)MatchmakingMode, EOnlineComparisonOp::Equals, 0);
	SearchSettings->QuerySettings.Set(SEARCH_PRIVACYLEVEL, (int32)gameInstance->GetLobbyPrivacy(), EOnlineComparisonOp::Equals, 1);
	SearchSettings->QuerySettings.Set(SEARCH_INPROGRESS, 0, EOnlineComparisonOp::Equals, 2);
	SearchSettings->QuerySettings.Set(SEARCH_NETWORK_BUILD_NUMBER, SEARCH_BUILD, EOnlineComparisonOp::Equals, 3);
	SearchSettings->QuerySettings.Set(SEARCH_RUGBY_RULES, RuleType == 0 ? SEARCH_15s_RULE_VALUE : SEARCH_7s_RULE_VALUE, EOnlineComparisonOp::Equals, 4);

#if PLATFORM_XBOXONE	

	if (gameInstance->GetLobbyPrivacy() == EPrivacyLevel::InviteOnly)
	{
		SessionTypeName = XBOXONE_PRIVATEMATCH_TEMPLATE_NAME;
		SessionHopperName = XBOXONE_PRIVATEMATCH_HOPPER_NAME;
	}

	SearchSettings->QuerySettings.Set(SETTING_GAMEMODE, CurrentSessionParams->GameMode, EOnlineComparisonOp::Equals);
	SearchSettings->QuerySettings.Set(SEARCH_XBOX_LIVE_HOPPER_NAME, SessionHopperName, EOnlineComparisonOp::Equals);
	SearchSettings->QuerySettings.Set(SEARCH_XBOX_LIVE_SESSION_TEMPLATE_NAME, SessionTypeName, EOnlineComparisonOp::Equals);
	SearchSettings->QuerySettings.Set(SEARCH_KEYWORDS, CUSTOMMATCHKEYWORD + FString::FromInt(SEARCH_BUILD), EOnlineComparisonOp::Equals);
	//SearchSettings->QuerySettings.Set(SEARCH_USER, gameInstance->GetPrimaryPlayerController()->GetPlayerState()->GetPlayerName(), EOnlineComparisonOp::NotEquals);
	SearchSettings->QuerySettings.Set(SETTING_MAX_RESULT, 100, EOnlineComparisonOp::NotEquals);

	if (CurrentSessionParams->PrivacyLevel == EPrivacyLevel::Public)
	{
		SearchSettings->QuerySettings.Set(SEARCH_OPEN_SLOTS, gameInstance->GetNumLocalPlayers(), EOnlineComparisonOp::GreaterThanEquals, 4);
		SearchSettings->QuerySettings.Set(SEARCH_NETWORK, SEARCH_NETWORK_VALUE, EOnlineComparisonOp::Equals, 5);
	}

#endif	

	SearchSettings->TimeoutInSeconds = 120.0f;
	//====================================================================End Search Section===============================================================================================	


	Sessions->ClearOnMatchmakingCompleteDelegate_Handle(OnMatchmakingCompleteDelegateHandle);
	OnMatchmakingCompleteDelegateHandle = Sessions->AddOnMatchmakingCompleteDelegate_Handle(OnMatchmakingCompleteDelegate);

	// Perform matchmaking with all local players
	TArray<TSharedRef<const FUniqueNetId>> LocalPlayerIds;
	for (int32 i = 0; i < gameInstance->GetNumLocalPlayers(); ++i)
	{
		FUniqueNetIdRepl PlayerId = gameInstance->GetLocalPlayerByIndex(i)->GetPreferredUniqueNetId();
		if (PlayerId.IsValid())
		{
			LocalPlayerIds.Add((*PlayerId).AsShared());
		}
	}

	TSharedRef<FOnlineSessionSearch> SearchSettingsRef = SearchSettings.ToSharedRef();

	(*HostSettings) = MakeShareable(new FRugbyOnlineSessionSettings(CurrentSessionParams->bIsLAN, bIsPresence, MaxPlayers));

	(*HostSettings)->bShouldAdvertise = NewHostSettings.bShouldAdvertise;
	(*HostSettings)->bAllowInvites = NewHostSettings.bAllowInvites;
	(*HostSettings)->bAllowJoinViaPresence = NewHostSettings.bAllowJoinViaPresence;
	(*HostSettings)->bAllowJoinViaPresenceFriendsOnly = NewHostSettings.bAllowJoinViaPresenceFriendsOnly;
	(*HostSettings)->Settings = NewHostSettings.Settings;
	(*HostSettings)->bUsesPresence = NewHostSettings.bUsesPresence;
	(*HostSettings)->bAllowJoinInProgress = NewHostSettings.bAllowJoinInProgress;
	(*HostSettings)->NumPrivateConnections = NewHostSettings.NumPrivateConnections;
	(*HostSettings)->NumPublicConnections = NewHostSettings.NumPublicConnections;

	//DumpSessionSettings((*HostSettings));
	//FOnlineSessionSearch blah = SearchSettingsRef.Get();
	//DumpSearchSettings(&blah.QuerySettings);

	gameInstance->SetHostSetting((*HostSettings));

	if (!Sessions->StartMatchmaking(LocalPlayerIds, SessionName, NewHostSettings, SearchSettingsRef))
	{
		OnMatchmakingComplete(SessionName, false);
		return false;
	}

	return true;
}

void ARugbyGameSession::FindFriends()
{
	UE_LOG(LogOnline, Warning, TEXT("ARugbyGameSession::FindFriends"));
	IOnlineFriendsPtr FriendsInfo = Online::GetFriendsInterface();

	if (FriendsInfo)
	{
		FriendsInfo->ReadFriendsList(0, "default", FindFriendsDone);
	}
}

void ARugbyGameSession::OnFoundFriends(int32 LocalUserNum, bool bSucceeded, const FString& ListName, const FString& ErrorStr)
{
	UE_LOG(LogOnline, Warning, TEXT("ARugbyGameSession::OnFoundFriends()"));

	OnlineFriends.Empty();

	if (bSucceeded)
	{
		UE_LOG(LogOnline, Warning, TEXT("ARugbyGameSession::OnFoundFriends() bSucceeded"));
		IOnlineFriendsPtr FriendsInfo = Online::GetFriendsInterface();

		if (FriendsInfo)
		{
			//// GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL remove ERICTOTEST Friend  interface haas changed
			TArray<TSharedRef<FOnlineFriend>> OutFriends; 
			bool bGetFriendSucceed = FriendsInfo->GetFriendsList(LocalUserNum, ListName, OutFriends);
			UE_LOG(LogOnline, Warning, TEXT("ARugbyGameSession::OnFoundFriends() Getting Friends list %d"), bGetFriendSucceed);
			if ( bGetFriendSucceed)
			{
				for (int32 i = 0; i < OutFriends.Num(); i++)
				{
					FRugbyFriendSessionInfo FoundInfo;
					FoundInfo.AccountID = OutFriends[i]->GetUserId();
					FoundInfo.NickName = OutFriends[i]->GetDisplayName();
					FOnlineUserPresence FriendPresence = OutFriends[i]->GetPresence();
					UE_LOG(LogOnlineFriend, Warning,
					       TEXT(
						       "ARugbyGameSession::OnFoundFriends() is_online: %d, is_playing: %d, is_playingthisgame : %d , bIsJoinable : %d bHasVoiceSupport: %d , Status: %s"
					       ), FriendPresence.bIsOnline, FriendPresence.bIsPlaying, FriendPresence.bIsPlayingThisGame,
					       FriendPresence.bIsJoinable, FriendPresence.bHasVoiceSupport,
					       *FriendPresence.Status.ToDebugString());

#if PLATFORM_SWITCH
					// GG WW AFL RUGBY
					// when running the Switch build, we couldn't get the AppId for switch,
					// so the bIsJoinable and bIsPlayingThisGame are always false.  Check OnlineFriendSwitch.cpp@L29
                   	// set it to online playing if the player is online.
					// ERICTOTEST I dont know why Rugby map the online playing state to a magic number 2,
					// a reference to check the state number is in WWUIPopulatorSwitchFriendPlay.cpp
					FoundInfo.OnlineMode = FriendPresence.bIsOnline ? EOnlinePresenceState::Away : EOnlinePresenceState::Offline;
#else
					FoundInfo.OnlineMode = (FriendPresence.bIsJoinable && FriendPresence.bIsPlayingThisGame) ? EOnlinePresenceState::Away : EOnlinePresenceState::Offline;
#endif	
#if PLATFORM_SWITCH
					TArray<FFriendSessionInfo> SwitchFriendSessionInfo = FriendsInfo->GetFriendSessionInfo();
					if (i < SwitchFriendSessionInfo.Num())
						FoundInfo.ProfileIcon = SwitchFriendSessionInfo[i].ProfileIcon;
					else
						FoundInfo.ProfileIcon = nullptr;
#else
					FoundInfo.ProfileIcon = nullptr;
#endif
					OnlineFriends.Add(FoundInfo);
				}
			}
			/*
			TArray<FFriendSessionInfo> TempInfo = FriendsInfo->GetFriendSessionInfo();
			UE_LOG(LogOnline, Warning, TEXT("ARugbyGameSession::OnFoundFriends() Getting Friends list"));

			for (int32 i = 0; i < TempInfo.Num(); i++)
			{
				FRugbyFriendSessionInfo FoundInfo;
				FoundInfo.AccountID = TempInfo[i].AccountID;
				FoundInfo.NickName = TempInfo[i].NickName;
				FoundInfo.OnlineMode = TempInfo[i].OnlineMode;
				FoundInfo.ProfileIcon = TempInfo[i].ProfileIcon;

				OnlineFriends.Add(FoundInfo);
			}*/
		}
	}

	UE_LOG(LogOnline, Warning, TEXT("ARugbyGameSession::OnFoundFriends() FindFriendsCompleteEvent.ExecuteIfBound()"));
	FindFriendsCompleteEvent.ExecuteIfBound(bSucceeded, OnlineFriends);

	if (FindFriendsCompleteEvent.IsBound())
	{
		UE_LOG(LogOnline, Warning, TEXT("ARugbyGameSession::OnFoundFriends() Clean up FindFriendsCompleteEvent"));
		FindFriendsCompleteEvent.Unbind();
	}
}

bool ARugbyGameSession::JoinFriendSession(TSharedPtr<const FUniqueNetId> UserID)
{
	UE_LOG(LogOnline, Warning, TEXT("ARugbyGameSession::JoinFriendSession"));
	IOnlineSubsystem* const OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
		if (Sessions)
		{
			URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(GetWorld()->GetGameInstance());

			OnFindFriendSessionCompleteDelegateHandle = Sessions->AddOnFindFriendSessionCompleteDelegate_Handle(0, OnFindFriendSessionCompleteDelegate);

			//True if we started an async task
			//// GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL remove ERICTOTEST Friend  interface haas changed
			
			if (Sessions->FindFriendSession(0, *UserID.Get()))
			{
				UE_LOG(LogOnline, Warning, TEXT("ARugbyGameSession::JoinFriendSession() starting async job"));
				return true;
			}
			else
			{
				UE_LOG(LogOnline, Warning, TEXT("ARugbyGameSession::JoinFriendSession No Friends To Join"));
			}
		}
	}

	return false;
}

void ARugbyGameSession::OnFindFriendSessionComplete(int32 PlayerNum, bool bSucceeded, const TArray<FOnlineSessionSearchResult>& Seesions)
{
	UE_LOG(LogOnline, Warning, TEXT("ARugbyGameSession::OnFindFriendSessionComplete"));

	auto OnlineSessions = Online::GetSessionInterface();
	URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(GetWorld()->GetGameInstance());

	OnlineSessions->ClearOnFindFriendSessionCompleteDelegate_Handle(0, OnFindFriendSessionCompleteDelegateHandle);

	if (!UOBJ_IS_VALID(gameInstance) || Seesions.Num() == 0)
	{
		UE_LOG(LogOnline, Warning, TEXT(" ARugbyGameSession::OnFindFriendSessionComplete() no Seesions"));

		//gameInstance->DealMenuAction(SCREEN_POP_MENUSCREEN, Modals_UI::ModalOnlineLoading);
		OnJoinSessionComplete(RUGAMESESSIONNAME/*FName(*GAMESESSIONNAME)*/, EOnJoinSessionCompleteResult::SessionDoesNotExist);
		return;
	}


	FOnlineSessionSearchResult FriendSearchResult = Seesions[0];

	TArray<FName> KeyList;

	int32 KeyCount = FriendSearchResult.Session.SessionSettings.Settings.GetKeys(KeyList);

	FNamedOnlineSession* NamedSessionFound = OnlineSessions->GetNamedSession(RUGAMESESSIONNAME/*FName(*GAMESESSIONNAME)*/);

	if (!FriendSearchResult.Session.SessionSettings.bAllowInvites || !FriendSearchResult.Session.SessionSettings.bAllowJoinInProgress)
	{
		OnJoinSessionComplete(RUGAMESESSIONNAME/*FName(*GAMESESSIONNAME)*/, EOnJoinSessionCompleteResult::SessionDoesNotExist);
		UE_LOG(LogOnline, Warning, TEXT("Can not join invites not allowed!!"));
		return;
	}

	if (KeyCount > 0)
	{
		UE_LOG(LogOnline, Warning, TEXT("Found Session Settings: %d"), KeyCount);
		for (size_t i = 0; i < KeyCount; i++)
		{
			UE_LOG(LogOnline, Warning, TEXT("Session Setting Name: %s"), *KeyList[i].ToString());
		}
		int32 MatchmakingMode = (int32)EMatchmakingMode::Unranked;
		int32 InProgress = 0;

		FriendSearchResult.Session.SessionSettings.Get(SEARCH_INPROGRESS, InProgress);
		FriendSearchResult.Session.SessionSettings.Get(SEARCH_MATCHMODE, MatchmakingMode);

		if ((EMatchmakingMode)MatchmakingMode == EMatchmakingMode::Ranked)
		{
			OnJoinSessionComplete(RUGAMESESSIONNAME/*FName(*GAMESESSIONNAME)*/, EOnJoinSessionCompleteResult::SessionDoesNotExist);
			UE_LOG(LogOnline, Warning, TEXT("Can not join a ranked lobby!!"));
			return;
		}

		if (InProgress == 1)
		{
			OnJoinSessionComplete(RUGAMESESSIONNAME/*FName(*GAMESESSIONNAME)*/, EOnJoinSessionCompleteResult::SessionDoesNotExist);
			UE_LOG(LogOnline, Warning, TEXT("Can not join a match that is in progress!!"));
			return;
		}
	}


	//if (Seesions[0].IsValid())
	{
		APlayerController* pPrimaryController = gameInstance->GetPrimaryPlayerController();
		if (UOBJ_IS_VALID(pPrimaryController) && UOBJ_IS_VALID(pPrimaryController->GetLocalPlayer()) && pPrimaryController->GetLocalPlayer()->GetPreferredUniqueNetId().IsValid())
		{
			TSharedPtr<const FUniqueNetId> PlayerUserId = pPrimaryController->GetLocalPlayer()->GetPreferredUniqueNetId().GetUniqueNetId();
			OnJoinSessionCompleteDelegate = FOnJoinSessionCompleteDelegate::CreateUObject(this, &ARugbyGameSession::OnJoinSessionComplete);

			if (JoinSession(PlayerUserId, RUGAMESESSIONNAME/* FName(*GAMESESSIONNAME)*/, Seesions[0]))
			{
				UE_LOG(LogOnline, Warning, TEXT("Join friend session was successful."));
				return;
			}
			else
			{
				UE_LOG(LogOnline, Warning, TEXT("Join friend session was unsuccessful."));
			}
		}
	}

	OnJoinSessionComplete(RUGAMESESSIONNAME/*FName(*GAMESESSIONNAME)*/, EOnJoinSessionCompleteResult::UnknownError);
}

void ARugbyGameSession::OnCancelMatchmakingComplete(FName SessionName, bool bWasSuccessful)
{
	auto Sessions = Online::GetSessionInterface();
	if (Sessions.IsValid())
	{
		Sessions->ClearOnCancelMatchmakingCompleteDelegate_Handle(OnCancelMatchmakingCompleteDelegateHandle);
	}

	bUsedInputToCancelQuickmatchSearch = false;

	if (!bWasSuccessful)
	{
		UE_LOG(LogOnline, Warning, TEXT("Failed to stop matchmaking."));
	}
	else
	{
		UE_LOG(LogOnline, Warning, TEXT("Auto matchmaking has been canceled."));
	}

	URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(GetWorld()->GetGameInstance());
	if (UOBJ_IS_VALID(gameInstance))
	{
		//gameInstance->DealMenuAction(SCREEN_POP_MENUSCREEN, Modals_UI::ModalOnlineLoading);
	}

}

void ARugbyGameSession::OnMatchmakingComplete(FName SessionName, bool bWasSuccessful)
{
	auto Sessions = Online::GetSessionInterface();
	URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(GetWorld()->GetGameInstance());
	if (!UOBJ_IS_VALID(gameInstance))
	{
		return;
	}

	if (Sessions.IsValid())
	{
		Sessions->ClearOnMatchmakingCompleteDelegate_Handle(OnMatchmakingCompleteDelegateHandle);

		if (bQuickmatchSearchRequestCanceled && bUsedInputToCancelQuickmatchSearch)
		{
			bQuickmatchSearchRequestCanceled = false;
			// Clean up the session in case we get this event after canceling

			if (bWasSuccessful && Sessions.IsValid())
			{
				APlayerController* pPrimaryController = gameInstance->GetPrimaryPlayerController();
				if (UOBJ_IS_VALID(pPrimaryController) && UOBJ_IS_VALID(pPrimaryController->GetLocalPlayer()) && pPrimaryController->GetLocalPlayer()->GetPreferredUniqueNetId().IsValid())
				{
					Sessions->DestroySession(RUGAMESESSIONNAME/*FName(*GAMESESSIONNAME)*/);
				}
			}

			gameInstance->GetMatchmakingManager()->Cancel();
			gameInstance->HideOnlineScreens();
			return;
		}

		if (!bWasSuccessful)
		{
			UE_LOG(LogOnline, Warning, TEXT("Matchmaking was unsuccessful."));

			//if (!gameInstance->TryMatchMakingAgain())
			{
				gameInstance->GetMatchmakingManager()->Cancel();
				gameInstance->HideOnlineScreens();
				gameInstance->ShowErrorUIPopup("[ID_MATCHMAKING]", "[ID_MATCHMAKING_UNSUCCESSFUL]");
			}

			return;
		}

		UE_LOG(LogOnline, Log, TEXT("Matchmaking successful! Session name is %s."), *SessionName.ToString());

		/* Removing this for now so i can keep testing
				if (gameInstance->GetPrimaryPlayerController() == NULL)
				{
					UE_LOG(LogOnline, Warning, TEXT("OnMatchmakingComplete: No owner."));

					gameInstance->GetMatchmakingManager()->Cancel();
					gameInstance->HideOnlineScreens();
					gameInstance->ShowErrorUIPopup("Match Making", "Matchmaking was unsuccessful, No Session Owner.");
					return;
				}*/

		auto MatchmadeSession = Sessions->GetNamedSession(SessionName);

		if (!MatchmadeSession)
		{
			UE_LOG(LogOnline, Warning, TEXT("OnMatchmakingComplete: No session."));

			gameInstance->GetMatchmakingManager()->Cancel();
			gameInstance->HideOnlineScreens();
			gameInstance->ShowErrorUIPopup("[ID_MATCHMAKING]", "[ID_MATCHMAKING_UNSUCCESSFUL_SESSION]");
			return;
		}

		if (!MatchmadeSession->OwningUserId.IsValid())
		{
			UE_LOG(LogOnline, Warning, TEXT("OnMatchmakingComplete: No session owner/host."));

			gameInstance->GetMatchmakingManager()->Cancel();
			gameInstance->HideOnlineScreens();
			gameInstance->ShowErrorUIPopup("[ID_MATCHMAKING]", "[ID_MATCHMAKING_UNSUCCESSFUL_HOST]");
			return;
		}

		UE_LOG(LogOnline, Log, TEXT("OnMatchmakingComplete: Session host is %d."), *MatchmadeSession->OwningUserId->ToString());

		if (UOBJ_IS_VALID(gameInstance))
		{
			auto Subsystem = IOnlineSubsystem::Get();
			if (Subsystem != nullptr)
			{
				if (MatchmadeSession->bHosting)
				{
					UE_LOG(LogOnline, Warning, TEXT("OnMatchmakingComplete: Host Session"));

					// This console is the host, start the map.
					gameInstance->HostSession_Switch();
					return;
				}
				else
				{
					UE_LOG(LogOnline, Warning, TEXT("OnMatchmakingComplete: Join Session"));

					// We are the client, join the host.
					gameInstance->JoinSession_Switch(SessionName);
					return;
				}
			}
			else
			{
				UE_LOG(LogOnline, Warning, TEXT("OnMatchmakingComplete: Subsystem invlaid"));
			}
		}

	}

	gameInstance->GetMatchmakingManager()->Cancel();
	gameInstance->HideOnlineScreens();
}

void ARugbyGameSession::OnConnectionCheckComplete(int32 InUserId, int32 InConnectionState)
{
	if (InConnectionState == 0)
	{
		UE_LOG(LogOnlineGame, Warning, TEXT("Connection State: %d (SCE_NP_REACHABILITY_STATE_UNAVAILABLE)"), InConnectionState);
	}
	else if (InConnectionState == 1)
	{
		UE_LOG(LogOnlineGame, Warning, TEXT("Connection State: %d (SCE_NP_REACHABILITY_STATE_AVAILABLE)"), InConnectionState);
	}
	else if (InConnectionState == 2)
	{
		URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(GetWorld()->GetGameInstance());

		//gameInstance->TimeSinceLastNetworkCheck = 0.0f;
		//gameInstance->WaitingForOnlineCheck = false;

		//Dont really need to log this as if we get this far then the connection is good
		UE_LOG(LogOnlineGame, Warning, TEXT("Connection State: %d (SCE_NP_REACHABILITY_STATE_REACHABLE)"), InConnectionState);
	}
}

void ARugbyGameSession::BuildHostSettings()
{
	// TODO: We could probably break this up into its own build matchmaking/privacy settings, and expand this function to build out the rest of the host settings.
	(*HostSettings)->bShouldAdvertise = true;
	
#if PLATFORM_WINDOWS && !PLATFORM_WINGDK
	(*HostSettings)->bUseLobbiesIfAvailable = true; // WWS Nick add to fix a bug in Steam in UE4.27
#endif

	if (CurrentSessionParams->MatchmakingMode == EMatchmakingMode::Ranked)
	{
#if PLATFORM_WINDOWS
		(*HostSettings)->bAllowInvites = false;
		(*HostSettings)->bAllowJoinViaPresence = false;
		(*HostSettings)->bAllowJoinViaPresenceFriendsOnly = false;
#else
		(*HostSettings)->bAllowInvites = false;
		(*HostSettings)->bAllowJoinViaPresence = false;
		(*HostSettings)->bAllowJoinViaPresenceFriendsOnly = false;
#endif
	}
	else
	{
		switch (CurrentSessionParams->PrivacyLevel)
		{
			// Anyone can join via matchmaking, invites, or presence join
		case EPrivacyLevel::Public:
		{
			(*HostSettings)->bAllowInvites = true;
			(*HostSettings)->bShouldAdvertise = true;
			(*HostSettings)->bAllowJoinViaPresence = true;
			(*HostSettings)->bAllowJoinViaPresenceFriendsOnly = false;
		}
		break;
		// Only friends can join your lobby via presence, or you invite them
		case EPrivacyLevel::Friends:
			(*HostSettings)->bAllowJoinViaPresenceFriendsOnly = true;
			break;
			// People can only join if they get an invite.
		case EPrivacyLevel::InviteOnly:
		{
#if PLATFORM_WINDOWS
			(*HostSettings)->bShouldAdvertise = true;
#else
			(*HostSettings)->bShouldAdvertise = false;
#endif
			(*HostSettings)->bAllowJoinViaPresence = false;
#if PLATFORM_WINDOWS
			(*HostSettings)->bAllowJoinViaPresenceFriendsOnly = true;
#else
			(*HostSettings)->bAllowJoinViaPresenceFriendsOnly = false;
#endif
		}
		break;
		default:
			break;
		}
	}
}

void ARugbyGameSession::OnFindSessionsComplete(bool bWasSuccessful)
{
	UE_LOG(LogOnlineGame, Warning, TEXT("OnFindSessionsComplete bSuccess: %d"), bWasSuccessful);

	IOnlineSubsystem* const OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
		if (Sessions.IsValid())
		{
			Sessions->ClearOnFindSessionsCompleteDelegate_Handle(OnFindSessionsCompleteDelegateHandle);

			UE_LOG(LogOnlineGame, Warning, TEXT("Num Search Results: %d"), SearchSettings->SearchResults.Num());
			for (int32 SearchIdx = 0; SearchIdx < SearchSettings->SearchResults.Num(); SearchIdx++)
			{
				const FOnlineSessionSearchResult& SearchResult = SearchSettings->SearchResults[SearchIdx];
				DumpSession(&SearchResult.Session);
			}

			//Break matchmaking if requested
			//if (wwPlayerManager::Get().GetGameInstance()->bMatchmakingCancelled)
			{
				//UE_LOG(LogOnlineGame, Warning, TEXT("ARugbyGameSession::OnFindSessionsComplete()-> Abort MatchMaking"));
				//wwPlayerManager::Get().GetGameInstance()->bMatchmakingCancelled = false;
				//return;
			}
		}
	}
	OnFindSessionsComplete().Broadcast(bWasSuccessful);
}

void ARugbyGameSession::ResetBestSessionVars()
{
	CurrentSessionParams->BestSessionIdx = -1;
}

void ARugbyGameSession::ChooseBestSession()
{
	// Start searching from where we left off
	for (int32 SessionIndex = CurrentSessionParams->BestSessionIdx + 1; SessionIndex < SearchSettings->SearchResults.Num(); SessionIndex++)
	{
		// Found the match that we want
		CurrentSessionParams->BestSessionIdx = SessionIndex;
		return;
	}

	CurrentSessionParams->BestSessionIdx = -1;
}

void ARugbyGameSession::StartMatchmaking()
{
	ResetBestSessionVars();
	ContinueMatchmaking();
}

void ARugbyGameSession::ContinueMatchmaking()
{
	ChooseBestSession();
	if (CurrentSessionParams->BestSessionIdx >= 0 && CurrentSessionParams->BestSessionIdx < SearchSettings->SearchResults.Num())
	{
		IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
		if (OnlineSub)
		{
			IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
			if (Sessions.IsValid() && CurrentSessionParams->UserId.IsValid())
			{
				OnJoinSessionCompleteDelegateHandle = Sessions->AddOnJoinSessionCompleteDelegate_Handle(OnJoinSessionCompleteDelegate);
				Sessions->JoinSession(*CurrentSessionParams->UserId, CurrentSessionParams->SessionName, SearchSettings->SearchResults[CurrentSessionParams->BestSessionIdx]);
			}
		}
	}
	else
	{
		OnNoMatchesAvailable();
	}
}

void ARugbyGameSession::StopMatchmaking()
{
	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
		if (Sessions.IsValid())
		{
			//wwPlayerManager::Get().GetGameInstance()->bMatchmakingCancelled = true;

			//if (wwPlayerManager::Get().GetGameInstance()->bIsMatchMaking)
			{
#if PLATFORM_SWITCH
				bQuickmatchSearchRequestCanceled = true;
				bUsedInputToCancelQuickmatchSearch = true;
				const FUniqueNetId& theID = (*CurrentSessionParams->UserId);

				if (theID.IsValid())
				{
					Sessions->CancelMatchmaking(theID, CurrentSessionParams->SessionName);
				}

				return;
#endif
				Sessions->CancelFindSessions();
			}

		}
	}
}

void ARugbyGameSession::OnNoMatchesAvailable()
{
	UE_LOG(LogOnlineGame, Warning, TEXT("Matchmaking complete, no sessions available."));
	SearchSettings = NULL;
}

void ARugbyGameSession::FindSessions(TSharedPtr<const FUniqueNetId> UserId, FName InSessionName, bool bIsLAN, bool bIsPresence, EMatchmakingMode MatchmakingMode, const FString& GameType, uint8 RuleType)
{
	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		//CurrentSessionParams = new FRugbyGameSessionParams();
		CurrentSessionParams->Clear();
		CurrentSessionParams->SessionName = InSessionName;
		CurrentSessionParams->bIsLAN = bIsLAN;
		CurrentSessionParams->bIsPresence = true;
		CurrentSessionParams->UserId = UserId;
#if PLATFORM_WINDOWS
		CurrentSessionParams->MatchmakingMode = EMatchmakingMode::Unranked;
#else
		CurrentSessionParams->MatchmakingMode = MatchmakingMode;
#endif  //PLATFORM_WINDOW

		CurrentSessionParams->GameMode = GameType;

		IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
		if (Sessions.IsValid() && CurrentSessionParams->UserId.IsValid())
		{
			SearchSettings = MakeShareable(new FRugbyOnlineSearchSettings(bIsLAN, bIsPresence));

			MaxPlayers = (CurrentSessionParams->MatchmakingMode == EMatchmakingMode::Ranked ? RANKED_NUM_PLAYERS : DEFAULT_NUM_PLAYERS);
			URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(GetWorld()->GetGameInstance());
			gameInstance->SetSessionMaxPlayers(MaxPlayers);

#if PLATFORM_SWITCH


			SearchSettings->QuerySettings.Set(SEARCH_MATCHMODE, (int32)MatchmakingMode, EOnlineComparisonOp::Equals, 0);
			SearchSettings->QuerySettings.Set(SEARCH_PRIVACYLEVEL, (int32)gameInstance->GetLobbyPrivacy(), EOnlineComparisonOp::Equals, 1);

			/*if (CurrentSessionParams->PrivacyLevel == EPrivacyLevel::Public)
			{
			SearchSettings->QuerySettings.Set(SEARCH_INPROGRESS, 0, EOnlineComparisonOp::Equals,2);
			SearchSettings->QuerySettings.Set(SEARCH_NETWORK, SEARCH_NETWORK_VALUE, EOnlineComparisonOp::Equals,3);
			SearchSettings->QuerySettings.Set(SEARCH_OPEN_SLOTS, gameInstance->GetNumLocalPlayers(), EOnlineComparisonOp::GreaterThanEquals,4);
			SearchSettings->QuerySettings.Set(SEARCH_NETWORK_BUILD_NUMBER, SEARCH_BUILD, EOnlineComparisonOp::Equals,5);
			}*/

#else


#if PLATFORM_WINDOWS
			SearchSettings->QuerySettings.Set(SEARCH_RUGBY_RULES, RuleType == 0 ? SEARCH_15s_RULE_VALUE : SEARCH_7s_RULE_VALUE, EOnlineComparisonOp::Equals);
#else
			CurrentSessionParams->SessionTemplateName = MatchmakingMode == EMatchmakingMode::Ranked ? FString("RankedMatchSession") : FString("QuickMatchSession");
			CurrentSessionParams->MatchingHopper = MatchmakingMode == EMatchmakingMode::Ranked ? FString("RankedMatch") : FString("QuickMatch");

			SearchSettings->QuerySettings.Set(SETTING_MATCHING_HOPPER, CurrentSessionParams->MatchingHopper, EOnlineComparisonOp::Equals);

			SearchSettings->QuerySettings.Set(SETTING_SESSION_TEMPLATE_NAME, CurrentSessionParams->SessionTemplateName, EOnlineComparisonOp::Equals);

			SearchSettings->QuerySettings.Set(SEARCH_MATCHMODE, (int32)MatchmakingMode, EOnlineComparisonOp::Equals);
			SearchSettings->QuerySettings.Set(SEARCH_PRIVACYLEVEL, (int32)gameInstance->GetLobbyPrivacy(), EOnlineComparisonOp::Equals);

			//if (CurrentSessionParams->PrivacyLevel == EPrivacyLevel::Public)
			{
				SearchSettings->QuerySettings.Set(SEARCH_NETWORK, SEARCH_NETWORK_VALUE, EOnlineComparisonOp::Equals);
				SearchSettings->QuerySettings.Set(SEARCH_INPROGRESS, SEARCH_NOT_INPROGRESSS_VALUE, EOnlineComparisonOp::Equals);
				SearchSettings->QuerySettings.Set(SEARCH_OPEN_SLOTS, gameInstance->GetNumLocalPlayers(), EOnlineComparisonOp::GreaterThanEquals);
				SearchSettings->QuerySettings.Set(SEARCH_NETWORK_BUILD_NUMBER, SEARCH_BUILD, EOnlineComparisonOp::Equals);
				SearchSettings->QuerySettings.Set(SEARCH_RUGBY_RULES, RuleType == 0 ? SEARCH_15s_RULE_VALUE : SEARCH_7s_RULE_VALUE, EOnlineComparisonOp::Equals);
			}
#endif  //PLATFORM_WINDOW



#if !UE_BUILD_SHIPPING && PLATFORM_WINDOWS
			SearchSettings->QuerySettings.Set(SEARCH_LANKEY, GetOnlineDebugKey(), EOnlineComparisonOp::Equals);
#endif //!UE_BUILD_SHIPPING && PLATFORM_WINDOWS
#if PLATFORM_XBOXONE
			FString SessionTypeName = MatchmakingMode == EMatchmakingMode::Ranked ? XBOXONE_RANKEDMATCH_TEMPLATE_NAME : XBOXONE_UNRANKEDMATCH_TEMPLATE_NAME;
			FString SessionHopperName = MatchmakingMode == EMatchmakingMode::Ranked ? XBOXONE_RANKEDMATCH_HOPPER_NAME : XBOXONE_UNRANKEDMATCH_HOPPER_NAME;

			if (gameInstance->GetLobbyPrivacy() == EPrivacyLevel::InviteOnly)
			{
				SessionTypeName = XBOXONE_PRIVATEMATCH_TEMPLATE_NAME;
				SessionHopperName = XBOXONE_PRIVATEMATCH_HOPPER_NAME;
			}

			SearchSettings->QuerySettings.Set(SETTING_GAMEMODE, CurrentSessionParams->GameMode, EOnlineComparisonOp::Equals);
			SearchSettings->QuerySettings.Set(SEARCH_XBOX_LIVE_HOPPER_NAME, SessionHopperName, EOnlineComparisonOp::Equals);
			SearchSettings->QuerySettings.Set(SEARCH_XBOX_LIVE_SESSION_TEMPLATE_NAME, SessionTypeName, EOnlineComparisonOp::Equals);

			FString Keyword = CUSTOMMATCHKEYWORD + SEARCH_BUILD + (RuleType == 0 ? SEARCH_15s_RULE_VALUE : SEARCH_7s_RULE_VALUE);

			SearchSettings->QuerySettings.Set(SEARCH_KEYWORDS, Keyword, EOnlineComparisonOp::Equals);
			//SearchSettings->QuerySettings.Set(SEARCH_USER, gameInstance->GetPrimaryPlayerController()->GetPlayerState()->GetPlayerName(), EOnlineComparisonOp::NotEquals);
			SearchSettings->QuerySettings.Set(SETTING_MAX_RESULT, 100, EOnlineComparisonOp::NotEquals);

#endif
#if  PLATFORM_WINDOWS
			SearchSettings->QuerySettings.Set(SEARCH_KEYWORDS, CUSTOMMATCHKEYWORD, EOnlineComparisonOp::Equals);
#endif
#endif	// !SWITCH
			SearchSettings->TimeoutInSeconds = 120.0f;
			TSharedRef<FOnlineSessionSearch> SearchSettingsRef = SearchSettings.ToSharedRef();

			OnFindSessionsCompleteDelegateHandle = Sessions->AddOnFindSessionsCompleteDelegate_Handle(OnFindSessionsCompleteDelegate);
			DumpMatchingParams(CurrentSessionParams);
			DumpSearchSession(SearchSettings);
			Sessions->FindSessions(*CurrentSessionParams->UserId, SearchSettingsRef);
		}
	}
	else
	{
		OnFindSessionsComplete(false);
	}
}

bool ARugbyGameSession::JoinSession(TSharedPtr<const FUniqueNetId> UserId, FName InSessionName, int32 SessionIndexInSearchResults)
{
	bool bResult = false;

	if (SessionIndexInSearchResults >= 0 && SessionIndexInSearchResults < SearchSettings->SearchResults.Num())
	{
		bResult = JoinSession(UserId, InSessionName, SearchSettings->SearchResults[SessionIndexInSearchResults]);
	}

	return bResult;
}

bool ARugbyGameSession::JoinSession(TSharedPtr<const FUniqueNetId> UserId, FName InSessionName, const FOnlineSessionSearchResult& SearchResult)
{
	bool bResult = false;

	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
		if (Sessions.IsValid() && UserId.IsValid())
		{
			OnJoinSessionCompleteDelegateHandle = Sessions->AddOnJoinSessionCompleteDelegate_Handle(OnJoinSessionCompleteDelegate);
			bResult = Sessions->JoinSession(*UserId, InSessionName, SearchResult);

			if (bResult)
			{
				DumpSession(&SearchResult.Session);
			}
		}
	}

	return bResult;
}

void ARugbyGameSession::OnJoinSessionComplete(FName InSessionName, EOnJoinSessionCompleteResult::Type Result)
{
	UE_LOG(LogOnlineGame, Warning, TEXT("OnJoinSessionComplete %s bSuccess: %d"), *InSessionName.ToString(), static_cast<int32>(Result));
	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();



#if PLATFORM_SWITCH
	URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(GetWorld()->GetGameInstance());

	if (Result == EOnJoinSessionCompleteResult::AlreadyInSession)
	{
		UE_LOG(LogOnline, Warning, TEXT(" Join friend session was unsuccessful. AlreadyInSession"));
		SIFApplication::GetApplication()->ShowErrorUIPopup("[ID_INVITEPOPUPTITLE]", "[ID_FAILEDTOJOINTHESESSION]");
		//gameInstance->ShowErrorUIPopup("Join Friend Session", "Failed to join friend session, Already In Session!");
		SIFApplication::GetApplication()->DealMenuAction(SCREEN_POP_MENUSCREEN, Screens_UI::ModalWaiting);
		return;
	}

	if (Result == EOnJoinSessionCompleteResult::SessionIsFull)
	{
		UE_LOG(LogOnline, Warning, TEXT(" Join friend session was unsuccessful. SessionIsFull"));
		SIFApplication::GetApplication()->ShowErrorUIPopup("[ID_INVITEPOPUPTITLE]", "[ID_SESSION_FULL_OR_STARTED]");
		//gameInstance->ShowErrorUIPopup("Join Friend Session", "Failed to join friend session, Session Is Full!");
		SIFApplication::GetApplication()->DealMenuAction(SCREEN_POP_MENUSCREEN, Screens_UI::ModalWaiting);
		return;
	}

	if (Result == EOnJoinSessionCompleteResult::SessionDoesNotExist)
	{
		UE_LOG(LogOnline, Warning, TEXT(" Join friend session was unsuccessful. SessionDoesNotExist"));
		SIFApplication::GetApplication()->ShowErrorUIPopup("[ID_INVITEPOPUPTITLE]", "[ID_SESSIONDOESNOTEXIST]");
		//gameInstance->ShowErrorUIPopup("Join Friend Session", "Failed to join friend session, Session Does Not Exist!");
		SIFApplication::GetApplication()->DealMenuAction(SCREEN_POP_MENUSCREEN, Screens_UI::ModalWaiting);
		return;
	}


	if (Result == EOnJoinSessionCompleteResult::CouldNotRetrieveAddress)
	{
		UE_LOG(LogOnline, Warning, TEXT(" Join friend session was unsuccessful. CouldNotRetrieveAddress"));
		SIFApplication::GetApplication()->ShowErrorUIPopup("[ID_INVITEPOPUPTITLE]", "[ID_FAILEDTOJOINTHESESSION]");
		//gameInstance->ShowErrorUIPopup("Join Friend Session", "Failed to join friend session, Could Not Retrieve Address!");
		SIFApplication::GetApplication()->DealMenuAction(SCREEN_POP_MENUSCREEN, Screens_UI::ModalWaiting);
		return;
	}

	if (Result == EOnJoinSessionCompleteResult::UnknownError)
	{
		UE_LOG(LogOnline, Warning, TEXT(" Join friend session was unsuccessful. UnknownError"));

		SIFApplication::GetApplication()->ShowErrorUIPopup("[ID_INVITEPOPUPTITLE]", "[ID_FAILEDTOJOINTHESESSION]");
		//gameInstance->ShowErrorUIPopup("Join Friend Session", "Unknown Error!");
		SIFApplication::GetApplication()->DealMenuAction(SCREEN_POP_MENUSCREEN, Screens_UI::ModalWaiting);
		return;
	}
#endif

	if (OnlineSub)
	{
		IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
		if (Sessions.IsValid())
		{
			Sessions->ClearOnJoinSessionCompleteDelegate_Handle(OnJoinSessionCompleteDelegateHandle);
#if PLATFORM_SWITCH
			if (UOBJ_IS_VALID(gameInstance))
			{
				SIFApplication::GetApplication()->DealMenuAction(SCREEN_POP_MENUSCREEN, Screens_UI::ModalWaiting);
				auto Subsystem = IOnlineSubsystem::Get();
				if (Subsystem != nullptr)
				{
					UE_LOG(LogOnline, Warning, TEXT("Friend Session: Join Session"));


					// We are the client, join the host.
					gameInstance->JoinSession_Switch(SessionName);

					return;
				}
				else
				{
					UE_LOG(LogOnline, Warning, TEXT("Friend Session: Subsystem invalid"));
				}
			}
#else
			bool bWillTravel = false;

			//Sessions->OnConnectionStateCallback.AddUObject(this, &ARugbyGameSession::OnConnectionCheckComplete);
			//// GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL remove ERICTOTEST OnlineSession interface haas changed


			OnJoinSessionComplete().Broadcast(Result);
#endif
		}
	}


	URugbyGameInstance* GameInstance = Cast<URugbyGameInstance>(GetWorld()->GetGameInstance());

	if (GameInstance && GameInstance->GetConnectionManager())
	{
		GameInstance->GetConnectionManager()->OnStartSessionComplete(InSessionName, true);
	}

	// according to AFL we don't run this on SWITCH
	//OnJoinSessionComplete().Broadcast(Result);
}

void ARugbyGameSession::UpdateSessionSettings()
{
	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		IOnlineSessionPtr SessionInt = OnlineSub->GetSessionInterface();
		if (SessionInt.IsValid() && HostSettings->IsValid())
		{
			UE_LOG(LogTemp, Warning, TEXT("------------------------------------------------------"));
			UE_LOG(LogTemp, Warning, TEXT("------------------------------------------------------"));
			UE_LOG(LogTemp, Warning, TEXT("About to update Session Settings. Dumping old ones now:"));
			UE_LOG(LogTemp, Warning, TEXT("------------------------------------------------------"));
			UE_LOG(LogTemp, Warning, TEXT("------------------------------------------------------"));
			SessionInt->DumpSessionState();

			OnUpdateSessionCompleteDelegateHandle = SessionInt->AddOnUpdateSessionCompleteDelegate_Handle(OnUpdateSessionCompleteDelegate);

			/*URugbyGameInstance* GameInstance = wwPlayerManager::Get().GetGameInstance();
			if (GameInstance)
			{
				CurrentSessionParams->MatchmakingMode = GameInstance->GetMatchmakingMode();
				CurrentSessionParams->PrivacyLevel = GameInstance->GetLobbyPrivacy();
			}*/

			// TODO: Move this to the build settings function instead?
			(*HostSettings)->Set(SEARCH_MATCHMODE, (int32)CurrentSessionParams->MatchmakingMode, EOnlineDataAdvertisementType::DontAdvertise);
			(*HostSettings)->Set(SEARCH_PRIVACYLEVEL, (int32)CurrentSessionParams->PrivacyLevel, EOnlineDataAdvertisementType::DontAdvertise);

			BuildHostSettings();

			(*HostSettings)->bAllowJoinInProgress = false;

			SessionInt->UpdateSession(SessionName, *(*HostSettings), true);
		}
	}
}

void ARugbyGameSession::OnUpdateSessionComplete(FName SessionName, bool bWasSuccessful)
{
	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
		if (Sessions.IsValid())
		{
			Sessions->ClearOnFindSessionsCompleteDelegate_Handle(OnFindSessionsCompleteDelegateHandle);
			if (bWasSuccessful)
			{
				UE_LOG(LogTemp, Warning, TEXT("Updated new Session Settings. Dumping new ones now:"));
				Sessions->DumpSessionState();
			}
		}
	}
}

bool ARugbyGameSession::TravelToSession(int32 ControllerId, FName InSessionName)
{
	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		FString URL;
		IOnlineSessionPtr Sessions = OnlineSub->GetSessionInterface();
		if (Sessions.IsValid() && Sessions->GetResolvedConnectString(InSessionName, URL))
		{
			APlayerController* PC = UGameplayStatics::GetPlayerController(GetWorld(), ControllerId);
			if (PC)
			{
				PC->ClientTravel(URL, TRAVEL_Absolute);
				return true;
			}
		}
		else
		{
			UE_LOG(LogOnlineGame, Warning, TEXT("Failed to join session %s"), *SessionName.ToString());
		}
	}
#if !UE_BUILD_SHIPPING
	else
	{
		APlayerController* PC = UGameplayStatics::GetPlayerController(GetWorld(), ControllerId);
		if (PC)
		{
			FString LocalURL(TEXT("127.0.0.1"));
			PC->ClientTravel(LocalURL, TRAVEL_Absolute);
			return true;
		}
	}
#endif //!UE_BUILD_SHIPPING

	return false;
}

//void ARugbyGameSession::CreateStatID()
//{
//	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
//	if (OnlineSub)
//	{
//		IOnlineVoicePtr p_Voice = OnlineSub->GetVoiceInterface();
//		if (p_Voice.IsValid())
//		{
//			p_Voice->
//		}
//	}
//}



