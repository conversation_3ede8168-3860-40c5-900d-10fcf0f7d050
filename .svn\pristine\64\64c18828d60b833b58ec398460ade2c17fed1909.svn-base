/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

// stdafx
//#rc3_legacy_pch #include "Precompiled.h"

// my includes
#include "Match/RugbyUnion/Statistics/RUStatisticsSystem.h"

// library includes
#include <Mab/MabInclude.h>
#include <Mab/Types/MabNamedValueList.h>
#include <Mab/Threading/MabLock.h>
//#rc3_legacy_include #include <Mab/Central/MabCentralObjectTraversal.h>

// local includes
#include "Match/SIFGameWorld.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/RUTackleHelper.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUDBPlayer.h"
#include "Match/RugbyUnion/RUDBTeam.h"
#include "Databases/RUGameDatabaseManager.h"
#include "Databases/SqliteMabObject.h"
//#rc3_legacy_include #include "Databases/SqliteMabDatabase.h"
#include "Databases/SqliteMabStatement.h"
#include "Match/RugbyUnion/Statistics/RUStatsTracker.h"
#include "Match/SSStreamPacker.h"
#include "Match/RugbyUnion/CompetitionMode/OLD_RU/RUCompetitionModeManager.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DBTeam.h"
//#rc3_legacy_include #include "Match/SIFGameHelpers.h"
#include "RugbyGameInstance.h"

#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DBPlayer.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Database.h"
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "Match/RugbyUnion/RUDatabaseConstants.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Tournament.h"

#include "Match/Debug/SIFDebug.h"
#include "Match/RugbyUnion/Rules/RURulesDebugSettings.h"
#include "Match/RugbyUnion/RUProModeDebugSettings.h"


#define PLAYER_STAT_DESCRIPTOR(member, name, format) { _RUStatisticsSystem::GetStatOffset(&RUDB_STATS_PLAYER::member), name, format }
#define TEAM_STAT_DESCRIPTOR(member, name, format) { _RUStatisticsSystem::GetStatOffset(&RUDB_STATS_TEAM::member), name, format }

// these are really used for matching a stat to a string
const RUStatDescriptor RUDB_STATS_PLAYER::STAT_DESCRIPTORS[] =
{
	PLAYER_STAT_DESCRIPTOR(conversion_attempts, "[ID_ISTAT_CONVERSION_ATTEMPTS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(field_goal_attempts, "[ID_ISTAT_FIELD_GOAL_ATTEMPTS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(penalty_goal_attempts, "[ID_ISTAT_PENALTY_GOAL_ATTEMPTS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(handling_errors, "[ID_ISTAT_HANDLING_ERRORS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(hitups, "[ID_ISTAT_HITUPS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(high_tackles, "[ID_ISTAT_HIGH_TACKLES]", "%d"),
	PLAYER_STAT_DESCRIPTOR(injuries, "[ID_ISTAT_INJURIES]", "%d"),
	PLAYER_STAT_DESCRIPTOR(kicks, "[ID_ISTAT_KICKS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(line_breaks, "[ID_ISTAT_LINE_BREAKS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(offloads, "[ID_ISTAT_OFFLOADS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(offloads_attempted, "[ID_ISTAT_OFFLOADS_ATTEMPTED]", "%d"),
	PLAYER_STAT_DESCRIPTOR(penalties_against, "[ID_ISTAT_PENALTIES_AGAINST]", "%d"),
	PLAYER_STAT_DESCRIPTOR(points_scored, "[ID_ISTAT_POINTS_SCORED]", "%d"),
	PLAYER_STAT_DESCRIPTOR(red_cards, "[ID_ISTAT_RED_CARDS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(successful_conversion_attempts, "[ID_ISTAT_SUCCESSFUL_CONVERSION_ATTEMPTS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(successful_field_goals, "[ID_ISTAT_SUCCESSFUL_FIELD_GOALS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(successful_penalty_goals, "[ID_ISTAT_SUCCESSFUL_PENALTY_GOALS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(successful_tackles, "[ID_ISTAT_SUCCESSFUL_TACKLES]", "%d"),
	PLAYER_STAT_DESCRIPTOR(successful_try_attempts, "[ID_ISTAT_SUCCESSFUL_TRY_ATTEMPTS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(tackle_attempts, "[ID_ISTAT_TACKLE_ATTEMPTS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(tries_scored, "[ID_ISTAT_TRIES_SCORED]", "%d"),
	PLAYER_STAT_DESCRIPTOR(try_attempts, "[ID_ISTAT_TRY_ATTEMPTS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(yellow_cards, "[ID_ISTAT_YELLOW_CARDS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(kicking_meters_gained, "[ID_FSTAT_KICKING_METERS_GAINED]", "%f"),
	PLAYER_STAT_DESCRIPTOR(running_meters_gained, "[ID_FSTAT_RUNNING_METERS_GAINED]", "%f"),

	PLAYER_STAT_DESCRIPTOR(ruck_entry, "[ID_ISTAT_RUCK_ENTRY]", "%d"),
	PLAYER_STAT_DESCRIPTOR(contest_win, "[ID_ISTAT_CONTEST_WIN]", "%d"),
	PLAYER_STAT_DESCRIPTOR(successful_lineout_steal, "[ID_ISTAT_LINEOUT_STEAL_ATTEMPTS]", "%d"),
	PLAYER_STAT_DESCRIPTOR(lineout_steal_attempts, "[ID_ISTAT_SUCCESSFUL_LINEOUT_STEAL]", "%d")
};

const size_t RUDB_STATS_PLAYER::NUM_STAT_DESCRIPTORS = StaticArraySize(RUDB_STATS_PLAYER::STAT_DESCRIPTORS);

const RUStatDescriptor RUDB_STATS_TEAM::STAT_DESCRIPTORS[] =
{
	TEAM_STAT_DESCRIPTOR(score, "[ID_ISTAT_TEAM_SCORE]", "%d"),
	TEAM_STAT_DESCRIPTOR(opponent_score, "[ID_ISTAT_TEAM_OPPONENT_SCORE]", "%d"),
	TEAM_STAT_DESCRIPTOR(scrums, "[ID_ISTAT_TEAM_SCRUMS]", "%d"),
	TEAM_STAT_DESCRIPTOR(num_five_plus_phases, "[ID_ISTAT_TEAM_NUM_FIVE_PLUS_PHASES]", "%d"),
	TEAM_STAT_DESCRIPTOR(possession, "[ID_FSTAT_TEAM_POSSESSION]", "%f"),
	TEAM_STAT_DESCRIPTOR(territory, "[ID_FSTAT_TEAM_TERRITORY]", "%f")
};
const size_t RUDB_STATS_TEAM::NUM_STAT_DESCRIPTORS = StaticArraySize(RUDB_STATS_TEAM::STAT_DESCRIPTORS);

RUDB_STATS_PLAYER::RUDB_STATS_PLAYER()
: competition_id(SQLITEMAB_INVALID_ID)
, match_id(SQLITEMAB_INVALID_ID)
, team_id(SQLITEMAB_INVALID_ID)
, player_id(SQLITEMAB_INVALID_ID)
, conversion_attempts(0)
, field_goal_attempts(0)
, penalty_goal_attempts(0)
, handling_errors(0)
, hitups(0)
, high_tackles(0)
, injuries(0)
, kicks(0)
, line_breaks(0)
, offloads(0)
, offloads_attempted(0)
, penalties_against(0)
, points_scored(0)
, red_cards(0)
, successful_conversion_attempts(0)
, successful_field_goals(0)
, successful_penalty_goals(0)
, successful_tackles(0)
, successful_try_attempts(0)
, tackle_attempts(0)
, tries_scored(0)
, try_attempts(0)
, yellow_cards(0)
, kicking_meters_gained(0)
, running_meters_gained(0)
, ruck_entry(0)
, contest_win(0)
, lineout_steal_attempts(0)
, successful_lineout_steal(0)
{
}

void RUDB_STATS_PLAYER::Reset(bool soft_reset)
{
	if (soft_reset)
	{
		// save database ids and restore them after reset
		unsigned short saved_competition_id = competition_id;
		unsigned short saved_match_id = match_id;
		unsigned short saved_team_id = team_id;
		unsigned short saved_player_id = player_id;
		*this = RUDB_STATS_PLAYER();
		competition_id = saved_competition_id;
		match_id = saved_match_id;
		team_id = saved_team_id;
		player_id = saved_player_id;
	}
	else
	{
		// reset everything
		*this = RUDB_STATS_PLAYER();
	}
}

// Save state / Load state
void RUDB_STATS_PLAYER::Pack(SSStreamPacker& stream)
{
	stream.Pack(competition_id);
	stream.Pack(match_id);
	stream.Pack(team_id);
	stream.Pack(player_id);
	stream.Pack(conversion_attempts);
	stream.Pack(field_goal_attempts);
	stream.Pack(penalty_goal_attempts);
	stream.Pack(handling_errors);
	stream.Pack(hitups);
	stream.Pack(high_tackles);
	stream.Pack(injuries);
	stream.Pack(kicks);
	stream.Pack(line_breaks);
	stream.Pack(offloads);
	stream.Pack(offloads_attempted);
	stream.Pack(penalties_against);
	stream.Pack(points_scored);
	stream.Pack(red_cards);
	stream.Pack(successful_conversion_attempts);
	stream.Pack(successful_field_goals);
	stream.Pack(successful_penalty_goals);
	stream.Pack(successful_tackles);
	stream.Pack(successful_try_attempts);
	stream.Pack(tackle_attempts);
	stream.Pack(tries_scored);
	stream.Pack(try_attempts);
	stream.Pack(yellow_cards);
	stream.Pack(kicking_meters_gained);
	stream.Pack(running_meters_gained);

	stream.Pack(ruck_entry);
	stream.Pack(contest_win);
	stream.Pack(lineout_steal_attempts);
	stream.Pack(successful_lineout_steal);
}

void RUDB_STATS_PLAYER::Unpack(SSStreamPacker& stream)
{
	stream.Unpack(competition_id);
	stream.Unpack(match_id);
	stream.Unpack(team_id);
	stream.Unpack(player_id);
	stream.Unpack(conversion_attempts);
	stream.Unpack(field_goal_attempts);
	stream.Unpack(penalty_goal_attempts);
	stream.Unpack(handling_errors);
	stream.Unpack(hitups);
	stream.Unpack(high_tackles);
	stream.Unpack(injuries);
	stream.Unpack(kicks);
	stream.Unpack(line_breaks);
	stream.Unpack(offloads);
	stream.Unpack(offloads_attempted);
	stream.Unpack(penalties_against);
	stream.Unpack(points_scored);
	stream.Unpack(red_cards);
	stream.Unpack(successful_conversion_attempts);
	stream.Unpack(successful_field_goals);
	stream.Unpack(successful_penalty_goals);
	stream.Unpack(successful_tackles);
	stream.Unpack(successful_try_attempts);
	stream.Unpack(tackle_attempts);
	stream.Unpack(tries_scored);
	stream.Unpack(try_attempts);
	stream.Unpack(yellow_cards);
	stream.Unpack(kicking_meters_gained);
	stream.Unpack(running_meters_gained);

	stream.Unpack(ruck_entry);
	stream.Unpack(contest_win);
	stream.Unpack(lineout_steal_attempts);
	stream.Unpack(successful_lineout_steal);
}

RUDB_STATS_TEAM::RUDB_STATS_TEAM()
: competition_id(SQLITEMAB_INVALID_ID)
, match_id(SQLITEMAB_INVALID_ID)
, team_id(SQLITEMAB_INVALID_ID)
, score(0)
, opponent_score(0)
, scrums(0)
, num_five_plus_phases(0)
, possession(0)
, territory(0)
, tries_conceded(0)
, tries_scored(0)
, penalties_conceded(0)
, penalties_awarded(0)
{
}

void RUDB_STATS_TEAM::Reset(bool soft_reset)
{
	if (soft_reset)
	{
		// save database ids and restore them after reset
		unsigned short saved_competition_id = competition_id;
		unsigned short saved_match_id = match_id;
		unsigned short saved_team_id = team_id;
		*this = RUDB_STATS_TEAM();
		competition_id = saved_competition_id;
		match_id = saved_match_id;
		team_id = saved_team_id;
	}
	else
	{
		// reset everything
		*this = RUDB_STATS_TEAM();
	}
}


// Save state / Load state
void RUDB_STATS_TEAM::Pack(SSStreamPacker& stream)
{
	stream.Pack(competition_id);
	stream.Pack(match_id);
	stream.Pack(team_id);
	stream.Pack(score);
	stream.Pack(opponent_score);
	stream.Pack(scrums);
	stream.Pack(num_five_plus_phases);
	stream.Pack(possession);
	stream.Pack(territory);
	stream.Pack(tries_conceded);
	stream.Pack(tries_scored);
	stream.Pack(penalties_conceded);
	stream.Pack(penalties_awarded);
}

void RUDB_STATS_TEAM::Unpack(SSStreamPacker& stream)
{
	stream.Unpack(competition_id);
	stream.Unpack(match_id);
	stream.Unpack(team_id);
	stream.Unpack(score);
	stream.Unpack(opponent_score);
	stream.Unpack(scrums);
	stream.Unpack(num_five_plus_phases);
	stream.Unpack(possession);
	stream.Unpack(territory);
	stream.Unpack(tries_conceded);
	stream.Unpack(tries_scored);
	stream.Unpack(penalties_conceded);
	stream.Unpack(penalties_awarded);
}


/// Methods to get string table name for a given member
const RUStatDescriptor* _RUStatisticsSystem::FindStatDescriptor(size_t offset, const RUStatDescriptor* descriptor_list, size_t num_descriptors)
{
	for (size_t i=0; i<num_descriptors; ++i)
	{
		if (descriptor_list[i].offset == offset)
			return &descriptor_list[i];
	}
	MABBREAKMSG("No STAT_OFFSET_NAME found for member");
	return NULL;
}

const int RUStatisticsSystem::TRY_SCORER_ENUM_LIST[] =
{
	CCDB_COMPINSTPLAYERMATCHSTATS_TRY_SCORER_1_STR,
	CCDB_COMPINSTPLAYERMATCHSTATS_TRY_SCORER_2_STR,
	CCDB_COMPINSTPLAYERMATCHSTATS_TRY_SCORER_3_STR
};

const size_t RUStatisticsSystem::NUM_TRY_SCORERS_RECORDED = StaticArraySize( RUStatisticsSystem::TRY_SCORER_ENUM_LIST );

RUStatisticsSystem::RUStatisticsSystem(RUGameDatabaseManager& database_manager)
: current_world(NULL)
, current_competition_id(SQLITEMAB_INVALID_ID)
, current_match_id(SQLITEMAB_INVALID_ID)
, current_match_half(-1)
, game_db_manager(&database_manager)
{
}

RUStatisticsSystem::~RUStatisticsSystem()
{
}

// Save state / Load state
void RUStatisticsSystem::PackCurrentGame(SSStreamPacker& stream)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	for(size_t i=0; i < current_match_team_stats.size(); ++i)
		current_match_team_stats[i].Pack(stream);

	for(size_t i=0; i < current_match_player_stats.size(); ++i)
		for(size_t j=0; j < current_match_player_stats[i].size(); ++j)
			current_match_player_stats[i][j].Pack(stream);
}

void RUStatisticsSystem::UnpackCurrentGame(SSStreamPacker& stream)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	for(size_t i=0; i < current_match_team_stats.size(); ++i)
		current_match_team_stats[i].Unpack(stream);

	for(size_t i=0; i < current_match_player_stats.size(); ++i)
		for(size_t j=0; j < current_match_player_stats[i].size(); ++j)
			current_match_player_stats[i][j].Unpack(stream);
}

void RUStatisticsSystem::NewGame(const RUDB_TEAM& team_a, const RUDB_TEAM& team_b, unsigned short competition_id, unsigned short match_id)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	MABASSERT(current_match_half == RU_STAT_PRE_MATCH);

	current_competition_id = competition_id;		//competition_instance ? competition_instance->GetDbId() : SQLITEMAB_INVALID_ID;
	current_match_id = match_id;					//match ? match->GetDbId() : SQLITEMAB_INVALID_ID;
	current_match_half = RU_STAT_FIRST_HALF;

	// for convenience, store teams in an array
	const MabArray< const RUDB_TEAM*, 2> teams = {{ &team_a, &team_b }};

	// store player and team ids for each team
	for (size_t team_index=0; team_index<2; ++team_index)
	{
		const RUDB_TEAM& current_team = *teams[team_index];
		const unsigned short current_team_id = current_team.GetDbId();
		const size_t num_team_players = MabMath::Min<size_t>(current_team.GetNumLineups(), NUM_PLAYERS_PER_TEAM_INC_BENCH_INIT/*NUM_STAT_PLAYERS*/);

		// store the team ids
		current_team_ids[team_index] = current_team_id;

		current_teams_sides[team_index] = team_index==0?SIDE_A:SIDE_B;

		// set up team stats records
		current_match_team_stats[team_index].Reset(false);
		current_match_team_stats[team_index].competition_id = current_competition_id;
		current_match_team_stats[team_index].match_id = current_match_id;
		current_match_team_stats[team_index].team_id = current_team_id;

		// resize player id vector
		current_player_ids[team_index].resize(num_team_players);

		// resize player stats vector
		current_match_player_stats[team_index].resize(num_team_players);

		for (size_t player_index=0; player_index<num_team_players; ++player_index)
		{
			// add player id to player id's list
			const RUDB_LINEUP& lineup = current_team.GetLineup((int)player_index);
			const unsigned short current_player_id = lineup.player_id;
			current_player_ids[team_index][player_index] = current_player_id;

			// set up player stats record
			// these are mostly used to confirm we have looked up the right record
			current_match_player_stats[team_index][player_index].Reset(false); // This might happen through the resize anyway...
			current_match_player_stats[team_index][player_index].competition_id = current_competition_id;
			current_match_player_stats[team_index][player_index].match_id = current_match_id;
			current_match_player_stats[team_index][player_index].team_id = current_team_id;
			current_match_player_stats[team_index][player_index].player_id = current_player_id;
		}
	}
}

void RUStatisticsSystem::NewGame(RL3DB_TEAM team_a, RL3DB_TEAM team_b, unsigned short competition_id, unsigned short match_id)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	MABASSERT(current_match_half == RU_STAT_PRE_MATCH);

	current_competition_id = competition_id;		//competition_instance ? competition_instance->GetDbId() : SQLITEMAB_INVALID_ID;
	current_match_id = match_id;					//match ? match->GetDbId() : SQLITEMAB_INVALID_ID;
	current_match_half = RU_STAT_FIRST_HALF;

	// for convenience, store teams in an array
	const MabArray< RL3DB_TEAM, 2> teams = {{ team_a, team_b }};

	// store player and team ids for each team
	for (size_t team_index=0; team_index<2; ++team_index)
	{
		RL3DB_TEAM current_team = teams[team_index];
		const unsigned short current_team_id = current_team.GetDbId();
		int num_players_in_squad = current_team.GetNumPlayers();
		const size_t num_team_players = MabMath::Min<size_t>(num_players_in_squad, NUM_PLAYERS_PER_TEAM_INC_BENCH_INIT/*NUM_STAT_PLAYERS*/);

		// store the team ids
		current_team_ids[team_index] = current_team_id;

		current_teams_sides[team_index] = team_index==0?SIDE_A:SIDE_B;

		// set up team stats records
		current_match_team_stats[team_index].Reset(false);
		current_match_team_stats[team_index].competition_id = current_competition_id;
		current_match_team_stats[team_index].match_id = current_match_id;
		current_match_team_stats[team_index].team_id = current_team_id;

		// resize player id vector
		current_player_ids[team_index].resize(num_team_players);

		// resize player stats vector
		current_match_player_stats[team_index].resize(num_team_players);


		for (size_t player_index=0; player_index<num_team_players; ++player_index)
		{
			// add player id to player id's list
			const RL3DB_CONTRACT lineup = current_team.GetPlayer((int)player_index);

			const unsigned short current_player_id = lineup.index;
			current_player_ids[team_index][player_index] = current_player_id;

			// set up player stats record
			// these are mostly used to confirm we have looked up the right record
			current_match_player_stats[team_index][player_index].Reset(false); // This might happen through the resize anyway...
			current_match_player_stats[team_index][player_index].competition_id = current_competition_id;
			current_match_player_stats[team_index][player_index].match_id = current_match_id;
			current_match_player_stats[team_index][player_index].team_id = current_team_id;
			current_match_player_stats[team_index][player_index].player_id = current_player_id;
		}
	}


	// Debugging
	/*for(int team_index = 0; team_index < 2; team_index++)
	{
		RL3DB_TEAM current_team = teams[team_index];
		MABLOGDEBUG("Players added to statistics for team %s", current_team.GetName());
		for(size_t pp = 0; pp < current_match_player_stats[team_index].size(); pp ++)
		{
			RL3DB_PLAYER playa(current_match_player_stats[team_index][pp].player_id);
			MABLOGDEBUG("player(%i): %s", current_match_player_stats[team_index][pp].player_id, playa.GetName().c_str());
		}
	}*/
}




void RUStatisticsSystem::EndCurrentGame()
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	// if this is competition/coach mode then match id should be set and we want to save the stats to the database
	if (current_competition_id != SQLITEMAB_INVALID_ID && current_match_id != SQLITEMAB_INVALID_ID)
	{
		current_competition_id = SQLITEMAB_INVALID_ID;
		current_match_id = SQLITEMAB_INVALID_ID;
	}

	ResetCurrentMatchStats(false);
}

void RUStatisticsSystem::AttachMonitors(SIFGameWorld* game_world)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	// save the current game world
	current_world = game_world;

	/// creates a new game when listeners are attached to it
	RUTeam* team_a = current_world->GetTeam(SIDE_A);
	RUTeam* team_b = current_world->GetTeam(SIDE_B);

	MABASSERT(team_a && team_b);
	if (team_a && team_b)
	{
		const RUActiveCompetitionBase* active_comp = nullptr;
		const RUDB_COMP_INST_MATCH* next_match = nullptr;

		SIFApplication::GetApplication()->GetCompetitionFields(&active_comp, &next_match);

		NewGame(team_a->GetDbTeam(),
			team_b->GetDbTeam(),
			active_comp ? (unsigned short)active_comp->GetInstanceId() : SQLITEMAB_INVALID_ID,
			next_match ? next_match->GetDbId() : SQLITEMAB_INVALID_ID);

		RUGameEvents& events = *current_world->GetEvents();
		events.pass.Add(this, &RUStatisticsSystem::AddPassInterface);
		events.kick.Add(this, &RUStatisticsSystem::AddKickInterface);
		events.fumble.Add(this, &RUStatisticsSystem::AddHandlingErrorInterface);
		events.knock_on.Add(this, &RUStatisticsSystem::AddHandlingErrorInterface);
		events.forward_pass.Add(this, &RUStatisticsSystem::AddForwardPassHandlingErrorInterface);
		events.tackle.Add(this, &RUStatisticsSystem::AddTackleInterface);
		events.scrum_ball_out.Add(this, &RUStatisticsSystem::AddScrumInterface);
		events.conversion_finish.Add(this, &RUStatisticsSystem::AddConversionInterface);
		events.penalty_goal_finish.Add(this, &RUStatisticsSystem::AddPenaltyGoalInterface);
		events.hitup.Add(this, &RUStatisticsSystem::AddHitupInterface);
		events.injury.Add(this, &RUStatisticsSystem::AddInjuryInterface);
		events.stat_line_break.Add(this, &RUStatisticsSystem::AddLineBreakInterface);
		events.penalty.Add(this, &RUStatisticsSystem::AddPenaltyInterface);
		events.penalty_from_advantage.Add(this, &RUStatisticsSystem::AddPenaltyInterface);
		events.red_card.Add(this, &RUStatisticsSystem::AddRedCardInterface);
		events.yellow_card.Add(this, &RUStatisticsSystem::AddYellowCardInterface);
		events.try_result.Add(this, &RUStatisticsSystem::AddTryInterface);
		events.running_metres_gained.Add(this, &RUStatisticsSystem::AddRunningMetresGainedInterface);
		events.kicking_metres_gained.Add(this, &RUStatisticsSystem::AddKickingMetresGainedInterface);
		events.drop_goal.Add(this, &RUStatisticsSystem::AddDropGoalInterface);
		events.half_time.Add(this, &RUStatisticsSystem::AddHalfTimeInterface);
		events.full_time.Add(this, &RUStatisticsSystem::AddFullTimeInterface);


		events.ruck_player_joined.Add(this, &RUStatisticsSystem::AddRuckEnterInterface);
		events.lineout_steal.Add(this, &RUStatisticsSystem::AddLineoutInterface);
		events.breakdown_turnover.Add(this, &RUStatisticsSystem::AddRuckTurnOverInterface);
	}
}

//////////////////////////////////////////////////////////////////
// dettaches all the game listeners and sets the game* to null
void RUStatisticsSystem::DetachMonitors()
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	if (current_world)
	{
		RUGameEvents& events = *current_world->GetEvents();
		events.pass.Remove(this, &RUStatisticsSystem::AddPassInterface);
		events.kick.Remove(this, &RUStatisticsSystem::AddKickInterface);
		events.fumble.Remove(this, &RUStatisticsSystem::AddHandlingErrorInterface);
		events.knock_on.Remove(this, &RUStatisticsSystem::AddHandlingErrorInterface);
		events.forward_pass.Remove(this, &RUStatisticsSystem::AddForwardPassHandlingErrorInterface);
		events.tackle.Remove(this, &RUStatisticsSystem::AddTackleInterface);
		events.scrum.Remove(this, &RUStatisticsSystem::AddScrumInterface);
		events.conversion_finish.Remove(this, &RUStatisticsSystem::AddConversionInterface);
		events.penalty_goal_finish.Remove(this, &RUStatisticsSystem::AddPenaltyGoalInterface);
		events.hitup.Remove(this, &RUStatisticsSystem::AddHitupInterface);
		events.injury.Remove(this, &RUStatisticsSystem::AddInjuryInterface);
		events.stat_line_break.Remove(this, &RUStatisticsSystem::AddLineBreakInterface);
		events.penalty.Remove(this, &RUStatisticsSystem::AddPenaltyInterface);
		events.penalty_from_advantage.Remove(this, &RUStatisticsSystem::AddPenaltyInterface);
		events.red_card.Remove(this, &RUStatisticsSystem::AddRedCardInterface);
		events.yellow_card.Remove(this, &RUStatisticsSystem::AddYellowCardInterface);
		events.try_result.Remove(this, &RUStatisticsSystem::AddTryInterface);
		events.running_metres_gained.Remove(this, &RUStatisticsSystem::AddRunningMetresGainedInterface);
		events.kicking_metres_gained.Remove(this, &RUStatisticsSystem::AddKickingMetresGainedInterface);
		events.drop_goal.Remove(this, &RUStatisticsSystem::AddDropGoalInterface);
		events.half_time.Remove(this, &RUStatisticsSystem::AddHalfTimeInterface);
		events.full_time.Remove(this, &RUStatisticsSystem::AddFullTimeInterface);


		events.ruck_player_joined.Remove(this, &RUStatisticsSystem::AddRuckEnterInterface);
		events.lineout_steal.Remove(this, &RUStatisticsSystem::AddLineoutInterface);
		events.breakdown_turnover.Remove(this, &RUStatisticsSystem::AddRuckTurnOverInterface);

		EndCurrentGame();

		current_world = NULL;
	}
}


/// Resets all the current game stats for when the player apts to restart a match
void RUStatisticsSystem::ResetCurrentMatchStats(bool soft_reset)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	current_match_half = RU_STAT_PRE_MATCH;

	// loops through the stats structures resetting their values
	for (size_t team_index=0; team_index<2; ++team_index)
	{
		current_match_team_stats[team_index].Reset(soft_reset);

		for (size_t player_index=0, num_players=current_match_player_stats[team_index].size(); player_index<num_players; ++player_index)
		{
			current_match_player_stats[team_index][player_index].Reset(soft_reset);
		}
	}
}

///////////////////////////////////////////////////////////////////////////
// helper function for finding ID's
///////////////////////////////////////////////////////////////////////////
void RUStatisticsSystem::GetPlayerId( const ARugbyCharacter* player, SSTEAMSIDE *team, unsigned short& player_id) const
{
	const RUPlayerAttributes* attrib = player->GetAttributes();
	*team = attrib->GetTeam()->GetSide();
	player_id = attrib->GetDBPlayer()->GetDbId();
}

SSTEAMSIDE RUStatisticsSystem::GetCurrentMatchTeamSide(unsigned short team_id) const
{
	if (current_team_ids[0] == team_id)
		return SIDE_A;
	else
		return SIDE_B;
}

bool RUStatisticsSystem::GetCurrentMatchTeamIndex(SSTEAMSIDE team_side, size_t& team_index) const
{
	if (current_teams_sides[0] == team_side)
		team_index = 0;
	else if (current_teams_sides[1])
		team_index = 1;
	else
		return false;

	return true;
}

bool RUStatisticsSystem::GetCurrentMatchPlayerIndex(SSTEAMSIDE team_side, unsigned short player_id, size_t& team_index, size_t& player_index) const
{
	if (!GetCurrentMatchTeamIndex(team_side, team_index))
		return false;

	for (size_t i=0, size=current_player_ids[team_index].size(); i<size; ++i)
	{
		if (current_player_ids[team_index][i] == player_id)
		{
			player_index = i;
			return true;
		}
	}

	return false;
}

RUDB_STATS_PLAYER* RUStatisticsSystem::GetCurrentMatchPlayerStats(SSTEAMSIDE team_side, unsigned short player_id)
{
	size_t team_index, player_index;
	if (!GetCurrentMatchPlayerIndex(team_side, player_id, team_index, player_index))
		return NULL;

	RUDB_STATS_PLAYER& result = current_match_player_stats[team_index][player_index];

	// check this is the right one
	MABASSERT(result.competition_id == current_competition_id);
	MABASSERT(result.match_id == current_match_id);
	MABASSERT(result.team_id == current_team_ids[team_index]);
	MABASSERT(result.player_id == player_id);

	return &result;
}

const RUDB_STATS_PLAYER*  RUStatisticsSystem::GetCurrentMatchPlayerStats(unsigned short team_id, unsigned short player_id) const
{
	SSTEAMSIDE side = GetCurrentMatchTeamSide(team_id);
	return GetCurrentMatchPlayerStats( side, player_id );
}

const RUDB_STATS_PLAYER*  RUStatisticsSystem::GetCurrentMatchPlayerStats(SSTEAMSIDE team_side, unsigned short player_id) const
{
	return const_cast<RUStatisticsSystem*>(this)->GetCurrentMatchPlayerStats(team_side, player_id);
}

RUDB_STATS_TEAM* RUStatisticsSystem::GetCurrentMatchTeamStats(SSTEAMSIDE team_side)
{
	size_t team_index;
	if (!GetCurrentMatchTeamIndex(team_side, team_index))
		return NULL;

	RUDB_STATS_TEAM& result = current_match_team_stats[team_index];

	// check this is the right one
	MABASSERT(result.match_id == current_match_id);
	MABASSERT(result.team_id == current_team_ids[team_index]);

	return &result;
}

const RUDB_STATS_TEAM* RUStatisticsSystem::GetCurrentMatchTeamStats(SSTEAMSIDE team_side) const
{
	return const_cast<RUStatisticsSystem*>(this)->GetCurrentMatchTeamStats(team_side);

}



/****************************************************/
/*****************     EVENT    *********************/
/*****************   LISTENER   *********************/
/*****************   FUNCTIONS  *********************/
/****************************************************/

void RUStatisticsSystem::AddScore(RUDB_STATS_PLAYER& player_stat, SSTEAMSIDE team_side, SSTEAMSIDE opposing_team_side, int points)
{
	MABASSERT(opposing_team_side!=SIDE_NONE);

	player_stat.points_scored += points;

	RUDB_STATS_TEAM* team_stat = GetCurrentMatchTeamStats(team_side);
	team_stat->score += points;

	RUDB_STATS_TEAM* opposing_team_stat = GetCurrentMatchTeamStats(opposing_team_side);
	opposing_team_stat->opponent_score += points;

	if( current_world != NULL ) current_world->GetEvents()->score_changed();
}

#if defined(ENABLE_SOAK_TEST) || defined(ENABLE_GAME_DEBUG_MENU)
/// For soaks, force the score...
void RUStatisticsSystem::ForceTeamScore(SSTEAMSIDE team_side, int score)
{
	RUDB_STATS_TEAM* team_stat = GetCurrentMatchTeamStats(team_side);
	team_stat->score = score;
}
#endif

//////////////////////////////////////////
// Conversion event listener
void RUStatisticsSystem::AddConversionInterface( bool success )
{
	ARugbyCharacter* kicker = current_world->GetGameState()->GetLastBallHolder();
	unsigned short player_id;
	SSTEAMSIDE team_side;
	GetPlayerId(kicker, &team_side, player_id);
	AddConversion(team_side, (team_side==SIDE_A)?SIDE_B:SIDE_A, player_id, success);
}

void RUStatisticsSystem::AddConversion(SSTEAMSIDE team_side, const SSTEAMSIDE opposing_team_side, unsigned short player_id, bool success)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;

	player_stat->conversion_attempts++;

	if(success)
	{
		player_stat->successful_conversion_attempts++;

		int conversion_score = SIFApplication::GetApplication()->GetMatchGameSettings()->GetConversionScore();

		AddScore(*player_stat, team_side, opposing_team_side, conversion_score);
	}
}

//////////////////////////////////////////
// Penalty goal event listener
void RUStatisticsSystem::AddPenaltyGoalInterface( bool success, const FVector & /*crossed_goal_position*/ )
{
	ARugbyCharacter* kicker = current_world->GetGameState()->GetLastBallHolder();
	unsigned short player_id;
	SSTEAMSIDE team_side;
	GetPlayerId(kicker, &team_side, player_id);
	AddPenaltyGoal(team_side,  (team_side==SIDE_A)?SIDE_B:SIDE_A, player_id, success);
}

void RUStatisticsSystem::AddPenaltyGoal(SSTEAMSIDE team_side, SSTEAMSIDE opposing_team_side, unsigned short player_id, bool success)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;

	player_stat->penalty_goal_attempts++;

	if(success)
	{
		player_stat->successful_penalty_goals++;

		int penalty_score = SIFApplication::GetApplication()->GetMatchGameSettings()->GetPenaltyScore();

		AddScore(*player_stat, team_side, opposing_team_side, penalty_score);
	}
}


//////////////////////////////////////////
// Handling error event listener
void RUStatisticsSystem::AddHandlingErrorInterface( ARugbyCharacter* player, bool /*from_tackle*/, const FVector& /*position*/ )
{
	unsigned short player_id;
	SSTEAMSIDE team;
	GetPlayerId(player, &team, player_id);
	AddHandlingError(team, player_id);
}

void RUStatisticsSystem::AddHandlingError(SSTEAMSIDE team_side, unsigned short player_id)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;

	player_stat->handling_errors++;
}

//////////////////////////////////////////
// Forward pass event listener
void RUStatisticsSystem::AddForwardPassHandlingErrorInterface( ARugbyCharacter* player1, ARugbyCharacter* player2 )
{
	unsigned short player_id_1 = 0, player_id_2 = 0;
	SSTEAMSIDE team1 = SIDE_NONE, team2 = SIDE_NONE;
	GetPlayerId(player1, &team1, player_id_1);
	if (player2) GetPlayerId(player2, &team2, player_id_2);
	AddForwardPassHandlingError(team1, player_id_1, team2, player_id_2);
}

void RUStatisticsSystem::AddForwardPassHandlingError(SSTEAMSIDE team_side1, unsigned short player_id_1, SSTEAMSIDE team_side2, unsigned short player_id_2)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_1_stat = GetCurrentMatchPlayerStats(team_side1, player_id_1);

	MABASSERTMSG(player_1_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_1_stat == NULL)
		return;

	player_1_stat->handling_errors++;

	if (player_id_2 > 0)
	{
		RUDB_STATS_PLAYER* player_2_stat = GetCurrentMatchPlayerStats(team_side2, player_id_2);

		MABASSERTMSG(player_2_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
		if(player_2_stat == NULL)
			return;

		player_2_stat->handling_errors++;
	}
}

//////////////////////////////////////////
// Hit up event listener
void RUStatisticsSystem::AddHitupInterface( ARugbyCharacter* player )
{
	unsigned short player_id;
	SSTEAMSIDE team;
	GetPlayerId(player, &team, player_id);
	AddHitup(team, player_id);
}

void RUStatisticsSystem::AddHitup(SSTEAMSIDE team_side, unsigned short player_id)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;

	player_stat->hitups++;
}

//////////////////////////////////////////
// Injury event listener
void RUStatisticsSystem::AddInjuryInterface( ARugbyCharacter* player, TACKLE_INJURY_TYPE injury_type )
{
	MABUNUSED(injury_type);
	unsigned short player_id;
	SSTEAMSIDE team;
	GetPlayerId(player, &team, player_id);
	AddInjury(team, player_id);
}

void RUStatisticsSystem::AddInjury(SSTEAMSIDE team_side, unsigned short player_id)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;

	player_stat->injuries++;
}

//////////////////////////////////////////
// meters gained event listener
void RUStatisticsSystem::AddRunningMetresGainedInterface( ARugbyCharacter* player, float metres_gained )
{
	unsigned short player_id;
	SSTEAMSIDE team;
	GetPlayerId(player, &team, player_id);

#ifdef ENABLE_GAME_DEBUG_MENU
	int idx = player->GetAttributes()->GetTeamIndex();
	SIFDebug::GetRulesDebugSettings()->SetLastMetersGainedPlayer((int)team*64 + idx);
#endif

	AddRunningMetresGained(team, player_id, metres_gained);
}

void RUStatisticsSystem::AddRunningMetresGained(SSTEAMSIDE team_side, unsigned short player_id, float metres_gained)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;

	player_stat->running_meters_gained += metres_gained;
}

//////////////////////////////////////////
// kicking meters gained event listener
void RUStatisticsSystem::AddKickingMetresGainedInterface( ARugbyCharacter* player, float metres_gained )
{
	unsigned short player_id;
	SSTEAMSIDE team;
	GetPlayerId(player, &team, player_id);

#ifdef ENABLE_GAME_DEBUG_MENU
	int idx = player->GetAttributes()->GetTeamIndex();
	SIFDebug::GetRulesDebugSettings()->SetLastMetersGainedPlayer((int)team*64 + idx);
#endif

	AddKickingMetresGained(team, player_id, metres_gained);
}

void RUStatisticsSystem::AddKickingMetresGained(SSTEAMSIDE team_side, unsigned short player_id, float metres_gained)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;

	player_stat->kicking_meters_gained += metres_gained;
}

//////////////////////////////////////////
// kick event listener
void RUStatisticsSystem::AddKickInterface( ARugbyCharacter* kicker, KickContext /*kick_context*/, KickType /*kick*/, const FVector& /*position*/ )
{
	unsigned short player_id;
	SSTEAMSIDE team;
	GetPlayerId(kicker, &team, player_id);
	AddKick(team, player_id);
}

void RUStatisticsSystem::AddKick(SSTEAMSIDE team_side, unsigned short player_id)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;

	player_stat->kicks++;
}

//////////////////////////////////////////
// line break event listener
void RUStatisticsSystem::AddLineBreakInterface( ARugbyCharacter* player, LINE_BREAK_TYPE /*line_break_type*/ )
{
	unsigned short player_id;
	SSTEAMSIDE team;
	GetPlayerId(player, &team, player_id);
	AddLineBreak(team, player_id);
}

void RUStatisticsSystem::AddLineBreak(SSTEAMSIDE team_side, unsigned short player_id)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;

	player_stat->line_breaks++;
}

//////////////////////////////////////////
// pass event listener
void RUStatisticsSystem::AddPassInterface( ARugbyCharacter* passing_player, ARugbyCharacter* /*player_to_pass_to*/, const FVector& /*target_position*/, PASS_TYPE type, bool success )
{
	// TODO: need to check on the pass types, what offloads were used for and if they're used now

	unsigned short passing_player_id;
	SSTEAMSIDE passing_team;
	GetPlayerId(passing_player, &passing_team, passing_player_id);
	// TODO: currently a null pointer on player_to_pass_to
	//attributes = player_to_pass_to->GetAttributes();
	//unsigned int recieving_player_id = attributes->GetIndex();
	AddPass(passing_team, passing_player_id, success, type == PT_OFFLOAD );
}

void RUStatisticsSystem::AddPass(SSTEAMSIDE team_side, unsigned short player_id, bool success, bool is_offload)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;

	if(success)
	{
		if ( is_offload )
			player_stat->offloads++;
	}
	else
	{
		player_stat->handling_errors++;
		//IncrementCurrentGameStats(passing_team_id, recieving_player_id, ISTAT_HANDLING_ERRORS, value);
	}

	if ( is_offload )
		player_stat->offloads_attempted++;
}

//////////////////////////////////////////
// penalty against event listener
void RUStatisticsSystem::AddPenaltyInterface( ARugbyCharacter* player, ARugbyCharacter* /*offended*/, const FVector& /*position*/ , PENALTY_REASON)
{
	unsigned short player_id;
	SSTEAMSIDE team;
	GetPlayerId(player, &team, player_id);
	AddPenalty(team, player_id);
}


void RUStatisticsSystem::AddPenalty(SSTEAMSIDE team_side, unsigned short player_id)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;

	player_stat->penalties_against++;

	// update team stats.

	SSTEAMSIDE opposing_team_side = (team_side==SIDE_A)?SIDE_B:SIDE_A;

	RUDB_STATS_TEAM* team_stat = GetCurrentMatchTeamStats(team_side);
	RUDB_STATS_TEAM* opp_team_stat = GetCurrentMatchTeamStats(opposing_team_side);

	team_stat->penalties_conceded++;
	opp_team_stat->penalties_awarded++;

}

//////////////////////////////////////////
// red card event listener
void RUStatisticsSystem::AddRedCardInterface( ARugbyCharacter* player )
{
	unsigned short player_id;
	SSTEAMSIDE team;
	GetPlayerId(player, &team, player_id);
	AddRedCard(team, player_id);
}

void RUStatisticsSystem::AddRedCard(SSTEAMSIDE team_side, unsigned short player_id)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;

	player_stat->red_cards++;
}

//////////////////////////////////////////
// scrum listener event listener
void RUStatisticsSystem::AddScrumInterface(RUTeam *winning_team )
{
	AddScrum(winning_team->GetSide());
}

void RUStatisticsSystem::AddScrum(SSTEAMSIDE team_side)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_TEAM* team_stat = GetCurrentMatchTeamStats(team_side);
	team_stat->scrums++;
}

//////////////////////////////////////////
// Tackle event listener
void RUStatisticsSystem::AddTackleInterface( const RUTackleResult& tackle_result )
{
	for (int i = 0; i < tackle_result.n_tacklers; ++i)
	{
		unsigned short player_id = 0;
		SSTEAMSIDE team;
		if (i < MAX_TACKLERS)
		{
			GetPlayerId(tackle_result.tacklers[i], &team, player_id);
			AddTackle(team, player_id, tackle_result.successful, tackle_result.tackle_result == TRT_HEAD_HIGH2);
		}
	}
}

void RUStatisticsSystem::AddTackle(SSTEAMSIDE team_side, unsigned short player_id, bool success, bool high)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;


	player_stat->tackle_attempts++;

	if (success)
		player_stat->successful_tackles++;

	if (high)
		player_stat->high_tackles++;
}

//////////////////////////////////////////
// Ruck entry event listener
void RUStatisticsSystem::AddRuckEnterInterface( ARugbyCharacter* player, const FVector&, RUZoneJoinType )
{
	unsigned short player_id = 0;
	SSTEAMSIDE team;
	GetPlayerId(player, &team, player_id);
	AddRuckEnter(team, player_id);
}

void RUStatisticsSystem::AddRuckEnter(SSTEAMSIDE team_side, unsigned short player_id)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;


	player_stat->ruck_entry++;
}

//////////////////////////////////////////
// Contest win event listener
void RUStatisticsSystem::AddRuckTurnOverInterface( ARugbyCharacter* /*holder*/, ARugbyCharacter* contester )
{
	unsigned short player_id = 0;
	SSTEAMSIDE team;
	GetPlayerId(contester, &team, player_id);
	AddRuckTurnOver(team, player_id);
}

void RUStatisticsSystem::AddRuckTurnOver(SSTEAMSIDE team_side, unsigned short player_id)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;


	player_stat->contest_win++;
}

//////////////////////////////////////////
// Ruck entry event listener
void RUStatisticsSystem::AddLineoutInterface( ARugbyCharacter* player, bool wasStolen, bool /*isCatcher*/ )
{
	unsigned short player_id = 0;
	SSTEAMSIDE team;
	GetPlayerId(player, &team, player_id);
	AddLineout(team, player_id, wasStolen);
}

void RUStatisticsSystem::AddLineout(SSTEAMSIDE team_side, unsigned short player_id, bool stolen)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;


	player_stat->lineout_steal_attempts++;

	if(stolen)
		player_stat->successful_lineout_steal++;
}

//////////////////////////////////////////
// Try event listener
void RUStatisticsSystem::AddTryInterface( bool success, bool /*penalty_try*/, ARugbyCharacter* try_scorer)
{
	unsigned short player_id;
	SSTEAMSIDE team;
	GetPlayerId(try_scorer, &team, player_id);

	SSTEAMSIDE opposing_team = current_world->GetGameState()->GetDefendingTeam()->GetSide();

	AddTry(team, opposing_team, player_id, success);
}

void RUStatisticsSystem::AddTry(SSTEAMSIDE team_side, const SSTEAMSIDE  opposing_team_side, unsigned short player_id, bool success)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;


	player_stat->try_attempts++;

	if(success)
	{
		int try_score = SIFApplication::GetApplication()->GetMatchGameSettings()->GetTryScore();

		player_stat->successful_try_attempts++;
		player_stat->tries_scored++;

		AddScore(*player_stat, team_side, opposing_team_side, try_score);

		// update team stats.
		RUDB_STATS_TEAM* team_stat = GetCurrentMatchTeamStats(team_side);
		RUDB_STATS_TEAM* opp_team_stat = GetCurrentMatchTeamStats(opposing_team_side);
		team_stat->tries_scored++;
		opp_team_stat->tries_conceded++;
	}
}

//////////////////////////////////////////
// yellow card event listener
void RUStatisticsSystem::AddYellowCardInterface( ARugbyCharacter* player )
{
	unsigned short player_id;
	SSTEAMSIDE team;
	GetPlayerId(player, &team, player_id);
	AddYellowCard(team, player_id);
}

void RUStatisticsSystem::AddYellowCard(SSTEAMSIDE team_side, unsigned short player_id)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;

	player_stat->yellow_cards++;
}

//////////////////////////////////////////
// half time event listener, requests the stats tracker information
void RUStatisticsSystem::AddHalfTimeInterface()
{
	AddHalf();
}

//////////////////////////////////////////
// full time event listener, requests the stats tracker information
void RUStatisticsSystem::AddFullTimeInterface()
{
	AddHalf();
}

void RUStatisticsSystem::AddHalf()
{
	RUStatsTracker* tracker = current_world->GetStatsTracker();

//	unsigned short team_a_id = current_world->GetTeam(SIDE_A)->GetDbTeam().GetDbId();
//	unsigned short team_b_id = current_world->GetTeam(SIDE_B)->GetDbTeam().GetDbId();

	AddHalf(SIDE_A, tracker->GetNumberOfFivePlusPhases(SIDE_A), tracker->GetBallPossession(SIDE_A), tracker->GetTerritory(SIDE_A));
	AddHalf(SIDE_B, tracker->GetNumberOfFivePlusPhases(SIDE_B), tracker->GetBallPossession(SIDE_B), tracker->GetTerritory(SIDE_B));

	tracker->Reset();

	current_match_half++;
}

void RUStatisticsSystem::AddHalf(SSTEAMSIDE team_side, int num_five_plus_phases, float possession, float territory)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_TEAM* team_stat = GetCurrentMatchTeamStats(team_side);
	team_stat->num_five_plus_phases += num_five_plus_phases;
	team_stat->possession += possession;
	team_stat->territory += territory;

}

void RUStatisticsSystem::AddDropGoalInterface(bool success, const FVector& /*position*/)
{
	ARugbyCharacter* player = current_world->GetGameState()->GetLastKicker();
	unsigned short player_id;

	SSTEAMSIDE team_side;
	GetPlayerId(player, &team_side, player_id);
	AddDropGoal(team_side, (team_side==SIDE_A)?SIDE_A:SIDE_B, player_id, success);
}

void RUStatisticsSystem::AddDropGoal(const SSTEAMSIDE team_side, SSTEAMSIDE opposing_team_side, unsigned short player_id, bool success)
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );
	RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id);

	MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
	if(player_stat == NULL)
		return;

	player_stat->field_goal_attempts++;

	if(success)
	{
		player_stat->successful_field_goals++;

		bool beyond40 = false;

		if (current_world)
		{
			float lastKickDistance = current_world->GetGameState()->GetLastKickPosition().z;

			//GGS SRA: calculate if the kick was less than 10m from the centreline, thus 40m from the tryline;
			if (FMath::Abs(lastKickDistance) < 10.0f)
			{
				beyond40 = true;
			}
		}
		else
		{
			//GGS SRA: for simulations, putting the 2-point drop goal rate at roughly 10%
			if (MabMath::Rand(10.0f) < 1.0f)
			{
				beyond40 = true;
			}
		}

		int drop_goal_score = SIFApplication::GetApplication()->GetMatchGameSettings()->GetDropGoalScore(beyond40);
		AddScore(*player_stat, team_side, opposing_team_side, drop_goal_score);
	}
}




///------------------------------------------------------------------------
/// Works out each player's performance and ranks the top three.
///------------------------------------------------------------------------

void RUStatisticsSystem::CalculatePlayerPerformances()
{
	//#rc3_legacy float game_length = (float) SIFGameHelpers::GAGetGameLength();
	//#rc3_legacy MABUNUSED(game_length);

	// This is a fairly rudimentary assessment, and it should get looked at by
	// someone who actually understands the sport. For now though, it's
	// primarily based on metres gained, tries scored, and tackles made. Points
	// are taken off for lots of missed tackles and passes. Lots of points are
	// taken off for getting sin binned.

	top_players_index[0] = -1;
	top_players_team_index[0] = -1;
	top_players_db_id[1] = DB_INVALID_ID;
	top_players_score[2] = 0.0f;

	const bool sevens = false; // Nick WWS 7s to Womens 13s // SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GameModeIsR7();
	const int playersOnTeamIncBench = RUGameSettings::RU_GAME_MODE_LIMITS::GetNumberOfPlayersPerTeamIncBench(sevens);

	RUCareerModeManager*	manager = SIFApplication::GetApplication()->GetCareerModeManager();
	RL3Database*			database = manager->GetRL3Database();
	RL3TableCache*			performance_cache = database->GetPlayerPerformanceValuesCache();

	for (int side = 0; side < 2; side++)
	{
		for (int player = 0; player < playersOnTeamIncBench; player++)
		{
			performances[side][player] = 0.0f;

			unsigned short player_id = current_match_player_stats[side][player].player_id;
			RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats((SSTEAMSIDE)side, player_id);

			MABASSERTMSG(player_stat, "Work out why the player cannot be found. Game will crash when we access this NULL pointer.");
			if(player_stat == NULL)
				continue;


			RL3DB_PLAYER db_player(player_id);
			// Get the player position as an index
			int positionIndex = PlayerPositionEnum::GetPositionIndexFromPlayerPosition(db_player.GetPositionCategory(sevens ? 3 : 0));

			// The database is set up in such a way where it has sevens positions, then fifteens positions in order.
			if (!sevens) positionIndex += 7;

			rudb_player_performance_values_row* row_data = (rudb_player_performance_values_row*)performance_cache->GetRowByIndex((unsigned int)(positionIndex));

			// Positive impact on performance
			performances[side][player] += CalculateValueOf(TRIES_SCORED, player_stat, row_data);
			performances[side][player] += CalculateValueOf(RUNNING_METERS, player_stat, row_data);
			performances[side][player] += CalculateValueOf(KICKING_METERS, player_stat, row_data);
			performances[side][player] += CalculateValueOf(TACKLES_SUCCESSFUL, player_stat, row_data);
			performances[side][player] += CalculateValueOf(LINE_BREAKS, player_stat, row_data);
			performances[side][player] += CalculateValueOf(OFFLOADS_SUCCESSFUL, player_stat, row_data);
			performances[side][player] += CalculateValueOf(DROP_GOALS_SUCCESSFUL, player_stat, row_data);
			performances[side][player] += CalculateValueOf(CONVERSIONS_SUCCESSFUL, player_stat, row_data);

			// Negative impact on performance
			performances[side][player] += CalculateValueOf(HANDLING_ERRORS, player_stat, row_data);
			performances[side][player] += CalculateValueOf(PENALTIES_CONCEDED, player_stat, row_data);
			performances[side][player] += CalculateValueOf(TACKLES_FAILED, player_stat, row_data);
			performances[side][player] += CalculateValueOf(YELLOW_CARDS, player_stat, row_data);
			performances[side][player] += CalculateValueOf(RED_CARDS, player_stat, row_data);

			// You must really suck to get < 0
			performances[side][player] = MabMath::Max(0.0f, performances[side][player]);

			// HES Ticket #10356 BE A PRO - Add on a 10% Win Bonus to Performance Rating
			if(manager->IsActive() && manager->GetIsCareerModePro() && manager->IsProPlayer(player_id))
			{
				if( GetCurrentMatchTeamStats(SSTEAMSIDE(side))->score > GetCurrentMatchTeamStats(SSTEAMSIDE((side == 0 ? 1 : 0)))->score )
				{
					//if(performances[side][player] <= 80.0f)
					performances[side][player] *= 1.1f;
					performances[side][player] = MabMath::Min(100.0f, performances[side][player]);
				}


#if defined ENABLE_GAME_DEBUG_MENU
				if(SIFDebug::GetProModeDebugSettings()->GetPerformanceRating() > 0)
				{
					performances[side][player] = (float)(SIFDebug::GetProModeDebugSettings()->GetPerformanceRating() * 10);
				}
#endif
			}

			// Make sure we didn't do anything stupid.
			MABASSERT(performances[side][player] <= 100.0f);

			// We've been working with scores up to 100. We had it up to 1.0 before. So let's keep that for now.
			if(performances[side][player] > 0.0f)
				performances[side][player] /= 100.0f;

			// Make sure for a second time we haven't done anything stupid.
			MABASSERT(performances[side][player] >= 0.0f && performances[side][player] <= 1.0f);

			int position = -1;
			// Check the
			for (int i = 0; i < 3; i++)
			{
				// If this top X position has not been filled, take it.
				if (top_players_index[i] == -1)
				{
					position = i;
					break;
				}
				// This top X position is filled, check if our performance is higher than what was stored.
				else if (performances[side][player] > performances[top_players_team_index[i]][top_players_index[i]])
				{
					position = i;
					break;
				}
			}

			// We found a top 3 position
			if (position != -1)
			{
				// Push everyone down a notch.
				for (int i = 1; i >= position; i--)
				{
					top_players_index[i + 1]		= top_players_index[i];
					top_players_team_index[i + 1]	= top_players_team_index[i];
					top_players_score[i + 1]		= top_players_score[i];
					top_players_db_id[i + 1]		= top_players_db_id[i];
				}

				// Store this players stuff in the arrays
				top_players_index[position]			= player;
				top_players_team_index[position]	= side;
				top_players_score[position]			= performances[side][player];
				top_players_db_id[position]			= player_id;
			}
		}
	}
}

float RUStatisticsSystem::CalculateValueOf(PERFORMANCE_INDICATOR kpi, RUDB_STATS_PLAYER* player_stat, rudb_player_performance_values_row* row_data)
{
	float num = 0.0f;
	float val = 0.0f;
	float cap = 0.0f;

	// Our return value
	float val_change = 0.0f;
	float sign = 1.0f;

	// Work out our value, and cap depending on the KPI
	switch (kpi)
	{
	case TRIES_SCORED:
		num = (float)player_stat->tries_scored;
		val = (float)row_data->try_val / 100.0f;//DB stores int
		cap = (float)row_data->try_cap;
		break;
	case RUNNING_METERS:
		num = player_stat->running_meters_gained;
		val = (float)row_data->running_meters_val / 100.0f;//DB stores int
		cap = (float)row_data->running_meters_cap;
		break;
	case KICKING_METERS:
		num = player_stat->kicking_meters_gained;
		val = (float)row_data->kicking_meters_val / 100.0f;//DB stores int
		cap = (float)row_data->kicking_meters_cap;
		break;
	case TACKLES_SUCCESSFUL:
		num = (float)player_stat->successful_tackles;
		val = (float)row_data->tackles_val / 100.0f;//DB stores int
		cap = (float)row_data->tackles_cap;
		break;
	case LINE_BREAKS:
		num = (float)player_stat->line_breaks;
		val = (float)row_data->line_breaks_val / 100.0f;//DB stores int
		cap = (float)row_data->line_breaks_cap;
		break;
	case OFFLOADS_SUCCESSFUL:
		num = (float)player_stat->offloads;
		val = (float)row_data->offloads_val / 100.0f;//DB stores int
		cap = (float)row_data->offloads_cap;
		break;
	case DROP_GOALS_SUCCESSFUL:
		{
			float goal_kick_num = (float)player_stat->field_goal_attempts;
			// I also like to live dangerously
			if(goal_kick_num > 0)
			{
				float ratio = (float)player_stat->successful_field_goals / goal_kick_num;
				val_change = ratio * (float)row_data->drop_goal_cap;
				MABASSERT(val_change >= 0 && val_change <= row_data->drop_goal_cap);
			}

			//Special case
			return val_change;
		}
	case CONVERSIONS_SUCCESSFUL:
		{
			float conversion_kick_num = (float)player_stat->conversion_attempts;
			if(conversion_kick_num > 0)
			{
				float ratio = (float)player_stat->successful_conversion_attempts / conversion_kick_num;
				val_change = ratio * (float)row_data->goal_kick_cap;
				MABASSERT(val_change >= 0 && val_change <= row_data->goal_kick_cap);
			}

			//Special case
			return val_change;
		}
	case HANDLING_ERRORS:
		num = (float)player_stat->handling_errors;
		val = (float)row_data->handling_errors_val / 100.0f;//DB stores int
		cap = (float)row_data->handling_errors_cap;
		sign = -1.0f;
		break;
	case PENALTIES_CONCEDED:
		num = (float)player_stat->penalties_against;
		val = (float)row_data->penalties_conceded_val / 100.0f;//DB stores int
		cap = (float)row_data->penalties_conceded_cap;
		sign = -1.0f;
		break;
	case TACKLES_FAILED:
		num = (float)(player_stat->tackle_attempts - player_stat->successful_tackles);
		val = (float)row_data->miss_tackles_val / 100.0f;//DB stores int
		cap = (float)row_data->miss_tackles_cap;
		sign = -1.0f;
		break;
	case YELLOW_CARDS:
		num = (float)player_stat->yellow_cards;
		val = (float)row_data->yellow_card_val / 100.0f;//DB stores int
		cap = (float)row_data->yellow_card_cap;
		sign = -1.0f;
		break;
	case RED_CARDS:
		num = (float)player_stat->red_cards;
		val = (float)row_data->red_card_val / 100.0f;//DB stores int
		cap = (float)row_data->red_card_cap;
		sign = -1.0f;
		break;
	default:
		break;
	}

	val_change = num * val;
	MabMath::Clamp(val_change, 0.0f, cap);

	// Yes this can be true.
	if( val_change != 0.0f )
		val_change *= sign;

	return val_change;



}

///--------------------------------------------------------------------------
/// Returns the top three performing players in the match.
///--------------------------------------------------------------------------

int RUStatisticsSystem::GetTopPerformingPlayerIndex(int index)
{
	MABASSERT(index >= 0 && index < 3);

	return top_players_index[index];
}

///--------------------------------------------------------------------------
/// Returns the DB ID for the top three performing players in the match.
///--------------------------------------------------------------------------

unsigned short RUStatisticsSystem::GetTopPerformingPlayerDBID(int index)
{
	MABASSERT(index >= 0 && index < 3);

	return top_players_db_id[index];
}

///--------------------------------------------------------------------------
/// Returns the DB ID for the top three performing players in the match.
///--------------------------------------------------------------------------

float RUStatisticsSystem::GetPerformanceForPlayerIDInTeamID(unsigned short player_id, unsigned short team_id)
{
	float performance = -1.0f;
	// Check for our team
	for(int i = 0; i < 2; i++)
	{
		// This is our team.
		if(current_match_player_stats[i][0].team_id == team_id)
		{
			for(int j = 0; j < (int)current_match_player_stats[i].size()/*NUM_PLAYERS_PER_TEAM_INC_BENCH_INIT*/; j ++)
			{
				// The current match stats is a vector and has a different length to the performances array length.
				//if((size_t)j >= current_match_player_stats[i].size())
				//	continue;

				if(current_match_player_stats[i][j].player_id == player_id)
				{
					performance = performances[i][j];
				}
			}
		}
	}

	MABASSERTMSG(performance >= 0.0f, MabString(0, "Couldn't find the performance for player ID (%i), in team ID (%i)", player_id, team_id).c_str());
	return performance;
}


///--------------------------------------------------------------------------
///--------------------------------------------------------------------------

void RUStatisticsSystem::AddTeamStats(RL3DB_TEAM team, const RUDB_STATS_TEAM &team_match_stats, unsigned short comp_instance_db_id, int satellite_index, bool is_preliminary_round)
{
	// N.B: Games played, lost, won etc.. are handled in RL3Tournament::PostResult/UpdateConfidencesAndStreaksAfterMatch/PostBye...

	team.IncrementCompetitionTriesConceded(comp_instance_db_id, satellite_index, team_match_stats.tries_conceded);
	team.IncrementCompetitionTriesScored(comp_instance_db_id, satellite_index, team_match_stats.tries_scored);
	team.IncrementCompetitionPenaltiesAwardedAgainst(comp_instance_db_id, satellite_index, team_match_stats.penalties_conceded);
	team.IncrementCompetitionPenaltiesAwarded(comp_instance_db_id, satellite_index, team_match_stats.penalties_awarded);

	if(is_preliminary_round)
	{
		team.IncrementCompetitionPreliminaryTriesConceded(comp_instance_db_id, satellite_index, team_match_stats.tries_conceded);
		team.IncrementCompetitionPreliminaryTriesScored(comp_instance_db_id, satellite_index, team_match_stats.tries_scored);
		team.IncrementCompetitionPreliminaryPenaltiesAwardedAgainst(comp_instance_db_id, satellite_index, team_match_stats.penalties_conceded);

		// Todo increment bonus points.
	}

	/// Match 'maximum' statistics...

	if( team_match_stats.tries_scored > team.GetCompetitionMatchMaxTriesScored(comp_instance_db_id))
		team.SetCompetitionMatchMaxTriesScored(comp_instance_db_id, team_match_stats.tries_scored);

	if( team_match_stats.tries_conceded > team.GetCompetitionMatchMaxTriesConceded(comp_instance_db_id))
		team.SetCompetitionMatchMaxTriesConceded(comp_instance_db_id, team_match_stats.tries_conceded);

	if( team_match_stats.score > team.GetCompetitionMatchMaxPointsFor(comp_instance_db_id))
		team.SetCompetitionMatchMaxPointsFor(comp_instance_db_id, team_match_stats.score);

	if( team_match_stats.opponent_score > team.GetCompetitionMatchMaxPointsAgainst(comp_instance_db_id))
		team.SetCompetitionMatchMaxPointsAgainst(comp_instance_db_id, team_match_stats.opponent_score);

	if( team_match_stats.penalties_conceded > team.GetCompetitionMatchMaxPenaltiesAwardedAgainst(comp_instance_db_id))
		team.SetCompetitionMatchMaxPenaltiesAwardedAgainst(comp_instance_db_id, team_match_stats.penalties_conceded);
}

///--------------------------------------------------------------------------
/// Add any cummulative player stats to team stats....
///--------------------------------------------------------------------------

void RUStatisticsSystem::AddTeamStats(RL3DB_TEAM team, const RUDB_STATS_PLAYER &player_stats, unsigned short comp_instance_db_id, int satellite_index)
{
	/// Yellow cards, red cards, injuries???

	MABUNUSED(team);
	MABUNUSED(player_stats);
	MABUNUSED(comp_instance_db_id);
	MABUNUSED(satellite_index);
}

///--------------------------------------------------------------------------
///--------------------------------------------------------------------------

void RUStatisticsSystem::AddPlayerStats(RL3DB_PLAYER player, const RUDB_STATS_PLAYER &player_match_stats, unsigned short comp_instance_db_id)
{
	/// Optimized database access:- Be careful when accessing rows directly!

	RL3Database *database = RL3Database::GetInstance();


	RUCareerModeManager* manager = SIFApplication::GetApplication()->GetCareerModeManager();
	if(manager->IsActive() && !manager->GetIsCareerModeCoach() && manager->IsProPlayer(player.GetDbId()))
	{
		RL3TableCache *cache = database->GetCareerProPlayerStats();

		RL3DB_COMPETITION_INSTANCE	comp_inst(comp_instance_db_id);
		unsigned short comp_db_id = comp_inst.GetCompetitionId();
		unsigned short stat_index = cache->SelectWhere(	CCDB_CAREERPROPLAYERSTATS_COMP_ID, comp_db_id,
														CCDB_CAREERPROPLAYERSTATS_YEAR, (unsigned short)comp_inst.GetStartDate().GetYear(), false);

		// Meaning we dont have stats for this comp yet
		if(stat_index==0)
		{
			stat_index = cache->AddNewEntry();
		}

		rudb_career_pro_player_stats_row *player_stats = (rudb_career_pro_player_stats_row*)cache->GetRowStart(stat_index);
		player_stats->comp_id					=	comp_db_id;
		player_stats->year						=	(unsigned short)comp_inst.GetStartDate().GetYear();

		player_stats->tries_scored				+=	(unsigned char)player_match_stats.tries_scored;
		player_stats->tackle_attempts			+=	(unsigned short)player_match_stats.tackle_attempts;
		player_stats->successful_tackles		+=	(unsigned short)player_match_stats.successful_tackles;
		player_stats->line_breaks				+=	(unsigned char)player_match_stats.line_breaks;
		player_stats->offloads					+=	(unsigned char)player_match_stats.offloads;
		player_stats->offloads_attempted		+=	(unsigned char)player_match_stats.offloads_attempted;
		player_stats->kicks						+=	(unsigned char)player_match_stats.kicks;
		player_stats->handling_errors			+=	(unsigned char)player_match_stats.handling_errors;
		player_stats->field_goal_attempts		+=	(unsigned char)player_match_stats.field_goal_attempts;
		player_stats->successful_field_goals	+=	(unsigned char)player_match_stats.successful_field_goals;
		player_stats->points_scored				+=	(unsigned short)player_match_stats.points_scored;
		player_stats->yellow_cards				+=	(unsigned char)player_match_stats.yellow_cards;
		player_stats->red_cards					+=	(unsigned char)player_match_stats.red_cards;
		player_stats->injuries					+=	(unsigned char)player_match_stats.injuries;
		player_stats->penalties_against			+=	(unsigned char)player_match_stats.penalties_against;
		player_stats->games_played				+=	(unsigned char)1;
		player_stats->running_meters			+=	(unsigned short)player_match_stats.running_meters_gained;
		player_stats->kicking_meters			+=	(unsigned short)player_match_stats.kicking_meters_gained;
		player_stats->goal_attempts				+=	(unsigned char)player_match_stats.conversion_attempts;
		player_stats->successful_goals			+=	(unsigned char)player_match_stats.successful_conversion_attempts;
		player_stats->successful_penalties		+=	(unsigned char)player_match_stats.successful_penalty_goals;
		player_stats->penalty_attempts			+=	(unsigned char)player_match_stats.penalty_goal_attempts;

		player_stats->ruck_entry				+=	(unsigned char)player_match_stats.ruck_entry;
		player_stats->contest_win				+=	(unsigned char)player_match_stats.contest_win;
		player_stats->lineout_steal_attempts	+=	(unsigned char)player_match_stats.lineout_steal_attempts;
		player_stats->successful_lineout_steal	+=	(unsigned char)player_match_stats.successful_lineout_steal;

	}

	RL3TableCache *cache = database->GetCompInstPlayerStatsCache();

	unsigned short stat_index = cache->SelectWhere(CCDB_COMPINSTPLAYERSTATS_INSTANCE_ID,comp_instance_db_id, CCDB_COMPINSTPLAYERSTATS_PLAYER_ID, player.GetDbId(), false);
	if(stat_index==0)
		return;

	rudb_comp_inst_player_stats_row *player_stats = (rudb_comp_inst_player_stats_row*)cache->GetRowStart(stat_index);

	player_stats->tries_scored				+=	(unsigned char)player_match_stats.tries_scored;
	player_stats->tackle_attempts			+=	(unsigned short)player_match_stats.tackle_attempts;
	player_stats->successful_tackles		+=	(unsigned short)player_match_stats.successful_tackles;
	player_stats->line_breaks				+=	(unsigned char)player_match_stats.line_breaks;
	player_stats->offloads					+=	(unsigned char)player_match_stats.offloads;
	player_stats->offloads_attempted		+=	(unsigned char)player_match_stats.offloads_attempted;
	player_stats->kicks						+=	(unsigned char)player_match_stats.kicks;
	player_stats->handling_errors			+=	(unsigned char)player_match_stats.handling_errors;
	player_stats->field_goal_attempts		+=	(unsigned char)player_match_stats.field_goal_attempts;
	player_stats->successful_field_goals	+=	(unsigned char)player_match_stats.successful_field_goals;
	player_stats->points_scored				+=	(unsigned short)player_match_stats.points_scored;
	player_stats->yellow_cards				+=	(unsigned char)player_match_stats.yellow_cards;
	player_stats->red_cards					+=	(unsigned char)player_match_stats.red_cards;
	player_stats->injuries					+=	(unsigned char)player_match_stats.injuries;
	player_stats->penalties_against			+=	(unsigned char)player_match_stats.penalties_against;
	player_stats->games_played				+=	(unsigned char)1;
	player_stats->running_meters			+=	(unsigned short)player_match_stats.running_meters_gained;
	player_stats->kicking_meters			+=	(unsigned short)player_match_stats.kicking_meters_gained;
	player_stats->goal_attempts				+=	(unsigned char)player_match_stats.conversion_attempts;
	player_stats->successful_goals			+=	(unsigned char)player_match_stats.successful_conversion_attempts;
	player_stats->successful_penalties		+=	(unsigned char)player_match_stats.successful_penalty_goals;
	player_stats->penalty_attempts			+=	(unsigned char)player_match_stats.penalty_goal_attempts;

	player_stats->ruck_entry				+=	(unsigned char)player_match_stats.ruck_entry;
	player_stats->contest_win				+=	(unsigned char)player_match_stats.contest_win;
	player_stats->lineout_steal_attempts	+=	(unsigned char)player_match_stats.lineout_steal_attempts;
	player_stats->successful_lineout_steal	+=	(unsigned char)player_match_stats.successful_lineout_steal;

}

///--------------------------------------------------------------------------
///--------------------------------------------------------------------------

void RUStatisticsSystem::AddCompetitionStats(unsigned short comp_instance_db_id, bool is_preliminary_round)
{
	/// ***** BLEDISLOE_SUB_COMPETITION_HACK ****** (search for this to get all related code)
	/// If quad nations + oz v nz, then also a bledisloe cup match (if in franchise mode).

	RUCareerModeManager*		manager = SIFApplication::GetApplication()->GetCareerModeManager();
	RL3DB_COMPETITION_INSTANCE	comp_inst(comp_instance_db_id);
	unsigned short bledisloe_comp_id = 0;

	if(comp_inst.GetCompetitionId()==DB_COMPID_QUADNATIONS && manager->IsInFranchise())
	{
		if((current_team_ids[0]==DB_TEAMID_NZ && current_team_ids[1]==DB_TEAMID_AUSTRALIA) ||
			(current_team_ids[0]==DB_TEAMID_AUSTRALIA && current_team_ids[1]==DB_TEAMID_NZ))
		{
			bledisloe_comp_id = manager->GetTournament()->GetBledisloeCompetition();
		}
	}

	// Update the team stats.
	for (int team_index = 0; team_index < 2; team_index++)
	{
		RL3DB_TEAM team( current_team_ids[team_index] );

		AddTeamStats( team, current_match_team_stats[team_index], comp_instance_db_id, comp_inst.GetCurrentSatellite(), is_preliminary_round );

		if(bledisloe_comp_id!=0)
		{	/// ***** BLEDISLOE_SUB_COMPETITION_HACK ****** (search for this to get all related code)
			AddTeamStats( team, current_match_team_stats[team_index], bledisloe_comp_id, comp_inst.GetCurrentSatellite(), is_preliminary_round );
		}

		// Update the team's player's stats.
		int num_players = (int)current_match_player_stats[team_index].size();
		for (int player_index = 0; player_index < num_players; player_index++)
		{
			RL3DB_PLAYER player(current_match_player_stats[team_index][player_index].player_id);
			AddPlayerStats( player, current_match_player_stats[team_index][player_index], comp_instance_db_id);
			AddTeamStats(team, current_match_player_stats[team_index][player_index], comp_instance_db_id, comp_inst.GetCurrentSatellite());

			if(bledisloe_comp_id!=0)
			{	/// ***** BLEDISLOE_SUB_COMPETITION_HACK ****** (search for this to get all related code)
				AddPlayerStats( player, current_match_player_stats[team_index][player_index], bledisloe_comp_id);
				AddTeamStats(team, current_match_player_stats[team_index][player_index], bledisloe_comp_id, comp_inst.GetCurrentSatellite());
			}
		}
	}
}

static void GetCombinedTryScorerString( int num_tries, const char* team_abbreviation, const MabString& player_last_name, MabString& combined_try_scorer_string_out )
{
	// Combined string is in the format "Barrett [USA] 4".
	// Double-[ is our way of indicating a single [ to the translator.
	combined_try_scorer_string_out = MabString( 0, "%s [[%s] %d", player_last_name.c_str(), team_abbreviation, num_tries );
}

/// Helper method to write our try scorer strings to the database. They come in the form of the try leader map.
static void WriteTryScorersToDatabase( const MabMultiMap< int, MabString >& try_leader_map, RL3TableCache& player_match_stats_cache, const MabVector< unsigned short >& player_match_stat_db_id_list )
{
	// First iter goes through the enums above...
	const int* try_scorer_enum_iter = RUStatisticsSystem::TRY_SCORER_ENUM_LIST;

	// Second iterator goes across the list of all players with tries....BACKWARDS (so more tries is ranked higher.)
	MabMultiMap< int, MabString >::const_reverse_iterator try_leader_iter = try_leader_map.rbegin();
	const MabMultiMap< int, MabString >::const_reverse_iterator endpoint = try_leader_map.rend();

	// Complicated conditionals: need to check that we've not filled out all three enums, and that we're not out of try-scorers.
	while( try_scorer_enum_iter < RUStatisticsSystem::TRY_SCORER_ENUM_LIST + StaticArraySize( RUStatisticsSystem::TRY_SCORER_ENUM_LIST ) && try_leader_iter != endpoint )
	{
		const int num_tries = try_leader_iter->first;

		// Get all the players that have equal numbers of tries to this number.
		std::pair< MabMultiMap< int, MabString >::const_iterator, MabMultiMap< int, MabString >::const_iterator > equal_range = try_leader_map.equal_range( num_tries );
		const size_t num_matching_elts = try_leader_map.count( num_tries );

		// This is the number of display slots; the number of values in TRY_SCORER_ENUM_LIST that haven't already been filled up.
		size_t num_slots_remaining = StaticArraySize( RUStatisticsSystem::TRY_SCORER_ENUM_LIST ) - ( try_scorer_enum_iter - RUStatisticsSystem::TRY_SCORER_ENUM_LIST );

		// We will either have fewer items to write than slots available, which is easy: just write all the items and continue.....
		if( num_matching_elts <= num_slots_remaining )
		{
			for( MabMultiMap< int, MabString >::const_iterator subset_iter = equal_range.first; subset_iter != equal_range.second; ++subset_iter, ++try_scorer_enum_iter, ++try_leader_iter )
			{
				for( MabVector< unsigned short >::const_iterator player_match_stat_db_id_iter = player_match_stat_db_id_list.begin(); player_match_stat_db_id_iter != player_match_stat_db_id_list.end(); ++player_match_stat_db_id_iter )
				{
					player_match_stats_cache.WriteString( *player_match_stat_db_id_iter, *try_scorer_enum_iter, subset_iter->second.c_str() );
				}
			}
		}
		// ...or we will have too many items for our number of remaining slots.
		// In this case, get a random selection of the options and shrink the list to the number of slots available.
		else
		{
			// Pass through one more time and get a random distribution of the options available.
			MabVector< MabString > full_option_list;
			full_option_list.reserve( num_matching_elts );
			for( MabMultiMap< int, MabString >::const_iterator subset_iter = equal_range.first; subset_iter != equal_range.second; ++subset_iter )
			{
				full_option_list.push_back( subset_iter->second );
			}
			CustomShuffle( full_option_list.begin(), full_option_list.end() );
			full_option_list.resize( num_slots_remaining );

			for( MabVector< MabString >::const_iterator shuffled_shrunken_list_iter = full_option_list.begin(); shuffled_shrunken_list_iter != full_option_list.end(); ++shuffled_shrunken_list_iter, ++try_scorer_enum_iter, ++try_leader_iter )
			{
				for( MabVector< unsigned short >::const_iterator player_match_stat_db_id_iter = player_match_stat_db_id_list.begin(); player_match_stat_db_id_iter != player_match_stat_db_id_list.end(); ++player_match_stat_db_id_iter )
				{
					player_match_stats_cache.WriteString( *player_match_stat_db_id_iter, *try_scorer_enum_iter, shuffled_shrunken_list_iter->c_str() );
				}
			}
		}
	}

}

void RUStatisticsSystem::SavePlayerMatchStatistics()
{
	if( current_competition_id == SQLITEMAB_INVALID_ID || current_match_id == SQLITEMAB_INVALID_ID )
	{
		MABBREAK();
		return;
	}


	RL3Database* const database = RL3Database::GetInstance();
	RL3TableCache* const player_match_stats_cache = database->GetPlayerMatchStatsCache();

	// Map from number of tries to a map from country names to combined try-scoring strings.
	MabMultiMap< int, MabString > try_leader_map( SIFHEAP_DYNAMIC );

	MabVector< unsigned short > player_match_stat_db_id_list;

	for( size_t team_index=0u; team_index < 2u; ++team_index )
	{
		const unsigned short player_match_stat_db_id = player_match_stats_cache->AddNewEntry();

		player_match_stat_db_id_list.push_back( player_match_stat_db_id );

		const RUDB_STATS_TEAM& team_stats = current_match_team_stats[ team_index ];

		MABASSERT(team_stats.competition_id != SQLITEMAB_INVALID_ID && team_stats.competition_id == current_competition_id);
		MABASSERT(team_stats.match_id != SQLITEMAB_INVALID_ID && team_stats.match_id == current_match_id);
		MABASSERT(team_stats.team_id != SQLITEMAB_INVALID_ID && team_stats.team_id == current_team_ids[team_index]);

		player_match_stats_cache->WriteUShort( player_match_stat_db_id, CCDB_COMPINSTPLAYERMATCHSTATS_COMP_INST_MATCH_ID, team_stats.match_id );
		player_match_stats_cache->WriteUShort( player_match_stat_db_id, CCDB_COMPINSTPLAYERMATCHSTATS_TEAM_ID, team_stats.team_id );

		player_match_stats_cache->WriteFloat( player_match_stat_db_id, CCDB_COMPINSTPLAYERMATCHSTATS_POSSESSION, team_stats.possession );
		player_match_stats_cache->WriteFloat( player_match_stat_db_id, CCDB_COMPINSTPLAYERMATCHSTATS_TERRITORY, team_stats.territory );

		player_match_stats_cache->WriteUChar( player_match_stat_db_id, CCDB_COMPINSTPLAYERMATCHSTATS_PENALTIES_CONCEDED, static_cast< unsigned char >(  team_stats.penalties_conceded ) );
		player_match_stats_cache->WriteUChar( player_match_stat_db_id, CCDB_COMPINSTPLAYERMATCHSTATS_TRIES, static_cast< unsigned char >( team_stats.tries_scored ) );

		// Now it gets a little complicated:
		// conversions and drop goals need to be summed, and try leaders need to be found.
		//
		// Values for sums...
		unsigned short num_conversions = 0u, num_drop_goals = 0u, num_handling_errors = 0u, num_penalty_goals = 0u;

		// Get the player stats for this particular team in this particular match.
		const MabVector< RUDB_STATS_PLAYER >& team_player_stats = current_match_player_stats[ team_index ];

		// Now iterate through all players....
		for( size_t player_index=0u, size=team_player_stats.size(); player_index < size; ++player_index )
		{
			const RUDB_STATS_PLAYER& player_stats = team_player_stats[ player_index ];

			// Add in these player stats....
			num_conversions += static_cast< unsigned short >( player_stats.successful_conversion_attempts );
			num_drop_goals += static_cast< unsigned short >( player_stats.successful_field_goals );
			num_handling_errors += static_cast< unsigned short >( player_stats.handling_errors );
			num_penalty_goals += static_cast< unsigned short >( player_stats.successful_penalty_goals );

			MABASSERT( player_stats.player_id != SQLITEMAB_INVALID_ID && player_stats.player_id == current_player_ids[ team_index ][ player_index ] );

			// ...and if the player is valid *and scored a try*.....
			if( player_stats.player_id != SQLITEMAB_INVALID_ID && player_stats.tries_scored > 0 )
			{
				// Then use this player to generate a try scorer string.
				RL3DB_PLAYER player = database->GetPlayer( player_stats.player_id );
				RL3DB_TEAM team = database->GetTeam( team_stats.team_id );

				MabString combined_string( SIFHEAP_DYNAMIC );

				GetCombinedTryScorerString( player_stats.tries_scored, team.GetMnemonic(), player.GetLastName(), combined_string );

				try_leader_map.insert( std::pair< int, MabString >( player_stats.tries_scored, combined_string ) );
			}
		}

		// Now write the summed conversions and drop goals....
		player_match_stats_cache->WriteUChar( player_match_stat_db_id, CCDB_COMPINSTPLAYERMATCHSTATS_CONVERSIONS, static_cast< unsigned char >(num_conversions) );
		player_match_stats_cache->WriteUChar( player_match_stat_db_id, CCDB_COMPINSTPLAYERMATCHSTATS_DROP_GOALS, static_cast< unsigned char >(num_drop_goals) );
		player_match_stats_cache->WriteUChar( player_match_stat_db_id, CCDB_COMPINSTPLAYERMATCHSTATS_HANDLING_ERRORS, static_cast< unsigned char >(num_handling_errors) );
		player_match_stats_cache->WriteUChar( player_match_stat_db_id, CCDB_COMPINSTPLAYERMATCHSTATS_PENALTIES_CONVERTED, static_cast< unsigned char >(num_penalty_goals) );
	}

	// Now finally write out the try scorers to the database.
	//
	WriteTryScorersToDatabase( try_leader_map, *player_match_stats_cache, player_match_stat_db_id_list );
}


// We aren't actually using this code right now, just commenting out in case there's a need for it later
#if 0
static void SqlError(sqlite3* db)
{
	const char* error_msg = sqlite3_errmsg(db);
	MABLOGMSG(LOGCHANNEL_SYSTEM, LOGTYPE_ERROR, error_msg);
	MABBREAKMSG(error_msg);
	MABUNUSED_NDEBUG(error_msg);
}

/// Wrapper for prepare, which makes it easy to log our queries
static int MabSqlitePrepare(sqlite3* db, const MabString& query_string, sqlite3_stmt** stmt)
{
	MABLOGDEBUG(query_string.c_str());
	return sqlite3_prepare_v2(db, query_string.c_str(), query_string.length(), stmt, NULL);
}

// Helper function for creating a select statement that selects all variables for the given type definition
// Largely taken from SqliteMabSteamer but with modifications required for stats purposes.
// \param database_manager	Game database manager to get sqlite database and type database from
// \param object			The stats object to create the select for
// \param constraints		Name value pair list of constraints to be added to the where clause, values of 0 are ignored
// \param sum_results		Flag if the columns should be summed, this must be true if not selecting a single row
template <typename T>
static sqlite3_stmt* PrepareSelect(RUGameDatabaseManager* database_manager, const T& object, const MabNamedValueList& constraints, bool sum_results)
{
	const MabCentralTypeDatabase2* type_database = &database_manager->GetTypeDatabase();
	const MabTypeDefinition2* type_definition = type_database->GetTypeDefinition(object.RTTGetClassName());
	MabCentralObjectTraversal traversal(type_database, NULL, type_definition);

	sqlite3* db = database_manager->GetDatabase()->GetDatabase();

	MabString constructed_query(SIFHEAP_DYNAMIC);
	constructed_query.reserve(1024);
	constructed_query = "SELECT ";

	bool first_column = true;
	while (traversal.NextVariable())
	{
		const MabTypeDefinition2::Variable& variable = traversal.GetVariable();
		MABASSERT(variable.name);
		MABASSERT(!variable.IsSubObject());
		MABASSERT(!variable.IsComplex());

		// if not the first column then output the seperator
		if (!first_column)
			constructed_query.append(",");
		else
			first_column = false;

		if (const MabNamedValue* constraint = constraints.GetNamedValue(variable.name))
		{
			// if this is one of our constraints, then just add the value to the select statement
			// this is to preserve the order of the results.
			constructed_query.append("'");
			constructed_query.append(constraint->ToString());
			constructed_query.append("'");
		}
		else if (sum_results)
		{
			constructed_query.append("SUM(");
			constructed_query.append(variable.name);
			constructed_query.append(")");
		}
		else
		{
			// otherwise add the column name to the query
			constructed_query.append(variable.name);
		}
	}

	// get the table name
	const char* table_name = type_definition->name;

	// add where clause
	MabString select_condition(SIFHEAP_DYNAMIC);

	select_condition.sprintf(" FROM %s WHERE ", table_name);

	bool first_constraint = true;
	for (unsigned int i=0; i<constraints.GetCount(); ++i)
	{
		// if the constraint is SQLITEMAB_INVALID_ID then don't include it in the where clause
		const MabNamedValue* constraint = &constraints.GetNamedValue(i);
		if (constraint->ToInt() == 0)
			continue;

		if (!first_constraint)
			select_condition.append(" AND ");
		else
			first_constraint = false;

		select_condition.append(constraint->GetName());
		select_condition.append("='");
		select_condition.append(constraint->ToString());
		select_condition.append("'");
	}

	constructed_query.append(select_condition);

	sqlite3_stmt* stmt;
	int rc = MabSqlitePrepare(db, constructed_query, &stmt);
	if (rc != SQLITE_OK)
	{
		MABLOGMSG(LOGCHANNEL_SYSTEM, LOGTYPE_ERROR, "PrepareSelectForObject failed: %s", constructed_query.c_str());
		SqlError(db);
		sqlite3_finalize(stmt);
		return NULL;
	}

	return stmt;
}

// Helper function for processing a select statement created by the PrepareSelect function.
// Largely taken from SqliteMabSteamer but with modifications required for stats purposes.
// \param database_manager	Game database manager to get sqlite database and type database from
// \param statement			The prepared sqlite3_stmt to process
// \param object			The stats struct the returned values are serialised into
template <typename T>
static bool ProcessStatement(RUGameDatabaseManager* database_manager, sqlite3_stmt* statement, T& object)
{
	int rc = sqlite3_step(statement);
	if (rc != SQLITE_ROW)
	{
		MABASSERT(rc == SQLITE_DONE); // if there were no results, that's OK, otherwise something bad happened

		int rc = sqlite3_finalize(statement);
		MABASSERT(rc == SQLITE_OK);
		MABUNUSED(rc);

		return false;
	}

	const MabCentralTypeDatabase2* type_database = &database_manager->GetTypeDatabase();
	const MabTypeDefinition2* type_definition = type_database->GetTypeDefinition(object.RTTGetClassName());
	MabCentralObjectTraversal traversal(type_database, &object, type_definition);

	int column_index = 0;
	while (traversal.NextVariable())
	{
		const MabTypeDefinition2::Variable& variable = traversal.GetVariable();
		MABASSERT(variable.name);
		MABASSERT(!variable.IsComplex());
		MABASSERT(!variable.IsSubObject());
		MABUNUSED(variable);

		SqliteMabStreamer::ColParam params;
		params.statement = statement;
		params.column_index = column_index;
		MabStreamMemory column_stream(SIFHEAP_DYNAMIC);
		MABVERIFY(column_stream.PushBack(reinterpret_cast<char*>(&params), sizeof(params)));
		traversal.DeserialiseVariable(NULL, &column_stream, 0, 1);
		++column_index;
	}

	MABASSERT(column_index == sqlite3_column_count(statement));
	MABASSERT((rc = sqlite3_step(statement)) == SQLITE_DONE);

	rc = sqlite3_finalize(statement);
	MABASSERT(rc == SQLITE_OK);

	return true;
}

static void PrepareConstraints(MabNamedValueList& constraints, unsigned short competition_id, unsigned short match_id, unsigned short team_id, unsigned short player_id)
{
	constraints.AddValue(MabNamedValue("id", 0));
	constraints.AddValue(MabNamedValue("competition_id", competition_id));
	constraints.AddValue(MabNamedValue("match_id", match_id));
	constraints.AddValue(MabNamedValue("team_id", team_id));
	constraints.AddValue(MabNamedValue("player_id", player_id));
}

static void PrepareConstraints(MabNamedValueList& constraints, unsigned short competition_id, unsigned short match_id, unsigned short team_id)
{
	constraints.AddValue(MabNamedValue("id", 0));
	constraints.AddValue(MabNamedValue("competition_id", competition_id));
	constraints.AddValue(MabNamedValue("match_id", match_id));
	constraints.AddValue(MabNamedValue("team_id", team_id));
}

bool RUStatisticsSystem::GetCompetitionStats(unsigned short competition_id, unsigned short team_id, unsigned short player_id, RUDB_STATS_PLAYER& player_stats) const
{
	MabNamedValueList constraints;
	PrepareConstraints(constraints, competition_id, 0, team_id, player_id);

	// always sum result when getting stats for the whole competition
	bool sum_result = true;

	if (sqlite3_stmt* stmt = PrepareSelect(game_db_manager, player_stats, constraints, sum_result))
	{
		return ProcessStatement(game_db_manager, stmt, player_stats);
	}

	return false;
}

bool RUStatisticsSystem::GetCompetitionStats(unsigned short competition_id, unsigned short team_id, RUDB_STATS_TEAM& team_stats) const
{
	MabNamedValueList constraints;
	PrepareConstraints(constraints, competition_id, 0, team_id);

	// always sum result when getting stats for the whole competition
	bool sum_result = true;

	if (sqlite3_stmt* stmt = PrepareSelect(game_db_manager, team_stats, constraints, sum_result))
	{
		return ProcessStatement(game_db_manager, stmt, team_stats);
	}

	return false;
}

bool RUStatisticsSystem::GetMatchStats(unsigned short match_id, unsigned short team_id, unsigned short player_id, RUDB_STATS_PLAYER& player_stats) const
{
	MabNamedValueList constraints;
	PrepareConstraints(constraints, 0, match_id, team_id, player_id);

	// sum the results if we are selecting by wildcard. We don't need to worry about competition_id because match_ids are specific to
	// a single competition anyway.
	bool sum_result = (match_id == SQLITEMAB_INVALID_ID || team_id == SQLITEMAB_INVALID_ID || player_id == SQLITEMAB_INVALID_ID);

	if (sqlite3_stmt* stmt = PrepareSelect(game_db_manager, player_stats, constraints, sum_result))
	{
		return ProcessStatement(game_db_manager, stmt, player_stats);
	}

	return false;

}

bool RUStatisticsSystem::GetMatchStats(unsigned short match_id, unsigned short team_id, RUDB_STATS_TEAM& team_stats) const
{
	MabNamedValueList constraints;
	PrepareConstraints(constraints, 0, match_id, team_id);

	// sum the results if we are selecting by wildcard. We don't need to worry about competition_id because match_ids are specific to
	// a single competition anyway.
	bool sum_result = (match_id == SQLITEMAB_INVALID_ID || team_id == SQLITEMAB_INVALID_ID);

	if (sqlite3_stmt* stmt = PrepareSelect(game_db_manager, team_stats, constraints, sum_result))
	{
		return ProcessStatement(game_db_manager, stmt, team_stats);
	}

	return false;
}
#endif
