// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.


#include "WWUIScreenControllerAssignment.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Utility/Helpers/SIFGameHelpers.h"
#include "Utility/Helpers/SIFUIHelpers.h"
#include "Utility/Helpers/SIFPlayerHelpers.h"
#include "Utility/Helpers/SIFInGameHelpers.h"
#include "Utility/Helpers/SIFMatchmakingHelpers.h"

#include "Rugby/UI/Screens/Modals/WWUIModalWarningMessage.h"

#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "Match/RugbyUnion/RUStadiumManager.h"
#include "GameModes/RugbyGameModeBase.h"
#include "WWUITabSwitcher.h"
#include "GameModes/RugbyGameViewportClient.h"
#include "Runtime/UMG/Public/Components/ProgressBar.h"
#include "WWUITranslationManager.h"
#include "RugbyGameInstance.h"
#include "Match/SIFGameWorld.h"
#include "RugbyCameraActor.h"
#include "SIFMissingControllerListener.h"

#include "WWUIRichTextBlockWithTranslate.h"
#include "WWUIScrollBox.h"
#include "Match/Components/SSHumanPlayer.h"
#include "WWUIScreenOnlineSearchResults.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Commentary/RUCommentary.h"
#include "Utility/Helpers/SIFAudioHelpers.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseLineOut.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/Ball/SSBall.h"

#define PROGRESSBARNAME "ProgressBar"
#define TITLENAME "Title"

void UWWUIScreenControllerAssignment::Startup(UWWUIStateScreenData* InData)
{
	SIFGameHelpers::GASetTeamStripsToGameSettings(false);

	//Get game instance
	if (URugbyGameInstance* gameInstance = GetWorld()->GetGameInstance<URugbyGameInstance>())
	{
		if (InData)
		{
			UWWUIControllerAssignmentInData * indata = Cast<UWWUIControllerAssignmentInData>(InData);
			GameMode = indata->GameMode;
			bFromInGame = indata->inGame;
			bPreOnlineMode = indata->bPreOnlineMode;
			bOnlineMode = indata->bOnlineMode;
			bForceSelection = indata->forceSelection;
			pSearchInData = indata->pSearchInData;

			// breadcrumb text defaults to single match
			if (indata->BreadcrumbString != "")
			{
				UWidget* pHeader = FindChildWidget(WWUIScreenAssignControllers_UI::BP_HeaderHalfTop);
				if (pHeader)
				{
					UTextBlock* pSubtitleText = Cast<UTextBlock>(FindChildOfTemplateWidget(pHeader, WWUIScreenAssignControllers_UI::Subtitle));
					if (pSubtitleText)
					{
						SetWidgetText(pSubtitleText, FText::FromString(UWWUITranslationManager::Translate(FString(indata->BreadcrumbString)).ToUpper()));
					}
					else
					{
						ensure(pSubtitleText);
					}
				}
				else
				{
					ensure(pHeader);
				}
			}
		}

		if (SIFApplication::GetApplication()->GetCareerModeManager() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro())
		{
			GameMode = FString("BeAPro");
		}

		// breadcrumb text defaults to single match
		
		//Assign Components
		controllerAssignmentWidget = Cast<UWWUIControllerAssignmentWidget>(FindChildWidget(WWUIScreenAssignControllers_UI::BP_ControllerAssignment));
		StadiumNameText = FindChildWidget(WWUIScreenAssignControllers_UI::StadiumName);
		DifficultyText = FindChildWidget(WWUIScreenAssignControllers_UI::DiffultyText);
		WeatherText = FindChildWidget(WWUIScreenAssignControllers_UI::Weather);
		RulesetText = FindChildWidget(WWUIScreenAssignControllers_UI::Ruleset);
		LeftTeamName = FindChildWidget(WWUIScreenAssignControllers_UI::Team1Name);
		RightTeamName = FindChildWidget(WWUIScreenAssignControllers_UI::Team2Name);

		//All of these should exist
		MABASSERT(controllerAssignmentWidget);
		MABASSERT(StadiumNameText);
		MABASSERT(DifficultyText);
		MABASSERT(WeatherText);
		MABASSERT(RulesetText);
		MABASSERT(LeftTeamName);
		MABASSERT(RightTeamName);

		if (bFromInGame)
		{
			StadiumNameText->SetVisibility(ESlateVisibility::Collapsed);
			DifficultyText->SetVisibility(ESlateVisibility::Collapsed);
			WeatherText->SetVisibility(ESlateVisibility::Collapsed);
			RulesetText->SetVisibility(ESlateVisibility::Collapsed);

			UWidget* widget = nullptr;
			widget = FindChildWidget(WWUIScreenAssignControllers_UI::MatchSettings);
			if (widget) { widget->SetVisibility(ESlateVisibility::Collapsed); }
			widget = FindChildWidget(WWUIScreenAssignControllers_UI::Subheading_Strip);
			if (widget)	{	widget->SetVisibility(ESlateVisibility::Collapsed);	}
			widget = FindChildOfTemplateWidget(controllerAssignmentWidget, WWUIScreenAssignControllers_UI::P1Kit);
			if (widget)	{	widget->SetVisibility(ESlateVisibility::Collapsed);	}
			widget = FindChildOfTemplateWidget(controllerAssignmentWidget, WWUIScreenAssignControllers_UI::P2Kit);
			if (widget)	{	widget->SetVisibility(ESlateVisibility::Collapsed);	}
			widget = FindChildWidget(WWUIScreenAssignControllers_UI::SettingsLineTop);
			if (widget) { widget->SetVisibility(ESlateVisibility::Collapsed); }
			widget = FindChildWidget(WWUIScreenAssignControllers_UI::SettingsLineBottom);
			if (widget) { widget->SetVisibility(ESlateVisibility::Collapsed); }
		}
		else
		{
			//Update strip names
			UpdateTeamStripText(0);
			UpdateTeamStripText(1);
		}

		//Enable drop in 
		if (GameMode.Compare("BeAPro") != 0)
		{
			APlayerController* firstPlayer = GetWorld()->GetGameInstance<URugbyGameInstance>()->GetMasterPlayerController();
			if (UOBJ_IS_VALID(firstPlayer) && UOBJ_IS_VALID(firstPlayer->GetLocalPlayer()))
			{
				if (URugbyGameViewportClient* rugbyViewport = Cast<URugbyGameViewportClient>(firstPlayer->GetLocalPlayer()->ViewportClient))
				{
					this->SetDropInAllowed(true);
					rugbyViewport->EnableDropIn();
					rugbyViewport->SetDropInKey(EKeys::Gamepad_Special_Right); // Legend text is set to Special right
					gameInstance->GetDropInDelegate()->AddDynamic(this, &UWWUIScreenControllerAssignment::PlayerDropInOut);
				}
			}
		}
		else
		{
			APlayerController* firstPlayer = GetWorld()->GetGameInstance<URugbyGameInstance>()->GetMasterPlayerController();
			if (UOBJ_IS_VALID(firstPlayer) && UOBJ_IS_VALID(firstPlayer->GetLocalPlayer()))
			{
				if (URugbyGameViewportClient* rugbyViewport = Cast<URugbyGameViewportClient>(firstPlayer->GetLocalPlayer()->ViewportClient))
				{
					this->SetDropInAllowed(false);
					rugbyViewport->DisableDropIn();
					//rugbyViewport->SetDropInKey(EKeys::Gamepad_Special_Right); // Legend text is set to Special right
					//gameInstance->GetDropInDelegate()->AddDynamic(this, &UWWUIScreenControllerAssignment::PlayerDropInOut);
				}
			}
		}

		//If pro mode, show goals and progress
		if(!bFromInGame)
		{
			if (RUCareerModeManager* careerModeManager = gameInstance->GetCareerModeManager())
			{
				if (UWWUIScrollBox* goals = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenAssignControllers_UI::GoalsList)))
				{
					// Show the goals list
					if (careerModeManager->GetIsCareerModePro())
					{
						goals->SetVisibility(ESlateVisibility::SelfHitTestInvisible);											   
						goals->PopulateAndRefresh();
					}
					else
					{
						goals->SetVisibility(ESlateVisibility::Collapsed);
					}
				}
				else
				{
					ensure(goals);
				}
			}
		}

		bIsPlayGo = false;
		SIFGameWorld* pGameWorld = gameInstance->GetActiveGameWorld();
		if (pGameWorld)
		{
			bIsPlayGo = pGameWorld->GetIsPlayGo();
		}
	}

	bTeamsNeedResetting = true;

	ugcRestrictionHandle = SIFApplication::GetApplication()->OnUGCRestrictionChanged.AddUObject(this, &UWWUIScreenControllerAssignment::HandleUGCRestrictionChange);

#ifdef UI_USING_UMG
#endif
}

void UWWUIScreenControllerAssignment::PlayerDropInOut(bool IsDropIn)
{
	//Don't allow changing controllers in pro mode.
	if (GameMode.Compare("BeAPro") == 0)
	{
		return;
	}

	DuplicateInputComponent();

	if (URugbyGameInstance* gameInstance = GetWorld()->GetGameInstance<URugbyGameInstance>())
	{
		TArray<ARugbyPlayerController*> tempControllers = gameInstance->GetLocalPlayerControllers();
		TArray<ARugbyPlayerController*> combinedControllers;
		combinedControllers.Append(localPlayers);
		combinedControllers.Append(tempControllers);

		for (ARugbyPlayerController* playerController : combinedControllers)
		{
			//Add player if not in the local players list
			if (!localPlayers.Contains(playerController))
			{
				SIFUIHelpers::ListenToController(playerController->GetControllerIndex(), true);
				controllerAssignmentWidget->AddController(playerController->GetConnectionIndex());
				OldListeningControllerIds.Add(playerController->GetControllerIndex());
				UE_LOG(LogTemp, Warning, TEXT("Adding %s. Controller index: %f,  Connection Index: %d"), *playerController->GetName(), playerController->GetControllerIndex(), playerController->GetConnectionIndex());

			}
			//Remove player if not in the temp list
			if (!tempControllers.Contains(playerController))
			{
				SIFUIHelpers::ListenToController(playerController->GetControllerIndex(), false);
				TArray<int> activeControllers = controllerAssignmentWidget->GetActiveControllers();
				for (int i = 0; i < tempControllers.Num(); i++)
				{
					activeControllers.Remove(tempControllers[i]->GetConnectionIndex());
				}
				for (int removedController : activeControllers)
				{
					controllerAssignmentWidget->RemoveController(removedController);				
				}
				SIFApplication::GetApplication()->AttemptDropOutController(playerController->GetControllerIndex());
				OldListeningControllerIds.Remove(playerController->GetControllerIndex());
			}
		}
		localPlayers = tempControllers;
	}

	UpdateHelpText();
}

void UWWUIScreenControllerAssignment::NavigateBack(APlayerController* playerController)
{
	//If player has selected a side, deselect it
	ARugbyPlayerController * rugbyPlayerController = Cast<ARugbyPlayerController>(playerController);
	int connectionIndex = rugbyPlayerController->GetConnectionIndex();
	if (rugbyPlayerController && controllerAssignmentWidget->IsControllerLocked(connectionIndex))
	{
		controllerAssignmentWidget->UnlockController(connectionIndex);
	}
	else //else go back a screen
	{
		if (bForceSelection)
		{
			EnterGame();
		}
		else if (UWorld* world = GetWorld())
		{
			if (!bFromInGame)
			{
#if PLATFORM_SWITCH
				SIFApplication::GetApplication()->SignOutSplitScreenPlayers();
#endif
				SIFApplication::GetApplication()->RemoveSplitScreenPlayers();
			}

			if (URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(world->GetGameInstance()))
			{
				gameInstance->DealMenuAction(SCREEN_BACK_FADE, "");
			}
		}
	}
}


//===============================================================================
//===============================================================================
void UWWUIScreenControllerAssignment::UpdateMatchSettings()
{
	URugbyGameInstance* gameInstance = GetWorld()->GetGameInstanceChecked<URugbyGameInstance>();
	if (gameInstance)
	{
		RUGameSettings* settings = gameInstance->GetMatchGameSettings();
		if (settings)
		{
			if (bIsPlayGo)
			{
				//Hard set the stadium to Eden Park
				// GGS Nick Crash Why is this done?!?! Commnet out for now
				// SIFGameHelpers::GASetStadium(DB_STADIUMID_EDEN);
			}

			//Stadium name
			FString stadiumNameString;
			stadiumNameString = FString(SIFGameHelpers::GAGetStadiumName(SIFGameHelpers::GAGetStadium()).c_str());

			SetWidgetText(StadiumNameText, FText::FromString(stadiumNameString).ToUpper());

			//Difficulty settings
			FString difficultyString = SIFGameHelpers::GAGetDifficultyName();
			SetWidgetText(DifficultyText, FText::FromString(UWWUITranslationManager::Translate("[ID_MATCH_DIFFICULTY_" + difficultyString + "]") + ", " + UWWUITranslationManager::Translate("[ID_GAME_LENGTH_" + FString::FromInt(SIFGameHelpers::GAGetGameLength()) + "_MINUTES]")).ToUpper());

			//Weather settings
			FString weatherString;
			weatherString.Append(UWWUITranslationManager::Translate("[ID_TIMEOFDAY_" + FString(SIFGameHelpers::GAGetTimeOfDay()) + "]").ToUpper());
			weatherString.Append(", ");
			weatherString.Append(UWWUITranslationManager::Translate("[ID_MATCH_SETTINGS_CONDITIONS_" + FString(SIFGameHelpers::GAGetConditions()) + "]").ToUpper());
				
			SetWidgetText(WeatherText, FText::FromString(weatherString));

			//Law settings
			FText LawSettingsText = FText::FromString(UWWUITranslationManager::Translate("[ID_LAW_VARIATION_" + FString::FromInt(SIFGameHelpers::GAGetGameLaw()) + "]")).ToUpper();
			SetWidgetText(RulesetText, LawSettingsText);
		}

		UpdateTeamNames();
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenControllerAssignment::UpdateTeamNames()
{
	if (SIFGameHelpers::GAGetTeamIsCustom(SIFGameHelpers::GAGetTeam(0)) && ((bIsSomeAccountRestricted && SIFGameHelpers::GAGetTeamDownloadUser(SIFGameHelpers::GAGetTeam(0)) != "") || bIsNonPrimaryRestricted))
	{
		SetWidgetText(LeftTeamName, FText::FromString(UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper()));
	}
	else
	{
		FString team1name = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetTeamNameIncludingThe(SIFGameHelpers::GAGetTeam(0)).c_str());
		SetWidgetText(LeftTeamName, FText::FromString(team1name));
	}

	if (SIFGameHelpers::GAGetTeamIsCustom(SIFGameHelpers::GAGetTeam(1)) && ((bIsSomeAccountRestricted && SIFGameHelpers::GAGetTeamDownloadUser(SIFGameHelpers::GAGetTeam(1)) != "") || bIsNonPrimaryRestricted))
	{
		SetWidgetText(RightTeamName, FText::FromString(UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper()));
	}
	else
	{
		FString team2name = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetTeamNameIncludingThe(SIFGameHelpers::GAGetTeam(1)).c_str());
		SetWidgetText(RightTeamName, FText::FromString(team2name));
	}
}

void UWWUIScreenControllerAssignment::Update(float DeltaTime)
{

}

//Registers screen input functions
#ifdef UI_USING_UMG
void UWWUIScreenControllerAssignment::RegisterFunctions()
{
	bool careerMode = false;
	bool proMode = false;
	RUCareerModeManager* careerManager;
	if (GetWorld() && GetWorld()->GetGameInstance<URugbyGameInstance>())
	{
		careerManager = GetWorld()->GetGameInstance<URugbyGameInstance>()->GetCareerModeManager();
		MABASSERT(careerManager);
		if (careerManager)
		{
			careerMode = careerManager->IsActive();
			proMode = careerManager->GetIsCareerModePro();
		}
	}

	AddInputAction(FString("UI_Right"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenControllerAssignment::ControllerMovingRight), true);
	AddInputAction(FString("UI_Left"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenControllerAssignment::ControllerMovingLeft), true);
	AddInputAction(FString("RU_UI_ACTION_ACCEPT"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenControllerAssignment::SelectSide));
	AddInputAction(FString("RU_UI_ACTION_RETURN"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenControllerAssignment::NavigateBack));

	if (!proMode)
	{
		AddInputAction(FString("RU_UI_ACTION_DROPINOUT"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenControllerAssignment::DropOutController));
	}

	if(!bFromInGame)
	{
		if(!bIsPlayGo && !careerMode)
		{
			AddInputAction(FString("RU_UI_ACTION_MATCH_SETTINGS"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenControllerAssignment::OpenMatchSettings));
		}
		AddInputAction(FString("RU_UI_ACTION_CHANGE_STRIP"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenControllerAssignment::OnChangeStripClicked));
	}
}
#endif

void UWWUIScreenControllerAssignment::OnInFocus()
{
	Super::OnInFocus();
	FSlateApplication::Get().SetAllUserFocusToGameViewport();
	SetFocusForControllers();	//Added for maintaining focus after coming back from match settings screen
	UpdateMatchSettings();

	//Reset default teams if needed, only if not in game
	if (bTeamsNeedResetting && !bFromInGame)
	{
		if (controllerAssignmentWidget)
		{
			// Nick WWS this was causing a bug in Be A Pro, setting the inital controller Left before the teams are set on re-entry, seems ok without it :/
			// controllerAssignmentWidget->ResetTeams();
		}

		//	Mattt H - This is to update the text after it updates the strips through ResetTeams().
		UpdateTeamStripText(0);
		UpdateTeamStripText(1);
	}

	//Get player controllers list
	localPlayers = SIFApplication::GetApplication()->GetLocalPlayerControllers();

	if (bFromInGame)
	{
		//This screen uses all the controllers, but when we go back we only want to be listening to the controller
		// we were listening to before.
		OldListeningControllerIds.Empty();
		for(int i = 0; i < localPlayers.Num(); i++)
		{
			int controllerIndex = localPlayers[i]->GetControllerIndex();
			if (SIFUIHelpers::ListeningToController(controllerIndex) == true)
			{
				OldListeningControllerIds.Add(controllerIndex);
			}
		}
	}

	SIFUIHelpers::ListenToAllControllers();

	if (USIFMissingControllerListener * mcl = SIFApplication::GetApplication()->GetMissingControllerListener())
		mcl->ForceSendNotification(true);

	

	TMap<int, ControllerAssignmentState> PlayerTeam;

	PlayerTeam = GetDefaultTeam();

	//Initialise the controller select widget
	if (controllerAssignmentWidget && bTeamsNeedResetting)
	{
		controllerAssignmentWidget->Init(PlayerTeam, SIFGameHelpers::GAGetIsAProMode());
	}

	UpdateHelpText();

	SetUpProMode();
}

void UWWUIScreenControllerAssignment::SetUpProMode()
{
	UWidget* progress_node = FindChildWidget(WWUIScreenAssignControllers_UI::ProgressList);
	UWidget* pro_goals_node = FindChildWidget(WWUIScreenAssignControllers_UI::GoalsList);
	UWidget* pro_goals_text = FindChildWidget(WWUIScreenAssignControllers_UI::ProgressText);
	UWidget* progress_text = FindChildWidget(WWUIScreenAssignControllers_UI::GoalsText);
	RUCareerModeManager* careerManager;

	if (GetWorld() && GetWorld()->GetGameInstance<URugbyGameInstance>())
	{

		careerManager = GetWorld()->GetGameInstance<URugbyGameInstance>()->GetCareerModeManager();
		MABASSERT(careerManager);
		if (careerManager && SIFGameHelpers::GAGetIsAProMode() && !bFromInGame)
		{
			if (progress_node && pro_goals_node && pro_goals_text && progress_text)
			{
				progress_node->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
				pro_goals_node->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
				pro_goals_text->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
				progress_text->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
			}

			//We need to change this up dep}ing on if the current match is a rep or club match....
			float capCalc = careerManager->GetProProgressCalculatedClubCP();
			float gkCalc = careerManager->GetProProgressCalculatedClubGK();
			float pkCalc = careerManager->GetProProgressCalculatedClubPK();

			float capTarget = careerManager->GetProProgressTargetClubCP();
			float gkTarget = careerManager->GetProProgressTargetClubGK();
			float pkTarget = careerManager->GetProProgressTargetClubPK();

			float capScaled = (capCalc / capTarget);
			float gkScaled = (gkCalc / gkTarget);
			float pkScaled = (pkCalc / pkTarget);

			UWidget* Captain = FindChildWidget(WWUIScreenAssignControllers_UI::Captain);
			UWidget* GoalKicker = FindChildWidget(WWUIScreenAssignControllers_UI::GoalKicker);
			UWidget* PlayKicker = FindChildWidget(WWUIScreenAssignControllers_UI::PlayKicker);

			UProgressBar* captain_indicator = Cast<UProgressBar>(FindChildOfTemplateWidget(Captain, WWUIScreenAssignControllers_UI::ProProgressBar));
			UProgressBar* gk_indicator = Cast<UProgressBar>(FindChildOfTemplateWidget(GoalKicker, WWUIScreenAssignControllers_UI::ProProgressBar));
			UProgressBar* pk_indicator = Cast<UProgressBar>(FindChildOfTemplateWidget(PlayKicker, WWUIScreenAssignControllers_UI::ProProgressBar));

			UTextBlock* captain_text = Cast<UTextBlock>(FindChildOfTemplateWidget(Captain, TITLENAME));
			UTextBlock* gk_text = Cast<UTextBlock>(FindChildOfTemplateWidget(GoalKicker, TITLENAME));
			UTextBlock* pk_text = Cast<UTextBlock>(FindChildOfTemplateWidget(PlayKicker, TITLENAME));

			if (captain_text && gk_text && pk_text)
			{
				SetWidgetText(captain_text, FText::FromString(UWWUITranslationManager::Translate("[ID_TEAM_CAPTAIN]")));
				SetWidgetText(gk_text, FText::FromString(UWWUITranslationManager::Translate("[ID_TEAM_GOAL_KICKER]")));
				SetWidgetText(pk_text, FText::FromString(UWWUITranslationManager::Translate("[ID_TEAM_PLAY_KICKER]")));
			}

			float w = capScaled;
			if (w > 1)
			{
				w = 1;
			}
			if (captain_indicator)
			{
				captain_indicator->SetPercent(w);
			}

			w = gkScaled;
			if (w > 1)
			{
				w = 1;
			}
			if (gk_indicator)
			{
				gk_indicator->SetPercent(w);
			}

			w = pkScaled;
			if (w > 1)
			{
				w = 1;
			}
			if (pk_indicator)
			{
				pk_indicator->SetPercent(w);
			}

			UWWUITabSwitcher * heading = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenAssignControllers_UI::BP_HeaderHalfBottom));
			if (heading)
			{
				heading->SetTabText(0, FText::FromString(UWWUITranslationManager::Translate("[ID_DETAILS]")));
			}
		}
		else
		{
			if (progress_node && pro_goals_node && pro_goals_text && progress_text)
			{
				progress_node->SetVisibility(ESlateVisibility::Collapsed);
				pro_goals_node->SetVisibility(ESlateVisibility::Collapsed);
				pro_goals_text->SetVisibility(ESlateVisibility::Collapsed);
				progress_text->SetVisibility(ESlateVisibility::Collapsed);
			}

			UWWUITabSwitcher * heading = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenAssignControllers_UI::BP_HeaderHalfBottom));
			if (heading)
			{
				heading->SetTabText(0, FText::FromString(UWWUITranslationManager::Translate("[ID_ASSIGN_CONTROLLERS_TITLE]")));
			}
		}

		// Passes in false, because the controller assignment screen operates differently to the career hub.
		// The first team is always the home team, not the player controlled team.
		SIFGameHelpers::GASetTeamStripsToGameSettings(false);
	}
}

void UWWUIScreenControllerAssignment::OnOutFocus(bool ShouldOutFocus)
{
	StoreFocusForControllers();
	Super::OnOutFocus(ShouldOutFocus);
}

void UWWUIScreenControllerAssignment::ControllerMovingRight(APlayerController* playerController)
{
	if (ARugbyPlayerController * rugbyPlayerController = Cast<ARugbyPlayerController>(playerController))
	{
		int connectionIndex = rugbyPlayerController->GetConnectionIndex();
		if (connectionIndex != -1)
		{
			// #NeedImplementation maybe.
			//SetObjectParameter(controller_label, false)

			controllerAssignmentWidget->MovePlayerRight(connectionIndex);
			UpdateHelpText();
		}
	}
}

void UWWUIScreenControllerAssignment::OpenMatchSettings(APlayerController* playerController)
{
	if (GetWorld() && GetWorld()->GetGameInstance())
	{
		UWorld* world = GetWorld();
		URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(world->GetGameInstance());
		gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::MatchSettings);
		bTeamsNeedResetting = false;
	}
}

void UWWUIScreenControllerAssignment::ControllerMovingLeft(APlayerController* playerController)
{
	if (ARugbyPlayerController * rugbyPlayerController = Cast<ARugbyPlayerController>(playerController))
	{
		int connectionIndex = rugbyPlayerController->GetConnectionIndex();
		if (connectionIndex != -1)
		{
			UE_LOG(LogTemp, Warning, TEXT("Controller moving left is %d"), connectionIndex);
			// #NeedImplementation maybe.
			//SetObjectParameter(controller_label, true)

			controllerAssignmentWidget->MovePlayerLeft(connectionIndex);
			UpdateHelpText();
		}
	}
}

void UWWUIScreenControllerAssignment::SelectSide(APlayerController* playerController)
{
	//Check if all controllers not in the middle has confirmed, if not, just set the controller to confirmed
	// Add a check when joining multiplayer dialog is showing
#if PLATFORM_XBOXONE
	if (SIFMatchmakingHelpers::GetInvitePending())
	{
		return;
	}
#endif

	ARugbyPlayerController * rugbyPlayerController = Cast < ARugbyPlayerController>(playerController);

	int connectionIndex = rugbyPlayerController->GetConnectionIndex();
	if (controllerAssignmentWidget)
	{
		int team_index = controllerAssignmentWidget->GetPlayerSide(connectionIndex);

		if (SIFGameHelpers::GAGetIsAProMode() && team_index == -1)
		{
			return;
		}

		if (!controllerAssignmentWidget->IsControllerLocked(connectionIndex) && team_index != -1)
		{
			controllerAssignmentWidget->LockController(connectionIndex);
			//Commenting this out so the game goes once the controllers are confirmed, as in RC3
//return;
		}

		int unconfirmed_neutral_controllers = 0;
		bool all_confirmed = true;
		bool unconfirmed_neutral = false;
		bool unconfirmed_not_neutral = false;
		for (int i = 0; i < 8; i++)
		{
			bool controller_ready = true; //SIFUIHelpers::GetIsControllerReady(i); //#rc3_legacy_input
			int teamindex = controllerAssignmentWidget->GetPlayerSide(i);
			bool confirmed = controllerAssignmentWidget->IsControllerLocked(i);

			if (!confirmed && controller_ready)
			{
				all_confirmed = false;
				if (teamindex == -1)
				{
					unconfirmed_neutral = true;
					unconfirmed_neutral_controllers = unconfirmed_neutral_controllers + 1;
				}
				else
				{
					unconfirmed_not_neutral = true;
				}
			}
		}


		if (!all_confirmed && !SIFGameHelpers::GAGetIsAProMode())
		{
			// If there's any controller in the middle with team_index == -1, show a popup asking if it's intended.If there's no 
			if (unconfirmed_neutral && !unconfirmed_not_neutral)
			{
				// raise popup - means there's some that haven't selected but all not neutral controllers have confirmed
				//if (unconfirmed_neutral_controllers == 1)
				//{
				//	//UILaunchPopUpOverride("ControllerSelectionNeutral", "[ID_ASSIGN_CONTROLLER_NEUTRAL_SINGULAR_BODY]");   #rc3_legacy_popup
				//}
				//else
				//{
				//	//UILaunchPopUpOverride("ControllerSelectionNeutral", "" ..unconfirmed_neutral_controllers .. " [ID_ASSIGN_CONTROLLER_NEUTRAL_PLURAL_BODY]");
				//}

				UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

				if (unconfirmed_neutral_controllers == 1)
				{
					modalData->WarningDialogue = FString("[ID_ASSIGN_CONTROLLER_NEUTRAL_SINGULAR_BODY]");
				}
				else
				{
					modalData->WarningDialogue = FString(FString::FromInt(unconfirmed_neutral_controllers) + " [ID_ASSIGN_CONTROLLER_NEUTRAL_PLURAL_BODY]");
				}
				modalData->LegendString = FString("[ID_ASSIGN_CONTROLLER_NEUTRAL_HELP]");

				TArray<FModalButtonInfo> ButtonData;
				//continue button
				FWWUIModalDelegate EnterGameDelegate;
				EnterGameDelegate.BindUObject(this, &UWWUIScreenControllerAssignment::EnterGameCalledByDelegate);
				ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_POPUP_BUTTON_TEXT_CONTINUE]"), EnterGameDelegate));

				//cancel button
				//DoNotSaveDelegate.BindUObject(this, &UWWUIScreenCareerHUB::WarningMessageTest2);
				//should simply close on select
				ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_CUSTOMISE_PLAYER_BUTTON_CANCEL]"), FWWUIModalDelegate()));

				modalData->ButtonData = ButtonData;

				modalData->CloseOnBackButton = true;
				modalData->CloseOnSelectButton = true;
				modalData->AssignGoBackToButtonsWithUnboundDelegates = true;

				UWorld* pWorld = GetWorld();
				if (pWorld)
				{
					URugbyGameInstance* pRugbyGameInstance = Cast<URugbyGameInstance>(pWorld->GetGameInstance());
					if (pRugbyGameInstance)
					{
						pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
					}
				}

			}
		}
		else
		{
			EnterGame();
		}
	}
}

void UWWUIScreenControllerAssignment::DropOutController(APlayerController* playerController)
{
	if (URugbyGameInstance* gm = SIFApplication::GetApplication())
	{
		if (URugbyGameViewportClient* viewportClient = Cast<URugbyGameViewportClient>(gm->GetGameViewportClient()))
		{
			if (!viewportClient->GetDropOutAllowed())
			{
				SIFUIHelpers::LaunchWarningPopup(UWWUITranslationManager::Translate(FString("[ID_CONTROLLER_CONNECTED_POPUP]")), UWWUITranslationManager::TranslateWithFont(FString("[ID_POPUP_ACKNOWLEDGE]")), nullptr);
			}
		}
	}
}

bool UWWUIScreenControllerAssignment::EnterGameCalledByDelegate(APlayerController *)
{
	EnterGame();
	return true;
}

void UWWUIScreenControllerAssignment::EnterGame()
{
	//Firstly, check if we should just simulate the match without hitting game
	/*if (UIIsUnlocked("CONF_HUB_FAKE_GAME") && GameMode ~= "quick_match" )
	{
		SIFUIHelpers::SetCurrentWindow("CompetitionFakeGame");
		return;
	}*/

	//Updated the commentary language to ensure it's in English for French Womens
	RUCommentary* commentary = SIFApplication::GetApplication()->GetCommentarySystem();
	if (commentary)
	{
		commentary->ForceCommentaryReload();
	}
	
	//Assign teams
	for (int i = 0; i < localPlayers.Num(); i++)
	{
		int id = localPlayers[i]->GetPlayerIndex();
		int connectionIndex = localPlayers[i]->GetConnectionIndex();
		int controllerIndex = localPlayers[i]->GetControllerIndex();
		int playerSide = controllerAssignmentWidget->GetPlayerSide(connectionIndex);

		if (bFromInGame)
		{		
			SIFInGameHelpers::SetRunningPlayerTeam(EHumanPlayerSlot(connectionIndex), id, playerSide);
			SIFInGameHelpers::SetPlayerTeam(EHumanPlayerSlot(connectionIndex), id,controllerIndex, playerSide);
			if (playerSide != -1)
			{
				OldListeningControllerIds.Add(controllerIndex);
				SIFPlayerHelpers::PMSetPlayerHuman(connectionIndex, controllerIndex);
				SIFApplication::GetApplication()->GetMissingControllerListener()->SetControllerRelevant(localPlayers[i]->GetControllerIndex(), true);
			}
			else
			{
				OldListeningControllerIds.Remove(controllerIndex);
				SIFApplication::GetApplication()->GetMissingControllerListener()->SetControllerRelevant(localPlayers[i]->GetControllerIndex(), false);
			}

			SIFAudioHelpers::PauseAndMuteInGameSFX(false);

			//Fix lineout icons
			if (SIFGameWorld * game_world = SIFApplication::GetApplication()->GetActiveGameWorld())
			{
				RUGamePhaseLineOut* lineoutPhase = nullptr;
				if (game_world->GetGameState()->GetPhase() == RUGamePhase::LINEOUT)
				{
					lineoutPhase = game_world->GetGameState()->GetPhaseHandler<RUGamePhaseLineOut>();
				}

				if (lineoutPhase != nullptr && lineoutPhase->GetSetplayState() == RUGamePhaseLineOut::SP_STATE_WAIT_THROW)
				{
					RUGameEvents* game_events = game_world->GetEvents();
					if (game_events)
					{
						game_events->lineout_refresh(game_world->GetBall()->GetHolder());
					}

					/*switch (lineoutPhase->GetSetplayState())
					{
					case RUGamePhaseLineOut::SP_STATE_WAIT_THROW:
					{
						RUGameEvents* game_events = game_world->GetEvents();
						if (game_events)
						{
							game_events->lineout_refresh(game_world->GetBall()->GetHolder());
						}
					}
					break;
					case RUGamePhaseLineOut::SP_STATE_INIT:
					case RUGamePhaseLineOut::SP_STATE_WAIT_FOR_ROLE:
					case RUGamePhaseLineOut::SP_STATE_WAIT_FOR_PLAYERS:
					case RUGamePhaseLineOut::SP_STATE_RUNNING:
					case RUGamePhaseLineOut::SP_STATE_WAIT_RELEASE:
					case RUGamePhaseLineOut::SP_STATE_FINISHED:
					default:
					{
						UE_LOG(LogTemp, Warning, TEXT("Not in a needed lineout phase"));
					}
					break;
					}*/
					
				}
			}
		}
		else
		{
			SIFInGameHelpers::SetPlayerTeam(EHumanPlayerSlot(connectionIndex), id,controllerIndex, playerSide);
			if (playerSide != -1)
			{
				SIFPlayerHelpers::PMSetPlayerHuman(connectionIndex, controllerIndex);
				SIFApplication::GetApplication()->GetMissingControllerListener()->SetControllerRelevant(localPlayers[i]->GetControllerIndex(), true);
			}
			else
			{
				SIFApplication::GetApplication()->GetMissingControllerListener()->SetControllerRelevant(localPlayers[i]->GetControllerIndex(), false);
			}
		}
	}

	//if (SIFGameHelpers::GAIsPlatformPC())
	//{
	//	// Always enable master controller even if they are not playing
	//	SIFPlayerHelpers::PMSetPlayerHuman(0, 0);
	//}

	//ProceedToNextWindow(ui_object, parameters)

	SIFUIHelpers::SetCurrentBackground("");
	//GAEnterGame("pong");

	//SIFUIHelpers::FlushMappedInputs(parameters.controller_id, "ACTION_TYPE_ACTION");

	bTeamsNeedResetting = true;
	//EntryFromTutorialPauseMenu = false;

	//sync certain player profile settings to the game settings structure.
	SIFGameHelpers::GASyncProfileSettingsToGameSettings();

	// Request r}er of team faces after all selection has been completed.
	SIFGameHelpers::GASetTeamFaceGenerationEnabled(false);		//Pause now, re - enabled after async game loading has completed.

	//Always mark controller 0 relevant
	SIFApplication::GetApplication()->GetMissingControllerListener()->SetControllerRelevant(0, true);

	for (SSHumanPlayer * human : SIFApplication::GetApplication()->GetActiveGameWorld()->GetHumanPlayers())
	{
		if (human->IsPlaying())
		{
			if (ARugbyCharacter * selected_character = human->GetRugbyCharacter())
			{
				if (human->GetTeam() != selected_character->GetAttributes()->GetTeam())
				{
					human->AssignBestPlayer();
				}
			}
			else
			{
				human->AssignBestPlayer();
			}
		}
	}

	if (bPreOnlineMode)
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		if (pRugbyGameInstance)
		{
			pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::OnlineSearchResults, pSearchInData);
		}

		return;
	}
	else if (bFromInGame || bOnlineMode)
	{
		if (UWorld* world = GetWorld())
		{
			if (URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(world->GetGameInstance()))
			{
				gameInstance->DealMenuAction(SCREEN_BACK_FADE, Screens_UI::SelectTeams);
			}
		}
		return;
	}

	if (GetWorld() && GetWorld()->GetGameInstance<URugbyGameInstance>())
	{
		if (UWWUITransitionManager * TransitionManager = GetWorld()->GetGameInstance<URugbyGameInstance>()->GetTransitionManager())
		{
			TransitionManager->RequestTransitionStart(0.5f, FWWUIOnTransitionStartComplete::CreateUObject(this, &UWWUIScreenControllerAssignment::EnterGameCallback));
			SetInputEnabled(false);

#ifdef ENABLE_ANALYTICS
			RUCareerModeManager* careerManager;
			if (GetWorld() && GetWorld()->GetGameInstance<URugbyGameInstance>())
			{
				careerManager = GetWorld()->GetGameInstance<URugbyGameInstance>()->GetCareerModeManager();
				MABASSERT(careerManager);
				if (careerManager && careerManager->IsActive())
				{
					if(careerManager->IsInFranchise())
					{
						careerManager->RegisterCareerAdvanceRoundAnalytics("");
					}
					else
					{
						careerManager->RegisterCompAdvanceRoundAnalytics("");
					}
				}
			}
#endif
		}
	}
}

void UWWUIScreenControllerAssignment::EnterGameCallback()
{
	UWorld* world = GetWorld();
	if (UOBJ_IS_VALID(world))
	{
		ARugbyGameModeBase * rugbyGameMode = Cast<ARugbyGameModeBase>(GetWorld()->GetAuthGameMode());
		if (UOBJ_IS_VALID(rugbyGameMode))
		{
			rugbyGameMode->TryLoadMatch();
		}
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenControllerAssignment::SaveSelections()
{

}

//System event function
bool UWWUIScreenControllerAssignment::OnSystemEvent(WWUINodeProperty& eventProperty)
{
	//#NeedImplementation does this function ever get called at the moment?

	FString EventName = eventProperty.GetStringProperty("system_event");

	if (EventName == "oncontrollerstatechanged")
	{
		int missingController = eventProperty.GetIntProperty("missing_controller");

		// We only want to notify for controller disconnections from the master controller.
		if (missingController == SIFUIHelpers::GetCurrentMasterControllerIndex())
		{
			SIFApplication::GetApplication()->GetMissingControllerListener()->CommonSystemEvent(eventProperty);
		}
		return true;
	}
	else if (EventName == "team_strips_changed")
	{
		UpdateTeamStripText(controllerAssignmentWidget->GetPlayerSide(0));
		UpdateTeamStripText(controllerAssignmentWidget->GetPlayerSide(1));
		return true;
	}
	else if (EventName == "connect_new_controller")
	{
		//This event is now only fired to remove controllers
		bool isConnected = eventProperty.GetBoolProperty("is_connected");
		int missingController = eventProperty.GetIntProperty("missing_controller");

		
		/*if (!isConnected)
		{
			TArray<FControllerDisconnectionData> disconnectedControllers = SIFApplication::GetApplication()->GetControllerDisconnectionData();
			ULocalPlayer * player = nullptr;

			int connectionIdx = -1;
			for (FControllerDisconnectionData data : disconnectedControllers)
			{
				if (data.PreDisconnectControllerId == missingController)
				{
					if (data.LocalPlayer.IsValid())
					{
						player = data.LocalPlayer.Get();
					}
				}
			}

			if (player)
			{
				ARugbyPlayerController * pc = Cast<ARugbyPlayerController>(player->GetPlayerController(GetWorld()));
				if (pc)
				{
					connectionIdx = pc->GetConnectionIndex();
				}
			}


			controllerAssignmentWidget->RemoveController(connectionIdx);
		}*/
		return true;
	}
	return false;
}

void UWWUIScreenControllerAssignment::OnChangeStripClicked(APlayerController* playerController)
{
	//We need to cycle around the strip of the current team.
	//Which team is the master controller currently set to ?
	if (GetWorld() && GetWorld()->GetGameInstance())
	{
		for (int i = 0; i < localPlayers.Num(); i++)
		{
			if (localPlayers[i] == playerController) //GetWorld()->GetGameInstance()->GetPrimaryPlayerController())
			{
				int team_index = controllerAssignmentWidget->GetPlayerSide(i);
				if (SIFGameHelpers::GAAreAllTeamStripsTheSame(team_index))
				{
					//The team is not focused on a team or not having more than one strip
					return;
				}
				CycleTeamStrip(team_index);
			}
		}
	}
}

void UWWUIScreenControllerAssignment::CycleTeamStrip(int team_index)
{
	//What strip is currently set ?
	int strip_index = SIFGameHelpers::GAGetCurrentSelectedStrip(team_index);

	//Update strip.
	strip_index = strip_index + 1;
	if (strip_index >= 2)
	{
		strip_index = 0;
	}

	SIFGameHelpers::GASetCurrentSelectedStrip(team_index, strip_index);
	
	SIFUIHelpers::MenuSoundStripChange();

	//The strip titles also need to be updated.
	UpdateTeamStripText(team_index);
}

void UWWUIScreenControllerAssignment::UpdateTeamStripText(int team_index)
{
	int strip_index = SIFGameHelpers::GAGetCurrentSelectedStrip(team_index);

	FString strip_text = "[ID_HOME_STRIP]";
	//Some teams only have one strip, so their secondary strip is the same at their primary.
	//The text, however, should still say the home option.
	if (strip_index == 1 && !SIFGameHelpers::GAAreAllTeamStripsTheSame(team_index))
	{
		if (SIFGameHelpers::GAGetTeam(team_index) == 1044)    //NORTH HARBOUR, HES REQUEST
		{
			strip_text = "[ID_THIRTY_ANNIVERSARY]";
		}
		else
		{
			strip_text = "[ID_AWAY_STRIP]";
		}
	}

	UWidget* stripTextWidget = nullptr;
	if (team_index == 0)
	{
		stripTextWidget = FindChildOfTemplateWidget(controllerAssignmentWidget, WWUIScreenAssignControllers_UI::P1Kit);
	}
	else
	{
		stripTextWidget = FindChildOfTemplateWidget(controllerAssignmentWidget, WWUIScreenAssignControllers_UI::P2Kit);
	}

	if (GetWorld() && GetWorld()->GetGameInstance<URugbyGameInstance>() && stripTextWidget)
	{
		SetWidgetText(stripTextWidget, FText::FromString(UWWUITranslationManager::Translate(strip_text)));
	}
}

void UWWUIScreenControllerAssignment::UpdateHelpText()
{
	bool careerMode = false;
	bool proMode = false;
	RUCareerModeManager* careerManager;
	if (GetWorld() && GetWorld()->GetGameInstance<URugbyGameInstance>())
	{
		careerManager = GetWorld()->GetGameInstance<URugbyGameInstance>()->GetCareerModeManager();
		MABASSERT(careerManager);
		if (careerManager)
		{
			careerMode = careerManager->IsActive();
			proMode = careerManager->GetIsCareerModePro();
		}
	}

	FString addControllerText = ((controllerAssignmentWidget->GetActiveControllers().Num() >= 4) || proMode) ? "" : "[ID_ADD_CONTROLLER]";

	int masterControllerIdx = 0;

	int assignedPlayers = 0;

	//If the master controller is on a side, they need to see that they can change the strip.
	for (int i = 0; i < localPlayers.Num(); i++)
	{
		if (controllerAssignmentWidget->GetPlayerSide(i) != -1)
		{
			assignedPlayers++;
		}

		if (localPlayers[i] == GetWorld()->GetGameInstance()->GetPrimaryPlayerController())
		{
			masterControllerIdx = i;
			break;
		}
	}
	
	FString new_text = addControllerText + "[ID_ASSIGN_CONTROLLERS_QUICK_MATCH_HELP]";
	int team_index = controllerAssignmentWidget->GetPlayerSide(masterControllerIdx);

	if (!SIFGameHelpers::GAAreAllTeamStripsTheSame(team_index))
	{
		if (!careerMode && !bIsPlayGo)
		{
			new_text = addControllerText + "[ID_ASSIGN_CONTROLLERS_QUICK_MATCH_HELP]";
		}
		else
		{
			new_text = addControllerText + "[ID_ASSIGN_CONTROLLERS_COMPETITION_HELP]";
		}
	}
	else
	{
		if (!careerMode && !bIsPlayGo)
		{
			new_text = addControllerText + "[ID_ASSIGN_CONTROLLERS_QUICK_MATCH_HELP_NO_CHANGE_STRIP]";
		}
		else
		{
			new_text = addControllerText + "[ID_ASSIGN_CONTROLLERS_COMPETITION_HELP_NO_CHANGE_STRIP]";
		}
	}
	
	if (bFromInGame)
	{
		new_text = addControllerText + "[ID_ASSIGN_CONTROLLERS_COMPETITION_HELP_NO_CHANGE_STRIP]";
	}

	if(careerMode && proMode && assignedPlayers == 0)
	{
		new_text = "[ID_COMPETITION_BACK_HELP]";
	}

	UWWUIRichTextBlockWithTranslate * helpText = Cast<UWWUIRichTextBlockWithTranslate>(FindChildWidget(WWUIScreenAssignControllers_UI::HelpText));
	//SetWidgetText(helpText, FText::FromString(UWWUITranslationManager::TranslateWithFont(new_text)));
	if (helpText)
	{
		helpText->SetText(new_text);
	}
	else
	{
		ensure(helpText);
	}
}

//================================================================================================================
//========================================= Adding the rest of the script - CT ===================================
//================================================================================================================

void UWWUIScreenControllerAssignment::Shutdown()
{
	if (USIFMissingControllerListener * mcl = SIFApplication::GetApplication()->GetMissingControllerListener())
	{
		mcl->ForceSendNotification(false);
	}

	SIFApplication::GetApplication()->OnUGCRestrictionChanged.Remove(ugcRestrictionHandle);

	//Disable drop in 
	URugbyGameInstance* gameInstance = GetWorld()->GetGameInstance<URugbyGameInstance>();
	APlayerController* firstPlayer = GetWorld()->GetGameInstance()->GetPrimaryPlayerController();
	if (UOBJ_IS_VALID(firstPlayer) && UOBJ_IS_VALID(firstPlayer->GetLocalPlayer()))
	{
		if (URugbyGameViewportClient* rugbyViewport = Cast<URugbyGameViewportClient>(firstPlayer->GetLocalPlayer()->ViewportClient))
		{
			this->SetDropInAllowed(false);
			rugbyViewport->DisableDropIn();
			rugbyViewport->DisableDropOut();
			if (gameInstance)
			{
				gameInstance->GetDropInDelegate()->Clear();
			}
		}
	}

	if (bFromInGame)
	{
		//Set control back to the player who paused initially.
		SIFUIHelpers::ListenToNoControllers();
		SIFUIHelpers::DisableInGameInput(false);
		
		bool bControlGiven = false;


		for (int i = 0; i < OldListeningControllerIds.Num(); i++)
		{
			if (OldListeningControllerIds[i] != -1)
			{
				SIFUIHelpers::ListenToController(OldListeningControllerIds[i], true);
				bControlGiven = true;
			}
		}
		
		if(!bControlGiven)
		{
			int controlling_player = SIFApplication::GetApplication()->GetMasterControllerId();
			if (controlling_player == -1)
			{
				controlling_player = 0;
			}
			SIFUIHelpers::ListenToController(controlling_player, true);

		}

		UWWUIScreenManager * UIScreenManager = SIFApplication::GetApplication()->GetUIScreenManager();
		if (UIScreenManager)
		{
			if ((UIScreenManager->FindScreenTemplate(Screens_UI::PauseMenu) < 0) &&
				(UIScreenManager->FindScreenTemplate(Screens_UI::AssignControllers) < 0))
			{
				SIFGameHelpers::GAResumeGame();
			}
		}
	}
}


//===============================================================================
//===============================================================================
void UWWUIScreenControllerAssignment::HandleUGCRestrictionChange(bool bIsRestricted)
{
	bIsSomeAccountRestricted = SIFApplication::GetApplication()->IsAnyUserRestricted();
	bIsNonPrimaryRestricted = SIFApplication::GetApplication()->IsNonPrimaryUserRestricted();
	UpdateTeamNames();
}

//================================================================================================================
//================================================================================================================

TMap<int, ControllerAssignmentState> UWWUIScreenControllerAssignment::GetDefaultTeam()
{
	// --  The default team for the master controller should be on the first team.
	TMap<int, ControllerAssignmentState> team_pos;

	if (SIFGameHelpers::GAGetIsAProMode() && controller_reconnect)
	{
		controller_reconnect = false;
		return team_pos;
	}

	TArray<ULocalPlayer*> tempLocalPlayers = SIFApplication::GetApplication()->GetLocalPlayers();

	//Only show the first controller for be a pro
	if (GameMode.Compare("BeAPro") == 0)
	{
		while (tempLocalPlayers.Num() > 1)
		{
			tempLocalPlayers.RemoveAt(tempLocalPlayers.Num() - 1);
		}
	}

	for(ULocalPlayer * lp : tempLocalPlayers)
	{
		//Check to make sure the player is actually connected
		if (APlayerController * playerController = lp->GetPlayerController(GetWorld()))
		{
			// --  if (we're playing in a competition ) we need to select the side that has our current team.
			if (SIFGameHelpers::GACompetitionIsCompetitionActive())
			{
				// --  So which side has the player's team ID?
				if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
				{
					if (RUCareerModeManager* pCareerModeManager = pRugbyGameInstance->GetCareerModeManager())
					{
						if (ARugbyPlayerController * rugbyPlayerController = Cast<ARugbyPlayerController>(playerController))
						{
							int connectionIndex = rugbyPlayerController->GetConnectionIndex();
							const RUDB_COMP_INST_MATCH* current_match = pCareerModeManager->GetNextMatch();
							int32 team_id = SIFGameHelpers::GACompetitionGetCurrentPlayerTeamId();
							ControllerAssignmentState state = ControllerAssignmentState::Unassigned;

							// Additional controllers should start neutral
							if (connectionIndex == 0)
							{
								if (team_id == current_match->away_team_id)
								{
									state = ControllerAssignmentState::Right;
								}
								else
								{
									state = ControllerAssignmentState::Left;
								}
							}
							team_pos.Add(connectionIndex, state);
						}
					}
				}
			}
			else
			{
				if (ARugbyPlayerController * rugbyPlayerController = Cast<ARugbyPlayerController>(playerController))
				{
					int connectionIndex = rugbyPlayerController->GetConnectionIndex();
					ControllerAssignmentState state = ControllerAssignmentState::Unassigned;
					
					if (bFromInGame)
					{
						int humanSlot = -1;
						int index = 0;
						if (SIFGameWorld * game_world = SIFApplication::GetApplication()->GetActiveGameWorld())
						{
							for (SSHumanPlayer * human : game_world->GetHumanPlayers())
							{
								if (human->GetPlayerController() == rugbyPlayerController)
								{
									humanSlot = index;
								}
								index++;
							}
						}

						int32 team_id = SIFInGameHelpers::GetRunningPlayerTeam(rugbyPlayerController->GetControllerIndex());

						// Additional controllers should start neutral
						if (team_id == 0)
						{
							state = ControllerAssignmentState::Left;
						}
						else if (team_id == 1)
						{
							state = ControllerAssignmentState::Right;
						}
					}
					else
					{
						// Additional controllers should start neutral
						if (connectionIndex == 0)
						{
							// --  It's just a normal match, so default the team to the home team.
							state = ControllerAssignmentState::Left;
						}
					}
				
				
					team_pos.Add(connectionIndex, state);
				}
			}
		}
		if (SIFGameHelpers::GAGetIsAProMode() && team_pos.Num() == 1)		//Only 1 controller should be visible in pro mode.
		{
			return team_pos;
		}
	}
	
	return team_pos;
}


//================================================================================================================
//================================================================================================================
