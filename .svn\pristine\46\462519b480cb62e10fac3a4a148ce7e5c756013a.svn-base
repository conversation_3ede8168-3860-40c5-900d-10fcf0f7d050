#ifndef RUROLEPLAYTHEBALL_H
#define RUROLEPLAYTHEBALL_H

#include "Mab/Time/MabTimer.h"
#include "Match/SSRole.h"

class NMMabAnimationNetwork;

/**
	Rework from class RURoleTapRestart into RURolePlayTheBall #MLW
*/

class RURolePlayTheBall : public SSRole
{
	MABRUNTIMETYPE_HEADER(RURolePlayTheBall);

public:

	enum class RoleState
	{
		GETUP,					// Gets up from the ground and tries to face the correct direction
		PLAYTHEBALL,			// Starts play the ball anims
		INHANDS,				// While ball is still in hands, we wait..
		RELEASE,				// When the ball has been released
		COMPLETE,				// The role has been completed
		PREPARING,				// Used to get players in position when its not a tackle
		PLAYTHEBALLGROUND		// Starts ground play the ball anims
	};

	RURolePlayTheBall( SIFGameWorld* game );

	/// Enter this role with the specified player.
	void Enter(ARugbyCharacter* player) override;

	/// Exit this role.
	void Exit(bool forced) override;

	/// Advance this role.
	void UpdateLogic(const MabTimeStep& game_time_step) override;

	/// Get the fitness of the player for the given behaviour
	static int GetFitness(const ARugbyCharacter* player, const SSRoleArea* area);

	/// returns true if we're interruptible, false if we're not
	bool IsInterruptable() const override;

	const char* GetShortClassName() const override { return "PTB"; }

	void AnimationEvent(float time, ERugbyAnimEvent event, size_t userdata, bool bIsBlendingOut = false);

	/**
	 * Warp the player to their play restart position for the handover.
	 */
	void WarpToWaypoint() override;
private:
	
	void StartPlayAnimation();
	// Rotates the pla
	void RotatePlayerToPlayDir();
	// Checks if the player is facing play direction with in the angle
	bool IsFacingPlayDirection(float MaxAngleDegrees);

	/// Get the initial position for the restart - required on warp and initial phases so centralised
	FVector GetInitialPosition();

	RoleState		state;

	MabTime currentWaitDuration = 5.0f;
	MabTime waitBeforeRotateDuration = 4.0f;

	// GGs JZ Define the weights ground or standing animation,
	// note that ground has to be facing forward with in 45 degrees, otherwise does standing
	float WeightPlayBallWhileGettingUp = 1.0f;  // % chance for play the ball while getting up
	float WeightGettingUp = 0.0f;               // % chance for just getting up
	// Used to rotate while on the ground for ground play the ball
	bool ShouldRotate = false;

	bool animationPlayed = false;

	// Generate a random number between 0 and 1, will need to use a differnet random so it syncs across network, GetRNG should be used instead.
	// However atm we don't need to do weighted as we can just do groun animation if we are facing forward.
	//float RandomValue = FMath::FRand();

	const char* GETUP_REQUEST = "getup";
	const char* PLAY_THE_BALL_GROUND = "play_the_ball_ground";

	//GGS SRA: Using this to ensure the tackle ends the half in overtime, but only at an apropriate time
	bool notify_tackle_ended = false;
};

#endif //RUROLEPLAYTHEBALL_H
