/*--------------------------------------------------------------
|        Copyright (C) 1997-2008 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#include "RUStatsScorer.h"

//#include <Precompiled.h>
//#include <MabVariant.h>

#include "RugbyGameInstance.h"
#if PLATFORM_XBOX360
//#include <Xbox360MabUserInfo.h>
//#include "GameConfig.spa.h"
//#include "SIFXbox360SerialLuaFunctions.h"
//#include "RUGameSettings.h"
#elif PLATFORM_PS3
//#include <PS3MabOnlineUserManager.h>
#elif PLATFORM_PS4
//#include <PS4MabOnlineUserManager.h>
#elif PLATFORM_WINDOWS && defined ENABLE_STEAM
//#include <SteamMab.h>
#endif

//#include "SIFStatisticsHandler.h"
//#include "SIFPlayerProfilePropertyDefs.h"

#include "Mab/MabEvent.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "RUStatisticsSystem.h"
#include "Match/PlayerProfile/SIFPlayerProfileManager.h"
#include "Match/PlayerProfile/SIFPlayerProfile.h"
//#include "SIFWindowSystem.h"

#include "Mab/Net/MabNetworkManager.h"

// formerly from SIFPlayerProfilePropertyDefs.h
#define PLAYER_PROFILE_ONLINE_GAMES					"online_games"
#define PLAYER_PROFILE_ONLINE_LOSTS					"online_losts"

#define PLAYER_PROFILE_ONLINE_SEVENS_GAMES			"online_sevens_games"
#define PLAYER_PROFILE_ONLINE_SEVENS_LOSTS			"online_sevens_losts"

static const MabVariant VAR_ERROR_VAL ( MabVariant( (int)0, 0 ) );
static const MabVariant INT_ZERO = MabVariant( 0, -1 );
static const MabVariant INT64_ZERO = MabVariant( MabInt64( 0LL ) );

static bool GetIsOnlineMatch();
static bool GetIsPrivateMatch();
static bool GetIsRankedMatch();

RUStatsScorer::RUStatsScorer()
	: game_world( NULL ),
		controller_index( -1 ),
		match_complete_state(eMCT_None),
		theyQuit(false),
		weQuit(false),
		bDeductedPointsOnLoadStart(false)
{
	score_type_mapping[ RUGameScore::RANKING_SCORE ]	= TYPE_INT64;
	score_type_mapping[ RUGameScore::WINS ]				= TYPE_INT;
	score_type_mapping[ RUGameScore::LOSSES ]			= TYPE_INT;
	score_type_mapping[ RUGameScore::DRAWS ]			= TYPE_INT;
	score_type_mapping[ RUGameScore::RELIABILITY ]		= TYPE_INT;
	score_type_mapping[ RUGameScore::VISIBLE_SCORE ]	= TYPE_INT64;
	score_type_mapping[ RUGameScore::READ_WINS ]		= TYPE_INT;
	score_type_mapping[ RUGameScore::READ_LOSSES ]		= TYPE_INT;
	score_type_mapping[ RUGameScore::READ_DRAWS ]		= TYPE_INT;
	score_type_mapping[ RUGameScore::READ_RELIABILITY ]	= TYPE_INT;
	score_type_mapping[ RUGameScore::READ_VISIBLE_SCORE ]	= TYPE_INT64;
#if PLATFORM_PS4
	score_type_mapping[ RUGameScore::GAMETYPE ]				= TYPE_INT;
#endif
}

RUStatsScorer::~RUStatsScorer()
{
	score_type_mapping.clear();
}

void RUStatsScorer::AttachToGameWorld( int _controller_index, SIFGameWorld& _game_world )
{
	game_world = &_game_world;
	controller_index = _controller_index;
	match_complete_state = eMCT_None;
}

void RUStatsScorer::RemoveFromGameWorld()
{
	if( game_world != NULL )
	{
		game_world = NULL;
		controller_index = -1;
		match_complete_state = eMCT_None;
	}
}

void RUStatsScorer::InitGameEvents(SIFGameWorld *_game)
{
	SIFGameWorld *game = _game ? _game : SIFApplication::GetApplication()->GetActiveGameWorld();
	if (!game)
	{
		MABASSERTMSG(false,"Wa liao eh!");
		return;
	}

	MABASSERTMSG(game->GetEvents(),"we need events here");
	game->GetEvents()->first_half_start.Add(this, &RUStatsScorer::OnGameStart);

#if (PLATFORM_SWITCH ==0)
	game->GetEvents()->finish.Add(this, &RUStatsScorer::OnGameEnd);
#endif // PLATFORM_SWITCH
	
	//game->GetEvents()->final_game_percentage.Add(this, &RUStatsScorer::OnFinalStatistics);
	MABASSERTMSG(SIFApplication::GetApplication() && SIFApplication::GetApplication()->GetNetworkManager(),"we need nm here");
	SIFApplication::GetApplication()->GetNetworkManager()->OnNetSyncFail().AddRaw(this,&RUStatsScorer::OnSyncFail);
	SIFApplication::GetApplication()->GetNetworkManager()->OnNetSyncAlone().AddRaw(this,&RUStatsScorer::OnSyncAlone);
	SIFApplication::GetApplication()->GetNetworkManager()->OnNetSyncHalt().AddRaw(this,&RUStatsScorer::OnSyncHalt);
	SIFApplication::GetApplication()->GetNetworkManager()->OnDisconnected().AddRaw(this,&RUStatsScorer::OnDisconnect);
}
void RUStatsScorer::TermGameEvents(SIFGameWorld *_game)
{
	SIFGameWorld *game = _game ? _game : SIFApplication::GetApplication()->GetActiveGameWorld();
	if (!game)
	{
		MABASSERTMSG(false,"Wa liao eh!");
		return;
	}

	if (game->GetEvents())
	{
		game->GetEvents()->first_half_start.Remove(this, &RUStatsScorer::OnGameStart);
		game->GetEvents()->finish.Remove(this, &RUStatsScorer::OnGameEnd);
		//game->GetEvents()->final_game_percentage.Remove(this, &RUStatsScorer::OnFinalStatistics);
	}
	if (SIFApplication::GetApplication() && SIFApplication::GetApplication()->GetNetworkManager())
	{
		SIFApplication::GetApplication()->GetNetworkManager()->OnNetSyncFail().RemoveAll(this);
		SIFApplication::GetApplication()->GetNetworkManager()->OnNetSyncAlone().RemoveAll(this);
		SIFApplication::GetApplication()->GetNetworkManager()->OnNetSyncHalt().RemoveAll(this);
		SIFApplication::GetApplication()->GetNetworkManager()->OnDisconnected().RemoveAll(this);
	}
}

void RUStatsScorer::OnGameLoaded()
{
	weQuit = false;
	theyQuit = false;
#ifndef ENABLE_STATS_WRITES_ON_ALL_GAMES
	
	//These parameters are not yet set up at this point.
	//if (!GetIsOnlineMatch() || GetIsPrivateMatch())
	//{
	//	return;
	//}

	if (SIFApplication::GetApplication()->GetOnlineMode() == EOnlineMode::Offline)
	{
		return;
	}

	if (SIFApplication::GetApplication()->GetMatchmakingMode() != EMatchmakingMode::Ranked)
	{
		return;
	}
#endif

	SIFPlayerProfileManager* const player_profile_manager = SIFPlayerProfileManager::GetInstance();
	SIFPlayerProfile* const master_profile = player_profile_manager->GetMasterProfile();
	if (!master_profile)
	{
		MABBREAKMSG("Where is the master profile!");
		return;
	}

	//Add a loss and a reliability loss.  These will be removed on game completion.
	int32 wins = 0;
	int32 losses = 1;
	int32 draws = 0;
	int32 score = 0;
	master_profile->SetReliabilityForOnlineMatchQuit();
	int32 reliability = master_profile->GetReliabilityRatingAsRaw();
	//int reliabilityPercent = master_profile->GetReliabilityRatingAsPercentage();
	int32 points = 0;

	if (SIFApplication::GetApplication()->GetLeaderboardManager())
	{
		UE_LOG(LogOnlineGame, Warning, TEXT("AddPlayerLeaderboardValues"));
		SIFApplication::GetApplication()->GetLeaderboardManager()->AddPlayerLeaderboardValues(LBT_UNKNOWN, score, wins, draws, losses, reliability, points);
		SIFApplication::GetApplication()->GetLeaderboardManager()->PushPlayerLeaderboardData();
	}

	bDeductedPointsOnLoadStart = true;

	const int master_controller = master_profile->GetControllerIndex();
	player_profile_manager->SaveProfile(master_profile->GetName().c_str(), master_controller);
}

void RUStatsScorer::OnGameStart()
{
	weQuit = false;
	theyQuit = false;
}

void RUStatsScorer::OnGameEnd()
{
	if (theyQuit)
	{
		OnlineMatchQuitUpon();
	}
	else if (weQuit)
	{
		//OnlineMatchQuit();

		weQuit = false;
		theyQuit = false;
		//Leave game in case we are still in one

		return; //return out to prevent removing the loss.
	}
	else 
	{
		if (game_world)
		{
			OnlineMatchCompleted(false); // Nick WWS 7s to Womens 13s // game_world->GetGameSettings().game_settings.GameModeIsR7());
		}
		else
		{
			game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
			if (game_world)
				OnlineMatchCompleted(false); // Nick WWS 7s to Womens 13s //game_world->GetGameSettings().game_settings.GameModeIsR7());
		}
	}

#if !defined ENABLE_STATS_WRITES_ON_ALL_GAMES
	// if network game && matchmaking == ranked

	if (!game_world)
		game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	if (game_world && game_world->GetGameSettings().game_settings.network_game && SIFApplication::GetApplication()->GetMatchmakingMode() == EMatchmakingMode::Ranked)
#endif
	{
		SIFPlayerProfileManager* const player_profile_manager = SIFPlayerProfileManager::GetInstance();
		SIFPlayerProfile* const master_profile = player_profile_manager->GetMasterProfile();
		if (master_profile)
		{
			int32 wins = GetScore(RUGameScore::WINS).ToInt();
			int32 losses = bDeductedPointsOnLoadStart ? GetScore(RUGameScore::LOSSES).ToInt() - 1 : GetScore(RUGameScore::LOSSES).ToInt(); //We sent off a loss at the start.  Either remove it or leave it.
			int32 draws = GetScore(RUGameScore::DRAWS).ToInt();
			int32 score = GetScore(RUGameScore::RANKING_SCORE).ToInt64();
			//int reliability = GetScore(RUGameScore::RELIABILITY).ToInt();
			int32 reliability = master_profile->GetReliabilityRatingAsRaw();
			//int reliabilityPercent = master_profile->GetReliabilityRatingAsPercentage();
			int32 points = GetScore(RUGameScore::VISIBLE_SCORE).ToInt64();

			if (SIFApplication::GetApplication()->GetLeaderboardManager())
			{
				UE_LOG(LogOnlineGame, Warning, TEXT("AddPlayerLeaderboardValues"));
				SIFApplication::GetApplication()->GetLeaderboardManager()->AddPlayerLeaderboardValues(LBT_UNKNOWN, score, wins, draws, losses, reliability, points);
				SIFApplication::GetApplication()->GetLeaderboardManager()->PushPlayerLeaderboardData();
			}
		}
		else
		{
			MABBREAKMSG("Where is the master profile!");
		}
	}
	weQuit = false;
	theyQuit = false;
	bDeductedPointsOnLoadStart = false;
}

void RUStatsScorer::OnSyncFail()
{
}

void RUStatsScorer::OnSyncAlone()
{
	theyQuit = true;
}

void RUStatsScorer::OnSyncHalt()
{
}

void RUStatsScorer::OnDisconnect()
{
	// Don't care if we are not online or this isn't a ranked match.
	if (SIFApplication::GetApplication()->GetOnlineMode() == EOnlineMode::Offline)
	{
		return;
	}

	if (SIFApplication::GetApplication()->GetMatchmakingMode() != EMatchmakingMode::Ranked)
	{
		return;
	}

	// Something happened in the lobby that disconnected us while we were loading, lets revert our points and reliability.
	if (SIFApplication::GetApplication()->GetActiveGameWorld() && SIFApplication::GetApplication()->GetActiveGameWorld()->IsSandbox())
	{
		// Make sure we have already deducted points.
		if (bDeductedPointsOnLoadStart)
		{
			SIFPlayerProfileManager* const player_profile_manager = SIFPlayerProfileManager::GetInstance();
			SIFPlayerProfile* const master_profile = player_profile_manager->GetMasterProfile();
			if (!master_profile)
			{
				MABBREAKMSG("Where is the master profile!");
				return;
			}

			master_profile->SetReliabilityForOnlineMatchCompleted();
			int reliability = master_profile->GetReliabilityRatingAsRaw();

			UE_LOG(LogOnlineGame, Warning, TEXT("AddPlayerLeaderboardValues"));
			SIFApplication::GetApplication()->GetLeaderboardManager()->AddPlayerLeaderboardValues(LBT_UNKNOWN, 0, 0, 0, -1, reliability, 0);
			SIFApplication::GetApplication()->GetLeaderboardManager()->PushPlayerLeaderboardData();

			// Reset the flag.
			bDeductedPointsOnLoadStart = false;
		}
	}
}

const MabVariant& RUStatsScorer::GetScore( RUGameScore::Type index ) const
{
	MabMap< RUGameScore::Type, MabVariant >::const_iterator iter = scores.find( index );
	if ( iter != scores.end() )
	{
		VariantTypeCorrectCheck( index, iter->second.GetType() );
		return iter->second;
	} else {
#ifdef BUILD_DEBUG
		for ( MabMap< RUGameScore::Type, MabVariant >::const_iterator iter2 = scores.begin(); iter2 != scores.end(); ++iter2 )
		{
			MABLOGDEBUG( "Error case for GetScore; map index %d has value %s", iter2->first, iter2->second.ToString().c_str() );
		}
		MABBREAKMSG( "This score has not yet been set!" );
#endif
		return VAR_ERROR_VAL;
	}
};

void RUStatsScorer::SetScore( RUGameScore::Type index, const MabVariant& value )
{
	MABLOGDEBUG("RUStatsScorer::SetScore(%d,%s)",index,value.ToString().c_str());
	UE_LOG(LogOnlineLeaderboard, Warning, TEXT("RUStatsScorer::SetScore(%d,%hs)"), index, value.ToString().c_str() );
	VariantTypeCorrectCheck( index, value.GetType() );
	scores[ index ] = value;
};

#if PLATFORM_PS3 || PLATFORM_PS4
void RUStatsScorer::SetPreviousRankingScore( const MabVariant &value)
{
	previousRankingScore = value;
}

/// Returns the previous ranking score that we have just read, or have just posted to the ranking servers
/// If we haven't cached the previous ranking score it will be TYPE_UNKNOWN found through IsUnknown()
const MabVariant &RUStatsScorer::GetPreviousRankingScore() const
{
	return previousRankingScore;
}
#endif

void RUStatsScorer::UpdateScoresFromStats( const MabMap< RUGameScore::Type, MabVariant >& new_scores )
{
#ifdef BUILD_DEBUG
	for ( MabMap< RUGameScore::Type, MabVariant >::const_iterator iter = new_scores.begin(); iter != new_scores.end(); ++iter )
	{
		const VARIANTTYPE& variant_type = (*iter).second.GetType();
		switch( variant_type.val )
		{
		case TYPE_INT:
			MABLOGDEBUG( "Score type: %u, value: %d, VariantType: %s", iter->first, iter->second.ToInt(), variant_type.ToString().c_str() );
			break;
		case TYPE_INT64:
			MABLOGDEBUG( "Score type: %u, value: %d, VariantType: %s", iter->first,  (int)iter->second.ToInt64(), variant_type.ToString().c_str() );
			break;
		case TYPE_FLOAT:
			MABLOGDEBUG( "Score type: %u, value: %f, VariantType: %s", iter->first, iter->second.ToFloat(), variant_type.ToString().c_str() );
			break;
		}

		VariantTypeCorrectCheck( iter->first, iter->second.GetType() );
	}
#endif

	//insert the new scores, this will only add scores for which we do not already have a mapping, so if
	//we have a full set of scores already this won't do anything.
	scores.insert( new_scores.begin(), new_scores.end() );
}

void RUStatsScorer::VariantTypeCorrectCheck( RUGameScore::Type internal_type_enum, const VARIANTTYPE& variant_type ) const
{
	VariantTypeCorrectCheck( internal_type_enum, variant_type.val );
}

void RUStatsScorer::VariantTypeCorrectCheck(RUGameScore::Type internal_type_enum, VARIANTTYPEENUM variant_enum) const
{
#ifdef BUILD_DEBUG
	if ( score_type_mapping.size() == 0 ) return;
	MabMap< RUGameScore::Type, VARIANTTYPEENUM >::const_iterator iter = score_type_mapping.find( internal_type_enum );
	MABASSERTMSG( iter != score_type_mapping.end(), "score_type_mapping variable does not recognise this stats property!" );
	if ( iter == score_type_mapping.end() ) return;
	MABASSERTMSG( iter->second == variant_enum, "Variant is not the same type as has been registered with the stats scorer!" );
#else
	MABUNUSED(internal_type_enum);
	MABUNUSED(variant_enum);
#endif
}

#if 0
static bool CommonMessageSend( int controller_id, SIFStatisticsHandlerMessage& msg_out )
{
#if PLATFORM_XBOX360 || PLATFORM_XBOXONE || PLATFORM_PS3 || PLATFORM_PS4 || (PLATFORM_WINDOWS && defined ENABLE_STEAM)

#if PLATFORM_XBOX360
	// Check if we're signed in. If not, just return false.
	if( SIFApplication::GetApplication()->GetXbox360UserInfo()->IsUserOnline( controller_id ) )
#elif PLATFORM_PS3 || PLATFORM_PS4
	if( SIFApplication::GetApplication()->GetOnlineUserManager()->IsOnline() )
#elif PLATFORM_WINDOWS && defined ENABLE_STEAM
	if( SteamMab::IsOnline() )
#elif PLATFORM_XBOXONE
	if(true)
#endif
	{
		msg_out.player_controller = controller_id;
	} 
	else 
	{
		return false;
	}

	// Xbox One handles the message completely different. So for all other platforms do the following.
#ifndef PLATFORM_XBOXONE
	if( SIFApplication::GetApplication()->GetGameSettings()->game_settings.GameModeIsR13() )
	{
		msg_out.leaderboard_ids.push_back(STATS_VIEW_ONLINE_MATCHES);
	}
	else
	{
		msg_out.leaderboard_ids.push_back(STATS_VIEW_ONLINE_SEVENS_MATCHES);
	}
#endif // !PLATFORM_XBOXONE
	msg_out.perform_write = true;

	// RU-specific
#if PLATFORM_PS3 || PLATFORM_PSP2 || PLATFORM_PS4
	msg_out.write_without_read = false;
#endif

	return true;
#else
	MABUNUSED( controller_id );
	MABUNUSED( msg_out );
	return false;
#endif
}
#endif

static bool GetPlayerControllerAndTeamScores( const SIFGameWorld& game_world, int controller_index, int& player_score_out, int& opposition_score_out )
{
	const RUStatisticsSystem* const stats_system = SIFApplication::GetApplication()->GetStatisticsSystem();

	if( stats_system == NULL )
	{
		UE_LOG(LogOnlineLeaderboard, Warning, TEXT("RUStatsScorer::GetPlayerControllerAndTeamScores():   stats_system null! return now."));

		MABBREAK();
		return false;
	}

	const MabVector<SSHumanPlayer *> human_player_list = game_world.GetHumanPlayers();
	for( MabVector<SSHumanPlayer *>::const_iterator iter = human_player_list.begin(); iter != human_player_list.end(); ++iter )
	{
		const SSHumanPlayer* const current_player = *iter;
		if( current_player->GetControllerIndex() != controller_index )
		{
			continue;
		}

		// This is our team; find out which team it is and set up player_team and opposing team, then extract scores.
		//
		const RUTeam* const player_team = current_player->GetTeam();
		const RUTeam* opposing_team = NULL;

		if( player_team == NULL )
		{
			MABBREAK();
			continue;
		}

		if( player_team == game_world.GetTeam( SIDE_A ) )
		{
			opposing_team = game_world.GetTeam( SIDE_B );
		}
		else if( player_team == game_world.GetTeam( SIDE_B ) )
		{
			opposing_team = game_world.GetTeam( SIDE_A );
		}

		if( opposing_team == NULL )
		{
			MABBREAK();
			continue;
		}

		player_score_out = stats_system->GetCurrentMatchStat( player_team, &RUDB_STATS_TEAM::score );
		opposition_score_out = stats_system->GetCurrentMatchStat( opposing_team, &RUDB_STATS_TEAM::score );
		return true;
	}
	UE_LOG(LogOnlineLeaderboard, Warning, TEXT("RUStatsScorer::GetPlayerControllerAndTeamScores(): loop over, no matching player found! return now."));

	return false;
}

/// Under http://intranet/wiki/RU_OnlineLeaderboard#ReliabilityRating:
///
///GREEN/EXCELLENT: 80-100% reliability over past 10 games - earns full points (20 win/8 draw/4 loss)
///ORANGE/GOOD: 60-79% reliability over past 10 games - earns lower points (10 win/4 draw/2 loss)
///RED/POOR: 0-59% reliability over past 10 games - earns lowest points (5 win/2 draw/1 loss) 
void GetPointsFromReliabilityAndResult( RUGameScore::Type result_type, int reliability, MabInt64& score_out )
{
	if( reliability >= 90 )
	{
		switch( result_type )
		{
		case RUGameScore::WINS:
			score_out = 20LL;
			break;
		case RUGameScore::LOSSES:
			score_out = 4LL;
			break;
		case RUGameScore::DRAWS:
			score_out = 8LL;
			break;
		default:
			MABBREAKMSG( "Unsupported result type!" );
		}
	}
	else if( reliability >= 70 )
	{
		switch( result_type )
		{
		case RUGameScore::WINS:
			score_out = 10LL;
			break;
		case RUGameScore::LOSSES:
			score_out = 2LL;
			break;
		case RUGameScore::DRAWS:
			score_out = 4LL;
			break;
		default:
			MABBREAKMSG( "Unsupported result type!" );
		}
	}
	else
	{
		switch( result_type )
		{
		case RUGameScore::WINS:
			score_out = 5LL;
			break;
		case RUGameScore::LOSSES:
			score_out = 1LL;
			break;
		case RUGameScore::DRAWS:
			score_out = 2LL;
			break;
		default:
			MABBREAKMSG( "Unsupported result type!" );
		}

	}
}

static bool GetIsOnlineMatch()
{
	const SIFGameWorld* const game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	if( game_world == NULL ) return false;

	const RUGameSettings* game_settings = &game_world->GetGameSettings();
	if( game_settings == NULL ) return false;

	return( game_settings->game_settings.network_game );
}

static bool GetIsPrivateMatch() 
{
	if( const RUGameSettings* const game_settings = &SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings() )
	{
		return( game_settings->game_settings.private_match );
	}
	else
	{
		return true;
	}
}

static bool GetIsRankedMatch()
{
	return(SIFApplication::GetApplication()->GetMatchmakingMode() == EMatchmakingMode::Ranked);
}

void RUStatsScorer::OnlineMatchCompleted(bool is_sevens)
{
	MABLOGDEBUG("Match complete state: %i",match_complete_state);
//	MABASSERT(match_complete_state == eMCT_None || match_complete_state == eMCT_Completed);
	if(match_complete_state != eMCT_None)
	{
		UE_LOG(LogOnlineLeaderboard, Warning, TEXT("RUStatsScorer::OnlineMatchCompleted(): match complete state not equal to eMCT_NONE! return now."));
		return;
	}

	SIFPlayerProfileManager* const player_profile_manager = SIFPlayerProfileManager::GetInstance();
	SIFPlayerProfile* const master_profile = player_profile_manager->GetMasterProfile();
	if( !master_profile )
	{
		UE_LOG(LogOnlineLeaderboard, Warning, TEXT("RUStatsScorer::OnlineMatchCompleted(): no master profile found! return now."));

		MABBREAKMSG( "Where is the master profile!" );
		return;
	}

#ifndef ENABLE_STATS_WRITES_ON_ALL_GAMES
	if( !GetIsOnlineMatch() || GetIsPrivateMatch() )
	{
		UE_LOG(LogOnlineLeaderboard, Warning, TEXT("RUStatsScorer::OnlineMatchCompleted(): not online match or is private match! return now."));

		return;
	}

	if (SIFApplication::GetApplication()->GetMatchmakingMode() != EMatchmakingMode::Ranked)
	{
		UE_LOG(LogOnlineLeaderboard, Warning, TEXT("RUStatsScorer::OnlineMatchCompleted(): not rank game! return now."));
		return;
	}
#endif


	// Zero out scores initially.
	for( RUGameScore::Type score_type = (RUGameScore::Type)( 0 ); score_type <= RUGameScore::RANKING_SCORE; score_type = (RUGameScore::Type)( (int)score_type + 1 ) )
	{
		if( score_type == RUGameScore::VISIBLE_SCORE || score_type == RUGameScore::RANKING_SCORE )
		{
			SetScore( score_type, MabVariant( MabInt64( 0ll ) ) );
		}
		else
		{
			SetScore( score_type, MabVariant( 0, -1 ) );
		}
	}

	const int master_controller = master_profile->GetControllerIndex();

	master_profile->SetReliabilityForOnlineMatchCompleted();

	int player_score = 0, opposing_score = 0;

	if (!game_world)
		game_world = SIFApplication::GetApplication()->GetActiveGameWorld();

	if (!game_world)
		UE_LOG(LogOnlineLeaderboard, Warning, TEXT("RUStatsScorer::OnlineMatchCompleted(): Could not get game world object!"));

	if( !game_world || !GetPlayerControllerAndTeamScores( *game_world, master_controller, player_score, opposing_score ) )
	{
		UE_LOG(LogOnlineLeaderboard, Warning, TEXT("RUStatsScorer::OnlineMatchCompleted(): either game world invalid or get score failed! return now."));
		return;
	}
	UE_LOG(LogOnlineLeaderboard, Warning, TEXT("RUStatsScorer::OnlineMatchCompleted(): game world object is OK."));

	MabInt64 set_score( 0LL );
	RUGameScore::Type result_type = RUGameScore::LAST_ELEMENT;
	if( player_score == opposing_score )
	{
		result_type = RUGameScore::DRAWS;
	} 
	else if( player_score > opposing_score )
	{
		result_type = RUGameScore::WINS;
	}
	else if( player_score < opposing_score )
	{
		result_type = RUGameScore::LOSSES;
	}

	// Get the points that this score's worth.
	GetPointsFromReliabilityAndResult( result_type, master_profile->GetReliabilityRatingAsPercentage(), set_score );

	// Only need to increment the result type, all other fields have been set to 0.
	SetScore( result_type, MabVariant( 1, -1 ) );
	MABLOGDEBUG("OnlineMatchCompleted score=%d",(MabInt32)set_score);
	SetScore( RUGameScore::VISIBLE_SCORE, MabVariant( set_score ) );
	SetScore( RUGameScore::RANKING_SCORE, MabVariant( set_score ) );

	SetScore( RUGameScore::RELIABILITY, MabVariant( master_profile->GetReliabilityRatingAsPercentage(), -1 ) );

	// Just in cases set these values from leadersboards... in theory more accurate?
	master_profile->GetNamedValueList()->SetValue(is_sevens ? PLAYER_PROFILE_ONLINE_SEVENS_LOSTS : PLAYER_PROFILE_ONLINE_LOSTS, GetScore(RUGameScore::LOSSES).ToInt() );
	master_profile->GetNamedValueList()->SetValue(is_sevens ? PLAYER_PROFILE_ONLINE_SEVENS_GAMES : PLAYER_PROFILE_ONLINE_GAMES, 
		GetScore(RUGameScore::WINS).ToInt() + 
		GetScore(RUGameScore::DRAWS).ToInt() +
		GetScore(RUGameScore::LOSSES).ToInt() );

	// Construct the message to send.
	//SIFStatisticsHandlerMessage tmp_msg;
	//if( CommonMessageSend( master_controller, tmp_msg ) )
	{
		//Notify( tmp_msg );
	}

	match_complete_state = eMCT_Completed;
}

// Call when an online match has been quit.
void RUStatsScorer::OnlineMatchQuit()
{
	weQuit = true;
//	MABASSERT(match_complete_state == eMCT_None || match_complete_state == eMCT_Quit);
	if(match_complete_state != eMCT_None)
		return;

	SIFPlayerProfileManager* const player_profile_manager = SIFPlayerProfileManager::GetInstance();
	SIFPlayerProfile* const master_profile = player_profile_manager->GetMasterProfile();
	if( !master_profile )
	{
		MABBREAKMSG( "Where is the master profile!" );
		return;
	}

#ifndef ENABLE_STATS_WRITES_ON_ALL_GAMES
	if( !GetIsOnlineMatch() || GetIsPrivateMatch() )
	{
		return;
	}
#endif
	SIFGameWorld *gw = SIFApplication::GetApplication()->GetActiveGameWorld();
	int mode = 0; // Nick  WWS 7s to Womens // gw->GetGameSettings().game_settings.GetGameMode();
#if PLATFORM_PS4
	int gameType = 15; // Nick  WWS 7s to Womens // (mode == GAME_MODE_SEVENS) ? 7 : 15;
#endif
	int shortMatch = 10; // Nick  WWS 7s to Womens // (mode == GAME_MODE_SEVENS) ? 3 : 10;

	const int master_controller = master_profile->GetControllerIndex();

//	MABASSERT(game_world);
	
	if( game_world == NULL )
		game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	if (!game_world)
		return;

	bool should_save_profile = true;
#if PLATFORM_XBOX360
	should_save_profile = SIFXbox360SerialLuaFunctions::IsControllerSignedIn( master_controller );
#endif

	if ( should_save_profile )
	{
		//if (game_world->GetGameTimer()->GetScaledMinutesElapsed() >= shortMatch)
		//{
		//	master_profile->SetReliabilityForOnlineMatchQuit();
		//}

		SetScore( RUGameScore::WINS,			INT_ZERO );
		SetScore( RUGameScore::LOSSES,			INT_ZERO );
		SetScore( RUGameScore::DRAWS,			INT_ZERO );
		MABLOGDEBUG("OnlineMatchQuit score=%d",0);
		SetScore( RUGameScore::VISIBLE_SCORE,	INT64_ZERO );
		SetScore( RUGameScore::RANKING_SCORE,	INT64_ZERO );	
		SetScore( RUGameScore::RELIABILITY, MabVariant( master_profile->GetReliabilityRatingAsPercentage(), -1 ) );
#if PLATFORM_PS4
		SetScore(RUGameScore::GAMETYPE,MabVariant(gameType,-1));
#endif

		//player_profile_manager->SaveProfile( master_profile->GetName().c_str(), master_controller );

		// Construct the message to send.
		//SIFStatisticsHandlerMessage tmp_msg;
		//CommonMessageSend( master_controller, tmp_msg );

		//Notify( tmp_msg );
		match_complete_state = eMCT_Quit;
	}
}

// Call when a player has been quit UPON, that is, the opposing player has quit.
void RUStatsScorer::OnlineMatchQuitUpon()
{
//	MABASSERT(match_complete_state == eMCT_None || match_complete_state == eMCT_QuitUpon);
	if(match_complete_state != eMCT_None)
		return;

	SIFPlayerProfileManager* const player_profile_manager = SIFPlayerProfileManager::GetInstance();
	SIFPlayerProfile* const master_profile = player_profile_manager->GetMasterProfile();
	if( !master_profile )
	{
		MABBREAKMSG( "Where is the master profile!" );
		return;
	}

#ifndef ENABLE_STATS_WRITES_ON_ALL_GAMES
	if( !GetIsOnlineMatch() || GetIsPrivateMatch() )
	{
		return;
	}

	if (SIFApplication::GetApplication()->GetMatchmakingMode() != EMatchmakingMode::Ranked)
	{
		return;
	}
#endif

	const int master_controller = master_profile->GetControllerIndex();

	if( !game_world)
		game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	if (!game_world)
	{
		MABASSERT(game_world);
		return;
	}
	// If we're only in the first 10Min or the quittee's losing, set everything to zero and send the notification.

	int player_score = 0, opposing_score = 0;
	if( !GetPlayerControllerAndTeamScores( *game_world, master_controller, player_score, opposing_score ) )
	{
		return;
	}

	int mode = 0; // Nick  WWS 7s to Womens // game_world->GetGameSettings().game_settings.GetGameMode();
	int shortMatch = 10; // Nick  WWS 7s to Womens // (mode == GAME_MODE_SEVENS) ? 3 : 10;

	//if (game_world->GetGameTimer()->GetScaledMinutesElapsed() < shortMatch)
	//{
	//	SetScore( RUGameScore::WINS, INT_ZERO );
	//	SetScore( RUGameScore::LOSSES, INT_ZERO );
	//	SetScore( RUGameScore::DRAWS, INT_ZERO );
	//	SetScore( RUGameScore::VISIBLE_SCORE, INT64_ZERO );
	//	SetScore( RUGameScore::RANKING_SCORE, INT64_ZERO );
	//	master_profile->SetReliabilityForOnlineMatchCompleted();
	//	SetScore( RUGameScore::RELIABILITY, MabVariant( master_profile->GetReliabilityRatingAsPercentage(), -1 ) );
	//}
	//else
	{
		// Give points for Win, and online match completed.
		master_profile->SetReliabilityForOnlineMatchCompleted();
		MabInt64 set_score( 0LL );
		MabVariant wins = INT_ZERO;
		RUGameScore::Type result = RUGameScore::DRAWS;
		//if (player_score > opposing_score)
		{
			result = RUGameScore::WINS;
			wins = MabVariant(1, -1);
		}
		//else if (player_score < opposing_score)
		//{
		//	result = RUGameScore::LOSSES;
		//}
		GetPointsFromReliabilityAndResult( result, master_profile->GetReliabilityRatingAsPercentage(), set_score );

		SetScore( RUGameScore::WINS, wins );
		SetScore( RUGameScore::LOSSES, INT_ZERO );
		SetScore( RUGameScore::DRAWS, INT_ZERO );
		MABLOGDEBUG("OnlineMatchQuitUpon score=%d",(MabInt32)set_score);
		SetScore( RUGameScore::VISIBLE_SCORE, MabVariant( set_score ) );
		SetScore( RUGameScore::RANKING_SCORE, MabVariant( set_score ) );
		SetScore( RUGameScore::RELIABILITY, MabVariant( master_profile->GetReliabilityRatingAsPercentage(), -1 ) );
	}
		
	bool should_save_profile = true;
#if PLATFORM_XBOX360
	// HACK: This checks the console to see if we're signed in.
	// We can't save a profile if there's no active profile.
	// You're probably wondering why we can't just check to profile manager to see who's signed in, right?
	// Well if the user is currently in the process of signing out when we go to save the profile,
	// the profile manager will say the profile is signed in because the sign out process hasn't completed.
	// This is bad because saving is async, so the profile wont save until next frame, but by that time the
	// profile has signed out.
	// The solution here is to ask the console for the signin state, which returns signed out even if the
	// sign out process isn't complete.
	should_save_profile = SIFXbox360SerialLuaFunctions::IsControllerSignedIn( master_controller );
#endif

	if ( should_save_profile )
	{
		player_profile_manager->SaveProfile( master_profile->GetName().c_str(), master_controller );
	}

	// Construct the message to send.
	//SIFStatisticsHandlerMessage tmp_msg;
	//CommonMessageSend( master_controller, tmp_msg );

	//Notify( tmp_msg );

	match_complete_state = eMCT_QuitUpon;
}
