// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIPopulatorCareerTeamSquad.h"

#include "WWUIListField.h"

#include "Rugby/RugbyGameInstance.h"

#include "WWUITranslationManager.h"

#include "Rugby/Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3CompetitionPlayerHelper.h"

#include "ScrollBox.h"
#include "WidgetSwitcher.h"
#include "Border.h"
#include "SizeBox.h"
#include "Image.h"

#include "UI/GeneratedHeaders/WWUIScreenCareerTeamSquad_UI_Namespace.h"
#include "WWUIPopulatorInGameSquad.h"
#include "UI/Components/WWUIUserWidgetCareerSquadText.h"

#ifdef ENABLE_GAME_DEBUG_MENU
#include "Match/Debug/SIFDebug.h"
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeDebugSettings.h"
#endif
#include "Utility/Helpers/SIFGameHelpers.h"
#include "Match/HUD/RUHUDUpdater.h"


#define RUCSP_SELECTED_MARKED_TEXT		"SelectedMarkedText"
#define RUCSP_UNSELECTED_MARKED_TEXT	"UnselectedMarkedText"
#define RUCSP_SELECTED_SWAP_TEXT		"UnselectedSwapText"
#define RUCSP_UNSELECTED_SWAP_TEXT		"UnselectedSwapText"


#define RUCSP_COUNT			"Column0/Count"
#define RUCSP_POS			"Column1/Pos"
#define RUCSP_SECOND		"Column2/Second"
#define RUCSP_NAME			"Column3/Name"
#define RUCSP_RATING		"Column4/Rating"
#define RUCSP_STATUS		"Column5/Status"

#define RUCSP_PLACEHOLDER	"PC"


// Message for when we finish populating and refreshing
#define RUCSP_LIST_UPDATE				"career_squad_list_update"

#define RUCSP_PLAYER_ID					"career_squad_player_id"
#define RUCSP_PLAYER_LIST_INDEX			"career_squad_player_list_index"
#define RUCSP_SWAP_PLAYER_ID			"career_squad_swap_player_id"
#define RUCSP_REFRESH_ON_SWAP			"career_squad_refresh_on_swap"
#define RUCSP_PLAYER_STATES_PROPERTY	"career_squad_player_states"

void UWWUIPopulatorCareerTeamSquad::Populate(UWidget* widget)
{
	if (inPreConstruct)
	{
		preConstructOwner = widget;
	}

	Clear(widget);

	CareerSquadDataFileCreationNodeCallback callbackObject(widget, dataList.ArrayOption);

	uint32 ItemCount = OrderedPlayers.Num();

	if (inPreConstruct)
	{
		ItemCount = dataList.ArrayOption.Num();
	}

	CreateNodesFromTemplate(dataList.TemplateName, ItemCount, &callbackObject);

	PrevStartingIndex = -1;
	PrevSelectedIndex = -1;

	if (ScreenRef)
	{
#ifdef UI_USING_UMG
		ScreenRef->StoreChildWidgets();
#else
		if (ScreenRef && ScreenRef->GetStateScreen())
		{
			ScreenRef->GetStateScreen()->StoreChildWidgets();
		}
#endif
	}
}

//===============================================================================
//===============================================================================

void UWWUIPopulatorCareerTeamSquad::Refresh(UWidget* widget)
{
	// Get starting index
	UScrollBox* listbox = Cast<UScrollBox>(widget);

	if (!listbox)
	{
		return;
	}

	// Get properties
	int old_player_to_swap = PlayerToSwapID;
	
	PlayerToSwapID = SwapPlayerID;

	// Load team
	RU_TeamDB_Data* db_team = SIFApplication::GetApplication()->GetGameDBHelper()->GetTeamDBData();
	MABASSERT(db_team);


	// Get captain and goalkicker of the team
	int old_captain_player_id = CaptainPlayerID;
	int old_goalkicker_player_id = GoalKickerPlayerID;
	int old_playkicker_player_id = PlayKickerPlayerID;

	CaptainPlayerID = db_team->GetCaptain();
	GoalKickerPlayerID = db_team->GetGoalKicker(0);
	PlayKickerPlayerID = db_team->GetPlayKicker(0);


	// Refresh if necessary
	if (PrevStartingIndex == 0 && !bRefreshOnSwap)
	{
		if (PrevSelectedIndex != SelectedIndex)
		{
			// Unselect current player
			{
				const PlayerInfo& player_info = OrderedPlayers[PrevSelectedIndex];
				UWWUIListField* pChild = Cast<UWWUIListField>(listbox->GetChildAt(PrevSelectedIndex));
				
				if (pChild)
				{
					PopulatePlayerInfo(player_info, pChild, PrevSelectedIndex + 1);
				}
			}


			// Select the new player
			{
				const PlayerInfo& player_info = OrderedPlayers[SelectedIndex];
				UWWUIListField* pChild = Cast<UWWUIListField>(listbox->GetChildAt(SelectedIndex));

				if (pChild)
				{
					PopulatePlayerInfo(player_info, pChild, SelectedIndex + 1);
				}
			}

			PrevSelectedIndex = SelectedIndex;
		}

		// If player row has changed, just repopulate the new and old players
		if (old_player_to_swap != PlayerToSwapID)
		{
			RepopulatePlayerInfoByIds(widget, old_player_to_swap, PlayerToSwapID);
		}

		if (old_captain_player_id != CaptainPlayerID)
		{
			RepopulatePlayerInfoByIds(widget, old_captain_player_id, CaptainPlayerID);
		}

		if (old_goalkicker_player_id != GoalKickerPlayerID)
		{
			RepopulatePlayerInfoByIds(widget, old_goalkicker_player_id, GoalKickerPlayerID);
		}

		if (old_playkicker_player_id != PlayKickerPlayerID)
		{
			RepopulatePlayerInfoByIds(widget, old_playkicker_player_id, PlayKickerPlayerID);
		}
	}
	else
	{
		// If refreshing on swap, then choose the selected player again (reset selected index)
		if (bRefreshOnSwap)
		{
			// If current list already has the selected player, don't have to do anything
			for (unsigned int i = 0; i < (unsigned int)listbox->GetChildrenCount(); ++i)
			{
				if (i < (unsigned int)OrderedPlayers.Num())
				{
					const PlayerInfo& player_info = OrderedPlayers[i];
					if (player_info.db_id == SelectedPlayerID)
					{
						// Need to select the right node after the populate has run
						// Reselect in the current list
						SelectedIndex = (int)i;

						SelectedPlayerID = DB_INVALID_ID;
						break;
					}
				}
			}

			// Reset the starting/selected index to select player
			if (SelectedPlayerID != DB_INVALID_ID)
			{
				for (int i = 0; i < OrderedPlayers.Num(); i++)
				{
					if (OrderedPlayers[i].db_id == SelectedPlayerID)
					{
						SelectedIndex = (int)(i);
						// Reselect in the current list

						break;
					}
				}
			}
		}

		// Populate children
		for (int i = 0; i < listbox->GetChildrenCount(); ++i)
		{
			if(i < OrderedPlayers.Num())
			{
				const PlayerInfo& player_info = OrderedPlayers[i];

				UWWUIListField* pChild = Cast<UWWUIListField>(listbox->GetChildAt(i));

				if (pChild)
				{
					// Set info for lua
					int PlayerDBID = player_info.db_id;
					pChild->SetProperty(RUCSP_PLAYER_ID, &PlayerDBID, PROPERTY_TYPE_INT);

					unsigned int list_index = (unsigned int)(i);
					pChild->SetProperty(RUCSP_PLAYER_LIST_INDEX, &list_index, PROPERTY_TYPE_INT);

					PopulatePlayerInfo(player_info, pChild, list_index);
				}
			}
		}

	}

	PrevStartingIndex = 0;
	PrevSelectedIndex = SelectedIndex;

	bRefreshOnSwap = false;
}

//===============================================================================
//===============================================================================

void UWWUIPopulatorCareerTeamSquad::PopulateAndRefresh(UWidget* widget)
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (!pRugbyGameInstance)
	{
		return;
	}

	RUDBHelperInterface* pDatabaseHelper = pRugbyGameInstance->GetGameDBHelper();

	if (!pDatabaseHelper)
	{
		return;
	}

	// Get loaded team
	RU_TeamDB_Data* db_team = pDatabaseHelper->GetTeamDBData();

	MABASSERT(db_team);

	UScrollBox* listbox = Cast<UScrollBox>(widget);

	if (listbox)
	{
		if (listbox->GetChildrenCount() == 0 || bRefreshOnSwap)
		{
			// Load players
			OrderedPlayers.Empty();
			GenerateOrderedPlayerList(db_team);
		}

		if (listbox->GetChildrenCount() == 0)		//|| refresh_on_swap )
		{
			// Populate
			Populate(widget);
		}

		Refresh(widget);

		WWUINodeProperty UpdateEvent = WWUINodeProperty();
		FString EventName = "career_squad_list_update";
		UpdateEvent.SetProperty("system_event", &EventName, PROPERTY_TYPE_FSTRING);

		ScreenRef->OnSystemEvent(UpdateEvent);
	}
}

//===============================================================================
//===============================================================================

void UWWUIPopulatorCareerTeamSquad::GenerateOrderedPlayerList(RU_TeamDB_Data* db_team)
{
	// Get helper
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (!pRugbyGameInstance)
	{
		return;
	}

	RUDBHelperInterface* pDatabaseHelper = pRugbyGameInstance->GetGameDBHelper();

	if (!pDatabaseHelper)
	{
		return;
	}

	RL3CompetitionPlayerHelper* competition_player_helper = SIFApplication::GetApplication()->GetCareerModeManager()->GetCompetitionPlayerHelper();

	// Get all players in display order
	MabVector< unsigned short > player_lineup;
	db_team->GetAllPlayersInDisplayOrder(player_lineup, true);

	/// Remove blank players at end of squad.
	//int playersOnTeamIncBench = SIFApplication::GetApplication()->GetGameWorld()->GetGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBench();
	//int	playersPerTeam = db_team->GetIsR7Exclusive() ? DB_NUM_PLAYERS_PER_TEAM_R7 : DB_NUM_PLAYERS_PER_TEAM;

	int	playersOnTeamIncBench =  // Nick WWS &s to Womens 13s // db_team->GetIsR7Exclusive() ?
		// Nick WWS &s to Womens 13s // SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBenchR7() :
		SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBenchR13();

	if (bFromInGame)
	{
		while (player_lineup.size() > (size_t)playersOnTeamIncBench)
		{
			player_lineup.pop_back();
		}
	}
	else
	{
		while (player_lineup.size() > (size_t)playersOnTeamIncBench/*NUM_PLAYERS_PER_TEAM_INC_BENCH*/ && player_lineup[player_lineup.size() - 1] == DB_INVALID_ID)
		{
			player_lineup.pop_back();
		}
	}
	//MABASSERT(player_lineup.size()<=(size_t)DB_NUM_PLAYERS_PER_TEAM);

	// Cache player info
	if (OrderedPlayers.Num() != player_lineup.size())
	{
		OrderedPlayers.Empty();
		for (MabVector< unsigned short >::iterator id_it = player_lineup.begin(); id_it != player_lineup.end(); ++id_it)
		{
			PlayerInfo pinfo;
			OrderedPlayers.Add(pinfo);
		}
	}


	auto player_it = OrderedPlayers.CreateIterator();
	for (MabVector< unsigned short >::iterator id_it = player_lineup.begin();
		id_it != player_lineup.end();
		++id_it, ++player_it)
	{
		if (*id_it != DB_INVALID_ID)
		{
			if (*id_it != player_it->db_id)
			{
				RU_PlayerDB_Data* player = pDatabaseHelper->LoadPlayerDBData(*id_it);
				player->PopulatePlayerInfo(&(*player_it), false);  // Nick WWS &s to Womens 13s // db_team->GetIsR7Exclusive());

				if(!bFromInGame)
				{
					player_it->injury_period = player->GetNumDaysInjured();
					player_it->suspension_period = player->GetNumDaysSuspended();
				}
								
				player_it->valid = true;

				RL3DB_PLAYER db_player((unsigned short)player_it->db_id);
				RL3DB_TEAM team((unsigned short)db_team->GetDbId());

				if(!bFromInGame)
				{
					if (db_team->IsInternational())
					{
						player_it->is_requisitioned = false;		// Don't show for international teams. (As icon will be on every player!)
						player_it->is_temporary = false;
					}
					else
					{
						player_it->is_requisitioned = competition_player_helper->IsPlayerExcludedByInternationalGame(db_player, NUM_INTERNATIONAL_DAYS_EXCLUDED);

						RU_ContractDB_Data* db_contract = pDatabaseHelper->LoadContractDBData(db_team->GetDbId(), *id_it);
						MABASSERT(db_contract);
						player_it->contract_position = (PLAYER_POSITION)db_contract->GetPosition();

						player_it->is_temporary = db_contract->GetValue() == 0;
					}
				}
			}
		}
	}

	//------------------------------------------------------
	// Encode player states as a string to send to lua.
	PlayerStates.Empty();
	for (int i = 0; i < OrderedPlayers.Num(); i++)
	{
		const PlayerInfo& player_info = OrderedPlayers[i];
		if (player_info.db_id == DB_INVALID_ID)
		{
			PlayerStates.Add(EPlayerState::X);
		}
		else if ((player_info.suspension_period > 0 || player_info.injury_period > 0 || player_info.is_requisitioned) && i < playersOnTeamIncBench/*NUM_PLAYERS_PER_TEAM_INC_BENCH*/)
		{
			PlayerStates.Add(EPlayerState::X);
		}
		else
		{
			PlayerStates.Add(EPlayerState::O);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIPopulatorCareerTeamSquad::PopulatePlayerInfo(const PlayerInfo& player_info, UWidget* row_node, unsigned int list_index)
{
	// Dewald WW - i dont know how this works, but i hope it does work...
	RU_TeamDB_Data* db_team = SIFApplication::GetApplication()->GetGameDBHelper()->GetTeamDBData();
	int	playersPerTeam =  // Nick WWS &s to Womens 13s //db_team->GetIsR7Exclusive() ? DB_NUM_PLAYERS_PER_TEAM_R7 : 
		DB_NUM_PLAYERS_PER_TEAM;

	int	playersOnField =  // Nick WWS &s to Womens 13s //db_team->GetIsR7Exclusive() ?
		// Nick WWS &s to Womens 13s //SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamR7() :
		SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamR13();

	int	playersOnTeamIncBench =  // Nick WWS &s to Womens 13s //db_team->GetIsR7Exclusive() ?
		// Nick WWS &s to Womens 13s //SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBenchR7() :
		SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBenchR13();

	UWWUIListField* pListField = Cast<UWWUIListField>(row_node);

	if (pListField)
	{
		UImage* pSquadDividerImage = Cast<UImage>(ScreenRef->FindChildOfTemplateWidget(pListField, "ImageDivider"));

		playersPerTeam = SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeam();
		playersOnTeamIncBench = SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeamIncBench();

		if (pSquadDividerImage)
		{
			pSquadDividerImage->SetVisibility(((int)list_index == playersPerTeam || (int)list_index == playersOnTeamIncBench || (int)list_index == playersOnField) ?
				ESlateVisibility::Visible :
				ESlateVisibility::Collapsed);
		}

		bool marked = (player_info.injury_period > 0 || player_info.suspension_period > 0) && list_index <= (unsigned int)playersOnTeamIncBench;		// NB. list_index starts at 1.

		bool swapped = PlayerToSwapID == player_info.db_id;

		UWidget* pChild = ScreenRef->FindChildOfTemplateWidget(pListField, WWUIScreenCareerTeamSquad_UI::CareerSquadText);

		if (pChild)
		{

			UWWUIUserWidgetCareerSquadText* pSquadText = Cast< UWWUIUserWidgetCareerSquadText>(pChild);

			if (pSquadText)
			{
				pSquadText->SetSquadTextColor(PIGS_DEFAULT_TEXT_COLOR, true);


				if (marked)
				{
					pSquadText->SetSquadTextColor(FLinearColor(PIGS_MARKED_TEXT_COLOR));
				}

				else if (swapped)
				{
					pSquadText->SetSquadTextColor(PIGS_SWAP_TEXT_COLOR);
				}

			}
			else
			{
				ensure(pSquadText);
			}

		}
		else
		{
			ensure(pChild);
			return;
		}

		UWidget* fatigue_bar_node = ScreenRef->FindChildOfTemplateWidget(pChild, WWUIScreenCareerTeamSquad_UI::SizeBoxFatigueBar);
		if (fatigue_bar_node)
		{
			fatigue_bar_node->SetVisibility(ESlateVisibility::Hidden);
		}

		// Turn on indicators and get the status
		
		UWidget* captain_node = ScreenRef->FindChildOfTemplateWidget(pChild, "RoleIconCaptain");
		UWidget* goalkicker_node = ScreenRef->FindChildOfTemplateWidget(pChild, "RoleIconGoalKicker");
		UWidget* playkicker_node = ScreenRef->FindChildOfTemplateWidget(pChild, "RoleIconPlayKicker");
		UWidget* injury_node = ScreenRef->FindChildOfTemplateWidget(pChild, "SizeBoxInjured");
		UWidget* suspended_node = ScreenRef->FindChildOfTemplateWidget(pChild, "SizeBoxSuspended");
		UWidget* warning_node = ScreenRef->FindChildOfTemplateWidget(pChild, "SizeBoxWarning");
		UWidget* requisitioned_node = ScreenRef->FindChildOfTemplateWidget(pChild, "SizeBoxRequisitioned");
		UWidget* temp_player_node = ScreenRef->FindChildOfTemplateWidget(pChild, "SizeBoxTemp");

		if (captain_node && goalkicker_node && playkicker_node && injury_node && suspended_node && warning_node && requisitioned_node && temp_player_node)
		{
			captain_node->SetVisibility(ESlateVisibility::Collapsed);
			goalkicker_node->SetVisibility(ESlateVisibility::Collapsed);
			playkicker_node->SetVisibility(ESlateVisibility::Collapsed);
			injury_node->SetVisibility(ESlateVisibility::Collapsed);
			suspended_node->SetVisibility(ESlateVisibility::Collapsed);
			warning_node->SetVisibility(ESlateVisibility::Collapsed);
			requisitioned_node->SetVisibility(ESlateVisibility::Collapsed);
			temp_player_node->SetVisibility(ESlateVisibility::Collapsed);

			FString status = "";

			if (player_info.db_id != DB_INVALID_ID)
			{
				if (player_info.suspension_period > 0)
				{
					suspended_node->SetVisibility(ESlateVisibility::Visible);
					status = FString::FromInt(player_info.suspension_period) + " [ID_DAYS]";
				}
				else if (player_info.injury_period > 0)
				{
					injury_node->SetVisibility(ESlateVisibility::Visible);
					status = FString::FromInt(player_info.injury_period) + " [ID_DAYS]";
				}
				else
				{
					if (player_info.is_requisitioned)
					{
						requisitioned_node->SetVisibility(ESlateVisibility::Visible);
					}
					if (player_info.db_id == CaptainPlayerID)
					{
						captain_node->SetVisibility(ESlateVisibility::Visible);
					}
					if (player_info.db_id == GoalKickerPlayerID)
					{
						goalkicker_node->SetVisibility(ESlateVisibility::Visible);
					}
					if (player_info.db_id == PlayKickerPlayerID)
					{
						playkicker_node->SetVisibility(ESlateVisibility::Visible);
					}
				}

				if (player_info.is_temporary)
				{
					temp_player_node->SetVisibility(ESlateVisibility::Visible);
				}
			}
			else
			{
				warning_node->SetVisibility(ESlateVisibility::Visible);
			}

			UTextBlock* pNumberText = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(pChild, "TextNumber"));
			UTextBlock* pPositionText = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(pChild, "TextPosition"));
			UTextBlock* pSecondPositionText = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(pChild, "TextSecondPosition"));
			UTextBlock* pNameText = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(pChild, "TextName"));
			UTextBlock* pRatingText = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(pChild, "TextRating"));
			UTextBlock* pStatusText = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(pChild, "TextStatusText"));

			if (pNumberText && pPositionText && pSecondPositionText && pNameText && pRatingText && pStatusText)
			{
				pNumberText->SetText(FText::FromString(FString::FromInt(list_index + 1)));

				if (player_info.db_id != DB_INVALID_ID)
				{
					pSecondPositionText->SetVisibility(ESlateVisibility::Visible);
					pNameText->SetVisibility(ESlateVisibility::Visible);
					pRatingText->SetVisibility(ESlateVisibility::Visible);
					pStatusText->SetVisibility(ESlateVisibility::Visible);

					pPositionText->SetText(FText::FromString(UWWUITranslationManager::Translate(PlayerPositionEnum::GetPlayerPositionTextAbbreviated(player_info.primary_position))).ToUpper());
					pSecondPositionText->SetText(FText::FromString(UWWUITranslationManager::Translate(PlayerPositionEnum::GetPlayerPositionTextAbbreviated(player_info.secondary_position))).ToUpper());

					FString display_name;
#ifdef ENABLE_GAME_DEBUG_MENU
					if (SIFDebug::GetCareerModeDebugSettings()->GetDisplayIDs())
					{
						FString FirstName = SIFGameHelpers::GAConvertMabStringToFString(player_info.first_name);
						FString LastName = SIFGameHelpers::GAConvertMabStringToFString(player_info.last_name);

						
						if (!FirstName.IsEmpty())
						{
							display_name = FString::FromInt(player_info.db_id) + ", " + LastName + ", " + FirstName[0] + ".";
				}
						else
						{
							display_name = FString::FromInt(player_info.db_id) + ", " + LastName + ", " ;
						}

						
					}
					else
#endif
					{
						FString FirstName = SIFGameHelpers::GAConvertMabStringToFString(player_info.first_name);
						FString LastName = SIFGameHelpers::GAConvertMabStringToFString(player_info.last_name);

						if (!FirstName.IsEmpty())
						{
							display_name = LastName + ", " + FirstName[0] + ".";
						}
						else
						{
							display_name = LastName + ", ";
						}
					}

					REMOVE_UNUSED_CHARACTER(display_name);
					MabString displayName = SIFGameHelpers::GAConvertFStringToMabString(display_name);
					RUHUDUpdater::CensorPlayerName(-1, player_info.db_id, displayName);

					pNameText->SetText(SIFGameHelpers::GAConvertMabStringToFText(displayName).ToUpper());
					pRatingText->SetText(FText::FromString(FString::FromInt(player_info.overall_rating)));

					pStatusText->SetText(FText::FromString(UWWUITranslationManager::Translate(status)).ToUpper());
					pStatusText->SetVisibility(ESlateVisibility::Visible);
					
					//FLinearColor SelectedColour = FLinearColor(0.084376, 0.318547, 0.610496);

					//if (player_info.db_id == player_to_swap)
					//{
					//	SelectedColour = FLinearColor::White;
					//}

					//pNumberText->SetColorAndOpacity(SelectedColour);
					//pNameText->SetColorAndOpacity(SelectedColour);
					//pRatingText->SetColorAndOpacity(SelectedColour);
					//pStatusText->SetColorAndOpacity(SelectedColour);
					//pPositionText->SetColorAndOpacity(SelectedColour);
					//pSecondPositionText->SetColorAndOpacity(SelectedColour);
				}
				else
				{
					pPositionText->SetText(FText::FromString(UWWUITranslationManager::Translate("[ID_EMPTY_PLAYER_SLOT]").ToUpper()));
					pSecondPositionText->SetVisibility(ESlateVisibility::Hidden);
					pNameText->SetVisibility(ESlateVisibility::Hidden);
					pRatingText->SetVisibility(ESlateVisibility::Hidden);
					pStatusText->SetVisibility(ESlateVisibility::Hidden);
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIPopulatorCareerTeamSquad::RepopulatePlayerInfoByIds(UWidget* node, int player1_db_id, int player2_db_id)
{
	// Get starting index
	UScrollBox* listbox = Cast<UScrollBox>(node);

	if (!listbox)
	{
		return;
	}

	// Check all children
	for (int i = 0; i < listbox->GetChildrenCount(); ++i)
	{
		const PlayerInfo& player_info = OrderedPlayers[i];

		if (player_info.db_id != player1_db_id && player_info.db_id != player2_db_id)
			continue;

		// Repopulate
		UWidget* child = listbox->GetChildAt(i);
		MABASSERT(child);
		unsigned int list_index = (unsigned int)(i);
		PopulatePlayerInfo(player_info, child, list_index);
	}
}

//===============================================================================
//===============================================================================

UWWUIPopulatorCareerTeamSquad::CareerSquadDataFileCreationNodeCallback::CareerSquadDataFileCreationNodeCallback(UWidget* containerToPopulate, TArray<FWWUIScreenTemplateDataOption>& inDataOptions) :
	container(),
	dataOptions(inDataOptions),
	ItemIndex(0)
{
	container = Cast<UPanelWidget>(containerToPopulate);

	if (!container)
	{
		FString errorString = containerToPopulate != nullptr ? *containerToPopulate->GetPathName() : FString("NULL");
		//UE_LOG(LogWWUI, Error, TEXT("Cast to scroll box failed while attempting DataFileCreationNodeCallback on node %s"), *errorString);
	}
}

//===============================================================================
//===============================================================================

void UWWUIPopulatorCareerTeamSquad::CareerSquadDataFileCreationNodeCallback::Callback(UUserWidget* widget)
{
	container->AddChild(widget);
	ItemIndex++;
}
