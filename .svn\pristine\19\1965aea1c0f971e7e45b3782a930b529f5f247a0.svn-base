/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseDecideLineOutNumbers.h"

#include "Character/RugbyPlayerController.h"
#include "Match/HUD/RU3DHUDManager.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/Enums/RUPenaltyDecisionEnum.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUInputPhaseDecision.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/SIFGameWorld.h"

//#rc3_legacy_include #include "RUContextualHelper.h"

#include "Character/RugbyPlayerController.h"
#include "RugbyGameInstance.h"
#include "Match/Components/SSHumanPlayer.h"

MABRUNTIMETYPE_IMP1( RUGamePhaseDecideLineOutNumbers, RUGamePhaseHandler );

static const char* IMPACT_HUD_TEXT = "[ID_LINEOUT_SIGNALLED]";

RUGamePhaseDecideLineOutNumbers::RUGamePhaseDecideLineOutNumbers( SIFGameWorld *ggame )
: game( ggame )
, choosing_player( NULL )
{
}

RUGamePhaseDecideLineOutNumbers::~RUGamePhaseDecideLineOutNumbers()
{

}

void RUGamePhaseDecideLineOutNumbers::Enter()
{
	// WJS RLC ##### Should never get here on league game
	MABASSERT(0);

	RUTeam* restart_team = game->GetGameState()->GetPlayRestartTeam();
	choosing_player = restart_team->GetPlayKicker();

	// Must be a human (if available).
	if ( !choosing_player->GetHumanPlayer() )
	{
		if ( restart_team->GetNumHumanPlayers() != 0 )
		{
			SSHumanPlayer* human = restart_team->GetHumanPlayer( 0 );
			human->SetRugbyCharacter( choosing_player );
		}
	}

	game->GetGameState()->SetBallHolder( choosing_player );
	game->GetGameState()->SetAttackingTeam( restart_team );

	RUHUDUpdater* hud_updater = game->GetHUDUpdater();
	if ( hud_updater )
	{
		hud_updater->SetImpactText( IMPACT_HUD_TEXT );
	}

	game->GetRules()->EnableConsequences( true );

	RUDecisionConsequence	con_three_person_lineout;
	RUDecisionConsequence	con_five_person_lineout;
	RUDecisionConsequence	con_seven_person_lineout;

	// Kick for touch consequence.
	con_three_person_lineout.decision = RUC_SELECT_THREE_PLAYER_LINEOUT;
	con_three_person_lineout.restart_team = restart_team;
	con_three_person_lineout.restart_position = game->GetGameState()->GetPlayRestartPosition();
	con_three_person_lineout.restart_player = NULL;
	con_three_person_lineout.decision_event = NULL;

	con_five_person_lineout.decision = RUC_SELECT_FIVE_PLAYER_LINEOUT;
	con_five_person_lineout.restart_team = restart_team;
	con_five_person_lineout.restart_position = game->GetGameState()->GetPlayRestartPosition();
	con_five_person_lineout.restart_player = NULL;
	con_five_person_lineout.decision_event = NULL;

	con_seven_person_lineout.decision = RUC_SELECT_SEVEN_PLAYER_LINEOUT;
	con_seven_person_lineout.restart_team = restart_team;
	con_seven_person_lineout.restart_position = game->GetGameState()->GetPlayRestartPosition();
	con_seven_person_lineout.restart_player = NULL;
	con_seven_person_lineout.decision_event = NULL;

	RUInputPhaseDecision* decisions = game->GetInputManager()->GetDecisionInterface();
	decisions->SetDecisionMaker( choosing_player->GetHumanPlayer() );
	decisions->Cleanup();


	// Right
	// Bottom
	// Left
	// Nick WWS 7s to Womens 13s //
	/*
#ifdef ENABLE_SEVENS_MODE
	// Don't add an option for a seven man line out if we're playing a sevens game.
	// I dont think this is neccessary any more? Since we don't use concequences anymore
	if(!game->GetGameSettings().game_settings.GameModeIsR7())
		decisions->AddConsequence( con_seven_person_lineout );
#endif */

	decisions->AddConsequence( con_three_person_lineout );
	decisions->AddConsequence( con_five_person_lineout );

	decisions->SetRenderTillResolved( game->GetRules(), restart_team, false, false );

	// Dewald WW -
	// When SetRenderTillResolved gets run, it switches the game phase to RUGamePhaseDecision, this causes the current phase to exit resulting in consequences to be disabled.
	// Because of that, when we actually make a decision, it fails because we're not allowed. Re enable it here to make it work again
	game->GetRules()->EnableConsequences( true );

	// When we're playign a pro career, we need to double check that our pro is actually the decision maker
	if(SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro())
	{
		ARugbyCharacter* proPlayer = SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer();

		// Pro player doesn't exist, probably taken off field. Don't show decision numbers
		if( proPlayer == NULL )
			return;

		// If we're not the decision maker, then we shouldn't show the lineout numbers.
		if( choosing_player != proPlayer )
			return;
	}

	if ( hud_updater && restart_team && choosing_player )
	{
		int num_human_players_in_attacking_team = restart_team->GetNumHumanPlayers();
		for ( int i = 0; i < num_human_players_in_attacking_team; ++i )
		{
			// Check to make sure that the restart team has human players on this device.
			// Should prevent both consoles getting the lineout controls tooltip, and only show it to the relevant player.
			if ( restart_team->GetHumanPlayer( i ) && !restart_team->GetHumanPlayer( i )->IsNetworkPlayer() )
			{
				// Show the decide lineout numbers help text.
				hud_updater->SetDecideLineoutNumbersHelpTextVisible( true );
				break;
			}
		}
	}
}

void RUGamePhaseDecideLineOutNumbers::Exit()
{
	game->GetRules()->EnableConsequences( false );
}
