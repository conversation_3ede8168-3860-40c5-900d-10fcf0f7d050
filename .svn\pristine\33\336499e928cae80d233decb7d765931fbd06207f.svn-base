#include "SteamCheck.h"

//#ifdef wwSTEAM_ENABLED
//#if PLATFORM_WINDOWS && (WITH_EDITOR == 0)
//#define ENABLE_STEAMCHECK
#ifdef ENABLE_STEAMCHECK


//#include "base64.h" //base64 is now covered by FBase64
//#include "json/json_reader.h"
//#include "json/json_writer.h"
//#include "json/json_elements.h"
#include "MemCheck.h"

#include "Runtime/Core/Public/Misc/Base64.h"
//#include "Plugins/wwHttpModule/Source/wwHttpModule/Public/wwHttpService.h"
#include "wwHttpModule/Public/wwHttpService.h"

#include "UObject/NoExportTypes.h"
#include "Runtime/Online/HTTP/Public/Http.h"
#include "Json.h"
#include "JsonUtilities.h"

#include "OnlineSubsystemSteam.h"
//#include "OnlineAuthInterfaceSteam.h"
#include "OnlineEncryptedAppTicketInterfaceSteam.h"


SteamCheck::SteamCheck() :m_result(SCR_NONE), m_totalTime(0)
{

}

SteamCheck SteamCheck::g_SteamCheck;
//uint32 k_unSecretData = 0x5496; //rc3
//uint32 k_unSecretData = 0x2d16; //afl3
//uint32 k_unSecretData = 0x112f; //afle2
uint32 k_unSecretData = 0x1f2a; //rc4

void SteamCheck::ServerResponse(FHttpRequestPtr InRequest, FHttpResponsePtr InResponse, bool WasSuccessful)
{
	UE_LOG(LogTemp, Display, TEXT("SteamCheck::ServerResponse"));
	ServerResponseHandler(InRequest, InResponse, WasSuccessful);
}

void SteamCheck::ServerResponseHandler(FHttpRequestPtr InRequest, FHttpResponsePtr InResponse, bool WasSuccessful)
{
	UE_LOG(LogTemp, Display, TEXT("SteamCheck::ServerResponseHandler"));

	m_result = SCR_ERROR;


	// Application needs to know if request failed so we need to call the callback whether we have a valid response or not (or we need some other error handler)
	// eg InResponse will be null if we failed to connect to server
	// For now just force WasSuccessful to false if response not valid for any reason. Could pass through a more detailed error if required.

	//bool bIsValidResponse = IsValidResponse(InResponse, WasSuccessful);
	bool bIsValidResponse = false;
	if (InResponse.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("Response valid %d"), InResponse->GetResponseCode());

		if (EHttpResponseCodes::IsOk(InResponse->GetResponseCode()))
		{
			UE_LOG(LogTemp, Warning, TEXT("Response code ok %d"), InResponse->GetResponseCode());
			bIsValidResponse = true;
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Http Response returned error code: %d"), InResponse->GetResponseCode());
		UE_LOG(LogTemp, Warning, TEXT("Response Content = %s"), *InResponse->GetContentAsString());
	}

	// Not sure if WasSuccessful can be true if EHttpResponseCodes is other than OK
	if (!bIsValidResponse && WasSuccessful)
	{
		UE_LOG(LogTemp, Warning, TEXT("UWWHttpService::ResponseCallback : bIsValidResponse == true but WasSuccessful == false"));
		WasSuccessful = false;
	}

	//FString ResponseContentAsString = bIsValidResponse ? InResponse->GetContentAsString() : "";
	TArray<uint8> content;
	if (bIsValidResponse)
	{
		content = InResponse->GetContent();
	}

	if (!WasSuccessful)
	{
		m_result = SCR_ERROR_INVALIDSERVERRESPONSE;
		return;
	}

	FString resultStr;
	//FString encodedStr = m_asyncHttp.response.headers["data"];
	FString encodedStr = InResponse->GetHeader("data");

	std::string encodedStdString = TCHAR_TO_ANSI(*encodedStr);
	std::string decodedStdStr = base64_decode(encodedStdString);
	resultStr = decodedStdStr.c_str();


	bool moduleLoaded = MemCheck::instance().LoadModule((const unsigned char*)content.GetData(), content.Num());

	if (!moduleLoaded)
	{
		m_result = SCR_ERRORMOD;
		return;
	}

	if (resultStr.Len() == 0)
	{
		m_result = SCR_ERROR_INVALIDSERVERDATA;
		return;
	}

	//TSharedPtr<FJsonObject> json = GetJsonObjFromString(resultStr);
	TSharedPtr<FJsonObject> json;

	//Create a reader pointer to read the json data
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(resultStr);

	//Deserialize the json data given Reader and the actual object to deserialize
	if (!FJsonSerializer::Deserialize(Reader, json))
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to deserialize json data from string: %s"), *resultStr);
	}

	if (json.IsValid() && json->Values.Num() > 0)
	{
		//json::Object::iterator it = dom.Find("result");
		bool result = json->GetBoolField("result");
		if (result)
		{
			FString idStr = json->GetStringField("data");
			if (idStr.Len() > 0)
			{
				uint64 id = _atoi64(TCHAR_TO_ANSI(*idStr));
				id -= k_unSecretData;

				IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
				if (OnlineSub && OnlineSub->GetSubsystemName() == STEAM_SUBSYSTEM)
				{
					FOnlineSubsystemSteam* pOnlineSubSteam = static_cast<FOnlineSubsystemSteam*>(OnlineSub);
					if (pOnlineSubSteam && pOnlineSubSteam->GetSteamAppId() == id)
					{
						m_result = SCR_OK;
					}
				}
			}
		}
	}
	else
	{
		m_result = SCR_ERROR_SERVERDATACOULDNOTBEREAD;
	}
}

void SteamCheck::OnRequestEncryptedAppTicket(uint8* encryptedAppTicket, uint32 length)
{
	UE_LOG(LogTemp, Display, TEXT("SteamCheck::OnRequestEncryptedAppTicket"));
	RequestEncryptedAppTicketHandler(encryptedAppTicket, length);
}

void SteamCheck::RequestEncryptedAppTicketHandler(uint8* encryptedAppTicket, uint32 length)
{
	UE_LOG(LogTemp, Display, TEXT("SteamCheck::RequestEncryptedAppTicketHandler"));

	//unsigned char* pCharPointer = (unsigned char*)TCHAR_TO_ANSI(*encryptedAppTicket);
	//std::string dataStd = base64_encode(pCharPointer, encryptedAppTicket.Len());
	std::string dataStd = base64_encode((unsigned char*)(encryptedAppTicket), length);


	//wwTRACE("raw string = %s", TCHAR_TO_ANSI(*encryptedAppTicket));
	//wwTRACE("base64 string = %s", dataStd.c_str());
	FString data = dataStd.c_str();
	//FString data = FBase64::Encode(encryptedAppTicket);



	//const char* url = "http://192.168.0.183/www2/ba?game=afl3";
	//const char* url = http://www2.wwud.com.au/ba?game=afl3;


	TSharedRef<IHttpRequest, ESPMode::ThreadSafe> Request = FHttpModule::Get().CreateRequest();

	//Request->SetURL("https://www2.wwud.com.au/ca?game=rc4");			//Our WW test server. (actually a live server because evo 1 and rc3 use it)
	Request->SetURL("http://rc4.trublu.com.au/ca?game=rc4");			//The HES server.

	//SetRequestHeaders(Request);

	Request->SetHeader(TEXT("User-Agent"), TEXT("X-UnrealEngine-Agent"));
	Request->SetHeader(TEXT("Content-Type"), TEXT("application/x-www-form-urlencoded"));
	Request->SetHeader(TEXT("Accepts"), TEXT("application/json"));

	//Request->OnProcessRequestComplete().BindUObject(this, &UWWHttpService::ResponseCallback);
	Request->OnProcessRequestComplete().BindRaw(this, &SteamCheck::ServerResponse);
	Request->SetVerb(REQUEST_POST);
	Request->SetContentAsString(data);
	Request->ProcessRequest();
}



SteamCheck& SteamCheck::instance()
{
	return g_SteamCheck;
}

void SteamCheck::StatCheck()
{
	m_result = SCR_CHECKING;
	m_totalTime = 0;

	UE_LOG(LogTemp, Display, TEXT("SteamCheck::StatCheck"));
	/*if(SteamFriends()->GetPersonaState()==k_EPersonaStateOffline)
	{
		m_result = SCR_ERROR;
		return;
	}*/

	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		if (OnlineSub->GetSubsystemName() == STEAM_SUBSYSTEM)
		{
			FOnlineSubsystemSteam* pOnlineSubSteam = static_cast<FOnlineSubsystemSteam*>(OnlineSub);
			if (pOnlineSubSteam)
			{
				UE_LOG(LogTemp, Warning, TEXT("SteamCheck::StatCheck - Requesting encrypted ticket"));
				//SteamAPICall_t hSteamAPICall = SteamUser()->RequestEncryptedAppTicket( &k_unSecretData, sizeof( k_unSecretData ) );
				//m_SteamCallResultEncryptedAppTicket.Set( hSteamAPICall, this, &SteamCheck::OnRequestEncryptedAppTicket );
				/*
				FOnlineEncryptedAppTicketSteam* ticketSteam = pOnlineSubSteam->GetEncryptedAppTicketInterface().Get();

				ticketSteam->OnEncryptedAppTicketResultDelegate.AddRaw(this, &SteamCheck::OnRequestEncryptedAppTicket);
				ticketSteam->RequestEncryptedAppTicket(&k_unSecretData, sizeof(k_unSecretData));
				*/
				//pOnlineSubSteam->OnEncryptedAppTicketDelegate.BindRaw(this, &SteamCheck::OnRequestEncryptedAppTicket);
				//pOnlineSubSteam->RequestEncryptedAppTicket(&k_unSecretData, sizeof(k_unSecretData));

				//FOnlineAuthSteamPtr testPtr = pOnlineSubSteam->GetAuthInterface();
				//testPtr->RequestEncryptedAppTicket(&k_unSecretData, sizeof(k_unSecretData));


				//OnlineSub->GetIdentityInterface()->GetAuthToken(0);

				m_result = SCR_OK; // GGS Nick Temp Bypass Server Check

				return;
			}
		}
	}

	m_result = SCR_ERROR_NOSUBSYSTEM;

}

SteamCheck::SteamCheckResult SteamCheck::GetResult()
{
	return m_result;
}

void SteamCheck::ResetResult()
{
	m_result = SCR_NONE;
}

void SteamCheck::Update(int deltaMs)
{
	if (m_result == SCR_CHECKING)
	{
		UE_LOG(LogTemp, Display, TEXT("SteamCheck::Update checking"));
		enum { TIMEOUT = 10 * 1000 };
		m_totalTime += deltaMs;
		if (m_totalTime > TIMEOUT)
		{
			m_result = SCR_ERROR_TIMEOUT;
			return;
		}

		//m_asyncHttp.Update();
	}

}

#endif

