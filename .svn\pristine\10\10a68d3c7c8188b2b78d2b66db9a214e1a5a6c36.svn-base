/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/HUD/RUHUDUpdater.h"

#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DBCompetitionDefinition.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DatabaseTypes.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Statistics/RUStatisticsSystem.h"
#include "Match/SIFGameContext.h"
#include "Match/SIFGameWorld.h"
#include "Match/SIFUIConstants.h"
#include "Utility/Helpers/SIFGameHelpers.h"
#include "Utility/Helpers/SIFUIHelpers.h"
#include "Utility/Helpers/SIFAudioHelpers.h"
#include "Utility/Helpers/SIFUIEffectsHelpers.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Image.h"
#include "RugbyGameInstance.h"


//< UI Includes >
#include "UI/GeneratedHeaders/WWUIScreenInGameHud_UI_Namespace.h"
#include "UI/GeneratedHeaders/Animations_UI_Namespace.h"
#include "UI/Screens/WWUIScreenInGameHUD.h"
#include "WWUITranslationManager.h"
#include "WWUIFunctionLibrary.h"
#include "Kismet/KismetTextLibrary.h"
#include "UserWidget.h"
#include "TextBlock.h"
#include "PanelWidget.h"
#include "Border.h"
#include "HorizontalBox.h"
#include "TimerManager.h"
#include "ScaleBox.h"

//#include "RUAudio.h"
//#include "RUGameDatabaseManager.h"
//#include "RUGamePhaseScrum.h"
//#include "RUHumanPlayerColours.h"
//#include "RUPlayerAttributes.h"
//#include "RUPlayerPositionEnum.h"
#include "Match/AI/Roles/Competitors/RURoleKickOffKicker.h"
#include "Utility/Helpers/SIFInGameHelpers.h"
#include "RUSubstitutionManager_echar.h"
#include "CanvasPanel.h"
//#include "RUSubstitutionManager_echar.h"
//#include "SIFDebug.h"
//#include "SIFAudioHelpers.h"
//#include "SIFGameHelpers.h"
//#include "SIFInGameHelpers.h"
//#include "SIFInputConstants.h"
#include "Utility/Helpers/SIFMatchmakingHelpers.h"
#include "WWUIUserWidget.h"
#include "WWUIFixedMinimap.h"
#include "Match/SSTeam.h"
#include "Databases/RUGameDatabaseManager.h"
#include "Utility/TransformUtility.h"
#include "Character/RugbyPlayerController.h"
#include "RUHumanPlayerColours.h"
#include "Match/Ball/SSBall.h"
#include "WidgetLayoutLibrary.h"
#include "WWUIRichTextBlockWithTranslate.h"
#include "OverlaySlot.h"
#include "Overlay.h"
#include "WidgetTree.h"
#include "WWUITextBlock.h"
#include "UI/WWUIContextOptionContainer.h"
#include "UI/Components/WWUICountdownClock.h"
#include "InvalidationBox.h"
#include "Kismet/GameplayStatics.h"
#include "CanvasPanelSlot.h"
#include "Kismet/KismetMathLibrary.h"
#include "../AI/Roles/Competitors/RURoleShootForGoal.h"
//#include "SIFUIHelpers.h"
//#include "SSGameTimer.h"
//#include "Character/RugbyPlayerController.h"
//#include "SSMath.h"
//#include <MabCamera.h>
//#include <MabCentralInstanceDatabase.h>
//#include <MabCentralTypeDatabase.h>
//#include <MabLuaAutoBinder.h>
//#include <MabObjectResource.h>

//#include "RUUIContextOptionContainer.h"
//#include "RUUIPlayerFaceImage.h"
//#include "RUUIStreamingImage.h"
//#include "SIFUIRadialSliceImage.h"
//#include "SIFWindowSystem.h"
//#include <MabUIAnimationManager.h>
//#include <MabUIImage.h>
//#include <MabUIManager.h>
//#include <MabUITextInterface.h>

#if (PLATFORM_WINDOWS)
//#include <MabXInputGameController.h>
#include "Mab/Utility/MabTranslationManager.h"
//#include "SIFWindowSystem.h"

#elif PLATFORM_PS4
//#include "RugbyGameInstance.h"
#include "Utility/Helpers/SIFGeneralHelpersPS4.h"
//#include "ps4/MabPS4Controller.h"
#include "Mab/Utility/MabTranslationManager.h"
#endif

/*//#rc3_legacy
#if (PLATFORM_XBOXONE)
#include "XboxOneMabMatchMaking.h"
#include "Mab/Utility/MabTranslationManager.h"
#endif*/

// Context paths
const char RU_GAMEPLAY_HUD[] = "GameplayHUD";
const char RU_HUD_BOX[] = "HUDBox";
const char RU_HUD_TIMER[] = "Timer";
const char RU_HUD_TIMER_MESSAGE[] = "TimerMessage";
const char RU_HUD_TEAM0[] = "Team0";
const char RU_HUD_TEAM1[] = "Team1";
const char RU_HUD_MESSAGE[] = "Message";
const char RU_HUD_IMPACT_TEXT[] = "ImpactText";
const char RU_HUD_PRO_REQUEST_FEEDBACK_TEXT[] = "ProRequestFeedback";
const char RU_HUD_PRO_GOAL_FEEDBACK_TEXT[] = "ProGoalUpdate";
const char RU_HUD_CARDS[] = "Cards";
const char RU_HUD_PAUSE_DISABLED[] = "pause_disabled_icon";

const char RU_CENSORED_MNEMONIC[] = "***";

const char RU_HUD_PLAYER_INFO[] = "PlayerInfo";
const char RU_HUD_TEAM_INFO[] = "TeamInfo";
const char RU_HUD_SUBSTITUTION_INFO[] = "MatchInfo/SubstitutionInfo";

const char RU_TM_PLAYER_DETAIL[] = "RootMenuWindow/TeamLineup/InterchangeTab/PlayerDetail/PlayerLeft";
const char RU_HUD_KICK_FOR_POINTS[] = "GameplayHUD/KickForPointHelp";
const char RU_HUD_PLACEHOLDER_TEXT[] = "PlaceholderText";
const char RU_HUD_SPEAKING_TEXT[] = "Speaking";
const char RU_HUD_REPLAY_TEXT[] = "ReplayText";
const char RU_HUD_REPLAY_BOX[] = "ReplayBox";

const char RU_SCRUM_HUD[] = "ScrumHUD";
const char RU_SCRUM_HELP_TEXT[] = "ScrumHelpText";
const char RU_SCRUM_ENGAGE_TEXT[] = "EngageSequenceText";

const char RU_HUD_HELPTEXT[] = "GameplayHUD/HelpButtonTemplate";

const char RU_CONTEXT_OPTION_ATTACK[] = "GameplayHUD/ContextOptionAttack";
const char RU_CONTEXT_OPTION_DEFEND[] = "GameplayHUD/ContextOptionDefense";

const FString RU_SUBSTITUTION_INDEX = "substitution_index";


const FString RU_IMPACT_ANIMATION_SLIDE_IN_FADE = "impact_text_animation";
const char RU_PRO_REQUEST_FEEDBACK_ANIMATION_SLIDE_IN_FADE[] = "pro_request_feedback_text_animation";
const char RU_PRO_GOAL_FEEDBACK_ANIMATION_SLIDE_IN_FADE[] = "pro_goal_feedback_text_animation";


const FString RU_IMPACT_ANIMATION_SLIDE_IN = "impact_text_persistent_animation";
const char RU_PRO_REQUEST_FEEDBACK_ANIMATION_SLIDE_IN[] = "pro_request_feedback_text_persistent_animation";
const char RU_PRO_GOAL_FEEDBACK_ANIMATION_SLIDE_IN[] = "pro_goal_feedback_text_persistent_animation";

const char RU_HUD_OFFSCREEN_MARKER_SPRINTF[] = "RootMenuWindow/GameWindow/PlayerOffscreenMarkers/marker_%d";
const char RU_HUD_OFFSCREEN_MARKER_OFFSIDE[] = "offside";
const char RU_HUD_OFFSCREEN_MARKER_METERS[] = "meters";

const char RU_HUD_NETWORK_LONGSTALL[] = "LongStall";
const char RU_HUD_NETWORK_SHORTSTALL[] = "ShortStall";

const char RU_QUICK_LINEOUT_HELP_TEXT[] = "RootMenuWindow/GameWindow/QuickLineoutHelp";
const char RU_DECIDE_LINEOUT_NUMBERS_HELP_TEXT[] = "RootMenuWindow/GameWindow/LineoutDecideNumbersHelp";

const FColor RU_HUD_COUNTDOWNCLOCK_GRAPHBACKGROUND_COLOUR = FLinearColor(0.4f, 0.4f, 0.4f, 0.3f).ToFColor(false);
const FColor RU_HUD_COUNTDOWNCLOCK_GRAPH_COLOUR = FLinearColor(0.9f, 0.9f, 0.9f, 0.7f).ToFColor(false);

const MabColour RU_GENERIC_INFO_DEFAULT_EDGE_COLOUR(0.9f, 0.9f, 0.9f, 0.7f);


const FLinearColor COLOR_TIMER_WHITE = FLinearColor(1, 1, 1);
const FLinearColor COLOR_TIMER_ORANGE = FLinearColor(0.984375f, 0.157234f, 0.056397f);

const FColor PRIMARY_ON_COLOUR = FColor(2, 54, 118);
const FColor PRIMARY_OFF_COLOUR = FColor(159, 16, 4);
const FColor SECONDARY_ON_COLOUR = FColor(0, 180, 255);
const FColor SECONDARY_OFF_COLOUR = FColor(240, 79, 47);
const FColor TEXT_ON_COLOUR = FColor(0, 180, 255);
const FColor TEXT_OFF_COLOUR = FColor(240, 79, 47);

const char RU_HUD_PlayerInfo[] = "PlayerInfo";

const float TRY_HUD_DISPLAY_DELAY = 3.4f;

const float PAUSE_DISABLED_ICON_FADE_TIME = 1.0f;

const float PAUSE_DISABLED_ONLINE_TIME = 3.0f;

/// Widget offsets
//const int HUD_TIMER_TEXT = 0;
const char HUD_TIMER_TEXT[] = "TimerText";


enum HUD_PLAYER_WIDGETS {
	HUD_TEAM_NAME_TEXT,
	HUD_TEAM_COLOUR,
	HUD_TEAM_SCORE_HIGHLIGHT,
	HUD_TEAM_SCORE_TEXT,
	HUD_TEAM_SCORE_GLOW
};
enum HUD_MESSAGE_WIDGETS {
	HUD_MESSAGE_BACKGROUND = 0,
	HUD_MESSAGE_TITLE_TEXT,
	HUD_MESSAGE_MAIN_TEXT
};

enum TEAM_MANAGEMENT_WIDGETS {
	TM_PLAYER_PORTRAIT,
	TM_PLAYER_NUMBER,
	TM_PLAYER_NAME
};

enum PLAYER_DETAIL_WIDGETS {
	PLAYER_DETAIL_PORTRAIT,
	PLAYER_DETAIL_NAME_BG,
	PLAYER_DETAIL_NAME
};

enum PLAYER_STATS_WIDGETS {
	PLAYER_STATS_STATIC_POSITION,
	PLAYER_STATS_STATIC_SPECIALTIES,
	PLAYER_STATS_POSITION_1_RATING_BG,
	PLAYER_STATS_POSITION_1_RATING,
	PLAYER_STATS_POSITION_1,
	PLAYER_STATS_POSITION_2_RATING_BG,
	PLAYER_STATS_POSITION_2_RATING,
	PLAYER_STATS_POSITION_2,
	PLAYER_STATS_POSITION_3_RATING_BG,
	PLAYER_STATS_POSITION_3_RATING,
	PLAYER_STATS_POSITION_3,
	PLAYER_STATS_SPECIALTIES_1,
	PLAYER_STATS_SPECIALTIES_1_BAR_BG,
	PLAYER_STATS_SPECIALTIES_1_BAR,
	PLAYER_STATS_SPECIALTIES_1_RATING,
	PLAYER_STATS_SPECIALTIES_2,
	PLAYER_STATS_SPECIALTIES_2_BAR_BG,
	PLAYER_STATS_SPECIALTIES_2_BAR,
	PLAYER_STATS_SPECIALTIES_2_RATING,
	PLAYER_STATS_SPECIALTIES_3,
	PLAYER_STATS_SPECIALTIES_3_BAR_BG,
	PLAYER_STATS_SPECIALTIES_3_BAR,
	PLAYER_STATS_SPECIALTIES_3_RATING
};

enum PLAYER_STATUS_WIDGETS {
	PLAYER_STATUS_BG,
	PLAYER_STATUS_TEXT
};

enum SCRUM_TIMER_WIDGETS {
	SCRUM_TIMER_MARKER,
	SCRUM_TIMER_FEEDBACK
};

enum SCRUM_ACCURACY_WIDGETS {
	SCRUM_ACCURACY_LEFT,
	SCRUM_ACCURACY_RIGHT,
	SCRUM_ACCURACY_FEEDBACK,
	SCRUM_ACCURACY_MARKERS
};

enum SCRUM_ACCURACY_MARKER_WIDGETS {
	SCRUM_ACCURACY_MARKER1,
	SCRUM_ACCURACY_MARKER2,
	SCRUM_ACCURACY_MARKER3,
	SCRUM_ACCURACY_MARKER4
};

//#rc3_legacy static const MabColour PLAYER_COLOURS[SIF_MAX_CONTROLLERS] =
static const MabColour PLAYER_COLOURS[4] =
{
	MabColour(1.0f, 1.0f, 0.0f, 1.0f),
	MabColour(0.0f, 1.0f, 1.0f, 1.0f),
	MabColour(1.0f, 0.0f, 1.0f, 1.0f),
	MabColour(1.0f, 1.0f, 0.5f, 1.0f)
};

// Fast Sine
// http://www.devmaster.net/forums/showthread.php?t=5784
template <typename T/*, bool accurate*/>
static T FastSin(const T theta)
{
	const T B = 1.2732395447351626861510701069801f; // 4 / PI
	const T C = -0.40528473456935108577551785283891f; // -4 / (PI^2)

	T y = B * theta + C * theta * MabMath::Abs(theta);

	/*if (accurate)*/
	{
		const T P = 0.225f;

		y = P * (y * MabMath::Abs(y) - y) + y;
	}

	return y;
}

// Fast sin interpolate - for any pulsing of UI widgets
MabReal FastSinInterpolate(MabReal current_time, MabReal frequency, MabReal minimum, MabReal range)
{
	MabReal theta = (2 * PI) * current_time; // frequency of 1
	theta = theta * (1 / frequency); // apply frequency of pulse period

	// range reduce theta for FastSin
	theta = -((theta - MabMath::Floor(theta / 6.283185307179586476925286766559f)  * 6.283185307179586476925286766559f) - PI);

	MabReal alpha = 0.5f * FastSin<MabReal/*, true*/>(theta) + 0.5f; // fast Sine, between 0 and 1
	alpha = range * alpha + minimum;

	return alpha;
}

MABRUNTIMETYPE_IMP1(RUHUDUpdater, RUHUDUpdaterBase)

RUHUDUpdater::RUHUDUpdater(const SIFGameContext& context, const char* hud_context) :
	window_context(hud_context),
	game_context(&context),
	gameWindow(NULL),
	hud_box(NULL),
	pro_request_feedback(NULL),
	pro_goal_feedback(NULL),
	pro_request_feedback_animation(),
	pro_goal_feedback_text_animation(),
	team_info(NULL),
	player_info(NULL),
	substitution_info(NULL),
	scoreboard(NULL),
	impact_text(NULL),
	impact_text_animation(),
	timer(NULL),
	timer_message(NULL),
	timer_colour(1.0f, 1.0f, 1.0f),
	red_card_num_home(0),
	red_card_num_away(0),
	minimap_node(NULL),
	lineout_active(false),
	half_or_full_time(false),
	player_display_info(NULL),
	player_info_display(),
	gameplay_hud(NULL),
	hud_footer(NULL),
	quick_lineout_help_text(NULL),
	decide_lineout_numbers_help_text(NULL),
	in_game_help_text(NULL),
	scrum_help_text(NULL),
	minimap_target_opacity(0.0f),
	game_window_invalidation_box(NULL),
	timer_invalidation_box(NULL),
	timer_message_invalidation_box(NULL),
	player_info_invalidation_box(NULL),

	
	context_container_primary(NULL),
	context_container_secondary(NULL),
	screen_messages(),
	screen_message_idx(-1),
	offscreen_ball_marker(NULL),
	pause_disabled_icon(NULL),
	pause_disabled_icon_fade_time(0.0f),
	pause_disabled_online_time(0.0f),
#if defined(RC4_ENABLE_HUD_UPDATER)
	message(NULL),
	impact_text(NULL),
	pro_request_feedback(0),
	pro_goal_feedback_text(0),
	player_info(0),
	team_info(0),
	substitution_info(0),
	player_display_info(0),
	team_management(0),
	player_detail(NULL),
	kick_for_points_help(NULL),
	in_game_help_text(NULL),
	placeholder_text(NULL),
	speaking_text(NULL),
	scrum_hud(NULL),
	team0_scrum_timer(NULL),
	team1_scrum_timer(NULL),
	team0_scrum_feedback(NULL),
	team1_scrum_feedback(NULL),
	network_shortstall(NULL),
	network_longstall(NULL),
#endif // defined(RC4_ENABLE_HUD_UPDATER)
	countdown_clock(NULL),
	countdown_clock_spacer(NULL),
	replay_box(NULL),
	impact_text_resized(false),
	pro_request_feedback_resized(false),
	pro_goal_feedback_text_resized(false),
	team_info_resized(false),
	player_info_resized(false),
	substitution_info_resized(false),
	cutscene_skip_text_shown(false),
	online_cutscene_wait_text_shown(false),
	requested_info_finish(false),
	deferred_player_infos()
{

	Initialise();
}

RUHUDUpdater::~RUHUDUpdater()
{

}

void RUHUDUpdater::AttachMonitors()
{
	// The HUD cares about when a replay starts and stops, so it's important to add some listeners for these events.
	MABASSERT(game_context);
	if (game_context)
	{
		SIFGameWorld* game_world = game_context->GetGameWorld();
		MABASSERT(game_world);
		if (game_world != NULL)
		{
			RUGameEvents* events = game_world->GetEvents();
			events->kick_restart_start.Add(this, &RUHUDUpdater::OnKickOff);

			events->ball_holder_changed.Add(this, &RUHUDUpdater::BallHolderChanged);
			events->commentary_replay_started.Add(this, &RUHUDUpdater::OnReplayStarted);
			events->replay_playback_finish.Add(this, &RUHUDUpdater::OnReplayStopped);
			events->perform_interchange.Add(this, &RUHUDUpdater::OnPerformInterchange);
			events->try_result.Add(this, &RUHUDUpdater::OnTryResult);
			events->penalty_goal_finish.Add(this, &RUHUDUpdater::OnPenaltyGoal);
			events->penalty_goal_decision.Add(this, &RUHUDUpdater::OnPenaltyGoalDecision);

			events->half_time.Add(this, &RUHUDUpdater::OnHalfTime);
			events->full_time.Add(this, &RUHUDUpdater::OnFullTime);

			events->knock_on.Add(this, &RUHUDUpdater::OnKnockOn);
			events->kick_at_posts_ready.Add(this, &RUHUDUpdater::OnConversionStart);
			events->conversion_finish.Add(this, &RUHUDUpdater::OnConversionFinish);
			events->drop_goal.Add(this, &RUHUDUpdater::OnDropGoal);
			events->scrum_reset.Add(this, &RUHUDUpdater::ScrumReset);
			events->advantage_to.Add(this, &RUHUDUpdater::Advantage);
			events->advantage_ended.Add(this, &RUHUDUpdater::AdvantageEnded);
			events->lineout_signalled.Add(this, &RUHUDUpdater::LineoutSignalled);
			events->touch_scrum_signalled.Add(this, &RUHUDUpdater::TouchScrumSignalled);
			events->lineout_throw.Add(this, &RUHUDUpdater::LineoutThrow);
			events->lineout_throw_fault_nrc.Add(this, &RUHUDUpdater::LineoutThrowNRCFault);
			events->lineout_time_out.Add(this, &RUHUDUpdater::LineoutTimeout);
			events->lineout_finished.Add(this, &RUHUDUpdater::LineoutFinished);
			events->forward_pass.Add(this, &RUHUDUpdater::ForwardPass);
			events->breakdown_should_release.Add(this, &RUHUDUpdater::BreakdownShouldRelease);
			events->breakdown_end.Add(this, &RUHUDUpdater::BreakdownEnd);
			events->ruck_ball_released.Add(this, &RUHUDUpdater::RuckBallReleased);

			events->restart_kickoff_not_10m.Add(this, &RUHUDUpdater::RestartKickOffNotTen);
			events->restart_out_on_full.Add(this, &RUHUDUpdater::RestartKickOffDirectlyIntoTouch);
			events->restart_kickoff_into_ingoal.Add(this, &RUHUDUpdater::RestartKickOffIntoInGoal);

#ifdef ENABLE_SEVENS_MODE
			events->restart_kickoff_offside.Add(this, &RUHUDUpdater::RestartKickOffOffside);
#endif

			events->free_kick_awarded.Add(this, &RUHUDUpdater::NotifyFreeKickAwarded);
			events->notify_pro_goal_increment.Add(this, &RUHUDUpdater::NotifyProGoalIncrement);

			events->tackle_count_changed.Add(this, &RUHUDUpdater::OnTackleCountChanged);
		}
	}
}

void RUHUDUpdater::DetachMonitors()
{
	// Remove event listeners.
	SIFGameWorld* game_world = game_context->GetGameWorld();
	if (game_world != NULL)
	{
		RUGameEvents* events = game_world->GetEvents();
		events->kick_restart_start.Remove(			this, &RUHUDUpdater::OnKickOff);
		events->ball_holder_changed.Remove(			this, &RUHUDUpdater::BallHolderChanged);
		events->commentary_replay_started.Remove(	this, &RUHUDUpdater::OnReplayStarted);
		events->replay_playback_finish.Remove(		this, &RUHUDUpdater::OnReplayStopped);
		events->perform_interchange.Remove(			this, &RUHUDUpdater::OnPerformInterchange);
		events->try_result.Remove(					this, &RUHUDUpdater::OnTryResult);
		events->penalty_goal_finish.Remove(			this, &RUHUDUpdater::OnPenaltyGoal);
		events->penalty_goal_decision.Remove(		this, &RUHUDUpdater::OnPenaltyGoalDecision);

		events->half_time.Remove(					this, &RUHUDUpdater::OnHalfTime);
		events->full_time.Remove(					this, &RUHUDUpdater::OnFullTime);

		events->knock_on.Remove(					this, &RUHUDUpdater::OnKnockOn);
		events->kick_at_posts_ready.Remove(			this, &RUHUDUpdater::OnConversionStart);
		events->conversion_finish.Remove(			this, &RUHUDUpdater::OnConversionFinish);
		events->drop_goal.Remove(					this, &RUHUDUpdater::OnDropGoal);
		events->scrum_reset.Remove(					this, &RUHUDUpdater::ScrumReset);
		events->advantage_to.Remove(				this, &RUHUDUpdater::Advantage);
		events->advantage_ended.Remove(				this, &RUHUDUpdater::AdvantageEnded);
		events->lineout_signalled.Remove(			this, &RUHUDUpdater::LineoutSignalled);
		events->lineout_throw.Remove(				this, &RUHUDUpdater::LineoutThrow);
		events->lineout_throw_fault_nrc.Remove(		this, &RUHUDUpdater::LineoutThrowNRCFault);
		events->lineout_time_out.Remove(			this, &RUHUDUpdater::LineoutTimeout);
		events->forward_pass.Remove(				this, &RUHUDUpdater::ForwardPass);
		events->breakdown_should_release.Remove(	this, &RUHUDUpdater::BreakdownShouldRelease);
		events->breakdown_end.Remove(				this, &RUHUDUpdater::BreakdownEnd);
		events->ruck_ball_released.Remove(			this, &RUHUDUpdater::RuckBallReleased);

		events->restart_kickoff_not_10m.Remove(		this, &RUHUDUpdater::RestartKickOffNotTen);
		events->restart_out_on_full.Remove(			this, &RUHUDUpdater::RestartKickOffDirectlyIntoTouch);
		events->restart_kickoff_into_ingoal.Remove(	this, &RUHUDUpdater::RestartKickOffIntoInGoal);

#ifdef ENABLE_SEVENS_MODE
		events->restart_kickoff_offside.Remove(		this, &RUHUDUpdater::RestartKickOffOffside);
#endif

		events->free_kick_awarded.Remove(			this, &RUHUDUpdater::NotifyFreeKickAwarded);
		events->notify_pro_goal_increment.Remove(	this, &RUHUDUpdater::NotifyProGoalIncrement);

		events->tackle_count_changed.Remove(		this, &RUHUDUpdater::OnTackleCountChanged);
	}
}

void RUHUDUpdater::Initialise()
{
	gameInstance = SIFApplication::GetApplication();
	if (!gameInstance) return;

	gameWindow = Cast<UWWUIScreenInGameHUD>(gameInstance->GetCurrentScreenTemplate());
	if (!gameWindow) return;

	lineout_active = false;
	half_or_full_time = false;

	//< Match Info UI >==================================================================================================================
	UUserWidget* match_info;
	match_info			= Cast<UUserWidget>(	UWWUIFunctionLibrary::FindChildWidget(gameWindow, WWUIScreenInGameHud_UI::MatchInfo));
	team_info			= Cast<UWWUIUserWidget>(UWWUIFunctionLibrary::FindChildWidget(match_info, WWUIScreenInGameHud_UI::TeamInfo));
	player_info			= Cast<UWWUIUserWidget>(UWWUIFunctionLibrary::FindChildWidget(match_info, WWUIScreenInGameHud_UI::PlayerInfo));
	substitution_info	= Cast<UWWUIUserWidget>(UWWUIFunctionLibrary::FindChildWidget(match_info, WWUIScreenInGameHud_UI::SubstitutionInfo));


	//< HUD Sections. >==================================================================================================================
	gameplay_hud					= Cast<UWWUIUserWidget>(UWWUIFunctionLibrary::FindChildWidget(gameWindow, WWUIScreenInGameHud_UI::BP_GameplayHUD));
	game_window_invalidation_box	= Cast<UInvalidationBox>(UWWUIFunctionLibrary::FindChildWidget(gameWindow, WWUIScreenInGameHud_UI::HUDBodyInvalidationBox));


	//< HUD Box >========================================================================================================================
	hud_box					= Cast<UWWUIUserWidget>(UWWUIFunctionLibrary::FindChildWidget(gameWindow,	WWUIScreenInGameHud_UI::BP_HUD_HUDBox));
	pro_request_feedback	= Cast<UWWUIUserWidget>(UWWUIFunctionLibrary::FindChildWidget(hud_box, WWUIScreenInGameHud_UI::BP_ProRequestFeedback));
	pro_goal_feedback		= Cast<UWWUIUserWidget>(UWWUIFunctionLibrary::FindChildWidget(hud_box, WWUIScreenInGameHud_UI::BP_ProGoalUpdate));

	//< Contextual UI. >=================================================================================================================
	context_container_primary	= Cast<UWWUIContextOptionContainer>(UWWUIFunctionLibrary::FindChildWidget(gameplay_hud, WWUIScreenInGameHud_UI::ContextOptionAttack));
	context_container_secondary = Cast<UWWUIContextOptionContainer>(UWWUIFunctionLibrary::FindChildWidget(gameplay_hud, WWUIScreenInGameHud_UI::ContextOptionDefense));
	UWWUIFunctionLibrary::SetVisibility(context_container_primary,		false);
	UWWUIFunctionLibrary::SetVisibility(context_container_secondary,	false);


	//< Load Scoreboard Widgets >========================================================================================================
	scoreboard		= Cast<UWWUIUserWidget>(	UWWUIFunctionLibrary::FindChildWidget(hud_box,		WWUIScreenInGameHud_UI::BP_HUD_GameInfo));
	timer			= Cast<UTextBlock>(			UWWUIFunctionLibrary::FindChildWidget(scoreboard,	WWUIScreenInGameHud_UI::TimeText));
	timer_message	= Cast<UBorder>(			UWWUIFunctionLibrary::FindChildWidget(scoreboard,	WWUIScreenInGameHud_UI::BorderExtendedTime));
	impact_text		= Cast<UHorizontalBox>(		UWWUIFunctionLibrary::FindChildWidget(scoreboard,	WWUIScreenInGameHud_UI::ImpactText));
	timer_invalidation_box			= Cast<UInvalidationBox>(	UWWUIFunctionLibrary::FindChildWidget(scoreboard,	WWUIScreenInGameHud_UI::TimeTextInvalidationBox));
	timer_message_invalidation_box	= Cast<UInvalidationBox>(	UWWUIFunctionLibrary::FindChildWidget(scoreboard,	WWUIScreenInGameHud_UI::TimerMessageInvalidationBox));

	UWWUIFunctionLibrary::SetVisibility(scoreboard, ESlateVisibility::Collapsed);

	//< HELP TIP TEXT >==================================================================================================================
	quick_lineout_help_text				= Cast<UWWUIRichTextBlockWithTranslate>(	UWWUIFunctionLibrary::FindChildWidget(gameWindow,	WWUIScreenInGameHud_UI::QuickLineoutHelpText));
	decide_lineout_numbers_help_text	= Cast<UWWUITextBlock>(	UWWUIFunctionLibrary::FindChildWidget(gameWindow,	WWUIScreenInGameHud_UI::LineoutDecideNumberHelpText));
	hud_footer							= Cast<UUserWidget>(	UWWUIFunctionLibrary::FindChildWidget(gameplay_hud,	WWUIScreenInGameHud_UI::BP_FooterDefault));
	scrum_help_text						= Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindChildWidget(gameWindow, WWUIScreenInGameHud_UI::ScrumHelpText));
	in_game_help_text					= Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindChildWidget(hud_footer, WWUIScreenInGameHud_UI::HelpTip));
	conversion_place_help_text			= Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindChildWidget(gameWindow, WWUIScreenInGameHud_UI::ConversionPlace));
	conversion_aim_help_text			= Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindChildWidget(gameWindow, WWUIScreenInGameHud_UI::ConversionAim));
	conversion_accuracy_help_text		= Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindChildWidget(gameWindow, WWUIScreenInGameHud_UI::ConversionAccuracy));
	conversion_power_help_text			= Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindChildWidget(gameWindow, WWUIScreenInGameHud_UI::ConversionPower));

	UWWUIFunctionLibrary::SetVisibility(quick_lineout_help_text,			ESlateVisibility::Collapsed);
	UWWUIFunctionLibrary::SetVisibility(decide_lineout_numbers_help_text,	ESlateVisibility::Collapsed);
	UWWUIFunctionLibrary::SetVisibility(scrum_help_text,					ESlateVisibility::Collapsed);
	UWWUIFunctionLibrary::SetVisibility(conversion_place_help_text,			ESlateVisibility::Collapsed);
	UWWUIFunctionLibrary::SetVisibility(conversion_aim_help_text,			ESlateVisibility::Collapsed);
	UWWUIFunctionLibrary::SetVisibility(conversion_accuracy_help_text,		ESlateVisibility::Collapsed);
	UWWUIFunctionLibrary::SetVisibility(conversion_power_help_text,			ESlateVisibility::Collapsed);

	SetupMinimap();

	//< COUNTDOWN CLOCK >================================================================================================================
	countdown_clock = Cast<UWWUICountdownClock>(UWWUIFunctionLibrary::FindChildWidget(hud_box, WWUIScreenInGameHud_UI::CountdownClock));
	if (countdown_clock)
	{
		//< Colour the clock parts to a neutral color. >
		countdown_clock->SetFillColor(RU_HUD_COUNTDOWNCLOCK_GRAPH_COLOUR);
		countdown_clock->SetBackgroundColor(RU_HUD_COUNTDOWNCLOCK_GRAPHBACKGROUND_COLOUR);

		//< Start off hidden. >
		countdown_clock->SetVisibility(ESlateVisibility::Hidden);
		countdown_clock->SetTimeVisibility(false);
	}

	countdown_clock_spacer = UWWUIFunctionLibrary::FindChildWidget(hud_box, WWUIScreenInGameHud_UI::CountdownClockSpacer);
	if (countdown_clock_spacer)
	{
		if (SIFApplication::GetApplication()->GetMatchGameSettings() && SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.network_game)
		{
			countdown_clock_spacer->SetVisibility(ESlateVisibility::Visible);
		}
		else
		{
			countdown_clock_spacer->SetVisibility(ESlateVisibility::Collapsed);
		}
	}


	//< Gather the off screen markers >
	offscreen_markers.Empty();
	for (int i = 0; i < NUM_HUMAN_PLAYERS; ++i)
	{
		FString tmp = FString::Printf(TEXT("marker_%d"), i);
		offscreen_markers.Add(Cast<UUserWidget>(UWWUIFunctionLibrary::FindWidget(gameWindow, tmp)));
		UWWUIFunctionLibrary::SetVisibility(offscreen_markers[i], ESlateVisibility::Collapsed);
	}

	offscreen_ball_marker = Cast<UUserWidget>(UWWUIFunctionLibrary::FindWidget(gameWindow, FString("marker_0")));

	replay_box = UWWUIFunctionLibrary::FindChildWidget(gameWindow, (FString)RU_HUD_REPLAY_BOX);
	if (replay_box)
	{
		replay_box->SetVisibility(ESlateVisibility::Hidden);
	}

	pause_disabled_icon = UWWUIFunctionLibrary::FindChildWidget(hud_box, (FString)RU_HUD_PAUSE_DISABLED);
	if (pause_disabled_icon)
	{
		pause_disabled_icon->SetVisibility(ESlateVisibility::Hidden);
	}
#if defined(RC4_ENABLE_HUD_UPDATER)
	//Update these with the WWUIScreenInGameHud_UI::Namespace calls when the elements exist
	message = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(hud_box, (FString)RU_HUD_MESSAGE));
	
	kick_for_points_help = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(gameWindow, (FString)RU_HUD_KICK_FOR_POINTS));
	placeholder_text = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(gameWindow, (FString)RU_HUD_PLACEHOLDER_TEXT));
	speaking_text = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(gameWindow, (FString)RU_HUD_SPEAKING_TEXT));

	network_shortstall = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(gameWindow, (FString)RU_HUD_NETWORK_SHORTSTALL));
	network_longstall = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(gameWindow, (FString)RU_HUD_NETWORK_LONGSTALL));

	//Team Lineup screen?
	// SURYA_TODO: Confirm this is alright being done here even though it's not part of the GameWindow, easier to populate here than in lua
	/*team_management = ui_manager->GetNodeByContext("RootMenuWindow/TeamLineup");
	player_detail = ui_manager->GetNodeByContext(RU_TM_PLAYER_DETAIL);*/

	//scrum_hud = SIFUIHelpers::NodeGetChild(player_object, RU_SCRUM_HUD);
	if (message)			{ message->SetVisibility(ESlateVisibility::Hidden); }
	if (network_shortstall) { network_shortstall->SetVisibility(ESlateVisibility::Hidden); }
	if (network_longstall)	{ network_longstall->SetVisibility(ESlateVisibility::Hidden); }
	if (placeholder_text)	{ placeholder_text->SetVisibility(ESlateVisibility::Hidden); }
	

	

	if (speaking_text)
	{
		speaking_text->SetVisibility(ESlateVisibility::Hidden);
		//speaking_text->SetAlpha(1.0f);
	}
	if (scrum_hud)
	{
		scrum_hud->SetVisibility(ESlateVisibility::Hidden);
	}

	//Cannot convert from const char to const TCHAR
	//offscreen_ball_marker = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(gameWindow, FString::Printf(RU_HUD_OFFSCREEN_MARKER_SPRINTF, 0)));

	

#endif // defined(RC4_ENABLE_HUD_UPDATER)

	// We will get a hold of the player info node so that we have access to the text boxes.
	// #hud_updates_player_display Will need to rejig this when the HUD is put together
	player_display_info				= Cast<UCanvasPanel>(		UWWUIFunctionLibrary::FindChildWidget(gameWindow, WWUIScreenInGameHud_UI::PlayerInfoPanels));
	player_info_invalidation_box	= Cast<UInvalidationBox>(	UWWUIFunctionLibrary::FindChildWidget(gameWindow, WWUIScreenInGameHud_UI::PlayerInfoPanelsInvalidationBox));
	if (player_display_info != NULL)
	{
		// Initialise all the elements.
		player_info_display.resize(NUM_HUMAN_PLAYERS_PER_TEAM);
		for (size_t i = 0; i < player_info_display.size(); ++i)
		{
			UWidget* current_node = UWWUIFunctionLibrary::FindChildWidget(Cast<UUserWidget>(player_display_info->GetChildAt(i)), WWUIScreenInGameHud_UI::Current);
			UWidget* old_node = UWWUIFunctionLibrary::FindChildWidget(Cast<UUserWidget>(player_display_info->GetChildAt(i)), WWUIScreenInGameHud_UI::Old);
			player_info_display[i].Initialise(current_node, old_node);
		}
	}
}

void RUHUDUpdater::ShutdownHUD()
{
	gameWindow = nullptr;
	hud_box = nullptr;
	countdown_clock = nullptr;
	countdown_clock_spacer = nullptr;
	pro_request_feedback = nullptr;
	pro_goal_feedback = nullptr;
	team_info = nullptr;
	player_info = nullptr;
	substitution_info = nullptr;
	scoreboard = nullptr;
	impact_text = nullptr;
	timer = nullptr;
	timer_message = nullptr;
	if (minimap_node->IsValidLowLevel())
	{
		minimap_node->Shutdown();
	}
	offscreen_markers.Empty();
	minimap_node = nullptr;
	player_display_info = nullptr;
	gameplay_hud = nullptr;
	hud_footer = nullptr;
	quick_lineout_help_text = nullptr;
	decide_lineout_numbers_help_text = nullptr;
	in_game_help_text = nullptr;
	scrum_help_text = nullptr;
	context_container_primary = nullptr;
	context_container_secondary = nullptr;
	conversion_place_help_text = nullptr;
	conversion_aim_help_text = nullptr;
	conversion_accuracy_help_text = nullptr;
	conversion_power_help_text = nullptr;

	game_window_invalidation_box = nullptr;
	timer_invalidation_box = nullptr;
	timer_message_invalidation_box = nullptr;
	player_info_invalidation_box = nullptr;
}

void RUHUDUpdater::RestartGame()
{
	//GG WW AFL RUGBY ERICTOTEST NICKTOTEST
#if(PLATFORM_PS4 || PLATFORM_SWITCH)
	if (!gameWindow) return;
#endif
	//< Reset timer. >
	FString timer_buf = "00:00";
	UWWUIFunctionLibrary::SetText(timer, timer_buf);

	//< Reset Team 0 Scoreboard >-------------------------------------------------------------------------------------
	//< Get team widgets. >
	{
		UTextBlock* team_name_text = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::Team0NameText));
		UTextBlock* team_score_text = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::Team0ScoreText));
		UImage* team_colour_node = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::Team0Colour));

		//< Set team default values. >
		UWWUIFunctionLibrary::SetText(team_name_text, ConvertMabStringToFString(SIFGameHelpers::GAGetSideMnemonic(0)));
		UWWUIFunctionLibrary::SetText(team_score_text, FString("0"));

		//< Set team color. >
		MabColour friendly_colour = SIFInGameHelpers::GetTeamColour(0);
		UWWUIFunctionLibrary::SetColorAndOpacity(team_colour_node, FLinearColor(friendly_colour.r, friendly_colour.g, friendly_colour.b, friendly_colour.a));
	}
	//----------------------------------------------------------------------------------------------------------------



	//< Reset Team 1 Scoreboard >-------------------------------------------------------------------------------------
	//< Get team widgets. >
	{
		UTextBlock* team_name_text = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::TextTeam1Name));
		UTextBlock* team_score_text = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::TextTeam1Score));
		UImage* team_colour_node = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::Team1Colour));

		//< Set team default values. >
		UWWUIFunctionLibrary::SetText(team_name_text, ConvertMabStringToFString(SIFGameHelpers::GAGetSideMnemonic(1)));
		UWWUIFunctionLibrary::SetText(team_score_text, FString("0"));

		//< Set team color. >
		MabColour friendly_colour = SIFInGameHelpers::GetTeamColour(1);
		UWWUIFunctionLibrary::SetColorAndOpacity(team_colour_node, FLinearColor(friendly_colour.r, friendly_colour.g, friendly_colour.b, friendly_colour.a));
	}
	//----------------------------------------------------------------------------------------------------------------

	if (scoreboard)
		scoreboard->InvalidateCache();

#if defined(RC4_ENABLE_HUD_UPDATER)
#if (PLATFORM_PS4)
	SetCensoredName(game_context->GetGameWorld()->GetTeam(0)->GetDbTeam(), Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(team0, (FString)"HUD_TEAM_NAME_TEXT")), true);
	SetCensoredName(game_context->GetGameWorld()->GetTeam(1)->GetDbTeam(), Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(team1, (FString)"HUD_TEAM_NAME_TEXT")), true);
#elif (PLATFORM_XBOXONE)
	UUserWidget* opponent_team_name_text = SIFMatchmakingHelpers::IsHost() ? team1 : team0;
	RUTeam* opponent_team = SIFMatchmakingHelpers::IsHost() ? game_context->GetGameWorld()->GetTeam(1) : game_context->GetGameWorld()->GetTeam(0);
	SetCensoredName(opponent_team->GetDbTeam(), Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(opponent_team_name_text, (FString)"HUD_TEAM_NAME_TEXT")), true);
#endif

#endif // defined(RC4_ENABLE_HUD_UPDATER)
	if (replay_box)
	{
		replay_box->SetVisibility(ESlateVisibility::Hidden);
	}

	// clear any pending hud messages, they don't make any sense now anyway
	deferred_player_infos.clear();

	SetScrumHUDVisible(false);

	//< Reset team cards. >
	red_card_num_home = 0;
	red_card_num_away = 0;

	SetRedCard(SIDE_A, 0);
	SetRedCard(SIDE_B, 0);
	SetYellowCard(SIDE_A, 0);
	SetYellowCard(SIDE_B, 0);
}

void RUHUDUpdater::Update(float delta_time)
{
	UpdateMinimap();

	//< Update Scoreboard Timer >-------------------------------------------------------------------------------------
	SSGameTimer *game_timer = game_context->GetGameWorld()->GetGameTimer();
	if (game_timer->IsExpired())
	{
		//< When timer runs out.. play audio & change color. >
		if (timer_colour != COLOR_TIMER_ORANGE)
		{
			SIFAudioHelpers::PlaySoundEvent(RU_SOUND_MATCH_OVERTIME_STARTED);
		}

		// Change HUD text/background to orange - overtime
		timer_colour = COLOR_TIMER_ORANGE;

	}
	else //< Keep it blue. >
	{
		timer_colour = COLOR_TIMER_WHITE;
	}

	//< Apply updated timer color >
	UWWUIFunctionLibrary::SetColorAndOpacity(timer, timer_colour);

	//< Update timer visibility & text. >
	if (game_timer->IsInExtraTime() && game_timer->IsPlayForEver())
	{
		UWWUIFunctionLibrary::SetVisibility(timer, false);
	}
	else
	{
		UWWUIFunctionLibrary::SetVisibility(timer, true);

		int min_elapsed = game_timer->GetScaledMinutesElapsed();
		int sec_elapsed = game_timer->GetScaledSecondsElapsed();

		FString timer_buf = FString::Printf(TEXT("%02d:%02d"), min_elapsed, sec_elapsed);
		UWWUIFunctionLibrary::SetText(timer, timer_buf);
	}

	if (timer_invalidation_box) timer_invalidation_box->InvalidateCache();
	//----------------------------------------------------------------------------------------------------------------


	//< Update Timer Message >----------------------------------------------------------------------------------------
	if (timer_message)
	{
		//< Timer message should only be visible while in overtime. >
		const bool is_in_extra_time = game_timer->IsInExtraTime();
		UWWUIFunctionLibrary::SetVisibility(timer_message, is_in_extra_time);

		//< Display either the extra timer message OR golden point. >
		if (is_in_extra_time)
		{
			//	Not a type, that is the widget name.
			if (UWidget* tempHudHudBoxWidget = UWWUIFunctionLibrary::FindChildWidget(hud_box, WWUIScreenInGameHud_UI::BP_HUD_GameInfo))
			{
				if (UWWUITextBlock* message_text = Cast<UWWUITextBlock>(UWWUIFunctionLibrary::FindChildWidget(tempHudHudBoxWidget, WWUIScreenInGameHud_UI::TextExtendedTime)))
				{
					message_text->SetText(FText::FromString(
						game_timer->IsPlayForEver() ?
						UWWUITranslationManager::Translate("[ID_GOLDEN_POINT]") :
						UWWUITranslationManager::Translate("[ID_EXTRA_TIME]")));
				}
			}
		}
	}
	if (timer_message_invalidation_box) timer_message_invalidation_box->InvalidateCache();
	//----------------------------------------------------------------------------------------------------------------

	UpdateScreenMessage(delta_time);

	UpdateCountdownClock(delta_time);

	UpdateDeferredPlayerInfo(delta_time);

	//< Update 2D Context Containers. >
	if (context_container_primary) context_container_primary->Update( delta_time );
	if (context_container_secondary) context_container_secondary->Update( delta_time );
	

	//==================================================================================================================================================================================================
	//< UPDATE HELP TIP TEXT. >=========================================================================================================================================================================
	bool display_help = false;
	
	if (game_context->GetGameWorld()->GetGameState()->GetPhase() == RUGamePhase::KICK_OFF)
	{
		//< Display HUD_BOX. >
		UWWUIFunctionLibrary::SetVisibility(hud_box, ESlateVisibility::Visible);
		if (hud_box) hud_box->InvalidateCache();

		//< If ball is held by local player, set help text to Kick. >
		if (ARugbyCharacter* ball_holder = game_context->GetGameWorld()->GetGameState()->GetBallHolder())
		{
			if (SSHumanPlayer* human_player = ball_holder->GetHumanPlayer())
			{
				if (!human_player->IsNetworkPlayer())
				{
					RURoleKickOffKicker* kick_off_kicker = MabCast<RURoleKickOffKicker>(ball_holder->GetRole());
					if (kick_off_kicker && kick_off_kicker->GetKickoffState() == KICK_OFF_WAIT_FOR_INPUT)
					{
						in_game_help_text->SetText("[ID_INGAME_KICK_HELP_TEXT]");
						if (gameplay_hud) gameplay_hud->InvalidateCache();
						display_help = true;
					}
				}
			}
		}
	}
		
	if (cutscene_skip_text_shown && in_game_help_text)
	{

		in_game_help_text->SetText(FString("[ID_IG_INTRO_CUTSCENE_SKIP_HELP]"));
		
		if (gameInstance->GetMatchGameSettings()->game_settings.network_game)
		{
			if (online_cutscene_wait_text_shown)
			{
				in_game_help_text->SetText(FString("[ID_ONLINE_PLAYER_OPPONENT_NOT_READY]"));
			}
		}
		
		if (gameplay_hud)
		{
			gameplay_hud->InvalidateCache();
		}

		display_help = true;
	}

	UWWUIFunctionLibrary::SetVisibility(hud_footer, display_help);
	//==================================================================================================================================================================================================
	//< END >



	//==================================================================================================================================================================================================
	//< UPDATE PLAYER INFO PANELS. (Display names above player heads) >=================================================================================================================================
	if (player_display_info != NULL)
	{
		// All of the human players need to be retrieved
		SIFGameWorld* game_world = game_context->GetGameWorld();

		// We don't store off the number of players because this can vary though the game.
		const size_t num_of_players = game_world->GetHumanPlayers().size();
		const size_t available_slot_n = player_display_info->GetChildrenCount();

		size_t info_index = 0;
		for (int i = 0; i < (int)num_of_players; ++i)
		{
			SSHumanPlayer* controlling_player = game_world->GetHumanPlayer((EHumanPlayerSlot)i);
			if ( controlling_player->IsNetworkPlayer() == true || info_index >= available_slot_n)
			{
				// We don't want to display info for networked players.
				// If we've exceeded the number of available slots then me have to ignore it.
				continue;
			}

			player_info_display[info_index].SetCurrentPlayer( controlling_player ? controlling_player->GetRugbyCharacter() : NULL );
			++info_index;
		}

		// Update all player info. Otherwise, the player name may never fade out when switching teams.
		for ( unsigned int i = 0; i < player_info_display.size(); ++i )
		{
			player_info_display[i].Update( delta_time );
		}
		player_info_invalidation_box->InvalidateCache();
	}
	//==================================================================================================================================================================================================
	//< END >

	if (pause_disabled_icon_fade_time > 0.0f)
	{
		pause_disabled_icon_fade_time -= delta_time;
		if (pause_disabled_icon_fade_time <= 0.0f)
		{
			pause_disabled_icon->SetVisibility(ESlateVisibility::Hidden);
		}
	}
	
	if (pause_disabled_online_time > 0.0f)
	{
		pause_disabled_online_time -= delta_time;
	}
}

void RUHUDUpdater::SyncUpdate()
{
	if (impact_text_resized)
	{
		impact_text_resized = false;
		
		//< Start animation to slide out. >
		UWWUIFunctionLibrary::PlayAnimation(scoreboard, impact_text_animation);
		scoreboard->InvalidateCache();
	}

	// Our feedback for when we make a request as a pro player
	if (pro_request_feedback_resized && pro_request_feedback)
	{
		pro_request_feedback_resized = false;

		//< Start animation to slide out >
		UWWUIFunctionLibrary::PlayAnimation(hud_box, pro_request_feedback_animation);
		pro_request_feedback->InvalidateCache();
	}
	
	// Our feedback for when we make progress on a pro goal
	if (pro_goal_feedback_text_resized && pro_goal_feedback)
	{
		pro_goal_feedback_text_resized = false;

		//< Start animation to slide out >
		UWWUIFunctionLibrary::PlayAnimation(hud_box, pro_goal_feedback_text_animation);
		pro_goal_feedback->InvalidateCache();
	}

	if (team_info_resized && team_info)
	{
		team_info_resized = false;

		//< Play animation on the team_info panel, this will open, then close the panel. >
		UWWUIFunctionLibrary::PlayAnimation(team_info, FString("team_info_animation"));
		SIFAudioHelpers::PlaySoundEvent(RU_SOUND_INFO_PANEL_APPEARS);
		team_info->InvalidateCache();
	}

	if (player_info_resized && player_info)
	{
		player_info_resized = false;
		//< Play animation on the player_panel panel, this will open, then close the panel. >
		UWWUIFunctionLibrary::PlayAnimation(player_info, FString("player_info_animation"));
		SIFAudioHelpers::PlaySoundEvent(RU_SOUND_INFO_PANEL_APPEARS);
		player_info->InvalidateCache();
	}

	// Stop displaying 'player_info' - nicely, fast forward ui-animation till just before it starts closing.
	if (requested_info_finish)
	{
		if (player_info)
		{
			const float PLAYER_INFO_END_TIME = 3.0f;
			UWWUIFunctionLibrary::SkipForwardInAnimation(player_info, FString("player_info_animation"), PLAYER_INFO_END_TIME);
		}
		if (team_info)
		{
			const float TEAM_INFO_END_TIME = 3.0f;
			UWWUIFunctionLibrary::SkipForwardInAnimation(team_info, FString("team_info_animation"), TEAM_INFO_END_TIME);
		}

		deferred_player_infos.clear();
		requested_info_finish = false;
	}

	if (substitution_info_resized && substitution_info)
	{
		substitution_info_resized = false;

		//--------------------------------------------------------------------------------------------------------------------
		//< HOME TEAM SUBSTITUTION HUD >--------------------------------------------------------------------------------------
		UWidget* team_node						= UWWUIFunctionLibrary::FindChildWidget(substitution_info, WWUIScreenInGameHud_UI::Team_0);
		UWWUIUserWidget* substitution_list		= Cast<UWWUIUserWidget>(UWWUIFunctionLibrary::FindChildWidget(substitution_info, WWUIScreenInGameHud_UI::SubstitutionList_0));
		UVerticalBox* substitution_list_box		= Cast<UVerticalBox>(UWWUIFunctionLibrary::FindChildWidget(substitution_list, WWUIScreenInGameHud_UI::SubstitutionListVerticleBox));

		// We only want to display the information we're using.
		int available_index = substitution_list->GetIntProperty(RU_SUBSTITUTION_INDEX);
		if (available_index == MAX_int32) available_index = 0;

		// Display home team if there is something to show.
		UWWUIFunctionLibrary::SetVisibility(team_node, (available_index != 0) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		if (available_index != 0)
		{
			UWWUIFunctionLibrary::PlayAnimation(substitution_info, FString("substitution_info_animation_team0"));
		}

		// Only display fields with data.
		for (size_t j = 0; j < substitution_list_box->GetChildrenCount(); ++j)
		{
			if (UUserWidget* substitution_child = Cast<UUserWidget>(substitution_list_box->GetChildAt(j + 1)))
			{
				// We only want it to be visible if we've got something to show.
				substitution_child->SetRenderOpacity(1.0f);
				UWWUIFunctionLibrary::SetVisibility(substitution_child, (j < static_cast<size_t>(available_index)) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
				UWWUIFunctionLibrary::PlayAnimation(substitution_child, FString("substitution_elemenet_animation"));
			}
		}

		// Clear out the substitution index for the next run.
		int resettingSubstitutionIndex = 0;
		substitution_list->SetProperty(RU_SUBSTITUTION_INDEX, &resettingSubstitutionIndex, UIPropertyType::PROPERTY_TYPE_INT);
		substitution_list->InvalidateCache();
		//< END - HOME TEAM SUBSTITUTION HUD >--------------------------------------------------------------------------------
		//--------------------------------------------------------------------------------------------------------------------



		//--------------------------------------------------------------------------------------------------------------------
		//< AWAY TEAM SUBSTITUTION HUD >--------------------------------------------------------------------------------------
		team_node				= UWWUIFunctionLibrary::FindChildWidget(substitution_info, WWUIScreenInGameHud_UI::Team_1);
		substitution_list		= Cast<UWWUIUserWidget>(UWWUIFunctionLibrary::FindChildWidget(substitution_info, WWUIScreenInGameHud_UI::SubstitutionList_1));
		substitution_list_box	= Cast<UVerticalBox>(UWWUIFunctionLibrary::FindChildWidget(substitution_list, WWUIScreenInGameHud_UI::SubstitutionListVerticleBox));

		// We only want to display the information we're using.
		available_index = substitution_list->GetIntProperty(RU_SUBSTITUTION_INDEX);
		if (available_index == MAX_int32) available_index = 0;

		// Display home team if there is something to show.
		UWWUIFunctionLibrary::SetVisibility(team_node, (available_index != 0) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		if (available_index != 0)
		{
			UWWUIFunctionLibrary::PlayAnimation(substitution_info, FString("substitution_info_animation_team1"));
		}

		// Only display fields with data.
		for (size_t j = 0; j < substitution_list_box->GetChildrenCount(); ++j)
		{
			if (UUserWidget* substitution_child = Cast<UUserWidget>(substitution_list_box->GetChildAt(j + 1)))
			{
				// We only want it to be visible if we've got something to show.
				substitution_child->SetRenderOpacity(1.0f);
				UWWUIFunctionLibrary::SetVisibility(substitution_child, (j < static_cast<size_t>(available_index)) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
				UWWUIFunctionLibrary::PlayAnimation(substitution_child, FString("substitution_elemenet_animation"));
			}
		}

		// Clear out the substitution index for the next run.
		resettingSubstitutionIndex = 0;
		substitution_list->SetProperty(RU_SUBSTITUTION_INDEX, &resettingSubstitutionIndex, UIPropertyType::PROPERTY_TYPE_INT);
		substitution_list->InvalidateCache();
		//< END - HOME TEAM SUBSTITUTION HUD >--------------------------------------------------------------------------------
		//--------------------------------------------------------------------------------------------------------------------

		
		// We only want to play the sound once, even if both team subs are displaying.
		SIFAudioHelpers::PlaySoundEvent(RU_SOUND_INFO_PANEL_APPEARS);
		substitution_info->InvalidateCache();
	}
}

// Resyncs the hud display
void RUHUDUpdater::ReSyncStats()
{
	SIFGameWorld* game = game_context->GetGameWorld();
	RUStatisticsSystem* stats = SIFApplication::GetApplication()->GetStatisticsSystem();

	//< Update Home Team scores. >
	UTextBlock* team0_score_text = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::Team0ScoreText));
	UWWUIFunctionLibrary::SetText(team0_score_text, ConvertMabStringToFString(MabString(32, "%d", stats->GetCurrentMatchStat(game->GetTeam(SIDE_A), &RUDB_STATS_TEAM::score)).c_str()));
	
	//< Update Away Team scores. >
	UTextBlock* team1_score_text = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::TextTeam1Score));
	UWWUIFunctionLibrary::SetText(team1_score_text, ConvertMabStringToFString(MabString(32, "%d", stats->GetCurrentMatchStat(game->GetTeam(SIDE_B), &RUDB_STATS_TEAM::score)).c_str()));

	if (scoreboard) scoreboard->InvalidateCache();
}

void RUHUDUpdater::SetScreenMessage(const MabString& message_id, const MabString& message_title, const MabString& message_text, float priority, float timeout)
{
	MABUNUSED(message_id);
	MABUNUSED(message_title);
	MABUNUSED(message_text);
	MABUNUSED(priority);
	MABUNUSED(timeout);
}

#if defined(RC4_ENABLE_HUD_UPDATER)
void RUHUDUpdater::SetScreenMessageNew(const MabString& message_id, const MabString& message_title, const MabString& message_text, float priority, float timeout)
{
	/*bool found = false;
	// Then check the existing messages.
	for (TArray<ScreenMessage>::iterator i = screen_messages.begin(); i != screen_messages.end(); ++i)
	{
		// If found...
		if (strcmp(message_id.c_str(), i->message_id.c_str()) == 0)
		{
			// update with new text, timeout and priority
			i->message_title = message_title;
			i->message_text = message_text;
			i->timeout = timeout;
			i->priority = priority;

			// Don't need to add to stack
			found = true;

			// Can also early out, since there shouldn't be anything else to update
			break;
		}
	}

	// If the message_id wasn't found, add a message to the vector
	if (!found)
	{
		screen_messages.push_back(ScreenMessage(message_id, message_title, message_text, priority, timeout));
	}

	// Update the on screen messages if we either don't have one displayed, received a new one of higher priorty
	// or if we updated a message which could be the active one.
	if (screen_message_idx == -1 || screen_messages[screen_message_idx].priority < priority || found)
	{
		ReselectScreenMessage();
	}*/
}
#endif // defined(RC4_ENABLE_HUD_UPDATER)

void RUHUDUpdater::RemoveScreenMessage(const FString& message_id)
{
	// Check all messages for matching IDs
	for (int i = screen_messages.Num() - 1; i >= 0; i--)
	{
		// On match, erase. Keep looking to filter out duplicates if they do get introduced for some reason
		if (screen_messages.IsValidIndex(i))
		{
			if (message_id.Compare(screen_messages[i].message_id) == 0)
				screen_messages.RemoveAt(i);
		}
	}

	// Since the erased message could be anywhere in the vector we'll need to search for correct index again
	ReselectScreenMessage();
}

void RUHUDUpdater::ClearScreenMessages()
{
	// Clear vector
	screen_messages.Empty();
	screen_message_idx = -1;

#if defined(RC4_ENABLE_HUD_UPDATER)
	// Clear widget
	if (message)
	{
		message->SetVisibility(ESlateVisibility::Hidden);
	}
#endif // defined(RC4_ENABLE_HUD_UPDATER)
}

void RUHUDUpdater::UpdateScreenMessage(float delta_time)
{
	// Flag for whether we need to update on screen message
	bool screen_msg_needs_update = false;

	// Update message timers
	for (int i = screen_messages.Num() - 1; i >= 0; i--)
	{
		// Update the time of any message with a timeout
		if (screen_messages[i].timeout > 0.0f)
			screen_messages[i].timeout -= delta_time;

		// Clear expired messages
		if (screen_messages[i].timeout < 0.0f)
		{
			screen_messages.RemoveAt(i);
			screen_msg_needs_update = true;
		}
	}

	// Only call reselect on the screen message if a message timed out
	if (screen_msg_needs_update)
		ReselectScreenMessage();
}

void RUHUDUpdater::ReselectScreenMessage()
{
	// If we need an update we can be fairly sure our index is no longer valid
	screen_message_idx = -1;
	int idx = 0;
	float highest_priority = 0.0f;

	// Search for the highest priority
	for (int i = 0; i < screen_messages.Num(); i++)
	{
		if (screen_messages[i].priority > highest_priority)
		{
			screen_message_idx = i;
			highest_priority = screen_messages[i].priority;
		}
	}

	// Is our index valid?
	if (screen_message_idx >= 0 && screen_message_idx < (int)screen_messages.Num())
	{
		// Grab the message at the index and draw on screen
		const ScreenMessage new_message = screen_messages[screen_message_idx];

#if defined(RC4_ENABLE_HUD_UPDATER)
		if (message)
		{
			UTextBlock* title_widget = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(message, (FString)"HUD_MESSAGE_TITLE_TEXT"));
			UTextBlock* main_widget = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(message, (FString)"HUD_MESSAGE_MAIN_TEXT"));
			//FString text = new_message.message_title;
			//title_widget->SetText(FText::FromString(text));
			//text = new_message.message_text;
			//main_widget->SetText(FText:FromString(text));
			UWWUIFunctionLibrary::SetText(title_widget, new_message.message_title);
			UWWUIFunctionLibrary::SetText(main_widget, new_message.message_text);

			// FOR ARU BUILD
			message->SetVisibility(ESlateVisibility::Visible);
		}
#endif // defined(RC4_ENABLE_HUD_UPDATER)
	}
	else
	{
#if defined(RC4_ENABLE_HUD_UPDATER)
		// If not, clear widget
		if (message)
		{
			message->SetVisibility(ESlateVisibility::Hidden);
		}
#endif // defined(RC4_ENABLE_HUD_UPDATER)
	}
}

void RUHUDUpdater::OnReplayStarted(REPLAY_TYPE replay_type, int /*played_count*/)
{
	MABUNUSED(replay_type);
//#if defined(RC4_ENABLE_HUD_UPDATER)
	//SIFUIEffectsHelpers::NodeRunNamedAnimation(replay_text, "replay_text_animation");
	if(replay_box != NULL)
	{
		replay_box->SetVisibility(ESlateVisibility::Visible); 
	}
//#endif // defined(RC4_ENABLE_HUD_UPDATER)
}

void RUHUDUpdater::OnReplayStopped()
{
//#if defined(RC4_ENABLE_HUD_UPDATER)
	//SIFUIEffectsHelpers::NodeStopAllAnimations(replay_text);
	if (replay_box != NULL)
	{
		replay_box->SetVisibility(ESlateVisibility::Hidden);
	}
//#endif // defined(RC4_ENABLE_HUD_UPDATER)
}

void RUHUDUpdater::OnPerformInterchange(int, RUTeam*)
{
	RUSubstitutionManager* subs_manager = SIFApplication::GetApplication()->GetActiveGameWorld()->GetSubstitutionManager();
	MABASSERT(subs_manager);
	if (!subs_manager)
		return;

	SetRedCard(SIDE_A, subs_manager->NumPlayersSentOff(0));
	SetRedCard(SIDE_B, subs_manager->NumPlayersSentOff(1));

	SetYellowCard(SIDE_A, subs_manager->GetNumPlayerInSinbin(SIDE_A));
	SetYellowCard(SIDE_B, subs_manager->GetNumPlayerInSinbin(SIDE_B));
}

void RUHUDUpdater::SetQuickLineoutHelpTextVisible(bool visible)
{
	UWWUIFunctionLibrary::SetVisibility(quick_lineout_help_text, visible);
	if (game_window_invalidation_box) game_window_invalidation_box->InvalidateCache();
}

void RUHUDUpdater::SetDecideLineoutNumbersHelpTextVisible(bool visible)
{
	UWWUIFunctionLibrary::SetVisibility(decide_lineout_numbers_help_text, visible);
	if (game_window_invalidation_box) game_window_invalidation_box->InvalidateCache();
}

void RUHUDUpdater::ShowPauseDisabledIcon()
{
	if (pause_disabled_icon)
	{
		pause_disabled_icon->SetVisibility(ESlateVisibility::Visible);
		pause_disabled_icon_fade_time = PAUSE_DISABLED_ICON_FADE_TIME;
	}
}

void RUHUDUpdater::UpdateContextOption(const MabVector<ContextData> &new_option, bool is_primary )
{
	if (context_container_primary && is_primary)
	{
		context_container_primary->UpdateContextOption(new_option);
	}

	if (context_container_secondary && !is_primary)
	{
		context_container_secondary->UpdateContextOption(new_option);
	}
}

void RUHUDUpdater::UpdateContextOption(int index, const ContextData& data, bool is_primary  )
{
	if (context_container_primary && is_primary)
	{
		context_container_primary->UpdateContextOption(index, data);
	}

	if (context_container_secondary && !is_primary)
	{
		context_container_secondary->UpdateContextOption(index, data);
	}
}

void RUHUDUpdater::ClearContextOptions(bool is_primary)
{
	if (context_container_primary && is_primary)
	{
		context_container_primary->ClearContainer();
	}

	if (context_container_secondary && !is_primary)
	{
		context_container_secondary->ClearContainer();
	}
}

void RUHUDUpdater::SetContextOptionsVisible(bool visible, bool is_primary)
{
}

void RUHUDUpdater::SetContextOptionsName(const char* name, bool is_primary)
{
	if (context_container_primary && is_primary)
	{
		UTextBlock* title = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(gameplay_hud, WWUIScreenInGameHud_UI::AttackTitle));
		UWWUIFunctionLibrary::SetText(title, FString(name));
	}
	else if (context_container_secondary && !is_primary)
	{
		UTextBlock* title = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(gameplay_hud, WWUIScreenInGameHud_UI::DefenseTitle));
		UWWUIFunctionLibrary::SetText(title, FString(name));
	}
}

//===============================================================================
//===============================================================================
void RUHUDUpdater::SetCutSceneSkipTextVisible(bool new_value)
{
	cutscene_skip_text_shown = new_value;

	if (!new_value)
	{
		SetOnlineCutSceneWaitTextVisible(false);
	}
}

void RUHUDUpdater::SetCutsceneActiveState(bool is_active)
{
	UWWUIFunctionLibrary::SetVisibility(hud_box, !is_active);
	UWWUIFunctionLibrary::SetVisibility(minimap_node, !is_active);
	if (hud_box) hud_box->InvalidateCache();
}

void RUHUDUpdater::UpdateTeamLineup(int team_index)
{
#if defined(RC4_ENABLE_HUD_UPDATER)
#if (PLATFORM_XBOXONE) || (PLATFORM_PS4)
	int opponent_team_id = SIFMatchmakingHelpers::IsHost() ? 1 : 0;
	MABLOGDEBUG("Update team info for team index: %d and opponent team is %d", team_index, opponent_team_id);
#endif
	if (team_management)
	{
		RUTeam* team = game_context->GetGameWorld()->GetTeam((SSTEAMSIDE)team_index);
		RUTeam* other_team = (RUTeam*)team->GetOppositionTeam();

		if (game_context->GetGameWorld()->GetCutSceneManager()->GetCinematicsEnabled())
		{
			RUAudio* audio = SIFApplication::GetApplication()->GetRUAudio();

			MabString name(team->GetDbTeam().GetName());
			MabString other_name(other_team->GetDbTeam().GetName());

			if (name.find(" R7", 0) != MabString::npos)
			{
				RL3DB_COUNTRY team_country(team->GetDbTeam().GetAssociatedCountryId());
				name = team_country.GetName();
			}

			if (other_name.find(" R7", 0) != MabString::npos)
			{
				RL3DB_COUNTRY other_team_country(other_team->GetDbTeam().GetAssociatedCountryId());
				other_name = other_team_country.GetName();
			}

			if (audio->HasAnthemEvent(name.c_str(), other_name.c_str()))
			{
				if (audio->GetMusic() == "")
				{
					audio->PushAnthemMusic(name.c_str());
					//audio->PushAnthemMusic(team->GetDbTeam().GetName());
				}
				else
				{
					audio->PopMusic();
					audio->PushAnthemMusic(name.c_str());
					//audio->PushAnthemMusic(team->GetDbTeam().GetName());
				}
			}
		}

		// Update team name as well
		UTextBlock* team_name = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(team_management, (FString)"BackgroundMenuTemplate/MenuTitle/Heading"));
		if (team_name)
		{
			FString text = team->GetDbTeam().GetShortName();
			team_name->SetText(FText::FromString(text).ToUpper());
#if (PLATFORM_XBOXONE)
			if (team_index == opponent_team_id)
#endif
#if (PLATFORM_XBOXONE) || (PLATFORM_PS4)
			{
				const RUDB_TEAM& opponent_team = team->GetDbTeam();
				SetCensoredName(opponent_team, team_name);
			}
#endif
		}
		UUserWidget* team_lineup = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(team_management, (FString)"LineupTab/TeamLineup"));
		int playersPerTeam = game_context->GetGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();
		int sevens_offset = 0;

		// Nick  WWS 7s to Womens //
		/*
#ifdef ENABLE_SEVENS_MODE
		bool isSevensGame = gameInstance->GetMatchGameSettings()->game_settings.GetGameMode() == GAME_MODE_SEVENS;
		if (isSevensGame)
		{
			//sevens_offset = (int)team_lineup->GetNumChildren() - NUM_PLAYERS_PER_TEAM_SEVENS;
		}
#endif*/

		for (int i = 0; i < playersPerTeam/*NUM_PLAYERS_PER_TEAM*/; ++i)
		{
			//MabUINode* current_player = team_lineup->GetChildByIndex(i+sevens_offset);
			//Cannot convert from const char to const TCHAR
			//UUserWidget* current_player = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(team_lineup, (FString::Printf("%d", i + sevens_offset))));

			int index = i;
			// Nick  WWS 7s to Womens //
			/*
#ifdef ENABLE_SEVENS_MODE
			GAME_MODE gameMode = gameInstance->GetMatchGameSettings()->game_settings.GetGameMode();
			// Special case for Sevens games.
			// Since the real R15 positions don't exist, we use the offset positions for R7
			if (gameMode == GAME_MODE_SEVENS)
			{
				if (index == 3) index = 8;
				if (index == 4) index = 9;
				if (index == 5) index = 11;
				if (index == 6) index = 14;
			}
#endif */

			ARugbyCharacter* player = team->GetPlayerByPosition((PLAYER_POSITION)(1 << index));
			//current_player->SetVisibility((player != NULL) ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
			MABASSERTMSG(player != NULL, "Missing player in lineup. Has it been deleted because of a duplicate ID?");
			if (player == NULL)
			{
				// This is just a safety check. If we're missing a player for some odd reason then we don't want the game to crash.
				continue;
			}
			//current_player->SetProperty("db_id", MabString(0, "%d", player->GetAttributes()->GetDBPlayer()->GetDbId()));
			/*UTextBlock* player_position_widget;// = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(current_player, (FString)"Background/Number"));
			if (player_position_widget)
			{
				//player_position_widget->SetText(MabString(0, "[ID_NUMBER_%d]", player->GetAttributes()->GetNumber()));
				//Cannot convert from const char to const TCHAR
				//UWWUIFunctionLibrary::SetText(player_position_widget, FString::Printf("%d.", player->GetAttributes()->GetNumber()));
			}*/

			// player portrait
			/*RUUIPlayerFaceImage *player_face = MabCast<RUUIPlayerFaceImage>(current_player->GetChildByContext("Portrait"));
			player_face->SetPlayerIndex(i);
			player_face->SetAtlasIndex(team_index);*/

			/*UTextBlock* player_name_widget;// = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(current_player, (FString)"Background/Name"));
			if (player_name_widget)
			{
				FString playerName = "";  //SIFInGameHelpers::GetPlayerDisplayName(team_index, index);
				UWWUIFunctionLibrary::SetText(player_name_widget, playerName);
#if (PLATFORM_XBOXONE)
				if (game_context->GetGameWorld()->GetGameSettings().game_settings.network_game)
				{
					if (team_index == opponent_team_id)
					{
						const RUDB_TEAM& opponent_team = team->GetDbTeam();
						bool isCustomer = player->GetAttributes()->GetDBPlayer()->IsCustom();
						/ *if (isCustomer && !game_context->GetMatchMakingManager()->GetViewOthersPrivilege())
						{
							MabString player_consored_string = MabGlobalTranslationManager::GetInstance()->Translate("[ID_XBOX_CENSORED_NAME]");
							player_name_widget->SetText(MabStringHelper::ToUpper(player_consored_string));
						}* /
					}
				}

#elif (PLATFORM_PS4)
				CensorPlayerName(&team->GetDbTeam(), player->GetAttributes()->GetDBPlayer(), playerName);
				UWWUIFunctionLibrary::SetText(player_name_widget, playerName);
#endif
			}*/
		}
	}
#endif // defined(RC4_ENABLE_HUD_UPDATER)
}

void RUHUDUpdater::UpdatePlayerDetail(const ARugbyCharacter* player, UUserWidget* container /*= NULL */)
{
#if defined(RC4_ENABLE_HUD_UPDATER)
	UUserWidget* player_container = container ? container : player_detail;
	if (player)
	{
		//player_container->SetProperty("db_id", MabString(0, "%d", player->GetAttributes()->GetDBPlayer()->GetDbId()));
		UUserWidget* player_info = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(player_container, (FString)"PlayerInfo"));
		if (player_info)
		{
			//MabUIImage* player_portrait = MabCast<MabUIImage>(player_info->GetChildByIndex(PLAYER_DETAIL_PORTRAIT));
			UImage* player_portrait = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(player_info, (FString)"PLAYER_DETAIL_PORTRAIT"));
			if (player_portrait)
			{

			}
			//MabUITextInterface* player_name = MabCast<MabUITextInterface>(player_info->GetChildByIndex(PLAYER_DETAIL_NAME));
			UTextBlock* player_name = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(player_info, (FString)"PLAYER_DETAIL_NAME"));
			if (player_name)
			{
				FString playerName = UTF8_TO_TCHAR(player->GetAttributes()->GetDisplayName());
#if PLATFORM_PS4
				CensorPlayerName(NULL, player->GetAttributes()->GetDBPlayer(), playerName);
#endif
				UWWUIFunctionLibrary::SetText(player_name, playerName);
			}
		}

		UUserWidget* player_stats = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(player_container, (FString)"PlayerStats"));
		if (player_stats)
		{
			const RUDB_PLAYER* db_player = player->GetAttributes()->GetDBPlayer();

			// Player positions
			for (int i = 0; i < 3; ++i)
			{
				bool valid_position = false;

				// MULTI_POSITION_CATEGORY_CHANGE
				// Nick WWS 7s to Womens 13s //if (gameInstance->GetMatchGameSettings()->game_settings.GameModeIsR7())
				//	valid_position = db_player->GetPositionCategoryR7(i) != 0;
				//else
					valid_position = db_player->GetPositionCategoryR13(i) != 0;

				/*UImage* player_position_bg;// = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(player_stats, FString::Printf("%d", PLAYER_STATS_POSITION_1_RATING_BG + (i * 3))));
				if (player_position_bg)
				{
					player_position_bg->SetVisibility(valid_position ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
				}*/
				/*UTextBlock* player_rating_position;// = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(player_stats, FString::Printf("%d", PLAYER_STATS_POSITION_1_RATING_BG + (i * 3) + 1)));
				if (player_rating_position)
				{
					player_rating_position->SetVisibility(valid_position ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
					UWWUIFunctionLibrary::SetText(player_rating_position, (FString)"(PH)5.0");
				}*/
				/*UTextBlock* player_position;// = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(player_stats, FString::Printf("%d", PLAYER_STATS_POSITION_1_RATING_BG + (i * 3) + 2)));
				if (player_position)
				{
					player_position->SetVisibility(valid_position ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
					// MULTI_POSITION_CATEGORY_CHANGE
					if (gameInstance->GetMatchGameSettings()->game_settings.GameModeIsR7())
					{
						UWWUIFunctionLibrary::SetText(player_position, (FString)(PlayerPositionEnum::GetPlayerPositionText(db_player->GetPositionCategoryR7(i))));
					}
					else
					{
						UWWUIFunctionLibrary::SetText(player_position, (FString)(PlayerPositionEnum::GetPlayerPositionText(db_player->GetPositionCategoryR13(i))));
					}

					//player_position->SetText(PlayerPositionEnum::GetPlayerPositionText(db_player->position_categories[i]));
				}*/
			}

			// Player specialties
			for (int i = 0; i < 3; ++i)
			{
				/*UTextBlock* player_specialties;// = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(player_stats, FString::Printf("%d", PLAYER_STATS_SPECIALTIES_1 + (i * 4))));
				if (player_specialties)
				{
					player_specialties->SetVisibility(ESlateVisibility::Hidden);
				}
				UImage* player_bar_bg_specialties;// = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(player_stats, FString::Printf("%d", PLAYER_STATS_SPECIALTIES_1 + (i * 4) + 1)));
				if (player_bar_bg_specialties)
				{
					player_bar_bg_specialties->SetVisibility(ESlateVisibility::Hidden);
				}
				UImage* player_bar_specialties;// = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(player_stats, FString::Printf("%d", PLAYER_STATS_SPECIALTIES_1 + (i * 4) + 2)));
				if (player_bar_specialties)
				{
					player_bar_specialties->SetVisibility(ESlateVisibility::Hidden);
				}
				UTextBlock* player_rating_specialties;// = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(player_stats, FString::Printf("%d", PLAYER_STATS_SPECIALTIES_1 + (i * 4) + 3)));
				if (player_rating_specialties)
				{
					player_rating_specialties->SetVisibility(ESlateVisibility::Hidden);
					UWWUIFunctionLibrary::SetText(player_rating_specialties, (FString)"100)");
				}*/
			}
		}
		/*UUserWidget* player_status;// = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(player_status, (FString)"PlayerStatus"));
		if (player_status)
		{
			UTextBlock* player_status_text;// = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(player_status, (FString)"PLAYER_STATUS_TEXT"));
			if (player_status_text)
			{

			}
		}*/
	}
#endif // defined(RC4_ENABLE_HUD_UPDATER)
}

void RUHUDUpdater::SetKickForPointsHelp(const MabString& text)
{
#if defined(RC4_ENABLE_HUD_UPDATER)
	if (/*symbol.length() == 0 &&*/ text.length() == 0)
	{
		kick_for_points_help->SetVisibility(ESlateVisibility::Hidden);
	}
	else
	{
		//SIFUIHelpers::TextSetText(kick_for_points_help->GetChildByIndex(1), symbol.c_str());
		//SIFUIHelpers::TextSetText(kick_for_points_help->GetChildByContext("KickForHelpText"), text.c_str());
		kick_for_points_help->SetVisibility(ESlateVisibility::Visible);
	}
#endif // defined(RC4_ENABLE_HUD_UPDATER)
}

void RUHUDUpdater::SetPlaceholderTextVisible(bool /*visible*/)
{
	//if (placeholder_text)
	//	placeholder_text->SetVisible(visible);
}

#if defined(RC4_ENABLE_HUD_UPDATER)
void RUHUDUpdater::SetSpeakingTextVisible(bool visible)
{
	if (speaking_text)
	{
		speaking_text->SetVisibility(visible ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
	}
}

void RUHUDUpdater::SetSpeakingText(MabString id, MabColour colour, bool on)
{
	speaking_text->SetVisibility(on ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
	if (speaking_text && on)
	{
		//speaking_text->SetColour(colour);
		//MabUITextInterface *t = dynamic_cast<MabUITextInterface *>(speaking_text);
		/*MabUITextInterface *t = (MabUITextInterface *)(speaking_text);
		if (t)
		{
			t->SetText(id);
		}*/
	}
}
#endif // defined(RC4_ENABLE_HUD_UPDATER)

void RUHUDUpdater::SetScrumHUDVisible(bool visible)
{
	UWWUIFunctionLibrary::SetVisibility(gameplay_hud, !visible);
	UWWUIFunctionLibrary::SetVisibility(scrum_help_text, visible);
}

void RUHUDUpdater::SetScrumHelperText(const MabString& message)
{
	if (scrum_help_text)
	{
		scrum_help_text->SetText(SIFGameHelpers::GAConvertMabStringToFString(message));
		if (game_window_invalidation_box) game_window_invalidation_box->InvalidateCache();
	}
}

void RUHUDUpdater::SetProRequestFeedbackText(const MabString& message, const MabString& sub_message, const bool wasSuccess, const char* animation_name)
{
	pro_request_feedback_animation = animation_name;
	pro_request_feedback_resized = true;

	//< Load widgets. >
	UTextBlock* title_text		= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(pro_request_feedback, WWUIScreenInGameHud_UI::Title));
	UTextBlock* message_text	= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(pro_request_feedback, WWUIScreenInGameHud_UI::Message));
	UWidget* mid_size_box		= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(pro_request_feedback, WWUIScreenInGameHud_UI::MidSizeBox));
	UWidget* cap_widget			= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(pro_request_feedback, WWUIScreenInGameHud_UI::Cap));
	UWidget* cap_border			= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(pro_request_feedback, WWUIScreenInGameHud_UI::CapBorder));

	//< Apply text. >
	UWWUIFunctionLibrary::SetText(title_text,	UWWUITranslationManager::Translate(SIFGameHelpers::GAConvertMabStringToFString(message)));
	UWWUIFunctionLibrary::SetText(message_text, UWWUITranslationManager::Translate(SIFGameHelpers::GAConvertMabStringToFString(sub_message)));

	//< Hide sub-message panel if sub-message is empty. >
	UWWUIFunctionLibrary::SetVisibility(mid_size_box,	(sub_message.length() != 0));
	UWWUIFunctionLibrary::SetVisibility(cap_widget,		(sub_message.length() != 0));

	//< Color cap depending on the success result. >
	UWWUIFunctionLibrary::SetColorAndOpacity(cap_border, wasSuccess ? FColor(3, 106, 14, 255) : FColor(148, 14, 13, 255));

	//< Audio feedback needs to be played. >
	SIFAudioHelpers::PlaySoundEvent(RU_SOUND_PRO_REQUEST_FEEDBACK_TEXT);
}

bool RUHUDUpdater::IsShowingProRequestFeedback()
{
	return (pro_request_feedback) ? (pro_request_feedback->GetRenderOpacity() > 0.0f) : false;
	return false;
}

void RUHUDUpdater::SetProGoalFeedbackText(const MabString& message, const MabString& sub_message, const bool wasCompleted, const bool wasSuccess, const char* animation_name)
{
	pro_goal_feedback_text_animation = animation_name;
	pro_goal_feedback_text_resized = true;

	//< Load widgets. >
	UTextBlock* title_text		= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(pro_goal_feedback, WWUIScreenInGameHud_UI::Title));
	UTextBlock* message_text	= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(pro_goal_feedback, WWUIScreenInGameHud_UI::Message));
	UWidget* mid_size_box		= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(pro_goal_feedback, WWUIScreenInGameHud_UI::MidSizeBox));
	UWidget* cap_widget			= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(pro_goal_feedback, WWUIScreenInGameHud_UI::Cap));
	UWidget* cap_border			= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(pro_goal_feedback, WWUIScreenInGameHud_UI::CapBorder));

	//< Apply text. >
	UWWUIFunctionLibrary::SetText(title_text, UWWUITranslationManager::Translate(SIFGameHelpers::GAConvertMabStringToFString(message)));
	UWWUIFunctionLibrary::SetText(message_text, UWWUITranslationManager::Translate(SIFGameHelpers::GAConvertMabStringToFString(sub_message)));

	//< Hide sub-message panel if sub-message is empty. >
	UWWUIFunctionLibrary::SetVisibility(mid_size_box, (sub_message.length() != 0));
	UWWUIFunctionLibrary::SetVisibility(cap_widget, (sub_message.length() != 0));

	//< Color cap depending on the success result. >
	if (wasCompleted) UWWUIFunctionLibrary::SetColorAndOpacity(cap_border, wasSuccess ? FColor(3, 106, 14, 255) : FColor(148, 14, 13, 255));
	else UWWUIFunctionLibrary::SetColorAndOpacity(cap_border, FColor::White);

	//< Audio feedback needs to be played. >
	SIFAudioHelpers::PlaySoundEvent(RU_SOUND_PRO_GOAL_FEEDBACK_TEXT);
}

bool RUHUDUpdater::IsShowingProGoalFeedback()
{
	return (pro_goal_feedback) ? (pro_goal_feedback->GetRenderOpacity() > 0.0f) : false;
	return false;
}

void RUHUDUpdater::SetImpactText(const FString& message, const FString& sub_message, const FLinearColor& colour, const FString animation_name)
{
	impact_text_animation = animation_name;


	if (UWWUITextBlock* text = Cast<UWWUITextBlock>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::ImpactTextMessage)))
	{
		text->SetText(FText::FromString(message));
		impact_text_resized = true;
	}

	UTextBlock* sub_text = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::ImpactTextSubMessage));
	if (sub_text)
	{
		UWWUIFunctionLibrary::SetText(sub_text, sub_message);

		//< Hide sub message if string is empty. >
		UBorder* background = Cast<UBorder>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::ImpactTextSubMessageBorder));
		UWWUIFunctionLibrary::SetVisibility(background, (sub_message.Len() != 0));

		//wants to find the child of sub_text, but that's a text block and it wants a user widget. Fix this later
		UImage* colour_node = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::ImpactTeamColor));
		if (colour_node)
		{
			UWWUIFunctionLibrary::SetVisibility(colour_node, (sub_message.Len() != 0));
			UWWUIFunctionLibrary::SetColorAndOpacity(colour_node, colour);
		}
	}

	// Audio feedback needs to be played.
	SIFAudioHelpers::PlaySoundEvent(RU_SOUND_MATCH_IMPACT_TEXT);

	if (scoreboard) scoreboard->InvalidateCache();
}

void RUHUDUpdater::SetHelpTipVisibility(bool _Visibility)
{
	UWWUIFunctionLibrary::SetVisibility(in_game_help_text, _Visibility);
	if (gameplay_hud) gameplay_hud->InvalidateCache();
}

void RUHUDUpdater::SetPlayerInfo(RUPlayerAttributes* attributes, const MabString& additional_info)
{
	SetPlayerInfo(attributes->GetDBPlayer()->GetDbId(), attributes->GetTeam(), additional_info);
}

void RUHUDUpdater::SetPlayerInfo(unsigned int player_db_id, RUTeam* player_team, const MabString& additional_info, ARugbyCharacter* aplayer)
{
	MABASSERTMSG(player_db_id >= DB_FIRST_PLAYER_ID, "Invalid Player ID was passed in, Speak to MaxG");
	if(player_db_id < DB_FIRST_PLAYER_ID)
	{
		return;
	}

	if (player_info)
	{
		// check if ther is already something showing, in which case add to the pending queue
		if (IsHUDBusy())
		{
			deferred_player_infos.push_back(RUHUDDeferredPlayerInfo(player_db_id, player_team, additional_info));
			return;
		}

		player_info->SetVisibility(ESlateVisibility::SelfHitTestInvisible);

		RUDB_PLAYER* player = player_team->GetDbPlayerById(player_db_id);
		const RUDB_TEAM& team = player_team->GetDbTeam();
		UTextBlock* text = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(player_info, (FString)"PlayerName"));
		if (text)
		{
			MabString name;
			if (player)
			{
				player->GetCombinedName(name);
				CensorPlayerName(&team, player, name);
			}

			FString txt = SIFGameHelpers::GAConvertMabStringToFString(name);
			if (aplayer)
			{
				int jersey_number = aplayer->GetAttributes()->GetNumber();
				txt = FString::Printf(TEXT("%s (%d)"), *txt, jersey_number);
			}
			text->SetText(FText::FromString(txt));
		}


		if (UUserWidget* TemplateWidget = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(player_info, (FString)"Template")))
		{
			UUserWidget* additional_node = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(TemplateWidget, (FString)"AdditionalInfo"));
			if (additional_node)
			{
				if (UTextBlock* infoText = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(additional_node, (FString)"Info")))
				{
					infoText->SetText(FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(additional_info)));
					infoText->SetVisibility((additional_info.length() != 0) ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
				}
			}
		}

		// set logo
		UImage* logo = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(player_info, (FString)"Logo"));
		if (logo)
		{
			UWWUIFunctionLibrary::SetTexture(logo, SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetTeamLogoHUDAssetPath(player_team->GetDbTeam().GetDbId())));
		}

		// team colour
		MabColour team_colour;
		team.GetScreenFriendlyColour(team_colour);

		UImage *left_team_colour = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(player_info, (FString)"Background/LeftColour"));
		if (left_team_colour)
		{
			left_team_colour->SetBrushTintColor(FLinearColor(team_colour.r, team_colour.g, team_colour.b, team_colour.a));
		}
		UImage *right_team_colour = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(player_info, (FString)"Background/RightColour"));
		if (right_team_colour)
		{
			right_team_colour->SetBrushTintColor(FLinearColor(team_colour.r, team_colour.g, team_colour.b, team_colour.a));
		}

		player_info_resized = true;
	}
}

void RUHUDUpdater::SetGenericInfo(const FString& info, RUTeam* team, const FString& additional_info)
{
//#if defined(RC4_ENABLE_HUD_UPDATER)
	// Re-use the player info widget
	if (UOBJ_IS_VALID(player_info))
	{

		// check if ther is already something showing, in which case add to the pending queue
		if (IsHUDBusy())
		{
			MabString mabInfo = SIFGameHelpers::GAConvertFStringToMabString(info);
			MabString maxAdditionalInfo = SIFGameHelpers::GAConvertFStringToMabString(additional_info);
			deferred_player_infos.push_back(RUHUDDeferredPlayerInfo(mabInfo, team, maxAdditionalInfo));
			return;
		}

		player_info->SetVisibility(ESlateVisibility::SelfHitTestInvisible);

		UTextBlock* text = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(player_info, (FString)"PlayerName"));
		if (text)
		{
			text->SetText(FText::FromString(info));
		}

		if (UUserWidget* TemplateWidget = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(player_info, (FString)"Template")))
		{
			UUserWidget* additional_node = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(TemplateWidget, (FString)"AdditionalInfo"));
			if (additional_node)
			{
				if (UTextBlock* infoText = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(additional_node, (FString)"Info")))
				{
					infoText->SetText(FText::FromString(additional_info));
					infoText->SetVisibility(additional_info.Len() != 0 ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
				}
			}
		}

		UImage* logo = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(player_info, (FString)"Logo"));
		UImage *left_team_colour = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(player_info, (FString)"LeftColour"));
		UImage *right_team_colour = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(player_info, (FString)"RightColour"));

		if (!team)
		{
			// no team, hide team specific glyphs
			if (logo)
			{
				logo->SetVisibility(ESlateVisibility::Hidden);
			}
			if (left_team_colour)
			{
				left_team_colour->SetBrushTintColor(FLinearColor(
					RU_GENERIC_INFO_DEFAULT_EDGE_COLOUR.r,
					RU_GENERIC_INFO_DEFAULT_EDGE_COLOUR.b,
					RU_GENERIC_INFO_DEFAULT_EDGE_COLOUR.g,
					RU_GENERIC_INFO_DEFAULT_EDGE_COLOUR.a));
			}
			if (right_team_colour)
			{
				right_team_colour->SetBrushTintColor(FLinearColor(
					RU_GENERIC_INFO_DEFAULT_EDGE_COLOUR.r,
					RU_GENERIC_INFO_DEFAULT_EDGE_COLOUR.b,
					RU_GENERIC_INFO_DEFAULT_EDGE_COLOUR.g,
					RU_GENERIC_INFO_DEFAULT_EDGE_COLOUR.a));
			}
		}
		else
		{
			const RUDB_TEAM& db_team = team->GetDbTeam();
			RUDB_TEAM tempTeam = db_team;
			if (logo)
			{
				logo->SetVisibility(ESlateVisibility::Visible);
				SIFUIHelpers::ImageSetLogoHud(logo, tempTeam);
			}

			// team col
			MabColour team_colour;
			db_team.GetScreenFriendlyColour(team_colour);
			if (left_team_colour)
			{
				left_team_colour->SetBrushTintColor(FLinearColor(team_colour.r, team_colour.g, team_colour.b, team_colour.a));
				left_team_colour->SynchronizeProperties();
			}
			if (right_team_colour)
			{
				right_team_colour->SetBrushTintColor(FLinearColor(team_colour.r, team_colour.g, team_colour.b, team_colour.a));
				right_team_colour->SynchronizeProperties();
			}
		}

		player_info_resized = true;
	}
//#endif // defined(RC4_ENABLE_HUD_UPDATER)
}

///-----------------------------------------------------------------------------
/// Setup deferred call to DisplaySetTeamInfo.
///-----------------------------------------------------------------------------

void RUHUDUpdater::SetTeamInfo(const RUDB_TEAM& team0, const RUDB_TEAM& team1, const MabString& additional_info, TEAM_INFO_TYPE info_type)
{
	MABUNUSED(team1);		// inferred from team0.

	SIFGameWorld *game = game_context->GetGameWorld();
	RUTeam *team = game->GetTeam(SIDE_A);

	if (team->GetDbTeam().GetDbId() != team0.GetDbId())
		team = game->GetTeam(SIDE_B);

	deferred_player_infos.push_back(RUHUDDeferredPlayerInfo(team, additional_info, info_type, TRY_HUD_DISPLAY_DELAY));
}

///-----------------------------------------------------------------------------
/// Display deferred team info (setup by call to SetTeamInfo()).
///-----------------------------------------------------------------------------

void RUHUDUpdater::DisplaySetTeamInfo(const RUDB_TEAM& team0, const RUDB_TEAM& team1, const MabString& additional_info, TEAM_INFO_TYPE info_type)
{
	UUserWidget* TemplateWidget = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(team_info, WWUIScreenInGameHud_UI::Template));
	UUserWidget* additional_node = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(TemplateWidget, WWUIScreenInGameHud_UI::AdditionalInfo));

	//< check if there is already something showing, in which case add to the pending queue >
	if (IsHUDBusy())
	{
		RUHUDDeferredPlayerInfo& next_info = deferred_player_infos.front();
		deferred_player_infos.push_back(RUHUDDeferredPlayerInfo(info_type, next_info.team, additional_info));
		return;
	}


	//< Set data on the InGameHUD's team_info >
	if (info_type == TEAM_INFO_TYPE_TEAM_SCORE)
	{
		//< Will trigger a team_info panel animation in the SyncUpdate().. >
		team_info_resized = true;

		//< Home Team Data >=========================================================================================================
		UUserWidget* team0_panel	= Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(team_info, WWUIScreenInGameHud_UI::Team0));
		UTextBlock* home_score		= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(team0_panel, WWUIScreenInGameHud_UI::Score));
		UTextBlock* home_name		= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(team0_panel, WWUIScreenInGameHud_UI::TeamName));
		UImage* home_logo			= Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(team0_panel, WWUIScreenInGameHud_UI::Logo));
		UImage* home_colour			= Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(team0_panel, WWUIScreenInGameHud_UI::LeftColour));

		//< Set Home Score >
		FString home_score_str = SIFGameHelpers::GAConvertMabStringToFString(MabString(32, "%d", SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat(game_context->GetGameWorld()->GetTeam(SIDE_A), &RUDB_STATS_TEAM::score)).c_str());
		check(!home_score_str.IsEmpty());
		UWWUIFunctionLibrary::SetText(home_score, home_score_str);
		UWWUIFunctionLibrary::SetVisibility(home_score, true);

		//< Set Home Name >
		MabString teamName = team0.GetShortName();
		CensorTeamName(&team0, teamName);

		UWWUIFunctionLibrary::SetText(home_name, SIFGameHelpers::GAConvertMabStringToFString(teamName));		
		UWWUIFunctionLibrary::SetTexture(home_logo, SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetLogoHUDAssetPath(team0.GetLogoID())));
	
		if (home_colour)
		{
			MabColour friendly_colour;
			team0.GetScreenFriendlyColour(friendly_colour);
			UWWUIFunctionLibrary::SetColorAndOpacity(home_colour, SIFGameHelpers::GAConvertMabColorToFLinearColor(friendly_colour));
		}
		//===========================================================================================================================



		//< Away Team Data >=========================================================================================================
		UUserWidget* team1_panel	= Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(team_info, WWUIScreenInGameHud_UI::Team1));
		UTextBlock* away_score		= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(team1_panel, WWUIScreenInGameHud_UI::Score));
		UTextBlock* away_name		= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(team1_panel, WWUIScreenInGameHud_UI::TeamName));
		UImage* away_logo			= Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(team1_panel, WWUIScreenInGameHud_UI::Logo));
		UImage* away_colour			= Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(team1_panel, WWUIScreenInGameHud_UI::RightColour));

		//< Set Away Score >
		FString away_score_str = SIFGameHelpers::GAConvertMabStringToFString(MabString(32, "%d", SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat(game_context->GetGameWorld()->GetTeam(SIDE_B), &RUDB_STATS_TEAM::score)).c_str());
		UWWUIFunctionLibrary::SetText(away_score, away_score_str);
		UWWUIFunctionLibrary::SetVisibility(away_score, true);

		//< Set Away Name >
		teamName = team1.GetShortName();
		CensorTeamName(&team1, teamName);

		UWWUIFunctionLibrary::SetText(away_name, SIFGameHelpers::GAConvertMabStringToFString(teamName));
		UWWUIFunctionLibrary::SetTexture(away_logo, SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetLogoHUDAssetPath(team1.GetLogoID())));

		if (away_colour)
		{
			MabColour friendly_colour;
			team1.GetScreenFriendlyColour(friendly_colour);
			UWWUIFunctionLibrary::SetColorAndOpacity(away_colour, SIFGameHelpers::GAConvertMabColorToFLinearColor(friendly_colour));
		}
		//===========================================================================================================================

		//< Set Additional node content >============================================================================================
		if (additional_node)
		{
			if (UTextBlock* infoText = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(additional_node, (FString)"Info")))
			{
				UWWUIFunctionLibrary::SetText(infoText, SIFGameHelpers::GAConvertMabStringToFString(additional_info));
				UWWUIFunctionLibrary::SetVisibility(infoText, (additional_info.length() != 0));
			}
		}
		//===========================================================================================================================

		/*if (divider)
		{
			divider->SetTextureResourceByName("team_info_team_divider_team_score");
		}*/
	}

	//< Set data on the RunOnWindow's team_info >
	else if (info_type == TEAM_INFO_TYPE_TEAM_V_TEAM)
	{
		SIFAudioHelpers::PlaySoundEvent(RU_SOUND_INFO_PANEL_APPEARS);

		// This also needs to be set on the runon window because it is a different screen.
		UWWUIScreenTemplate* runon_window = Cast<UWWUIScreenTemplate>(gameInstance->GetCurrentScreenTemplate());

		UUserWidget* runon_team_0_node = Cast<UUserWidget>(runon_window->FindChildWidget((FString) "Team0"));
		UUserWidget* runon_team_1_node = Cast<UUserWidget>(runon_window->FindChildWidget((FString) "Team1"));
		UUserWidget* runon_additional_info = Cast<UUserWidget>(runon_window->FindChildWidget((FString)"AdditionalInfo"));

		if (runon_team_0_node)
		{
			UTextBlock* name = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(runon_team_0_node, (FString)"TeamName"));
			UImage* logo = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(runon_team_0_node, (FString)"Logo"));
			UImage* team_colour = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(runon_team_0_node, (FString)"TeamColour"));

			MabString teamName = team0.GetShortName();
			CensorTeamName(&team0, teamName);

			name->SetText(SIFGameHelpers::GAConvertMabStringToFText(teamName));

			if (team_colour)
			{
				MabColour friendly_colour;
				team0.GetPrimaryColour(friendly_colour);
				team_colour->SetBrushTintColor(FLinearColor(friendly_colour.r, friendly_colour.g, friendly_colour.b, friendly_colour.a));
				team_colour->SynchronizeProperties();
			}

			MabString hud_logo_resource = SIFGameHelpers::GAGetLogoHUDAssetPath(team0.GetLogoID());
			SIFUIHelpers::ImageSetLogoHud(logo, 0);
		}

		if (runon_team_1_node)
		{
			UTextBlock* name = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(runon_team_1_node, (FString)"TeamName"));
			UImage* logo = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(runon_team_1_node, (FString)"Logo"));
			UImage* team_colour = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(runon_team_1_node, (FString)"TeamColour"));

			MabString teamName = team1.GetShortName();
			CensorTeamName(&team1, teamName);

			name->SetText(SIFGameHelpers::GAConvertMabStringToFText(teamName));

			MabString hud_logo_resource = SIFGameHelpers::GAGetLogoHUDAssetPath(team1.GetLogoID());
			SIFUIHelpers::ImageSetLogoHud(logo, 1);

			if (team_colour)
			{
				MabColour friendly_colour;
				team1.GetPrimaryColour(friendly_colour);
				team_colour->SetBrushTintColor(FLinearColor(friendly_colour.r, friendly_colour.g, friendly_colour.b, friendly_colour.a));
				team_colour->SynchronizeProperties();
			}
		}

		if (runon_additional_info)
		{
			if (UTextBlock* info = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(runon_additional_info, (FString)"Info")))
			{
				info->SetText(FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(additional_info)));
				info->SetVisibility((additional_info.length() != 0) ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
			}

		}

		/*if (divider)
		{
			//divider->SetTextureResourceByName("team_info_team_divider_team_v_team");
		}*/

	}
	
	/*
	#rc3_legacy_platform
#if (PLATFORM_XBOXONE) || (PLATFORM_PS4)
	int opponent_team_id = SIFMatchmakingHelpers::IsHost() ? 1 : 0;
	const RUGameSettings* settings = SIFApplication::GetApplication()->GetGameWorld()->GetGameSettings();
#if !PLATFORM_PS4
	if (settings->game_settings.network_game)
#endif
	{
		MabString team_string;
		MabUINode* opponent_team_node = SIFMatchmakingHelpers::IsHost() ? team1_node : team0_node;

		const RUDB_TEAM& opponent_team = settings->team_settings[SIFMatchmakingHelpers::IsHost() ? SIDE_B : SIDE_A].team;

		if (opponent_team.IsCustom() || MabStringHelper::ToUpper(opponent_team.GetShortName()) != SIFGameHelpers::GAGetTeamName(opponent_team.GetDatabaseId()).c_str()) {
#if (PLATFORM_XBOXONE)
			if (!SIFApplication::GetApplication()->GetMatchMakingManager()->GetViewOthersPrivilege()) {
#elif (PLATFORM_PS4)
			if (SIFGeneralHelpersPS4::IsUGCRestricted(-1))
			{
#endif

				UTextBlock* name = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(opponent_team_node, (FString)"TeamName"));
				MabString team_string = MabGlobalTranslationManager::GetInstance()->Translate("[ID_XBOX_CENSORED_NAME]");
				name->SetText(FText::FromString(team_string));
				if (info_type == TEAM_INFO_TYPE_TEAM_V_TEAM) {
					MABLOGDEBUG("info_type is %i, and team string is %s", info_type, team_string.c_str());
					MabUINode* runon_window = SIFUIHelpers::GetUINode(MabString(0, "%s/%s", RugbyUIWindowNames::SIFUI_ROOT_MENU_WINDOW_NAME, SIFUI_RUNON_WINDOW_NAME).c_str());
					UUserWidget* runon_team_0_node = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(runon_window, (FString) "MatchInfo/TeamInfo/Team0"));
					UUserWidget* runon_team_1_node = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(runon_window, (FString) "MatchInfo/TeamInfo/Team1"));
					UUserWidget* opponent_runon_node = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(SIFMatchmakingHelpers, (FString)) ? runon_team_1_node : runon_team_0_node);
					UTextBlock* runon_name = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(opponent_runon_node, (FString)"TeamName"));
					runon_name->SetText(MabStringHelper::ToUpper(team_string));
				}
			}
		}
	}
#endif*/
}
	

void RUHUDUpdater::SetupMinimap()
{
	//==================================================================================================================================
	//< SETUP MINIMAP. >================================================================================================================

	minimap_node = Cast<UWWUIFixedMinimap>(UWWUIFunctionLibrary::FindChildWidget(hud_box, WWUIScreenInGameHud_UI::BP_WWUIFixedMinimap));
	if (!minimap_node || !gameInstance) return;

	minimap_node->SetWorldBounds(
		FVector2D(-(HALF_FIELD_LENGTH_WithTryZone * 100), HALF_FIELD_LENGTH_WithTryZone * 100),
		FVector2D(-(HALF_FIELD_WIDTH * 100), HALF_FIELD_WIDTH * 100));

	minimap_node->SetPerspective(18.0f);

	TArray<uint8> markerGroups;
	for (uint8 newGroup = FMinimapMarkerGroups::Start; newGroup != FMinimapMarkerGroups::Max; newGroup++)
		markerGroups.Add(newGroup);
	minimap_node->AddMarkerGroups(markerGroups, TArray<FMarkerGroup>());

	//< Setup team colors on the Minimap >
	//< Get DB Teams >
	RUDB_TEAM home_team_db_id = gameInstance->GetActiveGameWorld()->GetTeam(SIDE_A)->GetDbTeam();
	RUDB_TEAM away_team_db_id = gameInstance->GetActiveGameWorld()->GetTeam(SIDE_B)->GetDbTeam();

	//< Get DB Strips; Contains a lot of useful color information. >
	RUDB_TEAM_STRIP home_strip;
	RUDB_TEAM_STRIP away_strip;

	RUGameDatabaseManager* database_manager = gameInstance->GetGameDatabaseManager();
	database_manager->LoadData(home_strip, SIFGameHelpers::GAGetTeamStripId(SIDE_A));
	database_manager->LoadData(away_strip, SIFGameHelpers::GAGetTeamStripId(SIDE_B));

	//< Get the primary color of the teams kits. >
	FLinearColor home_color = SIFGameHelpers::GAConvertMabColorToFLinearColor(home_strip.GetPrimaryColour());
	FLinearColor away_color = SIFGameHelpers::GAConvertMabColorToFLinearColor(away_strip.GetPrimaryColour());

	//< If Primary Kit color is too similar, decide who uses their secondary color. >
	if (minimap_node->SimilarColors(home_color, away_color))
	{
		if (home_strip.GetPrimaryRatio() > away_strip.GetPrimaryRatio())
		{
			away_color = SIFGameHelpers::GAConvertMabColorToFLinearColor(away_strip.GetSecondaryColour());
		}
		else
		{
			home_color = SIFGameHelpers::GAConvertMabColorToFLinearColor(home_strip.GetSecondaryColour());
		}
	}

	//=================================================================================================================================
	//< CREATE HOME MARKERS >==========================================================================================================
	AddPlayersMarkerToGroup(gameInstance->GetActiveGameWorld()->GetTeam(SIDE_A)->GetPlayers(),		SIDE_A, home_color);
	AddPlayersMarkerToGroup(gameInstance->GetActiveGameWorld()->GetTeam(SIDE_A)->GetBenchPlayers(), SIDE_A, home_color);

	//=================================================================================================================================
	//< CREATE AWAY MARKERS >==========================================================================================================
	AddPlayersMarkerToGroup(gameInstance->GetActiveGameWorld()->GetTeam(SIDE_B)->GetPlayers(),		SIDE_B, away_color);
	AddPlayersMarkerToGroup(gameInstance->GetActiveGameWorld()->GetTeam(SIDE_B)->GetBenchPlayers(), SIDE_B, away_color);

	//=================================================================================================================================
	//< CREATE BALL MARKER >===========================================================================================================

	//< Create marker. >
	FMarkerGroup* markerGroup		= markerGroup = minimap_node->GetMarkerGroup(FMinimapMarkerGroups::Ball);
	ARugbyCharacter* ball_holder	= gameInstance->GetActiveGameWorld()->GetGameState()->GetBallHolder();
	if (ASSBall* ball = gameInstance->GetActiveGameWorld()->GetBall())
	{
		FMinimapMarkerData newMarker = FMinimapMarkerData();

		//< Set player details. >
		newMarker.actor = ball;
		newMarker.colors.Add(FLinearColor::White);

		//< Add textures from bottom to top. >
		newMarker.textureFilepaths.Add(TEXT("/Game/Rugby/cmn_con/ui/hud/mini_map/minimap_ball_highlight"));
		newMarker.textureFilepaths.Add(TEXT("/Game/Rugby/cmn_con/ui/hud/mini_map/minimap_ball"));

		//< Set marker size. >
		newMarker.size = FVector2D(45, 45);

		//< Bind Creation callback. >
		newMarker.createMarkerCallback.BindLambda([this](FMinimapMarkerDataPtr data) {CreateBallMarker(data); });
		newMarker.possessionChangedCallback.BindLambda([this](FMinimapMarkerDataPtr data) {BallMarkerPossessionChanged(data); });

		markerGroup->Add(newMarker);
	}


	//=================================================================================================================================
	//< CREATE MISSING MARKERS & DISABLE >=============================================================================================

	//< Create Markers. >
	minimap_node->CreateMissingMarkers();

	//< Hide it. >
	minimap_node->Disable(false);
}

void RUHUDUpdater::UpdateMinimap()
{
	if (!minimap_node || !minimap_node->IsValidLowLevel()) return;
	float new_minimap_target_opacity = 0.0f;

	//< Set to be translucent if player behind Minimap. >
	bool playerBehindMinimap = false;
	for (const ARugbyCharacter* home_player : gameInstance->GetActiveGameWorld()->GetTeam(SIDE_A)->GetPlayers())
	{
		if (home_player && home_player->IsPlayerControlled())
		{
			if (IsPlayerBehindMinimap(home_player, 15.0f))
				playerBehindMinimap = true;
		}
	}
	for (const ARugbyCharacter* away_player : gameInstance->GetActiveGameWorld()->GetTeam(SIDE_B)->GetPlayers())
	{
		if (away_player && away_player->IsPlayerControlled())
		{
			if (IsPlayerBehindMinimap(away_player, 15.0f))
				playerBehindMinimap = true;
		}
	}

	//< HUD is busy, fade away Minimap. >
	if (IsHUDBusy())
	{
		//< HUD is busy, hide Minimap. >
		new_minimap_target_opacity = 0.0f;
	}
	else if (playerBehindMinimap || lineout_active)
	{
		//< Controlled player is standing behind Minimap OR we're in a lineout. >
		new_minimap_target_opacity = 0.3f;
	}
	else if (!IsHUDBusy())
	{
		//< HUD is available, display Minimap. >
		new_minimap_target_opacity = 1.0f;
	}

	//< If the Minimap's target opacity has changed, update the Minimap. >
	if (minimap_target_opacity != new_minimap_target_opacity)
	{
		float targetTime = FMath::GetMappedRangeValueClamped(FVector2D(0.0f, 1.0f), FVector2D(0.0f, 0.5f), FMath::Abs(minimap_target_opacity - minimap_node->GetRenderOpacity()));
		UWWUIFunctionLibrary::StopNodeAnimation(minimap_node);
		UWWUIFunctionLibrary::NodeFadeTo(minimap_node, new_minimap_target_opacity, targetTime);
	}
	//< If no animation is currently playing BUT it's opacity is not correct, start animation. >
	else if (!UWWUIFunctionLibrary::IsPlayingNodeAnimation(minimap_node) && minimap_node->GetRenderOpacity() != minimap_target_opacity)
	{
		float targetTime = FMath::GetMappedRangeValueClamped(FVector2D(0.0f, 1.0f), FVector2D(0.0f, 0.5f), FMath::Abs(minimap_target_opacity - minimap_node->GetRenderOpacity()));
		UWWUIFunctionLibrary::StopNodeAnimation(minimap_node);
		UWWUIFunctionLibrary::NodeFadeTo(minimap_node, new_minimap_target_opacity, targetTime);
	}

	minimap_target_opacity = new_minimap_target_opacity;
	minimap_node->Update();
}

void RUHUDUpdater::UpdateMinimapPlayerPossessions()
{
	if (SIFApplication::GetApplication()->GetConnectionManager()->GetCurrentState() == ERugbyNetworkState::Disconnecting ||
		SIFApplication::GetApplication()->GetConnectionManager()->GetCurrentState() == ERugbyNetworkState::Disconnected)
	{
		return;
	}

	if (minimap_node && minimap_node->IsValidLowLevel())
		minimap_node->UpdatePossessions();
}

void RUHUDUpdater::UpdateMinimapBallPossession()
{
	if (SIFApplication::GetApplication()->GetConnectionManager()->GetCurrentState() == ERugbyNetworkState::Disconnecting ||
		SIFApplication::GetApplication()->GetConnectionManager()->GetCurrentState() == ERugbyNetworkState::Disconnected)
	{
		return;
	}

	//< Hack to get the ball possession callback to fire correctly. >
	FMarkerGroup* markerGroup = minimap_node->GetMarkerGroup(FMinimapMarkerGroups::Ball);
	if (markerGroup->IsValidIndex(0))
	{
		FMinimapMarkerData& data = (*markerGroup)[0];
		data.prevPossessed = true;

		if (minimap_node && minimap_node->IsValidLowLevel())
			minimap_node->UpdatePossessions();
	}
}

void RUHUDUpdater::AddPlayersMarkerToGroup(const SIFRugbyCharacterList& _CharacterList, SSTEAMSIDE _Side, FLinearColor _SideColour)
{
	//< Check how many markers are needed. >
	FMarkerGroup* markerGroup	= minimap_node->GetMarkerGroup((_Side == SIDE_A) ? FMinimapMarkerGroups::Home : FMinimapMarkerGroups::Away);
	int playerCount				= _CharacterList.size();

	//< Create markers. >
	for (int i = 0; i < playerCount; i++)
	{
		//< Add new marker data for each remaining player. >
		if (ARugbyCharacter* home_player = _CharacterList[i])
		{
			FMinimapMarkerData newMarker = FMinimapMarkerData();

			//< Set player details. >
			newMarker.actor = home_player;

			//< Add textures from bottom to top. >
			newMarker.textureFilepaths.Add(TEXT("/Game/Rugby/cmn_con/ui/hud/mini_map/minimap_marker_outline"));
			newMarker.textureFilepaths.Add(TEXT("/Game/Rugby/cmn_con/ui/hud/mini_map/minimap_marker"));

			//< Set marker size. >
			newMarker.size = FVector2D(25, 25);

			//< Add colors from bottom to top. >
			const ARugbyPlayerController* controller = home_player->GetRugbyPlayerController();
			if (home_player->IsPlayerControlled() && controller)
			{
				newMarker.colors.Add(RU_LOCAL_PLAYER_COLOURS[controller->GetControllerIndex()]);
				newMarker.prevPossessed = true;
			}
			else
			{
				newMarker.colors.Add(FLinearColor::White);
			}

			newMarker.colors.Add(_SideColour);

			//< Bind Creation callback. >
			newMarker.createMarkerCallback.BindLambda([this](FMinimapMarkerDataPtr data) {CreatePlayerMarker(data); });
			newMarker.possessionChangedCallback.BindLambda([this](FMinimapMarkerDataPtr data) {PlayerMarkerPossessionChanged(data); });

			markerGroup->Add(newMarker);
		}
	}
}

void RUHUDUpdater::CreatePlayerMarker(FMinimapMarkerDataPtr dataPtr)
{
	FMinimapMarkerData* data = dataPtr.Get();
	if (!data || !data->marker || !data->overlay) return;

	UCanvasPanelSlot* markerSlot = Cast<UCanvasPanelSlot>(data->marker->Slot);
	markerSlot->SetSize(data->size);

	//< Create layers from bottom to top. >
	for (int i = 0; i < data->textureFilepaths.Num(); i++)
	{
		//< Create new layer & add to overlay. >
		UImage* newImageLayer = data->marker->WidgetTree->ConstructWidget<UImage>(UImage::StaticClass());
		data->overlay->AddChild(newImageLayer);

		//< Set overlay slot settings. >
		UOverlaySlot* newLayerSlot = Cast<UOverlaySlot>(data->overlay->GetSlots()[i]);
		newLayerSlot->SetVerticalAlignment(VAlign_Fill);
		newLayerSlot->SetHorizontalAlignment(HAlign_Fill);

		newImageLayer->bDisablePixelSnapping = true;
		newImageLayer->SynchronizeProperties();

		//< Apply texture. >
		UWWUIFunctionLibrary::SetTexture(newImageLayer, data->textureFilepaths[i]);

		//Set colour...
		if (data->colors.IsValidIndex(i))
			UWWUIFunctionLibrary::SetColorAndOpacity(newImageLayer, data->colors[i]);
	}
}

void RUHUDUpdater::CreateBallMarker(FMinimapMarkerDataPtr dataPtr)
{
	FMinimapMarkerData* data = dataPtr.Get();
	if (!data || !data->marker || !data->overlay) return;

	UCanvasPanelSlot* markerSlot = Cast<UCanvasPanelSlot>(data->marker->Slot);
	markerSlot->SetSize(data->size);

	//< Create layers from bottom to top. >
	for (int i = 0; i < data->textureFilepaths.Num(); i++)
	{
		//< Create new layer & add to overlay. >
		UImage* newImageLayer = data->marker->WidgetTree->ConstructWidget<UImage>(UImage::StaticClass());
		data->overlay->AddChild(newImageLayer);

		//< Set overlay slot settings. >
		UOverlaySlot* newLayerSlot = Cast<UOverlaySlot>(data->overlay->GetSlots()[i]);
		newLayerSlot->SetVerticalAlignment(VAlign_Fill);
		newLayerSlot->SetHorizontalAlignment(HAlign_Fill);

		newImageLayer->bDisablePixelSnapping = true;
		newImageLayer->SynchronizeProperties();

		//< Apply texture. >
		UWWUIFunctionLibrary::SetTexture(newImageLayer, data->textureFilepaths[i]);

		//Set colour to highlight only.
		if (data->colors.IsValidIndex(0) && i == 0)
			UWWUIFunctionLibrary::SetColorAndOpacity(newImageLayer, data->colors[0]);
	}
}

void RUHUDUpdater::PlayerMarkerPossessionChanged(FMinimapMarkerDataPtr _dataPtr)
{
	FMinimapMarkerData* data = _dataPtr.Get();
	if (!data || !data->marker || !data->overlay) return;

	//< Get marker slot for zorder. > 
	UCanvasPanelSlot* markerSlot = Cast<UCanvasPanelSlot>(data->marker->Slot);

	//< Get new color. >
	FLinearColor newColor = FLinearColor::White;
	if (data->IsPossessed())
	{
		if (ARugbyCharacter* character = Cast<ARugbyCharacter>(data->actor))
		{
			if (ARugbyPlayerController* controller = character->GetRugbyPlayerController())
			{
				//< Update Z-Order & color. >
				markerSlot->SetZOrder(1);
				newColor = RU_LOCAL_PLAYER_COLOURS[controller->GetControllerIndex()];
			}
		}
	}
	else
	{
		markerSlot->SetZOrder(0);
	}

	//< Update bottom/highlight layer. >
	if (UImage* highlightLayer = Cast<UImage>(data->overlay->GetChildAt(0)))
	{
		UWWUIFunctionLibrary::SetColorAndOpacity(highlightLayer, newColor);
	}
}

void RUHUDUpdater::BallMarkerPossessionChanged(FMinimapMarkerDataPtr _dataPtr)
{
	FMinimapMarkerData* data = _dataPtr.Get();
	if (!data || !data->marker || !data->overlay) return;

	//< Get marker slot for zorder. > 
	if (UCanvasPanelSlot* markerSlot = Cast<UCanvasPanelSlot>(data->marker->Slot))
		markerSlot->SetZOrder(999);

	//< Get new color. >
	FLinearColor newColor = FLinearColor::White;
	if (ARugbyCharacter* ball_holder = gameInstance->GetActiveGameWorld()->GetGameState()->GetBallHolder())
	{
		if (ARugbyPlayerController* controller = ball_holder->GetRugbyPlayerController())
			newColor = RU_LOCAL_PLAYER_COLOURS[controller->GetControllerIndex()];
	}

	//< Update bottom/highlight layer. >
	if (UImage* highlightLayer = Cast<UImage>(data->overlay->GetChildAt(0)))
	{
		UWWUIFunctionLibrary::SetColorAndOpacity(highlightLayer, newColor);
	}
}

void RUHUDUpdater::BallHolderChanged(ARugbyCharacter* _newHolder, ARugbyCharacter* _oldHolder)
{
	UpdateMinimapBallPossession();
}

bool RUHUDUpdater::IsPlayerBehindMinimap(const ARugbyCharacter* _rugbyCharacter, float _leniency)
{
	if (!minimap_node || !minimap_node->IsValidLowLevel()) return false;

	ARugbyPlayerController* player = gameInstance->GetFirstLocalRugbyPlayerController();
	if (!player || !player->IsValidLowLevel()) return false;

	//< Project target to screen space. >
	FVector2D screenSpacePosition;
	if (UGameplayStatics::ProjectWorldToScreen(player, _rugbyCharacter->GetActorLocation(), screenSpacePosition, true))
	{
		float dpi_scale = UWidgetLayoutLibrary::GetViewportScale(minimap_node);
		FVector2D viewport_size = UWidgetLayoutLibrary::GetViewportSize(minimap_node);
		FVector2D mini_size = minimap_node->GetDesiredSize();

		//< Apply DPI Scaling. >
		viewport_size /= dpi_scale;
		screenSpacePosition /= dpi_scale;

		float viewport_half_x = viewport_size.X * 0.5;
		float min_map_x = viewport_half_x - _leniency - (mini_size.X * 0.5);
		float max_map_x = viewport_half_x + _leniency + (mini_size.X * 0.5);

		float min_map_y = viewport_size.Y - mini_size.Y - _leniency;
		//< Make minimap bounds extend slightly down to account for player positions being taken from their feet. >
		float max_map_y = viewport_size.Y + (mini_size.Y * 0.5) + _leniency;

		if (screenSpacePosition.X > min_map_x && screenSpacePosition.X < max_map_x)
		{
			if (screenSpacePosition.Y > min_map_y && screenSpacePosition.Y < max_map_y)
			{
				//< Hopefully that means the player is sharing screen space with the player. >
				return true;
			}
		}
	}

	return false;
}

bool RUHUDUpdater::IsHUDBusy() const
{
	return team_info_resized 
		|| player_info_resized 
		|| substitution_info_resized 
		|| UWWUIFunctionLibrary::IsPlayingAnimation(player_info)
		|| UWWUIFunctionLibrary::IsPlayingAnimation(team_info)
		|| UWWUIFunctionLibrary::IsPlayingAnimation(substitution_info)
		;
}

void RUHUDUpdater::AddSubstitutionInfo(RUTeam& team, const RUDB_PLAYER& off_player, const RUDB_PLAYER& on_player)
{
	if (substitution_info == NULL)
	{
		return;
	}

	// Ok special case here, HES Ticket #11176. We have an issue where the substitute info comes up while the score info comes up after a conversion kick
	// Since the conversion kick info is delayed by like 3.4 seconds, the IsHUDBusy() check will fail, so we check our deferred list to see if it's still upcoming, and then add this substitute info at the end.
	// TRY_SCORE_INFO
	bool defer = IsHUDBusy();
	if (!defer)
	{
		if (deferred_player_infos.size() > 0)
		{
			for (int i = 0; i < (int)deferred_player_infos.size(); i++)
			{
				if (deferred_player_infos[i].info_type == TRY_SCORE_INFO)
				{
					defer = true;
					break;
				}
			}
		}
	}

	// check if there is already something showing, in which case add to the pending queue
	if (defer)
	{
		deferred_player_infos.push_back(RUHUDDeferredPlayerInfo(&team, off_player.GetDbId(), on_player.GetDbId()));
		return;
	}

	substitution_info_resized = true;

	// We've got to figure out which side this team belongs to.
	UCanvasPanel* substitution_team = Cast<UCanvasPanel>(UWWUIFunctionLibrary::FindChildWidget(substitution_info, (team.GetSide() == SIDE_A) ? WWUIScreenInGameHud_UI::Team_0 : WWUIScreenInGameHud_UI::Team_1));
	UWWUIUserWidget* substitution_list = Cast<UWWUIUserWidget>(UWWUIFunctionLibrary::FindChildWidget(substitution_info, (team.GetSide() == SIDE_A) ? WWUIScreenInGameHud_UI::SubstitutionList_0 : WWUIScreenInGameHud_UI::SubstitutionList_1));
	UVerticalBox* substitution_list_verticle_box = Cast<UVerticalBox>(UWWUIFunctionLibrary::FindChildWidget(substitution_list, WWUIScreenInGameHud_UI::SubstitutionListVerticleBox));

	// Set the team info.
	UTextBlock* team_name_node = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(substitution_info, (team.GetSide() == SIDE_A) ? WWUIScreenInGameHud_UI::PlayerName_0 : WWUIScreenInGameHud_UI::PlayerName_1));
	check(team_name_node);
	if (team_name_node != NULL)
	{
		MabString name = team.GetDbTeam().GetShortName();
		CensorTeamName(&team.GetDbTeam(), name);

		UWWUIFunctionLibrary::SetText(team_name_node, SIFGameHelpers::GAConvertMabStringToFText(name).ToUpper());
	}

	// We've got to get the next available position in the substitution list.
	int available_index = substitution_list->GetIntProperty(RU_SUBSTITUTION_INDEX);
	if (available_index == MAX_int32) available_index = 0;

	UUserWidget* substitution_node	= Cast<UUserWidget>(substitution_list_verticle_box->GetChildAt(available_index + 1));
	UUserWidget* line_0				= Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(substitution_node, WWUIScreenInGameHud_UI::Line_0));
	UUserWidget* line_1				= Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(substitution_node, WWUIScreenInGameHud_UI::Line_1));
	UBorder* panel_color_0			= Cast<UBorder>(UWWUIFunctionLibrary::FindChildWidget(line_0, WWUIScreenInGameHud_UI::BackgroundColour));
	UBorder* panel_color_1			= Cast<UBorder>(UWWUIFunctionLibrary::FindChildWidget(line_1, WWUIScreenInGameHud_UI::BackgroundColour));

	const int max_substitution_slots = 7;
	if (available_index > max_substitution_slots)
	{
		return;
	}

	UImage* substitution_icon = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(substitution_node, (FString)"Icon"));
	if (substitution_icon)
	{
		UWWUIFunctionLibrary::SetTexture(substitution_icon, FString("team_lineup_substitution_icon"));
		substitution_icon->SetRenderTransformAngle(0.0f);
	}

	UTextBlock* on_direction = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(line_0, WWUIScreenInGameHud_UI::Direction));
	UTextBlock* off_direction = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(line_1, WWUIScreenInGameHud_UI::Direction));

	//< Set Field Direction Text >
	if (on_direction && off_direction)
	{
		UWWUIFunctionLibrary::SetText(on_direction, UWWUITranslationManager::Translate(FString("[ID_ON]")));
		UWWUIFunctionLibrary::SetText(off_direction, UWWUITranslationManager::Translate(FString("[ID_OFF]")));

		UWWUIFunctionLibrary::SetVisibility(on_direction, true);
		UWWUIFunctionLibrary::SetVisibility(off_direction, true);

		UWWUIFunctionLibrary::SetColorAndOpacity(Cast<UWidget>(on_direction), TEXT_ON_COLOUR);
		UWWUIFunctionLibrary::SetColorAndOpacity(Cast<UWidget>(off_direction), TEXT_OFF_COLOUR);
	}

	// The details of the player need to be added to the list.
	// By putting the logic into an array, we can reduce the amount of copy+paste code.
	static const RUDB_PLAYER* player_data[] = { 0, 0 };
	player_data[0] = &on_player;
	player_data[1] = &off_player;

	for (size_t i = 0; i < StaticArraySize(player_data); ++i)
	{
		UUserWidget* child = (i == 0) ? line_0 : line_1;

		UTextBlock* position_num_text = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(child, WWUIScreenInGameHud_UI::PositionNum));
		UTextBlock* player_name = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(child, WWUIScreenInGameHud_UI::PlayerName));

		//< Set position text. >
		UWWUIFunctionLibrary::SetText(position_num_text, FString::Printf(TEXT("%i."), team.GetShirtNumberFromPlayerDbId(player_data[i]->GetDbId())));

		//< Set player name text. >
		FString name = UTF8_TO_TCHAR(player_data[i]->GetLastName());
		//CensorPlayerName(&team.GetDbTeam(), player_data[i], name);
		UWWUIFunctionLibrary::SetText(player_name, name);
	}

	// Need to tint the node the correct colours.
	UWWUIFunctionLibrary::SetColorAndOpacity(Cast<UWidget>(panel_color_0), PRIMARY_ON_COLOUR);
	UWWUIFunctionLibrary::SetColorAndOpacity(Cast<UWidget>(panel_color_1), PRIMARY_OFF_COLOUR);

	// We need to increment the available index so the next substitution doesn't overwrite this node.
	++available_index;
	substitution_list->SetProperty(RU_SUBSTITUTION_INDEX, &available_index, PROPERTY_TYPE_INT);

	// Last step: Update the logo
	UImage* image = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(substitution_info, team.GetSide() == SIDE_A 
		? WWUIScreenInGameHud_UI::Logo_0 
		: WWUIScreenInGameHud_UI::Logo_1));

	FString image_resource = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetTeamLogoHUDAssetPath(team.GetDbTeam().GetDbId()));
	UWWUIFunctionLibrary::SetTexture(image, image_resource);
}

void RUHUDUpdater::AddSubstitutionInfo(RUTeam& team, const RUDB_PLAYER& player, const RU_INTERCHANGE_EVENT_TYPE interchange_type)
{
	if (substitution_info == NULL)
	{
		return;
	}

	// check if ther is already something showing, in which case add to the pending queue
	if (IsHUDBusy())
	{
		deferred_player_infos.push_back(RUHUDDeferredPlayerInfo(&team, player.GetDbId(), interchange_type));
		return;
	}

	substitution_info_resized = true;

	// We've got to figure out which side this team belongs to.
	UCanvasPanel* substitution_team = Cast<UCanvasPanel>(UWWUIFunctionLibrary::FindChildWidget(substitution_info, team.GetSide() == SIDE_A ? WWUIScreenInGameHud_UI::Team_0 : WWUIScreenInGameHud_UI::Team_1));
	UWWUIUserWidget* substitution_list = Cast<UWWUIUserWidget>(UWWUIFunctionLibrary::FindChildWidget(substitution_info, team.GetSide() == SIDE_A ? WWUIScreenInGameHud_UI::SubstitutionList_0 : WWUIScreenInGameHud_UI::SubstitutionList_1));
	UVerticalBox* substitution_list_verticle_box = Cast<UVerticalBox>(UWWUIFunctionLibrary::FindChildWidget(substitution_list, WWUIScreenInGameHud_UI::SubstitutionListVerticleBox));

	//< Set the team info. >
	UTextBlock* team_name_node = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(substitution_info, team.GetSide() == SIDE_A ? WWUIScreenInGameHud_UI::PlayerName_0 : WWUIScreenInGameHud_UI::PlayerName_1));
	if (team_name_node != NULL)
	{
		MabString name = team.GetDbTeam().GetShortName();
		CensorTeamName(&team.GetDbTeam(), name);

		UWWUIFunctionLibrary::SetText(team_name_node, SIFGameHelpers::GAConvertMabStringToFText(name));
	}


	// We've got to get the next available position in the substitution list.
	int available_index = substitution_list->GetIntProperty(RU_SUBSTITUTION_INDEX);
	if (available_index == MAX_int32) available_index = 0;

	//< +1 to skip over the spacer we're using to push the list down. >
	UUserWidget* substitution_node = Cast<UUserWidget>(substitution_list_verticle_box->GetChildAt(available_index + 1));
	UUserWidget* line_0 = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(substitution_node, WWUIScreenInGameHud_UI::Line_0));
	UUserWidget* line_1 = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(substitution_node, WWUIScreenInGameHud_UI::Line_1));

	const int max_substitution_slots = 7;
	if (available_index > max_substitution_slots)
	{
		return;
	}

	//< All of these substitutions use common elements, so we'll store them off here. >
	UBorder*	panel_color_0	= Cast<UBorder>(UWWUIFunctionLibrary::FindChildWidget(line_0, WWUIScreenInGameHud_UI::BackgroundColour));
	UTextBlock* player_name_0	= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(line_0, WWUIScreenInGameHud_UI::PlayerName));
	UTextBlock* position_num_0	= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(line_0, WWUIScreenInGameHud_UI::PositionNum));
	UTextBlock* direction_0		= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(line_0, WWUIScreenInGameHud_UI::Direction));
	UTextBlock* arrow_0			= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(line_0, WWUIScreenInGameHud_UI::substitution_arrow));

	UBorder*	panel_color_1	= Cast<UBorder>(UWWUIFunctionLibrary::FindChildWidget(line_0, WWUIScreenInGameHud_UI::BackgroundColour));
	UTextBlock* player_name_1	= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(line_1, WWUIScreenInGameHud_UI::PlayerName));
	UTextBlock* position_num_1	= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(line_1, WWUIScreenInGameHud_UI::PositionNum));
	UTextBlock* direction_1		= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(line_1, WWUIScreenInGameHud_UI::Direction));
	UTextBlock* arrow_1			= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(line_1, WWUIScreenInGameHud_UI::substitution_arrow));


	// Some of the nodes need to be hidden, and some need to be displayed.
	UWWUIFunctionLibrary::SetVisibility(direction_1, ESlateVisibility::Hidden);
	UWWUIFunctionLibrary::SetVisibility(position_num_1, ESlateVisibility::Hidden);

	//< Set bottom position text. >
	UWWUIFunctionLibrary::SetText(position_num_0, FString::Printf(TEXT("%i."), team.GetShirtNumberFromPlayerDbId(player.GetDbId())));

	//< Set bottom name text. >
	FString plName = UTF8_TO_TCHAR(player.GetLastName());
#if PLATFORM_PS4
	//CensorPlayerName(NULL, &player, plName);
#endif
	UWWUIFunctionLibrary::SetText(player_name_0, plName);

	// Now we need to figure out what the display message needs to be for the different types.
	UWWUIFunctionLibrary::SetText(player_name_1, UWWUITranslationManager::Translate(FString::Printf(TEXT("[ID_%s]"), ANSI_TO_TCHAR(RU_INTERCHANGE_EVENT_TYPE_STRINGS[interchange_type]))));

	// The colours and the logo will need to be updated depending on if the player is going on or off of the field.
	if (interchange_type == RU_INTERCHANGE_INJURY || interchange_type == RU_INTERCHANGE_SINBIN_OFF || interchange_type == RU_INTERCHANGE_SENTOFF)
	{
		// Player being sent off.
		UWWUIFunctionLibrary::SetText(direction_0, UWWUITranslationManager::Translate(FString("[ID_OFF]")));
		UWWUIFunctionLibrary::SetColorAndOpacity(Cast<UWidget>(direction_0), TEXT_OFF_COLOUR);

		// Everything needs to be tinted red.
		UWWUIFunctionLibrary::SetColorAndOpacity(Cast<UWidget>(panel_color_0), PRIMARY_OFF_COLOUR);
		UWWUIFunctionLibrary::SetColorAndOpacity(Cast<UWidget>(panel_color_1), SECONDARY_OFF_COLOUR);
	}
	else if (interchange_type == RU_INTERCHANGE_SINBIN_RETURN)
	{
		// Player returning to the field.
		// Everything needs to be tinted blue.
		UWWUIFunctionLibrary::SetColorAndOpacity(Cast<UWidget>(panel_color_0), PRIMARY_ON_COLOUR);
		UWWUIFunctionLibrary::SetColorAndOpacity(Cast<UWidget>(panel_color_1), SECONDARY_ON_COLOUR);

		UWWUIFunctionLibrary::SetText(direction_0, UWWUITranslationManager::Translate(FString("[ID_ON]")));
		UWWUIFunctionLibrary::SetColorAndOpacity(Cast<UWidget>(direction_0), TEXT_ON_COLOUR);
	}

	// We need to increment the available index so the next substitution doesn't overwrite this node.
	++available_index;
	substitution_list->SetProperty(RU_SUBSTITUTION_INDEX, &available_index, PROPERTY_TYPE_INT);
}

void RUHUDUpdater::SetRedCard(int team_idx, int count)
{
	//< Get Red Card Horizontal Box. >
	UHorizontalBox* redCardBox = Cast<UHorizontalBox>(
		UWWUIFunctionLibrary::FindChildWidget(scoreboard, team_idx == SIDE_A ? 
			WWUIScreenInGameHud_UI::HomeCardsBoxRed : 
			WWUIScreenInGameHud_UI::AwayCardsBoxRed));

	if (!redCardBox->IsValidLowLevel()) return;

	//< Get Red Card Count. >
	int& card_count = (team_idx == SIDE_A) ? red_card_num_home : red_card_num_away;
	card_count = count;

	//< Update Visibility. >
	for (int i = 0; i < redCardBox->GetChildrenCount(); i++)
	{
		UWWUIFunctionLibrary::SetVisibility(redCardBox->GetChildAt(i), (i < card_count));
	}

	if (scoreboard) scoreboard->InvalidateCache();
}

void RUHUDUpdater::SetYellowCard(int team_idx, int total_count)
{
	//< Get Yellow Card Horizontal Box. >
	UHorizontalBox* yellowCardBox = Cast<UHorizontalBox>(
		UWWUIFunctionLibrary::FindChildWidget(scoreboard, team_idx == SIDE_A ?
			WWUIScreenInGameHud_UI::HomeCardsBoxYellow :
			WWUIScreenInGameHud_UI::AwayCardsBoxYellow));

	if (!yellowCardBox->IsValidLowLevel()) return;

	//< Update Visibility. >
	for (int i = 0; i < yellowCardBox->GetChildrenCount(); i++)
	{
		UWWUIFunctionLibrary::SetVisibility(yellowCardBox->GetChildAt(i), (i < total_count));
	}

	if (scoreboard) scoreboard->InvalidateCache();
}

void RUHUDUpdater::OnTryResult(bool success, bool /*penalty_try*/, ARugbyCharacter* try_scorer)
{
	if (!success) return;

	SSTEAMSIDE team_side = try_scorer->GetAttributes()->GetTeam()->GetSide();
	UUserWidget* team_node = nullptr;
	if (team_node)
	{
		//SIFUIEffectsHelpers::NodeRunNamedAnimation(team_node, "try_awarded_animation");
	}

	ReSyncStats();

	if (team_info)
	{
		// Additional info - which half
		const std::vector<std::unique_ptr<RUTeam>>& teams = game_context->GetGameWorld()->GetTeams();

		SSGT_HALF half = game_context->GetGameWorld()->GetGameTimer()->GetHalf();
		SetTeamInfo(teams[SIDE_A]->GetDbTeam(), teams[SIDE_B]->GetDbTeam(),
			half == FIRST_HALF ? "[ID_FIRST_HALF]" : "[ID_SECOND_HALF]",
			TEAM_INFO_TYPE_TEAM_SCORE);

		UUserWidget* team_info_scoring_team_node = NULL;
		if (team_side == SIDE_A)
		{
			UUserWidget* team0_panel = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(team_info, WWUIScreenInGameHud_UI::Team0));
			UWWUIFunctionLibrary::PlayAnimation(team0_panel, FString("team_glow_animation"));
		}
		else
		{
			UUserWidget* team1_panel = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(team_info, WWUIScreenInGameHud_UI::Team1));
			UWWUIFunctionLibrary::PlayAnimation(team1_panel, FString("team_glow_animation"));
		}
	}

	// queue try scorer message straight after
	deferred_player_infos.push_back(RUHUDDeferredPlayerInfo(try_scorer->GetAttributes()->GetDBPlayer()->GetDbId(), try_scorer->GetAttributes()->GetTeam(), "[ID_TRY_SCORER]"));
}

void RUHUDUpdater::OnConversionFinish(bool success)
{
	KickForPointsFinished(success, false, false);
}

void RUHUDUpdater::OnPenaltyGoal(bool success, const FVector & /*crossed_goal_position*/)
{
	KickForPointsFinished(success, false, true);
	if (minimap_node) minimap_node->Enable();
}

void RUHUDUpdater::OnPenaltyGoalDecision(ARugbyCharacter* offending_player, const FVector& position)
{
	if (minimap_node) minimap_node->Disable();
}

void RUHUDUpdater::OnDropGoal(bool success, const FVector& /*pos*/)
{
	// ignore failed drop kicks
	if (!success)
	{
		return;
	}
	KickForPointsFinished(success, true, false);
}

void RUHUDUpdater::KickForPointsFinished(bool success, bool drop_goal, bool penalty_kick)
{
	RUTeam* team = game_context->GetGameWorld()->GetGameState()->GetLastBallHolder()->GetAttributes()->GetTeam();

	if (success)
	{
#if defined(RC4_ENABLE_HUD_UPDATER)
		UUserWidget* team_node = team->GetSide() == SIDE_A ? team0 : NULL;
		if (!team_node)
		{
			team_node = team->GetSide() == SIDE_B ? team1 : NULL;
		}
		if (team_node)
		{
			//SIFUIEffectsHelpers::NodeRunNamedAnimation(team_node, "try_awarded_animation");
		}
#endif // defined(RC4_ENABLE_HUD_UPDATER)
	}

	ReSyncStats();

	// show score regardless of successful conversion
	//if (team_info)
	{
		MabString message_title = "[ID_CONVERSION_SUCCESSFUL]";
		if (drop_goal)
		{
			message_title = "[ID_DROPGOAL_SUCCESSFUL]";
		}
		else if (penalty_kick)
		{
			message_title = success ? "[ID_PENALTY_KICK_SUCCESSFUL]" : "[ID_PENALTY_KICK_FAILED]";
		}
		else if (!success)
		{
			message_title = "[ID_CONVERSION_FAILED]";
		}

		// Additional info - which half
		const auto& teams = game_context->GetGameWorld()->GetTeams();
		SetTeamInfo(teams[SIDE_A]->GetDbTeam(), teams[SIDE_B]->GetDbTeam(), message_title, TEAM_INFO_TYPE_TEAM_SCORE);

		// if successful animate the score twinkle
		if (success)
		{
			UUserWidget* team_info_scoring_team_node = NULL;
			if (team->GetSide() == SIDE_A)
			{
				UUserWidget* team0_panel = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(team_info, WWUIScreenInGameHud_UI::Team0));
				UWWUIFunctionLibrary::PlayAnimation(team0_panel, FString("team_glow_animation"));
			}
			else
			{
				UUserWidget* team1_panel = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(team_info, WWUIScreenInGameHud_UI::Team1));
				UWWUIFunctionLibrary::PlayAnimation(team1_panel, FString("team_glow_animation"));
			}
		}
	}
}

void RUHUDUpdater::OnKnockOn(ARugbyCharacter* /*offending_player*/, bool /*from_tackle*/, const FVector& /*position*/)
{
	SetImpactText("[ID_KNOCK_ON]");
}

void RUHUDUpdater::OnConversionStart(ARugbyCharacter* player, bool isPlaceKick)
{
	MABUNUSED(isPlaceKick);
	if (player)
	{
		RUPlayerAttributes* attributes = player->GetAttributes();
		RUGamePhase phase = player->GetGameWorld()->GetGameState()->GetPhase();
		if (phase == RUGamePhase::PENALTY_SHOOT_FOR_GOAL)
		{
			SetPlayerInfo(attributes, "[ID_PENALTY_KICKER]");
		}

		if (minimap_node) minimap_node->Disable();
	}
}

// Some R7 specific kick off penalty rules
void RUHUDUpdater::RestartKickOffNotTen(ARugbyCharacter* offending_player)
{
	// Only if cutscenes are on, otherwise we get overlapping stuff
	if (game_context->GetGameWorld()->GetCutSceneManager()->GetCinematicsEnabled())
	{
		if (team_info)
		{
			RUPlayerAttributes* attributes = offending_player->GetAttributes();
			SetPlayerInfo(attributes, "[ID_RESTART_NOT_TEN]");
		}
	}

	
	SetImpactText("[ID_RESTART_NOT_TEN]");
}

#ifdef ENABLE_SEVENS_MODE
void RUHUDUpdater::RestartKickOffOffside(ARugbyCharacter* offending_player)
{
	//Only display impact text when the game state was kick off
	// Only if cutscenes are on, otherwise we get overlapping stuff
	if (game_context->GetGameWorld()->GetCutSceneManager()->GetCinematicsEnabled())
	{
		if (offending_player->GetGameWorld()->GetGameState()->GetPhase() == RUGamePhase::KICK_OFF)
		{
			SetImpactText("[ID_KICKOFF_PLAYER_OFFSIDE]");
		}
	}

	//if(team_info)
	{
		RUPlayerAttributes* attributes = offending_player->GetAttributes();
		SetPlayerInfo(attributes, "[ID_KICKOFF_PLAYER_OFFSIDE]");
	}
}

void RUHUDUpdater::RestartKickOffDirectlyIntoTouch(const FVector& /*position*/, ARugbyCharacter* offending_player)
{
	// Only if cutscenes are on, otherwise we get overlapping stuff
	if (game_context->GetGameWorld()->GetCutSceneManager()->GetCinematicsEnabled())
	{
		SetImpactText("[ID_KICKOFF_DIRECT_INTO_TOUCH]");
	}

	//if(team_info)
	{
		RUPlayerAttributes* attributes = offending_player->GetAttributes();
		SetPlayerInfo(attributes, "[ID_KICKOFF_DIRECT_INTO_TOUCH]");
	}
}

void RUHUDUpdater::RestartKickOffIntoInGoal(ARugbyCharacter* offending_player)
{
	// Only if cutscenes are on, otherwise we get overlapping stuff
	if (game_context->GetGameWorld()->GetCutSceneManager()->GetCinematicsEnabled())
	{
		SetImpactText("[ID_BALL_FORCED]");
	}

	//if(team_info)
	{
		RUPlayerAttributes* attributes = offending_player->GetAttributes();
		SetPlayerInfo(attributes, "[ID_BALL_FORCED]");
	}
}
#endif

void RUHUDUpdater::NotifyFreeKickAwarded(ARugbyCharacter*)
{
	//if(team_info)
	{
		RUPlayerAttributes* attributes = game_context->GetGameWorld()->GetGameState()->GetAttackingTeam()->GetPlayKicker()->GetAttributes();
		if (game_context->GetGameWorld()->GetGameState()->GetMarkingRugbyCharacter())
		{
			attributes = game_context->GetGameWorld()->GetGameState()->GetMarkingRugbyCharacter()->GetAttributes();
		}
		SetPlayerInfo(attributes, "[ID_PHASE_DECISION_FREE_KICK]");
	}
}

void RUHUDUpdater::NotifyProGoalIncrement(MabString& goalTitle, MabString& updateValue, bool goalCompleted, bool wasSuccess)
{
	MABLOGDEBUG("RUHUDUpdater::NotifyProGoalIncrement()");

	SetProGoalFeedbackText(goalTitle, updateValue, goalCompleted, wasSuccess);
}

void RUHUDUpdater::ScrumReset(RUTeam* team, ScrumResetContext reset_context)
{
	RUTeam* opposition_team = game_context->GetGameWorld()->GetTeam((SSTEAMSIDE)(1 - team->GetSide()));
	if (reset_context == SRSC_WHEELED)
	{
		SetImpactText("[ID_SCRUM_RESTART_WHEELED]");
	}
	else if (reset_context == SRSC_COLLAPSED)
	{
		MabString teamName = opposition_team->GetDbTeam().GetShortName();
		CensorTeamName(&opposition_team->GetDbTeam(), teamName);

		SetImpactText("[ID_SCRUM_RESTART_COLLAPSED]", ConvertMabStringToFString(teamName));
	}
}

//===============================================================================
//===============================================================================
void RUHUDUpdater::SetConversionHelpVisible(int index)
{
	UWWUIFunctionLibrary::SetVisibility(conversion_place_help_text,		false);
	UWWUIFunctionLibrary::SetVisibility(conversion_aim_help_text,		false);
	UWWUIFunctionLibrary::SetVisibility(conversion_accuracy_help_text,	false);
	UWWUIFunctionLibrary::SetVisibility(conversion_power_help_text,		false);

	switch (index)
	{
	case RURoleShootForGoal::KFP_BALL_PLACE:
		UWWUIFunctionLibrary::SetVisibility(conversion_place_help_text,		true);
		break;
	case RURoleShootForGoal::KFP_AIM:
		UWWUIFunctionLibrary::SetVisibility(conversion_aim_help_text,		true);
		break;
	case RURoleShootForGoal::KFP_AIM_ACCURACY:
		UWWUIFunctionLibrary::SetVisibility(conversion_accuracy_help_text,	true);
		break;
	case RURoleShootForGoal::KFP_POWER:
		UWWUIFunctionLibrary::SetVisibility(conversion_power_help_text,		true);
		break;
	}
}

void RUHUDUpdater::CancelActiveInfoPanels()
{
	UWWUIFunctionLibrary::StopAnimation(team_info);
	UWWUIFunctionLibrary::StopAnimation(substitution_info);

	//< Ensure player info panel is hidden. >
	UWWUIFunctionLibrary::StopAnimation(player_info);
	UWidget* baseWidget = Cast<UWidget>(UWWUIFunctionLibrary::FindChildWidget(player_info, WWUIScreenInGameHud_UI::Template));
	UWWUIFunctionLibrary::SetOpacity(baseWidget, 0.0f);

	//< Ensure team info panel is hidden. >
	UWWUIFunctionLibrary::StopAnimation(team_info);
	baseWidget = Cast<UWidget>(UWWUIFunctionLibrary::FindChildWidget(team_info, WWUIScreenInGameHud_UI::Template));
	UWWUIFunctionLibrary::SetOpacity(baseWidget, 0.0f);

	//< Ensure substitution info panels are hidden. >
	UWWUIFunctionLibrary::StopAnimation(substitution_info);
	UWWUIFunctionLibrary::SetOpacity(UWWUIFunctionLibrary::FindChildWidget(substitution_info, WWUIScreenInGameHud_UI::LeftTeam), 0.0f);
	UWWUIFunctionLibrary::SetOpacity(UWWUIFunctionLibrary::FindChildWidget(substitution_info, WWUIScreenInGameHud_UI::RightTeam), 0.0f);

	UVerticalBox* homeSubListBox = Cast<UVerticalBox>(UWWUIFunctionLibrary::FindChildWidget(UWWUIFunctionLibrary::FindChildWidget(substitution_info, WWUIScreenInGameHud_UI::SubstitutionList_0), WWUIScreenInGameHud_UI::SubstitutionListVerticleBox));
	UVerticalBox* awaySubListBox = Cast<UVerticalBox>(UWWUIFunctionLibrary::FindChildWidget(UWWUIFunctionLibrary::FindChildWidget(substitution_info, WWUIScreenInGameHud_UI::SubstitutionList_1), WWUIScreenInGameHud_UI::SubstitutionListVerticleBox));

	//< Only display fields with data. >
	for (size_t j = 0; j < homeSubListBox->GetChildrenCount(); ++j)
	{
		if (UUserWidget* substitution_child = Cast<UUserWidget>(homeSubListBox->GetChildAt(j + 1)))
		{
			// We only want it to be visible if we've got something to show.
			UWWUIFunctionLibrary::StopAnimation(substitution_child);
			UWWUIFunctionLibrary::SetOpacity(substitution_child, 0.0f);
		}
	}
	//< Only display fields with data. >
	for (size_t j = 0; j < awaySubListBox->GetChildrenCount(); ++j)
	{
		if (UUserWidget* substitution_child = Cast<UUserWidget>(awaySubListBox->GetChildAt(j + 1)))
		{
			// We only want it to be visible if we've got something to show.
			UWWUIFunctionLibrary::StopAnimation(substitution_child);
			UWWUIFunctionLibrary::SetOpacity(substitution_child, 0.0f);
		}
	}
}

void RUHUDUpdater::Advantage(RUTeam* team_with_adv)
{
	FLinearColor color;
#ifdef USE_TEAM_COLOURS_FOR_IMPACT_TEXT
	MabColour mabColor = MabColour::Zero;
	team_with_adv->GetDbTeam().GetPrimaryColour(mabColor);
	color = FLinearColor(mabColor.r, mabColor.g, mabColor.b, mabColor.a);
#else
	SSHumanPlayer* human = team_with_adv->GetHumanPlayer(0);
	if (human)
	{
		colour = human->IsNetworkPlayer() ? RU_REMOTE_PLAYER_COLOURS[human->GetIndex()] : RU_LOCAL_PLAYER_COLOURS[human->GetIndex()];
	}
#endif

	MabString teamName = team_with_adv->GetDbTeam().GetShortName();
	CensorTeamName(&team_with_adv->GetDbTeam(), teamName);

	SetImpactText("[ID_ADVANTAGE_TO]", SIFGameHelpers::GAConvertMabStringToFString(teamName), color, RU_IMPACT_ANIMATION_SLIDE_IN);
}


void RUHUDUpdater::AdvantageEnded(ARugbyCharacter*)
{
	if (!impact_text) return;

	//< If displaying tackler text, fade out. >
	UWWUITextBlock* impact_text_node = Cast<UWWUITextBlock>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::ImpactTextMessage));
	if (impact_text_node->GetCachedText().ToString().Compare("[ID_ADVANTAGE_TO]", ESearchCase::IgnoreCase) == 0)
	{
		UWWUIFunctionLibrary::NodeFadeTo(impact_text, 0.0f, 0.5f);
	}
}

void RUHUDUpdater::LineoutSignalled(FVector, bool)
{
	if (!half_or_full_time)
	{
		SetImpactText("[ID_LINEOUT_SIGNALLED]");
		lineout_active = true;
	}
}

void RUHUDUpdater::TouchScrumSignalled(FVector, bool)
{
	if (!half_or_full_time)
	{
		//SetImpactText("[ID_LINEOUT_SIGNALLED]");
		//lineout_active = true;
	}
}

void RUHUDUpdater::LineoutThrow(bool illegal, const FVector&, ARugbyCharacter*, THROW_TYPE, ARugbyCharacter*)
{
	lineout_active = false;
	if (illegal)
	{
		SetImpactText("[ID_LINEOUT_ILLEGAL_THROW]");
	}
}

void RUHUDUpdater::LineoutThrowNRCFault()
{
	SetImpactText("[ID_LINEOUT_ILLEGAL_THROW]");
}

void RUHUDUpdater::LineoutTimeout()
{
	SIFGameWorld* game = game_context->GetGameWorld();

	if (game && game->GetGameState()->GetPhase() == RUGamePhase::LINEOUT)
	{
		SetImpactText("[ID_LINEOUT_TIMEOUT]");
	}
}

void RUHUDUpdater::LineoutFinished()
{
	lineout_active = false;
}

void RUHUDUpdater::ForwardPass(ARugbyCharacter*, ARugbyCharacter*)
{
	SetImpactText("[ID_FORWARD_PASS]");
}

void RUHUDUpdater::BreakdownShouldRelease(ARugbyCharacter* contesting_defender, ARugbyCharacter* /*breakdown_holder*/)
{
	FLinearColor color;
#ifdef USE_TEAM_COLOURS_FOR_IMPACT_TEXT
	MabColour mabColor = MabColour::Zero;
	contesting_defender->GetAttributes()->GetTeam()->GetDbTeam().GetPrimaryColour(mabColor);
	color = FLinearColor(mabColor.r, mabColor.g, mabColor.b, mabColor.a);
#else
	SSHumanPlayer* human = contesting_defender->GetAttributes()->GetTeam()->GetHumanPlayer(0);
	if (human)
	{
		colour = human->IsNetworkPlayer() ? RU_REMOTE_PLAYER_COLOURS[human->GetIndex()] : RU_LOCAL_PLAYER_COLOURS[human->GetIndex()];
	}
#endif

	MabString teamName = contesting_defender->GetAttributes()->GetTeam()->GetDbTeam().GetShortName();
	CensorTeamName(&contesting_defender->GetAttributes()->GetTeam()->GetDbTeam(), teamName);

	SetImpactText("[ID_TACKLER_RELEASE]", SIFGameHelpers::GAConvertMabStringToFString(teamName), color, RU_IMPACT_ANIMATION_SLIDE_IN);
}

void RUHUDUpdater::HideTacklerReleaseText()
{
	if (!impact_text)
	{
		return;
	}

	//< If displaying tackler text, fade out. >
	UWWUITextBlock* impact_text_node = Cast<UWWUITextBlock>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::ImpactTextMessage));
	if (impact_text_node->GetCachedText().ToString().Compare("[ID_TACKLER_RELEASE]", ESearchCase::IgnoreCase) == 0)
	{
		UWWUIFunctionLibrary::NodeFadeTo(impact_text, 0.0f, 0.5f);
	}
}

void RUHUDUpdater::OnKickOff()
{
	half_or_full_time = false;

	if (minimap_node->IsValidLowLevel())
		minimap_node->Enable(true);

	if (scoreboard->IsValidLowLevel())
	{
		scoreboard->SetVisibility(ESlateVisibility::Visible);
		scoreboard->InvalidateCache();
	}
}

void RUHUDUpdater::OnTackleCountChanged(int count)
{
	UTextBlock* tackle_count_text = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::TackleCountText));
	FString txt = "";
	if (count > 0)
	{
		const TCHAR* ext = TEXT("th");
		switch (count)
		{
			case 1:
				ext = TEXT("st");
				break;
			case 2:
				ext = TEXT("nd");
				break;
			case 3:
				ext = TEXT("rd");
				break;
		}
		txt = FString::Printf(TEXT("%d%s"), count, ext);
	}
	else
		txt = "Zero";

	UWWUIFunctionLibrary::SetText(tackle_count_text, txt);

	// AJ NRL Meters Gained UI
	UInvalidationBox* meters_gained_box = Cast<UInvalidationBox>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::MetersGainedInvalidationBox_1));
	if (count == 5)// && meters_gained_box)
	{
		//UWWUIFunctionLibrary::SetVisibility(meters_gained_box, ESlateVisibility::Visible);
		UTextBlock* meters_gained_text = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(scoreboard, WWUIScreenInGameHud_UI::MetersGainedText));
		RUStatisticsSystem* statsSystem = SIFApplication::GetApplication()->GetStatisticsSystem();
		if (statsSystem && meters_gained_text)
		{
			UWWUIFunctionLibrary::SetVisibility(meters_gained_box, ESlateVisibility::Visible);
			FString metersText = FString::Printf(TEXT("Meters Gained: %.2f m"), statsSystem->GetCurrentSetMetersGained());
			UWWUIFunctionLibrary::SetText(meters_gained_text, metersText);
		}
	}
	else if (meters_gained_box)
	{
		UWWUIFunctionLibrary::SetVisibility(meters_gained_box, ESlateVisibility::Collapsed);
	}
}

void RUHUDUpdater::OnHalfTime()
{
	half_or_full_time = true;
	CheckImpactTextStatus();
}

void RUHUDUpdater::OnFullTime()
{
	half_or_full_time = true;
	CheckImpactTextStatus();
}

void RUHUDUpdater::CheckImpactTextStatus()
{
	// If the impact text is set to happen.
	if (impact_text_resized)
	{
		//	If a lineout is set to happen.
		if (lineout_active)
		{
			impact_text_resized = false;
			lineout_active = false;
		}
	}
}

//	Call this when we want to show the user when golden point has started.
void RUHUDUpdater::ShowGoldenPoint()
{
	//SetImpactText("[ID_GOLDEN_POINT]");
}

void RUHUDUpdater::BreakdownEnd(ARugbyCharacter* /*breakdown_holder*/)
{
	HideTacklerReleaseText();
}

void RUHUDUpdater::RuckBallReleased(RUTeam * /*team*/, const FVector & /*position*/, float /*time*/)
{
	HideTacklerReleaseText();
}

void RUHUDUpdater::SetOffscreenMarkerVisible(EHumanPlayerSlot player_index, bool visible)
{
	MABASSERT((int)player_index >= 0 && (int)player_index < NUM_HUMAN_PLAYERS);
	if ((int)player_index < 0 || (int)player_index > NUM_HUMAN_PLAYERS)
		return;

	if (offscreen_markers.IsValidIndex((int)player_index))
	{
		UUserWidget* offscreen_marker_node = offscreen_markers[(int)player_index];
		if (!offscreen_marker_node)
			return;

		offscreen_marker_node->SetVisibility(visible ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
}

void RUHUDUpdater::SetOffscreenBallMarkerVisible(bool visible)
{
	if (!offscreen_ball_marker) return;
	offscreen_ball_marker->SetVisibility(visible ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
}

bool RUHUDUpdater::TwoLineIntersection(FVector2D LineAStart, FVector2D LineAEnd, FVector2D LineBStart, FVector2D LineBEnd, FVector2D& IntersectionPoint)
{
	float s1_x, s1_y, s2_x, s2_y;
	s1_x = LineAEnd.X - LineAStart.X;     s1_y = LineAEnd.Y - LineAStart.Y;
	s2_x = LineBEnd.X - LineBStart.X;     s2_y = LineBEnd.Y - LineBStart.Y;
	float s, t;
	s = (-s1_y * (LineAStart.X - LineBStart.X) + s1_x * (LineAStart.Y - LineBStart.Y)) / (-s2_x * s1_y + s1_x * s2_y);
	t = (s2_x * (LineAStart.Y - LineBStart.Y) - s2_y * (LineAStart.X - LineBStart.X)) / (-s2_x * s1_y + s1_x * s2_y);
	if (s >= 0 && s <= 1 && t >= 0 && t <= 1)
	{
		// Collision detected
		IntersectionPoint.X = LineAStart.X + (t * s1_x);
		IntersectionPoint.Y = LineAStart.Y + (t * s1_y);
		return true;
	}
	return false; // No collision
}

void RUHUDUpdater::UpdateOffscreenMarker(EHumanPlayerSlot player_index, const FLinearColor& player_colour, const FVector& world_space_position, bool offside)
{
	check((int)(int)player_index >= 0 && (int)player_index < NUM_HUMAN_PLAYERS);
	if ((int)player_index < 0 || (int)player_index > NUM_HUMAN_PLAYERS)
		return;

	//< Gather the relevant elements. >---------------------------------------------------------------------------------------------------------
	UUserWidget*			marker			= offscreen_markers.IsValidIndex((int)player_index) ? offscreen_markers[(int)player_index] : nullptr;
	UImage*					marker_arrow	= Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(marker, WWUIScreenInGameHud_UI::ArrowImage));
	ARugbyPlayerController* player			= Cast<ARugbyPlayerController>(UGameplayStatics::GetPlayerController(SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameInstance().GetWorld(), 0));
	ARugbyCameraActor*		cam				= (player->IsValidLowLevel()) ? Cast<ARugbyCameraActor>(player->GetDefaultGameCamera()) : nullptr;
	if (!cam || !player || !marker) return;

	const FVector2D ViewportSize			= UWidgetLayoutLibrary::GetViewportSize(cam) /= UWidgetLayoutLibrary::GetViewportScale(cam);
	const float		ViewportScale			= UWidgetLayoutLibrary::GetViewportScale(cam);
	const float		FADE_IN_RANGE_IN_PIXELS = 150.0f / ViewportScale;
	const float		CLAMP_PIXELS			= 75.0f / ViewportScale;

	FVector2D vpTopLeft		= FVector2D(0, 0);
	FVector2D vpTopRight	= FVector2D(ViewportSize.X, 0);
	FVector2D vpBottomLeft	= FVector2D(0, ViewportSize.Y);
	FVector2D vpBottomRight = FVector2D(ViewportSize.X, ViewportSize.Y);


	//< Update Rotation. >----------------------------------------------------------------------------------------------------------------------
	FVector PointOnPlane = UKismetMathLibrary::ProjectPointOnToPlane(world_space_position, cam->GetActorLocation(), UKismetMathLibrary::GetForwardVector(cam->GetActorRotation()));
	FVector TargetDirection = UKismetMathLibrary::Normal(UKismetMathLibrary::LessLess_VectorRotator(FVector(PointOnPlane - cam->GetActorLocation()), cam->GetActorRotation()));
	float IndicatorRotationInDegrees = UKismetMathLibrary::DegAcos(UKismetMathLibrary::Dot_VectorVector(TargetDirection, FVector(0, 0, 1)));

	/// Flip due to issue with aCos.
	if (TargetDirection.y < 0) IndicatorRotationInDegrees = (360 - IndicatorRotationInDegrees);
	marker_arrow->SetRenderTransformAngle(IndicatorRotationInDegrees);


	//< Update Position. >----------------------------------------------------------------------------------------------------------------------
	IndicatorRotationInDegrees -= 90;
	FVector2D IndicatorDirection = FVector2D(UKismetMathLibrary::DegCos(IndicatorRotationInDegrees), UKismetMathLibrary::DegSin(IndicatorRotationInDegrees));

	FVector2D IndicatorPosition = FVector2D::ZeroVector;
	FVector2D IntersectionEndPoint = (IndicatorDirection * 9000) + (ViewportSize * 0.5f);
	TwoLineIntersection(ViewportSize * 0.5, IntersectionEndPoint, vpTopLeft,	vpTopRight,		IndicatorPosition);	/// Top
	TwoLineIntersection(ViewportSize * 0.5, IntersectionEndPoint, vpTopLeft,	vpBottomLeft,	IndicatorPosition);	/// Left
	TwoLineIntersection(ViewportSize * 0.5, IntersectionEndPoint, vpTopRight,	vpBottomRight,	IndicatorPosition);	/// Right
	TwoLineIntersection(ViewportSize * 0.5, IntersectionEndPoint, vpBottomLeft, vpBottomRight,	IndicatorPosition);	/// Bottom

	IndicatorPosition.X = FMath::Clamp(IndicatorPosition.X, CLAMP_PIXELS, ViewportSize.X - CLAMP_PIXELS);
	IndicatorPosition.Y = FMath::Clamp(IndicatorPosition.Y, CLAMP_PIXELS, ViewportSize.Y - CLAMP_PIXELS);

	UCanvasPanelSlot* MarkerSlot = Cast<UCanvasPanelSlot>(marker->Slot);
	MarkerSlot->SetPosition(IndicatorPosition);


	//< Update colour & opacity. >--------------------------------------------------------------------------------------------------------------
	/// Apply player colour
	UWWUIFunctionLibrary::SetColorAndOpacity(marker_arrow, player_colour);

	/// Use project world to screen to get distance to edge of screen.
	FVector2D WorldToScreenPos;
	UGameplayStatics::ProjectWorldToScreen(SIFApplication::GetApplication()->GetFirstLocalRugbyPlayerController(), world_space_position, WorldToScreenPos);
	WorldToScreenPos /= ViewportScale;

	float alpha = FMath::Min(
		((ViewportSize.X * 0.5f) - FMath::Abs(WorldToScreenPos.x - (ViewportSize.X * 0.5f))) / FADE_IN_RANGE_IN_PIXELS,
		((ViewportSize.Y * 0.5f) - FMath::Abs(WorldToScreenPos.y - (ViewportSize.Y * 0.5f))) / FADE_IN_RANGE_IN_PIXELS);
	// GGS Nick PS4 TODO - changed this to compile for PS4, seems ok
	//FMath::Clamp(alpha, 0.0f, 1.0f);
	FMath::ClampWrap(alpha, 0.0f, 1.0f);
	alpha = 1.0f - alpha;

	marker->SetRenderOpacity(alpha);


	//< NOTE: Implement offside image. >
}

void RUHUDUpdater::UpdateOffscreenBallMarker(const FLinearColor& colour, const FVector& world_space_position)
{
	//< Off-screen marker rotates around the screen to show where your selected player is if he is off-screen >
	bool pro_mode = game_context->GetGameWorld()->GetGameSettings().game_settings.GetIsAProMode();
	if (!pro_mode) return;

	FVector ue_world_space_pos = TransformUtility::ConvertScaledToUnreal(world_space_position);

	//< Gather the relevant elements. >---------------------------------------------------------------------------------------------------------
	UUserWidget*			marker = offscreen_ball_marker;
	UImage*					marker_arrow = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(marker, WWUIScreenInGameHud_UI::ArrowImage));
	ARugbyPlayerController* player = Cast<ARugbyPlayerController>(UGameplayStatics::GetPlayerController(SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameInstance().GetWorld(), 0));
	ARugbyCameraActor*		cam = (player->IsValidLowLevel()) ? Cast<ARugbyCameraActor>(player->GetDefaultGameCamera()) : nullptr;
	if (!cam || !player || !marker) return;

	const FVector2D ViewportSize = UWidgetLayoutLibrary::GetViewportSize(cam) /= UWidgetLayoutLibrary::GetViewportScale(cam);
	const float		ViewportScale = UWidgetLayoutLibrary::GetViewportScale(cam);
	const float		FADE_IN_RANGE_IN_PIXELS = 150.0f / ViewportScale;
	const float		CLAMP_PIXELS = 75.0f / ViewportScale;

	FVector2D vpTopLeft		= FVector2D(0, 0);
	FVector2D vpTopRight	= FVector2D(ViewportSize.X, 0);
	FVector2D vpBottomLeft	= FVector2D(0, ViewportSize.Y);
	FVector2D vpBottomRight = FVector2D(ViewportSize.X, ViewportSize.Y);


	//< Update Rotation. >----------------------------------------------------------------------------------------------------------------------
	FVector PointOnPlane = UKismetMathLibrary::ProjectPointOnToPlane(ue_world_space_pos, cam->GetActorLocation(), UKismetMathLibrary::GetForwardVector(cam->GetActorRotation()));
	FVector TargetDirection = UKismetMathLibrary::Normal(UKismetMathLibrary::LessLess_VectorRotator(FVector(PointOnPlane - cam->GetActorLocation()), cam->GetActorRotation()));
	float IndicatorRotationInDegrees = UKismetMathLibrary::DegAcos(UKismetMathLibrary::Dot_VectorVector(TargetDirection, FVector(0, 0, 1)));

	/// Flip due to issue with aCos.
	if (TargetDirection.y < 0) IndicatorRotationInDegrees = (360 - IndicatorRotationInDegrees);
	marker_arrow->SetRenderTransformAngle(IndicatorRotationInDegrees);


	//< Update Position. >----------------------------------------------------------------------------------------------------------------------
	IndicatorRotationInDegrees -= 90;
	FVector2D IndicatorDirection = FVector2D(UKismetMathLibrary::DegCos(IndicatorRotationInDegrees), UKismetMathLibrary::DegSin(IndicatorRotationInDegrees));

	FVector2D IndicatorPosition = FVector2D::ZeroVector;
	FVector2D IntersectionEndPoint = (IndicatorDirection * 9000) + (ViewportSize * 0.5f);
	TwoLineIntersection(ViewportSize * 0.5, IntersectionEndPoint, vpTopLeft, vpTopRight, IndicatorPosition);	/// Top
	TwoLineIntersection(ViewportSize * 0.5, IntersectionEndPoint, vpTopLeft, vpBottomLeft, IndicatorPosition);	/// Left
	TwoLineIntersection(ViewportSize * 0.5, IntersectionEndPoint, vpTopRight, vpBottomRight, IndicatorPosition);	/// Right
	TwoLineIntersection(ViewportSize * 0.5, IntersectionEndPoint, vpBottomLeft, vpBottomRight, IndicatorPosition);	/// Bottom

	IndicatorPosition.X = FMath::Clamp(IndicatorPosition.X, CLAMP_PIXELS, ViewportSize.X - CLAMP_PIXELS);
	IndicatorPosition.Y = FMath::Clamp(IndicatorPosition.Y, CLAMP_PIXELS, ViewportSize.Y - CLAMP_PIXELS);

	UCanvasPanelSlot* MarkerSlot = Cast<UCanvasPanelSlot>(marker->Slot);
	MarkerSlot->SetPosition(IndicatorPosition);


	//< Update colour & opacity. >--------------------------------------------------------------------------------------------------------------
	/// Apply player colour
	UWWUIFunctionLibrary::SetColorAndOpacity(marker_arrow, colour);

	/// Use project world to screen to get distance to edge of screen.
	FVector2D WorldToScreenPos;
	UGameplayStatics::ProjectWorldToScreen(SIFApplication::GetApplication()->GetFirstLocalRugbyPlayerController(), ue_world_space_pos, WorldToScreenPos);
	WorldToScreenPos /= ViewportScale;

	float alpha = FMath::Min(
		((ViewportSize.X * 0.5f) - FMath::Abs(WorldToScreenPos.x - (ViewportSize.X * 0.5f))) / FADE_IN_RANGE_IN_PIXELS,
		((ViewportSize.Y * 0.5f) - FMath::Abs(WorldToScreenPos.y - (ViewportSize.Y * 0.5f))) / FADE_IN_RANGE_IN_PIXELS);
	// GGS Nick PS4 TODO - changed this to compile for PS4, seems ok
	//FMath::Clamp(alpha, 0.0f, 1.0f);
	FMath::ClampWrap(alpha, 0.0f, 1.0f);
	alpha = 1.0f - alpha;

	marker->SetRenderOpacity(alpha);
}


void RUHUDUpdater::SetCountdownClockVisible(bool visible)
{
	//< Show/Hide the Countdown Clock. >
	countdown_clock_visible = visible;
	UWWUIFunctionLibrary::SetVisibility(countdown_clock, visible);

	if (countdown_clock_spacer)
	{
		if (SIFApplication::GetApplication()->GetMatchGameSettings() && SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.network_game)
		{
			countdown_clock_spacer->SetVisibility(ESlateVisibility::Visible);
		}
		else
		{
			countdown_clock_spacer->SetVisibility(ESlateVisibility::Collapsed);
		}
	}
}


void RUHUDUpdater::UpdateCountdownClock(float delta_time)
{
	// update the countdown clock this is made of two main parts, a line that
	// rotates around (the clock hand) and also an image that gets revealed
	// with the line to make the remaining time have more pop

	countdown_clock_current_time += delta_time;
	float time_percentage = GetCountdownPercentage();
	if (countdown_clock) countdown_clock->SetFillPercentage(time_percentage);
}

void RUHUDUpdater::StartCountdownClock(float total_time)
{
	/// Start the countdown clock ticking and also reset it back to 0.0 if it was already running
	countdown_clock_total_time = total_time;
	countdown_clock_current_time = 0.0f;
}


void RUHUDUpdater::UpdateDeferredPlayerInfo(float delta_time)
{
	// early out if no items pending
	if (deferred_player_infos.empty())
		return;

	// check if there is currently any info showing
	if (IsHUDBusy())
		return;

	// otherwise show the new items
	RUHUDDeferredPlayerInfo& next_info = deferred_player_infos.front();

	next_info.defer_timer -= delta_time;
	if (next_info.defer_timer > 0.0f)
		return;

	switch (next_info.info_type)
	{
	case PLAYER_INFO_DB:
		SetPlayerInfo(next_info.player_db_id_1, next_info.team, next_info.additional_info);
		break;

	case PLAYER_INFO_NO_DB:
		SetGenericInfo(SIFGameHelpers::GAConvertMabStringToFString(next_info.info), next_info.team, SIFGameHelpers::GAConvertMabStringToFString(next_info.additional_info));
		break;

	case TRY_SCORE_INFO:
	{
		RUTeam *opp_team = (RUTeam*)next_info.team->GetOppositionTeam();
		DisplaySetTeamInfo(next_info.team->GetDbTeam(), opp_team->GetDbTeam(), next_info.additional_info, next_info.team_info);
	}
	break;
	case PLAYER_SUBTITUTION:
	{
		if (next_info.interchange_type == RU_INTERCHANGE_NUM_TYPES)
		{
			AddSubstitutionInfo(*next_info.team, *next_info.team->GetDbPlayerById(next_info.player_db_id_1), *next_info.team->GetDbPlayerById(next_info.player_db_id_2));
		}
		else
		{
			AddSubstitutionInfo(*next_info.team, *next_info.team->GetDbPlayerById(next_info.player_db_id_1), next_info.interchange_type);
		}
	}
	break;
	}

	deferred_player_infos.erase(deferred_player_infos.begin());

}

#if (PLATFORM_XBOXONE)
void RUHUDUpdater::SetCensoredName(const RUDB_TEAM& opponent_team, UWidget* node, bool isShort /* = false*/)
{
	//#rc3_legacy_xboxone
	/*const RUGameSettings settings = SIFApplication::GetApplication()->GetGameWorld()->GetGameSettings();
	MabUITextInterface* name = MabCast<MabUITextInterface>(node);
	if(settings.game_settings.network_game)
	{

		const RUDB_TEAM& remote_team = settings.team_settings[ SIFMatchmakingHelpers::IsHost() ? SIDE_B : SIDE_A ].team;
		MABLOGDEBUG("Opponent remote team id is %d and team name is %s, iscustomer is %i",remote_team.GetDbId(),remote_team.GetShortName(),remote_team.IsCustom());
		MabString team_string;
		MABLOGDEBUG("Opponent team id is %d and team name is %s, iscustomer is %i",opponent_team.GetDbId(),opponent_team.GetShortName(),opponent_team.IsCustom());
		if (remote_team.IsCustom() || MabStringHelper::ToUpper(remote_team.GetShortName()) != SIFGameHelpers::GAGetTeamName(remote_team.GetDatabaseId()).c_str()) {
			if(!SIFApplication::GetApplication()->GetMatchMakingManager()->GetViewOthersPrivilege()){
				MabString team_string = MabGlobalTranslationManager::GetInstance()->Translate( "[ID_XBOX_CENSORED_NAME]" );
				if(!isShort)
					name->SetText(  MabStringHelper::ToUpper( team_string ));
				else
					name->SetText( MabString("CSM"));
			}
		}
	}*/
}

#elif PLATFORM_PS4

void RUHUDUpdater::SetCensoredName(const RUDB_TEAM& opponent_team, UWidget* node, bool isShort /* = false*/)
{
	//#rc3_legacy_ps4 
	/*MabUITextInterface* textNode = MabCast<MabUITextInterface>(node);
	if (1//SIFApplication::GetApplication()->GetGameWorld()->GetGameSettings().game_settings.network_game
		&& opponent_team.IsCustom()
		&& SIFGeneralHelpersPS4::IsUGCRestricted(-1)
		)
	{
		MabString name = MabGlobalTranslationManager::GetInstance()->Translate("[ID_XBOX_CENSORED_NAME]");
		if (isShort)
			name = name.substr(0,3);
		textNode->SetText(MabStringHelper::ToUpper(name));
	}*/
}

#endif

//===============================================================================
//===============================================================================
void RUHUDUpdater::ResetPauseDisabledOnlineTime()
{
	pause_disabled_online_time = PAUSE_DISABLED_ONLINE_TIME;
}

/*static*/ void RUHUDUpdater::CensorPlayerName(const RUDB_TEAM *team, const RUDB_PLAYER *player, MabString &name)
{
	//	Network game only.
	if (SIFApplication::GetApplication()->GetMatchGameSettings() && SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.network_game)
	{
		//	If the team is restricted according to UGC accounts and if the team is remote.
		if (!team || SIFGameHelpers::GAIsOnlineTeamRestricted(*team))
		{
			//	The team should be restricted so all custom players should also be restricted.
			if (!player || (player->IsCustom()))
			{
				name = SIFGameHelpers::GAConvertFStringToMabString(UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper());
			}
		}
		else
		{
			//	If the team isn't restricted.
			if (team && SIFGameHelpers::GAIsOnlineTeamRemote(*team))
			{
				//	If the remote team has custom players on a non custom team, check if the players are custom and someone is ugc restricted.
				if (!player || (player->IsCustom() && SIFApplication::GetApplication()->IsAnyUserRestricted()))
				{
					name = SIFGameHelpers::GAConvertFStringToMabString(UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper());
				}
			}
			else
			{
				//	If the team is a local custom team, check if the player is custom and local and someone is ugc restricted.
				if (!player || (player->IsCustom() && ((SIFApplication::GetApplication()->IsAnyUserRestricted() && (SIFGameHelpers::GAGetPlayerDownloadUser(player->GetDbId()) != "")) || SIFApplication::GetApplication()->IsNonPrimaryUserRestricted())))
				{
					name = SIFGameHelpers::GAConvertFStringToMabString(UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper());
				}
			}
		}
	}
	else
	{
		//	Non network game.
		if (!player || (player->IsCustom() && ((SIFApplication::GetApplication()->IsAnyUserRestricted() && (SIFGameHelpers::GAGetPlayerDownloadUser(player->GetDbId()) != "")) || SIFApplication::GetApplication()->IsNonPrimaryUserRestricted())))
		{
			name = SIFGameHelpers::GAConvertFStringToMabString(UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper());
		}
	}
}
/*static*/ void RUHUDUpdater::CensorPlayerName(const RL3DB_TEAM *team, RL3DB_PLAYER *player, MabString &name)
{
	//	This should only be called in offline games.
	if (!player || (player->GetIsCustom() && ((SIFApplication::GetApplication()->IsAnyUserRestricted() && (player->GetDownloadIdUser() != "")) || SIFApplication::GetApplication()->IsNonPrimaryUserRestricted())))
	{
		name = SIFGameHelpers::GAConvertFStringToMabString(UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper());
	}
}

/*static*/ void RUHUDUpdater::CensorPlayerName(int teamId, int playerId, MabString &name)
{
	if (playerId < 0)
	{
		RUHUDUpdater::CensorPlayerName(name);
		return;
	}
	
	RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();
	RUDB_PLAYER p;
	database_manager->LoadData(p,playerId);
	RUHUDUpdater::CensorPlayerName(NULL,&p,name);
}

/*static*/ void RUHUDUpdater::CensorTeamName(const RUDB_TEAM *team, MabString &name)
{
	//	Network game only.
	if (SIFApplication::GetApplication()->GetMatchGameSettings() && SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.network_game)
	{
		//	If the team is restricted according to UGC accounts and if the team is remote.
		if (!team || SIFGameHelpers::GAIsOnlineTeamRestricted(*team))
		{
			name = SIFGameHelpers::GAConvertFStringToMabString(UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper());
		}
	}
	else
	{
		//	Non network game.
		if (!team || (SIFGameHelpers::GAGetTeamIsCustom(team->GetDbId()) && ((SIFApplication::GetApplication()->IsAnyUserRestricted() && (SIFGameHelpers::GAGetTeamDownloadUser(team->GetDbId()) != "")) || SIFApplication::GetApplication()->IsNonPrimaryUserRestricted())))
		{
			name = SIFGameHelpers::GAConvertFStringToMabString(UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper());
		}
	}
}

/*static*/ void RUHUDUpdater::CensorTeamName(const int db_id, MabString &name)
{
	RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();
	RUDB_TEAM fullDBTeam;
	database_manager->LoadData(fullDBTeam, db_id);
	RUHUDUpdater::CensorTeamName(&fullDBTeam, name);
}

//This is only used for the team details screen in the fanhub
//as it will censor the player name if the non-primary user is restricted
/*static*/ void RUHUDUpdater::CensorPlayerName(MabString &name)
{
	if (SIFApplication::GetApplication()->IsNonPrimaryUserRestricted())
	{
		name = SIFGameHelpers::GAConvertFStringToMabString(UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper());
	}
}

//This is only used for the save boxes in the main menu
//as it will censor the team name if the non-primary user is restricted
/*static*/ void RUHUDUpdater::CensorTeamName(MabString &name)
{
	if (SIFApplication::GetApplication()->IsNonPrimaryUserRestricted())
	{
		name = SIFGameHelpers::GAConvertFStringToMabString(UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper());
	}
}

//This is only used for the save boxes in the main menu
//as it will censor the competition name if the non-primary user is restricted
/*static*/ void RUHUDUpdater::CensorCompetitionName(MabString &name)
{
	if (SIFApplication::GetApplication()->IsNonPrimaryUserRestricted())
	{
		name = SIFGameHelpers::GAConvertFStringToMabString(UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper());
	}
}

/*static*/ void RUHUDUpdater::CensorCompetitionName(const int dbid, MabString &name)
{
	RL3DB_COMPETITION_DEFINITION comp_def((unsigned short)dbid);
	// id=3ea works, id=3eb fails, so I've hacked GetIsCustom() to survive
	CensorCompetitionName(&comp_def, name);
}

/*static*/ void RUHUDUpdater::CensorCompetitionName(const RL3DB_COMPETITION_DEFINITION *comp, MabString &name)
{
	if (!comp || (comp->GetIsCustom() && (SIFApplication::GetApplication()->IsAnyUserRestricted() || SIFApplication::GetApplication()->IsNonPrimaryUserRestricted())))
	{
		name = SIFGameHelpers::GAConvertFStringToMabString(UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper());
	}
}

/*static*/ void RUHUDUpdater::CensorTeamMnemonic(const int db_id, MabString &name)
{
	RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();
	RUDB_TEAM fullDBTeam;
	database_manager->LoadData(fullDBTeam, db_id);
	RUHUDUpdater::CensorTeamMnemonic(&fullDBTeam, name);
}

void RUHUDUpdater::CensorTeamMnemonic(const RUDB_TEAM *team, MabString &name)
{
	//	Network game only.
	if (SIFApplication::GetApplication()->GetMatchGameSettings() && SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.network_game)
	{
		//	If the team is restricted according to UGC accounts and if the team is remote.
		if (!team || SIFGameHelpers::GAIsOnlineTeamRestricted(*team))
		{
			name = RU_CENSORED_MNEMONIC;
		}
	}
	else
	{
		//	Non network game.
		if (!team || (SIFGameHelpers::GAGetTeamIsCustom(team->GetDbId()) && ((SIFApplication::GetApplication()->IsAnyUserRestricted() && (SIFGameHelpers::GAGetTeamDownloadUser(team->GetDbId()) != "")) || SIFApplication::GetApplication()->IsNonPrimaryUserRestricted())))
		{
			name = RU_CENSORED_MNEMONIC;
		}
	}
}

FString RUHUDUpdater::ConvertMabStringToFString(MabString InMabString)
{
	return SIFGameHelpers::GAConvertMabStringToFString(InMabString);
}

void RUHUDUpdater::InvalidateAll()
{
	if (scoreboard)
		scoreboard->InvalidateCache();

	if (timer_invalidation_box)
		timer_invalidation_box->InvalidateCache();

	if (timer_message_invalidation_box)
		timer_message_invalidation_box->InvalidateCache();

	if (hud_box)
		hud_box->InvalidateCache();

	if (gameplay_hud)
		gameplay_hud->InvalidateCache();

	if (player_info_invalidation_box)
		player_info_invalidation_box->InvalidateCache();

	if (pro_request_feedback)
		pro_request_feedback->InvalidateCache();

	if (pro_goal_feedback)
		pro_goal_feedback->InvalidateCache();

	if (team_info)
		team_info->InvalidateCache();

	if (player_info)
		player_info->InvalidateCache();

	if (game_window_invalidation_box)
		game_window_invalidation_box->InvalidateCache();

	if (substitution_info)
	{
		substitution_info->InvalidateCache();

		UWWUIUserWidget* substitution_list = Cast<UWWUIUserWidget>(UWWUIFunctionLibrary::FindChildWidget(substitution_info, WWUIScreenInGameHud_UI::SubstitutionList_0));
		if (substitution_list)
			substitution_list->InvalidateCache();
	}
}


//#endif // defined(RC4_ENABLE_HUD_UPDATER)

RUHUDInfo::RUHUDInfo()
	: countdown_clock_total_time(0.0f)
	, countdown_clock_current_time(0.0f)
{
}

RUHUDInfo::~RUHUDInfo()
{
}

float RUHUDInfo::GetCountdownPercentage()
{
	float time_percentage = 0.0f;
	if (countdown_clock_total_time != 0.0f)
		time_percentage = countdown_clock_current_time / countdown_clock_total_time;

	time_percentage = FMath::Clamp(time_percentage, 0.0f, 1.0f);
	return time_percentage;
}