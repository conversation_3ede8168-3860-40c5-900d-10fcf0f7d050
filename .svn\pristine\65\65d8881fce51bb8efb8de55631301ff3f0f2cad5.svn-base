/*--------------------------------------------------------------
|        Copyright (C) 1997-2007 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/RUSandboxGame.h"

#include "Character/RugbyPlayerController.h"

#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/Ball/SSBall.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Character/RugbyPlayerController.h"
#include "Match/HUD/RU3DHUDManager.h"
#include "Match/RUAsyncLoadingEnable.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/RugbyUnion/TutorialMode/RUTutorialManager.h"
#include "Match/SIFGameObject.h"
#include "Match/SIFGamePauseState.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSPostEffectsManager.h"
#include "Match/SSRoleFactory.h"
#include "Utility/RURandomNumberGenerator.h"

//#rc3_legacy_include #include "MabControlActionManager.h"
//#rc3_legacy_include #include "NMMabAnimationNetwork.h"
//#rc3_legacy_include #include "RUContextualHelper.h"
//#rc3_legacy_include #include "LevelData/SIFLevelLauncher.h"
#include "SIFMissingControllerListener.h"
//#rc3_legacy_include #include "Utility/Soak/SIFSoakManager.h"
//#rc3_legacy_include #include "SIFUIHelpers.h"
//#rc3_legacy_include #include "SIFUIInputAdapter.h"
//#rc3_legacy_include #include "SIFWindowSystem.h"

#include "RugbyGameInstance.h"

static const char* SETTINGS_FILENAME = "ruged/sandbox_settings.json";

RUSandboxGame::RUSandboxGame(SIFGameWorld* sandboxWorld) 
	: mSandboxWorld(sandboxWorld)
	, mTutorialRunning(false)
{
}

RUSandboxGame::~RUSandboxGame()
{
	if (mSandboxWorld != nullptr)
	{
		mSandboxWorld->m_onWorldAwaken.Remove(m_sandboxAwakenDelegateHandle);
		mSandboxWorld->FreeAllocated();
		delete mSandboxWorld;
		mSandboxWorld = NULL;
	}
}

void RUSandboxGame::PreSyncUpdate(const MabTimeStep& realTimeStep)
{
	SIFGamePauseState* pause_state = SIFApplication::GetApplication()->GetSandboxPauseState();
	if ((!pause_state->IsGamePaused() || pause_state->IsGamePauseChangePending()) && !pause_state->IsGamePendingChangePaused())
	{
		mSandboxWorld->PreSyncUpdate(realTimeStep);
	}

	//#rc3_legacy mSandboxWorld->GetPostEffectsManager()->Update( MabMath::Min(realTimeStep.delta_time.ToSeconds(), 1.0f / 15.0f) );		// TEMP... clamp time due to non-async load.
}

void RUSandboxGame::PostSyncUpdate(const MabTimeStep& realTimeStep)
{
	SIFGamePauseState* pause_state = SIFApplication::GetApplication()->GetSandboxPauseState();
	if ((!pause_state->IsGamePaused() || pause_state->IsGamePauseChangePending()) && !pause_state->IsGamePendingChangePaused())
	{
		mSandboxWorld->PostSyncUpdate(realTimeStep);
	}
}

/// Render - renders the minigame
void RUSandboxGame::Render()
{
	//#rc3_legacy sandbox_world->Render();
}

void RUSandboxGame::SyncUpdate()
{
	mSandboxWorld->SyncUpdate();
}

void RUSandboxGame::SetupActors(const FSerialiseTrainingLevel* mTrainingLevel, bool isTutorial)
{
	if (!mSandboxWorld->IsAwake() || mTrainingLevel->agents.Num() <= 0)
	{
		return;
	}

	mSandboxWorld->GetGameState()->Reset();	// Need to be called to clear up zone position assignments

	mSandboxWorld->RestartGame();

	// First move all players on to the bench.

	for (auto& team : mSandboxWorld->GetTeams())
	{
		while (team->GetPlayers().size() != 0u)
			team->MoveToBench(team->GetPlayers().front(), PS_BENCH);

		team->ClearHumanPlayers();
	}

	mSandboxWorld->GetGameState()->SetBallHolder(NULL);

	// iterate over each player event setting the x,z position
	int team_players[NUM_TEAMS_INIT] = { 0, 0 };

	int human_player_idx = 0;

	// Work out which shirts are available (not specified in data)
	// screw that, we'll just put the position in the data
	int used_shirts_0 = 0;
	int used_shirts_1 = 0;
	int next_free_shirt_0 = 0;
	int next_free_shirt_1 = 0;

	for (auto &agents : mTrainingLevel->agents)
	{		
		int team_num = agents.teamNum;

		int shirt_num = agents.position;

		if (shirt_num != 0)
		{
			if (team_num == 0)
			{
				used_shirts_0 |= 1 << (shirt_num - 1);
			}
			else
			{
				used_shirts_1 |= 1 << (shirt_num - 1);
			}
		}
	}

	// Do setup...
	for (const FSerialiseTutorialAgentStart& agent : mTrainingLevel->agents)
	{
		const float x = agent.x.get_graph_value(0.f);
		const float z = agent.z.get_graph_value(0.f);
		const FVector pos = { x, 0.f, z };

		// setup player position

		RUTeam* team = mSandboxWorld->GetTeam(agent.teamNum);

		ARugbyCharacter* player = nullptr;

		bool is7s = false; // Nick WWS 7s to Womens 13s // team->GetIsR7ExclusiveTeam();
	
		//SRA: Until we work out why the 7s run around field doens't play nicely with the 
		//changed sandbox players, we will just use the normal positions for both 15s and 7s
		//int shirt_num = is7s ? agent.position7s : agent.position;
		int shirt_num = agent.position;
		int team_num = agent.teamNum;

		int playersPerTeam = mSandboxWorld->GetGameSettings().game_limits.GetNumberOfPlayersPerTeamR13();

		if (shirt_num == 0)
		{
			while (player == NULL && shirt_num + 1 < playersPerTeam/*NUM_PLAYERS_PER_TEAM*/)
			{
				if (team_num == 0)
				{
					while ((used_shirts_0&(1 << next_free_shirt_0)) != 0 && next_free_shirt_0 < playersPerTeam/*NUM_PLAYERS_PER_TEAM*/)
						next_free_shirt_0++;

					next_free_shirt_0++;
					MABASSERT(next_free_shirt_0 < playersPerTeam);
					shirt_num = next_free_shirt_0;
				}
				else
				{
					while ((used_shirts_1&(1 << next_free_shirt_1)) != 0 && next_free_shirt_1 < playersPerTeam/*NUM_PLAYERS_PER_TEAM*/)
						next_free_shirt_1++;

					next_free_shirt_1++;
					MABASSERT(next_free_shirt_1 < playersPerTeam);
					shirt_num = next_free_shirt_1;
				}

				//KM: Forcing the gamemode to 15s here, because low performance mode makes a number of positions are unavailable in sandbox mode (bad)
#if LOW_MEMORY_MODE
				player = team->GetBenchPlayerByPosition(PlayerPositionEnum::GetPlayerPositionFromStartingJerseyNumber(shirt_num, 0));
#else
				player = team->GetBenchPlayerByPosition(PlayerPositionEnum::GetPlayerPositionFromStartingJerseyNumber(shirt_num));
#endif
			}
		}
		else
		{
			//KM: Forcing the gamemode to 15s here, because low performance mode makes a number of positions are unavailable in sandbox mode (bad)
#if LOW_MEMORY_MODE
			player = team->GetBenchPlayerByPosition(PlayerPositionEnum::GetPlayerPositionFromStartingJerseyNumber(shirt_num, 0));
#else
			player = team->GetBenchPlayerByPosition(PlayerPositionEnum::GetPlayerPositionFromStartingJerseyNumber(shirt_num));
#endif
		}

		MABASSERTMSG(player != nullptr, "Invalid Player Shirt Number: So this player will be missing in Training field");

		if (player != nullptr)		// Due to async-loading, player is not guaranteed to have loaded yet.
		{
			RUPlayerMovement* playerMovement = player->GetMovement();					

			if (player->GetAttributes()->GetState() == PS_BENCH || player->GetAttributes()->GetState() == PS_SINBIN)
			{
				player->GetAttributes()->GetTeam()->MoveToField(player);
			}
			else
			{
				UE_LOG(LogTemp, Display, TEXT("RUSandboxGame::SetupActors: Player already in Field: '%s'"), *player->GetName());
			}


			// Set human player
			if (agent.humanPlayer == 1)
			{
				SSHumanPlayer* human = mSandboxWorld->GetHumanPlayer(static_cast<EHumanPlayerSlot>(human_player_idx++));
				if (human != nullptr)
				{
					human->SetBufferedInput(SSPackedInput());
					human->SetTeam(team);
					human->SetRugbyCharacter(player);

					if (mSandboxWorld->IsAwake())
					{
						mSandboxWorld->GetGameState()->SetBallHolder(player, true);
						mSandboxWorld->GetGameState()->SetDefaultBallOrigin(pos);
					}
				}
				else
				{
					MABBREAKMSG("Not enough humans on team to complete tutorial!");
				}
			}

			++team_players[agent.teamNum];

			player->SetMabTransform(MabMatrix::TransMatrix(pos));

			RUPlayerMovement* movement = player->GetMovement();
			movement->SetCurrentPosition(pos);
			wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
			movement->SetTargetPosition(pos);
			movement->SetCurrentFacingAngle(0);
			movement->SetCurrentSpeed(0.0f);
			movement->StopAllMovement();
			movement->ResetLean();

			// Set the role

			MABASSERT(agent.role != ERugbyFormationRole::ALL);
			if (agent.role != ERugbyFormationRole::ALL)
			{
				//#rc3_legacy todo: probably just replace this with a big table
				player->SetRole(mSandboxWorld->GetRoleFactory()->Instance(SSEVDSFormationManager::GetRoleFromEnum(agent.role)));
			}

			// TODO: Charles to tidy this up
			/*#rc3_legacy
			NMMabAnimationNetwork* network = player->GetComponent<NMMabAnimationNetwork>();
			if(network)
			{
				network->Reset();
				network->SetLevelOfDetail( player->GetGame()->GetAnimation()->MinimumLevelOfDetail(), player->GetGame()->GetAnimation()->MaximumLevelOfDetail() );
				network->UpdateLevelOfDetail( player->GetGame()->GetCamera() );
				network->BeginUpdate( 0.0f, false );
				network->UpdatePrePhysics( 0.0f );
				network->UpdatePostPhysics( 0.0f );
				network->ComputeWorldTransforms();
			}
			*/
			//#rc3_legacy_animation. Alternative to previous logic.
			player->GetMovement()->StopAllMovement();
			if (player->GetAnimation())
			{
				player->GetAnimation()->Reset();
				player->GetAnimation()->GetStateMachine().Reset();
			}

			// set all the player stats to high values if sandbox.
			RUPlayerAttributes* attributes = player->GetAttributes();
			attributes->SetGeneralKickAccuracy(1.0f);
			attributes->SetAcceleration(0.7f);
			attributes->SetSpeed(0.7f);
			attributes->SetAgility(1.0f);
			attributes->SetBreakTackleAbility(0.0f);
			attributes->SetCatchAbility(1.0f);
			attributes->SetGoalKickAccuracy(1.0f);
			attributes->SetPassAccuracy(1.0f);
			attributes->SetTackleAbility(1.0f);
			attributes->SetAggression(0.0f);
		}
	}

	mSandboxWorld->RebuildPlayerList();

	if (mSandboxWorld->IsAwake())
	{
		ARugbyCharacter* player = mSandboxWorld->GetFirstHumanPlayer()->GetRugbyCharacter();
		if (player == nullptr)
			player = mSandboxWorld->GetTeam(SIDE_A)->GetPlayer(0);

		mSandboxWorld->GetGameState()->SetBallHolder(player, true);
	}

	// Make sure human players are set up correctly
	mSandboxWorld->SetupHumanPlayersForLoading(isTutorial);
}

void RUSandboxGame::WakeSandbox()
{
	if (!mSandboxWorld)
	{
		return;
	}

	mSandboxWorld->ResetHudIndicators();

	if (!m_sandboxAwakenDelegateHandle.IsValid())
	{
		m_sandboxAwakenDelegateHandle = mSandboxWorld->m_onWorldAwaken.Add(FGameWorldDelegate::CreateRaw(this, &RUSandboxGame::OnSandboxAwaken));
	}

	URugbyGameInstance& gameInstance = mSandboxWorld->GetGameInstance();
	if (!gameInstance.IsActiveGameWorld(mSandboxWorld))
	{
		gameInstance.ChangeActiveGameWorld(mSandboxWorld);
		return;
	}

	if (!mSandboxWorld->IsAwake())
	{
		mSandboxWorld->AwakenInstance();
		return;
	}

	ResetSandbox();
}

void RUSandboxGame::OnSandboxAwaken(WORLD_ID worldId)
{
	ResetSandbox();
}

void RUSandboxGame::ResetSandbox()
{
	std::unique_ptr<FSerialiseTrainingLevel> trainingLevel = evds::construct_training_level(SETTINGS_FILENAME);

	//!!!! KM - Temporarily always select the first valid shirts for Switch
	//!!!! KM - This is also causing sliding on players in the run around if you came from a 7s match, disabling custom positins from file for the moment
#if 1 //PLATFORM_SWITCH
	if (trainingLevel)
	{
		for (auto &agents : trainingLevel->agents)
		{
			agents.position = 0;
			agents.position7s = 0;
		}
	}
#endif

	mSandboxWorld->GetRNG()->SetAssertIfUsed(false);

	SetupActors( trainingLevel.get());

	SSEVDSFormationManager* formationManager = mSandboxWorld->GetTeam(0)->GetFormationManager();
	// Set up formation... - REMOVED so that freeball assignment could occur
	//formationManager->ForceFormation("Tutorial",1);
	formationManager->Reset();
	//formationManager->UpdateLogic(1.0f);
	formationManager->MovePlayersToKickOffPositions();

	mSandboxWorld->GetRNG()->SetAssertIfUsed(true);

	// Make sure tutorial is actually clear.
	mTutorialRunning = false;
	mSandboxWorld->GetTutorialManager()->ClearTutorial();

	// Make sure play is not suspended for runaround. Cutscenes/tutorials can enable/disable as required.
	mSandboxWorld->GetRules()->SuspendPlay(false, "Reset Sandbox");
}

void RUSandboxGame::ChangeWind(FVector wind_direction, float speed)
{
	// Set wind on gameworld
	mSandboxWorld->SetupWind(wind_direction, speed);
}
