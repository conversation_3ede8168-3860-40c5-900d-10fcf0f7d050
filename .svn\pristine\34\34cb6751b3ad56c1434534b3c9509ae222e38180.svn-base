/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef SIF_PLAYER_PROFILE_CONSTANTS_H_INCLUDED
#define SIF_PLAYER_PROFILE_CONSTANTS_H_INCLUDED

///
/// This header contains SIF-level constants needed by the player profile manager. 
/// These constants were previously declared in SIFPlayerProfileManager.cpp.
///
/// <AUTHOR>
///
const int RUMBLE_MAX_STRENGTH = 3;

#if PLATFORM_XBOX360
static char PLAYER_PROFILE_PLAYER_NAME_DEFAULT[ XUSER_NAME_SIZE ];
#else
static const char PLAYER_PROFILE_PLAYER_NAME_DEFAULT[] = "[ID_PLAYER1]";
#endif

#if PLATFORM_XBOXONE
static const char PLAYER_PROFILE_FILE_PATH[] = "data\\profiles\\";
#else
static const char PLAYER_PROFILE_FILE_PATH[] = "Saved\\SaveGames\\profiles\\";
#endif

/*#if PLATFORM_WINDOWS
static const char PLAYER_PROFILE_FILE_PATH[] = "data\\profiles\\";
#elif PLATFORM_IOS
const char PLAYER_PROFILE_FILE_PATH[] = "data/profiles/";
#elif PLATFORM_WII
const char PLAYER_PROFILE_FILE_PATH[] = "nand:";
#elif PLATFORM_XBOXONE
const static char PLAYER_PROFILE_FILE_PATH[] = "fs:G:\\data\\profiles\\";
#else
static const char PLAYER_PROFILE_FILE_PATH[] = "fs:data/profiles/";
#endif*/

static const float	PLAYER_PROFILE_SENSITIVITY_DEFAULT		= 1.0f;
// Made these strings - new settings menu doesn't support ints, and after 
// (de)serialisation they all become strings anyhow.
static const float	PLAYER_PROFILE_RUMBLE_STRENGTH_DEFAULT  = 1.0f;
static const char	PLAYER_PROFILE_DIFFICULTY_DEFAULT[]		= "1";
static const char	PLAYER_PROFILE_MOVEMENT_DEFAULT[]		= "0";
static const char	PLAYER_PROFILE_CONTROLLER_DEFAULT[]		= "0";
static const bool	PLAYER_PROFILE_RUMBLE_ENABLED_DEFAULT		= true;

static const char	PLAYER_PROFILE_LOADED_PARAM[]			= "loaded";
static const char	PLAYER_PROFILE_UNLOADED_PARAM[]			= "unloaded";
static const char	PLAYER_PROFILE_CHANGED_EVENT_NAME[]		= "onprofilechanged";
static const char	PLAYER_PROFILE_AUTOLOAD_NO_DATA_PARAM[]	= "no_data";
static const char	PLAYER_PROFILE_SAVED_PARAM[]			= "saved";
static const char	PLAYER_PROFILE_SAVE_FAILED_PARAM[]		= "save_failed";
static const char	POPUP_PROFILE_SAVING[]					= "ProfileSaving";

static const int	PLAYER_PROFILE_DEFAULT_TEAM_DEFAULT		= -1;			/// -1 = use region default.
static const int	PLAYER_PROFILE_DEFAULT_SEVENS_TEAM_DEFAULT = 1165; //Brisbane womens // 1221; /// Just setting this to Australia for now.
static const char	PLAYER_PROFILE_DEFAULT_PLAYER_LABELS[]	= "NAME";

// This is the name we give to profiles so that they have something to display on PS3.
#if PLATFORM_PS4
static const char	PLAYER_PROFILE_GENERIC_PLAYER_NAME[]	= "[ID_PS4_GENERIC_PROFILE_NAME_FOR_CONTROLLER_%d]";
#elif PLATFORM_SWITCH
static const char	PLAYER_PROFILE_GENERIC_PLAYER_NAME[] = "[ID_SWITCH_GENERIC_PROFILE_NAME_FOR_CONTROLLER_%d]";
#endif

static const char	PROFILE_LONG_SAVING_POPUP[]				= "ProfileSavingLong";

static const char	DEFAULT_NAME_INTERNAL[]					= "Default";

// The name of the system event when there is a change in a profile signin state.
static const char	PROFILE_SIGNIN_STATE_CHANGE_EVENT[]		= "on_profile_signin_state_changed";

// context help defaults
static const int	PLAYER_PROFILE_CH_PASSLEFT_DEFAULT			= 3;
static const int	PLAYER_PROFILE_CH_PASSRIGHT_DEFAULT			= 3;
static const int	PLAYER_PROFILE_CH_PASSLEFT_RUCK_DEFAULT		= 3;
static const int	PLAYER_PROFILE_CH_PASSRIGHT_RUCK_DEFAULT	= 3;
static const int	PLAYER_PROFILE_CH_STARTPUNT_DEFAULT			= 3;
static const int	PLAYER_PROFILE_CH_STARTGRUBBER_DEFAULT		= 3;
static const int	PLAYER_PROFILE_CH_CHIP_DEFAULT				= 3;
static const int	PLAYER_PROFILE_CH_CHANGE_PLAYER_DEFAULT		= 3;
static const int	PLAYER_PROFILE_CH_TACKLE_STANDARD_DEFAULT	= 3;
static const int	PLAYER_PROFILE_CH_TACKLE_HEAVY_DEFAULT		= 3;
static const int	PLAYER_PROFILE_CH_FEND_DEFAULT				= 3;
static const int	PLAYER_PROFILE_CH_SIDESTEP_DEFAULT			= 3;
static const int	PLAYER_PROFILE_GAME_LENGTH_DEFAULT			= 10;
static const int	PLAYER_PROFILE_STADIUM_DEFAULT				= 5000;
static const int	PLAYER_PROFILE_WEATHER_CONDITIONS_DEFAULT	= 0;
static const bool	PLAYER_PROFILE_ONLINE_TEAM_BALANCE_DEFAULT	= false;

// settings menus
static const bool	PLAYER_PROFILE_SETTINGS_BOOL_DEFAULT		= true;
static const char	PLAYER_PROFILE_INDICATOR_STYLE_DEFAULT[]	= "HUD";
static const char	PLAYER_PROFILE_CAMERA_ANGLE_DEFAULT[]		= "";//"BEHIND_MID";
static const char	PLAYER_PROFILE_CAMERA_ANGLE_DEFAULT_VALUE[] = "3"; //represents: SIDELINE
static const bool	PLAYER_PROFILE_SETTINGS_PRO_BOOL_DEFAULT	= false;
static const char	PLAYER_PROFILE_CAMERA_ANGLE_PRO_DEFAULT[]	= "";//"PRO_BEHIND_MID";
static const char	PLAYER_PROFILE_CAMERA_ANGLE_PRO_DEFAULT_VALUE[]	= "1"; //represents: BEHIND_MID
static const int	PLAYER_PROFILE_CAMERA_PRO_BALL_LOCK_MODE_DEFAULT_VALUE = 0;

// graphics settings
static const int	PLAYER_PROFILE_DISPLAY_MODE_DEFAULT = 0;
static const char	PLAYER_PROFILE_TARGET_DISPLAY_DEFAULT[] = "";
static const char	PLAYER_PROFILE_RESOLUTION_DEFAULT[] = "";
static const int	PLAYER_PROFILE_VSYNC_DEFAULT = 0;
static const char	PLAYER_PROFILE_FRAME_RATE_LIMIT_DEFAULT[] = "";
static const int	PLAYER_PROFILE_IMAGE_QUALITY_DEFAULT = -1;
static const int	PLAYER_PROFILE_ANTI_ALISAING_DEFAULT = 2;
static const int	PLAYER_PROFILE_ANISOTROPIC_FILTERING_DEFAULT = 2;

static const bool	PLAYER_PROFILE_MOTION_BLUR_DEFAULT = true;
static const bool	PLAYER_PROFILE_LENS_FLARE_DEFAULT = true;
static const bool	PLAYER_PROFILE_CROWD_DEFAULT = true;
static const bool	PLAYER_PROFILE_SHADOWS_DEFAULT = true;
static const bool	PLAYER_PROFILE_BLOOM_DEFAULT = true;
static const bool	PLAYER_PROFILE_SSAO_DEFAULT = true;

// extras			
static const bool	PLAYER_PROFILE_SETTINGS_EXTRA_DEFAULT = false;

// sound settings
#if PLATFORM_WINDOWS
static const float	PLAYER_PROFILE_MUSIC_VOLUME_DEFAULT			= 0.5f;
static const float	PLAYER_PROFILE_SOUNDFX_VOLUME_DEFAULT		= 0.5f;
static const float	PLAYER_PROFILE_VOICEFX_VOLUME_DEFAULT		= 0.5f;
static const float	PLAYER_PROFILE_COMMENTARYFX_VOLUME_DEFAULT	= 0.5f;
static const float	PLAYER_PROFILE_CROWDFX_VOLUME_DEFAULT		= 0.5f;
#else
static const float	PLAYER_PROFILE_MUSIC_VOLUME_DEFAULT			= 1.0f;
static const float	PLAYER_PROFILE_SOUNDFX_VOLUME_DEFAULT		= 1.0f;
static const float	PLAYER_PROFILE_VOICEFX_VOLUME_DEFAULT		= 1.0f;
static const float	PLAYER_PROFILE_COMMENTARYFX_VOLUME_DEFAULT	= 1.0f;
static const float	PLAYER_PROFILE_CROWDFX_VOLUME_DEFAULT		= 1.0f;
#endif

// gameplays sliders defaults
static const float PLAYER_PROFILE_HIGH_TACKLE_FREQUENCY_MIN		= 0.0f;
static const float PLAYER_PROFILE_HIGH_TACKLE_FREQUENCY_MAX		= 0.4f;
static const float PLAYER_PROFILE_HIGH_TACKLE_FREQUENCY_DEFAULT	= 0.04f;
static const float PLAYER_PROFILE_INJURY_FREQUENCY_MIN			= 0.0f;
static const float PLAYER_PROFILE_INJURY_FREQUENCY_MAX			= 1.0f;
static const float PLAYER_PROFILE_INJURY_FREQUENCY_DEFAULT		= 0.01f;
static const float PLAYER_PROFILE_OFFLOAD_FREQUENCY_MIN			= 0.0f;
static const float PLAYER_PROFILE_OFFLOAD_FREQUENCY_MAX			= 1.2f;
static const float PLAYER_PROFILE_OFFLOAD_FREQUENCY_DEFAULT		= 0.06f;
static const float PLAYER_PROFILE_OFFLOAD_SUCCESS_RATE_MIN		= 0.0f;
static const float PLAYER_PROFILE_OFFLOAD_SUCCESS_RATE_MAX		= 1.5f;
static const float PLAYER_PROFILE_OFFLOAD_SUCCESS_RATE_DEFAULT	= 0.8f;
static const float PLAYER_PROFILE_PASS_SUCCESS_RATE_MIN			= 0.0f;
static const float PLAYER_PROFILE_PASS_SUCCESS_RATE_MAX			= 2.0f;
static const float PLAYER_PROFILE_PASS_SUCCESS_RATE_DEFAULT		= 1.2f;
static const float PLAYER_PROFILE_BREAK_TACKLE_ABILITY_MIN		= 0.0f;
static const float PLAYER_PROFILE_BREAK_TACKLE_ABILITY_MAX		= 2.0f;
static const float PLAYER_PROFILE_BREAK_TACKLE_ABILITY_DEFAULT	= 0.8f;
static const float PLAYER_PROFILE_TACKLE_ABILITY_MIN			= 0.0f;
static const float PLAYER_PROFILE_TACKLE_ABILITY_MAX			= 2.0f;
static const float PLAYER_PROFILE_TACKLE_ABILITY_DEFAULT		= 1.22f;
static const float PLAYER_PROFILE_PROP_BREAKTHROUGH_MIN			= 0.0f;
static const float PLAYER_PROFILE_PROP_BREAKTHROUGH_MAX			= 1.0f;
static const float PLAYER_PROFILE_PROP_BREAKTHROUGH_DEFAULT		= 0.15f;

static const float PLAYER_PROFILE_FOURTYTWENTY_KICK_FREQUENCY_MIN		= 0.0f;
static const float PLAYER_PROFILE_FOURTYTWENTY_KICK_FREQUENCY_MAX		= 1.0f;
static const float PLAYER_PROFILE_FOURTYTWENTY_KICK_FREQUENCY_DEFAULT	= 0.15f;
static const float PLAYER_PROFILE_ATTACKING_URGENCY_MIN			= 0.0f;
static const float PLAYER_PROFILE_ATTACKING_URGENCY_MAX			= 2.0f;
static const float PLAYER_PROFILE_ATTACKING_URGENCY_DEFAULT		= 1.15f;
static const float PLAYER_PROFILE_DEFENSIVE_URGENCY_MIN			= 0.0f;
static const float PLAYER_PROFILE_DEFENSIVE_URGENCY_MAX			= 2.0f;
static const float PLAYER_PROFILE_DEFENSIVE_URGENCY_DEFAULT		= 1.25f;
static const float PLAYER_PROFILE_INTERCEPT_FREQUENCY_MIN		= 0.0f;
static const float PLAYER_PROFILE_INTERCEPT_FREQUENCY_MAX		= 1.0f;
static const float PLAYER_PROFILE_INTERCEPT_FREQUENCY_DEFAULT	= 0.7f;
static const float PLAYER_PROFILE_OFFLOAD_CHANCE_MIN			= 0.0f;		// This is a time grace window from when arms are free
static const float PLAYER_PROFILE_OFFLOAD_CHANCE_MAX			= 1.0f;
static const float PLAYER_PROFILE_OFFLOAD_CHANCE_DEFAULT		= 0.7f;
static const float PLAYER_PROFILE_RUCK_TACKLE_BONUS_MIN			= 0.0f;
static const float PLAYER_PROFILE_RUCK_TACKLE_BONUS_MAX			= 1.0f;
static const float PLAYER_PROFILE_RUCK_TACKLE_BONUS_DEFAULT		= 0.8f;

static const char	PLAYER_PROFILE_STATS_RECORDS_TAG_FORMAT[]	= "RECORD_STAT_%u_%d_%d";	// Competition_id, index of RU_RECORD_STAT, ordinal.
static const char	PLAYER_PROFILE_STATS_RECORDS_VALUE_DELIM	= '|';

#endif // SIF_PLAYER_PROFILE_CONSTANTS_H_INCLUDED


