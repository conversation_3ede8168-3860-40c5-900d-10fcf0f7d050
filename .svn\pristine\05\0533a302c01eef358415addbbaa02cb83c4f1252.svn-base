/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"

#include "Match/AI/SetPlays/SSSetPlayManager.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleSetplay.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleSetplayScrumHalf.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleSetplayPlayTheBallReceiver.h"
#include "RugbyGameInstance.h"
#include "Rugby/Character/RugbyCharacter.h"
#include "Engine/Engine.h"
#include "Match/Input/RUPassSetplayInterface.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Match/HUD/Marking/RUSetplayIndicator.h"
#include "Match/HUD/RU3DHUDManager.h"
#include "Match/SSSpatialHelper.h"
#include "Utility/RURandomNumberGenerator.h"

//Debug includes
#include "Runtime\Engine\Public\DrawDebugHelpers.h"
#include "Match\Components\RUPlayerAttributes.h"
#include "Match\RugbyUnion\RUTeam.h"
#include "Match/Input/RUPassExtendInterface.h"
#include "Match/SIFGameWorld.h"
#include "Match/SIFGameObject.h"
#include "Match/Ball/SSBall.h"
#include "WWUIFixedMinimap.h"
#include "Match/RugbyUnion/TutorialMode/RUTutorialManager.h"
#include "../Roles/Competitors/RURoleGetTheBall.h"

#define SMOOTHING_DISTANCE 1.0f
//#define LOADSETPLAYSFROMJSON 1

SSSetPlayManager::SSSetPlayManager(SSEVDSFormationManager * formationManager, SIFGameWorld *ggame) :
	FormationManager(formationManager),
	pass_setplay_interface(NULL),
	game(ggame)
{
	m_Origin = nullptr;
	CurrentSetplay = nullptr;
	pass_setplay_interface = MabMemNew(heap_id) RUPassSetplayInterface(game);
}

SSSetPlayManager::~SSSetPlayManager()
{
	FormationManager = nullptr;
	CurrentSetplay = nullptr;
}

void SSSetPlayManager::UpdateSimulation(float deltaTime)
{
	RUGameState* gameState = game->GetGameState();
	RUGamePhase gamePhase = gameState->GetPhase();

	if (gamePhase == RUGamePhase::HALF_TIME || gamePhase == RUGamePhase::POST_GAME)
	{
		return;
	}

	if (pass_setplay_interface)
	{
		pass_setplay_interface->UpdateSimulation(deltaTime);
	}
	UpdatePlayerSelection();

	if (ARugbyCharacter * ballHolder = gameState->GetBallHolder())
	{
		if (GTBOverridePlayer == ballHolder)
		{
			AbortSetplay();
		}
	}

	if (IsSetplayInProgress())
	{
		if (SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhase() != PLAY &&
			SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhase() != SCRUM &&
			SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhase() != RUCK &&
			SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhase() != PLAY_THE_BALL)
		{
			AbortSetplay();
		}
		else if (setplayTargettedCharacter && game->GetBall()->GetHolder() == setplayTargettedCharacter)
		{
			AbortSetplay();
		}
		else
		{
			if (FormationManager)
			{
				SSTeam * team = FormationManager->GetTeam();
				SIFRugbyCharacterList playerList = team->GetPlayers();
				for (int i = 0; i < playerList.size(); i++)
				{
					playerList[i]->GetMovement()->ForceWaypointChange();
				}
			}
		}
	}
}

//Checks if the player passed accross is currently in a set play.
bool SSSetPlayManager::isPlayerRunningSetplay(ARugbyCharacter * player)
{
	if (SetplayCharacters.Contains(player))
	{
		return true;
	}
	return false;
}

//Checks if the player has to stay behind the select player.
bool SSSetPlayManager::ShouldPlayerStayBehindSelectedPlayer(ARugbyCharacter * player)
{
	if (FormationManager)
	{
		if (const SSRoleArea * const_area = FormationManager->GetPlayerArea(player))
		{
			SSRoleArea * area = const_cast<SSRoleArea*>(const_area);
			if (FSerialiseFormationZone * zone = area ? area->get_zone() : nullptr)
			{
				return zone->stayBehindSelectedPlayer;
			}
		}
	}
	return false;
}

void SSSetPlayManager::UpdatePlayerSelection()
{
	//Check if the setplay interface is active
	if (CurrentSetplay)
	{
		if (human_player)
		{
			if (pass_setplay_interface && pass_setplay_interface->IsActive())
			{
				for (int i = 1; i <= 4; i++)
				{
					if (human_player->IsReleased(setplayDecisions[i]))
					{
						if (buttonCharacterMap.Find(i))
						{
							setplayTargettedCharacter = *buttonCharacterMap.Find(i);
							ShowCurrentSetplayDisplay(false);
#ifdef ENABLE_ANALYTICS
							if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
							{
								pRugbyGameInstance->GetPlayerAnalyticsData().MarkSetPlayUsed(CurrentSetplay->name, (wwSetPlayFaceOption)i);
							}
#endif
						}
					}
				}
			}

		}
		else if(setplayTargettedCharacter == nullptr)
		{
			TArray<int> possibleSetplays;
			buttonCharacterMap.GetKeys(possibleSetplays);
			if (possibleSetplays.Num() > 0)
			{
				int playerIndex = possibleSetplays[game->GetRNG()->RAND_RANGED_CALL(int, possibleSetplays.Num())];
				if (buttonCharacterMap.Find(playerIndex))
				{
					setplayTargettedCharacter = *buttonCharacterMap.Find(playerIndex);
				}
			}
		}
	}
}

//Returns the time value of the current waypoint the player is moving to
float SSSetPlayManager::GetPlayerMovementWaypointTime(ARugbyCharacter*  player, SSRoleArea * area)
{
	if (player && area)
	{
		//Get the position graph for the set play
		FSerialiseFormationZone* zone = area->get_zone();
		FSerialiseTypeGraph x = zone->x;
		FSerialiseTypeGraph z = zone->z;
		if (player->GetRole()->RTTGetType() == RURoleSetplay::RTTGetStaticType() ||
			player->GetRole()->RTTGetType() == RURoleSetplayScrumHalf::RTTGetStaticType() ||
			player->GetRole()->RTTGetType() == RURoleSetplayPlayTheBallReceiver::RTTGetStaticType())
		{
			if (RURoleSetplay * setplayRole = player->GetRoleSafe<RURoleSetplay>())
			{
				int posIndex = setplayRole->GetFormationPositionIndex();
				if (x.points.Num() > posIndex)
				{
					//Smooth the transition through waypoints
					if (!setplayRole->isActionInProgress() && HasSetplayStarted() && IsSyncPointComplete(player))
					{
						FVector targetPos = area->GetZonePosition(-(float)player->GetAttributes()->GetTeam()->GetPlayDirection(), posIndex);
						FVector startPos = area->GetZonePosition(-(float)player->GetAttributes()->GetTeam()->GetPlayDirection(), posIndex - 1);
						FVector playerPos = player->GetMabPosition();

						float waypointDistance = (playerPos - targetPos).Magnitude();
						float totalDistance = (targetPos - startPos).Magnitude();
						float fractionTravelled = waypointDistance / totalDistance;
						if (1/*waypointDistance < SMOOTHING_DISTANCE || setplayRole->bSmoothingInProgress*/)
						{
							setplayRole->bSmoothingInProgress = true;

// 							if (waypointDistance >= SMOOTHING_DISTANCE)
// 							{
// 								SetPlayerAtSyncPoint(player);
// 								setplayRole->bMovingToNextWaypoint = true;
// 							}

							return (float)posIndex + (1.0f - fractionTravelled);
						}
					}
					return posIndex;
				}
				else
				{
					//Player is at the end of the setplay, return the final point.
					UpdatePlayerSetplayStatus(player, area);
					return x.points.Num() - 1;
				}
			}
		}
	}
	return 0;
}

void SSSetPlayManager::DrawPlayerWaypoints(ARugbyCharacter*  player)
{
	if (player && FormationManager)
	{
		if (SSRoleArea * area = const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(player)))
		{
			if (FSerialiseFormationZone * zone = area->get_zone())
			{
				int numWaypoints = zone->x.points.Num();
				for (int i = 0; i < numWaypoints; i++)
				{
					//Get the mab position of the waypoint
					float playDir = 1;
					if (RUTeam * team = player->GetAttributes()->GetTeam())
					{
						playDir = -(float)team->GetPlayDirection();
					}
					FVector zonePosition = area->GetZonePosition(playDir, i);

					//Calculate the world position of the waypoint
					MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(zonePosition, converted_target_pos);

					//Set the colour depending on if the waypoint has been reached.
					FLinearColor waypointColour = FLinearColor::Yellow;
					if (RURoleSetplay* roleSetPlay = MabCast<RURoleSetplay>(player->GetRole()))
					{
						waypointColour = (roleSetPlay->GetFormationPositionIndex() + 1 > i) ? FLinearColor::Green : FLinearColor::Red;
					}

					DrawDebugSphere(
						player->GetWorld(),
						converted_target_pos,
						25,
						16,
						waypointColour.ToFColor(true),
						false,
						0.05,
						10,
						20
					);

					//Draw line to the next point
					FVector nextZonePosition = area->GetZonePosition(playDir, i+1);

					//Calculate the world position of the waypoint
					MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(nextZonePosition, converted_next_zone_pos);


					DrawDebugLine(
						player->GetWorld(),
						converted_target_pos,
						converted_next_zone_pos,
						waypointColour.ToFColor(true),
						false,
						0.05,
						10,
						20
					);
				}
			}
		}
	}
}

void SSSetPlayManager::UpdatePlayerSetplayStatus(ARugbyCharacter*  player, SSRoleArea * area)
{
	if (player && area)
	{
		//If the player has reached their final waypoint, check if they have completed all their actions
		RURoleSetplay* roleSetPlay = player->GetRoleSafe<RURoleSetplay>();
		if ((roleSetPlay && !roleSetPlay->GetPlayerSetplayComplete() && roleSetPlay->isActionInProgress()) || !HasSetplayStarted())
		{
			//If they haven't finished their actions, return the final waypoint position.
			return;
		}
		//they have finished the set play, remove them from the array.
		SetplayCharacters.Remove(player);
		TArray<FSerialiseFormationRole> tempRoles = area->get_roles();

		//Add the player to the array of players who have completed the setplay
		for (int i = 0; i < tempRoles.Num(); i++)
		{
			if (tempRoles[i].role == ERugbyFormationRole::SETPLAY || tempRoles[i].role == ERugbyFormationRole::SETPLAY_SCRUM_HALF || tempRoles[i].role == ERugbyFormationRole::SETPLAY_PTB_RECEIVER)
			{
				//Check if the role is already in the list and increment it if it is
				if (PlayersCompletedSetplayRole.Find(area->get_name()))
				{
					TArray<int> numCompletedArray = *PlayersCompletedSetplayRole.Find(area->get_name());

					//Increase array size if too small
					while (!numCompletedArray.IsValidIndex(i))
					{
						numCompletedArray.Add(0);
					}
					int currentNum = numCompletedArray[i];
					numCompletedArray[i] = ++currentNum;
					PlayersCompletedSetplayRole.Add(area->get_name(), numCompletedArray);
				}
				else
				{
					TArray<int> numCompletedArray = TArray<int>();
					for (int j = 0; j < i; j++)
					{
						numCompletedArray.Add(0);
					}
					numCompletedArray.Add(1);
					PlayersCompletedSetplayRole.Add(area->get_name(), numCompletedArray);
				}
			}
		}
	}

	//If there are no players in the setplay anymore, it is complete.
	if (SetplayCharacters.Num() == 0)
	{
		AbortSetplay();
	}
}

void SSSetPlayManager::ShowCurrentSetplayDisplay(bool show)
{
	if (show)
	{
		if(!bSplinesInitialised)
			InitialiseSplines();

		if (pass_setplay_interface)
		{			
			if (CurrentSetplay)
			{
				//Populate the map of buttons to receiving players in the current setplay
				for (FSerialiseFormationZone zone : CurrentSetplay->zones)
				{
					if (zone.passOrKickTarget > 0)
					{
						//Find the player in this zone.
						for (ARugbyCharacter * character : SetplayCharacters)
						{
							if (FormationManager)
							{
								if (SSRoleArea * tempArea = const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(character)))
								{
									if (tempArea->get_name().Compare(zone.name) == 0)
									{
										if(human_player || (zone.targettedAction != ERugbySetplayAction::KICKPUNT && zone.targettedAction != ERugbySetplayAction::KICKCHIP) )		//This is to make sure kicking setplays are not used by AI as the code is not implemented.
											buttonCharacterMap.Add(zone.passOrKickTarget, character);
									}
								}

							}
						}
					}
				}

				if (human_player && human_player->GetTeam())
				{
					pass_setplay_interface->StartExtend(human_player);

					setplayTargettedCharacter = nullptr;				
					
				}
				return;
			}
		}
	}

	pass_setplay_interface->Activate(false);
	buttonCharacterMap.Empty();
}

void SSSetPlayManager::PhaseChanged()
{
	RUGameState* gameState = game ? game->GetGameState() : nullptr;
	if (gameState)
	{
		RUGamePhase currentPhase = gameState->GetPhase();
		RUGamePhase previousPhase = gameState->GetPreviousPhase();

		if (currentPhase != previousPhase)
		{
			if (currentPhase != PLAY || (previousPhase != SCRUM && previousPhase != RUCK && previousPhase != PLAY_THE_BALL))
			{
				AbortSetplay();
			}
		}
	}
}

void SSSetPlayManager::InitialiseSplines()
{
	TArray<FMinimapLineData> lines;

	for (ARugbyCharacter * character : SetplayCharacters)
	{
		SSRoleArea * tempArea = FormationManager->GetPlayerArea(character) ? const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(character)) : nullptr;
		if (!tempArea)
			continue;

		FSerialiseFormationZone * zonePtr = tempArea->get_zone();
		if (!zonePtr)
			continue;

		FSerialiseFormationZone zone = *zonePtr;

		MabNURBSSpline * spline = new MabNURBSSpline();

		int numPoints = zone.x.points.Num();			//Add one CV for each point in the setplay
		int degree = numPoints >= 3 ? 2 : 1;			//Use a degree 2 spline if there are at least 3 points, otherwise use a linear spline.
		int numKnots = numPoints + degree + 1;			//number of knots is always the number of points + degree + 1
		int decisionIndex = zone.passOrKickTarget;

		if (numPoints <= 1 || decisionIndex == 0)
			continue;

		spline->SetDegree(degree);

		//Add the points as CV points.
		for (int i = 0; i < zone.x.points.Num(); i++)
		{
			int directionMultiplier = character->GetAttributes()->GetTeam()->GetPlayDirection() == ERugbyPlayDirection::SOUTH ? 1 : -1;
			FVector nodePosition = (directionMultiplier * FVector(zone.x.points[i].y, 0, zone.z.points[i].y)) + m_Origin->Setplay_position;
			const float SIDELINE_BUFFER = 0.8f; // Keep a buffer from the sideline
			game->GetSpatialHelper()->ClampWaypointToSideLine(nodePosition, SIDELINE_BUFFER);
			game->GetSpatialHelper()->ClampWaypointToDeadBallLine(nodePosition, SIDELINE_BUFFER);
			spline->AddCV(nodePosition, 1.0f);
		}

		//Add the knots. 
		//As the curve needs to touch the first and last points, the first degree + 1 knots must be 0, and the last degree + 1 should be 1

		//Add the 0 knots
		for (int i = 0; i < degree + 1; i++)
		{
			spline->AddKnot(0.0f);
		}

		//Calculate if there are any middle knots
		int numMiddleKnots = numKnots - (2 * (degree + 1));
		MABASSERT(numMiddleKnots >= 0);	//This should never happen

		//Add middle knots
		if (numMiddleKnots > 0)
		{
			float deltaValue = 1.0f / (numMiddleKnots + 1.0f);

			for (int i = 0; i < numMiddleKnots; i++)
			{
				spline->AddKnot((i + 1) * deltaValue);
			}
		}

		//Add the 1 knots
		for (int i = 0; i < degree + 1; i++)
		{
			spline->AddKnot(1.0f);
		}

		FMinimapLineData lineData;

		if (buttonColours.IsValidIndex(decisionIndex - 1))
		{
			m_SetplaySplines.Add(spline, buttonColours[decisionIndex - 1]);
			lineData.color = buttonColours[decisionIndex - 1];
		}
		else
		{
			MABASSERT(buttonColours.IsValidIndex(decisionIndex - 1));
		}

		spline->InitialiseApproximation(5);
		for (FVector point : spline->GetApproximatedPoints())
		{
			MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(point, convertedPoint);
			lineData.points.Add(convertedPoint);
		}

		lineData.width = 2.0f;

		lines.Add(lineData);
		spline->InitialiseApproximation();
	}

	bSplinesInitialised = true;

	if (game && game->Get3DHudManager() && game->Get3DHudManager()->GetSetplayIndicator())
	{
		game->Get3DHudManager()->GetSetplayIndicator()->Activate();
	}

	if (game && game->GetHUDUpdaterContextual())
	{
		if (UWWUIFixedMinimap * minimap = game->GetHUDUpdaterContextual()->GetMinimap())
		{
			minimap->SetLines(lines);
		}
	}
}


//Starts a set play by the name specified in the file.
void SSSetPlayManager::StartSetplayByName(FString setplayName, SSHumanPlayer * startingHuman /*= nullptr*/)
{
#ifdef ENABLE_ANALYTICS
	if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
	{
		pRugbyGameInstance->GetPlayerAnalyticsData().MarkSetPlayUsed(setplayName);
	}
#endif
	FSerialiseFormation * newSetplay = FormationManager->GetFormationByName(setplayName);
	CurrentSetplay = newSetplay;
	human_player = startingHuman;
	PlayersCompletedSetplayRole.Empty();
	SetplayCharacters.Empty();
	bSetplayStarted = false;
	InitialiseSyncPoints();
	
	if (RUGameEvents* gameEvents = game ? game->GetEvents() : nullptr)
	{
		gameEvents->phase_changed.Add(this, &SSSetPlayManager::PhaseChanged);
	}

	if (newSetplay)
	{
		if (FormationManager->GetFormationOrigin())
		{
			FVector setplayOrigin = FormationManager->GetFormationOrigin()->GetOrigin();
			float setplayRotation = FormationManager->GetFormationOrigin()->GetFacingAngle();
			SetSetplayOrigin(setplayOrigin, setplayRotation);
		}
	}

	if (FormationManager)
	{
		SSTeam * team = FormationManager->GetTeam();
		SIFRugbyCharacterList playerList = team->GetPlayers();
		for (int i = 0; i < playerList.size(); i++)
		{
			ARugbyCharacter * character = playerList.at(i);
			if (UOBJ_IS_VALID(character))
			{
				if (character->GetMovement())
				{
					const float FASTEST_SPEED = 40.0f * 1000.0f / 3600.0f;
					character->GetMovement()->OverrideMaxSpeed(FASTEST_SPEED * 4.0f);
					character->GetMovement()->OverrideMaxAcceleration(3.0f);
					character->GetMovement()->EnableDecelAtTarget(true);
				}
			}
		}
	}

	if (startingHuman == nullptr)
	{

	}
	else
	{
		game->GetEvents()->change_to_camera(GAME_CAM_SETPLAY);

		if (FormationManager)
		{
			SSTeam * team = FormationManager->GetTeam();
			for (int i = 0; i < team->GetNumHumanPlayers(); i++)
			{
				SSHumanPlayer * player = team->GetHumanPlayer(i);
				if (player != startingHuman)
				{
					player->SetRugbyCharacter(nullptr);
					//player->DisableInput(true);
				}
			}

			
		}
	}
}

/***********************************************************************************************************************************
			SYNC POINTS

			These take care of making sure actions that have to take place in order in a setplay, will wait for each other.
			There are two components:
				- First, the setplay has a list of syncParams, which is a list of all the sync points in this setplay, as well as how many players
				will be waiting at each sync point.
				-Second, each zone has a list of the syncPoints that it will wait at. When a waypoint is reached, that waypoints index will be used
				to check if there is a sync point here. If there is, the player will notify the manager that 1 player has reached the sync point, and
				then it will wait until the expected number of players reach the sync point before proceeding.
*************************************************************************************************************************************/

//Initialises the sync point array
void SSSetPlayManager::InitialiseSyncPoints()
{
	if (CurrentSetplay)
	{
		m_SyncPoints.Empty();
		TArray<FSerialiseSetplaySyncPoints> syncPoints = CurrentSetplay->syncParams;

		for (int i = 0; i < syncPoints.Num(); i++)
		{
			m_SyncPoints.Add(SyncPoint(syncPoints[i].key, syncPoints[i].numPlayers));
		}
	}
}

//Called by every player when they reach a waypoint to update their sync point if they have one
void SSSetPlayManager::SetPlayerAtSyncPoint(ARugbyCharacter * player)
{
	if (CurrentSetplay && FormationManager)
	{
		if (SSRoleArea * area = const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(player)))
		{
			if (player->GetRoleSafe<RURoleSetplay>())
			{
				int syncPointIndex = player->GetRoleSafe<RURoleSetplay>()->GetFormationPositionIndex() + 1;
				if (area->get_zone()->syncPoints.IsValidIndex(syncPointIndex))
				{
					FString syncKey = area->get_zone()->syncPoints[syncPointIndex];
					for (int i = 0; i < m_SyncPoints.Num(); i++)
					{
						if (m_SyncPoints[i].key == syncKey)
						{
							int currentNum = m_SyncPoints.Num();
							m_SyncPoints[i].waitingPlayers.AddUnique(player);
							int newNum = m_SyncPoints.Num();
							if (GEngine)
							{
								if (m_SyncPoints[i].waitingPlayers.Num() == m_SyncPoints[i].maxPlayers)
								{
									GEngine->AddOnScreenDebugMessage(-1, 15.0f, FColor::Purple, FString::Printf(TEXT("Sync Point Complete: %s"), *m_SyncPoints[i].key));
								}
								else if (newNum != currentNum)
								{
									GEngine->AddOnScreenDebugMessage(-1, 15.0f, FColor::Purple, FString::Printf(TEXT("Player at sync point: %s"), *m_SyncPoints[i].key));
								}
							}
						}
					}
				}
			}
		}
	}
}

//Called by players before doing movement calculations to see if they should wait for a sync point
bool SSSetPlayManager::IsSyncPointComplete(ARugbyCharacter * player)
{
	if (CurrentSetplay && FormationManager)
	{
		if (SSRoleArea * area = const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(player)))
		{
			if (player->GetRoleSafe<RURoleSetplay>())
			{
				int syncPointIndex = player->GetRoleSafe<RURoleSetplay>()->GetFormationPositionIndex() + 1;
				if (area->get_zone()->syncPoints.IsValidIndex(syncPointIndex))
				{
					FString syncKey = area->get_zone()->syncPoints[syncPointIndex];
					for (int i = 0; i < m_SyncPoints.Num(); i++)
					{
						if (m_SyncPoints[i].key == syncKey)
						{
							if (m_SyncPoints[i].waitingPlayers.Num() == m_SyncPoints[i].maxPlayers)
							{
								return true;
							}
							return false;
						}
					}
				}
				//Only reaches here if the player is not in a sync point
				return true;
			}
		}
	}
	return false;
}


void SSSetPlayManager::AbortSetplay()
{
	CurrentSetplay = nullptr;
	GTBOverridePlayer = nullptr;
	setplayTargettedCharacter = nullptr;
	ShowCurrentSetplayDisplay(false);
	bSplinesInitialised = false;
	PlayersCompletedSetplayRole.Empty();
	SetplayCharacters.Empty();

	if(game && game->Get3DHudManager() && game->Get3DHudManager()->GetSetplayIndicator())
		game->Get3DHudManager()->GetSetplayIndicator()->Deactivate();

	if (game && game->GetHUDUpdaterContextual())
	{
		if (UWWUIFixedMinimap * minimap = game->GetHUDUpdaterContextual()->GetMinimap())
		{
			minimap->RemoveAllLines();
		}
	}

	for (auto spline : m_SetplaySplines)
	{
		MabMemDeleteSafe(spline.Key);
	}
	m_SetplaySplines.Empty();

	for (ARugbyCharacter * teamPlayer : FormationManager->GetTeam()->GetPlayers())
	{
		if (teamPlayer->GetActionManager() &&
			teamPlayer->GetActionManager()->IsActionRunning(RU_ACTION_INDEX::ACTION_GETTHEBALL))
		{
			teamPlayer->GetActionManager()->GetAction<RUActionGetTheBall>()->SetCatchingPlayer(nullptr);
		}
	}

	if (RUGameEvents* gameEvents = game ? game->GetEvents() : nullptr)
	{
		gameEvents->phase_changed.Remove(this, &SSSetPlayManager::PhaseChanged);
	}

	SetSetplayStarted(false);
}

//Stores the current setplay origin
void SSSetPlayManager::SetSetplayOrigin(const FVector _setplay_position, float _facing_angle)
{
	if (m_Origin)
	{
		delete(m_Origin);
	}
	m_Origin = new SetplayOrigin(_setplay_position, _facing_angle);
	if (RUGameState* game_state = game ? game->GetGameState() : nullptr)
	{
		if (game_state->GetPhase() == RUGamePhase::PLAY_THE_BALL)
		{
			game_state->SetFormationTarget(ERugbyFormationTarget::SETPLAY_PTB_RECEIVER, m_Origin);
		}
		else
		{
			game_state->SetFormationTarget(ERugbyFormationTarget::SETPLAY_RUCK_CENTER, m_Origin);
		}
	}
}

//Getter for the setplay origin
SetplayOrigin * SSSetPlayManager::GetSetplayOrigin()
{
	return m_Origin;
}

//Called by the formation manager to add players to the set play when one starts
void SSSetPlayManager::AddPlayerToSetplay(ARugbyCharacter * player, SSRoleArea * area)
{
	SetplayCharacters.Add(player);
}

void SSSetPlayManager::RemovePlayerFromSetplay(ARugbyCharacter * player)
{
	SetplayCharacters.Remove(player);
	if (SetplayCharacters.Num() == 0)
	{
		AbortSetplay();
	}
}

bool SSSetPlayManager::AreSetplaysEnabled()
{
	//Setplays disabled for sevens as they are not implemented and are not used often in sevens anyway - #MB
	// Nick WWS 7s to Womens 13s //
	/*
	if (game &&	game->GetGameSettings().game_settings.GameModeIsR7())
	{
		return false;
	}*/

	//Setplays disabled in be a pro
	if (game &&	game->GetGameSettings().game_settings.GetIsAProMode())
	{
		return false;
	}

	//Setplays disabled in tutorials
	if (game && game->GetTutorialManager() && game->GetTutorialManager()->IsTutorialRunning())
	{
		return false;
	}

	//Don't allow setplays when a breakdown strategy has been started (send runner or prepre kicker)
	if (FormationManager)
	{
		SSTeam * ssteam = FormationManager->GetTeam();
		if (ssteam)
		{
			SIFRugbyCharacterList playerList = ssteam->GetPlayers();
			if (playerList.size() > 0)
			{
				ARugbyCharacter * tempPlayer = playerList.at(0);
				if (tempPlayer && tempPlayer->GetAttributes())
				{
					RUTeam * ruteam = tempPlayer->GetAttributes()->GetTeam();
					if (ruteam)
					{
						if (ruteam->GetStrategy().IsBreakdownStrategyStarted())
						{
							return false;
						}
					}
				}
			}
		}
	}

	//Safe zone added to setplays: Cannot use setplays within 10 meters of your own try line.
	if (game && game->GetBall())
	{
		FVector ballPosition = game->GetBall()->GetMabPosition();
		float zPos = game->GetSpatialHelper()->GetFieldZPosition(FormationManager->GetTeam()->GetPlayDirection(), FIELD_10M_LINE);
		float ballOffset = (ballPosition.z - zPos) * (int)FormationManager->GetTeam()->GetPlayDirection();
		if (ballOffset < 0)
		{
			return false;
		}
	}

	return bSetplaysEnabled;
}

bool SSSetPlayManager::ArePlayersInPosition()
{
	if (CurrentSetplay)
	{
		if (SetplayCharacters.Num() > 0)
		{
			//Iterate through all setplay players and check if they have reached waypoint 0 (The start point).
			for (ARugbyCharacter * player : SetplayCharacters)
			{
				if (RURoleSetplay * setplayRole = player->GetRoleSafe<RURoleSetplay>())
				{
					if (!setplayRole->isInStartPosition())
					{
						return false;
					}
				}
			}

			//Tell all players to ignore movement throttling while running setplay
			for (ARugbyCharacter * player : SetplayCharacters)
			{
				if (player->GetMovement())
				{
					player->GetMovement()->SetThrottleWhenCloseToWaypoint(false);
					player->GetMovement()->EnableDecelAtTarget(false);
				}
			}

			return true;
		}
	}
	return false;
}

void SSSetPlayManager::SetSetplayStarted(bool hasStarted)
{
	bSetplayStarted = hasStarted;

	if (FormationManager)
	{
		SSTeam * team = FormationManager->GetTeam();
		if (team)
		{
			SIFRugbyCharacterList playerList = team->GetPlayers();
			for (int i = 0; i < playerList.size(); i++)
			{
				ARugbyCharacter * character = playerList.at(i);
				if (UOBJ_IS_VALID(character))
				{
					if (character->GetMovement())
					{
						character->GetMovement()->OverrideMaxSpeed();
						character->GetMovement()->OverrideMaxAcceleration();
						character->GetMovement()->EnableDecelAtTarget(false);
					}
				}
			}
		}
	}
}

int SSSetPlayManager::NumPlayersCompletedSetplay(SSRoleArea * area, int zoneIndex)
{
	TArray<FSerialiseFormationRole> rolesArray = area->get_roles();
	if (rolesArray.IsValidIndex(zoneIndex))
	{
		if (rolesArray[zoneIndex].role == ERugbyFormationRole::SETPLAY || rolesArray[zoneIndex].role == ERugbyFormationRole::SETPLAY_SCRUM_HALF || rolesArray[zoneIndex].role == ERugbyFormationRole::SETPLAY_PTB_RECEIVER)
		{
			FString test = area->get_name();
			if (PlayersCompletedSetplayRole.Find(area->get_name()))
			{
				TArray<int> numCompletedArray = *PlayersCompletedSetplayRole.Find(area->get_name());
				if (numCompletedArray.IsValidIndex(zoneIndex))
				{
					int currentNum = numCompletedArray[zoneIndex];
					//MABASSERTMSG(currentNum <= expectedNumPlayers, "More players are assigned to this role than should be");
					return currentNum;
				}
			}
			return 0;
		}
		else
		{
			//MABASSERTMSG(rolesArray[zoneIndex].role == ERugbyFormationRole::SETPLAY || rolesArray[zoneIndex].role == ERugbyFormationRole::SETPLAY_SCRUM_HALF, "Only setplay roles should be checked here");
		}
	}
	return 0;
}


bool SSSetPlayManager::HasRoleAreaBeenFilled(SSRoleArea * area, int zoneIndex)
{
	TArray<FSerialiseFormationRole> rolesArray = area->get_roles();
	if (rolesArray.IsValidIndex(zoneIndex))
	{
		int expectedNumPlayers = rolesArray[zoneIndex].numPlayers;
		if (rolesArray[zoneIndex].overrideNumPlayers > 0)
		{
			expectedNumPlayers = rolesArray[zoneIndex].overrideNumPlayers;
		}
		if (expectedNumPlayers == NumPlayersCompletedSetplay(area, zoneIndex))
		{
			return true;
		}

		//MABASSERTMSG(NumPlayersCompletedSetplay(role) <= expectedNumPlayers, "More players are assigned to this role than is needed");
	}
	return false;
}

FSerialiseFormation * SSSetPlayManager::GetSetplayByIndex(int setplayIndex)
{
	if (m_SetplaysList.IsValidIndex(setplayIndex))
	{
		return m_SetplaysList[setplayIndex];
	}

	//MABASSERTMSG(m_SetplaysList.IsValidIndex(setplayIndex), "Something is asking for a setplay index outside the array");
	return nullptr;
}

void SSSetPlayManager::AddSetplayToList(FSerialiseFormation * addedSetplay, int index)
{
	//Make sure the array is large enough
	while (m_SetplaysList.Num() < index + 1)
	{
		m_SetplaysList.Add(nullptr);
	}

	MABASSERTMSG(m_SetplaysList.IsValidIndex(index), "Array isn't being made large enough to contain this index");

	m_SetplaysList[index] = addedSetplay;
}

void SSSetPlayManager::PopulateDefaultSetplays()
{
	if (FormationManager)
	{
		for (int i = 0; i < defaultSetplays.Num(); i++)
		{
			AddSetplayToList(FormationManager->GetFormationByName(defaultSetplays[i]), i);

#ifdef LOADSETPLAYSFROMJSON
			FSerialiseFormation * formationPtr = FormationManager->GetFormationByName(defaultSetplays[i]);
			if (formationPtr)
			{
				if (UDataTable * setplaysDt = SIFApplication::GetApplication()->GetThirteensSetplaysDt())
				{
					AddOrFindRow<FSerialiseFormation>(setplaysDt, *formationPtr);
				}
			}
#endif
		}
	}
}

template <typename T>
FDataTableRowHandle SSSetPlayManager::AddOrFindRow(UDataTable * inDatatable, T row)
{
	if (inDatatable)
	{
		FString contextString;
		TArray<FName> rowNames = inDatatable->GetRowNames();
		//for (FName &rowName : rowNames)
		//{
		//	if (T * comparisonRow = inDatatable->FindRow<T>(rowName, contextString))
		//	{
		//		if (comparisonRow && *comparisonRow == row)
		//		{
		//			FDataTableRowHandle returnHandle = FDataTableRowHandle();
		//			returnHandle.DataTable = inDatatable;
		//			returnHandle.RowName = FName(rowName);

		//			return returnHandle;			//Match row found, so return that row reference
		//		}
		//	}
		//}

		//If no matching row is found, create one.
		FName name = FName(*row.name);
		FName nameWithSuffix = name;

		//Check if a row with the same name exists
		if (inDatatable->FindRow<T>(name, contextString, false))
		{
			//If it does, add a suffix to the name
			int suffix = 0;
			nameWithSuffix = name;
			FString suffixString = FString::FromInt(suffix);
			nameWithSuffix = FName(*nameWithSuffix.ToString().Left(30));		//Shorten to 30 character to leave from for suffixes if needed
			nameWithSuffix.AppendString(suffixString);


			//Increment suffix until a uniwue name is found
			while (inDatatable->FindRow<T>(nameWithSuffix, contextString) && suffix < 100)
			{
				suffix++;
				nameWithSuffix = name;
				nameWithSuffix = FName(*nameWithSuffix.ToString().Left(30));		//Shorten to 30 character to leave from for suffixes if needed
				suffixString = FString::FromInt(suffix);
				nameWithSuffix.AppendString(suffixString);
			}
		}
		inDatatable->AddRow(nameWithSuffix, row);
		FDataTableRowHandle returnHandle = FDataTableRowHandle();
		returnHandle.DataTable = inDatatable;
		returnHandle.RowName = nameWithSuffix;
		return returnHandle;
	}

	return FDataTableRowHandle();
}

//#include "Match/RugbyUnion/RUTeam.h"
//#include "Match/AI/Formations/SSEVDSFormationManager.h"
//#include "Match/AI/Formations/SSEVDSFormationConstants.h"
//
//
//#include "Match/RugbyUnion/RUTeam.h"
//#include "Match/RugbyUnion/RUGameState.h"
////#rc3_legacy #include "SIFDebug.h"
//#include "Match/Debug/RUGameDebugSettings.h"
//#include "Match/AI/Formations/SSEVDSFormationManager.h"
//#include "Character/RugbyPlayerController.h"
//#include "Match/RugbyUnion/RUGameEvents.h"
//
//#include "Match/AI/Roles/Competitors/SetPlays/RURoleLineOutThrower.h"
//#include "Match/AI/Roles/Competitors/SetPlays/RURoleLineOut.h"
//#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuckScrumHalf.h"
//#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuck.h"
//#include "Match/AI/Roles/Competitors/SetPlays/RURoleScrumHalfBack.h"
//#include "Match/AI/Roles/Competitors/SetPlays/RURoleScrum.h"
//
////temp
//#include "Match/AI/Roles/Competitors/RURoleStandardBallHolder.h"
//
//#include <MabControlActionManager.h>
//
//
//enum {
//	SP_STATE_NONE = 0,
//	SP_STATE_WAIT_FOR_START,
//	SP_STATE_WAIT_BALL,				// 
//	SP_STATE_RUNNING,
//
//	SP_STATE_FINISHED,
//};
//
/////-------------------------------------------------------------------------
///// SSSetPlayManager - constructor.
/////-------------------------------------------------------------------------
//
//SSSetPlayManager::SSSetPlayManager(SIFGameWorld *ggame,MabControlActionManager* ccontrol_action_manager) : 
//  game(ggame),
//  control_action_manager(ccontrol_action_manager)
//{
//}
//
/////-------------------------------------------------------------------------
///// SSSetPlayManager - destructor.
/////-------------------------------------------------------------------------
//
//SSSetPlayManager::~SSSetPlayManager()
//{
//}
//
/////-------------------------------------------------------------------------
///// Reset - called at game start/restart.
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::Reset()
//{
//	team0_players.clear();
//	team1_players.clear();
//}
//
/////-------------------------------------------------------------------------
///// Called in game simulation logic.
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::UpdateLogic(float delta_game_time)
//{
//	RUGamePhase current_phase = game->GetGameState()->GetPhase();
//
//	allow_role_interupt = false;
//
//	if(current_phase!=last_phase)
//	{
//		switch(last_phase)
//		{
//		case RUGamePhase::LINEOUT:
//			LineOutExit();
//			break;
//
//		case RUGamePhase::RUCK:
//			RuckExit();
//			break;
//
//		case RUGamePhase::SCRUM:
//			ScrumExit();
//			break;
//
//		default:
//			break;
//		}
//
//		switch(current_phase)
//		{
//		case RUGamePhase::LINEOUT:
//			LineOutEnter();
//			break;
//
//		case RUGamePhase::RUCK:
//			RuckEnter();
//			break;
//
//		case RUGamePhase::SCRUM:
//			ScrumEnter();
//			break;
//
//		default:
//			break;
//		}
//	}
//
//	switch(current_phase)
//	{
//	case RUGamePhase::LINEOUT:
//		LineOutUpdate(delta_game_time);
//		break;
//
//	case RUGamePhase::RUCK:
//		RuckUpdate(delta_game_time);
//		break;
//
//	case RUGamePhase::SCRUM:
//		ScrumUpdate(delta_game_time);
//		break;
//
//	default:
//		break;
//	}
//
//	last_phase = current_phase;
//}
//
//
//
/////-------------------------------------------------------------------------
///// Join the 'group' for the setplay, (LINEOUT, SCRUM, RUCK, MAUL)
/////  - The formation manager controls the group.
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::JoinSetPlayGroup(ARugbyCharacter* player)
//{
//	if(player->GetAttributes()->GetTeam()->GetIndex()==0)
//	{
//		team0_players.push_back(player);
//	}
//	else
//	{
//		team1_players.push_back(player);
//	}
//}
//
/////-------------------------------------------------------------------------
///// Remove player from the group (done by roles on exit)
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::ExitSetPlayGroup(ARugbyCharacter* player)
//{
//	if(player->GetAttributes()->GetTeam()->GetIndex()==0)
//	{
//		MabVector<ARugbyCharacter*>::iterator itr = std::find(team0_players.begin(), team0_players.end(), player);
//		if(itr!=team0_players.end())
//			team0_players.erase(itr);
//	}
//	else
//	{
//		MabVector<ARugbyCharacter*>::iterator itr = std::find(team1_players.begin(), team1_players.end(), player);
//		if(itr!=team1_players.end())
//			team1_players.erase(itr);
//	}
//}
//
/////-------------------------------------------------------------------------
///// 
/////-------------------------------------------------------------------------
//
//bool SSSetPlayManager::GetPlayerTargetPosition(ARugbyCharacter* player, FVector &target_pos)
//{
//	switch(game->GetGameState()->GetPhase())
//	{
//	case RUGamePhase::LINEOUT:
//		SetLineOutPosition(player,target_pos);
//		break;
//
//	case RUGamePhase::RUCK:
//		SetRuckPosition(player,target_pos);
//		break;
//
//	case RUGamePhase::SCRUM:
//		SetScrumPosition(player,target_pos);
//		break;
//
//	default:
//		target_pos.x = target_pos.y = target_pos.z = 0.0f;
//		return false;
//	}
//
//	return true;
//}
//
/////-------------------------------------------------------------------------
///// 
/////-------------------------------------------------------------------------
//
//bool SSSetPlayManager::StartAllowed()
//{
//	switch(game->GetGameState()->GetPhase())
//	{
//	case RUGamePhase::LINEOUT:
//		return state==SP_STATE_WAIT_BALL;
//		break;
//
//	case RUGamePhase::RUCK:
//		return true;
//
//	case RUGamePhase::SCRUM:
//		return state==SP_STATE_WAIT_BALL || state==SP_STATE_FINISHED;
//		break;
//
//	default:
//		break;
//	}
//
//	return false;
//}
//
/////-------------------------------------------------------------------------
///// 
/////-------------------------------------------------------------------------
//
//bool SSSetPlayManager::Finished()
//{
//	switch(game->GetGameState()->GetPhase())
//	{
//	case RUGamePhase::LINEOUT:
//	case RUGamePhase::RUCK:
//	case RUGamePhase::SCRUM:
//		return state == SP_STATE_FINISHED;
//
//	//case RUGamePhase::PLAY:
//	//case GAME_PHASE_PASS:
//	//	return false;			// Important - to stop early exit during release phase.
//
//	default:
//		break;
//	}
//
//	return true;
//}
//
/////-------------------------------------------------------------------------
///// Return (in result) all the players in team 'team_no' with role matching 'role_id'
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::GetTeamPlayers(MabVector<ARugbyCharacter*> &result, int team_no, MabTypeID role_id)
//{
//	MabVector<ARugbyCharacter*> players = team_no==0?team0_players:team1_players;
//
//	for(size_t i=0;i<players.size();i++)
//	{
//		if(players[i]->GetRole()->RTTGetType()==role_id)
//		{
//			result.push_back(players[i]);
//		}
//	}
//}
//
/////-------------------------------------------------------------------------
///// Human players are not allowed inside the set-play group, switch them out...
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::RemoveHumansFromSetPlay()
//{
//	
//	for(int team_idx = 0; team_idx < (int)game->GetTeams().size(); team_idx++ )
//	{
//		MabVector<ARugbyCharacter*> players = team_idx==0?team0_players:team1_players;
//
//		for(size_t i=0;i<players.size();i++)
//		{
//			ARugbyCharacter* player = players[i];
//			SSHumanPlayer *human = player->GetHumanPlayer();
//			if(human!=NULL)
//			{
//				human->AssignBestPlayer(false);
//			}
//		}
//	}
//}
//
//
//
//
//
//
//
//
//
//
//
//
//
/////**************************************************************************************************************
/////**************************************************************************************************************
/////**************************************************************************************************************
/////*** Line out.
//
//
//
//
/////-------------------------------------------------------------------------
///// 
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::LineOutEnter()
//{
//	state = SP_STATE_WAIT_FOR_START;
//	state_timer = 0.0f;
//}
//
/////-------------------------------------------------------------------------
///// 
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::LineOutUpdate(float delta_game_time)
//{
//	RemoveHumansFromSetPlay();
//
//
//	if(state==SP_STATE_WAIT_FOR_START || state==SP_STATE_WAIT_BALL)
//	{
//		for ( MabVector<SSHumanPlayer*>::const_iterator i = game->GetHumanPlayers().begin(); i != game->GetHumanPlayers().end(); ++i )
//		{
//			SSHumanPlayer* human_player = *i;
//
//			if ( human_player->IsPlaying() && human_player->GetPlayer())
//			{
//				int controller_index = human_player->GetControllerIndex();
//
//				if ( control_action_manager->GetInput( controller_index, RU_INCREMENT_SETPLAY_PLAYERS ).Pressed() )		// KEY_M
//				{
//					game->GetTeam(0)->GetFormationManager()->IncrementVariableRoles(1);
//					game->GetTeam(1)->GetFormationManager()->IncrementVariableRoles(1);
//					state_timer = 0.0f;
//				}
//				if ( control_action_manager->GetInput( controller_index, RU_DECREMENT_SETPLAY_PLAYERS ).Pressed() )		// KEY_N
//				{
//					allow_role_interupt = true;
//					game->GetTeam(0)->GetFormationManager()->IncrementVariableRoles(-1);
//					game->GetTeam(1)->GetFormationManager()->IncrementVariableRoles(-1);
//					state_timer = 0.0f;
//				}
//			}
//		}
//	}
//
//
//	switch(state)
//	{
//	case SP_STATE_WAIT_FOR_START:
//		{
//			// 1. Check both teams formations to see if players are all in formation.
//			// 2. Check 'players' are in position for both sides.
//			// 3. After 10 seconds, start anyway!
//
//			//  - If both ok, then set state to SP_STATE_RUNNING.
//
//			bool do_start = false;
//
//
//
//
//
//
//
//			if(do_start || state_timer>5.0f)
//			{
//				state = SP_STATE_WAIT_BALL;
//				state_timer = 0.0f;
//			}
//		}
//		break;
//
//	case SP_STATE_WAIT_BALL:
//		// Wait for human/ai to throw ball.
//	
//		if(game->GetGameState()->GetBallHolder()==NULL)
//		{
//			// Ball has been thrown.
//			state = SP_STATE_RUNNING;
//			state_timer = 0.0f;
//		}
//		break;
//
//
//	case SP_STATE_RUNNING:	
//		if(state_timer>1.0f)
//		{
//			// For the moment the ball to an arbitary line-out player... (WAITING FOR GAME DESIGN!)
//			// (Note - can be the thrower at the moment!)
//
//			int team_no = game->GetRNG()->RandInt(2);
//			if(team_no>=2)
//				team_no = 1;
//
//			MabVector<ARugbyCharacter*> players;
//			
//			GetTeamPlayers(players,team_no, RURoleLineOut::RTTGetStaticType());
//
//			int pno = game->GetRNG()->RandInt(players.size());
//			if(pno>=(int)players.size())
//				pno = (int)players.size()-1;
//
//			RUGameState *game_state = game->GetGameState();
//			RUPlayerAttributes *plr_attribs = players[pno]->GetAttributes();
//
//			game_state->SetBallHolder(players[pno]);
//			game_state->SetAttackingTeam(plr_attribs->GetTeam());
//			game_state->SetPhase(RUGamePhase::PLAY);
//
//			// Because the phase has changed, the formation-setplay will de-activate - so we must 
//			// tell it to wait in the background for a few seconds (max) polling to see if the initial ball holder
//			// gets the ball, when this happens the formation-setplay 'animation' will kick in.
//			
//			plr_attribs->GetTeam()->GetFormationManager()->WaitForSetPlayOutCome();
//
//			// The player then decides what to do:- (pass the ball, form a maul, make a run) etc...
//			//			- (Could be a human player, so can't rely on a particular outcome, until it happens)
//			// The pass-to-player is set by formations set play... (if current formation has one)
//
//			game->GetEvents()->lineout_finished();
//
//			state = SP_STATE_FINISHED;
//			state_timer = 0.0f;
//		}
//		break;
//
//
//	case SP_STATE_FINISHED:
//		// Waiting for the game_phase to change...
//		break;
//
//	default:
//		break;
//
//	}
//	state_timer += delta_game_time;
//}
//
/////-------------------------------------------------------------------------
///// 
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::LineOutExit()
//{
//	if(state==SP_STATE_FINISHED)
//	{
//
//
//
//	}
//
//	state = SP_STATE_NONE;
//}
//
/////-------------------------------------------------------------------------
///// 
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::SetLineOutPosition(ARugbyCharacter* player, FVector &target_pos)
//{
//	RUTeam *team = player->GetAttributes()->GetTeam();
//	FVector pos = team->GetFormationManager()->GetPosition();
//
//	if(player->GetRole()->RTTGetType()==RURoleLineOutThrower::RTTGetStaticType())
//	{
//		target_pos = player->GetAttributes()->GetTeam()->GetFormationManager()->GetPosition();
//	}
//	else
//	{
//		// Botch, just line up based on shirt no.
//
//		float lineout_pos = 5.0f + (float)PlayerPositionEnum::GetPositionIndexFromPlayerPosition(player->GetAttributes()->GetPlayerPosition());
//
//		if(pos.x>0.0f)
//			pos.x -= lineout_pos;
//		else
//			pos.x += lineout_pos;
//
//		pos.z -= 0.5f * (float)team->GetPlayDirection();
//
//		target_pos = pos;
//	}
//}
//
//
//
//
//
/////**************************************************************************************************************
/////**************************************************************************************************************
/////**************************************************************************************************************
/////*** RUCK
//
//
//
//
/////-------------------------------------------------------------------------
///// 
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::RuckEnter()
//{
//	state = SP_STATE_RUNNING;
//	state_timer = 0.0f;
//}
//
/////-------------------------------------------------------------------------
///// 
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::RuckUpdate(float delta_game_time)
//{
//	RemoveHumansFromSetPlay();
//
//	if(state==SP_STATE_RUNNING)
//	{
//		for ( MabVector<SSHumanPlayer*>::const_iterator i = game->GetHumanPlayers().begin(); i != game->GetHumanPlayers().end(); ++i )
//		{
//			SSHumanPlayer* human_player = *i;
//
//			if ( human_player->IsPlaying() && human_player->GetPlayer() )
//			{
//				if ( human_player->IsOn( RU_INCREMENT_SETPLAY_PLAYERS ) )		// KEY_M
//				{
//					human_player->GetTeam()->GetFormationManager()->IncrementVariableRoles(1);
//					state_timer = 0.0f;
//				}
//			}
//		}
//	}
//
//
//	switch(state)
//	{
//	case SP_STATE_WAIT_BALL:
//		// Wait for human/ai to throw ball.
//		if(game->GetGameState()->GetBallHolder()==NULL)
//		{
//			// Ball has been thrown.
//			state = SP_STATE_RUNNING;
//			state_timer = 0.0f;
//		}
//		break;
//
//	case SP_STATE_RUNNING:	
//		if(state_timer>5.0f)
//		{
//			// Give the ball to the acting scrum-half following the ruck ready for release...
//			// For the moment after 5 seconds, the ball is given to a random side.
//
//			int team_no = game->GetRNG()->RandInt(2);			
//			if(team_no>=2)
//				team_no = 1;
//
//			MabVector<ARugbyCharacter*> players;
//			GetTeamPlayers(players, team_no, RURoleRuckScrumHalf::RTTGetStaticType() );
//			MABASSERT(players.size()>0);
//			
//			if(players.size()>0)
//			{
//				ARugbyCharacter* acting_scrum_half = players[0];
//
//				RUGameState *game_state = game->GetGameState();
//				RUPlayerAttributes *plr_attribs = acting_scrum_half->GetAttributes();
//
//				game_state->SetBallHolder(acting_scrum_half);
//				game_state->SetAttackingTeam(plr_attribs->GetTeam());
//				//game_state->SetPhase(RUGamePhase::PLAY);
//
//				// Because the phase has changed, the formation-setplay will de-activate - so we must 
//				// tell it to wait in the background for a few seconds (max) polling to see if the initial ball holder
//				// gets the ball, when this happens the formation-setplay 'animation' will kick in.
//
//				plr_attribs->GetTeam()->GetFormationManager()->WaitForSetPlayOutCome();
//
//				// The acting scrumhalf then decides what to do:- (pass the ball, form a maul, make a run) etc...
//				//			- (Could be a human player, so can't rely on a particular outcome, until it happens)
//				// The pass-to-player is set by formations set play... (if current formation has one)
//			}
//			else
//			{
//				// Belt & braces! - this should never happen, but if it does keep the game running.
//
//				game->GetGameState()->SetPhase(RUGamePhase::PLAY);			
//			}
//			state = SP_STATE_FINISHED;
//			state_timer = 0.0f;
//		}
//		else if(game)
//
//		break;
//
//
//	case SP_STATE_FINISHED:
//		// Waiting for the game_phase to change...
//		break;
//
//	default:
//		break;
//
//	}
//	state_timer += delta_game_time;
//}
//
/////-------------------------------------------------------------------------
///// 
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::RuckExit()
//{
//	state = SP_STATE_NONE;
//}
//
/////-------------------------------------------------------------------------
///// 
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::SetRuckPosition(ARugbyCharacter* player, FVector &target_pos)
//{
//	RUTeam *team = player->GetAttributes()->GetTeam();
//	FVector pos = team->GetFormationManager()->GetPosition();
//
//	if(player->GetRole()->RTTGetType()==RURoleRuckScrumHalf::RTTGetStaticType())
//	{
//		pos.z -= 1.0f * (float)team->GetPlayDirection();
//		
//		target_pos = pos;
//	}
//	else
//	{
//		// Botch, just line up based on shirt no.
//
//		pos.z -= 0.25f * (float)team->GetPlayDirection();
//
//		target_pos = pos;
//	}
//}
//
//
//
//
//
/////**************************************************************************************************************
/////**************************************************************************************************************
/////**************************************************************************************************************
/////*** SCRUM -----------------------------------------------------------------------
//
//
/////-------------------------------------------------------------------------
///// 
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::ScrumEnter()
//{
//	// temp
//	game->GetGameState()->SetBallHolder(game->GetGameState()->GetAttackingTeam()->GetPlayerByPosition(PP_HALFBACK));
//
//	state = SP_STATE_RUNNING;
//	state_timer = 0.0f;
//}
//
/////-------------------------------------------------------------------------
///// 
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::ScrumUpdate(float delta_game_time)
//{
//	RemoveHumansFromSetPlay();
//
//	switch(state)
//	{
//	case SP_STATE_RUNNING:	
//		if(state_timer>5.0f)
//		{
//			// Give the ball to the acting scrum-half following the scrum ready for release...
//			// For the moment after 5 seconds, the ball is given to a random side.
//
//			int team_no = game->GetRNG()->RandInt(2);
//			if(team_no>=2)
//				team_no = 1;
//
//			RUGameState *game_state = game->GetGameState();
//
//			MabVector<ARugbyCharacter*> players;
//			GetTeamPlayers(players, team_no, RURoleScrumHalfBack::RTTGetStaticType() );
//			MABASSERT(players.size()>0);
//
//			if(players.size()>0)
//			{
//				ARugbyCharacter* acting_scrum_half = players[0];
//
//				RUPlayerAttributes *plr_attribs = acting_scrum_half->GetAttributes();
//
//				game_state->SetBallHolder(acting_scrum_half);
//				game_state->SetAttackingTeam(plr_attribs->GetTeam());
//
//				//temp shouldn't enter play till after scrum is finished
//				//game_state->SetPhase(RUGamePhase::PLAY);
//			}
//			else
//			{
//				game->GetGameState()->SetPhase(RUGamePhase::PLAY);			
//			}
//			state = SP_STATE_WAIT_BALL;
//			state_timer = 0.0f;
//		}
//		//else if(game)
//			break;
//	case SP_STATE_WAIT_BALL:
//		{
//			MabVector<ARugbyCharacter*> players;
//			GetTeamPlayers(players, SIDE_A, RURoleScrumHalfBack::RTTGetStaticType() );
//			if(players.size()>0)
//			{
//				if(players[0] != game->GetGameState()->GetBallHolder())
//				{
//					GetTeamPlayers(players, SIDE_B, RURoleScrumHalfBack::RTTGetStaticType() );
//					if(players.size()==0)
//					{
//						break;
//					}
//				}
//				if(players[0]->GetRole()->IsInterruptable())
//				{
//					state = SP_STATE_FINISHED;
//					state_timer = 0.0f;
//				}
//			}
//		}
//		break;
//
//	case SP_STATE_FINISHED:
//		// Waiting for the game_phase to change...
//		break;
//
//	default:
//		break;
//
//	}
//	state_timer += delta_game_time;
//}
//
/////-------------------------------------------------------------------------
///// 
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::ScrumExit()
//{
//	state = SP_STATE_NONE;
//}
//
/////-------------------------------------------------------------------------
///// 
/////-------------------------------------------------------------------------
//
//void SSSetPlayManager::SetScrumPosition(ARugbyCharacter* player, FVector &target_pos)
//{
//	// arbitrary scrum positioning
//	FVector scrum[8];
//	scrum[0] = FVector(1.0f, 0.0f, 1.0f);  //tight head
//	scrum[1] = FVector(0.0f, 0.0f, 1.0f);  //hooker
//	scrum[2] = FVector(-1.0f, 0.0f, 1.0f); //loose head
//	scrum[3] = FVector(0.5f, 0.0f, 2.0f);  //lock
//	scrum[4] = FVector(-0.5f, 0.0f, 2.0f); //lock
//	scrum[5] = FVector(1.5f, 0.0f, 2.0f);  //blind flank
//	scrum[6] = FVector(-1.5f, 0.0f, 2.0f); //open flank
//	scrum[7] = FVector(0.0f, 0.0f, 3.0f);  //number 8
//
//	RUTeam *team = player->GetAttributes()->GetTeam();
//	FVector pos = team->GetFormationManager()->GetPosition();
//
//	if(player->GetRole()->RTTGetType()==RURoleScrumHalfBack::RTTGetStaticType())
//	{
//		pos.z -= 1.0f * (float)team->GetPlayDirection();
//		pos.x -= 4.0f * (float)team->GetPlayDirection();
//
//		target_pos = pos;
//	}
//	else
//	{
//		target_pos = scrum[PlayerPositionEnum::GetPositionIndexFromPlayerPosition(player->GetAttributes()->GetPlayerPosition())];
//		target_pos.z *= -(float)team->GetPlayDirection();
//
//		target_pos += pos;
//	}
//}