<html>
<head>
<title>Blueprint Reference | Common</title>
<link rel="stylesheet" href="style/docs.css">
<link rel="stylesheet" href="style/code_highlight.css">
<script type="text/javascript" src="scripts/language-selector.js"></script></head>
<body>
<div class="docs-body">
<div class="manual-toc">
<p>Unreal Integration 2.02</p>
<ul>
<li><a href="welcome.html">Welcome to FMOD for Unreal</a></li>
<li><a href="user-guide.html">User Guide</a></li>
<li><a href="settings.html">Settings</a></li>
<li><a href="plugins.html">Plugins</a></li>
<li><a href="niagara.html">Niagara Integration</a></li>
<li><a href="api-reference.html">API Reference</a></li>
<li class="manual-current-chapter manual-inactive-chapter"><a href="blueprint-reference.html">Blueprint Reference</a><ul class="subchapters"><li><a href="blueprint-reference-bus.html">Bus</a></li><li class="manual-current-chapter manual-active-chapter"><a href="blueprint-reference-common.html">Common</a></li><li><a href="blueprint-reference-component.html">Component</a></li><li><a href="blueprint-reference-eventinstance.html">Event Instance</a></li><li><a href="blueprint-reference-asynchronous-loading.html">Asynchronous Loading</a></li><li><a href="blueprint-reference-enums.html">Enums</a></li><li><a href="blueprint-reference-structs.html">Structs</a></li><li><a href="blueprint-reference-utilities.html">Utilities</a></li></ul></li>
<li><a href="platform-specifics.html">Platform Specifics</a></li>
<li><a href="troubleshooting.html">Troubleshooting</a></li>
<li><a href="audiolink.html">AudioLink</a></li>
<li><a href="glossary.html">Glossary</a></li>
</ul>
</div>
<div class="manual-content api">
<h1>7. Blueprint Reference | Common</h1>
<p>The common section contains methods for controlling the FMOD system.</p>
<p><strong>Methods:</strong></p>
<ul>
<li><span><a class="apilink" href="blueprint-reference-common.html#find-asset-by-name" title="Find an asset by name.">Find Asset By Name</a> Find an asset by name.</span></li>
<li><span><a class="apilink" href="blueprint-reference-common.html#find-event-by-name" title="Find an Event by name.">Find Event By Name</a> Find an Event by name.</span></li>
<li><span><a class="apilink" href="blueprint-reference-common.html#find-event-instances" title="Return a list of all event instances that are playing for this event.">Find Event Instances</a> Return a list of all event instances that are playing for this event.</span></li>
<li><span><a class="apilink" href="blueprint-reference-common.html#get-global-parameter-by-name" title="Get a global parameter value from the System.">Get Global Parameter By Name</a> Get a global parameter value from the System.</span></li>
<li><span><a class="apilink" href="blueprint-reference-common.html#get-global-parameter-value-by-name" title="Get a global parameter value from the System.">Get Global Parameter Value By Name</a> Get a global parameter value from the System.</span></li>
<li>
<p><span><a class="apilink" href="blueprint-reference-common.html#get-output-drivers" title="List all output device names.">Get Output Drivers</a> List all output device names.</span></p>
</li>
<li>
<p><span><a class="apilink" href="blueprint-reference-common.html#set-global-parameter-by-name" title="Set a global parameter from the System.">Set Global Parameter By Name</a> Set a global parameter from the System.</span></p>
</li>
<li><span><a class="apilink" href="blueprint-reference-common.html#set-output-driver-by-name" title="Set current output device by name or part of the name.">Set Output Driver By Name</a> Set current output device by name or part of the name.</span></li>
<li><span><a class="apilink" href="blueprint-reference-common.html#set-output-driver-by-index" title="Set current output device by its index from GetOutputDrivers.">Set Output Driver By Index</a> Set current output device by its index from GetOutputDrivers.</span></li>
<li>
<p><span><a class="apilink" href="blueprint-reference-common.html#set-locale" title="Set the active locale for subsequent bank loads.">Set Locale</a> Set the active locale for subsequent bank loads.</span></p>
</li>
<li>
<p><span><a class="apilink" href="blueprint-reference-common.html#mixer-suspend" title="Suspend the FMOD mixer.">Mixer Suspend</a> Suspend the FMOD mixer.</span></p>
</li>
<li>
<p><span><a class="apilink" href="blueprint-reference-common.html#mixer-resume" title="Resume the FMOD mixer.">Mixer Resume</a> Resume the FMOD mixer.</span></p>
</li>
<li>
<p><span><a class="apilink" href="blueprint-reference-common.html#load-bank" title="Load a bank.">Load Bank</a> Load a bank.</span></p>
</li>
<li><span><a class="apilink" href="blueprint-reference-common.html#unload-bank" title="Unload a bank.">Unload Bank</a> Unload a bank.</span></li>
<li><span><a class="apilink" href="blueprint-reference-common.html#is-bank-loaded" title="Return true if a bank is loaded.">Is Bank Loaded</a> Return true if a bank is loaded.</span></li>
<li><span><a class="apilink" href="blueprint-reference-common.html#load-bank-sample-data" title="Load bank sample data.">Load Bank Sample Data</a> Load bank sample data.</span></li>
<li>
<p><span><a class="apilink" href="blueprint-reference-common.html#unload-bank-sample-data" title="Unload bank sample data.">Unload Bank Sample Data</a> Unload bank sample data.</span></p>
</li>
<li>
<p><span><a class="apilink" href="blueprint-reference-common.html#play-event-2d" title="Play an event without a specified location.">Play Event 2D</a> Play an event without a specified location.</span></p>
</li>
<li><span><a class="apilink" href="blueprint-reference-common.html#play-event-at-location" title="Plays an event at the given location.">Play Event At Location</a> Plays an event at the given location.</span></li>
<li><span><a class="apilink" href="blueprint-reference-common.html#play-event-attached" title="Play an event attached to and following the specified component.">Play Event Attached</a> Play an event attached to and following the specified component.</span></li>
<li><span><a class="apilink" href="blueprint-reference-common.html#load-event-sample-data" title="Load event sample data.">Load Event Sample Data</a> Load event sample data.</span></li>
<li>
<p><span><a class="apilink" href="blueprint-reference-common.html#unload-event-sample-data" title="Unload event sample data.">Unload Event Sample Data</a> Unload event sample data.</span></p>
</li>
<li>
<p><span><a class="apilink" href="blueprint-reference-common.html#vca-set-volume" title="Set volume on a VCA.">VCA Set Volume</a> Set volume on a VCA.</span></p>
</li>
</ul>
<h2 api="function" id="find-asset-by-name"><a href="#find-asset-by-name">Find Asset By Name</a></h2>
<p>Find an asset by name.</p>
<p><img alt="Find Asset By Name" src="images/find-asset-by-name.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="n">UFMODAsset</span> <span class="o">*</span><span class="nf">FindAssetByName</span><span class="p">(</span>
  <span class="k">const</span> <span class="n">FString</span> <span class="o">&amp;</span><span class="n">Name</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>Name</dt>
<dd>The asset name.</dd>
</dl>
<p><strong>See Also:</strong> <a class="apilink" href="api-reference-ufmodasset.html">UFMODAsset</a></p>
<h2 api="function" id="find-event-by-name"><a href="#find-event-by-name">Find Event By Name</a></h2>
<p>Find an Event by name.</p>
<p><img alt="Find Event By Name" src="images/find-event-by-name.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="n">UFMODEvent</span> <span class="o">*</span><span class="nf">FindEventByName</span><span class="p">(</span>
  <span class="k">const</span> <span class="n">FString</span> <span class="o">&amp;</span><span class="n">Name</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>Name</dt>
<dd>The event name.</dd>
</dl>
<p><strong>See Also:</strong> <a class="apilink" href="api-reference-ufmodevent.html">UFMODEvent</a></p>
<h2 api="function" id="find-event-instances"><a href="#find-event-instances">Find Event Instances</a></h2>
<p>Return a list of all event instances that are playing for this event.</p>
<p><img alt="Find Event Instances" src="images/find-event-instances.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="n">TArray</span><span class="o">&lt;</span><span class="n">FFMODEventInstance</span><span class="o">&gt;</span> <span class="n">FindEventInstances</span><span class="p">(</span>
  <span class="n">UObject</span> <span class="o">*</span><span class="n">WorldContextObject</span><span class="p">,</span>
  <span class="n">UFMODEvent</span> <span class="o">*</span><span class="n">Event</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>WorldContextObject</dt>
<dd>Object from current world context.</dd>
<dt>Event</dt>
<dd><a class="apilink" href="api-reference-ufmodevent.html">UFMODEvent</a> to find instances of.</dd>
</dl>
<p>Be careful using this function because it is possible to find and alter any playing sound, even ones owned by other audio components.</p>
<p><strong>See Also:</strong> <a class="apilink" href="api-reference-ufmodblueprintstatics.html#ffmodeventinstance">FFMODEventInstance</a></p>
<h2 api="function" id="get-global-parameter-by-name"><a href="#get-global-parameter-by-name">Get Global Parameter By Name</a></h2>
<p>Get a global parameter value from the System.</p>
<p><img alt="Get Global Parameter By Name" src="images/get-global-parameter-by-name.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="kt">float</span> <span class="nf">GetGlobalParameterByName</span><span class="p">(</span>
  <span class="n">FName</span> <span class="n">Name</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>Name</dt>
<dd>Name of the parameter.</dd>
</dl>
<p>Will be deprecated in FMOD 2.01, use <a class="apilink" href="blueprint-reference-common.html#get-global-parameter-value-by-name">Get Global Parameter Value By Name</a> instead.</p>
<p><strong>See Also:</strong> <a class="apilink" href="blueprint-reference-common.html#set-global-parameter-by-name">Set Global Parameter By Name</a></p>
<h2 api="function" id="get-global-parameter-value-by-name"><a href="#get-global-parameter-value-by-name">Get Global Parameter Value By Name</a></h2>
<p>Get a global parameter value from the System.</p>
<p><img alt="Get Global Parameter Value By Name" src="images/get-global-parameter-value-by-name.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="kt">void</span> <span class="nf">GetGlobalParameterValueByName</span><span class="p">(</span>
    <span class="n">FName</span> <span class="n">Name</span><span class="p">,</span>
    <span class="kt">float</span> <span class="o">&amp;</span><span class="n">UserValue</span><span class="p">,</span>
    <span class="kt">float</span> <span class="o">&amp;</span><span class="n">FinalValue</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>Name</dt>
<dd>Name of parameter</dd>
<dt>UserValue</dt>
<dd>Parameter value as set from the public API.</dd>
<dt>FinalValue</dt>
<dd>Final combined parameter value.</dd>
</dl>
<h2 api="function" id="get-output-drivers"><a href="#get-output-drivers">Get Output Drivers</a></h2>
<p>List all output device names.</p>
<p><img alt="Get Output Drivers" src="images/get-output-drivers.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="n">TArray</span><span class="o">&lt;</span><span class="n">FString</span><span class="o">&gt;</span> <span class="n">GetOutputDrivers</span><span class="p">();</span>
</pre></div>

<p><strong>See Also:</strong> <a class="apilink" href="blueprint-reference-common.html#set-output-driver-by-index">Set Output Driver By Index</a>, <a class="apilink" href="blueprint-reference-common.html#set-output-driver-by-name">Set Output Driver By Name</a></p>
<h2 api="function" id="is-bank-loaded"><a href="#is-bank-loaded">Is Bank Loaded</a></h2>
<p>Return true if a bank is loaded.</p>
<p><img alt="Is Bank Loaded" src="images/is-bank-loaded.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="kt">bool</span> <span class="nf">IsBankLoaded</span><span class="p">(</span>
  <span class="n">UFMODBank</span> <span class="o">*</span><span class="n">Bank</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>Bank</dt>
<dd><a class="apilink" href="api-reference-ufmodbank.html">UFMODBank</a> to query.</dd>
</dl>
<p><strong>See Also:</strong> <a class="apilink" href="blueprint-reference-common.html#load-bank">Load Bank</a></p>
<h2 api="function" id="load-bank"><a href="#load-bank">Load Bank</a></h2>
<p>Load a bank.</p>
<p><img alt="Load Bank" src="images/load-bank.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="kt">void</span> <span class="nf">LoadBank</span><span class="p">(</span>
  <span class="n">UFMODBank</span> <span class="o">*</span><span class="n">Bank</span><span class="p">,</span>
  <span class="kt">bool</span> <span class="n">bBlocking</span><span class="p">,</span>
  <span class="kt">bool</span> <span class="n">bLoadSampleData</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>Bank</dt>
<dd><a class="apilink" href="api-reference-ufmodbank.html">UFMODBank</a> to use.</dd>
<dt>bBlocking</dt>
<dd>Whether the bank will load synchronously.</dd>
<dt>bLoadSampleData</dt>
<dd>Whether sample data will be preloaded immediately.</dd>
</dl>
<h2 api="function" id="load-bank-sample-data"><a href="#load-bank-sample-data">Load Bank Sample Data</a></h2>
<p>Load bank sample data.</p>
<p><img alt="Load Bank Sample Data" src="images/load-bank-sample-data.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="kt">void</span> <span class="nf">LoadBankSampleData</span><span class="p">(</span>
  <span class="n">UFMODBank</span> <span class="o">*</span><span class="n">Bank</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>Bank</dt>
<dd><a class="apilink" href="api-reference-ufmodbank.html">UFMODBank</a> to use.</dd>
</dl>
<h2 api="function" id="load-event-sample-data"><a href="#load-event-sample-data">Load Event Sample Data</a></h2>
<p>Load event sample data.</p>
<p><img alt="Load Event Sample Data" src="images/load-event-sample-data.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="kt">void</span> <span class="nf">LoadEventSampleData</span><span class="p">(</span>
  <span class="n">UObject</span> <span class="o">*</span><span class="n">WorldContextObject</span><span class="p">,</span>
  <span class="n">UFMODEvent</span> <span class="o">*</span><span class="n">Event</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>WorldContextObject</dt>
<dd>Object from current world context.</dd>
<dt>Event</dt>
<dd><a class="apilink" href="api-reference-ufmodevent.html">UFMODEvent</a> to use.</dd>
</dl>
<p>This can be done ahead of time to avoid loading stalls.</p>
<h2 api="function" id="mixer-resume"><a href="#mixer-resume">Mixer Resume</a></h2>
<p>Resume the FMOD mixer.</p>
<p><img alt="Mixer Resume" src="images/mixer-resume.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="kt">void</span> <span class="nf">MixerResume</span><span class="p">();</span>
</pre></div>

<p>Used when resuming the application.</p>
<h2 api="function" id="mixer-suspend"><a href="#mixer-suspend">Mixer Suspend</a></h2>
<p>Suspend the FMOD mixer.</p>
<p><img alt="Mixer Suspend" src="images/mixer-suspend.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="kt">void</span> <span class="nf">MixerSuspend</span><span class="p">();</span>
</pre></div>

<p>Used when suspending the application.</p>
<h2 api="function" id="play-event-2d"><a href="#play-event-2d">Play Event 2D</a></h2>
<p>Play an event without a specified location.</p>
<p><img alt="Play Event 2D" src="images/play-event-2d.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="n">FFMODEventInstance</span> <span class="nf">PlayEvent2D</span><span class="p">(</span>
  <span class="n">UObject</span> <span class="o">*</span><span class="n">WorldContextObject</span><span class="p">,</span>
  <span class="n">UFMODEvent</span> <span class="o">*</span><span class="n">Event</span><span class="p">,</span>
  <span class="kt">bool</span> <span class="n">bAutoPlay</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>WorldContextObject</dt>
<dd>Object from current world context.</dd>
<dt>Event</dt>
<dd><a class="apilink" href="api-reference-ufmodevent.html">UFMODEvent</a> to use.</dd>
<dt>bAutoPlay</dt>
<dd>Start the event automatically.</dd>
</dl>
<p>This returns an <a class="apilink" href="api-reference-ufmodblueprintstatics.html#ffmodeventinstance">FFMODEventInstance</a>.<br />
The sound does not travel with any actor.</p>
<h2 api="function" id="play-event-at-location"><a href="#play-event-at-location">Play Event At Location</a></h2>
<p>Plays an event at the given location.</p>
<p><img alt="Play Event At Location" src="images/play-event-at-location.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="n">FFMODEventInstance</span> <span class="nf">PlayEventAtLocation</span><span class="p">(</span>
  <span class="n">UObject</span> <span class="o">*</span><span class="n">WorldContextObject</span><span class="p">,</span>
  <span class="n">UFMODEvent</span> <span class="o">*</span><span class="n">Event</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">FTransform</span> <span class="o">&amp;</span><span class="n">Location</span><span class="p">,</span>
  <span class="kt">bool</span> <span class="n">bAutoPlay</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>WorldContextObject</dt>
<dd>Object from current world context.</dd>
<dt>Event</dt>
<dd><a class="apilink" href="api-reference-ufmodevent.html">UFMODEvent</a> to use.</dd>
<dt>Location</dt>
<dd>World position to play event at.</dd>
<dt>bAutoPlay</dt>
<dd>Start the event automatically.</dd>
</dl>
<p>This returns an FMOD Event Instance.<br />
The sound does not travel with any actor.</p>
<h2 api="function" id="play-event-attached"><a href="#play-event-attached">Play Event Attached</a></h2>
<p>Play an event attached to and following the specified component.</p>
<p><img alt="Play Event Attached" src="images/play-event-attached.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="k">class</span> <span class="nc">UFMODAudioComponent</span> <span class="o">*</span><span class="nf">PlayEventAttached</span><span class="p">(</span>
  <span class="n">UFMODEvent</span> <span class="o">*</span><span class="n">Event</span><span class="p">,</span>
  <span class="n">USceneComponent</span> <span class="o">*</span><span class="n">AttachToComponent</span><span class="p">,</span>
  <span class="n">FName</span> <span class="n">AttachPointName</span><span class="p">,</span>
  <span class="n">FVector</span> <span class="n">Location</span><span class="p">,</span>
  <span class="n">EAttachLocation</span><span class="o">::</span><span class="n">Type</span> <span class="n">LocationType</span><span class="p">,</span>
  <span class="kt">bool</span> <span class="n">bStopWhenAttachedToDestroyed</span><span class="p">,</span>
  <span class="kt">bool</span> <span class="n">bAutoPlay</span><span class="p">,</span>
  <span class="kt">bool</span> <span class="n">bAutoDestroy</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>Event</dt>
<dd><a class="apilink" href="api-reference-ufmodevent.html">UFMODEvent</a> to use.</dd>
<dt>AttachToComponent</dt>
<dd>Component to attach to.</dd>
<dt>AttachPointName <span><a class="token" href="glossary.html#documentation-conventions" title="Optional">Opt</a></span></dt>
<dd>Named point within the AttachComponent to play the sound at.</dd>
<dt>Location</dt>
<dd>Depending on the value of Location Type this is either a relative offset from the attach component/point or an absolute world position that will be translated to a relative offset.</dd>
<dt>LocationType</dt>
<dd>Location is a relative offset or an absolute world position.</dd>
<dt>bStopWhenAttachedToDestroyed</dt>
<dd>The sound should stop playing when the owner of the attach to component is destroyed.</dd>
<dt>bAutoPlay</dt>
<dd>Start the event automatically.</dd>
<dt>bAutoDestroy</dt>
<dd>Automatically destroy the audio component when the sound is stopped.</dd>
</dl>
<h2 api="function" id="set-global-parameter-by-name"><a href="#set-global-parameter-by-name">Set Global Parameter By Name</a></h2>
<p>Set a global parameter from the System.</p>
<p><img alt="Set Global Parameter By Name" src="images/set-global-parameter-by-name.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="kt">void</span> <span class="nf">SetGlobalParameterByName</span><span class="p">(</span>
  <span class="n">FName</span> <span class="n">Name</span><span class="p">,</span>
  <span class="kt">float</span> <span class="n">Value</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>Name</dt>
<dd>Name of parameter.</dd>
<dt>Value</dt>
<dd>Value to apply to the parameter.</dd>
</dl>
<h2 api="function" id="set-locale"><a href="#set-locale">Set Locale</a></h2>
<p>Set the active locale for subsequent bank loads.</p>
<p><img alt="Set Locale" src="images/set-locale.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="kt">void</span> <span class="nf">SetLocale</span><span class="p">(</span><span class="k">const</span> <span class="n">FString</span><span class="o">&amp;</span> <span class="n">Locale</span><span class="p">);</span>
</pre></div>

<h2 api="function" id="set-output-driver-by-index"><a href="#set-output-driver-by-index">Set Output Driver By Index</a></h2>
<p>Set current output device by its index from GetOutputDrivers.</p>
<p><img alt="Set Output Driver By Index" src="images/set-output-driver-by-index.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="kt">void</span> <span class="nf">SetOutputDriverByIndex</span><span class="p">(</span>
  <span class="kt">int</span> <span class="n">NewDriverIndex</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>NewDriverIndex</dt>
<dd>Index of driver to use.</dd>
</dl>
<p><strong>See Also:</strong> <a class="apilink" href="blueprint-reference-common.html#get-output-drivers">Get Output Drivers</a></p>
<h2 api="function" id="set-output-driver-by-name"><a href="#set-output-driver-by-name">Set Output Driver By Name</a></h2>
<p>Set current output device by name or part of the name.</p>
<p><img alt="Set Output Driver By Name" src="images/set-output-driver-by-name.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="kt">void</span> <span class="nf">SetOutputDriverByName</span><span class="p">(</span>
  <span class="n">FString</span> <span class="n">NewDriverName</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>NewDriverName</dt>
<dd>Name of new driver to use.</dd>
</dl>
<p><strong>See Also:</strong> <a class="apilink" href="blueprint-reference-common.html#get-output-drivers">Get Output Drivers</a></p>
<h2 api="function" id="unload-bank"><a href="#unload-bank">Unload Bank</a></h2>
<p>Unload a bank.</p>
<p><img alt="Unload Bank" src="images/unload-bank.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="kt">void</span> <span class="nf">UnloadBank</span><span class="p">(</span>
  <span class="n">UFMODBank</span> <span class="o">*</span><span class="n">Bank</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>Bank</dt>
<dd><a class="apilink" href="api-reference-ufmodbank.html">UFMODBank</a> to use.</dd>
</dl>
<h2 api="function" id="unload-bank-sample-data"><a href="#unload-bank-sample-data">Unload Bank Sample Data</a></h2>
<p>Unload bank sample data.</p>
<p><img alt="Unload Bank Sample Data" src="images/unload-bank-sample-data.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="kt">void</span> <span class="nf">UnloadBankSampleData</span><span class="p">(</span>
  <span class="n">UFMODBank</span> <span class="o">*</span><span class="n">Bank</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>Bank</dt>
<dd><a class="apilink" href="api-reference-ufmodbank.html">UFMODBank</a> to use.</dd>
</dl>
<h2 api="function" id="unload-event-sample-data"><a href="#unload-event-sample-data">Unload Event Sample Data</a></h2>
<p>Unload event sample data.</p>
<p><img alt="Unload Event Sample Data" src="images/unload-event-sample-data.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="kt">void</span> <span class="nf">UnloadEventSampleData</span><span class="p">(</span>
  <span class="n">UObject</span> <span class="o">*</span><span class="n">WorldContextObject</span><span class="p">,</span>
  <span class="n">UFMODEvent</span> <span class="o">*</span><span class="n">Event</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>WorldContextObject</dt>
<dd>Object from current world context.</dd>
<dt>Event</dt>
<dd><a class="apilink" href="api-reference-ufmodevent.html">UFMODEvent</a> to use.</dd>
</dl>
<h2 api="function" id="vca-set-volume"><a href="#vca-set-volume">VCA Set Volume</a></h2>
<p>Set volume on a VCA.</p>
<p><img alt="VCA Set Volume" src="images/vca-set-volume.png" /></p>
<p>
<div class="language-selector">
<div class="language-tab" data-language="language-cpp">C++</div>
</div>
</p>
<div class="highlight language-cpp"><pre><span></span><span class="k">static</span> <span class="kt">void</span> <span class="nf">VCASetVolume</span><span class="p">(</span>
  <span class="n">UFMODVCA</span> <span class="o">*</span><span class="n">Vca</span><span class="p">,</span>
  <span class="kt">float</span> <span class="n">Volume</span>
<span class="p">);</span>
</pre></div>

<dl>
<dt>Vca</dt>
<dd><a class="apilink" href="api-reference-ufmodvca.html">UFMODVCA</a> to use.</dd>
<dt>Volume</dt>
<dd>Volume value.</dd>
</dl></div>

<p class="manual-footer">Unreal Integration 2.02.20 (2023-12-12). &copy; 2023 Firelight Technologies Pty Ltd.</p>
</body>
</html>

</div>
