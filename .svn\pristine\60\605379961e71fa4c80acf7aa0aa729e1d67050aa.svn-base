// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIScreenCareerCompetitionInfo.h"

#include "Rugby/RugbyGameInstance.h"

// RC3
#include "Rugby/Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RUActiveCompetition.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Database.h"
#include "Rugby/Match/RugbyUnion/Statistics/RUStatisticsSystem.h"
#include "Rugby/Match/RugbyUnion/RUDatabaseConstants.h"

// Utility
#include "Rugby/Utility/Helpers/SIFGameHelpers.h"
#include "Rugby/Utility/Helpers/SIFUIHelpers.h"

// WW UI Generated Headers
#include "Rugby/UI/GeneratedHeaders/WWUIScreenCareerCompetitionInfo_UI_Namespace.h"
#include "Rugby/UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"

// WW UI Populators
#include "Rugby/UI/Populators/WWUIPopulatorCareerStandings.h"
#include "Rugby/UI/Populators/WWUIPopulatorCareerSchedule.h"
#include "Rugby/UI/Populators/WWUIPopulatorCompetitionFixtures.h"

// WW UI Screens
#include "Rugby/UI/Screens/WWUIScreenCareerTeamSquad.h"
#include "Rugby/UI/Screens/Modals/WWUIModalSimulateMatch.h"
#include "Rugby/UI/Screens/Modals/WWUIModalWarningMessage.h"
#include "Rugby/UI/WWUICareerGlobal.h"

// WW UI Engine
#include "WWUITranslationManager.h"
#include "WWUITabSwitcher.h"
#include "WWUILegendBox.h"
#include "WWUIListField.h"
#include "WWUIFunctionLibrary.h"
#include "Rugby/UI/Components/WWUIModularLegendText.h"

// UE4 Engine
#include "Image.h"
#include "TextBlock.h"
#include "WidgetSwitcher.h"
#include "VerticalBox.h"
#include "HorizontalBox.h"
#include "Match/RugbyUnion/CompetitionMode/RUDBCompetitionTypes.h"
#include "UI/Components/WWUICustomScrollbar.h"
#include "ScrollBox.h"
#include "IConsoleManager.h"
#include "Match/HUD/RUHUDUpdater.h"

static const float INITIAL_CAROUSEL_BUMP_TIME = 0.1f;

#define TRIGGER_DELAY_TIME		(1.0f)

//This fixes an issue with Colours not being accurate to HEX
#define FLINEARCOLOR_MULTIPLY(col) (col * col)

void UWWUIScreenCareerCompetitionInfo::Startup(UWWUIStateScreenData* InData)
{
	CurrentScreenState = EScreenState::STATE_INIT;

	CurrentTab = ECompetitionInfoTab::CIT_STANDINGS;

	//---- Competition Standings ----
	CareerStandingsConferenceIndex = 0;

	// Get the career mode manager
	UWorld* pWorld = GetWorld();
	if (pWorld)
	{
		pRugbyGameInstance = Cast<URugbyGameInstance>(pWorld->GetGameInstance());
		if (pRugbyGameInstance)
		{
			pCareerModeManager = pRugbyGameInstance->GetCareerModeManager();
		}
	}

	if (!pCareerModeManager)
	{
		ensureMsgf(pCareerModeManager, TEXT("CareerSetup: Cannot get the career manager, this should not occur!"));
		return;
	}

	if (!pRugbyGameInstance)
	{
		ensureMsgf(pRugbyGameInstance, TEXT("CareerSetup: Cannot get the rugby game instance, this should not occur!"));
		return;
	}

	// Set the default fixture filter
	CurrentFixtureType = ECompetitionInfoFixtureType::PLAYER_GAMES;
	CareerFixturesSwitchCategory(true);

	CurrentScreenState = EScreenState::STATE_STARTUP;
}


//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::RegisterFunctions()
{
	AddInputAction("UI_Back", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerCompetitionInfo::OnBack), false, true); // B

	AddInputAction("RU_UI_ACTION_TABRIGHT", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerCompetitionInfo::IncrementTab)); // B
	AddInputAction("RU_UI_ACTION_TABLEFT", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerCompetitionInfo::DecrementTab)); // B

#if PLATFORM_SWITCH 
	AddInputAxis("UI_Triggers_Axis", FWWUIScreenAxisDelegate::CreateUObject(this, &UWWUIScreenCareerCompetitionInfo::OnRotationInput));
#else
	AddInputAction("UI_LeftTrigger", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerCompetitionInfo::OnLeftTrigger)); // B
	AddInputAction("UI_RightTrigger", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerCompetitionInfo::OnRightTrigger)); // B
#endif

	AddInputAction("RU_UI_ACTION_CAREERMODE_SCHEDULE_GO_TO_CURRENT_DATE", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerCompetitionInfo::OnFaceLeft));

	AddInputAction("RU_UI_ACTION_CAREERMODE_SCHEDULE_SIMULATE_TO_MATCH", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerCompetitionInfo::OnFaceTop));
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::TableOnSelectionChange(FString InTableId, int OldIdx, int NewIdx)
{
	if (InTableId == WWUIScreenCareerCompetitionInfo_UI::ScrollBoxSchedule)
	{
		ScheduleAndFixturesSelectedIndex = NewIdx;

		UWWUIScrollBox* pCurrentScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(InTableId));

		if (pCurrentScrollBox)
		{
			UWWUIListField* pLastField = nullptr;
			bool bSetNextFieldVisible = false;
			int32 CurrentIndex = NewIdx;

			for (int i = 0; i < pCurrentScrollBox->GetListLength(); i++)
			{
				UWWUIListField* pField = pCurrentScrollBox->GetListField(i);

				// Hide the widget if it does not have a button (so it must be a bracket or a spacer).
				if (pField)
				{
					if (pField->GetButtonWidget() == nullptr)
					{
						if (bSetNextFieldVisible)
						{
							pField->SetVisibility(ESlateVisibility::Visible);
							bSetNextFieldVisible = false;
						}
						else
						{
							pField->SetVisibility(ESlateVisibility::Hidden);
							pLastField = pField;
						}
					}
				}

				// This is the active bracket.
				if (i == CurrentIndex)
				{
					// Set the last bracket widget we came across to true.
					if (pLastField)
					{
						pLastField->SetVisibility(ESlateVisibility::Visible);
					}

					// Set a flag so we set the next bracket widget to visible.
					bSetNextFieldVisible = true;
				}
			}
		}

		CareerSchedulePopulateSelectedMatch(NewIdx);
	}
	else if (InTableId == WWUIScreenCareerCompetitionInfo_UI::ScrollBoxAllFixtures || InTableId == WWUIScreenCareerCompetitionInfo_UI::ScrollBoxPlayerFixtures)
	{
		ScheduleAndFixturesSelectedIndex = NewIdx;

		if (NewIdx == 1)
        {
            UWWUIScrollBox* pTacticsScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(InTableId));
            if (pTacticsScrollBox)
            {
                UScrollBox* pScrollBox = pTacticsScrollBox->GetScrollBox();
                if (pScrollBox)
                {
                    pScrollBox->ScrollWidgetIntoView(pTacticsScrollBox->GetListField(0));
                }
            }
        }

		CareerFixturesUpdateLegend(NewIdx);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::ExecuteTableFunction(FString InTableId, int InIdx, FString InAction, FString InActionString)
{
	if (CurrentTab == ECompetitionInfoTab::CIT_STANDINGS)
	{
		CareerStandingsViewSquadOptionOnClick(InIdx);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::OnInFocus()
{
	
	SetSlateLayoutCaching(true);

	CurrentScreenState = EScreenState::STATE_MAIN;
	OnNewTab(CurrentTab);

	m_canTriggerAxis = true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::OnOutFocus(bool ShouldOutFocus)
{
	SetSlateLayoutCaching(false);

	if (CareerScheduleBumpCarousellTimer.IsValid())
	{
		UWWUIFunctionLibrary::StopTimer(CareerScheduleBumpCarousellTimer);
	}
	
	if (m_triggerAxisHandle.IsValid())
	{
		UWWUIFunctionLibrary::StopTimer(m_triggerAxisHandle);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::OnWindowEnter()
{
	CurrentScreenState = EScreenState::STATE_MAIN;
	CareerScheduleUpdateHeading();

	// Populate these scrollboxes now to reduce lag.
	for (int32 i = 0; i < (int32)ECompetitionInfoFixtureType::MAX; i++)
	{
		ECompetitionInfoFixtureType FixtureType = (ECompetitionInfoFixtureType)i;

		UWWUIPopulatorCompetitionFixtures* pFixturesPopulator = GetFixturesPopulator(FixtureType);

		if (pFixturesPopulator)
		{
			int32 GroupID = FixtureType == (ECompetitionInfoFixtureType::ALL_GAMES) ? STANDINGS_FULL_TABLE_INDEX : STANDINGS_SHOW_GROUP_INDEX;

			pFixturesPopulator->SetGroupID(GroupID);
		}

		UWWUIScrollBox* pFixturesScrollBox = GetFixturesScrollBox(FixtureType);

		if (pFixturesScrollBox)
		{
			pFixturesScrollBox->PopulateAndRefresh();
		}
	}

	switch (CurrentTab)
	{
	default:
		break;
	case ECompetitionInfoTab::CIT_STANDINGS:
	{
		CareerStandingsOnWindowEnter();
	}
		break;
	case ECompetitionInfoTab::CIT_SCHEDULE:
	{
		CareerScheduleOnWindowEnter();
	}
		break;
	case ECompetitionInfoTab::CIT_FIXTURES:
	{
		CareerFixturesOnWindowEnter();
	}
		break;
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::OnBack(APlayerController* OwningPlayer)
{
	UWorld* world = GetWorld();
	if (world)
	{
		URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(world->GetGameInstance());
		if (gameInstance)
		{
			gameInstance->DealMenuAction(SCREEN_BACK, Screens_UI::CareerCompetitionInfo);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::OnRightTrigger(APlayerController* OwningPlayer)
{
	switch (CurrentTab)
	{
	default:
		break;
	case ECompetitionInfoTab::CIT_STANDINGS:
	{
		CareerStandingsUpdatePool(false);
	}
	break;
	case ECompetitionInfoTab::CIT_SCHEDULE:
	{
		CareerScheduleOnChangeMonth(+1);
	}
	break;
	case ECompetitionInfoTab::CIT_FIXTURES:
	{
		CareerFixturesSwitchCategory();
	}
		break;
	}

	m_triggerAxisHandle = UWWUIFunctionLibrary::OnTimer(TRIGGER_DELAY_TIME, FTimerDelegate::CreateUObject(this, &UWWUIScreenCareerCompetitionInfo::ResetAxisTrigger), false);
	m_canTriggerAxis = false;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::OnLeftTrigger(APlayerController* OwningPlayer)
{
	switch (CurrentTab)
	{
	default:
		break;
	case ECompetitionInfoTab::CIT_STANDINGS:
	{
		CareerStandingsUpdatePool(true);
	}
	break;
	case ECompetitionInfoTab::CIT_SCHEDULE:
	{
		CareerScheduleOnChangeMonth(-1);
	}
	break;
	case ECompetitionInfoTab::CIT_FIXTURES:
	{
		CareerFixturesSwitchCategory();
	}
		break;
	}

	//	Mattt H - This is only needed for switch so may aswell wrap it in a #if PLATFORM_SWITCH check.
#if PLATFORM_SWITCH
	m_triggerAxisHandle = UWWUIFunctionLibrary::OnTimer(TRIGGER_DELAY_TIME, FTimerDelegate::CreateUObject(this, &UWWUIScreenCareerCompetitionInfo::ResetAxisTrigger), false);
	m_canTriggerAxis = false;
#endif
}

void UWWUIScreenCareerCompetitionInfo::OnRotationInput(float AxisValue, APlayerController* OwningPlayer)
{
	if (m_canTriggerAxis)
	{
		if (AxisValue < 0.0f)
		{
			OnLeftTrigger(OwningPlayer);
		}
		else if (AxisValue > 0.0f)
		{
			OnRightTrigger(OwningPlayer);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::OnFaceLeft(APlayerController* OwningPlayer)
{
	if (CurrentTab == ECompetitionInfoTab::CIT_SCHEDULE)
	{
		CareerScheduleOnGotoCurrentDate();
	}
	else if (CurrentTab == ECompetitionInfoTab::CIT_FIXTURES)
	{
		if(!NoCurrentDate)	CareerFixturesGoToCurrentDate();
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::OnFaceTop(APlayerController* OwningPlayer)
{
	if (CurrentScreenState != EScreenState::STATE_SIMULATING)
	{
		if (CurrentTab == ECompetitionInfoTab::CIT_SCHEDULE)
		{
			CareerScheduleSimulateToMatch();
		}
		else if (CurrentTab == ECompetitionInfoTab::CIT_FIXTURES)
		{
			CareerFixturesSimulateToMatch();
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::IncrementTab(APlayerController* OwningPlayer)
{
	UWWUITabSwitcher* pTabSwitcher = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::TabContainer));

	if (pTabSwitcher)
	{
		pTabSwitcher->IncrementTab();

		OnNewTab((ECompetitionInfoTab)pTabSwitcher->GetActiveTabID());
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::DecrementTab(APlayerController* OwningPlayer)
{
	UWWUITabSwitcher* pTabSwitcher = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::TabContainer));

	if (pTabSwitcher)
	{
		pTabSwitcher->DecrementTab();

		OnNewTab((ECompetitionInfoTab)pTabSwitcher->GetActiveTabID());
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::OnNewTab(ECompetitionInfoTab NewTab)
{
	CurrentTab = NewTab;

	OnWindowEnter();

	UpdateLegend();
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::UpdateLegend()
{
	UWWUIModularLegendText* pModularLegend = Cast<UWWUIModularLegendText>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::ModularLegendText));

	if (!pModularLegend)
	{
		ensureMsgf(pModularLegend, TEXT("Could not find a valid legend!"));
		return;
	}

	switch (CurrentTab)
	{
	case ECompetitionInfoTab::CIT_STANDINGS:
	{
		pModularLegend->ShowLegend((int)ECompetitionInfoLegend::CIL_VIEW_SQUAD);
		pModularLegend->HideLegend((int)ECompetitionInfoLegend::CIL_GO_TO_CURRENT_DATE);
		pModularLegend->HideLegend((int)ECompetitionInfoLegend::CIL_SIMULATE_TO_MATCH);
	}
	break;

	case ECompetitionInfoTab::CIT_SCHEDULE:
	{
		pModularLegend->HideLegend((int)ECompetitionInfoLegend::CIL_VIEW_SQUAD);
	}
	break;
	case ECompetitionInfoTab::CIT_FIXTURES:
	{
		pModularLegend->HideLegend((int)ECompetitionInfoLegend::CIL_VIEW_SQUAD);
	}
	break;
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::UpdateFocus()
{
	UWidget* pFocusScrollBox = GetTabDefaultScrollbox(CurrentTab);

	if (pFocusScrollBox)
	{
		SetInitialFocus(pFocusScrollBox);
	}
}

//===============================================================================
//===============================================================================

UWWUIScrollBox* UWWUIScreenCareerCompetitionInfo::GetTabDefaultScrollbox(ECompetitionInfoTab Tab)
{
	FString ScrollBoxToFind = "";

	switch (Tab)
	{
	case ECompetitionInfoTab::CIT_STANDINGS:
	{
		ScrollBoxToFind = WWUIScreenCareerCompetitionInfo_UI::ScrollBoxStandings;
	}
	break;
	case ECompetitionInfoTab::CIT_SCHEDULE:
	{
		ScrollBoxToFind = WWUIScreenCareerCompetitionInfo_UI::ScrollBoxSchedule;
	}
	break;
	case ECompetitionInfoTab::CIT_FIXTURES:
	{
		if (CurrentFixtureType == ECompetitionInfoFixtureType::PLAYER_GAMES)
		{
			ScrollBoxToFind = WWUIScreenCareerCompetitionInfo_UI::ScrollBoxPlayerFixtures;
		}
		else
		{
			ScrollBoxToFind = WWUIScreenCareerCompetitionInfo_UI::ScrollBoxAllFixtures;
		}
	}
	break;
	}

	return Cast<UWWUIScrollBox>(FindChildWidget(ScrollBoxToFind));
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::SetInitialFocus(UWidget* pScrollBoxWidget)
{
	if (pScrollBoxWidget)
	{
		//Find the first element of the main menu and set focus
		UWWUIScrollBox* pCurrentScrollBox = Cast<UWWUIScrollBox>(pScrollBoxWidget);

		if (pCurrentScrollBox)
		{
			pCurrentScrollBox->FocusFirstListField(SIFApplication::GetApplication()->GetMasterPlayerController());
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerStandingsOnWindowEnter()
{
	SetSlateLayoutCaching(false);

	// Set default standings to display all
	RUActiveCompetitionBase* pActiveCompetition = pCareerModeManager->GetPlayersActiveCompetition();

	if (pActiveCompetition)
	{
		int PreliminaryFormat = pActiveCompetition->GetPreliminaryFormat();
		if (PreliminaryFormat != PRELIMINARIES_POOL) // 2 is PRELIMINARIES_POOL,
		{
			CareerStandingsConferenceIndex = STANDINGS_FULL_TABLE_INDEX;	// 1 is PRELIMINARIES_LEAGUE, 0 is PRELIMINARIES_NONE, 3 is PRELIMINARIES_SERIES
		}
	}

	// CareerStandings.SetConferenceIndex(CareerStandings.conference_index)

	// Set the current pool	for the player team
	CareerStandingsSetConferenceFromPlayerTeam();

	UpdateFocus();
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerStandingsViewSquadOptionOnClick(int32 NewIdx)
{
	// Set default standings to display all
	RUActiveCompetitionBase* pActiveCompetition = pCareerModeManager->GetActiveCompetition();

	if (pActiveCompetition)
	{
		uint32 CompetitionInstanceID = pActiveCompetition->GetInstanceId();
		uint32 TeamDatabaseID = 0;

		UWWUIScrollBox* pStandingsScrollBox = GetTabDefaultScrollbox(ECompetitionInfoTab::CIT_STANDINGS);

		if (pStandingsScrollBox)
		{
			UWWUIListField* pSelectedField = pStandingsScrollBox->GetListField(NewIdx);

			if (pSelectedField)
			{
				TeamDatabaseID = pSelectedField->GetIntProperty(STANDINGS_PROPERTY_DATABASE_ID);
			}

			if (TeamDatabaseID == 0 || CompetitionInstanceID == 0)
			{
				return;
			}

			UE_LOG(LogTemp, Warning, TEXT("CareerHUB.MySquadOptionOnClick"));

			SIFGameHelpers::GARequestFaceTeamRender(1, TeamDatabaseID, true, false);

			UWWUIStateScreenCareerTeamSquadData* inData = NewObject<UWWUIStateScreenCareerTeamSquadData>();

			inData->CompetitionID = CompetitionInstanceID;
			inData->TeamID = TeamDatabaseID;
			inData->AtlasID = 1; // For the faces
			inData->bReadOnly = true; //!SIFGameHelpers::GAIsPlayerTeamId(team_id); Old game just set this to false, so I have left it this way, but we could check properly like that <-

			SIFGameHelpers::GARequestFaceTeamRender(1, TeamDatabaseID, true, false);
			// We don't clear our list anyway so no need to do this in RC4.
			//CareerStandings.dont_clear = true

			SetSlateLayoutCaching(false);

			pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CareerTeamSquad, inData);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerStandingsUpdatePool(bool Left)
{
	int32 GroupID = CareerStandingsConferenceIndex;

	RUActiveCompetitionBase* pActiveCompetition = pCareerModeManager->GetPlayersActiveCompetition();

	if (pActiveCompetition)
	{
		int32 NumberOfPools = pActiveCompetition->GetNumberOfPools();
		int32 NumberSatelliteTournaments = pActiveCompetition->GetNumberOfSatelliteTournaments();
		int32 PreliminaryFormat = pActiveCompetition->GetPreliminaryFormat();
		int32 MinGroup = -1;

		// local left_string = "false"
		// if left then left_string = "true"; end
		// Message("UpdatePool("..left_string.."), group_id: "..group_id..", num_groups: "..num_groups..", format: "..preliminary_format)

		if (pActiveCompetition->GetDefinitionId() == 1002)
		{
			MinGroup = STANDINGS_FULL_TABLE_INDEX - 1;
		}

		if (PreliminaryFormat == PRELIMINARIES_POOL ||
			PreliminaryFormat == PRELIMINARIES_SATELLITE ||
			PreliminaryFormat == PRELIMINARIES_LEAGUE) // 2 is PRELIMINARIES_POOL, 1 is PRELIMINARIES_LEAGUE, 5 is SEVENS SERIES
		{
			if (Left)
			{
				GroupID = GroupID - 1;

				if (GroupID == MinGroup)
				{
					GroupID = NumberOfPools - 1;
				}
			}
			else
			{
				GroupID = GroupID + 1;
				if (GroupID == NumberOfPools)
				{
					GroupID = MinGroup + 1;
				}
			}
		}
		else	// 0 is PRELIMINARIES_NONE, 3 is PRELIMINARIES_SERIES
		{
			return;
		}

		CareerStandingsSetConferenceIndex(GroupID);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerStandingsSetConferenceIndex(int32 NewIndex)
{
	int32 InitialIndex = CareerStandingsConferenceIndex;

	CareerStandingsConferenceIndex = NewIndex;

	RUActiveCompetitionBase* pActiveCompetition = pCareerModeManager->GetPlayersActiveCompetition();

	if (pActiveCompetition)
	{
		// special case for Super Rugby(id 1002)
		int32 PreliminaryFormat = pActiveCompetition->GetPreliminaryFormat();

		if ((PreliminaryFormat == PRELIMINARIES_POOL || 
			PreliminaryFormat == PRELIMINARIES_LEAGUE) && pActiveCompetition->GetDefinitionId() != 1002)
		{
			// We want to make sure the conference index wraps, so if we go over the number we wrap around to - 1 and visa versa.
			// We use - 1 to display all the teams.This way the player can see all the pools individually AND see the overall position.
			int32 NumberOfPools = pActiveCompetition->GetNumberOfPools();

			if (CareerStandingsConferenceIndex < 0)
			{
				CareerStandingsConferenceIndex = NumberOfPools - 1;
			}
			else if (CareerStandingsConferenceIndex > NumberOfPools - 1)
			{
				CareerStandingsConferenceIndex = 0;
			}
		}
	}

	// With the number all sorted, we just need to set it in.The format must be a string.
	
	UWWUIPopulatorCareerStandings* pStandingsPopulator = GetStandingsPopulator();

	if (pStandingsPopulator)
	{
		pStandingsPopulator->SetGroupID(CareerStandingsConferenceIndex);

		UWWUIScrollBox* pStandingsScrollBox = GetTabDefaultScrollbox(ECompetitionInfoTab::CIT_STANDINGS);

		if (pStandingsScrollBox)
		{
			pStandingsScrollBox->PopulateAndRefresh();
		}

		if (InitialIndex != CareerStandingsConferenceIndex)
		{
			//pre-emptivily unfocus from a ListField to force an animation to play to fix text colours (RC4-4239)
			SetFocusToWidget(pStandingsScrollBox, SIFApplication::GetApplication()->GetMasterPlayerController());

			SetInitialFocus(pStandingsScrollBox);
		}
	}

	CareerStandingsUpdateConferenceTitle();
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerStandingsUpdateConferenceTitle()
{
	FString TitleText = "";
	FString NumberText = "";
	bool bFiltersVisible = true;

	RUActiveCompetitionBase* pActiveCompetition = pCareerModeManager->GetPlayersActiveCompetition();

	if (pActiveCompetition)
	{
		int32 NumGroups = pActiveCompetition->GetNumberOfPools();

		int32 PreliminaryFormat = pActiveCompetition->GetPreliminaryFormat();

		if (CareerStandingsConferenceIndex == STANDINGS_FULL_TABLE_INDEX && PreliminaryFormat != PRELIMINARIES_SATELLITE)
		{
			// -1 means that we don't want to see the individual pools, but the full table including each team.
			TitleText = "[ID_COMP_STANDINGS_FULL_TABLE]"; 
			CareerStandingsSetShowOverallSevensHeading(false);

			// Use default heading (pools / groups)

			if (NumGroups <= 1)
			{
				bFiltersVisible = false;
			}
		}
		else
		{
			if (NumGroups <= 1)
			{
				TitleText = "[ID_COMP_STANDINGS_FULL_TABLE]";
				CareerStandingsSetShowOverallSevensHeading(false);
				bFiltersVisible = false;
			}
			else if (CareerStandingsConferenceIndex == STANDINGS_FULL_TABLE_INDEX && PreliminaryFormat == PRELIMINARIES_SATELLITE)
			{
				// This is sevens and we are showing the full table, so switch up the header for the columns as well.
				TitleText = "[ID_SEVENS_SERIES_OVERALL_STANDINGS]";

				CareerStandingsSetShowOverallSevensHeading(true);
			}
			else
			{

				TitleText = SIFGameHelpers::GAConvertMabStringToFString(pActiveCompetition->GetPoolName(CareerStandingsConferenceIndex));

				// Show standings heading
				CareerStandingsSetShowOverallSevensHeading(false);
			}
		}
	}

	UWidget* pStandingsCategorySelectorWidget = FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::StandingsCategorySelector);

	if (pStandingsCategorySelectorWidget)
	{
		if (!bFiltersVisible)
		{
			UWidget* pPopulatorHeaderList = FindChildOfTemplateWidget(pStandingsCategorySelectorWidget, WWUIScreenCareerCompetitionInfo_UI::PopulatorHeaderList);

			if (pPopulatorHeaderList)
			{
				UWidget* pLeftNavLegend = FindChildOfTemplateWidget(pPopulatorHeaderList, WWUIScreenCareerCompetitionInfo_UI::FilterLeftLegend);
				UWidget* pRightNavLegend = FindChildOfTemplateWidget(pPopulatorHeaderList, WWUIScreenCareerCompetitionInfo_UI::FilterRightLegend);
				UWidget* pRightArrow = FindChildOfTemplateWidget(pPopulatorHeaderList, WWUIScreenCareerCompetitionInfo_UI::ImageRightArrow);
				UWidget* pLeftArrow = FindChildOfTemplateWidget(pPopulatorHeaderList, WWUIScreenCareerCompetitionInfo_UI::ImageLeftArrow);

				if (pLeftNavLegend && pLeftArrow && pRightArrow && pRightNavLegend)
				{
					pRightNavLegend->SetVisibility(ESlateVisibility::Collapsed);
					pLeftArrow->SetVisibility(ESlateVisibility::Collapsed);
					pLeftNavLegend->SetVisibility(ESlateVisibility::Collapsed);
					pRightArrow->SetVisibility(ESlateVisibility::Collapsed);
				}
			}
		}

		UTextBlock* pTitleText = Cast<UTextBlock>(FindChildOfTemplateWidget(pStandingsCategorySelectorWidget, WWUIScreenCareerCompetitionInfo_UI::TextCategoryName));

		if (pTitleText)
		{
			SetWidgetText(pTitleText, FText::FromString(UWWUITranslationManager::Translate(TitleText)));
		}

		UTextBlock* pNumCategoriesText = Cast<UTextBlock>(FindChildOfTemplateWidget(pStandingsCategorySelectorWidget, WWUIScreenCareerCompetitionInfo_UI::TextNumCategories));

		if (pNumCategoriesText)
		{
			pNumCategoriesText->SetVisibility(ESlateVisibility::Collapsed);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerStandingsSetConferenceFromPlayerTeam()
{
	RUActiveCompetitionBase* pActiveCompetition = pCareerModeManager->GetPlayersActiveCompetition();

	if (pActiveCompetition)
	{
		uint32 PlayerTeamID = pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch();

		// RC4 Reset the active competition to match the players, previous line resets it
		pActiveCompetition = pCareerModeManager->GetPlayersActiveCompetition();

		uint32 PlayerTeamPoolIndex = pActiveCompetition->GetTeamPoolIndex(PlayerTeamID);

		CareerStandingsSetConferenceIndex(PlayerTeamPoolIndex);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerStandingsScrollingListboxChanged(int Direction)
{
	// The standings list only really cares if the user moves left or right.
	// Because that's when the conference needs to update.

	// We can only change the conference the preliminary format is pools.
	RUActiveCompetitionBase* pActiveCompetition = pCareerModeManager->GetActiveCompetition();

	if (pActiveCompetition)
	{
		int32 PreliminaryFormat = pActiveCompetition->GetPreliminaryFormat();
		if (PreliminaryFormat != PRELIMINARIES_POOL) // 2 is PRELIMINARIES_POOL
		{
			return;
		}

		// Moving left or right will have the effect of increasing or decreasing the current conference index.
		int32 NewConferenceIndex = CareerStandingsConferenceIndex + Direction;

		CareerStandingsSetConferenceIndex(NewConferenceIndex);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerStandingsSetShowOverallSevensHeading(bool bShowHeading)
{
	UWidgetSwitcher* pHeadingWidgetSwitcher = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::WidgetSwitcherStandingsHeader));

	if (pHeadingWidgetSwitcher)
	{
		if (bShowHeading) // Setup the heading with team mnemonics.
		{
			pHeadingWidgetSwitcher->SetActiveWidgetIndex(STANDINGS_OVERALL_INDEX);

			UHorizontalBox* pActiveHeadingHorizontalBox = Cast<UHorizontalBox>(pHeadingWidgetSwitcher->GetActiveWidget());

			if (pActiveHeadingHorizontalBox)
			{
				TArray<UTextBlock*> pMnemonicTextBlocks;

				// Take one off for total points
				for (int32 i = STANDINGS_HEADER_MNEMONIC_START; i < pActiveHeadingHorizontalBox->GetChildrenCount() - 1; i++)
				{
					UTextBlock* pMnenomicTextBlock = Cast<UTextBlock>(pActiveHeadingHorizontalBox->GetChildAt(i));

					if (pMnenomicTextBlock)
					{
						pMnemonicTextBlocks.Add(pMnenomicTextBlock);
					}
				}

				SIFGameHelpers::GASetSeriesStandingsHeadings(pMnemonicTextBlocks);
			}
		}
		else // Show the usual heading
		{
			pHeadingWidgetSwitcher->SetActiveWidgetIndex(STANDINGS_POOL_INDEX);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerScheduleOnWindowEnter()
{
	SetSlateLayoutCaching(false);

	UWWUIFunctionLibrary::StopTimer(CareerScheduleBumpCarousellTimer);

	CareerScheduleUpdateHeading();

	UWWUIScrollBox* pScheduleScrollBox = GetTabDefaultScrollbox(ECompetitionInfoTab::CIT_SCHEDULE);

	if (pScheduleScrollBox)
	{
		pScheduleScrollBox->PopulateAndRefresh();
	}

	CareerScheduleOnGotoCurrentDate(true);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerScheduleOnChangeMonth(int32 Direction)
{
	MabDate current_date;

	UWWUIScrollBox* pScheduleScrollBox = GetTabDefaultScrollbox(ECompetitionInfoTab::CIT_SCHEDULE);

	if (pScheduleScrollBox)
	{
		UWWUIListField* pSelectedListField = pScheduleScrollBox->GetListField(pScheduleScrollBox->GetSelectedIndex());

		if (pSelectedListField)
		{
			RL3Database* const pDatabase = pCareerModeManager->GetRL3Database();
			if (pDatabase == NULL) return;

			int32 SelectedMatchDatabaseID = pSelectedListField->GetIntProperty(UWWUIPopulatorCareerSchedule::SELECTED_MATCH_DBID_PROPERTY_NAME);
			RL3DB_MATCH SelectedMatch(pDatabase->GetMatch(SelectedMatchDatabaseID));

			current_date = SelectedMatch.GetDate();
		}
		for (int i = pScheduleScrollBox->GetSelectedIndex(); (i < pScheduleScrollBox->GetListLength() && i > 0); i = i + Direction)
		{
			UWWUIListField* pListField = pScheduleScrollBox->GetListField(i);

			if (pListField)
			{
				RL3Database* const pDatabase = pCareerModeManager->GetRL3Database();
				if (pDatabase == NULL) return;

				int32 SelectedMatchDatabaseID = pListField->GetIntProperty(UWWUIPopulatorCareerSchedule::SELECTED_MATCH_DBID_PROPERTY_NAME);

				if (SelectedMatchDatabaseID != MAX_int32)
				{
					RL3DB_MATCH SelectedMatch(pDatabase->GetMatch(SelectedMatchDatabaseID));

					MabDate match_date = SelectedMatch.GetDate();

					if (match_date.GetMonth() != current_date.GetMonth())
					{
						pScheduleScrollBox->SetSelectedIndex(i);
						return;
					}
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerScheduleOnGotoCurrentDate(bool bForceSelection /*=false*/)
{
	UWWUIScrollBox* pScheduleScrollBox = GetTabDefaultScrollbox(ECompetitionInfoTab::CIT_SCHEDULE);

	if (pScheduleScrollBox)
	{
		for (int i = 0; i < pScheduleScrollBox->GetListLength(); i++)
		{
			UWWUIListField* pListField = pScheduleScrollBox->GetListField(i);

			if (pListField)
			{
				bool bIsNextPlayerMatch = pListField->GetBoolProperty(UWWUIPopulatorCareerSchedule::PLAYER_TEAM_NEXT_GAME_PROPERTY_NAME);

				if (bIsNextPlayerMatch)
				{
					if (!bForceSelection && pScheduleScrollBox->GetSelectedIndex() == i)
					{
						UpdateScheduleLegend(i);
						return;
					}

					pScheduleScrollBox->SetSelectedIndex(i);
					UpdateScheduleLegend(i);
					return;
				}
			}
		}

		pScheduleScrollBox->SetSelectedIndex(0);
		UpdateScheduleLegend(0);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerScheduleSimulateToMatch()
{
	if (CareerScheduleCanSimulateToSelection())
	{
		if (CareerScheduleSetSimulationDateToMatchSelectedInCompetitionScheduleScreen())
		{
			if (SIFGameHelpers::GAGetIsAProMode() && !pCareerModeManager->GetPostCompetitionMode())
			{
				LaunchConfirmSimulationPopup();
			}
			else
			{
				OnConfirmSimulation(nullptr);
			}

			CurrentScreenState = EScreenState::STATE_SIMULATING;
		}
	}
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerCompetitionInfo::CareerScheduleCanSimulateToSelection()
{
	if (pCareerModeManager->IsLionsTour())
	{
		return false;
	}

	UWWUIScrollBox* pCurrentScrollBox = GetTabDefaultScrollbox(CurrentTab);

	if (pCurrentScrollBox)
	{
		UWWUIListField* pSelectedListField = pCurrentScrollBox->GetListField(ScheduleAndFixturesSelectedIndex);

		if (pSelectedListField && pCareerModeManager)
		{
			RL3Database* const pDatabase = pCareerModeManager->GetRL3Database();
			if (pDatabase == NULL)
			{
				return false;
			}

			int32 SelectedMatchDatabaseID = pSelectedListField->GetIntProperty(UWWUIPopulatorCareerSchedule::SELECTED_MATCH_DBID_PROPERTY_NAME);
			RL3DB_MATCH SelectedMatch(pDatabase->GetMatch(SelectedMatchDatabaseID));

			MabDate SelectedMatchDate = SelectedMatch.GetDate();
			MabDate CurrentDate = *pCareerModeManager->GetCurrentDate();

			if (SelectedMatchDate > CurrentDate)
			{
				return true;
			}
		}
	}

	return false;
}

void UWWUIScreenCareerCompetitionInfo::UpdateScheduleLegend(int32 SelectedIndex)
{
	UWWUIScrollBox* pScheduleScrollBox = GetTabDefaultScrollbox(ECompetitionInfoTab::CIT_SCHEDULE);

	if (pScheduleScrollBox)
	{
		UWWUIListField* pSelectedListField = pScheduleScrollBox->GetListField(SelectedIndex);

		if (pSelectedListField)
		{
			UWWUIModularLegendText* pModularLegend = Cast<UWWUIModularLegendText>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::ModularLegendText));

			if (pModularLegend)
			{
				bool bIsNextPlayerMatch = pSelectedListField->GetBoolProperty(UWWUIPopulatorCareerSchedule::PLAYER_TEAM_NEXT_GAME_PROPERTY_NAME);

				if (!bIsNextPlayerMatch)
				{
					pModularLegend->ShowLegend((int)ECompetitionInfoLegend::CIL_GO_TO_CURRENT_DATE);
					pModularLegend->ShowLegend((int)ECompetitionInfoLegend::CIL_SIMULATE_TO_MATCH);
				}
				else
				{
					pModularLegend->HideLegend((int)ECompetitionInfoLegend::CIL_GO_TO_CURRENT_DATE);
					pModularLegend->HideLegend((int)ECompetitionInfoLegend::CIL_SIMULATE_TO_MATCH);
				}

				if (!CareerScheduleCanSimulateToSelection())
				{
					pModularLegend->HideLegend((int)ECompetitionInfoLegend::CIL_SIMULATE_TO_MATCH);
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerScheduleUpdateHeading()
{
	UWidget* pHeaderSubtitleWidget = FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::Subtitle);

	if (pHeaderSubtitleWidget)
	{
		WWUICareerGlobal::SetBreadCrumbFromTeamID(pHeaderSubtitleWidget, pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch(), false);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerSchedulePopulateSelectedMatch(int32 NewIndex)
{
	UWWUIScrollBox* pScheduleScrollBox = GetTabDefaultScrollbox(ECompetitionInfoTab::CIT_SCHEDULE);

	if (pScheduleScrollBox)
	{
		UWWUIListField* pSelectedListField = pScheduleScrollBox->GetListField(NewIndex);

		if (pSelectedListField)
		{
			RL3Database* const pDatabase = pCareerModeManager->GetRL3Database();
			if (pDatabase == NULL)
			{
				ensure(pDatabase != NULL);
				return;
			}

			int32 SelectedMatchDatabaseID = pSelectedListField->GetIntProperty(UWWUIPopulatorCareerSchedule::SELECTED_MATCH_DBID_PROPERTY_NAME);
			RL3DB_MATCH SelectedMatch(pDatabase->GetMatch(SelectedMatchDatabaseID));

			int32 SelectedCompIntstanceDatabaseID = pSelectedListField->GetIntProperty(UWWUIPopulatorCareerSchedule::SELECTED_COMPETITION_INSTANCE_DBID_PROPERTY_NAME);
			RL3DB_COMPETITION_INSTANCE SelectedCompInstance = pDatabase->GetCompetitionInstance(SelectedCompIntstanceDatabaseID);

			int32 PlayerTeamDatabaseID = pSelectedListField->GetIntProperty(UWWUIPopulatorCareerSchedule::PLAYER_TEAM_DBID_PROPERTY_NAME);

			CareerScheduleUpdateNextMatchInfo(SelectedMatch, SelectedCompInstance, pDatabase, PlayerTeamDatabaseID);

			UWidget* pPlayerTeamPanel = FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::PlayerTeamPanel);

			if (pPlayerTeamPanel)
			{
				CareerScheduleUpdateTeamPanel(pPlayerTeamPanel, SelectedMatch, SelectedCompInstance, PlayerTeamDatabaseID, pDatabase);
			}

			UWidget* pOpponentTeamPanel = FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::OpponentTeamPanel);

			if (pOpponentTeamPanel)
			{
				int32 OpponentTeamDatabaseID = pSelectedListField->GetIntProperty(UWWUIPopulatorCareerSchedule::OPPONENT_TEAM_DBID_PROPERTY_NAME);
				CareerScheduleUpdateTeamPanel(pOpponentTeamPanel, SelectedMatch, SelectedCompInstance, OpponentTeamDatabaseID, pDatabase);
			}
		}
	}

	UpdateScheduleLegend(NewIndex);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerScheduleUpdateNextMatchInfo(RL3DB_MATCH SelectedMatch, RL3DB_COMPETITION_INSTANCE SelectedCompetition, RL3Database* pDatabase, int32 PlayerTeamID)
{
	// Set the competition information string.

	UTextBlock* pCompNameText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::TextCompName));

	MabString CompName = SelectedCompetition.GetName();
	RL3DB_COMPETITION_DEFINITION CompetitionDefinition(SelectedCompetition.GetCompetitionId());

	if (pCompNameText)
	{
		RUHUDUpdater::CensorCompetitionName(&CompetitionDefinition, CompName);
		SetWidgetText(pCompNameText, FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(CompName)).ToUpper());
	}

	// Set the round number.
	UTextBlock* pRoundText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::TextGameType));

	if (pRoundText)
	{
		int RoundIndex = SelectedCompetition.GetRoundIndexFromMatch(SelectedMatch);
		const char *ShortName = "";
		if (CompetitionDefinition.GetDbId() != DB_COMPID_QUADNATIONS)// && CompetitionDefinition.GetDbId() != DB_COMPID_BLEDISLOE)
			ShortName = CompetitionDefinition.GetRoundShortName(RoundIndex);
		MabString RoundString;

		if (ShortName && strlen(ShortName) > 0)
		{
			RoundString = ShortName;

			if (CompetitionDefinition.GetPreliminaryFormat() == PRELIMINARIES_SATELLITE)
			{
				MabString Result;
				// Nick WWS &s to Womens 13s //
				//if (CompetitionDefinition.GetIsR7Exclusive())
				//{
				//	Result = MabString(0, "SAT=%s", "");
				//}
				//else
				//{
					Result = MabString(0, "SAT=%s", pCareerModeManager->GetSatelliteName(SelectedCompetition.GetCompetitionId(), CompetitionDefinition.GetSatelliteNumberFromRound(RoundIndex)));
				//}

				MabString ResultTranslated = "";
				FString TranslatedRoundString = UWWUITranslationManager::Translate(SIFGameHelpers::GAConvertMabStringToFString(RoundString));

				// Replace string (ignore the translate).
				ResultTranslated = pCareerModeManager->TranslateAndReplace(TCHAR_TO_UTF8(*TranslatedRoundString), Result.c_str());

				RoundString = ResultTranslated;
			}
		}
		else
		{
			RoundString = MabString(0, "[ID_COMPETITION_ROUND] %d", RoundIndex + 1);
		}

		SetWidgetText(pRoundText, FText::FromString(UWWUITranslationManager::Translate(SIFGameHelpers::GAConvertMabStringToFString(RoundString))).ToUpper());
	}


	RL3DB_RESULT this_result = SelectedMatch.GetResult();
	const bool has_result = this_result.GetHasResult();

	UWidgetSwitcher* pPlayedUnplayedWidgetSwitcher = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::WidgetSwitcherPlayedUnplayed));

	if (pPlayedUnplayedWidgetSwitcher)
	{
		ECompetitionInfoSchedulePlayedUnplayed NewState = has_result ? ECompetitionInfoSchedulePlayedUnplayed::CILSPU_PLAYED : ECompetitionInfoSchedulePlayedUnplayed::CILSPU_UNPLAYED;
		pPlayedUnplayedWidgetSwitcher->SetActiveWidgetIndex((int)NewState);
	}

	//PlayerPerformance
	//MabUITextInterface* const player_performance_node = CheckAndReturnNamedChildNode< MabUITextInterface >(MATCH_INFO_PLAYER_PERFORMANCE_NODE_NAME, match_info_container_node);
	//player_performance_node->SetVisible(false);

	CareerScheduleSelectedMatchDate = SelectedMatch.GetDate();

	UTextBlock* pMonthText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::TextMonth));

	if (pMonthText)
	{
		FString MonthString = UWWUITranslationManager::Translate(SIFGameHelpers::GAConvertMabStringToFString(CareerScheduleSelectedMatchDate.GetMonthString(CareerScheduleSelectedMatchDate.GetMonth())));
		FString YearString = FString::FromInt(CareerScheduleSelectedMatchDate.GetYear());
		SetWidgetText(pMonthText, FText::FromString(MonthString + " " + YearString));
	}

	UWWUIFunctionLibrary::StopTimer(CareerScheduleBumpCarousellTimer);

	if (has_result)
	{
		// Figure out if the player won or lost, then display.

		RL3DB_TEAM player_team = pDatabase->GetTeam(PlayerTeamID);
		const unsigned short selected_match_home_team_index = SelectedMatch.GetHomeTeam();

		RL3DB_TEAM home_team = SelectedCompetition.GetDbTeam(selected_match_home_team_index);
		const bool player_team_at_home = (home_team.GetDbId() == player_team.GetDbId());
		const bool home_team_won(this_result.side_a_score > this_result.side_b_score);
		const bool away_team_won(this_result.side_a_score < this_result.side_b_score);
		const short player_score = (player_team_at_home) ? this_result.side_a_score : this_result.side_b_score;
		const short opponent_score = (player_team_at_home) ? this_result.side_b_score : this_result.side_a_score;

		UTextBlock* pWinLossText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::TextWinLose));
		UTextBlock* pPlayerScoreText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::TextPlayerScore));
		UTextBlock* pOpponentScoreText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::TextOpponentScore));

		if (pWinLossText && pPlayerScoreText && pOpponentScoreText)
		{
			SetWidgetText(pPlayerScoreText, FText::FromString(FString::FromInt(player_score)));
			SetWidgetText(pOpponentScoreText, FText::FromString(FString::FromInt(opponent_score)));

			if ((player_team_at_home && home_team_won) || (!player_team_at_home && away_team_won))
			{
				// Player won.
				SetWidgetText(pWinLossText, UWWUITranslationManager::Translate(FText::FromString("[ID_MATCH_WIN]"))); 
				pWinLossText->SetColorAndOpacity(FLINEARCOLOR_MULTIPLY(UWWUIPopulatorCareerSchedule::COLOUR_GREEN_BRIGHT));

				pPlayerScoreText->SetColorAndOpacity(FLINEARCOLOR_MULTIPLY(UWWUIPopulatorCareerSchedule::COLOUR_GREEN_BRIGHT));
			}
			else if ((player_team_at_home && away_team_won) || (!player_team_at_home && home_team_won))
			{
				// Opponent won.
				SetWidgetText(pWinLossText, UWWUITranslationManager::Translate(FText::FromString("[ID_MATCH_LOSS]")));
				pWinLossText->SetColorAndOpacity(FLINEARCOLOR_MULTIPLY(UWWUIPopulatorCareerSchedule::COLOUR_RED_BRIGHT));

				pPlayerScoreText->SetColorAndOpacity(FLINEARCOLOR_MULTIPLY(UWWUIPopulatorCareerSchedule::COLOUR_RED_BRIGHT));
			}
			// Must've been a draw.
			else
			{
				MABASSERTMSG(this_result.side_a_score == this_result.side_b_score, "This isn't a draw!");
				SetWidgetText(pWinLossText, UWWUITranslationManager::Translate(FText::FromString("[ID_DRAW]")));
				pWinLossText->SetColorAndOpacity(FLinearColor::White);
			}
		}

		// Return the carousell to the first index
		UWidgetSwitcher* pMoreInfoCarousellWidgetSwitcher = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::WidgetSwitcherMatchInfoCarousell));

		if (pMoreInfoCarousellWidgetSwitcher)
		{
			pMoreInfoCarousellWidgetSwitcher->SetActiveWidgetIndex(0);
		}

		CareerScheduleBumpCarousellTimer = UWWUIFunctionLibrary::OnTimer(2.0f, FTimerDelegate::CreateUObject(this, &UWWUIScreenCareerCompetitionInfo::CareerScheduleBumpMoreInfoCarousell), true);

	}
	else
	{
		// Set the date of the match.
		UTextBlock* pMatchTimeText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::TextDateTime));

		if (pMatchTimeText)
		{
			SetWidgetText(pMatchTimeText, FText::FromString(FormatMabDate(&CareerScheduleSelectedMatchDate)));
		}

		// Set the name of the stadium.
		UTextBlock* pStadiumText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::TextLocation));

		if (pStadiumText)
		{
			const char* stadium_name = pDatabase->GetStadium(SelectedMatch.GetStadium()).GetName();
			/*if (stadium_name == NULL)
			{
				competition_stadium_name_node->SetText(MabString("").c_str());
			}
			else
			{
				competition_stadium_name_node->SetText(MabString(0, "%s", stadium_name).c_str());
			}*/
			SetWidgetText(pStadiumText, FText::FromString(stadium_name).ToUpper());
		}
	}

	// Now hand off population to an immense inner method, to populate the carousel.
	CareerSchedulePopulateMoreInfoCarousell(SelectedMatch, SelectedCompetition, pDatabase);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerSchedulePopulateMoreInfoCarousell(RL3DB_MATCH SelectedMatch, RL3DB_COMPETITION_INSTANCE SelectedCompetition, RL3Database* pDatabase)
{
	UWidget* pMoreInfoCarousellWidget = FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::MatchInfoCarousell);
	
	if (!pMoreInfoCarousellWidget)
	{
		return;
	}

	RL3DB_RESULT this_result = SelectedMatch.GetResult();
	const bool has_result = this_result.GetHasResult();
	if (has_result)
	{
		// Populate information about how the match went.
		//
		CareerScheduleUpdateMoreInfo(SelectedMatch, SelectedCompetition, pDatabase);
	}
	else
	{
		CareerSchedulePopulateMatchHistoryString(SelectedMatch, SelectedCompetition, pDatabase);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerScheduleUpdateMoreInfo(RL3DB_MATCH SelectedMatch, RL3DB_COMPETITION_INSTANCE SelectedCompetition, RL3Database* pDatabase)
{
	// We need to find the match in the list of competition instance matches. Shared fields are home team, away team, and date.

	int32 target_home_team_index = SelectedMatch.GetHomeTeam();
	int32 target_away_team_index = SelectedMatch.GetAwayTeam();
	const MabDate target_match_date = SelectedMatch.GetDate();

	RL3TableCache* const comp_inst_match_cache = pDatabase->GetMatchCache();

	// The trouble is that since we're matching three fields, we can't use the basic SelectWhere, which only takes two arguments.
	//	So pile up vectors of keys and values, and iterate through the result looking for the matching date.
	//
	MabVector< int32 > target_field_keys(0);
	target_field_keys.push_back(CCDB_MATCH_HOME_TEAM_ID);
	target_field_keys.push_back(CCDB_MATCH_AWAY_TEAM_ID);

	MabVector< int32 > target_field_values(0);
	target_field_values.push_back(target_home_team_index);
	target_field_values.push_back(target_away_team_index);

	const int num_matching_matches = comp_inst_match_cache->SelectCountWhere(target_field_keys, target_field_values);

	int match_index = 0;
	unsigned short comp_inst_match_id = 0;
	for (; match_index < num_matching_matches; ++match_index)
	{
		comp_inst_match_id = comp_inst_match_cache->SelectWhereOffset(target_field_keys, target_field_values, match_index);
		const MabDate matching_match_date = comp_inst_match_cache->ReadDate(comp_inst_match_id, CCDB_MATCH_DATE);
		if (matching_match_date == target_match_date)
		{
			break;
		}
	}

	if (match_index >= num_matching_matches)
	{
		MABBREAK();
		return;
	}

	RL3TableCache* const player_match_stats_cache = pDatabase->GetPlayerMatchStatsCache();
	if (player_match_stats_cache == NULL)
	{
		MABBREAK();
		return;
	}

	// Get the teams...
	//
	const unsigned short target_team1_db_id = SelectedCompetition.GetDbTeam(SelectedMatch.GetHomeTeam()).GetDbId();
	const unsigned short target_team2_db_id = SelectedCompetition.GetDbTeam(SelectedMatch.GetAwayTeam()).GetDbId();

	// ...and now find out which team is player controlled.
	const bool team1_player_controlled = pCareerModeManager->GetIsTeamPlayerControlled(target_team1_db_id);
	const bool team2_player_controlled = pCareerModeManager->GetIsTeamPlayerControlled(target_team2_db_id);

	if (!team1_player_controlled && !team2_player_controlled)
	{
		MABBREAKMSG("There appear to be no player-controlled teams in the match you're targeting!");
	}

	const unsigned short player_team_match_stats_db_id = (team1_player_controlled) ? target_team1_db_id : target_team2_db_id;
	const unsigned short ai_team_match_stats_db_id = (team1_player_controlled) ? target_team2_db_id : target_team1_db_id;

	// Now match up the player_match_stats row by means of the match_id and the player and AI ids.
	//
	const unsigned short player_match_stats_id = player_match_stats_cache->SelectWhere(CCDB_COMPINSTPLAYERMATCHSTATS_COMP_INST_MATCH_ID, comp_inst_match_id, CCDB_COMPINSTPLAYERMATCHSTATS_TEAM_ID, player_team_match_stats_db_id, false);
	const unsigned short ai_match_stats_id = player_match_stats_cache->SelectWhere(CCDB_COMPINSTPLAYERMATCHSTATS_COMP_INST_MATCH_ID, comp_inst_match_id, CCDB_COMPINSTPLAYERMATCHSTATS_TEAM_ID, ai_team_match_stats_db_id, false);

	UWidget* pMatchInfoCarousell = FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::MatchInfoCarousell);

	//--------------------------------------------
	// CRASH FIX: Competition has finished and specific match stats have now been deleted - SHOULDN'T REALLY BE LISTING THIS COMPETITIONS MATCHES AT ALL!!!!
	// Just don't show stats for these matches....
	if (player_match_stats_id == 0 || ai_match_stats_id == 0)
	{
		if (pMatchInfoCarousell)
		{
			pMatchInfoCarousell->SetVisibility(ESlateVisibility::Collapsed);
		}
		return;
	}

	// Make sure the the carousell is visible by default.
	if (pMatchInfoCarousell)
	{
		pMatchInfoCarousell->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	}
	//--------------------------------------------

	//// Now sum a few critical fields.
	unsigned short all_stats_ids[] =
	{
		player_match_stats_id,
		ai_match_stats_id
	};

	/*const*/ static int FIELDS_TO_BE_SUMMED[] =
	{
		CCDB_COMPINSTPLAYERMATCHSTATS_POSSESSION,
		CCDB_COMPINSTPLAYERMATCHSTATS_TERRITORY
	};

	MabMap< int, float > field_to_summed_value_map(SIFHEAP_DYNAMIC);
	for (unsigned short* all_stats_id_iter = all_stats_ids; all_stats_id_iter < all_stats_ids + StaticArraySize(all_stats_ids); ++all_stats_id_iter)
	{
		for (int* summed_fields_iter = FIELDS_TO_BE_SUMMED; summed_fields_iter < FIELDS_TO_BE_SUMMED + StaticArraySize(FIELDS_TO_BE_SUMMED); ++summed_fields_iter)
		{
			MABLOGDEBUG("Values: %u, %d", *all_stats_id_iter, *summed_fields_iter);
			const float this_value = player_match_stats_cache->ReadFloat(*all_stats_id_iter, *summed_fields_iter);
			if (field_to_summed_value_map.find(*summed_fields_iter) == field_to_summed_value_map.end())
			{
				field_to_summed_value_map[*summed_fields_iter] = this_value;
			}
			else
			{
				field_to_summed_value_map[*summed_fields_iter] += this_value;
			}
		}
	}

	// Now get the nodes that we want to write to. Initially just get a pair of player/opponent arrays to step through...
	//

	const unsigned short TEAM_MATCH_DB_ID_LIST[] =
	{
		player_match_stats_id,
		ai_match_stats_id
	};

	CareerSchedulePopulateStats(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::VerticalBoxStats), (int)ECompetitionInfoScheduleStatValue::CISSV_POSSESIONS, player_match_stats_id, player_match_stats_cache, field_to_summed_value_map, "TextPlayerValue");
	CareerSchedulePopulateStats(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::VerticalBoxStats), (int)ECompetitionInfoScheduleStatValue::CISSV_POSSESIONS, ai_match_stats_id, player_match_stats_cache, field_to_summed_value_map, "TextOpponentValue");


	CareerSchedulePopulateStats(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::VerticalBoxTries), (int)ECompetitionInfoScheduleStatValue::CISSV_TRIES, player_match_stats_id, player_match_stats_cache, field_to_summed_value_map, "TextPlayerValue");
	CareerSchedulePopulateStats(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::VerticalBoxTries), (int)ECompetitionInfoScheduleStatValue::CISSV_TRIES, ai_match_stats_id, player_match_stats_cache, field_to_summed_value_map, "TextOpponentValue");

	

	// Now process leading try scorers.
	//
	// Get parent node.

	// And now the child nodes.
	UVerticalBox* pTryScorerVerticalBox = Cast<UVerticalBox>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::VerticalBoxTopTryScorers));

	if (pTryScorerVerticalBox)
	{
		size_t try_scorer_ctr = 1u;
		for (; try_scorer_ctr <= RUStatisticsSystem::NUM_TRY_SCORERS_RECORDED; ++try_scorer_ctr)
		{
			UTextBlock* pCurrentTryScorerText = Cast<UTextBlock>(pTryScorerVerticalBox->GetChildAt(try_scorer_ctr));

			if (pCurrentTryScorerText)
			{
				const char* const try_scorer_string = player_match_stats_cache->ReadString(player_match_stats_id, RUStatisticsSystem::TRY_SCORER_ENUM_LIST[try_scorer_ctr - 1u]);

				SetWidgetText(pCurrentTryScorerText, UWWUITranslationManager::Translate(FText::FromString(try_scorer_string)));
			}
		}
	}
}

//===============================================================================
//===============================================================================

// Helper struct to communicate a line in the match history list.
struct MatchHistoryLine
{
public:
	MabDate date;

	FString WinningTeamMnemonic;
	FString WinningTeamScore;
	FString LosingTeamScore;

	bool bLeftScoreGreen = false;
	bool bRightScoreRed = false;
};

//===============================================================================
//===============================================================================

bool MatchHistoryLineComp(const MatchHistoryLine& left, const MatchHistoryLine& right)
{
	return left.date > right.date;
}

//===============================================================================
//===============================================================================

MatchHistoryLine GetMatchHistoryStrings(unsigned short team1_db_id, unsigned short team2_db_id, int team1_score, int team2_score, bool team1_player_controlled, bool team2_player_controlled)
{
	// Looking for a string that goes <WINNING_TEAM_MNEMONIC> <WINNING_TEAM_SCORE> - <LOSING_TEAM_SCORE>
	//	E.g. ARG 22 - 7
	//	With any player-controlled scores being highlighted if they won or lost.
	MatchHistoryLine NewHistoryLine = MatchHistoryLine();

	if (team1_score > team2_score)
	{
		RL3DB_TEAM winning_team(team1_db_id);
		MabString teamMnemonic = winning_team.GetMnemonic();
		RUHUDUpdater::CensorTeamMnemonic(team1_db_id, teamMnemonic);
		NewHistoryLine.WinningTeamMnemonic = ANSI_TO_TCHAR(teamMnemonic.c_str());

		NewHistoryLine.WinningTeamScore = FString::FromInt(team1_score);
		NewHistoryLine.bLeftScoreGreen = team1_player_controlled;

		NewHistoryLine.LosingTeamScore = FString::FromInt(team2_score);
		NewHistoryLine.bRightScoreRed = team2_player_controlled;

	}
	else if (team1_score < team2_score)
	{
		RL3DB_TEAM winning_team(team2_db_id);
		MabString teamMnemonic = winning_team.GetMnemonic();
		RUHUDUpdater::CensorTeamMnemonic(team2_db_id, teamMnemonic);
		NewHistoryLine.WinningTeamMnemonic = ANSI_TO_TCHAR(teamMnemonic.c_str());

		NewHistoryLine.WinningTeamScore = FString::FromInt(team2_score);
		NewHistoryLine.bLeftScoreGreen = team2_player_controlled;

		NewHistoryLine.LosingTeamScore = FString::FromInt(team1_score);
		NewHistoryLine.bRightScoreRed = team1_player_controlled;
	}
	else if (team1_score == team2_score)
	{
		if (team1_player_controlled)
		{
			RL3DB_TEAM team1(team1_db_id);
			MabString teamMnemonic = team1.GetMnemonic();
			RUHUDUpdater::CensorTeamMnemonic(team1_db_id, teamMnemonic);
			NewHistoryLine.WinningTeamMnemonic = ANSI_TO_TCHAR(teamMnemonic.c_str());

			NewHistoryLine.WinningTeamScore = FString::FromInt(team1_score);
			NewHistoryLine.LosingTeamScore = FString::FromInt(team2_score);
		}
		else if (team2_player_controlled)
		{
			RL3DB_TEAM team2(team2_db_id);
			MabString teamMnemonic = team2.GetMnemonic();
			RUHUDUpdater::CensorTeamMnemonic(team2_db_id, teamMnemonic);
			NewHistoryLine.WinningTeamMnemonic = ANSI_TO_TCHAR(teamMnemonic.c_str());

			NewHistoryLine.WinningTeamScore = FString::FromInt(team2_score);
			NewHistoryLine.LosingTeamScore = FString::FromInt(team1_score);
		}
	}

	return NewHistoryLine;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerSchedulePopulateMatchHistoryString(RL3DB_MATCH SelectedMatch, RL3DB_COMPETITION_INSTANCE SelectedCompetition, RL3Database* pDatabase)
{
	static const size_t NUMBER_OF_MATCHES_TO_INCLUDE = 3;

	UWidget* last_three_matches_line_nodes[NUMBER_OF_MATCHES_TO_INCLUDE];
	UTextBlock* last_three_matches_header = nullptr;

	UVerticalBox* pMatchHistoryVerticalBox = Cast<UVerticalBox>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::VerticalBoxHistory));
	if (pMatchHistoryVerticalBox)
	{
		// First child in the box is the header.
		last_three_matches_header = Cast<UTextBlock>(pMatchHistoryVerticalBox->GetChildAt(0));

		// The rest of the children are the matches.
		for (size_t i = 0; i < NUMBER_OF_MATCHES_TO_INCLUDE; ++i)
		{
			last_three_matches_line_nodes[i] = pMatchHistoryVerticalBox->GetChildAt(i + 1);

			if (last_three_matches_line_nodes[i] == nullptr)
			{
				return;
			}

			last_three_matches_line_nodes[i]->SetVisibility(ESlateVisibility::Collapsed);
		}
	}

	if (!last_three_matches_header)
	{
		return;
	}

	const RL3_CAREER_COMPLETED_MATCHES*	const completed_matches = pCareerModeManager->GetCompletedMatches();
	if (completed_matches == NULL)
	{
		MABBREAK();
		return;
	}

	unsigned short home_team_index = SelectedMatch.GetHomeTeam();
	unsigned short away_team_index = SelectedMatch.GetAwayTeam();

	const unsigned short target_team1_db_id = home_team_index == SHRT_MAX ? DB_INVALID_ID : SelectedCompetition.GetDbTeam(home_team_index).GetDbId();
	const unsigned short target_team2_db_id = away_team_index == SHRT_MAX ? DB_INVALID_ID : SelectedCompetition.GetDbTeam(away_team_index).GetDbId();

	// Determine which of these teams is the player team.
	const bool team1_player_controlled = pCareerModeManager->GetIsTeamPlayerControlled(target_team1_db_id);
	const bool team2_player_controlled = pCareerModeManager->GetIsTeamPlayerControlled(target_team2_db_id);

	if (!team1_player_controlled && !team2_player_controlled)
	{
		MABBREAKMSG("There appear to be no player-controlled teams in the match you're targeting!");
	}

	MabVector< MatchHistoryLine > history_line_list(SIFHEAP_DYNAMIC);

	for (unsigned short i = 0u; i < completed_matches->GetNumCompletedMatches(); ++i)
	{
		unsigned short this_home_team_db_id, this_away_team_db_id;
		int this_home_team_score, this_away_team_score;
		MabDate this_date;

		completed_matches->GetMatchInfoAtIndex(i, &this_home_team_db_id, &this_away_team_db_id, &this_home_team_score, &this_away_team_score, &this_date);

		if ((target_team1_db_id == this_home_team_db_id && target_team2_db_id == this_away_team_db_id)
			|| (target_team2_db_id == this_home_team_db_id && target_team1_db_id == this_away_team_db_id))
		{
			const bool home_team_player_controlled = (target_team1_db_id == this_home_team_db_id) ? team1_player_controlled : team2_player_controlled;
			const bool away_team_player_controlled = (target_team1_db_id == this_away_team_db_id) ? team1_player_controlled : team2_player_controlled;

			MatchHistoryLine history_line = GetMatchHistoryStrings(this_home_team_db_id, this_away_team_db_id, this_home_team_score, this_away_team_score, home_team_player_controlled, away_team_player_controlled);
			history_line.date = this_date;

			history_line_list.push_back(history_line);
		}
	}

	if (history_line_list.empty())
	{
		last_three_matches_header->SetVisibility(ESlateVisibility::Collapsed);
		return;
	}
	else
	{
		last_three_matches_header->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	}

	std::sort(history_line_list.begin(), history_line_list.end(), MatchHistoryLineComp);

	if (history_line_list.size() > NUMBER_OF_MATCHES_TO_INCLUDE)
	{
		history_line_list.resize(NUMBER_OF_MATCHES_TO_INCLUDE);
	}

	MabStringList::StringList history_str_list(SIFHEAP_DYNAMIC);
	size_t ctr = 0u;
	for (MabVector< MatchHistoryLine >::const_iterator iter = history_line_list.begin(); iter != history_line_list.end(); ++iter, ++ctr)
	{
		UWidget* pCurrentWidget = last_three_matches_line_nodes[ctr];

		if (pCurrentWidget)
		{
			pCurrentWidget->SetVisibility(ESlateVisibility::SelfHitTestInvisible);

			UTextBlock* pTeamNameText = Cast<UTextBlock>(FindChildOfTemplateWidget(pCurrentWidget, WWUIScreenCareerCompetitionInfo_UI::TextWinnerTeamName));

			if (pTeamNameText)
			{
				pTeamNameText->SetText(FText::FromString(history_line_list[ctr].WinningTeamMnemonic));
			}

			UTextBlock* pWinningScoreText = Cast<UTextBlock>(FindChildOfTemplateWidget(pCurrentWidget, WWUIScreenCareerCompetitionInfo_UI::TextWinnerScore));

			if (pWinningScoreText)
			{
				pWinningScoreText->SetText(FText::FromString(history_line_list[ctr].WinningTeamScore));

				// Reset the colour
				pWinningScoreText->SetColorAndOpacity(FLinearColor(0.084376f, 0.318547f, 0.610496f, 1.0f));

				if (history_line_list[ctr].bLeftScoreGreen)
				{
					pWinningScoreText->SetColorAndOpacity(UWWUIPopulatorCareerSchedule::GAMMA_CORRECTED_GREEN);
				}
			}

			UTextBlock* pLosingScoreText = Cast<UTextBlock>(FindChildOfTemplateWidget(pCurrentWidget, WWUIScreenCareerCompetitionInfo_UI::TextLoserScore));

			if (pLosingScoreText)
			{
				pLosingScoreText->SetText(FText::FromString(history_line_list[ctr].LosingTeamScore));

				// Reset the colour
				pLosingScoreText->SetColorAndOpacity(FLinearColor(0.084376f, 0.318547f, 0.610496f, 1.0f));

				if (history_line_list[ctr].bRightScoreRed)
				{
					pLosingScoreText->SetColorAndOpacity(UWWUIPopulatorCareerSchedule::GAMMA_CORRECTED_RED);
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerScheduleUpdateTeamPanel(UWidget* pTeamPanelWidget, RL3DB_MATCH SelectedMatch, RL3DB_COMPETITION_INSTANCE SelectedCompetition, int32 TeamDatabaseID, RL3Database* pDatabase)
{
	if (pTeamPanelWidget == NULL) return;

	RL3DB_TEAM Team = pDatabase->GetTeam(TeamDatabaseID);

	//// Change the background colour.

	UImage* pBackgroundImage = Cast<UImage>(FindChildOfTemplateWidget(pTeamPanelWidget, WWUIScreenCareerCompetitionInfo_UI::ImageTeamPanelBackground));

	if (pBackgroundImage)
	{
		// The opposition is still TBD
		if (Team.GetDbId() == DB_INVALID_ID)
		{
			pBackgroundImage->SetBrushTintColor(FLinearColor::White);
		}
		else
		{
			SIFUIHelpers::SetImageColourFromTeamColour(pBackgroundImage, Team);
		}
	}

	// Change the display string; maybe include home icon.
	// The opposition is still TBD
	UTextBlock* pTeamNameText = Cast<UTextBlock>(FindChildOfTemplateWidget(pTeamPanelWidget, WWUIScreenCareerCompetitionInfo_UI::TextTeamName));

	if (pTeamNameText)
	{
		if (Team.GetDbId() == DB_INVALID_ID)
		{
			SetWidgetText(pTeamNameText, FText::FromString("--")); // Consistent with the rets of the UI, where TDB or unknown teams are displayed as such
		}
		else
		{
			MabString TeamName = Team.GetName();
			const unsigned short SelectedMatchHomeTeamIndex = SelectedMatch.GetHomeTeam();
			RL3DB_TEAM RL3Team = SelectedCompetition.GetDbTeam(SelectedMatchHomeTeamIndex);
			const bool bTeamAtHome = (RL3Team.GetDbId() == Team.GetDbId());
			
			UWidget* pLeftHomeImage = FindChildOfTemplateWidget(pTeamPanelWidget, WWUIScreenCareerCompetitionInfo_UI::ImageHomeIcon);
			if (pLeftHomeImage)
			{
				pLeftHomeImage->SetVisibility(bTeamAtHome ? ESlateVisibility::SelfHitTestInvisible : ESlateVisibility::Collapsed);
			}

			RUHUDUpdater::CensorTeamName(Team.GetDbId(), TeamName);

			SetWidgetText(pTeamNameText, FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(TeamName)).ToUpper());
		}
	}

	// Change the opponent icon.

	UImage* pLogoImage = Cast<UImage>(FindChildOfTemplateWidget(pTeamPanelWidget, WWUIScreenCareerCompetitionInfo_UI::ImageTeamLogo));

	if (pLogoImage)
	{
		// The opposition is still TBD
		if (Team.GetDbId() == DB_INVALID_ID)
		{
			pLogoImage->SetVisibility(ESlateVisibility::Hidden);
		}
		else
		{
			UTexture2D* pTexture = nullptr;

			MabString pathString = SIFGameHelpers::GAGetTeamLogoAssetPath(Team.GetDbId());
			FString name = FString(pathString.c_str());
			pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name));

			if (pTexture)
			{
				pLogoImage->SetBrushFromTexture(pTexture, true);
				pLogoImage->SetVisibility(ESlateVisibility::Visible);
			}
		}
	}

	// Change the opponent rating number.

	UTextBlock* pRatingText = Cast<UTextBlock>(FindChildOfTemplateWidget(pTeamPanelWidget, WWUIScreenCareerCompetitionInfo_UI::TextRating));
	// The opposition is still TBD
	if (Team.GetDbId() == DB_INVALID_ID)
	{
		SetWidgetText(pRatingText, FText::FromString("00.0"));
	}
	else
	{
		FString OpponentTeamRating = FString::Printf(TEXT("%0.2f"), Team.GetNormalisedRanking() * 100.0f);
		SetWidgetText(pRatingText, FText::FromString(OpponentTeamRating));
	}


	int ShieldHolder = SIFApplication::GetApplication()->GetCareerModeManager()->GetRanfurlyShieldHolder();

	UWidget* pRanfurlyNode = FindChildOfTemplateWidget(pTeamPanelWidget, WWUIScreenCareerCompetitionInfo_UI::ImageRanfurlyShield);

	if (pRanfurlyNode)
	{
		bool bRanfurlyTeamPlaying = static_cast<unsigned short>(ShieldHolder) == Team.GetDbId() && ShieldHolder != DB_INVALID_ID;

		pRanfurlyNode->SetVisibility(bRanfurlyTeamPlaying ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerScheduleBumpMoreInfoCarousell()
{
	UWidgetSwitcher* pMoreInfoCarousellWidgetSwitcher = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::WidgetSwitcherMatchInfoCarousell));
	
	if (pMoreInfoCarousellWidgetSwitcher)
	{
		int32 NewIndex = FMath::ClampWrap(pMoreInfoCarousellWidgetSwitcher->GetActiveWidgetIndex() + 1, 0, pMoreInfoCarousellWidgetSwitcher->GetChildrenCount() - 1);

		pMoreInfoCarousellWidgetSwitcher->SetActiveWidgetIndex(NewIndex);
	}
}

//===============================================================================
//===============================================================================

FString UWWUIScreenCareerCompetitionInfo::CareerScheduleGetStatValue(ECompetitionInfoScheduleStatValue Stat, unsigned short db_id, RL3TableCache* player_match_stats_cache, MabMap< int, float >& field_to_summed_value_map)
{
	FString ReturnString = "";

	switch (Stat)
	{
	case ECompetitionInfoScheduleStatValue::CISSV_POSSESIONS:
	{
		const float possession_raw = player_match_stats_cache->ReadFloat(db_id, CCDB_COMPINSTPLAYERMATCHSTATS_POSSESSION);
		const float possession_portion = possession_raw / field_to_summed_value_map[CCDB_COMPINSTPLAYERMATCHSTATS_POSSESSION];
		ReturnString = FString::FromInt(possession_portion * 100.0) + "%";
	}
		break;
	case ECompetitionInfoScheduleStatValue::CISSV_TERRITORY:
	{
		const float territory_raw = player_match_stats_cache->ReadFloat(db_id, CCDB_COMPINSTPLAYERMATCHSTATS_TERRITORY);
		const float territory_portion = territory_raw / field_to_summed_value_map[CCDB_COMPINSTPLAYERMATCHSTATS_TERRITORY];
		ReturnString = FString::FromInt(territory_portion * 100.0) + "%";
	}
		break;
	case ECompetitionInfoScheduleStatValue::CISSV_HANDLING_ERRORS:
	{
		const unsigned short num_handling_errors = player_match_stats_cache->ReadUChar(db_id, CCDB_COMPINSTPLAYERMATCHSTATS_HANDLING_ERRORS);
		ReturnString = FString::FromInt(num_handling_errors);
	}
		break;
	case ECompetitionInfoScheduleStatValue::CISSV_PENALTIES_CONCEEDED:
	{
		const unsigned short num_penalties_conceded = player_match_stats_cache->ReadUChar(db_id, CCDB_COMPINSTPLAYERMATCHSTATS_PENALTIES_CONCEDED);
		ReturnString = FString::FromInt(num_penalties_conceded);
	}
		break;
	case ECompetitionInfoScheduleStatValue::CISSV_TRIES:
	{
		const unsigned short num_tries = player_match_stats_cache->ReadUChar(db_id, CCDB_COMPINSTPLAYERMATCHSTATS_TRIES);
		ReturnString = FString::FromInt(num_tries);
	}
		break;
	case ECompetitionInfoScheduleStatValue::CISSV_CONVERSIONS:
	{
		const unsigned short num_conversions = player_match_stats_cache->ReadUChar(db_id, CCDB_COMPINSTPLAYERMATCHSTATS_CONVERSIONS);
		ReturnString = FString::FromInt(num_conversions);
	}
		break;
	case ECompetitionInfoScheduleStatValue::CISSV_PENALTIES:
	{
		const unsigned short num_penalties_converted = player_match_stats_cache->ReadUChar(db_id, CCDB_COMPINSTPLAYERMATCHSTATS_PENALTIES_CONVERTED);
		ReturnString = FString::FromInt(num_penalties_converted);
	}
		break;
	case ECompetitionInfoScheduleStatValue::CISSV_DROP_GOALS:
	{
		const unsigned short num_drop_goals = player_match_stats_cache->ReadUChar(db_id, CCDB_COMPINSTPLAYERMATCHSTATS_DROP_GOALS);
		ReturnString = FString::FromInt(num_drop_goals);
	}
		break;
	default:
	{
		ReturnString = "Warning! Out of Enum Bounds!";
	}
		break;
	}

	return ReturnString;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerSchedulePopulateStats(UWidget* pVerticalBoxWidget, int StartIndex, unsigned short db_id, RL3TableCache* player_match_stats_cache, MabMap< int, float >& field_to_summed_value_map, FString TextName)
{
	UVerticalBox* pVerticalBox = Cast<UVerticalBox>(pVerticalBoxWidget);

	if (pVerticalBox)
	{
		for (int i = 0; i <= pVerticalBox->GetChildrenCount(); ++i)
		{
			UWidget* pCurrentStatWidget = pVerticalBox->GetChildAt(i);

			if (pCurrentStatWidget)
			{
				UTextBlock* pPlayerStatValue = Cast<UTextBlock>(FindChildOfTemplateWidget(pCurrentStatWidget, TextName));

				if (pPlayerStatValue)
				{
					FString ValueString = CareerScheduleGetStatValue((ECompetitionInfoScheduleStatValue)(i + StartIndex), db_id, player_match_stats_cache, field_to_summed_value_map);
					SetWidgetText(pPlayerStatValue, FText::FromString(ValueString));
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerCompetitionInfo::CareerScheduleSetSimulationDateToMatchSelectedInCompetitionScheduleScreen()
{
	// If date is current time then do nothing.
	const MabDate* current_date = pCareerModeManager->GetCurrentDate();
	if (CareerScheduleSelectedMatchDate.GetYear() == current_date->GetYear() && CareerScheduleSelectedMatchDate.GetDayOfYear() == current_date->GetDayOfYear()
		&& CareerScheduleSelectedMatchDate.GetHour() == current_date->GetHour()
		&& CareerScheduleSelectedMatchDate.GetMinute() == current_date->GetMinute()
		)
	{
		return false;
	}

	pCareerModeManager->SetSkipToDate(CareerScheduleSelectedMatchDate);

	return true;
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerCompetitionInfo::CareerScheduleSetSimulationDateToMatchSelectedInCompetitionFixturesScreen(int32 MatchIndex)
{
	RUActiveCompetitionBase* pActiveCompetition = pCareerModeManager->GetPlayersActiveCompetition();

	if (pActiveCompetition)
	{
		const RUDB_COMP_INST_MATCH* pCompetitionMatch = pActiveCompetition->GetMatch(MatchIndex);

		if (pCompetitionMatch == NULL)
		{
			return false;
		}

		pCareerModeManager->SetSkipToDate(pCompetitionMatch->date);


		return true;
	}

	return false;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::ResetAxisTrigger()
{
	if (m_triggerAxisHandle.IsValid())
	{
		UWWUIFunctionLibrary::StopTimer(m_triggerAxisHandle);
		m_canTriggerAxis = true;
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerFixturesOnWindowEnter()
{
	// called whenever we tab to this screen or when simulate matches popup is dismissed

	SetSlateLayoutCaching(true);

	CareerFixturesLastSelectedIndex = -1;
	CareerFixturesSelectedMatchIndex = -1;

	CareerScheduleUpdateHeading();

	CareerFixturesUpdateFixtureTable();

	UWidget* pFilterWidget = FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::HorizontalBoxFixturesSwitcher);

	if (pFilterWidget)
	{
		pFilterWidget->SetVisibility(pCareerModeManager->IsLionsTour() ? ESlateVisibility::Collapsed : ESlateVisibility::SelfHitTestInvisible);
	}

	CareerFixturesGoToCurrentDate();
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerFixturesSimulateToMatch()
{
	if (!pCareerModeManager->IsLionsTour())
	{
		if (CareerFixturesSelectedMatchIndex != -1)
		{
			if (CareerScheduleSetSimulationDateToMatchSelectedInCompetitionFixturesScreen(CareerFixturesSelectedMatchIndex))
			{
				if (SIFGameHelpers::GAGetIsAProMode() && !pCareerModeManager->GetPostCompetitionMode())
				{
					LaunchConfirmSimulationPopup();
				}
				else
				{
					OnConfirmSimulation(nullptr);
				}

				CurrentScreenState = EScreenState::STATE_SIMULATING;
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerFixturesSwitchCategory(bool SkipPopulateAndRefresh /*= false*/)
{
	// Toggle the filter type for the scrollbox
	CurrentFixtureType = (CurrentFixtureType == ECompetitionInfoFixtureType::ALL_GAMES) ? ECompetitionInfoFixtureType::PLAYER_GAMES : ECompetitionInfoFixtureType::ALL_GAMES;

	UWidgetSwitcher* pFixturesWidgetSwitcher = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::WidgetSwitcherFixtures));

	if (pFixturesWidgetSwitcher)
	{
		pFixturesWidgetSwitcher->SetActiveWidgetIndex((int32)CurrentFixtureType);
	}

	CareerFixturesUpdateFixtureTable(SkipPopulateAndRefresh);

	CareerFixturesGoToCurrentDate();
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerFixturesGoToCurrentDate()
{
	// Force selection of current date.

	NoCurrentDate = false;

	uint32 NewIndex = 1;
	bool bFoundFixture = false;

	UWWUIScrollBox* pScheduleScrollBox = GetTabDefaultScrollbox(ECompetitionInfoTab::CIT_FIXTURES);

	if (pScheduleScrollBox)
	{
		// Look for a game which is the current date
		for (int i = 0; i < pScheduleScrollBox->GetListLength(); i++)
		{
			UWWUIListField* pListField = pScheduleScrollBox->GetListField(i);

			if (pListField)
			{
				bool bIsCurrentDate = pListField->GetBoolProperty(UWWUIPopulatorCompetitionFixtures::RU_COMP_PROP_IS_CURRENT_DATE);

				const int match_index = pListField->GetIntProperty(UWWUIPopulatorCompetitionFixtures::RU_COMP_PROP_MATCH_INDEX);
				if (match_index >= 0)
				{
					if (match_index >= pCareerModeManager->GetActiveCompetition()->GetNextMatchIndex())
					{
						NewIndex = i;
						bFoundFixture = true;
						ScrollToIndex(pScheduleScrollBox, NewIndex);
						break;
					}
				}
			}
		}
	}

	// could not find next player game, competition must be over. Select first index and hide legend for and block goto current date button.
	if (!bFoundFixture)
	{
		NoCurrentDate = true;
		ScrollToIndex(pScheduleScrollBox, NewIndex);
	}

	CareerFixturesUpdateLegend(NewIndex);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerFixturesUpdateFixtureTable(bool SkipPopulateAndRefresh /*= false*/)
{
	UWWUIPopulatorCompetitionFixtures* pFixturesPopulator = GetFixturesPopulator();

	if (pFixturesPopulator)
	{
		int32 GroupID = pFixturesPopulator->GetGroupID();

		FString DisplayText = "";

		if (GroupID == STANDINGS_FULL_TABLE_INDEX)
		{
			// Set the filter text and table title

			DisplayText = "[ID_COMP_FIXTURES_ALL_TEAMS]";
		}
		else
		{
			DisplayText = "[ID_COMP_FIXTURES_MY_TEAM]";

			if (!pCareerModeManager->IsInFranchise() && SIFGameHelpers::GACompetitionGetNumPlayerTeams() > 1)
			{
				DisplayText = "[ID_COMP_FIXTURES_MY_TEAMS]";
			}
		}

		UWidget* pFixtureCategorySelectorWidget = FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::FixturesCategorySelector);

		if (pFixtureCategorySelectorWidget)
		{
			UTextBlock* pTitleText = Cast<UTextBlock>(FindChildOfTemplateWidget(pFixtureCategorySelectorWidget, WWUIScreenCareerCompetitionInfo_UI::TextCategoryName));

			if (pTitleText)
			{
				SetWidgetText(pTitleText, FText::FromString(UWWUITranslationManager::Translate(DisplayText)));
			}

			UTextBlock* pNumCategoriesText = Cast<UTextBlock>(FindChildOfTemplateWidget(pFixtureCategorySelectorWidget, WWUIScreenCareerCompetitionInfo_UI::TextNumCategories));

			if (pNumCategoriesText)
			{
				SetWidgetText(pNumCategoriesText, FText::FromString(""));
			}
		}
	}

	if (!SkipPopulateAndRefresh)
	{
		UWWUIScrollBox* pFixturesScrollBox = GetTabDefaultScrollbox(ECompetitionInfoTab::CIT_FIXTURES);

		if (pFixturesScrollBox)
		{
			pFixturesScrollBox->PopulateAndRefresh();
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::CareerFixturesUpdateLegend(int NewIdx)
{
	CareerFixturesSelectedMatchIndex = -1;

	UWWUIScrollBox* pScheduleScrollBox = GetTabDefaultScrollbox(ECompetitionInfoTab::CIT_FIXTURES);

	if (pScheduleScrollBox)
	{
		UWWUIListField* pSelectedListField = pScheduleScrollBox->GetListField(NewIdx);

		if (pSelectedListField)
		{
			bool bIsCurrentDate = pSelectedListField->GetBoolProperty(UWWUIPopulatorCompetitionFixtures::RU_COMP_PROP_IS_CURRENT_DATE);

			UWWUIModularLegendText* pModularLegend = Cast<UWWUIModularLegendText>(FindChildWidget(WWUIScreenCareerCompetitionInfo_UI::ModularLegendText));

			if (pModularLegend)
			{
				pModularLegend->HideLegend((int)ECompetitionInfoLegend::CIL_SIMULATE_TO_MATCH);

				if (bIsCurrentDate || NoCurrentDate)
				{
					// if no current date there will be no matches to simulate to either.

					pModularLegend->HideLegend((int)ECompetitionInfoLegend::CIL_GO_TO_CURRENT_DATE);
				}
				else
				{
					pModularLegend->ShowLegend((int)ECompetitionInfoLegend::CIL_GO_TO_CURRENT_DATE);

					bool bInvolvesPlayer = pSelectedListField->GetBoolProperty(UWWUIPopulatorCompetitionFixtures::RU_COMP_PROP_INVOLVES_PLAYER);

					if (bInvolvesPlayer)
					{
						if (!pCareerModeManager->IsLionsTour())
						{
							pModularLegend->ShowLegend((int)ECompetitionInfoLegend::CIL_SIMULATE_TO_MATCH);

							CareerFixturesSelectedMatchIndex = pSelectedListField->GetIntProperty(UWWUIPopulatorCompetitionFixtures::RU_COMP_PROP_MATCH_INDEX);
						}
					}
				}
			}
		}
	}
}


void UWWUIScreenCareerCompetitionInfo::ScrollToIndex(UWWUIScrollBox* pScrollBox, int32 index)
{
	if (pScrollBox)
	{
		pScrollBox->SetSelectedIndex(index);
		UScrollBox* pInnerScrollBox = pScrollBox->GetScrollBox();
		if (pInnerScrollBox)
		{
			pInnerScrollBox->OnUserScrolled.Broadcast(pInnerScrollBox->GetScrollOffset());
		}
		else
		{
			ensure(pInnerScrollBox);
		}
	}
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerCompetitionInfo::OnConfirmSimulation(APlayerController* OwningPlayer)
{
	if (pCareerModeManager->GetIsCareerModePro())
	{
		pCareerModeManager->OnProConfirmedSimulation();
	}

	FOnDismissPopup OnDismissDelegate;
	OnDismissDelegate.BindUObject(this, &UWWUIScreenCareerCompetitionInfo::OnWindowEnter);

	UWWUIModalSimulateMatchData* modalData = NewObject<UWWUIModalSimulateMatchData>();

	modalData->ForceSimulate = false;
	modalData->SkippingYears = false;
	modalData->bPreviousMatchPlayed = false;
	modalData->bDismissWithoutInput = true;
	modalData->bSimulateToDate = true;
	modalData->OnPopupDismissedDelegate = OnDismissDelegate;

	pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::SimulateMatch, modalData);
	
	return true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerCompetitionInfo::LaunchConfirmSimulationPopup()
{
	FWWUIModalDelegate MyDelegate = FWWUIModalDelegate::CreateUObject(this, &UWWUIScreenCareerCompetitionInfo::OnDenySimulation);

	TArray<FModalButtonInfo> ButtonData;
	ButtonData.Add(FModalButtonInfo("[ID_YES]", FWWUIModalDelegate::CreateUObject(this, &UWWUIScreenCareerCompetitionInfo::OnConfirmSimulation)));
	ButtonData.Add(FModalButtonInfo("[ID_NO]", MyDelegate));

	UWWUIModalWarningMessageData* InData = NewObject<UWWUIModalWarningMessageData>();

	InData->WarningDialogue = "[ID_CONFIRM_SIMULATION_HUB]";
	InData->LegendString = "[ID_SETTINGS_MENU_HELP]";
	InData->ButtonData = ButtonData;
	InData->OnSelectDelegate = MyDelegate;
	InData->OnBackDelegate = MyDelegate;

	pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, InData);
}

//===============================================================================
//===============================================================================

UWWUIPopulatorCareerStandings* UWWUIScreenCareerCompetitionInfo::GetStandingsPopulator()
{
	UWWUIScrollBox* pScrollBox = GetTabDefaultScrollbox(ECompetitionInfoTab::CIT_STANDINGS);

	if (pScrollBox)
	{
		return Cast<UWWUIPopulatorCareerStandings>(pScrollBox->GetPopulator());
	}

	return nullptr;
}

//===============================================================================
//===============================================================================

UWWUIPopulatorCompetitionFixtures* UWWUIScreenCareerCompetitionInfo::GetFixturesPopulator()
{
	UWWUIScrollBox* pScrollBox = GetTabDefaultScrollbox(ECompetitionInfoTab::CIT_FIXTURES);

	if (pScrollBox)
	{
		return Cast<UWWUIPopulatorCompetitionFixtures>(pScrollBox->GetPopulator());
	}

	return nullptr;
}

//===============================================================================
//===============================================================================

UWWUIPopulatorCompetitionFixtures* UWWUIScreenCareerCompetitionInfo::GetFixturesPopulator(ECompetitionInfoFixtureType FixtureType)
{
	UWWUIScrollBox* pScrollBox = GetFixturesScrollBox(FixtureType);

	if (pScrollBox)
	{
		return Cast<UWWUIPopulatorCompetitionFixtures>(pScrollBox->GetPopulator());
	}

	return nullptr;
}

//===============================================================================
//===============================================================================

UWWUIScrollBox* UWWUIScreenCareerCompetitionInfo::GetFixturesScrollBox(ECompetitionInfoFixtureType FixtureType)
{
	FString ScrollBoxToFind = "";

	if (FixtureType == ECompetitionInfoFixtureType::PLAYER_GAMES)
	{
		ScrollBoxToFind = WWUIScreenCareerCompetitionInfo_UI::ScrollBoxPlayerFixtures;
	}
	else
	{
		ScrollBoxToFind = WWUIScreenCareerCompetitionInfo_UI::ScrollBoxAllFixtures;
	}

	return Cast<UWWUIScrollBox>(FindChildWidget(ScrollBoxToFind));
}

//===============================================================================
//===============================================================================

FString UWWUIScreenCareerCompetitionInfo::FormatMabDate(const MabDate* pInMabDate)
{
	if (!pInMabDate)
	{
		return "Format Mab Date was passed an invalid date!";
	}

	return FString::FromInt(pInMabDate->GetDayNumber()) + " " + UWWUITranslationManager::Translate(SIFGameHelpers::GAConvertMabStringToFString(pInMabDate->GetMonthString(pInMabDate->GetMonth()))) + " " + FString::FromInt(pInMabDate->GetYear());
}

bool UWWUIScreenCareerCompetitionInfo::OnDenySimulation(APlayerController* OwningPlayer)
{
	CurrentScreenState = EScreenState::STATE_MAIN;

	SetSlateLayoutCaching(true);
	return true;
}

void UWWUIScreenCareerCompetitionInfo::SetSlateLayoutCaching(bool bEnabled)
{
	IConsoleVariable* enableSlateCaching = IConsoleManager::Get().FindConsoleVariable(TEXT("slate.enableLayoutCaching"));
	if (enableSlateCaching)
	{
		enableSlateCaching->Set(bEnabled ? 1 : 0);
	}
}
