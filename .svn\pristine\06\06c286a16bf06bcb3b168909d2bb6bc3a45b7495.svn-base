#ifndef RURoleSetplayPlayTheBallReceiver_H
#define RURoleSetplayPlayTheBallReceiver_H

#include "Mab/Time/MabTimer.h"
#include "Match/SSRole.h"
#include "RURoleSetplay.h"

/**
	Set Play Play the ball receiver
*/

class RURoleSetplayPlayTheBallReceiver : public RURoleSetplay
{
	MABRUNTIMETYPE_HEADER(RURoleSetplayPlayTheBallReceiver);

public:

	enum class RoleState
	{
		MOVING,					// move into position
		WAITING_FOR_RELEASE,    // waiting for the ball holder to release the ball
		PICKING_UP,				// Picking up the ball
		BALL_RECEIVED,			// Ball has been received
		DONE,					// player moves to general play as the ball holder
	};

	RURoleSetplayPlayTheBallReceiver(SIFGameWorld* game);

	/// Enter this role with the specified player.
	void Enter(ARugbyCharacter* player) override;

	/// Exit this role.
	void Exit(bool forced) override;

	/// Advance this role.
	void UpdateLogic(const MabTimeStep& game_time_step) override;

	/// Get the fitness of the player for the given behaviour
	static int GetFitness(const ARugbyCharacter* player, const SSRoleArea* area);

	/// returns true if we're interruptible, false if we're not
	bool IsInterruptable() const override;

	const char* GetShortClassName() const override { return "PTBR"; }

	void AnimationEvent(float time, ERugbyAnimEvent event, size_t userdata, bool bIsBlendingOut = false);

private:

	FVector GetPasserPosition();

	FVector GetReceiverPosition();

	void SetReceiverPosition();

	void UpdateReceivePosition();

	RoleState state;

	FVector m_last_passer_pos;

	FVector m_last_receiver_pos;

	const char* PICKUP_ANIM = "grab";
};

#endif //RURoleSetplayPlayTheBallReceiver_H
