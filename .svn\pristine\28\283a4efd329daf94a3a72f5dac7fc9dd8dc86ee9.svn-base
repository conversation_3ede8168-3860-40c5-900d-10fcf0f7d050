// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.


#include "WWUIScreenCustomiseCreateComp.h"

// Game Instance
#include "Rugby/RugbyGameInstance.h"

// Databases
#include "Rugby/Databases/RUGameDatabaseManager.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Database.h"

#include "Rugby/Match/RugbyUnion/RUDatabaseConstants.h"

// SIF Helpers
#include "Rugby/Utility/Helpers/SIFGameHelpers.h"
#include "Rugby/Utility/Helpers/SIFAudioHelpers.h"
#include "Rugby/Match/SIFUIConstants.h"
#include "Rugby/Utility/Helpers/SIFUIHelpers.h"

#if PLATFORM_PS4
#include "Rugby/Utility/Helpers/SIFGeneralHelpersPS4.h"
#endif

// Generated Headers
#include "Rugby/UI/GeneratedHeaders/WWUIScreenCustomiseCreateComp_UI_Namespace.h"
#include "Rugby/UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"

// WW UI Plugin
#include "WWUIScrollBox.h"
#include "WWUIFunctionLibrary.h"
#include "WWUIListField.h"
#include "WWUITranslationManager.h"
#include "WWUIRichTextBlockWithTranslate.h"
#include "WWUITextBlock.h"

// UE UI
#include "WidgetSwitcher.h"
#include "EditableTextBox.h"
#include "Utility/Helpers/SIFAudioHelpers.h"
#include "Image.h"
#include "WWUITabSwitcher.h"
#include "WidgetTree.h"
#include "UserWidget.h"
#include "EditableText.h"
#include "ScrollBox.h"
#include "Match/RugbyUnion/CompetitionMode/RUDBCompetitionTypes.h"
#include "Match/RugbyUnion/CompetitionMode/RUCompetitionCustomisationHelper.h"
#include "WWUIEditableText.h"
#include "WWUIEditableTextBox.h"
#include "WWUIModalTemplate.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3TournamentConstants.h"
#include "Match/RugbyUnion/RUStadiumManager.h"
#include "UI/Populators/WWUIPopulatorCompPool.h"
#include "UI/Populators/WWUIPopulatorTeamSelectList.h"
#include "Modals/WWUIModalWarningMessage.h"
#include "FlowNodes/FlowControlManager.h"
#include "FlowNodes/SIFMainMenuFlowNode.h"
#include "SIFFlowConstants.h"

static const int STARTING_INDEX_POOLS = 1;
static const int STARTING_INDEX_LEAGUE = 2;

#define SEARCH_MAX_TEXT_LENGTH	(32)

bool AlphabeticalSortComparator(const RUDB_ICON& left, const RUDB_ICON& right)
{
	return strcmp(left.GetNameString(), right.GetNameString()) < 0;
}

void UWWUIScreenCustomiseCreateComp::Startup(UWWUIStateScreenData* InData /*= nullptr*/)
{

	static constexpr int CompNameMaxCharacters = 42;

	/// Init with default values.
	m_currentTab = ECompetitionCustomiseTab::DETAILS;

	m_pCareerModeManager	= nullptr;
	m_pRugbyGameInstance	= nullptr;
	m_pDatabaseManager		= nullptr;

	TabDefaultScrollBoxes.Add(ECompetitionCustomiseTab::DETAILS,	Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::DetailsListBox)));
	TabDefaultScrollBoxes.Add(ECompetitionCustomiseTab::FORMAT,		Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::FormatListBox)));
	TabDefaultScrollBoxes.Add(ECompetitionCustomiseTab::POINTS,		Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::PointsListBox)));
	TabDefaultScrollBoxes.Add(ECompetitionCustomiseTab::TEAMS,		Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::PoolScrollbox1)));
	
	// Load the Pool Widget groups
	for (int i = 0; i < COMPETITION_MAX_POOL_COUNT; i++)
	{
		PoolWidgetGroups.Add(FPoolWidgetGroup(i + 1, this));
	}

	m_pRugbyGameInstance	= SIFApplication::GetApplication();
	m_pCareerModeManager	= m_pRugbyGameInstance->GetCareerModeManager();
	m_pDatabaseManager		= m_pRugbyGameInstance->GetGameDatabaseManager();

	/// Load widgets.
	LogoImageWidget		= Cast<UImage>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::CompetitionLogo));
	TrophyImageWidget	= Cast<UImage>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::CompetitionTrophy));

	/// Setup text entry box.
	TextEntryBox = Cast<UUserWidget>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::TextEntryBox));

	UWWUIFunctionLibrary::SetVisibility(TextEntryBox, ESlateVisibility::Hidden);
	UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(TextEntryBox, WWUIScreenCustomiseCreateComp_UI::BorderLinesSearchBox), ESlateVisibility::Visible);

	if (UWWUIEditableTextBox* pSearchTextWidget = Cast<UWWUIEditableTextBox>(UWWUIFunctionLibrary::FindChildWidget(TextEntryBox, WWUIScreenCustomiseCreateComp_UI::EditableTextBoxSearchTextEntry)))
	{
		pSearchTextWidget->SetMaxCharLength(SEARCH_MAX_TEXT_LENGTH);
		pSearchTextWidget->ClearKeyboardFocusOnCommit = false;
		pSearchTextWidget->SynchronizeProperties();
	}

	/// Load first field.
	if (UWWUIListField* CompNameField = TabDefaultScrollBoxes[ECompetitionCustomiseTab::DETAILS]->GetListField((int)ECompetitionDetailsOptions::NAME))
	{
		/// Retreive it's editable text component.
		if (UWWUIEditableText* EditableTextComponent = Cast<UWWUIEditableText>(UWWUIFunctionLibrary::FindChildWidget(CompNameField, WWUIScreenCustomiseCreateComp_UI::EditableTextField)))
		{
			EditableTextComponent->SetMaxCharLength(CompNameMaxCharacters);
		}
	}

	

	/// Team Select Widget.
	TeamSelectWidget				= Cast<UUserWidget>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::SelectTeamWidget));
	CompetitionListSwitcher			= Cast<UUserWidget>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::CompetitionListSwitcher));
	TeamListScrollbox				= Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::TeamListScrollbox));
	SearchScrollbox					= Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::SearchScrollBox));

	/// Team Select Search box.
	TextEntryBoxSearchName			= Cast<UUserWidget>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::TextEntryBoxSearchName));
	TeamSelectSearchEditableTextBox = Cast<UWWUIEditableTextBox>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::EditableTextBoxSearchTextEntry));

#if (PLATFORM_XBOXONE || PLATFORM_SWITCH)
	//Comeptition Name
	SIFUIHelpers::SetEditableTextVirtualKeyboardType(Cast<UWWUIEditableText>(UWWUIFunctionLibrary::FindChildWidget(TabDefaultScrollBoxes[ECompetitionCustomiseTab::DETAILS]->GetListField(0), WWUIScreenCustomiseCreateComp_UI::EditableTextField)), EVirtualKeyboardType::AlphaNumeric);
#endif

	ugcUsernameCheckCompleteHandle = SIFApplication::GetApplication()->OnUGCUsernameCheckComplete.AddUObject(this, &UWWUIScreenCustomiseCreateComp::HandleUGCUsernameCheckComplete);
}


//===============================================================================
//===============================================================================
void UWWUIScreenCustomiseCreateComp::Shutdown()
{
	Super::Shutdown();
	SIFApplication::GetApplication()->OnUGCUsernameCheckComplete.Remove(ugcUsernameCheckCompleteHandle);
}

void UWWUIScreenCustomiseCreateComp::RegisterFunctions()
{
	AddInputAction("UI_Back",	FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::OnBack));
	AddInputAction("UI_Select", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::OnSelect));

	AddInputAction("UI_Left",	FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::OnLeft), true);
	AddInputAction("UI_Right",	FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::OnRight), true);

#if PLATFORM_SWITCH 
	AddInputAxis("UI_Triggers_Axis", FWWUIScreenAxisDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::OnSwitchTriggerAxis));
#else
	AddInputAction("UI_LeftTrigger",	FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::OnLeftTrigger));
	AddInputAction("UI_RightTrigger",	FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::OnRightTrigger));
#endif

	AddInputAction("RU_UI_ACTION_TABRIGHT",				FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::IncrementTab));
	AddInputAction("RU_UI_ACTION_TABLEFT",				FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::DecrementTab));

	AddInputAction("RU_UI_ACTION_EDIT_TEXT",			FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::OnOpenTextEntry));

	AddInputAction("RU_UI_ACTION_MOVE_TEAM",			FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::OnMoveTeam));
	AddInputAction("RU_UI_ACTION_AUTOFILL",				FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::OnAutofill));
	AddInputAction("RU_UI_ACTION_COMPETITION_ADD_TEAM", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::OnAddRemoveTeam));
}


void UWWUIScreenCustomiseCreateComp::ExecuteTableFunction(FString InTableId, int InIdx, FString InAction, FString InActionString)
{
	if (m_currentTab == ECompetitionCustomiseTab::TEAMS)
	{
		if (TeamTabState == ETeamTabState::TEAM_SELECT)
		{
			//< A team has been selected, we need to hide this list and return the selected team. >
			//< So which team has been selected. >
			UWWUIListField* selected_page = TeamListScrollbox->GetListField(TeamListScrollbox->GetSelectedIndex());
			int new_team_id = selected_page->GetIntProperty("team_db_id");

			if (!SIFGameHelpers::GADoesCustomCompetitionContainTeam(new_team_id))
			{
				int old_team_index = MoveSelection1->GetIntProperty(COMP_POOL_PROPERTY_TEAM_INDEX);
				int pool_num = GetPoolIndexFromListField(MoveSelection1);
				SIFGameHelpers::GAReplaceCustomCompetitionTeam(old_team_index, pool_num, new_team_id);

				/// We need to refresh all the elements so that they appear correctly.
				RefreshTeamTab();
				SetTeamTabState(ETeamTabState::NORMAL);
			}
		}
		else
		{
			OnMoveTeam(nullptr);
		}
	}
}


void UWWUIScreenCustomiseCreateComp::TableOnSelectionChange(FString InTableId, int OldIdx, int NewIdx)
{
	if (InTableId == WWUIScreenCustomiseCreateComp_UI::FormatListBox )
	{
		UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenCustomiseCreateComp_UI::FormatImage), NewIdx != (int)ECompetitionFormatOptions::GRAND_FINAL);
		UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenCustomiseCreateComp_UI::StadiumImage), NewIdx == (int)ECompetitionFormatOptions::GRAND_FINAL);
	}

	if (InTableId == WWUIScreenCustomiseCreateComp_UI::TeamListScrollbox)
	{
		//< Update all team information and logo. >
		TeamDatabaseID = GetSelectedTeamData(NewIdx);
		UpdateTeamInfo();
		UpdateHelpText();
	}

	for (int i = 0; i < PoolWidgetGroups.Num(); i++)
	{
		if (PoolWidgetGroups[i].Populator->GetTableId() == InTableId)
		{
			if (OldIdx == NewIdx)
			{
				//< Handle custom vertical navigation between scroll boxes. >
				int newScrollboxIndex = i + (OldIdx == 0 ? -4 : 4);
				if (PoolWidgetGroups.IsValidIndex(newScrollboxIndex))
				{
					if (PoolWidgetGroups[newScrollboxIndex].Scrollbox->GetVisibility() != ESlateVisibility::Collapsed)
					{
						DismissAllFocusEffect();
						PoolWidgetGroups[newScrollboxIndex].Scrollbox->SetSelectedIndex(OldIdx == 0 ? PoolWidgetGroups[newScrollboxIndex].Scrollbox->GetListLength()-1 : 0);
						CurrentSelectCol = newScrollboxIndex;
						CurrentSelectRow = OldIdx == 0 ? PoolWidgetGroups[newScrollboxIndex].Scrollbox->GetListLength() - 1 : 0;
					}
				}
			}
			else
			{
				DismissAllFocusEffect();
				PoolWidgetGroups[i].Scrollbox->SetSelectedIndex(NewIdx);
				CurrentSelectCol = i;
				CurrentSelectRow = NewIdx;
			}
		}
	}
}

bool UWWUIScreenCustomiseCreateComp::OnSystemEvent(WWUINodeProperty& eventParams)
{
	FString EventName = eventParams.GetStringProperty("system_event");

	if (EventName.Compare(GAME_DB_SAVE_OK_NAME) == 0 || EventName.Compare(GAME_DB_SAVE_FAIL_NAME) == 0)
	{
		Exit(nullptr);
	}

	return false;
}

void UWWUIScreenCustomiseCreateComp::OnInFocus()
{
#if PLATFORM_SWITCH	
	SIFGameHelpers::GARequestCameraTransition(RugbyUIWindowNames::RUUI_COMPETITION_SELECT_WINDOW_NAME);
#endif
	LoadSettingsFromDatabase();
	OnTabChanged();
	BindPCTextEntry();
}


void UWWUIScreenCustomiseCreateComp::OnOutFocus(bool ShouldOutFocus)
{
}


void UWWUIScreenCustomiseCreateComp::OnBack(APlayerController* OwningPlayer)
{
	if (TeamTabState == ETeamTabState::TEAM_SELECT || TeamTabState == ETeamTabState::MOVE)
	{
		SetTeamTabState(ETeamTabState::NORMAL);
	}
	else
	{
		if (PC_KEYBOARD_FOCUS)
		{
			UnbindPCTextEntry();
			ExitTextEntry(OwningPlayer);
			return;
		}

		/// Launch save or exit pop up.
 		UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();
 		TArray<FModalButtonInfo> ButtonData;

		FWWUIModalDelegate saveDelegate;
		saveDelegate.BindUObject(this, &UWWUIScreenCustomiseCreateComp::SaveAndExit);
 		ButtonData.Add(FModalButtonInfo(FText::FromString(UWWUITranslationManager::Translate("[ID_CUSTOMISE_PLAYER_BUTTON_SAVE]")), saveDelegate));
 
		FWWUIModalDelegate exitDelegate;
		exitDelegate.BindUObject(this, &UWWUIScreenCustomiseCreateComp::Exit);
		ButtonData.Add(FModalButtonInfo(FText::FromString(UWWUITranslationManager::Translate("[ID_CUSTOMISE_PLAYER_BUTTON_NO]")), exitDelegate));

		

 		modalData->WarningDialogue = "[ID_CUSTOMISE_PLAYER_POPUP_MAIN_TEXT]";
 		modalData->LegendString = "[ID_POPUP_HELPTEXT_CANCEL]";
 		modalData->ButtonData = ButtonData;
 
 		modalData->CloseOnBackButton = true;
 		modalData->CloseOnSelectButton = true;
 
 		if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
 		{
 			pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
 		}
	}
}


//===============================================================================
//===============================================================================
void UWWUIScreenCustomiseCreateComp::OnSelect(APlayerController* OwningPlayer)
{
	if (PC_KEYBOARD_FOCUS && TextEntryBox)
	{
		if (UWWUIEditableTextBox* EditableTextBox = Cast<UWWUIEditableTextBox>(UWWUIFunctionLibrary::FindChildWidget(TextEntryBox, WWUIScreenCustomiseCreateComp_UI::EditableTextBoxSearchTextEntry)))
		{
			FText textEnteredString = EditableTextBox->GetText();
			EditableTextBox->CommitTextManual(textEnteredString, ETextCommit::OnEnter);
		}
	}
}

void UWWUIScreenCustomiseCreateComp::OnLeft(APlayerController* OwningPlayer)
{
	IncrementOption(-1);
}


void UWWUIScreenCustomiseCreateComp::OnRight(APlayerController* OwningPlayer)
{
	IncrementOption(1);
}


void UWWUIScreenCustomiseCreateComp::OnLeftTrigger(APlayerController* OwningPlayer)
{
	if (m_currentTab != ECompetitionCustomiseTab::TEAMS) return;
	if (TeamTabState == ETeamTabState::TEAM_SELECT)
	{
		IncrementCompFilter(-1);
		UpdateHelpText();
	}
}

void UWWUIScreenCustomiseCreateComp::OnRightTrigger(APlayerController* OwningPlayer)
{
	if (m_currentTab != ECompetitionCustomiseTab::TEAMS) return;
	if (TeamTabState == ETeamTabState::TEAM_SELECT)
	{
		IncrementCompFilter(1);
		UpdateHelpText();
	}
}

void UWWUIScreenCustomiseCreateComp::OnSwitchTriggerAxis(float AxisValue, APlayerController* OwningPlayer)
{
	//< Update Switch Tab Delay >
	if (AxisValue < 0.0f || AxisValue > 0.0f)
	{
		if (AxisValue < 0.0f)		OnLeftTrigger(OwningPlayer);
		else if (AxisValue > 0.0f)	OnRightTrigger(OwningPlayer);

		SwitchTabReadyTimerHandle	= UWWUIFunctionLibrary::OnTimer(1.0f, FTimerDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::ResetTabReadyTimer), false);
		bIsSwitchTabReady			= false;
	}
}

void UWWUIScreenCustomiseCreateComp::ResetTabReadyTimer()
{
	if (SwitchTabReadyTimerHandle.IsValid())
	{
		UWWUIFunctionLibrary::StopTimer(SwitchTabReadyTimerHandle);
		bIsSwitchTabReady = true;
	}
}

void UWWUIScreenCustomiseCreateComp::IncrementOption(int _dir)
{
	switch (m_currentTab)
	{
		case ECompetitionCustomiseTab::DETAILS:
			switch ((ECompetitionDetailsOptions)TabDefaultScrollBoxes[m_currentTab]->GetSelectedIndex())
			{
				case ECompetitionDetailsOptions::LOGO:			OnLogoChanged(_dir);			break;
				case ECompetitionDetailsOptions::TROPHY:		OnTrophyChanged(_dir);			break;
				//case ECompetitionDetailsOptions::LAW_VARIATION: OnLawVariationChanged(_dir);	break; Nick GG Remove Law Variation for RL
				case ECompetitionDetailsOptions::GENDER:		OnGenderChanged(_dir);			break;
				default:
					break;
			}
			break;
		case ECompetitionCustomiseTab::FORMAT:
			switch ((ECompetitionFormatOptions)TabDefaultScrollBoxes[m_currentTab]->GetSelectedIndex())
			{
				// Nick GG Remove Game mode for RL
				//case ECompetitionFormatOptions::GAME_MODE:		OnGameModeChanged(_dir);		break;
				case ECompetitionFormatOptions::ROUND_ROBIN:	OnRoundRobinChanged(_dir);		break;
				case ECompetitionFormatOptions::POOL_COUNT:		OnPoolCountChanged(_dir);		break;
				case ECompetitionFormatOptions::TEAM_COUNT:		OnTeamCountChanged(_dir);		break;
				case ECompetitionFormatOptions::PLAY_EACH:		OnPlayEachChanged(_dir);		break;
				case ECompetitionFormatOptions::FINALS:			OnFinalsChanged(_dir);			break;
				case ECompetitionFormatOptions::ADVANCE_A:		OnAdvanceAChanged(_dir);		break;
				case ECompetitionFormatOptions::ADVANCE_B:		OnAdvanceBChanged(_dir);		break;
				case ECompetitionFormatOptions::GRAND_FINAL:	OnGrandFinalChanged(_dir);		break;
				default:
					break;
			}
			break;
		case ECompetitionCustomiseTab::POINTS:
			OnPointsChanged(_dir, TabDefaultScrollBoxes[m_currentTab]);
			break;
		case ECompetitionCustomiseTab::TEAMS:
		{
			//< Find currently focused scroll box. >
			for (int i = 0; i < PoolWidgetGroups.Num(); i++)
			{
				if (UWWUIScrollBox* poolScrollBox = PoolWidgetGroups[i].Scrollbox)
				{
					for (int f = 0; f < poolScrollBox->GetListLength(); f++)
					{
						if (poolScrollBox->GetListField(f)->GetButtonWidget()->HasAnyUserFocus())
						{
							//< Navigate horizontally across the scroll boxes. >
							/// Calculate new scroll box index.
							int newScrollboxIndex = FMath::Clamp(i + _dir, (i >= 4) ? 4 : 0, (i >= 4) ? 7 : 3);

							/// Attempt to move across.
							if (PoolWidgetGroups.IsValidIndex(newScrollboxIndex))
							{
								if (UWWUIScrollBox* nextPoolScrollBox = PoolWidgetGroups[newScrollboxIndex].Scrollbox)
								{
									if (nextPoolScrollBox->GetListLength() >= f)
									{
										if (newScrollboxIndex != i)
											DismissAllFocusEffect();
										nextPoolScrollBox->SetSelectedIndex(f);
										CurrentSelectCol = newScrollboxIndex;
										CurrentSelectRow = f;
										return;
									}
								}
							}
						}
					}
				}
			}
		}
			break;
		default:
			break;
	}
}

void UWWUIScreenCustomiseCreateComp::IncrementCompFilter(int _dir)
{
	if (TeamListScrollbox)
	{
		if (UWWUIPopulatorTeamSelectList* pTeamSelectListPopulator = Cast<UWWUIPopulatorTeamSelectList>(TeamListScrollbox->GetPopulator()))
		{
			FString NewCompName	= pTeamSelectListPopulator->IncrementFilterCompetition(_dir);

			if (UWidget* pCompetitionCategorySelectorWidget = FindChildWidget(WWUIScreenCustomiseCreateComp_UI::CompetitionListSwitcher))
			{
				UTextBlock* pTitleText = Cast<UTextBlock>(FindChildOfTemplateWidget(pCompetitionCategorySelectorWidget, WWUIScreenCustomiseCreateComp_UI::TextCategoryName));
				SetWidgetText(pTitleText, FText::FromString(UWWUITranslationManager::Translate(NewCompName)));

				FString NumberText = FString::FromInt(pTeamSelectListPopulator->GetCurrentCompetitionIndex() + 1) + " / " + FString::FromInt(pTeamSelectListPopulator->GetCompetitionCount());

				UTextBlock* pNumCategoriesText = Cast<UTextBlock>(FindChildOfTemplateWidget(pCompetitionCategorySelectorWidget, WWUIScreenCustomiseCreateComp_UI::TextNumCategories));
				SetWidgetText(pNumCategoriesText, FText::FromString(NumberText));
			}

			TeamListScrollbox->PopulateAndRefresh();
			TeamListScrollbox->SetSelectedIndex(0);

			TeamDatabaseID = GetSelectedTeamData(TeamListScrollbox->GetSelectedIndex());
			UpdateTeamInfo();
		}
	}
}

void UWWUIScreenCustomiseCreateComp::IncrementTab(APlayerController* OwningPlayer)
{
	if (TeamTabState == ETeamTabState::TEAM_SELECT) return;

	if (UWWUITabSwitcher* pTabSwitcher = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::BP_TabContainer)))
	{
		pTabSwitcher->IncrementTab();
		m_currentTab = (ECompetitionCustomiseTab)pTabSwitcher->GetActiveTabID();
	}

	OnTabChanged();
}


void UWWUIScreenCustomiseCreateComp::DecrementTab(APlayerController* OwningPlayer)
{
	if (TeamTabState == ETeamTabState::TEAM_SELECT) return;

	if (UWWUITabSwitcher* pTabSwitcher = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::BP_TabContainer)))
	{
		pTabSwitcher->DecrementTab();
		m_currentTab = (ECompetitionCustomiseTab)pTabSwitcher->GetActiveTabID();
	}

	OnTabChanged();
}


bool UWWUIScreenCustomiseCreateComp::SaveAndExit(APlayerController* OwningPlayer)
{
	SIFGameHelpers::GACustomCompetitionApplyNumOfTeams();
	SIFGameHelpers::GARemoveInvalidCustomCompetitionTeams();

	//< We should only be able to save if we have valid teams. >
	if (!SIFGameHelpers::GAIsCustomCompetitionTeamsValid())
	{
		/// Teams are invalid, we need to tell the user.
		UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();
		TArray<FModalButtonInfo> ButtonData;
		ButtonData.Add(FModalButtonInfo(UWWUITranslationManager::Translate(FString("[ID_POPUP_OK]"))));

		modalData->WarningDialogue = "[ID_CUSTOM_COMP_INVALID_TEAMS_POPUP_TEXT]";
		modalData->LegendString = "[ID_POPUP_HELP_TEXT_ACCEPT]";
		modalData->ButtonData = ButtonData;

		modalData->CloseOnBackButton = false;
		modalData->CloseOnSelectButton = true;

		if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
		{
			pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
		}
	}
	else if (!Validate())
	{
		//< Popup for this is handled in the Validate method. >
	}
	else
	{
		SIFUIHelpers::ShowSavingOverlay(true, true);
		SIFGameHelpers::GASaveCustomCompetition();
	}

	return true;
}

bool UWWUIScreenCustomiseCreateComp::Exit(APlayerController* OwningPlayer)
{
	SIFApplication::GetApplication()->DealMenuAction(SCREEN_CANCEL, Screens_UI::WarningMessage);
	SIFApplication::GetApplication()->DealMenuAction(SCREEN_CANCEL, Screens_UI::CustomiseCreateComp);
	SIFApplication::GetApplication()->DealMenuAction(SCREEN_CANCEL_FADE, Screens_UI::CustomiseCompTemplate);

	SIFGameHelpers::GACancelCurrentCustomCompetition();
	SIFGameHelpers::GACleanupCompetitionCustomisationHelper();
	return true;
}


void UWWUIScreenCustomiseCreateComp::OnOpenTextEntry(APlayerController* OwningPlayer)
{
	//< Only continue if selected correct field. >
	if ((m_currentTab != ECompetitionCustomiseTab::DETAILS) || TabDefaultScrollBoxes[m_currentTab]->GetSelectedIndex() != (int)ECompetitionDetailsOptions::NAME)
		return;

#if !PLATFORM_WINDOWS //< PS4, XBONE & SWITCH >
	if (UWWUIListField* TempListField = TabDefaultScrollBoxes[ECompetitionCustomiseTab::DETAILS]->GetListField(TabDefaultScrollBoxes[m_currentTab]->GetSelectedIndex()))
	{
		if (UWWUIEditableText* pEnterTextBox = Cast<UWWUIEditableText>(UWWUIFunctionLibrary::FindChildWidget(TempListField, WWUIScreenCustomiseCreateComp_UI::EditableTextField)))
		{
			if (pEnterTextBox->EnterTextEditMode(OwningPlayer))
			{
				BindConsoleTextEntry();

				SIFUIHelpers::ListenToNoControllers();
			}
		}	
	}
#else //< PC >
	if (TextEntryBox && !PC_KEYBOARD_FOCUS) 
	{
		TextEntryBox->SetVisibility(ESlateVisibility::Visible);
		if (UWWUIEditableTextBox* EditableTextBox = Cast<UWWUIEditableTextBox>(UWWUIFunctionLibrary::FindChildWidget(TextEntryBox, WWUIScreenCustomiseCreateComp_UI::EditableTextBoxSearchTextEntry)))
		{
			if (EditableTextBox->EnterTextEditMode(OwningPlayer))
			{
				BindPCTextEntry();
			}

			/// Load first field.
			UWWUIListField* CompNameField = TabDefaultScrollBoxes[ECompetitionCustomiseTab::DETAILS]->GetListField((int)ECompetitionDetailsOptions::NAME);
			if (!CompNameField) return;

			/// Retreive it's editable text component.
			UWWUIEditableText* EditableTextComponent = Cast<UWWUIEditableText>(UWWUIFunctionLibrary::FindChildWidget(CompNameField, WWUIScreenCustomiseCreateComp_UI::EditableTextField));
			if (!EditableTextComponent) return;

			/// Apply text to pop up & block unwanted inputs.
			EditableTextBox->SetText(EditableTextComponent->GetText());
			PC_KEYBOARD_FOCUS = true;
		}

		SIFAudioHelpers::PlayInputSoundEvent("event:/ui/help_tips/open_popup");
	}
#endif
}

void UWWUIScreenCustomiseCreateComp::OnPCConfirmTextEntry(const FText& _Text, ETextCommit::Type _CommitMethod)
{
	if (_CommitMethod == ETextCommit::OnCleared)
	{
		//< Return out, allowing the screen to deal with this on key UP. >
		return;
	}

	UnbindPCTextEntry();
	ApplyEnteredText(_Text.ToString());
}

void UWWUIScreenCustomiseCreateComp::OnConsoleConfirmTextEntry(const FText& _Text, ETextCommit::Type _CommitMethod)
{
	if (_CommitMethod == ETextCommit::OnCleared)
	{
		//< Exit text entry. >
		UnbindConsoleTextEntry();
		ExitTextEntry(nullptr);
		return;
	}

	UnbindConsoleTextEntry();
	ApplyEnteredText(_Text.ToString());
}

void UWWUIScreenCustomiseCreateComp::HandleTextChangedFilterResult(FString _String, EWWUITextFilterResult _FilterResult)
{
	HandleTextFilterResult(_String, _FilterResult);
}

void UWWUIScreenCustomiseCreateComp::HandleTextCommittedFilterResult(FString _String, EWWUITextFilterResult _FilterResult, ETextCommit::Type _CommitMethod)
{
	if (_CommitMethod == ETextCommit::Default) return;

	HandleTextFilterResult(_String, _FilterResult);
}

void UWWUIScreenCustomiseCreateComp::HandleTextFilterResult(FString _String, EWWUITextFilterResult _FilterResult)
{
	SIFUIHelpers::ListenToAllControllers();
	UnbindPCTextEntry();

	switch (_FilterResult)
	{
#if !PLATFORM_WINDOWS //< PS4, XBONE & SWITCH >
	case EWWUITextFilterResult::PROFANITY:	SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::OFFENSIVE_LANGUAGE,	FWWUIModalDelegate::CreateLambda([](APlayerController* OwningController) { SIFUIHelpers::ListenToNoControllers(); return true; }));		break;
	default:								SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::INVALID_NAME,			FWWUIModalDelegate::CreateLambda([](APlayerController* OwningController) { SIFUIHelpers::ListenToNoControllers(); return true; }));		break;
#else //< PC >
	case EWWUITextFilterResult::PROFANITY:	SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::OFFENSIVE_LANGUAGE,	FWWUIModalDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::ReopenTextEntry));	break;
	default:								SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::INVALID_NAME,			FWWUIModalDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::ReopenTextEntry));	break;
#endif
	}
}

void UWWUIScreenCustomiseCreateComp::ApplyEnteredText(FString _Text)
{
	MabString NewText = SIFGameHelpers::GAConvertFStringToMabString(_Text);

	//< Remove invisible characters. >
	UWWUIEditableText* EditableTextComponent = Cast<UWWUIEditableText>(UWWUIFunctionLibrary::FindChildWidget(TabDefaultScrollBoxes[ECompetitionCustomiseTab::DETAILS]->GetListField(0), WWUIScreenCustomiseCreateComp_UI::EditableTextField));
	if (!EditableTextComponent) return;

	UFont* EditableTextFont = const_cast<UFont*>(Cast<UFont>(EditableTextComponent->WidgetStyle.Font.FontObject));
	if (EditableTextFont) NewText = SIFGameHelpers::GAFixName2(NewText, EditableTextFont);

	//< Apply text change if valid. >
	if (NewText != "" && SIFGameHelpers::GACareerManagerNameIsValid(NewText.c_str(), true))
	{
		//< Apply text change. >
		FString UnrealNameString = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAFixName(NewText.c_str()));
		EditableTextComponent->SetText(FText::FromString(UnrealNameString));
		ExitTextEntry(nullptr);

		SIFGameHelpers::GASetCustomCompetitionName(NewText.c_str());
	}
	else
	{
		SIFUIHelpers::ListenToAllControllers();
#if !PLATFORM_WINDOWS //< PS4, XBONE & SWITCH >
		SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::INVALID_NAME, FWWUIModalDelegate::CreateLambda([](APlayerController* OwningController) { SIFUIHelpers::ListenToNoControllers(); return true; }));
#else //< PC >
		SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::INVALID_NAME, FWWUIModalDelegate::CreateUObject(this, &UWWUIScreenCustomiseCreateComp::ReopenTextEntry));
#endif
	}
}


bool UWWUIScreenCustomiseCreateComp::ExitTextEntry(APlayerController* OwningPlayer)
{
	PC_KEYBOARD_FOCUS = false;
	SIFAudioHelpers::PlayInputSoundEvent("event:/ui/help_tips/close_popup");
	SIFUIHelpers::ListenToAllControllers();
	TabDefaultScrollBoxes[ECompetitionCustomiseTab::DETAILS]->SetSelectedIndex((int)ECompetitionDetailsOptions::NAME);

	UWWUIFunctionLibrary::SetVisibility(TextEntryBox, ESlateVisibility::Hidden);
	return true;
}

bool UWWUIScreenCustomiseCreateComp::ReopenTextEntry(APlayerController* OwningPlayer)
{
	PC_KEYBOARD_FOCUS = false;

	OnOpenTextEntry(SIFApplication::GetApplication()->GetMasterPlayerController());
	return true;
}

void UWWUIScreenCustomiseCreateComp::BindPCTextEntry()
{
#if PLATFORM_WINDOWS //< PC >
	if (UWWUIEditableTextBox* EditableTextBox = Cast<UWWUIEditableTextBox>(UWWUIFunctionLibrary::FindChildWidget(TextEntryBox, WWUIScreenCustomiseCreateComp_UI::EditableTextBoxSearchTextEntry)))
	{
		if (!EditableTextBox->OnTextCommitted.Contains(this, FName("OnPCConfirmTextEntry")))
		{
			EditableTextBox->OnTextCommitted.AddDynamic(this, &UWWUIScreenCustomiseCreateComp::OnPCConfirmTextEntry);
		}
		if (!EditableTextBox->GetTextCommittedFilterCallback().IsBound())
		{
			EditableTextBox->GetTextCommittedFilterCallback().BindUObject(this, &UWWUIScreenCustomiseCreateComp::HandleTextCommittedFilterResult);
		}
	}
#endif
}


void UWWUIScreenCustomiseCreateComp::UnbindPCTextEntry()
{
#if PLATFORM_WINDOWS //< PC >
	if (UWWUIEditableTextBox* EditableTextBox = Cast<UWWUIEditableTextBox>(UWWUIFunctionLibrary::FindChildWidget(TextEntryBox, WWUIScreenCustomiseCreateComp_UI::EditableTextBoxSearchTextEntry)))
	{
		if (EditableTextBox->OnTextCommitted.Contains(this, FName("OnPCConfirmTextEntry")))
		{
			EditableTextBox->OnTextCommitted.RemoveDynamic(this, &UWWUIScreenCustomiseCreateComp::OnPCConfirmTextEntry);
		}
		if (EditableTextBox->GetTextCommittedFilterCallback().IsBound())
		{
			EditableTextBox->GetTextCommittedFilterCallback().Unbind();
		}
	}
#endif
}

void UWWUIScreenCustomiseCreateComp::BindConsoleTextEntry()
{
#if !PLATFORM_WINDOWS //< XBOX, PS4, SWITCH >
	if (UWWUIListField* TempListField = TabDefaultScrollBoxes[ECompetitionCustomiseTab::DETAILS]->GetListField(TabDefaultScrollBoxes[m_currentTab]->GetSelectedIndex()))
	{
		if (UWWUIEditableText* pEnterTextBox = Cast<UWWUIEditableText>(UWWUIFunctionLibrary::FindChildWidget(TempListField, WWUIScreenCustomiseCreateComp_UI::EditableTextField)))
		{
			if (!pEnterTextBox->OnTextCommitted.Contains(this, FName("OnConsoleConfirmTextEntry")))
			{
				pEnterTextBox->OnTextCommitted.AddDynamic(this, &UWWUIScreenCustomiseCreateComp::OnConsoleConfirmTextEntry);
			}
			if (!pEnterTextBox->GetTextCommittedFilterCallback().IsBound())
			{
				pEnterTextBox->GetTextCommittedFilterCallback().BindUObject(this, &UWWUIScreenCustomiseCreateComp::HandleTextCommittedFilterResult);
			}
		}
	}
#endif
}

void UWWUIScreenCustomiseCreateComp::UnbindConsoleTextEntry()
{
	//Controller disconencted while keyboard was open would change focusing causing text entry to unbind
	//this is to prevent it from unbinding so when our controller comes back the text input still works
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (pRugbyGameInstance)
	{
		if (pRugbyGameInstance->GetIsVirtualKeyboardShowing())
		{
			return;
		}
	}

#if !PLATFORM_WINDOWS //< XBOX, PS4, SWITCH >
	if (UWWUIListField* TempListField = TabDefaultScrollBoxes[ECompetitionCustomiseTab::DETAILS]->GetListField(TabDefaultScrollBoxes[m_currentTab]->GetSelectedIndex()))
	{
		if (UWWUIEditableText* pEnterTextBox = Cast<UWWUIEditableText>(UWWUIFunctionLibrary::FindChildWidget(TempListField, WWUIScreenCustomiseCreateComp_UI::EditableTextField)))
		{
			if (pEnterTextBox->OnTextCommitted.Contains(this, FName("OnConsoleConfirmTextEntry")))
			{
				pEnterTextBox->OnTextCommitted.RemoveDynamic(this, &UWWUIScreenCustomiseCreateComp::OnConsoleConfirmTextEntry);
			}
			if (pEnterTextBox->GetTextCommittedFilterCallback().IsBound())
			{
				pEnterTextBox->GetTextCommittedFilterCallback().Unbind();
			}
		}
	}
#endif
}

void UWWUIScreenCustomiseCreateComp::OnTabChanged()
{
	TeamTabHightLightCleared = false;
	SIFGameHelpers::GACustomCompetitionApplyNumOfTeams();
	int SelectedIndex = 0;

	switch (m_currentTab)
	{
		case ECompetitionCustomiseTab::DETAILS:
		{
			UWWUIScrollBox* pScrollBox = TabDefaultScrollBoxes[m_currentTab];
			if (pScrollBox)
			{
				if (UWWUIListField* pListField = pScrollBox->GetListField((int32)ECompetitionDetailsOptions::GENDER))
				{
	#ifdef DISABLE_WOMENS
					pListField->SetVisibility(ESlateVisibility::Collapsed);
	#else
					pListField->SetIsEnabled(false);
	#endif
				}
			}
		}
		break;
		
		case ECompetitionCustomiseTab::FORMAT:
		{
			UWWUIScrollBox* pScrollBox = TabDefaultScrollBoxes[m_currentTab];
			if (pScrollBox)
			{
				// Nick GG Remove Game mode for RL
				/*
				if (UWWUIListField* pListField = pScrollBox->GetListField((int32)ECompetitionFormatOptions::GAME_MODE))
				{
					int gender = DetailsValueMap[ECompetitionDetailsOptions::GENDER];

					switch (gender)
					{
						case PLAYER_GENDER_FEMALE:
						{
							pListField->SetIsEnabled(false);
							SelectedIndex = 1;
						}
						break;
						default:
						{
							pListField->SetIsEnabled(true);
						}
						break;
					}
				}
				*/
			}
		}
		break;

		case ECompetitionCustomiseTab::TEAMS:
		{
			SIFGameHelpers::GARemoveInvalidCustomCompetitionTeams();
			RefreshTeamTab(true);
		}
		break;
	default:
		break;
	}

	TabDefaultScrollBoxes[m_currentTab]->SetSelectedIndex(SelectedIndex);
	UpdateHelpText();
}


void UWWUIScreenCustomiseCreateComp::OnMoveTeam(APlayerController* OwningPlayer)
{
	if (m_currentTab != ECompetitionCustomiseTab::TEAMS) return;

	if (TeamTabState == ETeamTabState::NORMAL)
	{
		/// Move Requested.
		SetTeamTabState(ETeamTabState::MOVE);
	}
	else if (TeamTabState == ETeamTabState::MOVE)
	{
		/// Found the one to swap it with.
		MoveSelection2 = GetSelectedTeamField();
		SwapTeams(MoveSelection1, MoveSelection2);
	}
}

void UWWUIScreenCustomiseCreateComp::OnAddRemoveTeam(APlayerController* OwningPlayer)
{
	if (m_currentTab != ECompetitionCustomiseTab::TEAMS) return;

	if (TeamTabState == ETeamTabState::NORMAL)
	{
		SetTeamTabState(ETeamTabState::TEAM_SELECT);
	}
}

void UWWUIScreenCustomiseCreateComp::OnAutofill(APlayerController* OwningPlayer)
{
	if (m_currentTab != ECompetitionCustomiseTab::TEAMS) return;

	// We need to gather all teams not currently in the comp.
	RUCompetitionCustomisationHelper* comp_helper = SIFApplication::GetApplication()->GetCompetitionCustomisationHelper();
	const RUDB_COMP_DEF* comp_definition = comp_helper->GetCurrentCompDefinition();

	TArray<int> PopulatedTeamIDs;
	TArray<int> AvailableTeamIDs;

	/// Gather all team IDs in comp
	for (size_t i = 0; i < comp_definition->teams.size(); ++i)
	{
		const RUDB_COMP_DEF_TEAM* team_def = &comp_definition->teams[i];
		PopulatedTeamIDs.Add(team_def->team_id);
	}

	int max_number_of_teams = SIFGameHelpers::GAGetNumberOfTeams();
	for (int i = 0; i < max_number_of_teams; i++)
	{
		int team_id = SIFGameHelpers::GAGetTeamIdFromIndex(i);
		if (team_id == DB_INVALID_ID)
			continue;

		bool is_in_list = PopulatedTeamIDs.Contains(team_id);
		bool add_team = !is_in_list;

		if (SIFGameHelpers::GAGetCustomCompetitionR7Exclusive() != SIFGameHelpers::GAIsTeamR7(team_id))
		{
			add_team = false;
		}

		RL3DB_TEAM db_team((unsigned short)team_id);
		unsigned short team_gender_permissions = db_team.GetGenderPermissionFlags();
		if ((team_gender_permissions & SIFGameHelpers::GAGetCustomCompetitionGenderPermissionFlags()) != team_gender_permissions)
		{
			add_team = false;
		}

		if (add_team)
		{
			AvailableTeamIDs.Add(team_id);
		}
	}

	TArray<UWWUIListField*> CurrentTeamFields = GetCurrentTeamsList();
	for (int i = 0; i < CurrentTeamFields.Num(); i++)
	{
		PopulateTeamID(CurrentTeamFields[i], AvailableTeamIDs);
	}

	RefreshTeamTab();
}

void UWWUIScreenCustomiseCreateComp::RefreshTeamTab(bool _Repopulate)
{
	SetTeamTabState(ETeamTabState::NORMAL);
	int num_of_teams = SIFGameHelpers::GAGetCustomCompetitionNumOfTeams();

	// We need to know how many pools we're dealing with because we must only show the number of pools requested.
	int num_of_pools = 1;
	if (strcmp(SIFGameHelpers::GAGetCustomCompetitionRoundRobin(), RUUI_CUSTOM_COMP_ROUND_ROBIN_POOLS) == 0 || strcmp(SIFGameHelpers::GAGetCustomCompetitionRoundRobin(), RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE) == 0)
		num_of_pools = SIFGameHelpers::GAGetCustomCompetitionNumPools();

	// We need to hide all the pools pages that don't correspond to this one.
	switch (num_of_pools)
	{
	case 1: 
	{
		SetNumVisiblePoolHeaders(num_of_pools, 0);
		SetNumVisiblePoolHeaders(0, 1);

		SetNumVisiblePoolScrollboxes(3, 0);
		SetNumVisiblePoolScrollboxes(0, 1);

		PopulatePool(1, TArray<int>({ 1, 2, 3 }), !_Repopulate);
	}
	break;
	case 2:
	{
		SetNumVisiblePoolHeaders(num_of_pools,	0);
		SetNumVisiblePoolHeaders(0,				1);

		SetNumVisiblePoolScrollboxes(4, 0);
		SetNumVisiblePoolScrollboxes(0, 1);

		PopulatePool(1, TArray<int>({ 1, 2 }), !_Repopulate);
		PopulatePool(2, TArray<int>({ 3, 4 }), !_Repopulate);
	}
	break;
	case 3:
	case 4:
	{
		SetNumVisiblePoolHeaders(num_of_pools, 0);
		SetNumVisiblePoolHeaders(0, 1);

		SetNumVisiblePoolScrollboxes(num_of_pools, 0);
		SetNumVisiblePoolScrollboxes(0, 1);

		for (int i = 1; i < num_of_pools+1; i++)
		{
			PopulatePool(i, TArray<int>({ i }), !_Repopulate);
		}


	}
	break;
	default:
	{
		int topPoolsCount		= GetTeamCountPerColumn(num_of_pools, 1, 2);
		int bottomPoolsCount	= GetTeamCountPerColumn(num_of_pools, 2, 2);

		SetNumVisiblePoolHeaders(topPoolsCount,		0);
		SetNumVisiblePoolHeaders(bottomPoolsCount,	1);

		SetNumVisiblePoolScrollboxes(topPoolsCount,		0);
		SetNumVisiblePoolScrollboxes(bottomPoolsCount,	1);

		for (int i = 1; i < topPoolsCount + 1; i++)
			PopulatePool(i, TArray<int>({ i }), !_Repopulate);

		for (int i = 1; i < bottomPoolsCount + 1; i++)
			PopulatePool( i + topPoolsCount, TArray<int>({ i + 4 }), !_Repopulate);
	}
		break;
	}

	if (TeamTabHightLightCleared)
		return;
	// Without this, all the fields are highlighted
	for (int pool_iter = 0; pool_iter < PoolWidgetGroups.Num(); pool_iter++)
	{
		if (PoolWidgetGroups.IsValidIndex(pool_iter))
		{
			for (int list_iter = 0; list_iter < PoolWidgetGroups[pool_iter].Scrollbox->GetListLength(); list_iter++)
			{
				PoolWidgetGroups[pool_iter].Scrollbox->SetSelectedIndex(list_iter);
			}
		}
	}
	TeamTabHightLightCleared = true;
}

void UWWUIScreenCustomiseCreateComp::SetNumVisiblePoolScrollboxes(int _Num, int _Row)
{
	int min_index = (_Row == 0) ? 0 : 4;
	int max_index = (_Row == 0) ? 4 : 8;

	int NumVisible = 0;
	for (int i = min_index; i < max_index; i++)
	{
		if (PoolWidgetGroups.IsValidIndex(i))
		{
			if (NumVisible < _Num)
			{
				UWWUIFunctionLibrary::SetVisibility(PoolWidgetGroups[i].Scrollbox, ESlateVisibility::Visible);
				NumVisible++;
			}
			else
			{
				UWWUIFunctionLibrary::SetVisibility(PoolWidgetGroups[i].Scrollbox, ESlateVisibility::Collapsed);
			}
		}
	}
}

void UWWUIScreenCustomiseCreateComp::SetNumVisiblePoolHeaders(int _Num, int _Row)
{
	int min_index = (_Row == 0) ? 0 : 4;
	int max_index = (_Row == 0) ? 4 : 8;

	int NumVisible = 0;
	for (int i = min_index; i < max_index; i++)
	{
		if (PoolWidgetGroups.IsValidIndex(i))
		{
			if (NumVisible < _Num)
			{
				UWWUIFunctionLibrary::SetVisibility(PoolWidgetGroups[i].Header, ESlateVisibility::Visible);
				NumVisible++;
			}
			else
			{
				UWWUIFunctionLibrary::SetVisibility(PoolWidgetGroups[i].Header, ESlateVisibility::Collapsed);
			}
		}
	}
}

int UWWUIScreenCustomiseCreateComp::GetTeamCountPerColumn(int _TeamCount, int _CurCol, int _MaxCol)
{
	return (_TeamCount / _MaxCol) + ((_CurCol <= _TeamCount % _MaxCol) ? 1 : 0);
}

void UWWUIScreenCustomiseCreateComp::PopulatePool(int _PoolIndex, TArray<int> ScrollboxIndexes, bool _Refresh)
{
	// Figure out number of teams in pool.
	int num_of_pools = 1;
	if (strcmp(SIFGameHelpers::GAGetCustomCompetitionRoundRobin(), RUUI_CUSTOM_COMP_ROUND_ROBIN_POOLS) == 0 || strcmp(SIFGameHelpers::GAGetCustomCompetitionRoundRobin(), RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE) == 0)
		num_of_pools = SIFGameHelpers::GAGetCustomCompetitionNumPools();
	int num_of_teams = GetTeamCountPerColumn(SIFGameHelpers::GAGetCustomCompetitionNumOfTeams(), _PoolIndex, num_of_pools);

	int populatedTeams = 0;
	for (int i = 0; i < ScrollboxIndexes.Num(); i++)
	{
		if (PoolWidgetGroups.IsValidIndex(ScrollboxIndexes[i] - 1))
		{
			if (UWWUIPopulatorCompPool* pop = PoolWidgetGroups[ScrollboxIndexes[i] - 1].Populator)
			{
				// Set pool id property.
				PoolWidgetGroups[ScrollboxIndexes[i] - 1].Scrollbox->SetProperty(COMP_POOL_PROPERTY_POOL_INDEX, &_PoolIndex, PROPERTY_TYPE_INT);

				// Calculate number of teams per scroll box.
				int scrollboxTeamCount = GetTeamCountPerColumn(num_of_teams, i+1, ScrollboxIndexes.Num());

				// Populate scroll boxes with correct team ids.
				pop->UpdateValues(_PoolIndex, GenerateTeamIndexesForScrollbox(_PoolIndex, populatedTeams, populatedTeams + scrollboxTeamCount), GenerateFieldIndexesForScrollbox(_PoolIndex, populatedTeams, populatedTeams + scrollboxTeamCount));
				if (_Refresh )
					PoolWidgetGroups[ScrollboxIndexes[i] - 1].Scrollbox->Refresh();
				else
					PoolWidgetGroups[ScrollboxIndexes[i] - 1].Scrollbox->Populate();
				populatedTeams += scrollboxTeamCount;
			}
		}
	}
}


TArray<int> UWWUIScreenCustomiseCreateComp::GenerateTeamIndexesForScrollbox(int _PoolIndex, int _TeamMinIndex, int _TeamMaxIndex)
{
	TArray<int> TeamsIndexesInPool;
	TArray<int> TeamIndexesInScrollbox;
	RUCompetitionCustomisationHelper* comp_helper = SIFApplication::GetApplication()->GetCompetitionCustomisationHelper();
	const RUDB_COMP_DEF* comp_definition = comp_helper->GetCurrentCompDefinition();

	// Gather all team indexes in pool.
	for (size_t i = 0; i < comp_definition->teams.size(); ++i)
	{
		const RUDB_COMP_DEF_TEAM* team = &comp_definition->teams[i];
		if (team->pool_or_conference == _PoolIndex-1) TeamsIndexesInPool.Add(i);
	}

	for (int i = _TeamMinIndex; i < TeamsIndexesInPool.Num(); i++)
	{
		if (i < _TeamMaxIndex) TeamIndexesInScrollbox.Add(TeamsIndexesInPool[i]);
	}


	return TeamIndexesInScrollbox;
}

TArray<int> UWWUIScreenCustomiseCreateComp::GenerateFieldIndexesForScrollbox(int _PoolIndex, int _TeamMinIndex, int _TeamMaxIndex)
{
	TArray<int> TeamsIndexesInPool;
	TArray<int> TeamIndexesInScrollbox;
	RUCompetitionCustomisationHelper* comp_helper = SIFApplication::GetApplication()->GetCompetitionCustomisationHelper();
	const RUDB_COMP_DEF* comp_definition = comp_helper->GetCurrentCompDefinition();

	// Gather all team indexes in pool.
	for (size_t i = 0; i < comp_definition->teams.size(); ++i)
	{
		const RUDB_COMP_DEF_TEAM* team = &comp_definition->teams[i];
		if (team->pool_or_conference == _PoolIndex - 1) TeamsIndexesInPool.Add(i);
	}

	for (int i = _TeamMinIndex; i < TeamsIndexesInPool.Num(); i++)
	{
		if (i < _TeamMaxIndex) TeamIndexesInScrollbox.Add(i + 1);
	}

	return TeamIndexesInScrollbox;
}

void UWWUIScreenCustomiseCreateComp::SwapTeams(UWWUIListField* _TeamA, UWWUIListField* _TeamB)
{
	if (!_TeamA || !_TeamB) return;

	int team_a_index	= _TeamA->GetIntProperty(COMP_POOL_PROPERTY_TEAM_INDEX);
	int team_a_pool		= GetPoolIndexFromListField(_TeamA);

	int team_b_index	= _TeamB->GetIntProperty(COMP_POOL_PROPERTY_TEAM_INDEX);
	int team_b_pool		= GetPoolIndexFromListField(_TeamB);

	SIFGameHelpers::GASwapCustomCompetitionTeam(team_a_index, team_a_pool, team_b_index, team_b_pool);

	// Update the lists so we can see the changes.
	RefreshTeamTab();

	if (team_a_index != team_b_index)
	{
		// Play FMODPlaySoundEvent("RUAudio/ui/manage_squad/swap_players")
	}
}

int UWWUIScreenCustomiseCreateComp::GetPoolIndexFromListField(UWWUIListField* _ListField)
{
	for (int i = 0; i < PoolWidgetGroups.Num(); i++)
	{
		if (UWWUIScrollBox* poolScrollBox = PoolWidgetGroups[i].Scrollbox)
		{
			for (int f = 0; f < poolScrollBox->GetListLength(); f++)
			{
				if (_ListField == poolScrollBox->GetListField(f))
				{
					return poolScrollBox->GetIntProperty(COMP_POOL_PROPERTY_POOL_INDEX) - 1;
				}
			}
		}
	}

	return 0;
}

UWWUIListField* UWWUIScreenCustomiseCreateComp::GetSelectedTeamField()
{
	for (int i = 0; i < PoolWidgetGroups.Num(); i++)
	{
		if (UWWUIScrollBox* poolScrollBox = PoolWidgetGroups[i].Scrollbox)
		{
			for (int f = 0; f < poolScrollBox->GetListLength(); f++)
			{
				if (poolScrollBox->GetListField(f)->GetButtonWidget()->HasAnyUserFocus())
				{
					return poolScrollBox->GetListField(f);
				}
			}
		}
	}

	return nullptr;
}

void UWWUIScreenCustomiseCreateComp::SetTeamTabState(ETeamTabState _NewState)
{
	ETeamTabState oldState = TeamTabState;
	TeamTabState = _NewState;

	/// If new state == MOVE, activate the highlight.

	if (_NewState == ETeamTabState::MOVE)
	{
		/// Activate Highlight on the first Move Selection.
		MoveSelection1 = GetSelectedTeamField();
		UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(MoveSelection1, FString("Highlight")), ESlateVisibility::Visible);
		UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(MoveSelection1, FString("HighlightText")), ESlateVisibility::Visible);

	}
	else if (_NewState == ETeamTabState::TEAM_SELECT)
	{
		/// First check if the selected item has a team already.
		UWWUIListField* selected_field = GetSelectedTeamField();
		int selected_team_id = selected_field->GetIntProperty(COMP_POOL_PROPERTY_TEAM_ID);
		if (selected_team_id == 0)
		{
			MoveSelection1 = GetSelectedTeamField();
			DisplayTeamSelect();
			//MenuSoundMenuSelect();
		}
		else
		{
			//< Tell the custom comp helper about the change. >
			int team_index = selected_field->GetIntProperty(COMP_POOL_PROPERTY_TEAM_INDEX);
			int team_pool = GetPoolIndexFromListField(selected_field);
			SIFGameHelpers::GAReplaceCustomCompetitionTeam(team_index, team_pool, 0);

			//< Don't change state into team_select. >
			TeamTabState = oldState;

			//< Refresh the list. >
			RefreshTeamTab();
			//MenuSoundBackCurrent();
		}
	}

	if (oldState == ETeamTabState::MOVE)
	{
		/// We're no longer selected a node to move, so disable highlight.
		UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(MoveSelection1, FString("Highlight")), ESlateVisibility::Collapsed);
		UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(MoveSelection1, FString("HighlightText")), ESlateVisibility::Collapsed);
		MoveSelection1 = nullptr;
	}

	if (oldState == ETeamTabState::TEAM_SELECT)
	{
		HideTeamSelect();
	}

	UpdateHelpText();
}

void UWWUIScreenCustomiseCreateComp::PopulateTeamID(UWWUIListField* _ListField, TArray<int>& _TeamIDs)
{
	if (!_ListField) return;

	int pool_num = GetPoolIndexFromListField(_ListField);
	int team_id = _ListField->GetIntProperty(COMP_POOL_PROPERTY_TEAM_ID);

	// Check if the field has no assigned team
	if (team_id == 0)
	{
		int random_index = 0;
		int chosen_value = 0;

		// Look for a random valid team to assign to the field
		while (chosen_value == 0 && _TeamIDs.Num() > 0)
		{
			random_index = FMath::RandRange(0, _TeamIDs.Num() - 1);
			chosen_value = _TeamIDs[random_index];

			if (!SIFGameHelpers::GAIsTeamAvailable(chosen_value))
			{
				chosen_value = 0;
				_TeamIDs.RemoveAt(random_index);
			}
		}

		// If no team was chosen, we don't have any available teams
		if (chosen_value == 0)
		{
			UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenCustomiseCreateComp::PopulateTeamID - No more teams in the _TeamIDs list"));
			return;
		}

		// Make team assignment for field
		int old_team_index = _ListField->GetIntProperty(COMP_POOL_PROPERTY_TEAM_INDEX);
		SIFGameHelpers::GAReplaceCustomCompetitionTeam(old_team_index, pool_num, chosen_value);
		_TeamIDs.RemoveAt(random_index);
	}
}

TArray<UWWUIListField*> UWWUIScreenCustomiseCreateComp::GetCurrentTeamsList()
{
	TArray<UWWUIListField*> CurrentListFields;

	for (int i = 0; i < PoolWidgetGroups.Num(); i++)
	{
		if (UWWUIScrollBox* sb = PoolWidgetGroups[i].Scrollbox)
		{
			if (sb->GetVisibility() != ESlateVisibility::Collapsed)
			{
				for (int f = 0; f < sb->GetListLength(); f++)
				{
					CurrentListFields.Add(sb->GetListField(f));
				}
			}
		}
	}

	CurrentListFields.StableSort(FSortTeamFields());
	return CurrentListFields;
}

int UWWUIScreenCustomiseCreateComp::GetSelectedTeamData(int32 NewIdx)
{
	if (UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::TeamListScrollbox)))
	{
		if (UWWUIListField* pSelectedListField = pTeamListScrollbox->GetListField(NewIdx))
		{
			// Grab the the team db id set up when the list was populated.
			return pSelectedListField->GetIntProperty("team_db_id");
		}
	}

	return 0;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseCreateComp::UpdateTeamInfo()
{
	if (TeamDatabaseID == 0)
	{
		return;
	}

	MabString LogoPath = SIFGameHelpers::GAGetTeamLogoAssetPath(TeamDatabaseID);

	UImage* pLogoImage = Cast<UImage>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::ImageTeamLogo));

	if (pLogoImage)
	{
		// The opposition is still TBD
		UTexture2D* pTexture = nullptr;

		FString name = FString(LogoPath.c_str());
		pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name));

		if (pTexture)
		{
			pLogoImage->SetBrushFromTexture(pTexture, true);
			pLogoImage->SetVisibility(ESlateVisibility::Visible);
		}
	}

	UImage* logoBackground = Cast<UImage>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::teamBkgd));
	SIFUIHelpers::SetImageColourFromTeamColour(logoBackground, TeamDatabaseID);

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		RUDB_TEAM db_team;
		RUGameDatabaseManager* database_manager = pRugbyGameInstance->GetGameDatabaseManager();

		if (database_manager)
		{
			database_manager->LoadData(db_team, TeamDatabaseID);
			MabColour team_colour;
			db_team.GetPrimaryColour(team_colour);

			UpdateTeamRating(db_team.GetNormaliseRanking() * 100.0f);

			UpdateTeamCreator(db_team);
		}
	}
}

void UWWUIScreenCustomiseCreateComp::UpdateTeamRating(float NewRating)
{
	//Update ranking
	FString rankingString = FString::Printf(TEXT("%.2f"), NewRating);
	SetWidgetText(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::TextRating), FText::FromString(rankingString));
}

//===============================================================================
//===============================================================================
void UWWUIScreenCustomiseCreateComp::UpdateTeamCreator(RUDB_TEAM &db_team)
{
	UWidget* pCreatorBox = FindChildWidget(WWUIScreenCustomiseCreateComp_UI::HorizontalBoxCreator);
	if (pCreatorBox)
	{
#ifdef SHOW_UGC_CREATOR
		if (db_team.IsCustom() && SIFGameHelpers::GAGetTeamDownloadUser(db_team.GetDbId()) != "")
		{
			pCreatorBox->SetVisibility(ESlateVisibility::Visible);
			FString creatorName = "";
			FString uploaderName = "";

			UTextBlock* pCreatedByText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCustomiseCreateComp_UI::TextCreator));
			if (pCreatedByText)
			{
				FString downloadInfo = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetTeamDownloadUser(db_team.GetDbId()));
				creatorName = downloadInfo;

				downloadInfo.Split(",", &creatorName, &uploaderName);

				FString CreatedByString = "[ID_CREATED_BY]: " + creatorName + "\n[ID_UPLOADED_BY]: " + uploaderName;
				SetWidgetText(pCreatedByText, FText::FromString(UWWUITranslationManager::Translate(CreatedByString)));
			}
		}
		else
#endif
		{
			pCreatorBox->SetVisibility(ESlateVisibility::Collapsed);
		}
	}
}
// Nick GG Remove Game mode for RL
/*
void UWWUIScreenCustomiseCreateComp::UpdateGameMode(bool seven_exclusive)
{
	SIFGameHelpers::GASetCustomCompetitionR7Exclusive(seven_exclusive);

	if (TeamListScrollbox)
	{
		if (UWWUIPopulatorTeamSelectList* pTeamSelectListPopulator = Cast<UWWUIPopulatorTeamSelectList>(TeamListScrollbox->GetPopulator()))
		{
			pTeamSelectListPopulator->SetTeamLimit(seven_exclusive ? FString("CUSTOM_SEVENS") : FString("RC3_TEAMS_NO_SEVENS"));
			pTeamSelectListPopulator->SetCustomGameMode(seven_exclusive ? FString("COMP_CUTSOMISE_SEVENS") : FString(""));
		}
	}
}
*/
void UWWUIScreenCustomiseCreateComp::UpdateGenderPermissions(unsigned char permission_flags)
{
	SIFGameHelpers::GASetCustomCompetitionGenderPermissionFlags(permission_flags);

	if (TeamListScrollbox)
	{
		if (UWWUIPopulatorTeamSelectList* pTeamSelectListPopulator = Cast<UWWUIPopulatorTeamSelectList>(TeamListScrollbox->GetPopulator()))
		{
			pTeamSelectListPopulator->SetGenderPermissionFlags(permission_flags);
		}
	}
}

void UWWUIScreenCustomiseCreateComp::DisplayTeamSelect()
{
	if (TeamListScrollbox)
	{
		TeamSelectWidget->SetVisibility(ESlateVisibility::Visible);
		if (UWWUIPopulatorTeamSelectList* pTeamSelectListPopulator = Cast<UWWUIPopulatorTeamSelectList>(TeamListScrollbox->GetPopulator()))
		{
			int comp_id = SIFGameHelpers::GAGetCustomCompetitionOriginalId();
			pTeamSelectListPopulator->Populate();
			pTeamSelectListPopulator->SelectDefaultComp(TeamListScrollbox->GetScrollBox(), comp_id);
			IncrementCompFilter(0);
		}
		TeamListScrollbox->PopulateAndRefresh();
		TeamListScrollbox->FocusFirstListField(SIFApplication::GetApplication()->GetPrimaryAccountPlayerController());
	}
}

void UWWUIScreenCustomiseCreateComp::HideTeamSelect()
{
	TeamSelectWidget->SetVisibility(ESlateVisibility::Hidden);
	MoveSelection1->GetButtonWidget()->SetUserFocus(GetWorld()->GetFirstPlayerController());
	MoveSelection1 = nullptr;
}


void UWWUIScreenCustomiseCreateComp::OnNameChanged(FString _newName)
{
	if (TabDefaultScrollBoxes.Contains(ECompetitionCustomiseTab::DETAILS))
	{
		if (UWWUIListField* NameListField = TabDefaultScrollBoxes[ECompetitionCustomiseTab::DETAILS]->GetListField((int)ECompetitionDetailsOptions::NAME))
		{
			if (UWWUIEditableText* EditableText = Cast<UWWUIEditableText>(UWWUIFunctionLibrary::FindChildWidget(NameListField, WWUIScreenCustomiseCreateComp_UI::EditableTextField)))
				EditableText->SetText(FText::FromString(_newName));
		}
	}
}


void UWWUIScreenCustomiseCreateComp::OnLogoChanged(int _dir)
{
	DetailsValueMap[ECompetitionDetailsOptions::LOGO] += _dir;
	DetailsValueMap[ECompetitionDetailsOptions::LOGO] = FMath::ClampWrap(DetailsValueMap[ECompetitionDetailsOptions::LOGO], 0, (int)CompLogoList.size()-1);

	SetOptionText(TabDefaultScrollBoxes[m_currentTab],
		(int)ECompetitionDetailsOptions::LOGO,
		CompLogoList[DetailsValueMap[ECompetitionDetailsOptions::LOGO]].GetNameString(), 
		DetailsValueMap[ECompetitionDetailsOptions::LOGO]+1, 
		(int)CompLogoList.size());

	UpdateCompetitionLogo();
}


void UWWUIScreenCustomiseCreateComp::OnTrophyChanged(int _dir)
{
	DetailsValueMap[ECompetitionDetailsOptions::TROPHY] += _dir;
	DetailsValueMap[ECompetitionDetailsOptions::TROPHY] = FMath::ClampWrap(DetailsValueMap[ECompetitionDetailsOptions::TROPHY], 0, (int)CompTrophyList.size() - 1);

	SetOptionText(TabDefaultScrollBoxes[m_currentTab],
		(int)ECompetitionDetailsOptions::TROPHY,
		CompTrophyList[DetailsValueMap[ECompetitionDetailsOptions::TROPHY]].GetName(),
		DetailsValueMap[ECompetitionDetailsOptions::TROPHY]+1,
		(int)CompTrophyList.size());

	UpdateTrophyLogo();
}
/* Nick GG Remove Law Variation for RL
void UWWUIScreenCustomiseCreateComp::OnLawVariationChanged(int _dir)
{
	DetailsValueMap[ECompetitionDetailsOptions::LAW_VARIATION] += _dir;
	DetailsValueMap[ECompetitionDetailsOptions::LAW_VARIATION] = FMath::ClampWrap(DetailsValueMap[ECompetitionDetailsOptions::LAW_VARIATION], 0, 1);

	SetOptionText(TabDefaultScrollBoxes[m_currentTab],
		(int)ECompetitionDetailsOptions::LAW_VARIATION,
		(DetailsValueMap[ECompetitionDetailsOptions::LAW_VARIATION]) ? UWWUITranslationManager::Translate("[ID_LAW_VARIATION_1]") : UWWUITranslationManager::Translate("[ID_LAW_VARIATION_0]"),
		DetailsValueMap[ECompetitionDetailsOptions::LAW_VARIATION]+1,
		2);

	SIFGameHelpers::GASetCustomCompetitionGameLaw(DetailsValueMap[ECompetitionDetailsOptions::LAW_VARIATION]);
}
*/
void UWWUIScreenCustomiseCreateComp::OnGenderChanged(int _dir)
{
	int gender = DetailsValueMap[ECompetitionDetailsOptions::GENDER];
	gender = (gender + _dir) % NUM_PLAYER_GENDER_OPTIONS;
	if (gender < 0)
		gender += NUM_PLAYER_GENDER_OPTIONS;
	DetailsValueMap[ECompetitionDetailsOptions::GENDER] = gender;

	FString genderOptionText = FString("");
	switch (gender)
	{
	case PLAYER_GENDER_MALE:
		genderOptionText = UWWUITranslationManager::Translate("[ID_CUSTOMISE_COMPETITION_MALE]");
		break;
	case PLAYER_GENDER_FEMALE:
		genderOptionText = UWWUITranslationManager::Translate("[ID_CUSTOMISE_COMPETITION_FEMALE]");
		break;
	}

	SetOptionText(TabDefaultScrollBoxes[m_currentTab],
		(int)ECompetitionDetailsOptions::GENDER,
		genderOptionText,
		gender + 1,
		NUM_PLAYER_GENDER_OPTIONS);

	UpdateGenderPermissions(PLAYER_GENDER_AS_FLAG(gender));
	IncrementCompFilter(0);
}

void UWWUIScreenCustomiseCreateComp::OnPointsChanged(const int dir, UWWUIScrollBox* pointsListBox, const int overrideSelectedIndex /*= -1*/)
{
	if (pointsListBox == nullptr)
		return;

	const int actualSelectedIndex = overrideSelectedIndex == -1 ? pointsListBox->GetSelectedIndex() : overrideSelectedIndex;
	int newPoints = 0;
	int indexToDisplay = 0;
	FString diplayString = "";
	ECompetitionPointsOptions pointsOption = (ECompetitionPointsOptions)actualSelectedIndex;
	int maxIndexToDisplay = CustomiseCreateCompPointsMaxOptions[actualSelectedIndex];

	//UE_LOG(LogTemp, Log, TEXT("OnPointsChanged(); dir: %d , selectedIndex: %d"), dir, actualSelectedIndex);

	switch (pointsOption)
	{
		case ECompetitionPointsOptions::POINTS_FOR_WIN:
		{
			newPoints = SIFGameHelpers::GAGetCustomCompetitionWinPoints() + dir;
			newPoints = FMath::ClampWrap<int32>(newPoints, 1, CustomiseCreateCompPointsMaxOptions[(int)pointsOption]);
			diplayString = FString::FromInt(newPoints) + " " + UWWUITranslationManager::Translate("[ID_COMPETITION_FORMAT_POINTS]");;
			indexToDisplay = newPoints;

			SIFGameHelpers::GASetCustomCompetitionWinPoints(newPoints);

			//Force update POINTS_FOR_DRAW
			int drawDir = SIFGameHelpers::GAGetCustomCompetitionDrawPoints() == SIFGameHelpers::GAGetCustomCompetitionWinPoints() ? -1 : 0;
			OnPointsChanged(drawDir, pointsListBox, (int)ECompetitionPointsOptions::POINTS_FOR_DRAW);
		}
		break;

		//THIS NEEDS TO BE UPDATED IF POINT_FOR_WIN GOES BELOW THE CURRENT VALUE FOR POINTS_FOR_DRAW
		case ECompetitionPointsOptions::POINTS_FOR_DRAW:
		{
			newPoints = SIFGameHelpers::GAGetCustomCompetitionDrawPoints() + dir;
			newPoints = FMath::ClampWrap<int32>(newPoints, 0, SIFGameHelpers::GAGetCustomCompetitionWinPoints() - 1);
			diplayString = FString::FromInt(newPoints) + " " + UWWUITranslationManager::Translate("[ID_COMPETITION_FORMAT_POINTS]");;
			indexToDisplay = newPoints + 1;
			maxIndexToDisplay = SIFGameHelpers::GAGetCustomCompetitionWinPoints(); //The visible max value for draw is winpoints

			SIFGameHelpers::GASetCustomCompetitionDrawPoints(newPoints);
		}
		break;

		case ECompetitionPointsOptions::ATTACKING_BONUS:
		{
			const int previousPoints = SIFGameHelpers::GAGetCustomCompetitionAttackingBonus();
			newPoints = previousPoints + dir;

			if (newPoints == 1)
			{
				if (previousPoints > 1)
				{
					--newPoints;
				}
				else if (previousPoints == 0)
				{
					++newPoints;
				}
			}

			newPoints = FMath::ClampWrap<int32>(newPoints, 0, CustomiseCreateCompPointsMaxOptions[(int)pointsOption]);
			indexToDisplay = newPoints;
			if (newPoints == 0)
			{
				diplayString = UWWUITranslationManager::Translate("[ID_COMPETITION_POINTS_NONE]");
				indexToDisplay = 1;
			}
			else
			{
				diplayString = FString::FromInt(newPoints) + " " + UWWUITranslationManager::Translate("[ID_COMPETITION_BONUS_TYPE_A_POSTFIX]");
			}

			SIFGameHelpers::GASetCustomCompetitionAttackingBonus(newPoints);
		}
		break;

		case ECompetitionPointsOptions::DEFENSIVE_BONUS:
		{
			newPoints = SIFGameHelpers::GAGetCustomCompetitionDefendingBonus() + dir;
			newPoints = FMath::ClampWrap<int32>(newPoints, 0, CustomiseCreateCompPointsMaxOptions[(int)pointsOption] - 1);
			indexToDisplay = newPoints + 1;

			if (newPoints == 0)
			{
				diplayString = UWWUITranslationManager::Translate("[ID_COMPETITION_POINTS_NONE]");
			}
			else
			{
				diplayString = UWWUITranslationManager::Translate("[ID_COMPETITION_BONUS_TYPE_B_PREFIX]") + " " + FString::FromInt(newPoints) + " " + UWWUITranslationManager::Translate("[ID_COMPETITION_BONUS_TYPE_B_POSTFIX]");
			}

			SIFGameHelpers::GASetCustomCompetitionDefendingBonus(newPoints);
		}
		break;

		case ECompetitionPointsOptions::POINTS_FOR_BONUS:
		{
			newPoints = SIFGameHelpers::GAGetCustomCompetitionBonusPoints() + dir;
			newPoints = FMath::ClampWrap<int32>(newPoints, 0, CustomiseCreateCompPointsMaxOptions[(int)pointsOption] - 1);
			indexToDisplay = newPoints + 1;
			diplayString = FString::FromInt(newPoints) + " " + UWWUITranslationManager::Translate("[ID_COMPETITION_FORMAT_POINTS]");;

			SIFGameHelpers::GASetCustomCompetitionBonusPoints(newPoints);
		}
		break;
	}

	
	SetOptionText(
		pointsListBox,
		actualSelectedIndex,
		diplayString,
		indexToDisplay,
		maxIndexToDisplay
	);
}
// Nick GG Remove Game mode for RL
/*
void UWWUIScreenCustomiseCreateComp::OnGameModeChanged(int _dir)
{
	if (_dir)
	{
		UpdateGameMode(!SIFGameHelpers::GAGetCustomCompetitionR7Exclusive());
		SIFGameHelpers::GARemoveCustomCompetitionTeams();
	}

	SetOptionText(TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT],
		(int)ECompetitionFormatOptions::GAME_MODE,
		UWWUITranslationManager::Translate(FString(SIFGameHelpers::GAGetCustomCompetitionR7Exclusive() ? "[ID_PRO_MODE_GAME_MODE_SEVENS]" : "[ID_PRO_MODE_GAME_MODE_FIFTEENS]")).ToUpper(),
		(int)SIFGameHelpers::GAGetCustomCompetitionR7Exclusive() + 1,
		2);
}
*/

void UWWUIScreenCustomiseCreateComp::OnRoundRobinChanged(int _dir)
{
	FormatValueMap[ECompetitionFormatOptions::ROUND_ROBIN] += _dir;
	FormatValueMap[ECompetitionFormatOptions::ROUND_ROBIN] = FMath::ClampWrap(FormatValueMap[ECompetitionFormatOptions::ROUND_ROBIN], 0, (int)RoundRobinOptions.Num() -1);

	//< Ensure that finals are always on when changing to league. > 
	if (strcmp(RoundRobinOptions[FormatValueMap[ECompetitionFormatOptions::ROUND_ROBIN]], RUUI_CUSTOM_COMP_ROUND_ROBIN_LEAGUE) == 0)
	{
		SIFGameHelpers::GASetCustomCompetitionFinals(true);
		OnFinalsChanged(0);
	}

	SIFGameHelpers::GASetCustomCompetitionRoundRobin(RoundRobinOptions[FormatValueMap[ECompetitionFormatOptions::ROUND_ROBIN]]);

	SetOptionText(TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT],
		(int)ECompetitionFormatOptions::ROUND_ROBIN,
		UWWUITranslationManager::Translate(FString("[ID_COMPETITION_FORMAT_" + FString(RoundRobinOptions[FormatValueMap[ECompetitionFormatOptions::ROUND_ROBIN]]).ToUpper() + "]")),
		FormatValueMap[ECompetitionFormatOptions::ROUND_ROBIN] + 1,
		RoundRobinOptions.Num());

	UpdateTeamStepSize();
	RefreshTeamCount();
	RefreshPlayEach();
	RefreshAdvanceA();

	const char * round_id = SIFGameHelpers::GAGetCustomCompetitionRoundRobin();

	//< Update No. of teams option enabled. >
	TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT]->GetListField((int)ECompetitionFormatOptions::TEAM_COUNT)->SetIsEnabled(!(strcmp(round_id, RUUI_CUSTOM_COMP_ROUND_ROBIN_SERIES) == 0 || strcmp(round_id, RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE) == 0));
	UpdateAdvanceASelectability();

	if (strcmp(round_id, RUUI_CUSTOM_COMP_ROUND_ROBIN_LEAGUE) == 0)
	{
		TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT]->GetListField((int)ECompetitionFormatOptions::FINALS)->SetIsEnabled(true);
	}
	else
	{
		TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT]->GetListField((int)ECompetitionFormatOptions::FINALS)->SetIsEnabled(false);
		if (strcmp(round_id, RUUI_CUSTOM_COMP_ROUND_ROBIN_SERIES) == 0)
		{
			SetPlayFinalsSelected(false);
		}
		else if (strcmp(round_id, RUUI_CUSTOM_COMP_ROUND_ROBIN_POOLS) == 0 || strcmp(round_id, RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE) == 0)
		{
			SetPlayFinalsSelected(true);
		}
		UpdateAdvanceASelectability();
	}

	RefreshAdvanceA();
	OnGrandFinalChanged(0);
	UpdateFinalsDiagram();
}

void UWWUIScreenCustomiseCreateComp::OnPoolCountChanged(int _dir)
{
	FormatValueMap[ECompetitionFormatOptions::POOL_COUNT] = FMath::ClampWrap(FormatValueMap[ECompetitionFormatOptions::POOL_COUNT] + _dir, COMPETITION_MIN_POOL_COUNT, COMPETITION_MAX_POOL_COUNT);

	SIFGameHelpers::GASetCustomCompetitionNumPools(FormatValueMap[ECompetitionFormatOptions::POOL_COUNT]);

	SetOptionText(TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT],
		(int)ECompetitionFormatOptions::POOL_COUNT,
		FString::FromInt(FormatValueMap[ECompetitionFormatOptions::POOL_COUNT]),
		FormatValueMap[ECompetitionFormatOptions::POOL_COUNT] - 1,
		COMPETITION_MAX_POOL_COUNT - 1);

	UpdateTeamStepSize();
	RefreshTeamCount();
}

void UWWUIScreenCustomiseCreateComp::OnTeamCountChanged(int _dir)
{
	const char* round_id = SIFGameHelpers::GAGetCustomCompetitionRoundRobin();
	int step_size = (strcmp(round_id, RUUI_CUSTOM_COMP_ROUND_ROBIN_POOLS) == 0 || strcmp(round_id, RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE) == 0) ? SIFGameHelpers::GAGetCustomCompetitionNumPools() : 1;
	int option_size = (SIFGameHelpers::GAGetCustomCompetitionRoundRobin() == MabString(RUUI_CUSTOM_COMP_ROUND_ROBIN_SERIES)) ? 2 : 24;
	int list_length = (option_size / step_size) - 1;

	for (size_t i = list_length; i <= 0; i--)
	{
		int child_team_num = (i + 2) * step_size;
		if (SIFGameHelpers::GAGetCustomCompetitionRoundRobin() == MabString(RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE) && child_team_num < 16)
			list_length--;
	}

	FormatValueMap[ECompetitionFormatOptions::TEAM_COUNT] += _dir;
	FormatValueMap[ECompetitionFormatOptions::TEAM_COUNT] = FMath::ClampWrap(FormatValueMap[ECompetitionFormatOptions::TEAM_COUNT], 0, list_length - 1);
	
	int num_of_teams = (FormatValueMap[ECompetitionFormatOptions::TEAM_COUNT] + 2) * step_size;
	SIFGameHelpers::GASetCustomCompetitionNumOfTeams(num_of_teams);
	RefreshAdvanceA();

	SetOptionText(TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT],
		(int)ECompetitionFormatOptions::TEAM_COUNT,
		FString::FromInt(num_of_teams),
		FormatValueMap[ECompetitionFormatOptions::TEAM_COUNT] + 1,
		list_length);
}

void UWWUIScreenCustomiseCreateComp::OnPlayEachChanged(int _dir)
{
	FormatValueMap[ECompetitionFormatOptions::PLAY_EACH] = FMath::ClampWrap(FormatValueMap[ECompetitionFormatOptions::PLAY_EACH] + _dir, 0, PlayEachOptions.Num() - 1);

	SIFGameHelpers::GASetCustomCompetitionNumTimesEachTeamPlayed(PlayEachOptions[FormatValueMap[ECompetitionFormatOptions::PLAY_EACH]]);

	SetOptionText(TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT],
		(int)ECompetitionFormatOptions::PLAY_EACH,
		FString::Printf(TEXT("%iX"), PlayEachOptions[FormatValueMap[ECompetitionFormatOptions::PLAY_EACH]]),
		FormatValueMap[ECompetitionFormatOptions::PLAY_EACH] + 1,
		PlayEachOptions.Num());
}

void UWWUIScreenCustomiseCreateComp::OnFinalsChanged(int _dir)
{
	if (_dir != 0) SIFGameHelpers::GASetCustomCompetitionFinals(!SIFGameHelpers::GAGetCustomCompetitionFinals());

	SetOptionText(TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT],
		(int)ECompetitionFormatOptions::FINALS,
		UWWUITranslationManager::Translate((SIFGameHelpers::GAGetCustomCompetitionFinals()) ? FString("[ID_YES]") : FString("[ID_NO]")),
		(int)SIFGameHelpers::GAGetCustomCompetitionFinals() + 1,
		2);

	RefreshAdvanceA();
	UpdateAdvanceASelectability();

	OnGrandFinalChanged(0);
}

void UWWUIScreenCustomiseCreateComp::OnAdvanceAChanged(int _dir)
{
	FormatValueMap[ECompetitionFormatOptions::ADVANCE_A] = FMath::ClampWrap(FormatValueMap[ECompetitionFormatOptions::ADVANCE_A] + _dir, 0, AdvanceAOptions.Num() - 1);

	const MabString round_robin_value = SIFGameHelpers::GAGetCustomCompetitionRoundRobin();
	MabString display_value(0, "[ID_COMPETITION_FINALS_ADVANCE_A_PREFIX] %i", AdvanceAOptions[FormatValueMap[ECompetitionFormatOptions::ADVANCE_A]]);
	if (round_robin_value == RUUI_CUSTOM_COMP_ROUND_ROBIN_POOLS || round_robin_value == RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE)
	{
		display_value += " [ID_COMPETITION_FINALS_ADVANCE_A_POSTFIX]";
	}

	if (strcmp(SIFGameHelpers::GAGetCustomCompetitionRoundRobin(), RUUI_CUSTOM_COMP_ROUND_ROBIN_SERIES) != 0)
		SIFGameHelpers::GASetCustomCompetitionFinalsAdvanceA(AdvanceAOptions[FormatValueMap[ECompetitionFormatOptions::ADVANCE_A]]);

	SetOptionText(TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT],
		(int)ECompetitionFormatOptions::ADVANCE_A,
		UWWUITranslationManager::Translate((AdvanceAOptions[FormatValueMap[ECompetitionFormatOptions::ADVANCE_A]] == -1) ? FString("[ID_NOT_APPLICABLE]") : SIFGameHelpers::GAConvertMabStringToFString(display_value)),
		FormatValueMap[ECompetitionFormatOptions::ADVANCE_A] + 1,
		AdvanceAOptions.Num());

	RefreshAdvanceB();
	UpdateAdvanceBSelectability();
}

void UWWUIScreenCustomiseCreateComp::OnAdvanceBChanged(int _dir)
{
	FormatValueMap[ECompetitionFormatOptions::ADVANCE_B] = FMath::ClampWrap(FormatValueMap[ECompetitionFormatOptions::ADVANCE_B] + _dir, 0, AdvanceBOptions.Num() - 1);

	const MabString round_robin_value = SIFGameHelpers::GAGetCustomCompetitionRoundRobin();
	MabString display_value = "";
	if (AdvanceBOptions[FormatValueMap[ECompetitionFormatOptions::ADVANCE_B]] == 0)
	{
		display_value = MabString(32, "[ID_COMPETITION_POINTS_NONE]");
	}
	else if (AdvanceBOptions[FormatValueMap[ECompetitionFormatOptions::ADVANCE_B]] == -1)
	{
		display_value = "[ID_NOT_APPLICABLE]";
	}
	else
	{
		display_value = MabString(32, "[ID_COMPETITION_FINALS_ADVANCE_B_PREFIX] %i [ID_COMPETITION_FINALS_ADVANCE_B_POSTFIX]", AdvanceBOptions[FormatValueMap[ECompetitionFormatOptions::ADVANCE_B]]);
	}

	if (strcmp(SIFGameHelpers::GAGetCustomCompetitionRoundRobin(), RUUI_CUSTOM_COMP_ROUND_ROBIN_POOLS) == 0)
		SIFGameHelpers::GASetCustomCompetitionFinalsAdvanceB(AdvanceBOptions[FormatValueMap[ECompetitionFormatOptions::ADVANCE_B]]);

	SetOptionText(TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT],
		(int)ECompetitionFormatOptions::ADVANCE_B,
		UWWUITranslationManager::Translate(SIFGameHelpers::GAConvertMabStringToFString(display_value)),
		FormatValueMap[ECompetitionFormatOptions::ADVANCE_B] + 1,
		AdvanceBOptions.Num());

	UpdateFinalsDiagram();
}

void UWWUIScreenCustomiseCreateComp::OnGrandFinalChanged(int _dir)
{
	//< Update selected state. >
	bool is_selectable = SIFGameHelpers::GAGetCustomCompetitionFinals();
	if (strcmp(SIFGameHelpers::GAGetCustomCompetitionRoundRobin(), RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE) == 0)
		is_selectable = true;
	TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT]->GetListField((int)ECompetitionFormatOptions::GRAND_FINAL)->SetIsEnabled(is_selectable);

	FormatValueMap[ECompetitionFormatOptions::GRAND_FINAL] = FMath::ClampWrap(FormatValueMap[ECompetitionFormatOptions::GRAND_FINAL] + _dir, 0, (int)ExportedStadiumList.size() - 1);
	SIFGameHelpers::GASetCustomCompetitionGrandFinalStadiumId(ExportedStadiumList[FormatValueMap[ECompetitionFormatOptions::GRAND_FINAL]]->GetDbId());
	
	UpdateStadiumImage();

	SetOptionText(TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT],
		(int)ECompetitionFormatOptions::GRAND_FINAL,
		UWWUITranslationManager::Translate(is_selectable ? FString(ExportedStadiumList[FormatValueMap[ECompetitionFormatOptions::GRAND_FINAL]]->GetName()) : FString("[ID_NOT_APPLICABLE]")),
		FormatValueMap[ECompetitionFormatOptions::GRAND_FINAL] + 1,
		ExportedStadiumList.size());
}

bool UWWUIScreenCustomiseCreateComp::Validate()
{
	bool is_valid = true;

	/// Load first field.
	UWWUIListField* CompNameField = TabDefaultScrollBoxes[ECompetitionCustomiseTab::DETAILS]->GetListField((int)ECompetitionDetailsOptions::NAME);
	if (!CompNameField) return false;

	/// Retreive it's editable text component.
	UWWUIEditableText* EditableTextComponent = Cast<UWWUIEditableText>(UWWUIFunctionLibrary::FindChildWidget(CompNameField, WWUIScreenCustomiseCreateComp_UI::EditableTextField));
	if (!EditableTextComponent) return false;

	//< The competition must have a valid name, meaning that it can not be an empty string. >
	FString comp_name = EditableTextComponent->GetText().ToString();
	if (comp_name == "" || !SIFGameHelpers::GACareerManagerNameIsValid(TCHAR_TO_ANSI(*comp_name), true))
	{
		/// The competition doesn't have a valid name. We must tell the user to enter something.
		is_valid = false;
		SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::INVALID_NAME, nullptr);
	}

	return is_valid;
}

void UWWUIScreenCustomiseCreateComp::UpdateTrophyLogo()
{
	MabString tempMabCompetitionTrophyPath = SIFGameHelpers::GAGetTrophyName(CompTrophyList[DetailsValueMap[ECompetitionDetailsOptions::TROPHY]].GetDbId());

	FString trophyName = FString("/Game/Rugby/cmn_con/ui/Career/trophies/trophy_") + tempMabCompetitionTrophyPath.c_str() + FString(".trophy_") + tempMabCompetitionTrophyPath.c_str();
	UTexture2D* pTrophyTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *trophyName));

	if (!pTrophyTexture)
	{
		trophyName = "/Game/Rugby/cmn_con/ui/GenericMissingTexture.GenericMissingTexture";
		pTrophyTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *trophyName));
	}

	if (pTrophyTexture && TrophyImageWidget)
	{
		TrophyImageWidget->SetBrushFromTexture(pTrophyTexture);
	}

	SIFGameHelpers::GASetCustomCompetitionTrophyId(CompTrophyList[DetailsValueMap[ECompetitionDetailsOptions::TROPHY]].GetDbId());
}

void UWWUIScreenCustomiseCreateComp::UpdateCompetitionLogo()
{
	MabString tempMabCompetitionLogoPath = MabString(0, "comp_logo_%s", MabStringHelper::ToLower(CompLogoList[DetailsValueMap[ECompetitionDetailsOptions::LOGO]].GetIconFileString()).c_str());
	FString logoName = FString("/Game/Rugby/cmn_con/ui/Logos/league_logos/") + tempMabCompetitionLogoPath.c_str() + FString(".") + tempMabCompetitionLogoPath.c_str();

	UTexture2D* pLogoTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *logoName));

	if (!pLogoTexture)
	{
		logoName = "/Game/Rugby/cmn_con/ui/GenericMissingTexture.GenericMissingTexture";
		pLogoTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *logoName));
	}

	if (pLogoTexture  && LogoImageWidget)
	{
		LogoImageWidget->SetBrushFromTexture(pLogoTexture);
	}

	SIFGameHelpers::GASetCustomCompetitionLogoId(CompLogoList[DetailsValueMap[ECompetitionDetailsOptions::LOGO]].GetDbId());
}

void UWWUIScreenCustomiseCreateComp::UpdateTeamStepSize()
{
	const char * round_id	= SIFGameHelpers::GAGetCustomCompetitionRoundRobin();
	int selected_pool_count = FormatValueMap[ECompetitionFormatOptions::POOL_COUNT];

	//< Update No. of teams option enabled. >
	TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT]->GetListField((int)ECompetitionFormatOptions::TEAM_COUNT)->SetIsEnabled(!(strcmp(round_id, RUUI_CUSTOM_COMP_ROUND_ROBIN_SERIES) == 0 || strcmp(round_id, RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE) == 0));

	if (strcmp(round_id, RUUI_CUSTOM_COMP_ROUND_ROBIN_POOLS) == 0 || strcmp(round_id, RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE) == 0)
	{
		TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT]->GetListField((int)ECompetitionFormatOptions::POOL_COUNT)->SetIsEnabled(true);

		SIFGameHelpers::GASetCustomCompetitionNumPools(FormatValueMap[ECompetitionFormatOptions::POOL_COUNT]);
		SetOptionText(TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT],
			(int)ECompetitionFormatOptions::POOL_COUNT,
			FString::FromInt(FormatValueMap[ECompetitionFormatOptions::POOL_COUNT]),
			FormatValueMap[ECompetitionFormatOptions::POOL_COUNT] - 1,
			COMPETITION_MAX_POOL_COUNT - 1);
	}
	else 
	{
		TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT]->GetListField((int)ECompetitionFormatOptions::POOL_COUNT)->SetIsEnabled(false);
		SIFGameHelpers::GASetCustomCompetitionNumPools(1);

		SetOptionText(TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT],
			(int)ECompetitionFormatOptions::POOL_COUNT,
			UWWUITranslationManager::Translate(FString("[ID_NOT_APPLICABLE]")),
			FormatValueMap[ECompetitionFormatOptions::POOL_COUNT] + 1,
			COMPETITION_MAX_POOL_COUNT - 1);
	}
}

void UWWUIScreenCustomiseCreateComp::RefreshTeamCount()
{
	const int old_num_of_teams = SIFGameHelpers::GAGetCustomCompetitionNumOfTeams();

	const char* round_id = SIFGameHelpers::GAGetCustomCompetitionRoundRobin();
	int step_size = (strcmp(round_id, RUUI_CUSTOM_COMP_ROUND_ROBIN_POOLS) == 0 || strcmp(round_id, RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE) == 0) ? SIFGameHelpers::GAGetCustomCompetitionNumPools() : 1;
	int option_size = (SIFGameHelpers::GAGetCustomCompetitionRoundRobin() == MabString(RUUI_CUSTOM_COMP_ROUND_ROBIN_SERIES)) ? 2 : 24;
	int list_length = (option_size / step_size) - 1;

	for (size_t i = list_length; i <= 0; i--)
	{
		int child_team_num = (i + 2) * step_size;
		if (SIFGameHelpers::GAGetCustomCompetitionRoundRobin() == MabString(RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE) && child_team_num < 16)
			list_length--;
	}

	// Now we need to try and select the closest value.
	int current_min_delta = 9999999;
	size_t current_min_index = 0;
	for (size_t i = 0; i < list_length; ++i)
	{
		int child_team_num = (i + 2) * step_size;

		const int current_delta = MabMath::Abs(child_team_num - old_num_of_teams);
		if (current_delta < current_min_delta)
		{
			current_min_delta = current_delta;
			current_min_index = i;
		}
	}

	FormatValueMap[ECompetitionFormatOptions::TEAM_COUNT] = current_min_index;
	OnTeamCountChanged(0);
}

void UWWUIScreenCustomiseCreateComp::RefreshPlayEach()
{
	PlayEachOptions.Empty();

	const MabString round_robin_id = SIFGameHelpers::GAGetCustomCompetitionRoundRobin();
	if (round_robin_id == RUUI_CUSTOM_COMP_ROUND_ROBIN_SERIES)
	{
		// Series is currently the only option to give three options.
		PlayEachOptions.Add(3);
		PlayEachOptions.Add(5);
		PlayEachOptions.Add(7);
	}
	else
	{
		PlayEachOptions.Add(1);
		PlayEachOptions.Add(2);
	}

	int outIndex = 0;
	if (!PlayEachOptions.Find(SIFGameHelpers::GAGetCustomCompetitionPlayEach(), outIndex))
		outIndex = 0;
		
	FormatValueMap[ECompetitionFormatOptions::PLAY_EACH] = outIndex;
	OnPlayEachChanged(0);
}

void UWWUIScreenCustomiseCreateComp::RefreshAdvanceA()
{
	const int old_advance_a				= SIFGameHelpers::GAGetCustomCompetitionFinalsAdvanceA();
	const MabString round_robin_value	= SIFGameHelpers::GAGetCustomCompetitionRoundRobin();
	const bool is_playing_finals		= SIFGameHelpers::GAGetCustomCompetitionFinals();
	AdvanceAOptions.Empty();

	if (is_playing_finals == false || round_robin_value == RUUI_CUSTOM_COMP_ROUND_ROBIN_SERIES)
	{
		// We need to add one element and set the name to N/A.
		AdvanceAOptions.Add(-1);
	}
	else
	{
		const int num_of_pools = SIFGameHelpers::GAGetCustomCompetitionNumPools();
		const int num_of_teams = SIFGameHelpers::GAGetCustomCompetitionNumOfTeams();
		const int num_of_teams_per_pool = num_of_teams / num_of_pools;

		// The starting value depends on if we're playing a league or pools.
		int starting_index = STARTING_INDEX_POOLS;
		if (round_robin_value == RUUI_CUSTOM_COMP_ROUND_ROBIN_LEAGUE)
		{
			// Leagues should start at two.
			starting_index = STARTING_INDEX_LEAGUE;
		}

		// We can figure out the max advance A option by determining how many plays
		// can fit into the available finals slots.
		// There can only be eight teams in the finals, so the top X per team should not total to above this value.
		const int top_advance_a_option = MabMath::Min(MAX_TEAM_IN_FINALS / num_of_pools, num_of_teams_per_pool);
		MABASSERT(top_advance_a_option * num_of_pools <= MAX_TEAM_IN_FINALS);

		// We can now construct the options in the container.
		for (int i = starting_index; i <= top_advance_a_option; ++i)
		{
			AdvanceAOptions.Add(i);
		}
	}

	// Now we need to try and select the closest value.
	int current_min_delta = 9999999;
	size_t current_min_index = 0;
	for (size_t i = 0; i < AdvanceAOptions.Num(); ++i)
	{
		int child_advance_a = AdvanceAOptions[i];

		const int current_delta = MabMath::Abs(child_advance_a - old_advance_a);
		if (current_delta < current_min_delta)
		{
			current_min_delta = current_delta;
			current_min_index = i;
		}
	}

	FormatValueMap[ECompetitionFormatOptions::ADVANCE_A] = current_min_index;
	OnAdvanceAChanged(0);
}

void UWWUIScreenCustomiseCreateComp::RefreshAdvanceB()
{
	const int old_advance_b = SIFGameHelpers::GAGetCustomCompetitionFinalsAdvanceB();
	const MabString round_robin_value = SIFGameHelpers::GAGetCustomCompetitionRoundRobin();
	AdvanceBOptions.Empty();

	if (round_robin_value == RUUI_CUSTOM_COMP_ROUND_ROBIN_POOLS)
	{
		// Now the options we present depend on the number of pools and the Advance A selected option.
		const int num_of_pools = SIFGameHelpers::GAGetCustomCompetitionNumPools();
		const int advance_a_option = SIFGameHelpers::GAGetCustomCompetitionFinalsAdvanceA();
		const int num_of_teams = SIFGameHelpers::GAGetCustomCompetitionNumOfTeams();
		//const int num_of_teams_per_pool = num_of_teams / num_of_pools;

		// The number of advance B options is just the remainder of finals slots.
		// We can have a maximum of eight teams in the finals, so we look at how many per team we're sending through
		// and then how many slots are left.
		int top_advance_b_option = MAX_TEAM_IN_FINALS - (advance_a_option * num_of_pools);

		// We also have to be careful to check we've got enough teams to add.
		if ((num_of_pools * advance_a_option) + top_advance_b_option > num_of_teams)
		{
			top_advance_b_option = num_of_teams - (num_of_pools * advance_a_option);
		}

		// We can now construct the options in the container.
		for (int i = 0; i <= top_advance_b_option; ++i)
		{
			AdvanceBOptions.Add(i);
		}
	}

	// We want the selection to be N/A
	if (AdvanceBOptions.Num() == 0) AdvanceBOptions.Add(-1);

	// Now we need to try and select the closest value.
	int current_min_delta = 9999999;
	size_t current_min_index = 0;
	for (size_t i = 0; i < AdvanceBOptions.Num(); ++i)
	{
		int child_advance_b = AdvanceBOptions[i];

		const int current_delta = MabMath::Abs(child_advance_b - old_advance_b);
		if (current_delta < current_min_delta)
		{
			current_min_delta = current_delta;
			current_min_index = i;
		}
	}

	FormatValueMap[ECompetitionFormatOptions::ADVANCE_B] = current_min_index;
	OnAdvanceBChanged(0);
}

void UWWUIScreenCustomiseCreateComp::SetPlayFinalsSelected(bool _shouldPlayFinals)
{
	SIFGameHelpers::GASetCustomCompetitionFinals(_shouldPlayFinals);

	SetOptionText(TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT],
		(int)ECompetitionFormatOptions::FINALS,
		UWWUITranslationManager::Translate((SIFGameHelpers::GAGetCustomCompetitionFinals()) ? FString("[ID_YES]") : FString("[ID_NO]")),
		(int)SIFGameHelpers::GAGetCustomCompetitionFinals() + 1,
		2);
}

void UWWUIScreenCustomiseCreateComp::UpdateAdvanceASelectability()
{
 	bool is_selectable = true;
	if (!SIFGameHelpers::GAGetCustomCompetitionFinals())
		is_selectable = false;
	else if (strcmp(SIFGameHelpers::GAGetCustomCompetitionRoundRobin(), RUUI_CUSTOM_COMP_ROUND_ROBIN_SERIES) == 0 || strcmp(SIFGameHelpers::GAGetCustomCompetitionRoundRobin(), RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE) == 0)
		is_selectable = false;

	TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT]->GetListField((int)ECompetitionFormatOptions::ADVANCE_A)->SetIsEnabled(is_selectable);
}

void UWWUIScreenCustomiseCreateComp::UpdateAdvanceBSelectability()
{
	bool is_selectable = true;
	if (!SIFGameHelpers::GAGetCustomCompetitionFinals())
		is_selectable = false;
	else if (strcmp(SIFGameHelpers::GAGetCustomCompetitionRoundRobin(), RUUI_CUSTOM_COMP_ROUND_ROBIN_LEAGUE) == 0 || strcmp(SIFGameHelpers::GAGetCustomCompetitionRoundRobin(), RUUI_CUSTOM_COMP_ROUND_ROBIN_SERIES) == 0 || strcmp(SIFGameHelpers::GAGetCustomCompetitionRoundRobin(), RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE) == 0)
		is_selectable = false;

	TabDefaultScrollBoxes[ECompetitionCustomiseTab::FORMAT]->GetListField((int)ECompetitionFormatOptions::ADVANCE_B)->SetIsEnabled(is_selectable);
}

void UWWUIScreenCustomiseCreateComp::UpdateFinalsDiagram()
{
	int num_of_pools	= SIFGameHelpers::GAGetCustomCompetitionNumPools();
	int advance_a		= SIFGameHelpers::GAGetCustomCompetitionFinalsAdvanceA();
	int advance_b		= SIFGameHelpers::GAGetCustomCompetitionFinalsAdvanceB();
	int finals_format = (num_of_pools * advance_a) + advance_b;

	if (strcmp(SIFGameHelpers::GAGetCustomCompetitionRoundRobin(), RUUI_CUSTOM_COMP_ROUND_ROBIN_SATELLITE) == 0)
		finals_format = 0;

	UWidget* format_image = UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenCustomiseCreateComp_UI::FormatImage);
	bool format_image_visibility = true;

	FString format_texture;
	switch (finals_format)
	{
	case 2: format_texture = "finals_top2";		break;
	case 3: format_texture = "finals_top3";		break;
	case 4: format_texture = "finals_top4c";	break;
	case 5: format_texture = "finals_top5a";	break;
	case 6: format_texture = "finals_top6a";	break;
	case 7: format_texture = "finals_top7";		break;
	case 8: format_texture = "finals_top8c";	break;
	default: format_image_visibility = false;
		break;
	}

	UWWUIFunctionLibrary::SetOpacity(format_image, format_image_visibility ? 1.0f : 0.0f);
	UWWUIFunctionLibrary::SetTexture(format_image, FString::Printf(TEXT("/Game/Rugby/cmn_con/ui/finals_brackets/%s"), *format_texture));
}

void UWWUIScreenCustomiseCreateComp::UpdateStadiumImage()
{
	UImage* formatImage = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenCustomiseCreateComp_UI::FormatImage));

	FString stadiumRenderPath = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetStadiumRenderPath(ExportedStadiumList[FormatValueMap[ECompetitionFormatOptions::GRAND_FINAL]]->GetDbId()));

	UWWUIFunctionLibrary::SetTexture(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenCustomiseCreateComp_UI::StadiumImage), stadiumRenderPath);
}

/// Previously most of this work was done by some wacky populators.
void UWWUIScreenCustomiseCreateComp::LoadSettingsFromDatabase()
{
	LoadDetailSettingFromDatabase();
	LoadFormatSettingFromDatabase();
	LoadPointsSettingFromDatabase();
	LoadTeamSettingsFromDatabase();
}


void UWWUIScreenCustomiseCreateComp::LoadDetailSettingFromDatabase()
{
	/// Load Logos & Trophies from database.
	m_pDatabaseManager->LoadAllData<RUDB_ICON>(CompLogoList);
	m_pDatabaseManager->LoadAllData<RUDB_COMP_TROPHY>(CompTrophyList);

	/// Sort Logos by Alphabetical order.
	std::sort(CompLogoList.begin(), CompLogoList.end(), AlphabeticalSortComparator);

	/// Load starting values based off previously selected template.
	FString comp_name = SIFGameHelpers::GAGetCustomCompetitionName();
	int comp_logo_index = 0;
	int comp_trophy_index = 0;
	int gender_value = 0;

	for (int i = 0; i < CompLogoList.size(); i++) if (CompLogoList[i].GetDbId() == SIFGameHelpers::GAGetCustomCompetitionLogoId()) comp_logo_index = i;
	for (int i = 0; i < CompTrophyList.size(); i++) if (CompTrophyList[i].GetDbId() == SIFGameHelpers::GAGetCustomCompetitionTrophyId()) comp_trophy_index = i;
	for (int i = 0; i < NUM_PLAYER_GENDER_OPTIONS; ++i) if (PLAYER_GENDER_AS_FLAG(i) == SIFGameHelpers::GAGetCustomCompetitionGenderPermissionFlags()) gender_value = i;

	DetailsValueMap.Add(ECompetitionDetailsOptions::LOGO,			comp_logo_index);
	DetailsValueMap.Add(ECompetitionDetailsOptions::TROPHY,			comp_trophy_index);
	//DetailsValueMap.Add(ECompetitionDetailsOptions::LAW_VARIATION,	SIFGameHelpers::GAGetCustomCompetitionGameLaw()); Nick GG Remove Law Variation for RL
	DetailsValueMap.Add(ECompetitionDetailsOptions::GENDER,			gender_value);

	OnNameChanged(comp_name);
	OnLogoChanged(0);
	OnTrophyChanged(0);
	//OnLawVariationChanged(0); Nick GG Remove Law Variation for RL
	OnGenderChanged(0);
}

void UWWUIScreenCustomiseCreateComp::LoadFormatSettingFromDatabase()
{
	/// Loading Round Robin data.
	RoundRobinOptions.Add(RUUI_CUSTOM_COMP_ROUND_ROBIN_POOLS);
	RoundRobinOptions.Add(RUUI_CUSTOM_COMP_ROUND_ROBIN_LEAGUE);
	RoundRobinOptions.Add(RUUI_CUSTOM_COMP_ROUND_ROBIN_SERIES);

	int32 RoundRobinIndex = 0;
	for (int i = 0; i < RoundRobinOptions.Num(); i++)
		if (strcmp(RoundRobinOptions[i], SIFGameHelpers::GAGetCustomCompetitionRoundRobin()) == 0) RoundRobinIndex = i;

	//FormatValueMap.Add(ECompetitionFormatOptions::GAME_MODE,	0);
	FormatValueMap.Add(ECompetitionFormatOptions::ROUND_ROBIN,	RoundRobinIndex);
	FormatValueMap.Add(ECompetitionFormatOptions::POOL_COUNT,	SIFGameHelpers::GAGetCustomCompetitionNumPools());
	FormatValueMap.Add(ECompetitionFormatOptions::TEAM_COUNT,	SIFGameHelpers::GAGetCustomCompetitionNumOfTeams());
	FormatValueMap.Add(ECompetitionFormatOptions::PLAY_EACH,	0);
	FormatValueMap.Add(ECompetitionFormatOptions::ADVANCE_A,	0);
	FormatValueMap.Add(ECompetitionFormatOptions::ADVANCE_B,	0);
	FormatValueMap.Add(ECompetitionFormatOptions::GRAND_FINAL,	0);

	/// Load Grand Final Stadium.
	const RUStadiumManager* const stadium_mgr = SIFApplication::GetApplication()->GetActiveGameWorld()->GetStadiumManager();
	stadium_mgr->GetExportedStadiumList(ExportedStadiumList);
	for (int i = 0; i < ExportedStadiumList.size(); i++)
	{
		if (ExportedStadiumList[i]->GetDbId() == SIFGameHelpers::GAGetCustomCompetitionGrandFinalStadiumId())
			FormatValueMap[ECompetitionFormatOptions::GRAND_FINAL] = i;
	}
	// Nick GG Remove Game mode for RL
	// OnGameModeChanged(0);
	RefreshPlayEach();
	OnRoundRobinChanged(0);
	OnPoolCountChanged(0);
	OnTeamCountChanged(0);
	OnFinalsChanged(0);

	/// Load Advance A.
	RefreshAdvanceA();
	int advance_a_index = 0;
	AdvanceAOptions.Find(SIFGameHelpers::GAGetCustomCompetitionFinalsAdvanceA(), advance_a_index);
	FormatValueMap[ECompetitionFormatOptions::ADVANCE_A] = advance_a_index;

	OnAdvanceAChanged(0);

	/// Load Advance B.
	RefreshAdvanceB();
	int advance_b_index = 0;
	AdvanceBOptions.Find(SIFGameHelpers::GAGetCustomCompetitionFinalsAdvanceB(), advance_b_index);
	FormatValueMap[ECompetitionFormatOptions::ADVANCE_B] = advance_b_index;

	OnAdvanceBChanged(0);
	OnGrandFinalChanged(0);
}

void UWWUIScreenCustomiseCreateComp::LoadPointsSettingFromDatabase()
{
	for (uint32 i = 0; i < (uint32)ECompetitionPointsOptions::COUNT; ++i)
	{
		OnPointsChanged(0, TabDefaultScrollBoxes[ECompetitionCustomiseTab::POINTS], (int)(ECompetitionPointsOptions)i);
	}
}

void UWWUIScreenCustomiseCreateComp::LoadTeamSettingsFromDatabase()
{
	//< This sets up the text for the list filter and populates the comp list. >
	// Nick GG Remove Game mode for RL
	// UpdateGameMode(SIFGameHelpers::GAGetCustomCompetitionR7Exclusive());
	IncrementCompFilter(0);
}

void UWWUIScreenCustomiseCreateComp::ApplySettingsToDatabase()
{
	
}


void UWWUIScreenCustomiseCreateComp::SetOptionText(UWWUIScrollBox* _Scrollbox, int32 _ScrollboxIndex, FString _Text, int32 _OptionValue, int32 _OptionMaxValue)
{
	if (_Scrollbox && _ScrollboxIndex < _Scrollbox->GetListLength())
	{
		UUserWidget* ListField = _Scrollbox->GetListField(_ScrollboxIndex);

		/// Set option text.
		UTextBlock* TempTextBlock			= Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(ListField, WWUIScreenCustomiseCreateComp_UI::Subtitle));
		UWWUIEditableText* TempEditableText = Cast<UWWUIEditableText>(UWWUIFunctionLibrary::FindChildWidget(ListField, WWUIScreenCustomiseCreateComp_UI::EditableTextField));
		if (TempTextBlock)		TempTextBlock->SetText(		FText::FromString(_Text.ToUpper()));
		if (TempEditableText)	TempEditableText->SetText(	FText::FromString(_Text));

		/// Set option count text.
		UTextBlock* OptionCountText = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(ListField, WWUIScreenCustomiseCreateComp_UI::NumOptions));
		if (OptionCountText) OptionCountText->SetText(FText::FromString(FString::FromInt(_OptionValue) + "/" + FString::FromInt(_OptionMaxValue)));
		
	}
}

void UWWUIScreenCustomiseCreateComp::UpdateHelpText()
{
	//< The help text we use depends on the state we are in. >
	FString text_to_set = "";

	if (m_currentTab == ECompetitionCustomiseTab::TEAMS)
	{
		switch (TeamTabState)
		{
		case ETeamTabState::NORMAL:			text_to_set = "[ID_CUSTOM_COMP_EDIT_POOLS_HELP]";	break;
		case ETeamTabState::MOVE:			text_to_set = "[ID_CUSTOM_COMP_SWAP_HELP]";			break;
		case ETeamTabState::TEAM_SELECT:
		{
			/*  The text to set here depends on what team we have selected.
				If the highlighted team has already been added to the pools list,
				then we shouldn't give them the option to add again. */
			if (UWWUIListField* selected_page = TeamListScrollbox->GetListField(TeamListScrollbox->GetSelectedIndex()))
			{
				int new_team_id = selected_page->GetIntProperty("team_db_id");

				if (SIFGameHelpers::GADoesCustomCompetitionContainTeam(new_team_id))
					text_to_set = "[ID_CUSTOM_COMP_SELECT_TEAM_INVALID_HELP]";
				else
					text_to_set = "[ID_CUSTOM_COMP_SELECT_TEAM_HELP]";
			}
			else
			{
				text_to_set = "[ID_CUSTOM_COMP_SELECT_TEAM_INVALID_HELP]";
			}
		}
		break;
		default:
			break;
		}
	}
	else
	{
		text_to_set = "[ID_CUSTOM_COMP_HELP_TIP]";
	}

	if (UWWUIRichTextBlockWithTranslate* textBlock = Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenCustomiseCreateComp_UI::HelpText)))
		textBlock->SetText(text_to_set);
}

//===============================================================================
//===============================================================================
void UWWUIScreenCustomiseCreateComp::HandleUGCUsernameCheckComplete(bool val)
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		RUDB_TEAM db_team;
		RUGameDatabaseManager* database_manager = pRugbyGameInstance->GetGameDatabaseManager();

		if (database_manager)
		{
			database_manager->LoadData(db_team, TeamDatabaseID);
			UpdateTeamCreator(db_team);
		}
	}
}
void UWWUIScreenCustomiseCreateComp::CleanupHighlightAndRedoFocus()
{
#if PLATFORM_SWITCH
	if (m_currentTab != ECompetitionCustomiseTab::TEAMS)
		return;
	
	for (int i = 0; i < PoolWidgetGroups.Num(); i++)
	{
		if (!PoolWidgetGroups[i].Scrollbox)
		{
			continue;
		}

			for(int j = 0; j < PoolWidgetGroups[i].Scrollbox->GetListLength(); j++)
			{
				if ( i == CurrentSelectCol && j == CurrentSelectRow)
					continue;
				UUserWidget* ListField = PoolWidgetGroups[i].Scrollbox->GetListField(j);
				UWWUIFunctionLibrary::PlayAnimation(ListField, FString("focused"), 0.149999, 1, EUMGSequencePlayMode::Type::Reverse);
			}
	}
#endif
}

void UWWUIScreenCustomiseCreateComp::DismissAllFocusEffect()
{
#if PLATFORM_SWITCH
	//PoolWidgetGroups
	for (int i = 0; i < PoolWidgetGroups.Num(); i++)
	{
		if (!PoolWidgetGroups[i].Scrollbox)
		{
			continue;
		}
		
		for (int j = 0; j < PoolWidgetGroups[i].Scrollbox->GetListLength(); j++)
		{
			UUserWidget* ListField = PoolWidgetGroups[i].Scrollbox->GetListField(j);
			UWWUIFunctionLibrary::PlayAnimation(ListField, FString("focused"), 0.149999, 1, EUMGSequencePlayMode::Type::Reverse);
		}
	}
#endif
}

FPoolWidgetGroup::FPoolWidgetGroup(int _PoolIndex, UUserWidget* _ScreenRef)
{
	Header		=						UWWUIFunctionLibrary::FindChildWidget(_ScreenRef, FString::Printf(TEXT("PoolHeader%d"),		_PoolIndex));
	HeaderText	= Cast<UTextBlock>(		UWWUIFunctionLibrary::FindChildWidget(_ScreenRef, FString::Printf(TEXT("PoolHeaderText%d"),	_PoolIndex)));
	Scrollbox	= Cast<UWWUIScrollBox>(	UWWUIFunctionLibrary::FindChildWidget(_ScreenRef, FString::Printf(TEXT("PoolScrollbox%d"),	_PoolIndex)));

	Populator = (Scrollbox) ? Cast<UWWUIPopulatorCompPool>(Scrollbox->GetPopulator()) : nullptr;

	if (HeaderText)
	{
		char poolChar = 'A';
		FString poolName = UWWUITranslationManager::Translate("[ID_CUSTOM_COMP_POOL]");
		poolName.Append(" ");
		poolName.AppendChar(poolChar + (_PoolIndex - 1));

		HeaderText->SetText(FText::FromString(poolName));
	}
}
