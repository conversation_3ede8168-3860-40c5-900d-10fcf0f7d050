/*--------------------------------------------------------------
|        Copyright (C) 1997-2012 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/


#include "WWUICompetitionMainMenuListboxPopulator.h"
#include "Databases/RUGameDatabaseManager.h"
#include "Match/SIFUIConstants.h"
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "WWUIScreenTemplate.h"
#include "RugbyGameInstance.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Database.h"
#include "UI/GeneratedHeaders/WWUIScreenMainMenu_UI_Namespace.h"
#include "UI/Screens/WWUIScreenMainMenu.h"
#include "UI/Populators/WWUICompetitionMainMenuListboxPopulator.h"

//Components
#include "Button.h"
#include "Widget.h"
#include "WidgetTree.h"
#include "RichTextBlock.h"
#include "WWUIListField.h"
#include "Utility/Helpers/SIFGameHelpers.h"
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "Rugby/UI/Screens/Components/WWUIMainMenuField.h"
#include "WWUITranslationManager.h"
#include "UI/Interfaces/CompetitionModePopulatedInterface.h"


UWWUICompetitionMainMenuListboxPopulator::UWWUICompetitionMainMenuListboxPopulator()
{
}

UWWUICompetitionMainMenuListboxPopulator::UWWUICompetitionMainMenuListboxPopulator( const RUGameDatabaseManager& game_database_mgr_ )
	: game_database_mgr( &game_database_mgr_ )
	, population_node( NULL )
{
}

UWWUICompetitionMainMenuListboxPopulator::~UWWUICompetitionMainMenuListboxPopulator()
{
}

void UWWUICompetitionMainMenuListboxPopulator::Refresh( UWidget * widget)
{
}

bool UWWUICompetitionMainMenuListboxPopulator::ReversePredicate( const BaseListboxChildCallback::CompetitionInfoStruct& left, const BaseListboxChildCallback::CompetitionInfoStruct& right )
{
	return( left.additional_info.last_modification_time > right.additional_info.last_modification_time );
}

void UWWUICompetitionMainMenuListboxPopulator::Populate(UWidget * widget)
{
	Clear(widget);

	if (!inPreConstruct)
	{
		if (widget == NULL)
		{
			MABBREAK();
			return;
		}

		if (!game_database_mgr)
		{
			game_database_mgr = GetWorld()->GetGameInstance<URugbyGameInstance>()->GetGameDatabaseManager();
		}

		if (game_database_mgr)
		{
			MabVector< BaseListboxChildCallback::CompetitionInfoStruct> competition_info_list;
			competition_info_list.reserve(game_database_mgr->MAX_NUM_COMPETITION_SAVES);

			for (size_t i = 0u; i < game_database_mgr->MAX_NUM_COMPETITION_SAVES; ++i)
			{
				if (game_database_mgr->GetCompetitionDatabaseExists((int)i))
				{
					RUGameDatabaseManager::CompetitionAdditionalInfo addl_info;
					MABVERIFY(game_database_mgr->GetCompetitionAdditionalInfo((int)i, addl_info));
					RUHUDUpdater::CensorCompetitionName(addl_info.competition_name);
					BaseListboxChildCallback::CompetitionInfoStruct this_struct(i, addl_info);
					competition_info_list.push_back(this_struct);
				}
			}

			std::sort(competition_info_list.begin(), competition_info_list.end(), ReversePredicate);

			UWWUICompetitionMainMenuListboxPopulator::MainMenuListboxChildCallback callbackObject(widget, competition_info_list);

			CreateNodesFromTemplate(dataList.TemplateName, competition_info_list.size(), &callbackObject);

			if (ScreenRef)
			{
#ifdef UI_USING_UMG
				ScreenRef->StoreChildWidgets();
#else
				if (ScreenRef && ScreenRef->GetStateScreen())
				{
					ScreenRef->GetStateScreen()->StoreChildWidgets();
				}
#endif
			}

			population_node = widget;
		}
	}
}

void UWWUICompetitionMainMenuListboxPopulator::PopulateAndRefresh(UWidget * widget)
{
	if (!inPreConstruct)
	{
		Populate(widget);
	}
}

UWWUICompetitionMainMenuListboxPopulator::BaseListboxChildCallback::BaseListboxChildCallback(UWidget * listbox_node_, const MabVector<CompetitionInfoStruct >& competition_info_list_)
	: listbox_node(listbox_node_)
	, competition_info_list(competition_info_list_)
	, list_index(0u)
{
}

void UWWUICompetitionMainMenuListboxPopulator::MainMenuListboxChildCallback::Callback(UUserWidget* new_node)
{

	// GG DJH: Moved Widget visual setup to be handled by the widget itself, Blueprint sided

	UWWUIListField * wwField = Cast<UWWUIListField>(new_node);
	if (!wwField->Implements<UCompetitionModePopulatedInterface>())
	{
		MABBREAKMSG("Widget does not implement Competition Mode Interface!");
		return;
	}

	UButton * button_node = wwField->GetButtonWidget();
	if (button_node == NULL)
	{
		MABBREAK();
		return;
	}

	UWWUIScreenManager* pScreenManager = SIFApplication::GetApplication()->GetUIScreenManager();
	if (!pScreenManager)
	{
		ensure(pScreenManager);
		return;
	}

	int32 MainMenuScreenIndex = pScreenManager->FindScreenTemplate(Screens_UI::MainMenu);
	UWWUIScreenMainMenu * mainMenuRef = Cast<UWWUIScreenMainMenu>(pScreenManager->GetScreenTemplate(MainMenuScreenIndex));
	if (mainMenuRef)
	{
		button_node->OnClicked.AddDynamic(mainMenuRef, &UWWUIScreenMainMenu::CompetitionLoadOnClick);
	}
	else
	{
		ensure(mainMenuRef);
	}

	const CompetitionInfoStruct& info_struct = competition_info_list[list_index];

	const size_t competition_slot_index = info_struct.slot_index;
	//const MabString competition_slot_str(0, "%u", competition_slot_index);
	//const MabString competition_one_based_slot_str(0, "%u", competition_slot_index + 1);

	const MabDate& last_modification_date = info_struct.additional_info.last_modification_time;

	FCompetitionMenuPopulationPack competitionData;
	competitionData.Competition = FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.competition_name));
	competitionData.NextMatchAwayTeam = FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.next_match_away_team));
	competitionData.NextMatchHomeTeam = FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.next_match_home_team));
	competitionData.Round = FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.round_string));
	competitionData.NumberOfUserTeams = info_struct.additional_info.num_user_teams;
	competitionData.LastModificationTime = FDateTime(last_modification_date.GetYear(), last_modification_date.GetMonth(), last_modification_date.GetDayNumber(), last_modification_date.GetHour(), last_modification_date.GetMinute(), last_modification_date.GetSeconds());
	competitionData.SaveIndex = (int)competition_slot_index;

	const FName InfoPanelWidgetName = FName("CompetitionInfoPanel");
	UWidget* infoPanel = mainMenuRef->FindChildWidget(InfoPanelWidgetName); // DJH: Should be a UUserWidget - if not, this will crash later on.

	ICompetitionModePopulatedInterface::Execute_OnCompetitionPopulated(wwField, (UUserWidget*)infoPanel, competitionData);

	//Set the text of the main button to be Load Competition n, where n corresponds to the slot.
	//int32 childIdx;
	//UTextBlock * button_text_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(button_node, FName(*MAIN_MENU_BUTTON_TEXT_NODE_NAME), childIdx));
	//if (button_text_node == NULL)
	//{
	//	MABBREAK();
	//	return;
	//}

	//FString title = FString::Printf(TEXT("%s | %s"), 
	//	*comp_name,
	//	*date_text
	//);
	//pMainMenuField->SetTitle(FText::FromString(title));

	//Process the listbox with all the additional info on it.
	//UUserWidget* addl_info_listbox_node_userwidget = Cast<UUserWidget>(UWidgetTree::FindWidgetChild(button_node, FName(*ADDL_INFO_LISTBOX_NODE_NAME), childIdx));
	//UPanelWidget* addl_info_listbox_node = Cast<UPanelWidget>(addl_info_listbox_node_userwidget->GetRootWidget());
	//if (Cast<UPanelWidget>(addl_info_listbox_node) == NULL)
	//{
	//	MABBREAK();
	//	return;
	//}

	//Handle invalid data from xbox OFFLINE mode
	//#if PLATFORM_XBOXONE
	//UBorder* blue_border = Cast<UBorder>(UWidgetTree::FindWidgetChild(button_node, FName(*ADDL_INFO_BLUE_BORDER_NAME), childIdx));
	//if (blue_border)
	//{
	//	if (info_struct.additional_info.competition_name.empty() &&
	//		info_struct.additional_info.num_user_teams == 0 &&
	//		info_struct.additional_info.next_match_away_team.empty() &&
	//		info_struct.additional_info.next_match_home_team.empty() &&
	//		info_struct.additional_info.round_string.empty())
	//	{
	//		addl_info_listbox_node->SetRenderOpacity(0.0f);
	//		blue_border->SetRenderOpacity(0.0f);
	//	}
	//	else
	//	{
	//		addl_info_listbox_node->SetRenderOpacity(1.0f);
	//		blue_border->SetRenderOpacity(1.0f);
	//	}
	//}
	//#endif 

	//Set the competition string.
	//UTextBlock* competition_name_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*COMPETITION_NAME_NODE_NAME), childIdx));
	//if (competition_name_node == NULL)
	//{
	//	MABBREAK();
	//	return;
	//}
	//
	//competition_name_node->SetText(FText::FromString(UTF8_TO_TCHAR(info_struct.additional_info.competition_name.c_str())));
	//
	////Check to see if the competition is completed.
	//const MabString COMPETITION_COMPLETE_STRING = "[ID_COMPETITION_COMPLETE]";
	//if (FString(info_struct.additional_info.round_string.c_str()).Compare(COMPETITION_COMPLETE_STRING.c_str()) == 0)
	//{
	//	//Set the round string.
	//	UTextBlock * round_string_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*ROUND_STRING_NODE_NAME), childIdx));
	//	if (round_string_node == NULL)
	//	{
	//		MABBREAK();
	//		return;
	//	}
	//	round_string_node->SetText(FText::FromString(""));
	//
	//	//Set the text to the completed string.
	//	UTextBlock * home_team_name_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*HOME_TEAM_NODE_NAME), childIdx));
	//	if (home_team_name_node == NULL)
	//	{
	//		MABBREAK();
	//		return;
	//	}
	//	home_team_name_node->SetText(FText::FromString(COMPETITION_COMPLETE_STRING.c_str()));
	//
	//
	//	UWidget * versus_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*VERSUS_TEAM_NODE_NAME), childIdx));
	//	versus_node->SetVisibility(ESlateVisibility::Hidden);
	//}
	//else
	//{
	//	//Set the round string.
	//	UTextBlock* round_string_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*ROUND_STRING_NODE_NAME), childIdx));
	//	if (round_string_node == NULL)
	//	{
	//		MABBREAK();
	//		return;
	//	}
	//	round_string_node->SetText(FText::FromString(UTF8_TO_TCHAR(info_struct.additional_info.round_string.c_str()) + FString(" -")));
	//
	//
	//	//Set the home team name
	//	UTextBlock* home_team_name_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*HOME_TEAM_NODE_NAME), childIdx));
	//	if (home_team_name_node == NULL)
	//	{
	//		MABBREAK();
	//		return;
	//	}
	//	home_team_name_node->SetText(FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.next_match_home_team)));
	//
	//	UWidget* versus_node = Cast<UWidget>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*VERSUS_TEAM_NODE_NAME), childIdx));
	//	versus_node->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	//
	//	//Set the away team name
	//	UTextBlock* const away_team_name_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*AWAY_TEAM_NODE_NAME), childIdx));
	//	if (away_team_name_node == NULL)
	//	{
	//		MABBREAK();
	//		return;
	//	}
	//	away_team_name_node->SetText(FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(info_struct.additional_info.next_match_away_team)));
	//}
	//
	////Set the user team string.
	//UTextBlock* user_team_string_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*USER_TEAM_STRING_NODE_NAME), childIdx));
	//if (user_team_string_node == NULL)
	//{
	//	MABBREAK();
	//	return;
	//}

	//Iterate through all the available competition names and find this one, so that you can correctly set the maximum number of playable teams.
	
	//int max_num_player_controllable_teams = MAX_NUM_PLAYABLE_COMPETITIONS;
	//
	//if (RL3Database* database = RL3Database::GetInstance())
	//{
	//	if (RL3TableCache* comp_definition_list = database->GetCompDefinitionCache())
	//	{
	//		for (int i = 0; i < comp_definition_list->GetNumRows(); ++i)
	//		{
	//			unsigned short db_id = comp_definition_list->GetDbIdForRow(i);
	//
	//			if (comp_definition_list->ReadString(db_id, CCDB_COMPDEF_NAME) == info_struct.additional_info.competition_name)
	//			{
	//				RL3DB_COMPETITION_DEFINITION comp_def(db_id);
	//				const int num_teams_total = comp_def.GetNumTeams();
	//				if (num_teams_total < max_num_player_controllable_teams)
	//				{
	//					max_num_player_controllable_teams = num_teams_total;
	//				}
	//				// Nick WWS &s to Womens 13s // 
	//				/*
//#ifdef ENABLE_SEVENS_MODE
	//				if (comp_def.GetIsR7Exclusive() == 1)
	//				{
	//					//void SetProperty(FString inKey, void* inData, UIPropertyType inType = _internalREUSE);
	//					FString gameModeString = "1";
	//					wwField->SetProperty("game_mode", &gameModeString, UIPropertyType::PROPERTY_TYPE_FSTRING);
	//					//button_node->SetProperty("game_mode", MabString(0, "%d", GAME_MODE_SEVENS));  //#MB - node property
	//				}
	//				else
	//				{
	//					FString gameModeString = "0";
	//					wwField->SetProperty("game_mode", &gameModeString, UIPropertyType::PROPERTY_TYPE_FSTRING);
	//				}
//#endif */
	//			}
	//		}
	//	}
	//}
	//
	//const MabString user_team_string(0, "%d/%d", info_struct.additional_info.num_user_teams, max_num_player_controllable_teams);
	//user_team_string_node->SetText(FText::FromString(user_team_string.c_str()));
	//
	//
	//// Set the last modification time string.
	//UTextBlock* last_modification_time_node = Cast<UTextBlock>(UWidgetTree::FindWidgetChild(Cast<UPanelWidget>(addl_info_listbox_node), FName(*LAST_MODIFICATION_TIME_NODE_NAME), childIdx));
	//if (last_modification_time_node == NULL)
	//{
	//	MABBREAK();
	//	return;
	//}
	//
	//const MabDate& last_modification_date = info_struct.additional_info.last_modification_time;
	//const MabString date_text(0, RUCareerModeManager::DATE_FORMAT_STRING, last_modification_date.GetDayNumber(), last_modification_date.GetMonth(), last_modification_date.GetYear(), last_modification_date.GetHour(), last_modification_date.GetMinute());
	//last_modification_time_node->SetText(FText::FromString(date_text.c_str()));

	// Now handle the button node itself.
	const MabString listbox_index_str(0, "%d", list_index);

	FString newName = wwField->GetName();
	newName += listbox_index_str.c_str();
	//wwField->Rename(*newName);
	int slotNum = (int)competition_slot_index;
	wwField->SetProperty(COMPETITION_SLOT_PROPERTY_NAME, &slotNum, UIPropertyType::PROPERTY_TYPE_INT);
	//button_node->SetProperty(COMPETITION_SLOT_PROPERTY_NAME, competition_slot_str);		//node property

	int gm = (int)GameModeType::Competition;
	wwField->SetProperty(CAREER_MODE_PROPERTY_NAME, &gm, UIPropertyType::PROPERTY_TYPE_INT);

	Cast<UPanelWidget>(listbox_node)->AddChild(new_node);

	if (list_index == competition_info_list.size() - 1)
	{
		Cast<UPanelWidget>(listbox_node)->GetChildAt(list_index)->SetNavigationRule(EUINavigation::Down, EUINavigationRule::Explicit, FName(*WWUIScreenMainMenu_UI::NewCompetition));
		Cast<UPanelWidget>(listbox_node)->GetChildAt(0)->SetNavigationRule(EUINavigation::Up, EUINavigationRule::Explicit, FName(*WWUIScreenMainMenu_UI::NewCompetition));
	}

	if ((list_index + 1) % 2 == 0)
	{
		new_node->SetPadding(FMargin(0.0f, 0.0f, 0.0f, 10.0f));
	}

	++list_index;

	//button_node->GetUIManager()->DeferredDelete(new_node);
}

