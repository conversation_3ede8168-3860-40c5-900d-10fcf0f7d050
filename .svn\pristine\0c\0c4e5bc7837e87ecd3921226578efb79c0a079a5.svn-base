/*--------------------------------------------------------------
|        Copyright (C) 1997-2012 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#pragma once

#include "CoreMinimal.h"
#include "WWUIPopulator.h"
#include "Databases/RUGameDatabaseManager.h"
#include "WWUICareerMainMenuListboxPopulator.generated.h"

UCLASS()
class RUGBY_API UWWUICareerMainMenuListboxPopulator : public UWWUIPopulator
{
	GENERATED_BODY()

public:
	UWWUICareerMainMenuListboxPopulator();
	UWWUICareerMainMenuListboxPopulator( const RUGameDatabaseManager * game_database_mgr_ );
	virtual ~UWWUICareerMainMenuListboxPopulator();

	virtual void Refresh(UWidget* node);
	virtual void Populate(UWidget* node);
	virtual void PopulateAndRefresh(UWidget* node);

	virtual bool ShouldDeferPopulationEvent(UWidget* /*node*/) { return false; }

private:
	FString NEW_COMPETITION_NODE_NAME = FString("New");

	class BaseListboxChildCallback : public NodeCreationCallback
	{
	public:

		struct CareerInfoStruct
		{
			size_t slot_index;
			RUGameDatabaseManager::CareerCoachAdditionalInfo additional_info;

			CareerInfoStruct( size_t slot_index_, const RUGameDatabaseManager::CareerCoachAdditionalInfo additional_info_ )
				: slot_index( slot_index_ )
				, additional_info( additional_info_ )
			{}
		};

		BaseListboxChildCallback(UWidget * listbox_node_, const MabVector< CareerInfoStruct > career_info_list_ ) ;
		virtual ~BaseListboxChildCallback() {}

	protected:
		UWidget* listbox_node = nullptr;

		MabVector< CareerInfoStruct > career_info_list;

		size_t list_index = 0u;
	};

	class MainMenuListboxChildCallback : public BaseListboxChildCallback
	{
	public:
		MainMenuListboxChildCallback(UWidget * listbox_node_, const MabVector< CareerInfoStruct > career_info_list_ )
			: BaseListboxChildCallback( listbox_node_, career_info_list_ )
		{
		}
		virtual ~MainMenuListboxChildCallback() {}

		virtual void Callback(UUserWidget* new_node );

	private:
		FString BUTTON_NODE_NAME = FString("Button_Function");
		const FString MAIN_MENU_BUTTON_TEXT_NODE_NAME = FString("Text");
		const FString POPUP_BUTTON_UNSELECTED_NODE_NAME = FString("UnselectedText/TextWidget:Unselected");
		const FString POPUP_BUTTON_SELECTED_NODE_NAME = FString("SelectedText/TextWidget:Selected");
		const FString ADDL_INFO_LISTBOX_NODE_NAME = FString("SaveInfoListBox");
		const FString ADDL_INFO_BLUE_BORDER_NAME = FString("BlueBorder");
		const FString COMPETITION_NAME_NODE_NAME = FString("CompName");
		const FString ROUND_STRING_NODE_NAME = FString("RoundName");
		const FString HOME_TEAM_NODE_NAME = FString("HomeTeam");
		const FString VERSUS_TEAM_NODE_NAME = FString("vs");
		const FString AWAY_TEAM_NODE_NAME = FString("AwayTeam");
		const FString USER_TEAM_STRING_NODE_NAME = FString("UserTeamsValue");
		const FString LAST_MODIFICATION_TIME_NODE_NAME = FString("LastModificationTimeValue");
		const FString CLUB_TEAM_NODE_NAME = FString("ClubTeam");
		const FString INTERNATIONAL_TEAM_NODE_NAME = FString("InternationalTeam");
		const FString AMPERSAND_NODE_NAME = FString("and");
		const FString POPUP_TEMPLATE_RESOURCE_NAME = "popup_button";
		const FString MAIN_MENU_TEMPLATE_RESOURCE_NAME = "career_main_menu_listbox_template";
		const FString COMPETITION_SLOT_PROPERTY_NAME = "slot";
		const FString CAREER_MODE_PROPERTY_NAME = "careerMode";
		const FString ONCLICK_METHOD_NAME = "TooManyCareersPopup.OnClick";
	};

	/// Helper method to sort the career_info_list properly.
	static bool ReversePredicate( const BaseListboxChildCallback::CareerInfoStruct& left, const MainMenuListboxChildCallback::CareerInfoStruct& right );

	const RUGameDatabaseManager* game_database_mgr;
	UWidget* population_node = nullptr;

	const int MAX_NUM_PLAYABLE_COMPETITIONS = 8;
};
