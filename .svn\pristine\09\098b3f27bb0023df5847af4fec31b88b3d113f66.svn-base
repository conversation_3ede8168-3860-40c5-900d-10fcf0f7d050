/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/RUTackleHelper.h"

#include "Match/AI/Actions/RUActionFend.h"
#include "Match/AI/Actions/RUActionPass.h"
#include "Match/AI/Actions/RUActionScoreTry.h"
#include "Match/AI/Actions/RUActionSideStep.h"
#include "Match/AI/Actions/RUActionTacklee.h"
#include "Match/Ball/SSBall.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Character/RugbyPlayerController.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUStadiumManager.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/Offside/RUOffsideIndicator.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/RugbyUnion/Statistics/RUStatisticsSystem.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSGameTimer.h"
#include "Match/SSMath.h"
#include "Match/SSPlayerFilter.h"
#include "Match/SSSpatialHelper.h"
#include "Utility/RURandomNumberGenerator.h"

//#rc3_legacy_include #include "Match/Debug/RUGameDebugSettings.h"
//#rc3_legacy_include #include "SIFDebug.h"
//#rc3_legacy_include #include "TackleDef.h"
//#rc3_legacy_include #include "TryTackleDef.h"
//#rc3_legacy_include #include <NMMabMath.h>

#include "Character/RugbyPlayerController.h"
#include "RugbyGameInstance.h"

#ifdef ENABLE_TRIGGER_RULES_DEBUG_SETTINGS
//#rc3_legacy_include #include "Match/Debug/SIFCommonDebugSettings.h"
#include "Rugby\Match\RugbyUnion\Rules\RURulesDebugSettings.h"
#endif
#if !UE_BUILD_SHIPPING
#include "Utility/consoleVars.h"
#include "Rugby/Match/AI/Roles/Competitors/RURoleFullback.h"
#endif
#include <set>

#include "../AI/Roles/Competitors/SetPlays/RURoleSetplay.h"
#include "../AI/Roles/Competitors/SetPlays/RURolePlayTheBall.h"


#if PLATFORM_WINDOWS || PLATFORM_XBOXONE
#ifdef SUPPRESS_DEPRECATION_WARNINGS
#pragma warning(push)
#pragma warning(disable : 4996)
#endif
#endif

class TackleDef;
class TryTackleDef;

//#define ENABLE_ALWAYS_SUSPEND
//#define ENABLE_ALWAYS_INJURED_IN_TACKLE
//#define ENABLE_NEVER_INJURED_IN_TACKLE
//#define ENABLE_ALWAYS_HEAD_HIGH_TACKLE

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

//TODO: RUPort - Disabled due to warnings
static int MASTER_TACKLE_DETERMINATION_COUNT = 0;

/// Global constants for the tackle system
const float AGRESSION_MULTIPLIER = 0.30f;
const float POSITION_DIRECTION_MULTIPLIER = 1.00f;
const float IMPETUS_VARIANCE = 0.20f;
const float WEAKER_PLAYER_CONTRIBUTON = 0.30f;
const float MAX_OPP_OBSTRUCTION_DISTANCE = 0.5f; // Maximum distance a player can be from the tackler-tacklee vector to not be considered an obstruction
const float SQ_MAX_OPP_OBSTRUCTION_DISTANCE = MAX_OPP_OBSTRUCTION_DISTANCE * MAX_OPP_OBSTRUCTION_DISTANCE; // Squared dist for speed
const float MAX_OUR_OBSTRUCTION_DISTANCE = 0.1f; // Maximum distance a player can be from the tackler-tacklee vector to not be considered an obstruction
const float SQ_MAX_OUR_OBSTRUCTION_DISTANCE = MAX_OUR_OBSTRUCTION_DISTANCE * MAX_OUR_OBSTRUCTION_DISTANCE; // Squared dist for speed
const float SIDE_TACKLE_COS_ANGLE = MabMath::Cos( MabMath::Deg2Rad( 45.0f ) );
const float REAR_TACKLE_COS_ANGLE = MabMath::Cos( MabMath::Deg2Rad( 45.0f + 90.0f ) );

/// Tacklee to tackler impetus ratio threshholds
const float TAKEN_DOWN_THRESHOLD = 0.5f;
const float HELD_THRESHOLD = 1.06f;
const float BREAK_TACKLE_THRESHOLD = 1.27f;
const float BUSTED_OVER_THRESHOLD = 1.5f;
const float MIN_TACKLE_SPEED_FOR_BREAKOUTS = 4.0f;

// Some of the general prefixes
static const char* TACKLEE_PREFIX = "tacklee_";
static const char* TACKLER_PREFIX = "tackler_";


const char* RUTackleHelper::TACKLE_TYPES[] =
{
	"null",
	"standard",
	"contested",
	"sidestep",
	"fend",
	"try",
	"ankle_tap",
	"getup",
	"head_high",
	"multi_man",
	"dive_miss",
	"try_pushed",
	"try_corner",
	"null"
};

const char* RUTackleHelper::TACKLE_DOMINANCES[] =
{
	"a2",
	"a1",
	"eq",
	"d1",
	"d2",
	"null"
};

const char* RUTackleHelper::TACKLE_HEIGHTS[] =
{
	"ankle",
	"waist",
	"chest",
	"head",
	"null"
};

const char* RUTackleHelper::TACKLE_TRY_PROBABILITIES[] =
{
	"LikelyTry",
	"MarginalTry",
	"UnlikelyTry",
	"HeldUpTry",
	"PushBackTry",
	"JumpOverTry",
	"null"
};

const char* RUTackleHelper::TACKLE_SIDESTEP_TYPES[] =
{
	"fooled",
	"partial_fail",
	"partial_success",
	"not_fooled",
	"null"
};


/// Initilaise the tackle helper system
RUTackleHelper::RUTackleHelper( SIFGameWorld* _game )
: game(_game),
  min_injury_probability( 0.0f ),
  forced_tackle_result_set( false ),
  forced_tackle_result()
#ifdef DEBUG_DIVE_TACKLE
  ,debug_tackle(false)
#endif
{
	Reset();
}

/// Cleanup the tackle helper system
RUTackleHelper::~RUTackleHelper()
{
}

int RUTackleHelper::GetMasterTackleDeterminationIndex() const
{
	return MASTER_TACKLE_DETERMINATION_COUNT;
}

/*static*/ void RUTackleHelper::ClearMasterTackleDeterminationIndex()
{
	MASTER_TACKLE_DETERMINATION_COUNT= 0;
}

void RUTackleHelper::Reset()
{
	min_injury_probability = PLAYER_PROFILE_INJURY_FREQUENCY_MIN; // Added to the profile settings instead

#ifdef DEBUG_DIVE_TACKLE
	DebugTackle( false );
#endif
}
void RUTackleHelper::GameReset()
{
	Reset();
	forced_tackle_result.GameReset();
	//const float MIN_INJURY_PROBABILITY = 0.96f; // 4%
	//min_injury_probability = MIN_INJURY_PROBABILITY;
	forced_tackle_result_set = false;
	forced_tackle_result.Reset();
}
void RUTackleHelper::printState()
{
#ifdef _DEBUG
	//game
	forced_tackle_result.printState();
	MABLOGDEBUG("TCL: %f %d",min_injury_probability,forced_tackle_result_set);
#endif
}

/// Helper function to modify the aggression of a player based on their
/// aggressiveness
void ModifyPlayerAggressionBasedOnAgressivensss( ARugbyCharacter* player, float* p_agg_to_update )
{
	float aggressiveness = player->GetAttributes()->GetAggressiveness();

	const float MAX_AGG_MODIFIER = 0.18f;

	// Rework players aggressiveness from -1.0f to 1.0f
	float factor = (aggressiveness - 0.5f)/(0.5f);
	MabMath::Clamp( factor, -1.0f, 1.0f );

	*p_agg_to_update *= (1.0f + (factor * MAX_AGG_MODIFIER));
}



#ifdef DEBUG_DIVE_TACKLE

#define DD_TEXT_HUMAN(player, buffer, i)		if ( player->GetHumanPlayer() ) \
												{ 	char msg[1024];							 \
													sprintf( msg, "%s, %4.2f ", buffer, player->GetGame()->GetSimTime()->GetAbsoluteTime().ToSeconds() ); \
													DD_TEXT(msg, i); }

#define DD_TEXT(buffer, i)		if ( debug_tackle ) \
								{ MABLOGMSG(  LOGCHANNEL_ALWAYS, LOGTYPE_ALWAYS, "RUTackleHelper: %s", buffer );	\
								SIF_DEBUG_DRAW( SetText((long)this+i, 650, 450+12*(float)i, buffer, MabColour::Yellow) ); }

#define DD_LINE(p1, p2, i)		if ( debug_tackle ) \
								SIF_DEBUG_DRAW( Set3DLine((long)this+100+i, p1, p2, MabColour::Yellow, MabColour::Yellow) );

#define DD_REMOVE_TEXT(i)		SIF_DEBUG_DRAW( RemoveText((long)this+i) )
#define DD_REMOVE_LINE(i)		SIF_DEBUG_DRAW( Remove3DLine((long)this+100+i) )


void RUTackleHelper::DebugTackle( bool debug )
{
	debug_tackle = debug;

	if ( debug == false )
	{
		for( int i=0; i < 20; i++ )
		{
			DD_REMOVE_TEXT(i);
			DD_REMOVE_LINE(i);
		}
	}
	else
	{
		if ( game->GetGameSettings().game_settings.game_type != GAME_TRAINING && game->GetGameSettings().game_settings.game_type != GAME_MENU)
		{
			char buffer[1024];
			sprintf( buffer, "Debug Tackle: Current Time: %4.2f ", game->GetSimTime()->GetAbsoluteTime().ToSeconds() );
			DD_TEXT( buffer, 0 );
		}
	}
}

#endif


/*
	Held*					HL	Ankle	A	Impact					IM	Head On		HO
	Taken Down*				TD	Waist	W	Held Loop				HL	From Left	FL
	Ball In All				BA	Chest	C	Release From Held		HR	From Right	FR
	Driven					DR	Head	H	Break Out From Held		BO	From Behind	FB
	Busted Over*			BU				Taken Down From Held	TD
	Brushed me Off*			BO				Ground Struggle Loop	GS
	Spear Tackle			ST				Ground Release			GR
	Head High Tackle		HH
	Smashed Em*				SM
	Tackled Without Ball	WB
	Ankle Tap				AT
	Two Man Tackle*			TM
	Missed					MI
	Jump Over (after a try is scored)	JO
*/

bool RUTackleHelper::IsTackleCandidate( ARugbyCharacter* player, bool ignore_interruptable )
{
	MABUNUSED(ignore_interruptable);
	ARugbyCharacter* ball_holder = game->GetGameState()->GetBallHolder();

#ifdef DEBUG_DIVE_TACKLE
	int dd_text_index = 2;
	DD_TEXT_HUMAN(player,"RUTackleHelper::IsTackleCandidate",dd_text_index);
	dd_text_index++;
#endif

	// Dont tackle if we are not standard play
	if ( !game->GetGameState()->IsGameInStandardPlay() )
	{
#ifdef DEBUG_DIVE_TACKLE
		DD_TEXT_HUMAN(player,"game_phase != phase_play",dd_text_index);
#endif
		return false;
	}

	// if there's no ballholder he's not
	if ( ball_holder == NULL )
	{
#ifdef DEBUG_DIVE_TACKLE
		DD_TEXT_HUMAN(player,"ball_holder == NULL",dd_text_index);
#endif
		return false;
	}

	// Is the tackler and ballholder on opp teams
	if ( ball_holder->GetAttributes()->GetTeam() == player->GetAttributes()->GetTeam() )
	{
#ifdef DEBUG_DIVE_TACKLE
		DD_TEXT_HUMAN(player,"player and ballholder are in same team",dd_text_index);
#endif
		return false;
	}

	// Can a new tackle be started on the ball holder?
	if ( ball_holder->GetActionManager()->ShouldPrewindAction( ACTION_TACKLEE ) )
	{
#ifdef DEBUG_DIVE_TACKLE
		DD_TEXT_HUMAN(player,"ball_holder already tackled",dd_text_index);
#endif
		return false;
	}

	// Can the tacklee do the tackle?
	if ( !player->GetRole() || !player->GetActionManager()->CanUseAction( ACTION_TACKLER ) )
	{
#ifdef DEBUG_DIVE_TACKLE
		DD_TEXT_HUMAN(player,"player has no role or cant use tackler action",dd_text_index);
#endif
		return false;
	}

	RUStrategyHelper* strategy_helper = game->GetStrategyHelper();
	MABASSERT( strategy_helper != NULL );

	if ( !strategy_helper->ShouldStillAttemptTackleOnBallHolder() )
	{
#ifdef DEBUG_DIVE_TACKLE
		DD_TEXT_HUMAN(player,"ShouldStillAttemptTackleOnBallHolder failed",dd_text_index);
#endif
		return false;
	}

	// If the player is an AI player on a human team then they should not tackle if they
	// are offside
	if ( player->GetAttributes()->GetTeam()->GetNumHumanPlayers() > 0 && player->GetHumanPlayer()== NULL &&
		 strategy_helper->WillTacklerBeOffside( player, 9.5f ) )
	{
#ifdef DEBUG_DIVE_TACKLE
		DD_TEXT_HUMAN(player,"AI player offside",dd_text_index);
#endif
		return false;
	}

	// If play is suspended. Tackles are not a go.
	// Todo: Technically this is checked within !strategy_helper->ShouldStillAttemptTackleOnBallHolder(). Find out why the check isn't working.
	if ( game->GetRules()->IsPlaySuspended() )
	{
#ifdef DEBUG_DIVE_TACKLE
		DD_TEXT_HUMAN(player,"Play Suspended",dd_text_index);
#endif
		return false;
	}

	// TEMP DEBUG
	/*
	if ( MabMath::Fabs( tackle_player->GetCurrentPosition().z ) < 49.5f )
		return false;
	*/

#ifdef DEBUG_DIVE_TACKLE
	DD_TEXT_HUMAN(player,"tackle candidate : success",dd_text_index);
#endif

	return true;
}

bool RUTackleHelper::DetermineTackleResult( ARugbyCharacter* tackler, bool heavy_tackle, SIDESTEP_SIDE sidestep_side, RUTackleResult &result )
{
	if ( !DetermineTackleResultInternal(tackler, heavy_tackle, sidestep_side, result) )
	{
		return false;
	}

	result.FallBackToMostAppropriateAvailableTackle();

	if ( forced_tackle_result_set )
	{
		forced_tackle_result_set = false;

		// abort forced tackle in some circumstances
		// forced tackles are used in LineOutToMaul code, but you can get in a situation where
		// the forced tackle has been set but the ball has gotten away, so the tackle is waiting
		// and then any tackles after that (including dive_miss, which doesn't even affect the ball holder)
		// will fire it
		// so awkward conditions:
		// only abort forced tackle during normal gameplay (tutorials need them)
		// abort if calculated tackle was a dive misses
		// abort if the time since lineout requested it has been longer than X

		bool abort_forced_tackle = false;

		const float FORCE_TACKLE_TIMEOUT = 2.0f;

		if (!game->IsMatch())
			abort_forced_tackle = false;
		else if (result.tackle_result == TRT_DIVE_MISS)
			abort_forced_tackle = true;
		else if (game->GetGameState()->GetTimeSinceLineout() > FORCE_TACKLE_TIMEOUT)
			abort_forced_tackle = true;

		if (!abort_forced_tackle)
		{
			result.tackle_result = forced_tackle_result.tackle_result;
			result.held = forced_tackle_result.held;
			result.taken_down = forced_tackle_result.taken_down;
			result.successful = forced_tackle_result.successful;
			result.anim_sequences = forced_tackle_result.anim_sequences;
			result.drop_ball_type = TDT_NONE;
			result.state_machine = forced_tackle_result.state_machine;
			result.dominance = forced_tackle_result.dominance;
			result.ss_tackle_type = forced_tackle_result.ss_tackle_type;
			result.try_tackle_type = forced_tackle_result.try_tackle_type;

			if ( forced_tackle_result.body_position[0] != TACKLE_BODY_POSITION::TBP_UNKNOWN )
			{
				result.body_position[0] = forced_tackle_result.body_position[0];
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
				result.body_position[1] = forced_tackle_result.body_position[1];
#else
				MABASSERT(MAX_TACKLERS == 1); // #rc3_legacy below errors on clang
#endif 
				//int max_tac = MAX_TACKLERS;
				//if (max_tac > 1)
				//	result.body_position[1] = forced_tackle_result.body_position[1];
				//if (max_tac > 2)
				//	result.body_position[2] = forced_tackle_result.body_position[2];
			}
		}
	}

	//// TYRONE: Always print out what animation we are using in debug
	#ifdef ENABLE_RUGED
	if ( result.tacklee != NULL )
	{
		MabString tacklee_anim_name = result.GetTackleeAnimationName();
		//bool anim_exists = NMMabAnimationRepository::GetAnimationReference( tacklee_anim_name.c_str() ).IsValid();
		MABLOGMSG(LOGCHANNEL_GFX, LOGTYPE_INFO, "Using tackle animation %s", tacklee_anim_name.c_str() );
	}
	// Print out the details
	MABLOGDEBUG( "Tackle Result%s", result.GetDebugString().c_str() );

	#endif

	#ifdef ENABLE_OSD
		RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
		settings->PushDebugString( game, RUGameDebugSettings::DP_TACKLE, result.GetShortDebugString().c_str() );
		//SIFApplication::GetApplication()->GetAppTime()->GetSimulationTimeSource()->Pause( true );
	#endif

	/// Store the initial movement state
	if ( result.tacklee )
	{
		result.init_tacklee_movement.state  = result.tacklee->GetMovement()->GetMovementState();
		result.init_tacklee_movement.istate = result.tacklee->GetMovement()->GetMovementIntermediateState();
		result.init_tacklee_movement.params = result.tacklee->GetMovement()->GetMovementParameters();
	}

	for( int i = 0; i < result.n_tacklers; i++ )
	{
		if ( i < MAX_TACKLERS && result.tacklers[i] )
		{
			result.init_tackler_movement[i].state  = result.tacklers[i]->GetMovement()->GetMovementState();
			result.init_tackler_movement[i].istate = result.tacklers[i]->GetMovement()->GetMovementIntermediateState();
			result.init_tackler_movement[i].params = result.tacklers[i]->GetMovement()->GetMovementParameters();
		}
	}

	return true;
}

/// Structure controlling how dominance affects:
///   * Aggression
///   * Success/Failure of tackle
typedef struct
{
	TACKLE_DOMINANCE dominance;
	float min_impetus_ratio;
	float tacklee_aggression_mult;
	float tackler_aggression_mult;
	float prob_success_ai[DIF_MAX][GAME_MODE_MAX];
	float prob_success_human[DIF_MAX][GAME_MODE_MAX];
} TACKLE_DOMINANCE_META;

static const TACKLE_DOMINANCE_META tackle_dominance_meta[5] =
{
				///	Dominance		Min Imp Ratio	Tke agg mult	Tkr agg mult	Success prob
	/* A2 */	{	TACKLE_DOMINANCE::TDOM_ATTACK2,	1.36f,			0.7f,			0.3f,
																					// AI
																					// R15, R7
																					{{0.3f, 0.35f },	// DIF_VERYEASY
																					{ 0.3f, 0.43f },	// DIF_EASY
																					{ 0.3f, 0.50f },	// DIF_NORMAL
																					{ 0.3f, 0.58f },	// DIF_HARD
																					{ 0.3f, 0.66f }}	// DIF_PRO
																					,
																					// HUMAN
																					// R15, R7
																					{{0.3f, 0.90f },	// DIF_VERYEASY
																					{ 0.3f, 0.83f },	// DIF_EASY
																					{ 0.3f, 0.78f },	// DIF_NORMAL
																					{ 0.3f, 0.73f },	// DIF_HARD
																					{ 0.3f, 0.70f }}	// DIF_PRO
},
	/* A1 */	{	TACKLE_DOMINANCE::TDOM_ATTACK1,	1.15f,			0.6f,			0.4f,
																					// AI
																					// R15, R7
																					{{0.4f, 0.35f },	// DIF_VERYEASY
																					{ 0.4f, 0.43f },	// DIF_EASY
																					{ 0.4f, 0.50f },	// DIF_NORMAL
																					{ 0.4f, 0.58f },	// DIF_HARD
																					{ 0.4f, 0.66f }}	// DIF_PRO
																					,
																					// HUMAN
																					// R15, R7
																					{{0.4f, 0.90f },	// DIF_VERYEASY
																					{ 0.4f, 0.83f },	// DIF_EASY
																					{ 0.4f, 0.78f },	// DIF_NORMAL
																					{ 0.4f, 0.73f },	// DIF_HARD
																					{ 0.4f, 0.70f }}	// DIF_PRO
	},
		/* EQ */	{	TACKLE_DOMINANCE::TDOM_ATTACK_EQ,	0.95f,			0.6f,			0.4f,
																					// AI
																					// R15, R7
																					{{0.6f, 0.35f },	// DIF_VERYEASY
																					{ 0.6f, 0.43f },	// DIF_EASY
																					{ 0.6f, 0.50f },	// DIF_NORMAL
																					{ 0.6f, 0.58f },	// DIF_HARD
																					{ 0.6f, 0.66f }}	// DIF_PRO
																					,
																					// HUMAN
																					// R15, R7
																					{{0.6f, 0.90f },	// DIF_VERYEASY
																					{ 0.6f, 0.83f },	// DIF_EASY
																					{ 0.6f, 0.78f },	// DIF_NORMAL
																					{ 0.6f, 0.73f },	// DIF_HARD
																					{ 0.6f, 0.70f }}	// DIF_PRO
	},
		/* D1 */	{	TACKLE_DOMINANCE::TDOM_DEFENCE1,	0.5f,			0.6f,			0.4f,
																					// AI
																					// R15, R7
																					{{1.0f, 0.35f },	// DIF_VERYEASY
																					{ 1.0f, 0.43f },	// DIF_EASY
																					{ 1.0f, 0.50f },	// DIF_NORMAL
																					{ 1.0f, 0.58f },	// DIF_HARD
																					{ 1.0f, 0.66f }}	// DIF_PRO
																					,
																					// HUMAN
																					// R15, R7
																					{{1.0f, 1.0f },	// DIF_VERYEASY
																					{ 1.0f, 1.0f },	// DIF_EASY
																					{ 1.0f, 1.0f },	// DIF_NORMAL
																					{ 1.0f, 1.0f },	// DIF_HARD
																					{ 1.0f, 1.0f }}	// DIF_PRO
	},
		/* D2 */	{	TACKLE_DOMINANCE::TDOM_DEFENCE2,	0.3f,			0.6f,			0.4f,
																					// AI
																					// R15, R7
																					{{1.0f, 0.35f },	// DIF_VERYEASY
																					{ 1.0f, 0.43f },	// DIF_EASY
																					{ 1.0f, 0.50f },	// DIF_NORMAL
																					{ 1.0f, 0.58f },	// DIF_HARD
																					{ 1.0f, 0.66f }}	// DIF_PRO
																					,
																					// HUMAN
																					// R15, R7
																					{{1.0f, 1.0f },	// DIF_VERYEASY
																					{ 1.0f, 1.0f },	// DIF_EASY
																					{ 1.0f, 1.0f },	// DIF_NORMAL
																					{ 1.0f, 1.0f },	// DIF_HARD
																					{ 1.0f, 1.0f }}	// DIF_PRO
	}
};
#pragma optimize("", off)
bool RUTackleHelper::DetermineTackleResultInternal( ARugbyCharacter* tackler, bool heavy_tackle, SIDESTEP_SIDE sidestep_side, RUTackleResult& result )
{
	// Determine what type of tackle the player will perform
	// and return the result in result
	ARugbyCharacter* primary_tackler = tackler;
	MABASSERT( primary_tackler );
	result.Reset();

#ifdef DEBUG_DIVE_TACKLE
	int dd_text_index = 2;
	DD_TEXT_HUMAN(tackler,"RUTackleHelper::DetermineTackleResultInternal",dd_text_index);
	dd_text_index++;
#endif

	// The tackler is not allowed to already be tackling another player
	if ( primary_tackler->GetRole() && primary_tackler->GetActionManager()->IsActionRunning( ACTION_TACKLER ) )
	{

#ifdef DEBUG_DIVE_TACKLE
		DD_TEXT_HUMAN(tackler,"player already tackler",dd_text_index);
#endif

		result.tackle_result = TRT_NO_TACKLE;
		return false;
	}

	RUTeam* defending_team = primary_tackler->GetAttributes()->GetTeam();

	// Ball Holder PID - NOTE ball is allowed to be free, tackles can be made on players without the ball
	ARugbyCharacter* ball_holder = game->GetGameState()->GetBallHolder();

	/// Find out where ball holder is in relation to us
	RUTackleRangeMeta range_meta;

	if ( ball_holder )
	{
		GetTackleRangeMeta( ball_holder, primary_tackler, range_meta );
	}
	else
	{
#ifdef DEBUG_DIVE_TACKLE
		DD_TEXT_HUMAN(tackler,"no ball holder",dd_text_index);
#endif

		result.tackle_result = TRT_NO_TACKLE;
		return false;
	}

	//GGS SRA: Determining if an injured player is still getting up. If so, don't tackle them again
	if (ball_holder->GetRoleSafe<RURolePlayTheBall>())
	{
		MABLOGDEBUG("Ball holder is in Play The Ball role, tackle failed");
		return false;
	}

	bool potential_side_step = sidestep_side != SSS_UNKNOWN;
	if ( !potential_side_step && !range_meta.in_normal_tackle_range )
	{
		if ( range_meta.in_dive_miss_range )
		{
			if ( IsADivingMiss( ball_holder, primary_tackler, result ) )
			{
#ifdef DEBUG_DIVE_TACKLE
				DD_TEXT_HUMAN(tackler,"dive_miss success",dd_text_index);
#endif
				return true;
			}
			else
			{
				result.tackle_result = TRT_NO_TACKLE;
				return false;
			}
		}


#ifdef DEBUG_DIVE_TACKLE
		DD_TEXT_HUMAN(tackler,"not in normal tackle range",dd_text_index);
#endif

		result.tackle_result = TRT_NO_TACKLE;
		return false;
	}

	//------------------------------------------------------------------
	// Determine which players are likely to be involved in the tackle
	//------------------------------------------------------------------

	RLP_FILTERPARAMETERS params;
	RLPResultList attacking_players;

	// Now find all players in the tackle zone
	const static float MAX_OBS_DIST = 5.0f;
	FVector tackler_pos = primary_tackler->GetMovement()->GetCurrentPosition();
	RLPResultList players;
	params.filters = RLP_FILTER_MAX_DIST;
	params.max_dist_x_ref = tackler_pos.x;
	params.max_dist_z_ref = tackler_pos.z;
	params.max_dist = MAX_OBS_DIST;
	game->GetPlayerFilters()->GetPlayerPlayerDistanceSort()->SetReferencePlayer( tackler );
	game->GetFilteredPlayerList( players, params, game->GetPlayerFilters()->GetPlayerPlayerDistanceSort() );

	// This player is the target in the range
	// If there is a defensive or offensive player in or close to being in the way then
	// do not attempt the tackle
	FVector tacklee_to_tackler = tackler_pos - ball_holder->GetMovement()->GetCurrentPosition();
	FVector tackler_to_tacklee = -tacklee_to_tackler;
	float sq_dist;
	bool tackler_is_obstructed = false;
	float t;

	// Iterate over all players and check for obstruction
	for( RLPResultList::iterator it = players.begin(); !(it == players.end()); ++it )
	{
		if ( (*it).player == tackler || (*it).player == ball_holder )
			continue;

		t = SSMath::GetLineParametricValue( (*it).player->GetMovement()->GetCurrentPosition(), tackler_pos, tackler_to_tacklee );

		if ( t < 0.0f || t > 1.0f )
			continue;

		sq_dist = SSMath::SquaredDistanceFromPointToSegment(
			(*it).player->GetMovement()->GetCurrentPosition(),
			tackler_pos,
			tackler_to_tacklee,
			t
		);

		// Check to see if one of our players or the opposition is obstrcuting us from tackling them
		if ( ((*it).player->GetAttributes()->GetTeam() == tackler->GetAttributes()->GetTeam() && sq_dist < SQ_MAX_OUR_OBSTRUCTION_DISTANCE) ||
			 ((*it).player->GetAttributes()->GetTeam() != tackler->GetAttributes()->GetTeam() && sq_dist < SQ_MAX_OPP_OBSTRUCTION_DISTANCE) )
		{
			// A player is obstructing so quit
			tackler_is_obstructed = true;
			break;
		}
	}

	ARugbyCharacter* player_to_tackle = NULL;
	if ( !tackler_is_obstructed )
		player_to_tackle = ball_holder;

	MABASSERT( ball_holder != tackler );

	// Bail out if there was nobody suitable to tackle
	if ( player_to_tackle == NULL )
	{
#ifdef DEBUG_DIVE_TACKLE
		DD_TEXT_HUMAN(tackler,"no players to tackle",dd_text_index);
#endif
		result.tackle_result = TRT_NO_TACKLE;
		return false;
	}

	//-----------------------------------------------------------------
	// Now work out if there is a team mate available
	// to aid in the tackle (and if it is appropriate for them to join)
	//-----------------------------------------------------------------

	ARugbyCharacter* tacklee = player_to_tackle;

	// Determine the body position
	// NOTE: This will need to take into account the closure rate of the tacklee/tackler at some stage
	TACKLE_BODY_POSITION tackle_body_position = sidestep_side != SSS_UNKNOWN ? TACKLE_BODY_POSITION::TBP_CHEST : GetTackleBodyPosition( game->GetSpatialHelper()->GetPlayerToPlayerDistance( tackler, player_to_tackle ) );

	// Determine the heavy tackle result type
	result.tackle_heavy_type = GetHeavyTackleResult( player_to_tackle, tackler, heavy_tackle );

#ifdef ENABLE_GAME_DEBUG_MENU
#ifdef ENABLE_TRIGGER_RULES_DEBUG_SETTINGS
	if(SIFDebug::GetRulesDebugSettings()->IsNextTackleHigh())
	{
		result.tackle_heavy_type = THT_HIGH_TACKLE;
	}
#endif
#endif

	// Initialise the tacklee
	result.tacklee = player_to_tackle;

	// Initialise the number of tacklers to 1 - it will increment as we find more valid ones
	result.tacklers[0] = tackler;
	result.n_tacklers = 1;

	if ( player_to_tackle == ball_holder )
	{
		// If the tackle direction is head on then we assume that we can alter
		// the body position regardless of whether it is ankle or not
		//if ( result.tackled_from_direction[0] == TFD_FRONT ) {

			// Check to see if there are any other team members
			// within striking distance of the proposed tacklee
			const float MAX_SUPPORT_TACKLE_DISTANCE = MAX_TACKLE_DISTANCE * 1.32f * 2.0f;
			FVector tacklee_pos = tacklee->GetMovement()->GetCurrentPosition();
			RLPResultList team_mates_in_range;
			params.filters |= RLP_FILTER_TEAM;
			params.team = defending_team;
			params.max_dist_x_ref = tacklee_pos.x;
			params.max_dist_z_ref = tacklee_pos.z;
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
			params.max_dist = MAX_SUPPORT_TACKLE_DISTANCE;
#endif
			game->GetFilteredPlayerList( team_mates_in_range, params, game->GetPlayerFilters()->GetPlayerPlayerDistanceSort() );

			// Find the closest team mate within the tackle distance that is facing the right direction
			// with respect to the tackler (from left, from right or head on)
			//float closest_appropriate_dist = 0.0f;
			//TACKLED_FROM_DIRECTION tackled_from_dir_type;
			//FVector tacklee_to_support_tackler;
			float dist;

#if wwDEBUG_ARB
			ERugbyAnim_Mode_Tackles tackleMode = player_to_tackle->GetAnimation()->GetStateMachine().GetSMTackle()->GetCurrentTackleMode();
			if (tackleMode != ERugbyAnim_Mode_Tackles::null)
			{
				wwDO_NOTHING;
			}
#endif

			std::set<std::pair<float, ARugbyCharacter*>> teammates_nearest;
			for( RLPResultList::iterator it = team_mates_in_range.begin(); !(it == team_mates_in_range.end()) && result.n_tacklers < MAX_TACKLERS; ++it )
			{
				RLP_RESULT& team_mate = *it;

				dist = game->GetSpatialHelper()->GetPlayerToPlayerDistance( result.tacklee, team_mate.player );

				// Do not use the tackler or the ball holder
				if ( (*it).player == result.tacklers[0] )
					continue;
				if ( (*it).player == ball_holder )
					continue;
				// Do not use tacklers that are outside the maximum support tackle distance
				if ( dist > MAX_SUPPORT_TACKLE_DISTANCE )
					continue;
				// Ensure his behaviour can help in the tackle
				if ( !(*it).player->GetRole()
					|| !(*it).player->GetActionManager()->CanUseAction( ACTION_TACKLER ) )
					continue;

			#ifdef ENABLE_GAME_DEBUG_MENU
				#ifdef ENABLE_TRIGGER_RULES_DEBUG_SETTINGS
					if( SIFDebug::GetRulesDebugSettings()->IsNextTackleHigh() )
						continue;
				#endif
			#endif

				// Now determine if the tackler is within the right angle ranges
				//tackled_from_dir_type = GetTackledFromDirection( (*it).player, result.tacklee );

				//if ( tackled_from_dir_type != TFD_BEHIND ) {
				//	if ( support_tackler < 0 || dist < closest_appropriate_dist ) {
				// Add this is a candidate pid
				//support_tackler = (*it).player;
					if (result.n_tacklers < MAX_TACKLERS)
					{
						teammates_nearest.insert( std::make_pair(dist, (*it).player) );
					}
						//closest_appropriate_dist = dist;
						//support_tackled_from_dir = tackled_from_dir_type;
				//	}
				//}
			}

			// order from nearest to furthest teammates
			for (auto it = teammates_nearest.begin(); it != teammates_nearest.end() && result.n_tacklers < MAX_TACKLERS; ++it)
			{
				result.tacklers[ result.n_tacklers ] = it->second;
				result.tackled_from_direction[ result.n_tacklers ] = GetTackledFromDirection( it->second, player_to_tackle );
				result.n_tacklers++;
			}

		//}
	}

	/*
	// Determine the tackler directions
	// This is movement direction required by the tackler to effect the tackle
	// NOTE : There may need to be adjustments for the current speed of the players
	result.tackler_direction[0] = GetTacklerDirection( tackler, player_to_tackle );

	// Determine the tackled from directions
	result.tackled_from_direction[0] = GetTackledFromDirection( tackler, player_to_tackle );
	*/

	// Now setup the tacklers in the tackle
	// Body position for initial tackler is set here
	result.body_position[0] = tackle_body_position;

	//--------------------------------------------------------
	//
	// Now determine what type of tackle will be effected
	//
	//--------------------------------------------------------

	// First order the tackler pids based on tackle from direction
	// This makes it easier for the multi-man tackle and subsequent calculations
	int i;

	for( i = 0; i < MAX_TACKLERS; i++ )
	{
		if ( result.tacklers[i] != NULL )
		{
			// Determine the tackled from directions
			result.tackled_from_direction[i] = GetTackledFromDirection( result.tacklers[i], player_to_tackle );
		}
	}

	// Order Tackler pids in the given result based on tackled from direction, then by left right order for direction
	// This makes animation lookups alot easier for multi person tackles
	OrderTacklerPlayers( result );

	// Get hold of the speeds for all tackler/tacklees
	result.tacklee_speed = tacklee->GetMovement()->GetCurrentSpeed();
	// Reduce effective tacklee speed based on the number of re-entrant tackles they have had
	if ( tacklee->GetActionManager()->IsActionRunning( ACTION_TACKLEE ))
	{
		RUActionTacklee* tacklee_action = result.tacklee->GetActionManager()->GetAction<RUActionTacklee>();
		const static float TACKLE_REENTRY_MULTIPLIER[] = { 1.0f, 0.7f, 0.5f, 0.4f, 0.3f };
		float multiplier = TACKLE_REENTRY_MULTIPLIER[ tacklee_action->GetTackleReEntryCount() ];
		result.tacklee_speed *= multiplier;
		for( i = 0; i < MAX_TACKLERS; i++ )
		{
			if ( result.tacklers[i] != NULL )
				MabMath::ClampUpper( result.tacklee_speed,result.tacklers[i]->GetMovement()->GetCurrentSpeed() );
		}
	}
	result.tacklee_aggression = tacklee->GetAttributes()->GetAggression();
	GetMaxTackleImpetus( result.tacklee, result.tacklee, result.tacklee_speed, result.tacklee_aggression, (TACKLED_FROM_DIRECTION)result.tackled_from_direction[0], true, heavy_tackle, result.max_tacklee_impetus );

	for( i = 0; i < MAX_TACKLERS; i++ )
	{
		if ( result.tacklers[i] != NULL )
		{
			result.tackler_speed[i]      = result.tacklers[i]->GetMovement()->GetCurrentSpeed();
			result.tackler_aggression[i] = result.tacklers[i]->GetAttributes()->GetAggression();
			result.tackler_direction[i]  = GetTacklerDirection( result.tacklers[i], player_to_tackle );
		}
		else
		{
			result.tackler_speed[i] = 0.0f;
			result.tackler_aggression[i] = 0.0f;
		}

		// Get hold of the tacklers impetus
		GetMaxTackleImpetus( result.tacklers[i], result.tacklee, result.tackler_speed[i], result.tackler_aggression[i], (TACKLED_FROM_DIRECTION)result.tackled_from_direction[0], false, heavy_tackle, result.max_tackler_impetus[i] );
	}

	//------------------------------------------------
	// For multi person tackles - verify that we
	// have a relevant animation (and that it exists)
	//------------------------------------------------

	//VerifyAndCullMultipleTacklers( result );

	//------------------------------------------------
	// Now work out impetus values and determine
	// the specific tackle type
	//------------------------------------------------

	// Work out the tackler impetus
	result.actual_tacklers_impetus = result.max_tackler_impetus[0] * ((1.0f - IMPETUS_VARIANCE) + game->GetRNG()->RAND_RANGED_CALL(float, IMPETUS_VARIANCE ));

	// Work out the tacklee impetus
	result.actual_tacklee_impetus = result.max_tacklee_impetus * ((1.0f - IMPETUS_VARIANCE) + game->GetRNG()->RAND_RANGED_CALL(float, IMPETUS_VARIANCE ));

	// Now we can work out what kind of tackle it is
	//float tackle_impetus_diff = result.actual_tacklee_impetus - result.actual_tacklers_impetus;
	float tackle_ratio_diff = result.actual_tacklee_impetus / result.actual_tacklers_impetus;

	//------------------------------------------------
	// Set the default tackle animations
	//------------------------------------------------

	result.anim_sequences |= TAS_IMPACT;

	/*
	/// Primary context			Secondary context				Sub context 1			Sub context 2			Sub context 3				Sub context 4
	///------------------		--------------------			--------------			-------------------		------------------------	----------------------
	TRT_STANDARD,			/// STANDARD_TACKLE_TYPE			TACKLE_DOMINANCE		TACKLE_BODY_POSITION	TACKLED_FROM_DIRECTION
	TRT_CONTESTED,			///									TACKLED_FROM_DIRECTION
	TRT_SIDESTEP,			/// SIDESTEP_TACKLE_TYPE			TACKLED_FROM_DIRECTION
	TRT_FEND2,				/// FEND_TACKLE_TYPE				TACKLE_BODY_POSITION	TACKLED_FROM_DIRECTION
	TRT_TRY,				/// TRY_TACKLE_TYPE					TACKLE_BODY_POSITION	TACKLED_FROM_DIRECTION
	TRT_ANKLE_TAP2,			///									TACKLED_FROM_DIRECTION
	TRT_GROUND_GETUP,		///		`							TACKLED_FROM_DIRECTION
	TRT_HEAD_HIGH2,			///									TACKLED_FROM_DIRECTION	TACKLER_ARM
	TRT_MULTI_MAN2,			///									TACKLED_FROM_DIRECTION	TACKLE_BODY_POSITION	TACKLED_FROM_DIRECTION		TACKLE_BODY_POSITION
	TRT_DIVE_MISS,			///
	*/

	/// Work out the tackle dominance
	result.dominance = TACKLE_DOMINANCE::TDOM_DEFENCE2;

	for( i = (int)TACKLE_DOMINANCE::TDOM_ATTACK2; i <= (int)TACKLE_DOMINANCE::TDOM_DEFENCE2; i++ )
	{
		if ( tackle_ratio_diff >= tackle_dominance_meta[ i ].min_impetus_ratio )
		{
			result.dominance = tackle_dominance_meta[ i ].dominance;
			break;
		}
	}

	// Just testing out what its like with PROP roles having higher chance of tackle dominance
	if (MLW_PROP_ROLE_OVERRIDES())
	{
		if (tacklee->GetAttributes()->GetPlayerPosition() & PP_PROP)
		{
			if (result.tacklers[0]->GetAttributes()->GetPlayerPosition() & PP_PROP)
			{
				result.dominance = TACKLE_DOMINANCE::TDOM_ATTACK_EQ;
				FString msg = "Both players are prop, equal tackle dominance";
				GEngine->AddOnScreenDebugMessage(-1, 1.0f, FColor::Red, *msg);
			}
			else
			{
				const float TACKLE_REENTRY_MULTIPLIER[] = { 1.0f, 0.7f, 0.5f, 0.4f, 0.3f };
				int tackleEntryCount = tacklee->GetActionManager()->GetAction<RUActionTacklee>()->GetTackleReEntryCount();
				MabMath::Clamp(tackleEntryCount, 0, 4);
				float multiplier = TACKLE_REENTRY_MULTIPLIER[tackleEntryCount] * game->GetGameSettings().game_settings.slider_prop_breakthrough;

				auto rand = game->GetRNG()->RAND_RANGED_CALL(float, 1.0f);
				result.dominance = rand < (0.6f * multiplier) ? TACKLE_DOMINANCE::TDOM_ATTACK1 : TACKLE_DOMINANCE::TDOM_ATTACK_EQ;
				FString msg = "Tacklee is a PROP, tackler is not, giving PROP tackle advantage";
				GEngine->AddOnScreenDebugMessage(-1, 2.0f, FColor::Green, *msg);
				GEngine->AddOnScreenDebugMessage(-1, 2.0f, FColor::Red, FString::Printf(TEXT("TACKLE REENTRY COUNT IS: %f, multiplier is %f, slider diff is %f"), tackleEntryCount, multiplier, game->GetGameSettings().game_settings.slider_prop_breakthrough));
			}
		}
	}

	//If a break tackle boost is set, use that value.
	float setplayTackleModifier = tacklee->GetAttributes()->GetBreakTackleBoost();
	if (setplayTackleModifier > 1.0f)
	{
		result.dominance = TACKLE_DOMINANCE::TDOM_ATTACK2;
	}


	///-------------------------------------------------------------------------------------
	/// Now work out the tackle type
	///-------------------------------------------------------------------------------------
	///
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
	bool bSupportTacklerAvailable = result.n_tacklers == 1 || result.tacklers[1] == nullptr;
	bool bOneManTackleStandard = false;
	if (!bSupportTacklerAvailable)
	{
		// we prioritize two man tackle, so only do other type of tackle if there is only one tackler.
#endif

		if ( IsGroundGetup( result.tacklee,
							 result.tacklers[0],
							 (TACKLER_DIRECTION)result.tackler_direction[0],
							 (TACKLED_FROM_DIRECTION)result.tackled_from_direction[0], heavy_tackle, result ) )
		{
			// This method sets everything up anyway
		}
		else if ( IsAFend( result.tacklee,
								 result.tacklers[0],
								 (TACKLER_DIRECTION)result.tackler_direction[0],
								 (TACKLED_FROM_DIRECTION)result.tackled_from_direction[0], heavy_tackle, result ) )
		{
			// This method sets everything up anyway
		}
		else if ( IsASideStep( result.tacklee,
			result.tacklers[0],
			(TACKLER_DIRECTION)result.tackler_direction[0],
			(TACKLED_FROM_DIRECTION)result.tackled_from_direction[0], sidestep_side, result ) )
		{
			// This method sets everything up anyway
		}
		else if ( IsATryCornerTackle( result.tacklee, result.tacklers[0], result) )
		{
		}
		else if ( IsATryTackle( result.tacklee,
							 result.tacklers[0],
							 (TACKLER_DIRECTION)result.tackler_direction[0],
							 (TACKLED_FROM_DIRECTION)result.tackled_from_direction[0], heavy_tackle, result ) )
		{
			// This method sets everything up anyway
		}
		else if ( IsAnAnkleTap( result.tacklee,
								 result.tacklers[0],
								 (TACKLER_DIRECTION)result.tackler_direction[0],
								 (TACKLED_FROM_DIRECTION)result.tackled_from_direction[0], heavy_tackle, result ) )
		{
			// This method sets everything up anyway
		}

		else if ( IsAContestedTackle( result.tacklee,
			result.tacklers[0],
			(TACKLER_DIRECTION)result.tackler_direction[0],
			(TACKLED_FROM_DIRECTION)result.tackled_from_direction[0], heavy_tackle, result ) )
		{
			// This method sets everything up anyway
		}
		else if ( IsAHeadHigh( result.tacklee,
								 result.tacklers[0],
								 (TACKLER_DIRECTION)result.tackler_direction[0],
								 (TACKLED_FROM_DIRECTION)result.tackled_from_direction[0], heavy_tackle, result ) )
		{
			// This method sets everything up anyway
		}
		else 
		{
			bOneManTackleStandard = true;
		}
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
	}

	if (bSupportTacklerAvailable || bOneManTackleStandard)
#else
	else /// Must be s standard tackle
#endif
	{
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
#else
		result.n_tacklers = 1;
#endif
		result.variant_index = 1;

		/// Modify aggression and choose success probability for tackle
		result.tackle_result = TRT_STANDARD;
		result.new_tacklee_aggression	 = tacklee->GetAttributes()->GetAggression()			* tackle_dominance_meta[(int) result.dominance ].tacklee_aggression_mult;
		result.new_tackler_aggression[0] = primary_tackler->GetAttributes()->GetAggression()	* tackle_dominance_meta[(int) result.dominance ].tackler_aggression_mult;

		float prob = 0.0f;
		// If the tacklers team is not human controlled, and they are versing a human team
		if( tackler->GetAttributes()->GetTeam()->GetNumHumanPlayers() == 0 && tacklee->GetAttributes()->GetTeam()->GetNumHumanPlayers() > 0)
		{
			prob = tackle_dominance_meta[(int)result.dominance].prob_success_ai[(int)tackler->GetGameWorld()->GetGameSettings().difficulty][0];
				// Nick  WWS 7s to Womens //[(int)tackler->GetGameWorld()->GetGameSettings().game_settings.GetGameMode()];
		}
		else
		{
			prob = tackle_dominance_meta[(int)result.dominance].prob_success_human[(int)tackler->GetGameWorld()->GetGameSettings().difficulty][0];
				// Nick  WWS 7s to Womens //[(int)tackler->GetGameWorld()->GetGameSettings().game_settings.GetGameMode()];
		}

#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE
// boost the success rate for two tacklers while slightly decrease the success rate for one tackler
			if (result.n_tacklers > 1)
			{
				prob = prob * 1.2f; // increase the probability for two
			}
			else
			{
				prob = prob * 0.6f; // decrease the probability for one
			}
			MabMath::Clamp(prob, 0.1f, 0.99f);
#endif
		
#if defined ENABLE_TWO_MAN_TACKLE && ENABLE_TWO_MAN_TACKLE && !UE_BUILD_SHIPPING
		
		bool bForceSuccessTwoManTackle = CVarNextSuccessTwoManTackle.GetValueOnAnyThread();
		bool bForceFailedTwoManTackle = CVarNextFailTwoManTackle.GetValueOnAnyThread();
		if (result.n_tacklers > 1 && bForceSuccessTwoManTackle) // two man tackle
		{
			prob = 1.0f;
			// ensure the other flag is set false.
			CVarNextFailTwoManTackle.AsVariable()->Set(0, EConsoleVariableFlags::ECVF_SetByConsole);
		}
		else if (result.n_tacklers > 1 && bForceFailedTwoManTackle)
		{
			prob = 0.0f;
			CVarNextSuccessTwoManTackle.AsVariable()->Set(0, EConsoleVariableFlags::ECVF_SetByConsole);
		}
#endif
		result.successful = game->GetRNG()->RAND_RANGED_CALL(float, 1.0f ) <= prob;

		// Sometimes while in the try zone IsATryTackle would return false, which means a standard tackle would occur.
		// This can cause a lot of frustration if those tackles constantly fail.
		FieldExtents extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
		float dist_to_try_line = extents.y / 2.0f - (tacklee->GetMovement()->GetCurrentPosition().z * tacklee->GetAttributes()->GetTeam()->GetPlayDirection());
		if( dist_to_try_line < 0.0f )
		{
			result.successful = true;
		}

		result.held = true;
		result.taken_down = result.successful;

		/// For tackles from behind - it does not make sense for the tackler to
		/// pull the tacklee back to his own line (is unrealistic) so we limit
		/// The maximum defensive dominance from behind to EQ
		if ( (result.n_tacklers == 1 && result.tackled_from_direction[0] == TFD_BEHIND) && result.dominance >= TACKLE_DOMINANCE::TDOM_DEFENCE1 )
			result.dominance = TACKLE_DOMINANCE::TDOM_ATTACK_EQ;

		if ( result.successful )
		{
			result.anim_sequences |= TAS_TAKEN_DOWN;
			result.anim_sequences |= TAS_GROUND_STRUGGLE;
			result.anim_sequences |= TAS_GROUND_RELEASE;
			result.std_tackle_type = STT_SUCCESS;
			result.state_machine = TSM_SUCCESS;
		}
		else
		{
			result.std_tackle_type = STT_FAILED;
			result.state_machine = TSM_FAILED;
			tacklee->GetAttributes()->TackleWasBroken();
		}

		// We are currently using this to evaluate standard tackles, because there are no heavy tackles.
		// Tweaking the probability might be necessary!
		if ( WillDropBallInHeavyTackle( result.tacklee ) )
		{
			result.drop_ball_type = TDT_AT_IMPACT;
		}
	}

#ifdef DEBUG_DIVE_TACKLE
	DD_TEXT_HUMAN(tackler,"tackle success",dd_text_index);
#endif


	// Set the injury status for the tackle result
	SetInjuryStatus( result );

	// Set the suspension status for the tackle result
	SetSuspensionStatus( result );

	// Check for disabled offloads
	if ( !game->GetGameSettings().game_settings.custom_rule_offloads )
		result.can_offload = false;

	// Update the tackle determination iteration
	result.tackle_determination_index = ++MASTER_TACKLE_DETERMINATION_COUNT;

	// Apply final aggression modifications for the players
	if ( result.tacklee != NULL )
	{
		if ( game->GetGameSettings().difficulty > (MAX_DIFFICULTY / 2.0f) && tacklee->GetAttributes()->GetTeam()->GetNumHumanPlayers() == 0 )
		{
			float modifier = 1.0f + (game->GetGameSettings().difficulty - (MAX_DIFFICULTY / 2.0f)) * 0.2f;
			result.new_tacklee_aggression *= modifier;
		}
		ModifyPlayerAggressionBasedOnAgressivensss( result.tacklee, &(result.new_tacklee_aggression) );
	}

	for( i = 0; i < result.n_tacklers; i++ )
	{
		if (i < MAX_TACKLERS && result.tacklers[i] != NULL) 
		{
			ModifyPlayerAggressionBasedOnAgressivensss(result.tacklers[i], &(result.new_tackler_aggression[i]));
		}
	}

	result.tackle_angle = GetTackleAngle(result.tacklers[0], result.tacklee);

	return true;
}
#pragma optimize("", on)
RUTackleResult::RUTackleResult()
: tackle_result(TRT_NO_TACKLE)
//body_position
//tackler_direction
//tackled_from_direction
//tackler_arm
, fend_type(FT_UNKNOWN)
, state_machine(TSM_UNKNOWN)
, dominance(TACKLE_DOMINANCE::TDOM_UNKNOWN)
, std_tackle_type(STT_UNKNOWN)
, ss_tackle_type(SSTT_UNKNOWN)
, fend_tackle_type(FTT_UNKNOWN)
, try_tackle_type(TRY_TACKLE_TYPE::TTT_UNKNOWN)
, tackle_angle(0.0f)
//tackler_speed
//max_tackler_impetus
, actual_tacklers_impetus(0.0f)
, tacklee_speed(0.0f)
, max_tacklee_impetus(0.0f)
, actual_tacklee_impetus(0.0f)
//tacklers
, n_tacklers(0)
, tacklee(NULL)
//tackler_aggression
, tacklee_aggression(0.0f)
//new_tackler_aggression
, new_tacklee_aggression(0.0f)
, can_offload(false)
, taken_down(false)
, held(false)
, drop_ball_type(TDT_NONE)
//injured_players
//tacklers_suspended
//injury_types
, tackle_prone_type(TPT_NONE)
, tackle_heavy_type(THT_NONE)
, successful(false)
, is_video_ref(false)
, is_try_tackle(false)
, prob_video_ref_try(1.0f)
, anim_sequences(0)
, play_rate(0.0f)
, align_anim(TAT_ALIGN_BOTH)
, variant_index(0)
, tackle_determination_index(0)
, init_tacklee_movement()
//init_tackler_movement
{
	for (size_t i = 0u; i < MAX_TACKLERS; ++i)
	{
		body_position[i] = TACKLE_BODY_POSITION::TBP_UNKNOWN;
		tackler_direction[i] = TD_UNKNOWN;
		tackled_from_direction[i] = TFD_UNKNOWN;
		tackler_arm[i] = TRA_NONE;
		tackler_speed[i] = 0.0f;
		max_tackler_impetus[i] = 0.0f;
		tacklers[i] = nullptr;
		tackler_aggression[i] = 0.0f;
		new_tackler_aggression[i] = 0.0f;
		tacklers_suspended[i] = SUSPENSION_NONE;
	}

	for (size_t i = 0u; i < MAX_TACKLERS+1; ++i)
	{
		injured_players[i] = nullptr;
		injury_types[i] = TIT_NONE;
	}

	Reset();
}

RUTackleResult::~RUTackleResult()
{

}

int RUTackleResult::GetTacklerIndexFromPlayer( ARugbyCharacter* player ) const
{
	// loop through array looking for pid
	for( int i = 0; i < n_tacklers; i++ ) {
		if ( i < MAX_TACKLERS && player == tacklers[i] ) {
			return i;
		}
	}

	// not found
	MABBREAKMSG( "Invalid player supplied to GetTacklerIndexFromPlayer" );
	return 0;
}

bool RUTackleResult::IsTacklerSuspended( int player_index ) const
{
	MABASSERT(player_index < MAX_TACKLERS);
	//player must be in the current tackle result because it's retrieved from RUTackleResult.tacklers
	return tacklers_suspended[player_index] != SUSPENSION_NONE;
	//if ( player == NULL )
	//	return false;

	//// loop through suspended tacklers looking for pid
	//for( int i = 0; i < MAX_TACKLERS; i++ ) {
	//	if (tacklers_suspended[i] == player )
	//		return true;
	//}

	//// not found
	//return false;
}

bool RUTackleResult::IsPlayerInjured( ARugbyCharacter* player ) const
{
	// loop through injured players looknig for pid
	for( int i = 0; i < (MAX_TACKLERS + 1); i++ ) {
		if (injured_players[i] == player )
			return true;
	}

	// not found
	return false;
}

TACKLE_INJURY_TYPE RUTackleResult::GetTackleInjuryType( ARugbyCharacter* player ) const
{
	// loop through injured pids looking for pid
	for( int i = 0; i < (MAX_TACKLERS + 1); i++ ) {
		if (injured_players[i] == player )
			// found
			return (TACKLE_INJURY_TYPE)injury_types[i];
	}

	// not found
	return TIT_NONE;
}

void RUTackleResult::Reset()
{
	tackle_result = TRT_UNKNOWN;

	body_position.fill(TACKLE_BODY_POSITION::TBP_UNKNOWN);
	tackler_speed.fill(0.0f);
	tackler_arm.fill(TRA_NONE);
	max_tackler_impetus.fill(0.0f);
	tacklers.fill(nullptr);
	new_tackler_aggression.fill(0.0f);
	tackler_direction.fill(TD_UNKNOWN);
	tackled_from_direction.fill(TFD_UNKNOWN);
	tackler_speed.fill(0.0f);
	tackler_aggression.fill(0.0f);

	state_machine = TSM_UNKNOWN;
	dominance = TACKLE_DOMINANCE::TDOM_UNKNOWN;				/// The relative dominance of tacklee to tackler in the tackle
	std_tackle_type = STT_UNKNOWN;
	ss_tackle_type = SSTT_UNKNOWN;
	fend_tackle_type = FTT_UNKNOWN;
	try_tackle_type = TRY_TACKLE_TYPE::TTT_UNKNOWN;

	n_tacklers = 0;
	fend_type = FT_UNKNOWN;
	tacklee_speed = 0.0f;
	tacklee_aggression = 0.0f;
	actual_tacklers_impetus = 0.0f;
	max_tacklee_impetus = 0.0f;
	actual_tacklee_impetus = 0.0f;
	tacklee = nullptr;
	new_tacklee_aggression = 0.0f;
	can_offload = false;
	taken_down = false;
	held = false;
	successful = false;
	anim_sequences = 0;
	play_rate = 1.0f;
	drop_ball_type = TDT_NONE;
	tackle_prone_type = TPT_NONE;
	tackle_heavy_type = THT_NONE;
	variant_index = 1;
	is_video_ref = false;
	is_try_tackle = false;
	prob_video_ref_try = 1.0f;

	injured_players.fill(nullptr);
	injury_types.fill(TIT_NONE);

	tacklers_suspended.fill(SUSPENSION_NONE);
}
void RUTackleResult::GameReset()
{
	Reset();
	init_tackler_movement.fill(MOVEMENT_DATA());
	tackle_angle = 0.0;
	align_anim = TAT_ALIGN_NONE;
	tackle_determination_index = 0;
	init_tacklee_movement = MOVEMENT_DATA();
}
void RUTackleResult::printState()
{
#ifdef _DEBUG
	//tacklers[0]
	//tacklee
	//injured_players[0,1]
	MABLOGDEBUG("TRSLT: %d %d %d %d %d %d %d %d %d %d %d %f %f %f %f %f %f %f %d %f %f %f %f %d %d %d %d %d %d %d %d",
		tackle_result,body_position[0],tackler_direction[0],
		tackled_from_direction[0],tackler_arm[0],fend_type,state_machine,dominance,std_tackle_type,
		ss_tackle_type,fend_tackle_type,tackle_angle,tackler_speed[0],max_tackler_impetus[0],actual_tacklers_impetus,
		tacklee_speed,max_tacklee_impetus,actual_tacklee_impetus,n_tacklers,tackler_aggression[0],
		tacklee_aggression,new_tackler_aggression[0],new_tacklee_aggression,can_offload,taken_down,held,drop_ball_type,
		tacklers_suspended[0],injury_types[0],injury_types[1],tackle_prone_type);
	//init_tacklee_movement
	//init_tackler_movement[0]
	MABLOGDEBUG("%d %d %d %d %f %d %f %d %d %d",
		tackle_heavy_type,successful,is_video_ref,is_try_tackle,prob_video_ref_try,anim_sequences,play_rate,align_anim,variant_index,tackle_determination_index);
#endif
}

MabString RUTackleResult::GetTackleeDropTypeString( TACKLEE_DROP_TYPE type )
{
	MabString drop_type_str;

	// Get tackle result type
	switch( type ) {
	case TDT_NONE:
		drop_type_str = "None";
		break;
	case TDT_AT_IMPACT:
		drop_type_str = "On Impact";
		break;
	case TDT_AT_HIT_GROUND:
		drop_type_str = "On Ground Impact";
		break;
	case TDT_RANDOM:
		drop_type_str = "Random";
		break;
	default:
		drop_type_str = "Unknown";
		break;
	}

	return drop_type_str;


}

MabString RUTackleResult::GetTackleProneTypeString( TACKLE_PRONE_TYPE type )
{
	MabString prone_type_str;

	// Get tackle result type
	switch( type ) {
	case TPT_NONE:
		prone_type_str = "None";
		break;
	case TPT_FRONT:
		prone_type_str = "Front";
		break;
	case TPT_BACK:
		prone_type_str = "Back";
		break;
	default:
		prone_type_str = "Unknown";
		break;
	}

	return prone_type_str;
}

MabString RUTackleResult::GetTackleHeavyTypeString( TACKLE_HEAVY_TYPE type )
{
	MabString heavy_type_str;

	// Get tackle result type
	switch( type ) {
	case THT_NONE:
		heavy_type_str = "None";
		break;
	case THT_OK:
		heavy_type_str = "OK";
		break;
	case THT_HIGH_TACKLE:
		heavy_type_str = "High Tackle";
		break;
	case THT_MISS_TACKLE:
		heavy_type_str = "Missed Tackle";
		break;
/*	case THT_SPEAR_TACKLE:
		heavy_type_str = "Spear Tackle";
		break;*/
	default:
		heavy_type_str = "Unknown";
		break;
	}

	return heavy_type_str;
}

MabString RUTackleResult::GetTackleTypeString( TACKLE_RESULT_TYPE type )
{
	MabString result_type_str;

	// Get tackle result type
	switch( type ) {
	case TRT_NO_TACKLE:
		result_type_str = "No Tackle";
		break;
	case TRT_STANDARD:
		result_type_str = "Standard";
		break;
	case TRT_CONTESTED:
		result_type_str = "Contested";
		break;
	case TRT_SIDESTEP:
		result_type_str = "Sidestep";
		break;
	case TRT_FEND2:
		result_type_str = "Fend";
		break;
	case TRT_TRY:
		result_type_str = "Try";
		break;
	case TRT_ANKLE_TAP2:
		result_type_str = "Ankle Tap";
		break;
	case TRT_GROUND_GETUP:
		result_type_str = "Ground Getup";
		break;
	case TRT_HEAD_HIGH2:
		result_type_str = "Head High";
		break;
	case TRT_MULTI_MAN2:
		result_type_str = "Multi man";
		break;
	case TRT_DIVE_MISS:
		result_type_str = "Dive miss";
		break;

	default:
		result_type_str = "Unknown";
		break;
	}

	return result_type_str;
}

MabString RUTackleResult::GetTackleTypeString()
{
	MabString result;
	result = GetTackleTypeString( tackle_result );

	switch( tackle_result )
	{
	case TRT_STANDARD:
		result += "-";
		result += GetTackleDominanceString( dominance );
		break;
	case TRT_CONTESTED:
		break;
	case TRT_SIDESTEP:
		result += "-";
		result += GetSideStepTackleTypeString( ss_tackle_type );
		break;
	case TRT_FEND2:
		result += "-";
		result += GetFendTackleTypeString( fend_tackle_type );
		break;
	case TRT_TRY:
		result += "-";
		result += GetTryTackleTypeString( try_tackle_type );
		break;
	case TRT_ANKLE_TAP2:
		break;
	case TRT_GROUND_GETUP:
		break;
	case TRT_HEAD_HIGH2:
		break;
	case TRT_MULTI_MAN2:
		break;
	case TRT_DIVE_MISS:
		break;
	default:
		break;
	}

	return result;
}

MabString RUTackleResult::GetTackleBodyPositionString( TACKLE_BODY_POSITION body_position )
{
	MabString body_pos_str;

	// Get Tackle Body Position
	switch( body_position ) {
	case TACKLE_BODY_POSITION::TBP_CHEST:
		body_pos_str = "Chest";
		break;
	case TACKLE_BODY_POSITION::TBP_WAIST:
		body_pos_str = "Waist";
		break;
	case TACKLE_BODY_POSITION::TBP_ANKLE:
		body_pos_str = "Ankle";
		break;
	case TACKLE_BODY_POSITION::TBP_HEAD:
		body_pos_str = "Head";
		break;
	default:
		body_pos_str = "Unknown";
		break;
	}

	return body_pos_str;
}

MabString RUTackleResult::GetTacklerDirectionString( TACKLER_DIRECTION dir )
{
	MabString tackle_direction_str;

	// Get direction for tacklers
	switch( dir ) {
	case TD_FORWARD:
		tackle_direction_str = "Forward";
		break;
	case TD_LEFT:
		tackle_direction_str = "Left";
		break;
	case TD_RIGHT:
		tackle_direction_str = "Right";
		break;
	default:
		tackle_direction_str = "Unknown";
		break;
	}

	return tackle_direction_str;
}

MabString RUTackleResult::GetFendTypeString( FEND_TYPE fend_type )
{
	MabString fend_type_str;

	// Get fend type for tacklers
	switch( fend_type ) {
	case FT_LEFT:
		fend_type_str = "Left";
		break;
	case FT_RIGHT:
		fend_type_str = "Right";
		break;
	case FT_FRONTLEFTSTRAIGHT:
		fend_type_str = "Front Left Straight";
		break;
	case FT_FRONTLEFTBENT:
		fend_type_str = "Front Left Bent";
		break;
	case FT_FRONTRIGHTSTRAIGHT:
		fend_type_str = "Front Right Straight";
		break;
	case FT_FRONTRIGHTBENT:
		fend_type_str = "Front Right Bent";
		break;
	case FT_BACKLEFT:
		fend_type_str = "Back Left";
		break;
	case FT_BACKRIGHT:
		fend_type_str = "Back right";
		break;
	case FT_SHOULDERBARGE:
		fend_type_str = "Shoulder Barge";
		break;
	default:
		fend_type_str = "Unknown";
		break;
	}

	return fend_type_str;
}

MabString RUTackleResult::GetTackledFromDirectionString( TACKLED_FROM_DIRECTION tackled_from )
{
	MabString tackled_from_direction_str;

	// Get tackled from direction for tacklees
	switch( tackled_from ) {
	case TFD_FRONT:
		tackled_from_direction_str = "Front";
		break;
	case TFD_SIDE_LEFT:
		tackled_from_direction_str = "Left Side";
		break;
	case TFD_SIDE_RIGHT:
		tackled_from_direction_str = "Right Side";
		break;
	case TFD_BEHIND:
		tackled_from_direction_str = "Behind";
		break;
	default:
		tackled_from_direction_str = "Unknown";
		break;
	}

	return tackled_from_direction_str;
}

MabString RUTackleResult::GetTackleStateMachineString( TACKLE_STATE_MACHINE state_machine )
{
	return "";//#rc3_legacy return TACKLE_STATE_MACHINE_STRINGS[ state_machine ];
}

MabString RUTackleResult::GetTackleDominanceString( TACKLE_DOMINANCE dominance )
{
	return "";//#rc3_legacy return TACKLE_DOMINANCE_STRINGS[ dominance ];
}

MabString RUTackleResult::GetStandardTackleTypeString( STANDARD_TACKLE_TYPE standard_tackle_type )
{
	return "";//#rc3_legacy return STANDARD_TACKLE_TYPE_STRINGS[ standard_tackle_type ];
}

MabString RUTackleResult::GetSideStepTackleTypeString( SIDESTEP_TACKLE_TYPE sidestep_tackle_type )
{
	return "";//#rc3_legacy return SIDESTEP_TACKLE_TYPE_STRINGS[ sidestep_tackle_type ];
}

MabString RUTackleResult::GetFendTackleTypeString( FEND_TACKLE_TYPE fend_tackle_type )
{
	return "";//#rc3_legacy return FEND_TACKLE_TYPE_STRINGS[ fend_tackle_type ];
}

MabString RUTackleResult::GetTryTackleTypeString( TRY_TACKLE_TYPE try_tackle_type )
{
	return "";//#rc3_legacy return TRY_TACKLE_TYPE_STRINGS[ try_tackle_type ];
}

MabString RUTackleResult::GetTacklerArmString( TACKLER_ARM tackler_arm )
{
	MabString tackler_arm_str;

	// Get the tackle arm for tacklers
	switch( tackler_arm ) {
	case TRA_LEFT:
		tackler_arm_str = "Left Arm";
		break;
	case TRA_RIGHT:
		tackler_arm_str = "Right Arm";
		break;
	case TRA_BOTH:
		tackler_arm_str = "Both Arms";
		break;
	default:
		tackler_arm_str = "Unknown";
		break;
	}

	return tackler_arm_str;
}

MabString RUTackleResult::GetTackleBodyPositionMnemonic( TACKLE_BODY_POSITION body_position )
{
	MabString body_pos_string;

	if ( body_position == TACKLE_BODY_POSITION::TBP_CHEST ) {
		body_pos_string = "C";
	} else if ( body_position == TACKLE_BODY_POSITION::TBP_WAIST ) {
		body_pos_string = "W";
	} else if ( body_position == TACKLE_BODY_POSITION::TBP_ANKLE ) {
		body_pos_string = "A";
	}  else if ( body_position == TACKLE_BODY_POSITION::TBP_HEAD ) {
		body_pos_string = "H";
	} else {
		body_pos_string = "?";
	}

	return body_pos_string;
}

TACKLE_BODY_POSITION RUTackleResult::GetTackleBodyPositionFromMnemonic( MabString& body_position_mnemonic )
{
	TACKLE_BODY_POSITION body_position;

	if ( body_position_mnemonic == "C" ) {
		body_position = TACKLE_BODY_POSITION::TBP_CHEST;
	} else if ( body_position_mnemonic == "W" ) {
		body_position = TACKLE_BODY_POSITION::TBP_WAIST;
	} else if ( body_position_mnemonic == "A" ) {
		body_position = TACKLE_BODY_POSITION::TBP_ANKLE;
	}  else if ( body_position_mnemonic == "H" ) {
		body_position = TACKLE_BODY_POSITION::TBP_HEAD;
	} else {
		body_position = TACKLE_BODY_POSITION::TBP_UNKNOWN;
	}

	return body_position;
}

MabString RUTackleResult::GetTacklerDirectionMnemonic( TACKLER_DIRECTION dir )
{
	// Get hold of Tackler Direction String
	MabString tklr_dir_string;

	if ( dir == TD_FORWARD ) {
		tklr_dir_string = "F";
	} else if ( dir == TD_LEFT ) {
		tklr_dir_string = "L";
	} else if ( dir == TD_RIGHT ) {
		tklr_dir_string = "R";
	} else {
		tklr_dir_string = "?";
	}

	return tklr_dir_string;
}

MabString RUTackleResult::GetTackledFromDirectionMnemonic( TACKLED_FROM_DIRECTION tackled_from )
{
	MabString tkl_from_dir_string;

	// Get hold of the tackle from direction string
	if ( tackled_from == TFD_FRONT ) {
		tkl_from_dir_string = "HO";
	} else if ( tackled_from == TFD_SIDE_LEFT ) {
		tkl_from_dir_string = "FL";
	} else if ( tackled_from == TFD_SIDE_RIGHT ) {
		tkl_from_dir_string = "FR";
	} else if ( tackled_from == TFD_BEHIND ) {
		tkl_from_dir_string = "FB";
	} else {
		tkl_from_dir_string = "??";
	}

	return tkl_from_dir_string;
}

MabString RUTackleResult::GetTackleSequenceMnemonic( TACKLE_SEQUENCE sequence )
{
	MabString tkl_sequence_string;

	if ( sequence == TAS_IMPACT ) {
		tkl_sequence_string = "IM";
	} else if ( sequence == TAS_HELD_LOOP ) {
		tkl_sequence_string = "HL";
	} else if ( sequence == TAS_HELD_RELEASE ) {
		tkl_sequence_string = "HR";
	} else if ( sequence == TAS_BREAK_OUT ) {
		tkl_sequence_string = "BO";
	} else if ( sequence == TAS_STRIP_BALL_SUCCESS ) {
		tkl_sequence_string = "SS";
	} else if ( sequence == TAS_STRIP_BALL_FAIL ) {
		tkl_sequence_string = "SF";
	} else if ( sequence == TAS_STRIP_BALL_PARTIAL ) {
		tkl_sequence_string = "SP";
	} else if ( sequence == TAS_TAKEN_DOWN ) {
		tkl_sequence_string = "TD";
	} else if ( sequence == TAS_GROUND_STRUGGLE ) {
		tkl_sequence_string = "GS";
	} else if ( sequence == TAS_GROUND_RELEASE ) {
		tkl_sequence_string = "GR";
	} else {
		tkl_sequence_string = "??";
	}

	return tkl_sequence_string;
}

MabString RUTackleResult::GetShortDebugString()
{
	char buffer[1024];
	char minibuf[256];
	MabString str;
	int i;

	buffer[0] = 0;

	// build brief info
	MabStringHelper::Sprintf( minibuf, "Iter				 : %d\n", tackle_determination_index );
	MabStringHelper::Strcat( buffer, "\n" );
	MabStringHelper::Strcat( buffer, minibuf );

	str = GetTackleTypeString();
	MabStringHelper::Sprintf( minibuf, "Type              : %s\n", str.c_str() );
	MabStringHelper::Strcat( buffer, minibuf );
	MabStringHelper::Strcat( buffer, MabString(128, "Angle               : %.3f\n", MabMath::Rad2Deg(tackle_angle)).c_str());

	for( i = 0; i < MAX_TACKLERS; i++ ) {
		str = GetTackleBodyPositionString( (TACKLE_BODY_POSITION)body_position[i] );
		MabStringHelper::Sprintf( minibuf, "Body Position     : %s\n", str.c_str() );
		MabStringHelper::Strcat( buffer, minibuf );
	}

	for( i = 0; i < MAX_TACKLERS; i++ ) {
		str = GetTacklerDirectionString( (TACKLER_DIRECTION)tackler_direction[i] );
		MabStringHelper::Sprintf( minibuf, "Tackler %d Dir     : %s\n", i, str.c_str() );
		MabStringHelper::Strcat( buffer, minibuf );
	}

	for( i = 0; i < MAX_TACKLERS; i++ ) {
		str = GetTackledFromDirectionString( (TACKLED_FROM_DIRECTION)tackled_from_direction[i] );
		MabStringHelper::Sprintf( minibuf, "Tackler %d From Dir: %s\n", i, str.c_str() );
		MabStringHelper::Strcat( buffer, minibuf );
	}

	MabStringHelper::Sprintf( minibuf, "Tacklee Speed     : %2f\n", tacklee_speed );
	MabStringHelper::Strcat( buffer, minibuf );

	MabStringHelper::Sprintf( minibuf, "Tacklee Aggression: %2f\n", tacklee_aggression );
	MabStringHelper::Strcat( buffer, minibuf );

	for( i = 0; i < MAX_TACKLERS; i++ ) {
		MabStringHelper::Sprintf( minibuf, "Tackler %d Speed     : %2f\n", i,	 tackler_speed[i] );
		MabStringHelper::Strcat( buffer, minibuf );
		MabStringHelper::Sprintf( minibuf, "Tackler %d Aggression: %2f\n", i, tackler_aggression[i] );
		MabStringHelper::Strcat( buffer, minibuf );
	}

	MabStringHelper::Sprintf( minibuf, "Impetus Ratio     : %2f / %2f = %2f\n", actual_tacklee_impetus, actual_tacklers_impetus, actual_tacklee_impetus / actual_tacklers_impetus );
	MabStringHelper::Strcat( buffer, minibuf );

	MabStringHelper::Sprintf( minibuf, "Heavy Tackle      : %s\n", GetTackleHeavyTypeString( tackle_heavy_type ).c_str() );
	MabStringHelper::Strcat( buffer, minibuf );

	MABASSERT( strlen( buffer ) < 1024 );

	return buffer;
}

MabString RUTackleResult::GetDebugString()
{
	char buffer[2048];
	char minibuf[256];
	MabString str;
	int i;

	buffer[0] = 0;

	// build comprehensive info
	MabStringHelper::Sprintf( minibuf, "Iter				 : %d\n", tackle_determination_index );
	MabStringHelper::Strcat( buffer, "\n" );
	MabStringHelper::Strcat( buffer, minibuf );

	str = GetTackleTypeString();
	MabStringHelper::Sprintf( minibuf, "Type              : %s\n", str.c_str() );
	MabStringHelper::Strcat( buffer, minibuf );
	MabStringHelper::Sprintf( minibuf, "Number Of Tacklers: %d\n", n_tacklers );
	MabStringHelper::Strcat( buffer, minibuf );
	MabStringHelper::Strcat( buffer, MabString(128, "Angle               : %.3f\n", MabMath::Rad2Deg(tackle_angle)).c_str());

	// Print out the animation names
	str = GetTackleeAnimationName( TAS_IMPACT );
	MabStringHelper::Sprintf( minibuf, "Tklee Impact Anm  : %s\n", str.c_str() );
	MabStringHelper::Strcat( buffer, minibuf );
	for( i = 0; i < n_tacklers; i++ ) {
		str = GetTacklerAnimationName( TAS_IMPACT, i );
		MabStringHelper::Sprintf( minibuf, "Tkler %d Impact Anm: %s\n", i, str.c_str() );
		MabStringHelper::Strcat( buffer, minibuf );
	}

	for( i = 0; i < MAX_TACKLERS; i++ ) {
		str = GetTackleBodyPositionString( (TACKLE_BODY_POSITION)body_position[i] );
		MabStringHelper::Sprintf( minibuf, "Body Position     : %s\n", str.c_str() );
		MabStringHelper::Strcat( buffer, minibuf );
	}

	for( i = 0; i < MAX_TACKLERS; i++ ) {
		str = GetTacklerDirectionString( (TACKLER_DIRECTION)tackler_direction[i] );
		MabStringHelper::Sprintf( minibuf, "Tackler %d Dir     : %s\n", i, str.c_str() );
		MabStringHelper::Strcat( buffer, minibuf );
	}

	for( i = 0; i < MAX_TACKLERS; i++ ) {
		str = GetTackledFromDirectionString( (TACKLED_FROM_DIRECTION)tackled_from_direction[i] );
		MabStringHelper::Sprintf( minibuf, "Tackler %d From Dir: %s\n", i, str.c_str() );
		MabStringHelper::Strcat( buffer, minibuf );
	}

	for( i = 0; i < MAX_TACKLERS; i++ ) {
		str = GetTacklerArmString( (TACKLER_ARM)tackler_arm[i] );
		MabStringHelper::Sprintf( minibuf, "Tackler %d Arm     : %s\n", i, str.c_str() );
		MabStringHelper::Strcat( buffer, minibuf );
	}

	for( i = 0; i < MAX_TACKLERS; i++ ) {
		MabStringHelper::Sprintf( minibuf, "Tackler %d Speed   : %2f\n", i, tackler_speed[i] );
		MabStringHelper::Strcat( buffer, minibuf );
		MabStringHelper::Sprintf( minibuf, "Tackler %d Aggression: %2f\n", i, tackler_aggression[i] );
		MabStringHelper::Strcat( buffer, minibuf );
	}

	for( i = 0; i < MAX_TACKLERS; i++ ) {
		MabStringHelper::Sprintf( minibuf, "Max Tackler %d Imp : %2f\n", i, max_tackler_impetus[i] );
		MabStringHelper::Strcat( buffer, minibuf );
	}

	MabStringHelper::Sprintf( minibuf, "Act Tacklers Imp  : %2f\n", actual_tacklers_impetus );
	MabStringHelper::Strcat( buffer, minibuf );
	MabStringHelper::Sprintf( minibuf, "Max Tacklee Imp   : %2f\n", max_tacklee_impetus );
	MabStringHelper::Strcat( buffer, minibuf );
	MabStringHelper::Sprintf( minibuf, "Act Tacklee Imp   : %2f\n", actual_tacklee_impetus );
	MabStringHelper::Strcat( buffer, minibuf );
	MabStringHelper::Sprintf( minibuf, "Impetus Ratio     : %2f\n", actual_tacklee_impetus / actual_tacklers_impetus );
	MabStringHelper::Strcat( buffer, minibuf );

	for( i = 0; i < n_tacklers; i++ ) {
		MabStringHelper::Sprintf( minibuf, "Tackler %d Pid     : %d\n", i, tacklers[i]->GetAttributes()->GetIndex() );
		MabStringHelper::Strcat( buffer, minibuf );
	}

	MabStringHelper::Sprintf( minibuf, "Tacklee Pid       : %d\n", tacklee->GetAttributes()->GetIndex() );
	MabStringHelper::Strcat( buffer, minibuf );

	MabStringHelper::Sprintf( minibuf, "Tacklee Speed     : %2f\n", tacklee_speed );
	MabStringHelper::Strcat( buffer, minibuf );

	MabStringHelper::Sprintf( minibuf, "Tacklee Aggression: %2f\n", tacklee_aggression );
	MabStringHelper::Strcat( buffer, minibuf );

	str = GetFendTypeString( (FEND_TYPE)fend_type );
	MabStringHelper::Sprintf( minibuf, "Fend Type         : %s\n", str.c_str() );
	MabStringHelper::Strcat( buffer, minibuf );

	for( i = 0; i < MAX_TACKLERS; i++ ) {
		MabStringHelper::Sprintf( minibuf, "New Tackler %d Agg : %2f\n", i, new_tackler_aggression[i] );
		MabStringHelper::Strcat( buffer, minibuf );
	}

	MabStringHelper::Sprintf( minibuf, "New Tacklee Agg   : %2f\n", new_tacklee_aggression );
	MabStringHelper::Strcat( buffer, minibuf );
	MabStringHelper::Sprintf( minibuf, "Can Offload       : %d\n", can_offload );
	MabStringHelper::Strcat( buffer, minibuf );
	MabStringHelper::Sprintf( minibuf, "Taken Down        : %d\n", taken_down );
	MabStringHelper::Strcat( buffer, minibuf );
	MabStringHelper::Sprintf( minibuf, "Held              : %d\n", held );
	MabStringHelper::Strcat( buffer, minibuf );
	MabStringHelper::Sprintf( minibuf, "Successful        : %d\n", successful );
	MabStringHelper::Strcat( buffer, minibuf );

	str = GetTackleeDropTypeString( (TACKLEE_DROP_TYPE)drop_ball_type );
	MabStringHelper::Sprintf( minibuf, "Drop Ball Type    : %s\n", str.c_str() );
	MabStringHelper::Strcat( buffer, minibuf );

	str = GetTackleProneTypeString( (TACKLE_PRONE_TYPE)tackle_prone_type );
	MabStringHelper::Sprintf( minibuf, "Prone Type        : %s\n", str.c_str() );
	MabStringHelper::Strcat( buffer, minibuf );

	str = GetTackleHeavyTypeString( (TACKLE_HEAVY_TYPE)tackle_heavy_type );
	MabStringHelper::Sprintf( minibuf, "Heavy Type        : %s\n", str.c_str() );
	MabStringHelper::Strcat( buffer, minibuf );

	int n_injured = 0;
	MabStringHelper::Strcat( buffer, "Injured Players   :" );
	for( i = 0; i < (MAX_TACKLERS + 1); i++ ) {
		if (injured_players[i])
		{
			MabStringHelper::Sprintf( minibuf, " %d (%d)", injured_players[i]->GetAttributes()->GetIndex(), injury_types[i] );
			MabStringHelper::Strcat( buffer, minibuf );
			n_injured++;
		}
	}
	if (n_injured == 0)
	{
		MabStringHelper::Strcat( buffer, " None" );
	}
	MabStringHelper::Strcat( buffer, "\n" );

	int n_suspended = 0;
	MabStringHelper::Strcat( buffer, "Suspended Tacklers   :" );
	for( i = 0; i < MAX_TACKLERS; i++ ) {
		if ( tacklers_suspended[i] != SUSPENSION_NONE )
		{
			MabStringHelper::Sprintf( minibuf, " %d", tacklers[i]->GetAttributes()->GetIndex() );
			MabStringHelper::Strcat(buffer, minibuf);
			n_suspended++;
		}
	}
	if (n_suspended == 0)
	{
		MabStringHelper::Strcat( buffer, " None" );
	}
	MabStringHelper::Strcat( buffer, "\n" );
	MABASSERT( strlen( buffer ) < 2048 );

	return MabString( buffer );
}

/// Get hold of the tacklee animation name for this tackle result
MabString RUTackleResult::GetTackleeAnimationName( /*int variant_index,*/ TACKLE_SEQUENCE sequence ) const
{
	char buf[3] = "";
	if ( variant_index >= 0 )
	{
		MabStringHelper::Sprintf( buf, "%02d", variant_index );
	}

	return TACKLEE_PREFIX + GetBaseTackleAnimationName( sequence ) + buf;
}

/// Get hold of the tackler animation name for this tackle result
MabString RUTackleResult::GetTacklerAnimationName( TACKLE_SEQUENCE sequence, int tackler_index ) const
{
	char buf[3] = "";
	if ( variant_index >= 0 ) {
		MabStringHelper::Sprintf( buf, "%02d", variant_index );
	}

	// If it is a multi man tackle we should also include the tackler index
	MabString name = TACKLER_PREFIX;
	if ( tackle_result == TRT_MULTI_MAN2 ) {
		char tbuf[2];
		MABASSERT( tackler_index >= 0 && tackler_index < 10 );
		MabStringHelper::Sprintf( tbuf, "%d", tackler_index );
		name += tbuf;
	}

	// Add on the base name and the variant index
	name += GetBaseTackleAnimationName( sequence );
	name += buf;
	return name;
}

MabString RUTackleResult::GetBaseTackleAnimationName( TACKLE_SEQUENCE sequence ) const
{
	MABUNUSED(sequence);
	return "taken_down";

//	// Work out the base tackle animation name
//	MabString result;
//
//	MabString body_pos_string;
//	MabString tkl_from_dir_string;
//	MabString tklr_dir_string;
//	MabString tkl_type_string;
//	MabString tkl_sequence_string;
//	MabString tkl_arm_string;
//
//	// Each Tackle mnemonic will be named using the following format <tackle type><body pos><sub name><direction>
//	// Two man tackles have the following format <tackler index><tackle type><body pos><sub name><direction>
//
//	// Get hold of the tackle type string
//	tkl_type_string = GetTackleTypeMnemonic( (TACKLE_RESULT_TYPE)tackle_result, (FEND_TYPE)fend_type );
//
//	// Get hold of the body position string
//
//	// For a two man tackle the body position is determined by the first tackler
//	//int tackler_body_pos_index = tackler_index;
//	//if ( tackle_result == TRT_MULTI_MAN )
//	//	tackler_body_pos_index = 0;
//
//	body_pos_string = GetTackleBodyPositionMnemonic( (TACKLE_BODY_POSITION)body_position[0] );
//
//	// If it is a two man tackle then the direction is determined from the second tackler
//	//int tackler_from_dir_index = tackler_index;
//	//if ( tackle_result == TRT_MULTI_MAN )
//	//	tackler_from_dir_index = 1;
//
//	// Get hold of the tackle from direction string
//	tkl_from_dir_string = GetTackledFromDirectionMnemonic( (TACKLED_FROM_DIRECTION)tackled_from_direction[0] );
//
//	// Get hold of Tackler Direction String
//	tklr_dir_string = GetTacklerDirectionMnemonic( (TACKLER_DIRECTION)tackler_direction[0] );
//
//	// Get hold of the tackle sequence string
//	tkl_sequence_string = GetTackleSequenceMnemonic( sequence );
//
//	// Get hold of the tackler arm string
//	if ( tackle_result == TRT_HEAD_HIGH ) {
//		if ( tackler_arm[0] == TRA_LEFT ) {
//			tkl_arm_string = "L";
//		} else if ( tackler_arm[0] == TRA_RIGHT ) {
//			tkl_arm_string = "R";
//		} else {
//			tkl_arm_string = "?";
//		}
//	}
//
//	// build the string
//	if ( tackle_result == TRT_NO_TACKLE ) {
//		result = "NOTACKLE";
//	} else if ( tackle_result == TRT_MISSED ) {
//		result = tkl_type_string + tklr_dir_string;
//	} else if ( tackle_result == TRT_TRY_JUMPOVER ) {
//		result = tkl_type_string;
//	} else if ( tackle_result == TRT_HEAD_HIGH ) {
//		result = tkl_type_string + body_pos_string + tkl_sequence_string + tkl_from_dir_string + tkl_arm_string;
//	} else if ( tackle_result == TRT_FEND ) {
//
//		MabString fend_str;
//
//		switch ( fend_type ) {
//			case FT_LEFT:
//				fend_str = "LL";
//			break;
//			case FT_RIGHT:
//				fend_str = "RR";
//			break;
//			case FT_FRONTLEFTSTRAIGHT:
//				fend_str = "FLS";
//			break;
//			case FT_FRONTLEFTBENT:
//				fend_str = "FLB";
//			break;
//			case FT_FRONTRIGHTSTRAIGHT:
//				fend_str = "FRS";
//			break;
//			case FT_FRONTRIGHTBENT:
//				fend_str = "FRB";
//			break;
//			case FT_BACKLEFT:
//				fend_str = "BL";
//			break;
//			case FT_BACKRIGHT:
//				fend_str = "BR";
//			break;
//			case FT_SHOULDERBARGE:
//				fend_str = "FR";
//			break;
//		}
//
//        result = tkl_type_string + fend_str;
//
//	} else if ( tackle_result == TRT_MULTI_MAN ) {
//
//		// Get tklr_index_string
//		/*
//		MabString tklr_index_string;
//		if ( tackler_name ) {
//			char buf[2];
//			MABASSERT( tackler_index >= 0 && tackler_index < 10 );
//			MabStringHelper::Sprintf( buf, "%d", tackler_index );
//			tklr_index_string = buf;
//		}
//		*/
//
//		result = tkl_type_string + tkl_sequence_string;
//		// Build up the tackled from direction/body pos string
//		MabString id_string;
//		for( int i = 0; i < n_tacklers; i++ ) {
//			id_string += GetTackledFromDirectionMnemonic( (TACKLED_FROM_DIRECTION)tackled_from_direction[i] );
//			id_string += GetTackleBodyPositionMnemonic( (TACKLE_BODY_POSITION)body_position[i] );
//		}
//		result += id_string;
//	} else if ( tackle_result == TRT_ON_GROUND ) {
//		MabString prone_type_str = "??";
//		if ( tackle_prone_type == TPT_FRONT )
//			prone_type_str = "F";
//		else if ( tackle_prone_type == TPT_BACK )
//			prone_type_str = "B";
//
//		result = tkl_type_string + prone_type_str + tkl_sequence_string + tkl_from_dir_string;
//	} else {
//		// The default for all normal tackles - held etc...
//		result = tkl_type_string + body_pos_string + tkl_sequence_string + tkl_from_dir_string;
//	}
//
//	return result;
}

// TEMPORARY METHOD TO FALLBACK TO THE MOST APPROPRIATE AVAILABLE TACKLE

void RUTackleResult::FallBackToMostAppropriateAvailableTackle()
{
	//// This assumes that if the tacklee anim is available then so is the tackler
	//if ( tacklee == NULL ) return;

	//// See if we can find the exact anim
	//MabString anim_name;

	//if ( tackle_result != TRT_MISSED && tackle_result != TRT_TRY_JUMPOVER )
	//	anim_name = GetTackleeAnimationName();
	//else
	//	anim_name = GetTacklerAnimationName();

	//bool anim_exists = false;	//NMMabAnimationRepository::GetAnimationReference( anim_name.c_str() ).IsValid();
	////MABLOGDEBUG( "Trying to find tackle animation %s - %d", tacklee_anim_name.c_str(), anim_exists );
	//MABLOGMSG( LOGCHANNEL_ALWAYS, LOGTYPE_INFO, "Trying to find tackle animation %s - %d", anim_name.c_str(), anim_exists );

	//if ( anim_exists )
	//	return;

	//MABBREAKMSG( MabString( 128, "RUTackleResult::FallBackToMostAppropriateAvailableTackle\nAnimation %s is missing", anim_name.c_str() ).c_str() );

	//switch( tackle_result ) {
	//case TRT_DRIVEN:
	//case TRT_SPEAR:
	//case TRT_ANKLE_TAP:
	//case TRT_WITHOUT_BALL:
	//case TRT_IN_AIR:
	//case TRT_ON_GROUND:
	//case TRT_UNKNOWN:
	////case TRT_SMASHED:
	//	tackle_result = TRT_HELD;
	//	break;
	//case TRT_TAKEN_DOWN:
	//	if ( body_position[0] == TBP_CHEST )
	//		tackle_result = TRT_HELD;
	//	else if ( body_position[0] == TBP_WAIST )
	//		body_position[0] = TBP_ANKLE;
	//	break;
	//default:
	//	break;
	//}

	//// Only to alter for tackle class
	//switch( tackle_result ) {
	//case TRT_HELD:
	//	held = true;
	//	successful = true;
	//	taken_down = false;
	//	anim_sequences = TAS_IMPACT;
	//	anim_sequences |= TAS_HELD_LOOP;
	//	anim_sequences |= TAS_HELD_RELEASE;

	//	if ( body_position[0] != TBP_CHEST )
	//		anim_sequences |= TAS_BREAK_OUT;

	//	// For non ankle tackles we can also be taken down
	//	if ( body_position[0] != TBP_ANKLE ) {
	//		anim_sequences |= TAS_TAKEN_DOWN;
	//		anim_sequences |= TAS_GROUND_STRUGGLE;
	//		anim_sequences |= TAS_GROUND_RELEASE;
	//	}

	//	// For chest and waist tackles that are not from behind we
	//	// can also strip the ball
	//	if ( body_position[0] != TBP_ANKLE &&
	//		 tackled_from_direction[0] != TFD_BEHIND ) {
	//		anim_sequences |= TAS_STRIP_BALL_FAIL;
	//		anim_sequences |= TAS_STRIP_BALL_SUCCESS;
	//		anim_sequences |= TAS_STRIP_BALL_PARTIAL;
	//	}

	//	can_offload = !(body_position[0] == TBP_CHEST && tackled_from_direction[0] != TFD_BEHIND);
	//	break;
	//case TRT_HEAD_HIGH:
	//	tackled_from_direction[0] = TFD_FRONT;
	//	break;
	////case TRT_TAKEN_DOWN:
	//case TRT_SMASHED:
	//	body_position[0] = TBP_WAIST;
	//	break;
	//case TRT_BARRELLED:
	//	body_position[0] = TBP_CHEST;
	//	break;
	//case TRT_BRUSHED_OFF:
	//case TRT_BUSTED_OVER:
	//	tackle_result = TRT_BRUSHED_OFF;
	//	body_position[0] = TBP_CHEST;
	//	break;
	//case TRT_MULTI_MAN:
	//	//if ( tackled_from_direction[1] == TFD_SIDE_RIGHT )
	//	//	tackled_from_direction[1] = TFD_FRONT;
	//	break;
	//default:
	//	break;
	//}
}


TACKLE_BODY_POSITION RUTackleHelper::GetTackleBodyPosition( float /*tackler_to_tacklee_dist*/ )
{
	static const float PROB_CHEST = 0.4f;
	static const float PROB_WAIST = 0.35f;
	static const float PROB_ANKLE = 0.25f;
	static const float prob_tot = PROB_CHEST + PROB_WAIST + PROB_ANKLE;
	static const float probs[] = { PROB_CHEST, PROB_WAIST, PROB_ANKLE };
	static const TACKLE_BODY_POSITION body_pos[] = { TACKLE_BODY_POSITION::TBP_CHEST, TACKLE_BODY_POSITION::TBP_WAIST, TACKLE_BODY_POSITION::TBP_ANKLE };
	static const int n_probs = sizeof( probs ) / sizeof( probs[0] );
	float curr_prob = 0.0f;
	float rand = game->GetRNG()->RAND_RANGED_CALL(float, prob_tot );

	// pick a body position and tackle type semi-randomly (weighted)
	for( int i = 0; i < n_probs; i++ ) {
		curr_prob += probs[i];
		if ( rand < curr_prob ) {
			return body_pos[i];
		}
	}

	return TACKLE_BODY_POSITION::TBP_UNKNOWN;
}

TACKLER_DIRECTION RUTackleHelper::GetTacklerDirection( ARugbyCharacter* tackler, ARugbyCharacter* tacklee, float project_time )
{
	FVector tacklee_projected_pos = tacklee->GetMovement()->GetCurrentPosition() + tacklee->GetMovement()->GetCurrentVelocity() * project_time;
	FVector tackler_to_tacklee = (tacklee_projected_pos - tackler->GetMovement()->GetCurrentPosition()).Unit();
	FVector tackler_dir;
	SSMath::AngleToMabVector3( tackler->GetMovement()->GetCurrentFacingAngle(), tackler_dir );

	// figure out some angle stuff
	float cos_angle = tackler_dir.Dot( tackler_to_tacklee );
	TACKLER_DIRECTION tackler_dir_type = TD_UNKNOWN;

	if ( cos_angle > SIDE_TACKLE_COS_ANGLE ) {
		tackler_dir_type = TD_FORWARD;
	} else {
		// Find out which side the tackler has to move to by rotating the vector so it points own the z axis
		FVector v;
		MabMatrix::MatrixMultiply(v, tackler_to_tacklee, MabMatrix::RotMatrixY(-tackler->GetMovement()->GetCurrentFacingAngle()));

		// Renderware coordinates - left is positive x
		if ( v.x > 0.0f )
			tackler_dir_type = TD_LEFT;
		else
			tackler_dir_type = TD_RIGHT;
	}

	return tackler_dir_type;
}

float RUTackleHelper::GetTackleAngle( ARugbyCharacter* tackler, ARugbyCharacter* tacklee )
{
	float tackler_angle = SSMath::CalcAngleFromPoints( tackler->GetMovement()->GetCurrentPosition(), tacklee->GetMovement()->GetCurrentPosition() );

	// Calculate the angle delta between the facing angle of the player and the tackler coming in
	float angle_delta = MabMath::AngleDelta( tacklee->GetMovement()->GetCurrentFacingAngle(), tackler_angle );

	// Now flip the result, angle delta of 180 needs to be mapped in as 0
	if (angle_delta < 0.0f)
		return -PI - angle_delta;
	else
		return PI - angle_delta;
}

TACKLED_FROM_DIRECTION RUTackleHelper::GetTackledFromDirection( ARugbyCharacter* tackler, ARugbyCharacter* tacklee )
{
	TACKLED_FROM_DIRECTION tackled_from_dir_type = TFD_UNKNOWN;

	FVector tacklee_to_tackler = (tackler->GetMovement()->GetCurrentPosition() - tacklee->GetMovement()->GetCurrentPosition()).Unit();

	// do some angle stuff

	FVector tacklee_dir;
	SSMath::AngleToMabVector3( tacklee->GetMovement()->GetCurrentFacingAngle(), tacklee_dir );

	float cos_angle = tacklee_dir.Dot( tacklee_to_tackler );

	if ( cos_angle > SIDE_TACKLE_COS_ANGLE ) {
		tackled_from_dir_type = TFD_FRONT;
	} else if ( cos_angle < REAR_TACKLE_COS_ANGLE ) {
		tackled_from_dir_type = TFD_BEHIND;
	} else {
		// Find out which side the tackler is on by rotating the vector so it points own the z axis
		FVector v;
		MabMatrix::MatrixMultiply(v, tacklee_to_tackler, MabMatrix::RotMatrixY(-tacklee->GetMovement()->GetCurrentFacingAngle()));
		if ( v.x > 0.0f )
			tackled_from_dir_type = TFD_SIDE_LEFT;
		else
			tackled_from_dir_type = TFD_SIDE_RIGHT;
	}

	return tackled_from_dir_type;
}

void RUTackleHelper::GetMaxTackleImpetus( ARugbyCharacter* player, ARugbyCharacter* tacklee, float speed, float aggression, TACKLED_FROM_DIRECTION tfd, bool is_tacklee, bool heavy_tackle, float& max_tackle_impetus )
{
	if ( player == NULL ) return;

	// TODO : Need to calculate position direction modifier
	float tackle_pos_dir_factor = 1.0f;
	float player_ability = 1.0f;

	if ( is_tacklee )
	{
		player_ability = player->GetAttributes()->GetBreakTackleAbility();
		if ( player->GetMovement()->IsSprintModeEnabled() )
		{
			static const float SPRINT_MODE_MULTIPLIER = 1.2f;
			player_ability *= SPRINT_MODE_MULTIPLIER;
		}
		//MABLOGDEBUG( "***Tacklee ability %d %0.2f", pid, player_ability );
	}
	else
	{
		player_ability = player->GetAttributes()->GetTackleAbility();

		/// TYRONE - make humans tackle better than AI to improve feel
		const static float HUMAN_BONUS = 0.1f;
		if ( player->GetHumanPlayer() != NULL )
			player_ability += HUMAN_BONUS;
		//MABLOGDEBUG( "***Tackler ability %d %0.2f", pid, player_ability );
	}

	// Change difficulty for computer players
	if ( player->GetAttributes()->GetTeam()->GetNumHumanPlayers() == 0 )
	{
		player_ability += player->GetGameWorld()->GetGameSettings().GetCurrentDifficultyOffsetFromDefault() * 0.15f;
	}
	else
	{
		MabMath::Clamp( player_ability, 0.0f, 1.0f );
	}

	/// Scale ability by gameplay sliders
	if ( game->GetGameSettings().game_settings.game_type != GAME_TRAINING && game->GetGameSettings().game_settings.game_type != GAME_MENU)
	{
		if ( is_tacklee )
		{
			float slider_val = game->GetGameSettings().game_settings.slider_break_tackle_ability;
			// Nick WWS 7s to Womens 13s //
			//if(game->GetGameSettings().game_settings.GameModeIsR7())
			//	slider_val *= 2.0f;
			MabMath::Clamp(slider_val, PLAYER_PROFILE_TACKLE_ABILITY_MIN, PLAYER_PROFILE_TACKLE_ABILITY_MAX);

			player_ability *= slider_val;
		}
		else
		{
			float slider_val = game->GetGameSettings().game_settings.slider_tackle_ability;
			// Nick WWS 7s to Womens 13s //
			//if (game->GetGameSettings().game_settings.GameModeIsR7())
			//	slider_val *= 2.0f;
			MabMath::Clamp(slider_val, PLAYER_PROFILE_TACKLE_ABILITY_MIN, PLAYER_PROFILE_TACKLE_ABILITY_MAX);

			player_ability *= slider_val;
		}
	}

	//Scale ability by the break tackle boost
	float break_tackle_boost = tacklee->GetAttributes()->GetBreakTackleBoost();
	player_ability *= break_tackle_boost;

	// Work out the players tackle impetus
	max_tackle_impetus = player_ability * tackle_pos_dir_factor + ((speed/10.0f+aggression) / 4.0f);

	// For the lowest difficulty we hamper the tackle impetus even further for AI teams
	const float SCHOOLBOY_IMPETUS_FACTOR = 0.6f;
	if ( game->GetGameSettings().difficulty == DIF_VERYEASY && player->GetAttributes()->GetTeam()->GetNumHumanPlayers() == 0 )
	{
		max_tackle_impetus *= SCHOOLBOY_IMPETUS_FACTOR;
	}

	// if the player is moving slowly, less than a walk, then the impetus should be scaled down considerably
	// as without much momentum, it is substantially more difficult to have influence in the tackle direction
	const static float IDEAL_WALK = 1.2f;
	const static float PENALTY_FACTOR = 0.20f;
	if ( speed < IDEAL_WALK ) {
		// players take a penalty for moving slowly
		float slow_penalty = PENALTY_FACTOR * ( IDEAL_WALK - speed );
		max_tackle_impetus -= slow_penalty;
	}

	// Add on some impetus for a hevay tackle
	if ( !is_tacklee && heavy_tackle )
		max_tackle_impetus += 0.5f;

	// Reduce the effectiveness if it is raining (more so for non front on tackles)
	bool wet_conditions =  game->GetGameSettings().weather_settings.raining && !game->GetStadiumManager()->IsStadiumCovered();
	if ( wet_conditions && !is_tacklee ) {
		float impetus_drop_for_rain = 0.0f;
		if ( tfd == TFD_FRONT ) {
			impetus_drop_for_rain = (0.07f + game->GetRNG()->RAND_RANGED_CALL(float, 0.07f ));
		} else {
			impetus_drop_for_rain = (0.15f + game->GetRNG()->RAND_RANGED_CALL(float, 0.1f ));
		}
		float tacklee_speed = tacklee->GetMovement()->GetCurrentSpeed();
		impetus_drop_for_rain *= (tacklee_speed / 6.0f );
		max_tackle_impetus -= impetus_drop_for_rain;
	}

	//MABLOGDEBUG( "***Tackle Impetus %d %0.2f (Abl %0.2f Spd %0.2f Agg %0.2f)", pid, max_tackle_impetus, player_ability, speed, aggression );
	MabMath::ClampLower( max_tackle_impetus, 0.1f );
}

bool RUTackleHelper::WillDropBallInHeavyTackle( ARugbyCharacter* tacklee )
{
	static const float LOWEST_PROB_DROP		 = 12.5f / 1000.0f;
	static const float HIGHEST_PROB_DROP	 = 50.0f / 1000.0f;

	// Ensure the ball is not dropped during a handover
	//if ( game->GetGameControlManager()->GetActiveNode() == "HANDOVER" )
	//	return false;

	float catch_ability = tacklee->GetAttributes()->GetCatchAbility();
	float prob_drop = MabMath::Lerp( HIGHEST_PROB_DROP, LOWEST_PROB_DROP, catch_ability );
	float prob_hold = 1.0f - prob_drop;

	if ( game->GetGameState()->IsRaining() )
	{
		prob_hold *= 0.96f;
	}

	if ( game->GetGameSettings().difficulty >= (MAX_DIFFICULTY / 2.0f)  &&
			tacklee->GetAttributes()->GetTeam()->GetNumHumanPlayers() == 0 )
	{
			float modifier = 1.0f + (game->GetGameSettings().difficulty - (MAX_DIFFICULTY / 2.0f)) * 0.03f;
			prob_hold *= modifier;
	}

	bool will_drop = ( game->GetRNG()->RAND_CALL(float) > prob_hold );

	return will_drop;
}

bool RUTackleHelper::SetInjuryStatus( RUTackleResult& result )
{

	// The tacklee will be injured in the following circumstances
	// The tackle is a successful tackle that leaves the tacklee on the ground
	// The aggression levels involved are high
	// Probability also increases depending on speed/weight of players involved in tackle

#ifdef ENABLE_ARMAGEDDON_BUILD
	// Armageddon - no injuries allowed
	return false;
#endif

#ifdef ENABLE_NEVER_INJURED_IN_TACKLE
	return false;
#endif

	// we cannot allow an injury and penalty to co-occur
	if ( result.tackle_result == TRT_HEAD_HIGH2 )
		return false;

	/// Don't allow injuries too close to the sideline as this can cause issues - Fix for #47413
	static const float SIDELINE_BUFFER = 4.0f;

	if ( result.tacklee && MabMath::Fabs( result.tacklee->GetMovement()->GetCurrentPosition().x ) > (FIELD_WIDTH * 0.5f - SIDELINE_BUFFER) )
		return false;

	RUStrategyHelper* strategy_helper = game->GetStrategyHelper();
	MABASSERT( strategy_helper != NULL );

	// ditto - make sure no tackler is going to be called offside
	for ( int tackler_index = 0; tackler_index < result.n_tacklers; tackler_index++ )
		if ( tackler_index < MAX_TACKLERS && result.tacklers[tackler_index] != NULL )
			if ( strategy_helper->WillTacklerBeOffside( result.tacklers[tackler_index] ) )
				return false;

	// Ensure that the current team can still sustain injures
	if ( !result.tacklee->GetAttributes()->GetTeam()->CanSustainInjury() )
		return false;

	// Can't have an injury on a try tackle
	if ( result.is_try_tackle )
		return false;

	// Is try_tackle doesn't work all the time so putting in a safety.
	//  - Don't allow injuries in near the goal area on either side - stops complications due to overlapping try + injury cutscenes.

	const float NO_INJURY_DISTANCE_FROM_TRY_LINE = 5.0f;

	FieldExtents extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	float zpos = result.tacklee->GetMovement()->GetCurrentPosition().z;
	float no_injury_z = extents.y * 0.5f - NO_INJURY_DISTANCE_FROM_TRY_LINE;

	if(zpos>no_injury_z || zpos<-no_injury_z)
	{
		MABLOGDEBUG("Too close to goal area, injury cancelled");
		return false;
	}

	// Only successful tackles that take the tacklee to the ground trigger injuries
	if ( result.state_machine != TSM_SUCCESS )
	{
		return false;
	}

	//MABLOGDEBUG("Forcing tackle injury");
	//result.injury_types[0] = (TACKLE_INJURY_TYPE)(int)game->GetRNG()->RAND_RANGED_CALL(float, TIT_NONE);
	//result.injured_players[0] = result.tacklee;
	//return true;

	// Debug Flag for testing injuries
/*
#ifdef ENABLE_GAME_DEBUG_MENU
	#ifdef ENABLE_TRIGGER_RULES_DEBUG_SETTINGS
	if ( SIFDebug::GetRulesDebugSettings()->GetNextTackleCausesInjury() )
	{
		MABLOGDEBUG("Forcing tackle injury");
		result.injury_types[0] = (TACKLE_INJURY_TYPE) (int)game->GetRNG()->RAND_RANGED_CALL( float, TIT_NONE );
		result.injured_players[0] = result.tacklee;
		return true;
	}
#endif
	#endif*/

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)	

	bool debugInfo = CInjuryDebug.GetValueOnGameThread() > 0 || FParse::Param(FCommandLine::Get(), TEXT("InjuryDebug"));
	if (debugInfo)
	{
		MABLOGDEBUG("Forcing tackle injury");
		result.injury_types[0] = CInjuryDebug.GetValueOnGameThread() < TACKLE_INJURY_TYPE (TIT_NONE) ? TACKLE_INJURY_TYPE(CInjuryDebug.GetValueOnGameThread()-1): TIT_HEAD;
		result.injured_players[0] = result.tacklee;
		return true;
	}
#endif

	// If the ball is going ot be dropped then just ignore the injury
	if ( result.drop_ball_type != TDT_NONE )
		return false;

	// The tackle impetus must be above a certain threshold
	// and they must be closing on each other rapidly
	float total_factor = result.actual_tacklers_impetus * 10.0f + game->GetSpatialHelper()->GetPlayerToPlayerClosingRate( result.tacklee, result.tacklers[0] );

	if ( total_factor < 14.0f )
		return false;

	// Now work out the probability of an injury occuring
	int injury_tackler_index = 0;//result.GetTacklerIndexFromPid( max_aggression );

	float weight_differential = (float)(result.tacklers[injury_tackler_index]->GetAttributes()->GetWeight() - result.tacklee->GetAttributes()->GetWeight());

	static const float BASE_PROB_INJURY = 0.99f;	// This has been moved into the slider settings
	float chance_of_injury = BASE_PROB_INJURY;

	if ( game->GetGameSettings().game_settings.game_type != GAME_TRAINING && game->GetGameSettings().game_settings.game_type != GAME_MENU)
	{
		chance_of_injury = 1.0f - game->GetGameSettings().game_settings.slider_injury_frequency;
	}


	const float PROB_DROP_PER_KG_DIFF = 0.005f;
	float weight_probability_adjustment = (PROB_DROP_PER_KG_DIFF * weight_differential);
	MabMath::Clamp(weight_probability_adjustment, -0.02f, 0.02f);

	float rand = game->GetRNG()->RAND_CALL(float);
	float prob = chance_of_injury - weight_probability_adjustment;

	MabMath::Clamp(prob, PLAYER_PROFILE_INJURY_FREQUENCY_MIN, PLAYER_PROFILE_INJURY_FREQUENCY_MAX);

	bool forcedInjury = false;
#ifdef ENABLE_GAME_DEBUG_MENU
#ifdef ENABLE_TRIGGER_RULES_DEBUG_SETTINGS
	if (SIFDebug::GetRulesDebugSettings()->GetNextTackleCausesInjury())
	{
		MABLOGDEBUG("Forcing tackle injury");
		forcedInjury = true;
	}
#endif
#endif

	if ( prob > rand && !forcedInjury)
		return false;

	// With each injury, the minimum probability will increase.
	//min_injury_probability += (1.0f - min_injury_probability) / 2.0f;

	// Otherwise - set the injured players
	result.injured_players[0] = result.tacklee;

	switch( result.tackle_result ) {
	case TRT_HEAD_HIGH2:
		result.injury_types[0] = TIT_HEAD;
		break;
	case TRT_CONTESTED:
	case TRT_MULTI_MAN2:
	default:
		result.injury_types[0] = (TACKLE_INJURY_TYPE) game->GetRNG()->RAND_RANGED_CALL(int, TIT_NONE );
		break;
	}


	//GGS SRA: Reduce player stamina proportionate to the severity of the injury and apply to
	float staminaLossBase = 0.15f;
	float staminaLossMulti = 1.0f;

	switch (result.injury_types[0])
	{
	case TACKLE_INJURY_TYPE::TIT_HEAD:
		staminaLossMulti = 2.0f;
		break;
	case TACKLE_INJURY_TYPE::TIT_STOMACH:
		staminaLossMulti = 1.5f;
		break;
	case TACKLE_INJURY_TYPE::TIT_KNEE:
	default:
		staminaLossMulti = 1.0f;
		break;
	}

	float staminaLossTotal = staminaLossBase * staminaLossMulti;

	result.tacklee->GetAttributes()->DecreaseStamina(staminaLossTotal, staminaLossTotal);
	result.tacklee->GetAttributes()->SetIsInjured(result.injury_types[0]);
	result.tacklee->GetAttributes()->SetInvolvedInInjuryTackle(true);
	result.tacklers[injury_tackler_index]->GetAttributes()->SetInvolvedInInjuryTackle(true);

	return true;
}

bool RUTackleHelper::SetSuspensionStatus( RUTackleResult& result )
{
	// The tackler can be suspended in the following circumstances
	// The tackle is a head high or spear
	// Goes up if they have done it before in the game

#ifdef ENABLE_ARMAGEDDON_BUILD
	// Armageddon - no suspension allowed
	return false;
#endif

#if !UE_BUILD_SHIPPING
	if (CVarNextTackleHigh.GetValueOnGameThread() > 0)
	{
		// Override set-by mask to clear so that variable can be cleared, if defaul SetByCode mask is used then it fails as SetByConsole has priority.
		CVarNextTackleHigh.AsVariable()->Set(0, EConsoleVariableFlags::ECVF_SetByConsole);
		result.tacklers_suspended[0] = SUSPENSION_NONE;
		return true;
	}
	if (CVarNextTackleYellowCard.GetValueOnGameThread() > 0)
	{
		// Override set-by mask to clear so that variable can be cleared, if defaul SetByCode mask is used then it fails as SetByConsole has priority.
		CVarNextTackleYellowCard.AsVariable()->Set(0, EConsoleVariableFlags::ECVF_SetByConsole);
		result.tacklers_suspended[0] = SUSPENSION_YELLOW_CARD;
		return true;
	}
#endif

#ifndef ENABLE_ALWAYS_SUSPEND
	if ( result.tackle_result != TRT_HEAD_HIGH2 )
		return false;
#endif

	RUTeam *tacklers_team = result.tacklers[0]->GetAttributes()->GetTeam();

	// Ensure that the tacklers team can sustain another suspension
	/// Check to see if the game can sustain a suspension
	if ( !tacklers_team->CanSustainSendOff() )
		return false;

	// CRAIG NOTES THAT THIS HAS BEEN ENABLED TO HAVE THE FOLLOWING FUNCTIONALITY FOR NRU2 GM:
	// * HIGH TACKLES ARE YELLOW-CARDED IN THE INITIAL STAGES
	// * ONCE A PLAYER HAS A YELLOW-CARD FOR HIGH TACKLE, THE NEXT ONE WILL BE A RED
	// THIS IS THE REQUESTED FUNCTIONALITY BY ALAN.
	//// They have a 60% chance of receiving a suspension
	//// Goes down 10% for every extra tackle made there after
	float yellow_card_probability = 0.0f;
	float red_card_probability = 0.0f;
	unsigned int high_tackle_count = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( result.tacklers[0], &RUDB_STATS_PLAYER::high_tackles );
	// when SetSuspensionStatus is called, the stats system would not have been updated, so 0 = first offence, 1 = second offence, 2 and default = third offence and above
	MABASSERT( high_tackle_count >= 0 && high_tackle_count <= 5 );	/// TYRONE : Just added in check here as I suspect on occassion the stats are returning unnatrually high values
	switch( high_tackle_count )
	{
		case 0:
			yellow_card_probability = 0.1f;
			red_card_probability = 0.01f;
			break;
		case 1:
			yellow_card_probability = 0.2f;
			red_card_probability = 0.1f;
			break;
		case 2:
		default:
			yellow_card_probability = 0.4f;
			red_card_probability = 0.2f;
			break;
	}

	float probability = game->GetRNG()->RAND_CALL(float);

	bool force_red_card = false;

#ifdef ENABLE_GAME_DEBUG_MENU
	#ifdef ENABLE_TRIGGER_RULES_DEBUG_SETTINGS
	if( SIFDebug::GetRulesDebugSettings()->IsNextHighTackleYellowCard() )
	{
		yellow_card_probability = 1.1f;
		red_card_probability = 0.0f;
	}
	else if( SIFDebug::GetRulesDebugSettings()->IsNextHighTackleRedCard())
	{
		yellow_card_probability = 0.0f;
		red_card_probability = 1.1f;
		force_red_card = true;
	}
	#endif
#endif

	///------------------------------
	/// Fallback for ticket #48441 - only allow red cards after two yellows!

	const int MIN_YELLOWS_BEFORE_RED_CARD = 2;

	RUStatisticsSystem *statistics_system = SIFApplication::GetApplication()->GetStatisticsSystem();
	int total_yellow_cards = statistics_system->GetCurrentMatchStat( tacklers_team->GetSide(), &RUDB_STATS_PLAYER::yellow_cards );

	if( total_yellow_cards<MIN_YELLOWS_BEFORE_RED_CARD && !force_red_card )
		red_card_probability = -1.0f;

	///------------------------------

	if( probability < red_card_probability )
	{
		result.tacklers_suspended[0] = SUSPENSION_RED_CARD;
		return true;
	}
	// if we get here, means we don't get red card, but we might get yellow card
	if( probability < yellow_card_probability )
	{
		result.tacklers_suspended[0] = SUSPENSION_YELLOW_CARD;
		return true;
	}
	// if we get here, means we are not suspended
	result.tacklers_suspended[0] = SUSPENSION_NONE;

	return false;
}

MabString RUTackleResult::GetInjuryAnimationName( ARugbyCharacter* player, bool get_transition, bool on_back ) const
{
	MabString start_str;
	MabString injury_str;
	MabString portion_str;
	MabString result;

	// build a string

	if ( on_back )
		start_str = "B";
	else
		start_str = "F";

	if ( get_transition )
		portion_str = start_str;
	else
		portion_str = "L";

	TACKLE_INJURY_TYPE tit = GetTackleInjuryType( player );

	// mark this as the most recent injury type
	//RUCinematicManager::GetRUInstance()->SetMostRecentInjuryType( (int) tit );

	switch( tit ) {
	case TIT_KNEE:    injury_str = "L"; break;
	case TIT_STOMACH: injury_str = "W"; break;
	case TIT_HEAD:	  injury_str = "H"; break;
	default:
		injury_str = "?";
		break;
	}

	result = "IN";
	result += injury_str;
	result += portion_str;
	result += "01";

	return result;
}

TACKLE_HEAVY_TYPE RUTackleHelper::GetHeavyTackleResult( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, bool heavy_tackle )
{
	MABUNUSED(tackler);
	MABUNUSED(tacklee);
	int i;

	// Work out what the tackle
	if ( !heavy_tackle )
		return THT_NONE;

	float probabilities[ THT_NONE ];
	const static bool ALWAY_HEAD_HIGH_ON_HEAVY = false;
	probabilities[ THT_OK ] = ALWAY_HEAD_HIGH_ON_HEAVY ? 0.0f : 0.8f;
	probabilities[ THT_MISS_TACKLE ] = ALWAY_HEAD_HIGH_ON_HEAVY ? 0.0f : 0.1f;
	probabilities[ THT_HIGH_TACKLE ] = 0.1f;

	float sum_prob = 0.0f;
	for( i = 0; i < THT_NONE; i++ )
		sum_prob += probabilities[i];

	// get a semi-random (weighted) heavy tackle type
	float rand = game->GetRNG()->RAND_RANGED_CALL(float, sum_prob );
	float prob_val = 0.0f;
	for( i = 0; i < THT_NONE; i++ ) {
		prob_val += probabilities[i];
		if ( rand < prob_val )
			return (TACKLE_HEAVY_TYPE) i;
	}

	return THT_NONE;
}

// Order Tackler pids in the given result based on tackled from direction, then by left right order for direction
// This makes animation lookups alot easier for multi person tackles
typedef struct {
	ARugbyCharacter* tackler; // The pid of this tackler
	TACKLED_FROM_DIRECTION tfd; // The direction that they are tackled from
} OTP; // Order tackler pid struct;


#ifdef BUILD_DEBUG
bool RUTackleHelper::VerifyMultiManTackleAnimations()
{
	return true;
/*
//	//Just finished checking the dependency that the tackle system has on animation and I think you were pretty much on the right track.
//	//
//	//There is a defined sort order for how tacklers are attached based on the naming convention and where they are in the world and I believe it covers all cases.
//	//
//	//Tackle animations and (animation naming and tackler locations) must follow the given sort order as list below:
//	//
//	//1.	HO Tacklers first � ordered left to right (see arrows) below
//	//2.	FL Tacklers second � ordered left to right (see arrows) below
//	//3.	FR Tacklers third � ordered left to right (see arrows) below
//	//4.	FB tacklers last � ordered left to right  (see arrows) below
//	//
//	//(Note I may have the left to right round the wrong way)
//	//
//	//              --------------->
//	//    ^     \        HO (1)       /
//	//	  |	     \___________________/      |
//	//    |                                 |
//	//    |                                 |
//	//    |  FL(2)					 FR(3)  |
//	//    |                                 |
//	//    |       ___________________       |
//	//	  |   	 /       FB (4)      \      v
//	//          /                     \
//	//              <---------------
//	//
//	//Some examples of correct naming:
//	//TEMMIMFLCFRC01 � (OK because FL � 2 appears before FR - 3)
//	//TEMMIMHOWFBA01 � (OK because HO � 1 appears before FB � 4)
//	//
//	//Some examples of incorrect naming:
//	//TEMMIMFBWFRC01 � (Incorrect because FB � 4 appears before FR � 3)
//	//TEMMIMFLWHOC01 � (Incorrect because FL � 2 appears before FR � 1)
//	//
//	//When the tacklers enter from the different quadrants there is no chance of players overlapping,
//	//however when two tacklers enter from the same quadrant we need some way of deciding how tacklers should be ordered so we can play the correct animation.
//	//This is where the left to right ordering comes in
//	//
//	//So for our trouble animation �
//	//TEMMIMHOCHOW01
//	//
//	//the left most head on tackler will be assigned to the first position (HOC) and expected to enter from the left head on position of the tackle � this will be TR0 also
//	//and the second will be assigned to the HOW position and expected to enter from a slightly more right of center offset. � this will be TR1 also.
//	//
//	//Hopefully that explains it and should help you just reassign animation names accordingly.
//	//      +z
//	// +ve X | -ve X
//	//      -z
//
//	const static bool has_run = false;
//
//	if ( has_run )
//		return true;
//
//	MabVector< MabString > mm_animations;
//	MabVector< MabString >::iterator it;
//	NMMabAnimationRepository::GetAnimationsMatching( "TEMMIM", mm_animations );
//
//	MabVector< MabString > tfd_mnemonics;
//	for( int i = 0; i < TFD_UNKNOWN; ++i ) {
//		tfd_mnemonics.push_back( RUTackleResult::GetTackledFromDirectionMnemonic( (TACKLED_FROM_DIRECTION) i ) );
//	}
//
//	MabString tfd_str[2];
//	int tfd_index[2];
//	int error_count = 0;
//
//	for( it = mm_animations.begin(); it != mm_animations.end(); ++it )
//	{
//		// Check the first and second entries
//
//		// TEMMIMFLWHOC01
//
//		// Make sure that the mnemonics are correct
//		MabString& anim_name = *it;
//		tfd_str[0] = anim_name.substr( 6, 2 );
//		tfd_str[1] = anim_name.substr( 9, 2 );
//
//		for( size_t i = 0; i < 2; ++i ) {
//
//			tfd_index[i] = -1;
//
//			bool found = false;
//			for( size_t j = 0; j < tfd_mnemonics.size() && !found; ++j ) {
//				found = (tfd_str[i] == tfd_mnemonics[j]);
//			}
//
//			if ( !found ) {
//				MABLOGMSG( LOGCHANNEL_ALWAYS, LOGTYPE_ERROR, "MM Anim: %s has invalid tfd mnemonics %s", anim_name.c_str(), tfd_str[ i ].c_str() );
//				++error_count;
//			} else {
//				tfd_index[i] = (int) j - 1;
//			}
//		}
//
//
//		// Now check the order to ensure that they're correct
//		// Ensure that the indices are in the correct order
//		if ( tfd_index[1] < tfd_index[0] ) {
//			MABLOGMSG( LOGCHANNEL_ALWAYS, LOGTYPE_ERROR, "MM Anim: %s has incorrectly ordered tfd mnemonics %s occurs before %s", anim_name.c_str(), tfd_str[1].c_str(), tfd_str[0].c_str() );
//			++error_count;
//		}
//	}
//
//	has_run = true;
//
//	return error_count == 0;*/
}
#endif

void RUTackleHelper::OrderTacklerPlayers( RUTackleResult& result )
{
	// If there aren't enough tacklers for a multi tackle then no need to order
	if ( result.n_tacklers < 2 )
		return;

	// Make sure we don't have too many
	MABASSERT( result.n_tacklers <= MAX_TACKLERS );

	/// Get the prioritised list
	TFD_LIST base_list;
	std::list< OTP > ordered_list;
	std::list< OTP >::iterator it;

	bool inserted = false;
	ARugbyCharacter* tackler;
	OTP to_insert;

	int i;
	for( i = 0; i < result.n_tacklers; i++ ) {
		// Insertion sort into the base list
		inserted = false;
		if (i < MAX_TACKLERS)
		{
			tackler = result.tacklers[i];
			to_insert.tfd = (TACKLED_FROM_DIRECTION)result.tackled_from_direction[ i ];
			to_insert.tackler = result.tacklers[ i ];

			for( it = ordered_list.begin(); it != ordered_list.end(); ++it ) {
				// If the current tackled from direction has higher precedence then
				// insert it here
				if ( to_insert.tfd < (*it).tfd ) {
					ordered_list.insert( it, to_insert );
					inserted = true;
					break;
				}
			}

			if ( !inserted ) {
				ordered_list.push_back( to_insert );
			}
		}
	}

	// Now that we have the list - propogate to the tackle_result
	i = 0;
	for( it = ordered_list.begin(); it != ordered_list.end(); ++it ) {
		if (i < MAX_TACKLERS)
		{
			result.tacklers[i] = (*it).tackler;
			result.tackled_from_direction[i] = (*it).tfd;
		}
		i++;
	}
}

// Two man tackle struct for below algorithm
typedef struct {
	ARugbyCharacter* tackler;
	TACKLED_FROM_DIRECTION dir;
	TACKLE_BODY_POSITION pos;
} TMT; // Two man tackle struct for below algorithm

//-----------------------------------------------------------------------------------------
// Analyse the tackle struct
// Preconditions - tackled from direction and pids must have been set + n_tacklers
//-----------------------------------------------------------------------------------------

void RUTackleHelper::VerifyAndCullMultipleTacklers( RUTackleResult& result )
{
	// If there aren't enough tacklers for a multi tackle then just return
	if ( result.n_tacklers < 2 || game->GetRNG()->RAND_CALL(float) < 0.5f ) {
		result.n_tacklers = 1;
		return;
	}

//	#ifdef BUILD_DEBUG
//	MABASSERT( VerifyMultiManTackleAnimations() );
//	#endif
//
//	// Make sure we don't have too many
//	MABASSERT( result.n_tacklers <= MAX_TACKLERS );
//
//	//--------------------------------------------------------
//	// Work out what animations can support the given tacklers
//	//--------------------------------------------------------
//
//	// The algorithm is as such:
//	// Step 1
//	//   Generate all permutations of animation names based on
//	//	 tackled from directions and try and find matching
//	//   animations in the repository
//	//   the naming convention specifies a strict tackled from
//	//   direction order to minimise lookups/permutations
//	//  Step 2
//	//   Once a valid animation has been found
//	//	   work out what body positions the animation is for
//	//	   match this up with the input pids and create a
//	//	   record of which tackler indices match to each
//	//	   tackler animation index (ordered by direction,
//	//	   left/right assocation).
//
//	//-------------------------------------------------------
//	// Engage integration on Step 1
//	// Smackdown!
//	//-------------------------------------------------------
//
//	/// Get the prioritised list
//	TFD_LIST base_list;
//	TFD_LIST::iterator it;
//	std::list< TFD_LIST > permutation_list;
//
//	// *NOTE* - This assumes that the tackler pids have been ordered correctly already!!!!!
//	for( int i = 0; i < result.n_tacklers; i++ ) {
//		// Insertion sort into the base list
//		base_list.push_back( (TACKLED_FROM_DIRECTION)result.tackled_from_direction[ i ] );
//	}
//
//	// Print out the ordered list (DEBUG)
//	MabString curr_str = "BASE ";
//	TFD_LIST::iterator tfd_it;
//
//	for( tfd_it = base_list.begin(); tfd_it != base_list.end(); ++tfd_it ) {
//		TACKLED_FROM_DIRECTION curr = *tfd_it;
//		curr_str += " ";
//		curr_str += RUTackleResult::GetTackledFromDirectionString( curr );
//	}
//	//MABLOGMSG( LOGCHANNEL_ALWAYS, LOGTYPE_INFO, curr_str.c_str() );
//
//	// Now generate all permutations
//	GenerateTackledFromDirectionPermutations( result.n_tacklers, base_list.begin(), base_list.end(), permutation_list );
//
//	// Iterate over pemutations and try and find an animation set that matches
//	std::list< TFD_LIST >::iterator perm_it;
//
//	MabString tacklee_anim_base_name;
//	MabString tackled_from_dir_part;
//	MabVector< MabString > tacklee_animations;
//
//	MabString base_prefix = TACKLEE_PREFIX;
//	base_prefix += MULTIMAN_PREFIX;
//	base_prefix += "IM";
//
//	bool found_valid_animation = false;
//	MabString selected_anim_name = "";
//
//	for( perm_it = permutation_list.begin(); perm_it != permutation_list.end(); ++perm_it ) {
//		// Build up the name of the tacklee animation to search for
//		tacklee_anim_base_name = base_prefix;
//
//		// Tack on the directional parts from the permutation
//		TFD_LIST& curr_list = *perm_it;
//
//		for( it = curr_list.begin(); it != curr_list.end(); ++it ) {
//			TACKLED_FROM_DIRECTION tfd = *it;
//
//			// Add on the direction mnemonic
//			tacklee_anim_base_name += RUTackleResult::GetTackledFromDirectionMnemonic( tfd );
//
//			// Add a wildcard for the body positon
//			tacklee_anim_base_name += "?";
//		}
//
//		// DEBUG - Print out the requested search string
//		//MABLOGMSG( LOGCHANNEL_ALWAYS, LOGTYPE_INFO, "Searching for: %s", tacklee_anim_base_name.c_str() );
//
//		// Get hold of all the celebration animations
//		tacklee_animations.clear();
//		NMMabAnimationRepository::GetAnimationsMatching( tacklee_anim_base_name, tacklee_animations );
//
//		size_t n_animations = tacklee_animations.size();
//
//		//MABLOGMSG( LOGCHANNEL_ALWAYS, LOGTYPE_INFO, "Found %d matching animations", n_animations );
//		/*
//		for( size_t i = 0; i < n_animations; i++ ) {
//			MABLOGMSG( LOGCHANNEL_ALWAYS, LOGTYPE_INFO, tacklee_animations[i].c_str() );
//		}
//		*/
//
//		// If we have found a match - then pick a random variant
//		if ( n_animations > 0 ) {
//			int selected_anim = )int)game->GetRNG()->RandFloat( (int) n_animations );
//			selected_anim_name = tacklee_animations[ selected_anim ];
//
//			// Now that we have the animation - work out which tacklers match
//			size_t permutation_length = curr_list.size();
//			std::list< TACKLE_BODY_POSITION > body_positions;
//			std::list< TACKLE_BODY_POSITION >::iterator body_it;
//
//			// Scan over the given string searching for all body positions
//			const char* ptr = selected_anim_name.c_str();
//
//			// Skip over the first part of the anim name
//			ptr += base_prefix.size();
//
//			// NOTE : This assumes each tackled from direction mnemonic is 2 characters (for convenience)
//			const int TFD_MNEMONIC_SIZE = 2;
//			MabString body_pos_mnemonic;
//			TACKLE_BODY_POSITION tbp;
//
//			for( size_t i = 0; i < permutation_length; i++ ) {
//				ptr += TFD_MNEMONIC_SIZE;
//				// The body position should be the next character
//				body_pos_mnemonic = ptr[0];
//				tbp = RUTackleResult::GetTackleBodyPositionFromMnemonic( body_pos_mnemonic );
//
//				// Add this to the list of body positions
//				body_positions.push_back( tbp );
//
//				ptr++;
//			}
//
//			// Now get hold of the varinat index
//			int variant_index;
//			MABVERIFY( sscanf(ptr, "%02d", &variant_index) > 0 );
//			result.variant_index = variant_index;
//
//			//MABLOGMSG( LOGCHANNEL_ALWAYS, LOGTYPE_INFO, "Picked %s", selected_anim_name.c_str() );
//
//			// Now we have the body positions from the animation and the direction as supplied
//			// we need to match up the input tacklers too
//
//			// Iterate over the successful tackled from direction sequence and find a tackler to match
//			// *NOTE* that since the tacklers are ordered correctly
//			int inserted_tackle_base = 0; // increment this as we insert
//			std::list< TMT > resulting_tackler_info;
//			std::list< TMT >::iterator tmt_it;
//
//			for( it = curr_list.begin(), body_it = body_positions.begin(); it != curr_list.end(); ++it, ++body_it ) {
//				TACKLED_FROM_DIRECTION tfd = *it;
//
//				/*
//				MABLOGMSG( LOGCHANNEL_ALWAYS, LOGTYPE_INFO, "Looking to insert %s, %s starting from %d",
//					RUTackleResult::GetTackledFromDirectionMnemonic( tfd ).c_str(),
//					RUTackleResult::GetTackleBodyPositionMnemonic( *body_it ).c_str(),
//					inserted_tackle_base
//				);
//				*/
//
//				for( int i = inserted_tackle_base; i < result.n_tacklers; i++ ) {
//					// If we have match then use this tackler
//					if ( result.tackled_from_direction[ i ] == tfd ) {
//						// Set the body position for this tackler
//						resulting_tackler_info.push_back( TMT() );
//						TMT& new_info = resulting_tackler_info.back();
//						new_info.dir = tfd;
//						new_info.pos = *body_it;
//						new_info.tackler = result.tacklers[i];
//						inserted_tackle_base = i + 1;
//						//MABLOGMSG( LOGCHANNEL_ALWAYS, LOGTYPE_INFO, "Found at pos %d", i );
//						break;
//					}
//				}
//			}
//
//			// Now that we have the new tackler info - apply it to the result
//			result.n_tacklers = 0;
//			for( tmt_it = resulting_tackler_info.begin(); tmt_it != resulting_tackler_info.end(); ++tmt_it ) {
//				TMT& new_info = *tmt_it;
//				result.tacklers[ result.n_tacklers ]  = new_info.tackler;
//				result.body_position[ result.n_tacklers ] = new_info.pos;
//				result.tackled_from_direction[ result.n_tacklers ] = new_info.dir;
//				/*
//				MABLOGMSG( LOGCHANNEL_ALWAYS, LOGTYPE_INFO, "Tackler %d has pid %d, TFD=%s, BP=%s",
//					result.n_tacklers,
//					result.tacklers[ result.n_tacklers ],
//					RUTackleResult::GetTackledFromDirectionMnemonic( result.tackled_from_direction[ result.n_tacklers ] ).c_str(),
//					RUTackleResult::GetTackleBodyPositionMnemonic( result.body_position[ result.n_tacklers ] ).c_str()
//				);
//				*/
//				result.n_tacklers++;
//			}
//
//			found_valid_animation = true;
//			MABASSERT( result.n_tacklers > 1 );
//			break;
//		} // We found a valid animation
//	}
//
//	// If we have found a valid animation then everything will be setup correctly
//	// otherwise just use the first tackler
//	if ( !found_valid_animation )
//	{
//		result.n_tacklers = 1;
//		result.variant_index = 1;
//	}
//	else if ( result.n_tacklers == 2 && result.tackled_from_direction[0] == result.tackled_from_direction[1] )
//	{
//		// For tackles that have multiple entrants from one side - we should ensure that they attach to the correct animation
//		// sor orde them appropriately by side of the attach locations that they are presently on
//
//		MabString anim_te_name = selected_anim_name;
//		MabString anim_tr_name[2];
//
//		anim_tr_name[0] = "TR0";
//		anim_tr_name[1] = "TR1";
//		anim_tr_name[0] += anim_te_name.substr( 2 );
//		anim_tr_name[1] += anim_te_name.substr( 2 );
//
//		NMMabAnimationReference anim_te_ref = NMMabAnimationRepository::GetAnimationReference( anim_te_name.c_str() );
//		NMMabAnimationReference anim_tr_ref[2];
//
//		anim_tr_ref[0] = NMMabAnimationRepository::GetAnimationReference( anim_tr_name[0].c_str() );
//		anim_tr_ref[1] = NMMabAnimationRepository::GetAnimationReference( anim_tr_name[1].c_str() );
//
//		//NiAnimationCache* animation_cache = NMMabAnimationRepository::GetAnimationCache();
//		//MABASSERT( animation_cache != NULL );
//		// Get the positions of tr0 and tr1 in relation to the tacklee
//		//NiQuatTransform tr0_attach = animation_cache->Interpolate( anim_te_ref   .GetAnimation()->GetAnimation(), anim_te_ref.IsMirror(),	 "tacklerAttach", 0.0f );
//		//NiQuatTransform tr1_attach = animation_cache->Interpolate( anim_tr_ref[1].GetAnimation()->GetAnimation(), anim_tr_ref[1].IsMirror(), "root",		  0.0f );
//		//tr1_attach = tr0_attach.HierApply( tr1_attach );
//
//		// TEMP.
//
//		FVector tr0_attach_transform(0,1,0);			// AsMab( tr0_attach.GetTranslate() )
//		FVector tr1_attach_transform(0,0,0);			// AsMab( tr1_attach.GetTranslate() )
//
//		// Now we want to transform this by the tackled from direction
//		float rotate_angle = 0.0f;
//
//		TACKLED_FROM_DIRECTION tfd = result.tackled_from_direction[0];
//		switch ( tfd ) {
//			case TFD_FRONT:			rotate_angle = 0.0f; break;
//			case TFD_SIDE_LEFT:		rotate_angle =  PI / 2.0f; break;
//			case TFD_SIDE_RIGHT:	rotate_angle = -PI / 2.0f; break;
//			case TFD_BEHIND:		rotate_angle =  PI; break;
//		}
//
//		FVector mab_tr0_attach = tr0_attach_transform * MabMatrix::RotMatrixY( rotate_angle );
//		FVector mab_tr1_attach = tr1_attach_transform * MabMatrix::RotMatrixY( rotate_angle );
//
//		// These angles need to be rotated in world space
//		float rot_world_angle = -result.tacklee->GetMovement()->GetCurrentFacingAngle() + rotate_angle;
//		FVector mab_tr0_pos	  = (result.tacklers[0]->GetMovement()->GetCurrentPosition() - result.tacklee->GetMovement()->GetCurrentPosition()) * MabMatrix::RotMatrixY( rot_world_angle );
//		FVector mab_tr1_pos	  = (result.tacklers[1]->GetMovement()->GetCurrentPosition() - result.tacklee->GetMovement()->GetCurrentPosition()) * MabMatrix::RotMatrixY( rot_world_angle );
//
//		// Now we check to see if the tacklers are on the same relative side as the joints - if they are not then we swap them
//		float attach_sign = MabMath::Sign( mab_tr1_attach.x - mab_tr0_attach.x );
//		float pos_sign = MabMath::Sign( mab_tr1_pos.x - mab_tr0_pos.x );
//
//		if ( attach_sign != 0.0f && pos_sign != 0.0f && attach_sign != pos_sign )
//		{
//			// Swap the tacklers
//			MABLOGMSG( LOGCHANNEL_GAME, LOGTYPE_INFO, "RUTackleHelper::VerifyAndCullMultipleTacklers - swapping tacklees over for %s", anim_te_name.c_str() );
//			std::swap( result.tacklers[0], result.tacklers[1] );
//			std::swap( result.tackled_from_direction[0], result.tackled_from_direction[1] );
//			// Note - don't swap body positions as the animation name requires this
//			//std::swap( result.body_position[0], result.body_position[1] );
//			std::swap( result.tackler_direction[0], result.tackler_direction[1] );
//			std::swap( result.tackler_arm[0], result.tackler_arm[1] );
//			std::swap( result.tackler_speed[0], result.tackler_speed[1] );
//			std::swap( result.max_tackler_impetus[0], result.max_tackler_impetus[1] );
//			std::swap( result.tackler_aggression[0], result.tackler_aggression[1] );
//			std::swap( result.new_tackler_aggression[0], result.new_tackler_aggression[1] );
//			std::swap( result.tacklers_suspended[0], result.tacklers_suspended[1] );
//		}
//	}
}

bool RUTackleHelper::TFDLengthSort( const RUTackleHelper::TFD_LIST& tfd1, const RUTackleHelper::TFD_LIST& tfd2 )
{
	return tfd1.size() > tfd2.size();
}

void RUTackleHelper::GenerateTackledFromDirectionPermutations(
		int n_tacklers,
		const TFD_LIST::iterator& current,
		const TFD_LIST::iterator& end,
		std::list< TFD_LIST >& returned_permutations
) {
	// CRAZY Algorithm Tyrone made up - ask him
	// uses bit patterns to generate initial permutations
	unsigned int n_possible_permutations = (1 << (unsigned int) n_tacklers);

	TFD_LIST::iterator temp;
	unsigned int idx;
	int n_items;
	TFD_LIST curr_list;

	for( unsigned int i = 0; i < n_possible_permutations; i++  ) {
		n_items = 0;

		//returned_permutations.push_back( TFD_LIST() );
		// = returned_permutations.back();
		curr_list.clear();
		idx = n_tacklers - 1;
		for( temp = current; temp != end; ++temp ) {
			if ( !(i & (1 << idx)) ) {
				curr_list.push_back( *temp );
				n_items++;
			}
			idx--;
		}

		// Only add items that are greater than 2 in length and not already in the list
		if ( n_items >= 2 ) {
			std::list< TFD_LIST >::iterator check_it;
			check_it = std::find( returned_permutations.begin(), returned_permutations.end(), curr_list );
			// Now we add
			if ( check_it == returned_permutations.end() ) {
				returned_permutations.push_back( curr_list );
			}
		}
	}

	// Now they need to be sorted based on the current order but
	// most elements to least elements - this is done by an
	// insertion sort into the returned list
	std::list< TFD_LIST >::iterator it;
	returned_permutations.sort( TFDLengthSort );

	// Print out the list - DEBUG
	/*
	TFD_LIST::iterator tfd_it;
	for( it = returned_permutations.begin(); it != returned_permutations.end(); ++it ) {
		TFD_LIST& curr_list = *it;
		MabString curr_str = "ITEM ";

		for( tfd_it = curr_list.begin(); tfd_it != curr_list.end(); ++tfd_it ) {
			TACKLED_FROM_DIRECTION curr = *tfd_it;
			curr_str += " ";
			curr_str += RUTackleResult::GetTackledFromDirectionString( curr );
		}
		MABLOGMSG( LOGCHANNEL_ALWAYS, LOGTYPE_INFO, curr_str.c_str() );
	}
	*/
}

// Should this role do an AI tackle - We use this also for when
// humans take over an AI player but give them a short grace period for tackling the player
bool RUTackleHelper::ShouldDoAnAITackle(ARugbyCharacter* player, float rand_for_offside_tackle)
{
#if !UE_BUILD_SHIPPING
	if (CVarDisableTackle.GetValueOnGameThread())
	{
		bool bFullBackCanTackle = CVarDisableTackle.GetValueOnGameThread() >= 2;
		bool bFullBack = player->GetRole()->RTTGetType() == RURoleFullback::RTTGetStaticType();
		if (!bFullBackCanTackle || !bFullBack)
		{
			return false;
		}
	}
#endif
	/// Other rules
	/// If we are AI then
	///   Do not take over an human that is already tackling the ball holder
	///	  Only tackle if:
	///	     We can do a more dominant tackle towards their goal line
	///      They can still offload the ball
	///      There is no-one else to pass to
	bool player_is_human = player->GetHumanPlayer() != NULL;
	bool player_is_ai = !player_is_human;
	ARugbyCharacter* ball_holder = game->GetGameState()->GetBallHolder();

	if ( player_is_ai && ball_holder && ball_holder->GetActionManager()->IsActionRunning( ACTION_TACKLEE ) )
	{
		RUActionTacklee* action_tacklee = ball_holder->GetActionManager()->GetAction<RUActionTacklee>();
		RUTackleResult& tackle_result = action_tacklee->GetTackleResult();
		bool existing_tackler_is_human = tackle_result.tacklers[0] != NULL && tackle_result.tacklers[0]->GetHumanPlayer() != NULL;
		const static float MIN_VEL_BACKWARDS = -1.0f;
		bool being_tackled_backwards = (ball_holder->GetMovement()->GetCurrentVelocity().z * ball_holder->GetAttributes()->GetPlayDirection()) < MIN_VEL_BACKWARDS;

		float bh_play_angle = ball_holder->GetAttributes()->GetTeam()->GetPlayAngle();
		float us_to_bh_angle = SSMath::CalcAngleFromPoints( player->GetMovement()->GetCurrentPosition(), ball_holder->GetMovement()->GetCurrentPosition() );
		const static float TACKLE_BACKWARDS_MIN_ANGLE = 145.0f;
		bool likely_we_will_tackle_backwards = MabMath::Fabs( MabMath::AngleDelta( bh_play_angle, us_to_bh_angle ) ) > MabMath::Deg2Rad( TACKLE_BACKWARDS_MIN_ANGLE );
		bool is_an_offload = RUActionPass::IsAnOffload( ball_holder );
		bool bh_can_offload = is_an_offload && action_tacklee->CanOffloadNow();
		bool bh_can_pass = !is_an_offload && action_tacklee->CanPassNow();
		bool tackle_is_successful = tackle_result.successful;
		bool likely_we_will_do_a_better_tackle = !tackle_is_successful || (tackle_result.tackle_result == TRT_STANDARD && tackle_result.dominance <= TACKLE_DOMINANCE::TDOM_ATTACK_EQ);

		/// Don't override human tackles
		if ( existing_tackler_is_human )
			return false;

		/// If the current tackle is going to fail then tackle them
		if ( !tackle_is_successful )
			return true;

		/// If the ball holder cannot offload then don't try
		if ( !(bh_can_offload || bh_can_pass) )
			return false;

		if ( !being_tackled_backwards && likely_we_will_do_a_better_tackle && likely_we_will_tackle_backwards )
			return true;

		return false;
	}

	// Is this a human player - if so allow them some lenience
	if ( player_is_human )
	{
		// Check if player is in tackle range
		RUTackleRangeMeta meta_data;
		GetTackleRangeMeta( ball_holder, player, meta_data );
		if ( meta_data.in_normal_tackle_range )
		{
			// Allow tackle if player just became human
			const float STILL_DO_AI_TACKLE_FOR_HUMAN_TIME = 0.5f;
			return (player->GetHumanPlayer()->GetTimeSinceLastPlayerChange() < STILL_DO_AI_TACKLE_FOR_HUMAN_TIME);
		}
		return false;
	}
	else
	{

		// ai take into account offsideness when deciding to tackle		
		float offsideness = player->GetOffSideIndicator()->GetOffsideValue();
		if (offsideness == 0.0f)
			return true;

		// if you have humans on your team then don't do offside tackles, it sucks
		if (player->GetAttributes()->GetTeam()->GetNumHumanPlayers() > 0)
			return false;

		const float MIN_OFFSIDE_TACKLE_CHANCE = 0.002f; // super low chance, good boys shouldn't tackle offside
		const float MAX_OFFSIDE_TACKLE_CHANCE = 0.05f;
		float probability_to_break_rules = MabMath::Lerp(MIN_OFFSIDE_TACKLE_CHANCE, MAX_OFFSIDE_TACKLE_CHANCE, player->GetAttributes()->GetDiscipline());

		return rand_for_offside_tackle < probability_to_break_rules;

	}


}

void RUTackleHelper::SetForcedTackleResult( const RUTackleResult* tackle_result )
{
	if ( tackle_result )
	{
		forced_tackle_result_set = true;
		forced_tackle_result = *tackle_result;
	}
	else
	{
		forced_tackle_result_set = false;
	}
}

bool RUTackleHelper::IsGroundGetup( ARugbyCharacter* /*tacklee*/, ARugbyCharacter* /*tackler*/, TACKLER_DIRECTION /*td*/, TACKLED_FROM_DIRECTION /*tfd*/, bool /*heavy_tackle*/, RUTackleResult& /*result*/ )
{
	// This method sets everything up anyway

	/// Need to detect that player has gone down to collect ball and trying to getup
	return false;

	//result.state_machine = TSM_SUCCESS;
}

bool RUTackleHelper::IsAFend( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, TACKLER_DIRECTION /*td*/, TACKLED_FROM_DIRECTION /*tfd*/, bool /*heavy_tackle*/, RUTackleResult& result )
{
	FVector tackler_position = tackler->GetMovement()->GetCurrentPosition();

	// If human, the player must have activated the fending before the contact and determine if the tackle can be blocked
	RUActionManager* action_manager = tacklee->GetActionManager();
	if ( !action_manager->IsActionRunning(ACTION_FEND) )
	{
		return false;
	}

	result.tackle_result = TRT_FEND2;
	/// Adjust the play rate based on players break tackle ability
	result.play_rate = 0.5f + tacklee->GetAttributes()->GetBreakTackleAbility() * 0.85f;
	MabMath::ClampLower( result.play_rate, 1.0f );
	result.can_offload = true;
	result.new_tacklee_aggression = tacklee->GetAttributes()->GetAggression() * 0.5f;
	result.new_tackler_aggression[0] = tackler->GetAttributes()->GetAggression() * 0.8f;
	result.held = false;
	result.taken_down = false;
	result.successful = !action_manager->GetAction<RUActionFend>()->CanBlockTackler(tackler);
	result.state_machine = result.successful ? TSM_SUCCESS : TSM_FAILED;
	if ( result.successful )
	{
		result.fend_tackle_type = FTT_FEND_FAILED;
		result.anim_sequences |= TAS_TAKEN_DOWN;
		result.anim_sequences |= TAS_GROUND_STRUGGLE;
		result.anim_sequences |= TAS_GROUND_RELEASE;
	}
	else
	{
		result.fend_tackle_type = FTT_FEND_SUCCESS;
	}

	result.fend_type = FT_FRONTLEFTSTRAIGHT;
	result.body_position[0] = TACKLE_BODY_POSITION::TBP_CHEST;
	result.n_tacklers = 1;
	result.variant_index = 1;

	return true;
}

bool RUTackleHelper::IsASideStep( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, TACKLER_DIRECTION /*td*/, TACKLED_FROM_DIRECTION /*tfd*/, SIDESTEP_SIDE sidestep_side, RUTackleResult& result )
{
	FVector tacklee_to_tackler = tacklee->GetMabTransform().Inverse().TransformPos( tackler->GetMabPosition() );
	bool step_to_correct_side = false;
	switch ( sidestep_side )
	{
		case SSS_UNKNOWN:
			step_to_correct_side = false;
			break;

		case SSS_ANY:
		case SSS_CLEVER:
			step_to_correct_side = tacklee_to_tackler.z > 0.0f;
			break;

		case SSS_LEFT:
			step_to_correct_side = tacklee_to_tackler.z > 0.0f && tacklee_to_tackler.x > -0.25f;
			break;

		case SSS_RIGHT:
			step_to_correct_side = tacklee_to_tackler.z > 0.0f && tacklee_to_tackler.x < 0.25f;
			break;

		default:
			MABBREAK();
			break;
	}

	if ( step_to_correct_side )
	{
		const float SIDESTEP_VARIANCE = 0.20f;
		const float SIDESTEP_FOOLED_RATIO = 1.5f;
		const float SIDESTEP_PARTIAL_FAIL_RATIO = 1.0f;
		const float SIDESTEP_PARTIAL_SUCCESS_RATIO = 0.8f;

		float tacklee_sidestep_chance = tacklee->GetAttributes()->GetAgility() * ((1.0f - SIDESTEP_VARIANCE) + game->GetRNG()->RAND_RANGED_CALL(float, SIDESTEP_VARIANCE));
		float tackler_sidestep_chance = tackler->GetAttributes()->GetAgility() * ((1.0f - SIDESTEP_VARIANCE) + game->GetRNG()->RAND_RANGED_CALL(float, SIDESTEP_VARIANCE));
		float tackle_sidestep_ratio = tacklee_sidestep_chance / tackler_sidestep_chance;
		if ( tackle_sidestep_ratio > SIDESTEP_FOOLED_RATIO )
		{
			result.successful = false;
			result.ss_tackle_type = SSTT_FOOLED;
		}
		else if ( tackle_sidestep_ratio > SIDESTEP_PARTIAL_FAIL_RATIO )
		{
			result.successful = false;
			result.ss_tackle_type = SSTT_PARTIAL_FAIL;
		}
		else if ( tackle_sidestep_ratio > SIDESTEP_PARTIAL_SUCCESS_RATIO )
		{
			result.successful = true;
			result.ss_tackle_type = SSTT_PARTIAL_SUCCESS;
			result.anim_sequences |= TAS_TAKEN_DOWN;
			result.anim_sequences |= TAS_GROUND_STRUGGLE;
			result.anim_sequences |= TAS_GROUND_RELEASE;
		}
		else
		{
			result.successful = true;
			result.ss_tackle_type = SSTT_NOT_FOOLED;
			result.anim_sequences |= TAS_TAKEN_DOWN;
			result.anim_sequences |= TAS_GROUND_STRUGGLE;
			result.anim_sequences |= TAS_GROUND_RELEASE;
		}

		result.tackle_result = TRT_SIDESTEP;
		/// Adjust the play rate based on players break tackle ability
		result.play_rate = 0.5f + tacklee->GetAttributes()->GetBreakTackleAbility() * 0.85f;
		MabMath::ClampLower( result.play_rate, 1.0f );
		result.can_offload = true;
		result.new_tacklee_aggression = tacklee->GetAttributes()->GetAggression() * 0.5f;
		result.new_tackler_aggression[0] = tackler->GetAttributes()->GetAggression() * 0.8f;
		result.held = false;
		result.taken_down = false;
		result.state_machine = result.successful ? TSM_SUCCESS : TSM_FAILED;
		result.body_position[0] = TACKLE_BODY_POSITION::TBP_CHEST;
		result.n_tacklers = 1;
		result.variant_index = 1;
	}
	return step_to_correct_side;
}

bool RUTackleHelper::IsAContestedTackle(ARugbyCharacter* tacklee, ARugbyCharacter* tackler, TACKLER_DIRECTION /*td*/, TACKLED_FROM_DIRECTION /*tfd*/, bool /*heavy_tackle*/, RUTackleResult& result )
{
	if ( result.dominance != TACKLE_DOMINANCE::TDOM_ATTACK_EQ )
		return false;

	if ( !game->GetSpatialHelper()->IsInField( tacklee->GetMovement()->GetCurrentPosition() ) )
	{
		MABLOGDEBUG( "Tacklee is in ingoal so preventing a contested tackle from occurring." );
		return false;
	}

	// if your facing the wrong way then no contest
	float angle_diff = MabMath::Fabs(tacklee->GetMovement()->GetCurrentFacingAngle() - tacklee->GetAttributes()->GetTeam()->GetPlayAngle());
	if (angle_diff > (PI/2.0f) && angle_diff < (2.0f*PI - PI/2.0f))
		return false;

	if ( result.tackler_direction[0] == TD_FORWARD && (result.tackled_from_direction[0] == TFD_FRONT || result.tackled_from_direction[0] == TFD_SIDE_LEFT || result.tackled_from_direction[0] == TFD_SIDE_RIGHT) )
	{
		const static float CONTESTED_PROB = 0.2f;
		if ( game->GetRNG()->RAND_CALL(float) > CONTESTED_PROB )
			return false;

		// Make sure that the tackle body position is either around the chest or the waist
		result.body_position[0] = TACKLE_BODY_POSITION::TBP_CHEST;
		result.tackle_result = TRT_CONTESTED;
		result.state_machine = TSM_CONTESTED;
		result.taken_down = false;
		result.new_tacklee_aggression = 0.0f;
		result.new_tackler_aggression[0] = tackler->GetAttributes()->GetAggression() * 0.75f;
		result.held = false;
		result.successful = true;
		result.n_tacklers = 1;
		result.anim_sequences |= TAS_HELD_LOOP;
		result.anim_sequences |= TAS_HELD_RELEASE;
		result.anim_sequences |= TAS_TAKEN_DOWN;
		result.anim_sequences |= TAS_BREAK_OUT;
		return true;
	}

	return false;
}

bool RUTackleHelper::IsATryCornerTackle( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, RUTackleResult& tackle_result )
{
	// Check distances to try and side lines
	FieldExtents extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	float distance_to_try_line = extents.y / 2.0f - (tacklee->GetMovement()->GetCurrentPosition().z * tacklee->GetAttributes()->GetTeam()->GetPlayDirection());
	float distance_to_side_line = extents.x / 2.0f - MabMath::Fabs( tacklee->GetMovement()->GetCurrentPosition().x );

	const float MAXIMUM_DISTANCE_FROM_TRY_LINE = 2.0f;
	const float MAXIMUM_DISTANCE_FROM_SIDE_LINE = 2.0f;
	if ( distance_to_try_line > MAXIMUM_DISTANCE_FROM_TRY_LINE || distance_to_side_line > MAXIMUM_DISTANCE_FROM_SIDE_LINE )
		return false;


	// Check warping
	if ( !HasValidTackleAnim( tacklee, tackler, TRT_TRY_CORNER ) )
		return false;

	// Construct tackle result
	tackle_result.n_tacklers = 1;
	tackle_result.variant_index = 1;
	tackle_result.tackle_result = TRT_TRY_CORNER;
	tackle_result.try_tackle_type = TRY_TACKLE_TYPE::TTT_UNKNOWN;
	tackle_result.body_position[0] = TACKLE_BODY_POSITION::TBP_ANKLE;
	tackle_result.can_offload = false;
	tackle_result.new_tacklee_aggression = tacklee->GetAttributes()->GetAggression() * 0.5f;
	tackle_result.new_tackler_aggression[0] = tackler->GetAttributes()->GetAggression() * 0.5f;
	tackle_result.successful = false;
	tackle_result.held = false;
	tackle_result.taken_down = false;
	tackle_result.anim_sequences = TAS_IMPACT;
	tackle_result.state_machine = TSM_FAILED;
	tackle_result.is_video_ref = false;
	tackle_result.is_try_tackle = false;
	tackle_result.prob_video_ref_try = 1.0f;
	tackle_result.align_anim = TAT_ALIGN_TACKLER;
	return true;
}

bool RUTackleHelper::IsATryTackle(  ARugbyCharacter* tacklee, ARugbyCharacter* tackler, TACKLER_DIRECTION td, TACKLED_FROM_DIRECTION tfd, bool heavy_tackle, RUTackleResult& result )
{
	MABUNUSED(heavy_tackle);
	MABUNUSED(tfd);
	MABUNUSED(td);


	/*
	Hi Guys,

	I've come up with the animation requirements for the video ref and
	around the try area related tackles.  Please note that we will *not*
	be attempting to have players try and just put the ball over the line
	or anything related to line proximity.  It will be too difficult to try
	and get correct in the time we have left.  What we will be doing is
	playing these try animations when we are confident that they are very
	close to, if not over the line already (max 1 metre away).  We need to
	make sure that all tackles that result in going to video ref end up
	with at least 1.5 metres forward progress to cover ourselves.

	All tackles will be single man and just impact loops only.

	There will be basically 5 sub types of tackles to be used around the try
	area.  The first three will all be related to video ref and will all
	involve the tacklee making a concerted effort to put the ball down
	(assuming he is on or over the line already), and the tackler making a
	concerted effort to prevent them from putting the ball down but the final
	result for video ref purposes should look like one of the following:

	--Type 1-- Prefix = TS
	Tackle marginal but looks like successful try.  This put down needs to look
	like a try that you would award 80% of the time but disallow 20% of the
	time. --Type 2-- Prefix = TM Tackle marginal but could go either way.  This
	tackle should look like a 50% I would give it, 50% it would be disallowed.
	--Type 3-- Prefix = TF Tackle marginal but doesn't look like a try.  This
	put down needs to look like a try that you would award 20% of the time but
	disallow 80% of the time.

	All of the above tackles should have 1 variation each for each tackled from
	direction left, right, head on, from behind and all be tackled around the
	chest initially.

	The tackle name for all of the above should have the form

	TR/TE <Prefix> + C + IM + <Dir> + <Variant>

	e.g.

	TETSCIMFL01 for a type 1, from left variant 01.

	This is a total of 12 animations.

	The other two types are:

	--Type 4-- Prefix = TH
	The fourth type will probably not go to video ref but will be an obvious
	held up tackle.  (At no point will the ball look like it has been
	grounded). All should be tackled around the chest and there will be one
	from each direction for a total of 4 animations

	e.g. TETHCIMHO01.

	--Type 5-- Prefix = TP
	No try pushed back while on the goal line.  These will allow the player to
	tackle guys who are diving for the try line.  The idea is that these will be
	head on tackles with the tackler going down low and wrestling or quickly driving
	the attacker back a metre or two.  All tackle will be head on, chest (body part
	is almost irrelevant though but use this for convention). Suggest 4 variations
	on this type of tackle.

	e.g.

	TETPCIMHO01
	TETPCIMHO02
	TETPCIMHO03
	TETPCIMHO04

	I suggest looking closely at some reference to get an idea for what these
	should look like.  If you have any questions on the above or are unsure of
	what to do, please make sure that you come and talk to me.
	*/

	// THE BIGGEST HACK EVER! VIDEO REF NOT AWARDING POINTS IN EXTRA TIME AND ENDING THE HALF
	// SOLUTION: disable the vidref in extra time
	if ( game->GetGameTimer() && game->GetGameTimer()->IsExpired() )
	{
		result.is_video_ref = false;
		result.is_try_tackle = false;
		return false;
	}

	// TODO : Check that this is what is needed
	result.state_machine = TSM_SUCCESS;

	// Work out if we are close to the try line and then work out what type of try we want
	FieldExtents extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	float dist_to_try_line = extents.y / 2.0f - (tacklee->GetMovement()->GetCurrentPosition().z * tacklee->GetAttributes()->GetTeam()->GetPlayDirection());

	// Now do sub cases depending on how close we are
	const float DIST_THRESHOLD_FOR_VIDEO_REF_START = 1.0f;
	const float DIST_THRESHOLD_FOR_VIDEO_REF_END = -5.0f;

	// If we are close to the try line but facing back towards our own goal line then we want
	// to try and avoid try tackles in these circumstances
	const float PREVENT_RUNBACK_THRESHOLD = 2.5f;
	if ( dist_to_try_line <= DIST_THRESHOLD_FOR_VIDEO_REF_START && dist_to_try_line > -PREVENT_RUNBACK_THRESHOLD )
	{
		float angle_delta_from_facing = MabMath::Fabs( MabMath::AngleDelta( tacklee->GetAttributes()->GetTeam()->GetPlayAngle(), tacklee->GetMovement()->GetCurrentFacingAngle() ) );
		if ( angle_delta_from_facing > MabMath::Deg2Rad( 95.0f ) )
		{
			return false;
		}
	}

	float tackle_ratio_diff = result.actual_tacklee_impetus / result.actual_tacklers_impetus;

	const float PUSHBACK_THRESHOLD = 0.8f;
	MABUNUSED(PUSHBACK_THRESHOLD);

	const float DIST_THRESHOLD_FOR_PUSH_BACK_START = 4.0f;
	const float DIST_THRESHOLD_FOR_PUSH_BACK_END = -0.1f;
	if ( dist_to_try_line < DIST_THRESHOLD_FOR_PUSH_BACK_START && dist_to_try_line > DIST_THRESHOLD_FOR_PUSH_BACK_END )
	{
		// Check the tackle impetus
		if ( tackle_ratio_diff < PUSHBACK_THRESHOLD && result.tackled_from_direction[0] == TFD_FRONT )
		{
			result.tackle_result = TRT_TRY_PUSHED;
			result.try_tackle_type = TRY_TACKLE_TYPE::TTT_PUSHBACK;
			result.body_position[0] = TACKLE_BODY_POSITION::TBP_CHEST;
			result.can_offload = false;
			result.new_tacklee_aggression = tacklee->GetAttributes()->GetAggression() * 0.5f;
			result.new_tackler_aggression[0] = tackler->GetAttributes()->GetAggression() * 0.5f;
			result.held = false;
			result.taken_down = true;
			result.successful = true;
			result.n_tacklers = 1;
			result.variant_index = 1;
			result.is_video_ref = false;
			result.is_try_tackle = false;
			result.prob_video_ref_try = 0.8f;
			result.anim_sequences |= TAS_TAKEN_DOWN | TAS_GROUND_STRUGGLE | TAS_GROUND_RELEASE;
			return true;
		}
	}

	// If video ref is not enabled then return - none of the below tackle types will be shown
	if ( !game->GetGameSettings().game_settings.custom_rule_video_ref )
		return false;

	// slight deviation here from the usual logic - we're gonna assume that due to the random nature of the sport
	// no matter who has the impetus advantage, probably half the time the tackler is going to 'get it wrong'
	// and the ballholder is going to plant an obvious try.
	const float PROB_TRY_TACKLE = 0.40f;
	bool is_normal_tackle = game->GetRNG()->RAND_CALL(float) > PROB_TRY_TACKLE;

	const float PROB_NOT_TRY_THRESHOLD = 0.9f;
	const float MAYBE_TRY_THRESHOLD = 1.2f;
	const float PROB_TRY_THRESHOLD = 1.8f;

#ifdef ENABLE_GAME_DEBUG_MENU
#ifdef ENABLE_TRIGGER_RULES_DEBUG_SETTINGS
	int next_tackle_try_probability = SIFDebug::GetRulesDebugSettings()->GetNextTackleTryProbability();
	if ( next_tackle_try_probability != (int)TRY_TACKLE_TYPE::TTT_UNKNOWN )
	{
		switch ( next_tackle_try_probability )
		{
			case (int)TRY_TACKLE_TYPE::TTT_SUCCESS_LIKELY:
				tackle_ratio_diff = PROB_TRY_THRESHOLD + 1.0f;
				break;

			case (int)TRY_TACKLE_TYPE::TTT_SUCCESS_MARGINAL:
				tackle_ratio_diff = MabMath::Lerp( MAYBE_TRY_THRESHOLD, PROB_TRY_THRESHOLD, 0.5f );
				break;

			case (int)TRY_TACKLE_TYPE::TTT_FAIL_LIKELY:
				tackle_ratio_diff = MabMath::Lerp( PROB_NOT_TRY_THRESHOLD, MAYBE_TRY_THRESHOLD, 0.5f );
				break;

			case (int)TRY_TACKLE_TYPE::TTT_HELDUP:
				tackle_ratio_diff = MabMath::Lerp( PUSHBACK_THRESHOLD, PROB_NOT_TRY_THRESHOLD, 0.5f );
				break;

			case (int)TRY_TACKLE_TYPE::TTT_PUSHBACK:
			case (int)TRY_TACKLE_TYPE::TTT_JUMPOVER:
			case (int)TRY_TACKLE_TYPE::TTT_UNKNOWN:
			default:
				break;
		}
	}
	is_normal_tackle = false;
#endif
#endif

	//RUPORT: TODO: Add upright checks
	bool tacklee_is_upright = true;//tacklee->GetAnimation()->IsPlayerUpright();

	// If was not a push back so check for held up/video ref type trys
	if ( tacklee_is_upright && !is_normal_tackle && dist_to_try_line < DIST_THRESHOLD_FOR_VIDEO_REF_START && dist_to_try_line > DIST_THRESHOLD_FOR_VIDEO_REF_END )
	{
		// Held up
		if ( tackle_ratio_diff < PROB_NOT_TRY_THRESHOLD && HasValidTackleAnim(tacklee, tackler, TRT_TRY, true, TACKLE_BODY_POSITION::TBP_CHEST, TRY_TACKLE_TYPE::TTT_HELDUP) )
		{
			result.tackle_result = TRT_TRY;
			result.try_tackle_type = TRY_TACKLE_TYPE::TTT_HELDUP;
			result.body_position[0] = TACKLE_BODY_POSITION::TBP_CHEST;
			result.can_offload = false;
			result.new_tacklee_aggression = tacklee->GetAttributes()->GetAggression() * 0.5f;
			result.new_tackler_aggression[0] = tackler->GetAttributes()->GetAggression() * 0.5f;
			result.held = false;
			result.taken_down = false;
			result.successful = true;
			result.n_tacklers = 1;
			result.variant_index = 1;
			result.is_video_ref = false;
			result.is_try_tackle = true;
			result.prob_video_ref_try = 0.0f;
			return true;
		} // Probably not a try
		else if ( tackle_ratio_diff < MAYBE_TRY_THRESHOLD && HasValidTackleAnim(tacklee, tackler, TRT_TRY, true, TACKLE_BODY_POSITION::TBP_CHEST, TRY_TACKLE_TYPE::TTT_FAIL_LIKELY) )
		{
			result.tackle_result = TRT_TRY;
			result.try_tackle_type = TRY_TACKLE_TYPE::TTT_FAIL_LIKELY;
			result.body_position[0] = TACKLE_BODY_POSITION::TBP_CHEST;
			result.can_offload = false;
			result.new_tacklee_aggression = tacklee->GetAttributes()->GetAggression() * 0.5f;
			result.new_tackler_aggression[0] = tackler->GetAttributes()->GetAggression() * 0.5f;
			result.held = false;
			result.taken_down = false;
			result.successful = true;
			result.n_tacklers = 1;
			result.variant_index = 1;
			result.is_video_ref = true;
			result.is_try_tackle = true;
			result.prob_video_ref_try = 0.2f;

			return true;
		} // Marginal try
		else if ( tackle_ratio_diff < PROB_TRY_THRESHOLD && HasValidTackleAnim(tacklee, tackler, TRT_TRY, true, TACKLE_BODY_POSITION::TBP_CHEST, TRY_TACKLE_TYPE::TTT_SUCCESS_MARGINAL) )
		{
			result.tackle_result = TRT_TRY;
			result.try_tackle_type = TRY_TACKLE_TYPE::TTT_SUCCESS_MARGINAL;
			result.body_position[0] = TACKLE_BODY_POSITION::TBP_CHEST;
			result.can_offload = false;
			result.new_tacklee_aggression = tacklee->GetAttributes()->GetAggression() * 0.5f;
			result.new_tackler_aggression[0] = tackler->GetAttributes()->GetAggression() * 0.5f;
			result.held = false;
			result.taken_down = false;
			result.successful = true;
			result.n_tacklers = 1;
			result.variant_index = 1;
			result.is_video_ref = true;
			result.is_try_tackle = true;
			result.prob_video_ref_try = 0.5f;
			return true;
		} // Looks like a try
		else if ( HasValidTackleAnim(tacklee, tackler, TRT_TRY, true, TACKLE_BODY_POSITION::TBP_CHEST, TRY_TACKLE_TYPE::TTT_SUCCESS_LIKELY) )
		{
			result.tackle_result = TRT_TRY;
			result.try_tackle_type = TRY_TACKLE_TYPE::TTT_SUCCESS_LIKELY;
			result.body_position[0] = TACKLE_BODY_POSITION::TBP_CHEST;
			result.can_offload = false;
			result.new_tacklee_aggression = tacklee->GetAttributes()->GetAggression() * 0.5f;
			result.new_tackler_aggression[0] = tackler->GetAttributes()->GetAggression() * 0.5f;
			result.held = false;
			result.taken_down = false;
			result.successful = true;
			result.n_tacklers = 1;
			result.variant_index = 1;
			result.is_video_ref = true;
			result.is_try_tackle = true;
			result.prob_video_ref_try = 0.8f;
			return true;
		}
	}

	return false;
}

bool RUTackleHelper::IsAnAnkleTap(  ARugbyCharacter* tacklee, ARugbyCharacter* tackler, TACKLER_DIRECTION td, TACKLED_FROM_DIRECTION tfd, bool heavy_tackle, RUTackleResult& result )
{
	MABUNUSED(td);
	MABUNUSED(heavy_tackle);

	bool forceAnkleTap = false;

#if !UE_BUILD_SHIPPING
	if (CVarNextTackleAnkleTap.GetValueOnGameThread() > 0)
	{
		// Override set-by mask to clear so that variable can be cleared, if defaul SetByCode mask is used then it fails as SetByConsole has priority.
		CVarNextTackleAnkleTap.AsVariable()->Set(0, EConsoleVariableFlags::ECVF_SetByConsole);
		forceAnkleTap = true;
	}
#endif
	
	if (!forceAnkleTap)
	{
		// No ankle taps from the front
		if (tfd == TFD_FRONT)
			return false;

		// If the player is already being tackled then cannot do this

		// If the player that is being chased has a higher top speed than
		// this player and the closure rate is very low or then do an ankle tap

		// Tacklee cannot be in the middle of a tackler either
		if (tacklee->GetActionManager()->IsActionRunning(ACTION_TACKLEE))
			return false;

		// Work out if we are going to reach them before they can reach the goal line
		if (WillTackleeReachGoal(tacklee))
			return false;

		// Tacklee must be at least jog speed
		if (tacklee->GetMovement()->GetCurrentSpeed() < tacklee->GetMovement()->GetIdealSpeed(AS_JOG))
			return false;

		if (HighClosureRate(tackler, tacklee))
			return false;

		if (tacklee->GetMovement()->GetMaxSpeed() < tackler->GetMovement()->GetMaxSpeed())
			return false;

		// Check to see that the tacklee has very little in front on him
		RLP_FILTERPARAMETERS params;
		RLPResultList defending_players;

		ERugbyPlayDirection tacklee_play_dir = tacklee->GetAttributes()->GetTeam()->GetPlayDirection();

		params.filters = RLP_FILTER_TEAM | RLP_FILTER_Z_CUTOFF | RLP_FILTER_EXCLUDE_PLAYER | RLP_FILTER_MAX_X_DIST;
		params.team = tacklee->GetAttributes()->GetOppositionTeam();
		params.z_cutoff_direction = (ERugbyPlayDirection)-tacklee_play_dir;
		params.z_cutoff_position = tacklee->GetMovement()->GetCurrentPosition().z;
		params.exclude_player = tackler;
		params.max_x_dist = 3.0f;
		params.max_x_ref = tacklee->GetMovement()->GetCurrentPosition().x;

		game->GetPlayerFilters()->GetPlayerPlayerDistanceSort()->SetReferencePlayer(tacklee);
		game->GetFilteredPlayerList(defending_players, params, game->GetPlayerFilters()->GetPlayerPlayerDistanceSort());

		if (defending_players.size() > 0) {
			return false;
		}
	}

	result.tackle_result = TRT_ANKLE_TAP2;
	result.body_position[0] = TACKLE_BODY_POSITION::TBP_ANKLE;
	result.can_offload = true;
	result.new_tacklee_aggression = tacklee->GetAttributes()->GetAggression() * 0.5f;
	result.new_tackler_aggression[0] = tackler->GetAttributes()->GetAggression() * 0.5f;
	result.held = false;

#if ENABLE_SUCCESSFUL_ANKLE_TAPS
	float prob = 0.0f;
	// If the tacklers team is not human controlled, and they are versing a human team
	if (tackler->GetAttributes()->GetTeam()->GetNumHumanPlayers() == 0 && tacklee->GetAttributes()->GetTeam()->GetNumHumanPlayers() > 0)
	{
		prob = tackle_dominance_meta[(int)result.dominance].prob_success_ai[(int)tackler->GetGameWorld()->GetGameSettings().difficulty][0];
		// Nick  WWS 7s to Womens //[(int)tackler->GetGameWorld()->GetGameSettings().game_settings.GetGameMode()];
	}
	else
	{
		prob = tackle_dominance_meta[(int)result.dominance].prob_success_human[(int)tackler->GetGameWorld()->GetGameSettings().difficulty][0];
		// Nick  WWS 7s to Womens //[(int)tackler->GetGameWorld()->GetGameSettings().game_settings.GetGameMode()];
	}

	if (game->GetRNG()->RAND_RANGED_CALL(float, 1.0f) <= prob)
	{
		result.successful = true;
		result.taken_down = true;
		result.state_machine = TSM_SUCCESS;
		result.anim_sequences |= TAS_TAKEN_DOWN | TAS_GROUND_STRUGGLE | TAS_GROUND_RELEASE;
	}
	else
#endif
	{
		result.successful = false;
		result.taken_down = false;
		result.state_machine = TSM_FAILED;
	}

	result.n_tacklers = 1;
	result.variant_index = 1;

	return true;
}

bool RUTackleHelper::IsAHeadHigh(  ARugbyCharacter* tacklee, ARugbyCharacter* tackler, TACKLER_DIRECTION td, TACKLED_FROM_DIRECTION tfd, bool heavy_tackle, RUTackleResult& result )
{
	MABUNUSED(heavy_tackle);
	MABUNUSED(td);
	MABUNUSED(tfd);
	MABUNUSED(result);
	FVector tackler_to_tacklee = (tacklee->GetMovement()->GetCurrentPosition() - tackler->GetMovement()->GetCurrentPosition()).Unit();

	/// TYRONE : Prevent head highs from happening while a pass is running.  Can lead to complex code after head high, penalty injury so p
	if ( tacklee->GetActionManager()->IsActionRunning( ACTION_PASS ) )
		return false;

	// dont head high near the try line/zone, we don't handle penalty goals and such well
	const float NO_HEAD_HIGH_IN_TRY_ZONE_MARGIN = 4.0f;
	if (MabMath::Fabs(tacklee->GetMovement()->GetCurrentPosition().z) > (FIELD_LENGTH * 0.5f - NO_HEAD_HIGH_IN_TRY_ZONE_MARGIN))
		return false;

#ifdef ENABLE_GAME_DEBUG_MENU
#ifdef ENABLE_TRIGGER_RULES_DEBUG_SETTINGS
	if( SIFDebug::GetRulesDebugSettings()->IsNextTackleHigh() )
	{
		float rotateAngle = FMath::RadiansToDegrees(-(tackler->GetMovement()->GetCurrentFacingAngle() + tackler->GetMovement()->GetCurrentFacingAngle()) / 2.0f);
		FVector v = tackler_to_tacklee.RotateAngleAxis(rotateAngle, FVector::Y_AXIS);
		TACKLER_ARM ta = TRA_NONE;
		if ( v.x > 0.0f )
			ta = TRA_LEFT;
		else
			ta = TRA_RIGHT;

		result.tackle_result = TRT_HEAD_HIGH2;
		result.tackler_arm[0] = ta;
		result.can_offload = false;
		result.new_tacklee_aggression = 0.0f;
		result.new_tackler_aggression[0] = tackler->GetAttributes()->GetAggression() * 0.8f;
		result.held = false;
		result.taken_down = true;
		result.state_machine = TSM_SUCCESS;
		result.successful = true;
		result.body_position[0] = TACKLE_BODY_POSITION::TBP_HEAD;
		result.n_tacklers = 1;
		result.variant_index = 1;

		return true;
	}
#endif
#endif

#ifndef ENABLE_ALWAYS_HEAD_HIGH_TACKLE
	// Don't allow high tackles in tutorials.
	if ( game->GetGameSettings().game_settings.game_type == GAME_TRAINING || game->GetGameSettings().game_settings.game_type == GAME_MENU )
		return false;
#endif

	// Overriding check - do not let AI tackler cause head highs if there are human players on the team
#ifndef ENABLE_ALWAYS_HEAD_HIGH_TACKLE
#if !UE_BUILD_SHIPPING
	if (CVarNextTackleYellowCard.GetValueOnGameThread() == 0 && CVarNextTackleHigh.GetValueOnGameThread() == 0)
	{
#endif
		int num_human_players = tackler->GetAttributes()->GetTeam()->GetNumHumanPlayers();
		bool no_human = tackler->GetHumanPlayer() == NULL;
		MABUNUSED(num_human_players);
		MABUNUSED(no_human);
		if (tackler->GetAttributes()->GetTeam()->GetNumHumanPlayers() > 0 && tackler->GetHumanPlayer() == NULL)
			return false;
#if !UE_BUILD_SHIPPING
	}
#endif
#endif

	// Check 1 - make sure the tacklee is not being hit from behind
	if ( tfd == TFD_BEHIND )
		return false;

	// Check 2 - make sure that the either the tacklee or tackler is running at
	// sufficient speed
#ifndef ENABLE_ALWAYS_HEAD_HIGH_TACKLE
#if !UE_BUILD_SHIPPING
	if (CVarNextTackleYellowCard.GetValueOnGameThread() == 0 && CVarNextTackleHigh.GetValueOnGameThread() == 0)
	{
#endif
		const float MIN_SPEED_FOR_HEAD_HIGH = 4.5f;

		if (tacklee->GetMovement()->GetCurrentSpeed() < MIN_SPEED_FOR_HEAD_HIGH &&
			tackler->GetMovement()->GetCurrentSpeed() < MIN_SPEED_FOR_HEAD_HIGH)
			return false;
#if !UE_BUILD_SHIPPING
	}
#endif
#endif

#ifndef ENABLE_ALWAYS_HEAD_HIGH_TACKLE
#if !UE_BUILD_SHIPPING
	if (CVarNextTackleYellowCard.GetValueOnGameThread() == 0 && CVarNextTackleHigh.GetValueOnGameThread() == 0)
	{
#endif
		if (result.tackle_heavy_type != THT_HIGH_TACKLE)
		{

// humans will only head high if they use an aggressive tackle
			if (tackler->GetHumanPlayer() != NULL && !heavy_tackle)
				return false;

			// This doesn't match our current player motion/locomotion. It will never happen.
			// --v--
			//// Check 3 - make sure that the tacklee is just to the side of the
			//// tackler and in the correct distance range to effect the tackle
			//FVector tacklee_dir, tackler_dir;
			//SSMath::AngleToMabVector3( tacklee->GetMovement()->GetCurrentFacingAngle(), tacklee_dir );
			//SSMath::AngleToMabVector3( tackler->GetMovement()->GetCurrentFacingAngle(), tackler_dir );

			//static const float HEAD_HIGH_MIN_ANGLE = MabMath::Cos( MabMath::Deg2Rad( 90.0f - 5.0f ) );
			//static const float HEAD_HIGH_MAX_ANGLE = MabMath::Cos( MabMath::Deg2Rad( 90.0f + 5.0f ) );

			//float cos_angle = tackler_dir.Dot( tackler_to_tacklee );

			//if ( (cos_angle > HEAD_HIGH_MIN_ANGLE) || (cos_angle < HEAD_HIGH_MAX_ANGLE) )
			//	return false;
			// --^--

			//const float MIN_DIST_FOR_HEAD_HIGH = 1.2f;
			const float MIN_DIST_FOR_HEAD_HIGH = 0.9f;
			if (game->GetSpatialHelper()->GetPlayerToPlayerDistance(tacklee, tackler) < MIN_DIST_FOR_HEAD_HIGH)
				return false;

			static const float BASE_CHANCE_OF_HEAD_HIGH = 0.02f;	// This has been moved into the slider settings
			float chance_of_head_high = BASE_CHANCE_OF_HEAD_HIGH;
			const static float HEAVY_TACKLE_MULTIPLIER = 2.0f;

			if (game->GetGameSettings().game_settings.game_type != GAME_TRAINING && game->GetGameSettings().game_settings.game_type != GAME_MENU)
			{
				chance_of_head_high = game->GetGameSettings().game_settings.slider_high_tackle_frequency;
			}

			if (heavy_tackle)
				chance_of_head_high *= HEAVY_TACKLE_MULTIPLIER;

			// Add in a bit of randomness
			if (game->GetRNG()->RAND_CALL(float) > chance_of_head_high)
				return false;
		}
#if !UE_BUILD_SHIPPING
	}
#endif
#endif
	// It has succeeded - update the tackle result

	// Find out which arm the tackle will be using
	FVector v;
	MabMatrix::MatrixMultiply(v, tackler_to_tacklee, MabMatrix::RotMatrixY(-(tackler->GetMovement()->GetCurrentFacingAngle() + tackler->GetMovement()->GetCurrentFacingAngle()) / 2.0f));

	TACKLER_ARM ta = TRA_NONE;
	if ( v.x > 0.0f )
		ta = TRA_LEFT;
	else
		ta = TRA_RIGHT;

	result.tackle_result = TRT_HEAD_HIGH2;
	result.state_machine = TSM_SUCCESS;
	result.tackler_arm[0] = ta;
	result.can_offload = false;
	result.new_tacklee_aggression = 0.0f;
	result.new_tackler_aggression[0] = tackler->GetAttributes()->GetAggression() * 0.8f;
	result.held = false;
	result.taken_down = true;
	result.successful = true;
	result.body_position[0] = TACKLE_BODY_POSITION::TBP_HEAD;
	result.n_tacklers = 1;
	result.variant_index = 1;

	return true;
}

bool RUTackleHelper::IsADivingMiss( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, RUTackleResult& result )
{
#ifdef DEBUG_DIVE_TACKLE
	int dd_text_index = 2;
	DD_TEXT_HUMAN(tackler,"RUTackleHelper::IsADivingMiss",dd_text_index);
	dd_text_index++;
#endif

	if ( tackler->GetHumanPlayer() == NULL )
	{
#ifdef DEBUG_DIVE_TACKLE
		DD_TEXT_HUMAN(tackler,"Not human player",dd_text_index);
#endif
		return false;
	}

	result.tackle_result = TRT_DIVE_MISS;
	result.can_offload = true;
	result.new_tacklee_aggression = tacklee->GetAttributes()->GetAggression();
	result.new_tackler_aggression[0] = tackler->GetAttributes()->GetAggression() * 0.8f;

	result.n_tacklers = 1;
	result.tacklers[0] = tackler;
	result.tackler_direction[0]  = GetTacklerDirection( result.tacklers[0], tacklee );
	result.tacklee = tacklee;

	// Since we are missing out which way to dive that makes it look believable
	result.held = false;
	result.taken_down = false;
	result.successful = false;
	result.variant_index = 1;
	result.state_machine = TSM_FAILED;
	result.anim_sequences = TAS_IMPACT;
	result.align_anim = TAT_ALIGN_NONE;

	return true;
}

// Check if tacklee would reach goal before tackle could be triggered
bool RUTackleHelper::WillTackleeReachGoal( ARugbyCharacter* tacklee ) const
{
	FieldExtents extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	ERugbyPlayDirection tacklee_play_dir = tacklee->GetAttributes()->GetTeam()->GetPlayDirection();
	float dist_to_goal = extents.y/2.0f - (tacklee->GetMovement()->GetCurrentPosition().z * tacklee_play_dir);

	float tacklee_min_time_to_goal = dist_to_goal / (tacklee->GetMovement()->GetCurrentVelocity().z * tacklee_play_dir );

	return ( tacklee_min_time_to_goal < 0.0f );
}

// Check if closure rate is high
bool RUTackleHelper::HighClosureRate( ARugbyCharacter* tackler, ARugbyCharacter* tacklee ) const
{
	float closure_rate = game->GetSpatialHelper()->GetPlayerToPlayerClosingRate( tacklee, tackler );
	return ( closure_rate > 7.0f );
}
void RUTackleHelper::GetTackleRangeMeta( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, RUTackleRangeMeta& returned_meta )
{
	MABASSERT( tackler != NULL );

	/// If there is no tacklee - we still want to update charge attacker details
	if ( !tacklee )
	{
		returned_meta.in_immediate_tackle_dist = false;
		returned_meta.in_normal_tackle_range = false;

		/// The below may seem odd but defenders know how to track players who are about to receive the ball and still want to charge
		returned_meta.in_charge_attacker_range = game->GetSpatialHelper()->GetPlayerToBallDistance( tackler ) < MAX_CHARGE_ATTACKER_DIST;
		returned_meta.in_dive_miss_range = false;
	}
	else
	{
		RLPResultList opp_pids;
		RLP_RESULT tackler_res;
		tackler_res.result_list = &opp_pids;
		tackler_res.player = tackler;
		opp_pids.push_back( tackler_res );

		OPP_INTERCEPT_RESULT result = { NULL, FVector::ZeroVector, 0.0f, 0.0f };
		OPP_INTERCEPT_PARAMS params;
		const static float OBSTRUCTION_DIST = 0.0f;
		params.obstruction_dist = OBSTRUCTION_DIST;

		FVector tacklee_target = game->GetStrategyHelper()->GetPlayerInterceptTarget( tacklee );
		game->GetStrategyHelper()->FindOppositionInterceptPoint( tacklee, opp_pids,  tacklee_target, result, &params );

		float bh_closure_time = result.intercept_time;
		float bh_dist             = game->GetSpatialHelper()->GetPlayerToPlayerDistance(      tackler, tacklee );

		if ( bh_dist < MAX_CHARGE_ATTACKER_DIST )
		{
			bh_closure_time = MabMath::Min( bh_closure_time, game->GetSpatialHelper()->GetPlayerToPlayerClosingTime( tacklee, tackler ) );
		}

		const static float MAX_NORMAL_CLOSURE_TIME = 0.27f;
		const static float SIDESTEP_CLOSURE_TIME = 0.60f;

		//SETDEBUGTEXTWORLD( 672138349 + tackler->GetAttributes()->GetIndex(), tackler->GetMovement()->GetCurrentPosition(), MabString( 64, "%0.2f", bh_closure_time ).c_str() );

		/// We are in range if our closure time suits or we are close
		returned_meta.in_immediate_tackle_dist = bh_dist < MAX_TACKLE_DISTANCE;
		returned_meta.in_normal_tackle_range   = bh_closure_time < MAX_NORMAL_CLOSURE_TIME || returned_meta.in_immediate_tackle_dist;
		returned_meta.in_charge_attacker_range = bh_closure_time < MAX_CHARGE_CLOSURE_TIME || bh_dist < MAX_CHARGE_ATTACKER_DIST;
		returned_meta.in_dive_miss_range       = bh_closure_time > MAX_CHARGE_CLOSURE_TIME;
		returned_meta.in_side_step_range	   = bh_closure_time < SIDESTEP_CLOSURE_TIME;
	}
}

//void RUTackleHelper::GetTackleRangeMeta( ARugbyCharacter* tacklee, ARugbyCharacter* tackler, RUTackleRangeMeta& returned_meta )
//{
//	MABASSERT( tackler != NULL );
//
//	/// If there is no tacklee - we still want to update charge attacker details
//	const static float MAX_CHARGE_ATTACKER_DIST = 7.0f;
//	if ( !tacklee )
//	{
//		returned_meta.in_immediate_tackle_dist = false;
//		returned_meta.in_normal_tackle_range = false;
//
//		/// The below may seem odd but defenders know how to track players who are about to receive the ball and still want to charge
//		returned_meta.in_charge_attacker_range = game->GetSpatialHelper()->GetPlayerToBallDistance( tackler ) < MAX_CHARGE_ATTACKER_DIST;
//		returned_meta.in_dive_miss_range = false;
//	}
//	else
//	{
//		//float bh_closure_time = game->GetSpatialHelper()->GetPlayerToPlayerClosingTime( tackler, tacklee );
//		float bh_dist		  = game->GetSpatialHelper()->GetPlayerToPlayerDistance(	tackler, tacklee );
//
//		const static float MAX_NORMAL_CLOSURE_TIME = 0.27f;
//		const static float MAX_CHARGE_CLOSURE_TIME = 0.7f;
//
//		/// We are in range if our closure time suits or we are close
//		returned_meta.in_immediate_tackle_dist = bh_dist < MAX_TACKLE_DISTANCE * 1.5f;
//		returned_meta.in_normal_tackle_range   = bh_closure_time < MAX_NORMAL_CLOSURE_TIME || returned_meta.in_immediate_tackle_dist;
//		returned_meta.in_charge_attacker_range = bh_closure_time < MAX_CHARGE_CLOSURE_TIME || bh_dist < MAX_CHARGE_ATTACKER_DIST;
//		returned_meta.in_dive_miss_range	   = !returned_meta.in_charge_attacker_range;
//	}
//}


// Checks is we have a valid tackle anim
//	- Checks if the tacklee and tackler would warp on alignment for given tackle type
//	- Checks if try tackle would fall in field
bool RUTackleHelper::HasValidTackleAnim( ARugbyCharacter* tacklee, ARugbyCharacter* tackler,
										TACKLE_RESULT_TYPE type, bool successful,
										TACKLE_BODY_POSITION height, TRY_TACKLE_TYPE try_type,
										TACKLE_DOMINANCE dominance, SIDESTEP_TACKLE_TYPE sidestep_type ) const
{

	// Dive miss dont align, so no need to check for warping
	if ( type == TRT_DIVE_MISS )
		return false;

	MABASSERT( tacklee && tackler );	

	if (tacklee && tacklee->GetAnimation() && tacklee->GetAnimation()->GetStateMachine().GetSMTackle())
	{
		return tacklee->GetAnimation()->GetStateMachine().GetSMTackle()->HasValidTryTackleAnim (type, try_type, tacklee, tackler );
	}
	else
	{
		UE_DEBUG_BREAK();
		return false;
	}

	//RUPlayerAnimation* tacklee_animation = tacklee->GetAnimation();

	//float warp_cost = FLT_MAX;

	//char node_name[256];
	//TackleNodeName( node_name, sizeof(node_name), type, true, successful, height, try_type, dominance, sidestep_type );


	//const NodeDef* tacklee_node_def = tacklee->GetAnimation()->FindNodeDef( node_name );
	//MABASSERT( tacklee_node_def );
	//if ( tacklee_node_def )
	//{
	//	// Get contact vector/rotation
	//	NMP::Vector3 contact_vector = NMMabMath::ToMorpheme( tacklee->GetTransform().Inverse().TransformPos(tackler->GetMabPosition()) );
	//	NMP::Quat contact_rotation = NMMabMath::ToMorpheme( tacklee->GetTransform().Inverse() * tackler->GetTransform() ).toQuat();

	//	// Get best tackle anim's alignment cost
	//	if ( tacklee_node_def->type() == NODE_TRY_TACKLE )
	//	{
	//		const TryTackleDef* try_tacklee_def = (const TryTackleDef*)tacklee_node_def;
	//		int best_tackle_node_idx = tacklee_animation->GetTryTackleAnimation( try_tacklee_def, contact_vector );

	//		// Check if tacklee falls in field
	//		if ( tacklee_animation->WillTryTackleFallInField( try_tacklee_def, best_tackle_node_idx ) )
	//			return false;

	//		contact_vector.normalise();
	//		warp_cost = try_tacklee_def->getTackleNodeAlignmentCost( best_tackle_node_idx, contact_vector );
	//	}
	//	else
	//	{
	//		const TackleDef* tacklee_def = (const TackleDef*)tacklee_node_def;
	//		int best_tackle_node_idx = tacklee_def->getBestTackleNodeIndex( contact_vector, contact_rotation );
	//		contact_vector.normalise();
	//		warp_cost = tacklee_def->getTackleNodeAlignmentCost( best_tackle_node_idx, contact_vector );
	//	}
	//}

	//// Check if tacklee or tackler would warp more than threshold
	//const float TACKLE_WARP_THRESHOLD = 0.5f;
	//return warp_cost > TACKLE_WARP_THRESHOLD;
	
}

// Get tackle node def name
void RUTackleHelper::TackleNodeName( char* name, int name_len,
									TACKLE_RESULT_TYPE type, bool tacklee, bool successful,
									TACKLE_BODY_POSITION height, TRY_TACKLE_TYPE try_type,
									TACKLE_DOMINANCE dominance, SIDESTEP_TACKLE_TYPE sidestep_type ) const
{
	MABASSERT( name && name_len > 0 );

	char role[10];
	strcpy( role, tacklee ? "tacklee" : "tackler" );

	memset( name, 0, name_len );

	switch ( type )
	{
	case TRT_TRY:
		snprintf( name, name_len, "%s|%s|%sTackle", RUPlayerAnimation::TACKLES_NODE_PATH, TACKLE_TYPES[type], TACKLE_TRY_PROBABILITIES[(int)try_type] );
		break;

	case TRT_STANDARD:
		snprintf( name, name_len, "%s|%s_%s_%s|%s_%s", RUPlayerAnimation::TACKLES_NODE_PATH, TACKLE_TYPES[type], successful ? "success" : "fail", role, TACKLE_HEIGHTS[(int)height], TACKLE_DOMINANCES[(int)dominance] );
		break;

	case TRT_FEND2:
		snprintf( name, name_len, "%s|%s_%s_%s|tackle", RUPlayerAnimation::TACKLES_NODE_PATH, TACKLE_TYPES[type], successful ? "fail" : "success", role );
		break;

	case TRT_SIDESTEP:
		snprintf( name, name_len, "%s|sidestep_%s_%s|%s", RUPlayerAnimation::TACKLES_NODE_PATH, successful ? "fail" : "success", role, TACKLE_SIDESTEP_TYPES[sidestep_type] );
		break;

	case TRT_ANKLE_TAP2:
	case TRT_TRY_PUSHED:
		snprintf( name, name_len, "%s|%s_%s|%s", RUPlayerAnimation::TACKLES_NODE_PATH, TACKLE_TYPES[type], role, TACKLE_DOMINANCES[(int)dominance] );
		break;

	case TRT_TRY_CORNER:
		snprintf( name, name_len, "%s|%s|tackle", RUPlayerAnimation::TACKLES_NODE_PATH, TACKLE_TYPES[type] );
		break;

	default:
		snprintf( name, name_len, "%s|%s_%s|tackle", RUPlayerAnimation::TACKLES_NODE_PATH, TACKLE_TYPES[type], role );
		break;
	}
}

bool RUTackleResult::isTackleResultTryType()
{
	return ( tackle_result == TRT_TRY || tackle_result == TRT_TRY_CORNER );
}

#if PLATFORM_WINDOWS || PLATFORM_XBOXONE
#ifdef SUPPRESS_DEPRECATION_WARNINGS
#pragma warning(pop)
#endif
#endif