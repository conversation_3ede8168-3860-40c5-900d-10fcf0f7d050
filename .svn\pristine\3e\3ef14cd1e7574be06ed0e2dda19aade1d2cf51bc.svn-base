import random
import csv
import os
from datetime import date, timedelta
import math

# --- Data for Generation ---
POSITION_MAP = {
    'Lock': 1, 'Right Second Row': 2, 'Left Second Row': 4, 'Tighthead Prop': 8,
    '<PERSON>': 16, 'Loosehead Prop': 32, 'Halfback': 64, 'Five-Eighth': 128,
    'Left Winger': 256, 'Inside Centre': 512, 'Outside Centre': 1024,
    'Right Winger': 2048, 'Fullback': 4096,
}
ID_TO_POSITION_MAP = {v: k for k, v in POSITION_MAP.items()}
COMPLEMENTARY_POS_MAP = {
    'Forward': ['Lock', 'Loosehead Prop', 'Tighthead Prop', 'Left Second Row', 'Right Second Row'],
    'Halves': ['Halfback', 'Five-Eighth', 'Hooker'],
    'Outside Back': ['Left Winger', 'Right Winger', 'Inside Centre', 'Outside Centre', 'Fullback']
}
NSW_TEAMS = {"Penrith Panthers", "Sydney", "South Sydney", "Parramatta", "Manly-Warringah", "Cronulla-Sutherland", "Canterbury-Bankstown", "Newcastle", "St George Illawarra", "Wests Sydney", "Canberra", "New Zealand"}
QLD_TEAMS = {"Brisbane", "North Queensland", "Gold Coast", "Redcliffe"}
PLAYER_CSV_HEADERS = [
    "id", "first_name", "last_name", "commentary_name", "custom", "artist_customised", "m_generic", "dob", "height",
    "weight", "nation", "ethnicity", "position_category1_id", "position_category2_id", "position_category3_id",
    "position_category1_r7_id", "position_category2_r7_id", "position_category3_r7_id", "preferred_foot",
    "fitness", "agility", "speed", "acceleration", "aggressiveness", "tackle_ability", "break_tackle_ability",
    "pass_accuracy", "offload_ability", "general_kick_accuracy", "goal_kick_accuracy", "catch_ability", "strength",
    "mental_agility", "jump_ability", "discipline", "influence", "star_factor", "player_proportions_id",
    "franchise_region", "rookie", "caps", "license_restrict", "r7_exclusive", "skin_texture", "shirt_style",
    "sleeve", "sock_style", "head_mesh", "head_texture", "eye_texture", "hair_mesh", "hair_texture", "hair_tint_0",
    "hair_tint_1", "hair_tint_2", "beard_mesh", "beard_tint_0", "beard_tint_1", "beard_tint_2", "age_texture",
    "age_intensity", "bleach_texture", "bleach_intensity", "brow_line_texture", "brow_line_intensity",
    "crows_feet_texture", "crows_feet_intensity", "eye_darkening_texture", "eye_darkening_intensity",
    "eyebrow_texture", "eyebrow_intensity", "eyebrow_tint", "florid_texture", "florid_intensity", "freckle_texture",
    "freckle_intensity", "frown_line_texture", "frown_line_intensity", "laugh_line_texture",
    "laugh_line_intensity", "lower_mouth_texture", "lower_mouth_intensity", "mole_texture", "mole_intensity",
    "nose_texture", "nose_intensity", "philtrum_texture", "philtrum_intensity", "pimple_texture",
    "pimple_intensity", "scar_texture", "scar_intensity", "stubble_texture", "stubble_intensity", "stubble_tint",
    "mouth_guard_texture", "mouth_guard_tint_0", "mouth_guard_tint_1", "mouth_guard_tint_2", "shoes_club_mesh",
    "shoes_club_texture", "shoe_l_club_tint_0", "shoe_l_club_tint_1", "shoe_l_club_tint_2", "shoe_r_club_tint_0",
    "shoe_r_club_tint_1", "shoe_r_club_tint_2", "shoes_intl_mesh", "shoes_intl_texture", "shoe_l_intl_tint_0",
    "shoe_l_intl_tint_1", "shoe_l_intl_tint_2", "shoe_r_intl_tint_0", "shoe_r_intl_tint_1", "shoe_r_intl_tint_2",
    "head_gear_club_style", "head_gear_club_mesh", "head_gear_club_texture", "head_gear_club_tint_0",
    "head_gear_club_tint_1", "head_gear_club_tint_2", "head_gear_intl_style", "head_gear_intl_mesh",
    "head_gear_intl_texture", "head_gear_intl_tint_0", "head_gear_intl_tint_1", "head_gear_intl_tint_2",
    "tattoo_shoulder_l_texture", "tattoo_shoulder_r_texture", "tattoo_forearm_l_texture", "tattoo_forearm_r_texture",
    "tattoo_hand_l_texture", "tattoo_hand_r_texture", "tattoo_leg_l_texture", "tattoo_leg_r_texture",
    "sleeves_tint", "bandage", "gender"
]
LINEUP_CSV_HEADERS = ["Team_ID", "Team_Name"] + [f"{i+1}_player_ID" for i in range(40)]
men_player_id_counter = 1030
women_player_id_counter = 5501

# --- Core Functions ---
def load_names_from_csv(filename="Players_With_Codes.csv"):
    names_by_nation, all_first_names, all_last_names_with_eth = {}, [], []
    try:
        with open(filename, mode='r', encoding='utf-8') as f:
            r = csv.DictReader(f)
            for row in r:
                fn, ln, code = row.get('FirstName'), row.get('LastName'), row.get('CountryCode')
                eths = [int(e) for e in [row.get('ethnicity1'), row.get('ethnicity2'), row.get('ethnicity3')] if e] or [11]
                if fn: all_first_names.append(fn)
                if ln: all_last_names_with_eth.append({'name': ln, 'ethnicities': eths})
                if code:
                    if code not in names_by_nation: names_by_nation[code] = {'first': [], 'last': []}
                    if fn: names_by_nation[code]['first'].append(fn)
                    if ln: names_by_nation[code]['last'].append({'name': ln, 'ethnicities': eths})
        print(f"Successfully loaded {len(all_first_names)} names from {filename}.")
        return names_by_nation, all_first_names, all_last_names_with_eth
    except FileNotFoundError: return {}, ["John"], [{'name': "Doe", 'ethnicities': [11]}]

def get_player_name(nation_id, gender_id, names_by_nation, all_first_names, all_last_names_with_eth):
    nation_id_str = str(nation_id)
    if gender_id == 1: fn = ""
    else:
        wildcard_f = random.random() < 0.20
        nation_fn = names_by_nation.get(nation_id_str, {}).get('first')
        fn = random.choice(all_first_names) if wildcard_f or not nation_fn else random.choice(nation_fn)
    wildcard_l = (nation_id == 1017) or (random.random() < 0.20)
    nation_ln_pool = names_by_nation.get(nation_id_str, {}).get('last')
    ln_obj = random.choice(all_last_names_with_eth) if wildcard_l or not nation_ln_pool else random.choice(nation_ln_pool)
    if random.random() < 0.25: eth = random.randint(1, 12)
    else: eth = random.choice(ln_obj['ethnicities'])
    return fn, ln_obj['name'], eth

def map_position_to_archetype(position_name):
    for archetype, positions in COMPLEMENTARY_POS_MAP.items():
        if position_name in positions: return archetype
    return 'Forward'

def get_complementary_position_ids(primary_position_name, archetype):
    options = COMPLEMENTARY_POS_MAP[archetype].copy()
    if primary_position_name in options: options.remove(primary_position_name)
    if len(options) < 2:
        options.extend(COMPLEMENTARY_POS_MAP['Forward' if archetype != 'Forward' else 'Outside Back'])
    p2, p3 = random.sample(options, 2)
    return POSITION_MAP.get(p2, 1048576), POSITION_MAP.get(p3, 1048576)

def generate_player_stats(player_rating, archetype, pos, pos2, pos3, fn, ln, nation, gender, eth):
    global men_player_id_counter, women_player_id_counter
    if gender == 0: player_id = men_player_id_counter; men_player_id_counter += 1
    else: player_id = women_player_id_counter; women_player_id_counter += 1
    mods = {'Forward': {'spd': 0.85, 'str': 1.20, 'agl': 0.85, 'tkl': 1.15, 'pss': 0.80, 'kck': 0.50},'Outside Back': {'spd': 1.15, 'str': 0.90, 'agl': 1.10, 'tkl': 0.90, 'pss': 0.90, 'kck': 0.75},'Halves': {'spd': 1.00, 'str': 0.85, 'agl': 1.05, 'tkl': 0.80, 'pss': 1.20, 'kck': 1.25},'Hooker': {'spd': 0.95, 'str': 0.95, 'agl': 1.00, 'tkl': 1.05, 'pss': 1.10, 'kck': 0.65}}.get(archetype, {})
    def create_stat(base, mod=1.0, min_v=2000, var=0.07):
        b_stat = (min_v + ((base - 5000) / 5000) * (10000 - min_v)) * mod
        return int(max(min_v, min(10000, round(b_stat + (b_stat * var * (random.random() * 2 - 1))))))
    data = {"id": player_id, "first_name": fn, "last_name": ln, "commentary_name": ln, "nation": nation, "gender": gender, "ethnicity": eth, "preferred_foot": random.randint(0,1), "position_category1_id": pos, "position_category2_id": pos2, "position_category3_id": pos3, "position_category1_r7_id": pos, "position_category2_r7_id": pos2, "position_category3_r7_id": pos3, "fitness": create_stat(player_rating), "agility": create_stat(player_rating, mods['agl']), "speed": create_stat(player_rating, mods['spd']), "acceleration": create_stat(player_rating, mods['spd']), "aggressiveness": create_stat(player_rating, mods['str']), "tackle_ability": create_stat(player_rating, mods['tkl']), "break_tackle_ability": create_stat(player_rating, mods['str']), "pass_accuracy": create_stat(player_rating, mods['pss']), "offload_ability": create_stat(player_rating, mods['pss'] * 0.8), "general_kick_accuracy": create_stat(player_rating, mods['kck']), "goal_kick_accuracy": create_stat(player_rating, mods['kck'] * 0.8, min_v=1000), "catch_ability": create_stat(player_rating), "strength": create_stat(player_rating, mods['str']), "mental_agility": create_stat(player_rating), "jump_ability": create_stat(player_rating, mods['agl']), "discipline": create_stat(player_rating), "influence": create_stat(player_rating)}
    dob = date(1992, 1, 1) + timedelta(days=random.randrange(6209)); data["dob"] = dob.strftime("%Y-%m-%d") + " 00:00:00"; data["caps"] = random.randint(0, 250) if dob.year < 2004 else 0
    norm_s, norm_j = max(0, min(1, (data['strength'] - 2000) / 8000)), max(0, min(1, (data['jump_ability'] - 2000) / 8000))
    if gender == 0: h_base, h_r = (184, 12) if archetype == 'Forward' else (178, 14); w_base, w_r = (103, 18) if archetype == 'Forward' else (85, 16)
    else: h_base, h_r = (170, 10) if archetype == 'Forward' else (166, 12); w_base, w_r = (78, 15) if archetype == 'Forward' else (66, 14)
    data['height'] = int(h_base + (h_r * norm_j * 0.6) + (h_r * norm_s * 0.4) + random.randint(-2, 2)); data['weight'] = int(w_base + (w_r * norm_s) + random.randint(-3, 3))
    if archetype in ['Outside Back', 'Halves', 'Hooker']: data['star_factor'] = int(sum([data[s] for s in ['fitness', 'speed', 'acceleration', 'agility', 'break_tackle_ability', 'tackle_ability', 'pass_accuracy', 'general_kick_accuracy', 'goal_kick_accuracy', 'catch_ability']]) / 10)
    else: data['star_factor'] = int(sum([data[s] for s in ['fitness', 'speed', 'acceleration', 'aggressiveness', 'agility', 'break_tackle_ability', 'tackle_ability', 'pass_accuracy', 'offload_ability', 'catch_ability']]) / 10)
    for h in PLAYER_CSV_HEADERS:
        if h not in data: data[h] = 0
    return data

def save_to_csv(data, filename, headers):
    exists = os.path.isfile(filename)
    with open(filename, mode='a', newline='', encoding='utf-8') as f:
        w = csv.DictWriter(f, fieldnames=headers)
        if not exists: w.writeheader()
        w.writerow(data)

def generate_standard_team(team_rating, team_name, squad_size, nationality_info, name_data, player_pool):
    print(f"\n--- Generating Standard Team: {team_name} ---")
    mean_pr = team_rating - 300; std_dev = 500
    ratings = [random.normalvariate(mean_pr, std_dev) for _ in range(squad_size)]; ratings.sort(reverse=True)
    if squad_size > 10:
        ratings[0] *= 1.06; ratings[1] *= 1.04
        for i in range(1, min(5, squad_size // 4)): ratings[-i] *= 0.92
    ratings = [max(5000, min(9999, r)) for r in ratings]
    num_local = int(squad_size * (nationality_info['percent_local'] / 100))
    foreign_pool = nationality_info['secondary_countries']
    nations = [nationality_info['base_country']] * num_local + [random.choice(foreign_pool if random.random() >= 0.1 else list(range(1001, 1024))) for _ in range(squad_size - num_local)]
    random.shuffle(nations)
    gender = 0 if nationality_info['sex'].upper() == 'M' else 1
    player_fn = "players_men.csv" if gender == 0 else "players_women.csv"
    core_pos = sorted(POSITION_MAP.keys(), key=lambda k: POSITION_MAP[k])
    positions = core_pos + random.choices(list(POSITION_MAP.keys()), k=squad_size - len(core_pos))
    
    generated_player_ids = []
    for i in range(squad_size):
        rating, pos_name, nation = ratings[i], positions[i], nations[i]
        archetype = map_position_to_archetype(pos_name)
        pos1, pos2, pos3 = POSITION_MAP.get(pos_name, 1), *get_complementary_position_ids(pos_name, archetype)
        fn, ln, eth = get_player_name(nation, gender, *name_data)
        player_data = generate_player_stats(rating, archetype, pos1, pos2, pos3, fn, ln, nation, gender, eth)
        player_data['club_team_name'] = team_name
        player_pool.append(player_data)
        generated_player_ids.append(player_data['id'])
        
        # MODIFIED: Create a copy to save, without the extra 'club_team_name' key
        save_data = player_data.copy()
        del save_data['club_team_name']
        save_to_csv(save_data, player_fn, PLAYER_CSV_HEADERS)
        
    print(f"--- Successfully generated {squad_size} players for {team_name} ---")
    return generated_player_ids

def _select_or_generate_player(position_name, available_players, nationality_info, team_rating, player_pool, name_data):
    position_id = POSITION_MAP[position_name]
    best_player = next((p for p in available_players if int(p['position_category1_id']) == position_id), None)
    gender = 0 if nationality_info['sex'].upper() == 'M' else 1
    if best_player:
        available_players.remove(best_player)
        print(f"  Selected: {best_player['first_name']} {best_player['last_name']:<10} for {position_name}")
        return best_player
    else:
        print(f"  No player for {position_name}. Generating a new one...")
        rating = team_rating + random.randint(-100, 100)
        archetype = map_position_to_archetype(position_name)
        pos2, pos3 = get_complementary_position_ids(position_name, archetype)
        fn, ln, eth = get_player_name(nationality_info['base_country'], gender, *name_data)
        new_player = generate_player_stats(rating, archetype, position_id, pos2, pos3, fn, ln, nationality_info['base_country'], gender, eth)
        new_player['club_team_name'] = 'GeneratedForRepTeam'
        
        # MODIFIED: Create a copy to save, without the extra 'club_team_name' key
        save_data = new_player.copy()
        del save_data['club_team_name']
        save_to_csv(save_data, "players_men.csv" if gender == 0 else "players_women.csv", PLAYER_CSV_HEADERS)

        player_pool.append(new_player)
        print(f"  Generated: {fn} {ln:<10} for {position_name}")
        return new_player

def generate_state_of_origin_team(team_id, team_rating, team_name, squad_size, state, player_pool, name_data, already_selected_ids):
    print(f"\n--- Selecting State of Origin Team: {team_name} (State: {state}) ---")
    lineup = []
    eligible_clubs = NSW_TEAMS if state == "NSW" else QLD_TEAMS
    available_players = [p for p in player_pool if p.get('club_team_name') in eligible_clubs and int(p['id']) not in already_selected_ids]
    available_players.sort(key=lambda x: int(x['star_factor']), reverse=True)
    
    core_pos = sorted(POSITION_MAP.keys(), key=lambda k: POSITION_MAP[k])
    reserve_template = ['Hooker', 'Tighthead Prop', 'Lock', 'Five-Eighth', 'Loosehead Prop', 'Outside Centre']
    positions_to_fill = core_pos + random.choices(reserve_template, k=squad_size - len(core_pos))
    
    for pos_name in positions_to_fill:
        if len(lineup) >= squad_size: break
        player = _select_or_generate_player(pos_name, available_players, {'base_country': 1002, 'sex': 'M'}, team_rating, player_pool, name_data)
        if player: lineup.append(player)
            
    lineup_row = {"Team_ID": team_id, "Team_Name": team_name}
    for i, p in enumerate(lineup):
        lineup_row[f"{i+1}_player_ID"] = p['id']
        already_selected_ids.add(int(p['id']))
    return lineup_row

def generate_combination_team(team_id, team_rating, team_name, squad_size, nationality_info, player_pool, name_data):
    print(f"\n--- Selecting Combination Team: {team_name} ---")
    gender = 0 if nationality_info['sex'].upper() == 'M' else 1
    available_players = [p for p in player_pool if int(p['nation']) == nationality_info['base_country'] and int(p['gender']) == gender]
    available_players.sort(key=lambda x: int(x['star_factor']), reverse=True)
    lineup, core_pos = [], sorted(POSITION_MAP.keys(), key=lambda k: POSITION_MAP[k])
    reserve_template = ['Hooker', 'Tighthead Prop', 'Lock', 'Five-Eighth', 'Loosehead Prop', 'Outside Centre', 'Fullback', 'Left Second Row', 'Right Winger', 'Halfback', 'Left Winger']
    reserve_template.extend(reserve_template * (squad_size // len(reserve_template)))
    positions_to_fill = core_pos + reserve_template
    for i in range(squad_size):
        pos_name = positions_to_fill[i]
        player = _select_or_generate_player(pos_name, available_players, nationality_info, team_rating, player_pool, name_data)
        if player: lineup.append(player)
    lineup_row = {"Team_ID": team_id, "Team_Name": team_name}
    for i, p in enumerate(lineup):
        lineup_row[f"{i+1}_player_ID"] = p['id']
    return lineup_row

def create_example_files():
    if not os.path.exists("TeamRanks.csv"):
        print("Creating example TeamRanks.csv file...")
        with open("TeamRanks.csv", "w", newline="") as f:
            w = csv.writer(f); w.writerow(["ID", "Team_Name", "Team_Rank", "Number_of_Players", "Base_Country", "Sex", "Percent_Local", "country_2", "country_3", "country_4", "country_5", "country_6", "Combination_Team"])
            w.writerow(["2001", "Penrith Panthers", "9200", "38", "1002", "M", "80", "1001", "1005", "1022", "1008", "1012", "No"])
            w.writerow(["1013", "Australia Men", "9900", "30", "1002", "M", "100", "", "", "", "", "", "Yes"])
    if not os.path.exists("Players_With_Codes.csv"):
        print("Creating example Players_With_Codes.csv file...")
        with open("Players_With_Codes.csv", "w", newline="", encoding='utf-8') as f:
            w = csv.writer(f); w.writerow(["FirstName","LastName","Country","CountryCode","ethnicity1","ethnicity2","ethnicity3"])
            w.writerow(["Alejandro","Abadie","Argentina","1007","1","5","2"]); w.writerow(["James","Smith","Australia","1002","1","2",""]); w.writerow(["Tevita","Li","Tonga","1017","11","",""])

def process_teams_from_csv(ranks_filename, name_data):
    try:
        with open(ranks_filename, mode='r', encoding='utf-8') as f:
            teams = list(csv.DictReader(f))
        
        player_pool = []
        all_lineups = []
        
        # Process teams in their original order from the CSV
        for team_row in teams:
            try:
                team_id, team_name, team_rank, squad_size = team_row['ID'], team_row['Team_Name'], int(team_row['Team_Rank']), int(team_row['Number_of_Players'])
                nat_info = {'base_country': int(team_row['Base_Country']), 'sex': team_row['Sex'], 'percent_local': int(team_row.get('Percent_Local', 100))}
                nat_info['secondary_countries'] = [int(c) for c in [team_row.get(f'country_{i}') for i in range(2,7)] if c] or [1001]

                if team_row.get('Combination_Team', 'No').lower() == 'no':
                    player_ids = generate_standard_team(team_rank, team_name, squad_size, nat_info, name_data, player_pool)
                    lineup_row = {"Team_ID": team_id, "Team_Name": team_name}
                    for i, p_id in enumerate(player_ids):
                        lineup_row[f"{i+1}_player_ID"] = p_id
                    all_lineups.append(lineup_row)
            except (KeyError, ValueError) as e: print(f"Skipping invalid standard team row: {team_row}. Error: {e}")

        # Now that the pool is built, process combination teams
        origin_selected_ids = set()
        for team_row in teams:
            try:
                if team_row.get('Combination_Team', 'No').lower() == 'yes':
                    team_id, team_name, team_rank, squad_size = team_row['ID'], team_row['Team_Name'], int(team_row['Team_Rank']), int(team_row['Number_of_Players'])
                    nat_info = {'base_country': int(team_row['Base_Country']), 'sex': team_row['Sex']}
                    
                    if team_id in ['1034', '1035']:
                        state = "NSW" if team_id == '1034' else "QLD"
                        lineup_row = generate_state_of_origin_team(team_id, team_rank, team_name, squad_size, state, player_pool, name_data, origin_selected_ids)
                    else:
                        lineup_row = generate_combination_team(team_id, team_rank, team_name, squad_size, nat_info, player_pool, name_data)
                    all_lineups.append(lineup_row)
            except (KeyError, ValueError) as e: print(f"Skipping invalid combination team row: {team_row}. Error: {e}")

        all_lineups.sort(key=lambda x: int(x['Team_ID']))
        with open("lineups.csv", "w", newline="", encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=LINEUP_CSV_HEADERS)
            writer.writeheader()
            writer.writerows(all_lineups)
        print("\n--- All teams processed and lineups.csv created. ---")

    except FileNotFoundError: print(f"Error: The file '{ranks_filename}' was not found.")
    except Exception as e: print(f"A critical error occurred: {e}")

if __name__ == "__main__":
    create_example_files()
    for f in ["players_men.csv", "players_women.csv", "lineups.csv"]:
        if os.path.exists(f): os.remove(f)
    
    name_data = load_names_from_csv()
    process_teams_from_csv("TeamRanks.csv", name_data)
