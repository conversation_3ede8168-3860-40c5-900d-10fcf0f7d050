/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef RU_STATISTICS_SYSTEM_H
#define RU_STATISTICS_SYSTEM_H

/**
* RUStatisticsSystem.h
*
* <AUTHOR>
*/

#include "Databases/SqliteMabObject.h"
#include "Match/RugbyUnion/Enums/RUContextEnums.h"
#include "Match/RugbyUnion/Enums/RUKickTypeEnum.h"
#include "Match/RugbyUnion/Enums/RUPassTypeEnum.h"
#include "Match/RugbyUnion/Enums/RUTackleEnum.h"
#include "Match/RugbyUnion/Enums/SSTeamSideEnum.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Statistics/RUStatsConstants.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RUDatabaseCaches.h"
#include "Match/SSGameTimer.h"

class SIFGameWorld;
class RUTackleResult;
class ARugbyCharacter;
class RUGameDatabaseManager;
class SqliteMabDatabase;
class SqliteMabStatement;
class RUTeam;
class SSStreamPacker;

struct RUDB_TEAM;
struct RUDB_COMP_INST_MATCH;
struct RUDB_COMP_INST;
class  RL3DB_TEAM;
class  RL3DB_PLAYER;

//#define NUM_STAT_PLAYERS NUM_PLAYERS_PER_TEAM_INC_BENCH

enum PERFORMANCE_INDICATOR
{
	TRIES_SCORED,
	RUNNING_METERS,
	KICKING_METERS,
	TACKLES_SUCCESSFUL,
	LINE_BREAKS,
	OFFLOADS_SUCCESSFUL,
	DROP_GOALS_SUCCESSFUL,
	CONVERSIONS_SUCCESSFUL,
	HANDLING_ERRORS,
	PENALTIES_CONCEDED,
	TACKLES_FAILED,
	YELLOW_CARDS,
	RED_CARDS
};

enum RU_STAT_TIME
{
	RU_STAT_PRE_MATCH = -1,
	RU_STAT_FIRST_HALF,
	RU_STAT_SECND_HALF,
	RU_STAT_FULL_TIME,
};

/// used for matching a stat member variable offset to a string table lookup
struct RUStatDescriptor
{
	size_t offset;
	const char* ui_name;
	const char* format;
};

/// Can be used to store individual player statistics or the sum of player statistics for a team
struct RUDB_STATS_PLAYER
{
public:
	/// Constructor initialises default values
	RUDB_STATS_PLAYER();

	/// Reset to default values
	/// @param soft_reset If true then don't reset the database id fields (match_id, team_id, player_id)
	void Reset(bool soft_reset);

	// Save state / Load state
	void Pack(SSStreamPacker& stream);
	void Unpack(SSStreamPacker& stream);

public:
	unsigned short competition_id;	//< database id of the RUDB_COMP_INST this stat refers to
	unsigned short match_id;		//< database id of the RUDB_COMP_INST_MATCH this stat refers to or 0 if not applicable
	unsigned short team_id;			//< database id of the RUDB_TEAM this stat refers to or 0 if not applicable
	unsigned short player_id;		//< database id of the RUDB_PLAYER this stat refers to or 0 if not applicable

	int		conversion_attempts;
	int		field_goal_attempts;
	int		penalty_goal_attempts;
	int		handling_errors;
	int		hitups;
	int		high_tackles;
	int		injuries;
	int		kicks;
	int		line_breaks;
	int		offloads;
	int		offloads_attempted;
	int		penalties_against;
	int		points_scored;
	int		red_cards;
	int		successful_conversion_attempts;
	int		successful_field_goals;
	int		successful_penalty_goals;
	int		successful_tackles;
	int		successful_try_attempts; // This seems to be some legacy code. tries_scored is used instead - Dewald WW
	int		tackle_attempts;
	int		tries_scored;
	int		try_attempts;
	int		yellow_cards;
	float	kicking_meters_gained;
	float	running_meters_gained;

	int		ruck_entry;
	int		contest_win;
	int		lineout_steal_attempts;
	int		successful_lineout_steal;

	static const RUStatDescriptor STAT_DESCRIPTORS[];
	static const size_t NUM_STAT_DESCRIPTORS;
};


/// Used for storing stats for a specific team
struct RUDB_STATS_TEAM
{
public:
	/// Constructor initialises default values
	RUDB_STATS_TEAM();

	/// Reset to default values
	/// @param soft_reset If true then don't reset the database id fields (match_id, team_id, player_id)
	void Reset(bool soft_reset);

	// Save state / Load state
	void Pack(SSStreamPacker& stream);
	void Unpack(SSStreamPacker& stream);

public:
	unsigned short competition_id;	//< database id of the RUDB_COMP_INST this stat refers to
	unsigned short match_id;		//< database id of the RUDB_COMP_INST_MATCH this stat refers to or 0 if not applicable
	unsigned short team_id;			//< database id of the RUDB_TEAM this stat refers to or 0 if not applicable

	int				score;
	int				opponent_score;
	int				scrums;
	int				num_five_plus_phases;
	float			possession;
	float			territory;

	int				tries_conceded;
	int				tries_scored;
	int				penalties_conceded;
	int				penalties_awarded;

	static const RUStatDescriptor STAT_DESCRIPTORS[];
	static const size_t NUM_STAT_DESCRIPTORS;
};

class RUStatisticsSystem
{
public:
	// References to all try-scorer variable enums...
	static const int TRY_SCORER_ENUM_LIST[];
	static const size_t NUM_TRY_SCORERS_RECORDED;

	RUStatisticsSystem(RUGameDatabaseManager& database_manager);
	~RUStatisticsSystem();

	// Save state / Load state
	void PackCurrentGame(SSStreamPacker& stream);
	void UnpackCurrentGame(SSStreamPacker& stream);

	/// Called when a player detaches/attaches monitors for the current game

	void NewGame(const RUDB_TEAM& team_a, const RUDB_TEAM& team_b,  unsigned short competition_id, unsigned short match_id);

	void NewGame(RL3DB_TEAM team_a, RL3DB_TEAM team_b, unsigned short competition_id, unsigned short match_id);

	/// Writes the current game stats to the database if appropriate and resets current match stats data structures
	void EndCurrentGame();

	/// Resets all the current game stats for when the player opts to restart a match
	void ResetCurrentMatchStats() { ResetCurrentMatchStats(true); }

	//@{ Current match team stats, including sum of player stats for all players on team

	/// This returns the value of an individual stat value.
	/// It is useful when you want the sum of all player stats and they need to be added up
	template <typename T>
	T GetCurrentMatchStat(const RUTeam* team, T RUDB_STATS_TEAM::*member) const;

	template <typename T>
	T GetCurrentMatchStat(SSTEAMSIDE team_side, T RUDB_STATS_TEAM::*member) const;

	template <typename T>
	T GetCurrentMatchStat(const RUTeam* team, T RUDB_STATS_PLAYER::*member) const;

	template <typename T>
	T GetCurrentMatchStat(SSTEAMSIDE team_side, T RUDB_STATS_PLAYER::*member) const;

	//@}

	//@{ Current match player stats
	template <typename T>
	T GetCurrentMatchStat(const ARugbyCharacter* player, T RUDB_STATS_PLAYER::*member) const;

	template <typename T>
	T GetCurrentMatchStat(SSTEAMSIDE team_side, unsigned short player_id, T RUDB_STATS_PLAYER::*member) const;
	//@}

	/// Helper method for finding the string table name and printf format for the stat
	template <typename C, typename T>
	static bool FindStatNameAndFormat(T C::*member, const char** name, const char** format);

	/// Attaches all the game listeners and keeps a pointer to the current game - calls NewGame()
	void AttachMonitors(SIFGameWorld* game_world);

	/// dettaches all the game listeners and sets the game* to null - calls EndCurrentGame()
	void DetachMonitors();

	// Raw stats methods - in game should use the event interface, these are used by match simulation
	void AddConversion(SSTEAMSIDE team_side, SSTEAMSIDE opposing_team_side, unsigned short player_id, bool success);
	void AddPenaltyGoal(SSTEAMSIDE team_side, SSTEAMSIDE opposing_team_side, unsigned short player_id, bool success);
	void AddDropGoal(SSTEAMSIDE team_side, SSTEAMSIDE opposing_team_side, unsigned short player_id, bool success);
	void AddTry(SSTEAMSIDE team_side, SSTEAMSIDE opposing_team_side, unsigned short player_id, bool success);
	void AddScrum(SSTEAMSIDE team_side);
	void AddPenalty(SSTEAMSIDE team_side, unsigned short player_id);
	void AddHitup(SSTEAMSIDE team_side, unsigned short player_id);
	void AddLineBreak(SSTEAMSIDE team_side, unsigned short player_id);
	void AddPass(SSTEAMSIDE team_side, unsigned short passing_player_id, bool success, bool is_offload);
	void AddHandlingError(SSTEAMSIDE team_side, unsigned short player_id);
	void AddForwardPassHandlingError(SSTEAMSIDE team_side1, unsigned short player_id_1, SSTEAMSIDE team_side2, unsigned short player_id_2);
	void AddTackle(SSTEAMSIDE team_side, unsigned short player_id, bool success, bool high);
	void AddKick(SSTEAMSIDE team_side, unsigned short player_id);
	void AddKickingMetresGained(SSTEAMSIDE team_side, unsigned short player_id, float metres_gained);
	void AddRunningMetresGained(SSTEAMSIDE team_side, unsigned short player_id, float metres_gained);
	void AddYellowCard(SSTEAMSIDE team_side, unsigned short player_id);
	void AddRedCard(SSTEAMSIDE team_side, unsigned short player_id);
	void AddInjury(SSTEAMSIDE team_side, unsigned short player_id);
	void AddHalf(SSTEAMSIDE team_side, int num_five_plus_phases, float possession, float territory);

	void AddRuckEnter(SSTEAMSIDE team_side, unsigned short player_id);
	void AddRuckTurnOver(SSTEAMSIDE team_side, unsigned short player_id);

	void AddLineout(SSTEAMSIDE team_side, unsigned short player_id, bool stolen);

	// AJ NRL additions for Meters Gained
	// when a team gains possession to start a new set
	void StartNewSet();
	// add meters to the current set
	void AddMetersToCurrentSet(float meters);
	// Get current set meters
	float GetCurrentSetMetersGained() const;
	// Event Handlers
	void AddPossessionChangeInterface(RUTeam* new_team);
	void AddFortyTwentyKickAwardedInterface(const FVector& pos, ARugbyCharacter* kicker, const KickContext& ctx);

#if defined(ENABLE_SOAK_TEST) || defined(ENABLE_GAME_DEBUG_MENU)
	/// For soaks, force the score...
	void ForceTeamScore(SSTEAMSIDE team_side, int score);
#endif

	///----------------------------------
	/// RC2: From RL3...
	///----------------------------------

	/// Works out each player's performance and ranks the top three.
	void CalculatePlayerPerformances();

	float CalculateValueOf( PERFORMANCE_INDICATOR kpi, RUDB_STATS_PLAYER* player_stat, rudb_player_performance_values_row* row_data );

	/// \brief Returns the requested player from the top three performing players in the match.
	///
	/// @param index [in] An index into the top three, can be 0, 1 or 2.
	/// @return The index of the player who is in the specified spot. This
	/// is a standard player_index, so ranges from 0-33. (0-16 is home side, 16-33
	/// is away side).
	int	 GetTopPerformingPlayerIndex(int index);
	unsigned short	 GetTopPerformingPlayerDBID(int index);

	float	 GetPerformanceForPlayerIDInTeamID(unsigned short team_id, unsigned short player_id);

	void AddCompetitionStats(unsigned short comp_instance_db_id, bool is_preliminary_round);

	/// Save off the current match's statistics to the rudb_comp_inst_player_match_stats structure.
	void SavePlayerMatchStatistics();

	SSTEAMSIDE GetCurrentMatchTeamSide(unsigned short team_id) const;
	const RUDB_STATS_PLAYER* GetCurrentMatchPlayerStats(unsigned short team_id, unsigned short player_id) const;

private:

	/// Resets all the current game stats for when the player opts to restart a match
	/// @param soft_reset If true retains the database ids and match half values on the stat record
	void ResetCurrentMatchStats(bool soft_reset);

	/// Helper for finding indices of players and teams in the current game
	bool GetCurrentMatchTeamIndex(SSTEAMSIDE team_side, size_t& team_index) const;
	bool GetCurrentMatchPlayerIndex(SSTEAMSIDE team_side, unsigned short player_id, size_t& team_index, size_t& player_index) const;

	RUDB_STATS_PLAYER* GetCurrentMatchPlayerStats(SSTEAMSIDE team_side, unsigned short player_id);
	const RUDB_STATS_PLAYER* GetCurrentMatchPlayerStats(SSTEAMSIDE team_side, unsigned short player_id) const;

	RUDB_STATS_TEAM* GetCurrentMatchTeamStats(SSTEAMSIDE team_side);
	const RUDB_STATS_TEAM* GetCurrentMatchTeamStats(SSTEAMSIDE team_side) const;

	// helper function for finding ID's
	void GetPlayerId( const ARugbyCharacter* player, SSTEAMSIDE *team_side, unsigned short& player_id ) const;
//	void GetTeamId( const RUTeam* team, unsigned short& team_id ) const;

	/// event listeners
	void AddConversionInterface( bool success );
	void AddPenaltyGoalInterface( bool success, const FVector &crossed_goal_position );
	void AddHandlingErrorInterface( ARugbyCharacter* player, bool from_tackle, const FVector& position );
	void AddForwardPassHandlingErrorInterface( ARugbyCharacter* player1, ARugbyCharacter* player2 );
	void AddHitupInterface( ARugbyCharacter* player );
	void AddInjuryInterface( ARugbyCharacter* player, TACKLE_INJURY_TYPE injury_type );
	void AddRunningMetresGainedInterface( ARugbyCharacter* player, float metres_gained );
	void AddKickingMetresGainedInterface( ARugbyCharacter* player, float metres_gained );
	void AddKickInterface( ARugbyCharacter* kicker, KickContext kick_context, KickType kick, const  FVector& position );
	void AddLineBreakInterface( ARugbyCharacter* player, LINE_BREAK_TYPE line_break_type );
	void AddPassInterface( ARugbyCharacter* passing_player, ARugbyCharacter* player_to_pass_to, const FVector& target_position, PASS_TYPE type, bool success );
	void AddPenaltyInterface( ARugbyCharacter* player, ARugbyCharacter* offended, const FVector& position, PENALTY_REASON );
	void AddRedCardInterface( ARugbyCharacter* player );
	void AddScrumInterface( RUTeam *team );
	void AddTackleInterface( const RUTackleResult& tackle_result );
	//void AddHighTackle( SSTEAMSIDE team_side, unsigned short player_db_id );
	void AddTryInterface( bool success, bool penalty_try, ARugbyCharacter* try_scorer );
	void AddYellowCardInterface( ARugbyCharacter* player );
	void AddHalfTimeInterface();
	void AddFullTimeInterface();
	void AddDropGoalInterface(bool success, const FVector& position);

	void AddRuckEnterInterface( ARugbyCharacter*, const FVector&, RUZoneJoinType );
	void AddRuckTurnOverInterface( ARugbyCharacter*, ARugbyCharacter* );
	void AddLineoutInterface( ARugbyCharacter* player, bool wasStolen, bool isCatcher );

	// event helpers
	void AddScore(RUDB_STATS_PLAYER& player_stat, SSTEAMSIDE team_side, SSTEAMSIDE opposing_team_side, int points);
	void AddHalf(); //< used by AddHalfTimeInterface and AddFullTimeInterface

	void AddTeamStats(RL3DB_TEAM team, const RUDB_STATS_TEAM &team_match_stats, unsigned short comp_instance_db_id, int satellite_index, bool is_preliminary_round);
	void AddTeamStats(RL3DB_TEAM team, const RUDB_STATS_PLAYER &player_stats, unsigned short comp_instance_db_id, int satellite_index);
	void AddPlayerStats(RL3DB_PLAYER player, const RUDB_STATS_PLAYER &player_match_stats, unsigned short comp_instance_db_id);

private:

	/// Lock to ensure this class is thread safe
	mutable MabCriticalSection stats_lock;

	/// The current game world, if we are in game
	SIFGameWorld* current_world;

	/// Current competition id of the current match (SQLITEMAB_INVALID_ID if not applicable)
	unsigned short current_competition_id;

	/// Current match id of the current match (SQLITEMAB_INVALID_ID if not applicable)
	unsigned short current_match_id;

	/// The teams participating in the current match
	MabArray< unsigned short, 2 > current_team_ids;

	MabArray< SSTEAMSIDE, 2 > current_teams_sides;

	/// The players participating in the current match
	MabArray< MabVector<unsigned short>, 2> current_player_ids;

	/// The half being played in the current match
	char current_match_half;

	/// Stores the team statistics for each half of the current match
	MabArray<RUDB_STATS_TEAM, 2> current_match_team_stats;

	/// Stores the player statistics for each half of the current match
	MabArray< MabVector< RUDB_STATS_PLAYER >, 2> current_match_player_stats;

	RUGameDatabaseManager* game_db_manager;

	// AJ NRL additions
	float current_set_meters_gained;
	SSTEAMSIDE current_team_in_possession;

	///--------------------------------
	/// RC2... FROM RL3...
	///--------------------------------

	float performances[2][NUM_PLAYERS_PER_TEAM_INC_BENCH_INIT];	/// How well each player performed in the game.
	int top_players_index[3];		//// The top three players in the match
	int top_players_team_index[3];		//// The top three players in the match
	unsigned short top_players_db_id[3];		//// The top three players in the match
	float top_players_score[3];		//// The top three players in the match
};

/// this is just to hide some helper functions
namespace _RUStatisticsSystem
{
	const RUStatDescriptor* FindStatDescriptor(size_t offset, const RUStatDescriptor* descriptor_list, size_t num_descriptors);

	template <typename C, typename T>
	inline size_t GetStatOffset(T C::*member)
	{
		T* ptr = &(((C*)NULL)->*member);
		return reinterpret_cast<size_t>(ptr);
	}

	template <typename C, typename T>
	inline const RUStatDescriptor* FindStatDescriptor(T C::*member)
	{
		return FindStatDescriptor(GetStatOffset(member), C::STAT_DESCRIPTORS, C::NUM_STAT_DESCRIPTORS);
	}

}

template <typename T>
T RUStatisticsSystem::GetCurrentMatchStat(SSTEAMSIDE team_side, T RUDB_STATS_TEAM::*member) const
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	if (const RUDB_STATS_TEAM* team_stat = GetCurrentMatchTeamStats(team_side))
	{
		T value = ((RUDB_STATS_TEAM*)team_stat)->*member;
		return value;
	}
	else
	{
		MABBREAKMSG("No RU_STATS_TEAM record found in current match for team");
		return T(0);
	}
}

template <typename T>
T RUStatisticsSystem::GetCurrentMatchStat(const RUTeam* team, T RUDB_STATS_TEAM::*member) const
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	return GetCurrentMatchStat(team->GetSide(), member);
}

template <typename T>
T RUStatisticsSystem::GetCurrentMatchStat(SSTEAMSIDE team_side, T RUDB_STATS_PLAYER::*member) const
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	size_t team_index;
	T result(0);

	if (!GetCurrentMatchTeamIndex(team_side, team_index))
	{
		MABBREAKMSG("No RU_STATS_PLAYER records found in current match for team");
		return result;
	}

	// loop through all player stats to create a total for the team of this stat
	for (size_t player_index=0, num_players=current_match_player_stats[team_index].size(); player_index < num_players; ++player_index)
	{
		const RUDB_STATS_PLAYER* player_stat = &current_match_player_stats[team_index][player_index];
		T value = ((RUDB_STATS_PLAYER*)player_stat)->*member;
		result += value;
	}

	return result;
}

template <typename T>
T RUStatisticsSystem::GetCurrentMatchStat(const RUTeam* team, T RUDB_STATS_PLAYER::*member) const
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	return GetCurrentMatchStat(team->GetSide(), member);
}

template <typename T>
T RUStatisticsSystem::GetCurrentMatchStat(const ARugbyCharacter* player, T RUDB_STATS_PLAYER::*member) const
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	SSTEAMSIDE team;
	unsigned short player_id;
	GetPlayerId(player, &team, player_id);
	return GetCurrentMatchStat(team, player_id, member);
}

template <typename T>
T RUStatisticsSystem::GetCurrentMatchStat(SSTEAMSIDE team_side, unsigned short player_id, T RUDB_STATS_PLAYER::*member) const
{
	MabScopedLock<MabCriticalSection> thread_lock( stats_lock );

	if (const RUDB_STATS_PLAYER* player_stat = GetCurrentMatchPlayerStats(team_side, player_id))
	{
		T value = ((RUDB_STATS_PLAYER*)player_stat)->*member;
		return value;
	}
	else
	{
		//MABBREAKMSG("No RU_STATS_PLAYER record found in current match for team and player");
		return T(0);
	}
}

/// Helper method for finding the string table name and printf format for the stat
template <typename C, typename T>
bool RUStatisticsSystem::FindStatNameAndFormat(T C::*member, const char** name, const char** format)
{
	if (const RUStatDescriptor* stat_descriptor = _RUStatisticsSystem::FindStatDescriptor(member))
	{
		*name = stat_descriptor->ui_name;
		*format = stat_descriptor->format;
		return true;
	}
	return false;
}

#endif //RU_STATISTICS_SYSTEM_H
