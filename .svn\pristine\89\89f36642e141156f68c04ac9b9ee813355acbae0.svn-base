/*--------------------------------------------------------------
|        Copyright (C) 1997-2008 by Prodigy Design Ltd         |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#include "RUCommentary.h"

//#rc3_legacy_pch #include <Precompiled.h>

// Mab headers.
#include "Mab/Lua/MabLuaInterpreter.h"
#include "Mab/Central/OLD/MabCentralAccessor.h"

// SIF headers.
#include "Match/Audio/SIFFMODUtil.h"
//#rc3_legacy_include #include "SIFFMODSoundResource.h"
#include "Match/Audio/SIFAudio.h"
#include "Match/PlayerProfile/SIFPlayerProfileManager.h"
#include "Match/PlayerProfile/SIFPlayerProfile.h"
#include "Match/PlayerProfile/SIFPlayerProfilePropertyDefs.h"
#include "Match/SIFGameWorld.h"

// SS headers
#include "Match/Ball/SSBall.h"
#include "Match/SSContextBucket.h"
#include "SSCommentaryLoad.h"
#include "SSCommentaryInserts.h"
#include "SSPhrase.h"
#include "SSPhraseSelector.h"
#include "Match/SSSpatialHelper.h"
#include "SSCommentaryClassificationAssociations.h"
#include "Match/SSGameTimerHalfObserver.h"

// RU headers
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/Statistics/RUStatisticsSystem.h"
#include "Match/RugbyUnion/Statistics/RUStatsTracker.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/ContextBucket/RUCBGameStats2Bucket.h"
#include "Match/RugbyUnion/ContextBucket/RUContextBucketBasicEvents.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/RugbyUnion/Statistics/RURecordTypeEnum.h"
#include "Match/RugbyUnion/RUEmotionEngineManager.h"
#include "Match/RugbyUnion/RUMatchIntensityManager.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "RUCommentaryDataProvider.h"
#include "RUCommentaryDataCollector.h"
#include "RUPhraseBinder.h"
#include "RUPhraseRenderer.h"
#include "RUPhraseFilterArticles.h"
#include "RUPhraseFilterPhraseRepeat.h"
#include "RUPhraseFilterPlayedCount.h"
#include "Utility/RURandomNumberGenerator.h"
#include "RUCommentaryClassificationTypes.h"
//#rc3_legacy_include #include "RUEmotionEngineDebugMenu.h"
#include "RUPMPDirector.h"
//#rc3_legacy_include #include "RUPhraseFilterExcitement.h"
#include "Match/RugbyUnion/Rules/RURuleTrigger.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "RUCommentaryCompetitionAnalysis.h"
#include "Match/RugbyUnion/RUGameState.h"

// Other
#include "time.h"
//#rc3_legacy_include #include "RUCommentaryPlayer.h"
#include "Match/RugbyUnion/RUSandboxGame.h"
#include "Databases/RUGameDatabaseManager.h"

#include "RugbyGameInstance.h"
#include "fmod_studio.hpp"
#include "fmod.hpp"

#if PLATFORM_SWITCH
#include "SwitchPathManager.h"
#endif
#include "RUCommentaryPlayer.h"
#include "Utility/Helpers/SIFGameHelpers.h"

//#include "Match/AI/Actions/RUActionTacklee.h"
/// The minimum amount of time we wait before playing a phrase, to allow us
/// to pick the right event from a compact sequence of closely placed events such as TACKLE, TRY
static const float PENDING_PHRASE_MIN_WAIT_TIME = 0.05f;// 0.5f;

static const int MAX_BIND_ATTEMPTS = 3;

const float RUCommentary::PHRASE_INTERRUPTION_DELAY = 0.05f;

const float RUCommentary::COMMENTARY_PACING_URGENCY_PERIOD = 10.0f;

static const float MAX_PAUSE_TIME = 20.0f;//3.0f * 60.0f; // In seconds

static const float NISBO_VOLUME = 1.0f;
static const float MARSHALL_VOLUME = 1.0f;

RUCommentator::RUCommentator(
	MABMEM_HEAP heap,
	const char *database_file_name,
	const char *wwavebank_resource_name,
	RUCommentaryDataProvider *data_provider,
	MabLuaInterpreter *interpreter,
	SSCommentaryClassificationAssociations *classification_associations,
	float volume,
	CommentatorType newCommType,
	bool create_debug_wavebank
	)
:
	wavebank(NULL),
	debug_wavebank(NULL),
	phrase_selector(NULL),
	inserts(NULL),
	volume(volume)
{
	//Load in the raw commentary data
	SSCommentaryLoad *commentary_load = MabMemNew(SIFHEAP_DYNAMIC) SSCommentaryLoad(database_file_name);

	//create the inserts collection.
	inserts = MabMemNew(heap) SSCommentaryInserts(heap, commentary_load->GetDatabase());
#ifndef DISABLE_AUDIO_COMMENTARY
	//create and setup the phrase selector
	phrase_selector = MabMemNew( heap ) SSPhraseSelector( heap );
	phrase_selector->Initialise( commentary_load->GetDatabase(), data_provider, interpreter, classification_associations );

	//Ditch the raw commentary data
	if (commentary_load)
	{
		MabMemDeleteSafe(commentary_load);
	}

	//create wavebank
	FMOD::Studio::System* event_system = SIFApplication::GetApplication()->GetAudio()->GetFMODInstance();
	FMOD::System* system = NULL;
	event_system->getCoreSystem( &system );
	FMOD_MODE mode = FMOD_3D | FMOD_LOWMEM | FMOD_CREATESTREAM | FMOD_NONBLOCKING;
	//#rc3_legacy #if PLATFORM_PS3 == 1
	//#rc3_legacy MabString value(0, "%s/data/" PLATFORM_NAME "/%s", SIFApplication::GetApplication()->GetApplicationParameters().ps3_app_path, wwavebank_resource_name);
	//#rc3_legacy #else
	MabString value(wwavebank_resource_name);
	MABLOGDEBUG("RUCommentator(%s)",value.c_str());
	//#rc3_legacy #endif
	SIF_FMOD_COMMAND(system->createStream(value.c_str(), mode, NULL, &wavebank));


#if defined ENABLE_SIF_DEBUG_DRAW && defined ENABLE_SUB_SOUND_LENGTH_DEBUG
	if(create_debug_wavebank)
	{
		//try to open an info-only version as well.
		FMOD::System* system = NULL;
		SIF_FMOD_COMMAND( wavebank->getSystemObject( &system ));
		MABASSERT(system);

		MabString filename = wavebank_resource->GetSoundValue();
		FMOD_MODE mode =  FMOD_3D | FMOD_SOFTWARE | FMOD_LOWMEM | FMOD_OPENONLY | FMOD_CREATESTREAM | FMOD_NONBLOCKING;
		SIF_FMOD_COMMAND( system->createStream( filename.c_str(), mode, NULL, &debug_wavebank) );
	}
#else
	MABUNUSED(create_debug_wavebank);
#endif
#else
	MABUNUSED(wwavebank_resource_name);
	MABUNUSED(data_provider);
	MABUNUSED(interpreter);
	MABUNUSED(classification_associations);
	MABUNUSED(volume);
	MABUNUSED(create_debug_wavebank);
#endif

	m_commentatorType = newCommType;

}

RUCommentator::~RUCommentator()
{
	if(wavebank) SIF_FMOD_COMMAND( wavebank->release() );
	wavebank = NULL;
	MabMemDeleteSafe(phrase_selector);
	MabMemDeleteSafe(inserts);
}


RUCommentaryBoundPhrase::RUCommentaryBoundPhrase()
:
	commentator(NULL),
	selected_phrase(),
	bound_phrase(),
	referenced_articles(NULL),
	creation_time(0),
	play_time(0)
{
	referenced_articles = MabMemNew(MMHEAP_PERMANENT_DATA) RUReferencedArticles();
}


RUCommentaryBoundPhrase::~RUCommentaryBoundPhrase()
{
	MabMemDeleteSafe(referenced_articles);
}

void RUCommentaryBoundPhrase::Reset()
{
	commentator = NULL;
	selected_phrase.Reset();
	bound_phrase.Reset();
	referenced_articles->Reset();
}

RUCommentaryBoundPhrase &RUCommentaryBoundPhrase::operator = (const RUCommentaryBoundPhrase &rhs)
{
	commentator = rhs.commentator;
	selected_phrase = rhs.selected_phrase;
	*referenced_articles = *rhs.referenced_articles;
	bound_phrase = rhs.bound_phrase;
	creation_time = rhs.creation_time;
	play_time = rhs.play_time;
	return *this;
}

RUCommentary::RUCommentary( MABMEM_HEAP _heap )
: 
	heap( _heap ),
	game( NULL ), 
	context_bucket(NULL),
	load_next_update(false),
	time_paused(0.0f),
	paused(false),
	m_isDisabled(false),
	m_isColourDisabled(false),
	loaded_language(RUCL_None),
	event_search_time(-1.0f),
	last_event_search_absolute_time(0),
	highest_record_id_encountered(-1),
	commentary_data_provider(NULL),
	commentary_interpreter(NULL),
	data_collector(NULL),
	phrase_binder(NULL),
	phrase_renderer(NULL),
	classification_associations(NULL),
	pre_mid_post_director(NULL),
	commentator_nisbo(NULL),
	commentator_marshall(NULL),
	pending_phrase(),
	current_phrase(),
	last_phrase(),
	currently_referenced_articles(NULL),
	calculated_game_data(false)
{
	//Setup the classification associations
	classification_associations = MabMemNew(heap) SSCommentaryClassificationAssociations;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_NONE,1.0f,0.0f))
		->AddSupersedence(CCT_PLAY_NOTABLE)
		->AddInvalidation(CCT_PLAY_IMPORTANT)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PLAY,1.0f,0.0f))
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddInvalidation(CCT_PLAY_IMPORTANT)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PLAY_NOTABLE,1.0f,0.0f, 1.0f))
		->AddSupersedence(CCT_PLAY_BLOCKING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PLAY_IMPORTANT,1.0f,0.0f, 2.0f))
		->InheritsFrom(CCT_PLAY_NOTABLE)
		->AddSupersedence(CCT_PENALTY)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PLAY_BLOCKING,1.0f,0.0f,0.0f))
		->AddInvalidation(CCT_PLAY_IMPORTANT)
		;
	

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PLAY_INTERRUPTING,1.0f,0.0f))
		->InheritsFrom(CCT_PLAY_IMPORTANT)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PLAY_STOPPAGE_RULE,1.0f,0.0f))
		->InheritsFrom(CCT_PLAY_BLOCKING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_AMBIENT_IN_GAME,1.0f,0.0f))
		->AddInvalidation(CCT_PLAY_IMPORTANT)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_AMBIENT_PMP,1.0f,0.0f))
		->InheritsFrom(CCT_PLAY_BLOCKING)
		;
	
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_RUNNING,1.0f,0.0f))
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		->AddSupersedence(CCT_TRY_ANTICIPATE)
		->AddSupersedence(CCT_COLOUR)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_RUNNING_SUPPORT,1.0f,3.0f))
		->InheritsFrom(CCT_RUNNING)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		->AddSupersedence(CCT_TRY_ANTICIPATE)
		->AddSupersedence(CCT_COLOUR)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_RUNNING_LOOKS_FOR_SUPPORT,1.0f,10.0f))
		->InheritsFrom(CCT_RUNNING_SUPPORT)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		->AddSupersedence(CCT_TRY_ANTICIPATE)
		->AddSupersedence(CCT_COLOUR)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_RUNNING_HAS_SUPPORT,1.0f,10.0f))
		->InheritsFrom(CCT_RUNNING_SUPPORT)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		->AddSupersedence(CCT_TRY_ANTICIPATE)
		->AddSupersedence(CCT_COLOUR)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_RUNNING_NEEDS_SUPPORT,1.0f,10.0f))
		->InheritsFrom(CCT_RUNNING_SUPPORT)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		->AddSupersedence(CCT_TRY_ANTICIPATE)
		->AddSupersedence(CCT_COLOUR)
		;


	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_TRY_ANTICIPATE,1.0f,7.0f))
		->InheritsFrom(CCT_RUNNING)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddInvalidation(CCT_END_RUN_NEGATIVE)
		->AddInvalidation(CCT_TOUCH)
		->AddInvalidation(CCT_PLAY_BLOCKING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_END_RUN,1.0f,0.0f))
		->AddSupersedence(CCT_END_RUN_NEGATIVE)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_END_RUN_NEGATIVE,1.0f,0.0f))
		->InheritsFrom(CCT_END_RUN)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_LOST_POSSESSION)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_TACKLE,1.0f,0.0f))
		->InheritsFrom(CCT_END_RUN_NEGATIVE)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddSupersedence(CCT_COLOUR)
		->AddSupersedence(CCT_PASS_NOTABLE)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_TACKLE_SIGNIFICANT,1.0f,0.0f, 1.0f))
		->InheritsFrom(CCT_PLAY_NOTABLE)
		->InheritsFrom(CCT_TACKLE)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddSupersedence(CCT_PLAY_IMPORTANT)
		;

	
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_TACKLE_INTO_TOUCH, 1.0f,0.0f, 3.0f))
		->InheritsFrom(CCT_TACKLE_SIGNIFICANT)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_TACKLE_HIGH,1.0f,0.0f, 3.0f))
		->InheritsFrom(CCT_TACKLE)
		->InheritsFrom(CCT_PLAY_INTERRUPTING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_TOUCH,1.0f,0.0f))
		->InheritsFrom(CCT_PLAY_IMPORTANT)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_LOST_POSSESSION,1.0f,0.0f, 6.0f))
		->InheritsFrom(CCT_PLAY_IMPORTANT)
		->InheritsFrom(CCT_END_RUN_NEGATIVE)
		->AddInvalidation(CCT_SCORE)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		;

	//TODO: need to differentiate this from place-kicks as they can run during a play stoppage rule.
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_KICK,1.0f,0.0f))
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_KICK_SPECIFIC)
		->AddSupersedence(CCT_PLAY_IMPORTANT)
		;


	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_KICK_GOAL_READY,1.0f,6.0f,0.3f))
		->InheritsFrom(CCT_KICK)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddInvalidation(CCT_KICK_RESULT_ANTICIPATE)
		->AddSupersedence(CCT_KICK_SPECIFIC)
		->AddSupersedence(CCT_PLAY_IMPORTANT)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_KICK_GOAL_READY_IMPORTANT,1.0f,6.0f,0.3f))
		->InheritsFrom(CCT_KICK_GOAL_READY)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddInvalidation(CCT_KICK_RESULT_ANTICIPATE)
		->AddSupersedence(CCT_KICK_SPECIFIC)
		->AddSupersedence(CCT_PLAY_IMPORTANT)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_KICK_SPECIFIC,1.0f,0.0f))
		->InheritsFrom(CCT_KICK)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddSupersedence(CCT_PLAY_IMPORTANT)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_KICK_TO_TOUCH,1.0f,0.0f, 3.0f))
		->InheritsFrom(CCT_KICK_SPECIFIC)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_IMPORTANT)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_KICK_RESULT_ANTICIPATE,1.0f,0.0f))
		->InheritsFrom(CCT_KICK)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_KICK_MISS,1.0f,0.0f,3.0f))
		->InheritsFrom(CCT_KICK)
		->AddSupersedence(CCT_KICK_MISS_EXCEPTIONAL)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_KICK_MISS_EXCEPTIONAL,1.0f,0.0f,3.0f))
		->InheritsFrom(CCT_KICK_MISS)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_TIMEKEEPING,0.5f,15.0f))
		->AddSupersedence(CCT_TIMEKEEPING_URGENT)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_COLOUR)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_TIMEKEEPING_URGENT,0.5f,15.0f))
		->InheritsFrom(CCT_TIMEKEEPING)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_COLOUR)
		->AddSupersedence(CCT_PLAY_IMPORTANT)
		;
	
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_SCORE,1.0f, 0.0f, 6.0f))
		->InheritsFrom(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_TMO_TRY_AWARDED)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_SCORE_TRY,1.0f, 0.0f, 6.0f))
		->InheritsFrom(CCT_SCORE)
		->AddSupersedence(CCT_TMO_TRY_AWARDED)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_RUCK,0.15f,7.0f))
		->AddSupersedence(CCT_PLAY_NOTABLE)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_COLOUR)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_RUCK_IMPORTANT,1.0f,0.0f))
		->InheritsFrom(CCT_RUCK)
		->InheritsFrom(CCT_PLAY_NOTABLE)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddSupersedence(CCT_PLAY_IMPORTANT)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_RUCK_RESULT_ANTICIPATE, 0.5f, 5.0f))
		->InheritsFrom(CCT_RUCK)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddSupersedence(CCT_PLAY_IMPORTANT)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PASS,1.0f,0.0f))
		->InheritsFrom(CCT_END_RUN)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		->AddSupersedence(CCT_PASS_FROM_RUCK)
		->AddSupersedence(CCT_PASS_FROM_MAUL)
		->AddSupersedence(CCT_COLOUR)
		->AddSupersedence(CCT_PASS_NOTABLE)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PASS_FROM_RUCK,1.0f,0.0f))
		->InheritsFrom(CCT_PASS)
		->InheritsFrom(CCT_RUCK_IMPORTANT)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddSupersedence(CCT_PLAY_IMPORTANT)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PASS_FROM_MAUL,1.0f,0.0f))
		->InheritsFrom(CCT_PASS)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		;


	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PASS_NOTABLE,1.0f,0.0f))
		->InheritsFrom(CCT_PLAY_NOTABLE)
		->InheritsFrom(CCT_PASS)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddSupersedence(CCT_PLAY_IMPORTANT)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PASS_LOOSE, 1.0f, 0.0f))
		->InheritsFrom(CCT_PLAY_NOTABLE)
		->InheritsFrom(CCT_PASS)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		;
	
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PASS_FORWARD,1.0f,4.0f, 2.0f))
		->InheritsFrom(CCT_PASS_NOTABLE)
		->InheritsFrom(CCT_PLAY_IMPORTANT)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		;

	
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PENALTY,1.0f,0.0f,6.0f))
		->InheritsFrom(CCT_PLAY_IMPORTANT)
		;


	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_MARSHALL_HIGH_TACKLE,1.0f,6.0f,0.2f))
		->InheritsFrom(CCT_PLAY_IMPORTANT)
		;

	
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_MAUL,1.0f,0.0f, 2.0f))
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		->AddSupersedence(CCT_COLOUR)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_MAUL_PERIODIC,0.7f,6.0f))
		->InheritsFrom(CCT_MAUL)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		->AddSupersedence(CCT_COLOUR)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_SCRUM,1.0f,0.0f))
		->AddSupersedence(CCT_PLAY_NOTABLE)
		->AddSupersedence(CCT_SCRUM_IMPORTANT)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddSupersedence(CCT_COLOUR)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_SCRUM_PERIODIC, 1.0f, 5.0f))
		->InheritsFrom(CCT_SCRUM)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		->AddSupersedence(CCT_SCRUM_IMPORTANT)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddSupersedence(CCT_COLOUR)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_SCRUM_IMPORTANT,1.0f,0.0f, 2.0f))
		->InheritsFrom(CCT_SCRUM)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_IMPORTANT)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_LINEOUT,1.0f,0.0f, 3.0f))
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_COLOUR)
		->AddSupersedence(CCT_PLAY_IMPORTANT)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		->AddSupersedence(CCT_TACKLE_INTO_TOUCH)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_COLOUR,1.0f,0.0f, 5.0f))
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_AMBIENT_PMP)
		->AddSupersedence(CCT_COLOUR_BONUS_POINTS)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_COLOUR_GAME, 0.3f, 6.0f, 8.0f))
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_COLOUR)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_COLOUR_GAME_SPORADIC,0.1f,20.0f,6.0f))
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_COLOUR)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_COLOUR_GAME_CHANCES,0.3f,20.0f,6.0f))
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->InheritsFrom(CCT_COLOUR_GAME)
		->AddSupersedence(CCT_COLOUR)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		->AddSupersedence(CCT_PLAY_BLOCKING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_COLOUR_GAME_RAIN_AFFECTING,1.0f,60.0f,6.0f))
		->InheritsFrom(CCT_COLOUR_GAME)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_COLOUR_GAME_WIND_AFFECTING,1.0f,120.0f,6.0f))
		->InheritsFrom(CCT_COLOUR_GAME)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_COLOUR)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_COLOUR_BONUS_POINTS,1.0f,0.0f,6.0f))
		->InheritsFrom(CCT_COLOUR)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_COLOUR_CURRENT_SCORE,0.5f,15.0f,1.0f))
		->InheritsFrom(CCT_COLOUR)
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_PLAY_NOTABLE)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_MARSHALL_POINTS_FOLLOWUP,1.0f, 7.0f, 5.0f))
		->AddInvalidation(CCT_PLAY_INTERRUPTING)
		->AddSupersedence(CCT_AMBIENT_PMP)
		->AddSupersedence(CCT_COLOUR_BONUS_POINTS)
		;
	

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_GENERIC,1.0f,0.0f, 4.0f))
		->InheritsFrom(CCT_COLOUR)
		->AddSupersedence(CCT_PMP_SPECIFIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME)
		;
	
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_SPECIFIC,1.0f,0.0f, 4.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_PMP_HISTORIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_HISTORIC,1.0f,0.0f, 4.0f))
		->InheritsFrom(CCT_PMP_SPECIFIC)
		->AddSupersedence(CCT_PMP_SIGNIFICANT)
		->AddSupersedence(CCT_AMBIENT_IN_GAME)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_SIGNIFICANT,1.0f,0.0f, 4.0f))
		->InheritsFrom(CCT_PMP_HISTORIC)
		->AddSupersedence(CCT_PMP_SIGNIFICANT_SPECIFIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME)
		;

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_SIGNIFICANT_SPECIFIC,1.0f,0.0f, 4.0f))
		->InheritsFrom(CCT_PMP_SIGNIFICANT)
		->AddSupersedence(CCT_AMBIENT_IN_GAME)
		;

	//gating categories to prevent a commentator from mentioning the same stat twice in a row
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_NISBO_STAT_TRY_COUNT,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_NISBO_STAT_PENALTY_COUNT,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_NISBO_STAT_CAPACITY,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_NISBO_STAT_SCORE,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_NISBO_STAT_TREND,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	

	//some, many gating categories for Marshall.
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_TRIES,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_PHASES,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_PENALTIES,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_HANDLING,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_TACKLING,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_INJURIES,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_GOALKICK,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_KICKING,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_SCRUMS,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_LINEBREAK,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_YELLOW_CARD,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_RED_CARD,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_LINEOUTS,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_RUCK_TURNOVER,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_TERRITORY,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_PERFORMANCE,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_KEY_PLAYER,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_POSSESSION,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);
	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_PMP_MARSHALL_STAT_TURNOVERS,1.0f,15.0f))
		->InheritsFrom(CCT_PMP_GENERIC)
		->AddSupersedence(CCT_AMBIENT_IN_GAME);

	classification_associations->AddClassification(MabMemNew( heap) SSCommentaryClassification(CCT_TMO_TRY_AWARDED,1.0f,0.0f))
		->InheritsFrom(CCT_PLAY_IMPORTANT);
	
	classification_associations->Finalise();

	commentary_data_provider = MabMemNew(heap) RUCommentaryDataProvider;
	commentary_interpreter = MabMemNew(heap) MabLuaInterpreter();
	phrase_binder = MabMemNew( heap ) RUPhraseBinder( this, commentary_data_provider);
	data_collector = MabMemNew(heap) RUCommentaryDataCollector(heap, commentary_data_provider, commentary_interpreter, phrase_binder );
	currently_referenced_articles = MabMemNew(heap) RUReferencedArticles();
#ifndef DISABLE_COMMENTARY
	//TODO: add a phrase length filter, to randomly select for short or long phrases.
	phrase_renderer = MabMemNew(heap) RUPhraseRenderer();
#endif

	//create the colour commentary director
	pre_mid_post_director = MabMemNew( heap ) RUPMPDirector();
}


/**
* ~RUCommentary()
*
* Destructor
*/
RUCommentary::~RUCommentary()
{	
	MabMemDeleteSafe(pre_mid_post_director);
	MabMemDeleteSafe(phrase_renderer);

	MabMemDeleteSafe(data_collector);
	MabMemDeleteSafe(commentary_interpreter);
	MabMemDeleteSafe(commentary_data_provider);
	
	MabMemDeleteSafe(phrase_binder);
	MabMemDeleteSafe(classification_associations);


	MabMemDeleteSafe(commentator_marshall);
	MabMemDeleteSafe(commentator_nisbo);

	MabMemDeleteSafe(currently_referenced_articles);
}

void RUCommentary::Update( MabObservable<SIFProfileUpdateMessage>* /*source*/, const SIFProfileUpdateMessage& msg )
{
	// Only change the sound settings if this profile is the master profile that can change settings
	if( msg.update_type == SIF_PROFILE_ONACTIVE || msg.update_type == SIF_PROFILE_CHANGED )
	{
		load_next_update = true;
	}
}

void RUCommentary::Reset()
{
	pending_phrase.Reset();
	current_phrase.Reset();
	last_phrase.Reset();

	phrase_renderer->Stop();
	pre_mid_post_director->Stop();

	currently_referenced_articles->Reset();

	event_search_time = 0;
	last_event_search_absolute_time = 0;
	highest_record_id_encountered = -1;

	commentary_data_provider->Reset();
	data_collector->Reset();

	calculated_game_data = false;

	m_previousPlayerIdxSpokenByMain = -1;
	//#rc3_legacy commentary_interpreter->DoGarbageCollection();
}


void RUCommentary::AttachGameWorld( SIFGameWorld* ggame )
{
	context_bucket = NULL;

	if(ggame==NULL && game!=NULL)
	{
		game->GetEvents()->full_time.Remove(this, &RUCommentary::OnFullTime);
	}

	// Store gameworld
	game = ggame; 

	phrase_binder->SetWorld(ggame);
	data_collector->SetWorld(ggame);

	if(game!=NULL && game->GetEmotionEngineManager())
		context_bucket = game->GetEmotionEngineManager()->GetContextBucket();

	pre_mid_post_director->SetWorld(ggame);

	if(game)
	{
		game->GetEvents()->full_time.Add(this, &RUCommentary::OnFullTime);
	}

	Reset();
}

/// Load data for Commentary (if Called a 2nd time, will re-load, adjust, etc
void RUCommentary::LoadData(Language load_language)
{
	static const char* COMMENTARY_DATABASE[][2] =
	{
		{"", ""},
		{"en_playbyplaycom.xds", "en_colourcom.xds"},
		{"fr_playbyplaycom.xds", "fr_colourcom.xds"}
	};

	static const char* COMMENTARY_WAVEBANK[][2] =
	{
		{"", ""},
		{"en_playbyplaycom.fsb", "en_colourcom.fsb"},
		{"fr_playbyplaycom.fsb", "fr_colourcom.fsb"}
	};

	const FString InternalProjectFSBPath = FString("Rugby/commentary_fsb/");

#if PLATFORM_PS4
	const FString COMMENTARY_FSB_PATH = "/app0/Rugby/Content/" + InternalProjectFSBPath;
#elif PLATFORM_SWITCH
	const FString COMMENTARY_FSB_PATH = GetSwitchPathManager().ConvertToSwitchPath(TEXT("Rugby/Content/"), true) + InternalProjectFSBPath;
#else
	const FString COMMENTARY_FSB_PATH = FPaths::ProjectContentDir() + InternalProjectFSBPath;
#endif
	FString mainCommPath = COMMENTARY_FSB_PATH + COMMENTARY_WAVEBANK[load_language][0];
	FString colourCommPath = COMMENTARY_FSB_PATH + COMMENTARY_WAVEBANK[load_language][1];

	if(loaded_language != load_language)
	{
		// Clear any current commentary
		Skip();

		//Setup Nisbo.
		if (commentator_nisbo)
		{
			MabMemDeleteSafe(commentator_nisbo);
		}
		commentator_nisbo = MabMemNew(heap) RUCommentator(
			heap,
			COMMENTARY_DATABASE[load_language][0],
			TCHAR_TO_ANSI(*mainCommPath),
			commentary_data_provider,
			commentary_interpreter,
			classification_associations,
			NISBO_VOLUME,
			CommentatorType::MAIN,
			true);

		//Setup Marshall
		if (commentator_marshall)
		{
			MabMemDeleteSafe(commentator_marshall);
		}
		commentator_marshall = MabMemNew(heap) RUCommentator(
			heap,
			COMMENTARY_DATABASE[load_language][1],
			TCHAR_TO_ANSI(*colourCommPath),
			commentary_data_provider,
			commentary_interpreter,
			classification_associations,
			MARSHALL_VOLUME,
			CommentatorType::COLOUR
			);

		loaded_language = load_language;
		PostLoadDataSetup();
	}
}

//#define SCREEN_PRINT_COMMENTARY 1

void RUCommentary::Update(float delta_time)
{	
	if (IsDisabled())
	{
		return;
	}

	const MabTime current_time = MabTime::GetCurrentMabTime();

#ifdef DISABLE_AUDIO_COMMENTARY
	return;
#endif

	if(load_next_update)
	{
		SIFPlayerProfile* master_profile = SIFPlayerProfileManager::GetInstance()->GetMasterProfile();
		if(master_profile)
		{
			const MabNamedValue* value = master_profile->GetNamedValueList()->GetNamedValue(PLAYER_PROFILE_COMMENTARY_LANGUAGE);
			if(value)
			{
				Language loadLanguage = (Language)value->ToInt();

				if (loadLanguage == Language::RUCL_French)
				{
					RUGameSettings* game_settings = SIFApplication::GetApplication()->GetMatchGameSettings();
					if (game_settings)
					{
						const RUDB_TEAM& db_team = game_settings->team_settings[(int)SSTEAMSIDE::SIDE_A].team;
						unsigned char gender_flag = SIFGameHelpers::GAGetTeamGenderPermissionFlags(db_team.GetDbId());

						if ((gender_flag & PLAYER_GENDER_FLAG_FEMALE) == PLAYER_GENDER_FLAG_FEMALE)
						{
							loadLanguage = RUCL_English;
						}
					}
				}

				LoadData(loadLanguage);
				load_next_update = false;
			}
		}
	}

#ifdef COMMENTARY_NAME_DEBUG
	phrase_renderer->Update(delta_time);
#endif

	// Must have a GameWorld to do anything useful.
	if( !game)
		return;
	
	// Must have a GameState to do anything useful.
	if (!game->GetGameState())
		return;

	// Don't bother doing anything if we are paused.
	if(time_paused > 0.0f)
	{
		time_paused -= delta_time;
		if(time_paused <= 0.0f)
		{
			phrase_renderer->Stop();
			//SetPaused(false);
		}
	}

	if(paused)
		return;

	if(game->GetGameState()->GetPhase() == NONE)
		return;

	if(!calculated_game_data)
	{
		data_collector->CalculateGameData();

		//make sure we seed the periodic stats
		data_collector->CollectPeriodicStats();
		calculated_game_data = true;
	}

	data_collector->Update(delta_time);
	
	//check to see if our current phrase has stopped playing.
	//this will give us the opportunity to immediately queue, the resulting events.
	if(current_phrase.selected_phrase.phrase && phrase_renderer->GetState() == RUPhraseRenderer::SPEECH_FREE)
	{
		last_phrase = current_phrase;
		last_phrase.bound_phrase.finished_playing_time = MabTime::GetCurrentMabTime();

		//check to see if this phrase had an event to spawn when it stopped playing
		ProcessPhraseCompletion(last_phrase);

		current_phrase.Reset();
	}

	event_search_time += delta_time;

	//check to see if any phrases have become eligible
	if(	context_bucket != NULL && 
	    game->GetGameSettings().game_settings.game_type != GAME_TRAINING )
	{
		event_search_time = 0;

		//search for new events from the last time we searched
		MabVector<SSContextBucketEvent*> results;
		unsigned int num_events = context_bucket->GetEvents( 
			results,
			3, 
			last_event_search_absolute_time,
			true, 
			SSCB_CLASS_GAMEPLAY, 
			SSCB_EVENT_TYPE_NONE );

#ifdef wwDEBUG_MG
		APlayerController* player = UGameplayStatics::GetPlayerController(game->GetGameInstance().GetWorld(), 0);
		if (player)
		{
			if (player->WasInputKeyJustPressed(EKeys::NumPadZero))
			{
				GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Green, FString::Printf(TEXT("Forced Comm Event Triggered")));

				results.clear();
				
				//=============================================
				//Inserts forced SSContextBucketEvent event here
				//=============================================

				//EG comm event
				/*
				RUGameState *game_state = game->GetGameState();
				RUTeam* teamA = game_state->GetNorthTeam();
				RUTeam* teamB = game_state->GetSouthTeam();
				unsigned short team_idxA = teamA->GetDbTeam().GetDbId();
				unsigned short team_idxB = teamB->GetDbTeam().GetDbId();
				SSContextBucketEvent* lolMemLeak = new RUCBPreMidPostEvent(0, SSCB_PMP_FULL_TIME, team_idxA, team_idxB);
				results.push_back(lolMemLeak);
				*/

				//=============================================

				num_events = results.size();
				IsForcingComm = true;
			}
		}
#endif

		//set the last search time, leave it as an inclusive value (we will get the results that are posted at that time stamp twice) as
		//we will remember the highest record ID we've seen so far, and cull records lower than that.
		//This is because our num-results limit might cut the results between two records that have the same
		//time stamp. 
		if(results.size() > 0)
		{
			//set the last search time to the last result, as we may take a couple of frames to process
			//a set of events that share a single time stamp.
			last_event_search_absolute_time = results.back()->event_time;
		}
		else
		{
			last_event_search_absolute_time = game->GetSimTime()->GetAbsoluteStepCount();
		}

		if(num_events > 0)
		{
			//grab our currently selected phrase, the phrase selector will
			//use it to determine if it can be replaced by a more important / lucky phrase.
			SSSelectedPhrase selected_phrase = pending_phrase.selected_phrase;
			bool can_replace_current = (!selected_phrase.phrase && current_time - current_phrase.bound_phrase.bound_time < PENDING_PHRASE_MIN_WAIT_TIME);
			if( can_replace_current )
			{
				selected_phrase = current_phrase.selected_phrase;
			}

			for ( unsigned int i = 0; i < num_events; i++ )
			{
				SSContextBucketEvent *current_event = results[i];

				if(current_event->record_id <= highest_record_id_encountered && !IsForcingComm)
				{
					//we've already considered up to and including this record, so we don't
					//need to consider it again
					continue;
				}

				//remember that we've considered this event.
				highest_record_id_encountered = current_event->record_id;

				MabTime current_mab_time = MabTime::GetCurrentMabTime();

				UpdateAmbientClassifications();
				
				//setup the source data for the selection criteria to draw on.
				data_collector->CollectFromEvent(current_event);
				
				//determine how urgent it is that we play something to fill the air.
				float pacing_urgency = CalculatePacingUrgency(current_mab_time);

				EvaluateEventForCommentator(commentator_nisbo, current_event, selected_phrase, current_mab_time, pacing_urgency);

				if (!m_isColourDisabled)
				{
					EvaluateEventForCommentator(commentator_marshall, current_event, selected_phrase, current_mab_time, pacing_urgency);
				}
			}

			if( can_replace_current && pending_phrase.selected_phrase.phrase )
			{
				phrase_renderer->StopOnNextSilence();
			}
		}
	}

	//check to see if we can play our pending phase.
	if(pending_phrase.selected_phrase.phrase )
	{
		//wait a small amount of time between binding a phrase and playing it to allow immediately following events
		//a chance to evaluate.
		//if(current_time - pending_phrase.bound_phrase.bound_time > PENDING_PHRASE_MIN_WAIT_TIME)
		{
			//play the phrase when the commentary is free
			if( phrase_renderer->GetState() == RUPhraseRenderer::SPEECH_FREE )
			{
				float interruption_delay = PHRASE_INTERRUPTION_DELAY;

#ifdef ENABLE_SIF_DEBUG_DRAW
				interruption_delay = SIFDebug::emotion_engine_debug_menu->phrase_interruption_delay;		
#endif

				//if we've interrupted a phrase that was playing, wait for a little bit
				if(!phrase_renderer->IsStoppingOnNextSilence()
					|| current_time - last_phrase.bound_phrase.finished_playing_time > interruption_delay)
				{
					MABASSERT(pending_phrase.commentator);

					RURenderablePhrase renderable_phrase;
					renderable_phrase.phrase = pending_phrase.bound_phrase.bound_samples;

					renderable_phrase.wavebank = pending_phrase.commentator->wavebank;
					renderable_phrase.debug_wavebank = pending_phrase.commentator->debug_wavebank;

					phrase_renderer->Play(renderable_phrase, pending_phrase.commentator->volume);

					//remember when we've played this phrase, so that we can reduce the chance of selecting
					//it again.
					pending_phrase.selected_phrase.phrase->played_time = current_time.ToSeconds();

					//update the played time of the appropriate classifications for pacing purposes.
					pending_phrase.selected_phrase.phrase_group->classification->SetPlayed(current_time);

					//make the pending phrase the current phrase, so that we can remember what we're saying for potential
					//interruptions if appropriate
					current_phrase = pending_phrase;
					current_phrase.play_time = MabTime::GetCurrentMabTime();

					//update any articles that we've mentioned from this phrase.
					currently_referenced_articles->UpdateFrom(pending_phrase.referenced_articles);

					//clear out the pending phrase
					pending_phrase.Reset();

					IsForcingComm = false;
				}
			}
			else if(
#ifdef ENABLE_SIF_DEBUG_DRAW
				SIFDebug::emotion_engine_debug_menu->commentary_always_interrupts ||		
#endif
				current_phrase.selected_phrase.phrase_group->classification->InvalidatedBy(
					pending_phrase.selected_phrase.phrase_group->classification)
				)
			{
				//we have waited the minimum amount of time, and are ready to play, but we invalidate the currently playing phrase
				//so lets interrupt the current phrase.
				//phrase_renderer->Stop(PENDING_PHRASE_MIN_WAIT_TIME);

				phrase_renderer->StopOnNextSilence();

				//Glen: TODO: determine if the last phrase was killed due to a stop on silence request and add an appropriate
				//pause length for taking a breath or exclaiming before playing the interrupting sample.
			}
			else if(MabTime::GetCurrentMabTime() - pending_phrase.bound_phrase.bound_time
				> pending_phrase.selected_phrase.phrase_group->classification->GetMaxQueueTime() )
			{
				//we've been holding onto the pending phrase for too long, so lets ditch it.
			
#ifdef ANDREWS_PHRASE_DEBUGOR
				MABLOGDEBUG( "### Timing Out : %s", pending_phrase.selected_phrase.phrase_group->group_id.c_str() );
#endif

				pending_phrase.Reset();
			}
		}
	}

	//TODO: we may be in a transition to something important and there is a reason for the speech being free, such as a game state
	//where commentary isn't appropriate. In which case we want to avoid the PMP director queueing further phrases. Though the
	//ambient classification system should cover this case. 'Should', it may be buggy.
	bool speech_free = pending_phrase.selected_phrase.phrase == NULL && current_phrase.selected_phrase.phrase == NULL;
	pre_mid_post_director->Update(delta_time,speech_free); 

#ifdef SCREEN_PRINT_COMMENTARY

	MabDebugDraw::Point2D debug_pos(0.0f,100.0f);

	debug_pos.y += 20.0f;
	if(pending_phrase.selected_phrase.phrase)
	{
		MabString debug_string;
		if(phrase_renderer->GetState() == RUPhraseRenderer::SPEECH_PLAYING && phrase_renderer->IsStoppingOnNextSilence())
		{
			debug_string = MabString(256, "Pending Phrase: %s, INTERRUPTING, %s, %s",
				pending_phrase.selected_phrase.phrase_group->group_id.c_str(),
				pending_phrase.selected_phrase.phrase->phrase.c_str(),
				pending_phrase.selected_phrase.phrase->articles.c_str());
		}
		else
		{
			debug_string = MabString(256, "Pending Phrase: %s, %s, %s",
				pending_phrase.selected_phrase.phrase_group->group_id.c_str(),
				pending_phrase.selected_phrase.phrase->phrase.c_str(),
				pending_phrase.selected_phrase.phrase->articles.c_str());
		}

		SIF_DEBUG_DRAW( SetText(  SIFDebugDrawSingleFrameHandle(), debug_pos.x, debug_pos.y, debug_string.c_str(), MabColour::White ) );

		MabColour colour( MabColour::DarkBlue.r, MabColour::DarkBlue.g, MabColour::DarkBlue.b, 0.75f );		
		SIF_DEBUG_DRAW( Set2DRect( SIFDebugDrawSingleFrameHandle(), NULL, debug_pos, debug_pos + MabDebugDraw::Point2D(float(debug_string.length())*8.0f, 20.0f), colour ) );
	}

	debug_pos.y += 20.0f;
	if(current_phrase.selected_phrase.phrase)
	{
		MabString debug_string(256, "Current Phrase: %s, %s, %s, %0.2fs %0.2fI",
			current_phrase.selected_phrase.phrase_group->group_id.c_str(),
			current_phrase.selected_phrase.phrase->phrase.c_str(),
			current_phrase.selected_phrase.phrase->articles.c_str(),
			(current_phrase.play_time - current_phrase.creation_time).ToSeconds(),
			SSExcitementLevelToFloat(current_phrase.selected_phrase.phrase->excitement_level) );

		SIF_DEBUG_DRAW( SetText(  SIFDebugDrawSingleFrameHandle(), debug_pos.x, debug_pos.y, debug_string.c_str(), MabColour::White ) );

		MabColour colour( MabColour::DarkBlue.r, MabColour::DarkBlue.g, MabColour::DarkBlue.b, 0.75f );		
		SIF_DEBUG_DRAW( Set2DRect( SIFDebugDrawSingleFrameHandle(), NULL, debug_pos, debug_pos + MabDebugDraw::Point2D(float(debug_string.length())*8.0f, 20.0f), colour ) );
	}

	debug_pos.y += 40.0f;
	if(last_phrase.selected_phrase.phrase)
	{
		MabString debug_string(256, "Last Phrase: %s, %s, %s",
			last_phrase.selected_phrase.phrase_group->group_id.c_str(),
			last_phrase.selected_phrase.phrase->phrase.c_str(),
			last_phrase.selected_phrase.phrase->articles.c_str());


		SIF_DEBUG_DRAW( SetText(  SIFDebugDrawSingleFrameHandle(), debug_pos.x, debug_pos.y, debug_string.c_str(), MabColour::White ) );

		MabColour colour( MabColour::DarkBlue.r, MabColour::DarkBlue.g, MabColour::DarkBlue.b, 0.75f );		
		SIF_DEBUG_DRAW( Set2DRect( SIFDebugDrawSingleFrameHandle(), NULL, debug_pos, debug_pos + MabDebugDraw::Point2D(float(debug_string.length())*8.0f, 20.0f), colour ) );
	}

	
#endif

	phrase_renderer->Update(delta_time);
}

/// Pauses or unpauses commentary
void RUCommentary::SetPaused(bool newPaused)
{
	//check to see if we are already there.
	if(paused == newPaused)
		return;

	//pause the commentary sound event
	phrase_renderer->SetPaused(newPaused);


	//remember that we are paused so that we don't process new events.
	paused = newPaused;
	time_paused = paused ? MAX_PAUSE_TIME : 0.0f;
}

/// Skips current commentary
void RUCommentary::Skip()
{
	pending_phrase.Reset();
	phrase_renderer->Stop(); // For now lets try this.
}

void RUCommentary::OnFullTime()
{
	//Glen: TODO: 
	if(data_collector->GetCompetitionAnalysis())
	{
		data_collector->GetCompetitionAnalysis()->AnalysePostMatch(commentary_interpreter, data_collector->GetFavouredTeam());
	}
}

float RUCommentary::CalculatePacingUrgency( const MabTime &current_time)
{
	float pacing_urgency = 0.0f;

	MabTime last_play_time = 0.0f;

	if(last_phrase.selected_phrase.phrase)
	{
		last_play_time = last_phrase.bound_phrase.finished_playing_time;
	}

	//if we're currently playing a phrase, then the last time we've played something is 'now'
	if(phrase_renderer->GetState() != RUPhraseRenderer::SPEECH_FREE)
	{
		last_play_time = current_time;
	}
	
	float pacing_urgency_period = COMMENTARY_PACING_URGENCY_PERIOD;

#ifdef ENABLE_SIF_DEBUG_DRAW
	pacing_urgency_period = SIFDebug::emotion_engine_debug_menu->commentary_pacing_urgency_period;
	MabMath::ClampLower(pacing_urgency_period, 0.1f);
#endif

	float elapsed_time = (current_time - last_play_time).ToSeconds();
	pacing_urgency = elapsed_time / pacing_urgency_period;

	MabMath::ClampUpper(pacing_urgency, 1.0f);

	//square the pacing urgency value to ensure that it doesn't come on too quickly
	pacing_urgency *= pacing_urgency;

	return pacing_urgency;
}

void RUCommentary::EvaluateEventForCommentator(
	RUCommentator* commentator,
	SSContextBucketEvent *current_event,
	SSSelectedPhrase &selected_phrase,
	MabTime current_mab_time,
	float pacing_urgency)
{
	if (current_event)
	{
		UE_LOG(
			LogCommentaryEventInformation,
			Log,
			TEXT("RUCommentary::EvaluateEventForCommentator Start of function, checking what event this is: event class - %i & event time %i & event type - %i"),
// 			typeid(*current_event).name(),
// 			typeid(*current_event).raw_name(),
			current_event->event_class,
			current_event->event_time,
			current_event->event_type
		);

		if (selected_phrase.phrase && selected_phrase.phrase_group)
		{
			UE_LOG(
				LogCommentaryEventInformation,
				Log,
				TEXT("RUCommentary::EvaluateEventForCommentator after event check, checking what phrase this is: current phrase is - %s & phrase articles - %s & phrase gender - %i & phrase play count - %i & phrase played time - %f with phrase group id is - %s & phrase group priority - %i & phrase group trigger event - %i"),
				*MabCharArrayToFString(selected_phrase.phrase->phrase.c_str()),
				*MabCharArrayToFString(selected_phrase.phrase->articles.c_str()),
				selected_phrase.phrase->gender,
				selected_phrase.phrase->play_count,
				selected_phrase.phrase->played_time,
				*MabCharArrayToFString(selected_phrase.phrase_group->group_id.c_str()),
				selected_phrase.phrase_group->priority,
				selected_phrase.phrase_group->trigger_event
			);
		}

		current_event->PrintDebug();
	}

	//select a valid phrase group from which to draw a phrase from
	SSSelectedPhraseGroup selected_group;
	if(commentator->phrase_selector && commentator->phrase_selector->SelectPhraseGroup( current_event->event_type, current_mab_time, pacing_urgency, selected_group ))
	{
		SSSelectedPhrase previously_selected_phrase = selected_phrase;

		if (selected_group.phrase_group)
		{
			UE_LOG(
				LogCommentaryEventInformation,
				Log,
				TEXT("RUCommentary::EvaluateEventForCommentator Select group has been selected, checking what phrase group this is: current phrase group id is - %s & phrase group priority - %i & phrase group trigger event - %i with weighting at selection is - %f"),
				*MabCharArrayToFString(selected_group.phrase_group->group_id.c_str()),
				selected_group.phrase_group->priority,
				selected_group.phrase_group->trigger_event,
				selected_group.weighting_at_selection
			);
		}

		//now find a phrase within that group that we can bind data for
		for(int j = 0; j < MAX_BIND_ATTEMPTS && commentator->phrase_selector->SelectPhraseFromSelectedGroup(current_event, selected_group, selected_phrase); j++)
		{
			//Glen: HACK TO WORK AROUND AN INTRINSIC BUG OF RUPhraseFilterPlayedCount
			//increment the played count for at least one phrase that passes through RUPhraseFilterPlayedCount and don't wait until
			//we've actually played it. This is because that there is a high chance that a phrase that fails to bind now will 
			//also fail to bind in the future. The RUPhraseFilterPlayedCount will see the low played count of this
			//phrase (because we keep on failing to bind it) and will keep on returning this and other un-bindable phrases through.
			//This will end up never letting a phrase through that we can bind, resulting in whole categories suddenly not playing anymore
			//after all their bindable phrases have played once.
			selected_phrase.phrase->play_count++;

			UE_LOG(
				LogCommentaryEventInformation,
				Log,
				TEXT("RUCommentary::EvaluateEventForCommentator for loop checking phrases, checking what event this is: event class - %i & event time %i & event type - %i"),
// 				typeid(*current_event).name(),
// 				typeid(*current_event).raw_name(),
				current_event->event_class,
				current_event->event_time,
				current_event->event_type
			);

			if (selected_group.phrase_group)
			{
				UE_LOG(
					LogCommentaryEventInformation,
					Log,
					TEXT("RUCommentary::EvaluateEventForCommentator Select group has been selected, checking what phrase group this is: current phrase group id is - %s & phrase group priority - %i & phrase group trigger event - %i with weighting at selection is - %f"),
					*MabCharArrayToFString(selected_group.phrase_group->group_id.c_str()),
					selected_group.phrase_group->priority,
					selected_group.phrase_group->trigger_event,
					selected_group.weighting_at_selection
				);
			}

			if (selected_phrase.phrase && selected_phrase.phrase_group)
			{
				UE_LOG(
					LogCommentaryEventInformation,
					Log,
					TEXT("RUCommentary::EvaluateEventForCommentator after event check, checking what phrase this is: current phrase is - %s & phrase articles - %s & phrase gender - %i & phrase play count - %i & phrase played time - %f with phrase group id is - %s & phrase group priority - %i & phrase group trigger event - %i"),
					*MabCharArrayToFString(selected_phrase.phrase->phrase.c_str()),
					*MabCharArrayToFString(selected_phrase.phrase->articles.c_str()),
					selected_phrase.phrase->gender,
					selected_phrase.phrase->play_count,
					selected_phrase.phrase->played_time,
					*MabCharArrayToFString(selected_phrase.phrase_group->group_id.c_str()),
					selected_phrase.phrase_group->priority,
					selected_phrase.phrase_group->trigger_event
				);
			}

			//handle gendered phrases
			if (!EvaluatePhraseForGender(selected_phrase, current_event, commentator->GetCommentatorType() == CommentatorType::COLOUR))
			{
				UE_LOG(
					LogCommentaryEventInformation,
					Log,
					TEXT("RUCommentary::EvaluateEventForCommentator Failed gender check")
				);

				UE_LOG(
					LogCommentaryEventInformation,
					Log,
					TEXT("RUCommentary::EvaluateEventForCommentator checking what event this is: event class - %i & event time %i & event type - %i"),
// 					typeid(*current_event).name(),
// 					typeid(*current_event).raw_name(),
					current_event->event_class,
					current_event->event_time,
					current_event->event_type
				);

				if (selected_phrase.phrase && selected_phrase.phrase_group)
				{
					UE_LOG(
						LogCommentaryEventInformation,
						Log,
						TEXT("RUCommentary::EvaluateEventForCommentator after event check, checking what phrase this is: current phrase is - %s & phrase articles - %s & phrase gender - %i & phrase play count - %i & phrase played time - %f with phrase group id is - %s & phrase group priority - %i & phrase group trigger event - %i"),
						*MabCharArrayToFString(selected_phrase.phrase->phrase.c_str()),
						*MabCharArrayToFString(selected_phrase.phrase->articles.c_str()),
						selected_phrase.phrase->gender,
						selected_phrase.phrase->play_count,
						selected_phrase.phrase->played_time,
						*MabCharArrayToFString(selected_phrase.phrase_group->group_id.c_str()),
						selected_phrase.phrase_group->priority,
						selected_phrase.phrase_group->trigger_event
					);
				}

				//if it doesnt match, we should not play the phrase!
				selected_phrase = previously_selected_phrase; //restore the previous phrase if we've failed to select the right gendered phrase.
				continue;
			}


			//bind some data to this phrase that may be provided in
			//the event.
			RUBoundPhrase bound_phrase;
			if(phrase_binder->BindPhrase(selected_phrase, current_event, commentator->inserts, commentator->phrase_selector, bound_phrase))
			{
				UE_LOG(
					LogCommentaryEventInformation,
					Log,
					TEXT("RUCommentary::EvaluateEventForCommentator Phrase has been bound")
					);

				UE_LOG(
					LogCommentaryEventInformation,
					Log,
					TEXT("RUCommentary::EvaluateEventForCommentator checking what event this is: event class - %i & event time %i & event type - %i"),
// 					typeid(*current_event).name(),
// 					typeid(*current_event).raw_name(),
					current_event->event_class,
					current_event->event_time,
					current_event->event_type
				);

				if (selected_group.phrase_group)
				{
					UE_LOG(
						LogCommentaryEventInformation,
						Log,
						TEXT("RUCommentary::EvaluateEventForCommentator Select group has been selected, checking what phrase group this is: current phrase group id is - %s & phrase group priority - %i & phrase group trigger event - %i with weighting at selection is - %f"),
						*MabCharArrayToFString(selected_group.phrase_group->group_id.c_str()),
						selected_group.phrase_group->priority,
						selected_group.phrase_group->trigger_event,
						selected_group.weighting_at_selection
					);
				}

				if (selected_phrase.phrase && selected_phrase.phrase_group)
				{
					UE_LOG(
						LogCommentaryEventInformation,
						Log,
						TEXT("RUCommentary::EvaluateEventForCommentator after event check, checking what phrase this is: current phrase is - %s & phrase articles - %s & phrase gender - %i & phrase play count - %i & phrase played time - %f with phrase group id is - %s & phrase group priority - %i & phrase group trigger event - %i"),
						*MabCharArrayToFString(selected_phrase.phrase->phrase.c_str()),
						*MabCharArrayToFString(selected_phrase.phrase->articles.c_str()),
						selected_phrase.phrase->gender,
						selected_phrase.phrase->play_count,
						selected_phrase.phrase->played_time,
						*MabCharArrayToFString(selected_phrase.phrase_group->group_id.c_str()),
						selected_phrase.phrase_group->priority,
						selected_phrase.phrase_group->trigger_event
					);
				}

				//we've successfully bound a phrase.
				pending_phrase.Reset();
				pending_phrase.selected_phrase = selected_phrase;
				pending_phrase.bound_phrase = bound_phrase;
				pending_phrase.commentator = commentator;
				pending_phrase.creation_time = MabTime::GetCurrentMabTime();

				//store the referenced articles as well.
				pending_phrase.referenced_articles->player_index = selected_phrase.phrase->consequential_player_id;
				pending_phrase.referenced_articles->team_index = selected_phrase.phrase->consequential_team_id;

				//handle m_previousPlayerIdxEventDataNameUsed if it was the main commentator
				if (commentator->GetCommentatorType() == CommentatorType::MAIN)
				{
					//we got this far, temporarily store 'playerIdxEventDataName' for when the colour commentator does a response 
					//so it knows which player its talking about (since colour comm doesnt have articles)
					//if the phrase was not talking about a player, it will reset

					//just incase multiple phrases get binded before the colour comm gets to speak (only set a new value if its a valid player id)
					//so the the colour comm can consume that id when it needs too
					int potentialPlayerId = GetPlayerIdxFromEventAndPhrase(current_event, pending_phrase.selected_phrase.phrase);
					if (potentialPlayerId != -1)
					{
						m_previousPlayerIdxSpokenByMain = potentialPlayerId;
					}
				}
				else
				{
					//reset this out as the colour commentator has used it or didnt need it :P
					m_previousPlayerIdxSpokenByMain = -1;
				}

				MABLOGDEBUG("RUCommentary::EvaluateEventForCommentator(); Phrase Successfully Bound! Phrase: %s ; Articles: %s , Gender: %d", selected_phrase.phrase->phrase.c_str(), selected_phrase.phrase->articles.c_str(), selected_phrase.phrase->gender);

				break;
			}
			else
			{
				//restore the previous phrase if we've failed to bind the new phrase.
				selected_phrase = previously_selected_phrase;

				UE_LOG(
					LogCommentaryEventInformation,
					Log,
					TEXT("RUCommentary::EvaluateEventForCommentator Phrase bind has failed")
				);
			}
		}
	}
}

bool RUCommentary::EvaluatePhraseForGender(const SSSelectedPhrase selected_phrase, SSContextBucketEvent* current_event, const bool isColourCommentator)
{
	//quick error check
	if (!current_event)
	{
		return false;
	}

	int playerId = -1;
	uint32 genderOfTargetPlayer = COMMENTARY_GENDER_NEUTRAL;
	if (selected_phrase.phrase)
	{
		//if this event isnt gender neutral, lets check if we can say it :P
		if (selected_phrase.phrase->gender != COMMENTARY_GENDER_NEUTRAL)
		{
			//ifs its talking about any player (if it isnt, we can assume its neutral)
			//can continue if its the colour comm (since colour comm doesnt have articles)
			if (selected_phrase.phrase->articles.find("player") != MabString::npos || isColourCommentator)
			{
				if (!isColourCommentator)
				{
					playerId = GetPlayerIdxFromEventAndPhrase(current_event, selected_phrase.phrase);
				}
				else
				{
					playerId = m_previousPlayerIdxSpokenByMain;
				}

				//error check for colour comm
				if (isColourCommentator && m_previousPlayerIdxSpokenByMain == -1)
				{
#ifdef ENABLE_COMMENTARY_PEDANTIC_CHECKING
					MABASSERTMSG(false, MabString(0, "EvaluatePhraseForGender(); Colour Comm Gendered event but has invalid m_previousPlayerIdxSpokenByMain! Phrase: %s", selected_phrase.phrase->phrase.c_str()).c_str());
#endif
				}

				//get the gender of the player thats being talked about
				if(playerId > -1)
				{
					RUCommentaryPlayer player_out = RUCommentaryPlayer(game, playerId);
					if (player_out.db_player)
					{
						if (player_out.db_player->IsCustom())
						{
							if (player_out.db_player)
							{
								genderOfTargetPlayer = player_out.db_player->gender + 1; //converting db gender into commentary gender
							}
							else
							{
								RL3DB_PLAYER rl3Player = RL3DB_PLAYER(player_out.db_player->GetDbId());
								genderOfTargetPlayer = rl3Player.GetGender() + 1; //converting db gender into commentary gender
							}
						}
						else
						{
							RL3DB_PLAYER rl3Player = RL3DB_PLAYER(player_out.db_player->GetDbId());
							genderOfTargetPlayer = rl3Player.GetGender() + 1; //converting db gender into commentary gender
						}
					}
				}
				else
				{
					//somehow we have an invalid player id
					//best to skip this phrase...
					return false;
				}
			}
			else
			{
#ifdef ENABLE_COMMENTARY_PEDANTIC_CHECKING
				MABASSERTMSG(false, MabString(0, "EvaluatePhraseForGender(); Gendered event but no player article?! Fix this in Main Commentary database! Phrase: %s", selected_phrase.phrase->phrase.c_str()).c_str() );
#endif
			}

			//does the phrase's gender match the player its talking about?
			if (genderOfTargetPlayer != selected_phrase.phrase->gender)
			{
				//it doesnt match, this event is not valid!
				return false;
			}
		}
	}

	//we got to here, must be valid :P
	return true;
}

MabString RUCommentary::GetPlayerIdxEventDataNameFromPhrase(SSPhrase* phrase)
{
	if (phrase)
	{
		//if the phrase is event talking about a player
		if (phrase->articles.find("player") != MabString::npos)
		{
			//hack method of handling talking about player2 or player (since all the comm events seem to only use those)
			return (phrase->articles.find("player2") != MabString::npos ? "player2_index" : "player_index");
		}
	}
	else
	{
#ifdef ENABLE_COMMENTARY_PEDANTIC_CHECKING
		MABASSERTMSG(false, MabString(0, "RUCommentary::GetPlayerIdxEventDataNameFromPhrase(); Phrase Null?!?!").c_str());
#endif
	}

	return "";
}

int RUCommentary::GetPlayerIdxFromEventAndPhrase(SSContextBucketEvent* current_event, SSPhrase* phrase)
{
	MabString playerIdxEventDataName = GetPlayerIdxEventDataNameFromPhrase(phrase);
	if (!playerIdxEventDataName.empty())
	{
		MabCentralAccessor event_accessor(current_event);
		int* player_id = event_accessor.Get<int>(playerIdxEventDataName.c_str());;

		if (player_id)
		{
			return *player_id;
		}
	}
	
	return -1;
}

void RUCommentary::ProcessPhraseCompletion(const RUCommentaryBoundPhrase &phrase)
{

	if(!context_bucket)
		return;

	RUContextBucketBasicEvents *event_handler = game->GetEmotionEngineManager()->GetBasicEventsHandler();

	switch( phrase.selected_phrase.phrase_group->trigger_event)
	{
	case SSCB_EVENT_TRY:
		{
			//post a try follow up event.
			if(RUCBTryEvent *previous_event = (RUCBTryEvent*) context_bucket->GetLastEvent(SSCB_EVENT_TRY))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_MARSHALL_TRY);
			}
		}
		break;

	case SSCB_MARSHALL_TRY:
		{
			//allow either marshall or nisbo to follow up after marshalls statement.
			if(RUCBTryEvent *previous_event = (RUCBTryEvent*) context_bucket->GetLastEvent(SSCB_EVENT_TRY))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_EVENT_TRY_FOLLOW_UP);
			}
		}
		break;

	case SSCB_EVENT_TRY_FOLLOW_UP:
		{
			//queue Marshall to praise the try scoring player
			if(RUCBTryEvent *previous_event = (RUCBTryEvent*) context_bucket->GetLastEvent(SSCB_EVENT_TRY))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_EVENT_MARSHALL_PRAISE_PLAYER);
			}
			else
			{
				MABBREAK();
			}
		}
		break;

	case SSCB_EVENT_KICK_RESULT:
		{
			//queue a Marshall response to the points being scored, if this was a score
			if(RUCBKickResultEvent *previous_event = (RUCBKickResultEvent*) context_bucket->GetLastEvent(SSCB_EVENT_KICK_RESULT))
			{
				if(previous_event->success)
				{
					//get Marshall to talk about the points
					event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_MARSHALL_POINTS_SCORED);
	
					//fire off the follow up event as well, in-case there is no eligible group for SSCB_MARSHALL_POINTS_SCORED
					event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_MARSHALL_POINTS_FOLLOWUP);
				}

				if (previous_event->kick_context != KC_DROPGOAL)
				{	
					event_handler->StoreGenericFollowUp(previous_event->player_index, previous_event->player2_index, SSCB_MARSHALL_KICK_RESULT);
				}
			}
		}
		break;

	case SSCB_MARSHALL_KICK_RESULT:
		{
			//queue a Marshall response to the points being scored, if this was a score
			if(RUCBKickResultEvent *previous_event = (RUCBKickResultEvent*) context_bucket->GetLastEvent(SSCB_EVENT_KICK_RESULT))
			{
				if(previous_event->success)
				{
					event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_MARSHALL_POINTS_SCORED);

					//fire off the follow up event as well, in-case there is no eligible group for SSCB_MARSHALL_POINTS_SCORED
					event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_MARSHALL_POINTS_FOLLOWUP);
				}
			}

		}
		break;


	//Brian hack - tackle counter
	case SSCB_EVENT_TACKLEE_ON_GROUND:
	{
		int tackleCount = game->GetGameState()->GetTackleCount();
		static unsigned int seed = 12345;
		seed = (1103515245 * seed + 12345) % 32768;
		if (RUCBTackleEvent* previous_event = (RUCBTackleEvent*)context_bucket->GetLastEvent(SSCB_EVENT_TACKLEE_ON_GROUND)) {
		if (tackleCount == 1)
		{
			int randomNumber = (seed % 2) + 1;
			if (randomNumber == 1)
			{
				SSRoleReferee::ref_tackle_count(1);
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_EVENT_TACKLECOUNT_1);
			}
			else
			{
				SSRoleReferee::tackle_held();
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_NISBO_TACKLE_FOLLOWUP);
			}
		}

		else if (tackleCount == 2)
		{
			int randomNumber = (seed % 2) + 1;
			if (randomNumber == 1)
			{
				SSRoleReferee::ref_tackle_count(2);
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_EVENT_TACKLECOUNT_2);
			}
			else
			{
				SSRoleReferee::tackle_held();
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_NISBO_TACKLE_FOLLOWUP);
			}
		}
		
		else if (tackleCount == 3)
		{
			int randomNumber = (seed % 2) + 1;
			if (randomNumber == 1)
			{
				SSRoleReferee::ref_tackle_count(3);
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_EVENT_TACKLECOUNT_3);
			}
			else
			{
				SSRoleReferee::tackle_held();
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_NISBO_TACKLE_FOLLOWUP);
			}
		}

		else if (tackleCount == 4)
		{
			int randomNumber = (seed % 2) + 1;
			if (randomNumber == 1)
			{
				SSRoleReferee::ref_tackle_count(4);
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_EVENT_TACKLECOUNT_4);
			}
			else
			{
				SSRoleReferee::tackle_held();
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_NISBO_TACKLE_FOLLOWUP);
			}
		}

		else if (tackleCount == 5) 
		{
			int randomNumber = (seed % 2) + 1;
			if (randomNumber == 1) 
			{
				SSRoleReferee::ref_tackle_count(5);
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_EVENT_TACKLECOUNT_5);
			}
			else
			{
				SSRoleReferee::tackle_held();
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_NISBO_TACKLE_FOLLOWUP);
			}
		}
		// GGs JZ Moved ref call for 6th to Play the ball phase, as it calls during a high tackle but the commentary doesn't
		// if it is the 6th tackle or zero, zero incase the count has been reset
		else if (tackleCount == 6 || game->GetGameState()->IsZeroTackle()) 
		{
			event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_EVENT_TACKLECOUNT_6);
		}

		//Original code - first line moved to top, 2nd line moved to each check
		//if (RUCBTackleEvent* previous_event = (RUCBTackleEvent*)context_bucket->GetLastEvent(SSCB_EVENT_TACKLEE_ON_GROUND)) {
		//	event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_NISBO_TACKLE_FOLLOWUP);
		}
	}
	break;


	//Brian hack - this is the original code for "case SSCB_EVENT_TACKLEE_ON_GROUND:"
	/*
	case SSCB_EVENT_TACKLEE_ON_GROUND:
	{
		if (RUCBTackleEvent* previous_event = (RUCBTackleEvent*)context_bucket->GetLastEvent(SSCB_EVENT_TACKLEE_ON_GROUND))
		{
			event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_NISBO_TACKLE_FOLLOWUP);
		}
	}
	break;
	*/

	case SSCB_MARSHALL_POINTS_SCORED:
		{
			MabVector<SSCB_TYPE> point_scored_event_types(SIFHEAP_DYNAMIC);
			point_scored_event_types.push_back(SSCB_EVENT_KICK_RESULT);
			point_scored_event_types.push_back(SSCB_EVENT_TRY);

			SSContextBucketEvent *points_scored_event = context_bucket->GetLastEvent( point_scored_event_types );
			if(points_scored_event)
			{
				if(points_scored_event->event_type == SSCB_EVENT_KICK_RESULT)
				{
					RUCBKickResultEvent *previous_event = static_cast<RUCBKickResultEvent*>(points_scored_event);
					event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_MARSHALL_POINTS_FOLLOWUP);
				}
				else if(points_scored_event->event_type == SSCB_EVENT_TRY)
				{
					RUCBTryEvent *previous_event = static_cast<RUCBTryEvent*>(points_scored_event);
					event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_MARSHALL_POINTS_FOLLOWUP);
				}
			}
		}
		break;

	case SSCB_EVENT_MARSHALL_LINEOUT_POSITIVE:
	case SSCB_MARSHALL_TURNOVER:
	case SSCB_EVENT_MARSHALL_PRAISE_PLAYER:
		{
			event_handler->StoreGenericFollowUp(-1,-1, SSCB_NISBO_AGREEMENT);
		}
		break;

	case SSCB_EVENT_LINEOUT_CATCH:
		{
			//see if nisbo said something bad.
			if(phrase.selected_phrase.phrase_group->group_id == "LINEOUT_WON_AGAINST_THROW")
			{
				if(RUCBLineoutThrownEvent *previous_event = (RUCBLineoutThrownEvent*) context_bucket->GetLastEvent(SSCB_EVENT_LINEOUT_THROW))
				{
					event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_EVENT_MARSHALL_LINEOUT_NEGATIVE);
				}
			}
			//see if nisbo said something good.
			else if(phrase.selected_phrase.phrase_group->group_id == "LINEOUT_BALL_TAKEN")
			{
				if(RUCBLineoutCatchEvent *previous_event = (RUCBLineoutCatchEvent*) context_bucket->GetLastEvent(SSCB_EVENT_LINEOUT_CATCH))
				{
					event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_EVENT_MARSHALL_LINEOUT_POSITIVE);
				}
			}
		}
		break;

	case SSCB_EVENT_LINEOUT_SLAP_DOWN:
		{
			if(RUCBLineoutSlapDownEvent *previous_event = (RUCBLineoutSlapDownEvent*) context_bucket->GetLastEvent(SSCB_EVENT_LINEOUT_SLAP_DOWN))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_EVENT_MARSHALL_LINEOUT_POSITIVE);
			}
		}
		break;

	case SSCB_EVENT_LINEOUT_MISS:
		{
			if(RUCBLineoutThrownEvent *previous_event = (RUCBLineoutThrownEvent*) context_bucket->GetLastEvent(SSCB_EVENT_LINEOUT_THROW))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_EVENT_MARSHALL_LINEOUT_NEGATIVE);
			}
		}
		break;

	case SSCB_EVENT_LINEOUT_THROW:
		{
			if(phrase.selected_phrase.phrase_group->group_id == "LINEOUT_TOO_SHORT" 
				|| phrase.selected_phrase.phrase_group->group_id == "LINEOUT_TOO_LONG")
			{
				if(RUCBLineoutThrownEvent *previous_event = (RUCBLineoutThrownEvent*) context_bucket->GetLastEvent(SSCB_EVENT_LINEOUT_THROW))
				{
					event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_EVENT_MARSHALL_LINEOUT_NEGATIVE);
				}
			}
			else if( phrase.selected_phrase.phrase_group->group_id == "LINEOUT_NOT_STRAIGHT")
			{
				if(RUCBLineoutThrownEvent *previous_event = (RUCBLineoutThrownEvent*) context_bucket->GetLastEvent(SSCB_EVENT_LINEOUT_THROW))
				{
					event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_EVENT_MARSHALL_LINEOUT_NOT_STRAIGHT);
				}
			}
		}
		break;

	case SSCB_EVENT_KNOCK_ON:
		{
			//check for an advantage event, which should be soon after the knock on event
			if(RUCBAdvantageEvent *advantage_event = (RUCBAdvantageEvent*) context_bucket->GetLastEvent( SSCB_EVENT_ADVANTAGE, SSCB_EVENT_KNOCK_ON))
			{
				//we have an advantage event, so trigger some advantage commentary
				event_handler->StoreGenericFollowUp(advantage_event->player_index, -1, SSCB_NISBO_ADVANTAGE);
			}
			else if(RUCBKnockOnEvent *previous_event = (RUCBKnockOnEvent*) context_bucket->GetLastEvent(SSCB_EVENT_KNOCK_ON))
			{
				//otherwise see if marshall has anything to say.

				//see if we can grab the previous tackle event
				int player2 = -1;
				if(RUCBTackleEvent *previous_tackle = (RUCBTackleEvent*) context_bucket->GetLastEvent(SSCB_EVENT_TACKLE))
				{
					player2 = previous_tackle->player2_index;
				}

				event_handler->StoreGenericFollowUp(previous_event->player_index, player2, SSCB_MARSHALL_KNOCKON);
				event_handler->StoreGenericFollowUp(previous_event->player_index, player2, SSCB_MARSHALL_ERROR);
			}
		}
		break;

	case SSCB_NISBO_ADVANTAGE:
		{
			//grab the knock on event that would have triggered the advantage event.
			if(RUCBKnockOnEvent *previous_event = (RUCBKnockOnEvent*) context_bucket->GetLastEvent(SSCB_EVENT_KNOCK_ON))
			{
				//see if we can grab the previous tackle event
				int player2 = -1;
				if(RUCBTackleEvent *previous_tackle = (RUCBTackleEvent*) context_bucket->GetLastEvent(SSCB_EVENT_TACKLE))
				{
					player2 = previous_tackle->player2_index;
				}

				event_handler->StoreGenericFollowUp(previous_event->player_index, player2, SSCB_MARSHALL_KNOCKON);
			}
		}
		break;

	case SSCB_NISBO_OFFSIDE_ADVANTAGE:
		{
			RUCBGenericFollowUpEvent* offside_event = (RUCBGenericFollowUpEvent*)context_bucket->GetLastEvent( SSCB_NISBO_OFFSIDE_ADVANTAGE );
			if(offside_event)
			{
				event_handler->StoreGenericFollowUp(offside_event->player_index, offside_event->player2_index, SSCB_MARSHALL_PENALTY);
				event_handler->StoreGenericFollowUp(offside_event->player_index, offside_event->player2_index, SSCB_MARSHALL_PENALTY_FOLLOWUP);
			}
		}
		break;

	case SSCB_EVENT_FUMBLE:
		{
			if(RUCBFumbleEvent *previous_event = (RUCBFumbleEvent*) context_bucket->GetLastEvent(SSCB_EVENT_FUMBLE))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_MARSHALL_BALL_NOT_FORWARD);
			}
		}
		break;

	case SSCB_MARSHALL_BALL_NOT_FORWARD:
		{
			if(RUCBGenericFollowUpEvent *previous_event = MabCast<RUCBGenericFollowUpEvent>(context_bucket->GetLastEvent(SSCB_MARSHALL_BALL_NOT_FORWARD)))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_MARSHALL_HANDLING_ERROR_FOLLOW_UP);
			}
		}
		break;

	case SSCB_MARSHALL_KNOCKON:
		{
			if(RUCBGenericFollowUpEvent *previous_event = MabCast<RUCBGenericFollowUpEvent>(context_bucket->GetLastEvent(SSCB_MARSHALL_KNOCKON)))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_MARSHALL_HANDLING_ERROR_FOLLOW_UP);
			}
		}
		break;

	case SSCB_EVENT_RUCK_RESULT:
		{
			if(phrase.selected_phrase.phrase_group->group_id == "RUCK_TURNOVER")
			{
				if(RUCBRuckResultEvent *previous_event = (RUCBRuckResultEvent*) context_bucket->GetLastEvent(SSCB_EVENT_RUCK_RESULT))
				{
					event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_MARSHALL_TURNOVER);
				}
			}
		}
		break;

	case SSCB_EVENT_CATCH:
		{
			if(phrase.selected_phrase.phrase_group->group_id != "CATCH_ANTICIPATE")
			{
				if(RUCBCatchEvent *previous_event = (RUCBCatchEvent*) context_bucket->GetLastEvent(SSCB_EVENT_CATCH))
				{
					event_handler->StoreGenericFollowUp(previous_event->player_index, previous_event->player2_index, SSCB_MARSHALL_CATCH);
				}
			}
		}
		break;



	case SSCB_EVENT_INJURY:
		{
			if(RUCBInjuryEvent *previous_event = (RUCBInjuryEvent*) context_bucket->GetLastEvent(SSCB_EVENT_INJURY))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_MARSHALL_INJURY_FOLLOWUP);
			}
		}
		break;

	case SSCB_EVENT_PENALTY:
		{
			if(RUCBPenaltyEvent *previous_event = (RUCBPenaltyEvent*) context_bucket->GetLastEvent(SSCB_EVENT_PENALTY))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_MARSHALL_PENALTY);
			}
		}
		break;

	case SSCB_MARSHALL_PENALTY:
		{
			if(RUCBPenaltyEvent *previous_event = (RUCBPenaltyEvent*) context_bucket->GetLastEvent(SSCB_EVENT_PENALTY))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_MARSHALL_PENALTY_FOLLOWUP);
			}
		}
		break;

	case SSCB_EVENT_PENALTY_GOAL_KICK_DECIDED:
		{
			if(RUCBPenaltyGoalKickDecidedEvent *previous_event = (RUCBPenaltyGoalKickDecidedEvent*) context_bucket->GetLastEvent(SSCB_EVENT_PENALTY_GOAL_KICK_DECIDED))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_MARSHALL_PENALTYGOAL_DECIDE);
			}
		}
		break;

	case SSCB_EVENT_PASS:
		{
			if(phrase.selected_phrase.phrase_group->group_id == "PASS_FORWARD")
			{
				if(RUCBPassEvent *previous_event = (RUCBPassEvent*) context_bucket->GetLastEvent(SSCB_EVENT_PASS))
				{
					event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_MARSHALL_ERROR);
				}
			}
		}
		break;

	case SSCB_EVENT_REPLAY_TRY:
		{
			if(RUCBTryEvent *previous_event = (RUCBTryEvent*) context_bucket->GetLastEvent(SSCB_EVENT_TRY))
			{
				//event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_MARSHALL_TRY_ANALYSIS);
				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_NISBO_AGREEMENT);				
			}
		}
		break;

	case SSCB_EVENT_REPLAY:
		{
			SSCBCutsceneEvent *cutscene_event = MabCast< SSCBCutsceneEvent >(context_bucket->GetLastEvent(SSCB_EVENT_REPLAY));

			if(cutscene_event && cutscene_event->replay_type == RT_VIDEO_REF)
			{
// 				event_handler->StoreGenericFollowUp(-1, -1, SSCB_MARSHALL_REPLAY_FOLLOW_UP);
				event_handler->StoreGenericFollowUp(-1, -1, SSCB_NISBO_AGREEMENT);
			}
		}
		break;

// 	case SSCB_EVENT_TMO_DELIBERATION:
// 		{
// 			RUCBTelevisionMatchOfficialEvent *cutscene_event = MabCast< RUCBTelevisionMatchOfficialEvent >(context_bucket->GetLastEvent(SSCB_EVENT_TMO_DELIBERATION));
// 
// 			if(cutscene_event)
// 			{
// 				event_handler->StoreCutsceneEvent<SSCB_EVENT_REPLAY>(RT_VIDEO_REF);
// 			}
// 
// 		}
// 		break;

	case SSCB_EVENT_SCRUM_CALLED:
		{
			if(RUCBScrumCalledEvent *previous_event = (RUCBScrumCalledEvent *) context_bucket->GetLastEvent(SSCB_EVENT_SCRUM_CALLED))
			{
				if(RUTeam *team = game->GetTeamFromDB( previous_event->team_index))
				{
					if(ARugbyCharacter *player = team->GetPlayerByPosition(PP_SCRUM_HALF))
					{
						event_handler->StoreGenericFollowUp(player->GetAttributes()->GetDbId(),-1, SSCB_MARSHALL_SCRUM_CALLED);
					}
				}
			}
		}
		break;

	case SSCB_EVENT_SCRUM_BALL_IN:
		{
			if(RUCBScrumCalledEvent *previous_event = (RUCBScrumCalledEvent *) context_bucket->GetLastEvent(SSCB_EVENT_SCRUM_CALLED))
			{
				if(RUTeam *team = game->GetTeamFromDB( previous_event->team_index))
				{
					if(ARugbyCharacter *player = team->GetPlayerByPosition(PP_SCRUM_HALF))
					{
						event_handler->StoreGenericFollowUp(player->GetAttributes()->GetDbId(),-1, SSCB_EVENT_STOPPAGE_BANTER_START);
					}
				}
			}
		}
		break;

	case SSCB_EVENT_LINEOUT_READY:
		{
			if(RUCBLineoutReadyEvent *previous_event = (RUCBLineoutReadyEvent *) context_bucket->GetLastEvent(SSCB_EVENT_LINEOUT_READY))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_EVENT_STOPPAGE_BANTER_START);
			}
		}
		break;

	case SSCB_CONVERSION_TRANSITION:
		{
			if(RUCBKickAtPostsReadyEvent *previous_event = (RUCBKickAtPostsReadyEvent *) context_bucket->GetLastEvent(SSCB_CONVERSION_TRANSITION))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index,-1, SSCB_CONVERSION_TRANSITION_FOLLOW_UP);
			}
		}
		break;

	//give marshall a chance to respond to either nisbo's scrum result, or his picked up commentary.
	case SSCB_EVENT_PICKED_UP:
	case SSCB_EVENT_SCRUM_RESULT:
		{
			if(RUCBPickedUpEvent *picked_up_event = static_cast< RUCBPickedUpEvent *>(context_bucket->GetLastEvent(SSCB_EVENT_PICKED_UP)))
			{
				//see if this was picked up from a scrum
				if(picked_up_event->picked_up_context != PUC_FROM_SCRUM)
					break;

				if(RUCBScrumResultEvent *previous_event = static_cast< RUCBScrumResultEvent *>(context_bucket->GetLastEvent(SSCB_EVENT_SCRUM_RESULT)))
				{
					if(RUTeam *team = game->GetTeamFromDB( previous_event->team_index))
					{
						if(ARugbyCharacter *player = team->GetPlayerByPosition(PP_HOOKER))
						{
							//allow marshall to respond to nisbo's ball picked up response
							event_handler->StoreGenericFollowUp(player->GetAttributes()->GetDbId(), -1, SSCB_MARSHALL_SCRUM_RESULT);
						}
					}
				}
			}
		}
		break;

	case SSCB_EVENT_KICK:
		{
			if(RUCBKickEvent *previous_event = (RUCBKickEvent *) context_bucket->GetLastEvent(SSCB_EVENT_KICK))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index, previous_event->player2_index, SSCB_MARSHALL_KICK);
			}
		}
		break;

	case SSCB_EVENT_KICK_TERRITORY:
		{
			if(RUCBTerritoryKickEvent *previous_event = (RUCBTerritoryKickEvent *) context_bucket->GetLastEvent(SSCB_EVENT_KICK_TERRITORY))
			{
				RUCBKickEvent* kick_event = (RUCBKickEvent*)context_bucket->GetLastEvent( SSCB_EVENT_KICK );
				if(!kick_event)
					return;

				//don't do a territory kick response for penalty kicks for touch.
				if(kick_event->kick_type != KC_FREEPLAY
					&& kick_event->kick_type != KC_FREEBALL_KICK)
					return;

				event_handler->StoreGenericFollowUp(previous_event->player_index, -1, SSCB_MARSHALL_TERRITORY_KICK);
			}
		}
		break;

	case SSCB_EVENT_INTERCHANGE_MADE:
		{
			if(RUCBInterchangeEvent *previous_event = MabCast<RUCBInterchangeEvent>(context_bucket->GetLastEvent(SSCB_EVENT_INTERCHANGE_MADE)))
			{
				event_handler->StoreGenericFollowUp(previous_event->player_index, previous_event->player2_index, SSCB_MARSHALL_INTERCHANGE_FOLLOWUP);
			}
		}
		break;

	}
}

///----------------------------------------------------------
/// Should the stadium pan be delayed to allow pre-haka commentary to finish?
///----------------------------------------------------------

bool RUCommentary::ShouldHoldStadiumPan(bool player_wishes_to_skip)
{
	if(player_wishes_to_skip)
	{
		phrase_renderer->Stop(0.5f);

		if(pre_mid_post_director->GetPreMatchPhase() < RUPMPDirector::HAKA_PENDING)
			pre_mid_post_director->ProgressPreMatch(RUPMPDirector::HAKA_PENDING);
	}

	return phrase_renderer->GetState() != RUPhraseRenderer::SPEECH_FREE;
}

bool RUCommentary::HaveCommentaryForLastName( const MabString &last_name)
{
	if(!commentator_nisbo)
	{
		MABBREAK();
		return false;
	}


	if (commentator_nisbo->inserts->GetInsertFSB("player", MabString("|") + last_name, INSERT_ID_MATCH_END) < 0)
	{
		return commentator_nisbo->inserts->GetInsertFSB("player", MabString("|") + last_name, INSERT_ID_MATCH_END, INSERT_BEGINNING) >= 0;
	}

	return true;
}

void RUCommentary::CutsceneEvent( CommentaryCutsceneEvent cutscene_event)
{
#ifndef DISABLE_FMOD
	if(cutscene_event == CCE_HAKA_FINISHED)
		pre_mid_post_director->ProgressPreMatch(RUPMPDirector::HAKA_ENDING);
#endif
}

void RUCommentary::UpdateAmbientClassifications()
{
	SSCommentaryClassification *play_stoppage_rule_classification = classification_associations->GetClassification(CCT_PLAY_STOPPAGE_RULE);
	SSCommentaryClassification *in_game_classification = classification_associations->GetClassification(CCT_AMBIENT_IN_GAME);
	SSCommentaryClassification *pmp_classification = classification_associations->GetClassification(CCT_AMBIENT_PMP);

	//determine if this is a gameplay event that occurs after a game ending rule consequence is about
	//to fire.
	RURuleTrigger *rule_trigger = game->GetRules()->GetActiveTrigger();
	if(!rule_trigger)
		rule_trigger = game->GetRules()->GetPendingTrigger();

	if(rule_trigger && rule_trigger->GetType() & 
		(RURT_BALLFORCED |
		RURT_BALLOUT |
		RURT_DEADBALL |
		RURT_DROPKICK |
		RURT_FORWARDPASS |
		RURT_HALFOVER |
		RURT_PENALTY |
		RURT_TRYHELDUP |
		RURT_TRYSUCCESS |
		RURT_PENALTY_GOAL |
		RURT_OFFSIDE |
		RURT_MAULHELDUP)
		)
	{
		//looks like we have a rule active that that will end regular gameplay, lets
		//set up an environmental classification that will guard against gameplay related
		//classifications
		commentator_marshall->phrase_selector->AddAmbientClassification(play_stoppage_rule_classification);
		commentator_nisbo->phrase_selector->AddAmbientClassification(play_stoppage_rule_classification);
	}
	else
	{
		commentator_marshall->phrase_selector->RemoveAmbientClassification(play_stoppage_rule_classification);
		commentator_nisbo->phrase_selector->RemoveAmbientClassification(play_stoppage_rule_classification);
	}

	if(game->GetGameTimer()->GetHalfObserver()->IsHalfEnded())
	{
		commentator_marshall->phrase_selector->AddAmbientClassification(pmp_classification);
		commentator_nisbo->phrase_selector->AddAmbientClassification(pmp_classification);

		commentator_marshall->phrase_selector->RemoveAmbientClassification(in_game_classification);
		commentator_nisbo->phrase_selector->RemoveAmbientClassification(in_game_classification);
	}
	else
	{

		//determine if we are in-game or in the pre mid post period.
		switch(game->GetGameState()->GetPhase())
		{
		case PRE_GAME:
		case HALF_TIME:
		case POST_GAME:
			{
				commentator_marshall->phrase_selector->AddAmbientClassification(pmp_classification);
				commentator_nisbo->phrase_selector->AddAmbientClassification(pmp_classification);

				commentator_marshall->phrase_selector->RemoveAmbientClassification(in_game_classification);
				commentator_nisbo->phrase_selector->RemoveAmbientClassification(in_game_classification);
			}
			break;

		default:
			{
				commentator_marshall->phrase_selector->AddAmbientClassification(in_game_classification);
				commentator_nisbo->phrase_selector->AddAmbientClassification(in_game_classification);

				commentator_marshall->phrase_selector->RemoveAmbientClassification(pmp_classification);
				commentator_nisbo->phrase_selector->RemoveAmbientClassification(pmp_classification);
			}
		}
	}
}

void RUCommentary::DebugPlayerName(int player_db_id)
{
#ifdef COMMENTARY_NAME_DEBUG

	InsertPosition posList[] = 
	{
		INSERT_BEGINNING,
		INSERT_MIDDLE
	};int posCount = 2;

	SSExcitementLevel levelList[] = 
	{
		SS_EXCITEMENT_LOW,
		SS_EXCITEMENT_MEDIUM,
		SS_EXCITEMENT_HIGH
	};int levelCount = 3;

	MabVector<int> nameList;
	RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();
	RL3DB_PLAYER player = database_manager->GetRL3Database()->GetPlayer(player_db_id);

// 	MABLOGDEBUG("=============================================\n");
// 	MABLOGDEBUG("%s\n", player.GetName().c_str());

	for (int j = 0; j < levelCount; j++)
	{
		for (int i = 0; i < posCount; i++)
		{
			int player_fsb = -1;
		
			if(player.GetIsCustom())
			{
				//custom players need to use their commentary name
// 				MABLOGDEBUG("CUSTOM NAME: %s\n", player.GetName().c_str());
				player_fsb = commentator_nisbo->inserts->GetInsertFSB("player", MabString("|") + player.GetName(), INSERT_ID_MATCH_END, posList[i], levelList[j]);
			}
			else
			{
				MabString player_first_name = player.GetFirstName();
				MabString player_last_name = player.GetLastName();
		
				//not a custom player, search for a full name
				player_fsb = commentator_nisbo->inserts->GetInsertFSB("player", player_first_name + "|" + player_last_name, INSERT_ID_MATCH_FULL, posList[i], levelList[j]);

				if (player_fsb > 0)
				{
// 					MABLOGDEBUG("FULL NAME: %d\n", player_fsb);
					nameList.push_back(player_fsb);
					player_fsb = -1;
				}

				//we don't have an FSB for the players full name, try to find a last name
				player_fsb = commentator_nisbo->inserts->GetInsertFSB("player", MabString("|") + player_last_name, INSERT_ID_MATCH_END, posList[i], levelList[j]);
			}
		
			if (player_fsb > 0)
			{
// 				MABLOGDEBUG("LAST NAME: %d\n", player_fsb);
				nameList.push_back(player_fsb);
			}
		}
	}

// 	MABUNUSED( player_db_id );
// 	nameList.push_back(9064);
// 	nameList.push_back(10472);
// 	nameList.push_back(10473);
// 	nameList.push_back(10521);
// 	nameList.push_back(10522);
// 	nameList.push_back(11930);
// 	nameList.push_back(11931);
// 	nameList.push_back(12498);

	RUBoundPhrase bound_phrase;
	bound_phrase.Initialise(nameList, 0);


	RURenderablePhrase renderable_phrase;
	renderable_phrase.phrase = bound_phrase.bound_samples;

	renderable_phrase.wavebank = commentator_nisbo->wavebank;
	renderable_phrase.debug_wavebank = commentator_nisbo->debug_wavebank;

	
	phrase_renderer->Play(renderable_phrase, commentator_nisbo->volume);
#else
	MABUNUSED( player_db_id );
#endif
}

void RUCommentary::CleanupLuaInterpreter()
{
	if (commentary_interpreter)
	{
		commentary_interpreter->Cleanup();
	}
}

void RUCommentary::PostLoadDataSetup()
{
#ifndef DISABLE_AUDIO_COMMENTARY
	// HACK: Remove commentary containing inappropriate language, or references to unlicensed material

	//team_info_england_002 - mentions real player on non licenced team
	SSPhraseGroup* tmp_phrase_grp = commentator_nisbo->phrase_selector->GetPhraseGroup("TEAM_INFO_ENGLAND");
	if(tmp_phrase_grp) tmp_phrase_grp->RemovePhrase(1);
	
	//nz_v_eng_generic_002 - mentions trophy we dont award
	tmp_phrase_grp = commentator_nisbo->phrase_selector->GetPhraseGroup("NZ_v_ENG_GENERIC");
	if(tmp_phrase_grp) tmp_phrase_grp->RemovePhrase(1);

	//nz_v_fra_generic_001 - mentions trophy we dont award
	tmp_phrase_grp = commentator_nisbo->phrase_selector->GetPhraseGroup("NZ_v_FRA_GENERIC");
	if(tmp_phrase_grp) tmp_phrase_grp->RemovePhrase(0);


	//add some filters to the phrase selector
	//more or less add them by order of efficiency, to cull the numbers before the more expensive filters.
	
	//add a filter that stops phrases being repeated back to back (or near to).
	commentator_nisbo->phrase_selector->AddSelectionFilter( MabMemNew( heap ) RUPhraseFilterPhraseRepeat(&last_phrase, &current_phrase));
	commentator_marshall->phrase_selector->AddSelectionFilter( MabMemNew( heap ) RUPhraseFilterPhraseRepeat(&last_phrase, &current_phrase));

	//add a filter that ensures consistent references, and avoids unnecessary repeats of player and team names.
	commentator_nisbo->phrase_selector->AddSelectionFilter( MabMemNew( heap ) RUPhraseFilterArticles(phrase_binder, currently_referenced_articles));
	commentator_marshall->phrase_selector->AddSelectionFilter( MabMemNew( heap ) RUPhraseFilterArticles(phrase_binder, currently_referenced_articles));

	//Filter based on excitement
	//commentator_nisbo->phrase_selector->AddSelectionFilter( MabMemNew( heap ) RUPhraseFilterExcitement());
	//commentator_marshall->phrase_selector->AddSelectionFilter( MabMemNew( heap ) RUPhraseFilterExcitement());

	//THESE FILTERS MUST COME LAST/////////////////////////////////////////////////////
	commentator_nisbo->phrase_selector->AddSelectionFilter( MabMemNew( MMHEAP_PERMANENT_DATA ) RUPhraseFilterPlayedCount());
	commentator_marshall->phrase_selector->AddSelectionFilter( MabMemNew( MMHEAP_PERMANENT_DATA ) RUPhraseFilterPlayedCount());
	///////////////////////////////////////////////////////////////////////////////////
#endif
}

