// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIPopulatorCustomiseEditPlayers.h"

#include "Rugby/RugbyGameInstance.h"

// RC3 Stuff
#include "Match/RugbyUnion/RUDBPlayer.h"
#include "Match/RugbyUnion/RUDBTeam.h"
#include "Databases/RUGameDatabaseManager.h"
#include "Match/RugbyUnion/RUDatabaseConstants.h"
#include "Match/SIFUIConstants.h"

// Unreal Widgets
#include "ScrollBox.h"
#include "HorizontalBox.h"
#include "TextBlock.h"

// WW Widgets
#include "WWUIListField.h"
#include "WWUITranslationManager.h"
#include "WWUIPopulatorCustomiseSearch.h"
#include "Utility/Helpers/SIFGameHelpers.h"

const int MAX_PLAYERS_R7 = DB_NUM_PLAYERS_PER_TEAM_R7;
const int MAX_PLAYERS_R15 = DB_NUM_PLAYERS_PER_TEAM;
const int MAX_PLAYERS = 80;

bool player_sort_name(RUDB_PLAYER& player1, RUDB_PLAYER& player2)
{
	int sort = strcmp(MabStringHelper::ToLower(player1.GetLastName()).c_str(), MabStringHelper::ToLower(player2.GetLastName()).c_str());
	if (sort == 0)
		sort = strcmp(player1.GetFirstName(), player2.GetFirstName());

	return sort < 0;
}

void UWWUIPopulatorCustomiseEditPlayers::Populate(UWidget* widget)
{
	if (inPreConstruct)
	{
		Clear(widget);
	}

	int32 ItemCount = dataList.ArrayOption.Num();

	preConstructOwner = widget;

	if (!inPreConstruct)
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		if (!pRugbyGameInstance)
		{
			return;
		}

		RUGameDatabaseManager* database_manager = pRugbyGameInstance->GetGameDatabaseManager();

		if (!database_manager)
		{
			return;
		}

		MabVector<RUDB_PLAYER> player_list(0);

		if (bUseCustomOnly)
		{
			MabVector<unsigned short> player_id_list(0);
			if (!database_manager->LoadIdListConditional< RUDB_PLAYER >("custom", 1, player_id_list))
			{
				MABBREAKMSG("LoadIdListConditional failed based on the custom field!");
			}

			// If in delete 'mode', only allow deletion of players that where 'created' by user. (db_id >= MINIMUM_CREATED_PLAYER_DB_ID)
			if (bIsDelete)
			{
				for (unsigned int i = 0; i < player_id_list.size(); i++)
				{
					if (player_id_list[i] < MINIMUM_CREATED_PLAYER_DB_ID)
					{
						player_id_list.erase(player_id_list.begin() + i);
						i--;
					}
				}
			}

			player_list.resize(player_id_list.size());
			database_manager->LoadAllData(player_id_list, player_list);
		}
		else
		{
			if (TeamID == 0)
			{
				MabVector<unsigned short> player_id_list(SIFHEAP_DYNAMIC);
				if (!database_manager->LoadIdListConditional< RUDB_PLAYER >("custom", 1, player_id_list))
				{
					MABBREAKMSG("LoadIdListConditional failed based on the custom field!");
				}

				//bIsDelete = true;
				// If in delete 'mode', only allow deletion of players that where 'created' by user. (db_id >= MINIMUM_CREATED_PLAYER_DB_ID)
				//if (bIsDelete)
				{
					for (unsigned int i = 0; i < player_id_list.size(); i++)
					{
						if (player_id_list[i] < MINIMUM_CREATED_PLAYER_DB_ID)
						{
							player_id_list.erase(player_id_list.begin() + i);
							i--;
						}
					}
				}

				player_list.resize(player_id_list.size());
				database_manager->LoadAllData(player_id_list, player_list);
			}
			else
			{
				// load the team to get the lineup
				RUDB_TEAM team;
				database_manager->LoadData(team, TeamID);

				// load the players
				player_list.resize(team.GetNumLineups());
				for (size_t i = 0; i < player_list.size(); ++i)
				{
					database_manager->LoadData(player_list[i], team.GetLineup((int)i).player_id);
				}
			}
		}

		std::sort(player_list.begin(), player_list.end(), player_sort_name);

		UnrealPlayerList.Empty(player_list.size());

		for (unsigned int i = 0; i < player_list.size(); i++)
		{
			UnrealPlayerList.Add(player_list[i]);
		}

		ItemCount = TeamID == 0 ? UnrealPlayerList.Num() : MAX_PLAYERS_R15;
	}

	CustomiseEditPlayerCreationNodeCallback callbackObject(widget);

	UScrollBox* pContainer = Cast<UScrollBox>(widget);

	ResizeNodeCountFromTemplate(dataList.TemplateName, ItemCount, &callbackObject, pContainer);

	if (!inPreConstruct)
	{
		SelectDefaultPlayer(widget);
	}
}

void UWWUIPopulatorCustomiseEditPlayers::Refresh(UWidget* widget)
{
	RUDB_TEAM team;

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (!pRugbyGameInstance)
	{
		return;
	}

	RUGameDatabaseManager* database_manager = pRugbyGameInstance->GetGameDatabaseManager();

	if (!database_manager)
	{
		return;
	}

	if (TeamID != 0)
	{
		database_manager->LoadData(team, TeamID);
	}

	// Checks if the team this player belongs to is an R7 team or not
	bool isR15Team = !team.GetIsR7Exclusive();//GAIsTeamR13(team_id);

	UScrollBox* pScrollBox = Cast<UScrollBox>(widget);

	if (!pScrollBox)
	{
		return;
	}

	int num = pScrollBox->GetChildrenCount();

	for (int i = 0; i < num; i++)
	{
		UWWUIListField* pListField = Cast<UWWUIListField>(pScrollBox->GetChildAt(i));

		if (pListField)
		{
			pListField->SetVisibility(ESlateVisibility::Visible);

			UHorizontalBox* pHorizontalBox = Cast<UHorizontalBox>(ScreenRef->FindChildOfTemplateWidget(pListField, "HorizontalBoxText"));
			if (pHorizontalBox)
			{
				if (i < UnrealPlayerList.Num())
				{
					int32 PositionIndex = 0;

					for (int j = 0; j < pHorizontalBox->GetChildrenCount(); j++)
					{
						ECustomiseEditPlayerFieldItem CurrentItem = (ECustomiseEditPlayerFieldItem)j;

						UTextBlock* pTextBlock = Cast<UTextBlock>(pHorizontalBox->GetChildAt(j));

						if (!pTextBlock && j == (int32)ECustomiseEditPlayerFieldItem::NAME)
						{
							pTextBlock = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(pHorizontalBox, "TextName"));
						}

						FString ItemText = "";

						switch (CurrentItem)
						{
						case ECustomiseEditPlayerFieldItem::SECOND_POSITION:
						{
							PositionIndex = 1;
						}
						// FALLS THROUGH
						case ECustomiseEditPlayerFieldItem::POSITION:
						{
							// MULTI_POSITION_CATEGORY_CHANGE
							//player_info.primary_position<=PP_FULLBACK ? PlayerPositionEnum::GetPlayerPositionTextAbbreviated(  player_info.primary_position ) : "- -"
							PLAYER_POSITION primary_position =  // Nick WWS &s to Womens 13s // (isR15Team && UnrealPlayerList[i].gender == PLAYER_GENDER_MALE) ? 
								UnrealPlayerList[i].GetPositionCategoryR13(PositionIndex);
							// Nick WWS &s to Womens 13s //: UnrealPlayerList[i].GetPositionCategoryR7(PositionIndex);
							ItemText = primary_position <= PP_FULLBACK ? PlayerPositionEnum::GetPlayerPositionTextAbbreviated(primary_position) : "- -";
							ItemText = UWWUITranslationManager::Translate(ItemText);
						}
						break;
						case ECustomiseEditPlayerFieldItem::NAME:
						{
							if(UnrealPlayerList[i].IsCustom() && ((SIFApplication::GetApplication()->IsAnyUserRestricted() && SIFGameHelpers::GAGetPlayerDownloadUser(UnrealPlayerList[i].GetDbId()) != "") || SIFApplication::GetApplication()->IsNonPrimaryUserRestricted()))
							{
								ItemText = UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper();
							}
							else
							{
								ItemText = UTF8_TO_TCHAR(UnrealPlayerList[i].GetLastName()) + FString(", ") + UTF8_TO_TCHAR(UnrealPlayerList[i].GetFirstName());
								REMOVE_UNUSED_CHARACTER(ItemText);
							}
						}
						break;
						case ECustomiseEditPlayerFieldItem::AGE:
						{
							ItemText = FString::FromInt(UnrealPlayerList[i].GetAge());
						}
						break;
						case ECustomiseEditPlayerFieldItem::RATING:
						{
							ItemText = FString::FromInt(UnrealPlayerList[i].GetOverallRating());
						}
						break;
						}

						if (pTextBlock)
						{
							pTextBlock->SetText(FText::FromString(ItemText).ToUpper());
						}
					}

					int32 DbID = UnrealPlayerList[i].GetDbId();
					pListField->SetProperty("player_id", &DbID, PROPERTY_TYPE_INT);
					DbID += 1;
					pListField->SetProperty("player_index", &DbID, PROPERTY_TYPE_INT);
				}
				else
				{
					for (int j = 0; j < pHorizontalBox->GetChildrenCount(); j++)
					{
						ECustomiseEditPlayerFieldItem CurrentItem = (ECustomiseEditPlayerFieldItem)i;

						UTextBlock* pTextBlock = Cast<UTextBlock>(pHorizontalBox->GetChildAt(j));

						int32 DbID = 0;
						pListField->SetProperty("player_id", &DbID, PROPERTY_TYPE_INT);

						pListField->SetVisibility(ESlateVisibility::Collapsed);
					}
				}
			}
		}
	}
}


void UWWUIPopulatorCustomiseEditPlayers::SelectDefaultPlayer(UWidget* node)
{
	// We'll convert the ID to a string, because that's the format all the team nodes will be holding it as.
	bool id_found = false;

	UScrollBox* pScrollBox = Cast<UScrollBox>(node);

	if (pScrollBox)
	{
		// We will need to search through all the competitions and check which one holds the team.
		for (int32 i = 0; i < pScrollBox->GetChildrenCount() && !id_found; ++i)
		{
			UWWUIListField* current_player_node = Cast<UWWUIListField>(pScrollBox->GetChildAt(i));

			if (current_player_node)
			{
				const int32 compare_id = current_player_node->GetIntProperty("player_id");

				if (compare_id == LastSelection)
				{
					// Old system selected the last node here, we will just set it and retrieve it after populating. -JG
					id_found = true;
					break;
				}
			}
		}
	}

	if (!id_found)
	{
		LastSelection = INVALID_LAST_SELECTION;
	}
}

UWWUIPopulatorCustomiseEditPlayers::CustomiseEditPlayerCreationNodeCallback::CustomiseEditPlayerCreationNodeCallback(UWidget* containerToPopulate) :
	container()
{
	container = Cast<UScrollBox>(containerToPopulate);

	if (!container)
	{
		FString errorString = containerToPopulate != nullptr ? *containerToPopulate->GetPathName() : FString("NULL");
		UE_LOG(LogTemp, Error, TEXT("Cast to scroll box failed while attempting DataFileCreationNodeCallback on node %s"), *errorString);
	}
}

void UWWUIPopulatorCustomiseEditPlayers::CustomiseEditPlayerCreationNodeCallback::Callback(UUserWidget* widget)
{
	container->AddChild(widget);
}

