/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/SSRoleFactory.h"
//#rc3_legacy_include #include <MabInstancer.h>

// Role includes
#include "Match/SSRoleNull.h"
#include "Match/AI/Roles/Competitors/RURoleGetTheBall.h"
#include "Match/AI/Roles/Competitors/RURoleMarkDefend.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuckDefend.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuckSendRunner.h"
#include "Match/AI/Roles/Competitors/RURoleSupport.h"
#include "Match/AI/Roles/Competitors/RURoleStandardBallHolder.h"
#include "Match/AI/Roles/Competitors/RURoleKickOffKicker.h"
#include "Match/AI/Roles/Deprecated/RURoleLazySupport.h"
#include "Match/AI/Roles/Deprecated/RURoleGenericRetire.h"
#include "Match/AI/Roles/Competitors/SSRoleFormation.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleLineOut.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleLineOutThrower.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleLineOutReceiver.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuck.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuckScrumHalf.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleMaul.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleMaulHalfback.h"
#include "Match/AI/Roles/Competitors/RURoleShootForGoal.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleScrum.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleScrumHalfBack.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBall.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBallReceiver.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBallDefender.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBallSecondDefender.h"
#include "Match/AI/Roles/Officials/SSRoleReferee.h"
#include "Match/AI/Roles/Officials/SSRoleTouchJudge.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleTutorial.h"
#include "Match/AI/Roles/Competitors/RURolePenaltyAttack.h"
#include "Match/AI/Roles/Competitors/RURolePenaltyDefence.h"
#include "Match/AI/Roles/Competitors/RURoleTapRestart.h"
#include "Match/AI/Roles/Competitors/RURoleWingDefend.h"
#include "Match/AI/Roles/Competitors/RURoleFullback.h"
#include "Match/AI/Roles/Competitors/SSRoleCutScene.h"
#include "Match/AI/Roles/Competitors/RURoleTryReaction.h"
#include "Match/AI/Roles/Competitors/SSRoleReplay.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleSetplay.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleSetplayScrumHalf.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleSetplayPlayTheBallReceiver.h"
#include "Match/AI/Roles/Competitors/RURoleKickOffChaser.h"
#include "Match/AI/Roles/Competitors/RURoleCutSceneReaction.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleQuickLineOutThrower.h"

/**
 * Default Instancer for Roles
 *
 * <AUTHOR> Weehuizen.
 */

template< class InstantiateType >
struct DefaultRoleInstancer
{
	/// does nothing if the target_object is not NULL
	static SSRole* Instantiate( SIFGameWorld* game )
	{
		return MabMemNew(heap) InstantiateType(game);
	}
};

#define ADDDEFAULTINSTANCER( InstantiateClass ) \
	role_meta[InstantiateClass::RTTGetStaticType()] = RoleInfo(InstantiateClass::RTTGetStaticClassName(), &DefaultRoleInstancer<InstantiateClass>::Instantiate, &InstantiateClass::GetFitness);

SSRoleFactory::SSRoleFactory(SIFGameWorld* _game) : game(_game)
{
	ADDDEFAULTINSTANCER( SSRoleNull );
	ADDDEFAULTINSTANCER( SSRoleFormation );
	ADDDEFAULTINSTANCER( SSRoleCutScene );
	ADDDEFAULTINSTANCER( RURoleKickOffKicker );
	ADDDEFAULTINSTANCER( RURoleGenericRetire );
	ADDDEFAULTINSTANCER( RURoleGetTheBall );
	ADDDEFAULTINSTANCER( RURoleStandardBallHolder );
	ADDDEFAULTINSTANCER( RURoleSupport );
	ADDDEFAULTINSTANCER( RURoleLazySupport );
	ADDDEFAULTINSTANCER( RURoleMarkDefend );
	ADDDEFAULTINSTANCER( RURoleRuckDefend );
	ADDDEFAULTINSTANCER( RURoleRuckSendRunner );
	ADDDEFAULTINSTANCER( RURoleLineOutThrower );
	ADDDEFAULTINSTANCER( RURoleLineOut );
	ADDDEFAULTINSTANCER( RURoleLineOutReceiver );
	ADDDEFAULTINSTANCER( RURoleRuck );
	ADDDEFAULTINSTANCER( RURoleRuckScrumHalf );
	ADDDEFAULTINSTANCER( RURoleShootForGoal );
	ADDDEFAULTINSTANCER( RURoleScrum );
	ADDDEFAULTINSTANCER( RURoleScrumHalfBack );
	ADDDEFAULTINSTANCER( SSRoleReferee );
	ADDDEFAULTINSTANCER( SSRoleTouchJudge );
	ADDDEFAULTINSTANCER( RURoleTutorial );
	ADDDEFAULTINSTANCER( RURolePenaltyAttack );
	ADDDEFAULTINSTANCER( RURolePenaltyDefence );
	ADDDEFAULTINSTANCER( RURoleTapRestart );
	ADDDEFAULTINSTANCER( RURoleFullback );
	ADDDEFAULTINSTANCER( RURoleRightWingDefend );
	ADDDEFAULTINSTANCER( RURoleLeftWingDefend );
	ADDDEFAULTINSTANCER( RURoleTryReaction );
	ADDDEFAULTINSTANCER( SSRoleReplay );
	ADDDEFAULTINSTANCER( RURoleKickOffChaser );
	ADDDEFAULTINSTANCER( RURoleCutsceneReaction );
	ADDDEFAULTINSTANCER( RURoleMaul );
	ADDDEFAULTINSTANCER( RURoleMaulHalfback );
	ADDDEFAULTINSTANCER( RURoleQuickLineOutThrower );
	ADDDEFAULTINSTANCER( RURoleSetplay );
	ADDDEFAULTINSTANCER( RURoleSetplayScrumHalf );
	ADDDEFAULTINSTANCER( RURoleSetplayPlayTheBallReceiver );
	ADDDEFAULTINSTANCER( RURolePlayTheBall );
	ADDDEFAULTINSTANCER( RURolePlayTheBallReceiver );
	ADDDEFAULTINSTANCER( RURolePlayTheBallDefender );
	ADDDEFAULTINSTANCER( RURolePlayTheBallSecondDefender );

}

SSRoleFactory::~SSRoleFactory()
{
	PurgeReleasedRoles();
}

SSRole* SSRoleFactory::Instance(MabTypeID type_id)
{
	const auto itr = role_meta.find(type_id);
	if (itr == role_meta.end())
		return nullptr;

	return itr->second.instance_function(game);
}

void SSRoleFactory::Release(SSRole* role)
{
	released_roles.push_back(role);
}

void SSRoleFactory::PurgeReleasedRoles()
{
	while (!released_roles.empty())
	{
		MabMemDelete(released_roles.back());
		released_roles.pop_back();
	}
}

SSRoleFactory::FitnessFunction SSRoleFactory::GetFitnessFunction(MabTypeID type_id) const
{
	const auto itr = role_meta.find(type_id);
	if (itr == role_meta.end())
		return nullptr;

	return itr->second.fitness_function;
}

const char* SSRoleFactory::GetRoleName(MabTypeID type_id) const
{
	const auto itr = role_meta.find(type_id);
	if (itr == role_meta.end())
		return nullptr;

	return itr->second.name;
}