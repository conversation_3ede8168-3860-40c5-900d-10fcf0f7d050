[Audio]
AudioDeviceModuleName=AudioMixerXAudio2
UnfocusedVolumeMultiplier=1.0

[/Script/Engine.GameEngine]
+NetDriverDefinitions=(DefName="GameNetDriver",DriverClassName="OnlineSubsystemSteam.SteamNetDriver",DriverClassNameFallback="OnlineSubsystemUtils.IpNetDriver")

[/Script/OnlineSubsystemSteam.SteamNetDriver]
NetConnectionClassName="OnlineSubsystemSteam.SteamNetConnection"
MaxClientRate=150000
MaxInternetClientRate=100000

[/Script/Engine.GameSession]
bRequiresPushToTalk=false

[/Script/Engine.AudioSettings]
VoiPSampleRate=24000
MaximumConcurrentStreams=16
VoiPSoundClass=/Game/Rugby/Core/SoundClasses/VOIP.VOIP

[OnlineSubsystemSteam]
bEnabled=true
SteamDevAppId=3770100
SteamAppId=3770100
Achievement_0_Id="ACH_BEAT_BARRETT_RUNNING"
Achievement_1_Id="ACH_BEAT_POLLARD_KICKING"
Achievement_2_Id="ACH_BEAT_FARRELL_GOAL_KICKING"
Achievement_3_Id="ACH_BREAK_TACKLE_WITH_MTAWARIRA"
Achievement_4_Id="ACH_CROSS_FIELD_KICK"
Achievement_5_Id="ACH_N_SIDESTEPS"
Achievement_6_Id="ACH_BREAK_N_TACKLES"
Achievement_7_Id="ACH_N_OFFLOADS"
Achievement_8_Id="ACH_GAME_WINNING_DROPGOAL"
Achievement_9_Id="ACH_SCORE_TRY_BEFORE_N_MINUTES_R7"
Achievement_10_Id="ACH_CHANGE_AFTER_60_MINUTES"
Achievement_11_Id="ACH_COMEBACK"
Achievement_12_Id="ACH_SUCCESS_KICK_AFTER_MISSED_KICKS"
Achievement_13_Id="ACH_COMPETITION_WIN_RATE_1"
Achievement_14_Id="ACH_COMPETITION_WIN_RATE_2"
Achievement_15_Id="ACH_COMPETITION_WIN_RATE_3"
Achievement_16_Id="ACH_LINEOUT_PERCENTAGE"
Achievement_17_Id="ACH_TERRITORY_PERCENTAGE"
Achievement_18_Id="ACH_INTERCEPT_BALL"
Achievement_19_Id="ACH_LONG_PASS"
Achievement_20_Id="ACH_USE_ALL_SET_PLAYS"
Achievement_21_Id="ACH_USE_ALL_MECHANICS"
Achievement_22_Id="ACH_INJURED_FROM_HEAVY_TACKLE"
Achievement_23_Id="ACH_USE_ALL_SUBSTITUTIONS"
Achievement_24_Id="ACH_ANY_KICK_GOAL_BOUNCES_OFF_POSTS_AND_SCORES"
Achievement_25_Id="ACH_TRY_STARTING_FROM_OWN_GOAL"
Achievement_26_Id="ACH_CREATED_PLAYER_COACH_N_YEARS_1"
Achievement_27_Id="ACH_CREATED_PLAYER_COACH_N_YEARS_2"
Achievement_28_Id="ACH_CREATED_PLAYER_COACH_N_YEARS_3"
Achievement_29_Id="ACH_CREATED_PLAYER_COACH_N_YEARS_4"
Achievement_30_Id="ACH_CREATED_PLAYER_COACH_N_YEARS_5"
Achievement_31_Id="ACH_TOP_TRY_SCORER"
Achievement_32_Id="ACH_TOP_POINT_SCORER"
Achievement_33_Id="ACH_TOP_GOAL_KICKER"
Achievement_34_Id="ACH_PRO_PROMOTION_CAPTAIN"
Achievement_35_Id="ACH_PRO_PROMOTION_KICKER"
Achievement_36_Id="ACH_CAREER_HIRE_PLAYER_RATED_AT_LEAST_N"
Achievement_37_Id="ACH_CUSTOM_TEAM_RATED_AT_LEAST_N"
Achievement_38_Id="ACH_CREATE_PRO_PLAYER"
Achievement_39_Id="ACH_WIN_MATCH_PLAYING_AS_N_INTERNATIONAL_TEAMS"
Achievement_40_Id="ACH_WIN_TOURNAMENT_1"
Achievement_41_Id="ACH_WIN_TOURNAMENT_2"
Achievement_42_Id="ACH_WIN_TOURNAMENT_3"
Achievement_43_Id="ACH_WIN_TOURNAMENT_4"
Achievement_44_Id="ACH_WIN_TOURNAMENT_5"
Achievement_45_Id="ACH_WIN_TOURNAMENT_6"
Achievement_46_Id="ACH_WIN_TOURNAMENT_7"
Achievement_47_Id="ACH_WIN_TOURNAMENT_8"
Achievement_48_Id="ACH_WIN_ALL_MATCHES_IN_COMPETITION_1"
Achievement_49_Id="ACH_GOLD_IN_ALL_TUTORIALS"
Achievement_50_Id="ACH_CARD_AWARDED"

[OnlineSubsystem]
;DefaultPlatformService=Null
DefaultPlatformService=Steam
bHasVoiceEnabled=true

[Voice] 
bEnabled=true

;[GameNetDriver PacketHandlerProfileConfig]
+Components=OnlineSubsystemSteam.SteamAuthComponentModuleInterface

;[PendingNetDriver PacketHandlerProfileConfig]
+Components=OnlineSubsystemSteam.SteamAuthComponentModuleInterface