// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIScreenMultiplayerLobby.h"
#include "UI/GeneratedHeaders/WWUIScreenMultiplayerLobby_UI_Namespace.h"
#include "Button.h"
#include "Match/RugbyUnion/RUTypes.h"
#include "WidgetSwitcher.h"
#include "Character/RugbyPlayerState.h"
#include "GameFramework/GameStateBase.h"
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "UI/GeneratedHeaders/Animations_UI_Namespace.h"
#include "Kismet/KismetSystemLibrary.h"
#include "WWUIScreenOnlineSelectTeams.h"
#include "Utility/Helpers/SIFMatchmakingHelpers.h"
#include "Utility/Helpers/SIFInGameHelpers.h"
#include "Match/RugbyUnion/RUSandboxGame.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Utility/Helpers/SIFPlayerHelpers.h"
#include "GameModes/RugbyGameState.h"
#include "Character/RugbyPlayerController.h"
#include "WWUIScrollBox.h"
#include "UI/Populators/WWUIPopulatorOnlinePlayers.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/RUDBTeam.h"
#include "Databases/RUGameDatabaseManager.h"
#include "WWUITextBlock.h"
#include "Image.h"
#include "Networking/ConnectionManager.h"
#include "Utility/Helpers/SIFUIHelpers.h"
#include "WWUIListField.h"
#include "WidgetNavigation.h"
#include "WidgetTree.h"
#include "ScrollBox.h"
#include "WWUIRichTextBlockWithTranslate.h"
#include "Networking/VoipManager.h"
#include "Modals/WWUIModalWaiting.h"
#include "GameModes/RugbyGameModeBase.h"
#include "WWUIScreenPauseMenu.h"
#include "GameFramework/DefaultPawn.h"
#include "WWUIStateScreenModal.h"
#include "UI/WWUIVoipScreen.h"
#include "RugbyGameInstance.h"
#include "FlowNodes/FlowControlManager.h"
#include "FlowNodes/TrainingFlowNode.h"
#include "Mab/Net/MabNetworkManager.h"


#include "Match/HUD/RUHumanPlayerColours.h"

#if PLATFORM_PS4
#include "PS4Application.h"
// GGS Nick PS4 TODO Header files changed, compiles, need to check if it actually works...
//#include "PS4InputInterface.h"
#include "PS4Controllers.h"
#endif
#include "Match/PlayerProfile/SIFPlayerProfileConstants.h"
#include "Match/RugbyUnion/RUGameSettingsTeamSettings.h"

bool UWWUIScreenMultiplayerLobby::SelectingTeam = false;

#define TRIGGER_ACTIVE_VALUE	(0.25f)

#define SHOW_MUTE_LEGEND 1

#define MAX_HUMAN_PLAYERS_R7	(7)

void UWWUIScreenMultiplayerLobby::OnHostChecked(bool HostAlive)
{
	bCheckForMabNetworkReady = true;
}

void UWWUIScreenMultiplayerLobby::OnMabNetworkReady()
{
	//This should happen here so we know that we have loaded into a lobby, we should do a check for connections/remote role on player state so we can tell if we are host or a client
	SIFApplication::GetApplication()->DealMenuAction(SCREEN_POP_MENUSCREEN, Screens_UI::ModalWaiting);
	SIFApplication::GetApplication()->GetConnectionManager()->GotoState(ERugbyNetworkState::Connected);
	bProceedToTeamSelectIfLocalLeader = true;

	GameInstance->StartUserRestrictionCheck(EUserPrivileges::CanCommunicateOnline);
	GameInstance->StartUserRestrictionCheck(EUserPrivileges::CanUseUserGeneratedContent);

	ARugbyGameState* TheGameState = Cast< ARugbyGameState>(UGameplayStatics::GetGameState(GetWorld()));

	if (TheGameState)
	{
		TheGameState->StartVoipScreen();
		if (SIFApplication::GetApplication()->GetVoipManager())
		{
			SIFApplication::GetApplication()->GetVoipManager()->UnmuteAllTalkers();
		}


		TArray<AActor*> FoundActors;

		UGameplayStatics::GetAllActorsOfClass(SIFApplication::GetApplication()->GetWorld(), ADefaultPawn::StaticClass(), FoundActors);

		if (FoundActors.Num() > 0)
		{
			for (size_t i = 0; i < FoundActors.Num(); i++)
			{
				FoundActors[i]->ConditionalBeginDestroy();
			}
		}
	}

	// Setup the controllers for run around and then refresh our possesion.
	{
		SetupControllersForRunAround();

		// We just changed some playerIds on their states, so need to update the human players.
		RUSandboxGame* pSandboxGame = GameInstance->GetSandboxGame();

		if (pSandboxGame)
		{
			SIFGameWorld* pSandboxGameWorld = pSandboxGame->GetGameWorld();

			if (pSandboxGameWorld)
			{
				pSandboxGameWorld->SetupHumanPlayersForLoading(false);
			}
		}
	}
}

void UWWUIScreenMultiplayerLobby::ShowWaitingModal()
{
	//Show this just in case the network drops while we are loading in, once we know the host is alive we will hide this and continue on with the normal lobby flow OnHostChecked(true)
	UWWUIModalWaitingData* MData = NewObject< UWWUIModalWaitingData>();
	MData->HeaderString = "[ID_ONLINESESSION]";
	MData->BodyString = "[ID_SETTINGUPLOBBY]";
	MData->CloseOnBackButton = false;
	MData->CloseOnSelectButton = false;
	SIFApplication::GetApplication()->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::ModalWaiting, MData);
	SIFApplication::GetApplication()->DealMenuAction(SCREEN_POP_MENUSCREEN, Screens_UI::Loading);
}

void UWWUIScreenMultiplayerLobby::ShowTeamGenderChangeModal()
{
	//Unready
	for (ARugbyPlayerController* pc : GameInstance->GetLocalPlayerControllers())
	{
		if (ARugbyPlayerState* rugbyPlayerState = pc->GetPlayerState<ARugbyPlayerState>())
		{
			if (UOBJ_IS_VALID(pc))
			{
				if (UOBJ_IS_VALID(rugbyPlayerState))
				{
					if ((rugbyPlayerState->m_homeTeamLeader || rugbyPlayerState->m_awayTeamLeader))
					{
						pc->SetIsReadyState(false);
						break;
					}
				}
			}
		}
	}
	// Dismiss all error modals on screen before trying to add a new one.
	DismissAllErrorModalsOnScreen();
	//Show this when the client needs to update their team.
	UWWUIModalWarningMessageData* MData = NewObject< UWWUIModalWarningMessageData>();
	MData->WarningDialogue = "[ID_ONLINE_TEAM_GENDER_CHANGE]";
	MData->LegendString = "[ID_RU_UI_ACTION_ACCEPT_CONTINUE_LEGEND]";
	MData->OnSelectDelegate.BindUObject(this, &UWWUIScreenMultiplayerLobby::ProceedToTeamSelectOnModal);
	MData->CloseOnBackButton = false;
	SIFApplication::GetApplication()->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, MData);
}

void UWWUIScreenMultiplayerLobby::DismissAllErrorModalsOnScreen()
{
	UWWUIScreenManager* ScreenManager = SIFApplication::GetApplication()->GetUIScreenManager();
	ScreenManager->RemoveScreen(Screens_UI::WarningMessage);
}

void UWWUIScreenMultiplayerLobby::HandleNetworkTick(float deltaTime)
{
	if (GetWorld())
	{
		if (GetWorld()->GetNetMode() == ENetMode::NM_Client)
		{
			//We want to block here until we are sure the host is still active
			if (GameInstance->WaitingForHostCheck)
			{
				CurrentWaitTime += deltaTime;

				if (GameInstance->HostIsAlive && CurrentWaitTime > 5.0f)
				{
					OnHostChecked(true);
					GameInstance->WaitingForHostCheck = false;
					CurrentWaitTime = 0.0f;
				}
				else
				{
					//Dont really need to do much here because connection manager should catch this
					//We will be in connecting state until OnHostChecked() is called, connecting state has a timeout so it should just hold us here and kill the lobby if need be
				}
			}

			// Check the mab network to make sure all peers are connected.
			if (bCheckForMabNetworkReady)
			{
				URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

				if (pRugbyGameInstance)
				{
					MabNetworkManager* pMabNetworkManager = pRugbyGameInstance->GetNetworkManager();

					if (pMabNetworkManager)
					{
						bool bAllPeersConnected = true;

						for (int32 i = 0; i < pMabNetworkManager->GetNumPeers(); i++)
						{
							const MabNetworkManager::PeerInformation* pCurrentPeerInfo = pMabNetworkManager->GetPeerInformation(i);

							if (pCurrentPeerInfo)
							{
								if (pCurrentPeerInfo->state != MabNetworkManager::PNS_CONNECTED_NOTIFIED)
								{
									bAllPeersConnected = false;
								}
							}
						}

						if (bAllPeersConnected)
						{
							OnMabNetworkReady();
							bCheckForMabNetworkReady = false;
						}
					}
				}
			}
		}
	}
}

void UWWUIScreenMultiplayerLobby::Startup(UWWUIStateScreenData* InData)
{
	Super::Startup();

	// Storing focus for when this is pushed when disconnecting controllers
	StoreFocusForControllers();

	//	SetInputEnabled stops input to the screen, so this will be set to true on infocus if we are not going to the team select screen.
	SetInputEnabled(false);

	// Reset this flag for the invite manager.
	SIFApplication::GetApplication()->GetInviteManager()->SetHasInvite(false);

	ResetControllersForRunAround();

	SIFUIHelpers::DisableInGameInput(false);

	m_inviteObject.Init(Cast<ARugbyPlayerController>(GetOwningPlayer()));
	//< Get Game Instance. >
	GameInstance = Cast<URugbyGameInstance>(SIFApplication::GetApplication());
	if (!GameInstance) return;

	//	Make sure the VoipManager has Shutdown since the last time it was used.
#if (PLATFORM_SWITCH == 0 /*&& PLATFORM_WINDOWS == 0*/)
	if (GameInstance->GetVoipManager())
	{
		if (GameInstance->GetVoipManager()->IsVoipServiceSetup() || GameInstance->GetVoipManager()->IsVoipServiceEnabled())
		{
			GameInstance->GetVoipManager()->SetVoipServiceSetup(false);
			GameInstance->GetVoipManager()->ShutdownVoipService();
		}
	}
#endif

	//Clients will go into a holding state while they wait for it all to replicate over, the host will just show the lobby right away
	if (GetWorld())
	{
		//This will get our current data, we will update this after the match is over, this will also remove some reliability that we will regain if we complete the match 
		GameInstance->GetLeaderboardManager()->GetInitalDataForLobby(GameInstance->GetMatchmakingManager()->GetGametype() == GAME_MODE_RU13 ? true : false);
	
		GameInstance->OnNetworkRefresh.AddDynamic(this, &UWWUIScreenMultiplayerLobby::OnReceivedNetworkRefresh);

		if (GetWorld()->GetNetMode() == ENetMode::NM_Client)
		{
			//We really only need to bind this if we are a client.
			OnNetworkTickDelegateHandle = GameInstance->GetConnectionManager()->NetworkTick.AddUObject(this, &UWWUIScreenMultiplayerLobby::HandleNetworkTick);

			ARugbyPlayerController* MainController = Cast< ARugbyPlayerController>(GameInstance->GetPrimaryPlayerController());
			MainController->Server_AreYouAlive();
			UWWUIFunctionLibrary::OnFrameDelay(1, FTimerDelegate::CreateUObject(this, &UWWUIScreenMultiplayerLobby::ShowWaitingModal));

			//Mute all talkers when the modal is up.
			if (SIFApplication::GetApplication()->GetVoipManager())
			{
				SIFApplication::GetApplication()->GetVoipManager()->MuteAllTalkers();
			}
		}
		else
		{
			// Setup the controllers for run around. Possesion will update after the host players have been registered.
			SetupControllersForRunAround();

			GameInstance->GetConnectionManager()->GotoState(ERugbyNetworkState::Connected);
			
			//This call will also unlock the session so clients can join
			GameInstance->HostListen();
			bProceedToTeamSelectIfLocalLeader = true;

			// Start our restriction checks.
			GameInstance->StartUserRestrictionCheck(EUserPrivileges::CanCommunicateOnline);
			GameInstance->StartUserRestrictionCheck(EUserPrivileges::CanUseUserGeneratedContent);

			ARugbyGameState* TheGameState = Cast< ARugbyGameState>(UGameplayStatics::GetGameState(GetWorld()));

			if (TheGameState)
			{
				TheGameState->StartVoipScreen();
			}
		}
	}

	//< Get Lobby Player States. >
	TArray<APlayerState*> playerStates = GetWorld()->GetGameState()->PlayerArray;
	for (int i = 0; i < playerStates.Num(); i++)
	{
		ARugbyPlayerState* state = Cast<ARugbyPlayerState>(playerStates[i]);
		if (state)
		{
			state->ResetIdleTimer();
		}
	}	

#if !UE_BUILD_SHIPPING
	if (FParse::Param(FCommandLine::Get(), TEXT("LanGame")))
	{
		FString lanKey;
		FParse::Value(FCommandLine::Get(), TEXT("LanKey="), lanKey);

		FString lanName;
		FParse::Value(FCommandLine::Get(), TEXT("LanName="), lanName);

		bool isHost = GameInstance->GetMatchmakingManager()->IsHost();
		FString windowTitle = FString::Printf(TEXT("[%s] %s - %s"), *lanKey, (isHost ? TEXT("HOST") : TEXT("CLIENT")), *lanName);
		UKismetSystemLibrary::SetWindowTitle(FText::FromString(windowTitle));
	}
#endif // !UE_BUILD_SHIPPING

	// Callback to when the whole lobby is ready
	OnLobbyReadyDelegateHandle = GameInstance->OnAllPlayerStatesReadyChanged.AddUObject(this, &UWWUIScreenMultiplayerLobby::OnAllPlayerStateReadyChanged);
	OnMatchReadyDelegateHandle = GameInstance->OnAllConsolesLoadedMatch.AddUObject(this, &UWWUIScreenMultiplayerLobby::OnMatchLoadFinish);
	
// 
// 	//=======================================================
// 	//< Setup Custom Navigation Rules for the player lists. >
// 	UWWUIScrollBox* homeTeamScrollbox		= Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::HomeTeamList));
// 	UWWUIScrollBox* awayTeamScrollbox		= Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::AwayTeamList));
// 	UWWUIScrollBox* missingPlayerScrollbox	= Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::MissingPlayerList));
// 	if (!homeTeamScrollbox || !awayTeamScrollbox || !missingPlayerScrollbox) return;
// 
// 	UScrollBox* homeInternalScrollbox		= homeTeamScrollbox->GetScrollBox();
// 	UScrollBox* awayInternalScrollbox		= awayTeamScrollbox->GetScrollBox();
// 	UScrollBox* missingInternalScrollbox	= missingPlayerScrollbox->GetScrollBox();
// 	if (!homeInternalScrollbox || !awayInternalScrollbox || !missingInternalScrollbox) return;
// 
// 	homeInternalScrollbox->SetNavigationRule(EUINavigation::Up,		EUINavigationRule::Custom, "FindNextWidgetToFocus");
// 	homeInternalScrollbox->SetNavigationRule(EUINavigation::Down,	EUINavigationRule::Custom, "FindNextWidgetToFocus");
// 	homeInternalScrollbox->SetNavigationRule(EUINavigation::Left,	EUINavigationRule::Custom, "FindNextWidgetToFocus");
// 	homeInternalScrollbox->SetNavigationRule(EUINavigation::Right,	EUINavigationRule::Custom, "FindNextWidgetToFocus");
// 
// 	awayInternalScrollbox->SetNavigationRule(EUINavigation::Up,		EUINavigationRule::Custom, "FindNextWidgetToFocus");
// 	awayInternalScrollbox->SetNavigationRule(EUINavigation::Down,	EUINavigationRule::Custom, "FindNextWidgetToFocus");
// 	awayInternalScrollbox->SetNavigationRule(EUINavigation::Left,	EUINavigationRule::Custom, "FindNextWidgetToFocus");
// 	awayInternalScrollbox->SetNavigationRule(EUINavigation::Right,	EUINavigationRule::Custom, "FindNextWidgetToFocus");
// 
// 	missingInternalScrollbox->SetNavigationRule(EUINavigation::Up,		EUINavigationRule::Custom, "FindNextWidgetToFocus");
// 	missingInternalScrollbox->SetNavigationRule(EUINavigation::Down,	EUINavigationRule::Custom, "FindNextWidgetToFocus");
// 	missingInternalScrollbox->SetNavigationRule(EUINavigation::Left,	EUINavigationRule::Custom, "FindNextWidgetToFocus");
// 	missingInternalScrollbox->SetNavigationRule(EUINavigation::Right,	EUINavigationRule::Custom, "FindNextWidgetToFocus");
// 
// 	homeInternalScrollbox->Navigation->ResolveRules(homeTeamScrollbox,			homeTeamScrollbox->WidgetTree);
// 	awayInternalScrollbox->Navigation->ResolveRules(awayTeamScrollbox,			awayTeamScrollbox->WidgetTree);
// 	missingInternalScrollbox->Navigation->ResolveRules(missingPlayerScrollbox,	missingPlayerScrollbox->WidgetTree);
// 
// 	homeInternalScrollbox->BuildNavigation();
// 	awayInternalScrollbox->BuildNavigation();
// 	missingInternalScrollbox->BuildNavigation();
//

#if PLATFORM_PS4
	SetupControllerColours();
#endif

	UpdateLegendText();
}

//===============================================================================
//===============================================================================
void UWWUIScreenMultiplayerLobby::UpdateMatchSettings()
{
	UWidget* mapText = FindChildWidget(WWUIScreenMultiplayerLobby_UI::Map);
	UWidget* gameModeText = FindChildWidget(WWUIScreenMultiplayerLobby_UI::GameMode);
	UWidget* matchLengthText = FindChildWidget(WWUIScreenMultiplayerLobby_UI::MatchLength);
	UWidget* timeOfDayText = FindChildWidget(WWUIScreenMultiplayerLobby_UI::TimeOfDay);

	MABASSERT(mapText);
	MABASSERT(gameModeText);
	MABASSERT(matchLengthText);
	MABASSERT(timeOfDayText);

	FString stadiumNameString;
	int stadiumID = SIFMatchmakingHelpers::GetValue(1, "tf_stadium");
	if (stadiumID == 0)
	{
		stadiumID = SIFGameHelpers::GAGetStadium();
	}
	stadiumNameString = FString(SIFGameHelpers::GAGetStadiumName(stadiumID).c_str());
	SetWidgetText(mapText, FText::FromString(stadiumNameString).ToUpper());

	if (SIFApplication::GetApplication() && SIFApplication::GetApplication()->GetMatchmakingManager())
	{
		SetWidgetText(gameModeText, FText::FromString(UWWUITranslationManager::Translate(SIFApplication::GetApplication()->GetMatchmakingManager()->GetGametype() == GAME_MODE_RU13 ? "[ID_RUGBY_FIFTEENS]" : "[ID_RUGBY_SEVENS]")));
	}

	SetWidgetText(matchLengthText, FText::FromString(UWWUITranslationManager::Translate("[ID_GAME_LENGTH_" + FString::FromInt(SIFGameHelpers::GAGetGameLength()) + "_MINUTES]")));

	FString weatherString;
	weatherString.Append(UWWUITranslationManager::Translate("[ID_TIMEOFDAY_" + FString(SIFGameHelpers::GAGetTimeOfDay()) + "]"));
	weatherString.Append(", ");
	weatherString.Append(UWWUITranslationManager::Translate("[ID_MATCH_SETTINGS_CONDITIONS_" + FString(SIFGameHelpers::GAGetConditions()) + "]"));
	SetWidgetText(timeOfDayText, FText::FromString(weatherString));
}

void UWWUIScreenMultiplayerLobby::RegisterFunctions()
{
	AddInputAction(FString("RU_UI_ACTION_TUTORIALS"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenMultiplayerLobby::OnSelect));
	AddInputAction(FString("RU_UI_ACTION_PAUSE"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenMultiplayerLobby::OnPause));

	// Inputs for changing team.
	AddInputAction(FString("RU_UI_LOBBY_CHANGE_SIDE"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenMultiplayerLobby::OnSwapSide));
	//AddInputAction(FString("RU_UI_LOBBY_INVITE"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenMultiplayerLobby::OnAttemptInvite));

#if PLATFORM_XBOXONE
	AddInputAction(FString("RU_UI_ACTION_ACCEPT"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenMultiplayerLobby::OnAttemptViewGamerCard));
#endif

#if PLATFORM_WINDOWS
	// Modifier
	AddInputAxis("RU_UI_LOBBY_MODIFIER", FWWUIScreenAxisDelegate::CreateUObject(this, &UWWUIScreenMultiplayerLobby::OnModifier));
	AddInputAction(FString("RU_UI_LOBBY_KICK"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenMultiplayerLobby::OnAttemptKick));
	AddInputAction(FString("RU_UI_LOBBY_MUTE"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenMultiplayerLobby::OnAttemptMute));
#endif

	//AddInputAxis("RU_UI_CHANGE_SELECTION_VERTICAL", FWWUIScreenAxisDelegate::CreateUObject(this, &UWWUIScreenMultiplayerLobby::OnVerticalNavigation));

#if wwDEBUG_SRA
	//AddInputAction(FString("RU_UI_LOBBY_MUTE"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenMultiplayerLobby::OnDebugLaunchPopup));
	//AddInputAction(FString("RU_SIDESTEP_KB_LEFT"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenMultiplayerLobby::OnDebugReturnToTitle)); //Keyboard X
#endif

#if PLATFORM_WINDOWS
	GetOnKeyDownMulticast()->AddUObject(this, &UWWUIScreenMultiplayerLobby::OnKeyDownCallback);
#endif 

}

#if PLATFORM_WINDOWS
void UWWUIScreenMultiplayerLobby::OnKeyDownCallback(const FKey& InKey)
{
	if (InKey == FKey("Tab"))
	{
		TabDownDetected = true;
	}
}
#endif

//===============================================================================
//===============================================================================
#if wwDEBUG_SRA
void UWWUIScreenMultiplayerLobby::OnDebugLaunchPopup(APlayerController* _PlayerController)
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

	FString textString = FString::Printf(TEXT("Testing VOIP overlap update thing"));
	FString legendString = "[ID_MAIN_MENU_SUB_HELP]";

	modalData->WarningDialogue = textString;
	modalData->LegendString = legendString;
	modalData->CloseOnBackButton = true;
	modalData->CloseOnSelectButton = true;

	TArray<FModalButtonInfo> ButtonData;

	m_gameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
}

//===============================================================================
//===============================================================================
void UWWUIScreenMultiplayerLobby::OnDebugReturnToTitle(APlayerController* _PlayerController)
{
	SIFGameHelpers::GASetMainMenuFlowScreen("TitleScreen");

	URugbyGameInstance* pGameInstance = URugbyGameInstance::GetInstance();
	if (pGameInstance)
	{
		pGameInstance->SetInOnlineArea(false);
		pGameInstance->SetInFanhubArea(false); // Especially relevant to suppress the game's ability to pop up a connection error and take you to the main menu
	}

	if (SIFApplication::GetApplication()->GetOnlineMode() == EOnlineMode::Online)
	{
		SIFApplication::GetApplication()->SetOnlineMode(EOnlineMode::Offline);
	}

	// Exit the game.
	SIFGameHelpers::GAExitGame(true);
}

#endif

void UWWUIScreenMultiplayerLobby::Update(const float DeltaTime)
{
	//< Update timer if active >
	if (GameInstance)
	{
		if (GameInstance->WaitingForHostCheck)
		{
			return;
		}

		if (ModifierActive)
		{
			UpdateLegendText();
		}

		if (CountdownTimerVisible)
		{
			ARugbyGameState* const MyGameState = Cast<ARugbyGameState>(GetWorld()->GetGameState());
			if (MyGameState && MyGameState->StartingMatch)
			{
				UWWUIFunctionLibrary::SetText(FindChildWidget(WWUIScreenMultiplayerLobby_UI::CountdownTimer), FString::FromInt(MyGameState->RemainingTime));
			}
			else
			{
				UWWUIFunctionLibrary::SetText(FindChildWidget(WWUIScreenMultiplayerLobby_UI::CountdownTimer), FString::FromInt(MyGameState->RemainingTimeReadyUp));
			}
		}

		if (LoadingMatch)
		{
			if (GameInstance->GetMatchGameWorld())
			{
				UWWUIFunctionLibrary::SetText(FindChildWidget(WWUIScreenMultiplayerLobby_UI::LoadingPercent), FString::Printf(TEXT("%i%%"), GameInstance->GetMatchGameWorld()->GetLoadCompletionEstimate()));

				//	Mattt H - If the match is still loading, and the flow node is set to entering a match and match ready, and the loading is 100%,
				//			- than there may need to be another check (maybe a timer), but this seems to be stuck so set the Set connection manager to disconnecting
				if (GameInstance->GetMatchmakingManager()->IsHost())
				{
					if (GameInstance->GetFlowControlManager())
					{
						UFlowNode* tempNode = GameInstance->GetFlowControlManager()->GetActiveNode();

						if (tempNode)
						{
							if (UTrainingFlowNode* tempTrainingNode = Cast<UTrainingFlowNode>(tempNode))
							{
								if (tempTrainingNode->IsEnteringMatch())
								{
									if (tempTrainingNode->IsMatchReady())
									{
										if (GameInstance->GetMatchGameWorld()->GetLoadCompletionEstimate() == 100)
										{
											if (GetWorld())
											{
												bool disconnect = true;

												for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
												{
													APlayerController* NextPlayer = Iterator->Get();

													if (NextPlayer)
													{
														if (!NextPlayer->IsLocalController())
														{
															disconnect = false;
														}
													}
												}

												// Check for singleplayer mode.
												{
#ifdef WITH_EDITOR
													if (FParse::Param(FCommandLine::Get(), TEXT("AllowSinglePlayer")))
													{
														disconnect = false;
													}
#endif

#if defined ONLINE_ALLOWSINGLEPLAYER
													disconnect = false;
#endif
												}

												if (disconnect)
												{
													if (GameInstance->GetConnectionManager())
													{
														ARugbyGameState* TheGameState = Cast<ARugbyGameState>(UGameplayStatics::GetGameState(GetWorld()));

														if (TheGameState)
														{
															TheGameState->EndVoipScreen();
														}

														GameInstance->GetConnectionManager()->GotoState(ERugbyNetworkState::Disconnecting, ERugbyDisconnectionReason::Generic);
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
		else
		{

		


			if (GameInstance->GetWorld() && GameInstance->GetWorld()->GetGameState())
			{
				//Update the player list when someone drops the lobby
				int32 CurrentPlayerCount = GameInstance->GetWorld()->GetGameState()->PlayerArray.Num();
				//int32 counter = 0;

				//I have removed this as the game mode will handle adding player states for us as they join, adding player states here could lead to interesting things, like an extra invalid state getting added or 
				//a state getting added before it should, leading to an extra player state in the list.

				//Host players will get added in ARugbyGameModeBase::AddHostPlayers() once they have valid net id's.
				//Clients will get added on during APlayerState::PostInitializeComponents(), this will happen on the host and the player array will get updated on clients.

/*
				if (GetWorld()->GetNetMode() != ENetMode::NM_Client)
				{
					for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
					{
						APlayerController* NextPlayer = Iterator->Get();
						
						//If we dont have a valid index then we can assume we need to add the player state to the array
						if (!GameInstance->GetWorld()->GetGameState()->PlayerArray.IsValidIndex(counter) && UOBJ_IS_VALID(NextPlayer))
						{
						//	if (NextPlayer->PlayerState != GameInstance->GetWorld()->GetGameState()->PlayerArray[counter])
							{
								GameInstance->GetWorld()->GetGameState()->AddPlayerState(NextPlayer->PlayerState);
							}
						}

						counter++;
					}
				}*/

				if (LastPlayerCount != CurrentPlayerCount)
				{
					//RefreshPlayerList();
					CheckIsHostAlone();
					UpdateLegendText();

					LastPlayerCount = CurrentPlayerCount;
				}
			}

			if (SIFApplication::GetApplication()->GetVoipManager() && !bProceedToTeamSelectIfLocalLeader)
			{
				ARugbyGameState* TheGameState = Cast< ARugbyGameState>(UGameplayStatics::GetGameState(GetWorld()));
				if (TheGameState)
				{
					if (SIFApplication::GetApplication()->GetConnectionManager()->GetCurrentState() == ERugbyNetworkState::Online)
					{
						if (GameInstance->GetVoipManager()->IsVoipServiceSetup() && GameInstance->GetVoipManager()->HasChatRestrictionBeenSet() == true)
						{
							if (!TheGameState->IsVoipScreenVisible())
							{
								TheGameState->ShowVoipScreen(true);
								SIFApplication::GetApplication()->GetVoipManager()->UnmuteAllTalkers();
							}
						}
#if PLATFORM_SWITCH
						else
						{
							//GameInstance->GetVoipManager()->IsVoipServiceSetup() returns false, so in some situations on Switch the VOIP list will disappear and not come back
							//As it's used for the player list as well, just make sure it's on-screen
							if (!TheGameState->IsVoipScreenVisible())
							{
								TheGameState->ShowVoipScreen(true);
							}
						}
#endif
					}
				}
			}
		}

		UpdateMatchSettings();

		//Lets setup VOIP here
		//We dont need to do this on switch, this needs to happen on both ends
		bool bVoipIsSetup = false;

#if (PLATFORM_SWITCH == 0 /*&& PLATFORM_WINDOWS == 0*/)
		//Need to make sure we are valid for voip before we go setting things up
		if (SIFApplication::GetApplication()->GetConnectionManager()->GetCurrentState() == ERugbyNetworkState::Online)
		{
			if (GameInstance->GetVoipManager()->HasChatRestrictionBeenSet() == true)
			{
				bVoipIsSetup = GameInstance->GetVoipManager()->IsVoipServiceSetup();
				if (!bVoipIsSetup)
				{
					if (!GameInstance->GetVoipManager()->IsAnyOnlineControllerCommunicationRestricted())
					{
						if (GameInstance->GetVoipManager()->SetupVoipService())
						{
							GameInstance->GetVoipManager()->SetVoipServiceSetup(true);
							GameInstance->GetVoipManager()->SetSessionVoip(true);
						}
						else
						{
							UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenMultiplayerLobby::Update Could not start VOIP! Likely the VoiceInt was invalid if this is steam."));
							bVoipIsSetup = true;
						}
					}
					else
					{
						//If we are restricted we want to say we are setup but not activated
						GameInstance->GetVoipManager()->SetVoipServiceSetup(true);
						GameInstance->GetVoipManager()->SetVoipServiceEnabled(false);

						GameInstance->GetVoipManager()->RestrictVoip();
					}
				}
			}
		}
#else
		bVoipIsSetup = true;
#endif

#ifdef WITH_EDITOR
		if (FParse::Param(FCommandLine::Get(), TEXT("NOSTEAM")))
		{
			bVoipIsSetup = true;
		}
#endif

		bool bErrorMessageOnScreen = GameInstance->GetUIScreenManager()->FindScreenTemplate(Screens_UI::ModalNetworkConnectivity) >= 0;

		// Once we have setup the voip stuff, show the team select.
		if (!bErrorMessageOnScreen && bVoipIsSetup && bProceedToTeamSelectIfLocalLeader)
		{
			TeamSelectIfLocalTeamLeader();
			bProceedToTeamSelectIfLocalLeader = false;
		}

		if (bCheckTeamsFromTeamSelect && !SelectingTeam)
		{
			CheckTeamGender();
			bCheckTeamsFromTeamSelect = false;
		}
	}

	if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
	{
		for (ARugbyPlayerController* pc : pRugbyGameInstance->GetLocalPlayerControllers())
		{
			if (pc->IsNetReady())
			{
				pc->UpdateLobbyPrivacyLevel((uint8)pRugbyGameInstance->GetLobbyPrivacy());
			}
		}
	}
}

void UWWUIScreenMultiplayerLobby::OnInFocus()
{
	Super::OnInFocus();

	SIFGameHelpers::GAResumeSandboxGame();

	SIFGameHelpers::GASetIsAProMode(false);

	SIFUIHelpers::DisableInGameInput(false);

	SIFUIHelpers::ListenToAllControllers();

	if (!SelectingTeam)
	{
		SetInputEnabled(true);
	}

	IsLeavingScreen = false;
	
	SelectingTeam = false;

	//< Update Lobby Player List. >
	//RefreshPlayerList();
	UpdateLocalPlayersVoip();
	if (UWidget* tempVoipList = FindChildWidget(WWUIScreenMultiplayerLobby_UI::VoipTeamListVerticalBox))
	{
		tempVoipList->SetVisibility(ESlateVisibility::Hidden);
	}

	if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
	{
		//The last DisallowLocalTalkers_ThisFunctionsIsNotForMutingVOIP call was made when we finished creating/joining the game session
		pRugbyGameInstance->GetVoipManager()->AllowLocalTalkers_ThisFunctionIsNotForUnMutingVOIP();

		if (pRugbyGameInstance->GetVoipManager())
		{
			ARugbyGameState* TheGameState = Cast< ARugbyGameState>(UGameplayStatics::GetGameState(GetWorld()));
			if (TheGameState)
			{
				TheGameState->ShowVoipScreen(true);

				if (TheGameState->IsVoipScreenVisible())
				{
					pRugbyGameInstance->GetVoipManager()->UnmuteAllTalkers();
				}
				else
				{
					//We might want to be muted due to things such as the setting up lobby modal, but the 
					//AllowLocalTalkers has unmuted us.
					pRugbyGameInstance->GetVoipManager()->MuteAllTalkers();
				}
			}
		}
	}
}

//This will happen any time we show the pause menu or the team select menu
void UWWUIScreenMultiplayerLobby::OnOutFocus(bool ShouldOutFocus)
{
	Super::OnOutFocus(ShouldOutFocus);

	if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
	{
		if (ARugbyPlayerController* rugbyPlayerController = Cast<ARugbyPlayerController>(pRugbyGameInstance->GetMasterPlayerController()))
		{
			ModifierChanged(false, rugbyPlayerController);
		}
	}

	//SIFApplication::GetApplication()->GetVoipManager()->DisallowLocalTalkers();
}

bool UWWUIScreenMultiplayerLobby::OnSystemEvent(WWUINodeProperty& eventParams)
{
	FString EventName = eventParams.GetStringProperty("system_event");

	UE_LOG(LogNetwork, Display, TEXT("UWWUIScreenMultiplayerLobby::OnSystemEvent Event: %s"), *EventName);

	if (EventName.Compare(FString("set_team")) == 0)
	{
		OnLobbyTeamChanged(eventParams.GetIntProperty("team_index"), eventParams.GetIntProperty("db_id"));
		UpdateVoipListTeamHeaderDetails();
		UpdateFieldTeamStrip();
	}
	else if (EventName.Compare(FString("on_game_settings_changed")) == 0)
	{
		//RefreshPlayerList();
		UpdateFieldTeamStrip();
		UpdateLocalPlayersVoip();
		CheckTeamGender();
		UpdateVoipListTeamHeaderDetails();
	}
	else if (EventName.Compare("on_game_initialised") == 0)
	{
		UpdateVoipListTeamHeaderDetails();
		OnMatchLoadStart();
	}

	return true;
}

void UWWUIScreenMultiplayerLobby::TableOnSelectionChange(FString InTableId, int OldIdx, int NewIdx)
{
	UpdateLegendText(NewIdx);

	//< Update navigation bindings for the team scroll boxes. >
	//UWWUIScrollBox* homeTeamScrollbox		= Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::HomeTeamList));
	//UWWUIScrollBox* awayTeamScrollbox		= Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::AwayTeamList));
	//UWWUIScrollBox* missingPlayerScrollbox	= Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::MissingPlayerList));
	//if (!homeTeamScrollbox || !awayTeamScrollbox || !missingPlayerScrollbox) return;
	//
	///// Home Scrollbox Bindings.
	//if (homeTeamScrollbox->GetListLength() - 1 == NewIdx)
	//	homeTeamScrollbox->NavigationDelegateDown.BindUFunction(this, "CustomPlayerListNavigation");
	//else
	//	homeTeamScrollbox->NavigationDelegateDown.Unbind();
	//
	///// Away Scrollbox Bindings.
	//if (NewIdx == 0)
	//	awayTeamScrollbox->NavigationDelegateUp.BindUFunction(this, "CustomPlayerListNavigation");
	//else
	//	awayTeamScrollbox->NavigationDelegateUp.Unbind();
	//
	//if (awayTeamScrollbox->GetListLength() - 1 == NewIdx)
	//	awayTeamScrollbox->NavigationDelegateDown.BindUFunction(this, "CustomPlayerListNavigation");
	//else
	//	awayTeamScrollbox->NavigationDelegateDown.Unbind();
	//
	///// Missing Scrollbox Binding.
	//if (NewIdx == 0)
	//	missingPlayerScrollbox->NavigationDelegateUp.BindUFunction(this, "CustomPlayerListNavigation");
	//else
	//	missingPlayerScrollbox->NavigationDelegateUp.Unbind();

}

void UWWUIScreenMultiplayerLobby::ExecuteTableFunction(FString InTableId, int InIdx, FString InAction, FString InActionString)
{
#if PLATFORM_XBOXONE
// 	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
// 
// 	if (pRugbyGameInstance)
// 	{
// 		APlayerController* OwningController = pRugbyGameInstance->GetMasterPlayerController();
// 		OnAttemptViewGamerCard(OwningController);
// 	}
#endif
}

//< Inputs >
void UWWUIScreenMultiplayerLobby::OnSelect(APlayerController* _PlayerController)
{
	bool bSkipUniqueConnectionsCheck = false;
#ifdef WITH_EDITOR
	if (FParse::Param(FCommandLine::Get(), TEXT("AllowSinglePlayer")))
	{
		bSkipUniqueConnectionsCheck = true;
	}
#endif

// #if defined ONLINE_ALLOWSINGLEPLAYER
// 	bSkipUniqueConnectionsCheck = true; //AllowSinglePlayer
// #endif  

	if (!LoadingMatch && !bProceedToTeamSelectIfLocalLeader) //&& !IsLeavingScreen)
	{
		if (!bSkipUniqueConnectionsCheck)
		{
			if (CheckIsHostAlone())
			{
				return;
			}
		}

		if (ARugbyPlayerController* playerController = Cast<ARugbyPlayerController>(_PlayerController))
		{
			if (ARugbyPlayerState* rugbyPlayerState = playerController->GetPlayerState<ARugbyPlayerState>())
			{

				if (UOBJ_IS_VALID(playerController))
				{
					if (UOBJ_IS_VALID(rugbyPlayerState))
					{
						playerController->SetIsReadyState(!rugbyPlayerState->IsReady());
					}
				}

				if (!rugbyPlayerState->IsHost())
				{
					LocallyReadyCheck = (rugbyPlayerState->IsReady() == false);
					rugbyPlayerState->SetLocalReady(LocallyReadyCheck);
					//	This currently does nothing as the populator already gets updated on tick/update, which sets the ready states every tick/update, which will correct this at the moment.
					//GameInstance->VoipScreenOverrideReadyStates(true, LocallyReadyCheck, rugbyPlayerState);
				}
			}
		}
	}
}

bool UWWUIScreenMultiplayerLobby::CheckIsHostAlone()
{
	if (GetWorld() && GetWorld()->GetNetMode() != ENetMode::NM_Client)
	{
		bool onlyLocalPlayer = true;

		TArray<APlayerController*> playerList;

		for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
		{
			APlayerController* NextPlayer = Iterator->Get();

			if (NextPlayer)
			{
				playerList.Add(NextPlayer);

				if (!NextPlayer->IsLocalController())
				{
					onlyLocalPlayer = false;
				}
			}
		}

		if (onlyLocalPlayer)
		{
			for (int i = 0; i < playerList.Num(); ++i)
			{
				APlayerController* playerController = playerList[i];
				if (playerController)
				{
					if (ARugbyPlayerController* rugbyPlayerController = Cast<ARugbyPlayerController>(playerController))
					{
						if (ARugbyPlayerState* rugbyPlayerState = playerController->GetPlayerState<ARugbyPlayerState>())
						{
							if (rugbyPlayerState->IsReady())
							{
								rugbyPlayerController->SetIsReadyState(false);
							}
						}
					}
				}
			}
			return true;
		}
	}

	return false;
}

void UWWUIScreenMultiplayerLobby::OnPause(APlayerController* _PlayerController)
{
	if (!SIFGameHelpers::IsPrimaryLocalPlayerController(_PlayerController))
	{
		return;
	}

	if (ModifierActive)
	{
		return;
	}

	if (!LoadingMatch && !LocallyReadyCheck && !bProceedToTeamSelectIfLocalLeader)
	{
		if (ARugbyPlayerController* playerController = Cast<ARugbyPlayerController>(_PlayerController))
		{
			if (ARugbyPlayerState* rugbyPlayerState = playerController->GetPlayerState<ARugbyPlayerState>())
			{
				if (!rugbyPlayerState->IsReady())
				{
					if (UOBJ_IS_VALID(_PlayerController->GetLocalPlayer()))
					{
						IsLeavingScreen = true;
						DisplayPauseMenu(_PlayerController->GetLocalPlayer()->GetControllerId());
						//if (GameInstance)
						//{
						//	GameInstance->GetConnectionManager()->GotoState(ERugbyNetworkState::Disconnecting);
						//}
						//RefreshPlayerList();
						UpdateLocalPlayersVoip();
					}
				}
			}
		}
	}
}

void UWWUIScreenMultiplayerLobby::DisplayPauseMenu(int playerControllerId)
{
	if (SIFApplication::GetApplication()->GetVoipManager())
	{
		SIFApplication::GetApplication()->GetVoipManager()->MuteAllTalkers();
		ARugbyGameState* TheGameState = Cast< ARugbyGameState>(UGameplayStatics::GetGameState(GetWorld()));
		if (TheGameState)
		{
			TheGameState->ShowVoipScreen(false);
		}
	}

	//if (pause_enabled && !doing_save)
	{
		//< We need to display the pause menu. >
		//EntryFromTutorialPauseMenu = true;
		if (ModifierActive)
		{
			if (ARugbyPlayerController* rugbyPlayerController = Cast<ARugbyPlayerController>(GameInstance->GetMasterPlayerController()))
			{
				ModifierChanged(false, rugbyPlayerController);
			}
		}

		RaisePauseMenu(playerControllerId);
	}
}

void UWWUIScreenMultiplayerLobby::RaisePauseMenu(int playerControllerId)
{
	//< Pause the game & push the pause menu. >
	//SIFUIHelpers::ListenToAllControllers();
	SIFGameHelpers::GAPauseSandboxGame();

	//#rc3_legacy SIFUIHelpers::SetCurrentWindow(SIFUI_IN_GAME_MENU_WINDOW_NAME);
	// SIFGameHelpers::GAPauseGame(); // why pause the match game from the training field?

	SIFInGameHelpers::EnableMenuPostEffects();

	//#rc3_legacy
	// Replace QuitTutorialsYesOnClick with opening the pause menu...
	//QuitTutorialsYesOnClick(nullptr);

	UWWUIScreenPauseMenuData* pInData = NewObject<UWWUIScreenPauseMenuData>();
	if (pInData)
	{
		pInData->callingScreen = this;
		pInData->in_training = true;
		pInData->in_online_lobby = true;
		pInData->controllingPlayer = playerControllerId;
	}

	if(GameInstance)
	{
		GameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::PauseMenu, pInData);
	}
}

void UWWUIScreenMultiplayerLobby::OnSwapSide(APlayerController* _PlayerController)
{
#if PLATFORM_WINDOWS
	if (GameInstance->GetUIScreenManager()->GetUsingGamepad() || (!GameInstance->GetUIScreenManager()->GetUsingGamepad() && TabDownDetected))
	{
		TabDownDetected = false;
#endif
		if (!ModifierActive && !LocallyReadyCheck && !bProceedToTeamSelectIfLocalLeader && !LoadingMatch)
		{
			if (GameInstance && GameInstance->GetMatchmakingMode() != EMatchmakingMode::Ranked)
			{
				if (ARugbyPlayerController* pc = Cast<ARugbyPlayerController>(_PlayerController))
				{
					bool allowSwitch = true;

#if PLATFORM_SWITCH
					if (!pc->IsPrimaryPlayer())
					{
						return;
					}
#endif
					// Nick WWS 7s to Womens 13s //
					/*
					if (SIFGameHelpers::GAGetGameModeIsR7())
					{
						SSTEAMSIDE destinationTeam = SSTEAMSIDE::SIDE_NONE;
						ARugbyPlayerState* controllerState = pc->GetPlayerState<ARugbyPlayerState>();
						if (controllerState)
						{
							destinationTeam = (controllerState->GetTeamSide() == SSTEAMSIDE::SIDE_A) ? SSTEAMSIDE::SIDE_B : SSTEAMSIDE::SIDE_A;
						}

						if (AGameStateBase* gameState = UGameplayStatics::GetGameState(GetWorld()))
						{
							int destinationPlayerCount = 0;

							TArray<APlayerState*> totalGameStatePlayerArray = gameState->PlayerArray;
							for (APlayerState* playerState : totalGameStatePlayerArray)
							{
								if (playerState)
								{
									if (ARugbyPlayerState* rugbyPlayerState = Cast<ARugbyPlayerState>(playerState))
									{
										if (rugbyPlayerState->GetTeamSide() == destinationTeam)
										{
											destinationPlayerCount++;
										}
									}
								}
							}

							if (destinationPlayerCount >= MAX_HUMAN_PLAYERS_R7)
							{
								allowSwitch = false;

								FString errorString = UWWUITranslationManager::Translate("[ID_ONLINE_NO_SPACE_ON_TEAM]");
								FString sideString = UWWUITranslationManager::Translate(destinationTeam == SSTEAMSIDE::SIDE_A ? "[ID_MATCH_HOME]" : "[ID_MATCH_AWAY]");
								errorString = errorString.Replace(TEXT("%s"), *sideString);

								GameInstance->ShowErrorUIPopup("", errorString);
							}
						}
					} */

					if (allowSwitch)
					{
						pc->RequestTeamSideChange();

#if PLATFORM_SWITCH
						if(pc->IsPrimaryPlayer())
						{
							TArray<ARugbyPlayerController*> tempControllers = GameInstance->GetLocalPlayerControllers();

							for (ARugbyPlayerController* playerController : tempControllers)
							{
								if (playerController != pc)
								{
									playerController->RequestTeamSideChange();
								}
							}
						}
#endif
					}
				}
			}
		}
#if PLATFORM_WINDOWS
	}
#endif
}

void UWWUIScreenMultiplayerLobby::UpdateFieldTeamStrip()
{
	URugbyGameInstance* pRugbyGameInstance = URugbyGameInstance::GetInstance();
	if (pRugbyGameInstance != nullptr)
	{
		SIFGameWorld* pActiveGameWorld = pRugbyGameInstance->GetActiveGameWorld();
		if (pActiveGameWorld != nullptr && pActiveGameWorld->IsSandbox())
		{
			if (pRugbyGameInstance->GetMatchGameSettings())
			{
				SSTEAMSIDE stripSideToShow = SSTEAMSIDE::SIDE_A;
				if (IsAnyLocalControllerLeader(SSTEAMSIDE::SIDE_A))
				{
					stripSideToShow = SIDE_A;
				}
				else
				{
					if (IsAnyLocalControllerLeader(SSTEAMSIDE::SIDE_B))
					{
						stripSideToShow = SIDE_B;
					}
					else
					{
						if (pRugbyGameInstance->GetMasterPlayerController())
						{
							if (ARugbyPlayerState* tempPlayerState = Cast<ARugbyPlayerState>(pRugbyGameInstance->GetMasterPlayerController()->PlayerState))
							{
								stripSideToShow = tempPlayerState->GetTeamSide();
							}
						}
					}
				}

				//	This only changes side a because in the lobby, we only ever see sida a.
				SIFGameHelpers::GASetTeamStripFromStripID(pRugbyGameInstance->GetMatchGameSettings()->team_settings[stripSideToShow].team.GetStripId(0), SSTEAMSIDE::SIDE_A);
			}
		}
	}
}

void UWWUIScreenMultiplayerLobby::OnModifier(float _AxisValue, APlayerController* _Controller)
{
	if (SelectingTeam) return;

	/// Active Modifier flag ONLY if input is coming from master controller.
	if (ARugbyPlayerController* rugbyPlayerController = Cast<ARugbyPlayerController>(_Controller))
	{
		if (GameInstance->GetMasterPlayerController() == rugbyPlayerController)
		{
			bool isTriggerDown = _AxisValue > TRIGGER_ACTIVE_VALUE ? true : false;
			if (ModifierActive != isTriggerDown)
			{
				ModifierChanged(isTriggerDown, rugbyPlayerController);
			}
		}
	}
}

void UWWUIScreenMultiplayerLobby::OnAttemptMute(APlayerController* _PlayerController)
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->VoipScreenMuteUser(_PlayerController);
	}
// 	if (CanMuteSelectedPlayer().bCanMutePlayer)
// 	{
// 		if (ARugbyPlayerController* targetPlayerController = GetPlayerControllerFromPlayerListFocus())
// 		{
// 			if (ARugbyPlayerState* playerState = targetPlayerController->GetPlayerState<ARugbyPlayerState>())
// 			{
// 				URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
// 
// 				if (pRugbyGameInstance && pRugbyGameInstance->GetVoipManager())
// 				{
// 					pRugbyGameInstance->GetVoipManager()->ToggleLocalPlayerMute(playerState);
// 				}
// 			}
// 		}
// 	}
}

void UWWUIScreenMultiplayerLobby::OnAttemptKick(APlayerController* _PlayerController)
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->VoipScreenKickUser(_PlayerController);
	}
// 	if (ARugbyPlayerController* targetPlayerController = GetPlayerControllerFromPlayerListFocus())
// 	{
// 		if (CanKickSelectedPlayer())
// 		{
// 			UWorld* pWorld = GetWorld();
// 
// 			if (pWorld)
// 			{
// 				ARugbyGameModeBase* pRugbyGameMode = Cast<ARugbyGameModeBase>(pWorld->GetAuthGameMode());
// 
// 				if (pRugbyGameMode)
// 				{
// 					pRugbyGameMode->KickPlayer(targetPlayerController);
// 				}
// 			}
// 		}
// 	}
}

// void UWWUIScreenMultiplayerLobby::OnVerticalNavigation(float _AxisValue, APlayerController* _PlayerController)
// {
// 	if (ModifierActive)
// 	{
// 		if (_AxisValue <= -0.1f || _AxisValue >= 0.1f)
// 		{
// 			if (GameInstance)
// 			{
// 				GameInstance->VoipScreenUpdateVoipListSetFocus(_AxisValue, _PlayerController);
// 			}
// 		}
// 	}
// }

#if PLATFORM_XBOXONE
void UWWUIScreenMultiplayerLobby::OnAttemptViewGamerCard(APlayerController* _PlayerController)
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->VoipScreenViewGamerCard(_PlayerController);
	}
// 	if (ARugbyPlayerController* playerController = GetPlayerControllerFromPlayerListFocus())
// 	{
// 		if (ARugbyPlayerState* playerState = playerController->GetPlayerState<ARugbyPlayerState>())
// 		{
// 			const auto OnlineSub = IOnlineSubsystem::Get();
// 
// 			if (OnlineSub)
// 			{
// 				const auto IdentityInterface = OnlineSub->GetIdentityInterface();
// 				if (IdentityInterface.IsValid())
// 				{
// 					TSharedPtr<GenericApplication> GenericApplication = FSlateApplication::Get().GetPlatformApplication();
// 					const bool bIsLicensed = GenericApplication->ApplicationLicenseValid();
// 
// 					int32 controllerId = 0;
// 					controllerId = SIFApplication::GetApplication()->GetPrimaryAccountControllerIndex();
// 					if (controllerId != -1)
// 					{
// 						const auto LoginStatus = IdentityInterface->GetLoginStatus(controllerId);
// 						if (LoginStatus == ELoginStatus::LoggedIn && bIsLicensed)
// 						{
// 							// Show the gamer card.
// 							const auto ExternalUI = OnlineSub->GetExternalUIInterface();
// 							if (ExternalUI.IsValid())
// 							{
// 								if (_PlayerController && _PlayerController->GetLocalPlayer())
// 								{
// 									const TSharedPtr<const FUniqueNetId> requestorNetID = _PlayerController->GetLocalPlayer()->GetPreferredUniqueNetId().GetUniqueNetId();
// 
// 									ExternalUI->ShowProfileUI(*requestorNetID, *playerState->UniqueId.GetUniqueNetId());
// 									return;
// 								}
// 							}
// 						}
// 					}
// 				}
// 			}
// 		}
// 	}
}
#endif

/*void UWWUIScreenMultiplayerLobby::OnAttemptInvite(APlayerController* _PlayerController)
{
	if (ModifierActive)
	{
		if (m_inviteObject.CanInvite())
		{
			UE_LOG(LogTemp, Warning, TEXT("Did show invite UI"));
			m_inviteObject.Invite();
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Did not show invite UI"));
		}
	}
}*/

//< Helpers >
void UWWUIScreenMultiplayerLobby::TeamSelectIfLocalTeamLeader()
{
	//< For each local controller: if team leader, goto team select. >
	for (ARugbyPlayerController* pc : GameInstance->GetLocalPlayerControllers())
	{
		if (ARugbyPlayerState* rugbyPlayerState = pc->GetPlayerState<ARugbyPlayerState>())
		{
			if (UOBJ_IS_VALID(pc))
			{
				if (UOBJ_IS_VALID(rugbyPlayerState))
				{
					if ((rugbyPlayerState->m_homeTeamLeader || rugbyPlayerState->m_awayTeamLeader))
					{
						//SIFApplication::GetApplication()->DealMenuAction(SCREEN_POP_MENUSCREEN, Screens_UI::Loading);
						//SIFApplication::GetApplication()->HideOnlineScreens();

						//	Set the ready state to false (as we clearly won't be ready if this happens).
						pc->SetIsReadyState(false);
						IsLeavingScreen = true;
						//	Mattt H - Parse in the bProceedToTeamSElectIfLocalLeader as this is the initial time we enter the screen and need to chose a team.
						UWWUIScreenMultiplayerLobby::ProceedToTeamSelect(pc, bProceedToTeamSelectIfLocalLeader);
						break;
					}
				}
			}		
		}
	}
}

void UWWUIScreenMultiplayerLobby::ProceedToTeamSelect(ARugbyPlayerController* _PlayerController, bool _InitialEntry)
{
	if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
	{
		if (UWWUIStateScreenOnlineSelectTeamsData* pInData = NewObject<UWWUIStateScreenOnlineSelectTeamsData>())
		{
			SIFUIHelpers::DisableInGameInput(true);

			pInData->playerController = _PlayerController;
			pInData->initialEntry = _InitialEntry;
			SelectingTeam = true;
			pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::OnlineSelectTeams, pInData);
		}
	}
}

bool UWWUIScreenMultiplayerLobby::ProceedToTeamSelectOnModal(APlayerController* _PlayerController)
{
	//< For each local controller: if team leader, goto team select. >
	for (ARugbyPlayerController* pc : GameInstance->GetLocalPlayerControllers())
	{
		if (ARugbyPlayerState* rugbyPlayerState = pc->GetPlayerState<ARugbyPlayerState>())
		{
			if (rugbyPlayerState->m_homeTeamLeader || rugbyPlayerState->m_awayTeamLeader)
			{
				//SIFApplication::GetApplication()->DealMenuAction(SCREEN_POP_MENUSCREEN, Screens_UI::Loading);
				//SIFApplication::GetApplication()->HideOnlineScreens();
				IsLeavingScreen = true;
				UWWUIScreenMultiplayerLobby::ProceedToTeamSelect(pc);
				return true;
			}
		}
	}

	return false;
}

void UWWUIScreenMultiplayerLobby::ShowCountdownTimer(bool _Show)
{
	//UWWUIFunctionLibrary::SetVisibility(FindChildWidget(WWUIScreenMultiplayerLobby_UI::CountdownTimer), _Show);
	//CountdownTimerVisible = _Show;
}

void UWWUIScreenMultiplayerLobby::UpdateLegendText(int32 OverrideIndex /*= -1*/)
{
	UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::ChangeTeamLegend), true);
	UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::InviteLegend), false);
	UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::MuteLegend), false);
	UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::ReadyLegend), true);
	UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::PauseLegend), true);
#if PLATFORM_WINDOWS
	UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::HoldLegend), true);
#else
	UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::HoldLegend), false);
#endif

	//Ready & change team Legend
	if (ARugbyPlayerController* pMasterPlayerController = Cast<ARugbyPlayerController>(GameInstance->GetMasterPlayerController()))
	{
		if (ARugbyPlayerState* pControllerPlayerState = pMasterPlayerController->GetPlayerState<ARugbyPlayerState>())
		{
			if (LoadingMatch)
			{
				UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::ReadyLegend), false);
				UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::ChangeTeamLegend), false);
				if (UWidget* pauseLegend = UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::PauseLegend))
				{
					pauseLegend->SetVisibility(ESlateVisibility::Collapsed);
				}
			}
			else if (pControllerPlayerState->IsReady())
			{
				UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::ChangeTeamLegend), false);
				if (UWidget* pauseLegend = UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::PauseLegend))
				{
					pauseLegend->SetVisibility(ESlateVisibility::Collapsed);
				}
				
				if (UWWUIRichTextBlockWithTranslate* readyText = Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenMultiplayerLobby_UI::ReadyLegend)))
				{
					readyText->SetText("[ID_ONLINE_PLAYER_NOT_READY_UP]");
				}
			}
			else
			{
				if (UWWUIRichTextBlockWithTranslate* readyText = Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenMultiplayerLobby_UI::ReadyLegend)))
				{
					readyText->SetText("[ID_ONLINE_PLAYER_READY_UP]");
				}
			}			

			if (pControllerPlayerState->IsHost())
			{
				//UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::ChangeTeamLegend), false);

				bool onlyLocalPlayer = true;

				for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
				{
					APlayerController* NextPlayer = Iterator->Get();

					if (NextPlayer)
					{
						if (!NextPlayer->IsLocalController())
						{
							onlyLocalPlayer = false;
						}
					}
				}

				if (onlyLocalPlayer)
				{
					if (UWidget* readyLegend = UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::ReadyLegend))
					{
						readyLegend->SetVisibility(ESlateVisibility::Collapsed);
					}
				}
			}
		}
	}

	//	Hide the change team legend, if the game is in ranked.
	if (GameInstance && GameInstance->GetMatchmakingMode() == EMatchmakingMode::Ranked)
	{
		UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::ChangeTeamLegend), false);
	}


	//	Hide the change team legend, if are viewing the player lists.
 	if (ModifierActive)
 	{
		//Mute legend
		if (UWWUIRichTextBlockWithTranslate* muteText = Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenMultiplayerLobby_UI::MuteLegend)))
		{
			URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
			if (pRugbyGameInstance)
			{
				VoipScreenCanMutePlayerResult muteResult = pRugbyGameInstance->VoipScreenCanMuteUser();
				UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::MuteLegend), muteResult.bCanMutePlayer);
				muteText->SetText(muteResult.bPlayerCurrentlyMuted ? "[ID_ONLINE_PLAYER_UNMUTE]" : "[ID_ONLINE_PLAYER_MUTE]");
			}
		}


		//< Invite legend. >
		/*if (AGameStateBase* gameState = UGameplayStatics::GetGameState(GetWorld()))
		{
			TArray<APlayerState*> playerArray = gameState->PlayerArray;
			UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::InviteLegend), (GameInstance->GetSessionMaxPlayers() - playerArray.Num()) != 0);
		}*/

		UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::ChangeTeamLegend), false);
 	}
}

void UWWUIScreenMultiplayerLobby::ModifierChanged(bool _NewModifierValue, ARugbyPlayerController* _PlayerController)
{
#if !PLATFORM_WINDOWS
	return; //#simple_voip This can be removed for patch 1.2 when the voip functionality is expanded.
#endif

	if (!_PlayerController) return;
	ModifierActive = _NewModifierValue;

	/// Update Input.
	SIFUIHelpers::DisableInGameInput(ModifierActive);

	bool voipScreenVisible = false;

	ARugbyGameState* TheGameState = Cast< ARugbyGameState>(UGameplayStatics::GetGameState(GetWorld()));
	if (TheGameState)
	{
		voipScreenVisible = TheGameState->IsVoipScreenVisible();
	}

	if (voipScreenVisible)
	{
		/// Update UI focus.
		if (!_NewModifierValue)
		{
			if (GameInstance)
			{
				GameInstance->VoipScreenVoipListRemoveFocus();
			}
			/// Remove focus by setting it to the viewport.
			FSlateApplication::Get().SetUserFocusToGameViewport(_PlayerController->GetControllerIndex());
		}
		else
		{
			/// Set focus to first valid player field.
			//if (GetFirstValidPlayerListField()) GetFirstValidPlayerListField()->GetButtonWidget()->SetUserFocus(_PlayerController);
			if (GameInstance)
			{
				GameInstance->VoipScreenVoipListSetFocus(_PlayerController);
			}
		}
	}

	UpdateLegendText();
}

UWWUIListField* UWWUIScreenMultiplayerLobby::GetFirstValidPlayerListField()
{
	if (UWWUIScrollBox* homeTeamScrollbox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::HomeTeamList)))
	{
		if (UWWUIListField* returnListField = Cast<UWWUIListField>(homeTeamScrollbox->GetListField(0)))
			return returnListField;
	}

	if (UWWUIScrollBox* awayTeamScrollbox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::AwayTeamList)))
	{
		if (UWWUIListField* returnListField = Cast<UWWUIListField>(awayTeamScrollbox->GetListField(0)))
			return returnListField;
	}

	return nullptr;
}

void UWWUIScreenMultiplayerLobby::RefreshPlayerList()
{
	UpdateLegendText();

	UWWUIScrollBox* homeTeamScrollbox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::HomeTeamList));
	UWWUIScrollBox* awayTeamScrollbox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::AwayTeamList));
	UWWUIScrollBox* missingPlayerScrollbox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::MissingPlayerList));
	if (!homeTeamScrollbox || !awayTeamScrollbox || !missingPlayerScrollbox) return;

	//< Set all populator sides. >
	if (UWWUIPopulatorOnlinePlayers* populatorHome = Cast<UWWUIPopulatorOnlinePlayers>(homeTeamScrollbox->GetPopulator())) { populatorHome->PopulatorSide = SIDE_A; }
	if (UWWUIPopulatorOnlinePlayers* populatorAway = Cast<UWWUIPopulatorOnlinePlayers>(awayTeamScrollbox->GetPopulator())) { populatorAway->PopulatorSide = SIDE_B; }
	if (UWWUIPopulatorOnlinePlayers* populatorMissing = Cast<UWWUIPopulatorOnlinePlayers>(missingPlayerScrollbox->GetPopulator())) { populatorMissing->PopulatorSide = SIDE_NONE; }

	//< Refresh all populators >
	homeTeamScrollbox->Refresh();
	awayTeamScrollbox->Refresh();
	missingPlayerScrollbox->Refresh();

	//< Setup the values for the home team header. >
	if (UUserWidget* homeTeamHeader = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::HomeTeamHeader)))
	{
		if (RUGameSettings* game_settings = SIFApplication::GetApplication()->GetMatchGameSettings())
		{
			RUDB_TEAM db_team = game_settings->team_settings[SSTEAMSIDE::SIDE_A].team;
			MabColour team_colour;
			db_team.GetScreenFriendlyColour(team_colour);

			FLinearColor team_display_colour = SIFGameHelpers::GAConvertMabColorToFLinearColor(team_colour);
			if (FMath::Max3(team_display_colour.R, team_display_colour.G, team_display_colour.B) > 0.6f)
			{
				team_display_colour = UWWUIFunctionLibrary::ScaleLinearColourBetween(team_display_colour, 0.0f, 0.6f);
			}

			//< Set Colour >
			if (UBorder* colourBorder1 = Cast<UBorder>(UWWUIFunctionLibrary::FindChildWidget(homeTeamHeader, WWUIScreenMultiplayerLobby_UI::TeamColour1)))
				colourBorder1->SetBrushColor(team_display_colour);

			if (UBorder* colourBorder2 = Cast<UBorder>(UWWUIFunctionLibrary::FindChildWidget(homeTeamHeader, WWUIScreenMultiplayerLobby_UI::TeamColour2)))
				colourBorder2->SetBrushColor(team_display_colour);

			//< Set team logo >
			MabString LogoPath = SIFGameHelpers::GAGetTeamLogoHUDAssetPath(db_team.GetDbId());
			if (UImage* pLogoImage = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(homeTeamHeader, WWUIScreenMultiplayerLobby_UI::TeamLogo)))
			{
				FString name = FString(LogoPath.c_str());
				if (UTexture2D* pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name)))
				{
					pLogoImage->SetBrushFromTexture(pTexture, true);
				}
			}

			//< Set Team Name >
			if (UWWUITextBlock* teamName = Cast<UWWUITextBlock>(UWWUIFunctionLibrary::FindChildWidget(homeTeamHeader, WWUIScreenMultiplayerLobby_UI::TeamName)))
				teamName->SetText(FText::FromString(db_team.GetShortName()));
		}
	}

	if (UUserWidget* awayTeamHeader = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::AwayTeamHeader)))
	{
		if (RUGameSettings* game_settings = SIFApplication::GetApplication()->GetMatchGameSettings())
		{
			RUDB_TEAM db_team = game_settings->team_settings[SSTEAMSIDE::SIDE_B].team;
			MabColour team_colour;
			db_team.GetScreenFriendlyColour(team_colour);

			FLinearColor team_display_colour = SIFGameHelpers::GAConvertMabColorToFLinearColor(team_colour);
			if (FMath::Max3(team_display_colour.R, team_display_colour.G, team_display_colour.B) > 0.6f)
			{
				team_display_colour = UWWUIFunctionLibrary::ScaleLinearColourBetween(team_display_colour, 0.0f, 0.6f);
			}

			//< Set Colour >
			if (UBorder* colourBorder1 = Cast<UBorder>(UWWUIFunctionLibrary::FindChildWidget(awayTeamHeader, WWUIScreenMultiplayerLobby_UI::TeamColour1)))
				colourBorder1->SetBrushColor(team_display_colour);

			if (UBorder* colourBorder2 = Cast<UBorder>(UWWUIFunctionLibrary::FindChildWidget(awayTeamHeader, WWUIScreenMultiplayerLobby_UI::TeamColour2)))
				colourBorder2->SetBrushColor(team_display_colour);

			//< Set team logo >
			MabString LogoPath = SIFGameHelpers::GAGetTeamLogoAssetPath(db_team.GetDbId());
			if (UImage* pLogoImage = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(awayTeamHeader, WWUIScreenMultiplayerLobby_UI::TeamLogo)))
			{
				FString name = FString(LogoPath.c_str());
				if (UTexture2D* pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name)))
				{
					pLogoImage->SetBrushFromTexture(pTexture, true);
				}
			}

			//< Set Team Name >
			if (UWWUITextBlock* teamName = Cast<UWWUITextBlock>(UWWUIFunctionLibrary::FindChildWidget(awayTeamHeader, WWUIScreenMultiplayerLobby_UI::TeamName)))
				teamName->SetText(FText::FromString(db_team.GetShortName()));
		}
	}
}

void UWWUIScreenMultiplayerLobby::UpdateLocalPlayersVoip()
{
	// This call was done when the player list refreshed in AFL.
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->GetVoipManager()->UpdateLocalPlayersVoip(ENetworkVoipChannel::OPEN);
	}
}

void UWWUIScreenMultiplayerLobby::RefreshMatchSettings()
{

}

ARugbyPlayerController* UWWUIScreenMultiplayerLobby::GetPlayerControllerFromPlayerListFocus(int32 OverrideIndex /*= -1*/)
{
	//	Mattt H - This needs to use the VoipScreen list box.
	//< Run custom navigation between the 3 scroll boxes. >
	UWWUIScrollBox* homeTeamScrollbox		= Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::HomeTeamList));
	UWWUIScrollBox* awayTeamScrollbox		= Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::AwayTeamList));
	UWWUIScrollBox* missingPlayerScrollbox	= Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::MissingPlayerList));
	if (!homeTeamScrollbox || !awayTeamScrollbox || !missingPlayerScrollbox || !GameInstance) return nullptr;

	if (AGameStateBase* gameState = UGameplayStatics::GetGameState(GetWorld()))
	{
		TArray<APlayerState*>		playerArray = gameState->PlayerArray;
		TArray<ARugbyPlayerState*>	homePlayerStates;
		TArray<ARugbyPlayerState*>	awayPlayerStates;

		//< Gather the current player states for both sides. >
		for (APlayerState* playerState : playerArray)
		{
			if (ARugbyPlayerState* rugbyPlayerState = Cast<ARugbyPlayerState>(playerState))
			{
				if (rugbyPlayerState->GetTeamSide() == SSTEAMSIDE::SIDE_A) homePlayerStates.Add(rugbyPlayerState);
				if (rugbyPlayerState->GetTeamSide() == SSTEAMSIDE::SIDE_B) awayPlayerStates.Add(rugbyPlayerState);
			}
		}

		//< Check the home player list >
		if (homeTeamScrollbox->GetSelectedIndex(true) != -1)
		{
			int32 SelectedIndex = homeTeamScrollbox->GetSelectedIndex(true);

			if (OverrideIndex >= 0) SelectedIndex = OverrideIndex;

			if (homePlayerStates.IsValidIndex(SelectedIndex))
				return Cast<ARugbyPlayerController>(homePlayerStates[SelectedIndex]->GetOwner());
		}
		//< Check the away player list >
		else if (awayTeamScrollbox->GetSelectedIndex(true) != -1)
		{
			int32 SelectedIndex = awayTeamScrollbox->GetSelectedIndex(true);

			if (OverrideIndex >= 0) SelectedIndex = OverrideIndex;

			if (awayPlayerStates.IsValidIndex(SelectedIndex))
				return Cast<ARugbyPlayerController>(awayPlayerStates[SelectedIndex]->GetOwner());
		}
		else if (missingPlayerScrollbox->GetSelectedIndex(true) != -1)
		{
			return nullptr;
		}
	}

	return nullptr;
}

UWidget* UWWUIScreenMultiplayerLobby::CustomPlayerListNavigation(EUINavigation _Navigation)
{
	//< Run custom navigation between the 3 scroll boxes. >
	UWWUIScrollBox* homeTeamScrollbox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::HomeTeamList));
	UWWUIScrollBox* awayTeamScrollbox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::AwayTeamList));
	UWWUIScrollBox* missingPlayerScrollbox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenMultiplayerLobby_UI::MissingPlayerList));
	if (!homeTeamScrollbox || !awayTeamScrollbox || !missingPlayerScrollbox) return nullptr;

	if (homeTeamScrollbox->GetSelectedIndex(true) != -1)
	{
		if (_Navigation == EUINavigation::Down && awayTeamScrollbox->GetListLength() > 0)
		{
			awayTeamScrollbox->SetSelectedIndex(0);
			UpdateSelectedIndex(awayTeamScrollbox->GetName(), 0, true);
		}
		else if (_Navigation == EUINavigation::Down && missingPlayerScrollbox->GetListLength() > 0)
		{
			missingPlayerScrollbox->SetSelectedIndex(0);
			UpdateSelectedIndex(missingPlayerScrollbox->GetName(), 0, true);
		}
	}
	else if (awayTeamScrollbox->GetSelectedIndex(true) != -1)
	{
		if (_Navigation == EUINavigation::Up && homeTeamScrollbox->GetListLength() > 0)
		{
			homeTeamScrollbox->SetSelectedIndex(homeTeamScrollbox->GetListLength() - 1);
			UpdateSelectedIndex(homeTeamScrollbox->GetName(), homeTeamScrollbox->GetListLength() - 1, true);
		}
		if (_Navigation == EUINavigation::Down && missingPlayerScrollbox->GetListLength() > 0)
		{
			missingPlayerScrollbox->SetSelectedIndex(0);
			UpdateSelectedIndex(missingPlayerScrollbox->GetName(), 0, true);
		}
	}
	else if (missingPlayerScrollbox->GetSelectedIndex(true) != -1)
	{
		if (_Navigation == EUINavigation::Up && awayTeamScrollbox->GetListLength() > 0)
		{
			awayTeamScrollbox->SetSelectedIndex(awayTeamScrollbox->GetListLength() - 1);
			UpdateSelectedIndex(awayTeamScrollbox->GetName(), awayTeamScrollbox->GetListLength() - 1, true);
		}
		else if (_Navigation == EUINavigation::Up && homeTeamScrollbox->GetListLength() > 0)
		{
			homeTeamScrollbox->SetSelectedIndex(homeTeamScrollbox->GetListLength() - 1);
			UpdateSelectedIndex(homeTeamScrollbox->GetName(), homeTeamScrollbox->GetListLength() - 1, true);
		}
	}

	return nullptr;
}

//< Network Callbacks. >
void UWWUIScreenMultiplayerLobby::OnReceivedNetworkRefresh(ENetworkRefreshType RefreshType)
{
	switch (RefreshType)
	{
	case ENetworkRefreshType::PLAYER_STATE:
		//RefreshPlayerList();
		UpdateLocalPlayersVoip();
		UpdateFieldTeamStrip();
		break;
	case ENetworkRefreshType::LOBBY_STATE:
		break;
	case ENetworkRefreshType::MATCH_SETTINGS:
		RefreshMatchSettings();
		UpdateFieldTeamStrip();
		break;
	default:
		break;
	}

	//Mattt H - I think it is good to do a legend refresh during this.
	UpdateLegendText();
}

void UWWUIScreenMultiplayerLobby::UpdateVoipListTeamHeaderDetails()
{
	//	Mattt H - This tells the voip screen to change/adjust the team header details to the new team (so it doesn't spam).
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->VoipScreenSetTeamHeaderDetails(true);
	}
}

void UWWUIScreenMultiplayerLobby::OnLobbyTeamChanged(int _Side, int _NewTeamDbId)
{

	//RefreshPlayerList();
	UpdateLocalPlayersVoip();
	CheckTeamGender();
}

void UWWUIScreenMultiplayerLobby::OnMatchLoadFinish()
{
	ShowCountdownTimer(true);
}

bool UWWUIScreenMultiplayerLobby::CanKickSelectedPlayer(int32 OverrideIndex /*= -1*/)
{
	bool bCanKickSelectedPlayer = false;

	if (ARugbyPlayerController* pTargetPlayerController = GetPlayerControllerFromPlayerListFocus(OverrideIndex))
	{
		ARugbyPlayerController* pMasterPlayerController = Cast<ARugbyPlayerController>(GameInstance->GetMasterPlayerController());

		// Make sure we aren't trying to kick ourselves.
		if (pTargetPlayerController != pMasterPlayerController)
		{
			if (ARugbyPlayerState* pMasterPlayerState = pMasterPlayerController->GetPlayerState<ARugbyPlayerState>())
			{
				ARugbyPlayerState* pTargetPlayerState = pTargetPlayerController->GetPlayerState<ARugbyPlayerState>();
				// Make sure we are from different console groups.
				if (pMasterPlayerState->PlayerStateIsFromDifferentGroup(pTargetPlayerState))
				{
					// If we are the host, we can kick the player.
					bCanKickSelectedPlayer = pMasterPlayerState->IsHost();
				}
			}
		}
	}

	return bCanKickSelectedPlayer;
}

CanMutePlayerResult UWWUIScreenMultiplayerLobby::CanMuteSelectedPlayer(int32 OverrideIndex /*= -1*/)
{
	CanMutePlayerResult CanMutePlayerResultValue;

	if (ARugbyPlayerController* playerController = GetPlayerControllerFromPlayerListFocus(OverrideIndex))
	{
		ARugbyPlayerController* myPlayerController = Cast<ARugbyPlayerController>(GameInstance->GetMasterPlayerController());

		// Make sure we aren't trying to mute ourselves.
		if (myPlayerController != playerController)
		{
			if (ARugbyPlayerState* playerState = playerController->GetPlayerState<ARugbyPlayerState>())
			{
				URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

				if (pRugbyGameInstance && pRugbyGameInstance->GetVoipManager())
				{
					CanMutePlayerResultValue.bPlayerCurrentlyMuted = pRugbyGameInstance->GetVoipManager()->IsPlayerMuted(playerState);
				}
				
				CanMutePlayerResultValue.bCanMutePlayer = true;
			}
		}
	}

	return CanMutePlayerResultValue;
}

void UWWUIScreenMultiplayerLobby::CheckTeamGender()
{
	//Check if not the host, than set the genderPermissionFlags based on the team and repopulate.
	if (!SelectingTeam)
	{
		if (GameInstance && !LoadingMatch)
		{
			if (IsAnyLocalControllerLeader(SSTEAMSIDE::SIDE_B))
			{
				if (RUGameSettings* game_settings = GameInstance->GetMatchGameSettings())
				{
					RUDB_TEAM db_homeTeam = game_settings->team_settings[SSTEAMSIDE::SIDE_A].team;
					RUDB_TEAM db_awayTeam = game_settings->team_settings[SSTEAMSIDE::SIDE_B].team;
					if (db_homeTeam.GetGenderPermissionFlags() != db_awayTeam.GetGenderPermissionFlags())
					{
						if (ModifierActive)
						{
							if (ARugbyPlayerController* rugbyPlayerController = Cast<ARugbyPlayerController>(GameInstance->GetMasterPlayerController()))
							{
								ModifierChanged(false, rugbyPlayerController);
							}
						}

						//	Bring modal up here.
						ShowTeamGenderChangeModal();
					}
				}
			}
		}
	}
	else
	{
		bCheckTeamsFromTeamSelect = true;
	}
}

bool UWWUIScreenMultiplayerLobby::IsAnyLocalControllerLeader(SSTEAMSIDE inTeamSide)
{
	TArray<ULocalPlayer *> LocalPlayers = SIFApplication::GetApplication()->GetLocalPlayers();
	for (ULocalPlayer * pCurrentPlayer : LocalPlayers)
	{
		ARugbyPlayerController* pPlayerController = Cast<ARugbyPlayerController>(pCurrentPlayer->GetPlayerController(GetWorld()));
		if (UOBJ_IS_VALID(pPlayerController))
		{
			if (ARugbyPlayerState* localControllerPlayerState = Cast<ARugbyPlayerState>(pPlayerController->PlayerState))
			{
				if ((inTeamSide == SSTEAMSIDE::SIDE_A && localControllerPlayerState->m_homeTeamLeader) || (inTeamSide == SSTEAMSIDE::SIDE_B && localControllerPlayerState->m_awayTeamLeader))
				{
					return true;
				}
			}
		}
	}

	return false;
}

int UWWUIScreenMultiplayerLobby::HowManyLocalControllersOnSide(SSTEAMSIDE inTeamSide)
{
	int count = 0;

	TArray<ULocalPlayer *> LocalPlayers = SIFApplication::GetApplication()->GetLocalPlayers();
	for (ULocalPlayer * pCurrentPlayer : LocalPlayers)
	{
		ARugbyPlayerController* pPlayerController = Cast<ARugbyPlayerController>(pCurrentPlayer->GetPlayerController(GetWorld()));
		if (UOBJ_IS_VALID(pPlayerController))
		{
			if (ARugbyPlayerState* localControllerPlayerState = Cast<ARugbyPlayerState>(pPlayerController->PlayerState))
			{
				if (inTeamSide == localControllerPlayerState->GetTeamSide())
				{
					count++;
				}
			}
		}
	}

	return count;
}

void UWWUIScreenMultiplayerLobby::ResetControllersForRunAround()
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		RUGameSettings* pMatchGameSettings = pRugbyGameInstance->GetMatchGameSettings();

		if (pMatchGameSettings)
		{
			for (int32 PlayerSlot = 0; PlayerSlot < 8; PlayerSlot++)
			{
				FRugbyHumanPlayerSettings* pCurrentHumanSettings = pMatchGameSettings->GetHumanPlayerSettings((EHumanPlayerSlot)PlayerSlot);

				if (pCurrentHumanSettings)
				{
					pCurrentHumanSettings->team = SSTEAMSIDE_SIDE_INVALID;// SSTEAMSIDE::SIDE_NONE;
					pCurrentHumanSettings->player_id = -1;
					pCurrentHumanSettings->peer_id = -1;
					pCurrentHumanSettings->controller_id = -1;
				}
			}
		}

		// We just changed some playerIds on their states, so need to update the human players.
		RUSandboxGame* pSandboxGame = pRugbyGameInstance->GetSandboxGame();

		if (pSandboxGame)
		{
			SIFGameWorld* pSandboxGameWorld = pSandboxGame->GetGameWorld();

			if (pSandboxGameWorld)
			{
				pSandboxGameWorld->SetupHumanPlayersForLoading(false);
	}
}
	}
}

void UWWUIScreenMultiplayerLobby::SetupControllersForRunAround()
{
	FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator();
	RUGameSettings* pMatchGameSettings = GameInstance->GetMatchGameSettings();

	if (pMatchGameSettings)
	{
		for (; Iterator; ++Iterator)
		{
			ARugbyPlayerController* pPlayerController = Cast<ARugbyPlayerController>(Iterator->Get());
			if (UOBJ_IS_VALID(pPlayerController))
			{
				if (pPlayerController->IsLocalPlayerController())
				{
					FRugbyHumanPlayerSettings* pCurrentHumanSettings = pMatchGameSettings->GetHumanPlayerSettings((EHumanPlayerSlot)pPlayerController->GetConnectionIndex());

					if (pCurrentHumanSettings)
					{
						pCurrentHumanSettings->team = SSTEAMSIDE::SIDE_A;
						pCurrentHumanSettings->player_id = pPlayerController->GetPlayerIndex();
					}
				}
			}
		}
	}
}

void UWWUIScreenMultiplayerLobby::SetupPlayerLevelLaunchData()
{
	TArray<ULocalPlayer *> LocalPlayers = SIFApplication::GetApplication()->GetLocalPlayers();

	for (ULocalPlayer * pCurrentPlayer : LocalPlayers)
	{
		SIFPlayerHelpers::PMSetPlayerHuman(pCurrentPlayer->GetControllerId(), pCurrentPlayer->GetControllerId());
	}
}

#if PLATFORM_PS4
void UWWUIScreenMultiplayerLobby::SetupControllerColours()
{
	FPS4Application* pPS4App = FPS4Application::GetPS4Application();

	if (pPS4App)
	{
		IInputInterface* pPS4InputInterface = pPS4App->GetInputInterface();

		if (pPS4InputInterface)
		{
			FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator();

			for (; Iterator; ++Iterator)
			{
				ARugbyPlayerController* pPlayerController = Cast<ARugbyPlayerController>(Iterator->Get());
				if (UOBJ_IS_VALID(pPlayerController))
				{
					if (pPlayerController->IsLocalPlayerController())
					{
						ARugbyPlayerState* pPlayerState = Cast<ARugbyPlayerState>(pPlayerController->PlayerState);

						if (pPlayerState)
						{
							const FLinearColor NewPlayerColour = RU_LOCAL_PLAYER_COLOURS[pPlayerState->m_connectionIndex];
							FColor InColour = FColor(NewPlayerColour.R * 255, NewPlayerColour.G * 255, NewPlayerColour.B * 255);
							pPS4InputInterface->SetLightColor(pPlayerController->GetControllerIndex(), InColour);
						}
					}
				}
			}
		}
	}
}

#endif

void UWWUIScreenMultiplayerLobby::OnAllPlayerStateReadyChanged(bool bAllReady)
{
	//RefreshPlayerList();
	UpdateLocalPlayersVoip();
	ShowCountdownTimer(bAllReady);
	LocallyReadyCheck = false;
}

void UWWUIScreenMultiplayerLobby::OnMatchLoadStart()
{
	UE_LOG(LogNetwork, Display, TEXT("UWWUIScreenMultiplayerLobby::OnMatchLoadStart"));

	// We received the authoritive human player settings from the host at this point, we can setup our local level launch data now.
	SetupPlayerLevelLaunchData();

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->EnterTrainingFlow(TrainingPrompt::EnteringMatch);

		pRugbyGameInstance->HandleVoipScreenMatchReady(true);

		//< Display loading icon. >
		UWWUIFunctionLibrary::SetVisibility(FindChildWidget(WWUIScreenMultiplayerLobby_UI::LoadingIcon), ESlateVisibility::Visible);
		UWWUIFunctionLibrary::SetVisibility(FindChildWidget(WWUIScreenMultiplayerLobby_UI::LoadingPercent), ESlateVisibility::Visible);
		UWWUIFunctionLibrary::PlayAnimation(this, Animations_UI::BP_UIScreenMultiplayerLobby::SpinLoadIcon, 0, 0);
		LoadingMatch = true;
		pRugbyGameInstance->GetConnectionManager()->SetMaploading(true);

		//< Get Lobby Player States. >
		TArray<APlayerState*> playerStates = GetWorld()->GetGameState()->PlayerArray;
		for (int i = 0; i < playerStates.Num(); i++)
		{
			ARugbyPlayerState* state = Cast<ARugbyPlayerState>(playerStates[i]);
			if (state)
			{
				state->ResetIdleTimer();
			}
		}
	}

	ShowCountdownTimer(false);

	UpdateLegendText(); //RC4-6839,	Need to call this to hide the ready and pause legends. (Checks LoadingMatch)
}

void UWWUIScreenMultiplayerLobby::Shutdown()
{
	if (SIFApplication::GetApplication()->GetVoipManager())
	{
		SIFApplication::GetApplication()->GetVoipManager()->MuteAllTalkers();
	}

	if (GameInstance)
	{
		//< Clear network refresh delegate >
		GameInstance->OnNetworkRefresh.Remove(this, FName("OnReceivedNetworkRefresh"));

		GameInstance->OnAllPlayerStatesReadyChanged.Remove(OnLobbyReadyDelegateHandle);
		GameInstance->OnAllConsolesLoadedMatch.Remove(OnMatchReadyDelegateHandle);
		GameInstance->OnAsyncGameLoadComplete.Remove(OnLoadCompleteDelegateHandle);
		GameInstance->GetConnectionManager()->NetworkTick.Remove(OnNetworkTickDelegateHandle);
	}

	SIFUIHelpers::DisableInGameInput(false);

#if (PLATFORM_XBOXONE || PLATFORM_XBOX360) //&& defined (GAMECENTRE_ENABLED) 
	SIFGameHelpers::XSSetShouldAllowReturnToStartScreen(true);
#endif
}