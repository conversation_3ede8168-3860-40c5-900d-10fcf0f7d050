/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#pragma once
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/RUOrigin.h"


class ARugbyCharacter;

class SetplayOrigin : public IRUOrigin
{
public:
	SetplayOrigin() = default;
	SetplayOrigin(const FVector& _setplay_position, float _facing_angle) : Setplay_position(_setplay_position), facing_angle(_facing_angle) {}

	FVector GetOrigin() const override { return Setplay_position; }
	float GetFacingAngle() const override { return facing_angle; }

	FVector Setplay_position = FVector::ZeroVector;
	float facing_angle = 0.f;
};

struct SyncPoint {
	FString key;
	int maxPlayers;
	TArray<ARugbyCharacter *> waitingPlayers;

	SyncPoint()
	{
		key = "";
		maxPlayers = 0;
		waitingPlayers = TArray<ARugbyCharacter *>();
	}

	SyncPoint(FString inKey, int inNumPlayers)
	{
		key = inKey;
		maxPlayers = inNumPlayers;
		waitingPlayers = TArray<ARugbyCharacter *>();
	}
};

class RUPassSetplayInterface;

class SSSetPlayManager
{

public:
	SSSetPlayManager(SSEVDSFormationManager * formationManager, SIFGameWorld *ggame);
	~SSSetPlayManager();

	void UpdateSimulation(float deltaTime);
	void StartSetplayByName(FString setplayName, SSHumanPlayer * startingHuman = nullptr);
	void AbortSetplay();
	void SetSetplayOrigin(const FVector _setplay_position, float _facing_angle);
	void SetSetplayStarted(bool hasStarted);
	void AddPlayerToSetplay(ARugbyCharacter * player, SSRoleArea * area);
	void RemovePlayerFromSetplay(ARugbyCharacter * player);
	
	bool AreSetplaysEnabled();

	//Gets the "time" value of where the player is in their setplay. note: This is the index value of the waypoints.
	float GetPlayerMovementWaypointTime(ARugbyCharacter*  player, SSRoleArea * area);

	//Currently a debug function to draw the player waypoints, but could be turned into the setplay indicators
	void DrawPlayerWaypoints(ARugbyCharacter* player);

	void UpdatePlayerSetplayStatus(ARugbyCharacter * player, SSRoleArea * area);

	//Shows/Hides the 3D HUD for the setplay in progress
	void ShowCurrentSetplayDisplay(bool show);

	void PhaseChanged();
	void InitialiseSplines();
	//Get the number of players who have completed a setplay
	int NumPlayersCompletedSetplay(SSRoleArea * area, int zoneIndex);
	
	//Return the origin on the setplay
	SetplayOrigin * GetSetplayOrigin();

	//Check if a role area has had all it's players assigned
	bool HasRoleAreaBeenFilled(SSRoleArea * area, int zoneIndex);

	FSerialiseFormation * GetSetplayByIndex(int setplayIndex);

	void AddSetplayToList(FSerialiseFormation * addedSetplay, int index);

	void PopulateDefaultSetplays();

	template <typename T> FDataTableRowHandle AddOrFindRow(UDataTable * inDatatable, T row);
	//Check if every player is in position. Note: This is different from HasSetplayStarted, as this manually checks through all the players, whereas HasSetPlayStarted returns a bool.
	bool ArePlayersInPosition();

	//Check if the player is running a setplay
	bool isPlayerRunningSetplay(ARugbyCharacter * player);

	bool ShouldPlayerStayBehindSelectedPlayer(ARugbyCharacter * player);
	void UpdatePlayerSelection();
	//Return the current setplay
	FSerialiseFormation * GetCurrentSetplay() { return CurrentSetplay; }

	//Check if there is a setplay in progress for this team.
	bool IsSetplayInProgress() { return CurrentSetplay != nullptr; }

	//Return if the players have all reached the starting positions
	bool HasSetplayStarted() { return bSetplayStarted; };

	void SetPlayerAtSyncPoint(ARugbyCharacter * player);

	bool IsSyncPointComplete(ARugbyCharacter * player);

	ARugbyCharacter * GetSetplayTargettedCharacter() { return setplayTargettedCharacter; };

	ARugbyCharacter * GetGTBOverridePlayer() { return GTBOverridePlayer; };
	void SetGTBOverridePlayer(ARugbyCharacter * inChar) { GTBOverridePlayer = inChar; };

	TMap<MabNURBSSpline*, FColor> GetSetplaySplines() { return m_SetplaySplines; }

	const TArray<FString> defaultSetplays = {
		FString("NULL"),
		FString("Dummy Skip Pass"),
		FString("Classic"),
		FString("Cross Kick and Wing"),
		FString("Dummy Skip Pass PTB"),
		FString("Classic PTB"),
		FString("Cross Kick and Wing PTB"),
	};

private:
	SIFGameWorld* game = nullptr;

	//Array of game actions to match button indexes
	const TArray<ERugbyGameAction> setplayDecisions =
	{
		ERugbyGameAction::RU_GAME_ACTIONS_MAX,	//This max is here because the index 0 for an action means no action
		ERugbyGameAction::DECISION_BOTTOM,
		ERugbyGameAction::DECISION_RIGHT,
		ERugbyGameAction::DECISION_LEFT,
		ERugbyGameAction::DECISION_TOP
	};

#if PLATFORM_PS4
	const TArray<FColor> buttonColours = {
		FColor::FromHex("3371CEFF"), //playstation x blue
		FColor::FromHex("FF2222FF"), //playstation circle red
		FColor::FromHex("FF24F2FF"), //playstation square pink
		FColor::FromHex("0DC259FF") //playstation triangle green
	};
#else
	const TArray<FColor> buttonColours = {
		FColor::FromHex("3cdb4e"), //xbox A green
		FColor::FromHex("d04242"), //xbox B red
		FColor::FromHex("40ccd0"), //xbox X blue
		FColor::FromHex("ecdb33") //xbox Y yellow
	};
#endif
//SWITCH_TO_LOOK_AT

	ARugbyCharacter * setplayTargettedCharacter = nullptr;

	void InitialiseSyncPoints();

	//3D UI for setplays interface
	RUPassSetplayInterface * pass_setplay_interface;

	//The current setplay in progress
	FSerialiseFormation * CurrentSetplay = nullptr;

	//Manager pointers
	SSEVDSFormationManager * FormationManager = nullptr;
	TArray<ARugbyCharacter*> SetplayCharacters;

	//Map of players to the buttons they are matched to.
	TMap<int, ARugbyCharacter*> buttonCharacterMap;

	//Map of all setplay roles that have been completed so that the formation manager doesn't set them again.
	TMap<FString, TArray<int>> PlayersCompletedSetplayRole;

	//bool for whether all players have reached the starting positions.
	bool bSetplayStarted;

	//The origin of the setplay
	SetplayOrigin * m_Origin = nullptr;

	//Array of the setplays the player has access to
	TArray<FSerialiseFormation *> m_SetplaysList;

	//The current sync point data
	TArray<SyncPoint> m_SyncPoints;

	//The array of setplay splines
	TMap<MabNURBSSpline*, FColor> m_SetplaySplines;

	//The human player that started the setplay/is in control.
	SSHumanPlayer* human_player;

	//The human player that started the setplay/is in control.
	ARugbyCharacter * GTBOverridePlayer = nullptr;

	//Bool for enabling/disabling setplays
	bool bSetplaysEnabled = true;

	//Bool to keep track of if setplay indicators are showing
	bool bSplinesInitialised = false;
};



/*
//class SIFGameWorld;
//class ARugbyCharacter;
//class SSTeam;
//class MabControlActionManager;
//
//#include "Match/RugbyUnion/RUGameState.h"
//
//
/////-------------------------------------------------------------------------
///// CLASS: SSSetPlayManager
/////  - Controls all setplays (Scrums,lineouts,rucks etc...)
/////  - There is only one, it controls both sides and determines outcomes.
/////-------------------------------------------------------------------------
//
//class SSSetPlayManager
//{
//	/// Constructor/destructor
//public:
//	SSSetPlayManager( SIFGameWorld* ggame, MabControlActionManager* ccontrol_action_manager);	
//	~SSSetPlayManager();	
//	
//	void	Reset();
//
//	void	JoinSetPlayGroup(ARugbyCharacter* player);
//	void	ExitSetPlayGroup(ARugbyCharacter* player);
//
//	bool	GetPlayerTargetPosition(ARugbyCharacter* player, FVector &target_pos);
//
//	void	UpdateLogic(float delta_game_time);
//
//	bool	StartAllowed();
//	bool	Finished();
//
//	bool	AllowRoleInterrupt(){ return allow_role_interupt; }			// Roles can check if they can be interrupted due to a user event (eg. +/- players in lineout)
//
//private:
//	void	SetLineOutPosition(ARugbyCharacter* player, FVector &target_pos);
//	void	LineOutEnter();
//	void	LineOutUpdate(float delta_game_time);
//	void	LineOutExit();
//
//	void	SetRuckPosition(ARugbyCharacter* player, FVector &target_pos);
//	void	RuckEnter();
//	void	RuckUpdate(float delta_game_time);
//	void	RuckExit();
//
//	void	SetScrumPosition(ARugbyCharacter* player, FVector &target_pos);
//	void	ScrumEnter();
//	void	ScrumUpdate(float delta_game_time);
//	void	ScrumExit();
//
//	void	GetTeamPlayers(SIFRugbyCharacterList& result, int team_no, MabTypeID role_id);
//
//	void	RemoveHumansFromSetPlay();
//
//private:
//	SIFGameWorld			*game;
//	MabControlActionManager	*control_action_manager;
//
//	RUGamePhase	last_phase;
//
//	int				state;
//	float			state_timer;
//
//	bool			allow_role_interupt;
//
//	SIFRugbyCharacterList team0_players;
//	SIFRugbyCharacterList team1_players;
//};
*/

