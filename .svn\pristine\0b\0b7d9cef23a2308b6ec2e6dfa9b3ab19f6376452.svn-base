/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"

#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/Formations/SSEVDSFormationEnum.h"
#include "Match/AI/Formations/SSEVDSFormationConstants.h"

#include "Match/RugbyUnion/RUTeam.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/RugbyUnion/Enums/RUPlayerPositionEnum.h"
#include "Match/Debug/RUDebugService.h"
#include "Match/SSSpatialHelper.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/Ball/SSBall.h"
#include "Match/SSRole.h"
#include "Match/AI/Roles/Competitors/RURoleBaseBallHolder.h"
#include "Match/SSRoleFactory.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/Debug/RUGameDebugSettings.h"
#include "Match/Debug/SIFDebug.h"
#include "Match/Components/RUActionManager.h"
#include "Match/SSMath.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Character/RugbyPlayerController.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/TutorialMode/RUTutorialManager.h"
#include "Match/PlayerProfile/SIFPlayerProfileConstants.h"

#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Utility/RURandomNumberGenerator.h"
#include "Match/SSPlayerFilter.h"
#include "Match/SSRole.h"

#include "Match/AI/Roles/Competitors/RURoleKickOffKicker.h"
#include "Match/AI/Roles/Competitors/RURoleMarkDefend.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuckDefend.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuckSendRunner.h"
#include "Match/AI/Roles/Competitors/SSRoleFormation.h"
#include "Match/AI/Roles/Competitors/RURoleStandardBallHolder.h"
#include "Match/AI/Roles/Competitors/RURoleGetTheBall.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleLineOut.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleLineOutThrower.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleLineOutReceiver.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuck.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuckScrumHalf.h"
#include "Match/AI/Roles/Competitors/RURoleShootForGoal.h"
#include "Match/SSRoleNull.h"
#include "Match/AI/Roles/Competitors/RURoleSupport.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleScrum.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleScrumHalfBack.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleTutorial.h"
#include "Match/AI/Roles/Competitors/RURolePenaltyAttack.h"
#include "Match/AI/Roles/Competitors/RURolePenaltyDefence.h"
#include "Match/AI/Roles/Competitors/RURoleTapRestart.h"
#include "Match/AI/Roles/Competitors/RURoleWingDefend.h"
#include "Match/AI/Roles/Competitors/RURoleFullback.h"
#include "Match/AI/Roles/Competitors/RURoleTryReaction.h"
#include "Match/AI/Roles/Competitors/RURoleKickOffChaser.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleMaul.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleSetplay.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleSetplayScrumHalf.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleSetplayPlayTheBallReceiver.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleMaulHalfback.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBall.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBallReceiver.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURolePlayTheBallDefender.h"
#include "Match/AI/Actions/RUActionTackler.h"

#include "Match/Components/RUPlayerState.h"
#include "Match/AI/Roles/Competitors/SSRoleCutScene.h"
#include "Match/AI/Roles/Competitors/RURoleCutSceneReaction.h"

#include "Match/Camera/SSCameraManager.h"

#include "Utility/RURandomNumberGenerator.h"

#include "Match/RugbyUnion/RUGameEvents.h"

//#rc3_legacy_include #include "MabProfiler.h"

#include "Match/RUAsyncLoadingEnable.h"
//#rc3_legacy_include #include "SIFAsyncLoadingThread.h"
#include "Match/RugbyUnion/RUSandboxGame.h"

#include "RugbyGameInstance.h"
#include "Match/SIFGameWorld.h"
#include "Match/AI/SetPlays/SSSetPlayManager.h"


#include "Utility/TransformUtility.h"
#include "CoreMinimal.h"
#if !UE_BUILD_SHIPPING
#include "DrawDebugHelpers.h"
#include "Utility/consoleVars.h"
#endif
#include "../Actions/RUActionPass.h"

//#define SAVEDFORMATIONSTODT 1
#pragma optimize("", off) //WJS

bool SSEVDSFormationManager::AIDisabledForCutscenes = false;

#if 0 //#rc3_legacy
//------------------------------------------------------------------------------------------------------------------
/// Since EVDS data is shared between formations and formation managers, we need to send changes to all formations
//------------------------------------------------------------------------------------------------------------------
class SSEVDSFormationEditorInterface : public MabEVDSEditor
{
private:
	typedef MabVector< SSEVDSFormationManager* > Managers;
	Managers managers;
public:
	SSEVDSFormationEditorInterface() : MabEVDSEditor() {};

	virtual void EVDSStopEditing(const MabString & /*file_name*/) {};
	virtual void EVDSStartEditing(const MabString & /*file_name*/) {};
	virtual void EVDSDataModified(const MabString & file_name )
	{
		Managers::iterator it;
		for( it = managers.begin(); it != managers.end(); ++it )
		{
			SSEVDSFormationManager* mgr = *it;
			mgr->EVDSDataModified( file_name );
		}
	}
	virtual void EVDSSetTime(const MabString &/*file_name*/, float /*time*/) {};
	virtual void EVDSGenericCommand(const MabString &/*arguments*/) {};

	void Add( SSEVDSFormationManager* mgr )
	{
		MABASSERT( std::find( managers.begin(), managers.end(), mgr ) == managers.end() );
		managers.push_back( mgr );
	}

	void Remove( SSEVDSFormationManager* mgr )
	{
		MABASSERT( std::find( managers.begin(), managers.end(), mgr ) != managers.end() );
		managers.erase( std::find( managers.begin(), managers.end(), mgr ) );
	}
};

static SSEVDSFormationEditorInterface formation_editor_interface;
#endif

// - for current formation:-
//		- morphs positions to keep the players on field (scaling)
//		- blends positions from the last formation (over a few seconds)
//		- checks for formation becoming invalid.
//
// - if current formation not valid.
//		- score every formation and choose highest scoring.
//		- if best_formation!=current
//		- current_formatiurgencyon = best_formation
//		- set blend timer to a few seconds.
//		- assign each player a 'blend_player_idx'. (based on a function of jersey number/distance/available)
//
// - SSStrategies can influence or choose the next formation but don't control player positioning.
//
// - SSRoles can be reduced as to:-
//		SSRoleFormation
//		SSRoleBallHolder + derived.
//		SSRoleKickOffKicker
//		SSRoleGetTheBall
//
// -  Actions unchanged.


const char* OVERRIDE_AREA_ROLES_PROPERTY_NAME[3] = { "overridenumplayers0", "overridenumplayers1",  "overridenumplayers2" };	// We need 2 attributes, 1 for each team because formation EVDS data is shared
const char* OVERRIDE_AREA_LINE_SPACING_NAME[3]   = { "overridelinespacing0", "overridelinespacing1", "overridelinespacing2" };	// We need 2 attributes, 1 for each team because formation EVDS data is shared

#define MIN_FORMATION_SCORE	0.0f

const int PLAYER_NUMBER_MASK	= 0x7F;
const int SLOT_USED_MASK		= 0x80;


enum {
	BITFLAGS_BALLHOLDER = 0x1,
	BITFLAGS_DISABLED = 0x2,
};

enum {
	FORMATION_LEFT=1,
	FORMATION_RIGHT=-1,
};

enum {
	SP_STATE_NONE = 0,
	SP_STATE_WAIT_THROW,
	SP_STATE_WAIT_FOR_PLAYERS,
	SP_STATE_RUNNING,
	SP_STATE_WAIT_RELEASE,
	SP_STATE_FINISHED
};

//static int xflip_mapping[] =
//{//	1, 2, 3, 4, 5, 6, 7, 8, 9,10,11,12,13,14,15
//	2, 1, 0, 4, 3, 7, 6, 5, 8, 9, 13, 12, 11, 10, 14, 15
//};



//const char *DATA_FILE_NAME = "ruged/formations/formations_evds_export.xds";
const char *DATA_FILE_NAME = "ruged/formations/formations_evds_export.xml";

/* #rc3_legacy
SSEVDSFormationManager::EVDSFormation::EVDSFormation()
: file_name()
, fm_event()
, strategy( 0 )
, origin_target( 0 )
, attacking( ERugbyFormationTeamMode::ATTACK )
, game_phase( 0 )
, xmin( 0.0f )
, xmax( 0.0f )
, zmin( 0.0f )
, zmax( 0.0f )
, x_center( false )
, z_center( false )
, allow_x_mirror( false )
, warp_to_positions( false )
, default_role_id( 0 )
, default_idle_group( 0 )
, zones()
, lines()
{}*/

///-------------------------------------------------------------------------
/// Constructor
///-------------------------------------------------------------------------

SSEVDSFormationManager::SSEVDSFormationManager(SIFGameWorld *ggame, SSTeam *tteam)
: game(ggame)
, team(tteam)
, disable_formation(false)
, disable_formation_change(false)
, disable_formation_change_during_phase(false)
, game_phase_at_formation_change( RUGamePhase::NONE )
//#rc3_legacy , heap(game->GetHeap())
//#rc3_legacy , queued_async_job(false)
, current_x_direction(FORMATION_LEFT)
//#rc3_legacy, current_strategy(ERugbyFormationStrategyType::ONE)
, formation_origin( NULL )
, formation_start_x(0.0f)
, formation_changed_this_frame(false)
, warp_next_formation(false)
, backline_players_left(0)
, backline_players_right(0)
, backline_players_total(0)
, force_position_evaluation(false)
, pass_priority_timer()
, SetplayManager(nullptr)
{
	memset(role_table,0,sizeof(role_table));
	//#rc3_legacy formation_editor_interface.Add( this );

	SetplayManager = new SSSetPlayManager(this, game);

	StartLoadingOfFormations();

	game->GetEvents()->player_swap.Add( this, &SSEVDSFormationManager::SwapPlayer );
	game->GetEvents()->player_deleted.Add( this, &SSEVDSFormationManager::RemovePlayer );
	AIDisabledForCutscenes = false;
	Reset();
}

///-------------------------------------------------------------------------
/// Reset
///-------------------------------------------------------------------------

void SSEVDSFormationManager::Reset()
{
	disable_formation = false;
	disable_formation_change = false;
	disable_formation_change_during_phase = false;
	game_phase_at_formation_change = RUGamePhase::NONE;

	mCurrentFormation = nullptr;

	current_x_direction = FORMATION_LEFT;

	formation_origin = NULL;
	formation_start_x = 0;

	backline_players_left = backline_players_right = -1;
	backline_players_total = -1;

	formation_changed_this_frame = false;
	// Warp if not in training
	warp_next_formation = ( game->GetGameSettings().game_settings.game_type != GAME_TRAINING && game->GetGameSettings().game_settings.game_type != GAME_MENU);

	// WJS RLC NOT NEEDED int numPlayersInFormation	= game->GetGameSettings().game_limits.GetNumberOfPlayersInFormation();

	// Clear current_areas for players.
	mFormationPlayers.fill(FormationPlayer());
	// WJS RLC TODO Remove MABUNUSED(numPlayersInFormation);

	force_position_evaluation = false;

	//Clear role table... probably
	//for(int i=0; i < numPlayers * playersPerTeam; ++i)
	for (int i = 0; i < (NUM_PLAYERS_INIT * NUM_PLAYERS_PER_TEAM_INIT); ++i)
	{
		role_table[i] = 0;
	}

	current_areas.clear();
	current_setplay_areas.clear();
	LoadFormationsFromDatatable();
}

void SSEVDSFormationManager::GameReset()
{
	Reset();
}
void SSEVDSFormationManager::printState()
{
	MABLOGDEBUG("FORM:");
}
///-------------------------------------------------------------------------
/// Destructor
///-------------------------------------------------------------------------

SSEVDSFormationManager::~SSEVDSFormationManager()
{
	//#rc3_legacy formation_editor_interface.Remove( this );

//#rc3_legacy
//	MabMemFree(formation_scores);
//
//	MabEVDS* evds = SIFApplication::GetApplication()->GetEVDS();
//	for ( MabVector<EVDSFormation*>::const_iterator iter = formations.begin(); iter != formations.end(); ++iter )
//	{
//		EVDSFormation *formation = *iter;
//
//		// Fix for quit bug. -Kade WW
//		MabString filename;
//#ifdef ENABLE_SEVENS_MODE
//		// A sevens game for the purpose of loading formations is defined as a game mode of R7, and not being in the main menu. - Dewald WW
//		bool isSevensGame = false;
//		if(SIFApplication::GetApplication()->GetSandboxGame())
//		{
//			isSevensGame = SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetGameMode() == GAME_MODE_SEVENS && SIFApplication::GetApplication()->GetSandboxGame()->GetGameWorld()->GetSandboxEnvironment() != SBE_MENU;
//		}
//
//		if (isSevensGame)
//			filename = MabString(0, "ruged/formations_sevens/%s",formation->file_name.c_str());
//		else
//#endif
//			filename = MabString(0, "ruged/formations/%s",formation->file_name.c_str());
//
//		evds->FreeContainer(filename);
//
//		MabMemDelete(formation);
//	}
//
	for (std::vector<SSRoleArea*>::iterator iter = current_areas.begin(); iter != current_areas.end(); ++iter)
	{
		SSRoleArea *area = *iter;
		MabMemDelete(area);
	}
	if (SetplayManager)
	{
		delete(SetplayManager);
	}
	SetplayManager = nullptr;
	game->GetEvents()->player_swap.Remove( this, &SSEVDSFormationManager::SwapPlayer );
	game->GetEvents()->player_deleted.Remove( this, &SSEVDSFormationManager::RemovePlayer );
}

///-------------------------------------------------------------------------
/// Setup load of formations.
///-------------------------------------------------------------------------

void SSEVDSFormationManager::StartLoadingOfFormations()
{
	return;
///// WJS RLC NOT NEEDED
///// WJS RLC NOT NEEDED	MabEVDS *evds = SIFApplication::GetApplication()->GetEVDS();
///// WJS RLC NOT NEEDED
///// WJS RLC NOT NEEDED#ifdef ENABLE_SEVENS_MODE
///// WJS RLC NOT NEEDED	bool isSevensGame = false;
///// WJS RLC NOT NEEDED	
///// WJS RLC NOT NEEDED	if (SIFApplication::GetApplication()->GetActiveGameWorld() && SIFApplication::GetApplication()->GetActiveGameWorld()->GetWorldId() != WORLD_ID::MENU)
///// WJS RLC NOT NEEDED	{
///// WJS RLC NOT NEEDED		isSevensGame = SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetGameMode() == GAME_MODE_SEVENS;
///// WJS RLC NOT NEEDED	}
///// WJS RLC NOT NEEDED#endif
///// WJS RLC NOT NEEDED	MabString filePath = MabString(0, "pkcmn:ruged/formations");
///// WJS RLC NOT NEEDED
///// WJS RLC NOT NEEDED	// WJS RLC ##### Do I need to check or set  League directory for formations
///// WJS RLC NOT NEEDED#ifdef ENABLE_SEVENS_MODE
///// WJS RLC NOT NEEDED	if(isSevensGame)
///// WJS RLC NOT NEEDED		filePath += "_sevens";
///// WJS RLC NOT NEEDED#endif
///// WJS RLC NOT NEEDED
///// WJS RLC NOT NEEDED#if 0 //#rc3_legacy
///// WJS RLC NOT NEEDED	MabFileNameList files;
///// WJS RLC NOT NEEDED	MabFilePath dirpath = filePath;
///// WJS RLC NOT NEEDED	if(!MabFileSystem::GetFilesInDirectory(dirpath, files, "*.xml" ))
///// WJS RLC NOT NEEDED	{
///// WJS RLC NOT NEEDED#ifdef ENABLE_SEVENS_MODE
///// WJS RLC NOT NEEDED		if(isSevensGame)
///// WJS RLC NOT NEEDED			dirpath = MabString(0,"fs:data/" PLATFORM_NAME "/ruged/formations_sevens");
///// WJS RLC NOT NEEDED		else
///// WJS RLC NOT NEEDED#endif
///// WJS RLC NOT NEEDED			dirpath = MabString(0,"fs:data/" PLATFORM_NAME "/ruged/formations");
///// WJS RLC NOT NEEDED
///// WJS RLC NOT NEEDED		MabFileSystem::GetFilesInDirectory(dirpath, files, "*.xml" );
///// WJS RLC NOT NEEDED	}
///// WJS RLC NOT NEEDED
///// WJS RLC NOT NEEDED	if(!game->IsMatch())
///// WJS RLC NOT NEEDED	{
///// WJS RLC NOT NEEDED		LoadFormation("cutscene.xml", evds, formations, heap);
///// WJS RLC NOT NEEDED	}
///// WJS RLC NOT NEEDED	else
///// WJS RLC NOT NEEDED	{
///// WJS RLC NOT NEEDED		for(MabFileNameList::iterator iter = files.begin(); iter!=files.end(); ++iter)
///// WJS RLC NOT NEEDED		{
///// WJS RLC NOT NEEDED			MabString fname = *iter;
///// WJS RLC NOT NEEDED			LoadFormation(fname, evds, formations, heap, &formation_editor_interface );
///// WJS RLC NOT NEEDED		}
///// WJS RLC NOT NEEDED	}
///// WJS RLC NOT NEEDED
///// WJS RLC NOT NEEDED	int num_formations = (int)files.size();
///// WJS RLC NOT NEEDED	formation_scores = (float*) MabMemCalloc(sizeof(float),num_formations<<1, MabMemGetDefaultObjectHeap(this));
///// WJS RLC NOT NEEDED#endif
///// WJS RLC NOT NEEDED
}

///-------------------------------------------------------------------------
/// Load a formation.
///-------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SSEVDSFormationManager::LoadFormation(const MabString& fname, MabEVDS* evds, std::vector<std::shared_ptr<EVDSFormation>>& load_formations)
{
#ifdef ENABLE_SEVENS_MODE
	const bool isSevensGame = SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetGameMode() == GAME_MODE_SEVENS;

	MabString folderConcat = isSevensGame ? "_sevens/" : "/";
#else
	MabString folderConcat = "/";
#endif

	MabString filename = "ruged/formations";
	filename += folderConcat + fname.c_str();

	const int NUM_FORMATION_HANDLE_BITS = 9;

	if(strcmp(filename.c_str(),DATA_FILE_NAME)!=0)
	{
		MabEVDSContainer* container = evds->LoadContainer(filename,NUM_FORMATION_HANDLE_BITS);

		MabEVDSEventHandles events;
		container->GetEventsByTypeAndName( "Formation", events );

		for( size_t i=0; i < events.size(); i++ )
		{
			load_formations.emplace_back(std::make_shared<EVDSFormation>());
			load_formations.back()->fm_event = events[i];
			load_formations.back()->file_name = fname;
		}
	}
}
#endif

///-------------------------------------------------------------------------
/// Setup async load of formations.
///-------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SSEVDSFormationManager::StartAsyncLoadingOfFormations()
{
	if(game->GetWorldId()!=WORLD_ID::SANDBOX || formations.size()>1 || queued_async_job)
		return;

	//#rc3_legacy SIFAsyncLoadingThread *load_thread = SIFApplication::GetApplication()->GetAsyncLoadThread();
#ifdef CHARACTER_CREATOR_BUILD
	queued_async_job = false;
#else
	queued_async_job = true;
#endif
	//#rc3_legacy load_thread->QueueCallback(SSEVDSFormationManager::LoadFormationsAsyncJob, this);
}
#endif

///-------------------------------------------------------------------------
/// Async load job to load up all formations in background.
///-------------------------------------------------------------------------

#if 0 //#rc3_legacy
void SSEVDSFormationManager::LoadFormationsAsyncJob(void *user_data, SIFAsyncLoadingThread *load_thread, bool is_abort)
{
	SSEVDSFormationManager *manager = (SSEVDSFormationManager*)user_data;
	if(is_abort)
	{
		manager->queued_async_job = false;
		return;
	}

	MABUNUSED(load_thread);

	load_thread->SetSyncLocking(true);

	MABLOGDEBUG("LoadFormationsAsyncJob");

	//------------------------------------------------------
	// Load formations, into local 'load_formations' vector.

	MabEVDS *evds = SIFApplication::GetApplication()->GetEVDS();
	MabVector<EVDSFormation*> load_formations;

#ifdef ENABLE_SEVENS_MODE
	// A sevens game for the purpose of loading formations is defined as a game mode of R7, and not being in the main menu. - Dewald WW
	bool isSevensGame = false;
	if(SIFApplication::GetApplication()->GetSandboxGame())
	{
		isSevensGame = SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetGameMode() == GAME_MODE_SEVENS && SIFApplication::GetApplication()->GetSandboxGame()->GetGameWorld()->GetSandboxEnvironment() != SBE_MENU;
	}
#endif

	MabString filePath = MabString(0, "pkcmn:ruged/formations");
#ifdef ENABLE_SEVENS_MODE
	if(isSevensGame)
		filePath += "_sevens";
#endif

	MabFileNameList files;
	MabFilePath dirpath = filePath;
	if(!MabFileSystem::GetFilesInDirectory(dirpath, files, "*.xml" ))
	{
#ifdef ENABLE_SEVENS_MODE
		if(isSevensGame)
			dirpath = MabString(0,"fs:data/" PLATFORM_NAME "/ruged/formations_sevens");
		else
#endif
			dirpath = MabString(0,"fs:data/" PLATFORM_NAME "/ruged/formations");

		MabFileSystem::GetFilesInDirectory(dirpath, files, "*.xml" );
	}

	for(MabFileNameList::iterator iter = files.begin(); iter!=files.end(); ++iter)
	{
		MabString fname = *iter;
		LoadFormation(fname, evds, load_formations,manager->heap, &formation_editor_interface);
		MabCore::Sleep(3);
	}

	MABLOGDEBUG("LoadFormationsAsyncJob (2)");

	//------------------------------------------------------------------
	// Then in sync-lock section replace formation managers with loaded.

	load_thread->SyncLock();

#ifdef ENABLE_SEVENS_MODE
	MabString folderConcat = isSevensGame ? "_sevens/" : "/";
#else
	MabString folderConcat = "/";
#endif
	for ( MabVector<EVDSFormation*>::iterator iter = manager->formations.begin(); iter != manager->formations.end(); ++iter )
	{
		EVDSFormation *formation = *iter;
		MabString filename = "ruged/formations";
		filename += folderConcat + formation->file_name.c_str();
		evds->FreeContainer(filename);
		MabMemDelete(formation);
	}

	manager->formations.clear();
	for ( MabVector<EVDSFormation*>::const_iterator iter = load_formations.begin(); iter != load_formations.end(); ++iter )
	{
		EVDSFormation *formation = *iter;
		manager->formations.push_back(formation);
	}

	manager->Reset();

	load_thread->SyncUnlock();
	load_thread->SetSyncLocking(false);

	manager->queued_async_job = false;

	MABLOGDEBUG("LoadFormationsAsyncJob: Done");
}
#endif

///-------------------------------------------------------------------------
/// Setup the EVDSFormations in 'formations' from the evds data
///-------------------------------------------------------------------------

template <typename T>
FDataTableRowHandle SSEVDSFormationManager::AddOrFindRow(UDataTable * inDatatable, T row)
{
	if (inDatatable)
	{
		FString contextString;
		TArray<FName> rowNames = inDatatable->GetRowNames();
		//for (FName &rowName : rowNames)
		//{
		//	if (T * comparisonRow = inDatatable->FindRow<T>(rowName, contextString))
		//	{
		//		if (comparisonRow && *comparisonRow == row)
		//		{
		//			FDataTableRowHandle returnHandle = FDataTableRowHandle();
		//			returnHandle.DataTable = inDatatable;
		//			returnHandle.RowName = FName(rowName);

		//			return returnHandle;			//Match row found, so return that row reference
		//		}
		//	}
		//}

		//If no matching row is found, create one.
		FName name = FName(*row.name);
		FName nameWithSuffix = name;

		//Check if a row with the same name exists
		if (inDatatable->FindRow<T>(name, contextString, false))
		{
			MABASSERTMSG(false, "Two identically names formations found");
			return FDataTableRowHandle();
		}
		inDatatable->AddRow(nameWithSuffix, row);
		FDataTableRowHandle returnHandle = FDataTableRowHandle();
		returnHandle.DataTable = inDatatable;
		returnHandle.RowName = nameWithSuffix;
		return returnHandle;
	}

	return FDataTableRowHandle();
}

void SSEVDSFormationManager::LoadFormationsFromDatatable()
{
	mFormations.clear();

#ifndef SAVEDFORMATIONSTODT
	bool isSevensGame = false;
	if (SIFApplication::GetApplication()->GetActiveGameWorld() && SIFApplication::GetApplication()->GetActiveGameWorld()->GetWorldId() != WORLD_ID::MENU)
	{
		isSevensGame = SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetGameMode() == GAME_MODE_SEVENS;
	}

	if (false == isSevensGame)
	{
		// WJS RLC Changed to Thirteens if (UDataTable * fifteensFormationsDt = SIFApplication::GetApplication()->GetFifteensFormationsDt())
		if (UDataTable* formationsDt = SIFApplication::GetApplication()->GetThirteensFormationsDt())
		{
			FString contextString;
			TArray<FSerialiseFormation*> tempFormations;
			formationsDt->GetAllRows<FSerialiseFormation>(contextString, tempFormations);
			for (FSerialiseFormation * formation : tempFormations)
			{
				const FSerialiseFormation tempFormation = *formation;
				mFormations.push_back(std::make_unique<FSerialiseFormation>(tempFormation));
			}
		}

		// WJS RLC TODO Check that the 15's set Plays are ok.
		if (UDataTable * fifteensSetplaysDt = SIFApplication::GetApplication()->GetFifteensSetplaysDt())
		{
			FString contextString;
			TArray<FSerialiseFormation*> tempFormations;
			fifteensSetplaysDt->GetAllRows<FSerialiseFormation>(contextString, tempFormations);
			for (FSerialiseFormation * formation : tempFormations)
			{
				const FSerialiseFormation tempFormation = *formation;
				mFormations.push_back(std::make_unique<FSerialiseFormation>(tempFormation));
			}
		}
	}
	else
	{
		if (UDataTable * sevensFormationsDt = SIFApplication::GetApplication()->GetSevensFormationsDt())
		{
			FString contextString;
			TArray<FSerialiseFormation*> tempFormations;
			sevensFormationsDt->GetAllRows<FSerialiseFormation>(contextString, tempFormations);
			for (FSerialiseFormation * formation : tempFormations)
			{
				const FSerialiseFormation tempFormation = *formation;
				mFormations.push_back(std::make_unique<FSerialiseFormation>(tempFormation));
			}
		}
		if (UDataTable * sevensSetplaysDt = SIFApplication::GetApplication()->GetSevensSetplaysDt())
		{
			FString contextString;
			TArray<FSerialiseFormation*> tempFormations;
			sevensSetplaysDt->GetAllRows<FSerialiseFormation>(contextString, tempFormations);
			for (FSerialiseFormation * formation : tempFormations)
			{
				const FSerialiseFormation tempFormation = *formation;
				mFormations.push_back(std::make_unique<FSerialiseFormation>(tempFormation));
			}
		}

	}

#else
	TArray<FSerialiseFormation> fifteensFormations;
	for (const FString& fileName : evds::get_formation_name_list(false))
	{
		mFormations.push_back(evds::construct_formation(fileName));
		fifteensFormations.Add(*evds::construct_formation(fileName).get());
	}

	for (FSerialiseFormation formation : fifteensFormations)
	{
		if (!SetplayManager->defaultSetplays.Contains(formation.name))
		{
			if (UDataTable * fifteensFormationsDt = SIFApplication::GetApplication()->GetFifteensFormationsDt())
			{
				AddOrFindRow<FSerialiseFormation>(fifteensFormationsDt, formation);
			}
		}
	}
	
	TArray<FSerialiseFormation> sevensFormations;
	for (const FString& fileName : evds::get_formation_name_list(true))
	{
		mFormations.push_back(evds::construct_formation(fileName, true));
		sevensFormations.Add(*evds::construct_formation(fileName, true).get());
	}

	for (FSerialiseFormation formation : sevensFormations)
	{
		if (UDataTable * sevensFormationsDt = SIFApplication::GetApplication()->GetSevensFormationsDt())
		{
			if (!SetplayManager->defaultSetplays.Contains(formation.name))
			{
				AddOrFindRow<FSerialiseFormation>(sevensFormationsDt, formation);
			}
		}
	}

#endif


	if (SetplayManager)
	{
		SetplayManager->PopulateDefaultSetplays();
	}

#if 0
	//const MabEVDSEventHandle myzones;
	for (std::shared_ptr<EVDSFormation>& formation : load_formations)
	{
		//CreateFormationFromFile will clear the arrays before filling the data.
		UFormationsManager::CreateFormationFromFile(formation->file_name.c_str() , formation->gRugbyFormation, formation->gFormationZoneInfo, formation->gFormationLineInfo);
		MabStringHelper::Strcpy( formation->name, TCHAR_TO_ANSI(*formation->gRugbyFormation.FormationName));

		////MABLOGDEBUG( formation->name );
		//MabEVDSEventHandle conditions = formation->fm_event->GetHandle< MabEVDSEvent >("conditions");

		formation->strategy = formation->gRugbyFormation.strategy;//conditions->GetParam<int>( "strategy", 0);
		formation->origin_target = formation->gRugbyFormation.origin_target;//conditions->GetParam<int>( "origintarget", GSFMT_TRACKBALL );
		//MABASSERTMSG( formation->origin_target != GSFMT_INHERIT, "Formations targets not allowed to be inherit - only for zones/lines");
		formation->attacking = formation->gRugbyFormation.attacking; //conditions->GetParam<int>( "attacking", ERugbyFormationTeamMode::ATTACK );
		formation->game_phase = formation->gRugbyFormation.game_phase;//conditions->GetParam<int>("gamephase",-1);
		//formation->warp_to_positions = conditions->GetParam<int>("warptopositions",0)!=0;
		formation->xmin = formation->gRugbyFormation.xmin;
		formation->xmax = formation->gRugbyFormation.xmax;
		formation->zmin = formation->gRugbyFormation.zmin;
		formation->zmax = formation->gRugbyFormation.zmax;

		//formation->default_role_id = formation->fm_event->GetParam<int>("defaultrole",FMROLE_FORMATION);
		formation->default_idle_group = formation->gRugbyFormation.default_idle_group;//conditions->GetParam<int>("idlegroup",ERugbyFormationIdleGroup::NONE);
		/*if (formation->gFormationZoneInfo.Num())
			formation->zoneptr = &formation->gFormationZoneInfo[0];
*/
		/*if (gFormationLineInfo.Num())
			formation->lineptr = &gFormationLineInfo[0];*/

		//formation->x_center = conditions->GetParam<int>("xcenter",0)!=0;
		//formation->z_center = conditions->GetParam<int>("zcenter",0)!=0;
		//MABASSERTMSG( formation->x_center == false && formation->z_center == false, "x_center and z_center are deprecated - user origintargets");
		//formation->allow_x_mirror = conditions->GetParam<int>("xmirror",0)!=0;
		formation->lines.clear();
		formation->zones.clear();
		//formation->lines.push_back(myzones);
		//formation->fm_event->GetHandles( "zones", formation->zones );
		//formation->fm_event->GetHandles( "lines", formation->lines );
		//MABLOGDEBUG("********** FORMATION: %s,%d,%d",formation->name,num_zones,num_lines);
	}
#endif


}

///-------------------------------------------------------------------------
///  Expand roles/zones etc to allow faster access (for AssignRoles etc...)
///-------------------------------------------------------------------------

void SSEVDSFormationManager::SetupCurrentFormationData()
{
	// If no current formation set and no set play running - we have nothing to do
	if (mCurrentFormation == nullptr)
		return;

	int numZones = 0;
	int numLines = 0;

	numZones += mCurrentFormation->zones.Num();
	numLines += mCurrentFormation->lines.Num();

/*	if (SetplayManager->GetCurrentSetplay())
	{
		numZones += SetplayManager->GetCurrentSetplay()->zones.Num();
		numLines += SetplayManager->GetCurrentSetplay()->lines.Num();
	}*/

	current_areas.resize(numZones + numLines, nullptr);
	for (int i = 0; i < numZones + numLines; i++)
	{
		if (!current_areas[i])
		{
			current_areas[i] = new SSRoleArea(*game, *this);
		}
	}


	// Initialise zones /////
	for (int i = 0; i < numZones; ++i)
	{
		if (mCurrentFormation && mCurrentFormation->zones.IsValidIndex(i))
		{
			//const FSerialiseFormationZone& zone = mCurrentFormation->zones[i];
			SSRoleArea* area = current_areas[i];
			area->zone = &mCurrentFormation->zones[i];
			area->isLine = false;
			area->spline.Clear();
			area->Reset(); // note: just does more set up

			//If on the training field, up the priority of the fullback to ensure one is used.
			if (SIFApplication::GetApplication() && SIFApplication::GetApplication()->GetActiveGameWorld())
			{
				if ((SIFApplication::GetApplication()->GetActiveGameWorld()->GetWorldIdAsString().Compare("GAME") != 0) && area && area->get_name().Compare("Fullback") == 0)
				{
					area->zone->priority = 49.0f;
				}
			}
		}
		/*else if(SetplayManager && SetplayManager->GetCurrentSetplay())
		{
			int arrayIndex = i - mCurrentFormation->zones.Num();
			const FSerialiseFormationZone& zone = SetplayManager->GetCurrentSetplay()->zones[arrayIndex];
			SSRoleArea* area = current_areas[i];
			area->zone = zone;
			area->isLine = false;
			area->spline.Clear();
			area->Reset(); // note: just does more set up
		}*/
	}

	// Initialise lines /////

	for (int i = 0; i < numLines; i++)
	{
		if (mCurrentFormation && mCurrentFormation->lines.IsValidIndex(i))
		{
			//const FSerialiseFormationLine& line = mCurrentFormation->lines[i];
			SSRoleArea* area = current_areas[i + numZones];
			area->line = &mCurrentFormation->lines[i];
			area->isLine = true;
			area->UpdateSpline();
			area->Reset(); // note: just does more set up
		}
		/*else if (SetplayManager && SetplayManager->GetCurrentSetplay())
		{
			int arrayIndex = i - mCurrentFormation->zones.Num();
			const FSerialiseFormationLine& line = SetplayManager->GetCurrentSetplay()->lines[arrayIndex];
			SSRoleArea* area = current_areas[i + numZones];
			area->line = line;
			area->isLine = true;
			area->UpdateSpline();
			area->Reset(); // note: just does more set up
		}*/
		// Some checks in here to make sure that we use the correct sor strategies with the spacing types
		// Fan sorting types must be used with fan spacing types
		//bool is_fan_spacing = (area->line_type == ERugbyFormationPathType::BIASMINSPACING || area->line_type == ERugbyFormationPathType::OPENBLIND );
		//bool is_fan_sort = (area->tsort_strategy == ERugbyFormationPlayerSortStrategy::RANDOM || area->tsort_strategy == ERugbyFormationPlayerSortStrategy::FAN_OPENBLIND);
		//MABUNUSED( (is_fan_spacing && is_fan_sort) || (!is_fan_spacing && !is_fan_sort));
		// ^^^^^
	}

	// Sort by priority - highest to lowest priority
	const auto predicate = [](const auto* a, const auto* b) { return a->get_priority() > b->get_priority(); };
	std::sort(current_areas.begin(), current_areas.end(), predicate);

	// And clear pointers to old-areas..
	for (auto& player : mFormationPlayers)
	{
		if (player.current_area && 
			player.current_area->get_roles().IsValidIndex(0) &&
			player.current_area->get_roles()[0].role != ERugbyFormationRole::SETPLAY && 
			player.current_area->get_roles()[0].role != ERugbyFormationRole::SETPLAY_SCRUM_HALF &&
			player.current_area->get_roles()[0].role != ERugbyFormationRole::SETPLAY_PTB_RECEIVER)
		{
			player.current_area = nullptr;
		}
	}
}

void SSEVDSFormationManager::SetupCurrentSetplayData()
{
	int numZones = 0;
	int numLines = 0;

	if (SetplayManager && SetplayManager->GetCurrentSetplay())
	{
		numZones += SetplayManager->GetCurrentSetplay()->zones.Num();
		numLines += mCurrentSetplay->lines.Num();
	}

	current_setplay_areas.resize(numZones + numLines, nullptr);
	for (int i = 0; i < numZones + numLines; i++)
	{
		if (!current_setplay_areas[i])
		{
			current_setplay_areas[i] = new SSRoleArea(*game, *this);
		}
	}
	

	// Initialise zones /////
	for (int i = 0; i < numZones; ++i)
	{
		if(SetplayManager && SetplayManager->GetCurrentSetplay())
		{
			//const FSerialiseFormationZone& zone = SetplayManager->GetCurrentSetplay()->zones[i];
			SSRoleArea* area = current_setplay_areas[i];
			area->zone = &SetplayManager->GetCurrentSetplay()->zones[i];
			area->isLine = false;
			area->spline.Clear();
			area->Reset(); // note: just does more set up
		}
	}

	for (int i = 0; i < numLines; i++)
	{
		if (SetplayManager && SetplayManager->GetCurrentSetplay())
		{
			SSRoleArea* area = current_setplay_areas[i + numZones];
			area->line = &SetplayManager->GetCurrentSetplay()->lines[i];
			area->isLine = true;
			area->UpdateSpline();
			area->Reset(); // note: just does more set up
		}
	}

	// Sort by priority - highest to lowest priority
	const auto predicate = [](const auto* a, const auto* b) { return a->get_priority() > b->get_priority(); };
	std::sort(current_setplay_areas.begin(), current_setplay_areas.end(), predicate);

	// And clear pointers to old-areas..
	for (auto& player : mFormationPlayers)
	{
		if (player.current_area &&
			player.current_area->get_roles().IsValidIndex(0) &&
			player.current_area->get_roles()[0].role == ERugbyFormationRole::SETPLAY &&
			player.current_area->get_roles()[0].role == ERugbyFormationRole::SETPLAY_SCRUM_HALF &&
			player.current_area->get_roles()[0].role == ERugbyFormationRole::SETPLAY_PTB_RECEIVER)
		{
			player.current_area = nullptr;
		}
	}
}

SSRoleArea* SSEVDSFormationManager::GetAreaByName(const FString& name)
{
	for (SSRoleArea* area : current_areas) 
	{
		if (MabStringHelper::StringCompareIgnoreCase(area->get_name(), name) == 0) 
		{
			return area;
		}
	}

	return nullptr;
}

const FString& SSEVDSFormationManager::GetCurrentFormationName()
{
	MABASSERT(mCurrentFormation != nullptr);

	return mCurrentFormation->name;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void SSEVDSFormationManager::SetWarpOnCutSceneFinish()
{
	if (mCurrentFormation != nullptr && mCurrentFormation->conditions.gamePhase == ERugbyFormationGamePhase::CUTSCENE)
	{
		warp_next_formation = true;
	}
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

FVector SSEVDSFormationManager::GetOrigin()
{
	MABASSERT(formation_origin);
	if(!formation_origin) return FVector::ZeroVector;
	return formation_origin->GetOrigin();
}

///-------------------------------------------------------------------------
/// Update logic.
///-------------------------------------------------------------------------

void SSEVDSFormationManager::UpdateLogic(float delta_game_time)
{
	if(disable_formation)
		return;

	if (SetplayManager)
	{
		SetplayManager->UpdateSimulation(delta_game_time);
	}

	// During cutscenes, override the phase, done here to guarantee correct ordering.	
	SSCutSceneManager *cutscene_manager = game->GetCutSceneManager();
	if(cutscene_manager->IsSimulationDisabled())
	//if (cutscene_manager->IsCinematicRunning())
	{
		RUGameState *game_state = game->GetGameState();
		RUGamePhase phase = cutscene_manager->GetGamePhaseOverride();
		if(phase!=game_state->GetPhase())
			game_state->SetPhase(phase);
	}

	/// See if the formation needs to change
	MAB_PROFILE_SECTION_START(profile1, "FM: UpdateCurrentFormation");
	bool formation_changed = UpdateCurrentFormation();
	MAB_PROFILE_SECTION_END(profile1);

	if (formation_changed)
	{
		wwNETWORK_TRACE_JG("SSEVDSFormationManager::UpdateLogic New formation %s", mCurrentFormation ? TCHAR_TO_UTF8(*mCurrentFormation->name) : "NULL");
	}

	/// Update player roles
	MAB_PROFILE_SECTION_START(profile2, "FM: UpdateRoles");
	if(!formation_changed)
	{
		UpdateRoles();
	}
	MAB_PROFILE_SECTION_END(profile2);

	// jamesg - not sure if the formation should ever be null
	if (mCurrentFormation == nullptr)
		return;


	///------------------------------------------------------------------------
	/// Calculate formation origin
	///------------------------------------------------------------------------

	UpdateFormationOrigin();

	/// Update all areas in current formation
	MAB_PROFILE_SECTION_START(profile3, "FM: Update-Areas");
	for (SSRoleArea* area : current_areas)
	{
		area->Update();
	}
	for (SSRoleArea* area : current_setplay_areas)
	{
		area->Update();
	}
	MAB_PROFILE_SECTION_END(profile3);

	///--------------------------------------------
	/// Update pass priorities
	///--------------------------------------------
	if ( team == game->GetGameState()->GetAttackingTeam() && game->GetGameState()->GetBallHolder() != NULL )
	{
		MAB_PROFILE_SECTION_START(profile4, "FM: UpdatePassPriorities");
		UpdatePassPriorities( PASS_PRI_ALL );
		MAB_PROFILE_SECTION_END(profile4);
	}

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
	UpdateDebug();
#endif
}


///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void SSEVDSFormationManager::UpdateLogicPostPlayerUpdate()
{
	//-----------------------------------------------
	// Warp players to positions if formation->warp_to_positions is set.
	if (mCurrentFormation && (mCurrentFormation->conditions.warpToPositions || warp_next_formation) && !warp_completed/*formation_changed_this_frame*/ )
	{
		if (game->GetGameState()->GetPhase() == SCRUM)
		{
			if (mCurrentSetplay)
			{
				return;
			}
		}

		warp_next_formation = false;
		warp_completed = true;


		/// TYRONE : Prime this so formation variables get reset
		/// The previous logic assumed that all roles would update waypooints every frame which is not alwys the case
		/// Some roles (SSRoleformation) need to do a standard formation movement to set their target position on warp

		// UpdateLogic - with formation change disabled.
		//	FIXES:	   - If formation change happens (x-direction swap most likely) the role areas will not have setup before warp occurs,
		//				 causing all players in an area to stand at its center.
		bool previous_setting = disable_formation_change;
		disable_formation_change = true;
		UpdateLogic( SIMULATION_INTERVAL );
		disable_formation_change = previous_setting;

		/// NOTE - we prime TWICE to make sure all players relative to ball positions etc... get set
		//// THIS IS INTENTIONAL AND CORRECT
		for( int i = 0; i < 2; i++ )
		{
			/// Warp all players
			for (ARugbyCharacter * player : team->GetPlayers())
			{
				//If this is the first check, populate the players array
				if (!playersWarped.Contains(player))
				{
					playersWarped.Add(player, false);
				}

				//If this player hasn't warped, do the warp
				if (bool * warpedPtr = playersWarped.Find(player))
				{
					if (!(*warpedPtr))
					{
						//If the player warps succesfully, mark that they warped
						bool warpSuccesful = game->GetStrategyHelper()->WarpPlayerToWaypoint(player);
						if (!warpSuccesful)
						{
							warp_completed = false;
						}
						else
						{
							playersWarped[player] = true;
						}
					}
				}
			}

			/// Prime the ball so it is in the correct place also
			game->GetBall()->Update( SIMULATION_INTERVAL );

			/// Warp officials
			if (team == game->GetGameState()->GetAttackingTeam() && game->GetOfficialsTeam())
			{
				for (ARugbyCharacter * official : game->GetOfficialsTeam()->GetPlayers())
				{
					if (!playersWarped.Contains(official))
					{
						playersWarped.Add(official, false);
					}


					//If this player hasn't warped, do the warp
					if (bool * warpedPtr = playersWarped.Find(official))
					{
						if (!(*warpedPtr))
						{
							bool warpSuccesful = game->GetStrategyHelper()->WarpPlayerToWaypoint(official);
							if (!warpSuccesful)
							{
								warp_completed = false;
							}
							else
							{
								playersWarped[official] = true;
							}
						}
					}
				}
			}

		}

		game->GetCameraManager()->SetCameraSnap();
	}
}


//-----------------------------------------------
// Players are in position for both teams, roles have been assigned and ball holder *should* have been assigned
/// So we can update human player assignments
void SSEVDSFormationManager::UpdateLogicPostPlayerUpdate2()
{
	if(formation_changed_this_frame)
	{

		SET_CHANGEPLAYER_SECTION( game, "FORMCHANGE" );

		switch( mCurrentFormation->conditions.gamePhase )
		{
		case ERugbyFormationGamePhase::KICKOFF:
		case ERugbyFormationGamePhase::DROPOUT:
		case ERugbyFormationGamePhase::PENALTY:
		case ERugbyFormationGamePhase::DECIDE_LINEOUT_NUMBERS:
		case ERugbyFormationGamePhase::SCRUM:
		case ERugbyFormationGamePhase::PLAY_THE_BALL:
			team->AssignHumanPlayersOnPhaseChange( game->GetGameState()->GetPlayRestartPosition(), true );
			break;
		default:
			break;
		}

		SET_CHANGEPLAYER_SECTION( game, NULL );
	}

	formation_changed_this_frame = false;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

const SSRoleArea* SSEVDSFormationManager::GetPlayerArea(ARugbyCharacter* player)
{
	const int player_no = player->GetAttributes()->GetTeamIndex();
	return mFormationPlayers[player_no].current_area;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

ARugbyCharacter* SSEVDSFormationManager::GetSetplayFirstReceiver()
{
	for (int i = 0; i < mFormationPlayers.size(); i++)
	{
		if (mFormationPlayers[i].current_area && mFormationPlayers[i].current_area->get_name().Compare("FlyHalf") == 0)
		{
			return team->GetPlayer(i);
		}
	}
	
	return nullptr;
}

///-------------------------------------------------------------------------
/// MovePlayersToKickOffPositions - called at start of match.
///-------------------------------------------------------------------------

void SSEVDSFormationManager::MovePlayersToKickOffPositions()
{
	wwNETWORK_TRACE_JG("Moving players to kick off Position for team %s", *FString(((RUTeam*)team)->GetDbTeam().GetName()));

	SIFRugbyCharacterList players = team->GetPlayers();
	for( size_t p = 0; p < players.size(); ++p )
	{
		ARugbyCharacter* player = players[p];
		DoStandardFormationMovement(player);
	}
	game->GetStrategyHelper()->WarpAllPlayersToWaypoints((RUTeam*)team);
	game->GetStrategyHelper()->WarpAllPlayersToWaypoints((RUTeam*)team);
	
	game->GetCameraManager()->SetCameraSnap(0);
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void SSEVDSFormationManager::ForceFormation(const FString& name, int horizontalDirection)
{
	MABASSERT(horizontalDirection==1 || horizontalDirection==-1);
	MABASSERT(game->GetGameSettings().game_settings.game_type == GAME_TRAINING);

	mCurrentFormation = nullptr;

	EVDSFormation* formation = GetFormationByName(name);
	if ( formation != nullptr )
	{
		SetCurrentFormation(formation, horizontalDirection);
		current_x_direction = horizontalDirection;
	}

	disable_formation_change = (mCurrentFormation != nullptr);
	disable_formation = false;
}

void SSEVDSFormationManager::ForceClearFormation()
{
	MABASSERT(game->GetGameSettings().game_settings.game_type == GAME_TRAINING);

	disable_formation_change = true;
	disable_formation = true;
}

FSerialiseFormation* SSEVDSFormationManager::GetFormationByName(const FString& name)
{
	for (const auto& formation : mFormations) 
	{
		if (formation->name == name) 
		{
			return formation.get();
		}
	}

	return nullptr;
}


///*******************************************************************************************************************************
///*******************************************************************************************************************************
/// Formation scoring + changing.
///*******************************************************************************************************************************
///*******************************************************************************************************************************

void SSEVDSFormationManager::DisableFormationChangeDuringPhase()
{
	disable_formation_change_during_phase = true;
	game_phase_at_formation_change = game->GetGameState()->GetPhase();
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------
void SSEVDSFormationManager::EnableFormationChangeDuringPhase()
{
	disable_formation_change_during_phase = false;
	disable_formation_change = false;
}

///-------------------------------------------------------------------------
/// Needed to format mirrored formations like lineouts
///-------------------------------------------------------------------------
void SSEVDSFormationManager::ForceUpdateCurrentFormation()
{
	UpdateCurrentFormation();
}

///-------------------------------------------------------------------------
/// UpdateCurrentFormation
///-------------------------------------------------------------------------

bool SSEVDSFormationManager::UpdateCurrentFormation()
{
	if(disable_formation_change)
		return false;

	RUGameState *game_state = game->GetGameState();

	//#rc3_legacy
	SSCutSceneManager *cutscene_manager = game->GetCutSceneManager();
	if(!cutscene_manager->IsSimulationDisabled())
	//if (!cutscene_manager->IsCinematicRunning())
	{
		/// If we have been requested to not change formation during a phase then handle this
		if ( disable_formation_change_during_phase )
		{
			if ( game->GetGameState()->GetPhase() == game_phase_at_formation_change )
				return false;
			else
			{
				disable_formation_change_during_phase = false;
				game_phase_at_formation_change = RUGamePhase::NONE;
			}
		}
	}

	FSerialiseFormation* best_formation = nullptr;
	float best_score = MIN_FORMATION_SCORE-1.0f;
	int	best_direction = 0;

	FVector follow_pos = formation_origin != NULL ? formation_origin->GetOrigin() : FVector::ZeroVector;

	float play_dir = (float) team->GetPlayDirection();
	follow_pos.x *= -play_dir;
	follow_pos.z *= -play_dir;

	bool is_attacking = (team == game_state->GetAttackingTeam());

	/// Iterate over all formations and left/right permutations (if mirrored)
	/// Finding the formation with the best score
	static const int NORMAL_TEST_DIRECTIONS[] = { FORMATION_LEFT };
	static const int MIRRORED_TEST_DIRECTIONS[] = { FORMATION_LEFT, FORMATION_RIGHT };

	// Clear all formation scores
	formation_scores.assign(mFormations.size() * 2, -1.f); // * 2 for each possible direction of formation

	int i = 0;
	for (const auto& formation : mFormations)
	{
		// Setup directions to iterate over
		const int n_directions = formation->conditions.xMirror ? 2 : 1;
		const int* directions = formation->conditions.xMirror ? MIRRORED_TEST_DIRECTIONS : NORMAL_TEST_DIRECTIONS;

		for ( int j = 0; j < n_directions; j++ )
		{
			float score = GetFormationScore(formation.get(),follow_pos,is_attacking, directions[j] );
			formation_scores[i*2+j] = score;

			if(score>best_score)
			{
				best_score = score;
				best_formation = formation.get();
				best_direction = directions[j];
			}
		}

		i++;
	}

	// jamesg - wtf? if best_formation is Tutorial, change it to Tutorial: Defence ????
	if (best_formation && !is_attacking && MabStringHelper::StringCompareIgnoreCase( best_formation->name, "Tutorial" ) == 0 )
	{
		for ( unsigned int j = 0; j < mFormations.size(); ++j )
		{
			if ( MabStringHelper::StringCompareIgnoreCase( mFormations[j]->name, "Tutorial: Defence" ) == 0 )
			{
				best_formation = mFormations[j].get();
				best_direction = 0;
				break;
			}
		}
	}

	// Set the current formation to the new best formation
	return SetCurrentFormation(best_formation, best_direction);
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

bool SSEVDSFormationManager::SetCurrentFormation(EVDSFormation *best_formation, int best_direction)
{
	if((best_formation && (best_formation!=mCurrentFormation || best_direction!=current_x_direction)) || (SetplayManager && SetplayManager->GetCurrentSetplay() != mCurrentSetplay))
	{
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
		FString DebugCurrentFormation = mCurrentFormation ? mCurrentFormation->name : "NULL";
		FString DebugBestFormation = best_formation ? best_formation->name : "NULL";		
		
		UE_LOG(LogTemp, Display, TEXT("**** Change Formation: team_idx='%d' formation='%s' (from '%s') x_direction='%d' (from '%d')"),
				team->GetIndex(), *DebugBestFormation, *DebugCurrentFormation, best_direction, current_x_direction);			
#endif

#if defined ENABLE_SIF_DEBUG_DRAW && defined ENABLE_GAME_DEBUG_MENU
		//#rc3_legacy_debug_draw 
		/*SIFGameWorld* game = team->GetGame();
		SIFDebug::GetGameDebugSettings()->PushDebugString( game, RUGameDebugSettings::DP_RULES,
			MabString( 128, "**** Change Formation: team_idx=%d formation=%s (from %s) x_direction=%d (from %d)",
			team->GetIndex(), best_formation->name, mCurrentFormation?mCurrentFormation->name:"NULL", best_direction, current_x_direction ).c_str()
			);*/
#endif
		
		bool setplayChanged = false;
		if (SetplayManager && SetplayManager->GetCurrentSetplay() != mCurrentSetplay)
		{
			mCurrentSetplay = SetplayManager->GetCurrentSetplay();
			setplayChanged = true;
		}

		FVector formation_pos = formation_origin != NULL ? formation_origin->GetOrigin() : FVector::ZeroVector;
		//-----------------------------
		// Change formation
		//-----------------------------

		formation_start_x = formation_pos.x;

		mCurrentFormation = best_formation;
		current_x_direction = best_direction;
		formation_changed_this_frame = true;
		warp_completed = false;
		playersWarped.Empty();

		// Set expand formations data...
		SetupCurrentFormationData();

		if (setplayChanged)
		{
			SetupCurrentSetplayData();
		}

		if ( mCurrentFormation->conditions.gamePhase == ERugbyFormationGamePhase::QUICK_TAP_PENALTY )
			mCurrentFormation->conditions.warpToPositions = false;

		/// Clear human players on phases with warp
		if (mCurrentFormation->conditions.warpToPositions )
		{
			switch( mCurrentFormation->conditions.gamePhase )
			{
			case ERugbyFormationGamePhase::KICKOFF:
			case ERugbyFormationGamePhase::DROPOUT:
			case ERugbyFormationGamePhase::DECIDE_LINEOUT_NUMBERS:
			case ERugbyFormationGamePhase::PENALTY:
				team->UnassignHumanPlayers();
				break;
			case ERugbyFormationGamePhase::SCRUM:
			case ERugbyFormationGamePhase::PLAY_THE_BALL:
				if(!mCurrentSetplay)
					team->UnassignHumanPlayers();
				break;
			default:
				break;
			}
		}

		/// TYRONE : COllection of last minute hacks to fix issues - clean this up some day!
		{
			/// TYRONE : For want of a cleaner solution - make training field not use halfback area so player doesn't move after initial warp
			if ( game->GetGameState()->GetPhase() == RUGamePhase::PLAY && game->GetGameSettings().game_settings.game_type == GAME_TRAINING )
				OverrideAreaNumPlayers( "Halfback", 0, ERugbyFormationRole::FORMATION );

			/// TYRONE : There seems to be an occassional issue when conversion and tap restarts still have cutscenes active (at least player->GetState()->IsIncutScene() is true
			/// causing players not to be assigned initially and warp to only partially succeed.  Visual result is players move around
			/// So we check for these cases and forcible clear all roles and actions
			if ( mCurrentFormation->conditions.gamePhase == ERugbyFormationGamePhase::CONVERSION ||
				 mCurrentFormation->conditions.gamePhase == ERugbyFormationGamePhase::PENALTY ||
				 mCurrentFormation->conditions.gamePhase == ERugbyFormationGamePhase::QUICK_TAP_PENALTY ||
				 mCurrentFormation->conditions.gamePhase == ERugbyFormationGamePhase::DECIDE_LINEOUT_NUMBERS ||
				 mCurrentFormation->conditions.gamePhase == ERugbyFormationGamePhase::DROPOUT) // This was causing a kick-off rekick to not properly assign the kicker.
				game->GetStrategyHelper()->ClearRoles( static_cast< RUTeam* >( team ) );
		}

		// Assign roles.
		UpdateRoles();

		if ( mCurrentFormation->conditions.gamePhase == ERugbyFormationGamePhase::QUICK_TAP_PENALTY )
		{
			force_position_evaluation = false;
		}
		else
		{
			force_position_evaluation = true;
		}

		return true;
	}

	return false;
}

///-------------------------------------------------------------------------
/// GetFormationScore - get score for formation, best gets used.
///-------------------------------------------------------------------------

float SSEVDSFormationManager::GetFormationScore(FSerialiseFormation* formation, FVector& pos, bool is_attacking, int direction)
{
	// Attack or defensive? (or both (2))
	if (formation->conditions.attacking != ERugbyFormationTeamMode::BOTH)
	{
		if (is_attacking != (formation->conditions.attacking == ERugbyFormationTeamMode::ATTACK))
			return MIN_FORMATION_SCORE;
	}

	//#rc3_legacy
	//if (formation->strategy!=0 && formation->strategy!=current_strategy)
	//	return MIN_FORMATION_SCORE;

	//Tempprary check to stop the teams using pendulum defence while streategies aren't implemented
	if (formation->name.Compare("Pendulum: Defence") == 0)
	{
		return MIN_FORMATION_SCORE;
	}
	
	// Game phase...

	float score = 0.01f;			// Better than 0!

	RUGameState *game_state = game->GetGameState();
	RUGamePhase phase =  game_state->GetPhase();

	switch(formation->conditions.gamePhase)
	{
	case ERugbyFormationGamePhase::CUTSCENE:
//		if(!game->GetCutSceneManager()->IsSimulationDisabled() && phase != RUGamePhase::PRE_GAME)
//		{
//			return MIN_FORMATION_SCORE;
//		}
//		else
//		{
//			return 1000.0f;
//		}
		return MIN_FORMATION_SCORE;
		break;

	case ERugbyFormationGamePhase::STANDARDPLAY:
		{
			bool state_ok = (phase == RUGamePhase::PLAY && !game->GetStrategyHelper()->GetLastBallFreeInfo().IsBallLoose());
			if(!state_ok)
				return MIN_FORMATION_SCORE;
		}
		break;

	case ERugbyFormationGamePhase::SIMULATION:
		if(phase!=RUGamePhase::SIMULATION)
			return MIN_FORMATION_SCORE;
		break;

	case ERugbyFormationGamePhase::HALFTIME:
		if(phase!=RUGamePhase::HALF_TIME)
			return MIN_FORMATION_SCORE;
		break;

	case ERugbyFormationGamePhase::FULLTIME:
		if(phase!=RUGamePhase::POST_GAME)
			return MIN_FORMATION_SCORE;
		break;

	case ERugbyFormationGamePhase::KICKOFF:
		if(phase!=RUGamePhase::KICK_OFF)
			return MIN_FORMATION_SCORE;
		break;

	case ERugbyFormationGamePhase::PRE_KICKOFF:
		if(phase!=RUGamePhase::PRE_KICK_OFF && phase!=RUGamePhase::PRE_GAME)
			return MIN_FORMATION_SCORE;
		break;

	case ERugbyFormationGamePhase::TRY_REACTION:
		if(phase!=RUGamePhase::TRY_REACTION)
			return MIN_FORMATION_SCORE;
		break;

	case ERugbyFormationGamePhase::TRY_CUTSCENE:
		if(phase!=RUGamePhase::TRY_CUTSCENE)
			return MIN_FORMATION_SCORE;
		break;

	//case ERugbyFormationGamePhase::TACKLE:
	//	if(phase!=GAME_PHASE_TACKLE)
	//		return MAX_FORMATION_SCORE;
	//	break;

	case ERugbyFormationGamePhase::SCRUM:
		{
			if(phase!=RUGamePhase::SCRUM)
				return MIN_FORMATION_SCORE;
			else
				return 1000.0f;
		}
		break;

	case ERugbyFormationGamePhase::PRE_DROPOUT:
		if(phase!=RUGamePhase::PRE_DROPOUT)
			return MIN_FORMATION_SCORE;
		break;

	case ERugbyFormationGamePhase::PRE_KICKFORPOINTS:
		{
			bool state_ok = (  phase == RUGamePhase::PRE_PENALTY_SHOOT_FOR_GOAL
						|| phase == RUGamePhase::POST_CONVERSION );
			if(!state_ok)
				return MIN_FORMATION_SCORE;
		}
		break;

	case ERugbyFormationGamePhase::CONVERSION:
		{
			bool state_ok = (phase == RUGamePhase::CONVERSION ||
				// Penalty shoot for goal during fifteens will use the conversion formations. Sevens has a special case
				(phase == RUGamePhase::PENALTY_SHOOT_FOR_GOAL && game->GetGameSettings().game_settings.GameModeIsR13()));

			if(!state_ok)
				return MIN_FORMATION_SCORE;
			else
				return 1000.0f;
		}
		break;

	case ERugbyFormationGamePhase::PENALTY_SHOOT_FOR_GOAL:
		{
			// Sevens we have a special case during penalty goal shooting.
			bool state_ok = phase == RUGamePhase::PENALTY_SHOOT_FOR_GOAL && game->GetGameSettings().game_settings.GameModeIsR7();
			if(!state_ok)
				return MIN_FORMATION_SCORE;
			else
				return 1000.0f;
		}
		break;

	case ERugbyFormationGamePhase::RUCK:
		{
			if(phase!=RUGamePhase::RUCK)
				return MIN_FORMATION_SCORE;
			else
				score = 1000.0f;
		}
		break;

	case ERugbyFormationGamePhase::MAUL:
		{
			if(phase!=RUGamePhase::MAUL)
				return MIN_FORMATION_SCORE;
			else
				score = 1000.0f;
		}
		break;

	case ERugbyFormationGamePhase::FREEBALL:
		{
			bool state_ok =  (phase == RUGamePhase::PLAY && game->GetStrategyHelper()->GetLastBallFreeInfo().IsBallLoose());
//			bool state_ok = game->GetStrategyHelper()->IsBallReallyFree();
			if (!state_ok)
				return MIN_FORMATION_SCORE;
			else
				return 1000.0f;
		}
		break;

	case ERugbyFormationGamePhase::QUICK_LINEOUT:
		{
			bool state_ok = (phase == RUGamePhase::QUICK_LINEOUT);
			if (!state_ok)
				return MIN_FORMATION_SCORE;

			FVector ball_pos = game->GetBall()->GetCurrentPosition();
			float play_dir = (float) team->GetPlayDirection();
			ball_pos.x *= -play_dir;
			ball_pos.z *= -play_dir;

			if(direction>0)		/// Off right..
			{
				if(ball_pos.x<0)
					score = 1000.0f;
				else
					return MIN_FORMATION_SCORE;
			}
			else				/// Off left.
			{
				if(ball_pos.x>0)
					score = 1000.0f;
				else
					return MIN_FORMATION_SCORE;
			}
		}
		break;

	case ERugbyFormationGamePhase::DECIDE_LINEOUT_NUMBERS:
		{
			bool state_ok = (phase == RUGamePhase::DECIDE_LINEOUT_NUMBERS );

			if (!state_ok)
				return MIN_FORMATION_SCORE;
			else
				return score = 1000.0f;
		}
		break;


	case ERugbyFormationGamePhase::LINEOUT:
		if(phase!=RUGamePhase::LINEOUT)
			return MIN_FORMATION_SCORE;
		else
		{
			FVector lineout_pos = game_state->GetPlayRestartPosition();
			float play_dir = (float) team->GetPlayDirection();
			lineout_pos.x *= -play_dir;
			lineout_pos.z *= -play_dir;

			if(direction>0)		/// Off right..
			{
				if(lineout_pos.x<0)
					score = 1000.0f;
				else
					return MIN_FORMATION_SCORE;
			}
			else				/// Off left.
			{
				if(lineout_pos.x>0)
					score = 1000.0f;
				else
					return MIN_FORMATION_SCORE;
			}

			/// Check the z min and max values to see if this score applies
			float rel_z_pos = lineout_pos.z * team->GetPlayDirection();
			if ( !MabMath::InRangeInclusive( rel_z_pos, formation->conditions.zMin, formation->conditions.zMax ) )
			{
				static const float MULTIPLIER = 0.2f;
				score *= MULTIPLIER;
			}

			return score;
		}
		break;

	case ERugbyFormationGamePhase::DROPOUT:
		if(phase!=RUGamePhase::DROPOUT)
			return MIN_FORMATION_SCORE;
		break;

	case ERugbyFormationGamePhase::PENALTY:
		{
			bool state_ok = ( phase == RUGamePhase::DECISION_PENALTY || phase == RUGamePhase::DECISION || phase == RUGamePhase::PENALTY_TAP_RESTART );
			if( !state_ok )
				return MIN_FORMATION_SCORE;
			else
				score = 1000.0f;
		}
		break;

	case ERugbyFormationGamePhase::TEN_M_KICK:
		{
			bool state_ok = (phase == RUGamePhase::FREE_KICK || phase == RUGamePhase::PENALTY_KICK_FOR_TOUCH);
			if ( !state_ok )
				return MIN_FORMATION_SCORE;
			else
				score = 1000.0f; // Really want to pick this
		}
		break;

	case ERugbyFormationGamePhase::TEN_M_TAP:
		//{
		//	if ( phase != RUGamePhase::PENALTY_TAP_RESTART )
		//	{
		//		return MIN_FORMATION_SCORE;
		//	}
		//	else
		//	{
		//		// For now this is the only valid formation
		//		score = 1000.0f;
		//	}
		//}
		break;

	case ERugbyFormationGamePhase::REACTION_CUTSCENE:
		{
			bool state_ok = (phase == RUGamePhase::REACTION_CUTSCENE || phase == RUGamePhase::INJURY);
			if ( !state_ok )
				return MIN_FORMATION_SCORE;
			else
				score = 1000.0f;
		}
		break;

	case ERugbyFormationGamePhase::TUTORIALS:
		{
			if (game->GetGameSettings().game_settings.game_type == GAME_TRAINING &&
					game->GetTutorialManager()->IsTutorialRunning() )
			{
				if ( is_attacking )
				{
					// We want to override any non forced formation during tutorials
					return 500.0f;
				}
				else
				{
					return 550.0f;	// Return higher for defensive, so that the defensive team opts for tutorial_defense.
				}
			}
			else
			{
				return MIN_FORMATION_SCORE;
			}
		}
		break;

	case ERugbyFormationGamePhase::QUICK_TAP_PENALTY:
		{
			bool state_ok = ( phase == RUGamePhase::ELECT_QUICK_TAP || phase == RUGamePhase::QUICK_TAP_PENALTY );
			if( !state_ok )
			{
				return MIN_FORMATION_SCORE;
			}
			else
			{
				score = 1000.0f;
			}
		}
		break;

		/*case FORMATION_QUICK_LINEOUT:
		{
		bool state_ok = phase == RUGamePhase::QUICK_LINEOUT;
		if ( !state_ok )
		{
		return MIN_FORMATION_SCORE;
		}
		else
		{
		score = 1000.0f;
		}
		}
		break;*/

	case ERugbyFormationGamePhase::RUGBY_SEVENS_EXTRA_TIME_COIN_TOSS:
		if(phase!=RUGamePhase::EXTRA_TIME_TOSS)
			return MIN_FORMATION_SCORE;
		else
			score = 1000.0f;
		break;

	case ERugbyFormationGamePhase::PLAY_THE_BALL:
		if (phase != RUGamePhase::PLAY_THE_BALL)
			return MIN_FORMATION_SCORE;
		else
			score = 1000.0f;
		break;

	case ERugbyFormationGamePhase::SETPLAY:				//Setplays should only ever be executed by the setplay manager check at the start of this function.
	default:
		return MIN_FORMATION_SCORE;
		break;
	}

	// Check field position...
	float multiplier = 1.0f;

	float xmin = formation->conditions.xMin;
	float xmax = formation->conditions.xMax;
	float zmin = formation->conditions.zMin;
	float zmax = formation->conditions.xMin;

	if(direction!=FORMATION_LEFT)
	{
		xmin = -formation->conditions.xMax;
		xmax = -formation->conditions.xMin;
	}

	if(pos.x < xmin || pos.x > xmax || pos.z < zmin || pos.z> zmax)
	{
		multiplier = 0.2f;
	}


	if(formation==mCurrentFormation)
	{
		score += 10.0f;

		if(direction==current_x_direction)
			score += 10.0f * multiplier;
	}

	float cx = (xmax + xmin) * 0.5f;
	float cz = (zmax + zmin) * 0.5f;
	float dx = pos.x - cx;
	float dz = pos.z - cz;
	float rng = 50.0f - MabMath::Sqrt(dx*dx + dz*dz);
	MabMath::Clamp(rng,0.0f,50.0f);


	score += rng * multiplier;

	return score;
}


///-------------------------------------------------------------------------
/// GetOppositionFormationManager
///-------------------------------------------------------------------------

const SSEVDSFormationManager* SSEVDSFormationManager::GetOppositionFormationManager() const
{
	return team->GetOppositionTeam()->GetFormationManager();
}

///-------------------------------------------------------------------------
/// Given the current 'SSRoleArea' which MUST be a line,
///-------------------------------------------------------------------------

const SSRoleArea* SSEVDSFormationManager::GetOpposingLine(const SSRoleArea* line) const
{
	if (!line->isLine) return nullptr;

	// Which side is my line on (normalized)...

	float x0, x1;
	line->GetXRange(x0,x1);
	float xdir = x1 - x0;

	return GetOppositionFormationManager()->GetLineWithDirection(xdir < 0.0f ? 1 : -1);
}

///-------------------------------------------------------------------------
/// Return the first 'line' with matching 'direction' (NULL if non found)
///-------------------------------------------------------------------------

const SSRoleArea* SSEVDSFormationManager::GetLineWithDirection(int direction) const
{
	for (const SSRoleArea* area : current_areas)
	{
		if (area->isLine)
		{
			float x0, x1;
			area->GetXRange(x0,x1);
			float xdir = x1 - x0;

			if((xdir<0 && direction==-1) || (xdir>0 && direction==1))
			{
				if (area->get_name() != "LineOut") // One exception, saves placing flags in all lines.
					return area;
			}
		}
	}

	return nullptr;
}


///-------------------------------------------------------------------------
/// Give player + line, work out which player on the opposition team to mark.
///   (On opposing line with same slot idx).
/// - returns NULL if no player found.
///-------------------------------------------------------------------------

ARugbyCharacter* SSEVDSFormationManager::GetPlayerToMark(ARugbyCharacter* me)
{
	const int player_no = me->GetAttributes()->GetTeamIndex();
	const SSRoleArea* line = mFormationPlayers[player_no].current_area;

	if (line != nullptr)
	{
		const SSRoleArea* opp_line = GetOpposingLine(line);
		if (opp_line != nullptr)
		{
			const int slot_to_mark = line->what_att_slot_to_mark[player_no];
			if ( slot_to_mark != -1 )
				return opp_line->GetSlotPlayer(slot_to_mark);
		}
	}

	return nullptr;
}


///*******************************************************************************************************************************
///*******************************************************************************************************************************
/// Movement and 'slot' allocation with in SSRoleAreas.
///*******************************************************************************************************************************
///*******************************************************************************************************************************

///-------------------------------------------------------------------------
/// RefereeMovement : Do slot based movement for referee.
///-------------------------------------------------------------------------

void SSEVDSFormationManager::DoRefereeMovement(ARugbyCharacter* player)
{
	const float play_dir = -float(team->GetPlayDirection());

	for (SSRoleArea* area : current_areas)
	{
		if (area->get_player_mask() == ERugbyFormationPlayerMask::REFEREE)
		{
			MABASSERT(area->isLine == false);

			FieldExtents extents = game->GetSpatialHelper()->GetFieldExtents();
			extents.x *= 0.5f;
			float leftline = -extents.x;
			float rightline = extents.x;

			FVector targ_pos = area->GetZonePosition(play_dir);

			FVector curr_pos = player->GetMovement()->GetCurrentPosition();
			float urgency;
			ACTOR_SPEED max_actor_speed;

			urgency = GetPlayerUrgency( area, player, 0, curr_pos, max_actor_speed );
			MabMath::Clamp( urgency, 0.1f, 1.0f );

			/// Clamp in field... (x).
			MabMath::Clamp(targ_pos.x, leftline, rightline);

			player->GetMovement()->SetThrottleAndTargetSpeedByUrgency( urgency, max_actor_speed );
			wwNETWORK_TRACE_JG("DoRefereeMovement");
			wwNETWORK_TRACE_JG("SSEDVDSFormationManager");
			player->GetMovement()->SetTargetPosition(targ_pos);

#if wwDEBUG_ARB && 0
			if (player->GetVisible())
			{
				static const FVector textOffset(0.0f, -200.0f, 0.0f);
				FString debugString = FString::Printf
				(
					TEXT("Urgency: %0.2f\nMax Speed: %d\nThrottle: %0.2f\nSpeed: %0.2f\nTarget Speed: %0.2f\nFacing: %0.2f"),
					urgency,
					max_actor_speed,
					player->GetMovement()->GetAccelerationThrottle(),
					player->GetMovement()->GetCurrentSpeed(),
					player->GetMovement()->GetTargetSpeed(),
					player->GetAnimation()->GetFacingVariable()->getValue()
				);
				DrawDebugString(player->GetWorld(), textOffset, debugString, player, FColor::White, 0.0f);
				DrawDebugLine(player->GetWorld(), player->GetActorTransform().GetTranslation(), TransformUtility::ConvertScaledToUnreal(targ_pos), FColor::Yellow, false, 0.0f, 0, 5.0f);
			}
#endif
		}
	}
}

int	SSEVDSFormationManager::UpdatePlayerSlot( ARugbyCharacter* player, bool apply_standard_exclusions )
{
	if ( apply_standard_exclusions )
	{
		/// If we're in a tackle and have been incapacitated then we should not update our slot
		if ( player->GetActionManager()->IsActionRunning( ACTION_TACKLER ) )
		{
			RUActionTackler* action_tackler = player->GetActionManager()->GetAction< RUActionTackler >();
			if ( action_tackler->GetRuckTeamState() != NULL )
				return -1;
		}
	}

	SSRoleArea* area = mFormationPlayers[ player->GetAttributes()->GetTeamIndex() ].current_area;
	if ( area == nullptr )
		return -1;

	return area->UpdatePlayerSlot( player );
}

///-------------------------------------------------------------------------
/// StandardFormationMovement : Do slot based movement.
///-------------------------------------------------------------------------

/// Get the position for the formation and the urgency that we are expected to use
void SSEVDSFormationManager::GetStandardFormationMovement( ARugbyCharacter* player, FVector& returned_position, float& returned_urgency, ACTOR_SPEED& max_actor_speed )
{
	RUPlayerMovement *movement = player->GetMovement();
	RUPlayerAttributes *attribs = player->GetAttributes();
	
	MABASSERT(movement && attribs);
	
	int player_no = attribs->GetTeamIndex();

	MABASSERT(player_no >= 0 && player_no < 14);

	SSRoleArea* area = mFormationPlayers[player_no].current_area;

	/// If there is no area for this player then we have nothing to do!
	FVector position = player->GetMovement()->GetCurrentPosition();

	wwNETWORK_TRACE_JG("SSEVDSFormationManager::GetStandardFormationMovement Initial Position: x:%f y:%f z:%f", position.X, position.Y, position.Z);

	if ( area != nullptr )
	{
		/// Use the current formation area (zone) to set the player's position
		int slot_idx = area->UpdatePlayerSlot(player);

		movement->SetDebugVal(2, (float)slot_idx);

		float play_dir = -(float)team->GetPlayDirection();

		/// Part of a line formation
		if (area->isLine)
		{
			GetLineZoneMovement(area, play_dir, slot_idx, position, player, player_no);
			wwNETWORK_TRACE_JG("SSEVDSFormationManager::GetStandardFormationMovement Line Zone Position: x:%f y:%f z:%f", position.X, position.Y, position.Z);
		}
		/// We are in an elliptical zone->
		else
		{
			float time = 0.0f;
			if (SetplayManager && SetplayManager->isPlayerRunningSetplay(player))
			{
				time = SetplayManager->GetPlayerMovementWaypointTime(player, area);
			}
			GetEllipticalZoneMovement(area, position, play_dir, player, slot_idx, time);
			wwNETWORK_TRACE_JG("SSEVDSFormationManager::GetStandardFormationMovement Elliptical Zone Position: x:%f y:%f z:%f", position.X, position.Y, position.Z);
		}
	}
	else
	{
		//SIF_DEBUG_DRAW( SetBox( 9008 + player->GetAttributes()->GetNumber(), position, FVector(0.5f, 0.1f, 0.5f), MabColour::Red));
		MABLOGDEBUG("BAD FORMATION AREA=NULL!");
	}

	float urgency = GetPlayerUrgency( area, player, player_no, position, max_actor_speed);
	MabMath::Clamp( urgency, 0.1f, 1.0f );

	/// Setup return variables
	returned_urgency  = urgency;
	returned_position = position;

	wwNETWORK_TRACE_JG("SSEVDSFormationManager::GetStandardFormationMovement Final Position: x:%f y:%f z:%f", position.X, position.Y, position.Z);

	MABASSERT (FINITE(position.x) && FINITE(position.z));
}

void SSEVDSFormationManager::DoStandardFormationMovement(ARugbyCharacter* player, bool force /* = false*/ )
{
	FVector position;
	float urgency;
	ACTOR_SPEED max_actor_speed;

	RUPlayerMovement* movement = player->GetMovement();

	GetStandardFormationMovement( player, position, urgency, max_actor_speed );

	if (position.IsZero())
	{
		UE_LOG(LogTemp, Warning, TEXT("%s %s is getting a position of zero."), *FString(player->GetAttributes()->GetFirstName()), *FString(player->GetAttributes()->GetLastName()));
	}

	movement->SetDebugVal(0,urgency);

	movement->SetThrottleAndTargetSpeedByUrgency( urgency, max_actor_speed );
	wwNETWORK_TRACE_JG("SSEVDSFormationManager::DoStandardFormationMovement");	

	if (SetplayManager && SetplayManager->HasSetplayStarted() && SetplayManager->isPlayerRunningSetplay(player))
	{
		if (game && game->GetBall())
		{
			//Stay behind the ball
			if ((game->GetBall()->GetMabPosition().z - position.z) * player->GetAttributes()->GetPlayDirection() < 0)
			{
				position.z = game->GetBall()->GetMabPosition().z;
				wwNETWORK_TRACE_JG("SSEVDSFormationManager::DoStandardFormationMovement Adjusted behind ball z: %f", position.z);
			}

			//Stay behind the pass target
			if (ARugbyCharacter * ball_holder = game->GetBall()->GetHolder())
			{
				//Check this is not the ball holder
				if (ball_holder != player)
				{
					//Check there is a pass running
					if (ball_holder->GetActionManager()->IsActionRunning(ACTION_PASS))
					{
						RUActionPass * pass_action = ball_holder->GetActionManager()->GetAction<RUActionPass>();
						if (pass_action)
						{
							//Make sure it's not a dummy pass
							if (!pass_action->IsADummyPass())
							{
								ARugbyCharacter * pass_receiver = pass_action->GetTargetPlayer();
								if (pass_receiver)
								{
									//Make sure this is not the receiver
									if (pass_receiver != player)
									{
										//Only force the stay behind if the player is close to moving ahead of the receiver.
										if (FMath::Abs(pass_receiver->GetMabPosition().z - player->GetMabPosition().z) < 2.0f)
										{
											if ((pass_receiver->GetMabPosition().z - position.z) * player->GetAttributes()->GetPlayDirection() < 0)
											{
												position.z = pass_receiver->GetMabPosition().z;
												wwNETWORK_TRACE_JG("SSEVDSFormationManager::DoStandardFormationMovement Adjusted behind player z: %f", position.z);
											}
										}
									}
								}
							}
						}
					}
				}
			}

			//If they are meant to stay behind the targetted, hold their position until the selected player is ahead of them
			if (SetplayManager->ShouldPlayerStayBehindSelectedPlayer(player))
			{
				if (ARugbyCharacter * selected_player = SetplayManager->GetSetplayTargettedCharacter())
				{
					if (player != selected_player)
					{
						FVector selected_player_pos = selected_player->GetMabPosition();
						if ((selected_player_pos.z - position.z) * player->GetAttributes()->GetPlayDirection() < 0)
						{
							position.z = selected_player->GetMabPosition().z;
							wwNETWORK_TRACE_JG("SSEVDSFormationManager::DoStandardFormationMovement Adjusted behind target z: %f", position.z);
						}
					}
				}
			}
		}
	}

	movement->SetTargetPosition(position, force);
}

float SSEVDSFormationManager::GetWaypointTravelledFraction(ARugbyCharacter* player)
{
	RUPlayerMovement *movement = player->GetMovement();
	RUPlayerAttributes *attribs = player->GetAttributes();
	MABASSERT(movement && attribs);
	int player_no = attribs->GetTeamIndex();
	MABASSERT(player_no >= 0 && player_no < 14);
	SSRoleArea* area = mFormationPlayers[player_no].current_area;

	/// If there is no area for this player then we have nothing to do!
	FVector previousWaypoint;
	FVector nextWaypoint;

	float time = 0.0f;
	if (SetplayManager && SetplayManager->isPlayerRunningSetplay(player))
	{
		if (RURoleSetplay * setplayRole = player->GetRoleSafe<RURoleSetplay>())
		{
			time = setplayRole->GetFormationPositionIndex();
		}
	}
	float prevTime = time;
	float nextTime = time + 1;
	if (area)
	{
		GetEllipticalZoneMovement(area, previousWaypoint, player->GetAttributes()->GetTeam()->GetRight(), player, 0, prevTime);
		GetEllipticalZoneMovement(area, nextWaypoint, player->GetAttributes()->GetTeam()->GetRight(), player, 0, nextTime);
	}


	float distBetweenPoints = (nextWaypoint - previousWaypoint).Magnitude();
	if (distBetweenPoints == 0)
	{
		return 0;
	}
	else
	{
		return (player->GetMovement()->GetCurrentPosition() - previousWaypoint).Magnitude() / distBetweenPoints;
	}
}






///*******************************************************************************************************************************
///*******************************************************************************************************************************
/// Player removal/replacement.




///-------------------------------------------------------------------------
/// Player has been deleted, remove any references.
///-------------------------------------------------------------------------

void SSEVDSFormationManager::RemovePlayer(ARugbyCharacter* player)
{
	for (SSRoleArea* area : current_areas)
		area->RemovePlayer(player);
	for (SSRoleArea* area : current_setplay_areas)
		area->RemovePlayer(player);
}

///-------------------------------------------------------------------------
/// Player has been swapped (substituted), replace any references.
///-------------------------------------------------------------------------

void SSEVDSFormationManager::SwapPlayer(ARugbyCharacter* player, ARugbyCharacter* replacement)
{
	for (SSRoleArea* area : current_areas)
		area->SwapPlayer(player, replacement);
	for (SSRoleArea* area : current_setplay_areas)
		area->SwapPlayer(player, replacement);
}

//Temp function controlling AI for cutscene debugging #MB
void SSEVDSFormationManager::SetAIRunning(bool isRunning)
{
	AIDisabledForCutscenes = !isRunning;
}



///*******************************************************************************************************************************
///*******************************************************************************************************************************
/// SSRoleArea



///-------------------------------------------------------------------------
/// SSRoleArea: Constructor.
///-------------------------------------------------------------------------

SSRoleArea::SSRoleArea(const SIFGameWorld& _game, const SSEVDSFormationManager& _manager)
: openside_dir( OSD_LEFT )
, openside_size( OSS_BIG )
, slot_count_last_frame(0)
, players_in_area(0)
, game(&_game)
, manager(&_manager)
, isLine(false)
, doReset(false)
, spline()
, splineLength(0)
, what_att_slot_to_mark()
{
	slots.reserve( FMAREA_MAX_SLOTS );
	slots_open.reserve(FMAREA_MAX_SLOTS);
	slots_blind.reserve(FMAREA_MAX_SLOTS);
	Reset();
}

///-------------------------------------------------------------------------
/// Player has been deleted, remove any references.
///-------------------------------------------------------------------------

void SSRoleArea::RemovePlayer(ARugbyCharacter* player)
{
	for(SlotEntries::iterator iter = slots.begin(); iter!=slots.end(); )
	{
		if((*iter).player==player)
		{
			iter = slots.erase(iter);
		}
		else
		{
			++iter;
		}
	}
}

///-------------------------------------------------------------------------
/// Player has been swapped (substituted), replace any references.
///-------------------------------------------------------------------------

void SSRoleArea::SwapPlayer(ARugbyCharacter* player, ARugbyCharacter* replacement)
{
	for(size_t i=0;i<slots.size();i++)
	{
		if(slots[i].player==player)
		{
			slots[i].player = replacement;
		}
	}
}


///-------------------------------------------------------------------------
/// UpdateOpenBlind
///-------------------------------------------------------------------------

bool SSRoleArea::PickNextPlayer( SSRoleArea::SlotEntries& to_choose_from, SSRoleArea::SlotEntries& add_to, const PLAYER_POSITION* positions, const int n_positions ) const
{
	SlotEntries::iterator it;
	for( int i = 0; i < n_positions; ++i )
	{
		const PLAYER_POSITION pos = positions[i];
		for( it = to_choose_from.begin(); it != to_choose_from.end(); ++it )
		{
			ARugbyCharacter* player = (*it).player;
			if ( player->GetAttributes()->GetPlayerPosition() & pos )
			{
				add_to.push_back( *it );
				to_choose_from.erase( it );
				return true;
			}
		}
	}

	return false;
}

// WJS RLC Mimic FIVEEIGHTH_PICK_ORDER for now
const PLAYER_POSITION NUMBER_EIGHT_LOCK_FORWARD_PICK_ORDER[]	= { PP_FIVEEIGHTH, PP_HALFBACK, PP_INSIDE_CENTER_LEFTCENTRE, PP_OUTSIDE_CENTER_RIGHTCENTRE, PP_BACK, PP_FORWARD };

const PLAYER_POSITION FIVEEIGHTH_PICK_ORDER[]					= { PP_FIVEEIGHTH, PP_HALFBACK, PP_INSIDE_CENTER_LEFTCENTRE, PP_OUTSIDE_CENTER_RIGHTCENTRE, PP_BACK, PP_FORWARD };
const PLAYER_POSITION INSIDE_CENTER_LEFTCENTRE_PICK_ORDER[]		= { PP_INSIDE_CENTER_LEFTCENTRE, PP_OUTSIDE_CENTER_RIGHTCENTRE, PP_BACK, PP_FORWARD };
const PLAYER_POSITION OUTSIDE_CENTER_RIGHTCENTRE_PICK_ORDER[]	= { PP_OUTSIDE_CENTER_RIGHTCENTRE, PP_INSIDE_CENTER_LEFTCENTRE, PP_BACK, PP_FORWARD };
const PLAYER_POSITION FULLBACK_PICK_ORDER[]						= { PP_FULLBACK };
const PLAYER_POSITION LEFTWING_PICK_ORDER[]						= { PP_LEFTWING, PP_OUTSIDE_CENTER_RIGHTCENTRE, PP_INSIDE_CENTER_LEFTCENTRE, PP_BACK, PP_FORWARD };
const PLAYER_POSITION RIGHTWING_PICK_ORDER[]					= { PP_RIGHTWING, PP_OUTSIDE_CENTER_RIGHTCENTRE, PP_INSIDE_CENTER_LEFTCENTRE, PP_BACK, PP_FORWARD };
const PLAYER_POSITION OPENSIDE_FLANKER_PICK_ORDER[]				= { /* WJS RLC Not needed?? PP_OPENSIDE_FLANKER, PP_FLANKER,*/ PP_NUMBER_EIGHT_LOCK_FORWARD, PP_FORWARD };
const PLAYER_POSITION BLINDSIDE_FLANKER_PICK_ORDER[]			= { /* WJS RLC Not needed?? PP_BLINDSIDE_FLANKER, PP_FLANKER,*/ PP_NUMBER_EIGHT_LOCK_FORWARD, PP_FORWARD };
const PLAYER_POSITION FORWARD_PICK_ORDER[]						= { PP_FORWARD };
const PLAYER_POSITION BACK_PICK_ORDER[]							= { PP_BACK };

#define CASEPICK( POS ) case PP_ ## POS: PickNextPlayer( to_choose_from, add_to, POS ## _PICK_ORDER, sizeof( POS ## _PICK_ORDER ) / sizeof( PLAYER_POSITION ) ); break;
void SSRoleArea::SelectPlayer( PLAYER_POSITION pos, SlotEntries& to_choose_from, SlotEntries& add_to )
{
	switch (pos)
	{
		CASEPICK( FIVEEIGHTH );
		CASEPICK( INSIDE_CENTER_LEFTCENTRE );
		CASEPICK( OUTSIDE_CENTER_RIGHTCENTRE );
		CASEPICK( LEFTWING );
		CASEPICK( RIGHTWING );
		CASEPICK( FULLBACK );
		//  WJS RLC Not needed?? CASEPICK( OPENSIDE_FLANKER );
		//  WJS RLC Not needed?? CASEPICK( BLINDSIDE_FLANKER );
		CASEPICK( FORWARD );
		CASEPICK( BACK );

		// WJS RLC ... added to stop unknown player assert below
		// todo do we need it?
		CASEPICK(NUMBER_EIGHT_LOCK_FORWARD);
		default:
			MABBREAKMSG( "Unknown player position or group to select from");
			break;
	}

}

void SSRoleArea::UpdateOpenBlind()
{
	/// Check to see if we should update slots

	/// If someone has been removed or added to slots then rebalance
	FVector ref_pos = FVector::ZERO;
	if (origin)
	{
		ref_pos = origin->GetOrigin();
	}
	bool slot_count_changed = slot_count_last_frame != (int) slots.size();
	/*XXXstatic*/ float MIN_DELTA_CHANGE = 3.0f;
	bool sevens = game->GetGameSettings().game_settings.GameModeIsR7();
	if(sevens)
		MIN_DELTA_CHANGE = 1.25f;

	bool ref_pos_moved = (ref_pos - slot_reference).Magnitude() > MIN_DELTA_CHANGE;

	if ( !(slot_count_changed || ref_pos_moved ) )
		return;

	/// If the slot reference position has changed then re balance
	slot_reference = ref_pos;

	slots_open.clear();
	slots_blind.clear();

	if ( slots.empty())
		return;

	// HES request to make players go to the openside during rucks.
	float dist_from_closest_sideline = (FIELD_WIDTH * 0.5f) - MabMath::Fabs( ref_pos.x );
	const static float MIN_DIST_FROM_SIDELINE_FOR_BLINDSIDE = 10.0f;
	bool sevens_and_close_to_side_rucks = sevens && game->GetGameState()->GetPhase() == RUGamePhase::RUCK && dist_from_closest_sideline < MIN_DIST_FROM_SIDELINE_FOR_BLINDSIDE;

	const static float BIG_OPEN_BREAKDOWN_MAX_OFFSET = 7.5f;
	SlotEntries to_choose_from = slots;
	SlotEntries slot_open_wing, slot_blind_wing;
	SlotEntries::const_iterator it;

	openside_dir = (ref_pos.x * slots[0].player->GetAttributes()->GetPlayDirection()) < 0.0f ? OSD_LEFT : OSD_RIGHT;
	openside_size =  MabMath::Fabs( ref_pos.x) > BIG_OPEN_BREAKDOWN_MAX_OFFSET ? OSS_BIG : OSS_SMALL;

	bool is_send_runner_zone = MabStringHelper::StringCompareIgnoreCase( get_name(), SEND_RUNNER_ZONE_NAME ) == 0;

	/// Special case for send runner zone - HACK should really be data driven but this is quicker!
	if ( is_send_runner_zone )
	{
		// Work out proportion of slots either side
		dist_from_closest_sideline = (FIELD_WIDTH * 0.5f) - MabMath::Fabs( game->GetBall()->GetCurrentPosition().x );
		/*XXXstatic*/ float MIN_DIST_FROM_SIDELINE_ALLOW_RUNNERS = 5.0f;
		// HES request - don't want players too close on to the sideline during sevens
		if( sevens )
		{
			MIN_DIST_FROM_SIDELINE_ALLOW_RUNNERS = 7.5f;
		}

		/// If we are too close to the sideline then all players go on the open side
		if ( dist_from_closest_sideline < MIN_DIST_FROM_SIDELINE_ALLOW_RUNNERS )
		{
			slots_open.insert( slots_open.end(), to_choose_from.begin(), to_choose_from.end() );
		}
		else
		{
			int n_slots_open = 0, n_slots_blind = 0;
			// Set blind slots first as we always want this to be non zero in this second case
			if ( to_choose_from.size() > 1 )
			{
				float proportion_blind = dist_from_closest_sideline / FIELD_WIDTH;
				n_slots_blind = (int) MabMath::Ceil( proportion_blind * (float) to_choose_from.size() );
			}
			n_slots_open = (int)(to_choose_from.size()) - n_slots_blind;

			// Now move this number of players to each group
			for( int i = 0; i < (int) to_choose_from.size(); ++i )
			{
				if ( i < n_slots_open )
					slots_open.push_back( to_choose_from[i] );
				else
					slots_blind.push_back( to_choose_from[i] );
			}
		}
	}
	else
	{
		//sevens = false;
		/*if( sevens )
		{
			float ref_x = MabMath::Abs(ref_pos.x);

			// If we're inside 5m from the sideline + our normal spacing
			if(ref_x >= (FIELD_WIDTH * 0.5f) - 10.0f)
			{
				sevens = false;
			}
		}*/

		if ( openside_size == OSS_BIG )
		{
			// Open
			// PP_FIVEEIGHTH, PP_INSIDE_CENTER_LEFTCENTRE, PP_OUTSIDE_CENTER_RIGHTCENTRE, Optional Fullback, Optional Forwards (Prioritise Openside flanker), Correct Wing
			// Blind
			// Optional Forwards (Prioritise Blindside flanker), Opposite wing

			//if (manager && game && (manager->GetTeam() == game->GetGameState()->GetDefendingTeam()))
			{
				SelectPlayer(PP_FORWARD, to_choose_from, slots_open);
				SelectPlayer(PP_FORWARD, to_choose_from, slots_blind);
				SelectPlayer(PP_FORWARD, to_choose_from, slots_open);
				SelectPlayer(PP_FORWARD, to_choose_from, slots_open);
				SelectPlayer(PP_FORWARD, to_choose_from, slots_blind);
				SelectPlayer(PP_FORWARD, to_choose_from, slots_open);
				SelectPlayer(PP_FORWARD, to_choose_from, slots_open);
				SelectPlayer(PP_FORWARD, to_choose_from, slots_open);
			}

			/// Fill open side slots first
			SelectPlayer(PP_NUMBER_EIGHT_LOCK_FORWARD, to_choose_from, slots_open );
			SelectPlayer(PP_INSIDE_CENTER_LEFTCENTRE, to_choose_from, slots_open );
			SelectPlayer(PP_OUTSIDE_CENTER_RIGHTCENTRE, to_choose_from, slots_open );
			SelectPlayer( PP_FULLBACK, to_choose_from, slots_open );
			SelectPlayer( openside_dir == OSD_LEFT ? PP_LEFTWING : PP_RIGHTWING, to_choose_from, slot_open_wing );

			// Don't add these guys to sevens
			if(!sevens)
			{
				// WJS RLC Not needed?? SelectPlayer( PP_OPENSIDE_FLANKER, to_choose_from, slots_open );
				SelectPlayer( PP_FORWARD, to_choose_from, slots_open );
			}

			// If we're not a sevens match, or we're sevens and were far away from the sideline add these guys
			if( !sevens_and_close_to_side_rucks )
			{
				SelectPlayer( PP_FORWARD, to_choose_from, slots_blind );
				SelectPlayer( openside_dir == OSD_LEFT ? PP_RIGHTWING : PP_LEFTWING, to_choose_from, slot_blind_wing );
			}

			/// Move any remaining players to the end of the open slots
			slots_open.insert( slots_open.end(), to_choose_from.begin(), to_choose_from.end() );

			/// Add wingers to the end
			slots_open.insert ( slots_open.end(),  slot_open_wing.begin(), slot_open_wing.end() );
			slots_blind.insert( slots_blind.end(), slot_blind_wing.begin(), slot_blind_wing.end() );
		}
		else
		{

			//if (manager && game && (manager->GetTeam() == game->GetGameState()->GetDefendingTeam()))
			{
				SelectPlayer(PP_FORWARD, to_choose_from, slots_open);
				SelectPlayer(PP_FORWARD, to_choose_from, slots_blind);
				SelectPlayer(PP_FORWARD, to_choose_from, slots_open);
				SelectPlayer(PP_FORWARD, to_choose_from, slots_open);
				SelectPlayer(PP_FORWARD, to_choose_from, slots_blind);
				SelectPlayer(PP_FORWARD, to_choose_from, slots_open);
				SelectPlayer(PP_FORWARD, to_choose_from, slots_blind);
				SelectPlayer(PP_FORWARD, to_choose_from, slots_open);
			}

			// Open
			// PP_FIVEEIGHTH, PP_INSIDE_CENTER_LEFTCENTRE, Optional Fullback, Optional Forwards, Correct Wing
			// Blind
			// PP_OUTSIDE_CENTER_RIGHTCENTRE, Optional Forwards, (Prioritise Blindside flanker), Opposite Wing
			SelectPlayer( PP_FIVEEIGHTH, to_choose_from, slots_open );
			SelectPlayer( PP_INSIDE_CENTER_LEFTCENTRE, to_choose_from, slots_open );
			SelectPlayer( PP_FULLBACK, to_choose_from, slots_open );
			SelectPlayer( openside_dir == OSD_LEFT ? PP_LEFTWING : PP_RIGHTWING, to_choose_from, slot_open_wing );
			SelectPlayer( PP_FORWARD, to_choose_from, slots_open );

			SelectPlayer( openside_dir == OSD_LEFT ? PP_RIGHTWING : PP_LEFTWING, to_choose_from, slot_blind_wing );

			// If we're not a sevens match, or we're sevens and were far away from the sideline add these guys
			if( !sevens_and_close_to_side_rucks )
			{
				SelectPlayer( PP_OUTSIDE_CENTER_RIGHTCENTRE, to_choose_from, slots_blind );
			}

			// Don't add these guys to sevens
			if(!sevens)
			{
				// WJS RLC Not needed?? SelectPlayer( PP_BLINDSIDE_FLANKER, to_choose_from, slots_blind );
				SelectPlayer( PP_FORWARD, to_choose_from, slots_blind );
			}

			/// Move any remaining players to the end of the open slots
			slots_open.insert( slots_open.end(), to_choose_from.begin(), to_choose_from.end() );

			/// Add wingers to the end
			slots_open.insert ( slots_open.end(),  slot_open_wing.begin(), slot_open_wing.end() );
			slots_blind.insert( slots_blind.end(), slot_blind_wing.begin(), slot_blind_wing.end() );
		}
	}

	/// Now order open and blind slots from left to right as this is what everything else expects them in
	/// They are presently in in to out format
	if ( openside_dir == OSD_LEFT )
		std::reverse( slots_open.begin(), slots_open.end() );
	else
		std::reverse( slots_blind.begin(), slots_blind.end() );
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

FVector SSRoleArea::GetZonePosition(float play_dir, float time /* = 0.0f*/) const
{
	if ( !origin ) return FVector::ZeroVector;

	FVector result;

	MABASSERT(isLine == false);

	float px = zone->x.get_graph_value(time);
	float pz = zone->z.get_graph_value(time);

    if (manager)
	{
        px *= play_dir * manager->current_x_direction;
    }
	pz *= play_dir;

	FVector ref_pos = FVector::ZERO;
	ref_pos = origin->GetOrigin();
	
	//Aim setplays on the open side.
	if (zone->type == ERugbyFormationZoneType::OPENSIDE_BIAS)
	{
		float directionMultiplier = 1;
		if (manager && manager->team)
		{
			directionMultiplier = manager->team->GetRight();			//Reverse the direction for the left side
		}
	}

	FVector zone_origin = origin->GetOrigin();

	FieldExtents extents = manager->game->GetSpatialHelper()->GetFieldExtents();
	extents.x *= 0.5f;

	if (zone->movementType == ERugbyFormationZoneMovementType::PENDULUM)
	{
		float t = zone_origin.x / extents.x;
		MabMath::Clamp(t,-1.0f,1.0f);

		t = t * zone->mvParam1 * PI * 0.5f;

		float cs = MabMath::Cos(t);
		float ss = MabMath::Sin(t);

		result.x = px * cs + pz * ss * (1.0f + zone->mvParam2);
		result.z = zone_origin.z + pz*cs - px*ss;
	}
	else if (zone->movementType == ERugbyFormationZoneMovementType::POD)
	{
		result.x = (zone_origin.x + px) + zone->mvParam1 * ((manager->formation_start_x + px) - (zone_origin.x + px));
		result.z = zone_origin.z + pz;

		float diff = zone_origin.x - manager->formation_start_x;
		if(diff<0)
			diff = -diff;

		result.z += zone->mvParam2 * diff * play_dir;
	}
	else if (zone->movementType == ERugbyFormationZoneMovementType::REFEREE)
	{
		px = px>0.0f ? px : -px;
		if(zone_origin.x>0.0f)
		{
			px = -px;
		}

		result.x = zone_origin.x + px;
		result.z = zone_origin.z + pz;
	}
	else
	{
		result.x = zone_origin.x + px;
		result.z = zone_origin.z + pz;
	}

	result.y = 0.0f;

	/// Clamp in field... (x).

	const float wd = extents.x - zone->width.get_graph_value(0.0f) * 0.5f;
	MabMath::Clamp(result.x, -wd, wd);

	return result;
}

///-------------------------------------------------------------------------
/// GetXRange - get the min and max x positions for this area
/// Mainly only applies to lines
///-------------------------------------------------------------------------

void SSRoleArea::GetXRange(float& start, float& end) const
{
	MABASSERT(isLine == true); // added by jamesg

//	if (isLine)
//	{
		FVector startPos = GetPathPosition( 0.0f, true );
		FVector endPos = GetPathPosition( 1.0f, true );

		start = startPos.x;
		end = endPos.x;

		if (start == end) end += 0.01f;
//	}
//	else
//	{
//		start = 0.0f;
//		end = 0.0f;
//	}
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

FVector SSRoleArea::GetPathPosition(float t, bool world_space) const
{
	FVector result = spline.Evaluate(t);

	if ( world_space )
	{
		FVector area_origin = FVector::ZeroVector;
		if ( origin )
		{
			area_origin = origin->GetOrigin();
		}

		float play_dir = -float(manager->GetTeam()->GetPlayDirection());

		result.x *= play_dir * float(manager->GetCurrentFormationXDirection());
		result.z *= play_dir;
		result.x += area_origin.x;
		result.z += area_origin.z;
		result.y = 0.0f;
	}

	return result;
}

void SSRoleArea::UpdateAreaOrigin()
{
	if ( get_origin_target() == ERugbyFormationTarget::INHERIT )
		origin = manager->GetFormationOrigin();
	else
		origin = manager->game->GetGameState()->GetFormationTarget(get_origin_target());
}

///-------------------------------------------------------------------------
/// Get Z offset for player in slot 'slot_idx'
///-------------------------------------------------------------------------

float SSRoleArea::GetPathZOffset(ARugbyCharacter* player)
{
	int position = PlayerPositionEnum::GetPositionIndexFromPlayerPosition( player->GetAttributes()->GetPlayerPosition() );
	int playersPerTeam = manager->game->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();

	if(position>=0 && position<playersPerTeam/*NUM_PLAYERS_PER_TEAM*/)
	{
		return slot_z_offsets[position];
	}
	else
	{
		return 0.0f;
	}
}


///-------------------------------------------------------------------------
/// Return the length of the spline (path).
///-------------------------------------------------------------------------

float SSRoleArea::GetPathLength() const
{
	return splineLength;
}

///-------------------------------------------------------------------------
/// Return the player in the given slot_idx (NULL if none).
///-------------------------------------------------------------------------

ARugbyCharacter* SSRoleArea::GetSlotPlayer(int slot_idx) const
{
	if( slot_idx >= 0 && slot_idx< (int) slots.size() )
		return slots[slot_idx].player;
	else
		return NULL;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void SSRoleArea::Reset()
{
	/// Don't use RURandomNumberGenerator in this function, will break the replay system!
	slots.clear();
	slot_count_last_frame = 0;
	doReset = true;
	int playersPerTeam = manager->game->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();

	for(int i = 0; i < playersPerTeam/*NUM_PLAYERS_PER_TEAM*/; i++)
	{
		slot_x_offsets[i] = 0.0f;
		slot_z_offsets[i] = 0.0f;
	}

	for ( int i = 0; i < playersPerTeam/*NUM_PLAYERS_PER_TEAM*/; ++i )
		what_att_slot_to_mark[i] = -1;

	/// Set to an invalid reference so we can recaluclate if needed
	slot_reference.Set( 1e10f, 1e10f, 1e10f );
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void SSRoleArea::UpdateSpline()
{
	spline.Clear();

	for (const FSerialiseFormationLinePoint& linePoint : line->points)
	{
		const float x = linePoint.x.get_graph_value(0.f);
		const float z = linePoint.z.get_graph_value(0.f);

		spline.AddCV({x, 0.f, z}, 1.f);
	}

	spline.CreateStandardKnotVector();

	splineLength = spline.CalculateLength(32);
}



///-------------------------------------------------------------------------
/// When a player requires a slot (is using the area) this function must
/// be called on every update that you still want the player in a slot
///-------------------------------------------------------------------------

struct PlayerSlotMatch
{
	PlayerSlotMatch( ARugbyCharacter* player ) : player( player ) {}
	bool operator () (const SSRoleArea::SlotEntry& slot)
	{
		return slot.player == player;
	}
	ARugbyCharacter* player;
};

int SSRoleArea::UpdatePlayerSlot( ARugbyCharacter* player )
{
	/// Find the player in our slot list
	SlotEntries::iterator it = std::find_if( slots.begin(), slots.end(), PlayerSlotMatch( player ) );
	if ( it != slots.end() )
	{
		it->used_last_frame = true;
		return (int)(it - slots.begin());	// Return index in array
	}

	MABASSERT( (int) slots.size() < FMAREA_MAX_SLOTS );

	/// Add the player to our slot list
	slots.push_back( SlotEntry( player ) );
	return (int)(slots.size());
}

///-------------------------------------------------------------------------
/// Get all the players in this area
///-------------------------------------------------------------------------

bool SSRoleArea::GetPlayers(SIFRugbyCharacterList& results)
{
	results.clear();
	for(size_t i=0;i<slots.size();i++)
	{
		results.push_back(slots[i].player);
	}
	return true;
}

///-------------------------------------------------------------------------
/// Update
///-------------------------------------------------------------------------

const float PREVIOUS_TARGET_MOD = 3.0f;

struct SlotUsedMatch
{
	SlotUsedMatch( bool slot_used ) : slot_used( slot_used ) {}
	bool operator() (const SSRoleArea::SlotEntry& slot)
	{
		return slot.used_last_frame == slot_used;
	}
	bool slot_used;
};

struct LineTSort
{
	LineTSort() : xstart( 0.0f ), xend( 0.0f ), sort_mode( ERugbyFormationPlayerSortStrategy::RANDOM ) { sort_function = &LineTSort::GetLineTRandom; }

	inline float GetLineT( const ARugbyCharacter* player ) const
	{
		return (*this.*sort_function)( player );
	}

	float GetLineTRandom( const ARugbyCharacter* player ) const
	{
		float t = (player->GetMovement()->GetCurrentPosition().x - xstart) / (xend - xstart);

		int pp  = (int)player->GetAttributes()->GetPlayerPosition();

		// Hack so that wings are always in correct positions.
		if(pp&PP_RIGHTWING)
			t = 1.0f;

		if(pp&PP_LEFTWING)
			t = -1.0f;

		return t;
	}//ERugbyFormationPlayerSortStrategy::RANDOM

	float GetLineTLinearInsideOut( const ARugbyCharacter* player ) const
	{
		float t = 0.0f;

		int pp  = (int)player->GetAttributes()->GetPlayerPosition();

		/// Work out which side of the field is the open side (TODO: need to measure from last breakdown)
		SIFGameWorld* game = player->GetGameWorld();
		FVector ball_pos = game->GetBall()->GetCurrentPosition();
		ERugbyPlayDirection play_dir = player->GetAttributes()->GetPlayDirection();

		/// Systems
		/// North - Left = +1, Right = -1
		/// South - Left = -1, Right = +1
		/// t - Left = 0, Right = 1
		const static float MULT = 1.0f;
		float fan_dir_t = MabMath::Sign( ball_pos.x * play_dir * MULT );
		/// DO we need to check area mirror too?

		/// And modify by play direction
		if (pp & PP_FLY_HALF_STAND_OFF)
			t = 0.1f;

		if (pp & PP_INSIDE_CENTER_LEFTCENTRE)
			t = 0.2f;

		if (pp & PP_OUTSIDE_CENTER_RIGHTCENTRE)
			t = 0.3f;

		// This might happen if a "flat" line is used (where players line up along their try line).
		if (pp & PP_FULLBACK)
			t = 0.4f;

		// Put the wing at the end of the line (far side of the field from the lineout).
		// We know this is the correct wing as the other has already been chosen for the Winger lineout zone (see the lineout EVDS files).

		if ( pp & PP_BACK )
			t = fan_dir_t < 0.0f ? 1.0f - t : t;

		if ( pp & PP_LEFTWING )
			t = 0.0f;
		if ( pp & PP_RIGHTWING )
			t = 1.0f;

		return t;
	} //ERugbyFormationPlayerSortStrategy::LINEAR_INSIDETOOUT

	bool operator () ( const SSRoleArea::SlotEntry& a, const SSRoleArea::SlotEntry& b ) const
	{
		return GetLineT( a.player ) < GetLineT( b.player );
	}

	float xstart, xend;
	ERugbyFormationPlayerSortStrategy sort_mode;
	float (LineTSort::*sort_function)(const ARugbyCharacter* player) const;
};

void SSRoleArea::Update()
{
	/// Do any reset functionality which involves RURandomNumberGenerator here!!!!
	if (doReset)
	{
		if (isLine && get_name() != "LineOut")
		{
			int playersPerTeam = manager->game->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();

			RURandomNumberGenerator* rng = manager->game->GetRNG();
			for(int i = 0; i < playersPerTeam/*NUM_PLAYERS_PER_TEAM*/; i++)
			{
				slot_z_offsets[i] = rng->RAND_CALL(float);
			}
		}
		doReset = false;
	}

	/// Remove all slots that haven't been used - set remaining to used = false
	UpdateUnusedSlots();
	players_in_area = 0;

	/// Update our origin
	UpdateAreaOrigin();

	// Placement logic...

	// If we are a mark opponent line calculate the order of marking
	if ( isLine && line->type == ERugbyFormationLineType::MARKOPPONENT )
	{
		// calculate order of opponents to be marked
		const SSRoleArea* opp_line = manager->GetOpposingLine(this);
		if (opp_line != nullptr)
		{
			int num_attacking_slots = opp_line->SlotsUsed();
			MABASSERT( manager->game->GetBall() );
			FVector ball_position = manager->game->GetBall()->GetCurrentPosition();

			MabArray<float, NUM_PLAYERS_PER_TEAM_INIT> dist_to_ball_holder;
			for ( int i = 0; i < num_attacking_slots; ++i )
			{
				ARugbyCharacter* player = opp_line->GetSlotPlayer(i);
				float d2ball = (player->GetMovement()->GetCurrentPosition() - ball_position ).Magnitude();
				dist_to_ball_holder[i] = d2ball;
			}

			MabArray<int, NUM_PLAYERS_PER_TEAM_INIT> sorted_attack_slots;
			int current_best = 0;
			float current_dist = 10000.0f;
			for ( int i = 0; i < num_attacking_slots; ++i )
			{
				for ( int j = 0; j < num_attacking_slots; ++j )
				{
					if ( dist_to_ball_holder[j] < dist_to_ball_holder[current_best] )
					{
						current_best = j;
						current_dist = dist_to_ball_holder[j];
					}
				}

				sorted_attack_slots[i] = current_best;
				dist_to_ball_holder[current_best] = 11000.0f;
				current_best = 0;
				current_dist = 10000.0f;
			}

			int playersPerTeam = manager->game->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();

			// make next -1 so we know when to stop
			if ( num_attacking_slots < playersPerTeam/*NUM_PLAYERS_PER_TEAM*/ )
				sorted_attack_slots[num_attacking_slots] = -1;

			// test each player and rate their best defender
			MabArray<int, NUM_PLAYERS_PER_TEAM_INIT> pre_who_mark_who;
			for ( int i = 0; i < playersPerTeam/*NUM_PLAYERS_PER_TEAM*/; ++i )
			{
				pre_who_mark_who[i] = what_att_slot_to_mark[i];
				what_att_slot_to_mark[i] = -1;
			}
			if ( slots.size() > 0 && num_attacking_slots > 0)
			{
				for ( size_t i = 0; i < slots.size(); ++i )
				{
					MabArray<float, NUM_PLAYERS_PER_TEAM_INIT> defence_score;
					int priority_attacker = sorted_attack_slots[i];
					if ( priority_attacker == -1 )
						break;

					ARugbyCharacter* att_plyer = opp_line->GetSlotPlayer(priority_attacker);
					for ( size_t j = 0; j < slots.size(); ++j )
					{
						ARugbyCharacter* def_player = slots[j].player;

						int player_no = def_player->GetAttributes()->GetTeamIndex();
						if ( what_att_slot_to_mark[player_no] != -1 )
						{
							defence_score[j] = 10000.0f;
							continue;
						}

						float dist_to_attacker = MabMath::Abs(def_player->GetMovement()->GetCurrentPosition().x - att_plyer->GetMovement()->GetCurrentPosition().x );
						MabMath::ClampLower( dist_to_attacker, 0.05f );

						if ( pre_who_mark_who[player_no] == priority_attacker )
						{

							dist_to_attacker -= PREVIOUS_TARGET_MOD;
							MabMath::ClampLower( dist_to_attacker, 0.0f );
						}

						defence_score[j] = dist_to_attacker;
					}

					int best_defender_slot = 0;
					float best_score = defence_score[best_defender_slot];

					for ( size_t j = 1; j < slots.size(); ++j )
					{
						if ( defence_score[j] < defence_score[best_defender_slot] )
						{
							best_defender_slot = (int)j;
							best_score = defence_score[(int)j];
						}
					}

					ARugbyCharacter* best_def_player = slots[best_defender_slot].player;
					int player_no = best_def_player->GetAttributes()->GetTeamIndex();
					what_att_slot_to_mark[player_no] = priority_attacker;
				}
			}
		}
	}

	/// Update the players in our area
	for( size_t i = 0; i < slots.size(); i++ )
		players_in_area |= slots[i].player->GetAttributes()->GetPlayerPosition();

	/// Update line
	if (isLine)
	{
		UpdateOpenBlind();

		// Sort players in slots by distance from line start...
		if ( line->playerSortStrategy == ERugbyFormationPlayerSortStrategy::RANDOM )
		{
			LineTSort slot_sorter;
			GetXRange(slot_sorter.xstart,slot_sorter.xend);
			std::sort( slots.begin(), slots.end(), slot_sorter );
		}
		else if ( line->playerSortStrategy == ERugbyFormationPlayerSortStrategy::LINEAR_INSIDETOOUT )
		{
			LineTSort slot_sorter;
			GetXRange(slot_sorter.xstart,slot_sorter.xend);
			slot_sorter.sort_function = &LineTSort::GetLineTLinearInsideOut;
			std::sort(slots.begin(), slots.end(), slot_sorter);
		}
		else if ( line->playerSortStrategy  == ERugbyFormationPlayerSortStrategy::FAN_OPENBLIND )
		{
			/// Use open blind
			slots.clear();

			if ( openside_dir == OSD_LEFT )
			{
				slots.insert( slots.end(), slots_open.begin(), slots_open.end() );
				slots.insert( slots.end(), slots_blind.begin(), slots_blind.end() );
			}
			else
			{
				slots.insert( slots.end(), slots_blind.begin(), slots_blind.end() );
				slots.insert( slots.end(), slots_open.begin(), slots_open.end() );
			}
		}
	}
	/// Update elliptical zones
	else
	{
		//If the zone is openside biased, find the open side
		if (zone->type == ERugbyFormationZoneType::OPENSIDE_BIAS)
		{
			UpdateZoneOpensideBias();
		}

		if(slot_count_last_frame != (int)slots.size())		// Player has left/joined zone->
		{
			float wd = zone->width.get_graph_value(0.f);
			float ht = zone->height.get_graph_value(0.f);

			switch(zone->positioning)
			{
			case ERugbyFormationZonePositioningMode::RANDOM:
				if(slots.size()>1)
				{
					RURandomNumberGenerator* rng = manager->game->GetRNG();

					float dx = 2.0f / (float)slots.size();
					float x = -(float)(slots.size()-1) * 0.5f * dx;

					for(size_t i=0;i<slots.size();i++)
					{
						slot_x_offsets[i] = x * wd * 0.5f;											// X is equally spaced along width of zone->
						float zrange = MabMath::Sqrt(1-(x*x)) * ht;
						slot_z_offsets[i] = (rng->RAND_RANGED_CALL(float, zrange) - zrange * 0.5f);			// Z is random, but inside ellipse of zone->
						x += dx;
					}
				}
				else
				{
					slot_x_offsets[0] = 0.0f;
					slot_z_offsets[0] = 0.0f;
				}
				break;

			case ERugbyFormationZonePositioningMode::PLAYERPOSITION:
			default:
				if(slots.size()<2)
				{
					slot_x_offsets[0] = 0.0f;
					slot_z_offsets[0] = 0.0f;
				}
				else
				{
					for(size_t i=0;i<slots.size();i++)
					{
						float t = (float)i / (float)(slots.size());
						t *= PI*2.0f;
						slot_x_offsets[i] = MabMath::Cos(t) * wd * 0.35f;
						slot_z_offsets[i] = MabMath::Sin(t) * ht * 0.35f;
					}
				}
				break;
			}

		}
	}

	// Store for use - this copy is only ever updated in here.
	slot_count_last_frame = (int)slots.size();
}

//Updates the openside bias for zones
void SSRoleArea::UpdateZoneOpensideBias()
{
	if (!zone)
		return;

	FSerialiseTypeGraph * xPosList = &zone->x;
	FVector ref_pos = FVector::ZERO;
	if (origin)
	{
		ref_pos = origin->GetOrigin();
	}
	float directionMultiplier = 1;
	if (manager && manager->team)
	{
		directionMultiplier = manager->team->GetRight();
	}

	if (!zone->isFlipped)
	{
		if ((directionMultiplier < 0 && ref_pos.x > 0) || (directionMultiplier > 0 && ref_pos.x < 0))
		{
			for (int i = 0; i < xPosList->points.Num(); i++)
			{
				xPosList->points[i].y = -xPosList->points[i].y;
			}

			zone->isFlipped = true;
		}
	}
	else
	{
		if ((directionMultiplier < 0 && ref_pos.x < 0) || (directionMultiplier > 0 && ref_pos.x > 0))
		{
			for (int i = 0; i < xPosList->points.Num(); i++)
			{
				xPosList->points[i].y = -xPosList->points[i].y;
			}

			zone->isFlipped = false;
		}
	}
}

///-------------------------------------------------------------------------
/// Get the players slot position in the line->
///  returns -1 if player not in line->
///-------------------------------------------------------------------------

int SSRoleArea::GetPlayerSlotIdx(ARugbyCharacter* player)
{
	SlotEntries::iterator it = std::find_if( slots.begin(), slots.end(), PlayerSlotMatch( player ) );
	if ( it != slots.end() )
		return (int)(it - slots.begin());

	return -1;
}

FString SSRoleArea::getPlayerMaskAsString()
{
	FString ret = FString(TEXT("None")) ;
	ERugbyFormationPlayerMask mask = get_player_mask();

	//*
	switch (mask)
	{
		case ERugbyFormationPlayerMask::ALL:					ret = FString(TEXT("ALL"					  )); break;
		case ERugbyFormationPlayerMask::P1:						ret = FString(TEXT("P1"						  )); break;
		case ERugbyFormationPlayerMask::P2:						ret = FString(TEXT("P2"						  )); break;
		case ERugbyFormationPlayerMask::P3:						ret = FString(TEXT("P3"						  )); break;
		case ERugbyFormationPlayerMask::P4:						ret = FString(TEXT("P4"						  )); break;
		case ERugbyFormationPlayerMask::P5:						ret = FString(TEXT("P5"						  )); break;
		case ERugbyFormationPlayerMask::P6:						ret = FString(TEXT("P6"						  )); break;
		case ERugbyFormationPlayerMask::P7:						ret = FString(TEXT("P7"						  )); break;
		case ERugbyFormationPlayerMask::P8:						ret = FString(TEXT("P8"						  )); break;
		case ERugbyFormationPlayerMask::P9:						ret = FString(TEXT("P9"						  )); break;
		case ERugbyFormationPlayerMask::P10:					ret = FString(TEXT("P10"					  )); break;
		case ERugbyFormationPlayerMask::P11:					ret = FString(TEXT("P11"					  )); break;
		case ERugbyFormationPlayerMask::P12:					ret = FString(TEXT("P12"					  )); break;
		case ERugbyFormationPlayerMask::P13:					ret = FString(TEXT("P13"					  )); break;
		//case ERugbyFormationPlayerMask:://P14:				ret = FString(TEXT("P14"					  )); break;
		//case ERugbyFormationPlayerMask:://P15:				ret = FString(TEXT("P15"					  )); break;
		case ERugbyFormationPlayerMask::FORWARDS:				ret = FString(TEXT("Forwards"				  )); break;
		case ERugbyFormationPlayerMask::BACKS:					ret = FString(TEXT("Backs"					  )); break;
		case ERugbyFormationPlayerMask::REFEREE:				ret = FString(TEXT("Referee"				  )); break;
		case ERugbyFormationPlayerMask::TWOPOD_1:				ret = FString(TEXT("Two-Pod_1"				  )); break;
		case ERugbyFormationPlayerMask::TWOPOD_2:				ret = FString(TEXT("Two-Pod_2"				  )); break;
		case ERugbyFormationPlayerMask::THREEPOD_1:				ret = FString(TEXT("Three-Pod_1"			  )); break;
		case ERugbyFormationPlayerMask::THREEPOD_2:				ret = FString(TEXT("Three-Pod_2"			  )); break;
		case ERugbyFormationPlayerMask::THREEPOD_3:				ret = FString(TEXT("Three-Pod_3"			  )); break;
		case ERugbyFormationPlayerMask::BACKSTHENFORWARDS:		ret = FString(TEXT("Backs then Forwards"	  )); break;
		case ERugbyFormationPlayerMask::FORWARDSTHENBACKS:		ret = FString(TEXT("Forwards then Backs"	  )); break;
		case ERugbyFormationPlayerMask::WING:					ret = FString(TEXT("Winger"					  )); break;
		case ERugbyFormationPlayerMask::SETPLAYGTB:				ret = FString(TEXT("Setplay GetTheBall"		  )); break;

	}
	//*/

	return ret;
}

///-------------------------------------------------------------------------
/// Get parametric position of intersect of line from position, at angle with line->
///  angle = 0 = straight up/down pitch.
///-------------------------------------------------------------------------

float SSRoleArea::GetLineIntersectT(FVector pos, float angle)
{
	MABASSERT(isLine == true);

	if (!isLine)
		return -1.0f;

	float x0,x1;
	GetXRange(x0,x1);

	if(x1>x0)
		angle = -angle;

	float current_x_direction =  float(manager->current_x_direction);
	float play_dir = - float(manager->team->GetPlayDirection());

	if(play_dir>0)
	{
		if(current_x_direction>0)
			angle = -angle;
	}
	else
	{
		if(current_x_direction<0)
			angle = -angle;
	}

	// Create plane...

	float A,C,D;				// B = 0.
	A = MabMath::Cos(angle);
	C = MabMath::Sin(angle);
	D = -(pos.x * A  + pos.z * C);

#define NUM_INTERSECT_INTERATIONS	8

	float t0 = 0;
	float t1 = 1;
	float t = 0.5f;

	for(int i=0;i<NUM_INTERSECT_INTERATIONS;i++)
	{
		t = (t0+t1)*0.5f;
		FVector pos0 = GetPathPosition(t, true);

		float r0 = pos0.x*A + pos0.z*C + D;
		if(x1<x0)
			r0 = -r0;

		if(r0>0)
			t1 = t;
		else
			t0 = t;

	}

	return t;
}

///-------------------------------------------------------------------------
/// GetPlayerZonePosition: Get internal position of player (int slot_idx).
///-------------------------------------------------------------------------

void SSRoleArea::GetPlayerZonePosition(ARugbyCharacter* player, int slot_idx, float &dx, float &dz, float time /* = 0.0f*/)
{
	MABUNUSED(player);
	MABASSERT(isLine == false);

	switch(zone->positioning)
	{
	case ERugbyFormationZonePositioningMode::PLAYERPOSITION:
		{
			float wd = zone->width.get_graph_value(0.0f);
			float ht = zone->height.get_graph_value(0.0f);
			float t = 0.0f;
			float r = 0.35f;

			PLAYER_POSITION position = player->GetAttributes()->GetPlayerPosition();

			// for player positions?
			switch(position)
			{
			case PP_LOOSEHEAD_PROP:
				t = MabMath::Deg2Rad(45.0f);
				break;
			case PP_HOOKER:
				break;
			case PP_TIGHTHEAD_PROP:
				t = MabMath::Deg2Rad(-45.0f);
				break;
			case PP_NUMBER_FOUR_LOCK_SECOND_ROW_TWELVE:
				t = MabMath::Deg2Rad(90.0f);
				r = 0.2f;
				break;
			case PP_NUMBER_FIVE_LOCK_SECOND_ROW_ELEVEN:
				t = MabMath::Deg2Rad(-90.0f);
				r = 0.2f;
				break;
			///// WJS RLC Not needed??
			///// case PP_BLINDSIDE_FLANKER:
			///// 	t = MabMath::Deg2Rad(135.0f);
			///// 	break;
			case PP_NUMBER_EIGHT_LOCK_FORWARD:
				t = MabMath::Deg2Rad(180.0f);
				break;
			///// WJS RLC Not needed??
			///// case PP_OPENSIDE_FLANKER:
			///// 	t = MabMath::Deg2Rad(-135.0f);
			///// 	break;

			default:
				r = 0.0f;
				break;
			}

			float current_direction =  float(manager->current_x_direction);
			float play_dir = float(manager->team->GetPlayDirection());

			dx = MabMath::Sin(t) * wd * r * play_dir * current_direction;
			dz = MabMath::Cos(t) * ht * r * play_dir;
		}


		break;

	default:
		dx = slot_x_offsets[slot_idx];
		dz = slot_z_offsets[slot_idx];
		break;
	}
}

/// Remove all slots that haven't been used - set remaining to used = false
void SSRoleArea::UpdateUnusedSlots()
{
	/// Remove all slots that that did not set their slot used flag
	SlotEntries::iterator last_valid_elem = std::remove_if( slots.begin(), slots.end(), SlotUsedMatch( false ) );
	slots.erase( last_valid_elem, slots.end() );

	/// Set all current slots that did have it set to false
	for( SlotEntries::iterator it = slots.begin(); it != slots.end(); ++it )
		it->used_last_frame = false;
}

///*******************************************************************************************************************************
///*******************************************************************************************************************************
/// EVDS SSEventDataSystemEditor callbacks.
///*******************************************************************************************************************************
///*******************************************************************************************************************************





///-------------------------------------------------------------------------------
/// SSEventDataSystemEditor - Called to start editing cutscene.
///-------------------------------------------------------------------------------

void SSEVDSFormationManager::EVDSStartEditing(const MabString &name)
{
	MABUNUSED(name);
}

///-------------------------------------------------------------------------------
/// SSEventDataSystemEditor - Called to start editing cutscene.
///-------------------------------------------------------------------------------

void SSEVDSFormationManager::EVDSStopEditing(const MabString &name)
{
	MABUNUSED(name);
}

///-------------------------------------------------------------------------------
/// SSEventDataSystemEditor - Data has been modified (Possibly a big-change)
///-------------------------------------------------------------------------------

void SSEVDSFormationManager::EVDSDataModified(const MabString &name)
{
	MABUNUSED(name);
//	UpdateFormationsDataFromEVDS(formations);
//	SetupCurrentFormationData();
}

///-------------------------------------------------------------------------------
/// SSEventDataSystemEditor.
///-------------------------------------------------------------------------------

void SSEVDSFormationManager::EVDSSetTime(const MabString &name, float time)
{
	MABUNUSED(name);
	MABUNUSED(time);
}

///-------------------------------------------------------------------------------
/// SSEventDataSystemEditor
///-------------------------------------------------------------------------------

void SSEVDSFormationManager::EVDSGenericCommand(const MabString &arguments)
{
	MABUNUSED(arguments);
}





//*****************************************************************************************************************************
//*****************************************************************************************************************************
// Send debug information to RugEd.
//*****************************************************************************************************************************
//*****************************************************************************************************************************





///-------------------------------------------------------------------------
/// Send debug information about this formation manager to the RUDebugService
///-------------------------------------------------------------------------
#ifdef ENABLE_RUGED
void SSEVDSFormationManager::SendDebugInfo(RUDebugService *service)
{
	int team_idx = team->GetIndex();

////	float play_dir = (float) team->GetPlayDirection();
//
//	for(int i=0;i<NUM_FORMATION_PLAYERS;i++)
//	{
//		if(!(formation_players[i].flags&BITFLAGS_DISABLED))
//		{
//			MabVector<float> values;
//
//			values.push_back(formation_players[i].x);
//			values.push_back(formation_players[i].z);
//			values.push_back(formation_players[i].width);
//			values.push_back(formation_players[i].height);
//
//			service->SendFormationPlayerInfo(team_idx,i,values);
//		}
//	}

	{
		MabString info;

		MABASSERT(current_formation);

		if( current_formation != NULL )
		{
			info = MabString(64,"%s  dir=%d",current_formation->name,current_x_direction);

			MabVector<float> values;

			FVector formation_pos = formation_origin != NULL ? formation_origin->GetOrigin( this ) : FVector::ZeroVector;

			values.push_back(formation_pos.x);
			values.push_back(formation_pos.z);

			int num_forms = formations.size() * 2;
			values.push_back((float)num_forms);
			for(int i=0;i<num_forms;i++)
			{
				values.push_back(formation_scores[i]);
			}

			// Send formation information (in float array as it already exists).

			values.push_back((float)current_areas.size());
			for(int i=0;i<(int)current_areas.size();i++)
			{
				SSRoleArea *area = current_areas[i];
				float play_dir = - (float) team->GetPlayDirection();

				FVector position;

				if(area->is_line)
				{
					values.push_back(16.0f);

					for(int i=0;i<16;i++)
					{
						float t = (float)i/15.0f;
						area->GetPathPosition(t,position,true);
						values.push_back(position.x);
						values.push_back(position.z);
					}
				}
				else
				{
					values.push_back(0.0f);

					area->GetZonePosition(position,play_dir);

					float width = area->width->GetGraphValue(0);
					float height = area->height->GetGraphValue(0);

					values.push_back(position.x);
					values.push_back(position.z);
					values.push_back(width);
					values.push_back(height);
				}
			}

			service->SendFormationInfo(team_idx,info.c_str(),values);
		}
	}

	// Send debug role information.

	for( MabVector<DebugRoleInfo*>::iterator iter = debug_roles.begin(); iter != debug_roles.end(); ++iter )
	{
		MabString role_name = (*iter)->role_name;
		MabVector<int> priorities;

		int playersPerTeam = game->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();

		for(int i=0;i<playersPerTeam/*NUM_PLAYERS_PER_TEAM*/;i++)
			priorities.push_back((*iter)->priorities[i]);

		service->SendRoleInfo(team_idx, role_name, priorities);
	}

}
#endif


///*******************************************************************************************************************************
///*******************************************************************************************************************************
/// Role assignment.
///*******************************************************************************************************************************
///*******************************************************************************************************************************

/// \brief OverrideAreaNumPlayers
/// @param area_name The name of the area for which to apply the override accepts trailing wildcard of * e.g Backs* will match all areas that start with Backs
/// @param num_players The number of players to override for this region.  Set to OANP_NO_OVERRIDE to revert to initial formation setting
/// @param role_id The sub role id to apply this to.  Set to OANP_ALL_ROLES to apply this setting to all roles for the formation.  Note an assert will fire if this is the formation contains more than one role entry
/// @param formation_name The formation to apply the role override to.  NULL uses the currently active formation
bool SSEVDSFormationManager::OverrideAreaNumPlayers(const FString& areaName, int num_players, ERugbyFormationRole role_id, const FString& formation_name)
{
	MABASSERT(team);
	MABASSERT(team->GetIndex() >= 0 && team->GetIndex() <= 2);

	/// Look in the current formation and find the requested area
	EVDSFormation*	formation				= formation_name.IsEmpty() ? mCurrentFormation : GetFormationByName(formation_name);
	bool			found					= false;
	FString			strZoneToOverrideName	= areaName.Replace(TEXT("*"), TEXT(""));

	if (formation == NULL)
		return false;
		
	//////////////////////////////////////////////////////////////////////////
	// Doubling up on the following loop for both zones and lines, because the code was refactored causing this knock on.
	for (size_t j = 0; j < formation->zones.Num(); j++)
	{
		/// See if the area name matches
		FSerialiseFormationZone& zone = formation->zones[j];
		FString strZonezName = zone.name;

		// Does the nae match?
		if (false == strZonezName.StartsWith(strZoneToOverrideName))
			continue;

		/// Yep, Now see if we have a role match in this area
		TArray<FSerialiseFormationRole>& roles = zone.roles;

		for (size_t k = 0; k < roles.Num(); k++)
		{
			FSerialiseFormationRole& role = roles[k];
			ERugbyFormationRole role_idx = role.role;

			if (role_idx == role_id || role_id == ERugbyFormationRole::ALL)
			{
				MABASSERT(role_id != ERugbyFormationRole::ALL || k == 0);
				found = true;
				role.overrideNumPlayers = num_players;
			}
		}
	}

	// Not sure if this is needed anymore since the refactor? Do line formations use the overrides?
	for (size_t j = 0; j < formation->lines.Num(); j++)
	{
		/// See if the area name matches
		FSerialiseFormationLine& line = formation->lines[j];
		FString strLineName = line.name;

		if (false == strLineName.StartsWith(strZoneToOverrideName))
			continue;

		/// Now see if we have a role match in this area
		TArray<FSerialiseFormationRole>& roles = line.roles;

		for (size_t k = 0; k < roles.Num(); k++)
		{
			FSerialiseFormationRole& role = roles[k];
			ERugbyFormationRole role_idx = role.role;

			if (role_idx == role_id || role_id == ERugbyFormationRole::ALL)
			{
				MABASSERT(role_id != ERugbyFormationRole::ALL || k == 0);
				found = true;
				role.overrideNumPlayers = num_players;
			}
		}
	}
	
	//UE_LOG ( LogTemp, Display, TEXT("OverrideAreaNumPlayers: AreaName: '%s' NumPlayer: '%d' roleID: ERugbyFormationRole: '%d' formation_name: '%s' Found: '%d'"), *areaName, num_players, role_id, *formation_name, (int) found );

	return found;
}

void SSEVDSFormationManager::OverrideAreaLineSpacing( const FString& areaName, const float lineSpacing, const FString& formationName)
{
	/// Look in the current formation and find the requested area
	EVDSFormation* formation = formationName.IsEmpty() ? mCurrentFormation : GetFormationByName(formationName);
	//MABASSERT( formation != NULL );

	if (formation == NULL)
		return;

	bool wildcard_end = areaName.EndsWith(TEXT("*"));
	FString area_name = areaName.Replace(TEXT("*"), TEXT(""));

	//MabEVDSEventHandles* areas_list[2] = { &formation->zones, &formation->lines };
	//for (int i = 0; i < 2; i++)
	{
		// DH - The formation code was refactored, so we don't need to do the spacing for zones?
		for (size_t j = 0; j < formation->lines.Num(); j++)
		{
			/// See if the area name matches
			FSerialiseFormationLine area = formation->lines[j];
			FString test_area_name = area.name;

			bool area_match = test_area_name.StartsWith(area_name);

			if (!area_match)
				continue;

			area.overrideSpacing = lineSpacing;

			/// Apply to area if it is current
			SSRoleArea* ss_area = GetAreaByName(test_area_name);
			if (ss_area != NULL)
				ss_area->line->overrideSpacing = lineSpacing;
		}
	}
}

void SSEVDSFormationManager::ResetNumPlayersOverrides()
{
	for (auto& formation : mFormations)
	{
		for (auto& zone : formation->zones)
		{
			for (auto& role : zone.roles)
			{
				role.overrideNumPlayers = -1;
			}
		}

		for (auto& line : formation->lines) 
		{
			for (auto& role : line.roles)
			{
				role.overrideNumPlayers = -1;
			}
		}
	}

#if 0 //Above code reproduces this from rc3
	for( size_t i = 0; i < formations.size(); ++i )
	{
		auto& formation = formations[i];

		MabEVDSEventHandles* areas_list[2] = { &formation->zones, &formation->lines };
		for( int i = 0; i < 2; i++ )
		{
			MabEVDSEventHandles& areas = *areas_list[i];
			for( size_t j = 0; j < areas.size(); j++ )
			{
				/// See if the area name matches
				MabEVDSEventHandle area = areas[j];

				/// Now see if we have a role match in this area
				MabEVDSEventHandles roles;
				area->GetHandles( "role", roles );

				for( size_t k = 0; k < roles.size(); k++ )
				{
					MabEVDSEventHandle role = roles[k];
					if ( role->GetProperty( OVERRIDE_AREA_ROLES_PROPERTY_NAME[team->GetIndex()] ) )
						role->SetProperty( OVERRIDE_AREA_ROLES_PROPERTY_NAME[team->GetIndex()], -1 );
				}
			}
		}
	}
#endif
}

///-------------------------------------------------------------------------
/// Update the players roles... Called every tick.
///-------------------------------------------------------------------------

void SSEVDSFormationManager::UpdateRoles()
{
	//--------------------------------
	// For debugging cutscenes
	if (AIDisabledForCutscenes)
	{
		if (team == game->GetGameState()->GetAttackingTeam())
		{
			MabVector<FMRole> roles;
			AssignPlayerRoles(roles, team->GetPlayers(), SSRoleNull::RTTGetStaticType());
			return;
		}
		
		if (team != game->GetGameState()->GetAttackingTeam())
		{
			MabVector<FMRole> roles;
			AssignPlayerRoles(roles, team->GetPlayers(), SSRoleNull::RTTGetStaticType());
			return;
		}	
	}
	//--------------------------------

#if !UE_BUILD_SHIPPING
	SIFRugbyCharacterList disabled_players;

	if (CVarDisableAttackingTeam.GetValueOnAnyThread())
	{
		if (team == game->GetGameState()->GetAttackingTeam())
		{
			MabVector<FMRole> roles;
			AssignPlayerRoles(roles, team->GetPlayers(), SSRoleNull::RTTGetStaticType());
			return;
		}
	}
	if (CVarDisableDefendingTeam.GetValueOnAnyThread())
	{
		if (team != game->GetGameState()->GetAttackingTeam())
		{
			MabVector<FMRole> roles;
			AssignPlayerRoles(roles, team->GetPlayers(), SSRoleNull::RTTGetStaticType());
			return;
		}
	}
	if (CVarDisableDefendingTeamExceptFullback.GetValueOnAnyThread())
	{
		if (game->GetGameState()->GetPhase() == PLAY)
		{
			if (team != game->GetGameState()->GetAttackingTeam())
			{
				MabVector<FMRole> roles;
				disabled_players = team->GetPlayers();
				SIFRugbyCharacterList::const_iterator del_start = disabled_players.end();
				SIFRugbyCharacterList::const_iterator del_end = disabled_players.end();
				bool do_delete = false;
				for (SIFRugbyCharacterList::const_iterator iter = disabled_players.begin(); iter != disabled_players.end(); ++iter)
				{
					const ARugbyCharacter* player = *iter;
					if (player && (player->GetAttributes()->GetPlayerPosition() & PP_FULLBACK) != 0)
					{
						if (!do_delete)
						{
							do_delete = true;
							del_start = iter;
							del_end = iter;
						}
						else
						{
							del_end = iter;
						}
					}
				}

				if (do_delete)
				{
					if (del_start == del_end)
					{
						disabled_players.erase(del_start);
					}
					else
					{
						disabled_players.erase(del_start, del_end);
					}
				}

				AssignPlayerRoles(roles, disabled_players, SSRoleNull::RTTGetStaticType());
			}
		}
	}
	if (CVarDisableAI_NGB.GetValueOnAnyThread())
	{
		if (team->GetHumanPlayer(0) == NULL
			&& (game->GetGameState()->GetPhase() == RUGamePhase::PLAY
				|| game->GetGameState()->GetPhase() == RUGamePhase::QUICK_TAP_PENALTY
				|| game->GetGameState()->GetPhase() == RUGamePhase::ELECT_QUICK_TAP))
		{
			MabVector<FMRole> roles;
			AssignPlayerRoles(roles, team->GetPlayers(), SSRoleNull::RTTGetStaticType());
			return;
		}
	}

#endif

#ifdef ENABLE_GAME_DEBUG_MENU
	if (SIFDebug::GetGameDebugSettings()->GetAttackDisabled())
	{
		if (team == game->GetGameState()->GetAttackingTeam())
		{
			MabVector<FMRole> roles;
			AssignPlayerRoles(roles, team->GetPlayers(), SSRoleNull::RTTGetStaticType());
			return;
		}
	}
	if (SIFDebug::GetGameDebugSettings()->GetDefenceDisabled())
	{
		if (team != game->GetGameState()->GetAttackingTeam())
		{
			MabVector<FMRole> roles;
			AssignPlayerRoles(roles, team->GetPlayers(), SSRoleNull::RTTGetStaticType());
			return;
		}
	}

	// Used to disable AI tackling not human (only in gameplay phase)
	if (SIFDebug::GetGameDebugSettings()->GetAIDisabled() && team->GetHumanPlayer(0) == NULL &&
		team != game->GetGameState()->GetAttackingTeam() 
		&& (game->GetGameState()->GetPhase() == RUGamePhase::PLAY 
			|| game->GetGameState()->GetPhase() == RUGamePhase::QUICK_TAP_PENALTY
			|| game->GetGameState()->GetPhase() == RUGamePhase::ELECT_QUICK_TAP))
	{
		MabVector<FMRole> roles;
		AssignPlayerRoles(roles, team->GetPlayers(), SSRoleNull::RTTGetStaticType());
		return;
	}
#endif

	// Update origins before roles since some role calcs bask fitness on area origin
	for (SSRoleArea* area : current_areas) 
	{
		area->UpdateAreaOrigin();
	}
	for (SSRoleArea* area : current_setplay_areas)
	{
		area->UpdateAreaOrigin();
	}

	MabVector<FMRole> roles;
	if (SetplayManager && SetplayManager->IsSetplayInProgress())
	{
		for (SSRoleArea* area : current_setplay_areas)
		{
			if (area->get_player_mask() != ERugbyFormationPlayerMask::REFEREE)
			{
				if (area->get_roles().Num() == 0)
				{
					FMRole fmrole;
					fmrole.role = GetRoleFromEnum(mCurrentFormation->defaultRole);
					fmrole.player_mask = area->get_player_mask();
					fmrole.area = area;
					roles.push_back(fmrole);
				}
				else
				{
					const int playersPerTeam = game->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();
					int max_players = playersPerTeam/*NUM_PLAYERS_PER_TEAM*/ + 1;			// +1 Allows for referees role.
					int plr_count = 0;
					int roleCount = area->get_roles().Num();
					int index = 0;
					for (const FSerialiseFormationRole& role : area->get_roles())
					{
						int numPlayersCompletedSetplay = 0;

						//Check if role is a setplay
						if (role.role == ERugbyFormationRole::SETPLAY || role.role == ERugbyFormationRole::SETPLAY_SCRUM_HALF  
							|| role.role == ERugbyFormationRole::SETPLAY_PTB_RECEIVER)
						{
							//If it is a setplay, update the number of players who have already completed this role.
							numPlayersCompletedSetplay = SetplayManager->NumPlayersCompletedSetplay(area, index);
						}


						ERugbyFormationRole role_idx = role.role;
						int num_players = role.numPlayers;
						int override_num_players = role.overrideNumPlayers;

						if (override_num_players >= 0)
						{
							num_players = override_num_players;
						}

						//Subtract the number of players who have already completed the setplay from the num_players.
						num_players -= numPlayersCompletedSetplay;

						if (/*role_idx >= 0 &&*/ num_players > 0)
						{
							MabTypeID roleid = GetRoleFromEnum(role_idx);
							FMRole fmrole;
							fmrole.role = roleid;
							fmrole.player_mask = area->get_player_mask();
							fmrole.area = area;

							for (int j = 0; j < num_players; j++)
							{
								plr_count++;
								if (plr_count <= max_players)
								{
									roles.push_back(fmrole);
								}
							}
						}

						index++;
					}
				}
			}
		}
	}
	else
	{

		for (SSRoleArea* area : current_areas)
		{
			if (area->get_player_mask() != ERugbyFormationPlayerMask::REFEREE)
			{
				if (area->get_roles().Num() == 0)
				{
					FMRole fmrole;
					fmrole.role = GetRoleFromEnum(mCurrentFormation->defaultRole);
					fmrole.player_mask = area->get_player_mask();
					fmrole.area = area;
					roles.push_back(fmrole);
				}
				else
				{
					const int playersPerTeam = game->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();
					int max_players = playersPerTeam/*NUM_PLAYERS_PER_TEAM*/ + 1;			// +1 Allows for referees role.
					int plr_count = 0;
					int roleCount = area->get_roles().Num();
					int index = 0;
					for (const FSerialiseFormationRole& role : area->get_roles())
					{


						ERugbyFormationRole role_idx = role.role;
						int num_players = role.numPlayers;
						int override_num_players = role.overrideNumPlayers;

						if (override_num_players >= 0)
						{
							num_players = override_num_players;
						}

						if (role_idx != ERugbyFormationRole::UNUSED && num_players > 0)
						{
							MabTypeID roleid = GetRoleFromEnum(role_idx);
							FMRole fmrole;
							fmrole.role = roleid;
							fmrole.player_mask = area->get_player_mask();
							fmrole.area = area;

							for (int j = 0; j < num_players; j++)
							{
								plr_count++;
								if (plr_count <= max_players)
								{
									roles.push_back(fmrole);
								}
							}
						}

						index++;
					}
				}
			}
		}
	}
	if (mCurrentSetplay != nullptr)
	{
		MabTypeID default_roleid = GetRoleFromEnum(mCurrentSetplay->defaultRole);

		AssignPlayerRoles(roles, team->GetPlayers(), default_roleid);
	}
	else if (mCurrentFormation != nullptr)
	{
		MabTypeID default_roleid = GetRoleFromEnum(mCurrentFormation->defaultRole);

		SIFRugbyCharacterList team_players = team->GetPlayers();

#if !UE_BUILD_SHIPPING	
		bool do_delete = false;
		SIFRugbyCharacterList::const_iterator del_start = team_players.end();
		SIFRugbyCharacterList::const_iterator del_end = team_players.end();
		for (SIFRugbyCharacterList::const_iterator disable_iter = disabled_players.begin(); disable_iter != disabled_players.end(); ++disable_iter)
		{
			const ARugbyCharacter* disable_player = *disable_iter;
			for (SIFRugbyCharacterList::const_iterator iter = team_players.begin(); iter != team_players.end(); ++iter)
			{
				const ARugbyCharacter* player = *iter;
				if (disable_player == player)
				{
					if (!do_delete)
					{
						do_delete = true;
						del_start = iter;
						del_end = iter;
					}
				}
				else
				{
					if (do_delete)
					{
						del_end = iter;
					}
				}
			}
		}

		if (do_delete)
		{
			if (del_start == del_end)
			{
				team_players.erase(del_start);
			}
			else
			{
				team_players.erase(del_start, del_end);
			}
		}
#endif


		AssignPlayerRoles(roles, team_players, default_roleid);
	}
}


///-------------------------------------------------------------------------
/// Given role_idx (from RugEd formation) return the MabTypeId for the role.
///-------------------------------------------------------------------------

MabTypeID SSEVDSFormationManager::GetRoleFromEnum(ERugbyFormationRole role)
{
	switch (role)
	{
	case ERugbyFormationRole::KICKOFFKICKER:
		return RURoleKickOffKicker::RTTGetStaticType();

	case ERugbyFormationRole::MARKDEFEND:
		return RURoleMarkDefend::RTTGetStaticType();

	case ERugbyFormationRole::RUCKDEFEND:
		return RURoleRuckDefend::RTTGetStaticType();

	case ERugbyFormationRole::RUCKSENDRUNNER:
		return RURoleRuckSendRunner::RTTGetStaticType();

	case ERugbyFormationRole::BALLHOLDER:
		return RURoleStandardBallHolder::RTTGetStaticType();

	case ERugbyFormationRole::GETTHEBALL:
		return RURoleGetTheBall::RTTGetStaticType();

	case ERugbyFormationRole::LINEOUT:
		return RURoleLineOut::RTTGetStaticType();

	case ERugbyFormationRole::LINEOUT_THROWER:
		return RURoleLineOutThrower::RTTGetStaticType();

	case ERugbyFormationRole::LINEOUT_RECEIVER:
		return RURoleLineOutReceiver::RTTGetStaticType();

	case ERugbyFormationRole::RUCK:
		return RURoleRuck::RTTGetStaticType();

	case ERugbyFormationRole::RUCK_SCRUMHALF:
		return RURoleRuckScrumHalf::RTTGetStaticType();

	case ERugbyFormationRole::SCRUM_HALFBACK:
		return RURoleScrumHalfBack::RTTGetStaticType();

	case ERugbyFormationRole::SCRUM:
		return RURoleScrum::RTTGetStaticType();

	case ERugbyFormationRole::SUPPORT:
		return RURoleSupport::RTTGetStaticType();

	case ERugbyFormationRole::SHOOT_FOR_GOAL:
		return RURoleShootForGoal::RTTGetStaticType();

	case ERugbyFormationRole::TUTORIAL:
		return RURoleTutorial::RTTGetStaticType();

	case ERugbyFormationRole::PENALTY_ATTACK:
		return RURolePenaltyAttack::RTTGetStaticType();

	case ERugbyFormationRole::PENALTY_DEFENCE:
		return RURolePenaltyDefence::RTTGetStaticType();

	case ERugbyFormationRole::TAP_RESTART:
		return RURoleTapRestart::RTTGetStaticType();

	case ERugbyFormationRole::FORMATION:
		return SSRoleFormation::RTTGetStaticType();

	case ERugbyFormationRole::NULL_ROLE:
		return SSRoleNull::RTTGetStaticType();

	case ERugbyFormationRole::UNUSED_TWO:
		return SSRoleNull::RTTGetStaticType();

	case ERugbyFormationRole::FULLBACK:
		return RURoleFullback::RTTGetStaticType();

	case ERugbyFormationRole::RIGHT_WING_DEFEND:
		return RURoleRightWingDefend::RTTGetStaticType();

	case ERugbyFormationRole::LEFT_WING_DEFEND:
		return RURoleLeftWingDefend::RTTGetStaticType();

	case ERugbyFormationRole::TRY_REACTION:
		return RURoleTryReaction::RTTGetStaticType();

	case ERugbyFormationRole::KICKOFF_CHASER:
		return RURoleKickOffChaser::RTTGetStaticType();

	case ERugbyFormationRole::REACTION_CUTSCENE:
		return RURoleCutsceneReaction::RTTGetStaticType();

	case ERugbyFormationRole::MAUL:
		return RURoleMaul::RTTGetStaticType();

	case ERugbyFormationRole::MAUL_HALFBACK:
		return RURoleMaulHalfback::RTTGetStaticType();

	case ERugbyFormationRole::SETPLAY:
		return RURoleSetplay::RTTGetStaticType();

	case ERugbyFormationRole::SETPLAY_SCRUM_HALF:
		return RURoleSetplayScrumHalf::RTTGetStaticType();
	
	case ERugbyFormationRole::SETPLAY_PTB_RECEIVER:
		return RURoleSetplayPlayTheBallReceiver::RTTGetStaticType();

	case ERugbyFormationRole::PLAY_THE_BALL:
		return RURolePlayTheBall::RTTGetStaticType();

	case ERugbyFormationRole::PLAY_THE_BALL_RECEIVER:
		return RURolePlayTheBallReceiver::RTTGetStaticType();

	case ERugbyFormationRole::PLAY_THE_BALL_DEFENDER:
		return RURolePlayTheBallDefender::RTTGetStaticType();

	default:
		return SSRoleNull::RTTGetStaticType();
	}
}

//*****************************************************************************************************************************
//*****************************************************************************************************************************
// ROLE ASSIGNMENT SYSTEM
//*****************************************************************************************************************************
//*****************************************************************************************************************************

#if (PLATFORM_WINDOWS) && defined( ENABLE_OSD ) && defined ( ENABLE_GAME_DEBUG_MENU )
#define FITNESS_DEBUGCODE( x ) x
#endif

#ifndef FITNESS_DEBUGCODE
#define FITNESS_DEBUGCODE( x )
#endif

#ifndef HARDCORE_FITNESS_DEBUGCODE
#define HARDCORE_FITNESS_DEBUGCODE( x )
#endif


// Boost a fitness gets if the actor is already using the behaviour specified
const int ALREADY_USING_UPDATER_BOOST = 7;

// If a priority is greater than this, then the team averaging logic is not applied, to ensure the actor
// gets the actor updater
const float HIGH_PRIORITY_MARK = 85.0f;

// Balance for best average assignment if highest priorities is within the range
const float BALANCE_RANGE = 1000.0f;

// Priority data
typedef struct
{
	int largest; // Largest priority
	int delta;	 // Delta to next highest priority
} PriorityDetails;


///-------------------------------------------------------------------------
/// Assign roles to players.
///-------------------------------------------------------------------------

void SSEVDSFormationManager::AssignPlayerRoles(MabVector<FMRole> &roles, const SIFRugbyCharacterList& players, MabTypeID default_role )
{
	int numPlayers = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayers();

	// #dewald removing these asserts as they are firing off now due to us having lots more players loaded at all times
	//MABASSERT( roles.size() * players.size() < (size_t) (numPlayers * SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam()));
	std::vector< std::pair< int, PriorityDetails > > assignment_order;
	using FitnessCache = std::map< MabTypeID, int >;
	FitnessCache fitness_cache;

	SSRoleFactory* role_factory = game->GetRoleFactory();

	// Clear old data
	for ( int i = 0; i < numPlayers; i++ )
		role_table[i] = -2;


	FITNESS_DEBUGCODE(
		RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
		char buffer[128];
		char line[512] = "";
		MabStringHelper::Sprintf( line, "%-20s %-16s %4s", "Role", "Area, PID->", "Num" );
		for ( unsigned int i = 0; i < players.size(); i++ )
		{
			MabStringHelper::Sprintf( buffer, " % 5d", players[i]->GetAttributes()->GetIndex() );
			MabStringHelper::Strcat( line, buffer );
		}
		settings->PushDebugString( game, RUGameDebugSettings::DP_ROLE_ASSIGNMENT, line  );
	)

	FITNESS_DEBUGCODE(
		MabVector< MabString > debug_strings;
		debug_strings.resize( roles.size() );
	);


	// Get player rating for each 'unique role' type....
	const static int SAME_ROLE_BOOST = 10;
	bool isPlayTheBall = game->GetGameState()->GetPhase() == PLAY_THE_BALL;

	std::array<ARugbyCharacter*, NUM_PLAYERS_INIT> assigned_players;
	assigned_players.fill(nullptr);
	int assign_index = 0;

	//If there is a setplay ruck scrum half, remove the default scrum half from the roles list.
	
	
	bool containsSetplayPTBReceiver = false;
	bool containsSetplayScrumHalf = false;
	for (MabVector<FMRole>::iterator iter = roles.begin(); iter != roles.end(); ++iter)
	{
		if (isPlayTheBall)
		{

			//Remove the PlayTheBallReceiver if a setplay starts as they are replaced by SetplayPlayTheBallReceiver
			if ((*iter).role == RURoleSetplayPlayTheBallReceiver::RTTGetStaticType())
			{
				containsSetplayPTBReceiver = true;
			}
			if ((*iter).role == RURolePlayTheBallReceiver::RTTGetStaticType() && containsSetplayPTBReceiver)
			{
				iter = roles.erase(iter);
			}
		}
		else
		{
			//Remove the scrumhalf/ruckscrumhalf if a setplay starts as they are replaced by setplayscrumhalf
			if ((*iter).role == RURoleSetplayScrumHalf::RTTGetStaticType())
			{
				containsSetplayScrumHalf = true;
			}
			if ((*iter).role == RURoleRuckScrumHalf::RTTGetStaticType() && containsSetplayScrumHalf)
			{
				iter = roles.erase(iter);
			}
		}
	}


	// Remove roles which have been assigned to players and are currently non-interuptable.

	for ( unsigned int j = 0; j < players.size(); j++ )
	{
		ARugbyCharacter*	player			= players[j];
		SSRole*				current_role	= player->GetRole();

		if(current_role != NULL && !current_role->IsInterruptable())				//If the player current has an uninterruptable role
		{
            MabTypeID			current_role_id = current_role->RTTGetType();
			RUPlayerAttributes*	attribs			= player->GetAttributes();
			int					player_no		= attribs->GetTeamIndex();
			const SSRoleArea*	area			= mFormationPlayers[player_no].current_area;

			// Iterate through the new roles to find a matching role
			// with either the same area or is a scrum half or in a setplay
			for (MabVector<FMRole>::iterator iter = roles.begin(); iter != roles.end(); ++iter )	
			{
				if (isPlayTheBall)
				{
					if ((*iter).role == current_role_id &&
						((*iter).area == area || current_role_id == GetRoleFromEnum(ERugbyFormationRole::SETPLAY_PTB_RECEIVER)))
					{
						// If the matching role is found in the new roles, remove it. 
						// This is to prevent changing players in a role already underway
						iter = roles.erase(iter);
						break;
					}
				}
				else
				{
					if ((*iter).role == current_role_id &&
						((*iter).area == area ||
							current_role_id == RURoleRuckScrumHalf::RTTGetStaticType() ||
							current_role_id == RURoleSetplayScrumHalf::RTTGetStaticType() ||
							current_role_id == RURoleRuck::RTTGetStaticType()))
					{
						// If the matching role is found in the new roles, remove it. 
						// This is to prevent changing players in a role already underway
						iter = roles.erase(iter);
						break;
					}
				}
				
			}

			//Mark the player as having already been assigned a role.
			assigned_players[assign_index++] = player;
		}
	}

	// PLAY_THE_BALL SPECIFIC
	// There currently is no way to assign a role directly to a player who is closest to a player of another role
	// If we are in play the ball phase, we need to find the closest attacking player to the current attacking tacklee
	// This will assign them the role of being the ball receiver
	ARugbyCharacter* closestAttackerToTackledPlayer = nullptr;
	if (isPlayTheBall)
	{
		closestAttackerToTackledPlayer = GetReceivingPlayer();
	}
	// PLAY_THE_BALL SPECIFIC

	// Generate table.
	for ( size_t playerIdx = 0; playerIdx < players.size(); playerIdx++ )
	{
		//Get the players attributes
		ARugbyCharacter*	player			= players[playerIdx];
		RUPlayerAttributes*	attribs			= player->GetAttributes();
		const SSRoleArea*	current_area	= GetPlayerArea( player );
		SSRole*				current_role	= player->GetRole();
		MabTypeID			current_role_id	= current_role->RTTGetType();

		fitness_cache.clear();

		//Iterate through all the new roles
		for ( size_t roleIdx = 0; roleIdx < roles.size(); ++roleIdx )
		{
			FMRole&		fmrole		= roles[roleIdx];
			MabTypeID	new_role_id = fmrole.role;
			int			fitness		= 0;

			//Search for the same role already being tested for this player
			//FitnessCache::iterator cachedI = fitness_cache.find(new_role_id);

			//If it has been tested, use that fitness. Note: Setplays skip this check as they require checks per area.
			/*if (cachedI != fitness_cache.end() && new_role_id != RURoleSetplayScrumHalf::RTTGetStaticType() && new_role_id != RURoleSetplay::RTTGetStaticType())
			{
				fitness = (*cachedI).second;
			}
			else*/
			{
				//If it has not been tested yet, calculate it using the roles fitness function
				SSRoleFactory::FitnessFunction fitness_function = role_factory->GetFitnessFunction(new_role_id);
				MABASSERT(fitness_function != nullptr);
				
				fitness						= fitness_function(player, fmrole.area);
				fitness_cache[new_role_id]	= fitness;
			}

			//Alter the fitness by the player mask
			fitness += PlayerMaskFitnessAlteration(attribs->GetPlayerPosition(), fmrole.player_mask, fmrole.area);
			
			if (isPlayTheBall)
			{
				if (new_role_id == RURolePlayTheBallReceiver::RTTGetStaticType())
				{
					if (closestAttackerToTackledPlayer != nullptr && closestAttackerToTackledPlayer == player)
					{
						fitness = OVERRIDE_FITNESS;
					}
					else
					{
						fitness = UNSELECTABLE_FITNESS;
					}
				}				
			}

			//Add a boost if the player is being assigned to the same area they are currently assigned to
			if (current_role && current_role_id == new_role_id && current_area != nullptr)
			{
				bool same_area = fmrole.area == current_area ||
					(fmrole.area->get_group_strategy() != ERugbyFormationGroupStrategyType::NOT_SET &&
						fmrole.area->get_group_strategy() == current_area->get_group_strategy());
				if (same_area)
					fitness += SAME_ROLE_BOOST;
			}

			//If the players current role is uninterruptable, set the new role as unselectable
			if (current_role && !current_role->IsInterruptable())
			{
				fitness = UNSELECTABLE_FITNESS;
			}

			// If the new role is in a setplay, check that the player can move before assigning it.
			// This is done as an extra precaution here as sometimes there are not enough players for every role on the field, 
			// and the setplay needs to make sure it gets players who can move with priority as it will cause an NMA otherwise.
			if (new_role_id == RURoleSetplayScrumHalf::RTTGetStaticType() || 
				new_role_id == RURoleSetplay::RTTGetStaticType() || 
				new_role_id == RURoleSetplayPlayTheBallReceiver::RTTGetStaticType())
			{
				RUActionManager* pActionManager = player->GetActionManager();
				if (!pActionManager || pActionManager->IsActionRunning(ACTION_TACKLEE) || pActionManager->IsActionRunning(ACTION_TACKLER))
					fitness = UNSELECTABLE_FITNESS;

				if (pActionManager && pActionManager->UFIsLocked(UF_DOMOTION))
					fitness = UNSELECTABLE_FITNESS;
			}

			int role_table_index = ((int)roleIdx * (int)players.size()) + (int)playerIdx;
			int playersPerTeam = 0;

			playersPerTeam = game->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();

	#ifdef ENABLE_PRO_MODE
			if (attribs->nextIgnoredRole == new_role_id)
				fitness = UNSELECTABLE_FITNESS;
	#endif

			// #dewald removing these asserts as they are firing off now due to us having lots more players loaded at all times
			//MABASSERT(role_table_index < numPlayers/*NUM_PLAYERS*/ * playersPerTeam/*NUM_PLAYERS_PER_TEAM*/);

			//Add the calculated fitness to the role table
			role_table[role_table_index] = fitness;
		}

#ifdef ENABLE_PRO_MODE
		// After assigning the roles, mark the player ignored role back to NULL so that we dont lock them out of this role forever
		attribs->nextIgnoredRole = 0;
#endif
	}

	FITNESS_DEBUGCODE(
		for (unsigned int i = 0; i < debug_strings.size(); i++)
			settings->PushDebugString(game, RUGameDebugSettings::DP_ROLE_ASSIGNMENT, debug_strings[i].c_str());
	)

		FITNESS_DEBUGCODE(
			char assignment_order_string[256];
	MabStringHelper::Strcpy(assignment_order_string, "");
	)

		HARDCORE_FITNESS_DEBUGCODE(
			int swaps = 0;
	int* table_copy = new int[roles.size() * players.size()];
	memcpy(table_copy, table, roles.size() * players.size() * sizeof(int));
	)

		//------------------------------------------------------------
		// Do the assignment

	
	bool setplayStarted = false;
		
	for (int role_index = 0; role_index < (int)roles.size(); ++role_index)
	{
		int player_index = GetBestPlayerAssignment(players, roles, role_index);

		wwNETWORK_TRACE_JG("SetPlayerRole 1 role_index: %d, player_index: %d", role_index, player_index);

		if (player_index != -1)
		{
			MABASSERT(player_index >= 0 && player_index < (int)players.size());

			// Assign the update
			ARugbyCharacter* player = players[player_index];
			MabTypeID role_id = roles[role_index].role;

			wwNETWORK_TRACE_JG("SetPlayerRole 1 role_id: %d, player: %d", role_id, player->GetAttributes()->GetDbId());

			if (!player->GetRole() || player->GetRole()->RTTGetType() != role_id)
			{
				wwNETWORK_TRACE_JG("SetPlayerRole 1");
				//Assign the new role
				SetPlayerRole(player, role_factory->Instance(role_id), roles[role_index].area);

				//Update the setplay manager if a character was set to the setplay role
				if (role_id == RURoleSetplayScrumHalf::RTTGetStaticType())
				{
					MABASSERT(!player->GetActionManager()->IsActionRunning(ACTION_TACKLER));
					MABASSERT(!player->GetActionManager()->IsActionRunning(ACTION_TACKLEE));
					SetplayManager->AddPlayerToSetplay(player, roles[role_index].area);
					setplayStarted = true;
				}		

				if (role_id == RURoleSetplay::RTTGetStaticType() )
				{
					MABASSERT(!player->GetActionManager()->IsActionRunning(ACTION_TACKLER));
					MABASSERT(!player->GetActionManager()->IsActionRunning(ACTION_TACKLEE));
					SetplayManager->AddPlayerToSetplay(player, roles[role_index].area);
					setplayStarted = true;
				}

				if (role_id == RURoleSetplayPlayTheBallReceiver::RTTGetStaticType())
				{
					MABASSERT(!player->GetActionManager()->IsActionRunning(ACTION_TACKLER));
					MABASSERT(!player->GetActionManager()->IsActionRunning(ACTION_TACKLEE));
					SetplayManager->AddPlayerToSetplay(player, roles[role_index].area);
					setplayStarted = true;
				}
			}
			else
			{
				wwNETWORK_TRACE_JG("SetPlayerRole 2");
				SetPlayerRole(player, NULL , roles[ role_index ].area );
			}

			assigned_players[assign_index++] = player;
		}
	}

	HARDCORE_FITNESS_DEBUGCODE (
		if ( swaps )
		{
			Log::Debug( "Table Dump - %d Swaps: ", swaps );
			for ( int y = 0; y < roles.size(); y++ )
			{
				char buffer[256] = "";
				for ( int x = 0; x < players.size(); x++ )
				{
					char val[32];
					MabStringHelper::Sprintf( val, "%02d ", table_copy[ ( y * players.size() ) + x ] );
					MabStringHelper::Strcat( buffer, val );
				}
				Log::Debug( buffer );
			}
			char buffer[256];
			MabStringHelper::Strcpy( buffer, "" );
			for ( int i = 0; i < assignment_order.size(); i++ )
			{
				char val[32];
				MabStringHelper::Sprintf( val, "%02d(%02d,%02d) ", actor_ids[assignment_order[ i ].first], assignment_order[ i ].second.largest, assignment_order[ i ].second.delta );
				MabStringHelper::Strcat( buffer, val );
			}
			Log::Debug( "Assignment Order: %s", buffer );
		}
	)

	FITNESS_DEBUGCODE(
		settings->PushDebugString(game, RUGameDebugSettings::DP_ROLE_ASSIGNMENT, assignment_order_string );
	)

	// Assign default roles
	if ( default_role != 0 )
	{
		for ( unsigned int j = 0; j < players.size(); j++ )
		{
			ARugbyCharacter* player = players[j];
			SSRole* role = player->GetRole();
			if (std::find(assigned_players.begin(), assigned_players.end(), player) == assigned_players.end())
			{
				if (role->IsInterruptable() && role->RTTGetType() != default_role)
				{
					wwNETWORK_TRACE_JG_DISABLED("SetPlayerRole 3");
					SetPlayerRole(player, role_factory->Instance(default_role), NULL);
				}
				else
				{
					wwNETWORK_TRACE_JG_DISABLED("SetPlayerRole 4");
					SetPlayerRole(player, NULL, NULL);
				}
			}
		}
	}

	//UE_LOG(LogTemp, Log, TEXT("======================"));
	/*for (int i = 0; i < players.size(); i++)
	{
		if ((players[i]->GetAttributes()->GetPlayerPosition() | PLAYER_POSITION::PP_FORWARD) != 0)
		{
			if (const SSRoleArea * area = GetPlayerArea(players[i]))
			{
				UE_LOG(LogTemp, Log, TEXT("%d area : %s"), players[i]->GetAttributes()->GetPlayerPosition(), *area->get_name());
			}
			else
			{
				UE_LOG(LogTemp, Log, TEXT("%d has no area assigned"), players[i]->GetAttributes()->GetPlayerPosition());
			}
		}
		
	}*/

	//Force update the blinds to make sure setplay is oriented correctly before indicators are drawn.
	for (int i = 0; i < current_setplay_areas.size(); i++)
	{
		if (current_setplay_areas.at(i))
		{
			current_setplay_areas.at(i)->UpdateZoneOpensideBias();
		}
	}

	if(setplayStarted && SetplayManager && !SetplayManager->GetSetplayTargettedCharacter())
		SetplayManager->ShowCurrentSetplayDisplay(true);
}

///-------------------------------------------------------------------------
/// Set a players role + role area->
///-------------------------------------------------------------------------
void SSEVDSFormationManager::SetPlayerRole(ARugbyCharacter* player, SSRole* new_role, SSRoleArea* area)
{
	wwNETWORK_TRACE_JG("SetPlayerRole");

	if (area)
	{
		wwNETWORK_TRACE_JG("SetPlayerRole slots %d, open: %d, blind: %d", area->slots.size(), area->slots_open.size(), area->slots_blind.size());
	}

	RUPlayerAttributes* attribs = player->GetAttributes();

	int player_no = attribs->GetTeamIndex();
	mFormationPlayers[player_no].current_area = area;

	if (new_role != nullptr)
	{
		// Set the initial urgency for this player/area
		if (area)
		{
			mFormationPlayers[player_no].initial_urgency = MabMath::Lerp(area->get_min_urgency(), area->get_max_urgency(), game->GetRNG()->RAND_CALL(float));
		}

		// Set the default idle group for this player
		RUPlayerAnimation* player_anim = player->GetAnimation();
		if ( player_anim )
			player_anim->SetDefaultIdleGroup(mCurrentFormation->conditions.defaultIdleGroup);

		// Send an event telling other's that a player's role has changed
		SSRole* old_role = player->GetRole();
		MabTypeID old_role_id = 0;
		if ( old_role != nullptr ) old_role_id = old_role->RTTGetType();
		MabTypeID new_role_id = new_role->RTTGetType();

		player->SetRole(std::move(new_role));

		game->GetEvents()->player_role_change( player, old_role_id, new_role_id );
	}

	if (new_role != NULL)
	{
		wwNETWORK_TRACE_JG("SetPlayerRole: %d: %d : %s : %x", attribs->GetTeam()->GetIndex(), player_no, new_role->GetShortClassName(), area);
	}
	else
	{
		wwNETWORK_TRACE_JG("SetPlayerRole: %d: %d : [Null] : %x", attribs->GetTeam()->GetIndex(), player_no, area);
	}

}

///-------------------------------------------------------------------------
/// Get fitness alteration based on position mask in formation. (Heavy biasing)
///-------------------------------------------------------------------------

int	SSEVDSFormationManager::PlayerMaskFitnessAlteration(PLAYER_POSITION position, ERugbyFormationPlayerMask playerMask, const SSRoleArea* area )
{
	if (playerMask==ERugbyFormationPlayerMask::ALL)
		return 0;

	if(playerMask==ERugbyFormationPlayerMask::REFEREE)
		return 10 * MINIMUM_FITNESS;

	const static int FWD_BACK_BOOST = 100;

	switch(playerMask)
	{
	case ERugbyFormationPlayerMask::FORWARDS:
		if((position&PP_FORWARD)==0)
			return 10 * MINIMUM_FITNESS;
		break;

	case ERugbyFormationPlayerMask::BACKS:
		if((position&PP_BACK)==0)
			return 10 * MINIMUM_FITNESS;
		break;

	case ERugbyFormationPlayerMask::TWOPOD_1:
		if((position&(PP_LOOSEHEAD_PROP | PP_HOOKER | PP_TIGHTHEAD_PROP | PP_NUMBER_FOUR_LOCK_SECOND_ROW_TWELVE))==0)
			return MINIMUM_FITNESS;
		break;

	case ERugbyFormationPlayerMask::TWOPOD_2:
		if((position&(PP_NUMBER_FIVE_LOCK_SECOND_ROW_ELEVEN /* // WJS RLC Not needed??| PP_BLINDSIDE_FLANKER | PP_OPENSIDE_FLANKER*/ | PP_NUMBER_EIGHT_LOCK_FORWARD))==0)
			return MINIMUM_FITNESS;
		break;


	case ERugbyFormationPlayerMask::THREEPOD_1:
		if((position&(PP_LOOSEHEAD_PROP | PP_HOOKER | PP_TIGHTHEAD_PROP))==0)
			return MINIMUM_FITNESS;		
		break;

	case ERugbyFormationPlayerMask::THREEPOD_2:
		if((position&(PP_NUMBER_FOUR_LOCK_SECOND_ROW_TWELVE | PP_NUMBER_FIVE_LOCK_SECOND_ROW_ELEVEN /* | // WJS RLC Not needed?? PP_BLINDSIDE_FLANKER*/))==0)
			return MINIMUM_FITNESS;
		break;

	case ERugbyFormationPlayerMask::THREEPOD_3:
		// WJS RLC Do i need to change flankers to second row (11 and 12) players?
		if((position&(/* // WJS RLC Not needed??PP_OPENSIDE_FLANKER |"*/ PP_NUMBER_EIGHT_LOCK_FORWARD))==0)
			return MINIMUM_FITNESS;
		break;

	case ERugbyFormationPlayerMask::FORWARDSTHENBACKS:
		if(position & PP_FORWARD)
			return FWD_BACK_BOOST;
		else 
		if (position & PP_BACK)
			return 0;
		else
			return MINIMUM_FITNESS;
		break;

	case ERugbyFormationPlayerMask::BACKSTHENFORWARDS:
		if(position & PP_BACK)
			return FWD_BACK_BOOST;
		else if (position &PP_FORWARD)
			return 0;
		else
			return 10 * MINIMUM_FITNESS;
		break;

	case ERugbyFormationPlayerMask::WING:
		if ( position & PP_WING )
		{
			/// Work out which side of the field that the requested area is on
			FVector zone_origin;
			if (area->isLine)
				zone_origin = area->GetPathPosition( 0.5f, true );
			else
				zone_origin = area->GetZonePosition( -float(team->GetPlayDirection()) );
			//MABASSERT( zone_origin.x != 0.0f );

			float rel_side = MabMath::Sign( zone_origin.x * (float) team->GetPlayDirection() );
			if ( rel_side >= 0.0f )
				return position & PP_LEFTWING ? 30 : 0;
			else
				return position & PP_RIGHTWING ? 30 : 0;
		} 
		else 
		if ( position & PP_BACK )
			return 20;
		else
			return MINIMUM_FITNESS;
		break;
	case ERugbyFormationPlayerMask::SETPLAYGTB:
		if (SetplayManager->GetGTBOverridePlayer() && (position & SetplayManager->GetGTBOverridePlayer()->GetAttributes()->GetPlayerPosition()))
		{
			/// Work out which side of the field that the requested area is on
			return 10000;
		}
		else
			return MINIMUM_FITNESS;
		break;
		
	case ERugbyFormationPlayerMask::BALL_HOLDER:
		if (game->GetGameState()->GetBallHolder() == nullptr)
		{
			return MINIMUM_FITNESS;
		}

		if (game->GetGameState()->GetBallHolder()->GetAttributes()->GetPlayerPosition() == position)
		{
			return 10000;
		}
		else
		{
			return MINIMUM_FITNESS;
		}
		break;
		
	default:

		//* WJS RLC
		// jamesg - this looks dangerous
		if(uint8(playerMask)>=uint8(ERugbyFormationPlayerMask::P1) && uint8(playerMask)<=uint8(ERugbyFormationPlayerMask::P13))
		{
			if (game)
			{
				// WLS RLC NOT NEEDED if (game->GetGameSettings().game_settings.GameModeIsR7())
				//{
				//	playerMask = ConvertFifteensPositionToSevens(playerMask);
				//}
			}

			int mask = 1 << (int(playerMask) - int(ERugbyFormationPlayerMask::P1));
			if(position != mask)
				return  MINIMUM_FITNESS;
			else
				return 20;
		}
		//*/
		break;
	}

	return 0;
}


///// WJS RLC Node needed
///// ERugbyFormationPlayerMask SSEVDSFormationManager::ConvertFifteensPositionToSevens(ERugbyFormationPlayerMask inPos)
///// {
///// 
///// 	//Positions are assigned to different numbers in sevens
///// 	switch (inPos)
///// 	{
///// 		//Player 4 is the scrum half
///// 	case ERugbyFormationPlayerMask::P9:
///// 		return ERugbyFormationPlayerMask::P4;
///// 
///// 		//Player 5 is the flyhalf
///// 	case ERugbyFormationPlayerMask::P10:
///// 		return ERugbyFormationPlayerMask::P5;
///// 
///// 		//Player 6 is the centre
///// 	case ERugbyFormationPlayerMask::P12:
///// 		return ERugbyFormationPlayerMask::P6;
///// 
///// 		//Player 7 is the winger
///// 	case ERugbyFormationPlayerMask::P11:
///// 	// WJS RLC case ERugbyFormationPlayerMask::P14:
///// 	// WJS RLC case ERugbyFormationPlayerMask::P15:
///// 		return ERugbyFormationPlayerMask::P7;
///// 
///// 	}
///// 
///// 	return inPos;
///// }

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

int SSEVDSFormationManager::GetBestPlayerAssignment(const SIFRugbyCharacterList& players, const MabVector<FMRole>& roles, int role_index)
{
	int num_players = (int) players.size();
	int largest = MINIMUM_FITNESS;
	int best_player_idx = -1;

	//Find the player with the highest fitness for the role
	for ( int i = 0; i < num_players; i++ )
	{
		int fitness = role_table[ ( role_index * num_players ) + i ];
		if ( fitness < MINIMUM_FITNESS && mCurrentSetplay == nullptr )
			continue;

		if ( fitness > largest )
		{
			largest = fitness;
			best_player_idx = i;
		}
	}

	if(best_player_idx!=-1)
	{
		// remove player from table.

		int num_roles = (int) roles.size();
		for ( int j = 0; j < num_roles; j++ )
		{
			int role_table_index = ( j * num_players ) + best_player_idx;
			//int playersPerTeam = 0;
			//playersPerTeam = game->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();
			//int numPlayers = 0;
			//numPlayers = game->GetGameSettings().game_limits.GetNumberOfPlayers();

			// #dewald removing these asserts as they are firing off now due to us having lots more players loaded at all times
			//MABASSERT(role_table_index < numPlayers/*NUM_PLAYERS*/ * playersPerTeam/*NUM_PLAYERS_PER_TEAM*/);
			role_table[ role_table_index ] = UNSELECTABLE_FITNESS;
		}

	}

	return best_player_idx;
}


//*****************************************************************************************************************************
//*****************************************************************************************************************************
// PASS PRIORITY SYSTEM
//*****************************************************************************************************************************
//*****************************************************************************************************************************





///-------------------------------------------------------------------------
/// Update the pass priorities. Result posted on the blackboard.
///-------------------------------------------------------------------------

void SSEVDSFormationManager::UpdatePassPriorities( int pass_priorities_to_apply, bool force_update)
{
	//MABUNUSED(pass_priorities_to_apply);
	MABUNUSED(force_update);
	MABASSERT( game != nullptr );

	RUGameState *game_state = game->GetGameState();
	MABASSERT(game_state!=NULL);

	if ( !force_update )
	{
		// Don't update if we are on defence or there is no ballholder
		if ( team != game_state->GetAttackingTeam() || game_state->GetBallHolder() == NULL )
			return;

		if (!pass_priority_timer.GetNumTimerEventsRaised())
			return;
	}

	// There are several things that can contribute to the final pass priority for each
	// player:

	// 1. Each role running on the player - there is a default implementation on RL3Role, but this can be overidden as necessary
	// 2. If the player is in an overlap or not
	// 3. Player attack abilities, break tackle, speed
	// 4. Close to line - then break through ability

	// Array of priority contribution values from all requested factors to consider for each player
	//int priority_contributions [PASS_PRIORITY_LAST] = { 0 };
	int *priority_contributions = NULL;

	// Current sum for each player
	int pass_priority_sum = 0;

	bool playingProGame = false;
#ifdef ENABLE_PRO_MODE
	playingProGame = game->GetGameSettings().game_settings.GetIsAProMode();
#endif
	bool playingSevensGame = game->GetGameSettings().game_settings.GameModeIsR7();

	// Iterate over all players and work out what is required
	const SIFRugbyCharacterList& players = team->GetPlayers();

	ARugbyCharacter* player;
	float play_dir = (float) team->GetPlayDirection();

	RUBlackBoard& blackboard = ((RUTeam*)team)->GetBlackBoard();

//	SSStrategyHelper* strategy_helper = GetStrategyHelper();
//	MABASSERT( strategy_helper != NULL );

	/// Cache the overlap details for use
//	const OverlapDetails& overlap_details = strategy_helper->GetOverlapDetails( team );
//	const RL3DB_PLAYER* db_player = NULL;

	/// Also the extents of the field for - bustover calculations
	FieldExtents extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();

	float last_pass_dir = game_state->GetLastPassDirection();
	int last_pass_dir_times = game_state->GetLastPassDirectionTimes();

	// Additional priority contributions for passes in the same direction n times
	int PASS_TIMES_ADD_VALUES[] = { 15, 10, 7, 6, 5, 4, 3, 2, 0 };

	// Encourage directional passes way more in sevens
	if(playingSevensGame)
	{
		PASS_TIMES_ADD_VALUES[0] = 20; // 15
		PASS_TIMES_ADD_VALUES[1] = 15; // 10
		PASS_TIMES_ADD_VALUES[2] = 10; // 7
		PASS_TIMES_ADD_VALUES[3] = 5; // 6
		PASS_TIMES_ADD_VALUES[4] = 4; // 5
		PASS_TIMES_ADD_VALUES[5] = 3; // 4
		PASS_TIMES_ADD_VALUES[6] = 2; // 3
		PASS_TIMES_ADD_VALUES[7] = 1; // 2
		PASS_TIMES_ADD_VALUES[8] = 0; // 0
	}

	const int N_MAX_PASS_TIMES = sizeof( PASS_TIMES_ADD_VALUES ) / sizeof( int );

	extents.x *= 0.5f;
	extents.x -= 0.8f;/*SIDELINE_BUFFER*/;
	float leftline = -extents.x;
	float rightline = extents.x;

	/// Work out who the likely passer is for each team
	RUGamePhase phase = game->GetGameState()->GetPhase();
	ARugbyCharacter* likely_passer = NULL;
	SIFRugbyCharacterList passers;

	if ( game->GetGameState()->GetBallHolder() )
	{
		likely_passer = game->GetGameState()->GetBallHolder();
	}
	else if ( phase == RUGamePhase::RUCK )
	{
		game->GetStrategyHelper()->GetTeamRoles( static_cast< RUTeam* >( team ), RURoleRuckScrumHalf::RTTGetStaticType(), passers );
		MABASSERT( passers.size() == 1 );
		if ( !passers.empty() )
		{
			likely_passer = passers[0];
		}
	}
	else if ( phase == RUGamePhase::SCRUM )
	{
		game->GetStrategyHelper()->GetTeamRoles( static_cast< RUTeam* >( team ), RURoleScrumHalfBack::RTTGetStaticType(), passers );
		MABASSERT( passers.size() == 1 );
		if(passers.size()>0)
			likely_passer = passers[0];
	}
	else
	{
		likely_passer = game->GetGameState()->GetLastBallHolder();
	}

	if ( !likely_passer )
	{
		ResetPassPriorityTimer();
		return;
	}

	for( size_t i = 0; i < players.size(); i++ )
	{
		// Get the player
		player = players[i];
		priority_contributions = player->GetState()->GetPassPriorities();

		RUPlayerMovement *plr_movement = player->GetMovement();
		RUPlayerAttributes *plr_attribs = player->GetAttributes();
		SSRole *role = player->GetRole();

		// Check if this is the pro player
		bool isProPlayer = false;
		if(playingProGame) isProPlayer = SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(player);

		// Work out all of the contributions
		for( int j = 0; j < PASS_PRIORITY_LAST; j++ )
		{
			priority_contributions[j] = 0;
		}

		/// Check if this is the pro player and if they have been requesting the ball
		if ( pass_priorities_to_apply & (1<<PRI_PRO_REQUEST) && isProPlayer)
		{
			// Every time the pass request button is pressed the 'pass request' level increments for the player ( 0 -> 1 )
			// Use this as a scale for the priority addition. Alternatively we can just cap it to the full extent.
			priority_contributions[ PRI_PRO_REQUEST ] = (int)(40.0f * plr_attribs->GetPassRequestLevel());
		}

		/// Get the roles contribution - usually the primary amount
		if ( pass_priorities_to_apply & (1<<PRI_ROLE) && role )
		{
			priority_contributions[ PRI_ROLE ] = role->GetPassPriority( likely_passer );
			if ( priority_contributions[ PRI_ROLE ] == 0 ) /// Cannot pass to this player
			{
				blackboard.PostEntry<int>( RUBB_ATTRIB_PASS_PRIORITY, player, 0 );
				continue;
			}
		}

		/// Have some points just for being awesome
		if ( pass_priorities_to_apply & (1<<PRI_PRO_PLAYER) && isProPlayer)
		{
			priority_contributions[ PRI_PRO_PLAYER ] = 10;
		}

		/// Get the contribution from an overlap
		// RUPORT
		//if ( pass_priorities_to_apply & (1<<PRI_OVERLAP) )
		//{
		//	priority_contributions[ PRI_OVERLAP ] = overlap_details.IsPlayerInOverlap( player ) ? 10 : 0;
		//}

		// Copied from open blind formation code - Dewald WW
		if ( pass_priorities_to_apply & (1<<PRI_INTO_OPEN) )
		{
			FVector passee_origin = player->GetMovement()->GetCurrentPosition();
			FVector pass_origin = likely_passer->GetMovement()->GetCurrentPosition();

			float fx = pass_origin.x * play_dir * current_x_direction;	/// Fx and path positions are in the space of the team's relative direction with -1 being left, +1 being right (I think)
			float space_left = (fx - leftline);
			float space_right = (rightline - fx);
			MabMath::ClampLower( space_left, 0.0f );
			MabMath::ClampLower( space_right, 0.0f );

			bool left_is_open = space_left > space_right;

			priority_contributions[ PRI_INTO_OPEN ] = 0;
			if(!left_is_open && passee_origin.x * play_dir > pass_origin.x * play_dir)
			{
				priority_contributions[ PRI_INTO_OPEN ] = 10;
			}
			else if(left_is_open && passee_origin.x * play_dir < pass_origin.x * play_dir)
			{
				priority_contributions[ PRI_INTO_OPEN ] = 10;
			}
		}

		/// From the players top speed
		if ( pass_priorities_to_apply & (1<<PRI_SPEED) )
		{
			// Weight speed way higher in sevens
			if(playingSevensGame)
				priority_contributions[ PRI_SPEED ] = (int) (plr_attribs->GetSpeed() * 3.0f);
			else
				priority_contributions[ PRI_SPEED ] = (int) (plr_attribs->GetSpeed() * 1.0f);
		}

		/// From the players closure rate on the ball holder
		if ( pass_priorities_to_apply & (1<<PRI_CLOSURE) && game_state->GetBallHolder())
		{
			float closure_rate = game->GetSpatialHelper()->GetPlayerToPlayerClosingRate( player, game_state->GetBallHolder() );
			if ( closure_rate > 0.0f )
			{
				if(playingSevensGame)
					priority_contributions[ PRI_CLOSURE ] = 15 + (int) (closure_rate);
				else
					priority_contributions[ PRI_CLOSURE ] = 10 + (int) (closure_rate);
			}
		}

		/// From the number of players that are behind us - the more the better
		if ( pass_priorities_to_apply & (1<<PRI_LINE_BREAK) )
		{
			if( game->GetGameSettings().num_active_teams > 1 )
			{
				RLPResultList result_list;
				RLP_FILTERPARAMETERS filter_params;
				filter_params.filters = RLP_FILTER_TEAM | RLP_FILTER_EXCLUDE_AGGREGATES | RLP_FILTER_Z_CUTOFF;
				filter_params.z_cutoff_position = plr_movement->GetCurrentPosition().z;
				filter_params.z_cutoff_direction = plr_attribs->GetPlayDirection();
				filter_params.team = plr_attribs->GetOppositionTeam();
				filter_params.excluded_actions[0] = ACTION_TACKLER;
				filter_params.n_excluded_actions = 1;
				game->GetFilteredPlayerList( result_list, filter_params, NULL );
				int MULTIPLIER = 3;

				if(playingSevensGame)
					MULTIPLIER = 5;

				priority_contributions[ PRI_LINE_BREAK ] = (int) result_list.size() * MULTIPLIER;
			}
			else
			{
				priority_contributions[ PRI_LINE_BREAK ] = 0;
			}

			if ( player == game_state->GetBallHolder() && (!player->GetActionManager()->IsActionRunning( ACTION_TACKLEE ) /* RUPORT || player->GetActionManager()->GetAction<RUTackleeAction>()->IsBreakingOut()*/ )  )
			{
				RURoleBaseBallHolder* base_ball_holder_role = MabCast< RURoleBaseBallHolder >( player->GetRole() );

				if ( base_ball_holder_role && !base_ball_holder_role->GetArePlayersObstructingForwardProgress() )
					priority_contributions[ PRI_LINE_BREAK ] += 40;
			}

			//SETDEBUGTEXTWORLD( RL3GAME_MDD_BASE + 50 + player->GetIndex(), player->GetCurrentPosition(), MabString( 16, "%d", result_list.size() ) );

			// Second check to see if the player has clear space ahead of him
		}

		/// From the players break tackle ability
		if ( pass_priorities_to_apply & (1<<PRI_BRKTKL) )
		{
			priority_contributions[ PRI_BRKTKL ] = (int) (plr_attribs->GetBreakTackleAbility() * 5.0f);
		}

		/// From the players forward speed (towards opposition goal line)
		/// Temporary until we get the forward progress thing
		if ( pass_priorities_to_apply & (1<<PRI_FORWARD_SPEED) )
		{
			priority_contributions[ PRI_FORWARD_SPEED ] = (int) (plr_movement->GetCurrentVelocity().z * play_dir * 0.65f);
		}

		/// If we are close to the line then is this player good
		/// at busting over
		if ( pass_priorities_to_apply & (1<<PRI_BUSTOVER) )
		{
			// If the attacking team is close to the line
			float dist_to_goal = extents.y/2.0f - (plr_movement->GetCurrentPosition().z * plr_attribs->GetPlayDirection() );
			const float DIST_TO_CONSIDER_BUSTOVER = 10.0f;

			const float MIN_BUSTOVER_TACKLE_ABILITY = 0.8f;
			if ( dist_to_goal < DIST_TO_CONSIDER_BUSTOVER && plr_attribs->GetBreakTackleAbility() > MIN_BUSTOVER_TACKLE_ABILITY )
				priority_contributions[ PRI_BUSTOVER ] = (int) (plr_attribs->GetBreakTackleAbility() * 5.0f);
		}

		/// For the pass direction assistance
		if ( pass_priorities_to_apply & (1<<PRI_LAST_PASS_DIR) )
		{
			float pass_dir_for_this_player = MabMath::Sign( plr_movement->GetCurrentPosition().x - game->GetBall()->GetCurrentPosition().x );

			priority_contributions[ PRI_LAST_PASS_DIR ] = 0;

			if ( last_pass_dir != 0.0f && last_pass_dir_times > 0 && last_pass_dir == pass_dir_for_this_player ) {
				int pass_add_modifier_idx = (int) last_pass_dir_times - 1;
				MabMath::ClampUpper( pass_add_modifier_idx, N_MAX_PASS_TIMES - 1 );
				priority_contributions[ PRI_LAST_PASS_DIR ] = PASS_TIMES_ADD_VALUES[ pass_add_modifier_idx ];
			}
		}

		// Formation pass priority...
		//if ( (pass_priorities_to_apply & (1<<PRI_FORMATION))  && current_formation!=NULL)
		//{
		//	int position_idx = GetFormationPositionIdx(player);
		//	if(position_idx!=-1)
		//	{
		//		if(current_bh_key==0 || current_bh_key>=current_formation->num_bhkeys)
		//		{
		//			priority_contributions[ PRI_FORMATION] = GetFormationDataPlayer(current_formation,current_direction,position_idx)->pass_priority;
		//		}
		//		else
		//		{
		//			BHKEY *key = &current_formation->bhkeys[current_bh_key-1];
		//			priority_contributions[ PRI_FORMATION] = GetFormationBHPlayerKey(key,current_direction,position_idx)->pass_priority;
		//		}
		//	}
		//}

		// Sum the contributions
		pass_priority_sum = 0;
		for( int j = 0; j < PASS_PRIORITY_LAST; j++ )
		{
			if ( !(pass_priorities_to_apply & (1<<j)) )
				continue;
			pass_priority_sum += priority_contributions[j];
		}

		//SETDEBUGTEXTWORLD( 500000000 + (100 * player->GetAttributes()->GetTeamIndex()) + player->GetAttributes()->GetIndex(), player->GetMovement()->GetCurrentPosition(), MabString( 16, "%i", pass_priority_sum ) );

		//SIF_DEBUG_DRAW(
		//			SetText
		//			(
		//				1000000 + player_to_pass_to->GetAttributes()->GetDbId(),
		//				player_to_pass_to->GetMovement()->GetCurrentPosition() + FVector( 0.0f, 2.0f, 0.0f ),
		//				MabString( 16, "Dist clsng %.2f > %.2f", distance, pass_meta.max_pass_distance_by_closing).c_str(),
		//				MabColour::White
		//			)
		//);

		// Apply to the player
		blackboard.PostEntry<int>( RUBB_ATTRIB_PASS_PRIORITY, player, pass_priority_sum );
	}

	// Reset the recalc timer
	ResetPassPriorityTimer();
}

///-------------------------------------------------------------------------
/// Reset the pass priority timer
///  - pass priorities are recalculated every half a second.
///-------------------------------------------------------------------------

void SSEVDSFormationManager::ResetPassPriorityTimer( bool initial_event )
{
	const float FREQUENCY = 0.5f;
	pass_priority_timer.Reset( game->GetSimTime(), FREQUENCY, initial_event );
}

const static float SIDELINE_BUFFER = 0.8f; // Keep a buffer from the sideline

void SSEVDSFormationManager::GetLineZoneMovement( SSRoleArea * area, float play_dir, int slot_idx, FVector &position, ARugbyCharacter*  player, int player_no )
{
	MABASSERT(area->isLine == true);

	float t = 0.0f;

	FieldExtents extents = game->GetSpatialHelper()->GetFieldExtents();
	extents.x *= 0.5f;
	extents.x -= SIDELINE_BUFFER;
	float leftline = -extents.x;
	float rightline = extents.x;
	FVector line_origin = FVector::ZERO;
	if (area && area->GetOrigin())
	{
		line_origin = area->GetOrigin()->GetOrigin();
	}
	// HES request to make players go to the openside during rucks.
	bool sevens = game->GetGameSettings().game_settings.GameModeIsR7();
	if(sevens && game->GetGameState()->GetPhase() == RUGamePhase::RUCK)
	{
		float dist_from_closest_sideline = (FIELD_WIDTH * 0.5f) - MabMath::Fabs( line_origin.x );
		const static float MIN_DIST_FROM_SIDELINE_FOR_BLINDSIDE = 10.0f;

		/// If we are too close to the sideline then all players go on the open side
		if ( dist_from_closest_sideline < MIN_DIST_FROM_SIDELINE_FOR_BLINDSIDE )
		{
			area->line->type = ERugbyFormationLineType::OPENBLIND;
		}
	}

	if (area->line->type == ERugbyFormationLineType::BIASMINSPACING)
	{
		const float spacing = area->line->overrideSpacing >= 0.0f ? area->line->overrideSpacing : area->line->spacing;
		const float min_spacing = area->line->minSpacing;
		//float max_spacing = 10.0f;

		float fx = line_origin.x * play_dir * current_x_direction;
		//float bh_gap = area->mv_param1;		// "mv_param1" is spacing of 'first receiver' from ball holder.

		/// Work out what proportion of left/right slots are "ideal" for a balanced line
		float space_left = (fx - leftline);
		float space_right = (rightline - fx);
		MabMath::ClampLower( space_left, 0.0f );
		MabMath::ClampLower( space_right, 0.0f );

		float left_slot_pct = space_left / (rightline - leftline);
		MabMath::Clamp( left_slot_pct, 0.0f, 1.0f );

		int num_players = area->SlotsUsed();
		int ideal_players_left = (int) MabMath::Round( left_slot_pct * (float) num_players);
		int ideal_players_right = num_players - ideal_players_left;

		int max_players_left  = (int) ((fx - leftline)  / min_spacing);
		int max_players_right = (int) ((rightline - fx) / min_spacing);

		/// If we haven't initialised the number of players then do so
		if ( backline_players_total < 0 || backline_players_total != num_players )
		{
			backline_players_total = num_players;
			backline_players_left = ideal_players_left;
			backline_players_right = ideal_players_right;
		}

		if ( backline_players_left > max_players_left )
		{
			backline_players_left = max_players_left;
			backline_players_right = num_players - backline_players_left;
		}

		if ( backline_players_right > max_players_right )
		{
			backline_players_right = max_players_right;
			backline_players_left = num_players - backline_players_right;
		}

		/// Work out if we are on the left or right
		/// 0 -> backline_players_left = LEFT
		/// backline_players_left -> total = RIGHT

		// Ismael: To fix this by somebody to avoid dividing by 0 at the line -> float max_slot_size = ...
		if ( backline_players_left == 0 )
			backline_players_left = 1;

		if ( backline_players_right == 0 )
			backline_players_right = 1;

		/// Work out slot offset from formation origin based on calculated slots left/slots right
		bool is_left_slot = slot_idx < backline_players_left;
		int   slot_offset   = is_left_slot ? slot_idx - backline_players_left : slot_idx - backline_players_left + 1;

		float max_slot_size = 1.0f;
		if(is_left_slot)
		{
			if(backline_players_left>0)
				max_slot_size = space_left / backline_players_left;
		}
		else
		{
			if(backline_players_right>0)
				max_slot_size = space_right / backline_players_right;
		}


		float slot_size = MabMath::Min( spacing, max_slot_size );
		MabMath::ClampLower( slot_size, min_spacing );

		float slot_x = fx + (float) slot_offset * slot_size;
		position.x = slot_x * play_dir * current_x_direction;
		position.y = 0.0f;
		position.z = line_origin.z;

		t = area->GetLineIntersectT(position, 0.0f);
		MABASSERT( t >= 0.0f && t <= 1.0f );
	}
	else if (area->line->type == ERugbyFormationLineType::OPENBLIND)
	{
		// Used for breaks in play, rucks, scrums, mauls to form backline properly
		// UpdateOpenBlind has sorted and stored slots for this
		// Left t = 0, Right t = 1, Assume t = 0.5 is the ball holder slot

		// Use the line spacing if we can fit players on both sides
		const float spacing = area->line->overrideSpacing >= 0.0f ? area->line->overrideSpacing : area->line->spacing;

		float fx = line_origin.x * play_dir * current_x_direction;	/// Fx and path positions are in the space of the team's relative direction with -1 being left, +1 being right (I think)
		float space_left = (fx - leftline);
		float space_right = (rightline - fx);
		MabMath::ClampLower( space_left, 0.0f );
		MabMath::ClampLower( space_right, 0.0f );
		float space_open  = space_left > space_right ? space_left : space_right;
		float space_blind = space_left > space_right ? space_right : space_left;

		float open_spacing  = space_open  / area->slots_open.size();
		float blind_spacing = space_blind / area->slots_blind.size();
		open_spacing = MabMath::Min( open_spacing, spacing );
		blind_spacing = MabMath::Min( open_spacing, blind_spacing );

		bool slot_is_open = area->openside_dir == SSRoleArea::OSD_LEFT ? slot_idx < (int) area->slots_open.size() : slot_idx >= (int) area->slots_blind.size();
		float slot_size = slot_is_open ? open_spacing : blind_spacing;
		int slot_offset;
		if ( area->openside_dir == SSRoleArea::OSD_LEFT )
		{
			if ( slot_is_open )
				slot_offset = slot_idx - (int)area->slots_open.size();
			else
				slot_offset = slot_idx - (int)area->slots_open.size() + 1;
		}
		else
		{
			if ( !slot_is_open )
				slot_offset = slot_idx - (int)area->slots_blind.size();
			else
				slot_offset = slot_idx - (int)area->slots_blind.size() + 1;
		}

		float slot_x = fx + (float) slot_offset * slot_size;
		position.x = slot_x * play_dir * current_x_direction;
		position.y = 0.0f;
		position.z = line_origin.z;

		t = area->GetLineIntersectT(position, 0.0f);
		MABASSERT( t >= 0.0f && t <= 1.0f );
	}
	else if (area->line->type == ERugbyFormationLineType::MINSPACING)
	{
		const int total_slots = int(area->splineLength / area->line->minSpacing);
		if (total_slots == 1)
		{
			t = 0.5f;
		}
		else
		{	
			t = float(slot_idx) / float(total_slots - 1);
		}
	}
	else if (area->line->type == ERugbyFormationLineType::MARKOPPONENT)
	{
		//MABBREAKMSG( "Didn't think this was being used" );
		// Dewald WW - I'm using this now. In the defensive scrum formation i'm using it to line up properly with the attacking scrum formation.
		// Since we only have 3 people in the backline the formations never mirrored, i.e. 2 on the left and 1 on the right for each side.
		// Not sure why they had the break in here before, as it seems to work fine.

		ARugbyCharacter* mark = GetPlayerToMark(player);
		if(mark!=NULL)
		{
			t = area->GetLineIntersectT(mark->GetMovement()->GetCurrentPosition(), 0.0f );//MabMath::Deg2Rad(-20.0f));
			//MABLOGDEBUG("Team - Player: %d - %d, Marking: %d at %0.3f", player->GetAttributes()->GetTeam()->GetIndex(), player->GetAttributes()->GetTeamIndex(), mark->GetAttributes()->GetTeamIndex(), t );
		}
		else
		{
			//float len = area->GetPathLength();
			//(area->num_slots * area->minspacing) / len;
			//MABLOGDEBUG("Team - Player: %d - %d, No one to mark: %0.3f", player->GetAttributes()->GetTeam()->GetIndex(), player->GetAttributes()->GetTeamIndex(), t );
			bool non_marker = false;

			// check if anyone else on the line is no marking anyone
			for ( size_t i = 0; i < area->slots.size(); ++i )
			{
				if ( (int) i != slot_idx )
				{
					ARugbyCharacter* other_player = area->GetSlotPlayer((int)i);

					if ( other_player != NULL )
					{
						int index = other_player->GetAttributes()->GetTeamIndex();
						if ( area->what_att_slot_to_mark[index] == -1 )
							non_marker = true;
					}
				}
			}

			if ( non_marker )
			{
				t = 0.565f;
				area->what_att_slot_to_mark[player_no] = -2;
			}
			else
				t = 0.435f;
		}
	}
	else
	{
		if(area->slots.size()>1)
		{
			t = (float)slot_idx / (float)(area->slots.size()-1);
		}
	}

	position = area->GetPathPosition(t,true);
	//MABLOGDEBUG("Pos : %0.3f,%0.3f,%0.3f", position.x, position.y, position.z );

	if(slot_idx!=0)
		position.z += area->GetPathZOffset(player);

	game->GetSpatialHelper()->ClampWaypointToSideLine( position, SIDELINE_BUFFER );
	ClampPlayerZPosition( player, position );

}

void SSEVDSFormationManager::GetEllipticalZoneMovement( SSRoleArea * area, FVector &position, float play_dir, ARugbyCharacter*  player, int slot_idx, float time /* = 0.0f*/ )
{
	position = area->GetZonePosition(play_dir, time);

	if(area->slots.size()>1)
	{
		float dx,dz;
		area->GetPlayerZonePosition(player,slot_idx,dx,dz, time);
		position.x += dx;
		position.z += dz;
	}

	// clamp positions to the try line unless you are the full back or the ball is over the try line
	game->GetSpatialHelper()->ClampWaypointToSideLine( position, SIDELINE_BUFFER );
	ClampPlayerZPosition( player, position );
}

float SSEVDSFormationManager::GetPlayerUrgency( SSRoleArea * area, ARugbyCharacter* player, int player_no, FVector &position, ACTOR_SPEED& max_actor_speed )
{
	///-----------------------------
	/// Urgency calculation
	float urgency = 0.7f;
	max_actor_speed = AS_SPRINT;

	if (area != nullptr)
	{
		switch( area->get_urgency_mode() ) {
			case ERugbyFormationUrgencyMode::MINMAX:
				urgency = mFormationPlayers[player_no].initial_urgency;
				break;
			case ERugbyFormationUrgencyMode::XDIST_FROM_BALL:
				{
					float x_dist_from_ball = MabMath::Fabs( position.x - game->GetBall()->GetCurrentPosition().x );
					float pct = (x_dist_from_ball - area->get_min_urgency_dist_x()) / (area->get_max_urgency_dist_x() - area->get_min_urgency_dist_x());
					MabMath::Clamp( pct, 0.0f, 1.0f );

					//MABBREAKMSG(MabString(0, "ERugbyFormationUrgencyMode::XDIST_FROM_BALL Doesn't do anything? Formation: %s, Area: %s", current_formation->name, area->GetName()).c_str());
					// Dewald WW - Why is this case not doing anything with the values calculated?! I'd except it to do at least something like this:
					// This was from RC2, and some formations from RC2 used this urgency mode as well.
					urgency = MabMath::Lerp(area->get_min_urgency(), area->get_max_urgency(), pct);
				}
				break;
			case ERugbyFormationUrgencyMode::PARTICIPATION:
				{
					urgency = GetUrgencyByParticipation( player, max_actor_speed );
				}
				break;
		}
	}

	return urgency;
}

float SSEVDSFormationManager::GetUrgencyByParticipation( ARugbyCharacter* player, ACTOR_SPEED& returned_max_actor_speed )
{
	float urgency = 0.0f;

	RUTeam* ruteam = player->GetAttributes()->GetTeam();
	const RUDB_TEAM& team_attribs = ruteam->GetDbTeam();
	float participation_t = (ruteam == game->GetGameState()->GetAttackingTeam()) ? team_attribs.GetNormalisedAttack() : team_attribs.GetNormalisedDefense();

	static const float PART_URGENCIES_HIGH[PL_MAX] = { 0.99f, 0.99f, 0.7f, 0.3f };
	static const float PART_URGENCIES_LOW[PL_MAX]  = { 0.75f, 0.75f, 0.6f, 0.3f };
	static const ACTOR_SPEED MAX_ACTOR_SPEED[PL_MAX] = { AS_SPRINT, AS_SPRINT, AS_RUN, AS_JOG };
	PARTICIPATION_LEVEL participation_level = player->GetState()->GetParticipationLevel();

	float urgency_high = PART_URGENCIES_HIGH[participation_level];
	float urgency_low  = PART_URGENCIES_LOW[participation_level];

	static const float PART_URGENCIES_MULT_BU_DIFFICULTY[DIF_MAX] = { 1.0f, 1.0f, 1.0f, 1.2f, 1.4f };
	if ( participation_level == PL_INTERESTED || participation_level == PL_PARTICPATING )
	{
		if ( player->GetGameWorld()->GetGameState()->IsGameInStandardPlay() )
		{
			participation_t *= PART_URGENCIES_MULT_BU_DIFFICULTY[ game->GetGameSettings().difficulty ];
			MabMath::ClampUpper( participation_t, 1.0f );
		}
	}


	urgency = MabMath::Lerp( urgency_low, urgency_high, participation_t );

	/// Scale player urgencies by gameplay sliders
	float slider_t = 0.5f;
	if ( player->GetGameWorld()->GetGameState()->IsGameInStandardPlay() && game->GetGameSettings().game_settings.game_type != GAME_TRAINING && game->GetGameSettings().game_settings.game_type != GAME_MENU)
	{
		RUGameSettings* game_settings = &game->GetGameSettings();

		float slider_val = (ruteam == game->GetGameState()->GetAttackingTeam()) ? game_settings->game_settings.slider_attacking_urgency : game_settings->game_settings.slider_defensive_urgency;

		if(ruteam->GetIsR7ExclusiveTeam())
			slider_val *= (ruteam == game->GetGameState()->GetAttackingTeam()) ? 0.5f : 2.0f;

		MabMath::Clamp(slider_val,	(ruteam == game->GetGameState()->GetAttackingTeam()) ? PLAYER_PROFILE_ATTACKING_URGENCY_MIN : PLAYER_PROFILE_DEFENSIVE_URGENCY_MIN,
									(ruteam == game->GetGameState()->GetAttackingTeam()) ? PLAYER_PROFILE_ATTACKING_URGENCY_MAX : PLAYER_PROFILE_DEFENSIVE_URGENCY_MAX);

		slider_t   = (ruteam == game->GetGameState()->GetAttackingTeam()) ?
			(slider_val - PLAYER_PROFILE_ATTACKING_URGENCY_MIN) / (PLAYER_PROFILE_ATTACKING_URGENCY_MAX - PLAYER_PROFILE_ATTACKING_URGENCY_MIN) :
			(slider_val - PLAYER_PROFILE_DEFENSIVE_URGENCY_MIN) / (PLAYER_PROFILE_DEFENSIVE_URGENCY_MAX - PLAYER_PROFILE_DEFENSIVE_URGENCY_MIN);
		urgency *= slider_val;
		static const float MIN_URGENCY = 0.2f;
		MabMath::Clamp( urgency, MIN_URGENCY, 1.0f );
	}

	returned_max_actor_speed = MAX_ACTOR_SPEED[ participation_level ];

	/// When gameplay sliders go up to higher values allow higher top speeds to increase also
	const static float SLIDER_INCREASE_ACTOR_SPEED_1_THRESHHOLD = 0.70f;
	const static float SLIDER_INCREASE_ACTOR_SPEED_2_THRESHHOLD = 0.80f;
	const static float SLIDER_INCREASE_ACTOR_SPEED_3_THRESHHOLD = 0.90f;
	const static int SLIDER_INCREASE_ACTOR_SPEED_1_AMOUNT = 1;
	const static int SLIDER_INCREASE_ACTOR_SPEED_2_AMOUNT = 2;
	const static int SLIDER_INCREASE_ACTOR_SPEED_3_AMOUNT = 3;

	if ( slider_t > SLIDER_INCREASE_ACTOR_SPEED_3_THRESHHOLD )
		returned_max_actor_speed = (ACTOR_SPEED) (returned_max_actor_speed + SLIDER_INCREASE_ACTOR_SPEED_3_AMOUNT);
	else if ( slider_t > SLIDER_INCREASE_ACTOR_SPEED_2_THRESHHOLD )
		returned_max_actor_speed = (ACTOR_SPEED) (returned_max_actor_speed + SLIDER_INCREASE_ACTOR_SPEED_2_AMOUNT);
	else if ( slider_t > SLIDER_INCREASE_ACTOR_SPEED_1_THRESHHOLD )
		returned_max_actor_speed = (ACTOR_SPEED) (returned_max_actor_speed + SLIDER_INCREASE_ACTOR_SPEED_1_AMOUNT);

	MabMath::ClampUpper( returned_max_actor_speed, AS_SPRINT );

	return urgency;
}


/// Get the origin of the formation from the formation parameters
void SSEVDSFormationManager::UpdateFormationOrigin()
{
	//// Get the requested origin target
	formation_origin = game->GetGameState()->GetFormationTarget( mCurrentFormation->conditions.originTarget );
}

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
ARugbyCharacter* SSEVDSFormationManager::GetReceivingPlayer()
{
	ARugbyCharacter* closest_attacker = nullptr;

	// Find the closest attacker
	auto ballHolder = game->GetGameState()->GetBallHolder();
	if (ballHolder)
	{
		//RLP_DISTANCE_TO_BALL_SORT dist_sorter; // Sorter is not returning expected results? Doing sort below
		RLP_FILTERPARAMETERS params;
		RLPResultList attacking_players;
		params.filters |= RLP_FILTER_TEAM;
		params.filters |= RLP_FILTER_EXCLUDE_PLAYER;
		params.team = ballHolder->GetAttributes()->GetTeam();
		params.exclude_player = ballHolder;
		params.max_dist = 1.0f;
		params.n_excluded_actions = 2;
		params.excluded_actions[0] = ACTION_TACKLEE;
		params.excluded_actions[1] = ACTION_TACKLER;
		game->GetFilteredPlayerList(attacking_players, params, NULL);

		ARugbyCharacter* closestPlayer = nullptr;
		float lastDist = MAX_flt;
		auto ballPos = game->GetBall()->GetCurrentPosition(true);
		for (RLPResultList::iterator it = attacking_players.begin(); it != attacking_players.end(); ++it)
		{
			ARugbyCharacter* attacker = (*it).player;

			if (attacker)
			{
				float dist = FVector::Dist(ballPos, (*it).player->GetMovement()->GetCurrentPosition());

				if (dist < lastDist)
				{
					lastDist = dist;
					closest_attacker = attacker;
				}
			}
		}
		MABASSERT(closest_attacker != nullptr);
	}

	return closest_attacker;
}

//#if defined ENABLE_GAME_DEBUG_MENU
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
void SSEVDSFormationManager::UpdateDebug()
{
	/// Draw debug for lines/zones
	#define ENABLE_INGAME_FORMATION_DEBUG
	#if defined ENABLE_INGAME_FORMATION_DEBUG

	bool debugAttacking = CVarFormationVisibleAtt.GetValueOnAnyThread() > 0 || FParse::Param(FCommandLine::Get(), TEXT("FormationAttVisible"));
	bool debugDefending = CVarFormationVisibleDef.GetValueOnAnyThread() > 0 || FParse::Param(FCommandLine::Get(), TEXT("FormationDefVisible"));


	// WJS RLC TEST 
	///debugAttacking = true;
	// debugDefending = true;

	if ( !debugAttacking && !debugDefending)
		return;

	const static float NOT_USED_ALPHA = 0.2f;

	bool is_attacking = team == game->GetGameState()->GetAttackingTeam();
	FColor colour1 = is_attacking ? FColor::Green : FColor::Red;

	if (is_attacking)
	{
		//#rc3_legacy_debug_cmd Add to command line args
		if (!debugAttacking)
		{
			return;
		}
		GEngine->AddOnScreenDebugMessage(-1, -1, colour1, FString::Printf(TEXT("Attacking: %s"), *mCurrentFormation->name));
	}
	else
	{
		//#rc3_legacy_debug_cmd Add to command line args
		if (!debugDefending) 
		{
			return;
		}
		GEngine->AddOnScreenDebugMessage(-1, -1, colour1, FString::Printf(TEXT("Defending: %s"), *mCurrentFormation->name));
	}

	/*const*/ static bool DRAW_LINES = true;
	static const int SPACE_INCREMENT = 2;
	const static int UNUSED_COL_DIVISOR = 4;

	/// Draw the formation origin
	for(int i = 0; i < (int)current_areas.size(); i++)
	{
		SSRoleArea* area = current_areas[i];

		/// Work out total number of players in this area
		int total_players_in_area = 0;
		for (int j = 0; j < area->get_roles().Num(); j ++)
		{
			FSerialiseFormationRole role = area->get_roles()[j];
			int num_players = role.numPlayers;
			int override_num_players = role.overrideNumPlayers;
			if (override_num_players >= 0)
			{
				num_players = override_num_players;
			}
			total_players_in_area += num_players;
		}

		FColor colour2 = area->GetOrigin()->GetColour();

		if ( area->is_line() )
		{
			static const int DIVISIONS = 40;
			float t_div = 1.0f / (float) DIVISIONS;

			for( int j = 0; j < DIVISIONS; j += SPACE_INCREMENT )
			{
				FVector from, to;
				float t_from = t_div * j;
				float t_to   = t_from + t_div;
				from = area->GetPathPosition( t_from, true );
				MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(from, from_converted);
				to = area->GetPathPosition( t_to, true );
				MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(to, to_converted);

				const FColor& col = ((j / SPACE_INCREMENT) % 2 == 0) ? colour1 : colour2;
				FColor used_col = col;
				if ( total_players_in_area == 0 )
				{
					used_col.A = NOT_USED_ALPHA;
					used_col.R /= UNUSED_COL_DIVISOR;
					used_col.G /= UNUSED_COL_DIVISOR;
					used_col.B /= UNUSED_COL_DIVISOR;
				}

				DrawDebugLine(game->GetGameInstance().GetWorld(), from_converted, to_converted, used_col, false, 0.03f);
			}

			// Print name on middle of line
			FVector origin_intersect = area->GetPathPosition( 0.5f, true );

			MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(origin_intersect, origin_intersect_converted);

			DrawDebugString(game->GetGameInstance().GetWorld(), origin_intersect_converted, area->get_name(), NULL, colour2, 0.03f);

			// Draw line to origin to show association
			if ( DRAW_LINES )
			{
				FVector Origin = area->GetOrigin()->GetOrigin();
				MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(Origin, Origin_converted);
				DrawDebugLine(game->GetGameInstance().GetWorld(), Origin_converted, origin_intersect_converted, colour2, false, 0.01f, 0, 6.0f);
			}
		} 
		else 
		{
			// Draw the zone ellipse
			float half_width  = (area->zone->width.get_graph_value(0.0f) * 0.5f) * 100.0f;
			float half_height = (area->zone->height.get_graph_value( 0.0f ) * 0.5f) * 100.0f;

			static const int DIVISIONS = 24;
			FVector centre = area->GetZonePosition( (float)-team->GetPlayDirection() );
			MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(centre, centre2);

			float angle_div = PI * 2.0f / (float) DIVISIONS;
			for( int j = 0; j < DIVISIONS; j+=SPACE_INCREMENT )
			{
				float ang_from = j * angle_div;
				float ang_to   = ang_from + angle_div;
				FVector from(centre2.x + MabMath::Sin( ang_from ) * half_width, centre2.y + MabMath::Cos( ang_from ) * half_height, 0.0f );
				FVector to  (centre2.x + MabMath::Sin( ang_to   ) * half_width, centre2.y + MabMath::Cos( ang_to   ) * half_height, 0.0f );
				const FColor& col = ((j / SPACE_INCREMENT) % 2 == 0) ? colour1 : colour2;
				FColor used_col = col;
				if ( total_players_in_area == 0 )
				{
					used_col.A = NOT_USED_ALPHA ;
					used_col.R /= UNUSED_COL_DIVISOR;
					used_col.G /= UNUSED_COL_DIVISOR;
					used_col.B /= UNUSED_COL_DIVISOR;
				}

				DrawDebugLine(game->GetGameInstance().GetWorld(), from, to, used_col, false, 0.01f, 0, 3.0f);
			}

			// Print name in middle of zone
			FString label = FString::Printf(TEXT("%s::%s:%d"), *area->get_name(),*area->getPlayerMaskAsString(), total_players_in_area );
			DrawDebugString(game->GetGameInstance().GetWorld(), centre2, label, NULL, colour2, 0.03f);

			// Draw line to origin target
			if ( DRAW_LINES )
			{
				FVector Origin = area->GetOrigin()->GetOrigin();
				MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(Origin, Origin_converted);
				DrawDebugLine(game->GetGameInstance().GetWorld(), Origin_converted, centre2, colour2, false, 0.0f);
			}
		}
	}
	#endif
}
#endif

void SSEVDSFormationManager::ClampPlayerZPosition( ARugbyCharacter*  player, FVector &position )
{
	// clamp positions to the try line unless you are the full back or the ball is over the try line
	float clamp_length = 0.0f;
	const bool is_defensive_set_piece = team == game->GetGameState()->GetDefendingTeam() &&
			( mCurrentFormation->conditions.gamePhase == ERugbyFormationGamePhase::SCRUM ||
			  mCurrentFormation->conditions.gamePhase == ERugbyFormationGamePhase::LINEOUT );

	if ( is_defensive_set_piece )
		clamp_length = FIELD_LENGTH * 0.5f;
	else if ( game->GetBall()->IsOverTryline() ||
		 player->GetRole()->RTTGetType() == RURoleFullback::RTTGetStaticType()
		 || team == game->GetGameState()->GetAttackingTeam()
		 || mCurrentFormation->conditions.gamePhase == ERugbyFormationGamePhase::CONVERSION
	)
		clamp_length = ( FIELD_LENGTH + FIELD_IN_GOAL_LENGTH ) * 0.5f - 0.5f;
	else
		clamp_length = FIELD_LENGTH * 0.5f;

	MabMath::Clamp( position.z, -clamp_length, clamp_length );
}
#pragma optimize("", on)