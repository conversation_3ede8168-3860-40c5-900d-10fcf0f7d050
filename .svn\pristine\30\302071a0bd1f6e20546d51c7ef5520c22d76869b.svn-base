#pragma once

#include "Rugby.h"
#include "CoreMinimal.h"
#include "Types/MabTypes.h"

inline FString MabCharArrayToFString(const MabChar* fmt, ...)
{
	va_list vlist;
	MabChar	printString[2048];
	int l = 0;

	checkf(fmt, TEXT("buffer is null"));

	va_start(vlist, fmt);
	l += vsnprintf(printString + l, sizeof(printString) - l, fmt, vlist);
	va_end(vlist);
	if ((l + 1) < sizeof(printString) && printString[l - 1] != '\n')
	{
		printString[l++] = '\0';
	}

	FString unrealString(printString);
	return unrealString;
}

#define MABLOG_CAT(category, format, ...) UE_LOG(category, Display, TEXT("%s"), *MabCharArrayToFString(format, ##__VA_ARGS__));

#define MABLOGDEBUG(format, ... ) MABLOG_CAT(LogMab, format, ##__VA_ARGS__)
#define MABLOGMSG( channel, type, format, ... ) MABLOG_CAT(LogMab, format, ##__VA_ARGS__)

// Network trace logs for JosephG
#ifdef  ENABLE_VERBOSE_NETWORK_LOG
#define wwNETWORK_TRACE_JG(format, ...) MABLOG_CAT(LogDesync, format, ##__VA_ARGS__)
#else
#define wwNETWORK_TRACE_JG(...)
#endif

#if UE_BUILD_SHIPPING
#define ggNETWORK_TRACE_JG(...)
#else
#define ggNETWORK_TRACE_JG(format, ...) MABLOG_CAT(LogDesync, format, ##__VA_ARGS__)
#endif

#define wwNETWORK_TRACE_JG_ALWAYS(format, ...) MABLOG_CAT(LogDesync, format, ##__VA_ARGS__)
#define wwNETWORK_TRACE_JG_DISABLED(...)

#define MABBREAK()				UE_DEBUG_BREAK()
#define MABBREAKMSG( y )												\
{																		\
	UE_LOG(LogTemp, Display, TEXT("%s"), ANSI_TO_TCHAR(y));				\
	UE_DEBUG_BREAK();													\
}

#ifdef MAB_CHECKS_DONT_CRASH
	#define MABASSERT( x )			ensure(x)
	#define MABASSERTMSG( x, y )	ensureMsgf(x, TEXT("%s"), ANSI_TO_TCHAR(y))

	#define MABVERIFY( x )			ensure(x)
	#define MABVERIFYMSG( x, y )	ensureMsgf(x, TEXT("%s"), ANSI_TO_TCHAR(y))
#else
	#define MABASSERTWARNING()		ANSI_TO_TCHAR("Mab assert usage: Please confirm condition validity and update usage to check/verify/ensure.")
	#define MABVERIFYWARNING()		ANSI_TO_TCHAR("Mab verify usage: Please confirm condition validity and update usage to check/verify/ensure.")

    //checkf commented as it completely crashes the game. Changed to ensureAlways so that you get the assert, but you have a choice to proceed ahead or fix it.    
    //#define MABASSERT( x )			checkf(x, TEXT("%s\n%s"), ANSI_TO_TCHAR(#x), MABASSERTWARNING())
	//#define MABASSERTMSG( x, y )	checkf(x, TEXT("%s\n%s"), ANSI_TO_TCHAR(y), MABASSERTWARNING())
    
    
	#define MABASSERT( x )			ensureAlways(x)
	#define MABASSERTMSG( x, y )	ensureAlwaysMsgf(x, TEXT("%s"), *(MabCharArrayToFString(y)))

	#define MABVERIFY( x )			verifyf(x, TEXT("%s\n%s"), ANSI_TO_TCHAR(#x), MABVERIFYWARNING())
	#define MABVERIFYMSG( x, y )	verifyf(x, TEXT("%s\n%s"), ANSI_TO_TCHAR(y), MABVERIFYWARNING())
#endif

//#define MABASSERT( x )		(void)0
//#define MABASSERTMSG( x, y )	(void)0

#define MAB_STATIC_ASSERT(x)	static_assert(x, "compile time assert")

#define MAB_PROFILE_SECTION(...) do {} while(false)
#define MAB_PROFILE_SECTION_START(...) do {} while(false)
#define MAB_PROFILE_SECTION_END(...) do {} while(false)
