/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef SS_CONTEXT_BUCKET_H
#define SS_CONTEXT_BUCKET_H

#include "Match/Debug/RUCBDebugService.h"
#include "Match/SIFGameWorld.h"

#include "Mab/MabInclude.h"
/*#rc3_legacy_include
#include "Mab/Lua/MabLuaAutoBinder.h" //#rc3_legacy_lua
#include "Mab/Objects/MabObject.h"
#include "Mab/Pool/MabPool.h"
#include "Mab/Time/MabTimeStep.h"
*/

//#rc3_legacy_include #include "MabBATSContext.h"
//#rc3_legacy_include #include "MabCentralTypeDatabase.h"
//#rc3_legacy_include #include "MabCentralTypeDatabase.h"
//#rc3_legacy_include #include "SIFDebug.h"
//#rc3_legacy_echar #include "SSContextBucket_echar.h"

class SIFGameWorld;

enum SSCB_CLASS
{
	SSCB_CLASS_NONE,
	SSCB_CLASS_GAMEPLAY,
	SSCB_CLASS_GAME_TIMING,
	SSCB_CLASS_GAMEPLAY_DERIVED,
	SSCB_CLASS_COLOUR,
	SSCB_CLASS_HISTORY_STAT_RAW
};

enum SSCB_TYPE
{
	SSCB_EVENT_TYPE_NONE,
	SSCB_EVENT_CROWD_INTENSITY,
	SSCB_EVENT_PLAYER_WALK_ON,
	SSCB_EVENT_PASS,
	SSCB_EVENT_KICK_AT_POSTS_READY,
	SSCB_EVENT_TRY_LIKELY,
	SSCB_EVENT_BALL_HOLDER_RUN,
	SSCB_EVENT_TRY_ATTEMPT,
	SSCB_EVENT_TRY,
	SSCB_EVENT_TOUCHDOWN_OWN_GOAL,
	SSCB_EVENT_KICK,
	SSCB_CONVERSION_TRANSITION,
	SSCB_CONVERSION_TRANSITION_FOLLOW_UP,	//DANGER: Not Deterministic ABROWN
	SSCB_EVENT_KICK_RESULT_ANTICIPATE,
	SSCB_EVENT_KICK_RESULT,
	SSCB_EVENT_KICK_TERRITORY,
	SSCB_EVENT_CROSSED_POST_WRONG_TYPE,
	SSCB_EVENT_RUCK,
	SSCB_EVENT_RUCK_JOIN,
	SSCB_EVENT_RUCK_RESULT,
	SSCB_EVENT_RUCK_SCOOT,
	SSCB_EVENT_RUCK_SLOW,
	SSCB_EVENT_BREAKDOWN_CONTEST,
	SSCB_EVENT_BREAKDOWN_HOLDING_ON,
	SSCB_EVENT_BREAKDOWN_HOLDING_ON_ILLEGAL,
	SSCB_EVENT_BREAKDOWN_SHOULD_RELEASE,
	SSCB_EVENT_BREAKDOWN_SHOULD_RELEASE_ILLEGAL,
	SSCB_EVENT_BREAKDOWN_TURNOVER,
	SSCB_EVENT_SCRUM_TOUCH_SIGNALLED,
	SSCB_EVENT_SCRUM_CALLED,
	SSCB_EVENT_SCRUM,
	SSCB_EVENT_SCRUM_BALL_IN,
	SSCB_EVENT_SCRUM_PUSHING,
	SSCB_EVENT_SCRUM_RESULT,
	SSCB_EVENT_NUMBER_EIGHT_PICKUP,
	SSCB_EVENT_BALL_DEAD,
	SSCB_EVENT_KICK_AND_CHASE,
	SSCB_EVENT_LINE_BREAK,
	SSCB_EVENT_KICKOFF_READY,
	SSCB_EVENT_KICKOFF_WHISTLE,
	SSCB_EVENT_KICKOFF_OUT_ON_FULL,
	SSCB_EVENT_KICK_SLOW,
	SSCB_EVENT_KICK_TIMED_OUT,
	SSCB_EVENT_TIME_WASTING,
	SSCB_EVENT_SIDE_STEP,
	SSCB_EVENT_LINEOUT_SIGNALLED,
	SSCB_EVENT_LINEOUT_DECISION,
	SSCB_EVENT_LINEOUT_READY,
	SSCB_EVENT_LINEOUT_THROW,
	SSCB_EVENT_LINEOUT_MISS,
	SSCB_EVENT_LINEOUT_CATCH,
	SSCB_EVENT_LINEOUT_MAUL,
	SSCB_EVENT_LINEOUT_RUN,
	SSCB_EVENT_LINEOUT_PASS,
	SSCB_EVENT_LINEOUT_SLAP_DOWN,
	SSCB_EVENT_LINEOUT_FAULT,
	SSCB_EVENT_BROKEN_TACKLE,
	SSCB_EVENT_TACKLE,
	SSCB_EVENT_TACKLEE_ON_GROUND,
	//Brian hack
	//SSCB_EVENT_TACKLE_COUNT_READ,
	//SSCB_EVENT_TACKLEE_ON_GROUND_TACKLENUMBERS,
	SSCB_EVENT_INJURY,
	SSCB_EVENT_CARRIED_INTO_TOUCH,
	SSCB_EVENT_BALL_IN_TOUCH,
	SSCB_EVENT_TIME_KEEPING,
	SSCB_EVENT_TIME_TICK,
	SSCB_TIMING_NEW_PHASE,
	SSCB_EVENT_FIST_PUMP,
	SSCB_EVENT_FOWARD_PASS,
	SSCB_EVENT_MARK,
	SSCB_EVENT_CATCH,
	SSCB_EVENT_PENALTY,
	SSCB_EVENT_PENALTY_GOAL_KICK_DECIDED,
	SSCB_EVENT_PENALTY_TOUCH_KICK_DECIDED,
	SSCB_EVENT_PENALTY_TAP_DECIDED,
	SSCB_EVENT_BALL_DEAD_DROP_OUT,
	SSCB_EVENT_BALL_DEAD_SCRUM,
	SSCB_EVENT_ON_FULL_DECIDE_SCRUM,
	SSCB_EVENT_ON_FULL_DECIDE_REKICK,
	SSCB_EVENT_ON_FULL_DECIDE_LINEOUT,
	SSCB_EVENT_TAP_RESTART,
	SSCB_EVENT_QUICK_TAP,
	SSCB_EVENT_COLLECTED,
	SSCB_EVENT_PICKED_UP,
	SSCB_EVENT_FUMBLE,
	SSCB_EVENT_KNOCK_ON,
	SSCB_EVENT_OFFSIDE,
	SSCB_EVENT_STOPPAGE,
	SSCB_EVENT_ADVANTAGE,
	SSCB_EVENT_MAUL_FORMED,
	SSCB_EVENT_MAUL_BALL_RELEASED,
	SSCB_EVENT_MAUL_HELD_UP,
	SSCB_EVENT_MAUL_PROGRESS,
	SSCB_EVENT_MAUL_COLLAPSED,
	SSCB_EVENT_MAUL_LOOKS_HALTED,
	SSCB_STAT_TEAM,
	SSCB_STAT_PLAYER,
	SSCB_STAT_COMPETITION,
	SSCB_EVENT_YELLOW_CARD,
	SSCB_EVENT_RED_CARD,
	SSCB_EVENT_INJURY_STARTED,
	SSCB_EVENT_INTERCHANGE_STARTED,
	SSCB_EVENT_INTERCHANGE_MADE,
	SSCB_EVENT_DISTRACTION,
	SSCB_EVENT_TMO_DELIBERATION,
	SSCB_EVENT_TMO_DECISION,

	//pre match
	SSCB_PMP_OPENING_STATEMENT,
	SSCB_PMP_COMPETITION_STANDINGS,
	SSCB_PMP_SIGNIFICANCE_RANFURLY,
	SSCB_PMP_TEAM_FORM,
	SSCB_PMP_INTRODUCE_C2_1,
	SSCB_PMP_INTRODUCE_C2_2,
	SSCB_PMP_PREMATCH_STATEMENT,
	SSCB_PMP_MATCH_ATMOSPHERE,
	SSCB_PMP_MATCH_EXPECTATIONS,
	SSCB_PMP_C2_PREMATCH_CLOSING_STATEMENT,
	SSCB_PMP_TEAM_LINEUPS,
	SSCB_PMP_PRE_HAKA,
	SSCB_PMP_POST_HAKA,
	SSCB_PMP_C1_PREMATCH_CLOSING_STATEMENT,

	//half time
	SSCB_PMP_CURRENT_SCORE,
	SSCB_PMP_HIGH_LEVEL_STATS,
	SSCB_PMP_PASS_TO_C2,
	SSCB_PMP_GENERIC_AGREEMENT,
	SSCB_PMP_HALF_TIME_ANALYSIS,
	SSCB_PMP_STATS_OPENER,
	SSCB_PMP_TERRITORY_POSSESSION,
	SSCB_PMP_STATS_BREAKDOWN,
	SSCB_PMP_STATS_CLOSER,
	SSCB_PMP_HALF_TIME_CLOSING_C2,
	SSCB_PMP_HALF_TIME_CLOSING_C1,

	//full time
	SSCB_PMP_FULL_TIME,
	SSCB_PMP_MATCH_OUTCOME,
	SSCB_PMP_PLAYER_EMOTION,
	SSCB_PMP_COMPETITION_IMPACT,
	SSCB_PMP_IMPACT_RANFURLY,
	SSCB_PMP_WIN_STREAK,
	SSCB_PMP_FULLTIME_PASS_TO_C2,
	SSCB_PMP_FULL_TIME_OPENER,
	SSCB_PMP_MATCH_ANALYSIS,
	SSCB_PMP_KEY_FIGURES_STATS,
	SSCB_PMP_FULL_TIME_RESULT,
	SSCB_PMP_MATCH_TREND,
	SSCB_PMP_FINAL_SCORE,
	SSCB_PMP_FULL_TIME_SIGNOFF,
	SSCB_PMP_TROPHY_CEREMONY,
	SSCB_PMP_TROPHY_AWARDED,

	//colour
	SSCB_NISBO_AGREEMENT,
	SSCB_NISBO_ADVANTAGE,
	SSCB_NISBO_OFFSIDE_ADVANTAGE,
	SSCB_NISBO_SCRUM_RESULT,

	SSCB_EVENT_STOPPAGE_BANTER_START,

	SSCB_EVENT_REPLAY,
	SSCB_EVENT_REPLAY_TRY,

	SSCB_MARSHALL_TRY,
	SSCB_EVENT_TRY_FOLLOW_UP,
	SSCB_MARSHALL_POINTS_SCORED,
	SSCB_MARSHALL_POINTS_FOLLOWUP,

	SSCB_EVENT_MARSHALL_PRAISE_PLAYER,
	SSCB_EVENT_MARSHALL_LINEOUT_NOT_STRAIGHT,
	SSCB_EVENT_MARSHALL_LINEOUT_POSITIVE,
	SSCB_EVENT_MARSHALL_LINEOUT_NEGATIVE,
	SSCB_MARSHALL_AGREEMENT,
	SSCB_MARSHALL_DISSAPOINTED,
	SSCB_MARSHALL_TURNOVER,
	SSCB_MARSHALL_CATCH,
	SSCB_MARSHALL_KNOCKON,
	SSCB_MARSHALL_ERROR,
	SSCB_MARSHALL_BALL_NOT_FORWARD,
	SSCB_MARSHALL_HANDLING_ERROR_FOLLOW_UP,
	SSCB_MARSHALL_INJURY_FOLLOWUP,
	SSCB_MARSHALL_PENALTY,
	SSCB_MARSHALL_PENALTY_FOLLOWUP,
	SSCB_MARSHALL_PENALTYGOAL_DECIDE,
	SSCB_MARSHALL_TRY_ANALYSIS,
	SSCB_MARSHALL_REPLAY_FOLLOW_UP,
	SSCB_MARSHALL_SCRUM_CALLED,
	SSCB_MARSHALL_SCRUM_RESULT,
	SSCB_MARSHALL_KICK,
	SSCB_MARSHALL_TERRITORY_KICK,
	SSCB_MARSHALL_KICK_RESULT,
	SSCB_MARSHALL_INTERCHANGE_FOLLOWUP,
	SSCB_NISBO_TACKLE_FOLLOWUP,
	//Brian hack
	SSCB_EVENT_TACKLECOUNT_1,
	SSCB_EVENT_TACKLECOUNT_2,
	SSCB_EVENT_TACKLECOUNT_3,
	SSCB_EVENT_TACKLECOUNT_4,
	SSCB_EVENT_TACKLECOUNT_5,
	SSCB_EVENT_TACKLECOUNT_6,

	SSCB_EVENT_HANDOVER,

	SSCB_EVENT_QUICK_THROW_IN,
	SSCB_EVENT_QUICK_THROW_IN_DENIED,

	//SetPlays
	SSCB_SETPLAY_EVENT_PRE,
	SSCB_SETPLAY_EVENT_POST,

	SSCB_EVENT_4020,

	SSCB_NUM_TYPES,
};

class SSContextBucketEvent: public MabObject
{
	MABRUNTIMETYPE_HEADER(SSContextBucketEvent);
public:
	SSContextBucketEvent();
	SSContextBucketEvent(
		SSCB_CLASS ev_class,
		SSCB_TYPE ev_type,
		int simt );

	virtual void PrintDebug(){};
	static void DefineMabCentralInterfaces();
#ifdef ENABLE_RUGED
	virtual void Serialize( MabVector<MabString>&, MabVector<MabString>&);
	virtual void SerialiseUsingMabCentral( MabVector<MabString>&, MabVector<MabString>&);
	void UpdateDebugMonitor();

	static void SerialiseFromDefinition(
		MabObject* record,
		const MabTypeDefinition *definition,
		MabVector<MabString>& keys,
		MabVector<MabString>& values);

#endif

	int			event_class;
	int			event_type;
	int			event_time; //sim time steps
	int			record_id;
};


class SSCBBiggestEvent: public SSContextBucketEvent
{
public:
	SSCBBiggestEvent()
	{
		MABUNUSED(padd);
	};

private:
	char padd[68];
};

class SSContextBucket
{
public:
	SSContextBucket(SIFGameWorld* ggame);
	~SSContextBucket();
	template <class T> void AddEvent(const T& ev);

	///Delays the addition of this event to the main event queue until the absolute time marked in
	///the event has been reached.
	template <class T> void AddEventDelayed(const T &ev);

	template <class T> int GetEvents( MabVector<T>& results,
									   int num_results,
									   int time_criteria,
									   bool absolute_time,
									   SSCB_CLASS ev_class_criteria,
									   SSCB_TYPE ev_type_criteria );

	///returns an event relative to the most recent event, such that 0 would be the most recent event, 1 would be the
	///second most recent event, etc.
	SSContextBucketEvent* GetEvent ( unsigned int index_from_top );

	///returns the most recent event that satisfies the given criteria.
	SSContextBucketEvent* GetLastEvent ( MabVector<SSCB_TYPE>& criteria, SSCB_TYPE ev_stop_criteria = SSCB_EVENT_TYPE_NONE );

	///returns the most recent event that satisfies the given criteria, stopping when we have reached the terminating
	///criteria
	SSContextBucketEvent* GetLastEvent ( SSCB_TYPE ev_type_criteria, SSCB_TYPE ev_stop_criteria = SSCB_EVENT_TYPE_NONE );

	///returns the most recent event that came before 'staring_ev_id' and satisfies the given criteria, stopping when we have reached the terminating
	///criteria that is also before 'starting_ev_id'
	SSContextBucketEvent* GetLastEvent ( int starting_ev_id, SSCB_TYPE ev_type_criteria, SSCB_TYPE ev_stop_criteria = SSCB_EVENT_TYPE_NONE );

	///returns the event that follows immediately after the specified event, and is of the correct class
	SSContextBucketEvent* GetNextEvent ( int ev_id, SSCB_CLASS ev_class_criteria = SSCB_CLASS_NONE );

	///returns the most recent event of the given class (or any class if none specified)
	SSContextBucketEvent* GetNewestEvent( SSCB_CLASS criteria = SSCB_CLASS_NONE );

	size_t GetNumEvents() const;

	void DumpBucketDebug( SSCB_CLASS, SSCB_TYPE );
	void GetEventsDebug();
	void ClearEvents();

#ifdef ENABLE_RUGED
	void UpdateDebugMonitor( RUCBDebugService* service );
#endif

	void CleanPool();
	void Reset();
	void GameReset();
	void Update( const MabTimeStep& real_time_step );
	void ProcessDelayedAdds();
	const char* SSCBClass_to_string( SSCB_CLASS t );
	const char* SSCBType_to_string( SSCB_TYPE t );
private:
	std::vector<SSCBBiggestEvent> events_pool; //#rc3_legacy_comm, MabPool<SSCBBiggestEvent> events_pool; 

	typedef MabVector<SSContextBucketEvent*> Events;
	Events events;
	Events delayed_add_events;
	bool do_clean;
	int record_count;
	unsigned int last_cleanup_index;
	bool ordered_by_id;
	bool dirty;
	SIFGameWorld* game;

	static const int   POOL_SIZE;
	static const float INITIAL_LIFE;
	static const float START_CLEANUP_UTILIZATION;
	static const float STOP_CLEANUP_UTILIZATION;
	static const int   CLEAN_PER_FRAME;
	static const float LIFETIME_CLEANUP_THRESHHOLD;

#ifdef ENABLE_RUGED
	RUCBDebugService *rucb_service;
#endif

};

template <class T>
int SSContextBucket::GetEvents(
	MabVector<T>& results,
	int num_results,
	int time_criteria,
	bool absolute_time,
	SSCB_CLASS ev_class_criteria,
	SSCB_TYPE ev_type_criteria
	)
{
	results.clear();

	int counter = 0;
	int events_size = (int)events.size();

	int search_time;
	if ( absolute_time )
		search_time = time_criteria;
	else
		search_time = game->GetSimTime()->GetAbsoluteStepCount() - time_criteria;

	if ( num_results <= 0 )
	{
		if ( num_results == 0 )
			counter = events_size * -1;
		else
			counter = num_results;

		for ( int i = 0; i < events_size && counter < 0; i++ )
		{
			SSContextBucketEvent* ev = events[i];
			MABASSERT(ev);
			if(!ev) continue;
			if ( ( ev->event_type == ev_type_criteria || ev_type_criteria == SSCB_EVENT_TYPE_NONE )
				&& ( ev->event_class == ev_class_criteria || ev_class_criteria == SSCB_CLASS_NONE )
				&& ( ev->event_time >= search_time ) )
			{
				results.push_back( T(events[i]) );
				counter++;
			}
		}
	}
	else
	{
		for ( int i = events_size-1; i >= 0 && counter < num_results; i-- )
		{
			SSContextBucketEvent* ev = events[i];
			MABASSERT(ev);
			if(!ev) continue;
			if ( ( ev->event_type == ev_type_criteria || ev_type_criteria == SSCB_EVENT_TYPE_NONE )
				&& ( ev->event_class == ev_class_criteria || ev_class_criteria == SSCB_CLASS_NONE )
				&& ( ev->event_time >= search_time ) )
			{
				results.insert(results.begin(),T(events[i]));
				counter++;
			}
		}
	}


	return (int)results.size();
}

template <class T> void SSContextBucket::AddEvent(const T& ev)
{
#ifdef CONTEXT_BUCKET_VERBOSE_OUTPUT
	MABLOGDEBUG("ContextBucket AddEvent %i, sim time secs %f, sim time frames %i, num_events %i",
		ev.event_type, (float)ev.event_time / SIMULATION_RATE, ev.event_time, events.size() );
#endif
	//#rc3_legacy_comm, void* tmp = events_pool.Allocate();
	SSContextBucketEvent* bucket_ev = new T(ev);//#rc3_legacy_comm, new(tmp) T(ev);
	record_count++;
	bucket_ev->record_id = record_count;
	events.push_back(bucket_ev);
	dirty = true;

#ifdef ENABLE_RUGED

	if ( bucket_ev->event_class == SSCB_CLASS_GAMEPLAY ||
		 bucket_ev->event_class == SSCB_CLASS_GAME_TIMING )
	{
		MabBATSContext *bats_context =  SIFDebug::GetBATSContext();
		if ( bats_context != NULL )
			rucb_service = (RUCBDebugService*) bats_context->GetService("RUCBDEBUG");

		if ( rucb_service != NULL )
		{
			// Send ContextBucket event to StencilEd
			MabVector<MabString> keys;
			MabVector<MabString> values;

			bucket_ev->Serialize( keys, values );
			rucb_service->AddEvent( keys, values );
		}
	}
#endif
}


template <class T> void SSContextBucket::AddEventDelayed(const T &ev)
{
	//#rc3_legacy_comm, void* tmp = events_pool.Allocate();
	SSContextBucketEvent* bucket_ev = new T(ev);//#rc3_legacy_comm, new(tmp) T(ev);
	bucket_ev->record_id = -1;
	delayed_add_events.push_back(bucket_ev);
}

#endif
