// Copyright (c) 2017-2018 Wicked Witch Software Pty. Ltd.
/*
Helper class for the UI, remove matching logic from both UI and RGInstance
- replacement for SIFMatchmakingHelpers and bunches of other LUA interface fns

TODO
	- need a shutdown state, incl Leave()
	- need to see it working in ranked games
	- need to remove the NOT_REQD stuff after merging back into mainline
	- need to check that all of the helper stuff is gone
	- need to see it working in steam
	- need to see it working on switch (merge in diffs from afl)
	- tidy up RUInstance startup (blahPC etc)
*/

#include "MatchingManager.h"
#include "RugbyGameInstance.h"
#include "Engine/Classes/GameFramework/GameModeBase.h"
#include "GameModes/RugbyGameSession.h"
#include "Networking/RUNetworkState.h"
#include "Mab/Net/MabNetworkManager.h"
#include "Utility/Helpers/SIFGameHelpers.h"
#include "Utility/Helpers/SIFMatchmakingHelpers.h"
#include "ConnectionChecker.h"
#include "OnlineError.h"

// matching debug
#if 1
	#define MDB(sev,fmt,...) UE_LOG(LogTemp,sev,TEXT(fmt),__VA_ARGS__)
#else
	#define MDB(...)
#endif
// matching error
#define ME(fmt,...) {	\
	UE_LOG(LogTemp,Display,TEXT(fmt),__VA_ARGS__);	\
	ensureAlwaysMsgf(false,TEXT(fmt),__VA_ARGS__);	\
}

UMatchingManager::UMatchingManager() :
	UObject(),
	eventMatchmakingStarted(),
	eventMatchmakingFailure(),
	eventMatchmakingSuccess(),
	eventMatchmakingSessionBegin(),
	eventMatchmakingSessionEnd(),
	m_pRugbyGameInstance(NULL),
	m_privacy(EPrivacyLevel::Public),
	m_onlineMatch(EOnlineMode::Offline),
	m_gameType(GAME_MODE::GAME_MODE_RU13),
	m_matchingMode(EMatchmakingMode::Unranked),
	m_state(MS_Null),
	m_nextState(MS_Null),
	m_operationRequested(MS_Null),
	m_acceptInvitations(true),
	m_updateAccumulator(0.0f),
	m_lastStateChange(0.0f),
	m_maxSearchTime(5.0f),
	m_maxBetweenSearchTime(3.0f),
	m_adjustSearchDelay(0.0f),
	m_searchStartTime(0.0f)
{
	MDB(Display,"UMatchingManager::UMatchingManager",0);
	m_pRugbyGameInstance = SIFApplication::GetApplication();
	m_state = MS_Init;	// ready to roll
	m_nextState = MS_Init;
#if PLATFORM_PS4
	m_maxSearchTime = 30.0;
#endif
	
}

UMatchingManager::~UMatchingManager()
{
	MDB(Display,"UMatchingManager::~UMatchingManager",0);
}

void UMatchingManager::SetGametype(GAME_MODE g)
{
	MDB(Display,"UMatchingManager::SetGametype",0);
	m_gameType = g;
	SIFGameHelpers::GASetGameMode(g);
	//m_pRugbyGameInstance->GetMatchGameSettings()->game_settings.GetGameMode()
}

GAME_MODE UMatchingManager::GetGametype()
{
	return(m_gameType);
}

void UMatchingManager::SetAcceptInvitations(bool ai)
{
	MDB(Display,"UMatchingManager::SetAcceptInvitations",0);
	m_acceptInvitations = ai;
}

bool UMatchingManager::GetAcceptInvitations()
{
	return(m_acceptInvitations);
}

void UMatchingManager::CheckParamConsistency()
{
	MDB(Display,"UMatchingManager::CheckParamConsistency",0);
	bool doAssert = false;

	/*if (GetMode() != m_pRugbyGameInstance->GetMatchmakingMode())
	{
		doAssert = true;
		MDB(Display,"UMatchingManager::CheckParamConsistency mode is wrong %d != %d",GetMode(),m_pRugbyGameInstance->GetMatchmakingMode());
	}*/
	/*if (GetPrivacy() != m_pRugbyGameInstance->GetLobbyPrivacy())
	{
		doAssert = true;
		MDB(Display,"UMatchingManager::CheckParamConsistency privacy is wrong %d != %d",GetPrivacy(),m_pRugbyGameInstance->GetLobbyPrivacy());
	}*/
	if (m_pRugbyGameInstance->GetMatchGameSettings()->game_settings.private_match && SIFApplication::GetApplication()->GetLobbyPrivacy() == EPrivacyLevel::Public)
	{
		doAssert = true;
		MDB(Display,"UMatchingManager::CheckParamConsistency privacy is wrong 2 %d != %d", SIFApplication::GetApplication()->GetLobbyPrivacy(),m_pRugbyGameInstance->GetMatchGameSettings()->game_settings.private_match);
	}
	else if (!m_pRugbyGameInstance->GetMatchGameSettings()->game_settings.private_match && SIFApplication::GetApplication()->GetLobbyPrivacy() != EPrivacyLevel::Public)
	{
		doAssert = true;
		MDB(Display,"UMatchingManager::CheckParamConsistency privacy is wrong 3 %d != %d", SIFApplication::GetApplication()->GetLobbyPrivacy(),m_pRugbyGameInstance->GetMatchGameSettings()->game_settings.private_match);
	}
	/*if (GetOnline() != m_pRugbyGameInstance->GetOnlineMode())
	{
		doAssert = true;
		MDB(Display,"UMatchingManager::CheckParamConsistency online is wrong %d != %d",GetOnline(),m_pRugbyGameInstance->GetOnlineMode());
	}*/
	if (GetGametype() != m_pRugbyGameInstance->GetMatchGameSettings()->game_settings.GetGameMode())
	{
		doAssert = true;
		MDB(Display,"UMatchingManager::CheckParamConsistency type is wrong %d != %d",GetGametype(),m_pRugbyGameInstance->GetMatchGameSettings()->game_settings.GetGameMode());
	}
	if (!m_pRugbyGameInstance->GetMatchGameSettings()->game_settings.network_game)
	{
		doAssert = true;
		MDB(Display,"UMatchingManager::CheckParamConsistency not a n/w game %d",m_pRugbyGameInstance->GetMatchGameSettings()->game_settings.network_game);
	}
	if (doAssert)
	{
		MABASSERTMSG(false,"UMatchingManager::CheckParamConsistency something failed");
	}
}

bool UMatchingManager::StartSearch(ULocalPlayer* PlayerOwner)
{
	MDB(Display,"UMatchingManager::StartSearch",0);

	//Let connection manager know we are match making, this way if it fails we will timeout at some point
	SIFApplication::GetApplication()->GetConnectionManager()->StartedMatchMaking();

	bool ret = false;
#if 0
	if (!PlayerOwner)
		return(ret);

	UWorld* const World = m_pRugbyGameInstance->GetWorld();
	if (World)
	{
		AGameModeBase* const Game = World->GetAuthGameMode();
		if (Game)
		{
			ARugbyGameSession* const GameSession = Cast<ARugbyGameSession>(Game->GameSession);
			if (GameSession)
			{
				GameSession->OnFindSessionsComplete().RemoveAll(this);
				m_OnSearchSessionsCompleteDelegateHandle = GameSession->OnFindSessionsComplete().AddUObject(this, &UMatchingManager::OnSearchSessionsComplete);

				bool bIsDedicatedServer = false;
				bool bFindLAN = true;
				GameSession->FindSessions(PlayerOwner->GetPreferredUniqueNetId().GetUniqueNetId(), RUGAMESESSIONNAME, bFindLAN, !bIsDedicatedServer, GetMode());
				ret = true;
			}
		}
	}
#endif
	return(ret);
}

// temp
void UMatchingManager::OnRegisterJoiningLocalPlayerComplete(const FUniqueNetId& PlayerId, EOnJoinSessionCompleteResult::Type Result)
{
	MDB(Display,"UMatchingManager::OnRegisterJoiningLocalPlayerComplete Result=%d",Result);
	FinishJoinSession(Result);
}

//temp
bool UMatchingManager::FinishSessionCreation(EOnJoinSessionCompleteResult::Type Result)
{
	MDB(Display,"UMatchingManager::FinishSessionCreation Result=%d",Result);
	if (Result == EOnJoinSessionCompleteResult::Success)
	{
		// need to wait for lobby map to load now
		SetNextState(MS_LoadingLobby);
		return true;
		
	}
	else
	{		
		SetNextState(MS_Error);
		return false;
	}
}

// temp
void UMatchingManager::OnRegisterLocalPlayerComplete(const FUniqueNetId& PlayerId, EOnJoinSessionCompleteResult::Type Result)
{
	MDB(Display,"UMatchingManager::OnRegisterLocalPlayerComplete Result=%d",Result);
	FinishSessionCreation(Result);
}


// temp
bool UMatchingManager::FindSessions(ULocalPlayer* PlayerOwner, bool bIsDedicatedServer, bool bFindLAN)
{
	MDB(Display, "UMatchingManager::FindSessions", 0);
	GEngine->AddOnScreenDebugMessage(-1, 10.f, FColor::Red, FString::Printf(TEXT("Finding Sessions...")));

	check(PlayerOwner != nullptr);
	if (PlayerOwner)
	{
		ARugbyGameSession* const GameSession = m_pRugbyGameInstance->GetGameSession();
		if (GameSession)
		{
			GameSession->OnFindSessionsComplete().RemoveAll(this);
			m_OnSearchSessionsCompleteDelegateHandle = GameSession->OnFindSessionsComplete().AddUObject(this, &UMatchingManager::OnSearchSessionsComplete);

			SetNextState(MS_Searching);
			GameSession->FindSessions(PlayerOwner->GetPreferredUniqueNetId().GetUniqueNetId(), 
				RUGAMESESSIONNAME,
				bFindLAN,
				!bIsDedicatedServer,
				SIFApplication::GetApplication()->GetMatchmakingMode(),
				RUGAMESESSIONNAME_STR,
				GetGametype() == GAME_MODE_RU13 ? 0 : 1);
			return true;
		}
	}
	
	return false;
}

// temp
bool UMatchingManager::FinishJoinSession(EOnJoinSessionCompleteResult::Type Result)
{
	MDB(Display,"UMatchingManager::FinishJoinSession",0);
	if (Result != EOnJoinSessionCompleteResult::Success)
	{
		// We failed to join, so if we are in the middle of a search we want to continue with that rather than drop into error mode
		if (m_operationRequested == MS_Searching)
		{
			SetNextState(MS_Searching);
		}
		else
		{
			SetNextState(MS_Error);
		}
		return false;
	}

	// Successful join, need to wait for lobby map to load now
	MaybeUpdateStateMachine();
	SetNextState(MS_LoadingLobby);
	return true;
}



// temp
bool UMatchingManager::HostGame(ULocalPlayer* LocalPlayer)
{
	MDB(Display,"UMatchingManager::HostGame",0);
	GEngine->AddOnScreenDebugMessage(-1, 10.f, FColor::Red, FString::Printf(TEXT("Hosting Session...")));
	//XXX ShutdownActiveGamesSessions();

	return m_pRugbyGameInstance->HostGame(LocalPlayer);
}

/*


#define SEARCH_BUILD  BUILD_NUMBER
#define SEARCH_MATCHMODE	SETTING_CUSTOMSEARCHINT1
#define SEARCH_PRIVACYLEVEL SETTING_CUSTOMSEARCHINT2
#define SEARCH_NETWORK		SETTING_CUSTOMSEARCHINT3
#define SEARCH_INPROGRESS	SETTING_CUSTOMSEARCHINT4
#define SEARCH_OPEN_SLOTS	SETTING_CUSTOMSEARCHINT5
#define SEARCH_NETWORK_BUILD_NUMBER	SETTING_CUSTOMSEARCHINT6
#define SEARCH_RUGBY_RULES	SETTING_CUSTOMSEARCHINT7*/

int32 UMatchingManager::HandleFoundSession_XboxOne(FOnlineSessionSearchResult result,int32 index)
{
#if  PLATFORM_XBOXONE
	FString InProgressValue;
	result.Session.SessionSettings.Get(SEARCH_INPROGRESS, InProgressValue);

	FString RulesValue;
	result.Session.SessionSettings.Get(SEARCH_RUGBY_RULES, RulesValue);

	FString SlotValue;
	result.Session.SessionSettings.Get(SEARCH_OPEN_SLOTS, SlotValue);

	int32 OpenSlots = FCString::Atoi(*SlotValue);

/*
	if (OpenSlots == 0)
	{
		UE_LOG_ONLINE_SESSION(Log, TEXT("Open Slots: %i"), OpenSlots);
		return -1;
	}*/

	//Make sure we are on the correct network, the match isnt in progress and its the same build
	if (InProgressValue == SEARCH_NOT_INPROGRESSS_VALUE)
	{
		bool IsVlaidRuleType = false;

		if (GetGametype() == GAME_MODE_RU13 && RulesValue == SEARCH_15s_RULE_VALUE)
		{
			//15's match
			IsVlaidRuleType = true;
		}
		// Nick  WWS 7s to Womens //
		/*
		if (GetGametype() == GAME_MODE_SEVENS && RulesValue == SEARCH_7s_RULE_VALUE)
		{
			//7's match
			IsVlaidRuleType = true;
		}
		*/
		if (IsVlaidRuleType)
		{
			return index;
		}
	}

	return -1;

#endif
	
	return index;
}

int32 UMatchingManager::HandleFoundSession_PS4(FOnlineSessionSearchResult result, int32 index)
{

#if PLATFORM_PS4
	int32 SessionInProgress = -1;
	int32 OpenSlots = -1;
	int32 BuildNumber = -1;
	EMatchmakingMode MatchingMode = EMatchmakingMode::Unranked;
	EPrivacyLevel LobbyMode = EPrivacyLevel::Public;
	int32 NetworkMode = -1;
	UE_LOG_ONLINE_SESSION(Log, TEXT("HandleFoundSession_PS4 Start"));
	for (FSessionSettings::TConstIterator It(result.Session.SessionSettings.Settings); It; ++It)
	{
		FName Key = It.Key();
		const FOnlineSessionSetting& Setting = It.Value();
		FName SettingName =SIFApplication::GetApplication()->GetInviteManager()->ExternalIntAttrToSessionSettingKey(Key);

		if (SettingName == SEARCH_MATCHMODE)
		{
			int32 Mode = -1;
			Setting.Data.GetValue(Mode);

			FString StrValue = "";

			MatchingMode = (EMatchmakingMode)Mode;

			if (MatchingMode == EMatchmakingMode::Ranked)
			{
				StrValue = "Ranked";
			}

			if (MatchingMode == EMatchmakingMode::Unranked)
			{
				StrValue = "Unranked";
			}

			UE_LOG_ONLINE_SESSION(Log, TEXT("Match Type: %s"), *StrValue);
		}

		if (SettingName == SEARCH_PRIVACYLEVEL)
		{
			int32 Mode = -1;
			Setting.Data.GetValue(Mode);

			FString StrValue = "";

			LobbyMode = (EPrivacyLevel)Mode;

			if (LobbyMode == EPrivacyLevel::Friends)
			{
				StrValue = "Friends";
			}

			if (LobbyMode == EPrivacyLevel::InviteOnly)
			{
				StrValue = "InviteOnly";
			}

			if (LobbyMode == EPrivacyLevel::Public)
			{
				StrValue = "Public";
			}

			UE_LOG_ONLINE_SESSION(Log, TEXT("Privacy Level: %s"), *StrValue);
		}

		if (SettingName == SEARCH_NETWORK)
		{
			Setting.Data.GetValue(NetworkMode);

			UE_LOG_ONLINE_SESSION(Log, TEXT("Network Mode: %i"), NetworkMode);
		}

		if (SettingName == SEARCH_INPROGRESS)
		{
			Setting.Data.GetValue(SessionInProgress);

			FString StrValue = "No";

			if (SessionInProgress == 1)
			{
				StrValue = "Yes";
			}

			UE_LOG_ONLINE_SESSION(Log, TEXT("Session In Progress: %s "), *StrValue);
		}

		if (SettingName == SEARCH_OPEN_SLOTS)
		{
			Setting.Data.GetValue(OpenSlots);

			UE_LOG_ONLINE_SESSION(Log, TEXT("Open Slots: %i"), OpenSlots);
		}

		if (SettingName == SEARCH_NETWORK_BUILD_NUMBER)
		{
			Setting.Data.GetValue(BuildNumber);

			UE_LOG_ONLINE_SESSION(Log, TEXT("Build Number: %i"), BuildNumber);
		}
	}

/*
#if  UE_BUILD_SHIPPING	
		if (BuildNumber != BUILD_NUMBER)
		{
			return -1;
		}
#endif //  UE_BUILD_SHIPPING*/

		UE_LOG_ONLINE_SESSION(Log, TEXT("HandleFoundSession_PS4 End"));

		if (OpenSlots == 0 && SessionInProgress == SEARCH_INPROGRESSS_VALUE)
		{
			UE_LOG_ONLINE_SESSION(Log, TEXT("OpenSlots == 0 && SessionInProgress == SEARCH_INPROGRESSS_VALUE"));
			return -1;
		}

		if (SessionInProgress == SEARCH_INPROGRESSS_VALUE)
		{
			UE_LOG_ONLINE_SESSION(Log, TEXT("SessionInProgress == SEARCH_INPROGRESSS_VALUE"));
			return -1;
		}

		if (OpenSlots == 0)
		{
			UE_LOG_ONLINE_SESSION(Log, TEXT("OpenSlots == 0"));
			return -1;
		}

		UE_LOG_ONLINE_SESSION(Log, TEXT("HandleFoundSession_PS4 Valid Session Index"));
		
#endif // PLATFORM_PS4
		return index;
}


ARugbyGameSession* UMatchingManager::GetGameSession() const
{
	UWorld* const World =SIFApplication::GetApplication()->GetWorld();
	if (World)
	{
		AGameModeBase* const Game = World->GetAuthGameMode();
		if (Game)
		{
			return Cast<ARugbyGameSession>(Game->GameSession);
		}
	}

	return nullptr;
}

int32 UMatchingManager::HandleFoundSession_Windows(FOnlineSessionSearchResult result, uint32 index)
{
#if PLATFORM_WINDOWS
	int32 SessionInProgress = -1;
	int32 OpenSlots = -1;
	int32 BuildNumber = -1;
	EMatchmakingMode MatchingMode = EMatchmakingMode::Unranked;
	EPrivacyLevel LobbyMode = EPrivacyLevel::Public;
	int32 NetworkMode = -1;
	UE_LOG_ONLINE_SESSION(Log, TEXT("HandleFoundSession_Windows Start"));

#if WITH_EDITOR
		FString FoundLanKey;
		result.Session.SessionSettings.Get(SEARCH_LANKEY, FoundLanKey);

		if (!SIFApplication::GetApplication()->GetLanKeyValue().IsEmpty() || !FoundLanKey.IsEmpty())
		{
			//If we are using a LAN key then lets just ignore the rest of the settings and join this session
			// Check LAN keys
			if (FoundLanKey.Compare(SIFApplication::GetApplication()->GetLanKeyValue()) == 0)
			{
				return index;
			}
		}

#endif // WITH_EDITOR

	for (FSessionSettings::TConstIterator It(result.Session.SessionSettings.Settings); It; ++It)
	{
		FName Key = It.Key();
		const FOnlineSessionSetting& Setting = It.Value();
		FName SettingName = SIFApplication::GetApplication()->GetInviteManager()->ExternalIntAttrToSessionSettingKey(Key);

		if (SettingName == SEARCH_MATCHMODE)
		{
			int32 Mode = -1;
			Setting.Data.GetValue(Mode);

			FString StrValue = "";

			MatchingMode = (EMatchmakingMode)Mode;

			if (MatchingMode == EMatchmakingMode::Ranked)
			{
				StrValue = "Ranked";
			}

			if (MatchingMode == EMatchmakingMode::Unranked)
			{
				StrValue = "Unranked";
			}

			UE_LOG_ONLINE_SESSION(Log, TEXT("Match Type: %s"), *StrValue);

			if (MatchingMode != SIFApplication::GetApplication()->GetMatchmakingMode())
			{
				return -1;
			}
			else
			{
				SIFApplication::GetApplication()->SetSessionMaxPlayers(MatchingMode == EMatchmakingMode::Ranked ? RANKED_NUM_PLAYERS : DEFAULT_NUM_PLAYERS);
			}
		}

		if (SettingName == SEARCH_PRIVACYLEVEL)
		{
			int32 Mode = -1;
			Setting.Data.GetValue(Mode);

			FString StrValue = "";

			LobbyMode = (EPrivacyLevel)Mode;

			if (LobbyMode == EPrivacyLevel::Friends)
			{
				StrValue = "Friends";
			}

			if (LobbyMode == EPrivacyLevel::InviteOnly)
			{
				StrValue = "InviteOnly";
			}

			if (LobbyMode == EPrivacyLevel::Public)
			{
				StrValue = "Public";
			}

			UE_LOG_ONLINE_SESSION(Log, TEXT("Privacy Level: %s"), *StrValue);

			if (LobbyMode != SIFApplication::GetApplication()->GetLobbyPrivacy())
			{
				return -1;
			}
		}

		if (SettingName == SEARCH_NETWORK)
		{
			Setting.Data.GetValue(NetworkMode);

			UE_LOG_ONLINE_SESSION(Log, TEXT("Network Mode: %i"), NetworkMode);
		}

		if (SettingName == SEARCH_INPROGRESS)
		{
			Setting.Data.GetValue(SessionInProgress);

			FString StrValue = "No";

			if (SessionInProgress == 1)
			{
				StrValue = "Yes";
			}

			UE_LOG_ONLINE_SESSION(Log, TEXT("Session In Progress: %s "), *StrValue);
		}

		if (SettingName == SEARCH_OPEN_SLOTS)
		{
			Setting.Data.GetValue(OpenSlots);

			UE_LOG_ONLINE_SESSION(Log, TEXT("Open Slots: %i"), OpenSlots);
		}

		if (SettingName == SEARCH_NETWORK_BUILD_NUMBER)
		{
			Setting.Data.GetValue(BuildNumber);

			UE_LOG_ONLINE_SESSION(Log, TEXT("Build Number: %i"), BuildNumber);
		}
	}

	/*
	#if  UE_BUILD_SHIPPING
			if (BuildNumber != BUILD_NUMBER)
			{
				return -1;
			}
	#endif //  UE_BUILD_SHIPPING*/

	UE_LOG_ONLINE_SESSION(Log, TEXT("HandleFoundSession_Windows End"));

	if (OpenSlots == 0 && SessionInProgress == SEARCH_INPROGRESSS_VALUE)
	{
		UE_LOG_ONLINE_SESSION(Log, TEXT("OpenSlots == 0 && SessionInProgress == SEARCH_INPROGRESSS_VALUE"));
		return -1;
	}

	if (SessionInProgress == SEARCH_INPROGRESSS_VALUE)
	{
		UE_LOG_ONLINE_SESSION(Log, TEXT("SessionInProgress == SEARCH_INPROGRESSS_VALUE"));
		return -1;
	}

	if (OpenSlots == 0)
	{
		UE_LOG_ONLINE_SESSION(Log, TEXT("OpenSlots == 0"));
		return -1;
	}

	UE_LOG_ONLINE_SESSION(Log, TEXT("HandleFoundSession_Windows Valid Session Index"));

#endif // PLATFORM_WINDOWS
	return index;
}



// temp
void UMatchingManager::OnSearchSessionsComplete(bool bWasSuccessful)
{
	MDB(Display,"UMatchingManager::OnSearchSessionsComplete",0);
	ARugbyGameSession* const Session = m_pRugbyGameInstance->GetGameSession();
	if (Session)
	{
		Session->OnFindSessionsComplete().Remove(m_OnSearchSessionsCompleteDelegateHandle);

		int resultNum = -1;

		if (bWasSuccessful)
		{
			resultNum = Session->GetSearchResults().Num();
		}

		GEngine->AddOnScreenDebugMessage(-1, 10.f, FColor::Red, FString::Printf(TEXT("OnSearchSessionsComplete bSuccess: %d, resultNum: %i"), bWasSuccessful, resultNum));

		int joinIndex = -1;

		if (bWasSuccessful)
		{
			if (resultNum >= 1)
			{
#if PLATFORM_WINDOWS
				for (int i = 0; i < resultNum; i++)
				{
					FOnlineSessionSearchResult result = Session->GetSearchResults()[i];

					joinIndex = HandleFoundSession_Windows(result,i);
					if (joinIndex != -1)
					{
						break;
					}
#ifdef ENABLE_FAKE_INVITE
				//Create a fake invite instead of joining a session (steal session data and store it as a fake invite)
				FRugbyPendingInvite PendingInvite;
				PendingInvite.ControllerId = 0;
				PendingInvite.UserId = m_pRugbyGameInstance->GetFirstGamePlayer()->GetPreferredUniqueNetId().GetUniqueNetId();
				PendingInvite.InviteResult = result;
				PendingInvite.bPrivilegesCheckedAndAllowed = false;
				SIFApplication::GetApplication()->GetInviteManager()->FakeInvite = PendingInvite;

				//forcefully fail the joining by joining a crap session
				m_pRugbyGameInstance->JoinSession(m_pRugbyGameInstance->GetFirstGamePlayer(), -1);
				return;
#endif
			}
#else
				// TODO: We should get a best result, i.e. lowest ping, infrequently matched opponents, etc. For now just take the first one and join it.
				//For now we will ignore invalid networks
				for (int i = 0; i < resultNum; i++)
				{
					FOnlineSessionSearchResult result = Session->GetSearchResults()[i];

					if (TryedSessions.Num() == 0)
					{
						//XboxOne
#if PLATFORM_XBOXONE
						joinIndex = HandleFoundSession_XboxOne(result, i);
						if (joinIndex != -1)
						{
							break;
						}
#endif
						//PS4
#if PLATFORM_PS4
						joinIndex = HandleFoundSession_PS4(result, i);
						if (joinIndex != -1)
						{
							break;
						}
#endif

						//Others
#if PLATFORM_PS4 ==0 && PLATFORM_XBOXONE ==0

						int32 NetworkValue;
						result.Session.SessionSettings.Get(SEARCH_NETWORK, NetworkValue);
						if (NetworkValue == SEARCH_NETWORK_VALUE)
						{
							joinIndex = i;
							break;
						}
#endif
					}
					else
					{
						//This will makes sure we dont try to join the same session twice, this will get reset once we close the onlien menu
						for (size_t j = 0; j < TryedSessions.Num(); j++)
						{
							FString OldSessioNID = TryedSessions[j];

							if (OldSessioNID == result.Session.GetSessionIdStr())
							{
								continue;
							}
							else
							{
								//XboxOne
#if PLATFORM_XBOXONE
								joinIndex = HandleFoundSession_XboxOne(result, i);
								//We have a valid session
								if (joinIndex != -1)
								{
									break;
								}
#endif
								//PS4
#if PLATFORM_PS4
								joinIndex = HandleFoundSession_PS4(result, i);
								if (joinIndex != -1)
								{
									break;
								}
#endif
								//Others
#if PLATFORM_PS4 ==0 && PLATFORM_XBOXONE ==0
								int32 NetworkValue;
								result.Session.SessionSettings.Get(SEARCH_NETWORK, NetworkValue);
								if (NetworkValue == SEARCH_NETWORK_VALUE)
								{
									joinIndex = i;
									break;
								}
#endif
							}
						}
					}

				}
#endif
			}
		}
		GEngine->AddOnScreenDebugMessage(-1, 10.f, FColor::Red, FString::Printf(TEXT("Join index: %i"), joinIndex));

		// Attempt to join the index we picked.
		if (Session->GetSearchResults().IsValidIndex(joinIndex))
		{
#if PLATFORM_WINDOWS == 0
			TryedSessions.Add(Session->GetSearchResults()[joinIndex].Session.GetSessionIdStr());
#endif
			m_pRugbyGameInstance->JoinSession(m_pRugbyGameInstance->GetFirstGamePlayer(), joinIndex);
			return;
		}
		// We found no sessions, and since we're match making, we should attempt to host one instead.
		else
		{

			float timeInState = m_lastStateChange > 0 ? m_updateAccumulator - m_lastStateChange : 0.0f;
			searchingTime = m_updateAccumulator - m_searchStartTime;

			WaitAbitUntilTryAgain = true;
			CurrentWaitTime = 0.0f;
			//FMath::RandRange(MinMatchMakingTimer, MaxMatchMakingTimer);
			MDB(Display,"UMatchingManager::OnSearchSessionsComplete timeInState %f : %f/%f",timeInState,searchingTime,m_maxSearchTime);
			return;
		}
	}
	// if no choice for next have already been made, -> IDLE
	if (m_nextState == MS_Searching)
	{
		//SetNextState(MS_Init);
	}
}

// temp
void UMatchingManager::BeginMatchMaking()
{
	MDB(Display,"UMatchingManager::BeginMatchMaking",0);
	GEngine->AddOnScreenDebugMessage(-1, 10.f, FColor::Red, FString::Printf(TEXT("Beginning Matchmaking...")));	

	bool bUseSeperateReceiveThread = true;

	// When true, for BSD sockets the system will use a separate thread to receive data. 
	if (bUseSeperateReceiveThread)
	{
		IConsoleVariable* netReceiveCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("net.IpNetDriverUseReceiveThread"));
		if (netReceiveCVar)
		{
			netReceiveCVar->Set(1);
		}
	}

#if !UE_BUILD_SHIPPING
	// Auto start for hosts just go straight into hosting. Clients, and other logic will fall into finding a match instead.
	if (FParse::Param(FCommandLine::Get(), TEXT("LanAutoStart")) && FParse::Param(FCommandLine::Get(), TEXT("LanHost")))
	{
		SetNextState(MS_Hosting);
	}
	else
#endif
	{
		bool bIsLan = SIFApplication::GetApplication()->GetOnlineMode() == EOnlineMode::LAN;
		FindSessions(m_pRugbyGameInstance->GetFirstGamePlayer(), /*bIsDedicatedServer*/false, bIsLan);
	}
}

bool UMatchingManager::Start()
{
	bool ret = false;
	MDB(Display,"UMatchingManager::Start Privacy=%d Online=%d Type=%d Mode=%d State=%d Invitations=%d", SIFApplication::GetApplication()->GetLobbyPrivacy(), SIFApplication::GetApplication()->GetOnlineMode(),GetGametype(), SIFApplication::GetApplication()->GetMatchmakingMode(),m_state,m_acceptInvitations);
	
#if !PLATFORM_SWITCH
	//UWWUIScreenMainMenu::OnlineQuickMatchOptionOnClick
	//SIFGameHelpers::GASetGameType
	EOnlineMode onlineMode = SIFApplication::GetApplication()->GetOnlineMode();
#ifdef WITH_EDITOR
	bool LanGame = FParse::Param(FCommandLine::Get(), TEXT("LanGame"));
	if (LanGame && onlineMode != EOnlineMode::Offline)
	{
		onlineMode = EOnlineMode::LAN;
		SIFApplication::GetApplication()->SetOnlineMode(onlineMode);
		
	}
#endif
	// This will also be set when we host or join a game, for private matches that don't go through the matching manager.
	m_pRugbyGameInstance->GetMatchGameSettings()->game_settings.network_game = true;

	UE_LOG(LogTemp, Display, TEXT("UMatchingManager::Start Setting game settings network game state to true"));

	SIFApplication::GetApplication()->GetConnectionManager()->GotoState(ERugbyNetworkState::MatchMaking);
	CheckParamConsistency();

	//wwPlayerManager::Get().GetPlayerAnalyticsData().SetOnlineGameMode(GetMode());
	
	//m_pRugbyGameInstance->SetMatchmakingMode(InMatchmakingMode);
	//GEngine->OnNetworkFailure().AddUObject(GEngine, &UMatchingManager::HandleNetworkFailure);

	// from AFL
	/*if (!GEngine->OnNetworkFailure().IsBound())
	{
		GEngine->OnNetworkFailure().AddUObject(GEngine, &UEngine::HandleNetworkFailure);
	}*/

	//SIFMatchmakingHelpers::SearchForGame
	//SIFMatchmakingHelpers::StartGenericMatchmaking

	m_searchStartTime = m_updateAccumulator;
	SetNextState(MS_Searching);

	ret = true;
#endif
	return(ret);
}

void UMatchingManager::Cancel()
{
	MDB(Display,"UMatchingManager::CancelMatching",0);
	GEngine->AddOnScreenDebugMessage(-1, 10.f, FColor::Red, FString::Printf(TEXT("Matchmaking cancelled")));

	ARugbyGameSession* const GameSession = m_pRugbyGameInstance->GetGameSession();
	if (UOBJ_IS_VALID(GameSession))
	{
		UE_LOG(LogTemp, Warning, TEXT("AAflGameSession::CancelMatchmaking() Start Abort MatchMaking"));
		GameSession->StopMatchmaking();
	}
	Reset();
	SetNextState(MS_Init);
}

void UMatchingManager::JoinSession()
{
	MDB(Display,"UMatchingManager::JoinSession",0);
	//MABASSERTMSG(false,"not implemented");
	SetNextState(MS_Joining);
}

bool UMatchingManager::HostSession()
{
	MDB(Display,"UMatchingManager::HostSession",0);
	SetNextState(MS_Hosting);
	return(true);
}

void UMatchingManager::LeaveSession()
{
	MDB(Display,"UMatchingManager::LeaveSession",0);
	MABASSERTMSG(false,"not implemented");
	/*
	m_pRugbyGameInstance->GetVoiceManager()->SetRecording(false);
	m_pRugbyGameInstance->GetOnlineGameManager()->LeaveLobby();
	m_pRugbyGameInstance->GetNetworkManager()->LeaveGame();
	m_pRugbyGameInstance->GetApplicationParameters().disable_nclient_stuff = false;
	*/
	if (IsHost())
	{
		//
	}
}


void UMatchingManager::MaybeUpdateStateMachine()
{
	// we shouldn't need this but for LAN games some things call the callback immediately, triggering a state change
	// this should really happen in the Tick() because we are calling UI callbacks here, but...
	if (m_nextState != m_state)
	{
		StateTransition(m_nextState);
		StateTransition_UpdateState();
	}
}

void UMatchingManager::Update(float delta)
{
	m_updateAccumulator += delta;

	if (WaitAbitUntilTryAgain)
	{
		CurrentWaitTime += delta;

		if (CurrentWaitTime >= 1.0f)
		{
			WaitAbitUntilTryAgain = false;
			CurrentWaitTime = 0.0f;

			if (searchingTime > m_maxSearchTime)
			{
				SetNextState(MS_Hosting);
			}
			else
			{
				SetNextState(MS_Researching);
			}
		}
	}

	if (m_state != MS_Init && m_state != MS_Null)
	{
		if (SIFApplication::GetApplication()->GetOnlineMode() != EOnlineMode::Offline)
		{
			if (SIFApplication::GetApplication()->GetConnectionManager()->GetConnectionStatus() == EOnlineServerConnectionStatus::Type::NotConnected)
			{
				Reset();
				//SIFApplication::GetApplication()->FinishSessionCreation(EOnJoinSessionCompleteResult::CouldNotRetrieveAddress);
			}
		}
	}

	//FPlatformTime::Seconds()

	// Transition state
	StateTransition(m_nextState);

	// Update state
	StateTransition_UpdateState();
}

void UMatchingManager::SetNextState(EMatchmakingState ns)
{
	MDB(Display,"UMatchingManager::SetNextState %d -> %d (previously %d)",m_state,ns,m_nextState);
//	MABASSERTMSG(m_state == m_nextState,"UMatchingManager::SetNextState 2 state changes per frame");
	m_nextState = ns;
};

void UMatchingManager::StateTransition_BeginState()
{
	switch (m_state)
	{
	case MS_Null:
		break;
	case MS_Init:
		break;
	case MS_Searching:
		if (m_operationRequested == MS_Null)
			m_operationRequested = MS_Searching;
		BeginMatchMaking();
		eventMatchmakingStarted();
		break;
	case MS_Researching:
		// min delay is half max, max means MAX
		m_adjustSearchDelay = FMath::RandRange(0.0f,m_maxBetweenSearchTime / 2.0f);
		break;
	case MS_Joining:
		//if (m_operationRequested == MS_Null)
			m_operationRequested = MS_Joining;
		break;
	case MS_Hosting:
		//if (m_operationRequested == MS_Null)
			m_operationRequested = MS_Hosting;
		HostGame(m_pRugbyGameInstance->GetFirstGamePlayer());
		break;
	case MS_LoadingLobby:
		break;
	case MS_Connected:
		eventMatchmakingSuccess();
		eventMatchmakingSessionBegin();
		break;
	case MS_Error:
		eventMatchmakingFailure();
		break;
	default:
		break;
	}
}

void UMatchingManager::StateTransition_UpdateState()
{
	float timeInState = m_lastStateChange > 0 ? m_updateAccumulator - m_lastStateChange : 0.0f;
	switch (m_state)
	{
	case MS_Null:
		break;
	case MS_Init:
		break;
	case MS_Searching:
		//if (timeInState > m_maxSearchTime)
		//	SetNextState(MS_Hosting);
		break;
	case MS_Researching:
		MDB(Display,"UMatchingManager::StateTransition_UpdateState MS_Researching %f/(%f - %f)",timeInState,m_maxBetweenSearchTime,m_adjustSearchDelay);
		if (timeInState > (m_maxBetweenSearchTime - m_adjustSearchDelay))
		{
			SetNextState(MS_Searching);
		}
		break;
	case MS_Joining:
		break;
	case MS_Hosting:
		break;
	case MS_LoadingLobby:
		break;
	case MS_Connected:
		break;
	case MS_Error:
		break;
	default:
		break;
	}
}

void UMatchingManager::StateTransition_EndState()
{
	switch (m_state)
	{
	case MS_Null:
		break;
	case MS_Init:
		break;
	case MS_Searching:
		break;
	case MS_Researching:
		break;
	case MS_Joining:
		break;
	case MS_Hosting:
		break;
	case MS_LoadingLobby:
		break;
	case MS_Connected:
		eventMatchmakingSessionEnd();
		break;
	case MS_Error:
		Reset();
		break;
	default:
		break;
	}
}

bool UMatchingManager::StateTransition(EMatchmakingState nextState)
{
	//MDB(Display,"UMatchingManager::StateTransition %d -> %d",m_state,nextState);
	if (m_state == nextState)
	{
		return(false);
	}
	MDB(Display,"UMatchingManager::StateTransition %d -> %d",m_state,nextState);

	// Next state
	switch (nextState)
	{
		case MS_Null:
			break;
		case MS_Init:
			break;
		case MS_Searching:
			if (m_state == MS_Init)
				;
			else if (m_state == MS_Researching)
				;
			else if (m_state == MS_Null)
				;
			else
			{
				//ME("cannot transition from state %d -> MS_Searching",m_state);
				//return(false);
			}
			break;
		case MS_Researching:
			if (m_state != MS_Searching)
			{
				//ME("cannot transition from state %d -> MS_Researching",m_state);
				//return(false);
			}
			break;
		case MS_Joining:
			if (m_state == MS_Init)
				;
			else if (m_state == MS_Searching)
				;
			else
			{
				//ME("cannot transition from state %d -> MS_Joining",m_state);
				//return(false);
			}
			break;
		case MS_Hosting:
			if (m_state == MS_Init)
				;
			else if (m_state == MS_Searching)
				;
			else if (m_state == MS_Joining)
				;
			else if (m_state == MS_Null)
				;
	
			else
			{
				//ME("cannot transition from state %d -> MS_Hosting",m_state);
				//return(false);
			}
			break;
		case MS_LoadingLobby:
			if (m_state == MS_Joining)
				;
			else if (m_state == MS_Hosting)
				;
			else
			{
				//ME("cannot transition from state %d -> MS_LoadingLobby",m_state);
				//return(false);
			}
			break;
		case MS_Connected:
			if (m_state == MS_Joining)
				;
			else if (m_state == MS_Hosting)
				;
			else if (m_state == MS_LoadingLobby)
				;
			else
			{
				//ME("cannot transition from state %d -> MS_Connected",m_state);
				//return(false);
			}
			break;
		case MS_Error:
			break;
		default:
			break;
	}

	// End of state
	StateTransition_EndState();

	// change to next state
	m_state = nextState;
	m_lastStateChange = m_updateAccumulator;

	// Beginning of state
	StateTransition_BeginState();
	return(true);
}



void UMatchingManager::SetupQuickmatch15s()
{
	SIFApplication::GetApplication()->SetMatchmakingMode(EMatchmakingMode::Unranked);
	SIFApplication::GetApplication()->SetLobbyPrivacy(EPrivacyLevel::Public);
	//SIFApplication::GetApplication()->SetOnlineMode(EOnlineMode::Online);

	SetGametype(GAME_MODE::GAME_MODE_RU13);
	SetAcceptInvitations(true);
};

void UMatchingManager::SetupQuickmatch7s()
{
	SIFApplication::GetApplication()->SetMatchmakingMode(EMatchmakingMode::Unranked);
	SIFApplication::GetApplication()->SetLobbyPrivacy(EPrivacyLevel::Public);
	//SIFApplication::GetApplication()->SetOnlineMode(EOnlineMode::Online);
	SetGametype(GAME_MODE::GAME_MODE_RU13W); // Nick  WWS 7s to Womens //
	SetAcceptInvitations(true);
};

void UMatchingManager::SetupPrivate15s()
{
	SIFApplication::GetApplication()->SetMatchmakingMode(EMatchmakingMode::Unranked);
	SIFApplication::GetApplication()->SetLobbyPrivacy(EPrivacyLevel::InviteOnly);
	//SIFApplication::GetApplication()->SetOnlineMode(EOnlineMode::Online);
	SetGametype(GAME_MODE::GAME_MODE_RU13);
	SetAcceptInvitations(true);
};

void UMatchingManager::SetupPrivate7s()
{
	SIFApplication::GetApplication()->SetMatchmakingMode(EMatchmakingMode::Unranked);
	SIFApplication::GetApplication()->SetLobbyPrivacy(EPrivacyLevel::InviteOnly);
	//SIFApplication::GetApplication()->SetOnlineMode(EOnlineMode::Online);

	SetGametype(GAME_MODE::GAME_MODE_RU13W);// Nick  WWS 7s to Womens //
	SetAcceptInvitations(true);
};

void UMatchingManager::SetupRanked15s()
{
	SIFApplication::GetApplication()->SetMatchmakingMode(EMatchmakingMode::Ranked);
	SIFApplication::GetApplication()->SetLobbyPrivacy(EPrivacyLevel::Public);
	//SIFApplication::GetApplication()->SetOnlineMode(EOnlineMode::Online);
	SetGametype(GAME_MODE::GAME_MODE_RU13);
	SetAcceptInvitations(false);
};

void UMatchingManager::SetupRanked7s()
{
	SIFApplication::GetApplication()->SetMatchmakingMode(EMatchmakingMode::Ranked);
	SIFApplication::GetApplication()->SetLobbyPrivacy(EPrivacyLevel::Public);
	//SIFApplication::GetApplication()->SetOnlineMode(EOnlineMode::Online);

	SetGametype(GAME_MODE::GAME_MODE_RU13W); // Nick  WWS 7s to Womens //
	SetAcceptInvitations(false);
};

bool UMatchingManager::IsQuickmatch()
{
	return(SIFApplication::GetApplication()->GetLobbyPrivacy() == EPrivacyLevel::Public);
};


bool UMatchingManager::IsHost()
{
	if (GetWorld() && GetWorld()->GetNetMode() == ENetMode::NM_Client)
	{
		return false;
	}

	return true;
}

void UMatchingManager::SetToConnected()
{
	// wish we could test WHAT has been loaded here
	if (m_state == MS_Joining || m_state == MS_Hosting || m_state == MS_LoadingLobby)
	{
		MDB(Display,"UMatchingManager::OnPostLoadMap",0);
		SetNextState(MS_Connected);
	}
}

void UMatchingManager::Reset()
{
	MDB(Display,"UMatchingManager::Reset",0);
	SIFApplication::GetApplication()->SetLobbyPrivacy(EPrivacyLevel::Public);
	SIFApplication::GetApplication()->SetOnlineMode(EOnlineMode::Offline);
	SetGametype(GAME_MODE::GAME_MODE_RU13);
	SIFApplication::GetApplication()->SetMatchmakingMode(EMatchmakingMode::Unranked);
	m_operationRequested = MS_Null;
	m_acceptInvitations = true;
	m_updateAccumulator = 0.0f;
	m_searchStartTime = 0.0f;
	m_state = MS_Null;
}

void UMatchingManager::ClearError()
{
	MDB(Display,"UMatchingManager::ClearError",0);
	SetNextState(MS_Init);
}



//