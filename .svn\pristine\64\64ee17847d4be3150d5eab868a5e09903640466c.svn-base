/*--------------------------------------------------------------
|        Copyright (C) 1997-2008 by Prodigy Design Ltd         |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "RUCommentaryDataCollector.h"

#include <Rugby\Mab\Lua\MabLuaInterpreter.h>
#include "Mab/Central/OLD/MabCentralAccessor.h"


#include "Match/Ball/SSBall.h"
#include "Match/SSSpatialHelper.h"

#include "Match/RugbyUnion/ContextBucket/RUContextBucketBasicEvents.h"
#include "RUCommentaryDataProvider.h"
#include "RUPhraseBinder.h"
#include "RUCommentaryMatchTrendAnalysis.h"
#include "RUCommentaryCompetitionAnalysis.h"
#include "Match/RugbyUnion/RUEmotionEngineManager.h"
#include "Utility/RURandomNumberGenerator.h"

#include "Match/RugbyUnion/Statistics/RUStatisticsSystem.h"
#include "Match/RugbyUnion/Statistics/RUStatsTracker.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhasePenaltyGoal.h"
#include "Match/RugbyUnion/CompetitionMode/OLD_RU/RUCompetitionModeManager.h"
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "RUCommentaryPlayer.h"
#include "Match/RugbyUnion/RUStadiumManager.h"

#include "Match/RugbyUnion/RUDatabaseConstants.h"
#include "RugbyGameInstance.h"
#include "Mab/Mem/MabMemTypes.h"
#include "RUCommentaryDataSources_echar.h"


static const float UPDATE_PERIODIC_STATS_PERIOD = 10.0f;

template <typename StatType>
static StatType GetBestPlayerAt(RUStatisticsSystem *statistics_system, RUTeam *team, StatType RUDB_STATS_PLAYER::*member, unsigned int *player_id_out);



RUCommentaryDataCollector::RUCommentaryDataCollector(
	MABMEM_HEAP heap,
	RUCommentaryDataProvider *new_data_provider,
	MabLuaInterpreter *new_interpreter,
	RUPhraseBinder *new_phrase_binder)
:
	commentary_data_provider(new_data_provider),
	commentary_interpreter(new_interpreter),
	phrase_binder(new_phrase_binder),
	match_trend_analysis(NULL),
	competition_analysis(NULL),
	game(NULL),
	context_bucket(NULL),
	update_periodic_stats_timer(),
	favoured_team(NULL),
	predicted_winner(NULL),
	power_difference(0.0f),
	best_try_scorer_id(0),
	best_line_breaker_id(0),
	best_tackler_id(0),
	best_goal_scorer_id(0),
	best_points_scorer_id(0)
{
	MABASSERT(commentary_data_provider);
	MABASSERT(commentary_interpreter);
	MABASSERT(phrase_binder);

	match_trend_analysis = MabMemNew(heap) RUCommentaryMatchTrendAnalysis();
	competition_analysis = MabMemNew(heap) RUCommentaryCompetitionAnalysis();

	//setup some test values and constants on the lua interpreter
	commentary_interpreter->SetGlobalVariable("EXCITEMENT", 1);

	for(unsigned int i = 0; i < CPT_NUM_PASS_TYPES; i++)
	{
		commentary_interpreter->SetGlobalVariable(RUCommentaryPassType_STRINGS[i], (int)i);
	}

	for(unsigned int i = 0; i < PL_NUM_PASS_LENGTHS; i++)
	{
		commentary_interpreter->SetGlobalVariable(RUCommentaryPassLength_STRINGS[i], (int)i);
	}
}

RUCommentaryDataCollector::~RUCommentaryDataCollector()
{
	MabMemDeleteSafe(competition_analysis);
	MabMemDeleteSafe(match_trend_analysis);
}

void RUCommentaryDataCollector::SetWorld(SIFGameWorld *new_game)
{
	context_bucket = NULL;
	
	game = new_game;

	if(game!=NULL && game->GetEmotionEngineManager())
		context_bucket = game->GetEmotionEngineManager()->GetContextBucket();

	match_trend_analysis->SetWorld(new_game);
	competition_analysis->SetWorld(new_game);

	if(game)
	{
		update_periodic_stats_timer.Initialise(game->GetSimTime(),UPDATE_PERIODIC_STATS_PERIOD, false);
	}
	else
	{
		update_periodic_stats_timer.SetEnabled(false);
	}
}

void RUCommentaryDataCollector::Reset()
{
	competition_analysis->Reset();
	match_trend_analysis->Reset();

	favoured_team = NULL;
}

void RUCommentaryDataCollector::Update( float /*delta_time*/ )
{
	if(update_periodic_stats_timer.GetNumTimerEventsRaised() > 0)
	{
		CollectPeriodicStats();
		update_periodic_stats_timer.AcknowledgeAll();
	}
}

int CalculateErrorRate( int error_count )
{
	int game_length = SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.game_length;  //#rc3_legacy GetGameSettings()
	int actual_game_length = SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetActualGameLength();//#rc3_legacy GetGameSettings()
	int game_length_stat_multiplier = actual_game_length/*ACTUAL_GAME_LENGTH*/ / game_length;

	float error_rate = (float)(error_count * game_length_stat_multiplier);

	switch( game_length )
	{
	case 20:
		error_rate /= 0.9f;
		break;

	case 40:
		error_rate /= 0.7f;
		break;

	case 80:
		error_rate /= 0.45f;
		break;
	};

	return (int)error_rate;
}


/// Extracts what ever information we can from the given event.
void RUCommentaryDataCollector::CollectFromEvent(SSContextBucketEvent *current_event)
{
	MABASSERT(game);

	RUStatisticsSystem *statistics_system = SIFApplication::GetApplication()->GetStatisticsSystem();

	//test grabbing the field position_z if it exists
	float position_z = game->GetBall() ? game->GetBall()->GetCurrentPosition().z : 0.0f;
	float position_x = game->GetBall() ? game->GetBall()->GetCurrentPosition().x : 0.0f;

	MabCentralAccessor event_accessor(current_event);
	float *event_position_z = event_accessor.Get<float>( "position_z" );
	if( event_position_z )
		position_z = *event_position_z;

	float *event_position_x = event_accessor.Get<float>( "position_x" );
	if( event_position_x )
		position_x = *event_position_x;

	//determine the attack direction;
	ERugbyPlayDirection offense_play_direction = game->GetGameState()->GetAttackingTeam()->GetPlayDirection();

	//reverse it if we're going the other way
	float offence_relative_position = position_z * offense_play_direction;

	//offset it from our own goal line.
	offence_relative_position += FIELD_LENGTH * 0.5f;	

	commentary_data_provider->SetDataSource(DS_FIELD_POSITION_OFFENSE, (int)offence_relative_position);
	commentary_interpreter->SetGlobalVariable("FIELD_POSITION_OFFENSE", (int)offence_relative_position);

	commentary_data_provider->SetDataSource(DS_FIELD_POSITION_X_ABSOLUTE, (int)MabMath::Fabs(position_x));
	

	RUTeam *winning_team = game->GetTeam(SIDE_A);
	RUTeam *losing_team = game->GetTeam(SIDE_B);

	unsigned int team_a_score = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( winning_team, &RUDB_STATS_TEAM::score );
	unsigned int team_b_score = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( losing_team, &RUDB_STATS_TEAM::score );

	if(team_b_score > team_a_score)
	{
		std::swap(winning_team, losing_team);
	}

	commentary_data_provider->SetDataSource(DS_P1_IS_LOMU, (int) false);
	commentary_interpreter->SetGlobalVariable("P1_NAME", "");
	commentary_interpreter->SetGlobalVariable("P1_IS_CUSTOM", false);


	//see if we can get the field position_z for the events player
	int *event_player_id = event_accessor.Get<int>( "player_index" );
	if(event_player_id)
	{
		unsigned short player_id = (unsigned short)(*event_player_id);

		RUCommentaryPlayer event_player(game, *event_player_id);
		if(event_player.player && event_player.team)
		{
			FVector player_position = event_player.player->GetMabPosition();
			ERugbyPlayDirection play_direction = event_player.team->GetPlayDirection();

			//reverse it if we're going the other way
			float events_player_relative_position = player_position.z * play_direction;

			//offset it from our own goal line.
			events_player_relative_position += FIELD_LENGTH * 0.5f;

			commentary_data_provider->SetDataSource(DS_FIELD_POSITION_PLAYER, (int)events_player_relative_position);

		}

		//#rc4_no_lomu 
		/*if(event_player.db_player 
			&& strcmp(event_player.db_player->GetFirstName(), "Jonah") == 0
			&& strcmp(event_player.db_player->GetLastName(), "Lomu") == 0)
		{
			commentary_data_provider->SetDataSource(DS_P1_IS_LOMU, (int) true);
		}*/

		if(event_player.db_player)
		{
			MabString combined_name;
			event_player.db_player->GetCombinedName(combined_name);

			commentary_interpreter->SetGlobalVariable("P1_NAME", combined_name.c_str());
			commentary_interpreter->SetGlobalVariable("P1_IS_CUSTOM", event_player.db_player->custom);
		}

		if(event_player.db_player && event_player.team)
		{
			SSTEAMSIDE players_side = event_player.team->GetSide();

			commentary_data_provider->SetDataSource(DS_PLAYER_AGILITY, event_player.db_player->agility );
			commentary_data_provider->SetDataSource(DS_PLAYER_ACCELERATION, event_player.db_player->acceleration );
			commentary_data_provider->SetDataSource(DS_PLAYER_GENERAL_KICK_ACCURACY, event_player.db_player->general_kick_accuracy);
			commentary_data_provider->SetDataSource(DS_PLAYER_GOAL_KICK_ACCURACY, event_player.db_player->goal_kick_accuracy);
			commentary_data_provider->SetDataSource(DS_PLAYER_HEIGHT, event_player.db_player->height );
			commentary_data_provider->SetDataSource(DS_PLAYER_PASS_ACCURACY, event_player.db_player->pass_accuracy );
			commentary_data_provider->SetDataSource(DS_PLAYER_SPEED, event_player.db_player->speed);
			commentary_data_provider->SetDataSource(DS_PLAYER_STRENGTH, event_player.db_player->strength );
			commentary_data_provider->SetDataSource(DS_PLAYER_TACKLE_ABILITY, event_player.db_player->tackle_ability );
			//#rc3_legacy_comm commentary_data_provider->SetDataSource(DS_PLAYER_IS_STAR, event_player.db_player->head != 0 ? 1 : 0 );
			
			commentary_data_provider->SetDataSource(DS_PLAYER_YELLOW_CARDS, statistics_system->GetCurrentMatchStat( players_side, player_id, &RUDB_STATS_PLAYER::yellow_cards ));
		}

		if(event_player.team)
		{
			int goal_attempts = 
				statistics_system->GetCurrentMatchStat(event_player.team->GetSide(), player_id, &RUDB_STATS_PLAYER::conversion_attempts)
				+ statistics_system->GetCurrentMatchStat(event_player.team->GetSide(), player_id, &RUDB_STATS_PLAYER::field_goal_attempts)
				+ statistics_system->GetCurrentMatchStat(event_player.team->GetSide(), player_id, &RUDB_STATS_PLAYER::penalty_goal_attempts);

			int goal_successes = 
				statistics_system->GetCurrentMatchStat(event_player.team->GetSide(), player_id, &RUDB_STATS_PLAYER::successful_conversion_attempts)
				+ statistics_system->GetCurrentMatchStat(event_player.team->GetSide(), player_id, &RUDB_STATS_PLAYER::successful_field_goals)
				+ statistics_system->GetCurrentMatchStat(event_player.team->GetSide(), player_id, &RUDB_STATS_PLAYER::successful_penalty_goals);

			commentary_data_provider->SetDataSource(DS_PLAYER_GOAL_ATTEMPTS, goal_attempts );
			commentary_data_provider->SetDataSource(DS_PLAYER_GOAL_SUCCESSES, goal_successes );
		}
	}

	//setup the position_z relative to the point of view of "team_index" or "player_index"
	MabStringList::StringList tokens;
	tokens.push_back("team_index");
	RUTeam *events_team = phrase_binder->SourceTeam(tokens.begin(), tokens, current_event);
	if(events_team)
	{
		ERugbyPlayDirection play_direction = events_team->GetPlayDirection();

		//reverse it if we're going the other way
		float events_team_relative_position = position_z * play_direction;

		//offset it from our own goal line.
		events_team_relative_position += FIELD_LENGTH * 0.5f;	

		commentary_data_provider->SetDataSource(DS_FIELD_POSITION, (int)events_team_relative_position);
		commentary_interpreter->SetGlobalVariable("FIELD_POSITION", (int)events_team_relative_position);

		commentary_data_provider->SetDataSource(DS_T1_IS_NZ, (int)(events_team->GetDbTeam().GetDbId() == DB_TEAMID_NZ));
	}
	else
	{
		commentary_data_provider->SetDataSource(DS_FIELD_POSITION, (int)offence_relative_position);
		commentary_interpreter->SetGlobalVariable("FIELD_POSITION", (int)offence_relative_position);

		commentary_data_provider->SetDataSource(DS_T1_IS_NZ, (int)false);
	}



	float ball_location_x = game->GetBall()->GetCurrentPosition().x;
	float distance_from_touch = (FIELD_WIDTH * 0.5f) - MabMath::Fabs( ball_location_x );
	commentary_data_provider->SetDataSource(DS_DISTANCE_FROM_TOUCH, (int) distance_from_touch);

	
	
	bool is_second_half = game->GetGameTimer()->GetHalf() == SECOND_HALF;

	//setup some game time related data sources.
	commentary_data_provider->SetDataSource(DS_SECOND_HALF, (int)is_second_half);

	commentary_data_provider->SetDataSource(DS_ELAPSED, game->GetGameTimer()->GetScaledMinutesElapsed());
	commentary_interpreter->SetGlobalVariable("ELAPSED", game->GetGameTimer()->GetScaledMinutesElapsed());

	commentary_data_provider->SetDataSource(DS_REMAINING, game->GetGameTimer()->GetScaledMinutesRemaining());
	commentary_interpreter->SetGlobalVariable("REMAINING", game->GetGameTimer()->GetScaledMinutesRemaining());

	commentary_data_provider->SetDataSource(DS_ELAPSED_HALF, game->GetGameTimer()->GetScaledMinutesElapsedHalf());
	commentary_data_provider->SetDataSource(DS_REMAINING_HALF, game->GetGameTimer()->GetScaledMinutesRemainingHalf());

	commentary_data_provider->SetDataSource(DS_GAME_PHASE, game->GetGameState()->GetPhase());


	commentary_data_provider->SetDataSource(DS_STANDARD_PLAY, game->GetGameState()->IsGameInStandardPlay() );

	///true if we are in standard play, amnd
	bool play_paused_ball_in_hand = false;
	if(game->GetGameState()->GetPhase() == RUCK
		|| game->GetGameState()->GetPhase() == LINEOUT
		|| game->GetGameState()->GetPhase() == SCRUM
		|| game->GetGameState()->GetPhase() == PENALTY_TAP_RESTART
		|| game->GetGameState()->GetPhase() == QUICK_TAP_PENALTY )
	{
		play_paused_ball_in_hand = true;
	}

	commentary_data_provider->SetDataSource(DS_PLAY_PAUSED_BALL_IN_HAND, play_paused_ball_in_hand);

	RUCommentaryPeriod current_period = CP_NO_PERIOD;

	switch(game->GetGameState()->GetPhase())
	{
	case NONE: break;
	case PRE_GAME: current_period = CP_PRE_GAME; break;
	case HALF_TIME: current_period = CP_HALF_TIME; break;
	case POST_GAME: current_period = CP_POST_GAME; break;
	default:
		{
			//Glen: For some reason extra time has been set in the first half after the hooter
			//has sounded.

			//this must be a play phase of some sort, see if we are in the first or second half
			//of if we are in some sort of extra time.
			EXTRATIME extra_time_mode = game->GetGameTimer()->GetExtraTimeMode();

			if(extra_time_mode == FIRST_EXTRA_TIME)
			{
				current_period = CP_EXTRA_TIME_FIRST;
			}
			else if(extra_time_mode == SECOND_EXTRA_TIME)
			{
				current_period = CP_EXTRA_TIME_SECOND;
			}
			else if(extra_time_mode == GOLDEN_POINT)
			{
				current_period = CP_GOLDEN_POINT;
			}
			else if(extra_time_mode == NOT_EXTRA_TIME)
			{
				//determine if we are in the first or second half
				if(game->GetGameTimer()->GetHalf() == FIRST_HALF)
				{
					current_period = CP_FIRST_HALF;
				}
				else
				{
					current_period = CP_SECOND_HALF;
				}
			}
		}
	};

	commentary_data_provider->SetDataSource(DS_CURRENT_PERIOD, current_period);

	unsigned int num_phases = game->GetStatsTracker()->GetNumberOfPhases();
	commentary_data_provider->SetDataSource(DS_NUM_PHASES, num_phases);


	unsigned int attacking_score = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( game->GetGameState()->GetAttackingTeam(), &RUDB_STATS_TEAM::score );
	unsigned int defending_score = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( game->GetGameState()->GetDefendingTeam(), &RUDB_STATS_TEAM::score );
	int offensive_defensive_difference = ( (int)attacking_score ) - ( (int)defending_score );
	commentary_data_provider->SetDataSource(DS_OFFENSIVE_SCORE, (int)attacking_score);
	commentary_data_provider->SetDataSource(DS_DEFENSIVE_SCORE, (int)defending_score);
	commentary_data_provider->SetDataSource(DS_OFFENSIVE_DEFENSIVE_SCORE_DIFFERENCE, offensive_defensive_difference );
	commentary_data_provider->SetDataSource(DS_SCORE_DIFFERENCE, MabMath::Abs(offensive_defensive_difference) );


	unsigned int home_score = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( game->GetTeam( SIDE_A ), &RUDB_STATS_TEAM::score );
	unsigned int away_score = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( game->GetTeam( SIDE_B ), &RUDB_STATS_TEAM::score );
	commentary_data_provider->SetDataSource(DS_HOME_TEAM_RELATIVE_SCORE, home_score - away_score );

	commentary_interpreter->SetGlobalVariable("OFFENSIVE_SCORE", (int)attacking_score);
	commentary_interpreter->SetGlobalVariable("DEFENSIVE_SCORE", (int)defending_score);
	commentary_interpreter->SetGlobalVariable("OFFENSIVE_DEFENSIVE_SCORE_DIFFERENCE", offensive_defensive_difference);


	float offensive_fatigue = match_trend_analysis->GetTeamsPerspective( game->GetGameState()->GetAttackingTeam()->GetSide()).average_of_most_fatigued_players;
	float defensive_fatigue = match_trend_analysis->GetTeamsPerspective( game->GetGameState()->GetDefendingTeam()->GetSide()).average_of_most_fatigued_players;

	commentary_data_provider->SetDataSource(DS_OFFENSIVE_FATIGUE, (int)(offensive_fatigue * 10.0f));
	commentary_data_provider->SetDataSource(DS_DEFENSIVE_FATIGUE, (int)(defensive_fatigue * 10.0f));

	//setup the score difference from the event teams perspective
	if(events_team)
	{
		RUStatsTracker *stats_tracker = game->GetStatsTracker();
		//RUStatisticsSystem *statistics_system = SIFApplication::GetApplication()->GetStatisticsSystem(); // JAMES O, shadow variable, exact same line as declared above

		RUTeam *other_team = (RUTeam *)events_team->GetOppositionTeam();
		unsigned int events_teams_score = statistics_system->GetCurrentMatchStat( events_team, &RUDB_STATS_TEAM::score );
		unsigned int other_teams_score = statistics_system->GetCurrentMatchStat( other_team, &RUDB_STATS_TEAM::score );

		commentary_data_provider->SetDataSource(DS_T1_SCORE, (int)events_teams_score);
		commentary_data_provider->SetDataSource(DS_T2_SCORE, (int)other_teams_score);
		commentary_data_provider->SetDataSource(DS_T1_SCORE_DIFFERENCE, ( (int)events_teams_score ) - ( (int)other_teams_score ) );


		//set the penalty count.
		unsigned int team1_penalty_count = statistics_system->GetCurrentMatchStat(events_team, &RUDB_STATS_PLAYER::penalties_against );
		commentary_data_provider->SetDataSource(DS_T1_PENALTY_COUNT, (int)team1_penalty_count);
		int actual_game_length = SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetActualGameLength();
		int penalty_rate = team1_penalty_count * /*ACTUAL_GAME_LENGTH*/actual_game_length / (game->GetGameTimer()->GetScaledMinutesElapsed() + 1);
		commentary_data_provider->SetDataSource(DS_T1_PENALTY_RATE, (int)penalty_rate);


		RUStatsTracker::StatsTeam &team1_stats = stats_tracker->GetTeamStats( events_team->GetSide());
		RUStatsTracker::StatsTeam &team2_stats = stats_tracker->GetTeamStats( other_team->GetSide());

		commentary_data_provider->SetDataSource(DS_T1_LINEOUT_TURNOVERS, (int)team1_stats.lineout_turnovers);
		commentary_data_provider->SetDataSource(DS_T1_LINEOUT_RETENTIONS, (int)team1_stats.lineout_retentions);

		commentary_data_provider->SetDataSource(DS_T1_TURNOVERS, (int)team1_stats.turnovers);
		commentary_data_provider->SetDataSource(DS_T2_TURNOVERS, (int)team2_stats.turnovers);

		int turnover_rate_t1 = CalculateErrorRate(team1_stats.turnovers);
		int turnover_rate_t2 = CalculateErrorRate(team2_stats.turnovers);

		commentary_data_provider->SetDataSource(DS_T1_TURNOVER_RATE, turnover_rate_t1);
		commentary_data_provider->SetDataSource(DS_T2_TURNOVER_RATE, turnover_rate_t2);

		float team_territory = stats_tracker->GetTerritory(events_team->GetSide())
			+ statistics_system->GetCurrentMatchStat(events_team, &RUDB_STATS_TEAM::territory);

		float opposing_territory = stats_tracker->GetTerritory(other_team->GetSide())
			+ statistics_system->GetCurrentMatchStat(other_team, &RUDB_STATS_TEAM::territory);

		float team_territory_proportion = team_territory;
		float total_territory = team_territory + opposing_territory;
		if(total_territory > 0.0f)
		{
			team_territory_proportion /= total_territory;
		}

		commentary_data_provider->SetDataSource(DS_TEAM_TERRITORY, (int)(team_territory_proportion * 100.0f));


		float team_possession = game->GetStatsTracker()->GetBallPossession(events_team->GetSide())
			+ statistics_system->GetCurrentMatchStat(events_team, &RUDB_STATS_TEAM::possession);

		float opposing_possession = game->GetStatsTracker()->GetBallPossession( other_team->GetSide())
			+ statistics_system->GetCurrentMatchStat(other_team, &RUDB_STATS_TEAM::possession);

		float team_possession_proportion = team_possession;
		float total_possession = team_possession + opposing_possession;
		if(total_possession > 0.0f)
		{
			team_possession_proportion /= total_possession;
		}

		commentary_data_provider->SetDataSource(DS_TEAM_POSSESSION, (int)(team_possession_proportion * 100.0f));
	}
	else
	{
		//otherwise default it to the offensive teams perspective
		commentary_data_provider->SetDataSource(DS_T1_SCORE, (int)attacking_score);
		commentary_data_provider->SetDataSource(DS_T2_SCORE, (int)defending_score);
		commentary_data_provider->SetDataSource(DS_T1_SCORE_DIFFERENCE, offensive_defensive_difference );
	}

	//calculate the score difference from the human team perspective
	if(favoured_team)
	{
		RUTeam *other_team = (RUTeam *)favoured_team->GetOppositionTeam();

		//additionally setup the score difference between the favoured team and the other team.
		unsigned int favoured_score = statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_TEAM::score );
		unsigned int opposing_score = statistics_system->GetCurrentMatchStat( other_team, &RUDB_STATS_TEAM::score );
		int score_difference = ( (int)favoured_score ) - ( (int)opposing_score );
		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_SCORE_DIFFERENCE, score_difference );
	}

	commentary_data_provider->SetDataSource(DS_LEAD_CHANGE_COUNT, match_trend_analysis->GetLeadChangeCount() );

	commentary_data_provider->SetDataSource(DS_WINNING_TEAM_PEAK_SCORE_LEAD, match_trend_analysis->GetTeamsPerspective(winning_team->GetSide()).max_lead);
	commentary_data_provider->SetDataSource(DS_WINNING_TEAM_PEAK_SCORE_LAG, match_trend_analysis->GetTeamsPerspective(winning_team->GetSide()).max_lag);

	commentary_data_provider->SetDataSource(DS_LOSING_TEAM_PEAK_SCORE_LEAD, match_trend_analysis->GetTeamsPerspective(losing_team->GetSide()).max_lead);
	commentary_data_provider->SetDataSource(DS_LOSING_TEAM_PEAK_SCORE_LAG, match_trend_analysis->GetTeamsPerspective(losing_team->GetSide()).max_lag);


	int random = (int)game->GetRNG()->GetSeed() % 10;
	commentary_data_provider->SetDataSource(DS_RANDOM, random );


	//Setup the play info data sources.
	const PlayInfo &play_info = game->GetStrategyHelper()->GetPlayInfo();
	const PlayInfo &previous_play_info = game->GetStrategyHelper()->GetPreviousPlayInfo();

	commentary_data_provider->SetDataSource(DS_PLAY_METERS_GAINED, (int)play_info.metres_gained);
	commentary_data_provider->SetDataSource(DS_PLAY_METERS_GAINED_PLAYER, (int)play_info.bh_metres_gained);
	commentary_data_provider->SetDataSource(DS_PLAY_PASSES, (int)play_info.n_passes);
	commentary_data_provider->SetDataSource(DS_PLAY_OFFLOADS, (int)play_info.n_offloads);
	commentary_data_provider->SetDataSource(DS_PLAY_PASSES_AND_OFFLOADS, play_info.n_passes + play_info.n_offloads);
	
	commentary_data_provider->SetDataSource(DS_LAST_PLAY_METERS_GAINED, (int)previous_play_info.metres_gained);
	commentary_data_provider->SetDataSource(DS_LAST_PLAY_METERS_GAINED_PLAYER, (int)previous_play_info.bh_metres_gained);
	commentary_data_provider->SetDataSource(DS_LAST_PLAY_PASSES, (int)previous_play_info.n_passes);
	commentary_data_provider->SetDataSource(DS_LAST_PLAY_OFFLOADS, (int)previous_play_info.n_offloads);
	commentary_data_provider->SetDataSource(DS_LAST_PLAY_PASSES_AND_OFFLOADS, previous_play_info.n_passes + previous_play_info.n_offloads);

	commentary_data_provider->SetDataSource(DS_BROKEN_DEFENSIVE_LINE, (int)play_info.bh_broken_defensive_line);
	commentary_data_provider->SetDataSource(DS_CLEAR_SPACE_AHEAD, (int)play_info.bh_clear_space_ahead);
	commentary_data_provider->SetDataSource(DS_IN_CLEAR, (int)play_info.bh_in_clear);
	commentary_data_provider->SetDataSource(DS_LIKELY_TO_SCORE, (int)play_info.bh_likely_to_score);
	commentary_data_provider->SetDataSource(DS_VERY_LONG_RUN, (int)play_info.bh_notified_very_long);
	commentary_data_provider->SetDataSource(DS_LONG_RUN, (int)play_info.bh_notified_long);
	commentary_data_provider->SetDataSource(DS_MEDIUM_RUN, (int)play_info.bh_notified_med);
	commentary_data_provider->SetDataSource(DS_RUN_LENGTH, (int)play_info.bh_metres_gained);
	commentary_data_provider->SetDataSource(DS_SURROUNDED, (int)play_info.bh_surrounded);
	commentary_data_provider->SetDataSource(DS_SUPPORTED, (int)play_info.bh_supported);
	commentary_data_provider->SetDataSource(DS_SUPPORT_DISTANCE, (int)play_info.bh_supporting_player_distance);

	static const int MAX_INTERCEPT_VALUE = 1000;

	commentary_data_provider->SetDataSource(DS_OPP_INTERCEPT_TIME,
		play_info.bh_lowest_closing_opp_time > (float)MAX_INTERCEPT_VALUE ? MAX_INTERCEPT_VALUE : (int)play_info.bh_lowest_closing_opp_time);

	commentary_data_provider->SetDataSource(DS_OPP_INTERCEPT_DISTANCE,
		play_info.bh_lowest_closing_opp_time_dist > (float)MAX_INTERCEPT_VALUE ? MAX_INTERCEPT_VALUE : (int)play_info.bh_lowest_closing_opp_time_dist);
	
	commentary_data_provider->SetDataSource(DS_OPP_DISTANCE,
		play_info.bh_lowest_opp_dist > (float)MAX_INTERCEPT_VALUE ? MAX_INTERCEPT_VALUE : (int)play_info.bh_lowest_opp_dist);

	commentary_data_provider->SetDataSource(DS_BALL_COLLECT_TYPE, game->GetStrategyHelper()->GetLastBallCollectInfo().event);
	commentary_data_provider->SetDataSource(DS_BALL_PICKUP_TYPE, game->GetStrategyHelper()->GetLastBallCollectInfo().pick_up_type);
	commentary_data_provider->SetDataSource(DS_BALL_COLLECT_ELAPSED, (int)game->GetStrategyHelper()->GetLastBallCollectInfo().TimeSince());

	

	//setup the total try count.
	int try_count = 0;
	try_count += SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( game->GetTeam(SIDE_A), &RUDB_STATS_PLAYER::tries_scored );
	try_count += SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( game->GetTeam(SIDE_B), &RUDB_STATS_PLAYER::tries_scored );
	commentary_data_provider->SetDataSource(DS_TRY_COUNT, try_count);

	commentary_data_provider->SetDataSource(DS_BALL_OUT_ENDS_HALF,
		game->GetGameTimer()->GetTimeRemaining() <= 0.0f
		&& game->GetGameTimer()->GetExtraTimeMode() != GOLDEN_POINT);

	commentary_data_provider->SetDataSource(DS_BALL_OUT_ENDS_GAME, 
		game->GetGameTimer()->GetTimeRemaining() <= 0.0f
		&& game->GetGameTimer()->GetHalf() == SECOND_HALF
		&& (team_a_score != team_b_score || !game->GetGameSettings().game_settings.extra_time_enabled)
		);

	competition_analysis->AnalyseForEvent(commentary_interpreter, current_event);

	//Glen: TODO: handle filling out the data source somewhere else
	//Potentially, given that these values are ints we could source them from
	//the event
	//such as "event.action_type" instead of DS_PASS_TYPE

	//Now that the lua interpreter is setup we could just register this event with the interpreter
	//as "event" here, and the conditions can then look up "event.action_type" for example.
	//Does require the interpreter to be tied into mab central (if it isn't already)

	switch(current_event->event_type)
	{
	case SSCB_EVENT_LINEOUT_DECISION:
		{
			RUCBLineoutDecisionEvent* lineout_decision_event = MabCast< RUCBLineoutDecisionEvent >(current_event);
			MABASSERT(lineout_decision_event);
			if(!lineout_decision_event)
				return;
			commentary_data_provider->SetDataSource(DS_LINEOUT_DECISION, lineout_decision_event->decision );
		}
		break;

	case SSCB_EVENT_LINEOUT_THROW:
		{
			RUCBLineoutThrownEvent* lineout_thrown_event = MabCast< RUCBLineoutThrownEvent >(current_event);
			MABASSERT(lineout_thrown_event);
			if(!lineout_thrown_event)
				return;
			commentary_data_provider->SetDataSource(DS_LINEOUT_THROW_TYPE, (int)lineout_thrown_event->throw_type );
			commentary_data_provider->SetDataSource(DS_LINEOUT_THROW_LEGAL, (int)lineout_thrown_event->legal_throw );
			commentary_data_provider->SetDataSource(DS_LINEOUT_THROW_X_TRAVEL_ABSOLUTE, (int)lineout_thrown_event->travel_distance_x_absolute );
		}
		break;
	case SSCB_EVENT_LINEOUT_CATCH:
		{
			RUCBLineoutCatchEvent* lineout_catch_event = MabCast< RUCBLineoutCatchEvent >( current_event );
			RUCBLineoutThrownEvent* lineout_thrown_event = MabCast< RUCBLineoutThrownEvent >( context_bucket->GetLastEvent(SSCB_EVENT_LINEOUT_THROW) );
			MABASSERT(lineout_thrown_event);
			if(!lineout_thrown_event)
				return;			
			commentary_data_provider->SetDataSource(DS_LINEOUT_CATCH_TURNOVER, (int)lineout_catch_event->turnover );
			commentary_data_provider->SetDataSource(DS_LINEOUT_THROW_TYPE, (int)lineout_thrown_event->throw_type );
			commentary_data_provider->SetDataSource(DS_LINEOUT_THROW_LEGAL, (int)lineout_thrown_event->legal_throw );
			commentary_data_provider->SetDataSource(DS_LINEOUT_THROW_X_TRAVEL_ABSOLUTE, (int)lineout_thrown_event->travel_distance_x_absolute );
		}
		break;
	case SSCB_EVENT_LINEOUT_MISS:
		{
			RUCBLineoutMissEvent* miss_event = MabCast< RUCBLineoutMissEvent >(current_event);
			RUCBLineoutThrownEvent* lineout_thrown_event = MabCast< RUCBLineoutThrownEvent >( context_bucket->GetLastEvent(SSCB_EVENT_LINEOUT_THROW) );
			MABASSERT(miss_event && lineout_thrown_event);
			if(!miss_event || !lineout_thrown_event)
				return;
			commentary_data_provider->SetDataSource(DS_LINEOUT_MISS_BOTH_TEAMS, (int)miss_event->both_teams_miss );
			commentary_data_provider->SetDataSource(DS_LINEOUT_THROW_TYPE, (int)lineout_thrown_event->throw_type );
			commentary_data_provider->SetDataSource(DS_LINEOUT_THROW_LEGAL, (int)lineout_thrown_event->legal_throw );
			commentary_data_provider->SetDataSource(DS_LINEOUT_THROW_X_TRAVEL_ABSOLUTE, (int)lineout_thrown_event->travel_distance_x_absolute );
		}
		break;

	case SSCB_EVENT_RUCK:
		{
			RUCBRuckEvent* ruck_event = MabCast< RUCBRuckEvent >(current_event);
			commentary_data_provider->SetDataSource(DS_RUCK_ISOLATED, (int)ruck_event->isolated);
			commentary_data_provider->SetDataSource(DS_RUCK_NUM_PHASES, ruck_event->num_phases);
			
		}
		break;

	case SSCB_EVENT_RUCK_JOIN:
		{
			RUCBRuckPlayerJoinEvent* ruck_join_event = MabCast< RUCBRuckPlayerJoinEvent >( current_event );
			commentary_data_provider->SetDataSource(DS_RUCK_JOIN_TYPE, ruck_join_event->join_type);
			commentary_data_provider->SetDataSource(DS_RUCK_JOIN_AGE, ruck_join_event->ruck_age);
			commentary_data_provider->SetDataSource(DS_RUCK_JOIN_OFFENCE_COUNT, ruck_join_event->attackers_count);
			commentary_data_provider->SetDataSource(DS_RUCK_JOIN_OFFENCE_DIFFERENCE, ruck_join_event->attackers_count - ruck_join_event->defenders_count);
			commentary_data_provider->SetDataSource(DS_RUCK_JOIN_DEFENCE_COUNT, ruck_join_event->defenders_count);
			commentary_data_provider->SetDataSource(DS_RUCK_JOIN_AFTER_RESULT, (int)ruck_join_event->added_after_result);

			commentary_data_provider->SetDataSource(DS_RUCK_BAR_POSITION, (int)(ruck_join_event->power_bar_position * 100.0f));
			commentary_data_provider->SetDataSource(DS_RUCK_BAR_VELOCITY, (int)(ruck_join_event->power_bar_velocity * 100.0f));
		}
		break;

	case SSCB_EVENT_RUCK_RESULT:
		{
			RUCBRuckResultEvent* ruck_result_event = MabCast< RUCBRuckResultEvent >(current_event);
			commentary_data_provider->SetDataSource(DS_RUCK_RESULT, ruck_result_event->ruck_result_context);
		}
		break;

	case SSCB_EVENT_PASS:
		{
			RUCBPassEvent *pass_event = MabCast< RUCBPassEvent >(current_event);
			commentary_data_provider->SetDataSource(DS_PASS_TYPE, pass_event->action_type);
			commentary_data_provider->SetDataSource(DS_PASS_LENGTH, pass_event->pass_length);
			commentary_data_provider->SetDataSource(DS_PASS_SOURCE, pass_event->pass_source);

			commentary_interpreter->SetGlobalVariable("PASS_TYPE", pass_event->action_type);
			commentary_interpreter->SetGlobalVariable("PASS_LENGTH", pass_event->pass_length);
			commentary_interpreter->SetGlobalVariable("BAD_PASS", pass_event->bad_pass);

			bool pass_from_halted_maul = false;

			//Glen: not sure where the best place is to put this logic
			//but see if we've processed a maul looks halted event recently
			if(pass_event->pass_source == PS_FROM_MAUL)
			{
				SSContextBucketEvent *halted_event = context_bucket->GetLastEvent(SSCB_EVENT_MAUL_LOOKS_HALTED, SSCB_EVENT_MAUL_FORMED);
				pass_from_halted_maul = halted_event != NULL;
			}

			commentary_data_provider->SetDataSource(DS_PASS_FROM_HALTED_MAUL, (int)pass_from_halted_maul);
			commentary_data_provider->SetDataSource(DS_BAD_PASS, pass_event->bad_pass);
		}
		break;

	case SSCB_EVENT_COLLECTED:
		{
			RUCBCollectedEvent* collect_event = MabCast< RUCBCollectedEvent >(current_event);
			commentary_data_provider->SetDataSource(DS_COLLECT_FUMBLE_RECOVER, (int)collect_event->fumble_recover);
			commentary_data_provider->SetDataSource(DS_COLLECT_BALL_IN_MOTION, (int)collect_event->ball_in_motion);
			commentary_data_provider->SetDataSource(DS_COLLECT_DIVE, (int)collect_event->dive_recover);
			commentary_data_provider->SetDataSource( DS_COLLECTED_PREVIOUS_EVENT, collect_event->preceding_event);
		}
		break;

	case SSCB_EVENT_CATCH:
		{
			RUCBCatchEvent *catch_event = MabCast< RUCBCatchEvent >(current_event);
			commentary_data_provider->SetDataSource(DS_CATCH_TYPE, catch_event->catch_type);
			commentary_data_provider->SetDataSource(DS_CATCH_IN_AIR, catch_event->catch_in_air);
			commentary_data_provider->SetDataSource(DS_CATCH_MARKED, catch_event->catch_marked);

			RUCommentaryPlayer player_one(game, catch_event->player_index);
			RUCommentaryPlayer player_two(game, catch_event->player2_index);
			bool catch_from_opposition = player_one.team != player_two.team;

			commentary_data_provider->SetDataSource(DS_CATCH_FROM_OPPOSITION, catch_from_opposition);
			commentary_data_provider->SetDataSource(DS_CATCH_ON_FULL, catch_event->on_full);
		}
		break;

	case SSCB_EVENT_TACKLE:
	case SSCB_EVENT_TACKLEE_ON_GROUND:
		{
			RUCBTackleEvent *tackle_event = MabCast< RUCBTackleEvent >(current_event);
			commentary_data_provider->SetDataSource(DS_TACKLE_TYPE, tackle_event->tackle_type);
			commentary_data_provider->SetDataSource(DS_TACKLE_SUCCESS, tackle_event->action_result);
			commentary_data_provider->SetDataSource(DS_TACKLE_IMPACT, tackle_event->impact);
			commentary_data_provider->SetDataSource(DS_TACKLE_NUM_TACKLERS, tackle_event->num_tacklers);
			commentary_data_provider->SetDataSource(DS_TACKLE_GROUND_MADE, tackle_event->ground_made);
			commentary_data_provider->SetDataSource(DS_TACKLE_PREVIOUS_BREAKS, tackle_event->previous_breaks);
			commentary_data_provider->SetDataSource(DS_TACKLE_DOMINANCE, tackle_event->tackle_dominance);
			commentary_data_provider->SetDataSource(DS_TACKLE_TRY_TYPE, tackle_event->try_tackle_type);

			//Brian hack
			//commentary_data_provider->SetDataSource(DS_TACKLE_COUNT, tackle_event->tackle_count);

			commentary_data_provider->SetDataSource(DS_TACKLE_PLAYER_BEST_RUNNER, (int)false);

			//determine who the best runner is.
			RUCommentaryPlayer commentary_player(game, tackle_event->player_index);
			if(commentary_player.team)
			{
				unsigned int best_runner_id = 0;
				GetBestPlayerAt(statistics_system, commentary_player.team, &RUDB_STATS_PLAYER::running_meters_gained, &best_runner_id);
				if((int)best_runner_id == tackle_event->player_index)
				{
					commentary_data_provider->SetDataSource(DS_TACKLE_PLAYER_BEST_RUNNER, (int)true);
				}
			}

		}
		break;

	case SSCB_EVENT_INJURY:
		{
			RUCBInjuryEvent *injury_event = MabCast< RUCBInjuryEvent >(current_event);
			commentary_data_provider->SetDataSource(DS_INJURY_TYPE, injury_event->injury_type);
		}
		break;

	case SSCB_EVENT_FUMBLE:
		{
			RUCBFumbleEvent *fumble_event = MabCast< RUCBFumbleEvent >(current_event);
			commentary_data_provider->SetDataSource(DS_HANDLING_ERROR_FROM_TACKLE, fumble_event->from_tackle);
			commentary_interpreter->SetGlobalVariable("HANDLING_ERROR_FROM_TACKLE", fumble_event->from_tackle);

			RUCommentaryPlayer commentary_player(game, fumble_event->player_index);

			if(commentary_player.team)
			{
				int handling_errors = statistics_system->GetCurrentMatchStat( commentary_player.team, &RUDB_STATS_PLAYER::handling_errors );

				int handling_error_rate = CalculateErrorRate(handling_errors);

				commentary_data_provider->SetDataSource(DS_T1_HANDLING_ERROR_RATE, handling_error_rate);
				commentary_data_provider->SetDataSource(DS_T1_HANDLING_ERRORS, handling_errors);
			}
		}
		break;

	case SSCB_EVENT_KNOCK_ON:
		{
			RUCBKnockOnEvent *knock_on_event = MabCast< RUCBKnockOnEvent >(current_event);
			commentary_data_provider->SetDataSource(DS_HANDLING_ERROR_FROM_TACKLE, knock_on_event->from_tackle);
			commentary_interpreter->SetGlobalVariable("HANDLING_ERROR_FROM_TACKLE", knock_on_event->from_tackle);

			RUCommentaryPlayer commentary_player(game, knock_on_event->player_index);
			if(commentary_player.team)
			{
				int handling_errors = statistics_system->GetCurrentMatchStat( commentary_player.team, &RUDB_STATS_PLAYER::handling_errors );
				int handling_error_rate = CalculateErrorRate(handling_errors);

				commentary_data_provider->SetDataSource(DS_T1_HANDLING_ERROR_RATE, handling_error_rate);
				commentary_data_provider->SetDataSource(DS_T1_HANDLING_ERRORS, handling_errors);
			}
		}
		break;

	case SSCB_EVENT_KICK:
		{
			RUCBKickEvent* kick_event = MabCast< RUCBKickEvent >(current_event);

			commentary_data_provider->SetDataSource( DS_KICK_FIELD_POSITION, commentary_data_provider->GetDataValue(DS_FIELD_POSITION));
		
			commentary_data_provider->SetDataSource( DS_KICK_CONTEXT, kick_event->kick_context);
			commentary_data_provider->SetDataSource( DS_KICK_TYPE, kick_event->kick_type);
			commentary_data_provider->SetDataSource( DS_KICK_DISTANCE_X, (int)kick_event->distance_x);
			commentary_data_provider->SetDataSource( DS_KICK_DISTANCE_Z, (int)kick_event->distance_z);
			commentary_data_provider->SetDataSource( DS_DEFENSIVE_PRESSURE, kick_event->pressure );
			commentary_data_provider->SetDataSource( DS_KICK_BOUNCE_FIELD_POSITION, kick_event->first_bounce_field_position );
			commentary_data_provider->SetDataSource( DS_KICK_BOUNCE_X_ABSOLUTE, kick_event->first_bounce_x_absolute);
			commentary_data_provider->SetDataSource( DS_KICK_OUT_ON_FULL, (int)kick_event->out_on_full );
			commentary_data_provider->SetDataSource( DS_KICK_BOUNCE_OUT_PROBABLE, (int)kick_event->bounce_out_probable );
		}
		break;

	case SSCB_EVENT_BALL_DEAD:
		{
			RUCBBallDeadEvent *dead_event = MabCast< RUCBBallDeadEvent >( current_event );
			commentary_data_provider->SetDataSource( DS_DEAD_CARRIED, (int) dead_event->carried);

			//determine the attack direction;
			ERugbyPlayDirection play_direction = game->GetGameState()->GetAttackingTeam()->GetPlayDirection();
			//reverse it if we're going the other way
			float ball_free_relative_to_offence = dead_event->ball_free_position_z * play_direction;
			//offset it from our own goal line.
			ball_free_relative_to_offence += FIELD_LENGTH * 0.5f;	

			commentary_data_provider->SetDataSource( DS_BALL_DEAD_KICK_POSITION, (int) ball_free_relative_to_offence);
		}
		break;

	case SSCB_EVENT_PENALTY:
		{
			if(RUCBPenaltyEvent *penalty_event = (RUCBPenaltyEvent*) current_event)
			{
				commentary_data_provider->SetDataSource(DS_PENALTY_REASON, penalty_event->penalty_reason);
			}
		}
		break;

	case SSCB_MARSHALL_PENALTY:
		{
			if (context_bucket->GetLastEvent(SSCB_NISBO_OFFSIDE_ADVANTAGE, SSCB_EVENT_PENALTY))
			{
				// no penalty event, it is the new offside call
				commentary_data_provider->SetDataSource(DS_PENALTY_REASON, PENALTY_REASON_OFFSIDE);
			}
			else
			{
				// must have had a penalty event first, now we are following up
				RUCBPenaltyEvent *penalty_event = (RUCBPenaltyEvent*)context_bucket->GetLastEvent(SSCB_EVENT_PENALTY);
				if (penalty_event)
				{
					// no penalty event, it is the new offside call
					commentary_data_provider->SetDataSource(DS_PENALTY_REASON, penalty_event->penalty_reason);
				}
			}
		}
		break;

	case SSCB_EVENT_PENALTY_GOAL_KICK_DECIDED:
	case SSCB_EVENT_PENALTY_TOUCH_KICK_DECIDED:
	case SSCB_EVENT_PENALTY_TAP_DECIDED:
		{
			MabCentralAccessor tap_event_accessor(current_event);
			float* ptr_position_x = tap_event_accessor.Get<float>( "position_x" );
			float* ptr_position_z = tap_event_accessor.Get<float>( "position_z" );
			MABASSERT( ptr_position_x && ptr_position_z );

			float tap_position_x = *ptr_position_x;
			float tap_position_z = *ptr_position_z;

			tap_position_x = MabMath::Fabs(position_x);
			tap_position_z *= offense_play_direction;
			tap_position_z += FIELD_LENGTH * 0.5f;

			commentary_data_provider->SetDataSource( DS_PENALTY_DECIDE_X_ABSOLUTE, (int)tap_position_x);
			commentary_data_provider->SetDataSource( DS_PENALTY_DECIDE_Z_OWN_GOAL_RELATIVE, (int)tap_position_z);
		}
		break;

	case SSCB_EVENT_KICK_RESULT_ANTICIPATE:
	case SSCB_EVENT_KICK_RESULT:
		{
			RUCBKickResultEvent* kick_result_event = MabCast< RUCBKickResultEvent >(current_event);

			ERugbyPlayDirection play_direction = game->GetGameState()->GetAttackingTeam()->GetPlayDirection();

			RUCommentaryPlayer kicking_player(game, kick_result_event->player_index);
			if(kicking_player.team)
			{
				play_direction = kicking_player.team->GetPlayDirection();
			}

			//set all the datasources from the kick event that triggered this result
			RUCBKickEvent* kick_event = MabCast< RUCBKickEvent >( context_bucket->GetLastEvent(SSCB_EVENT_KICK) );
			MABASSERT( kick_event );
			int kick_result_z_field_position = int(( kick_event->position_z * play_direction ) + FIELD_LENGTH * 0.5f);


			commentary_data_provider->SetDataSource(DS_KICK_CONTEXT, kick_result_event->kick_context);
			commentary_data_provider->SetDataSource(DS_KICK_RESULT_SUCCESS, (int)kick_result_event->success);
			commentary_data_provider->SetDataSource(DS_KICK_RESULT_Z_FIELD_POSITION, kick_result_z_field_position);

			FVector try_line_ball_position(kick_result_event->position_x, 0.0f, kick_result_event->position_z);

			float ball_position_down_field = try_line_ball_position.z * play_direction + FIELD_LENGTH * 0.5f;

			float goal_post_half_width = game->GetSpatialHelper()->GetPostHalfWidth();
			float distance = MabMath::Abs( MabMath::Abs(try_line_ball_position.x) - goal_post_half_width );
			const float NEAR_DISTANCE = 1.0f;
			commentary_data_provider->SetDataSource(DS_KICK_RESULT_NEAR_POSTS, distance < NEAR_DISTANCE ? 1 : 0 );

			RUCBKickAtPostsReadyEvent* kick_at_posts_ready_event = MabCast< RUCBKickAtPostsReadyEvent >( context_bucket->GetLastEvent(SSCB_EVENT_KICK_AT_POSTS_READY) );
			//MABASSERT(kick_event);
			if(kick_at_posts_ready_event)
			{
				commentary_data_provider->SetDataSource(DS_KICK_AT_POSTS_X_POSITION_ABSOLUTE, kick_at_posts_ready_event->abs_x_kick_position);
				commentary_data_provider->SetDataSource(DS_KICK_AT_POSTS_SCORE_DIFFERENCE, kick_at_posts_ready_event->score_difference);
				commentary_data_provider->SetDataSource(DS_KICK_AT_POSTS_IS_CONVERSION, kick_at_posts_ready_event->is_conversion ? 1 : 0 );
				commentary_data_provider->SetDataSource(DS_KICK_AT_POSTS_IS_PENALTY, kick_at_posts_ready_event->is_penalty ? 1 : 0 );
			}

			bool hit_posts = kick_result_event->num_post_strikes > 0;

			commentary_data_provider->SetDataSource(DS_KICK_AT_POSTS_HIT_POST, hit_posts ? 1 : 0);	

			bool falls_short = !hit_posts && ball_position_down_field < FIELD_LENGTH;
			commentary_data_provider->SetDataSource(DS_KICK_AT_POSTS_IS_SHORT, falls_short ? 1 : 0 );

			bool terrible = false;
			terrible = MabMath::Abs( try_line_ball_position.x ) > 20.0f;
			commentary_data_provider->SetDataSource(DS_KICK_AT_POSTS_MISS_TERRIBLE, terrible ? 1 : 0 );
			
			bool cross_the_posts = MabMath::Sign( kick_event->position_x ) == MabMath::Sign( kick_result_event->position_x );
			commentary_data_provider->SetDataSource(DS_KICK_AT_POSTS_MISS_ACROSS_POSTS, cross_the_posts ? 1 : 0 );

			bool to_the_left = kick_result_event->position_x * (float)play_direction > 0.0f;
			bool to_the_right = kick_result_event->position_x * (float)play_direction < 0.0f;
			commentary_data_provider->SetDataSource(DS_KICK_AT_POSTS_MISS_LEFT, to_the_left ? 1 : 0 );
			commentary_data_provider->SetDataSource(DS_KICK_AT_POSTS_MISS_RIGHT, to_the_right ? 1 : 0 );

			bool miss_just = !terrible && MabMath::Abs( try_line_ball_position.x ) > goal_post_half_width + 1.0f;
			commentary_data_provider->SetDataSource(DS_KICK_AT_POSTS_MISS_JUST, miss_just ? 1 : 0 );

			if( (KickContext)kick_result_event->kick_context == KC_DROPGOAL )
			{
				commentary_data_provider->SetDataSource(DS_DROP_GOAL_X_POSITION_ABSOLUTE, int(MabMath::Abs( kick_event->position_x ) ) );

				RUTeam* drop_goal_team = game->GetTeamFromDB( kick_result_event->team_index );
				int dropgoal_attacking_score = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( drop_goal_team, &RUDB_STATS_TEAM::score);
				int dropgoal_defending_score = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( (RUTeam*)drop_goal_team->GetOppositionTeam(), &RUDB_STATS_TEAM::score);
				int drop_goal_score_extend = ( dropgoal_attacking_score - defending_score - SIFApplication::GetApplication()->GetMatchGameSettings()->GetDropGoalScore(false) ) > 0 ? 1 : 0;
				commentary_data_provider->SetDataSource(DS_DROP_GOAL_SCORE_EXTEND, drop_goal_score_extend );
			}

			//set the number of successful penalty kicks for this player
			MABASSERT(kicking_player.db_player);
			int kicker_penalties_scored = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( kicking_player.team->GetSide() , kicking_player.db_player->GetDbId(), &RUDB_STATS_PLAYER::successful_penalty_goals);

			commentary_data_provider->SetDataSource(DS_KICKER_PENALTIES_SCORED, kicker_penalties_scored);
		}
		break;

	case SSCB_EVENT_KICK_TERRITORY:
		{
			RUCBTerritoryKickEvent *territory_event = MabCast< RUCBTerritoryKickEvent >(current_event);

			commentary_data_provider->SetDataSource(DS_KICK_TERRITORY_GAINED, (int)territory_event->territory_gained);
			commentary_data_provider->SetDataSource(DS_KICK_TERRITORY_ON_FULL, territory_event->on_full ? 1 : 0);
		}
		break;

	case SSCB_CONVERSION_TRANSITION:
	case SSCB_EVENT_KICK_AT_POSTS_READY:
		{
			RUCBKickAtPostsReadyEvent* conversion_event = MabCast< RUCBKickAtPostsReadyEvent >(current_event);
			commentary_data_provider->SetDataSource(DS_KICK_AT_POSTS_X_POSITION_ABSOLUTE, conversion_event->abs_x_kick_position);
			commentary_data_provider->SetDataSource(DS_KICK_AT_POSTS_SCORE_DIFFERENCE, conversion_event->score_difference);
			commentary_data_provider->SetDataSource(DS_KICK_AT_POSTS_IS_CONVERSION, conversion_event->is_conversion ? 1 : 0 );
			commentary_data_provider->SetDataSource(DS_KICK_AT_POSTS_IS_PENALTY, conversion_event->is_penalty ? 1 : 0 );
		}
		break;

	case SSCB_EVENT_LINE_BREAK:
		{
			RUCBLineBreakEvent* line_break_event = MabCast< RUCBLineBreakEvent >(current_event);
			commentary_data_provider->SetDataSource( DS_LINE_BREAK_TYPE, line_break_event->line_break_type );
		}
		break;

	case SSCB_EVENT_KICKOFF_READY:
		{
			RUCBKickOffReady* kick_off_ready = MabCast< RUCBKickOffReady >(current_event);
			commentary_data_provider->SetDataSource(DS_TIME_KEEPING_CONTEXT, kick_off_ready->time_keeping_context);
			commentary_data_provider->SetDataSource(DS_KICK_CONTEXT, KC_KICKOFF);
		}
		break;

	case SSCB_EVENT_KICKOFF_WHISTLE:
		{
			RUCBKickOffWhistle* kick_off_whistle = MabCast< RUCBKickOffWhistle >(current_event);
			commentary_data_provider->SetDataSource(DS_TIME_KEEPING_CONTEXT, kick_off_whistle->time_keeping_context);
			commentary_data_provider->SetDataSource(DS_KICK_CONTEXT, KC_KICKOFF);
		}
		break;

	case SSCB_EVENT_TRY_ATTEMPT:
		{
			RUCBTryAttemptEvent *attempt_event = MabCast< RUCBTryAttemptEvent >(current_event);
			bool is_diving = attempt_event->is_diving;
			commentary_data_provider->SetDataSource(DS_TRY_ATTEMPT_DIVING, (int) is_diving);
		}
		break;

	case SSCB_EVENT_TRY:
		{
			RUCBTryEvent *try_event = MabCast< RUCBTryEvent >(current_event);

			commentary_data_provider->SetDataSource(DS_TRY_LOCATION, try_event->try_location_context);

			// was this the first try of the match?
			RUTeam* try_team = game->GetTeamFromDB( try_event->team_index );
			RUTeam* other_team = game->GetTeam( OtherTeam( try_team->GetSide() ) );
			RUStatisticsSystem* stats = SIFApplication::GetApplication()->GetStatisticsSystem();
			int trys_scored_try_team = stats->GetCurrentMatchStat( try_team, &RUDB_STATS_PLAYER::tries_scored );
			int trys_scored_other_team = stats->GetCurrentMatchStat( other_team, &RUDB_STATS_PLAYER::tries_scored );
			bool first_try = trys_scored_try_team + trys_scored_other_team == 1;
			commentary_data_provider->SetDataSource( DS_TRY_FIRST_MATCH_TRY, first_try ? 1 : 0 );

			commentary_data_provider->SetDataSource( DS_TRY_TEAM_TRY_COUNT, trys_scored_try_team );

			// was this try for the home or away side
			commentary_data_provider->SetDataSource( DS_TRY_HOME_TEAM, try_team->IsHomeTeam() ? 1 : 0 );

			// how many tries has this dude scored in this match
			int tries_scored_this_game_by_player = stats->GetCurrentMatchStat( try_team->GetSide(), (unsigned short)try_event->player_index, &RUDB_STATS_PLAYER::tries_scored );
			commentary_data_provider->SetDataSource( DS_TRY_PLAYER_TRY_COUNT, tries_scored_this_game_by_player );

			commentary_data_provider->SetDataSource( DS_TRY_MINUTES_SINCE_LAST_TRY, try_event->minutes_since_scoring_last_this_period );
			commentary_data_provider->SetDataSource( DS_TRY_METERS_GAINED, (int)try_event->meters_gained );
			commentary_data_provider->SetDataSource( DS_TRY_METRES_GAINED_PLAYER, (int)try_event->meters_gained_player );
			commentary_data_provider->SetDataSource( DS_DEFENSIVE_PRESSURE, try_event->defensive_pressure );
		}
		break;

	case SSCB_EVENT_SCRUM_CALLED:
		{
			RUCBScrumCalledEvent *scrum_called_event = MabCast< RUCBScrumCalledEvent >(current_event);
			RUTeam *offence_team = game->GetTeamFromDB(scrum_called_event->team_index);
			RUTeam *defensive_team = static_cast<RUTeam *>(offence_team->GetOppositionTeam());

			unsigned int attacking_scrums = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( offence_team, &RUDB_STATS_TEAM::scrums);
			unsigned int defending_scrums = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( defensive_team, &RUDB_STATS_TEAM::scrums);
			commentary_data_provider->SetDataSource( DS_SCRUM_COUNT, attacking_scrums + defending_scrums);

			RUStatsTracker *stats_tracker = game->GetStatsTracker();
			const RUStatsTracker::StatsTeam &attacker_stats = stats_tracker->GetTeamStats(offence_team);
			commentary_data_provider->SetDataSource( DS_SCRUM_OFFENCE_LAST_PREVIOUS_OWN, attacker_stats.won_previous_own_scrum);

			//setup some scrum result stat data sources
			SetupScrumDataSources(offence_team);
		}
		break;

	case SSCB_EVENT_SCRUM:
		{
		}
		break;

	case SSCB_EVENT_SCRUM_BALL_IN:
		{
		}
		break;

	case SSCB_EVENT_SCRUM_PUSHING:
		{
			RUCBScrumPushingEvent *pushing_event = MabCast< RUCBScrumPushingEvent >(current_event);

			commentary_data_provider->SetDataSource(DS_SCRUM_RELATIVE_POWER, (int)(pushing_event->relative_power * 10.0f));

			commentary_data_provider->SetDataSource(DS_SCRUM_ATTACK_PUSH_POWER, (int)(pushing_event->attacking_team_power * 100.0f));
			commentary_data_provider->SetDataSource(DS_SCRUM_DEFENCE_PUSH_POWER, (int)(pushing_event->defending_team_power * 100.0f));
			commentary_data_provider->SetDataSource(DS_SCRUM_PUSH_COUNT, pushing_event->shunt_count);
		}
		break;

	case SSCB_EVENT_SCRUM_RESULT:
		{
			RUCBScrumResultEvent *scrum_result_event = MabCast< RUCBScrumResultEvent >(current_event);

			//grab the result context
			commentary_data_provider->SetDataSource( DS_SCRUM_RESULT, scrum_result_event->scrum_result_context);
			commentary_data_provider->SetDataSource( DS_SCRUM_RESTART_CONTEXT, scrum_result_event->reset_context);
			commentary_data_provider->SetDataSource( DS_SCRUM_METRES_GAINED, (int)scrum_result_event->meters_gained);

			RUTeam *offence_team = game->GetTeamFromDB(scrum_result_event->team_index);

			//setup some scrum result stat data sources
			SetupScrumDataSources(offence_team);
		}
		break;


	case SSCB_EVENT_MAUL_FORMED:
	case SSCB_EVENT_MAUL_BALL_RELEASED:
	case SSCB_EVENT_MAUL_PROGRESS:
	case SSCB_EVENT_MAUL_COLLAPSED:
	case SSCB_EVENT_MAUL_HELD_UP:
	case SSCB_EVENT_MAUL_LOOKS_HALTED:
		{
			RUCBMaulEvent *maul_event = MabCast< RUCBMaulEvent >(current_event);
			commentary_data_provider->SetDataSource(DS_MAUL_METRES_GAINED, (int)maul_event->ground_made);
			commentary_data_provider->SetDataSource(DS_MAUL_VELOCITY, (int)(maul_event->velocity * 10.0f));
		}
		break;

	case SSCB_EVENT_PICKED_UP:
		{
			RUCBPickedUpEvent *picked_up_event = MabCast< RUCBPickedUpEvent >(current_event);

			commentary_data_provider->SetDataSource( DS_PICKED_UP_CONTEXT, picked_up_event->picked_up_context);
		}
		break;

	case SSCB_EVENT_BALL_HOLDER_RUN:
		{
			RUCBBallHolderRunningEvent *running_event = MabCast< RUCBBallHolderRunningEvent >(current_event);

			commentary_data_provider->SetDataSource( DS_CURRENT_RUNNING_EVENT, running_event->running_event_type );

		}
		break;

	case SSCB_EVENT_ADVANTAGE:
		{
			RUCBAdvantageEvent *advantage_event = MabCast< RUCBAdvantageEvent>(current_event);

			commentary_data_provider->SetDataSource(DS_ADVANTAGE_EVENT_TYPE, advantage_event->advantage_event_type);
		}
		break;

	case SSCB_EVENT_TOUCHDOWN_OWN_GOAL:
		{
			RUCBTouchdownOwnGoal *touch_event = MabCast< RUCBTouchdownOwnGoal>(current_event);

			commentary_data_provider->SetDataSource(DS_OWN_GOAL_TOUCHDOWN_RECEIVED_INGOAL, (int)touch_event->ball_received_in_goal);
			commentary_data_provider->SetDataSource(DS_OWN_GOAL_TOUCHDOWN_FROM_PENALTY_GOAL, (int)touch_event->received_from_penalty_goal); 
			
		}
		break;

	case SSCB_EVENT_CARRIED_INTO_TOUCH:
		{
			RUCBCarriedOutOfPlayEvent *carried_out_event = MabCast< RUCBCarriedOutOfPlayEvent >(current_event);
			commentary_data_provider->SetDataSource(DS_CARRIED_INTO_TOUCH_FROM_TACKLE, (int)carried_out_event->from_tackle);
		}
		break;

	case SSCB_EVENT_INTERCHANGE_STARTED:
		{
			RUSubstitutionManager *substitution_manager = game->GetSubstitutionManager();
			const RUInterchangeEvent *entry = substitution_manager->GetQueuedEvent(0);
			if(entry)
			{
				if(entry->GetPrimaryPlayer())
				{
					RUTeam *team = entry->GetPrimaryPlayer()->GetAttributes()->GetTeam();

					//set the number of interchanges made before this sequence of interchanges
					commentary_data_provider->SetDataSource(DS_TEAM_INTERCHANGES_MADE, substitution_manager->GetNumInterchangesCompleted(team));

					//set the number of interchanges that this team has left after this sequence of interchanges
					commentary_data_provider->SetDataSource(DS_TEAM_INTERCHANGES_REMAINING, substitution_manager->GetNumRemainingInterchanges(team));
				}
			}
		}
		break;

	case SSCB_EVENT_INTERCHANGE_MADE:
		{
			RUCBInterchangeEvent *interchange_event = MabCast< RUCBInterchangeEvent >(current_event);

			commentary_data_provider->SetDataSource(DS_INTERCHANGE_TYPE, interchange_event->interchange_type);
		}
		break;

	case SSCB_EVENT_DISTRACTION:
		{
			RUCBDistractedEvent *distracted_event = MabCast< RUCBDistractedEvent >(current_event);

			commentary_data_provider->SetDataSource(DS_DISTRACTION_LEVEL, distracted_event->distraction_level);
		}
		break;
	
	case SSCB_EVENT_TMO_DECISION:
		{
			RUCBTelevisionMatchOfficialEvent *tmo_event = MabCast< RUCBTelevisionMatchOfficialEvent >(current_event);

			commentary_data_provider->SetDataSource(DS_TMO_TRY_AWARDED, tmo_event->try_awarded);
			commentary_data_provider->SetDataSource(DS_TMO_TRY_POSSIBLY_HELD_UP, tmo_event->possibly_held_up);
		}
		break;

	case SSCB_EVENT_REPLAY:
	case SSCB_EVENT_REPLAY_TRY:
		{
			SSCBCutsceneEvent *cutscene_event = MabCast< SSCBCutsceneEvent >(current_event);
			MABASSERT( cutscene_event );

			commentary_data_provider->SetDataSource( DS_REPLAY_TYPE, cutscene_event->replay_type );
		}
		break;

	case SSCB_PMP_OPENING_STATEMENT:
	case SSCB_PMP_COMPETITION_STANDINGS:
	case SSCB_PMP_TEAM_FORM:
	case SSCB_PMP_INTRODUCE_C2_1:
	case SSCB_PMP_INTRODUCE_C2_2:
	case SSCB_PMP_PREMATCH_STATEMENT:
	case SSCB_PMP_MATCH_ATMOSPHERE:
	case SSCB_PMP_MATCH_EXPECTATIONS:
	case SSCB_PMP_C2_PREMATCH_CLOSING_STATEMENT:
	case SSCB_PMP_TEAM_LINEUPS:
	case SSCB_PMP_PRE_HAKA:
	case SSCB_PMP_POST_HAKA:
	case SSCB_PMP_C1_PREMATCH_CLOSING_STATEMENT:
	case SSCB_PMP_CURRENT_SCORE:
	case SSCB_PMP_HIGH_LEVEL_STATS:
	case SSCB_PMP_PASS_TO_C2:
	case SSCB_PMP_GENERIC_AGREEMENT:
	case SSCB_PMP_HALF_TIME_ANALYSIS:
	case SSCB_PMP_STATS_OPENER:
	case SSCB_PMP_TERRITORY_POSSESSION:
	case SSCB_PMP_STATS_BREAKDOWN:
	case SSCB_PMP_STATS_CLOSER:
	case SSCB_PMP_HALF_TIME_CLOSING_C2:
	case SSCB_PMP_HALF_TIME_CLOSING_C1:
	case SSCB_PMP_FULL_TIME:
	case SSCB_PMP_MATCH_OUTCOME:
	case SSCB_PMP_PLAYER_EMOTION:
	case SSCB_PMP_COMPETITION_IMPACT:
	case SSCB_PMP_WIN_STREAK:
	case SSCB_PMP_FULL_TIME_OPENER:
	case SSCB_PMP_MATCH_ANALYSIS:
	case SSCB_PMP_KEY_FIGURES_STATS:
	case SSCB_PMP_FULL_TIME_RESULT:
	case SSCB_PMP_MATCH_TREND:
	case SSCB_PMP_FINAL_SCORE:
		{
			RUCBPreMidPostEvent *pmp_event = MabCast< RUCBPreMidPostEvent >(current_event);


			//need to set some team ID of the team
			if(RUTeam *team = game->GetTeamFromDB(pmp_event->team_index))
				commentary_interpreter->SetGlobalVariable("TEAM_MNEMONIC1", team->GetDbTeam().GetMnemonic());

			if(RUTeam *team = game->GetTeamFromDB(pmp_event->team2_index))
				commentary_interpreter->SetGlobalVariable("TEAM_MNEMONIC2", team->GetDbTeam().GetMnemonic());

			//todo, move this to the start of half time, and the start of full time.
			SetupPMPSourceData();

		}
		break;
	}
}

template <typename StatType>
static StatType GetBestPlayerAt(RUStatisticsSystem *statistics_system, RUTeam *team, StatType RUDB_STATS_PLAYER::*member, unsigned int *player_id_out)
{
	StatType best_stat = 0;
	unsigned int best_player_id = 0;
	*player_id_out = 0;
	int playersOnTeamIncBench = SIFApplication::GetApplication()->GetMatchGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeamIncBench();
	for(int i = 0; i < team->GetDbTeam().GetNumLineups() && i < playersOnTeamIncBench/*NUM_STAT_PLAYERS*/; i++)
	{
		unsigned short player_id = team->GetDbTeam().GetLineup(i).player_id;
		StatType player_stat = statistics_system->GetCurrentMatchStat(team->GetSide(), player_id, member);

		//only keep track of the clear leader
		if(player_stat == best_stat)
		{
			best_player_id = 0;
		}
		else if(player_stat > best_stat)
		{
			best_stat = player_stat;
			best_player_id = player_id;
		}
	}

	*player_id_out = best_player_id;

	if(!best_player_id)
		return 0;

	return best_stat;
}

static int CalculateBestGoalScorer(RUStatisticsSystem *statistics_system, RUTeam *team, unsigned int *player_id_out)
{
	int best_stat = 0;
	unsigned int best_player_id = 0;
	*player_id_out = 0;
	int playersOnTeamIncBench = SIFApplication::GetApplication()->GetMatchGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeamIncBench();

	for(int i = 0; i < team->GetDbTeam().GetNumLineups() && i < playersOnTeamIncBench/*NUM_STAT_PLAYERS*/; i++)
	{
		unsigned short player_id = team->GetDbTeam().GetLineup(i).player_id;

		int attempts = 
			statistics_system->GetCurrentMatchStat(team->GetSide(), player_id, &RUDB_STATS_PLAYER::conversion_attempts)
			+ statistics_system->GetCurrentMatchStat(team->GetSide(), player_id, &RUDB_STATS_PLAYER::field_goal_attempts)
			+ statistics_system->GetCurrentMatchStat(team->GetSide(), player_id, &RUDB_STATS_PLAYER::penalty_goal_attempts);

		int successes = 
			statistics_system->GetCurrentMatchStat(team->GetSide(), player_id, &RUDB_STATS_PLAYER::successful_conversion_attempts)
			+ statistics_system->GetCurrentMatchStat(team->GetSide(), player_id, &RUDB_STATS_PLAYER::successful_field_goals)
			+ statistics_system->GetCurrentMatchStat(team->GetSide(), player_id, &RUDB_STATS_PLAYER::successful_penalty_goals);


		//rate down a player who has missed a lot. (effectively only count them if they
		//have over a 50 % success rate.
		int player_stat = successes * 2 - attempts;
		if(player_stat < 0)
			player_stat = 0;

		//only keep track of the clear leader
		if(player_stat == best_stat)
		{
			best_player_id = 0;
		}
		else if(player_stat > best_stat)
		{
			best_stat = player_stat;
			best_player_id = player_id;
		}
	}

	*player_id_out = best_player_id;

	if(best_player_id <= 0)
		return 0;

	return best_stat;
}

///Moderately intensive function that sets up most of the data sources required for Pre-Mid-Post match
///commentary
void RUCommentaryDataCollector::SetupPMPSourceData()
{
	RUStatisticsSystem *statistics_system = SIFApplication::GetApplication()->GetStatisticsSystem();

	int game_length = SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.game_length;
	int actual_game_length = SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetActualGameLength();
	int game_length_stat_multiplier = actual_game_length/*ACTUAL_GAME_LENGTH*/ / game_length;

	RUTeam *winning_team = game->GetTeam(SIDE_A);
	RUTeam *losing_team = game->GetTeam(SIDE_B);

	unsigned int team_a_score = statistics_system->GetCurrentMatchStat( winning_team, &RUDB_STATS_TEAM::score );
	unsigned int team_b_score = statistics_system->GetCurrentMatchStat( losing_team, &RUDB_STATS_TEAM::score );

	if(team_b_score > team_a_score)
	{
		std::swap(winning_team, losing_team);
	}

	bool ranfurley_defence = false;
	if(SIFApplication::GetApplication()->GetCareerModeManager()->IsActive())
	{
		ranfurley_defence = SIFApplication::GetApplication()->GetCareerModeManager()->IsCurrentMatchRanfurlyShieldChallenge();
	}

	commentary_data_provider->SetDataSource(DS_RANFURLY_DEFENCE, ranfurley_defence ? 1:0);

	if(favoured_team)
	{
		RUTeam *other_team = (RUTeam *)favoured_team->GetOppositionTeam();

		/*
		//additionally setup the score difference between the favoured team and the other team.
		unsigned int favoured_score = statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_TEAM::score );
		unsigned int opposing_score = statistics_system->GetCurrentMatchStat( other_team, &RUDB_STATS_TEAM::score );
		int score_difference = ( (int)favoured_score ) - ( (int)opposing_score );
		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_SCORE_DIFFERENCE, score_difference );
		*/


		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_PEAK_SCORE_LAG, match_trend_analysis->GetTeamsPerspective(favoured_team->GetSide()).max_lag);

		//setup the possession difference
		float favoured_possession = statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_TEAM::possession );
		float opposing_possession = statistics_system->GetCurrentMatchStat( other_team, &RUDB_STATS_TEAM::possession );
		float total_possession = favoured_possession + opposing_possession;
		float possession_difference = favoured_possession - opposing_possession;
		if(total_possession > 0.0f)
		{
			possession_difference /= total_possession;
			possession_difference *= 100.0f;
		}

		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_POSSESSION_DIFFERENCE, (int)possession_difference );

		//setup the territory difference
		float favoured_territory = statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_TEAM::territory );
		float opposing_territory = statistics_system->GetCurrentMatchStat( other_team, &RUDB_STATS_TEAM::territory );
		float territory_difference = favoured_territory - opposing_territory;
		float total_territory = favoured_territory + opposing_territory;
		if(total_territory > 0.0f)
		{
			territory_difference /= total_territory;
			territory_difference *= 100.0f;
		}

		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_TERRITORY_DIFFERENCE, (int)territory_difference);

		int human_team_try_count = statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::tries_scored );
		int other_team_try_count = statistics_system->GetCurrentMatchStat( other_team, &RUDB_STATS_PLAYER::tries_scored );

		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_TRY_COUNT, game_length_stat_multiplier * human_team_try_count);
		commentary_data_provider->SetDataSource(DS_OTHER_TEAM_TRY_COUNT, game_length_stat_multiplier * other_team_try_count);
		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_TRY_COUNT_DIFFERENCE, game_length_stat_multiplier * (human_team_try_count - other_team_try_count));

		int human_5plus_phases = statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_TEAM::num_five_plus_phases );
		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_5PLUS_PHASES, game_length_stat_multiplier * human_5plus_phases);

		int human_penalty_count = statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::penalties_against );
		int other_penalty_count = statistics_system->GetCurrentMatchStat( other_team, &RUDB_STATS_PLAYER::penalties_against );

		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_PENALTY_COUNT, game_length_stat_multiplier * human_penalty_count);
		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_PENALTY_DIFFERENCE, game_length_stat_multiplier * ( human_penalty_count - other_penalty_count));
		commentary_data_provider->SetDataSource(DS_TOTAL_PENALTY_COUNT, game_length_stat_multiplier * (human_penalty_count + other_penalty_count));


		int human_handling_errors = statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::handling_errors );
		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_HANDLING_ERRORS, game_length_stat_multiplier * human_handling_errors);

		int human_tackle_count = statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::successful_tackles );
		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_TACKLE_COUNT, game_length_stat_multiplier * human_tackle_count);

		int human_misstackle_count = statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::tackle_attempts ) - human_tackle_count;
		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_MISSTACKLE_COUNT, game_length_stat_multiplier * human_misstackle_count);

		int total_injuries = 0;
		total_injuries += statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::injuries );
		total_injuries += statistics_system->GetCurrentMatchStat( other_team, &RUDB_STATS_PLAYER::injuries );
		commentary_data_provider->SetDataSource(DS_TOTAL_INJURIES, total_injuries);

		float goal_kick_rating = 0.5f;

		int goal_kick_attempts = 0;
		goal_kick_attempts += statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::conversion_attempts );
		goal_kick_attempts += statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::field_goal_attempts );
		goal_kick_attempts += statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::penalty_goal_attempts );

		int goal_kick_successes = 0;
		goal_kick_successes += statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::successful_conversion_attempts );
		goal_kick_successes += statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::successful_field_goals );
		goal_kick_successes += statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::successful_penalty_goals );

		if(goal_kick_attempts > 0)
		{
			goal_kick_rating = (float)goal_kick_successes / (float)goal_kick_attempts;

			//if there was only one attempt, normalise the result as this isn't statistically significant
			if(goal_kick_attempts == 1)
			{
				goal_kick_rating = 0.25f + (0.5f * goal_kick_rating);
			}
			//similarly normalise the result a bit for 2 attempts
			else if(goal_kick_attempts == 2)
			{
				goal_kick_rating = 0.1f + (0.8f * goal_kick_rating);
			}
		}

		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_GOALKICK_RATING, (int)(goal_kick_rating * 100.0f));

		//TODO: need to determine the primary kicking player for the human team.

		//set humans kicks in play count
		int kicks_in_play_count = statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::kicks );
		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_KICKS_IN_PLAY_COUNT, game_length_stat_multiplier * kicks_in_play_count);

		//set total line breaks.
		int total_line_breaks = statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::line_breaks );
		total_line_breaks += statistics_system->GetCurrentMatchStat( other_team, &RUDB_STATS_PLAYER::line_breaks );
		commentary_data_provider->SetDataSource(DS_TOTAL_LINE_BREAKS, game_length_stat_multiplier * total_line_breaks);

		//set total number of yellow cards
		int total_yellow_cards = statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::yellow_cards );
		total_yellow_cards += statistics_system->GetCurrentMatchStat( other_team, &RUDB_STATS_PLAYER::yellow_cards );
		commentary_data_provider->SetDataSource(DS_TOTAL_YELLOW_CARDS, total_yellow_cards);

		//classify the red cards
		int red_cards_favoured = statistics_system->GetCurrentMatchStat( favoured_team, &RUDB_STATS_PLAYER::red_cards );
		int red_cards_other = statistics_system->GetCurrentMatchStat( other_team, &RUDB_STATS_PLAYER::red_cards );

		if(red_cards_favoured + red_cards_other == 1)
			commentary_data_provider->SetDataSource(DS_RED_CARD_CLASSIFICATION, CRCC_ONE);
		else if(red_cards_favoured >= 2 && red_cards_other >= 2)
			commentary_data_provider->SetDataSource(DS_RED_CARD_CLASSIFICATION, CRCC_BOTH_TEAMS_TWO_PLUS);
		else if(red_cards_favoured >= 2 || red_cards_other >= 2)
			commentary_data_provider->SetDataSource(DS_RED_CARD_CLASSIFICATION, CRCC_ONE_TEAM_TWO_PLUS);
		else
			commentary_data_provider->SetDataSource(DS_RED_CARD_CLASSIFICATION, CRCC_NONE);

		float human_performance = (possession_difference + territory_difference) * 0.5f;
		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_PERFORMANCE, (int) human_performance);


		//add some stats and shit
		const RUStatsTracker::StatsTeam &favoured_stats = game->GetStatsTracker()->GetTeamStats( favoured_team->GetSide());
		const RUStatsTracker::StatsTeam &other_stats = game->GetStatsTracker()->GetTeamStats( other_team->GetSide());

		commentary_data_provider->SetDataSource(
			DS_TOTAL_RUCK_TURNOVERS,
			game_length_stat_multiplier * (favoured_stats.ruck_turnovers + other_stats.ruck_turnovers)
			);

		commentary_data_provider->SetDataSource(
			DS_TOTAL_TURNOVERS,
			game_length_stat_multiplier * (favoured_stats.turnovers + other_stats.turnovers)
			);

		commentary_data_provider->SetDataSource(
			DS_HUMAN_TEAM_RUCK_TURNOVERS,
			game_length_stat_multiplier * favoured_stats.ruck_turnovers
			);

		commentary_data_provider->SetDataSource(
			DS_HUMAN_TEAM_LINEOUT_TURNOVERS,
			game_length_stat_multiplier * favoured_stats.lineout_turnovers
			);

		commentary_data_provider->SetDataSource(
			DS_HUMAN_TEAM_SCRUM_TURNOVERS,
			game_length_stat_multiplier * favoured_stats.scrum_turnovers
			);

		commentary_data_provider->SetDataSource(
			DS_OTHER_TEAM_SCRUM_TURNOVERS,
			game_length_stat_multiplier * other_stats.scrum_turnovers
			);


		commentary_data_provider->SetDataSource(
			DS_HUMAN_TEAM_TURNOVERS,
			game_length_stat_multiplier * favoured_stats.turnovers
			);
	}
	else
	{
		MABBREAK();
	}

	if(winning_team && losing_team)
	{
		float winning_rating = RateTeamEffectiveness(winning_team);
		float losing_rating = RateTeamEffectiveness(losing_team);

		float power_diff = winning_rating - losing_rating;
		commentary_data_provider->SetDataSource(DS_LEADING_TEAM_RELATIVE_POWER, (int) (power_diff * 100.0f));

		commentary_interpreter->SetGlobalVariable("LEADING_TEAM_RELATIVE_POWER", (int) (power_diff * 100.0f));


		int winning_team_try_count = statistics_system->GetCurrentMatchStat( winning_team, &RUDB_STATS_PLAYER::tries_scored );
		int losing_team_try_count = statistics_system->GetCurrentMatchStat( losing_team, &RUDB_STATS_PLAYER::tries_scored );

		commentary_data_provider->SetDataSource(DS_WINNING_TEAM_RELATIVE_TRY_COUNT, winning_team_try_count - losing_team_try_count);

		//figure out who the best players are at this stage
		int best_trys_scored = GetBestPlayerAt(statistics_system, winning_team, &RUDB_STATS_PLAYER::tries_scored, &best_try_scorer_id);
		commentary_data_provider->SetDataSource(DS_WINNING_TEAM_BEST_TRY_SCORER_COUNT, best_trys_scored);

		int best_line_breaks = GetBestPlayerAt(statistics_system, winning_team, &RUDB_STATS_PLAYER::line_breaks, &best_line_breaker_id);
		commentary_data_provider->SetDataSource(DS_WINNING_TEAM_BEST_LINE_BREAKER_COUNT, best_line_breaks);

		int best_tackles = GetBestPlayerAt(statistics_system, winning_team, &RUDB_STATS_PLAYER::successful_tackles, &best_tackler_id);
		commentary_data_provider->SetDataSource(DS_WINNING_TEAM_BEST_TACKLER_COUNT, best_tackles);

		int best_goals = CalculateBestGoalScorer(statistics_system, winning_team, &best_goal_scorer_id);
		commentary_data_provider->SetDataSource(DS_WINNING_TEAM_BEST_GOALS_SCORER_COUNT, best_goals);

		int best_points = GetBestPlayerAt(statistics_system, winning_team, &RUDB_STATS_PLAYER::points_scored, &best_points_scorer_id);
		commentary_data_provider->SetDataSource(DS_WINNING_TEAM_BEST_POINT_SCORER_COUNT, best_points);
	}
	else
	{
		commentary_data_provider->SetDataSource(DS_LEADING_TEAM_RELATIVE_POWER, 0 );
	}
}




enum TeamPlayStyle
{
	TEAMPLAYSTYLE_RUN,
	TEAMPLAYSTYLE_BALANCED,
	TEAMPLAYSTYLE_KICK
};

static const float RUNNING_SLIDER_THRESHOLD = 0.37f;
static const float KICKING_SLIDER_THRESHOLD = 0.55f;

/// Collects the data that will be consistent throughout the whole game.
void RUCommentaryDataCollector::CalculateGameData()
{
	

	//set the stadium abbreviation
	unsigned short stadium_id = game->GetGameSettings().stadium_settings.GetStadiumID();
	const RUDB_STADIUM *stadium = SIFApplication::GetApplication()->GetMatchStadiumManager()->GetStadiumFromID(stadium_id);

	//#rc4_hack_commentary (remove incorrect commentary for stadiums)
	if (!CanSetStadiumABBR(stadium))
	{
		commentary_interpreter->SetGlobalVariable("STADIUM_ABBR", "");
	}
	else
	{
		commentary_interpreter->SetGlobalVariable("STADIUM_ABBR", stadium->GetAbbreviation());
	}



	favoured_team = ChooseFavouredTeam();
	if(favoured_team)
	{
		commentary_data_provider->SetDataSource(DS_HUMAN_TEAM_ID, favoured_team->GetDbTeam().GetDbId());
	}

	CalculatePredictedWinner();

	//determine the play styles of the two teams
	commentary_data_provider->SetDataSource(DS_PLAY_STYLE_COMPARISON, PS_NO_COMPARISON);

	RUTeam *team_a = game->GetTeam(SIDE_A);
	RUTeam *team_b = game->GetTeam(SIDE_B);

	TeamPlayStyle team_a_style = TEAMPLAYSTYLE_BALANCED;
	float team_a_slider = 
		team_a->GetDbTeam().GetNormalisedBackPassKickSlider(TEAM_SLIDER_DEF) * 0.4f
		+ team_a->GetDbTeam().GetNormalisedBackPassKickSlider(TEAM_SLIDER_MID) * 0.3f
		+ team_a->GetDbTeam().GetNormalisedBackPassKickSlider(TEAM_SLIDER_ATT) * 0.3f;

	if(team_a_slider < RUNNING_SLIDER_THRESHOLD)
		team_a_style = TEAMPLAYSTYLE_RUN;
	else if(team_a_slider > KICKING_SLIDER_THRESHOLD)
		team_a_style = TEAMPLAYSTYLE_KICK;

	TeamPlayStyle team_b_style = TEAMPLAYSTYLE_BALANCED;
	float team_b_slider = 
		team_b->GetDbTeam().GetNormalisedBackPassKickSlider(TEAM_SLIDER_DEF) * 0.4f
		+ team_b->GetDbTeam().GetNormalisedBackPassKickSlider(TEAM_SLIDER_MID) * 0.3f
		+ team_b->GetDbTeam().GetNormalisedBackPassKickSlider(TEAM_SLIDER_ATT) * 0.3f;

	if(team_b_slider < RUNNING_SLIDER_THRESHOLD)
		team_b_style = TEAMPLAYSTYLE_RUN;
	else if(team_b_slider > KICKING_SLIDER_THRESHOLD)
		team_b_style = TEAMPLAYSTYLE_KICK;

	if(team_a_style == TEAMPLAYSTYLE_BALANCED || team_b_style == TEAMPLAYSTYLE_BALANCED)
	{
		commentary_data_provider->SetDataSource(DS_PLAY_STYLE_COMPARISON, PS_NO_COMPARISON);
	}
	else if(team_a_style == TEAMPLAYSTYLE_RUN && team_b_style == TEAMPLAYSTYLE_RUN)
	{
		commentary_data_provider->SetDataSource(DS_PLAY_STYLE_COMPARISON, PS_RUN_RUN);
	}
	else if(team_a_style == TEAMPLAYSTYLE_KICK && team_b_style == TEAMPLAYSTYLE_KICK)
	{
		commentary_data_provider->SetDataSource(DS_PLAY_STYLE_COMPARISON, PS_KICK_KICK);
	}
	else
	{
		commentary_data_provider->SetDataSource(DS_PLAY_STYLE_COMPARISON, PS_RUN_KICK);
	}

	//Setup some environment stuff
	commentary_data_provider->SetDataSource(DS_WIND_STRENGTH, (int)game->GetGameSettings().weather_settings.wind_strength);
	commentary_data_provider->SetDataSource(DS_DAY, game->GetGameSettings().weather_settings.time_of_day==TIME_DAY?1:0);
	commentary_data_provider->SetDataSource(DS_RAINING, (int)game->GetGameSettings().weather_settings.raining);
	commentary_data_provider->SetDataSource(DS_OVERCAST, (int)game->GetGameSettings().weather_settings.overcast_selected);
	commentary_data_provider->SetDataSource(DS_CROWD_SIZE, (int)game->GetGameSettings().crowd_settings.crowd_size);
	commentary_data_provider->SetDataSource(DS_TIME_EXTRA_TIME_AVAILABLE, (int)game->GetGameSettings().game_settings.extra_time_enabled);

	bool wet = game->GetGameSettings().weather_settings.raining && !game->GetStadiumManager()->IsStadiumCovered();
	commentary_data_provider->SetDataSource(DS_WET, wet ? 1 : 0);
	commentary_data_provider->SetDataSource(DS_STADIUM_COVERED, game->GetStadiumManager()->IsStadiumCovered() ? 1 : 0);


	//indicate whether both teams are international
	bool is_international = team_a->GetDbTeam().IsInternational() && team_b->GetDbTeam().IsInternational();
	commentary_interpreter->SetGlobalVariable("INTERNATIONAL_MATCH", is_international);

	//setup some competition data
	competition_analysis->AnalyseCompetition(commentary_interpreter, favoured_team);

	SetupPMPSourceData();
}

bool RUCommentaryDataCollector::CanSetStadiumABBR(const RUDB_STADIUM* stadium)
{
	return strcmp(stadium->GetName(), "NZ Cup Stadium") != 0 //NZ Cup Stadium referring to this stadium as Rotorua International Stadium
		&& strcmp(stadium->GetName(), "HBF Park") != 0 // HBF Park referring to this stadium as NIB Stadium
		&& strcmp(stadium->GetName(), "Sky Stadium") != 0 //This generic stadium has a actual stadium's Abbreviation that isnt relevant
		&& strcmp(stadium->GetName(), "Japan Stadium") != 0 //This generic stadium has a actual stadium's Abbreviation that isnt relevant
		&& strcmp(stadium->GetName(), "Orangetheory Stadium") != 0 //AMI stadium was renamed to orangeTheory Stadium.
		&& strcmp(stadium->GetName(), "Marvel Stadium") != 0; //Etihad Stadium was using Marvel Stadium's commentary name.
}

/// Collects stats that we don't need to update very frequently.
void RUCommentaryDataCollector::CollectPeriodicStats()
{
	//grab some fatigue for each team
	match_trend_analysis->PerformPeriodicUpdate();
}

/// sets up the additional data sources for scrums from the perspective of the attacking
/// team. be it the team that is feeding the scrum, or the team that has won the scrum
void RUCommentaryDataCollector::SetupScrumDataSources( RUTeam *attacking_team)
{
	RUStatsTracker *stats_tracker = game->GetStatsTracker();

	const RUStatsTracker::StatsTeam &attacker_stats = stats_tracker->GetTeamStats(attacking_team);
	const RUStatsTracker::StatsTeam &defender_stats = stats_tracker->GetTeamStats(static_cast<RUTeam *>(attacking_team->GetOppositionTeam()));

	int total_scrum_count = attacker_stats.num_scrums_fed_with_result + defender_stats.num_scrums_fed_with_result;
	float metres_rating = 0;
	float turnover_rating = 0;

	if( total_scrum_count > 0 ) 
	{
		metres_rating = (float)(attacker_stats.scrum_meters_gained - defender_stats.scrum_meters_gained) / (float)total_scrum_count;
		turnover_rating = ((float)(attacker_stats.scrums_won_turned_over - defender_stats.scrums_won_turned_over) / (float)total_scrum_count) * 2.0f;
	}

	float scrum_dominance = metres_rating + turnover_rating;

	commentary_data_provider->SetDataSource(DS_SCRUM_DOMINANCE_OFFENCE, (int)(scrum_dominance * 20.0f)); // Note, multiplying by 20.0 makes the range -100 to 100 +- 40 for the effect of turnovers
}

/// Returns the team with the most number of humans currently controlling it,
/// Or the home team if there is an equal number of humans in control of each team
RUTeam *RUCommentaryDataCollector::ChooseFavouredTeam()
{
	//determine the commentators currently favoured teams
	RUTeam *new_favoured_team = NULL;
	RUTeam *home_team = NULL;
	int favoured_team_player_count = 0;
	for(int i = 0; i < game->GetNumTeams(); i++)
	{
		RUTeam *team = game->GetTeam(i);

		if(team->IsHomeTeam())
		{
			home_team = team;
		}

		int player_count = team->GetNumHumanPlayers();
		if(player_count == favoured_team_player_count)
		{
			new_favoured_team = NULL;
		}
		else if(player_count > favoured_team_player_count)
		{
			new_favoured_team = team;
			favoured_team_player_count = player_count;
		}
	}

	if(!new_favoured_team)
	{
		//the teams have the same number of human players, make the commentators
		//favour the home team.
		new_favoured_team = home_team;
	}

	return new_favoured_team;
}

void RUCommentaryDataCollector::CalculatePredictedWinner()
{
	RUTeam *team_a = game->GetTeam(SIDE_A);
	float team_rating_a = RateTeamEffectiveness(team_a);

	RUTeam *team_b = game->GetTeam(SIDE_B);
	float team_rating_b = RateTeamEffectiveness(team_b);

	predicted_winner = team_a;
	power_difference = team_rating_a - team_rating_b;

	if(team_rating_b > team_rating_a)
	{
		predicted_winner = team_b;
		power_difference = team_rating_b - team_rating_a;
	}
	else if(team_rating_b == team_rating_a)
	{
		predicted_winner = NULL;
		power_difference = 0.0f;
	}

	commentary_data_provider->SetDataSource(DS_TEAM_POWER_DIFFERENCE, (int)(power_difference * 100.0f));
}

static const float CONFIDENCE_CONTRIBUTION = 0.5f;
float RUCommentaryDataCollector::RateTeamEffectiveness(RUTeam *team)
{
	//grab confidence
	//confidence is normally at 0.5 for a team that hasn't had a win or loss streak, like at
	//the start of the competition.
	float confidence = 0.5f;

	//grab confidence for each team
	if(SIFApplication::GetApplication()->GetCareerModeManager()->IsActive())
	{
		if(SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro() || SIFApplication::GetApplication()->GetCareerModeManager()->GetActiveCompetition() == NULL)
			confidence = 0.5f;
		else
			confidence = SIFApplication::GetApplication()->GetCareerModeManager()->GetActiveCompetition()->GetTeamStatsForTeamId(team->GetDbTeam().GetDbId()).GetConfidence();
	}
	

	//grab this teams strength
	float power = team->GetDbTeam().GetNormaliseRanking();

	float rating = power * ((1.0f - CONFIDENCE_CONTRIBUTION) + (CONFIDENCE_CONTRIBUTION * confidence));

	return rating;
}