/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "RURoleSetplay.h"
#include "Match/Components/RUActionManager.h"
#include "Character/RugbyCharacter.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuck.h"
#include "Match/SSSpatialHelper.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/AI/SetPlays/SSSetPlayManager.h"
#include "Match/Input/RUPassExtendInterface.h"
#include "Match/Ball/SSBall.h"
#include "Match/RugbyUnion/RUGameGetToBall.h"

//Action includes
#include "Match/AI/Actions/RUAction.h"
#include "Match/AI/Actions/RUActionPass.h"
#include "Match/AI/Actions/RUActionKick.h"
#include "Match/AI/Roles/Competitors/RURoleGetTheBall.h"

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
#define SETPLAYDEBUG 1
//Debug include
#include "Utility/consoleVars.h"
#include "Engine/Public/DrawDebugHelpers.h"
#include "Mab/MabDebug.h"
#endif
#include "Match/RugbyUnion/RUInputKickInterface.h"
#include "Match/SSRole.h"
#include "Match/SIFGameWorld.h"
#include "Match/RugbyUnion/RUFunctionalityEnums.h"
#include "Match/Components/SSHumanPlayer.h"
#include "WWUIFunctionLibrary.h"
#include "TimerManager.h"
#include "Animation/AnimInstance.h"

MABRUNTIMETYPE_IMP1(RURoleSetplay, SSRole);

#define SETPLAY_RUNNER_SPEED_MULTIPLIER 1.4f

RURoleSetplay::RURoleSetplay(SIFGameWorld* game)
	: SSRole(game)
{

}

void RURoleSetplay::Enter(ARugbyCharacter* player)
{
	SSRole::Enter(player);
	this->m_pPlayer = player;
	if (player && player->GetActionManager())
	{
#ifdef SETPLAYDEBUG
		if (CVarSetPlayDebug.GetValueOnGameThread() > 0)
		{
			player->SetDebugSquareVisibility(true);
		}
#endif
		player->GetActionManager()->HFLockAll();
	}
	
	m_TargetWaypointIndex = -1;
	FormationManager = nullptr;
	SetplayManager = nullptr;

	if (player && player->GetMovement())
	{
		prevWaypointAcceptRange = player->GetMovement()->GetWaypointAcceptRange();
		player->GetMovement()->SetWaypointAcceptRange(1.0f);
	}

	//Bind a listener to the ball being kicked
	if (m_pGame &&
		m_pGame->GetEvents())
	{
		m_pGame->GetEvents()->knock_on.Add(this, &RURoleSetplay::BallDropped);
		m_pGame->GetEvents()->fumble.Add(this, &RURoleSetplay::BallDropped);
		m_pGame->GetEvents()->kick.Add(this, &RURoleSetplay::UpdateCatchKickTargetPosition);
	}
	
}

void RURoleSetplay::Exit(bool forced)
{
	if (m_pPlayer)
	{
		if (m_pPlayer->GetActionManager())
		{
			m_pPlayer->GetActionManager()->HFUnlockAll();
		}
		if (SetplayManager)
		{
			SetplayManager->RemovePlayerFromSetplay(m_pPlayer);		//This is done just to make sure setplays end if a cutscene starts
		}

		if (m_pPlayer->GetMovement())
		{
			m_pPlayer->GetMovement()->SetWaypointAcceptRange(prevWaypointAcceptRange);
			m_pPlayer->GetMovement()->EnableDecelAtTarget(true);
			m_pPlayer->GetMovement()->SetThrottleWhenCloseToWaypoint(true);
			m_pPlayer->GetMovement()->OverrideMaxSpeed();
			m_pPlayer->GetMovement()->OverrideMaxAcceleration();
		}
#ifdef SETPLAYDEBUG
		if (CVarSetPlayDebug.GetValueOnGameThread() > 0)
		{
			m_pPlayer->SetDebugSquareVisibility(false);
		}
#endif
	}

	//Unbind the ball kicked listener
	if (m_pGame &&
		m_pGame->GetEvents())
	{
		m_pGame->GetEvents()->kick.Remove(this, &RURoleSetplay::UpdateCatchKickTargetPosition);
		m_pGame->GetEvents()->kick.Remove(this, &RURoleSetplay::SetFocusToRunner);
		m_pGame->GetEvents()->knock_on.Remove(this, &RURoleSetplay::BallDropped);
		m_pGame->GetEvents()->fumble.Remove(this, &RURoleSetplay::BallDropped);
	}
	else if (SIFApplication::GetApplication()->GetActiveGameWorld() &&
		SIFApplication::GetApplication()->GetActiveGameWorld()->GetEvents())
	{
		SIFApplication::GetApplication()->GetActiveGameWorld()->GetEvents()->kick.Remove(this, &RURoleSetplay::UpdateCatchKickTargetPosition);
		SIFApplication::GetApplication()->GetActiveGameWorld()->GetEvents()->kick.Remove(this, &RURoleSetplay::SetFocusToRunner);
		SIFApplication::GetApplication()->GetActiveGameWorld()->GetEvents()->knock_on.Remove(this, &RURoleSetplay::BallDropped);
		SIFApplication::GetApplication()->GetActiveGameWorld()->GetEvents()->fumble.Remove(this, &RURoleSetplay::BallDropped);
	}

	SSRole::Exit(forced);
}

void RURoleSetplay::UpdateLogic(const MabTimeStep & game_time_step)
{
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
	if (CVarSetPlayDebug.GetValueOnGameThread() > 0)
	{
		ShowDebug();
	}
#endif

	//Get the managers if they are null
	if (!FormationManager)
	{
		if (m_pPlayer &&
			m_pPlayer->GetAttributes() &&
			m_pPlayer->GetAttributes()->GetTeam())
		{
			FormationManager = m_pPlayer->GetAttributes()->GetTeam()->GetFormationManager();
			
			if (FormationManager)
			{
				SetplayManager = FormationManager->GetSetplayManager();
			}

			if (m_pGame && m_pGame->GetGameState() && m_pGame->GetGameState()->GetPhase() != RUGamePhase::PLAY_THE_BALL)
			{
				//Call the setplay if you are acting as first receiver
				if (FormationManager->GetPlayerArea(m_pPlayer)->get_name().Contains(TEXT("flyhalf"), ESearchCase::IgnoreCase))
				{
					m_pPlayer->GetAnimation()->GetStateMachine().SendRequest("CALL_SET_PLAY");
				}
			}

			//When we first get the formation manager, set the player action state
			if (const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(m_pPlayer)))
			{
				if (FSerialiseFormationZone * zone = const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(m_pPlayer))->get_zone())
				{
					if (zone->actionList.IsValidIndex(m_TargetWaypointIndex + 1))
					{
						m_ActionState = zone->actionList[m_TargetWaypointIndex + 1];
						if (m_ActionState != ERugbySetplayAction::NOTHING)
						{
							bWaypointHasAction = true;
						}
					}
				}
			}
			else
			{
				m_ActionState = ERugbySetplayAction::NOTHING;
			}
		}
	}

#ifdef SETPLAYDEBUG
	if (CVarSetPlayDebug.GetValueOnGameThread() > 0 && SetplayManager)
	{
		SetplayManager->DrawPlayerWaypoints(m_pPlayer);

		static const FVector textOffset(0.0f, 100.0f, 0.0f);
		FString debugString = FString::Printf
		(
			TEXT("Waypoint index: %d \nWaypoint Reached: %s \n Performing Action: %s"),
			m_TargetWaypointIndex,
			bWaypointReached ? TEXT("TRUE") : TEXT("FALSE"),
			bWaypointHasAction ? TEXT("TRUE") : TEXT("FALSE")
		);
		DrawDebugString(m_pPlayer->GetWorld(), textOffset, debugString, m_pPlayer, FColor::White, 0.01f);
		
	}
#endif

	//Logic to cancel the setplay
	if (m_pGame &&
	m_pGame->GetGameState() &&
	m_pGame->GetGameState()->GetBallHolder())
	{
		//If the ball holder is tackled.
		if (m_pGame->GetGameState()->GetBallHolder()->GetActionManager() &&
		m_pGame->GetGameState()->GetBallHolder()->GetActionManager()->IsActionRunning(RU_ACTION_INDEX::ACTION_TACKLEE))
		{
			SetplayManager->AbortSetplay();
		}

		//If the enemy team gets the ball
		if (m_pPlayer && m_pGame->GetGameState()->GetBallHolder()->GetAttributes()->GetTeam() == m_pPlayer->GetAttributes()->GetOppositionTeam())
		{
			SetplayManager->AbortSetplay();
		}

		//If the ball bounces twice. This is to make sure the players aren't standing around watching the ball too long if the kick rebounds off the post or is kicked
		//very far from the catching player.
		if (m_pPlayer && m_pGame->GetBall()->GetNumBounces() >= 2)
		{
			SetplayManager->AbortSetplay();
		}
	}

	//Handle if the player is allowed to take over movement
	if (bWaitingForPlayerToOverrideMovement)
	{
		if (SSHumanPlayer * controllingHuman = m_pPlayer->GetHumanPlayer())
		{
			const FVector2D leftStickMovement = controllingHuman->GetLeftStick();
			const float DEADZONE_SIZE = 0.8f;

			if (leftStickMovement.Size() > DEADZONE_SIZE)
			{
				if (const SSRoleArea * constArea = FormationManager->GetPlayerArea(m_pPlayer))
				{
					SSRoleArea * tempArea = const_cast<SSRoleArea*>(constArea);		
					bLockPlayerControl = false;
					bWaitingForPlayerToOverrideMovement = false;
					return;
				}
			}
		}
	}


	//Handle updating the player movement
	if (m_pPlayer)
	{
		if (FormationManager)
		{
			/// Retain our slot in the formation manager
			FormationManager->UpdatePlayerSlot(m_pPlayer);

			//If the player has a target area, move towards it
			if (SSRoleArea * tempArea = const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(m_pPlayer)))
			{
#ifdef SETPLAYDEBUG
				if (CVarSetPlayDebug.GetValueOnGameThread() > 0)
				{
					//Debug drawing
					if (!bWaypointReached)
					{
						FVector target_pos = m_pPlayer->GetMovement()->GetTargetPosition();
						FVector startPos = m_pPlayer->GetMovement()->GetCurrentPosition();
						MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(target_pos, converted_target_pos);
						MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(startPos, converted_start_pos);

						DrawDebugLine(
							m_pPlayer->GetWorld(),
							converted_start_pos,
							converted_target_pos,
							FColor(255, 0, 0),
							false,
							-1,
							10,
							20
						);
					}
				}
#endif
				if (bLockPlayerControl && !bWaypointReached && !m_pPlayer->GetActionManager()->IsActionRunning(RU_ACTION_INDEX::ACTION_GETTHEBALL))
				{
					if (m_pPlayer->GetMovement())
					{
						if (!bWaypointHasAction && SetplayManager->HasSetplayStarted())
							m_pPlayer->GetMovement()->SetFacingFlags(AFFLAG_FACEMOTION);

						m_pMovement->ForceWaypointChange();
					}
					wwNETWORK_TRACE_JG("RURoleSetplay");
					
					FormationManager->DoStandardFormationMovement(m_pPlayer, true);
				}

				if (!bLockPlayerControl)
				{
					if (m_pGame->GetGameState()->GetBallHolder() == m_pPlayer)
					{
						if (m_pPlayer->GetAttributes()->GetTeam()->GetHumanPlayer(0))
						{
							m_pPlayer->GetActionManager()->HFUnlockAll();
						}
						else
						{
							SetplayManager->AbortSetplay();
						}
					}
				}
			}
			else
			{
				MABASSERTMSG(const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(m_pPlayer)), "No area found");
			}
		}
	}

	//Handle a player reaching a waypoint and updating the new target and action
	if (m_pPlayer)
	{
		if (m_pPlayer->GetState())
		{
			wwNETWORK_TRACE_JG("SetParticipationLevel {%4d} %s", __LINE__, __FILE__);
			m_pPlayer->GetState()->SetParticipationLevel(PARTICIPATION_LEVEL::PL_PARTICPATING);
		}
		if (FormationManager)
		{
			if(m_pPlayer->GetMovement())
			{
				//Check if they have reached the waypoint
				FVector currentPos = m_pPlayer->GetMovement()->GetCurrentPosition();
				FVector targetPos = FVector::ZERO;
				float urgency;
				ACTOR_SPEED max_actor_speed;
				FormationManager->GetStandardFormationMovement(m_pPlayer, targetPos, urgency, max_actor_speed);
				float idleRadius = 0;
				if (m_pPlayer->GetAnimation() && m_pPlayer->GetAnimation()->GetIdleRadiusVariable())
				{
					idleRadius = m_pPlayer->GetAnimation()->GetIdleRadiusVariable()->getValue();
				}
				float distance = (targetPos - currentPos).Magnitude();
				bWaypointReached = bWaypointReached ? true : (distance - idleRadius <= 1.0f);
				
				//Mark the player as having reached its sync point if there is one
				if (bWaypointReached)
				{
					SetplayManager->SetPlayerAtSyncPoint(m_pPlayer);
					float currentSpeed = m_pPlayer->GetMovement()->GetCurrentSpeed();
					if (currentSpeed < 1 )
					{
// 						if (!bIsInPosition && FormationManager && player == FormationManager->GetSetplayFirstReceiver())
// 						{
// 							player->GetAnimation()->GetStateMachine().SendRequest("CALL_SET_PLAY");
// 						}
						bIsInPosition = true;
					}
				}

				if (bMovingToNextWaypoint ||							//This is used by the setplay managed to update the waypoint if the smoothing avoids it
					(bWaypointReached &&								//Check player has reached waypoint
					bWaypointHasAction == false &&					//Check they have completed the action they had for this waypoint
					SetplayManager->IsSyncPointComplete(m_pPlayer) &&	//Check they are not waiting at a sync point
					SetplayManager->HasSetplayStarted()))			//Check the setplay has started			
				{
					//Reset the waypoint bool
					bMovingToNextWaypoint = false;
					bSmoothingInProgress = false;
					bWaypointReached = false;

					//Increment the current waypoint index
					m_TargetWaypointIndex++;
					
					//Update the player action state
					if (const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(m_pPlayer)) &&
						const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(m_pPlayer))->get_zone()->actionList.IsValidIndex(m_TargetWaypointIndex))
					{
						if (FSerialiseFormationZone * zone = const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(m_pPlayer))->get_zone())
						{
							m_ActionState = zone->actionList[m_TargetWaypointIndex];
							if (m_ActionState != ERugbySetplayAction::NOTHING)
							{
								bWaypointHasAction = true;
							}
						}
					}
					else
					{
						m_ActionState = ERugbySetplayAction::NOTHING;
					}
				}
			}
		}
	}

	//Handle performing actions
	if (bWaypointHasAction)
	{
		if (SetplayManager && SetplayManager->HasSetplayStarted())
		{
			switch (m_ActionState)
			{
			case ERugbySetplayAction::DUMMYPASS:
				m_PassType = PASS_TYPE::PT_DUMMY;
			case ERugbySetplayAction::PASS:
				UpdatePassAction();
				break;
			case ERugbySetplayAction::KICKPUNT:
				m_KickType = KickType::KICKTYPE_LONGPUNT;
				UpdateKickAction();
				break;
			case ERugbySetplayAction::KICKCHIP:
				m_KickType = KickType::KICKTYPE_SETPLAYCHIPKICK;
				UpdateKickAction();
				break;
			case ERugbySetplayAction::BAITPASS:
				bIsDummy = true;
			case ERugbySetplayAction::CATCHPASS:
				UpdateCatchPassAction();
				break;
			case ERugbySetplayAction::CATCHKICK:
				UpdateCatchKickAction(game_time_step.delta_time.ToSeconds());
				break;
			case ERugbySetplayAction::KICKGOAL:
				UpdateKickGoalAction();
				break;
			case ERugbySetplayAction::DECISION:
				UpdateDecisionAction();
				break;
			default:
				break;
			}
		}
	}

	SSRole::UpdateLogic(game_time_step);
}

ERugbySetplayAction RURoleSetplay::GetActionState()
{
	return m_ActionState;
}

void RURoleSetplay::SetTargettedStateOverride(ERugbySetplayAction inAction)
{
	m_TargettedStateOverride = inAction;
}

void RURoleSetplay::ClearTargettedStateOverride()
{
	m_TargettedStateOverride = ERugbySetplayAction::NOTHING;
}

RLROLE_INTERCEPT_TYPE RURoleSetplay::GetDesiredInterceptType()
{
	return RLRIT_FROM_SETPLAY;
}

void RURoleSetplay::BallDropped(ARugbyCharacter* player, bool fromTackle, const FVector& position)
{
	if (SetplayManager)
	{
		SetplayManager->AbortSetplay();
	}
}

void RURoleSetplay::UpdatePassAction()
{
	if (m_pPlayer && m_pPlayer->GetActionManager())
	{
		//If player has not started passing start it
		if (!m_pPlayer->GetActionManager()->IsActionRunning(RU_ACTION_INDEX::ACTION_PASS) && 
			m_pPlayer->GetActionManager()->CanUseAction(RU_ACTION_INDEX::ACTION_PASS) &&
			SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetBallHolder() == m_pPlayer)
		{
			//Pass parameters
			//ARugbyCharacter* target_player, PASS_TYPE type, RLROLE_INTERCEPT_TYPE intercept_override
			ARugbyCharacter * target_player = nullptr;
			PASS_TYPE type = m_PassType;
			RLROLE_INTERCEPT_TYPE intercept_override = RLROLE_INTERCEPT_TYPE::RLRIT_FROM_SETPLAY;
			ERugbySetplayAction targetRole = (m_ActionState == ERugbySetplayAction::PASS) ? ERugbySetplayAction::CATCHPASS : ERugbySetplayAction::BAITPASS;

			//Find the teammate waiting for the pass
			if (m_pPlayer->GetAttributes() && m_pPlayer->GetAttributes()->GetTeam())
			{
				RUTeam * team = m_pPlayer->GetAttributes()->GetTeam();
				for (ARugbyCharacter * teamPlayer : team->GetPlayers())
				{
					if (RURoleSetplay * setplayRole = teamPlayer->GetRoleSafe<RURoleSetplay>())
					{
						if (setplayRole->GetActionState() == targetRole)
						{
							target_player = teamPlayer;
						}
					}
				}
			}

			//only start the pass if a player was found to pass to, otherwise try again next frame (the pass receiver might be behind in the setplay)
			if (target_player != nullptr)
			{
				//Notify the receiving player 
				if (RURoleSetplay * setplayRole = target_player->GetRoleSafe<RURoleSetplay>())
				{
					setplayRole->SetPassingPlayer(m_pPlayer);
				}

				//If the dummy player is selected, do a proper pass.
				if (target_player == SetplayManager->GetSetplayTargettedCharacter())
				{
					type = PT_STANDARD;
				}

				m_pPlayer->GetActionManager()->StartAction<RUActionPass>(target_player, type, intercept_override);
				bWaypointHasAction = false;

#ifdef SETPLAYDEBUG
				if (CVarSetPlayDebug.GetValueOnGameThread() > 0)
				{
					if (GEngine)
						GEngine->AddOnScreenDebugMessage(-1, 15.0f, FColor::Purple, FString::Printf(TEXT("%s is passing to %s"), *m_pPlayer->GetFName().ToString(), *target_player->GetFName().ToString()));
				}
#endif 

				
			}
		}
	}
}

void RURoleSetplay::UpdateKickAction()
{
	if (m_pPlayer && m_pPlayer->GetActionManager())
	{
		//If player has not started passing start it
		if (!m_pPlayer->GetActionManager()->IsActionRunning(RU_ACTION_INDEX::ACTION_PREPARE_KICK) &&
			!m_pPlayer->GetActionManager()->IsActionRunning(RU_ACTION_INDEX::ACTION_KICK) &&
			m_pPlayer->GetActionManager()->CanUseAction(RU_ACTION_INDEX::ACTION_KICK))
		{
			//kick params
			//KickType p_KickType, const FVector &recommended_pos, bool account_for_wind
			KickType p_KickType = KickType::KICKTYPE_UPANDUNDER; //#MB no idea what kind of kick it should use
			FVector recommended_pos = FVector::ZERO;
			bool account_for_wind = true;		//account for wind otherwise set play wont work.
			bool kickCalculated = false;
			ARugbyCharacter * kickReceiver = nullptr;

			//Calculate the kick position based on the catcher.
			//Find the teammate waiting for the kick
			if (m_pPlayer->GetAttributes() && m_pPlayer->GetAttributes()->GetTeam())
			{
				RUTeam * team = m_pPlayer->GetAttributes()->GetTeam();
				for (ARugbyCharacter * teamPlayer : team->GetPlayers())
				{
					if (RURoleSetplay * setplayRole = teamPlayer->GetRoleSafe<RURoleSetplay>())
					{
						if (setplayRole->GetActionState() == ERugbySetplayAction::CATCHKICK)
						{
							//Initialise the kick parameters based on kick type
							//Note: The times are just guesses based on observation and are used to estimate the target kick position
							float maxKickRange = 0;
							float kickTime = 0;
							switch (m_KickType)
							{
							case KickType::KICKTYPE_LONGPUNT:
								maxKickRange = MAX_LONG_PUNT_DIST;
								kickTime = 3;
								break;
							case KickType::KICKTYPE_SETPLAYCHIPKICK:
								maxKickRange = MAX_SETPLAY_CHIP_KICK_DIST;
								kickTime = 1.5;
							}

							recommended_pos = CalculateKickTarget(teamPlayer, maxKickRange, kickTime);
							
							//Clamp the kick position within the field
							m_pPlayer->GetGameWorld()->GetSpatialHelper()->ClampWaypointToClampFieldExtents(recommended_pos);
#ifdef SETPLAYDEBUG
							if (CVarSetPlayDebug.GetValueOnGameThread() > 0)
							{
								MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(recommended_pos, convertedVector);

								DrawDebugSphere(
									m_pPlayer->GetWorld(),
									convertedVector,
									100,
									32,
									FColor(255, 0, 0),
									true,
									10
								);
							}
#endif

							kickCalculated = true;
						}
					}
				}
			}

			if (kickCalculated)
			{
				m_pPlayer->GetRole()->StartActionKickToPosition(m_KickType, recommended_pos, account_for_wind);
				bWaypointHasAction = false;
			}
		}
	}
}

FVector RURoleSetplay::CalculateKickTarget(ARugbyCharacter * kickReceiver, float maxKickRange, float kickTime)
{

	//Find the receivers max speed
	float maxSpeed = kickReceiver->GetMovement()->GetMaxSpeed();

	//Calculate the target direction									
	FVector currentPos = kickReceiver->GetMovement()->GetCurrentPosition();
	FVector goalPos = kickReceiver->GetMovement()->GetTargetPosition();
	FVector targetDirection = goalPos - currentPos;

	//Clamp by the players max speed
	targetDirection = targetDirection.GetClampedToSize(maxSpeed, maxSpeed);

	//distance = initial velocity * time + 0.5 * acceleration * time^2
	//Assume acceleration is at max for now #MB
	FVector recommended_pos = currentPos + (targetDirection * kickTime);

	//Check if the recommended_pos is too far for the kicker to kick
	if ((recommended_pos - m_pPlayer->GetMovement()->GetCurrentPosition()).Magnitude() > maxKickRange)
	{
		//If it is too far, kick as far ahead of the runner as you can
		// C ^2 = A^2 + C^2 - 2ABCos(theta)
		/*where:
		C = maxKickRange
		A = distance_between_players
		theta = angle between target direction and vector between players

		B^2 -(B)2ACos(theta) + (A^2 - C^2) = 0
		solve for b with quadratic equation:
		a = 1
		b = -2ACos(theta)
		c = (A^2 - C^2)
		*/
		FVector vectorBetweenPlayers = (currentPos - m_pPlayer->GetMovement()->GetCurrentPosition());
		float distance_between_players = vectorBetweenPlayers.Magnitude();
		float costheta = targetDirection.Dot(vectorBetweenPlayers) / (targetDirection.Magnitude() * vectorBetweenPlayers.Magnitude());

		float a = 1;
		float b = 2 * distance_between_players * costheta;
		float c = FMath::Square(distance_between_players) - FMath::Square(maxKickRange);

		float distance_in_front = (-b + FMath::Sqrt(FMath::Square(b) - 4 * a * c)) / (2 * a);

		//Extend distance_in_front in the direction of the runner's movement.
		recommended_pos = currentPos + (targetDirection.Unit() * distance_in_front);
	}
	return recommended_pos;
}

void RURoleSetplay::UpdateCatchPassAction()
{
	if (m_pPlayer && m_pPlayer->GetActionManager())
	{
		//If player has not started passing start it
		if (!m_pPlayer->GetActionManager()->IsActionRunning(RU_ACTION_INDEX::ACTION_PASS_ANTICIPATION) &&
			m_pPlayer->GetActionManager()->CanUseAction(RU_ACTION_INDEX::ACTION_PASS_ANTICIPATION))
		{
			//Receive pass parameters 
			//ARugbyCharacter* passing_player, RUPassExtendInterface* pass_extend, float side, int receiver_index, int receiver_count, ANTICIPATION_MODE mode = AM_LOCKED_TO_PASSER 
			RUPassExtendInterface * pass_extend = nullptr;
			float side = m_pPlayer->GetAttributes()->GetTeamSide();
			int receiver_index = 0;
			int receiver_count = 1;
			
			//If the player has been notified by the passing player, start the action
			if (m_PassingPlayer != nullptr)
			{
				m_pPlayer->GetActionManager()->StartAction<RUActionPassAnticipation>(m_PassingPlayer, pass_extend, side, receiver_index, receiver_count, RUActionPassAnticipation::ANTICIPATION_MODE::AM_NONE);
				if (bIsDummy)
				{
					bWaypointHasAction = false;
					m_PassingPlayer = nullptr;
					bMovingToNextWaypoint = true;
				}
			}
		}

		//If the player catches the ball, start moving to the next waypoint
		if (m_pGame->GetGameState()->GetBallHolder() == m_pPlayer)
		{
			bMovingToNextWaypoint = true;
			SetplayManager->SetPlayerAtSyncPoint(m_pPlayer);
		}
	}
}

void RURoleSetplay::UpdateCatchKickAction(float dt)
{
	//Wait for ball to be kicked
	if (bBallWasKicked)
	{
		//if (bWillPlayerArriveFirst)
		{
			m_TimeToBall -= dt;
		}
		// 		else
		// 		{
		// 			m_TimeToBall = game->GetBall()->GetBestPickupPosition(this->player, m_CatchKickPosition, FVector::ZERO, bWillPlayerArriveFirst);
		// 		}

				wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
		

		//Make sure no other players try to catch the ball
		RUTeam * team = m_pPlayer->GetAttributes()->GetTeam();
		for (ARugbyCharacter * teamPlayer : team->GetPlayers())
		{
			if (teamPlayer->GetActionManager() &&
				teamPlayer->GetActionManager()->IsActionRunning(RU_ACTION_INDEX::ACTION_GETTHEBALL))
			{
				teamPlayer->GetActionManager()->GetAction<RUActionGetTheBall>()->SetCatchingPlayer(m_pPlayer);
			}
		}

		//Calculate the speed so the player runs onto the ball
		//float distance = (m_CatchKickPosition - currentPos).Magnitude();
		//player->GetMovement()->SetTargetSpeed((m_TimeToBall > 0) ? ((distance) / m_TimeToBall) : 1.0f);

		if (!m_pPlayer->GetActionManager()->IsActionRunning(RU_ACTION_INDEX::ACTION_GETTHEBALL) && !bKickCatchInProgress)
		{
			//player->GetMovement()->SetTargetPosition(m_CatchKickPosition);
			m_pPlayer->GetActionManager()->StartAction<RUActionGetTheBall>();
			//player->GetActionManager()->GetAction<RUActionGetTheBall>()->ForceAction(GTB_ACTION::HIGHBALL_JUMP);
			bKickCatchInProgress = true;
		}

		//#MB - start of the speeding up the catch code.
// 		if (RUActionGetTheBall * gtbAction = player->GetActionManager()->GetAction<RUActionGetTheBall>())
// 		{
// 			if(player->GetAnimation()->PlayContactAnimation)
// 			player->GetAnimInstance()->Montage_SetPlayRate(1.15f);
// 		}


		//Ending the setplay role conditions

		if (m_pGame && m_pGame->GetBall() && m_pGame->GetBall()->GetNumBounces() > 0)
		{
			//bWaypointHasAction = false;
		}

		//If the player jumps for the ball the action is complete
		if (m_pPlayer->GetActionManager()->GetAction<RUActionGetTheBall>() &&
			m_pPlayer->GetActionManager()->GetAction<RUActionGetTheBall>()->GetState() == GTB_STATE::GTB_DOING_COLLECT)
		{
			//bWaypointHasAction = false;
		}
			
	}
	else
	{
		//Make sure the runner isn't offside before the kick
		//FVector currentTargetPosition = player->GetMovement()->GetTargetPosition();
		//FVector newTargetPosition = currentTargetPosition;

		////Find the kicker
		//if (player->GetAttributes() && player->GetAttributes()->GetTeam())
		//{
		//	RUTeam * team = player->GetAttributes()->GetTeam();
		//	for (ARugbyCharacter * teamPlayer : team->GetPlayers())
		//	{
		//		if (RURoleSetplay * setplayRole = teamPlayer->GetRoleSafe<RURoleSetplay>())
		//		{
		//			if (setplayRole->GetActionState() == ERugbySetplayAction::KICKCHIP ||
		//				setplayRole->GetActionState() == ERugbySetplayAction::KICKPUNT)
		//			{
		//				newTargetPosition.z = teamPlayer->GetMovement()->GetCurrentPosition().z;
		//			}
		//		}
		//	}
		//}

		//player->GetMovement()->SetTargetPosition(newTargetPosition);
	}
}

void RURoleSetplay::UpdateKickGoalAction()
{
	if (m_pPlayer && m_pPlayer->GetActionManager())
	{
		//If player has not started passing start it
		if (!m_pPlayer->GetActionManager()->IsActionRunning(RU_ACTION_INDEX::ACTION_PREPARE_KICK) &&
			!m_pPlayer->GetActionManager()->IsActionRunning(RU_ACTION_INDEX::ACTION_KICK) &&
			m_pPlayer->GetActionManager()->CanUseAction(RU_ACTION_INDEX::ACTION_KICK))
		{
			//kick params
			//KickType p_KickType, const FVector &recommended_pos, bool account_for_wind
			KickType p_KickType = KickType::KICKTYPE_DROPGOAL; //#MB no idea what kind of kick it should use
			FVector recommended_pos = FVector::ZERO;
			bool account_for_wind = true;		//account for wind otherwise set play wont work.
			//bool kickCalculated = false;
			ARugbyCharacter * kickReceiver = nullptr;

			//Calculate kick position
			//First get the cente of the goal posts
			FieldExtents try_line_extents = m_pGame->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
			float teamSide = m_pPlayer->GetAttributes()->GetTeam()->GetLeft();			//Uses get left here because a teams goal is the opposite of their side
			FVector centreOfGoalPost = FVector(0, 0, try_line_extents.y * teamSide/2);
		
			//Then extrapolate that to max kick distance
			FVector fromPlayerToGoal = centreOfGoalPost - m_pPlayer->GetMovement()->GetCurrentPosition();
			float distance = fromPlayerToGoal.Magnitude();

			//#MB - todo: check if the player is close enough and maybe not allow the kick if they are too far.

			//Max kick distance for a drop goal kick is 50 meters so
			FVector scaledVector = fromPlayerToGoal.Unit() * 50;
			recommended_pos = m_pPlayer->GetMovement()->GetCurrentPosition() + scaledVector;

#ifdef SETPLAYDEBUG
			if (CVarSetPlayDebug.GetValueOnGameThread() > 0)
			{
				FVector lineStart = m_pPlayer->GetMovement()->GetCurrentPosition();
				FVector lineEnd = recommended_pos;

				MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(lineStart, lineStartConverted);
				MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(lineEnd, lineEndConverted);

				DrawDebugLine(
					m_pPlayer->GetWorld(),
					lineStartConverted,
					lineEndConverted,
					FColor(0, 0, 255),
					false,
					5,
					10,
					10
				);
			}
#endif


			m_pPlayer->GetRole()->StartActionKickToPosition(p_KickType, recommended_pos, account_for_wind);
			bWaypointHasAction = false;
		}
	}
}

//kicker, kick_type, current ball position
void RURoleSetplay::UpdateCatchKickTargetPosition(ARugbyCharacter* player, KickContext context, KickType kick_type, const FVector& currentBallPosition)
{
	if (m_pGame &&
		m_pGame->GetEvents())
	{
		m_pGame->GetEvents()->kick.Remove(this, &RURoleSetplay::UpdateCatchKickTargetPosition);
	}

	bBallWasKicked = true;

	//Calculate where to catch the ball
	m_TimeToBall = m_pGame->GetBall()->GetBestPickupPosition(this->m_pPlayer, m_CatchKickPosition, FVector::ZERO, bWillPlayerArriveFirst);

}

void RURoleSetplay::SetFocusToRunner(ARugbyCharacter* player, KickContext context, KickType kick_type, const FVector& currentBallPosition)
{
	if (SetplayManager)
	{
		if (ARugbyCharacter * targettedPlayer = SetplayManager->GetSetplayTargettedCharacter())
		{
			if (UOBJ_IS_VALID(targettedPlayer))
			{
				if (SSHumanPlayer * human = player->GetHumanPlayer())
				{
					human->SetRugbyCharacter(targettedPlayer);

					SetplayManager->SetGTBOverridePlayer(targettedPlayer);

					if (RURoleSetplay * setplayRole = targettedPlayer->GetRoleSafe<RURoleSetplay>())
					{
						setplayRole->SetLockPlayerControl(false);
						setplayRole->SetWaitingForPlayerToOverrideMovement(true);
					}

					float speed = targettedPlayer->GetMovement()->GetMaxSpeed() * SETPLAY_RUNNER_SPEED_MULTIPLIER;

					targettedPlayer->GetMovement()->OverrideMaxSpeed(speed);
					targettedPlayer->GetMovement()->OverrideMaxAcceleration(SETPLAY_RUNNER_SPEED_MULTIPLIER);

					SetplayManager->SetPlayerAtSyncPoint(targettedPlayer);
					//SetplayManager->UpdatePlayerSetplayStatus(targettedPlayer);
					//SetplayManager->RemovePlayerFromSetplay(targettedPlayer);
				}
			}
		}
	}
	bWaypointHasAction = false;

	if (m_pGame)
	{
		m_pGame->GetEvents()->kick.Remove(this, &RURoleSetplay::SetFocusToRunner);
	}
	else if (SIFApplication::GetApplication()->GetActiveGameWorld())
	{
		m_pGame = SIFApplication::GetApplication()->GetActiveGameWorld();
		SIFApplication::GetApplication()->GetActiveGameWorld()->GetEvents()->kick.Remove(this, &RURoleSetplay::SetFocusToRunner);
	}
}

void RURoleSetplay::UpdateDecisionAction()
{
	if (SetplayManager)
	{
		//Check if a decision has been made
		ERugbySetplayAction selectedAction = ERugbySetplayAction::NOTHING;
		ARugbyCharacter * targettedPlayer = SetplayManager->GetSetplayTargettedCharacter();

		//If no decision is made by the time the player is in the decision state and has the ball, abort the setplay.
		if (!targettedPlayer)
		{
			if (m_pGame && m_pGame->GetGameState())
			{
				if (m_pGame->GetGameState()->GetBallHolder() == m_pPlayer)
				{
					SetplayManager->AbortSetplay();
				}
			}
		}
		else
		{
			if (FormationManager)
			{
				if (SSRoleArea * tempArea = const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(targettedPlayer)))
				{
					if (tempArea->get_zone())
					{
						selectedAction = tempArea->get_zone()->targettedAction;
					}
					else
					{
						MABASSERTMSG(tempArea->get_zone(), "Targetted player is not in a zone");
					}

					//Apply the targetted players break tackle boost
					targettedPlayer->GetAttributes()->SetBreakTackleBoost(tempArea->get_break_tackle_boost());
					
				}
				else
				{
					MABASSERTMSG(const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(targettedPlayer)), "Targetted player has no area assigned");
					//something must of gone wrong, abort the set play
					targettedPlayer = nullptr;

					SetplayManager->AbortSetplay();

					return;
				}
			}

			

			//If this player is the target, enable control of the player
			if (targettedPlayer == m_pPlayer)
			{
				if (selectedAction != ERugbySetplayAction::KICKGOAL)		//Goal kicks target themselves.
				{
					for (int i = 0; i < HF_LAST; i++)
					{
						if (m_pPlayer->GetActionManager()->HFIsLocked(i))
						{
							m_pPlayer->GetActionManager()->HFUnlock(i);
						}
					}			
					bLockPlayerControl = false;
					bWaypointHasAction = false;
					return;
				} 
			}

			bool kickCalculated = false;
			KickType kickType = KickType::KICKTYPE_SETPLAYCHIPKICK;
			PASS_TYPE type = PASS_TYPE::PT_STANDARD;
			RLROLE_INTERCEPT_TYPE intercept_override = RLROLE_INTERCEPT_TYPE::RLRIT_FROM_SETPLAY;
			FVector calculatedKickPosition = FVector::ZERO;
			SIFRugbyCharacterList playerList = m_pPlayer->GetAttributes()->GetTeam()->GetPlayers();
			ARugbyCharacter * middleman = nullptr;
			if (m_TargettedStateOverride != ERugbySetplayAction::NOTHING)
				selectedAction = m_TargettedStateOverride;
			switch (selectedAction)
			{
				//Kick to a player - This should start a kick for the player roughly in the direction of 
				//the receiving player or goals, but let them control it.
			case ERugbySetplayAction::KICKGOAL:
				if (!kickCalculated)
				{
					kickType = KickType::KICKTYPE_DROPGOAL;
					bool account_for_wind = true;		//account for wind otherwise set play wont work.

					//Calculate kick position
					//First get the cente of the goal posts
					FieldExtents try_line_extents = m_pGame->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
					float teamSide = m_pPlayer->GetAttributes()->GetTeam()->GetLeft();			//Uses get left here because a teams goal is the opposite of their side
					FVector centreOfGoalPost = FVector(0, 0, try_line_extents.y * teamSide / 2);

					//Then extrapolate that to max kick distance
					FVector fromPlayerToGoal = centreOfGoalPost - m_pPlayer->GetMovement()->GetCurrentPosition();
					float distance = fromPlayerToGoal.Magnitude();

					//Max kick distance for a drop goal kick is 50 meters so
					FVector scaledVector = fromPlayerToGoal.Unit() * 50;
					calculatedKickPosition = m_pPlayer->GetMovement()->GetCurrentPosition() + scaledVector;

					kickCalculated = true;
				} //intentional
			case ERugbySetplayAction::KICKCHIP:
				if (!kickCalculated)
				{
					calculatedKickPosition = CalculateKickTarget(targettedPlayer, MAX_SETPLAY_CHIP_KICK_DIST, 1.0f);
					kickType = KickType::KICKTYPE_SETPLAYCHIPKICK;
					kickCalculated = true;
				} //intentional
			case ERugbySetplayAction::KICKPUNT:
				if (!kickCalculated)
				{
					calculatedKickPosition = CalculateKickTarget(targettedPlayer, MAX_LONG_PUNT_DIST, 1.5f);
					kickType = KickType::KICKTYPE_LONGPUNT;
					kickCalculated = true;
				}

				//All kicks are initiated here		
				if (!bKickInProgress)
				{
					if (m_pPlayer->GetRole()->CanExecuteActionNow(RU_ACTION_INDEX::ACTION_KICK) && !m_pPlayer->GetRole()->IsActionRunning(RU_ACTION_INDEX::ACTION_KICK))
					{
						if (RUInputKickInterface* kick_interface = m_pGame->GetInputManager()->GetKickInterface())
						{
							m_pPlayer->GetActionManager()->HFUnlock(HF_KICK);
							kick_interface->SetKickPosition(calculatedKickPosition);
							kick_interface->StartKick(m_pPlayer, kickType, false, true);
							bKickInProgress = true;

							if (kickType != KickType::KICKTYPE_DROPGOAL)
							{
								m_pGame->GetEvents()->kick.Add(this, &RURoleSetplay::SetFocusToRunner);
							}
							else
							{
								bWaypointHasAction = false;
							}
						}
					}
				}
				break;
			case ERugbySetplayAction::PASSVIA:
				//Find the middleman 
				for (ARugbyCharacter * character : playerList)
				{
					if (FormationManager)
					{
						if (SSRoleArea * tempArea = const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(character)))
						{
							if (tempArea->get_zone())
							{
								if (tempArea->get_zone()->targettedAction == ERugbySetplayAction::PASS)
								{
									middleman = character;
								}
							}
						}
					}
				}
				if (m_pPlayer->GetRole()->CanExecuteActionNow(RU_ACTION_INDEX::ACTION_PASS) && !m_pPlayer->GetRole()->IsActionRunning(RU_ACTION_INDEX::ACTION_PASS) && middleman)
				{
					m_pPlayer->GetActionManager()->StartAction<RUActionPass>(middleman, type, intercept_override);
					if(middleman->GetRoleSafe<RURoleSetplay>())
						middleman->GetRoleSafe<RURoleSetplay>()->SetTargettedStateOverride(ERugbySetplayAction::PASS);
					bWaypointHasAction = false;
				}
				break;

			case ERugbySetplayAction::PASS:
				if (m_pPlayer->GetRole()->IsActionRunning(RU_ACTION_INDEX::ACTION_PASS))
				{
					m_pPlayer->GetActionManager()->GetAction(ACTION_PASS)->Exit();
				}
				if (m_pPlayer->GetRole()->CanExecuteActionNow(RU_ACTION_INDEX::ACTION_PASS) && !m_pPlayer->GetRole()->IsActionRunning(RU_ACTION_INDEX::ACTION_PASS) &&
					SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetBallHolder() == m_pPlayer)
				{
					m_pPlayer->GetActionManager()->StartAction<RUActionPass>(targettedPlayer, type, intercept_override);
					targettedPlayer->GetActionManager()->HFUnlockAll();
					if(targettedPlayer->GetRoleSafe<RURoleSetplay>())
						targettedPlayer->GetRoleSafe<RURoleSetplay>()->SetLockPlayerControl(false);
					bWaypointHasAction = false;
				}
				break;
			
			default:
				return;
			}
		}

		
	}

}

void RURoleSetplay::UpdateCutScene(const MabTimeStep & game_time_step)
{ 
}

bool RURoleSetplay::IsPlayerAvailable(const ARugbyCharacter * player)
{
	if (!player)
		return false;

	////Cannot be in the set play if already in the ruck
	//if (player->GetRole() && player->GetRole()->RTTGetStaticType() == RURoleRuck::RTTGetStaticType())
	//{
	//	return -9999;
	//}

	// If we are a tacklee or tackler then we cannot either
	if (!player->GetActionManager() ||
		player->GetActionManager()->IsActionRunning(ACTION_TACKLEE) ||
		player->GetActionManager()->IsActionRunning(ACTION_TACKLER))
		return false;

	// Can't be in the setplay if the player can't move
	if (player->GetActionManager()->UFIsLocked(UF_DOMOTION))
		return false;

	//Can't be in the setplay if their current role is not interruptable
	if (player->GetRole() && !player->GetRole()->IsInterruptable())
		return false;

	//Can't be in the setplay if in the ruck
	RURoleRuck* ruck_role = MabCast<RURoleRuck>(player->GetRole());
	if (ruck_role && ruck_role->GetState() >= RURoleRuck::RoleState::CONTESTING_BALL_GETUP && ruck_role->GetState() <= RURoleRuck::RoleState::RECOVERY)
	{
		return false;
	}

	return true;
}

int RURoleSetplay::GetFitness(const ARugbyCharacter * player, const SSRoleArea * area)
{
	if (!IsPlayerAvailable(player))
		return -9999;

	//Calculate weights from the setplay weights in the json file
	if (area)
	{
		float totalWeight = 0;
		TArray<FSerialiseSetplayFitnessWeight> weights = const_cast<SSRoleArea*>(area)->get_zone()->setplayFitnessWeights;

		bool positionSpecific = false;

		//Iterate through all weights
		for (FSerialiseSetplayFitnessWeight weight : weights)
		{
			//Position - Always prioritise this over anything else if possible.
			if ((int)weight.category >= (int)ERugbySetplayFitnessCategories::LEFTWING)
			{
				positionSpecific = true;

				PLAYER_POSITION expectedPosition = PLAYER_POSITION::PP_NONE;
				SSRoleArea * p_area = const_cast<SSRoleArea*>(area);
				bool swapWings = false;
				if (p_area && p_area->GetOrigin() && player && player->GetAttributes())
				{
					swapWings = (p_area->GetOrigin()->GetOrigin().x * (int)player->GetAttributes()->GetPlayDirection()) < 0.0f;
				}

				switch (weight.category)
				{
				case ERugbySetplayFitnessCategories::LEFTWING:
					if (swapWings)
					{
						expectedPosition = PP_RIGHTWING;
					}
					else
					{
						expectedPosition = PP_LEFTWING;
					}
					break;
				case ERugbySetplayFitnessCategories::RIGHTWING:
					if (swapWings)
					{
						expectedPosition = PP_LEFTWING;
					}
					else
					{
						expectedPosition = PP_RIGHTWING;
					}
					break;
				case ERugbySetplayFitnessCategories::OUTSIDECENTER:
					expectedPosition = PP_OUTSIDE_CENTER_RIGHTCENTRE;
					break;
				case ERugbySetplayFitnessCategories::INSIDECENTER:
					expectedPosition = PP_INSIDE_CENTER_LEFTCENTRE;
					break;
				case ERugbySetplayFitnessCategories::FULLBACK:
					expectedPosition = PP_FULLBACK;
					break;
				case ERugbySetplayFitnessCategories::SCRUMHALF:
					expectedPosition = PP_SCRUM_HALF;
					break;
				case ERugbySetplayFitnessCategories::FLYHALF:
					expectedPosition = PP_FLY_HALF_STAND_OFF;
					break;
				}

				PLAYER_POSITION playerPosition = player->GetAttributes()->GetPlayerPosition();

				ARugbyCharacter * correctPlayer = player->GetAttributes()->GetTeam()->GetPlayerByPosition(expectedPosition);

				if (playerPosition == expectedPosition)
				{
					totalWeight += 10000.0f;
				}
				else if (!IsPlayerAvailable(correctPlayer))
				{
					//Add a tiny distance weight on to ensure roles get filled if the expect player is busy.
					if (player->GetAttributes())
					{
						float distance_from_point = (area->GetZonePosition((float)player->GetAttributes()->GetTeam()->GetRight()) - player->GetMovement()->GetCurrentPosition()).Magnitude();
						totalWeight += (distance_from_point / player->GetGameWorld()->GetSpatialHelper()->GetFieldExtents().x) * weight.weight * 100;
					}
				}
			}

			/*if (positionSpecific && player && player->GetAttributes())
			{
				if((player->GetAttributes()->GetPlayerPosition() & PLAYER_POSITION::PP_BACK) != 0)
					continue;
			}*/

			//Distance weighting
			//This adds a weighting based on the distance between the player and the point as a ratio to the width of the field
			if (weight.category == ERugbySetplayFitnessCategories::DISTANCE)
			{
				if (player->GetAttributes())
				{
					float distance_from_point = (area->GetZonePosition((float)player->GetAttributes()->GetTeam()->GetRight()) - player->GetMovement()->GetCurrentPosition()).Magnitude();

#ifdef SETPLAYDEBUG
					if (CVarSetPlayDebug.GetValueOnGameThread() > 0)
					{
						/*FVector lineStart = player->GetMovement()->GetCurrentPosition();
						FVector lineEnd = area->GetZonePosition((float)player->GetAttributes()->GetTeam()->GetRight());

						MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(lineStart, lineStartConverted);
						MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(lineEnd, lineEndConverted);

						DrawDebugLine(
							player->GetWorld(),
							lineStartConverted,
							lineEndConverted,
							FColor(255, 0, 255),
							false,
							5,
							10,
							10
						);*/
					}
#endif

					totalWeight += (distance_from_point / player->GetGameWorld()->GetSpatialHelper()->GetFieldExtents().x) * weight.weight * 100;
				}
			}

			//Kick accuracy weighting
			else if (weight.category == ERugbySetplayFitnessCategories::KICKING)
			{
				if (player->GetAttributes())
				{
					float playerKickAccuracy = player->GetAttributes()->GetGoalKickAccuracy();

					totalWeight += playerKickAccuracy * weight.weight * 100;
				}
			}

			//Run speed weighting
			else if (weight.category == ERugbySetplayFitnessCategories::SPEED)
			{
				if (player->GetAttributes())
				{
					float playerSpeed = player->GetAttributes()->GetSpeed();

					totalWeight += playerSpeed * weight.weight * 100;
				}
			}

			//Stamina weighting
			else if (weight.category == ERugbySetplayFitnessCategories::STAMINA)
			{
				if (player->GetAttributes())
				{
					float playerStamina = player->GetAttributes()->GetStamina();

					totalWeight += playerStamina * weight.weight;
				}
			}
		}
		
		return totalWeight;
	}
	return 0;
}

bool RURoleSetplay::IsInterruptable() const
{
	if (SetplayManager)
	{
		return !SetplayManager->isPlayerRunningSetplay(m_pPlayer);
	}
	return false;
}

void RURoleSetplay::SetPassingPlayer(ARugbyCharacter * passingPlayer)
{
	m_PassingPlayer = passingPlayer;
}

bool RURoleSetplay::isActionInProgress()
{
	if (!bWaypointHasAction)
	{
		if (!FormationManager)
		{
			return false;
		}

		//Update the player action state
		if (const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(m_pPlayer)))
		{
			if (!const_cast<SSRoleArea*>(FormationManager->GetPlayerArea(m_pPlayer))->get_zone()->actionList.IsValidIndex(m_TargetWaypointIndex))
			{
				return false;
			}
			else
			{
				return true;
			}
		}
	}
	return true;
}

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
//===============================================================================
//===============================================================================
void RURoleSetplay::ShowDebug()
{
	if (m_pPlayer)
	{
		static const FVector textOffset(0.0f, 100.0f, 0.0f);
		ENUM_TO_FSTRING(ERugbySetplayAction, m_ActionState);
		FString debugString = FString::Printf
		(
			TEXT("Current Waypoint: %d \nReached Waypoint: %s \nWaypoint Action: %s"),
			m_TargetWaypointIndex + 1,
			m_pPlayer->GetMovement()->HasReachedWaypoint() ? TEXT("TRUE") : TEXT("FALSE"),
			*ENUM_TO_FSTRING(ERugbySetplayAction, m_ActionState)
		);
		DrawDebugString(m_pPlayer->GetWorld(), textOffset, debugString, m_pPlayer, FColor::White, 0.0f);
	}
}
#endif