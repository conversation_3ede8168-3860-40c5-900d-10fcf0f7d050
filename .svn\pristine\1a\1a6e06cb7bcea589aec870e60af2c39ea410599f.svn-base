/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef SS_ROLEREFEREE_H
#define SS_ROLEREFEREE_H

#include "Mab/Time/MabTimer.h"
#include "Match/RugbyUnion/Enums/RULineoutEnum.h"
#include "Match/RugbyUnion/Enums/RUPenaltyDecisionEnum.h"
#include "Match/SSRole.h"

class RUTeam;

class SSRoleReferee : public SSRole
{
	MABRUNTIMETYPE_HEADER( SSRoleReferee );

public:
	SSRoleReferee( SIFGameWorld* game );

	/// Enter this role with the specified player.
	void Enter(ARugbyCharacter* player) override;

	/// Exit this role.
	void Exit(bool forced) override;

	/// Advance this role.
	void UpdateLogic(const MabTimeStep& game_time_step) override;

	/// Advance this role in cutscene.
	virtual void UpdateCutScene( const MabTimeStep& game_time_step );

	void DoFacing();
	/// Get the fitness of the player for the given behaviour
	static int GetFitness(const ARugbyCharacter* player, const SSRoleArea* area);

	const char* GetShortClassName() const override { return "Ref"; }

	void WarpToWaypoint() override;
	//Brian hack
	static void ref_tackle_count(int tackle_number);
	static void tackle_held();
	static void tackle_high();
private:
	/// Event handlers
	void forward_pass(ARugbyCharacter*, ARugbyCharacter*);
	void free_kick_awarded(ARugbyCharacter*);
	void full_time();
	void first_half_start();
	void second_half_start();
	void knock_on( ARugbyCharacter*, bool, const FVector& );
	void knock_on_awarded();
	void advantage_over(ARugbyCharacter* offending_player);
	void advantage_started(ARugbyCharacter* offending_player);
	void commentary_interchange_started();
	void try_awarded();
	void lineout_faulty_throw_awarded( ARugbyCharacter*, const FVector& );
	void blow_penalty_whistle( PENALTY_REASON reason );
	void rule_trigger_ball_out( ARugbyCharacter*, const FVector&, const FVector&, bool, bool );
	void offside( ARugbyCharacter* );
	void breakdown_holding_on_illegal( ARugbyCharacter*, ARugbyCharacter* );
	void breakdown_should_release( ARugbyCharacter*, ARugbyCharacter* );
	void ball_dead_detected( ARugbyCharacter*, const FVector&, bool );
	void perform_interchange( int, RUTeam* );


	/// The delay between the ball being knocked on and the ref calling for advantage.
	MabTimer advantage_delay;
	//hack bools
	bool m_hasPlayedDeadBallWhistle = false;
};

#endif
