// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIPopulatorLineupReserves.h"
#include "Rugby/Utility/Helpers/SIFGameHelpers.h"
#include "CoreMinimal.h"
#include "WWUIScreenTemplate.h"
#include "PanelWidget.h"
#include "WidgetTree.h"
#include "WWUIFunctionLibrary.h"

void UWWUIPopulatorLineupReserves::Populate(UWidget* widget)
{
	if (dataList.ArrayOption.Num() > 0)
	{
		preConstructOwner = widget;

		Clear(widget);

		DataFileCreationNodeCallbackLineupReserves callbackObject(widget, dataList.ArrayOption);
		CreateNodesFromTemplate(dataList.TemplateName, dataList.ArrayOption.Num(), &callbackObject);

		if (ScreenRef)
		{
			ScreenRef->StoreChildWidgets();
		}
	}
}


UWWUIPopulatorLineupReserves::DataFileCreationNodeCallbackLineupReserves::DataFileCreationNodeCallbackLineupReserves(UWidget* containerToPopulate, TArray<FWWUIScreenTemplateDataOption>& inDataOptions) :
	container(),
	dataOptions(inDataOptions)
{
	container = Cast<UPanelWidget>(containerToPopulate);

	if (!container)
	{
		FString errorString = containerToPopulate != nullptr ? *containerToPopulate->GetPathName() : FString("NULL");
		UE_LOG(LogTemp, Error, TEXT("Cast to scroll box failed while attempting DataFileCreationNodeCallback on node %s"), *errorString);
	}
}

void UWWUIPopulatorLineupReserves::DataFileCreationNodeCallbackLineupReserves::Callback(UUserWidget* widget)
{
	if (widget && container)
	{
		unsigned int currentChild = container->GetChildrenCount();
		int previousPlayers = 13; // Nick  WWS 7s to Womens // SIFGameHelpers::GAGetGameMode() == 1 ? 7 : 13; //Nick GGS chnage from 15 to 13

		UTextBlock* tBlock = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(widget, FString("Name")));
		UWWUIFunctionLibrary::SetText(tBlock, dataOptions[currentChild].Title);

		tBlock = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(widget, FString("Number")));
		UWWUIFunctionLibrary::SetText(tBlock, FString::FromInt(currentChild + previousPlayers + 1));

		//< Hides divider in last child >
		if (dataOptions.Num() == currentChild)
		{
			UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindChildWidget(widget, FString("Divider")), ESlateVisibility::Hidden);
		}

		container->AddChild(widget);
	}
}