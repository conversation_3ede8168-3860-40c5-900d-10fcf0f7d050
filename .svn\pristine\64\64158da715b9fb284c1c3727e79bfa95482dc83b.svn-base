/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseScrum.h"

#include "Mab/MabRandDistributions.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/Roles/Competitors/RURoleStandardBallHolder.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleScrum.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleScrumHalfBack.h"
#include "Match/AI/Roles/Officials/SSRoleReferee.h"
#include "Match/Ball/SSBall.h"
#include "Match/Camera/SSCameraManager.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/RUPlayerSound.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Character/RugbyPlayerController.h"
#include "Match/HUD/RU3DHUDManager.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameMovement.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSGameTimer.h"
#include "Match/SSMath.h"
#include "Match/SSRoleFactory.h"
#include "Match/SSSpatialHelper.h"
#include "Utility/RURandomNumberGenerator.h"

//#rc3_legacy_include #include "MabUITextInterface.h"
//#rc3_legacy_include #include "NMMabAnimationEvents.h"
//#rc3_legacy_include #include "NMMabAnimationNetwork.h"
//#rc3_legacy_include #include "RUContextualHelper.h"
#include "Networking/RUNetworkState.h"
//#rc3_legacy_include #include "RUScrumHUD.h"
//#rc3_legacy_include #include "RUSetPieceIndicator.h"
//#rc3_legacy_include #include "SIFDebug.h"
//#rc3_legacy_include #include "SIFRumbleHelpers.h"
//#rc3_legacy_include #include "SIFUIEffectsHelpers.h"
//#rc3_legacy_include #include "SIFUIHelpers.h"
#include "Match/HUD/RU3DHUDManager.h"
#include "Match/HUD/Marking/RUSetPieceIndicator.h"
#include "Character/RugbyPlayerController.h"
#include "Character/RugbyCharacter.h"
#include "RugbyGameInstance.h"

#define FULLBACK_MODE_TOGGLE_PERCENT 35.0

#ifdef ENABLE_OSD
#include "Match/Debug/RUGameDebugSettings.h"
#endif
#include "Utility/Helpers/SIFRumbleHelpers.h"
#include "Match/SSTeam.h"
#include "Match/AI/SetPlays/SSSetPlayManager.h"
#include "Utility/Helpers/SIFGameHelpers.h"

#if (PLATFORM_WINDOWS) || (PLATFORM_XBOX360)
//#rc3_legacy_include #include <MabXInputGameController.h>
#endif

MABRUNTIMETYPE_IMP1( RUGamePhaseScrum, RUGamePhaseHandler );

// Consts
const float SCRUM_PUSH_FREQUENCY = 1.4444f; // based on anim

static const size_t MAX_PLAYERS_PER_SIDE_IN_SCRUM = 8;

static const float PUSHING_PERIOD = 1.0f;
static const float PUSHING_EVENT_TRIGGER_PERIOD = 1.0f;
static const float ENGAGE_WAITING_DECAY = 0.025f;	//< Rate of decay after engage is called but before the player reacts
static const int COLLAPSES_BEFORE_PENALTY = 3;
static const float MAX_ACCURACY_HISTORY_TIME = 5.0f;	/// In seconds

// Scrum helper strings
//static const char* SCRUMTEXT_PRE_ENGAGE = "[ID_SCRUM_HELP_PRE_ENGAGE]";
//static const char* SCRUMTEXT_ENGAGE = "[ID_SCRUM_HELP_ENGAGE]";
static const char* SCRUMTEXT_PUSHING = "[ID_SCRUM_HELP_PUSHING]";

// Marker size constants
static const float RED_SEGMENT_LENGTH	= 0.075f;
static const float GREEN_SEGMENT_LENGTH = 0.05f;	//< Scaled by team scrum ability

// Marker consts
static const float MARKER_DECAY = 0.50f;
static const float MARKER_SCALE = 0.75f;

static const float ROTATION_DECAY = 1.4f;
static const float ROTATION_SCALE = 3.0f;
static const float ANGLE_CHANGE_RATE = 0.07f;

//DEBUG
//static const float MARKER_DECAY = 0.0f;
//static const float MARKER_SCALE = 0.001f * 30.0f;

static const char* SCRUM_BIND_ANIM_NAMES[MAX_PLAYERS_PER_SIDE_IN_SCRUM] =
{
	"scmbind01_no1",
	"scmbind01_no2",
	"scmbind01_no3",
	"scmbind01_no4",
	"scmbind01_no5",
	"scmbind01_no6",
	"scmbind01_no7",
	"scmbind01_no8"
};

///-------------------------------------------------------------------------
/// Constructor
///-------------------------------------------------------------------------

RUGamePhaseScrum::RUGamePhaseScrum(SIFGameWorld *ggame)
: game(ggame)
, setplay_time(0.0f)
, setplay_state(SP_STATE_NONE)
, feeding_team(NULL)
, scrum_states()
, curr_state(0)
, engagement_pause_type(0)
, engagement_state_transition_time(0.0f)
, engagement_failed( false )
, engagement_state( ENG_STATE_PRE )
, reset_scrum_state( false )
, shunt_applied( false )
, scrum_coll_centre( FVector::ZeroVector )
, scrum_coll_width( 0.0f)
, scrum_coll_height( 0.0f )
, is_ready(false)
, n_debug_text(0)
, n_debug_lines(0)
{
	// Construct scrum states for each team
	scrum_states.reserve( MAX_CONCURRENT_SCRUMS );
	for( size_t i = 0; i < MAX_CONCURRENT_SCRUMS; i++ )
	{
		scrum_states.push_back(std::make_unique<RUScrumState>());
		scrum_states_ptr.push_back(scrum_states.back().get());
		//#rc3_legacy scrum_states.push_back( game->GetObjectDatabase()->Allocate<RUScrumState>() );
		scrum_states.back()->scrum = this;
		scrum_states.back()->attacking.scrum = this;
		scrum_states.back()->defending.scrum = this;
	}

	// listen out for team change events, so we can adjust whos controlling what
	game->GetEvents()->team_assignments_changed.Add(this, &RUGamePhaseScrum::OnTeamAssignmentsChanged);
}

///-------------------------------------------------------------------------
/// Destructor
///-------------------------------------------------------------------------

RUGamePhaseScrum::~RUGamePhaseScrum()
{
	game->GetEvents()->team_assignments_changed.Remove(this, &RUGamePhaseScrum::OnTeamAssignmentsChanged);

	//#rc3_legacy
	//for( size_t i = 0; i < MAX_CONCURRENT_SCRUMS; i++ )
	//	game->GetObjectDatabase()->Destroy<RUScrumState>( scrum_states[i] );

	game->GetGameState()->LockHumanMovement( false );
}

///-------------------------------------------------------------------------
/// Have just changed to this phase
///-------------------------------------------------------------------------

RUScrumTeamState& RUGamePhaseScrum::GetCurrentAttackingTeamState()
{
	MABASSERT( curr_state >= 0 && curr_state < (int) MAX_CONCURRENT_SCRUMS );
	return scrum_states[curr_state]->attacking;
}

RUScrumTeamState& RUGamePhaseScrum::GetCurrentDefendingTeamState()
{
	MABASSERT( curr_state >= 0 && curr_state < (int) MAX_CONCURRENT_SCRUMS );
	return scrum_states[curr_state]->defending;
}

void RUGamePhaseScrum::StartNewScrum()
{
	/// Find the next available scrum state entry
	int new_state = -1;
	int state_idx = curr_state;
	for( size_t i = 0; i < scrum_states.size(); i++, state_idx = (state_idx+1) % scrum_states.size() )
	{
		if ( !scrum_states[i]->running ) {
			new_state = (int)i;
			break;
		}
	}

	MABASSERT( new_state >= 0 );
	MABLOGDEBUG( MabString( 32, "Starting new scrum %d to %d", curr_state, new_state  ).c_str() );
	if(new_state >= 0)
	{
		scrum_states[new_state]->Enter();
		curr_state = new_state;
	}
}

void RUGamePhaseScrum::Enter()
{
	is_ready = false;

	// TYRONE : In some circumstances this is not unset (advantage ended by a try being scored but disallowed) so resetting forcibly
	// in stoppages
	game->GetBall()->ResetTryCheck();

	game->GetGameState()->ResetPhases( false );

	SET_CHANGEPLAYER_SECTION( game, "SCRUMRESET" );
	game->GetTeam(0)->UnassignHumanPlayers();
	game->GetTeam(1)->UnassignHumanPlayers();
	SET_CHANGEPLAYER_SECTION( game, NULL );

	// Only clear on a first entry
	if ( !reset_scrum_state )
		game->GetStrategyHelper()->ClearRoles();

	// WJS RLC TODO CHECK IF SETTING THIS TO false IS RIGHT Re above test
	// Clear the reset flag
	reset_scrum_state = false;

	StartNewScrum();

	RUScrumTeamState& attacking_team = GetCurrentAttackingTeamState();
	RUScrumTeamState& defending_team = GetCurrentDefendingTeamState();

	// Clear relevant data
	attacking_team.Reset();
	defending_team.Reset();

	// Default setplay state
	TransitionToState(SP_STATE_INITIAL);

	// Give to attacking team 100% of the time. Defending team can then win in contest.
	feeding_team = game->GetGameState()->GetAttackingTeam();

	// Init teams
	attacking_team.team = game->GetGameState()->GetAttackingTeam();
	defending_team.team = game->GetGameState()->GetDefendingTeam();
	attacking_team.scrum = this;
	defending_team.scrum = this;

	// Init the scrum configuration
	ApplyDefaultScrumConfiguration( GetCurrentScrum(), attacking_team, true );
	ApplyDefaultScrumConfiguration( GetCurrentScrum(), defending_team, false );

	// Initialise scrum collidable variables
	scrum_coll_centre = FVector::ZeroVector;
	scrum_coll_width = scrum_coll_height = 0.0f;

	// give the feeding team a velocity benefit (depending on their abilities)
	const static float MAX_FEEDING_TEAM_VELOCITY_BOOST = 0.6f;
	const static float MIN_FEEDING_TEAM_VELOCITY_BOOST = 0.15f;
	GetCurrentScrum().scrum_shunt = MabMath::Lerp(MIN_FEEDING_TEAM_VELOCITY_BOOST, MAX_FEEDING_TEAM_VELOCITY_BOOST, attacking_team.GetScrumAbility());

	shunt_applied = false;

	game->GetGameState()->SetFormationTarget( ERugbyFormationTarget::RUCK_CENTER,	GetCurrentScrum().GetScrumOrigin() );


	game->GetGameState()->LockHumanMovement( true );

	//Offensive team sometimes will bring the fullback into line.
	float rand = game->GetRNG()->RAND_RANGED_CALL(float, 100);
	if (rand <= FULLBACK_MODE_TOGGLE_PERCENT)
	{
		RUTeam * offensive_team = game->GetGameState()->GetAttackingTeam();
		if (offensive_team)
		{
			offensive_team->GetStrategy().CycleFullbackOption();
		}
	}

	// DEV NOCHECKIN
	//game->GetGameState()->SetAttackingTeam( game->GetGameState()->GetDefendingTeam() );
}

RUScrumState& RUGamePhaseScrum::GetCurrentScrum()
{
	return *scrum_states[ curr_state ];
}

void RUGamePhaseScrum::UpdateStateInitial()
{
	if (game->GetHUDUpdater())
	{
		game->GetHUDUpdater()->SetScrumHelperText();
		game->GetHUDUpdater()->SetScrumHUDVisible(false);
	}

	// causes camera swing to kick in faster
	game->GetGameState()->SetAttackingTeam( feeding_team );

	/// We don't want our formation to change during the scrums so lock it in
	game->GetGameState()->GetAttackingTeam()->GetFormationManager()->DisableFormationChangeDuringPhase();
	game->GetGameState()->GetDefendingTeam()->GetFormationManager()->DisableFormationChangeDuringPhase();

	/// Put all players in correct formation - work out which side of the field we are on for open and blind side assignment
	RUScrumTeamState *team_scrum_states [2] = { &GetCurrentAttackingTeamState(), &GetCurrentDefendingTeamState() };
	RUZonePosition* returned_entry;
	ARugbyCharacter* returned_player;

	for( int i = 0; i < 2; i++ )
	{
		RUScrumTeamState* scrum_state = team_scrum_states[i];

		SIFRugbyCharacterList halfback_players;

		/// Assign halfback
		game->GetStrategyHelper()->GetTeamRoles( scrum_state->team, RURoleScrumHalfBack::RTTGetStaticType(), halfback_players );
		if (halfback_players.empty())
		{
			MABASSERTMSG(!halfback_players.empty(), "No halfback players found");
			return;
		}

		MABASSERT( halfback_players.size() == 1 );
		MABASSERT( scrum_state->locations[ SCRUMLOC_HALFBACK ].positions.size() == 1 );
		returned_entry = &scrum_state->locations[ SCRUMLOC_HALFBACK ].positions[0];
		returned_player = halfback_players.front();
		returned_entry->AssignPlayer( returned_player );

		//// Assign scrum players
		SIFRugbyCharacterList scrum_players;
		SIFRugbyCharacterList::iterator it;
		game->GetStrategyHelper()->GetTeamRoles( scrum_state->team, RURoleScrum::RTTGetStaticType(), scrum_players );

		RUZoneLocation& loc = scrum_state->locations[ SCRUMLOC_BOUND ];
		MABASSERTMSG( (scrum_players.size() >= loc.positions.size()), "We didnt find enough players to fill the scrum" );

		/// Work out which flanker zone position is open side
		const size_t left_pos_idx = 5;
		const size_t right_pos_idx = 6;
		const size_t LEFT_IDX = 0; const int RIGHT_IDX = 1;
		size_t flanker_slots[2] = { left_pos_idx, right_pos_idx }; // Left and right zone positions looking down field
		bool openside_on_right = scrum_state->scrum_state->GetScrumOrigin()->GetOrigin().x < 0.0f; // Open side is on right of field (-ve x is right)

		size_t open_side_flanker_idx = openside_on_right ? RIGHT_IDX : LEFT_IDX;
		if ( scrum_state->team->GetPlayDirection() == ERugbyPlayDirection::SOUTH )
			open_side_flanker_idx = 1 - open_side_flanker_idx;

		size_t blind_side_flanker_idx = 1 - open_side_flanker_idx;
		size_t open_side_flanker_pos_idx = flanker_slots[ open_side_flanker_idx ];
		size_t blind_side_flanker_pos_idx = flanker_slots[ blind_side_flanker_idx ];

		for( size_t j  = 0; j < loc.positions.size(); j++ )
		{
			RUZonePosition& pos = loc.positions[j];
			PLAYER_POSITION zone_player_pos = (PLAYER_POSITION) ((PTRINT) pos.GetUserData2());

			for( it = scrum_players.begin(); it != scrum_players.end();  )
			{
				bool player_match = false;
				PLAYER_POSITION player_pos = (*it)->GetAttributes()->GetPlayerPosition();
				if ( zone_player_pos & player_pos ) // Positional match
				{
					player_match = true;

					/* WJS RLC Not Needed??
					// Special case for flankers
					if ( zone_player_pos & PP_FLANKER )
					{
						player_match = (j == open_side_flanker_pos_idx && player_pos == PP_OPENSIDE_FLANKER) || (j == blind_side_flanker_pos_idx && player_pos == PP_BLINDSIDE_FLANKER);
					}
					//*/
				}

				if (  player_match ) {
					pos.AssignPlayer( *it );
					scrum_state->players_in_scrum.push_back( *it );
					it = scrum_players.erase( it );
				} else
					++it;
			}
		}

		// iterate the positions again, and try to fill in any empty slots, due to send offs and substitutions
		for( size_t j  = 0; j < loc.positions.size(); j++ )
		{
			if (!loc.positions[j].player)
			{
				MABASSERT(!scrum_players.empty());
				if (scrum_players.empty())
					break;

				ARugbyCharacter* next_player = scrum_players.back();
				loc.positions[j].AssignPlayer(next_player);
				scrum_state->players_in_scrum.push_back(next_player);
				scrum_players.pop_back();
			}
		}


	}

	SetupHumanContollers();

	SetScrumHUDVisible(true);
	
	// Give scrumhalf the ball
	ARugbyCharacter* player = GetCurrentAttackingTeamState().locations[ SCRUMLOC_HALFBACK ].positions[0].player;
	game->GetGameState()->SetBallHolder( player, true );
	player->GetMovement()->SetFacingPosition( GetCurrentScrum().GetScrumOrigin()->GetOrigin() );
	player->GetMovement()->SetFacingFlags( AFFLAG_FACEPOS );

	// Prepare for player warp
	TransitionToState(SP_STATE_WARP);
	UpdateStateWarp();
	//game->GetGameState()->LockHumanMovement( true );
}

void RUGamePhaseScrum::SetScrumHUDVisible(bool isVisible)
{
	// Set up help text
	if (game->GetHUDUpdater() && (GetCurrentAttackingTeamState().controlling_player_index != EHumanPlayerSlot::INVALID_HUMAN ||
		GetCurrentDefendingTeamState().controlling_player_index != EHumanPlayerSlot::INVALID_HUMAN))
	{
		game->GetHUDUpdater()->SetScrumHUDVisible(isVisible);
	}

	// show the hud indicator and such
	game->Get3DHudManager()->GetSetPieceIndicator()->SetColours(GetCurrentAttackingTeamState().controlling_player_index, GetCurrentDefendingTeamState().controlling_player_index);
	game->Get3DHudManager()->GetSetPieceIndicator()->SetVisible(isVisible);

}


void RUGamePhaseScrum::UpdateStateWarp()
{
	game->GetStrategyHelper()->WarpAllPlayersToWaypoints( game->GetGameState()->GetAttackingTeam() );
	game->GetStrategyHelper()->WarpAllPlayersToWaypoints( game->GetGameState()->GetDefendingTeam() );
	if (game->GetOfficialsTeam())
		game->GetStrategyHelper()->WarpAllPlayersToWaypoints( game->GetOfficialsTeam() );
	
	game->GetCameraManager()->SetCameraSnap(0);

	game->GetEvents()->change_to_camera(GAME_CAM_CROUCH_TOUCH_PAUSE);

	RUScrumTeamState& team_state = GetCurrentAttackingTeamState();
	ARugbyCharacter* number_eight = team_state.GetNo8Player();
	RUTeam* attk_team = team_state.team;

	// Players have warped, so start binding.
	game->GetEvents()->scrum_start(attk_team, number_eight);

	game->GetMovement()->RegisterStaticCollidable( &scrum_coll_centre, &scrum_coll_width, &scrum_coll_height, this );

	// Set up next state. Binding state lasts a short time. Binding will already have occurred.
	TransitionToState(SP_STATE_BINDING);
	engagement_state = ENG_STATE_PRE;
}

void RUGamePhaseScrum::UpdateStatePreBind()
{
	RUGameEvents* events = game->GetEvents();
	if ( setplay_time > 2.0f )
	{
		// Check players are in the correct location.
		bool players_in_pos = true;
		RUScrumTeamState *team_scrum_states [2] = { &GetCurrentAttackingTeamState(), &GetCurrentDefendingTeamState() };
		for( int i = 0; i < 2; ++i )
		{
			RUScrumTeamState* scrum_state = team_scrum_states[i];

			SIFRugbyCharacterList players = scrum_state->players_in_scrum;
			for ( SIFRugbyCharacterList::iterator iter = players.begin(); iter != players.end(); ++iter )
			{
				ARugbyCharacter* player = (*iter);
				RUZonePositionAssigned* pos_ass = player->GetPosAss();
				MABASSERT( pos_ass != NULL );
				RUZonePosition* pos = pos_ass->GetPosition();
				if( !pos->IsPlayerInPosition() )
				{
					players_in_pos = false;
					break;
				}
			}

			if ( !players_in_pos )
				break;
		}

		if ( players_in_pos )
		{
			RUScrumTeamState& team_state = GetCurrentAttackingTeamState();
			ARugbyCharacter* number_eight = team_state.GetNo8Player();
			RUTeam* attk_team = team_state.team;

			events->scrum_start(attk_team, number_eight);

			// Set up next state
			TransitionToState(SP_STATE_BINDING);
			engagement_state = ENG_STATE_PRE;
		}
	}
}

void RUGamePhaseScrum::UpdateStateBinding()
{
	static const float BINDING_DURATION = 0.5f;

	if ( setplay_time > BINDING_DURATION )
		TransitionToState(SP_STATE_ENGAGEMENTSEQUENCE);

}


void RUGamePhaseScrum::UpdateStateEngagementSequence( const MabTimeStep& game_time_step )
{

	const static float	PAUSE_DELAYS[5]		= {1.5f, 1.75f, 2.0f,2.25f,2.5f};


	// get the ref, so he can say 'crouch', 'touch', 'pause'
	RUPlayerSound* ref_sound = NULL;
	if ( game->GetOfficialsTeam() )
	{
		SIFRugbyCharacterList referees;
		game->GetStrategyHelper()->GetTeamRoles( game->GetOfficialsTeam(), SSRoleReferee::RTTGetStaticType(), referees );
		MABASSERTMSG(referees.size() == 1, "Where's the ref?");

		if ( referees.size() > 0 )
		{
			ref_sound = referees[0]->GetSound();
		}
	}

	if ( !ref_sound )
	{
		// In case of tutorials, we have no officials, so lets just use the ball holder.
		// Also fallback in the case there is no ref. Better to have the wrong person speaking than to not have voice.
		ARugbyCharacter* ball_holder = game->GetGameState()->GetBallHolder();
		if(ball_holder) ref_sound = ball_holder->GetSound();
	}

	RUScrumState &scrum_state = GetCurrentScrum();

	bool hold = false;


	struct EngagePhaseActions
	{
		const char* anim_request;
		const char* sound_request;
		int camera_change_request;
		float next_phase_delay;
		SCRUM_ENGAGE_STATE next_phase;
	};

	const static float PRE_ENGAGE_TIME = (SCRUM_PUSH_FREQUENCY - 0.15f - 0.3f);

	const EngagePhaseActions engage_phases[] =
	{
		/* ENG_STATE_PRE */				{ NULL,				NULL,		-2,						0.0f,				ENG_STATE_CROUCH_CALLED },
		/* ENG_STATE_CROUCH_CALLED */	{ NULL,				"crouch",	-2,						0.5f,				ENG_STATE_CROUCH },
		/* ENG_STATE_CROUCH */			{ "scrum_crouch",	NULL,		GAME_CAM_SCRUM_TOUCH,	1.0f,				ENG_STATE_TOUCH_CALLED },
		/* ENG_STATE_TOUCH_CALLED */	{ NULL,				"touch",	-2,						0.5f,				ENG_STATE_TOUCH },
		/* ENG_STATE_TOUCH */			{ "scrum_touch",	NULL,		GAME_CAM_SCRUM_PAUSE,	1.0f,				ENG_STATE_PAUSE },
		/* ENG_STATE_PAUSE */			{ NULL,				NULL,		-2,						10.0f /*custom*/,	ENG_STATE_PRE_ENGAGE },
		/* ENG_STATE_PRE_ENGAGE */		{ NULL,				"set",		GAME_CAM_SCRUM_PUSHING,	PRE_ENGAGE_TIME,	ENG_STATE_ENGAGE_CALLED },
		/* ENG_STATE_ENGAGE_CALLED */	{ NULL,				NULL,		-2,						0.3f,				ENG_STATE_ENGAGE },
		/* ENG_STATE_ENGAGE */			{ "scrum_engage",	NULL,		-2,						0.5f,				ENG_STATE_FEED },
		/* ENG_STATE_FEED */			{ NULL,				NULL,		-2,						0.7f,				ENG_STATE_START_PUSHING },
		/* ENG_STATE_START_PUSHING */	{ NULL,				NULL,		-2,						0.0f,				ENG_STATE_START_PUSHING },
	};

	float next_engagement_state_transition_time = 0.0f;
	int next_engagement_state = 0;

	if ( setplay_time > engagement_state_transition_time && !hold  )
	{
		if (engage_phases[engagement_state].anim_request)
			ApplyAnimationToAllPlayers(engage_phases[engagement_state].anim_request);
		if (ref_sound && engage_phases[engagement_state].sound_request)
			ref_sound->PlayRefVocal(engage_phases[engagement_state].sound_request);
		if (engage_phases[engagement_state].camera_change_request != -2)
		{
#ifdef ENABLE_PRO_MODE
			if(SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetIsAProMode())
			{

				if((RUGameCameraType) engage_phases[engagement_state].camera_change_request == GAME_CAM_SCRUM_PUSHING)
				{
					if(GetCurrentAttackingTeamState().GetIsProPlayerInScrum() || GetCurrentDefendingTeamState().GetIsProPlayerInScrum())
					{
						//MABLOGDEBUG("We're going to change the camera to pushing inside");
						game->GetEvents()->change_to_camera(GAME_CAM_SCRUM_PUSHING_INSIDE);
					}
					else
					{
						//MABLOGDEBUG("We're going to change the camera to pushing outside");
						game->GetEvents()->change_to_camera(GAME_CAM_SCRUM_PUSHING_OUTSIDE);
					}
				}
			}
			else
			{
				game->GetEvents()->change_to_camera((RUGameCameraType) engage_phases[engagement_state].camera_change_request);
			}
#else
		game->GetEvents()->change_to_camera((RUGameCameraType) engage_phases[engagement_state].camera_change_request);
#endif
		}
		next_engagement_state_transition_time = setplay_time + engage_phases[engagement_state].next_phase_delay;
		next_engagement_state = engage_phases[engagement_state].next_phase;
	}


	switch( engagement_state )
	{
		case ENG_STATE_PAUSE:
			// Play animation on ideal button hit.
			if ( setplay_time > engagement_state_transition_time && !hold  )
			{
				next_engagement_state_transition_time = setplay_time + PAUSE_DELAYS[engagement_pause_type] - PRE_ENGAGE_TIME;
			}
			break;
		case ENG_STATE_PRE_ENGAGE:
			if ( setplay_time > engagement_state_transition_time && !hold  )
			{
				scrum_state.shunt_timer = 0.0f;
				scrum_state.shunt_timer_lock = true;
			}
			break;
		case ENG_STATE_ENGAGE:
			UpdateEngaging();
			break;
		case ENG_STATE_FEED:
			if ( setplay_time > engagement_state_transition_time )
			{
				if 	( engagement_failed )
				{
					// Collapse for a failed engage. If both team fails, defense is said to have collapsed. Attacking advantage and all that.
					RUTeam* collapse_team = !GetCurrentDefendingTeamState().EngageValid() ? GetCurrentDefendingTeamState().team : GetCurrentAttackingTeamState().team;
					ScrumCollapsed( collapse_team );
				}
				else
					Feedball();
			}
			break;
		case ENG_STATE_START_PUSHING:
			if ( setplay_time > engagement_state_transition_time )
			{
				// force ball in if the animation has failed
				if (!scrum_state.ball_in)
					BallIn();

				ARugbyCharacter* feeder = scrum_state.attacking.GetScrumHalfPlayer();
				MABASSERT(feeder != nullptr);
				feeder->GetMabAnimationEvent().Remove( this, &RUGamePhaseScrum::OnAnimationEvent );

				TransitionToState(SP_STATE_PUSHING);
			}
			break;
		default:
			break;
	}


	if (engagement_state >= ENG_STATE_ENGAGE)
	{

		FVector scrum_angle_vect;
		float play_dir_scrum_angle = scrum_state.scrum_angle + (GetCurrentAttackingTeamState().team->GetPlayDirection() == ERugbyPlayDirection::NORTH ? 0.0f : PI);
		SSMath::AngleToMabVector3( play_dir_scrum_angle, scrum_angle_vect );

		//UpdateShuntMovement(scrum_angle_vect, game_time_step.delta_time.ToSeconds());
		UpdateInputAndPushAccuracy( game_time_step );
		UpdatePushingScrumPosition( scrum_angle_vect, game_time_step.delta_time.ToSeconds());

	}


	if ( setplay_time > engagement_state_transition_time && !hold  )
	{
		engagement_state_transition_time = next_engagement_state_transition_time;
		engagement_state = (SCRUM_ENGAGE_STATE) next_engagement_state;
	}

}



void RUGamePhaseScrum::UpdateEngaging()
{
	RUScrumTeamState& attacking_team = GetCurrentAttackingTeamState();
	RUScrumTeamState& defending_team = GetCurrentDefendingTeamState();

	bool proGame = SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro();

	// TODO: smarter AI engaging
	const float AI_ENGAGE_DELAY = 0.3f;
	bool att_team_enaged = (attacking_team.team->GetNumHumanPlayers() == 0 || (proGame && !attacking_team.GetIsProPlayerInScrum())) ? engagement_state_transition_time - setplay_time > AI_ENGAGE_DELAY : attacking_team.EngageValid();
	bool def_team_enaged = (defending_team.team->GetNumHumanPlayers() == 0 || (proGame && !defending_team.GetIsProPlayerInScrum())) ? engagement_state_transition_time - setplay_time > AI_ENGAGE_DELAY : defending_team.EngageValid();

	if ( att_team_enaged || def_team_enaged || setplay_time > engagement_state_transition_time )
	{

		// apply scrum enage rumbles
		if (attacking_team.controlling_player_index != EHumanPlayerSlot::INVALID_HUMAN) 
		{
			SIFRumbleHelpers::TriggerRumblePattern(game->GetHumanPlayer(attacking_team.controlling_player_index)->GetControllerIndex(), "scrum_engage");
		}
		if (defending_team.controlling_player_index != EHumanPlayerSlot::INVALID_HUMAN)
		{
			SIFRumbleHelpers::TriggerRumblePattern(game->GetHumanPlayer(defending_team.controlling_player_index)->GetControllerIndex(), "scrum_engage");
		}

		game->GetEvents()->scrum_down();

		engagement_failed = false;
	}

	UpdateAIPushValue(attacking_team);
	UpdateAIPushValue(defending_team);
}


void RUGamePhaseScrum::Feedball()
{
	// play feeder animation
	ARugbyCharacter* feeder = game->GetGameState()->GetBallHolder();
	if (feeder)
	{
#ifdef ENABLE_ASSERTS
		// check that the feeder is the scrum half
		ARugbyCharacter* scrum_half = GetCurrentScrum().attacking.GetScrumHalfPlayer();
		MABASSERT(scrum_half == feeder);
#endif
		feeder->GetMabAnimationEvent().Add( this, &RUGamePhaseScrum::OnAnimationEvent );
		feeder->GetAnimation()->PlayAnimation("feed_ball");
	}
}


void RUGamePhaseScrum::OnTeamAssignmentsChanged(RUTeam* /*new_team*/, SSHumanPlayer* /*human_player*/)
{
	if (game->GetGameState()->GetPhase() == RUGamePhase::SCRUM)
	{
		SetupHumanContollers();
		game->Get3DHudManager()->GetSetPieceIndicator()->SetColours( GetCurrentAttackingTeamState().controlling_player_index, GetCurrentDefendingTeamState().controlling_player_index );
	}
}



void RUGamePhaseScrum::OnAnimationEvent( float /*time*/, ERugbyAnimEvent event, size_t /*userdata*/, bool /*bIsBlendingOut = false*/)
{
	if (event == ERugbyAnimEvent::BALL_RELEASED_EVENT && setplay_state == SP_STATE_ENGAGEMENTSEQUENCE && game->GetGameState()->GetPhase() == RUGamePhase::SCRUM )
		BallIn();
}


void RUGamePhaseScrum::BallIn()
{
	RUScrumTeamState& attacking_team = GetCurrentAttackingTeamState();

	GetCurrentScrum().ball_in = true;

	game->GetGameState()->SetBallHolder(NULL);

	// Feed ball into scrum
	const static float TRAVEL_TIME = 0.7f;

	ASSBall* ball = game->GetBall();
	ball->CalculateTravelHeightData( TRAVEL_TIME, 0.0f);
	ball->SetTravelRotationSpeed( FVector::ZeroVector);

	FVector ball_position = ball->GetCurrentPosition();
	//ball_position.y = 0.2f;

	wwNETWORK_TRACE_JG("RUGamePhaseScrum::BallIn ball_position: X: %f, Y: %f, Z: %f", ball_position.X, ball_position.Y, ball_position.Z);

	FVector scrum_centre = GetCurrentScrum().GetScrumOrigin()->GetOrigin();
	wwNETWORK_TRACE_JG("RUGamePhaseScrum::BallIn scrum_centre: X: %f, Y: %f, Z: %f", scrum_centre.X, scrum_centre.Y, scrum_centre.Z);
	ball->SetTravelLine( ball_position, scrum_centre, TRAVEL_TIME );

	//fire the scrum ball in event.
	ARugbyCharacter* scrum_half = attacking_team.GetScrumHalfPlayer();
	game->GetEvents()->scrum_ball_in(attacking_team.team, scrum_half);

}


/*
// WJS RLC Not neeeded??
RUScrumEngageAction RUGamePhaseScrum::GetExpectedEngageAction()
{
	if ( engagement_state == ENG_STATE_PRE)
		return SCRUM_ACTION_NONE;

	return SCRUM_ACTION_ENGAGE;
}
//*/

/*
// WJS RLC Not neeeded??
ERugbyGameAction RUGamePhaseScrum::GetExpectedInputAction()
{
	constexpr const int RU_ACTION_MAP[6][2] = {
		{ ENG_STATE_PRE,	size_t(ERugbyGameAction::SCRUM_ENGAGE) },
		{ ENG_STATE_CROUCH,	size_t(ERugbyGameAction::SCRUM_ENGAGE) },
		{ ENG_STATE_TOUCH,	size_t(ERugbyGameAction::SCRUM_ENGAGE) },
		{ ENG_STATE_PAUSE,	size_t(ERugbyGameAction::SCRUM_ENGAGE) },
		{ ENG_STATE_ENGAGE,	size_t(ERugbyGameAction::SCRUM_ENGAGE) },
		{ ENG_STATE_FEED,	size_t(ERugbyGameAction::SCRUM_ENGAGE) }
	};

	return (ERugbyGameAction)RU_ACTION_MAP[engagement_state][1];
}
//*/

void RUGamePhaseScrum::UpdateAIRotationPosition()
{
	// Don't have the AI counter on very easy difficulty.
	if ( game->GetGameSettings().difficulty == DIF_VERYEASY )
		return;

	static const float AI_WHEEL_COUNTER_CHANCE_TABLE[] = { 0.0f, 0.3f, 0.5f, 0.8f, 1.0f };
	static const float AI_WHEEL_COUNTER_WITH_ADVANTAGE_CHANCE_TABLE[] = { 0.0f, 0.0f, 0.5f, 0.8f, 1.0f };
	static const float AI_SCRUM_ABILITY_STRENGTH_ADVANTAGE_TABLE[] = { 0.6f, 0.8f, 0.9f };

	static const float HIGH_SCRUM_ABILITY = 0.8f;
	static const float MED_SCRUM_ABILITY = 0.65f;
	static const float MIN_DISTANCE_FROM_CENTER = 10.0f;

	bool proGame = SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro();

	// AI should counter player attempts to wheel the scrum based on their scrum ability.
	RUScrumState& scrum_state = GetCurrentScrum();
	RUScrumTeamState* ai_side = NULL;
	RUScrumTeamState* human_side = NULL;

	// Assign the side without a human player to the ai.
	ai_side	= scrum_state.attacking.team->GetNumHumanPlayers() > 0 ? &scrum_state.defending : &scrum_state.attacking;

	// Human player must be the opposite side to the AI, as we have already determined both players are not AI.
	human_side = ai_side == &scrum_state.attacking ? &scrum_state.defending : &scrum_state.attacking;

	//if (scrum_team_state.team->GetNumHumanPlayers() > 0 || (proGame && scrum_team_state.GetIsProPlayerInScrum()))

	// If both AI we can just exit, as there will be no wheeling attempt.
	//if ( ai_side->team->GetNumHumanPlayers() != 0 )

	// for the purposes of pro games, a team can be said to have 0 human players if the pro player is outside of the scrum.
	if(	human_side->team->GetNumHumanPlayers() == 0 || (proGame && !human_side->GetIsProPlayerInScrum()))
	{
		MABLOGDEBUG("This scrum consists of two AI controlled teams");
		return;
	}


	// Work out a chance of AI countering based on difficulty setting. We use this to determine how often to counter the player's wheeling attempt.
	float ai_scrum_chance = AI_WHEEL_COUNTER_CHANCE_TABLE[game->GetGameSettings().difficulty];

	float rand = game->GetRNG()->RAND_CALL(float);	//0->1 not incl.
	if ( ai_scrum_chance > rand )
	{
		// We are countering, but how effectively?
		// On easy difficulty, counter based on team scrum ability.
		// On normal difficulty, counter with a chance of advantage.
		// On pro, always attempt to counter with advantage.
		float ai_scrum_strength = AI_WHEEL_COUNTER_WITH_ADVANTAGE_CHANCE_TABLE[game->GetGameSettings().difficulty];
		rand = game->GetRNG()->RAND_CALL(float);

		// If close to the opponents sideline, counter all wheeling attempts.
		if ( game->GetSpatialHelper()->IsInDef20( scrum_state.scrum_centre, ai_side->team->GetPlayDirection() ) )
		{
			// Always advantageous to counter if the other team is in our 22.
		}

		// Attempt to use the optimal counter only if we are off to one side of the field, it doesn't really make sense to do this near the center.
		else if ( fabs(scrum_state.scrum_centre.x) > MIN_DISTANCE_FROM_CENTER && ai_scrum_strength > rand )
		{
			// AI should attempt only attempt to counter the player if the player is wheeling in a direction advantageous to them.
			// If there is no advantage gained, don't bother countering it.
			bool closest_sideline_is_left_relative_to_north = scrum_state.scrum_centre.x < 0.0f;

			float wheeling_angle = human_side->rotation_position;

			// Need to work out whether the scrum is wheeling to the sideline or not.
			bool heading_towards_closest_sideline = false;
			if ( closest_sideline_is_left_relative_to_north )
			{
				// If closest sideline is left, and the scrum is turning counter-clockwise, we are heading towards closest sideline.
				heading_towards_closest_sideline = wheeling_angle < 0.0f;
			}
			else
			{
				// If closest sideline is right, and the scrum is turning clockwise, we are heading towards closest sideline.
				heading_towards_closest_sideline = wheeling_angle > 0.0f;
			}

			// Actually, we want to test for wheeling away from sideline (our backs to the sideline is advantageous).
			heading_towards_closest_sideline = !heading_towards_closest_sideline;

			bool direction_is_advantageous = heading_towards_closest_sideline;

			// These calculations assume we are facing north, invert the advantageous direction if we are playing south.
			bool playing_south = ai_side->team->GetPlayDirection() == ERugbyPlayDirection::SOUTH;
			if ( playing_south )
			{
				direction_is_advantageous = !direction_is_advantageous;
			}

			// If the overall direction is to the ai's advantage, don't bother countering.
			if ( direction_is_advantageous )
			{
				return;
			}
		}

		// Work out a strength for the ai based on their team's scrum ability.
		// We use this to determine how effective wheel countering is.
		float ai_scrum_ability = ai_side->GetScrumAbility();
		float counter_strength = 1.0f;
		if ( ai_scrum_ability >= HIGH_SCRUM_ABILITY )
		{
			counter_strength = AI_SCRUM_ABILITY_STRENGTH_ADVANTAGE_TABLE[2];
		}
		else if ( ai_scrum_ability >= MED_SCRUM_ABILITY )
		{
			counter_strength = AI_SCRUM_ABILITY_STRENGTH_ADVANTAGE_TABLE[1];
		}
		else
		{
			counter_strength = AI_SCRUM_ABILITY_STRENGTH_ADVANTAGE_TABLE[0];
		}

		// If we are on hard or pro difficulties, the AI should be able to initiate wheeling.
		if ( (0 == human_side->rotation_position) && (DIF_HARD == game->GetGameSettings().difficulty || DIF_PRO == game->GetGameSettings().difficulty) &&
			(fabs(scrum_state.scrum_centre.x) > MIN_DISTANCE_FROM_CENTER) && (ai_scrum_strength > rand) &&
			(false == game->GetSpatialHelper()->IsInDef20(scrum_state.scrum_centre, ai_side->team->GetPlayDirection())) )
		{
			float direction = 1.0f;

			// Find out which direction is advantageous.
			if ( scrum_state.scrum_centre.x > 0.0f )
			{
				direction = -1.0f;
			}

			if ( ERugbyPlayDirection::SOUTH == ai_side->team->GetPlayDirection() )
			{
				direction *= -1.0f;
			}

			ai_side->rotation_position += counter_strength * ROTATION_SCALE * direction;
		}
		else
		{
			ai_side->rotation_position = -1.0f * counter_strength * human_side->rotation_position;
		}
	}
}

void RUGamePhaseScrum::ApplyAnimationToAllPlayers( const MabString& animation_name )
{
	// This could be done using an event which is handled by all players in the scrum role. Probably better?
	RUScrumTeamState *team_scrum_states[2] = { &GetCurrentAttackingTeamState(), &GetCurrentDefendingTeamState() };
	for( int i = 0; i < 2; ++i )
	{
		RUScrumTeamState* scrum_team_state = team_scrum_states[i];
		SIFRugbyCharacterList* players = &scrum_team_state->players_in_scrum;
		for ( SIFRugbyCharacterList::iterator iter = players->begin(); iter != players->end(); ++iter )
		{
			RUPlayerAnimation* animation = (*iter)->GetAnimation();
			MABASSERT( animation );
			animation->PlayAnimation( animation_name.c_str() );
		}
	}
}

void RUGamePhaseScrum::UpdateStatePushing( const MabTimeStep& game_time_step )
{
#ifdef FREEZE_SCRUMS
	MABUNUSED( game_time_step );
	return;
#endif

	UE_LOG(LogDesync, Display, TEXT("RUGamePhaseScrum::UpdateStatePushing"));

	RUScrumState* current_scrum = &GetCurrentScrum();

	float delta_time = game_time_step.delta_time.ToSeconds();

	RUScrumTeamState* attacking_team_state = &GetCurrentAttackingTeamState();

	//const float PUSHING_DELAY_TIME = 1.0f;
	//if ( setplay_time > PUSHING_DELAY_TIME )
	{

		if (current_scrum->release_possible)
		{
			if (game->GetHUDUpdater())
			{
				game->GetHUDUpdater()->SetScrumHelperText();
				//SIF_DEBUG_DRAW( RemoveText(455232) );
			}
		}


		//UpdateOldControlStyle(game_time_step.delta_time.ToSeconds());

		// Grab the current scrum
		RUScrumState& scrum_state = GetCurrentScrum();

		FVector scrum_angle_vect;
		float play_dir_scrum_angle = scrum_state.scrum_angle + (attacking_team_state->team->GetPlayDirection() == ERugbyPlayDirection::NORTH ? 0.0f : PI);
		SSMath::AngleToMabVector3( play_dir_scrum_angle, scrum_angle_vect );
		scrum_angle_vect.Normalise();

		FVector origin = scrum_state.GetScrumOrigin()->GetOrigin();
		MABUNUSED(origin);

		// Calculate shunt magic
		UpdateShuntMovement(scrum_angle_vect, delta_time);

		UpdateInputAndPushAccuracy( game_time_step );

		// Evaluate where the ball currently is in relation to the scrum
		UpdateScrumWinConditions( scrum_angle_vect, delta_time );
		UpdateBallPosition( delta_time );
		if (!scrum_state.release_possible)
		{
			UpdatePushingScrumPosition(scrum_angle_vect, delta_time);
		}

		UpdatePushingRotation(delta_time);

		UpdateAIDecision( scrum_state );

		if (game->GetGameState()->GetBallHolder() != NULL)
		{
			TransitionToState(SP_STATE_WAIT_RELEASE);
		}
	}

}

void RUGamePhaseScrum::UpdateStateWaitRelease( float delta_time )
{
	// GGs JZ we shouldn't need NumberEightPickup for league
	//if ( game->GetGameSettings().game_settings.game_type != GAME_TRAINING && game->GetGameSettings().game_settings.game_type != GAME_MENU)
	//{
		//UpdateNumberEightPickup();
	//}

	RUGameState *game_state = game->GetGameState();

	UpdateBallPosition( delta_time );

	if(game_state->GetBallHolder()!=NULL)
	{
		RUGameEvents* game_events = game->GetEvents();
		game_events->scrum_finish();
		game_state->SetPhase(RUGamePhase::PLAY);

		// Clear current scrum
		GetCurrentScrum().Exit();
	}
}

void RUGamePhaseScrum::UpdateStateReset()
{
	// Give players a short period of time to get unbound
	if ( setplay_time > 2.0f )
	{
		// clear all the roles and reenter this phase
		// clear the roles so they don't have any state hanging around
		game->GetStrategyHelper()->ClearRoles(game->GetTeam(SIDE_A));
		game->GetStrategyHelper()->ClearRoles(game->GetTeam(SIDE_B));
		game->GetGameState()->SetPhase( RUGamePhase::SCRUM );
	}
}

//void RUGamePhaseScrum::UpdateNumberEightPickup()
//{
//	bool proGame = SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro();
//
//	// Don't update number 8 if we're playing pro, and our pro is outside of the scrum
//	// TOOD DEWALD: Don't even update if our pro player is inside the scrum, only update if our pro player is the scrum half?
//	if(proGame && !GetCurrentScrum().attacking.GetIsProPlayerInScrum())
//		return;
//
//	// No number 8 pickup when we play sevens either.
//	if ( game->GetGameSettings().game_settings.GameModeIsR7() )
//		return;
//
//	if ( game->GetGameState()->GetAttackingTeam()->GetNumHumanPlayers() > 0)
//	{
//		// Attacking team should be the team that won the scrum by this point.
//		RUTeam* attacking_team = game->GetGameState()->GetAttackingTeam();
//
//		const RUScrumTeamState& scrum_team_state = GetCurrentScrum().attacking.team == attacking_team ? GetCurrentScrum().attacking : GetCurrentScrum().defending;
//
//		SSHumanPlayer* human = game->GetHumanPlayer( scrum_team_state.controlling_player_index );
//
//		if (human == nullptr)
//		{
//			return;
//		}
//
//		//Don't update number 8 if setplay is being held down.
//		if (human->IsOn(ERugbyGameAction::RUCK_HALF_SET_PLAYS))
//		{
//			UE_LOG(LogTemp, Display, TEXT("RUGamePhaseScrum::UpdateNumberEightPickup Early exit due to setplay held down."));
//			return;
//		}
//
//		//Don't update number 8 if a setplay is in progress/being set up.
//		if (human->GetTeam()->GetFormationManager()->GetSetplayManager()->IsSetplayInProgress())
//		{
//			UE_LOG(LogTemp, Display, TEXT("RUGamePhaseScrum::UpdateNumberEightPickup Early exit due to set play."));
//			return;
//		}
//
//		//Don't update number 8 if a pass is being buffered
//		if ((human->IsOn(ERugbyGameAction::PASS_LEFT_SINGLE) || human->IsOn(ERugbyGameAction::PASS_RIGHT_SINGLE)
//			|| human->IsOn(ERugbyGameAction::PASS_LEFT_AUTO_DETERMINE) || human->IsOn(ERugbyGameAction::PASS_RIGHT_AUTO_DETERMINE)))
//		{
//			UE_LOG(LogTemp, Display, TEXT("RUGamePhaseScrum::UpdateNumberEightPickup Early exit due to pass."));
//			return;
//		}
//
//		//Extra check to not start the number 8 pickup if numer 9 has started a pass.
//		ARugbyCharacter* number_nine = attacking_team->GetPlayerByPosition(PP_SCRUM_HALF);
//		if (number_nine && number_nine->GetActionManager() && number_nine->GetActionManager()->IsActionRunning(ACTION_PASS))
//		{
//			UE_LOG(LogTemp, Display, TEXT("RUGamePhaseScrum::UpdateNumberEightPickup Early exit due to number nine pass. Number nine valid: %d"), number_nine != nullptr);
//			return;
//		}
//
//		if (human->IsOn( ERugbyGameAction::SCRUM_NUMBER_EIGHT_PICKUP ) )
//		{
//			// Boot number 9 out of role, assign number 8 instead.		
//			ARugbyCharacter* number_eight = attacking_team->GetPlayerByPosition( PP_NUMBER_EIGHT_LOCK_FORWARD );
//
//			if ( number_nine && number_eight )
//			{
//				// RC4-6305: Were able to get into a case where the No. 9 had already been told to animate to pick up the ball before triggering the No. 8 pickup.
//				// This could result in the No. 9 carrying through with the pickup action some time after the ball had already been picked up by the No.8 and teleporting the ball back.
//				number_nine->GetAnimation()->StopAnimation(ANIM_PICKUP_RUN);
//				number_nine->GetAnimation()->StopAnimation(ANIM_PICKUP);
//				number_nine->GetAnimation()->StopAnimation(ANIM_SCOOT);
//				number_nine->GetAnimation()->StopAnimation(ANIM_SCOOT);
//
//
//				game->GetGameState()->SetPlayRestartTeam( attacking_team );
//
//				game->GetGameState()->SetBallHolder( NULL );
//
//				number_eight->SetRole( game->GetRoleFactory()->Instance( RURoleScrumHalfBack::RTTGetStaticType() ), true );
//				RURoleScrumHalfBack* new_scrum_half = number_eight->GetRole<RURoleScrumHalfBack>();
//
//				new_scrum_half->DoNumberEightPickup();
//
//				// RC4-3922 Reduce the size of the collisions of the scrum members directly in front of the number 8 to prevent 
//				// him from being forced to run backwards out of the scrum are and potentially turning in a complete circle.
//				static const float REDUCED_COLLIDABLE_RADIUS = 0.2f;
//				auto ReducePlayerCollisionRadius = [](ARugbyCharacter* player, SIFGameWorld* pGame) -> void
//				{
//					if (player && pGame)
//					{
//						pGame->GetMovement()->UnregisterStaticCollidable(player);
//						pGame->GetMovement()->RegisterStaticCollidable(&player->GetMovement()->GetCurrentPosition(), REDUCED_COLLIDABLE_RADIUS, player);
//					}
//				};
//				ReducePlayerCollisionRadius(attacking_team->GetPlayerByPosition(PP_NUMBER_FOUR_LOCK_SECOND_ROW_TWELVE ), game);
//				ReducePlayerCollisionRadius(attacking_team->GetPlayerByPosition(PP_NUMBER_FIVE_LOCK_SECOND_ROW_ELEVEN ), game);
//				
//				// WJS RLC Not needed?? ReducePlayerCollisionRadius(attacking_team->GetPlayerByPosition(PP_BLINDSIDE_FLANKER), game);
//				// WJS RLC Not needed?? ReducePlayerCollisionRadius(attacking_team->GetPlayerByPosition(PP_OPENSIDE_FLANKER ), game);
//
//
//				game->GetEvents()->scrum_ball_release_possible( attacking_team );
//
//				human->SetKickInputIgnored(); // Prevent the player from immediately kicking the ball.
//
//				// Set up travel line for number 9 player (number 8 set up in the ScrumHalfBack role after grab animation plays).
//				const FVector& scrum_center = GetCurrentScrum().scrum_centre;
//
//				float direction_x = 1.0f;
//				float direction_z = 1.0f;
//
//				if ( number_nine->GetAttributes()->GetPlayDirection() == ERugbyPlayDirection::SOUTH )
//				{
//					direction_x = -1.0f;
//					direction_z = -1.0f;
//				}
//
//				const static float NINE_X_OFFSET = 8.0f;
//				const static float NINE_Z_OFFSET = 5.0f;
//				float DEFAULT_OFFSET_DIST = MabMath::Sqrt( NINE_X_OFFSET * NINE_X_OFFSET + NINE_Z_OFFSET * NINE_Z_OFFSET );
//				float DEFAULT_DIST_RUN_TIME = 3.8f;
//				FVector nine_target = scrum_center;
//				nine_target.x -= (NINE_X_OFFSET * direction_x);
//				const static float SIDELINE_OFFSET =  0.9f;
//				MabMath::Clamp( nine_target.x, -(FIELD_WIDTH* 0.5f - SIDELINE_OFFSET), +(FIELD_WIDTH* 0.5f - SIDELINE_OFFSET) );
//				nine_target.z += (NINE_Z_OFFSET * direction_z);
//
//				FVector nine_avoid_vertex = scrum_center;
//				nine_avoid_vertex.x -= ((NINE_X_OFFSET - 1.0f) * direction_x);
//				MabMath::Clamp( nine_avoid_vertex.x, -(MabMath::Fabs(nine_target.x) - 1.0f), +(MabMath::Fabs(nine_target.x) - 1.0f) );
//				nine_avoid_vertex.z -= (NINE_Z_OFFSET * direction_z);
//
//				RUMotionSpline* nine_spline = MabMemNew( MabMemGetDefaultObjectHeap( this ) ) RUMotionSpline( number_nine->GetMovement(), nine_target, nine_avoid_vertex );
//				float travel_time = ((scrum_center - nine_target).Magnitude() / DEFAULT_OFFSET_DIST) * DEFAULT_DIST_RUN_TIME;
//				nine_spline->SetTargetTravelTime( travel_time );
//
//				number_nine->GetMovement()->SetMotionSource( nine_spline, true, true );
//			}
//		}
//	}
//}

void RUGamePhaseScrum::UpdateHUD()
{
}

void RUGamePhaseScrum::UpdatePushingScrumPosition( FVector& /*scrum_angle_vect*/, float /*delta_time*/ )
{
	RUScrumState& scrum_state = GetCurrentScrum();

	//MB - increased the scrum shunt factor by 3 times, as the duration of scrums has been reduced to 1/3rd its normal time.
	const static float SHUNT_SCALAR = 1.125; //0.375f;

#if USING_CLIENT_SIDE_MINIGAMES
	UE_LOG(LogDesync, Display, TEXT("RUGamePhaseScrum::UpdatePushingScrumPosition Checking shunt timer: shunt timer: %f, scrum push freq: %f"), scrum_state.shunt_timer, SCRUM_PUSH_FREQUENCY);

	bool reset_shunt_timer = IsShuntTimerLimitReached() && scrum_state.shunt_timer_lock;

	if (reset_shunt_timer)
#else
	if (scrum_state.shunt_timer > SCRUM_PUSH_FREQUENCY)
#endif
	{
		scrum_state.shunt_timer -= SCRUM_PUSH_FREQUENCY;
		scrum_state.shunt_count++;

		scrum_state.push_state_needs_reset = true;

		UE_LOG(LogDesync, Display, TEXT("RUGamePhaseScrum::UpdatePushingScrumPosition Resetting shunt timer: shunt timer: %f, scrum push freq: %f, shunt count: %d"), scrum_state.shunt_timer, SCRUM_PUSH_FREQUENCY, scrum_state.shunt_count);

		// While we're restricting turnovers, we fudge the pushing scores in the attacking team's favour
		if(!game->GetGameTimer()->IsTurnoverAvailable())
		{
			if(scrum_state.attacking.has_pushed)
			{
				if (scrum_state.defending.push_score > scrum_state.attacking.push_score)
				{
					scrum_state.defending.push_score = scrum_state.attacking.push_score * 0.25f;
				}
			}
			else
			{
				scrum_state.attacking.push_score = scrum_state.defending.push_score + 0.1f;
			}
		}

		float shunt_value = scrum_state.attacking.push_score - scrum_state.defending.push_score;

		//MabString msg;
		//if ( MabMath::Feq( shunt_value, 0.0f ) )
		//	msg = "Even!!!";
		//else if ( shunt_value > 0.0f )
		//	msg.sprintf( "%s wins by %0.2f", scrum_state.attacking.team->GetDbTeam().GetName(), MabMath::Fabs( shunt_value ) );
		//else
		//	msg.sprintf( "%s wins by %0.2f", scrum_state.defending.team->GetDbTeam().GetName(), MabMath::Fabs( shunt_value ) );
		//
		//SETDEBUGTEXTWORLD( 402100966, scrum_state.GetScrumOrigin()->GetOrigin(), msg.c_str() );
		//
		//SETDEBUGTEXTWORLD( 37791055 + 0, scrum_state.GetScrumOrigin()->GetOrigin() + FVector(0,0,scrum_state.attacking.team->GetPlayDirection() * -4.0f ),
		//	MabString( 64, "SCORE %0.2f", scrum_state.attacking.push_score ).c_str() );
		//SETDEBUGTEXTWORLD( 37791055 + 1, scrum_state.GetScrumOrigin()->GetOrigin() + FVector(0,0,scrum_state.defending.team->GetPlayDirection() * -4.0f ),
		//	MabString( 64, "SCORE %0.2f", scrum_state.defending.push_score ).c_str() );


		// Drive towards the current scrum power over duration of SHUNT_TIME.
		scrum_state.scrum_shunt += shunt_value * SHUNT_SCALAR;

		game->GetEvents()->scrum_pushing(scrum_state.attacking.team, shunt_value);

		scrum_state.attacking.push_score = 0.0f;
		scrum_state.defending.push_score = 0.0f;

		UpdateAIPushValue(scrum_state.attacking);
		UpdateAIPushValue(scrum_state.defending);
	}

#if USING_CLIENT_SIDE_MINIGAMES
	if (scrum_state.shunt_timer > (SCRUM_PUSH_FREQUENCY / 2.f) && scrum_state.push_state_needs_reset)
	{
		ResetTeamStatesOnTimerLimitReached();
		scrum_state.push_state_needs_reset = false;
	}
#endif
}

void RUGamePhaseScrum::UpdateAIPushValue(RUScrumTeamState& scrum_team_state)
{
	// this sets the quality of the ai's next push, based on skill and difficulty
	// call this once per push

	bool proGame = SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro();

	// Don't update AI push if:
	// Pro game, and we have numan players, and pro is inside the scrum
	if(proGame)
	{
		if(scrum_team_state.GetIsProPlayerInScrum())
			return;
	}
	else
	{
		// Non pro game and team has human players
		if (scrum_team_state.team->GetNumHumanPlayers() > 0)
			return;
	}

	const static float AI_ACCURACY_MEAN_BY_DIFFICULTY[] = { 0.50f, 0.50f, 0.50f, 0.50f, 0.50f };
	const static float AI_ACCURACY_VARIANCE_BY_DIFFICULTY[] = { 1.0f, 0.40f, 0.25f, 0.20f, 0.15f };

	scrum_team_state.ai_push_accuracy = MabRandGaussian( *game->GetRNG(),
		AI_ACCURACY_MEAN_BY_DIFFICULTY[game->GetGameSettings().difficulty],
		AI_ACCURACY_VARIANCE_BY_DIFFICULTY[game->GetGameSettings().difficulty] );
}

#if !USING_CLIENT_SIDE_MINIGAMES
void RUGamePhaseScrum::UpdateInputAndPushAccuracy( const MabTimeStep& game_time_step )
{
	RUScrumState &scrum_state = GetCurrentScrum();

	// update the push timing

	float timing_offset = SCRUM_PUSH_FREQUENCY*0.5f + 0.15f;

	float time_as_radians = (scrum_state.shunt_timer + timing_offset) / SCRUM_PUSH_FREQUENCY * PI;
	float global_scrum_push_timing = (1.0f + MabMath::Cos(time_as_radians)) * 0.5f;
	const float INPUT_MINIMUM_THRESHOLD = 0.5f;	// pulse at points worst than this are ignored

	for (int i = 0; i < 2; ++i)
	{

		RUScrumTeamState& team_state = i==0 ? scrum_state.attacking : scrum_state.defending;


		//
		// Get analoge stick inputs (or fake ones from ai robots)
		//

		float left_stick = 0.0f;
		float right_stick = 0.0f;
		float left_stick_visual = 0.0f;
		float right_stick_visual = 0.0f;

		if (team_state.controlling_player_index != EHumanPlayerSlot::INVALID_HUMAN)
		{
			// check the input, large positive or negative on y axis will do
			left_stick = MabMath::Abs(game->GetHumanPlayer( team_state.controlling_player_index )->GetLeftStick().y);
			right_stick = MabMath::Abs(game->GetHumanPlayer( team_state.controlling_player_index )->GetRightStick().y);
			left_stick_visual = MabMath::Abs(game->GetHumanPlayer( team_state.controlling_player_index )->GetLeftStickLocal().y);
			right_stick_visual = MabMath::Abs(game->GetHumanPlayer( team_state.controlling_player_index )->GetRightStickLocal().y);

			// also check for l3 and r3 clicks, foolish humans and their spasming hands
			if ( game->GetHumanPlayer( team_state.controlling_player_index )->IsPressed( ERugbyGameAction::SCRUM_PUSH_LEFT ) )
				left_stick = 1.0f;
			if ( game->GetHumanPlayer( team_state.controlling_player_index )->IsPressed( ERugbyGameAction::SCRUM_PUSH_RIGHT ) || game->GetHumanPlayer( team_state.controlling_player_index )->IsPressed( ERugbyGameAction::UP_ALT ) )
				right_stick = 1.0f;

			// also check for l3 and r3 clicks, foolish humans and their spasming hands
			if ( game->GetHumanPlayer( team_state.controlling_player_index )->IsPressedLocal( ERugbyGameAction::SCRUM_PUSH_LEFT ) )
				left_stick_visual = 1.0f;
			if ( game->GetHumanPlayer( team_state.controlling_player_index )->IsPressedLocal( ERugbyGameAction::SCRUM_PUSH_RIGHT ) || game->GetHumanPlayer( team_state.controlling_player_index )->IsPressedLocal( ERugbyGameAction::UP_ALT ) )
				right_stick_visual = 1.0f;

			//MABLOGDEBUG( "%d %0.2f LOC %0.2f NET LAS=%0.4f RAS=%0.4f VLAS=%0.4f VRAS=%0.4f", i, game->GetSimTime()->GetAbsoluteTime().ToSeconds(), game->GetNetworkState() ? game->GetNetworkState()->GetInputAbsTime() : game->GetSimTime()->GetAbsoluteTime().ToSeconds(),
			//	left_stick, right_stick,
			//	left_stick_visual, right_stick_visual
			//);
		}
		else
		{
			GetAIPushInput(team_state, game_time_step, left_stick, right_stick);
		}

		//
		// check for stick pushes
		//

		// keep track of neutral
		const float NUETRAL_STICK_MAX_INPUT = 0.4f;
		if (left_stick + right_stick < NUETRAL_STICK_MAX_INPUT)
		{
			team_state.time_since_input_zerod = 0.0f;
		}
		else
		{
			team_state.time_since_input_zerod += game_time_step.delta_time.ToSeconds();
		}


		const float INPUT_PULSED_MIN_THRESHOLD = 0.9f;	// inputs combined must be greater than this
		const float INPUT_PULSE_QUICKNESS_THRESHOLD = 0.6f;	// pulse from neutral to full needs to be quicker than this in seconds
		bool is_push_attempt_this_frame = !team_state.has_pushed &&
					(left_stick > INPUT_PULSED_MIN_THRESHOLD || right_stick > INPUT_PULSED_MIN_THRESHOLD) &&
					team_state.time_since_input_zerod < INPUT_PULSE_QUICKNESS_THRESHOLD;
		bool is_visual_push_attempt_this_frame = !team_state.has_visual_pushed &&
			(left_stick_visual > INPUT_PULSED_MIN_THRESHOLD || right_stick_visual > INPUT_PULSED_MIN_THRESHOLD) &&
			team_state.time_since_input_zerod < INPUT_PULSE_QUICKNESS_THRESHOLD;

		//
		// apply results of successful push
		//

		const float SWEET_SPOT_SIZE = 0.01f;
		const float SWEET_SPOT_SCORE = 5.0f;
//		const static float VISUAL_POWER_SET[2] = { 0.0f };
//
//		#ifdef ENABLE_OSD
//		if ( team_state.team->GetNumHumanPlayers() > 0 )
//		{
//			RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
//			MabString activation_string( 128, "%d %0.2f GSP=%0.2f AM=%0.2f VAM=%0.2f AIP=%0.2f",
//				i, game->GetSimTime()->GetAbsoluteTime().ToSeconds(),
//				global_scrum_push_timing, team_state.accuracy_marker, team_state.visual_accuracy_marker, team_state.ai_push_accuracy
//			);
//			settings->PushDebugString( game, RUGameDebugSettings::DP_SCRUM, activation_string.c_str() );
//			MABLOGDEBUG( activation_string.c_str() );
//		}
//		#endif

		/// Lock the visual push to where we are now
		if ( is_visual_push_attempt_this_frame && !team_state.has_visual_pushed )
		{
			float accuracy_rate = (0.5f - MabMath::Abs(global_scrum_push_timing - 0.5f)) * 2.0f;

			if (accuracy_rate < INPUT_MINIMUM_THRESHOLD)
			{
				is_visual_push_attempt_this_frame = false;
			}

			if ( is_visual_push_attempt_this_frame )
			{
//				if ( team_state.team->GetNumHumanPlayers() > 0 )
//				{
//					#ifdef ENABLE_OSD
//					RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
//					MabString activation_string( 128, "Scrum visual %d power set at %0.2f, GSP=%0.2f VAM=%0.2f SH%0.2f", i, game->GetSimTime()->GetAbsoluteTime().ToSeconds(),
//						 global_scrum_push_timing, team_state.visual_accuracy_marker, scrum_state.shunt_timer
//					);
//					settings->PushDebugString( game, RUGameDebugSettings::DP_SCRUM, activation_string.c_str() );
//					MABLOGDEBUG( activation_string.c_str() );
//					#endif
//					VISUAL_POWER_SET[i] = game->GetSimTime()->GetAbsoluteTime().ToSeconds();
//				}

				team_state.has_visual_pushed = true;
			}
		}

		//bool winEvenIfNotPushing = true;

		//if(winEvenIfNotPushing && i==0 && !team_state.has_pushed)
		//	is_push_attempt_this_frame = true;

		/*
		// If we're not allowed a turn over, then basically make it impossible for the attacking team to lose here.
		// But only do it if there was a push attempt? Otherwise it looks a little weird.
		bool winEvenIfNotPushing = true;
		if(!game->GetGameTimer()->IsTurnoverAvailable() && (is_push_attempt_this_frame || winEvenIfNotPushing))
		{
			// Defending team
			if(i==1)
			{
				// Only set ours to 0.5 if the attacking team actually pushed, otherwise its a stalemate
				// Or if we set it to win even without pusing
				if(scrum_state.attacking.has_pushed || winEvenIfNotPushing)
					accuracy_rate = 0.5f;
			}
			// Attacking team
			else
			{
				// Make our pushes a little better than what they were
				accuracy_rate = MabMath::Max(accuracy_rate + 0.1f, 1.0f);
			}
		}*/


		if (is_push_attempt_this_frame)
		{
			// rewind this input attempt based on perceived lag (provided by the network manager)
			// normally this is zero (perhaps i should be nice and rewind one frame)
			/*const static float LOCAL_LAG = 0.0f;
			float perceived_lag = game->GetNetworkState()? game->GetNetworkState()->GetInputLagAdjustment() : LOCAL_LAG;*/

			if ( game->GetNetworkState() )
			{
				bool found = false;
				float input_abs_time = game->GetNetworkState()->GetInputAbsTime();
				for( size_t j = 0; j < team_state.accuracy_marker_history.size(); ++j )
				{
					if ( team_state.accuracy_marker_history[j].abs_time == input_abs_time )
					{
						found = true;
						team_state.accuracy_marker = team_state.accuracy_marker_history[j].accuracy_marker;
						//MABLOGDEBUG( "Network AM %d is %0.2f (from abs time %0.2f)", i, team_state.accuracy_marker, input_abs_time );
						break;
					}
				}
				MABASSERTMSG( found, "Scrum marker accuracy history did not contain required input" );
			}
			else
			{
				if ( team_state.accuracy_marker_history.empty())
					team_state.accuracy_marker = global_scrum_push_timing;
				else
					team_state.accuracy_marker = team_state.accuracy_marker_history.front().accuracy_marker;	// Use the most recently set value (should be where the visual marker stopped)
			}


			// convert the accuracy marker to a 0-1 score where the middle point is best
			float accuracy_rate = (0.5f - MabMath::Abs(team_state.accuracy_marker - 0.5f)) * 2.0f;

			/*if(!game->GetGameTimer()->IsTurnoverAvailable())
			{
				// Defending team
				if(i==1)
				{
					//accuracy_rate = 0.5f;
				}
				// Attacking team
				else
				{
					// Make our pushes a little better than what they were
					//accuracy_rate = MabMath::Max(accuracy_rate + 0.1f, 1.0f);
				}
			}*/

			// ignore pushes in the worst 50 area, so that button mashers can realise they are doing it wrong
			if (accuracy_rate < INPUT_MINIMUM_THRESHOLD)
			{
				is_push_attempt_this_frame = false;
			}

			if (is_push_attempt_this_frame)
			{
				team_state.has_pushed = true;

				float accuracy_range = SCRUM_MAX_ACCURATE_ZONE * GetTeamZonePower(i==0);

				accuracy_rate = (accuracy_rate - (1.0f - accuracy_range)) / (accuracy_range);
				MabMath::Clamp(accuracy_rate, 0.0f, 1.0f);

				// check for sweet spot
				if (accuracy_rate > 1.0f - SWEET_SPOT_SIZE)
					accuracy_rate = SWEET_SPOT_SCORE;

				team_state.push_score = accuracy_rate;

				//if ( team_state.team->GetNumHumanPlayers() > 0 )
				//{
				//	#ifdef ENABLE_OSD
				//	RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
				//	MabString activation_string( 128, "Scrum actual %d power set at %0.2f, AM=%0.2f VAM=%0.2f SCORE=%0.2f SH%0.2f, NLA = %0.2f, DELTA = %0.2f, DT=%0.2f, %s %s",
				//		i, game->GetSimTime()->GetAbsoluteTime().ToSeconds(),
				//		team_state.accuracy_marker, team_state.visual_accuracy_marker, team_state.push_score, scrum_state.shunt_timer, perceived_lag,
				//		(game->GetSimTime()->GetAbsoluteTime().ToSeconds() - VISUAL_POWER_SET[i]) - perceived_lag,
				//		game->GetSimTime()->GetDeltaTime().ToSeconds(),
				//		(left_stick_visual > INPUT_PULSED_MIN_THRESHOLD || right_stick_visual > INPUT_PULSED_MIN_THRESHOLD) ? "yes" : "no",
				//		(team_state.time_since_input_zerod < INPUT_PULSE_QUICKNESS_THRESHOLD) ? "yes" : "no"

				//	);
				//	settings->PushDebugString( game, RUGameDebugSettings::DP_SCRUM, activation_string.c_str() );
				//	MABLOGDEBUG( activation_string.c_str() );
				//	#endif
				//}

				// update rotation
				team_state.rotation_position = (right_stick - left_stick) * ROTATION_SCALE;

				// update the AI response to player rotation/wheel attempt.
				UpdateAIRotationPosition();
			}
		}

		//
		// if you have previously pushed then we make the accuracy makers stick where they are
		//

		float prev_time_as_radians = ((scrum_state.shunt_timer - game_time_step.delta_time.ToSeconds()) + timing_offset) / SCRUM_PUSH_FREQUENCY * PI;
		bool timer_looped = (MabMath::FMod(prev_time_as_radians, PI) > MabMath::FMod(time_as_radians, PI));

		if (team_state.has_pushed)
		{
			// reset push once the marker would be back to one edge
			if (timer_looped)
			{
//				if ( team_state.team->GetNumHumanPlayers() > 0 )
//				{
//					#ifdef ENABLE_OSD
//					RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
//					MabString activation_string( 128, "Scrum has pushed %d reset to false at %0.2f", i, game->GetSimTime()->GetAbsoluteTime().ToSeconds() );
//					settings->PushDebugString( game, RUGameDebugSettings::DP_SCRUM, activation_string.c_str() );
//					MABLOGDEBUG( activation_string.c_str() );
//					#endif
//				}

				team_state.has_pushed = false;
				game->GetEvents()->scrum_pre_push();
			}

			/// For the remote on network play - we step the marker back
			/// We know we are the remote if the local has visual pushed is false
			if ( game->GetNetworkState() != NULL && !team_state.has_visual_pushed )
			{
				team_state.visual_accuracy_marker = team_state.accuracy_marker;
			}
		}
		else {
			/// For local games update immediately before pushing as visual and network push happen on the same frame
			if ( game->GetNetworkState() == NULL )
				team_state.accuracy_marker = global_scrum_push_timing;
			else
				/// For network games, we set this AFTER we push the last known value as the visual push always comes first
				/// We want the value where the visual stopped
				team_state.accuracy_marker = scrum_state.last_global_scrum_push_timing;

			StoreAccuracyHistory(team_state);
		}

		if ( team_state.has_visual_pushed )
		{
			if (timer_looped)
			{
				team_state.has_visual_pushed = false;
			}
		} else if ( !team_state.has_pushed ) {
			team_state.visual_accuracy_marker = global_scrum_push_timing;
		}
	}

	scrum_state.last_global_scrum_push_timing = global_scrum_push_timing;

}
#endif

#if USING_CLIENT_SIDE_MINIGAMES
void RUGamePhaseScrum::UpdateInputAndPushAccuracy(const MabTimeStep& game_time_step)
{
	RUScrumState &scrum_state = GetCurrentScrum();

	// update the push timing

	float timing_offset = SCRUM_PUSH_FREQUENCY * 0.5f + 0.15f;

	float time_as_radians = (scrum_state.shunt_timer + timing_offset) / SCRUM_PUSH_FREQUENCY * PI;
	float global_scrum_push_timing = (1.0f + MabMath::Cos(time_as_radians)) * 0.5f;
	const float INPUT_MINIMUM_THRESHOLD = 0.5f;	// pulse at points worst than this are ignored

	for (int i = 0; i < 2; ++i)
	{

		RUScrumTeamState& team_state = i == 0 ? scrum_state.attacking : scrum_state.defending;


		//
		// Get analoge stick inputs (or fake ones from ai robots)
		//

		float left_stick = 0.0f;
		float right_stick = 0.0f;
		float left_stick_visual = 0.0f;
		float right_stick_visual = 0.0f;

		if (team_state.controlling_player_index != EHumanPlayerSlot::INVALID_HUMAN)
		{
			// check the input, large positive or negative on y axis will do
			left_stick = MabMath::Abs(game->GetHumanPlayer(team_state.controlling_player_index)->GetLeftStick().y);
			right_stick = MabMath::Abs(game->GetHumanPlayer(team_state.controlling_player_index)->GetRightStick().y);
			left_stick_visual = MabMath::Abs(game->GetHumanPlayer(team_state.controlling_player_index)->GetLeftStickLocal().y);
			right_stick_visual = MabMath::Abs(game->GetHumanPlayer(team_state.controlling_player_index)->GetRightStickLocal().y);

			// also check for l3 and r3 clicks, foolish humans and their spasming hands
			if (game->GetHumanPlayer(team_state.controlling_player_index)->IsPressed(ERugbyGameAction::SCRUM_PUSH_LEFT))
			{
				left_stick = 1.0f;
			}
			if (game->GetHumanPlayer(team_state.controlling_player_index)->IsPressed(ERugbyGameAction::SCRUM_PUSH_RIGHT) || game->GetHumanPlayer(team_state.controlling_player_index)->IsPressed(ERugbyGameAction::UP_ALT))
			{
				right_stick = 1.0f;
			}

			// also check for l3 and r3 clicks, foolish humans and their spasming hands
			if (game->GetHumanPlayer(team_state.controlling_player_index)->IsPressedLocal(ERugbyGameAction::SCRUM_PUSH_LEFT))
			{
				left_stick_visual = 1.0f;
			}
			if (game->GetHumanPlayer(team_state.controlling_player_index)->IsPressedLocal(ERugbyGameAction::SCRUM_PUSH_RIGHT) || game->GetHumanPlayer(team_state.controlling_player_index)->IsPressedLocal(ERugbyGameAction::UP_ALT))
			{
				right_stick_visual = 1.0f;
			}

			//MABLOGDEBUG( "%d %0.2f LOC %0.2f NET LAS=%0.4f RAS=%0.4f VLAS=%0.4f VRAS=%0.4f", i, game->GetSimTime()->GetAbsoluteTime().ToSeconds(), game->GetNetworkState() ? game->GetNetworkState()->GetInputAbsTime() : game->GetSimTime()->GetAbsoluteTime().ToSeconds(),
			//	left_stick, right_stick,
			//	left_stick_visual, right_stick_visual
			//);
		}
		else
		{
			GetAIPushInput(team_state, game_time_step, left_stick, right_stick);
		}

		//
		// check for stick pushes
		//

		// keep track of neutral
		const float NUETRAL_STICK_MAX_INPUT = 0.4f;
		if (left_stick + right_stick < NUETRAL_STICK_MAX_INPUT)
		{
			team_state.time_since_input_zerod = 0.0f;
		}
		else
		{
			team_state.time_since_input_zerod += game_time_step.delta_time.ToSeconds();
		}


		const float INPUT_PULSED_MIN_THRESHOLD = 0.9f;	// inputs combined must be greater than this
		const float INPUT_PULSE_QUICKNESS_THRESHOLD = 0.6f;	// pulse from neutral to full needs to be quicker than this in seconds

		bool input_pressed = (left_stick > INPUT_PULSED_MIN_THRESHOLD || right_stick > INPUT_PULSED_MIN_THRESHOLD) &&
			team_state.time_since_input_zerod < INPUT_PULSE_QUICKNESS_THRESHOLD;

		bool is_push_attempt_this_frame = !team_state.has_pushed && input_pressed;

		bool no_input_pressed = false;

		if (game->GetGameSettings().game_settings.network_game)
		{
			if (!team_state.has_pushed)
			{
				SSHumanPlayer* human = game->GetHumanPlayer(team_state.controlling_player_index);

				if (human)
				{
					if (IsShuntTimerLimitReached())
					{
						if (human->WasLastMinigameResultNoInput())
						{
							is_push_attempt_this_frame = true;
							no_input_pressed = true;
						}
					}
				}
			}
		}

		if (no_input_pressed)
		{
			if (team_state.push_attempt_last_frame)
			{
				is_push_attempt_this_frame = false;
				UE_LOG(LogDesync, Display, TEXT("RUGamePhaseScrum::UpdateInputAndPushAccuracy Ignoring scrum input as input was down last frame"));
			}

			team_state.push_attempt_last_frame = true;
		}
		else
		{
			UE_LOG(LogDesync, Display, TEXT("RUGamePhaseScrum::UpdateInputAndPushAccuracy Resetting input last frame check"));
			team_state.push_attempt_last_frame = false;
		}

		bool visual_input_pressed = (left_stick_visual > INPUT_PULSED_MIN_THRESHOLD || right_stick_visual > INPUT_PULSED_MIN_THRESHOLD) &&
			team_state.time_since_input_zerod < INPUT_PULSE_QUICKNESS_THRESHOLD;

		bool is_visual_push_attempt_this_frame = !team_state.has_visual_pushed && visual_input_pressed;


		//
		// apply results of successful push
		//

		const float SWEET_SPOT_SIZE = 0.01f;
		const float SWEET_SPOT_SCORE = 5.0f;
		//		const static float VISUAL_POWER_SET[2] = { 0.0f };
		//
		//		#ifdef ENABLE_OSD
		//		if ( team_state.team->GetNumHumanPlayers() > 0 )
		//		{
		//			RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
		//			MabString activation_string( 128, "%d %0.2f GSP=%0.2f AM=%0.2f VAM=%0.2f AIP=%0.2f",
		//				i, game->GetSimTime()->GetAbsoluteTime().ToSeconds(),
		//				global_scrum_push_timing, team_state.accuracy_marker, team_state.visual_accuracy_marker, team_state.ai_push_accuracy
		//			);
		//			settings->PushDebugString( game, RUGameDebugSettings::DP_SCRUM, activation_string.c_str() );
		//			MABLOGDEBUG( activation_string.c_str() );
		//		}
		//		#endif

				/// Lock the visual push to where we are now
		if (is_visual_push_attempt_this_frame && !team_state.has_visual_pushed)
		{
			float accuracy_rate = (0.5f - MabMath::Abs(global_scrum_push_timing - 0.5f)) * 2.0f;

			if (accuracy_rate < INPUT_MINIMUM_THRESHOLD)
			{
				is_visual_push_attempt_this_frame = false;
			}

			if (is_visual_push_attempt_this_frame)
			{
				//				if ( team_state.team->GetNumHumanPlayers() > 0 )
				//				{
				//					#ifdef ENABLE_OSD
				//					RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
				//					MabString activation_string( 128, "Scrum visual %d power set at %0.2f, GSP=%0.2f VAM=%0.2f SH%0.2f", i, game->GetSimTime()->GetAbsoluteTime().ToSeconds(),
				//						 global_scrum_push_timing, team_state.visual_accuracy_marker, scrum_state.shunt_timer
				//					);
				//					settings->PushDebugString( game, RUGameDebugSettings::DP_SCRUM, activation_string.c_str() );
				//					MABLOGDEBUG( activation_string.c_str() );
				//					#endif
				//					VISUAL_POWER_SET[i] = game->GetSimTime()->GetAbsoluteTime().ToSeconds();
				//				}

				team_state.has_visual_pushed = true;
			}
		}

		//bool winEvenIfNotPushing = true;

		//if(winEvenIfNotPushing && i==0 && !team_state.has_pushed)
		//	is_push_attempt_this_frame = true;

		/*
		// If we're not allowed a turn over, then basically make it impossible for the attacking team to lose here.
		// But only do it if there was a push attempt? Otherwise it looks a little weird.
		bool winEvenIfNotPushing = true;
		if(!game->GetGameTimer()->IsTurnoverAvailable() && (is_push_attempt_this_frame || winEvenIfNotPushing))
		{
			// Defending team
			if(i==1)
			{
				// Only set ours to 0.5 if the attacking team actually pushed, otherwise its a stalemate
				// Or if we set it to win even without pusing
				if(scrum_state.attacking.has_pushed || winEvenIfNotPushing)
					accuracy_rate = 0.5f;
			}
			// Attacking team
			else
			{
				// Make our pushes a little better than what they were
				accuracy_rate = MabMath::Max(accuracy_rate + 0.1f, 1.0f);
			}
		}*/


		if (is_push_attempt_this_frame)
		{
			if (game->GetGameSettings().game_settings.network_game)
			{
				team_state.wait_for_input = false;

				SSHumanPlayer* human = game->GetHumanPlayer(team_state.controlling_player_index);

				if (human)
				{
					if (human->WasLastMinigameResultNoInput())
					{
						team_state.accuracy_marker = 0.65f;
					}
					else
					{
						team_state.accuracy_marker = human->GetLastMinigameResult() / 100.0f;
					}

					UE_LOG(LogDesync, Display, TEXT("RUGamePhaseScrum::UpdateInputAndPushAccuracy Retrieving last minigame result Controller Index: %d, Player Slot: %d, Player Index: %d, Result: %f"), human->GetControllerIndex(), human->GetPlayerSlot(), human->GetPlayerIndex(), team_state.accuracy_marker);
				}
			}

			// convert the accuracy marker to a 0-1 score where the middle point is best
			float accuracy_rate = (0.5f - MabMath::Abs(team_state.accuracy_marker - 0.5f)) * 2.0f;

			/*if(!game->GetGameTimer()->IsTurnoverAvailable())
			{
				// Defending team
				if(i==1)
				{
					//accuracy_rate = 0.5f;
				}
				// Attacking team
				else
				{
					// Make our pushes a little better than what they were
					//accuracy_rate = MabMath::Max(accuracy_rate + 0.1f, 1.0f);
				}
			}*/

			// ignore pushes in the worst 50 area, so that button mashers can realise they are doing it wrong
			if (accuracy_rate < INPUT_MINIMUM_THRESHOLD)
			{
				is_push_attempt_this_frame = false;
			}

			team_state.has_pushed = true;

			float accuracy_range = SCRUM_MAX_ACCURATE_ZONE * GetTeamZonePower(i == 0);

			accuracy_rate = (accuracy_rate - (1.0f - accuracy_range)) / (accuracy_range);
			MabMath::Clamp(accuracy_rate, 0.0f, 1.0f);

			// check for sweet spot
			if (accuracy_rate > 1.0f - SWEET_SPOT_SIZE)
			{
				accuracy_rate = SWEET_SPOT_SCORE;
			}

			team_state.push_score = accuracy_rate;

			//if ( team_state.team->GetNumHumanPlayers() > 0 )
			//{
			//	#ifdef ENABLE_OSD
			//	RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
			//	MabString activation_string( 128, "Scrum actual %d power set at %0.2f, AM=%0.2f VAM=%0.2f SCORE=%0.2f SH%0.2f, NLA = %0.2f, DELTA = %0.2f, DT=%0.2f, %s %s",
			//		i, game->GetSimTime()->GetAbsoluteTime().ToSeconds(),
			//		team_state.accuracy_marker, team_state.visual_accuracy_marker, team_state.push_score, scrum_state.shunt_timer, perceived_lag,
			//		(game->GetSimTime()->GetAbsoluteTime().ToSeconds() - VISUAL_POWER_SET[i]) - perceived_lag,
			//		game->GetSimTime()->GetDeltaTime().ToSeconds(),
			//		(left_stick_visual > INPUT_PULSED_MIN_THRESHOLD || right_stick_visual > INPUT_PULSED_MIN_THRESHOLD) ? "yes" : "no",
			//		(team_state.time_since_input_zerod < INPUT_PULSE_QUICKNESS_THRESHOLD) ? "yes" : "no"

			//	);
			//	settings->PushDebugString( game, RUGameDebugSettings::DP_SCRUM, activation_string.c_str() );
			//	MABLOGDEBUG( activation_string.c_str() );
			//	#endif
			//}

			// update rotation
			team_state.rotation_position = (right_stick - left_stick) * ROTATION_SCALE;

			// update the AI response to player rotation/wheel attempt.
			UpdateAIRotationPosition();
		}

		//
		// if you have previously pushed then we make the accuracy makers stick where they are

		SSHumanPlayer* human = game->GetHumanPlayer(team_state.controlling_player_index);

		if (!team_state.has_pushed)
		{
			if (!team_state.has_visual_pushed)
			{
				team_state.accuracy_marker = global_scrum_push_timing;
			}

			/*if (game->GetGameSettings().game_settings.network_game)
			{
				if (human)
				{
					if (human->IsNetworkPlayer())
					{
						if (human->WasLastMinigameResultNoInput())
						{
							team_state.accuracy_marker = 0.65f;
						}
						else
						{
							team_state.accuracy_marker = human->GetLastMinigameResult() / 100.0f;
						}
					}
				}
			}*/
		}

		// Report what the indicator was at for network games.
		if (game->GetGameSettings().game_settings.network_game)
		{
			if (human)
			{
				if (!human->IsNetworkPlayer())
				{
					if (!IsShuntTimerLimitReached() || team_state.has_visual_pushed)
					{
						uint8 rounded_result = FMath::RoundToInt(team_state.accuracy_marker * 100.f);
						human->SetLastMinigameResult(rounded_result);
					}
					else
					{
						human->SetNoInputMinigameResult();
					}

					UE_LOG(LogDesync, Display, TEXT("RUGamePhaseScrum::UpdateInputAndPushAccuracy Updating last minigame result Controller Index: %d, Player Slot: %d, Player Index: %d, Result: %u"), human->GetControllerIndex(), human->GetPlayerSlot(), human->GetPlayerIndex(), human->GetLastMinigameResult());
				}
			}
		}
	}

	scrum_state.last_global_scrum_push_timing = global_scrum_push_timing;

}
#endif


void RUGamePhaseScrum::GetAIPushInput(RUScrumTeamState& team_state, const MabTimeStep& /*game_time_step*/, float& left_stick, float& right_stick)
{
	bool push =  team_state.accuracy_marker >= team_state.ai_push_accuracy;
	left_stick = push? 1.0f : 0.0f;
	right_stick = push? 1.0f : 0.0f;
}


FVector RUGamePhaseScrum::CheckScrumCollision(const FVector& scrum_position, const FVector& scrum_direction)
{
	// iterate all the registered collidables and check for collisions with our scrum
	// which we treat as a large rectangle
	//

	FVector adjusted_position = scrum_position;
	FVector scrum_left = scrum_direction.Cross(FVector::Y_AXIS).Unit();
	// hardciding scrum width and height. the calculated stuff is updated too frequently and gives jitter
	const float scrum_half_width = 1.0f;//(scrum_coll_width * 0.5f);
	const float scrum_half_height = 2.6f;//(scrum_coll_height * 0.5f);

	// first 4 static collidables are the goal posts
	// test only against them for now

	RUGameMovement::Collidables& collidables = game->GetMovement()->registered_collisions;
	for (int i = 0; i < 4; ++i)
	{
		const RUGameMovement::Collidable& collidable = collidables[i];

		// only handling circles for now
		if (collidable.type == RUGameMovement::Collidable::CIRCLE)
		{
			FVector relative_position = *collidable.position - adjusted_position;

			const float x_diff = relative_position.Dot(scrum_left);
			const float z_diff = relative_position.Dot(scrum_direction);
			const float x_dist = MabMath::Abs(x_diff);
			const float z_dist = MabMath::Abs(z_diff);

			if ( x_dist < (scrum_half_width + collidable.radius) &&
				 z_dist < (scrum_half_height + collidable.radius) )
			{
				// collision
				// WARNING: this collision resolution doesn't handle objects being completly inside each other

				// find the closest point on the rect to the circle center
				// which is the deepest point of penetration, we can
				// resolve this collision by pushing away in this direction

				FVector corners[] = {	- scrum_left * scrum_half_width + scrum_direction * scrum_half_height,
											  scrum_left * scrum_half_width + scrum_direction * scrum_half_height,
											  scrum_left * scrum_half_width - scrum_direction * scrum_half_height,
											- scrum_left * scrum_half_width - scrum_direction * scrum_half_height, };

				FVector closest_point = corners[0];
				float closest_point_dist_sqrd = FLT_MAX;

				for (int edge = 0; edge < 4; ++edge)
				{
					const FVector& a = corners[edge];
					const FVector& b = corners[(edge + 1) % 4];
					FVector e = (b - a);

					float edge_length = e.Magnitude();
					e.Normalise();
					float circle_dist = (relative_position - a).Dot(e);

					// clamp to on the edge
					MabMath::Clamp(circle_dist, 0.0f, edge_length);

					FVector point_on_edge = a + circle_dist * e;
					const FVector point_offset = (point_on_edge - relative_position);
					float this_points_dist_sqrd = point_offset.SquaredMagnitude();
					if (this_points_dist_sqrd < closest_point_dist_sqrd)
					{
						closest_point_dist_sqrd = this_points_dist_sqrd;
						closest_point = point_on_edge;
					}
				}

				//SIF_DEBUG_DRAW( SetSphere( 5634356, 0.1f, scrum_position + closest_point, MabColour::Cyan ) );

				// now resolve the collision by moving away from the circle in the direction of the closest point
				adjusted_position += (closest_point - relative_position).Unit() * (collidable.radius - MabMath::Sqrt(closest_point_dist_sqrd));

			}
		}

	}

	return adjusted_position;
}

void RUGamePhaseScrum::UpdateShuntMovement( FVector& scrum_angle, float delta_time )
{
	RUScrumState& scrum_state = GetCurrentScrum();
	if ( !MabMath::Feq( scrum_state.scrum_shunt, 0.0f)) // && !scrum_state.release_possible)
	{
		const float SHUNT_DECAY = 0.5f;
		const float SUPER_SHUNT_DECAY = 4.0f;

		float decay_rate = scrum_state.release_possible? SUPER_SHUNT_DECAY : SHUNT_DECAY;

		// Apply decay
		//scrum_state.scrum_shunt -= (scrum_state.scrum_shunt * SHUNT_DECAY) * delta_time;
		//MabMath::ClampLower(scrum_state.scrum_shunt, 0.0f);
		scrum_state.scrum_shunt = MabMath::Lerp( scrum_state.scrum_shunt, 0.0f, decay_rate * delta_time );

		float vel = scrum_state.scrum_shunt;
		const float SCRUM_MAX_SPEED = 2.0f;
		MabMath::Clamp(vel, -SCRUM_MAX_SPEED, SCRUM_MAX_SPEED);

		FVector new_scrum_position = scrum_state.scrum_centre + scrum_angle * (vel * delta_time);

		// check this potential new position for collisions with the goal posts and such
		// this function may adjust the position to slide around a collision
		new_scrum_position = CheckScrumCollision(new_scrum_position, scrum_angle);

		scrum_state.scrum_ground_gained += scrum_angle.Dot(new_scrum_position - scrum_state.scrum_centre);
		scrum_state.scrum_centre = new_scrum_position;

	}
}


void RUGamePhaseScrum::UpdateScrumWinConditions( FVector& /*scrum_angle*/, float delta_time )
{
#ifdef FREEZE_SCRUMS
	MABUNUSED( delta_time );
	return;
#endif

	MABUNUSED(delta_time);
	RUScrumState* scrum_state = &GetCurrentScrum();

#if USING_CLIENT_SIDE_MINIGAMES
	if (IsShuntTimerLimitReached())
	{
		if (IsWaitingForNetworkGameInput())
		{
			return;
		}
	}
#endif 

	RUScrumTeamState attacking_team_state = GetCurrentAttackingTeamState();
	RUScrumTeamState defending_team_state = GetCurrentDefendingTeamState();

	const float GROUND_GAINED_VICTORY_LIMIT = 2.0f;
	float MAX_SCRUM_DURATION = 3.5f;

	if (SIFGameHelpers::GAIsTutorialActive())
	{
		//Putting max scrum duration back to the original length to ensure the scrum tutorial works properly
		MAX_SCRUM_DURATION = 10.0f;
	}

	// Speed things up a bit for R7 games
	const float GROUND_GAINED_VICTORY_LIMIT_R7 = 1.0f;
	const float MAX_SCRUM_DURATION_R7 = 0.5f;

	bool isSevensGame = false; // Nick  WWS 7s to Womens // SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetGameMode() == GAME_MODE_SEVENS;

	// if the scrum has somehow made it into a try zone, then auto finish
	if (MabMath::Abs(scrum_state->scrum_centre.z) > game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals().y * 0.5f)
	{
		// When playing pro mode, our human player would have been the scrum half during the scrum so that we don't break the input
		// After the scrum we should remove that human, and assign our actual one.
#ifdef ENABLE_PRO_MODE
		if(game->GetGameSettings().game_settings.GetIsAProMode())
		{
			game->GetTeam(0)->AssignBestPlayer();
			game->GetTeam(1)->AssignBestPlayer();

			// Aaaand resume input, HFUnlockAll?!
			ARugbyCharacter* team1proPlayer = game->GetTeam(0)->GetProPlayer();
			if (team1proPlayer)
			{
				team1proPlayer->GetActionManager()->HFUnlockAll();
			}

			ARugbyCharacter* team2proPlayer = game->GetTeam(0)->GetProPlayer();
			if (team2proPlayer)
			{
				team2proPlayer->GetActionManager()->HFUnlockAll();
			}
		}
#endif

		if (scrum_state->scrum_ground_gained < 0.0f)
		{
			game->GetGameState()->SetAttackingTeam(defending_team_state.team);
		}

		TransitionToState(SP_STATE_WAIT_RELEASE);
	}

	if (MabMath::Abs(scrum_state->scrum_ground_gained) > (isSevensGame ? GROUND_GAINED_VICTORY_LIMIT_R7 : GROUND_GAINED_VICTORY_LIMIT))
	{
		// When playing pro mode, our human player would have been the scrum half during the scrum so that we don't break the input
		// After the scrum we should remove that human, and assign our actual one.
		if(game->GetGameSettings().game_settings.GetIsAProMode())
		{
			game->GetTeam(0)->AssignBestPlayer();
			game->GetTeam(1)->AssignBestPlayer();

			// Aaaand resume input, HFUnlockAll?!
			ARugbyCharacter* team1proPlayer = game->GetTeam(0)->GetProPlayer();
			if (team1proPlayer)
			{
				team1proPlayer->GetActionManager()->HFUnlockAll();
			}

			ARugbyCharacter* team2proPlayer = game->GetTeam(0)->GetProPlayer();
			if (team2proPlayer)
			{
				team2proPlayer->GetActionManager()->HFUnlockAll();
			}
		}

		if (scrum_state->scrum_ground_gained < 0.0f)
		{
			game->GetGameState()->SetAttackingTeam(defending_team_state.team);
		}

		TransitionToState(SP_STATE_WAIT_RELEASE);
		BallReleasePossible();
	}

	if ( setplay_time >= (isSevensGame ? MAX_SCRUM_DURATION_R7 : MAX_SCRUM_DURATION ))
	{
		// When playing pro mode, our human player would have been the scrum half during the scrum so that we don't break the input
		// After the scrum we should remove that human, and assign our actual one.
		if(game->GetGameSettings().game_settings.GetIsAProMode())
		{
			game->GetTeam(0)->AssignBestPlayer();
			game->GetTeam(1)->AssignBestPlayer();

			ARugbyCharacter* team1proPlayer = game->GetTeam(0)->GetProPlayer();
			if (team1proPlayer)
			{
				team1proPlayer->GetActionManager()->HFUnlockAll();
			}

			ARugbyCharacter* team2proPlayer = game->GetTeam(0)->GetProPlayer();
			if (team2proPlayer)
			{
				team2proPlayer->GetActionManager()->HFUnlockAll();
			}
		}

		if (scrum_state->scrum_ground_gained < 0.0f)
		{
			game->GetGameState()->SetAttackingTeam(defending_team_state.team);
		}

		TransitionToState(SP_STATE_WAIT_RELEASE);
		BallReleasePossible();
	}

	scrum_state->ball_target = scrum_state->scrum_ground_gained;

}


void RUGamePhaseScrum::UpdatePushingRotation(float delta_time)
{
	RUScrumState& scrum_state = GetCurrentScrum();

	// Clamp to something sensible
	// decay rotation
	scrum_state.attacking.rotation_position = MabMath::Lerp( scrum_state.attacking.rotation_position, 0.0f, ROTATION_DECAY * delta_time );
	scrum_state.defending.rotation_position = MabMath::Lerp( scrum_state.defending.rotation_position, 0.0f, ROTATION_DECAY * delta_time );

	scrum_state.scrum_angle += ( scrum_state.defending.rotation_position + scrum_state.attacking.rotation_position ) * ANGLE_CHANGE_RATE * delta_time;

	// Determine if we have wheeled
	const float SCRUM_WHEELED_ANGLE = PI / 2.0f;
	if ( scrum_state.scrum_angle > SCRUM_WHEELED_ANGLE || scrum_state.scrum_angle < -SCRUM_WHEELED_ANGLE )
	{
		ScrumWheeled( scrum_state );
		return;
	}

}

void RUGamePhaseScrum::UpdateBallPosition( float delta_time )
{
	if (game->GetGameState()->GetBallHolder() != NULL)
	{
		return;
	}

	RUScrumState* scrum_state = &GetCurrentScrum();
	RUScrumTeamState attacking_team_state = GetCurrentAttackingTeamState();

	FVector scrum_angle_vect;
	float play_dir_scrum_angle = scrum_state->scrum_angle + (attacking_team_state.team->GetPlayDirection() == ERugbyPlayDirection::NORTH ? 0.0f : PI);
	SSMath::AngleToMabVector3( play_dir_scrum_angle, scrum_angle_vect );
	scrum_angle_vect.Normalise();

	float adjusted_delta = 1.0f * delta_time;
	MabMath::Clamp( adjusted_delta, 0.0f, 1.0f );

	scrum_state->ball_state = MabMath::Lerp( scrum_state->ball_state, scrum_state->ball_target, adjusted_delta );

	static const FVector BALL_HEIGHT_OFFSET( 0.0f, BALL_RADIUS, 0.0f );

	const float MAX_BALL_DIST = 2.0f;

	float ball_offset = -scrum_state->ball_state;

	const float MAX_BALL_DIST_R7 = 1.0f;

	bool isSevensGame = false; // Nick  WWS 7s to Womens // SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetGameMode() == GAME_MODE_SEVENS;
	if (isSevensGame)
	{
		MabMath::Clamp(ball_offset, -MAX_BALL_DIST_R7, MAX_BALL_DIST_R7);
	}
	else
	{
		MabMath::Clamp(ball_offset, -MAX_BALL_DIST, MAX_BALL_DIST);
	}

	game->GetBall()->SetPositionAbsolute( scrum_state->scrum_centre + scrum_angle_vect * ball_offset + BALL_HEIGHT_OFFSET );
}

void RUGamePhaseScrum::ExecuteScrumAction( SSHumanPlayer* human_player, RUScrumEngageAction action )
{
	RUScrumTeamState* team_state = (human_player->GetTeam() == GetCurrentAttackingTeamState().team) ? &GetCurrentAttackingTeamState() : &GetCurrentDefendingTeamState();

	// Check if we are the scrum half / have control of the scrum
	if (human_player->GetPlayerSlot() != team_state->controlling_player_index)
	{
		return;
	}

	// These are both attacking actions only, currently.
	if (human_player->GetTeam() != game->GetGameState()->GetAttackingTeam())
	{
		return;
	}

	if ( setplay_state == SP_STATE_PUSHING && (action == SCRUM_ACTION_SCRUM_HALF || action == SCRUM_ACTION_PASS ) && GetCurrentScrum().release_possible)
	{
		TransitionToState(SP_STATE_WAIT_RELEASE);
	}
}

constexpr const RUZoneLocationBindState SCRUM_BIND_STATES[] = {
		{ "", "1" },
		{ "1", "23" },
		{ "12", "35" },
		{ "13", "24" },
		{ "123", "45" },
		{ "125", "3" },
		{ "134", "2" },
		{ "1234", "5" },
		{ "1235", "4" },
		{ "12345", "67" },
		{ "123456", "7" },
		{ "123457", "6" },
		{ "1234567", "8" },
		{ "12345678", "" }
};

constexpr const RUZoneLocationBindState SCRUM_BIND_R7_STATES[] = {
	{ "", "1" },
	{ "1", "23" },
};

constexpr const RUZoneLocationBindState SCRUMHALF_BIND_STATES[] =
{
	{ "", "1" },
	{ "1", "" }
};

void RUGamePhaseScrum::ApplyDefaultScrumConfiguration( RUScrumState& scrum_state, RUScrumTeamState& scrum_team_state, bool is_attack )
{	
	/// Clean out all scrum states
	for( size_t i = 0; i < scrum_team_state.locations.size(); i++ )
	{
		scrum_team_state.locations[i].positions.clear();
	}

	scrum_team_state.scrum_state = &scrum_state;

	IRUOrigin* scrum_origin = scrum_state.GetScrumOrigin();
	game->GetGameState()->SetFormationTarget( ERugbyFormationTarget::SCRUM_ORIGIN, scrum_origin );

	//-----------------------------------------
	// Prebind positions
	//-----------------------------------------

	RUZoneLocation* location = &scrum_team_state.locations[ SCRUMLOC_BOUND ];
	char idx = 0;

	// Offset for every player in the scrum to help match the two scrums up
	const char* TACKLE_JOINT = "tacklerAttach";

	RUGameAnimation* game_animation = game->GetAnimation();
	MABASSERT( game_animation );

	FVector spacing_offset_hack = FVector(0.0f, 0.0f, 0.15f);

	// Only add a row 2 and 3 if we're playing a normal 15 man union game. We only want a single 3 man row for sevens rugby.
	// Nick WWS 7s to Womens 13s //
	/*
	if(game->GetGameSettings().game_settings.GameModeIsR7())
	{
		// Row 1
		location->positions.push_back( RUZonePosition( scrum_origin, scrum_team_state.team, &scrum_team_state, location, idx++, -game_animation->Predict( SCRUM_BIND_ANIM_NAMES[0], false, TACKLE_JOINT, 0.0f ).Inverse().GetTranslation() + spacing_offset_hack, 0.0f, SCRUMLOC_BOUND, (void*) PP_LOOSEHEAD_PROP, 2 ) );
		location->positions.push_back( RUZonePosition( scrum_origin, scrum_team_state.team, &scrum_team_state, location, idx++, -game_animation->Predict( SCRUM_BIND_ANIM_NAMES[1], false, TACKLE_JOINT, 0.0f ).Inverse().GetTranslation() + spacing_offset_hack, 0.0f, SCRUMLOC_BOUND, (void*) PP_HOOKER, 1 ) );
		location->positions.push_back( RUZonePosition( scrum_origin, scrum_team_state.team, &scrum_team_state, location, idx++, -game_animation->Predict( SCRUM_BIND_ANIM_NAMES[2], false, TACKLE_JOINT, 0.0f ).Inverse().GetTranslation() + spacing_offset_hack, 0.0f, SCRUMLOC_BOUND, (void*) PP_TIGHTHEAD_PROP, 2 ) );

		APPLY_POSITIONCONFIG( (*location), SCRUM_BIND_STATES, SCRUMLOC_BOUND, NULL, "PreBind", true, false );
	}
	else
	{*/
		// Row 1
		location->positions.push_back( RUZonePosition( scrum_origin, scrum_team_state.team, &scrum_team_state, location, idx++, -game_animation->Predict( SCRUM_BIND_ANIM_NAMES[0], false, TACKLE_JOINT, 0.0f ).Inverse().GetTranslation() + spacing_offset_hack, 0.0f, SCRUMLOC_BOUND, (void*) PP_LOOSEHEAD_PROP, 2 ) );
		location->positions.push_back( RUZonePosition( scrum_origin, scrum_team_state.team, &scrum_team_state, location, idx++, -game_animation->Predict( SCRUM_BIND_ANIM_NAMES[1], false, TACKLE_JOINT, 0.0f ).Inverse().GetTranslation() + spacing_offset_hack, 0.0f, SCRUMLOC_BOUND, (void*) PP_HOOKER, 1 ) );
		location->positions.push_back( RUZonePosition( scrum_origin, scrum_team_state.team, &scrum_team_state, location, idx++, -game_animation->Predict( SCRUM_BIND_ANIM_NAMES[2], false, TACKLE_JOINT, 0.0f ).Inverse().GetTranslation() + spacing_offset_hack, 0.0f, SCRUMLOC_BOUND, (void*) PP_TIGHTHEAD_PROP, 2 ) );

		// Row 2
		location->positions.push_back( RUZonePosition( scrum_origin, scrum_team_state.team, &scrum_team_state, location, idx++, -game_animation->Predict( SCRUM_BIND_ANIM_NAMES[3], false, TACKLE_JOINT, 0.0f ).Inverse().GetTranslation() + spacing_offset_hack, 0.0f, SCRUMLOC_BOUND, (void*) PP_NUMBER_FOUR_LOCK_SECOND_ROW_TWELVE, 3 ) );
		location->positions.push_back( RUZonePosition( scrum_origin, scrum_team_state.team, &scrum_team_state, location, idx++, -game_animation->Predict( SCRUM_BIND_ANIM_NAMES[4], false, TACKLE_JOINT, 0.0f ).Inverse().GetTranslation() + spacing_offset_hack, 0.0f, SCRUMLOC_BOUND, (void*) PP_NUMBER_FIVE_LOCK_SECOND_ROW_ELEVEN, 3 ) );
		// WJS RLC Not needed?? location->positions.push_back( RUZonePosition( scrum_origin, scrum_team_state.team, &scrum_team_state, location, idx++, -game_animation->Predict( SCRUM_BIND_ANIM_NAMES[6], false, TACKLE_JOINT, 0.0f ).Inverse().GetTranslation() + spacing_offset_hack, 0.0f, SCRUMLOC_BOUND, (void*) PP_FLANKER, 3 ) );
		// WJS RLC Not needed?? location->positions.push_back( RUZonePosition( scrum_origin, scrum_team_state.team, &scrum_team_state, location, idx++, -game_animation->Predict( SCRUM_BIND_ANIM_NAMES[5], false, TACKLE_JOINT, 0.0f ).Inverse().GetTranslation() + spacing_offset_hack, 0.0f, SCRUMLOC_BOUND, (void*) PP_FLANKER, 3 ) );

		// Row 3
		location->positions.push_back( RUZonePosition( scrum_origin, scrum_team_state.team, &scrum_team_state, location, idx++, -game_animation->Predict( SCRUM_BIND_ANIM_NAMES[7], false, TACKLE_JOINT, 0.0f ).Inverse().GetTranslation() + spacing_offset_hack, 0.0f, SCRUMLOC_BOUND, (void*) PP_NUMBER_EIGHT_LOCK_FORWARD, 4 ) );

		APPLY_POSITIONCONFIG( (*location), SCRUM_BIND_STATES, SCRUMLOC_BOUND, NULL, "PreBind", true, false );
	//}

	//-----------------------------------------
	// Halfback scrum positions
	//-----------------------------------------

	idx = 0;
	location = &scrum_team_state.locations[ SCRUMLOC_HALFBACK ];
	FVector halfback_origin;
	if ( is_attack )
		halfback_origin.Set( 2.0f, 0.0f, 0.0f );
	else
		halfback_origin.Set( -2.0f, 0.0f, -2.0f );

	location->positions.push_back( RUZonePosition( scrum_origin, scrum_team_state.team , &scrum_team_state, location, idx++, halfback_origin, 0.0f, SCRUMLOC_HALFBACK ) );
	APPLY_POSITIONCONFIG( (*location), SCRUMHALF_BIND_STATES, SCRUMLOC_HALFBACK, NULL, "HalfBack", false, false );

	for( size_t i = 0; i < scrum_team_state.locations.size(); i++ )
	{
		MABVERIFY( scrum_team_state.locations[i].Validate() );
	}
}

///-------------------------------------------------------------------------
/// Have just exited from this game phase.
///-------------------------------------------------------------------------

void RUGamePhaseScrum::Exit()
{
	ClearDebug();

	game->GetMovement()->UnregisterStaticCollidable( this );

	game->GetEvents()->scrum_finish();
	scrum_states[curr_state]->Reset();

	if (game->GetHUDUpdater())
	{
		game->GetHUDUpdater()->SetScrumHUDVisible(false);
		//SIF_DEBUG_DRAW( RemoveText(455232) );
	}

	game->Get3DHudManager()->GetSetPieceIndicator()->SetVisible(false);

	game->GetGameState()->LockHumanMovement( false );
}

///-------------------------------------------------------------------------
/// Called every frame when this phase is active.
///-------------------------------------------------------------------------

void RUGamePhaseScrum::UpdateSimulation( const MabTimeStep& game_time_step )
{
#if USING_CLIENT_SIDE_MINIGAMES
	UE_LOG(LogDesync, Display, TEXT("RUGamePhaseScrum::UpdateSimulation"));

	RUScrumState* current_scrum = &GetCurrentScrum();

	if (game->GetGameSettings().game_settings.network_game)
	{
		if (IsShuntTimerLimitReached())
		{
			if (IsWaitingForNetworkGameInput())
			{
				// Reached the time limit but still waiting for input, lock the timer.
				current_scrum->shunt_timer_lock = false;
				UE_LOG(LogDesync, Display, TEXT("RUGamePhaseScrum::UpdateInputAndPushAccuracy Locking shunt timer"));

				UpdateInputAndPushAccuracy(game_time_step);
				return;
			}
			else
			{
				// Reached the time limit and have inputs, unlock the timer.
				current_scrum->shunt_timer_lock = true;
				UE_LOG(LogDesync, Display, TEXT("RUGamePhaseScrum::UpdateInputAndPushAccuracy Unlocking Shunt timer"));
			}
		}
	}
#endif

	UE_LOG(LogDesync, Display, TEXT("RUGamePhaseScrum::UpdateSimulation setplay_state: %d, engagement_state: %d"), setplay_state, engagement_state);

	switch( setplay_state )
	{
		case SP_STATE_INITIAL:				UpdateStateInitial();													break;
		case SP_STATE_WARP:					UpdateStateWarp();														break;
		case SP_STATE_PREBIND:				UpdateStatePreBind();													break;
		case SP_STATE_BINDING:				UpdateStateBinding();													break;
		case SP_STATE_ENGAGEMENTSEQUENCE:	UpdateStateEngagementSequence( game_time_step );						break;
		case SP_STATE_PUSHING:				UpdateStatePushing( game_time_step );									break;
		case SP_STATE_WAIT_RELEASE:			UpdateStateWaitRelease( game_time_step.delta_time.ToSeconds() );		break;

		case SP_STATE_RESET:				UpdateStateReset();					break;

		default:
			break;
	}

	// Can now un-suspend play!
	if (setplay_state == SP_STATE_ENGAGEMENTSEQUENCE)
	{
		is_ready = true;
	}

	// Store scrum marker history (will check to see if it has stored already for current frame)
	{
		RUScrumState &scrum_state = GetCurrentScrum();
		StoreAccuracyHistory( scrum_state.attacking );
		StoreAccuracyHistory( scrum_state.defending );
	}

	RUScrumState &scrum_state = GetCurrentScrum();
	if ( scrum_state.shunt_timer_lock )
	{
		scrum_state.shunt_timer += game_time_step.delta_time.ToSeconds();
		UE_LOG(LogDesync, Display, TEXT("RUGamePhaseScrum::UpdateSimulation Updating shunt timer: shunt timer: %f, delta time: %f"), scrum_state.shunt_timer, game_time_step.delta_time.ToSeconds());
	}

	/// Update the scrum collidable
	if (setplay_state >= SP_STATE_WARP && setplay_state < SP_STATE_WAIT_RELEASE)
	{
		UpdateScrumCollidable();
	}

	setplay_time += game_time_step.delta_time.ToSeconds();

	// If we're still in the phase, update debug. If phase changes this update will occur post Exit() call.
	if (game->GetGameState()->GetPhase() == RUGamePhase::SCRUM)
	{
		UpdateDebug(game_time_step.delta_time.ToSeconds());
	}

	//for (size_t i = 0; i < scrum_states.size(); i++) //#nirupam. Added this as workaround since Update is never called. In RC3 it gets called via GameObjectDatabase update in SifGameWorld
	//{
	//	scrum_states[i]->Update(game_time_step.delta_time.ToSeconds());
	//}

}



void RUGamePhaseScrum::TransitionToState(SCRUM_SETPLAY_STATE new_state)
{
	const static int override_push_timing = 3;

	setplay_state = new_state;
	setplay_time = 0.0f;
	if (setplay_state == SP_STATE_WAIT_RELEASE)
	{
		// This tells the scrum half the ball is out and they can do what they want with it
		game->GetEvents()->scrum_ball_out( game->GetGameState()->GetAttackingTeam() );
		game->GetEvents()->change_to_camera(GAME_CAM_INVALID);		
		game->Get3DHudManager()->GetSetPieceIndicator()->SetVisible(false);
	}
	else if (setplay_state == SP_STATE_ENGAGEMENTSEQUENCE)
	{
		engagement_pause_type = override_push_timing;//game->GetRNG()->RAND_RANGED_CALL(int, 5);
		engagement_state_transition_time = 0.0f;
		engagement_state = ENG_STATE_PRE;

		SSHumanPlayer* att_human = game->GetHumanPlayer(GetCurrentScrum().attacking.controlling_player_index);
		SSHumanPlayer* def_human = game->GetHumanPlayer(GetCurrentScrum().defending.controlling_player_index);
		bool has_local_controller = (att_human != nullptr && !att_human->IsNetworkPlayer()) || (def_human != nullptr && !def_human->IsNetworkPlayer());

		if (has_local_controller && game->GetHUDUpdater())
			game->GetHUDUpdater()->SetScrumHelperText( SCRUMTEXT_PUSHING );
	}
}


// Set up the HUD with sensible defaults.
void RUGamePhaseScrum::SetupHumanContollers()
{
	RUScrumTeamState& attacking_team = GetCurrentAttackingTeamState();
	RUScrumTeamState& defending_team = GetCurrentDefendingTeamState();

	// Default controller IDs
	attacking_team.controlling_player_index = EHumanPlayerSlot::INVALID_HUMAN;
	defending_team.controlling_player_index = EHumanPlayerSlot::INVALID_HUMAN;

	// When we play a pro game, assign the controller index only if our pro player is inside the scrum
	if(SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro())
	{
		if(!attacking_team.GetIsProPlayerInScrum() && !defending_team.GetIsProPlayerInScrum())
		{
			MABLOGDEBUG("Our pro player is NOT inside the scrum");
			//return;
		}
		else
		{
			if(attacking_team.GetIsProPlayerInScrum())
			{
				MABLOGDEBUG("Our pro player is inside the scrum on the attacking team");
				if(attacking_team.team->GetHumanPlayer(0))
					attacking_team.controlling_player_index = attacking_team.team->GetHumanPlayer(0)->GetPlayerSlot();
			}
			if(defending_team.GetIsProPlayerInScrum())
			{
				MABLOGDEBUG("Our pro player is inside the scrum on the defending team");
				if (defending_team.team->GetHumanPlayer(0))
					defending_team.controlling_player_index = defending_team.team->GetHumanPlayer(0)->GetPlayerSlot();
			}
		}

		return;
	}

	SET_CHANGEPLAYER_SECTION( game, "SCRUM");
	// Set the colour of the player controlling the scrum
	for (int i = 0; i < attacking_team.team->GetNumHumanPlayers(); ++i)
	{
		SSHumanPlayer* pHumanPlayer = attacking_team.team->GetHumanPlayer(i);

		if (pHumanPlayer->GetRugbyCharacter() && (pHumanPlayer->GetRugbyCharacter()->GetRole()->RTTGetType() == RURoleScrumHalfBack::RTTGetStaticType()))
		{
			attacking_team.controlling_player_index = pHumanPlayer->GetPlayerSlot();
		}
		else
		{
			pHumanPlayer->AssignBestPlayer(game);

			// Dewald WW - Check if we actually assigned a player to this human, if we have that means we have a real live human now controlling this side.
			// Set the controlling index then.
			if ( pHumanPlayer->GetRugbyCharacter()
				&& (attacking_team.controlling_player_index == EHumanPlayerSlot::INVALID_HUMAN || pHumanPlayer->GetRugbyCharacter()->GetRole()->RTTGetType() == RURoleScrumHalfBack::RTTGetStaticType()))
			{
				attacking_team.controlling_player_index = pHumanPlayer->GetPlayerSlot();
			}
		}
	}

	for (int i = 0; i < defending_team.team->GetNumHumanPlayers(); ++i)
	{
		SSHumanPlayer* pHumanPlayer = defending_team.team->GetHumanPlayer(i);

		if (pHumanPlayer->GetRugbyCharacter() && (pHumanPlayer->GetRugbyCharacter()->GetRole()->RTTGetType() == RURoleScrumHalfBack::RTTGetStaticType()))
		{
			defending_team.controlling_player_index = pHumanPlayer->GetPlayerSlot();
		}
		else
		{
			pHumanPlayer->AssignBestPlayer(game);

			// Dewald WW - Check if we actually assigned a player to this human, if we have that means we have a real live human now controlling this side.
			// Set the controlling index then.
			if ( pHumanPlayer->GetRugbyCharacter()
				&& (defending_team.controlling_player_index == EHumanPlayerSlot::INVALID_HUMAN || pHumanPlayer->GetRugbyCharacter()->GetRole()->RTTGetType() == RURoleScrumHalfBack::RTTGetStaticType()))
			{
				defending_team.controlling_player_index = pHumanPlayer->GetPlayerSlot();
			}
		}
	}
	SET_CHANGEPLAYER_SECTION( game, NULL);

}

void RUGamePhaseScrum::ScrumWheeled( RUScrumState& scrum_state )
{
	RUScrumTeamState* attacking_team_state = &GetCurrentAttackingTeamState();
	RUScrumTeamState* defending_team_state = &GetCurrentDefendingTeamState();

	// If the attacking team was in possession, give to the defending team.
	RUTeam* restart_team = ( scrum_state.ball_target < 0.0f ) ? attacking_team_state->team : defending_team_state->team;

	// Set up the correct feeding team
	game->GetGameState()->SetAttackingTeam( restart_team );

	// reclamp to 5 metres
	FVector restart_position = game->GetBall()->GetCurrentPosition();
	FieldExtents extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	MabMath::Clamp( restart_position.z, -(extents.y/2.0f - 5.0f), (extents.y/2.0f - 5.0f) );
	MabMath::Clamp( restart_position.x, -(extents.x/2.0f - 5.0f), (extents.x/2.0f - 5.0f) );

	game->GetGameState()->SetPlayRestartPosition( restart_position );

	// Exit the current one, to reset it's running state
	GetCurrentScrum().Exit();

	// Fire the reset event, which will reset players, amongst other things.
	game->GetEvents()->scrum_reset(restart_team, SRSC_WHEELED);

	TransitionToState(SP_STATE_RESET);
	reset_scrum_state = true;
}

void RUGamePhaseScrum::ScrumCollapsed( RUTeam* collapse_team)
{
	RUScrumTeamState* attacking_team_state = &GetCurrentAttackingTeamState();
	game->GetEvents()->scrum_reset( attacking_team_state->team, SRSC_COLLAPSED);

	RUScrumState* current_scrum = &GetCurrentScrum();
	current_scrum->collapse_count++;
	current_scrum->last_collapse_team = collapse_team;

	// If we've had X collapses in a row, call a penalty
	if ( current_scrum->collapse_count >= COLLAPSES_BEFORE_PENALTY )
	{
		game->GetEvents()->penalty( collapse_team->GetPlayerByPosition( PP_SCRUM_HALF ), nullptr, game->GetBall()->GetCurrentPosition(), PENALTY_REASON_COLLAPSING_SCRUM );
		game->GetGameState()->SetPhase( RUGamePhase::PLAY );
		return;
	}

	static const char* COLLAPSE_ANIM_REQ = "scrum_collapse";
	ApplyAnimationToAllPlayers( COLLAPSE_ANIM_REQ );

	TransitionToState(SP_STATE_RESET);
	reset_scrum_state = true;
}


void RUGamePhaseScrum::BallReleasePossible()
{
	if (!GetCurrentScrum().release_possible)
	{
		GetCurrentScrum().release_possible = true;
        if(game)
        {
			if (game->GetEvents() && game->GetGameState())
			{
				game->GetEvents()->scrum_ball_release_possible(game->GetGameState()->GetAttackingTeam());

				if (game && game->Get3DHudManager() && game->Get3DHudManager()->GetSetPieceIndicator())
					game->Get3DHudManager()->GetSetPieceIndicator()->SetVisible(false);
			}

            // Hud might not exist because of enormous training HUD hax :/
            if ( game->GetHUDUpdater() )
                game->GetHUDUpdater()->SetScrumHUDVisible(false);
        }
		
	}
}


void RUGamePhaseScrum::UpdateAIDecision( RUScrumState& scrum_state )
{
	bool proGame = SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro();
	// AI Logic tucked on the end
	if (
		// If the attacking team has no human players, or if it does and the pro player is outside of the scrum, act like AI
		( game->GetGameState()->GetAttackingTeam()->GetNumHumanPlayers() == 0 || (proGame && !GetCurrentAttackingTeamState().GetIsProPlayerInScrum()) )
		&& scrum_state.release_possible
		&& GetCurrentAttackingTeamState().locations[ SCRUMLOC_HALFBACK ].positions[0].player->GetMovement()->HasReachedWaypoint()
		)
	{
		TransitionToState(SP_STATE_WAIT_RELEASE);
	}
}

bool RUGamePhaseScrum::IsShuntTimerLimitReached()
{
	RUScrumState& scrum_state = GetCurrentScrum();
	UE_LOG(LogDesync, Display, TEXT("RUGamePhaseScrum::IsShuntTimerLimitReached %f > %f"), scrum_state.shunt_timer, SCRUM_PUSH_FREQUENCY);
	return scrum_state.shunt_timer > SCRUM_PUSH_FREQUENCY;
}

bool RUGamePhaseScrum::IsWaitingForNetworkGameInput()
{
	UE_LOG(LogDesync, Display, TEXT("RUGamePhaseScrum::IsWaitingForNetworkGameInput %d | %d"), GetCurrentAttackingTeamState().wait_for_input, GetCurrentDefendingTeamState().wait_for_input);
	if (game->GetGameSettings().game_settings.network_game)
	{
		if (GetCurrentAttackingTeamState().wait_for_input || GetCurrentDefendingTeamState().wait_for_input)
		{
			return true;
		}
	}

	return false;
}

void RUGamePhaseScrum::ResetTeamStatesOnTimerLimitReached()
{
	RUScrumState& scrum_state = GetCurrentScrum();
	for (int i = 0; i < 2; ++i)
	{
		RUScrumTeamState& team_state = i == 0 ? scrum_state.attacking : scrum_state.defending;

		team_state.has_pushed = false;
		team_state.has_visual_pushed = false;
		game->GetEvents()->scrum_pre_push();

		team_state.wait_for_input = true;

		//if ( team_state.team->GetNumHumanPlayers() > 0 )
//				{
//					#ifdef ENABLE_OSD
//					RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
//					MabString activation_string( 128, "Scrum has pushed %d reset to false at %0.2f", i, game->GetSimTime()->GetAbsoluteTime().ToSeconds() );
//					settings->PushDebugString( game, RUGameDebugSettings::DP_SCRUM, activation_string.c_str() );
//					MABLOGDEBUG( activation_string.c_str() );
//					#endif
//				}
	}
}

void RUGamePhaseScrum::UpdateDebug( float delta_time )
{
	MABUNUSED( delta_time );

#if defined(ENABLE_GAME_DEBUG_MENU) && defined(ENABLE_SETPLAY_DEBUG)
	long id = *(long*)this;
	int index = 0;

	RUScrumTeamState& attacking_team = GetCurrentAttackingTeamState();
	RUScrumTeamState& defending_team = GetCurrentDefendingTeamState();

	SIF_DEBUG_DRAW( SetBox( id + index++, GetCurrentScrum().scrum_centre, FVector( 0.1f, 0.1f, 0.1f ), MabColour::Cyan ) );


	static const float X_OFFSET = 5.0f;
	static const float Y_OFFSET = 310.0f;
	static const float Y_CHANGE = 10.0f;

	float y_pos = Y_OFFSET;

	//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "StateTime : %f", setplay_time) );
	//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "StateTransitionTime : %f", engagement_state_transition_time) );
	SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "EngagementState : %d", engagement_state) );
	SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "State : %d", engagement_state) );
	SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Att Score : %.2f", attacking_team.push_score) );
	SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Def Score : %.2f", defending_team.push_score) );

	SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Att Ability : %.2f", GetScrumAbility(true)) );
	SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Def Ability : %.2f", GetScrumAbility(false)) );

	//if ( engagement_state > 0 )
	{
		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Scrum Angle : %f", GetCurrentScrum().scrum_angle) );

		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Att CollPower : %f", attacking_team.collapse_power ) );
		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Def CollPower : %f", defending_team.collapse_power ) );
		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Momentum : %f", GetMomentum() ) );

		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Collapse_count : %d", GetCurrentScrum().collapse_count ) );
		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "LastCollapseTeamValid: %s", GetCurrentScrum().last_collapse_team != NULL ? "Exists" : "NULL") );
		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "LastCollapseTeam: %s", GetCurrentScrum().last_collapse_team == attacking_team.team ? "Attacking" : "Defending") );

		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "A LS : %s", attacking_team.l_stick_valid ? "True" : "False") );
		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "A RS : %s", attacking_team.r_stick_valid ? "True" : "False") );
		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "A Eng : %s", attacking_team.EngageValid() ? "True" : "False") );

		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "D LS : %s", defending_team.l_stick_valid ? "True" : "False") );
		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "D RS : %s", defending_team.r_stick_valid ? "True" : "False") );
		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "D Eng : %s", defending_team.EngageValid() ? "True" : "False") );

		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Att Accuracy : %f", attacking_team.engagement_accuracy ) );
		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Def Accuracy : %f", defending_team.engagement_accuracy ) );
		////SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Att Hit : %s", attacking_team.engagement_button_registered ? "True" : "False" ) );
		////SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Def Hit : %s", defending_team.engagement_button_registered ? "True" : "False" ) );
		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Att Pushing : %f", attacking_team.team_power) );
		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Def pushing : %f", defending_team.team_power) );
		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Acceleration: %f", decay_rate ) );

		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Power DIFF: %f", GetCurrentScrum().scrum_power) );
		//SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Ball State: %f", GetCurrentScrum().ball_state ) );
		//
		////SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Att Power : %f", ( attacking_team.team_power + (attacking_team.engagement_accuracy) / 4.0f ) / 2.0f ));
		////SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Def Power : %f", ( defending_team.team_power + (defending_team.engagement_accuracy) / 4.0f ) / 2.0f ));

		////SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Ball State: %f", GetCurrentScrum().ball_state ) );
		////SETDEBUGTEXTWORLD( id + index++, FVector(X_OFFSET, y_pos += Y_CHANGE, 0.0f), MabString(0, "Ball power: %f", GetCurrentScrum().scrum_power) );
	}

	n_debug_text = index;
#endif
}

void RUGamePhaseScrum::ClearDebug()
{
#if defined(ENABLE_GAME_DEBUG_MENU) && defined(ENABLE_SETPLAY_DEBUG)
	long id = *(long*)this;
	for (int i = 0; i < n_debug_text; ++i)
		SIF_DEBUG_DRAW( RemoveText( id + i ) );
#endif
}

void RUGamePhaseScrum::Reset()
{
	for( size_t i = 0; i < scrum_states.size(); i++ )
	{
		scrum_states[i]->Reset();
	}
}

bool RUGamePhaseScrum::IsStandardPlay()
{
	return setplay_state > SP_STATE_ENGAGEMENTSEQUENCE;
}

/// Update the collidable volume for the scrum
void RUGamePhaseScrum::UpdateScrumCollidable()
{
	SIFRugbyCharacterList scrum_players;
	game->GetStrategyHelper()->GetTeamRoles( game->GetGameState()->GetAttackingTeam(), RURoleScrum::RTTGetStaticType(), scrum_players );
	game->GetStrategyHelper()->GetTeamRoles( game->GetGameState()->GetDefendingTeam(), RURoleScrum::RTTGetStaticType(), scrum_players );

	MabRect<float> bounds;
	game->GetSpatialHelper()->GetPlayerGroupXZBounds( scrum_players, bounds );
	scrum_coll_centre.Set( (bounds.left + bounds.right) * 0.5f, 0.0f, (bounds.top + bounds.bottom) * 0.5f );
	scrum_coll_width  = MabMath::Fabs( bounds.left - bounds.right );
	scrum_coll_height = MabMath::Fabs( bounds.top - bounds.bottom );
}

float RUGamePhaseScrum::GetTeamPower( bool attacking )
{
	// Returns 0-to-cap power for each team.
	return attacking ? GetCurrentAttackingTeamState().marker_pos : 1.0f - GetCurrentDefendingTeamState().marker_pos;
}

float RUGamePhaseScrum::GetScrumAbility( bool attacking )
{
	return attacking ? GetCurrentAttackingTeamState().GetScrumAbility() : GetCurrentDefendingTeamState().GetScrumAbility();
}

float RUGamePhaseScrum::GetFocusValue( bool attacking )
{
	if ( setplay_state < SP_STATE_PUSHING )
		return 0.0f;
	else
		return attacking ? GetCurrentAttackingTeamState().engage_focus : GetCurrentDefendingTeamState().engage_focus;
}

#if PLATFORM_WINDOWS || PLATFORM_XBOX360 || PLATFORM_XBOXONE
#pragma warning( disable :4355 )
#endif

RUScrumState::RUScrumState()
: scrum_centre( FVector::ZeroVector )
, ball_offset( FVector::ZeroVector )
, ball_state( 0.0f )
, ball_target( 0.0f )
, release_possible( false )
, ball_in( false )
, scrum_angle( 0.0f )
, scrum_angle_mod( 0.0f )
, scrum_shunt( 0.0f )
, scrum_ground_gained( 0.0f )
, shunt_timer( 0.0f )
, shunt_timer_lock( false)
, shunt_count( 0 )
, scrum_constant_motion( 0.0f )
, last_global_scrum_push_timing( 0.0f )
, scrum_origin( this )
, attacking()
, defending()
, scrum( NULL )
, running( false )
, initialised( false )
, collapse_count( 0 )
, last_collapse_team( NULL )

{
}

RUScrumState::~RUScrumState()
{
}

void RUScrumState::Destroy()
{
	// TODO : Do I need to destroy remove from the database?
}

void RUScrumState::Enter()
{
	MABASSERT( scrum != NULL );
	scrum_centre = scrum->GetGame()->GetGameState()->GetPlayRestartPosition();
	scrum_angle = 0.0f;
	scrum_angle_mod = 0.0f;
	scrum_shunt = 0.0f;
	scrum_constant_motion = 0.0f;
	ball_offset.Set(0.0f, 0.0f, 0.0f);
	ball_state = 0.0f;
	ball_target = 0.0f;
	release_possible = false;
	ball_in = false;
	running = true;
	initialised = false;

	shunt_timer = 0.0f;
	shunt_timer_lock = false;
	shunt_count = 0;
	scrum_ground_gained = 0.0f;
	last_global_scrum_push_timing = 0.0f;

	attacking.accuracy_marker = 0.0f;
	attacking.accuracy_marker_history.clear();
	attacking.time_since_input_zerod = 0.0f;
	attacking.has_pushed = attacking.has_visual_pushed = false;
	attacking.push_score = 0.0f;
	defending.accuracy_marker = 0.0f;
	defending.accuracy_marker_history.clear();
	defending.time_since_input_zerod = 0.0f;
	defending.has_pushed = defending.has_visual_pushed = false;
	defending.push_score = 0.0f;
}

void RUScrumState::Update( float /*delta_time*/ )
{
	/// Update our running status
	/// If all scrums have no config then abort
	if ( initialised && running && attacking.AreAllPositionsFreeNow() && defending.AreAllPositionsFreeNow() ) {
		Exit();
	}

	if ( !running )
		return;
}

void RUScrumState::Exit()
{
	//MABLOGDEBUG( "Stopping scrum" );
	initialised = false;
	running = false;
}

void RUScrumState::Reset()
{
	// If we're resetting due to wheeling/collapse, keep this data around
	if ( !scrum->GetResetState() )
	{
		collapse_count = 0;
		last_collapse_team = NULL;
	}

	Exit();
	initialised = false;
	running = false;
	attacking.Reset();
	defending.Reset();
}

bool RUScrumTeamState::AreAllPositionsFreeNow()
{
	for( size_t i = 0; i < locations.size(); i++ )
	{
		/// It is free if the state os currently NULL or there are no slots assigned to the config
		if ( !(locations[i].bind_state == NULL || strlen( locations[i].bind_state->config ) == 0 )  )
			return false;
	}

	return true;
}


RUScrumTeamState::RUScrumTeamState():
players_in_scrum(),
hud( NULL ),
team( NULL ),
scrum( NULL ),
scrum_state( NULL ),
locations(),
rotation_position(0.0),
marker_pos( 0.0f ),
last_l_input( 0.0f ),
last_r_input( 0.0f ),
l_stick_valid( false ),
r_stick_valid( false ),
l_stick_accel_max(0.0f),
r_stick_accel_max(0.0f),
wait_for_input(true),
accuracy_marker(0.0f),
time_since_input_zerod(0.0f),
has_pushed(false),
push_score(0.0f),
engage_attempted(),
engage_focus( 0.0f ),
collapse_power( 1.0f ),
collapse_timer(),
controlling_player_index(EHumanPlayerSlot::FIRST_HUMAN),
ai_push_accuracy(0.0f)
{
	players_in_scrum.reserve( /*MAX_PLAYERS_PER_SIDE_IN_SCRUM*/ SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetNumberOfPlayersInScrum() );
}

RUScrumState::ScrumOrigin::ScrumOrigin( RUScrumState* scrum_state ):
scrum_state( scrum_state )
{

}

FVector RUScrumState::ScrumOrigin::GetOrigin() const
{
	return scrum_state->scrum_centre;
}

void RUScrumTeamState::Reset()
{
	players_in_scrum.clear();

	for( int i = 0; i < SCRUMLOC_COUNT; i++ )
	{
		locations[i].Reset();
	}

	rotation_position = 0.0f;

	// Engagement/pushing vars
	marker_pos = 0.0f;
	l_stick_valid = false;
	r_stick_valid = false;
	collapse_power = 1.0f;

	engage_attempted.Initialise( scrum->GetGame()->GetSimTime() );
	engage_attempted.SetEnabled( false );

	collapse_timer.Initialise( scrum->GetGame()->GetSimTime() );
	collapse_timer.SetEnabled( false );
}

bool RUScrumTeamState::GetIsProPlayerInScrum()
{
	bool isInside = false;
	for( size_t i  = 0; i < locations[SCRUMLOC_BOUND].positions.size(); i++ )
	{
		if(!locations[SCRUMLOC_BOUND].positions[i].player)
			continue;

		if(SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(locations[SCRUMLOC_BOUND].positions[i].player))
		{
			isInside = true;
			break;
		}
	}

	// Check the halfback location as well since that will be 'in' the scrum
	for( size_t i  = 0; i < locations[SCRUMLOC_HALFBACK].positions.size(); i++ )
	{
		if(!locations[SCRUMLOC_HALFBACK].positions[i].player)
			continue;

		if(SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(locations[SCRUMLOC_HALFBACK].positions[i].player))
		{
			isInside = true;
			break;
		}
	}

	MABLOGDEBUG("Pro player is %s the scrum", (isInside ? "inside" : "outside"));
	return isInside;
}

ARugbyCharacter* RUScrumTeamState::GetNo8Player()
{
	// get him by number first
	bool isSevensGame = false; // Nick WWS 7s to Womens 13s // SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GameModeIsR7();

	// In sevens our back man is our hooker. 15's it's number eight.
	PLAYER_POSITION positionToSearch	= isSevensGame ? PP_HOOKER	: PP_NUMBER_EIGHT_LOCK_FORWARD;
	// Our fall back is the index in the scrum positions, 2 for hooker in sevens, and 7 for num eight in 15's
	int				locPos				= isSevensGame ? 2			: 7;

	// First try to find our player in our team
	ARugbyCharacter* player = team->GetPlayerByPosition(positionToSearch);

	// Sometimes that player wont be found, because they may have been sent off. So check their placement in the scrum instead.
	if (!player)
		player = locations[SCRUMLOC_BOUND].positions[locPos].player;

	MABASSERT(player);
	return player;
}


ARugbyCharacter* RUScrumTeamState::GetScrumHalfPlayer()
{

	// try getting scrum half by position
	ARugbyCharacter* player = locations[SCRUMLOC_HALFBACK].positions[0].player;

	// if not try to get him by player position
	if (!player)
		player = team->GetPlayerByPosition(PP_SCRUM_HALF);

	MABASSERT(player);
	return player;
}



float RUGamePhaseScrum::GetEstimatedTimeTillBallBackInPlay()
{
	if ( setplay_state <= SP_STATE_ENGAGEMENTSEQUENCE )
		return 15.0f;

	if ( setplay_state == SP_STATE_PUSHING )
		return 4.0f;

	if ( setplay_state == SP_STATE_WAIT_RELEASE )
		return 1.0f;

	return 0.0f;
}


float RUGamePhaseScrum::GetTeamZonePower(bool attacking)
{
	float ability = (attacking? GetCurrentScrum().attacking : GetCurrentScrum().defending).GetScrumAbility();

	const float ABILITY_TO_POWER = 1.0f;
	const float MIN_POWER = 0.2f;
	const float MAX_POWER = 1.0f;

	ability = MIN_POWER + ability * ABILITY_TO_POWER;
	MabMath::ClampUpper(ability, MAX_POWER);

	return ability;
}

void RUGamePhaseScrum::StoreAccuracyHistory( RUScrumTeamState &team_state )
{
	/// Pop elements from the history that are no longer in the history window
	float curr_abs_time = game->GetSimTime()->GetAbsoluteTime().ToSeconds();
	while( !team_state.accuracy_marker_history.empty() && (curr_abs_time - team_state.accuracy_marker_history.back().abs_time) > MAX_ACCURACY_HISTORY_TIME )
	{
		team_state.accuracy_marker_history.pop_back();
	}

	/// Make sure that the current time hgas not yet been stored
	if ( team_state.accuracy_marker_history.empty() || team_state.accuracy_marker_history.front().abs_time != curr_abs_time )
	{
		team_state.accuracy_marker_history.push_front( RUScrumTeamState::MARKER_ENTRY( curr_abs_time, team_state.accuracy_marker ) );
	}
}
