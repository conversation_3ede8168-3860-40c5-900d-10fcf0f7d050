// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIScreenTeamLineup.h"
#include "RugbyGameInstance.h"

// WW UI Plugin
#include "WWUITranslationManager.h"
#include "WWUIRichTextBlockWithTranslate.h"
#include "WWUIFunctionLibrary.h"

#include "Rugby/Utility/Helpers/SIFGameHelpers.h"
#include "Rugby/Utility/Helpers/SIFInGameHelpers.h"
#include "Rugby/Utility/Helpers/SIFMatchMakingHelpers.h"
#include "Rugby/Utility/Helpers/SIFUIHelpers.h"

#include "WWUIScrollBox.h"

#include "Rugby/Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RUDBHelperInterface.h"

#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "UI/GeneratedHeaders/Animations_UI_Namespace.h"
#include "UI/GeneratedHeaders/WWUIScreenTeamLineup_UI_Namespace.h"

#include "Engine/Public/TimerManager.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Image.h"
#include "Utility/Helpers/SIFRichPresenceHelpers.h"
#include "UI/Components/WWUIUserWidgetPlayerFaceImage.h"
#include "CoreMinimal.h"
#include "WWUIListField.h"
#include "WidgetSwitcher.h"
#include "Kismet/GameplayStatics.h"
#include "SIFMissingControllerListener.h"

#include "Utility/Helpers/SIFPlayerHelpers.h"
#include "Border.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/SIFGameWorld.h"
#include "Utility/Helpers/SIFAudioHelpers.h"
#include "UI/Components/AutoScrollingBlock.h"
#include "Match/RugbyUnion/RUDBPlayer.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RUDatabaseCaches.h"

//< Online timers. >
int32 ONLINE_SCREEN_TIMEOUTTeamLineup = 12.0f;
//int32 ONLINE_ONTO_SECOND_TEAM = 6;
//ONLINE_SCREEN_TIMEOUT_NAME = "online_duration";
//ONLINE_ONTO_SECOND_TEAM_NAME = "online_onto_second_team";

//< Theses will be used for system events. >
const char* EVENT_TEAM_MANAGEMENT_PLAYER_READY = "team_management_ready";
const char* EVENT_TEAM_MANAGEMENT_PLAYER_READY_CHANGED = "team_management_ready_changed";
const char* charNetwork = "network";

int32 SEVENS_SIZE = 7;
int32 FIFTEENS_SIZE = 15;

ETeamLineupStates STATE = ETeamLineupStates::STATIC_LINEUP;
ETeamLineupModes MODE = ETeamLineupModes::PRE_GAME;

#define SCROLLBOX_PLAYER_SCROLL_FIELD_WIDGET_NAME "BP_ScrollingBlock"

void UWWUIScreenTeamLineup::RegisterFunctions()
{
	AddInputAction(FString("UI_Select"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamLineup::OnActionSkipCutscene), false, true);
}


//------------------------------------------------------------------------------
//------------------------------------------------------------------------------

void UWWUIScreenTeamLineup::Startup(UWWUIStateScreenData * InData)
{
	bIsSomeAccountRestricted = SIFApplication::GetApplication()->IsAnyUserRestricted();
	bIsNonPrimaryRestricted = SIFApplication::GetApplication()->IsNonPrimaryUserRestricted();

	m_bNetworkGame = (strcmp(SIFGameHelpers::GAGetGameType(), charNetwork) == 0);
	
	if (m_bNetworkGame)
	{
		if (UWWUIRichTextBlockWithTranslate* helptip = Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTeamLineup_UI::LegendText)))
		{
			helptip->SetText("[ID_COPYRIGHT_HELP]");
		}
	}

	//Populate Player Lists
	UWWUIScrollBox* pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTeamLineup_UI::ScrollBoxPlayersRight));
	if (pScrollBox)
	{
		PopulatePlayerList(pScrollBox, SSTEAMSIDE::SIDE_B);
	}

	pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTeamLineup_UI::ScrollBoxPlayersLeft));
	if (pScrollBox)
	{
		PopulatePlayerList(pScrollBox, SSTEAMSIDE::SIDE_A);

		if (!m_bNetworkGame)
		{
			for (int i = 0; i < SIFApplication::GetApplication()->GetNumLocalPlayers(); i++)
			{
				pScrollBox->FocusFirstListField(GetPlayerController(i));
			}
		}
	}

	UWWUIFunctionLibrary::PlayAnimation(this, Animations_UI::BP_UIScreenTeamLineup::FadeIn);
	DisplayingReserves = false;

	ugcRestrictionHandle = SIFApplication::GetApplication()->OnUGCRestrictionChanged.AddUObject(this, &UWWUIScreenTeamLineup::HandleUGCRestrictionChange);
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamLineup::Shutdown()
{
	SIFApplication::GetApplication()->OnUGCRestrictionChanged.Remove(ugcRestrictionHandle);

	if (SIFGameWorld* gw = SIFApplication::GetApplication()->GetActiveGameWorld())
		gw->GetHUDUpdater()->Update(0.0f);

	UWWUIFunctionLibrary::StopTimer(TimerHandleOnTimeout);
	UWWUIFunctionLibrary::StopTimer(TimerHandleLoseFocus);
	UWWUIFunctionLibrary::StopTimer(TimerHandleRegainFocus);
}
//===============================================================================
//===============================================================================
ARugbyPlayerController* UWWUIScreenTeamLineup::GetPlayerController(int player)
{
	if (SIFApplication::GetApplication())
	{
		ARugbyPlayerController * playerController = SIFApplication::GetApplication()->GetLocalPlayerControllerFromControllerId(player);

		if (playerController == nullptr)
		{
			TimerHandleRegainFocus = UWWUIFunctionLibrary::OnTimer(1.0f, FTimerDelegate::CreateUObject(this, &UWWUIScreenTeamLineup::RegainControllerFocus));
		}

		return playerController;
	}

	return nullptr;
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamLineup::RegainControllerFocus()
{
	if (SIFApplication::GetApplication())
	{
		ARugbyPlayerController * playerController = Cast<ARugbyPlayerController>(SIFApplication::GetApplication()->GetPrimaryAccountPlayerController());

		if (playerController == nullptr)
		{
			TimerHandleRegainFocus = UWWUIFunctionLibrary::OnTimer(1.0f, FTimerDelegate::CreateUObject(this, &UWWUIScreenTeamLineup::RegainControllerFocus));
		}
		else
		{
			UWWUIScrollBox* pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTeamLineup_UI::ScrollBoxPlayersLeft));
			if (pScrollBox)
			{
				for (ULocalPlayer * lp : SIFApplication::GetApplication()->GetLocalPlayers())
				{
					pScrollBox->FocusFirstListField(lp->GetPlayerController(GetWorld()));
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamLineup::HandleUGCRestrictionChange(bool bIsRestricted)
{
	bIsSomeAccountRestricted = SIFApplication::GetApplication()->IsAnyUserRestricted();
	bIsNonPrimaryRestricted = SIFApplication::GetApplication()->IsNonPrimaryUserRestricted();

	UpdateTeamHeading();

	if (DisplayingReserves)
	{
		//< Display reserves screen. >
		if (UWWUIScrollBox* homeScrollBox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTeamLineup_UI::ReservesHomeScrollbox)))
		{
			PopulateReserveList(homeScrollBox, SSTEAMSIDE::SIDE_A);
		}

		if (UWWUIScrollBox* awayScrollBox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTeamLineup_UI::ReservesAwayScrollbox)))
		{
			PopulateReserveList(awayScrollBox, SSTEAMSIDE::SIDE_B);
		}
	}
	else
	{
		UWWUIScrollBox* pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTeamLineup_UI::ScrollBoxPlayersRight));
		if (pScrollBox)
		{
			PopulatePlayerList(pScrollBox, SSTEAMSIDE::SIDE_B);
		}

		pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTeamLineup_UI::ScrollBoxPlayersLeft));
		if (pScrollBox)
		{
			PopulatePlayerList(pScrollBox, SSTEAMSIDE::SIDE_A);
			for (int i = 0; i < SIFApplication::GetApplication()->GetNumLocalPlayers(); i++)
			{
				pScrollBox->FocusFirstListField(GetPlayerController(i));
			}

		}

		TableOnSelectionChange("", -1, 0);
	}
}

//------------------------------------------------------------------------------
//------------------------------------------------------------------------------

void UWWUIScreenTeamLineup::PopulatePlayerList(UWWUIScrollBox* pScrollBox, SSTEAMSIDE teamID)
{
	UWWUIPopulator* pPopulator = pScrollBox->GetPopulator();

	if (pPopulator)
	{
		// Nick  WWS 7s to Womens //
		/*
		if (SIFGameHelpers::GAGetGameMode() == 1) {

			int SevensPositions[7] = { 1, 2, 3, 9, 10, 12, 15 };

			for (int j = 0; j < 7; j++)
			{
				FString PlayerName = UTF8_TO_TCHAR(SIFInGameHelpers::GetPlayerDisplayName((int)teamID, SevensPositions[j] - 1).c_str());
				REMOVE_UNUSED_CHARACTER(PlayerName);
				pPopulator->SetDataListOptionText(j, FText::FromString(PlayerName), true);
			}
		}
		else
		{
		*/
			for (int j = 0; j < 13; j++)
			{
				FString PlayerName = UTF8_TO_TCHAR(SIFInGameHelpers::GetPlayerDisplayName((int)teamID, j).c_str());
				REMOVE_UNUSED_CHARACTER(PlayerName);
				pPopulator->SetDataListOptionText(j, FText::FromString(PlayerName), true);
			}
		//}

		pScrollBox->PopulateAndRefresh();		
	}
}

//------------------------------------------------------------------------------------------------------------
//------------------------------------------------------------------------------------------------------------

void UWWUIScreenTeamLineup::TableOnSelectionChange(FString InTableId, int OldIdx, int NewIdx)
{
	//	Mattt H - This should Already be happening, I literally have no idea why it doesn't happen on the focused list, so we will just manually do it.
	//< Scroll the focused list field. >
	if (UWWUIScrollBox* pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTeamLineup_UI::ScrollBoxPlayersLeft)))
	{
#if PLATFORM_SWITCH
		pScrollBox->PopulateAndRefresh();
#endif
		if (UWWUIListField* newListField = pScrollBox->GetListField(NewIdx))
		{
#if PLATFORM_SWITCH
			UWWUIFunctionLibrary::PlayAnimation(newListField, FString("focused"), 0, 1, EUMGSequencePlayMode::Type::Forward);
#endif
			if (UAutoScrollingBlock* scrollField = Cast<UAutoScrollingBlock>(UWWUIFunctionLibrary::FindChildWidget(newListField, FString(SCROLLBOX_PLAYER_SCROLL_FIELD_WIDGET_NAME))))
			{
				scrollField->scrollMode = ScrollingBlockMode::SCROLL;
				scrollField->SetScrolling(true);
			}
		}
#if !PLATFORM_SWITCH
		//< UnFocus previous field. >
		if (OldIdx != -1)
		{
			if (UWWUIListField* oldListField = pScrollBox->GetListField(OldIdx))
			{
				if (UAutoScrollingBlock* scrollField = Cast<UAutoScrollingBlock>(UWWUIFunctionLibrary::FindChildWidget(oldListField, FString(SCROLLBOX_PLAYER_SCROLL_FIELD_WIDGET_NAME))))
				{
					scrollField->SetScrolling(false);
					scrollField->scrollMode = ScrollingBlockMode::ELLIPSIS;
				}
			}
		}
#endif
	}

	//< Animate the opposing list field. >
	if(UWWUIScrollBox* pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTeamLineup_UI::ScrollBoxPlayersRight)))
	{
#if PLATFORM_SWITCH
		pScrollBox->PopulateAndRefresh();
#endif
		//< Focus new field. >
		if (UWWUIListField* newListField = pScrollBox->GetListField(NewIdx))
		{
			UWWUIFunctionLibrary::PlayAnimation(newListField, FString("focused"), 0, 1, EUMGSequencePlayMode::Type::Forward);

			if (UAutoScrollingBlock* scrollField = Cast<UAutoScrollingBlock>(UWWUIFunctionLibrary::FindChildWidget(newListField, FString(SCROLLBOX_PLAYER_SCROLL_FIELD_WIDGET_NAME))))
			{
				scrollField->scrollMode = ScrollingBlockMode::SCROLL;
				scrollField->SetScrolling(true);
			}
		}
#if !PLATFORM_SWITCH  // This doesn't work well on Switch platform , so we are disabling it. 
		//< UnFocus previous field. >
		if (OldIdx != -1)
		{
			if (UWWUIListField* oldListField = pScrollBox->GetListField(OldIdx))
			{
				UWWUIFunctionLibrary::PlayAnimation(oldListField, FString("focused"), 0, 1, EUMGSequencePlayMode::Type::Reverse);

				if (UAutoScrollingBlock* scrollField = Cast<UAutoScrollingBlock>(UWWUIFunctionLibrary::FindChildWidget(oldListField, FString(SCROLLBOX_PLAYER_SCROLL_FIELD_WIDGET_NAME))))
				{
					scrollField->SetScrolling(false);
					scrollField->scrollMode = ScrollingBlockMode::ELLIPSIS;
				}
			}
		}
#endif
	}

	if (m_bNetworkGame)
	{
		//In a network game we're auto-scrolling, so let it animate the first table as well
		if (UWWUIScrollBox* pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTeamLineup_UI::ScrollBoxPlayersLeft)))
		{
#if PLATFORM_SWITCH
			pScrollBox->PopulateAndRefresh();
#endif
			//< Focus new field. >
			if (UWWUIListField* newListField = pScrollBox->GetListField(NewIdx))
			{
				UWWUIFunctionLibrary::PlayAnimation(newListField, FString("focused"), 0, 1, EUMGSequencePlayMode::Type::Forward);
			}
#if !PLATFORM_SWITCH  // This doesn't work well on Switch platform , so we are disabling it. 
			//< UnFocus previous field. >
			if (OldIdx != -1)
			{
				if (UWWUIListField* oldListField = pScrollBox->GetListField(OldIdx))
				{
					UWWUIFunctionLibrary::PlayAnimation(oldListField, FString("focused"), 0, 1, EUMGSequencePlayMode::Type::Reverse);
				}
			}
#endif
		}
	}

	m_currentTableIndex = NewIdx;

	//set player rugby position text - will be set for team 0 in SetPlayerInfo
	//set player info
	SetPlayerInfo(0, NewIdx);
	SetPlayerInfo(1, NewIdx);
}


//------------------------------------------------------------------------------------------------------------
//------------------------------------------------------------------------------------------------------------
void UWWUIScreenTeamLineup::ExecuteTableFunction(FString InTableId, int InIdx, FString InAction, FString InActionString)
{
	if (DisplayingReserves) return;

	//< Display reserves screen. >
	if (UWWUIScrollBox* homeScrollBox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTeamLineup_UI::ReservesHomeScrollbox)))
	{
		PopulateReserveList(homeScrollBox, SSTEAMSIDE::SIDE_A);
	}

	if (UWWUIScrollBox* awayScrollBox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTeamLineup_UI::ReservesAwayScrollbox)))
	{
		PopulateReserveList(awayScrollBox, SSTEAMSIDE::SIDE_B);
	}

	if (UWWUIRichTextBlockWithTranslate* helptip = Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTeamLineup_UI::LegendText)))
	{
		helptip->SetText("[ID_COPYRIGHT_HELP]");
	}

	UWWUIFunctionLibrary::PlayAnimation(this, Animations_UI::BP_UIScreenTeamLineup::FadeToReserves);
	TimerHandleLoseFocus = UWWUIFunctionLibrary::OnTimer(1.0f, FTimerDelegate::CreateUObject(this, &UWWUIScreenTeamLineup::OnTimeoutLoseFocus));
	DisplayingReserves = true;

	//pop the first anthem off the stack.
	SIFAudioHelpers::PopMusic();
}


//------------------------------------------------------------------------------------------------------------
//------------------------------------------------------------------------------------------------------------
void UWWUIScreenTeamLineup::SetPlayerInfo(int32 teamIdx, int32 playerIdx) 
{
	FString widgetName = teamIdx == 0 ? WWUIScreenTeamLineup_UI::PlayerInfoLeft : WWUIScreenTeamLineup_UI::PlayerInfoRight;

	UWidget* pWidget = Cast<UWidget>(FindChildWidget(widgetName));
	if (pWidget)
	{
		UWorld* pWorld = GetWorld();
		if (pWorld)
		{
			URugbyGameInstance* pRugbyGameInstance = Cast<URugbyGameInstance>(pWorld->GetGameInstance());
			if (pRugbyGameInstance)
			{
				RUDBHelperInterface* db_helper = pRugbyGameInstance->GetGameDBHelper();

				if (db_helper)
				{
					/// Get Team.
					RUTeam* team = SIFApplication::GetApplication()->GetActiveGameWorld()->GetTeam((SSTEAMSIDE)teamIdx);
					if (!team) return;

					/// Get RugbyCharacter via position.
					PLAYER_POSITION playerPos;
					// Nick  WWS 7s to Womens //
					//if (SIFGameHelpers::GAGetGameMode() == 1)
					//	playerPos = PlayerPositionEnum::GetPlayerPositionFromLineupIndexSevensOnly(playerIdx);
					//else
						playerPos = PlayerPositionEnum::GetPlayerPositionFromLineupIndex(playerIdx);

					ARugbyCharacter* player = team->GetPlayerByPosition(playerPos);
					if (!player) return;

					//	Mattt H - If stats (name/height/weight/face/age) don't show for a player than it is probably because of this.
					RUDB_PLAYER* rudbPlayer = player->GetAttributes()->GetDBPlayer();
					if (!rudbPlayer) return;

					bool isPlayerCustom = rudbPlayer->IsCustom();

					int32 player_db_id = player->GetAttributes()->GetDbId();
					RU_PlayerDB_Data* player_db = db_helper->LoadPlayerDBData(player_db_id);

					if (player_db)
					{
						UWWUIUserWidgetPlayerFaceImage* pPlayerFaceImage = Cast<UWWUIUserWidgetPlayerFaceImage>(FindChildOfTemplateWidget(pWidget, WWUIScreenTeamLineup_UI::PlayerFaceImage));

						if (pPlayerFaceImage)
						{
							pPlayerFaceImage->RefreshPlayerFaceImage(player_db_id, team->GetDbTeam().GetDbId(), false, teamIdx);
						}

						//Position Text:
						//only need to update postion text once
						if (teamIdx == 0)
						{				
							UTextBlock* pTextBlock = Cast<UTextBlock>(FindChildWidget(WWUIScreenTeamLineup_UI::TextPlayerPosition));
							if (pTextBlock)
							{
								// #TeamLinupImplementationNeeded
								/*
								FString PlayerPositionString = "";
								if (SIFGameHelpers::GAGetGameMode() == 1) {
									PlayerPositionString = SIFGameHelpers::GAGetPositionText(player_db->GetPrimaryPositionR7());
								}
								else
								{
									PlayerPositionString = SIFGameHelpers::GAGetPositionText(player_db->GetPrimaryPositionR13());
								}
								//needs to be translated
								pTextBlock->SetText(FText::FromString(PlayerPositionString));
								*/

								//only works for 15s (probably)
								RUCareerModeManager* pCareerModeManager = pRugbyGameInstance->GetCareerModeManager();
								if (pCareerModeManager)
								{
									FString PlayerPositionString = SIFGameHelpers::GAConvertMabStringToFString(pCareerModeManager->GetPlayerRecruitFilterName(playerIdx + 1));
									pTextBlock->SetText(FText::FromString(UWWUITranslationManager::Translate(PlayerPositionString)));
								}
							}
						}

						UTextBlock* pTextBlock = Cast<UTextBlock>(FindChildOfTemplateWidget(pWidget, WWUIScreenTeamLineup_UI::TextName));
						if (pTextBlock)
						{
							MabString playerName = "";
							if (isPlayerCustom)
							{
								rudbPlayer->GetCombinedName(playerName);
								
								RUDB_TEAM db_team = SIFApplication::GetApplication()->GetMatchGameSettings()->team_settings[teamIdx].team;
								RUHUDUpdater::CensorPlayerName(&db_team, rudbPlayer, playerName);
							}
							else
							{
								playerName = player_db->GetName();
								RUHUDUpdater::CensorPlayerName(nullptr, player_db, playerName);
							}
							pTextBlock->SetText(SIFGameHelpers::GAConvertMabStringToFText(playerName).ToUpper());
						}

						pTextBlock = Cast<UTextBlock>(FindChildOfTemplateWidget(pWidget, WWUIScreenTeamLineup_UI::TextAge));
						if (pTextBlock)
						{
							int age = 0;
							if (isPlayerCustom)
							{
								age = rudbPlayer->GetAge();
							}
							else
							{
								age = player_db->GetAge();
							}
							FString text = FString::FromInt(age);
							pTextBlock->SetText(FText::FromString(text));
						}

						pTextBlock = Cast<UTextBlock>(FindChildOfTemplateWidget(pWidget, WWUIScreenTeamLineup_UI::TextHeight));
						if (pTextBlock)
						{
							int height = 0;
							if (isPlayerCustom)
							{
								height = rudbPlayer->height;
							}
							else
							{
								height = player_db->GetHeight();
							}
							FString text = FString::FromInt(height);
							pTextBlock->SetText(FText::FromString(text));
						}

						pTextBlock = Cast<UTextBlock>(FindChildOfTemplateWidget(pWidget, WWUIScreenTeamLineup_UI::TextWeight));
						if (pTextBlock)
						{
							int weight = 0;
							if (isPlayerCustom)
							{
								weight = rudbPlayer->weight;
							}
							else
							{
								weight = player_db->GetWeight();
							}
							FString text = FString::FromInt(weight);
							pTextBlock->SetText(FText::FromString(text));
						}
					}
				}
			}
		}
	}
}


//------------------------------------------------------------------------------
//------------------------------------------------------------------------------

void UWWUIScreenTeamLineup::OnInFocus()
{
	OnWindowEnter();
	DuplicateInputComponent();
	SIFRichPresenceHelpers::RPSetPresenceInGame();

	if (m_bNetworkGame)
	{
		//Calling this here to set focus to the screen itself
		OnTimeoutLoseFocus();
	}

#if PLATFORM_WINDOWS
	if ((SIFGameHelpers::GAIsPlatformPC()))
	{
		SIFUIHelpers::SetOSMouseCursorActive(false);

		if (USIFMissingControllerListener * mcl = SIFApplication::GetApplication()->GetMissingControllerListener())
		{
			//// --  start at 2 by default, pc and mouse take 0 and 1 - not true anymore
			//for (int i = 0; i < SIFPlayerHelpers::PMGetMaxControllers(); i++)
			//{
			//	if (SIFPlayerHelpers::PMIsPlaying(i) == true)
			//	{
			//		mcl->SetControllerRelevant(i, true);
			//		SIFUIHelpers::ListenToController(i, true);
			//	};
			//}

			////Always set the kb and mouse to be active
			//mcl->SetControllerRelevant(0, true);
			//SIFUIHelpers::ListenToController(0, true);
		}
	}
#endif

	SIFGameHelpers::GASetGameInputActionManagerActive(true);
}


void UWWUIScreenTeamLineup::OnOutFocus(bool ShouldOutFocus)
{
	Super::OnOutFocus(ShouldOutFocus);
}

//------------------------------------------------------------------------------
//------------------------------------------------------------------------------

void UWWUIScreenTeamLineup::OnWindowEnter()
{
	SIFGameHelpers::GASetTeamFaceGenerationEnabled(true);	// -- Enable face generation(will cause stuttering), (will remove when more asynchronous!)

	// Nick  WWS 7s to Womens //
	//if (SIFGameHelpers::GAGetGameMode() == 1) {
	//	LINEUP_SIZE = SEVENS_SIZE;
	//}
	//else
	//{
		LINEUP_SIZE = FIFTEENS_SIZE;
	//}
	
	//SetLineupVisible();

	if (SIFGameHelpers::GAGetGameMode() == 1) {
		// #TeamLinupImplementationNeeded
		//UIFXNodeRunNamedAnimation(WINDOW_NODE, "team_lineup_onscreen_sevens_animation");
	}
	else
	{
		// #TeamLinupImplementationNeeded
		//UIFXNodeRunNamedAnimation(WINDOW_NODE, "team_lineup_onscreen_animation");
	}

	SIFUIHelpers::MenuSoundTeamLineup();

	// #TeamLinupImplementationNeeded - maybe
	//UINodeSetAlpha(WINDOW_NODE, 0.0); // -- fix a one frame glitch since the animation doesn't start immediately?


	// -- If we're playing an online match then we need to add a screen timeout so players don't grief each other.
	if (m_bNetworkGame)
	{
		SIFMatchmakingHelpers::SignalReady(EVENT_TEAM_MANAGEMENT_PLAYER_READY, false);

		//UISetTimer(ONLINE_SCREEN_TIMEOUT_NAME, ui_object, ONLINE_SCREEN_TIMEOUT);
		//UISetTimer(ONLINE_ONTO_SECOND_TEAM_NAME, ui_object, ONLINE_ONTO_SECOND_TEAM);

		//both teams on one screen, onto 2nd team not required
			// #TeamLinupImplementationNeeded	- may need to cancle timer somewhere
		TimerHandleOnTimeout = UWWUIFunctionLibrary::OnTimer(ONLINE_SCREEN_TIMEOUTTeamLineup, FTimerDelegate::CreateUObject(this, &UWWUIScreenTeamLineup::OnTimeoutOnlineDuration), false);

		m_changeViewedPlayerTimerMax = (float)ONLINE_SCREEN_TIMEOUTTeamLineup / (float)(LINEUP_SIZE + 1.0f);
		m_changeViewedPlayerTimer = -(m_changeViewedPlayerTimerMax * 0.5f);
	}

	// -- Enable post effect
	SIFInGameHelpers::EnableMenuPostEffects();

	// -- Set a timer if there's no human playing so we can skip to game window
	int32 num_human_team0 = SIFInGameHelpers::GetNumHumanPlayersOnTeam(0);
	int32 num_human_team1 = SIFInGameHelpers::GetNumHumanPlayersOnTeam(1);

	// -- The cutscene itself is around 20secs but the timer started on window enter way before the screen is displayed
	// -- so need to add more time for the first team lineup
	if (num_human_team0 == 0 && num_human_team1 == 0) {
		//UISetTimer("NextTeamLineup", ui_object, 20.0)
		//UISetTimer("NextWindow", ui_object, 30.0)

		//both teams on one screen, onto 2nd team not required

		// #TeamLinupImplementationNeeded 30s might be too long, out timer probably not called as early as rc3's
		// #TeamLinupImplementationNeeded	- may need to cancle timer somewhere
		// #TeamLinupImplementationNeeded - should this be handled by cutscenes? Currently I don't think it is.
		TimerHandleOnTimeout = UWWUIFunctionLibrary::OnTimer(30, FTimerDelegate::CreateUObject(this, &UWWUIScreenTeamLineup::OnTimeoutNextWindow), false);
	}

	UpdateTeamHeading();
	
	//UpdateHelptip();	//static in widget
}


//------------------------------------------------------------------------------
//------------------------------------------------------------------------------


void UWWUIScreenTeamLineup::StartGame() //close screen and continue
{
	SIFInGameHelpers::TeamLineupComplete();
	//SIFGameHelpers::GARequestGameStartCutscene();

	SIFInGameHelpers::DisableMenuPostEffects();

	SIFApplication::GetApplication()->GetActiveGameWorld()->GetCutSceneManager()->SetDisableCutSceneSkip(false);
	SIFApplication::GetApplication()->GetActiveGameWorld()->GetCutSceneManager()->SkipCutScene();
	SIFApplication::GetApplication()->DealMenuAction(SCREEN_REMOVE_SCREENS_EXCEPT, Screens_UI::InGameHud);
}

//------------------------------------------------------------------------------
//------------------------------------------------------------------------------

void UWWUIScreenTeamLineup::OnTimeoutOnlineDuration()
{
	SkipCutscene();
}

void UWWUIScreenTeamLineup::OnTimeoutLoseFocus()
{
	TArray<ULocalPlayer *> localPlayers = SIFApplication::GetApplication()->GetLocalPlayers();
	int playerIndex = 0;
	for (ULocalPlayer * lp : localPlayers)
	{
		SetFocusToWidget(this, UGameplayStatics::GetPlayerController(GetWorld(), playerIndex));
		playerIndex++;
	}
}

//------------------------------------------------------------------------------
//------------------------------------------------------------------------------

void UWWUIScreenTeamLineup::OnTimeoutNextWindow()
{
	if (m_bNetworkGame)
	{
		// --We need to tell the multiplayer match that we're ready.
		SIFMatchmakingHelpers::SignalReady(EVENT_TEAM_MANAGEMENT_PLAYER_READY, true);

		if (UWWUIRichTextBlockWithTranslate* helptip = Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTeamLineup_UI::LegendText)))
		{
			helptip->SetText("[ID_WAIT_FOR_PEER]");
		}
	}
	else
	{
		StartGame();
	}
}


//------------------------------------------------------------------------------
//------------------------------------------------------------------------------

void UWWUIScreenTeamLineup::OnActionSkipCutscene(APlayerController* controller)
{
	// #TeamLinupImplementationNeeded - not needed?
	//--Only allow the correct controller to control the team management
	//local controller_team = IGGetPlayerTeam(parameters.controller_id)
	//local humans_playing_both_team = IGIsHumanPlayersOnBothTeams()
	//local network_game = GAGetGameType() == "network"

	//if ((controller_team ~= CURRENT_TEAM and humans_playing_both_team and not network_SIFGameHelpers::GAme))
	//else if (parameters.action_event == "ACTION_TYPE_CUSTOM" and parameters.custom_action == "RU_UI_ACTION_SKIP_CUTSCENE" and
	//STATE == ETeamLineupStates::STATIC_LINEUP && MODE == ETeamLineupModes::PRE_GAME))

	SkipCutscene();
}

//------------------------------------------------------------------------------
//------------------------------------------------------------------------------

void UWWUIScreenTeamLineup::SkipCutscene()
{
	// --  What we do here depends on what sort of game we are playing.
	// --  On a networked game we need to wait until the other player is ready.
	if (m_bNetworkGame)
	{
		// --  We need to tell the multiplayer match that we're ready.
		SIFMatchmakingHelpers::SignalReady(EVENT_TEAM_MANAGEMENT_PLAYER_READY, true);

		if (UWWUIRichTextBlockWithTranslate* helptip = Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTeamLineup_UI::LegendText)))
		{
			helptip->SetText("[ID_WAIT_FOR_PEER]");
		}

		// --  We will probably also need some feedback to show the user that we're waiting on the other player.
		// #TeamLinupImplementationNeeded
		//auto helptip = UIGetNode("RootMenuWindow/TeamLineup/HelpText_NEW/Helptip")
		//UITextSetText(helptip, "[ID_WAIT_FOR_PEER]")
	}
	else 
	{
		if ( SIFApplication::GetApplication()->GetActiveGameWorld()->GetCutSceneManager()->GetLastCutSceneTime() < 1.5f )
		{
			UE_LOG(LogTemp, Warning, TEXT("Cutscene is not ready to be skipped yet, wait for 1.5 second."));
			return;
		}
		StartGame();
	}
}

void UWWUIScreenTeamLineup::PopulateReserveList(UWWUIScrollBox* pScrollBox, SSTEAMSIDE teamID)
{
	if (!pScrollBox) return;

	//< If found team & game instance. >
	URugbyGameInstance* pRugbyGameInstance = Cast<URugbyGameInstance>(GetWorld()->GetGameInstance());
	if (pRugbyGameInstance)
	{
		if (RUTeam* team = pRugbyGameInstance->GetActiveGameWorld()->GetTeam(teamID))
		{
			bool isCustom = SIFGameHelpers::GAGetTeamIsCustom(team->GetDbTeam().GetDbId());

			//< Get bench player IDs >
			MabVector<unsigned short> playerIDs;

			// RussellD :
			//     Fix for issue where extras are erroneously flagged as subs in career. I want this to match to the population of the
			//     in-game squad management list, so get the information in the same way; get a full ordered list and only look at the
			//     indexes that relate to our assumed bench positions.
			int playersOnField = pRugbyGameInstance->GetMatchGameSettings()->game_limits.GetNumberOfPlayersPerTeam();
			int playersOnFieldAndBench = playersOnField + pRugbyGameInstance->GetMatchGameSettings()->game_limits.GetNumberOfBenchPlayers();
			team->GetDbTeam().GetAllPlayersInDisplayOrder(playerIDs);
			//team->GetDbTeam().GetBenchPlayers(playerIDs);

			//< Populator data with player details. >
			UWWUIPopulator* pPopulator = pScrollBox->GetPopulator();
			
			pPopulator->GetDataList()->ArrayOption.Empty();

			RUDBHelperInterface* db_helper = pRugbyGameInstance->GetGameDBHelper();

			for (int i = playersOnField; i < playerIDs.size() && i < playersOnFieldAndBench; i++)
			{
				RU_PlayerDB_Data* player_db = db_helper->LoadPlayerDBData(playerIDs[i]);
				MabString PlayerName = player_db->GetLastName();	// Todo, this should be display name (display names add more info when there are duplicate last names)

				if(player_db->GetIsCustom() && SIFApplication::GetApplication()->GetMatchGameSettings() && SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.network_game)
				{
					PLAYER_POSITION playerPos;
					// Nick  WWS 7s to Womens //
					//if (SIFGameHelpers::GAGetGameMode() == 1)
					//	playerPos = PlayerPositionEnum::GetPlayerPositionFromLineupIndexSevensOnly(i);
					//else
						playerPos = PlayerPositionEnum::GetPlayerPositionFromLineupIndex(i);

					ARugbyCharacter* player = team->GetPlayerByPosition(playerPos);
					if (!player) return;

					RUDB_PLAYER* rudbPlayer = player->GetAttributes()->GetDBPlayer();
					RUDB_TEAM db_team = SIFApplication::GetApplication()->GetMatchGameSettings()->team_settings[teamID].team;
					RUHUDUpdater::CensorPlayerName(&db_team, rudbPlayer, PlayerName);
				}
				else
				{
					RUHUDUpdater::CensorPlayerName(nullptr, player_db, PlayerName);
				}
				//REMOVE_UNUSED_CHARACTER(PlayerName);				
				pPopulator->SetDataListOptionText(i, SIFGameHelpers::GAConvertMabStringToFText(PlayerName).ToUpper(), true);
			}

			//< Refresh Populator. >
			pScrollBox->PopulateAndRefresh();
		}
	}
}

//======================================================================================================
//======================================================================================================
void UWWUIScreenTeamLineup::UpdateTeamHeading()
{
	//< Set Home Team Logo. >
	if (UUserWidget* pWidget = Cast<UUserWidget>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTeamLineup_UI::PlayerInfoLeft)))
	{
		if (UImage* pImage = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(pWidget, WWUIScreenTeamLineup_UI::Logo)))
		{
			SIFUIHelpers::ImageSetLogoHud(pImage, 0);
		}
	}

	//< Set Away Team Logo. >
	if (UUserWidget* pWidget = Cast<UUserWidget>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTeamLineup_UI::PlayerInfoRight)))
	{
		if (UImage* pImage = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(pWidget, WWUIScreenTeamLineup_UI::Logo)))
		{
			SIFUIHelpers::ImageSetLogoHud(pImage, 1);
		}
	}

	//< Set Home Team Name >
	UTextBlock* homeTitle			= Cast<UTextBlock>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTeamLineup_UI::TeamNameLeft));
	UTextBlock* homeTitleReserves	= Cast<UTextBlock>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTeamLineup_UI::ReservesHomeTitle));

	MabString teamName = SIFInGameHelpers::GetTeamShortName(0);

	if (SIFApplication::GetApplication()->GetMatchGameSettings() && SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.network_game)
	{
		RUDB_TEAM db_team = SIFApplication::GetApplication()->GetMatchGameSettings()->team_settings[SSTEAMSIDE::SIDE_A].team;
		RUHUDUpdater::CensorTeamName(&db_team, teamName);
	}
	else
	{
		RUHUDUpdater::CensorTeamName(SIFGameHelpers::GAGetTeam(0), teamName);
	}

	UWWUIFunctionLibrary::SetText(homeTitle, FString(SIFGameHelpers::GAConvertMabStringToFString(teamName)).ToUpper());
	UWWUIFunctionLibrary::SetText(homeTitleReserves, FString(SIFGameHelpers::GAConvertMabStringToFString(teamName)).ToUpper());

	//< Set Away Team Name >
	UTextBlock* awayTitle			= Cast<UTextBlock>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTeamLineup_UI::TeamNameRight));
	UTextBlock* awayTitleReserves	= Cast<UTextBlock>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTeamLineup_UI::ReservesAwayTitle));

	teamName = SIFInGameHelpers::GetTeamShortName(1);

	if (SIFApplication::GetApplication()->GetMatchGameSettings() && SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.network_game)
	{
		RUDB_TEAM db_team = SIFApplication::GetApplication()->GetMatchGameSettings()->team_settings[SSTEAMSIDE::SIDE_B].team;
		RUHUDUpdater::CensorTeamName(&db_team, teamName);
	}
	else
	{
		RUHUDUpdater::CensorTeamName(SIFGameHelpers::GAGetTeam(1), teamName);
	}

	UWWUIFunctionLibrary::SetText(awayTitle, FString(SIFGameHelpers::GAConvertMabStringToFString(teamName)).ToUpper());
	UWWUIFunctionLibrary::SetText(awayTitleReserves, FString(SIFGameHelpers::GAConvertMabStringToFString(teamName)).ToUpper());
	
	//< Set Home Team Color >
	MabColour mabHomeColor = SIFInGameHelpers::GetTeamColour(0);
	FLinearColor linearHomeColor = FLinearColor(mabHomeColor.r, mabHomeColor.g, mabHomeColor.b, 1);

	UBorder* homeColorBorder = Cast<UBorder>(FindChildWidget(WWUIScreenTeamLineup_UI::HomeTeamColor));
	UImage* homeColorImage = Cast<UImage>(FindChildWidget(WWUIScreenTeamLineup_UI::TeamColorLeft));
	if (homeColorBorder && homeColorImage)
	{
		homeColorBorder->SetBrushColor(linearHomeColor);
		homeColorImage->SetColorAndOpacity(linearHomeColor);
	}

	//< Set Away Team Color >
	MabColour mabAwayColor = SIFInGameHelpers::GetTeamColour(1);
	FLinearColor linearAwayColor = FLinearColor(mabAwayColor.r, mabAwayColor.g, mabAwayColor.b, 1);

	homeColorBorder = Cast<UBorder>(FindChildWidget(WWUIScreenTeamLineup_UI::AwayTeamColor));
	homeColorImage = Cast<UImage>(FindChildWidget(WWUIScreenTeamLineup_UI::TeamColorRight));
	if (homeColorBorder && homeColorImage)
	{
		homeColorBorder->SetBrushColor(linearAwayColor);
		homeColorImage->SetColorAndOpacity(linearAwayColor);
	}
}


//======================================================================================================
//======================================================================================================
bool UWWUIScreenTeamLineup::OnSystemEvent(WWUINodeProperty & eventParams)
{
	if (eventParams.GetStringProperty("system_event") == EVENT_TEAM_MANAGEMENT_PLAYER_READY_CHANGED && SIFMatchmakingHelpers::IsAllReady(EVENT_TEAM_MANAGEMENT_PLAYER_READY))
	{
		StartGame();
		return true;
	}
	// #TeamLinupImplementationNeeded
	else if (false /*SIFGameHelpers::GAmeWindow.HandleNetworkSystemEvents(ui_object, parameters*/)
	{
		return true;
	}
	return false;
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamLineup::Update(float DeltaTime)
{
	if (m_bNetworkGame)
	{
		m_changeViewedPlayerTimer += DeltaTime;

		if (m_changeViewedPlayerTimer > m_changeViewedPlayerTimerMax)
		{
			m_changeViewedPlayerTimer = 0.0f;

			if (m_currentTableIndex + 1 == LINEUP_SIZE)
			{
				m_currentTableIndex = -1;
			}

			TableOnSelectionChange("", m_currentTableIndex, m_currentTableIndex + 1);
		}
	}
}
