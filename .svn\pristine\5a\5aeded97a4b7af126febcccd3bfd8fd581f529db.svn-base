// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#pragma once

#include "CoreMinimal.h"
#include "WWUIScreenTemplate.h"
#include "wwStadiumRuntime/Public/ReplayManager.h"
#include "WWUIScreenInstantReplay.generated.h"

class USlider;
class UTextBlock;
class APlayerController;
class UUserWidget;
class UWidgetSwitcher;
class UReplayManager;
class ARugbyReplayCineCameraActor;

UCLASS()
class RUGBY_API UWWUIScreenInstantReplay : public UWWUIScreenTemplate
{
	GENERATED_BODY()

		UWWUIScreenInstantReplay(const FObjectInitializer& ObjectInitializer);

protected:
	virtual void Startup(UWWUIStateScreenData* InData = nullptr) override;
	virtual void RegisterFunctions() override;

	virtual void Update(const float DeltaTime) override;

public:
	//==============================================================================================
	//< Inputs >====================================================================================
	void OnPlayPause(APlayerController* playerController);	//< Switches between play & pause. >

	void OnChangeTarget(APlayerController* playerController);
	void OnChangeCamera(APlayerController* playerController);
	void OnZoomIn(APlayerController* playerController);
	void OnZoomOut(APlayerController* playerController);

	void OnCyclePlayerUp(APlayerController* playerController);
	void OnCyclePlayerDown(APlayerController* playerController);

	void OnDone(APlayerController* playerController);
	void OnHide(APlayerController* playerController);

	void OnFastForward(float AxisValue, APlayerController* OwningPlayer);	//< Acts as time line scrubber while paused. Longer it's held, higher the speed. >
	void OnRewind(float AxisValue, APlayerController* OwningPlayer);		//< Acts as time line scrubber while paused. Longer it's held, higher the speed. >
	bool FastForwardHeld;
	bool RewindHeld;

	void OnCameraMoveX(float AxisValue, APlayerController* OwningPlayer);
	void OnCameraMoveY(float AxisValue, APlayerController* OwningPlayer);
	void OnCameraMoveZ(float AxisValue, APlayerController* OwningPlayer);
	void OnCameraRotatePitch(float AxisValue, APlayerController* OwningPlayer);
	void OnCameraRotateYaw(float AxisValue, APlayerController* OwningPlayer);

   void OnGoToNextFrame(APlayerController* OwningPlayer);
   void OnGoToPreviousFrame(APlayerController* OwningPlayer);
	//==============================================================================================

private:
	void OnRewindOrFastForward(int dir);				// -1 will be rewind, +1 will be fast forward
	void OnStepBackwardOrForward(int dir);				// -1 will be StepBackward +1 will be StepForward
	void OnZoom(int dir);								// -1 will be zoom out and +1 will be zoom in
	void OnCyclePlayer(int dir);						// -1 will cycle DownWard and +1 will be UpWard
	void UpdateTimeline(float timeInSeconds);			//< Moves the time line slider to match the time in seconds. >
	void SetSpeedText(float speedTextValue);			// Call this to change the speed text at bottom right corner
	FString ConvertSecondsToTime(int timeInSeconds);	// take in the time in second and return string which display time in minutes:seconds
	void ConvertZoomString(FString* _zoomString);

	//void UpdateText(EReplayManagerCamTrackingMode mode, const FString& camTarget);
	void UpdateText();
	void RefreshText();

	void HandleInputs();

	void HandleLeftStick();
	void HandleRightStick();

	bool m_moveAble = true; //true for free cam

	bool m_rotateAble = true;  //true for free cam

private:
	//< General >=====================
	UUserWidget* ReplayControlPanel = nullptr;
	bool IsPaused = true;
	bool IsVisible = true;
	bool ControlPanelVisible = true;
	bool ChangeableCamera = true;
	bool ChangeableZoom = true;
	bool ChangeableTarget = true;
	bool ChangeablePlayer = true;
	bool ChangeablePosition = true;
	bool ChangeableRotation = true;

	float StartTime = 0.0f;
	float EndTime = 0.0f;
	float CurrentTime = 0.0f;
	float TimeSpeed = 1.0f;
	
	float widgetStartupTime = 0.0f;

	FString m_currentZoom;
	FString m_currentCam;

	FVector m_cameraMove;
	FVector2D m_cameraRotate;

	UReplayManager* ReplayManager = nullptr;
	ARugbyReplayCineCameraActor* ReplayCameraActor = nullptr;

	//< Legends >=====================
	UWidgetSwitcher* PlaybackIconSwitcher = nullptr;
	UTextBlock* TargetText = nullptr;
	UTextBlock* CameraText = nullptr;
	UTextBlock* ZoomText = nullptr;
	UWidget* MoveLegend = nullptr;
	UWidget* RotateLegend = nullptr;
	UWidget* TargetLegend = nullptr;
	UWidget* CameraLegend = nullptr;
	UWidget* CyclePlayerLegend = nullptr;
	UWidget* ZoomLegend = nullptr;
	UWidget* PlayIcon = nullptr;
	UWidget* PauseIcon = nullptr;

#define wwINACTIVE_ALPHA	(0.2f)
#define wwACTIVE_ALPHA		(0.6f)
#define wwINUSE_ALPHA		(1.0f)

	//< Timeline >====================
	USlider*	Timeline = nullptr;
	UTextBlock* TimelineText = nullptr;
	UWidget*	PlaybackSpeed = nullptr;
	UTextBlock* PlaybackSpeedText = nullptr;
	float ScrubberForwardVelocity = 0.0f;
	float ScrubberBackwardVelocity = 0.0f;

	float ScrubberAcceleration = 0.1f; //< 0.1 sec >
	float ScrubberDelayMin = 0.05f; //< 0.2 sec >
	float ScrubberDelayMax = 0.5f; //< 1 sec >
	float ScrubberDelayForward = 0.5f; //< Between Min/Max >
	float ScrubberDelayBackward = 0.5f; //< Between Min/Max >

	float ScrubberTimerForward = 0.0f;
	float ScrubberTimerBackward = 0.0f;

	//< Re-enabling HUD >====================
	bool scrumHUDVisible = false;
	bool kickHUDVisible = false;
	bool conversionAngleHUDVisible = false;
	bool conversionPowerHUDVisible = false;
	bool conversionTeeHUDVisible = false;
	bool bombHUDVisible = false;
	bool windHUDVisible = false;
	bool setPlayVisible = false;


	//EReplayManagerCamTrackingMode m_replayCameraTrackingMode = EReplayManagerCamTrackingMode::Free;
};
