// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIScreenCompetitionSelect.h"

#include "Rugby/UI/GeneratedHeaders/WWUIScreenCompetitionSelect_UI_Namespace.h"
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "WWUIScrollBox.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"

#include "Utility/Helpers/SIFGameHelpers.h"

//#include "Rugby/RugbyGameInstance.h"	 in header
#include "Rugby/Match/SIFUIConstants.h"
#include "Rugby/Databases/RUGameDatabaseManager.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Database.h"

#include "Rugby/UI/Screens/WWUISettingsValues.h"

#include "Rugby/UI/Screens/WWUIScreenCompetitionSelectTeams.h"
#include "Widget.h"
#include "WWUIScrollBox.h"
#include "WWUIListField.h"

#include "Rugby/Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DBCompetitionDefinition.h"

#include "WWUITranslationManager.h"
#include "Utility/Helpers/SIFUIHelpers.h"

const TMap<FString, FString> UWWUIScreenCompetitionSelect::UnlicensedCompsRC4 = {
   {"AUC", "NRC"},
   {"TCC", "ACC"},
   {"NZC", "ITM"},
   {"SRU", "RUG"},
   {"PRM", "AVI"},
   {"FR1", "TOP"},
   {"FR2", "PRO"},
   {"E14", "RDP"}
};

//Registers screen input functions
#ifdef UI_USING_UMG
void UWWUIScreenCompetitionSelect::Startup(UWWUIStateScreenData* InData /* = nullptr */)
{
	UWWUIStateScreenCompetitionSelectData* ConvertedInData = Cast<UWWUIStateScreenCompetitionSelectData>(InData);
	if (ConvertedInData)
	{
		newCompetition = ConvertedInData->NewCompetition;
	}

	listBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCompetitionSelect_UI::ListBox));
	compDescription = Cast<UTextBlock>(FindChildWidget(WWUIScreenCompetitionSelect_UI::CompDescription));
	logo_node = Cast<UImage>(FindChildWidget(WWUIScreenCompetitionSelect_UI::CompetitionLogo));
	alt_logo_node = Cast<UImage>(FindChildWidget(WWUIScreenCompetitionSelect_UI::CompetitionLogoPrevious));

	ensure(listBox && compDescription && logo_node && alt_logo_node);

	//match_length_node = NULL;
	//options_node = NULL;	// competition option field

	pRugbyGameInstance = SIFApplication::GetApplication();
	if (!pRugbyGameInstance)
	{
		ensure(pRugbyGameInstance);
		return;
	}

	pCareerModeManager = pRugbyGameInstance->GetCareerModeManager();
	ensure(pCareerModeManager);
}

void UWWUIScreenCompetitionSelect::OnInFocus()
{
	SIFUIHelpers::ListenToPrimaryPlayer();

	SIFGameHelpers::GARequestCameraTransition(RugbyUIWindowNames::RUUI_COMPETITION_SELECT_WINDOW_NAME);
	SIFGameHelpers::GASetSandboxTickEnabled(true);

	// these are set in startup now
	/*logo_node = UIGetNode("CompetitionLogo")
	alt_logo_node = UIGetNode("CompetitionLogoPrevious")
	options_node = UIGetNode("CompetitionSelect/Settings/ListBox/Competition/SettingValue/Options")
	match_length_node = UIGetNode("CompetitionSelect/Settings/ListBox/MatchLength/SettingValue/Options");*/

	if (newCompetition)
	{
		// grab the manager and create a new comp mode from scratch.
		pCareerModeManager->NewCompetitionMode();

		SIFGameHelpers::GALoadProfileMatchSettings();

		newCompetition = false;

		//note : this populate is not deferred
		//UIRefreshPopulatedObject(UINodeGetContext(options_node))
		// ^^^ We are populating our lists via UpdateListBox

		pCareerModeManager->LoadCompetitionDefinitionList(CompetitionDefinitionList);

#ifdef DISABLE_WOMENS
		auto it = CompetitionDefinitionList.begin();

		while (it != CompetitionDefinitionList.end())
		{
			if (*it == DB_COMPID_WOMENS_SEVENS_CHAMP || *it == DB_COMPID_WOMENS_SEVENS_INT)
			{
				it = CompetitionDefinitionList.erase(it);
			}
			else
			{
				++it;
			}
		}
#endif

		if (pRugbyGameInstance)
		{
			RUGameDatabaseManager* pDatabaseManager = pRugbyGameInstance->GetGameDatabaseManager();
			if (pDatabaseManager)
			{
				pDatabase = pDatabaseManager->GetRL3Database();
				if (pDatabase)
				{
					//this is being initialised here so that we do not need to add an exception to UpdateCompetition when checking whether the last definition was R7Exclusive
					CompDefinition = pDatabase->GetCompetitionDefinition(CompetitionDefinitionList[competitionOptions.compIndex]);
				}
			}
		}
	}

	//Load Data initialises the options
	LoadData();


	//UINodeSetAlpha(logo_node, 1.0)
	//UINodeSetAlpha(alt_logo_node, 0.0)
	//logo_node->SetOpacity(1.0f);
	//alt_logo_node->SetOpacity(0.0f);

	InitCompetitionInfo();
	//InitCompetitionInfo initialises some text and logos

	UpdateListBox();
	listBox->SetSelectedIndex(0);
}

void UWWUIScreenCompetitionSelect::RegisterFunctions()
{
	AddInputAction(FString("UI_Back"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCompetitionSelect::OnBack));
	//AddInputAction(FString("UI_Select"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCompetitionSelect::OnSelect));
	AddInputAction(FString("UI_Left"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCompetitionSelect::OnLeft), true);
	AddInputAction(FString("UI_Right"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCompetitionSelect::OnRight), true);
}

bool UWWUIScreenCompetitionSelect::OnSystemEvent(WWUINodeProperty& eventParams)
{
	FString EventName = eventParams.GetStringProperty("system_event");

#if PLATFORM_SWITCH // On switch, wait until the save database loads are completed
	if (EventName.Compare(GAME_DB_CAREER_PRO_DATA_EXISTENCE_CHECK_COMPLETE) == 0)
#else
	if (EventName.Compare(GAME_DB_LOAD_OK_NAME) == 0 || EventName.Compare(GAME_DB_LOAD_FAIL_NAME) == 0)
#endif
	{
		SIFUIHelpers::ShowLoadingOverlay(false);
		ensureMsgf(EventName.Compare(GAME_DB_LOAD_FAIL_NAME) != 0, TEXT("The loading of the custom database failed!"));

		if (pRugbyGameInstance)
		{
			pRugbyGameInstance->DealMenuAction(SCREEN_CANCEL_FADE, Screens_UI::CompetitionSelect);
		}
	}

	return true;
}

#endif

void UWWUIScreenCompetitionSelect::OnBack(APlayerController* OwningPlayer)
{
	if (pCareerModeManager)
	{
		pCareerModeManager->EndCareerMode();
	}

	SIFUIHelpers::ShowLoadingOverlay(true, true);

	// This will call a system event to take us back to the main menu.
	SIFGameHelpers::GACustomDatabaseLoad();

	//unfocus from list to stop wierd input occurring when transitioning away
	SetFocusToWidget(this, OwningPlayer);
}

void UWWUIScreenCompetitionSelect::OnLeft(APlayerController* OwningPlayer)
{
	ChangeOption(-1);
}

void UWWUIScreenCompetitionSelect::OnRight(APlayerController* OwningPlayer)
{
	ChangeOption(1);
}

void UWWUIScreenCompetitionSelect::ChangeOption(int dir)
{
	if (listBox)
	{
		ETableOption currLine = (ETableOption)listBox->GetSelectedIndex();

		switch (currLine)
		{
		case OPTION_COMPETITION:
			UpdateCompetition(dir);
			break;
		case OPTION_DIFFICULTY:
			UpdateDifficulty(dir);
			break;
		case OPTION_LENGTH:
			UpdateMatchLength(dir);
			break;
		case OPTION_SQUAD_MGMT:
			UpdateSquadMgmt(dir);
			break;
		case OPTION_AUTOSAVE:
			UpdateAutosave(dir);
			break;
		default:
			break;
		}
	}
}

void UWWUIScreenCompetitionSelect::UpdateCompetition(int dir)
{
	competitionOptions.compIndex += dir;

	competitionOptions.compIndex = FMath::ClampWrap<int>(competitionOptions.compIndex, 0, CompetitionDefinitionList.size() - 1);

	if (pDatabase)
	{
		CompDefinition = pDatabase->GetCompetitionDefinition(CompetitionDefinitionList[competitionOptions.compIndex]);
		if (CompDefinition.GetIsR7Exclusive())
		{
			SIFGameHelpers::GASetGameMode(GAME_MODE::GAME_MODE_RU13W); // Nick  WWS 7s to Womens // SEVENS);SEVENS);
			pCareerModeManager->SetCareerGameModeR7();
		}
		else
		{
			SIFGameHelpers::GASetGameMode(GAME_MODE::GAME_MODE_RU13);
			pCareerModeManager->SetCareerGameModeR13();
		}
	}

	if (listBox)
	{
		UWidget* currentWidget = Cast<UWidget>(listBox->GetListField(ETableOption::OPTION_COMPETITION));

		UTextBlock* pSubtitleTextBlock = Cast<UTextBlock>(FindChildOfTemplateWidget(currentWidget, "SubTitle"));
		if (pSubtitleTextBlock)
		{
			FString optionText = CompDefinition.GetName();

			if (SIFApplication::GetApplication()->IsNonPrimaryUserRestricted() && CompDefinition.GetIsCustom())
			{
				optionText = UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper();
			}

			pSubtitleTextBlock->SetText(FText::FromString(optionText));
		}

		UTextBlock* pNumberOptionsText = Cast<UTextBlock>(FindChildOfTemplateWidget(currentWidget, "NumOptions"));
		if (pNumberOptionsText)
		{
			FString numOptions = FString::FromInt(competitionOptions.compIndex + 1) + "/" + FString::FromInt(CompetitionDefinitionList.size());
			SetWidgetText(pNumberOptionsText, FText::FromString(numOptions));
		}
	}

	competitionOptions.lengthIndex = COMPETITION_DEFAULT_MATCH_LENGTH_INDEX;
	UpdateMatchLength(0); // refresh times to new competition defaults
	UpdateCompetitionInfo(dir);
}

void UWWUIScreenCompetitionSelect::UpdateDifficulty(int dir)
{
	competitionOptions.difficultyIndex += dir;

	competitionOptions.difficultyIndex = FMath::ClampWrap<int>(competitionOptions.difficultyIndex, 0, Difficulties.Num() - 1);

	if (listBox)
	{
		UWidget* currentWidget = Cast<UWidget>(listBox->GetListField(ETableOption::OPTION_DIFFICULTY));

		UTextBlock* pSubtitleTextBlock = Cast<UTextBlock>(FindChildOfTemplateWidget(currentWidget, "SubTitle"));
		if (pSubtitleTextBlock)
		{
			pSubtitleTextBlock->SetText(Difficulties[competitionOptions.difficultyIndex].displayText);
		}

		UTextBlock* pNumberOptionsText = Cast<UTextBlock>(FindChildOfTemplateWidget(currentWidget, "NumOptions"));
		if (pNumberOptionsText)
		{
			FString numOptions = FString::FromInt(competitionOptions.difficultyIndex + 1) + "/" + FString::FromInt(Difficulties.Num());
			SetWidgetText(pNumberOptionsText, FText::FromString(numOptions));
		}
	}


}

void UWWUIScreenCompetitionSelect::UpdateMatchLength(int dir)
{
	competitionOptions.lengthIndex += dir;

	const TArray<FValueTextBinding<int>>* matchLengths = // Nick WWS &s to Womens 13s // (CompDefinition.GetIsR7Exclusive()) ? &SevensTimes : 
		&FifteensTimes;

	if (matchLengths)
	{

		competitionOptions.lengthIndex = FMath::ClampWrap<int>(competitionOptions.lengthIndex, 0, matchLengths->Num() - 1);

		if (listBox)
		{
			UWidget* currentWidget = Cast<UWidget>(listBox->GetListField(ETableOption::OPTION_LENGTH));

			UTextBlock* pSubtitleTextBlock = Cast<UTextBlock>(FindChildOfTemplateWidget(currentWidget, "SubTitle"));
			if (pSubtitleTextBlock)
			{
				FText optionText = ((*matchLengths)[competitionOptions.lengthIndex]).displayText;
				pSubtitleTextBlock->SetText(optionText);
			}

			UTextBlock* pNumberOptionsText = Cast<UTextBlock>(FindChildOfTemplateWidget(currentWidget, "NumOptions"));
			if (pNumberOptionsText)
			{
				FString numOptions = FString::FromInt(competitionOptions.lengthIndex + 1) + "/" + FString::FromInt(matchLengths->Num());
				SetWidgetText(pNumberOptionsText, FText::FromString(numOptions));
			}
		}
	}
}

void UWWUIScreenCompetitionSelect::UpdateSquadMgmt(int dir)
{
	if (dir != 0)
	{
		competitionOptions.auto_squad_mgmt = !competitionOptions.auto_squad_mgmt;
	}

	if (listBox)
	{
		UWidget* currentWidget = Cast<UWidget>(listBox->GetListField(ETableOption::OPTION_SQUAD_MGMT));

		UTextBlock* pSubtitleTextBlock = Cast<UTextBlock>(FindChildOfTemplateWidget(currentWidget, "SubTitle"));
		if (pSubtitleTextBlock)
		{
			FString optionText = competitionOptions.auto_squad_mgmt ? "[ID_COMP_SUBSTITUTIONS_AUTO]" : "[ID_COMP_SUBSTITUTIONS_MANUAL]"; 
			pSubtitleTextBlock->SetText(FText::FromString(UWWUITranslationManager::Translate(optionText)));
		}

		UTextBlock* pNumberOptionsText = Cast<UTextBlock>(FindChildOfTemplateWidget(currentWidget, "NumOptions"));
		if (pNumberOptionsText)
		{
			FString numOptions = FString(competitionOptions.auto_squad_mgmt ? "2" : "1") + "/2";
			SetWidgetText(pNumberOptionsText, FText::FromString(numOptions));
		}
	}
}

void UWWUIScreenCompetitionSelect::UpdateAutosave(int dir)
{
	if (dir != 0)
	{
		competitionOptions.autosave = !competitionOptions.autosave;
	}

	if (listBox)
	{
		UWidget* currentWidget = Cast<UWidget>(listBox->GetListField(ETableOption::OPTION_AUTOSAVE));

		UTextBlock* pSubtitleTextBlock = Cast<UTextBlock>(FindChildOfTemplateWidget(currentWidget, "SubTitle"));
		if (pSubtitleTextBlock)
		{
			FString optionText = competitionOptions.autosave ? "[ID_RULE_ON]" : "[ID_RULE_OFF]";
			pSubtitleTextBlock->SetText(FText::FromString(UWWUITranslationManager::Translate(optionText)));
		}

		UTextBlock* pNumberOptionsText = Cast<UTextBlock>(FindChildOfTemplateWidget(currentWidget, "NumOptions"));
		if (pNumberOptionsText)
		{
			FString numOptions = FString(competitionOptions.autosave ? "2" : "1") + "/2";
			SetWidgetText(pNumberOptionsText, FText::FromString(numOptions));
		}
	}
}

void UWWUIScreenCompetitionSelect::UpdateListBox()
{
	UpdateCompetition(0);
	UpdateDifficulty(0);
	UpdateMatchLength(0);
	UpdateSquadMgmt(0);
	UpdateAutosave(0);
}

void UWWUIScreenCompetitionSelect::SaveData()
{
	//auto selected_competition = UINodeGetSelectedNode(UIGetNode("Settings/ListBox/Competition/SettingValue/Options"))
	//auto competition_id = tonumber(UINodeGetProperty(selected_competition, "competition_id"))
	//int32 competition_id = CompetitionIDs[competitionOptions.compIndex];

	//will pass this with inData? also we are going to use CareerInitialSave instead
	//CompetitionInitialSave.CompetitionId = competition_id
	SIFGameHelpers::GACompetitionSetCompetitionDbId(CompDefinition.GetDbId()); // for the ui bg scene

	// tell the next screen the competition name - doing the above with indata instead
	//auto competition_name = UINodeGetProperty(selected_competition, "competition_name")
	//CompetitionSelectTeams.CompetitionName = competition_name

	//auto difficulty = CompetitionSettings.GetSetting(UIGetNode("Settings/ListBox/Difficulty/SettingValue/Options"))
	pCareerModeManager->SetGameDifficulty(DIFFICULTY(competitionOptions.difficultyIndex));

	//auto game_length = CompetitionSettings.GetSetting(UIGetNode("Settings/ListBox/MatchLength/SettingValue/Options"))
	const TArray<FValueTextBinding<int>>* MatchLengths = // Nick WWS &s to Womens 13s //(CompDefinition.GetIsR7Exclusive()) ? &SevensTimes : 
		&FifteensTimes;
	int MatchLengthInMinutes = ((*MatchLengths)[competitionOptions.lengthIndex]).value;
	pCareerModeManager->SetGameLength(MatchLengthInMinutes);

	//auto auto_squad = CompetitionSettings.GetSetting(UIGetNode("Settings/ListBox/AutoSquadSelection/SettingValue/Options"))
	pCareerModeManager->SetAutoSquadSelection(competitionOptions.auto_squad_mgmt);

	pCareerModeManager->SetAutoSaveEnabled(competitionOptions.autosave);
}

//////////////////////////////////////////////////////////////
void UWWUIScreenCompetitionSelect::LoadData()
{
	UpdateCompetition(0);

	//auto difficulty = pCareerModeManager->GetGameDifficulty();
	//auto difficulty_node = UINodeGetChild(ui_object, "Settings/ListBox/Difficulty/SettingValue/Options")
	//CompetitionSettings.SelectMatchOption(difficulty_node, difficulty)
	competitionOptions.difficultyIndex = pCareerModeManager->GetGameDifficulty();
	UpdateDifficulty(0);

	//auto game_length = pCareerModeManager : GetGameLength()
	//auto length_node = UINodeGetChild(ui_object, "Settings/ListBox/MatchLength/SettingValue/Options")
	//CompetitionSettings.SelectMatchOption(length_node, game_length)
	int MatchLengthInMinues = pCareerModeManager->GetGameLength();
	const TArray<FValueTextBinding<int>>* MatchLengths = // Nick WWS &s to Womens 13s //(CompDefinition.GetIsR7Exclusive()) ? &SevensTimes : 
		&FifteensTimes;
	for (int i = 0; i < MatchLengths->Num(); i++)
	{
		if ((*MatchLengths)[i].value == MatchLengthInMinues)
		{
			competitionOptions.lengthIndex = i;
			break;
		}
	}
	UpdateMatchLength(0);

	//auto auto_squad = pCareerModeManager : GetAutoSquadSelection()
	//auto auto_squad_node = UINodeGetChild(ui_object, "Settings/ListBox/AutoSquadSelection/SettingValue/Options")
	//CompetitionSettings.SelectMatchOption(auto_squad_node, auto_squad)
	competitionOptions.auto_squad_mgmt = pCareerModeManager->GetAutoSquadSelection();
	UpdateSquadMgmt(0);

	competitionOptions.autosave = pCareerModeManager->GetAutoSaveEnabled();
	UpdateAutosave(0);
}

//////////////////////////////////////////////////////////////
void UWWUIScreenCompetitionSelect::InitCompetitionInfo()
{
	//Set competition info text
	
	//auto competition_info = UINodeGetProperty(selected, "competition_info")
	//	See Populator:	node->SetProperty("competition_info", MabString(0, "[ID_COMP_DESCRIPTION_%s]", MabStringHelper::ToUpper(compNameShort).c_str() ) );
	FString CompMnemonicUpper = FString(CompDefinition.GetMnemonic()).ToUpper();

	const FString* pReplacement = UnlicensedCompsRC4.Find(CompMnemonicUpper);

	if (pReplacement)
	{
		CompMnemonicUpper = *pReplacement;
	}

	FString DescriptionString = "[ID_COMP_DESCRIPTION_" + CompMnemonicUpper + "]";

	//UIGetNode("CompetitionInfo").text_string = competition_info
	compDescription->SetText(FText::FromString(UWWUITranslationManager::Translate(DescriptionString)));
	
	// Get competition logo
	//auto competition_image = UINodeGetProperty(selected, "competition_image")

	//logo_node.streaming_texture = competition_image
	//alt_logo_node.streaming_texture = competition_image

	MabString MabCompetitionLogoPath = SIFGameHelpers::GAGetCompetitionIconTextureName(CompDefinition.GetDbId());

	FString name = FString("/Game/Rugby/cmn_con/ui/Logos/league_logos/") + MabCompetitionLogoPath.c_str() + FString(".") + MabCompetitionLogoPath.c_str();
	UTexture2D* pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name));

	if (!pTexture)
	{
		name = "/Game/Rugby/cmn_con/ui/GenericMissingTexture.GenericMissingTexture";
		pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name));
	}

	if (pTexture)
	{
		logo_node->SetBrushFromTexture(pTexture);
		alt_logo_node->SetBrushFromTexture(pTexture);
		last_comp_texture = pTexture;
	}
	else
	{
		ensure(pTexture);
	}
}
	//////////////////////////////////////////////////////////////


void UWWUIScreenCompetitionSelect::UpdateCompetitionInfo(int32 dir)
{
	//auto selected = UINodeGetSelectedNode(ui_node)

	FString CompMnemonicUpper = FString(CompDefinition.GetMnemonic()).ToUpper();

	const FString* pReplacement = UnlicensedCompsRC4.Find(CompMnemonicUpper);

	if (pReplacement)
	{
		CompMnemonicUpper = *pReplacement;
	}

	//Set competition info text
	//auto competition_info = UINodeGetProperty(selected, "competition_info")
	//UIGetNode("CompetitionInfo").text_string = competition_info
	FString DescriptionString = "[ID_COMP_DESCRIPTION_" + CompMnemonicUpper + "]";
	compDescription->SetText(FText::FromString(UWWUITranslationManager::Translate(DescriptionString)));

	// Done already
		//auto game_mode = UINodeGetProperty(selected, "game_mode");

		//auto pCareerModeManager = GACareerGetManager()
		//if (tonumber(game_mode) == fifteens) then
		//GASetGameMode(0);
		//pCareerModeManager:SetCareerGameModeR13()
		//	ChangeMatchLength(false)
		//else
		//GASetGameMode(1);
		//pCareerModeManager:SetCareerGameModeR7()
		//ChangeMatchLength(true)
		//end

	// Get competition logo
	//auto competition_image = UINodeGetProperty(selected, "competition_image")

	MabString MabCompetitionLogoPath = SIFGameHelpers::GAGetCompetitionIconTextureName(CompDefinition.GetDbId());

	FString name = FString("/Game/Rugby/cmn_con/ui/Logos/league_logos/") + MabCompetitionLogoPath.c_str() + FString(".") + MabCompetitionLogoPath.c_str();
	UTexture2D* pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name));

	if (!pTexture)
	{
		name = "/Game/Rugby/cmn_con/ui/GenericMissingTexture.GenericMissingTexture";
		pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name));
	}

	if (!pTexture)
	{
		ensure(pTexture);
		return;
	}

	if(pTexture)
	{
		// If nodes aren't blank animate logo onto screen
//if (parameters.selected_node != "" && parameters.previous_node != "")
//{
		if (dir != 0)
		{
			//Alternate which logo nodes are coming onto screen or going off(to avoid logo flash glitch)
			//auto on_logo = nil
			//auto off_logo = nil

			// skipping check because this function should only be called when it is true


				// Get indexes of selected and previous nodes so we can compare them
				//auto selected_comp_node = UIGetNode(parameters.selected_node)
				//auto selected_comp_index = UINodeGetChildIndex(selected_comp_node)
				//auto previous_comp_node = UIGetNode(parameters.previous_node)
				//auto previous_comp_index = UINodeGetChildIndex(previous_comp_node)

				//Set textures
				//auto competition_image_previous = UINodeGetProperty(previous_comp_node, "competition_image")
				//on_logo.streaming_texture = competition_image
				//off_logo.streaming_texture = competition_image_previous
			logo_node->SetBrushFromTexture(pTexture);
			alt_logo_node->SetBrushFromTexture(last_comp_texture);
			last_comp_texture = pTexture;

			// Stop any existing animations
			//UIFXNodeStopAllAnimations(on_logo)
			//UIFXNodeStopAllAnimations(off_logo)

			//Animate comp logo onto screen in correct direction

			if (dir > 0)
			{
				UWWUIFunctionLibrary::PlayAnimation(this, FName("LogoChangeRightOn"));
				UWWUIFunctionLibrary::PlayAnimation(this, FName("LogoPreviousChangeRightOff"));
			}
			else
			{
				UWWUIFunctionLibrary::PlayAnimation(this, FName("LogoChangeLeftOn"));
				UWWUIFunctionLibrary::PlayAnimation(this, FName("LogoPreviousChangeLeftOff"));
			}
		}
		else
		{
			logo_node->SetBrushFromTexture(pTexture);
			alt_logo_node->SetBrushFromTexture(last_comp_texture);
			last_comp_texture = pTexture;
		}
	}
}

//void UWWUIScreenCompetitionSelect::ChangeMatchLength(bool is_sevens)
//{
//	function ChangeMatchLength(is_sevens)
//
//	auto listbox = match_length_node
//
//	for i = 0, (UINodeGetNumChildren(listbox) - 1) do
//		auto listbox_item = UINodeGetChildByIndex(listbox, i)
//
//		auto game_mode_time = UINodeGetProperty(listbox_item, "game_mode_time");
//
//if game_mode_time == "sevens" then
//
//	if is_sevens then
//		listbox_item.selectable = true;
//listbox_item.enabled = true;
//	else
//		listbox_item.selectable = false;
//listbox_item.enabled = false;
//end
//	elseif game_mode_time == "both" then
//	listbox_item.selectable = true;
//listbox_item.enabled = true;
//else
//	listbox_item.selectable = not is_sevens;
//listbox_item.enabled = not is_sevens;
//end
//	end
//
//	if is_sevens then
//		UINodeSelectNodeByIndex(listbox, 2);
//	else
//		UINodeSelectNodeByIndex(listbox, 1);
//end
//
//	SetSelectionCount(listbox, nil)
//	end
//	
//}

//void UWWUIScreenCompetitionSelect::SetSelectionCount(UWidget* ui_node)
//{
//	function SetSelectionCount(ui_object, parameters)
//	auto selection_counter = UINodeGetChild(UINodeGetParent(ui_object), "SelectionCount")
//	auto selected_index = UINodeGetSelectedNodeIndex(ui_object)
//	auto total_number = 0//UINodeGetNumChildren(ui_node)
//
//	for i = 0, (UINodeGetNumChildren(ui_object) - 1) do
//		auto listbox_item = UINodeGetChildByIndex(ui_object, i)
//
//		if listbox_item.selectable then
//			total_number = total_number + 1;
//end
//	end
//
//	if (total_number == 1) then
//		UITextSetText(selection_counter, "1/" ..tostring(total_number))
//	else
//		if (selected_index > 2) then
//			selected_index = selected_index - 1;
//end
//
//	auto is_sevens = pCareerModeManager::GetIsCareerGameModeR7()
//	auto index_offset = 0;
//
//if not is_sevens then
//	index_offset = 1;
//end
//
//	UITextSetText(selection_counter, tostring(selected_index + index_offset) .. "/" ..tostring(total_number))
//	end
//
//	return true
//	end
//
//}

void UWWUIScreenCompetitionSelect::ExecuteTableFunction(FString InTableId, int InIdx, FString InAction, FString InActionString)
{
	SaveData();
	SIFGameHelpers::GASetSandboxTickEnabled(false);

	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->RequestTransitionStart(0.5f, FWWUIOnTransitionStartComplete::CreateUObject(this, &UWWUIScreenCompetitionSelect::ProceedToTeamSelect), true);
	}
}

void UWWUIScreenCompetitionSelect::ProceedToTeamSelect()
{
	UWWUIStateScreenCompetitionSelectTeamsData* InData = NewObject<UWWUIStateScreenCompetitionSelectTeamsData>();
	InData->comp_id = CompDefinition.GetDbId();
	InData->comp_name = CompDefinition.GetName();

	pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CompetitionSelectTeams, InData);
}

