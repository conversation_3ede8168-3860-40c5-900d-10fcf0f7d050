// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIScreenTeamDetails.h"

#include "RugbyGameInstance.h"
#include "SIFAchievementChecker.h"
#include "RUUIDatabaseQueryManager.h"

// Helpers
#include "Rugby/Utility/Helpers/SIFGameHelpers.h"
#include "Rugby/Utility/Helpers/SIFUIHelpers.h"
#include "Rugby/Utility/Helpers/WWCSVHelpers.h"

// Generated Headers
#include "UI/GeneratedHeaders/WWUIScreenTeamDetails_UI_Namespace.h"
#include "UI/GeneratedHeaders/WWUIScreenCareerPlayerDrafting_UI_Namespace.h"
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"

// Data table descriptors
#include "Rugby/DataTables/Characters/CharacterAssetTexture.h"

// Databases
#include "Rugby/Databases/RUGameDatabaseManager.h"

// WW UI
#include "UI/Components/WWUIUserWidgetTacticsList.h"

// WW UI Engine
#include "WWUIScrollBox.h"
#include "WWUITabSwitcher.h"
#include "WWUITranslationManager.h"
#include "WWUIListField.h"
#include "WWUIRichTextBlockWithTranslate.h"
#include "WWUIFunctionLibrary.h"

// UE4
#include "ScrollBox.h"
#include "../Components/WWUIUserWidgetPlayerDraftPanel.h"

// Debug
#include "Mab/Utility/MabStringHelper.h"

#include "Match\RugbyUnion\RUStadiumManager.h"
#include "Utility/Helpers/SIFAudioHelpers.h"
#include "Image.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
#include "WWUIEditableText.h"
#include "WWUIEditableTextBox.h"
#include "Engine/Font.h"
#include "FanHub/WWRugbyFanHubService.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Database.h"

#define EDITABLE_TEXT_FIELD ("EditableTextField")

#define TRIGGER_DELAY_TIME						(1.0f)
#define TEAM_DETAILS_MAX_TEXT_LENGTH			(30)


bool AlphabeticalStripSortComparator(const RUDB_TEAM_STRIP& left, const RUDB_TEAM_STRIP& right)
{
	return strcmp(left.GetName(), right.GetName()) < 0;
}

bool AlphabeticalLogoSortComparator(const RUDB_TEAM_LOGO& left, const RUDB_TEAM_LOGO& right)
{
	return strcmp(left.GetName(), right.GetName()) < 0;
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::RegisterFunctions()
{
	AddInputAction("UI_Back", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnBack), false, true); // B

	AddInputAction("RU_UI_ACTION_TABRIGHT", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::IncrementTab)); // B
	AddInputAction("RU_UI_ACTION_TABLEFT", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::DecrementTab)); // B

	AddInputAction("UI_Right", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnRight), true); // B
	AddInputAction("UI_Left", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnLeft), true); // B

	AddInputAction("RU_UI_ACTION_MINUS_TWENTY", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::PlayerAttributesMinusTwenty));
	AddInputAction("RU_UI_ACTION_PLUS_TWENTY", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::PlayerAttributesPlusTwenty));

	// My Squad AKA Drafting panel bindings:
	AddInputAction("RU_UI_ACTION_CAREERMODE_PLAYER_PROFILE", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnCareerModePlayerProfile)); // B

	AddInputAction("RU_UI_ACTION_SET_CAPTAIN", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnSetCaptain)); // B
	AddInputAction("RU_UI_ACTION_SET_KICKER", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnSetKicker)); // B
	AddInputAction("RU_UI_ACTION_SET_PLAY_KICKER", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnSetPlayKicker)); // B

#if PLATFORM_SWITCH 
	AddInputAxis("UI_Triggers_Axis", FWWUIScreenAxisDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnRotationInput));
#else
	AddInputAction("RU_UI_ACTION_SET_ROLES", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnSetRoles)); // B
#endif


	AddInputAction("RU_UI_ACTION_EDIT_TEXT", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnOpenTextEntry)); // B

	AddInputAction("RU_UI_ACTION_SWAP_PLAYER", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnSwapPlayer)); // B

	AddInputAction("RU_UI_ACTION_CAREERMODE_TEAM_AUTOFILL", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnTeamAutoFill)); // B
	AddInputAction("RU_UI_ACTION_CAREERMODE_TERMINATE_CONTRACT", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnCareerModeTerminateContract)); // B

	AddInputAction("UI_Select", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnSelect)); // B

	AddInputAction("RU_UI_ACTION_SELECT", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnResetTeam), false, true); // B

		// Debug options
#if !UE_BUILD_SHIPPING
#if PLATFORM_WINDOWS
	AddInputAction("RU_DEBUG_PLAYER_CREATOR_SAVE_CSV", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnDebugSaveCSV)); // B
#endif
#endif // UE_BUILD_SHIPPING
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::Startup(UWWUIStateScreenData* InData /*= nullptr*/)
{
	pCachedInData = InData;

	bIsEditTeam = false;
	bIsDownload = false;
	bInputEnabled = true;

	UWWUITeamDetailsScreenData* pTeamDetailsInData = Cast<UWWUITeamDetailsScreenData>(InData);
	if (pTeamDetailsInData)
	{
		bIsDownload = pTeamDetailsInData->bIsDownload;
		bIsEditTeam = pTeamDetailsInData->bIsEdit;
	}

	UWWUIScrollBox* pTacticsScrollBox = GetTabDefaultScrollbox(ETeamDetailsTab::TACTICS);
	if (pTacticsScrollBox)
	{
		RegisterScrollBox(WWUIScreenTeamDetails_UI::ScrollBoxTactics, pTacticsScrollBox);
	}

	//Initialise the draft panel in the "SQUAD" tab.
	pDraftPanel = Cast<UWWUIUserWidgetPlayerDraftPanel>(FindChildWidget(WWUIScreenTeamDetails_UI::PlayerDraftingPanel));

	if (pDraftPanel)
	{
		pDraftPanel->InitPlayerDraftPanel(
			FindChildWidget(WWUIScreenTeamDetails_UI::LegendText),
			FindChildWidget(WWUIScreenCareerPlayerDrafting_UI::ClubBudget),
			FindChildWidget(WWUIScreenTeamDetails_UI::TabContainer),
			bIsDownload,
			true,
			false
		);
		pDraftPanel->ScreenRef = this; 
	}
	else
	{
		ensure(pDraftPanel);
	}

	CurrentTab = ETeamDetailsTab::DETAILS;

	bResetEnabled = false;

	UWidget* pSearchTextWidget = FindChildWidget(WWUIScreenTeamDetails_UI::EditableTextBoxSearchTextEntry);

	//	Mattt H - This is confusing and annoying, but this is just used as the text editor and not a search box.
	UWWUIEditableTextBox* pSearchTextBox = Cast<UWWUIEditableTextBox>(pSearchTextWidget);
	if (pSearchTextBox)
	{
		pSearchTextBox->SetMaxCharLength(TEAM_DETAILS_MAX_TEXT_LENGTH);
		pSearchTextBox->ClearKeyboardFocusOnCommit = false;

		pSearchTextBox->SynchronizeProperties();
	}

	OptionValueMap.Empty();

	PopulateListBoxOptions(GetTabDefaultScrollbox(ETeamDetailsTab::DETAILS), &UWWUIScreenTeamDetails::TeamDetailsPopulateOption);
	PopulateListBoxOptions(GetTabDefaultScrollbox(ETeamDetailsTab::STRIP), &UWWUIScreenTeamDetails::StripPopulateOption);
	PopulateListBoxOptions(GetTabDefaultScrollbox(ETeamDetailsTab::ATTRIBUTES), &UWWUIScreenTeamDetails::AttributePopulateOption);

	// Update primary and alternate strip creators.
	StripUpdateStripCreator(EStripType::PRIMARY_STRIP);
	StripUpdateStripCreator(EStripType::ALTERNATE_STRIP);

	UWWUIUserWidgetTacticsList* pTacticsList = Cast<UWWUIUserWidgetTacticsList>(FindChildWidget(WWUIScreenTeamDetails_UI::TacticsList));

	if (pTacticsList)
	{
		if (pTacticsScrollBox)
		{
			pTacticsList->Populate(pTacticsScrollBox, this);
		}
	}


	UWidget* pHeaderWidget = FindChildWidget(WWUIScreenTeamDetails_UI::HeaderWidget);

	if (pHeaderWidget)
	{
		UTextBlock* pHeaderSubtitleWidget = Cast<UTextBlock>(FindChildOfTemplateWidget(pHeaderWidget, WWUIScreenTeamDetails_UI::Subtitle));

		if (pHeaderSubtitleWidget)
		{
			FString BreadCrumbString = "[ID_CUSTOMISE_CREATE_TEAM]";

			if (bIsDownload) BreadCrumbString = "[ID_VIEW_TEAM_DETAILS]";
			else if (bIsEditTeam) BreadCrumbString = "[ID_CUSTOMISE_EDIT_TEAM]";

			pHeaderSubtitleWidget->SetText(FText::FromString(UWWUITranslationManager::Translate(BreadCrumbString)));
		}
		else
		{
			ensure(pHeaderSubtitleWidget);
		}
	}
	else
	{
		ensure(pHeaderWidget);
	}

	//< Update text length for editable text fields. >
	if (UWWUIScrollBox* pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTeamDetails_UI::ScrollBoxTeamDetails)))
	{
		if (UWWUIEditableText* pTextBlock = Cast<UWWUIEditableText>(FindChildOfTemplateWidget(pScrollBox->GetListField((int32)ETeamDetailsOption::TEAM_NAME), EDITABLE_TEXT_FIELD)))
		{
			pTextBlock->SetMaxCharLength(TEAM_DETAILS_MAX_TEXT_LENGTH);
		}

		if (UWWUIEditableText* pTextBlock = Cast<UWWUIEditableText>(FindChildOfTemplateWidget(pScrollBox->GetListField((int32)ETeamDetailsOption::MNEMONIC), EDITABLE_TEXT_FIELD)))
		{
			pTextBlock->SetMinCharLength(MNEMONIC_REQUIRED_LENGTH);
		}
	}

	UpdateCreatorBox();

#if (PLATFORM_PS4 || PLATFORM_XBOXONE || PLATFORM_SWITCH)
	if (UWWUIScrollBox* pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTeamDetails_UI::ScrollBoxTeamDetails)))
	{

	//exclude ps4 when setting virtual keyboard to alpha-numeric type
	#if (!PLATFORM_PS4)
		SIFUIHelpers::SetEditableTextVirtualKeyboardType(Cast<UWWUIEditableText>(FindChildOfTemplateWidget(pScrollBox->GetListField((int32)ETeamDetailsOption::TEAM_NAME), EDITABLE_TEXT_FIELD)), EVirtualKeyboardType::AlphaNumeric);
	#endif

		UWWUIEditableText* mnemonicEditableText = Cast<UWWUIEditableText>(FindChildOfTemplateWidget(pScrollBox->GetListField((int32)ETeamDetailsOption::MNEMONIC), EDITABLE_TEXT_FIELD));
		if (mnemonicEditableText)
		{
			SIFUIHelpers::SetEditableTextVirtualKeyboardType(mnemonicEditableText, EVirtualKeyboardType::AlphaNumeric);
			//have the mnemonic text field check for special characters
			mnemonicEditableText->CheckSpecialCharacters = true;
		}

	}
#endif



	//if (!bIsDownload)
	//{
	//	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	//	if (pRugbyGameInstance)
	//	{
	//		SIFGameWorld* pMenuGameWorld = pRugbyGameInstance->GetMenuGameWorld();

	//		if (pMenuGameWorld)
	//		{
	//			SSCutSceneManager* pCutsceneManager = pMenuGameWorld->GetCutSceneManager();

	//			if (pCutsceneManager)
	//			{
	//				CinematicCutsceneDelegateHandle = pCutsceneManager->CinematicCutsceneStartedDelegate.AddUObject(this, &UWWUIScreenTeamDetails::OnCinematicCutsceneStarted);
	//			}
	//		}
	//	}
	//}

	UWWUIScrollBox* pStripScrollBox = GetTabDefaultScrollbox(ETeamDetailsTab::STRIP);

	if (pStripScrollBox)
	{
		UWWUIListField* pCompListField = pStripScrollBox->GetListField((int32)EStripOption::COMPETITION);

		if (pCompListField)
		{
			if (bIsDownload)
			{
				pCompListField->SetVisibility(ESlateVisibility::Collapsed);
			}
			else
			{
				pCompListField->SetIsEnabled(false);
			}
		}

		UWWUIListField* pTypeListField = pStripScrollBox->GetListField((int32)EStripOption::TYPE);

		if (pTypeListField)
		{
			if (bIsDownload)
			{
				pTypeListField->SetVisibility(ESlateVisibility::Collapsed);
			}
		}
	}

	ugcUsernameCheckCompleteHandle = SIFApplication::GetApplication()->OnUGCUsernameCheckComplete.AddUObject(this, &UWWUIScreenTeamDetails::HandleUGCUsernameCheckComplete);
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamDetails::Shutdown()
{
	Super::Shutdown();
	SIFApplication::GetApplication()->OnUGCUsernameCheckComplete.Remove(ugcUsernameCheckCompleteHandle);
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamDetails::UpdateCreatorBox()
{
	UWidget* pCreatorBox = FindChildWidget(WWUIScreenTeamDetails_UI::HorizontalBoxCreator);
	if (pCreatorBox)
	{
#ifdef SHOW_UGC_CREATOR
		UTextBlock* pCreatedByText = Cast<UTextBlock>(FindChildWidget(WWUIScreenTeamDetails_UI::TextBlockCreator));
		RUDB_TEAM* pCustomisationTeam = GetCustomisationTeam();
		FString creatorName = "";
		FString uploaderName = "";
		
		if (pCustomisationTeam /*&& pCustomisationTeam->IsCustom()*/)
		{
			if (pCustomisationTeam->GetDbId() == 0)
			{
				creatorName = pCustomisationTeam->GetCreatedBy();
				uploaderName= pCustomisationTeam->GetUploadedBy();
			}
			else if (SIFGameHelpers::GAGetTeamDownloadUser(pCustomisationTeam->GetDbId()) != "")
			{
				//This means we're viewing a previously downloaded player
				FString downloadInfo = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetTeamDownloadUser(pCustomisationTeam->GetDbId()));
				creatorName = downloadInfo;

				downloadInfo.Split(",", &creatorName, &uploaderName);
			}
		}

		if (pCreatedByText && creatorName != "")
		{
			FString CreatedByString = "[ID_CREATED_BY]: " + creatorName + "\n[ID_UPLOADED_BY]: " + uploaderName;
			SetWidgetText(pCreatedByText, FText::FromString(UWWUITranslationManager::Translate(CreatedByString)));
			pCreatorBox->SetVisibility(ESlateVisibility::Visible);
		}
		else
#endif
		{
			pCreatorBox->SetVisibility(ESlateVisibility::Collapsed);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::OnInFocus()
{
	OnWindowEnter();
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::OnOutFocus(bool ShouldOutFocus /* = true */)
{
	if (m_triggerAxisHandle.IsValid())
	{
		UWWUIFunctionLibrary::StopTimer(m_triggerAxisHandle);
	}

	if (!bIsDownload)
	{
		static IConsoleVariable* CVarShowWomenInCutscenes = IConsoleManager::Get().FindConsoleVariable(TEXT("ww.rugby.ShowWomenInCutscenes"));
		if (CVarShowWomenInCutscenes)
		{
			CVarShowWomenInCutscenes->Set(0);
		}
	}
}

//===============================================================================
//===============================================================================

bool UWWUIScreenTeamDetails::OnSystemEvent(WWUINodeProperty& eventParams)
{
	FString SystemEvent = eventParams.GetStringProperty("system_event");

	UE_LOG(LogTemp, Display, TEXT("UWWUIScreenTeamDetails.OnSystemEvent %s"), *SystemEvent);

	if (SystemEvent == "onnetworkdisconnected")
	{
		if (bIsDownload)
		{
			// #rc3_legacy_popup
			// Launch Popup LeaderBoardsConnectionLost
			ensureMsgf(false, TEXT("Missing a popup"));
		}
	}
	else if (SystemEvent.Compare(GAME_DB_SAVE_OK_NAME) == 0 || SystemEvent.Compare(GAME_DB_SAVE_FAIL_NAME) == 0)
	{
		SIFGameHelpers::GAFaceRendererClearTextureCache();

		//SIFGlobal.care_about_server = false;*/

		//if TeamCreatorExitConfirm.is_return_to_edit then
		//	TeamCreatorExitConfirm.is_return_to_edit = false;
		//if (GAIsCharacterCreator()) then
		//	GASetGameMode(SIFGlobal.GAME_MODE_FIFTEENS);
		//end
		//	ProceedToWindowBackwards("CustomiseTeamSelectTeam");
		//elseif GAIsCharacterCreator() then
		//	ProceedToWindowBackwards("CharacterCreator");
		//elseif CustomiseTeamDetails.is_download then
		//	CustomiseTeamDetails.is_download = false;
		//ProceedToPreviousWindow(ui_object, parameters, true)
		//	--ProceedToWindowBackwards("ListTeams");
		//else
		//	ProceedToWindowBackwards("MainMenu")
		//	end
		//	end

		SIFUIHelpers::ShowSavingOverlay(false);

		Exit();

		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
		if (pRugbyGameInstance)
		{
			SIFGameHelpers::GASetGameMode(GAME_MODE::GAME_MODE_RU13);
			pRugbyGameInstance->DealMenuAction(SCREEN_CANCEL_FADE, GetScreenID());
		}
	}
	else if (SystemEvent.Compare(STORED_AND_CLEARED_TEAM_EVENT) == 0)
	{
		// Stored the team, now save the database.
		SIFGameHelpers::GASaveCustomDatabase();
	}

	return true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::TableOnSelectionChange(FString InTableId, int OldIdx, int NewIdx)
{
	if (!GetInputEnabled())
	{
		return;
	}

	if (InTableId == WWUIScreenTeamDetails_UI::ScrollBoxTactics)
	{
		if (NewIdx == TACTICS_FIRST_EDITABLE_LIST_INDEX)
		{
			UWWUIScrollBox* pTacticsScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(InTableId));

			if (pTacticsScrollBox)
			{
				UScrollBox* pScrollBox = pTacticsScrollBox->GetScrollBox();

				if (pScrollBox)
				{
					pScrollBox->ScrollWidgetIntoView(pTacticsScrollBox->GetListField(TACTICS_DEFENCE_TITLE_INDEX));
				}
			}
		}
	}

	if (InTableId == WWUIScreenTeamDetails_UI::DraftListBox)
	{
		pDraftPanel->TableOnSelectionChange(InTableId, OldIdx, NewIdx);
	}

	if (InTableId == WWUIScreenTeamDetails_UI::ScrollBoxTeamDetails)
	{
		TeamDetailsSelectedIndex = NewIdx;
		TeamDetailsUpdateLegend();
		TeamDetailsUpdateSearchBoxCharacterLimit(NewIdx);
	}
}

void UWWUIScreenTeamDetails::ExecuteTableFunction(FString InTableId, int InIdx, FString InAction, FString InActionString)
{
	Super::ExecuteTableFunction(InTableId, InIdx, InAction, InActionString);

	if (!GetInputEnabled())
	{
		return;
	}

	if (CurrentTab == ETeamDetailsTab::SQUAD && !bIsDownload)
	{
		if (pDraftPanel->OnSelect()) return;
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::OnWindowEnter()
{
	OnNewTab(CurrentTab);

	m_canTriggerAxis = true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::SetInitialFocus(UWidget* pScrollBoxWidget)
{
	if (pScrollBoxWidget)
	{
		//Find the first element of the main menu and set focus
		UWWUIScrollBox* pCurrentScrollBox = Cast<UWWUIScrollBox>(pScrollBoxWidget);

		if (pCurrentScrollBox)
		{
			pCurrentScrollBox->FocusFirstListField(SIFApplication::GetApplication()->GetMasterPlayerController());
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::OnBack(APlayerController* OwningController)
{
	if (CurrentTab == ETeamDetailsTab::SQUAD && !bIsDownload)
	{
		if (pDraftPanel->OnBack()) return;
	}

	if (!bInputEnabled) return;
	if (bIsDownload)
	{
		NoSaveOptionOnClick(OwningController);
		return;
	}

	if (PC_KEYBOARD_FOCUS) //go through other register functions and return there too
	{
		UnbindPCTextEntry();
		ExitTextEntry(nullptr);
		return;
	}
	
	SyncCurrentTabToTeam();

	TArray<FModalButtonInfo> ButtonInfo;

	FWWUIModalDelegate SaveDelegate;
	SaveDelegate.BindUObject(this, &UWWUIScreenTeamDetails::SaveOptionOnClick);
	ButtonInfo.Add(FModalButtonInfo("[ID_CUSTOMISE_PLAYER_BUTTON_SAVE]", SaveDelegate));

	FWWUIModalDelegate NoSaveDelegate;
	NoSaveDelegate.BindUObject(this, &UWWUIScreenTeamDetails::NoSaveOptionOnClick);
	ButtonInfo.Add(FModalButtonInfo("[ID_CUSTOMISE_PLAYER_BUTTON_NO]", NoSaveDelegate));

	UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

	modalData->WarningDialogue = "[ID_CUSTOMISE_PLAYER_POPUP_MAIN_TEXT]";
	modalData->LegendString = "[ID_ASSIGN_CONTROLLER_NEUTRAL_HELP]";
	modalData->ButtonData = ButtonInfo;

	modalData->CloseOnBackButton = true;
	modalData->CloseOnSelectButton = false;

	// Force the axis trigger to reset if we go back from this popup.
	modalData->OnBackDelegate.BindLambda([this](APlayerController* pPlayerController) {m_canTriggerAxis = true; return true; });

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamDetails::OnCareerModePlayerProfile(APlayerController* OwningPlayer)
{
	if (CurrentTab == ETeamDetailsTab::SQUAD)
	{
		if (pDraftPanel->OnCareerModePlayerProfile()) return;
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamDetails::OnCareerModeTerminateContract(APlayerController* OwningPlayer)
{
	if (CurrentTab == ETeamDetailsTab::SQUAD && !bIsDownload)
	{
		if (pDraftPanel->OnCareerModeTerminateContract()) return;
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamDetails::OnSetCaptain(APlayerController* OwningPlayer)
{
	if (CurrentTab == ETeamDetailsTab::SQUAD && !bIsDownload)
	{
		if (pDraftPanel->OnSetCaptain()) return;
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamDetails::OnSetKicker(APlayerController* OwningPlayer)
{
	if (CurrentTab == ETeamDetailsTab::SQUAD && !bIsDownload)
	{
		if (pDraftPanel->OnSetKicker()) return;
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamDetails::OnSetPlayKicker(APlayerController* OwningPlayer)
{
	if (CurrentTab == ETeamDetailsTab::SQUAD && !bIsDownload)
	{
		if (pDraftPanel->OnSetPlayKicker()) return;
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamDetails::OnSetRoles(APlayerController* OwningPlayer)
{
	m_triggerAxisHandle = UWWUIFunctionLibrary::OnTimer(TRIGGER_DELAY_TIME, FTimerDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::ResetAxisTrigger), false);
	m_canTriggerAxis = false;

	if (CurrentTab == ETeamDetailsTab::SQUAD && !bIsDownload)
	{
		if (pDraftPanel->OnSetRoles()) return;
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamDetails::OnSwapPlayer(APlayerController* OwningPlayer)
{
	if (CurrentTab == ETeamDetailsTab::SQUAD && !bIsDownload)
	{
		if (pDraftPanel->OnSwapPlayer()) return;
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::OnTeamAutoFill(APlayerController* OwningPlayer)
{
	if (CurrentTab == ETeamDetailsTab::SQUAD && !bIsDownload)
	{
		if (pDraftPanel->OnTeamAutoFill()) return;
	}
}

//===============================================================================
//===============================================================================

bool UWWUIScreenTeamDetails::SquadValidate()
{
	RUDB_TEAM* pCustomisationTeam = GetCustomisationTeam();

	if (pCustomisationTeam)
	{
		if (!pCustomisationTeam->HasEnoughPlayers())
		{
			FString ContentString = (pCustomisationTeam->GetIsR7Exclusive()) ? "[ID_CUSTOMISE_TEAM_VALIDATE_PLAYERS_SEVENS]" : "[ID_CUSTOMISE_TEAM_VALIDATE_PLAYERS]";

			LaunchInvalidTeamPopup(ContentString);

			return false;
		}

		int32 MissingTitlesBitField = pCustomisationTeam->GetMissingTitledPlayerBitField();

		if (MissingTitlesBitField != 0)
		{
			FString ContentString = SIFGameHelpers::GAGetMissingTitledPlayerStringFromBitField(MissingTitlesBitField);
			
			LaunchInvalidTeamPopup(ContentString);

			return false;
		}

		SIFApplication::GetApplication()->GetAchievementChecker()->OnCustomTeamCreated(*pCustomisationTeam);
	}

	return true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::OnSelect(APlayerController* OwningPlayer)
{
	if (CurrentTab == ETeamDetailsTab::SQUAD)
	{
		pDraftPanel->OnSelect();
	}
	else if (CurrentTab == ETeamDetailsTab::DETAILS && PC_KEYBOARD_FOCUS)
	{
		UWWUIEditableTextBox* pEditableTextBox = Cast<UWWUIEditableTextBox>(FindChildWidget(WWUIScreenTeamDetails_UI::EditableTextBoxSearchTextEntry));
		if (pEditableTextBox)
		{
			FText textEnteredString = pEditableTextBox->GetText();

			pEditableTextBox->CommitTextManual(textEnteredString, ETextCommit::OnEnter);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::OnResetTeam(APlayerController* OwningPlayer)
{
	if (CurrentTab == ETeamDetailsTab::DETAILS && !PC_KEYBOARD_FOCUS && bResetEnabled)
	{
		TArray<FModalButtonInfo> ButtonData;

		ButtonData.Add(FModalButtonInfo("[ID_RESET_POPUP_YES]", FWWUIModalDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::OnResetTeamConfirm)));
		ButtonData.Add(FModalButtonInfo("[ID_RESET_POPUP_NO]"));

		SIFUIHelpers::LaunchWarningPopup("[ID_RESET_TEAM_PROMPT]", "[ID_POPUP_HELPTEXT_BACK]", ButtonData, true, true, 1);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::OnLeft(APlayerController* OwningController)
{
	if (!bInputEnabled)
	{
		return;
	}

	IncrementOption(-1);
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::OnRight(APlayerController* OwningController)
{
	if (!bInputEnabled)
	{
		return;
	}

	IncrementOption(+1);
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::PlayerAttributesPlusTwenty(APlayerController* OwningPlayer)
{
	IncrementOption(+20);
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::PlayerAttributesMinusTwenty(APlayerController* OwningPlayer)
{
	IncrementOption(-20);
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::IncrementTab(APlayerController* OwningController)
{
	if (!bInputEnabled)
	{
		return;
	}

	UWWUITabSwitcher* pTabSwitcher = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenTeamDetails_UI::TabContainer));

	if (pTabSwitcher)
	{
		pTabSwitcher->IncrementTab();

		OnNewTab((ETeamDetailsTab)pTabSwitcher->GetActiveTabID());
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::DecrementTab(APlayerController* OwningController)
{
	if (!bInputEnabled)
	{
		return;
	}

	UWWUITabSwitcher* pTabSwitcher = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenTeamDetails_UI::TabContainer));

	if (pTabSwitcher)
	{
		pTabSwitcher->DecrementTab();

		OnNewTab((ETeamDetailsTab)pTabSwitcher->GetActiveTabID());
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::OnRotationInput(float AxisValue, APlayerController* OwningPlayer)
{
	if (m_canTriggerAxis)
	{
		if (AxisValue < 0.0f)
		{
			OnSetRoles(OwningPlayer);
		}
		else if (AxisValue > 0.0f)
		{
			OnSetRoles(OwningPlayer);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::OnNewTab(ETeamDetailsTab NewTab)
{
	// Sync the old tab before we switch.
	SyncCurrentTabToTeam();

	CurrentTab = NewTab;

	UpdateOptionList();

	UWidget* pFocusScrollBox = GetTabDefaultScrollbox(CurrentTab);

	if (pFocusScrollBox)
	{
		// If coming from fanhub - list teams don't set focus unless on the tactics or squad tab.
		if (!bIsDownload || CurrentTab == ETeamDetailsTab::TACTICS || CurrentTab == ETeamDetailsTab::SQUAD)
		{
			SetInitialFocus(pFocusScrollBox);
		}
	}

	switch (NewTab)
	{
	case ETeamDetailsTab::DETAILS:
	{
		TeamDetailsOnWindowEnter();
	}
		break;
	case ETeamDetailsTab::STRIP:
	{
		StripOnWindowEnter();
	}
		break;
	case ETeamDetailsTab::ATTRIBUTES:
	{
		AttributesOnWindowEnter();
	}
		break;
	case ETeamDetailsTab::TACTICS:
	{
		TacticsOnWindowEnter();
	}
		break;
	case ETeamDetailsTab::SQUAD:
	{
		pDraftPanel->OnWindowEnter();
	}
		break;
	default:
		break;
	}
}

//===============================================================================
//===============================================================================

UWWUIScrollBox* UWWUIScreenTeamDetails::GetTabDefaultScrollbox(ETeamDetailsTab Tab)
{
	FString ScrollBoxToFind = "";

	switch (Tab)
	{
        case ETeamDetailsTab::DETAILS:
        {
            ScrollBoxToFind = WWUIScreenTeamDetails_UI::ScrollBoxTeamDetails;
        }
		break;
        case ETeamDetailsTab::STRIP:
        {
            ScrollBoxToFind = WWUIScreenTeamDetails_UI::ScrollBoxStrip;
        }
		break;
        case ETeamDetailsTab::ATTRIBUTES:
        {
            ScrollBoxToFind = WWUIScreenTeamDetails_UI::ScrollBoxAttributes;
        }
		break;
        case ETeamDetailsTab::TACTICS:
        {
            ScrollBoxToFind = WWUIScreenTeamDetails_UI::ScrollBoxTactics;
        }
		break;
        case ETeamDetailsTab::SQUAD:
        {
        }
		break;
	default:
		break;
	}

	return Cast<UWWUIScrollBox>(FindChildWidget(ScrollBoxToFind));
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::AddOptionBindings(TArray<FValueIDBinding> NewBindings, UWidget* pListField, int32 BindingDefaultValue)
{
	if (!pListField)
	{
		return;
	}

	if (NewBindings.Num() > 0)
	{
		FOptionProperties NewOption = FOptionProperties(BindingDefaultValue, NewBindings);

		OptionValueMap.Add(pListField, NewOption);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::PopulateListBoxOptions(UWWUIScrollBox* pScrollBox, PopulateOptionFunction PopulateFunction)
{
	if (pScrollBox)
	{
		for (int i = 0; i < pScrollBox->GetListLength(); i++)
		{
			UWWUIListField* pField = pScrollBox->GetListField(i);

			if (pField)
			{
				if (PopulateFunction)
				{
					(this->*PopulateFunction)(i, pField);
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::UpdateOptionList()
{
	UWWUIScrollBox* pScrollBox = GetTabDefaultScrollbox(CurrentTab);

	// If we are doing the strip of a downloaded team, just set the option text.
	if (CurrentTab == ETeamDetailsTab::STRIP)
	{
		RUDB_TEAM* pTeamData = GetCustomisationTeam();

		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		if (pRugbyGameInstance)
		{
			if (bIsDownload && pScrollBox && pTeamData)
			{
				for (int32 i = (int32)EStripOption::PRIMARY_STRIP; i <= (int32)EStripOption::ALTERNATE_STRIP; i++)
				{
					FString StripName = "";
					int32 StripDatabaseID = pTeamData->GetStripId(i - (int32)EStripOption::PRIMARY_STRIP);
					if (StripDatabaseID == DB_INVALID_ID && bIsDownload)
					{
						UWWRugbyFanHubService* pFanHubService = Cast<UWWRugbyFanHubService>(pRugbyGameInstance->GetFanHubService());

						if (pFanHubService)
						{
							TSharedPtr<RUDB_TEAM_STRIP>* pFoundServerStripData = pFanHubService->GetServerStripFromMap(pTeamData->GetServerID(), pTeamData->GetServerStripId(i - (int32)EStripOption::PRIMARY_STRIP));

							if (pFoundServerStripData)
							{
								TSharedPtr<RUDB_TEAM_STRIP> pServerStripData = *pFoundServerStripData;

								if (pServerStripData)
								{
									StripName = FString(pServerStripData->GetName());

#ifdef SHOW_UGC_CREATOR
									//Showing the username of the player that created the player you're editing
									UWidget* pDownloadedPlayerInfoWidget = FindChildWidget(WWUIScreenTeamDetails_UI::HorizontalBoxStripCreator);
									if (pDownloadedPlayerInfoWidget)
									{
										UTextBlock* pCreatedByText = Cast<UTextBlock>(FindChildWidget((i == (int32)EStripOption::PRIMARY_STRIP) ? WWUIScreenTeamDetails_UI::TextHomeCreator : WWUIScreenTeamDetails_UI::TextAwayCreator));

										FString creatorName = pServerStripData->GetCreatedBy();
										FString uploaderName = pServerStripData->GetUploadedBy();

										if (pCreatedByText)
										{
											FString CreatedByString;

											if (creatorName != "")
											{
												CreatedByString = "[ID_CREATED_BY]: " + creatorName + "\n[ID_UPLOADED_BY]: " + uploaderName;
											}

											SetWidgetText(pCreatedByText, FText::FromString(UWWUITranslationManager::Translate(CreatedByString)));
										}
									}
#endif
								}
							}
						}
					}
					else
					{
						RUGameDatabaseManager* pDatabaseManager = pRugbyGameInstance->GetGameDatabaseManager();

						if (pDatabaseManager)
						{
							RUDB_TEAM_STRIP Strip;
							pDatabaseManager->LoadData<RUDB_TEAM_STRIP>(Strip, StripDatabaseID);
							StripName = FString(Strip.GetName());
						}
					}

					WWOption::ForceOptionText(this, pScrollBox->GetListField(i), StripName);
				}

				return;
			}
		}
	}

	UpdateOptionList(pScrollBox);
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::UpdateOptionList(UWWUIScrollBox* pScrollBox)
{
	if (pScrollBox)
	{
		WWOption::UpdateAllOptions(this, pScrollBox->GetScrollBox(), OptionValueMap);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::IncrementOption(int32 Direction)
{
	// If coming from the list teams screen (fanhub) disable input left and right.
	if (bIsDownload)
	{
		return;
	}

	if (CurrentTab == ETeamDetailsTab::TACTICS)
	{
		UWWUIUserWidgetTacticsList* pTacticsList = Cast<UWWUIUserWidgetTacticsList>(FindChildWidget(WWUIScreenTeamDetails_UI::TacticsList));

		if (pTacticsList)
		{
			pTacticsList->IncrementOption(Direction);
		}
		return;
	}

	UWWUIScrollBox* pCurrentFocusedScrollBox = GetTabDefaultScrollbox(CurrentTab);

	if (pCurrentFocusedScrollBox)
	{
		int32 FocusedScrollBoxSelectedIndex = pCurrentFocusedScrollBox->GetSelectedIndex();

		UWWUIListField* pListField = pCurrentFocusedScrollBox->GetListField(FocusedScrollBoxSelectedIndex);

		if (pListField)
		{
			WWOption::UpdateOption(this, pListField, OptionValueMap, Direction);

			FOptionProperties* pOptionProperty = OptionValueMap.Find(pListField);

			if (pOptionProperty)
			{
				switch (CurrentTab)
				{
				case ETeamDetailsTab::DETAILS:
				{
					TeamDetailsOnSelectedOptionChanged(FocusedScrollBoxSelectedIndex, pOptionProperty);
				}
					break;
				case ETeamDetailsTab::STRIP:
				{
					StripOnSelectedOptionChanged(FocusedScrollBoxSelectedIndex, pOptionProperty->CurrentOptionValue);
				}
					break;
				case ETeamDetailsTab::ATTRIBUTES:
					break;
				case ETeamDetailsTab::TACTICS:
					break;
				case ETeamDetailsTab::SQUAD:
					break;
				default:
					break;

				}
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::SetLegendString(FString NewLegendString)
{
	UWWUIRichTextBlockWithTranslate* pLegendRichText = Cast<UWWUIRichTextBlockWithTranslate>(FindChildWidget(WWUIScreenTeamDetails_UI::LegendText));

	if (pLegendRichText)
	{
		pLegendRichText->SetText(NewLegendString);
	}
}

//===============================================================================
//===============================================================================

RUDB_TEAM* UWWUIScreenTeamDetails::GetCustomisationTeam()
{
	RUUIDatabaseQueryManager* pDatabaseQueryManager = SIFUIHelpers::GetQueryManager();

	RUDB_TEAM* pTeamData = nullptr;

	if (pDatabaseQueryManager)
	{
		pTeamData = pDatabaseQueryManager->GetTeamData();
	}

	return pTeamData;
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::TeamDetailsOnWindowEnter()
{
	//TeamDetailsSyncFromTeam();

	RUDB_TEAM* pCustomisationTeam = GetCustomisationTeam();

	if (pCustomisationTeam)
	{
		RUUIDatabaseQueryManager* pDatabaseQueryManager = SIFUIHelpers::GetQueryManager();

		if (pDatabaseQueryManager)
		{
			// --enable / disable reset based on value
			bResetEnabled = pDatabaseQueryManager->IsTeamResettable(pCustomisationTeam->GetDbId());
		}
	}

#ifdef DISABLE_WOMENS
	UWWUIScrollBox* pScrollBox = GetTabDefaultScrollbox(CurrentTab);
	if (pScrollBox)
	{
		if (UWWUIListField* pListField = pScrollBox->GetListField((int32)ETeamDetailsOption::GENDER))
		{
			pListField->SetVisibility(ESlateVisibility::Collapsed);
		}
	}
#else
	bool bCanEditGender = true;
	
	if (bIsEditTeam)
	{
		bCanEditGender = SIFGameHelpers::GACustomiseCanEditTeamGender(pCustomisationTeam->GetDbId());
	}
	
	if (pCustomisationTeam)
	{
		// Nick WWS &s to Womens 13s //
		//if (!pCustomisationTeam->GetIsR7Exclusive())
		//{
			bCanEditGender = false;
		//}
	}

	if (!bCanEditGender)
	{
		UWWUIScrollBox* pScrollBox = GetTabDefaultScrollbox(CurrentTab);
		if (pScrollBox)
		{
			if (UWWUIListField* pListField = pScrollBox->GetListField((int32)ETeamDetailsOption::GENDER))
			{
				SetFieldOptionInteractability(pListField, false);
			}
		}
	}
#endif

	TeamDetailsUpdateLegend();

	if (pCustomisationTeam)
	{
		if (pCustomisationTeam->GetIsR7Exclusive())
		{
			UE_LOG(LogTemp, Display, TEXT("CustomiseTeamDetails.OnWindowEnter SEVEN"));
			SIFGameHelpers::GASetGameMode(GAME_MODE_RU13W); // Nick  WWS 7s to Womens // SEVENS);

			static IConsoleVariable* CVarShowWomenInCutscenes = IConsoleManager::Get().FindConsoleVariable(TEXT("ww.rugby.ShowWomenInCutscenes"));
			if (CVarShowWomenInCutscenes)
			{
				CVarShowWomenInCutscenes->Set(true);

				URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(GetWorld()->GetGameInstance<URugbyGameInstance>());
				if (gameInstance)
				{
					SIFGameWorld* pGameWorld = gameInstance->GetActiveGameWorld();
					if (pGameWorld)
					{
						pGameWorld->GetCutSceneManager()->FinishCurrentCutScene();
					}
				}
			}
		}
		else
		{
			UE_LOG(LogTemp, Display, TEXT("CustomiseTeamDetails.OnWindowEnter FIFTEEN"));
			SIFGameHelpers::GASetGameMode(GAME_MODE_RU13);
		}
	}
}

//===============================================================================
//===============================================================================


void UWWUIScreenTeamDetails::TeamDetailsSyncToTeam()
{
	RUDB_TEAM* pCustomisationTeam = GetCustomisationTeam();

	if (pCustomisationTeam)
	{
		UWWUIScrollBox* pDetailsScrollBox = GetTabDefaultScrollbox(ETeamDetailsTab::DETAILS);
		if (pDetailsScrollBox)
		{
			for (int32 i = 0; i < pDetailsScrollBox->GetListLength(); i++)
			{
				UWWUIListField* pListField = pDetailsScrollBox->GetListField(i);

				if (pListField)
				{
					FOptionProperties* pOptionProperty = OptionValueMap.Find(pListField);

					ETeamDetailsOption CurrentOption = (ETeamDetailsOption)i;

					switch (CurrentOption)
					{
					case ETeamDetailsOption::TEAM_EMBLEM:
					{
						if (pOptionProperty)
						{
							pCustomisationTeam->SetLogoID(pOptionProperty->GetCurrentOptionAsInt32());
						}
					}
						break;
					case ETeamDetailsOption::TEAM_NAME:
					{
						UWWUIEditableText* pTextBlock = Cast<UWWUIEditableText>(FindChildOfTemplateWidget(pListField, EDITABLE_TEXT_FIELD));
						if (pTextBlock)
						{
							pCustomisationTeam->SetName(TCHAR_TO_UTF8(*pTextBlock->GetText().ToString().ToUpper()));
						}
					}
						break;
					case ETeamDetailsOption::MNEMONIC:
					{
						UWWUIEditableText* pTextBlock = Cast<UWWUIEditableText>(FindChildOfTemplateWidget(pListField, EDITABLE_TEXT_FIELD));
						if (pTextBlock)
						{
							pCustomisationTeam->SetMnemonic(TCHAR_TO_UTF8(*pTextBlock->GetText().ToString().ToUpper()));
						}
					}
						break;
					case ETeamDetailsOption::COMMENTARY_NAME:
					{
						if (pOptionProperty)
						{
							pCustomisationTeam->SetCommentaryNameID(pOptionProperty->GetCurrentOptionAsInt32());
						}
					}
						break;
					case ETeamDetailsOption::LOCATION:
					{
						if (pOptionProperty)
						{
							pCustomisationTeam->SetAssociatedCountryId(pOptionProperty->GetCurrentOptionAsInt32());
						}
					}
						break;
					case ETeamDetailsOption::HOME_STADIUM:
					{
						if (pOptionProperty)
						{
							pCustomisationTeam->SetHomeStadiumId(pOptionProperty->GetCurrentOptionAsInt32());
						}
					}
						break;
					case ETeamDetailsOption::GENDER:
					{
						if (pOptionProperty)
						{
							pCustomisationTeam->SetGenderPermissionFlags((unsigned char)pOptionProperty->GetCurrentOptionAsInt32());
							pCustomisationTeam->RemoveInvalidSquadMembers();
						}
					}
						break;
					default:
						break;
					}
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================

bool UWWUIScreenTeamDetails::TeamDetailsValidate()
{
	RUDB_TEAM* pCustomisationTeam = GetCustomisationTeam();

	if (pCustomisationTeam)
	{
		if (FString(pCustomisationTeam->GetName()).IsEmpty())
		{
			LaunchInvalidTeamPopup("[ID_CUSTOMISE_TEAM_VALIDATE_NAME]");
			return false;
		}

		FString Mnemonic = UTF8_TO_TCHAR(pCustomisationTeam->GetMnemonic());
		if (Mnemonic.Len() != MNEMONIC_REQUIRED_LENGTH)
		{
			LaunchInvalidTeamPopup("[ID_CUSTOMISE_TEAM_VALIDATE_MNEMONIC]");
			return false;
		}
	}

	return true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::TeamDetailsSyncFromTeam()
{
	//when you enter the screen, make sure you have the default primary and alternate strip.	
	//StripOnSelectedOptionChanged((int)(EStripOption::PRIMARY_STRIP), 0);
	//StripOnSelectedOptionChanged((int)(EStripOption::ALTERNATE_STRIP), 0);
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::TeamDetailsPopulateOption(int32 OptionIndex, UWidget* pListField)
{
	RUDB_TEAM* team_data = GetCustomisationTeam();

	ETeamDetailsOption Option = (ETeamDetailsOption)OptionIndex;

	TArray<FValueIDBinding> Bindings;

	int32 BindingDefaultValue = 0;

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	RUDB_TEAM* pCustomisationTeam = GetCustomisationTeam();

	if (pCustomisationTeam)
	{
		if (pRugbyGameInstance)
		{
			// Count how many teams
			RUGameDatabaseManager* pDatabaseManager = pRugbyGameInstance->GetGameDatabaseManager();

			if (pDatabaseManager)
			{
				switch (Option)
				{
				case ETeamDetailsOption::TEAM_EMBLEM:
				{
					MabVector<RUDB_TEAM_LOGO> logo_list(SIFHEAP_DYNAMIC);

					pDatabaseManager->LoadAllData(logo_list);
					std::sort(logo_list.begin(), logo_list.end(), AlphabeticalLogoSortComparator);


					int32 LogoCount = 0;

					for (int i = 0; i < logo_list.size(); i++)
					{
						TArray<int32> ExcludedLogos = { 1080, 1084, 1137 };

						// Hack to remove custom team logo with no texture.
						if (ExcludedLogos.Find(logo_list[i].GetDbId()) >= 0)
						{
							continue;
						}

						if (logo_list[i].GetDbId() == pCustomisationTeam->GetLogoID())
						{
							BindingDefaultValue = LogoCount;
						}
						
						Bindings.Add(FValueIDBinding(logo_list[i].GetDbId(), FText::FromString(UWWUITranslationManager::Translate(UTF8_TO_TCHAR(logo_list[i].GetName())))));
						LogoCount++;
					}
				}
				break;
				case ETeamDetailsOption::TEAM_NAME:
				{
					UEditableText* pTextBlock = Cast<UEditableText>(FindChildOfTemplateWidget(pListField, EDITABLE_TEXT_FIELD));
					if (pTextBlock)
					{
						FString TeamName = UTF8_TO_TCHAR(pCustomisationTeam->GetName());
						FText NameText = FText::FromString(TeamName);
						FText UpperName = NameText.ToUpper();
						pTextBlock->SetText(UpperName);
					}
				}
				break;
				case ETeamDetailsOption::MNEMONIC:
				{
					UEditableText* pTextBlock = Cast<UEditableText>(FindChildOfTemplateWidget(pListField, EDITABLE_TEXT_FIELD));
					if (pTextBlock)
					{
						//avoid scenarios where base game team's mnemonic is profane.
						if(!pCustomisationTeam->IsCustom())
						{
							pTextBlock->UEditableText::SetText(FText::FromString(pCustomisationTeam->GetMnemonic()).ToUpper());
						}
						else
						{
							pTextBlock->SetText(FText::FromString(pCustomisationTeam->GetMnemonic()).ToUpper());
						}
					}
				}
				break;
				case ETeamDetailsOption::COMMENTARY_NAME:
				{
					//RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();
					MabVector<RUDB_TEAM_COMMENTARY_NAME> commentary_names;

					pDatabaseManager->LoadAllData(commentary_names);

					for (int i = 0; i < commentary_names.size(); i++)
					{
						if (commentary_names[i].GetDbId() == pCustomisationTeam->GetCommentaryNameID())
						{
							BindingDefaultValue = i;
						}

						Bindings.Add(FValueIDBinding(commentary_names[i].GetDbId(), FText::FromString(UWWUITranslationManager::Translate(commentary_names[i].GetName()))));
					}
				}
				break;
				case ETeamDetailsOption::LOCATION:
				{
					MabVector<RUDB_COUNTRY> countries;

					pDatabaseManager->LoadAllData(countries);

					for (int i = 0; i < countries.size(); i++)
					{
						if (countries[i].GetDbId() == pCustomisationTeam->GetAssociatedCountryId())
						{
							BindingDefaultValue = i;
						}

						Bindings.Add(FValueIDBinding(countries[i].GetDbId(), FText::FromString(UWWUITranslationManager::Translate(countries[i].GetName()))));
					}
				}
				break;
				case ETeamDetailsOption::HOME_STADIUM:
				{
					SIFGameWorld* pActiveGameWorld = pRugbyGameInstance->GetActiveGameWorld();

					if (pActiveGameWorld)
					{
						const RUStadiumManager* const pStadiumManager = pActiveGameWorld->GetStadiumManager();

						if (pStadiumManager)
						{
							MabVector< const RUDB_STADIUM* > tmp_vec(SIFHEAP_DYNAMIC);
							pStadiumManager->GetExportedStadiumList(tmp_vec);

							for (int i = 0; i < tmp_vec.size(); i++)
							{
								if (tmp_vec[i]->GetDbId() == pCustomisationTeam->GetHomeStadiumId())
								{
									BindingDefaultValue = i;
								}

								Bindings.Add(FValueIDBinding(tmp_vec[i]->GetDbId(), FText::FromString(UWWUITranslationManager::Translate(tmp_vec[i]->GetName()))));
							}
						}
					}
				}
				break;
				case ETeamDetailsOption::GENDER:
				{
					for (int i = 0; i < NUM_PLAYER_GENDER_OPTIONS; ++i)
					{
						unsigned char genderFlag = PLAYER_GENDER_AS_FLAG(i);

						if (genderFlag == pCustomisationTeam->GetGenderPermissionFlags())
						{
							BindingDefaultValue = i;
						}

						FString genderOptionText = FString("");
						switch (i)
						{
						case PLAYER_GENDER_MALE:
							genderOptionText = UWWUITranslationManager::Translate("[ID_CUSTOMISE_PLAYER_MALE]");
							break;
						case PLAYER_GENDER_FEMALE:
							genderOptionText = UWWUITranslationManager::Translate("[ID_CUSTOMISE_PLAYER_FEMALE]");
							break;
						}

						Bindings.Add(FValueIDBinding(genderFlag, FText::FromString(genderOptionText)));
					}
				}
				break;
				default:
					break;
				}
			}
		}
	}

	AddOptionBindings(Bindings, pListField, BindingDefaultValue);

	FOptionProperties* pSetOptionProperty = OptionValueMap.Find(pListField);

	if (pSetOptionProperty)
	{
		TeamDetailsOnSelectedOptionChanged(OptionIndex, pSetOptionProperty);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::TeamDetailsOnSelectedOptionChanged(int32 Option, FOptionProperties* pNewProperty)
{
	if (!pNewProperty)
	{
		return;
	}

	ETeamDetailsOption CurrentOption = (ETeamDetailsOption)Option;

	switch (CurrentOption)
	{
	case ETeamDetailsOption::TEAM_EMBLEM:
	{
		UImage* pTeamLogoImage = Cast<UImage>(FindChildWidget(WWUIScreenTeamDetails_UI::ImageLogo));
		int32 NewLogoDatabaseID = pNewProperty->GetCurrentOptionAsInt32();

		if (pTeamLogoImage)
		{
			FString LogoFileName = FString(SIFGameHelpers::GAGetLogoAssetPath(NewLogoDatabaseID).c_str());

			UTexture2D* pTeamLogoTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *LogoFileName));

			if (!pTeamLogoTexture)
			{
				pTeamLogoTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("/Game/Rugby/cmn_con/ui/GenericMissingTexture.GenericMissingTexture")));
			}

			if (pTeamLogoTexture)
			{
				pTeamLogoImage->SetBrushFromTexture(pTeamLogoTexture, true);
			}
		}

		UImage* pLogoImageBkgd = Cast<UImage>(FindChildWidget(WWUIScreenTeamDetails_UI::teamBkgd));
		SIFUIHelpers::SetImageColourFromLogoColour(pLogoImageBkgd, NewLogoDatabaseID);
	}
		break;
	case ETeamDetailsOption::HOME_STADIUM:
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		if (pRugbyGameInstance)
		{
			SIFGameWorld* pActiveGameWorld = pRugbyGameInstance->GetActiveGameWorld();

			if (pActiveGameWorld)
			{
				const RUStadiumManager* const pStadiumManager = pActiveGameWorld->GetStadiumManager();

				if (pStadiumManager)
				{
					int32 NewStadiumDatabaseID = pNewProperty->GetCurrentOptionAsInt32();

					const RUDB_STADIUM* pStadium = pStadiumManager->GetStadiumFromID(NewStadiumDatabaseID);

					UImage* pStadiumRenderImage = Cast<UImage>(FindChildWidget(WWUIScreenTeamDetails_UI::ImageStadium));

					if (pStadiumRenderImage)
					{
						FString path = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetStadiumRenderPath(pStadium->GetDbId()));

						UTexture2D* pStadiumTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *path));

						if (!pStadiumTexture)
						{
							pStadiumTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("/Game/Rugby/cmn_con/ui/GenericMissingTexture.GenericMissingTexture")));
						}
						
						if (pStadiumTexture)
						{
							pStadiumRenderImage->SetBrushFromTexture(pStadiumTexture, true);
						}
					}
				}
			}
		}
	}
		break;
	case ETeamDetailsOption::GENDER:
		{
			unsigned char genderPermissionFlag = (unsigned char)pNewProperty->GetCurrentOptionAsInt32();
			bool teamExcludesMen = (PLAYER_GENDER_FLAG_MALE != (PLAYER_GENDER_FLAG_MALE & genderPermissionFlag));

			bool didChange = false;

			static IConsoleVariable* CVarShowWomenInCutscenes = IConsoleManager::Get().FindConsoleVariable(TEXT("ww.rugby.ShowWomenInCutscenes"));
			if (CVarShowWomenInCutscenes)
			{
				int newValue = teamExcludesMen ? 1 : 0;
				didChange = CVarShowWomenInCutscenes->GetInt() != newValue;
				CVarShowWomenInCutscenes->Set(newValue);
			}

			if (didChange)
			{
				URugbyGameInstance* gameInstance = Cast<URugbyGameInstance>(GetWorld()->GetGameInstance<URugbyGameInstance>());
				if (gameInstance)
				{
					SIFGameWorld* pGameWorld = gameInstance->GetActiveGameWorld();
					if (pGameWorld)
					{
						pGameWorld->GetCutSceneManager()->FinishCurrentCutScene();
					}
				}
			}
		}
		break;
	default:
		break;

	}
}

void UWWUIScreenTeamDetails::TeamDetailsUpdateLegend()
{

	FString LegendString = "";

	// if enter text field is selected, show edit text instead of +/- 20 legends.
	if (TeamDetailsSelectedIndex == 1 || TeamDetailsSelectedIndex == 2)
	{
		LegendString = "[ID_CUSTOMISE_TEAM_DETAILS_TEXT_HELP]";

		if (bResetEnabled)
		{
			LegendString = "[ID_CUSTOMISE_TEAM_DETAILS_RESET_TEXT_HELP]";
		}
		else if (bIsDownload)
		{
			LegendString = "[ID_LIST_PLAYERS_VIEW_DETAILS_HELP]";
		}

		if (PC_KEYBOARD_FOCUS)
		{
			LegendString = "[ID_MATCH_SETTINGS_HELP]";
		}
	}
	else
	{
		LegendString = "[ID_CUSTOMISE_TEAM_ATTRIBUTES_HELP]";

		if (bResetEnabled)
		{
			LegendString = "[ID_CUSTOMISE_TEAM_DETAILS_RESET_TWENTY_HELP]";
		}
		else if (bIsDownload)
		{
			LegendString = "[ID_LIST_PLAYERS_VIEW_DETAILS_HELP]";
		}
	}

	SetLegendString(LegendString);
}

void UWWUIScreenTeamDetails::TeamDetailsUpdateSearchBoxCharacterLimit(int32 InScrollboxIndex)
{
	//< Update search box text length. >
	if (UWWUIEditableTextBox* pSearchTextBox = Cast<UWWUIEditableTextBox>(FindChildWidget(WWUIScreenTeamDetails_UI::EditableTextBoxSearchTextEntry)))
	{
		pSearchTextBox->SetMaxCharLength(InScrollboxIndex == (int32)ETeamDetailsOption::TEAM_NAME ? TEAM_DETAILS_MAX_TEXT_LENGTH : MNEMONIC_REQUIRED_LENGTH);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::StripOnWindowEnter()
{
	FString LegendString = "[ID_LIST_PLAYERS_VIEW_DETAILS_HELP]";

	if (!bIsDownload)
	{
		LegendString = "[ID_CUSTOMISE_TEAM_ATTRIBUTES_HELP]";
	}

	SetLegendString(LegendString);

	bInputEnabled = false;

	StripTimerHandleSetInputEnabled = UWWUIFunctionLibrary::OnTimer(SET_STRIP_TO_SETTINGS_TIMER_DURATION, FTimerDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::StripOnTimerSetInputEnabled));
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::StripPopulateOption(int32 OptionIndex, UWidget* pListField)
{
	RUDB_TEAM* team_data = GetCustomisationTeam();

	EStripOption Option = (EStripOption)OptionIndex;

	TArray<FValueIDBinding> Bindings;

	int32 BindingDefaultValue = 0;

	int32 DefaultIndex = 0;

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		// Count how many teams
		RUGameDatabaseManager* pDatabaseManager = pRugbyGameInstance->GetGameDatabaseManager();

		if (Option == EStripOption::PRIMARY_STRIP)
		{
			DefaultIndex = team_data->GetStripId(0);
		}
		else if (Option == EStripOption::ALTERNATE_STRIP)
		{
			DefaultIndex = team_data->GetStripId(1);
		}

		switch (Option)
		{
			case EStripOption::TYPE:
			{
				Bindings.Add(FValueIDBinding((int32)EStripCategory::ALL,FText::FromString(UWWUITranslationManager::Translate("[ID_FILTER_ALL]"))));
				Bindings.Add(FValueIDBinding((int32)EStripCategory::COMPETITION,FText::FromString(UWWUITranslationManager::Translate("[ID_COMP_SETUP_COMPETITION]"))));

				if (pDatabaseManager)
				{
					MabVector<RUDB_TEAM_STRIP> strip_list(0);

					pDatabaseManager->LoadAllData(strip_list);

					// Remove officials strips.
					StripFilterCustomList(strip_list);

					if (strip_list.size() > 0)
					{
						Bindings.Add(FValueIDBinding((int32)EStripCategory::CUSTOM, FText::FromString(UWWUITranslationManager::Translate("[ID_PP_FILTER_CUSTOM]"))));
					}
				}
			}
			break;

			case EStripOption::COMPETITION:
			{
				if (pDatabaseManager)
				{
					MabVector<RUDB_COMP_DEF> comp_list(0);

					pDatabaseManager->LoadAllData(comp_list);

					for (int i = 0; i < comp_list.size(); i++)
					{
						FString CompName = UTF8_TO_TCHAR(comp_list[i].GetName());

						Bindings.Add(FValueIDBinding(comp_list[i].GetDbId(), FText::FromString(UWWUITranslationManager::Translate(CompName)).ToUpper()));
					}
				}
			}
			break;

			case EStripOption::ALTERNATE_STRIP:
			// Fall through
			case EStripOption::PRIMARY_STRIP:
			{
				StripUpdateStripOptionLists();
			}
			break;

			default:
			break;
		}
	}

	AddOptionBindings(Bindings, pListField, BindingDefaultValue);
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::StripOnSelectedOptionChanged(int32 Option, int32 NewIndex)
{
	GEngine->ForceGarbageCollection(false);

	SIFGameHelpers::GAFaceRendererClearTextureCache();

	EStripOption CurrentOption = (EStripOption)Option;

	switch (CurrentOption)
	{
	case EStripOption::ALTERNATE_STRIP:
	{
		StripSetAfterTimer(EStripType::PRIMARY_STRIP);
	}
	break;
	case EStripOption::PRIMARY_STRIP:
	{
		StripSetAfterTimer(EStripType::ALTERNATE_STRIP);
	}
	break;
	default:
	break;
	case EStripOption::TYPE:
	{
		bool bCompFieldEnabled = (NewIndex == (int32)EStripCategory::COMPETITION) ? true : false;

		UWWUIScrollBox* pStripScrollBox = GetTabDefaultScrollbox(ETeamDetailsTab::STRIP);

		if (pStripScrollBox)
		{
			UWWUIListField* pCompListField = pStripScrollBox->GetListField((int32)EStripOption::COMPETITION);

			if (pCompListField)
			{
				pCompListField->SetIsEnabled(bCompFieldEnabled);
			}
		}

		StripUpdateStripOptionLists();

		StripSetAfterTimer(EStripType::PRIMARY_STRIP);
		StripSetAfterTimer(EStripType::ALTERNATE_STRIP);
	}
	break;
	case EStripOption::COMPETITION:
	{
		StripUpdateStripOptionLists();

		StripSetAfterTimer(EStripType::PRIMARY_STRIP);
		StripSetAfterTimer(EStripType::ALTERNATE_STRIP);
	}
	break;

	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::StripClearPopulatedLists()	//unused?
{
	if (bIsDownload)
	{
		StripResetCinematicStrips();
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamDetails::StripUpdateStripOptionLists()
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		RUGameDatabaseManager* pDatabaseManager = pRugbyGameInstance->GetGameDatabaseManager();

		if (pDatabaseManager)
		{
			EStripCategory StripCategory = (EStripCategory)StripGetCurrentStripOptionValue(EStripOption::TYPE);

			MabVector<RUDB_TEAM_STRIP> strip_list(0);

			if (StripCategory == EStripCategory::ALL)
			{
				pDatabaseManager->LoadAllData(strip_list);

				// Remove officials strips.
				for (MabVector<RUDB_TEAM_STRIP>::iterator it = strip_list.begin(); it != strip_list.end(); )
				{
					if (it->GetDbId() >= DB_REFEREE_STRIPS_BEGIN && it->GetDbId() <= DB_REFEREE_STRIPS_END)
					{
						it = strip_list.erase(it);
					}
					else
					{
						it++;
					}
				}
			}
			else if (StripCategory == EStripCategory::COMPETITION)
			{
				TArray<int32> CompTeams;
				TArray<int32> LoadedStrips;

				int32 CompetitionID = StripGetCurrentStripOptionValue(EStripOption::COMPETITION);

				MabVector<RUDB_COMP_DEF_TEAM> comp_team_list(0);

				pDatabaseManager->LoadAllData(comp_team_list);

				// Remove officials strips.
				for (MabVector<RUDB_COMP_DEF_TEAM>::iterator it = comp_team_list.begin(); it != comp_team_list.end(); )
				{
					if (it->GetCompetitionID() == CompetitionID)
					{
						CompTeams.AddUnique(it->team_id);
					}

					it++;
				}

				for (auto& CurrentTeamID : CompTeams)
				{
					RUDB_TEAM CurrentTeam;
					pDatabaseManager->LoadData(CurrentTeam, CurrentTeamID);

					for (int32 i = 0; i < RUDB_TEAM::MAX_STRIPS; i++)
					{
						int32 StripID = CurrentTeam.GetStripId(i);
						if (StripID != DB_INVALID_ID && !LoadedStrips.Contains(StripID))
						{
							RUDB_TEAM_STRIP NewStrip;
							pDatabaseManager->LoadData(NewStrip, StripID);
							strip_list.push_back(NewStrip);
						}
					}
				}
			}
			else if (StripCategory == EStripCategory::CUSTOM)
			{
				pDatabaseManager->LoadAllData(strip_list);

				StripFilterCustomList(strip_list);
			}

			// Sort the strips in alphabetical order.
			std::sort(strip_list.begin(), strip_list.end(), AlphabeticalStripSortComparator);

			RUDB_TEAM* team_data = GetCustomisationTeam();

			for (int32 i = (int32)EStripOption::PRIMARY_STRIP; i <= (int32)EStripOption::ALTERNATE_STRIP; i++)
			{
				EStripOption Option = (EStripOption)i;

				int32 DefaultIndex = 0;

				if (Option == EStripOption::PRIMARY_STRIP)
				{
					DefaultIndex = team_data->GetStripId(0);
				}
				else if (Option == EStripOption::ALTERNATE_STRIP)
				{
					DefaultIndex = team_data->GetStripId(1);
				}

				UWWUIListField* pListField = nullptr;

				// Load the list field.
				{
					UWWUIScrollBox* pStripScrollBox = GetTabDefaultScrollbox(ETeamDetailsTab::STRIP);

					if (pStripScrollBox)
					{
						pListField = pStripScrollBox->GetListField(i);
					}
				}

				TArray<FValueIDBinding> Bindings;
				int32 BindingDefaultValue = 0;

				for (int j = 0; j < strip_list.size(); j++)
				{
					FString StripName = UTF8_TO_TCHAR(strip_list[j].GetName());

					if (!strip_list[j].is_custom)
					{
						if (strip_list[j].GetIsHomeStrip())
						{
							StripName.RemoveFromEnd("Home");
							StripName += "[ID_MATCH_HOME]";
						}
						else
						{
							StripName.RemoveFromEnd("Away");
							StripName += "[ID_MATCH_AWAY]";
						}
					}

					if (strip_list[j].is_custom && SIFApplication::GetApplication()->IsNonPrimaryUserRestricted())
					{
						StripName = UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper();
					}

					Bindings.Add(FValueIDBinding(strip_list[j].GetDbId(), FText::FromString(UWWUITranslationManager::Translate(StripName)).ToUpper()));

					if (strip_list[j].GetDbId() == DefaultIndex)
					{
						BindingDefaultValue = j;
					}
				}

				OptionValueMap.Remove(pListField);

				AddOptionBindings(Bindings, pListField, BindingDefaultValue);

				WWOption::UpdateOption(this, pListField, OptionValueMap, 0.0f);
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::StripOnTimerSyncFromTeam()
{
	StripSyncToTeam();
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::StripOnTimerSetPrimaryStrip()
{
	SIFGameHelpers::GASetCustomTeamCinematicPrimaryStrip(StripGetPrimaryID());
	StripUpdateStripCreator(EStripType::PRIMARY_STRIP);
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::StripOnTimerSetAlternativeStrip()
{
	SIFGameHelpers::GASetCustomTeamCinematicAlternateStrip(StripGetAlternativeID());
	StripUpdateStripCreator(EStripType::ALTERNATE_STRIP);
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamDetails::StripUpdateStripCreator(EStripType Type)
{
	//Showing the username of the player that created the player you're editing
	UWidget* pDownloadedPlayerInfoWidget = FindChildWidget(WWUIScreenTeamDetails_UI::HorizontalBoxStripCreator);
	if (pDownloadedPlayerInfoWidget)
	{
		UTextBlock* pCreatedByText = Cast<UTextBlock>(FindChildWidget(Type == EStripType::PRIMARY_STRIP ? WWUIScreenTeamDetails_UI::TextHomeCreator : WWUIScreenTeamDetails_UI::TextAwayCreator));

		FString creatorName = "";
		FString uploaderName = "";

#ifdef SHOW_UGC_CREATOR
		int32 StripID = Type == EStripType::PRIMARY_STRIP ? StripGetPrimaryID() : StripGetAlternativeID();

		SIFGameHelpers::GAGetTeamStripCreatorUploader(StripID, creatorName, uploaderName);
#endif

		if (pCreatedByText)
		{
			FString CreatedByString;

			if (creatorName != "")
			{
				CreatedByString = "[ID_CREATED_BY]: " + creatorName + "\n[ID_UPLOADED_BY]: " + uploaderName;
			}

			SetWidgetText(pCreatedByText, FText::FromString(UWWUITranslationManager::Translate(CreatedByString)));
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::StripOnTimerSetInputEnabled()
{
	bInputEnabled = true;
}

//===============================================================================
//===============================================================================
void UWWUIScreenTeamDetails::StripFilterCustomList(MabVector<RUDB_TEAM_STRIP>& StripList)
{
	// Remove officials strips.
	for (MabVector<RUDB_TEAM_STRIP>::iterator it = StripList.begin(); it != StripList.end(); )
	{
		bool is_ref_strip = it->GetDbId() >= DB_REFEREE_STRIPS_BEGIN && it->GetDbId() <= DB_REFEREE_STRIPS_END;
		bool is_custom_strip = it->is_custom;
		if (!is_custom_strip || is_ref_strip)
		{
			it = StripList.erase(it);
		}
		else
		{
			it++;
		}
	}
}

//===============================================================================


void UWWUIScreenTeamDetails::StripSetAfterTimer(EStripType Type)
{
	if (Type == EStripType::PRIMARY_STRIP)
	{
		if (StripTimerHandleSetAlternativeStrip.IsValid())
		{
			UWWUIFunctionLibrary::StopTimer(StripTimerHandleSetAlternativeStrip);
		}

		StripTimerHandleSetAlternativeStrip = UWWUIFunctionLibrary::OnTimer(SET_STRIP_TO_SETTINGS_TIMER_DURATION, FTimerDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::StripOnTimerSetAlternativeStrip));
	}
	else
	{
		if (StripTimerHandleSetPrimaryStrip.IsValid())
		{
			UWWUIFunctionLibrary::StopTimer(StripTimerHandleSetPrimaryStrip);
		}

		StripTimerHandleSetPrimaryStrip = UWWUIFunctionLibrary::OnTimer(SET_STRIP_TO_SETTINGS_TIMER_DURATION, FTimerDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::StripOnTimerSetPrimaryStrip));
	}
}

//===============================================================================
//===============================================================================
int32 UWWUIScreenTeamDetails::StripGetCurrentStripOptionValue(EStripOption Option)
{
	UWWUIScrollBox* pStripScrollBox = GetTabDefaultScrollbox(ETeamDetailsTab::STRIP);

	if (pStripScrollBox)
	{
		UWWUIListField* pTypeListField = pStripScrollBox->GetListField((int32)Option);

		if (pTypeListField)
		{
			FOptionProperties* pOptionValue = OptionValueMap.Find(pTypeListField);

			if (pOptionValue)
			{
				return pOptionValue->GetCurrentOptionAsInt32();
			}
		}
	}

	// Default to -1
	return -1;
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::ResetAxisTrigger()
{
	if (m_triggerAxisHandle.IsValid())
	{
		UWWUIFunctionLibrary::StopTimer(m_triggerAxisHandle);
		m_canTriggerAxis = true;
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::StripSyncToTeam()
{
	RUDB_TEAM* pCustomiationTeam = GetCustomisationTeam();

	if (pCustomiationTeam && !bIsDownload)
	{
		int32 PrimaryStripID = StripGetPrimaryID();
		pCustomiationTeam->SetStripId(0, PrimaryStripID);

		int32 AlternateStripID = StripGetAlternativeID();
		pCustomiationTeam->SetStripId(1, AlternateStripID);

		SIFGameHelpers::GASetCustomTeamCinematicPrimaryStrip(PrimaryStripID);
		SIFGameHelpers::GASetCustomTeamCinematicAlternateStrip(AlternateStripID);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::StripResetCinematicStrips()	//unused?
{
	// --Reseting the cinematic strips should set the strips back to being the home team strips.
	RUDB_TEAM pCustomisationTeam = RUDB_TEAM(); // #rc3_legacy UIGetQueryManager() :LoadTeamData(IGGetTeamId(0))
	pCustomisationTeam.Reset();
	int32 StripID = pCustomisationTeam.GetStripId(0);
	SIFGameHelpers::GASetCustomTeamCinematicPrimaryStrip(StripID);
	SIFGameHelpers::GASetCustomTeamCinematicAlternateStrip(StripID);

	RUUIDatabaseQueryManager* pQueryManager = SIFUIHelpers::GetQueryManager();

	if (pQueryManager)
	{
		pQueryManager->ClearTeamData();
	}
}

//===============================================================================
//===============================================================================

int32 UWWUIScreenTeamDetails::StripGetPrimaryID()
{
	UWWUIScrollBox* pScrollBox = GetTabDefaultScrollbox(ETeamDetailsTab::STRIP);

	if (pScrollBox)
	{
		UWWUIListField* pListField = pScrollBox->GetListField((int32)EStripOption::PRIMARY_STRIP);

		FOptionProperties* pOptionProperty = OptionValueMap.Find(pListField);

		if (pOptionProperty)
		{
			return pOptionProperty->GetCurrentOptionAsInt32();
		}
	}

	ensure(false); // Strip ID not found.

	return -1;
}

//===============================================================================
//===============================================================================

int32 UWWUIScreenTeamDetails::StripGetAlternativeID()
{
	UWWUIScrollBox* pScrollBox = GetTabDefaultScrollbox(ETeamDetailsTab::STRIP);

	if (pScrollBox)
	{
		UWWUIListField* pListField = pScrollBox->GetListField((int32)EStripOption::ALTERNATE_STRIP);

		FOptionProperties* pOptionProperty = OptionValueMap.Find(pListField);

		if (pOptionProperty)
		{
			return pOptionProperty->GetCurrentOptionAsInt32();
		}
	}

	ensure(false); // Strip ID not found.

	return -1;
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::AttributesOnWindowEnter()
{
	FString LegendString = "[ID_LIST_PLAYERS_VIEW_DETAILS_HELP]";

	if (!bIsDownload)
	{
		LegendString = "[ID_CUSTOMISE_TEAM_ATTRIBUTES_HELP]";
	}

	SetLegendString(LegendString);

	// AttributesSyncFromTeam();
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::AttributePopulateOption(int32 OptionIndex, UWidget* pListField)
{
	RUDB_TEAM* pTeamData = GetCustomisationTeam();// UIGetQueryManager() :GetTeamData() #rc3_legacy

	EStripOption Option = (EStripOption)OptionIndex;

	TArray<FValueIDBinding> Bindings;

	int32 BindingDefaultValue = pTeamData->GetAttribute((TEAM_ATTRIBUTE)OptionIndex) / 100;

	Bindings.Add(FValueIDBinding(0, FText::FromString("Progress Bar")));

	AddOptionBindings(Bindings, pListField, BindingDefaultValue);
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::AttributesSyncToTeam()
{
	RUDB_TEAM* pCustomisationTeam = GetCustomisationTeam();

	UWWUIScrollBox* pScrollBox = GetTabDefaultScrollbox(CurrentTab);

	if (pScrollBox)
	{
		for (int i = 0; i < pScrollBox->GetListLength(); i++)
		{
			UWWUIListField* pListField = pScrollBox->GetListField(i);

			FOptionProperties* pOptionProperty = OptionValueMap.Find(pListField);

			if (pOptionProperty)
			{
				pCustomisationTeam->SetAttribute((TEAM_ATTRIBUTE)i, pOptionProperty->CurrentOptionValue * 100);
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::TacticsOnWindowEnter()
{
	// When change to the tactics tab, force it to scroll the title into view.
	TableOnSelectionChange(WWUIScreenTeamDetails_UI::ScrollBoxTactics, TACTICS_DEFENCE_TITLE_INDEX, TACTICS_FIRST_EDITABLE_LIST_INDEX);

	TacticsUpdateLegend();
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::TacticsSyncToTeam()
{
	UWWUIUserWidgetTacticsList* pTacticsList = Cast<UWWUIUserWidgetTacticsList>(FindChildWidget(WWUIScreenTeamDetails_UI::TacticsList));

	if (pTacticsList)
	{
		pTacticsList->SyncToTeam();
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::TacticsUpdateLegend()
{
	FString LegendString = bIsDownload ? "[ID_LIST_PLAYERS_VIEW_DETAILS_HELP]" : "[ID_CUSTOMISE_TEAM_ATTRIBUTES_HELP]";

	SetLegendString(LegendString);
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::SyncCurrentTabToTeam()
{
	switch (CurrentTab)
	{
	case ETeamDetailsTab::DETAILS:
	{
		TeamDetailsSyncToTeam();
	}
		break;
	case ETeamDetailsTab::STRIP:
	{
		StripSyncToTeam();
	}
	break;
	case ETeamDetailsTab::ATTRIBUTES:
	{
		AttributesSyncToTeam();
	}
	break;
	case ETeamDetailsTab::TACTICS:
	{
		TacticsSyncToTeam();
	}
		break;
	case ETeamDetailsTab::SQUAD:
		break;
	default:
		break;
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::LaunchInvalidTeamPopup(FString ContextString)
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();
		TArray<FModalButtonInfo> ButtonData;

		FWWUIModalDelegate ExitDelegate;
		ExitDelegate.BindLambda([this](APlayerController*) {m_canTriggerAxis = true; return true; });

		ButtonData.Add(FModalButtonInfo("[ID_POPUP_OK]", ExitDelegate));

		FString LegendString = "[ID_MAIN_MENU_HELP]";

		modalData->WarningDialogue = ContextString;
		modalData->LegendString = LegendString;
		modalData->ButtonData = ButtonData;

		modalData->CloseOnBackButton = true;
		modalData->CloseOnSelectButton = true;

		modalData->OnBackDelegate = ExitDelegate;

		pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
	}
}

//===============================================================================
//===============================================================================

bool UWWUIScreenTeamDetails::SaveOptionOnClick(APlayerController* OwningPlayer)
{
	// --validate (The strip validate 
	if (TeamDetailsValidate() /*and StripValidate()*/ && SquadValidate())
	{
		SIFUIHelpers::ShowSavingOverlay(true, true);

		AsyncTask(ENamedThreads::AnyNormalThreadNormalTask, [=]()
		{
			//-- trigger the save
			RUUIDatabaseQueryManager* pQueryManager = SIFUIHelpers::GetQueryManager();

			if (pQueryManager)
			{
				pQueryManager->SaveAndClearTeamData();
				pQueryManager->ClearTeamData();
			}
		});
	}

	return true;
}

//===============================================================================
//===============================================================================

bool UWWUIScreenTeamDetails::NoSaveOptionOnClick(APlayerController* OwningPlayer)
{
	RUUIDatabaseQueryManager* pQueryManager = SIFUIHelpers::GetQueryManager();

	if (pQueryManager)
	{
		pQueryManager->ClearTeamData();
	}

	SIFGameHelpers::GAFaceRendererClearTextureCache();

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (pRugbyGameInstance)
	{
		SIFGameHelpers::GASetGameMode(GAME_MODE::GAME_MODE_RU13);
		pRugbyGameInstance->DealMenuAction(SCREEN_CANCEL_FADE, GetScreenID());
	}

	Exit();

	return true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::Exit()
{
	SetInputEnabled(false);
	//Make sure you reset the strip, other wise it will get applied to main menu cutscene. RC4-3603
	// But only if we are not a downloaded team. RC4-3730

	if (!bIsDownload)
	{
		// This is now done when the SetUICutscene is called, checks if it is going back to the main menu and resets the strips.
		//SIFGameHelpers::GASetCustomTeamCinematicPrimaryStrip(SIFGameHelpers::GAGetTeamStripId(0));
		//SIFGameHelpers::GASetCustomTeamCinematicAlternateStrip(SIFGameHelpers::GAGetTeamStripId(0));
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenTeamDetails::OnCinematicCutsceneStarted()
{
	RUDB_TEAM* pCustomisationTeam = GetCustomisationTeam();

	if (pCustomisationTeam)
	{
		// --Here we set the strips for the cinematic teams.
		StripOnTimerSetPrimaryStrip();
		StripOnTimerSetAlternativeStrip();
	}

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		SIFGameWorld* pMenuGameWorld = pRugbyGameInstance->GetMenuGameWorld();

		if (pMenuGameWorld)
		{
			SSCutSceneManager* pCutsceneManager = pMenuGameWorld->GetCutSceneManager();

			if (pCutsceneManager)
			{
				pCutsceneManager->CinematicCutsceneStartedDelegate.Remove(CinematicCutsceneDelegateHandle);
			}
		}
	}
}

void UWWUIScreenTeamDetails::OnOpenTextEntry(APlayerController* OwningPlayer)
{
	if (CurrentTab != ETeamDetailsTab::DETAILS)
	{
		return;
	}

	if (UWWUIScrollBox* pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTeamDetails_UI::ScrollBoxTeamDetails)))
	{
		//< Only continue if selected correct field. >
		int32 SelectedIndex = pScrollBox->GetSelectedIndex();

		if (SelectedIndex == (int32)ETeamDetailsOption::TEAM_EMBLEM || SelectedIndex > (int32)ETeamDetailsOption::MNEMONIC)
		{
			return;
		}

		//< Stored the selected index. >
		TextEntryIsFor = (ETeamDetailsOption)SelectedIndex;

#if !PLATFORM_WINDOWS //< PS4, XBONE & SWITCH >
		UWWUIListField* pListField = pScrollBox->GetListField(SelectedIndex);
		if (pListField)
		{
			UWWUIEditableText* pEnterTextBox = Cast<UWWUIEditableText>(UWWUIFunctionLibrary::FindChildWidget(pListField, WWUIScreenTeamDetails_UI::EditableTextField));
			if (pEnterTextBox)
			{
				if (pEnterTextBox->EnterTextEditMode(OwningPlayer))
				{
					BindConsoleTextEntry();

					SIFUIHelpers::ListenToNoControllers();
				}
			}
		}
#else //< PC >
		UWidget* TextEntryBox = FindChildWidget(WWUIScreenTeamDetails_UI::BorderLinesSearchBox);

		if (TextEntryBox && !PC_KEYBOARD_FOCUS)
		{
			TextEntryBox->SetVisibility(ESlateVisibility::Visible);
			UWWUIEditableTextBox* pEditableTextBox = Cast<UWWUIEditableTextBox>(FindChildWidget(WWUIScreenTeamDetails_UI::EditableTextBoxSearchTextEntry));
			if (pEditableTextBox)
			{
				pEditableTextBox->EnterTextEditMode(OwningPlayer);

				//< Force text to upper. >
				UEditableText* pTextBlock = Cast<UEditableText>(FindChildOfTemplateWidget(pScrollBox->GetListField(SelectedIndex), EDITABLE_TEXT_FIELD));
				if (pTextBlock)
				{
					FText starterText = pTextBlock->GetText().ToUpper();

					if (starterText.IsEmpty() && TextEntryIsFor == ETeamDetailsOption::MNEMONIC)
					{
						UEditableText* pTempTextBlock = Cast<UEditableText>(FindChildOfTemplateWidget(pScrollBox->GetListField((int)ETeamDetailsOption::TEAM_NAME), EDITABLE_TEXT_FIELD));
						if (pTempTextBlock)
						{
							FString starterString = pTempTextBlock->GetText().ToString();
							if (starterString.Len() > 0)
							{
								if (starterString.Len() > MNEMONIC_REQUIRED_LENGTH)
								{
									starterString.LeftChop(starterString.Len() - MNEMONIC_REQUIRED_LENGTH - 1);
								}
							}
							starterText = FText::FromString(starterString).ToUpper();
						}
					}

					pEditableTextBox->SetText(starterText);
				}

				UTextBlock* pEnterTextLabel = Cast<UTextBlock>(FindChildWidget(WWUIScreenTeamDetails_UI::TextEntryLabel));
				if (pEnterTextLabel)
				{
					FString LabelText = TextEntryIsFor == ETeamDetailsOption::TEAM_NAME ? "[ID_CUSTOMISE_TEAM_NAME]" : "[ID_CUSTOMISE_TEAM_MNEMONIC]";
					pEnterTextLabel->SetText(FText::FromString(UWWUITranslationManager::Translate(LabelText).ToUpper()));
				}

				BindPCTextEntry();
				PC_KEYBOARD_FOCUS = true;
			}

			TeamDetailsUpdateLegend();
			SIFAudioHelpers::PlayInputSoundEvent("event:/ui/help_tips/open_popup");
		}
#endif
	}
}

bool UWWUIScreenTeamDetails::ReopenTextEntry(APlayerController* OwningPlayer)
{
	PC_KEYBOARD_FOCUS = false;

	OnOpenTextEntry(SIFApplication::GetApplication()->GetMasterPlayerController());
	return true;
}

bool UWWUIScreenTeamDetails::ExitTextEntry(APlayerController* OwningPlayer)
{
	PC_KEYBOARD_FOCUS = false;
	SIFAudioHelpers::PlayInputSoundEvent("event:/ui/help_tips/close_popup");
	SIFUIHelpers::ListenToAllControllers();

	//< Hide the text entry widget for PC. >
	if (UWidget* pTextEntryWidget = FindChildWidget(WWUIScreenTeamDetails_UI::BorderLinesSearchBox))
		pTextEntryWidget->SetVisibility(ESlateVisibility::Collapsed);

	//< Restore Focus. >
	if(UWWUIScrollBox* pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTeamDetails_UI::ScrollBoxTeamDetails)))
		pScrollBox->SetSelectedIndex((int32)TextEntryIsFor);

	TeamDetailsUpdateLegend();
	return true;
}

void UWWUIScreenTeamDetails::OnPCConfirmTextEntry(const FText& _Text, ETextCommit::Type _CommitMethod)
{
	if (_CommitMethod == ETextCommit::OnCleared)
	{
		//< Return out, allowing the screen to deal with this on key UP. >
		return;
	}

	UnbindPCTextEntry();
	ApplyEnteredText(_Text.ToString());
}

void UWWUIScreenTeamDetails::OnConsoleConfirmTextEntry(const FText& _Text, ETextCommit::Type _CommitMethod)
{
	if (_CommitMethod == ETextCommit::OnCleared)
	{
		//< Exit text entry. >
		UnbindConsoleTextEntry();
		ExitTextEntry(nullptr);
		return;
	}

	UnbindConsoleTextEntry();
	ApplyEnteredText(_Text.ToString());
}

void UWWUIScreenTeamDetails::ApplyEnteredText(FString _String)
{
	// --  validate the input
	MabString NewText = TCHAR_TO_UTF8(*_String);

	if (UWWUIScrollBox* pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTeamDetails_UI::ScrollBoxTeamDetails)))
	{
		// Offset by one for the team emblem.
		int32 ListFieldIndex = (int32)TextEntryIsFor;
		if (UWWUIEditableText* pTextBlock = Cast<UWWUIEditableText>(FindChildOfTemplateWidget(pScrollBox->GetListField(ListFieldIndex), EDITABLE_TEXT_FIELD)))
		{
			/// Fix name.
			UFont* EditableTextFont = const_cast<UFont*>(Cast<UFont>(pTextBlock->WidgetStyle.Font.FontObject));
			if (EditableTextFont) NewText = SIFGameHelpers::GAFixName2(NewText, EditableTextFont);

			if (TextEntryIsFor == ETeamDetailsOption::MNEMONIC)
			{
				// disabled special characters for mnemonic, they are too large to store with out system.
				FString NewString = UTF8_TO_TCHAR(NewText.c_str());
				NewString.RemoveSpacesInline();
				TArray<FString> CharsToRemove = { "?", "_", "-", "~", "/",  "'", "{",  "}", "[",  "]", "." };
				for (FString& character : CharsToRemove)
				{
					NewString = NewString.Replace(*character, *FString(""));
				}

				NewString = NewString.Left(3); //i think this Left(3) is redundant, the text is already cut off to the first 3 characters... - MG
				NewString = NewString.ToUpper();

				//all this code only executes if the all filters passed
				//so if somehow the special char filter failed, this should cull it as a last resort.
				//if this text contains special characters
				NewString = CullSpecialCharacters(NewString);

				//Due to console's virtual keyboard setting the mnemonic text right before this delegate does 
				//We need to preemptively blank it out in the event that our call to SetText fails (due to our filters)
				//as we could get unwanted characters being inputted/displayed.
#if (PLATFORM_PS4 || PLATFORM_XBOXONE || PLATFORM_SWITCH)
				pTextBlock->UEditableText::SetText(FText::FromString(""));
#endif

				pTextBlock->SetText(FText::FromString(NewString));
				
			}
			else
			{
				pTextBlock->SetText(FText::FromString(UTF8_TO_TCHAR(NewText.c_str())).ToUpper());
			}
		}
	}

	ExitTextEntry(nullptr);
}

void UWWUIScreenTeamDetails::HandleTextCommittedFilterResult(FString _String, EWWUITextFilterResult _FilterResult, ETextCommit::Type _CommitType)
{
	if (_CommitType == ETextCommit::Default) return;

	HandleTextFilterResult(_String, _FilterResult);
}

void UWWUIScreenTeamDetails::HandleTextFilterResult(FString _String, EWWUITextFilterResult _FilterResult)
{
	SIFUIHelpers::ListenToAllControllers();
	UnbindPCTextEntry();

	switch (_FilterResult)
	{
#if !PLATFORM_WINDOWS //< PS4, XBONE & SWITCH >
	case EWWUITextFilterResult::PROFANITY:	SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::OFFENSIVE_LANGUAGE,	FWWUIModalDelegate::CreateLambda([](APlayerController* OwningController) { SIFUIHelpers::ListenToNoControllers(); return true; }));	break;
	default:								SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::INVALID_NAME,			FWWUIModalDelegate::CreateLambda([](APlayerController* OwningController) { SIFUIHelpers::ListenToNoControllers(); return true; }));	break;
#else //< PC >
	case EWWUITextFilterResult::PROFANITY:	SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::OFFENSIVE_LANGUAGE,	FWWUIModalDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::ReopenTextEntry));	break;
	default:								SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::INVALID_NAME,			FWWUIModalDelegate::CreateUObject(this, &UWWUIScreenTeamDetails::ReopenTextEntry));	break;
#endif
	}
}

void UWWUIScreenTeamDetails::BindPCTextEntry()
{
#if PLATFORM_WINDOWS //< PC >
	if (UWWUIEditableTextBox* EditableTextBox = Cast<UWWUIEditableTextBox>(FindChildWidget(WWUIScreenTeamDetails_UI::EditableTextBoxSearchTextEntry)))
	{
		if (!EditableTextBox->OnTextCommitted.Contains(this, FName("OnPCConfirmTextEntry")))
		{
			EditableTextBox->OnTextCommitted.AddDynamic(this, &UWWUIScreenTeamDetails::OnPCConfirmTextEntry);
		}
		if (!EditableTextBox->GetTextCommittedFilterCallback().IsBound())
		{
			EditableTextBox->GetTextCommittedFilterCallback().BindUObject(this, &UWWUIScreenTeamDetails::HandleTextCommittedFilterResult);
		}
	}
#endif
}

void UWWUIScreenTeamDetails::UnbindPCTextEntry()
{
#if PLATFORM_WINDOWS //< PC >
	if (UWWUIEditableTextBox* EditableTextBox = Cast<UWWUIEditableTextBox>(FindChildWidget(WWUIScreenTeamDetails_UI::EditableTextBoxSearchTextEntry)))
	{
		if (EditableTextBox->OnTextCommitted.Contains(this, FName("OnPCConfirmTextEntry")))
		{
			EditableTextBox->OnTextCommitted.RemoveDynamic(this, &UWWUIScreenTeamDetails::OnPCConfirmTextEntry);
		}
		if (EditableTextBox->GetTextCommittedFilterCallback().IsBound())
		{
			EditableTextBox->GetTextCommittedFilterCallback().Unbind();
		}
	}
#endif
}

void UWWUIScreenTeamDetails::BindConsoleTextEntry()
{
#if !PLATFORM_WINDOWS //< XBOX, PS4, SWITCH >
	if (UWWUIScrollBox* pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTeamDetails_UI::ScrollBoxTeamDetails)))
	{
		if (UWWUIListField* pListField = pScrollBox->GetListField((int)TextEntryIsFor))
		{
			if (UWWUIEditableText* pEnterTextBox = Cast<UWWUIEditableText>(FindChildOfTemplateWidget(pListField, WWUIScreenTeamDetails_UI::EditableTextField)))
			{
				if (!pEnterTextBox->OnTextCommitted.Contains(this, FName("OnConsoleConfirmTextEntry")))
				{
					pEnterTextBox->OnTextCommitted.AddDynamic(this, &UWWUIScreenTeamDetails::OnConsoleConfirmTextEntry);
				}
				if (!pEnterTextBox->GetTextCommittedFilterCallback().IsBound())
				{
					pEnterTextBox->GetTextCommittedFilterCallback().BindUObject(this, &UWWUIScreenTeamDetails::HandleTextCommittedFilterResult);
				}
			}
		}
	}
#endif
}

void UWWUIScreenTeamDetails::UnbindConsoleTextEntry()
{
	//Controller disconencted while keyboard was open would change focusing causing text entry to unbind
	//this is to prevent it from unbinding so when our controller comes back the text input still works
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (pRugbyGameInstance)
	{
		if (pRugbyGameInstance->GetIsVirtualKeyboardShowing())
		{
			return;
		}
	}

#if !PLATFORM_WINDOWS //< XBOX, PS4, SWITCH >
	if (UWWUIScrollBox* pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTeamDetails_UI::ScrollBoxTeamDetails)))
	{
		if (UWWUIListField* pListField = pScrollBox->GetListField((int)TextEntryIsFor))
		{
			if (UWWUIEditableText* pEnterTextBox = Cast<UWWUIEditableText>(FindChildOfTemplateWidget(pListField, WWUIScreenTeamDetails_UI::EditableTextField)))
			{
				if (pEnterTextBox->OnTextCommitted.Contains(this, FName("OnConsoleConfirmTextEntry")))
				{
					pEnterTextBox->OnTextCommitted.RemoveDynamic(this, &UWWUIScreenTeamDetails::OnConsoleConfirmTextEntry);
				}
				if (pEnterTextBox->GetTextCommittedFilterCallback().IsBound())
				{
					pEnterTextBox->GetTextCommittedFilterCallback().Unbind();
				}
			}
		}
	}
#endif
}

FString UWWUIScreenTeamDetails::CullSpecialCharacters(const FString& text)
{
	//Loop through characters
	FString retVal = "";
	for (int i = 0; i < text.GetCharArray().Num() - 1; ++i) //loop through array excluding null terminator
	{
		const int asciValue = (int)text.GetCharArray()[i];

		//only append ascii characters
		if ((asciValue >= 0 && asciValue < 128))
		{
			retVal.AppendChar(text.GetCharArray()[i]);
		}
	}

	return retVal;
}

bool UWWUIScreenTeamDetails::OnResetTeamConfirm(APlayerController* pOwningPlayer)
{
	RUUIDatabaseQueryManager* pDBQueryManager = SIFUIHelpers::GetQueryManager();
	if (pDBQueryManager)
	{
		RUDB_TEAM* db_team = pDBQueryManager->GetTeamData();
		if (db_team)
		{
			// need to cache id like this as it gets cleared.
			int32 TeamID = db_team->GetDbId();
			SIFGameHelpers::GAResetEditedCustomData(RCDT_TEAM, TeamID);
			pDBQueryManager->ClearTeamData();
			pDBQueryManager->LoadTeamData(TeamID);
		}
	}

	// this breaks the saving overlay.
	//SIFApplication::GetApplication()->DealMenuAction(SCREEN_CANCEL, GetScreenID());
	//SIFApplication::GetApplication()->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::TeamDetails, pCachedInData);

	//SRA: Recalling Startup() is not good, this should be adjusted
	Startup(pCachedInData);
	OnInFocus();

	return true;
}

void UWWUIScreenTeamDetails::SetFieldOptionInteractability(UWidget* pWidget, bool bInteractable)
{
	ESlateVisibility NewVisibility = bInteractable ? ESlateVisibility::Visible : ESlateVisibility::HitTestInvisible;

	pWidget->SetVisibility(NewVisibility);

	UWidget* pSubtitleText = FindChildOfTemplateWidget(pWidget, "Subtitle");

	if (pSubtitleText)
	{
		pSubtitleText->SetIsEnabled(bInteractable);
		//pSubtitleText->SetColorAndOpacity(bInteractable ? FLinearColor::White : FLinearColor::Black);
	}

	UWWUIListField* pListField = Cast<UWWUIListField>(pWidget);
	if (pListField)
	{
		// This is what we need to skip items with UWWUIScrollBox
		pListField->GetButtonWidget()->SetIsEnabled(bInteractable);
	}
}

#if PLATFORM_WINDOWS && !UE_BUILD_SHIPPING

void UWWUIScreenTeamDetails::OnDebugSaveCSV(APlayerController* OwningController)
{
	TArray<FModalButtonInfo> ButtonData;
	FWWUIModalDelegate SaveDelegate;
	SaveDelegate.BindUObject(this, &UWWUIScreenTeamDetails::DebugSaveCSVConfirm);
	ButtonData.Add(FModalButtonInfo(FText::FromString(UWWUITranslationManager::Translate("YES")), SaveDelegate));

	ButtonData.Add(FModalButtonInfo(FText::FromString(UWWUITranslationManager::Translate("NO")), FWWUIModalDelegate()));

	SIFUIHelpers::LaunchWarningPopup("YOU HAVE PRESSED THE DEBUG KEY TO SAVE OUT A PLAYER TO CSV\nWOULD YOU LIKE TO CONTINUE?\nMAKE SURE YOU HAVE CHECKED OUT THE DATABASE FOLDER OF THE ART REPO!", "[ID_ASSIGN_CONTROLLER_NEUTRAL_HELP]", ButtonData);
}

//===============================================================================
//===============================================================================

bool UWWUIScreenTeamDetails::DebugSaveCSVConfirm(APlayerController* OwningController)
{
	// Save the data so that the database is up to date.
	//CustomisePlayerCommonSaveToDatabase();

	// Load in the db player and grab the data in CSV form.
	MabVector<MabString> ValueStringArray;
	RUDB_TEAM* pCustomisationTeam = GetCustomisationTeam();

	RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();

	if (!pCustomisationTeam)
	{
		return false;
	}

	RUDB_TEAM CustomisedTeam = *pCustomisationTeam;

	// Remove the custom flag from the team we are editing.
	CustomisedTeam.SetIsCustom(false);

	if (database_manager)
	{
		ValueStringArray = database_manager->GetDataCSV(CustomisedTeam, false, false);
	}

	// Setup the file name.
	const char* BaseArtPath = std::getenv("RUGBY_ART_DIR");
	MabString art_path("");

	if (BaseArtPath)
	{
		art_path = BaseArtPath;
	}

	if (art_path == "")
	{
		art_path = "W:/proj/rc4_art/";
	}

	// For each of the objects that have data to write out.
	for (int32 i = 0; i < ValueStringArray.size(); i++)
	{
		MabString FileName = MabString(0, "%sasset/cmn_con/database/Exported/Tables/rudb_team.csv", art_path.c_str());
		WWCSVHelpers::WriteCSVToFile(ValueStringArray[i], FileName);

	}

	if (pCustomisationTeam)
	{
		//TArray<RUDB_LINEUP> Lineup;

		//RL3DB_TEAM Team_R3DB(pCustomisationTeam->GetDbId());
		//int NumPlayers = Team_R3DB.GetNumPlayers();

		//for (int plr_idx = 0; plr_idx < NumPlayers; plr_idx++)
		//{
		//	RL3DB_CONTRACT Contract = Team_R3DB.GetPlayer(plr_idx);

		//	RUDB_LINEUP LineupPlayer((int)pCustomisationTeam->GetDbId(), (PLAYER_POSITION)Contract.position, Contract.index);
		//	LineupPlayer.value = Contract.value;
		//	LineupPlayer.num_seasons = Contract.num_seasons;

		//	MABASSERTMSG(Contract.position != 0, MabString(0, "Player %i has no position in the %s lineup database table", plr_idx, Team_R3DB.GetName()).c_str());

		//	Lineup.Add(LineupPlayer);
		//}

		MabString FileName = MabString(0, "%sasset/cmn_con/database/Exported/Tables/rudb_lineup_new.csv", art_path.c_str());

		MabString ValueString = "";
		char id_buffer[16];
		MabStringHelper::Sprintf(id_buffer, "%d", (int)pCustomisationTeam->GetDbId());
		ValueString.append(id_buffer);
		ValueString.append(",");

		for (int32 i = 0; i < 4; i++)
		{
			for (int32 j = 0; j < 70; j++)
			{
				char buffer[16];
				if (j < pCustomisationTeam->GetNumPlayers())
				{
					RUDB_LINEUP& CurrentLineupPlayer = pCustomisationTeam->GetLineup(j);

					switch (i)
					{
					case 0: // ID
					{
						MabStringHelper::Sprintf(buffer, "%d", CurrentLineupPlayer.player_id);
					}
					break;
					case 1: // Seasons
					{
						MabStringHelper::Sprintf(buffer, "%d", CurrentLineupPlayer.num_seasons);
					}
					break;
					case 2: // Position
					{
						MabStringHelper::Sprintf(buffer, "%d", CurrentLineupPlayer.position);
					}
					break;
					case 3: // Position
					{
						MabStringHelper::Sprintf(buffer, "%d", CurrentLineupPlayer.value);
					}
					break;
					default:
					{
						ensureMsgf(false, TEXT("Out of bounds of lineup writing array!"));
					}
					break;
					}
				}
				else
				{
					MabStringHelper::Sprintf(buffer, "%d", 0);
				}
				ValueString.append(buffer);
				ValueString.append(",");
			}
		}

		// Remove last ,
		ValueString.pop_back();
		WWCSVHelpers::WriteCSVToFile(ValueString, FileName);
	}

	return true;
}

#endif
//===============================================================================
//===============================================================================
void UWWUIScreenTeamDetails::HandleUGCUsernameCheckComplete(bool val)
{
	switch (CurrentTab)
	{
	case ETeamDetailsTab::DETAILS:
	{
		UpdateCreatorBox();
	}
	break;
	case ETeamDetailsTab::SQUAD:
	{
		pDraftPanel->OnWindowEnter();
	}
	break;
	default:
		break;
	}
	
}
