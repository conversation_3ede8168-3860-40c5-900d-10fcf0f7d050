/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/RUGameGetToBall.h"

#include "Mab/AdvMath/MabAdvMath.h"
#include "Match/AI/Actions/RUActionInterceptBall.h"
#include "Match/AI/Formations/SSEVDSFormationEnum.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/Roles/Competitors/RURoleGetTheBall.h"
#include "Match/Ball/SSBall.h"
#include "Match/Ball/SSBallExtrapolationNode.h"
#include "Match/Ball/SSBallExtrapolatorHelper.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Character/RugbyPlayerController.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSSpatialHelper.h"
#include "Utility/RURandomNumberGenerator.h"

#ifdef DEBUG_GTB_MANAGER
#include "Match/Debug/RUGameDebugSettings.h"
#endif

#include "RugbyGameInstance.h"

static const char* GTB_AREA = "GetTheBall";

GTB_INFO::GTB_INFO()
{
	player = NULL;
	index = -1;
	Reset();
}

void GTB_INFO::Reset()
{
	contact_node = NULL;
	ball_to_node_abs_time = 0.0f;
	animation_time = 0.0f;
	player_reaction_abs_time = 0.0f;
	player_reaction_time = 0.0f;
	player_to_node_time = 0.0f;
	ball_position_at_contact_time = FVector::ZeroVector;
	final_player_position = FVector::ZeroVector;
	contact_anim = INVALID_CONTACT_ANIM_NODE;
	success = false;
	updated_time = false;
	lead_opposition = false;
	is_supporting = false;
	rating = -1.0f;
	team_arrival_index = -1;
	global_arrival_index = -1;
	is_active = false;
}

#ifdef DEBUG_GTB_MANAGER
GTB_INFO::~GTB_INFO()
{
	player = NULL;
	contact_node = NULL;
	ball_position_at_contact_time = FVector( 0.0f, 0.0f, 0.0f );
	final_player_position = FVector( 0.0f, 0.0f, 0.0f );
	player_reaction_abs_time = 0.0f;
	player_reaction_time = 0.0f;
	ball_to_node_abs_time = 0.0f;
	animation_time = 0.0f;
	player_to_node_time = 0.0f;
	contact_anim = INVALID_CONTACT_ANIM_NODE;
	success = false;
	lead_opposition = false;
}
#endif

RUGameGetToBall::RUGameGetToBall( SIFGameWorld* ggame )
: game( ggame )
, gtb_info_pool()
, gtb_info_pool_by_arrival()
, active(false)
, initialised(false)
//,num_gtb_players()
,lowest_ball_contact_times()
, end_node( NULL )
, gtb_height_zones()
{
	memset(num_gtb_players,0,sizeof(num_gtb_players));

	MABASSERTMSG(game && game->GetEvents(),"we need events here");
	game->GetEvents()->player_deleted.Add( this, &RUGameGetToBall::RemovePlayer );
	for( int i = 0 ; i < 2; i++ )
	{
		lowest_ball_contact_times[i].reserve( NUM_PLAYERS_INIT );
	}
}

/// Setup the GTB info's
void RUGameGetToBall::Initialise()
{
	Reset();
	//int numPlayers = game->GetGameSettings().game_limits.GetNumberOfPlayers();

	const SIFRugbyCharacterList& players = game->GetPlayers();
	for( size_t i = 0; i < (size_t) /*numPlayers*/NUM_PLAYERS_INIT; ++ i )
	{
		gtb_info_pool[ i ].index  = (int) i;
		gtb_info_pool[ i ].player = NULL;
	}

	for( size_t i = 0; i < players.size(); ++i )
	{
		ARugbyCharacter* player = players[ i ];
		if ( player->GetAttributes()->GetTeam() != game->GetOfficialsTeam() )
			gtb_info_pool[ player->GetAttributes()->GetIndex() ].player = player;
	}

	gtb_info_pool_by_arrival = gtb_info_pool;
	static const int HEIGHT_ZONES_RESERVE = 20;
	gtb_height_zones.reserve( HEIGHT_ZONES_RESERVE );

	initialised = true;
}

RUGameGetToBall::~RUGameGetToBall()
{
	if (game && game->GetEvents())
	{
		game->GetEvents()->player_deleted.Remove( this, &RUGameGetToBall::RemovePlayer );
	}
}

const static int DEFAULT_GTB_ROLE_COUNT = 4;

void RUGameGetToBall::Enter( const MabTimeStep& delta_game_time )
{
	/// TYRONE : For teams with human players - we add extra GTB roles
	// to avoid the situation where they can't get the ball if they want to
	for( int i = 0 ; i < 2; i++ )
	{
		RUTeam* team = game->GetTeam( i );
		const BallFreeInfo& bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
		if ( bfi.event == BFE_PASS && bfi.event_success == true)
			if ( team == game->GetGameState()->GetDefendingTeam() )
				num_gtb_players[i] = 1;
			else
				num_gtb_players[i] = 0;
		else
			num_gtb_players[i] = DEFAULT_GTB_ROLE_COUNT + team->GetNumHumanPlayers();


		team->GetFormationManager()->ForceUpdateCurrentFormation();
		team->GetFormationManager()->OverrideAreaNumPlayers( GTB_AREA, (int)num_gtb_players[i], ERugbyFormationRole::GETTHEBALL );
	}

	/// Update the GTB height zones
	gtb_height_zones.clear();
	SSBallExtrapolatorHelper::GetBallHeightZones( gtb_height_zones, game->GetBall()->GetActiveExtrapolationNode(), GTB_HEIGHT_MAX, GTB_HEIGHT_MIN );

	/// Get the last node in the extrapolation
	end_node = game->GetBall()->GetActiveExtrapolationNode();
	while( end_node->next_node != NULL && end_node->next_node->active )
		end_node = end_node->next_node;

	/// Setup reactions times now the ball if free
	for( size_t i = 0; i < gtb_info_pool.size(); ++i )
	{
		GTB_INFO& info = gtb_info_pool[i];
		if ( info.player && info.player->GetVisible() )
		{
			info.player_reaction_time		= GetTimeToReactToFreeBall( info.player );
			info.player_reaction_abs_time	= info.player_reaction_time + delta_game_time.abs_time;
		}
	}

}

void RUGameGetToBall::Exit()
{
	Reset();
}

// Reset state of GTB
void RUGameGetToBall::Reset( bool /*stop_action*/ )
{
	for( size_t i = 0; i < gtb_info_pool.size(); ++i )
	{
		gtb_info_pool[i].Reset();
	}
	active = false;

	/// Send abort messages to any running get the ball roles
	SIFRugbyCharacterList gtb_players;
	if ( game->GetGameState()->GetAttackingTeam() )
		game->GetStrategyHelper()->GetTeamRoles( game->GetGameState()->GetAttackingTeam(), RURoleGetTheBall::RTTGetStaticType(), gtb_players );
	if ( game->GetGameState()->GetDefendingTeam() )
		game->GetStrategyHelper()->GetTeamRoles( game->GetGameState()->GetDefendingTeam(), RURoleGetTheBall::RTTGetStaticType(), gtb_players );

	for( SIFRugbyCharacterList::iterator it = gtb_players.begin(); it != gtb_players.end(); ++it )
	{
		ARugbyCharacter* player = *it;
		RURoleGetTheBall* gtb_role = player->GetRole< RURoleGetTheBall >();
		gtb_role->OnAbort();
	}

	/// Reset role overrides
	for( int i = 0 ; i < 2; i++ )
	{
		RUTeam* team = game->GetTeam( i );
		team->GetFormationManager()->OverrideAreaNumPlayers( GTB_AREA, -1, ERugbyFormationRole::GETTHEBALL );
		num_gtb_players[i] = 0;
		lowest_ball_contact_times[i].clear();
	}

	end_node = NULL;
}

void RUGameGetToBall::GameReset()
{
	Reset();
	for (int i=0; i!=(int)gtb_info_pool_by_arrival.size(); i++)
		gtb_info_pool_by_arrival[i].Reset();
	//initialised = false;
	gtb_height_zones.clear();
}

void RUGameGetToBall::printState()
{
#ifdef _DEBUG
	//game
	//gtb_info_pool
	//gtb_info_pool_by_arrival
	//end_node
	MABLOGDEBUG("GTB: %d %d %u %u %u %u %u",active,initialised,
		(unsigned int)num_gtb_players[0],
		(unsigned int)num_gtb_players[1],
		(unsigned int)lowest_ball_contact_times[0].size(),
		(unsigned int)lowest_ball_contact_times[1].size(),
		(unsigned int)gtb_height_zones.size());
#endif
}

bool SortGTBRating( const GTB_INFO& a, const GTB_INFO& b )
{
	if ( a.player && b.player )
		return a.rating > b.rating;

	if ( !a.player && !b.player )
		return a.index < b.index;

	if ( !a.player )
		return false;	/// b has a player so is higher precedence

	if ( !b.player )
		return true;	/// a has a player so is higher precedence

	MABBREAK();
	return false;
}

bool SortPlayerIndex( const GTB_INFO& a, const GTB_INFO& b )
{
	return a.index < b.index;
}

//*******************************
//
//	Updates which players are currently able to gtb the quickest
//
//*******************************

void RUGameGetToBall::Update( const MabTimeStep& delta_game_time )
{
	if (game == nullptr)
		return;

	if (game->GetGameState() == nullptr)
		return;

	if (game->GetStrategyHelper() == nullptr)
		return;

#ifdef DEBUG_GTB_MANAGER
	debug_key_pool->ReleaseAll();
#endif
	// Start actions for players with GetTheBall role, if we have a leading gtb info
	const BallFreeInfo& bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	if ( game->GetGameState()->GetBallHolder() == NULL && (game->GetStrategyHelper()->IsBallReallyFree() || bfi.event == BFE_PASS) )
	{
		if ( !active )
			Enter( delta_game_time );

		active = true;

		/// Iterate over all gtbs and update
		for( int i = 0; i < 2; i++ )
		{
			lowest_ball_contact_times[i].clear();
		}

		int non_zero_fitness_count = 0;
		float fitness;

		for( size_t i = 0; i < gtb_info_pool.size(); ++i )
		{
			GTB_INFO& info = gtb_info_pool[i];
			info.rating = (float) UNSELECTABLE_FITNESS;
			if ( info.player && info.player->GetVisible() )
			{
				fitness = GetFitness( info.player, delta_game_time );
				if ( !MabMath::Feq( fitness, 0.0f ) )
					non_zero_fitness_count++;
			}
		}

		// If it is a successful pass then we need to make sure that we can actually get the ball
		// Otherwise override GTB players to zero
		if ( bfi.event == BFE_PASS && bfi.event_success )
		{
			if ( non_zero_fitness_count > 0 )	/// If there are options to intercept the ball we apply some "choice" logic
				// to prevent too many intercepts happening
			{
				float slider_bal = game->GetGameSettings().game_settings.slider_intercept_frequency;
				// Nick WWS 7s to Womens 13s //
				//if (game->GetGameSettings().game_settings.GameModeIsR7())
				//	slider_bal *= 0.2f;

				if ( game->GetRNG()->RAND_RANGED_CALL(float, 1.0f ) >  slider_bal)
					non_zero_fitness_count = 0;
			}

			if ( non_zero_fitness_count == 0 )	// If there are no players to assign
			{
				RUTeam* intercepting_team = game->GetGameState()->GetDefendingTeam();

				/// And abort any gtb behaviours
				SIFRugbyCharacterList interceptors;
				game->GetStrategyHelper()->GetTeamRoles(intercepting_team, RURoleGetTheBall::RTTGetStaticType(), interceptors );
				for ( SIFRugbyCharacterList::iterator it = interceptors.begin(); it != interceptors.end(); ++it )
				{
					ARugbyCharacter* player = *it;
					RURoleGetTheBall* gtb_role = player->GetRole< RURoleGetTheBall >();
					gtb_role->OnAbort();
				}

				intercepting_team->GetFormationManager()->OverrideAreaNumPlayers( GTB_AREA, -1, ERugbyFormationRole::GETTHEBALL );
			}
		}

		// Sort the player GTBs by rating to get order arrival indices
		std::sort( gtb_info_pool.begin(), gtb_info_pool.end(), SortGTBRating );

		size_t global_index = 0;
		size_t team_a_index = 0;
		size_t team_b_index = 0;
		for( size_t i = 0; i < gtb_info_pool.size(); ++i )
		{
			GTB_INFO& info = gtb_info_pool[i];
			info.is_supporting = true;

			if ( !info.is_active )
			{
				info.team_arrival_index   = -1;
				info.global_arrival_index = -1;
				continue;
			}

			SSTEAMSIDE side = info.player->GetAttributes()->GetTeam()->GetSide();
			if ( side == SIDE_A )
				info.team_arrival_index = (int)team_a_index++;
			else if ( side == SIDE_B )
				info.team_arrival_index = (int)team_b_index++;

			info.global_arrival_index = (int)global_index++;
			info.is_supporting = info.team_arrival_index > 0;
		}

		gtb_info_pool_by_arrival = gtb_info_pool;

		/// Resort back by player index
		std::sort( gtb_info_pool.begin(), gtb_info_pool.end(), SortPlayerIndex );

		// TODO : Lead and opposition must be close to do miss - but can do this is in action instead

	} else if ( active ) {
		Exit();
	} else {
		/// Always clear out the GTB role assignments - occassionally they are still active
		/// I think GTB is being turned on sometimes when it should't be (like in the ruck)
		/// Reset role overrides
		for( int i = 0 ; i < 2; i++ )
		{
			RUTeam* team = game->GetTeam( i );
			team->GetFormationManager()->OverrideAreaNumPlayers( GTB_AREA, -1, ERugbyFormationRole::GETTHEBALL );
		}
	}
}

float RUGameGetToBall::GetTimeToReactToFreeBall( ARugbyCharacter* player ) const
{
	// If the ball is presently not free then we have not yet had time
	if ( game->GetGameState()->GetBallHolder() )
		return 0.0f;

	float loose_ball_reaction_time = player->GetAttributes()->GenerateLooseBallReactionTime();

	static const float FUMBLE_REACTION_TIME = 0.2f;
	static const float KICK_REACTION_TIME = 0.0f;
	static const float OPPONENT_KICK_REACTION_TIME = 1.0f;
	static const float PASS_REACTION_TIME = 0.0f;
	static const float LINEOUT_CATCH_MISS = 0.0f;

	static const float DIFFICULTY_MODIFIER[] = {
		1.0f,  //VERY EASY
		0.80f, //EASY
		0.60f, //MEDIUM
		0.40f, //HARD
		0.20f  //RPO
	};

	// Check to see if we have had time to react to it
	float time_to_react = 0.0f;
	const BallFreeInfo& last_ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();
	if ( ( last_ball_free_info.event == BFE_FUMBLE || last_ball_free_info.event == BFE_KNOCKON ) )
		time_to_react += FUMBLE_REACTION_TIME + loose_ball_reaction_time;

	else if (last_ball_free_info.event == BFE_KICK && !last_ball_free_info.was_kick_restart)
	{
		if ((last_ball_free_info.last_player && last_ball_free_info.last_player->GetAttributes()->GetTeam() == player->GetAttributes()->GetTeam()) || last_ball_free_info.game_phase_when_released == RUGamePhase::DROPOUT)
		{
			time_to_react += KICK_REACTION_TIME + loose_ball_reaction_time;
		}
		else
		{
			float opp_kick_reaction_modified = 1.0f;
			if (game && game->GetGameSettings().difficulty < DIF_MAX)
			{
				opp_kick_reaction_modified = (OPPONENT_KICK_REACTION_TIME * DIFFICULTY_MODIFIER[game->GetGameSettings().difficulty]);
			}
			time_to_react += opp_kick_reaction_modified + loose_ball_reaction_time;
		}
	}
	else if ( last_ball_free_info.event == BFE_PASS )
		time_to_react +=  PASS_REACTION_TIME + loose_ball_reaction_time;

	else if ( last_ball_free_info.event == BFE_LINEOUT_THROW )
		time_to_react += LINEOUT_CATCH_MISS + loose_ball_reaction_time;

	return time_to_react;
}

// Gets closest node in ball extrapolation where player can play an anim and catch the ball
const GTB_INFO* RUGameGetToBall::GetBallContact( ARugbyCharacter* player, float max_time_considered )
{
	ASSBall* ball = game->GetBall();
	SSBallExtrapolationNode* active_ball_node = ball->GetActiveExtrapolationNode();
	SSBallExtrapolationNode* root_ball_node   = ball->GetRootExtrapolationNode();
	SSSpatialHelper* spatial_helper = game->GetSpatialHelper();

	// Get the best extrapolation ball node and the reach time from player position
	SSBallExtrapolationNode* best_node = NULL;
	float lowest_ball_node_time = FLT_MAX;
	float best_player_reach_time = FLT_MAX;
	float best_ball_reach_time = 0.0f;

	// Get nodes that match height restrictions
	// Find the time player takes to reach the node and calc best time
	const static float BALL_REACH_THRESHHOLD = 1.0f;
	const static float BALL_REACH_THRESHHOLD_MOVING_BALL = 1.0f;
	float standard_reach_ball_threshhold = game->GetGameState()->GetPhase() == RUGamePhase::LINEOUT ? 0.0f : BALL_REACH_THRESHHOLD_MOVING_BALL;

	GTB_INFO* gtb_info = NULL;
	gtb_info = &gtb_info_pool[ player->GetAttributes()->GetIndex() ];
	float player_reaction_time = gtb_info->player_reaction_time.ToSeconds();

	// Get nodes that match height

	// If the ball is at the end of the extrapolation
	// else if ball is in motion get the ball node to which he can reach the earliest
	// else get the last ball node
	if ( active_ball_node && active_ball_node->next_node && !active_ball_node->next_node->active )
	{
		best_node = active_ball_node;
		best_player_reach_time = spatial_helper->GetApproxTimeToReachPoint( player, best_node->position, BALL_REACH_THRESHHOLD, FVector::ZeroVector, false );
		best_ball_reach_time = best_player_reach_time;
		// Always make sure that the ball reach time is at least a minimum value as most calculations
		const static float MIN_BALL_RECH_TIME_WHEN_STOPPED = 0.7f;
		MabMath::ClampLower( best_ball_reach_time, MIN_BALL_RECH_TIME_WHEN_STOPPED );
	}
	else
	{
		// Nodes that match an actions height
		const static float ON_THE_FULL_AHEAD_TIME = 0.0f;
		const static float NOT_ON_THE_FULL_AHEAD_TIME = 0.2f;
		const static float SUCCESSFUL_PASS_AHEAD_TIME = 0.2f;
		const BallFreeInfo& bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();

		float max_contact_anim_time = 0.0f;
		if ( bfi.event == BFE_PASS && bfi.event_success )
			max_contact_anim_time = SUCCESSFUL_PASS_AHEAD_TIME;
		else if ( game->GetBall()->IsOnTheFull() )
			max_contact_anim_time = ON_THE_FULL_AHEAD_TIME;
		else
			max_contact_anim_time = NOT_ON_THE_FULL_AHEAD_TIME;

		size_t i = 0;
		size_t n_height_zones = gtb_height_zones.size();
		float contact_time;
		while ( i < n_height_zones && !best_node )
		{
			SSBallExtrapolationNode* node	  = gtb_height_zones[i].start;
			SSBallExtrapolationNode* node_end = gtb_height_zones[i].end;
			MABASSERT( node && node_end );
			SSBallExtrapolationNode* past_end_node = node_end->next_node;

			/// If we have already passed this height zone then move to the next
			if ( active_ball_node->time > node_end->time )
			{
				++i;
				continue;
			}

			float last_ahead_delta = 0.0f;
			float player_reach_height = player->GetAttributes()->GetHeight() / 100.0f * GTB_PERSON_HEIGHT_TO_REACH_MULTIPLIER;

			while ( node != past_end_node && node->velocity != FVector::ZeroVector ) // check to see if one of the previous actions could attach sooner
			{
				// For a ball extrapolation node, get ball time and player's reach time to the node's position
				float ball_reach_time_from_current = node->time - active_ball_node->time;

				/// If we have passed the time that we would consider then
				if ( ball_reach_time_from_current > max_time_considered )
					break;

				// If we can't physically reach this node then skip it
				if ( node->position.y > player_reach_height )
				{
					node = node->next_node;
					continue;
				}

				float ball_reach_time_from_start  =  node->time - root_ball_node->time;
				float player_reach_time = spatial_helper->GetApproxTimeToReachPoint( player, node->position, standard_reach_ball_threshhold, FVector::ZeroVector, false );
				contact_time = player_reach_time + max_contact_anim_time;

				// Check for time for player to reach the node before ball gets there and
				// get the earliest node
				if ( contact_time <= ball_reach_time_from_current && ball_reach_time_from_start >= player_reaction_time && node->time < lowest_ball_node_time )
				{
					lowest_ball_node_time = node->time;
					best_node = node;
					best_player_reach_time = player_reach_time;
					best_ball_reach_time = ball_reach_time_from_current;
					break;
				}

				/// Optimisation - skip ahead an amount if the time delta between when the player will get there and the ball is high.
				last_ahead_delta = player_reach_time - ball_reach_time_from_current;
				const static float SKIP_AHEAD_MULT = 0.50f;
				float skip_ahead = last_ahead_delta * SKIP_AHEAD_MULT;
				const static float MIN_THRESHOLD = 0.2f;
				if ( last_ahead_delta > MIN_THRESHOLD )
				{
					float end_time = skip_ahead + node->time;
					while ( node != past_end_node &&  node->velocity != FVector::ZeroVector && node->time < end_time )
						node = node->next_node;
				} else
					/// Otherwise, test against the next node
					node = node->next_node;
			}

			++i;
		}
	}

	// If we couldn't find a node player could reach, find the last ball node and just pick it up
	if ( best_node == NULL )
	{
		best_node = end_node;
		best_player_reach_time = spatial_helper->GetApproxTimeToReachPoint( player, best_node->position, standard_reach_ball_threshhold, FVector::ZeroVector, false );
		best_ball_reach_time = best_node->time - active_ball_node->time;
	}

	if( best_node->position.y < GTB_HEIGHT_MIN || best_node->position.y > GTB_HEIGHT_MAX )
		best_node = NULL;

#ifdef DEBUG_GTB_MANAGER
	if(best_node != NULL )
	{
		//MABASSERTMSG(best_node->next_node->active == true, "If this breaks here we may get the freeball lockup/timeout assert due to highball not finding a valid height zone");
		MABASSERT( best_node && (best_ball_reach_time >= 0.0f) && (best_player_reach_time >= 0.0f) );
	}
#endif

	if(best_node != NULL)
	{
		gtb_info->contact_node = best_node;
		gtb_info->ball_position_at_contact_time = best_node->position;
		gtb_info->final_player_position = FVector(best_node->position.x, 0.0f, best_node->position.z);
		gtb_info->ball_to_node_abs_time = game->GetSimTime()->GetAbsoluteTime() + best_ball_reach_time;
		gtb_info->player_to_node_time = best_player_reach_time;
		gtb_info->success = true;

		/// Update the lowest time lists
		int team_idx = player->GetAttributes()->GetTeam()->GetIndex();
		MabVector<float>& lbct = lowest_ball_contact_times[team_idx];

		if ( num_gtb_players[team_idx] > 0 )
		{
			if ( lbct.size() < num_gtb_players[team_idx] )
			{
				lbct.push_back( best_ball_reach_time );
				std::sort( lbct.begin(), lbct.end() );
			}
			else if ( best_ball_reach_time < lbct.back() )
			{
				lbct.pop_back();
				lbct.push_back( best_ball_reach_time );
				std::sort( lbct.begin(), lbct.end() );
			}
		}
		return gtb_info;
	}

	return NULL;
}

float RUGameGetToBall::GetCachedFitness( const ARugbyCharacter* player )
{
	MABASSERT( player != NULL );

	if ( player != NULL )
	{
		GTB_INFO* gtb_info = &gtb_info_pool[ player->GetAttributes()->GetIndex() ];
		return gtb_info->rating;
	}
	return -1.0f;
}

// Get player fitness to get the ball
//		Code moved & modified from RURoleGetTheBall:GetFitness
float RUGameGetToBall::GetFitness( ARugbyCharacter* player, const MabTimeStep& delta_game_time )
{
#ifdef DEBUG_GTB_MANAGER
	//MABLOGMSG( LOGCHANNEL_ALWAYS, LOGTYPE_ALWAYS, "RUGameGetToBall::GetFitness --------- : %x, %d, %d, %4.4f ", (unsigned int)player, (gtb_action_player!=NULL), game->GetStrategyHelper()->IsBallReallyFree(), game->GetSimTime()->GetAbsoluteTime().ToSeconds() );
#endif

	RUStrategyHelper* strategy_helper = game->GetStrategyHelper();

	// Return is there is a ball holder
	if ( game->GetGameState()->GetBallHolder() != NULL )
		return 0;

	// Check the strategy
	bool should_get_ball, should_track_ball, must_get_ball;
	strategy_helper->IsGoodIdeaToStillGetBall( player, should_track_ball, should_get_ball, must_get_ball );
	if ( !should_track_ball )
		return 0;

	/// Run some additional additional checks for successful passes to not try and intercept if there is no chance of getting to the ball
	/// Before the pass completes
	const BallFreeInfo& bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	bool is_successful_pass = bfi.event == BFE_PASS && bfi.event_success;
	const RUActionInterceptBall::PASS_DETAILS& lpd = RUActionInterceptBall::GetLastPassDetails();
	float time_till_pass_receieve = 0.0f;

	/// Quick rejection on max speed
	if ( is_successful_pass )
	{
		time_till_pass_receieve = lpd.received_sim_time.ToSeconds() - delta_game_time.abs_time.ToSeconds();
		//MABASSERT( time_till_pass_receieve >= 0.0f );
		float t;
		float dist_to_seg = MabMath::Sqrt( MabAdvMath::SquaredDistanceFromPointToSegment( player->GetMovement()->GetCurrentPosition(), game->GetBall()->GetCurrentPosition(true), FVector( lpd.final_catch_point.x, 0.0f, lpd.final_catch_point.z), t ) );
		float best_time_to_seg = dist_to_seg / player->GetMovement()->GetMaxSpeed();
		if ( best_time_to_seg > time_till_pass_receieve )
			return 0;
	}

	// Get ball intercept info
	int team_idx = player->GetAttributes()->GetTeam()->GetIndex();
	float max_time_considered = 1e10f;
	if ( lowest_ball_contact_times[team_idx].size() >= num_gtb_players[team_idx]  && !lowest_ball_contact_times[team_idx].empty() )
		max_time_considered = lowest_ball_contact_times[team_idx].back();

	GTB_INFO* gtb_info = (GTB_INFO*) GetBallContact( player, max_time_considered );
	if ( gtb_info == NULL || gtb_info->contact_node == NULL )
		return 0;

	float ball_reach_time = ( gtb_info->ball_to_node_abs_time - game->GetSimTime()->GetAbsoluteTime() ).ToSeconds();
	/// Secondary rejections
	if ( is_successful_pass )
	{
		if ( ball_reach_time > time_till_pass_receieve )
			return 0;
		float player_to_node_time = gtb_info->player_to_node_time.ToSeconds();
		if ( player_to_node_time > ball_reach_time )
			return 0;
	}

	// Get rating based on intercept time
	float rating = 0.0f;
	float rate_time = 0.0f;
	{
		// Calc rate_time, give higher rating to earliest ball node and then player's runtime to that ball node
		// So we could differentiate from 2 players running to same ball node and 2 players running to different ball nodes
		float player_to_node_time = gtb_info->player_to_node_time.ToSeconds();
		const static float HUMAN_BIAS_MAX_TIME = 1.0f;
		const static float HUMAN_BIAS_MULTIPER = 0.0f;
		if ( gtb_info->player->GetHumanPlayer() != NULL && player_to_node_time < HUMAN_BIAS_MAX_TIME && player_to_node_time < ball_reach_time )
			player_to_node_time *= HUMAN_BIAS_MULTIPER;

		rate_time = ( ball_reach_time * 2.0f + player_to_node_time) / 3.0f;
		const float MAX_TIME_CONSIDERED = 10.0f;
		MabMath::ClampUpper( rate_time, MAX_TIME_CONSIDERED );

		// Give higher rating to, the smaller rate_time
		const float MAX_RATING = 80.0f;
		rating = MAX_RATING * ( 1.0f - (rate_time / MAX_TIME_CONSIDERED) );
	}

	/// Tacklees and tacklers are not candidates
	if ( player->GetActionManager()->IsActionRunning(ACTION_TACKLER) || player->GetActionManager()->IsActionRunning(ACTION_TACKLEE) )
		rating -= 20.0f;

	// Interceptors are not candidates
	if ( player->GetActionManager()->IsActionRunning( ACTION_INTERCEPT ) )
		rating -= 1000.0f;

	/// If the ball is free from a pass and is successful then we only want opposition players to be involved
	if ( is_successful_pass == true && player->GetAttributes()->GetTeam() == game->GetGameState()->GetAttackingTeam() )
		rating -= 1000.0f;

	// Neither are roles who say they cannot get the ball now
	SSRole* role = player->GetRole();
	if ( !role->CanGetTheBallNow() )
		rating -= 20.0f;

	gtb_info->rating = rating;

	// Debug
#ifdef DEBUG_GTB_MANAGER
	char buffer[1024];
	sprintf( buffer, "%x, %4.4f, %4.4f, %4.4f, %4.4f", (unsigned int)player, rating, rate_time, gtb_info->player_to_node_time.ToSeconds(), ball_reach_time );
	//MABLOGMSG( LOGCHANNEL_ALWAYS, LOGTYPE_ALWAYS, "RUGameGetToBall::GetFitness --------- : %s ", buffer );
#endif

	return rating;
}

GTB_INFO* RUGameGetToBall::ActivatePlayerGTB( ARugbyCharacter* player )
{
	if ( !active )
		return NULL;

	GTB_INFO* info = GetGTBInfo( player );
	info->is_active = true;
	GetBallContact( player );  // TYRONE: Pretty sure we don't need to update this again without any bad side effects.  Should speed up calculation
	return info;
}

GTB_INFO* RUGameGetToBall::GetGTBInfo( ARugbyCharacter* player )
{
	MABASSERT( player != NULL );
	return &gtb_info_pool[ player->GetAttributes()->GetIndex() ];
}

void RUGameGetToBall::RemovePlayer( ARugbyCharacter* /*player*/ )
{
	Initialise();
}

const GTB_INFO* RUGameGetToBall::GetPlayerByTeamArrival( RUTeam* team, int index ) const
{
	if ( team == NULL )
		return NULL;

	const RUGameGetToBall::GTBArray& arrival_order = GetGTBInfoByArrival();
	RUGameGetToBall::GTBArray::const_iterator it;

	for( it = arrival_order.begin(); it != arrival_order.end(); ++it )
	{
		const GTB_INFO& gtb_info = *it;
		if ( gtb_info.is_active && gtb_info.player && gtb_info.team_arrival_index == index && gtb_info.player->GetAttributes()->GetTeam() == team )
		{
			return &gtb_info;
		}
	}

	return NULL;
}
