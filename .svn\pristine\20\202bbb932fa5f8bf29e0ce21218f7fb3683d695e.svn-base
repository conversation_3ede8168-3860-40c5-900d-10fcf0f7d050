/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/Ball/SSBall.h"

#include "Match/SIFGameWorld.h"
#include "SSBallExtrapolationParameters.h"
#include "SSBallExtrapolator.h"
#include "Match/Ball/SSBallExtrapolationNode.h"
#include "Match/Ball/SSBallExtrapolatorHelper.h"
#include "Match/RugbyUnion/RUDatabaseTypes.h"
#include "Character/RugbyPlayerController.h"

#include "Match/Effects/SIFEffectSystem.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/SSSpatialHelper.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/SSMath.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/Debug/SIFDebug.h"
#include "Utility/RURandomNumberGenerator.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/RugbyUnion/RUGameGetToBall.h"
#include "Match/AI/Roles/Competitors/RURoleGetTheBall.h"
#include "Match/RugbyUnion/Rules/RURulesDebugSettings.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleLineOut.h"
#include "Utility/RUNetworkStateChecker.h"

#include "Match/SIFGameObject.h"
#include "Match/SIFGameContext.h"
#include "Match/SSGameTimer.h"

#include "RugbyGameInstance.h"
#include "Match/Cutscenes/SSCutSceneManager.h"

#include "Utility/Helpers/SIFGameHelpers.h"
#include "Materials/MaterialInstanceDynamic.h"

//#rc3_legacy_include #include "NMMabSkeleton.h"
//#rc3_legacy_include #include "pssgnode.h"
//#rc3_legacy_include #include "PSSGMabVectormath.h"
//#rc3_legacy_include #include "SIFPSSGShaderParameters.h"

#ifdef ENABLE_RU_TEST_BED
#include "Match/RugbyUnion/TestBed/RUTestBedManager.h"
#endif

#ifdef ENABLE_GAME_DEBUG_MENU
#include "Match/Debug/SIFDebug.h"
#include "Match/Debug/RUKickDebugSettings.h"
#include "Match/Debug/RUGameDebugSettings.h"
#endif

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
#include "Utility/consoleVars.h"
#include "DrawDebugHelpers.h"
#endif

//#afl_replay
#include "ReplayManager.h"
#include "RugbyGameInstance.h"


const int NUM_BONES_TO_TEST = 5;
const MabString BONE_POSITIONS[NUM_BONES_TO_TEST] = { "leftAnkle", "rightAnkle", "leftWrist", "rightWrist", "head" };

const char * EFFECT_BALL_BOUNCE = "ball_bounce";
const float EFFECT_BALL_BOUNCE_MIN_VELSQ = 9.0f;

#ifdef ENABLE_RU_TEST_BED
// Not const so the test bed can change them
	FVector ASSBall::rotation_long_punt(0.25f * -PI, 0, 0);
	FVector ASSBall::rotation_punt(0.5f * PI, 0, 0);
	FVector ASSBall::rotation_drop_kick(-0.5f * PI, 0, 0);
	FVector ASSBall::rotation_grubber(-0.5f * PI, 0, 0);
	FVector ASSBall::rotation_up_and_under(PI, 0, 0);
	FVector ASSBall::rotation_chip(PI, 0, 0);
	FVector ASSBall::rotation_place_kick(0.7f * PI, 0, 0);
	FVector ASSBall::rotation_box_kick(PI, 0, 0);
	FVector ASSBall::spin_long_punt(0, 1.0f, 0);
	FVector ASSBall::spin_punt(-1.0f, 0, 0);
	FVector ASSBall::spin_drop_kick(1.0f, 0, 0);
	FVector ASSBall::spin_grubber(1.0f, 0, 0);
	FVector ASSBall::spin_chip(0.2f, 0, 0);
	FVector ASSBall::spin_up_and_under(0.2f, 0, 0);
	FVector ASSBall::spin_place_kick(1.0f, 0, 0);
	FVector ASSBall::spin_box_kick(0.2f, 0, 0);
#else
	const FVector ASSBall::rotation_long_punt(0.25f * -PI, 0, 0);
	const FVector ASSBall::rotation_punt(0.5f * PI, 0, 0);
	const FVector ASSBall::rotation_drop_kick(-0.5f * PI, 0, 0);
	const FVector ASSBall::rotation_grubber(-0.5f * PI, 0, 0);
	const FVector ASSBall::rotation_up_and_under(PI, 0, 0);
	const FVector ASSBall::rotation_chip(PI, 0, 0);
	const FVector ASSBall::rotation_place_kick(0.7f * PI, 0, 0);
	const FVector ASSBall::rotation_box_kick(PI, 0, 0);
	const FVector ASSBall::spin_long_punt(0, 1.0f, 0);
	const FVector ASSBall::spin_punt(-1.0f, 0, 0);
	const FVector ASSBall::spin_drop_kick(1.0f, 0, 0);
	const FVector ASSBall::spin_grubber(1.0f, 0, 0);
	const FVector ASSBall::spin_chip(0.2f, 0, 0);
	const FVector ASSBall::spin_up_and_under(0.2f, 0, 0);
	const FVector ASSBall::spin_place_kick(1.0f, 0, 0);
	const FVector ASSBall::spin_box_kick(0.2f, 0, 0);
#endif

static const float PICKUP_ATTACH_TIME_TRANS = 0.13f;
static const float PICKUP_ATTACH_TIME_ROT   = 0.40f;

///-------------------------------------------------------------------------------
/// SSBall: Constructor
///-------------------------------------------------------------------------------

ASSBall::ASSBall( /*SIFGameWorld* ggame, ARugbyBall* ballObject*/)
: /*game( ggame ),
  ball_object(ballObject),*/
  ball_holder( NULL ),
  root_node( NULL ),
  num_extrapolation_elements( 0 ),
  last_ball_position( FVector::ZeroVector ),
  last_ball_velocity( FVector::ZeroVector ),
  scale_multiplier( 0.0f ),
  active_node( NULL ),
  time_through_path( 0.0f ),
  percent_through_node( 0.0f ),
  attach_time( 0.0f ),
  attach_time_rot( 0.0f ),
  absolute_move_set( false ),
  absolute_rotate_set( false ),
  absolute_move_previous( FVector::ZeroVector ),
  absolute_rotate_previous( MabQuaternion::IDENTITY ),
  visible( false ),
  is_using_travel_line( false ),
  travel_start( FVector::ZeroVector ),
  travel_end( FVector::ZeroVector ),
  travel_current_pos( FVector::ZeroVector ),
  travel_adjustment( FVector::ZeroVector ),
  travel_rotation_speed( FVector::ZeroVector ),
  travel_distance( 0.0f ),
  travel_amount_through( 0.0f ),
  travel_time( 0.0f ),
  travel_gravity( 0.0f ),
  travel_max_height( 0.0f ),
  travel_line_wobble_angle( 0.0f ),
  travel_line_end( NULL ),
  unique_set_id( 0 ),
  ball_not_in_play( false ),
  dead_or_out_position( FVector::ZeroVector ),
  ball_over_tryline( false),
  ball_over_tryline_position( FVector::ZeroVector ),
  try_scored(false),
  ball_over_sideline( false ),
  ball_over_sideline_position( FVector::ZeroVector ),
  replay_store(),
  extrapolation_parameters(),
  //oval_extrapolator(ggame),
  extrapolation_buffer( NULL ),
  temporary_buffer( NULL ),
  saved_active_node( NULL ),
  saved_time_through_path( 0.0f ),
  saved_percent_through_node( 0.0f ),
  saved_travel_adjustment( FVector::ZeroVector ),
  pre_kick_rotation( MabQuaternion::IDENTITY ),
  num_post_strikes_since_airborne(0),
  consequence_in_progress( false ),
  last_in_play_bounce_position( FVector::ZeroVector )
{
	PrimaryActorTick.bCanEverTick = false;
	BallMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("Ball Mesh"));
	if (BallMesh)
	{
		RootComponent = BallMesh;
	}

	bReplicates = false;
	bReplicateMovement = false;

	//MABASSERT( game != NULL );

	absolute_rotate_previous = MabQuaternion(0, 0, 0, 1);

	absolute_move_set	= true;
	absolute_rotate_set = true;
	root_node			= NULL;
	active_node			= NULL;

	scale_multiplier = 1.0f;

	travel_adjustment = FVector( 0, 0, 0 );
	unique_set_id = 0;
	visible = true;

	extrapolation_buffer = ( SSBallExtrapolationNode* ) oval_extrapolator.CreateExtrapolationBuffer( BALL_EXTRAPOLATION_SIZE, MabMemGetDefaultObjectHeap(ggame) );
	temporary_buffer	 = ( SSBallExtrapolationNode* ) oval_extrapolator.CreateExtrapolationBuffer( BALL_EXTRAPOLATION_SIZE, MabMemGetDefaultObjectHeap(ggame) );

	this->UseExtrapolationStorage( extrapolation_buffer, BALL_EXTRAPOLATION_SIZE );
	this->SetPosition(FVector::ZeroVector);
}

///-------------------------------------------------------------------------------
/// SSBall: Destructor
///-------------------------------------------------------------------------------

ASSBall::~ASSBall()
{
	MabMemArrayDelete(extrapolation_buffer);
	MabMemArrayDelete(temporary_buffer);

	/*if(ball_object)
		ball_object->Destroy();*/
}

void ASSBall::Init(SIFGameWorld* ggame)
{
	game = ggame;
	oval_extrapolator.SetGameWorld(ggame);
}

void ASSBall::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	FinaliseReplay();

	Super::EndPlay(EndPlayReason);
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

bool ASSBall::WillBallGoOverPostsOnTheFull( FVector& position_over_line, BallThroughPostQueryResult &final_result_type )
{
	SSBallExtrapolationNode* current_node = root_node;
	SSBallExtrapolationNode* previous_node = root_node;
	if ( !root_node ) return false;

	bool through_posts = false;
	final_result_type = BALL_POST_NONE;

	while ( current_node && current_node->active && current_node->num_bounces == 0 && !through_posts )
	{
		BallThroughPostQueryResult current_result_type;

		through_posts = IsCrossingOverCrossbarBetweenPosts( previous_node->position, current_node->position, current_result_type );
		if ( through_posts )
		{
			final_result_type = current_result_type;
			position_over_line = current_node->position;
		}
		else if(current_result_type == BALL_POST_UNDER
			|| current_result_type == BALL_POST_WIDE)
		{
			//store the position it crosses the line for any misses.
			final_result_type = current_result_type;
			position_over_line = current_node->position;
		}

		previous_node = current_node;
		current_node = current_node->next_node;
	}

	if(final_result_type == BALL_POST_NONE)
	{
		//the ball must have fallen short, unless it started beyond the posts already, in which
		//case it will be long, but that will never happen.
		final_result_type = BALL_POST_SHORT;

		//set the final resting position of the ball,
		if(previous_node)
		{
			position_over_line = previous_node->position;
		}
	}

	return through_posts;
}

bool ASSBall::IsCrossingOverCrossbarBetweenPosts( const FVector& last_position, const FVector& position, BallThroughPostQueryResult &result_out) const
{
	result_out = BALL_POST_NONE;

	// If ball is not through posts, and last position was less than try line, current position
	// is greater than try line and height is above the cross bar,
	FieldExtents extents = game->GetSpatialHelper()->GetFieldExtents();
	FieldExtents try_line_extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	extents.x /= 2.0f;
	extents.y /= 2.0f;
	try_line_extents.y /= 2.0f;

	if ( MabMath::Fabs(last_position.z) > try_line_extents.y)
	{
		result_out = BALL_POST_LONG;
		return false;
	}

	if (MabMath::Fabs(position.z) < try_line_extents.y)
	{
		result_out = BALL_POST_SHORT;
		return false;
	}

	float z = 0.0f;
	if ( position.z > try_line_extents.y )
	{
		z = try_line_extents.y;
	}
	else if ( position.z < -try_line_extents.y )
	{
		z = -try_line_extents.y;
	}
	else
	{
		result_out = BALL_POST_SHORT;
		return false;
	}

	const float goal_post_half_width = game->GetSpatialHelper()->GetPostHalfWidth();
	const float goal_crossbar_height = game->GetSpatialHelper()->GetCrossBarHeight();
	MABASSERT( MabMath::Fabs(position.z - last_position.z) > 0.001f );
	float t = (z - last_position.z) / (position.z - last_position.z);
	float x = (position.x - last_position.x) * t + last_position.x;

	if(MabMath::Fabs( x ) >= goal_post_half_width)
	{
		result_out = BALL_POST_WIDE;
		return false;
	}

	if(position.y <= goal_crossbar_height)
	{
		result_out = BALL_POST_UNDER;
		return false;
	}

	result_out = BALL_POST_OVER;
	return true;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::SetBallAttachTime( float _attach_time_trans, float _attach_time_rot )
{
	wwNETWORK_TRACE_JG("ASSBall::SetBallAttachTime _attach_time_trans: %f, _attach_time_rot: %f", _attach_time_trans, _attach_time_rot);
	attach_time			= _attach_time_trans;
	attach_time_rot		= _attach_time_rot;
}

void ASSBall::SetHolder(ARugbyCharacter* new_ball_holder )
{
	UE_LOG(LogTemp, Display, TEXT("ASSBall::SetHolder: %s"), new_ball_holder ? *new_ball_holder->GetName() : TEXT("NULL"));

#if PLATFORM_WINDOWS && !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
	MABLOGDEBUG( "ASSBall::SetHolder: %d, %s", new_ball_holder ? new_ball_holder->GetAttributes()->GetIndex() : -1, new_ball_holder ? new_ball_holder->GetAttributes()->GetDisplayName() : "NULL" );

	if (new_ball_holder)
	{
		wwNETWORK_TRACE_JG("ASSBall::SetHolder %s", (*new_ball_holder->GetName()));

		last_in_play_bounce_position = FVector::ZeroVector;
	}
#endif

	// Stop the motion of the ball if previous holder was NULL
	if( ball_holder == NULL && new_ball_holder != NULL )
		StopAllMotion();

	// if new holder is NULL set world transform to last inherited transform
	if ( ball_holder != NULL && new_ball_holder == NULL )
	{
		static const float HEIGHT_THRESHOLD = 0.4f;	// If the ball is higher than this, it will be floating.
		const MabMatrix& ball_transform = GetMabTransform();
		FVector gfx_position = ball_transform.GetTranslation();

		MABASSERTMSG( active_node->next_node == NULL || !active_node->next_node->active, "TYRONE trying to fix ball hanging in air - ball should now drop instead of staying in fixed position" );
		if ( active_node->next_node && !active_node->next_node->active && gfx_position.y < HEIGHT_THRESHOLD )
		{
			MabQuaternion rotation;
			rotation.FromMatrix( ball_transform );

			FVector velocity = gfx_position - active_node->position;
			RawExtrapolation( active_node->position, velocity, rotation, FVector::ONE, CalculateWindDisplacement( velocity ) );
		}
		else
		{
			// If the ball is floating, force it to the ground.
			PropelBall( 0.0f, 0.0f, false, false );
		}
	}

	ball_holder = new_ball_holder;

	// so that it does not wait/hang in air for a frame if a player is warped
	if ( ball_holder != NULL )
	{
		const BallFreeInfo& bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
		if(bfi.sub_type != KICKTYPE_NONE || bfi.event == BFE_PASS || bfi.event == BFE_FUMBLE )
		{
			game->GetEvents()->change_to_camera(GAME_CAM_INVALID);
		}
	}
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::SetBallScaleMultiplier( float _scale_multiplier )
{
	scale_multiplier = _scale_multiplier;
}

// this is just to help debug the ball being invisible at the wrong times
void ASSBall::SetActorHiddenInGame(bool bNewHidden)
{
	Super::SetActorHiddenInGame(bNewHidden);
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::Reset()
{
	SetPositionAbsolute( FVector( 0, 0, 0 ) );
	SetRotationAbsolute( MabQuaternion::IDENTITY );
	ResetTryCheck();
	is_using_travel_line = false;
	travel_adjustment = FVector( 0, 0, 0 );
	ball_not_in_play = false;
	consequence_in_progress = false;
	saved_active_node = NULL;
	saved_time_through_path = 0.0f;
	saved_percent_through_node = 0.0f;
	saved_travel_adjustment = FVector::ZeroVector;
}
void ASSBall::GameReset()
{
	ball_holder = NULL;
	Reset();
}
void ASSBall::printState()
{
#ifdef _DEBUG
	//game
	//ball_object
	//root_node
	//active_node
	//absolute_move_previous
	//absolute_rotate_previous
	MABLOGDEBUG("BALL: holder=%p x=%d pos(%f,%f,%f) vel(%f,%f,%f) scale=%f time=%f pc=%f attach=%f/%f set=%d/%d vis=%d",
		ball_holder,num_extrapolation_elements,
		last_ball_position.x,last_ball_position.y,last_ball_position.z,
		last_ball_velocity.x,last_ball_velocity.y,last_ball_velocity.z,
		scale_multiplier,time_through_path,percent_through_node,attach_time,attach_time_rot,
		absolute_move_set,absolute_rotate_set,visible);
	//travel_start
	//travel_end
	//travel_current_pos
	//travel_adjustment
	//travel_rotation_speed
	//travel_line_end
	//dead_or_out_position
	//ball_over_tryline_position
	//ball_over_sideline_position
	//replay_store
	MABLOGDEBUG("line=%d %f %f %f %f %f %f id=%d %d %d %d %d",
		is_using_travel_line,travel_distance,travel_amount_through,travel_time,travel_gravity,
		travel_max_height,travel_line_wobble_angle,unique_set_id,ball_not_in_play,ball_over_tryline,
		try_scored,ball_over_sideline);
	//extrapolation_parameters
	//oval_extrapolator
	//extrapolation_buffer
	//temporary_buffer
	//saved_active_node
	//saved_travel_adjustment
	MABLOGDEBUG("%f %f %d %d",saved_time_through_path,saved_percent_through_node,
		num_post_strikes_since_airborne,consequence_in_progress);
#endif
}
///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::OnBounce( int num_bounces )
{
	MABUNUSED(num_bounces);
	game->GetEvents()->ball_bounce( GetCurrentPosition(), GetVelocity() );

	if (!ball_over_sideline)
	{
		// if ball is in play, keep its last bounce position, we use this for 40/20 - 20/40 bounce checks
		last_in_play_bounce_position = GetCurrentPosition();
	}

	//If ball is fast, make an effect
	//if(GetVelocity().SquaredMagnitude() > EFFECT_BALL_BOUNCE_MIN_VELSQ)
	//	CreateEffect( GetCurrentPosition(), EFFECT_BALL_BOUNCE );
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::OnUpdate()
{
	const float BUFFER_TO_CHECK = 2.0f;
	FieldExtents extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();

	if ( MabMath::Fabs(active_node->position.z) > (extents.y * 0.5f) - BUFFER_TO_CHECK )
	{
		float post_z = extents.y * 0.5f;
		if ( active_node->position.z < 0.0f)
		{
			post_z = -post_z;
		}

		// Check for post collision
		if (CheckPostCollision(active_node, post_z))
		{
			MABLOGDEBUG("game->GetLogic()->BallHitsPosts();");

			num_post_strikes_since_airborne ++;

			ContinueExtrapolation(active_node);
			game->GetEvents()->ball_hits_posts(active_node->position);
			game->GetGameGTB()->Reset();
		}
	}
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::SetPosition( const FVector &new_position )
{
	MABASSERTMSG(!new_position.ContainsNaN(), "SSBall: ball set with non finite position");

	// clear motion tree
	active_node->ClearChildren();
	SSBallExtrapolationNode* current_node;

	if ( active_node->active )
	{
		current_node = active_node->next_node;
		active_node->CopyTo( current_node );
	}
	else
	{
		current_node = active_node;
	}

	// build a new tree
	current_node->position = new_position;
	current_node->num_bounces = 0;
	current_node->time = 0;
	current_node->active = true;
	current_node->velocity = FVector::ZeroVector;
	current_node->rotation_speed = FVector::ZeroVector;
	current_node->rotation = MabQuaternion::IDENTITY;
	current_node->out_on_full = false;
	current_node->dead_ball = false;
	travel_adjustment = FVector( 0.0f, 0.0f, 0.0f );
	++unique_set_id;

	consequence_in_progress = false;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::NotifyExtrapolationChange()
{
	active_node				= root_node;
	time_through_path		= 0.0f;
	percent_through_node	= 0.0f;

	travel_adjustment = FVector( 0, 0, 0 );
	++unique_set_id;

	wwNETWORK_TRACE_JG("ASSBall::NotifyExtrapolationChange %f", time_through_path);
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------
void ASSBall::SetTravelLine( const FVector& start, const FVector& end, const MabTime& t, bool continue_extrap )
{
	MABASSERTMSG(!start.ContainsNaN() && !end.ContainsNaN(), "SSBall: ball travel line set with non finite position");

	// Store parameters for replay system...

	replay_store.is_pass = true;
	replay_store.start = start;
	replay_store.end = end;
	replay_store.time = t;

	// force the ball to travel from start to end in time t
	travel_amount_through = 0;
	travel_start	= start;
	travel_end		= end;
	travel_time		= t.ToSeconds();
	travel_distance = ( travel_end - travel_start ).Magnitude();
	travel_adjustment = FVector( 0, 0, 0 );

	is_using_travel_line = true;
	ExtrapolateTravelLine( continue_extrap );

	++unique_set_id;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::MoveTravelLine( float delta_x, float delta_z )
{
	if ( root_node->next_node->active )
	{
		travel_adjustment.x += delta_x;
		travel_adjustment.z += delta_z;
	}
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

bool ASSBall::UpdateTravelLine( float delta_time )
{
	travel_amount_through += ( travel_distance / travel_time ) * delta_time;

	float percentage = ( travel_amount_through / travel_distance );

	if ( percentage >= 1.0f )
	{
		// if we have exceeded the travel
		is_using_travel_line = false;
		travel_amount_through = travel_distance;
		travel_current_pos = travel_end;
	}
	else
	{

		// if we have moved along the line
		FVector delta_travel = ( travel_end - travel_start );
		FVector new_pos = delta_travel * percentage + travel_start;

		if ( percentage < 0.5f )
		{
			// on the way up
			float t = ( 0.5f - percentage ) * travel_time;
			float displacement = ( travel_gravity / 2.0f ) * ( t * t );
			new_pos.y += travel_max_height - displacement;
		}
		else
		{
			// on the way down
			float t = ( percentage - 0.5f ) * travel_time;
			float displacement = ( travel_gravity / 2.0f ) * ( t * t );
			new_pos.y += travel_max_height - displacement;
		}

		travel_current_pos = new_pos;
	}

	return true;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

float ASSBall::GetRemainingTravelLineTime()
{
	return travel_time - ( active_node->time + extrapolation_parameters.time_interval );
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::CalculateTravelHeightData( float t, float required_height )
{
	travel_max_height	= required_height;
	travel_gravity		= ( required_height / ( (t/2.0f) * (t/2.0f) ) ) * 2;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::SetTravelRotationSpeed( const FVector& speed, float wobble_angle )
{
	travel_rotation_speed = speed;
	travel_line_wobble_angle = wobble_angle;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

int ASSBall::ExtrapolateTravelLine( bool continue_extrap )
{
	FVector last_position;
	SSBallExtrapolationNode *current_node;

	MabQuaternion current_rotation = active_node->rotation;

	time_through_path = 0.0f;
	// and make a new one

	current_node = root_node;
	int total_nodes_created = 1;

	current_node->num_bounces		= 0;
	current_node->position			= travel_start;
	current_node->time				= 0;
	current_node->rotation			= current_rotation;
	current_node->rotation_speed	= FVector( 0, 0, 0 ); // TODO:
	current_node->velocity			= FVector( 0, 0, 0 );
	current_node->active			= true;

	// update the travel line
	UpdateTravelLine( extrapolation_parameters.time_interval );

	last_position = travel_current_pos;

	do
	{
		// update the travel line
		UpdateTravelLine( extrapolation_parameters.time_interval );

		// add another node
		SSBallExtrapolationNode* new_node = current_node->next_node;
		total_nodes_created++;

		// update rotation
		//FVector new_rotation_radians = current_rotation + travel_rotation_speed * ( current_node->time + SSBallHelper::GetExtrapolationParameters().time_interval );
		// find out how much the ball will rotate in this interval
		float dt = extrapolation_parameters.time_interval;
		MabQuaternion new_rotation = SSBallExtrapolatorHelper::ApplyAngularRotation( current_rotation, travel_rotation_speed, dt, SSBallExtrapolatorHelper::FBS_PRECONCAT );

		// apply the optional 'wobble' on to the extrapolation
		float wobble_angle = travel_line_wobble_angle;
		MabQuaternion wobble_quat;
		MabQuaternion rotation_to_set_on_ball = new_rotation;
		wobble_quat.FromEuler( 0.0f, wobble_angle, 0.0f );
		rotation_to_set_on_ball = wobble_quat * rotation_to_set_on_ball; // preconcatenate the rotation

		// set the details of motion onto the node
		new_node->num_bounces		= 0;
		new_node->position			= travel_current_pos;
		new_node->rotation			= rotation_to_set_on_ball;
		new_node->rotation_speed	= /*FVector(0, 0, 0); // TODO */travel_rotation_speed;
		new_node->time				= ( total_nodes_created - 1 ) * extrapolation_parameters.time_interval;
		new_node->velocity			= ( travel_current_pos - last_position ) * ( 1.0f / extrapolation_parameters.time_interval );
		new_node->active			= true;

		if ( total_nodes_created == BALL_EXTRAPOLATION_SIZE ) break;

		// next
		last_position = travel_current_pos;
		current_rotation = new_rotation;
		current_node = new_node;
	}
	while( is_using_travel_line );

	travel_line_end = current_node;

	if ( active_node->next_node ) {
		active_node->velocity = active_node->next_node->velocity;
	}
	if( continue_extrap )
	{
		if ( current_node->previous_node ) {
			current_node->velocity = current_node->previous_node->velocity;
		}
		//current_node->active = false;

		ContinueExtrapolation( current_node );

		if( current_node->next_node ){
			current_node->next_node->active = true;
			current_node->velocity = current_node->previous_node->velocity;
		}
		if ( active_node->next_node ) {
			active_node->velocity = active_node->next_node->velocity;
		}
		current_node->active = true;
	}
	else if ( current_node->next_node )
		current_node->next_node->active = false;

	NotifyExtrapolationChange();

	wwNETWORK_TRACE_JG("ASSBall::ExtrapolateTravelLine %f", time_through_path);

	return total_nodes_created;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

#define COMFORT_ZONE_CENTRE (1.4f)

float ASSBall::GetBestPickupPosition( ARugbyCharacter* player, FVector &position, FVector hand_offset_distance, bool &player_will_get_there_first)
{
	MABUNUSED(position);
	MABASSERT( player != NULL );
	MABASSERT( game != NULL );
	SSSpatialHelper* spatial_helper = game->GetSpatialHelper();
	MABASSERT( spatial_helper != NULL );

	SSBallExtrapolationNode *current_node = active_node;
	SSBallExtrapolationNode *best_node = NULL;
	float best_node_time = 100000.0f;
	int best_num_bounces = 100000;
	float best_comfort_distance = 5.0f;

	// Get player details
	FVector hand_offset;
	MabMatrix::MatrixMultiply(hand_offset, hand_offset_distance, MabMatrix::RotMatrixY(player->GetMovement()->GetCurrentMotionAngle()));

	while ( current_node != NULL && current_node->active )
	{
		//Is the ball going up for the first time?
		if (current_node->num_bounces != 0 || current_node->velocity.y <= 0)
		{
			// Is the ball too high to catch at this point?
			if (current_node->position.y <= MAX_RECEIVE_HEIGHT)
			{
				// get time it's going to take the ball to get here
				float time_for_ball = current_node->time - active_node->time + SIMULATION_INTERVAL;
				// get the time it's going to take the player to get here
				float time_for_player;
				FVector actual_position = current_node->position - hand_offset;
				time_for_player = spatial_helper->GetApproxTimeToReachPoint(player, actual_position);
				float ball_speed = current_node->velocity.Magnitude();

				// Player needs to get there before the ball if it is in motion
				if (ball_speed <= 0.1f || time_for_player <= time_for_ball + 1.5f)
				{
					bool new_best = false;

					// fewer bounces: automatically better
					if (current_node->num_bounces < best_num_bounces)
					{
						new_best = true;
					}
					// same number of bounces but more comfortable: better
					else if (current_node->num_bounces == best_num_bounces)
					{
						if (MabMath::Fabs(current_node->position.y - COMFORT_ZONE_CENTRE) < best_comfort_distance)
							new_best = true;
					}

					if (new_best)
					{
						// We've found a new best time
						best_num_bounces = current_node->num_bounces;
						best_node_time = time_for_ball + time_for_player;
						best_comfort_distance = MabMath::Fabs(current_node->position.y - COMFORT_ZONE_CENTRE);

						best_node = current_node;
						MABASSERT(best_node->time >= 0);
					}
				}
			}
		}
		current_node = current_node->next_node;
	}

	if ( best_node == NULL )
	{
		return -1;
	}

	float ball_time = best_node->time - active_node->time;
	FVector actual_position = best_node->position - hand_offset;

	position = actual_position;

	float player_time = spatial_helper->GetApproxTimeToReachPoint( player, actual_position );

	if ( player_time < ball_time )
	{
		player_will_get_there_first = true;
		MABASSERT( player_time>=0 );
	}
	else
	{
		player_will_get_there_first = false;
		MABASSERT( ball_time>=0 );
	}
	return ball_time;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::SetVisibleMab( bool visible_ )
{
	visible = visible_;
	SetVisible(visible);
}

///-------------------------------------------------------------------------------
/// Get the kick strength details for a magnitude kick
///-------------------------------------------------------------------------------
void ASSBall::GetKickStrengthDetails( const KickType p_KickType, const float kick_distance, bool account_for_wind, float& p_kick_strength_pct, float& wind_amount )
{
	float kick_scale = MAX_LONG_PUNT_DIST - MIN_LONG_PUNT_DIST;
	float kick_min = MIN_LONG_PUNT_DIST;
	wind_amount = 0.0f;

	switch (p_KickType)
	{
	case KICKTYPE_PENALTYPUNT:
	case KICKTYPE_LONGPUNT:
	case KICKTYPE_FREEKICK:
	case KICKTYPE_FOURTYTWENTYKICK:
	case KICKTYPE_TWENTYFOURTYKICK:
		kick_scale = MAX_LONG_PUNT_DIST - MIN_LONG_PUNT_DIST;
		kick_min = MIN_LONG_PUNT_DIST;
		wind_amount = 0.0f;
		break;
	case KICKTYPE_UPANDUNDER:
		kick_scale = MAX_UP_AND_UNDER_DIST - MIN_UP_AND_UNDER_DIST;
		kick_min = MIN_UP_AND_UNDER_DIST;
		wind_amount = 1.0f;
		break;
	case KICKTYPE_BOXKICK:
		kick_scale = MAX_BOX_KICK_DIST - MIN_BOX_KICK_DIST;
		kick_min = MIN_BOX_KICK_DIST;
		wind_amount = 1.0f;
		break;
	case KICKTYPE_CHIPKICK:
		kick_scale = MAX_CHIP_KICK_DIST - MIN_CHIP_KICK_DIST;
		kick_min = MIN_CHIP_KICK_DIST;
		break;
	case KICKTYPE_SETPLAYCHIPKICK:
		kick_scale = MAX_SETPLAY_CHIP_KICK_DIST - MIN_SETPLAY_CHIP_KICK_DIST;
		kick_min = MIN_SETPLAY_CHIP_KICK_DIST;
		break;
	case KICKTYPE_GRUBBERKICK:
		kick_scale = MAX_GRUBBER_KICK_DIST - MIN_GRUBBER_KICK_DIST;
		kick_min = MIN_GRUBBER_KICK_DIST;
		break;
	case KICKTYPE_DROPGOAL:
		kick_scale = MAX_DROP_GOAL_DIST - MIN_DROP_GOAL_DIST;
		kick_min = MIN_DROP_GOAL_DIST;
		wind_amount = 0.0f;
		break;
	case KICKTYPE_KICKOFF:
		kick_scale = MAX_KICKOFF_KICK_DIST - MIN_KICKOFF_KICK_DIST;
		kick_min = MIN_KICKOFF_KICK_DIST;
		wind_amount = 0.0f;
		break;
	case KICKTYPE_DROPKICK:
		kick_scale = MAX_DROP_KICK_DIST - MIN_DROP_KICK_DIST;
		kick_min = MIN_DROP_KICK_DIST;
		wind_amount = 0.0f;
		break;
	case KICKTYPE_PLACEKICK:
		kick_scale = MAX_PLACE_KICK_DIST - MIN_PLACE_KICK_DIST;
		kick_min = MIN_PLACE_KICK_DIST;
		wind_amount = 0.0f;
		break;
	default:
		kick_scale = MAX_LONG_PUNT_DIST - MIN_LONG_PUNT_DIST;
		kick_min = MIN_LONG_PUNT_DIST;
		break;
	}

#ifdef ENABLE_GAME_DEBUG_MENU
	RUKickDebugSettings* debug_settings = SIFDebug::GetKickDebugSettings();
	if ( debug_settings->ShouldOverrideKickValues() )
	{
		switch (p_KickType)
		{
		case KICKTYPE_PENALTYPUNT:
		case KICKTYPE_LONGPUNT:
		case KICKTYPE_FREEKICK:
		case KICKTYPE_FOURTYTWENTYKICK:
		case KICKTYPE_TWENTYFOURTYKICK:
			kick_scale = debug_settings->GetLongPuntKickMaxDist() - debug_settings->GetLongPuntKickMinDist();
			kick_min = debug_settings->GetLongPuntKickMinDist();
			wind_amount = 0.0f;
			break;
		case KICKTYPE_UPANDUNDER:
			kick_scale = debug_settings->GetUpAndUnderKickMaxDist() - debug_settings->GetUpAndUnderKickMinDist();
			kick_min = debug_settings->GetUpAndUnderKickMinDist();
			wind_amount = 1.0f;
			break;
		case KICKTYPE_BOXKICK:
			kick_scale = debug_settings->GetBoxKickMaxDist() - debug_settings->GetBoxKickMinDist();
			kick_min = debug_settings->GetBoxKickMinDist();
			wind_amount = 1.0f;
			break;
		case KICKTYPE_CHIPKICK:
			kick_scale = debug_settings->GetChipKickMaxDist() - debug_settings->GetChipKickMinDist();
			kick_min = debug_settings->GetChipKickMinDist();
			break;
		case KICKTYPE_SETPLAYCHIPKICK:
			kick_scale = debug_settings->GetSetplayChipKickMaxDist() - debug_settings->GetSetplayChipKickMinDist();
			kick_min = debug_settings->GetSetplayChipKickMinDist();
			break;
		case KICKTYPE_GRUBBERKICK:
			kick_scale = debug_settings->GetGrubberKickMaxDist() - debug_settings->GetGrubberKickMinDist();
			kick_min = debug_settings->GetGrubberKickMinDist();
			break;
		case KICKTYPE_DROPGOAL:
			kick_scale = debug_settings->GetDropGoalKickMaxDist() - debug_settings->GetDropGoalKickMinDist();
			kick_min = debug_settings->GetDropGoalKickMinDist();
			wind_amount = 0.0f;
			break;
		case KICKTYPE_KICKOFF:
			kick_scale = debug_settings->GetKickoffKickMaxDist() - debug_settings->GetKickoffKickMinDist();
			kick_min = debug_settings->GetKickoffKickMinDist();
			wind_amount = 0.0f;
			break;
		case KICKTYPE_DROPKICK:
			kick_scale = debug_settings->GetDropGoalKickMaxDist() - debug_settings->GetDropGoalKickMinDist();
			kick_min = debug_settings->GetDropGoalKickMinDist();
			wind_amount = 0.0f;
			break;
		case KICKTYPE_PLACEKICK:
			kick_scale = debug_settings->GetPlaceKickMaxDist() - debug_settings->GetPlaceKickMinDist();
			kick_min = debug_settings->GetPlaceKickMinDist();
			wind_amount = 0.0f;
			break;
		default:
			kick_scale = debug_settings->GetLongPuntKickMaxDist() - debug_settings->GetLongPuntKickMinDist();
			kick_min = debug_settings->GetLongPuntKickMinDist();
			break;
		}
	}
#endif

	p_kick_strength_pct = (kick_distance - kick_min) / ( kick_scale);

	if (account_for_wind == false)
		wind_amount = 0.0f;

	MabMath::Clamp(p_kick_strength_pct, 0.0f, 1.0f);
}

///-------------------------------------------------------------------------------
/// For a rugby ball at given orientation, find vector from center to given point.
/// (Approximate ball in default position as ellipsoid with major axis of 'radius2' aligned with
/// the z-axis and other axes of 'radius')
///-------------------------------------------------------------------------------

static FVector BallFindChordToPoint(const MabQuaternion &rotation, float radius, float radius2, const FVector &r)
{
	MABASSERT(radius2 > radius); // Not sure if this code works if the ball is flatter than it is round!

	// Ball is aligned along +ve z axis, find that vector in world space
	FVector axis(0, 0, 1);

	//Figure out if this code did actually mean to do the inverse transform, or if it is broken and should do a Transform()!
	axis = rotation.InverseTransform(axis);

	// Ball is symmetric, want the lower point
	if (axis.y > 0) axis = -axis;

	// How much longer is the ball than round?
	float amount_lengthened = (radius2 - radius) / radius;

	// To find the lowest point, we find the lowest point on a sphere and then scale that point
	// along the ball's long axis
	return r + axis * (axis.Dot(r)) * amount_lengthened;
}

///-------------------------------------------------------------------------------
/// test the ball pos against an upright post, modelled as a cylinder
/// returns a collision point and normal
///-------------------------------------------------------------------------------

static bool TestPost(const FVector &ball_pos, float ball_radius, float post_x, float post_z, float post_r, FVector &normal, FVector &contact_point)
{
	const float dx = post_x - ball_pos.x;
	const float dz = post_z - ball_pos.z;
	bool collis = (dx*dx + dz*dz) <= ((ball_radius + post_r)*(ball_radius + post_r));
	if (collis)
	{
		normal = FVector(-dx, 0.0f, -dz).Unit();
		contact_point = FVector(post_x, ball_pos.y, post_z) + normal * post_r;
	}
	return collis;
}

///-------------------------------------------------------------------------------
/// test the ball pos against the crossbar.
///-------------------------------------------------------------------------------

static bool TestCrossbar(const FVector &ball_pos, float ball_radius, float cross_dx, float cross_y, float cross_z, float cross_r, FVector &normal, FVector &contact_point)
{
	if (MabMath::Fabs(ball_pos.x) > cross_dx)
		return false;

	const float dy = cross_y - ball_pos.y;
	const float dz = cross_z - ball_pos.z;
	bool collis = (dy*dy + dz*dz) <= ((ball_radius + cross_r)*(ball_radius + cross_r));
	if (collis)
	{
		normal = FVector(0.0f, -dy, -dz).Unit();
		contact_point = FVector(ball_pos.x, cross_y, cross_z) + normal * cross_r;
	}
	return collis;
}

///-------------------------------------------------------------------------------
/// Check for a collision
///-------------------------------------------------------------------------------

bool ASSBall::CheckPostCollision(SSBallExtrapolationNode* current_node, float post_z)
{
	// Simulate ball as sphere of radius 10cm = 0.1m
#define BALL_RADIUS 0.1f
	// Sometimes we use a more accurate ellipsoidal ball, so use this radius for the longer (major) axis
#define BALL_RADIUS2 0.16f
	// Number of iterations of calculation.
#define NUM_POST_COLLISION_ITERATIONS 8

	const float ball_radius = 0.2f;			// (BALL_RADIUS + BALL_RADIUS2)/2.0f;
	const float post_radius = 0.06f;
	const float cross_radius = 0.2f;
	const float POSTS_COLLISION_EXCESS = 1.0f;

	float goal_half_width	   = game->GetSpatialHelper()->GetPostHalfWidth();
	float goal_crossbar_height = game->GetSpatialHelper()->GetCrossBarHeight();
	float goal_post_height     = game->GetSpatialHelper()->GetPostsHeight();

	/// Basic xz test range (for both posts)
	float xz_check_range_sq = goal_half_width + POSTS_COLLISION_EXCESS;
	xz_check_range_sq *= xz_check_range_sq;

	// Subdivide the movement from current to next node into
	// 'NUM_POST_COLLISION_ITERATIONS' and check each of these.

	FVector current_position = current_node->position;
	FVector delta = FVector::ZeroVector;

	int num_iterations = NUM_POST_COLLISION_ITERATIONS;
	if(current_node->next_node)
	{
		FVector next_position = current_node->next_node->position;
		delta = (next_position - current_position) / ((float) num_iterations + 1.0f);
	}
	else
	{	/// No next node, so one iteration only.
		num_iterations = 1;
	}

	do
	{
		float	cpx = current_position.x;
		float	cpz = current_position.z - post_z;

		/// Fast height check + Basic 2D range check in x,z (remember checking against both posts inside).

		if ( current_position.y < goal_post_height && (cpx*cpx + cpz*cpz)<xz_check_range_sq)
		{
			FVector normal, contact_point;

			// test for collision
			if (	TestPost(current_position, ball_radius,		-goal_half_width, post_z, post_radius, normal, contact_point)
				||	TestPost(current_position, ball_radius,		goal_half_width, post_z, post_radius, normal, contact_point)
				||	TestCrossbar(current_position, ball_radius, goal_half_width, goal_crossbar_height, post_z, cross_radius, normal, contact_point))
			{
				// check ball - posts aren't moving apart... false hit.
				if (normal.Dot(current_node->velocity) >= 0.0f)
					return false;

				// compute collision response
				contact_point = BallFindChordToPoint(current_node->rotation, BALL_RADIUS, BALL_RADIUS2, contact_point - current_position);
				OvalCollisionImpulse(contact_point, normal, FVector::ZeroVector, current_node->velocity, current_node->rotation_speed, SIMULATION_INTERVAL);

				return true;
			}
		}

		current_position += delta;
		num_iterations--;
	} while (num_iterations>0);

	return false;
}

///-------------------------------------------------------------------------------
/// Update...
///-------------------------------------------------------------------------------
void ASSBall::Update(float delta)
{
	if (m_inReplay)
	{
		return;
	}

	UpdateDebugDraw();

	last_ball_position = GetCurrentPosition();
	last_ball_velocity = GetVelocity();

	wwNETWORK_TRACE_JG("Current Ball Position: X: %f Y: %f Z: %f", GetCurrentPosition().X, GetCurrentPosition().Y, GetCurrentPosition().Z);
	// don't update this internal state when the game is suspended
	bool play_suspended = game->GetRules()->IsPlaySuspended(); // if play is suspended don't bother updating dead ball or ball out
	
	RUGamePhase phase = game->GetGameState()->GetPhase();
	RUGamePhase previousPhase = game->GetGameState()->GetPreviousPhase();

	bool was_in_scrum = (phase == RUGamePhase::SCRUM) || (previousPhase == RUGamePhase::SCRUM);


	//bool was_in_quick_lineout = game->GetGameState()->GetPhase() == RUGamePhase::QUICK_LINEOUT ||
	//		game->GetGameState()->GetPreviousPhase() == RUGamePhase::QUICK_LINEOUT;// && MabMath::Fabs( game->GetStrategyHelper()->GetLastBallCollectInfo().pos.x ) > (FIELD_WIDTH * 0.5f) && game->GetStrategyHelper()->GetLastBallFreeInfo().event == BFE_KICK );

	// do a rough check of field bounds with ball to see if we need to be doing any more checks
	// GGs JZ do not need was_in_scrum check here, if we are in or were in a scrum we should be able to trigger out of bounds.
	// Since a lineout involves a player throwing the ball in we need to prevent them from being out, however this is different for league as it's a scrum.
	if ( (!play_suspended /*&& !was_in_scrum && !was_in_quick_lineout*/ && MabMath::Abs(last_ball_position.x) > (FIELD_WIDTH / 2.0f) - 2.0f)
		 || MabMath::Abs(last_ball_position.z) > (FIELD_LENGTH / 2.0f) - 2.0f )
	{
		bool dead = IsDead();
		bool in_touch = IsInTouch();

		// WJS RLC set phase to check depending whether league game or not
		RUGamePhase phaseToCheck = RUGamePhase::SCRUM;;

		// debug log check here to help track potential ball out NMA.
		if (ball_not_in_play == false && consequence_in_progress)
		{
			// WJS RLC ##### Change RUGamePhase::LINEOUT to RUGamePhase::SCRUM?
			if ((in_touch && !dead && !active_node->dead_ball) || (dead &&  phaseToCheck != game->GetGameState()->GetPhase()))
			{
				MABLOGDEBUG("ASSBall::Update: consequence_in_progress=true, and is stopping multiple 'ball_out_detected' events");
			}
		}

		//check if the last update moved the ball or ball holder, out or dead
		if( ball_not_in_play == false && !consequence_in_progress )
		{
			if( in_touch || dead )
			{
				ball_not_in_play = true;

				if ( ball_over_sideline )
				{
					// Did the ball cross over the side-line in play? Or Dead ball?
					float field_extent_z = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals().y;
					field_extent_z *= 0.5f;

					if ( ( ball_over_sideline_position.z > field_extent_z ) || ( ball_over_sideline_position.z < -field_extent_z ) )
					{
						// Ball is definitely dead. It crossed the sideline from the in-goal area.
						dead_or_out_position = GetCurrentPosition();
						in_touch = false;
						active_node->dead_ball = dead = true;
					}
					else
					{
						// Ball is not dead. It landed in the dead ball zone, but did not cross the in-goal area.
						dead_or_out_position = ball_over_sideline_position;
						ball_over_sideline = false;
						in_touch = true;
						active_node->dead_ball = dead = false;
					}
				}
				else
				{
					dead_or_out_position = GetCurrentPosition();
				}
			}

			//fire events
			if( in_touch && !dead && !active_node->dead_ball )
			{
				const BallFreeInfo& lbfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
				bool out_on_full = active_node->out_on_full || (active_node->num_bounces == 0 && !game->GetGameState()->GetBallHolder() && lbfi.event == BFE_KICK );

				// This is where we check if we should fire off a quick lineout
				ARugbyCharacter* ballholder = game->GetGameState()->GetBallHolder();
				if ( ballholder )
				{
					game->GetEvents()->ball_out_no_y(ball_holder->GetMovement()->GetCurrentPosition());					//Used to call the ball out indicator

					const BallCollectInfo& ball_collect_info = game->GetStrategyHelper()->GetLastBallCollectInfo();
					bool caught_over_sidelines = !game->GetSpatialHelper()->IsInField( ball_collect_info.pos );

					if ( 0 == active_node->num_bounces && caught_over_sidelines )
					{
						// Hack. Ball extrapolation logic does not consider the possibility of a player catching the ball.
						// If player catches the ball outside the field of play, consider it as going out on the full.
						out_on_full = true;

						// Trigger a quick lineout?
						// If the team that is not to blame catches the ball out of the field of play (on the full), let them do a quick throw in.
						BallCollectInfo lbci = game->GetStrategyHelper()->GetLastBallCollectInfo();
						RUGamePhase previous_phase = lbfi.game_phase_when_released;

						bool out_on_full_from_kickoff_or_dropout = previous_phase == RUGamePhase::KICK_OFF || previous_phase == RUGamePhase::DROPOUT;
						
						if (  !out_on_full_from_kickoff_or_dropout && !ballholder->GetActionManager()->IsActionRunning(ACTION_TACKLEE) &&
							!game->GetGameTimer()->IsExpired() && lbfi.event == BFE_KICK &&
							// If the current ball holder is the same as before, and we were in a quick lineout, don't trigger another lineout
							//(!was_in_quick_lineout))
							// GGs JZ this previously related to lineouts does this need changing for scrums?
							(!was_in_scrum))
						{
							if ( lbfi.last_player && lbfi.last_player->GetAttributes()->GetTeam() != ballholder->GetAttributes()->GetTeam() &&
								previous_phase != RUGamePhase::PENALTY_KICK_FOR_TOUCH && lbci.event == BCE_CATCH )
							{
								game->GetGameState()->SetPlayRestartPosition( ballholder->GetMabPosition() );
								game->GetGameState()->SetAttackingTeam( ballholder->GetAttributes()->GetTeam() );
								game->GetGameState()->SetPlayRestartTeam( ballholder->GetAttributes()->GetTeam() );

								// Go to quick lineout.
								// WJS RLC ##### DONE CHANGE HERE TO THROW IN BALL INSTEAD OF LINE OUT
								// game->GetGameState()->SetPhase( RUGamePhase::QUICK_LINEOUT );
								game->GetGameState()->SetPhase(phaseToCheck);

								return;
							}
						}
					}
				}
				else
				{
					game->GetEvents()->ball_out_no_y(ball_over_sideline_position);					//Used to call the ball out indicator
				}

				game->GetEvents()->ball_out_detected( ball_holder, dead_or_out_position, out_on_full );

				// Potential fix to stop ball out nma.
				if(game->GetGameState()->IsGameInStandardPlay())
				{
					consequence_in_progress = true;
				}
			}
			else 
			// WJS RLC Changes RUGamePhase::LINEOUT to phaseToCheck
			// if( dead && RUGamePhase::LINEOUT != game->GetGameState()->GetPhase() )
			if (dead && phaseToCheck != game->GetGameState()->GetPhase())
			{
				game->GetEvents()->ball_dead_detected( ball_holder, dead_or_out_position, active_node->out_on_full || active_node->num_bounces == 0 );
				game->GetEvents()->ball_out_no_y(dead_or_out_position);

				// Potential fix to stop ball out nma.
				if(game->GetGameState()->IsGameInStandardPlay())
				{
					consequence_in_progress = true;
				}
			}
		}
		//check if the ball is back in play
		else 
		if( !in_touch && !dead )
		{
			ball_not_in_play = false;
		}
	}

	wwNETWORK_TRACE_JG("Current Ball Position 2: X: %f Y: %f Z: %f", GetCurrentPosition().X, GetCurrentPosition().Y, GetCurrentPosition().Z);

	// detect and store position of ball as it passes the try-line
	// required for commentary to determine ball position relative to posts
	if ( ball_over_tryline == false )
	{
		if ( IsOverTryline() == true )
		{
			ball_over_tryline = true;
			ball_over_tryline_position = GetCurrentPosition();
		}
	}
	else if ( IsOverTryline() == false )
	{
		ball_over_tryline = false;
	}

	// Track where the ball first went over the sidelines so that lineouts can be taken from the correct place.
	if ( ball_over_sideline == false && ball_not_in_play == false )
	{
		if ( IsOverSideline() == true )
		{
			ball_over_sideline = true;
			ball_over_sideline_position = GetCurrentPosition();
		}
	}
	else if ( IsOverSideline() == false )
	{
		ball_over_sideline = false;
	}

	MABASSERT( active_node );
	if ( ball_holder != NULL )
	{	
		wwNETWORK_TRACE_JG("Current Ball Position Ball is being held by %d", ball_holder->GetAttributes()->GetDbId());
		// Get ball transform from animation-skeleton
		RUPlayerAnimation* animation = ball_holder->GetAnimation();
		MabMatrix m;
		if ( animation->GetBallTransform( m ) )
		{

#if PLATFORM_WINDOWS && !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
			if (prev_ball_holder != ball_holder)
			{
				wwNETWORK_TRACE_JG("*******ASSBall::Update. BallHolder Name ***%s", UTF8_TO_TCHAR(*ball_holder->GetName()));
				prev_ball_holder = ball_holder;
			}
#endif
			const static float CONVERGENCE_AMOUNT = 0.95f; // Don't set to 1 as this won't really work

			/// Formula is from John Blow's - http://number-none.com/product/Toward%20Better%20Scripting,%20Part%201/index.html "Vectors changing over time" - Kester put us onto this
			/// from = lerp(to, from, pow(base, dt / convergence));  where base = 1 - convergence_amount
			FVector from_translation = GetMabPosition();
			FVector to_translation   = m.GetTranslation();
			FVector translation = to_translation;

			MabQuaternion to_rotation;
			MabQuaternion from_rotation;
			to_rotation.FromMatrix( m );
			from_rotation.FromMatrix( GetMabTransform() );
			MabQuaternion rotation = to_rotation;

			wwNETWORK_TRACE_JG("ASSBall::Update: translation 1 X: %f Y: %f Z: %f", translation.X, translation.Y, translation.Z);
			wwNETWORK_TRACE_JG("ASSBall::Update: attach_time: %f", attach_time);
			wwNETWORK_TRACE_JG("ASSBall::Update: attach_time: %f", attach_time_rot);

			if ( attach_time > 0.0f )
				translation = MabMath::Lerp( from_translation, to_translation, 1.0f - MabMath::Pow( 1.0f - CONVERGENCE_AMOUNT, delta / attach_time ) );

			if ( attach_time_rot > 0.0f )
				MabQuaternion::MabQuaternionSlerp( from_rotation, to_rotation, 1.0f - MabMath::Pow( 1.0f - CONVERGENCE_AMOUNT, delta / attach_time_rot ), rotation );

#if PLATFORM_WINDOWS && !(UE_BUILD_SHIPPING || UE_BUILD_TEST)

			if (CVarGetBallFreeInfo.GetValueOnAnyThread() != 0 || FParse::Param(FCommandLine::Get(), TEXT("GetBallFreeInfo")))
			{				
				MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(translation, newVector1);
				MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(from_translation, newVector2);
				DrawDebugLine(game->GetGameInstance().GetWorld(), newVector1, newVector2, FColor::Yellow, false, 2.0f);
			}
#endif

			wwNETWORK_TRACE_JG("ASSBall::Update: translation 2 X: %f Y: %f Z: %f", translation.X, translation.Y, translation.Z);

			//dont translate the ball position, let cutscene handle it, but still need the translation to be calculated so that when cutcene ends, ball is in correct position
			if (game->GetCutSceneManager()->IsCinematicRunning() == false) 
			{
				SetPositionAbsolute(translation);
				SetRotationAbsolute(rotation);
				MabMatrix final_transform;
				rotation.ToMatrix(final_transform);
				final_transform.SetTranslation(translation);

				SetMabTransform(final_transform);
				wwNETWORK_TRACE_JG("Current Ball Position 2.5: X: %f Y: %f Z: %f", GetCurrentPosition().X, GetCurrentPosition().Y, GetCurrentPosition().Z);
			}
		}

		wwNETWORK_TRACE_JG("Current Ball Position 2.75: X: %f Y: %f Z: %f", GetCurrentPosition().X, GetCurrentPosition().Y, GetCurrentPosition().Z);

		attach_time -= delta;
		attach_time_rot -= delta;
		return;
	}

	wwNETWORK_TRACE_JG("Current Ball Position 3: X: %f Y: %f Z: %f", GetCurrentPosition().X, GetCurrentPosition().Y, GetCurrentPosition().Z);
	//added this logic to make sure ball doesnot overwrite cutscene ball....
	//if (game->GetCutSceneManager()->IsCinematicRunning() && game->GetCutSceneManager()->CineMaticHasBallProp())
	//	return;

	wwNETWORK_TRACE_JG_DISABLED("Before Delta: time_through_path: %f", time_through_path);

	time_through_path += delta;

	wwNETWORK_TRACE_JG_DISABLED("After Delta: time_through_path: %f delta: %f", time_through_path, delta);

	   
	int last_bounce = active_node->num_bounces;

	int num_nodes_this_update = 0;
	// Find appropriate ('current') node in tree
	while ( (active_node->time < time_through_path) )
	{
		wwNETWORK_TRACE_JG_DISABLED("Current Ball Position 4: time_through_path: %f", time_through_path);
		wwNETWORK_TRACE_JG_DISABLED("Current Ball Position 4: active_node->time: %f", active_node->time);
		num_nodes_this_update++;

		// have we bounced?
		if ( active_node->num_bounces > last_bounce )
		{
			OnBounce( active_node->num_bounces );
			last_bounce = active_node->num_bounces;
		}

		OnUpdate();

		// check that we can go to any children
		if ( active_node->next_node == NULL )
			// break out of the loop if we can't
			break;
		if ( !active_node->next_node->active )
			break;


		// move to the next node, if
		// 1) the next node is active
		// 2) we have either passed its time, or are equal to its time
		if (active_node->next_node->active && (active_node->next_node->time < time_through_path || MabMath::Feq(active_node->next_node->time, time_through_path)))
		{
			active_node = active_node->next_node;
			wwNETWORK_TRACE_JG_DISABLED("Updating active node!");
			wwNETWORK_TRACE_JG_DISABLED("Current Ball Position 4: time_through_path: %f", time_through_path);
			wwNETWORK_TRACE_JG_DISABLED("Current Ball Position 4: active_node->time: %f", active_node->time);
		}
		else
		{
			break;
		}
	}

	wwNETWORK_TRACE_JG("Current Ball Position 4: X: %f Y: %f Z: %f", GetCurrentPosition().X, GetCurrentPosition().Y, GetCurrentPosition().Z);
	// if there is a node we are going to, then we want to interpolate
	// HACK: This should be done via the proper interpolation methods
	if ( active_node->next_node && active_node->next_node->active )
	{
		percent_through_node = ( time_through_path - active_node->time ) / ( active_node->next_node->time - active_node->time );
	}
	else
	{
		percent_through_node = 0.0f;
	}

	wwNETWORK_TRACE_JG_DISABLED("Current Ball Position 4: percent_through_node: %f, time_through_path: %f, active_node->time: %f, active_node->next_node->time: %f",	percent_through_node, time_through_path, active_node->time, active_node->next_node->time);

	MabQuaternion rotation( SSBallExtrapolatorHelper::GetRotationAtTime(active_node, time_through_path) );
	MabMatrix mtx1;
	rotation.ToMatrix( mtx1 );
	FVector position( SSBallExtrapolatorHelper::GetPositionAtTime(active_node, time_through_path) );
	MabMatrix mtx2 = MabMatrix::TransMatrix( position );
	mtx1 = mtx1 * mtx2;
	SetMabTransform( mtx1 );

	wwNETWORK_TRACE_JG("Current Ball Position 4.5: X: %f Y: %f Z: %f", GetCurrentPosition().X, GetCurrentPosition().Y, GetCurrentPosition().Z);

	//ball movement updated, check if it just flew between the posts
	HandleThroughThePostsEvent();

	{
		/// Check to see if the ball is about to go out on the full from a restart
		const static float PROJ_TIME = 0.3f;
		FVector last_projected_pos = last_ball_position + last_ball_velocity * PROJ_TIME;
		FVector this_projected_pos = GetCurrentPosition() + GetVelocity() * PROJ_TIME;
		#define BALL_OUT( pos ) (MabMath::Fabs( pos.x ) > FIELD_WIDTH * 0.5f || MabMath::Fabs( pos.z ) > (FIELD_LENGTH + FIELD_IN_GOAL_LENGTH) * 0.5f )
		bool was_last_projected_out = BALL_OUT( last_projected_pos );
		bool was_this_projected_out = BALL_OUT( this_projected_pos );

		wwNETWORK_TRACE_JG_DISABLED("last_projected_pos 4.75: X: %f Y: %f Z: %f", last_projected_pos.X, last_projected_pos.Y, last_projected_pos.Z);
		wwNETWORK_TRACE_JG_DISABLED("this_projected_pos 4.75: X: %f Y: %f Z: %f", this_projected_pos.X, this_projected_pos.Y, this_projected_pos.Z);
		wwNETWORK_TRACE_JG_DISABLED("last_ball_position 4.75: X: %f Y: %f Z: %f", last_ball_position.X, last_ball_position.Y, last_ball_position.Z);
		wwNETWORK_TRACE_JG_DISABLED("last_ball_velocity 4.75: X: %f Y: %f Z: %f", last_ball_velocity.X, last_ball_velocity.Y, last_ball_velocity.Z);

		const BallFreeInfo& bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
		bool was_from_kick_restart = bfi.event == BFE_KICK && (bfi.sub_type == KICKTYPE_KICKOFF || bfi.sub_type == KICKTYPE_DROPGOAL) && bfi.game_phase_when_released == RUGamePhase::KICK_OFF;
		if ( !was_last_projected_out && was_this_projected_out && was_from_kick_restart && IsOnTheFull() )
		{
			float time;
			FVector first_bounce_pos;
			GetBouncePosition( 1, first_bounce_pos, time );

			if ( BALL_OUT( first_bounce_pos ) )
				game->GetEvents()->predict_restart_out_on_full();
		}
	}

	wwNETWORK_TRACE_JG("Current Ball Position 5: X: %f Y: %f Z: %f", GetCurrentPosition().X, GetCurrentPosition().Y, GetCurrentPosition().Z);

	NET_STATE_CHECK_SCOPE( BallUpdate);
	NET_STATE_CHECK_WRITE( num_extrapolation_elements);
	NET_STATE_CHECK_WRITE( last_ball_position);
	NET_STATE_CHECK_WRITE( last_ball_velocity);
	NET_STATE_CHECK_WRITE( scale_multiplier);
	NET_STATE_CHECK_WRITE( time_through_path);
	NET_STATE_CHECK_WRITE( percent_through_node);
	NET_STATE_CHECK_WRITE( attach_time);
	NET_STATE_CHECK_WRITE( attach_time_rot);
	NET_STATE_CHECK_WRITE( absolute_move_set);
	NET_STATE_CHECK_WRITE( absolute_rotate_set);
	NET_STATE_CHECK_WRITE( absolute_move_previous);
	NET_STATE_CHECK_WRITE( absolute_rotate_previous);
	NET_STATE_CHECK_WRITE( is_using_travel_line);
	NET_STATE_CHECK_WRITE( travel_start);
	NET_STATE_CHECK_WRITE( travel_end);
	NET_STATE_CHECK_WRITE( travel_current_pos);
	NET_STATE_CHECK_WRITE( travel_adjustment);
	NET_STATE_CHECK_WRITE( travel_rotation_speed);
	NET_STATE_CHECK_WRITE( travel_distance);
	NET_STATE_CHECK_WRITE( travel_amount_through);
	NET_STATE_CHECK_WRITE( travel_time);
	NET_STATE_CHECK_WRITE( travel_gravity);
	NET_STATE_CHECK_WRITE( travel_max_height);
	NET_STATE_CHECK_WRITE( travel_line_wobble_angle); // how much the ball will wobble in flight - in radians
	NET_STATE_CHECK_WRITE( pre_kick_rotation);
}

const FVector& ASSBall::GetCurrentBallOverTrylinePosition()
{
	// force a re-check here, in case game event using this is called before update

	// detect and store position of ball as it passes the tryline
	// required for commentary to detrmine ball position relative to posts
	if ( ball_over_tryline == false )
	{
		if ( IsOverTryline() == true )
		{
			ball_over_tryline = true;
			ball_over_tryline_position = GetCurrentPosition();
		}
	}
	else if ( IsOverTryline() == false )
	{
		ball_over_tryline = false;
	}

	return ball_over_tryline_position;
}

void ASSBall::HandleThroughThePostsEvent()
{
	//try for an early return

	if( ball_holder != NULL )
		return;

	// All of these tests are only done if the ball is free from a kick and we know it is an
	// attempt at goal - this means we wont get ball through post notifications when it bounces
	// through or any other such thing.
	const BallFreeInfo& bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	if(bfi.event != BFE_KICK)
		return;

	bool is_attempt_at_goal = ( bfi.sub_type == KICKTYPE_DROPGOAL || bfi.sub_type == KICKTYPE_PLACEKICK ) && !bfi.was_kick_restart;

	//Glen: we would like to fire some commentary for kicks that pass over the goal that
	//are of the wrong type. This is with the exception of kicks that are meant to end up in the
	//goal zone, which may pass over the goal on the way to a player who will score a try.
	bool is_possible_attempt_of_wrong_type = false;
	if(!is_attempt_at_goal && (bfi.sub_type == KICKTYPE_LONGPUNT || bfi.sub_type == KICKTYPE_BOXKICK))
	{
		is_possible_attempt_of_wrong_type = true;
	}

	// If ball is not through posts, and last position was less than try line, current position
	// is greater than try line and height is above the cross bar,
	const FVector& position( GetCurrentPosition() );
	ASSBall::BallThroughPostQueryResult result_type;
	bool ball_through_posts = IsCrossingOverCrossbarBetweenPosts( last_ball_position, position, result_type );

	if ( result_type ==  BALL_POST_UNDER )
	{
		RUGameEvents* events = game->GetEvents();
		events->ball_under_posts( position, bfi.sub_type );
	}

	if( is_attempt_at_goal == false && is_possible_attempt_of_wrong_type == false)
		return;

	if(is_attempt_at_goal)
	{
		if ( ball_through_posts )
		{
			// notify the monitor
			RUGameEvents* events = game->GetEvents();
			events->ball_through_posts( position );
		}
		else if( result_type == ASSBall::BALL_POST_WIDE
			|| result_type == ASSBall::BALL_POST_UNDER)
		{
			RUGameEvents* events = game->GetEvents();
			events->ball_missed_goal( position );
		}
	}
	else if(is_possible_attempt_of_wrong_type && ball_through_posts)
	{
		//they've done some other type of kick, and it went over, lets fire a through goal of wrong type event.
		//Glen: I've made this a separate event now rather than make all the current handlers detect the
		//kick type themselves to avoid potentially breaking things this late in the game.
		RUGameEvents* events = game->GetEvents();
		events->ball_through_posts_of_wrong_type( position );
	}
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::SetTimeThroughExtrapolation( float time )
{
	time_through_path = time;

	// find the right node in the extrapolation
	SSBallExtrapolationNode* node = root_node;
	while( node->next_node != NULL && node->next_node->time < time && node->next_node->active ) node = node->next_node;

	active_node = node;

	// if there is a node we are going to, then we want to interpolate
	// HACK: This should be done via the proper interpolation methods
	if ( active_node->next_node && active_node->next_node->active )
	{
		percent_through_node = ( time_through_path - active_node->time ) / ( active_node->next_node->time - active_node->time );
	} else {
		percent_through_node = 0.0f;
	}

	wwNETWORK_TRACE_JG("ASSBall::SetTimeThroughExtrapolation %f time: %f", time_through_path, time);
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::StopAllMotion()
{
	// Clear velocities
	active_node->velocity = FVector::ZeroVector;
	active_node->rotation_speed = FVector::ZeroVector;

	// Remove all child nodes, we're not going anywhere else
	active_node->ClearChildren();
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

bool ASSBall::IsInMotion() const
{
	MABASSERT( active_node->active );
	// check that we're actually moving
	return ( active_node->velocity.Magnitude() > 0.1f );
}

///-------------------------------------------------------------------------------
/// Walk the nodes looking for a change in bounces
///-------------------------------------------------------------------------------

bool ASSBall::WillBallBounceAgain() const
{
	if( !active_node )
		return false;

	SSBallExtrapolationNode *current = active_node;
	bool ball_will_bounce = false;
	short current_bounce_number = active_node->num_bounces;

	while( current != NULL && current->active && !ball_will_bounce )
	{
		ball_will_bounce = current->num_bounces > current_bounce_number;
		current = current->next_node;
	}

	return ball_will_bounce;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

FVector ASSBall::GetExtrapolationPosition() const
{
	// work out the true position of the ball
	return SSBallExtrapolatorHelper::GetPositionAtTime( active_node, time_through_path ) + travel_adjustment;
}


FVector ASSBall::GetPositionAtTime( float time ) const
{
	return SSBallExtrapolatorHelper::GetPositionAtTime( active_node, time ) + travel_adjustment;
}


///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

FVector ASSBall::GetVisualPosition()
{
	return GetMabPosition();
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

FVector ASSBall::GetCurrentPosition( bool zero_out_y )
{
	FVector result = GetMabPosition();
	if ( zero_out_y )
	{
		result.y = 0.0f;
	}
	return result;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

MabQuaternion ASSBall::GetCurrentRotation()
{
	// work out the true rotation of the ball
	return GetMabOrientation();
	//return SSBallExtrapolatorHelper::GetRotationAtTime( active_node, time_through_path );//- active_node->time );
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

FVector ASSBall::GetVelocity() const
{
	if ( ball_holder != nullptr )
		return ball_holder->GetMovement()->GetCurrentVelocity();

	// work out the true velocity of the ball
	return SSBallExtrapolatorHelper::GetVelocityAtTime( active_node, time_through_path - active_node->time );
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

bool ASSBall::GetBouncePosition( int bounce_number, FVector& out, float& time )
{
	SSBallExtrapolationNode *current = root_node;

	while( current != NULL && current->active )
	{

		if ( current->num_bounces == bounce_number )
		{

			// found the bounce node, now just work out where we bounced
			out = current->position;
			time = current->time;

			return true;
		}

		current = current->next_node;
	}

	// if we dont find the bounce pos, just return the final position of the ball
	current = root_node;
	SSBallExtrapolationNode * last_active = root_node;
	while( current->active )
	{
		last_active = current;
		current = current->next_node;
	}

	// IF WE HIT THIS MABASSERT THEN WE DONT HAVE *ANY* ACTIVE NODES ON THE BALL! WHATS GONE WRONG?
	MABASSERT( last_active->active );

	out = last_active->position;
	time = last_active->time;

	return false;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::SetRotation( const MabQuaternion& rotation )
{
	// set a new rotation for the currently active node
	active_node->rotation = rotation;
	active_node->rotation.Normalize();
	active_node->active = true;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::SetPositionAbsolute ( const FVector &new_position )
{
	// if we dont have a pending SetPositionAbsolute, make note of its previous position so we know where to keyframe from
	if ( !absolute_move_set ) {
		absolute_move_previous = GetCurrentPosition();

		absolute_move_set = true;
	}

	// copy the current node so we get all its details, and just change the position
	active_node->CopyTo( root_node );

	// make it so we have no nodes that are active
	root_node->ClearChildren();
	active_node = root_node;
	active_node->active = false;

	// set the new position
	SetPosition(new_position);
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::SetRotationAbsolute( const MabQuaternion& rotation )
{
	// if we dont have a pending SetRotationAbsolute, make note of its previous rotation so we know where to keyframe from
	if ( !absolute_rotate_set ) {
		absolute_rotate_previous = GetCurrentRotation();

		absolute_rotate_set = true;
	}

	// copy the current node so we get all its details, and just change the position
	active_node->CopyTo( root_node );

	// make it so we have no nodes that are active
	root_node->ClearChildren();
	active_node = root_node;
	active_node->active = false;

	pre_kick_rotation = MabQuaternion::IDENTITY; //RC4-3888: Have to reset this, otherwise it will use some irrevelant data from previous kick.

	// set the new position
	SetRotation( rotation );
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

bool ASSBall::IsOnTheFull()
{
	return IsInMotion() && active_node->num_bounces == 0;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

int	ASSBall::GetNumBounces()
{
	return active_node->num_bounces;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::UseExtrapolationStorage( SSBallExtrapolationNode* root, int num_elements )
{
	root_node = root;
	num_extrapolation_elements = num_elements;

	NotifyExtrapolationChange();
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

bool ASSBall::IsInTouch()
{
	bool out_sides = false;
	bool over_try_line = false;

	// Ismael - There is an issue with the ball being detected out of sides at a lineout, so we need to prevent being this called when you're in a lineout phase
	RUGamePhase game_phase = game->GetGameState()->GetPhase();

	// WJS RLC Probably don't need the || game_phase == RUGamePhase::QUICK_LINEOUT as there is no lineout in League
	if (game_phase == RUGamePhase::SCRUM || game_phase == RUGamePhase::QUICK_LINEOUT)
		return false;

	float GROUNDED_HEIGHT_MIN = 0.2f;

	if(ball_holder != 0)
	{
		FVector bone_pos;
		BoneIndex bone_idx;
		SSSpatialHelper* helper = game->GetSpatialHelper();

		for ( int i = 0; i < NUM_BONES_TO_TEST; ++i )
		{
			bone_idx = ball_holder->FindBone(BONE_POSITIONS[i].c_str());

			if (bone_idx != INDEX_NONE && bone_idx < ball_holder->GetBoneCount())
			{
				bone_pos = ball_holder->GetBoneWorldPosition(bone_idx);
				if (bone_pos.y < GROUNDED_HEIGHT_MIN)
				{
					if (helper->IsJointOutOrTouchingSides(bone_pos))
						out_sides = true;
					if (helper->IsJointOverTryline(bone_pos))
						over_try_line = true;

					if (out_sides || over_try_line)
						break;
				}
			}
		}
	}
	else
	{
		FVector ball_position = GetCurrentPosition();

		FieldExtents extents = game->GetSpatialHelper()->GetFieldExtents();
		FieldExtents try_line_extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
		extents.x /= 2.0f;
		extents.y /= 2.0f;
		try_line_extents.y /= 2.0f;

		out_sides = ( ( ball_position.x > extents.x ) || ( ball_position.x < -extents.x ) ) && ( ball_position.y < GROUNDED_HEIGHT_MIN );
		over_try_line = ( ( ball_position.z > try_line_extents.y ) || ( ball_position.z < -try_line_extents.y ) ) && ( ball_position.y < GROUNDED_HEIGHT_MIN );

		// If ball is 'well' out then allow in touch straight away - to stop ball bouncing around under stadium + speed up play.
		if(!out_sides)
		{
			const float IMMEDIATE_INTOUCH_DISTANCE = 2.5f;
			extents.x += IMMEDIATE_INTOUCH_DISTANCE;

			if( ( ball_position.x > extents.x) || ( ball_position.x < -extents.x) )
			{
				float time = 0.0f;
				FVector first_bounce( FVector::ZeroVector );
				game->GetBall()->GetBouncePosition(1, first_bounce, time );

				extents.x += IMMEDIATE_INTOUCH_DISTANCE;
				if( ( first_bounce.x > extents.x ) || ( first_bounce.x < -extents.x ) )
				{
					out_sides = true;
				}
			}
		}
	}

	return out_sides && !over_try_line;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

bool ASSBall::IsDead()
{
	bool out_ends = false;
	bool out_sides = false;
	bool over_try_line = false;
	float GROUNDED_HEIGHT_MIN = 0.2f;

	if(ball_holder != 0)
	{
		FVector bone_pos;
		BoneIndex bone_idx;
		SSSpatialHelper* helper = game->GetSpatialHelper();

		for ( int i = 0; i < NUM_BONES_TO_TEST; ++i )
		{
			bone_idx = ball_holder->FindBone(BONE_POSITIONS[i].c_str());

			if (bone_idx != INDEX_NONE && bone_idx < ball_holder->GetBoneCount())
			{
				bone_pos = ball_holder->GetBoneWorldPosition(bone_idx);
				if (bone_pos.y < GROUNDED_HEIGHT_MIN)
				{
					if (helper->IsJointOutSides(bone_pos))
						out_sides = true;
					if (helper->IsJointOverTryline(bone_pos))
						over_try_line = true;
					if (helper->IsJointOutEnds(bone_pos))
						out_ends = true;

					if ((out_sides && over_try_line) || out_ends)
						break;
				}
			}

		}
	}
	else
	{
		FVector ball_position = GetCurrentPosition();

		FieldExtents extents = game->GetSpatialHelper()->GetFieldExtents();
		FieldExtents try_line_extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
		extents.x /= 2.0f;
		extents.y /= 2.0f;
		try_line_extents.y /= 2.0f;

		// Has the ball crossed any of the important lines?
		out_sides = ( ( ball_position.x > extents.x ) || ( ball_position.x < -extents.x ) ) && ( ball_position.y < GROUNDED_HEIGHT_MIN );
		over_try_line = ( ( ball_position.z > try_line_extents.y ) || ( ball_position.z < -try_line_extents.y ) );
		out_ends = ( ( ball_position.z > extents.y ) || ( ball_position.z < -extents.y ) ) && ( ball_position.y < GROUNDED_HEIGHT_MIN );

		// If ball is 'well' out then allow in touch straight away - to stop ball bouncing around under stadium + speed up play.
		if(!out_ends)
		{
			const float IMMEDIATE_DEAD_DISTANCE = 2.5f;
			extents.y += IMMEDIATE_DEAD_DISTANCE;

			if( ( ball_position.z > extents.y ) || ( ball_position.z < -extents.y ) )
			{
				float time = 0.0f;
				FVector first_bounce( FVector::ZeroVector );
				game->GetBall()->GetBouncePosition(1, first_bounce, time );

				if( ( first_bounce.z > extents.y ) || ( first_bounce.z < -extents.y ) )
				{
					out_ends = true;
				}
			}
		}


	}

	return ( out_sides && over_try_line ) || out_ends;
}

///-------------------------------------------------------------------------------
/// IsDead and IsInTouch are both false when the ball is touch in goal
/// However, we need to know the ball position as it crosses the try line
/// to determine which side of the posts it went over the line on
///-------------------------------------------------------------------------------

bool ASSBall::IsOverTryline()
{
	FVector ball_position = GetCurrentPosition();

	FieldExtents try_line_extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	try_line_extents.y /= 2.0f;

	// Has the ball crossed any of the important lines?
	bool over_try_line = ( ( ball_position.z > try_line_extents.y ) || ( ball_position.z < -try_line_extents.y ) );

	return over_try_line;
}

bool ASSBall::IsOverSideline()
{
	FVector ball_position = GetCurrentPosition();

	FieldExtents field_extents = game->GetSpatialHelper()->GetFieldExtents();
	field_extents.x *= 0.5f;

	bool out_sides = ( ( ball_position.x > field_extents.x ) || ( ball_position.x < -field_extents.x ) );

	return out_sides;
}

///-------------------------------------------------------------------------------
/// Test does not use the IsInTouch() IsDead() or ball_not_in_play as these are all
/// tested on current ball holder and valid try position needs to be tested on ball
/// position
///-------------------------------------------------------------------------------
bool ASSBall::IsValidTryPosition()
{
	if ( !IsOverTryline() )
		return false;

	FVector ball_position = GetCurrentPosition();
	// check dead
	bool is_dead = game->GetSpatialHelper()->IsJointOutEnds( ball_position );
	bool is_out = game->GetSpatialHelper()->IsJointOutSides( ball_position );

	return !is_dead && !is_out;
}


bool ASSBall::IsValidRuckPosition()
{
	bool ball_in_valid_ruck_place = !IsInTouch() && !IsDead() && !IsValidTryPosition() && !GetTryCheck();
	return ball_in_valid_ruck_place;
}


void ASSBall::ForceExtroplationEndpoint( SSBallExtrapolationNode* last_node )
{
	SSBallExtrapolationNode* test_node = active_node;
	while( test_node != last_node && test_node != NULL && test_node->active )
	{
		test_node = test_node->next_node;
	}
	if( test_node == last_node )
	{
		test_node->next_node->active = false;
	}
}

void ASSBall::ForceExtroplationEndpoint( SSBallExtrapolationNode* prior_node, float next_node_time, const FVector& next_node_pos,
										const FVector& next_node_vel, const FVector& next_node_rot_spd, const MabQuaternion& next_node_rot )
{
	SSBallExtrapolationNode* test_node = active_node;
	while( test_node != prior_node && test_node != NULL && test_node->active )
	{
		test_node = test_node->next_node;
	}
	if( test_node == prior_node )
	{
		test_node->next_node->next_node->active = false;
		test_node->next_node->active = true;
		test_node->next_node->time = next_node_time;
		test_node->next_node->position = next_node_pos;
		test_node->next_node->velocity = next_node_vel;
		test_node->next_node->rotation_speed = next_node_rot_spd;
		test_node->next_node->rotation = next_node_rot;
	}
}

#ifdef ENABLE_NETWORK_STATE_CHECKER
void ASSBall::PopulateNetworkStateCheck()
{
	NET_STATE_CHECK_SCOPE(SSBall);
	FVector pos = GetExtrapolationPosition();
	NET_STATE_CHECK_WRITE(pos);
	MabQuaternion rot = GetCurrentRotation();
	NET_STATE_CHECK_WRITE(rot);
}
#endif

//**********************************************************************************************************************
//**********************************************************************************************************************
// RL3BallHelper.cpp




///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::ContinueExtrapolation(SSBallExtrapolationNode *start_node)
{
	oval_extrapolator.ReExtrapolate( start_node, BALL_EXTRAPOLATION_SIZE - static_cast<int>(start_node - extrapolation_buffer) );
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::OvalCollisionImpulse(const FVector &r, const FVector &n, const FVector &resting_impulse, FVector &velocity, FVector &rotation_speed, float time, float random )
{
	oval_extrapolator.CollisionImpulse(r, n, resting_impulse, velocity, rotation_speed, time, random);
}

///-------------------------------------------------------------------------------
/// does a very simple kick
///-------------------------------------------------------------------------------

void ASSBall::KickSimple( KickType type, const FVector &new_velocity, bool is_simulation )
{
	if ( !is_simulation )
	{
		game->GetEvents()->change_to_post_kick_camera(type);
	}

	float kick_quality = type == KICKTYPE_NONE ? 0.0f : 1.0f;

	if ( is_simulation )
	{
		SaveExtrapolationState();
		SwapExtrapolationBuffers();

		oval_extrapolator.SetTotalWindDisplacement( CalculateWindDisplacement( new_velocity ) );
		oval_extrapolator.SetExtrapolationParameters( extrapolation_parameters );
		oval_extrapolator.Extrapolate( extrapolation_buffer, BALL_EXTRAPOLATION_SIZE, this->GetCurrentPosition(), new_velocity, pre_kick_rotation, KickRotationSpeed(new_velocity, kick_quality, type) );

		SwapExtrapolationBuffers();
		RestoreExtrapolationState();
	}
	else
	{
		if (game->GetGameState()->GetBallHolder() != GetHolder())
		{
			UE_LOG(LogTemp, Display, TEXT("ASSBall::KickSimple: GameState Ball Holder (%s) !=  Ball Holder (%s)"), 
				game->GetGameState()->GetBallHolder() ? *game->GetGameState()->GetBallHolder()->GetName() : TEXT("NULL"),
				GetHolder() ? *GetHolder()->GetName() : TEXT("NULL"));
		}

		if ( game->GetGameState()->GetBallHolder() != NULL )
		{
			game->GetGameState()->SetBallHolder( NULL );
		}

		FVector rotation_speed = KickRotationSpeed( new_velocity, kick_quality, type );

		ASSBall::RawExtrapolation( this->GetCurrentPosition(), new_velocity, pre_kick_rotation, rotation_speed, CalculateWindDisplacement( new_velocity ) );
	}
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::FreeBallKick( FVector &new_velocity )
{
	KickSimple( KICKTYPE_FREEBALLKICK, new_velocity, false );
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::SaveExtrapolationState()
{
	saved_active_node = active_node;
	saved_percent_through_node = percent_through_node;
	saved_time_through_path = time_through_path;
	saved_travel_adjustment = travel_adjustment;
	wwNETWORK_TRACE_JG("SaveExtrapolationState time_through_path: %f", time_through_path);
}

void ASSBall::RestoreExtrapolationState()
{
	wwNETWORK_TRACE_JG("RestoreExtrapolationState time_through_path: %f", time_through_path);
	active_node = saved_active_node;
	percent_through_node = saved_percent_through_node;
	time_through_path = saved_time_through_path;
	travel_adjustment = saved_travel_adjustment;
	wwNETWORK_TRACE_JG("RestoreExtrapolationState time_through_path: %f", time_through_path);
}

void ASSBall::SwapExtrapolationBuffers()
{
	SSBallExtrapolationNode* t = extrapolation_buffer;
	extrapolation_buffer = temporary_buffer;
	temporary_buffer = t;

	// set the ball to use the new extrapolation storage
	this->UseExtrapolationStorage( extrapolation_buffer, BALL_EXTRAPOLATION_SIZE );
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::RawExtrapolation( const FVector& position, const FVector& velocity,
							   const MabQuaternion& rotation, const FVector& rotation_speed,
							   const FVector& wind_displacement )
{
	/// Save record of state + extrapolation parameters here.

	replay_store.is_pass = false;
	replay_store.start = position;
	replay_store.end = velocity;
	replay_store.rotation = rotation;
	replay_store.rotation_speed = rotation_speed;
	replay_store.wind_displacement = wind_displacement;

	///--------------------

	oval_extrapolator.SetExtrapolationParameters( extrapolation_parameters );
	oval_extrapolator.SetTotalWindDisplacement( wind_displacement );
	oval_extrapolator.Extrapolate( extrapolation_buffer, BALL_EXTRAPOLATION_SIZE,
								   position, velocity,
								   rotation, rotation_speed );

	this->NotifyExtrapolationChange();
}

///-------------------------------------------------------------------------------
/// Given maximum height of a kick, calculate displacement of end point as a result of wind
///-------------------------------------------------------------------------------

FVector ASSBall::CalculateWindDisplacement( float kick_height )
{
	// Ramp wind off from max at 6m to none below 1m
	float min_wind_height = 2.0f;
	float max_wind_height = 10.0f;
	FVector min_wind_displacement(0, 0, 0);
	FVector max_wind_displacement = extrapolation_parameters.wind_amount * extrapolation_parameters.wind_velocity;
	float wind_amount = ( kick_height - min_wind_height ) / ( max_wind_height - min_wind_height );
	MabMath::Clamp<float>(wind_amount, 0.0f, 1.0f);

	// Smooth off start and end (Sigmoid-like function with domain and range of [0,1])
	// TODO do we want that?
	//wind_amount = wind_amount * wind_amount * (3 - 2 * wind_amount);
	FVector return_val = wind_amount * max_wind_displacement + (1 - wind_amount) * min_wind_displacement;

	return return_val;
}
///-------------------------------------------------------------------------------
/// Given kick of approximate distance and vertical angle, calculate displacement
/// of end point as a result of wind
///-------------------------------------------------------------------------------

FVector ASSBall::CalculateWindDisplacement( float distance, float angle_degrees )
{
	// Find velocity in vertical direction
	float totalVelocity = ConvertKickDistToMagnitude( distance, angle_degrees );
	float verticalVelocity = totalVelocity * MabMath::Sin( MabMath::Deg2Rad( angle_degrees ) );
	// Assuming kick follows a parabola, calculate the maximum height
	float max_height = 0.5f * verticalVelocity * verticalVelocity / extrapolation_parameters.gravity;
	return CalculateWindDisplacement( max_height );
}

///-------------------------------------------------------------------------------
/// Given kick of approximate velocity, calculate displacement of end point as a
/// result of wind
///-------------------------------------------------------------------------------

FVector ASSBall::CalculateWindDisplacement( const FVector &velocity )
{
	float verticalVelocity = velocity.y;
	// Assuming kick follows a parabola, calculate the maximum height
	float max_height = 0.5f * verticalVelocity * verticalVelocity / extrapolation_parameters.gravity;
	return CalculateWindDisplacement( max_height );
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

FVector ASSBall::KickRotationSpeed( const FVector &kick, float kick_quality, const KickType type  )
{
	// Compare perfect pre kick ball rotation with actual ball rotation to give correct spin.
	FVector perfect_rotation(0,0,0);
	FVector perfect_spin(0,0,0);
	float kick_angle = MabMath::GetAngle( kick.x, kick.z );

	// Conversion actual from quat to ball axis
	MabQuaternion q = pre_kick_rotation; //this->GetCurrentRotation();
	FVector ball_axis = q.Transform( FVector(1,0,0));
	MabMatrix rot_m = MabMatrix::RotMatrix(0, kick_angle, 0);

	switch ( type )
	{
	case KICKTYPE_CHIPKICK:
	case KICKTYPE_SETPLAYCHIPKICK:
		{
			perfect_rotation = ASSBall::rotation_chip;
			perfect_rotation.y += kick_angle;
			perfect_spin = ASSBall::spin_chip;
		}
		break;
	case KICKTYPE_DROPGOAL:
		{
			perfect_rotation = ASSBall::rotation_drop_kick;
			perfect_rotation.y += kick_angle;
			perfect_spin = ASSBall::spin_drop_kick;
		}
		break;
	case KICKTYPE_GRUBBERKICK:
		{
			perfect_rotation = ASSBall::rotation_grubber;
			perfect_rotation.y += kick_angle;
			perfect_spin = ASSBall::spin_grubber;
		}
		break;
	case KICKTYPE_PENALTYPUNT:
	case KICKTYPE_LONGPUNT:
		{
			// HES Complaining that all punts are torps
			if (game->GetRNG()->RAND_CALL(float) < 0.8f)
			{
				perfect_rotation = ASSBall::rotation_punt;
				perfect_rotation.y += kick_angle;
				perfect_spin = ASSBall::spin_punt;
				
			}
			else
			{
				perfect_rotation = ASSBall::rotation_long_punt;
				perfect_rotation.y += kick_angle;
				perfect_spin = ASSBall::spin_long_punt;

				// spin towards the sideline for spiral punts
				if (perfect_rotation.y < 0.5f * PI && perfect_rotation.y > 0.0f)
					perfect_spin.y *= -1;
				else if (perfect_rotation.y < -0.5f * PI && perfect_rotation.y > -PI)
					perfect_spin.y *= -1;

				// use rotation down the ball_axis rather than kick vector to give better result
				rot_m = MabMatrix::RotMatrix(ball_axis.x, ball_axis.y, ball_axis.z);
			}
		}
		break;
	case KICKTYPE_UPANDUNDER:
		{
			perfect_rotation = ASSBall::rotation_up_and_under;
			perfect_rotation.y += kick_angle;
			perfect_spin = ASSBall::spin_up_and_under;
		}
		break;
	case KICKTYPE_BOXKICK:
		{
			perfect_rotation = ASSBall::rotation_box_kick;
			perfect_rotation.y += kick_angle;
			perfect_spin = ASSBall::spin_box_kick;
		}
		break;
	case KICKTYPE_PLACEKICK:
		{
			perfect_rotation = ASSBall::rotation_place_kick;
			perfect_rotation.y += kick_angle;
			perfect_spin = ASSBall::spin_place_kick;
		}
		break;
	case KICKTYPE_NONE:
		{
			MabQuaternion ball_rot = MabQuaternion::EulerToQuaternion( ball_axis.x, ball_axis.y, ball_axis.z );
			perfect_rotation = ball_rot.Transform( FVector(1,0,0) );
			perfect_spin = FVector(0,0,0);
		}
		break;
	case KICKTYPE_THROWIN:
		{
			perfect_rotation = FVector(0, 0, 1);
			perfect_spin = FVector(0, 0, 0);
			rot_m = MabMatrix::IDENTITY;
		}
		break;
	default:
		{
			perfect_rotation = FVector(0.5f*PI, 0, 0);
			perfect_spin = FVector(1, 0, 0);
		}
		break;
	}

	// calculate perfect rotation ball axis
	MabQuaternion perfect_q = MabQuaternion::EulerToQuaternion( perfect_rotation.x, perfect_rotation.y, perfect_rotation.z );
	FVector test_rot = perfect_q.Transform( FVector(1,0,0) );

	kick_quality *= MabMath::Fabs( test_rot.Dot(ball_axis) );
	if (kick_quality > 0.9f)
		kick_quality = 1.0f;

	// Find bad spin
	FVector cross = kick.Cross(ball_axis);
	cross.Normalise();

	FVector cross_spin = FVector( cross.y, cross.x, cross.z );

	FVector spin = kick_quality*perfect_spin + (1.0f - kick_quality) * cross_spin;

	// Vaughan TODO 30/7/10 Spin speed is set to amount should this be variable instead?
	if (spin.Magnitude() < 0.01f) spin = FVector(1, 0, 0);
	else if ( spin.Magnitude() > 1.0f ) spin.Normalise();

	float amount = 5.0f * 2 * PI;
	spin *= amount;

	// return kick rotation
	FVector rot_speed;
	MabMatrix::MatrixMultiply(rot_speed, spin, rot_m);
	return rot_speed;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

float ASSBall::ConvertKickDistToMagnitude( float kick_distance, KickType kick_type )
{
	switch( kick_type )
	{
	case KICKTYPE_UPANDUNDER:
		return ConvertKickDistToMagnitude( kick_distance, UP_AND_UNDER_BASE_ANGLE + UP_AND_UNDER_RAND_ANGLE / 2.0f );
		break;
	case KICKTYPE_BOXKICK:
		return ConvertKickDistToMagnitude( kick_distance, BOX_KICK_BASE_ANGLE + BOX_KICK_RAND_ANGLE / 2.0f );
		break;
	case KICKTYPE_PENALTYPUNT:
	case KICKTYPE_LONGPUNT:
		return ConvertKickDistToMagnitude( kick_distance, LONG_PUNT_BASE_ANGLE + LONG_PUNT_RAND_ANGLE / 2.0f );
		break;
	case KICKTYPE_CHIPKICK:
		return ConvertKickDistToMagnitude( kick_distance, CHIP_KICK_BASE_ANGLE + CHIP_KICK_RAND_ANGLE / 2.0f );
		break;
	case KICKTYPE_SETPLAYCHIPKICK:
		return ConvertKickDistToMagnitude(kick_distance, SETPLAY_CHIP_KICK_BASE_ANGLE + SETPLAY_CHIP_KICK_RAND_ANGLE / 2.0f);
		break;
	case KICKTYPE_GRUBBERKICK:
		return ConvertKickDistToMagnitude( kick_distance, GRUBBER_KICK_BASE_ANGLE + GRUBBER_KICK_RAND_ANGLE / 2.0f );
		break;
	case KICKTYPE_DROPGOAL:
		return ConvertKickDistToMagnitude( kick_distance, DROP_GOAL_BASE_ANGLE + DROP_GOAL_RAND_ANGLE / 2.0f );
		break;
	default:
		MABBREAKMSG( "Unsupported kick type passed to RL3Ball::ConvertKickDistToMagnitude()" );
	}

	return 0.0f;
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

float ASSBall::ConvertKickDistToMagnitude( float kick_distance, float inclination_angle_degrees )
{
	// NOT USING AimHelper code anymore, so using old system.

	//// TYRONE : Use a more physics based formula - this assumes no air resistance initially
	float magnitude = MabMath::Sqrt( kick_distance * extrapolation_parameters.gravity / MabMath::Sin( 2 * MabMath::Deg2Rad( inclination_angle_degrees ) ) );
	// Then we crudely add air resistance here
	// According to this http://www.docstoc.com/docs/5331439/football-and-physics
	// Air resistance reduced distance of punts by 24-33% depending on kick distance


	// Vaughan 30/7/10: After discussions this system has been left in place.  Actual ball extrapolation uses a much more physics based air resistance formula.
	// This is just to align our expected distance to the actual achieved distance.
	// Actual testing of our code this should be in the range of 12-33%

	float test_dist = kick_distance;
	MabMath::Clamp( test_dist, MIN_LONG_PUNT_DIST, MAX_LONG_PUNT_DIST );
	const float MIN_EFFECT = 0.12f;
	const float MAX_EFFECT = 0.22f;
	float t = (test_dist - MIN_LONG_PUNT_DIST) / (MAX_LONG_PUNT_DIST - MIN_LONG_PUNT_DIST);
	float effect = MIN_EFFECT + t * (MAX_EFFECT - MIN_EFFECT);

	magnitude /= (1.0f - effect);

	return magnitude;

}

///-------------------------------------------------------------------------------
/// Pass
///-------------------------------------------------------------------------------

void ASSBall::Pass( const FVector& destination )
{
	float angle = 19.0f;

	num_post_strikes_since_airborne = 0;

	const FVector& position = this->GetCurrentPosition();
	FVector distance_vector = destination - position;
	float distance = distance_vector.Magnitude();
	distance_vector.y = 0.0f;

	float direction = destination.x < position.x ? 1.0f : -1.0f;
	float face_angle = SSMath::CalculateAngle( distance_vector );

	// Calculate total velocity required for pass
	float total_velocity = ConvertKickDistToMagnitude( distance, angle );

	// Rotate upwards
	FVector result;
	FVector up_field( 0.0f, 0.0f, 1.0f );
	MabMatrix rot_matrix = MabMatrix::RotMatrix(MabMath::Deg2Rad(-angle), 0.0f, 0.0f);
	MabMatrix::MatrixMultiply( result, up_field, rot_matrix );

	// Rotate direction
	float cos_rot = distance_vector.Unit().Dot( up_field.Unit() );

	// Make sure we go the right way
	float rad_angle = MabMath::ACos(cos_rot);
	if ( distance_vector.x < 0.0f )
	{
		rad_angle *= -1.0f;
	}

	rot_matrix = MabMatrix::RotMatrix(0.0f, rad_angle, 0.0f);
	MabMatrix::MatrixMultiply( result, result, rot_matrix );

	// Compute velocity and current rotation
	FVector velocity = result * total_velocity;
	MabQuaternion rotation( face_angle, FVector(0.0f, 1.0f, 0.0f) );

	// Compute rotation speed. Rotation axis is aligned with direction of travel, with a bit of randomness
	FVector rotation_speed = result * direction;
	rotation_speed *= 5.0f * 2.0f * PI; // Convert rotations/sec into radians

	this->SetRotationAbsolute( rotation );

	RawExtrapolation( position, velocity, rotation, rotation_speed, CalculateWindDisplacement( velocity ) );
}

///-------------------------------------------------------------------------------
/// ChipKick
///-------------------------------------------------------------------------------

void ASSBall::ChipKick( float strength_pct, float angle, float account_for_wind, bool is_simulation )
{
	MABUNUSED(account_for_wind);

	FVector angle_vec;

	// A strength a little greater than 1.0 is valid in certain conditions (team confidence boosts etc)
#ifdef ENABLE_GAME_DEBUG_MENU
	if( SIFDebug::GetKickDebugSettings()->ShouldOverrideKickValues() && SIFDebug::GetKickDebugSettings()->GetKickStrengthModifier() == 1.0f )
#endif
	{
		MABASSERT(strength_pct >= 0.0f && strength_pct <= 1.5f);
	}

	SSMath::AngleToMabVector3( angle, angle_vec );
	FVector kick_vector = angle_vec * ( MIN_CHIP_KICK_DIST + strength_pct * (MAX_CHIP_KICK_DIST - MIN_CHIP_KICK_DIST) ) + this->GetCurrentPosition();

#ifdef ENABLE_GAME_DEBUG_MENU
	RUKickDebugSettings* debug_settings = SIFDebug::GetKickDebugSettings();
	if ( debug_settings->ShouldOverrideKickValues() )
	{
		kick_vector = angle_vec * ( debug_settings->GetChipKickMinDist() + strength_pct * (debug_settings->GetChipKickMaxDist() - debug_settings->GetChipKickMinDist()) ) + this->GetCurrentPosition();
	}
#endif

	// if not simulating use a random
	float vangle = CHIP_KICK_BASE_ANGLE + 0.5f * CHIP_KICK_RAND_ANGLE;

	if ( !is_simulation )
		vangle = CHIP_KICK_BASE_ANGLE + game->GetRNG()->RAND_RANGED_CALL(float, CHIP_KICK_RAND_ANGLE );

#ifdef ENABLE_GAME_DEBUG_MENU
	vangle = debug_settings->GetBaseChipKickAngle() + 0.5f * debug_settings->GetRandChipKickAngle();

	if ( !is_simulation )
		vangle = debug_settings->GetBaseChipKickAngle() + game->GetRNG()->RAND_RANGED_CALL(float, debug_settings->GetRandChipKickAngle() );
#endif

	Kick( KICKTYPE_CHIPKICK, kick_vector, vangle, is_simulation );
}

///-------------------------------------------------------------------------------
/// SetplayChipKick
///-------------------------------------------------------------------------------

void ASSBall::SetplayChipKick(float strength_pct, float angle, float account_for_wind, bool is_simulation)
{
	MABUNUSED(account_for_wind);

	FVector angle_vec;

	// A strength a little greater than 1.0 is valid in certain conditions (team confidence boosts etc)
#ifdef ENABLE_GAME_DEBUG_MENU
	//if (SIFDebug::GetKickDebugSettings()->ShouldOverrideKickValues() && SIFDebug::GetKickDebugSettings()->GetKickStrengthModifier() == 1.0f)
#endif
	{
		MABASSERT(strength_pct >= 0.0f && strength_pct <= 1.5f);
	}

	SSMath::AngleToMabVector3(angle, angle_vec);
	FVector kick_vector = angle_vec * (MIN_SETPLAY_CHIP_KICK_DIST + strength_pct * (MAX_SETPLAY_CHIP_KICK_DIST - MIN_SETPLAY_CHIP_KICK_DIST)) + this->GetCurrentPosition();

#ifdef ENABLE_GAME_DEBUG_MENU
// 	RUKickDebugSettings* debug_settings = SIFDebug::GetKickDebugSettings();
// 	if (debug_settings->ShouldOverrideKickValues())
// 	{
// 		kick_vector = angle_vec * (debug_settings->GetSetplayChipKickMinDist() + strength_pct * (debug_settings->GetSetplayChipKickMaxDist() - debug_settings->GetSetplayChipKickMinDist())) + this->GetCurrentPosition();
// 	}
#endif

	// if not simulating use a random
	float vangle = SETPLAY_CHIP_KICK_BASE_ANGLE + 0.5f * SETPLAY_CHIP_KICK_RAND_ANGLE;

	if (!is_simulation)
		vangle = SETPLAY_CHIP_KICK_BASE_ANGLE + game->GetRNG()->RAND_RANGED_CALL(float, SETPLAY_CHIP_KICK_RAND_ANGLE);

#ifdef ENABLE_GAME_DEBUG_MENU
	//vangle = debug_settings->GetBaseChipKickAngle() + 0.5f * debug_settings->GetRandChipKickAngle();

	//if (!is_simulation)
		//vangle = debug_settings->GetBaseChipKickAngle() + game->GetRNG()->RAND_RANGED_CALL(float, debug_settings->GetRandChipKickAngle());
#endif

	Kick(KICKTYPE_SETPLAYCHIPKICK, kick_vector, vangle, is_simulation);
}

///-------------------------------------------------------------------------------
/// Kick
///-------------------------------------------------------------------------------

void ASSBall::Kick( KickType type, FVector &_destination, float angle, bool is_simulation, float apply_for_wind )
{
	MABASSERT( angle > 0.0f && angle < 90.0f );

	num_post_strikes_since_airborne = 0;

	if ( !is_simulation )
	{
		game->GetEvents()->change_to_post_kick_camera(type);
	}

	FVector destination = _destination;

	{
		FVector distance = destination - this->GetCurrentPosition();
		FVector displacement = FVector::ZeroVector;

		if (apply_for_wind) {
			// To account for wind, we choose an amount of wind displacement based on approximate height
			// that ball will get to, and subtract that displacement from the destination point.
			displacement = CalculateWindDisplacement( distance.Magnitude(), angle );

			// How much of the wind are we trying to correct for?
			displacement *= apply_for_wind;

			// Update kick destination
			destination -= displacement;

			// Inform extrapolator of the wind displacement to apply
			oval_extrapolator.SetTotalWindDisplacement( displacement );
		}

		//SIF_DEBUG_DRAW( SetText( RL3GAME_MDD_BASE + 51, 20, 312, MabString( 32, "Kick( vangle=%0.2f, afw=%0.2f, is_sim=%s, origdist=%0.1f, winddisp=%0.1f, disp=%0.1f", angle, apply_for_wind, is_simulation, distance.Magnitude(), displacement.Magnitude(), (destination - ball->GetCurrentPosition()).Magnitude() ), MabColour::White ) );
	}

	FVector distance = destination - this->GetCurrentPosition();
	distance.y = 0.0f;

	// Calculate total velocity required for kick
	float totalVelocity = ConvertKickDistToMagnitude( distance.Magnitude(), angle );
	//SIF_DEBUG_DRAW( SetText( RL3GAME_MDD_BASE + 52, 20, 324, MabString( 32, "Kick( totalvel=%0.1f )", totalVelocity ), MabColour::White ) );

	MabMatrix rotMatrix;
	FVector result;
	FVector upField(0.0f, 0.0f, 1.0f);

	// Rotate upwards
	rotMatrix = MabMatrix::RotMatrix(MabMath::Deg2Rad(-angle), 0.0f, 0.0f);
	MabMatrix::MatrixMultiply(result, upField, rotMatrix);

	// Rotate direction

	// Dot product
	float cos_rot = distance.Unit().Dot(upField.Unit());

	// Make sure we go the right way
	float rad_angle = MabMath::ACos( cos_rot );

	if ( distance.x < 0.0f )
		rad_angle *= -1.0f;

	rotMatrix = MabMatrix::RotMatrix(0.0f, rad_angle, 0.0f);
	MabMatrix::MatrixMultiply(result, result, rotMatrix);

	FVector velocity = result * totalVelocity;

	if ( is_simulation )
	{
		SaveExtrapolationState();
		SwapExtrapolationBuffers();

		if ( apply_for_wind == 0.0f )
		{
			oval_extrapolator.SetTotalWindDisplacement( CalculateWindDisplacement( velocity ) );
		}

		FVector ball_rot = KickRotationSpeed(velocity, 1.0f, type);

		//MABLOGDEBUG( "Extrapolate Velocity X: %f, Y: %f, Z: %f", velocity.x, velocity.y, velocity.z );
		//MABLOGDEBUG( "Rotation X: %f, Y: %f, Z: %f", ball_rot.x, ball_rot.y, ball_rot.z );

#ifdef ENABLE_GAME_DEBUG_MENU
		if ( SIFDebug::GetKickDebugSettings()->ShouldOverrideKickValues() && SIFDebug::GetKickDebugSettings()->GetKicksIgnoreWind() )
		{
			oval_extrapolator.SetTotalWindDisplacement( FVector::ZeroVector );
		}
#endif

		oval_extrapolator.SetExtrapolationParameters( extrapolation_parameters );
		oval_extrapolator.Extrapolate( extrapolation_buffer, BALL_EXTRAPOLATION_SIZE, this->GetCurrentPosition(), velocity, pre_kick_rotation, ball_rot );

		SwapExtrapolationBuffers();
		RestoreExtrapolationState();
	}
	else
	{
		if (game->GetGameState()->GetBallHolder() != GetHolder())
		{
			UE_LOG(LogTemp, Display, TEXT("ASSBall::Kick: GameState Ball Holder (%s) !=  Ball Holder (%s)"),
				game->GetGameState()->GetBallHolder() ? *game->GetGameState()->GetBallHolder()->GetName() : TEXT("NULL"),
				GetHolder() ? *GetHolder()->GetName() : TEXT("NULL"));
		}

		// Ensure the ball is not attached
		if (game->GetGameState()->GetBallHolder() != NULL)
		{
			game->GetGameState()->SetBallHolder(NULL);
		}

		ball_not_in_play = false;

#ifdef ENABLE_GAME_DEBUG_MENU
		if ( SIFDebug::GetKickDebugSettings()->ShouldOverrideKickValues() && SIFDebug::GetKickDebugSettings()->GetKicksIgnoreWind() )
		{
			RawExtrapolation( this->GetCurrentPosition(), velocity, pre_kick_rotation, KickRotationSpeed( velocity, 1.0f, type ), FVector::ZeroVector );
		}
		else
		{
			RawExtrapolation( this->GetCurrentPosition(), velocity, pre_kick_rotation, KickRotationSpeed( velocity, 1.0f, type ), CalculateWindDisplacement( velocity ) );
		}
#else
		RawExtrapolation( this->GetCurrentPosition(), velocity, pre_kick_rotation, KickRotationSpeed( velocity, 1.0f, type ), CalculateWindDisplacement( velocity ) );
#endif

		FVector pos;
		ASSBall::BallThroughPostQueryResult query_result;
		if(type == KICKTYPE_DROPGOAL && !WillBallGoOverPostsOnTheFull(pos, query_result))
		{
			game->GetEvents()->change_to_post_kick_camera(KICKTYPE_UPANDUNDER);
		}
	}
}

///-------------------------------------------------------------------------------
///	LineOutThrowIn - Base on place kick, (Temporary - Rob.H)
///-------------------------------------------------------------------------------
void ASSBall::LineOutThrowIn(float strength_pct, float angle )
{
	FVector angle_vec( FVector::ZeroVector );
	// A strength a little greater than 1.0 is valid in certain conditions (team confidence boosts etc)
#ifdef ENABLE_GAME_DEBUG_MENU
	if( SIFDebug::GetKickDebugSettings()->ShouldOverrideKickValues() && SIFDebug::GetKickDebugSettings()->GetKickStrengthModifier() == 1.0f )
#endif
	{
		MABASSERT(strength_pct >= 0.0f && strength_pct <= 1.5f);
	}
	SSMath::AngleToMabVector3( angle, angle_vec );
	FVector kick_vector =  angle_vec * ( 5.0f + strength_pct * (20.0f - 5.0f) ) + this->GetCurrentPosition();

	float vangle = 25.0f;
	Kick( KICKTYPE_PLACEKICK, kick_vector, vangle, false );
}

//-------------------------------------------------------------------------------
const float MAX_DISTANCE_THROWIN = 13.f;
const float MIN_ANGLE_THROWIN	 = 17.f;
const float FACTOR_ANGLE		 = 2.f;
const float MIN_HEIGHT_BALL		 = 3.f;

#ifdef BUILD_DEBUG
const float LONG_PASS_ANGLE		 = 22.5f;
#else
const float LONG_PASS_ANGLE		 = 27.f;
#endif

///-------------------------------------------------------------------------------
/// Lineout throwing given a position
///-------------------------------------------------------------------------------
void ASSBall::LineOutThrowIn (const FVector& destination, float strength )
{
	MABLOGDEBUG("ASSBall::LineOutThrowIn to %.2f, %.2f, %.2f, at strength %.2f", destination.x, destination.y, destination.z, strength);
	FVector current_position = this->GetCurrentPosition();
	FVector kick_vector =  destination - current_position;
	float magnitude = kick_vector.Magnitude();
	float vangle = MIN_ANGLE_THROWIN + FACTOR_ANGLE * (MAX_DISTANCE_THROWIN - magnitude);

	// Set up the throw in angle and strength
	kick_vector.Normalise();
	kick_vector *= magnitude * strength;
	kick_vector += current_position;

	// Setup the rotation angle
	pre_kick_rotation.x = pre_kick_rotation.y = pre_kick_rotation.z = pre_kick_rotation.w = 1.f;
	pre_kick_rotation.z = 0.8f;

	// Depending on the height of the launch we need to change the angle
	float extra_angle = (destination.y - MIN_HEIGHT_BALL) * 10.f;
	vangle += extra_angle;

	// Just for debug: Add more angle to compensate the difference of speed
#ifdef BUILD_DEBUG
	vangle += 3.f + (magnitude < 8.f ? 8.f : 5.5f);
#endif

	// For long throws, we need to set the angle manually
	if ( destination.y <= LINEOUT_LONG_THROW_HEIGHT )
	{
		vangle = LONG_PASS_ANGLE;
	}

	Kick (KICKTYPE_THROWIN, kick_vector, vangle, false);
}

void ASSBall::LineOutQuickThrowIn (const FVector& destination, float strength )
{
	float vangle = 80.0f;
	const FVector& current_position = this->GetCurrentPosition();
	FVector kick_vector =  destination - current_position;
	float magnitude = kick_vector.Magnitude();

	kick_vector.Normalise();
	kick_vector *= magnitude * strength;
	kick_vector += current_position;

	Kick( KICKTYPE_THROWIN, kick_vector, vangle, false, 1.0f );
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------
void ASSBall::PlaceKickStrengthAngle( float strength_pct, float angle, float /*kick_ability*/ )
{
	FVector angle_vec( FVector::ZeroVector );
	// A strength a little greater than 1.0 is valid in certain conditions (team confidence boosts etc)
#ifdef ENABLE_GAME_DEBUG_MENU
	if( SIFDebug::GetKickDebugSettings()->ShouldOverrideKickValues() && SIFDebug::GetKickDebugSettings()->GetKickStrengthModifier() == 1.0f )
#endif
	{
		MABASSERT(strength_pct >= 0.0f && strength_pct <= 1.5f);
	}
	SSMath::AngleToMabVector3( angle, angle_vec );

	// take into account the players ability to give them a little extra distance boost!
	/*const float MIN_BOOST = 1.0f;
	const float MAX_BOOST = 1.5f;
	float dist_boost = MabMath::Lerp(MIN_BOOST, MAX_BOOST, kick_ability);
	MABLOGDEBUG("Player kick stat is %.2f, so give a distance boost of %.2f", kick_ability, dist_boost);
	MABUNUSED(dist_boost);

	strength_pct *= dist_boost;*/

	FVector kick_vector =  angle_vec * ( MIN_PLACE_KICK_DIST + strength_pct * (MAX_PLACE_KICK_DIST - MIN_PLACE_KICK_DIST) ) + this->GetCurrentPosition( true );

#ifdef ENABLE_GAME_DEBUG_MENU
	RUKickDebugSettings* debug_settings = SIFDebug::GetKickDebugSettings();
	if ( debug_settings->ShouldOverrideKickValues() )
	{
		kick_vector =  angle_vec * ( debug_settings->GetPlaceKickMinDist() + strength_pct * (debug_settings->GetPlaceKickMaxDist() - debug_settings->GetPlaceKickMinDist()) ) + this->GetCurrentPosition();
	}
#endif

	float vangle = 0.0f;

	// Nick WWS 7s to Womens //
	//if( game->GetGameSettings().game_settings.GameModeIsR13() )
	//{
		vangle = PLACE_KICK_BASE_ANGLE + game->GetRNG()->RAND_RANGED_CALL(float, PLACE_KICK_RAND_ANGLE );
	//}
	//else
	//{
	//	vangle = PLACE_KICK_BASE_ANGLE + game->GetRNG()->RAND_RANGED_CALL(float, PLACE_KICK_RAND_ANGLE );
	//}

#ifdef ENABLE_GAME_DEBUG_MENU
	if ( debug_settings->ShouldOverrideKickValues() )
	{
		vangle = debug_settings->GetBasePlaceKickAngle() + game->GetRNG()->RAND_RANGED_CALL(float, debug_settings->GetRandPlaceKickAngle() );
	}
#endif

	Kick (KICKTYPE_PLACEKICK, kick_vector, vangle, false);
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------
void ASSBall::KickOffKick( float strength_pct, float angle )
{
	FVector angle_vec( FVector::ZeroVector );
	// A strength a little greater than 1.0 is valid in certain conditions (team confidence boosts etc)

#ifdef ENABLE_GAME_DEBUG_MENU
	if( SIFDebug::GetKickDebugSettings()->ShouldOverrideKickValues() && SIFDebug::GetKickDebugSettings()->GetKickStrengthModifier() == 1.0f )
#endif
	{
		MABASSERT(strength_pct >= 0.0f && strength_pct <= 1.5f);
	}

	SSMath::AngleToMabVector3(angle, angle_vec);
	FVector kick_vector =  angle_vec * ( MIN_KICKOFF_KICK_DIST + strength_pct * (MAX_KICKOFF_KICK_DIST - MIN_KICKOFF_KICK_DIST) ) + this->GetCurrentPosition();

#ifdef ENABLE_GAME_DEBUG_MENU
	RUKickDebugSettings* debug_settings = SIFDebug::GetKickDebugSettings();
	if ( debug_settings->ShouldOverrideKickValues() )
	{
		kick_vector =  angle_vec * ( debug_settings->GetKickoffKickMinDist() + strength_pct * (debug_settings->GetKickoffKickMaxDist() - debug_settings->GetKickoffKickMinDist()) ) + this->GetCurrentPosition();
	}
#endif

	float vangle = KICKOFF_KICK_BASE_ANGLE + game->GetRNG()->RAND_RANGED_CALL(float, KICKOFF_KICK_RAND_ANGLE );

#ifdef ENABLE_GAME_DEBUG_MENU
	vangle = debug_settings->GetBaseKickoffKickAngle() + game->GetRNG()->RAND_RANGED_CALL(float, debug_settings->GetRandKickoffKickAngle() );
#endif

	Kick( KICKTYPE_KICKOFF, kick_vector, vangle, false );
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::FreeBallKick( float strength_pct, float angle, bool is_simulation )
{
	FVector angle_vec;
	SSMath::AngleToMabVector3( angle, angle_vec );
	// A strength a little greater than 1.0 is valid in certain conditions (team confidence boosts etc)

#ifdef ENABLE_GAME_DEBUG_MENU
	if( SIFDebug::GetKickDebugSettings()->ShouldOverrideKickValues() && SIFDebug::GetKickDebugSettings()->GetKickStrengthModifier() == 1.0f )
#endif
	{
		MABASSERT(strength_pct >= 0.0f && strength_pct <= 1.5f);
	}

	FVector kick_vector = angle_vec * ( MIN_FREE_BALL_KICK_DIST + strength_pct * (MAX_FREE_BALL_KICK_DIST - MIN_FREE_BALL_KICK_DIST) );

#ifdef ENABLE_GAME_DEBUG_MENU
	RUKickDebugSettings* debug_settings = SIFDebug::GetKickDebugSettings();
	if ( debug_settings->ShouldOverrideKickValues() )
	{
		kick_vector = angle_vec * ( debug_settings->GetFreeKickMinDist() + strength_pct * (debug_settings->GetFreeKickMaxDist() - debug_settings->GetFreeKickMinDist()) );
	}
#endif

	float elevation_angle = FREE_BALL_KICK_BASE_ANGLE + game->GetRNG()->RAND_RANGED_CALL(float, FREE_BALL_KICK_RAND_ANGLE );

#ifdef ENABLE_GAME_DEBUG_MENU
	elevation_angle = debug_settings->GetBaseFreeBallKickAngle() + game->GetRNG()->RAND_RANGED_CALL(float, debug_settings->GetRandFreeBallKickAngle() );
#endif

	elevation_angle = MabMath::Deg2Rad(elevation_angle);

	MabMatrix rotMatrix;
	rotMatrix = MabMatrix::RotMatrix(-elevation_angle, 0.0f, 0.0f);
	FVector result;
	MabMatrix::MatrixMultiply(result, kick_vector, rotMatrix);

	KickSimple( KICKTYPE_FREEBALLKICK, result, is_simulation );
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::GrubberKick( float strength_pct, float angle, bool is_simulation )
{
	FVector angle_vec;

	// A strength a little greater than 1.0 is valid in certain conditions (team confidence boosts etc)
#ifdef ENABLE_GAME_DEBUG_MENU
	if( SIFDebug::GetKickDebugSettings()->ShouldOverrideKickValues() && SIFDebug::GetKickDebugSettings()->GetKickStrengthModifier() == 1.0f )
#endif
	{
		MABASSERT(strength_pct >= 0.0f && strength_pct <= 1.5f);
	}
	SSMath::AngleToMabVector3( angle, angle_vec );
	// half grubber kicks as they are way too powerful
	strength_pct *= 0.5f;

	FVector kick_vector = angle_vec * ( MIN_GRUBBER_KICK_DIST + strength_pct * (MAX_GRUBBER_KICK_DIST - MIN_GRUBBER_KICK_DIST) );

#ifdef ENABLE_GAME_DEBUG_MENU
	RUKickDebugSettings* debug_settings = SIFDebug::GetKickDebugSettings();
	if ( debug_settings->ShouldOverrideKickValues() )
	{
		kick_vector = angle_vec * ( debug_settings->GetGrubberKickMinDist() + strength_pct * (debug_settings->GetGrubberKickMaxDist() - debug_settings->GetGrubberKickMinDist()) );
	}
#endif

	float elevation_angle = GRUBBER_KICK_BASE_ANGLE + game->GetRNG()->RAND_RANGED_CALL(float, GRUBBER_KICK_RAND_ANGLE );

#ifdef ENABLE_GAME_DEBUG_MENU
	elevation_angle = debug_settings->GetBaseGrubberKickAngle() + game->GetRNG()->RAND_RANGED_CALL(float, debug_settings->GetRandGrubberKickAngle() );
#endif

	elevation_angle = MabMath::Deg2Rad(elevation_angle);

	MabMatrix rotMatrix;
	rotMatrix = MabMatrix::RotMatrix(elevation_angle, 0.0f, 0.0f);
	FVector result;
	MabMatrix::MatrixMultiply(result, kick_vector, rotMatrix);

	KickSimple( KICKTYPE_GRUBBERKICK, result, is_simulation );
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::UpAnUnderKick( float strength_pct, float angle, float account_for_wind, bool is_simulation )
{
	FVector angle_vec;

	// A strength a little greater than 1.0 is valid in certain conditions (team confidence boosts etc)
#ifdef ENABLE_GAME_DEBUG_MENU
	if( SIFDebug::GetKickDebugSettings()->ShouldOverrideKickValues() && SIFDebug::GetKickDebugSettings()->GetKickStrengthModifier() == 1.0f )
#endif
	{
		MABASSERT(strength_pct >= 0.0f && strength_pct <= 1.5f);
	}
	SSMath::AngleToMabVector3( angle, angle_vec );
	FVector kick_vector = angle_vec * ( MIN_UP_AND_UNDER_DIST + strength_pct * (MAX_UP_AND_UNDER_DIST - MIN_UP_AND_UNDER_DIST) ) + GetCurrentPosition();

#ifdef ENABLE_GAME_DEBUG_MENU
	RUKickDebugSettings* debug_settings = SIFDebug::GetKickDebugSettings();
	if ( debug_settings->ShouldOverrideKickValues() )
	{
		kick_vector = angle_vec * ( debug_settings->GetUpAndUnderKickMinDist() + strength_pct * (debug_settings->GetUpAndUnderKickMaxDist() - debug_settings->GetUpAndUnderKickMinDist()) ) + GetCurrentPosition();
	}
#endif


	// if not simulating use a random
	float vangle = UP_AND_UNDER_BASE_ANGLE + 0.5f * UP_AND_UNDER_RAND_ANGLE;

	if ( !is_simulation )
		vangle = UP_AND_UNDER_BASE_ANGLE + game->GetRNG()->RAND_RANGED_CALL(float, UP_AND_UNDER_RAND_ANGLE );

#ifdef ENABLE_GAME_DEBUG_MENU
	vangle = debug_settings->GetBaseUpAndUnderAngle() + 0.5f * debug_settings->GetRandUpAndUnderAngle();

	if ( !is_simulation )
		vangle = debug_settings->GetBaseUpAndUnderAngle() + game->GetRNG()->RAND_RANGED_CALL(float, debug_settings->GetRandUpAndUnderAngle() );
#endif

	Kick( KICKTYPE_UPANDUNDER, kick_vector, vangle, is_simulation, account_for_wind);
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::BoxKick( float strength_pct, float angle, float account_for_wind, bool is_simulation )
{
	FVector angle_vec;

	// A strength a little greater than 1.0 is valid in certain conditions (team confidence boosts etc)
#ifdef ENABLE_GAME_DEBUG_MENU
	if( SIFDebug::GetKickDebugSettings()->ShouldOverrideKickValues() && SIFDebug::GetKickDebugSettings()->GetKickStrengthModifier() == 1.0f )
#endif
	{
		MABASSERT(strength_pct >= 0.0f && strength_pct <= 1.5f);
	}
	SSMath::AngleToMabVector3( angle, angle_vec );
	FVector kick_vector = angle_vec * ( MIN_BOX_KICK_DIST + strength_pct * (MAX_BOX_KICK_DIST - MIN_BOX_KICK_DIST) ) + GetCurrentPosition();

#ifdef ENABLE_GAME_DEBUG_MENU
	RUKickDebugSettings* debug_settings = SIFDebug::GetKickDebugSettings();
	if ( debug_settings->ShouldOverrideKickValues() )
	{
		kick_vector = angle_vec * ( debug_settings->GetBoxKickMinDist() + strength_pct * (debug_settings->GetBoxKickMaxDist() - debug_settings->GetBoxKickMinDist()) ) + GetCurrentPosition();
	}
#endif

	// if not simulating use a random
	float vangle = BOX_KICK_BASE_ANGLE + 0.5f * BOX_KICK_RAND_ANGLE;

	if ( !is_simulation )
		vangle = BOX_KICK_BASE_ANGLE + game->GetRNG()->RAND_RANGED_CALL(float, BOX_KICK_RAND_ANGLE );

#ifdef ENABLE_GAME_DEBUG_MENU
	vangle = debug_settings->GetBaseBoxKickAngle() + 0.5f * debug_settings->GetRandBoxKickAngle();

	if ( !is_simulation )
		vangle = debug_settings->GetBaseBoxKickAngle() + game->GetRNG()->RAND_RANGED_CALL(float, debug_settings->GetRandBoxKickAngle() );
#endif

	Kick( KICKTYPE_BOXKICK, kick_vector, vangle, is_simulation, account_for_wind);
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::LongPuntKick( float strength_pct, float angle, float account_for_wind, bool is_simulation )
{
	//MABLOGDEBUG("Punt: str %f, ang %f, wind %f", strength_pct, angle, account_for_wind );
	FVector angle_vec;

	// A strength a little greater than 1.0 is valid in certain conditions (team confidence boosts etc)
#ifdef ENABLE_GAME_DEBUG_MENU
	if( SIFDebug::GetKickDebugSettings()->ShouldOverrideKickValues() && SIFDebug::GetKickDebugSettings()->GetKickStrengthModifier() == 1.0f )
#endif
	{
		MABASSERT(strength_pct >= 0.0f && strength_pct <= 1.5f);
	}
	SSMath::AngleToMabVector3( angle, angle_vec );
	float dist = ( MIN_LONG_PUNT_DIST + strength_pct * (MAX_LONG_PUNT_DIST - MIN_LONG_PUNT_DIST) );
	MabMath::ClampUpper( dist, MAX_LONG_PUNT_DIST );

#ifdef ENABLE_GAME_DEBUG_MENU
	RUKickDebugSettings* debug_settings = SIFDebug::GetKickDebugSettings();
	if ( debug_settings->ShouldOverrideKickValues() )
	{
		dist = ( debug_settings->GetLongPuntKickMinDist() + strength_pct * (debug_settings->GetLongPuntKickMaxDist() - debug_settings->GetLongPuntKickMinDist()) );
		MabMath::ClampUpper( dist, debug_settings->GetLongPuntKickMaxDist() );
	}
#endif

	FVector kick_vector = angle_vec * dist + GetCurrentPosition();

	// if not simulating use a random
	float vangle = LONG_PUNT_BASE_ANGLE + 0.5f * LONG_PUNT_RAND_ANGLE;

	if ( !is_simulation )
		vangle = LONG_PUNT_BASE_ANGLE + game->GetRNG()->RAND_RANGED_CALL(float, LONG_PUNT_RAND_ANGLE );

#ifdef ENABLE_GAME_DEBUG_MENU
	vangle = debug_settings->GetBaseLongPuntAngle() + 0.5f * debug_settings->GetRandLongPuntAngle();

	if ( !is_simulation )
		vangle = debug_settings->GetBaseLongPuntAngle() + game->GetRNG()->RAND_RANGED_CALL(float, debug_settings->GetRandLongPuntAngle() );
#endif

#ifdef ENABLE_LONG_PUNT_KICK_DEBUG
	SIF_DEBUG_DRAW( SetText( RL3GAME_MDD_BASE + 50, 20, 300, MabString( 32, "LongPuntKick( strength=%0.2f, angle=%0.2f, afw=%0.2f, is_sim=%s, vangle=%0.2f", strength_pct, angle, account_for_wind, is_simulation, vangle ), MabColour::White ) );
#endif

	//MABLOGDEBUG( "KickVector X: %f, Y:%f, Z: %f, VAngle: %f ", kick_vector.x, kick_vector.y, kick_vector.z, vangle );
	Kick( KICKTYPE_LONGPUNT, kick_vector , vangle, is_simulation, account_for_wind );
}

///-------------------------------------------------------------------------------
/// Used to calculate where a punt will land for displaying the punt target
///-------------------------------------------------------------------------------
void ASSBall::CalculateKickTarget(KickType type, float strength_pct, float angle, FVector &target)
{
	MABLOGDEBUG( "Calc Kick Target Str: %f, Ang: %f", strength_pct, angle );
	// simulate the kick
	switch(type)
	{
	case KICKTYPE_UPANDUNDER:	UpAnUnderKick( strength_pct, angle, false, true ); break;
	case KICKTYPE_PENALTYPUNT:
	case KICKTYPE_LONGPUNT:		LongPuntKick( strength_pct, angle, false, true ); break;
	case KICKTYPE_CHIPKICK:		ChipKick( strength_pct, angle, false, true ); break;
	case KICKTYPE_SETPLAYCHIPKICK:		SetplayChipKick( strength_pct, angle, false, true ); break;
	case KICKTYPE_GRUBBERKICK:	GrubberKick( strength_pct, angle, true ); break;
	case KICKTYPE_DROPGOAL:		DropGoal( strength_pct, angle, false, true ); break;
	case KICKTYPE_BOXKICK:		BoxKick( strength_pct, angle, false, true ); break;
	case KICKTYPE_DROPKICK:		DropGoal( strength_pct, angle, false, true ); break;
	case KICKTYPE_KICKOFF:		DropGoal( strength_pct, angle, false, true ); break;
	case KICKTYPE_FREEKICK:		LongPuntKick( strength_pct, angle, false, true ); break;
	case KICKTYPE_PLACEKICK:
	case KICKTYPE_FREEBALLKICK:
	default:
		{
			// Kick is unknown or not currently supported by this function
			MABBREAKMSG("unknown kicktype made");
			return;
		}
	}

	// swap to the tempory buffer
	SaveExtrapolationState();
	SwapExtrapolationBuffers();

	float time;
	GetBouncePosition( 1, target, time );

	// swap buffers back
	SwapExtrapolationBuffers();
	RestoreExtrapolationState();
}


///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::DropGoal( float strength_pct, float angle, float account_for_wind, bool is_simulation )
{
	MABUNUSED(account_for_wind);

	FVector angle_vec;

	// A strength a little greater than 1.0 is valid in certain conditions (team confidence boosts etc)
#ifdef ENABLE_GAME_DEBUG_MENU
	if( SIFDebug::GetKickDebugSettings()->ShouldOverrideKickValues() && SIFDebug::GetKickDebugSettings()->GetKickStrengthModifier() == 1.0f )
#endif
	{
		MABASSERT(strength_pct >= 0.0f && strength_pct <= 1.5f);
	}
	SSMath::AngleToMabVector3( angle, angle_vec );
	FVector kick_vector =  angle_vec * ( MIN_DROP_GOAL_DIST + strength_pct * (MAX_DROP_GOAL_DIST - MIN_DROP_GOAL_DIST) ) + GetCurrentPosition();

#ifdef ENABLE_GAME_DEBUG_MENU
	RUKickDebugSettings* debug_settings = SIFDebug::GetKickDebugSettings();
	if ( debug_settings->ShouldOverrideKickValues() )
	{
		kick_vector =  angle_vec * ( debug_settings->GetDropGoalKickMinDist() + strength_pct * (debug_settings->GetDropGoalKickMaxDist() - debug_settings->GetDropGoalKickMinDist()) ) + GetCurrentPosition();
	}
#endif

	// if not simulating use a random
	float vangle = DROP_GOAL_BASE_ANGLE + 0.5f * DROP_GOAL_RAND_ANGLE;

	if ( !is_simulation )
		vangle = DROP_GOAL_BASE_ANGLE + game->GetRNG()->RAND_RANGED_CALL(float, DROP_GOAL_RAND_ANGLE );

#ifdef ENABLE_GAME_DEBUG_MENU
	vangle = debug_settings->GetBaseDropGoalAngle() + 0.5f * debug_settings->GetRandDropGoalAngle();

	if ( !is_simulation )
		vangle = debug_settings->GetBaseDropGoalAngle() + game->GetRNG()->RAND_RANGED_CALL(float, debug_settings->GetRandDropGoalAngle() );
#endif

	Kick( KICKTYPE_DROPGOAL, kick_vector, vangle, is_simulation );
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void ASSBall::DropKick( float strength_pct, float angle )
{
	FVector angle_vec;
	// A strength a little greater than 1.0 is valid in certain conditions (team confidence boosts etc)
#ifdef ENABLE_GAME_DEBUG_MENU
	if( SIFDebug::GetKickDebugSettings()->ShouldOverrideKickValues() && SIFDebug::GetKickDebugSettings()->GetKickStrengthModifier() == 1.0f )
#endif
	{
		MABASSERT(strength_pct >= 0.0f && strength_pct <= 1.5f);
	}
	SSMath::AngleToMabVector3( angle, angle_vec );
	FVector kick_vector =  angle_vec * ( MIN_DROP_KICK_DIST + strength_pct * (MAX_DROP_KICK_DIST - MIN_DROP_KICK_DIST) ) + GetCurrentPosition();

#ifdef ENABLE_GAME_DEBUG_MENU
	RUKickDebugSettings* debug_settings = SIFDebug::GetKickDebugSettings();
	if ( debug_settings->ShouldOverrideKickValues() )
	{
		kick_vector =  angle_vec * ( debug_settings->GetDropKickMinDist() + strength_pct * (debug_settings->GetDropKickMaxDist() - debug_settings->GetDropKickMinDist()) ) + GetCurrentPosition();
	}
#endif

	float vangle = DROP_KICK_BASE_ANGLE + game->GetRNG()->RAND_RANGED_CALL(float, DROP_KICK_RAND_ANGLE );

#ifdef ENABLE_GAME_DEBUG_MENU
	vangle = debug_settings->GetBaseDropKickAngle() + game->GetRNG()->RAND_RANGED_CALL(float, debug_settings->GetRandDropKickAngle() );
#endif

	Kick( KICKTYPE_DROPKICK, kick_vector, vangle, false );
}


void ASSBall::UpdateDebugDraw()
{	
#if PLATFORM_WINDOWS && !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
	{
		if (CVarGetBallFreeInfo.GetValueOnAnyThread() != 0 || FParse::Param(FCommandLine::Get(), TEXT("GetBallFreeInfo")))
		{
			if (root_node && num_extrapolation_elements > 0)
			{
				SSBallExtrapolationNode* node = root_node;
				SSBallExtrapolationNode* node_end = root_node + num_extrapolation_elements - 1;
				while (node != node_end && node->active)
				{
					FVector position = (node + 0)->position;
					FVector next_position = (node + 1)->position;
					FColor used_col(0.0f* 255.0f, 0.7f *255.0f, 0.7f * 255.0f, 1.0f* 255.0f);
					MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(position, newVector1);
					MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(next_position, newVector2);
					DrawDebugLine(game->GetGameInstance().GetWorld(), newVector1, newVector2, used_col);// , false, 0.03f);
					//SIF_DEBUG_DRAW( Set3DLine((long) node + 0, position, next_position, MabColour(0.0f, 0.7f, 0.7f, 1.0f), MabColour(0.0f, 0.7f, 0.7f, 1.0f)) );

					position.y = 0.0f;
					next_position.y = 0.0f;
					used_col = FColor(0.0f* 255.0f, 0.3f* 255.0f, 0.3f* 255.0f, 1.0f* 255.0f);
					MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(position, newVector3);
					MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(next_position, newVector4);
					DrawDebugLine(game->GetGameInstance().GetWorld(), newVector3, newVector4, used_col);// , false, 0.03f);
					//SIF_DEBUG_DRAW( Set3DLine((long) node + 1, position, next_position, MabColour(0.0f, 0.3f, 0.3f, 1.0f), MabColour(0.0f, 0.3f, 0.3f, 1.0f)) );

					++node;
				}
			}
		}
	}
#endif
}

//void ASSBall::UpdateDebugDraw()
//{
////#ifdef ENABLE_GAME_DEBUG_MENU
//	//if(SIFDebug::GetGameDebugSettings()->GetBallFreeInfo() == true)
////#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
////	if (CVarGetBallFreeInfo.GetValueOnAnyThread() != 0 || FParse::Param(FCommandLine::Get(), TEXT("GetBallFreeInfo")))
////	{
////		const BallFreeInfo& bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
////		FVector position = GetCurrentPosition();
////		DrawDebugString(game->GetGameInstance().GetWorld(), position, bfi.IsBallLoose() ? FString("Ball is loose") : FString("Ball is not loose"), NULL, FColor::White, 0.03f);
////
////		const char* kick_type_strings[] =
////		{
////			"KICKTYPE_NONE",
////			"KICKTYPE_UPANDUNDER",
////			"KICKTYPE_LONGPUNT",
////			"KICKTYPE_CHIPKICK",
////			"KICKTYPE_GRUBBERKICK",
////			"KICKTYPE_DROPGOAL",
////			"KICKTYPE_PLACEKICK",
////			"KICKTYPE_FREEBALLKICK",
////			"KICKTYPE_DROPKICK",
////			"KICKTYPE_KICKOFF",
////			"KICKTYPE_FREEKICK",
////			"KICKTYPE_BOXKICK"
////		};
////
////		position.y += 0.2f;
////		if (bfi.sub_type < KICKTYPE_MAX && bfi.sub_type >= 0)
////		{
////			FString kicktypemessage = FString::Printf(TEXT("Last Kick Type: %s"), ANSI_TO_TCHAR(kick_type_strings[bfi.sub_type]));
////			DrawDebugString(game->GetGameInstance().GetWorld(), position, kicktypemessage, NULL, FColor::White, 0.03f);
////		}
////	}
////#endif
////#endif
//}


/// Creates a particle system at the specified location.
void ASSBall::CreateEffect( const FVector& location, const char *particle_effect )
{
#if 0 //#rc3_legacy
	// Access the particle system
	SIFEffectSystem *effect_system = GetGame()->GetEffectSystem();
	if( effect_system )
	{
		//effect_system->InstanceEffect<FVector>( particle_effect, continuous, location );
		effect_system->InstanceEffect( particle_effect, NULL, location );
	}
#endif
}

#pragma optimize("", off)
void ASSBall::PropelBall( float angle, float move_distance, bool force_upwards, bool setbh_null )
{	
	num_post_strikes_since_airborne = 0;

	// Below threshold down, above threshold up, within random.
	const float threshold_for_down = 3.1f;
	const float threshold_for_up = 6.0f;
	float check_up = (move_distance - threshold_for_down) / (threshold_for_up - threshold_for_down);
	MABASSERT( threshold_for_up > threshold_for_down );

	bool going_up = force_upwards || GetGame()->GetRNG()->RAND_CALL(float) < check_up;
	FVector distance = FVector( 0.0f, -GetCurrentPosition().y , move_distance );
	//distance.y = 0.0f;

	// Calculate total speed required to land it where you need
	float speed = going_up ? ConvertKickDistToMagnitude( distance.Magnitude(), 28.0f ) : 5.0f + distance.Magnitude() * 2.0f;
	FVector kick_vector( FVector::ZeroVector );
	SSMath::AngleToMabVector3( angle, kick_vector );

	float elevation_angle = MabMath::Deg2Rad(going_up ? -28.0f : 0.0f);
	MabMatrix rotation = MabMatrix::RotMatrix( elevation_angle, 0.0f, 0.0f ) * MabMatrix::RotMatrix( 0.0f, angle, 0.0f );
	FVector direction = rotation.TransformPos( FVector(0.0f, 0.0f, 1.0f) );
	FVector velocity = speed * direction;

	// Ensure the ball is not attached
	if ( game->GetGameState()->GetBallHolder() && setbh_null )
	{
		game->GetGameState()->SetBallHolder( NULL );
	}

	RawExtrapolation( GetCurrentPosition(), velocity, GetCurrentRotation(), KickRotationSpeed(velocity, 0.3f, KICKTYPE_NONE) / 4.0f, CalculateWindDisplacement(velocity) );
}
#pragma optimize("", on)

/// Pre kick rotation gets the rotation based on the holders skeleton
void ASSBall::GetPreKickRotation(MabQuaternion &rot)
{
	if ( ball_holder != NULL )
	{
		BoneIndex index = ball_holder->FindBone("ball");
		MABASSERT( index != BoneIndex(-1) );
		if ( index != BoneIndex(-1) )
		{
			rot.FromMatrix(ball_holder->GetBoneWorldTransform(index));
		}
	}
}

void ASSBall::SetupBallDropRotation( float kick_angle, float strength, KickType kick_type, MabQuaternion& final_pre_rotation )
{
	FVector perfect_rotation(0, 0, 0);

	switch ( kick_type )
	{
	case KICKTYPE_UPANDUNDER:	perfect_rotation = ASSBall::rotation_up_and_under;	break;
	case KICKTYPE_PENALTYPUNT:
	case KICKTYPE_FOURTYTWENTYKICK:
	case KICKTYPE_TWENTYFOURTYKICK:
	case KICKTYPE_LONGPUNT:		perfect_rotation = ASSBall::rotation_long_punt;		break;
	case KICKTYPE_CHIPKICK:		perfect_rotation = ASSBall::rotation_chip;			break;
	case KICKTYPE_SETPLAYCHIPKICK:		perfect_rotation = ASSBall::rotation_chip;			break;
	case KICKTYPE_GRUBBERKICK:	perfect_rotation = ASSBall::rotation_grubber;		break;
	case KICKTYPE_DROPGOAL:		perfect_rotation = ASSBall::rotation_drop_kick;		break;
	case KICKTYPE_PLACEKICK:	perfect_rotation = ASSBall::rotation_place_kick;		break;
	case KICKTYPE_FREEBALLKICK: break;
	case KICKTYPE_FREEKICK:		perfect_rotation = ASSBall::rotation_long_punt;		break;
	case KICKTYPE_KICKOFF:		perfect_rotation = ASSBall::rotation_drop_kick;		break;
	case KICKTYPE_DROPKICK:		perfect_rotation = ASSBall::rotation_drop_kick;		break;
	case KICKTYPE_BOXKICK:		perfect_rotation = ASSBall::rotation_box_kick;		break;
	default:
		MABBREAKMSG( "Bad kick type" );
	}

	// get perfect final rotation
	perfect_rotation.y += kick_angle;

	MabQuaternion perfect_final_pre_rotation = MabQuaternion::EulerToQuaternion( perfect_rotation.x, perfect_rotation.y, perfect_rotation.z );

	// Estimate worst_case rotation (this uses long punt calculation to determine kick direction) will be slightly different for other kicks but close enough
	FVector angle_vec;
	SSMath::AngleToMabVector3( kick_angle, angle_vec );

	float dist = ( MIN_LONG_PUNT_DIST + strength * (MAX_LONG_PUNT_DIST - MIN_LONG_PUNT_DIST) );
	MabMath::ClampUpper( dist, MAX_LONG_PUNT_DIST );

	FVector kick_vector = angle_vec * dist + game->GetBall()->GetCurrentPosition();

	FVector perfect_ball_axis = perfect_final_pre_rotation.Transform( FVector(1,0,0));
	FVector worst_case_kick = kick_vector.Cross(perfect_ball_axis);

	// Find actual pre kick rotation for this kick
	// Vaughan TODO 30/7/10: calculate amendments to rotation based on player pressure / emotion etc not just ability
	MabQuaternion bad_rotation = MabQuaternion::EulerToQuaternion(worst_case_kick.x, worst_case_kick.y, worst_case_kick.z);
	float ability = 0.7f; // Scrum halfs use this for box kicks as they haven't picked up the ball yet.

	if ( kick_type == KICKTYPE_PLACEKICK )
		ability = 1.0f;
	else if ( game->GetBall()->GetHolder() != NULL )
		ability = game->GetBall()->GetHolder()->GetAttributes()->GetGeneralKickAccuracy();

	MabQuaternion::MabQuaternionSlerp( bad_rotation, perfect_final_pre_rotation, ability, final_pre_rotation );
}

void ASSBall::SetTransparency( float value )
{
	MABUNUSED( value );
	// Currently can break the game on PS3 due to some render instance buggery
	/*SIFPSSGShaderParameters::ShaderParameter alpha(ball_object, SPP_ALPHA, Vector4(value, 0, 0, 0));

	game->GetGameContext()->GetShaderParameters()->SetParameter(alpha);*/
}

void ASSBall::SetColourTint( MabColour colour )
{
	UMaterialInstanceDynamic* dynmBallMat = Cast<UMaterialInstanceDynamic>(GetBallMesh()->GetMaterial(0));
	if (dynmBallMat)
	{
		dynmBallMat->SetVectorParameterValue(TEXT("ColorTint"), SIFGameHelpers::GAConvertMabColorToFLinearColor(colour));
	}
#if 0 //#rc3_legacy
	SIFPSSGShaderParameters::ShaderParameter ambient(ball_object, SPP_AMBIENTMATERIAL, Vector4(colour.r, colour.g, colour.b, 0.7f));
	SIFPSSGShaderParameters::ShaderParameter diffuse(ball_object, SPP_DIFFUSEMATERIAL, Vector4(colour.r, colour.g, colour.b, 0.7f));

	game->GetGameContext()->GetShaderParameters()->SetParameter(ambient);
	game->GetGameContext()->GetShaderParameters()->SetParameter(diffuse);
#endif
}


void ASSBall::InitialiseReplay()
{
#ifndef DISABLE_REPLAYS

	UReplayManager* ReplayManager = SIFApplication::GetApplication()->ReplayManager;

	if (ReplayManager != nullptr)
	{
		FReplayStreamActor* ReplayStreamActor = ReplayManager->RegisterReplayStreamActor(this, nullptr);

		if (ReplayStreamActor != nullptr)
		{
			ReplayStreamActor->OnStartPlayback.BindUObject(this, &ASSBall::OnReplayStart);
			ReplayStreamActor->OnStopPlayback.BindUObject(this, &ASSBall::OnReplayStop);
			ReplayStreamActor->OnAttachActor.BindUObject(this, &ASSBall::OnReplayAttach);
			ReplayStreamActor->OnDetachActor.BindUObject(this, &ASSBall::OnReplayDetach);
		}
	}

#endif // !DISABLE_REPLAYS
}

void ASSBall::FinaliseReplay()
{
#ifndef DISABLE_REPLAYS

	UReplayManager* ReplayManager = SIFApplication::GetApplication()->ReplayManager;

	if (ReplayManager != nullptr)
	{
		ReplayManager->DeregisterReplayStreamActor(this);
	}

#endif // !DISABLE_REPLAYS
}

void ASSBall::OnReplayStart()
{
#ifndef DISABLE_REPLAYS

	//#afl_replay
	/*SetActorTickEnabled(false);
	m_replayCachedLinearVelocity = GetLinearVelocity();

	UPrimitiveComponent *Root = Cast<UPrimitiveComponent>(GetRootComponent());
	Root->SetSimulatePhysics(false);
	Root->SetEnableGravity(false);
	SetActorEnableCollision(false);

	m_pReplayCachedPlayerWithBall = m_pCurrentPlayerWithBall;
	m_pCurrentPlayerWithBall = nullptr;*/

	m_inReplay = true;

#endif
}

void ASSBall::OnReplayStop()
{
#ifndef DISABLE_REPLAYS

	m_inReplay = false;
	//#afl_replay
	/*m_pCurrentPlayerWithBall = m_pReplayCachedPlayerWithBall;
	m_pReplayCachedPlayerWithBall = nullptr;

	SetActorTickEnabled(true);

	UPrimitiveComponent *Root = Cast<UPrimitiveComponent>(GetRootComponent());
	Root->SetSimulatePhysics(true);
	Root->SetEnableGravity(true);
	SetActorEnableCollision(true);

	SetLinearVelocity(m_replayCachedLinearVelocity);*/

#endif
}

void ASSBall::OnReplayAttach(AActor* ActorAttach, FName AttachSocketName)
{
#ifndef DISABLE_REPLAYS

	//#afl_replay 
	/*AFootballCreature* FootballCreature = Cast<AFootballCreature>(ActorAttach);

	if (FootballCreature != nullptr)
	{
		USceneComponent* SceneComponent = FootballCreature->BodyMesh;

		if (SceneComponent != nullptr)
		{
			AttachToComponent(SceneComponent, FAttachmentTransformRules::SnapToTargetIncludingScale, AttachSocketName);
		}
	}*/

#endif
}

void ASSBall::OnReplayDetach()
{
#ifndef DISABLE_REPLAYS

	//#afl_replay DetachFromActor(FDetachmentTransformRules::KeepWorldTransform);

#endif
}