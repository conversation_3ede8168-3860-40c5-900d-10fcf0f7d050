/*--------------------------------------------------------------
|        Copyright (C) 1997-2011 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/Rules/RURulesDebugSettings.h"

//#include <Network.h>
#include "Mab/Lua/MabLuaAutoBinder.h" //#rc3_legacy_lua
#include "Match/Debug/RUGameDebugSettings.h"

#include "Match/SIFGameContext.h"
#include "Match/Debug/SIFDebug.h"
//#include "SIFDebugMenu.h"
//#include "SIFDebugDrawKeys.h"
//#include "SIFGameFlowNode.h"
#include "Match/SIFGamePauseState.h"
#include "Match/AI/Actions/RUAction.h"
//#include "RUCrowdManager.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "SIFFlowConstants.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SSRole.h"
#include "Rugby/Animation/RugbyAnimationEnums.h"
#include "Match/Components/RUActionManager.h"
#include "Match/RugbyUnion/Enums/RUPlayerPositionEnum.h"
//#rc3_legacy_include #include "PSSGMabVectormath.h"
//#include <MabCentralTypeDatabase.h>
//#include <MabLuaTypeDatabase.h>
#include "Mab/Utility/MabTranslationManager.h"
//#include <MabController.h>
#include "Match/Camera/SSCameraManager.h"
#include "Match/SSRoleNull.h"
#include "Match/SSRoleFactory.h"
#include "Match/SSSpatialHelper.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
//#include "NMMabAnimationNetwork.h"
#include "Utility/RURandomNumberGenerator.h"
#include "Match/HUD/RU3DHUDManager.h"
//#rc3_legacy_include #include "RUContextualHelper.h"
#include "Match/RugbyUnion/RUEmotionEngineManager.h"
#include "Match/SSReplaysMk2/SSReplayManager.h"
#include "Match/Components/RUPlayerState.h"
#include "Match/Components/RUPlayerLookAt.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/RUAsyncLoadingEnable.h"
#include "Match/RugbyUnion/RUGameGetToBall.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Match/RugbyUnion/RUWeatherManager.h"
#include "UI/Screens/WWUIScreenDebugWindow.h"

#ifdef ENABLE_GAME_DEBUG_MENU

// Implement for serialisation.
MABRUNTIMETYPE_IMP1( RURulesDebugSettings, MabObject )

static const char* DEBUG_MENU_NAME = "Rules Debug Settings";
static const char* DEBUG_MENU_PATH = "ui/menu/game_debug_options.xml";

static const char* NEXT_TACKLE_TRY_PROBABILITY = "next_tackle_try_probability";
static const char* NEXT_TACKLE_IS_HIGH				= "next_tackle_is_high";
static const char* NEXT_HIGH_TACKLE_IS_YELLOW		= "next_high_tackle_yellow_card";
static const char* NEXT_HIGH_TACKLE_IS_SECOND_YELLOW= "next_yellow_is_2nd_yellow_card";
static const char* NEXT_HIGH_TACKLE_IS_RED			= "next_high_tackle_red_card";
static const char* NEXT_TACKLE_IS_OFFSIDE			= "next_tackle_is_offside";
static const char* NEXT_TACKLE_CAUSES_INJURY		= "next_tackle_causes_injury";
static const char* NEXT_GAME_KICK_SOMEONE_OFFSIDE	= "next_kick_someone_is_offside";
static const char* NEXT_PASS_IS_FORWARD				= "next_pass_is_forward";
static const char* NEXT_CATCH_OR_COLLECT_IS_KNOCK_ON= "next_handle_knock_on";
static const char* NEXT_TACKLE_FIFTH_TACKLE			= "next_tackle_fifth_tackle";
static const char* ZERO_REMAINING_TIME				= "zero_time_remaining";
static const char* NEXT_KICK_CATCH_IS_MARK			= "next_kick_catch_is_mark";
static const char* FORCE_VIDEO_REF					= "force_video_ref";
static const char* FORCE_VIDEO_REF_AWARD_TRY		= "force_video_ref_awardtry";
static const char* FORCE_EXTRA_TIME					= "force_extra_time";
static const char* SHOW_RULES_DEBUG_INFO			= "show_debug_info";
static const char* NEXT_TACKLE_GOES_TO_MAUL			= "next_tackle_goes_to_maul";
static const char* NEXT_TACKLE_GOES_TO_SCRUM		= "next_tackle_goes_to_scrum";
static const char* DEBUG_NMA						= "break_on_NMA";
static const char* EXPERIMENTAL_USE_IT_RUCK			= "use_experimental_use_it_ruck_rule";
static const char* RESET_CONSEQUENCE_IN_PROG		= "reset_consequence_in_progress";

RURulesDebugSettings::RURulesDebugSettings()
: show_debug_info(false)
, next_tackle_is_high( false )
, next_high_tackle_yellow_card( false )
, next_yellow_card_is_second_yellow_card( false )
, next_high_tackle_red_card( false )
, next_tackle_is_offside( false )
, next_tackle_causes_injury( false )
, next_game_kick_someone_is_offside( false )
, next_pass_is_forward( false )
, next_catch_or_collect_is_knock_on( false )
, next_tackle_fifth_tackle( false )
, zero_remaining_time( false )
, next_kick_catch_is_mark( false )
, force_extra_time( false )
, force_video_ref( false )
, force_video_ref_result( true )
, next_tackle_goes_to_maul( false )
, next_tackle_goes_to_scrum( false )
, debug_NMA( false )
, next_tackle_try_probability( (int)TRY_TACKLE_TYPE::TTT_UNKNOWN )
, experimental_use_it_ruck( false )
, last_player_meters_gained(0)
, reset_consequence_in_progress(false)
{
	//SIFDebug::GetDebugMenu()->AddMenu(DEBUG_MENU_NAME, this, DEBUG_MENU_PATH);

	//Bind variables to UI Fields!
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_IS_HIGH), &next_tackle_is_high);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_NEXT_HIGH_TACKLE_YELLOW_CARD), &next_high_tackle_yellow_card);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_NEXT_YELLOW_IS_SECOND_YELLOW_CARD), &next_yellow_card_is_second_yellow_card);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_NEXT_HIGH_TACKLE_RED_CARD), &next_high_tackle_red_card);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_IS_OFFSIDE), &next_tackle_is_offside);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_CAUSES_INJURY), &next_tackle_causes_injury);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_NEXT_KICK_SOMEONE_IS_OFFISDE), &next_game_kick_someone_is_offside);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_NEXT_PASS_IS_FORWARD), &next_pass_is_forward);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_NEXT_HANDLE_KNOCK_ON), &next_catch_or_collect_is_knock_on);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_FIFTH_TACKLE), &next_tackle_fifth_tackle);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_ZERO_TIME_REMAINING), &zero_remaining_time);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_NEXT_KICK_CATCH_IS_MARK), &next_kick_catch_is_mark);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_TRY_PROBABILITY), &next_tackle_try_probability);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_FORCE_VIDEO_REF), &force_video_ref);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_FORCE_VIDEO_REF_AWARDTRY), &force_video_ref_result); //Maybe wrong var
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_GOES_TO_MAUL), &next_tackle_goes_to_maul);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_GOES_TO_SCRUM), &next_tackle_goes_to_scrum);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_BREAK_ON_NMA), &debug_NMA);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_FORCE_EXTRA_TIME), &force_extra_time);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_USE_EXPERIMENTAL_USE_IT_RUCK_RULE), &experimental_use_it_ruck);
	UWWUIScreenDebugWindow::AddToOptionsBindings(FString(DEBUGOPTIONS_TEXTFIELD_RESET_CONSEQUENCES_IN_PROGRESS), &reset_consequence_in_progress);
}

RURulesDebugSettings::~RURulesDebugSettings()
{
	//SIFDebug::GetDebugMenu()->RemoveMenu(DEBUG_MENU_NAME);
}

void RURulesDebugSettings::DefineMabCentralInterfaces()
{
	/// Define a type for the settings object
	MabObjectType settings_type = MabCentralTypeDatabase::DefineType( RURulesDebugSettings::RTTGetStaticClassName(), "RU Rules Debug Settings" );

	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, SHOW_RULES_DEBUG_INFO, "bool", offsetof(RURulesDebugSettings, show_debug_info));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, NEXT_TACKLE_IS_HIGH, "bool", offsetof(RURulesDebugSettings, next_tackle_is_high));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, NEXT_HIGH_TACKLE_IS_YELLOW, "bool", offsetof(RURulesDebugSettings, next_high_tackle_yellow_card));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, NEXT_HIGH_TACKLE_IS_SECOND_YELLOW, "bool", offsetof(RURulesDebugSettings, next_yellow_card_is_second_yellow_card));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, NEXT_HIGH_TACKLE_IS_RED, "bool", offsetof(RURulesDebugSettings, next_high_tackle_red_card));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, NEXT_TACKLE_IS_OFFSIDE, "bool", offsetof(RURulesDebugSettings, next_tackle_is_offside));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, NEXT_TACKLE_CAUSES_INJURY, "bool", offsetof(RURulesDebugSettings, next_tackle_causes_injury));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, NEXT_GAME_KICK_SOMEONE_OFFSIDE, "bool", offsetof(RURulesDebugSettings, next_game_kick_someone_is_offside));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, NEXT_PASS_IS_FORWARD, "bool", offsetof(RURulesDebugSettings, next_pass_is_forward));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, NEXT_CATCH_OR_COLLECT_IS_KNOCK_ON, "bool", offsetof(RURulesDebugSettings, next_catch_or_collect_is_knock_on));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, NEXT_TACKLE_FIFTH_TACKLE, "bool", offsetof(RURulesDebugSettings, next_tackle_fifth_tackle));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, ZERO_REMAINING_TIME, "bool", offsetof(RURulesDebugSettings, zero_remaining_time));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, NEXT_KICK_CATCH_IS_MARK, "bool", offsetof(RURulesDebugSettings, next_kick_catch_is_mark));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, NEXT_TACKLE_TRY_PROBABILITY, "int", offsetof(RURulesDebugSettings, next_tackle_try_probability));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, FORCE_VIDEO_REF, "bool", offsetof(RURulesDebugSettings, force_video_ref));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, FORCE_VIDEO_REF_AWARD_TRY, "bool", offsetof(RURulesDebugSettings, force_video_ref_result));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, NEXT_TACKLE_GOES_TO_MAUL, "bool", offsetof(RURulesDebugSettings, next_tackle_goes_to_maul));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, NEXT_TACKLE_GOES_TO_SCRUM, "bool", offsetof(RURulesDebugSettings, next_tackle_goes_to_scrum));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, DEBUG_NMA, "bool", offsetof(RURulesDebugSettings, debug_NMA));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, FORCE_EXTRA_TIME, "bool", offsetof(RURulesDebugSettings, force_extra_time));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, EXPERIMENTAL_USE_IT_RUCK, "bool", offsetof(RURulesDebugSettings, experimental_use_it_ruck));
	MabCentralTypeDatabase::DefineTypeRawAttribute(settings_type, RESET_CONSEQUENCE_IN_PROG, "bool", offsetof(RURulesDebugSettings, reset_consequence_in_progress));
}

// Registers the Lua methods associated with this object.
void RURulesDebugSettings::RegisterLuaMethods()
{
}

void RURulesDebugSettings::RegisterMenuConstraints( SIFDebugMenu* debug_menu )
{
	static const char* TACKLE_TRY_PROBABILITY_NAMES[] =
	{
		"Use normal rules",
		"80% try, 20% no try",
		"50% try, 50% no try",
		"20% try, 80% no try",
		"0% try, 100% no try"
	};
	//MABASSERT( debug_menu );
	//debug_menu->RegisterVariableEnum( RURulesDebugSettings::RTTGetStaticClassName(), NEXT_TACKLE_TRY_PROBABILITY, false, TACKLE_TRY_PROBABILITY_NAMES );
}

#endif  // ENABLE_GAME_DEBUG_MENU
