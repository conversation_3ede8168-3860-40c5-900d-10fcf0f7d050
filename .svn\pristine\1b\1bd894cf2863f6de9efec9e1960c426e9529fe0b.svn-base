/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Actions/RUActionKick.h"

#include "Match/Ball/SSBall.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAnimation.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUDBPlayer.h"
#include "Match/RugbyUnion/RUGameAnimation.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUInputKickInterface.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Rugby/Utility/RURandomNumberGenerator.h"
#include "Character/RugbyPlayerController.h"
#include "Character/RugbyCharacterAnimInstance.h"
#include "Match/SSTeam.h"
#include "../Formations/SSEVDSFormationManager.h"
#include "../SetPlays/SSSetPlayManager.h"
#include "Match/RugbyUnion/RUTeam.h"

//#rc3_legacy_include #include <NMMabAnimationEvents.h>

/// used to determine the place kick velocity

const float MAX_KICK_FACING_DEVIATION = 20.0f;	// Maxiumum angle in degress that
												// the player can be facing away from
												// where they want to kick before they peform the kick
												// This has been reduced from 40.0f as we now can only kick forward even if running backwards
												// And 40.0f didn't look quite right

//request names for kick animations

static const char* LONG_PUNT_KICK_ANIM_REQ	= "long_punt_kick";
static const char* DROP_GOAL_ANIM_REQ		= "drop_kick";
static const char* DROP_KICK_ANIM_REQ		= "drop_kick";
static const char* GRUBBER_KICK_ANIM_REQ	= "grubber_kick";
static const char* CHIP_KICK_ANIM_REQ		= "chip_kick";
static const char* BOX_KICK_ANIM_REQ		= "box_kick";
static const char* KICKOFF_KICK_ANIM_REQ	= "kickoff";


MABRUNTIMETYPE_IMP1(RUActionKick, RUAction);

RUActionKick::RUActionKick( ARugbyCharacter* player )
: RUAction( player )
, ball_kicked( false )
, ball_kicked_during_blendOut (false)
, ball_dropped( false )
, kick_type( KICKTYPE_NONE )
, kick_angle( 0.0f )
, kick_strength_pct( 0.0f )
, account_for_wind_value( 0.0f )
, recommended_kick_position( 0.0f, 0.0f, 0.0f )
, should_account_for_wind( false )
, kick_entry_speed( 0.0f )
, kick_done( false )
, kick_mode( KM_STRENGTH_ANGLE )
, kick_state( KS_NONE )
, release_time( 0.0f )
{
}

void RUActionKick::Start( KickType p_kick_type, float p_kick_angle, float p_kick_strength_pct, float account_for_wind )
{
	kick_done = false;
	ball_kicked = false;
	ball_kicked_during_blendOut = false;
	ball_dropped = false;

	// Copy the input vairables to members
	kick_type			= p_kick_type;
	kick_strength_pct	= p_kick_strength_pct;
	kick_angle			= p_kick_angle;
	account_for_wind_value = account_for_wind;
	kick_state			= KS_GET_INTO_POSITION;
	kick_mode			= KM_STRENGTH_ANGLE;
	kick_entry_speed	= m_pPlayer->GetMovement()->GetCurrentSpeed();

	// Add on error due to the kickers kicking accuracy
	float power_deviation = 0.0f;
	float angle_deviation = 0.0f;
	m_pPlayer->GetAttributes()->CalculateGeneralKickInaccuracy( power_deviation, angle_deviation );

	kick_strength_pct *= (1.0f + power_deviation);

	kick_angle += angle_deviation;

	MABASSERT( kick_strength_pct > 0.0f && kick_strength_pct < 1.5f );

	RUPlayerMovement *plr_movement = m_pPlayer->GetMovement();

	// set a waypoint in front of the player
	float current_speed = plr_movement->GetCurrentSpeed();

	if ( current_speed > 0.2f ) {
		float rot = plr_movement->GetCurrentFacingAngle();
		FVector new_target;
		SSMath::AngleToMabVector3( rot, new_target );

		new_target *= current_speed * 2.0f;
		new_target += plr_movement->GetCurrentPosition();
		wwNETWORK_TRACE_JG("RUActionKick");
		plr_movement->SetTargetPosition( new_target );
		m_lock_manager.UFLock(UF_SETWAYPOINT);
	}

	RUAction::Enter();
}

void RUActionKick::Start( KickType p_kick_type, const FVector &recommended_pos, bool account_for_wind )
{
	//SETDEBUGMARKER(45678, recommended_pos, MabColour::Red);

	kick_done = false;
	ball_kicked = false;
	ball_kicked_during_blendOut = false;

	// Copy the input variables to members
	kick_type			= p_kick_type;
	recommended_kick_position = recommended_pos;
	should_account_for_wind = account_for_wind;
	kick_state			= KS_GET_INTO_POSITION;
	kick_mode			= KM_POSITION;
	kick_entry_speed	= m_pPlayer->GetMovement()->GetCurrentSpeed();

#ifdef BUILD_DEBUG
	float dist = 0.0f;
	switch (kick_type)
	{
		case KICKTYPE_PENALTYPUNT:
		case KICKTYPE_LONGPUNT:			dist = MAX_LONG_PUNT_DIST;		break;
		case KICKTYPE_BOXKICK:			dist = MAX_BOX_KICK_DIST;		break;
		case KICKTYPE_UPANDUNDER:		dist = MAX_UP_AND_UNDER_DIST;	break;
		case KICKTYPE_CHIPKICK:			dist = MAX_CHIP_KICK_DIST;		break;
		case KICKTYPE_SETPLAYCHIPKICK:	dist = MAX_SETPLAY_CHIP_KICK_DIST;		break;
		case KICKTYPE_GRUBBERKICK:		dist = MAX_GRUBBER_KICK_DIST;	break;
		case KICKTYPE_DROPGOAL:			dist = MAX_DROP_GOAL_DIST;		break;
		case KICKTYPE_PLACEKICK:		dist = MAX_PLACE_KICK_DIST;		break;
		case KICKTYPE_KICKOFF:			dist = MAX_KICKOFF_KICK_DIST;	break;
		default:						dist = MAX_LONG_PUNT_DIST;		break;
	}
	// Ensure we're not trying to kick too far

	float kick_dist = (recommended_pos - player->GetMovement()->GetCurrentPosition()).ApproxMagnitude();
	MABLOGDEBUG( "kick_dist: %0.2f", kick_dist );

	if(kick_dist < dist)
	{
		MABLOGDEBUG("RUActionKick::Start: Kick distance (%.2f) > max distance (%.2f)",kick_dist,dist);
	}

	MABASSERT(kick_dist < dist*1.1f);	// 10% over assert!
#endif

	// set a waypoint in front of the player
	float current_speed = m_pPlayer->GetMovement()->GetCurrentSpeed();

	if ( current_speed > 0.2f )
	{
		wwNETWORK_TRACE_JG("RUActionKick");
		m_pPlayer->GetMovement()->SetTargetPosition( recommended_pos );
	}

	RUAction::Enter();
}

bool RUActionKick::InternalEnter()
{
	kick_done = false;

	//Register animation event
	m_pPlayer->GetMabAnimationEvent().Add( this, &RUActionKick::AnimationEvent );

	return true;
}

void RUActionKick::InternalExit(bool in_destroy)
{
	if(in_destroy)
		return;

	//Unregister animation event
	if ( IsRunning() == true )
		m_pPlayer->GetMabAnimationEvent().Remove( this, &RUActionKick::AnimationEvent );

	if(!ball_kicked) // then we need to clean up cameras as we were most likely tackled out of the kick action
	{
		// if ball dropped but not kicked knock it on as player has been tackled mid kick
		if ( ball_dropped && kick_type != KICKTYPE_BOXKICK && m_pPlayer->GetActionManager()->IsActionRunning( ACTION_TACKLEE ) )
			m_pGame->GetGameState()->HandlingError( m_pPlayer, true );

		m_pGame->GetEvents()->change_to_camera(GAME_CAM_INVALID);
	}

	// if human clear kick incase of early out
	SSHumanPlayer* human = m_pPlayer->GetHumanPlayer();
	if( human != NULL )
	{
		RUInputKickInterface* kick_interface = m_pGame->GetInputManager()->GetKickInterface();
		kick_interface->StopKick();

		// Make sure that any prewound pass is cleared if the ball has left the
		// player's hands.
		if ( ball_dropped )
		{
			human->ClearCachedOption();
		}
	}

	if (RUPlayerAnimation* pAnim = m_pPlayer->GetAnimation())
	{
		pAnim->StopAnimation(LONG_PUNT_KICK_ANIM_REQ);
		pAnim->StopAnimation(DROP_GOAL_ANIM_REQ);
		pAnim->StopAnimation(DROP_KICK_ANIM_REQ);
		pAnim->StopAnimation(GRUBBER_KICK_ANIM_REQ);
		pAnim->StopAnimation(CHIP_KICK_ANIM_REQ);
		pAnim->StopAnimation(BOX_KICK_ANIM_REQ);
		pAnim->StopAnimation(KICKOFF_KICK_ANIM_REQ);
	}

	kick_state = KS_NONE;
}

RUActionKick::~RUActionKick()
{
}

void RUActionKick::UpdateKickStrengthAndAngleFromPositionDetails( const FVector& desired_pos )
{
	FVector delta_kick_vec = desired_pos - m_pPlayer->GetMovement()->GetCurrentPosition();
	delta_kick_vec.y = 0.0f;

	float distance_for_kick = delta_kick_vec.ApproxMagnitude();
	kick_angle = SSMath::CalculateAngle( delta_kick_vec.x, delta_kick_vec.z );

	m_pGame->GetBall()->GetKickStrengthDetails( kick_type, distance_for_kick, should_account_for_wind, kick_strength_pct, account_for_wind_value );

	// Add on error due to the kickers kicking accuracy
	float power_deviation = 0.0f;
	float angle_deviation = 0.0f;
	m_pPlayer->GetAttributes()->CalculateGeneralKickInaccuracy( power_deviation, angle_deviation );

	kick_strength_pct *= ( 1.0f + power_deviation );
	kick_angle += angle_deviation;

	MABASSERT(kick_strength_pct >= 0.0f && kick_strength_pct <= 1.5f);
}

void RUActionKick::Update(const MabTimeStep& game_time_step)
{
	MABUNUSED( game_time_step );

	RUPlayerMovement *plr_movement = m_pPlayer->GetMovement();
	RUPlayerAttributes *plr_attribs = m_pPlayer->GetAttributes();

	switch( kick_state )
	{
	case KS_GET_INTO_POSITION:
		{
			// If we are in the in goal then return
			if ( m_pGame->GetSpatialHelper()->IsInInGoal( plr_movement->GetCurrentPosition(), plr_attribs->GetPlayDirection() ) )
			{
				Exit();
				return;
			}

			// Rotate and waypoint the player so that
			// they are mostly facing the kick
			m_lock_manager.UFLock( UF_SETWAYPOINT );
			m_lock_manager.UFLock( UF_SETFACING );

			// Set a target position
			FVector target_pos;
			if ( kick_mode == KM_STRENGTH_ANGLE )
			{
				FVector kick_dir;
				SSMath::AngleToMabVector3( kick_angle, kick_dir );
				target_pos = plr_movement->GetCurrentPosition() + (kick_dir * 10.0f);
			}
			else if ( kick_mode == KM_POSITION )
			{
				// If we have past the position then we need to calculate a new one
				float z_rel_to_recommended = (plr_movement->GetCurrentPosition().z - recommended_kick_position.z) * float(plr_attribs->GetPlayDirection());
				const float MIN_DIST_TO_BE_BEHIND = 1.8f;

				// Vaughan REFACT: WHat the hell is this even used, check AI
				// If we're human we allow them to kick backwards
				if ( z_rel_to_recommended > -MIN_DIST_TO_BE_BEHIND && !m_pPlayer->GetHumanPlayer())
				{
					RUStrategyPos pos;
					if ( m_pGame->GetStrategyHelper()->FindBestGenericKickPos( m_pPlayer, kick_type, pos ) ) {
						target_pos.Set( pos.x, 0.0f, pos.z );
					} else {
						// Too bad give up!
						Exit();
						return;
					}
				} else {
					target_pos = recommended_kick_position;
				}
			} else {
				MABBREAK();
			}

			wwNETWORK_TRACE_JG("RUActionKick");
			plr_movement->SetTargetPosition( target_pos );
			float target_speed = plr_movement->GetIdealSpeed( AS_SLOWJOG );
			// Clamp to the current speed/this helps grubber kick players maintain momentum
			MabMath::ClampLower( target_speed, plr_movement->GetCurrentSpeed() );
			plr_movement->SetTargetSpeed( target_speed );

			// And direction to face
			float delta_angle = 0.0f;
			float target_angle = 0.0f;

			if ( kick_mode == KM_STRENGTH_ANGLE )
			{
				target_angle = kick_angle;
			}
			else if ( kick_mode == KM_POSITION )
			{
				target_angle = SSMath::CalculateAngle( target_pos - plr_movement->GetCurrentPosition() );
			}
			else
			{
				MABBREAK();
			}

			delta_angle = MabMath::AngleDelta( target_angle, plr_movement->GetCurrentFacingAngle() );

			// If we are not close enough to facing the correct angle then
			// make the player face the angle
			if ( MabMath::Fabs( delta_angle ) >= MabMath::Deg2Rad( MAX_KICK_FACING_DEVIATION ) )
			{
				plr_movement->SetFacingFlags( AFFLAG_FACETARGANGLE );
				plr_movement->SetTargetFacingAngle( target_angle );
			}

			//Check if the player is in a setplay
			bool inSetplay = false;
			if (m_pPlayer && m_pPlayer->GetAttributes() && m_pPlayer->GetAttributes()->GetTeam())
			{
				if (SSEVDSFormationManager * formationManager = m_pPlayer->GetAttributes()->GetTeam()->GetFormationManager())
				{
					if (SSSetPlayManager * setplayManager = formationManager->GetSetplayManager())
					{
						inSetplay = setplayManager->isPlayerRunningSetplay(m_pPlayer);
					}
				}
			}

			// Now check to see if we are close enough to facing
			// the right direction to do the kick
			if ( MabMath::Fabs( delta_angle ) < MabMath::Deg2Rad( MAX_KICK_FACING_DEVIATION ) || kick_type == KICKTYPE_BOXKICK || inSetplay)
			{
				plr_movement->SetFacingFlags( AFFLAG_FACETARGANGLE );
				plr_movement->SetTargetFacingAngle( target_angle );

				// If we are kicking to position then
				if ( kick_mode == KM_POSITION )
					UpdateKickStrengthAndAngleFromPositionDetails( target_pos );

				// We are close enough so switch over
				if ( SelectAndPlayAnimation() )
				{
					m_lock_manager.UFLock( UF_DOANIMGRAPH );
					m_lock_manager.UFLock( UF_DOMOTION );

					kick_state = KS_WAIT_KICK_OCCURED;
				}
			} else
				break;
		}
	case KS_WAIT_KICK_OCCURED:
		{
			// If we've waited long enough and the ball is held by the right 'person' (player or -1)
			if ( !kick_done
				&& (( kick_type == KICKTYPE_KICKOFF && m_pGame->GetGameState()->GetBallHolder() == NULL ) //&& (( kick_type == KICKTYPE_PLACEKICK && m_pGame->GetGameState()->GetBallHolder() == NULL )
				   || m_pGame->GetGameState()->GetBallHolder() == m_pPlayer ) )
			{
				// reset the players controller
				if ( m_pGame->GetGameState()->IsGameInStandardPlay() )
				{
					m_lock_manager.UFUnlock( UF_DOANIMGRAPH );
					m_lock_manager.UFUnlock( UF_DOMOTION );
					m_lock_manager.UFUnlock( UF_SETFACING );
					m_lock_manager.UFUnlock( UF_SETWAYPOINT );
				}
			}

			// do the kick
			if ( ball_kicked && !kick_done )
			{
				// check box kick as it can ground the ball in the ingoal atm
				if ( kick_type == KICKTYPE_BOXKICK && m_pGame->GetGameState()->GetPhase() != RUGamePhase::PLAY )
				{
					UE_LOG(LogTemp, Error, TEXT("RUActionKick - Trying to Box Kick when game phase is not PLAY"));
					kick_done = true;
					Exit();
					return;
				}

				// setup the pre kick ball rotation ( used to calculate ball spin )
				MabQuaternion final_pre_rotation;

				if (ball_kicked_during_blendOut) //RC4-3946: Reduce kick intensity if this kick is during Anim Blendout (ex: kick while getting tackled).
				{					
					float StrengthDivider = (m_pGame->GetRNG()->RAND_RANGED_CALL(float, 0.5f));
					MabMath::Clamp(StrengthDivider, 0.1f, 0.5f );
					kick_strength_pct *= StrengthDivider;
				}

				m_pGame->GetBall()->SetupBallDropRotation( kick_angle, kick_strength_pct, kick_type, final_pre_rotation );
				m_pGame->GetBall()->SetPreKickRotation( final_pre_rotation );

				if (kick_type != KICKTYPE_FOURTYTWENTYKICK && kick_type != KICKTYPE_TWENTYFOURTYKICK && kick_type != KICKTYPE_CHIPKICK)
				{
					CheckKickRange(kick_type);
				}
				
				m_pGame->GetGameState()->Kick(m_pPlayer, kick_type, kick_strength_pct, kick_angle, account_for_wind_value);
				kick_done = true;
				kick_state = KS_WAIT_KICK_ANIM_DONE;				
				UE_LOG(LogTemp, Display, TEXT("%s: RUKickBallUpdater: Strength '%.3f' ball has been kicked - waiting for anim to finish, IsBlendOut '%d'"), *m_pPlayer->GetName(), kick_strength_pct, (int) ball_kicked_during_blendOut);
				return;
			}
		}
		break;
case KS_WAIT_KICK_ANIM_DONE:
		{
			// Try and make sure that the player maintains momentum after the kick - towards the ball's bounce point
			MABASSERT( kick_done );
			if ( m_pPlayer->GetAnimation()->HasAnyLocomotionOrIdle())
			{
				if ( kick_type == KICKTYPE_CHIPKICK || kick_type == KICKTYPE_SETPLAYCHIPKICK || kick_type == KICKTYPE_FREEBALLKICK || kick_type == KICKTYPE_GRUBBERKICK || kick_type == KICKTYPE_UPANDUNDER )
				{
					ASSBall* ball = m_pGame->GetBall();
					FVector first_bounce_pos;
					float time;
					RUPlayerMovement* movement = m_pPlayer->GetMovement();
					ball->GetBouncePosition( 1, first_bounce_pos, time );

					const static float MIN_PROJECT_DIST = 12.0f;
					const static float CHASE_URGENCY = 0.95f;
					FVector run_delta = (first_bounce_pos - movement->GetCurrentPosition());
					float magnitude = MabMath::Max( run_delta.Magnitude(), MIN_PROJECT_DIST );
					FVector target = movement->GetCurrentPosition() + run_delta.Unit() * magnitude;

					wwNETWORK_TRACE_JG("RUActionKick");
					movement->SetTargetPosition( target );
					movement->SetThrottleAndTargetSpeedByUrgency( CHASE_URGENCY );
					movement->SetCurrentVelocity( run_delta.Unit() * kick_entry_speed );
				}
				Exit();
				MABLOGDEBUG( "RUKickBallUpdater: %d informing behaviour complete", plr_attribs->GetIndex() );
			}
		}
		break;
	case KS_NONE:
		break;
	}
}


bool RUActionKick::SelectAndPlayAnimation()
{
	// Choose best kick animation
	const char* kick_anim_name = NULL;

	switch (kick_type)
	{
	case KICKTYPE_UPANDUNDER:
		//currently long punt kick for debug purposes.
		kick_anim_name = LONG_PUNT_KICK_ANIM_REQ;
		break;

	case KICKTYPE_PENALTYPUNT:
	case KICKTYPE_LONGPUNT:
	case KICKTYPE_FREEKICK:
	case KICKTYPE_FOURTYTWENTYKICK:
	case KICKTYPE_TWENTYFOURTYKICK:
		kick_anim_name = LONG_PUNT_KICK_ANIM_REQ;
		break;

	case KICKTYPE_CHIPKICK:
	case KICKTYPE_SETPLAYCHIPKICK:
		kick_anim_name = CHIP_KICK_ANIM_REQ;
		break;

	case KICKTYPE_GRUBBERKICK:
		kick_anim_name = GRUBBER_KICK_ANIM_REQ;
		break;

	case KICKTYPE_DROPGOAL:
		kick_anim_name = DROP_GOAL_ANIM_REQ;
		break;

	case KICKTYPE_PLACEKICK:
		break;

	case KICKTYPE_FREEBALLKICK:
		break;

	case KICKTYPE_KICKOFF:
		kick_anim_name = KICKOFF_KICK_ANIM_REQ;
		break;
	case KICKTYPE_DROPKICK:
		kick_anim_name = DROP_KICK_ANIM_REQ;
		break;

	case KICKTYPE_BOXKICK:
		kick_anim_name = BOX_KICK_ANIM_REQ;
		break;
	default:
		MABBREAKMSG("Bad kick type");
	}

	RUPlayerAnimation* anim = m_pPlayer->GetAnimation();

	//if (kick_anim_name && anim->IsAnimationAvailable(kick_anim_name)) //#rc3_legacy_animation. Rewritten this to check for statemachine before calling
	
	ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = anim->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();	
	bool isFullBodyActionNodeActive = anim->GetStateMachine().GetSMFullBodyActions()->IsNodeActive();

	bool CanPlayKick = false;

	//box kick can be for dummyhalf or player in idle
	if (kick_type == KICKTYPE_BOXKICK)
	{
		if ((CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null) || (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::DummyHalf))
		{
			CanPlayKick = true;
		}
	}
	//other kicks can be when player in idle
	else if (kick_anim_name && CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null)
	{
		CanPlayKick = true;
	}
	//this else should not happen, but added a check for debugging
	else
	{
		//MABLOGDEBUG("RUKickBallUpdater: Cant play kick animation, state machine is not set to null or dummy state");
		UE_LOG(LogTemp, Warning, TEXT("RUKickBallUpdater: Cant play kick animation, state machine is '%d'"), (int) CurrentFullBodySMState);
	}

	if (CanPlayKick && isFullBodyActionNodeActive )
	{
		// it would be expected that prefered_foot would be the character '1', but it is coming through from the 
		// data base as the char value 1 so currently testing on that
		float handedness = m_pPlayer->GetAttributes()->GetDBPlayer()->prefered_foot == 1u ? 1.0f : -1.0f;

		// Don't support more than 2 footed players - you never know.
		MABASSERT(handedness == -1.0f || handedness == 1.0f);

		anim->SetVariable(anim->GetHandednessVariable(), handedness);
		anim->PlayAnimation(kick_anim_name);

		release_time = m_pGame->GetSimTime()->GetAbsoluteTime();
		UE_LOG(LogTemp, Warning, TEXT("Kick Name '%s'"), ANSI_TO_TCHAR(kick_anim_name));
		//#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)	
		//if (GEngine)
		//{
		//	FString myString(kick_anim_name);			
		//	GEngine->AddOnScreenDebugMessage(-1, 3.0f, FColor::Cyan, myString);
		//}
		//#endif
		return true;
	}

	return false;
}

//-------------------------------------------------------------------------
// Checks if a kick originating from within the 40 or 20 meter lines
// Helper function that will apply the 4020/2040 kick type to
// any kicks coming from behind the 40 and 20 meter lines
// AI Players will have decided on doing a 4020/2040 kick and will have
// the type already, but players won't as there is no specific 4020/2040 
// input button. This addresses adding that type so the game rules can
// trigger 4020/2040 awards if the circumstances for them are reached
//-------------------------------------------------------------------------
void RUActionKick::CheckKickRange(KickType& kickContext)
{
	ERugbyPlayDirection play_dir	= m_pPlayer->GetAttributes()->GetPlayDirection();
	const FVector& player_pos		= m_pPlayer->GetMovement()->GetCurrentPosition();
	float player_z_pos				= player_pos.z * (float)play_dir;
	//4020 zone
	const float Z_END_4020			= -10.0f;
	const float LEEWAY_4020			= 10.0f;
	const float Z_START_4020		= Z_END_4020 - LEEWAY_4020;
	//2040 zone
	const float Z_END_2040			= -30.0f;
	const float LEEWAY_2040			= 10.0f;
	const float Z_START_2040		= Z_END_2040 - LEEWAY_2040;

	if (player_z_pos > Z_START_4020 && player_z_pos < Z_END_4020)
	{
		kickContext = KICKTYPE_FOURTYTWENTYKICK;
		return;
	}
	else if (player_z_pos > Z_START_2040 && player_z_pos < Z_END_2040)
	{
		kickContext = KICKTYPE_TWENTYFOURTYKICK;
		return;
	}
}

/// For the current aggregate, say whether or not the given aggregate is allowed to run
bool RUActionKick::CanEnterOtherAction( RU_ACTION_INDEX id )
{
	if ( !IsRunning() )
		return true;

	if ( id != ACTION_TACKLEE )
		return false;

	return true;
}



void RUActionKick::AnimationEvent(float /*time*/, ERugbyAnimEvent event, size_t /*userdata*/, bool IsBlendingOut)
{
	if (event != ERugbyAnimEvent::FOOTSTEPS_EVENT)
	{
		UE_LOG(LogTemp, Display, TEXT("RUActionKick::AnimationEvent - Event: %s   IsBlendingOut: %d"), *ENUM_TO_FSTRING(ERugbyAnimEvent, event), IsBlendingOut);
	}

	if ( event == ERugbyAnimEvent::BALL_KICK_EVENT )
	{
		ball_kicked = true;

		ball_kicked_during_blendOut = IsBlendingOut;

//#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)	
//		if (GEngine && ball_kicked_during_blendOut)
//		{
//			FString myString("KickAtBlend");
//			GEngine->AddOnScreenDebugMessage(-1, 3.0f, FColor::Cyan, myString);
//		}
//#endif
	}

	if ( event == ERugbyAnimEvent::BALL_DROP_EVENT )
	{
		ball_dropped = true;
	}
}

bool RUActionKick::CanPrewindAction() const
{
	return RUAction::CanPrewindAction();
	//return !IsRunning() && !player->GetActionManager()->IsActionRunning( ACTION_TACKLEE );
}
