/*--------------------------------------------------------------
|        Copyright ( 1997-2007 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/RUGameState.h"

#include "Match/SIFGameWorld.h"

#include "Match/Ball/SSBall.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/SSSpatialHelper.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/SSRole.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Components/RUActionManager.h"
#include "Match/AI/Actions/RUActionScoreTry.h"
#include "Match/AI/Actions/RUActionTacklee.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/Formations/SSEVDSFormationEnum.h"
#include "Match/AI/Formations/SSEVDSFormationConstants.h"
#include "Match/SSRoleFactory.h"
#include "Match/SSRoleNull.h"
#include "Match/SSMath.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseConversion.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseLineOut.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseScrum.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseScrumLeague.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseRuck.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseMaul.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseTryReaction.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhasePenaltyGoal.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseKickoff.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseDropOut.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseQuickLineOut.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseDecideLineOutNumbers.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseExtraTimeToss.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhasePlayTheBall.h"
#include "Match/Debug/RUGameDebugSettings.h"
//#rc3_legacy_echar #include "RUGameState_echar.h"
#include "Match/Components/RUPlayerState.h"
#include "Match/RugbyUnion/Rules/RURulesDebugSettings.h"
#include "Match/AI/Roles/Competitors/RURoleGetTheBall.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuckScrumHalf.h"
#include "Match/Debug/SIFDebug.h"
#include "Match/SSPlayerFilter.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Match/Debug/RUKickDebugSettings.h"
#include "Match/RugbyUnion/RUTeamStrategy.h"
#include "Match/RugbyUnion/Rules/RURuleTrigger.h"
#include "Match/Components/SSHumanPlayer.h"

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
#include "Utility/consoleVars.h"
#endif

//Brian hack
//const int& RUGameState::GetTackleCount() const {
//	return m_currentTackles;
//}

MABRUNTIMETYPE_IMP( RUGamePhaseHandler )

/// IRU Origin interface for the play restart position
class RestartPositionOrigin : public IRUOrigin
{
public:
	RestartPositionOrigin( RUGameState* state, bool centred ) : state( state ), centred( centred ) {}

	FVector GetOrigin() const override
	{
		FVector restart_pos = state->GetPlayRestartPosition();

		/// Centre the x if requested
		if ( centred )
			restart_pos.x = 0.0f;

		return restart_pos;
	}
	float GetFacingAngle() const override { return 0.0f; }

	RUGameState* state;
	bool centred;
};

/// IRU Origin interface for the play restart position
class PenaltyShotDefensiveOrigin : public IRUOrigin
{
public:
	PenaltyShotDefensiveOrigin( RUGameState* state ) : state( state ) {}

	FVector GetOrigin() const override
	{
		const static float MIN_DIST_BACK = 35.0f;

		FVector default_pos = FVector::ZeroVector;

		/// Make sure we are standing a minimum distance back for shots at goal but not too far back
		/// For conversions it doesn't matter
		if ( state->GetPhase() == RUGamePhase::PENALTY_SHOOT_FOR_GOAL )
		{
			RUTeam* kicking_team = state->GetAttackingTeam();
			FVector restart_pos = state->GetPlayRestartPosition();
			float dist_from_tryline = FIELD_LENGTH * 0.5f - (restart_pos.z * kicking_team->GetPlayDirection());

			if ( dist_from_tryline > MIN_DIST_BACK )
				default_pos.z -= ((dist_from_tryline- MIN_DIST_BACK) * kicking_team->GetPlayDirection());
		}

		return default_pos;
	}
	float GetFacingAngle() const override { return 0.0f; }

	RUGameState* state;
};

/// IRU Origin interface for default - "Ball holder"
class RUTrackBallOrigin : public IRUOrigin
{
public:
	enum VelocityTrackType { VEL_NONE, VEL_FULL, VEL_Z };
	enum PositionTrackType { POS_ALL, POS_Z_ONLY };

	RUTrackBallOrigin( RUGameState* state, SIFGameWorld* game, VelocityTrackType vel_track_type = VEL_NONE, PositionTrackType pos_track_type = POS_ALL ) : state( state ), game( game ), vel_track_type( vel_track_type ), pos_track_type( pos_track_type ), origin( FVector::ZeroVector ) {}

	FVector GetOrigin() const override { return origin; }

	float GetFacingAngle() const override { return 0.0f; }

	/// Get ball holder velocity
	FVector GetVelocity() const
	{
		ARugbyCharacter* ball_holder = state->GetBallHolder();
		FVector velocity = FVector::ZeroVector;

		if(ball_holder!=NULL)
			velocity = ball_holder->GetMovement()->GetCurrentPosition();

		return velocity;
	}

	void Reset()
	{
		origin = FVector::ZeroVector;
	}

	/// Update the origin and store the result
	void Update( float delta_time )
	{		
		ARugbyCharacter* ball_holder = state->GetBallHolder();
		FVector last_origin = origin;
		origin = FVector::ZeroVector;

		//if(game->GetCutSceneManager()->IsSimulationDisabled())
		if (game->GetCutSceneManager()->IsCinematicRunning())
		{	// Override origin when cutscene running.
			//  - have to do this as replay system complete fecks up the ball and player states.
			origin = game->GetCutSceneManager()->GetCutSceneOrigin(); //#rc3_legacy_animation
		}
		else
		{
			float time;

			/// If there is no ball holder then we can also see if the attack has a new potential ball holder
			if ( ball_holder == NULL )
			{
				const BallFreeInfo& bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
				/// See if there is a player ready to receiver the ball
				if ( bfi.event == BFE_PASS && !bfi.IsBallLoose() )
				{
					/// Find all interceptors on the attacking team
					RLPResultList result_list;
					RLP_FILTERPARAMETERS params;
					params.filters = RLP_FILTER_TEAM | RLP_FILTER_FUNCTION;
					params.team = game->GetGameState()->GetAttackingTeam();
					params.filter_function = InterceptorFilterFunction;
					game->GetFilteredPlayerList( result_list, params, NULL );

					if ( !result_list.empty() )
					{
						MABASSERT( result_list.size() == 1 );
						ball_holder = result_list[0].player;
					}
				}
			}

			/// Special case for receivers
			if(ball_holder!=NULL)
			{
				const static float VEL_PROJECT = 1.0f;
				const static float CONVERGE_AMOUNT = 0.97f;
				const static float CONVERGE_TIME = 0.5f;

				FVector new_origin  = ball_holder->GetMovement()->GetCurrentPosition();
				FVector current_vel = ball_holder->GetMovement()->GetCurrentVelocity();
				FVector project_vel = current_vel * VEL_PROJECT;

				if ( vel_track_type == VEL_FULL)
					new_origin += project_vel;
				else if ( vel_track_type == VEL_Z )
					new_origin.z += project_vel.z;
				else {

				}

				origin = MabMath::Lerp( new_origin, last_origin, MabMath::Pow( 1.0f - CONVERGE_AMOUNT, delta_time / CONVERGE_TIME ) );
			}
			else {
				ASSBall* ball = game->GetBall();
				if ( ball->IsOnTheFull() )
					ball->GetBouncePosition( 1, origin, time );
				else
					origin	= ball->GetCurrentPosition( true );
			}

			if ( pos_track_type == POS_Z_ONLY )
				origin.y = origin.x = 0.0f;
		}
	}

	static bool InterceptorFilterFunction( ARugbyCharacter* player )
	{
		return player->GetActionManager()->IsActionRunning( ACTION_INTERCEPT );
	}

	#ifdef ENABLE_GAME_DEBUG_MENU
	virtual FColor GetColour() const { return FColor::White; }
	#endif

	RUGameState* state;
	SIFGameWorld* game;
	VelocityTrackType vel_track_type;
	PositionTrackType pos_track_type;
	FVector origin;
};

/// Custom origins for pod formation zones
/// These are spread across the left, middle and right of the field
/// When the ball moves into their "area" they will track the x position of the ball
class RUPodOrigin : public IRUOrigin
{
public:
	enum PodLocation{ PL_LEFT, PL_MIDDLE, PL_RIGHT, PL_LAST };
	RUPodOrigin( SIFGameWorld* game, PodLocation location )
		: game( game ), location( location ), origin( FVector::ZeroVector )
	{

	}

	FVector GetOrigin() const override { return origin; }
	float GetFacingAngle() const override { return 0.0f; }

	void Reset()
	{
		origin = FVector::ZeroVector;
	}

	/// Update the origin and store the result
	void Update( float delta_time )
	{
		/// Pod roaming zones look like this

		/// {								FIELD   WIDTH							}
		///	|			|			|			|			|			|			|
		///	|	<--	   Left		-->	|	<--	  Middle -->	|	<--	  Right	-->		|
		///	|			|			|			|			|			|			|

		const static float POD_Z_OFFSET = 5.0f;

		ARugbyCharacter* ball_holder = game->GetGameState()->GetBallHolder();
		FVector last_origin = origin, new_origin;

		const static float POD_SIDE_DIST_START = FIELD_WIDTH * 1.0f / 6.0f;
		FVector ball_pos = game->GetBall()->GetCurrentPosition();

		// Project out the z component of the ball holder velocity if there is one
		const static float PROJECT_TIME = 1.0f;
		if (ball_holder != NULL) 
		{
			ball_pos.z += ball_holder->GetMovement()->GetCurrentVelocity().z * PROJECT_TIME;
		}

		ERugbyPlayDirection attack_play_dir = game->GetGameState()->GetAttackingTeam()->GetPlayDirection();
		float play_dir_ball_pos = ball_pos.x * -attack_play_dir;	/// Get it into co-ords left is negative, right is positive
		float play_dir_ball_pos_orig = play_dir_ball_pos;

		float location_bounds[PL_LAST][2] = { { -FIELD_WIDTH * 0.5f, -POD_SIDE_DIST_START }, { -POD_SIDE_DIST_START, +POD_SIDE_DIST_START }, { POD_SIDE_DIST_START, +FIELD_WIDTH * 0.5f } };
		MabMath::Clamp( play_dir_ball_pos, location_bounds[ location ][0], location_bounds[ location ][1] );

		play_dir_ball_pos *= -attack_play_dir;
		new_origin.Set( play_dir_ball_pos, 0.0f, ball_pos.z - attack_play_dir * POD_Z_OFFSET );

		/// If a breakdown is happening in our zone then we move to it
		const static float DEFAULT_CONVERGE_TIME = 0.5f;
		float converge_time = DEFAULT_CONVERGE_TIME;
		//if ( game->GetGameState()->GetBreakdownState() == BD_POSSIBLE && MabMath::InRangeInclusive( play_dir_ball_pos_orig, location_bounds[ location ][0], location_bounds[ location ][1] ) )
		if ( ball_holder && ball_holder->GetActionManager()->IsActionRunning( ACTION_TACKLEE ) && MabMath::InRangeInclusive( play_dir_ball_pos_orig, location_bounds[ location ][0], location_bounds[ location ][1] ) )
		{
			RUActionTacklee* action_tacklee = ball_holder->GetActionManager()->GetAction< RUActionTacklee >();
			if ( action_tacklee->GetTackleResult().successful )
			{
				const static float BREAKDOWN_CONVERGE_TIME = 0.1f;
				const static float BREAKDOWN_OFFSET = 1.0f;
				new_origin.z = ball_pos.z - attack_play_dir * BREAKDOWN_OFFSET;
				converge_time = BREAKDOWN_CONVERGE_TIME;
			}
		}

		/// Aprroach the new origin
		const static float CONVERGE_AMOUNT = 0.97f;
		origin = MabMath::Lerp( new_origin, last_origin, MabMath::Pow( 1.0f - CONVERGE_AMOUNT, delta_time / converge_time ) );
	}

private:
	SIFGameWorld* game;
	PodLocation location;
	FVector origin;
};

/// IRU Origin interface for the play restart position
class ZeroOrigin : public IRUOrigin
{
public:
	ZeroOrigin() = default;

	FVector GetOrigin() const override { return FVector::ZeroVector; }
	float GetFacingAngle() const override { return 0.0f; }
};

///-------------------------------------------------------------------------
/// RUGameState: Constructor
///-------------------------------------------------------------------------

RUGameState::RUGameState(SIFGameWorld *ggame)
: game(ggame)
, events(game->GetEvents())
, ball_holder(NULL)
, last_ball_holder(NULL)
, last_ball_holder_human(NULL)
, attacking_team(NULL)
, defending_team(NULL)
, play_restart_team(NULL)
, last_team_to_score(NULL)
, last_player_to_score(NULL)
, advantage_team(NULL)
, restart_kick_type(KICKTYPE_NONE)
, unsimulated_time(0.0f)
, play_restart_position(FVector::ZeroVector)
, last_pass_direction(0.0f)
, last_pass_direction_times(0)
, last_kick_position(FVector::ZeroVector)
, try_attempt_position(FVector::ZeroVector)
, last_try_attempter(NULL)
, last_try_attempter_human(NULL)
, last_try_scorer(NULL)
, last_try_scorer_human(NULL)
, shoot_for_goal_human(NULL)
, penalty_decision_human(NULL)
, video_ref_try_probability(1.0f)
, try_score_position(FVector::ZeroVector)
, last_conversion_success(false)
, try_scored_in_regular_tackle(false)
, last_kicker(NULL)
, last_offending_player(NULL)
, last_offended_player(NULL)
, last_offended_human(NULL)
, last_ball_was_carried_out(false)
, time_since_last_kick_restart()
, time_since_last_lineout()
, time_since_phase_change()
, phase(RUGamePhase::NONE)
, previous_phase(RUGamePhase::NONE)
, ball_restart_z(0.0f)
, kick_strike_done(false)
, try_ball_down(false)
, metres_gained_flag_z(0)
, ball_in_air(false)
, formation_targets()
, default_formation_origin( NULL )
, default_formation_origin_project_vel( NULL )
, default_formation_origin_project_vel_z( NULL )
, default_formation_origin_project_vel_z_centered(NULL)
, pod_left_origin( NULL )
, pod_middle_origin( NULL )
, pod_right_origin( NULL )
, play_restart_origin( NULL )
, play_restart_centred( NULL )
, zero_origin( NULL )
, penalty_shot_defensive_origin( NULL )
, breakdown_state( RUBreakdownState::NONE )
, breakdown_holder( NULL )
, human_movement_locked( false )
, m_currentTackles (0)
{
	InitVariables();

	memset(this->phase_handlers, 0, sizeof(phase_handlers));

	AddPhaseHandler(RUGamePhase::CONVERSION, MabMemNew(heap_id) RUGamePhaseConversion(ggame));
	
	// WJS RLC Not Needed Remove AddPhaseHandler(RUGamePhase::LINEOUT, MabMemNew(heap_id) RUGamePhaseLineOut(ggame));
	AddPhaseHandler(RUGamePhase::RUCK, MabMemNew(heap_id) RUGamePhaseRuck(ggame));
	// WJS RLC Not Needed Remove AddPhaseHandler(RUGamePhase::MAUL, MabMemNew(heap_id) RUGamePhaseMaul(ggame));

	// WJS RLC ##### Scrum phase handler replacement
	AddPhaseHandler(RUGamePhase::SCRUM, MabMemNew(heap_id) RUGamePhaseScrumLeague(ggame));
	AddPhaseHandler(RUGamePhase::PLAY_THE_BALL, MabMemNew(heap_id) RUGamePhasePlayTheBall(ggame));
	
	//	WJS RLC TODO REMOVE AddPhaseHandler(RUGamePhase::SCRUM, MabMemNew(heap_id) RUGamePhaseScrum(ggame));
	
	
	AddPhaseHandler(RUGamePhase::TRY_REACTION, MabMemNew(heap_id) RUGamePhaseTryReaction(ggame));
	AddPhaseHandler(RUGamePhase::PENALTY_SHOOT_FOR_GOAL, MabMemNew(heap_id) RUGamePhasePenaltyGoal(ggame));
	AddPhaseHandler(RUGamePhase::KICK_OFF, MabMemNew(heap_id) RUGamePhaseKickoff(ggame));
	AddPhaseHandler(RUGamePhase::DROPOUT, MabMemNew(heap_id) RUGamePhaseDropOut(ggame));
	AddPhaseHandler(RUGamePhase::EXTRA_TIME_TOSS, MabMemNew(heap_id) RUGamePhaseExtraTimeToss(ggame));

	/// TYRONE : Add generic stoppage handlers that lock all human players from moving amongst other things
	AddPhaseHandler(RUGamePhase::PENALTY, MabMemNew(heap_id) RUGamePhaseGeneralStoppage(ggame));
	AddPhaseHandler(RUGamePhase::PENALTY_TAP_RESTART, MabMemNew(heap_id) RUGamePhaseGeneralStoppage(ggame));
	AddPhaseHandler(RUGamePhase::PRE_PENALTY_SHOOT_FOR_GOAL, MabMemNew(heap_id) RUGamePhaseGeneralStoppage(ggame));
	AddPhaseHandler(RUGamePhase::PENALTY_KICK_FOR_TOUCH, MabMemNew(heap_id) RUGamePhaseGeneralStoppage(ggame));
	AddPhaseHandler(RUGamePhase::DECISION_PENALTY, MabMemNew(heap_id) RUGamePhaseGeneralStoppage(ggame));
	AddPhaseHandler(RUGamePhase::FREE_KICK, MabMemNew(heap_id) RUGamePhaseGeneralStoppage(ggame));
	AddPhaseHandler(RUGamePhase::POST_CONVERSION, MabMemNew(heap_id) RUGamePhaseGeneralStoppage(ggame));
	AddPhaseHandler(RUGamePhase::INJURY, MabMemNew(heap_id) RUGamePhaseGeneralStoppage(ggame));
	AddPhaseHandler(RUGamePhase::PRE_DROPOUT, MabMemNew(heap_id) RUGamePhaseGeneralStoppage(ggame));
	AddPhaseHandler(RUGamePhase::ELECT_QUICK_TAP, MabMemNew(heap_id) RUGamePhaseGeneralStoppage(ggame));
	AddPhaseHandler(RUGamePhase::QUICK_TAP_PENALTY, MabMemNew(heap_id) RUGamePhaseGeneralStoppage(ggame));
	AddPhaseHandler(RUGamePhase::QUICK_LINEOUT, MabMemNew(heap_id) RUGamePhaseQuickLineOut(ggame));
	AddPhaseHandler(RUGamePhase::DECIDE_LINEOUT_NUMBERS, MabMemNew(heap_id) RUGamePhaseDecideLineOutNumbers(ggame));

#ifdef ENABLE_GAME_DEBUG_MENU
	debug_setplay = NULL;
#endif

	///-------------------------------------------------------------
	/// Setup formation targets
	///-------------------------------------------------------------

	ResetFormationTargets();

	/// Default formation types
	default_formation_origin			   = MabMemNew( MabMemGetDefaultObjectHeap( this ) ) RUTrackBallOrigin( this, game, RUTrackBallOrigin::VEL_NONE );
	default_formation_origin_project_vel   = MabMemNew( MabMemGetDefaultObjectHeap( this ) ) RUTrackBallOrigin( this, game, RUTrackBallOrigin::VEL_FULL );
	default_formation_origin_project_vel_z = MabMemNew( MabMemGetDefaultObjectHeap( this ) ) RUTrackBallOrigin( this, game, RUTrackBallOrigin::VEL_Z );
	default_formation_origin_project_vel_z_centered = MabMemNew( MabMemGetDefaultObjectHeap( this ) ) RUTrackBallOrigin( this, game, RUTrackBallOrigin::VEL_Z, RUTrackBallOrigin::POS_Z_ONLY );

	pod_left_origin							= MabMemNew( MabMemGetDefaultObjectHeap( this ) ) RUPodOrigin( game, RUPodOrigin::PL_LEFT );
	pod_middle_origin						= MabMemNew( MabMemGetDefaultObjectHeap( this ) ) RUPodOrigin( game, RUPodOrigin::PL_MIDDLE );
	pod_right_origin						= MabMemNew( MabMemGetDefaultObjectHeap( this ) ) RUPodOrigin( game, RUPodOrigin::PL_RIGHT );

	/// Play restart types
	play_restart_origin	     = MabMemNew( MabMemGetDefaultObjectHeap( this ) ) RestartPositionOrigin( this, false );
	play_restart_centred	 = MabMemNew( MabMemGetDefaultObjectHeap( this ) ) RestartPositionOrigin( this, true );

	/// Zero origin
	zero_origin	     = MabMemNew( MabMemGetDefaultObjectHeap( this ) ) ZeroOrigin();
	penalty_shot_defensive_origin = MabMemNew( MabMemGetDefaultObjectHeap( this ) ) PenaltyShotDefensiveOrigin( this );

	/// Register all formation targets
	SetFormationTarget( ERugbyFormationTarget::TRACKBALL					,default_formation_origin );
	SetFormationTarget( ERugbyFormationTarget::TRACKBALL_VEL				,default_formation_origin_project_vel );
	SetFormationTarget( ERugbyFormationTarget::TRACKBALL_VEL_Z				,default_formation_origin_project_vel_z );
	SetFormationTarget( ERugbyFormationTarget::TRACKBALL_VEL_Z_CENTERED		,default_formation_origin_project_vel_z_centered );
	SetFormationTarget( ERugbyFormationTarget::POD_LEFT						,pod_left_origin );
	SetFormationTarget( ERugbyFormationTarget::POD_MIDDLE					,pod_middle_origin );
	SetFormationTarget( ERugbyFormationTarget::POD_RIGHT					,pod_right_origin );
	SetFormationTarget( ERugbyFormationTarget::RESTART_POSITION				,play_restart_origin );
	SetFormationTarget( ERugbyFormationTarget::RESTART_CENTERED				,play_restart_centred );
	SetFormationTarget( ERugbyFormationTarget::ORIGIN						,zero_origin );
	SetFormationTarget( ERugbyFormationTarget::PENALTY_SHOT_DEFENSIVE_ORIGIN,penalty_shot_defensive_origin );

	events->try_ball_down.Add( this, &RUGameState::TryBallDown );
	events->player_deleted.Add( this, &RUGameState::OnPlayerDeleted );
	events->advantage_started.Add( this, &RUGameState::OnAdvantageStart );
	events->advantage_ended.Add( this, &RUGameState::OnAdvantageEnd );
	events->ball_bounce.Add( this, &RUGameState::BallBounce );
	events->rule_trigger_ball_out.Add( this, &RUGameState::BallOut );
	events->ball_dead_detected.Add( this, &RUGameState::BallDead );
	events->scrum_finish.Add( this, &RUGameState::ResetMetresGainedFlagToBallPosition );
	events->lineout_finished.Add( this, &RUGameState::ResetMetresGainedFlagToBallPosition );
	events->play_the_ball_finished.Add( this, &RUGameState::ResetMetresGainedFlagToBallPosition );


	time_since_last_kick_restart = MabTime(-1.0f);
	time_since_last_lineout = MabTime(-1.0f);
	time_since_phase_change = MabTime(0.0f);
}

///-------------------------------------------------------------------------
/// RUGameState: Destructor
///-------------------------------------------------------------------------
RUGameState::~RUGameState()
{
	
}

void RUGameState::Cleanup()
{
	ResetFormationTargets();

	SetPhase(RUGamePhase::NONE);		/// Exit active game phase handler.

	/// Delete all of the existing formation targets
	MabMemDeleteSafe( default_formation_origin );
	MabMemDeleteSafe( default_formation_origin_project_vel );
	MabMemDeleteSafe( default_formation_origin_project_vel_z );
	MabMemDeleteSafe( default_formation_origin_project_vel_z_centered );
	MabMemDeleteSafe( pod_left_origin );
	MabMemDeleteSafe( pod_middle_origin );
	MabMemDeleteSafe( pod_right_origin );
	MabMemDeleteSafe( play_restart_origin );
	MabMemDeleteSafe( play_restart_centred );
	MabMemDeleteSafe( zero_origin );
	MabMemDeleteSafe( penalty_shot_defensive_origin );

	for (int i = 0; i < RUGamePhase::COUNT; i++)
	{
		if (phase_handlers[i])
		{
			MabMemDelete(phase_handlers[i]);
		}
	}

	events->try_ball_down.Remove( this, &RUGameState::TryBallDown );
	events->player_deleted.Remove( this, &RUGameState::OnPlayerDeleted );
	events->advantage_started.Remove( this, &RUGameState::OnAdvantageStart );
	events->advantage_ended.Remove( this, &RUGameState::OnAdvantageEnd );
	events->ball_bounce.Remove( this, &RUGameState::BallBounce );
	events->rule_trigger_ball_out.Remove( this, &RUGameState::BallOut );
	events->ball_dead_detected.Remove( this, &RUGameState::BallDead );
	events->scrum_finish.Remove( this, &RUGameState::ResetMetresGainedFlagToBallPosition );
	events->lineout_finished.Remove( this, &RUGameState::ResetMetresGainedFlagToBallPosition );
	events->play_the_ball_finished.Remove( this, &RUGameState::ResetMetresGainedFlagToBallPosition );

}

///-------------------------------------------------------------------------
/// Update
///-------------------------------------------------------------------------

void RUGameState::UpdateSimulation( const MabTimeStep& game_time_step )
{
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
	if (CVarShowGamePhase.GetValueOnAnyThread())
	{
		GEngine->AddOnScreenDebugMessage(-1, -1.0f, FColor::Red, FString::Printf(TEXT("Game Phase: %s"), ANSI_TO_TCHAR(GetPhaseName(GetPhase()))));
	}
#endif

	//bool disable_simulation = game->GetCutSceneManager()->IsSimulationDisabled();
	bool disable_simulation = game->GetCutSceneManager()->IsCinematicRunning();	
	if(!disable_simulation)
	{
		if (phase_handlers[phase])
			phase_handlers[phase]->UpdateSimulation(game_time_step);
	}

	float dt = game_time_step.delta_time.ToSeconds();
	/// Update the track ball origins
	default_formation_origin->Update( dt );
	default_formation_origin_project_vel->Update( dt );
	default_formation_origin_project_vel_z->Update( dt );
	default_formation_origin_project_vel_z_centered->Update( dt );
	pod_left_origin->Update( dt );
	pod_middle_origin->Update( dt );
	pod_right_origin->Update( dt );

	if(!disable_simulation)
	{
		UpdateBreakdown();

		// Update timers
		if ( phase == RUGamePhase::KICK_OFF || phase == RUGamePhase::DROPOUT )
			time_since_last_kick_restart = MabTime(0.0f);
		else if ( time_since_last_kick_restart.ToMicroSeconds() >= 0 )
			time_since_last_kick_restart += game_time_step.delta_time;

		// WJS RLC #### Not needed
		// ######################################
		// if ( phase == RUGamePhase::LINEOUT )
		// 	time_since_last_lineout = MabTime( 0.0f );
		// else if ( time_since_last_lineout.ToMicroSeconds() >= 0 )
		// 	time_since_last_lineout += game_time_step.delta_time;
		// #######################################

		time_since_phase_change += MabTime( game_time_step.delta_time.ToSeconds() );

		// Handle hal back
		const static float MIN_STANDARD_PLAY_TIME_BEFORE_HALFBACK_FOLLOW = 2.0f;
		if (phase == RUGamePhase::PLAY)
		{
			// Are we in training?
			if (game->GetGameSettings().game_settings.game_type == GAME_TRAINING || game->GetGameSettings().game_settings.game_type == GAME_MENU)
			{
				// Yep, Disable halfback in training formation
				GetAttackingTeam()->GetFormationManager()->OverrideAreaNumPlayers("Halfback", 0, ERugbyFormationRole::FORMATION);
			}
			else
			if (time_since_phase_change > MIN_STANDARD_PLAY_TIME_BEFORE_HALFBACK_FOLLOW)
			{
				/// Only turn on halfback when we have been in standard play for some time
				GetAttackingTeam()->GetFormationManager()->OverrideAreaNumPlayers("Halfback", -1, ERugbyFormationRole::FORMATION);
			}
		}

		/* WJS RLC
		/// Only turn on halfback when we have been in standard play for some time
		const static float MIN_STANDARD_PLAY_TIME_BEFORE_HALFBACK_FOLLOW = 2.0f;
		if ( phase == RUGamePhase::PLAY && time_since_phase_change > MIN_STANDARD_PLAY_TIME_BEFORE_HALFBACK_FOLLOW )
			GetAttackingTeam()->GetFormationManager()->OverrideAreaNumPlayers( "Halfback", -1, ERugbyFormationRole::FORMATION );

		//// Disable halfback in training formation
		if ( phase == RUGamePhase::PLAY && (game->GetGameSettings().game_settings.game_type == GAME_TRAINING || game->GetGameSettings().game_settings.game_type == GAME_MENU) )
			GetAttackingTeam()->GetFormationManager()->OverrideAreaNumPlayers( "Halfback", 0, ERugbyFormationRole::FORMATION );
			//*/

		RulesUpdate();
	}
}

///-------------------------------------------------------------------------
/// Reset all game state.
///-------------------------------------------------------------------------

void RUGameState::Reset()
{
	//Why do we do this as we free allocate the world? Would it not be best to reset this before we start a new match?
	//Why do we do it when we end a match? As the world is getting freed?
	if (SIFApplication::GetApplication()->GetConnectionManager()->GetCurrentState() == ERugbyNetworkState::Disconnecting ||
		SIFApplication::GetApplication()->GetConnectionManager()->GetCurrentState() == ERugbyNetworkState::Disconnected)
	{
		return;
	}

	SetPhase(RUGamePhase::NONE);

	InitVariables();

	default_formation_origin->Reset();
	default_formation_origin_project_vel_z->Reset();
	default_formation_origin_project_vel->Reset();

	// Reset all phases
	ResetPhases( true );

	SetPlayRestartTeam((RUTeam*)game->GetTeam(game->GetGameSettings().game_settings.initial_kickoff_team) );
	SetKickRestartKickType( KICKTYPE_KICKOFF );
	SetPhase(RUGamePhase::KICK_OFF);

	// reset advantage state
	advantage_team = NULL;
}
void RUGameState::GameReset()
{
	default_formation_origin_project_vel_z_centered->Reset();

	pod_left_origin->Reset();
	pod_middle_origin->Reset();
	pod_right_origin->Reset();
	//play_restart_origin;
	//play_restart_centred;
	//zero_origin;
	//penalty_shot_defensive_origin;

	events = game->GetEvents();
	time_since_phase_change = MabTime();
	kick_strike_done = false;
	try_ball_down = false;
	//formation_targets
	//default_formation_origin = NULL;
	//default_formation_origin_project_vel = NULL;
	//default_formation_origin_project_vel_z = NULL;
	//default_formation_origin_project_vel_z_centered = NULL;
	//pod_left_origin = NULL;
	//pod_middle_origin = NULL;
	//pod_right_origin = NULL;
	//play_restart_origin = NULL;
	//play_restart_centred = NULL;
	//zero_origin = NULL;
	//penalty_shot_defensive_origin = NULL;
	breakdown_state = RUBreakdownState::NONE;
	breakdown_holder = NULL;
	human_movement_locked = false;

	Reset();
}

void RUGameState::ResetPhases( bool include_current )
{
	for (int i = 0; i < RUGamePhase::COUNT; i++)
	{
		if (include_current || (RUGamePhase)i != phase)
		{
			if (phase_handlers[i])
			{
				phase_handlers[i]->Reset();
			}
		}
	}
}


///-------------------------------------------------------------------------
/// Initialize all variables.
///-------------------------------------------------------------------------

void RUGameState::InitVariables()
{
	phase					= RUGamePhase::NONE;
	previous_phase			= RUGamePhase::NONE;

	//play_position			= FVector::ZeroVector;
	play_restart_position	= FVector::ZeroVector;
	last_kick_position		= FVector::ZeroVector;
	try_score_position		= FVector::ZeroVector;
	try_attempt_position	= FVector::ZeroVector;
	//last_penalty_position	= FVector::ZeroVector;

	//last_points_scorer	= NULL;
	last_try_scorer			= NULL;
	last_try_scorer_human	= NULL;
	shoot_for_goal_human	= NULL;
	penalty_decision_human	= NULL;
	video_ref_try_probability = 1.0f;
	ball_holder				= NULL;
	last_ball_holder		= NULL;
	last_ball_holder_human	= NULL;
	//dummy_half			= NULL;
	//scrum_feeder			= NULL;
	//hitup_player			= NULL;
	last_try_attempter		= NULL;
	last_try_attempter_human= NULL;
	//tacklee				= NULL;
	//tackler				= NULL;
	//player_to_pass_to		= NULL;
	last_kicker				= NULL;
	last_offending_player	= NULL;
	last_offended_player	= NULL;
	last_offended_human		= NULL;
	//last_field_goal_kicker  = NULL;
	//shoot_for_goal_kicker	= NULL;
	//last_injured_player	= NULL;
	attacking_team			= NULL;
	defending_team			= NULL;
	play_restart_team		= NULL;
	last_team_to_score		= NULL;
	last_player_to_score	= NULL;
	advantage_team			= NULL;
	//last_penalty_team		= NULL;
	//last_penalty_human		= NULL;
	//last_penalty_goal_kick_result	= false;
	last_conversion_success = false;
	//last_penalty_decision = PENALTY_DECIDE_NONE;
	try_scored_in_regular_tackle = false;
	last_ball_was_carried_out = false;

	last_pass_direction =  0.0f;
	last_pass_direction_times = 0;

	metres_gained_flag_z = 0;
	ball_in_air = false;

	//ball_out_of_play = false;

//	warp_pending = false;
	unsimulated_time = 0.0f;

	ball_restart_z = 0.0f;
	breakdown_state = RUBreakdownState::NONE;
	breakdown_holder = NULL;
	human_movement_locked = false;

	time_since_last_lineout = MabTime( -1.0f );
	time_since_last_kick_restart = MabTime( -1.0f );

	SetKickRestartKickType( KICKTYPE_KICKOFF );
	SetPlayRestartTeam(NULL);
}

///-------------------------------------------------------------------------
/// RulesUpdate:
///-------------------------------------------------------------------------

void RUGameState::RulesUpdate()
{
	if(IsGameInStandardPlay())
	{
		// Check for Try/Held up/Forced ball...
		CheckBallHolderPlacement();
	}
}

///-------------------------------------------------------------------------
/// Reset the ball holder.
///   ???Not sure why this function is required???
///-------------------------------------------------------------------------

void RUGameState::ResetBallHolder( ARugbyCharacter* player )
{
	game->GetBall()->SetHolder( player );
	last_ball_holder = ball_holder;
	ball_holder = player;
}

///-------------------------------------------------------------------------
/// Set the ball holder.
///-------------------------------------------------------------------------

void RUGameState::SetBallHolder( ARugbyCharacter* player, bool attach_ball_to_player )
{
	///MABASSERT( player != ball_holder );

	//MABLOGDEBUG( "RUGameState::SetBallHolder: %d, %s", player ? player->GetAttributes()->GetIndex() : -1, player ? player->GetAttributes()->GetDisplayName() : "NULL" );

	if ( attach_ball_to_player )
	{
		game->GetBall()->SetHolder( player );
	}
	else
	{
		game->GetBall()->SetHolder( NULL );
	}

	if ( ball_holder != NULL )
	{
		last_ball_holder = ball_holder;
	}

	ball_holder = player;

	if ( player != NULL )
	{
		if( phase != RUGamePhase::DECISION && phase != RUGamePhase::DECISION_PENALTY && phase != RUGamePhase::ELECT_QUICK_TAP )
		{
			SetAttackingTeam( player->GetAttributes()->GetTeam() );
		}
	}

	SET_CHANGEPLAYER_SECTION( game, "SETBH" );
	if(ball_holder!=NULL)
	{
		attacking_team->GetHumanSelector().AssignHumanToPlayer(ball_holder);
	}

	if ( ball_holder )
		last_ball_holder_human = ball_holder->GetHumanPlayer();

	SET_CHANGEPLAYER_SECTION( game, NULL );

	// Fire an event to notify listeners of the change in ball holder
	events->ball_holder_changed(ball_holder, last_ball_holder);

	// NMA DEBUG STUFF
	//---------------------------------------------------------------------------------------------------------------------------------
#ifdef ENABLE_GAME_DEBUG_MENU
	if ( ball_holder && SIFDebug::GetRulesDebugSettings()->DebugNMAEnabled() )
	{
		MABLOGDEBUG( "Game thinks ballholder is: %s", ball_holder->GetAttributes()->GetDBPlayer()->GetLastName() );

		// Make sure this player exists on a team.
		if ( -1 != game->GetTeam(SIDE_A)->GetPlayerIndex( ball_holder ) )
		{
			MABLOGDEBUG( "Game thinks ballholder is on Side A, which is: %s", game->GetTeam(SIDE_A)->GetDbTeam().GetName() );
		}
		else if ( -1 != game->GetTeam(SIDE_B)->GetPlayerIndex( ball_holder ) )
		{
			MABLOGDEBUG( "Game thinks ballholder is on Side B, which is: %s", game->GetTeam(SIDE_B)->GetDbTeam().GetName() );
		}
		else
		{
			// Well shit! Most likely an NMA!
			MABLOGDEBUG( "From team: %s", ball_holder->GetAttributes()->GetDBPlayer()->GetClubName().c_str() ); // PS3 fails to build if we don't pass this as a c-style string.
			MABBREAKMSG( "NMA?!!" );
		}
	}
#endif
	//---------------------------------------------------------------------------------------------------------------------------------
}

///-------------------------------------------------------------------------
/// Set the current attacking team to \e team and the defending team to be the other team.
/// @param team
///		The RL3Team to set the attacking team to.
///-------------------------------------------------------------------------

void RUGameState::SetAttackingTeam( RUTeam* team, bool notify_posession_change )
{
	MABASSERT( team != NULL );

	if ( team != attacking_team && team!=NULL )
	{
		const auto& teams = game->GetTeams();

		attacking_team = team;
		if(teams.size() > 1)
			defending_team = (attacking_team == teams[0].get() ? teams[1] : teams[0]).get();

		//// Fire an event to notify listeners of the change in possession of ball.
		if (notify_posession_change)
		{
			game->GetEvents()->possession_change( attacking_team );
			ResetTackleCount();
		}
		
		attacking_team->GetFormationManager()->UpdatePassPriorities(PASS_PRI_ALL, true);
	}

}

///-------------------------------------------------------------------------
/// Set play restart position.
///-------------------------------------------------------------------------

void RUGameState::SetPlayRestartPosition( const FVector& position )
{
	play_restart_position = FVector( position.x, 0.0f, position.z );

	FieldExtents extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	float min_x = -extents.x / 2.0f + 1.0f;
	float max_x =  extents.x / 2.0f - 1.0f;
	float min_z = -extents.y / 2.0f;
	float max_z =  extents.y / 2.0f;
	MabMath::Clamp( play_restart_position.x, min_x, max_x  );
	MabMath::Clamp( play_restart_position.z, min_z, max_z );

	wwNETWORK_TRACE_JG("RUGameState::SetPlayRestartPosition play_restart_position: X: %f, Y: %f, Z: %f", play_restart_position.X, play_restart_position.Y, play_restart_position.Z);
}

///-------------------------------------------------------------------------
/// Get the play restart position.
///-------------------------------------------------------------------------

const FVector& RUGameState::GetPlayRestartPosition() const
{
	return play_restart_position;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::SetLastPassDirection( float llast_pass_direction )
{
	if ( last_pass_direction != llast_pass_direction )
	{
		last_pass_direction_times = 0;
	}

	last_pass_direction = llast_pass_direction;
	++last_pass_direction_times;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

float RUGameState::GetLastPassDirection() const
{
	return last_pass_direction;

}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

int RUGameState::GetLastPassDirectionTimes() const
{
	return last_pass_direction_times;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------
#pragma optimize("", off)
bool RUGameState::SetPhase( RUGamePhase sstate )
{
	MABLOGDEBUG(MabString(0, "Phase Changed to - %s", GetPhaseName(sstate)).c_str());
	FString infoOutput(GetPhaseName(sstate));

	/*if ( GEngine )
	{
		GEngine->AddOnScreenDebugMessage(-1, 3.0f, FColor::Green, infoOutput);
	}*/

	previous_phase = phase;

	if (phase_handlers[previous_phase])
	{
		phase_handlers[previous_phase]->Exit();
	}

	phase = sstate;
	
	if (phase_handlers[phase])
		phase_handlers[phase]->Enter();

	game->GetEvents()->phase_changed();
#ifdef ENABLE_OSD
	RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
	MabString debug_str(64, "PhaseChange: %s", GetPhaseName());
	settings->PushDebugString(game, RUGameDebugSettings::DP_CHANGE_PLAYER, debug_str.c_str());
#endif

	time_since_phase_change = MabTime(0.0f);

	return true;
}
#pragma optimize("", on)
///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

RUGamePhase RUGameState::GetPhase() const
{
	return phase;
}

RUGamePhaseHandler* RUGameState::GetPhaseHandler() const
{
	return GetPhaseHandler(phase);
}

RUGamePhaseHandler* RUGameState::GetPhaseHandler( RUGamePhase requested_phase ) const
{
	//MABASSERT(requested_phase > RUGamePhase::NONE && requested_phase < RUGamePhase::COUNT);
	return phase_handlers[requested_phase];
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

const char*	RUGameState::GetPhaseName( RUGamePhase sstate ) const
{
	static const char* GAME_STATES[size_t(RUGamePhase::COUNT)] =
	{
		"RUGamePhase::NONE",
		"RUGamePhase::KICK_OFF",
		"RUGamePhase::PLAY",
		"RUGamePhase::CENTER_DROP_OUT",
		"RUGamePhase::PENALTY",
		"RUGamePhase::PENALTY_KICK_FOR_TOUCH",
		"RUGamePhase::PENALTY_SHOOT_FOR_GOAL",
		"RUGamePhase::PENALTY_TAP_RESTART",
		"RUGamePhase::SCRUM",
		"RUGamePhase::CONVERSION",
		"RUGamePhase::RUCK",
		"RUGamePhase::LINEOUT",
		"RUGamePhase::DROPOUT",
		"RUGamePhase::DECISION",
		"RUGamePhase::DECISION_PENALTY",
		"RUGamePhase::FREE_KICK",
		"RUGamePhase::MAUL",
		"RUGamePhase::LINEOUT_RUCK",
		"RUGamePhase::LINEOUT_MAUL",
		"RUGamePhase::PRE_GAME",
		"RUGamePhase::HALF_TIME",
		"RUGamePhase::POST_GAME",
		"RUGamePhase::PRE_KICK_OFF",
		"RUGamePhase::TRY_REACTION",
		"RUGamePhase::TRY_CUTSCENE",
		"RUGamePhase::REACTION_CUTSCENE",
		"RUGamePhase::PRE_PENALTY_SHOOT_FOR_GOAL",
		"RUGamePhase::INJURY",
		"RUGamePhase::POST_CONVERSION",
		"RUGamePhase::PRE_DROPOUT",
		"RUGamePhase::ELECT_QUICK_TAP",
		"RUGamePhase::QUICK_TAP_PENALTY",
		"RUGamePhase::QUICK_LINEOUT",
		"RUGamePhase::DECIDE_LINEOUT_NUMBERS",
		"RUGamePhase::EXTRA_TIME_TOSS",
		"RUGamePhase::SIMULATION",
		"RUGamePhase::PLAY_THE_BALL"
	};

	return GAME_STATES[size_t(sstate)];
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

const char*	RUGameState::GetPhaseName() const
{
	return GetPhaseName( GetPhase() );
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

RUGamePhase RUGameState::GetPreviousPhase() const
{
	return previous_phase;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

inline void RUGameState::AddPhaseHandler(RUGamePhase phase_idx, RUGamePhaseHandler *handler)
{
	MABASSERT(phase_idx >= RUGamePhase::NONE && phase_idx < RUGamePhase::COUNT);
	phase_handlers[phase_idx] = handler;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------
bool RUGameState::IsGameInStandardPlay(bool ignore_suspended_play) const
{
	if (phase_handlers[phase])
		return phase_handlers[phase]->IsStandardPlay() && (ignore_suspended_play || !game->GetRules()->IsPlaySuspended());

	// Fall through. If we don't have a handler, then check if we're in RUGamePhase::PLAY
	return (( phase == RUGamePhase::PLAY && (ignore_suspended_play || !game->GetRules()->IsPlaySuspended()) ) || 
		((game->GetGameSettings().game_settings.game_type == GAME_TRAINING || game->GetGameSettings().game_settings.game_type == GAME_MENU) && (ignore_suspended_play || !game->GetRules()->IsPlaySuspended()) ));
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::Pass( ARugbyCharacter* passing_player, ARugbyCharacter* player_to_pass_to, const FVector& target_position, PASS_TYPE type, bool success )
{
	MABASSERT( passing_player );
	MABASSERT( GetBallHolder() == passing_player );

	if ( GetBallHolder() == passing_player )
	{
		if (type != PT_DUMMY)
		{
			FVector likely_pass_dest = player_to_pass_to != NULL ? player_to_pass_to->GetMovement()->GetCurrentPosition() : target_position;

			SetLastPassDirection( MabMath::Sign(likely_pass_dest.x - passing_player->GetMovement()->GetCurrentPosition().x) );
			SetBallHolder( NULL, false );

			events->pass( passing_player, player_to_pass_to, target_position, type, success );
			//if(success)
			if(phase != RUGamePhase::LINEOUT)
				events->change_to_camera(GAME_CAM_NO_BALL_HOLDER);
		}
	}
	AwardMetresGained( passing_player );
}

///-------------------------------------------------------------------------
/// LineOutThrow
///-------------------------------------------------------------------------

void RUGameState::LineOutThrow(ARugbyCharacter* player, float strength_pct, float angle)
{
	MABASSERT( player != NULL );
	MABASSERT( GetBallHolder() == player || GetLastBallHolder() == player );

	if ( player == GetBallHolder() || player == GetLastBallHolder() )
	{
		game->GetBall()->LineOutThrowIn(strength_pct, angle);
	}
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

KickContext RUGameState::ResolveKickContext( KickType kickType )
{
	if (phase == RUGamePhase::KICK_OFF)
	{
		//MABASSERT( kickType == KICKTYPE_KICKOFF );
		return KC_KICKOFF;
	}

	if (phase == RUGamePhase::DROPOUT)
	{
		//MABASSERT( kickType == KICKTYPE_DROPKICK );
		return KC_DROPOUT;
	}

	if (phase == RUGamePhase::PENALTY_KICK_FOR_TOUCH)
	{
		return KC_PENALTY_TOUCH;
	}

	if (phase == RUGamePhase::PENALTY_SHOOT_FOR_GOAL)
	{
		return KC_PENALTY_GOAL;
	}

	if (phase == RUGamePhase::CONVERSION)
	{
		return KC_CONVERSION;
	}

	if (phase == RUGamePhase::FREE_KICK)
	{
		//MABASSERT( kickType == KICKTYPE_FREEKICK );
		return KC_FREEKICK;
	}

	// 4020
	if (kickType == KICKTYPE_FOURTYTWENTYKICK)
	{
		return KC_FOURTYTWENTY;
	}

	if (kickType == KICKTYPE_TWENTYFOURTYKICK)
	{
		return KC_TWENTYFOURTY;
	}

	if (kickType == KICKTYPE_FREEBALLKICK)
	{
		//MABASSERT( phase == RUGamePhase::PLAY );
		return KC_FREEBALL_KICK;
	}

	if (kickType == KICKTYPE_DROPGOAL)
	{
		//MABASSERT( phase == RUGamePhase::PLAY );
		return KC_DROPGOAL;
	}

	if (kickType == KICKTYPE_BOXKICK)
	{
		//MABASSERT( phase == RUGamePhase::PLAY );
		return KC_BOXKICK;
	}

	if (phase == RUGamePhase::PLAY)
	{
		return KC_FREEPLAY;
	}



	return KC_NONE;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------
#pragma optimize ("", off)
void RUGameState::Kick( ARugbyCharacter* player, KickType kick_type, float strength_pct, float angle, float account_for_wind )
{
	//MABLOGDEBUG("RUGameState:KICK");

#ifdef ENABLE_GAME_DEBUG_MENU
	if ( SIFDebug::GetKickDebugSettings()->ShouldOverrideKickValues() )
	{
		float kick_strength_modifier = SIFDebug::GetKickDebugSettings()->GetKickStrengthModifier();
		strength_pct *= kick_strength_modifier;
	}
#endif

	MABASSERT( player != NULL );
	// place kicks could be kicked by any player
	MABASSERT( GetBallHolder() == player || GetLastBallHolder() == player || kick_type == KICKTYPE_PLACEKICK);

	if ( player == GetBallHolder() || player == GetLastBallHolder() || kick_type == KICKTYPE_PLACEKICK )
	{
		bool update_state = false;
		KickContext kick_context = ResolveKickContext( kick_type );

		switch ( kick_type )
		{
		case KICKTYPE_UPANDUNDER:
			{
				game->GetBall()->UpAnUnderKick( strength_pct, angle, account_for_wind, false );
				update_state = true;
			}
			break;

		case KICKTYPE_BOXKICK:
			{
				game->GetBall()->BoxKick( strength_pct, angle, account_for_wind, false );
				update_state = true;
			}
			break;

		case KICKTYPE_PENALTYPUNT:
		case KICKTYPE_LONGPUNT:
		case KICKTYPE_FOURTYTWENTYKICK:
		case KICKTYPE_TWENTYFOURTYKICK:
			{
				game->GetBall()->LongPuntKick( strength_pct, angle, account_for_wind, false );
				update_state = true;
			}
			break;

		case KICKTYPE_CHIPKICK:
			{
				game->GetBall()->ChipKick( strength_pct, angle, account_for_wind, false );
				update_state = true;
			}
			break;

		case KICKTYPE_SETPLAYCHIPKICK:
			{
				game->GetBall()->SetplayChipKick(strength_pct, angle, account_for_wind, false);
				update_state = true;
			}
			break;

		case KICKTYPE_GRUBBERKICK:
			{
				game->GetBall()->GrubberKick( strength_pct, angle, false );
				update_state = true;
			}
			break;

		case KICKTYPE_DROPGOAL:
			{
				game->GetBall()->DropGoal( strength_pct, angle, account_for_wind, false );
				update_state = true;
			}
			break;

		case KICKTYPE_KICKOFF:
			{
				update_state = true;
				kick_strike_done = true;
				game->GetBall()->KickOffKick( strength_pct, angle );
			}
			break;

		case KICKTYPE_PLACEKICK:
			if( GetPhase() == RUGamePhase::KICK_OFF  || GetPhase() == RUGamePhase::DROPOUT)
			{
				update_state = true;
				kick_strike_done = true;
				float kick_stat = (player->GetAttributes()->GetGoalKickAccuracy() + player->GetAttributes()->GetGeneralKickAccuracy()) / 2.0f;
				game->GetBall()->PlaceKickStrengthAngle( strength_pct, angle, kick_stat);
			}
			else if ( GetPhase() == RUGamePhase::PENALTY_SHOOT_FOR_GOAL || GetPhase() == RUGamePhase::CONVERSION )
			{
				game->GetBall()->PlaceKickStrengthAngle( strength_pct, angle, player->GetAttributes()->GetGoalKickAccuracy() );

				//set kick flag
				kick_strike_done = true;

				update_state = true;
			}
			break;

		case KICKTYPE_FREEBALLKICK:
			{
				game->GetBall()->FreeBallKick(strength_pct, angle);
				update_state = true;
			}
			break;

		case KICKTYPE_DROPKICK:
			{
				game->GetBall()->DropKick( strength_pct, angle );
				update_state = true;
			}
			break;
		case KICKTYPE_FREEKICK:
			{
				game->GetBall()->LongPuntKick( strength_pct, angle, account_for_wind, false );
				update_state = true;
			}
			break;
		default:
			MABBREAK();
			break;
		}

		// We only update the state if we've done a state transition
		if( update_state )
		{
			last_kicker = player;
			SetLastKickPosition( game->GetBall()->GetCurrentPosition() );

			// Commentary requires the kick destination not the ball position
			float time = 0.0f;
			FVector position( 0.0f, 0.0f, 0.0f );
			game->GetBall()->GetBouncePosition( 1, position, time );
			events->kick( player, kick_context, kick_type, position );

			if ( GetPhase() != RUGamePhase::PENALTY_SHOOT_FOR_GOAL && GetPhase() != RUGamePhase::CONVERSION )
				//dont go to new state here, as this would trigger AI into trying to get the ball
				SetPhase( RUGamePhase::PLAY );

			//monitor->Kick( kick_type, game->GetBall()->GetCurrentPosition() );
		}
		AwardMetresGained( player );
		ball_in_air = true;
	}
}
#pragma optimize ("", on)
///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::KickRestartStart()
{
	events->kick_restart_start();
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::KickRestart( bool force_restart )
{
	MABUNUSED( force_restart );

	events->game_restart();
	bool valid_transition = false;

	// shouldn't be on but better safe than sorry
	try_ball_down = false;

	valid_transition = SetPhase(RUGamePhase::KICK_OFF);

	if( force_restart || valid_transition )
	{
		SetAttackingTeam(GetPlayRestartTeam());
		//monitor->WhistleRestart();
		events->commentary_restart( GetPlayRestartTeam()->GetPlayKicker(), game->GetBall()->GetCurrentPosition() );
	}
}

///-------------------------------------------------------------------------
/// Kicks and catches are caught. Catches always on the full. Kicks can
/// bounce 1x, before before the ::Collected event is invoked instead
///-------------------------------------------------------------------------
void RUGameState::Caught(ARugbyCharacter* player, bool in_the_air )
{
	MABASSERT( player != NULL );
	MABASSERT( GetBallHolder() == NULL );

	if ( GetBallHolder() == NULL )
	{
		int bounces = game->GetBall()->GetNumBounces();
		game->GetEvents()->caught(player, in_the_air, bounces);
		// TYRONE : This is set after the caught event as some ball state like IsBallOnFull relies on this.
		SetBallHolder( player, true );
	}

	if( ball_in_air )
	{
		ball_in_air = false;
		AwardKickingMetresGained( GetLastKicker(), game->GetBall()->GetCurrentPosition() );
	}
}

///-------------------------------------------------------------------------
/// Ball bounce event handler. Update kicking metres gained.
///-------------------------------------------------------------------------
void RUGameState::BallBounce(const FVector& position, const FVector& velocity)
{
	MABUNUSED(velocity);

	FieldExtents field_extents = game->GetSpatialHelper()->GetFieldExtents();
	field_extents.x *= 0.5f;
	field_extents.y *= 0.5f;

	bool out_sides = ( ( position.x > field_extents.x ) || ( position.x < -field_extents.x ) );
	bool out_end = ( ( position.z > field_extents.y ) || ( position.z < -field_extents.y ) );

	if(out_sides || out_end)
		return;

	if( ball_in_air )
	{
		ball_in_air = false;
		AwardKickingMetresGained( GetLastKicker(), position );
	}
}

///-------------------------------------------------------------------------
/// Ball out event handler. Update metres gained.
///-------------------------------------------------------------------------

void RUGameState::BallOut( ARugbyCharacter* Inball_holder, const FVector& position, const FVector& restart_position, bool on_full, bool carried_out )
{
	MABUNUSED(position);
	MABUNUSED(on_full);
	MABUNUSED(carried_out);

	//This stops multiple ball out events firing, while AI runs down to an idle state
	if( !IsGameInStandardPlay() && GetPhase() != RUGamePhase::PENALTY_SHOOT_FOR_GOAL )
		return;

	if( ball_in_air )
	{
		ball_in_air = false;
		AwardKickingMetresGained( GetLastKicker(), restart_position );
	}

	if (Inball_holder != NULL )
	{
		AwardMetresGained(Inball_holder);
	}
	else
	{
		ResetMetresGainedFlag( MabMath::Round( restart_position.z ) );
	}
}

///-------------------------------------------------------------------------
/// Ball dead event handler. Update metres gained.
///-------------------------------------------------------------------------
void RUGameState::BallDead( ARugbyCharacter* Inball_holder, const FVector& position, bool on_full )
{
	MABUNUSED(on_full);

	//This stops multiple ball out events firing, while AI runs down to an idle state
	if( !IsGameInStandardPlay() && GetPhase() != RUGamePhase::PENALTY_SHOOT_FOR_GOAL )
		return;

	if( ball_in_air )
	{
		ball_in_air = false;
		AwardKickingMetresGained( GetLastKicker(), position );
	}

	if (Inball_holder != NULL )
	{
		AwardMetresGained(Inball_holder);
	}
	else
	{
		ResetMetresGainedFlag( MabMath::Round( position.z ) );
	}
}


///-------------------------------------------------------------------------
/// Loose balls from fumbles, knock ons, passes to no-one and kicks that
/// have bounced 2x or more.
///-------------------------------------------------------------------------
void RUGameState::Collected(ARugbyCharacter* player)
{
	MABASSERT( player != NULL );
	MABASSERT( GetBallHolder() == NULL );

	if ( GetBallHolder() == NULL )
	{
		bool ball_in_motion = game->GetBall()->IsInMotion();
		SetBallHolder( player, true );
		events->collected( player, ball_in_motion );
	}
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------
void RUGameState::PickedUp(ARugbyCharacter* player, PickedUpContext picked_up_context)
{
	MABASSERT( player != NULL );
	//MABASSERT( GetBallHolder() == NULL );

	SetBallHolder(player);
	events->ruck_scrum_ball_picked_up( player, picked_up_context );
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::KickRestartRunUp()
{
	events->kick_restart_run_up();
	events->commentary_kick_off_ref_whistle();
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::KickRestartFinished()
{
	//monitor->KickRestart();
	events->kick_restart();
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::SetPlayRestartTeam( RUTeam* team )
{
	play_restart_team = team;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

RUTeam* RUGameState::GetPlayRestartTeam() const
{
	return play_restart_team;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::SetKickRestartKickType( KickType kick_type )
{
	restart_kick_type = kick_type;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

KickType RUGameState::GetKickRestartKickType() const
{
	return restart_kick_type;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::TryAttempt( ARugbyCharacter* player, bool diving )
{
	events->try_attempt( player, diving );
	SetLastTryAttempter( player );
	SetTryAttemptPosition( player->GetMovement()->GetCurrentPosition() );

	AwardMetresGained(player);
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::TryBallDown()
{
	if ( IsGameInStandardPlay() )
		try_ball_down = true;
}

bool RUGameState::IsTryBallDown() const
{
	return try_ball_down;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::TryAttemptEnd()
{
	game->GetEvents()->try_attempt_end();
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::SetTryScorePosition(const FVector& position)
{
	try_score_position = position;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

const FVector& RUGameState::GetTryScorePosition() const
{
	return try_score_position;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::SetLastConversionSuccess( bool success )
{
	if(success)
	{
		if(last_kicker!=NULL)
		{
			SetLastPlayerToScore(last_kicker);	// sets last_team_to_score
		}
	}
	last_conversion_success = success;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

bool RUGameState::GetLastConversionSuccess()
{
	return last_conversion_success;
}

///-------------------------------------------------------------------------
/// Get the last player to score.
///-------------------------------------------------------------------------

ARugbyCharacter* RUGameState::GetLastTryScorer() const
{
	return last_try_scorer;
}

///-------------------------------------------------------------------------
/// Get the last human to score.
///-------------------------------------------------------------------------

SSHumanPlayer* RUGameState::GetLastTryScorerHuman() const
{
	return last_try_scorer_human;
}

///-------------------------------------------------------------------------
/// Set the TMO successful probality.
///-------------------------------------------------------------------------

void RUGameState::SetVideoRefTryProbability( float vvideo_ref_try_probability )
{
	video_ref_try_probability = vvideo_ref_try_probability;
}

///-------------------------------------------------------------------------
/// Get the TMO successful probality.
///-------------------------------------------------------------------------

float RUGameState::GetVideoRefTryProbability() const
{
	return video_ref_try_probability;
}

///-------------------------------------------------------------------------
/// Return the last team to score (in any fashion - try/conversion/penalty).
///-------------------------------------------------------------------------

RUTeam* RUGameState::GetLastTeamToScore() const
{
	return last_team_to_score;
}

///-------------------------------------------------------------------------
/// Set the last player to score  (off anything try/dropkick/conversion)
///-------------------------------------------------------------------------

void RUGameState::SetLastPlayerToScore(ARugbyCharacter* scorer)
{
	last_player_to_score = scorer;
	last_team_to_score = scorer->GetAttributes()->GetTeam();
}

///-------------------------------------------------------------------------
/// Get the last player to score (off anything try/dropkick/conversion)
///-------------------------------------------------------------------------

ARugbyCharacter* RUGameState::GetLastPlayerToScore()
{
	return last_player_to_score;
}

///-------------------------------------------------------------------------
/// Set the last player to score a try.
///-------------------------------------------------------------------------

void RUGameState::SetLastTryScorer(ARugbyCharacter* scorer)
{
	last_try_scorer = scorer;
	SetLastPlayerToScore(scorer);		// sets last team to score
}


///-------------------------------------------------------------------------
/// Set the last human player to score a try.
///-------------------------------------------------------------------------

void RUGameState::SetLastTryScorerHuman(SSHumanPlayer* scorer)
{
	last_try_scorer_human = scorer;
}

///-------------------------------------------------------------------------
/// Set the human player that will take the shot at goal.
///-------------------------------------------------------------------------

void RUGameState::SetShootForGoalHuman(SSHumanPlayer* shoot_for_goal_human_)
{
	shoot_for_goal_human = shoot_for_goal_human_;
}

///-------------------------------------------------------------------------
/// Set the human player that will take the shot at goal.
///-------------------------------------------------------------------------

SSHumanPlayer* RUGameState::GetShootForGoalHuman() const
{
	return shoot_for_goal_human;
}

///-------------------------------------------------------------------------
/// Set the human player that will take the shot at goal.
///-------------------------------------------------------------------------

void RUGameState::SetPenaltyDecisionHuman(SSHumanPlayer* penalty_decision_human_)
{
	penalty_decision_human = penalty_decision_human_;
}

///-------------------------------------------------------------------------
/// Set the Rugby character in charge of the decision consequence.
///-------------------------------------------------------------------------
void RUGameState::SetMarkingRugbyCharacter(ARugbyCharacter * inCharacter)
{
	marking_rugby_character = inCharacter;
}

///-------------------------------------------------------------------------
/// Set the human player that will take the shot at goal.
///-------------------------------------------------------------------------

SSHumanPlayer* RUGameState::GetPenaltyDecisionHuman() const
{
	return penalty_decision_human;
}

ARugbyCharacter* RUGameState::GetMarkingRugbyCharacter()
{
	return marking_rugby_character;
}

///-------------------------------------------------------------------------
/// Set the position of the last kick.
///-------------------------------------------------------------------------

void RUGameState::SetLastKickPosition( const FVector& position )
{
	last_kick_position = position;
}

///-------------------------------------------------------------------------
/// Get the position of the last kick.
///-------------------------------------------------------------------------

const FVector& RUGameState::GetLastKickPosition() const
{
	return last_kick_position;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::SetLastTryAttempter(ARugbyCharacter* Inball_holder)
{
	last_try_attempter = Inball_holder;
	last_try_attempter_human = Inball_holder->GetHumanPlayer();
}

///-------------------------------------------------------------------------
/// Get last human player to attempt try.
///-------------------------------------------------------------------------

SSHumanPlayer *RUGameState::GetLastTryAttempterHuman() const
{
	return last_try_attempter_human;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::SetTryAttemptPosition(const FVector& position)
{
	try_attempt_position = position;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

ARugbyCharacter* RUGameState::GetLastTryAttempter() const
{
	return last_try_attempter;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

const FVector& RUGameState::GetTryAttemptPosition() const
{
	return try_attempt_position;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::SetTryScoredInTackle( bool valid )
{
	try_scored_in_regular_tackle = valid;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

bool RUGameState::GetTryScoredInTackle() const
{
	return try_scored_in_regular_tackle;
}

//-------------------------------------------------------------------------
//
//-------------------------------------------------------------------------

ARugbyCharacter* RUGameState::CheckBallHolderPlacement()
{
	MABASSERT( game != NULL );

	ASSBall* ball = game->GetBall();
	if(ball->GetTryCheck())
	{
		return NULL; // the try has already been awarded for this phase
	}

	// ball holder may have put it down already
	const BallFreeInfo& bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	bool was_ball_lost = bfi.event == BFE_KNOCKON || bfi.event == BFE_FUMBLE;
    
    //Nirupam: This is commented as this always sets a ball holder even though there is none.
    //So effectively we always have a ball holder even though no one has the ball.
	/*if ( ball_holder == NULL && !was_ball_lost )
	{
		ball_holder = GetLastBallHolder();
	}*/

	/// This can happen as the game loads
	//MABASSERT( ball_holder != NULL );
	if ( ball_holder == NULL )
		return NULL;


	// Check if a penalty was initiated
	if ( game->GetRules()->IsPenaltyTriggerActive() )
	{
		return NULL;
	}

	// ARB - Moved this check as it could result in player with ball getting stuck being repeatedly tackled in their try zone or
	// having multiple players from the same team trying to dive onto the ball evey time it was put down in the try zone.
	// Check if the other team has advantage, in which case try is denied
// 	if (game->GetGameState()->GetAdvantageTeam() && game->GetGameState()->GetAdvantageTeam() != ball_holder->GetAttributes()->GetTeam())
// 		return NULL;

	// Possible fix for #12748
	if( game->GetRules()->GetPendingTrigger() && game->GetRules()->GetPendingTrigger()->GetType() & RURT_DEADBALL )
		return NULL;

	// Check ball position
	ATTACK_PLAYZONE zone = game->GetSpatialHelper()->GetAttackPlayZone(ball_holder->GetAttributes()->GetPlayDirection(), ball->GetCurrentPosition().z);
	if ( zone != ATTACK_INGOAL_ZONE && zone != ATTACK_TRY_ZONE )
	{
		return NULL;
	}

	if( !ball->IsValidTryPosition() )
	{
		return NULL;
	}

	if ( ball_holder->GetActionManager()->IsActionRunning( ACTION_KICK ) )
	{
		return NULL;
	}

	// If ballholder was assigned GTB role, then only dive and cradle dive are valid try actions
	if ( ball_holder->GetActionManager()->IsActionRunning( ACTION_GETTHEBALL ) )
	{
		RUActionGetTheBall* gtb_action = ball_holder->GetActionManager()->GetAction< RUActionGetTheBall >();
		if ( gtb_action->GetCollectType() != GTB_ACTION::DIVE && gtb_action->GetCollectType() != GTB_ACTION::CRADLE_DIVE )
			return NULL;
	}

	// If ball holder is the ruckscrum half then he is just doing a ball pickup
	if ( ball_holder->GetRole() && ball_holder->GetRole()->RTTGetType() == RURoleRuckScrumHalf::RTTGetStaticType() &&
		 ball_holder->GetActionManager()->IsActionRunning(ACTION_TRY) == false )
	{
		return NULL;
	}

	//----------------------------------------------------
	// Calculate some useful variables based on the balls
	// position
	//----------------------------------------------------

	FVector ball_pos = ball->GetVisualPosition();

	// TYRONE : If fps is low then sometimes reading visual position can be more
	// problematic
	// On Score Try animations - be more lenient

	bool grounded = false;
	float video_ref_try_prob = 1.0f;
	bool try_running = ball_holder->GetActionManager()->IsActionRunning( ACTION_TRY );
	bool is_ball_holder = GetBallHolder() != NULL;

	const float GROUNDED_HEIGHT_MIN = 0.3f;  // same as value used in RL3
	if ( try_ball_down )
	{
		grounded = true;
		try_ball_down = false;
	}
	else if ( try_running )
	{
		RUActionScoreTry* try_action = ball_holder->GetActionManager()->GetAction<RUActionScoreTry>();
		grounded = try_action->IsTouchingDown();
	}
	else if ( is_ball_holder )
	{
		// If the try action is not running, only check grounded if we have a current ball holder. Previous ball holder is *not* valid.
		grounded = (ball_pos.y < GROUNDED_HEIGHT_MIN);
	}
	else
	{
		return NULL;
	}

	// check for held up
	if ( ball_holder->GetActionManager()->IsActionRunning(ACTION_TACKLEE) )
	{
		RUActionTacklee *tacklee_action = ball_holder->GetActionManager()->GetAction<RUActionTacklee>();

		// if the tackle animation is complete and the ball has not been grounded, the ball is held up
		if ( tacklee_action->GetState() == TS_POST_TACKLE )
		{
			if ( !grounded )
			{
				//MABLOGDEBUG( "RUGameState::CheckBallHolderPlacement: Video Ref Try Probability: HeldUp" );

				// If held up, go to TMO, but with no chance of being awarded.
				SetVideoRefTryProbability(0.0f);
				SetLastTryAttempter(ball_holder);
				SetTryAttemptPosition( ball_holder->GetMovement()->GetCurrentPosition() );

				game->GetRules()->SuspendPlay( true, "RUGameState::CheckBallHolderPlacement 1");		// Stop double try..

				events->commentary_held_up();
				events->held_up();
				events->video_referee_deliberation();
				return(NULL);
			}
		}
		else if ( tacklee_action->GetTackleResult().is_try_tackle && tacklee_action->IsTouchingDown() )
		{
			grounded = true;
		}

		video_ref_try_prob = tacklee_action->GetTackleResult().prob_video_ref_try;
	}

	// Check for own ingoal
	if(zone == ATTACK_INGOAL_ZONE)
	{
		// If the ball is dead, the dropout takes precedence.
		if ( grounded && !ball->IsDead() )
			events->touchdown_own_ingoal();

		return NULL;
	}

	// Check if the other team has advantage, in which case try is denied
	if (game->GetGameState()->GetAdvantageTeam() && game->GetGameState()->GetAdvantageTeam() != ball_holder->GetAttributes()->GetTeam())
	{
		if (grounded)
		{
			events->try_denied_from_advantage();
		}
		return NULL;
	}

	if ( !grounded )
	{
		return NULL;
	}

	try_ball_down = false;

	const float MAXIMUM_TMO_AWARD_PROBABILITY = 1.0f;

#ifdef ENABLE_GAME_DEBUG_MENU
	if( SIFDebug::GetRulesDebugSettings()->GetForceVideoRef() )
	{
		if( SIFDebug::GetRulesDebugSettings()->GetForceVideoRefResult() )
			video_ref_try_prob = MAXIMUM_TMO_AWARD_PROBABILITY - 0.001f;
		else
			video_ref_try_prob = 0.0f;
	}
#endif

	if(video_ref_try_prob <MAXIMUM_TMO_AWARD_PROBABILITY)
	{
		// T.M.O! (Video ref).
		//MABLOGDEBUG( "RUGameState::CheckBallHolderPlacement: Video Ref Try Probability : %f", video_ref_try_prob );
		SetVideoRefTryProbability(video_ref_try_prob);
		SetLastTryAttempter(ball_holder);
		SetTryAttemptPosition( ball_holder->GetMovement()->GetCurrentPosition() );

		game->GetRules()->SuspendPlay( true, "RUGameState::CheckBallHolderPlacement 2");

		events->video_referee_deliberation();

		return(NULL);
	}

	// Award try.
	//MABLOGDEBUG( "RUGameState::CheckBallHolderPlacement: Try Awarded" );

	events->try_result( true, false, ball_holder );

	// Training field NMA's if phase is changed ( no way to get back to game phase play )
	if( game->GetGameSettings().game_settings.game_type != GAME_TRAINING && game->GetGameSettings().game_settings.game_type != GAME_MENU)
	{
		SetPhase( RUGamePhase::TRY_REACTION );
	}

	game->GetRules()->SuspendPlay( true, "RUGameState::CheckBallHolderPlacement 3");		// Stop double try..

	return ball_holder;
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------
void RUGameState::Tackle( const RUTackleResult& tackle_result )
{
	BallFreeInfo info = game->GetStrategyHelper()->GetLastBallFreeInfo();
	MABUNUSED(info);
	if ( tackle_result.tackle_result != TRT_TRY || tackle_result.try_tackle_type != TRY_TACKLE_TYPE::TTT_JUMPOVER )
	{
		events->tackle( tackle_result );

		// Apply some dirt to the tacklee
		static const float DIRT_INCREMENT_PER_TACKLE = 0.02f;
		tackle_result.tacklee->GetState()->IncrementDirtLevel( DIRT_INCREMENT_PER_TACKLE );

		for( int i = 0; i < tackle_result.n_tacklers; ++i )
		{
			// Apply dirt to tacklers too
			tackle_result.tacklers[i]->GetState()->IncrementDirtLevel( DIRT_INCREMENT_PER_TACKLE );
		}

		AwardMetresGained( tackle_result.tacklee );

#ifdef ENABLE_GAME_DEBUG_MENU
		// Check if debug option is set to force scrum after this tackle
		if (SIFDebug::GetRulesDebugSettings()->GetNextTackleGoesToScrum())
		{
			// Force a scrum consequence
			game->GetRules()->StartConsequence(RUC_SCRUM);
			return;
		}
#endif
	}
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::TackleContested( const RUTackleResult& tackle_result )
{
	events->tackle_contested(tackle_result);
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::StripResult( const RUTackleResult& tackle_result, ARugbyCharacter* stripper, STRIP_RESULT strip_result )
{
	if ( strip_result != SR_FAIL )
	{
		// only tell monitor about full/partial/penalty strips
		// param is full_strip, which is true if the result is ST_SUCCESS
		//monitor->Stripped( strip_result == SR_SUCCESS );
	}

	// commentary always wants to know the strip result
	events->strip_result( tackle_result, stripper, strip_result );
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::BrokenTackle( const RUTackleResult& tackle_result )
{
	events->broken_tackle( tackle_result );
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::Held( const RUTackleResult& tackle_result )
{
	MABUNUSED(tackle_result);

//	if ( game->IsFrozen() )
//	{
//		return;
//	}

	// In the case of is_video_ref && GetTryScoredInTackle, defer game state change to
	// RL3RuleTriggerAttackTackledVideoRefOppInGoal

	// In the case of is_try_tackle, defere game state change to
	// RL3RuleTriggerAttackTackledHeldupOppInGoal. In very rare cases
	// RL3RuleTriggerAttackTackledHeldupOppInGoal doesnt fire however
	// because the tackle animation didn't take them into ATTACK_TRY_ZONE

	//ATTACK_PLAYZONE zone = game->GetAttackPlayZone( tackle_result.tacklee->GetPlayDirection(), game->GetBall()->GetCurrentPosition().z );
	//if( ( tackle_result.is_video_ref && game->GetTryScoredInTackle() ) || ( tackle_result.is_try_tackle && zone == ATTACK_TRY_ZONE ) )
	//{
	//	//game->IncrementTackleCount();
	//	monitor->Tackle( tackle_result );
	//	return;
	//}

	//if ( tackle_result.tackle_result != TRT_NO_TACKLE && game->GetTackleCount() < MAX_TACKLES )
	//{
	//	if ( Goto(GAME_STATE_PLAY_THE_BALL) )
	//	{
	//		//RL3Player* ball_holder = game->GetBallHolder();
	//		//MABASSERT( ball_holder != NULL );
	//		//ball_holder->SetTargetPosition( ball_holder->GetCurrentPosition() );
	//		game->IncrementTackleCount();
	//		events->held( tackle_result );
	//		monitor->Tackle( tackle_result );
	//		//game->SetPlayRestartPosition( game->GetBall()->GetCurrentPosition() );
	//	}
	//}
	//else if ( tackle_result.tackle_result != TRT_NO_TACKLE && game->GetTackleCount() >= MAX_TACKLES )
	//{
	//	Handover();
	//}
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::HandlingError( ARugbyCharacter* player, bool from_tackle, float new_ball_angle, float new_distance )
{
	if ( player && !( MabMath::Fabs(player->GetMovement()->GetCurrentPosition().x) <  FIELD_WIDTH * 0.5f ) )
	{
		// Prevent a player going for a quick lineout from committing a handling error.
		// Go to a lineout instead. Don't allow handling errors to occur outside the field of play.

		// WJS RLC #######What todo here? Do we goto scrum?? Assert for now
		MABASSERTMSG(false, "WJS RLC #######What todo here ? Do we goto scrum ?");
		events->lineout_signalled( game->GetGameState()->GetPlayRestartPosition(), false ); // in HandlingError()
		return;
	}

	// Set the ball holder to the player and then NULL to change
	// the attacking team
	game->GetGameState()->SetBallHolder( player, true );
	game->GetGameState()->SetBallHolder( NULL, false );

	// Calculate a sensible direction and speed to move the ball with.

	// defaults to FLT_MAX which means that we need to calculate the new ball direction, if new_ball_angle is not FLT_MAX
	// it means that the new angle has been given.
	// If the player is moving slow the ball will bounce in the direction he is facing.
	// If the player is moving fast the ball will bounce in the direction he is moving.

	FVector new_ball_dir( FVector::ZeroVector );

	// If we've been passed in an angle and direction then use this
	if (new_ball_angle != FLT_MAX && new_distance != FLT_MAX)
	{
		MabMatrix::MatrixMultiply(new_ball_dir, FVector(0.0f, 0.0f, new_distance), MabMatrix::RotMatrixY(new_ball_angle));
	}
	else if (player->GetMovement()->GetCurrentSpeed() < 0.3f)
	{
		// If we're going slow then just knock it on ahead of us
		MabMatrix::MatrixMultiply(new_ball_dir, FVector(0.0f, 0.0f, 0.4f) , MabMatrix::RotMatrixY(player->GetMovement()->GetCurrentFacingAngle()));
	}
	else
	{
		static const float KNOCKON_MULTIPLIER = 0.1f;
		// If we're going fast
		new_ball_dir = player->GetMovement()->GetCurrentVelocity() * KNOCKON_MULTIPLIER;
	}

	//calculate the balls new movement
	float new_angle = SSMath::CalculateAngle( new_ball_dir );
	float new_dist	= new_ball_dir.Magnitude();

	//calculate the ball being a knock on or a fumble (fumbles bounce backwards)
	bool knockons_allowed = game->GetGameSettings().game_settings.custom_rule_knock_ons;
	bool is_knock_on = new_ball_dir.z * player->GetAttributes()->GetPlayDirection() > 0.0f;
	if( is_knock_on && knockons_allowed )
	{
		events->knock_on( player, from_tackle, player->GetMovement()->GetCurrentPosition() );
	}
	else
	{
		events->fumble( player, from_tackle, player->GetMovement()->GetCurrentPosition() );
	}

	game->GetBall()->PropelBall( new_angle, new_dist );
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::Injury( ARugbyCharacter* injured_player, TACKLE_INJURY_TYPE injury_type )
{
	//SetLastInjuredPlayer( player );
	events->injury( injured_player, injury_type );

	SetPhase(RUGamePhase::INJURY);
	events->cutscene_injury( injured_player, injury_type );
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::HeldTooLong( ARugbyCharacter* tackler, ARugbyCharacter* tacklee )
{
	//monitor->HeldTooLong( tackler, tacklee );
	events->commentary_held_too_long( tackler, tacklee );
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::NotifySuspension( ARugbyCharacter* player, SUSPENSION_RESULT suspension_result )
{
	MABASSERT( suspension_result != SUSPENSION_NONE );

	bool suspend_player = suspension_result == SUSPENSION_RED_CARD;

	if( !suspend_player )
	{
		events->yellow_card( player );
	}
	else
	{
		events->red_card( player );

		// TODO... (Mark.B)
		//if (game->GetTournamentManager()->IsActive())
		//{
		//	game->AddSuspension((RL3DB_PLAYER*)player->GetDbPlayer());
		//}
	}
}


///-------------------------------------------------------------------------
/// SetDebugSetPlay
///-------------------------------------------------------------------------

#ifdef ENABLE_GAME_DEBUG_MENU
void RUGameState::SetDebugSetPlay(SETPLAY *data)
{
	debug_setplay = data;
}
#endif


#if WITH_EDITOR && !UE_BUILD_SHIPPING
///-------------------------------------------------------------------------
///  Force a line out.
///-------------------------------------------------------------------------
void RUGameState::DebugForceLineOut()
{
	FieldExtents extents = game->GetSpatialHelper()->GetFieldExtents();
	FieldExtents extents_nig = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	extents.x *= 0.5f;
	extents.y *= 0.5f;

	// On field, start a line-out

	RUTeam *restart_team = game->GetTeam(0);

	SetPlayRestartPosition( FVector(extents.x, 0.0f, 0.0f) );
	SetPlayRestartTeam(restart_team);

	SetPhase(RUGamePhase::LINEOUT);

	/// Fire an event to let others know a lineout will now begin

	events->ball_out( nullptr, game->GetBall()->GetCurrentBallOutDeadPosition(), true );
	events->pre_lineout_signalled();  
	events->lineout_signalled( game->GetGameState()->GetPlayRestartPosition(), false ); // in DebugForceLineOut()

	DebugClearRolesAndActions();
}

///-------------------------------------------------------------------------
/// Force a conversion.
///-------------------------------------------------------------------------

void RUGameState::DebugForceConversion()
{
	RUTeam *restart_team = game->GetTeam(0);

	float play_dir = (float)restart_team->GetPlayDirection();

	FieldExtents extents_nig = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	extents_nig.y *= 0.5f;

	SetPlayRestartPosition( FVector(0.0f,0.0f,(extents_nig.y - 22.0f) * play_dir) );
	SetPlayRestartTeam(restart_team);
	SetKickRestartKickType( KICKTYPE_PLACEKICK );

	events->kick_at_posts_ready( game->GetGameState()->GetBallHolder(), true );

	SetPhase(RUGamePhase::CONVERSION);

	DebugClearRolesAndActions();
}

///-------------------------------------------------------------------------
/// Force a kickoff
///-------------------------------------------------------------------------

void RUGameState::DebugForceKickOff()
{

	RUTeam* restart_team = game->GetTeam(0);

	SetPlayRestartPosition( FVector(0.0f,0.0f,0.0f) );
	SetPlayRestartTeam(restart_team);
	SetKickRestartKickType( KICKTYPE_KICKOFF );

	SetPhase(RUGamePhase::KICK_OFF);

	SetAttackingTeam(restart_team);
	//monitor->WhistleRestart();
	events->commentary_restart( restart_team->GetPlayKicker(), game->GetBall()->GetCurrentPosition() );
	events->game_restart();

	DebugClearRolesAndActions();
}

///-------------------------------------------------------------------------
/// Force a dropout.
///-------------------------------------------------------------------------

void RUGameState::DebugForceDropOut()
{
	events->touchdown_own_ingoal();

	DebugClearRolesAndActions();
}

///-------------------------------------------------------------------------
/// Force a scrum.
///-------------------------------------------------------------------------

void RUGameState::DebugForceScrum()
{
	SetPlayRestartPosition( FVector::ZeroVector );

	if(ball_holder != 0)
	{
		SetAttackingTeam(ball_holder->GetAttributes()->GetTeam());
		SetPlayRestartTeam(ball_holder->GetAttributes()->GetTeam());
	}
	else if(last_ball_holder != 0)
	{
		SetAttackingTeam(last_ball_holder->GetAttributes()->GetTeam());
		SetPlayRestartTeam(last_ball_holder->GetAttributes()->GetTeam());
	}

	game->GetRules()->StartConsequence(RUC_SCRUM);
}

///-------------------------------------------------------------------------
/// Force a FreeKick.
///-------------------------------------------------------------------------

void RUGameState::DebugForceFreeKick()
{
	if(ball_holder != 0)
	{
		SetAttackingTeam(ball_holder->GetAttributes()->GetTeam());
		SetPlayRestartTeam(ball_holder->GetAttributes()->GetTeam());
	}
	else if(last_ball_holder != 0)
	{
		SetAttackingTeam(last_ball_holder->GetAttributes()->GetTeam());
		SetPlayRestartTeam(last_ball_holder->GetAttributes()->GetTeam());
	}
	SetKickRestartKickType(KICKTYPE_FREEKICK);
	SetPhase(RUGamePhase::FREE_KICK);

	DebugClearRolesAndActions();
}


///-------------------------------------------------------------------------
/// Force a penalty kick for touch.
///-------------------------------------------------------------------------

void RUGameState::DebugForcePenaltyKickTouch()
{
	if(ball_holder != 0)
	{
		SetAttackingTeam(ball_holder->GetAttributes()->GetTeam());
		SetPlayRestartTeam(ball_holder->GetAttributes()->GetTeam());
	}
	else if(last_ball_holder != 0)
	{
		SetAttackingTeam(last_ball_holder->GetAttributes()->GetTeam());
		SetPlayRestartTeam(last_ball_holder->GetAttributes()->GetTeam());
	}
	SetPhase(RUGamePhase::PENALTY_KICK_FOR_TOUCH);

	DebugClearRolesAndActions();
}

///-------------------------------------------------------------------------
/// Force a penalty kick at goal.
///-------------------------------------------------------------------------

void RUGameState::DebugForcePenaltyKickGoal()
{
	if(ball_holder != 0)
	{
		SetAttackingTeam(ball_holder->GetAttributes()->GetTeam());
		SetPlayRestartTeam(ball_holder->GetAttributes()->GetTeam());
	}
	else if(last_ball_holder != 0)
	{
		SetAttackingTeam(last_ball_holder->GetAttributes()->GetTeam());
		SetPlayRestartTeam(last_ball_holder->GetAttributes()->GetTeam());
	}
	SetPhase(RUGamePhase::PENALTY_SHOOT_FOR_GOAL);

	DebugClearRolesAndActions();
}

///-------------------------------------------------------------------------
/// Force a penalty tap and go
///-------------------------------------------------------------------------

void RUGameState::DebugForcePenaltyTapAndGo()
{
	if(ball_holder != 0)
	{
		SetAttackingTeam(ball_holder->GetAttributes()->GetTeam());
		SetPlayRestartTeam(ball_holder->GetAttributes()->GetTeam());
	}
	else if(last_ball_holder != 0)
	{
		SetAttackingTeam(last_ball_holder->GetAttributes()->GetTeam());
		SetPlayRestartTeam(last_ball_holder->GetAttributes()->GetTeam());
	}

	//SetPlayRestartPosition( FVector(0.0f,0.0f,0.0f) );
	SetPlayRestartPosition(game->GetBall()->GetCurrentPosition());

	SetPhase(RUGamePhase::PENALTY_TAP_RESTART);

	DebugClearRolesAndActions();
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::DebugForcePenaltyAwarded()
{
	if(ball_holder != 0)
		events->penalty(ball_holder, NULL, FVector(0.0f, 0.0f, 0.0f), PENALTY_REASON_HEAD_HIGH_TACKLE);
	else if(last_ball_holder != 0)
		events->penalty(last_ball_holder, NULL, FVector(0.0f, 0.0f, 0.0f), PENALTY_REASON_HEAD_HIGH_TACKLE);



}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

void RUGameState::DebugClearRolesAndActions()
{
	const SIFRugbyCharacterList& players = game->GetPlayers();

	// Clear roles and actions...
	// Iterate over all of the actors in the game
	for( size_t i = 0; i < players.size(); i++ )
	{
		ARugbyCharacter* player = players[i];
		if(player->GetAttributes()->GetTeam()!=game->GetOfficialsTeam())
		{
			player->ClearRole();
			player->GetActionManager()->AbortAllActions();
		}
	}
}
#endif // WITH_EDITOR && !UE_BUILD_SHIPPING

// Helper functions when we simulate matches in progress by time
void RUGameState::ForceRandomLineout()
{
	MABLOGDEBUG("RUGameState::ForceRandomLineout():");

	FieldExtents extents = game->GetSpatialHelper()->GetFieldExtents();
	FieldExtents extents_nig = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	float halfextentsx = extents.x * 0.5f;

	// Pick a random restart team
	RUTeam *restart_team = game->GetTeam(MabMath::RandInt(2));

	// Pick a random position on the field
	SetPlayRestartPosition( FVector(	(MabMath::RandInt(2) == 0 ? -halfextentsx : halfextentsx),
		0.0f,
		MabMath::Rand(extents_nig.y) - (extents_nig.y * 0.5f)));
	SetPlayRestartTeam(restart_team);

	SetPhase(RUGamePhase::PLAY);

	events->ball_out( nullptr, game->GetBall()->GetCurrentBallOutDeadPosition(), true );
	events->pre_lineout_signalled(); 
	events->lineout_signalled( GetPlayRestartPosition(), false ); // In ForceRandomLineout()
}

void RUGameState::ForceRandomScrum()
{
	FieldExtents extents_nig = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	float xExtent = extents_nig.x - 10.0f;
	float zExtent = extents_nig.y - 10.0f;

	// Pick a random position on the field for a scrum
	SetPlayRestartPosition(  FVector(
		 MabMath::Rand(xExtent) - (xExtent * 0.5f),
		 0.0f,
		 MabMath::Rand(zExtent) - (zExtent * 0.5f))
		 );

	// Pick a random team to feed
	RUTeam *restart_team = game->GetTeam(MabMath::RandInt(2));
	SetAttackingTeam(restart_team);
	SetPlayRestartTeam(restart_team);

	SetPhase(RUGamePhase::PLAY);

	game->GetRules()->StartConsequence(RUC_SCRUM);
}

void RUGameState::ResetFormationTargets()
{
	formation_targets.fill(nullptr);
}

//-------------------------------------------------------------------------
// #MLW Increment tackle count during tackles 
//-------------------------------------------------------------------------
void RUGameState::IncrementTackleCount()
{
	m_currentTackles += 1;
	game->GetEvents()->tackle_count_changed(m_currentTackles);
}

//-------------------------------------------------------------------------
// #MLW Reset tackle counter
//-------------------------------------------------------------------------
void RUGameState::ResetTackleCount()
{
	m_currentTackles = 0;
	game->GetEvents()->tackle_count_changed(m_currentTackles);
}

//-------------------------------------------------------------------------
// GGs JZ Are we currently on 0 tackles?
//-------------------------------------------------------------------------
const bool RUGameState::IsZeroTackle()
{
	return m_currentTackles == 0;
}

//-------------------------------------------------------------------------
// #MLW Are we currently on 5 tackles?
//-------------------------------------------------------------------------
const bool RUGameState::IsFifthTackle()
{
	return m_currentTackles == MAX_TACKLES - 1;
}

//-------------------------------------------------------------------------
// #MLW Is this the 6th tackle?
//-------------------------------------------------------------------------
const bool RUGameState::IsSixthTackle()
{
	return m_currentTackles == MAX_TACKLES;
}

void RUGameState::Handover()
{
	RUGameState* state = game->GetGameState();
	if (state)
	{
		SetPhase(RUGamePhase::PLAY_THE_BALL);
	}
}

EHandoverType RUGameState::GetHandoverType() const
{
	return current_handover_type;
}

void RUGameState::SetHandoverType(EHandoverType NewType)
{
	current_handover_type = NewType;
}

ARugbyCharacter* RUGameState::GetClosestPlayerToBall(RUTeam* Team)
{
	FVector ballPos = game->GetBall()->GetCurrentPosition();

	float closestDist = FLT_MAX;
	ARugbyCharacter* closestPlayer = nullptr;

	for (int i = 0; i < Team->GetNumPlayersOnField(); i++)
	{
		ARugbyCharacter* player = Team->GetPlayer(i);
		FVector playerPos = player->GetMovement()->GetCurrentPosition();

		float dist = FVector::Dist(playerPos, ballPos);
		if (dist < closestDist)
		{
			closestDist = dist;
			closestPlayer = player;
		}
	}
	return closestPlayer;
}

bool RUGameState::OutOnFullInOwnGoalZone(ARugbyCharacter* player)
{
	const float TRY_LINE_Z = HALF_FIELD_LENGTH;           // 50.0f
	const float IN_GOAL_END_Z = TRY_LINE_Z + FIELD_IN_GOAL_LENGTH; // 70.0f
	const float posZ = player->GetMovement()->GetCurrentPosition().Z;
	ERugbyPlayDirection playDirection = game->GetGameState()->GetLastBallHolder()->GetAttributes()->GetPlayDirection();
	bool bInOwnGoalZone = false;

	if (playDirection == ERugbyPlayDirection::SOUTH)
	{
		bInOwnGoalZone = (posZ >= TRY_LINE_Z && posZ <= IN_GOAL_END_Z);
	}
	else if (playDirection == ERugbyPlayDirection::NORTH)
	{
		bInOwnGoalZone = (posZ <= -TRY_LINE_Z && posZ >= -IN_GOAL_END_Z);
	}
	if(bInOwnGoalZone && game->GetBall()->IsOnTheFull())
	{
		return true;
	}
	else
		return false;
}

void RUGameState::UpdateClosestDefenderToTackler()
{
	ClosestToTackler = nullptr;

	ARugbyCharacter* ballHolder = GetBallHolder();
	if (!ballHolder) return;

	ARugbyCharacter* tackler = game->GetGameState()->GetClosestPlayerToBall(game->GetGameState()->GetDefendingTeam());
	if (!tackler) return;

	RUTeam* team = tackler->GetAttributes()->GetTeam();
	if (!team) return;

	float closestDist = FLT_MAX;

	for (int i = 0; i < team->GetNumPlayersOnField(); ++i)
	{
		ARugbyCharacter* teammate = team->GetPlayer(i);
		if (!teammate || teammate == tackler) continue;

		float dist = FVector::Dist(teammate->GetMovement()->GetCurrentPosition(), tackler->GetMovement()->GetCurrentPosition());

		if (dist < closestDist)
		{
			closestDist = dist;
			ClosestToTackler = teammate;
		}
	}
}

bool RUGameState::IsNearFieldEdge(const FVector& position, float minDistanceFromTouchLine, float minDistanceFromTryLine)
{
	const float MAX_X = HALF_FIELD_WIDTH;   // e.g., 35
	const float MAX_Z = HALF_FIELD_LENGTH;  // e.g., 50

	return FMath::Abs(position.X) > MAX_X - minDistanceFromTouchLine ||
		FMath::Abs(position.Z) > MAX_Z - minDistanceFromTryLine;
}

FVector RUGameState::ClampAwayFromFieldEdge(const FVector& position, float minDistanceFromTouchLine, float minDistanceFromTryLine)
{
	const float MAX_X = HALF_FIELD_WIDTH;
	const float MAX_Z = HALF_FIELD_LENGTH;

	FVector adjusted = position;

	if (FMath::Abs(adjusted.X) > MAX_X - minDistanceFromTouchLine)
	{
		adjusted.X = (MAX_X - minDistanceFromTouchLine) * FMath::Sign(adjusted.X);
	}

	if (FMath::Abs(adjusted.Z) > MAX_Z - minDistanceFromTryLine)
	{
		adjusted.Z = (MAX_Z - minDistanceFromTryLine) * FMath::Sign(adjusted.Z);
	}

	return adjusted;
}

const IRUOrigin* RUGameState::GetFormationTarget(ERugbyFormationTarget index)
{
	MABASSERT(formation_targets.at(size_t(index)) != nullptr);
	return formation_targets.at(size_t(index));
}

void RUGameState::SetFormationTarget(ERugbyFormationTarget index, const IRUOrigin* target)
{
	formation_targets.at(size_t(index)) = target;
}

void RUGameState::SetDefaultBallOrigin(FVector origin)
{
	default_formation_origin->origin = origin;
	default_formation_origin_project_vel->origin = origin;
	default_formation_origin_project_vel_z->origin = origin;
}

float RUGameState::GetEstimatedTimeTillBallBackInPlay()
{
	wwNETWORK_TRACE_JG("RUGameState GetEstimatedTimeTillBallBackInPlay");
	/// Cover the standard scenarios
	if ( phase == RUGamePhase::RUCK )
	{
		RUGamePhaseRuck* ruck_phase = GetPhaseHandler<RUGamePhaseRuck>();
		RUTeam* winning_team;
		const static float BUFFER = 0.5f;
		return ruck_phase->GetExpectedTimeBallClear( winning_team ) + BUFFER;

	/// Lineouts
	} else if ( phase == RUGamePhase::LINEOUT ) {
		RUGamePhaseLineOut* lineout_phase = GetPhaseHandler<RUGamePhaseLineOut>();
		return lineout_phase->GetEstimatedTimeTillBallBackInPlay();

	/// Scrums
	} else if ( phase == RUGamePhase::SCRUM ) {
		RUGamePhaseScrum* scrum_phase = GetPhaseHandler<RUGamePhaseScrum>();
		return scrum_phase->GetEstimatedTimeTillBallBackInPlay();

	/// Kickoffs
	} else if ( phase == RUGamePhase::KICK_OFF ) {
		wwNETWORK_TRACE_JG("RUGameState GetEstimatedTimeTillBallBackInPlay RUGamePhase::KICK_OFF");
		RUGamePhaseKickoff* kickoff_phase = GetPhaseHandler<RUGamePhaseKickoff>();
		return kickoff_phase->GetEstimatedTimeTillBallBackInPlay();

	/// Dropouts
	} else if ( phase == RUGamePhase::DROPOUT ) {
		RUGamePhaseDropOut* dropout_phase = GetPhaseHandler<RUGamePhaseDropOut>();
		return dropout_phase->GetEstimatedTimeTillBallBackInPlay();
	/// Standard play
	} else if ( IsGameInStandardPlay() ) {
		return 0.0f;
	} else {
		/// Must be in stoppage
		return 20.0f;
	}
}

//MabEvent< ARugbyCharacter* >						breakdown_possible;		///< Possibility of a breakdown situation occurring (breakdown ball holder/potential holder)
//MabEvent< ARugbyCharacter* >						breakdown_start;		///< Breakdown has started	(breakdown holder)
//MabEvent< ARugbyCharacter*, ARugbyCharacter* >		breakdown_contested;	///< Breakdown contested	(breakdown holder, first contester)
//MabEvent< ARugbyCharacter* >						breakdown_end;			///< Breakdown has ended	(breakdown holder)

/// Breakdown state
void RUGameState::BreakdownPossible( ARugbyCharacter* breakdown_holder_ )
{
	breakdown_holder = breakdown_holder_;

	/// Changes made when state is actually changing
	if ( breakdown_state != RUBreakdownState::POSSIBLE )
	{
		game->GetEvents()->breakdown_possible( breakdown_holder );
		BreakdownSetRoleCount( breakdown_holder->GetAttributes()->GetTeam(), (int)(breakdown_holder->GetAttributes()->GetTeam()->GetStrategy().GetRuckCommittalCount()) );
		BreakdownSetRoleCount( breakdown_holder->GetAttributes()->GetOppositionTeam(), (int)(breakdown_holder->GetAttributes()->GetOppositionTeam()->GetStrategy().GetRuckCommittalCount()) );
	}

	breakdown_state = RUBreakdownState::POSSIBLE;
}

void RUGameState::BreakdownStart( ARugbyCharacter* breakdown_holder_ )
{
	//MABLOGDEBUG( "RUGameState::BreakdownStart: holder(%d)", breakdown_holder_->GetAttributes()->GetIndex() );

	MABASSERT( breakdown_state != RUBreakdownState::ACTIVE );

	breakdown_holder = breakdown_holder_;
	/// Changes made when state is actually changing
	if ( breakdown_state != RUBreakdownState::ACTIVE )
	{
		#ifdef ENABLE_OSD
		RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
		settings->PushDebugString( game, RUGameDebugSettings::DP_BREAKDOWN, MabString( 32, "Start %d", breakdown_holder->GetAttributes()->GetIndex() ).c_str() );
		#endif

		game->GetEvents()->breakdown_start( breakdown_holder );

		BreakdownSetRoleCount( breakdown_holder->GetAttributes()->GetTeam(), -1 );
		BreakdownSetRoleCount( breakdown_holder->GetAttributes()->GetOppositionTeam(), -1 );
	}
	breakdown_state = RUBreakdownState::ACTIVE;
}

void RUGameState::BreakdownFinish( ARugbyCharacter* /*breakdown_holder_*/ )
{
	//MABLOGDEBUG( "RUGameState::BreakdownFinish: holder(%d), state(%d)", breakdown_holder ? breakdown_holder->GetAttributes()->GetIndex() : -1, breakdown_state );

	//MABASSERT( breakdown_state != BD_NONE );

	if ( breakdown_holder )
	{
		BreakdownSetRoleCount( breakdown_holder->GetAttributes()->GetTeam(), -1 );
		BreakdownSetRoleCount( breakdown_holder->GetAttributes()->GetOppositionTeam(), -1 );
	}

	if ( breakdown_state != RUBreakdownState::NONE && breakdown_holder)
	{
		#ifdef ENABLE_OSD
		RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
		settings->PushDebugString( game, RUGameDebugSettings::DP_BREAKDOWN, MabString( 32, "Start %d", breakdown_holder->GetAttributes()->GetIndex() ).c_str() );
		#endif

		game->GetEvents()->breakdown_end( breakdown_holder );
	}

	breakdown_holder = NULL;
	breakdown_state = RUBreakdownState::NONE;
}

void RUGameState::UpdateBreakdown()
{
	/// Check for cases where the breakdown has ended
	bool in_standard_play = IsGameInStandardPlay();
	bool breakdown_over = !in_standard_play || (breakdown_holder != NULL && breakdown_holder != ball_holder && !(GetPhase() == RUGamePhase::RUCK || GetPhase() == RUGamePhase::MAUL) );

	if ( breakdown_over && (breakdown_state == RUBreakdownState::POSSIBLE || breakdown_state == RUBreakdownState::ACTIVE ) )
	{
		if(breakdown_holder!=NULL)
		{
			//MABLOGDEBUG( "RUGameState::UpdateBreakdown: player(%d), state(%d), over(%d)", breakdown_holder->GetAttributes()->GetIndex(), breakdown_state, breakdown_over );
		}
		BreakdownFinish( breakdown_holder );
	}
}

void RUGameState::BreakdownSetRoleCount( RUTeam* bd_team, int num_players )
{
	bd_team->GetFormationManager()->OverrideAreaNumPlayers( BREAKDOWN_SUPPORT_ZONE_NAME, num_players, ERugbyFormationRole::FORMATION );
}

float RUGameState::GetTimeSinceKickOff() const
{
	float time = time_since_last_kick_restart.ToSeconds();
	return time;
}

float RUGameState::GetTimeSinceLineout() const
{
	float time = time_since_last_lineout.ToSeconds();
	return time;
}

float RUGameState::GetTimeSincePhaseChange() const
{
	float time = time_since_phase_change.ToSeconds();
	return time;
}

///------------------------------------------------------------
/// GameEvent -> player_deleted.
///------------------------------------------------------------

void RUGameState::OnPlayerDeleted(ARugbyCharacter* player)
{
	if(player==ball_holder)
		SetBallHolder(NULL);

	if(player==last_ball_holder)
		last_ball_holder = NULL;

	if(player==last_try_attempter)
		last_try_attempter = NULL;

	if(player==last_try_scorer)
		last_try_scorer = NULL;

	if(player==last_kicker)
		last_kicker = NULL;

	if(player==last_offending_player)
		last_offending_player = NULL;

	if(player==last_offended_player)
	{
		last_offended_player = NULL;
		last_offended_human = NULL;
	}

	if(player==last_player_to_score)
		last_player_to_score = NULL;

	if(player==breakdown_holder)
		breakdown_holder = NULL;
}


///------------------------------------------------------------
/// GameEvent -> Advantage
///------------------------------------------------------------

void RUGameState::OnAdvantageStart(ARugbyCharacter* offending_player)
{
	advantage_team = offending_player->GetAttributes()->GetTeam()->GetSide() == SIDE_A? game->GetTeam(SIDE_B) : game->GetTeam(SIDE_A);
}

void RUGameState::OnAdvantageEnd(ARugbyCharacter* /*offending_player*/)
{
	advantage_team = NULL;
}

//-----------------------------------------------------------
RUTeam* RUGameState::GetNorthTeam() const
{
	RUTeam* team = NULL;

	if ( attacking_team )
		team = attacking_team->GetPlayDirection() == ERugbyPlayDirection::NORTH ? attacking_team : defending_team;

	return team;
}

//-----------------------------------------------------------
RUTeam* RUGameState::GetSouthTeam() const
{
	RUTeam* team = NULL;

	if ( attacking_team )
		team = attacking_team->GetPlayDirection() == ERugbyPlayDirection::SOUTH ? attacking_team : defending_team;

	return team;
}

void RUGameState::SetLastOffendedPlayer(ARugbyCharacter* player)
{
	last_offended_player = player;
	last_offended_human  = NULL;
	if ( last_offended_player )
		last_offended_human = last_offended_player->GetHumanPlayer();
}

///-------------------------------------------------------------------
///
void RUGameState::ResetMetresGainedFlag(int new_metres_gained_flag)
{
	metres_gained_flag_z = new_metres_gained_flag;
}

///-------------------------------------------------------------------
///
void RUGameState::AwardMetresGained( ARugbyCharacter* player )
{
	MABASSERT( player );

	if( player )
	{
		float metres_gained = ( player->GetMovement()->GetCurrentPosition().z - metres_gained_flag_z ) * player->GetAttributes()->GetPlayDirection();
		if( metres_gained > 0.0f )
		{
			game->GetEvents()->running_metres_gained( player, metres_gained );
		}
		ResetMetresGainedFlagToPlayerPosition( player );
	}
	else
	{
		ResetMetresGainedFlagToBallPosition();
	}
}

///-------------------------------------------------------------------
///
void RUGameState::AwardKickingMetresGained( ARugbyCharacter* player, const FVector& ball_position )
{
	MABASSERT( player );

	if( player )
	{
		float metres_gained = ( ball_position.z - metres_gained_flag_z ) * player->GetAttributes()->GetPlayDirection();
		if( metres_gained > 0.0f )
		{
			game->GetEvents()->kicking_metres_gained( player, metres_gained );
		}
	}
	ResetMetresGainedFlag( MabMath::Round( ball_position.z ) );
}

///-------------------------------------------------------------------
///
void RUGameState::ResetMetresGainedFlagToPlayerPosition( ARugbyCharacter* player )
{
	MABASSERT( player );
	if( player )
	{
		ResetMetresGainedFlag( MabMath::Round( player->GetMovement()->GetCurrentPosition().z ) );
	}
	else
	{
		ResetMetresGainedFlagToBallPosition();
	}
}

///-------------------------------------------------------------------
///
void RUGameState::ResetMetresGainedFlagToBallPosition()
{
	ResetMetresGainedFlag( MabMath::Round( game->GetBall()->GetCurrentPosition().z ) );
}



