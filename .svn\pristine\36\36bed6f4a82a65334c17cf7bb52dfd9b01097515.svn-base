/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/Rules/Triggers/RURuleTriggerBallout.h"

#include "Match/Ball/SSBall.h"
#include "Match/Camera/SSCameraManager.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUInputPhaseDecision.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSGameTimer.h"
#include "Match/SSSpatialHelper.h"



//#rc3_legacy_include #include "NMMabAnimationNetwork.h"

MABRUNTIMETYPE_IMP1(RURuleTriggerBallout, RURuleTrigger);

static const char* ANKLE_BONE_NAMES[] = { "leftAnkle", "rightAnkle" };

RURuleTriggerBallout::RURuleTriggerBallout(RURules* rules)
: RURuleTrigger(rules)
, decision_required(false)
, restart_position(0.0f, 0.0f, 0.0f)
, restart_notified( false )
, last_breakdown_pos(FVector::ZeroVector)
, carried_out(false)
{
	MABASSERTMSG(game && game->GetEvents(),"we need events here");
	game->GetEvents()->phase_changed.Add(this, &RURuleTriggerBallout::OnPhaseChanged);
}

RURuleTriggerBallout::~RURuleTriggerBallout()
{
	if (game && game->GetEvents())
	{
		game->GetEvents()->phase_changed.Remove(this, &RURuleTriggerBallout::OnPhaseChanged);
	}
}

void RURuleTriggerBallout::AttachMonitors()
{
	MABASSERTMSG(game && game->GetEvents(),"we need events here");
	game->GetEvents()->ball_out_detected.Add(this, &RURuleTriggerBallout::OnBallOut);

	// WJS RLC NOT NEEDED
	/*
	{
		game->GetEvents()->ruck_formed.Add(this, &RURuleTriggerBallout::OnRuckFormed);
		game->GetEvents()->maul_formed.Add(this, &RURuleTriggerBallout::OnMaulFormed);
		game->GetEvents()->lineout_ready.Add(this, &RURuleTriggerBallout::OnLineoutFormed);
	}
	//*/


	game->GetEvents()->scrum_start.Add(this, &RURuleTriggerBallout::OnScrumFormed);
	game->GetEvents()->pass.Add( this, &RURuleTriggerBallout::OnPass );
}

void RURuleTriggerBallout::DeattachMonitors()
{
	if (game && game->GetEvents())
	{
		game->GetEvents()->ball_out_detected.Remove(this, &RURuleTriggerBallout::OnBallOut);

		// WJS RLC NOT NEEDED IN LEAGUE
		/*
		{
			game->GetEvents()->ruck_formed.Remove(this, &RURuleTriggerBallout::OnRuckFormed);
			game->GetEvents()->maul_formed.Remove(this, &RURuleTriggerBallout::OnMaulFormed);
			game->GetEvents()->lineout_ready.Remove(this, &RURuleTriggerBallout::OnLineoutFormed);
		}
		//*/

		game->GetEvents()->scrum_start.Remove(this, &RURuleTriggerBallout::OnScrumFormed);
		game->GetEvents()->pass.Remove( this, &RURuleTriggerBallout::OnPass );
	}
}

void RURuleTriggerBallout::Enter()
{
	timer.Reset(rules->GetGame()->GetSimTime(), 0.5f);
}

void RURuleTriggerBallout::Update(float)
{
	if(game->GetGameState()->GetPhase() != RUGamePhase::DECISION)
	{
		if (timer.GetNumTimerEventsRaised() > 0)
		{
			if(decision_required && !restart_notified)
			{
				/// Give the ball to someone on the restart team otherwise RURoleTapRestart will be the last attacking team
				/// And they will choose

				BallFreeInfo ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();
				ARugbyCharacter* kicker = ball_free_info.last_player;
				game->GetGameState()->SetPlayRestartTeam( restart_team );
				game->GetGameState()->SetLastBallWasCarriedOut(false);

				// Trigger cutscene
				game->GetEvents()->restart_out_on_full( restart_position, kicker);
				restart_notified = true;
			}
			else 
			if( !decision_required )
			{
				game->GetGameState()->SetPlayRestartPosition(restart_position);
				game->GetGameState()->SetPlayRestartTeam( restart_team );
				game->GetGameState()->SetAttackingTeam( restart_team );
				game->GetGameState()->SetLastBallWasCarriedOut(carried_out);

				rules->StartConsequence(consequence);
			}
		}
	}
}

void RURuleTriggerBallout::OnRuckFormed( ARugbyCharacter*  /*player*/, SIFRugbyCharacterList * /*tacklers*/)
{
	last_breakdown_pos = game->GetBall()->GetCurrentPosition();
}

void RURuleTriggerBallout::OnMaulFormed( ARugbyCharacter*  /*player*/, ARugbyCharacter*  )
{
	last_breakdown_pos = game->GetBall()->GetCurrentPosition();
}

void RURuleTriggerBallout::OnLineoutFormed( ARugbyCharacter* /*thrower*/ )
{
	last_breakdown_pos = game->GetBall()->GetCurrentPosition();

	// Since game->GetSpatialHelper()->IsInDef22 checks if "x pos < FIELD_WIDTH / 2.0f" we need to clamp it to that, otherwise it will fail.
	if(last_breakdown_pos.x > 35.0f)
	{
		last_breakdown_pos.x = (FIELD_WIDTH / 2.0f) - 0.5f;
	}
	else
	{
		last_breakdown_pos.x = (-FIELD_WIDTH / 2.0f) + 0.5f;
	}
}

void RURuleTriggerBallout::OnScrumFormed( RUTeam*, ARugbyCharacter* )
{
	last_breakdown_pos = game->GetBall()->GetCurrentPosition();
}

bool RURuleTriggerBallout::HandleFortyTwentyKick(ARugbyCharacter* pKicker, RUTeam* pKickerTeam, const FVector& position)
{
	bool ret = false;
	const BallFreeInfo& ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();

	// Was the last kick a 4020 kick?
	if (ball_free_info.kick_context == KC_FOURTYTWENTY)
	{
		UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); FORTY TWENTY CHECK"));
		FVector lastBouncePosition = game->GetBall()->GetLastInPlayBounce();

		if (MabMath::Abs(lastBouncePosition.x) <= FIELD_WIDTH * 0.5f)
		{
			// Half field length + last bounce position (centre field is z 0)
			const float lastBounceDist = FIELD_LENGTH * 0.5f + MabMath::Abs(lastBouncePosition.z);

			// Greater or equal to 20 meters
			if (lastBounceDist >= 80.0f)
			{
				UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); FORTY TWENTY AWARDED"));
				//FString msg = "LAST BOUNCE WAS BEHIND THE 20 METER LINE ^^^^^^";
				//FString msg = "AWARDED 4020!";
				//GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Orange, *msg);

				// Yep
				game->GetGameState()->SetAwarded4020kick(true);
				game->GetGameState()->SetPlayRestartTeam(pKickerTeam);
				
				// Ensure we cannot restart closer than 10m
				const float dir = (float)pKicker->GetAttributes()->GetPlayDirection();
				FVector playRestartPosition = MabMath::Abs(position.z) >= 40.0f ? FVector(position.x, position.y, 40.0f * dir) : position;

				game->GetGameState()->SetPlayRestartPosition(playRestartPosition);
				game->GetEvents()->fourtytwenty_kick_awarded(playRestartPosition, ball_free_info.last_player, ball_free_info.kick_context);
				ret = true;
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); FORTY TWENTY LAST BOUNCE WAS BEFORE THE 20 METER LINE"));
				//FString msg = "LAST BOUNCE WAS BEFORE THE 20 METER LINE VVVVVVVV";
				//GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Red, *msg);
			}
		}
	}

	return ret;
}

bool RURuleTriggerBallout::HandleTwentyFortyKick(ARugbyCharacter* pKicker, RUTeam* pKickerTeam, const FVector& position)
{
	bool ret = false;
	const BallFreeInfo& ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();

	// Was the last kick a 2040 kick?
	if (ball_free_info.kick_context == KC_TWENTYFOURTY)
	{
		UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); TWENTY FORTY CHECK"));
		FVector lastBouncePosition = game->GetBall()->GetLastInPlayBounce();

		if (MabMath::Abs(lastBouncePosition.x) <= FIELD_WIDTH * 0.5f)
		{
			// Half field length + last bounce position (centre field is z 0)
			const float lastBounceDist = FIELD_LENGTH * 0.5f + MabMath::Abs(lastBouncePosition.z);

			// Greater or equal to 40 meters
			if (lastBounceDist >= 60.0f)
			{
				UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); TWENTY FORTY AWARDED"));
				//FString msg = "LAST BOUNCE WAS BEHIND THE 40 METER LINE ^^^^^^";
				//FString msg = "AWARDED 2040!";
				//GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Orange, *msg);

				// Yep
				game->GetGameState()->SetAwarded4020kick(true);
				game->GetGameState()->SetPlayRestartTeam(pKickerTeam);

				// Ensure we cannot restart closer than 10m
				const float dir = (float)pKicker->GetAttributes()->GetPlayDirection();
				FVector playRestartPosition = MabMath::Abs(position.z) >= 40.0f ? FVector(position.x, position.y, 40.0f * dir) : position;

				game->GetGameState()->SetPlayRestartPosition(playRestartPosition);
				game->GetEvents()->fourtytwenty_kick_awarded(playRestartPosition, ball_free_info.last_player, ball_free_info.kick_context);
				ret = true;
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); TWENTY FORTY LAST BOUNCE WAS BEFORE THE 40 METER LINE"));

				//FString msg = "LAST BOUNCE WAS BEFORE THE 40 METER LINE VVVVVVVV";
				//GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Red, *msg);
			}
		}
	}

	return ret;
}


///// Not needed refer cole 20250325 bool RURuleTriggerBallout::HandleR7BalloutOnKickoff(RUGamePhase previous_phase, RUTeam* pKickerTeam)
///// Not needed refer cole 20250325 {
///// Not needed refer cole 20250325 	bool ret = false;
///// Not needed refer cole 20250325 
///// Not needed refer cole 20250325 	if (game->GetGameSettings().game_settings.GameModeIsR7() && previous_phase == RUGamePhase::KICK_OFF)
///// Not needed refer cole 20250325 	{
///// Not needed refer cole 20250325 		UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); GameModeIsR7 and previous_phase was KICK_OFF: restart_team = kicker_team->GetOppositionTeam()"));
///// Not needed refer cole 20250325 		
///// Not needed refer cole 20250325 		decision_required = false;
///// Not needed refer cole 20250325 		//Non offending team gets to restart
///// Not needed refer cole 20250325 		restart_team = static_cast<RUTeam*>(pKickerTeam->GetOppositionTeam());
///// Not needed refer cole 20250325 		restart_position = FVector::ZeroVector;
///// Not needed refer cole 20250325 
///// Not needed refer cole 20250325 		RUGameEvents* events = game->GetEvents();
///// Not needed refer cole 20250325 
///// Not needed refer cole 20250325 		RUInputPhaseDecision* decisions = game->GetInputManager()->GetDecisionInterface();
///// Not needed refer cole 20250325 
///// Not needed refer cole 20250325 		MABASSERT(decisions->GetNumConsequences() == 0);
///// Not needed refer cole 20250325 
///// Not needed refer cole 20250325 		{	// TAP DECISION
///// Not needed refer cole 20250325 			RUDecisionConsequence con_tap;
///// Not needed refer cole 20250325 			con_tap.decision = RUC_PENALTY_TAP;
///// Not needed refer cole 20250325 			con_tap.restart_team = restart_team;
///// Not needed refer cole 20250325 			con_tap.restart_position = restart_position;
///// Not needed refer cole 20250325 			con_tap.restart_player = NULL;
///// Not needed refer cole 20250325 			con_tap.decision_event = &events->free_kick_tap_decision;
///// Not needed refer cole 20250325 			decisions->AddConsequence(con_tap);
///// Not needed refer cole 20250325 		}
///// Not needed refer cole 20250325 
///// Not needed refer cole 20250325 		{	// SCRUM DECISION
///// Not needed refer cole 20250325 			RUDecisionConsequence con_scrum;
///// Not needed refer cole 20250325 			con_scrum.decision = RUC_SCRUM;
///// Not needed refer cole 20250325 			con_scrum.restart_team = restart_team;
///// Not needed refer cole 20250325 
///// Not needed refer cole 20250325 			/// Scrum should be halfway
///// Not needed refer cole 20250325 			con_scrum.restart_position = restart_position;
///// Not needed refer cole 20250325 			con_scrum.restart_position.y = 0.0f;
///// Not needed refer cole 20250325 			con_scrum.restart_player = NULL;
///// Not needed refer cole 20250325 			con_scrum.decision_event = &events->restart_out_full_scrum_decision;
///// Not needed refer cole 20250325 			decisions->AddConsequence(con_scrum);
///// Not needed refer cole 20250325 		}
///// Not needed refer cole 20250325 
///// Not needed refer cole 20250325 		/*
///// Not needed refer cole 20250325 		//	WJS RLS ##### Not sure if we have ruck rekicks in league games??
///// Not needed refer cole 20250325 		//	is this just a play on now??
///// Not needed refer cole 20250325 			Not needed
///// Not needed refer cole 20250325 		{	// REKICK DECISION
///// Not needed refer cole 20250325 			RUDecisionConsequence con_rekick;
///// Not needed refer cole 20250325 			con_rekick.decision			= RUC_REKICK;
///// Not needed refer cole 20250325 			con_rekick.restart_team		= pKickerTeam;
///// Not needed refer cole 20250325 			con_rekick.restart_position = FVector::ZeroVector;
///// Not needed refer cole 20250325 			con_rekick.restart_player	= NULL;
///// Not needed refer cole 20250325 			con_rekick.decision_event	= &events->restart_out_full_rekick_decision;
///// Not needed refer cole 20250325 			decisions->AddConsequence(con_rekick);
///// Not needed refer cole 20250325 		}
///// Not needed refer cole 20250325 		//*/
///// Not needed refer cole 20250325 
///// Not needed refer cole 20250325 		{	// FREE KICK DECISION
///// Not needed refer cole 20250325 			RUDecisionConsequence con_freekick;
///// Not needed refer cole 20250325 			con_freekick.decision			= RUC_FREE_KICK;
///// Not needed refer cole 20250325 			con_freekick.restart_team		= restart_team;
///// Not needed refer cole 20250325 			con_freekick.restart_position	= restart_position;
///// Not needed refer cole 20250325 			con_freekick.restart_player		= NULL;
///// Not needed refer cole 20250325 			con_freekick.decision_event		= &events->free_kick_kick_decision;
///// Not needed refer cole 20250325 			decisions->AddConsequence(con_freekick);
///// Not needed refer cole 20250325 		}
///// Not needed refer cole 20250325 
///// Not needed refer cole 20250325 		ret = true;
///// Not needed refer cole 20250325 	}
///// Not needed refer cole 20250325 
///// Not needed refer cole 20250325 
///// Not needed refer cole 20250325 	return ret;
///// Not needed refer cole 20250325 }
//*/

bool RURuleTriggerBallout::HandleBalloutFromKickoffOrDropout(RUGamePhase previous_phase, RUTeam* pKickerTeam,bool on_full)
{
	bool ret = false;

	if ((previous_phase == RUGamePhase::KICK_OFF || previous_phase == RUGamePhase::DROPOUT) && on_full)
	{
		UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); HandleBalloutFromKickoffOrDropout() onThe full: %d"), (on_full ? 1 : 0));
		// if this is the end of the half or end of game, don't do decisions, let it go through
		// the normal flow, which will end the half
		if (!game->GetGameTimer()->IsExpired())
		{
			// Changes from HES. In sevens there should be a free kick option, which is and option between
			// Was this an R7 game and handled?
			// Not needed Refer Cole 20250325 if (false == HandleR7BalloutOnKickoff(previous_phase, pKickerTeam))
			{
				// No, this is a normal game so add decisions
				const BallFreeInfo& ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();

				UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); previous_phase was %d: "), (int)previous_phase);

				RUGameEvents* events = game->GetEvents();
				RUInputPhaseDecision* decisions = game->GetInputManager()->GetDecisionInterface();
				RUDecisionConsequence con_scrum;
				RUDecisionConsequence con_rekick;
				RUDecisionConsequence con_lineout;

				MABASSERT(decisions->GetNumConsequences() == 0);

				{	// SCRUM DECISION
					con_scrum.decision = RUC_SCRUM;
					con_scrum.restart_team = restart_team;

					/// Scrum should be on the 22 or halfway
					if (previous_phase == RUGamePhase::KICK_OFF)
						con_scrum.restart_position = FVector::ZeroVector;
					else
					{
						// WJS RLC ####### Do we need to change scrum at 22 line??
						// Yeah it should be on the 20 line
						float M20_LINE = (FIELD_LENGTH * 0.5f) - 20.0f;   // - 22.0f
						con_scrum.restart_position.z = M20_LINE * -ball_free_info.last_player->GetAttributes()->GetPlayDirection();
						con_scrum.restart_position.x = 0.0f;
					}
					con_scrum.restart_position.y = 0.0f;
					con_scrum.restart_player = NULL;
					con_scrum.decision_event = &events->restart_out_full_scrum_decision;
					decisions->AddConsequence(con_scrum);
				}

				/* As far as I can tell this is not needed
				// WJS RLS ##### Not sure if we have ruc rekicks in league games??
				{	// REKICK DECISION
					con_rekick.decision = RUC_REKICK;
					con_rekick.restart_team = pKickerTeam;
					con_rekick.restart_position = ball_free_info.pos;

					// HES - re-kick should be taken from the center.
					if (previous_phase == RUGamePhase::KICK_OFF)
						con_rekick.restart_position = FVector::ZeroVector;

					con_rekick.restart_position.y = 0.0f;
					con_rekick.restart_player = NULL;
					con_rekick.decision_event = &events->restart_out_full_rekick_decision;
					decisions->AddConsequence(con_rekick);
				}
				//*/

				UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); Decision required"));

				decision_required = true;
				rules->SetTrigger(this);
				ret = true;
			}

		}
	}
	return ret;
}

bool RURuleTriggerBallout::HandleBalloutNormalPlay(RUGamePhase previous_phase,bool on_full, RUTeam* pKickerTeam)
{
	bool ret = false;
	const BallFreeInfo& ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();
	if (previous_phase == RUGamePhase::PLAY || previous_phase == RUGamePhase::FREE_KICK) // FROM NORMAL PLAY
	{
		UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); HandleBalloutNormalPlay() onThe full: %d"), (on_full ? 1 : 0));
		if (on_full)
		{
			// ##############################################################
			// WJS RLS TODO , CHANGE 22 to 20 once the field art is updated
			// ##############################################################
			
			// The following logic is responsible for determining if there is a gain in ground from the ball being kicked out on the full from
			// inside the defenders 22.
			// A kick results in gain in ground if it was collected inside their 22 and then kicked out from inside their 22 (opponents brought ball in).
			// Or if the ball was passed into the 22 area from a player outside the 22.
			BallCollectInfo lbci = game->GetStrategyHelper()->GetLastBallCollectInfo();
			BallCollectInfo bci = game->GetStrategyHelper()->GetLastPossesionChangeBallCollectInfo();

			bool last_player_on_team_in_22 = false;
			if (bci.last_player && bci.last_player->GetAttributes()->GetTeam() == pKickerTeam)
			{
				last_player_on_team_in_22 = game->GetSpatialHelper()->IsInDef20(bci.pos, pKickerTeam->GetPlayDirection());
			}

			bool picked_up_in_22 = false;
			if (lbci.last_player && lbci.event == BCE_PICKUP)
			{
				if (lbci.last_player->GetAttributes()->GetTeam() == pKickerTeam)
				{
					picked_up_in_22 = game->GetSpatialHelper()->IsInDef20(lbci.pos, pKickerTeam->GetPlayDirection());
				}
			}

			const FVector& kicker_pos = lbci.last_player ? lbci.last_player->GetMabPosition() : FVector::ZeroVector;
			const FVector& passer_pos = last_pass_pos;

			bool kicker_was_in_22 = game->GetSpatialHelper()->IsInDef20(kicker_pos, pKickerTeam->GetPlayDirection()); // Player caught ball inside 22m.
			bool passer_not_in_22 = !game->GetSpatialHelper()->IsInDef20(passer_pos, pKickerTeam->GetPlayDirection()); // From a player outside 22m.

			bool passed_back_into_22 = lbci.event == BCE_CATCH && kicker_was_in_22 && passer_not_in_22;

			bool collected_inside_22 = last_player_on_team_in_22 || picked_up_in_22 || passed_back_into_22 || game->GetSpatialHelper()->IsInDef20(last_breakdown_pos, pKickerTeam->GetPlayDirection());

			bool kicked_inside_22 = game->GetSpatialHelper()->IsInDef20(ball_free_info.pos, pKickerTeam->GetPlayDirection());
			bool valid_kick_from_22 = collected_inside_22 && kicked_inside_22;

			// If the ball was on the full and kicked from outside the 22 and not a penalty kick, take back to kick position
			if (!valid_kick_from_22)
			{
				// only setting Z so that the ball X pos is not taken from kick position
				restart_position.z = ball_free_info.pos.z;
			}
		}

		ret = true;
	}

	return ret;
}

bool RURuleTriggerBallout::HandleBalloutHeldByPlayer()
{
	UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); Player holding the ball past touch line"));
	const BallFreeInfo& ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();
	ARugbyCharacter* ballholder = game->GetGameState()->GetBallHolder();

	// Rules change: Catching the ball outside of the field of play with 1 foot out means its your fault.
	// Both feet out makes it the kicking teams fault.
	BoneIndex bone_idx;
	FVector bone_pos;
	bool ankle_bone_out_sideline[2];

	for (int i = 0; i < 2; ++i)
	{
		// Test ankle bones to see where feet are.
		bone_idx = ballholder->FindBone(ANKLE_BONE_NAMES[i]);
		bone_pos = ballholder->GetBoneWorldPosition(bone_idx);
		ankle_bone_out_sideline[i] = game->GetSpatialHelper()->IsJointOutOrTouchingSides(bone_pos);
	}

	MABLOGDEBUG("RURuleTriggerBallout::OnBallOut(); ballholder's Team = %s", ballholder->GetAttributes()->GetTeam()->GetDbTeam().GetName());

	// Changed this to also trigger if the ball holders feet are both inside the field, as being tackle over the line can cause this situaiton.
	if (false == (ankle_bone_out_sideline[0] && ankle_bone_out_sideline[1]))
	{
		// If only 1 foot is out, its the ballholder's fault the ball went out.
		UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); ballholder's fault the ball went out: restart_team = BallHolder's Opposition Team"));
		restart_team = (RUTeam*)ballholder->GetAttributes()->GetTeam()->GetOppositionTeam();
		carried_out = true;
	}
	else 
	if (ballholder->GetActionManager()->IsActionRunning(ACTION_TACKLEE))
	{
		//cover situation where the ball holder was yeeted(with both ankles) over the field by a tackle
		UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); ballholder's fault the ball went out, because they were tackled: restart_team = BallHolder's Opposition Team"));
		restart_team = (RUTeam*)ballholder->GetAttributes()->GetTeam()->GetOppositionTeam();
		carried_out = true;
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); ballholder is out of field..."));

		// The following code handles the case of the ballholder catching the ball out of the field of play.
		// Be warned, this code can be reached when a player runs the ball out of bounds,
		// as the out of bounds doesn't trigger until a foot is close to the ground (planted).
		if (ball_free_info.last_player)
		{
			MABLOGDEBUG("Ball free event: %i", ball_free_info.event);
			MABLOGDEBUG("Ball free ball_free_info.game_phase_when_released: %i", ball_free_info.game_phase_when_released);
			MABLOGDEBUG("Ball free last player team: %s", ball_free_info.last_player->GetAttributes()->GetTeam()->GetDbTeam().GetName());

			// RussellD:
			// I've simplified the code used here, making use of the ball collection position to ascertain whether the ball was caught out of bounds.
			// This mimicks the behaviour of the quick lineout detection code in the SSBall::Update.
			// I'm still a little perplexed why the lineout code behaves like it does below, so I've left the code in full but commented out for now.

			FVector lastCollectBallPosition		= game->GetStrategyHelper()->GetLastBallCollectInfo().pos;
			bool lastCollectBallOutsideField	= !game->GetSpatialHelper()->IsInField(lastCollectBallPosition);

			if (lastCollectBallOutsideField)
			{
				if (ball_free_info.event == BFE_KICK && ball_free_info.game_phase_when_released == RUGamePhase::PENALTY_KICK_FOR_TOUCH)
				{
					// Special case to handle the possibility of an opponent catching the ball from a kick for touch penalty.
					UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); ball_free_info.event == BFE_KICK && ball_free_info.game_phase_when_released == RUGamePhase::PENALTY_KICK_FOR_TOUCH: restart_team = ball_free_info.last_player->GetAttributes()->GetTeam()"));
					restart_team = (RUTeam*)ball_free_info.last_player->GetAttributes()->GetTeam();
				}
				else
				{
					// Special case to handle when the ball is collected out of bounds
					UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); ball collected outside field: restart_team = ball_free_info.last_player->GetAttributes()->GetOppositionTeam()"));
					restart_team = (RUTeam*)ball_free_info.last_player->GetAttributes()->GetOppositionTeam();
				}
			}
			else
			{
				// Case for if the ball was dragged out of bounds, and both feet left the field together
				UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); ball collected inside field: restart_team = ballholder->GetAttributes()->GetOppositionTeam()"));
				restart_team = (RUTeam*)ballholder->GetAttributes()->GetOppositionTeam();
				carried_out = true;
			}

			/* Old logic for the ballout trigger, leaving here for quick reference, especially because it has been a problem area
			if ( ball_free_info.event == BFE_LINEOUT_THROW )
			{
				// There is a chance that the last ball free event generated was from a lineout throw.
				// If you get the ball off the opposing teams' throw in, and run it out, and the above ankle logic doesn't detect you running the ball out,
				// the wrong team is given the throw in. This fixes that.
				UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); ball_free_info.event == BFE_LINEOUT_THROW: restart_team = ball_free_info.last_player->GetAttributes()->GetTeam()"));
				restart_team = (RUTeam*)ball_free_info.last_player->GetAttributes()->GetTeam();
			}
			else if ( ball_free_info.event == BFE_KICK && ball_free_info.game_phase_when_released == RUGamePhase::PENALTY_KICK_FOR_TOUCH )
			{
				// Special case to handle the possibility of an opponent catching the ball from a kick for touch penalty.
				UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); ball_free_info.event == BFE_KICK && ball_free_info.game_phase_when_released == RUGamePhase::PENALTY_KICK_FOR_TOUCH: restart_team = ball_free_info.last_player->GetAttributes()->GetTeam()"));
				restart_team = (RUTeam*)ball_free_info.last_player->GetAttributes()->GetTeam();
			}
			else if ( ball_free_info.event == BFE_KICK && ball_free_info.game_phase_when_released == RUGamePhase::KICK_OFF )
			{
				// From a kick off, and the kick off team touched it last?
				UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); ball_free_info.event == BFE_KICK && ball_free_info.game_phase_when_released == RUGamePhase::KICK_OFF: restart_team = ballholder->GetAttributes()->GetTeam()"));
				restart_team = (RUTeam*)ballholder->GetAttributes()->GetTeam();
			}
			else if (ball_free_info.event == BFE_RUCKBALLRELEASED)
			{
				// If ball is stolen from a ruck the player carrying the ball out of the ruck can incorrectly
				// trigger this and award the ball to the wrong team if the ball was not passed.
				UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); ball_free_info.event == BFE_RUCKBALLRELEASED: restart_team = ballholder->GetAttributes()->GetOppositionTeam()"));
				restart_team = (RUTeam*)ballholder->GetAttributes()->GetOppositionTeam();
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); no ball_free_info conditions met: restart_team = ballholder->GetAttributes()->GetTeam()"));
				restart_team = (RUTeam*)ballholder->GetAttributes()->GetTeam();// ->GetOppositionTeam();
			}
			*/
		}
		else
		{
			// We have a ball holder, and the ball wasn't free. Should mean the ball holder ran it out.
			UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); We have a ball holder, and the ball wasn't free. Should mean the ball holder ran it out: restart_team =  ballholder->GetAttributes()->GetTeam()->GetOppositionTeam()"));
			restart_team	= (RUTeam*)ballholder->GetAttributes()->GetTeam()->GetOppositionTeam();
			carried_out		= true;
		}
	}

	return true;
}

/// listener for the ball_out_detected(ARugbyCharacter* , const FVector& , bool) event
void RURuleTriggerBallout::OnBallOut(ARugbyCharacter* holder, const FVector& position, bool on_full)
{
	UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); holder: %d | position: %f, %f, %f | on_full: %d"), (holder?1:0), position.X, position.Y, position.Z, (on_full?1:0));

	if(decision_required || !game->GetGameState()->IsGameInStandardPlay() || game->GetBall()->GetTryCheck())
	{
		UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); decision required early out"));
		return;
	}
	// set the restart position to where the ball went out in favor of defending team
	restart_position	= position;
	restart_position.y	= 0.0f;
	restart_notified	= false;
	carried_out			= false;
	bool goal_line_drop_out  = false;

	const static float FIVE_METRES_FROM_END_TRYLINE = (FIELD_LENGTH * 0.5f) - 5.0f;
	const static float TEN_METRES_FROM_SIDE_TRYLINE = (FIELD_WIDTH * 0.5f) - 10.0f;

	const BallFreeInfo& ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();
	if (!holder)
	{
		UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); holder is null: holder = ball_free_info.last_player"));
		holder = ball_free_info.last_player;
	}

	if ( holder == NULL )
	{
		UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); holder is still null: holder = game->GetGameState()->GetLastBallHolder()"));
		holder = game->GetGameState()->GetLastBallHolder();
	}

	// Note: This will be replaced if a different condition is hit below, but fixes game from crashing when ball is passed out.
	if (holder)
	{
		UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); holder is Valid: restart_team = BallHolder Opposition Team"));
		restart_team = holder->GetAttributes()->GetOppositionTeam();
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); holder is Not Valid: restart_team = GetGameState()->GetDefendingTeam()"));
		restart_team = game->GetGameState()->GetDefendingTeam();
	}

	RUGamePhase previous_phase = ball_free_info.game_phase_when_released;
	bool out_on_full_from_kickoff_or_dropout = on_full && ( previous_phase == RUGamePhase::KICK_OFF || previous_phase == RUGamePhase::DROPOUT );

	//SRA: Adding this check in to handle players collecting a 40/20 kick outside the field, which should still allow the 40/20 rule to trigger
	FVector lastCollectBallPosition = game->GetStrategyHelper()->GetLastBallCollectInfo().pos;
	bool lastCollectBallOutsideField = !game->GetSpatialHelper()->IsInField(lastCollectBallPosition);

	// If the last event was a kick, we need to do some extra checks
	if ( ball_free_info.event == BFE_KICK && ( game->GetGameState()->GetBallHolder() == NULL || out_on_full_from_kickoff_or_dropout || lastCollectBallOutsideField))
	{
		ARugbyCharacter* kicker = ball_free_info.last_player;
		RUTeam* kicker_team = kicker->GetAttributes()->GetTeam();
		kicker->GetAttributes()->GetPlayDirection();

		//////////////////////////////////////
		// Do 4020 / 2040 kick checks
		//////////////////////////////////////
		if (HandleFortyTwentyKick(kicker,kicker_team,position))
			return;

		if (HandleTwentyFortyKick(kicker,kicker_team,position))
			return;

		/// If the ball was kicked into touch from a penalty before being picked up by the opposition
		/// then it should be awarded to that team
		UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); last event was a kick: restart_team = kicker_team->GetOppositionTeam()"));
		restart_team = (RUTeam*)kicker_team->GetOppositionTeam();

		// On a penalty kick team retains ball
		if (previous_phase == RUGamePhase::PENALTY_KICK_FOR_TOUCH)
		{
			UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); previous_phase was PENALTY_KICK_FOR_TOUCH: restart_team = kicker_team"));
			restart_team = kicker_team;
		}
		else 
		if ((previous_phase == RUGamePhase::KICK_OFF || previous_phase == RUGamePhase::DROPOUT) )// && !on_full)
		{
			if (on_full)
			{
				; // DO NOTHING - logic is already handled
				UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); ((previous_phase == RUGamePhase::KICK_OFF || previous_phase == RUGamePhase::DROPOUT) && on_full)"));
			}
			else
			{
				; // DO NOTHING - logic is already handled
				UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); ((previous_phase == RUGamePhase::KICK_OFF || previous_phase == RUGamePhase::DROPOUT) && !on_full)"));
			}
		}
		else 
		if (HandleBalloutFromKickoffOrDropout(previous_phase,kicker_team,on_full))
		{
			return;
		}
		else 
		if (HandleBalloutNormalPlay(previous_phase,on_full,kicker_team))
		{
			UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); Normal play handled"));
			if (on_full)
			{
				if (game->GetGameState()->OutOnFullInOwnGoalZone(holder))
				{
					restart_team = holder->GetAttributes()->GetTeam();
					goal_line_drop_out = true;
				}
				else
				{
					game->GetGameState()->SetHandoverType(EHandoverType::OUTONFULL);
				}
			}
		}
		else
		{
			MABBREAKMSG( "Unhandled ball out condition" );
		}
	}
	else
	// Ball was not Kicked out, but is it being held by a player?
	if ( game->GetGameState()->GetBallHolder() )
	{
		//Yep, ball was detected out whilst being held by a player
		HandleBalloutHeldByPlayer();
	}

	UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); HH"));

	// Set to the side line
	// make sure the lineout is 5 meters back from tryline
	restart_position.x = game->GetSpatialHelper()->GetFieldExtents().x * (restart_position.x < 0.0f ? -1.0f : 1.0f);
	MabMath::Clamp(restart_position.z, -FIVE_METRES_FROM_END_TRYLINE, +FIVE_METRES_FROM_END_TRYLINE);

	// WJS RLC League rule to start Scrum 10 metres back from side line
	MabMath::Clamp( restart_position.x, -TEN_METRES_FROM_SIDE_TRYLINE, +TEN_METRES_FROM_SIDE_TRYLINE);

	//indicate that this rule trigger has fired for the commentary to react to.
	game->GetEvents()->rule_trigger_ball_out( holder, position, restart_position, on_full, carried_out );

	UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); Final Restart Team Is: %s"), ANSI_TO_TCHAR(restart_team->GetShortName().c_str()));

	// GGs JZ if kicked into touch from penalty then is a free kick 10m in, rule book section 13, 4(a) page 34
	if (previous_phase == RUGamePhase::PENALTY_KICK_FOR_TOUCH)
	{
		FVector new_restart_pos = game->GetGameState()->ClampAwayFromFieldEdge(position, 10.0f, 10.0f);
		game->GetGameState()->SetPlayRestartPosition(new_restart_pos);
		consequence = RUC_FREE_KICK;
		if (game->GetScreenWipeManager())
		{
			game->GetScreenWipeManager()->StartWipe(1.0f, SWIPE_WIPE);
		}
	}
	else if (goal_line_drop_out)
	{	
		restart_position = FVector(0.0f, 0.0f, ((FIELD_LENGTH * 0.5f)) * -restart_team->GetPlayDirection());
		game->GetGameState()->SetPlayRestartPosition(restart_position);
		consequence = RUC_22_DROP_OUT;
	}
	else 
	{
		// WJS RLC ##### BALL OUT TO RUC_SCRUM INSTEAD OF RUC_LINEOUT
		//consequence = RUC_LINEOUT;
		game->GetGameState()->SetPlayRestartPosition(restart_position);
		consequence = RUC_TOUCH_SCRUM;
	}

	rules->SetTrigger(this);

	UE_LOG(LogTemp, Warning, TEXT("RURuleTriggerBallout::OnBallOut(); END"));
}

void RURuleTriggerBallout::Reset()
{
	decision_required = false;
	restart_notified = false;
}

void RURuleTriggerBallout::OnPhaseChanged()
{
	RUGamePhase current_phase = game->GetGameState()->GetPhase();

	if ( current_phase == RUGamePhase::PLAY || current_phase == RUGamePhase::KICK_OFF || current_phase == RUGamePhase::DROPOUT )
		Reset();

	RUGamePhase previous_phase = game->GetGameState()->GetPreviousPhase();
	/// TYRONE : Note the previous phase check for RUGamePhase::PLAY is needed as when doing a kick it seems to fire on phase changed back into phase play
	if ( current_phase == RUGamePhase::PLAY && !(	previous_phase == RUGamePhase::RUCK ||
												previous_phase == RUGamePhase::MAUL ||
												previous_phase == RUGamePhase::PLAY ||
												previous_phase == RUGamePhase::LINEOUT ||
												previous_phase == RUGamePhase::SCRUM))
		last_breakdown_pos = FVector::ZeroVector;
}

void RURuleTriggerBallout::OnPass( ARugbyCharacter* passing_player, ARugbyCharacter* player_to_pass_to, const FVector& /*target_position*/, PASS_TYPE /*type*/, bool success )
{
	if ( success && player_to_pass_to )	// Only track if the pass was successful, other conditions/checks take care of other situations.
	{
		last_pass_pos = passing_player->GetMabPosition();
	}
}
