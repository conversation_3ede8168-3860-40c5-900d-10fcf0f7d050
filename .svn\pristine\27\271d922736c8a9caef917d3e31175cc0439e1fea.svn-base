/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#pragma once
//#define WORLD_ID::GAME		0
//#define WORLD_ID::SANDBOX		1


#define MLW_4020_OVERRIDES()		(false) // Forces players to attempt 4020/2040 kicks as soon as 
											// they receive the ball and are within the correct field ranges

#define MLW_PROP_ROLE_OVERRIDES()	(false)	// Enables testing on allowing PROP player roles to override tackles
											// allowing them to be able to breakthrough the lines a bit easier
											// This was all just testing and not implemented or decided on.

class AActor;
class ARugbyCharacter;
class URugbyGameInstance;
class URugbyLevelChunk;
class ARugbyPauseHandler;

// Mab
class MabNetworkManager;
class MabCamera2;
class MabDebugDraw;
class MabLuaInterpreter;
class MabControlActionManager;
class MabLockStepTimeSource;

// NMMab & PSSG
class NMMabResource;
class NMMabAnimationWorld;
class NMMabAnimationRepository;
class PSSGMabDynamicLightLinkManager;
class PSSGMabShaderManager;
class PSSGMabAnimation;
class PSSGMabAnimationController;

// SIF
//class SIFGameJobQueue;
class SIFGameObject;
class SIFGameContext;
class SIFViewManager;
class SIFGameObjectDatabase;
class SIFEffectSystem;
class SIFBruteForceLightDatabase;
class SIFAmbientPointCloud;
class SIFLevelPostSettings;
class SIFPSSGParticleSystemsManager;
class SIFPSSGShaderParameters;
class SIFAsyncLoadingThread;

// SS
class ASSBall;
class SSCameraManager;
class ARugbyPlayerController;
class SSRoleFactory;
class SSInputManager;
class SSSpatialHelper;
class SSStrategy;
class SSSetPlayData;
class SSGameTimer;
class SSCutSceneManager;
class SSStreamPacker;
class SSPostEffectsManager;
class SSReplayManager;
class SSScreenWipeManager;
class SSHumanPlayer;

// RU
class RUGameRumbleManager;
class RUGameDatabaseManager;
class RUNetworkState;
class URugbyGameWorldSettings; typedef URugbyGameWorldSettings RUGameSettings;
class RUTeam;
class RUGameEvents;
class RUStrategyHelper;
class RUTackleHelper;
class RUGameState;
class RUGameDebugSettings;
class RU3DHUDManager;
class RUHUDUpdater;
class RUHUDUpdaterTraining;
class RUHUDUpdaterContextual;
class RUHUDUpdaterUserStrategy;
class RUHUDUpdaterReplay;
class RUGameLocomotion;
class RULineoutRater;
class RUGameAnimation;
class RUGameMovement;
class RUGameGetToBall;
class RUStatsTracker;
class RURules;
class RUTutorialManager;
class RUCommentary;
class RUCBGameStats2Bucket;
class RUTestBedManager;
class RUStadiumManager;
class RURandomNumberGenerator;
//class RUCrowdManager;
class ACrowdbase;
class ASkyDomeBase;
class AStadiumBase;
class AFieldRugby;
class RUEmotionEngineManager;
class RUPSSGJumboTrons;
class RUPSSGCameraEffects;
class RUHUDUpdaterBase;
class RUWeatherManager;
class RUTeamConfidenceHandler;
class RUSubstitutionManager;
class RUGameWorldAudio;
class PSSGMabDatabaseResourceFile;

class RLPResultList;
class RLP_FILTERPARAMETERS;
struct RLP_SORTER;
class RUPlayerFilters;
class ARugbyCameraActor;

class PlayerCustomisationInfo;

namespace PSSG
{
	typedef unsigned int PDatabaseID;
	class PTimeController;
	class PRootNode;
	class PLightNode;
}

namespace SIFPSSGUtil
{
	class SIFPSSGClonedShaderManager;
	class SIFPSSGSceneCloner;
}

// Stats test manager is only for use with StencilEd live update
#ifdef ENABLE_RUGED
	class RUStatsTestManager;
#include "Match/Debug/RUDebugService.h"
#endif

#include <cstdlib>

#include <CoreMinimal.h>
#include <Kismet/GameplayStatics.h>
#include "Runtime/Engine/Classes/Engine/World.h"

#include "RugbyEnums.h"
#include "Match/RugbyUnion/Enums/SSTeamSideEnum.h"
#include "Match/SIFObjectLists.h"
#include "Match/SIFGraphicsHandle.h"
#include "RugbyAsyncTaskQueue.h"

#include "Containers/Queue.h"
#include "Engine/StreamableManager.h"

#ifdef ENABLE_LIVELINK
#include "LiveLinkInstance.h"
#endif
#include "Rugby.h"

#ifdef ENABLE_SYNCD_RAND_TRACKING
#include <fstream>
#endif // ENABLE_SYNCD_RAND_TRACKING

#define MAX_RUGED_POSITIONS	16

#define WRITE_POS_TO_FILE 0

/// Sandbox environment settings.
enum SANDBOX_ENVIRONMENT
{
	SBE_MENU=0,
	SBE_TRAINING,
	SBE_PLAYER_CUSTOMIZE
};

// Player Swap Requests
enum TEAM_SWAP_REQUEST
{
	TEAM_SWAP_NONE=0
	,TEAM_SWAP_DEFAULT
	//,TEAM_SWAP_CAREER
};

#include "Mab/MabInclude.h"
#include "Mab/Interfaces/MabNonCopyable.h"
#include "Mab/Resources/MabResourceBase.h"
#include "Mab/Time/MabTimeStep.h"
#include "RugbyUnion/RUGameEvents.h"

UENUM()
enum class WORLD_ID : uint8
{
	GAME,
	SANDBOX,
	MENU
};

UENUM()
enum class WORLD_STATE : uint8
{
	NONE,
	ALLOCATING,
	ALLOCATED,
	FREEING,
	FREED,
	SLEEPING,
	ASLEEP,
	AWAKENING,
	AWAKE
};

DECLARE_DELEGATE_OneParam(FGameWorldDelegate, WORLD_ID);
DECLARE_MULTICAST_DELEGATE_OneParam(FGameWorldMulticastDelegate, WORLD_ID);


/* Represents the game simulation. A SIFGameWorld has the lifetime of the play session.
 *
 * Application authors will need to modify this heavily to meet their needs, or tear it out entirely.
 * If you need to something to own a set of all the game objects, this would be a good class to do that.
 *
 * <AUTHOR> Timpany
 *
 ******************************************
 * Modified by Jeremy Lai for Pong:
 * The central class that handles the main execution and will delegate control to the ball and player
 * (which, in turn, will control the paddle, and if necessary, the leaderboard scores).
 *
 * Collision testing for the paddles and ball versus the boundary/goals will occur in here.
 * On particular events, this class will generate a message and notify all listeners of such events
 *
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 * Torn apart by Joe O'Sullivan for SIF2.0
 *
*/


static const char HOME_TEAM_SWAP_COMPLETE_EVENT[] = "onteamchangecomplete";

//#rc3_legacy
class SIFViewManager
{

};

//DECLARE_DELEGATE_RetVal(bool, FRugbyQueuedTask);

class SIFGameWorld : public MabNonCopyable
{
public:
	SIFGameWorld(URugbyGameInstance& gameInstance,
				 RUGameSettings& gameSettings,
				 //MabControlActionManager *control_action_manager
				 MabLockStepTimeSource* simulation_time,
				 WORLD_ID world_id = WORLD_ID::GAME );
	~SIFGameWorld();

	// Pre sync - Post sync variables
	bool bSimulationUpdateThisFrame = false;
	MabTimeStep LastFrameStep;

	bool should_update_sim = false;
	bool bDoMatchOverSim = false;
	bool bTickedPreSyncSim = false;

	void PreSyncUpdate(const MabTimeStep& real_time_step);
	void PostSyncUpdate(const MabTimeStep& real_time_step);

	bool IsMatch()		{ return world_id == WORLD_ID::GAME; }
	bool IsSandbox()	{ return world_id == WORLD_ID::SANDBOX; }
	bool IsMenu()		{ return world_id == WORLD_ID::MENU; }

	bool SetSimulationTimeLock(bool) { /*std::abort();*/ return false; }
	MabLockStepTimeSource* GetSimTimeNonConst() { return simulation_time; }

	void SyncUpdate();
	//#rc3_legacy void Render();

#if defined(ENABLE_SYNCD_RAND_TRACKING) && WRITE_POS_TO_FILE
	std::fstream pos_file;
#endif // ENABLE_SYNCD_RAND_TRACKING

	/// Called when the world is entered - this is either immediate,
	/// or when the game successfully connects to all remote hosts
	//void OnWorldEnter();

	ARugbyCameraActor*				GetWorldCamera() const;
	SIFGameContext*					GetGameContext() const { return game_context.get(); }
	//#rc3_legacy SIFGameObjectDatabase*			GetObjectDatabase() const { return object_database; }
	//#rc3_legacy NMMabAnimationWorld*			GetAnimationManager() const { return animation_manager; }
	MabNetworkManager*				GetNetworkManager() const { return network_manager; }
	SIFEffectSystem*				GetEffectSystem() const { return /*effect_system*/nullptr; }
	RUNetworkState*					GetNetworkState() const { return network_state; }
	//#rc3_legacy MabLuaInterpreter*				GetLuaInterpreter() const;

	void							RequestAsyncLoadStart();

	//#rc3_legacy PSSG::PDatabaseID				GetSceneDatabaseId() const;
	//#rc3_legacy SIFGameJobQueue*				GetJobQueue() const;
	//#rc3_legacy SIFBruteForceLightDatabase*		GetLightDatabase() const;
	//#rc3_legacy PSSGMabShaderManager*			GetShaderManager();
	//#rc3_legacy PSSGMabDynamicLightLinkManager*	GetLightLinkManager();
	//#rc3_legacy SIFViewManager*					GetViewManager() const;
	//#rc3_legacy PSSG::PNode*					GetStadiumNode() const { return stadium_node; }
	//#rc3_legacy void							SetStadiumNode(PSSG::PNode *node, PSSG::PDatabaseID id){ stadium_node = node; pssg_stadium_id=id; }
	//#rc3_legacy PSSGMabAnimation*				GetPssgAnimationManager();
	//#rc3_legacy SIFPSSGUtil::SIFPSSGClonedShaderManager *GetEnvironmentClonedShaderManager();

	//#rc3_legacy SIFAmbientPointCloud*	GetAmbientPointCloud() { return ambient_point_cloud; }

	//#rc3_legacy void DeferredFree(PSSG::PNode* node, bool add_front = false);
	//#rc3_legacy void DeferredAddChild(PSSG::PNode* parent, PSSG::PNode* node, unsigned int light_link_mask);
	//#rc3_legacy void DeferredAddToRoot( SIFGraphicsHandle node, unsigned int light_link_mask );

	void SleepInstance();
	void AwakenInstance();
	void DelayedAwakenInstance();
	FGameWorldMulticastDelegate	m_onWorldAwaken;
	FGameWorldMulticastDelegate	m_onWorldSleep;
	//#rc3_legacy void printGameState();

	void ResetHudIndicators();

	//#rc3_legacy PSSGMabAnimationController* CreateAnimations(PSSG::PNode* node, SIFPSSGUtil::SIFPSSGSceneCloner& cloner, PSSG::PTimeController* override_time_controller = NULL);

	// RussellD Okay, this is a bit weird, but will probably work for now...
	template<class T>//template<class T, class = typename std::enable_if<std::is_base_of<SIFGameObject, T>::value && std::is_base_of<AActor, T>::value>::type>
	T* BeginDeferredActorSpawnFromClass(UWorld* world);
	template<class T>//template<class T, class = typename std::enable_if<std::is_base_of<SIFGameObject, T>::value && std::is_base_of<AActor, T>::value>::type>
	T* BeginDeferredActorSpawnFromClass(UWorld* world, TSubclassOf<T> actorClass);

public://#rc3_legacy protected:
	bool Allocate( MABMEM_HEAP heap, bool asynchronous, FSimpleDelegate onAllocationComplete = FSimpleDelegate());
	void FreeAllocated();
	bool HasAllocated() { return world_is_allocated; }
	bool HasLoaded() { return world_is_allocated && !m_isLoading; }

	void GameWorldSetupUpdate();
	void EstimateLoadCompletion();
	void AbortWorldLoad();
	int GetLoadCompletionEstimate();

	// Functions to completely prevent players from updating when the world is not active
	void DisablePlayers();
	void EnablePlayers();

	// Functions to pause players updates when the pause menu is pushed on.
	void SetUnrealActorsPaused(const bool bEnabled);
	void PauseUnrealActors();
	void ResumeUnrealActors();

	//start of a match and during restart, we want to reset some things (like footsteps and rain)
	void ResetGraphicsObjects();
	void RequestUpdateSkyDomeLightingDelayed();
	//pauses time of day and rain effects on the field
	void PauseGraphicsObjects(bool IsPausing);
	//shows actors that were hidden during the loading
	void ShowMatchGraphics();

	void OnCutsceneStart();
	void OnCutsceneEnd();

	void SetFootprintsEnabled(bool bEnabled);

	void InitialisePauseHandler(class SIFAppTime* pAppTime, class SIFGamePauseState* pPauseState);

	void CancelSkyDomeUpdates(bool cancel);

private:

	FTimerHandle WorldReadyForPlayHandle;
	MabNetworkManager*				network_manager;
	RUNetworkState*					network_state;

	bool AsyncLoadStadiumMaps();
	bool AreStadiumMapsLoaded();

	bool InitialiseFootballerMaps();
	bool AsyncLoadFootballerMaps();
	bool AreFootballerMapsLoaded();

	bool InitialiseFootballerMap(SSTEAMSIDE side);
	bool AsyncLoadFootballerMap(SSTEAMSIDE side);

	TArray<FDelegateHandle>		m_characterMapInitialRevealCallback;
	TArray<URugbyLevelChunk*>	m_characterMapChunks;
	TArray<ARugbyCharacter*>	m_mapFootballers[SSTEAMSIDE::SIDE_NONE];

	void OnFootballerMapLoaded(URugbyLevelChunk* footballerLevelChunk, SSTEAMSIDE side);
	void OnFootballerMapShown(URugbyLevelChunk* footballerLevelChunk, SSTEAMSIDE side);

	//void OnLoadingFootballerMapComplete(URugbyLevelChunk* footballerLevelChunk, SSTEAMSIDE side);
	//void OnLoadingFootballerMapCancelled();

	void InitialiseFootballers();

	void OnLoadingComplete();
	void OnLoadingCancelled();

	void FinishWaking();

	//void SetLevelChunksVisible(bool visible); //#rc3_legacy void SetGraphicsNodesAttached(bool enabled);
	void HideWorld();
	void RevealWorld();
	void OnLevelChunkRevealed(URugbyLevelChunk* pLevelChunk);
	void OnWorldRevealed();
	
	//sets up the crowd and the time of day for this match
	void FindMatchGraphicsObjects();
	//spawns everything needed for the match's
	void GenerateMatchGraphics();
	//setups up match specific settings on the graphics
	void SetupMatchGraphics();
	

	MabMutex						isRendering;

	MabLockStepTimeSource*			simulation_time;
	//#rc3_legacy NMMabResource*					animation_resource;
	//#rc3_legacy SIFGameObjectDatabase*			object_database;
	std::unique_ptr<SIFGameContext>					game_context;
	//#rc3_legacy SIFAmbientPointCloud*			ambient_point_cloud;

	//#rc3_legacy SIFPSSGParticleSystemsManager*	particles_manager;
	//#rc3_legacy MabNetworkManager*				network_manager;
	//#rc3_legacy RUNetworkState*					network_state;
	//#rc3_legacy SIFEffectSystem*				effect_system;
	//#rc3_legacy NMMabAnimationWorld*			animation_manager;
	//#rc3_legacy MabVector<MabResourceBase*>		stadium_resources;

	/// Creates the SIFEffectSystem object and registers all its components
	//#rc3_legacy void CreateEffectSystem();

	/// Adds any nodes in the add_child queue to the scene. Should only be called during SyncUpdate.
	//#rc3_legacy void UpdateDeferredAttachQueue();

	/// Cleanup the Death Queue. Should only be called during SyncUpdate (althought is used in SIFGameWorld destructor as well)
	//void CleanupDeathQueue();

public:
	/// RU Methods
	void InitialiseActors();
	void InitialiseReplayManager();
	void CleanupActors();
	void CleanupMostActors();

	ARugbyCharacter* GetPlayerFromFootballerMap(int index, SSTEAMSIDE side) const;
	ARugbyCharacter* GetPlayer(int index) const;
	ARugbyCharacter* GetPlayerFromDB(int db_id) const;
	void RebuildPlayerList();

	//Returns the Simulation Time source (please be careful with non-const)
	const MabLockStepTimeSource* GetSimTime() const { return simulation_time; }
	//#rc3_legacy MabLockStepTimeSource* GetSimTimeNonConst() { return simulation_time; }
	//#rc3_legacy bool SetSimulationTimeLock(bool enabled) { return false; }

	RURandomNumberGenerator* GetRNG() const { return random_number_generator; }
	//#rc3_legacy int GetHeap() const { return heap; }
	int GetHeap() const { return 0; }

	ASSBall* GetBall() const { return ball; }
	SSRoleFactory* GetRoleFactory() const { return role_factory.get(); }

	int GetNumTeams() const;
	RUTeam*	GetTeam( int index ) const;
	RUTeam*	GetTeamFromDB( int db_id ) const;
	RUTeam*	GetTeam( SSTEAMSIDE ) const;

	const std::vector<std::unique_ptr<RUTeam>>& GetTeams() const { return teams; }

	RUGameLocomotion* GetLocomotion() const { return locomotion.get(); }
	RULineoutRater* GetLineoutRater() const { return lineout_rater.get(); }
	RUGameAnimation* GetAnimation() const { return animation.get(); }
	RUGameMovement* GetMovement() const { return movement.get(); }
	RUGameGetToBall* GetGameGTB() const { return game_gtb.get(); }
	RUGameEvents* GetEvents() const { return events.get(); }

	void SetCamera(ARugbyCameraActor* new_cam );
	ARugbyCameraActor*	GetCamera() const;

	RUGameSettings& GetGameSettings() const { return game_settings; }

	//Debug settings for time of day
	bool bLockTime = false;
	float timeOfDayOverride = 3.0f;

	///-----------------------------------------------------
	/// TEAM ASSIGNMENTS
	///-----------------------------------------------------

	/*
	void AssignPlayerControllerToTeam(ARugbyPlayerController* player, RUTeam* team);
	RUTeam* GetTeamAssignmentForPlayerController(const ARugbyPlayerController* player) const;

	TArray<ARugbyPlayerController*> GetAssignedPlayerControllers() const;
	ARugbyPlayerController* GetFirstAssignedPlayerController() const;
	ARugbyPlayerController* GetAssignedPlayerControllerFromControllerId(int controllerId) const;
	ARugbyPlayerController* GetAssignedPlayerControllerFromPlayerId(int playerId) const;
	*/

	const MabVector<SSHumanPlayer*>& GetHumanPlayers() const;
	SSHumanPlayer* GetHumanPlayer(EHumanPlayerSlot playerNumber = EHumanPlayerSlot::FIRST_HUMAN) const;
	SSHumanPlayer* GetHumanPlayerFromPlayerIndex(int playerIndex) const;
	SSHumanPlayer* GetFirstHumanPlayer() const;
	int GetNumActiveHumanPlayers() const;
	SSHumanPlayer* GetFirstActiveHumanPlayer( ) const;

	#ifdef ENABLE_OSD
	void SetChangePlayerSection( const char* section_name );
	#define SET_CHANGEPLAYER_SECTION( game, section_name ) game->SetChangePlayerSection( section_name )
	#else
	#define SET_CHANGEPLAYER_SECTION( game, section_name )
	#endif

	///-----------------------------------------------------

	void RestartGame();
	void FullGameRestart();

	void GetFilteredPlayerList( RLPResultList& result_list, RLP_FILTERPARAMETERS& filter_params, RLP_SORTER* sorter ) const;

	SIFViewManager* GetViewManager() const { return view_manager.get(); }
	SSCameraManager* GetCameraManager() const { return camera_manager; }
	SSInputManager* GetInputManager() const { return input_manager.get(); }
	RUStrategyHelper *GetStrategyHelper() const { return strategy_helper.get(); }
	RUTackleHelper *GetTackleHelper() const { return tackle_helper.get(); }
	RUGameState *GetGameState() const { return game_state.get(); }
	SSSpatialHelper *GetSpatialHelper() const { return spatial_helper.get(); }
	const SIFRugbyCharacterList& GetPlayers() const { return players; }
	const SIFRugbyCharacterList& GetAllPlayers() const { return all_players; }
	SSGameTimer* GetGameTimer(){ return game_timer.get(); }

	void AppendPlayer(ARugbyCharacter* player);

	RUHUDUpdater* GetHUDUpdater();
	RUHUDUpdaterTraining* GetHUDUpdaterTraining();
	RUHUDUpdaterUserStrategy* GetHUDUpdaterUserStrategy( SSTEAMSIDE side ) { return hud_updater_strategy[ side ]; }

	RUHUDUpdaterContextual* GetHUDUpdaterContextual() { return hud_updater_contextual; }
	RUHUDUpdaterReplay* GetReplayHUDUpdater(){ return replay_hud_updater; }
	RUStatsTracker* GetStatsTracker(){ return stats_tracker.get(); }
	RU3DHUDManager* Get3DHudManager(){ return hud3d; }
	RUTutorialManager* GetTutorialManager(){ return tutorial_manager.get(); }
	SSCutSceneManager* GetCutSceneManager(){ return cutscene_manager.get(); }
	RURules* GetRules() const { return rules.get(); }
	RUTeam* GetOfficialsTeam() { return officials.get(); }
	SSPostEffectsManager *GetPostEffectsManager() const { return /*post_effects_manager*/nullptr; }
	SSReplayManager *GetReplayManager(){ return replay_manager.get(); }
	RUStadiumManager *GetStadiumManager(){ return stadium_manager; }
	RUPSSGCameraEffects* GetCameraEffects() const { return /*camera_effects*/nullptr; }
	RUWeatherManager* GetWeatherManager() const { return weather_manager; }
	RUSubstitutionManager *GetSubstitutionManager() const { return substitution_manager.get(); }
	SSScreenWipeManager *GetScreenWipeManager(){ return screen_wipe_manager.get(); }
	RUGameRumbleManager *GetRumbleManager(){ return rumble_manager; }
	RUGameWorldAudio* GetGameWorldAudio() { return world_audio.get(); }
	RUPlayerFilters* GetPlayerFilters(){ return player_filters.get(); }
	//#rc3_legacy SIFPSSGParticleSystemsManager* GetParticlesManager(){ return	particles_manager; };

	WORLD_ID GetWorldId() const { return world_id; }
	WORLD_STATE GetWorldState() const { return world_state; }
	FString GetWorldIdAsString();

	bool IsAwake() const { return world_state == WORLD_STATE::AWAKE; }
	bool IsVisible() const { return world_is_revealed; }

	void SetAsyncLoadingStarted(bool value) { async_loading_started = value; }
	bool HasAsyncLoadingStarted() const { return async_loading_started || async_load_start_requested; }// Used by the tutorials to determine if the aysnc loading is happening behind the scenes
	void ApplyCustomisationAsync(ARugbyCharacter* player, const PlayerCustomisationInfo& csinfo, bool do_customisation);

	void SetPlayersVisible(bool visible, RUTeam* team = NULL);  // If team is NULL then it is for all players (including officials)

	void SetAllPlayerFacials( bool enabled );

	/// All texture swaps have to be recorded, due to pssg and mab keeping separate reference counts.
	//#rc3_legacy void RegisterTextureSwap(PSSG::PNode *node, const char *node_name, const char* param_name);


	ACrowdbase* GetCrowdManager() { return crowd_manager.Get(); }
	ASkyDomeBase* GetSkyManager() { return sky_manager.Get(); }
	AFieldRugby* GetFieldManager() { return field_manager.Get(); }
	RUEmotionEngineManager*	GetEmotionEngineManager() { return emotion_engine_manager; }

	//#rc3_legacy PSSG::PNode* AddToScene(const char *resource_name, unsigned int light_link_mask, SIFPSSGUtil::SIFPSSGSceneCloner* cloner = NULL);
	//#rc3_legacy PSSG::PNode* AddToScene(PSSG::PNode* asset, unsigned int light_link_mask, SIFPSSGUtil::SIFPSSGSceneCloner* cloner = NULL);
	//#rc3_legacy PSSG::PNode* AddToScene(const char *resource_name, PSSG::PNode* parent, unsigned int light_link_mask, SIFPSSGUtil::SIFPSSGSceneCloner* cloner = NULL );
	//#rc3_legacy PSSG::PNode* AddToScene(PSSG::PNode* asset, PSSG::PNode* parent, unsigned int light_link_mask, SIFPSSGUtil::SIFPSSGSceneCloner* cloner = NULL );

	//#rc3_legacy void RemoveFromScene(PSSG::PNode* node);
	PSSG::PRootNode* GetSceneRootNode() const { return nullptr; } //#rc3_legacy : actually defined in cpp

	void AddStadiumChunk(URugbyLevelChunk* subLevel);
	void SetCharacterMapChunk(SSTEAMSIDE side, URugbyLevelChunk* chunk);
	void DestroyCharacterMapChunk(SSTEAMSIDE side);

	//#rc3_legacy void AddPssgNodeForDeletion(PSSG::PNode* node);

	AActor* AddProp( const FVector& _position, const FVector& _rotation, const char *resource_name, unsigned int light_link_mask );
	void RemoveProp(AActor* prop );
	//#rc3_legacy void AddToResourceList( MabResourceBase* resource ) { stadium_resources.push_back( resource ); }
	//#rc3_legacy void RemoveFromResourceList( MabResourceBase* resource );

	//#rc3_legacy PSSG::PLightNode *CreateLight();
	//#rc3_legacy void AddLight(PSSG::PLightNode *light, unsigned int light_link_mask);
	//#rc3_legacy void RemoveLight(PSSG::PLightNode *light);
	//#rc3_legacy void SetLightsModified();

	// Attempt to ReSync net game via data received
	void NetLoadReSync(SSStreamPacker& data);
	void NetGenerateReSync(SSStreamPacker& data);

	void ResetActors(bool is_restart);
	void TeamLineupComplete();
	void RestartKick();

	//#rc3_legacy void NotifyToUpdateController(bool is_suspended);

	void InitialisePostLoad();

	//#rc3_legacy void PrintSceneGraph();

	// Safe player delete, gets deferred to sync-update (DoPlayerDelete is then called)
	//#rc3_legacy void DeletePlayer(ARugbyCharacter* player);

	// Non-safe player delete
	//void DeletePlayerNow(ARugbyCharacter* player, bool destroy_player=true);
	//void SwapPlayerInLists(ARugbyCharacter *player, ARugbyCharacter *replacement);
	void SwapPlayerInActiveList(ARugbyCharacter* existing, ARugbyCharacter* replacement);

	//#rc3_legacy void UndoTextureSwaps(PSSG::PNode *node);

	/// Request a sandbox environment setting (deferred t SyncUpdate)
	void RequestSandboxEnvironment(SANDBOX_ENVIRONMENT setting);
	SANDBOX_ENVIRONMENT GetSandboxEnvironment() const { return sandbox_environment_setting; }
	
	// Gets the closest axis north/south/east/west vector
	FVector GetClosestBasisVector(const FVector& test_vector) const;
	FVector2D GetClosestBasisVector(const FVector2D& test_vector) const;

	/// Quick load and UI-team swapping.
	inline bool GetQuickLoadActive() { return m_quickLoadActive; }

	void SetSandboxTeamsDirty() { m_bForceReloadTeam = true; }
	inline bool IsSandboxTeamSwapActive() { return m_teamSwapActive; }
	bool SwapToPreferredSandboxTeams();
	bool ********************************();
	bool SwapSandboxTeams(int homeTeam, int awayTeam);
	
private:
	bool GetPreferredSandboxTeams(int& homeTeam, int& awayTeam);
	void DoSwapSandboxTeams();
	void DoFullSwapSandboxTeams();

public:
	struct PlayerOverride
	{
		int LineupIndex;
		int OverrideShirtNumber;
		int OverrideUIId;
		unsigned short OverrideDbId;
	};

	bool TryGetMenuWomanOverrideDbIdByShirtNumber(SSTEAMSIDE side, int shirtNumber, unsigned short& outDbId) const;
	bool TryGetMenuWomanOverrideByLineupIndex(SSTEAMSIDE side, int lineupIndex, PlayerOverride& outPlayerOverride) const;

	static int GetUIOppositionTeam(int team_id);

	// Starts any loading of assets if required by the window change.
	// Returns true if loading is required, a callback will be triggered once load is complete.
	//#rc3_legacy bool StartUILoadPendingIfRequired( const MabString& from_window, const MabString& to_window );

	void OnScreenEnter(FString screenEntered);
	void TriggerDelayedTransition();
	void ClearDelayedTransition();

	// Enable all players who are going to be playing to control a player whilst loading.
	void	SetupHumanPlayersForLoading( bool is_tutorial = false );
	/// Reset the human players for the sandbox game. (AwakenInstance)
	void	ResetSandboxHumanPlayers();

#if 0 //#rc3_legacy 
	/// Request loading of non-permanent players.
	void	LoadNonPermanentPlayers();
	/// Delete non-permanent players
	void	DeleteNonPermanentPlayers();
#endif

	/// Tell gameworld who the customisation player is.
	void	SetCustomisationPlayer(ARugbyCharacter* player){ customisation_player = player; }
	/// Get the customisation player.
	ARugbyCharacter* GetCustomisationPlayer();

	/// Set the light nodes for the sandbox.
	void SetMainMenuLightNode(PSSG::PNode *node){ /*main_menu_lights = node;*/ }
	void SetTrainingLightNode(PSSG::PNode *node){ /*training_lights = node;*/ }

	/// Set up the wind
	void SetupWind( FVector direction, float wind_strength );

	unsigned short GetPseudoCompetitionId() const { return pseudo_competition_id; }
	URugbyGameInstance& GetGameInstance() const { return mGameInstance; }

	/// Get the file name for the ball - based on teams playing/competition etc...
	FString GetBallFileName(int TeamId = -1);

	void UpdateCamera(const MabTimeStep & time_step, bool updateIfPaused = false);	//Public update camera function for debugging purposes

	bool GetIsPlayGo() { return m_isPlayGo; }

	/// Load the ball material override
	void LoadBallMaterial(int TeamId = -1);
	void InitialiseNetworking();
	void DestroyNetworking();

	FSimpleMulticastDelegate& GetOnGameWorldReadyDelegate() { return m_onGameWorldReady; }
	FSimpleMulticastDelegate& GetOnLoadingCompleteDelegate() { return m_onLoadingComplete; }

private:	
	void InitialiseAnimation(void);
	void InitialiseBall();
	void InitialiseTeams();

	bool disable_simulation = false;
	void PreSyncUpdateSimulation( const MabTimeStep& time_step );
	void PostSyncUpdateSimulation( const MabTimeStep& time_step );


	void AssignPlayerNumbers();
	void CreateHumanPlayers();
	void SetOfficialsKickOffPositions();
	void InitialiseCommon();
	//#rc3_legacy 
	void Initialise3DHUD( /*MabControlActionManager *control_action_manager*/ );
	void InitialiseHUD(/* MabControlActionManager *control_action_manager */);

	void ResetContextBucket();

	void InitialiseSandbox( MabControlActionManager *control_action_manager );
	void InitialiseMenu();
	void InitialiseMain();	
	void InitialiseWeather();
	//#rc3_legacy void RelightScene();

	//#rc3_legacy void DoAnimationUpdates(float delta_time);
	/// TEMP function to update ui load pending
	//#rc3_legacy void UpdateUILoadPending();

	//#rc3_legacy void UndoAllTextureSwaps();

	double m_loadStartTime;

	void OnAsyncLoadingStart();

	/// Set the roles for the officials.
	void SetOfficialsRoles();

	/// Set the sandbox environment (weather/post-post effects etc..)
	void SetSandboxEnvironment(SANDBOX_ENVIRONMENT setting, bool force=false);

	/// Load/restore post effect settings.
	//#rc3_legacy void LoadPostEffectSettings(const MabString &file_name);
	//#rc3_legacy void RestorePostEffectSettings();

	/// Load/restore shadow settings.
	//#rc3_legacy void LoadShadowSettings(const MabString &file_name);
	//#rc3_legacy void RestoreShadowSettings();

	/// Switch the UI light source. (MUST BE CALLED FROM SYNCUPDATE!)
	//#rc3_legacy void SetLightSource(bool training);

	/// Handles if the user has received an invite while they were launching into the match.
	void ProcessInitialState();


	/// Load the replay screen wipe texture.
	void LoadScreenWipe();

	/// Set up psuedo_competition_id (used by GetBallFilename,LoadScreenWipe and stadium manager)
	void CalculatePseudoCompetitionId();


	void ResetPlayerTickPaused();
	void SetupPlayerTickPaused();

	TMap<FGraphEventRef, ARugbyCharacter*> TaskGraphCompletionEvents;
	TMap<TSharedRef<struct FJobEvent, ESPMode::ThreadSafe>, ARugbyCharacter*> JobCompletionEvents;

	void PreSyncUpdateCharacterAnimation(float delta_time);
	void PostSyncUpdateCharacterAnimation(float delta_time);

	void UpdateCharacterExternalRateOptimization(float delta_time, uint64 step_count);

	void LogAndCheckNetworkState(float delta_time, uint32 step_count);

	void ShowDebugNetworkInfo();

private:
	URugbyGameInstance & mGameInstance;
	RUGameSettings& game_settings;
	
	bool						m_isLoading;
	FSimpleMulticastDelegate	m_onLoadingComplete;
	FSimpleMulticastDelegate	m_onLoadingCancelled;
	FSimpleMulticastDelegate	m_onGameWorldReady;
	int							m_loadCompletionEstimate;

	//TMap<int, RUTeam*> m_PlayerTeamAssignments;

	// moved from SIFApplicationBase
	std::unique_ptr<SIFViewManager> view_manager;

	std::unique_ptr<RUGameEvents>				events;							///< The events that can be fired from this RL3Game.
	ASSBall*		ball;							///< The ball.
	SIFRugbyCharacterList		players;						///< The list of players.
	SIFRugbyCharacterList		all_players;					///< The list of all players (benched and on-field)
	SIFRugbyPropList			props;							///< The list of props.
	MabVector<URugbyLevelChunk*>	stadium_chunks;
	std::vector<std::unique_ptr<RUTeam>>		teams;							///< The teams.
	std::unique_ptr<RUTeam>						officials;						///< The officials.
	std::unique_ptr<RUGameLocomotion>			locomotion;						///< The locomotion data shared by all players.
	std::unique_ptr<RULineoutRater>				lineout_rater;					///< The jump-catch data for lineout shared by all players.
	std::unique_ptr<RUGameAnimation>			animation;						///< The animation data shared by all players.
	std::unique_ptr<RUGameMovement>				movement;						///< The movement data shared by all players.
	std::unique_ptr<RUGameGetToBall>			game_gtb;						///< The class which resolves who get the ball in freeball
	std::unique_ptr<SSRoleFactory>				role_factory;					///< The factory used for creating player roles
	 
	SSCameraManager*							camera_manager;					///< The camera manager.
    //#rc3_legacy_camera
	ARugbyCameraActor*							camera;							///< The world camera.
	MabVector<SSHumanPlayer*>					human_players;					///< The the human players playing this game.
	//#rc3_legacy 
	std::unique_ptr<SSInputManager>				input_manager;					///< The input manager, it needs to be updated in our simulation update
	std::unique_ptr<RUStrategyHelper>			strategy_helper;				///< The RUStrategyHelper for this game.
	std::unique_ptr<RUTackleHelper>				tackle_helper;
	std::unique_ptr<RUGameState>				game_state;						///< Current game state information (Was part of RL3Game)
	std::unique_ptr<SSSpatialHelper>			spatial_helper;					///< Tracks information about where players are on the field
	std::unique_ptr<SSGameTimer>				game_timer;						///< Tracks how long a game has been running, fires halftime/fulltime events
	RURandomNumberGenerator*					random_number_generator;		///< Handles the generation & tracking of random numbers	
	RU3DHUDManager*				hud3d;
	float						unused_time;					///< Left over 30hz lockstep time
	
	ARugbyCameraActor*			active_camera;					///< The main game camera.
	RUHUDUpdaterBase*			hud_updater;					///< The HUD manager/updater
	RUHUDUpdaterContextual*		hud_updater_contextual;			///< The HUD manager/updater for contextual help
	RUHUDUpdaterUserStrategy*	hud_updater_strategy[2];		///< The HUD manager/updater for user startegies for each team respectively
	RUHUDUpdaterReplay*			replay_hud_updater;				///< The HUD manager/updater for replay
	std::unique_ptr<RUStatsTracker>				stats_tracker;
	std::unique_ptr<RURules>					rules;							///< The rules currently active on this game
	std::unique_ptr<RUTutorialManager>			tutorial_manager;				///< Tracks tutorials
	std::unique_ptr<SSCutSceneManager>			cutscene_manager;
	
	//#rc3_legacy SSPostEffectsManager*		post_effects_manager;
	std::unique_ptr<SSReplayManager>			replay_manager;
	RUStadiumManager*			stadium_manager;				/// Local copy (owned by SIFApplication).
	RUWeatherManager*			weather_manager;
	std::unique_ptr<RUTeamConfidenceHandler>	team_confidence_handler;		///< Monitors events and changes team confidence
	std::unique_ptr<RUSubstitutionManager>		substitution_manager;
	std::unique_ptr <SSScreenWipeManager>		screen_wipe_manager;
	RUGameRumbleManager*		rumble_manager;
	std::unique_ptr <RUGameWorldAudio>			world_audio;

	/*
	SSContextBucket*			context_bucket;
	RUShameAndGloryManager*		shame_and_glory_manager;
	RUMatchIntensityManager*	match_intensity_manager;
	RUContextBucketBasicEvents*	basic_events_handler;
	SSCBEventSequenceDefinition* event_sequences_def;
	RUCBEventAnalyser*			event_analyser;
	RUCrowdReactionManager*		crowd_reaction_manager;
	*/

	RUCBGameStats2Bucket*		game_stats2bucket;
	RUEmotionEngineManager*		emotion_engine_manager;

	//#rc3_legacy RUCrowdManager*				crowd_manager;
	TWeakObjectPtr<ACrowdbase>		crowd_manager;
	TWeakObjectPtr<ASkyDomeBase>	sky_manager;
	TWeakObjectPtr<AStadiumBase>	stadium_overloading_manager;
	TWeakObjectPtr<AFieldRugby>		field_manager;
	//ACrowdbase*					crowd_manager;
	//ASkyDomeBase*				sky_manager;
	//AStadiumBase*				stadium_overloading_manager;
	//AFieldRugby*				field_manager;

	TSharedPtr<FStreamableHandle> m_signage_actor_async_spawn_handle;

	//#rc3_legacy SIFPSSGShaderParameters*	shader_parameters;
	//#rc3_legacy RUPSSGJumboTrons*			jumbotrons;
	//#rc3_legacy RUPSSGCameraEffects*		camera_effects;

	bool						allow_team_reload = true;

	bool						relight_scene;
	bool						do_mem_log_print;
	bool						reenabled_rendering;

	WORLD_ID					world_id;
	WORLD_STATE					world_state;

	bool						world_is_revealing;
	bool						world_is_revealed;

	bool						world_is_allocated;

	bool						async_loading_started;

	//#rc3_legacy_pssg MabVector<PSSG::PNode*>		node_clean_up;

	MabString					settings_path;
	MabString					shadow_settings_path;
	int							delayed_awaken;

	// Online automatic quit if opponent (or you have) has disconnected.
	int							delayed_initial_state_check;
	bool						initial_state_checked;

	bool						m_isPlayGo;

	ARugbyPauseHandler*			m_pPauseHandler = nullptr;

	class TextureSwapRecord
	{
	public:
		TextureSwapRecord(PSSG::PNode *node, const char *node_name,const char *param_name)
			: node(node)
			, node_name(node_name)
			, param_name(param_name)
		{
		}

		PSSG::PNode *node;
		const char *node_name;
		const char *param_name;
	};
	MabVector<TextureSwapRecord> texture_swaps;

	enum GameWorldSetup
	{
		SETUP_IDLE = 0,

		STADIUM_LOAD,
		STADIUM_LOAD_WAIT,

		TEAMS_LOAD,
		TEAMS_LOAD_WAIT,
		TEAMS_INITIALISE,
		TEAMS_SWAP, // Entry point for swapping teams and recustomising

		CHARACTER_CUSTOMISATIONS_LOAD,
		CHARACTER_CUSTOMISATIONS_LOAD_WAIT,
		CHARACTER_CUSTOMISATIONS_INITIALISE,
		CHARACTER_FACE_RENDER_WAIT,
		CHARACTER_MESH_MERGE_START,
		CHARACTER_MESH_MERGE_WAIT,
		CHARACTER_INITIALISE_REPLAY,

		SETUP_COMPLETED,
		SETUP_CANCELLED,
		SETUP_ERROR
	};
	GameWorldSetup m_gameWorldSetupState;

	std::unique_ptr<RUPlayerFilters> player_filters;

	// test counter for ui load pending callbacks
	int load_pending_test_counter;

	int							async_load_start_requested;			// Set !ASYNC_LOAD_NONE (0) when async-load has been requested (processed in syncupdate)
	bool						non_permanent_players_loaded;		// Set true when non-permanent players have been loaded/requested.

	// current environment setting of sandbox.
	SANDBOX_ENVIRONMENT			sandbox_environment_setting;
	SANDBOX_ENVIRONMENT			requested_sandbox_environment_setting;

	// The current customisation player.
	ARugbyCharacter				*customisation_player;

	//#rc3_legacy PSSG::PNode					*main_menu_lights;
	//#rc3_legacy PSSG::PNode					*training_lights;
	bool						using_training_lights;

	// Keep tab of the ball resource as it can now be swapped out.
	//#rc3_legacy PSSGMabDatabaseResourceFile	*ball_resource;

	// Current competition_id OR if playing quick-match and both are members of the same competition.
	unsigned short				pseudo_competition_id;

	// Set when players have to be reloaded after restart.
	int							restart_reload_players;

	TArray<FString>				m_delayedTransitionScreen;

	bool						m_quickLoadActive;
	TEAM_SWAP_REQUEST			m_quickLoadDelayedTeamSwapRequest;

	bool						m_teamSwapActive;
	bool						m_bForceReloadTeam;

	bool						m_allowVoipDelayed = false;
	float						m_allowVoipTimer = 0.0f;

	RugbyAsyncTask::AsyncTaskQueue		m_asyncTaskQueue;

	struct CharacterMergingManager
	{
	public:
		int m_currentCharacter = 0;
		int m_currentTimer = 0;
		const int m_TimerCounter = 10;
	};
	CharacterMergingManager m_mergingManager;
};

///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------
template<class T>//template<class T, class>
T* SIFGameWorld::BeginDeferredActorSpawnFromClass(UWorld* world)
{
	static_assert(std::is_base_of<SIFGameObject, T>::value && std::is_base_of<AActor, T>::value, "");

	T* spawnedObject = nullptr;

	if (world != nullptr && world->IsValidLowLevel())
	{
		spawnedObject = Cast<T>(UGameplayStatics::BeginDeferredActorSpawnFromClass(world, T::StaticClass(), FTransform(), ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn));
	}

	if (UOBJ_IS_VALID(spawnedObject))
	{
		spawnedObject->SetGameWorld(this);

#if WITH_EDITOR
		FName folderPath(*FString::Printf(TEXT("%s/Actors"), *GetWorldIdAsString()));
		spawnedObject->SetFolderPath(folderPath);
#endif
	}

	return spawnedObject;
}

///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------
template<class T>//template<class T, class>
T* SIFGameWorld::BeginDeferredActorSpawnFromClass(UWorld* world, TSubclassOf<T> actorClass)
{
	static_assert(std::is_base_of<SIFGameObject, T>::value && std::is_base_of<AActor, T>::value, "");

	T* spawnedObject = nullptr;

	if (world != nullptr && world->IsValidLowLevel())
	{
		spawnedObject = Cast<T>(UGameplayStatics::BeginDeferredActorSpawnFromClass(world, actorClass, FTransform(), ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn));
	}

	if (UOBJ_IS_VALID(spawnedObject))
	{
		spawnedObject->SetGameWorld(this);

#if WITH_EDITOR
		FName folderPath(*FString::Printf(TEXT("%s/%s"), *GetWorldIdAsString(), ANSI_TO_TCHAR("Actors")));
		spawnedObject->SetFolderPath(folderPath);
#endif
	}

	return spawnedObject;
}