// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIUserWidgetPlayerProfile.h"

#include "Rugby/RugbyGameInstance.h"

#include "UI/WWUICareerGlobal.h"
#include "Rugby/Utility/Helpers/SIFGameHelpers.h"

#include "UI/GeneratedHeaders/WWUIScreenCareerTeamSquad_UI_Namespace.h"

#include "Rugby/Match/RugbyUnion/RUSettingsEnums.h"
#include "Rugby/Match/RugbyUnion/RUPlayerAttributesHelper.h"
#include "Rugby/Match/RugbyUnion/RUDBPlayer.h"

#include "UI/Components/WWUIUserWidgetPlayerFaceImage.h"

#include "WWUITranslationManager.h"

#include "Rugby/Utility/Helpers/SIFGameHelpers.h"

#include "Rugby/Match/RugbyUnion/CompetitionMode/RUDBHelperInterface.h"

#include "UI/Components/WWUIUserWidgetPlayerAttributes.h"

#include "Image.h"
#include "ProgressBar.h"
#include "ScrollBox.h"
#include "HorizontalBox.h"
#include "Widget.h"
#include "TextBlock.h"
#include "WWUIFunctionLibrary.h"
#include "UnrealString.h"
#include "Engine/World.h"
#include "Utility/Helpers/SIFUIHelpers.h"
#include "RUUIDatabaseQueryManager.h"

void UWWUIUserWidgetPlayerProfile::PopulateShortProfile(uint32 TeamDatabaseID, uint32 PlayerDatabaseID, int32 ComparedPlayerdatabaseID, int32 SelectedIndex, bool ProPostMatch /*= false*/, int32 AtlasIndex /*= 0*/, bool showStatChanged /*= false*/)
{
	URugbyGameInstance* pRugbyGameInstance = nullptr;
	UWorld* pWorld = GetWorld();
	if (pWorld)
	{
		pRugbyGameInstance = Cast<URugbyGameInstance>(pWorld->GetGameInstance());
	}

	RU_PlayerDB_Data* player_data = nullptr;;

	if (pRugbyGameInstance)
	{
		RUDBHelperInterface* db_helper = pRugbyGameInstance->GetGameDBHelper();

		if (db_helper)
		{
			player_data = db_helper->LoadPlayerDBData(PlayerDatabaseID);
		}
	}

	if (PlayerDatabaseID == DB_SERVER_PLAYER_ID)
	{
#if defined (FANHUB_ENABLED)
		RUDB_PLAYER* pPlayerDetails = SIFGameHelpers::GAGetRUDBServerPlayer(SelectedIndex);

		// --Populate player info
				// Populate player info
		FString FirstName = SIFGameHelpers::GAConvertMabStringToFString(pPlayerDetails->GetFirstName());
		FString LastName = SIFGameHelpers::GAConvertMabStringToFString(pPlayerDetails->GetLastName());

		FString PlayerNameString = (FirstName + " " + LastName);

		REMOVE_UNUSED_CHARACTER(PlayerNameString);

		if (pPlayerDetails->IsCustom() && ((SIFApplication::GetApplication()->IsAnyUserRestricted() && SIFGameHelpers::GAGetPlayerDownloadUser(pPlayerDetails->GetDbId()) != "") || SIFApplication::GetApplication()->IsNonPrimaryUserRestricted()))
		{
			PlayerNameString = UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper();
		}

		// --if IsPS4() and GPS4IsUGCRestricted(-1) and player_details:IsCustom() then
		//--	player_name = "[ID_XBOX_CENSORED_NAME]";
		// --end;

		FString PlayerRatingString = FString::FromInt(pPlayerDetails->GetOverallRating());

		FString PlayerPositionString = SIFGameHelpers::GAConvertMabStringToFString(pPlayerDetails->GetPositionR15Text(0));

		if (pPlayerDetails->gender == PLAYER_GENDER_FEMALE)
		{
			PlayerPositionString = SIFGameHelpers::GAConvertMabStringToFString(pPlayerDetails->GetPositionR7Text(0));
		}

		// --If we have a valid team ID, check if it's a Sevens team
		UE_LOG(LogTemp, Display, TEXT("Check if team %d is a sevens team"), TeamDatabaseID);

		if (TeamDatabaseID > 0)
		{
			if (SIFGameHelpers::GAIsTeamR7(TeamDatabaseID))
			{
				UE_LOG(LogTemp, Display, TEXT("Yes"));
				PlayerPositionString = SIFGameHelpers::GAConvertMabStringToFString(pPlayerDetails->GetPositionR7Text(0));
			}
			else
			{
				UE_LOG(LogTemp, Display, TEXT("No"));
			}
		}
		// since this is a string table entry, we know we can remove the last character and add '_UPPER]' on the end.
		PlayerPositionString = PlayerPositionString.LeftChop(1) + "_UPPER]";

		// Position
		PlayerPositionString = UWWUITranslationManager::Translate(PlayerPositionString).ToUpper();

		// Flag
		MabString FlagPath = SIFGameHelpers::GAGetCountryFlag(pPlayerDetails->nation);
		UTexture2D* pFlagTexture = nullptr;
		FString name = FString("/Game/Rugby/cmn_con/ui/flags/") + FlagPath.c_str() + FString(".") + FlagPath.c_str();
		pFlagTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name));

		PopulateShortProfileData(PlayerNameString, PlayerRatingString, PlayerPositionString, pFlagTexture, PlayerDatabaseID, ComparedPlayerdatabaseID, TeamDatabaseID, SelectedIndex, ProPostMatch, AtlasIndex, showStatChanged);
#endif
	}
	else if (player_data != nullptr)
	{
		// Populate player info
		FString FirstName = SIFGameHelpers::GAConvertMabStringToFString(player_data->GetFirstName());
		FString LastName = SIFGameHelpers::GAConvertMabStringToFString(player_data->GetLastName());

		FString PlayerNameString = (FirstName + " " + LastName);

		REMOVE_UNUSED_CHARACTER(PlayerNameString);

		if (player_data->GetIsCustom() && ((SIFApplication::GetApplication()->IsAnyUserRestricted() && SIFGameHelpers::GAGetPlayerDownloadUser(player_data->GetDbId()) != "") || SIFApplication::GetApplication()->IsNonPrimaryUserRestricted()))
		{
			PlayerNameString = UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper();
		}
		// #rc3_legacy_censor
		/*if IsPS4() and GPS4IsUGCRestricted(-1) and GAIsPlayerCustom(player_db_id) then
				player_name = "[ID_XBOX_CENSORED_NAME]";
		end;*/

		FString PlayerRatingString = FString::FromInt(player_data->GetOverallRating());

		FString PlayerPositionString = "[ID_INVALID_POSITION]";

		if (player_data->GetGender() == PLAYER_GENDER_FEMALE)
		{
			PlayerPositionString = SIFGameHelpers::GAGetPositionText(player_data->GetPrimaryPositionR7());
		}

		// If we have a valid team ID, check if it's a Sevens team
		// Message("Check if team " ..team_db_id .. " is a sevens team");
		if (PlayerPositionString == "[ID_INVALID_POSITION]")
		{
			if (TeamDatabaseID > 0)
			{
				if (SIFGameHelpers::GAIsTeamR7(TeamDatabaseID))
				{
					// Message("	Yes");
					PlayerPositionString = SIFGameHelpers::GAGetPositionText(player_data->GetPrimaryPositionR7());
				}
				else
				{
					//Message("	No");
					PlayerPositionString = SIFGameHelpers::GAGetPositionText(player_data->GetPrimaryPositionR13());
				}
			}
			else
			{
				// Nick WWS 7s to Womens 13s //
				/*
				if (SIFGameHelpers::GAGetGameModeIsR7())
				{
					PlayerPositionString = SIFGameHelpers::GAGetPositionText(player_data->GetPrimaryPositionR7());


					if (PlayerPositionString == "[ID_INVALID_POSITION]")
					{
						PlayerPositionString = SIFGameHelpers::GAGetPositionText(player_data->GetPrimaryPositionR13());
					}
				}
				else
				{ */
					PlayerPositionString = SIFGameHelpers::GAGetPositionText(player_data->GetPrimaryPositionR13());

					if (PlayerPositionString == "[ID_INVALID_POSITION]")
					{
						PlayerPositionString = SIFGameHelpers::GAGetPositionText(player_data->GetPrimaryPositionR7());
					}
				//}
			}
		}

		ensure(PlayerPositionString != "[ID_INVALID_POSITION]");

		// since this is a string table entry, we know we can remove the last character and add '_UPPER]' on the end.
		PlayerPositionString = PlayerPositionString.LeftChop(1) + "_UPPER]";

		// Flag
		MabString FlagPath = SIFGameHelpers::GAGetPlayersTeamFlag(PlayerDatabaseID);
		UTexture2D* pFlagTexture = nullptr;
		FString name = FString("/Game/Rugby/cmn_con/ui/flags/") + FlagPath.c_str() + FString(".") + FlagPath.c_str();
		pFlagTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name));

		// Position
		PlayerPositionString = UWWUITranslationManager::Translate(PlayerPositionString).ToUpper();

		PopulateShortProfileData(PlayerNameString, PlayerRatingString, PlayerPositionString, pFlagTexture, PlayerDatabaseID, ComparedPlayerdatabaseID, TeamDatabaseID, 0, ProPostMatch, AtlasIndex, showStatChanged);

		// Setting visibility. SetShortProfileToRecruit() is hiding these so sometimes we need to show them again. -CT
		UTextBlock* position_node = Cast<UTextBlock>(UWWUIFunctionLibrary::FindWidget(ScreenRef, WWUIScreenCareerTeamSquad_UI::TextPositionLarge));
		UWidget* attributes_list = UWWUIFunctionLibrary::FindWidget(ScreenRef, WWUIScreenCareerTeamSquad_UI::CareerHorizontalPlayerStats);
		UWidget* rating_node = UWWUIFunctionLibrary::FindWidget(ScreenRef, WWUIScreenCareerTeamSquad_UI::OverlayRating);
		UWidget* flag_node = UWWUIFunctionLibrary::FindWidget(ScreenRef, WWUIScreenCareerTeamSquad_UI::ImageNationality);

		if (position_node && attributes_list && rating_node && flag_node)
		{
			UWWUIFunctionLibrary::SetVisibility(rating_node, true);
			UWWUIFunctionLibrary::SetVisibility(position_node, true);
			UWWUIFunctionLibrary::SetVisibility(flag_node, true);
			UWWUIFunctionLibrary::SetVisibility(attributes_list, true);

			UpdateSmallProfile();
		}
		else
		{
			ensure(position_node && attributes_list && rating_node && flag_node);
		}

	}
	else
	{
		UTextBlock* pPlayerName = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(this, WWUIScreenCareerTeamSquad_UI::TextPlayerName));
		if (pPlayerName)
		{
			pPlayerName->SetText(FText::FromString("").ToUpper());
			pPlayerName->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		}

		UWWUIUserWidgetPlayerFaceImage* pPlayerImage = Cast<UWWUIUserWidgetPlayerFaceImage>(UWWUIFunctionLibrary::FindWidget(ScreenRef, WWUIScreenCareerTeamSquad_UI::PlayerFaceImage));

		if (pPlayerImage)
		{
			pPlayerImage->RefreshPlayerFaceImage(nullptr, true);
		}

		UTextBlock* position_node = Cast<UTextBlock>(UWWUIFunctionLibrary::FindWidget(ScreenRef, WWUIScreenCareerTeamSquad_UI::TextPositionLarge));
		UWidget* attributes_list = UWWUIFunctionLibrary::FindWidget(ScreenRef, WWUIScreenCareerTeamSquad_UI::CareerHorizontalPlayerStats);
		UWidget* rating_node = UWWUIFunctionLibrary::FindWidget(ScreenRef, WWUIScreenCareerTeamSquad_UI::OverlayRating);
		UWidget* flag_node = UWWUIFunctionLibrary::FindWidget(ScreenRef, WWUIScreenCareerTeamSquad_UI::ImageNationality);

		if (position_node && attributes_list && rating_node && flag_node)
		{
			UWWUIFunctionLibrary::SetVisibility(rating_node, false);
			UWWUIFunctionLibrary::SetVisibility(rating_node, false);
			UWWUIFunctionLibrary::SetVisibility(position_node, false);
			UWWUIFunctionLibrary::SetVisibility(flag_node, false);
			UWWUIFunctionLibrary::SetVisibility(attributes_list, false);
		}
		// Hide all the nodes

		UWidget* pDownloadedPlayerInfoWidget = ScreenRef->FindChildWidget(WWUIScreenCareerTeamSquad_UI::DownloadedPlayerInfo);
		if (pDownloadedPlayerInfoWidget)
		{
			pDownloadedPlayerInfoWidget->SetVisibility(ESlateVisibility::Collapsed);
		}
	}
}

void UWWUIUserWidgetPlayerProfile::InGamePopulateShortProfile(int32 TeamIndex, uint32 PlayerDatabaseID, int32 ComparedPlayerDatabaseID, int32 AtlasIndex)
{
	RUDB_PLAYER* player_data = (RUDB_PLAYER*)(SIFGameHelpers::GAGetInGameDBPlayer(TeamIndex, PlayerDatabaseID));  //returns a RUDB_PLAYER MabObject
	
	if (player_data != nullptr)
	{
		// Populate player info
		FString FirstName = UTF8_TO_TCHAR(player_data->GetFirstName());
		FString LastName = UTF8_TO_TCHAR(player_data->GetLastName());

		FString PlayerNameString = (FirstName + " " + LastName);

		if (player_data->IsCustom() && ((SIFApplication::GetApplication()->IsAnyUserRestricted() && SIFGameHelpers::GAGetPlayerDownloadUser(player_data->GetDbId()) != "") || SIFApplication::GetApplication()->IsNonPrimaryUserRestricted()))
		{
			PlayerNameString = UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper();
		}

		// #rc3_legacy_censor
		/*if IsPS4() and GPS4IsUGCRestricted(-1) and GAIsPlayerCustom(player_db_id) then
				player_name = "[ID_XBOX_CENSORED_NAME]";
		end;*/

		FString PlayerRatingString = FString::FromInt(player_data->GetOverallRating());

		FString PlayerPositionString = SIFGameHelpers::GAConvertMabStringToFString(player_data->GetPositionR15Text(0));

		int32 team_db_id = SIFGameHelpers::GAGetTeamIDFromIndex(TeamIndex);
		// If we have a valid team ID, check if it's a Sevens team
		if (team_db_id > 0)
		{
			if (SIFGameHelpers::GAIsTeamR7(team_db_id))
			{
				PlayerPositionString = SIFGameHelpers::GAConvertMabStringToFString(player_data->GetPositionR7Text(0));
			}
		}

		// since this is a string table entry, we know we can remove the last character and add '_UPPER]' on the end.
		PlayerPositionString = PlayerPositionString.LeftChop(1) + "_UPPER]";

		MabString FlagPath = SIFGameHelpers::GAGetPlayersTeamFlag(PlayerDatabaseID);

		PopulateShortProfileData(PlayerNameString, PlayerRatingString, PlayerPositionString, nullptr, PlayerDatabaseID, ComparedPlayerDatabaseID, team_db_id, 0, false, AtlasIndex);
	}
	else
	{
		// Hide all the nodes
	}
}

void UWWUIUserWidgetPlayerProfile::PopulateShortProfileData(FString PlayerName, FString PlayerRating, FString PlayerPosition, UTexture2D* pFlagTexture, int32 PlayerDatabaseID, int32 ComparedPlayerDatabaseID, int32 TeamDatabaseID, int32 LineupIndex /* = 0*/, bool ProPostMatch /*= false*/, int32 AtlasIndex /* = 0*/, bool showStatChanges /*= false*/)
{
	UTextBlock* pPlayerName = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(this, WWUIScreenCareerTeamSquad_UI::TextPlayerName));

	if (pPlayerName)
	{
		pPlayerName->SetText(FText::FromString(PlayerName).ToUpper());
		pPlayerName->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	}

	UTextBlock* pPlayerRating = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(this, WWUIScreenCareerTeamSquad_UI::TextRating));

	if (pPlayerRating)
	{
		pPlayerRating->SetText(FText::FromString(PlayerRating));
		pPlayerRating->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	}

	UTextBlock* pPlayerPosition = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(this, WWUIScreenCareerTeamSquad_UI::TextPositionLarge));

	if (pPlayerPosition)
	{
		pPlayerPosition->SetText(FText::FromString(PlayerPosition));
		pPlayerPosition->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	}

	// flag
	UImage* pFlagImage = Cast<UImage>(ScreenRef->FindChildOfTemplateWidget(this, WWUIScreenCareerTeamSquad_UI::ImageNationality));

	if (pFlagImage)
	{
		pFlagImage->SetVisibility(ESlateVisibility::SelfHitTestInvisible);

		if (pFlagTexture)
		{
			pFlagImage->SetBrushFromTexture(pFlagTexture);
		}
		else
		{
			ensure(pFlagTexture);
		}
	}

	UWWUIUserWidgetPlayerFaceImage* pPlayerImage = Cast<UWWUIUserWidgetPlayerFaceImage>(ScreenRef->FindChildOfTemplateWidget(this, WWUIScreenCareerTeamSquad_UI::PlayerFaceImage));

	if (pPlayerImage)
	{
		pPlayerImage->SetVisibility(ESlateVisibility::SelfHitTestInvisible);

		// Just gonna set the screenref here just in case :)
		pPlayerImage->ScreenRef = ScreenRef;

		if (PlayerDatabaseID == DB_SERVER_PLAYER_ID)
		{
			pPlayerImage->RefreshPlayerFaceImage(LineupIndex, TeamDatabaseID, true, AtlasIndex);
		}
		else
		{
			pPlayerImage->RefreshPlayerFaceImage(PlayerDatabaseID, TeamDatabaseID, true, AtlasIndex);
		}
	}

	UWWUIUserWidgetPlayerAttributes* pPlayerAttributesWidget = Cast<UWWUIUserWidgetPlayerAttributes>(ScreenRef->FindChildWidget(WWUIScreenCareerTeamSquad_UI::CareerHorizontalPlayerStats));
	// Refresh player attributes
	if (pPlayerAttributesWidget)
	{
		pPlayerAttributesWidget->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		pPlayerAttributesWidget->RefreshAttributes(PlayerDatabaseID, ComparedPlayerDatabaseID, false, LineupIndex, ProPostMatch, showStatChanges);
	}

	//Showing the username of the player that created the player you're editing
	UWidget* pDownloadedPlayerInfoWidget = ScreenRef->FindChildWidget(WWUIScreenCareerTeamSquad_UI::DownloadedPlayerInfo);
	if (pDownloadedPlayerInfoWidget)
	{
#ifdef SHOW_UGC_CREATOR
		UTextBlock* pCreatedByText = Cast<UTextBlock>(ScreenRef->FindChildWidget(WWUIScreenCareerTeamSquad_UI::TextCreatedBy));
		RU_PlayerDB_Data* pPlayerDatabaseData = nullptr;
		
		FString creatorName = "";
		FString uploaderName = "";
		if (PlayerDatabaseID == 1)
		{
			//This deals with a custom player inside a custom team
			RUDB_TEAM *custom_team = NULL;
			custom_team = SIFUIHelpers::GetQueryManager()->GetTeamData();
			if (custom_team)
			{
				MabVector< unsigned short > player_lineup;
				custom_team->GetAllPlayersInDisplayOrder(player_lineup, true);

				RUDB_PLAYER* server_player;
				RUDB_TEAM*	customisation_team = NULL;
				URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

				if (pRugbyGameInstance)
				{
					RUUIDatabaseQueryManager* pDatabaseQueryManager = pRugbyGameInstance->GetDbQueryManager();
					if (pDatabaseQueryManager)
					{
						customisation_team = (RUDB_TEAM*)pDatabaseQueryManager->GetTeamData();

						server_player = customisation_team->GetServerRUDBPlayerAtIndex(LineupIndex).Get();
						if (server_player != NULL)
						{
							creatorName = server_player->GetCreatedBy();
							uploaderName = server_player->GetUploadedBy();
						}
					}
				}
			}
		}
		else
		{
			RUDBHelperInterface* pDatabaseHelperInterface = SIFApplication::GetApplication()->GetGameDBHelper();
			if (pDatabaseHelperInterface)
			{
				pPlayerDatabaseData = pDatabaseHelperInterface->LoadPlayerDBData(PlayerDatabaseID);
			}

			if (pPlayerDatabaseData /*&& pPlayerDatabaseData->GetIsCustom()*/)
			{
				if (pPlayerDatabaseData->GetDbId() == 0)
				{
					//This means we're viewing a FanHub player
					creatorName = SIFGameHelpers::GAConvertMabStringToFString(pPlayerDatabaseData->GetCreatedBy());
					uploaderName = SIFGameHelpers::GAConvertMabStringToFString(pPlayerDatabaseData->GetUploadedBy());
				}
				else if (SIFGameHelpers::GAGetPlayerDownloadUser(pPlayerDatabaseData->GetDbId()) != "")
				{
					//This means we're viewing a previously downloaded player
					FString downloadInfo = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetPlayerDownloadUser(pPlayerDatabaseData->GetDbId()));
					creatorName = downloadInfo;

					downloadInfo.Split(",", &creatorName, &uploaderName);
				}
			}
		}

		bool showUsername = true;
		//We don't want to show this in a match, for things like manage team or the injury screen
		if (SIFApplication::GetApplication()->GetActiveGameWorld() && SIFApplication::GetApplication()->GetActiveGameWorld()->GetWorldId() != WORLD_ID::MENU)
		{
			showUsername = false;
		}
		//We also don't want this showing in career hub
		if (SIFApplication::GetApplication()->GetCareerModeManager() && SIFApplication::GetApplication()->GetCareerModeManager()->IsActive())
		{
			showUsername = false;
		}

		if (showUsername && pCreatedByText && creatorName != "")
		{
			FString CreatedByString = "[ID_CREATED_BY]: " + creatorName + "\n[ID_UPLOADED_BY]: " + uploaderName;

			pCreatedByText->SetText(FText::FromString(UWWUITranslationManager::Translate(CreatedByString)));
			pDownloadedPlayerInfoWidget->SetVisibility(ESlateVisibility::Visible);
		}
		else
#endif
		{
			pDownloadedPlayerInfoWidget->SetVisibility(ESlateVisibility::Collapsed);
		}
	}
}

void UWWUIUserWidgetPlayerProfile::SetShortProfileToRecruit()
{
	// setting image to default
	UWWUIUserWidgetPlayerFaceImage* pPlayerImage = Cast<UWWUIUserWidgetPlayerFaceImage>(ScreenRef->FindChildOfTemplateWidget(this, WWUIScreenCareerTeamSquad_UI::PlayerFaceImage));

	if (pPlayerImage)
	{
		pPlayerImage->RefreshPlayerFaceImage(nullptr, true);	// sets the image to the sillhouette 
	}
	else
	{
		ensure(pPlayerImage);
	}

	UTextBlock* name_node		= Cast<UTextBlock>(UWWUIFunctionLibrary::FindWidget(ScreenRef, WWUIScreenCareerTeamSquad_UI::TextPlayerName));
	UTextBlock* position_node	= Cast<UTextBlock>(UWWUIFunctionLibrary::FindWidget(ScreenRef, WWUIScreenCareerTeamSquad_UI::TextPositionLarge));
	UWidget* attributes_list	= UWWUIFunctionLibrary::FindWidget(ScreenRef, WWUIScreenCareerTeamSquad_UI::CareerHorizontalPlayerStats);
	UWidget* rating_node		= UWWUIFunctionLibrary::FindWidget(ScreenRef, WWUIScreenCareerTeamSquad_UI::OverlayRating);
	UWidget* portrait_node		= UWWUIFunctionLibrary::FindWidget(ScreenRef, WWUIScreenCareerTeamSquad_UI::ImagePlayerPortrait);
	UWidget* flag_node			= UWWUIFunctionLibrary::FindWidget(ScreenRef, WWUIScreenCareerTeamSquad_UI::ImageNationality);

	UWWUIFunctionLibrary::SetText(name_node, UWWUITranslationManager::Translate(FString("[ID_RECRUIT_PLAYER]")));
	UWWUIFunctionLibrary::SetVisibility(rating_node, false);
	UWWUIFunctionLibrary::SetVisibility(position_node, false);
	UWWUIFunctionLibrary::SetVisibility(flag_node, false);
	UWWUIFunctionLibrary::SetVisibility(portrait_node, true);
	UWWUIFunctionLibrary::SetVisibility(attributes_list, false);

	if (position_node && attributes_list && rating_node && flag_node && portrait_node && name_node)
	{
		UWWUIFunctionLibrary::SetText(name_node, UWWUITranslationManager::Translate(FString("[ID_RECRUIT_PLAYER]")));
		UWWUIFunctionLibrary::SetVisibility(rating_node, false);
		UWWUIFunctionLibrary::SetVisibility(position_node, false);
		UWWUIFunctionLibrary::SetVisibility(flag_node, false);
		UWWUIFunctionLibrary::SetVisibility(portrait_node, true);
		UWWUIFunctionLibrary::SetVisibility(attributes_list, false);
	}
	else
	{
		ensure(position_node && attributes_list && rating_node && flag_node && portrait_node && name_node);
	}

	UWidget* pDownloadedPlayerInfoWidget = ScreenRef->FindChildWidget(WWUIScreenCareerTeamSquad_UI::DownloadedPlayerInfo);
	if (pDownloadedPlayerInfoWidget)
	{
		pDownloadedPlayerInfoWidget->SetVisibility(ESlateVisibility::Collapsed);
	}
}