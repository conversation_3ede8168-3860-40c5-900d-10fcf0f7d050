/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#pragma once

#include "Match/RugbyUnion/RUSettingsEnums.h"

#include "Match/RugbyUnion/CompetitionMode/RUDBCompetitionTypes.h"
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "Match/RugbyUnion/CompetitionMode/RUActiveCompetition.h"

#include "Match/SSPlayDirection.h"
#include "Match/Components/RUPlayerAttributes.h"

#include "Match/RugbyUnion/RUGameSettingsTeamSettings.h"

#include "RUGameSettings.generated.h"

//class RL3Database;

/**
// Stores data passed when creating an RL3Game.
//
// <AUTHOR> Baker.
//		   ported by RH.
*/

// foward declarations
class RUTeam;
class URugbyGameWorldSettings;

USTRUCT()
struct FRugbyGameSettings
{
	GENERATED_BODY()

private:
#ifdef ENABLE_PRO_MODE
	bool				is_be_a_pro;
	int					pro_jersey_number;
#endif

	GAME_MODE			gameMode;

public:
	FRugbyGameSettings();

	GAME_MODE GetGameMode() const { return gameMode; }

	bool GameModeIsR7() const { return gameMode == GAME_MODE_RU13W; } // Nick  WWS 7s to Womens // GAME_MODE_SEVENS;}
	bool GameModeIsR13() const { return gameMode == GAME_MODE_RU13; }

	void SetGameMode(GAME_MODE newGameMode)
	{
		MABLOGDEBUG("GameMode just got changed to %s", (newGameMode == GAME_MODE_RU13 ? "R13" : "R13W"));
		gameMode = newGameMode;
	}

#ifdef ENABLE_PRO_MODE
	// Add these pro mode things - Dewald WW
	bool GetIsAProMode() const { return is_be_a_pro; }
	void SetIsAProMode(bool newVal) { is_be_a_pro = newVal; }

	int GetProControllerIndex() const { return 0; }

	/*int					GetProJersey()				{ return pro_jersey_number; }

	// Add this for now to check if a SIFGameObject is a pro player by using the attributes
	// TODO, move into promodemanager, and clean up.
	bool				IsProPlayer(ARugbyCharacter* playerObject)
	{
		return playerObject->GetAttributes()->GetDbId() == pro_jersey_number;
	}

	// Deprecated for now
	//bool				IsProJersey(int jerseyNum)	{ return pro_jersey_number == jerseyNum; }
	void				SetProJersey(int newVal)	{ pro_jersey_number = newVal; }*/
#endif

	bool				is_grand_final;
	GAME_TYPE			game_type;
	bool				extra_time_enabled;
	int					num_interchanges;
	int					game_length = 0; // THIS IS THE REAL WORLD LENGTH IN MINUTES, NOT THE LENGTH OF THE GAME - Dewald WW
	SSTEAMSIDE			initial_kickoff_team;
	std::vector<ERugbyPlayDirection>		initial_play_direction;

	SSTEAMSIDE			initial_kickoff_team_extra_time_coin_toss;
	std::vector<ERugbyPlayDirection>		initial_play_direction_extra_time_coin_toss;

	MabUInt64			enabled_triggers;
	bool				change_favourite_team;

	// Custom rules.
	bool				custom_rule_forward_pass;		// turn forward passes off, on by default
	bool				custom_rule_offside;			// turn offsides off, on by default
	bool				custom_rule_sendoffs_enabled;	// sendoffs on by default.
	bool				custom_rule_sinbins_enabled;	// sinbins enabled?
	bool				custom_rule_fatigue_enabled;	// player fatigue enabled?
	bool				custom_rule_offloads;		// Are offloads allowed in the game?
	bool				custom_rule_injuries;		// turn injuries off, on by default
	bool				custom_rule_video_ref;		// turn video ref decisions off, on by default
	bool				custom_rule_slo_mo;			// turn on/off slo mo kicking
	bool				custom_rule_knock_ons;		// turn knock ons off, on by default

	// Networking.
	bool				network_game;				// Is this a network game
	bool				network_replays;			// Is this network game using replays
	bool				network_cutscenes;			// Is this network game using cutscenes
	bool				private_match;				// Is this network game a private match (or a quick match?)
	bool				network_balanced_stats;		// Is the network game using balanced stats

	// Gameplay sliders
	float				slider_injury_frequency;
	float				slider_high_tackle_frequency;
	float				slider_offload_frequency;
	float				slider_offload_success_rate;
	float				slider_pass_success_rate;
	float				slider_break_tackle_ability;
	float				slider_tackle_ability;
	float				slider_prop_breakthrough;
	float				slider_fourtytwenty_kick_frequency;
	float				slider_attacking_urgency;
	float				slider_defensive_urgency;
	float				slider_intercept_frequency;
	float				slider_offload_chance;
	float				slider_ruck_tackle_bonus;
};

USTRUCT()
struct FRugbyWeatherSettings
{
	GENERATED_BODY()

public:
	FRugbyWeatherSettings();

	LIGHTING_CONDITIONS lighting_conditions;
	float               wind_strength;
	FVector				wind_direction;
	bool				raining;

	// to allow for overcast nights in commentary
	TIMEOFDAY			time_of_day;

	// in conjunction with raining, can work out if sunny or overcast was selected
	bool				overcast_selected;
};

// Crowd.
USTRUCT()
struct FRugbyCrowdSettings
{
	GENERATED_BODY()

public:
	FRugbyCrowdSettings();

	CROWD_SIZE	crowd_size;
	int			crowd_density;
};

USTRUCT()
struct FRugbyStadiumSettings
{

	GENERATED_BODY()


public:
	FRugbyStadiumSettings();

	UPROPERTY()
	FString stadium_abbr;

	FIELD_SIGNAGE field_signage;

	void SetStadiumAbbreviation(const char* _stadium_abbr) { stadium_abbr = UTF8_TO_TCHAR(_stadium_abbr); }
	const char* GetStadiumAbbreviation() const;

	unsigned short GetStadiumID() const;
	RUDB_STADIUM::STADIUM_SIZE_TYPE GetStadiumSize() const;

private:
	static char stadium_abbr_cstr[8];
};

USTRUCT()
struct FRugbyHumanPlayerSettings
{
	GENERATED_BODY()

public:
	FRugbyHumanPlayerSettings();

	int	team; // Which team they belong to, with 0 being the first index (naturally! DIE NON-UTF FACE). -1 denotes an AI player
	int player_id; // The Unreal player id for this player

	int controller_id;
	int peer_id; // The id of the peer that this applies to
};

USTRUCT()
struct FRugbyCompetitionSettings
{
	GENERATED_BODY()

public:
	FRugbyCompetitionSettings();

	/// The primary key id of the competition definition selected by the player.
	int					competition_definition_id;

	/// The primary key id of the team selected from the teams table for this competition.
	int					team_id;
};
 
 USTRUCT()
 struct FRugbyGameModeLimits
 {
 	GENERATED_BODY()
 
 public:
	 FRugbyGameModeLimits(const URugbyGameWorldSettings* gameSettings = nullptr) : mGameSettings(gameSettings) {}
 
 private:
 
///// 	/* WJS RLS TODO REMOVE
///// 	// consts for Rugby Union 15 player games
///// 	static const int NUM_TEAMS_U15 = 2;
///// 	static const int NUM_BENCH_PLAYERS_U15 = 8;
///// 	static const int NUM_NON_PLAYING_PLAYERS_U15 = 8; // There is no "proper" value for this, but this is used as a nice default for the team creator.
///// 	static const int NUM_PLAYERS_PER_TEAM_U15 = 15;
///// 	static const int NUM_OFFICIALS_U15 = 3;
///// 	static const int NUM_FORWARD_PLAYERS_U15 = 8;	// Num players in scrum.
///// 	static const int NUM_SUBSTITUTIONS_ALLOWED_U15 = 8;
///// 	static const int INSTANT_ACTION_GAME_LENGTH_U15 = 10;
///// 	static const int ACTUAL_HALF_LENGTH_U15 = 40;
///// 	static const int ACTUAL_GAME_LENGTH_U15 = 2 * ACTUAL_HALF_LENGTH_U15;
///// 	static const int EXTRATIME_ACTUAL_HALF_LENGTH_U15 = 10;
///// 	//*/
///// 
///// 
	static const int NUM_TEAMS_U13						= 2;
	static const int NUM_BENCH_PLAYERS_U13				= 4;
	static const int NUM_NON_PLAYING_PLAYERS_U13		= 8;	// There is no "proper" value for this, but this is used as a nice default for the team creator.
	static const int NUM_PLAYERS_PER_TEAM_U13			= 13;
	static const int NUM_OFFICIALS_U13					= 3;
	static const int NUM_FORWARD_PLAYERS_U13			= 6;	// Num players in scrum.
	static const int NUM_SUBSTITUTIONS_ALLOWED_U13		= 4;
	static const int INSTANT_ACTION_GAME_LENGTH_U13		= 10;	// WJS RLC Not sure what this is for or what value it should be yet
	static const int ACTUAL_HALF_LENGTH_U13				= 40;
	static const int ACTUAL_GAME_LENGTH_U13				= 2 * ACTUAL_HALF_LENGTH_U13;
	static const int EXTRATIME_ACTUAL_HALF_LENGTH_U13	= 10;
	
	
	// consts for Rugby Sevens teams
	static const int NUM_TEAMS_SEVENS					= 2;
	static const int NUM_BENCH_PLAYERS_SEVENS			= 5;
	static const int NUM_NON_PLAYING_PLAYERS_SEVENS		= 5; // There is no "proper" value for this, but this is used as a nice default for the team creator.
	static const int NUM_PLAYERS_PER_TEAM_SEVENS		= 7;
	static const int NUM_PLAYERS_SPARE					= 1;
	static const int NUM_OFFICIALS_SEVENS				= 5;
	static const int NUM_FORWARD_PLAYERS_SEVENS			= 3;	// Num players in scrum.
	static const int NUM_SUBSTITUTIONS_ALLOWED_SEVENS	= 5;
	static const int INSTANT_ACTION_GAME_LENGTH_SEVENS	= 7;
	static const int ACTUAL_HALF_LENGTH_SEVENS			= 7;
	static const int EXTRA_HALF_LENGTH_FOR_FINALS_SEVENS	= 3;
	static const int ACTUAL_GAME_LENGTH_SEVENS				= 2 * ACTUAL_HALF_LENGTH_SEVENS;
	static const int EXTRATIME_ACTUAL_HALF_LENGTH_SEVENS	= 5;
///// 
	bool IsGameModeR7() const;
	bool IsGrandFinal() const;
///// 
public:
///// 
#ifdef ENABLE_SEVENS_MODE
	// Extra game mode functions that we'll switch in the real function if we have sevens mode defined
	int GetNumberOfTeamsR7() { return NUM_TEAMS_SEVENS; }
	int GetNumberOfTeamsR13() { return NUM_TEAMS_U13; }
	int GetNumberOfTeamsPlusOfficialsR7() { return GetNumberOfTeamsR7() + 1; }
	int GetNumberOfTeamsPlusOfficialsR13() { return GetNumberOfTeamsR13() + 1; }
	int GetNumberOfBenchPlayersR7() { return NUM_BENCH_PLAYERS_SEVENS; }
	int GetNumberOfBenchPlayersR13() { return NUM_BENCH_PLAYERS_U13; }
	int GetNumberOfNonPlayingPlayersR7() { return NUM_NON_PLAYING_PLAYERS_SEVENS; }
	int GetNumberOfNonPlayingPlayersR13() { return NUM_NON_PLAYING_PLAYERS_U13; }
	int GetNumberOfPlayersPerTeamR7() { return NUM_PLAYERS_PER_TEAM_SEVENS; }
	int GetNumberOfPlayersPerTeamR13() { return NUM_PLAYERS_PER_TEAM_U13; }
	int GetNumberOfOfficialsR7() { return NUM_OFFICIALS_SEVENS; }
	int GetNumberOfOfficialsR13() { return NUM_OFFICIALS_U13; }
	int GetNumberOfPlayersR7() { return (GetNumberOfPlayersPerTeamR7() + GetNumberOfBenchPlayersR7())	* GetNumberOfTeamsR7(); }
	int GetNumberOfPlayersR13() { return (GetNumberOfPlayersPerTeamR13() + GetNumberOfBenchPlayersR13()) * GetNumberOfTeamsR13(); }
	int GetNumberOfPlayersPerTeamIncBenchR7() { return GetNumberOfPlayersPerTeamR7() + GetNumberOfBenchPlayersR7(); }
	int GetNumberOfPlayersPerTeamIncBenchR13() { return GetNumberOfPlayersPerTeamR13() + GetNumberOfBenchPlayersR13(); }
	int GetNumberOfForwardPlayersR7() { return NUM_FORWARD_PLAYERS_SEVENS; }
	int GetNumberOfForwardPlayersR13() { return NUM_FORWARD_PLAYERS_U13; }
	int GetNumberOfPlayersInFormationR7() { return GetNumberOfPlayersPerTeamR7() + 1; }
	int GetNumberOfPlayersInFormationR13() { return GetNumberOfPlayersPerTeamR13() + 1; }
	int GetNumberOfPlayersInScrumR7() { return NUM_FORWARD_PLAYERS_SEVENS; }
	int GetNumberOfPlayersInScrumR13() { return NUM_FORWARD_PLAYERS_U13; }
	int GetInstantActionGameLengthR7() { return INSTANT_ACTION_GAME_LENGTH_SEVENS; }
	int GetInstantActionGameLengthR13() { return INSTANT_ACTION_GAME_LENGTH_U13; }
	int GetActualHalfLengthsInFinalsR7() { return ACTUAL_HALF_LENGTH_SEVENS + EXTRA_HALF_LENGTH_FOR_FINALS_SEVENS; }
	int GetActualHalfLengthsInFinalsR13() { return ACTUAL_HALF_LENGTH_U13; }

	// In the HSBC Sevens World Series, only the Cup final, which determines the overall winner of an event,
	// is played with 10-minute halves; all finals for lower-level trophies are played with 7-minute halves
	int GetActualHalfLengthsR7() { return IsGrandFinal() ? GetActualHalfLengthsInFinals() : ACTUAL_HALF_LENGTH_SEVENS; }
	int GetActualHalfLengthsR13() { return IsGrandFinal() ? GetActualHalfLengthsInFinals() : ACTUAL_HALF_LENGTH_U13; }
	int GetActualGameLengthR7() { return IsGrandFinal() ? GetActualGameLengthInFinals() : ACTUAL_GAME_LENGTH_SEVENS; }
	int GetActualGameLengthR13() { return IsGrandFinal() ? GetActualGameLengthInFinals() : ACTUAL_GAME_LENGTH_U13; }

	int GetActualGameLengthInFinalsR7() { return ACTUAL_GAME_LENGTH_SEVENS + (EXTRA_HALF_LENGTH_FOR_FINALS_SEVENS * 2); }
	int GetActualGameLengthInFinalsR13() { return ACTUAL_GAME_LENGTH_U13; }
	int GetExtraActualHalfLengthR7() { return EXTRATIME_ACTUAL_HALF_LENGTH_SEVENS; }
	int GetExtraActualHalfLengthR13() { return EXTRATIME_ACTUAL_HALF_LENGTH_U13; }
#endif
	
	int GetNumberOfPlayersSpare()			const { return NUM_PLAYERS_SPARE; }
	int GetNumberOfTeams()					const { return GetNumberOfTeams(IsGameModeR7()); }
	int GetNumberOfTeamsPlusOfficials()		const { return GetNumberOfTeamsPlusOfficials(IsGameModeR7()); }
	int GetNumberOfBenchPlayers()			const { return GetNumberOfBenchPlayers(IsGameModeR7()); }
	int GetNumberOfNonPlayingPlayers()		const { return GetNumberOfNonPlayingPlayers(IsGameModeR7()); }
	int GetNumberOfPlayersPerTeam()			const { return GetNumberOfPlayersPerTeam(IsGameModeR7()); }
	int GetNumberOfOfficials()				const { return GetNumberOfOfficials(IsGameModeR7()); }
	int GetNumberOfPlayers()				const { return GetNumberOfPlayers(IsGameModeR7()); }
	int GetNumberOfPlayersPerTeamIncBench() const { return GetNumberOfPlayersPerTeamIncBench(IsGameModeR7()); }
	int GetNumberOfForwardPlayers()			const { return GetNumberOfForwardPlayers(IsGameModeR7()); }
	int GetNumberOfPlayersInFormation()		const { return GetNumberOfPlayersInFormation(IsGameModeR7()); }
	int GetNumberOfPlayersInScrum()			const { return GetNumberOfPlayersInScrum(IsGameModeR7()); }
	int GetInstantActionGameLength()		const { return GetInstantActionGameLength(IsGameModeR7()); }
	int GetActualHalfLengthsInFinals()		const { return GetActualHalfLengthsInFinals(IsGameModeR7()); }
	int GetActualHalfLengths()				const { return GetActualHalfLengths(IsGameModeR7(), IsGrandFinal()); }
	int GetActualGameLength()				const { return GetActualGameLength(IsGameModeR7(), IsGrandFinal()); }
	int GetActualGameLengthInFinals()		const { return GetActualGameLengthInFinals(IsGameModeR7()); }
	int GetExtraActualHalfLength()			const { return GetExtraActualHalfLength(IsGameModeR7()); }
 
 
 	// WJS RLC ##### Add Rugby league values here
 	static int GetNumberOfTeams(bool sevens)
 	{
 		return sevens ? NUM_TEAMS_SEVENS : NUM_TEAMS_U13;
 	}
 
 	static int GetNumberOfTeamsPlusOfficials(bool sevens)
 	{
 		return GetNumberOfTeams(sevens) + 1;
 	}
 
 	static int GetNumberOfBenchPlayers(bool sevens)
 	{
 		return sevens ? NUM_BENCH_PLAYERS_SEVENS : NUM_BENCH_PLAYERS_U13;
 	}
 
 	static int GetNumberOfNonPlayingPlayers(bool sevens)
 	{
 		return sevens ? NUM_NON_PLAYING_PLAYERS_SEVENS : NUM_NON_PLAYING_PLAYERS_U13;
 	}
 
 	static int GetNumberOfPlayersPerTeam(bool sevens)
 	{
 		return sevens ? NUM_PLAYERS_PER_TEAM_SEVENS : NUM_PLAYERS_PER_TEAM_U13;
 	}
 
 	static int GetNumberOfOfficials(bool sevens)
 	{
 		return sevens ? NUM_OFFICIALS_SEVENS : NUM_OFFICIALS_U13;
 	}
 
 	static int GetNumberOfPlayers(bool sevens)
 	{
 		return GetNumberOfPlayersPerTeamIncBench(sevens) * GetNumberOfTeams(sevens);
 	}
 
 	static int GetNumberOfPlayersPerTeamIncBench(bool sevens)
 	{
 		return GetNumberOfPlayersPerTeam(sevens) + GetNumberOfBenchPlayers(sevens);
 	}
 
 	static int GetNumberOfForwardPlayers(bool sevens)
 	{
 		return sevens ? NUM_FORWARD_PLAYERS_SEVENS : NUM_FORWARD_PLAYERS_U13;
 	}
 
 	static int GetNumberOfPlayersInFormation(bool sevens)
 	{
 		return GetNumberOfPlayersPerTeam(sevens) + 1;
 	}
 
 	static int GetNumberOfPlayersInScrum(bool sevens)
 	{
 		return GetNumberOfForwardPlayers(sevens); //Coincidentally this is exactly correct, 8 for fifteens, 3 for sevens.
 	}
 
 	static int GetInstantActionGameLength(bool sevens)
 	{
 		return sevens ? INSTANT_ACTION_GAME_LENGTH_SEVENS : INSTANT_ACTION_GAME_LENGTH_U13;
 	}
 
 	static int GetActualHalfLengthsInFinals(bool sevens)
 	{
 		return sevens ? ACTUAL_HALF_LENGTH_SEVENS + EXTRA_HALF_LENGTH_FOR_FINALS_SEVENS : ACTUAL_HALF_LENGTH_U13;
 	}
 
 	static int GetExtraActualHalfLength(bool sevens)
 	{
 		return sevens ? EXTRATIME_ACTUAL_HALF_LENGTH_SEVENS : EXTRATIME_ACTUAL_HALF_LENGTH_U13;
 	}
 
 	static int GetActualHalfLengths(bool sevens, bool finals)
 	{
 		return finals ? GetActualHalfLengthsInFinals(sevens) : (sevens ? ACTUAL_HALF_LENGTH_SEVENS : ACTUAL_HALF_LENGTH_U13);
 	}
 
 	static int GetActualGameLength(bool sevens, bool finals)
 	{
 		return GetActualHalfLengths(sevens, finals) * 2;
 	}
 
 	static int GetActualGameLengthInFinals(bool sevens)
 	{
 		return GetActualHalfLengthsInFinals(sevens) * 2;
 	}
 
	const URugbyGameWorldSettings* mGameSettings;
 };

UCLASS()
class URugbyGameWorldSettings final : public UObject
{
	GENERATED_BODY()
		 
private:


	// WJS RLC ##### TODO Do we need to worry about these score differences
	// WJS RLC No as we are not have multiple laws
	// Refer Cole March 2025
	///static const int IRB_CONVERSION_SCORE = 2;
	//static const int NRC_CONVERSION_SCORE = 3;
	//static const int IRB_PENALTY_SCORE = 3;
	//static const int NRC_PENALTY_SCORE = 2;
	//static const int IRB_DROP_GOAL_SCORE = 3;
	//static const int NRC_DROP_GOAL_SCORE = 2;



	static const int LEAGUE_DROP_GOAL_SCORE		= 1;
	static const int LEAGUE_DROP_GOAL_40M_SCORE = 2;
	static const int LEAGUE_CONVERSION_SCORE	= 2;
	static const int LEAGUE_TRY_SCORE			= 4;
	static const int LEAGUE_PENALTY_GOAL_SCORE	= 2;




public:
	URugbyGameWorldSettings( /*RL3Database* database */);
	//virtual ~URugbyGameWorldSettings() {}

	void printState() const;

	// Called on game exit, to prepare for next game.
	void Reset( MabUInt32 initial_seed = 0);
	void Clear( bool dont_clear_teams = false ); // Clears all data out.

	virtual bool IsSupportedForNetworking() const override { return true; }

	int GetGameLength( int index );
	int GetSingleGameLength( int index );
	int GetGameLengthIndex( int game_length );
	void CalculateCrowdSize();
	void SetupOfficials();
	void NormaliseStats();

	int GetConversionScore() const;
	int GetConversionScore( GAME_LAW for_law ) const;
	int GetPenaltyScore() const;
	int GetPenaltyScore( GAME_LAW for_law ) const;
	int GetDropGoalScore( bool beyond40 ) const;
	int GetDropGoalScore( GAME_LAW for_law, bool beyond40 ) const;
	int GetTryScore() const;
	int GetTryScore(GAME_LAW for_law) const;


	/// Set the primary key id of the competition the player has selected to compete in.
	void SetCompetitionDefinitionID( int competition_id ) { competition_settings.competition_definition_id = competition_id; }
	int GetCompetitionDefinitionID() const { return competition_settings.competition_definition_id; }

	/// Set the primary key id of the team the player has chosen to compete as in a competition
	void SetCompetitionTeamID( int competition_team_id ) { competition_settings.team_id = competition_team_id; }
	int GetCompetitionTeamID() const { return competition_settings.team_id; }

	/// Fills the Half and Full time stats window
	void PopulateStatsWindow( bool full_time );

	/// Get the current difficulty offset from the default
	float GetCurrentDifficultyOffsetFromDefault() const { return float(difficulty) - float(DEFAULT_DIFFICULTY); }
	
	FRugbyHumanPlayerSettings* GetHumanPlayerSettings(EHumanPlayerSlot playerSlot);
	FRugbyHumanPlayerSettings* GetHumanPlayerSettingsFromPlayerId(int playerId);
	FRugbyHumanPlayerSettings* GetHumanPlayerSettingsFromControllerId(int controllerId);
	
	//UPROPERTY(ReplicatedUsing = OnRep_GameSettings)		FRugbyGameSettings				game_settings;
	//UPROPERTY(ReplicatedUsing = OnRep_WeatherSettings)	FRugbyWeatherSettings			weather_settings;
	//UPROPERTY(ReplicatedUsing = OnRep_CrowdSettings)	FRugbyCrowdSettings				crowd_settings;
	//UPROPERTY(ReplicatedUsing = OnRep_StadiumSettings)	FRugbyStadiumSettings			stadium_settings;
	//
	////typedef TArray<FRugbyTeamSettings> FRugbyTeamSettingsList;
	//UPROPERTY(ReplicatedUsing = OnRep_TeamSettings)		TArray<FRugbyTeamSettings>			team_settings;
	////typedef TArray<FRugbyHumanPlayerSettings> FRugbyHumanPlayerSettingsList;
	//UPROPERTY(ReplicatedUsing = OnRep_HumanPlayerSettings)	TArray<FRugbyHumanPlayerSettings>	human_player_settings;

	FRugbyGameSettings				game_settings;
	FRugbyWeatherSettings			weather_settings;
	FRugbyCrowdSettings				crowd_settings;
	FRugbyStadiumSettings			stadium_settings;

	TArray<FRugbyTeamSettings>			team_settings;
	TArray<FRugbyHumanPlayerSettings>	human_player_settings;

	FRugbyGameModeLimits		game_limits;
	FRugbyCompetitionSettings	competition_settings;

	DIFFICULTY					difficulty;
	GAME_LAW					game_law;
	SUBSTITUTION_MODE			substitution_mode;
	int							num_active_teams;
	MabUInt32					initial_random_seed;

	typedef FRugbyGameSettings			RU_GAME_SETTINGS;
	typedef FRugbyWeatherSettings		RU_WEATHER_SETTINGS;
	typedef FRugbyCrowdSettings			RU_CROWD_SETTINGS;
	typedef FRugbyStadiumSettings		RU_STADIUM_SETTINGS;
	typedef FRugbyHumanPlayerSettings	RU_HUMAN_PLAYER_SETTINGS;
	typedef FRugbyCompetitionSettings	RU_COMPETITION_SETTINGS;
	typedef FRugbyGameModeLimits		RU_GAME_MODE_LIMITS;

	FSimpleMulticastDelegate m_onGameSettingsChanged;
	FSimpleMulticastDelegate m_onWeatherSettingsChanged;
	FSimpleMulticastDelegate m_onCrowdSettingsChanged;
	FSimpleMulticastDelegate m_onStadiumSettingsChanged;
	FSimpleMulticastDelegate m_onTeamSettingsChanged;
	FSimpleMulticastDelegate m_onHumanPlayerSettingsChanged;

private:

	UFUNCTION() void OnRep_GameSettings()			{ UE_LOG(LogTemp, Log, TEXT("Received game settings")); m_onGameSettingsChanged.Broadcast(); }
	UFUNCTION() void OnRep_WeatherSettings()		{ UE_LOG(LogTemp, Log, TEXT("Received weather settings")); m_onWeatherSettingsChanged.Broadcast(); }
	UFUNCTION() void OnRep_CrowdSettings()			{ UE_LOG(LogTemp, Log, TEXT("Received crowd settings")); m_onCrowdSettingsChanged.Broadcast(); }
	UFUNCTION() void OnRep_StadiumSettings()		{ UE_LOG(LogTemp, Log, TEXT("Received stadium settings")); m_onStadiumSettingsChanged.Broadcast(); }
	UFUNCTION() void OnRep_TeamSettings()			{ UE_LOG(LogTemp, Log, TEXT("Received team settings")); m_onTeamSettingsChanged.Broadcast(); }
	UFUNCTION() void OnRep_HumanPlayerSettings()	{ UE_LOG(LogTemp, Log, TEXT("Received human settings")); m_onHumanPlayerSettingsChanged.Broadcast(); }
};

typedef URugbyGameWorldSettings RUGameSettings;
