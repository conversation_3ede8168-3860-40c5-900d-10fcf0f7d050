// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#pragma once

#include "CoreMinimal.h"

/***********************************************************************
This header is used to define the debug options visible in the debug 
window. 
Steps to add to the debug options:
1: Define the name of the debug option and widget type in DebugOptionsList
		eg. DebugOption(FText::FromString("CUTSCENE ID"), WidgetType::EDITABLETEXT)

2: Bind the value being changed by this widget to this widget.
		eg. UWWUIScreenDebugWindow::AddToOptionsBindings(FString("CUTSCENE ID"), &CutSceneID);
		This code is called in the location the variable being changed is defined.

3: Add the widget to the array that determines it's return type.
		eg. FString("CUTSCENE ID") is in the IntegerOptions array as the CutsceneIndex value it is bound to is an integer.
		This is needed because variable bound to the option is bound as a void pointer, so the debug options need to know what type to cast
		the result to.
		note: Widgets do not NEED a return type if they are not bound to a value. For example: Cutscene name is used to hold the name
		of the current cutscene playing, but the player cannot manually change it, so it needs no return value.

(OPTIONAL: If your adding a new page)
4: If your adding a new page to the debug menu, You need to Add another element to the EDebugTabs enum class.
			eg.
			enum class EDebugTabs 
			{
				PAGE1,
				PAGE2,
				NEWPAGE, <---- *you want to add your new page before 'MAX'. 
				MAX 
			};

5: Once you've added the new enum element, you need to create an new debug Options list.
			This will contain all the elements/options you want in this new page.
			eg.
			static TArray<DebugOption> NewPageDebugOptionsList = 
			{
				DebugOption(FText::FromString("LOOK AT ME IM AN OPTION"), WidgetType::TICKBOX)
			};

6: Once the options list has been made, you need to add a new case inside a EDebugTabs Switch statement that uses your newly made enum element & options list. 
			This switch statement resides inside of UWWUIScreenDebugWindow::PopulateDebugOptions().
			eg.
			switch (currentTabIndex)
			{
			case EDebugTabs::PAGE1:
				DebugOptionsList = Page1DebugOptionsList;
				break;
			case EDebugTabs::PAGE2:
				DebugOptionsList = Page2DebugOptionsList;
				break;

			case EDebugTabs::NEWPAGE:							<----
				DebugOptionsList = NewPageDebugOptionsList;		<---- You add this entire case
				break;											<----

			}


		
*************************************************************************/



enum class WidgetType {
	EDITABLETEXT,
	TEXT,
	DROPDOWN,
	TICKBOX
};

enum class EDebugTabs {
	CUTSCENES,
	ANIMATIONS,
	RULES,
	RECORDING,
	CAREER,
	PRO,
	KICK,
	EMOTION,
	MAX
};

struct DebugOption
{
	FText DebugName;
	WidgetType widgetType;

	DebugOption()
	{
		DebugName = FText::FromString("");
		widgetType = WidgetType::TEXT;
	}

	DebugOption(FText inText, WidgetType inWidgetType)
	{
		DebugName = inText;
		widgetType = inWidgetType;
	}
};

//Array used to create all the cutscene option widgets.
static TArray<DebugOption> CutsceneDebugOptionsList = {
	DebugOption(FText::FromString("CUTSCENE ID"), WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString("CUTSCENE NAME"), WidgetType::TEXT),
	DebugOption(FText::FromString("SUBCUTSCENE"), WidgetType::DROPDOWN),
	DebugOption(FText::FromString("TRIGGERING TEAM INDEX"), WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString("CAMERA INDEX"), WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString("NUMBER OF CAMERAS"), WidgetType::TEXT),
	DebugOption(FText::FromString("CUTSCENE PLAYBACK SPEED"), WidgetType::DROPDOWN),
	DebugOption(FText::FromString("X OFFSET"), WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString("Y OFFSET"), WidgetType::EDITABLETEXT)
};

//Array used to create all the Animation option widgets.
static TArray<DebugOption> AnimationDebugOptionsList = {
	DebugOption(FText::FromString("ANIMATION ID"), WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString("ANIMATION NAME"), WidgetType::TEXT),
	DebugOption(FText::FromString("ANIMATION FACE ID"), WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString("ANIMATION PLAYBACK SPEED"), WidgetType::DROPDOWN),
	DebugOption(FText::FromString("LOCK TIME OF DAY"), WidgetType::TICKBOX),
	DebugOption(FText::FromString("TIME OF DAY"), WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString("HAS BALL"), WidgetType::TICKBOX),
	DebugOption(FText::FromString("PLAY TACKLES"), WidgetType::TICKBOX)
};

//Array used to create all the Rules option widgets.
#define DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_IS_HIGH "NEXT TACKLE IS HIGH"
#define DEBUGOPTIONS_TEXTFIELD_NEXT_HIGH_TACKLE_YELLOW_CARD "NEXT HIGH TACKLE YELLOW CARD"
#define DEBUGOPTIONS_TEXTFIELD_NEXT_YELLOW_IS_SECOND_YELLOW_CARD "NEXT YELLOW IS 2ND YELLOW CARD"
#define DEBUGOPTIONS_TEXTFIELD_NEXT_HIGH_TACKLE_RED_CARD "NEXT HIGH TACKLE RED CARD"
#define DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_IS_OFFSIDE "NEXT TACKLE IS OFFSIDE"
#define DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_CAUSES_INJURY "NEXT TACKLE CAUSES INJURY"
#define DEBUGOPTIONS_TEXTFIELD_NEXT_KICK_SOMEONE_IS_OFFISDE "NEXT KICK SOMEONE IS OFFISDE"
#define DEBUGOPTIONS_TEXTFIELD_NEXT_PASS_IS_FORWARD "NEXT PASS IS FORWARD"
#define DEBUGOPTIONS_TEXTFIELD_NEXT_HANDLE_KNOCK_ON "NEXT HANDLE KNOCK ON"
#define DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_FIFTH_TACKLE "NEXT TACKLE FIFTH TACKLE"
#define DEBUGOPTIONS_TEXTFIELD_ZERO_TIME_REMAINING "ZERO TIME REMAINING"
#define DEBUGOPTIONS_TEXTFIELD_NEXT_KICK_CATCH_IS_MARK "NEXT KICK CATCH IS MARK"
#define DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_TRY_PROBABILITY "NEXT TACKLE TRY PROBABILITY"
#define DEBUGOPTIONS_TEXTFIELD_FORCE_VIDEO_REF "FORCE VIDEO REF"
#define DEBUGOPTIONS_TEXTFIELD_FORCE_VIDEO_REF_AWARDTRY "FORCE VIDEO REF AWARDTRY"
#define DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_GOES_TO_MAUL "NEXT TACKLE GOES TO MAUL"
#define DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_GOES_TO_SCRUM "NEXT TACKLE GOES TO SCRUM"
#define DEBUGOPTIONS_TEXTFIELD_BREAK_ON_NMA "BREAK ON NMA"
#define DEBUGOPTIONS_TEXTFIELD_FORCE_EXTRA_TIME "FORCE EXTRA TIME"
#define DEBUGOPTIONS_TEXTFIELD_USE_EXPERIMENTAL_USE_IT_RUCK_RULE "USE EXPERIMENTAL USE IT RUCK RULE"
#define DEBUGOPTIONS_TEXTFIELD_RESET_CONSEQUENCES_IN_PROGRESS "RESET CONSEQUENCES IN PROGRESS"

static TArray<DebugOption> RulesDebugOptionsList = {
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_IS_HIGH), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NEXT_HIGH_TACKLE_YELLOW_CARD), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NEXT_YELLOW_IS_SECOND_YELLOW_CARD), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NEXT_HIGH_TACKLE_RED_CARD), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_IS_OFFSIDE), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_CAUSES_INJURY), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NEXT_KICK_SOMEONE_IS_OFFISDE), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NEXT_PASS_IS_FORWARD), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NEXT_HANDLE_KNOCK_ON), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_FIFTH_TACKLE), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_ZERO_TIME_REMAINING), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NEXT_KICK_CATCH_IS_MARK), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_TRY_PROBABILITY), WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_FORCE_VIDEO_REF), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_FORCE_VIDEO_REF_AWARDTRY), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_GOES_TO_MAUL), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_GOES_TO_SCRUM), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_BREAK_ON_NMA), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_FORCE_EXTRA_TIME), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_USE_EXPERIMENTAL_USE_IT_RUCK_RULE), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_RESET_CONSEQUENCES_IN_PROGRESS), WidgetType::TICKBOX),
};

#define DEBUGOPTIONS_TEXTFIELD_RECORD_DEMO "RECORD DEMO"
#define DEBUGOPTIONS_TEXTFIELD_PLAYBACK_DEMO "PLAYBACK DEMO"
#define DEBUGOPTIONS_TEXTFIELD_SAVE_DEMO "SAVE DEMO"
#define DEBUGOPTIONS_TEXTFIELD_GLOBAL_DEMO_EXPORT "GLOBAL DEMO EXPORT"

static TArray<DebugOption> RecordingDebugOptionsList = {
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_RECORD_DEMO), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_PLAYBACK_DEMO), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_SAVE_DEMO), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_GLOBAL_DEMO_EXPORT), WidgetType::TICKBOX)
};

#define DEBUGOPTIONS_TEXTFIELD_WIN_ALL_MATCHES		"WIN ALL MATCHES"
#define DEBUGOPTIONS_TEXTFIELD_LOSE_ALL_MATCHES		"LOSE ALL MATCHES"
#define DEBUGOPTIONS_TEXTFIELD_DEBUG_DB_SAVE		"DEBUG DB SAVE"
#define DEBUGOPTIONS_TEXTFIELD_ACCEPT_ALL_OFFERS	"ACCEPT ALL OFFERS"
#define DEBUGOPTIONS_TEXTFIELD_DISPLAY_IDS			"DISPLAY IDS"
#define DEBUGOPTIONS_TEXTFIELD_NO_SIM_INJURIES		"NO SIM INJURIES"
#define DEBUGOPTIONS_TEXTFIELD_NO_SIM_CARDS			"NO SIM CARDS"
#define DEBUGOPTIONS_TEXTFIELD_SIMULATE_TO_YEAR		"SIMULATE TO YEAR"
#define DEBUGOPTIONS_TEXTFIELD_DATABASE_STATS		"DATABASE STATS"


static TArray<DebugOption> CareerDebugOptionsList = {
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_WIN_ALL_MATCHES), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_LOSE_ALL_MATCHES), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_DEBUG_DB_SAVE), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_ACCEPT_ALL_OFFERS), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_DISPLAY_IDS), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NO_SIM_INJURIES), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_NO_SIM_CARDS), WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_SIMULATE_TO_YEAR), WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_DATABASE_STATS), WidgetType::TICKBOX)
};


#define DEBUGOPTIONS_TEXTFIELD_SHOW_INFLUENCE						"SHOW_INFLUENCE"
#define DEBUGOPTIONS_TEXTFIELD_SHOW_CAN_REQ_PASS					"SHOW_CAN_REQ_PASS"
#define DEBUGOPTIONS_TEXTFIELD_SHOW_CAN_REQ_KICK_DOWN_FIELD			"SHOW_CAN_REQ_KICK_DOWN_FIELD"
#define DEBUGOPTIONS_TEXTFIELD_SHOW_CAN_REQ_KICK_FOR_TOUCH			"SHOW_CAN_REQ_KICK_FOR_TOUCH"
#define DEBUGOPTIONS_TEXTFIELD_OVERRIDE_PASS_REQ_DIST				"OVERRIDE_PASS_REQ_DIST"
#define DEBUGOPTIONS_TEXTFIELD_OVERRIDE_KICK_DOWN_FIELD_REQ_DIST	"OVERRIDE_KICK_DOWN_FIELD_REQ_DIST"
#define DEBUGOPTIONS_TEXTFIELD_OVERRIDE_KICK_FOR_TOUCH_REQ_DIST		"OVERRIDE_KICK_FOR_TOUCH_REQ_DIST"
#define DEBUGOPTIONS_TEXTFIELD_PASS_REQ_VALUE						"PASS_REQ_VALUE"
#define DEBUGOPTIONS_TEXTFIELD_KICK_DOWN_FIELD_REQ_VALUE			"KICK_DOWN_FIELD_REQ_VALUE"
#define DEBUGOPTIONS_TEXTFIELD_KICK_FOR_TOUCH_REQ_VALUE				"KICK_FOR_TOUCH_REQ_VALUE"
#define DEBUGOPTIONS_TEXTFIELD_DRAW_PRO_REQ_PASS_DISTANCE			"DRAW_PRO_REQ_PASS_DISTANCE"
#define DEBUGOPTIONS_TEXTFIELD_DRAW_NORMAL_PASS_DISTANCE			"DRAW_NORMAL_PASS_DISTANCE"
#define DEBUGOPTIONS_TEXTFIELD_OVERRIDE_PRO_PASS_DIST				"OVERRIDE_PRO_PASS_DIST"
#define DEBUGOPTIONS_TEXTFIELD_PRO_PASS_DIST_X						"PRO_PASS_DIST_X"
#define DEBUGOPTIONS_TEXTFIELD_PRO_PASS_DIST_Z						"PRO_PASS_DIST_Z"
#define DEBUGOPTIONS_TEXTFIELD_PRO_C_LIMIT							"PRO_C_LIMIT"
#define DEBUGOPTIONS_TEXTFIELD_PRO_GK_LIMIT							"PRO_GK_LIMIT"
#define DEBUGOPTIONS_TEXTFIELD_PRO_PK_LIMIT							"PRO_PK_LIMIT"
#define DEBUGOPTIONS_TEXTFIELD_UNIQUE_GOALS_PER_MATCH				"UNIQUE_GOALS_PER_MATCH"
#define DEBUGOPTIONS_TEXTFIELD_ALWAYS_INT_INTEREST					"ALWAYS_INT_INTEREST"
#define DEBUGOPTIONS_TEXTFIELD_ALWAYS_CLUB_RE_SIGN_INTEREST			"ALWAYS_CLUB_RE_SIGN_INTEREST"
#define DEBUGOPTIONS_TEXTFIELD_SET_PERFORMANCE_RATING_TO			"SET_PERFORMANCE_RATING_TO"
#define DEBUGOPTIONS_TEXTFIELD_ENABLE_SIMULATION_STATS				"ENABLE_SIMULATION_STATS"

static TArray<DebugOption> ProDebugOptionsList = {
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_SHOW_INFLUENCE),						WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_SHOW_CAN_REQ_PASS),					WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_SHOW_CAN_REQ_KICK_DOWN_FIELD),			WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_SHOW_CAN_REQ_KICK_FOR_TOUCH),			WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_OVERRIDE_PASS_REQ_DIST),				WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_OVERRIDE_KICK_DOWN_FIELD_REQ_DIST),	WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_OVERRIDE_KICK_FOR_TOUCH_REQ_DIST),		WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_PASS_REQ_VALUE),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_KICK_DOWN_FIELD_REQ_VALUE),			WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_KICK_FOR_TOUCH_REQ_VALUE),				WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_DRAW_PRO_REQ_PASS_DISTANCE),			WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_DRAW_NORMAL_PASS_DISTANCE),			WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_OVERRIDE_PRO_PASS_DIST),				WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_PRO_PASS_DIST_X),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_PRO_PASS_DIST_Z),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_PRO_C_LIMIT),							WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_PRO_GK_LIMIT),							WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_PRO_PK_LIMIT),							WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_UNIQUE_GOALS_PER_MATCH),				WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_ALWAYS_INT_INTEREST),					WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_ALWAYS_CLUB_RE_SIGN_INTEREST),			WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_SET_PERFORMANCE_RATING_TO),			WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_ENABLE_SIMULATION_STATS),				WidgetType::TICKBOX)

};

#define OVERRIDE_KICK_VALUES		 "override_kick_values_with_these_ones"
#define KICKS_IGNORE_WIND			 "kicks_ignore_wind"
#define KICK_STRENGTH_MODIFIER		 "kick_strength_modifier"
#define CHIP_KICK_MIN_DIST			 "chip_kick_min_dist"
#define CHIP_KICK_MAX_DIST			 "chip_kick_max_dist"
#define UP_AND_UNDER_KICK_MIN_DIST	 "up_and_under_kick_min_dist"
#define UP_AND_UNDER_KICK_MAX_DIST	 "up_and_under_kick_max_dist"
#define LONG_PUNT_KICK_MIN_DIST		 "long_punt_kick_min_dist"
#define LONG_PUNT_KICK_MAX_DIST		 "long_punt_kick_max_dist"
#define GRUBBER_KICK_MIN_DIST		 "grubber_kick_min_dist"
#define GRUBBER_KICK_MAX_DIST		 "grubber_kick_max_dist"
#define DROP_GOAL_KICK_MIN_DIST		 "drop_goal_kick_min_dist"
#define DROP_GOAL_KICK_MAX_DIST		 "drop_goal_kick_max_dist"
#define PLACE_KICK_MIN_DIST			 "place_kick_min_dist"
#define PLACE_KICK_MAX_DIST			 "place_kick_max_dist"
#define DROP_KICK_MIN_DIST			 "drop_kick_min_dist"
#define DROP_KICK_MAX_DIST			 "drop_kick_max_dist"
#define FREE_KICK_MIN_DIST			 "free_kick_min_dist"
#define FREE_KICK_MAX_DIST			 "free_kick_max_dist"
#define KICKOFF_KICK_MIN_DIST		 "kickoff_kick_min_dist"
#define KICKOFF_KICK_MAX_DIST		 "kickoff_kick_max_dist"
#define BOX_KICK_MIN_DIST			 "box_kick_min_dist"
#define BOX_KICK_MAX_DIST			 "box_kick_max_dist"
#define BASE_CHIP_KICK_ANGLE		 "chip_kick_base_angle"
#define BASE_UP_AND_UNDER_ANGLE		 "up_and_under_base_angle"
#define BASE_LONG_PUNT_ANGLE		 "long_punt_base_angle"
#define RAND_LONG_PUNT_ANGLE		 "long_punt_rand_angle"
#define BASE_GRUBBER_KICK_ANGLE		 "grubber_kick_base_angle"
#define BASE_DROP_GOAL_ANGLE		 "drop_goal_base_angle"
#define BASE_PLACE_KICK_ANGLE		 "place_kick_base_angle"
#define RAND_PLACE_KICK_ANGLE		 "place_kick_rand_angle"
#define BASE_DROP_KICK_ANGLE		 "drop_kick_base_angle"
#define RAND_DROP_KICK_ANGLE		 "drop_kick_rand_angle"
#define BASE_FREE_BALL_KICK_ANGLE	 "free_ball_kick_base_angle"
#define RAND_FREE_BALL_KICK_ANGLE	 "free_ball_kick_rand_angle"
#define BASE_KICKOFF_KICK_ANGLE		 "kickoff_kick_base_angle"
#define BASE_BOX_KICK_ANGLE			 "box_kick_base_angle"


static TArray<DebugOption> KickDebugOptionsList = {
	DebugOption(FText::FromString(OVERRIDE_KICK_VALUES),						WidgetType::TICKBOX),
	DebugOption(FText::FromString(KICKS_IGNORE_WIND),						WidgetType::TICKBOX),
	DebugOption(FText::FromString(KICK_STRENGTH_MODIFIER),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(CHIP_KICK_MIN_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(CHIP_KICK_MAX_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(UP_AND_UNDER_KICK_MIN_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(UP_AND_UNDER_KICK_MAX_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(LONG_PUNT_KICK_MIN_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(LONG_PUNT_KICK_MAX_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(GRUBBER_KICK_MIN_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(GRUBBER_KICK_MAX_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DROP_GOAL_KICK_MIN_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DROP_GOAL_KICK_MAX_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(PLACE_KICK_MIN_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(PLACE_KICK_MAX_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DROP_KICK_MIN_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DROP_KICK_MAX_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(FREE_KICK_MIN_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(FREE_KICK_MAX_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(KICKOFF_KICK_MIN_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(KICKOFF_KICK_MAX_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(BOX_KICK_MIN_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(BOX_KICK_MAX_DIST),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(BASE_CHIP_KICK_ANGLE),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(BASE_UP_AND_UNDER_ANGLE),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(BASE_LONG_PUNT_ANGLE),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(RAND_LONG_PUNT_ANGLE),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(BASE_GRUBBER_KICK_ANGLE),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(BASE_DROP_GOAL_ANGLE),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(BASE_PLACE_KICK_ANGLE),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(RAND_PLACE_KICK_ANGLE),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(BASE_DROP_KICK_ANGLE),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(RAND_DROP_KICK_ANGLE),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(BASE_FREE_BALL_KICK_ANGLE),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(RAND_FREE_BALL_KICK_ANGLE),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(BASE_KICKOFF_KICK_ANGLE),						WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(BASE_BOX_KICK_ANGLE),						WidgetType::EDITABLETEXT)
};


#define DEBUGOPTIONS_TEXTFIELD_DRAW_SHAME_AND_GLORY				"draw_shame_and_glory"
#define DEBUGOPTIONS_TEXTFIELD_COMMENTATOR_SPECTRUM_CUTOFF		"commentator_spectrum_cutoff"
#define DEBUGOPTIONS_TEXTFIELD_PHRASE_INTERRUPTION_DELAY		"phrase_interruption_delay"
#define DEBUGOPTIONS_TEXTFIELD_COMMENTARY_PACING_URGENCY_PERIOD	"commentary_pacing_urgency_period"
#define DEBUGOPTIONS_TEXTFIELD_COMMENTARY_ALWAYS_INTERRUPTS		"commentary_always_interrupts"

static TArray<DebugOption> EmotionDebugOptionsList = {
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_DRAW_SHAME_AND_GLORY),				WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_COMMENTATOR_SPECTRUM_CUTOFF),		WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_PHRASE_INTERRUPTION_DELAY),		WidgetType::EDITABLETEXT),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_COMMENTARY_PACING_URGENCY_PERIOD),	WidgetType::TICKBOX),
	DebugOption(FText::FromString(DEBUGOPTIONS_TEXTFIELD_COMMENTARY_ALWAYS_INTERRUPTS),		WidgetType::TICKBOX),
};

// == OPTIONS == //

//Array to notify the debug options that these widgets expect an integer return value.
static TArray<FString> IntegerOptions = {
	//Cutscene options
	FString("CUTSCENE ID"),
	FString("TRIGGERING TEAM INDEX"),
	FString("SUBCUTSCENE"),
	FString("CAMERA INDEX"),

	//Animation options
	FString("ANIMATION ID"),
	FString("ANIMATION FACE ID"),

	//Rules Options
	FString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_TRY_PROBABILITY),

	//Career Options
	FString(DEBUGOPTIONS_TEXTFIELD_SIMULATE_TO_YEAR),

	//Pro
	FString(DEBUGOPTIONS_TEXTFIELD_SET_PERFORMANCE_RATING_TO)
};

//Array to notify the debug options that these widgets expect a bool return value.
static TArray<FString> BoolOptions = {
	//Animation options
	FString("LOCK TIME OF DAY"),
	FString("HAS BALL"),
	FString("PLAY TACKLES"),

	//Rules Options
	FString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_IS_HIGH),
	FString(DEBUGOPTIONS_TEXTFIELD_NEXT_HIGH_TACKLE_YELLOW_CARD),
	FString(DEBUGOPTIONS_TEXTFIELD_NEXT_YELLOW_IS_SECOND_YELLOW_CARD),
	FString(DEBUGOPTIONS_TEXTFIELD_NEXT_HIGH_TACKLE_RED_CARD),
	FString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_IS_OFFSIDE),
	FString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_CAUSES_INJURY),
	FString(DEBUGOPTIONS_TEXTFIELD_NEXT_KICK_SOMEONE_IS_OFFISDE),
	FString(DEBUGOPTIONS_TEXTFIELD_NEXT_PASS_IS_FORWARD),
	FString(DEBUGOPTIONS_TEXTFIELD_NEXT_HANDLE_KNOCK_ON),
	FString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_FIFTH_TACKLE),
	FString(DEBUGOPTIONS_TEXTFIELD_ZERO_TIME_REMAINING),
	FString(DEBUGOPTIONS_TEXTFIELD_NEXT_KICK_CATCH_IS_MARK),
	FString(DEBUGOPTIONS_TEXTFIELD_FORCE_VIDEO_REF),
	FString(DEBUGOPTIONS_TEXTFIELD_FORCE_VIDEO_REF_AWARDTRY),
	FString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_GOES_TO_MAUL),
	FString(DEBUGOPTIONS_TEXTFIELD_NEXT_TACKLE_GOES_TO_SCRUM),
	FString(DEBUGOPTIONS_TEXTFIELD_BREAK_ON_NMA),
	FString(DEBUGOPTIONS_TEXTFIELD_FORCE_EXTRA_TIME),
	FString(DEBUGOPTIONS_TEXTFIELD_USE_EXPERIMENTAL_USE_IT_RUCK_RULE),
	FString(DEBUGOPTIONS_TEXTFIELD_RESET_CONSEQUENCES_IN_PROGRESS),

	//Recording options
	FString(DEBUGOPTIONS_TEXTFIELD_RECORD_DEMO),
	FString(DEBUGOPTIONS_TEXTFIELD_PLAYBACK_DEMO),
	FString(DEBUGOPTIONS_TEXTFIELD_SAVE_DEMO),
	FString(DEBUGOPTIONS_TEXTFIELD_GLOBAL_DEMO_EXPORT),

	//Career options
	FString(DEBUGOPTIONS_TEXTFIELD_WIN_ALL_MATCHES),
	FString(DEBUGOPTIONS_TEXTFIELD_LOSE_ALL_MATCHES),
	FString(DEBUGOPTIONS_TEXTFIELD_DEBUG_DB_SAVE),
	FString(DEBUGOPTIONS_TEXTFIELD_ACCEPT_ALL_OFFERS),
	FString(DEBUGOPTIONS_TEXTFIELD_DISPLAY_IDS),
	FString(DEBUGOPTIONS_TEXTFIELD_NO_SIM_INJURIES),
	FString(DEBUGOPTIONS_TEXTFIELD_NO_SIM_CARDS),
	FString(DEBUGOPTIONS_TEXTFIELD_DATABASE_STATS),

	//Pro
	FString(DEBUGOPTIONS_TEXTFIELD_SHOW_INFLUENCE),
	FString(DEBUGOPTIONS_TEXTFIELD_SHOW_CAN_REQ_PASS),
	FString(DEBUGOPTIONS_TEXTFIELD_SHOW_CAN_REQ_KICK_DOWN_FIELD),
	FString(DEBUGOPTIONS_TEXTFIELD_SHOW_CAN_REQ_KICK_FOR_TOUCH),
	FString(DEBUGOPTIONS_TEXTFIELD_OVERRIDE_PASS_REQ_DIST),
	FString(DEBUGOPTIONS_TEXTFIELD_OVERRIDE_KICK_DOWN_FIELD_REQ_DIST),
	FString(DEBUGOPTIONS_TEXTFIELD_OVERRIDE_KICK_FOR_TOUCH_REQ_DIST),
	FString(DEBUGOPTIONS_TEXTFIELD_DRAW_PRO_REQ_PASS_DISTANCE),
	FString(DEBUGOPTIONS_TEXTFIELD_DRAW_NORMAL_PASS_DISTANCE),
	FString(DEBUGOPTIONS_TEXTFIELD_OVERRIDE_PRO_PASS_DIST),
	FString(DEBUGOPTIONS_TEXTFIELD_UNIQUE_GOALS_PER_MATCH),
	FString(DEBUGOPTIONS_TEXTFIELD_ALWAYS_INT_INTEREST),
	FString(DEBUGOPTIONS_TEXTFIELD_ALWAYS_CLUB_RE_SIGN_INTEREST),
	FString(DEBUGOPTIONS_TEXTFIELD_ENABLE_SIMULATION_STATS),

	//Kick options
	FString(OVERRIDE_KICK_VALUES),
	FString(KICKS_IGNORE_WIND),

	//Emotion Options
	FString(DEBUGOPTIONS_TEXTFIELD_DRAW_SHAME_AND_GLORY),
	FString(DEBUGOPTIONS_TEXTFIELD_COMMENTARY_ALWAYS_INTERRUPTS)
};

//Array to notify the debug options that these widgets expect a float return value.
static TArray<FString> FloatOptions = {
	FString("X OFFSET"),
	FString("Y OFFSET"),
	FString("TIME OF DAY"),

	//Kick Options
	FString(KICK_STRENGTH_MODIFIER),
	FString(CHIP_KICK_MIN_DIST),
	FString(CHIP_KICK_MAX_DIST),
	FString(UP_AND_UNDER_KICK_MIN_DIST),
	FString(UP_AND_UNDER_KICK_MAX_DIST),
	FString(LONG_PUNT_KICK_MIN_DIST),
	FString(LONG_PUNT_KICK_MAX_DIST),
	FString(GRUBBER_KICK_MIN_DIST),
	FString(GRUBBER_KICK_MAX_DIST),
	FString(DROP_GOAL_KICK_MIN_DIST),
	FString(DROP_GOAL_KICK_MAX_DIST),
	FString(PLACE_KICK_MIN_DIST),
	FString(PLACE_KICK_MAX_DIST),
	FString(DROP_KICK_MIN_DIST),
	FString(DROP_KICK_MAX_DIST),
	FString(FREE_KICK_MIN_DIST),
	FString(FREE_KICK_MAX_DIST),
	FString(KICKOFF_KICK_MIN_DIST),
	FString(KICKOFF_KICK_MAX_DIST),
	FString(BOX_KICK_MIN_DIST),
	FString(BOX_KICK_MAX_DIST),
	FString(BASE_CHIP_KICK_ANGLE),
	FString(BASE_UP_AND_UNDER_ANGLE),
	FString(BASE_LONG_PUNT_ANGLE),
	FString(RAND_LONG_PUNT_ANGLE),
	FString(BASE_GRUBBER_KICK_ANGLE),
	FString(BASE_DROP_GOAL_ANGLE),
	FString(BASE_PLACE_KICK_ANGLE),
	FString(RAND_PLACE_KICK_ANGLE),
	FString(BASE_DROP_KICK_ANGLE),
	FString(RAND_DROP_KICK_ANGLE),
	FString(BASE_FREE_BALL_KICK_ANGLE),
	FString(RAND_FREE_BALL_KICK_ANGLE),
	FString(BASE_KICKOFF_KICK_ANGLE),
	FString(BASE_BOX_KICK_ANGLE),

	//Pro options
	FString(DEBUGOPTIONS_TEXTFIELD_PASS_REQ_VALUE),
	FString(DEBUGOPTIONS_TEXTFIELD_KICK_DOWN_FIELD_REQ_VALUE),
	FString(DEBUGOPTIONS_TEXTFIELD_KICK_FOR_TOUCH_REQ_VALUE),
	FString(DEBUGOPTIONS_TEXTFIELD_PRO_PASS_DIST_X),
	FString(DEBUGOPTIONS_TEXTFIELD_PRO_PASS_DIST_Z),
	FString(DEBUGOPTIONS_TEXTFIELD_PRO_C_LIMIT),
	FString(DEBUGOPTIONS_TEXTFIELD_PRO_GK_LIMIT),
	FString(DEBUGOPTIONS_TEXTFIELD_PRO_PK_LIMIT),

	//Emotion Options
	FString(DEBUGOPTIONS_TEXTFIELD_COMMENTARY_PACING_URGENCY_PERIOD),
	FString(DEBUGOPTIONS_TEXTFIELD_COMMENTATOR_SPECTRUM_CUTOFF),
	FString(DEBUGOPTIONS_TEXTFIELD_PHRASE_INTERRUPTION_DELAY)
};

//Array to notify the debug options that these widgets expect a string return value.
static TArray<FString> StringOptions = {};

//Playback speed array
static TArray<float> PlaybackSpeeds = {	1, 0.5, 0.25 };
