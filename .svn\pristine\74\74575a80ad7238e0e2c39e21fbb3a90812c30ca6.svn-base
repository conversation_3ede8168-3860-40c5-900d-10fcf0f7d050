/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef SIF_INGAME_LUA_FUNCTIONS_H
#define SIF_INGAME_LUA_FUNCTIONS_H

#include "RugbyEnums.h"
class MabString;
class MabColour;

/**
 * \class SIFInGameHelpers
 *	
 *	A place to put C++ functions that get bound to Lua functions for in game methods. Different
 *	from SIFGameHelpers in that it contains game specific handling, where as SIFGameHelpers
 *	contains general methods.
 *
 *	<AUTHOR> Pahuru, based on SIFGameHelpers.
*/

class SIFInGameHelpers
{
public:
	static void RegisterFunctions();

	static bool IsPauseMenuAvailable();
	static void ShowPauseDisabledIcon();
	static void RaisePauseMenu();

	static void DismissPauseMenu();

	/// Displays the injury substitution screen.
	/// The team index can either be 0 or 1 based on the side they are playing.
	/// The selected_player_id is the ID of which bench player should have the default selection.
	/// Set selected_player_id as -1 to just set the selection to the first bench player.
	static void RaiseInjurySubstitutionMenu( const int team_index, const int selected_player_id );
	static void DismissInjurySubstitutionMenu();

	static void EnableMenuPostEffects();
	static void DisableMenuPostEffects();

	static void OnDismissControllerMissingPopup();

	static bool IsGameOver();

	/// Sets a controller to a particular team
	static void SetPlayerTeam(EHumanPlayerSlot player_slot, int player_index,int ctrlr_id, int team_index);
	/// Sets a controller to a particular team
	static void SetOnlinePlayerTeam(EHumanPlayerSlot player_slot, int player_index, int team_index, int peer_id, int player_id );
	/// Gets the team the player controlling the controller index is playing for
	static int GetPlayerTeam(int player_index);
	/// Removes all players from any teams that have been set.
	static void ClearPlayerTeams();
	/// Sets a controller to a particular team - in game from pause menu
	static void SetRunningPlayerTeam(EHumanPlayerSlot player_slot, int player_index, int team_index);
	static void SetMatchWorldPlayerTeam(EHumanPlayerSlot player_slot, int player_index, int team_index);
	/*#rc3_legacy_input : RussellD : Needs reimplementation*/
	static int GetRunningPlayerTeam( int controller_index );
    static int GetRunningPlayerTeamId( int controller_index );
	

	static bool IsHumanPlayersOnBothTeams();
	static int GetNumHumanPlayersOnTeam( int team_index );
	/// Locks all input for all human players, not just current active players.
	static void LockAllHumanPlayerInputs( bool input_locked );

	static void PlayReplay();

	static void RewindReplay();

	static void FastForwardReplay();

	static void StopReplay();

	static void UpdateTeamLineup( int team_index );

	static void TeamLineupComplete();

	static MabString GetPlayerName( int team_index, int player_position );
	static MabString GetPlayerDisplayName( int team_index, int player_position );
	static MabString GetPlayerNumber( int team_index, int player_position );
	static MabString GetTeamName( int team_index );
	static MabString GetTeamShortName( int team_index );
	static MabColour GetTeamColour( int team_index );
	static int GetTeamId( int team_index );
	static int GetTeamScore( int team_index );
	static void UpdatePlayerDetail( int player_db_id );
	static void SwapPlayerPosition( int team_index, int player_a_db_id, int player_b_db_id );
	static void SwapPlayerFieldBench( int team_index, int field_player_db_id, int bench_player_db_id );

	static MabString GetInjuredPlayerPosition();
	static MabString GetInjuredPlayerName();

	static void SetAsCaptain( int team_index, int player_db_id );
	static void SetAsKicker( int team_index, int player_db_id );
	static void SetAsPlayKicker( int team_index, int player_db_id );

	static int GetCaptainId( int team_index );
	static int GetKickerId( int team_index );
	static int GetPlayKickerId( int team_index );

	static bool IsCaptain( int team_index, int player_db_id );
	static bool IsKicker( int team_index, int player_db_id );
	static bool IsPlayKicker( int team_index, int player_db_id );

	static bool IsPlayerOnField( int team_index, int player_db_id );
	static int GetFieldPlayerWithBestGoalKicking( int team_index );
	static int GetFieldPlayerWithBestPlayKicking( int team_index );
	static int GetFieldPlayerWithBestMentalAgility( int team_index );
	static void EnsureCaptainAndKickerOnField( int team_index );

	/// Sets if the 2D HUD player marker for a specific player should render when they run outside the view of the camera.
	static void SetDisplayOffscreenPlayerMarker( EHumanPlayerSlot player, bool should_display );

	/// Substitutions will not happen straight away, they need to wait until a valid opportunity to swap them
	/// This swap will be added to a list of swaps that will occur at the next valid time.
	static EValidInterchangeReason RequestPlayerSubstitution( const int team_index, int player_a_db_id, int player_b_db_id , int player_a_ui_index, int player_b_ui_index);
	/// Retrieves the number of substitutions that are yet to be applied to the match.
	static int GetNumberOfPendingSubstitutions();
    /// Retrieves the number of remaining substitutions for current match
    static int GetNumberOfRemainingSubstitutions(const int team_index);
	/// Retrieves the team index (SIDE_A or SIDE_B) that a specific substitution applies for.
	static int GetSubstitutionTeamIndex(const int substitution_index );
	/// Retrieves the player's databse ID for a specific substitution.
	/// The player_index determines which player to retrieve the ID for. A substitution involves two players
	/// so this value must be either 0 or 1.
	static int GetSubstitutionPlayerDatabaseIndex(const int substitution_index, const int player_index);

	/// Fire off all pending substitutions at half-time.
	static void StartAllPendingSubstitutions();
	/// Have all pending substitutions completed?
	static bool HavePendingSubstitutionsCompleted();
	/// Some players should not be substituted with if they have already had a substitution.
	static bool IsPlayerAValidSubstitution( const int player_db_id , int team_idx );
	/// Some field players should not be substituted, for instance if they've got a red/yellow card.
	static bool IsFieldPlayerAValidSubstitution( const int player_db_id, int team_idx );
	/// The substitutes the injured player with the selected player from the player_db_id.
	static void ApplyInjurySubstitution( const int player_db_id );
	/// Checks to see if the requested player is currently being interchanged.
	static bool IsPlayerOnFieldAndLoaded( int player_db_id, int team_idx );


	static void StartNextHalf();

	static void IGSetTeamInfo();

	/// Starts the pre-match commentary sequence
	static void StartPreMatchCommentary();

	/// Notifies a player disconnection
	static void NotifyPlayerDisconnection (const char* message);

#ifdef ENABLE_SEVENS_MODE
	/// Extra Time Coin Toss Functions
	static bool CoinTossMakeDecision1();
	static bool CoinTossMakeDecision2();
	static void CoinTossStartNextPhase();

	static int GetCoinTossDecisionMakerSide();
	static bool	GetCoinTossDecisionMakerIsCPU();

	static MabString GetCoinTossDecisionDescriptionString(int forPhase);
	static MabString GetCoinTossResultDescriptionString(int forPhase);
	static MabString GetCoinTossDecisionMakerName();

	static int GetCoinTossState();

	static const char* GetKickOrReceiveDecisionMakerTeamName();
	static const char* GetNorthOrSouthDecisionMakerTeamName();
	static const char* GetKickingDirectionString();
	static const char* GetNonKickingDirectionString();
	static const char* GetKickOrReceiveDecision();
	static const char* GetNorthOrSouthDecision();
#endif

private:
	/// Hide the constructor/destructor. This class shouldn't be instantiated
	SIFInGameHelpers(){};
	virtual ~SIFInGameHelpers(){};
};

#endif //SIF_INGAME_LUA_FUNCTIONS_H