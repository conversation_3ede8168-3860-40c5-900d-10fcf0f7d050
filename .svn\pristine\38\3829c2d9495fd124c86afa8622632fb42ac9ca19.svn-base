#ifndef RUROLEPLAYTHEBALLDEFENDER_H
#define RUROLEPLAYTHEBALLDEFENDER_H

#include "Mab/Time/MabTimer.h"
#include "Match/SSRole.h"

class NMMabAnimationNetwork;

/**
	Play the ball defender role, will be assigned to the defending player involved in the tackle
	Player will stand and defend in front of play the ball player until play is resumed
*/

class RURolePlayTheBallDefender : public SSRole
{
	MABRUNTIMETYPE_HEADER(RURolePlayTheBallDefender);

public:

	enum class RoleState
	{
		GETUP,					// Gets up from the ground and tries to face the correct direction
		MOVING,					// Moves to position
		//REJOIN,					// Rejoin the defensive line GGs JZ do we need to rejoin? shouldn't the player be ready for tackles on the marker
		COMPLETE,				// The role has been completed
	};

	RURolePlayTheBallDefender( SIFGameWorld* game );

	/// Enter this role with the specified player.
	void Enter(ARugbyCharacter* player) override;

	/// Exit this role.
	void Exit(bool forced) override;

	/// Advance this role.
	void UpdateLogic(const MabTimeStep& game_time_step) override;

	/// Get the fitness of the player for the given behaviour
	static int GetFitness(const ARugbyCharacter* player, const SSRoleArea* area);

	/// returns true if we're interruptible, false if we're not
	bool IsInterruptable() const override;

	const char* GetShortClassName() const override { return "PTBDef"; }

protected:
	RoleState		state;
	
	// Updates the position where the player should stand and defend
	void UpdateDefenderPosition();
	void SetDefenderPosition();

	FVector m_lastFramePosition;
	FVector m_last_defender_pos;
private:

	//void UpdateRejoinPosition();
	virtual FVector GetDefenderPosition();

	virtual void WarpToWaypoint();

	bool isHuman = false;
	MabTime currentWaitDuration = 0.0f;
	MabTime waitBeforePlayingDuration = 1.0f;

	float warp_timer = 0;

	bool warped = false;

};

#endif //RUROLEPLAYTHEBALLDEFENDER_H
