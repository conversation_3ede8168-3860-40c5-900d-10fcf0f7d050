/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/TutorialMode/Tutorials/RUTutorialKickingConversion.h"

#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/Roles/Competitors/RURoleShootForGoal.h"
#include "Match/Ball/SSBall.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Character/RugbyPlayerController.h"
#include "Match/HUD/RU3DHUDManager.h"
#include "Match/HUD/RUHUDUpdaterTraining.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUInputKickInterface.h"
#include "Match/RugbyUnion/RUSandboxGame.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURuleTriggerEnum.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/SIFGameWorld.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Match/RugbyUnion/TutorialMode/RUTutorialManager.h"

//#rc3_legacy_include #include "RUContextualHelper.h"

#include "RugbyGameInstance.h"

namespace RUTUTORIALKICKINGCONVERSION
{
	const float METER_FAIL = 0.95f;
	const float ACCURACY_FAIL = 55.0f;
}

RUTutorialKickingConversion::RUTutorialKickingConversion( SIFGameWorld* game )
: RUTutorial( game, RUTutorialType::KICKING_CONVERSION, RUTutorialGroup::LESSON1, "ruged/tutorials/kick_conversion.json" ),
  kick_started( false ),
  internal_success( false ),
  conversion_strength(0.0f),
  conversion_accuracy(0.0f)
  //start_timer()
{
	//
}

// Perform logic. Check state. Do anything appropriate.
void RUTutorialKickingConversion::Update( float /*delta_time*/ )
{

}

// Start the tutorial
void RUTutorialKickingConversion::Initialise()
{
	//game->GetGameSettings().game_settings.SetGameMode(GAME_MODE_SEVENS);

	// turn off game rules (stop player reposition after out of bounds)
	game->GetRules()->EnableConsequences( false );
	game->GetRules()->EnableTriggers( RURT_NONE );

	// Hook up to events
	RUGameEvents* game_events = game->GetEvents();
	game_events->ball_dead_detected.Add( this, &RUTutorialKickingConversion::BallDead );
	game_events->ball_out_detected.Add( this, &RUTutorialKickingConversion::BallInTouch );
	game_events->ball_through_posts.Add( this, &RUTutorialKickingConversion::BallThroughPosts );
	game_events->ball_bounce.Add( this, &RUTutorialKickingConversion::OnBounce );
	game_events->kick.Add( this, &RUTutorialKickingConversion::OnKick );

	MabVector<ERugbyGameAction> actions;
	actions.reserve(2);
	actions.push_back(ERugbyGameAction::ANALOG_Y);
	actions.push_back(ERugbyGameAction::STARTKICK_RESTART);

	MabVector<ERugbyGameAction> actions2;
	actions2.reserve(2);
	actions2.push_back(ERugbyGameAction::ANALOG_X);
	actions2.push_back(ERugbyGameAction::STARTKICK_RESTART);

	// Step 1 (set up ball) only exists for R15 conversions
	// Nick WWS 7s to Womens //
	//if (game->GetGameSettings().game_settings.GameModeIsR13())
	//{
		game->GetTutorialManager()->SetStage(RUStageNum::ONE, "[ID_TUTORIAL_KICKING_CONVERSION_STAGEONE]", "1", actions);
		game->GetTutorialManager()->SetStage(RUStageNum::TWO, "[ID_TUTORIAL_KICKING_CONVERSION_STAGETWO]", "1", actions2);
		game->GetTutorialManager()->SetStage(RUStageNum::THREE, "[ID_TUTORIAL_KICKING_CONVERSION_STAGETHREE]", "1", ERugbyGameAction::STARTKICK_RESTART);
		game->GetTutorialManager()->SetStage(RUStageNum::FOUR, "[ID_TUTORIAL_KICKING_CONVERSION_STAGETHREE]", "1", ERugbyGameAction::STARTKICK_RESTART);
	//}
	//else
	//{
	//	MABBREAKMSG("No R7 tutorial for this");
	//	game->GetTutorialManager()->SetStage(RUStageNum::ONE, "[ID_TUTORIAL_KICKING_CONVERSION_STAGETWO]", "1", actions);
	//	game->GetTutorialManager()->SetStage(RUStageNum::TWO, "[ID_TUTORIAL_KICKING_CONVERSION_STAGETHREE]", "1", actions2);
	//	game->GetTutorialManager()->SetStage(RUStageNum::THREE, "[ID_TUTORIAL_KICKING_CONVERSION_STAGETHREE]", "1", ERugbyGameAction::STARTKICK_RESTART);
	//}

}

// Start the tutorial
void RUTutorialKickingConversion::Start()
{
	MabString name(32, "ID_TUTORIAL_KICKING_CONVERSION");
	game->GetTutorialManager()->DisplayObjective( name );

	// If no ball holder, give to the player
	if (!m_player || (m_player!=NULL && !m_player->GetAttributes()->IsActive()) )
	{
		m_player = GetTutorialPlayer()->GetRugbyCharacter();
		GetTutorialPlayer()->ResetActions();
	}

	RUPlayerAttributes* player_attribs = m_player->GetAttributes();
	// max out the general kick accuracy
	player_attribs->SetGeneralKickAccuracy( 1.0f );

	// Give the ball to the m_player
	game->GetGameState()->SetBallHolder( m_player, true );

	// turn off formations
// 	SSEVDSFormationManager *formation_manager = game->GetTeam(0)->GetFormationManager();
// 	formation_manager->Reset();

	RUGameState* state = game->GetGameState();
	state->SetAttackingTeam( m_player->GetAttributes()->GetTeam() );

	//m_player->GetMovement()->SetCurrentPosition(FVector( -20.0f + MabMath::Rand( 20.0f), 0, 40.0f ));

	FVector player_pos = m_player->GetMovement()->GetCurrentPosition();
	state->SetTryScorePosition( FVector( 0, 0, 55.0f ) );
	state->SetPlayRestartPosition( player_pos );

	m_player->GetMovement()->SetCurrentPosition( player_pos + FVector(0.0f,0.0f,0.0f));

	// Initialise win conditions
	SetSuccessState( false, true );
	SetFailureState( false, true );
	kick_started = false;
	internal_success = false;

	game->GetGameState()->SetPhase( RUGamePhase::CONVERSION );

	float wind_strength = 5.0f;
	FVector wind_dir = FVector( 0.0f, 0, 1.0f);

	game->SetupWind(wind_dir, wind_strength);
}

// CleanUp the tutorial
void RUTutorialKickingConversion::CleanUp()
{
#ifdef ENABLE_ANALYTICS
	if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
	{
		pRugbyGameInstance->GetPlayerAnalyticsData().MarkTutorialViewed(wwAnalyticsTutorialOption::SimpleConversion);
	}
#endif

	// Turn rules back on
	game->GetRules()->EnableConsequences( true );

	// Cleanup event listeners and anything else relevant.
	RUGameEvents* game_events = game->GetEvents();
	game_events->ball_dead_detected.Remove( this, &RUTutorialKickingConversion::BallDead );
	game_events->ball_out_detected.Remove( this, &RUTutorialKickingConversion::BallInTouch );
	game_events->ball_through_posts.Remove( this, &RUTutorialKickingConversion::BallThroughPosts );
	game_events->ball_bounce.Remove( this, &RUTutorialKickingConversion::OnBounce );
	game_events->kick.Remove( this, &RUTutorialKickingConversion::OnKick );

	m_player = NULL;
}

// Has user kicked the ball out (not on the full)?
bool RUTutorialKickingConversion::HasSucceeded()
{
	return GetSuccessState() && !GetFailureState();
}

// Has user gone in the wrong direction or not moved for Y seconds?
bool RUTutorialKickingConversion::HasFailed()
{
	return GetFailureState();
}

void RUTutorialKickingConversion::BallDead(ARugbyCharacter* ball_holder, const FVector& position, bool on_full)
{
	MABUNUSED(ball_holder);
	MABUNUSED(position);
	MABUNUSED(on_full);

	if ( !internal_success )
	{
		SetFailReason();
		SetFailureState( true );
	}
}

void RUTutorialKickingConversion::BallInTouch(ARugbyCharacter* ball_holder, const FVector &position, bool on_full)
{
	MABUNUSED(ball_holder);
	MABUNUSED(position);
	MABUNUSED(on_full);

	if ( !internal_success )
	{
		SetFailReason();
		SetFailureState( true );
	}
}

void RUTutorialKickingConversion::BallThroughPosts( const FVector& position )
{
	if (!mDemoActive && game->GetBall()->IsOnTheFull() )
	{
		CalculateMedal( position );
		internal_success = true;
	}
}

void RUTutorialKickingConversion::OnBounce( const FVector& position, const FVector& velocity )
{
	MABUNUSED( position );
	MABUNUSED( velocity );

	if ( internal_success )
	{
		SetSuccessState( true );
	}
	else
	{
		SetFailReason();
		SetFailureState( true );
	}
}

bool RUTutorialKickingConversion::IsStageComplete( RUStageNum stage_num )
{
	switch ( stage_num )
	{
		// Place
	case RUStageNum::ONE:
		{
			RURoleShootForGoal* role = MabCast<RURoleShootForGoal>(m_player->GetRole());
			if ( role && role->GetKickState() > RURoleShootForGoal::KFP_BALL_PLACE ) // KICK_BALL_PLACE = 1
			{
				return true;
			}
		}
		break;
		// Aim
	case RUStageNum::TWO:
		{
			RURoleShootForGoal* role = MabCast<RURoleShootForGoal>(m_player->GetRole());
			if ( role && role->GetKickState() > RURoleShootForGoal::KFP_AIM ) // KICK_RUNUP = 5
			{
				return true;
			}
		}
		break;
		//Accuracy
	case RUStageNum::THREE:
		{
			RURoleShootForGoal* role = MabCast<RURoleShootForGoal>(m_player->GetRole());
			if ( role && role->GetKickState() > RURoleShootForGoal::KFP_AIM_ACCURACY ) // KICK_RUNUP = 5
			{
				return true;
			}
		}
		break;
		//Power
	case RUStageNum::FOUR:
		{
			RURoleShootForGoal* role = MabCast<RURoleShootForGoal>(m_player->GetRole());
			if ( role )
			{
				//if(role->GetRunUpAnimationTriggered() ) // KICK_COMPLETE = 7
				RUInputKickInterface* kick_interface = game->GetInputManager()->GetKickInterface();

				if( kick_interface->GetConversionPowerSet() )
				{
					game->GetHUDUpdaterTraining()->FadeOutRolodex();
					return true;
				}
			}
		}
	default: break;
	}

	return false;
}

void RUTutorialKickingConversion::CalculateMedal( const FVector& ball_pos )
{
	MABUNUSED( ball_pos );

	if ( mMedalAchievement < RUTutorialMedal::GOLD )
	{
		game->GetTutorialManager()->DisplayAchievement( RUTutorialMedal::GOLD );
		mMedalAchievement = RUTutorialMedal::GOLD;
	}
	mSuccessString = "[ID_TUTORIAL_KICKING_CONVERSION_SUCCESS_01]";
}

void RUTutorialKickingConversion::OnKick(ARugbyCharacter* player, KickContext context, KickType type, const FVector &position)
{
	MABUNUSED(player);
	MABUNUSED(context);
	MABUNUSED(type);

	conversion_strength = game->GetTutorialManager()->GetReportedValue(0);
	conversion_accuracy = position.z;
}

void RUTutorialKickingConversion::SetFailReason()
{
	if ( conversion_strength < RUTUTORIALKICKINGCONVERSION::METER_FAIL )
		mFailString = "[ID_TUTORIAL_KICKING_CONVERSION_FAIL_03]";
	else if ( conversion_accuracy < RUTUTORIALKICKINGCONVERSION::ACCURACY_FAIL )
		mFailString = "[ID_TUTORIAL_KICKING_CONVERSION_FAIL_01]";
	else
		mFailString = "[ID_TUTORIAL_KICKING_CONVERSION_FAIL_02]";
}
