#include "RURoleSetplayScrumHalf.h"
/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#include "Character/RugbyCharacter.h"
#include "Match/AI/SetPlays/SSSetPlayManager.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/Ball/SSBall.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuckScrumHalf.h"

MABRUNTIMETYPE_IMP1(RURoleSetplayScrumHalf, RURoleSetplay);

RURoleSetplayScrumHalf::RURoleSetplayScrumHalf(SIFGameWorld * game) : RURoleSetplay(game)
{

}

void RURoleSetplayScrumHalf::Enter(ARugbyCharacter* player)
{
	//As this class could be entered before the scrum half picks up the ball, play the animation for picking up the ball
	// you should be crouched
	RURoleSetplay::Enter(player);

	Crouch(true);

	this->m_pPlayer = player;	
	
	bPickingUpBall = false;

	if (player)
	{
		player->GetMabAnimationEvent().Add(this, &RURoleSetplayScrumHalf::AnimationEvent);
	}
}

void RURoleSetplayScrumHalf::Exit(bool forced)
{
	Crouch(false);
	m_pPlayer->GetMabAnimationEvent().Remove(this, &RURoleSetplayScrumHalf::AnimationEvent);
	RURoleSetplay::Exit(forced);
}

void RURoleSetplayScrumHalf::UpdateLogic(const MabTimeStep & game_time_step)
{
	RURoleSetplay::UpdateLogic(game_time_step);

	//Only perform ball pickup logic if the setplay hasn't started yet
	if (SetplayManager && !SetplayManager->HasSetplayStarted())
	{
		//Check if all players are in position
		if (SetplayManager->ArePlayersInPosition())
		{
			//Start the setplay 
			SetplayManager->SetSetplayStarted(true);
		}
	}

	if (SetplayManager->HasSetplayStarted())
	{
		//If the ball delay has passed, pick up the ball.
		if (!bPickingUpBall && !bBallPickedUp)
		{
			//if (animation->IsAnimationAvailable(ANIM_PICKUP))
			RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
			ERugbyAnim_Mode_FullBodyActions CurrentFullBodySMState = animation->GetStateMachine().GetSMFullBodyActions()->GetFullBodyActionState();
			if (CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::DummyHalf || CurrentFullBodySMState == ERugbyAnim_Mode_FullBodyActions::Null)	//dummyhalf or number eight							
			{
				animation->PlayAnimation(ANIM_PICKUP);
				bPickingUpBall = true;
			}
		}
	}

	//If the player is mid picking up the ball
	if (bPickingUpBall && !bBallPickedUp)
	{
		//If the player doesnt have the ball yet, stay crouched
		if (m_pGame->GetGameState()->GetBallHolder() != m_pPlayer)
		{
			Crouch(true);
		}
		else
		{
			//Otherwise stand up
			bPickingUpBall = false;
			bBallPickedUp = true;
			Crouch(false, true);
		}
	}
}

void RURoleSetplayScrumHalf::NotifyCollected()
{
	//MABLOGMSG( LOGCHANNEL_GAME, LOGTYPE_INFO, "RuckScrumHalf(%d) Update (%f): %s", player->GetAttributes()->GetIndex(), game->GetSimTime()->GetAbsoluteTime().ToSeconds(), __FUNCTION__ );
	//MABASSERT( !notified_collect );	// TODO: Re-enable and fix doubel notification from animation
	if (m_pPlayer->GetAttributes()->GetTeam() == m_pGame->GetGameState()->GetAttackingTeam())
	{
		m_pPlayer->GetMovement()->SetRepulsionEnabled(true);
		m_pPlayer->GetMovement()->SetAvoidanceEnabled(true);

		const static float ATTACH_TRANS_TIME = 0.13f;
		const static float ATTACH_ROT_TIME = 0.4f;
		m_pGame->GetBall()->SetBallAttachTime(ATTACH_TRANS_TIME, ATTACH_ROT_TIME);

		m_pGame->GetGameState()->PickedUp(m_pPlayer, PUC_FROM_RUCK);
	}
}

void RURoleSetplayScrumHalf::AnimationEvent(float /*time*/, ERugbyAnimEvent event, size_t userdata, bool /*bIsBlendingOut = false*/)
{
	// only to silence debug messages
	if (event == ERugbyAnimEvent::FOOTSTEPS_EVENT || event == ERugbyAnimEvent::BALL_RELEASED_EVENT)
		return;

	if (event == ERugbyAnimEvent::BALL_CONTACT_EVENT)
	{
		if (SetplayManager->HasSetplayStarted())
		{
			NotifyCollected();
			return;
		}
	}
}

//This class should always replace the scrum half
int RURoleSetplayScrumHalf::GetFitness(const ARugbyCharacter * player, const SSRoleArea * area)
{
	if (player->GetGameWorld()->GetGameState()->GetPhase() == PLAY_THE_BALL)
		return 0;

	if (FString(player->GetRole()->GetShortClassName()).Compare(FString("RuckHalfback")) == 0 ||
		FString(player->GetRole()->GetShortClassName()).Compare(FString("ScrumHB")) == 0)
	{
		return 10000;
	}
	return 0;
}

void RURoleSetplayScrumHalf::Crouch(bool enable, bool /*clear*/)
{
	RUPlayerAnimation* animation = m_pPlayer->GetAnimation();
	if (animation != NULL)
	{
		animation->SetVariable(animation->GetDummyHalfVariable(), enable ? 1.0f : 0.0f);
	}
}

bool RURoleSetplayScrumHalf::IsInterruptable() const
{
	if (SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetBallHolder() == m_pPlayer)
		return false;

	return RURoleSetplay::IsInterruptable();
}
