/*--------------------------------------------------------------
|        Copyright (C) 1997-2011 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef RU_RULES_DEBUG_SETTINGS_H
#define RU_RULES_DEBUG_SETTINGS_H

#include "Mab/Objects/MabObject.h"
//#include <MabArray.h>

//#ifdef ENABLE_GAME_DEBUG_MENU

class ARugbyCharacter;

class SIFGameWorld;
class SIFDebugMenu;

class RURulesDebugSettings : public MabObject
{
	MABRUNTIMETYPE_HEADER( RURulesDebugSettings )

	public:
		RURulesDebugSettings();
		virtual ~RURulesDebugSettings();

		static void DefineMabCentralInterfaces();
		static void RegisterLuaMethods();
		static void RegisterMenuConstraints( SIFDebugMenu* debug_menu );

		int		GetNextTackleTryProbability() const { return next_tackle_try_probability; }

		bool GetShowDebugInfo(){ return show_debug_info; }
		bool IsNextTackleHigh() { return next_tackle_is_high; }
		bool GetNextTackleIsHigh() { return DisableDebugOption( next_tackle_is_high ); }
		bool IsNextHighTackleYellowCard() { return next_high_tackle_yellow_card; }
		bool GetNextHighTackleYellowCard() { return DisableDebugOption( next_high_tackle_yellow_card ); }
		bool IsYellowCardIsSecondYellowCard() { return next_yellow_card_is_second_yellow_card; }
		bool IsNextHighTackleRedCard() { return next_high_tackle_red_card; }
		bool GetNextHighTackleRedCard() { return DisableDebugOption( next_high_tackle_red_card ); }
		bool IsNextTackleOffside() { return next_tackle_is_offside; }
		bool GetNextTackleIsOffside() { return DisableDebugOption( next_tackle_is_offside ); }
		bool GetNextTackleCausesInjury() { return DisableDebugOption( next_tackle_causes_injury ); }
		bool IsNextKickSomeoneOffside() { return next_game_kick_someone_is_offside; }
		bool GetNextKickSomeoneIsOffside() { return DisableDebugOption( next_game_kick_someone_is_offside ); }
		bool GetNextPassIsForward() { return DisableDebugOption( next_pass_is_forward ); }
		bool GetNextCatchOrCollectIsKnockOn() { return DisableDebugOption( next_catch_or_collect_is_knock_on ); }
		bool GetNextTackleFifthTackle() { return DisableDebugOption( next_tackle_fifth_tackle ); }
		bool GetZeroRemainingTime() { return DisableDebugOption( zero_remaining_time ); }
		bool GetNextKickCatchIsMark() { return next_kick_catch_is_mark; }
		bool IsForceExtraTime() { return force_extra_time; }
		void ForceExtraTime() { force_extra_time = true; }
		bool GetForceVideoRef(){ return force_video_ref; }
		bool GetForceVideoRefResult(){ return force_video_ref_result; }
		bool GetNextTackleGoesToMaul() { return next_tackle_goes_to_maul; }
		bool GetNextTackleGoesToScrum() { return DisableDebugOption( next_tackle_goes_to_scrum ); }
		void ForceRedCard (bool value) { next_tackle_is_high = next_high_tackle_red_card = value; }
		bool DebugNMAEnabled() { return debug_NMA; }
		bool ExperimentalUseItRuckRuleEnabled() { return experimental_use_it_ruck; }

		int		GetLastMetersGainedPlayer(){ return last_player_meters_gained; }
		void	SetLastMetersGainedPlayer(int value){ last_player_meters_gained = value; }

		bool GetResetConsequenceInProgress(){ return reset_consequence_in_progress; }
		void SetResetConsequenceInProgress(bool value){ reset_consequence_in_progress = value; }

private:

		bool DisableDebugOption( bool& option )
		{
			if( option )
			{
				option = false;
				return true;
			}
			return false;
		}

		bool show_debug_info;
		bool next_tackle_is_high;
		bool next_high_tackle_yellow_card;
		bool next_yellow_card_is_second_yellow_card;
		bool next_high_tackle_red_card;
		bool next_tackle_is_offside;
		bool next_tackle_causes_injury;
		bool next_game_kick_someone_is_offside;
		bool next_pass_is_forward;
		bool next_catch_or_collect_is_knock_on;
		bool next_tackle_fifth_tackle;
		bool zero_remaining_time;
		bool next_kick_catch_is_mark;
		bool force_extra_time;
		bool force_video_ref;
		bool force_video_ref_result;
		bool next_tackle_goes_to_maul;
		bool next_tackle_goes_to_scrum;
		bool debug_NMA;
		int	next_tackle_try_probability;
		bool experimental_use_it_ruck;

		int	last_player_meters_gained;
		bool reset_consequence_in_progress;
};

#endif

//#endif
