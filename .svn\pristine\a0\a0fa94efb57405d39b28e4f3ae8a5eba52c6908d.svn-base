// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIScreenOnlineSelectTeams.h"
#include "UI/GeneratedHeaders/WWUIScreenOnlineSelectTeams_UI_Namespace.h"
#include "WWUIScrollBox.h"
#include "WWUIScreenControllerAssignment.h"
#include "UI/Populators/WWUIPopulatorTeamSelectList.h"
#include "Character/RugbyPlayerState.h"
#include "Utility/Helpers/SIFGameHelpers.h"
#include "Utility/Helpers/SIFMatchmakingHelpers.h"
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "WWUITextBlock.h"
#include "UserWidget.h"
#include "Image.h"
#include "Border.h"
#include "UI/Populators/WWUIPopulatorOnlinePlayers.h"
#include "Match/RugbyUnion/RUDBTeam.h"
#include "Engine/Texture2D.h"
#include "Networking/VoipManager.h"
#include "GameModes/RugbyGameState.h"
#include "WWUIRichTextBlockWithTranslate.h"
#include "Match/PlayerProfile/SIFPlayerProfilePropertyDefs.h"
#include "Match/PlayerProfile/SIFPlayerProfileManager.h"
#include "Mab/Types/MabNamedValueList.h"
#include "Match/PlayerProfile/SIFPlayerProfile.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DBTeam.h"
#include "Match/PlayerProfile/SIFPlayerProfileConstants.h"
#include "Match/RugbyUnion/RUSandboxGame.h"
#include "Match/SIFGameWorld.h"
#include "RugbyGameInstance.h"
#include "Match/SSTeam.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/Rules/RURules.h"

#define TRIGGER_DELAY_TIME			(1.0f)
#define SWITCH_STRIP_DELAY_TIME		(0.2f)

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::Startup(UWWUIStateScreenData* InData /*= nullptr*/)
{
	Super::Startup();

	//< Get Game Instance. >
	GameInstance = Cast<URugbyGameInstance>(SIFApplication::GetApplication());
	if (!GameInstance) return;

	scene_root = NULL;
	filtered_teams_list = NULL;
	team_list_heading = NULL;
	help_text = NULL;
	rating = NULL;
	loading_spokes = NULL;
	chat_indicator = NULL;

	initial_entry = true;
	all_lists_refreshed = false;
	show_title_animation = false;
	in_scene = false;
	m_switchInitialFocus = false;
	m_initalEntry = false;
	m_setScrollBoxIndexOnTick = false;

	m_ScrollBoxIndex = -1;

	if (UWWUIStateScreenOnlineSelectTeamsData* pOnlineSelectTeamsData = Cast<UWWUIStateScreenOnlineSelectTeamsData>(InData))
	{
		PlayerController = pOnlineSelectTeamsData->playerController;
		m_initalEntry = pOnlineSelectTeamsData->initialEntry;
	}

	UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenOnlineSelectTeams_UI::TeamListScrollbox));

	if (pTeamListScrollbox)
	{
		UWWUIPopulatorTeamSelectList* pTeamSelectListPopulator = Cast<UWWUIPopulatorTeamSelectList>(pTeamListScrollbox->GetPopulator());

		if (pTeamSelectListPopulator)
		{
			if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
			{
				ARugbyPlayerState* rugbyPlayerState = PlayerController->GetPlayerState<ARugbyPlayerState>();
				if (rugbyPlayerState && rugbyPlayerState->GetTeamSide() == SSTEAMSIDE::SIDE_B)
				{
					if (RUGameSettings* game_settings = SIFApplication::GetApplication()->GetMatchGameSettings())
					{
						if (rugbyPlayerState)
						{
							RUDB_TEAM db_team = game_settings->team_settings[SSTEAMSIDE::SIDE_A].team;
							pTeamSelectListPopulator->SetGenderPermissionFlags(db_team.GetGenderPermissionFlags());
						}
					}
				}
				else
				{
					pTeamSelectListPopulator->SetGenderPermissionFlags(~0);
				}
			}
			else
			{
				pTeamSelectListPopulator->SetGenderPermissionFlags(~0);
			}

			//	Mattt H - Not sure why we are just putting in 1 team limit and letting the populator handle it, so I have just split it up here and set it up based on what the game mode is.
			if (SIFGameHelpers::GAGetGameMode() == GAME_MODE_RU13W) // Nick  WWS 7s to Womens // SEVENS)
			{
				pTeamSelectListPopulator->SetTeamLimit(TEAM_SELECT_CUSTOM_SEVENS);
			}
			else
			{
				// This will adjust the teams list for 15s to not include 7s teams. If in 7s mode it will not be used.
				pTeamSelectListPopulator->SetTeamLimit(TEAM_SELECT_RC3_NO_SEVENS);
			}
		}
	}

	//From OnCreate
	/*OnlineSelectTeams.scene_root = ui_object

	-- We need to inialise all the member varaibles.
	OnlineSelectTeams.filtered_teams_list = UINodeGetChild(ui_object, "SelectTeams/TeamMenu/List/FilteredTeams")
	OnlineSelectTeams.filter_node = UINodeGetChild(ui_object, "SelectTeams/TeamMenu/Filter");
	OnlineSelectTeams.team_list_heading = UINodeGetChild(ui_object, "SelectTeams/TeamMenu/Heading")
	OnlineSelectTeams.help_text = UINodeGetChild(ui_object, "HelpButtonTemplate/TextWidget:stencil_heading")
	OnlineSelectTeams.rating = UINodeGetChild(ui_object, "SelectTeams/TeamMenu/TeamInfo/Rating")
	OnlineSelectTeams.team_info = UINodeGetChild(ui_object, "SelectTeams/TeamMenu/TeamInfo")
	OnlineSelectTeams.loading_spokes = UINodeGetChild(ui_object, "Animation");*/

#if PLATFORM_SWITCH 
	m_switchInitialFocus = true;
#endif

	// Increment the filter position with no direction to update the filter text. This will also do an initial populate of the scrollbox.
	IncrementFilterCompetition(0);

	FavouriteTeamDefaultSelect();

	//screenTeamA = SIFGameHelpers::GAGetTeam(0);
	//screenTeamB = SIFGameHelpers::GAGetTeam(1);
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::RegisterFunctions()
{
	AddInputAction(FString("UI_Back"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenOnlineSelectTeams::OnBack));

#if PLATFORM_SWITCH 
	AddInputAxis("UI_Triggers_Axis", FWWUIScreenAxisDelegate::CreateUObject(this, &UWWUIScreenOnlineSelectTeams::OnRotationInput));
#else
	AddInputAction("UI_LeftTrigger", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenOnlineSelectTeams::OnLeftTrigger), false, true); // B
	AddInputAction("UI_RightTrigger", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenOnlineSelectTeams::OnRightTrigger), false, true); // B
#endif
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::OnInFocus()
{
	Super::OnInFocus();

	m_canTriggerAxis = true;

	SIFUIHelpers::ListenToPrimaryPlayer();

	//< Update Lobby Player List. >
	//RefreshPlayerList();
	if (UWidget* tempVoipList = FindChildWidget(WWUIScreenOnlineSelectTeams_UI::VoipTeamListVerticalBox))
	{
		tempVoipList->SetVisibility(ESlateVisibility::Hidden);
	}

	// Callback to when the whole lobby is ready
	OnLobbyReadyDelegateHandle = SIFApplication::GetApplication()->OnAllPlayerStatesReadyChanged.AddUObject(this, &UWWUIScreenOnlineSelectTeams::OnLobbyReadyChanged);
	SIFApplication::GetApplication()->OnNetworkRefresh.AddDynamic(this, &UWWUIScreenOnlineSelectTeams::OnReceivedNetworkRefresh);
	OnWindowEnter();

	SIFGameHelpers::GAResumeSandboxGame();

	if (SIFApplication::GetApplication()->GetVoipManager())
	{
		SIFApplication::GetApplication()->GetVoipManager()->MuteAllTalkers();
		ARugbyGameState* TheGameState = Cast< ARugbyGameState>(UGameplayStatics::GetGameState(GetWorld()));
		if (TheGameState)
		{
			TheGameState->ShowVoipScreen(false);
		}
	}

	SIFGameWorld* world = SIFApplication::GetApplication()->GetSandboxGame()->GetGameWorld();
	world->GetRules()->EnableConsequences(true);
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::Update(const float DeltaTime)
{
	//if (GameInstance->GetWorld() && GameInstance->GetWorld()->GetGameState())
	//{
	//	//Update the player list when someone drops the lobby
	//	int32 CurrentPlayerCount = GameInstance->GetWorld()->GetGameState()->PlayerArray.Num();
	//
	//	if (LastPlayerCount != CurrentPlayerCount)
	//	{
	//
	//		//RefreshPlayerList();
	//
	//		LastPlayerCount = CurrentPlayerCount;
	//	}
	//}

#if PLATFORM_SWITCH 
	if (m_switchInitialFocus)
	{
		UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenOnlineSelectTeams_UI::TeamListScrollbox));

		if (pTeamListScrollbox)
		{
			SetInitialFocus(pTeamListScrollbox);
			m_switchInitialFocus = false;
		}
	}
#endif

	if (m_setScrollBoxIndexOnTick)
	{
		UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenOnlineSelectTeams_UI::TeamListScrollbox));

		if (pTeamListScrollbox && m_ScrollBoxIndex != -1)
		{
			pTeamListScrollbox->SetSelectedIndex(m_ScrollBoxIndex);
		}

		m_setScrollBoxIndexOnTick = false;
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::OnOutFocus(bool ShouldOutFocus)
{
	Super::OnOutFocus(ShouldOutFocus);

	//From OnWindowExit()
	/*UIClearPopulatedObject(UINodeGetContext(OnlineSelectTeams.filtered_teams_list))
		if OnlineSelectTeams.initial_entry then
			IGLockAllHumanPlayerInputs(false)
			end
			OnlineSelectTeams.initial_entry = false
			OnlineSelectTeams.in_scene = false;
	GAResumeSandboxGame()
		UIDisableInGameInput(false);

	--Stop looping animations
		UIFXNodeStopAllAnimations(OnlineSelectTeams.team_list_heading)*/

	SIFApplication::GetApplication()->OnAllPlayerStatesReadyChanged.Remove(OnLobbyReadyDelegateHandle);
	SIFApplication::GetApplication()->OnNetworkRefresh.Remove(this, FName("OnReceivedNetworkRefresh"));
}


void UWWUIScreenOnlineSelectTeams::ExecuteTableFunction(FString InTableId, int InIdx, FString InAction, FString InActionString)
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (!PlayerController) return;
	ARugbyPlayerState* rugbyPlayerState = PlayerController->GetPlayerState<ARugbyPlayerState>();

	if (rugbyPlayerState && pRugbyGameInstance && TeamDatabaseID != DB_INVALID_ID)
	{
		SIFGameHelpers::GASetMutliplayerTeam((int)rugbyPlayerState->m_teamSide, TeamDatabaseID);

		if (pRugbyGameInstance->GetMatchmakingManager()->IsHost())
		{
			if (pRugbyGameInstance->GetMatchmakingManager()->IsQuickmatch())
			{
				SIFGameHelpers::GASetRandomStadiumFromTeam(TeamDatabaseID);
			}
			SIFMatchmakingHelpers::SetValue("tf_stadium", SIFGameHelpers::GAGetStadium());
		}

		if (SwitchStripsTimer.IsValid())
			UWWUIFunctionLibrary::StopTimer(SwitchStripsTimer);
		SwitchSandboxStrips(false);
//		SwitchSandboxTeam();

		pRugbyGameInstance->DealMenuAction(SCREEN_CANCEL_FADE, GetScreenID());
	}
}

void UWWUIScreenOnlineSelectTeams::TableOnSelectionChange(FString InTableId, int OldIdx, int NewIdx)
{
	UpdateSelectedTeam(NewIdx);
	UpdateTeamInfo();
}


void UWWUIScreenOnlineSelectTeams::OnWindowEnter()
{
	/*OnlineSelectTeams.all_lists_refreshed = false
		OnlineSelectTeams.in_scene = true;
	UISetCurrentBackground("")
		UIDisableInGameInput(true);
	--GAResetSandbox();

	--Setting in the default team ID will set that team to be selected after is has populated.
		UINodeSetProperty(OnlineSelectTeams.filtered_teams_list, "default_id", tostring(MultiplayerTrainingField.player_team_id))
		if MultiplayerLobby.game_mode_initialised then
			UIRefreshPopulatedObjectByPointer(OnlineSelectTeams.filtered_teams_list)
		else
			OnlineSelectTeams.filtered_teams_list.visible = false;
	OnlineSelectTeams.filter_node.visible = false;
	OnlineSelectTeams.team_info.visible = false;
	OnlineSelectTeams.loading_spokes.visible = true;
	end
		-- Start looping animation that pulses the filter arrows to draw attention to them
		UIFXNodeRunNamedAnimation(OnlineSelectTeams.team_list_heading, "arrows_pulse_animation")

		--If this is the first time we've entered this screen for this online session then
		--we need to prevent the user from pressing back.
		if OnlineSelectTeams.initial_entry then
			OnlineSelectTeams.help_text.text_string = "[ID_SELECT_TEAM_INITIAL_ENTRY_HELP]"
			if (MultiplayerTrainingField.in_training_field) then
				-- pause first, otherwise we lose control to AI madness, :jb
				GAPauseSandboxGame()
				end
				IGLockAllHumanPlayerInputs(true)

			else
				GAPauseSandboxGame()
				OnlineSelectTeams.help_text.text_string = "[ID_SELECT_TEAM_HELP]"
				end

				OnlineSelectTeams.show_title_animation = false
				ChatIndicatorOnEnter(OnlineSelectTeams, "OnlineSelectTeams");*/

	UWWUIScrollBox* pTeamScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenOnlineSelectTeams_UI::TeamListScrollbox));

	if (pTeamScrollBox)
	{
		SetInitialFocus(pTeamScrollBox);
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::OnTimeout()
{
	/*if parameters.timer_event == OnlineSelectTeams.TempPositionChangeData.PositionChangeTimer then
		OnlineSelectTeams.UpdateCompTeamsListboxSelection()	
		return true
	end
	
	return false*/
}

//===============================================================================
//===============================================================================
bool UWWUIScreenOnlineSelectTeams::OnSystemEvent(WWUINodeProperty & eventProperty)
{
	Super::OnSystemEvent(eventProperty);

	FString EventName = eventProperty.GetStringProperty("system_event");
	if (EventName.Compare(FString("set_team")) == 0)
	{
		OnLobbyTeamChanged(eventProperty.GetIntProperty("team_index"), eventProperty.GetIntProperty("db_id"));
	}
	else if (EventName.Compare(FString("on_game_settings_changed")) == 0)
	{
		//RefreshPlayerList();
		CheckTeamGenders();
	}
	/*if (parameters.system_event == "team_select_list_populated") then
		OnlineSelectTeams.all_lists_refreshed = true

		--Force update of competition name heading
		OnlineSelectTeams.FilteredTeamListChanged(OnlineSelectTeams.filtered_teams_list, parameters)
		return true
		elseif ChatIndicatorOnSystemEvent(OnlineSelectTeams, "OnlineSelectTeams", ui_object, parameters) then
		return(true);
	else
		return MultiplayerTrainingField.OnSystemEvent(ui_object, parameters)
		end*/
	UpdateLegend();
	return false;
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::OnBack(APlayerController* controller)
{
	if (!m_initalEntry)
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
		if (pRugbyGameInstance)
		{
			if (SwitchStripsTimer.IsValid())
				UWWUIFunctionLibrary::StopTimer(SwitchStripsTimer);
			SwitchSandboxStrips(true);

			if (!pRugbyGameInstance->GetMatchmakingManager()->IsHost())
			{
				if (RUGameSettings* game_settings = SIFApplication::GetApplication()->GetMatchGameSettings())
				{
					RUDB_TEAM db_homeTeam = game_settings->team_settings[SSTEAMSIDE::SIDE_A].team;
					RUDB_TEAM db_awayTeam = game_settings->team_settings[SSTEAMSIDE::SIDE_B].team;

					if (db_homeTeam.GetGenderPermissionFlags() == db_awayTeam.GetGenderPermissionFlags())
					{
						pRugbyGameInstance->DealMenuAction(SCREEN_CANCEL, Screens_UI::OnlineSelectTeams);
					}
				}
			}
			else
			{
				pRugbyGameInstance->DealMenuAction(SCREEN_CANCEL, Screens_UI::OnlineSelectTeams);
			}
		}
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::UpdateCompTeamsListboxSelection()
{
	/*local selected_bar = OnlineSelectTeams.TempPositionChangeData.Selected_Bar
	local selected_node = OnlineSelectTeams.TempPositionChangeData.Selected_Node
	
	-- Slide the bar
	local selected_node_anchor = UINodeGetAnchor(selected_node)
	local selected_bar_anchor = UINodeGetAnchor(selected_bar)
	
	selected_bar_anchor.y = selected_node_anchor.y
	UIFXNodeStopAllAnimations(selected_bar)
	UIFXNodeSlideAnchorTo(selected_bar, selected_bar_anchor, 0.16667, 0)*/
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::TeamMenuOnAction()
{
	/*local action_handled = true
	
	if not MultiplayerLobby.game_mode_initialised then
		return false;
	end
	
	if parameters.action_event == "ACTION_TYPE_ACTION" then
		local selected_team_filter = UINodeGetSelectedNode( OnlineSelectTeams.filtered_teams_list )
		local team_list = UINodeGetChild( selected_team_filter, "ListBox" )
		local selected_team = UINodeGetSelectedNode( team_list )
		
		if (selected_team ~= nil) then
			local team_id = tonumber( UINodeGetProperty( selected_team, "db_id" ))
			
			MultiplayerTrainingField.SetCurrentlySelectedTeam( team_id )
			ProceedToWindow("MultiplayerLobby");
			--ProceedToPreviousWindow( UIGetCurrentWindow(), parameters, true)
			OnlineSelectTeams.FlushInputs( parameters.controller_id ) 
		end
	else
		action_handled = false
	end
	
	return action_handled*/
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::OnCompTeamsListboxChanged()
{
	/*-- Play the audio file.
	MenuSoundMenuMove()
	
	local team_info_node = UINodeGetChild( ui_object, "../../../../TeamInfo" )
	local player_menu = UINodeGetParent( team_info_node )
	
	-- Will have to wait for the next frame because the anchor positions haven't been updated yet.
	OnlineSelectTeams.TempPositionChangeData.Selected_Node = UIGetNode(parameters.selected_node)
	OnlineSelectTeams.TempPositionChangeData.Selected_Bar	 = UINodeGetChild( ui_object, "../../../SelectedBar" )	
	UISetTimerForFrameCount( OnlineSelectTeams.TempPositionChangeData.PositionChangeTimer, OnlineSelectTeams.scene_root, OnlineSelectTeams.TempPositionChangeData.PositionChangeTimerLength )
	
	TeamSelectOnTeamChanged( ui_object, parameters )
	
	OnlineSelectTeams.UpdateTeamInfo( team_info_node, OnlineSelectTeams.TempPositionChangeData.Selected_Node )
	
	return true*/
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::FilteredTeamListChanged()
{
	/*-- The title needs to be set correctly
	local heading = UINodeGetChild( ui_object, "../../Filter/filter_bar_mid/Name" )
	local heading_count = UINodeGetChild( ui_object, "../../Filter/filter_bar_mid/Count" )
	local selected_child = UINodeGetSelectedNode( ui_object )
	local team_list = UINodeGetChild( selected_child, "ListBox" )
	
	local selected_list = UINodeGetSelectedNode( selected_child )
	UITextSetText( heading, UINodeGetProperty( team_list, "comp_name" ))
	UITextSetText( heading_count, UINodeGetSelectedNodeIndex(ui_object)  + 1 .. "/" .. UINodeGetNumChildren(ui_object) )
	

	-- We need to focus the selection on the correct team.
	local selection_changed_params = {}
	selection_changed_params.selected_node = UINodeGetContext( UINodeGetSelectedNode( selected_list ))
	selection_changed_params.previous_node = selection_changed_params.selected_node
	OnlineSelectTeams.OnCompTeamsListboxChanged( selected_list, selection_changed_params )*/
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::UpdateTeamInfo()
{
	if (TeamDatabaseID == 0)
	{
		return;
	}

	MabString LogoPath = SIFGameHelpers::GAGetTeamLogoAssetPath(TeamDatabaseID);

	UImage* pLogoImage = Cast<UImage>(FindChildWidget(WWUIScreenOnlineSelectTeams_UI::ImageTeamLogo));

	if (pLogoImage)
	{
		// The opposition is still TBD
		UTexture2D* pTexture = nullptr;

		FString name = FString(LogoPath.c_str());
		pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name));

		if (pTexture)
		{
			pLogoImage->SetBrushFromTexture(pTexture, true);
			pLogoImage->SetVisibility(ESlateVisibility::Visible);
		}
	}

	/*UImage* logoBackground = Cast<UImage>(FindChildWidget(WWUIScreenOnlineSelectTeams_UI::teamBkgd));
	SIFUIHelpers::SetImageColourFromTeamColour(logoBackground, TeamDatabaseID);*/

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		RUDB_TEAM db_team;
		RUGameDatabaseManager* database_manager = pRugbyGameInstance->GetGameDatabaseManager();

		if (database_manager)
		{
			database_manager->LoadData(db_team, TeamDatabaseID);
			MabColour team_colour;
			db_team.GetPrimaryColour(team_colour);

			UpdateTeamRating(db_team.GetNormaliseRanking() * 100.0f);
		}
	}
	// #rc3_legacy
	//UpdateTeamRating(FMath::RoundToInt(pNewTeamData->ranking));
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::FlushInputs(int controller_id)
{
	/*-- Clear out all pending actions.
	UIFlushMappedInputs( controller_id, "ACTION_TYPE_ACTION" )
	UIFlushMappedInputs( controller_id, "ACTION_TYPE_BACK" )*/
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::SetRatingsVisible(bool is_visible)
{
	/*-- Ensure the is_visible isn't set to nil.
	if is_visible == nil then
		is_visible = true
	end
	
	-- There are three things we need to hide: the rating, title and background.
	-- These three options could be grouped together in the layout tool under a common node,
	-- but I don't want to change anything I don't have to at this stage of the project.
	if OnlineSelectTeams.rating then
		OnlineSelectTeams.rating.visible = is_visible
	end*/
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::FavouriteTeamDefaultSelect()
{
	UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenOnlineSelectTeams_UI::TeamListScrollbox));

	if (pTeamListScrollbox)
	{
		UWWUIPopulatorTeamSelectList* pTeamSelectListPopulator = Cast<UWWUIPopulatorTeamSelectList>(pTeamListScrollbox->GetPopulator());

		if (pTeamSelectListPopulator)
		{
			if (SIFPlayerProfileManager::GetInstance() && SIFPlayerProfileManager::GetInstance()->GetMasterProfile() && SIFPlayerProfileManager::GetInstance()->GetMasterProfile()->GetNamedValueList())
			{
				int32 fifteensFavouriteTeam = -1;
				int32 sevensFavouriteTeam = -1;

				if (SIFPlayerProfileManager::GetInstance()->GetMasterProfile()->GetNamedValueList()->GetNamedValue(PLAYER_PROFILE_DEFAULT_TEAM))
				{
					fifteensFavouriteTeam = SIFPlayerProfileManager::GetInstance()->GetMasterProfile()->GetNamedValueList()->GetNamedValue(PLAYER_PROFILE_DEFAULT_TEAM)->ToInt();
				}
				if (SIFPlayerProfileManager::GetInstance()->GetMasterProfile()->GetNamedValueList()->GetNamedValue(PLAYER_PROFILE_DEFAULT_SEVENS_TEAM))
				{
					sevensFavouriteTeam = SIFPlayerProfileManager::GetInstance()->GetMasterProfile()->GetNamedValueList()->GetNamedValue(PLAYER_PROFILE_DEFAULT_SEVENS_TEAM)->ToInt();
				}

				bool searchForFavouriteTeam = true;
				int32 favouriteTeam = fifteensFavouriteTeam;
				int32 populatorTeamIndex = -1;

				if (SIFGameHelpers::GAGetGameMode() == GAME_MODE_RU13W) // Nick  WWS 7s to Womens //SEVENS)
				{
					favouriteTeam = sevensFavouriteTeam;

					//	Because we can only choose genders in sevens.
					URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
					if (pRugbyGameInstance)
					{
						RUGameDatabaseManager* database_manager = pRugbyGameInstance->GetGameDatabaseManager();

						if (database_manager)
						{
							RUDB_TEAM db_favourite_team;
							database_manager->LoadData(db_favourite_team, favouriteTeam);
							if (db_favourite_team.GetGenderPermissionFlags() != pTeamSelectListPopulator->GetGenderPermissionFlags() )
							{
								ARugbyPlayerState* rugbyPlayerState = PlayerController->GetPlayerState<ARugbyPlayerState>();
								if (rugbyPlayerState && rugbyPlayerState->GetTeamSide() == SSTEAMSIDE::SIDE_B)
								{
									if (RUGameSettings* game_settings = SIFApplication::GetApplication()->GetMatchGameSettings())
									{
										if (rugbyPlayerState)
										{
											RUDB_TEAM db_home_team = game_settings->team_settings[SSTEAMSIDE::SIDE_A].team;
											favouriteTeam = SIFGameWorld::GetUIOppositionTeam(db_home_team.GetDatabaseId());
										}
									}
								}
							}
						}
					}
				}

				if (searchForFavouriteTeam)
				{
					for (uint32 competitionIndex = pTeamSelectListPopulator->GetCurrentCompetitionIndex(); competitionIndex < pTeamSelectListPopulator->GetCompetitionCount() - 1; ++competitionIndex)
					{
						if (pTeamSelectListPopulator->CompetitionIndexIsValid(competitionIndex))
						{
							for (uint32 competitionTeamIndex = 0; competitionTeamIndex < pTeamSelectListPopulator->GetCompetitionTeamCount(competitionIndex); ++competitionTeamIndex)
							{
								if (favouriteTeam == pTeamSelectListPopulator->GetCompetitionTeamDatabaseID(competitionIndex, competitionTeamIndex))
								{
									competitionIndex = pTeamSelectListPopulator->GetCompetitionCount() - 1;
									populatorTeamIndex = competitionTeamIndex;
									competitionTeamIndex = pTeamSelectListPopulator->GetCompetitionTeamCount(competitionIndex);
								}
							}

							if (populatorTeamIndex == -1)
							{
								IncrementFilterCompetition(1);
							}
						}
					}
				}

				if (populatorTeamIndex != -1)
				{
					m_setScrollBoxIndexOnTick = true;
					m_ScrollBoxIndex = populatorTeamIndex;
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::IncrementFilterCompetition(int32 Direction)
{
	UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenOnlineSelectTeams_UI::TeamListScrollbox));

	if (pTeamListScrollbox)
	{
		UWWUIPopulatorTeamSelectList* pTeamSelectListPopulator = Cast<UWWUIPopulatorTeamSelectList>(pTeamListScrollbox->GetPopulator());

		if (pTeamSelectListPopulator)
		{
			FString NewCompName = pTeamSelectListPopulator->IncrementFilterCompetition(Direction);

			UWidget* pCompetitionCategorySelectorWidget = FindChildWidget(WWUIScreenOnlineSelectTeams_UI::CompetitionListSwitcher);

			if (pCompetitionCategorySelectorWidget)
			{
				UTextBlock* pTitleText = Cast<UTextBlock>(FindChildOfTemplateWidget(pCompetitionCategorySelectorWidget, WWUIScreenOnlineSelectTeams_UI::TextCategoryName));

				if (pTitleText)
				{
					SetWidgetText(pTitleText, FText::FromString(UWWUITranslationManager::Translate(NewCompName)));
				}

				FString NumberText = FString::FromInt(pTeamSelectListPopulator->GetCurrentCompetitionIndex() + 1) + " / " + FString::FromInt(pTeamSelectListPopulator->GetCompetitionCount());

				UTextBlock* pNumCategoriesText = Cast<UTextBlock>(FindChildOfTemplateWidget(pCompetitionCategorySelectorWidget, WWUIScreenOnlineSelectTeams_UI::TextNumCategories));

				if (pNumCategoriesText)
				{
					SetWidgetText(pNumCategoriesText, FText::FromString(NumberText));
				}
			}

		}

		pTeamListScrollbox->PopulateAndRefresh();

		if (!m_switchInitialFocus)
		{
			SetInitialFocus(pTeamListScrollbox);
		}

		//UpdateSelectedTeam(pTeamListScrollbox->GetSelectedIndex());
		//UpdateTeamInfo();
		
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::UpdateSelectedTeam(int32 NewIdx)
{
	UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenOnlineSelectTeams_UI::TeamListScrollbox));

	if (pTeamListScrollbox)
	{
		UWWUIListField* pSelectedListField = pTeamListScrollbox->GetListField(NewIdx);

		if (pSelectedListField)
		{
			unsigned char old_gender_flags = SIFGameHelpers::GAGetTeamGenderPermissionFlags(TeamDatabaseID);

			// Grab the the team db id set up when the list was populated.
			TeamDatabaseID = pSelectedListField->GetIntProperty("team_db_id");

			unsigned char new_gender_flags = SIFGameHelpers::GAGetTeamGenderPermissionFlags(TeamDatabaseID);

			if (old_gender_flags != new_gender_flags)
			{
				update_player_model_genders = true;
			}

			if (SwitchStripsTimer.IsValid())
				UWWUIFunctionLibrary::StopTimer(SwitchStripsTimer);
			SwitchStripsTimer = UWWUIFunctionLibrary::OnTimer(SWITCH_STRIP_DELAY_TIME, FTimerDelegate::CreateUObject(this, &UWWUIScreenOnlineSelectTeams::SwitchSandboxStrips, false));
		}
	}

	UpdateLegend();
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::UpdateLegend()
{
	FString legendString = "[INP_GetAction(RU_UI_ACTION_ACCEPT)] SELECT TEAM";

	if (!m_initalEntry)
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
		if (pRugbyGameInstance)
		{
			if (!pRugbyGameInstance->GetMatchmakingManager()->IsHost())
			{
				if (RUGameSettings* game_settings = SIFApplication::GetApplication()->GetMatchGameSettings())
				{
					RUDB_TEAM db_homeTeam = game_settings->team_settings[SSTEAMSIDE::SIDE_A].team;
					RUDB_TEAM db_awayTeam = game_settings->team_settings[SSTEAMSIDE::SIDE_B].team;

					if (db_homeTeam.GetGenderPermissionFlags() == db_awayTeam.GetGenderPermissionFlags())
					{
						legendString += " [INP_GetAction(RU_UI_ACTION_RETURN)] BACK";
					}
				}
			}
			else
			{
				legendString += " [INP_GetAction(RU_UI_ACTION_RETURN)] BACK";
			}
		}
	}

	if (UWidget* tempLegendWidget = UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenOnlineSelectTeams_UI::LegendText))
	{
		if (UWWUIRichTextBlockWithTranslate* tempLegendText = Cast<UWWUIRichTextBlockWithTranslate>(tempLegendWidget))
		{
			tempLegendText->SetText(FText::FromString(legendString));
		}
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenOnlineSelectTeams::SetInitialFocus(UWidget* pScrollBoxWidget)
{
	if (pScrollBoxWidget)
	{
		//Find the first element of the main menu and set focus
		UWWUIScrollBox* pCurrentScrollBox = Cast<UWWUIScrollBox>(pScrollBoxWidget);

		if (pCurrentScrollBox)
		{
			pCurrentScrollBox->FocusFirstListField(SIFApplication::GetApplication()->GetMasterPlayerController());
		}
	}
}

void UWWUIScreenOnlineSelectTeams::UpdateTeamRating(float NewRating)
{

}

void UWWUIScreenOnlineSelectTeams::OnRightTrigger(APlayerController* OwningPlayer)
{
	IncrementFilterCompetition(+1);

	m_triggerAxisHandle = UWWUIFunctionLibrary::OnTimer(TRIGGER_DELAY_TIME, FTimerDelegate::CreateUObject(this, &UWWUIScreenOnlineSelectTeams::ResetAxisTrigger), false);
	m_canTriggerAxis = false;
}

void UWWUIScreenOnlineSelectTeams::OnLeftTrigger(APlayerController* OwningPlayer)
{
	IncrementFilterCompetition(-1);

	m_triggerAxisHandle = UWWUIFunctionLibrary::OnTimer(TRIGGER_DELAY_TIME, FTimerDelegate::CreateUObject(this, &UWWUIScreenOnlineSelectTeams::ResetAxisTrigger), false);
	m_canTriggerAxis = false;
}

void UWWUIScreenOnlineSelectTeams::OnRotationInput(float AxisValue, APlayerController* OwningPlayer)
{
	if (m_canTriggerAxis)
	{
		if (AxisValue < 0.0f)
		{
			OnLeftTrigger(OwningPlayer);
		}
		else if (AxisValue > 0.0f)
		{
			OnRightTrigger(OwningPlayer);
		}
	}
}

void UWWUIScreenOnlineSelectTeams::ResetAxisTrigger()
{
	if (m_triggerAxisHandle.IsValid())
	{
		UWWUIFunctionLibrary::StopTimer(m_triggerAxisHandle);
		m_canTriggerAxis = true;
	}
}

void UWWUIScreenOnlineSelectTeams::RefreshPlayerList()
{
	if (UWWUIScrollBox* homeTeamScrollbox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenOnlineSelectTeams_UI::HomeTeamList)))
	{
		if (UWWUIPopulatorOnlinePlayers* populator = Cast<UWWUIPopulatorOnlinePlayers>(homeTeamScrollbox->GetPopulator()))
			populator->PopulatorSide = SIDE_A;

		homeTeamScrollbox->Refresh();
	}

	if (UWWUIScrollBox* awayTeamScrollbox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenOnlineSelectTeams_UI::AwayTeamList)))
	{
		if (UWWUIPopulatorOnlinePlayers* populator = Cast<UWWUIPopulatorOnlinePlayers>(awayTeamScrollbox->GetPopulator()))
			populator->PopulatorSide = SIDE_B;

		awayTeamScrollbox->Refresh();
	}

	if (UWWUIScrollBox* missingPlayerScrollbox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenOnlineSelectTeams_UI::MissingPlayerList)))
	{
		if (UWWUIPopulatorOnlinePlayers* populator = Cast<UWWUIPopulatorOnlinePlayers>(missingPlayerScrollbox->GetPopulator()))
			populator->PopulatorSide = SIDE_NONE;

		missingPlayerScrollbox->Refresh();
	}

	if (UUserWidget* homeTeamHeader = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenOnlineSelectTeams_UI::HomeTeamHeader)))
	{
		if (RUGameSettings* game_settings = SIFApplication::GetApplication()->GetMatchGameSettings())
		{
			RUDB_TEAM db_team = game_settings->team_settings[SSTEAMSIDE::SIDE_A].team;
			MabColour team_colour;
			db_team.GetScreenFriendlyColour(team_colour);

			FLinearColor team_display_colour = SIFGameHelpers::GAConvertMabColorToFLinearColor(team_colour);
			if (FMath::Max3(team_display_colour.R, team_display_colour.G, team_display_colour.B) > 0.6f)
			{
				team_display_colour = UWWUIFunctionLibrary::ScaleLinearColourBetween(team_display_colour, 0.0f, 0.6f);
			}

			//< Set Colour >
			if (UBorder* colourBorder1 = Cast<UBorder>(UWWUIFunctionLibrary::FindChildWidget(homeTeamHeader, WWUIScreenOnlineSelectTeams_UI::TeamColour1)))
				colourBorder1->SetBrushColor(team_display_colour);

			if (UBorder* colourBorder2 = Cast<UBorder>(UWWUIFunctionLibrary::FindChildWidget(homeTeamHeader, WWUIScreenOnlineSelectTeams_UI::TeamColour2)))
				colourBorder2->SetBrushColor(team_display_colour);

			//< Set team logo >
			MabString LogoPath = SIFGameHelpers::GAGetTeamLogoHUDAssetPath(db_team.GetDbId());
			if (UImage* pLogoImage = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(homeTeamHeader, WWUIScreenOnlineSelectTeams_UI::TeamLogo)))
			{
				FString name = FString(LogoPath.c_str());
				if (UTexture2D* pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name)))
				{
					pLogoImage->SetBrushFromTexture(pTexture, true);
				}
			}

			//< Set Team Name >
			if (UWWUITextBlock* teamName = Cast<UWWUITextBlock>(UWWUIFunctionLibrary::FindChildWidget(homeTeamHeader, WWUIScreenOnlineSelectTeams_UI::TeamName)))
				teamName->SetText(FText::FromString(db_team.GetShortName()));
		}
	}

	if (UUserWidget* awayTeamHeader = Cast<UUserWidget>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenOnlineSelectTeams_UI::AwayTeamHeader)))
	{
		if (RUGameSettings* game_settings = SIFApplication::GetApplication()->GetMatchGameSettings())
		{
			RUDB_TEAM db_team = game_settings->team_settings[SSTEAMSIDE::SIDE_B].team;
			MabColour team_colour;
			db_team.GetScreenFriendlyColour(team_colour);

			FLinearColor team_display_colour = SIFGameHelpers::GAConvertMabColorToFLinearColor(team_colour);
			if (FMath::Max3(team_display_colour.R, team_display_colour.G, team_display_colour.B) > 0.6f)
			{
				team_display_colour = UWWUIFunctionLibrary::ScaleLinearColourBetween(team_display_colour, 0.0f, 0.6f);
			}

			//< Set Colour >
			if (UBorder* colourBorder1 = Cast<UBorder>(UWWUIFunctionLibrary::FindChildWidget(awayTeamHeader, WWUIScreenOnlineSelectTeams_UI::TeamColour1)))
				colourBorder1->SetBrushColor(team_display_colour);

			if (UBorder* colourBorder2 = Cast<UBorder>(UWWUIFunctionLibrary::FindChildWidget(awayTeamHeader, WWUIScreenOnlineSelectTeams_UI::TeamColour2)))
				colourBorder2->SetBrushColor(team_display_colour);

			//< Set team logo >
			MabString LogoPath = SIFGameHelpers::GAGetTeamLogoAssetPath(db_team.GetDbId());
			if (UImage* pLogoImage = Cast<UImage>(UWWUIFunctionLibrary::FindChildWidget(awayTeamHeader, WWUIScreenOnlineSelectTeams_UI::TeamLogo)))
			{
				FString name = FString(LogoPath.c_str());
				if (UTexture2D* pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name)))
				{
					pLogoImage->SetBrushFromTexture(pTexture, true);
				}
			}

			//< Set Team Name >
			if (UWWUITextBlock* teamName = Cast<UWWUITextBlock>(UWWUIFunctionLibrary::FindChildWidget(awayTeamHeader, WWUIScreenOnlineSelectTeams_UI::TeamName)))
				teamName->SetText(FText::FromString(db_team.GetShortName()));
		}
	}
}

void UWWUIScreenOnlineSelectTeams::OnReceivedNetworkRefresh(ENetworkRefreshType RefreshType)
{
	//if (RefreshType == ENetworkRefreshType::PLAYER_STATE) RefreshPlayerList();
}

void UWWUIScreenOnlineSelectTeams::OnLobbyReadyChanged(bool bAllReady)
{
	//RefreshPlayerList();
}

void UWWUIScreenOnlineSelectTeams::OnLobbyTeamChanged(int _Side, int _NewTeamDbId)
{
	CheckTeamGenders();
	//RefreshPlayerList();
}

void UWWUIScreenOnlineSelectTeams::CheckTeamGenders()
{
	if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
	{
		if (ARugbyPlayerState* rugbyPlayerState = PlayerController->GetPlayerState<ARugbyPlayerState>())
		{
			if (RUGameSettings* game_settings = pRugbyGameInstance->GetMatchGameSettings())
			{
				//	Home Team Checks.

				//	As home team, we decide the gender, so change the other team if neccessary.
				if (rugbyPlayerState->m_teamSide == SSTEAMSIDE::SIDE_A)
				{
					unsigned char mask = PLAYER_GENDER_FLAG_MALE | PLAYER_GENDER_FLAG_FEMALE;

					RUDB_TEAM db_myTeam = game_settings->team_settings[SIDE_A].team;
					RUDB_TEAM db_opposingTeam = game_settings->team_settings[SSTEAMSIDE::SIDE_B].team;
					if ((db_myTeam.GetGenderPermissionFlags() & mask) != (db_opposingTeam.GetGenderPermissionFlags() & mask))
					{
						SetGameSettingsTeamToDefaultTeam();
					}
				}

				//	Away Team Checks.

				if (rugbyPlayerState->m_teamSide == SSTEAMSIDE::SIDE_B)
				{
					//	Set the populator gender based on the home team.
					UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenOnlineSelectTeams_UI::TeamListScrollbox));

					if (pTeamListScrollbox)
					{
						UWWUIPopulatorTeamSelectList* pTeamSelectListPopulator = Cast<UWWUIPopulatorTeamSelectList>(pTeamListScrollbox->GetPopulator());

						if (pTeamSelectListPopulator)
						{
							if (TeamDatabaseID != DB_INVALID_ID)
							{
								RUDB_TEAM db_opposingTeam = game_settings->team_settings[SSTEAMSIDE::SIDE_A].team;
								if (pTeamSelectListPopulator->GetGenderPermissionFlags() != db_opposingTeam.GetGenderPermissionFlags())
								{
									pTeamSelectListPopulator->SetGenderPermissionFlags(db_opposingTeam.GetGenderPermissionFlags());

									// Increment the filter position with no direction to update the filter text. This will also do an initial populate of the scrollbox.
									IncrementFilterCompetition(0);
									FavouriteTeamDefaultSelect();

									SwapSandboxTeamGender();
								}
							}
						}
					}
				}
			}
		}
	}
}

void UWWUIScreenOnlineSelectTeams::SetGameSettingsTeamToDefaultTeam()
{
	if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
	{
		if (RUGameSettings* game_settings = pRugbyGameInstance->GetMatchGameSettings())
		{
			//	Set the team to a default for that gender so that there isn't any cases of a mismatched team.
			ARugbyPlayerState* rugbyPlayerState = pRugbyGameInstance->GetMasterPlayerController()->GetPlayerState<ARugbyPlayerState>();
			if (rugbyPlayerState)
			{
				//	Get the other team.
				RUDB_TEAM db_myTeam = game_settings->team_settings[SSTEAMSIDE::SIDE_A].team;
				RL3DB_TEAM defaultTeam(SIFGameHelpers::GAGetDefaultTeam());

				unsigned char mask = PLAYER_GENDER_FLAG_MALE | PLAYER_GENDER_FLAG_FEMALE;
				if ((defaultTeam.GetGenderPermissionFlags() & mask) == (db_myTeam.GetGenderPermissionFlags() & mask))
				{
					//	Use the default team as it is compatible with the gender of the other team.
					SIFGameHelpers::GASetTeam(SSTEAMSIDE::SIDE_B, SIFGameHelpers::GAGetDefaultTeam());
				}
				else
				{
					//	Match the gender with the opposing team.
					bool teamWithWomen = (db_myTeam.GetGenderPermissionFlags() & PLAYER_GENDER_FLAG_FEMALE) == PLAYER_GENDER_FLAG_FEMALE;
					if (teamWithWomen)
					{
						SIFGameHelpers::GASetTeam(SSTEAMSIDE::SIDE_B, pRugbyGameInstance->GetDefaultWomensTeam());
					}
					else
					{
						//	THIS NEEDS TO BE CHANGED TO A FUCNTION FOR R7'S IF WE CHANGE HOW THAT WORKS :C
						//	The r7 default team is aus.
						SIFGameHelpers::GASetTeam(SSTEAMSIDE::SIDE_B, PLAYER_PROFILE_DEFAULT_SEVENS_TEAM_DEFAULT);
					}
				}
			}
		}
	}
}

void UWWUIScreenOnlineSelectTeams::SwitchSandboxStrips(bool restoreToGameSettings)
{
	int teamId = TeamDatabaseID;

	if (restoreToGameSettings)
	{
		if (!UOBJ_IS_VALID(PlayerController))
		{
			return;
		}

		ARugbyPlayerState* pRugbyPlayerState = PlayerController->GetPlayerState<ARugbyPlayerState>();
		if (!pRugbyPlayerState)
		{
			return;
		}

		teamId = SIFGameHelpers::GAGetTeam((int)pRugbyPlayerState->m_teamSide);
	}

	URugbyGameInstance* pRugbyGameInstance = URugbyGameInstance::GetInstance();
	if (pRugbyGameInstance != nullptr)
	{
		SIFGameWorld* pActiveGameWorld = pRugbyGameInstance->GetActiveGameWorld();
		if (pActiveGameWorld != nullptr && pActiveGameWorld->IsSandbox())
		{
			SIFGameHelpers::GASetTeamStrip(teamId, (int)SSTEAMSIDE::SIDE_A);

			if (update_player_model_genders)
			{
				update_player_model_genders = false;
				SwapSandboxTeamGender();
			}
		}
	}
}

void UWWUIScreenOnlineSelectTeams::SwapSandboxTeamGender()
{
	SIFGameWorld * world = SIFApplication::GetApplication()->GetSandboxGame()->GetGameWorld();

	ARugbyPlayerState* rugbyPlayerState = PlayerController->GetPlayerState<ARugbyPlayerState>();

	SSTEAMSIDE mySide = rugbyPlayerState ? rugbyPlayerState->GetTeamSide().GetValue() : SSTEAMSIDE::SIDE_A;

	if (world && TeamDatabaseID != DB_INVALID_ID)
	{
		//Get the default team and the newly selectd team
		RL3DB_TEAM defaultTeam(SIFGameHelpers::GAGetDefaultTeam());

		RL3DB_TEAM newTeamSelected(TeamDatabaseID);

		unsigned char mask = PLAYER_GENDER_FLAG_MALE | PLAYER_GENDER_FLAG_FEMALE;
		if ((defaultTeam.GetGenderPermissionFlags() & mask) == (newTeamSelected.GetGenderPermissionFlags() & mask))
		{
			//	Use the default team as it matches the selected teams gender
			SIFGameHelpers::GASetTeam(mySide, SIFGameHelpers::GAGetDefaultTeam());
		}
		else
		{
			//Otherwise, the default team doesn't match the gender we want, so use the default womens team, or the default r7s team.
			bool teamWithWomen = (newTeamSelected.GetGenderPermissionFlags() & PLAYER_GENDER_FLAG_FEMALE) == PLAYER_GENDER_FLAG_FEMALE;
			if (teamWithWomen)
			{
				SIFGameHelpers::GASetTeam(mySide, SIFApplication::GetApplication()->GetDefaultWomensTeam());
			}
			else
			{
				//	THIS NEEDS TO BE CHANGED TO A FUCNTION FOR R7'S IF WE CHANGE HOW THAT WORKS :C
				//	The r7 default team is aus.
				SIFGameHelpers::GASetTeam(mySide, PLAYER_PROFILE_DEFAULT_SEVENS_TEAM_DEFAULT);
			}
		}
		
		TArray<FRugbyTeamSettings> settings_array = SIFApplication::GetApplication()->GetMatchGameSettings()->team_settings;
		FRugbyTeamSettings teamSettings = settings_array[mySide];

		world->GetTeam(SIDE_A)->SetDbTeam(teamSettings);

		RUTeam* team = world->GetTeam(SIDE_A);

		const SIFRugbyCharacterList& players = team->GetPlayers();

		for (unsigned int i = 0; i < players.size(); ++i)
		{
			ARugbyCharacter * player = players[i];
			PlayerCustomisationInfo csinfo = player->GetCustomisationInfo();

			if (!UOBJ_IS_VALID(player))
			{
				UE_LOG(LogTemp, Warning, TEXT("SIFGameWorld::DoFullSwapSandboxTeams Invalid player at index: %d on side: %d"), i, 0);
				continue;
			}

			RUDB_PLAYER* db_player = nullptr;
			PLAYER_POSITION	player_position = PP_NONE;


			SSTEAMSIDE side = team->GetSide();

			db_player = &team->GetDbPlayerByIndex(i);
			player_position = team->GetDbPlayerPositionByIndex(i);

			// Removed since we should not need to initialise players here, it is done on world creation.
			//if (player->GetAttributes())
			//{
			//	player->GetAttributes()->Initialise(team, db_player, player_position);
			//}

			csinfo.Setup(db_player);

			player->SetCustomisationInfo(csinfo);

			player->ApplyCustomisation();
		}
	}
}

void UWWUIScreenOnlineSelectTeams::SwitchSandboxTeam()
{
	
}