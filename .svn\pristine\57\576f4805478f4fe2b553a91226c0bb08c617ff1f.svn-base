// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.


#include "WWUIScreenOnlinePlaySettings.h"
#include "UI/GeneratedHeaders/WWUIScreenOnlinePlaySettings_UI_Namespace.h"
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"

#include "Utility/Helpers/SIFGameHelpers.h"

#include "WWUITranslationManager.h"
#include "WWUIFunctionLibrary.h"
#include "WWUISettingsValues.h"

#include "Match/RugbyUnion/RUStadiumManager.h"
#include "RugbyGameInstance.h"

#include "WWUIScrollBox.h"
#include "WWUIUserWidget.h"
#include "Image.h"

#define ONLINE_PLAY_SETTINGS_STADIUM_PROPERTY_NAME "Stadium_ID"

void UWWUIScreenOnlinePlaySettings::Startup(UWWUIStateScreenData* InData /*= nullptr*/)
{
	if (UWWUIScrollBox* scrollbox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenOnlinePlaySettings_UI::MatchSettingsScrollBox)))
	{
		m_pMatchSettingsScrollBox = scrollbox;
		m_pMatchSettingsScrollBox->FocusFirstListField(SIFApplication::GetApplication()->GetMasterPlayerController());

		FOptionProperties tempOptions;
		for (int8 i = 0; i < OnlinePlaySettingsSettingss::OPSS_Max; ++i)
		{
			UUserWidget* tempOptionWidget = Cast<UUserWidget>(scrollbox->GetListField(i));

			if (tempOptionWidget)
			{
				m_aWidget_list.Add(tempOptionWidget);
			}
			m_aOptionIndex_list.Add(0);
		}

		LoadStadiumsOptions();
		LoadMatchLengthOptions();
		LoadTimeOfDayOptions();
		LoadConditionOptions();
		LoadBalancedTeamsOptions();

		for (int8 i = 0; i < OnlinePlaySettingsSettingss::OPSS_Max; ++i)
		{
			UWidget* tempWidget = GetIndexedListWidgetFromWidgetList(i);

			if (tempWidget)
			{
				WWOption::UpdateOption(this, tempWidget, optionValueMap, 0);
			}
		}
	}
}

//New fucntion to bind screen inputs
void UWWUIScreenOnlinePlaySettings::RegisterFunctions()
{
	AddInputAction(FString("UI_Left"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenOnlinePlaySettings::OnLeft), true);
	AddInputAction(FString("UI_Right"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenOnlinePlaySettings::OnRight), true);
	AddInputAction(FString("UI_Select"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenOnlinePlaySettings::OnSelect));
	AddInputAction(FString("UI_Back"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenOnlinePlaySettings::OnBack));
}

// //OnWindowEnter
// void UWWUIScreenOnlinePlaySettings::OnInFocus()
// {
// 	//The game settings need to be populated.
// 	LoadStadiumOptions();
// 	LoadMatchLengthOptions();
// 	LoadTimeOfDayOptions();
// 	LoadConditionOptions();
// 	// Mattt H - Need to add the balanced team option here (loading).
// 
// 	LoadSettings();
// 
// 	GARandomiseWeather();
// 	PopulateMatchSettings();
// 	SetMatchLength();
// 
// 	//Set the default visibility of the slider info node.
// 	TeamSettingSelectionChanged();
// }
// 
// //OnWindowExit
// void UWWUIScreenOnlinePlaySettings::OnOutFocus(bool ShouldOutFocus)
// {
// 	UIClearPopulatedObject(OnlinePlaySettings.StadiumValueSetting);
// }
// 
// void UWWUIScreenOnlinePlaySettings::OnTimeout()
// {
// 
// }
// 
// bool UWWUIScreenOnlinePlaySettings::OnSystemEvent(WWUINodeProperty & eventProperty)
// {
// 	if (parameters.system_event == "on_disconnected")
// 	{
// 		UILaunchPopUpByName("LeaderboardsConnectionLost");
// 	}
// }

//replaces on action back
void UWWUIScreenOnlinePlaySettings::OnBack(APlayerController* controller)
{
// 	if (parameters.action_event == "ACTION_TYPE_ACTION")
// 	{
// 		local next_window_name = UINodeGetProperty(ui_object, "next_window");
// 		local next_window_object = UIGetNode("RootMenuWindow/" ..next_window_name);
// 		UINodeSetProperty(next_window_object, "is_quick_match", "false");
// 		UINodeSetProperty(next_window_object, "settings_object", "none");
// 		UINodeSetProperty(next_window_object, "search_ct_idx", parameters.controller_id);
// 
// 		//Need to make sure we save the settings.
// 		CommonSettings.SaveChanges(ui_object, parameters);
// 		OnlinePlaySettings.SaveSettings();
// 
// 		ProceedToNextWindow(ui_object, parameters);
// 		return true;
// 	}
// 	else if (parameters.action_event == "ACTION_TYPE_BACK")
// 	{
// 		ProceedToPreviousWindow(ui_object, parameters);
// 	}
// 	return false;.

	//	Mattt H - Most Likely need to add some networking re-initialising stuff here (leaving the screen and stopping a networked game (or something like that)).

	//Move back a screen
	if (URugbyGameInstance* gameInstance = SIFApplication::GetApplication())
	{
		gameInstance->DealMenuAction(ScreenAction::SCREEN_BACK_FADE, "");
	}
}

//Called when the a button is clicked. This is used to get around the fact that buttons consume the screen input
void UWWUIScreenOnlinePlaySettings::ExecuteTableFunction(FString InTableId, int InIdx, FString InAction, FString InActionString)
{
	if (URugbyGameInstance* gameInstance = SIFApplication::GetApplication())
	{
		OnSelect(gameInstance->GetPrimaryPlayerController());
	}
}

//replaces on action Select
void UWWUIScreenOnlinePlaySettings::OnSelect(APlayerController* controller)
{
	if (GetInputEnabled())
	{
		if (URugbyGameInstance* gameInstance = SIFApplication::GetApplication())
		{
			gameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::OnlineSearchResults);
		}
	}
}

//replaces on action Left
void UWWUIScreenOnlinePlaySettings::OnLeft(APlayerController* controller)
{
	ChangeSelectedOption(-1);
}

//replaces on action Right
void UWWUIScreenOnlinePlaySettings::OnRight(APlayerController* controller)
{
	ChangeSelectedOption(1);
}

void UWWUIScreenOnlinePlaySettings::ChangeSelectedOption(int32 InChangeDir)
{
	if (m_pMatchSettingsScrollBox)
	{
		int32 tempSelectedIndex = m_pMatchSettingsScrollBox->GetSelectedIndex();

		//Get focused widget
		UWidget* focus = GetIndexedListWidgetFromWidgetList(tempSelectedIndex);

		if (focus)
		{
			WWOption::UpdateOption(this, focus, optionValueMap, InChangeDir);
		}

		UWidget* tempListField = Cast<UWidget>(m_pMatchSettingsScrollBox->GetListField(tempSelectedIndex));

		if (tempSelectedIndex == OnlinePlaySettingsSettingss::OPSS_Stadium)
		{
			if (UWWUIUserWidget* tempUWWUIUserWidget = Cast<UWWUIUserWidget>(focus))
			{
				if (tempListField)
				{
					FOptionProperties* tempOptionProperty = optionValueMap.Find(tempListField);
					tempUWWUIUserWidget->SetProperty(ONLINE_PLAY_SETTINGS_STADIUM_PROPERTY_NAME, &m_aStadiumIds_list[tempOptionProperty->CurrentOptionValue], UIPropertyType::PROPERTY_TYPE_INT);
				}
			}

			StadiumSelectionChanged();
		}
		if (tempSelectedIndex == OnlinePlaySettingsSettingss::OPSS_BalancedTeams)
		{
			if (UWWUIUserWidget* tempUWWUIUserWidget = Cast<UWWUIUserWidget>(focus))
			{
				if (tempListField)
				{
					FOptionProperties* tempOptionProperty = optionValueMap.Find(tempListField);
					TeamSettingSelectionChanged(tempOptionProperty->CurrentOptionValue == 1 ? true : false);
				}
			}
		}
	}
}

void UWWUIScreenOnlinePlaySettings::StadiumSelectionChanged()
{
	if (m_pMatchSettingsScrollBox)
	{
		int32 tempSelectedIndex = m_pMatchSettingsScrollBox->GetSelectedIndex();

		UWidget* tempUserWidget = GetIndexedListWidgetFromWidgetList(tempSelectedIndex);

		if (tempUserWidget)
		{
			int32 tempSelectedStadium = 0;
			if (UWWUIUserWidget* tempWWUIUserWidget = Cast<UWWUIUserWidget>(tempUserWidget))
			{
				tempSelectedStadium = tempWWUIUserWidget->GetIntProperty(ONLINE_PLAY_SETTINGS_STADIUM_PROPERTY_NAME);
			}

			const RUDB_STADIUM* stadium;
			if (SIFApplication::GetApplication() && SIFApplication::GetApplication()->GetMatchStadiumManager())
			{
				if (UWWUIUserWidget* tempUWWUIUserWidget = Cast<UWWUIUserWidget>(tempUserWidget))
				{
					tempUWWUIUserWidget->SetProperty(ONLINE_PLAY_SETTINGS_STADIUM_PROPERTY_NAME, &m_aStadiumIds_list[0], UIPropertyType::PROPERTY_TYPE_INT);
				}

				stadium = SIFApplication::GetApplication()->GetMatchStadiumManager()->GetStadiumFromID(tempSelectedStadium);

				if (stadium)
				{
					FString name = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetStadiumRenderPath(stadium->GetDbId()));
					UTexture2D* tex = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name));

					//Set the stadium image
					UImage* stadiumImage = Cast<UImage>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenOnlinePlaySettings_UI::StadiumImage));
					if (stadiumImage)
					{
						stadiumImage->SetBrushFromTexture(tex);
					}
				}
			}
		}
	}
}

// void UWWUIScreenOnlinePlaySettings::SetMatchLength()
// {
// 	UWidget* match_length_node = UIGetNode(OnlinePlaySettings.MatchLengthValueSetting);
// 
// 	for (int32 i = 0, UINodeGetNumChildren(match_length_node) - 1)
// 	{
// 		UWidget* child = UINodeGetChildByIndex(match_length_node, i);
// 
// 		int32 game_mode = tonumber(UINodeGetProperty(child, "game_mode"));
// 
// 		if (game_mode == SIFGameHelpers::GAGetGameMode() || game_mode == 2)
// 		{
// 			child.visible = true;
// 			child.selectable = true;
// 			child.enabled = true;
// 		}
// 		else
// 		{
// 			child.visible = false;
// 			child.selectable = false;
// 			child.enabled = false;
// 		}
// 
// 		if (game_mode == SIFGlobal.GAME_MODE_SEVENS)
// 		{
// 			if (child.name == "14mins")
// 			{
// 				UINodeSelectNodeByIndex(match_length_node, i);
// 			}
// 		}
// 		else
// 		{
// 			if (child.name == "10mins")
// 			{
// 				UINodeSelectNodeByIndex(match_length_node, i);
// 			}
// 		}
// 	}
// }
// 
// void UWWUIScreenOnlinePlaySettings::PopulateMatchSettings()
// {
// 	UWidget* length_node = UINodeGetChild(ui_object, OnlinePlaySettings.MatchLengthValueSetting);
// 	int32 game_length = SIFGameHelpers::GAGetGameLength();
// 	SelectMatchOption(length_node, game_length);
// 	UpdateSelectionCounter(length_node, parameters);
// 
// 	UWidget* time_node = UINodeGetChild(ui_object, OnlinePlaySettings.TimeOfDayValueSetting);
// 	int32 time_of_day = SIFGameHelpers::GAGetTimeOfDay();
// 	OnlinePlaySettings.SelectMatchOption(time_node, time_of_day);
// 	UpdateSelectionCounter(time_node, parameters);
// 
// 	UWidget* conditions_node = UINodeGetChild(ui_object, OnlinePlaySettings.ConditionsValueSetting);
// 	int32 conditions = SIFGameHelpers::GAGetConditions();
// 	SelectMatchOption(conditions_node, conditions);
// 	UpdateSelectionCounter(time_node, parameters);
// 
// 	//We have to set in the current stadium ID so the populator knows which one to select.
// 	UWidget* stadium_id = SIFGameHelpers::GAGetStadium();
// 	int32 stadium_listbox = UINodeGetChild(ui_object, OnlinePlaySettings.StadiumValueSetting);
// 	UINodeSetProperty(stadium_listbox, "stadium_id", stadium_id);
// 
// 	//This will generate all the stadiums in the list.
// 	UIRefreshPopulatedObjectByPointerWithString(stadium_listbox, "include_random");
// }
// 
// void UWWUIScreenOnlinePlaySettings::SelectMatchOption()
// {
// 	for (int32 i = 0, (UINodeGetNumChildren(listbox) - 1), ++i)
// 	{
// 		int32 listbox_item = UINodeGetChildByIndex(listbox, i);
// 		if (UINodeGetProperty(listbox_item, "settings_value") == tostring(value))
// 		{
// 			UINodeSelectNode(listbox, listbox_item);
// 			return;
// 		}
// 	}
// }
// 
// void UWWUIScreenOnlinePlaySettings::SaveSettings()
// {
// 	int32 game_length = OnlinePlaySettings.GetSetting(UIGetNode(OnlinePlaySettings.MatchLengthValueSetting));
// 	if (game_length)
// 	{
// 		GASetGameLength(game_length);
// 	}
// 
// 	int32 time_of_day = OnlinePlaySettings.GetSetting(UIGetNode(OnlinePlaySettings.TimeOfDayValueSetting));
// 	int32 conditions = OnlinePlaySettings.GetSetting(UIGetNode(OnlinePlaySettings.ConditionsValueSetting));
// 	if (time_of_day && conditions)
// 	{
// 		GASetWeather(time_of_day, conditions);
// 	}
// 
// 	int32 stadium_listbox = UIGetNode(OnlinePlaySettings.StadiumValueSetting);
// 	int32 stadium_id = tonumber(UINodeGetProperty(UINodeGetSelectedNode(stadium_listbox), "stadium_id"));
// 	if (stadium_id)
// 	{
// 		UINodeSetProperty(stadium_listbox, "stadium_id", stadium_id);
// 		GASetStadium(stadium_id);
// 	}
// 
// 	// Should this online game use balanced team stats ?
// 	int32 balanced_teams = OnlinePlaySettings.GetSetting(UIGetNode(OnlinePlaySettings.TeamValueSettings));
// 	if (balanced_teams)
// 	{
// 		MMSetMatchBalancedStatsGameSettings(balanced_teams == "true");
// 	}
// }
// 
// FString UWWUIScreenOnlinePlaySettings::GetSetting()
// {
// 	UWidget* selected_item = UINodeGetSelectedNode(listbox);
// 	FString property_value = UINodeGetProperty(selected_item, "settings_value");
// 	if (property_value == "none")
// 	{
// 		return nil;
// 	}
// 
// 	return property_value;
// }

void UWWUIScreenOnlinePlaySettings::LoadStadiumsOptions()
{
	MabVector<const RUDB_STADIUM *> stadiumList;
	SIFApplication::GetApplication()->GetMatchStadiumManager()->GetExportedStadiumList(stadiumList);
	TArray<const RUDB_STADIUM *> stadiumArray;
	TArray<FText> StadiumNames;

	//Sort into array
	for (int32 i = 0; i < stadiumList.size(); i++)
	{
		stadiumArray.Add(stadiumList[i]);
	}

	stadiumArray.Sort([](const RUDB_STADIUM A, const RUDB_STADIUM B) { return A.GetName() < B.GetName(); } );

	for (int32 i = 0; i < stadiumList.size(); i++)
	{
		stadiumArray.Add(stadiumList[i]);
		int32 id = stadiumList[i]->GetDbId();
		StadiumNames.Add(FText::FromString(SIFGameHelpers::GAGetStadiumName(id).c_str()));
		m_aStadiumIds_list.Add(id);
	}

	//Create the names map
	UWidget* stadiumWidget = GetIndexedListWidgetFromWidgetList(OnlinePlaySettingsSettingss::OPSS_Stadium);
	if (stadiumWidget)
	{
		TArray<FValueIDBinding> bindings;
		for (int i = 0; i < StadiumNames.Num(); i++)
		{
			bindings.Add(FValueIDBinding(i, StadiumNames[i]));
		}
		FOptionProperties optionPropeties = FOptionProperties(OnlinePlaySettingsSettingss::OPSS_Stadium, bindings);
		optionValueMap.Add(stadiumWidget, optionPropeties);

		if (UWWUIUserWidget* tempUWWUIUserWidget = Cast<UWWUIUserWidget>(stadiumWidget))
		{
			tempUWWUIUserWidget->SetProperty(ONLINE_PLAY_SETTINGS_STADIUM_PROPERTY_NAME, &m_aStadiumIds_list[0], UIPropertyType::PROPERTY_TYPE_INT);
		}
	}

	StadiumSelectionChanged();
}

void UWWUIScreenOnlinePlaySettings::LoadMatchLengthOptions()
{
	TArray<FValueIDBinding> bindings;

	//Default to 15s time unless the game mode is 7s
	TArray<FValueTextBinding<int>> timeArray = FifteensTimes;

	// Nick  WWS 7s to Womens //
	/*
	if (SIFGameHelpers::GAGetGameMode() == 1)
	{
		timeArray = SevensTimes;
	}
	*/

	UWidget* matchLengthWidget = GetIndexedListWidgetFromWidgetList(OnlinePlaySettingsSettingss::OPSS_MatchLength);
	if (matchLengthWidget)
	{
		for (int i = 0; i < timeArray.Num(); i++)
		{
			bindings.Add(FValueIDBinding(i, timeArray[i].displayText));
		}
		FOptionProperties optionPropeties = FOptionProperties(OnlinePlaySettingsSettingss::OPSS_MatchLength, bindings);

		optionValueMap.Add(matchLengthWidget, optionPropeties);
	}
}

void UWWUIScreenOnlinePlaySettings::LoadTimeOfDayOptions()
{
	TArray<FValueIDBinding> bindings;

	UWidget* timeOfDayWidget = GetIndexedListWidgetFromWidgetList(OnlinePlaySettingsSettingss::OPSS_TimeOfDay);
	if (timeOfDayWidget)
	{
		for (int i = 0; i < TimeOfDayList.Num(); i++)
		{
			bindings.Add(FValueIDBinding(i, TimeOfDayList[i].displayText));
		}
		FOptionProperties optionPropeties = FOptionProperties(OnlinePlaySettingsSettingss::OPSS_TimeOfDay, bindings);

		optionValueMap.Add(timeOfDayWidget, optionPropeties);
	}
}

void UWWUIScreenOnlinePlaySettings::LoadConditionOptions()
{
	TArray<FValueIDBinding> bindings;

	UWidget* conditionsWidget = GetIndexedListWidgetFromWidgetList(OnlinePlaySettingsSettingss::OPSS_Conditions);
	if (conditionsWidget)
	{
		for (int i = 0; i < ConditionsList.Num(); i++)
		{
			bindings.Add(FValueIDBinding(i, ConditionsList[i].displayText));
		}
		FOptionProperties optionPropeties = FOptionProperties(OnlinePlaySettingsSettingss::OPSS_Conditions, bindings);

		optionValueMap.Add(conditionsWidget, optionPropeties);
	}
}

void UWWUIScreenOnlinePlaySettings::LoadBalancedTeamsOptions()
{
	TArray<FValueIDBinding> bindings;

	UWidget* timeOfDayWidget = GetIndexedListWidgetFromWidgetList(OnlinePlaySettingsSettingss::OPSS_BalancedTeams);
	if (timeOfDayWidget)
	{
		bindings.Add(FValueIDBinding(0, FText::FromString(UWWUITranslationManager::Translate(FString("[ID_NO]")))));
		bindings.Add(FValueIDBinding(1, FText::FromString(UWWUITranslationManager::Translate(FString("[ID_YES]")))));

		FOptionProperties optionPropeties = FOptionProperties(OnlinePlaySettingsSettingss::OPSS_BalancedTeams, bindings);

		optionValueMap.Add(timeOfDayWidget, optionPropeties);
	}

	TeamSettingSelectionChanged(false);
}

//	Mattt H - This is to skip adding the valid index check everywhere, when grabbing a widget from the list.
UWidget* UWWUIScreenOnlinePlaySettings::GetIndexedListWidgetFromWidgetList(int32 InIndex)
{
	if (m_aWidget_list.IsValidIndex(InIndex))
	{
		return m_aWidget_list[InIndex];
	}

	return nullptr;
}

//	Mattt H - This is to hide/show the "Gameplay slider settings are in effect" string based on if the teams are balanced are not (0 means hide).
void UWWUIScreenOnlinePlaySettings::TeamSettingSelectionChanged(bool IsTeamsBalanced)
{
	//	This needs to use the text of the gameplay slider strings, instead of the title slot.
	UWidget* tempWidget = UWWUIFunctionLibrary::FindWidget(this, WWUIScreenOnlinePlaySettings_UI::GamePlaySliderText);
	if (tempWidget)
	{
		UWWUIFunctionLibrary::SetVisibility(tempWidget, (IsTeamsBalanced ? ESlateVisibility::Hidden : ESlateVisibility::Visible));
	}
}

// //Loads All settings
// void UWWUIScreenOnlinePlaySettings::LoadSettingsWidgets()
// {
// 	URugbyGameInstance* gameInstance = GetWorld()->GetGameInstanceChecked<URugbyGameInstance>();
// 	if (gameInstance)
// 	{
// 		RUGameSettings* settings = gameInstance->GetMatchGameSettings();
// 		if (settings)
// 		{
// 			//Stadium name
// 			FString stadiumNameString = FString(SIFGameHelpers::GAGetStadiumName(SIFGameHelpers::GAGetStadium()).c_str());
// 			UWidget * widget = FindChildWidget(WWUIScreenMatchSettings_UI::Stadium);
// 			if (widget)
// 			{
// 				SetWidgetText(FindChildOfTemplateWidget(widget, SUBTITLENAME), FText::FromString(stadiumNameString).ToUpper());
// 				SetSelectionCount(widget);
// 				OnStadiumChanged();
// 			}
// 
// 			//Match Length
// 			FString matchLengthString = UWWUITranslationManager::Translate("[ID_GAME_LENGTH_" + FString::FromInt(SIFGameHelpers::GAGetGameLength()) + "_MINUTES]");
// 			widget = FindChildWidget(WWUIScreenMatchSettings_UI::MatchLength);
// 			if (widget)
// 			{
// 				SetWidgetText(FindChildOfTemplateWidget(widget, SUBTITLENAME), FText::FromString(matchLengthString).ToUpper());
// 				SetSelectionCount(widget);
// 			}
// 
// 			//Time of Day
// 			FString timeOfDayString = UWWUITranslationManager::Translate("[ID_TIMEOFDAY_" + FString(SIFGameHelpers::GAGetTimeOfDay()) + "]");
// 			widget = FindChildWidget(WWUIScreenMatchSettings_UI::TimeofDay);
// 			if (widget)
// 			{
// 				SetWidgetText(FindChildOfTemplateWidget(widget, SUBTITLENAME), FText::FromString(timeOfDayString).ToUpper());
// 				SetSelectionCount(widget);
// 			}
// 
// 			//Conditions
// 			FString conditionsString = UWWUITranslationManager::Translate("[ID_MATCH_SETTINGS_CONDITIONS_" + FString(SIFGameHelpers::GAGetConditions()) + "]");
// 			widget = FindChildWidget(WWUIScreenMatchSettings_UI::Conditions);
// 			if (widget)
// 			{
// 				SetWidgetText(FindChildOfTemplateWidget(widget, SUBTITLENAME), FText::FromString(conditionsString).ToUpper());
// 				SetSelectionCount(widget);
// 			}
// 
// 			//Law settings
// 			FString lawString;
// 			switch (SIFGameHelpers::GAGetGameLaw())
// 			{
// 				case GAME_LAW_NORMAL: lawString.Append(UWWUITranslationManager::Translate("[ID_LAW_VARIATION_0]"));
// 					break;
// 				case GAME_LAW_NRC: lawString.Append(UWWUITranslationManager::Translate("[ID_LAW_VARIATION_1]"));
// 					break;
// 			}
// 			widget = FindChildWidget(WWUIScreenMatchSettings_UI::LawVariation);
// 			if (widget)
// 			{
// 				SetWidgetText(FindChildOfTemplateWidget(widget, SUBTITLENAME), FText::FromString(lawString).ToUpper());
// 				SetSelectionCount(widget);
// 			}
// 		}
// 	}
// }

/*
function OnlinePlaySettings.OnAction(ui_object, parameters)
if (parameters.action_event == "ACTION_TYPE_ACTION") then
local next_window_name = UINodeGetProperty(ui_object, "next_window")
local next_window_object = UIGetNode("RootMenuWindow/" ..next_window_name)
UINodeSetProperty(next_window_object, "is_quick_match", "false")
UINodeSetProperty(next_window_object, "settings_object", "none")
UINodeSetProperty(next_window_object, "search_ct_idx", parameters.controller_id)

--Need to make sure we save the settings.
CommonSettings.SaveChanges(ui_object, parameters)
OnlinePlaySettings.SaveSettings()

ProceedToNextWindow(ui_object, parameters)
return true
elseif parameters.action_event == "ACTION_TYPE_BACK" then
ProceedToPreviousWindow(ui_object, parameters)
end
return false
end

function OnlinePlaySettings.StadiumSelectionChanged(ui_object, parameters)
--We need to update the display preview.
local selected_stadium = UINodeGetSelectedNode(ui_object)
if not selected_stadium then
return
end

local stadium_node = UIGetNode(OnlinePlaySettings.StadiumPreview)
if stadium_node then
local stadium_render = UINodeGetProperty(selected_stadium, "stadium_render")
stadium_node.streaming_texture = stadium_render
end
end

function OnlinePlaySettings.SetMatchLength()
local match_length_node = UIGetNode(OnlinePlaySettings.MatchLengthValueSetting);

for i = 0, UINodeGetNumChildren(match_length_node) - 1 do
local child = UINodeGetChildByIndex(match_length_node, i);

local game_mode = tonumber(UINodeGetProperty(child, "game_mode"));

if (game_mode == GAGetGameMode() or game_mode == 2) then
child.visible = true;
child.selectable = true;
child.enabled = true;
else
child.visible = false;
child.selectable = false;
child.enabled = false;
end

if (game_mode == SIFGlobal.GAME_MODE_SEVENS) then
if (child.name == "14mins") then
UINodeSelectNodeByIndex(match_length_node, i);
end
else
if (child.name == "10mins") then
UINodeSelectNodeByIndex(match_length_node, i);
end
end
end
end

function OnlinePlaySettings.PopulateMatchSettings(ui_object, parameters)
local length_node = UINodeGetChild(ui_object, OnlinePlaySettings.MatchLengthValueSetting)
local game_length = GAGetGameLength()
OnlinePlaySettings.SelectMatchOption(length_node, game_length)
UpdateSelectionCounter(length_node, parameters)

local time_node = UINodeGetChild(ui_object, OnlinePlaySettings.TimeOfDayValueSetting)
local time_of_day = GAGetTimeOfDay()
OnlinePlaySettings.SelectMatchOption(time_node, time_of_day)
UpdateSelectionCounter(time_node, parameters)

local conditions_node = UINodeGetChild(ui_object, OnlinePlaySettings.ConditionsValueSetting)
local conditions = GAGetConditions()
OnlinePlaySettings.SelectMatchOption(conditions_node, conditions)
UpdateSelectionCounter(time_node, parameters)

--We have to set in the current stadium ID so the populator knows which one to select.
local stadium_id = GAGetStadium()
local stadium_listbox = UINodeGetChild(ui_object, OnlinePlaySettings.StadiumValueSetting)
UINodeSetProperty(stadium_listbox, "stadium_id", stadium_id)

--This will generate all the stadiums in the list.
UIRefreshPopulatedObjectByPointerWithString(stadium_listbox, "include_random")
end

function OnlinePlaySettings.SelectMatchOption(listbox, value)
for i = 0, (UINodeGetNumChildren(listbox) - 1) do
local listbox_item = UINodeGetChildByIndex(listbox, i)
if UINodeGetProperty(listbox_item, "settings_value") == tostring(value) then
UINodeSelectNode(listbox, listbox_item)
return
end
end
end

function OnlinePlaySettings.SaveSettings()
local game_length = OnlinePlaySettings.GetSetting(UIGetNode(OnlinePlaySettings.MatchLengthValueSetting))
if game_length then
GASetGameLength(game_length)
end

local time_of_day = OnlinePlaySettings.GetSetting(UIGetNode(OnlinePlaySettings.TimeOfDayValueSetting))
local conditions = OnlinePlaySettings.GetSetting(UIGetNode(OnlinePlaySettings.ConditionsValueSetting))
if time_of_day and conditions then
GASetWeather(time_of_day, conditions)
end

local stadium_listbox = UIGetNode(OnlinePlaySettings.StadiumValueSetting)
local stadium_id = tonumber(UINodeGetProperty(UINodeGetSelectedNode(stadium_listbox), "stadium_id"))
if stadium_id then
UINodeSetProperty(stadium_listbox, "stadium_id", stadium_id)
GASetStadium(stadium_id)
end

-- Should this online game use balanced team stats ?
local balanced_teams = OnlinePlaySettings.GetSetting(UIGetNode(OnlinePlaySettings.TeamValueSettings))
if balanced_teams then
MMSetMatchBalancedStatsGameSettings(balanced_teams == "true")
end
end

function OnlinePlaySettings.GetSetting(listbox)
local selected_item = UINodeGetSelectedNode(listbox)
local property_value = UINodeGetProperty(selected_item, "settings_value")
if property_value == "none" then
return nil
end
return property_value
end

function OnlinePlaySettings.TeamSettingSelectionChanged(ui_object, parameters)
local slider_info_node = UIGetNode("SliderInfo")
slider_info_node.visible = UINodeGetSelectedNodeIndex(ui_object) == 0
end
*/
