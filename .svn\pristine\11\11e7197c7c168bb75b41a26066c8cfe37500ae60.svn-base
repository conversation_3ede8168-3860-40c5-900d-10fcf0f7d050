// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIScreenCareerHUB.h"
#include "WWUIStateScreenModal.h"
#include "Rugby/Utility/Helpers/SIFGameHelpers.h"

#include "Rugby/Match/RugbyUnion/CompetitionMode/RUActiveCompetition.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RUDBHelperInterface.h"

#include "Rugby/Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "Rugby/UI/Components/WWUIUserWidgetPopupComponentTrophy.h"

#include "Rugby/UI/Screens/WWUIScreenCareerTeamSquad.h"

#include "Rugby/UI/WWUICareerGlobal.h"
#include "Rugby/UI/Screens/Modals/WWUIModalNowPlayingAsTeam.h"
#include "Rugby/UI/Screens/Modals/WWUIModalWarningMessage.h"
#include "Rugby/UI/Screens/Modals/WWUIModalSimulateMatch.h"
#include "Rugby/UI/Screens/WWUIScreenCareerSaveAndQuit.h"
#include "Rugby/UI/Populators/WWUIPopulatorHubStandings.h"

#include "WWUITranslationManager.h"

#include "Rugby/UI/GeneratedHeaders/WWUIScreenCareerHUB_UI_Namespace.h"
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"

#include "WWUIFunctionLibrary.h"

#include "WidgetSwitcher.h"
#include "Border.h"
#include "HorizontalBox.h"
#include "Image.h"

#include "WWUIScrollBox.h"
#include "WWUITabSwitcher.h"
#include "WWUIListField.h"

#include "Rugby/RugbyGameInstance.h"
#include "Rugby/Databases/RUGameDatabaseManager.h"

#include "Utility/Helpers/SIFUIHelpers.h"

#include "WWUIRichTextBlockWithTranslate.h"

#include "SIFMissingControllerListener.h"
#include "SIFAchievementChecker.h"

#include "Rugby/UI/Screens/WWUIScreenCareerPlayerDrafting.h"
#include "Rugby/UI/Screens/WWUIScreenCareerMyContracts.h"
#include "Utility/Helpers/SIFRichPresenceHelpers.h"
#include "WWUIScreenControllerAssignment.h"
#include "Modals/WWUIModalWinningTeam.h"
#include "WWUIScreenCareerCompetitionStats.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
#include "WWUIScreenSquadPostMatch.h"


void UWWUIScreenCareerHUB::Startup(UWWUIStateScreenData* InData)
{
	alwaysAllowUpdate = true;

	DO_A_SAVE_PROPERTY_NAME = "do_competition_save";

	TeamDatabaseID = 0;	//setting with indata

	ProPlayerDatabaseID = 0;

	bRunCheckPopups = false;

	ResetSelection = true;

	MatchChanged = false;

	VisitedDrafting = false;

	ForceSimulate = false;

	SkippingYears = false;

	ShowingFinalResult = false;
	ShowingSatelliteEndRoundResult = false;

	HelpTextNode = nullptr;

	SelectedIndexStore = 0;

	CompetitionInfoMode = "Injury";

	HelpTextSuffix = "_NO_EXIT";

	IsSevens = false;
	CoachCareer = false;
	ViewProAccessories = false;

	RugbyWorldChapmionshipDatabaseID = 1001;

	FinalCupPopupDismissed = false;
	HasFinalPopupDisplayed = false;
	SatNum = -1;
	TrophyTypeID = -1;

	ExitDelay = false;
	SaveObject = nullptr;
	DoSaveProperty = 0;

	bReturningFromCareerPostMatch = false;
	EntryDelayTimer = 0;

	if (InData)
	{
		if (IsValid(InData))
		{
			UWWUIStateScreenCareerHUBData* pPassAroundData = Cast< UWWUIStateScreenCareerHUBData>(InData);

			if (pPassAroundData)
			{
				TeamDatabaseID = pPassAroundData->team_id;
			}
		}
	}

	// Get the career mode manager
	pRugbyGameInstance = SIFApplication::GetApplication();
	if (pRugbyGameInstance)
	{
		pCareerModeManager = pRugbyGameInstance->GetCareerModeManager();
	}

	if (!pCareerModeManager)
	{
		ensureMsgf(pCareerModeManager, TEXT("CareerSetup: Cannot get the career manager, this should not occur!"));
		return;
	}

	if (!pRugbyGameInstance)
	{
		ensureMsgf(pRugbyGameInstance, TEXT("CareerSetup: Cannot get the rugby game instance, this should not occur!"));
		return;
	}	

	pCareerModeManager->pScreenCareerHUB = this;

	if (pCareerModeManager->GetIsCareerModeCoach())
	{
		SIFGameHelpers::GASetIsAProMode(false);
	}
	else
	{
		ProPlayerDatabaseID = pCareerModeManager->GetProID();
		SIFGameHelpers::GASetIsAProMode(true);
	}

	if (pCareerModeManager->GetIsCareerGameModeR7())
	{
		SIFGameHelpers::GASetGameMode(GAME_MODE::GAME_MODE_RU13W); // Nick  WWS 7s to Womens // SEVENS);
	}
	else
	{
		SIFGameHelpers::GASetGameMode(GAME_MODE::GAME_MODE_RU13);
	}

#ifndef DEBUG_CAREER_HUB_LAUNCH_SCREEN_OPTIONS
	SetMenuOptionIsEnabled(9, false);
	SetMenuOptionIsEnabled(10, false);
	SetMenuOptionIsEnabled(11, false);
	SetMenuOptionIsEnabled(12, false);
	SetMenuOptionIsEnabled(13, false);
	SetMenuOptionIsEnabled(14, false);
	SetMenuOptionIsEnabled(15, false);
#endif

	if (pCareerModeManager->GetIsCareerModePro())
	{
		UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Pro career, turn on MyPro menu option."));

		SetMenuOptionIsEnabled(EMenuOption::MO_MY_PRO, true);
	}
	else
	{
		// Disable Be a Pro specific component
		SetMenuOptionIsEnabled(EMenuOption::MO_MY_PRO, false);
	}

	// Disable news and inbox depending on franchise type
	UWWUIScrollBox* pMenuScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerHUB_UI::ScrollBoxMenu));

	if (pMenuScrollBox)
	{
		UWWUIListField* pInboxWidget = pMenuScrollBox->GetListField((int)EMenuOption::MO_INBOX);

		UWidget* pNewsWidget = FindChildWidget(WWUIScreenCareerHUB_UI::HorizontalBoxNewsTicker);

		if (pInboxWidget && pNewsWidget)
		{
			if (pCareerModeManager->IsInFranchise())
			{
				pInboxWidget->SetIsEnabled(true);
				pInboxWidget->SetRenderOpacity(1.0f);

				//pNewsWidget->SetVisibility(ESlateVisibility::Visible);
			}
			else
			{
				pInboxWidget->SetIsEnabled(false);
				pInboxWidget->SetRenderOpacity(0.25f);
				pInboxWidget->SetVisibility(ESlateVisibility::Collapsed);

				pNewsWidget->SetVisibility(ESlateVisibility::Collapsed);
			}
		}
		else
		{
			ensure(pInboxWidget && pNewsWidget);
		}
	}

	// Update Rich Presence

	if (SIFGameHelpers::GAIsConsole())
	{
		SIFRichPresenceHelpers::RPSetPresenceMenus();
	}
}

void UWWUIScreenCareerHUB::Shutdown()
{
	UWWUIFunctionLibrary::StopTimer(regainControllerFocus);

	if (pCareerModeManager)
	{
		pCareerModeManager->pScreenCareerHUB = nullptr;
		pCareerModeManager->SetLastGameQuit(false);
		pCareerModeManager->SetReturningFromGame(false);
		pCareerModeManager = nullptr;
	}

	UWWUIFunctionLibrary::ClearAllFrameDelays();

}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::RegisterFunctions()
{
	AddInputAction("UI_Select", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::OnSelect)); // B
	AddInputAction("UI_Back", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::OnBack)); // B
	AddInputAction("RU_UI_ACTION_CAREERMODE_SIMULATE_MATCH", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::OnSimulateMatch));
	AddInputAction("RU_UI_ACTION_SAVE", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::OnSave));
	AddInputAction("RU_UI_ACTION_CAREER_SAVE_PRO_PLAYER", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::OnSavePro));


	AddToFunctionMap("PlayOptionOnClick", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::PlayMatchOptionOnClick));
	AddToFunctionMap("MySquadOptionOnClick", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::MySquadOptionOnClick));
	AddToFunctionMap("OpponentSquadOptionOnClick", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::OpponentSquadOptionOnClick));

	AddToFunctionMap("MyProOptionOnClick", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::MyProOptionOnClick));

#ifdef DEBUG_CAREER_HUB_LAUNCH_SCREEN_OPTIONS
	AddToFunctionMap("MyContractsOptionOnClick", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::DebugMyContractsOptionOnClick));
	AddToFunctionMap("DebugDraftClick", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::DebugDraftOnClick));
	AddToFunctionMap("DebugRecruitClick", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::DebugRecruitOnClick));
	AddToFunctionMap("DebugOfferClick", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::DebugOfferOnClick));
	AddToFunctionMap("DebugProPostClick", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::DebugProPostOnClick));
	AddToFunctionMap("DebugProPostPerfClick", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::DebugProPostPerfOnClick));
	AddToFunctionMap("DebugSquadPostMatch", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::DebugSquadPostMatch));
#endif

}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::TableOnSelectionChange(FString InTableId, int OldIdx, int NewIdx)
{

}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::ExecuteTableFunction(FString InTableId, int InIdx, FString InAction, FString InActionString)
{
#ifdef ENABLE_GAME_DEBUG_MENU
	if (!GetInputEnabled())
	{
		UE_LOG(LogTemp, Warning, TEXT("CareerHUB.ExecuteTableFunction, input is disabled"));
		if (!ForceSimulate)
		{
			return;
		}
	}

	if (ForceSimulate && !GetInputEnabled())
	{
		SetInputEnabled(true);
	}
#endif

	if (ShowingFinalResult)
	{
		OnSelect(GetMasterPlayerController());
		return;
	}

	Super::ExecuteTableFunction(InTableId, InIdx, InAction, InActionString);
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::PopupWarningIfTitlesUnassigned(uint32 TeamID)
{
	int PositionBitField = SIFGameHelpers::GACompetitionGetMissingTitledPlayers(TeamID);
	if (PositionBitField != 0)
	{
		FString UnassignedTitlePopupString = SIFGameHelpers::GAGetMissingTitledPlayerStringFromBitField(PositionBitField);
		LaunchSimpleSquadPopup(UnassignedTitlePopupString);
		return true;
	}
	else
	{
		return false;
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::OnWindowEnter()
{
	//double StartTime = FPlatformTime::Seconds();
	//int Counter = 0;
	//auto PrintTime = [StartTime, &Counter]() {	UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenCareerHUB::OnWindowEnter PrintTime %d: %f"), Counter++, FPlatformTime::Seconds() - StartTime); };
	//PrintTime();

	m_showingPostScreens = false;

	SetInputEnabled(false);

	UE_LOG(LogTemp, Warning, TEXT("======================== CareerHUB.OnWindowEnter ========================"));

	ViewProAccessories = false;

	SIFGameHelpers::GASyncProfileSettingsToGameSettings();

	SIFGameHelpers::GASetSandboxTickEnabled(true);

	// This was a fix for a lineup bug that happened after drafting
	if (VisitedDrafting)
	{
		uint32 PlayerTeamID = pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch();
		bool HasUnassignedPlayers = SIFGameHelpers::GACompetitionHasUnassignedPlayers(PlayerTeamID);

		if (HasUnassignedPlayers)
		{
			pCareerModeManager->AutoGenerateSquadLineup(PlayerTeamID);
			VisitedDrafting = false;
		}
	}

	ShowingSatelliteEndRoundResult = false;

	UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Set Date on Enter"));

	SetDate();

	if (ResetSelection)
	{
		ResetSelection = false;
		ResetSelectedMenuItem();
	}
	else
	{
		// Get the selected table element
		//UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Reselecting our node!"));
		// Set selection to invalid selection
		// Select the node again
		// Get the now selected node
		//UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Now it is:"));
	}

	// Refresh the standings and bled standings tables

	UpdatePoolTitle();

	UWWUIScrollBox* pStandingScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerHUB_UI::ScrollBoxStandings));

	if (pStandingScrollBox)
	{
		pStandingScrollBox->PopulateAndRefresh();
	}

	UWWUIScrollBox* pBledScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerHUB_UI::ScrollBoxBled));

	if (pBledScrollBox)
	{
		pBledScrollBox->PopulateAndRefresh();
	}

	SetGoalStandingsMode(GSM_STANDINGS);

	// Already visible in RC4
	// News ticker H box visible
	// Date visible
	// Comp Logo H Box visible


	uint32 MyTeamDatabaseID = pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch();

	UE_LOG(LogTemp, Warning, TEXT("CareerHUB.OnWindowEnter, GetCurrentPlayerTeamId() is: %d"), MyTeamDatabaseID);
	UE_LOG(LogTemp, Warning, TEXT("Local Team ID is: %d"), TeamDatabaseID);

	if (MyTeamDatabaseID == 0)
	{
		// No matches left with my team(career or competition is almost over!)
		MyTeamDatabaseID = pCareerModeManager->GetClubTeamID();	// Stop change team pop up
		TeamDatabaseID = MyTeamDatabaseID;

		UE_LOG(LogTemp, Warning, TEXT("CareerHUB.OnWindowEnter, no team, it is now set to my_team_db_id is: %d"), TeamDatabaseID);
	}

	UE_LOG(LogTemp, Warning, TEXT("CareerHUB.OnWindowEnter, primary team : %d"), pCareerModeManager->GetTeamID());

	UpdateProStartingLineupText(FindChildWidget(WWUIScreenCareerHUB_UI::OverlayProPlaying));

	UWWUIScrollBox* pMenuScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerHUB_UI::ScrollBoxMenu));

	UWidget* pNextMatchWidget = FindChildWidget(WWUIScreenCareerHUB_UI::VerticalBoxNextMatch);

	UWidget* pProNotPlayingWidget = FindChildWidget(WWUIScreenCareerHUB_UI::OverlayProPlaying);

	if (!pCareerModeManager->DoesNextMatchInvolvePlayersTeam())
	{
		UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Next match does not include our team."));

		if (pProNotPlayingWidget && pNextMatchWidget)
		{
			pNextMatchWidget->SetVisibility(ESlateVisibility::Collapsed);
			pProNotPlayingWidget->SetVisibility(ESlateVisibility::Collapsed);
		}

		SetMenuOptionText(EMenuOption::MO_PLAY_MATCH, UWWUITranslationManager::Translate("[ID_ADVANCE]").ToUpper());

		SetLegendText("[ID_CAREER_HUB_ADVANCE_HELP_TEXT]");

		//Message("1: "..tostring(help_string))

		SetMenuOptionIsEnabled(EMenuOption::MO_OPPONENTS_SQUAD, false);
	}
	else
	{
		// We entered the career HUB, and this is an active game
		UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Next match includes our team."));

		if (pNextMatchWidget)
		{
			pNextMatchWidget->SetVisibility(ESlateVisibility::Visible);
		}

		PopulateNextMatchDetails();
		PopulateOpponentDetails();

		// Checks pro mode, and if we're playing

		bool bForceAdvantageButton = false;

		if (pCareerModeManager->GetIsCareerModePro())
		{
			if (!SIFGameHelpers::GAGetIsTeamPlayerInStartingLineup(pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch(), pCareerModeManager->GetProID()))
			{
				UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Pro career, change PLAY MATCH -> ADVANCE."));
				bForceAdvantageButton = true;
			}
		}

		SetLegendText("[ID_CAREER_HUB_ADVANCE_HELP_TEXT]");

		if (bForceAdvantageButton)
		{
			SetMenuOptionText(EMenuOption::MO_PLAY_MATCH, UWWUITranslationManager::Translate("[ID_ADVANCE]").ToUpper());
		}
		else
		{
			SetMenuOptionText(EMenuOption::MO_PLAY_MATCH, UWWUITranslationManager::Translate("[ID_COMPETITION_PLAY_MATCH_MENU_ITEM]").ToUpper());

			if (pCareerModeManager->IsLionsTour())
			{
				SetLegendText("[ID_CAREER_HUB_NOSIMULATE_HELP_TEXT]");
			}
			else
			{
				SetLegendText("[ID_CAREER_HUB_SIMULATE_HELP_TEXT]");
			}
		}

		//Message("2: "..tostring(help_string))
		SetMenuOptionIsEnabled(EMenuOption::MO_OPPONENTS_SQUAD, true);

		//---- End match does not include players team.
	}

	// So we just came from the trophy screen (during satellite finals).
	if (bNextOnEnterShouldSim)
	{
		UE_LOG(LogTemp, Display, TEXT("CareerHUB: Next enter should sim RETURN."));

		UWWUIScreenManager* screen_manager = SIFApplication::GetApplication()->GetUIScreenManager();
		int32 simulateModalIndex = screen_manager ? screen_manager->FindScreenTemplate(Screens_UI::SimulateMatch) : -1;
		if (simulateModalIndex < 0)
		{
			UE_LOG(LogTemp, Warning, TEXT("Launching new Simulation Modal as a delayed action."));

			DelayedEntryAction([this]()
			{
				UE_LOG(LogTemp, Display, TEXT("Launching Simulation Match Popup: Next Enter Should Sim"));
				LaunchSimulateMatchPopup();
			});
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Skipping launching new Simulation Modal, there is already one on the stack."));
		}

		SimulateMatchPopup_bPreviousMatchPlayed = true;
		MatchChanged = true;
		bNextOnEnterShouldSim = false;
	}

	SetEmailIndicators();
	SetMySquadIndicators();
	SetDraftingMode();

	RUActiveCompetitionBase* pActiveCompetition = pCareerModeManager->GetActiveCompetition();
	if (pActiveCompetition && pActiveCompetition->IsCompetitionOver() && !pCareerModeManager->IsInFranchise())
	{
		SIFApplication::GetApplication()->GetAchievementChecker()->OnCompetitionOver(pActiveCompetition);
	}

	if (pCareerModeManager->IsInFranchise() && IsCurrentCareerCompComplete())
	{
		UE_LOG(LogTemp, Warning, TEXT("CareerHUB: End of Franchise detected."));
		CareerEndCurrentCompetition();
	}

	bool IsSatellite = false;

	if (pActiveCompetition)
	{
		IsSatellite = (pActiveCompetition->GetPreliminaryFormat() == 5);

		// Get the standings list box

		if (pActiveCompetition->GetDefinitionId() == RugbyWorldChapmionshipDatabaseID)
		{
			// Set the item count to 6
		}
		else
		{
			// Set the item count to 5
		}
	}

	bool IsFranchise = pCareerModeManager->IsInFranchise();

	bool CompOver = pCareerModeManager->IsCareerModeOver();

//SWITCH_TO_LOOK_AT - this crashes on switch, when looking at varArgs. Strange? Guess it's the fact it was missing TEXT("...")
	//UE_LOG(LogTemp, Warning, TEXT("CareerHUB: This is the final cup popup dismissed variable value: %s"), FinalCupPopupDismissed ? "True" : "False");
	UE_LOG(LogTemp, Warning, TEXT("CareerHUB: This is the final cup popup dismissed variable value: %s"), FinalCupPopupDismissed ? TEXT("True") : TEXT("False"));

	UE_LOG(LogTemp, Warning, TEXT("-----------------------------------------------------------------------------------------------------------------------------------------"));
	UE_LOG(LogTemp, Warning, TEXT("-----------------------------------------------------------------------------------------------------------------------------------------"));
	UE_LOG(LogTemp, Warning, TEXT("satellite: %s"), IsSatellite ? TEXT("True") : TEXT("False"));
	UE_LOG(LogTemp, Warning, TEXT("franchise: %s"), IsFranchise ? TEXT("True") : TEXT("False"));
	UE_LOG(LogTemp, Warning, TEXT("comp over: %s"), CompOver ? TEXT("True") : TEXT("False"));
	UE_LOG(LogTemp, Warning, TEXT("final cup dismissed: %s"), FinalCupPopupDismissed ? TEXT("True") : TEXT("False"));
	UE_LOG(LogTemp, Warning, TEXT("end of satellite: %s"), pCareerModeManager->IsEndOfSatellite() ? TEXT("True") : TEXT("False"));
	UE_LOG(LogTemp, Warning, TEXT("-------------------------------------------------------------"));
	UE_LOG(LogTemp, Warning, TEXT("final popup displayed %s"), HasFinalPopupDisplayed ? TEXT("True") : TEXT("False"));
	UE_LOG(LogTemp, Warning, TEXT("-----------------------------------------------------------------------------------------------------------------------------------------"));
	UE_LOG(LogTemp, Warning, TEXT("-----------------------------------------------------------------------------------------------------------------------------------------"));

	if (!IsSatellite)
	{
		UE_LOG(LogTemp, Warning, TEXT("CareerHUB: This is not a satellite"));
		FinalCupPopupDismissed = true;
		HasFinalPopupDisplayed = true;
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("CareerHUB: This is a satellite"));

		int satelliteCount = 1;

		if (pActiveCompetition)
		{
			satelliteCount = pActiveCompetition->GetPreliminaryAttrib1();
		}

		if (satelliteCount == 1 || TrophyTypeID == -1)
		{
			FinalCupPopupDismissed = true;
			HasFinalPopupDisplayed = true;
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Starting our different checks for career state."));

	// Super awesome weird logic.Careers and competitions run things completely differently, but use the same careerHUB
	// If final_cup_popup_dismissed is false, it ignores a massive chunk of code doing breadcrumbs, and other UI element settings
	// This is competition mode specifically only, so for franchise mode just go straight through.

	if (FinalCupPopupDismissed || IsFranchise)
	{
		UE_LOG(LogTemp, Warning, TEXT("CareerHUB: final_cup_popup_dismissed"));

		if ((!IsFranchise && CompOver) || (!IsFranchise && IsSatellite && pCareerModeManager->IsEndOfSatellite()))
		{
			// or CareerHUB.IsCurrentCareerCompComplete() then
			UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Not franchise, comp over, not satellite ||OR|| not franchise and is satellite end of satellite detected."));
			//UE_LOG(LogTemp, Warning, TEXT("CareerHUB.OnWindowEnter : Competition over, show Winner");
			if (HasFinalPopupDisplayed)
			{
				UE_LOG(LogTemp, Warning, TEXT("CareerHUB: has_final_popup_displayed"));
				//game is over!hide all the hub and show something else!

				// HideCareerHUB();
				{
					// Obsollete from widget switcher implementation
					//SetWidgetVisibility(pNextMatchWidget, ESlateVisibility::Collapsed);
					//SetWidgetVisibility(FindChildWidget(WWUIScreenCareerHUB_UI::OverlayChallengeDefence), ESlateVisibility::Collapsed);
					////final_result.visible = true;
					//SetWidgetVisibility(FindChildWidget(WWUIScreenCareerHUB_UI::VerticalBoxCareerComplete), ESlateVisibility::Visible);
					//SetWidgetVisibility(FindChildWidget(WWUIScreenCareerHUB_UI::ScrollBoxMenu), ESlateVisibility::Collapsed);
					//SetWidgetVisibility(FindChildWidget(WWUIScreenCareerHUB_UI::WidgetSwitcherStandingsGoals), ESlateVisibility::Collapsed);
					//SetWidgetVisibility(FindChildWidget(WWUIScreenCareerHUB_UI::HorizontalBoxNewsTicker), ESlateVisibility::Collapsed);
					//SetWidgetVisibility(FindChildWidget(WWUIScreenCareerHUB_UI::HeaderWidget), ESlateVisibility::Hidden);

					UWidgetSwitcher* pWidgetSwitcher = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenCareerHUB_UI::WidgetSwitcherMainAndFinalResult));
					if (pWidgetSwitcher)
					{
						pWidgetSwitcher->SetActiveWidgetIndex((int32)CareerEndingsSwitcherIndexes::COMPETITION_FINAL_RESULTS);
						HideHeader();
					}
					else { ensure(pWidgetSwitcher); }
				}

				ShowingFinalResult = true;

				uint32 CompetitionID = 0;

				if (pActiveCompetition)
				{
					CompetitionID = pActiveCompetition->GetDefinitionId();
				}

				uint32 WinningTeamID = SIFGameHelpers::GACompetitionGetWinningTeamId();

				UWWUIUserWidgetPopupComponentTrophy* pTrophyPanel = Cast< UWWUIUserWidgetPopupComponentTrophy>(FindChildWidget(WWUIScreenCareerHUB_UI::PopupFinalResultContent));
				if (pTrophyPanel)
				{
					pTrophyPanel->SetupCompEndPopup(WinningTeamID, CompetitionID, TeamDatabaseID);
				}
				else
				{
					ensure(pTrophyPanel);
				}

				UTextBlock* pFinalResultsWinText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerHUB_UI::TextFinalResultMessage));
				if (pFinalResultsWinText)
				{
					if (pCareerModeManager->IsTeamPlayerControlled(WinningTeamID))
					{
						pFinalResultsWinText->SetText(FText::FromString(UWWUITranslationManager::Translate("[ID_RESULTS_YOU_WON]")));
					}
					else
					{
						pFinalResultsWinText->SetText(FText::FromString(UWWUITranslationManager::Translate("[ID_RESULTS_YOU_LOST]")));
					}
				}
				else { ensure(pFinalResultsWinText); }

				UWidget* pCareerCompleteTitleWidget = FindChildWidget(WWUIScreenCareerHUB_UI::TextFiredRetiredHeading);

				if (pCareerCompleteTitleWidget)
				{
					SetWidgetText(pCareerCompleteTitleWidget, FText::FromString(UWWUITranslationManager::Translate("[ID_COMPETITION_FINAL_RESULT]").ToUpper()));
				}

				SetLegendText("[ID_COMP_HUB_COMP_OVER_HELP_TEXT]");

				UE_LOG(LogTemp, Warning, TEXT("Career HUB: 3:."));
				// The strips of the cinematic players must match the player's team.
				uint32 LastTeamID = pCareerModeManager->GetTeamIdForLastPlayerTeamMatch();

				if (pCareerModeManager->GetIsTeamPlayerControlled(WinningTeamID))
				{
					// Setup a breadcrumb
					SetBreadCrumbFromTeamID(WinningTeamID, true);
					UE_LOG(LogTemp, Warning, TEXT("[CareerHUB, comp over] Setting Team 0 and reloading strips for team 0"));
					SetTeamsAsync(WinningTeamID, DB_INVALID_ID, true);
					//SIFGameHelpers::GASetTeam(0, WinningTeamID);
					//SIFGameHelpers::GAStartSetTeamLikenessesToMatchGameSettings();
					SIFGameHelpers::GASetTeamStrip(WinningTeamID, 0);
				}
				else
				{
					// Setup a breadcrumb
					SetBreadCrumbFromTeamID(LastTeamID, true);
					UE_LOG(LogTemp, Warning, TEXT("[CareerHUB, comp over] Setting Team 0 and reloading both strips"));
					SetTeamsAsync(LastTeamID, DB_INVALID_ID, true);
					//SIFGameHelpers::GASetTeam(0, LastTeamID);
					//SIFGameHelpers::GAStartSetTeamLikenessesToMatchGameSettings();
					SIFGameHelpers::GASetTeamStrip(LastTeamID, 0);
				}

				TeamDatabaseID = MyTeamDatabaseID;	// Stop change team popup

			}
		}
		else if (pCareerModeManager->IsActive() && pCareerModeManager->IsFired())
		{
			UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Fired detected"));

			OnFired();
		}
		else if (pCareerModeManager->IsActive() && pCareerModeManager->IsCareerModeOver())
		{
			UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Career over"));

			// UE_LOG(LogTemp, Warning, TEXT("CareerHUB.OnWindowEnter : Career over: Show rating");

			//game is over!hide all the hub and show something else!

			//HideCareerHUB();
			{
				// Obsolete because we are now using a widget switcher
				//SetWidgetVisibility(pNextMatchWidget, ESlateVisibility::Collapsed);
				//SetWidgetVisibility(pProNotPlayingWidget, ESlateVisibility::Collapsed);
				//SetWidgetVisibility(FindChildWidget(WWUIScreenCareerHUB_UI::OverlayChallengeDefence), ESlateVisibility::Collapsed);
				////final_result.visible = false
				////final_result.selectable = false
				//SetWidgetVisibility(FindChildWidget(WWUIScreenCareerHUB_UI::VerticalBoxCareerComplete), ESlateVisibility::Visible);
				//SetWidgetVisibility(FindChildWidget(WWUIScreenCareerHUB_UI::ScrollBoxMenu), ESlateVisibility::Collapsed);
				//SetWidgetVisibility(FindChildWidget(WWUIScreenCareerHUB_UI::WidgetSwitcherStandingsGoals), ESlateVisibility::Collapsed);
				//SetWidgetVisibility(FindChildWidget(WWUIScreenCareerHUB_UI::HorizontalBoxNewsTicker), ESlateVisibility::Collapsed);
				//SetWidgetVisibility(FindChildWidget(WWUIScreenCareerHUB_UI::HeaderWidget), ESlateVisibility::Hidden);

				UWidgetSwitcher* pWidgetSwitcher = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenCareerHUB_UI::WidgetSwitcherMainAndFinalResult));
				if (pWidgetSwitcher)
				{
					pWidgetSwitcher->SetActiveWidgetIndex((int32)CareerEndingsSwitcherIndexes::FIRED_OR_RETIRED);
					HideHeader();
				}
				else { ensure(pWidgetSwitcher); }
			}
			ShowingFinalResult = true;

			uint32 LastTeamID = pCareerModeManager->GetTeamIdForLastPlayerTeamMatch();

			if (LastTeamID == 0)
			{
				LastTeamID = pCareerModeManager->GetClubTeamID();
			}

			SetBreadCrumbFromTeamID(LastTeamID, true);
			SetCompetitionLogo(LastTeamID, FindChildWidget(WWUIScreenCareerHUB_UI::ImageSecondaryCompLogo));
			UE_LOG(LogTemp, Warning, TEXT("[CareerHUB, career over] Setting Team 0 and reloading both strips"));
			SetTeamsAsync(LastTeamID, DB_INVALID_ID, false);
			//SIFGameHelpers::GASetTeam(0, LastTeamID);
			//SIFGameHelpers::GAStartSetTeamLikenessesToMatchGameSettings();
			//SIFGameHelpers::GASetTeamStripsToGameSettings(true);

			UWidgetSwitcher* pFiredRetiredSwitcher = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenCareerHUB_UI::WidgetSwitcherFiredRetired));
			if (pFiredRetiredSwitcher)
			{
				pFiredRetiredSwitcher->SetActiveWidgetIndex(1);
			}
			else
			{
				ensure(pFiredRetiredSwitcher);
			}

			UWidget* pCareerCompleteTitleWidget = FindChildWidget(WWUIScreenCareerHUB_UI::TextFiredRetiredHeading);

			if (pCareerCompleteTitleWidget)
			{
				SetWidgetText(pCareerCompleteTitleWidget, FText::FromString(UWWUITranslationManager::Translate("[ID_CAREER_COMPLETE_TAB_HEADING]").ToUpper()));
			}

			// Set stars...
			SetRatingStars(false);

			// Pro career we allow the user to save their pro playa as well.
			if (pCareerModeManager->GetIsCareerModePro()) //&& !IsCWS
			{
				// Legend for pro
				SetLegendText("[ID_COMP_HUB_COMP_OVER_PRO_HELP_TEXT]");
				UE_LOG(LogTemp, Warning, TEXT("4: "));
			}
			else
			{
				// Legend comp over no pro
				SetLegendText("[ID_COMP_HUB_COMP_OVER_HELP_TEXT]");
				UE_LOG(LogTemp, Warning, TEXT("5: "));
			}
		}
		else if (pCareerModeManager->GetPostCompetitionMode())
		{
			UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Post competition mode"));

			// show a different breadcrumb for career mode.
			SetBreadCrumbFromTeamID(MyTeamDatabaseID, true);
			SetCompetitionLogo(MyTeamDatabaseID, FindChildWidget(WWUIScreenCareerHUB_UI::ImageSecondaryCompLogo));
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Last else statement trying to work out the state of the career."));
			// UE_LOG(LogTemp, Warning, TEXT("CareerHUB.OnWindowEnter : Show next match info");

			bool UsePlayerCrumbs = true;
			if (pCareerModeManager->GetIsCareerModePro())
			{
				if (pCareerModeManager->IsEndOfSatelliteTrophyRound())
				{
					UsePlayerCrumbs = false;
				}
			}

			uint32 OppositionTeamDatabaseID = pCareerModeManager->GetPlayerControlledOppositionTeamIdOfNextMatch();

			if (OppositionTeamDatabaseID == 0)
			{
				OppositionTeamDatabaseID = pCareerModeManager->GetClubTeamID();
			}

			UE_LOG(LogTemp, Warning, TEXT("[CareerHUB, show next match info] Setting Team 0 to my team, team 1 to opp team, and reloading both strips"));

			SetTeamsAsync(MyTeamDatabaseID, OppositionTeamDatabaseID);

			//SIFGameHelpers::GASetTeamStripsToGameSettings(true);

			// Trigger async loading for the player faces
			//SIFGameHelpers::GARequestFaceTeamRender(0, MyTeamDatabaseID, true, false);
			//SIFGameHelpers::GARequestFaceTeamRender(1, OppositionTeamDatabaseID, true, false);

			// show a different breadcrumb for career mode.
			if (UsePlayerCrumbs)
			{
				// UE_LOG(LogTemp, Warning, TEXT("Using player crumbs");
				SetBreadCrumbFromTeamID(MyTeamDatabaseID, true);
				SetCompetitionLogo(MyTeamDatabaseID, FindChildWidget(WWUIScreenCareerHUB_UI::ImageSecondaryCompLogo));
			}
			else
			{
				// UE_LOG(LogTemp, Warning, TEXT("Setting up the crumbs based on the sat round/comp stuff");
				SetBreadcrumbFromSatelliteRound(pCareerModeManager->GetEndOfSatelliteTrophyID(), 12, true);
				SetCompetitionLogoFromCompetition(pCareerModeManager->GetEndOfSatelliteCompetitionID(), FindChildWidget(WWUIScreenCareerHUB_UI::ImageSecondaryCompLogo));
			}

			// Update ticker headlines
			PopulateTickerHeadlines();

			// ranfurly shield
			UTextBlock* pRanfurlyText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerHUB_UI::TextRanfurlyShieldChallenge));
			UWidget* pRanfurlyContainer = FindChildWidget(WWUIScreenCareerHUB_UI::OverlayChallengeDefence);
			if (pRanfurlyContainer && pRanfurlyText)
			{
				if (pCareerModeManager->IsActive() && pCareerModeManager->IsCurrentMatchRanfurlyShieldChallenge())
				{
					uint32 RanfurlyShieldHolder = pCareerModeManager->GetRanfurlyShieldHolder();							

					if (RanfurlyShieldHolder == MyTeamDatabaseID)
					{
						//ranfurly_shield_node.visible = true
						//UINodeGetChild(ranfurly_shield_node, "Challenge").visible = false
						//UINodeGetChild(ranfurly_shield_node, "Defence").visible = true
						pRanfurlyContainer->SetVisibility(ESlateVisibility::Visible);
						pRanfurlyText->SetText(FText::FromString(UWWUITranslationManager::Translate(FString("[ID_RANFURLY_SHIELD_DEFENCE]"))));
					}
					else if (RanfurlyShieldHolder == OppositionTeamDatabaseID)
					{
						/*ranfurly_shield_node.visible = true
						UINodeGetChild(ranfurly_shield_node, "Challenge").visible = true
						UINodeGetChild(ranfurly_shield_node, "Defence").visible = false*/
						pRanfurlyContainer->SetVisibility(ESlateVisibility::Visible);
						pRanfurlyText->SetText(FText::FromString(UWWUITranslationManager::Translate(FString("[ID_RANFURLY_SHIELD_CHALLENGE]"))));
					}
					else
					{
						// ranfurly_shield_node.visible = false
						pRanfurlyContainer->SetVisibility(ESlateVisibility::Collapsed);
					}
				
				}
				else
				{
					// ranfurly_shield_node.visible = false
					pRanfurlyContainer->SetVisibility(ESlateVisibility::Collapsed);
				}
			}
			else
			{
				ensure(pRanfurlyContainer && pRanfurlyText);
			}
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("CareerHUB: competition mode and NOT final_cup_popup_dismissed"));
		//We will hit this usually when we're playing a comp and it's a satellite tournament, so we need to reset the crumbs.
		bool UsePlayerCrumbs = true;
		if (pCareerModeManager->GetIsCareerModePro())
		{
			if (pCareerModeManager->IsEndOfSatelliteTrophyRound())
			{
				UsePlayerCrumbs = false;
			}
		}

		// show a different breadcrumb for career mode.
		if (UsePlayerCrumbs)
		{
			//UE_LOG(LogTemp, Warning, TEXT("Using player crumbs");
			SetBreadCrumbFromTeamID(MyTeamDatabaseID, true);
			SetCompetitionLogo(MyTeamDatabaseID, FindChildWidget(WWUIScreenCareerHUB_UI::ImageSecondaryCompLogo));
		}
		else
		{
			// UE_LOG(LogTemp, Warning, TEXT("Setting up the crumbs based on the sat round/comp stuff");
			SetBreadcrumbFromSatelliteRound(pCareerModeManager->GetEndOfSatelliteTrophyID(), 12, true);
			SetCompetitionLogoFromCompetition(pCareerModeManager->GetEndOfSatelliteCompetitionID(), FindChildWidget(WWUIScreenCareerHUB_UI::ImageSecondaryCompLogo));
		}

		SIFGameHelpers::GAStartSetTeamLikenessesToMatchGameSettings();
	}

	// Updates the heading text(the coloured bar top left)
	UpdateTitleText();

	SkippingYears = false;

	if (ForceSimulate)
	{
		// DEBUG VERSION ONLY : Advance or simulate...

		//UIExpireTimer("ForceAdvanceTimer")
		//UISetTimerForFrameCount("ForceAdvanceTimer", ui_object, 10)

		pCareerModeManager->SetDoneInitialDrafting(true);
		return;
	}

	if (SIFGameHelpers::GAGetIsAProMode())
	{
		SIFGameHelpers::GASetIsAProMode(1);
	}

	// Set focus back to the menu
	if (pMenuScrollBox && !ShowingFinalResult)
	{
		SetInitialFocus(pMenuScrollBox);
	}

	// Auto advance at start of career mode...

	if (pCareerModeManager->IsInFranchise() && !pCareerModeManager->GetDoneInitialDrafting())
	{
		UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Auto advance career for initial drafting."));
		pCareerModeManager->SetDoneInitialDrafting(true);

		// If the next game doesn't involve our team simulate.
		if (!pCareerModeManager->DoesNextMatchInvolvePlayersTeam())
		{
			DelayedEntryAction([this]()
			{
				// We can reset this here... since we aren't actually on a match yet that has our team in it.
				m_settingUpTeams = false;
				PlayMatchOptionOnClick();
			});
			return;
		}
	}


	// If career team has changed
	if (TeamDatabaseID != MyTeamDatabaseID)
	{
			UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Career team has chaned. Popup NowPlayingTeam."));

			TeamDatabaseID = MyTeamDatabaseID;

			DelayedEntryAction([=]()
			{

				if (!IsValidLowLevel())
				{
					UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenCareerHUB::OnWindowEnter UWWUIScreenCareerHUB was not valid"));
					return;
				}

			UWWUIModalNowPlayingAsTeamData* modalData = NewObject<UWWUIModalNowPlayingAsTeamData>();
			modalData->CompetitionInfo = WWUICareerGlobal::GetCompetitionInfo(TeamDatabaseID, true);
			modalData->TeamName = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetTeamShortName(TeamDatabaseID));
			modalData->pTeamLogo = LoadTeamLogo(TeamDatabaseID);

			RL3DB_TEAM PlayerTeam(TeamDatabaseID);
			MabColour LogoColour = PlayerTeam.GetPrimaryLogoColour();
			modalData->TeamColor = FLinearColor(LogoColour.r, LogoColour.g, LogoColour.b, 1.0f);

			UWidget* pTextBlock = FindChildWidget(WWUIScreenCareerHUB_UI::BottomLegendText);
			if (pTextBlock)
			{
				modalData->pLegendWidget = pTextBlock;
			}

			pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::ModalNowPlayingAsTeam, modalData);
		});
	}

	DelayedEntryAction([this]() {
		LastDelayedEntryAction(); });

	//---- End OnWindowEnter
}

//===============================================================================
//===============================================================================


void UWWUIScreenCareerHUB::OnWindowExit()
{
	// UIExpireTimer("CorrectVisibilityBug")
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::OnRevertSaveEventHelpTextChanges()
{
	//if (IsXbox360() and GAGetXBox360ContinueWithoutSavingProfile(UIGetCurrentMasterController())) then
	//	CareerHUB.HelpTextNode.text_string = RemoveSubstr(CareerHUB.HelpTextNode.text_string, ".[INP_GetAction(RU_UI_ACTION_SAVE)] SAVE", " ")
	//	end
	//	Message("6: "..tostring(CareerHUB.HelpTextNode.text_string))
	//	local output = string.find(CareerHUB.HelpTextNode.text_string, CareerHUB.HelpTextSuffix)
	//	if (output == nil) then
	//		Log("Unexpectedly, this help doesn't have NO_EXIT at the end of it!")
	//	else
	//		CareerHUB.HelpTextNode.text_string = string.gsub(CareerHUB.HelpTextNode.text_string, CareerHUB.HelpTextSuffix .. "%]", "]")
	//		Message("7: "..tostring(CareerHUB.HelpTextNode.text_string))
	//		end
}

//===============================================================================
//===============================================================================
//#define POST_MATCH_ATTRIBUTES
void UWWUIScreenCareerHUB::OnInFocus()
{
	m_doneFadeIn = false;

	if (pCareerModeManager->GetReturningFromGame() && !pCareerModeManager->GetLastGameQuit())
	{
#ifdef POST_MATCH_ATTRIBUTES
		if (!bReturningFromCareerPostMatch && pRugbyGameInstance)
		{
			bReturningFromCareerPostMatch = true;
			m_showingPostScreens = true;

			if (pCareerModeManager->GetIsCareerModePro())
			{
				DelayedEntryAction([=]() {
					pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::ProCareerPostMatch);
				});				
				return;
			}
			else
			{
				DelayedEntryAction([=]() {
					uint32 MyTeamDatabaseID = pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch();

					UE_LOG(LogTemp, Warning, TEXT("CareerHUB.OnInFocus, GetCurrentPlayerTeamId() is: %d"), MyTeamDatabaseID);
					UE_LOG(LogTemp, Warning, TEXT("Local Team ID is: %d"), TeamDatabaseID);

					if (MyTeamDatabaseID == 0)
					{
						// No matches left with my team(career or competition is almost over!)
						MyTeamDatabaseID = pCareerModeManager->GetClubTeamID();	// Stop change team pop up
						TeamDatabaseID = MyTeamDatabaseID;

						UE_LOG(LogTemp, Warning, TEXT("CareerHUB.OnInFocus, no team, it is now set to my_team_db_id is: %d"), TeamDatabaseID);
					}
					SetTeamsAsync(MyTeamDatabaseID, DB_INVALID_ID, true, true);
				});
				return;
			}
		}
#else
		if (pCareerModeManager->GetIsCareerModePro() && !bReturningFromCareerPostMatch)
		{
			// SIFGameHelpers::GASetMainMenuFlowScreen("ProCareerPostMatch");
			if (pRugbyGameInstance)
			{
				// Set this flag so when we return we do the stuff that was dealt by CompetitionSimulateMatch (which is the OnPlayedMatchFinished function below).
				bReturningFromCareerPostMatch = true;

				DelayedEntryAction([=]() {
					pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::ProCareerPostMatch);
				});
				m_showingPostScreens = true;
				return;
			}
		}
#endif
		else
		{
			// This function will handle our OnWindowEnter
			OnPlayedMatchFinished();
			return;
		}
	}

	if (SIFGameHelpers::GAIsConsole())
	{
		//Get current master controller
		int master_controller = SIFUIHelpers::GetCurrentMasterControllerIndex();

		if (master_controller == -1)
		{
			master_controller = 0;
			SIFApplication::GetApplication()->SetMasterControllerId(0);
		}

		//Tell the MCL to listen to the master controller only
		if (USIFMissingControllerListener * mcl = SIFApplication::GetApplication()->GetMissingControllerListener())
		{
			mcl->SetActive(true);
			mcl->SetControllerRelevant(master_controller, true);

			if (!mcl->IsControllerConnected(master_controller))
			{
				mcl->OnControllerDisconnected(master_controller);
			}
		}
	}

	OnRevertSaveEventHelpTextChanges();
	OnWindowEnter();

	EntryDelayTimer = 0;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::OnOutFocus(bool ShouldOutFocus)
{
	UWWUIFunctionLibrary::StopTimer(regainControllerFocus);
}

void UWWUIScreenCareerHUB::Update(float DeltaTime)
{
	if (!m_doneFadeIn && !m_showingPostScreens)
	{
		if (pRugbyGameInstance->GetMenuGameWorld()->GetCutSceneManager()->IsCinematicRunning())
		{
			pRugbyGameInstance->RequestTransitionHoldFinish(0.5f);
			m_doneFadeIn = true;
		}
	}
	
	if (bLaunchCompEndPopup && EntryDelayTimer == 0) // Only run if there are no pending delayed screens.
	{
		// Don't push on this popup if there are other active popups on top of it.
		bool bHasFocus = false;
		if (UWWUIScreenManager* pScreenManager = pRugbyGameInstance->GetUIScreenManager())
		{
			const int32 screenIdx = pScreenManager->FindScreenTemplate(Screens_UI::CareerHUB);
			const int32 screenCount = pScreenManager->GetScreenStackCount();

			bHasFocus = screenIdx == screenCount - 1;
		}

		if (bHasFocus)
		{
			bLaunchCompEndPopup = false;
			UWWUIScreenManager* pScreenManager = pRugbyGameInstance->GetUIScreenManager();
			if (pScreenManager)
			{
				if (pScreenManager->FindScreenTemplate(Screens_UI::ProCareerPostMatch) == -1 && pScreenManager->FindScreenTemplate(Screens_UI::ProCareerPostMatchPerf) == -1)
				{
					DeferedLaunchingCompEndPopup(LaunchCompEndPopup_winning_team_id, LaunchCompEndPopup_competition_id);
				}
			}
		}
	}

	if (!regainControllerFocus.IsValid())
	{
		RegainControllerFocus();
	}
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::OnSystemEvent(WWUINodeProperty& eventProperty)
{
	FString EventName = eventProperty.GetStringProperty("system_event");

	if (EventName == "simulate_popup_complete")
	{
		OnWindowEnter();
	}
	else if (EventName == "date_update")
	{
		SetDate();
	}
	else if (EventName == "CAREERHUB_FORCE_ADVANCE")
	{
		ForceSimulate = true;
	}
	else if (EventName == "CAREERHUB_STOP_FORCE_ADVANCE")
	{
		ForceSimulate = false;
	}

	return true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::TimerForceAdvanceTimer()
{
	if (pCareerModeManager->IsCareerModeOver())
	{
		CareerEndCurrentCompetition();
		return;
	}
	else
	{
		SkippingYears = true;

		if (!pCareerModeManager->DoesNextMatchInvolvePlayersTeam())
		{
			if (pCareerModeManager->CanPlayerAdvance())
			{
				SimulateMatchPopup_bPreviousMatchPlayed = true;
				UE_LOG(LogTemp, Display, TEXT("Launching Simulation Match Popup: Force Advance Timer"));
				LaunchSimulateMatchPopup();
				MatchChanged = true;
			}
			else
			{
				// just going to fake hitting the simulate button.
				// UISetCurrentTab("")
				SimulateMatchPopup_bDismissWithoutInput = true;
				SimulateMatchOnClick();
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::OnDismissTrophy()
{
	UE_LOG(LogTemp, Warning, TEXT("Dismissed the trophy popup"));

	if (SatNum == 8 && TrophyTypeID == 3)
	{
		FinalCupPopupDismissed = true;
	}

	if (pCareerModeManager->GetPostCompetitionMode())
	{
		UE_LOG(LogTemp, Warning, TEXT("It is post comp mode, don't sim when dismissing the trophy screen"));
		// -- CareerHUB.OnWindowEnter(ui_object, parameters)
		return;
	}

	if (pCareerModeManager->IsEndOfSatellite())
	{
		UE_LOG(LogTemp, Warning, TEXT("This is the last round of the satellite, and it's complete, so don't sim"));

		OnWindowEnter();
		return;
	}

	if (ShowingSatelliteEndRoundResult)
	{
		UE_LOG(LogTemp, Warning, TEXT("Dismissed the trophy popup 1"));
		UE_LOG(LogTemp, Warning, TEXT("We should be leaving this screen now and return to the game hub?"));

		// Remove those stupid things before we leave
		/*local final_result = UIGetNode("FinalResult")
		local career_complete = UIGetNode("CareerCompleteNew")*/
		// final_result.visible = false;
		// career_complete.visible = false;
		UWidgetSwitcher* pWidgetSwitcher = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenCareerHUB_UI::WidgetSwitcherMainAndFinalResult));
		if (pWidgetSwitcher)
		{
			pWidgetSwitcher->SetActiveWidgetIndex((int32)CareerEndingsSwitcherIndexes::CAREER_MAIN);
			HideHeader(true);
			ShowingFinalResult = false;
		}
		else { ensure(pWidgetSwitcher); }

		// So if this was an end of a normal trophy round, or end of a satellite, we sim to the next match.
		// Otherwise leave it on the advance screen so that we can still look at the stats etc.
		if (IsCurrentCareerCompComplete())
		{
			UE_LOG(LogTemp, Warning, TEXT("Career Comp is complete, we shouldn't sim to the next match"));
			bNextOnEnterShouldSim = false;
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Still playing the comp, sim to the next match."));
			bNextOnEnterShouldSim = true;
		}

		OnWindowEnter();
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::OnBack(APlayerController* OwningPlayer)
{
	if (!CanExit())
	{
		return;
	}

	QuitToMainMenu();
}

void UWWUIScreenCareerHUB::OnSelect(APlayerController* OwningPlayer)
{
	if (ShowingFinalResult)
	{
		//Message("On Action when showing the final result screen");
		pCareerModeManager->SetEndOfCareerStatsTeamIsInternational(false);

		UWWUIStateScreenCareerCompStatsData* pInData = NewObject< UWWUIStateScreenCareerCompStatsData>();
		pInData->bShowingFinalResults = ShowingFinalResult;

		pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CareerCompetitionStats, pInData);
	}
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::OnExitSaveConfirmed(APlayerController* OwningPlayer)
{
	ExitCompetitionConfirmed(true);
	return true;
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::OnExitNoSave(APlayerController* OwningPlayer)
{
	ExitCompetitionConfirmed(false);
	return true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SetTeamsAsync(uint32 PrimaryTeamID, uint32 SecondaryTeamID /*= DB_INVALID_ID*/, bool bSetStrips /*= false*/, bool bPostMatch /*= false*/)
{
	SIFUIHelpers::ShowLoadingPopup(true);
	m_settingUpTeams = true;
	// Wait a short time so last result is visible..
	UWWUIFunctionLibrary::OnFrameDelay(1, FTimerDelegate::CreateLambda([=]()
	{
		AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [=]()
		{
			if (pRugbyGameInstance)
			{
				pRugbyGameInstance->SetPauseSyncAsyncUpdate(true);
				// The players in the background needs to reflect the team we're currently playing as.
				// The team playing in the cinematic is taken from team index zero in the game settings.
				SIFGameHelpers::GASetTeam(0, PrimaryTeamID);

				if (SecondaryTeamID != DB_INVALID_ID)
				{
					SIFGameHelpers::GASetTeam(1, SecondaryTeamID);
				}

				////#rc4_no_likeness_comp_hub SIFGameHelpers::GAStartSetTeamLikenessesToMatchGameSettings();
				pRugbyGameInstance->SetPauseSyncAsyncUpdate(false);

				if (bSetStrips)
				{
					AsyncTask(ENamedThreads::GameThread, [=]()
					{
						SIFGameHelpers::GAStartSetTeamLikenessesToMatchGameSettings();
						SIFGameHelpers::GASetTeamStripsToGameSettings(true);
					});
				}
			}

			AsyncTask(ENamedThreads::GameThread, [=]()
			{
				// Finished setup so reset the flag.
				m_settingUpTeams = false;
				SIFUIHelpers::ShowLoadingPopup(false);

				if (bPostMatch)
				{
					ShowSquadPostMatch(nullptr);
				}
			});
		});
	}));
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::OnSave(APlayerController * OwningPlayer)
{
	//ManualCompetitionSave popup
	TArray<FModalButtonInfo> ButtonData;
	FWWUIModalDelegate SaveDelegate;
	SaveDelegate.BindUObject(this, &UWWUIScreenCareerHUB::DoManualSaveAccepted);
	ButtonData.Add(FModalButtonInfo(FText::FromString(UWWUITranslationManager::Translate("[ID_CUSTOMISE_PLAYER_BUTTON_SAVE]")), SaveDelegate));

	ButtonData.Add(FModalButtonInfo(FText::FromString(UWWUITranslationManager::Translate("[ID_CUSTOMISE_PLAYER_BUTTON_NO]")), FWWUIModalDelegate()));

	SIFUIHelpers::LaunchWarningPopup("[ID_CUSTOMISE_PLAYER_POPUP_MAIN_TEXT]", "[ID_POPUP_HELPTEXT_BACK]", ButtonData);
}

void UWWUIScreenCareerHUB::OnSavePro(APlayerController* OwningPlayer)
{
	if (ShowingFinalResult && pCareerModeManager->GetIsCareerModePro())
	{
		// CareerHubExitConfirmProSave
		TArray<FModalButtonInfo> ButtonData;
		FWWUIModalDelegate SaveDelegate;
		SaveDelegate.BindUObject(this, &UWWUIScreenCareerHUB::ExitSaveProPlayerConfirmed);
		ButtonData.Add(FModalButtonInfo("[ID_YES]", SaveDelegate));
		ButtonData.Add(FModalButtonInfo("[ID_NO]"));

		SIFUIHelpers::LaunchWarningPopup("[ID_COMP_HUB_CONFIRM_PRO_SAVE_TEXT]", "[ID_POPUP_HELPTEXT_BACK]", ButtonData);
	}
}

void UWWUIScreenCareerHUB::OnSimulateMatch(APlayerController* OwningPlayer)
{
	if (ShowingFinalResult) return;

	UE_LOG(LogTemp, Warning, TEXT("Trying to run the Simulate Action"));
	if (ShowingSatelliteEndRoundResult)
	{
		UE_LOG(LogTemp, Warning, TEXT("We're currently showing the end of round screen, so we probably wont be allowed to sim"));
	}

	// when playing pro career, we need to warn the user first to make sure they understand simulation will not progress their player.
	if (pCareerModeManager->GetIsCareerModePro() && !pCareerModeManager->GetPostCompetitionMode())
	{
		if (ForceSimulate)
		{
			OnConfirmSimulation(OwningPlayer);
		}
		else
		{
			TArray<FModalButtonInfo> ButtonData;

			ButtonData.Add(FModalButtonInfo("[ID_YES]", FWWUIModalDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::OnConfirmSimulation)));
			ButtonData.Add(FModalButtonInfo("[ID_NO]"));

			SIFUIHelpers::LaunchWarningPopup("[ID_CONFIRM_SIMULATION_HUB]", "[ID_SETTINGS_MENU_HELP]", ButtonData);
		}
	}
	else
	{
		if (pCareerModeManager->GetPostCompetitionMode())
		{
			ShowingSatelliteEndRoundResult = false;
		}

		OnConfirmSimulation(OwningPlayer);
	}
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::OnConfirmSimulation(APlayerController* OwningPlayer)
{
	if (!pCareerModeManager->IsCareerModeOver() && !ShowingSatelliteEndRoundResult)
	{
		if (pCareerModeManager->DoesNextMatchInvolvePlayersTeam())
		{
			// local is_lions_tour = CareerHUB.manager : IsToursMode()
			// if (not is_lions_tour) or (is_lions_tour and CareerHUB.manager:IsToursSimulateEnabled()) then
			//  just going to fake hitting the simulate button.

			if (pCareerModeManager->GetIsCareerModePro())
			{
				pCareerModeManager->OnProConfirmedSimulation();
			}

			SimulateMatchPopup_bDismissWithoutInput = false;
			SimulateMatchOnClick();
		}
		else
		{
			// Advance...
			PlayMatchOptionOnClick();
		}
	}

	return true;
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::DoManualSaveAccepted(APlayerController* OwningPlayer)
{
	//Message("CareerHUB.DoManualSaveAccepted");

	SIFUIHelpers::ShowSavingOverlay(true);


	pCareerModeManager->SaveCareerMode(true);
	return true;
}

//===============================================================================
//===============================================================================


void UWWUIScreenCareerHUB::PlayMatchOptionOnClick(APlayerController* OwningPlayer /*= nullptr*/)
{
	// Don't allow us to proceed if still setting up teams.
	if (m_settingUpTeams)
	{
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("CareerHUB.PlayMatchOptionOnClick"));
	if (!pCareerModeManager)
	{
		UE_LOG(LogTemp, Warning, TEXT("CareerHUB.PlayMatchOptionOnClick - pCareerModeManager was null"));
		return;
	}

	if (!IsValidLowLevel())
	{
		UE_LOG(LogTemp, Warning, TEXT("CareerHUB.PlayMatchOptionOnClick - UWWUIScreenCareerHUB was not valid"));
		return;
	}

	// When playing a pro career, check if our pro is in the current lineup.
	if (pCareerModeManager->GetIsCareerModePro())
	{
		if (pCareerModeManager->GetPostCompetitionMode())
		{
			pCareerModeManager->OnAdvanceEndOfComp();
		}

		if (SIFGameHelpers::GAGetIsTeamPlayerInStartingLineup(pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch(), pCareerModeManager->GetProID()))
		{
			UE_LOG(LogTemp, Warning, TEXT("Going to play game"));
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("we should sim this, since we're not in the lineup"));

			SimulateMatchPopup_bDismissWithoutInput = false;
			SimulateMatchOnClick();
			return;
		}
	}

	// RC2: 'Advance' has been pressed.
	if (!pCareerModeManager->DoesNextMatchInvolvePlayersTeam())
	{
		if (pCareerModeManager->CanPlayerAdvance())
		{
#ifdef ENABLE_ANALYTICS
			pCareerModeManager->RegisterCareerAdvanceRoundAnalytics("next match");
#endif
			SimulateMatchPopup_bPreviousMatchPlayed = true;
			UE_LOG(LogTemp, Display, TEXT("Launching Simulation Match Popup: Play Match Option (Advance) Clicked."));
			LaunchSimulateMatchPopup();
			MatchChanged = true;
			return;
		}
		else
		{
			UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();
			TArray<FModalButtonInfo> ButtonData;
			FString PopupString = "[ID_REP_NOT_ENOUGH_PLAYERS]";
			FString LegendString = "[ID_MAIN_MENU_HELP]";

			FWWUIModalDelegate DoNothingDelegate;
			ButtonData.Add(FModalButtonInfo(FText::FromString(UWWUITranslationManager::Translate("[ID_POPUP_OK]")), DoNothingDelegate));

			modalData->WarningDialogue = PopupString;
			modalData->LegendString = LegendString;
			modalData->ButtonData = ButtonData;

			modalData->CloseOnBackButton = true;
			modalData->CloseOnSelectButton = true;

			UWidget* pTextBlock = FindChildWidget(WWUIScreenCareerHUB_UI::BottomLegendText);
			if (pTextBlock)
			{
				modalData->pLegendWidget = pTextBlock;
			}

			if (pRugbyGameInstance)
			{
				pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
			}
			return;
		}
	}

	// This is the automated - call at the start of career.Already in correct place, so early
	// out to prevent badness caused by ProceedToNextWindow(ui_object, parameters).
	/*if ui_object == nil then
	return true
	end*/

	if (!ValidatePlayerTeamFirstXV())
	{
		return;
	}

	// We need to tell the assign controllers screen what text it should use for the breadcrumb
	// AssignControllers.MenuBreadcrumbText = "[ID_PLAY_MATCH]"
	BreadcrumbForControllerAssignment = "[ID_PLAY_MATCH]";

	// Setup next match
	RUDB_COMP_INST_MATCH* NextMatch = const_cast<RUDB_COMP_INST_MATCH*>(pCareerModeManager->GetNextMatch());

	if (!NextMatch)
	{
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("[CareerHUB, going into match] Setting team 0 to home, and team 1 to away"));
	SIFGameHelpers::GASetTeam(0, NextMatch->home_team_id);
	SIFGameHelpers::GASetTeam(1, NextMatch->away_team_id);
	SIFGameHelpers::GASetStadium(NextMatch->stadium_id);
	SIFGameHelpers::GACompetitionSetWeather(NextMatch);
	SIFGameHelpers::GASetDifficulty(pCareerModeManager->GetGameDifficulty());
	SIFGameHelpers::GASetGameLength(pCareerModeManager->GetGameLength());
	SIFGameHelpers::GASetSubstitutionMode(pCareerModeManager->GetSubstitutionMode());

	SIFGameHelpers::GACompetitionSetIsFinal();

	pCareerModeManager->SetLastGameQuit(false);
	pCareerModeManager->SetReturningFromGame(false);

	UWWUIControllerAssignmentInData* InData = NewObject< UWWUIControllerAssignmentInData>();

	// for pro games we want to show the goal progress first.
	if (pCareerModeManager->GetIsCareerModePro())
	{
		InData->GameMode = FString("BeAPro");
		SIFGameHelpers::GASetMainMenuFlowScreen("ProCareerPostMatch");
	}
	else
	{
		InData->GameMode = FString("Competition");
		SIFGameHelpers::GASetMainMenuFlowScreen("CompetitionSimulateMatch");
	}
	if (pRugbyGameInstance)
	{
		
		InData->BreadcrumbString = BreadcrumbForControllerAssignment;
		pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::AssignControllers, InData);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SimulateMatchOnClick()
{
	// if CareerHUB.is_sevens then
	// GASetGameMode(1);
	// end
	UE_LOG(LogTemp, Warning, TEXT("CareerHUB.SimulateMatchOnClick"));

	// early out if our current lineup is invalid
	if (!ValidatePlayerTeamFirstXV())
	{
		return;
	}

	UE_LOG(LogTemp, Display, TEXT("Launching Simulation Match Popup: Simulate match clicked."));
	LaunchSimulateMatchPopup();
	MatchChanged = true;
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::ValidatePlayerTeamFirstXV()
{
	// first check if this is a player match
	bool bIsPlayerMatch = pCareerModeManager->IsCurrentMatchPlayerMatch();
	if (!bIsPlayerMatch)
	{
		// teams are definitely valid if this isn't a player match!
		return true;
	}

	// because the invalid_players list only contains starting lineup, just check and see if its empty or not!

	// Message("GetCurrentPlayerTeamId 2");
	uint32 PlayerTeamID = pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch();

	if (PlayerTeamID == 0)
	{
		return false;
	}

	bool bHasUnassignedPlayers = SIFGameHelpers::GACompetitionHasUnassignedPlayers(PlayerTeamID);
	if (bHasUnassignedPlayers)
	{
		if (pCareerModeManager->GetAutoSquadSelection() == 1)
		{
			pCareerModeManager->AutoGenerateSquadLineup(PlayerTeamID);
		}
		else
		{
			//UILaunchPopUpByName("UnassignedPlayerInFirstFifteenPopup");
			TArray<FModalButtonInfo> ButtonData;

			FWWUIModalDelegate ExitDelegate;

			ExitDelegate.BindUObject(this, &UWWUIScreenCareerHUB::GotoMySquad);

			ButtonData.Add(FModalButtonInfo("[ID_POPUP_OK]", ExitDelegate));

			FString TitleString = "[ID_CAREER_HUB_UNASSIGNED_PLAYER]";
			FString LegendString = "[ID_MAIN_MENU_HELP]";

			SIFUIHelpers::LaunchWarningPopup(TitleString, LegendString, ButtonData, false, true);

			return false;
		}
	}

	MabString InvalidPlayers = SIFGameHelpers::GACompetitionGetInjuredOrSuspendedPlayers(PlayerTeamID);
	if (InvalidPlayers != "")
	{
		if (pCareerModeManager->GetAutoSquadSelection() == 1)
		{
			pCareerModeManager->AutoGenerateSquadLineup(PlayerTeamID);
		}
		else
		{
			LaunchInjuredOrSuspendedPopup();
			return false;
		}
	}


	bool bHasRequisitionedPlayers = SIFGameHelpers::GACompetitionHasRequisitionedPlayer(PlayerTeamID);

	if (bHasRequisitionedPlayers)
	{
		if (pCareerModeManager->GetAutoSquadSelection() == 1)
		{
			pCareerModeManager->AutoGenerateSquadLineup(PlayerTeamID);
		}
		else
		{
			LaunchInjuredOrSuspendedPopup();
			return false;
		}
	}

	// Ensure there is a kicker and captain in First XV
	if (PopupWarningIfTitlesUnassigned(PlayerTeamID))
	{
		if (pCareerModeManager->GetAutoSquadSelection() == 1)
		{
			pCareerModeManager->AutoGenerateSquadLineup(PlayerTeamID);
		}
		else
		{
			return false;
		}
	}

	return true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::MyProOptionOnClick(APlayerController* OwningPlayer)
{
	SIFGameHelpers::GARequestFaceTeamRender(0, pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch(), true, false);
	pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::CareerMyPro);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::InboxOptionOnClick()
{

}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::DraftingOptionOnClick()
{

}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::MySquadOptionOnClick(APlayerController* OwningPlayer)
{
	UE_LOG(LogTemp, Warning, TEXT("CareerHUB.MySquadOptionOnClick"));

	// Don't allow us to proceed if still setting up teams.
	if (m_settingUpTeams)
	{
		return;
	}

	uint32 TeamID = pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch();

	SIFGameHelpers::GARequestFaceTeamRender(0, TeamID, true, false);

	uint32 CompetitionID = pCareerModeManager->GetActiveCompetition()->GetInstanceId();
	// Message("GetCurrentPlayerTeamId 3");

	if (TeamID == 0 || CompetitionID == 0)
	{
		return;
	}

	UWWUIStateScreenCareerTeamSquadData* inData = NewObject<UWWUIStateScreenCareerTeamSquadData>();

	inData->CompetitionID = CompetitionID;
	inData->TeamID = TeamID;
	inData->AtlasID = 0; // For the faces
	inData->bReadOnly = false; // Since this is our team

	pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CareerTeamSquad, inData);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::ShowSquadPostMatch(APlayerController* OwningPlayer)
{
	UE_LOG(LogTemp, Warning, TEXT("CareerHUB.ShowSquadPostMatch"));

	uint32 TeamID = pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch();

	SIFGameHelpers::GARequestFaceTeamRender(0, TeamID, true, false);

	uint32 CompetitionID = pCareerModeManager->GetActiveCompetition()->GetInstanceId();
	
	if (TeamID == 0 || CompetitionID == 0)
	{
		return;
	}

	UWWUIStateScreenSquadPostMatchData* inData = NewObject<UWWUIStateScreenSquadPostMatchData>();

	inData->CompetitionID = CompetitionID;
	inData->TeamID = TeamID;
	inData->AtlasID = 0; // For the faces
	inData->bReadOnly = false; // Since this is our team

	pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::SquadPostMatch, inData);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::OpponentSquadOptionOnClick(APlayerController* OwningPlayer)
{
	UE_LOG(LogTemp, Warning, TEXT("CareerHUB.OpponentSquadOptionOnClick"));

	// Don't allow us to proceed if still setting up teams.
	if (m_settingUpTeams)
	{
		return;
	}

	uint32 TeamID = pCareerModeManager->GetPlayerControlledOppositionTeamIdOfNextMatch();

	SIFGameHelpers::GARequestFaceTeamRender(0, TeamID, true, false);

	uint32 CompetitionID = pCareerModeManager->GetActiveCompetition()->GetInstanceId();
	// Message("GetCurrentPlayerTeamId 3");

	if (TeamID == 0 || CompetitionID == 0)
	{
		return;
	}

	UWWUIStateScreenCareerTeamSquadData* inData = NewObject<UWWUIStateScreenCareerTeamSquadData>();

	inData->CompetitionID = CompetitionID;
	inData->TeamID = TeamID;
	inData->AtlasID = 0; // For the faces
	inData->bReadOnly = !SIFGameHelpers::GAIsPlayerTeamId(TeamID);

	pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CareerTeamSquad, inData);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SettingsOptionOnClick()
{

}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::CompetitionInfoOptionOnClick()
{

}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::StatisticsOptionOnClick()
{

}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::UpdateTitleText()
{
	uint32 TeamID = pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch();

	if (TeamID != 0)
	{
		UWidget* pTabSwitcher = FindChildWidget(WWUIScreenCareerHUB_UI::BP_TabCareerHUB);

		if (pTabSwitcher)
		{
			FString TeamNameString = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetTeamName(TeamID));
			
			UTextBlock* pTabText = Cast<UTextBlock>(FindChildOfTemplateWidget(pTabSwitcher, "Title"));

			if (pTabText)
			{
				pTabText->SetText(FText::FromString(TeamNameString).ToUpper());
			}

			UWidget* pRatingWidget = FindChildOfTemplateWidget(pTabSwitcher, WWUIScreenCareerHUB_UI::TextRating);

			if (pRatingWidget)
			{
				FString RatingString = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetTeamRating(TeamID));
				SetWidgetText(pRatingWidget, FText::FromString(RatingString));
			}
		}
	}
}

//===============================================================================
//===============================================================================
APlayerController* UWWUIScreenCareerHUB::GetMasterPlayerController()
{
	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		APlayerController* controller = m_gameInstance->GetMasterPlayerController();

		UWidget * widget = GetUserFocusedWidget(controller);

		if (controller == nullptr || !widget || !widget->IsVisible())
		{
			UWWUIFunctionLibrary::StopTimer(regainControllerFocus);
			regainControllerFocus = UWWUIFunctionLibrary::OnTimer(1.0f, FTimerDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::RegainControllerFocus));
		}

		return controller;
	}

	return nullptr;
}

//===============================================================================
//===============================================================================
void UWWUIScreenCareerHUB::RegainControllerFocus()
{
	if (!GetIsEnabled())
	{
		return;
	}

	URugbyGameInstance* m_gameInstance = SIFApplication::GetApplication();

	if (m_gameInstance)
	{
		APlayerController* controller = m_gameInstance->GetMasterPlayerController();

		UWidget * widget = GetUserFocusedWidget(controller);

		if (widget && widget->IsVisible())
		{
			return;
		}

		if (controller == nullptr)
		{
			UWWUIFunctionLibrary::StopTimer(regainControllerFocus);
			regainControllerFocus = UWWUIFunctionLibrary::OnTimer(1.0f, FTimerDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::RegainControllerFocus));
		}
		else
		{
			UWWUIScrollBox* pMenuScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerHUB_UI::ScrollBoxMenu));
			if (pMenuScrollBox)
			{
				SetInitialFocus(pMenuScrollBox);
			}
			else
			{
				return;
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::ExitCompetitionConfirmed(bool bDoSave)
{
	ResetSelection = true;
	MatchChanged = false;

	SIFGameHelpers::GASetSandboxTickEnabled(false);

	UWWUICareerSaveAndQuitScreenData* pInData = NewObject<UWWUICareerSaveAndQuitScreenData>();

	if (pInData)
	{
		pInData->bDoSave = bDoSave;
		pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CareerSaveAndQuit, pInData);
	}

	if (bDoSave) SIFUIHelpers::ShowSavingOverlay(true);

	if(USIFMissingControllerListener *  mcl = SIFApplication::GetApplication()->GetMissingControllerListener())
		mcl->ClearOldNotifications();
}



//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::ExitSaveProPlayerConfirmed(APlayerController* OwningPlayer)
{
	UE_LOG(LogTemp, Warning, TEXT("User confirmed that they would like to save their pro player. Prompt them to save their career."));

	pCareerModeManager->StoreProPlayerToSave();	// adding functionality to this in RC4, main menu will check if a save is stored.
	   
	QuitToMainMenu();
	return true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SetCompetitionLogo(uint32 TeamID, UWidget* pCompLogoWidget)
{
	uint32 CompetitionID = pCareerModeManager->GetNextPlayerCompDefId();

	SetCompetitionLogoFromCompetition(CompetitionID, pCompLogoWidget);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SetCompetitionLogoFromCompetition(uint32 CompetitionID, UWidget* pCompLogoWidget)
{
	if (CompetitionID != 0)
	{
		MabString MabCompetitionLogoPath = SIFGameHelpers::GAGetCompetitionIconTextureName(CompetitionID);

		UTexture2D* pTexture = nullptr;

		FString name = FString("/Game/Rugby/cmn_con/ui/Logos/league_logos/") + MabCompetitionLogoPath.c_str() + FString(".") + MabCompetitionLogoPath.c_str();
		pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name));

		if (pTexture)
		{
			UImage* pCompetitionLogoImage = Cast<UImage>(pCompLogoWidget);

			if (pCompetitionLogoImage)
			{
				pCompetitionLogoImage->SetVisibility(ESlateVisibility::Visible);
				pCompetitionLogoImage->SetBrushFromTexture(pTexture, true);
			}
		}
	}
	else
	{
		// Hide the logo
		if (pCompLogoWidget)
		{
			pCompLogoWidget->SetVisibility(ESlateVisibility::Hidden);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SetBreadCrumbFromTeamID(uint32 TeamID, bool SupressTeamName)
{
	UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Breadcrumb from team ID: %d."), TeamID);

	UWidget* pHeaderSubtitleWidget = FindChildWidget(WWUIScreenCareerHUB_UI::Subtitle);

	if (pHeaderSubtitleWidget)
	{
		WWUICareerGlobal::SetBreadCrumbFromTeamID(pHeaderSubtitleWidget, TeamID, SupressTeamName);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SetBreadcrumbFromSatelliteRound(int SatIndex, uint32 RoundIndex, bool SupressTeamName)
{
	UE_LOG(LogTemp, Warning, TEXT("CareerHUB: Breadcrumb from sat index: %d, round index: %d."), SatIndex, RoundIndex);

	UWidget* pHeaderSubtitleWidget = FindChildWidget(WWUIScreenCareerHUB_UI::Subtitle);

	if (pHeaderSubtitleWidget)
	{
		// -- breadcrumb_node.text_string = CareerHUB.GetCompetitionInfo(sat_index, suppress_team_name)
		// -- breadcrumb_node.text_string = "Bread From Sat: " ..tostring(sat_index) .. " round: " ..tostring(round_index);

		RUDBHelperInterface* pDatabaseHelper = pRugbyGameInstance->GetGameDBHelper();

		if (pDatabaseHelper)
		{
			RU_CompDefDB_Data* pCompDefinition = pDatabaseHelper->LoadCompDefDBData(pCareerModeManager->GetEndOfSatelliteCompetitionID());

			if (pCompDefinition)
			{
				//Active competition may have already moved on at this point.
				uint32 CompTrophyID = pCareerModeManager->GetEndOfSatelliteTrophyID();

				FString CompName = pCompDefinition->GetName();				//e.g.Sevens World Series

				// Censor PS4 #rc3_legacy_censor
				//if IsPS4() and GPS4IsUGCRestricted(-1) and comp_def : GetIsCustom() then
				//comp_name = "[ID_XBOX_CENSORED_NAME]";
				//}
				uint32 Year = pCareerModeManager->GetCurrentYear();		//e.g. 2014
				FString TrophyName = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetTrophyRealName(CompTrophyID)); //e.g.Gold Coast Bowl

				//FString replacements1 = "\"YEAR=%d\",\"COMP=%s\"";
				FString replacements2 = FString::Printf(TEXT("\"TROPHY=%s\""), *TrophyName);
				//FString popup_title1_str = "[ID_WIN_COMP_TEXT]";
				FString popup_title2_str = "[ID_WIN_TROPHY_TEXT]";
				//FString popup_text_str = SIFGameHelpers::GAGetTeamShortName(CareerHUB.manager:GetEndOfSatelliteTrophyRoundTrophyWinningTeam());

				// breadcrumb_node.text_string = pCareerModeManager->TranslateAndReplace(popup_title1_str, replacements1);
				MabString MabBreadCrumbString = pCareerModeManager->TranslateAndReplace(TCHAR_TO_UTF8(*popup_title2_str), TCHAR_TO_UTF8(*replacements2));

				SetWidgetText(pHeaderSubtitleWidget, FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(MabBreadCrumbString)).ToUpper());
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::CareerEndCurrentCompetition()
{
	pCareerModeManager->EndCurrentCompetition();

	// All the logic for updating the screen is handled in the OnWindowEnter function.
	// Calling it here will force the screen to update properly.
	OnWindowEnter();
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::CanExit()
{
	bool bCanExit = true;

	// We wouldn't be able to exit if we're in career and if we're currently viewing who won.
	// The database doesn't cope well with ending on a completed comp. User must move onto the next comp before they leave.
	if (IsCurrentCareerCompComplete())
	{
		UE_LOG(LogTemp, Warning, TEXT("CareerHUB.CanExit()  CANT EXIT!!! IsCurrentCareerCompComplete() == true"));
		bCanExit = false;
	}
	else if (ExitDelay)
	{
		UE_LOG(LogTemp, Warning, TEXT("CareerHUB.CanExit()  CANT EXIT!!!  exit_delay == true"));
		bCanExit = false;
	}

	return bCanExit;
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::IsCurrentCareerCompComplete()
{
	RUActiveCompetitionBase* pActiveCompetition = pCareerModeManager->GetActiveCompetition();

	if (pCareerModeManager->IsActive() && !pCareerModeManager->IsCareerModeOver())
	{
		if (!pActiveCompetition)
		{
			ensureMsgf(pActiveCompetition, TEXT("No active competition!"));
			return true;
		}
		return  pActiveCompetition->IsCompetitionOver();
	}

	return false;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SetDate()
{
	UWidget* pDateTextWidget = FindChildWidget(WWUIScreenCareerHUB_UI::TextHeaderDate);

	if (pDateTextWidget)
	{
		const MabDate* pCurrentDate = pCareerModeManager->GetCurrentDate();

		if (pCurrentDate)
		{
			SetWidgetText(pDateTextWidget, FText::FromString(FormatMabDate(pCurrentDate)));
		}
	}

	SetDraftingMode();
	SetMySquadIndicators();
	SetEmailIndicators();
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SetDraftingMode()
{
	SetMenuOptionIsEnabled(EMenuOption::MO_DRAFTING, pCareerModeManager->IsDraftingEnabled());
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SetMySquadIndicators()
{
	UWWUIScrollBox* pMenuScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerHUB_UI::ScrollBoxMenu));

	UWidget* pMySquadMenuField = nullptr;

	if (pMenuScrollBox)
	{
		pMySquadMenuField = pMenuScrollBox->GetListField((int32)EMenuOption::MO_MY_SQUAD);
	}

	UWidget* pMySquadInjuredWidget = nullptr;
	UWidget* pMySquadSuspendedWidget = nullptr;
	UWidget* pMySquadInternationalDutyWidget = nullptr;

	if (pMySquadMenuField)
	{
		pMySquadInjuredWidget = FindChildOfTemplateWidget(pMySquadMenuField, "ImageInjury");
		pMySquadSuspendedWidget = FindChildOfTemplateWidget(pMySquadMenuField, "ImageSuspension");
		pMySquadInternationalDutyWidget = FindChildOfTemplateWidget(pMySquadMenuField, "ImageInternational");
	}

	if (pMySquadInjuredWidget && pMySquadSuspendedWidget && pMySquadInternationalDutyWidget)
	{
		pMySquadInjuredWidget->SetVisibility(ESlateVisibility::Collapsed);
		pMySquadSuspendedWidget->SetVisibility(ESlateVisibility::Collapsed);
		pMySquadInternationalDutyWidget->SetVisibility(ESlateVisibility::Collapsed);
	}

	uint32 MyTeamDatabaseID = pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch();

	RUDBHelperInterface* pDatabaseHelper = pRugbyGameInstance->GetGameDBHelper();

	FString MySquadButtonText = UWWUITranslationManager::Translate("[ID_MY_SQUAD]");

	if (pDatabaseHelper && MyTeamDatabaseID != 0)
	{
		RU_TeamDB_Data* pTeamDatabaseData = pDatabaseHelper->LoadTeamDBData(MyTeamDatabaseID);

		if (pTeamDatabaseData)
		{
			if (pTeamDatabaseData->HasInjuries())
			{
				if (pMySquadInjuredWidget)
				{
					pMySquadInjuredWidget->SetVisibility(ESlateVisibility::Visible);
				}
			}

			if (pTeamDatabaseData->HasSuspensions())
			{
				if (pMySquadSuspendedWidget)
				{
					pMySquadSuspendedWidget->SetVisibility(ESlateVisibility::Visible);
				}
			}

			if (pTeamDatabaseData->HasPlayersGoneInternational())
			{
				if (pMySquadInternationalDutyWidget)
				{
					pMySquadInternationalDutyWidget->SetVisibility(ESlateVisibility::Visible);
				}
			}
		}
	}

	SetMenuOptionText(EMenuOption::MO_MY_SQUAD, MySquadButtonText);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SetContractIndicators()
{
	// This function was never called in RL3 so have not implemented the LUA - Joseph. G

	//local menu_options = UIGetNode(CareerHUB.MENU_OPTIONS_NODE)

	//	--Set unread email count
	//	local num_unread_contracts = CareerHUB.manager:GetNumUnreadContracts()
	//	local inbox_node = UINodeGetChild(menu_options, "MyContracts")
	//	local inbox_text_node = UINodeGetChild(inbox_node, "Text")
	//	local contract_text = nil

	//	if num_unread_contracts~= 0 then
	//		contract_text = "[ID_MY_CONTRACTS] [ID_NEW_MAIL_INDICATOR] "..tostring(num_unread_contracts)
	//	else
	//		contract_text = "[ID_MY_CONTRACTS]"
	//		end

	//		UITextSetText(inbox_text_node, contract_text)

	//		local selected_node = UINodeGetSelectedNode(menu_options)
	//		if UINodeGetName(selected_node) == "MyContracts" then
	//			local menu_node = UIGetNode(CareerHUB.MENU_NODE)
	//			local selected_text = UINodeGetChild(menu_node, "SelectedText")
	//			UITextSetText(selected_text, contract_text)
	//			end
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SetEmailIndicators()
{
	FString EmailText = "[ID_INBOX]";

	uint32 NumEmails = pCareerModeManager->GetNumUnreadEmails();

	UWWUIScrollBox* pMenuScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerHUB_UI::ScrollBoxMenu));

	UWidget* pEmailMenuField = nullptr;

	if (pMenuScrollBox)
	{
		pEmailMenuField = pMenuScrollBox->GetListField((int32)EMenuOption::MO_INBOX);
	}

	UWidget* pProPlayerInjuredWidget = nullptr;

	if (pEmailMenuField)
	{
		pProPlayerInjuredWidget = FindChildOfTemplateWidget(pEmailMenuField, "ImageMail");
	}

	if (pProPlayerInjuredWidget)
	{
		pProPlayerInjuredWidget->SetVisibility((NumEmails > 0) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}

	UTextBlock* pTextMailCount = Cast<UTextBlock>(FindChildOfTemplateWidget(pEmailMenuField, "TextMailCount"));
	if (pTextMailCount)
	{
		pTextMailCount->SetVisibility((NumEmails > 0) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		pTextMailCount->SetText(FText::FromString(FString::FromInt(NumEmails)));
	}
	else
	{
		ensure(pTextMailCount);
	}

	SetMenuOptionText(EMenuOption::MO_INBOX, UWWUITranslationManager::Translate(EmailText));
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::OnFired()
{
	ShowingFinalResult = true;

	//game is over!hide all the hub and show something else!

	UWidgetSwitcher* pWidgetSwitcher = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenCareerHUB_UI::WidgetSwitcherMainAndFinalResult));
	if (pWidgetSwitcher)
	{
		pWidgetSwitcher->SetActiveWidgetIndex((int32)CareerEndingsSwitcherIndexes::FIRED_OR_RETIRED);
		HideHeader();
	}
	else { ensure(pWidgetSwitcher); }

	// shows fired text by default

	// Set stars...
	SetRatingStars(true);

	if (pCareerModeManager->GetIsCareerModePro())
	{
		SetLegendText("[ID_COMP_HUB_COMP_OVER_PRO_HELP_TEXT]");
	}
	else
	{
		SetLegendText("[ID_COMP_HUB_COMP_OVER_HELP_TEXT]");
	}
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::OnFranchiseEnd(APlayerController* OwningPlayer)
{
	//ProceedToWindowForward("CareerPlayerDrafting")

	if (pRugbyGameInstance)
	{
		UWWUIStateScreenPlayerDraftingData* pInData = NewObject<UWWUIStateScreenPlayerDraftingData>();

		if (pInData)
		{
			pInData->is_rep_selection = true;
			pInData->is_customisation = false;
		}

		pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CareerPlayerDrafting, pInData);
	}

	return false;
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::OnRepSelection(APlayerController* OwningPlayer)
{
	//ProceedToWindowForward("CareerPlayerDrafting")

	if (pRugbyGameInstance)
	{
		UWWUIStateScreenPlayerDraftingData* pInData = NewObject<UWWUIStateScreenPlayerDraftingData>();

		if (pInData)
		{
			pInData->is_rep_selection = true;
			pInData->is_customisation = false;
		}

		pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::CareerPlayerDrafting, pInData);
	}


	return false;
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::OnFranchiseDrafting(APlayerController* OwningPlayer)
{
	//ProceedToWindowForward("CareerMyContracts")
	UWWUIStateScreenPlayerDraftingData* InData = NewObject<UWWUIStateScreenPlayerDraftingData>();
	InData->is_rep_selection = false;
	InData->is_customisation = false;
	pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CareerPlayerDrafting, InData);
	return false;
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::OnProClubDrafting(APlayerController* OwningPlayer)
{

	UE_LOG(LogTemp, Warning, TEXT("About to proceed to club team contracts"));

	//ProceedToWindowForward("CareerMyContracts")

	if (pRugbyGameInstance)
	{
		UWWUIStateScreenCareerMyContracts* pInData = NewObject<UWWUIStateScreenCareerMyContracts>();

		if (pInData)
		{
			pInData->bIsClubSelection = true;
			pInData->bIsInternationalSelection = false;
		}

		pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::CareerMyContracts, pInData);
	}
	return false;
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::OnProIntDraftingYes(APlayerController* OwningPlayer)
{
	OnProIntDrafting(true);
	return false;
}

bool UWWUIScreenCareerHUB::OnProIntDraftingNo(APlayerController* OwningPlayer)
{
	OnProIntDrafting(false);
	return false;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::OnProIntDrafting(bool bAccepted)
{
	if (bAccepted)
	{
		UE_LOG(LogTemp, Warning, TEXT("Accepted international selection"));
		pCareerModeManager->ProPlayerAcceptRepSelection();
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Declined international selection"));
		pCareerModeManager->ProPlayerDeclineRepSelection();
	}

	if (!pCareerModeManager->DoesNextMatchInvolvePlayersTeam())
	{
		if (pCareerModeManager->CanPlayerAdvance())
		{
			SimulateMatchPopup_bPreviousMatchPlayed = true;
			UE_LOG(LogTemp, Display, TEXT("Launching Simulation Match Popup: Pro international drafting."));
			LaunchSimulateMatchPopup();
			MatchChanged = true;
		}
		else
		{
			// UILaunchPopUpByName("NotEnoughPlayersInSquadPopup");
			TArray<FModalButtonInfo> Buttons;
			Buttons.Add(FModalButtonInfo("[ID_POPUP_OK]"));
			SIFUIHelpers::LaunchWarningPopup("[ID_REP_NOT_ENOUGH_PLAYERS]", "[ID_MAIN_MENU_HELP]", Buttons, false, true);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::OnPlayedMatchFinished()
{
	if (pCareerModeManager->GetLastGameQuit() == false)
	{
		if (pCareerModeManager->IsCareerModeOver())
		{
			// Don't try to simulate.
			if (pRugbyGameInstance)
			{
				// not sure why we were ever doing this. RC3 RUCareerModeManger seems to always cancel this screen anyway.
				// pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::CareerCompetitionInfo);

			}
			OnWindowEnter();	// need to call OnWindoEnter before leaving this function.
			return;
		}
		else
		{
			// Start automatic simulation if next match not the players... (If 'Advance' would be shown)

			if (!pCareerModeManager->DoesNextMatchInvolvePlayersTeam())
			{
				if (pCareerModeManager->CanPlayerAdvance())
				{
					SimulateMatchPopup_bPreviousMatchPlayed = true;
					DelayedEntryAction([this]() {
						UE_LOG(LogTemp, Display, TEXT("Launching Simulation Match Popup: Played Match Finished"));
						LaunchSimulateMatchPopup();
					});
					MatchChanged = true;
				}
				else
				{
					//UILaunchPopUpByName("NotEnoughPlayersInSquadPopup")
					TArray<FModalButtonInfo> Buttons;
					Buttons.Add(FModalButtonInfo("[ID_POPUP_OK]"));
					SIFUIHelpers::LaunchWarningPopup("[ID_REP_NOT_ENOUGH_PLAYERS]", "[ID_MAIN_MENU_HELP]", Buttons, false, true);
				}
			}
		}
	}


	// Stop swap teams popup showing.
	// Message("GetCurrentPlayerTeamId 7");
	TeamDatabaseID = pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch();
	ProPlayerDatabaseID = pCareerModeManager->GetProID();
	UE_LOG(LogTemp, Warning, TEXT("Career HUB: Match finished, check what our pro ID is: %d"), ProPlayerDatabaseID);

	OnWindowEnter();
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::UpdatePoolTitle()
{
	UWidget* pStandingsTitleWidget = FindChildWidget(WWUIScreenCareerHUB_UI::TextPool);

	if (pStandingsTitleWidget && pCareerModeManager)
	{
		int CompetitionDatabaseID = pCareerModeManager->GetPlayersNextCompetitionInstanceId();

		int GroupID = pCareerModeManager->GetNextPlayerCompetitionGroupId();

		FString GroupString = "";

		if (GroupID == -1)
		{
			GroupString = "[ID_COMP_STANDINGS_FULL_TABLE]";
		}
		else
		{
			GroupString = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GACompetitionGetPoolName(CompetitionDatabaseID, GroupID));
		}

		SetWidgetText(pStandingsTitleWidget, FText::FromString(UWWUITranslationManager::Translate(GroupString)));

		UWWUIScrollBox* pStandingScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerHUB_UI::ScrollBoxStandings));

		if (pStandingScrollBox)
		{
			UWWUIPopulatorHubStandings* pPopulatorHubStandings = Cast<UWWUIPopulatorHubStandings>(pStandingScrollBox->GetPopulator());
			if (pPopulatorHubStandings)
			{
				pPopulatorHubStandings->SetGroupID(GroupID);
			}
			else
			{
				ensure(pPopulatorHubStandings);
			}
		}
		else
		{
			ensure(pStandingScrollBox);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::LaunchSimpleSquadPopup(FString PopupString)
{
	UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();
	TArray<FModalButtonInfo> ButtonData;

	FWWUIModalDelegate ExitDelegate;

	ExitDelegate.BindUObject(this, &UWWUIScreenCareerHUB::GotoMySquad);

	ButtonData.Add(FModalButtonInfo("[ID_POPUP_OK]", ExitDelegate));

	FString LegendString = "[ID_MAIN_MENU_HELP]";

	modalData->WarningDialogue = PopupString;
	modalData->LegendString = LegendString;
	modalData->ButtonData = ButtonData;

	modalData->CloseOnBackButton = false;
	modalData->CloseOnSelectButton = true;

	UWidget* pTextBlock = FindChildWidget(WWUIScreenCareerHUB_UI::BottomLegendText);
	if (pTextBlock)
	{
		modalData->pLegendWidget = pTextBlock;
	}

	pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::LaunchSimulateMatchPopup()	//being called when entering career mode 
{
	UWWUIModalSimulateMatchData* modalData = NewObject<UWWUIModalSimulateMatchData>();

	// Create a delegate for dismissal of the popup and bind it to the on window enter function.
	FOnDismissPopup OnDismissDelegate;
	OnDismissDelegate.BindUObject(this, &UWWUIScreenCareerHUB::OnWindowEnter);

	// Copy the delegate to the in data.
	modalData->OnPopupDismissedDelegate = OnDismissDelegate;

	modalData->ForceSimulate = ForceSimulate;
	modalData->SkippingYears = SkippingYears;
	modalData->bPreviousMatchPlayed = SimulateMatchPopup_bPreviousMatchPlayed;
	modalData->bDismissWithoutInput = SimulateMatchPopup_bDismissWithoutInput;

	UWidget* pTextBlock = FindChildWidget(WWUIScreenCareerHUB_UI::BottomLegendText);
	if (pTextBlock)
	{
		modalData->pLegendWidget = pTextBlock;
	}

	pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::SimulateMatch, modalData);

	SimulateMatchPopup_bPreviousMatchPlayed = false;
	bRunCheckPopups = false;

#ifdef ENABLE_ANALYTICS
	if (pCareerModeManager)
	{
		pCareerModeManager->RegisterCareerAdvanceRoundAnalytics("next match");
	}
#endif
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::LaunchInjuredOrSuspendedPopup()
{
	LaunchSimpleSquadPopup("[ID_COMPETITION_HUB_INJURED_OR_SUSPENDED_PLAYER]");
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::LaunchNotEnoughPlayersPopup()
{
	LaunchSimpleSquadPopup("[ID_CAREER_HUB_NOT_ENOUGH_PLAYERS]");
}

void UWWUIScreenCareerHUB::LaunchUnassignedPlayerInFirstFifteenPopup()
{
	LaunchSimpleSquadPopup("[ID_CAREER_HUB_UNASSIGNED_PLAYER]");
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::LaunchingPromotionPopup(bool Promotion, ProRole Role)
{
	//Message("Launching promo popup");

	// --  Show popup 
	//auto popup_node = UILaunchPopUpByName("ProPlayerPromotion")

	FString MessageString = Promotion ? "[ID_PROMOTED]\n\n" : "[ID_DEMOTED]\n\n";

	switch (Role)
	{
		case ProRole::CAPTAIN:
		{
			MessageString += Promotion ? "[ID_PROMOTION_CAPTAIN]" : "[ID_DEMOTION_CAPTAIN]";
		}
		break;

		case ProRole::PLAY_KICKER:
		{
			MessageString += Promotion ? "[ID_PROMOTION_PLAY_KICKER]" : "[ID_DEMOTION_PLAY_KICKER]";
		}
		break;

		case ProRole::GOAL_KICKER:
		{
			MessageString += Promotion ? "[ID_PROMOTION_GOAL_KICKER]" : "[ID_DEMOTION_GOAL_KICKER]";
		}
		break;
	}
	//TArray<FModalButtonInfo> BtnInfo;
	//BtnInfo.Add(FModalButtonInfo("[ID_POPUP_BUTTON_TEXT_CONTINUE]", FWWUIModalDelegate::CreateUObject(this, &UWWUIScreenCareerHUB::OnDismissProPlayerPromotion)));
	//SIFUIHelpers::LaunchWarningPopup(MessageString, "[ID_INGAME_HALFTIME_HELP]", BtnInfo, false, true);
	SIFUIHelpers::LaunchWarningPopup(MessageString, "[ID_INGAME_HALFTIME_HELP]", nullptr, false, true);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::LaunchingCompEndPopup(int32 winning_team_id, int32 competition_id)
{
	//this flags the popup to be launched by Update() when career hub is ready. this was done so that this popup does not launch above pro post match screens.
	bLaunchCompEndPopup = true;
	LaunchCompEndPopup_winning_team_id = winning_team_id;
	LaunchCompEndPopup_competition_id = competition_id;
}

void UWWUIScreenCareerHUB::DeferedLaunchingCompEndPopup(int32 winning_team_id, int32 competition_id)
{
	// --  Show popup 
	//auto popup_node = UILaunchPopUpByName("EndOfComp")
	//CareerHUB.SetupCompEndPopup(popup_node, winning_team_id, competition_id)


	// This function is now only called once other popups have been dismissed so we can safely set the breadcrumb text without it interfering with the breadcrumb text of other popups.
	SetBreadCrumbFromTeamID(winning_team_id, true);

	// Removed delayed entry action since this function is only called during Update and is guarded with a check to ensure that there are no pending delayed actions.
	//DelayedEntryAction([=]() {
		UWWUIModalWinningTeamStateScreenData* InData = NewObject<UWWUIModalWinningTeamStateScreenData>();
		InData->winning_team_id = winning_team_id;
		InData->competition_id = competition_id;
		InData->team_db_id = TeamDatabaseID;
		InData->DismissTrophyPopupDelegate.BindUObject(this, &UWWUIScreenCareerHUB::OnDismissTrophy);

		UWidget* pTextBlock = FindChildWidget(WWUIScreenCareerHUB_UI::BottomLegendText);
		if (pTextBlock)
		{
			InData->pLegendWidget = pTextBlock;
		}

		if (pRugbyGameInstance)
		{
			pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WinningTeamPopup, InData);
		}
	//});
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::LaunchingCompEndSatellitePopup(int32 satelliteNum, SATELLITE_TROPHY trophy_type, int32 trophy_id, int32 winning_team_id, int32 competition_id)
{
	FinalCupPopupDismissed = false;

	SatNum = satelliteNum;

	TrophyTypeID = int(trophy_type);

	if (satelliteNum == 8 && trophy_type == 3)
	{
		//Message("Final popup displayer = "..tostring(CareerHUB.has_final_popup_displayed));
		HasFinalPopupDisplayed = true;
		//Message("Final popup displayer = "..tostring(CareerHUB.has_final_popup_displayed));
	}

	// --  Mmmm Breadcrumbs
	SetBreadcrumbFromSatelliteRound(trophy_id, 12, true);

	// --  Show popup 
	//auto popup_node = UILaunchPopUpByName("EndOfComp")

	DelayedEntryAction([=]() {
		UWWUIModalWinningTeamStateScreenData* InData = NewObject<UWWUIModalWinningTeamStateScreenData>();
		InData->satelliteNum = satelliteNum;
		InData->trophy_type = trophy_type;
		InData->winning_team_id = winning_team_id;
		InData->competition_id = competition_id;
		InData->DismissTrophyPopupDelegate.BindUObject(this, &UWWUIScreenCareerHUB::OnDismissTrophy);

		UWidget* pTextBlock = FindChildWidget(WWUIScreenCareerHUB_UI::BottomLegendText);
		if (pTextBlock)
		{
			InData->pLegendWidget = pTextBlock;
		}

		if (pRugbyGameInstance)
		{
			pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WinningTeamPopup, InData);
		}
	});

	ShowingSatelliteEndRoundResult = true;
}

//===============================================================================
//===============================================================================

//void UWWUIScreenCareerHUB::SetupSatelliteTrophyRoundEndPopup(UWidget* pModalWidget, int SatelliteNum, int TrophyType, uint32 WinningTeamID, int CompetitionID)
//{
	// now done in UWWUIUserWidgetPopupComponentTrophy::SetupSatelliteTrophyRoundEndPopup
//}


//===============================================================================
//===============================================================================

//void UWWUIScreenCareerHUB::SetupCompEndPopup(uint32 WinningTeamID, int32 competition_id)
//{
//	Moved to UWWUIUserWidgetPopupComponentTrophy::SetupCompEndPopup
//}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SetRatingStars(bool HasBeenFired)
{
	int Rating = SIFGameHelpers::GACareerGetRating();

	UTextBlock* pTextBlock = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerHUB_UI::TextFinalCareerRating));
	if (pTextBlock)
	{
		FString RatingStringKey = "[ID_COACH_RATING_" + FString::FromInt(Rating) + "]";
		pTextBlock->SetText(FText::FromString(UWWUITranslationManager::Translate(RatingStringKey)));
	}
	else
	{
		ensure(pTextBlock);
	}

	UHorizontalBox* pFinalCareerRatingHorizontalBox = Cast<UHorizontalBox>(FindChildWidget(WWUIScreenCareerHUB_UI::HorizontalBoxFinalCareerRating));

	if (pFinalCareerRatingHorizontalBox)
	{
		for (int i = 0; i < pFinalCareerRatingHorizontalBox->GetChildrenCount(); i++)
		{
			UImage* pStarImage = Cast<UImage>(pFinalCareerRatingHorizontalBox->GetChildAt(i));

			if (pStarImage)
			{
				if (i < Rating)
				{
					if (HasBeenFired)
					{
						pStarImage->SetColorAndOpacity(FLinearColor(0.822786f, 0.270498f, 0.040915f, 1.0f));
					}
					else
					{
						pStarImage->SetColorAndOpacity(FLinearColor(1.0f, 1.0f, 1.0f, 1.0f));
					}
				}
				else
				{
					pStarImage->SetColorAndOpacity(FLinearColor(0.045f, 0.045f, 0.045f, 0.7f));
				}
			}
			else
			{
				ensure(pStarImage);
			}
		}
	}
	else
	{
		ensure(pFinalCareerRatingHorizontalBox);
	}
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::GotoMySquad(APlayerController* OwningPlayer /*= nullptr*/)
{
	MySquadOptionOnClick(OwningPlayer);
	return true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::UpdateProStartingLineupText(UWidget* pProNotPlayingWidget)
{
	UWWUIScrollBox* pMenuScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerHUB_UI::ScrollBoxMenu));

	UWidget* pMyProMenuField = nullptr;

	if (pMenuScrollBox)
	{
		pMyProMenuField = pMenuScrollBox->GetListField((int32)EMenuOption::MO_MY_PRO);
	}

	UWidget* pHeaderWidget = nullptr;
	UWidget* pFifteensWidget = nullptr;

	UWidget* pProPlayerInjuredWidget = nullptr;
	UWidget* pProPlayerSuspendedWidget = nullptr;
	UWidget* pProPlayerInternationalDutyWidget = nullptr;
	UTextBlock* pProPlayerDurationText = nullptr;

	if (pMyProMenuField)
	{
		pProPlayerInjuredWidget = FindChildOfTemplateWidget(pMyProMenuField,"ImageInjury");
		pProPlayerSuspendedWidget = FindChildOfTemplateWidget(pMyProMenuField, "ImageSuspension");
		pProPlayerInternationalDutyWidget = FindChildOfTemplateWidget(pMyProMenuField, "ImageInternational");
		pProPlayerDurationText = Cast<UTextBlock>(FindChildOfTemplateWidget(pMyProMenuField, "DurationText"));
	}

	if (pProNotPlayingWidget)
	{
		pProNotPlayingWidget->SetVisibility(ESlateVisibility::Hidden);
	}

	if (pHeaderWidget)
	{
		pHeaderWidget->SetVisibility(ESlateVisibility::Hidden);
	}

	if (pProNotPlayingWidget && pProPlayerSuspendedWidget && pProPlayerInternationalDutyWidget && pProPlayerDurationText)
	{
		pProPlayerInjuredWidget->SetVisibility(ESlateVisibility::Hidden);
		pProPlayerSuspendedWidget->SetVisibility(ESlateVisibility::Hidden);
		pProPlayerInternationalDutyWidget->SetVisibility(ESlateVisibility::Hidden);
		pProPlayerDurationText->SetText(FText::FromString(""));
	}

	// Not playing pro mode, so leave now
	if (!pCareerModeManager->GetIsCareerModePro())
	{
		return; //false;
	}


	// We're in this lineup, so don't display the element
	if (SIFGameHelpers::GAGetIsTeamPlayerInStartingLineup(pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch(), pCareerModeManager->GetProID()))
	{
		UE_LOG(LogTemp, Warning, TEXT("CareerHUB.UpdateProStartingLineupText - Player is in the lineup"));

		if (pProNotPlayingWidget && pHeaderWidget)
		{
			pProNotPlayingWidget->SetVisibility(ESlateVisibility::Hidden);
			pHeaderWidget->SetVisibility(ESlateVisibility::Hidden);
		}
	}
	else
	{
		if (pProNotPlayingWidget && pHeaderWidget)
		{
			pProNotPlayingWidget->SetVisibility(ESlateVisibility::Visible);
			pHeaderWidget->SetVisibility(ESlateVisibility::Visible);
		}

		RUDBHelperInterface* pDatabaseHelper = pRugbyGameInstance->GetGameDBHelper();

		if (pDatabaseHelper)
		{
			RU_PlayerDB_Data* pPlayerDatabase = pDatabaseHelper->LoadPlayerDBData(pCareerModeManager->GetProID());

			if (pPlayerDatabase)
			{
				// Player is injured
				if (pPlayerDatabase->GetIsInjuredWrapper())
				{
					UE_LOG(LogTemp, Warning, TEXT("CareerHUB.UpdateProStartingLineupText - Player is not in the lineup. INJURED."));

					if (pProPlayerInjuredWidget)
					{
						pProPlayerInjuredWidget->SetVisibility(ESlateVisibility::Visible);
					}

					int32 num_days = pPlayerDatabase->GetNumDaysInjured(); //""

					if (pProPlayerDurationText)
					{
						pProPlayerDurationText->SetText(FText::FromString(UWWUITranslationManager::Translate(FString::FromInt(num_days) + " [ID_DAYS]")).ToUpper());
					}

					// Player is suspended
				}
				else if (pPlayerDatabase->GetIsSuspendedWrapper())
				{
					UE_LOG(LogTemp, Warning, TEXT("CareerHUB.UpdateProStartingLineupText - Player is not in the lineup. SUSPENDED."));

					if (pProPlayerSuspendedWidget)
					{
						pProPlayerSuspendedWidget->SetVisibility(ESlateVisibility::Visible);
					}

					int32 num_days = pPlayerDatabase->GetNumDaysSuspended();
					if (pProPlayerDurationText)
					{
						pProPlayerDurationText->SetText(FText::FromString(UWWUITranslationManager::Translate(FString::FromInt(num_days) + " [ID_DAYS]")).ToUpper());
					}
				}
				// Playing for international team
				else if (pPlayerDatabase->GetIsOnInternationalDuty())
				{
					UE_LOG(LogTemp, Warning, TEXT("CareerHUB.UpdateProStartingLineupText - Player is not in the lineup. INTERNATIONAL DOODY."));

					if (pProPlayerInternationalDutyWidget)
					{
						pProPlayerInternationalDutyWidget->SetVisibility(ESlateVisibility::Visible);
					}

					if (pProPlayerDurationText)
					{
						pProPlayerDurationText->SetText(FText::FromString(UWWUITranslationManager::Translate("[ID_ON_INTERNATIONAL_DUTY]")).ToUpper());
					}
				}
				//Player just isn't in the team.......
				else
				{
					UE_LOG(LogTemp, Warning, TEXT("CareerHUB.UpdateProStartingLineupText - Not in the lineup at all... Issue?"));

					if (pProNotPlayingWidget)
					{
						pProNotPlayingWidget->SetVisibility(ESlateVisibility::Visible);
					}

					if (pHeaderWidget)
					{
						pHeaderWidget->SetVisibility(ESlateVisibility::Visible);
					}
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::PopulateNextMatchDetails()
{
	RUActiveCompetitionBase* pActiveCompetition = pCareerModeManager->GetActiveCompetition();

	if (pActiveCompetition)
	{
		const RUDB_COMP_INST_MATCH* pNextMatch = pActiveCompetition->GetNextMatch();

		if (pNextMatch)
		{
			MabString MabRoundNameString = "";
			pActiveCompetition->GetRoundName(pNextMatch->GetRoundId(), MabRoundNameString);

			FString RoundNameString = SIFGameHelpers::GAConvertMabStringToFString(MabRoundNameString);

			UWidget* pRoundWidget = FindChildWidget(WWUIScreenCareerHUB_UI::TextMatchRound);

			if (pRoundWidget)
			{
				SetWidgetText(pRoundWidget, FText::FromString(RoundNameString));
			}

			MabDate NextMatchDate = pNextMatch->GetDate();

			UWidget* pDateWidget = FindChildWidget(WWUIScreenCareerHUB_UI::TextMatchDate);

			if (pDateWidget)
			{
				SetWidgetText(pDateWidget, FText::FromString(FormatMabDate(&NextMatchDate)));
			}

			RL3DB_STADIUM NextMatchStadium = pNextMatch->GetStadiumId();

			bool bValidStadium = pNextMatch->GetStadiumId() != SHRT_MAX && pNextMatch->GetStadiumId() != DB_INVALID_ID;

			ensure(bValidStadium);

			/*if(!bValidStadium)
			{
				RL3DB_TEAM homeTeam(pNextMatch->GetHomeTeamId());
				NextMatchStadium = homeTeam.GetHomeStadium();

				bValidStadium = homeTeam.GetHomeStadium() != SHRT_MAX && homeTeam.GetHomeStadium() != DB_INVALID_ID;

				ensure(bValidStadium);
			}

			const char* const NextMatchStadiumString = bValidStadium ? NextMatchStadium.GetName() : "";*/
			const char* const NextMatchStadiumString = bValidStadium ? NextMatchStadium.GetName() : "SHRT_MAX";

			UWidget* pLocationWidget = FindChildWidget(WWUIScreenCareerHUB_UI::TextMatchLocation);

			if (pLocationWidget)
			{
				SetWidgetText(pLocationWidget, FText::FromString(NextMatchStadiumString));
			}

			// Resolve locale keys:  [ID_AT_HOME_INDICATOR] #rc3_legacy_language
			FString HeadingText = "[ID_NEXT_MATCH] - " + RoundNameString;
			HeadingText = UWWUITranslationManager::Translate(HeadingText);

			UImage* pHomeWidget = Cast<UImage>(FindChildWidget(WWUIScreenCareerHUB_UI::ImageNextMatchHome));

			if (pHomeWidget)
			{
				pHomeWidget->SetVisibility((pCareerModeManager->IsTeamPlayerControlled(pNextMatch->GetHomeTeamId()) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed));
			}

			UWidget* pNextMatchTextWidget = FindChildWidget(WWUIScreenCareerHUB_UI::TextNextMatchTitle);

			if (pNextMatchTextWidget)
			{
				SetWidgetText(pNextMatchTextWidget, FText::FromString(HeadingText));
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::PopulateOpponentDetails()
{
	static const char NEXT_MATCH_OPPONENT_PARENT_NODE_NAME[] = "Opponent";
	static const char NEXT_MATCH_OPPONENT_TEAM_NAME_NODE_NAME[] = "TeamName";
	static const char NEXT_MATCH_USER_TEAM_NAME_NODE_NAME[] = "UserTeamName";
	static const char NEXT_MATCH_OPPONENT_TEAM_LOGO_NODE_NAME[] = "TeamLogo";
	static const char NEXT_MATCH_OPPONENT_RATING_VALUE_NODE_NAME[] = "Rating/Value";
	static const char TEAM_LOGO_PATH_NAME_PREFIX[] = "team_logo";
	static const char NEXT_MATCH_BACKGROUND[] = "Opponent/team_blur_bkgd";

	uint32 NextOpponentTeamID = pCareerModeManager->GetPlayerControlledOppositionTeamIdOfNextMatch();

	if (NextOpponentTeamID == 0)
	{
		return; // "Could not find a next match!";
	}

	uint32 NextUserTeamID = pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch();

	if (NextUserTeamID == 0)
	{
		return; // "Could not find a next match!";
	}

	RL3DB_TEAM UserTeam(NextUserTeamID);
	RL3DB_TEAM OpponentTeam(NextOpponentTeamID);

	// Set the team name.

	MabString MabOpponentTeamName = OpponentTeam.GetName();

	// Censor name for PS4 #rc3_legacy_censor
	RUHUDUpdater::CensorTeamName(OpponentTeam.GetDbId(), MabOpponentTeamName);

	UWidget* pOpponentTeamNameWidget = FindChildWidget(WWUIScreenCareerHUB_UI::TextOpponentTeamName);

	if (pOpponentTeamNameWidget)
	{
		SetWidgetText(pOpponentTeamNameWidget, FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(MabOpponentTeamName)));
	}

	// Set the user team name.

	MabString MabUserTeamName = UserTeam.GetName();

	// Censor name for PS4 #rc3_legacy_censor
	RUHUDUpdater::CensorTeamName(UserTeam.GetDbId(), MabUserTeamName);

	UWidget* pUserTeamNameWidget = FindChildWidget(WWUIScreenCareerHUB_UI::TextUserTeamName);

	if (pUserTeamNameWidget)
	{
		SetWidgetText(pUserTeamNameWidget, FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(MabUserTeamName)));
	}

	UTexture2D* pTeamTexture = LoadTeamLogo(NextOpponentTeamID);

	if (pTeamTexture)
	{
		UImage* pOpponentTeamLogo = Cast<UImage>(FindChildWidget(WWUIScreenCareerHUB_UI::ImageOpponentTeamLogo));

		if (pOpponentTeamLogo)
		{
			pOpponentTeamLogo->SetBrushFromTexture(pTeamTexture, true);
		}
	}

	UImage* opponentlogoBackground = Cast<UImage>(FindChildWidget(WWUIScreenCareerHUB_UI::teamBkgd));
	SIFUIHelpers::SetImageColourFromTeamColour(opponentlogoBackground, OpponentTeam);

	// Set the rating.

	const FString OpponentTeamRating = FString::Printf(TEXT("%0.2f"), OpponentTeam.GetNormalisedRanking() * 100.0f);
	
	UWidget* pOpponentTeamRatingWidget = FindChildWidget(WWUIScreenCareerHUB_UI::TextOpponentRating);

	if (pOpponentTeamRatingWidget)
	{
		SetWidgetText(pOpponentTeamRatingWidget, FText::FromString(OpponentTeamRating));
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::PopulateTickerHeadlines()
{
	const RUActiveCompetitionBase* const pActiveComp = pCareerModeManager->GetActiveCompetition();

	if (pActiveComp == NULL)
	{
		return;
	}

	if (pActiveComp->GetDefinitionId() == DB_INVALID_ID)
	{
		return;
	}

	if (pActiveComp->GetInstanceId() == DB_INVALID_ID)
	{
		return;
	}

	const RUGameDatabaseManager* const pGameDatabase = SIFApplication::GetApplication()->GetGameDatabaseManager();

	if (pGameDatabase == NULL)
	{
		return;
	}

	MabStringList::StringList MabNewsTickerStringList(SIFHEAP_DYNAMIC);
	MabString MabNewsTickerString(SIFHEAP_DYNAMIC);

	pActiveComp->GetTickerStrings(MabNewsTickerStringList);

	MabStringList::JoinList(MabNewsTickerStringList, MabNewsTickerString, "  /  ");

	FString NewsTickerString = SIFGameHelpers::GAConvertMabStringToFString(MabNewsTickerString);

	UWidget* pNewsTickerWidget = FindChildWidget(WWUIScreenCareerHUB_UI::TextNews);

	if (pNewsTickerWidget)
	{
		SetWidgetText(pNewsTickerWidget, FText::FromString(NewsTickerString).ToUpper());
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::ResetSelectedMenuItem()
{
	UWWUIScrollBox* pMenuScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerHUB_UI::ScrollBoxMenu));

	if (pMenuScrollBox)
	{
		pMenuScrollBox->SetSelectedIndex(0);
	}
}

//===============================================================================
//===============================================================================

UTexture2D* UWWUIScreenCareerHUB::LoadTeamLogo(uint32 TeamID)
{
	UTexture2D* pTexture = nullptr;

	if (TeamID != 0)
	{
		RUGameDatabaseManager* pDatabaseManager = pRugbyGameInstance->GetGameDatabaseManager();
		if (pDatabaseManager)
		{
			RUDB_TEAM_LITE DatabaseTeam;
			MABVERIFY(pDatabaseManager->LoadData(DatabaseTeam, TeamID));

			MabString pathString = SIFGameHelpers::GAGetTeamLogoAssetPath(DatabaseTeam.GetDbId());
			FString name = FString(pathString.c_str());
			pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name));
			//MABASSERT(tex);
		}
	}

	return pTexture;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SetGoalStandingsMode(GoalsStandingMode NewMode)
{
	UWidgetSwitcher* pGoalStandingSwitcher = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenCareerHUB_UI::WidgetSwitcherStandingsGoals));

	if (pGoalStandingSwitcher)
	{
		pGoalStandingSwitcher->SetActiveWidgetIndex((int32)NewMode);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SetInitialFocus(UWidget* pScrollBoxWidget)
{
	if (pScrollBoxWidget)
	{
		//Find the first element of the main menu and set focus
		UWWUIScrollBox* pCurrentScrollBox = Cast<UWWUIScrollBox>(pScrollBoxWidget);

		if (pCurrentScrollBox && GetMasterPlayerController())
		{
			pCurrentScrollBox->FocusFirstListField(GetMasterPlayerController());
		}
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenCareerHUB::SetMenuOptionIsEnabled(int32 idx, bool bEnabled)
{
	UWWUIScrollBox* pMenuScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerHUB_UI::ScrollBoxMenu));

	if (pMenuScrollBox)
	{
		UWWUIListField* pOptionListField = pMenuScrollBox->GetListField(idx);

		if (pOptionListField)
		{
			pOptionListField->SetVisibility(bEnabled ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		}
	}
}

void UWWUIScreenCareerHUB::SetMenuOptionIsEnabled(EMenuOption InMenuOption, bool bEnabled)
{
	UWWUIScrollBox* pMenuScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerHUB_UI::ScrollBoxMenu));

	if (pMenuScrollBox)
	{
		UWWUIListField* pOptionListField = pMenuScrollBox->GetListField((int)InMenuOption);

		if (pOptionListField)
		{
			pOptionListField->SetVisibility(bEnabled ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SetMenuOptionText(EMenuOption InMenuOption, FString NewText)
{
	UWWUIScrollBox* pMenuScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerHUB_UI::ScrollBoxMenu));

	if (pMenuScrollBox)
	{
		UWWUIListField* pOptionListField = pMenuScrollBox->GetListField((int)InMenuOption);

		if (pOptionListField)
		{
			UWidget* pTitleText = FindChildOfTemplateWidget(pOptionListField, "Title");

			if (pTitleText)
			{
				SetWidgetText(pTitleText, FText::FromString(NewText));
			}
		}
	}
}

//===============================================================================
//===============================================================================

FString UWWUIScreenCareerHUB::FormatMabDate(const MabDate* pInMabDate)
{
	if (!pInMabDate)
	{
		return "Format Mab Date was passed an invalid date!";
	}
	
	return FString::FromInt(pInMabDate->GetDayNumber()) + " " + UWWUITranslationManager::Translate(SIFGameHelpers::GAConvertMabStringToFString(pInMabDate->GetMonthString(pInMabDate->GetMonth()))) + " " + FString::FromInt(pInMabDate->GetYear());
}

void UWWUIScreenCareerHUB::LastDelayedEntryAction()
{
	if (!IsValidLowLevel())
	{
		UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenCareerHUB::OnWindowEnter LastDelayedEntryAction UWWUIScreenCareerHUB was not valid"));
		return;
	}

	EntryDelayTimer = 0;
	bRunCheckPopups = true;
	SetInputEnabled(true);

	int master_controller = SIFUIHelpers::GetCurrentMasterControllerIndex() == -1 ? 0 : SIFUIHelpers::GetCurrentMasterControllerIndex();
	SIFUIHelpers::ListenToController(master_controller, true);
}

void UWWUIScreenCareerHUB::QuitToMainMenu()
{
	// Quit to the main menu

	UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();

	modalData->WarningDialogue = FString("[ID_CUSTOMISE_PLAYER_POPUP_MAIN_TEXT]");
	modalData->LegendString = FString("[ID_SETTINGS_MENU_HELP]");

	TArray<FModalButtonInfo> ButtonData;

	FWWUIModalDelegate SaveDelegate;
	FWWUIModalDelegate DoNotSaveDelegate;

	//save button
	SaveDelegate.BindUObject(this, &UWWUIScreenCareerHUB::OnExitSaveConfirmed);
	ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_CUSTOMISE_PLAYER_BUTTON_SAVE]"), SaveDelegate));

	//do not save button
	DoNotSaveDelegate.BindUObject(this, &UWWUIScreenCareerHUB::OnExitNoSave);
	ButtonData.Add(FModalButtonInfo(FText::FromString("[ID_CUSTOMISE_PLAYER_BUTTON_NO]"), DoNotSaveDelegate));

	modalData->ButtonData = ButtonData;

	UWidget* pTextBlock = FindChildWidget(WWUIScreenCareerHUB_UI::BottomLegendText);
	if (pTextBlock)
	{
		modalData->pLegendWidget = pTextBlock;
	}

	pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
}


//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::SetLegendText(FString _text)
{
	UWWUIRichTextBlockWithTranslate* pTextBlock = Cast<UWWUIRichTextBlockWithTranslate>(FindChildWidget(WWUIScreenCareerHUB_UI::BottomLegendText));
	if (pTextBlock)
	{
		pTextBlock->SetText(_text);
	}
	else
	{
		ensure(pTextBlock);
	}
}


//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::HideCareerHUB()
{
	// this is implemented in line with a widget switcher
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerHUB::OnDismissProPlayerPromotion(APlayerController* OwningPlayer)
{
	//Message("Dismissed the promotion popup");
	return false;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerHUB::HideHeader(bool showInstead /*= false*/)
{
	UWidget* pHeader = FindChildWidget(WWUIScreenCareerHUB_UI::HeaderWidget);
	if (pHeader)
	{
		pHeader->SetVisibility(showInstead ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
	}
	else
	{
		ensure(pHeader);
	}

	UWidget* pDateAndCompLogo = FindChildWidget(WWUIScreenCareerHUB_UI::OverlayPaddingDateAndCompLogo);
	if (pDateAndCompLogo)
	{
		pDateAndCompLogo->SetVisibility(showInstead ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
	}
	else
	{
		ensure(pDateAndCompLogo);
	}
}

//===============================================================================
//===============================================================================

#ifdef DEBUG_CAREER_HUB_LAUNCH_SCREEN_OPTIONS

void UWWUIScreenCareerHUB::DebugMyContractsOptionOnClick(APlayerController* OwningPlayer)
{
	pCareerModeManager->AddProContractsFromInterest();
	pCareerModeManager->AddContract(true, (unsigned short)TeamDatabaseID, 3);
	pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CareerMyContracts);
}

void UWWUIScreenCareerHUB::DebugDraftOnClick(APlayerController* OwningPlayer)
{
	UWWUIStateScreenPlayerDraftingData* InData = NewObject<UWWUIStateScreenPlayerDraftingData>();
	InData->is_rep_selection = true;
	InData->is_customisation = false;
	pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CareerPlayerDrafting, InData);
}

#include "Rugby/UI/Screens/WWUIScreenCareerPlayerRecruiting.h"
void UWWUIScreenCareerHUB::DebugRecruitOnClick(APlayerController* OwningPlayer)
{
	UWWUIStateScreenCareerPlayerRecruitingData* InData = NewObject<UWWUIStateScreenCareerPlayerRecruitingData>();
	InData->team_db_id = SIFGameHelpers::GACompetitionGetCurrentPlayerTeamId();
	pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CareerPlayerRecruiting, InData);
}
void UWWUIScreenCareerHUB::DebugOfferOnClick(APlayerController* OwningPlayer)
{
	pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CareerPlayerOffer);
}
void UWWUIScreenCareerHUB::DebugProPostOnClick(APlayerController* OwningPlayer)
{
	pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::ProCareerPostMatch);
}
void UWWUIScreenCareerHUB::DebugProPostPerfOnClick(APlayerController* OwningPlayer)
{
	pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::ProCareerPostMatchPerf);
}
void UWWUIScreenCareerHUB::DebugSquadPostMatch(APlayerController* OwningPlayer)
{
	uint32 TeamID = pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch();
	SIFGameHelpers::GARequestFaceTeamRender(0, TeamID, true, false);
	uint32 CompetitionID = pCareerModeManager->GetActiveCompetition()->GetInstanceId();
	UWWUIStateScreenSquadPostMatchData* inData = NewObject<UWWUIStateScreenSquadPostMatchData>();
	inData->CompetitionID = CompetitionID;
	inData->TeamID = TeamID;
	inData->AtlasID = 0; // For the faces
	inData->bReadOnly = false; // Since this is our team
	pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::SquadPostMatch, inData);
}

#endif