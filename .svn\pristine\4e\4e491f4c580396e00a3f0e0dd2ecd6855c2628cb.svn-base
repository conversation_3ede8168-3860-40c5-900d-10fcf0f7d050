// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWRugbyFanHubService.h"

#if PLATFORM_PS4
#include "Source/Private/OnlineSubsystemPS4Types.h"
#include "Source/Public/OnlineSubsystemPS4.h"
#endif

#if !defined (FANHUB_ENABLED)
UWWRugbyFanHubService::UWWRugbyFanHubService(const FObjectInitializer& ObjectInitializer)
	: UWWFanhubHttpService::UWWFanhubHttpService(ObjectInitializer)
{

}
#endif

#include "GameVersion.h"

#if defined (FANHUB_ENABLED)
#include "Rugby/RugbyGameInstance.h"
#include "Rugby/FanHub/WWFanHubSearchManager.h"

#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RUDatabaseCaches.h"

#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Database.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RUDatabaseCaches.h"

// WW Rugby UI
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"

#include "Rugby/Utility/Helpers/SIFUIHelpers.h"
#include "Utility/Helpers/SIFGameHelpers.h"
#include "OnlineSubsystem.h"
#include "Source/Public/Interfaces/OnlineTimeInterface.h"

#include "Async.h"

static const FString CLIENT_ID = "rugby_challenge_4";

static const FString PLAYER_VERSION = "0.1";
static const FString TEAM_VERSION = "0.1";
static const FString GUERNSEY_VERSION = "0.1";

#if UE_BUILD_SHIPPING
#define ENABLE_CHAMPION_DATA_UPLOAD 0
#else
#define ENABLE_CHAMPION_DATA_UPLOAD 1
#endif

static constexpr int32 NUM_CHAMPION_DATA_DOCS_PER_PAGE = 600;

UWWRugbyFanHubService::UWWRugbyFanHubService(const FObjectInitializer& ObjectInitializer)
	: UWWFanhubHttpService::UWWFanhubHttpService(ObjectInitializer)
{
	//Init("http://*************");
	//Init("http://aflevo2.trublu.com.au"); // HES
	//Init("http://************/"); // aws - don't use this, use HES!
	//Init("http://*************"); // virtual box
	//Init("http://ec2-13-236-93-236.ap-southeast-2.compute.amazonaws.com");  // glindagames dev aws VM
	//Init("https://jlrc4.trublu.com.au");  // HES  Jonah Lomu RC4  production environment
#if UE_BUILD_SHIPPING
	Init("https://jlrc4.trublu.com.au");
#else
	Init("http://ec2-3-27-146-119.ap-southeast-2.compute.amazonaws.com");
#endif

	SetPerPage(FANHUB_NUMBER_PLAYERS_PER_PAGE);
	SetSortKey(WWAflFanhubSortableField::ActiveUsers);
	SetSortOrder(-1);

	pRugbyGameInstance = SIFApplication::GetApplication();

	CurrentState = FS_None;

	CurrentDownloadState = DownloadState_None;
}

enum class EFanhubDiffType : uint8
{
	INSERT,
	REMOVE,
	UPDATE,
	NONE
};

void UpdateSimpleField(TSharedPtr< FJsonObject > pChangeObject, FString FieldName, TSharedPtr<FJsonValue> pNewFieldValue)
{
	switch (pNewFieldValue->Type)
	{
	case EJson::String:
	{
		pChangeObject->SetStringField(FieldName, pNewFieldValue->AsString());
	}
	break;
	case EJson::Number:
	{
		pChangeObject->SetNumberField(FieldName, pNewFieldValue->AsNumber());
	}
	break;
	case EJson::Boolean:
	{
		pChangeObject->SetBoolField(FieldName, pNewFieldValue->AsBool());
	}
	break;
	case EJson::Array:
	{
		pChangeObject->SetArrayField(FieldName, pNewFieldValue->AsArray());
	}
	break;
	case EJson::Object:
	{
		UE_LOG(LogTemp, Warning, TEXT("UpdateSimpleField Object is not a simple field!"));
	}
	break;
	default:
	{
		// None, Null,
		UE_LOG(LogTemp, Warning, TEXT("UpdateSimpleField Unknown field type!"));
	}
	break;
	}
}

void CreateObjectChangeList(TSharedPtr<FJsonObject> pBaseJson, TSharedPtr<FJsonObject> pNewJson, TArray< TSharedPtr< FJsonValue > >& pChangeList)
{
	for (auto& pCurrentValue : pNewJson->Values)
	{
		FString NewFieldName = pCurrentValue.Key;
		TSharedPtr<FJsonValue> pNewFieldValue = pCurrentValue.Value;

		// Create the new change entry object.
		TSharedPtr< FJsonObject > pChangeObject = MakeShareable(new FJsonObject);

		// Try find the value in the base.
		TSharedPtr<FJsonValue> BaseFieldValue = pBaseJson->TryGetField(NewFieldName);

		if (BaseFieldValue)
		{
			// Base has the same value.

			// Check if they are the same.
			if (FJsonValue::CompareEqual(*BaseFieldValue, *pNewFieldValue))
			{
				// They are the same, continue on.
				continue;
			}

			if (pNewFieldValue->Type == EJson::Number && BaseFieldValue->Type == EJson::Number)
			{
				if (FMath::IsNearlyEqual(pNewFieldValue->AsNumber(), BaseFieldValue->AsNumber(), 0.01))
				{
					// They are the same, continue on.
					continue;
				}
			}

			// Fields do not match. Type will be update.
			pChangeObject->SetNumberField("Type", (int32)EFanhubDiffType::UPDATE);

			// Store the name of the field.
			pChangeObject->SetStringField("Key", NewFieldName);

			if (pNewFieldValue->Type == EJson::Object)
			{
				TArray< TSharedPtr< FJsonValue > > SubChangeList;
				CreateObjectChangeList(BaseFieldValue->AsObject(), pNewFieldValue->AsObject(), SubChangeList);

				// If the change list is empty then we can continue.
				if (SubChangeList.Num() == 0)
				{
					continue;
				}

				// Create an object to hold the change list. This will differentiate this from a normal array.
				TSharedPtr< FJsonObject > pSubChangeListObject = MakeShareable(new FJsonObject);
				pSubChangeListObject->SetArrayField("ChangeList", SubChangeList);

				pChangeObject->SetObjectField("Value", pSubChangeListObject);
			}
			else
			{
				// Must be a simple field.
				UpdateSimpleField(pChangeObject, "Value", pNewFieldValue);
			}
		}
		else // Field is missing from base.
		{
			// Record that we should add the field via the change entry.
			pChangeObject->SetNumberField("Type", (int32)EFanhubDiffType::INSERT);

			// Store the name of the field.
			pChangeObject->SetStringField("Key", NewFieldName);

			if (pNewFieldValue->Type == EJson::Object)
			{
				pChangeObject->SetObjectField("Value", pNewFieldValue->AsObject());
			}
			else
			{
				// Must be a simple field.
				UpdateSimpleField(pChangeObject, "Value", pNewFieldValue);
			}
		}

		// We should now have a new change object to add to our array. Check first that it has the required fields.
		if (!pChangeObject->HasField("Type"))
		{
			// We didn't write anything into the type field, something must have gone wrong.
			UE_LOG(LogTemp, Warning, TEXT("CreateObjectChangeList No type given!"));
			continue;
		}

		if (!pChangeObject->HasField("Key"))
		{
			// We didn't write anything into the type field, something must have gone wrong.
			UE_LOG(LogTemp, Warning, TEXT("CreateObjectChangeList No key given!"));
			continue;
		}

		if (!pChangeObject->HasField("Value"))
		{
			// We didn't write anything into the value field, something must have gone wrong.
			UE_LOG(LogTemp, Warning, TEXT("CreateObjectChangeList Error writing field %s"), *NewFieldName);
			continue;
		}

		// Seems valid so add it to the array.
		pChangeList.Add(MakeShareable(new FJsonValueObject(pChangeObject)));
	}

	// Check for deletetions.
	for (auto& pCurrentValue : pBaseJson->Values)
	{
		FString BaseFieldName = pCurrentValue.Key;
		TSharedPtr<FJsonValue> pBaseFieldValue = pCurrentValue.Value;

		// Create the new change entry object.
		TSharedPtr< FJsonObject > pChangeObject = MakeShareable(new FJsonObject);
		pChangeObject->SetNumberField("Type", (int32)EFanhubDiffType::REMOVE);

		// Try find the value in the new.
		TSharedPtr<FJsonValue> pNewFieldValue = pBaseJson->TryGetField(BaseFieldName);

		if (pNewFieldValue)
		{
			// We have the field, continue to the next.
			continue;
		}
		else // Could not find the field in the new.
		{
			// Mark the field for deletion.
			pChangeObject->SetStringField("Key", BaseFieldName);
		}

		// We should now have a new change object to add to our array. Check first that it has the required fields.
		if (!pChangeObject->HasField("Type"))
		{
			// We didn't write anything into the type field, something must have gone wrong.
			UE_LOG(LogTemp, Warning, TEXT("CreateObjectChangeList No type given!"));
			continue;
		}

		if (!pChangeObject->HasField("Key"))
		{
			// We didn't write anything into the type field, something must have gone wrong.
			UE_LOG(LogTemp, Warning, TEXT("CreateObjectChangeList No key given!"));
			continue;
		}

		// Seems valid so add it to the array.
		pChangeList.Add(MakeShareable(new FJsonValueObject(pChangeObject)));
	}
}

void ApplyChangeListObject(TSharedPtr<FJsonObject> pBaseJson, TArray< TSharedPtr< FJsonValue > >& pChangeList)
{
	for (auto& pCurrentChange : pChangeList)
	{
		TSharedPtr<FJsonObject> pChangeObject = pCurrentChange->AsObject();
		if (pChangeObject)
		{
			// All changes should have a type and key.
			int32 DiffTypeIndex = (int32)EFanhubDiffType::NONE;
			FString FieldName;

			if (pChangeObject->TryGetNumberField("Type", DiffTypeIndex) && pChangeObject->TryGetStringField("Key", FieldName))
			{
				// We have valid type and key fields, convert over the type for switching.
				EFanhubDiffType DiffType = (EFanhubDiffType)DiffTypeIndex;

				switch (DiffType)
				{
				case EFanhubDiffType::INSERT:
				{
					// Check our expectations.
					// Check if the field already exists on the base.

					if (pBaseJson->HasField(FieldName))
					{
						UE_LOG(LogTemp, Warning, TEXT("ApplyChangeListObject Expectations not met, field already exists on base!"));
					}

					// The field is missing from the base.
					TSharedPtr<FJsonValue> pChangeValue = pChangeObject->TryGetField("Value");

					if (pChangeValue)
					{
						if (pCurrentChange->Type == EJson::Object)
						{
							pBaseJson->SetObjectField(FieldName, pChangeValue->AsObject());
						}
						else
						{
							UpdateSimpleField(pBaseJson, FieldName, pChangeValue);
						}
					}
				}
				break;
				case EFanhubDiffType::REMOVE:
				{
					// Check our expectations.
					// Check if the field has already been deleted on the base.

					if (!pBaseJson->HasField(FieldName))
					{
						UE_LOG(LogTemp, Warning, TEXT("ApplyChangeListObject Expectations not met, field already removed on base!"));
						continue; // Don't need to remove it twice.
					}

					pBaseJson->RemoveField(FieldName);
				}
				break;
				case EFanhubDiffType::UPDATE:
				{
					// Check our expectations.
					// Check if the field already exists on the base.

					if (!pBaseJson->HasField(FieldName))
					{
						UE_LOG(LogTemp, Warning, TEXT("ApplyChangeListObject Expectations not met, field does not exist on the base!"));
					}

					// Update the base field.
					TSharedPtr<FJsonValue> pChangeValue = pChangeObject->TryGetField("Value");

					if (pChangeValue)
					{
						if (pChangeValue->Type == EJson::Object)
						{
							TSharedPtr<FJsonObject> pSubChangeListObject = pChangeValue->AsObject();
							TSharedPtr<FJsonObject> pBaseObject = pBaseJson->GetObjectField(FieldName);
							if (pBaseObject && pSubChangeListObject)
							{
								TArray<TSharedPtr<FJsonValue>> pSubChangeList = pSubChangeListObject->GetArrayField("ChangeList");
								ApplyChangeListObject(pBaseObject, pSubChangeList);
							}
						}
						else
						{
							UpdateSimpleField(pBaseJson, FieldName, pChangeValue);
						}
					}
				}
				break;
				case EFanhubDiffType::NONE:
					break;
				default:
					break;
				}
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("ApplyChangeListObject Missing key or type!"));
			}
		}
	}
}

TSharedPtr< FJsonObject > LoadGameDB()
{
	FString SaveDirectory = FPaths::ProjectDir() + "Tools";
	FString FileName = FString("GameDB.json");

	IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

	// Get absolute file path
	FString AbsoluteFilePath = SaveDirectory + "/" + FileName;

	FString OutputString;
	FFileHelper::LoadFileToString(OutputString, *AbsoluteFilePath);

	//TSharedPtr<FJsonObject> json = GetJsonObjFromString(resultStr);
	TSharedPtr<FJsonObject> json;

	//Create a reader pointer to read the json data
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(OutputString);

	if (!FJsonSerializer::Deserialize(Reader, json))
	{
		// Nah.
	}

	return json;
}

void CreateJsonGameDB()
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	check(pRugbyGameInstance);

	if (pRugbyGameInstance)
	{
		RUGameDatabaseManager* pDatabaseManager = pRugbyGameInstance->GetGameDatabaseManager();

		if (pDatabaseManager)
		{
			TSharedPtr< FJsonObject > pGameDatabase = MakeShared<FJsonObject>();

			// Players
			{
				MabVector<unsigned short> PlayerList;
				pDatabaseManager->LoadIdList<RUDB_PLAYER>(PlayerList);

				TArray<TSharedPtr<FJsonValue>> pPlayerList;

				for (int32 i = 0; i < PlayerList.size(); i++)
				{
					// Load the player data.
					RUDB_PLAYER LoadedPlayer;
					pDatabaseManager->LoadData<RUDB_PLAYER>(LoadedPlayer, PlayerList[i]);

					TSharedPtr< FJsonObject > PlayerJSON = MakeShared<FJsonObject>();
					LoadedPlayer.SerializeToJsonObject(PlayerJSON, true);

					pPlayerList.Add(MakeShareable(new FJsonValueObject(PlayerJSON)));
				}

				pGameDatabase->SetArrayField("Players", pPlayerList);
			}

			// Teams
			{
				MabVector<unsigned short> TeamList;
				pDatabaseManager->LoadIdList<RUDB_TEAM>(TeamList);

				TArray<TSharedPtr<FJsonValue>> pTeamList;

				for (int32 i = 0; i < TeamList.size(); i++)
				{
					// Load the player data.
					RUDB_TEAM LoadedTeam;
					pDatabaseManager->LoadData<RUDB_TEAM>(LoadedTeam, TeamList[i]);

					TSharedPtr< FJsonObject > TeamJSON = LoadedTeam.SerializeToJson(true);

					pTeamList.Add(MakeShareable(new FJsonValueObject(TeamJSON)));
				}

				pGameDatabase->SetArrayField("Teams", pTeamList);
			}

			FString SaveDirectory = FPaths::ProjectDir() + "Tools";
			FString FileName = FString("GameDB.json");

			IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

			// Get absolute file path
			FString AbsoluteFilePath = SaveDirectory + "/" + FileName;

			FString OutputString;
			TSharedRef< TJsonWriter<> > Writer = TJsonWriterFactory<>::Create(&OutputString);
			FJsonSerializer::Serialize(pGameDatabase.ToSharedRef(), Writer);

			FFileHelper::SaveStringToFile(OutputString, *AbsoluteFilePath);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::BeginDestroy()
{
	if (TickDelegateHandle.IsValid())
	{
		FTicker::GetCoreTicker().RemoveTicker(TickDelegateHandle);
		TickDelegateHandle.Reset();
	}

	Super::BeginDestroy();
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::PopulatePlayerList(TSharedPtr<FJsonObject> InQueryJson, int InPage, const FServerRequestFinished RequestCompleteCallback, bool bIgnorePrivacy /*= false*/)
{
	CacheQuery(InQueryJson, InPage);

	CurrentRequestCompleteCallback = RequestCompleteCallback;

	ClearLastHandleErrorCode();

	LastPopulatePageCount = 0;

	CurrentDownloadState = DownloadState_InProgress;

	CurrentState = FS_Players;

	FString OptionalInput = "";

	if (bIgnorePrivacy)
	{
		OptionalInput.Append("&ignore_privacy=true");
	}

	ListDocuments(WWFanhubDocType::Player, InQueryJson, InPage, OptionalInput,
		FwwHttpRequestComplete::CreateLambda([=](FString ResponseBody, bool WasSuccessful) {

		if (WasSuccessful)
		{
			TSharedPtr<FJsonObject> json = GetJsonObjFromString(ResponseBody);
			if (json.IsValid() && json->Values.Num() > 0)
			{
				bool bodySuccess = json->GetBoolField("success");

				CurrentDownloadState = DownloadState_Complete;

				if (bodySuccess)
				{
					LastPopulatePageCount = json->GetIntegerField("pagecount");

					TArray<TSharedPtr<FJsonValue>> playerArray = json->GetArrayField("result");
					CurrentJsonResultArray = playerArray;

					ClearPlayerMap();

					if (playerArray.Num() > 0)
					{
#if PLATFORM_PS4
						FSimpleDelegate completionCallback;
						completionCallback.BindUObject(this, &UWWRugbyFanHubService::StartAsyncProfanityCheckPlayers);

						StartAsyncRetrieveAccountNamesForPlayers(completionCallback);
#else
						StartAsyncProfanityCheckPlayers();
#endif
						return;
					}
				}
			}
		}
		else
		{
			HandleErrorCode(ERRORCODE_SERVER_UNAVAILABLE, false);
		}
		RequestCompleteCallback.ExecuteIfBound();
		
	}));
}

//===============================================================================
//===============================================================================
void UWWRugbyFanHubService::StartAsyncProfanityCheckPlayers()
{
	bool failedProfanity = true;
	TArray<FString> profanityCheckString;

	//pre process profanity
	{
		for (int i = 0; i < CurrentJsonResultArray.Num(); i++)
		{
			TSharedPtr<RUDB_PLAYER> pDatabasePlayer = TSharedPtr<RUDB_PLAYER>(new RUDB_PLAYER());

			pDatabasePlayer->DeserializeFromJson(CurrentJsonResultArray[i]->AsObject());
			if (pDatabasePlayer->IsCustom())
			{
				FString ProfantyCheckString = GetPlayerProfanityCheckString(pDatabasePlayer);
				profanityCheckString.Add(ProfantyCheckString);
			}
			else
			{
				profanityCheckString.Add("");
			}
		}
	}

	//Run Profanity call
	APlayerController* pPlayerController = pRugbyGameInstance->GetPrimaryPlayerController();

	if (pPlayerController)
	{
		ULocalPlayer* pLocalPlayer = pPlayerController->GetLocalPlayer();
		if (pLocalPlayer->IsValidLowLevel())
		{
			FUniqueNetIdRepl UniqueNetID = pLocalPlayer->GetPreferredUniqueNetId();
			IOnlineSubsystem* pOnlineSub = IOnlineSubsystem::Get();
			if (pOnlineSub)
			{
				/**
				FProfanityAsyncFinished profanityCallback;
				profanityCallback.BindUObject(this, &UWWRugbyFanHubService::CheckProfanityAsyncPlayers);

				//returns true when async call started, so we want to have some data
				failedProfanity = !pOnlineSub->CheckProfanityArrayAsync(*UniqueNetID, profanityCheckString, profanityCallback);
				**/
				// GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL change to new API, UE4.27 removed profanity check interface!
			}
		}
	}

	//profanity call failed to run???
	//so we just add all the players anyway.
	if (failedProfanity)
	{
		ensureMsgf(false, TEXT("PopulatePlayerList: Async Profanity Check failed"));
		for (int i = 0; i < CurrentJsonResultArray.Num(); i++)
		{
			TSharedPtr<RUDB_PLAYER> pDatabasePlayer = TSharedPtr<RUDB_PLAYER>(new RUDB_PLAYER());

			pDatabasePlayer->DeserializeFromJson(CurrentJsonResultArray[i]->AsObject());

#if PLATFORM_PS4
			CorrectAccountNames(pDatabasePlayer);
#endif

			PlayerMap.Add(pDatabasePlayer->GetServerID(), pDatabasePlayer);
		}

		if (IsInGameThread())
		{
			CurrentRequestCompleteCallback.ExecuteIfBound();
		}
		else
		{
			RunCurrentRequestCompleteCallbackOnGameThread();
		}
	}
}

//===============================================================================
//===============================================================================
void UWWRugbyFanHubService::CheckProfanityAsyncPlayers(TArray<bool> playerResults)
{
	for (int i = 0; i < CurrentJsonResultArray.Num(); i++)
	{
		TSharedPtr<RUDB_PLAYER> pDatabasePlayer = TSharedPtr<RUDB_PLAYER>(new RUDB_PLAYER());
		pDatabasePlayer->DeserializeFromJson(CurrentJsonResultArray[i]->AsObject());

		//contains results from profanity in same order as they were supplied
		if (!playerResults.IsValidIndex(i))
		{
			// We didn't get a result from the profanity check. We can't be certain that we don't have a profane player so just throw an error.
			HandleServerUnavailableOnGameThread();
			return;
		}

		if (!playerResults[i])
		{
			ensureMsgf(false, TEXT("CheckProfanityAsyncPlayers: Failed Profanity Check Player"));
			pDatabasePlayer->Censor();
		}

#if PLATFORM_PS4
		CorrectAccountNames(pDatabasePlayer);
#endif

		PlayerMap.Add(pDatabasePlayer->GetServerID(), pDatabasePlayer);
	}

	RunCurrentRequestCompleteCallbackOnGameThread();
}

//===============================================================================
//===============================================================================
void UWWRugbyFanHubService::StartAsyncProfanityCheckTeams()
{
	bool failedProfanity = true;
	TArray<FString> profanityCheckString;

	//pre process profanity
	{
		for (int i = 0; i < CurrentJsonResultArray.Num(); i++)
		{
			TSharedPtr<RUDB_TEAM> pDatabaseTeam = TSharedPtr<RUDB_TEAM>(new RUDB_TEAM());

			pDatabaseTeam->DeserializeFromJson(CurrentJsonResultArray[i]->AsObject());

			//IsCustom check wasnt there before? was there a possible reason, not sure. added it to match players

			if (pDatabaseTeam->IsCustom())
			{
				FString ProfantyCheckString = GetTeamProfanityCheckString(pDatabaseTeam);
				profanityCheckString.Add(ProfantyCheckString);
			}
			else
			{
				profanityCheckString.Add("");
			}
		}
	}

	//Run Profanity call
	APlayerController* pPlayerController = pRugbyGameInstance->GetPrimaryPlayerController();

	if (pPlayerController)
	{
		ULocalPlayer* pLocalPlayer = pPlayerController->GetLocalPlayer();
		if (pLocalPlayer->IsValidLowLevel())
		{
			FUniqueNetIdRepl UniqueNetID = pLocalPlayer->GetPreferredUniqueNetId();
			IOnlineSubsystem* pOnlineSub = IOnlineSubsystem::Get();
			if (pOnlineSub)
			{
				/**
			if (pOnlineSub)
			{
				FProfanityAsyncFinished profanityCallback;
				profanityCallback.BindUObject(this, &UWWRugbyFanHubService::CheckProfanityAsyncTeams);

				//returns true when async call started, so we want to have some data
				failedProfanity = !pOnlineSub->CheckProfanityArrayAsync(*UniqueNetID, profanityCheckString, profanityCallback);
			}**/
			// GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL change to new API, UE4.27 removed profanity check interface!
			}
		}
	}

	//profanity call failed to run???
	//so we just add all the teams anyway.
	if (failedProfanity)
	{
		ensureMsgf(false, TEXT("PopulateTeamList: Async Profanity Check failed"));
		for (int i = 0; i < CurrentJsonResultArray.Num(); i++)
		{
			TSharedPtr<RUDB_TEAM> pDatabaseTeam = TSharedPtr<RUDB_TEAM>(new RUDB_TEAM());

			pDatabaseTeam->DeserializeFromJson(CurrentJsonResultArray[i]->AsObject());

#if PLATFORM_PS4
			CorrectAccountNames(pDatabaseTeam);
#endif

			TeamMap.Add(pDatabaseTeam->GetServerID(), pDatabaseTeam);
		}

		if (IsInGameThread())
		{
			CurrentRequestCompleteCallback.ExecuteIfBound();
		}
		else
		{
			RunCurrentRequestCompleteCallbackOnGameThread();
		}
	}
}

//===============================================================================
//===============================================================================
void UWWRugbyFanHubService::CheckProfanityAsyncTeams(TArray<bool> teamResults)
{
	for (int i = 0; i < CurrentJsonResultArray.Num(); i++)
	{
		TSharedPtr<RUDB_TEAM> pDatabaseTeam = TSharedPtr<RUDB_TEAM>(new RUDB_TEAM());
		pDatabaseTeam->DeserializeFromJson(CurrentJsonResultArray[i]->AsObject());

		//contains results from profanity in same order as they were supplied
		if (!teamResults.IsValidIndex(i))
		{
			// We didn't get a result from the profanity check. We can't be certain that we don't have a profane player so just throw an error.
			HandleServerUnavailableOnGameThread();
			return;
		}

		if (!teamResults[i])
		{
			ensureMsgf(false, TEXT("CheckProfanityAsyncTeams: Failed Profanity Check Team"));
			pDatabaseTeam->Censor();
		}

#if PLATFORM_PS4
		CorrectAccountNames(pDatabaseTeam);
#endif

		TeamMap.Add(pDatabaseTeam->GetServerID(), pDatabaseTeam);
	}

	RunCurrentRequestCompleteCallbackOnGameThread();
}

//===============================================================================
//===============================================================================
void UWWRugbyFanHubService::RunCurrentRequestCompleteCallbackOnGameThread()
{
	AsyncTask(ENamedThreads::GameThread, [=]()
	{
		CurrentRequestCompleteCallback.ExecuteIfBound();
	});
}

//===============================================================================
//===============================================================================
void UWWRugbyFanHubService::HandleServerUnavailableOnGameThread()
{
	AsyncTask(ENamedThreads::GameThread, [=]()
	{
		if (pRugbyGameInstance)
		{
			pRugbyGameInstance->ShowErrorUIPopup("", "[ID_WW_SERVER_UNAVAILABLE]", InPoints_UI::mainMenu);
		}
	});
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::PopulateTeamList(TSharedPtr<FJsonObject> InQueryJson, int InPage, const FServerRequestFinished RequestCompleteCallback)
{
	CacheQuery(InQueryJson, InPage);

	CurrentRequestCompleteCallback = RequestCompleteCallback;

	ClearLastHandleErrorCode();

	LastPopulatePageCount = 0;

	CurrentDownloadState = DownloadState_InProgress;

	CurrentState = FS_Teams;
	FString optionalInput = "";//FString::Printf(TEXT("&linkedDoc=%s&linkedSource=%s"), *WWAflFanhubDocType::Player, *FString("captain"));
	ListDocuments(WWFanhubDocType::Team, InQueryJson, InPage, optionalInput,
		FwwHttpRequestComplete::CreateLambda([=](FString ResponseBody, bool WasSuccessful) {

		if (WasSuccessful)
		{
			TSharedPtr<FJsonObject> json = GetJsonObjFromString(ResponseBody);
			if (json.IsValid() && json->Values.Num() > 0)
			{
				bool bodySuccess = json->GetBoolField("success");

				if (bodySuccess)
				{
					LastPopulatePageCount = json->GetIntegerField("pagecount");

					CurrentDownloadState = DownloadState_Complete;

					TArray<TSharedPtr<FJsonValue>> teamArray = json->GetArrayField("result");
					CurrentJsonResultArray = teamArray;

					ClearAllMaps();

					if (teamArray.Num() > 0)
					{
#if PLATFORM_PS4
						FSimpleDelegate completionCallback;
						completionCallback.BindUObject(this, &UWWRugbyFanHubService::StartAsyncProfanityCheckTeams);

						StartAsyncRetrieveAccountNamesForTeams(completionCallback);
#else
						StartAsyncProfanityCheckTeams();
#endif
						return;
					}
				}
			}
		}
		else
		{
			// Just flag error and leave for caller to display when appropriate
			HandleErrorCode(ERRORCODE_SERVER_UNAVAILABLE, false);
		}
		RequestCompleteCallback.ExecuteIfBound();

	}));
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::ShowLoadingModal(bool bShow)
{
	SIFUIHelpers::ShowLoadingPopup(bShow);
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::FailedProfanityCheckModal(bool bIsAPlayer, FString ProblemName)
{
	FString ErrorString = "[ID_PROFANITY_ERROR]: ";

	ErrorString += ProblemName;

	SIFUIHelpers::LaunchServerResponsePopup(ErrorString);

}

//===============================================================================
//===============================================================================

FString UWWRugbyFanHubService::GetPlayerProfanityCheckString(TSharedPtr<RUDB_PLAYER> pPlayerToCheck)
{
	return pPlayerToCheck ? FString(pPlayerToCheck->GetFirstName()) + "_" + FString(pPlayerToCheck->GetLastName()) : "";
}

//===============================================================================
//===============================================================================

FString UWWRugbyFanHubService::GetTeamProfanityCheckString(TSharedPtr<RUDB_TEAM> pTeamToCheck)
{
	return pTeamToCheck ? FString(pTeamToCheck->GetName()) + "_" + FString(pTeamToCheck->GetShortName()) + "_" + FString(pTeamToCheck->GetMnemonic()) : "";
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::ShowOptionsModal(bool bMyUpload, int InDocIdx, FServerRequestFinished RefreshScrollBoxDelegate, TArray<FModalButtonInfo> ExtraButtons)
{
	FString docOwnerId;
	FString docStatus;
	FString docId;
	FString docType = "";
	switch (CurrentState)
	{
	case FS_Players:
	{
		TSharedPtr<RUDB_PLAYER> cardRecPtr = GetPlayerAtIndex(InDocIdx);
		if (cardRecPtr.IsValid())
		{
			docId = cardRecPtr->GetServerID();
			docOwnerId = cardRecPtr->GetUploadedByID();
			docStatus = cardRecPtr->GetServerStatus();
			docType = WWFanhubDocType::Player;
		}
		break;
	}
	case FS_Teams:
	{
		TSharedPtr<RUDB_TEAM> teamRecPtr = GetTeamAtIndex(InDocIdx);
		if (teamRecPtr.IsValid())
		{
			docId = teamRecPtr->GetServerID();
			docOwnerId = teamRecPtr->GetUploadedByID();
			docStatus = teamRecPtr->GetServerStatus();
			docType = WWFanhubDocType::Team;
		}
		break;
	}
		case FS_None:
		default:
			break;
	}

	bool isShared = docStatus == WWFanhubDocStatus::Public;

	TArray<FModalButtonInfo> ButtonInfo;

	//FModalButtonInfo searchButton;
	//searchButton.Title = FText::FromString("");// ResolveLocaleKey(ELocaleKey::SearchTitle);  #afl_legacy

	//modalData->ButtonData.Add(searchButton);
	selectedModalOption = -1;
	isSuccessfull = false;
	if (bMyUpload)
	{
		FModalButtonInfo deleteButton;
		deleteButton.Title = FText::FromString("[ID_DELETE_UPLOADED_ITEM]");
		deleteButton.OnClickDelegate.BindUObject(this, &UWWRugbyFanHubService::ChangeStatusModalCallback,
			CurrentState, docId, WWFanhubDocStatus::Removed, RefreshScrollBoxDelegate);

		FModalButtonInfo shareButton;
		shareButton.Title = FText::FromString(isShared ? "[ID_UNSHARE_UPLOADED_PLAYER]" : "[ID_SHARE_UPLOADED_PLAYER]");
		shareButton.OnClickDelegate.BindUObject(this, &UWWRugbyFanHubService::ChangeStatusModalCallback,
			CurrentState, docId, isShared ? WWFanhubDocStatus::Private : WWFanhubDocStatus::Public, RefreshScrollBoxDelegate);
		
		ButtonInfo.Add(deleteButton);
		ButtonInfo.Add(shareButton);
	}
	else
	{
		FModalButtonInfo ReportButton;
		ReportButton.Title = FText::FromString("REPORT");
		ReportButton.OnClickDelegate.BindLambda([=](APlayerController* OwningController) {
			ShowReportModal(docType, InDocIdx, RefreshScrollBoxDelegate); 
			return true;
		});
		ButtonInfo.Add(ReportButton);
	}

	ButtonInfo.Append(ExtraButtons);

	SIFUIHelpers::LaunchWarningPopup("[ID_COMMUNITY_OPTIONS_TITLE]", "[ID_ASSIGN_CONTROLLER_NEUTRAL_HELP]", ButtonInfo);
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::ShowReportModal(FString DocType, int InDocIdx, FServerRequestFinished RefreshScrollBoxDelegate)
{
	TArray<FModalButtonInfo> ButtonInfo;

	for (int32 i = 0; i < (int32)EReportReason::MAX; i++)
	{
		EReportReason Reason = (EReportReason)i;
		FString ReasonString = "";
		FWWUIModalDelegate ReasonDelegate;

		switch (Reason)
		{
		case EReportReason::SEXUAL_CONTENT:
		{
			ReasonString = "[ID_REPORT_SEXUAL_CONTENT_TITLE]";
		}
			break;
		case EReportReason::HATEFUL_OR_ABUSIVE_CONTENT:
		{
			ReasonString = "[ID_REPORT_ABUSIVE_CONTENT_TITLE]";
		}
			break;
		case EReportReason::INFRINGES_RIGHTS:
		{
			ReasonString = "[ID_REPORT_INFRINGES_RIGHTS_TITLE]";
		}
			break;
		case EReportReason::OTHER:
		{
			ReasonString = "[ID_REPORT_OTHER_TITLE]";
		}
			break;
		}

		// Translate the reason string so it's readable on the reports screen.
		ReasonString = UWWUITranslationManager::Translate(ReasonString);

		ReasonDelegate.BindLambda([=](APlayerController* OwningController) { ReportDocument(DocType, InDocIdx, ReasonString, RefreshScrollBoxDelegate); return true; });

		ButtonInfo.Add(FModalButtonInfo(ReasonString, ReasonDelegate));
	}

	SIFUIHelpers::LaunchWarningPopup("[ID_REPORT_POPUP_TITLE]", "[ID_ASSIGN_CONTROLLER_NEUTRAL_HELP]", ButtonInfo);
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::ReportDocument(FString DocType, int32 InDocIdx, FString ReportReason, FServerRequestFinished RefreshScrollBoxDelegate)
{
	ClearLastErrorCode();

	ShowLoadingModal(true);

	FServerRequestFinished hideModalCallback;
	hideModalCallback.BindLambda([=]() {
		ShowLoadingModal(false);
		RefreshScrollBoxDelegate.ExecuteIfBound();
		SIFUIHelpers::LaunchWarningPopup("[ID_REPORT_PLAYER_SUCCESS]", "[ID_COPYRIGHT_HELP]", TArray<FModalButtonInfo>());
	});

	FString ServerID = "";

	if (DocType == WWFanhubDocType::Player)
	{
		TSharedPtr<RUDB_PLAYER> pReportedPlayer = GetPlayerAtIndex(InDocIdx);

		if (pReportedPlayer)
		{
			ServerID = pReportedPlayer->GetServerID();
		}
	}
	else if (DocType == WWFanhubDocType::Team)
	{
		TSharedPtr<RUDB_TEAM> pReportedTeam = GetTeamAtIndex(InDocIdx);
		if (pReportedTeam)
		{
			ServerID = pReportedTeam->GetServerID();
		}
	}

	Report(DocType, ServerID, ReportReason,
		FwwHttpRequestComplete::CreateLambda([=](FString ResponseBody, bool WasSuccessful)
	{
		if (WasSuccessful)
		{
			// We got a response from server, check if operation itself was successful
			bool bodySuccess = GetResponseBodySuccess(ResponseBody);

			if (bodySuccess)
			{
				if (DocType == WWFanhubDocType::Player)
					PopulatePlayerList(GetQueryCache(), GetPageCache(), hideModalCallback);
				else if (DocType == WWFanhubDocType::Team)
					PopulateTeamList(GetQueryCache(), GetPageCache(), hideModalCallback);
				
				return; // Leave Loading modal up
			}
			else
			{
				// FIXME : Add more detailed feedback if we want to
				HandleErrorCode(ERRORCODE_SERVER_UNAVAILABLE, false);
			}
		}
		else
		{
			HandleErrorCode(ERRORCODE_SERVER_UNAVAILABLE, false);
		}

		// Dropping Modal triggers notification to screen (OnDismissModal, OnEnterScreen etc) so do after we set error
		ShowLoadingModal(false);
	}));
}

//===============================================================================
//===============================================================================

bool UWWRugbyFanHubService::IsUploadTeamRecAndCustomPlayersInProgress()
{
	return UploadTeamRecAndCustomPlayersData.IsInProgress();
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::UploadRUDBTeamAndCustomPlayers(TSharedPtr<RUDB_TEAM> pInDatabaseTeam, FString InStatus)
{
	UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeamAndCustomPlayers Starting upload."));

	if (!pInDatabaseTeam)
	{
		ensureMsgf(false, TEXT("UploadTeamRecAndCustomPlayers : InTeamRec is null!"));
		return;
	}

	UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeamAndCustomPlayers Input team is valid. Doing profanity check."));

	APlayerController* PlayerController = pRugbyGameInstance->GetPrimaryPlayerController();

	if (PlayerController)
	{
		ULocalPlayer* localPlayer = PlayerController->GetLocalPlayer();

		if (UOBJ_IS_VALID(localPlayer))
		{
			FUniqueNetIdRepl uniqueNetId = localPlayer->GetPreferredUniqueNetId();
			IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();

			FString ProfantyCheckString = GetTeamProfanityCheckString(pInDatabaseTeam);
			/**
			FString ProfantyCheckString = GetTeamProfanityCheckString(pInDatabaseTeam);
			if (!OnlineSub->CheckProfanity(*uniqueNetId, ProfantyCheckString))
			{
				UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeamAndCustomPlayers Failed profanity check."));
				ensureMsgf(false, TEXT("UploadTeamRecAndCustomPlayersInTeamRec: Failed Profanity Check Team"));
				FailedProfanityCheckModal(false, FString(pInDatabaseTeam->GetName()));
				return;
			}
			**/
			// GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL change to new API, UE4.27 removed profanity check interface!
		}
	}

	if (UploadTeamRecAndCustomPlayersData.CurrentState != EUploadTeamState::UTS_None &&
		UploadTeamRecAndCustomPlayersData.CurrentState != EUploadTeamState::UTS_Complete)
	{
		UE_LOG(LogTemp, Warning, TEXT("UWWRugbyFanHubService::UploadRUDBTeamAndCustomPlayers Upload is in progress!"));
		ensureMsgf(false, TEXT("UploadTeamRecAndCustomPlayersInTeamRec: Upload in progress!"));
		return;
	}

	// Init
	UploadTeamRecAndCustomPlayersData.TeamRec = pInDatabaseTeam;
	UploadTeamRecAndCustomPlayersData.Status = InStatus;
	UploadTeamRecAndCustomPlayersData.PlayerIndex = -1;
	UploadTeamRecAndCustomPlayersData.UploadState = UploadState_None;
	UploadTeamRecAndCustomPlayersData.bUploadSuccess = false;
	UploadTeamRecAndCustomPlayersData.CurrentState = EUploadTeamState::UTS_UploadingPlayers;

	UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeamAndCustomPlayers Showing loading modal."));
	ShowLoadingModal(true); // ELocaleKey::Uploading); #afl_legacy

	// Turn on ticker to run our update

	if (!TickDelegate.IsBound())
	{
		UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeamAndCustomPlayers Tick delegate is not bound, starting ticker."));
		TickDelegate = FTickerDelegate::CreateUObject(this, &UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers);
	}

	ensureMsgf(!TickDelegateHandle.IsValid(), TEXT("TickDelegateHandle is still valid! Did we fail to clean up?"));
	TickDelegateHandle = FTicker::GetCoreTicker().AddTicker(TickDelegate);
}

//===============================================================================
//===============================================================================

bool UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers(float DeltaSeconds)
{
	UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Tick Current state: %d, Upload state: %d."), UploadTeamRecAndCustomPlayersData.CurrentState, UploadTeamRecAndCustomPlayersData.UploadState);

	// Check upload rec state
	if (UploadTeamRecAndCustomPlayersData.UploadState == UploadState_InProgress)
	{
		UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Player upload in progress. Waiting for next tick."));
		// Wait if upload rec in progress
		return true;
	}
	else if (UploadTeamRecAndCustomPlayersData.UploadState == UploadState_Complete)
	{
		// Upload rec complete

		UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Upload complete."));

		// Abort if player upload failed otherwise we'll upload a team with invalid player Id
		if (UploadTeamRecAndCustomPlayersData.CurrentState == EUploadTeamState::UTS_UploadingPlayers)
		{
			UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Still uploading players, checking upload was successful."));

			if (!UploadTeamRecAndCustomPlayersData.bUploadSuccess)
			{
				UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Player upload failed! Removing overlay. Setting state to complete."));

				ShowLoadingModal(false);

				HandleErrorCode(LastHandleErrorCode, true);

				// Fall through to clean up
				UploadTeamRecAndCustomPlayersData.CurrentState = EUploadTeamState::UTS_Complete;
			}
			else
			{
				UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Upload was a success!"));
			}
		}

		UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Upload finished. Resetting upload state."));
		UploadTeamRecAndCustomPlayersData.UploadState = UploadState_None;
	}

	if (UploadTeamRecAndCustomPlayersData.CurrentState == EUploadTeamState::UTS_UploadingPlayers)
	{
		UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers State is stil upload players."));
		// Find next player that needs to be uploaded ie is custom and never previously uploaded
		TSharedPtr<RUDB_TEAM> TeamRec = UploadTeamRecAndCustomPlayersData.TeamRec;

		MabVector<unsigned short> playerList;

		TeamRec->GetAllPlayersInDisplayOrder(playerList);

		int PlayerIndex = UploadTeamRecAndCustomPlayersData.PlayerIndex + 1;

		UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Last uploaded player index was %d, starting check at: %d."), UploadTeamRecAndCustomPlayersData.PlayerIndex, PlayerIndex);

		for (; PlayerIndex < playerList.size(); ++PlayerIndex)
		{
			unsigned short PlayerDatabaseID = playerList[PlayerIndex];

			UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Player at index is %d."), PlayerDatabaseID);

			// Invalid card rec
			if (PlayerDatabaseID == 0)
			{
				UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Skipping as DB index is 0."));
				continue;
			}

			RUGameDatabaseManager* pDatabaseManager = SIFApplication::GetApplication()->GetGameDatabaseManager();
			TSharedPtr<RUDB_PLAYER> pPlayerData = MakeShareable(new RUDB_PLAYER);

			if (!pDatabaseManager)
			{
				UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Database manager is not valid!"));
				continue;
			}

			pDatabaseManager->LoadData(*pPlayerData, PlayerDatabaseID);

			UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Loaded player from database."));

			// If we aren't a custom player, continue.
			if (pPlayerData->GetDbId() <= DB_LAST_PLAYER_ID)
			{
				UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Skipping player as they are not custom. Checking next player."));
				continue;
			}

			/*if (!pPlayerData->GetServerID().IsEmpty())
			{
				continue;
			}*/

			UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Player is custom, initiating upload."));

			// We need to upload player
			UploadTeamRecAndCustomPlayersData.PlayerIndex = PlayerIndex;
			UploadTeamRecAndCustomPlayersData.UploadState = UploadState_InProgress;

			bool result = UploadRUDBPlayer(pPlayerData, UploadTeamRecAndCustomPlayersData.Status, false);

			//upload failed..
			if (result == false)
			{
				UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Upload player returned false! Removing loading screen."));

				UploadTeamRecAndCustomPlayersData.PlayerIndex = 0;
				ShowLoadingModal(false);

				UploadTeamRecAndCustomPlayersData.CurrentState = EUploadTeamState::UTS_Complete;
				UploadTeamRecAndCustomPlayersData.UploadState = UploadState_None;
			}

			UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Waiting for next tick."));
			return true;
		}

		UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Finished uploading players, will now upload team."));

		// All Players that needed to be uploaded are uploaded! Now upload the team.
		UploadTeamRecAndCustomPlayersData.CurrentState = EUploadTeamState::UTS_UploadingTeam;
		UploadTeamRecAndCustomPlayersData.UploadState = UploadState_InProgress;

		UploadRUDBTeam(TeamRec, UploadTeamRecAndCustomPlayersData.Status);

		UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Waiting for next tick."));
		return true;
	}
	else if (UploadTeamRecAndCustomPlayersData.CurrentState == EUploadTeamState::UTS_UploadingTeam)
	{
		UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers We have finished uploading the team, setting state to complete."));

		// We're done
		UploadTeamRecAndCustomPlayersData.CurrentState = EUploadTeamState::UTS_Complete;

		// Fall through to clean up
	}

	UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::TickUploadRUDBTeamAndCustomPlayers Resetting state and removing ticker."));

	// Clean up and exit
	ensureMsgf(UploadTeamRecAndCustomPlayersData.CurrentState == EUploadTeamState::UTS_Complete, TEXT("Unexpected EUploadTeamState state"));
	UploadTeamRecAndCustomPlayersData.CurrentState = EUploadTeamState::UTS_None;

	// Deactivate ticker
	if (TickDelegateHandle.IsValid())
	{
		FTicker::GetCoreTicker().RemoveTicker(TickDelegateHandle);
	}

	TickDelegateHandle.Reset();

	return false;
}

//===============================================================================
//===============================================================================

bool UWWRugbyFanHubService::UploadRUDBPlayer(TSharedPtr<RUDB_PLAYER> pInDatabasePlayer, FString InString, bool bDisplayModals)
{
	ClearLastHandleErrorCode();

	UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBPlayer Uploading player."));

	if (!pInDatabasePlayer)
	{
		ensureMsgf(pInDatabasePlayer, TEXT("Invalid database player!"));
		return false;
	}

	UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBPlayer Player is valid. Checking profanity."));

	APlayerController* pPlayerController = pRugbyGameInstance->GetPrimaryPlayerController();

	if (UOBJ_IS_VALID(pPlayerController))
	{
		ULocalPlayer* pLocalPlayer = pPlayerController->GetLocalPlayer();

		if (UOBJ_IS_VALID(pLocalPlayer))
		{
			FUniqueNetIdRepl uniqueNetId = pLocalPlayer->GetPreferredUniqueNetId();
			IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();

			FString ProfantyCheckString = GetPlayerProfanityCheckString(pInDatabasePlayer);

			/**
			if (!OnlineSub->CheckProfanity(*uniqueNetId, ProfantyCheckString))
			{
				UE_LOG(LogTemp, Warning, TEXT("UWWRugbyFanHubService::UploadRUDBPlayer Player failed profanity check."));
				ensureMsgf(false, TEXT("UploadCardRec: Failed Profanity Check Player"));
				FString PlayerName = FString(pInDatabasePlayer->GetFirstName()) + " " + FString(pInDatabasePlayer->GetLastName());
				FailedProfanityCheckModal(true, PlayerName);
				return false;
			}
			**/
			// GGS WW WICKEDWITCH GLINDAGAMES RUGBY AFL change to new API, UE4.27 removed profanity check interface!
		}
	}

	FString optionalInput = FString();

	RL3DB_PLAYER rl3_player((unsigned short)pInDatabasePlayer->GetDbId());

	// Check if this is our first upload. If we downloaded a document which had us as the owner or when we finish uploading this document, we set this ID.
	// We check this ID hele so that when we try to upload it again, we update the document instead of uploading a fresh one.
	FString SavedServerID = SIFGameHelpers::GAConvertMabStringToFString(rl3_player.GetServerId());

	if (!SavedServerID.IsEmpty())
	{
		optionalInput += FString::Printf(TEXT("&docId=%s"), *SavedServerID);
	}

	// We append the creator here if this document was not created by our account. This way the server will know who created the document and can attach that
	// ID to this edited version created on this account.
	FString CreatedByID = SIFGameHelpers::GAConvertMabStringToFString(rl3_player.GetCreatedBy());

	if (!CreatedByID.IsEmpty())
	{
		optionalInput += FString::Printf(TEXT("&creator=%s"), *CreatedByID);
	}

	if (bDisplayModals)
	{
		ShowLoadingModal(true);
	}

	FString SerializationString;

	pInDatabasePlayer->SerializeToJson(SerializationString);

	UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBPlayer Starting upload."));

	UploadPlayer(SerializationString, InString, optionalInput,
		FwwHttpRequestComplete::CreateLambda([=](FString ResponseBody, bool WasSuccessful)
	{
		UploadTeamRecAndCustomPlayersData.UploadState = UploadState_Complete;

		UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBPlayer Player upload complete, setting team and player upload state to complete."));

		if (bDisplayModals)
		{
			UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBPlayer Removing loading screen."));
			ShowLoadingModal(false);
		}

		bool bodySuccess = false;

		if (WasSuccessful)
		{
			UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBPlayer Player upload received server response."));

			TSharedPtr<FJsonObject> json = GetJsonObjFromString(ResponseBody);
			if (json.IsValid() && json->Values.Num() > 0)
			{
				UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBPlayer JSON is valid."));

				bodySuccess = json->GetBoolField("success");

				if (bodySuccess)
				{
					UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBPlayer Player upload was success! Adding to player map."));

					FString serverId = json->GetObjectField("docId")->GetStringField("$id");
					bLastUploadWasUpdate = false;

					ServerPlayerMap.Add(pInDatabasePlayer->GetDbId(), serverId);

					RL3DB_PLAYER rl3_player((unsigned short)pInDatabasePlayer->GetDbId());

					// We always save this ID out so if we try to download a player we have uploaded but still have saved it doesn't make a copy.
					rl3_player.SetDownloadIdServer(TCHAR_TO_UTF8(*serverId));

					// Check if this is our first upload.
					FString DocumentUploadID = SIFGameHelpers::GAConvertMabStringToFString(rl3_player.GetServerId());

					if (DocumentUploadID.IsEmpty())
					{
						// Update the document ID's to match the ID given by the server.
						rl3_player.SetServerId(TCHAR_TO_UTF8(*serverId));

						UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBPlayer This was the first upload for this document, saving document ID."));
					}
					else
					{
						// We just updated a document we had previously uploaded.
						bLastUploadWasUpdate = true;

						// Let's set this anyway, but check it matches first. We are just updating a document already uploaded, so it should match.
						ensureMsgf(serverId == DocumentUploadID, TEXT("The ID the server returned and the one we have saved for this document do not match! This means the server made a new doc instead of updating!"));
						rl3_player.SetServerId(TCHAR_TO_UTF8(*serverId));

						UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBPlayer This was a document update."));
					}

#if PLATFORM_PS4
					// Always update the creator and uploader IDs
					rl3_player.SetCreatorPlatformId(TCHAR_TO_UTF8(*json->GetStringField("creatorPlatformID")));
					rl3_player.SetUploaderPlatformId(TCHAR_TO_UTF8(*json->GetStringField("uploaderPlatformID")));
#endif //PLATFORM_PS4

					// Update the creator and uploader display names
					FString creatorName = json->GetStringField("creatorDisplayName");
					FString uploaderName = json->GetStringField("uploaderDisplayName");
					FString downloadInfo = creatorName + "," + uploaderName;
					rl3_player.SetDownloadIdUser(TCHAR_TO_UTF8(*downloadInfo));

					if (bDisplayModals)
					{
						UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBPlayer We are displaying modals, so show the saving overlay and save the database!"));

						SIFUIHelpers::ShowSavingOverlay(true, true);

						RUGameDatabaseManager* pDatabaseManager = SIFApplication::GetApplication()->GetGameDatabaseManager();

						if (pDatabaseManager)
						{
							pDatabaseManager->SaveCustomDatabase();
						}

						// Up to the screen to tell us if we succeeded or failed.
					}
				}
				else
				{
					UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBPlayer Fanhub upload error. Probably uploaded a player who we don't have ownership for!"));

					int32 ErrorCode = json->GetIntegerField("error_code");

					HandleErrorCode(ErrorCode, bDisplayModals);
				}
			}
		}
		else
		{
			HandleErrorCode(ERRORCODE_SERVER_UNAVAILABLE, bDisplayModals);
		}

		UploadTeamRecAndCustomPlayersData.bUploadSuccess = WasSuccessful && bodySuccess;
		UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBPlayer Setting upload success for team and player upload. New value: %d"), UploadTeamRecAndCustomPlayersData.bUploadSuccess);
	}));

	return true;
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::UploadPlayer(FString InJson, FString InStatus, FString OptionalInput, const FwwHttpRequestComplete InCallback)
{
	FString postData = CreateInputData(WWFanhubServicePOST::UploadDocument, WWFanhubDocType::Player,
		FString::Printf(TEXT("&version=%s&status=%s&stat=%s%s"), *GetPlayerVersion(), *InStatus, *InJson, *OptionalInput));

	UploadDocument(postData, InCallback);
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::UploadRUDBTeam(TSharedPtr<RUDB_TEAM> InTeamRec, FString InStatus)
{
	ClearLastHandleErrorCode();

	UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeam Uploading team."));

	RL3DB_TEAM rl3_db_team = RL3DB_TEAM(InTeamRec->GetDbId());

	FString optionalInput = FString();

	// Check if this is our first upload. If we downloaded a document which had us as the owner or when we finish uploading this document, we set this ID.
	// We check this ID hele so that when we try to upload it again, we update the document instead of uploading a fresh one.
	FString SavedServerID = SIFGameHelpers::GAConvertMabStringToFString(rl3_db_team.GetServerId());

	if (!SavedServerID.IsEmpty())
	{
		optionalInput += FString::Printf(TEXT("&docId=%s"), *SavedServerID);
	}

	FString CreatedByID = SIFGameHelpers::GAConvertMabStringToFString(rl3_db_team.GetCreatedBy());

	if (!CreatedByID.IsEmpty())
	{
		optionalInput += FString::Printf(TEXT("&creator=%s"), *CreatedByID);
	}

	ShowLoadingModal(true); //, ELocaleKey::Uploading); #afl_legacy

	FString TeamString = "";
	InTeamRec->SerializeToJson(TeamString);

	UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeam Starting upload."));

	UploadTeam(TeamString, InStatus, optionalInput,
		FwwHttpRequestComplete::CreateLambda([=](FString ResponseBody, bool WasSuccessful)
	{
		UploadTeamRecAndCustomPlayersData.UploadState = UploadState_Complete;

		UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeam Team upload complete, setting team and player upload state to complete. Removing loading modal."));

		ShowLoadingModal(false);

		if (!WasSuccessful)
		{
			UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeam Upload failed, no server response received."));
			HandleErrorCode(ERRORCODE_SERVER_UNAVAILABLE, true);
		}

		bool bodySuccess = false;

		if (WasSuccessful)
		{
			UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeam Team upload received server response."));

			TSharedPtr<FJsonObject> json = GetJsonObjFromString(ResponseBody);
			if (json.IsValid() && json->Values.Num() > 0)
			{
				UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeam JSON is valid."));

				bodySuccess = json->GetBoolField("success");

				if (bodySuccess)
				{
					UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeam Team upload was success!"));

					FString serverId = json->GetObjectField("docId")->GetStringField("$id");
					bLastUploadWasUpdate = false;

					RL3DB_TEAM rl3_db_team = RL3DB_TEAM(InTeamRec->GetDbId());
					FString DocumentUploadID = SIFGameHelpers::GAConvertMabStringToFString(rl3_db_team.GetServerId());

					// We always save this ID out so if we try to download a player we have uploaded but still have saved it doesn't make a copy.
					rl3_db_team.SetDownloadIdServer(TCHAR_TO_UTF8(*serverId));

					if (DocumentUploadID.IsEmpty())
					{
						// Update the document ID's to match the ID given by the server.
						rl3_db_team.SetServerId(TCHAR_TO_UTF8(*serverId));

						UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeam This was the first upload for this document, saving document ID."));
					}
					else
					{
						// We just updated a document we had previously uploaded.
						bLastUploadWasUpdate = true;

						// Let's set this anyway, but check it matches first. We are just updating a document already uploaded, so it should match.
						ensureMsgf(serverId == DocumentUploadID, TEXT("The ID the server returned and the one we have saved for this document do not match! This means the server made a new doc instead of updating!"));
						rl3_db_team.SetServerId(TCHAR_TO_UTF8(*serverId));

						UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeam This was a document update."));
					}

#if PLATFORM_PS4
					// Always update the creator and uploader IDs
					rl3_db_team.SetCreatorPlatformId(TCHAR_TO_UTF8(*json->GetStringField("creatorPlatformID")));
					rl3_db_team.SetUploaderPlatformId(TCHAR_TO_UTF8(*json->GetStringField("uploaderPlatformID")));
#endif //PLATFORM_PS4

					// Update the creator and uploader display names
					FString creatorName = json->GetStringField("creatorDisplayName");
					FString uploaderName = json->GetStringField("uploaderDisplayName");
					FString downloadInfo = creatorName + "," + uploaderName;
					rl3_db_team.SetDownloadIdUser(TCHAR_TO_UTF8(*downloadInfo));

					RUGameDatabaseManager* pDatabaseManager = SIFApplication::GetApplication()->GetGameDatabaseManager();

					// Update strip uploader.
					if (pDatabaseManager)
					{
						for (size_t i = 0; i < RUDB_TEAM::MAX_STRIPS; ++i)
						{
							if (InTeamRec->GetStripId(i) != DB_INVALID_ID)
							{
								RUDB_TEAM_STRIP LoadedStrip;

								pDatabaseManager->LoadData(LoadedStrip, InTeamRec->GetStripId(i));

								if (LoadedStrip.is_custom)
								{
									LoadedStrip.SetUploadedBy(TCHAR_TO_UTF8(*uploaderName));
									LoadedStrip.SetPlatformIdUploader(TCHAR_TO_UTF8(*json->GetStringField("uploaderPlatformID")));

									pDatabaseManager->StoreData(LoadedStrip);
								}
							}
						}
					}

					UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeam Show the saving overlay and save the database!"));

					SIFUIHelpers::ShowSavingOverlay(true, true);

					if (pDatabaseManager)
					{
						pDatabaseManager->SaveCustomDatabase();
					}

					// Up to screen to handle custom database save event.
				}
				else
				{
					UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeam Fanhub upload error. Probably uploaded a player who we don't have ownership for!"));

					int32 ErrorCode = json->GetIntegerField("error_code");

					HandleErrorCode(ErrorCode, true);
				}
			}
		}

		UploadTeamRecAndCustomPlayersData.bUploadSuccess = WasSuccessful && bodySuccess;
		UE_LOG(LogTemp, Display, TEXT("UWWRugbyFanHubService::UploadRUDBTeam Setting upload success for team and player upload. New value: %d"), UploadTeamRecAndCustomPlayersData.bUploadSuccess);
	}));
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::UploadTeam(FString InJson, FString InStatus, FString OptionalInput, const FwwHttpRequestComplete InCallback)
{
	FString postData = CreateInputData(WWFanhubServicePOST::UploadDocument, WWFanhubDocType::Team,
		FString::Printf(TEXT("&version=%s&status=%s&stat=%s%s"), *GetTeamVersion(), *InStatus, *InJson, *OptionalInput));

	UploadDocument(postData, InCallback);
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::DownloadChampionData(const FwwHttpRequestComplete InCallback /*= FwwHttpRequestComplete()*/, int32 Page /*= 0*/)
{
	FwwHttpRequestComplete OnChampionDataDownloadCompleteDelegate = FwwHttpRequestComplete::CreateLambda([=](FString ResponseBody, bool WasSuccessful)
	{
		if (WasSuccessful)
		{
			TSharedPtr<FJsonObject> pJsonObject = GetJsonObjFromString(ResponseBody);
			TSharedPtr<FJsonObject, ESPMode::ThreadSafe> pThreadSafeJSON = MakeShared<FJsonObject, ESPMode::ThreadSafe>(*pJsonObject);

			bool bWasBodySuccessful = pThreadSafeJSON->GetBoolField("success");

			if (pThreadSafeJSON.IsValid() && bWasBodySuccessful)
			{
				AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [pThreadSafeJSON, InCallback, WasSuccessful, this, Page]()
				{

					check(pRugbyGameInstance);

					if (pRugbyGameInstance)
					{
						RUGameDatabaseManager* pDatabaseManager = pRugbyGameInstance->GetGameDatabaseManager();

						if (pDatabaseManager)
						{
							TArray<TSharedPtr<FJsonValue>> PlayerArray = pThreadSafeJSON->GetArrayField("players");

							// Dump the champion data card recs into the game database.
							if (PlayerArray.Num() > 0)
							{
								MabVector<unsigned short> IdList;
								pDatabaseManager->LoadIdList<RUDB_PLAYER>(IdList);

								for (int i = 0; i < PlayerArray.Num(); i++)
								{
									if (pRugbyGameInstance->FunhubShouldDropTasks)
									{
										// Tell the main menu we were successful on the game thread.
										AsyncTask(ENamedThreads::GameThread, [InCallback, WasSuccessful]()
										{
											InCallback.ExecuteIfBound("", false);
										});
										return;
									}

									TSharedPtr<FJsonObject> pPlayerJSON = PlayerArray[i]->AsObject();

									if (pPlayerJSON)
									{
										// Keep track of if this player is linked, if it is, we don't want to download live data for them.
										bool bIsLinked = false;

										RUDB_PLAYER DownloadedPlayer;

										uint32 PlayerChampionDataID;
										if (pPlayerJSON->TryGetNumberField("LocalID", PlayerChampionDataID))
										{
											// Try find the player we have downloaded in the list of players.
											for (auto& CurrentID : IdList)
											{
												if (CurrentID == PlayerChampionDataID)
												{
													pDatabaseManager->LoadData<RUDB_PLAYER>(DownloadedPlayer, PlayerChampionDataID);

													if (DownloadedPlayer.IsCustom())
													{
														RL3Database* pRL3Database = pDatabaseManager->GetRL3Database();

														if (pRL3Database)
														{
															unsigned short LinkTableID = pRL3Database->GetPlayerLinkCache()->SelectWhere(CCDB_PLAYERLINK_ORIGINAL_PLAYER, (unsigned short)CurrentID, false);
															if (LinkTableID != DB_INVALID_ID)
															{
																rudb_player_link_row* pLinkRow = (rudb_player_link_row*)pRL3Database->GetPlayerLinkCache()->GetRowStart(LinkTableID);

																if (pLinkRow)
																{
																	// Found the linked player, we can skip this one.
																	bIsLinked = true;
																}
																else
																{
																	UE_LOG(LogTemp, Warning, TEXT("Found linked player entry in cache but could not get row!"));
																}
															}
															else
															{
																UE_LOG(LogTemp, Warning, TEXT("Player was custom but could not find linked player entry!"));
															}
														}
													}

													if (DownloadedPlayer.morph_targets.GetDbId() == 0)
													{
														UE_LOG(LogTemp, Warning, TEXT("Player with ID: %d has no morph target entry in the morph targets table"), DownloadedPlayer.GetDbId());
													}

													// Found the player, break now.
													break;
												}
											}

											if (!bIsLinked)
											{
												TArray<TSharedPtr<FJsonValue>> pChangeList = pPlayerJSON->GetArrayField("ChangeList");

												TSharedPtr<FJsonObject> pPlayerObject;
												DownloadedPlayer.SerializeToJsonObject(pPlayerObject, true);

												ApplyChangeListObject(pPlayerObject, pChangeList);
												pDatabaseManager->StoreData(DownloadedPlayer);
											}
										}

										//// Try find the player we have downloaded in the list of players.
										//for (auto& CurrentID : IdList)
										//{
										//	if (CurrentID == PlayerChampionDataID)
										//	{
										//		pDatabaseManager->LoadData<RUDB_PLAYER>(DownloadedPlayer, PlayerChampionDataID);

										//		if (DownloadedPlayer.IsCustom())
										//		{
										//			RL3Database* pRL3Database = pDatabaseManager->GetRL3Database();

										//			if (pRL3Database)
										//			{
										//				unsigned short LinkTableID = pRL3Database->GetPlayerLinkCache()->SelectWhere(CCDB_PLAYERLINK_ORIGINAL_PLAYER, (unsigned short)CurrentID, false);
										//				if (LinkTableID != DB_INVALID_ID)
										//				{
										//					rudb_player_link_row* pLinkRow = (rudb_player_link_row*)pRL3Database->GetPlayerLinkCache()->GetRowStart(LinkTableID);

										//					if (pLinkRow)
										//					{
										//						// Found the linked player, we can skip this one.
										//						bIsLinked = true;
										//					}
										//					else
										//					{
										//						UE_LOG(LogTemp, Warning, TEXT("Found linked player entry in cache but could not get row!"));
										//					}
										//				}
										//				else
										//				{
										//					UE_LOG(LogTemp, Warning, TEXT("Player was custom but could not find linked player entry!"));
										//				}
										//			}
										//		}

										//		if (DownloadedPlayer.morph_targets.GetDbId() == 0)
										//		{
										//			UE_LOG(LogTemp, Warning, TEXT("Player with ID: %d has no morph target entry in the morph targets table"), DownloadedPlayer.GetDbId());
										//		}

										//		// Found the player, break now.
										//		break;
										//	}
										//}

										//if (!bIsLinked)
										//{
										//	DownloadedPlayer.DeserializeFromJson(pPlayerJSON, true);
										//	pDatabaseManager->StoreData(DownloadedPlayer);
										//}

										// Update the loading text with a percentage.
										{
											float CurrentCount = ((Page * NUM_CHAMPION_DATA_DOCS_PER_PAGE) + i);
											float PercentComplete = CurrentCount / (float)IdList.size();
											UE_LOG(LogDesync, Display, TEXT("%f"), PercentComplete);
											if (PlayerArray.Num() < NUM_CHAMPION_DATA_DOCS_PER_PAGE)
											{
												PercentComplete = CurrentCount / ((Page * NUM_CHAMPION_DATA_DOCS_PER_PAGE) + PlayerArray.Num());
											}

											int32 RoundedPercentComplete = FMath::RoundToInt(FMath::Clamp<float>(PercentComplete * 100.0f, 0.0f, 100.0f));
											SIFUIHelpers::UpdateLoadingPercentageText(FString::Printf(TEXT("%d%%"), RoundedPercentComplete));

											UE_LOG(LogDesync, Display, TEXT("%d, %f"), RoundedPercentComplete, PercentComplete);
										}
									}
								}
							}

							// Dump the champion data team recs into the game database.
							TArray<TSharedPtr<FJsonValue>> TeamArray = pThreadSafeJSON->GetArrayField("teams");

							if (TeamArray.Num() > 0)
							{
								MabVector<unsigned short> IdList;
								pDatabaseManager->LoadIdList<RUDB_TEAM>(IdList);

								for (int i = 0; i < TeamArray.Num(); i++)
								{
									if (pRugbyGameInstance->FunhubShouldDropTasks)
									{
										// Tell the main menu we were successful on the game thread.
										AsyncTask(ENamedThreads::GameThread, [InCallback, WasSuccessful]()
										{
											InCallback.ExecuteIfBound("", false);
										});
										return;
									}

									TSharedPtr<FJsonObject> pTeamJSON = TeamArray[i]->AsObject();

									if (pTeamJSON)
									{
										uint32 TeamChampionDataID = pTeamJSON->GetNumberField("local_id");

										// Keep track of if this team is linked, if it is, we don't want to download live data for them.
										bool bIsLinked = false;

										RUDB_TEAM DownloadedTeam;

										for (auto& CurrentID : IdList)
										{
											if (CurrentID == TeamChampionDataID)
											{
												pDatabaseManager->LoadData<RUDB_TEAM>(DownloadedTeam, TeamChampionDataID);

												if (DownloadedTeam.IsCustom())
												{
													RL3Database* pRL3Database = pDatabaseManager->GetRL3Database();

													if (pRL3Database)
													{
														unsigned short LinkTableID = pRL3Database->GetTeamLinkCache()->SelectWhere(CCDB_TEAMLINK_ORIGINAL_TEAM, (unsigned short)CurrentID, false);
														if (LinkTableID != DB_INVALID_ID)
														{
															rudb_team_link_row* pLinkRow = (rudb_team_link_row*)pRL3Database->GetTeamLinkCache()->GetRowStart(LinkTableID);

															if (pLinkRow)
															{
																// Found the team player, we can skip this one.
																bIsLinked = true;
															}
															else
															{
																UE_LOG(LogTemp, Warning, TEXT("Found linked team entry in cache but could not get row!"));
															}
														}
														else
														{
															UE_LOG(LogTemp, Warning, TEXT("Team was custom but could not find linked player entry!"));
														}
													}
												}

												break;
											}
										}

										if (!bIsLinked)
										{
											DownloadedTeam.DeserializeFromJson(pTeamJSON, true);
											pDatabaseManager->StoreData(DownloadedTeam);
										}
									}
								}
							}

							// Download the next batch of players.
							if (PlayerArray.Num() > 0 || TeamArray.Num() > 0)
							{
								DownloadChampionData(InCallback, Page + 1);
								return;
							}
						}
					}

					// Tell the main menu we were successful on the game thread.
					AsyncTask(ENamedThreads::GameThread, [InCallback, WasSuccessful]()
					{
						InCallback.ExecuteIfBound("", WasSuccessful);
					});
				});

				return;
			}
		}

		// Failure condition, should execute only if we did not get valid data from the server. This will tell the menu that we were unsuccessful.
		InCallback.ExecuteIfBound("", false);

	});

	GetChampionData(BUILD_NUMBER, NUM_CHAMPION_DATA_DOCS_PER_PAGE, OnChampionDataDownloadCompleteDelegate, Page);
}

//===============================================================================
//===============================================================================

#if	ENABLE_CHAMPION_DATA_UPLOAD

void UWWRugbyFanHubService::StartPlayerChampionDataUpload()
{
	check(pRugbyGameInstance);

	if (pRugbyGameInstance)
	{
		RUGameDatabaseManager* pDatabaseManager = pRugbyGameInstance->GetGameDatabaseManager();

		if (pDatabaseManager)
		{
			TSharedPtr< FJsonObject > pGameDatabase = LoadGameDB();

			TArray<TSharedPtr<FJsonValue>> JsonPlayers = pGameDatabase->GetArrayField("Players");

			TMap<uint32, TSharedPtr< FJsonObject>> JsonPlayersMap;

			for (auto& pCurrentJson : JsonPlayers)
			{
				TSharedPtr< FJsonObject > pCurrentJsonObject = pCurrentJson->AsObject();
				uint32 LocalID;
				if (pCurrentJsonObject->TryGetNumberField("local_id", LocalID))
				{
					JsonPlayersMap.Add(LocalID, pCurrentJsonObject);
				}
			}

			MabVector<unsigned short> PlayerList;
			pDatabaseManager->LoadIdList<RUDB_PLAYER>(PlayerList);

			std::function<FString(unsigned short)> SerializeLambda = [=](unsigned short CurrentIndex)
			{
				FString OutputString;

				// Load the player data.
				RUDB_PLAYER LoadedPlayer;
				pDatabaseManager->LoadData<RUDB_PLAYER>(LoadedPlayer, CurrentIndex);

				// Serialize it to JSON.
				TSharedPtr< FJsonObject > PlayerJSON = MakeShared<FJsonObject>();
				LoadedPlayer.SerializeToJsonObject(PlayerJSON, true);

				const TSharedPtr< FJsonObject >* ppGameDbJson = JsonPlayersMap.Find(CurrentIndex);

				if (ppGameDbJson)
				{
					TArray<TSharedPtr<FJsonValue>> pChangeList;
					CreateObjectChangeList(*ppGameDbJson, PlayerJSON, pChangeList);

					if (pChangeList.Num() > 0)
					{
						TSharedPtr< FJsonObject > pChangeObject = MakeShared<FJsonObject>();
						pChangeObject->SetArrayField("Changes", pChangeList);
						pChangeObject->SetNumberField("LocalID", CurrentIndex);

						TSharedRef< TJsonWriter<> > Writer = TJsonWriterFactory<>::Create(&OutputString);
						FJsonSerializer::Serialize(pChangeObject.ToSharedRef(), Writer);
					}
				}

				return OutputString;
			};

			// After players is complete:
			FwwHttpRequestComplete PlayerUploadComplete = FwwHttpRequestComplete::CreateLambda([=](FString ResponseBody, bool WasSuccessful) 
			{
				if (WasSuccessful)
				{
					// Everything is going okay, now do the teams.
					StartTeamChampionDataUpload();
					return;
				}

				SIFUIHelpers::ShowLoadingPopup(false);
				SIFUIHelpers::LaunchWarningPopup("CHAMPION DATA UPLOAD FAILED","", TArray<FModalButtonInfo>());
			});

			SIFUIHelpers::ShowLoadingPopup(true, "UPLOADING CHAMPION DATA");

			ProcessChampionDataUploadList(PlayerUploadComplete, WWFanhubDocType::ChampionDataPlayer, PlayerList, pDatabaseManager, 0, 0, SerializeLambda);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::StartTeamChampionDataUpload()
{
	check(pRugbyGameInstance);

	if (pRugbyGameInstance)
	{
		RUGameDatabaseManager* pDatabaseManager = pRugbyGameInstance->GetGameDatabaseManager();

		if (pDatabaseManager)
		{
			TSharedPtr< FJsonObject > pGameDatabase = LoadGameDB();

			TArray<TSharedPtr<FJsonValue>> JsonTeams = pGameDatabase->GetArrayField("Teams");

			TMap<uint32, TSharedPtr< FJsonObject>> JsonTeamsMap;

			for (auto& pCurrentJson : JsonTeams)
			{
				TSharedPtr< FJsonObject > pCurrentJsonObject = pCurrentJson->AsObject();
				uint32 LocalID;
				if (pCurrentJsonObject->TryGetNumberField("local_id", LocalID))
				{
					JsonTeamsMap.Add(LocalID, pCurrentJsonObject);
				}
			}

			MabVector<unsigned short> TeamList;
			pDatabaseManager->LoadIdList<RUDB_TEAM>(TeamList);

			std::function<FString(unsigned short)> SerializeLambda = [=](unsigned short CurrentIndex)
			{
				FString OutputString;

				// Load the team data.
				RUDB_TEAM LoadedTeam;
				pDatabaseManager->LoadData<RUDB_TEAM>(LoadedTeam, CurrentIndex);

				// Serialize it to JSON.
				TSharedPtr< FJsonObject > TeamJSON = LoadedTeam.SerializeToJson(true);

				const TSharedPtr< FJsonObject >* ppGameDbJson = JsonTeamsMap.Find(CurrentIndex);

				if (ppGameDbJson)
				{
					TArray<TSharedPtr<FJsonValue>> pChangeList;
					CreateObjectChangeList(*ppGameDbJson, TeamJSON, pChangeList);

					if (pChangeList.Num() > 0)
					{
						TSharedPtr< FJsonObject > pChangeObject = MakeShared<FJsonObject>();
						pChangeObject->SetArrayField("Changes", pChangeList);
						pChangeObject->SetNumberField("LocalID", CurrentIndex);

						TSharedRef< TJsonWriter<> > Writer = TJsonWriterFactory<>::Create(&OutputString);
						FJsonSerializer::Serialize(pChangeObject.ToSharedRef(), Writer);
					}
				}

				return OutputString;
			};

			FwwHttpRequestComplete TeamUploadComplete = FwwHttpRequestComplete::CreateLambda([=](FString ResponseBody, bool WasSuccessful)
			{
				SIFUIHelpers::ShowLoadingPopup(false);
				if (WasSuccessful)
				{
					// Everything completed successfully.
					SIFUIHelpers::LaunchWarningPopup("CHAMPION DATA UPLOAD COMPLETE", "", TArray<FModalButtonInfo>());
					return;
				}

				SIFUIHelpers::LaunchWarningPopup("CHAMPION DATA UPLOAD FAILED", "", TArray<FModalButtonInfo>());
			});

			ProcessChampionDataUploadList(TeamUploadComplete, WWFanhubDocType::ChampionDataTeam, TeamList, pDatabaseManager, 0, 0, SerializeLambda);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::ProcessChampionDataUploadList(FwwHttpRequestComplete CompletedDelegate, FString DocType, MabVector<unsigned short> IdList, RUGameDatabaseManager* pDatabaseManager, int32 CurrentIndex, int32 FailCounter, std::function<FString(unsigned short)> SerializeLambda)
{
	AsyncTask(ENamedThreads::GameThread, [=]()
	{
		FwwHttpRequestComplete OnChampionDataDownloadCompleteDelegate = FwwHttpRequestComplete::CreateLambda([=](FString ResponseBody, bool WasSuccessful)
		{
			bool bodySuccess = false;

			if (WasSuccessful)
			{
				TSharedPtr<FJsonObject> json = GetJsonObjFromString(ResponseBody);
				if (json.IsValid() && json->Values.Num() > 0)
				{
					bodySuccess = json->GetBoolField("success");

					if (bodySuccess)
					{
						int32 NextIndex = CurrentIndex + 1;
						// Everything is O.K!
						if (NextIndex < IdList.size())
						{
							ProcessChampionDataUploadList(CompletedDelegate, DocType, IdList, pDatabaseManager, NextIndex, FailCounter, SerializeLambda);
						}
						else
						{
							// Completed upload successfully.
							CompletedDelegate.ExecuteIfBound("", true);
						}
						return;
					}
				}
			}

			if (FailCounter > 10)
			{
				// Something went wrong too many times, send back a false.
				CompletedDelegate.ExecuteIfBound("", false);
			}
			else
			{
				// Add one to the fail counter and retry.
				ProcessChampionDataUploadList(CompletedDelegate, DocType, IdList, pDatabaseManager, CurrentIndex, FailCounter + 1, SerializeLambda);
			}
		});

		
		FString LoadingString = FString::Printf(TEXT("UPLOADING CHAMPION DATA %s: %d / %d"), *DocType, CurrentIndex + 1, IdList.size());
		SIFUIHelpers::UpdateLoadingPopupText(LoadingString);

		FString DocJSON = SerializeLambda(IdList[CurrentIndex]);
		if (!DocJSON.IsEmpty())
		{
			UploadChampDoc(DocJSON, DocType, OnChampionDataDownloadCompleteDelegate);
		}
		else
		{
			int32 NextIndex = CurrentIndex + 1;
			// Everything is O.K!
			if (NextIndex < IdList.size())
			{
				ProcessChampionDataUploadList(CompletedDelegate, DocType, IdList, pDatabaseManager, NextIndex, FailCounter, SerializeLambda);
			}
			else
			{
				// Completed upload successfully.
				CompletedDelegate.ExecuteIfBound("", true);
			}
		}

		return;
	});
}
#endif

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::ChangePlayerStatus(FString InServerId, FString InStatus, FServerRequestFinished RefreshScrollBoxDelegate, bool Repopulate /*= false*/)
{
	ClearLastHandleErrorCode();

	ShowLoadingModal(true);

	//FServerRequestFinished hideModalCallback;
	//hideModalCallback.BindUObject(this, &UWWRugbyFanHubService::ShowLoadingModal, false, FString("")); // ELocaleKey::None); #afl_legacy

	ChangeStatus(WWFanhubDocType::Player, InServerId, InStatus,
		FwwHttpRequestComplete::CreateLambda([=](FString ResponseBody, bool WasSuccessful)
	{
		isSuccessfull = WasSuccessful;
		if (WasSuccessful)
		{
			FString SuccessString = "";
			if (CurrentCommunityOption == ECommunityOption::DELETE_UPLOAD)
			{
				SuccessString = "[ID_DELETE_PLAYER_SUCCESS]";
			}
			else if (CurrentCommunityOption == ECommunityOption::SHARE)
			{
				SuccessString = "[ID_SHARE_PLAYER_SUCCESS]";
			}
			else if (CurrentCommunityOption == ECommunityOption::UNSHARE)
			{
				SuccessString = "[ID_UNSHARE_PLAYER_SUCCESS]";
			}

			if (Repopulate)
			{
				FServerRequestFinished RequestFinishedDelegate;
				RequestFinishedDelegate.BindLambda([this, RefreshScrollBoxDelegate, SuccessString]() {
					ShowLoadingModal(false);
					RefreshScrollBoxDelegate.ExecuteIfBound();
					SIFUIHelpers::LaunchWarningPopup(SuccessString, "[ID_COPYRIGHT_HELP]", TArray<FModalButtonInfo>());
				});

				PopulatePlayerList(GetQueryCache(), GetPageCache(), RequestFinishedDelegate);
				return;
			}

			//TArray<FModalButtonInfo> ButtonInfo;

			//FWWUIModalDelegate ConfirmDelegate;
			//ConfirmDelegate.BindLambda([RefreshScrollBoxDelegate](APlayerController* OwningController) {  });

			//ButtonInfo.Add(FModalButtonInfo("OK", ConfirmDelegate));
			SIFUIHelpers::LaunchWarningPopup(SuccessString, "[ID_COPYRIGHT_HELP]", TArray<FModalButtonInfo>());
		}
		else
		{
			HandleErrorCode(ERRORCODE_SERVER_UNAVAILABLE, false);
		}

		// Dropping Modal triggers notification to screen (OnDismissModal, OnEnterScreen etc) so do after we set error
		ShowLoadingModal(false);
	}));
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::ChangeTeamStatus(FString InServerId, FString InStatus, FServerRequestFinished RefreshScrollBoxDelegate, bool Repopulate /*= false*/)
{
	ClearLastHandleErrorCode();

	ShowLoadingModal(true); //ELocaleKey::Loading); #afl_legacy

	//FServerRequestFinished hideModalCallback;
	//hideModalCallback.BindUObject(this, &UWWRugbyFanHubService::ShowLoadingModal, false, FString("")); // ELocaleKey::None); #afl_legacy

	ChangeStatus(WWFanhubDocType::Team, InServerId, InStatus,
		FwwHttpRequestComplete::CreateLambda([=](FString ResponseBody, bool WasSuccessful)
	{
		isSuccessfull = WasSuccessful;
		if (WasSuccessful)
		{
			FString SuccessString = "";
			if (CurrentCommunityOption == ECommunityOption::DELETE_UPLOAD)
			{
				SuccessString = "[ID_DELETE_TEAM_SUCCESS]";
			}
			else if (CurrentCommunityOption == ECommunityOption::SHARE)
			{
				SuccessString = "[ID_SHARE_TEAM_SUCCESS]";
			}
			else if (CurrentCommunityOption == ECommunityOption::UNSHARE)
			{
				SuccessString = "[ID_UNSHARE_TEAM_SUCCESS]";
			}

			if (Repopulate)
			{
				FServerRequestFinished RequestFinishedDelegate;
				RequestFinishedDelegate.BindLambda([this, RefreshScrollBoxDelegate, SuccessString]() {
					ShowLoadingModal(false);
					RefreshScrollBoxDelegate.ExecuteIfBound();
					SIFUIHelpers::LaunchWarningPopup(SuccessString, "[ID_COPYRIGHT_HELP]", TArray<FModalButtonInfo>());
				});

				PopulateTeamList(GetQueryCache(), GetPageCache(), RequestFinishedDelegate);
				return;
			}

			//TArray<FModalButtonInfo> ButtonInfo;

			//FWWUIModalDelegate ConfirmDelegate;
			//ConfirmDelegate.BindLambda([RefreshScrollBoxDelegate](APlayerController* OwningController) {  });

			//ButtonInfo.Add(FModalButtonInfo("OK", ConfirmDelegate));
			SIFUIHelpers::LaunchWarningPopup(SuccessString, "[ID_COPYRIGHT_HELP]", TArray<FModalButtonInfo>());
		}
		else
		{
			HandleErrorCode(ERRORCODE_SERVER_UNAVAILABLE, false);
		}

		ShowLoadingModal(false);
	}));
}

//===============================================================================
//===============================================================================

bool UWWRugbyFanHubService::ChangeStatusModalCallback(APlayerController* OwningController, WWFanhubState InState, FString InServerId, FString InStatus, FServerRequestFinished RefreshScrollBoxDelegate)
{
	// #afl_legacy
	//pRugbyGameInstance->DealMenuAction(SCREEN_POP_MENUSCREEN, ""); // Modals_UI::FanhubContentOptions);

	if (InStatus.Equals("removed"))
	{
		selectedModalOption = 0;
		CurrentCommunityOption = ECommunityOption::DELETE_UPLOAD;
	}
	else if (InStatus.Equals("public"))
	{
		selectedModalOption = 1;
		CurrentCommunityOption = ECommunityOption::SHARE;
	}
	else if (InStatus.Equals("private"))
	{
		selectedModalOption = 2;
		CurrentCommunityOption = ECommunityOption::UNSHARE;
	}
	else
	{
		ensureMsgf(false, TEXT("Unexepected value for InStatus"));
	}

	switch (InState)
	{
	case FS_Players:
		ChangePlayerStatus(InServerId, InStatus, RefreshScrollBoxDelegate, true);
		break;
	case FS_Teams:
		ChangeTeamStatus(InServerId, InStatus, RefreshScrollBoxDelegate, true);
		break;
	case FS_None:
	default:
		break;
	}


	return true;
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::UploadChampDoc(FString InJson, FString DocType, const FwwHttpRequestComplete InCallback /*= FwwHttpRequestComplete()*/)
{
	FString postData = CreateInputData(WWFanhubServicePOST::UploadChampDocument, DocType,
		FString::Printf(TEXT("&version=%s&stat=%s"), *GetPlayerVersion(), *InJson));

	UploadDocument(postData, InCallback, WWFanhubRoutes::ChampionData);
}

//===============================================================================
//===============================================================================

FString UWWRugbyFanHubService::GetClientId()
{
	return CLIENT_ID;
}

//===============================================================================
//===============================================================================

FString UWWRugbyFanHubService::GetPlayerVersion()
{
	return PLAYER_VERSION;
}

//===============================================================================
//===============================================================================

FString UWWRugbyFanHubService::GetTeamVersion()
{
	return TEAM_VERSION;
}

//===============================================================================
//===============================================================================

TSharedPtr<RUDB_PLAYER> UWWRugbyFanHubService::TryGetDownloadedPlayer(FString ServerFootballerID)
{
	TSharedPtr<RUDB_PLAYER> CurrentCardRecPtr;

	if (GetPlayerMap().Contains(ServerFootballerID))
	{
		CurrentCardRecPtr = GetPlayerMap()[ServerFootballerID];
	}
	else
	{
		// This player was not retrieved from the server, but exists on the team, they were probably reported.
		TSharedPtr<RUDB_PLAYER> pMissingPlayer = TSharedPtr<RUDB_PLAYER>(new RUDB_PLAYER());

		if (pMissingPlayer)
		{
			pMissingPlayer->SetFirstName("BLOCKED");
			pMissingPlayer->SetLastName("BLOCKED");
			PlayerMap.Add(ServerFootballerID, pMissingPlayer);
			CurrentCardRecPtr = pMissingPlayer;
		}
	}

	ensureMsgf(CurrentCardRecPtr, TEXT("Server Player CardRec not found"));

	return CurrentCardRecPtr;
}

//===============================================================================
//===============================================================================

TSharedPtr<RUDB_PLAYER> UWWRugbyFanHubService::GetPlayerAtIndex(int InIdx)
{
	if (InIdx >= 0 && InIdx < PlayerMap.Num())
	{
		if (PlayerArrCache.Num() == 0)
		{
			PlayerMap.GenerateValueArray(PlayerArrCache);
		}

		return PlayerArrCache[InIdx];
	}

	return TSharedPtr<RUDB_PLAYER>();
}

//===============================================================================
//===============================================================================

TSharedPtr<RUDB_TEAM> UWWRugbyFanHubService::GetTeamAtIndex(int InIdx)
{
	if (InIdx >= 0 && InIdx < TeamMap.Num())
	{
		if (TeamArrCache.Num() == 0)
		{
			TeamMap.GenerateValueArray(TeamArrCache);
		}

		return TeamArrCache[InIdx];
	}

	return TSharedPtr<RUDB_TEAM>();
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::HandleErrorCode(int InErrorCode, bool DisplayError)
{
	LastHandleErrorCode = InErrorCode;

	TMap<int, FString> msgMap;
	msgMap.Add(ERRORCODE_SERVER_UNAVAILABLE, "[ID_WW_SERVER_UNAVAILABLE]");//"The FanHub servers are currently unavailable.\nPlease try again later.");
	msgMap.Add(ERRORCODE_DOCUMENT_NOT_OWNED, "[ID_BAD_UPLOAD_OWNER]"); //"The Player or Team you are trying to upload is not owned by your fanhub account. Please sign into the account which uploaded the team or player and try again.");
	msgMap.Add(ERRORCODE_USER_PASS_INCORRECT, "[ID_USER_PASS_INCORRECT]"); //"The username or password that you entered is incorrect.");
	msgMap.Add(ERRORCODE_SIGN_UP_EMAIL_EXISTS, "[ID_SIGN_UP_EMAIL_EXISTS]"); //"The email address you have entered already exists.");
	msgMap.Add(ERRORCODE_SIGN_UP_USERNAME_EXISTS, "[ID_SIGN_UP_USERNAME_EXISTS]"); //"The username you have entered already exists.");
	msgMap.Add(ERRORCODE_BAD_USERNAME, "[ID_BAD_USERNAME]");
	msgMap.Add(ERRORCODE_INVALID_EMAIL, "[ID_INVALIDE_EMAIL]");

	if (msgMap.Contains(InErrorCode))
	{
		UE_LOG(LogTemp, Warning, TEXT("UWWFanhubHttpService returned error: %s"), *msgMap[InErrorCode]);

		if (DisplayError)
		{
			if (pRugbyGameInstance)
			{
				//if (InErrorCode == ERRORCODE_SERVER_UNAVAILABLE)
				//{
				//	// Transition back to MainMenu and display error modal
				//	// FIXME : Do we want a title
				//	gameInstance->ShowErrorUIPopup("", ResolveLocaleKey(ELocaleKey::FanhubServerUnavailable).ToString(), AFLGameInstanceState::MainMenu, false);
				//}
				//else
				{
					SIFUIHelpers::LaunchServerResponsePopup(msgMap[InErrorCode]);
				}
			}
		}
	}
	else
	{
		//error codes 41, 42 and anything else comes here
		SIFUIHelpers::LaunchServerResponsePopup("[ID_ACTION_FAILED_UNKNOWN]");
		UE_LOG(LogTemp, Error, TEXT("UWWFanhubHttpService returned an unknown error of code: %d"), InErrorCode);
	}
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::ClearAllMaps()
{
	ClearPlayerMap();
	ClearTeamMap();
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::CacheMaps()
{
	PlayerMapCache.Append(PlayerMap);
	TeamMapCache.Append(TeamMap);
	LastPopulatePageCountCache = LastPopulatePageCount;
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::ResetFromCacheMaps()
{
	ClearAllMaps();

	PlayerMap.Append(PlayerMapCache);
	TeamMap.Append(TeamMapCache);

	ClearCacheMaps();

	LastPopulatePageCount = LastPopulatePageCountCache;
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::ClearCacheMaps()
{
	PlayerMapCache.Empty();
	TeamMapCache.Empty();
	LastPopulatePageCount = 0;
}

//===============================================================================
//===============================================================================

UWWFanHubSearchManager* UWWRugbyFanHubService::GetSearchManager()
{
	if (!SearchManager)
	{
		SearchManager = NewObject<UWWFanHubSearchManager>(this);
	}

	return SearchManager;
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::ConstructAndRunSearch(const FServerRequestFinished InCallback)
{
	CacheMaps();

	ClearLastHandleErrorCode();

	TSharedPtr<FJsonObject> jsonQuery = SearchManager->ConstructJsonQuery();

	switch (SearchManager->GetSearchMode())
	{
	case SearchType_Players:
		PopulatePlayerList(jsonQuery, 0, InCallback);
		break;
	case SearchType_Teams:
		PopulateTeamList(jsonQuery, 0, InCallback);
		break;
	default:
		break;
	}
}

//===============================================================================
//===============================================================================

void UWWRugbyFanHubService::ConstructAndRunSimpleSearch(const FServerRequestFinished InCallback, FString SearchString, bool bMyUploads /*= false*/)
{
	CacheMaps();

	ClearLastHandleErrorCode();

	TSharedPtr<FJsonObject> jsonQuery = SearchManager->ConstructSimpleNameQuery(SearchString);

	if (bMyUploads)
	{
		jsonQuery->SetStringField(WWFanhubColumn::UploaderId, GetWWID());
	}

	switch (SearchManager->GetSearchMode())
	{
	case SearchType_Players:
		PopulatePlayerList(jsonQuery, 0, InCallback);
		break;
	case SearchType_Teams:
		PopulateTeamList(jsonQuery, 0, InCallback);
		break;
	default:
		break;
	}
}

//===============================================================================
//===============================================================================

int UWWRugbyFanHubService::GetSearchMapCount()
{
	int count = 0;
	switch (SearchManager->GetSearchMode())
	{
	case SearchType_Players:
		count = PlayerMap.Num();
		break;
	case SearchType_Teams:
		count = TeamMap.Num();
		break;
	default:
		break;
	}

	return count;
}

#endif

#if PLATFORM_PS4

/*static*/ bool UWWRugbyFanHubService::GetOnlineIdsFromAccountIdsPS4(const TArray<FString>& AccountIds, FOnGetOnlineIds completionCallback)
{
	FOnlineSubsystemPS4* const PS4Subsystem = static_cast<FOnlineSubsystemPS4*>(IOnlineSubsystem::Get(PS4_SUBSYSTEM));

	int32 Result;
	SceUserServiceUserId InitialUser;
	if ((Result = sceUserServiceGetInitialUser(&InitialUser)) != SCE_OK)
	{
		UE_LOG(LogTemp, Error, TEXT("sceUserServiceGetInitialUser failed with error code 0x%08x"), Result);
		return false;
	}

	// GGS Nick PS4 TODO
	//int32 LocalUserIndex = FPS4Application::GetPS4Application()->GetUserIndex(InitialUser);
	//TSharedRef<FUniqueNetIdPS4 const> LocalPlayerId = FUniqueNetIdPS4::Cast(PS4Subsystem->GetIdentityInterface()->GetUniquePlayerId(LocalUserIndex).ToSharedRef());

	TArray<SceNpAccountId> AccountPlatformIds;

	for (auto const& AccountId : AccountIds)
	{
		AccountPlatformIds.AddUnique(PS4StringToAccountId(AccountId));//((SceNpAccountId)FCString::Atoi64(*AccountId));
	}

	FOnIdResolveComplete Delegate;
	Delegate.AddLambda([=](FOnlineIdMap ResolvedIds, bool bNameResolutionSuccessful, FString ErrorString)
	{
		if (!bNameResolutionSuccessful)
		{
			UE_LOG(LogTemp, Error, TEXT("GetOnlineIdsFromAccountIdsPS4 failed: %s"), *ErrorString);
		}

		TMap<FString, FString> ResultMap;

		for (auto const& AccountId : AccountPlatformIds)
		{
			SceNpOnlineId OnlineId;

			if (!ResolvedIds.GetOnlineId(AccountId, OnlineId))
			{
				continue;
			}

			ResultMap.Add(PS4AccountIdToString(AccountId), PS4OnlineIdToString(OnlineId));
		}

		completionCallback.ExecuteIfBound(ResultMap, bNameResolutionSuccessful, ErrorString);
	});
	// GGS Nick PS4 TODO
	//PS4Subsystem->ResolveOnlineIdsAsync(*LocalPlayerId, AccountPlatformIds, Delegate);
	return true;
}

void UWWRugbyFanHubService::StartAsyncRetrieveAccountNamesForPlayers(FSimpleDelegate completionCallback)
{
	TArray<FString> AccountIds;

	//Pull the account IDs out of the incoming data, so that we can process them for display name information
	{
		static_assert(sizeof(SceNpAccountId) == sizeof(uint64), "SceNpAccountId must be the same size as uint64.");

		for (int i = 0; i < CurrentJsonResultArray.Num(); i++)
		{
			TSharedPtr<FJsonObject> JsonObject = CurrentJsonResultArray[i]->AsObject();

			FString creatorDisplayName = JsonObject->GetStringField("creatorDisplayName");
			FString uploaderDisplayName = JsonObject->GetStringField("uploaderDisplayName");

			FString Delim(",");
			FString DisplayName;
			FString AccountID;

			if (creatorDisplayName.Split(Delim, &DisplayName, &AccountID))
			{
				AccountIds.AddUnique(*AccountID);
			}

			if (uploaderDisplayName.Split(Delim, &DisplayName, &AccountID))
			{
				AccountIds.AddUnique(*AccountID);
			}
		}
	}

	FOnGetOnlineIds resultCallback;
	resultCallback.BindUObject(this, &UWWRugbyFanHubService::OnRetrievedAccountNames, completionCallback);

	if (!GetOnlineIdsFromAccountIdsPS4(AccountIds, resultCallback))
	{
		RetrievedAccountNames.Empty();
		completionCallback.ExecuteIfBound();
	}
}

void UWWRugbyFanHubService::StartAsyncRetrieveAccountNamesForTeams(FSimpleDelegate completionCallback)
{
	TArray<FString> AccountIds;

	//Pull the account IDs out of the incoming data, so that we can process them for display name information
	{
		static_assert(sizeof(SceNpAccountId) == sizeof(uint64), "SceNpAccountId must be the same size as uint64.");

		for (int i = 0; i < CurrentJsonResultArray.Num(); i++)
		{
			TSharedPtr<FJsonObject> JsonObject = CurrentJsonResultArray[i]->AsObject();

			FString creatorDisplayName = JsonObject->GetStringField("creatorDisplayName");
			FString uploaderDisplayName = JsonObject->GetStringField("uploaderDisplayName");

			FString Delim(",");
			FString DisplayName;
			FString AccountID;

			if (creatorDisplayName.Split(Delim, &DisplayName, &AccountID))
			{
				AccountIds.AddUnique(*AccountID);
			}

			if (uploaderDisplayName.Split(Delim, &DisplayName, &AccountID))
			{
				AccountIds.AddUnique(*AccountID);
			}
		}
	}

	FOnGetOnlineIds resultCallback;
	resultCallback.BindUObject(this, &UWWRugbyFanHubService::OnRetrievedAccountNames, completionCallback);

	if (!GetOnlineIdsFromAccountIdsPS4(AccountIds, resultCallback))
	{
		RetrievedAccountNames.Empty();
		completionCallback.ExecuteIfBound();
	}
}

void UWWRugbyFanHubService::OnRetrievedAccountNames(const IdUsernameMap& ResultMap, bool bWasSuccessful, FString ErrorString, FSimpleDelegate completionCallback)
{
	// Even if bWasSuccessful indicates a web failure, we may still have cached values
	RetrievedAccountNames = ResultMap;

	completionCallback.ExecuteIfBound();
}

void UWWRugbyFanHubService::CorrectAccountNames(TSharedPtr<RUDB_PLAYER> player)
{
	FString* accountName = nullptr;

	FString createId = player->GetCreatedByAccountID();
	FString createName = player->GetCreatedBy();

	FString uploadId = player->GetUploadedByAccountID();
	FString uploadName = player->GetUploadedBy();

	if (SIFUIHelpers::IsUploaderCreatorAccountFromDifferentPlatform(createId))
	{
		// Created on another console
	}
	else
	{
		accountName = RetrievedAccountNames.Find(createId);
		if (accountName && !accountName->IsEmpty())
		{
			if (!accountName->Equals(createName))
			{
				player->SetCreatedBy(FString::Printf(TEXT("%s,%s"), **accountName, *createId));
			}
		}
	}

	if (SIFUIHelpers::IsUploaderCreatorAccountFromDifferentPlatform(uploadId))
	{
		// Created on another console
	}
	else
	{
		accountName = RetrievedAccountNames.Find(uploadId);
		if (accountName && !accountName->IsEmpty())
		{
			if (!accountName->Equals(uploadName))
			{
				player->SetUploadedBy(FString::Printf(TEXT("%s,%s"), **accountName, *uploadId));
			}
		}
	}
}

void UWWRugbyFanHubService::CorrectAccountNames(TSharedPtr<RUDB_TEAM> team)
{
	FString* accountName = nullptr;

	FString createId = team->GetCreatedByAccountID();
	FString createName = team->GetCreatedBy();

	FString uploadId = team->GetUploadedByAccountID();
	FString uploadName = team->GetUploadedBy();

	if (SIFUIHelpers::IsUploaderCreatorAccountFromDifferentPlatform(createId))
	{
		// Created on another console
	}
	else
	{
		accountName = RetrievedAccountNames.Find(createId);
		if (accountName && !accountName->IsEmpty())
		{
			if (!accountName->Equals(createName))
			{
				team->SetCreatedBy(FString::Printf(TEXT("%s,%s"), **accountName, *createId));
			}
		}
	}

	if (SIFUIHelpers::IsUploaderCreatorAccountFromDifferentPlatform(uploadId))
	{
		// Created on another console
	}
	else
	{
		accountName = RetrievedAccountNames.Find(uploadId);
		if (accountName && !accountName->IsEmpty())
		{
			if (!accountName->Equals(uploadName))
			{
				team->SetUploadedBy(FString::Printf(TEXT("%s,%s"), **accountName, *uploadId));
			}
		}
	}
}

#endif
