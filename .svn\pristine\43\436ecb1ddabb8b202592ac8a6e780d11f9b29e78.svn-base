/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef RUDATABASE_CONSTANTS_H
#define RUDATABASE_CONSTANTS_H

//#rc3_no_trublu const int DB_TEAMID_TRU_BLU					= 1218;
//const int DB_TEAMID_WICKED_WITCH			= 1219;

const int DB_TEAMID_DEMO_A					= 10001;
const int DB_TEAMID_DEMO_B					= 10002;

const int DB_TEAMID_NZ						= 1025;
//const int DB_TEAMID_NZ_R7					= 1220;
//const int DB_TEAMID_NZ_WR7					= 1276;
const int DB_TEAMID_AUSTRALIA				= 1013;
//const int DB_TEAMID_AUSTRALIA_R7			= 1221;
//const int DB_TEAMID_AUSTRALIA_WR7			= 1266;
//const int DB_TEAMID_SOUTHAFRICA				= 1016;
//const int DB_TEAMID_SOUTHAFRICA_R7			= 1234;
//const int DB_TEAMID_SOUTHAFRICA_WR7			= 1279;
const int DB_TEAMID_ENGLAND					= 1016;
//const int DB_TEAMID_ENGLAND_R7				= 1224;
//const int DB_TEAMID_ENGLAND_WR7				= 1270;
const int DB_TEAMID_FRANCE					= 1018;
//const int DB_TEAMID_FRANCE_R7				= 1226;
//const int DB_TEAMID_FRANCE_WR7				= 1272;
//const int DB_TEAMID_NEWCASTLE_FALCONS		= 1105;

//#rc4_no_lomu const int DB_TEAMID_LOMU_ALLSTARS			= 1113;
//const int DB_TEAMID_BOURGOIN				= 1139;
//const int DB_TEAMID_LIONS					= 1180;
//const int DB_TEAMID_BARBARIANS				= 1181;
//#rc4_no_lomu const int DB_TEAMID_LOMU					= 1106;
//const int DB_TEAMID_NSWQUEENS				= 1212;
//const int DB_TEAMID_USA						= 1020;
//const int DB_TEAMID_GEORGIA					= 1009;
//const int DB_TEAMID_TOULON					= 1087;
//const int DB_TEAMID_CLERMONT				= 1080;
//const int DB_TEAMID_REDS					= 1033;
//const int DB_TEAMID_NORTH_HARBOUR			= 1044;

// Super Rugby Teams
//South African
//const int DB_TEAMID_SR_BULLS				= 1023;
//const int DB_TEAMID_SR_CHEETAHS			= 1026;
//const int DB_TEAMID_SR_LIONS				= 1031;
//const int DB_TEAMID_SR_SHARKS				= 1034;
//const int DB_TEAMID_SR_STORMERS				= 1035;
/**
//AU
const int DB_TEAMID_SR_WARATAHS				= 1036;
//const int DB_TEAMID_SR_FORCE				= 1037;
const int DB_TEAMID_SR_BRUMBIES				= 1025;
const int DB_TEAMID_SR_REBELS				= 1032;
const int DB_TEAMID_SR_REDS					= 1033;

//NZ
const int DB_TEAMID_SR_BLUES				= 1024;
const int DB_TEAMID_SR_CHIEFS				= 1027;
const int DB_TEAMID_SR_CRUSADER				= 1028;
const int DB_TEAMID_SR_HIGHLANDERS			= 1029;
const int DB_TEAMID_SR_HURRICANES			= 1030;

//OTHER
//const int DB_TEAMID_SR_SASTRUGI				= 1036;//Generic - Warathas (Australia)
const int DB_TEAMID_SR_PANTHERS				= 1263;//Generic - Jaquares (Argentina)
const int DB_TEAMID_SR_WOLVES				= 1264;//Generic - Mountian wolves (japan)
*/
const int DB_TEAMID_OFFICIALS_1				= 1001;
const int DB_TEAMID_OFFICIALS_2				= 1002;
const int DB_TEAMID_OFFICIALS_3				= 1003;
const int DB_TEAMID_OFFICIALS_4				= 1004;
const int DB_TEAMID_OFFICIALS_5				= 1005;
const int DB_TEAMID_OFFICIALS_6				= 1006;
/*
const int DB_TEAMID_SIDHE_A					= 1148;
const int DB_TEAMID_SIDHE_B					= 1149;
*/

const int DB_TEAMID_NETWORK_START			= 1007;
const int DB_TEAMID_NETWORK_END				= 1029;

const int DB_TEAMID_NETWORK_FIFTEENS		= 1012;
//const int DB_TEAMID_NETWORK_SEVENS			= 1262;

const int DB_TEAMID_WOMENS_START			= 1165;
const int DB_TEAMID_WOMENS_END				= 1192;



const int DB_COMPID_WORLDCUP				= 9901;
const int DB_COMPID_SUPER15					= 9902;
const int DB_COMPID_EUROCHAMP				= 9903;
const int DB_COMPID_QUADNATIONS				= 9904; // Nick DB Remove
const int DB_COMPID_EURONATIONS				= 9905;
const int DB_COMPID_EURO_RUGBY_CLUB			= 9906;
const int DB_COMPID_ITM						= 9907;
const int DB_COMPID_AVIVA					= 9908;
//const int DB_COMPID_BLEDISLOE				= 1013;
//const int DB_COMPID_TOP14					= 1015;
const int DB_COMPID_RABODIRECTPRO			= 9909;
const int DB_COMPID_LIONS_TOUR				= 9910;
const int DB_COMPID_ENDOFYEAR_TOUR			= 9911;
const int DB_COMPID_PRO_D2					= 9912;
const int DB_COMPID_EURONATIONS_CUP			= 9913;
const int DB_COMPID_PACIFIC_CUP				= 9914;
const int DB_COMPID_AFRICAN_CUP 			= 9915;
const int DB_COMPID_SOUTH_AMERICAN_CUP		= 9916;
const int DB_COMPID_SEVENS					= 9917;
const int DB_COMPID_SEVENS_SERIES			= 9918;
const int DB_COMPID_NRC						= 9919;
const int DB_COMPID_ACC						= 9920;
const int DB_COMPID_WOMENS_SEVENS_CHAMP		= 9921;
const int DB_COMPID_WOMENS_SEVENS_INT		= 9922;

const int DB_COUNTRYID_NEWZEALAND			= 9923;
const int DB_COUNTRYID_AUSTRALIA			= 9924;
const int DB_COUNTRYID_SOUTHAFRICA			= 9925;

//Nick DB - Rugby League made up comps
const int DB_COMPID_RL_WORLDCUP				= 1001;
const int DB_COMPID_RL_HERITAGE				= 1002;
const int DB_COMPID_RL_PACIFIC_CUP			= 1003;
const int DB_COMPID_RL_PACIFIC_BOWL			= 1004;
const int DB_COMPID_RL_EURO_CUP				= 1005;
const int DB_COMPID_RL_RIVALS				= 1006;
const int DB_COMPID_RL_ARL					= 1007;
const int DB_COMPID_RL_NSW					= 1008;
const int DB_COMPID_RL_QUEENSLAND			= 1009;
const int DB_COMPID_RL_VICTORIAN			= 1010;
const int DB_COMPID_RL_WA					= 1011;
const int DB_COMPID_RL_SA					= 1012;
const int DB_COMPID_RL_NT					= 1013;
const int DB_COMPID_RL_ELITE				= 1014;
const int DB_COMPID_RL_CHALLENGER			= 1015;
const int DB_COMPID_RL_FRENCH_1				= 1016;
const int DB_COMPID_RL_FRENCH_2				= 1017;
const int DB_COMPID_RL_NZ					= 1018;
const int DB_COMPID_RL_NZ_CHALLENGER		= 1019;
const int DB_COMPID_RL_W_ARL				= 1020;
const int DB_COMPID_RL_W_ELITE				= 1021;
const int DB_COMPID_RL_W_WORLDCUP			= 1022;


//#rc4_no_lomu const int DB_PLAYERID_JONAH_LOMU			= 3605;

// HAKA LEADERS
const int DB_PLAYERID_TJ_PERENARA			= 3062;
const int DB_PLAYERID_AARON_SMITH			= 1999;
const int DB_PLAYERID_KIERAN_READ			= 1017;
const int DB_PLAYERID_NEHE_MILNER_SKUDDER	= 7454;

const int DB_STADIUMID_MEBS				= 1108;
const int DB_STADIUMID_FLAM				= 1105;
const int DB_STADIUMID_SUNC				= 1067;

const int DB_STADIUMID_MURR = 1030;
const int DB_STADIUMID_DEFR = 1064;

// The New Zealand Stadiums
const int DB_STADIUMID_MANA = 1089;
const int DB_STADIUMID_GROW = 1113;
// GGS Nick Crash Eden no longer exists and is used as a default in a few places, chnage to Sydney Football Stadium
// const int DB_STADIUMID_EDEN = 1001;
const int DB_STADIUMID_EDEN = 1011;
const int DB_STADIUMID_FORS = 1111;
const int DB_STADIUMID_MCLE = 1022;
const int DB_STADIUMID_EVEN = 1086;
const int DB_STADIUMID_NORT = 1082;
const int DB_STADIUMID_ROTO = 1081;
const int DB_STADIUMID_INVE = 1080;
const int DB_STADIUMID_TRAF = 1088;
const int DB_STADIUMID_WAIK = 1087;
const int DB_STADIUMID_WEST = 1016;
const int DB_STADIUMID_YARR = 1084;





















// RSA wanted alternative stadiums for when SA plays
const int DB_STADIUMID_ABSA				= 1053;
const int DB_STADIUMID_LOFT				= 1049;
const int DB_STADIUMID_COKE				= 1051;
const int DB_STADIUMID_NEWS				= 1046;
const int DB_STADIUMID_FREE				= 1050;

const int DB_STRIPID_NZ_HOME			= 1001;
const int DB_STRIPID_NZ_AWAY			= 2001;

const int DB_FRANCHISE_SOUTHERN			= 1001;
const int DB_FRANCHISE_NORTHERN			= 1002;

const int DB_REFEREE_STRIPS_BEGIN			= 995;
const int DB_REFEREE_STRIPS_END			= 999;

/// RL3 competitions (dummies).
//const int DB_COMPID_RL3_STATEOFORIGIN		= 9990;
//const int DB_COMPID_RL3_NRL				= 9991;
//const int DB_COMPID_RL3_SL					= 9992;
//const int DB_COMPID_RL3_WORLD_CUP			= 9993;
//const int DB_COMPID_RL3_TRIAL_GAMES_SL		= 9994;
//const int DB_COMPID_RL3_TRIAL_GAMES_NRL	= 9995;
//const int DB_COMPID_RL3_WORLD_CLUB_CHALLENGE = 9996;
//const int DB_COMPID_RL3_COOP_CHAMPIONSHIP	= 9997;
//const int DB_COMPID_RL3_COOP_CHAMPIONSHIP_1= 9998;
//const int DB_COMPID_RL3_FOUNDATION_CUP		= 9999;
//const int DB_COMPID_RL3_CHARITY_SHIELD		= 10000;
//const int DB_COMPID_RL3_KANGAROOS_TOUR		= 10001;
//const int DB_COMPID_RL3_LIONS_TOUR			= 10002;
//const int DB_COMPID_RL3_KIWI_TOUR			= 10003;

const int MINIMUM_CREATED_PLAYER_DB_ID		= 22867;

const int MAX_RUDB_TEAM_SHORT_NAME_LENGTH	= 19;

// Release dates...
const int RELEASE_YEAR						= 2026;
const char RELEASE_DATE_STRING[]			= "2026-01-01";

#endif
