/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/Rules/Triggers/RURuleTriggerConversion.h"

#include "Match/Ball/SSBall.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/SIFGameWorld.h"

MABRUNTIMETYPE_IMP1(RURuleTriggerConversion, RURuleTrigger);

static const float RULE_TRIGGER_DELAY = 0.8f;

static const float CONVERSION_SHORT_COMMENTARY_DELAY = 0.2f;
static const float CONVERSION_BOUNCED_OFF_POST_COMMENTARY_DELAY = 0.1f;
static const float CONVERSION_MISSED_POST_COMMENTARY_DELAY = 0.1f;


RURuleTriggerConversion::RURuleTriggerConversion(RURules* rules)
: RURuleTrigger(rules)
, restart_player(NULL)
, commentary_timer()
, ball_through_post_position( FVector::ZeroVector )
{
	//disabling the commentary on creation, as timers are created enabled even though
	//they have no time source, silly timers.
	commentary_timer.SetEnabled(false);
}

RURuleTriggerConversion::~RURuleTriggerConversion()
{
}

void RURuleTriggerConversion::AttachMonitors()
{
	RUGameEvents* events = game->GetEvents();
	MABASSERTMSG(events,"we need events here");
	events->conversion.Add( this, &RURuleTriggerConversion::Conversion );
	events->ball_bounce.Add( this, &RURuleTriggerConversion::BallBounce );
	events->ball_through_posts.Add( this, &RURuleTriggerConversion::BallThroughPosts );
	events->ball_hits_posts.Add( this, &RURuleTriggerConversion::BallHitPosts );
	events->ball_missed_goal.Add( this, &RURuleTriggerConversion::BallMissesGoal );
}

void RURuleTriggerConversion::DeattachMonitors()
{
	RUGameEvents* events = game->GetEvents();
	if (events)
	{
		events->conversion.Remove(this, &RURuleTriggerConversion::Conversion );
		events->ball_bounce.Remove( this, &RURuleTriggerConversion::BallBounce );
		events->ball_through_posts.Remove( this, &RURuleTriggerConversion::BallThroughPosts );
		events->ball_hits_posts.Remove( this, &RURuleTriggerConversion::BallHitPosts );
		events->ball_missed_goal.Remove( this, &RURuleTriggerConversion::BallMissesGoal );
	}
}

void RURuleTriggerConversion::Enter()
{
	timer.Reset( rules->GetGame()->GetSimTime(), RULE_TRIGGER_DELAY );
}

void RURuleTriggerConversion::Exit()
{
	commentary_timer.SetEnabled(false);
	//
	// This still happens but I've commented out the assert at least for now
	// because it seems harmless.  The NMA that is was supposed to catch has
	// been fixed.  -Charles.
	//
	//MABASSERTMSG( consequence == RUC_INVALID, "RURuleTriggerConversion exiting without starting its consequence! Please tell Charles" );
	consequence = RUC_INVALID;
}

void RURuleTriggerConversion::Update(float)
{
	if(commentary_timer.GetEnabled() && commentary_timer.GetNumTimerEventsRaised() > 0 )
	{
		bool successful = consequence == RUC_CONVERSION_SUCCESS;
		game->GetEvents()->commentary_conversion_result( successful, ball_through_post_position );
		commentary_timer.SetEnabled(false);
	}

	if ( timer.GetNumTimerEventsRaised() > 0 )
	{
		bool successful = consequence == RUC_CONVERSION_SUCCESS;
		RUGameState* state = game->GetGameState();
		state->SetPlayRestartTeam( restart_team );
		state->SetLastConversionSuccess( successful );
		state->SetPlayRestartPosition( FVector(0.0f, 0.0f, 0.0f) );
		game->GetEvents()->conversion_finish( successful );

		//need to ensure that the commentary event for successful conversions is fired after
		//we post the score rather than on its own timer, or it could potentially report with the wrong scores
		if (successful)
		{
			game->GetEvents()->commentary_conversion_result(successful, ball_through_post_position);
		}

		//If this goes off, we've ended up in here twice, please contact Steve or Mitchell
		MABASSERT(consequence != RUC_MAX);
		if (consequence != RUC_MAX)
		{
			rules->StartConsequence(consequence);
		}
		
		consequence = RUC_MAX; //Stops failure code from triggering the commentary
	}
}

void RURuleTriggerConversion::Conversion( bool /*success*/, const FVector& /*position*/ )
{
#ifdef ENABLE_DEBUG_RULE_MONITORING
	MABLOGMSG(LOGCHANNEL_DEBUG, LOGTYPE_INFO, "RulesTrigger Call: RURuleTriggerConversion::Conversion");
#endif
	// Nick  WWS 7s to Womens //
	/*
#ifdef ENABLE_SEVENS_MODE
	//If we're playing a sevens game, the team scoring kicks off, instead of the conceding team
	if (game->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_SEVENS)
	{
		restart_team = game->GetGameState()->GetLastBallHolder()->GetAttributes()->GetTeam();
	}
	else
#endif */
	{
		restart_team = game->GetGameState()->GetLastBallHolder()->GetAttributes()->GetOppositionTeam();
	}

	restart_player = restart_team->GetPlayKicker();
	consequence = RUC_INVALID;
}

void RURuleTriggerConversion::BallBounce( const FVector& position, const FVector& /*velocity*/ )
{
	//we may have multiple events fire during a conversion, only queue ourselves once
	if(rules->GetActiveTrigger() == this || rules->GetPendingTrigger() == this)
		return;

	if ( game->GetGameState()->GetPhase() == RUGamePhase::CONVERSION && consequence == RUC_INVALID )
	{
		consequence = RUC_CONVERSION_FAILURE;
		commentary_timer.Reset( rules->GetGame()->GetSimTime(), CONVERSION_SHORT_COMMENTARY_DELAY);
		ball_through_post_position = position;
		rules->SetTrigger( this );
		MABASSERTMSG( rules->GetPendingTrigger() == this, "RURuleTriggerConversion not pending trigger after ball bounced.  Please tell Charles" );
	}
}

void RURuleTriggerConversion::BallThroughPosts( const FVector& position )
{
	//we may have multiple events fire during a conversion, only queue ourselves once
	if(rules->GetActiveTrigger() == this || rules->GetPendingTrigger() == this)
		return;

	if ( game->GetGameState()->GetPhase() == RUGamePhase::CONVERSION )
	{
		consequence = RUC_CONVERSION_SUCCESS;
		ball_through_post_position = position;
		rules->SetTrigger( this );
		MABASSERTMSG( rules->GetPendingTrigger() == this, "RURuleTriggerConversion not pending trigger after ball through posts.  Please tell Charles" );
	}
}

void RURuleTriggerConversion::BallHitPosts( const FVector& /*position*/ )
{
	//we may have multiple events fire during a conversion, only queue ourselves once
	if(rules->GetActiveTrigger() == this || rules->GetPendingTrigger() == this)
		return;

	if ( game->GetGameState()->GetPhase() == RUGamePhase::CONVERSION && consequence == RUC_INVALID )
	{
		FVector position( FVector::ZeroVector );
		ASSBall::BallThroughPostQueryResult query_result;
		if ( !game->GetBall()->WillBallGoOverPostsOnTheFull(position, query_result) )
		{
			consequence = RUC_CONVERSION_FAILURE;
			commentary_timer.Reset( rules->GetGame()->GetSimTime(), CONVERSION_BOUNCED_OFF_POST_COMMENTARY_DELAY);
			ball_through_post_position = position;
			rules->SetTrigger( this );
			MABASSERTMSG( rules->GetPendingTrigger() == this, "RURuleTriggerConversion not pending trigger after ball hit posts.  Please tell Charles" );
		}
	}
}

void RURuleTriggerConversion::BallMissesGoal( const FVector& position )
{
	//we may have multiple events fire during a conversion, only queue ourselves once
	if(rules->GetActiveTrigger() == this || rules->GetPendingTrigger() == this)
		return;

	if( game->GetGameState()->GetPhase() == RUGamePhase::CONVERSION && consequence == RUC_INVALID )
	{
		consequence = RUC_CONVERSION_FAILURE;
		commentary_timer.Reset( rules->GetGame()->GetSimTime(), CONVERSION_MISSED_POST_COMMENTARY_DELAY);
		ball_through_post_position = position;
		rules->SetTrigger( this );
		MABASSERTMSG( rules->GetPendingTrigger() == this, "RURuleTriggerConversion not pending trigger after ball hit posts.  Please tell Charles" );
	}
}

void RURuleTriggerConversion::Reset()
{
	consequence = RUC_INVALID;
}
