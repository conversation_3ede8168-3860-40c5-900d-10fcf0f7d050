/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/Rules/Triggers/RURuleTriggerDeadBall.h"

#include "Match/Ball/SSBall.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUInputPhaseDecision.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSGameTimer.h"
#include "Match/SSSpatialHelper.h"

MABRUNTIMETYPE_IMP1(RURuleTriggerDeadBall, RURuleTrigger);

RURuleTriggerDeadBall::RURuleTriggerDeadBall(RURules* rules)
: RURuleTrigger(rules)
, restart_player(NULL)
, restart_position(0.0f, 0.0f, 0.0f)
, ball_through_posts(false)
, decision_required(false)
, restart_notified( false )
, offending_player( NULL )
, offending_position( FVector::ZeroVector )
, offending_consequence( RUC_INVALID )
{
	RUGameEvents* events = game->GetEvents();
	MABASSERTMSG(events,"we need events here");
	events->phase_changed.Add(this, &RURuleTriggerDeadBall::OnPhaseChanged);
}

RURuleTriggerDeadBall::~RURuleTriggerDeadBall()
{
	RUGameEvents* events = game->GetEvents();
	if (events)
	{
		events->phase_changed.Remove(this, &RURuleTriggerDeadBall::OnPhaseChanged);
	}
}

void RURuleTriggerDeadBall::AttachMonitors()
{
	RUGameEvents* events = game->GetEvents();
	MABASSERTMSG(events,"we need events here");
	events->ball_dead_detected.Add(this, &RURuleTriggerDeadBall::BallDead);
	events->ball_through_posts.Add(this, &RURuleTriggerDeadBall::DropGoalSuccess);
}

void RURuleTriggerDeadBall::DeattachMonitors()
{
	RUGameEvents* events = game->GetEvents();
	if (events)
	{
		events->ball_dead_detected.Remove(this, &RURuleTriggerDeadBall::BallDead);
		events->ball_through_posts.Remove(this, &RURuleTriggerDeadBall::DropGoalSuccess);
	}
}

void RURuleTriggerDeadBall::Enter()
{
	timer.Reset(rules->GetGame()->GetSimTime(), 0.5f);
}

void RURuleTriggerDeadBall::Update(float)
{
	if(game->GetGameState()->GetPhase() != RUGamePhase::DECISION)
	{
		RUGameState* state = game->GetGameState();

		if (timer.GetNumTimerEventsRaised() > 0)
		{
			if(decision_required && !restart_notified)
			{
				state->SetPlayRestartTeam(restart_team);

				/// Set restart position to a sensible value - used by penalty formation while decision is being made
				float MAX_X = FIELD_WIDTH  * 0.5f - 5.0f;
				float MAX_Z = FIELD_LENGTH * 0.5f - 5.0f;
				MabMath::Clamp( restart_position.x, -MAX_X, MAX_X );
				MabMath::Clamp( restart_position.z, -MAX_Z, MAX_Z );
				state->SetPlayRestartPosition( restart_position );

				restart_notified = true;
			}
			else if ( !decision_required )
			{
				state->SetAttackingTeam(restart_team);

				state->SetPlayRestartTeam(restart_team);
				state->SetPlayRestartPosition(restart_position);

				if (!restart_player)
					restart_player = restart_team->GetPlayKicker();	// Fix wrong team getting the ball and game NMAing.

				state->SetBallHolder(restart_player);

				/// Consequence will start in the cutscene
				// Clear restart player to ensure it doesn't leak between consequences.
				restart_player = NULL;
			}

			NotifyBallDead( offending_player, offending_position, offending_consequence );
		}
	}
}

void RURuleTriggerDeadBall::DropGoalSuccess(const FVector& pos)
{
#ifdef ENABLE_DEBUG_RULE_MONITORING
	MABLOGMSG(LOGCHANNEL_DEBUG, LOGTYPE_INFO, "RulesTrigger Call: RURuleTriggerDeadBall::DropGoalSuccess");
#endif
	MABUNUSED(pos);
	ball_through_posts = true;
}

void RURuleTriggerDeadBall::BallDead(ARugbyCharacter* ball_holder, const FVector& position, bool /*on_full*/)
{
#ifdef ENABLE_DEBUG_RULE_MONITORING
	MABLOGMSG(LOGCHANNEL_DEBUG, LOGTYPE_INFO, "RulesTrigger Call: RURuleTriggerDeadBall::BallDead");
#endif
	if( decision_required ||
		game->GetBall()->GetTryCheck() ||
		game->GetGameState()->GetPhase() == RUGamePhase::CONVERSION ||
		game->GetGameState()->GetPhase() == RUGamePhase::SCRUM ||
		game->GetRules()->IsPlaySuspended())
	{
		return;
	}

	//	When an attacking player sends or carries the ball into the opponents in-goal and it
	//	becomes dead there, either because a defender grounded it or because it went into touch in-
	//	goal or on or over the dead ball line, a drop-out is awarded.


	// Temp only works for running/kicking dead into oppositions deadzone

	MabString message;
	/// TODO : REMOVE manual strings here
	if(ball_holder != 0)
		message = MabString(32, "%s puts the ball dead", ball_holder->GetAttributes()->GetTeam()->GetDbTeam().GetName());
	else
		message = MabString(32, "%s puts the ball dead", game->GetGameState()->GetLastBallHolder()->GetAttributes()->GetTeam()->GetDbTeam().GetName());
	game->GetHUDUpdater()->SetScreenMessage("[ID_DEAD_BALL]", "[ID_DEAD_BALL]", message.c_str(), 0.5f, 3.0f );

	//----------------------------------------------------------------------------
	/// IMPORTANT META INFORMATION for rule calculation
	//----------------------------------------------------------------------------
	BallFreeInfo bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	BallCollectInfo bfc = game->GetStrategyHelper()->GetLastBallCollectInfo();

	ARugbyCharacter* last_played_by_player = NULL;

	// If ball was touched by a player from the team that didn't kick the ball dead, give them the penalty. For ticket #61465.
	if ( bfi.event == BFE_KICK && bfc.event == BCE_CATCH && bfi.last_player->GetAttributes()->GetTeam() != bfc.last_player->GetAttributes()->GetTeam() &&
		!game->GetSpatialHelper()->IsInField( bfc.pos ) )
	{
		 last_played_by_player = bfi.last_player;
	}
	else
	{
		last_played_by_player = ball_holder != NULL ? ball_holder : bfi.last_player;			/// Who last played the ball?
	}

	MABLOGDEBUG( "Last BFI event: %i", bfi.event );
	MABLOGDEBUG( "Last BFC event: %i", bfc.event );

	MABASSERT( last_played_by_player != NULL );
	/// TYRONE :Extra fallback but *should't happen - you should fix the root cause if you get the above assert
	if ( last_played_by_player == NULL )
	{
		last_played_by_player = game->GetGameState()->GetLastBallHolder();
	}

	RUTeam* last_played_by_team = last_played_by_player->GetAttributes()->GetTeam();
	restart_team = static_cast< RUTeam* >( last_played_by_team->GetOppositionTeam() );		// Default and used for cutscene player selection - do not remove

	/// Relative to who last played the ball - where did it go dead?
	bool dead_their_in_goal = MabMath::Sign( (float) last_played_by_team->GetPlayDirection() ) == MabMath::Sign( position.z);
	bool dead_our_in_goal   = !dead_their_in_goal;
	MABUNUSED( dead_our_in_goal );

	RUGameEvents* events = game->GetEvents();

	//----------------------------------------------------------------------------
	// NO ONE IN POSSESSION - dead ball
	//----------------------------------------------------------------------------
	if(ball_holder == NULL) // ball rolled/went out on full into touch, not carried
	{
		FVector ball_over_posts_pos = FVector::ZeroVector;
		ASSBall::BallThroughPostQueryResult query_result;

		/// SHOT AT GOAL - NO ACTION REQUIRED
		// if drop goal or conversion will go over the posts return.
		if ( (	bfi.sub_type == KICKTYPE_DROPGOAL ||
				bfi.sub_type == KICKTYPE_DROPKICK ||
				bfi.sub_type == KICKTYPE_PLACEKICK)
				&& game->GetBall()->WillBallGoOverPostsOnTheFull(ball_over_posts_pos, query_result) )
		{
			return;
		}

		// LAW 13: Kick-off and Restart Kicks
		// 13.9 BALL GOES INTO THE IN-GOAL

		//	(a) If the ball is kicked into the in-goal without having touched or been touched by a player, the opposing team has three choices:
		//  To ground the ball, or
		//	  To make it dead, or
		//	  To play on.

		//	(b) If the opposing team grounds the ball, or if they make it dead, or if the ball becomes dead by going into touch-in-goal or on or over the dead ball line, they have two choices:
		//    To have a scrum formed at the centre, and they throw in the ball, or
		//	  To have the other team kick off (or drop-out) again.
		if( (bfi.sub_type == KICKTYPE_KICKOFF || bfi.sub_type == KICKTYPE_DROPKICK) )
		{
			MABASSERT( dead_their_in_goal );
			if ( dead_their_in_goal )
			{
				if(RunRugbySevensVariationLawLogic(last_played_by_team, last_played_by_player, position)) return;

				// player kicked the start or 22 kick dead, scrum or re-kick choice
				RUDecisionConsequence con_scrum;
				con_scrum.decision = RUC_SCRUM;
				con_scrum.restart_team = static_cast< RUTeam* >( last_played_by_team->GetOppositionTeam() );
				con_scrum.restart_position = bfi.pos;
				con_scrum.restart_player = NULL;
				con_scrum.decision_event = &events->ball_dead_scrum_decision;

				// WJS RLS ##### Not sure if we have ruc rekicks in league games??
				// I think it's allowed in this case???
				RUDecisionConsequence con_rekick;
				con_rekick.decision = RUC_REKICK;
				con_rekick.restart_team = last_played_by_team;
				con_rekick.restart_position = bfi.pos;
				con_rekick.restart_player = NULL;
				con_rekick.decision_event = &events->ball_dead_rekick_decision;

				RUInputPhaseDecision* decisions = game->GetInputManager()->GetDecisionInterface();
				decisions->AddConsequence(con_scrum);
				decisions->AddConsequence(con_rekick);
				decision_required = true;

				offending_player = last_played_by_player;
				offending_position = position;
				offending_consequence = RUC_INVALID;

				/// Set restart position to a sensible value - used by penalty formation while decision is being made
				restart_position = last_played_by_player->GetMovement()->GetCurrentPosition();

				rules->SetTrigger(this);
				return;
			}
		}

		// When a penalty kick misses and the ball rolls dead, the result should be a 22m drop out.
		if ( bfi.sub_type == KICKTYPE_PLACEKICK )
		{
			restart_position = position;
			restart_position.z = ((FIELD_LENGTH*0.5f) - 22.0f) * -restart_team->GetPlayDirection();
			restart_position.x = 0.0f;
			consequence = RUC_22_DROP_OUT;
			decision_required = false;
			rules->SetTrigger(this);

			offending_player = last_played_by_player;
			offending_position = position;
			offending_consequence = consequence;
			return;
		}

		//  22.8 BALL KICKED DEAD THROUGH IN-GOAL
		//	If a team kicks the ball through their opponents in-goal into touch-in-goal or on or over the
		//	dead ball line, except by an unsuccessful kick at goal or attempted dropped goal, the
		//	defending team has two choices:
		//
		//  To have a drop-out,
		//	  or
		//	To have a scrum at the place where the ball was kicked and they throw in.
		if ( bfi.event == BFE_KICK && dead_their_in_goal )
		{
			RUDecisionConsequence con_scrum;
			con_scrum.decision = RUC_SCRUM;
			con_scrum.restart_team = static_cast< RUTeam* >( last_played_by_team->GetOppositionTeam() );
			con_scrum.restart_position = bfi.pos;
			con_scrum.restart_player = NULL;
			con_scrum.decision_event = &events->ball_dead_scrum_decision;

			RUDecisionConsequence con_22;
			con_22.decision = RUC_22_DROP_OUT;
			con_22.restart_team = static_cast< RUTeam* >( last_played_by_team->GetOppositionTeam() );
			con_22.restart_position = FVector::ZeroVector;
			con_22.restart_position.x = 0.0f;	/// TYRONE - Set position to zero to simpligy AI kick logic/reduce bugs and extra work
			con_22.restart_position.z = ((FIELD_LENGTH*0.5f) - 22) * last_played_by_team->GetPlayDirection();
			con_22.restart_player = NULL;
			con_22.decision_event = &events->ball_dead_dropout_decision;

			RUInputPhaseDecision* decisions = game->GetInputManager()->GetDecisionInterface();
			decisions->AddConsequence(con_scrum);
			decisions->AddConsequence(con_22);
			decision_required = true;

			offending_player = last_played_by_player;
			offending_position = position;
			offending_consequence = RUC_INVALID;

			/// Set restart position to a sensible value - used by penalty formation while decision is being made
			restart_position = last_played_by_player->GetMovement()->GetCurrentPosition();

			rules->SetTrigger(this);
			return;
		}
	}

	//
	//  22.11 BALL DEAD IN IN-GOAL (GENERAL CASE)
	// (a) When the ball touches the touch-in-goal line or the dead ball line, or touches anything or anyone beyond those lines,
	// the ball becomes dead.
	//		If the ball was played into in-goal by the attacking team, a drop-out shall be awarded to the defending team.
	//		If the ball was played into in-goal by the defending team, a 5-metre scrum shall be awarded and the attacking team throws in the ball.
	// (b) When a player carrying the ball touches the touch-in-goal line, the dead ball line, or touches
	// the ground beyond those lines, the ball becomes dead.
	//		If the ball was carried into in-goal by the attacking team, a drop-out shall be awarded to the defending team.
	//		If the ball was carried into in-goal by the defending team, a 5-metre scrum shall be awarded and the attacking team throws in the ball.
	if ( game->GetRules()->PlayingAdvantage() && dead_their_in_goal )	//Rule 12.1(c) implementation.
	{
		//  Duplicate of the else statement code, but think its best to make the distinction.
		restart_position = position;
		restart_position.z = ((game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals().y * 0.5f) - 5.0f) * last_played_by_team->GetPlayDirection();
		MabMath::Clamp( restart_position.x, -FIELD_WIDTH*0.5f + 5.0f, FIELD_WIDTH*0.5f - 5.0f);
		consequence = RUC_SCRUM;
		decision_required = false;
		rules->SetTrigger(this);

		offending_player = last_played_by_player;
		offending_position = position;
		offending_consequence = consequence;
	}
	else if ( dead_their_in_goal )	// Played into in goal by attacking team. Also implements rule 12.1(c).
	{
		//if(RunRugbySevensVariationLawLogic(last_played_by_team, last_played_by_player, position)) return;

		// Drop out consequence
		restart_position = position;
		restart_position.z = ((FIELD_LENGTH*0.5f) - 22.0f) * -restart_team->GetPlayDirection();
		restart_position.x = 0.0f;	/// TYRONE - Set position to zero to simplify AI kick logic/reduce bugs and extra work
		consequence = RUC_22_DROP_OUT;
		decision_required = false;
		rules->SetTrigger(this);

		offending_player = last_played_by_player;
		offending_position = position;
		offending_consequence = consequence;

		// Make very sure the restart team is correct. Critically important, if the wrong team gets the kick it results in an NMA.
		MABASSERT( offending_player );
		restart_team = offending_player ? static_cast<RUTeam*>( offending_player->GetAttributes()->GetTeam()->GetOppositionTeam() ) : restart_team;
		restart_player = restart_team->GetPlayKicker();
	}
	else // Played into in goal by defending team
	{
		// 5 metre scrum consequence
		MABASSERT( dead_our_in_goal );

		restart_position = position;
		restart_position.z = ((game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals().y * 0.5f) - 5.0f) * -last_played_by_team->GetPlayDirection();
		MabMath::Clamp( restart_position.x, -FIELD_WIDTH*0.5f + 5.0f, FIELD_WIDTH*0.5f - 5.0f);
		consequence = RUC_SCRUM;
		decision_required = false;
		rules->SetTrigger(this);

		offending_player = last_played_by_player;
		offending_position = position;
		offending_consequence = consequence;
	}
}

// LAW 13 RUGBY SEVENS VARIATION
// 13.9 Ball goes into the in-goal
//	If the opposing team grounds the ball, or if they make it dead, or if the ball becomes dead by going into touch-in-goal,
// or on or over the dead ball line, a free kick is awarded to the non-offending team at the centre of the half way line.
//		Sanction: Free Kick at the centre of the half way line.
bool RURuleTriggerDeadBall::RunRugbySevensVariationLawLogic(RUTeam * last_played_by_team, ARugbyCharacter* last_played_by_player, const FVector& position)
{
	// Nick WWS 7s to Womens 13s //
	/*
#ifdef ENABLE_SEVENS_MODE
	if(game->GetGameSettings().game_settings.GameModeIsR7())
	{
		offending_player = last_played_by_player;
		RUGameEvents* events = game->GetEvents();

		RUInputPhaseDecision* decisions = game->GetInputManager()->GetDecisionInterface();
		RUDecisionConsequence con_scrum;
		RUDecisionConsequence con_rekick;
		RUDecisionConsequence con_lineout;

		MABASSERT( decisions->GetNumConsequences() == 0 );

		{	// SCRUM DECISION
			con_scrum.decision = RUC_SCRUM;
			con_scrum.restart_team = restart_team;
			con_scrum.restart_position = FVector::ZeroVector;
			con_scrum.restart_player = NULL;
			con_scrum.decision_event = &events->free_kick_scrum_decision;
			decisions->AddConsequence(con_scrum);
		}

		// WJS RLS ##### Not sure if we have ruc rekicks in league games??
		// Might be ok in this case
		{	// REKICK DECISION
			con_rekick.decision = RUC_REKICK;
			con_rekick.restart_team = last_played_by_team;
			con_rekick.restart_position = FVector::ZeroVector;
			con_rekick.restart_player = NULL;
			con_rekick.decision_event = &events->free_kick_rekick_decision;
			decisions->AddConsequence(con_rekick);
		}

		// WJS RLC NOT NEEDED
		/////if(!IS_LEAGUE_GAME()) // No Lineouts in league games
		/////{	// LINEOUT DECISION
		/////	con_lineout.decision = RUC_LINEOUT;
		/////	con_lineout.restart_team = restart_team;
		/////	con_lineout.restart_position = FVector::ZeroVector;
		/////	con_lineout.restart_player = NULL;
		/////	con_lineout.decision_event = &events->free_kick_lineout_decision;
		/////
		/////	// For line outs make sure the line out is 5 meters back from try line
		/////	const static float FIVE_METRES_FROM_TRYLINE = (FIELD_LENGTH * 0.5f) - 5.0f;
		/////	MabMath::Clamp( con_lineout.restart_position.z, -FIVE_METRES_FROM_TRYLINE, +FIVE_METRES_FROM_TRYLINE );
		/////	con_lineout.restart_position.x = game->GetSpatialHelper()->GetFieldExtents().x * (position.x < 0.0f ? -1.0f : 1.0f);
		/////	decisions->AddConsequence(con_lineout);
		/////}
		
		decision_required = true;
		rules->SetTrigger(this);
		return true;
	}
#else */
	MABUNUSED(last_played_by_team);
	MABUNUSED(last_played_by_player);
	MABUNUSED(position);
//#endif

	return false;
}

void RURuleTriggerDeadBall::Reset()
{
	decision_required = false;
	restart_notified = false;
}

void RURuleTriggerDeadBall::OnPhaseChanged()
{
	RUGamePhase current_phase = game->GetGameState()->GetPhase();

	if ( current_phase == RUGamePhase::PLAY || current_phase == RUGamePhase::KICK_OFF || current_phase == RUGamePhase::DROPOUT )
		Reset();
}

void RURuleTriggerDeadBall::NotifyBallDead( ARugbyCharacter* player, const FVector& position, RURuleConsequence inConsequence )
{
	RUGameEvents* events = game->GetEvents();
	events->commentary_ball_dead( player, inConsequence);
	events->rule_trigger_ball_dead( player, position, inConsequence);
}
