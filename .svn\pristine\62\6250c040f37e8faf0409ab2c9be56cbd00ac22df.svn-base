// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Net/UnrealNetwork.h"
//

// Helper macro to make sure a UObject is not NULL and is still valid, we can technically just call IsValidLowLevel() 
// without error but doing so produces quite a lot of debug spam about objects not existing.
#define UOBJ_IS_VALID(pUobj)				((pUobj) != NULL && (pUobj)->IsValidLowLevel() && !((pUobj)->IsPendingKill()))

// Network logging
DECLARE_LOG_CATEGORY_EXTERN(LogNetwork, Verbose, All);
DECLARE_LOG_CATEGORY_EXTERN(LogNetworkNonGame, Verbose, All);
DECLARE_LOG_CATEGORY_EXTERN(LogNetworkSync, Verbose, All);
DECLARE_LOG_CATEGORY_EXTERN(LogNetworkReSync, Verbose, All);
DECLARE_LOG_CATEGORY_EXTERN(LogNetworkChe<PERSON>, Verbose, All);
DECLARE_LOG_CATEGORY_EXTERN(LogNetworkSignal, Verbose, All);
DECLARE_LOG_CATEGORY_EXTERN(LogNetworkRand, Verbose, All);
DECLARE_LOG_CATEGORY_EXTERN(LogNetworkVoip, Verbose, All);
DECLARE_LOG_CATEGORY_EXTERN(LogNetworkPeer, Verbose, All);
DECLARE_LOG_CATEGORY_EXTERN(LogNetworkXbox, Verbose, All);
DECLARE_LOG_CATEGORY_EXTERN(LogDesync, Verbose, All);
DECLARE_LOG_CATEGORY_EXTERN(LogMerge, Verbose, All);

DECLARE_LOG_CATEGORY_EXTERN(LogMab, Verbose, All);

// Face renderer
DECLARE_LOG_CATEGORY_EXTERN(LogFaceRenderer, Verbose, All);

DECLARE_LOG_CATEGORY_EXTERN(LogSqlite3, Verbose, All);

//	Commentary logging
DECLARE_LOG_CATEGORY_EXTERN(LogCommentaryEventInformation, Verbose, All);

/** Set to 1 to pretend we're building for console even on a PC, for testing purposes */
#define SIMULATE_CONSOLE_UI	0

#if PLATFORM_PS4 || PLATFORM_XBOXONE || PLATFORM_SWITCH || SIMULATE_CONSOLE_UI
#define CONSOLE_UI 1
#else
#define CONSOLE_UI 0
#endif

// When not defined prevents compilation of any fanhub code
#define FANHUB_ENABLED


// helper defines for personalised debugging
#if !UE_BUILD_SHIPPING
#define wwDEBUG_ARB 0
#define wwDEBUG_MJB 0
#define wwDEBUG_JG 0
#define wwDEBUG_JO 0
#define wwDEBUG_SRA 0
#define wwGHOSTBALL 1
//#define wwDEBUG_TG // Trent
//#define wwDEBUG_DH // Dewald
#else
#define wwDEBUG_ARB 0
#define wwDEBUG_JG  0
#define wwDEBUG_MJB 0
#define wwDEBUG_JO 0
#define wwDEBUG_SRA 0
#endif

#ifdef wwDEBUG_TG 
#define DISABLE_WORLD_FOR_CLIENT
#endif


// Helpful define for hitting breakpoint in debug.
#if !UE_BUILD_SHIPPING
#define wwDO_NOTHING		do{}while(0)
#else
#define wwDO_NOTHING
#endif

//does certin things to reduce memory at the cost of certin game features
//such as reducing the number of loaded players during the sandbox
#ifndef LOW_MEMORY_MODE
#if PLATFORM_SWITCH
#define LOW_MEMORY_MODE 1
#define ENABLE_MORPH_MERGE 0
#else
#define LOW_MEMORY_MODE 0
#endif
#endif

// GGS Nick - Was '1' disable for now... Problem in Merged meshes, NFI if it looks any different
#ifndef ENABLE_MORPH_MERGE
#define ENABLE_MORPH_MERGE 0
#endif

#ifndef VERBOSE_PLAYER_TEXTURES
#define VERBOSE_PLAYER_TEXTURES 0
#endif

#ifdef DISABLE_FMOD
#ifndef DISABLE_COMMENTARY
#define DISABLE_COMMENTARY 1
#endif
#endif

#ifndef RUN_MORPH_MERGE_ASYNC
#if PLATFORM_SWITCH
#define RUN_MORPH_MERGE_ASYNC 0
#else
#define RUN_MORPH_MERGE_ASYNC 0
#endif
#endif

#ifndef DESTROY_SANDBOX_BEFORE_MATCH
#if PLATFORM_SWITCH
#define DESTROY_SANDBOX_BEFORE_MATCH 1
#else
#define DESTROY_SANDBOX_BEFORE_MATCH 1
#endif
#endif

#ifndef GRASS_ONLY_IN_MAIN_MENU
#if PLATFORM_SWITCH
#define GRASS_ONLY_IN_MAIN_MENU 1
#else
#define GRASS_ONLY_IN_MAIN_MENU 0
#endif
#endif

#ifndef LIMITED_GAME_WORLD_TEAM_SWITCHING
#if PLATFORM_SWITCH
#define LIMITED_GAME_WORLD_TEAM_SWITCHING 1
#else
#define LIMITED_GAME_WORLD_TEAM_SWITCHING 0
#endif
#endif

#ifndef TEAM_LIKENESS_IN_COMP_UI
#if LIMITED_GAME_WORLD_TEAM_SWITCHING
#define TEAM_LIKENESS_IN_COMP_UI 0
#else
#define TEAM_LIKENESS_IN_COMP_UI 1
#endif
#endif

#ifndef ENABLE_SUCCESSFUL_ANKLE_TAPS
	#if UE_BUILD_SHIPPING
		#define ENABLE_SUCCESSFUL_ANKLE_TAPS 1
	#else
		#define ENABLE_SUCCESSFUL_ANKLE_TAPS 1
	#endif
#endif

#if PLATFORM_WINDOWS && (WITH_EDITOR == 0)
//#define ENABLE_STEAMCHECK
#endif

#if !PLATFORM_XBOXONE
#define UE_LOG_NET_NO_XBOX(LogType, Verbosity, Format, ...) \
{ \
	UE_LOG(LogNetwork, Verbosity, Format, ##__VA_ARGS__); \
}
#else
#define UE_LOG_NET_NO_XBOX(...)
#endif

#define USE_ENGINE_4_22_2 0
#define ENABLE_TWO_MAN_TACKLE 0 