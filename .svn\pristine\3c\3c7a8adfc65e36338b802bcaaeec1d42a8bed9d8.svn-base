// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIScreenSquadPostMatch.h"

#include "Rugby/RugbyGameInstance.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"

#include "Rugby/UI/GeneratedHeaders/WWUIScreenSquadPostMatch_UI_Namespace.h"
#include "Rugby/UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "Rugby/UI/WWUICareerGlobal.h"

#include "WWUITranslationManager.h"

#include "Rugby/Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DBTeam.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3CompetitionTeamHelper.h"

#include "Rugby/Match/RugbyUnion/CompetitionMode/RUDBHelperInterface.h"

#include "Rugby/Utility/Helpers/SIFGameHelpers.h"
#include "Rugby/Utility/Helpers/SIFInGameHelpers.h"
#include "Rugby/Utility/Helpers/SIFUIHelpers.h"

#include "UI/Components/WWUIUserWidgetPlayerProfile.h"
#include "UI/Components/WWUIUserWidgetSquadLayout.h"
#include "UI/Components/WWUICustomScrollbar.h"
#include "UI/Screens/Modals/WWUIModalWarningMessage.h"

#include "Rugby/UI/Populators/WWUIPopulatorSquadPostMatch.h"
#include "Rugby/UI/Populators/WWUIPopulatorInGameSquad.h"
#include "Rugby/UI/Screens/WWUIScreenCareerPlayerProfile.h"

#include "WWUITabSwitcher.h"
//#include "WWUILegendBox.h"
#include "WWUIScrollBox.h"
#include "WWUIListField.h"

#include "TextBlock.h"
#include "VerticalBox.h"
#include "HorizontalBox.h"
#include "WidgetTree.h"
#include "ProgressBar.h"
#include "Image.h"
#include "WWUIRichTextBlockWithTranslate.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "WWUIScreenSquadPostMatchProfile.h"

#define TRIGGER_DELAY_TIME		(1.0f)

static void PopulateTargetPlayerName(unsigned short target_id, MabString& name_out)
{
	if (target_id != DB_INVALID_ID)
	{
		RU_PlayerDB_Data player(target_id);

		// We are going to list both the first and last names.
		// We've got tons of room and it's clearer.
		name_out = MabString(0, "%s %s", player.GetFirstName().c_str(), player.GetLastName().c_str());
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::Startup(UWWUIStateScreenData* InData)
{
	// Get the career mode manager
	UWorld* pWorld = GetWorld();
	if (pWorld)
	{
		pRugbyGameInstance = Cast<URugbyGameInstance>(pWorld->GetGameInstance());
		if (pRugbyGameInstance)
		{
			pCareerModeManager = pRugbyGameInstance->GetCareerModeManager();
		}
	}

	if (!pCareerModeManager)
	{
		ensureMsgf(pCareerModeManager, TEXT("CareerSetup: Cannot get the career manager, this should not occur!"));
		return;
	}

	if (!pRugbyGameInstance)
	{
		ensureMsgf(pRugbyGameInstance, TEXT("CareerSetup: Cannot get the rugby game instance, this should not occur!"));
		return;
	}

	CurrentTab = ETempSquadTab::TST_SQUAD;

	InGameTeamSquadBenchStart = 7;

	bIsSomeAccountRestricted = SIFApplication::GetApplication()->IsAnyUserRestricted();
	bIsNonPrimaryRestricted = SIFApplication::GetApplication()->IsNonPrimaryUserRestricted();

	UWWUIStateScreenSquadPostMatchData* pSquadPostMatchData = Cast<UWWUIStateScreenSquadPostMatchData>(InData);

	if (pSquadPostMatchData)
	{
		TeamIndex = pSquadPostMatchData->TeamIndex;
		TeamID = pSquadPostMatchData->TeamID;
		SquadPostMatchAtlasID = pSquadPostMatchData->AtlasID;
		mControllingPlayer = pSquadPostMatchData->ControllingPlayer;
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::RegisterFunctions()
{
	AddInputAction("UI_Back", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenSquadPostMatch::OnBack), false, true);

	AddInputAction("RU_UI_ACTION_TABRIGHT", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenSquadPostMatch::IncrementTab));
	AddInputAction("RU_UI_ACTION_TABLEFT", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenSquadPostMatch::DecrementTab));

	AddInputAction("RU_UI_ACTION_PLAYER_PROFILE", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenSquadPostMatch::SquadPostMatchOnPlayerProfile));
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::OnInFocus()
{
	m_doneFadeIn = false;

	ugcRestrictionHandle = SIFApplication::GetApplication()->OnUGCRestrictionChanged.AddUObject(this, &UWWUIScreenSquadPostMatch::HandleUGCRestrictionChange);

	OnNewTab(CurrentTab);

	UWidget* pPlayerScrollBoxWidget = FindChildWidget(WWUIScreenSquadPostMatch_UI::PlayerScrollBox);
	if (pPlayerScrollBoxWidget)
	{
		SetInitialFocus(pPlayerScrollBoxWidget);
	}

	//Initialise the custom scrollbar
	UWidget* pScrollbar = FindChildWidget(WWUIScreenSquadPostMatch_UI::BP_CustomScrollbar);
	if (pScrollbar)
	{
		if (UWWUICustomScrollbar * customScrollbar = Cast<UWWUICustomScrollbar>(pScrollbar))
		{
			customScrollbar->InitialiseScrollbar();
		}
	}
}

void UWWUIScreenSquadPostMatch::OnOutFocus(bool ShouldOutFocus)
{
	SIFApplication::GetApplication()->OnUGCRestrictionChanged.Remove(ugcRestrictionHandle);
}

//===============================================================================
//===============================================================================


void UWWUIScreenSquadPostMatch::ExecuteTableFunction(FString InTableId, int InIdx, FString InString, FString InActionString)
{
	//context checks done inside the functions
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::TableOnSelectionChange(FString InTableId, int OldIdx, int NewIdx)
{
	SquadPostMatchEventUpdateSquadList(NewIdx);
}

//===============================================================================
//===============================================================================

bool UWWUIScreenSquadPostMatch::OnSystemEvent(WWUINodeProperty& eventProperty)
{
	FString EventName = eventProperty.GetStringProperty("system_event");

	if (EventName == "career_squad_list_update")
	{
		FString ScrollBoxString = WWUIScreenSquadPostMatch_UI::PlayerScrollBox;
		UWWUIScrollBox* pPlayerListScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(ScrollBoxString));

		if (pPlayerListScrollBox)
		{
			SquadPostMatchEventUpdateSquadList(pPlayerListScrollBox->GetSelectedIndex());
		}
	}

	return true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::OnBack(APlayerController* OwningPlayer)
{
	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->DealMenuAction(SCREEN_CANCEL, Screens_UI::SquadPostMatch);
	}
}
//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::IncrementTab(APlayerController* OwningPlayer)
{
	UWWUITabSwitcher* pTabSwitcher = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenSquadPostMatch_UI::TabContainer));

	if (pTabSwitcher)
	{
		pTabSwitcher->IncrementTab();
		OnNewTab((ETempSquadTab)pTabSwitcher->GetActiveTabID());
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::DecrementTab(APlayerController* OwningPlayer)
{
	UWWUITabSwitcher* pTabSwitcher = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenSquadPostMatch_UI::TabContainer));

	if (pTabSwitcher)
	{
		pTabSwitcher->DecrementTab();
		OnNewTab((ETempSquadTab)pTabSwitcher->GetActiveTabID());
	}
}


//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::SetInitialFocus(UWidget* pScrollBoxWidget)
{
	if (pScrollBoxWidget)
	{
		//Find the first element of the main menu and set focus
		UWWUIScrollBox* pCurrentScrollBox = Cast<UWWUIScrollBox>(pScrollBoxWidget);

		if (pCurrentScrollBox)
		{
			APlayerController * playerController = SIFApplication::GetApplication()->GetPlayerControllerFromControllerId(mControllingPlayer);
			if (playerController)
			{
				pCurrentScrollBox->FocusFirstListField(playerController);
				pCurrentScrollBox->SetControllingPlayer(playerController);
			}
			else
			{
				pCurrentScrollBox->FocusFirstListField(SIFApplication::GetApplication()->GetMasterPlayerController());
				pCurrentScrollBox->SetControllingPlayer(SIFApplication::GetApplication()->GetMasterPlayerController());
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::OnNewTab(ETempSquadTab NewMode)
{
	CurrentTab = NewMode;

	OnWindowEnter();
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::OnWindowEnter()
{
	UWidget* pHeaderSubtitleWidget = FindChildWidget(WWUIScreenSquadPostMatch_UI::Subtitle);

	if (pHeaderSubtitleWidget)
	{
		WWUICareerGlobal::SetBreadCrumbFromTeamID(pHeaderSubtitleWidget, TeamID, true);
	}

	switch (CurrentTab)
	{
	case ETempSquadTab::TST_SQUAD:
	{
		SquadPostMatchOnWindowEnter();
	}
	break;
	case ETempSquadTab::TST_PROFILE:
	{
		CareerTeamProfileOnWindowEnter();
	}
	break;
	default:
	{
		ensureMsgf(false, TEXT("Career Team Squad: Was passed an invalid new mode for OnNewTab."));
	}
	break;
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::CareerTeamProfileOnWindowEnter()
{
	DatabaseTeamData = SIFApplication::GetApplication()->GetGameDBHelper()->LoadTeamDBData(TeamID);
	
	CareerTeamProfilePopulateTeamProfile();

	CareerTeamProfilePopulateTeamInfo();

	UWWUIRichTextBlockWithTranslate* pLegend = Cast< UWWUIRichTextBlockWithTranslate>(FindChildWidget(WWUIScreenSquadPostMatch_UI::LegendText));
	if (pLegend)
	{
		pLegend->SetText("[ID_COMPETITION_BACK_HELP]");
	}
	else
	{
		ensure(pLegend);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::CareerTeamProfilePopulateTeamInfo()
{
	if (DatabaseTeamData)
	{
		MabString TeamName = DatabaseTeamData->GetName();

		RUHUDUpdater::CensorTeamName(DatabaseTeamData->GetDbId(), TeamName);

		bool customTeam = SIFGameHelpers::GAGetTeamIsCustom(DatabaseTeamData->GetDbId());

		UTextBlock* pTeamNameText = Cast<UTextBlock>(FindChildWidget(WWUIScreenSquadPostMatch_UI::TextTeamName));

		if (pTeamNameText)
		{
			if (customTeam && ((bIsSomeAccountRestricted && DatabaseTeamData->GetDownloadIdUser() != "") || bIsNonPrimaryRestricted))
			{
				SetWidgetText(pTeamNameText, FText::FromString(UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]")).ToUpper());
			}
			else
			{
				SetWidgetText(pTeamNameText, FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(TeamName)).ToUpper());
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::CareerTeamProfilePopulateTeamProfile()
{
	// Team profile will come from in Data
	// ProfileView LeftPanel

	// Logo
	UImage* pLogoImage = Cast<UImage>(FindChildWidget(WWUIScreenSquadPostMatch_UI::ImageTeamLogo));

	if (pLogoImage)
	{
		UTexture2D* pTexture = nullptr;

		MabString pathString = SIFGameHelpers::GAGetTeamLogoAssetPath(DatabaseTeamData->GetDbId());
		FString name = FString(pathString.c_str());
		pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name));

		if (pTexture)
		{
			pLogoImage->SetBrushFromTexture(pTexture, true);
		}
	}

	//logo bg
	UImage* pBackgroundImage = Cast<UImage>(FindChildWidget(WWUIScreenSquadPostMatch_UI::ImageTeamLogoBkgd));
	SIFUIHelpers::SetImageColourFromTeamColour(pBackgroundImage, DatabaseTeamData->GetDbId());

	// Location
	UTextBlock* pLocationText = Cast<UTextBlock>(FindChildWidget(WWUIScreenSquadPostMatch_UI::TextLocation));

	if (pLocationText)
	{
		unsigned short CountryID = DatabaseTeamData->GetAssociatedCountryId();
		if (CountryID != DB_INVALID_ID)
		{
			RL3DB_COUNTRY Country(CountryID);
			SetWidgetText(pLocationText, FText::FromString(Country.GetName()).ToUpper());
		}
	}

	// Set stadium names
	UVerticalBox* pGroundsVerticalBox = Cast<UVerticalBox>(FindChildWidget(WWUIScreenSquadPostMatch_UI::VerticalBoxGroundsContent));

	if (pGroundsVerticalBox)
	{
		int NumHomeStadiums = DatabaseTeamData->GetNumHomeStadiums();

		for (int i = 0; i < pGroundsVerticalBox->GetChildrenCount(); i++)
		{
			UTextBlock* pCurrentGroundText = Cast<UTextBlock>(pGroundsVerticalBox->GetChildAt(i));

			if (pCurrentGroundText)
			{
				if (i < NumHomeStadiums)
				{
					unsigned short StadiumDatabaseID = DatabaseTeamData->GetHomeStadium(i);
					RL3DB_STADIUM Stadium(StadiumDatabaseID);

					SetWidgetText(pCurrentGroundText, FText::FromString(Stadium.GetName()).ToUpper());
				}
				else
				{
					pCurrentGroundText->SetVisibility(ESlateVisibility::Hidden);
				}
			}
		}
	}

	// ProfileView MiddlePanel
	UTextBlock* pRatingText = Cast<UTextBlock>(FindChildWidget(WWUIScreenSquadPostMatch_UI::TextTeamStarRatingValue));
	UTextBlock* pCaptainText = Cast<UTextBlock>(FindChildOfTemplateWidget(FindChildWidget(WWUIScreenSquadPostMatch_UI::RoleCaptain), WWUIScreenSquadPostMatch_UI::TextRolePlayer));
	UTextBlock* pGoalKickerText = Cast<UTextBlock>(FindChildOfTemplateWidget(FindChildWidget(WWUIScreenSquadPostMatch_UI::RoleGoalKicker), WWUIScreenSquadPostMatch_UI::TextRolePlayer));
	UTextBlock* pPlayKickerText = Cast<UTextBlock>(FindChildOfTemplateWidget(FindChildWidget(WWUIScreenSquadPostMatch_UI::RolePlayKicker), WWUIScreenSquadPostMatch_UI::TextRolePlayer));

	if (pRatingText && pCaptainText != NULL && pGoalKickerText != NULL && pPlayKickerText != NULL)
	{
		RL3DB_TEAM RL3_Team((unsigned short)DatabaseTeamData->GetDbId());
		FString OpponentTeamRating = FString::Printf(TEXT("%0.2f"), RL3_Team.GetNormalisedRanking() * 100.0f);
		pRatingText->SetText(FText::FromString(OpponentTeamRating));

		FString CaptainString = CareerTeamProfileGetTeamPlayerName(DatabaseTeamData->GetCaptain());
		FString GoalKickerString = CareerTeamProfileGetTeamPlayerName(DatabaseTeamData->GetGoalKicker(0));
		FString PlayKickerString = CareerTeamProfileGetTeamPlayerName(DatabaseTeamData->GetPlayKicker(0));

		pCaptainText->SetText(FText::FromString(CaptainString).ToUpper());
		pGoalKickerText->SetText(FText::FromString(GoalKickerString).ToUpper());
		pPlayKickerText->SetText(FText::FromString(PlayKickerString).ToUpper());
	}

	UTextBlock* pScrumWeightText = Cast<UTextBlock>(FindChildWidget(WWUIScreenSquadPostMatch_UI::TextScrumWeight));

	if (pScrumWeightText != NULL)
	{
		const float ScrumWeightTotal = MabMath::FRound(CareerTeamProfileGetTeamScrumWeight());
		pScrumWeightText->SetText(FText::FromString(FString::FromInt(ScrumWeightTotal) + UWWUITranslationManager::Translate("[ID_KILOGRAMS_ABBR]")).ToUpper());
	}

	// Set confidence

	UTextBlock* pConfidenceText = Cast<UTextBlock>(FindChildOfTemplateWidget(FindChildWidget(WWUIScreenSquadPostMatch_UI::ConfidenceStatHorizontal), WWUIScreenSquadPostMatch_UI::TextStatValue));

	UProgressBar* pConfidenceProgressBar = Cast<UProgressBar>(FindChildOfTemplateWidget(FindChildWidget(WWUIScreenSquadPostMatch_UI::ConfidenceStatHorizontal), WWUIScreenSquadPostMatch_UI::ProgressBarStat));

	if (pConfidenceText && pConfidenceProgressBar)
	{
		//Default to 50% if not in a career game
		float ConfidenceValue = 0.5f;

		if (SIFApplication::GetApplication()->GetCareerModeManager()->IsActive())
		{
			unsigned short instance_id = (unsigned short)SIFApplication::GetApplication()->GetCareerModeManager()->GetPlayersActiveCompetition()->GetInstanceId();

			RL3DB_COMPETITION_INSTANCE comp_inst(instance_id);
			ConfidenceValue = DatabaseTeamData->GetCompetitionConfidence(instance_id, comp_inst.GetCurrentSatellite());
		}

		SetWidgetText(pConfidenceText, FText::FromString(FString::FromInt(FMath::RoundToInt(ConfidenceValue * 100.0f))));
		pConfidenceProgressBar->SetPercent(ConfidenceValue);
	}

	// Right panel
	UHorizontalBox* pTeamStatsHorizontalBox = Cast<UHorizontalBox>(FindChildWidget(WWUIScreenSquadPostMatch_UI::HorizontalBoxTeamStats));

	if (pTeamStatsHorizontalBox)
	{
		for (int i = 0; i < pTeamStatsHorizontalBox->GetChildrenCount(); ++i)
		{
			UWidget* pCurrentWidget = pTeamStatsHorizontalBox->GetChildAt(i);
			UProgressBar* pCurrentStatBar = Cast<UProgressBar>(FindChildOfTemplateWidget(pCurrentWidget, WWUIScreenSquadPostMatch_UI::ProgressBarCurrentPlayer));

			UTextBlock* pCurrentStatText = Cast<UTextBlock>(FindChildOfTemplateWidget(pCurrentWidget, WWUIScreenSquadPostMatch_UI::TextStatValue));

			if (pCurrentStatBar && pCurrentStatText)
			{
				pCurrentWidget->SetVisibility(ESlateVisibility::Visible);

				float NormalizeDataValue = CareerTeamProfileGetDatabaseTeamStat(ETempStat(i));

				float RoundedStatValue = FMath::RoundToInt(NormalizeDataValue * 100.0f);

				pCurrentStatBar->SetPercent(NormalizeDataValue);

				pCurrentStatText->SetText(FText::FromString(FString::FromInt(RoundedStatValue)));
			}
			else
			{
				pCurrentWidget->SetVisibility(ESlateVisibility::Collapsed);
			}
		}
	}
}

//===============================================================================
//===============================================================================

FString UWWUIScreenSquadPostMatch::CareerTeamProfileGetTeamPlayerName(unsigned short InID)
{
	MabString name = "";

	PopulateTargetPlayerName(InID, name);

	bool isCustom = false;

	if (bIsSomeAccountRestricted)
	{
		RU_PlayerDB_Data player(InID);
		if (player.GetIsCustom())
		{
			isCustom = true;
		}
	}

	if (isCustom && ((bIsSomeAccountRestricted && DatabaseTeamData->GetDownloadIdUser() != "") || bIsNonPrimaryRestricted))
	{
		return UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]");
	}
	else
	{
		return SIFGameHelpers::GAConvertMabStringToFString(name);
	}
}

//===============================================================================
//===============================================================================

float UWWUIScreenSquadPostMatch::CareerTeamProfileGetTeamScrumWeight()
{
	// The scrum weight is the combined weight of all the forward positions.
	// According to http://en.wikipedia.org/wiki/File:Rugby_formation.svg we can tell if a player is a forward
	// if they have a position between 1 and 8.
	return (float)SIFApplication::GetApplication()->GetCareerModeManager()->GetCompetitionTeamHelper()->GetScrumWeight((unsigned short)DatabaseTeamData->GetDbId());
}

//===============================================================================
//===============================================================================

float UWWUIScreenSquadPostMatch::CareerTeamProfileGetDatabaseTeamStat(ETempStat InStat)
{
	float AttributeValue = 0.0f;

	// Just going to do a big lookup table.

	switch (InStat)
	{
	case ETempStat::TS_ATTACK:
	{
		AttributeValue = DatabaseTeamData->GetNormalisedAttack();
	}
	break;
	case ETempStat::TS_DEFENCE:
	{
		AttributeValue = DatabaseTeamData->GetNormalisedDefence();
	}
	break;
	case ETempStat::TS_RUCK:
	{
		AttributeValue = DatabaseTeamData->GetNormalisedRuckAbility();
	}
	break;
	case ETempStat::TS_SCRUM:
	{
		AttributeValue = DatabaseTeamData->GetNormalisedMaulAbility();
	}
	break;
	case ETempStat::TS_MAUL:
	{
		AttributeValue = DatabaseTeamData->GetNormalisedScrumAbility();
	}
	break;
	case ETempStat::TS_LINE:
	{
		AttributeValue = DatabaseTeamData->GetNormalisedLineoutAbility();
	}
	break;
	}

	return AttributeValue;
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::SquadPostMatchOnWindowEnter()
{
	// Nick  WWS 7s to Womens //
	/*
	if (SIFGameHelpers::GAGetGameMode() == 1)
	{
		InGameTeamSquadTotalPlayers = 12;
		InGameTeamSquadBenchStart = 7;
	}
	else
	{
	*/
		InGameTeamSquadTotalPlayers = 23;
		InGameTeamSquadBenchStart = 15;
	//}

	// load correct team into helper db_team.
	RUDBHelperInterface* pDatabaseHelper = pRugbyGameInstance->GetGameDBHelper();

	if (pDatabaseHelper)
	{
		pDatabaseHelper->LoadTeamDBData(TeamID);
	}
	
	SIFGameHelpers::GASetTeamFaceGenerationEnabled(true);

	// this gets generated on demand using data from populator
	//for i = 1, InGameTeamSquad.total do
	//	InGameTeamSquad.jersey_numbers[i] = i;
	//end

	// Populators
	SquadPostMatchRefreshPlayerList();

	SquadPostMatchUpdateLegend();
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::SquadPostMatchOnPlayerProfile(APlayerController* OwningPlayer)
{
	if (CurrentTab != ETempSquadTab::TST_SQUAD)
	{
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("CareerHUB.MySquadOptionOnClick"));

	//uint32 TeamID_temp = pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch();

	//Team ID that was passed into this screen
	uint32 TeamID_temp = TeamID;

	if (TeamID_temp == 0)
	{
		return;
	}

	SIFGameHelpers::GARequestFaceTeamRender(0, TeamID_temp, true, false);

	uint32 CompetitionID = pCareerModeManager->GetActiveCompetition()->GetInstanceId();
	// Message("GetCurrentPlayerTeamId 3");
	if (CompetitionID == 0)
	{
		return;
	}

	//SRA: Obviously replace this with the array from the career manager when we get it
	TArray<TArray<FString>> attributeChangeArray;
	TArray<FString> tempArray;
	for (int i = 0; i < 4; i++)
	{
		int numActions = FMath::RandRange(2, 5);
		FString tempString = "Attribute ";
		tempString.AppendInt(i);
		tempArray.Add(tempString);

		for (int j = 0; j < numActions; j++)
		{
			tempString = "Action ";
			tempString.AppendInt(j);
			tempArray.Add(tempString);
		}
		attributeChangeArray.Add(tempArray);
		tempArray.Empty();
	}

	UWWUIStateScreenSquadPostMatchProfileData* inData = NewObject<UWWUIStateScreenSquadPostMatchProfileData>();

	inData->PlayerIndex = SquadPostMatchSelectedPlayerIndex;
	inData->PlayerDatabaseID = SquadPostMatchSelectedPlayerDatabaseID;
	inData->TeamID = TeamID_temp;
	inData->AtlasID = SquadPostMatchAtlasID; // For the faces
	inData->attributeChangeArray = attributeChangeArray;
	
	SIFApplication::GetApplication()->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::SquadPostMatchProfile, inData);
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::SquadPostMatchUpdateLegend()
{
	FString AssistanceString = "";

	int32 Total = 0;
	int32 FieldPlayers = 0;

	// Nick  WWS 7s to Womens //
	/*
	if (SIFGameHelpers::GAGetGameMode() == 1)
	{
		Total = 12;
		FieldPlayers = 7;
	}
	else
	{ 
	*/
		Total = 23;
		FieldPlayers = 15;
	//}

	if (SquadPostMatchSelectedPlayerIndex < FieldPlayers)
	{
		uint32 Position = 1 << SquadPostMatchSelectedPlayerIndex;
		AssistanceString = SIFGameHelpers::GAGetPositionText(Position);
	}
	else if (SquadPostMatchSelectedPlayerIndex <= Total)
	{
		AssistanceString = "[ID_SUBSTITUTE]";
	}

	FString help_string = "[ID_COMP_TEAM_MANAGEMENT_READ_ONLY_HELP]";

	UWWUIRichTextBlockWithTranslate* pLegend = Cast< UWWUIRichTextBlockWithTranslate>(FindChildWidget(WWUIScreenSquadPostMatch_UI::LegendText));
	if (pLegend)
	{
		pLegend->SetText(help_string);
	}
	else
	{
		ensure(pLegend);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::SquadPostMatchEventUpdateSquadList(int32 NewIdx)
{
	UWWUIScrollBox* pPlayerListScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenSquadPostMatch_UI::PlayerScrollBox));

	if (pPlayerListScrollBox)
	{
		UWWUIListField* pSelectedListField = Cast<UWWUIListField>(pPlayerListScrollBox->GetListField(NewIdx));

		if (pSelectedListField)
		{
			// Get player id from selected node
			SquadPostMatchSelectedPlayerDatabaseID = pSelectedListField->GetIntProperty(RUCSP_PLAYER_ID);
			SquadPostMatchSelectedPlayerIndex = pSelectedListField->GetIntProperty(RUCSP_PLAYER_LIST_INDEX);

			if (SquadPostMatchSelectedPlayerIndex == MAX_int32 || SquadPostMatchSelectedPlayerDatabaseID == MAX_int32)
			{
				return;
			}

			int32 atlas_index = SquadPostMatchAtlasID; // 0
			// if SquadPostMatch.read_only then
			// atlas_index = 1
			// end

			// Update player stat
			UWidget* pProfileContainer = FindChildWidget(WWUIScreenSquadPostMatch_UI::BP_PlayerProfileAndScrollboxTemplate);
			if (pProfileContainer)
			{
				UWWUIUserWidgetPlayerProfile* pPlayerProfile = Cast<UWWUIUserWidgetPlayerProfile>(FindChildOfTemplateWidget(pProfileContainer, WWUIScreenSquadPostMatch_UI::PlayerProfile));

				if (pPlayerProfile)
				{
					pPlayerProfile->PopulateShortProfile(TeamID, SquadPostMatchSelectedPlayerDatabaseID, 0, SquadPostMatchSelectedPlayerIndex, false, atlas_index, true);
				}
				else { ensure(pPlayerProfile); }
			}
			else { ensure(pProfileContainer); }

			// Update help tip
			SquadPostMatchUpdateLegend();
		}
	}
}

//===============================================================================
//===============================================================================

int32 UWWUIScreenSquadPostMatch::SquadPostMatchGetBenchStart()
{
	return 15; // Nick  WWS 7s to Womens // (SIFGameHelpers::GAGetGameMode() == 1 ? 7 : 15);
}

//===============================================================================
//===============================================================================

void UWWUIScreenSquadPostMatch::SquadPostMatchRefreshPlayerList()
{
	// Populators
	UWWUIScrollBox* pPlayerListScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenSquadPostMatch_UI::PlayerScrollBox));

	if (pPlayerListScrollBox)
	{
		UWWUIPopulatorSquadPostMatch* pCareerSquadPopulator = Cast<UWWUIPopulatorSquadPostMatch>(pPlayerListScrollBox->GetPopulator());

		if (pCareerSquadPopulator)
		{
			pCareerSquadPopulator->SetSelectedPlayerID(SquadPostMatchSelectedPlayerDatabaseID);
		}

		pPlayerListScrollBox->PopulateAndRefresh();

		pPlayerListScrollBox->SetSelectedIndex(SquadPostMatchSelectedPlayerIndex);
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenSquadPostMatch::HandleUGCRestrictionChange(bool bIsRestricted)
{
	bIsSomeAccountRestricted = SIFApplication::GetApplication()->IsAnyUserRestricted();
	bIsNonPrimaryRestricted = SIFApplication::GetApplication()->IsNonPrimaryUserRestricted();

	UWWUITabSwitcher* pTabSwitcher = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenSquadPostMatch_UI::TabContainer));

	if (pTabSwitcher)
	{
		OnNewTab((ETempSquadTab)pTabSwitcher->GetActiveTabID());
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenSquadPostMatch::Update(float DeltaTime)
{
	// #Dewald, because someone is holding onto the transition here, we have to force it off again
	if (!m_doneFadeIn)
	{
		m_doneFadeIn = true;
		if (SIFApplication::GetApplication()->GetMenuGameWorld()->GetCutSceneManager()->IsCinematicRunning())
		{
			SIFApplication::GetApplication()->RequestTransitionHoldFinish(0.5f);
		}
	}
}