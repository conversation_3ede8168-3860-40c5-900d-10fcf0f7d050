// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIScreenTrainingField.h"
#include "UI/GeneratedHeaders/WWUIScreenTrainingField_UI_Namespace.h"

// UI
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "UI/GeneratedHeaders/Animations_UI_Namespace.h"
#include "WWUIFunctionLibrary.h"
#include "Button.h"
#include "TextBlock.h"
#include "WWUIScrollBox.h"
#include "WWUIListField.h"
#include "WidgetSwitcher.h"
#include "ProgressBar.h"
#include "WidgetTree.h"
#include "Button.h"
#include "WWUITabSwitcher.h"
#include "UI/Screens/WWUIScreenPauseMenu.h"

// Populators
#include "../Populators/WWUIPopulatorTrainingLesson.h"

// Rugby
#include "Rugby.h"
#include "RugbyGameInstance.h"
#include "Match/RugbyUnion/TutorialMode/RUTutorialManager.h"
#include "FlowNodes/TrainingFlowNode.h"
#include "Match/RugbyUnion/TutorialMode/RUTutorialType.h"

#include "Match/HUD/RUHUDUpdaterTraining.h"
#include "Match/HUD/RUHUDUpdaterContextual.h"

#include "WWUIScreenPauseMenu.h"
#include "SIFFlowConstants.h"
#include "GameModes/RugbyGameState.h"
#include "Rugby/Utility/Helpers/SIFGameHelpers.h"
#include "Rugby/Utility/Helpers/SIFUIHelpers.h"
#include "Rugby/Match/SIFGameWorld.h"
#include "Rugby/Match/RugbyUnion/RUSandboxGame.h"
#include "Rugby/FlowNodes/FlowControlManager.h"
#include "Utility/Helpers/SIFAudioHelpers.h"
#include "WWUITranslationManager.h"
#include "FlowNodes/SIFMainMenuFlowNode.h"
#include "Utility/SIFSoundConstants.h"
#include "RugbyCameraActor.h"
#include "Modals/WWUIModalWarningMessage.h"
#include "WWUIModalTemplate.h"
#include "Image.h"

#include "Match/Debug/RUGameDebugSettings.h"

////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// DH - adding stuff to get the screen to progress
////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#include "RugbyGameInstance.h"
#include "GameModes/RugbyGameModeBase.h"
#include "Networking/NetworkEnums.h"
#include "Utility/Helpers/SIFUIHelpers.h"
#include "Utility/Helpers/SIFGameHelpers.h"
#include "Utility/Helpers/SIFRumbleHelpers.h"
#include "FlowNodes/FlowControlManager.h"
#include "WWUIRichTextBlockWithTranslate.h"
#include "Utility/Helpers/SIFMatchmakingHelpers.h"
#include "Utility/Helpers/SIFInGameHelpers.h"
#include "Utility/Helpers/SIFPlayerHelpers.h"
#include "Networking/RUNetworkState.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/SSTeam.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/Components/SSHumanPlayer.h"
#include "SIFMissingControllerListener.h"
#include "Character/RugbyPlayerState.h"
#include "Utility/Helpers/SIFRichPresenceHelpers.h"

#if PLATFORM_WINDOWS
#include "drm/MemCheck.h"
#endif

#ifdef UI_USING_UMG
////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::Startup(UWWUIStateScreenData* InData)
{
	//< Initalise the HUDUpdaterTraining >
	RUHUDUpdaterTraining* hudUpdaterTraining = SIFApplication::GetApplication()->GetSandboxGame()->GetGameWorld()->GetHUDUpdaterTraining();
	if (ensureMsgf(hudUpdaterTraining, TEXT("Cannot initalise the RUHUDUpdaterTraining. RUHUDUpdaterTraining is null!")))
	{
		hudUpdaterTraining->Initialise();
	}

	//< Initalise the HUDUpdaterContextual >
	RUHUDUpdaterContextual* hudUpdaterContextual = SIFApplication::GetApplication()->GetSandboxGame()->GetGameWorld()->GetHUDUpdaterContextual();
	if (ensureMsgf(hudUpdaterContextual, TEXT("Cannot initalise the RUHUDUpdaterContextual. RUHUDUpdaterContextual is null!")))
	{
		hudUpdaterContextual->Initialise();
	}

	//< Initalise local variables. >
	tutorialManager = SIFApplication::GetApplication()->GetSandboxGame()->GetGameWorld()->GetTutorialManager();
	LoadScreenWidgets();

	pause_enabled = false;
	
}

void UWWUIScreenTrainingField::Shutdown()
{

	RUHUDUpdaterTraining* hudUpdaterTraining = SIFApplication::GetApplication()->GetSandboxGame()->GetGameWorld()->GetHUDUpdaterTraining();
	if (hudUpdaterTraining)
	{
		hudUpdaterTraining->UnInitialise();
	}

	RUHUDUpdaterContextual* hudUpdaterContextual = SIFApplication::GetApplication()->GetSandboxGame()->GetGameWorld()->GetHUDUpdaterContextual();
	if (hudUpdaterContextual)
	{
		hudUpdaterContextual->Shutdown();
	}


	if (URugbyGameInstance* gameInstance = GetGameInstance())
	{
		gameInstance->OnAllPlayerStatesReadyChanged.Remove(OnLobbyReadyDelegateHandle);
		gameInstance->OnAllConsolesLoadedMatch.Remove(OnMatchReadyDelegateHandle);
		gameInstance->OnAsyncGameLoadComplete.Remove(OnLoadCompleteDelegateHandle);
	}

	UWWUIFunctionLibrary::StopAllNodeAnimations();

#if (PLATFORM_XBOXONE || PLATFORM_XBOX360) //&& defined (GAMECENTRE_ENABLED) 
	SIFGameHelpers::XSSetShouldAllowReturnToStartScreen(true);
#endif

	SIFGameHelpers::GASetTutorialState(false);

	Super::Shutdown();
}

//Adding system events - MB
bool UWWUIScreenTrainingField::OnSystemEvent(WWUINodeProperty & eventProperty)
{
	FString EventName = eventProperty.GetStringProperty("system_event");


	if (EventName.Compare(FString("async_load_finished")) == 0)
	{
		//#MB - moved this code to OnMatchReady
		return true;
	}
	else if (EventName.Compare(FString("onsavestarted")) == 0)
	{
		doing_save = true;
	}
	else if (EventName.Compare(FString("xboxoneuseradded")) == 0 && SIFGameHelpers::GAIsPlatformXboxOne())
	{
		//rc3_legacy_)popup UILaunchPopUpByName("XBSignedInAdded");
	}
	else if (EventName.Compare(FString("onsavetimeelapsed")) == 0)
	{
		doing_save = false;
	}
#if PLATFORM_WINGDK
	else if (EventName.Compare(FString("onsavefinished")) == 0)
	{
		doing_save = false;
	}
	// for winGDK platform, set doing_save to false here, because the following branch doesn't work.
#endif
	else if ((SIFGameHelpers::GAIsPlatformPS4() || SIFGameHelpers::GAIsPlatformXboxOne() || SIFGameHelpers::GAIsPlatformSwitch()) && EventName.Compare(FString("onsavefinished")) == 0)
	{
		doing_save = false;
	}
	else if (EventName.Compare("on_invite_accepted") == 0)
	{
		//#rc3_legacy_popup PopupInviteReceived.ok_enabled = true;
	}
	else if (EventName.Compare("on_game_initialised") == 0)
	{
		StartMulitplayerLoad();
	}
	else if (EventName.Compare("set_team") == 0)
	{
		/*local loading_node = UIGetNode(MultiplayerTrainingField.LOADING_ANIMATION)
		UINodeSetVisible(loading_node, false)

		MultiplayerTrainingField.actions_disabled = false;

		UINodeSetVisible(UIGetNode("RootMenuWindow/MultiplayerLobby/HelpButtonTemplate/Text"), true)

		local pause_loading_spokes = UIGetNode("RootMenuWindow/MultiplayerTrainingFieldPause/LoadingAnimation");
		local pause_quit_option = UIGetNode(MultiplayerTrainingFieldPause.LIST_BOX.."/Quit");

		pause_loading_spokes.visible = false;
		pause_quit_option.selectable = true
		UINodeSetAlpha(pause_quit_option, 1)*/

		int32 TeamIndex = eventProperty.GetIntProperty("team_index");

		if (TeamIndex == INT32_MAX)
		{
			ensureMsgf(false, TEXT("Invalid team index for system event 'set team'"));
		}

		int32 TeamDatabaseID = eventProperty.GetIntProperty("db_id");

		if (TeamDatabaseID == DB_INVALID_ID || TeamDatabaseID == INT32_MAX)
		{
			ensureMsgf(false, TEXT("Invalid team DB ID for system event 'set team'"));
		}

		SIFGameHelpers::GASetTeam(TeamIndex, TeamDatabaseID);
	}
	return true;
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::LoadScreenWidgets()
{
	//< Load Screen Templates >
	ScreenTemplate = Cast<UUserWidget>(FindChildWidget(WWUIScreenTrainingField_UI::ScreenTemplate));

	//< Widget switchers. >
	TutorialMenuHeaders = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenTrainingField_UI::TutorialMenuHeaders));
	TutorialMenuTitles = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenTrainingField_UI::TutorialMenuTitles));
	TutorialMenuScrollboxes = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenTrainingField_UI::TutorialMenuScrollboxes));

	//< Load Scroll Boxes>
	ScrollBoxCategory = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTrainingField_UI::ScrollBoxCategory));
	ScrollBoxLesson = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenTrainingField_UI::ScrollBoxLesson));

	//< Load Widgets >
	StatusBar = Cast<UUserWidget>(FindChildWidget(WWUIScreenTrainingField_UI::StatusBox));
	HeaderHUD = Cast<UUserWidget>(FindChildWidget(WWUIScreenTrainingField_UI::HeaderHUD));
	FooterHUD = Cast<UUserWidget>(FindChildWidget(WWUIScreenTrainingField_UI::DefaultFooterHidden));
	FooterHUD2 = Cast<UUserWidget>(FindChildWidget(WWUIScreenTrainingField_UI::BP_FooterDefault));
	HelpTip = Cast<UWWUIRichTextBlockWithTranslate>(FindChildWidget(WWUIScreenTrainingField_UI::HelpTip));
	RunAroundHelpText = Cast<UUserWidget>(FindChildWidget(WWUIScreenTrainingField_UI::RunAroundHelpText));

	//< Rolodex >
	Rolodex = Cast<UUserWidget>(FindChildWidget(WWUIScreenTrainingField_UI::Rolodex));

}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::RegisterFunctions()
{
	AddInputAction(FString("RU_UI_ACTION_ENTER_GAME"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTrainingField::OnEnterGame), false, false);
	AddInputAction(FString("RU_PAUSE"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTrainingField::OnStart), false, false);
	AddInputAction(FString("UI_CONFIRM"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTrainingField::OnConfirm), false, false);
	AddInputAction(FString("UI_Back"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTrainingField::OnBack), false, false);
	AddInputAction(FString("RU_UI_ACTION_TUTORIALS"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTrainingField::OnSwitchToTraining), false, false);
#ifdef ENABLE_DEBUG_KEYS
	AddInputAction(FString("RU_DEBUG_MENU_TOGGLE"), FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenTrainingField::OpenDebugWindow));
#endif
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::TableOnSelectionChange(FString InTableId, int OldIdx, int NewIdx)
{
	UE_LOG(LogTemp, Display, TEXT("%s selected index: %d"), *InTableId, NewIdx);

	if (InTableId == WWUIScreenTrainingField_UI::ScrollBoxLesson)
	{
		UpdateCategoryCompletion();
	}
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::ExecuteTableFunction(FString InTableId, int InIdx, FString InAction, FString InActionString)
{
	if (!GetInputEnabled())
	{
		return;
	}

	if (InTableId == WWUIScreenTrainingField_UI::ScrollBoxCategory)
	{
		//< Pull node properties off the list field & pass them onto the lesson scroll box. >
		UWWUIListField* executedListField = ScrollBoxCategory->GetListField(InIdx);
		FString category = executedListField->GetStringProperty("category");
		int tutorial_category = executedListField->GetIntProperty("tutorial_category");

		ScrollBoxLesson->SetProperty("category", &category, PROPERTY_TYPE_FSTRING);
		ScrollBoxLesson->SetProperty("tutorial_category", &tutorial_category, PROPERTY_TYPE_INT);

		// < Transition to the correct screen template & store the category index. >
		SetActiveScreenTemplate(ETrainingScreen::Lesson);
		CurrentCategoryIdx = InIdx;


		// < Refresh lesson populator with the category index. >
		if (ScrollBoxLesson)
		{
			// Tell the populator what category it's using { refresh.
			if (UWWUIPopulatorTrainingLesson* LessonPopulator = Cast<UWWUIPopulatorTrainingLesson>(ScrollBoxLesson->GetPopulator()))
			{
				LessonPopulator->SetCategoryID(RUTutorialGroup(CurrentCategoryIdx));
				ScrollBoxLesson->Refresh();
			}

			// Set focus to the first field in the lesson list.
			if (ScrollBoxLesson)
				ScrollBoxLesson->SetSelectedIndex(0);
		}
	}
	else
	{
		CurrentLessonIdx = InIdx;
		//< Transition from Lesson list into the tutorial. >
		//< See TutorialMenuButtonTemplate.lua - rc3 >

		//< Switch to HUD & Run selected tutorial. >
		SetActiveScreenTemplate(ETrainingScreen::HUD);

		//< Set tutorial state as dirty if already in a DEMO so that it replays the UI fade in animations. >
		if ((RUTutorialState)SIFGameHelpers::GAGetCurrentTutorialState() == RUTutorialState::DEMO)
			GetGameInstance()->GetActiveGameWorld()->GetHUDUpdaterTraining()->SetTutorialStateDirty(true);

		RUTutorialType tutorialIndex = GetTutorialTypeFromFieldID(InIdx);
		RunSelectedTutorial(tutorialIndex);
	}
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::SetFocusForControllers(APlayerController* InController /*= nullptr*/)
{
	InController = GetGameInstance()->GetMasterPlayerController();

	/// Manually handle regaining focus if there was no stored focus widget.
	if (InController)
	{
		if (GetUserFocusedWidget(InController) == nullptr)
		{
			switch (activeTrainingUI)
			{
			case ETrainingScreen::Category:
			{
				if(ScrollBoxCategory->GetListField(CurrentCategoryIdx))
				{
					if (UButton* button = ScrollBoxCategory->GetListField(CurrentCategoryIdx)->GetButtonWidget())
					{
						button->SetUserFocus(InController);
					}
				}
				break;
			}
			case ETrainingScreen::Lesson:
			{
				if(ScrollBoxLesson->GetListField(CurrentLessonIdx))
				{
					if (UButton* button = ScrollBoxLesson->GetListField(CurrentLessonIdx)->GetButtonWidget())
					{
						button->SetUserFocus(InController);
					}
				}
				break;
			}
			case ETrainingScreen::HUD:
			{
				SetUserFocus(InController);
				break;
			}
			default:
				break;
			}
		}
		else
		{
			Super::SetFocusForControllers(InController);
		}
	}
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::SetActiveScreenTemplate(ETrainingScreen inActiveUI)
{
	activeTrainingUI = inActiveUI;
	UImage* overlay = Cast<UImage>(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTrainingField_UI::WhiteOverlay));

	if (TutorialMenuTitles && TutorialMenuTitles->IsValidLowLevel())
	{
		TutorialMenuTitles->SetActiveWidgetIndex((int)inActiveUI);
	}
	if (TutorialMenuHeaders && TutorialMenuHeaders->IsValidLowLevel())
	{
		TutorialMenuHeaders->SetActiveWidgetIndex((int)inActiveUI);
	}
	if (TutorialMenuScrollboxes && TutorialMenuScrollboxes->IsValidLowLevel())
	{
		TutorialMenuScrollboxes->SetActiveWidgetIndex((int)inActiveUI);
	}

	UWWUIFunctionLibrary::SetVisibility(overlay, (inActiveUI == HUD) ? false : true);

	switch (inActiveUI)
	{
	case ETrainingScreen::Category:
	{
		GetGameInstance()->GetActiveGameWorld()->GetHUDUpdaterTraining()->SetTutorialInfoVisible(false);
		ScrollBoxCategory->Refresh();
		ScrollBoxCategory->SetSelectedIndex(CurrentCategoryIdx);
		CurrentLessonIdx = 0;
		SIFUIHelpers::DisableInGameInput(true);
		UpdateOverallCompletion();
		break;
	}
	case ETrainingScreen::Lesson:
	{
		GetGameInstance()->GetActiveGameWorld()->GetHUDUpdaterTraining()->SetTutorialInfoVisible(false);
		ScrollBoxLesson->Refresh();
		ScrollBoxLesson->SetSelectedIndex(CurrentLessonIdx);
		UpdateCategoryCompletion();
		SIFUIHelpers::DisableInGameInput(true);
		break;
	}
	case ETrainingScreen::HUD:
	{
		for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
		{
			if (APlayerController* PlayerController = Cast<APlayerController>(*Iterator))
			{
				SetUserFocus(PlayerController);
			}
		}

		//SetUserFocus(GetOwningPlayer());

		SIFUIHelpers::DisableInGameInput(false);
	}
	default:
		break;
	}

	SetFocusForControllers();
}

void UWWUIScreenTrainingField::SetRolodexIndex(int index, bool animateScroll)
{
	UWWUIScrollBox* RolodexScrollbox = Cast<UWWUIScrollBox>(UWWUIFunctionLibrary::FindChildWidget(Rolodex, WWUIScreenTrainingField_UI::RolodexScrollBox));
	if (RolodexScrollbox)
	{
		RolodexScrollbox->SetSelectedIndex(index, animateScroll);

		//< Get training flow node. >
		URugbyGameInstance* gameInstance = GetGameInstance();
		UFlowControlManager* flow_manager = gameInstance ? gameInstance->GetFlowControlManager() : nullptr;
		UTrainingFlowNode* flow_node = flow_manager ? Cast<UTrainingFlowNode>(flow_manager->FetchNode(RugbyFlowNodeNames::SIF_TRAINING_FLOWNAME)) : nullptr;

		if ((!flow_node || (flow_node && !flow_node->IsEnteringMatch())) && !SIFGameHelpers::GAIsDemoTutorialActive())
		{
			pause_enabled = true;
		}
	}
}

void UWWUIScreenTrainingField::OnConfirmQuitGameCallback()
{
	QuitTutorialsYesOnClick(nullptr);
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////
bool UWWUIScreenTrainingField::QuitTutorialsYesOnClick(APlayerController* OwningPlayer)
{
	SIFApplication::GetApplication()->RequestTransitionStart(0.5f, FWWUIOnTransitionStartComplete::CreateUObject(this, &UWWUIScreenTrainingField::QuitToMainMenuCallback), true);


	return true;
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::ReturnToMainMenu(APlayerController* OwningPlayer)
{
	SIFApplication::GetApplication()->RequestTransitionStart(0.5f, FWWUIOnTransitionStartComplete::CreateUObject(this, &UWWUIScreenTrainingField::QuitToMainMenuCallback), true);
}

void UWWUIScreenTrainingField::QuitToMainMenuCallback()
{
	//We dont want to do this in online games, we are already going back to the main menu so we shouldn't do all this again, or at all.
	if (SIFApplication::GetApplication()->GetOnlineMode() != EOnlineMode::Offline)
	{
		return;
	}

	SIFGameHelpers::GAClearTutorial();
	is_run_around = false;

	ResetState();
	//ReturnToMainMenu(OwningPlayer);

	//< Check if master controller is set, set if it's not. >
	if (SIFUIHelpers::GetCurrentMasterControllerIndex() == INVALID_CONTROLLERID)
	{
		SIFUIHelpers::SetCurrentMasterController(0);
	}

	SIFRumbleHelpers::PauseRumbles(false);
	SIFAudioHelpers::PopMusic();
	SIFGameHelpers::GAClearTutorialCutscene();
	SIFGameHelpers::GAClearTutorial();
	SIFGameHelpers::GAResumeSandboxGame();

	URugbyGameInstance* gameInstance = GetGameInstance();
	if(gameInstance)
	{
		UFlowControlManager* flow_manager = gameInstance != nullptr ? gameInstance->GetFlowControlManager() : nullptr;
		USIFMainMenuFlowNode* mainMenuNode = Cast<USIFMainMenuFlowNode>(flow_manager->FetchNode(RugbyFlowNodeNames::SIF_MAINMENU_FLOWNAME));
		if (mainMenuNode)
		{
			mainMenuNode->SetInitialScreen(InPoints_UI::mainMenu);
			flow_manager->SetActiveNode(RugbyFlowNodeNames::SIF_MAINMENU_FLOWNAME);
		}

#ifdef ENABLE_ANALYTICS
		gameInstance->RegisterFreeRoamAnalyticsData();
#endif
	}
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////
RUTutorialType UWWUIScreenTrainingField::GetTutorialTypeFromFieldID(int fieldID)
{
	// Get list of all current tutorials
	RUTutorialType tutorialType = RUTutorialType::NONE;
	TArray<RUTutorialType> availableTutorials = tutorialManager->GetTutorialMenuList((RUTutorialGroup)CurrentCategoryIdx);

	if (availableTutorials.IsValidIndex(fieldID))
		tutorialType = (RUTutorialType)availableTutorials[fieldID];

	return tutorialType;
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::RunSelectedTutorial(RUTutorialType tutorialType)
{
	//< Clearing any active tutorials. >
	if (SIFGameHelpers::GAIsDemoTutorialActive())
		SIFGameHelpers::GATerminateTutorialDemo();

	SIFRumbleHelpers::ClearRumbles();
	SIFRumbleHelpers::PauseRumbles(true);

	//< Starting tutorial. >
	SIFGameHelpers::GAClearTutorialFeedbackMsg();
	SIFGameHelpers::GASetTutorial((int)tutorialType);
	SIFGameHelpers::GAStartTutorialDemo();

	demoActive = true;

	//< Resume game. >
	SIFGameHelpers::GAResumeSandboxGame();
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::OnInFocus()
{
	SIFApplication::GetApplication()->RequestTransitionHoldFinish(0.5f);
	Super::OnInFocus();

	SIFUIHelpers::ListenToAllControllers();

	//< Copied from training_field.lua : TrainingField.OnWindowEnter >
	UE_LOG(LogTemp, Log, TEXT("TrainingField.OnWindowEnter"));

	SIFGameHelpers::GAResumeSandboxGame();
	URugbyGameInstance* gameInstance = GetGameInstance();
	RUSandboxGame* sandboxGame = gameInstance != nullptr ? gameInstance->GetSandboxGame() : nullptr;

	if (m_bHadControllerDisconnectWhilePaused)
	{
		m_bHadControllerDisconnectWhilePaused = false;

#if PLATFORM_PS4
		UFlowControlManager* flow_manager = gameInstance ? gameInstance->GetFlowControlManager() : nullptr;
		UTrainingFlowNode* flow_node = flow_manager ? Cast<UTrainingFlowNode>(flow_manager->FetchNode(RugbyFlowNodeNames::SIF_TRAINING_FLOWNAME)) : nullptr;
		if (flow_node && flow_node->GetTrainingPrompt() == TrainingPrompt::RunAround)
		{
			if (RUSandboxGame* sandbox_game = SIFApplication::GetApplication()->GetSandboxGame())
			{
				sandbox_game->ResetSandbox();
			}
		}
		else
		{
			SIFUIHelpers::DisableInGameInput(false);
			SIFGameHelpers::GASetCurrentTutorialState((int)RUTutorialState::INTRO);
			SIFGameHelpers::GAClearTutorialFeedbackMsg();
			SIFGameHelpers::GARestartTutorial();
			SIFRumbleHelpers::PauseRumbles(false);
		}
#endif
	}


	//< Don't run setup if returning from pause menu. >
	if (EntryFromTutorialPauseMenu)
	{
		EntryFromTutorialPauseMenu = false;
		return;
	}
	else
	{
		SIFAudioHelpers::PushMusic("event:/sounds/loading.it");
	}

	//< Load screen widgets into member variables. >
	LoadScreenWidgets();

	//< Reset everything to default state >
	if (SuppressReset)
	{
		SuppressReset = false;
	}
	else
	{
		ResetState();
	}

	//< Start asynchronous loading if appropriate >
	UWWUIFunctionLibrary::SetVisibility(FindChildWidget(WWUIScreenTrainingField_UI::LoadingIcon), ESlateVisibility::Collapsed);
	UWWUIFunctionLibrary::SetVisibility(FindChildWidget(WWUIScreenTrainingField_UI::LoadingPercent), ESlateVisibility::Collapsed);

	//HideTutorialCategoryMenu();
	UFlowControlManager* flow_manager = gameInstance ? gameInstance->GetFlowControlManager() : nullptr;
	UTrainingFlowNode* flow_node = flow_manager ? Cast<UTrainingFlowNode>(flow_manager->FetchNode(RugbyFlowNodeNames::SIF_TRAINING_FLOWNAME)) : nullptr;

	int primaryControllerID = gameInstance ? gameInstance->GetPrimaryAccountControllerIndex(): 0;
	RUGameSettings* settings = gameInstance ? gameInstance->GetMatchGameSettings() : nullptr;
	RUGameSettings::RU_HUMAN_PLAYER_SETTINGS* humanPlayerSettings = settings ? settings->GetHumanPlayerSettingsFromControllerId(primaryControllerID) : nullptr;
	int primaryPlayerTeam = humanPlayerSettings ? humanPlayerSettings->team : -1;
	bool isPrimaryPlayerPlaying = primaryPlayerTeam != -1;

	if (flow_node)
	{
		if (flow_node->IsEnteringMatch())
		{
			if (flow_node->IsMatchReady()) //< SP PreMatch Runaround >
			{
				OnMatchReady();
			}
			else //< SP PreMatch Runaround >
			{
				OnLoadCompleteDelegateHandle = gameInstance->OnAsyncGameLoadComplete.AddUObject(this, &UWWUIScreenTrainingField::OnLoadComplete);
				OnMatchReadyDelegateHandle = gameInstance->OnAllConsolesLoadedMatch.AddUObject(this, &UWWUIScreenTrainingField::OnMatchReady);

				//< Run the loading icon. >
				UWWUIFunctionLibrary::SetVisibility(FindChildWidget(WWUIScreenTrainingField_UI::LoadingIcon), ESlateVisibility::Visible);
				UWWUIFunctionLibrary::SetVisibility(FindChildWidget(WWUIScreenTrainingField_UI::LoadingPercent), ESlateVisibility::Visible);
				UWWUIFunctionLibrary::PlayAnimation(this, Animations_UI::BP_UIScreenTrainingField::SpinLoadIcon, 0, 0);
			}

			SetRunAroundHelpTextVisibility(isPrimaryPlayerPlaying, true);
			SetRunAroundHelpText(TEXT("[ID_TRAINING_FIELD_HELP_ASYNC]"));

		}
		else if (flow_node->IsNetworkRunAround()) //< MP PreMatch Runaround >
		{
			//< Server check to see if everyone is lobby ready >
			ARugbyGameState* gameState = Cast<ARugbyGameState>(gameInstance->GetWorld()->GetGameState());
			if (gameState)
			{
				OnLobbyReadyChanged(gameState->IsLobbyAllReady());
			}

			SetRunAroundHelpTextVisibility(isPrimaryPlayerPlaying, true);
			SetRunAroundHelpText(TEXT("[ID_TRAINING_FIELD_HELP_ASYNC]"));

			// Callback to when the whole lobby is ready
			OnLobbyReadyDelegateHandle = gameInstance->OnAllPlayerStatesReadyChanged.AddUObject(this, &UWWUIScreenTrainingField::OnLobbyReadyChanged);
			OnMatchReadyDelegateHandle = gameInstance->OnAllConsolesLoadedMatch.AddUObject(this, &UWWUIScreenTrainingField::OnMatchReady);
		}
		else
		{
			if (flow_node->GetTrainingPrompt() == TrainingPrompt::RunAround) //< SP Runaround >
			{
				pause_enabled = true;

				SetRunAroundHelpTextVisibility(true, true, 0.0f);
				SetRunAroundHelpText(TEXT("[ID_RUN_AROUND_HELP_TEXT]"));

				if (SIFApplication::GetApplication()->GetSandboxGame()
					&& SIFApplication::GetApplication()->GetSandboxGame()->GetGameWorld()
					&& SIFApplication::GetApplication()->GetSandboxGame()->GetGameWorld()->GetGameState()
					&& SIFApplication::GetApplication()->GetSandboxGame()->GetGameWorld()->GetGameState()->GetBallHolder()
					)
				{
					SetPlayerControl();
				}
			}
			else if (flow_node->GetTrainingPrompt() == TrainingPrompt::Tutorial) //< SP Tutorial >
			{
				//< Entering the tutorial screen. >
				DisplayTutorialCategoryMenu();

				UWWUIFunctionLibrary::SetVisibility(FooterHUD, true);
				UWWUIFunctionLibrary::SetVisibility(FooterHUD2, true);
				SetHelpTip("[ID_TRAINING_FIELD_HELP_TUTORIAL_MENU_ON]");
			}
		}
	}

	SIFRichPresenceHelpers::RPSetPresenceMenus();

	/*
	UISetCurrentBackground("")
	--GAResetSandbox();
	*/
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::SetPlayerControl()
{
	SIFGameWorld * sandbox_world = SIFApplication::GetApplication()->GetSandboxGame()->GetGameWorld();
	ARugbyCharacter * ball_holder = sandbox_world->GetGameState()->GetBallHolder();
	if (ball_holder)
	{
		if (ball_holder->GetHumanPlayer() == nullptr)
		{
			if (sandbox_world && sandbox_world->GetTeam(0))
			{
				SSEVDSFormationManager* formationManager = sandbox_world->GetTeam(0)->GetFormationManager();
				// Set up formation... - REMOVED so that freeball assignment could occur
				//formationManager->ForceFormation("Tutorial",1);
				formationManager->Reset();

				
				//SSHumanPlayer * human = sandbox_world->GetTeam(0)->GetHumanSelector().GetNextHumanToAssign(true);
				/*if (human)
				{
					sandbox_world->GetTeam(0)->GetHumanSelector().AssignHumanToPlayer(ball_holder);
					human->SetRugbyCharacter(ball_holder);
					if (ball_holder->GetHumanPlayer() == nullptr)
					{
						UWWUIFunctionLibrary::OnTimer(0.01f, FTimerDelegate::CreateUObject(this, &UWWUIScreenTrainingField::SetPlayerControl));
					}
				}*/
			}
		}
	}
	else
	{
		UWWUIFunctionLibrary::OnTimer(0.01f, FTimerDelegate::CreateUObject(this, &UWWUIScreenTrainingField::SetPlayerControl));
	}
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::OnOutFocus(bool ShouldOutFocus)
{
	Super::OnOutFocus(ShouldOutFocus);
	//UWWUIFunctionLibrary::StopAllNodeAnimations();

	/*
	--FMODSignalDSP("TrainingFieldDSP");

	UIFXNodeStopAllAnimations(TrainingField.GameplayHUD)
	*/
	SIFAudioHelpers::SetCategoryVolumeRelToProfile(SIF_SOUND_SPEECH_CATEGORY, 1.0);
	SIFAudioHelpers::SetCategoryVolumeRelToProfile(SIF_SOUND_GAMESFX_CATEGORY, 1.0);


	// Unregister our delegates
	URugbyGameInstance* gameInstance = GetGameInstance();
	UFlowControlManager* flow_manager = gameInstance ? gameInstance->GetFlowControlManager() : nullptr;
	UTrainingFlowNode* flow_node = flow_manager ? Cast<UTrainingFlowNode>(flow_manager->FetchNode(RugbyFlowNodeNames::SIF_TRAINING_FLOWNAME)) : nullptr;
	/*if (flow_node)
	{
		flow_node->m_OnMatchReady.Remove(OnMatchReadyDelegateHandle);
	}*/
}
#endif

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::Update(const float DeltaTime)
{
	//< Update timer if active >
	URugbyGameInstance* gameInstance = SIFApplication::GetApplication();
	if(gameInstance)
	{
		if (ShowCountdownTimer && gameInstance->GetOnlineMode() != EOnlineMode::Offline)
		{
			ARugbyGameState* const MyGameState = Cast<ARugbyGameState>(GetWorld()->GetGameState());
			if (MyGameState && MyGameState->StartingMatch)
			{
				UWWUIFunctionLibrary::SetText(FindChildWidget(WWUIScreenTrainingField_UI::CountdownTimer), FString::FromInt(MyGameState->RemainingTime));
			}
		}

		if(gameInstance->GetMatchGameWorld() && !LoadingFinished)
		{
			int loadPercentComplete = gameInstance->GetMatchGameWorld()->GetLoadCompletionEstimate();
			if (loadPercentComplete >= 100)
			{
				loadPercentComplete = 99;
			}
			UWWUIFunctionLibrary::SetText(FindChildWidget(WWUIScreenTrainingField_UI::LoadingPercent), FString::Printf(TEXT("%i%%"), loadPercentComplete));
		}
	}
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::SetStatusBarVisibility(bool isVisible)
{
	if (isVisible)
	{
		if (!statusBoxDisplayed) //< Transition from hidden to visible. >
		{
			UWWUIFunctionLibrary::PlayAnimation(this, Animations_UI::BP_UIScreenTrainingField::SlideInStatusBox);
			statusBoxDisplayed = true;
		}
	}
	else
	{
		if (statusBoxDisplayed) //< Transition from visible to hidden. >
		{
			UWWUIFunctionLibrary::PlayAnimation(this, Animations_UI::BP_UIScreenTrainingField::SlideInStatusBox, 0, 1, EUMGSequencePlayMode::Reverse);
			statusBoxDisplayed = false;
		}
	}
}

void UWWUIScreenTrainingField::SetStatusBarText(FString _InText)
{
	if (UWWUIRichTextBlockWithTranslate* textBlock = Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindChildWidget(StatusBar, WWUIScreenTrainingField_UI::Text)))
	{
		textBlock->SetText(_InText);
	}
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::SetCountdownVisibility(bool isVisible)
{

}

void UWWUIScreenTrainingField::SetRunAroundHelpTextVisibility(bool isVisible, bool enteringMatch, float animDuration /*= 1.0f*/)
{
	if (enteringMatch)
	{
		UWWUIFunctionLibrary::SetVisibility(FooterHUD, false);
		UWWUIFunctionLibrary::SetVisibility(FooterHUD2, false);
	}

	UWWUIFunctionLibrary::StopNodeAnimation(RunAroundHelpText);
	UWWUIFunctionLibrary::NodeAlignmentTo(RunAroundHelpText, FVector2D(isVisible ? 1 : 0, 0), animDuration);
}

void UWWUIScreenTrainingField::SetRunAroundHelpText(FString _InText)
{
	UWWUIRichTextBlockWithTranslate* textBlock = Cast<UWWUIRichTextBlockWithTranslate>(UWWUIFunctionLibrary::FindChildWidget(RunAroundHelpText, WWUIScreenTrainingField_UI::Text));
	if (textBlock)
		textBlock->SetText(_InText);
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::OnStart(APlayerController* playerController)
{
	if (!SIFGameHelpers::IsPrimaryLocalPlayerController(playerController))
	{
		return;
	}

	//< Try load online match, the match will auto start after load is finished. >
	if (!onlineLobbyReady && !offlineMatchReady)
	{
		if (UOBJ_IS_VALID(playerController) && UOBJ_IS_VALID(playerController->GetLocalPlayer()))
		{
			DisplayPauseMenu(playerController->GetLocalPlayer()->GetControllerId());
		}
	}
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void UWWUIScreenTrainingField::OnConfirm(APlayerController* playerController)
{
	if (!SIFGameHelpers::IsPrimaryLocalPlayerController(playerController))
	{
		return;
	}

	bool debugDemoRecord = false;
#ifdef ENABLE_GAME_DEBUG_MENU
	debugDemoRecord = SIFDebug::GetGameDebugSettings()->GetDemoRecord();
#endif

	if (demoActive && !debugDemoRecord)
	{
		RUTutorialState currentTutorialState = (RUTutorialState)SIFGameHelpers::GAGetCurrentTutorialState();
		if (currentTutorialState == RUTutorialState::DEMO) //-- TUTSTATE_DEMO or TUTSTATE_FULL_DEMO
		{
			SIFUIHelpers::DisableInGameInput(false);
			SIFGameHelpers::GAStartTutorial();
			SIFRumbleHelpers::ClearRumbles();
			SIFRumbleHelpers::PauseRumbles(false);
		}
		else if (currentTutorialState == RUTutorialState::FAIL) //-- TUTSTATE_FAIL
		{
			SIFUIHelpers::DisableInGameInput(false);
			SIFGameHelpers::GASetCurrentTutorialState((int)RUTutorialState::INTRO);
			SIFGameHelpers::GAClearTutorialFeedbackMsg();
			SIFGameHelpers::GARestartTutorial();
			SIFRumbleHelpers::PauseRumbles(false);
		}
		else if (currentTutorialState == RUTutorialState::SUCCESS) //-- TUTSTATE_SUCCESS
		{
			SIFUIHelpers::DisableInGameInput(true);
			SIFGameHelpers::GAClearTutorialFeedbackMsg();
			SIFGameHelpers::GASetCurrentTutorialState((int)RUTutorialState::MENU_SELECT);
		}
	}
}

void UWWUIScreenTrainingField::OnBack(APlayerController* playerController)
{
	URugbyGameInstance* gameInstance = GetGameInstance();
	UFlowControlManager* flow_manager = gameInstance != nullptr ? gameInstance->GetFlowControlManager() : nullptr;
	UTrainingFlowNode* flow_node = flow_manager ? Cast<UTrainingFlowNode>(flow_manager->FetchNode(RugbyFlowNodeNames::SIF_TRAINING_FLOWNAME)) : nullptr;

	if (!SIFGameHelpers::IsPrimaryLocalPlayerController(playerController))
	{
		return;
	}

	if (activeTrainingUI == ETrainingScreen::Lesson)
	{
		SetActiveScreenTemplate(ETrainingScreen::Category);
		if (ScrollBoxCategory)
		{
			ScrollBoxCategory->SetSelectedIndex(CurrentCategoryIdx);
		}

		UWWUIFunctionLibrary::SetVisibility(FooterHUD, true);
		UWWUIFunctionLibrary::SetVisibility(FooterHUD2, true);

#if PLATFORM_WINDOWS
		SetHelpTip("[ID_TRAINING_FIELD_PAUSE_HELP_TEXT]");
#else
		SetHelpTip("[ID_TRAINING_FIELD_HELP_TUTORIAL_MENU_ON]");
#endif

	}
	else if (activeTrainingUI == ETrainingScreen::Category)
	{
		int tut_state = SIFGameHelpers::GAGetCurrentTutorialState();
		bool valid_popup_state = false;

		SetTutorialElementsVisible(true);

		//< Check for valid popup states. >
		TArray<int> popup_states = { 0, 7, 8, 9 };
		for (int i = 0; i < popup_states.Num(); i++)
		{
			if (popup_states[i] == tut_state)
				valid_popup_state = true;
		}

		//< If we're in async flow, never show a popup. >
		if (flow_node && flow_node->IsEnteringMatch())
			valid_popup_state = false;

		if (valid_popup_state)
		{
			//< Launch Quit Tutorials PopUp. >
			UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();
			TArray<FModalButtonInfo> ButtonData;
			FString PopupString = "[ID_TUTORIAL_EXIT_POPUP_TEXT]";
			FString LegendString = "[ID_TM_CHANGE_POSITION_HELP]";

			FWWUIModalDelegate yesDelegate;
			yesDelegate.BindUObject(this, &UWWUIScreenTrainingField::QuitTutorialsYesOnClick);
			ButtonData.Add(FModalButtonInfo(FText::FromString(UWWUITranslationManager::Translate("[ID_YES]")), yesDelegate));

			FWWUIModalDelegate noDelegate;
			ButtonData.Add(FModalButtonInfo(FText::FromString(UWWUITranslationManager::Translate("[ID_NO]")), noDelegate));

			modalData->WarningDialogue = PopupString;
			modalData->LegendString = LegendString;
			modalData->ButtonData = ButtonData;

			modalData->CloseOnBackButton = true;
			modalData->CloseOnSelectButton = true;

			URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
			if (pRugbyGameInstance)
			{
				pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
			}
		}
		else
		{
			SetActiveScreenTemplate(ETrainingScreen::HUD);

			UWWUIFunctionLibrary::SetVisibility(FooterHUD, false);
			UWWUIFunctionLibrary::SetVisibility(FooterHUD2, false);

			//< If we're in async flow, never show a popup. >
			RUTutorialState currentState = (RUTutorialState)SIFGameHelpers::GAGetCurrentTutorialState();
			if (currentState == RUTutorialState::NONE || currentState == RUTutorialState::MENU_SELECT)
			{
				gameInstance->GetActiveGameWorld()->GetHUDUpdaterTraining()->SetTutorialInfoVisible(false);
				UWWUIFunctionLibrary::SetVisibility(RunAroundHelpText, true);
			}
			else
			{
				gameInstance->GetActiveGameWorld()->GetHUDUpdaterTraining()->SetTutorialInfoVisible(true);
				UWWUIFunctionLibrary::SetVisibility(RunAroundHelpText, false);
			}


			CurrentHelpTextVisible = false;
			CurrentHelpText.Empty();

			SIFUIHelpers::DisableInGameInput(false);
			SIFGameHelpers::GAResumeSandboxGame();

			if (!flow_node || (flow_node && !flow_node->IsEnteringMatch()))
			{
				pause_enabled = true;
			}
		}
	}
	//< Waiting for online match >
	else if (flow_node->IsNetworkRunAround())
	{
		APlayerController* player = GetOwningPlayer();
		ARugbyPlayerController* owningPlayerController = Cast<ARugbyPlayerController>(player);
		if (owningPlayerController)
		{
			owningPlayerController->SetIsReadyState(false);
		}

		//gameInstance->DealMenuAction(SCREEN_POP_MENUSCREEN, Screens_UI::TrainingField);
		flow_manager->SetActiveNode(RugbyFlowNodeNames::SIF_ONLINEHUB_FLOWNAME);

		//< Set the help tip text. >
		//SetHelpTip("[ID_TRAINING_FIELD_HELP_TUTORIAL_MENU_ON]");
	}
}

bool UWWUIScreenTrainingField::CanSwitchToTraining(APlayerController* playerController)
{
	URugbyGameInstance* pGameInstance = GetGameInstance();
	if (!pGameInstance)
	{
		return false;
	}

	if (!SIFGameHelpers::IsPrimaryLocalPlayerController(playerController))
	{
		return false;
	}

	RUGameSettings* pMatchSettings = pGameInstance->GetMatchGameSettings();
	UFlowControlManager* pFlowManager = pGameInstance->GetFlowControlManager();
	UTrainingFlowNode* pFlowNode = pFlowManager != nullptr ? Cast<UTrainingFlowNode>(pFlowManager->FetchNode(RugbyFlowNodeNames::SIF_TRAINING_FLOWNAME)) : nullptr;

	if (!pFlowNode)
	{
		return false;
	}

	if (pFlowNode->IsEnteringMatch())
	{
		FRugbyHumanPlayerSettings* pHumanPlayerSettings = pMatchSettings->GetHumanPlayerSettingsFromPlayerId(playerController->PlayerState->GetPlayerId());

		if (!pHumanPlayerSettings)
		{
			return false;
		}

		return pHumanPlayerSettings->team != -1;
	}

	return true;
}

//training_field.lua line 404
void UWWUIScreenTrainingField::OnSwitchToTraining(APlayerController* playerController)
{
	if (!CanSwitchToTraining(playerController))
	{
		return;
	}

	if (SIFGameHelpers::GAGetDisabledTutorials())
	{
		//Tutorials are disabled
		return;
	}
	if (activeTrainingUI != ETrainingScreen::HUD)
		return;

#if (PLATFORM_XBOXONE)
	if (!SIFApplication::GetApplication()->GetPrimaryAccountPlayer())
	{
		return;
	}
#endif	

	int STATE_NONE = 0;

	int STATE_TUTORIAL_PAN = SIFGameHelpers::GAGetCutsceneStateTutorialPan();
	int cutscene_state = SIFGameHelpers::GAGetCutsceneState();

	//If cutscene are still doing bizzare stuff { don't raise the tutorial menu here.
	if (cutscene_state != STATE_NONE && cutscene_state != STATE_TUTORIAL_PAN)
	{
		return;
	}

	int tut_state = SIFGameHelpers::GAGetCurrentTutorialState();

	URugbyGameInstance* gameInstance = GetGameInstance();
	UFlowControlManager* flow_manager = gameInstance != nullptr ? gameInstance->GetFlowControlManager() : nullptr;
	UTrainingFlowNode* flow_node = flow_manager ? Cast<UTrainingFlowNode>(flow_manager->FetchNode(RugbyFlowNodeNames::SIF_TRAINING_FLOWNAME)) : nullptr;

	//If we're in the async loading flow { state 0 is valid. Else normal rules apply.
	TArray<int> invalid_states;
	if (flow_node && flow_node->IsEnteringMatch())
	{
		invalid_states = { 6,7 };
	}
	else
	{
		invalid_states = { 0,6,7,9 };
	}

	for (int i = 0; i < invalid_states.Num(); i++)
	{
		if (invalid_states[i] == tut_state)
			return;
	}

	DisplayTutorialCategoryMenu();
	SIFRumbleHelpers::ClearRumbles();
	SIFRumbleHelpers::PauseRumbles(true);
}

void UWWUIScreenTrainingField::EnterGame()
{
		OnEnterGame(nullptr);
}

void UWWUIScreenTrainingField::OnEnterGame(APlayerController* playerController)
{
	//Only run the enter game if this is the top screen. Don't allow progressing if there are disconnect popups on top.
	if (SIFApplication::GetApplication()->GetMissingControllerListener()->GetControllerDialogsOnscreen() != 0)
	{
		return;
	}

	URugbyGameInstance* gameInstance = GetGameInstance();
	bool inOnline = gameInstance->GetOnlineMode() != EOnlineMode::Offline;

	// Make sure we don't let clients start or load the game. Up to the server to do that.
	ARugbyGameModeBase* rugbyGameMode = Cast<ARugbyGameModeBase>(gameInstance->GetWorld()->GetAuthGameMode());
	if (rugbyGameMode)
	{
		//< Try load online match, the match will auto start after load is finished. >
		if (onlineLobbyReady)
		{
#ifdef wwDEBUG_TG
			// Legacy method of starting a match without using the network manager.
			if (rugbyGameMode->TryLoadMatch())
			{
				//< Hide the start match text. >
				SetStatusBarVisibility(false);

				//< Display loading icon. >
				UWWUIFunctionLibrary::SetVisibility(FindChildWidget(WWUIScreenTrainingField_UI::LoadingIcon), ESlateVisibility::Visible);
				UWWUIFunctionLibrary::SetVisibility(FindChildWidget(WWUIScreenTrainingField_UI::LoadingPercent), ESlateVisibility::Visible);
				UWWUIFunctionLibrary::PlayAnimation(this, Animations_UI::BP_UIScreenTrainingField::SpinLoadIcon, 0, 0);
			}
#else
			if (GetWorld()->GetNetMode() != ENetMode::NM_Client)
			{
				SetMatchData();
				check(inOnline);
				SIFMatchmakingHelpers::StartGame();
			}
#endif
		}
		//< If offline match is loaded, try start match. >
		else if (offlineMatchReady)
		{
			check(!inOnline);
			if (GetWorld() && GetWorld()->GetGameInstance<URugbyGameInstance>())
			{
				if (UWWUITransitionManager * TransitionManager = GetWorld()->GetGameInstance<URugbyGameInstance>()->GetTransitionManager())
				{
					FWWUIOnTransitionStartComplete transitionCallback;
					transitionCallback.BindLambda([this, gameInstance]()
					{
						ARugbyGameModeBase* rugbyGameMode = Cast<ARugbyGameModeBase>(gameInstance->GetWorld()->GetAuthGameMode());
						if (rugbyGameMode->TryStartMatchFromLobby())
						{
							//< Hide the start match text. >
							SetStatusBarVisibility(false);
						}
					});

					//If auto starting, hold the fade to hide the first second or so of the game showing.
					if (SIFApplication::GetApplication()->GetAutoStartGame())
					{
						SIFApplication::GetApplication()->RequestTransitionStart(0.5f, transitionCallback, true);
					}
					else
					{
						TransitionManager->RequestTransitionStart(0.5f, transitionCallback);
					}
					SetInputEnabled(false);
				}
			}
		}
	}
}

URugbyGameInstance* UWWUIScreenTrainingField::GetGameInstance()
{
	UWorld* world = GetWorld();

	if (world == nullptr || !world->IsValidLowLevel())
		return nullptr;

	return Cast<URugbyGameInstance>(world->GetGameInstance());
}

RUSandboxGame* UWWUIScreenTrainingField::GetSandboxGame()
{
	URugbyGameInstance* gameInstance = GetGameInstance();

	if (gameInstance == nullptr)
		return nullptr;

	return gameInstance->GetSandboxGame();
}

void UWWUIScreenTrainingField::ResetState()
{
	//< Temp values. >
	SuppressReset = false;
	ShowStartMatch = false;
	ShowLoadMatch = false;
	ShowCountdownTimer = false;
	demoActive = false;

	//< Reset widget visibility  >
	UWWUIFunctionLibrary::SetVisibility(HeaderHUD, ESlateVisibility::Hidden);
	UWWUIFunctionLibrary::SetVisibility(MedalTimer, ESlateVisibility::Collapsed);
	SetActiveScreenTemplate(ETrainingScreen::HUD);

	//< Ensure that rolodex is always hidden when training starts. >
	UWWUIFunctionLibrary::SetVisibility(Rolodex, ESlateVisibility::Collapsed);

	//< Display help tip text. >
	UWWUIFunctionLibrary::SetVisibility(FooterHUD, true);
	UWWUIFunctionLibrary::SetVisibility(FooterHUD2, true);
	SetHelpTip("[ID_TRAINING_FIELD_HELP]");

	//< Slide in black bars. >
	//....

	pause_enabled = false;
	doing_save = false;

	//< We don't want to be in a tutorial state. >
	SIFGameHelpers::GAResetSandbox();
}

void UWWUIScreenTrainingField::OnLobbyReadyChanged(bool allReady)
{
	onlineLobbyReady = allReady;

	//< Lobby is ready to start. >
	if (allReady)
	{
		URugbyGameInstance* gameInstance = GetGameInstance();
		SetStatusBarVisibility(true);

		SetMatchData();

		if (gameInstance->GetActiveGameWorld()->GetNetworkState())
		{
			gameInstance->GetActiveGameWorld()->GetNetworkState()->Reset();
		}

		//< Server >
		if (gameInstance->GetWorld()->GetAuthGameMode())
		{
			SetStatusBarText(TEXT("Lobby is ready. Press start."));
		}
		//< Client >
		else
		{
			SetStatusBarText(TEXT("Waiting for host to start"));
		}
	}
	//< Lobby is not ready. >
	else
	{
		SetStatusBarVisibility(false);
	}
}

void UWWUIScreenTrainingField::SetMatchData()
{
	UWorld* pCurrentWorld = GetWorld();

	if (pCurrentWorld)
	{
		ARugbyGameState* pRugbyGameState = Cast<ARugbyGameState>(pCurrentWorld->GetGameState());
		if (pRugbyGameState)
		{
			for (int32 i = 0; i < 8; i++)
			{
				int32 player_index = i;

				if (pRugbyGameState->PlayerArray.IsValidIndex(i))
				{
					ARugbyPlayerState* pRugbyPlayerState = Cast<ARugbyPlayerState>(pRugbyGameState->PlayerArray[i]);

					if (pRugbyPlayerState)
					{
						//pRugbyPlayerState->m_teamSide;
						SIFInGameHelpers::SetOnlinePlayerTeam((EHumanPlayerSlot)player_index, i, pRugbyPlayerState->m_teamSide, 0, pRugbyPlayerState->GetPlayerId());
						SIFPlayerHelpers::PMSetPlayerHuman(player_index, i);
					}
				}
				else
				{
					SIFInGameHelpers::SetOnlinePlayerTeam((EHumanPlayerSlot)player_index, i, -1, 0, -1);
				}
			}
		}
	}
	//int32 NumberOfPeers = SIFMatchmakingHelpers::GetNumberOfPeers();

	//for (int32 PeerIndex = 0; PeerIndex < NumberOfPeers; PeerIndex++)
	//{
	//	UE_LOG(LogNetworkNonGame, Display, TEXT("Checking controllers for Peer Index: %d"), PeerIndex);
	//	int32 PeerID = SIFMatchmakingHelpers::GetPeerIdFromIndex(PeerIndex);

	//	if (PeerID < 0)
	//	{
	//		PeerID = SIFMatchmakingHelpers::GetLocalClientId();
	//	}

	//	UE_LOG(LogNetworkNonGame, Display, TEXT("Peer Index: %d has Peer ID: %d"), PeerIndex, PeerID);

	//	for (int32 i = 0; i < 8; i++)
	//	{
	//		FString FlagName = FString("tf_cont_") + FString::FromInt(i);
	//		int32 PlayerID = SIFMatchmakingHelpers::GetValue(PeerID, TCHAR_TO_UTF8(*FlagName));
	//		bool is_player = PlayerID > 0;
	//		int32 player_index = i;

	//		UE_LOG(LogNetworkNonGame, Display, TEXT("Peer ID: %d has controller flag: %d set to player ID: %d"), PeerID, i, PlayerID);

	//		if (is_player)
	//		{
	//			FString TeamFlagName = FString("tf_cont_team_") + FString::FromInt(i);
	//			int32 TeamSideID = SIFMatchmakingHelpers::GetValue(PeerID, TCHAR_TO_UTF8(*TeamFlagName));

	//			UE_LOG(LogNetworkNonGame, Display, TEXT("Setting team flag for controller: %d to Team Side: %d"), i, TeamSideID);

	//			SIFInGameHelpers::SetOnlinePlayerTeam((EHumanPlayerSlot)player_index, i, TeamSideID, PeerID, PlayerID);
	//			SIFPlayerHelpers::PMSetPlayerHuman(player_index, i);
	//		}
	//		else
	//		{
	//			//SIFInGameHelpers::SetOnlinePlayerTeam((EHumanPlayerSlot)player_index, i, -1, PeerID, -1);
	//		}
	//	}
	//}

	//--Local data
	//int32 local_index = SIFMatchmakingHelpers::GetLocalClientTeamIndex();
	//int32 local_offset = local_index * 4;

	//// Assign teams and controllers for host side.
	//int32 local_id = SIFMatchmakingHelpers::GetLocalClientId();
	//for (int32 i = 0; i <= 3; i++)
	//{
	//	FString FlagName = FString("tf_cont_") + FString::FromInt(i);
	//	int32 PlayerID = SIFMatchmakingHelpers::GetValue(local_id, TCHAR_TO_UTF8(*FlagName));
	//	bool is_player = PlayerID > 0;
	//	int32 player_index = local_offset + i;
	//	if (is_player)
	//	{
	//		FString TeamFlagName = FString("tf_cont_team_") + FString::FromInt(i);
	//		int32 TeamSideID = SIFMatchmakingHelpers::GetValue(local_id, TCHAR_TO_UTF8(*TeamFlagName));

	//		UE_LOG(LogNetwork, Display, TEXT("Creating local player: %d"), i);
	//		SIFInGameHelpers::SetOnlinePlayerTeam((EHumanPlayerSlot)player_index, i, TeamSideID, local_id, PlayerID);
	//		SIFPlayerHelpers::PMSetPlayerHuman(player_index, i);
	//	}
	//	else
	//	{
	//		SIFInGameHelpers::SetOnlinePlayerTeam((EHumanPlayerSlot)player_index, i, -1, local_id, -1);
	//	}
	//}

	//// -- Opponent data
	//int32 opponent_index = 1 - SIFMatchmakingHelpers::GetLocalClientTeamIndex();
	//int32 opponent_offset = opponent_index * 4;

	//// Assign teams and controllers for client side.
	//int32 opponent_id = SIFMatchmakingHelpers::GetPeerIdFromIndex(0);
	//for (int32 i = 0; i <= 3; i++)
	//{
	//	// --We need to tell the opponent which controller was activated.
	//	FString FlagName = FString("tf_cont_") + FString::FromInt(i);
	//	int32 PlayerID = SIFMatchmakingHelpers::GetValue(opponent_id, TCHAR_TO_UTF8(*FlagName));
	//	bool is_player = PlayerID > 0;
	//	int32 player_index = opponent_offset + i;
	//	if (is_player)
	//	{
	//		FString TeamFlagName = FString("tf_cont_team_") + FString::FromInt(i);
	//		int32 TeamSideID = SIFMatchmakingHelpers::GetValue(opponent_id, TCHAR_TO_UTF8(*TeamFlagName));

	//		UE_LOG(LogNetwork, Display, TEXT("Creating opponent player:%d "), i);
	//		SIFInGameHelpers::SetOnlinePlayerTeam((EHumanPlayerSlot)player_index, i, TeamSideID, opponent_id, PlayerID);
	//	}
	//	else
	//	{
	//		SIFInGameHelpers::SetOnlinePlayerTeam((EHumanPlayerSlot)player_index, i, -1, opponent_id, -1);
	//	}
	//}
}

void UWWUIScreenTrainingField::StartMulitplayerLoad()
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->EnterTrainingFlow(TrainingPrompt::EnteringMatch);
		
		//< Hide the start match text. >
		SetStatusBarVisibility(false);

		//< Display loading icon. >
		UWWUIFunctionLibrary::SetVisibility(FindChildWidget(WWUIScreenTrainingField_UI::LoadingIcon), ESlateVisibility::Visible);
		UWWUIFunctionLibrary::SetVisibility(FindChildWidget(WWUIScreenTrainingField_UI::LoadingPercent), ESlateVisibility::Visible);
		UWWUIFunctionLibrary::PlayAnimation(this, Animations_UI::BP_UIScreenTrainingField::SpinLoadIcon, 0, 0);
	}
}

void UWWUIScreenTrainingField::SetHelpTip(FString _HelpTipString)
{
	if (!HelpTip) return;

	UWWUIFunctionLibrary::SetVisibility(HelpTip, true);
	HelpTipTextBackup = _HelpTipString;
	HelpTip->SetText(FText::FromString(_HelpTipString));
	HelpTip->FormatText();
}

void UWWUIScreenTrainingField::DisplayPauseMenu(int playerControllerId)
{
	if (pause_enabled && !doing_save)
	{
		//< We need to display the pause menu. >
		EntryFromTutorialPauseMenu = true;
		RaisePauseMenu(playerControllerId);
	}
}

void UWWUIScreenTrainingField::RaisePauseMenu(int playerControllerId)
{
	//< Pause the game & push the pause menu. >
	//SIFUIHelpers::ListenToAllControllers();
	SIFGameHelpers::GAPauseSandboxGame();

	//#rc3_legacy SIFUIHelpers::SetCurrentWindow(SIFUI_IN_GAME_MENU_WINDOW_NAME);
	// SIFGameHelpers::GAPauseGame(); // why pause the match game from the training field?

	SIFInGameHelpers::EnableMenuPostEffects();

	//#rc3_legacy
	// Replace QuitTutorialsYesOnClick with opening the pause menu...
	//QuitTutorialsYesOnClick(nullptr);

	UWWUIScreenPauseMenuData* pInData = NewObject<UWWUIScreenPauseMenuData>();
	if (pInData)
	{
		pInData->callingScreen = this;
		pInData->in_training = true;
		pInData->controllingPlayer = playerControllerId;
	}

	GetGameInstance()->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::PauseMenu, pInData);
}

void UWWUIScreenTrainingField::OnLoadComplete()
{
#ifdef ENABLE_STEAMCHECK
	int rand1 = FMath::Rand();
	int rand2 = FMath::Rand();

	if (FParse::Param(FCommandLine::Get(), TEXT("NOSTEAM")) || MemCheck::instance().GetTheBallRolling(rand1, rand2) == ~(rand1 + rand2))
#endif
	{
		// if there's no human playing so we can skip to game window
		int num_human_team0 = SIFInGameHelpers::GetNumHumanPlayersOnTeam(0);
		int num_human_team1 = SIFInGameHelpers::GetNumHumanPlayersOnTeam(1);

		if (num_human_team0 == 0 && num_human_team1 == 0)
		{
			UWWUIFunctionLibrary::OnTimer(1.0f, FTimerDelegate::CreateUObject(this, &UWWUIScreenTrainingField::EnterGame));
		}

		LoadingFinished = true;
		UWWUIFunctionLibrary::SetText(FindChildWidget(WWUIScreenTrainingField_UI::LoadingPercent), FString("100%"));
		SIFUIHelpers::MenuSoundMatchLoaded();
		SIFGameHelpers::GATriggerRumbleOnAllPlayers("async_load_finished");

		//If we've got an invite p}ing { we just want to jump straight into the game so they
		//can be taken to the join lobby.
		//Avoid call GotoGame twice from both here and POPUP dialog
		//if (SIFGameHelpers::GAIsConsole() && SIFMatchmakingHelpers::GetInvitePending() /*&&  #rc3_legacy_popup UIIsPopupActive("InviteReceived") && JoinInviteWindow.action_disabled == false*/)
		//{
		//	//JoinInviteWindow.action_disabled = true
		//	//UIDismissPopUpByName("InviteReceived")
		//	OnEnterGame(nullptr);
		//	//UIFlushMappedInputs(parameters.controller_id, "ACTION_TYPE_ACTION");
		//	//UIShowRugbyDollarsOverlay(false)
		//}
		//else
		//{
		//	//Via animation, fade out the loading spinner and slide in the start prompt
		//	//UINodeSetVisible(TrainingField.StartText, true)
		//	//UIFXNodeRunNamedAnimation(TrainingField.GameplayHUD, "tutorial_async_loading_finished_show_start")
		//}
	}
#ifdef ENABLE_STEAMCHECK
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Load not complete due to GetTheBallRolling failure"));
	}
#endif
}

void UWWUIScreenTrainingField::OnMatchReady()
{
	//< Hide Loading Icon >
	URugbyGameInstance* gameInstance = GetGameInstance();

#if PLATFORM_WINDOWS

	int rand1 = FMath::Rand();
	int rand2 = FMath::Rand();

	if (FParse::Param(FCommandLine::Get(), TEXT("NOSTEAM")) || MemCheck::instance().GetTheBallRolling(rand1, rand2) == ~(rand1 + rand2))
#endif
	{
		//< Offline: Display start game option. >
		if (gameInstance->GetOnlineMode() == EOnlineMode::Offline)
		{
			offlineMatchReady = true;
			SetStatusBarVisibility(true);
			UWWUIFunctionLibrary::SetVisibility(FindChildWidget(WWUIScreenTrainingField_UI::LoadingIcon), ESlateVisibility::Collapsed);
			UWWUIFunctionLibrary::SetVisibility(FindChildWidget(WWUIScreenTrainingField_UI::LoadingPercent), ESlateVisibility::Collapsed);
			SetStatusBarText(TEXT("[ID_LOADING_SCREEN_HELP]"));

			//If auto starting, hold the fade to hide the first second or so of the game showing.
			if (SIFApplication::GetApplication()->GetAutoStartGame())
			{
				if (UWWUITransitionManager * TransitionManager = GetWorld()->GetGameInstance<URugbyGameInstance>()->GetTransitionManager())
				{
					FWWUIOnTransitionStartComplete transitionCallback;
					transitionCallback.BindLambda([this, gameInstance]()
					{
						ARugbyGameModeBase* rugbyGameMode = Cast<ARugbyGameModeBase>(gameInstance->GetWorld()->GetAuthGameMode());
						if (rugbyGameMode->TryStartMatchFromLobby())
						{
							//< Hide the start match text. >
							SetStatusBarVisibility(false);
						}
					});
					SIFApplication::GetApplication()->RequestTransitionStart(0.5f, transitionCallback, true);
				}
				SetInputEnabled(false);
			}
		}
		//< Online: Display countdown timer. >
		else
		{
			//< Display timer, text set in update(). >
			UWWUIFunctionLibrary::SetVisibility(FindChildWidget(WWUIScreenTrainingField_UI::CountdownTimer), ESlateVisibility::Visible);
			ShowCountdownTimer = true;
		}
	}
#if PLATFORM_WINDOWS
	else
	{
		UWWUIFunctionLibrary::SetText(FindChildWidget(WWUIScreenTrainingField_UI::LoadingPercent), FString("99%"));
	}
#endif
}

// training_field.lua line 487
// function TrainingField.DisplayTutorialCategoryMenu()
void UWWUIScreenTrainingField::DisplayTutorialCategoryMenu()
{
	pause_enabled = false;

	//--Save some variables for the help tips
	CurrentHelpText = HelpTipTextBackup;
	if (FooterHUD && FooterHUD2)
	{
		CurrentHelpTextVisible = FooterHUD->IsVisible();

		const int TUTSTATE_PRACTICE = 4;
		int tut_state = SIFGameHelpers::GAGetCurrentTutorialState();
		if (tut_state == TUTSTATE_PRACTICE)
		{
			SIFGameHelpers::GAPauseSandboxGame();
		}

		SetTutorialElementsVisible(false);

		//< Display the category scroll box. >
		SetActiveScreenTemplate(ETrainingScreen::Category);
		if (ScrollBoxCategory)
		{
			ScrollBoxCategory->SetSelectedIndex(CurrentCategoryIdx);
		}

		//< Hide the feedback message. >
		UWWUIFunctionLibrary::SetVisibility(UWWUIFunctionLibrary::FindWidget(this, WWUIScreenTrainingField_UI::FeedBackMessage), ESlateVisibility::Collapsed);
		UWWUIFunctionLibrary::SetVisibility(TutorialMenuTitles->GetActiveWidget(), ESlateVisibility::Visible);

		UpdateOverallCompletion();
		UWWUIFunctionLibrary::SetVisibility(FooterHUD, true);
		UWWUIFunctionLibrary::SetVisibility(FooterHUD2, true);
		UWWUIFunctionLibrary::SetVisibility(RunAroundHelpText, false);

#if PLATFORM_WINDOWS
		SetHelpTip("[ID_TRAINING_FIELD_PAUSE_HELP_TEXT]");
#else
		SetHelpTip("[ID_TRAINING_FIELD_HELP_TUTORIAL_MENU_ON]");
#endif

	}
	else
	{
		ensure(FooterHUD && FooterHUD2);
	}
}

// training_field.lua line 901
void UWWUIScreenTrainingField::SetTutorialElementsVisible(bool visible)
{
	//If we're in the correct state, set the elements to whatever is requested
	const int TUTSTATE_INTRO = 1;
	const int TUTSTATE_FINISHED = 5;

	int tut_state = SIFGameHelpers::GAGetCurrentTutorialState();
	if (tut_state >= TUTSTATE_INTRO && tut_state <= TUTSTATE_FINISHED)
	{
		UWWUIFunctionLibrary::SetVisibility(Rolodex, visible);
		UWWUIFunctionLibrary::SetVisibility(TutorialInfo, visible);
	}
}

// training_field.lua line 578
void UWWUIScreenTrainingField::UpdateOverallCompletion()
{
	// Update tutorial completion
	int completion_percentage = SIFGameHelpers::GAGetOverallTutorialCompletion();
	FString text = FString::FromInt(completion_percentage) + "% [ID_TUTORIAL_CATEGORY_COMPLETED]";

	UWWUITabSwitcher* tutorial_title = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenTrainingField_UI::TabContainerCategory));
	UTextBlock* tutorial_subtitle = Cast<UTextBlock>(FindChildOfTemplateWidget(FindChildWidget(WWUIScreenTrainingField_UI::HeaderCategory), WWUIScreenTrainingField_UI::Subtitle));
	tutorial_title->SetTabText(0, FText::FromString(UWWUITranslationManager::Translate("[ID_TRAINING_OPTIONS]")));
	UWWUIFunctionLibrary::SetText(tutorial_subtitle, UWWUITranslationManager::Translate(text));
}

// training_field.lua line 589
void UWWUIScreenTrainingField::UpdateCategoryCompletion()
{
	int tutorial_category = ScrollBoxLesson->GetIntProperty("tutorial_category");
	FString category = ScrollBoxLesson->GetStringProperty("category");
	int completion_percentage = SIFGameHelpers::GAGetTutorialCategoryCompletion(tutorial_category);
	FString subtitleText = FString::FromInt(completion_percentage) + "% [ID_TUTORIAL_CATEGORY_COMPLETED]";

	FString titleText = "[ID_" + category + "]";

	UWWUITabSwitcher* tutorial_title = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenTrainingField_UI::TabContainerLesson));
	UTextBlock* tutorial_subtitle = Cast<UTextBlock>(FindChildOfTemplateWidget(FindChildWidget(WWUIScreenTrainingField_UI::HeaderLesson), WWUIScreenTrainingField_UI::Subtitle));
	tutorial_title->SetTabText(0, UWWUITranslationManager::Translate(FText::FromString(titleText)));
	UWWUIFunctionLibrary::SetText(tutorial_subtitle, UWWUITranslationManager::Translate(subtitleText));

	pause_enabled = false;
}

void UWWUIScreenTrainingField::OpenDebugWindow(APlayerController* controller)
{
	EntryFromTutorialPauseMenu = true;
	GetGameInstance()->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::DebugWindow);
}
