/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/SSGameTimerHalfObserver.h"

#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/Statistics/RUStatisticsSystem.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSGameTimer.h"

#include "RugbyGameInstance.h"

///    If time expires and the ball is not dead, or an awarded scrum or lineout has not been completed, the referee allows play to continue
///			until the next time that the ball becomes dead.
///    The ball becomes dead when the referee would have awarded a scrum, lineout, an option to the non-infringing team,
///         drop out or after a conversion or successful penalty kick at goal.
///    If a scrum has to be reset, the scrum has not been completed.
///    If time expires and a mark, free kick or penalty kick is then awarded, the referee allows play to continue.


///---------------------------------------------------------------------
/// Constructor, registers event handlers.
///---------------------------------------------------------------------

SSGameTimerHalfObserver::SSGameTimerHalfObserver( SIFGameWorld* ggame )
: game( ggame )
, time_expired( false )
, half_end( false )
, triggered_half_end( false )
, full_end( false )
, triggered_full_end( false )
, ignore_next_scrum( false )
, ignore_next_dropout( false )
, trigger_half_end_next_frame( false )
{
	RUGameEvents* game_events = game->GetEvents();
	MABASSERT( game_events != NULL );

	//get notified when the time has expired
	game_events->pre_half_time.Add( this, &SSGameTimerHalfObserver::NotifyTimeExpired );
	game_events->pre_full_time.Add( this, &SSGameTimerHalfObserver::NotifyTimeExpired );
	game_events->pre_extra_time.Add( this, &SSGameTimerHalfObserver::NotifyTimeExpired );

	// get notified when play stopage occurred
	game_events->scrum.Add( this, &SSGameTimerHalfObserver::NotifyScrum );						// Used to end the half from 40/20, ballout, knockon, forward pass
	game_events->drop_goal.Add( this, &SSGameTimerHalfObserver::NotifyFieldGoal );				// Used to keep a flag that a point has been awarded, only used in extra time
	game_events->kick_restart_start.Add( this, &SSGameTimerHalfObserver::NotifyKickRestart );	// Used to end the half from try in extra time, conversion, field goal, penalty kick, ball dead own ingoal
	game_events->lineout_signalled.Add( this, &SSGameTimerHalfObserver::NotifyLineOut );
	game_events->touch_scrum_signalled.Add(this, &SSGameTimerHalfObserver::NotifyTouchScrum);
	game_events->cutscene_dropout.Add( this , &SSGameTimerHalfObserver::NotifyDropOut );
	game_events->tackle.Add( this, &SSGameTimerHalfObserver::NotifyTackle );

	// get notified if points have been awarded to be checked for extra time
	game_events->try_result.Add( this, &SSGameTimerHalfObserver::NotifyTryResult );				// Used to keep a flag that a point has been awarded, only used in extra time

	game_events->try_awarded.Add( this, &SSGameTimerHalfObserver::NotifyTryAwarded );
	game_events->penalty_try_awarded.Add( this, &SSGameTimerHalfObserver::NotifyPenaltyTryAwarded );

	game_events->cutscene_finish.Add( this, &SSGameTimerHalfObserver::NotifyCutsceneFinished );

	game_events->rule_trigger_ball_dead.Add( this, &SSGameTimerHalfObserver::BallDead );

	game_events->penalty_goal_finish.Add( this, &SSGameTimerHalfObserver::NotifyPenaltyFinished);
	game_events->Force_End_Of_Half.Add (this, &SSGameTimerHalfObserver::ForceEndOfHalf);
	game_events->Force_End_Of_Full.Add(this, &SSGameTimerHalfObserver::ForceEndOfFull);
}

///---------------------------------------------------------------------
/// Destructor, de-registers event handlers.
///---------------------------------------------------------------------

SSGameTimerHalfObserver::~SSGameTimerHalfObserver()
{
	RUGameEvents* game_events = game->GetEvents();
	//MABASSERT( game_events != NULL );

	//get notified when the time has expired
	if (game_events)
	{
		game_events->pre_half_time.Remove( this, &SSGameTimerHalfObserver::NotifyTimeExpired );
		game_events->pre_full_time.Remove( this, &SSGameTimerHalfObserver::NotifyTimeExpired );
		game_events->pre_extra_time.Remove( this, &SSGameTimerHalfObserver::NotifyTimeExpired );

		game_events->scrum.Remove( this, &SSGameTimerHalfObserver::NotifyScrum );
		game_events->drop_goal.Remove( this, &SSGameTimerHalfObserver::NotifyFieldGoal );
		game_events->kick_restart_start.Remove( this, &SSGameTimerHalfObserver::NotifyKickRestart );
		game_events->lineout_signalled.Remove( this, &SSGameTimerHalfObserver::NotifyLineOut );
		game_events->touch_scrum_signalled.Remove(this, &SSGameTimerHalfObserver::NotifyTouchScrum);
		game_events->cutscene_dropout.Remove( this , &SSGameTimerHalfObserver::NotifyDropOut );

		game_events->try_result.Remove( this, &SSGameTimerHalfObserver::NotifyTryResult );
		game_events->try_awarded.Remove( this, &SSGameTimerHalfObserver::NotifyTryAwarded );
		game_events->penalty_try_awarded.Remove( this, &SSGameTimerHalfObserver::NotifyPenaltyTryAwarded );

		game_events->cutscene_finish.Remove( this, &SSGameTimerHalfObserver::NotifyCutsceneFinished );

		game_events->rule_trigger_ball_dead.Remove( this, &SSGameTimerHalfObserver::BallDead );

		game_events->penalty_goal_finish.Remove( this, &SSGameTimerHalfObserver::NotifyPenaltyFinished);
		game_events->Force_End_Of_Half.Remove(this, &SSGameTimerHalfObserver::ForceEndOfHalf);
		game_events->Force_End_Of_Full.Remove(this, &SSGameTimerHalfObserver::ForceEndOfFull);
	}
}

///---------------------------------------------------------------------
/// Reset.
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::Reset()
{
	time_expired = false;
	half_end = false;
	triggered_half_end = false;
	full_end = false;
	triggered_full_end = false;
	trigger_half_end_next_frame = false;
#ifdef ENABLE_SEVENS_MODE
	scoredDuringExtraTime = false;
#endif
}

///---------------------------------------------------------------------
/// Update.
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::Update()
{
	// Do delayed TriggerHalfEnd if requested.
	if(trigger_half_end_next_frame)
	{
		trigger_half_end_next_frame = false;
		TriggerHalfEnd();
	}
}

///---------------------------------------------------------------------
/// Event handler: 'rule_trigger_ball_dead'
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::BallDead(ARugbyCharacter*, const FVector&, RURuleConsequence)
{
	MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "SSGameTimerHalfObserver: BallDead");
	if( time_expired )
	{
		MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "BallDead: HalfEnd()");
		HalfEnd();
	}
}

///---------------------------------------------------------------------
/// Event handler:  pre_half_time, pre_full_time, pre_extra_time
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::NotifyTimeExpired()
{
	MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "SSGameTimerHalfObserver: Time Expired");
	if( game->GetGameTimer()->GetExtraTimeMode() == SECOND_EXTRA_TIME)
	{
		// If in second extra time and scores are level, switch to play for ever immediately.

		RUStatisticsSystem *stats = SIFApplication::GetApplication()->GetStatisticsSystem();
		RUTeam *team0 = game->GetTeam(SIDE_A);
		RUTeam *team1 = game->GetTeam(SIDE_B);
		int score0 = stats->GetCurrentMatchStat(team0, &RUDB_STATS_TEAM::score);
		int score1 = stats->GetCurrentMatchStat(team1, &RUDB_STATS_TEAM::score);

		if(score0==score1)
		{
			MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "SSGameTimerHalfObserver: Switching to GOLDEN_POINT");
			game->GetGameTimer()->SetExtraTimeMode(GOLDEN_POINT);
			if (game->GetEvents())
			{
				game->GetEvents()->golden_point_time();
			}
			return;
		}
	}

	time_expired = true;
}


///---------------------------------------------------------------------
/// Event handler: scrum
///  - Used to end the half from 40/20, ballout, knockon, forward pass
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::NotifyScrum(RUTeam *)
{
	MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "SSGameTimerHalfObserver: Scrum Event");
	if( time_expired && !ignore_next_scrum)
	{
		MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "SSGameTimerHalfObserver: Ending On Scrum");
		HalfEnd();
	}

	ignore_next_scrum = false;
}

///---------------------------------------------------------------------
/// Event handler: lineout_signalled
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::NotifyLineOut( FVector /*location*/, bool from_decision)
{
	/// Code has been changed to give additional context for when the ball is carried out after a kick for touch
	/// if(time_expired && !from_decision)
	/// {
	/// 	// This is no longer an NRC specific rule, and is in the standard ruleset.
	/// 	//if(game->GetGameSettings().game_law == GAME_LAW_NRC)
	/// 	{
	/// 		const BallFreeInfo& ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();
	/// 		BALL_FREE_EVENT evt = ball_free_info.event;
	/// 		RUGamePhase previous_phase = ball_free_info.game_phase_when_released;
	/// 		
	/// 		if(evt == BFE_KICK && previous_phase == RUGamePhase::PENALTY_KICK_FOR_TOUCH)
	/// 		{
	/// 			return;
	/// 		}
	/// 	}
	/// 
	/// 	HalfEnd();
	/// }

	BallFreeInfo bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();

	ARugbyCharacter* ball_holder = game->GetGameState()->GetBallHolder();
	RUTeam* restart_team = game->GetGameState()->GetPlayRestartTeam();
	bool last_lineout_carried_out = game->GetGameState()->GetLastLineoutCarriedOut();

	bool was_carried_out = ball_holder != nullptr && ball_holder->GetAttributes()->GetTeam() != restart_team && last_lineout_carried_out;
	bool was_kick_for_touch = (!was_carried_out && bfi.event == BFE_KICK && bfi.game_phase_when_released == RUGamePhase::PENALTY_KICK_FOR_TOUCH);

	/// The response here must mirror SSCutSceneManager::LineoutSignalled to avoid NMA
	if (time_expired && !was_kick_for_touch && !from_decision)
	{
		HalfEnd();
	}
}

void SSGameTimerHalfObserver::NotifyTouchScrum(FVector /*location*/, bool from_decision)
{
	/// Code has been changed to give additional context for when the ball is carried out after a kick for touch
	/// if(time_expired && !from_decision)
	/// {
	/// 	// This is no longer an NRC specific rule, and is in the standard ruleset.
	/// 	//if(game->GetGameSettings().game_law == GAME_LAW_NRC)
	/// 	{
	/// 		const BallFreeInfo& ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();
	/// 		BALL_FREE_EVENT evt = ball_free_info.event;
	/// 		RUGamePhase previous_phase = ball_free_info.game_phase_when_released;
	/// 		
	/// 		if(evt == BFE_KICK && previous_phase == RUGamePhase::PENALTY_KICK_FOR_TOUCH)
	/// 		{
	/// 			return;
	/// 		}
	/// 	}
	/// 
	/// 	HalfEnd();
	/// }

	BallFreeInfo		bfi							= game->GetStrategyHelper()->GetLastBallFreeInfo();
	ARugbyCharacter*	ball_holder					= game->GetGameState()->GetBallHolder();
	RUTeam*				restart_team				= game->GetGameState()->GetPlayRestartTeam();
	bool				last_lineout_carried_out	= game->GetGameState()->GetLastLineoutCarriedOut();
	bool				was_carried_out				= ball_holder != nullptr && ball_holder->GetAttributes()->GetTeam() != restart_team && last_lineout_carried_out;
	bool				was_kick_for_touch			= (!was_carried_out && bfi.event == BFE_KICK && bfi.game_phase_when_released == RUGamePhase::PENALTY_KICK_FOR_TOUCH);

	/// The response here must mirror SSCutSceneManager::LineoutSignalled to avoid NMA
	if (time_expired && !was_kick_for_touch && !from_decision)
	{
		HalfEnd();
	}
}

///---------------------------------------------------------------------
/// Event handler: field_goal
///  Used to keep a flag that a point has been awarded, only used in extra time
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::NotifyFieldGoal( bool success, const FVector& )
{
	// Nick  WWS 7s to Womens //
	/*
#ifdef ENABLE_SEVENS_MODE
	scoredDuringExtraTime = success && game->GetGameTimer()->IsInExtraTime() && game->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_SEVENS;

	if( (game->GetGameTimer()->GetExtraTimeMode() == GOLDEN_POINT && success) || scoredDuringExtraTime)
#else */
	if( game->GetGameTimer()->GetExtraTimeMode() == GOLDEN_POINT && success)
//#endif
	{
		/// Point has been scored so allow golden point to end.
		time_expired = true;
	}
}

///---------------------------------------------------------------------
/// Event handler: cutscene_dropout
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::NotifyDropOut()
{
	MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "SSGameTimerHalfObserver: NotifyDropOut");
	if(time_expired && !ignore_next_dropout)
	{
		HalfEnd();
	}
	ignore_next_dropout = false;
}

///-------------------------------------------------------------------------------
///-------------------------------------------------------------------------------
void SSGameTimerHalfObserver::NotifyTackle(const RUTackleResult& tackleResult)
{
	MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "SSGameTimerHalfObserver: NotifyTackle");
	if (time_expired)
	{
		HalfEnd();
	}
}

///---------------------------------------------------------------------
/// Event handler: 	kick_restart_start
///  - Used to end the half from try in extra time, conversion, field goal, penalty kick, ball dead own ingoal
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::NotifyKickRestart()
{
	MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "SSGameTimerHalfObserver: Kick Restart Event");
	if( time_expired )
	{
		HalfEnd();
	}
}

///---------------------------------------------------------------------
/// Event handler: try_awarded
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::NotifyTryAwarded()
{
	// Nick  WWS 7s to Womens //
	/*
#ifdef ENABLE_SEVENS_MODE
	scoredDuringExtraTime = game->GetGameTimer()->IsInExtraTime() && game->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_SEVENS;

	if(game->GetGameTimer()->GetExtraTimeMode() == GOLDEN_POINT || scoredDuringExtraTime)
#else */
	if(game->GetGameTimer()->GetExtraTimeMode() == GOLDEN_POINT)
//#endif
	{
		time_expired = true;
		HalfEnd();
	}
}

///---------------------------------------------------------------------
/// Event handler: penalty_try_awarded
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::NotifyPenaltyTryAwarded()
{
	// Nick  WWS 7s to Womens //
	/*
#ifdef ENABLE_SEVENS_MODE
	scoredDuringExtraTime = game->GetGameTimer()->IsInExtraTime() && game->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_SEVENS;

	if( game->GetGameTimer()->GetExtraTimeMode() == GOLDEN_POINT || scoredDuringExtraTime)
#else */
	if( game->GetGameTimer()->GetExtraTimeMode() == GOLDEN_POINT)
//#endif
	{
		/// Point has been scored so allow golden point to end.
		time_expired = true;
	}
}

///---------------------------------------------------------------------
///  Event handler: penalty_goal_finish
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::NotifyPenaltyFinished(bool success, const FVector& )
{
	// Nick  WWS 7s to Womens //
	/*
#ifdef ENABLE_SEVENS_MODE
	scoredDuringExtraTime = success && game->GetGameTimer()->IsInExtraTime() && game->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_SEVENS;

	if((success && game->GetGameTimer()->GetExtraTimeMode() == GOLDEN_POINT) || scoredDuringExtraTime)
#else */
	if(success && game->GetGameTimer()->GetExtraTimeMode() == GOLDEN_POINT)
//#endif
	{
		// Trigger end of half next frame.
		// (Have to do this due to scores not being updated yet, on same game event!)
		time_expired = true;
		trigger_half_end_next_frame = true;
	}
}

///---------------------------------------------------------------------
/// Event handler: try_result
///  get notified if points have been awarded to be checked for extra time
///  - Used to keep a flag that a point has been awarded, only used in extra time
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::NotifyTryResult(  bool /*success*/, bool, ARugbyCharacter* )
{
	//if(success && game->GetGameTimer()->GetExtraTimeMode() == GOLDEN_POINT)
	//{
	//	time_expired = true;
	//}
}

///---------------------------------------------------------------------
/// Cutscene has ended
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::NotifyCutsceneFinished()
{
	MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "RL3GameHalfObserver: Cutscene Event");
	if( half_end )
	{
		MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "RL3GameHalfObserver: Cutscene Event Handled");
		TriggerHalfEnd();
		half_end = false;
	}

	if( full_end )
	{
		MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "RL3GameHalfObserver: Cutscene Event Handled");
		TriggerFullEnd();
		full_end = false;
	}
}

///---------------------------------------------------------------------
/// End the half!
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::HalfEnd()
{
	// Dont trigger half end while cutscene is playing.
	// HalfEnd will fire again at the end of the cutscene
	// when RL3CutsceneManager updates the game and fires
	// the next event.

	half_end = true;

	if( game->GetCutSceneManager()->IsCinematicRunning())
		return;

	// DH - Fix for when we score during golden point AFTER the buzzer hit.
	if( game->GetGameTimer()->GetExtraTimeMode() == GOLDEN_POINT )
	{
		triggered_half_end = false;
	}

	TriggerHalfEnd();
}

///---------------------------------------------------------------------
/// Trigger the end of half.
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::TriggerHalfEnd()
{
	if(triggered_half_end)
		return;

	triggered_half_end = true;

#ifdef ENABLE_SEVENS_MODE
	if(scoredDuringExtraTime)
	{
		game->GetEvents()->full_time();
		game->GetEvents()->cutscene_full_time();
	}
	else
#endif
	{
		if( game->GetGameTimer()->GetExtraTimeMode() == NOT_EXTRA_TIME )
		{
			if( game->GetGameTimer()->GetHalf() == FIRST_HALF)
			{
				game->GetEvents()->half_time();
				game->GetEvents()->cutscene_half_time();
			}
			else if( game->GetGameTimer()->GetHalf() == SECOND_HALF)
			{
				game->GetEvents()->full_time();
				game->GetEvents()->cutscene_full_time();
			}
		}
		else if( game->GetGameTimer()->GetExtraTimeMode() == FIRST_EXTRA_TIME)
		{
			game->GetEvents()->extra_time();
			game->GetEvents()->cutscene_extra_time();
		}
		else if( game->GetGameTimer()->GetExtraTimeMode() == SECOND_EXTRA_TIME || game->GetGameTimer()->GetExtraTimeMode() == GOLDEN_POINT)
		{
			game->GetEvents()->full_time();
			game->GetEvents()->cutscene_full_time();
		}
	}
}

///---------------------------------------------------------------------
/// End the half now.
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::ForceEndOfHalf()
{	
	MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "SSGameTimerHalfObserver: ForceEndOfHalf invoked - Is it intentional?????");
	game->GetGameTimer()->ForceTimeout();
	HalfEnd();
}


///---------------------------------------------------------------------
/// End the full!
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::FullEnd()
{
	// I'm hoping i can do this to end the whole game at any point (even if we're in the first half)

	full_end = true;

	if( game->GetCutSceneManager()->IsCinematicRunning())
		return;

	TriggerFullEnd();
}

///---------------------------------------------------------------------
/// Trigger the end of full time.
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::TriggerFullEnd()
{
	if(triggered_full_end)
		return;

	triggered_full_end = true;

	game->GetEvents()->full_time();
	game->GetEvents()->cutscene_full_time();
}

///---------------------------------------------------------------------
/// End the full now.
///---------------------------------------------------------------------

void SSGameTimerHalfObserver::ForceEndOfFull()
{
	MABLOGMSG(LOGCHANNEL_GAME, LOGTYPE_INFO, "SSGameTimerHalfObserver: ForceEndOfFull invoked - Is it intentional?????");
	game->GetGameTimer()->ForceTimeout();
	FullEnd();
}
