//This file is automatically generated in the UI Tool

#pragma  once

namespace WWUIScreenInGameHud_UI
{
	const FString BP_ScreenTemplateDefault = FString("BP_ScreenTemplateDefault");
	const FString HUDBodyInvalidationBox = FString("HUDBodyInvalidationBox");
	const FString BP_HUD_HUDBox = FString("BP_HUD_HUDBox");
	const FString ContextualHUD = FString("ContextualHUD");
	const FString PlayerInfoPanelsInvalidationBox = FString("PlayerInfoPanelsInvalidationBox");
	const FString PlayerInfoPanels = FString("PlayerInfoPanels");
	const FString Player_0 = FString("Player_0");
	const FString Player_1 = FString("Player_1");
	const FString Player_2 = FString("Player_2");
	const FString Player_3 = FString("Player_3");
	const FString MatchInfo = FString("MatchInfo");
	const FString UserStrategyHUDA = FString("UserStrategyHUDA");
	const FString UserStrategyHUDB = FString("UserStrategyHUDB");
	const FString LineoutDecideNumberHelpText = FString("LineoutDecideNumberHelpText");
	const FString BP_GameplayHUD = FString("BP_GameplayHUD");
	const FString PlayerOffscreenMarkers = FString("PlayerOffscreenMarkers");
	const FString marker_0 = FString("marker_0");
	const FString marker_1 = FString("marker_1");
	const FString marker_2 = FString("marker_2");
	const FString marker_3 = FString("marker_3");
	const FString marker_4 = FString("marker_4");
	const FString marker_5 = FString("marker_5");
	const FString marker_6 = FString("marker_6");
	const FString marker_7 = FString("marker_7");
	const FString ReplayBox = FString("ReplayBox");
	const FString ReplayText = FString("ReplayText");
	const FString ShortStall = FString("ShortStall");
	const FString LongStall = FString("LongStall");
	const FString QuickLineoutHelpText = FString("QuickLineoutHelpText");
	const FString ScrumHelpText = FString("ScrumHelpText");
	const FString ConversionPlace = FString("ConversionPlace");
	const FString ConversionAim = FString("ConversionAim");
	const FString ConversionAccuracy = FString("ConversionAccuracy");
	const FString ConversionPower = FString("ConversionPower");
	const FString FullscreenBackground = FString("FullscreenBackground");
	const FString BackgroundImages = FString("BackgroundImages");
	const FString Header = FString("Header");
	const FString Body = FString("Body");
	const FString Footer = FString("Footer");
	const FString ForegroundImages = FString("ForegroundImages");
	const FString InvalidationBox_0 = FString("InvalidationBox_0");
	const FString CanvasPanel_0 = FString("CanvasPanel_0");
	const FString MessageTitleText = FString("MessageTitleText");
	const FString MessageMainText = FString("MessageMainText");
	const FString BP_WWUIFixedMinimap = FString("BP_WWUIFixedMinimap");
	const FString BP_ProRequestFeedback = FString("BP_ProRequestFeedback");
	const FString BP_ProGoalUpdate = FString("BP_ProGoalUpdate");
	const FString BP_HUD_GameInfo = FString("BP_HUD_GameInfo");
	const FString pause_disabled_icon = FString("pause_disabled_icon");
	const FString CountdownClock = FString("CountdownClock");
	const FString CountdownClockSpacer = FString("CountdownClockSpacer");
	const FString ModifierButton = FString("ModifierButton");
	const FString ModifierColour = FString("ModifierColour");
	const FString ModifierButtonText = FString("ModifierButtonText");
	const FString ModifierText = FString("ModifierText");
	const FString TopButton = FString("TopButton");
	const FString TopColour = FString("TopColour");
	const FString TopButtonText = FString("TopButtonText");
	const FString TopText = FString("TopText");
	const FString LeftButton = FString("LeftButton");
	const FString LeftColour = FString("LeftColour");
	const FString LeftButtonText = FString("LeftButtonText");
	const FString LeftText = FString("LeftText");
	const FString RightButton = FString("RightButton");
	const FString RightColour = FString("RightColour");
	const FString RightButtonText = FString("RightButtonText");
	const FString RightText = FString("RightText");
	const FString BottomButton = FString("BottomButton");
	const FString BottomColour = FString("BottomColour");
	const FString BottomButtonText = FString("BottomButtonText");
	const FString BottomText = FString("BottomText");
	const FString Data0 = FString("Data0");
	const FString Data1 = FString("Data1");
	const FString Data2 = FString("Data2");
	const FString Data3 = FString("Data3");
	const FString Current = FString("Current");
	const FString Old = FString("Old");
	const FString TeamInfo = FString("TeamInfo");
	const FString PlayerInfo = FString("PlayerInfo");
	const FString SubstitutionInfo = FString("SubstitutionInfo");
	const FString Primary = FString("Primary");
	const FString Secondary = FString("Secondary");
	const FString BP_FooterDefault = FString("BP_FooterDefault");
	const FString HelpTip = FString("HelpTip");
	const FString ContextOptionAttack = FString("ContextOptionAttack");
	const FString AttackTitle = FString("AttackTitle");
	const FString BP_FieldContextOption = FString("BP_FieldContextOption");
	const FString BP_FieldContextOption_80 = FString("BP_FieldContextOption_80");
	const FString BP_FieldContextOption_136 = FString("BP_FieldContextOption_136");
	const FString ContextOptionDefense = FString("ContextOptionDefense");
	const FString DefenseTitle = FString("DefenseTitle");
	const FString BP_FieldContextOption_263 = FString("BP_FieldContextOption_263");
	const FString BP_FieldContextOption_C_265 = FString("BP_FieldContextOption_C_265");
	const FString BP_FieldContextOption_C_266 = FString("BP_FieldContextOption_C_266");
	const FString ArrowImage = FString("ArrowImage");
	const FString OffsideImage = FString("OffsideImage");
	const FString LoadingSpinner = FString("LoadingSpinner");
	const FString Text = FString("Text");
	const FString MinimapSizeBox = FString("MinimapSizeBox");
	const FString ScaleBox_0 = FString("ScaleBox_0");
	const FString MinimapImage = FString("MinimapImage");
	const FString ResizeCanvasPanel = FString("ResizeCanvasPanel");
	const FString MarkerCanvasPanel = FString("MarkerCanvasPanel");
	const FString BaseInvalidationBox = FString("BaseInvalidationBox");
	const FString Title = FString("Title");
	const FString MidSizeBox = FString("MidSizeBox");
	const FString Message = FString("Message");
	const FString Cap = FString("Cap");
	const FString CapBorder = FString("CapBorder");
	const FString TimerMessageInvalidationBox = FString("TimerMessageInvalidationBox");
	const FString BorderExtendedTime = FString("BorderExtendedTime");
	const FString TextExtendedTime = FString("TextExtendedTime");
	const FString Team0Colour = FString("Team0Colour");
	const FString HorizontalBoxTeam0 = FString("HorizontalBoxTeam0");
	const FString Team0NameText = FString("Team0NameText");
	const FString Team0ScoreText = FString("Team0ScoreText");
	const FString TackleCountText = FString("TackleCountText");
	const FString MetersGainedText = FString("MetersGainedText");
	const FString MetersGainedInvalidationBox_1 = FString("MetersGainedInvalidationBox_1");
	const FString HomeCardsBoxYellow = FString("HomeCardsBoxYellow");
	const FString Team0YellowCard1 = FString("Team0YellowCard1");
	const FString Team0YellowCard2 = FString("Team0YellowCard2");
	const FString Team0YellowCard3 = FString("Team0YellowCard3");
	const FString HomeCardsBoxRed = FString("HomeCardsBoxRed");
	const FString Team0RedCard1 = FString("Team0RedCard1");
	const FString Team0RedCard2 = FString("Team0RedCard2");
	const FString Team0RedCard3 = FString("Team0RedCard3");
	const FString TimeTextInvalidationBox = FString("TimeTextInvalidationBox");
	const FString TimeText = FString("TimeText");
	const FString ImageTeam1Background = FString("ImageTeam1Background");
	const FString HorizontalBoxTeam1 = FString("HorizontalBoxTeam1");
	const FString TextTeam1Score = FString("TextTeam1Score");
	const FString TextTeam1Name = FString("TextTeam1Name");
	const FString AwayCardsBoxRed = FString("AwayCardsBoxRed");
	const FString Team1RedCard3 = FString("Team1RedCard3");
	const FString Team1RedCard2 = FString("Team1RedCard2");
	const FString Team1RedCard1 = FString("Team1RedCard1");
	const FString AwayCardsBoxYellow = FString("AwayCardsBoxYellow");
	const FString Team1YellowCard1 = FString("Team1YellowCard1");
	const FString Team1YellowCard2 = FString("Team1YellowCard2");
	const FString Team1YellowCard3 = FString("Team1YellowCard3");
	const FString Team1Colour = FString("Team1Colour");
	const FString ImpactText = FString("ImpactText");
	const FString ImpactTextMessageBorder = FString("ImpactTextMessageBorder");
	const FString ImpactTextMessage = FString("ImpactTextMessage");
	const FString ImpactTextSubMessageBorder = FString("ImpactTextSubMessageBorder");
	const FString ImpactTextSubMessage = FString("ImpactTextSubMessage");
	const FString ImpactTeamColor = FString("ImpactTeamColor");
	const FString Clock = FString("Clock");
	const FString Image_72 = FString("Image_72");
	const FString TimerText = FString("TimerText");
	const FString Button = FString("Button");
	const FString Template = FString("Template");
	const FString HorizontalBox_1 = FString("HorizontalBox_1");
	const FString ColourSpacer = FString("ColourSpacer");
	const FString NameAndLogoOverlay = FString("NameAndLogoOverlay");
	const FString Background = FString("Background");
	const FString Team0 = FString("Team0");
	const FString Team1 = FString("Team1");
	const FString ColourSpacer2 = FString("ColourSpacer2");
	const FString PlayerName = FString("PlayerName");
	const FString Logo = FString("Logo");
	const FString Team_0 = FString("Team_0");
	const FString SubstitutionList_0 = FString("SubstitutionList_0");
	const FString LeftTeam = FString("LeftTeam");
	const FString LeftColour_0 = FString("LeftColour_0");
	const FString Logo_0 = FString("Logo_0");
	const FString PlayerName_0 = FString("PlayerName_0");
	const FString Team_1 = FString("Team_1");
	const FString SubstitutionList_1 = FString("SubstitutionList_1");
	const FString RightTeam = FString("RightTeam");
	const FString LeftColour_1 = FString("LeftColour_1");
	const FString Logo_1 = FString("Logo_1");
	const FString PlayerName_1 = FString("PlayerName_1");
	const FString RightColour_1 = FString("RightColour_1");
	const FString BP_HUDUserStrategyTemplate = FString("BP_HUDUserStrategyTemplate");
	const FString TopBoxSlot = FString("TopBoxSlot");
	const FString TopListBox = FString("TopListBox");
	const FString BP_StrategyBodyField_C_2 = FString("BP_StrategyBodyField_C_2");
	const FString BP_StrategyBodyField_C_1 = FString("BP_StrategyBodyField_C_1");
	const FString BP_StrategyBodyField = FString("BP_StrategyBodyField");
	const FString LineDepthHeader = FString("LineDepthHeader");
	const FString BottomBoxSlot = FString("BottomBoxSlot");
	const FString LineWidthHeader = FString("LineWidthHeader");
	const FString BottomListBox = FString("BottomListBox");
	const FString BP_StrategyBodyField_C_6 = FString("BP_StrategyBodyField_C_6");
	const FString BP_StrategyBodyField_C_7 = FString("BP_StrategyBodyField_C_7");
	const FString BP_StrategyBodyField_C_8 = FString("BP_StrategyBodyField_C_8");
	const FString LeftBoxSlot = FString("LeftBoxSlot");
	const FString RunnerLeftHeader = FString("RunnerLeftHeader");
	const FString LeftListBox = FString("LeftListBox");
	const FString SendLeftField = FString("SendLeftField");
	const FString RightBoxSlot = FString("RightBoxSlot");
	const FString RunnerRightHeader = FString("RunnerRightHeader");
	const FString RightListBox = FString("RightListBox");
	const FString SendRightField = FString("SendRightField");
	const FString PodsHeader = FString("PodsHeader");
	const FString FullbackHeader = FString("FullbackHeader");
	const FString RuckHeader = FString("RuckHeader");
	const FString BP_StrategyBodyField_C_486 = FString("BP_StrategyBodyField_C_486");
	const FString BP_StrategyBodyField_C_0 = FString("BP_StrategyBodyField_C_0");
	const FString BP_StrategyBodyField_C_3 = FString("BP_StrategyBodyField_C_3");
	const FString KickerHeader = FString("KickerHeader");
	const FString PrepareField = FString("PrepareField");
	const FString BP_DividerDefault = FString("BP_DividerDefault");
	const FString ContentSlot = FString("ContentSlot");
	const FString BorderRight = FString("BorderRight");
	const FString BorderLeft = FString("BorderLeft");
	const FString Symbol = FString("Symbol");
	const FString AdditionalInfo = FString("AdditionalInfo");
	const FString Content = FString("Content");
	const FString TeamName = FString("TeamName");
	const FString ScoreHighlight = FString("ScoreHighlight");
	const FString Score = FString("Score");
	const FString ScoreGlow = FString("ScoreGlow");
	const FString SubstitutionListVerticleBox = FString("SubstitutionListVerticleBox");
	const FString Substitution_0 = FString("Substitution_0");
	const FString Substitution_1 = FString("Substitution_1");
	const FString Substitution_2 = FString("Substitution_2");
	const FString Substitution_3 = FString("Substitution_3");
	const FString Substitution_4 = FString("Substitution_4");
	const FString Substitution_5 = FString("Substitution_5");
	const FString Substitution_6 = FString("Substitution_6");
	const FString UserIcon = FString("UserIcon");
	const FString ArrowLeft = FString("ArrowLeft");
	const FString ArrowDown = FString("ArrowDown");
	const FString ArrowUp = FString("ArrowUp");
	const FString ArrowRight = FString("ArrowRight");
	const FString TopSlot = FString("TopSlot");
	const FString BottomSlot = FString("BottomSlot");
	const FString LeftSlot = FString("LeftSlot");
	const FString RightSlot = FString("RightSlot");
	const FString Border_29 = FString("Border_29");
	const FString ImageDivider = FString("ImageDivider");
	const FString VerticalBox_0 = FString("VerticalBox_0");
	const FString Line_1 = FString("Line_1");
	const FString Line_0 = FString("Line_0");
	const FString BackgroundColour = FString("BackgroundColour");
	const FString substitution_arrow = FString("substitution_arrow");
	const FString PositionNum = FString("PositionNum");
	const FString Direction = FString("Direction");
}
