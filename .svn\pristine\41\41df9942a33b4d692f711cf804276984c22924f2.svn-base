/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/RUDBTeam.h"

#include "Databases/RUGameDatabaseManager.h"
#include "Databases/SqliteMabStatement.h"
#include "Mab/Lua/MabLuaAutoBinder.h" //#rc3_legacy_lua
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RUDatabaseCaches.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DBTeam.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DatabaseConstants.h"
#include "Match/RugbyUnion/Enums/RUPlayerPositionEnum.h"
#include "Match/RugbyUnion/RUDBPlayer.h"
#include "Match/RugbyUnion/RUDatabaseConstants.h"
#include "Match/RugbyUnion/RUDatabaseTypes.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/SIFGameWorld.h"

#include "WWUITranslationManager.h"

#include "Utility/Helpers/SIFGameHelpers.h"

#include "Json.h"
#include <string>

//#include "SIFGameHelpers.h"
//#include "Mab/Utility/MabTranslationManager.h"

#include "RugbyGameInstance.h"
#include "FanHub/WWRugbyFanHubService.h"

#if defined(GAMECENTRE_ENABLED)
#include "RUGameCentre.h"
#endif

#include <numeric>

MABRUNTIMETYPE_IMP1(RUDB_TEAM_STRIP, SqliteMabObject);
MABRUNTIMETYPE_IMP1(RUDB_TEAM, SqliteMabObject);
MABRUNTIMETYPE_IMP1(RUDB_TEAM_LITE, SqliteMabObject);
MABRUNTIMETYPE_IMP1(RUDB_UI_TEAM, SqliteMabObject);
MABRUNTIMETYPE_IMP1(RUDB_HOME_STADIUM, SqliteMabObject);
MABRUNTIMETYPE_IMP1(RUDB_TEAM_COMMENTARY_NAME, SqliteMabObject);
MABRUNTIMETYPE_IMP1(RUDB_TEAM_LOGO, SqliteMabObject);


static const MabColour::UIntByteOrder TEAM_LOGO_COLOUR_BYTE_ORDER = MabColour::MC_ARGB;

/// Constants to figure out whether to apply the star player threshold
static const unsigned int STAR_PLAYER_THRESHOLD = 7700;
static const float STAR_PLAYER_SCALE = 0.015f;

/// Weights to apply to the team and player rankings when calculating the final rank
static const float TEAM_RANKING_WEIGHT = 0.6f;
static const float PLAYER_RANKING_WEIGHT = 0.4f;

static bool in_serialisation = false;		// NOTE: OnObjectDeserialised is called after saving as well.

/// Player ranking weights
static const float RANKING_WEIGHTS[] =
{
	0.125f,
	0.125f,
	0.125f,
	0.125f,
	0.125f,
	0.125f,
	0.125f,
	0.125f,
};

// Register team types with the type database
//void RUDB_LINEUP::RegisterType(MabCentralTypeDatabase2& type_database)
//{
//	type_database.DefineType<RUDB_LINEUP, SqliteMabObject>()
//		.DefineAttribute("team_id", &RUDB_LINEUP::team_id)
//		.DefineAttribute("position_id", &RUDB_LINEUP::position)
//		//.DefineAttribute("player_id", &RUDB_LINEUP::player) // "player_id" contains the foreign key required for the RUDB_PLAYER complex object lookup
//		.DefineAttribute(PLAYER_ID_COLUMN_NAME, &RUDB_LINEUP::player_id) // "player_id" contains the foreign key required for the RUDB_PLAYER
//		.DefineAttribute("value", &RUDB_LINEUP::value)
//		.DefineAttribute("num_seasons", &RUDB_LINEUP::num_seasons)
//		;
//}

void RUDB_HOME_STADIUM::RegisterType( MabCentralTypeDatabase2& type_database )
{
	type_database.DefineType<RUDB_HOME_STADIUM, SqliteMabObject>()
		.DefineAttribute( "team_id", &RUDB_HOME_STADIUM::team_id )
		.DefineAttribute( "stadium_id", &RUDB_HOME_STADIUM::stadium_id )
		.DefineAttribute( "weight", &RUDB_HOME_STADIUM::weight )
		;
}

TSharedPtr< FJsonObject > RUDB_HOME_STADIUM::SerializeToJson()
{
	TSharedPtr< FJsonObject > obj = MakeShareable(new FJsonObject);

	obj->SetNumberField("team_id", team_id);
	obj->SetNumberField("stadium_id", stadium_id);
	obj->SetNumberField("weight", weight);

	return obj;
}

void RUDB_HOME_STADIUM::DeserializeFromJson(TSharedPtr< FJsonObject > InJson)
{
	team_id = static_cast<unsigned short>(InJson->GetNumberField("team_id"));
	stadium_id = static_cast<unsigned short>(InJson->GetNumberField("stadium_id"));
	weight = static_cast<float>(InJson->GetNumberField("weight"));
}

void RUDB_TEAM_STRIP::RegisterType(MabCentralTypeDatabase2& type_database)
{
	type_database.DefineType<RUDB_TEAM_STRIP, SqliteMabObject>()
		.DefineAttribute("texture_num", &RUDB_TEAM_STRIP::texture_num)
		.DefineAttribute("number_texture_num", &RUDB_TEAM_STRIP::number_texture_num)
		.DefineAttribute("is_home_strip", &RUDB_TEAM_STRIP::is_home_strip)
		.DefineAttribute("collar_style", &RUDB_TEAM_STRIP::collar_style)
		.DefineAttribute("primary_r", &RUDB_TEAM_STRIP::primary_r)
		.DefineAttribute("primary_g", &RUDB_TEAM_STRIP::primary_g)
		.DefineAttribute("primary_b", &RUDB_TEAM_STRIP::primary_b)
		.DefineAttribute("secondary_r", &RUDB_TEAM_STRIP::secondary_r)
		.DefineAttribute("secondary_g", &RUDB_TEAM_STRIP::secondary_g)
		.DefineAttribute("secondary_b", &RUDB_TEAM_STRIP::secondary_b)
		.DefineAttribute("tertiary_r", &RUDB_TEAM_STRIP::tertiary_r)
		.DefineAttribute("tertiary_g", &RUDB_TEAM_STRIP::tertiary_g)
		.DefineAttribute("tertiary_b", &RUDB_TEAM_STRIP::tertiary_b)
		.DefineAttribute("primary_percent", &RUDB_TEAM_STRIP::primary_percent)
		.DefineAttribute("name", &RUDB_TEAM_STRIP::SetName, &RUDB_TEAM_STRIP::GetName)
		.DefineAttribute("boot_texture_num", &RUDB_TEAM_STRIP::boot_texture_num)
		.DefineAttribute("boot_primary_r", &RUDB_TEAM_STRIP::boot_primary_r)
		.DefineAttribute("boot_primary_g", &RUDB_TEAM_STRIP::boot_primary_g)
		.DefineAttribute("boot_primary_b", &RUDB_TEAM_STRIP::boot_primary_b)
		.DefineAttribute("boot_secondary_r", &RUDB_TEAM_STRIP::boot_secondary_r)
		.DefineAttribute("boot_secondary_g", &RUDB_TEAM_STRIP::boot_secondary_g)
		.DefineAttribute("boot_secondary_b", &RUDB_TEAM_STRIP::boot_secondary_b)
		.DefineAttribute("boot_tertiary_r", &RUDB_TEAM_STRIP::boot_tertiary_r)
		.DefineAttribute("boot_tertiary_g", &RUDB_TEAM_STRIP::boot_tertiary_g)
		.DefineAttribute("boot_tertiary_b", &RUDB_TEAM_STRIP::boot_tertiary_b)
		.DefineAttribute("custom", &RUDB_TEAM_STRIP::is_custom)
#ifdef SAVE_STRIP_SERVER_DETAILS
		.DefineAttribute("created_by", &RUDB_TEAM_STRIP::SetCreatedBy, &RUDB_TEAM_STRIP::GetCreatedBy)
		.DefineAttribute("uploaded_by", &RUDB_TEAM_STRIP::SetUploadedBy, &RUDB_TEAM_STRIP::GetUploadedBy)
		.DefineAttribute("created_by_id", &RUDB_TEAM_STRIP::SetCreatedByID, &RUDB_TEAM_STRIP::GetCreatedByID)
		.DefineAttribute("uploaded_by_id", &RUDB_TEAM_STRIP::SetUploadedByID, &RUDB_TEAM_STRIP::GetUploadedByID)
		.DefineAttribute("platform_id_creator", &RUDB_TEAM_STRIP::SetPlatformIdCreator, &RUDB_TEAM_STRIP::GetPlatformIdCreator)
		.DefineAttribute("platform_id_uploader", &RUDB_TEAM_STRIP::SetPlatformIdUploader, &RUDB_TEAM_STRIP::GetPlatformIdUploader);
#else
		;
#endif
}

void RUDB_TEAM::RegisterType(MabCentralTypeDatabase2& type_database)
{
	type_database.DefineType<RUDB_TEAM, SqliteMabObject>()
		.DefineAttribute("name", &RUDB_TEAM::SetName, &RUDB_TEAM::GetName)
		.DefineAttribute("short_name", &RUDB_TEAM::SetShortName, &RUDB_TEAM::GetShortName)
		.DefineAttribute("commentary_name_id", &RUDB_TEAM::commentary_name_id)
#if !UE_BUILD_SHIPPING
		.DefineAttribute("requires_the", &RUDB_TEAM::requires_the)
#endif
		.DefineAttribute("custom", &RUDB_TEAM::custom)
		.DefineAttribute("strip_id_1", &RUDB_TEAM::strip_id, 0)
		.DefineAttribute("strip_id_2", &RUDB_TEAM::strip_id, 1)
		.DefineAttribute("strip_id_3", &RUDB_TEAM::strip_id, 2)
		.DefineAttribute("boot_texture_num", &RUDB_TEAM::boot_texture_num)
#if !UE_BUILD_SHIPPING
		.DefineAttribute("is_locked", &RUDB_TEAM::is_locked)
#endif
		.DefineAttribute("abbrev", &RUDB_TEAM::SetMnemonic, &RUDB_TEAM::GetMnemonic)
		.DefineAttribute("logo_id", &RUDB_TEAM::logo_id)
		.DefineAttribute("associated_country_id", &RUDB_TEAM::associated_country_id)
		.DefineAttribute("captain_id", &RUDB_TEAM::captain_id)
		.DefineAttribute("goal_kicker_id", &RUDB_TEAM::goal_kicker_id)
		.DefineAttribute("play_kicker_id", &RUDB_TEAM::play_kicker_id)
		.DefineAttribute("ranking", &RUDB_TEAM::ranking)
		.DefineAttribute("attack", &RUDB_TEAM::attack)
		.DefineAttribute("defence", &RUDB_TEAM::defence)
		.DefineAttribute("ruck_ability", &RUDB_TEAM::ruck_ability)
		.DefineAttribute("maul_ability", &RUDB_TEAM::maul_ability)
		.DefineAttribute("scrum_ability", &RUDB_TEAM::scrum_ability)
		.DefineAttribute("lineout_ability", &RUDB_TEAM::lineout_ability)
		.DefineAttribute("def_forward_pass_drive", &RUDB_TEAM::def_forward_pass_drive)
		.DefineAttribute("def_forward_contact_offload", &RUDB_TEAM::def_forward_contact_offload)
		.DefineAttribute("def_back_pass_kick", &RUDB_TEAM::def_back_pass_kick)
		.DefineAttribute("def_back_contact_offload", &RUDB_TEAM::def_back_contact_offload)
		.DefineAttribute("def_lineout_size", &RUDB_TEAM::def_lineout_size)
		.DefineAttribute("def_lineout_favoured_target", &RUDB_TEAM::def_lineout_favoured_target)
		.DefineAttribute("def_ruck_win", &RUDB_TEAM::def_ruck_win)
		.DefineAttribute("def_lineout_win", &RUDB_TEAM::def_lineout_win)
		.DefineAttribute("def_scrum_win", &RUDB_TEAM::def_scrum_win)
		.DefineAttribute("def_maul_win", &RUDB_TEAM::def_maul_win)
		.DefineAttribute("def_line_width", &RUDB_TEAM::def_line_width)
		.DefineAttribute("def_line_depth", &RUDB_TEAM::def_line_depth)
		.DefineAttribute("def_ruck_commitment", &RUDB_TEAM::def_ruck_commitment)
		.DefineAttribute("def_pod_option", &RUDB_TEAM::def_pod_option)
		.DefineAttribute("mid_forward_pass_drive", &RUDB_TEAM::mid_forward_pass_drive)
		.DefineAttribute("mid_forward_contact_offload", &RUDB_TEAM::mid_forward_contact_offload)
		.DefineAttribute("mid_back_pass_kick", &RUDB_TEAM::mid_back_pass_kick)
		.DefineAttribute("mid_back_contact_offload", &RUDB_TEAM::mid_back_contact_offload)
		.DefineAttribute("mid_lineout_size", &RUDB_TEAM::mid_lineout_size)
		.DefineAttribute("mid_lineout_favoured_target", &RUDB_TEAM::mid_lineout_favoured_target)
		.DefineAttribute("mid_ruck_win", &RUDB_TEAM::mid_ruck_win)
		.DefineAttribute("mid_lineout_win", &RUDB_TEAM::mid_lineout_win)
		.DefineAttribute("mid_scrum_win", &RUDB_TEAM::mid_scrum_win)
		.DefineAttribute("mid_maul_win", &RUDB_TEAM::mid_maul_win)
		.DefineAttribute("mid_line_width", &RUDB_TEAM::mid_line_width)
		.DefineAttribute("mid_line_depth", &RUDB_TEAM::mid_line_depth)
		.DefineAttribute("mid_ruck_commitment", &RUDB_TEAM::mid_ruck_commitment)
		.DefineAttribute("mid_pod_option", &RUDB_TEAM::mid_pod_option)
		.DefineAttribute("att_forward_pass_drive", &RUDB_TEAM::att_forward_pass_drive)
		.DefineAttribute("att_forward_contact_offload", &RUDB_TEAM::att_forward_contact_offload)
		.DefineAttribute("att_back_pass_kick", &RUDB_TEAM::att_back_pass_kick)
		.DefineAttribute("att_back_contact_offload", &RUDB_TEAM::att_back_contact_offload)
		.DefineAttribute("att_lineout_size", &RUDB_TEAM::att_lineout_size)
		.DefineAttribute("att_lineout_favoured_target", &RUDB_TEAM::att_lineout_favoured_target)
		.DefineAttribute("att_ruck_win", &RUDB_TEAM::att_ruck_win)
		.DefineAttribute("att_lineout_win", &RUDB_TEAM::att_lineout_win)
		.DefineAttribute("att_scrum_win", &RUDB_TEAM::att_scrum_win)
		.DefineAttribute("att_maul_win", &RUDB_TEAM::att_maul_win)
		.DefineAttribute("att_line_width", &RUDB_TEAM::att_line_width)
		.DefineAttribute("att_line_depth", &RUDB_TEAM::att_line_depth)
		.DefineAttribute("att_ruck_commitment", &RUDB_TEAM::att_ruck_commitment)
		.DefineAttribute("att_pod_option", &RUDB_TEAM::att_pod_option)
		.DefineAttribute("kick_kickoff_short_long", &RUDB_TEAM::kick_kickoff_short_long)
		.DefineAttribute("kick_kickoff_left_right", &RUDB_TEAM::kick_kickoff_left_right)
		.DefineAttribute("kick_dropout_short_long", &RUDB_TEAM::kick_dropout_short_long)
		.DefineAttribute("kick_dropout_left_right", &RUDB_TEAM::kick_dropout_left_right)
		.DefineAttribute("kick_touch_territory", &RUDB_TEAM::kick_touch_territory)
		.DefineAttribute("kick_penalty_touch_goal", &RUDB_TEAM::kick_penalty_touch_goal)
		.DefineAttribute("team_id", &RUDB_TEAM::home_stadiums)
		.DefineLuaMethod("GetDatabaseId", &RUDB_TEAM::GetDatabaseId)
		.DefineLuaMethod("SetPlayer", &RUDB_TEAM::SetPlayer)
		.DefineLuaMethod("ChangePlayerPosition", &RUDB_TEAM::ChangePlayerPosition)
		.DefineLuaMethod("RemovePlayer", &RUDB_TEAM::RemovePlayer)
		.DefineLuaMethod("HasEnoughPlayers", &RUDB_TEAM::HasEnoughPlayers)
		.DefineLuaMethod("GetMissingTitledPlayerBitField", &RUDB_TEAM::GetMissingTitledPlayerBitField)
		//.DefineLuaMethod("SwapPlayerPositions", &RUDB_TEAM::SwapPlayerPositions)
		.DefineLuaMethod("SwapPlayerPositionsAndFaces", &RUDB_TEAM::SwapPlayerPositionsAndFaces)
		.DefineLuaMethod("SetHomeStadiumId", &RUDB_TEAM::SetHomeStadiumId)
		.DefineLuaMethod("GetHomeStadiumId", &RUDB_TEAM::GetHomeStadiumId)
		.DefineLuaMethod("CopyTeamColours", &RUDB_TEAM::CopyTeamColours)
		//#dewald moved to SIFGameHelpers .DefineLuaMethod("GetLogoResource", &RUDB_TEAM::GetLogoAssetPath)
		.DefineLuaMethod("GetNumLineups", &RUDB_TEAM::GetNumLineups)
		.DefineLuaMethod("GetPlayer", &RUDB_TEAM::GetPlayer)
		.DefineLuaMethod("PlayersContractYears", &RUDB_TEAM::PlayersContractYears)
#if !UE_BUILD_SHIPPING
		.DefineAttribute("cash", &RUDB_TEAM::cash)
		.DefineAttribute("firing_fees", &RUDB_TEAM::firing_fees)
		.DefineAttribute("rep_area_id", &RUDB_TEAM::rep_area_id)
		.DefineAttribute("days_till_rep_selection_allowed", &RUDB_TEAM::days_till_rep_selection_allowed)
		.DefineAttribute("primary_comp", &RUDB_TEAM::primary_comp)
#endif
#ifdef ENABLE_SEVENS_MODE
		.DefineAttribute("r7_exclusive", &RUDB_TEAM::r7_exclusive)
#endif
		.DefineAttribute("permission_flags_gender", &RUDB_TEAM::permission_flags_gender)
		;
}

void RUDB_TEAM_LITE::RegisterType(MabCentralTypeDatabase2& type_database)
{
	type_database.DefineType<RUDB_TEAM_LITE, SqliteMabObject>()
		.DefineAttribute("name", &RUDB_TEAM_LITE::SetName, &RUDB_TEAM_LITE::GetName)
		.DefineAttribute("short_name", &RUDB_TEAM_LITE::SetShortName, &RUDB_TEAM_LITE::GetShortName)
		.DefineAttribute("abbrev", &RUDB_TEAM_LITE::SetMnemonic, &RUDB_TEAM_LITE::GetMnemonic)
		.DefineAttribute("requires_the", &RUDB_TEAM_LITE::name_requires_the)
		.DefineAttribute("logo_id", &RUDB_TEAM_LITE::logo_id)
		.DefineAttribute("strip_id_1", &RUDB_TEAM_LITE::strip_id, 0)
		.DefineAttribute("strip_id_2", &RUDB_TEAM_LITE::strip_id, 1)
		.DefineAttribute("strip_id_3", &RUDB_TEAM_LITE::strip_id, 2)
		.DefineAttribute("commentary_name_id", &RUDB_TEAM_LITE::commentary_name_id);
}

void RUDB_UI_TEAM::RegisterType(MabCentralTypeDatabase2& type_database)
{
	type_database.DefineType<RUDB_UI_TEAM, SqliteMabObject>()
		.DefineAttribute("player_id_1", &RUDB_UI_TEAM::player_id, 0)
		.DefineAttribute("player_id_2", &RUDB_UI_TEAM::player_id, 1)
		.DefineAttribute("player_id_3", &RUDB_UI_TEAM::player_id, 2)
		.DefineAttribute("player_id_4", &RUDB_UI_TEAM::player_id, 3);
}

RUDB_TEAM_COMMENTARY_NAME::RUDB_TEAM_COMMENTARY_NAME()
{
	name[ 0 ] = '\0';
}


void RUDB_TEAM_COMMENTARY_NAME::RegisterType( MabCentralTypeDatabase2& type_database )
{
	type_database.DefineType< RUDB_TEAM_COMMENTARY_NAME, SqliteMabObject >()
		.DefineAttribute( "name", &RUDB_TEAM_COMMENTARY_NAME::SetName, &RUDB_TEAM_COMMENTARY_NAME::GetName );
}

RUDB_TEAM_LOGO::RUDB_TEAM_LOGO()
{
	name[ 0 ] = '\0';
	filename[ 0 ] = '\0';
}


void RUDB_TEAM_LOGO::RegisterType( MabCentralTypeDatabase2& type_database )
{
	type_database.DefineType< RUDB_TEAM_LOGO, SqliteMabObject >()
		.DefineAttribute( "name", &RUDB_TEAM_LOGO::SetName, &RUDB_TEAM_LOGO::GetName )
		.DefineAttribute( "filename", &RUDB_TEAM_LOGO::SetFilename, &RUDB_TEAM_LOGO::GetFilename )
		.DefineAttribute("primary_logo_colour", &RUDB_TEAM_LOGO::SetPrimaryColourValue, &RUDB_TEAM_LOGO::GetPrimaryColourValue)
		.DefineAttribute("screen_friendly_logo_colour", &RUDB_TEAM_LOGO::SetScreenFriendlyColourValue, &RUDB_TEAM_LOGO::GetScreenFriendlyColourValue)
		.DefineAttribute("splotch_logo_colour", &RUDB_TEAM_LOGO::SetSplotchColourValue, &RUDB_TEAM_LOGO::GetSplotchColourValue)
;
}

void RUDB_TEAM_LOGO::GetScreenFriendlyColour( MabColour& colour_out )
{
	if( MabMath::Feq(screen_friendly_logo_colour.a, 0.0f) )
	{
		colour_out = primary_logo_colour;
	}
	else
	{
		colour_out = screen_friendly_logo_colour;
	}
}

void RUDB_TEAM_LOGO::SetPrimaryColourValue( int colour_val )
{
	primary_logo_colour = MabColour::FromUInt( (unsigned int)colour_val, TEAM_LOGO_COLOUR_BYTE_ORDER );
	primary_logo_colour.SetAlpha( 1.0f );
}

void RUDB_TEAM_LOGO::SetScreenFriendlyColourValue( int colour_val )
{
	if( colour_val != 0 )
	{
		screen_friendly_logo_colour = MabColour::FromUInt( (unsigned int)colour_val, TEAM_LOGO_COLOUR_BYTE_ORDER );
		screen_friendly_logo_colour.SetAlpha( 1.0f );
	}
	else if( primary_logo_colour.a != 0.0f )
	{
		screen_friendly_logo_colour = primary_logo_colour;
	}
	// Otherwise leave it as it was initialised.
}

void RUDB_TEAM_LOGO::SetSplotchColourValue(int colour_val)
{
	splotch_logo_colour = MabColour::FromUInt((unsigned int)colour_val, TEAM_LOGO_COLOUR_BYTE_ORDER);
	splotch_logo_colour.SetAlpha(1.0f);
}

int RUDB_TEAM_LOGO::GetPrimaryColourValue() const
{
	MabColour tmp_colour = primary_logo_colour;
	tmp_colour.a = 0.0f;
	return (int)( tmp_colour.ToUInt( TEAM_LOGO_COLOUR_BYTE_ORDER ) );
}

int RUDB_TEAM_LOGO::GetScreenFriendlyColourValue() const
{
	if( screen_friendly_logo_colour == primary_logo_colour )
	{
		return 0;
	}
	else
	{
		MabColour tmp_colour = screen_friendly_logo_colour;
		tmp_colour.a = 0.0f;
		return (int)( tmp_colour.ToUInt( TEAM_LOGO_COLOUR_BYTE_ORDER ) );
	}
}

int RUDB_TEAM_LOGO::GetSplotchColourValue() const
{
	MabColour tmp_colour = primary_logo_colour;
	tmp_colour.a = 0.0f;
	return (int)(tmp_colour.ToUInt(TEAM_LOGO_COLOUR_BYTE_ORDER));
}

TSharedPtr< FJsonObject > RUDB_TEAM_LOGO::SerializeToJson()
{
	TSharedPtr< FJsonObject > objLogo = MakeShareable(new FJsonObject);

	objLogo->SetStringField("name", name);
	objLogo->SetStringField("filename", filename);

	//Visuals
	TSharedPtr< FJsonObject > logoColourObj = MakeShareable(new FJsonObject);

	logoColourObj->SetNumberField("r", primary_logo_colour.r);
	logoColourObj->SetNumberField("g", primary_logo_colour.g);
	logoColourObj->SetNumberField("b", primary_logo_colour.b);
	logoColourObj->SetNumberField("a", primary_logo_colour.a);

	objLogo->SetObjectField("primary_logo_colour", logoColourObj);

	//Visuals
	TSharedPtr< FJsonObject > splotchLogoColourObj = MakeShareable(new FJsonObject);

	splotchLogoColourObj->SetNumberField("r", splotch_logo_colour.r);
	splotchLogoColourObj->SetNumberField("g", splotch_logo_colour.g);
	splotchLogoColourObj->SetNumberField("b", splotch_logo_colour.b);
	splotchLogoColourObj->SetNumberField("a", splotch_logo_colour.a);

	objLogo->SetObjectField("splotch_logo_colour", splotchLogoColourObj);

	TSharedPtr< FJsonObject > screenFriendlyLogoColourObj = MakeShareable(new FJsonObject);

	screenFriendlyLogoColourObj->SetNumberField("r", screen_friendly_logo_colour.r);
	screenFriendlyLogoColourObj->SetNumberField("g", screen_friendly_logo_colour.g);
	screenFriendlyLogoColourObj->SetNumberField("b", screen_friendly_logo_colour.b);
	screenFriendlyLogoColourObj->SetNumberField("a", screen_friendly_logo_colour.a);

	objLogo->SetObjectField("screen_friendly_logo_colour", screenFriendlyLogoColourObj);

	return objLogo;
}

void RUDB_TEAM_LOGO::DeserializeFromJson(TSharedPtr< FJsonObject > InJson)
{
	std::string str_name = TCHAR_TO_UTF8(*InJson->GetStringField("name"));
	memcpy(name, str_name.c_str(), str_name.size());
	name[str_name.size()] = '\0';

	std::string str_file_name = TCHAR_TO_UTF8(*InJson->GetStringField("filename"));
	memcpy(filename, str_file_name.c_str(), str_file_name.size());
	filename[str_file_name.size()] = '\0';

	TSharedPtr< FJsonObject > logoColourObj = InJson->GetObjectField("primary_logo_colour");

	primary_logo_colour.r = static_cast<float>(logoColourObj->GetNumberField("r"));
	primary_logo_colour.g = static_cast<float>(logoColourObj->GetNumberField("g"));
	primary_logo_colour.b = static_cast<float>(logoColourObj->GetNumberField("b"));
	primary_logo_colour.a = static_cast<float>(logoColourObj->GetNumberField("a"));

	TSharedPtr< FJsonObject > splotchLogoColourObj = InJson->GetObjectField("splotch_logo_colour");

	splotch_logo_colour.r = static_cast<float>(splotchLogoColourObj->GetNumberField("r"));
	splotch_logo_colour.g = static_cast<float>(splotchLogoColourObj->GetNumberField("g"));
	splotch_logo_colour.b = static_cast<float>(splotchLogoColourObj->GetNumberField("b"));
	splotch_logo_colour.a = static_cast<float>(splotchLogoColourObj->GetNumberField("a"));

	TSharedPtr< FJsonObject > screenFriendlyLogoColourObj = InJson->GetObjectField("screen_friendly_logo_colour");

	screen_friendly_logo_colour.r = static_cast<float>(InJson->GetNumberField("r"));
	screen_friendly_logo_colour.g = static_cast<float>(InJson->GetNumberField("g"));
	screen_friendly_logo_colour.b = static_cast<float>(InJson->GetNumberField("b"));
	screen_friendly_logo_colour.a = static_cast<float>(InJson->GetNumberField("a"));

}


RUDB_UI_TEAM::RUDB_UI_TEAM()
{
	std::fill(player_id, player_id + StaticArraySize(player_id), SQLITEMAB_INVALID_ID);
}

RUDB_TEAM_LITE::RUDB_TEAM_LITE()
{
	MabStringHelper::Strcpy(name, "");
	commentary_name[0] = '\0';
	logo_string[0] = '\0';
}

/// Gets the name to use for commentary.
const char* RUDB_TEAM_LITE::GetAndCacheCommentaryName() const
{
	if( commentary_name_id == SQLITEMAB_INVALID_ID ) return GetName();

	if( commentary_name[0] != '\0' ) return commentary_name;

	// Means commentary name's not yet been set and commentary_name_id is a foreign key.
	RUDB_TEAM_COMMENTARY_NAME tmp_commentary_name;
	SIFApplication::GetApplication()->GetGameDatabaseManager()->LoadData( tmp_commentary_name, commentary_name_id );
	MabStringHelper::Strcpy( commentary_name, tmp_commentary_name.name );
	return commentary_name;
}

const char* RUDB_TEAM_LITE::GetAndCacheLogoString() const
{
	if( logo_string[0] != '\0' ) return logo_string;

	if( logo_id == SQLITEMAB_INVALID_ID )
	{
		return "";
	}

	RUDB_TEAM_LOGO tmp_logo;
	SIFApplication::GetApplication()->GetGameDatabaseManager()->LoadData( tmp_logo, logo_id );
	MabStringHelper::Strcpy( logo_string, tmp_logo.GetFilename() );
	return logo_string;
}


RUDB_LINEUP::RUDB_LINEUP()
: team_id(SQLITEMAB_INVALID_ID)
, position(PP_NONE)
, player_id(SQLITEMAB_INVALID_ID)
, value(0)
, num_seasons(0)
{
}

RUDB_LINEUP::RUDB_LINEUP(unsigned short team_id, PLAYER_POSITION position, unsigned short player_id)
: team_id(team_id)
, position(position)
, player_id(player_id)
, value(100000)
, num_seasons(2)
{
}


TSharedPtr< FJsonObject > RUDB_LINEUP::SerializeToJson()
{
	TSharedPtr< FJsonObject > objLineup = MakeShareable(new FJsonObject);

#if defined(FANHUB_ENABLED)

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance && player_id > DB_LAST_PLAYER_ID)
	{
		UWWRugbyFanHubService* pFanHubService = Cast<UWWRugbyFanHubService>(pRugbyGameInstance->GetFanHubService());

		if (pFanHubService)
		{
			FString* pServerID = pFanHubService->GetServerPlayerFromMap(player_id);

			MABASSERTMSG(pServerID, "Couldn't find the players server ID? Were they uploaded correctly");
			if (pServerID)
			{
				objLineup->SetStringField("server_id", *pServerID);
			}
			objLineup->SetNumberField("player_id", DB_SERVER_PLAYER_ID);
		}
		else
		{
			objLineup->SetNumberField("player_id", player_id);
		}
	}
	else
#endif
	{
		objLineup->SetNumberField("player_id", player_id);
	}

	objLineup->SetNumberField("value", value);
	objLineup->SetNumberField("num_seasons", num_seasons);
	objLineup->SetNumberField("team_id", team_id);
	objLineup->SetNumberField("position", position);

	return objLineup;
}

void RUDB_LINEUP::DeserializeFromJson(TSharedPtr< FJsonObject > InJson)
{
	player_id = InJson->GetNumberField("player_id");

	if (player_id == DB_SERVER_PLAYER_ID)
	{
		server_id = InJson->GetStringField("server_id");
	}

	value = static_cast<int>(InJson->GetNumberField("value"));
	num_seasons = static_cast<short>(InJson->GetNumberField("num_seasons"));
	team_id = static_cast<unsigned short>(InJson->GetNumberField("team_id"));
	position = static_cast<PLAYER_POSITION>((int)(InJson->GetNumberField("position")));

	switch (position)
	{
		// :jb do we **ever** want to download a touch judge in a lineup?
		case PP_TOUCHJUDGE_LEFT:
		case PP_TOUCHJUDGE_RIGHT:
		case PP_TOUCHJUDGE_NORTH:
		case PP_TOUCHJUDGE_SOUTH:
			position = PP_REFEREE;
			break;
		default:
			break;
	}
}



RUDB_TEAM_STRIP::RUDB_TEAM_STRIP()
: collar_style(0)
, texture_num(0)
, number_texture_num(0)
, primary_r(0)
, primary_g(0)
, primary_b(0)
, secondary_r(0)
, secondary_g(0)
, secondary_b(0)
, tertiary_r(0)
, tertiary_g(0)
, tertiary_b(0)
, boot_texture_num(0)
, boot_primary_r(0)
, boot_primary_g(0)
, boot_primary_b(0)
, boot_secondary_r(0)
, boot_secondary_g(0)
, boot_secondary_b(0)
, boot_tertiary_r(0)
, boot_tertiary_g(0)
, boot_tertiary_b(0)
, primary_percent(50)
, is_home_strip(true)
, is_custom(false)
{
	SetCreatedBy("");
	SetUploadedBy("");
	SetPlatformIdCreator("");
	SetPlatformIdUploader("");
	SetCreatedByID("");
	SetUploadedByID("");
}

void RUDB_TEAM_STRIP::Reset()
{
	texture_num = 0;
	number_texture_num = 0;
	primary_r = 0;
	primary_g = 0;
	primary_b = 0;
	secondary_r = 0;
	secondary_g = 0;
	secondary_b = 0;
	tertiary_r = 0;
	tertiary_g = 0;
	tertiary_b = 0;
	boot_texture_num = 0;
	boot_primary_r = 0;
	boot_primary_g = 0;
	boot_primary_b = 0;
	boot_secondary_r = 0;
	boot_secondary_g = 0;
	boot_secondary_b = 0;
	boot_tertiary_r = 0;
	boot_tertiary_g = 0;
	boot_tertiary_b = 0;
	primary_percent = 50;
	is_home_strip = true;
	is_custom = false;
	db_id = 0;

	SetCreatedBy("");
	SetUploadedBy("");
	SetPlatformIdCreator("");
	SetPlatformIdUploader("");
	SetCreatedByID("");
	SetUploadedByID("");
}


TSharedPtr< FJsonObject > RUDB_TEAM_STRIP::SerializeToJson()
{
	TSharedPtr< FJsonObject > obj = MakeShareable(new FJsonObject);

	obj->SetStringField("name", GetName());

	obj->SetNumberField("texture_num",			texture_num);
	obj->SetNumberField("collar_style",			collar_style);
	obj->SetNumberField("number_texture_num",	number_texture_num);
	obj->SetNumberField("primary_r",			primary_r);
	obj->SetNumberField("primary_g",			primary_g);
	obj->SetNumberField("primary_b",			primary_b);
	obj->SetNumberField("secondary_r",			secondary_r);
	obj->SetNumberField("secondary_g",			secondary_g);
	obj->SetNumberField("secondary_b",			secondary_b);
	obj->SetNumberField("tertiary_r",			tertiary_r);
	obj->SetNumberField("tertiary_g",			tertiary_g);
	obj->SetNumberField("tertiary_b",			tertiary_b);
	obj->SetNumberField("boot_texture_num",		boot_texture_num);
	obj->SetNumberField("boot_primary_r",		boot_primary_r);
	obj->SetNumberField("boot_primary_g",		boot_primary_g);
	obj->SetNumberField("boot_primary_b",		boot_primary_b);
	obj->SetNumberField("boot_secondary_r",		boot_secondary_r);
	obj->SetNumberField("boot_secondary_g",		boot_secondary_g);
	obj->SetNumberField("boot_secondary_b",		boot_secondary_b);
	obj->SetNumberField("boot_tertiary_r",		boot_tertiary_r);
	obj->SetNumberField("boot_tertiary_g",		boot_tertiary_g);
	obj->SetNumberField("boot_tertiary_b",		boot_tertiary_b);

	// Set created by IDs.
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		if (pRugbyGameInstance)
		{
			UWWRugbyFanHubService* pFanHubService = Cast<UWWRugbyFanHubService>(pRugbyGameInstance->GetFanHubService());

			if (pFanHubService)
			{
				if (FString(GetCreatedByID()).IsEmpty())
				{
					SetCreatedByID(TCHAR_TO_UTF8(*pFanHubService->GetWWID()));
				}
			}
		}
	}

	// Just send along who made this.
	obj->SetStringField("created_by_id", GetCreatedByID());

	return obj;
}

void RUDB_TEAM_STRIP::DeserializeFromJson(TSharedPtr< FJsonObject > InJson, FString in_uploaded_by /*= FString()*/, FString in_platform_uploader_id /*= FString()*/, FString in_uploaded_by_id /*= FString()*/)
{
	SetName(TCHAR_TO_UTF8(*InJson->GetStringField("name")));

	texture_num =			InJson->GetNumberField("texture_num");
	collar_style =			InJson->GetNumberField("collar_style");
	number_texture_num =	InJson->GetNumberField("number_texture_num");
	primary_r =				InJson->GetNumberField("primary_r");
	primary_g =				InJson->GetNumberField("primary_g");
	primary_b =				InJson->GetNumberField("primary_b");
	secondary_r =			InJson->GetNumberField("secondary_r");
	secondary_g =			InJson->GetNumberField("secondary_g");
	secondary_b =			InJson->GetNumberField("secondary_b");
	tertiary_r =			InJson->GetNumberField("tertiary_r");
	tertiary_g =			InJson->GetNumberField("tertiary_g");
	tertiary_b =			InJson->GetNumberField("tertiary_b");
	boot_texture_num =		InJson->GetNumberField("boot_texture_num");
	boot_primary_r =		InJson->GetNumberField("boot_primary_r");
	boot_primary_g =		InJson->GetNumberField("boot_primary_g");
	boot_primary_b =		InJson->GetNumberField("boot_primary_b");
	boot_secondary_r =		InJson->GetNumberField("boot_secondary_r");
	boot_secondary_g =		InJson->GetNumberField("boot_secondary_g");
	boot_secondary_b =		InJson->GetNumberField("boot_secondary_b");
	boot_tertiary_r =		InJson->GetNumberField("boot_tertiary_r");
	boot_tertiary_g =		InJson->GetNumberField("boot_tertiary_g");
	boot_tertiary_b =		InJson->GetNumberField("boot_tertiary_b");

	// Uploader info.
	SetUploadedBy(TCHAR_TO_UTF8(*in_uploaded_by));
	SetPlatformIdUploader(TCHAR_TO_UTF8(*in_platform_uploader_id));

	// Creator info.
	{
		FString created_by_fstring = InJson->GetStringField("created_by");
		FString platform_id_creator_fstring = InJson->GetStringField("platform_creator_id");

		SetCreatedBy(TCHAR_TO_UTF8(*created_by_fstring));
		SetPlatformIdCreator(TCHAR_TO_UTF8(*platform_id_creator_fstring));
	}

	// WWIDs of the people who created and uploaded this.
	SetCreatedByID(TCHAR_TO_UTF8(*InJson->GetStringField("created_by_id")));
	SetUploadedByID(TCHAR_TO_UTF8(*in_uploaded_by_id));
}

static const unsigned short DEFAULT_TEAM_ATTRIBUTE_SETTING = 7500;
static const unsigned short DEFAULT_TACTICS_SETTING = 5000;

RUDB_TEAM::RUDB_TEAM() :
	SqliteMabObject()
	//,name()
	//,short_name()
	//,commentary_name()
	,commentary_name_id(SQLITEMAB_INVALID_ID)
	//,mnemonic()
	,logo_id(SQLITEMAB_INVALID_ID)
	,logo()
	//,custom(false)
	,associated_country_id(SQLITEMAB_INVALID_ID)
	//,strip_id()
	,boot_texture_num(SQLITEMAB_INVALID_ID)
	,lineup()
	,willingness_to_attack(DEFAULT_TEAM_ATTRIBUTE_SETTING)
	,ranking(DEFAULT_TEAM_ATTRIBUTE_SETTING)
	,attack(DEFAULT_TEAM_ATTRIBUTE_SETTING)
	,defence(DEFAULT_TEAM_ATTRIBUTE_SETTING)
	,ruck_ability(DEFAULT_TEAM_ATTRIBUTE_SETTING)
	,maul_ability(DEFAULT_TEAM_ATTRIBUTE_SETTING)
	,scrum_ability(DEFAULT_TEAM_ATTRIBUTE_SETTING)
	,lineout_ability(DEFAULT_TEAM_ATTRIBUTE_SETTING)
	,default_run_kick_slider(DEFAULT_TACTICS_SETTING)
	,default_lineout_quickness_slider(DEFAULT_TACTICS_SETTING)
	,default_ruck_commitment_slider(DEFAULT_TACTICS_SETTING)
	,default_defensive_line_speed_slider(DEFAULT_TACTICS_SETTING)
	,def_forward_pass_drive(DEFAULT_TACTICS_SETTING)
	,def_forward_contact_offload(DEFAULT_TACTICS_SETTING)
	,def_back_pass_kick(DEFAULT_TACTICS_SETTING)
	,def_back_contact_offload(DEFAULT_TACTICS_SETTING)
	,def_lineout_size(DEFAULT_TACTICS_SETTING)
	,def_lineout_favoured_target(DEFAULT_TACTICS_SETTING)
	,def_ruck_win(DEFAULT_TACTICS_SETTING)
	,def_lineout_win(DEFAULT_TACTICS_SETTING)
	,def_scrum_win(DEFAULT_TACTICS_SETTING)
	,def_maul_win(DEFAULT_TACTICS_SETTING)
	,def_line_width(DEFAULT_TACTICS_SETTING)
	,def_line_depth(DEFAULT_TACTICS_SETTING)
	,def_ruck_commitment(DEFAULT_TACTICS_SETTING)
	,def_pod_option(0)
	,mid_forward_pass_drive(DEFAULT_TACTICS_SETTING)
	,mid_forward_contact_offload(DEFAULT_TACTICS_SETTING)
	,mid_back_pass_kick(DEFAULT_TACTICS_SETTING)
	,mid_back_contact_offload(DEFAULT_TACTICS_SETTING)
	,mid_lineout_size(DEFAULT_TACTICS_SETTING)
	,mid_lineout_favoured_target(DEFAULT_TACTICS_SETTING)
	,mid_ruck_win(DEFAULT_TACTICS_SETTING)
	,mid_lineout_win(DEFAULT_TACTICS_SETTING)
	,mid_scrum_win(DEFAULT_TACTICS_SETTING)
	,mid_maul_win(DEFAULT_TACTICS_SETTING)
	,mid_line_width(DEFAULT_TACTICS_SETTING)
	,mid_line_depth(DEFAULT_TACTICS_SETTING)
	,mid_ruck_commitment(DEFAULT_TACTICS_SETTING)
	,mid_pod_option(0)
	,att_forward_pass_drive(DEFAULT_TACTICS_SETTING)
	,att_forward_contact_offload(DEFAULT_TACTICS_SETTING)
	,att_back_pass_kick(DEFAULT_TACTICS_SETTING)
	,att_back_contact_offload(DEFAULT_TACTICS_SETTING)
	,att_lineout_size(DEFAULT_TACTICS_SETTING)
	,att_lineout_favoured_target(DEFAULT_TACTICS_SETTING)
	,att_ruck_win(DEFAULT_TACTICS_SETTING)
	,att_lineout_win(DEFAULT_TACTICS_SETTING)
	,att_scrum_win(DEFAULT_TACTICS_SETTING)
	,att_maul_win(DEFAULT_TACTICS_SETTING)
	,att_line_width(DEFAULT_TACTICS_SETTING)
	,att_line_depth(DEFAULT_TACTICS_SETTING)
	,att_ruck_commitment(DEFAULT_TACTICS_SETTING)
	,att_pod_option(0)
	,kick_kickoff_short_long(DEFAULT_TACTICS_SETTING)
	,kick_kickoff_left_right(DEFAULT_TACTICS_SETTING)
	,kick_dropout_short_long(DEFAULT_TACTICS_SETTING)
	,kick_dropout_left_right(DEFAULT_TACTICS_SETTING)
	,kick_touch_territory(DEFAULT_TACTICS_SETTING)
	,kick_penalty_touch_goal(DEFAULT_TACTICS_SETTING)

	,r7_exclusive(false)
	,permission_flags_gender(PLAYER_GENDER_FLAG_MALE)

	,home_stadiums()
	,captain_id(SQLITEMAB_INVALID_ID)
	,play_kicker_id(SQLITEMAB_INVALID_ID)
	,goal_kicker_id(SQLITEMAB_INVALID_ID)
	,rating(3.0)
	,team_mongodb_id()

#if defined(FANHUB_ENABLED)
	,server_id ("")
	,uploaded_by ("")
	,created_by ("")
	,user_rating(0)
	,server_status("")
#endif

	// These variables are here in non shipping builds for writing out to the CSV database.
#if !UE_BUILD_SHIPPING
	,requires_the (false)
	,is_locked (false)
	,firing_fees (0)
	,cash (0)
	,rep_area_id (0)
	,days_till_rep_selection_allowed (0)
	,primary_comp (0)
#endif // !UE_BUILD_SHIPPING
	,sender_node_id(-1)
	,is_deserialized(false)

{
	memset(name,0,sizeof(name));
	memset(short_name,0,sizeof(short_name));
	memset(commentary_name,0,sizeof(commentary_name));
	memset(mnemonic,0,sizeof(mnemonic));
	memset(strip_id,0,sizeof(strip_id));
	memset(server_strip_id,0,sizeof(server_strip_id));

	name[0] = '\0';
	commentary_name[0] = '\0';
	mnemonic[0] = '\0';
	team_mongodb_id.clear();

	commentary_name_id = DB_INVALID_ID;

	for(unsigned int i = 0; i < MAX_STRIPS; i++)
		strip_id[i] = SQLITEMAB_INVALID_ID;

	for (unsigned int i = 0; i < MAX_STRIPS; i++)
		server_strip_id[i] = SQLITEMAB_INVALID_ID;
}

void RUDB_TEAM::Reset() {  *this = RUDB_TEAM(); }

void RUDB_TEAM::OverrideStats( const RUDB_TEAM& team )
{
	willingness_to_attack = team.willingness_to_attack;

	/// @name Attributes
	ranking			= team.ranking;
	attack			= team.attack;
	defence			= team.defence;
	ruck_ability	= team.ruck_ability;
	maul_ability	= team.maul_ability;
	scrum_ability	= team.scrum_ability;
	lineout_ability = team.lineout_ability;

	/// @name Slider defaults
	default_run_kick_slider				= team.default_run_kick_slider;
	default_lineout_quickness_slider	= team.default_lineout_quickness_slider;
	default_ruck_commitment_slider		= team.default_ruck_commitment_slider;
	default_defensive_line_speed_slider = team.default_defensive_line_speed_slider;

	/// Defence Sliders
	def_forward_pass_drive			= team.def_forward_pass_drive;
	def_forward_contact_offload		= team.def_forward_contact_offload;
	def_back_pass_kick				= team.def_back_pass_kick;
	def_back_contact_offload		= team.def_back_contact_offload;
	def_lineout_size				= team.def_lineout_size;
	def_lineout_favoured_target		= team.def_lineout_favoured_target;
	def_ruck_win					= team.def_ruck_win;
	def_lineout_win					= team.def_lineout_win;
	def_scrum_win					= team.def_scrum_win;
	def_maul_win					= team.def_maul_win;
	def_line_width					= team.def_line_width;
	def_line_depth					= team.def_line_depth;
	def_ruck_commitment				= team.def_ruck_commitment;
	def_pod_option					= team.def_pod_option;

	/// Midfield Sliders
	mid_forward_pass_drive			= team.mid_forward_pass_drive;
	mid_forward_contact_offload		= team.mid_forward_contact_offload;
	mid_back_pass_kick				= team.mid_back_pass_kick;
	mid_back_contact_offload		= team.mid_back_contact_offload;
	mid_lineout_size				= team.mid_lineout_size;
	mid_lineout_favoured_target		= team.mid_lineout_favoured_target;
	mid_ruck_win					= team.mid_ruck_win;
	mid_lineout_win					= team.mid_lineout_win;
	mid_scrum_win					= team.mid_scrum_win;
	mid_maul_win					= team.mid_maul_win;
	mid_line_width					= team.mid_line_width;
	mid_line_depth					= team.mid_line_depth;
	mid_ruck_commitment				= team.mid_ruck_commitment;
	mid_pod_option					= team.mid_pod_option;

	/// Attach Sliders
	att_forward_pass_drive			= team.att_forward_pass_drive;
	att_forward_contact_offload		= team.att_forward_contact_offload;
	att_back_pass_kick				= team.att_back_pass_kick;
	att_back_contact_offload		= team.att_back_contact_offload;
	att_lineout_size				= team.att_lineout_size;
	att_lineout_favoured_target		= team.att_lineout_favoured_target;
	att_ruck_win					= team.att_ruck_win;
	att_lineout_win					= team.att_lineout_win;
	att_scrum_win					= team.att_scrum_win;
	att_maul_win					= team.att_maul_win;
	att_line_width					= team.att_line_width;
	att_line_depth					= team.att_line_depth;
	att_ruck_commitment				= team.att_ruck_commitment;
	att_pod_option					= team.att_pod_option;

	/// One off sliders
	kick_kickoff_short_long			= team.kick_kickoff_short_long;
	kick_kickoff_left_right			= team.kick_kickoff_left_right;
	kick_dropout_short_long			= team.kick_dropout_short_long;
	kick_dropout_left_right			= team.kick_dropout_left_right;
	kick_touch_territory			= team.kick_touch_territory;
	kick_penalty_touch_goal			= team.kick_penalty_touch_goal;
}

void RUDB_TEAM::CopyRUDBTeam( const RUDB_TEAM& from )
{
	MABASSERT(&from != this);
	if (&from == this)
		return;

	OverrideStats( from );

	commentary_name_id = from.commentary_name_id;
	SetName(from.GetName());
	SetShortName(from.GetShortName());
	SetMnemonic(from.GetMnemonic());

	associated_country_id = from.associated_country_id;
	ranking = from.ranking;
	captain_id = from.captain_id;
	play_kicker_id = from.play_kicker_id;
	goal_kicker_id = from.goal_kicker_id;
	logo_id = from.logo_id;
	boot_texture_num = from.boot_texture_num;
	r7_exclusive = from.r7_exclusive;
	permission_flags_gender = from.permission_flags_gender;

	rating = from.rating;

	RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();
	database_manager->LoadData(logo, logo_id);

	lineup.clear();
	int i = 0;
	for (i = 0; i < from.GetNumLineups(); i++)
	{
		lineup.push_back(from.GetLineup(i));
	}

	home_stadiums.clear();
	MabVector<RUDB_HOME_STADIUM> tmp = from.GetHomeStadiums();
	for (i = 0; i < (int)tmp.size(); i++)
	{
		//tmp.at(i).team_id = GetDbId();
		home_stadiums.push_back(tmp.at(i));
	}

	for (size_t k = 0; k < MAX_STRIPS; k++)
	{
		strip_id[k] = from.GetStripId(k);
	}

	for (size_t k = 0; k < MAX_STRIPS; k++)
	{
		server_strip_id[k] = from.GetServerStripId(k);
	}
}

void RUDB_TEAM::SetAttribute(TEAM_ATTRIBUTE attribute, unsigned short value)
{
	switch (attribute)
	{
	case ATTACK:
	{
		attack = value;
	}
	break;
	case DEFENCE:
	{
		defence = value;
	}
	break;
	case RUCKING:
	{
		ruck_ability = value;
	}
	break;
	case MAULING:
	{
		maul_ability = value;
	}
	break;
	case SCRUMMANGING:
	{
		scrum_ability = value;
	}
	break;
	case LINEOUTS:
	{
		lineout_ability = value;
	}
	break;
	}
}

unsigned short RUDB_TEAM::GetAttribute(TEAM_ATTRIBUTE attribute)
{
	switch (attribute)
	{
	case ATTACK:
	{
		return attack;
	}
	break;
	case DEFENCE:
	{
		return defence;
	}
	break;
	case RUCKING:
	{
		return ruck_ability;
	}
	break;
	case MAULING:
	{
		return maul_ability;
	}
	break;
	case SCRUMMANGING:
	{
		return scrum_ability;
	}
	break;
	case LINEOUTS:
	{
		return lineout_ability;
	}
	break;
	}

	return -1;
}

/// Gets the name to use for commentary.
const char* RUDB_TEAM::GetAndCacheCommentaryName() const
{
	if( commentary_name_id == SQLITEMAB_INVALID_ID ) return GetName();

	if( commentary_name[0] != '\0' ) return commentary_name;

	// Means commentary name's not yet been set and commentary_name_id is a foreign key.
	RUDB_TEAM_COMMENTARY_NAME tmp_commentary_name;
	SIFApplication::GetApplication()->GetGameDatabaseManager()->LoadData( tmp_commentary_name, commentary_name_id );
	MabStringHelper::Strcpy( commentary_name, tmp_commentary_name.name );
	return commentary_name;
}

const char* RUDB_TEAM::GetLogoFileName() const
{
	if( logo.GetDbId() != 0 ) return logo.GetFilename();

	if( logo_id == SQLITEMAB_INVALID_ID )
	{
		return "";
	}

	SIFApplication::GetApplication()->GetGameDatabaseManager()->LoadData( logo, logo_id );
	return logo.GetFilename();
}

void RUDB_TEAM::GetPrimaryColour( MabColour& colour_out ) const
{
	if( logo.GetDbId() != 0 )
	{
		colour_out = logo.primary_logo_colour;
	}
	else
	{
		MABASSERT(logo_id != SQLITEMAB_INVALID_ID);
		// If the logo ID is 0, it will crash the game, so don't load the logo in this case and return black.
		// It's a catch until Kade works out the actual problem of why we're getting here.
		// It generally happens in the fan hub when viewing a list of teams and going between teams as is in the process of loading.
		if( logo_id == SQLITEMAB_INVALID_ID )
		{
			colour_out = MabColour::Black;
		}
		else
		{
			SIFApplication::GetApplication()->GetGameDatabaseManager()->LoadData( logo, logo_id );
			colour_out = logo.primary_logo_colour;
		}
	}
}

// Gets the secondary colour of the team, as defined by its logo.
void RUDB_TEAM::GetScreenFriendlyColour( MabColour& colour_out ) const
{
	if( logo.GetDbId() != 0 )
	{
		logo.GetScreenFriendlyColour( colour_out );
	}
	else
	{
		MABASSERT(logo_id != SQLITEMAB_INVALID_ID);
		// If the logo ID is 0, it will crash the game, so don't load the logo in this case and return black.
		// It's a catch until Kade works out the actual problem of why we're getting here.
		// It generally happens in the fan hub when viewing a list of teams and going between teams as is in the process of loading.
		if( logo_id == SQLITEMAB_INVALID_ID )
		{
			colour_out = MabColour::Black;
		}
		else
		{
			SIFApplication::GetApplication()->GetGameDatabaseManager()->LoadData( logo, logo_id );
			logo.GetScreenFriendlyColour( colour_out );
		}
	}
}

void RUDB_TEAM::GetSplotchLogoColour(MabColour& colour_out) const
{
	if (logo.GetDbId() != 0)
	{
		colour_out = logo.splotch_logo_colour;
	}
	else
	{
		MABASSERT(logo_id != SQLITEMAB_INVALID_ID);
		// If the logo ID is 0, it will crash the game, so don't load the logo in this case and return black.
		// It's a catch until Kade works out the actual problem of why we're getting here.
		// It generally happens in the fan hub when viewing a list of teams and going between teams as is in the process of loading.
		if (logo_id == SQLITEMAB_INVALID_ID)
		{
			colour_out = MabColour::Black;
		}
		else
		{
			SIFApplication::GetApplication()->GetGameDatabaseManager()->LoadData(logo, logo_id);
			colour_out = logo.splotch_logo_colour;
		}
	}
}

float RUDB_TEAM::GetNormaliseRanking() const
{
	return static_cast<float>( ranking ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

// Returns the teams willingness to attack normalised to a scale of 0->1.0f
float RUDB_TEAM::GetNormalisedWillingnessToAttack() const
{
	return static_cast<float>( willingness_to_attack ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

// Returns the teams attack normalised to a scale of 0->1.0f
float RUDB_TEAM::GetNormalisedAttack() const
{
	return static_cast<float>( attack ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

// Returns the teams defense normalised to a scale of 0->1.0f
float RUDB_TEAM::GetNormalisedDefense() const
{
	return static_cast<float>( defence ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

// Returns the teams overall stat, normalised to a scale of 0->1.0f
float RUDB_TEAM::GetNormalisedOverall() const
{
	return (GetNormalisedAttack() + GetNormalisedDefense()) * 0.5f;
}

float RUDB_TEAM::GetNormalisedRuckAbility() const
{
	return static_cast<float>( ruck_ability ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

float RUDB_TEAM::GetNormalisedMaulAbility() const
{
	return static_cast<float>( maul_ability ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

float RUDB_TEAM::GetNormalisedScrumAbility() const
{
	return static_cast<float>( scrum_ability ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

float RUDB_TEAM::GetNormalisedLineoutAbility() const
{
	return static_cast<float>( lineout_ability ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

float RUDB_TEAM::GetNormalisedRunKickSlider() const
{
	return static_cast<float>( default_run_kick_slider ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

float RUDB_TEAM::GetNormalisedLineoutQuicknessSlider() const
{
	return static_cast<float>( default_lineout_quickness_slider ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

float RUDB_TEAM::GetNormalisedRuckCommitmentSlider() const
{
	return  static_cast<float>( default_ruck_commitment_slider ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

float RUDB_TEAM::GetNormalisedDefensiveLineSpeedSlider() const
{
	return static_cast<float>( default_defensive_line_speed_slider ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

float RUDB_TEAM::GetNormalisedForwardPassDriveSlider( TEAM_SLIDER_FIELD field_pos ) const
{
	switch ( field_pos )
	{
	case TEAM_SLIDER_DEF:
		return static_cast<float>( def_forward_pass_drive ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_MID:
		return static_cast<float>( mid_forward_pass_drive ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_ATT:
		return static_cast<float>( att_forward_pass_drive ) / MAX_TEAM_ATTRIBUTE_VALUE;
	}

	return 0;
}

float RUDB_TEAM::GetNormalisedForwardContactOffloadSlider( TEAM_SLIDER_FIELD field_pos ) const
{
	float offload_probability = 0.0f;
	switch ( field_pos )
	{
	case TEAM_SLIDER_DEF:
		offload_probability = static_cast<float>( def_forward_contact_offload ) / MAX_TEAM_ATTRIBUTE_VALUE;
		break;
	case TEAM_SLIDER_MID:
		offload_probability = static_cast<float>( mid_forward_contact_offload ) / MAX_TEAM_ATTRIBUTE_VALUE;
		break;
	case TEAM_SLIDER_ATT:
		offload_probability = static_cast<float>( att_forward_contact_offload ) / MAX_TEAM_ATTRIBUTE_VALUE;
		break;
	}

	MabMath::Clamp(offload_probability, 0.0f, 1.0f);
	return offload_probability;
}

float RUDB_TEAM::GetNormalisedBackPassKickSlider( TEAM_SLIDER_FIELD field_pos ) const
{
	switch ( field_pos )
	{
	case TEAM_SLIDER_DEF:
		return static_cast<float>( def_back_pass_kick ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_MID:
		return static_cast<float>( mid_back_pass_kick ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_ATT:
		return static_cast<float>( att_back_pass_kick ) / MAX_TEAM_ATTRIBUTE_VALUE;
	}

	return 0;
}

float RUDB_TEAM::GetNormalisedBackContactOffloadSlider( TEAM_SLIDER_FIELD field_pos ) const
{
	float offload_probability = 0.0f;
	switch ( field_pos )
	{
	case TEAM_SLIDER_DEF:
		offload_probability = static_cast<float>( def_back_contact_offload ) / MAX_TEAM_ATTRIBUTE_VALUE;
		break;
	case TEAM_SLIDER_MID:
		offload_probability = static_cast<float>( mid_back_contact_offload ) / MAX_TEAM_ATTRIBUTE_VALUE;
		break;
	case TEAM_SLIDER_ATT:
		offload_probability = static_cast<float>( att_back_contact_offload ) / MAX_TEAM_ATTRIBUTE_VALUE;
		break;
	}
	MabMath::Clamp(offload_probability, 0.0f, 1.0f);
	return offload_probability;
}

float RUDB_TEAM::GetNormalisedLineoutSizeSlider( TEAM_SLIDER_FIELD field_pos ) const
{
	switch ( field_pos )
	{
	case TEAM_SLIDER_DEF:
		return static_cast<float>( def_lineout_size ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_MID:
		return static_cast<float>( mid_lineout_size ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_ATT:
		return static_cast<float>( att_lineout_size ) / MAX_TEAM_ATTRIBUTE_VALUE;
	}

	return 0;
}

float RUDB_TEAM::GetNormalisedLineoutThrowSlider( TEAM_SLIDER_FIELD field_pos ) const
{
	switch ( field_pos )
	{
	case TEAM_SLIDER_DEF:
		return static_cast<float>( def_lineout_favoured_target ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_MID:
		return static_cast<float>( mid_lineout_favoured_target ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_ATT:
		return static_cast<float>( att_lineout_favoured_target ) / MAX_TEAM_ATTRIBUTE_VALUE;
	}

	return 0;
}

float RUDB_TEAM::GetNormalisedLineoutWinSlider( TEAM_SLIDER_FIELD field_pos ) const
{
	switch ( field_pos )
	{
	case TEAM_SLIDER_DEF:
		return static_cast<float>( def_lineout_win ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_MID:
		return static_cast<float>( mid_lineout_win ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_ATT:
		return static_cast<float>( att_lineout_win ) / MAX_TEAM_ATTRIBUTE_VALUE;
	}

	return 0;
}

float RUDB_TEAM::GetNormalisedRuckWinSlider( TEAM_SLIDER_FIELD field_pos ) const
{
	switch ( field_pos )
	{
	case TEAM_SLIDER_DEF:
		return static_cast<float>( def_ruck_win ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_MID:
		return static_cast<float>( mid_ruck_win ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_ATT:
		return static_cast<float>( att_ruck_win ) / MAX_TEAM_ATTRIBUTE_VALUE;
	}

	return 0;
}

float RUDB_TEAM::GetNormalisedScrumWinSlider( TEAM_SLIDER_FIELD field_pos ) const
{
	switch ( field_pos )
	{
	case TEAM_SLIDER_DEF:
		return static_cast<float>( def_scrum_win ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_MID:
		return static_cast<float>( mid_scrum_win ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_ATT:
		return static_cast<float>( att_scrum_win ) / MAX_TEAM_ATTRIBUTE_VALUE;
	}

	return 0;
}

float RUDB_TEAM::GetNormalisedMaulWinSlider( TEAM_SLIDER_FIELD field_pos ) const
{
	switch ( field_pos )
	{
	case TEAM_SLIDER_DEF:
		return static_cast<float>( def_maul_win ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_MID:
		return static_cast<float>( mid_maul_win ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_ATT:
		return static_cast<float>( att_maul_win ) / MAX_TEAM_ATTRIBUTE_VALUE;
	}

	return 0;
}

void RUDB_TEAM::GetNormalisedFavouredKickoffSliders( float& distance, float& direction ) const
{
	distance = static_cast<float>( kick_kickoff_short_long ) / MAX_TEAM_ATTRIBUTE_VALUE;
	direction = static_cast<float>( kick_kickoff_left_right ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

void RUDB_TEAM::GetNormalisedFavouredDropoutSliders( float& distance, float& direction ) const
{
	distance = static_cast<float>( kick_dropout_short_long ) / MAX_TEAM_ATTRIBUTE_VALUE;
	direction = static_cast<float>( kick_dropout_left_right ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

float RUDB_TEAM::GetNormalisedKickTouchTerritorySlider() const
{
	return static_cast<float>( kick_touch_territory ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

float RUDB_TEAM::GetNormalisedKickPenaltyTouchGoalSlider() const
{
	return static_cast<float>( kick_penalty_touch_goal ) / MAX_TEAM_ATTRIBUTE_VALUE;
}

float RUDB_TEAM::GetNormalisedLineDepthSlider( TEAM_SLIDER_FIELD field_pos ) const
{
	switch ( field_pos )
	{
	case TEAM_SLIDER_DEF:
		return static_cast<float>( def_line_depth ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_MID:
		return static_cast<float>( mid_line_depth ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_ATT:
		return static_cast<float>( att_line_depth ) / MAX_TEAM_ATTRIBUTE_VALUE;
	}

	return 0;
}

float RUDB_TEAM::GetNormalisedLineWidthSlider( TEAM_SLIDER_FIELD field_pos ) const
{
	switch ( field_pos )
	{
	case TEAM_SLIDER_DEF:
		return static_cast<float>( def_line_width ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_MID:
		return static_cast<float>( mid_line_width ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_ATT:
		return static_cast<float>( att_line_width ) / MAX_TEAM_ATTRIBUTE_VALUE;
	}

	return 0;
}

RUTeamStrategy::POD_OPTION RUDB_TEAM::GetPodOption( TEAM_SLIDER_FIELD field_pos ) const
{
	switch ( field_pos )
	{
	case TEAM_SLIDER_DEF:
		return static_cast<RUTeamStrategy::POD_OPTION>(def_pod_option);
	case TEAM_SLIDER_MID:
		return static_cast<RUTeamStrategy::POD_OPTION>(mid_pod_option);
	case TEAM_SLIDER_ATT:
		return static_cast<RUTeamStrategy::POD_OPTION>(att_pod_option);
	}

	std::abort(); //#rc3_legacy this seems like something we should not be relying on...

//	if(SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GameModeIsR7())
//		return RUTeamStrategy::PO_R7_DEFAULT;
//	else
//		return RUTeamStrategy::PO_R15_DEFAULT;
}

float RUDB_TEAM::GetNormalisedRuckCommitmentSlider( TEAM_SLIDER_FIELD field_pos ) const
{
	switch ( field_pos )
	{
	case TEAM_SLIDER_DEF:
		return static_cast<float>( def_ruck_commitment ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_MID:
		return static_cast<float>( mid_ruck_commitment ) / MAX_TEAM_ATTRIBUTE_VALUE;
	case TEAM_SLIDER_ATT:
		return static_cast<float>( att_ruck_commitment ) / MAX_TEAM_ATTRIBUTE_VALUE;
	}

	return 0;
}

void RUDB_TEAM::RetrieveAllFieldPlayerIds(MabVector<RUDB_LINEUP>& lineup_out_list) const
{
	int playersPerTeam = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();

	// We know that there are going to be 15 positions to find, so we'll resize the array in one chunk now
	lineup_out_list.resize(playersPerTeam/*NUM_PLAYERS_PER_TEAM*/);

	// We need to find the 15 players from the group of lineups that are going to be on the field.
	int num_of_lineups = GetNumLineups();
	for (int lineup_index = 0; lineup_index < num_of_lineups; ++lineup_index)
	{
		const RUDB_LINEUP& db_lineup = GetLineup(lineup_index);

		// The fullback position has the highest value of the field players.
		// Anyone with a position with a higher value is not on the field
		if (db_lineup.position <= PP_FULLBACK)
		{
			// We will also sort the list now because its not hard for us to figure out the positions
			// that each position should slot into.
			int position_index = PlayerPositionEnum::GetStartingJerseyNumberFromPlayerPosition(db_lineup.position) - 1;
			MABASSERT(position_index >= 0 && position_index < (int)lineup_out_list.size());
			lineup_out_list[position_index] = db_lineup;
		}
	}
}

void RUDB_TEAM::RetrieveAllPlayerIdsFromPosition(const PLAYER_POSITION position, MabVector<RUDB_LINEUP>& lineup_out_list) const
{
	// Every player of that position will need to be added to the out_list.
	const int num_of_lineups = GetNumLineups();
	for (int lineup_index = 0; lineup_index < num_of_lineups; ++lineup_index)
	{
		const RUDB_LINEUP& db_lineup = GetLineup(lineup_index);
		if (db_lineup.position == position)
		{
			lineup_out_list.push_back(db_lineup);
		}
	}
}

bool RUDB_TEAM::CompareTeamByName(RUDB_TEAM* t2) const
{
	MabString team1(this->name);
	MabString team2(t2->name);
	return MabStringHelper::ToLower(team1) < MabStringHelper::ToLower(team2);
}

int RUDB_TEAM::GetLogoGUID() const
{
	MABBREAKMSG("Stub not implemented");
	return 0;//static_cast<int>( SIFApplication::GetApplication()->GetDatabase()->GetTeamBitsDatabase()->logos[logo].guid );
}

void RUDB_TEAM::RecalculateRanking()
{
	//if (db_id == 0)
		//return;

	MabVector<unsigned int> player_rankings;

	RUGameDatabaseManager* manager = SIFApplication::GetApplication()->GetGameDatabaseManager();

	// Early out if there are enough players to satisfy the ranking calculation
	if(lineup.size() < StaticArraySize(RANKING_WEIGHTS))
		return;

	player_rankings.reserve(lineup.size());

	// Calculate team rating

	const float team_ranks[] =
	{
		attack			* 0.4f,
		defence			* 0.4f,
		ruck_ability	* 0.05f,
		maul_ability	* 0.05f,
		scrum_ability	* 0.05f,
		lineout_ability * 0.05f,
	};

	float team_rank = 0.0f;
	for(unsigned int i = 0; i < StaticArraySize(team_ranks); ++i)
		team_rank += team_ranks[i];

	// Calculate player rating

	MabVector<RUDB_LINEUP>::const_iterator it = lineup.begin();
	for(; it != lineup.end(); ++it)
	{
		RUDB_PLAYER player;
		manager->LoadData(player, it->player_id);

		const unsigned int forward_rankings[] =
		{
			player.fitness,
			player.speed,
			player.strength,
			player.mental_agility,
			player.acceleration,
			player.aggressiveness,
			player.agility,
			player.discipline,
			player.break_tackle_ability,
			player.tackle_ability,
			player.pass_accuracy,
			player.offload_ability,
			player.catch_ability,
		};

		const unsigned int back_rankings[] =
		{
			player.fitness,
			player.speed,
			player.strength,
			player.mental_agility,
			player.acceleration,
			player.agility,
			player.discipline,
			player.jump_ability,
			player.break_tackle_ability,
			player.tackle_ability,
			player.pass_accuracy,
			player.general_kick_accuracy,
			player.goal_kick_accuracy,
			player.catch_ability,
		};

		unsigned int forward_ranking = 0;
		unsigned int back_ranking = 0;

		// Average the rankings to get a player ranking

		for (unsigned int i = 0; i < StaticArraySize(forward_rankings); ++i) 
		{
			forward_ranking += forward_rankings[i];
		}
		forward_ranking /= (unsigned int)StaticArraySize(forward_rankings);

		for (unsigned int i = 0; i < StaticArraySize(back_rankings); ++i) 
		{
			back_ranking += back_rankings[i];
		}
		back_ranking /= (unsigned int)StaticArraySize(back_rankings);

		unsigned int max_ranking = MabMath::Max(back_ranking, forward_ranking);

		player_rankings.push_back(max_ranking);
	}

	unsigned int rank = 0;
	float player_rank = 0;

	float star_player_scale = 1.0f;

	// Sort the players factors from greatest to smallest
	std::sort(player_rankings.begin(), player_rankings.end(), std::greater<unsigned int>());

	// Average player rankings and star player scale
	for(unsigned int i = 0; i < StaticArraySize(RANKING_WEIGHTS); i++)
	{
		if (player_rankings[i] > STAR_PLAYER_THRESHOLD) 
		{
			star_player_scale += STAR_PLAYER_SCALE;
		}
		player_rank += player_rankings[i] * RANKING_WEIGHTS[i];
	}

	// Apply the bonus star player scale for having star players in your team
	player_rank *= star_player_scale;

	// Calculate weight final rating based on the team and player rating
	rank = (unsigned int)(TEAM_RANKING_WEIGHT * team_rank + PLAYER_RANKING_WEIGHT * player_rank);
	MabMath::Clamp(rank, 0u, 10000u);

	ranking = (unsigned short)rank;
}

struct WeightSumFunctor
{
	float operator()( float accum, const RUDB_HOME_STADIUM& new_item ) const
	{
		return accum + new_item.weight;
	}
};

// A way to get a random stadium from the list of this team's home stadiums.
unsigned short RUDB_TEAM::GetRandomHomeStadiumId() const
{
	if( home_stadiums.empty() )
	{
		MABBREAKMSG( "Can't get a random home stadium id when you don't have any home stadiums defined!" );
		return 1001u;
		//return SQLITEMAB_INVALID_ID;
	}

	const float weight_sum = std::accumulate( home_stadiums.begin(), home_stadiums.end(), 0.0f, WeightSumFunctor() );
	if( weight_sum == 0.0f )
	{
		return home_stadiums[ MabMath::RandInt( (int)home_stadiums.size() ) ].stadium_id;
	}
	else
	{
		const float amortised_rand = MabMath::Rand( 1.0f ) * weight_sum;
		float accum_weight = 0.0f;
		for( MabVector< RUDB_HOME_STADIUM >::const_iterator iter = home_stadiums.begin(); iter != home_stadiums.end(); ++iter )
		{
			if( accum_weight + iter->weight >= amortised_rand )
			{
				return iter->stadium_id;
			}
			accum_weight += iter->weight;
		}
		MABBREAKMSG( "Internal error: for loop did not behave as expected in selecting a random stadium!" );
		return home_stadiums[ 0 ].stadium_id;
	}
}

void RUDB_TEAM::SetHomeStadiumId(int home_stadium_id)
{
	if(home_stadiums.empty())
	{
		RUDB_HOME_STADIUM home_stadium;
		home_stadium.team_id = this->db_id;
		home_stadium.stadium_id = static_cast<unsigned short>(home_stadium_id);
		home_stadium.weight = 0.0f;

		home_stadiums.push_back(home_stadium);
	}
	else
	{
		home_stadiums[0].stadium_id = static_cast<unsigned short>(home_stadium_id);
	}
}

int RUDB_TEAM::GetHomeStadiumId() const
{
	if(home_stadiums.empty())
		return SQLITEMAB_INVALID_ID;

	return home_stadiums[0].stadium_id;
}

struct LineupPlayerIdFindPredicate
{
	MABNONASSIGNABLE( LineupPlayerIdFindPredicate )
public:
	LineupPlayerIdFindPredicate( unsigned short _id ) : id( _id ) {}

	bool operator()( const RUDB_LINEUP& lineup ) const
	{
		return( lineup.player_id == id );
	}

	const unsigned short id;
};

/// Retrieves the player ID at the requested field position.
int RUDB_TEAM::GetPlayerIdByPosition( const PLAYER_POSITION position ) const
{
	int found_player_id = -1;

	// To find the player at that position, we'll need to look through the team lineup.
	for (int lineup_index = 0; lineup_index < GetNumLineups(); ++lineup_index)
	{
		const RUDB_LINEUP& current_lineup = GetLineup(lineup_index);
		if (current_lineup.position == position)
		{
			// We've found the correct player.
			found_player_id = current_lineup.player_id;
			break;
		}
	}

	return found_player_id;
}

int RUDB_TEAM::GetDatabaseId() const
{
	return (int)GetDbId();
}

// A method to swap the positions of two players, by their respective db_ids.
//bool RUDB_TEAM::SwapPlayerPositions( int first_player_id, int second_player_id )
//{
//	MabVector< RUDB_LINEUP >::iterator found_first_player_iter = std::find_if( lineup.begin(), lineup.end(), LineupPlayerIdFindPredicate( static_cast<unsigned short>(first_player_id) ) );
//	MabVector< RUDB_LINEUP >::iterator found_second_player_iter = std::find_if( lineup.begin(), lineup.end(), LineupPlayerIdFindPredicate( static_cast<unsigned short>(second_player_id) ) );
//
//	if( found_first_player_iter == lineup.end() || found_second_player_iter == lineup.end() )
//	{
//		return false;
//	}
//
//	std::swap( found_first_player_iter->player_id, found_second_player_iter->player_id );
//
//	return true;
//}

/// A method to swap the positions of two players, by their respective db_ids, and indoces in the atlas
bool  RUDB_TEAM::SwapPlayerPositionsAndFaces( int first_player_id, int second_player_id , int player1_ui_index, int player2_ui_index)
{
	MabVector< RUDB_LINEUP >::iterator found_first_player_iter = std::find_if( lineup.begin(), lineup.end(), LineupPlayerIdFindPredicate( static_cast<unsigned short>(first_player_id) ) );
	MabVector< RUDB_LINEUP >::iterator found_second_player_iter = std::find_if( lineup.begin(), lineup.end(), LineupPlayerIdFindPredicate( static_cast<unsigned short>(second_player_id) ) );
	if( found_first_player_iter == lineup.end() && found_second_player_iter == lineup.end() )
	{
		return false;
	}
	else if(found_first_player_iter != lineup.end() && found_second_player_iter != lineup.end() )
	{
		std::swap( found_first_player_iter->player_id, found_second_player_iter->player_id );
	}
	else
	{
		MabVector< unsigned short > players;
		GetAllPlayersInDisplayOrder( players, true );

		if( found_first_player_iter == lineup.end())
		{
			found_first_player_iter = found_second_player_iter;
			player2_ui_index = player1_ui_index;
		}

		// first player is valid, moving to empty slot 'player2_ui_index'

		if(player2_ui_index >= (int)lineup.size())
			return false;

		// 1. Calculate wanted index in lineup (count number of non-zero entries to 'player1_ui_index'.

		int idx = player2_ui_index;
		int wanted_idx = 0;
		while(idx>=0)
		{
			MABASSERT(idx >= 0 && idx < (int)players.size());
			if(players[idx]!=0)
				wanted_idx++;
			idx--;
		}

		// 2. Bubble found_first_player_iter entry either up or down until index = desired index.

		int current_idx = (int)(found_first_player_iter - lineup.begin());

		while(current_idx<wanted_idx)
		{
			MABASSERT(current_idx >= 0 && (current_idx+1) < (int)lineup.size());
			RUDB_LINEUP temp = lineup[current_idx];
			lineup[current_idx] = lineup[current_idx+1];
			lineup[current_idx+1] = temp;
			current_idx++;
		}

		while(current_idx>wanted_idx)
		{
			MABASSERT(current_idx > 1 && current_idx < (int)lineup.size());
			RUDB_LINEUP temp = lineup[current_idx];
			lineup[current_idx] = lineup[current_idx-1];
			lineup[current_idx-1] = temp;
			current_idx--;
		}

		// 3. Change position to either 1<<player2_ui_index or 'SUB'..
		int playersPerTeam = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();
		int playersOnTeamIncBench = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeamIncBench();

		MABASSERT(current_idx >= 0 && current_idx < (int)lineup.size());
		if(player2_ui_index < playersPerTeam/*NUM_PLAYERS_PER_TEAM*/)
			lineup[current_idx].position = (PLAYER_POSITION)(1<<player2_ui_index);
		else if(player2_ui_index < playersOnTeamIncBench/*NUM_PLAYERS_PER_TEAM_INC_BENCH*/)
			lineup[current_idx].position = PP_SUB;
		else
			lineup[current_idx].position = PP_REFEREE;
	}

	// RC2_TODO... Tell team face generator to swap faces in lineup...

	return true;

	// AGGHHH!!! Shouldn't save here!!!
//	const RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();
//	database_manager->StoreData( *this );
//	SIFGameHelpers::GARequestFaceTeamSwapPlayersRender( GetDatabaseId(), true, (short)player1_ui_index, (short)player2_ui_index);
//	return true;
}


struct LineupPositionFindPredicate
{
	MABNONASSIGNABLE( LineupPositionFindPredicate )
public:
	LineupPositionFindPredicate( PLAYER_POSITION _position ) : position( _position ) {}

	bool operator()( RUDB_LINEUP lineup ) const
	{
		return( lineup.position == position );
	}

	const PLAYER_POSITION position;
};

struct PlayerPositionLessThanInLineupFunctor
{
	MABNONASSIGNABLE( PlayerPositionLessThanInLineupFunctor )
public:
	PlayerPositionLessThanInLineupFunctor( const MabVector< RUDB_LINEUP >& _lineup_list ) : lineup_list( _lineup_list )
	{}

	bool operator()( unsigned short left, unsigned short right )
	{
		const MabVector< RUDB_LINEUP >::const_iterator	left_found_iter = std::find_if( lineup_list.begin(), lineup_list.end(), LineupPlayerIdFindPredicate( left ) ),
														right_found_iter = std::find_if( lineup_list.begin(), lineup_list.end(), LineupPlayerIdFindPredicate( right ) );

		if( left_found_iter->position == right_found_iter->position )
		{
			return( left_found_iter->player_id < right_found_iter->player_id );
		}
		return( left_found_iter->position < right_found_iter->position );
	}

	const MabVector< RUDB_LINEUP >& lineup_list;
};

void RUDB_TEAM::GetStartingPlayersInPositionOrder( MabVector< unsigned short >& starting_players_out, bool pad ) const
{
#ifdef ENABLE_SEVENS_MODE
	//GAME_MODE gameMode = SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetGameMode();
#endif

	for( int i = (int)PP_FULLBACK; i < (int)PP_REFEREE; i <<= 1 )
	{
		// Nick  WWS 7s to Womens //
		/*
#ifdef ENABLE_SEVENS_MODE
		//if(gameMode == GAME_MODE_SEVENS)

		// WJS RLC ##### Do we need to alter start positions RUDB_TEAM::GetStartingPlayersInPositionOrder

		if(r7_exclusive)
		{
			// Special case for Sevens games
			// This was a fix for the lineup creation function(s), when we loop through the position enum via bit shifting, we will only see the first 7 positions,
			// this wont work for R7 games, as it will include invalid positions, e.g. Lock Four, Lock Five... So just skip them
			// - Dewald WW
			if(i == (int)PP_NUMBER_FOUR_LOCK_SECOND_ROW_TWELVE) continue; // 8
			if(i == (int)PP_NUMBER_FIVE_LOCK_SECOND_ROW_ELEVEN) continue; // 16
			// WJS RLC Not needed if(i == (int)PP_BLINDSIDE_FLANKER) continue; // 32
			// WJS RLC Not needed if(i == (int)PP_OPENSIDE_FLANKER) continue; // 64
			if(i == (int)PP_NUMBER_EIGHT_LOCK_FORWARD) continue; // 128
			if(i == (int)PP_LEFTWING) continue; // 1024
			if(i == (int)PP_OUTSIDE_CENTER_RIGHTCENTRE) continue; // 4096
			if(i == (int)PP_RIGHTWING) continue; // 8192
		}
#endif */

		const MabVector< RUDB_LINEUP >::const_iterator found_position_iter = std::find_if( lineup.begin(), lineup.end(), LineupPositionFindPredicate( (PLAYER_POSITION)i ) );

		if( found_position_iter == lineup.end() )
		{
			if(pad)
				starting_players_out.push_back(SQLITEMAB_INVALID_ID);
			continue;
		}

		//MABASSERTMSG( std::find_if( found_position_iter + 1, lineup.end(), LineupPositionFindPredicate( (PLAYER_POSITION)i ) ) == lineup.end(), "Found more than one starter with this position in the lineup!" );

		starting_players_out.push_back( found_position_iter->player_id );
	}
}

static void GetPlayersAtNonStartingPositionHelper( MabVector< unsigned short >& player_ids_out, const MabVector< RUDB_LINEUP >& lineup, PLAYER_POSITION target_posn, unsigned int min_players = 0)
{
	unsigned int count = 0;

	MabVector< RUDB_LINEUP >::const_iterator iter = std::find_if( lineup.begin(), lineup.end(), LineupPositionFindPredicate( target_posn ) );
	for( ; iter != lineup.end(); iter = std::find_if( iter + 1, lineup.end(), LineupPositionFindPredicate( target_posn ) ) )
	{
		player_ids_out.push_back( iter->player_id );
		count++;
	}

	while(count < min_players)
	{
		player_ids_out.push_back(SQLITEMAB_INVALID_ID);
		count++;
	}
}

void RUDB_TEAM::GetBenchPlayers( MabVector< unsigned short >& bench_players_out, bool pad ) const
{
	int playersOnBench = -1;
	if(r7_exclusive)
		playersOnBench = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfBenchPlayersR7();
	else
		playersOnBench = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfBenchPlayersR13();

	MABASSERT(playersOnBench > 0);
	//int playersOnBench = SIFApplication::GetApplication()->GetGameWorld()->GetGameSettings().game_limits.GetNumberOfBenchPlayers();
	GetPlayersAtNonStartingPositionHelper( bench_players_out, lineup, PP_SUB, pad ? playersOnBench : 0);
}

void RUDB_TEAM::GetNonPlayingPlayers( MabVector< unsigned short >& non_playing_players_out, bool pad ) const
{
	int nonPlayers = -1;
	if(r7_exclusive)
		nonPlayers = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfNonPlayingPlayersR7();
	else
		nonPlayers = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfNonPlayingPlayersR13();

	MABASSERT(nonPlayers > 0);

	//int nonPlayers = SIFApplication::GetApplication()->GetGameWorld()->GetGameSettings().game_limits.GetNumberOfNonPlayingPlayers();
	GetPlayersAtNonStartingPositionHelper( non_playing_players_out, lineup, PP_REFEREE, pad ? nonPlayers/*NUM_NON_PLAYING_PLAYERS*/ : 0 );
}


/// This method will populate the passed-in list with ids of all the players, ordered by their PLAYER_POSITION, as they might be needed to be displayed in UI
void  RUDB_TEAM::GetAllPlayersInDisplayOrder( MabVector< unsigned short >& players_out, bool pad ) const
{
	GetStartingPlayersInPositionOrder(players_out, pad);
	GetBenchPlayers(players_out, pad);
	GetNonPlayingPlayers(players_out, pad);
}

struct LineupPositionLessThanFunctor
{
	bool operator()( const RUDB_LINEUP& left, const RUDB_LINEUP& right )
	{
		if( left.position == right.position )
		{
			return( left.player_id < right.player_id );
		}

		// We need to order the players so that the first 15 are the field players,
		// the next 7 are the sub players, and everyone else is afterwards.
		// The value of sub is larger than the value for referee, hence the special case check.
		if(left.position == PP_SUB && right.position == PP_REFEREE)
			return true;

		if(left.position == PP_REFEREE && right.position == PP_SUB)
			return false;

		return( left.position < right.position );
	}

};

void RUDB_TEAM::SortLineup()
{
	std::sort( lineup.begin(), lineup.end(), LineupPositionLessThanFunctor() );
}


int RUDB_TEAM::GetPlayer(int index)
{
	int playersPerTeam = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();
	int playersOnBench = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfBenchPlayers();

	if(index < playersPerTeam/*NUM_PLAYERS_PER_TEAM*/)
	{
		PLAYER_POSITION position = static_cast<PLAYER_POSITION>(1 << index);

		for(MabVector<RUDB_LINEUP>::iterator iter = lineup.begin(); iter != lineup.end(); iter++)
		{
			// There's already a player in this position, so change the players id
			if(iter->position == position)
			{
				return (int)iter->player_id;
			}
		}
	}
	else
	{
		index -= playersPerTeam/*NUM_PLAYERS_PER_TEAM*/;
		if(index < playersOnBench/*NUM_BENCH_PLAYERS*/)
		{
			MabVector< RUDB_LINEUP >::iterator iter = std::find_if( lineup.begin(), lineup.end(), LineupPositionFindPredicate( PP_SUB ) );
			for( ; iter != lineup.end(); iter = std::find_if( iter + 1, lineup.end(), LineupPositionFindPredicate( PP_SUB ) ) )
			{
				if(index == 0)
				{
					return (int)iter->player_id;
				}

				index--;
			}
		}
		else
		{
			index -= playersOnBench/*NUM_BENCH_PLAYERS*/;

			MabVector< RUDB_LINEUP >::iterator iter = std::find_if( lineup.begin(), lineup.end(), LineupPositionFindPredicate( PP_REFEREE ) );
			for( ; iter != lineup.end(); iter = std::find_if( iter + 1, lineup.end(), LineupPositionFindPredicate( PP_REFEREE ) ) )
			{
				if(index == 0)
				{
					return (int)iter->player_id;
				}

				index--;
			}
		}
	}

	return (int)SQLITEMAB_INVALID_ID;
}


void RUDB_TEAM::SetPlayer(int index, int dbId)
{
	int playersPerTeam = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();
	int playersOnBench = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfBenchPlayers();

	if(index < playersPerTeam/*NUM_PLAYERS_PER_TEAM*/)
	{
		PLAYER_POSITION position = PlayerPositionEnum::GetPlayerPositionFromLineupIndex( index ); //static_cast<PLAYER_POSITION>(1 << index);

		for(MabVector<RUDB_LINEUP>::iterator iter = lineup.begin(); iter != lineup.end(); iter++)
		{
			// There's already a player in this position, so change the players id
			if(iter->position == position)
			{
				iter->player_id = static_cast<unsigned short>(dbId);
				return;
			}
		}

		// There's no player in this position, so add one!
		RUDB_LINEUP player_lineup(this->GetDbId(), position, static_cast<unsigned short>(dbId));
		lineup.push_back(player_lineup);
	}
	else
	{
		index -= playersPerTeam/*NUM_PLAYERS_PER_TEAM*/;
		if(index < playersOnBench/*NUM_BENCH_PLAYERS*/)
		{
			MabVector< RUDB_LINEUP >::iterator iter = std::find_if( lineup.begin(), lineup.end(), LineupPositionFindPredicate( PP_SUB ) );
			for( ; iter != lineup.end(); iter = std::find_if( iter + 1, lineup.end(), LineupPositionFindPredicate( PP_SUB ) ) )
			{
				if(index == 0)
				{
					iter->player_id = static_cast<unsigned short>(dbId);
					index--;
					break;
				}

				index--;
			}

			if(index >= 0)
			{
				RUDB_LINEUP player_lineup(this->GetDbId(), PP_SUB, static_cast<unsigned short>(dbId));
				lineup.push_back(player_lineup);
			}
		}
		else
		{
			index -= playersOnBench/*NUM_BENCH_PLAYERS*/;

			MabVector< RUDB_LINEUP >::iterator iter = std::find_if( lineup.begin(), lineup.end(), LineupPositionFindPredicate( PP_REFEREE ) );
			for( ; iter != lineup.end(); iter = std::find_if( iter + 1, lineup.end(), LineupPositionFindPredicate( PP_REFEREE ) ) )
			{
				if(index == 0)
				{
					iter->player_id = static_cast<unsigned short>(dbId);
					index--;
					break;
				}

				index--;
			}

			if(index >= 0)
			{
				RUDB_LINEUP player_lineup(this->GetDbId(), PP_REFEREE, static_cast<unsigned short>(dbId));
				lineup.push_back(player_lineup);
			}
		}
	}
}


// Calculate the contract years for given player in this team
int RUDB_TEAM::PlayersContractYears( int player_db_id )
{
	const RUDB_LINEUP* db_lineup = GetLineupDBForPlayer( (unsigned short)player_db_id );
	MABASSERT(db_lineup);
	return db_lineup->num_seasons;
}

struct LineupGenderMismatchPredicate
{
	MABNONASSIGNABLE(LineupGenderMismatchPredicate)
public:
	LineupGenderMismatchPredicate(unsigned char gender) : m_gender(gender) {}

	bool operator()(RUDB_LINEUP lineupPosition) const
	{
		bool genderMatches = false;

		if (lineupPosition.player_id != DB_INVALID_ID)
		{
			if (lineupPosition.player_id == DB_SERVER_PLAYER_ID)
			{
				URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

				if (pRugbyGameInstance)
				{
					UWWRugbyFanHubService* pFanHubService = Cast<UWWRugbyFanHubService>(pRugbyGameInstance->GetFanHubService());

					if (pFanHubService)
					{
						TSharedPtr<RUDB_PLAYER> db_player = pFanHubService->TryGetDownloadedPlayer(lineupPosition.server_id);

						if (db_player)
						{
							unsigned char playerGenderFlag = PLAYER_GENDER_AS_FLAG(db_player->gender);
							genderMatches = (playerGenderFlag & m_gender) == playerGenderFlag;
							genderMatches = true;
						}
					}
				}
			}
			else
			{
				RL3DB_PLAYER player(lineupPosition.player_id);
				unsigned char playerGenderFlag = PLAYER_GENDER_AS_FLAG(player.GetGender());
				genderMatches = (playerGenderFlag & m_gender) == playerGenderFlag;
			}
		}

		return !genderMatches;
	}

	const unsigned char m_gender;
};

void RUDB_TEAM::RemoveInvalidSquadMembers()
{
	// Remove squad members that fail a gender check
	MabVector<RUDB_LINEUP>::iterator removal_iter = std::remove_if(lineup.begin(), lineup.end(), LineupGenderMismatchPredicate(GetGenderPermissionFlags()));
	lineup.erase(removal_iter, lineup.end());
}

void RUDB_TEAM::ChangePlayerPosition(int dbId, int newIndex)
{
	int playersPerTeam = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();
	int playersOnBench = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfBenchPlayers();

	for(MabVector<RUDB_LINEUP>::iterator iter = lineup.begin(); iter != lineup.end(); iter++)
	{
		if(iter->player_id == dbId)
		{
			if(newIndex < playersPerTeam/*NUM_PLAYERS_PER_TEAM*/)
			{
				iter->position = static_cast<PLAYER_POSITION>(1 << newIndex);
			}
			else
			{
				RUDB_LINEUP player = *iter;
				lineup.erase(iter);

				newIndex -= playersPerTeam/*NUM_PLAYERS_PER_TEAM*/;
				if(newIndex < playersOnBench/*NUM_BENCH_PLAYERS*/)
				{
					player.position = PP_SUB;

					int found_index = 0;
					MabVector<RUDB_LINEUP>::iterator inside_iter;
					for(inside_iter = lineup.begin(); inside_iter != lineup.end(); inside_iter++)
					{
						if(found_index == newIndex)
							break;

						if(inside_iter->position == PP_SUB)
							found_index++;
					}

					lineup.insert(inside_iter, player);
				}
				else
				{
					player.position = PP_REFEREE;

					int found_index = 0;
					MabVector<RUDB_LINEUP>::iterator inside_iter;
					for(inside_iter = lineup.begin(); inside_iter != lineup.end(); inside_iter++)
					{
						if(found_index == newIndex)
							break;

						if(inside_iter->position == PP_REFEREE)
							found_index++;
					}

					lineup.insert(inside_iter, player);
				}

			}

			return;
		}
	}

	MABBREAKMSG("Couldn't find player to change position.");
}

void RUDB_TEAM::RemovePlayer(int index)
{
	int playersPerTeam = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();
	int playersOnBench = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfBenchPlayers();

	if(index < playersPerTeam/*NUM_PLAYERS_PER_TEAM*/)
	{
		RUDB_LINEUP remove_me;
		PLAYER_POSITION position = static_cast<PLAYER_POSITION>(1 << index);

		if (this->r7_exclusive)
			position = PlayerPositionEnum::GetPlayerPositionFromLineupIndexSevensOnly(index);

		for(MabVector<RUDB_LINEUP>::iterator iter = lineup.begin(); iter != lineup.end(); iter++)
		{
			if(iter->position == position)
			{
				lineup.erase(iter);
				return;
			}
		}

		MABBREAKMSG("Couldnt find player in the right position to remove.");
	}
	else
	{
		index -= playersPerTeam/*NUM_PLAYERS_PER_TEAM*/;
		if(index < playersOnBench/*NUM_BENCH_PLAYERS*/)
		{
			MabVector< RUDB_LINEUP >::iterator iter = std::find_if( lineup.begin(), lineup.end(), LineupPositionFindPredicate( PP_SUB ) );
			for( ; iter != lineup.end(); iter = std::find_if( iter + 1, lineup.end(), LineupPositionFindPredicate( PP_SUB ) ) )
			{
				if(index == 0)
				{
					lineup.erase(iter);
					return;
				}

				index--;
			}

			MABBREAKMSG("Couldnt find player in the right position to remove.");
		}
		else
		{
			index -= playersOnBench/*NUM_BENCH_PLAYERS*/;

			MabVector< RUDB_LINEUP >::iterator iter = std::find_if( lineup.begin(), lineup.end(), LineupPositionFindPredicate( PP_REFEREE ) );
			for( ; iter != lineup.end(); iter = std::find_if( iter + 1, lineup.end(), LineupPositionFindPredicate( PP_REFEREE ) ) )
			{
				if(index == 0)
				{
					lineup.erase(iter);
					return;
				}

				index--;
			}

			MABBREAKMSG("Couldnt find player in the right position to remove.");
		}
	}
}

bool RUDB_TEAM::HasEnoughPlayers()
{
	MabVector<unsigned short> starting_players;
	MabVector<unsigned short> bench_players;

	this->GetStartingPlayersInPositionOrder(starting_players, false);
	this->GetBenchPlayers(bench_players, false);

	// Nick WWS &s to Womens 13s //
	int playersPerTeam = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeamR13();
											//GetIsR7Exclusive() ? SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeamR7()
											//: SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeamR13();
	int playersOnBench = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfBenchPlayersR13();
											//GetIsR7Exclusive() ? SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfBenchPlayersR7()
											//: SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfBenchPlayersR13();

	return starting_players.size() >= static_cast<size_t>(playersPerTeam/*NUM_PLAYERS_PER_TEAM*/)
		&& bench_players.size() >= static_cast<size_t>(playersOnBench/*NUM_BENCH_PLAYERS*/);
}

int RUDB_TEAM::GetMissingTitledPlayerBitField()
{
	MabVector< unsigned short > starting_player_ids;

	GetStartingPlayersInPositionOrder( starting_player_ids, false );

	bool	captain_missing = true,
			play_kicker_missing = true,
			goal_kicker_missing = true;

	for( MabVector< unsigned short >::const_iterator iter = starting_player_ids.begin(); iter != starting_player_ids.end(); ++iter )
	{
		if( captain_missing && *iter == captain_id )
		{
			captain_missing = false;
		}

		if( play_kicker_missing && *iter == play_kicker_id )
		{
			play_kicker_missing = false;
		}

		if( goal_kicker_missing && *iter == goal_kicker_id )
		{
			goal_kicker_missing = false;
		}
	}

	int bit_field_out = 0;
	if( captain_missing )		bit_field_out += ( 1 << 0 );
	if( play_kicker_missing	)	bit_field_out += ( 1 << 1 );
	if( goal_kicker_missing )	bit_field_out += ( 1 << 2 );

	return bit_field_out;
}



///-------------------------------------------------------------
/// Is this team an international team?
///-------------------------------------------------------------

unsigned short RUDB_TEAM::team_cache_id = 0;
bool RUDB_TEAM::is_international_cache = false;

bool RUDB_TEAM::IsInternational() const
{
	/// This method is likely to be called multiple times sequentially for the same team, so use a cache of size 1 to save
	/// on calling GetTeamType() - expensive.

	unsigned short dbId = GetDbId();

	if(team_cache_id != dbId)
	{
		team_cache_id = dbId;

		bool is_super15 = false;
		GetTeamType(dbId,&is_international_cache, &is_super15);
	}

	return is_international_cache;
}

void RUDB_TEAM::CopyTeamColours(int team_id)
{
	RUDB_TEAM team;
	SIFApplication::GetApplication()->GetGameDatabaseManager()->LoadData(team, team_id);

	this->logo_id = team.logo_id;
}

///-------------------------------------------------------------
/// Calculate if a team is international or super15 (looks at competitions)
///-------------------------------------------------------------

void RUDB_TEAM::GetTeamType(unsigned short db_id, bool *is_international, bool *is_super15)
{
	*is_international = false;
	*is_super15 = false;

	const RUGameDatabaseManager* const game_database_mgr = SIFApplication::GetApplication()->GetGameDatabaseManager();
	if( game_database_mgr == NULL )
	{
		MABBREAK();
		return;
	}

	SqliteMabStatement result;
	MabString statement = MabString(0,"SELECT competition_id FROM RUDB_COMP_DEF_TEAM WHERE team_id=%d",db_id);
	if(game_database_mgr->RawProcessSqlStatement(result, statement.c_str()))
	{
		while (result.Next())
		{
			short comp_id = result.GetColumnAsUShort(0);
			switch(comp_id)
			{
			case DB_COMPID_WORLDCUP:
			case DB_COMPID_QUADNATIONS:
			case DB_COMPID_EURONATIONS:
			case DB_COMPID_EURONATIONS_CUP:
			case DB_COMPID_AFRICAN_CUP:
			case DB_COMPID_SOUTH_AMERICAN_CUP:
				*is_international = true;
				break;
			case DB_COMPID_SUPER15:
				*is_super15 = true;;
				break;
			default:
				break;
			}
		}
	}
}

const char* RUDB_TEAM::GetShortName() const
{
	if( strlen( short_name ) > 0u || in_serialisation) return short_name;			// HAK: When saving always return "short_name",
	return name;																	// Alternative is to change all accessors in the game and lua. (Poor design in the first place).
}

const char* RUDB_TEAM_LITE::GetShortName() const
{
	if( strlen( short_name ) > 0u ) return short_name;
	return name;
}

// Get lineup db for a given player db id in this team
const RUDB_LINEUP* RUDB_TEAM::GetLineupDBForPlayer( unsigned short player_db_id ) const
{
	if( player_db_id == SQLITEMAB_INVALID_ID )
		return NULL;

	for(MabVector<RUDB_LINEUP>::const_iterator iter = lineup.begin(); iter != lineup.end(); iter++)
	{
		if (iter->player_id == player_db_id)
		{
			return &(*iter);
		}
	}

	return NULL;
}


///---------------------------------------------------------------------------------
/// Callbacks from serialisation..

void RUDB_TEAM::OnObjectDeserialised()
{
	if(!in_serialisation)
	{
		/// Load lineup...

		unsigned short team_db_id = GetDbId();
		RL3DB_TEAM team(team_db_id);
		int num_players = team.GetNumPlayers();

		lineup.clear();

		for(int plr_idx=0;plr_idx<num_players;plr_idx++)
		{
			RL3DB_CONTRACT contract = team.GetPlayer(plr_idx);

			RUDB_LINEUP lineup_plr((int)team_db_id, (PLAYER_POSITION)contract.position, contract.index);
			lineup_plr.value = contract.value;
			lineup_plr.num_seasons = contract.num_seasons;

			MABASSERTMSG(contract.position != 0, MabString(0, "Player %i has no position in the %s lineup database table", plr_idx, team.GetName()).c_str());

			lineup.push_back(lineup_plr);
		}
	}
	else
	{
		unsigned short team_db_id = GetDbId();
		RL3DB_TEAM team(team_db_id);
		team.DeleteAllPlayers();

		for(MabVector<RUDB_LINEUP>::const_iterator iter = lineup.begin(); iter != lineup.end(); iter++)
		{
			RL3DB_CONTRACT contract;

			contract.index = iter->player_id;
			contract.num_seasons = (unsigned char) iter->num_seasons;
			contract.value = iter->value;
			contract.position = (unsigned int)iter->position;

			team.AddPlayer(contract);
		}

		in_serialisation = false;
	}
}

unsigned short RUDB_TEAM::GetLineupPositionFromID(unsigned short db_id)
{
	if (db_id > DB_LAST_PLAYER_ID)
	{
		for (int i = 0; i < (int)lineup.size() - 1; i++)
		{
			if (lineup.at(i).player_id == db_id)
			{
				return (unsigned short)(i + 1);
			}
		}
	}

	return db_id;
}

void RUDB_TEAM::SerializeTacticsToJson(TSharedPtr< FJsonObject >& objTeam)
{
	objTeam->SetNumberField("default_run_kick_slider", default_run_kick_slider);
	objTeam->SetNumberField("default_lineout_quickness_slider", default_lineout_quickness_slider);
	objTeam->SetNumberField("default_ruck_commitment_slider", default_ruck_commitment_slider);
	objTeam->SetNumberField("default_defensive_line_speed_slider", default_defensive_line_speed_slider);
	objTeam->SetNumberField("def_forward_pass_drive", def_forward_pass_drive);
	objTeam->SetNumberField("def_forward_contact_offload", def_forward_contact_offload);
	objTeam->SetNumberField("def_back_pass_kick", def_back_pass_kick);
	objTeam->SetNumberField("def_back_contact_offload", def_back_contact_offload);
	objTeam->SetNumberField("def_lineout_size", def_lineout_size);
	objTeam->SetNumberField("def_lineout_favoured_target", def_lineout_favoured_target);
	objTeam->SetNumberField("def_ruck_win", def_ruck_win);
	objTeam->SetNumberField("def_lineout_win", def_lineout_win);
	objTeam->SetNumberField("def_scrum_win", def_scrum_win);
	objTeam->SetNumberField("def_maul_win", def_maul_win);
	objTeam->SetNumberField("def_line_width", def_line_width);
	objTeam->SetNumberField("def_line_depth", def_line_depth);
	objTeam->SetNumberField("def_ruck_commitment", def_ruck_commitment);
	objTeam->SetNumberField("def_pod_option", def_pod_option);
	objTeam->SetNumberField("mid_forward_pass_drive", mid_forward_pass_drive);
	objTeam->SetNumberField("mid_forward_contact_offload", mid_forward_contact_offload);
	objTeam->SetNumberField("mid_back_pass_kick", mid_back_pass_kick);
	objTeam->SetNumberField("mid_back_contact_offload", mid_back_contact_offload);
	objTeam->SetNumberField("mid_lineout_size", mid_lineout_size);
	objTeam->SetNumberField("mid_lineout_favoured_target", mid_lineout_favoured_target);
	objTeam->SetNumberField("mid_ruck_win", mid_ruck_win);
	objTeam->SetNumberField("mid_lineout_win", mid_lineout_win);
	objTeam->SetNumberField("mid_scrum_win", mid_scrum_win);
	objTeam->SetNumberField("mid_maul_win", mid_maul_win);
	objTeam->SetNumberField("mid_line_width", mid_line_width);
	objTeam->SetNumberField("mid_line_depth", mid_line_depth);
	objTeam->SetNumberField("mid_ruck_commitment", mid_ruck_commitment);
	objTeam->SetNumberField("mid_pod_option", mid_pod_option);
	objTeam->SetNumberField("att_forward_pass_drive", att_forward_pass_drive);
	objTeam->SetNumberField("att_forward_contact_offload", att_forward_contact_offload);
	objTeam->SetNumberField("att_back_pass_kick", att_back_pass_kick);
	objTeam->SetNumberField("att_back_contact_offload", att_back_contact_offload);
	objTeam->SetNumberField("att_lineout_size", att_lineout_size);
	objTeam->SetNumberField("att_lineout_favoured_target", att_lineout_favoured_target);
	objTeam->SetNumberField("att_ruck_win", att_ruck_win);
	objTeam->SetNumberField("att_lineout_win", att_lineout_win);
	objTeam->SetNumberField("att_scrum_win", att_scrum_win);
	objTeam->SetNumberField("att_maul_win", att_maul_win);
	objTeam->SetNumberField("att_line_width", att_line_width);
	objTeam->SetNumberField("att_line_depth", att_line_depth);
	objTeam->SetNumberField("att_ruck_commitment", att_ruck_commitment);
	objTeam->SetNumberField("att_pod_option", att_pod_option);
	objTeam->SetNumberField("kick_kickoff_short_long", kick_kickoff_short_long);
	objTeam->SetNumberField("kick_kickoff_left_right", kick_kickoff_left_right);
	objTeam->SetNumberField("kick_dropout_short_long", kick_dropout_short_long);
	objTeam->SetNumberField("kick_dropout_left_right", kick_dropout_left_right);
	objTeam->SetNumberField("kick_touch_territory", kick_touch_territory);
	objTeam->SetNumberField("kick_penalty_touch_goal", kick_penalty_touch_goal);
}

void RUDB_TEAM::DeserializeTacticsFromJson(TSharedPtr< FJsonObject > InJson)
{
	default_run_kick_slider = static_cast<unsigned short>(InJson->GetNumberField("default_run_kick_slider"));
	default_lineout_quickness_slider = static_cast<unsigned short>(InJson->GetNumberField("default_lineout_quickness_slider"));
	default_ruck_commitment_slider = static_cast<unsigned short>(InJson->GetNumberField("default_ruck_commitment_slider"));
	default_defensive_line_speed_slider = static_cast<unsigned short>(InJson->GetNumberField("default_defensive_line_speed_slider"));
	def_forward_pass_drive = static_cast<unsigned short>(InJson->GetNumberField("def_forward_pass_drive"));
	def_forward_contact_offload = static_cast<unsigned short>(InJson->GetNumberField("def_forward_contact_offload"));
	def_back_pass_kick = static_cast<unsigned short>(InJson->GetNumberField("def_back_pass_kick"));
	def_back_contact_offload = static_cast<unsigned short>(InJson->GetNumberField("def_back_contact_offload"));
	def_lineout_size = static_cast<unsigned short>(InJson->GetNumberField("def_lineout_size"));
	def_lineout_favoured_target = static_cast<unsigned short>(InJson->GetNumberField("def_lineout_favoured_target"));
	def_ruck_win = static_cast<unsigned short>(InJson->GetNumberField("def_ruck_win"));
	def_lineout_win = static_cast<unsigned short>(InJson->GetNumberField("def_lineout_win"));
	def_scrum_win = static_cast<unsigned short>(InJson->GetNumberField("def_scrum_win"));
	def_maul_win = static_cast<unsigned short>(InJson->GetNumberField("def_maul_win"));
	def_line_width = static_cast<unsigned short>(InJson->GetNumberField("def_line_width"));
	def_line_depth = static_cast<unsigned short>(InJson->GetNumberField("def_line_depth"));
	def_ruck_commitment = static_cast<unsigned short>(InJson->GetNumberField("def_ruck_commitment"));
	def_pod_option = static_cast<unsigned char>(InJson->GetNumberField("def_pod_option"));
	mid_forward_pass_drive = static_cast<unsigned short>(InJson->GetNumberField("mid_forward_pass_drive"));
	mid_forward_contact_offload = static_cast<unsigned short>(InJson->GetNumberField("mid_forward_contact_offload"));
	mid_back_pass_kick = static_cast<unsigned short>(InJson->GetNumberField("mid_back_pass_kick"));
	mid_back_contact_offload = static_cast<unsigned short>(InJson->GetNumberField("mid_back_contact_offload"));
	mid_lineout_size = static_cast<unsigned short>(InJson->GetNumberField("mid_lineout_size"));
	mid_lineout_favoured_target = static_cast<unsigned short>(InJson->GetNumberField("mid_lineout_favoured_target"));
	mid_ruck_win = static_cast<unsigned short>(InJson->GetNumberField("mid_ruck_win"));
	mid_lineout_win = static_cast<unsigned short>(InJson->GetNumberField("mid_lineout_win"));
	mid_scrum_win = static_cast<unsigned short>(InJson->GetNumberField("mid_scrum_win"));
	mid_maul_win = static_cast<unsigned short>(InJson->GetNumberField("mid_maul_win"));
	mid_line_width = static_cast<unsigned short>(InJson->GetNumberField("mid_line_width"));
	mid_line_depth = static_cast<unsigned short>(InJson->GetNumberField("mid_line_depth"));
	mid_ruck_commitment = static_cast<unsigned short>(InJson->GetNumberField("mid_ruck_commitment"));
	mid_pod_option = static_cast<unsigned char>(InJson->GetNumberField("mid_pod_option"));
	att_forward_pass_drive = static_cast<unsigned short>(InJson->GetNumberField("att_forward_pass_drive"));
	att_forward_contact_offload = static_cast<unsigned short>(InJson->GetNumberField("att_forward_contact_offload"));
	att_back_pass_kick = static_cast<unsigned short>(InJson->GetNumberField("att_back_pass_kick"));
	att_back_contact_offload = static_cast<unsigned short>(InJson->GetNumberField("att_back_contact_offload"));
	att_lineout_size = static_cast<unsigned short>(InJson->GetNumberField("att_lineout_size"));
	att_lineout_favoured_target = static_cast<unsigned short>(InJson->GetNumberField("att_lineout_favoured_target"));
	att_ruck_win = static_cast<unsigned short>(InJson->GetNumberField("att_ruck_win"));
	att_lineout_win = static_cast<unsigned short>(InJson->GetNumberField("att_lineout_win"));
	att_scrum_win = static_cast<unsigned short>(InJson->GetNumberField("att_scrum_win"));
	att_maul_win = static_cast<unsigned short>(InJson->GetNumberField("att_maul_win"));
	att_line_width = static_cast<unsigned short>(InJson->GetNumberField("att_line_width"));
	att_line_depth = static_cast<unsigned short>(InJson->GetNumberField("att_line_depth"));
	att_ruck_commitment = static_cast<unsigned short>(InJson->GetNumberField("att_ruck_commitment"));
	att_pod_option = static_cast<unsigned char>(InJson->GetNumberField("att_pod_option"));
	kick_kickoff_short_long = static_cast<unsigned short>(InJson->GetNumberField("kick_kickoff_short_long"));
	kick_kickoff_left_right = static_cast<unsigned short>(InJson->GetNumberField("kick_kickoff_left_right"));
	kick_dropout_short_long = static_cast<unsigned short>(InJson->GetNumberField("kick_dropout_short_long"));
	kick_dropout_left_right = static_cast<unsigned short>(InJson->GetNumberField("kick_dropout_left_right"));
	kick_touch_territory = static_cast<unsigned short>(InJson->GetNumberField("kick_touch_territory"));
	kick_penalty_touch_goal = static_cast<unsigned short>(InJson->GetNumberField("kick_penalty_touch_goal"));
}

void RUDB_TEAM::SerializeTacticsToJsonIndividual(TSharedPtr< FJsonObject >& objTeam)
{
	int teamID = GetDatabaseId();
	objTeam->SetNumberField(FString::FromInt(teamID) + " default_run_kick_slider", default_run_kick_slider);
	objTeam->SetNumberField(FString::FromInt(teamID) + " default_lineout_quickness_slider", default_lineout_quickness_slider);
	objTeam->SetNumberField(FString::FromInt(teamID) + " default_ruck_commitment_slider", default_ruck_commitment_slider);
	objTeam->SetNumberField(FString::FromInt(teamID) + " default_defensive_line_speed_slider", default_defensive_line_speed_slider);
	objTeam->SetNumberField(FString::FromInt(teamID) + " def_forward_pass_drive", def_forward_pass_drive);
	objTeam->SetNumberField(FString::FromInt(teamID) + " def_forward_contact_offload", def_forward_contact_offload);
	objTeam->SetNumberField(FString::FromInt(teamID) + " def_back_pass_kick", def_back_pass_kick);
	objTeam->SetNumberField(FString::FromInt(teamID) + " def_back_contact_offload", def_back_contact_offload);
	objTeam->SetNumberField(FString::FromInt(teamID) + " def_lineout_size", def_lineout_size);
	objTeam->SetNumberField(FString::FromInt(teamID) + " def_lineout_favoured_target", def_lineout_favoured_target);
	objTeam->SetNumberField(FString::FromInt(teamID) + " def_ruck_win", def_ruck_win);
	objTeam->SetNumberField(FString::FromInt(teamID) + " def_lineout_win", def_lineout_win);
	objTeam->SetNumberField(FString::FromInt(teamID) + " def_scrum_win", def_scrum_win);
	objTeam->SetNumberField(FString::FromInt(teamID) + " def_maul_win", def_maul_win);
	objTeam->SetNumberField(FString::FromInt(teamID) + " def_line_width", def_line_width);
	objTeam->SetNumberField(FString::FromInt(teamID) + " def_line_depth", def_line_depth);
	objTeam->SetNumberField(FString::FromInt(teamID) + " def_ruck_commitment", def_ruck_commitment);
	objTeam->SetNumberField(FString::FromInt(teamID) + " def_pod_option", def_pod_option);
	objTeam->SetNumberField(FString::FromInt(teamID) + " mid_forward_pass_drive", mid_forward_pass_drive);
	objTeam->SetNumberField(FString::FromInt(teamID) + " mid_forward_contact_offload", mid_forward_contact_offload);
	objTeam->SetNumberField(FString::FromInt(teamID) + " mid_back_pass_kick", mid_back_pass_kick);
	objTeam->SetNumberField(FString::FromInt(teamID) + " mid_back_contact_offload", mid_back_contact_offload);
	objTeam->SetNumberField(FString::FromInt(teamID) + " mid_lineout_size", mid_lineout_size);
	objTeam->SetNumberField(FString::FromInt(teamID) + " mid_lineout_favoured_target", mid_lineout_favoured_target);
	objTeam->SetNumberField(FString::FromInt(teamID) + " mid_ruck_win", mid_ruck_win);
	objTeam->SetNumberField(FString::FromInt(teamID) + " mid_lineout_win", mid_lineout_win);
	objTeam->SetNumberField(FString::FromInt(teamID) + " mid_scrum_win", mid_scrum_win);
	objTeam->SetNumberField(FString::FromInt(teamID) + " mid_maul_win", mid_maul_win);
	objTeam->SetNumberField(FString::FromInt(teamID) + " mid_line_width", mid_line_width);
	objTeam->SetNumberField(FString::FromInt(teamID) + " mid_line_depth", mid_line_depth);
	objTeam->SetNumberField(FString::FromInt(teamID) + " mid_ruck_commitment", mid_ruck_commitment);
	objTeam->SetNumberField(FString::FromInt(teamID) + " mid_pod_option", mid_pod_option);
	objTeam->SetNumberField(FString::FromInt(teamID) + " att_forward_pass_drive", att_forward_pass_drive);
	objTeam->SetNumberField(FString::FromInt(teamID) + " att_forward_contact_offload", att_forward_contact_offload);
	objTeam->SetNumberField(FString::FromInt(teamID) + " att_back_pass_kick", att_back_pass_kick);
	objTeam->SetNumberField(FString::FromInt(teamID) + " att_back_contact_offload", att_back_contact_offload);
	objTeam->SetNumberField(FString::FromInt(teamID) + " att_lineout_size", att_lineout_size);
	objTeam->SetNumberField(FString::FromInt(teamID) + " att_lineout_favoured_target", att_lineout_favoured_target);
	objTeam->SetNumberField(FString::FromInt(teamID) + " att_ruck_win", att_ruck_win);
	objTeam->SetNumberField(FString::FromInt(teamID) + " att_lineout_win", att_lineout_win);
	objTeam->SetNumberField(FString::FromInt(teamID) + " att_scrum_win", att_scrum_win);
	objTeam->SetNumberField(FString::FromInt(teamID) + " att_maul_win", att_maul_win);
	objTeam->SetNumberField(FString::FromInt(teamID) + " att_line_width", att_line_width);
	objTeam->SetNumberField(FString::FromInt(teamID) + " att_line_depth", att_line_depth);
	objTeam->SetNumberField(FString::FromInt(teamID) + " att_ruck_commitment", att_ruck_commitment);
	objTeam->SetNumberField(FString::FromInt(teamID) + " att_pod_option", att_pod_option);
	objTeam->SetNumberField(FString::FromInt(teamID) + " kick_kickoff_short_long", kick_kickoff_short_long);
	objTeam->SetNumberField(FString::FromInt(teamID) + " kick_kickoff_left_right", kick_kickoff_left_right);
	objTeam->SetNumberField(FString::FromInt(teamID) + " kick_dropout_short_long", kick_dropout_short_long);
	objTeam->SetNumberField(FString::FromInt(teamID) + " kick_dropout_left_right", kick_dropout_left_right);
	objTeam->SetNumberField(FString::FromInt(teamID) + " kick_touch_territory", kick_touch_territory);
	objTeam->SetNumberField(FString::FromInt(teamID) + " kick_penalty_touch_goal", kick_penalty_touch_goal);
}

//void RUDB_TEAM::DeserializeFromJson(TSharedPtr< FJsonObject > InJson)
//{
//	SetServerID(InJson->GetObjectField("_id")->GetStringField("$id")); //m_serverID = InJson->GetObjectField("_id")->GetStringField("$id");
//	//SetServerStatus(InJson->GetStringField("status")); //m_serverStatus = InJson->GetStringField("status");
//
//	SetCreatedBy(InJson->GetStringField("creatorDisplayName"));	//m_createdBy = InJson->GetStringField("creatorDisplayName");
//	SetCreatorId(InJson->GetStringField("creator"));	//m_createdById = InJson->GetStringField("creator");
//
//	SetUploadedBy(InJson->GetStringField("uploaderDisplayName"));	//m_uploadedBy = InJson->GetStringField("uploaderDisplayName");
//	SetUploaderId(InJson->GetStringField("uploader"));	//m_uploadedById = InJson->GetStringField("uploader");
//
//	sprintf(name, "%s", TCHAR_TO_ANSI(*InJson->GetStringField("name")));
//}



///---------------------------------------------------------------------------------
/// Callbacks from serialisation..
void RUDB_TEAM::OnObjectSerialised(  MabNamedValueList& additional_parameters )
{
	MABUNUSED(additional_parameters);
	in_serialisation = true;
}

TSharedPtr< FJsonObject > RUDB_TEAM::SerializeToJson(bool bChampionDataUpload /*= false*/)
{
	TSharedPtr< FJsonObject > objTeam = MakeShareable(new FJsonObject);

	if (bChampionDataUpload)
	{
		objTeam->SetNumberField("local_id", GetDbId());
	}

	objTeam->SetNumberField("associated_country_id", associated_country_id);
	objTeam->SetNumberField("willingness_to_attack", willingness_to_attack);
	objTeam->SetNumberField("ranking", ranking);
	objTeam->SetNumberField("attack", attack);
	objTeam->SetNumberField("defence", defence);
	objTeam->SetNumberField("ruck_ability", ruck_ability);
	objTeam->SetNumberField("maul_ability", maul_ability);
	objTeam->SetNumberField("scrum_ability", scrum_ability);
	objTeam->SetNumberField("lineout_ability", lineout_ability);

	SerializeTacticsToJson(objTeam);

#if defined (FANHUB_ENABLED)
	if (!bChampionDataUpload)
	{
		objTeam->SetNumberField("rating", rating);
	}
#endif

	unsigned short use_id = GetLineupPositionFromID(captain_id);
	objTeam->SetNumberField("captain_id", use_id);

	use_id = GetLineupPositionFromID(play_kicker_id);
	objTeam->SetNumberField("play_kicker_id", use_id);

	use_id = GetLineupPositionFromID(goal_kicker_id);
	objTeam->SetNumberField("goal_kicker_id", use_id);

	objTeam->SetObjectField("logo", logo.SerializeToJson());

	int lineup_count = 0;
	TArray< TSharedPtr< FJsonValue > > array_lineup;

	for (MabVector<RUDB_LINEUP>::iterator it = lineup.begin(); it != lineup.end(); it++)
	{
		TSharedPtr< FJsonObject > lineupObj = it->SerializeToJson();
		array_lineup.Add(MakeShareable(new FJsonValueObject(lineupObj)));
	}
	objTeam->SetArrayField("lineup", array_lineup);


#ifdef ENABLE_SEVENS_MODE
	objTeam->SetBoolField("r7_exclusive", r7_exclusive);
#endif

	objTeam->SetNumberField("permission_flags_gender", permission_flags_gender);

	int home_stadium_count = 0;
	TArray< TSharedPtr< FJsonValue > > array_home_stadium;
	for (MabVector<RUDB_HOME_STADIUM>::iterator it = home_stadiums.begin(); it != home_stadiums.end(); it++)
	{
		TSharedPtr< FJsonObject > stadiumObj = it->SerializeToJson();
		array_home_stadium.Add(MakeShareable(new FJsonValueObject(stadiumObj)));
	}
	objTeam->SetArrayField("home_stadium_array", array_home_stadium);

	objTeam->SetStringField("name", UTF8_TO_TCHAR(name));
	objTeam->SetStringField("short_name", UTF8_TO_TCHAR(short_name));
	objTeam->SetStringField("commentary_name", commentary_name);
	objTeam->SetStringField("mnemonic", mnemonic);
	objTeam->SetNumberField("commentary_name_id", commentary_name_id);
	objTeam->SetNumberField("logo_id", logo_id);
	objTeam->SetNumberField("boot_texture_num", boot_texture_num);

	RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();

	TArray< TSharedPtr< FJsonValue > > array_strip_id;
	unsigned int strip_id_count = 0;

	for (; strip_id_count < MAX_STRIPS; strip_id_count++)
	{
		TSharedPtr< FJsonObject > obj_strip = MakeShareable(new FJsonObject);
		int32 current_strip_id = strip_id[strip_id_count];
		if (current_strip_id > DB_LAST_STRIP_ID)
		{
//#if defined (SHOW_UGC_CREATOR)
			//current_strip_id = TEAM_CUSTOMISATION_CHARACTER_STRIP;
//#else
			RUDB_TEAM_STRIP StripToSerialize;

 			if (database_manager)
 			{
 				database_manager->LoadData<RUDB_TEAM_STRIP>(StripToSerialize, current_strip_id);
 
 				obj_strip = StripToSerialize.SerializeToJson();
 			}
//#endif
		}

//#if defined (SHOW_UGC_CREATOR)
//		obj_strip->SetNumberField("strip", current_strip_id);
//#else
		obj_strip->SetNumberField("strip", strip_id[strip_id_count]);
//#endif
		array_strip_id.Add(MakeShareable(new FJsonValueObject(obj_strip)));
	}
	objTeam->SetArrayField("strip_id", array_strip_id);

	return objTeam;
}


void RUDB_TEAM::SerializeToJson(FString& json_output, bool bChampionDataUpload /*= false*/)
{
	TSharedPtr< FJsonObject > objTeam = SerializeToJson(bChampionDataUpload);

	FString OutputString;
	TSharedRef< TJsonWriter<> > Writer = TJsonWriterFactory<>::Create(&OutputString);
	FJsonSerializer::Serialize(objTeam.ToSharedRef(), Writer);

	json_output = OutputString;
}

//bool RUDB_TEAM::DeserializeFromJsonSimplified(std::string& json_input)
//{
//	json::Object js_response_body;
//	std::istringstream is_response_body(json_input);
//	json::Reader::Read(js_response_body, is_response_body);
//
//	std::string str_name = InJson->GetStringField("name");
//	memcpy(name, str_name.c_str(), str_name.size());
//	name[str_name.size()] = '\0'; //need the terminating 0 so we don't get garbage after the memcpy
//
//	rating = static_cast<float>(InJson->GetNumberField("rating"));
//
//#if defined (GAMECENTRE_ENABLED)
//	user_rating = static_cast<int>(InJson->GetNumberField("myrating"));
//#endif
//
//	std::string str_creator = InJson->GetStringField("creator");
//	creator_id = MabString(str_creator.c_str());
//
//	r7_exclusive = json::Boolean(js_response_body["isSeven");
//
//	return true;
//}

void RUDB_TEAM::DeserializeFromJson(TSharedPtr< FJsonObject > InJson, bool bChampionDataDownload /*= false*/)
{

#if defined (FANHUB_ENABLED)
	if (!bChampionDataDownload)
	{
		FString ServerID = InJson->GetObjectField("_id")->GetStringField("$id");

		SetServerID(ServerID);

		SetCreatedBy(InJson->GetStringField("creatorDisplayName"));
		SetCreatedByID(InJson->GetStringField("creator"));

		SetUploadedBy(InJson->GetStringField("uploaderDisplayName"));
		SetUploadedByID(InJson->GetStringField("uploader"));

		SetServerStatus(InJson->GetStringField("status"));
	}
#endif


	//MABLOGMSG(LOGCHANNEL_DEBUG, LOGTYPE_INFO, "RUDB_TEAM::DeserializeFromJson %s", json_input.c_str());

	std::string str_name = TCHAR_TO_UTF8(*InJson->GetStringField("name"));
	memcpy(name, str_name.c_str(), str_name.size());
	name[str_name.size()] = '\0'; //need the terminating 0 so we don't get garbage after the memcpy

	std::string str_short_name = TCHAR_TO_UTF8(*InJson->GetStringField("short_name"));
	memcpy(short_name, str_short_name.c_str(), str_short_name.size());
	short_name[str_short_name.size()] = '\0';

	std::string str_commentary_name = TCHAR_TO_UTF8(*InJson->GetStringField("commentary_name"));
	memcpy(commentary_name, str_commentary_name.c_str(), str_commentary_name.size());
	commentary_name[str_commentary_name.size()] = '\0';

	std::string str_mnemonic = TCHAR_TO_UTF8(*InJson->GetStringField("mnemonic"));
	memcpy(mnemonic, str_mnemonic.c_str(), str_mnemonic.size());
	mnemonic[str_mnemonic.size()] = '\0';

	commentary_name_id = static_cast<unsigned short>(InJson->GetNumberField("commentary_name_id"));

	associated_country_id = static_cast<unsigned short>(InJson->GetNumberField("associated_country_id"));
	willingness_to_attack = static_cast<unsigned short>(InJson->GetNumberField("willingness_to_attack"));
	ranking = static_cast<unsigned short>(InJson->GetNumberField("ranking"));
	attack = static_cast<unsigned short>(InJson->GetNumberField("attack"));
	defence = static_cast<unsigned short>(InJson->GetNumberField("defence"));
	ruck_ability = static_cast<unsigned short>(InJson->GetNumberField("ruck_ability"));
	maul_ability = static_cast<unsigned short>(InJson->GetNumberField("maul_ability"));
	scrum_ability = static_cast<unsigned short>(InJson->GetNumberField("scrum_ability"));
	lineout_ability = static_cast<unsigned short>(InJson->GetNumberField("lineout_ability"));

	DeserializeTacticsFromJson(InJson);

	captain_id = static_cast<unsigned short>(InJson->GetNumberField("captain_id"));
	play_kicker_id = static_cast<unsigned short>(InJson->GetNumberField("play_kicker_id"));
	goal_kicker_id = static_cast<unsigned short>(InJson->GetNumberField("goal_kicker_id"));
	logo_id = static_cast<unsigned short>(InJson->GetNumberField("logo_id"));
	boot_texture_num = static_cast<unsigned char>(InJson->GetNumberField("boot_texture_num"));
	r7_exclusive = static_cast<bool>(InJson->GetBoolField("r7_exclusive"));
	permission_flags_gender = static_cast<unsigned char>(InJson->GetNumberField("permission_flags_gender"));

#if defined (FANHUB_ENABLED)
	if (!bChampionDataDownload)
	{
		rating = static_cast<float>(InJson->GetNumberField("rating"));

		int64 my_rating_field = -1.0;
		InJson->TryGetNumberField("myrating", my_rating_field);
		user_rating = static_cast<int>(my_rating_field);
	}
#endif

//	//logo.DeserializeFromJson(js_response_body["logo"]);

	//Apparently DeserializeFromJson for the logo wasn't doing anything as there was no logo data uploaded to the server.
	// This would lead to a hang when trying to set the customisation team if the logo was not populated with data in time.
	// Considering we have the logo ID I just decided to load the data here.
	// Sounds good to me, - JG
	RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();

	if (logo_id > DB_LAST_LOGO_ID || logo_id == 0)
	{
		logo_id = DB_LAST_LOGO_ID;
	}

	//if (logo.RTTGetType())
	{
		database_manager->LoadData(logo, logo_id);
	}


#if defined (FANHUB_ENABLED)
	//Lineup vector deserialization
	TArray< TSharedPtr< FJsonValue > > array_line_up = InJson->GetArrayField("lineup");

	if (bChampionDataDownload)
	{
		lineup.clear();
		lineup.reserve(array_line_up.Num());
	}

	for (auto& current_lineup : array_line_up)
	{
		unsigned short team_limit = r7_exclusive ? DB_NUM_PLAYERS_PER_TEAM_R7 : DB_NUM_PLAYERS_PER_TEAM;
		if (lineup.size() < team_limit)
		{
			RUDB_LINEUP objLineUp;

			objLineUp.DeserializeFromJson(current_lineup->AsObject());
			lineup.push_back(objLineUp);

			if (objLineUp.player_id == DB_SERVER_PLAYER_ID)
			{
				server_player_list.Add(lineup.size() - 1, objLineUp.server_id);
			}
		}
	}

	// String validation (and censorship) is handled by the fanhub per player.

#else
	for (auto& current_lineup : array_line_up)
	{
		RUDB_LINEUP objLineUp;

		objLineUp.DeserializeFromJson(current_lineup->AsObject());
		lineup.push_back(objLineUp);
	}
#endif

	//home stadium vector deserialization
	TArray< TSharedPtr< FJsonValue > > array_home_stadiums;
	if (bChampionDataDownload)
	{
		array_home_stadiums = InJson->GetArrayField("home_stadium_array");
	}
	else
	{
		array_home_stadiums = InJson->GetArrayField("home_stadium_array");
		
		if(array_home_stadiums.Num() == 0)
		{
			array_home_stadiums = InJson->GetArrayField("home_stadiums");
		}
	}

	int32 home_stadium_counter = 0;
	for (auto& current_stadium : array_home_stadiums)
	{
		if (!bChampionDataDownload || home_stadiums.size() < home_stadium_counter)
		{
			RUDB_HOME_STADIUM objHomeStadium;
			objHomeStadium.DeserializeFromJson(current_stadium->AsObject());
			home_stadiums.push_back(objHomeStadium);
		}
		else
		{
			home_stadiums[home_stadium_counter].DeserializeFromJson(current_stadium->AsObject());
		}

		home_stadium_counter++;
	}

	//Strip id array deserialize.
	TArray< TSharedPtr< FJsonValue > > array_strip_id = InJson->GetArrayField("strip_id");

	unsigned int strip_id_count = 0;

	for(auto& current_strip : array_strip_id)
	{
		if (strip_id_count < MAX_STRIPS)
		{
			TSharedPtr<FJsonObject> obj_strip = current_strip->AsObject();
			strip_id[strip_id_count] = static_cast<unsigned short>(obj_strip->GetNumberField("strip"));

			// We don't have this strip locally, download it and add it to a map.
			if (strip_id[strip_id_count] > DB_LAST_STRIP_ID)
			{
//#if defined (SHOW_UGC_CREATOR)
					//strip_id[strip_id_count] = TEAM_CUSTOMISATION_CHARACTER_STRIP;
//#else
#if defined (FANHUB_ENABLED)
					if (database_manager)
					{
						RUDB_TEAM_STRIP* StripToStore = new RUDB_TEAM_STRIP();

						// Pass through info on the person who uploaded this team. The person who uploaded the team is always the person who uploaded the kit.
						StripToStore->DeserializeFromJson(obj_strip, GetUploadedBy(), GetUploadedByAccountID(), GetUploadedByID());

						URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

						if (pRugbyGameInstance)
						{
							UWWRugbyFanHubService* pFanHubService = Cast<UWWRugbyFanHubService>(pRugbyGameInstance->GetFanHubService());

							if (pFanHubService)
							{
								pFanHubService->AddServerStripToMap(GetServerID(), strip_id[strip_id_count], MakeShareable(StripToStore));
							}
						}
					}

					server_strip_id[strip_id_count] = strip_id[strip_id_count];
					strip_id[strip_id_count] = DB_INVALID_ID;
#else
					strip_id[strip_id_count] = DB_LAST_STRIP_ID
#endif
//#endif
			}

			++strip_id_count;
		}
	}

	is_deserialized = true;
}


bool RUDB_TEAM::GetIsR7Exclusive() const
{
	return r7_exclusive;
}

#if defined (FANHUB_ENABLED)
// Check the server ID has been set or not, if yes, return true, else return false.
// Basically if the server ID has already been set, means this team has been uploaded to the server.
bool RUDB_TEAM::ServerIdStatus()
{
	if (0 == server_id.Compare(""))
	{
		return false;
	}
	else
	{
		return true;
	}
}

FString RUDB_TEAM::GetUploadedBy()
{
	return SplitDisplayAndAccountID(uploaded_by, true);
}

FString RUDB_TEAM::GetUploadedByAccountID()
{
	return SplitDisplayAndAccountID(uploaded_by, false);
}

FString RUDB_TEAM::GetCreatedBy()
{
	return SplitDisplayAndAccountID(created_by, true);
}

FString RUDB_TEAM::GetCreatedByAccountID()
{
	return SplitDisplayAndAccountID(created_by, false);
}

FString RUDB_TEAM::SplitDisplayAndAccountID(FString stringToSplit, bool bReturnDisplayName)
{
	FString Str(stringToSplit);
	FString Delim(",");
	FString DisplayName;
	FString AccountID;

	if (Str.Split(Delim, &DisplayName, &AccountID))
	{
		return bReturnDisplayName ? DisplayName : AccountID;
	}

	return Str;
}

TSharedPtr<RUDB_PLAYER> RUDB_TEAM::GetServerRUDBPlayerAtIndex(int player_index)
{
	TSharedPtr<RUDB_PLAYER> pPlayerAtIndex;

	FString* pString = server_player_list.Find(player_index);

	MABASSERTMSG(pString, "Could not find player at index!");
	if (pString)
	{
		URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

		if (pRugbyGameInstance)
		{
			UWWRugbyFanHubService* pFanHubService = Cast<UWWRugbyFanHubService>(pRugbyGameInstance->GetFanHubService());

			if (pFanHubService)
			{
				pPlayerAtIndex = pFanHubService->TryGetDownloadedPlayer(*pString);
			}
		}
	}

	return pPlayerAtIndex;
}

void RUDB_TEAM::Censor()
{
	SetName("Censored");
	SetShortName("Cen");
	SetMnemonic("CEN");
}

#endif
