#pragma once
 
#include "Misc/DateTime.h"
#include "CareerModePopulatedInterface.generated.h"
 
USTRUCT(BlueprintType)
struct FCareerMenuPopulationPack
{
	GENERATED_BODY()
	UPROPERTY(BlueprintReadOnly)
	FText Year;
	UPROPERTY(BlueprintReadOnly)
	FText Competition;
	UPROPERTY(BlueprintReadOnly)
	FText Round;
	UPROPERTY(BlueprintReadOnly)
	FText NextMatchHomeTeam;
	UPROPERTY(BlueprintReadOnly)
	FText NextMatchAwayTeam;
	UPROPERTY(BlueprintReadOnly)
	FText ClubTeam;
	UPROPERTY(BlueprintReadOnly)
	FText InternationalTeam;
	UPROPERTY(BlueprintReadOnly)
	FDateTime LastModificationTime;
	UPROPERTY(BlueprintReadOnly)
	int SaveIndex;
};

UINTERFACE(MinimalAPI, Blueprintable)
class UCareerModePopulatedInterface : public UInterface
{
	GENERATED_BODY()
};

class ICareerModePopulatedInterface
{
	GENERATED_BODY()
 
public:
	UFUNCTION(BlueprintCallable, BlueprintImplementableEvent)
		void OnCareerPopulated(
			UPanelWidget* InfoPanelRoot,
			FCareerMenuPopulationPack Data
		);
};