// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIPopulatorInGameSquad.h"

#include "UI/GeneratedHeaders/WWUIScreenCareerTeamSquad_UI_Namespace.h"
#include "WWUIFunctionLibrary.h"
#include "WWUIListField.h"
#include "WWUIScrollBox.h"
#include "ScrollBox.h"
#include "Character/RugbyCharacter.h"
#include "ProgressBar.h"
#include "Image.h"
#include "Match/RugbyUnion/RUDBTeam.h"
#include "RugbyGameInstance.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "WWUITranslationManager.h"
#include "Match/HUD/RUHUDUpdater.h"

#ifdef ENABLE_GAME_DEBUG_MENU
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeDebugSettings.h"
#endif
#include "UI/Components/WWUIUserWidgetCareerSquadText.h"
#include "Utility/Helpers/SIFGameHelpers.h"
#include "Utility/consoleVars.h"



#define RUCSP_COUNT			"Column0/Count"
#define RUCSP_POS			"Column1/Pos"
#define RUCSP_SECOND		"Column2/Second"
#define RUCSP_NAME			"Column3/Name"
#define RUCSP_RATING		"Column4/Rating"
#define RUCSP_FATIGUE		"Column5/FatigueBar/Fill"

#define RUCSP_PLACEHOLDER	"PC"

//------------------------------------------------------------------------------------
UWWUIPopulatorInGameSquad::UWWUIPopulatorInGameSquad()
	: prev_starting_index(-1)
	, prev_selected_index(-1)
	, player_to_swap(DB_INVALID_ID)
	, captain_player_id(DB_INVALID_ID)
	, goalkicker_player_id(DB_INVALID_ID)
	, playkicker_player_id(DB_INVALID_ID)
	, list_node(NULL)
{}

UWWUIPopulatorInGameSquad::~UWWUIPopulatorInGameSquad()
{}

void UWWUIPopulatorInGameSquad::Populate(UWidget* widget)
{
	if (inPreConstruct)
	{
		Clear(widget);
	}

	if (!inPreConstruct && list_node)
	{
		// Template for stat lines
		InGameSquadCreationCallback callbackObject(widget);
		UScrollBox* list_box = Cast<UScrollBox>(widget);

		int num_items = ordered_players.Num();

		int playersPerTeam = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();
		int playersOnTeamIncBench = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeamIncBench();

		bool is_injury = list_node->GetBoolProperty("is_injury");
		if (is_injury)
			num_items = playersOnTeamIncBench - playersPerTeam;

		ResizeNodeCountFromTemplate(dataList.TemplateName, num_items, &callbackObject, list_box);

		if (ScreenRef)
		{
#ifdef UI_USING_UMG
			ScreenRef->StoreChildWidgets();
#else
			if (ScreenRef && ScreenRef->GetStateScreen())
			{
				ScreenRef->GetStateScreen()->StoreChildWidgets();
			}
#endif
		}
	}

	prev_starting_index = -1;
	prev_selected_index = -1;
}

void UWWUIPopulatorInGameSquad::PopulateAndRefresh(UWidget* widget)
{
	list_container = Cast<UScrollBox>(widget);

	if (list_container && list_node)
	{
		//< Get loaded team. >
		// The DBTeam will be used to extract all the information we're going to populate the nodes with.
		int team_index = list_node->GetIntProperty(RUCSP_TEAM_INDEX);
		MABASSERT(team_index != MAX_int32);
		RUTeam *team = SIFApplication::GetApplication()->GetActiveGameWorld()->GetTeam(team_index);

		bool refresh_on_swap = list_node->GetBoolProperty(RUCSP_REFRESH_ON_SWAP);

		if (list_container->GetChildrenCount() == 0 || refresh_on_swap)
		{
			// Load players
			ordered_players.Empty();
			GenerateOrderedPlayerList(team, team_index);

			// Populate
			Populate(widget);
		}

		Refresh(widget);


		// Set common properties for the system event
		WWUINodeProperty UpdateEvent = WWUINodeProperty();
		FString EventName = "career_squad_list_update";
		UpdateEvent.SetProperty("system_event", &EventName, PROPERTY_TYPE_FSTRING);

		ScreenRef->OnSystemEvent(UpdateEvent);
	}
}

void UWWUIPopulatorInGameSquad::Refresh(UWidget* widget)
{
	list_container = Cast<UScrollBox>(widget);

	if (list_container && list_node)
	{
		int starting_index = 0;
		int selected_index = list_node->GetSelectedIndex();
		if (selected_index < 0)
			return;

		// The DBTeam will be used to extract all the information we're going to populate the nodes with.

		int team_index = list_node->GetIntProperty(RUCSP_TEAM_INDEX);
		bool is_injury = list_node->GetBoolProperty("is_injury");
		RUTeam *team = SIFApplication::GetApplication()->GetActiveGameWorld()->GetTeam(team_index);
		const RUDB_TEAM& db_team = team->GetDbTeam();

		// Get properties
		int old_player_to_swap = player_to_swap;
		player_to_swap = list_node->GetIntProperty(RUCSP_SWAP_PLAYER_ID);

		// Force refresh on swapping
		bool refresh_on_swap = list_node->GetBoolProperty(RUCSP_REFRESH_ON_SWAP);

		// Get captain and goalkicker of the team
		int old_captain_player_id = captain_player_id;
		int old_goalkicker_player_id = goalkicker_player_id;
		int old_playkicker_player_id = playkicker_player_id;

		captain_player_id = db_team.captain_id;
		goalkicker_player_id = db_team.goal_kicker_id;
		playkicker_player_id = db_team.play_kicker_id;

		// Refresh if necessary
		if (prev_starting_index == starting_index && !refresh_on_swap)
		{
			if (prev_selected_index != selected_index)
			{
				// Unselect current player
				{
					const PlayerInfo& player_info = ordered_players[starting_index + prev_selected_index];
					UWWUIListField* child = Cast<UWWUIListField>(list_container->GetChildAt(prev_selected_index));
					MABASSERT(child);
					PopulatePlayerInfo(player_info, team_index, child, starting_index + prev_selected_index, false);
				}

				// Select the new player
				{
					const PlayerInfo& player_info = ordered_players[starting_index + selected_index];
					UWWUIListField* child = Cast<UWWUIListField>(list_container->GetChildAt(selected_index));
					MABASSERT(child);
					PopulatePlayerInfo(player_info, team_index, child, starting_index + selected_index, true);
				}

				prev_selected_index = selected_index;
			}

			// If player row has changed, just repopulate the new and old players
			if (old_player_to_swap != player_to_swap)
				RepopulatePlayerInfoByIds(list_node, team_index, old_player_to_swap, player_to_swap);

			if (old_captain_player_id != captain_player_id)
				RepopulatePlayerInfoByIds(list_node, team_index, old_captain_player_id, captain_player_id);

			if (old_goalkicker_player_id != goalkicker_player_id)
				RepopulatePlayerInfoByIds(list_node, team_index, old_goalkicker_player_id, goalkicker_player_id);

			if (old_playkicker_player_id != playkicker_player_id)
				RepopulatePlayerInfoByIds(list_node, team_index, old_playkicker_player_id, playkicker_player_id);
		}
		else
		{
			// If refreshing on swap, then choose the selected player again (reset selected index)
			if (refresh_on_swap)
			{
				int select_player_db_id = list_node->GetIntProperty(RUCSP_PLAYER_ID);

				// If current list already has the selected player, dont have to do anything
				for (int i = 0; i < list_container->GetChildrenCount(); ++i)
				{
					const PlayerInfo& player_info = ordered_players[i];
					if (player_info.db_id == select_player_db_id)
					{
						// Reselect in the current list
						selected_index = i;
						list_node->SetSelectedIndex(selected_index);

						select_player_db_id = DB_INVALID_ID;
						break;
					}
				}

				// Reset the starting/selected index to select player
				if (select_player_db_id != DB_INVALID_ID)
				{
					for (int i = 0; i < ordered_players.Num(); i++)
					{
						if (ordered_players[i].db_id == select_player_db_id)
						{
							// Get starting & selected index
							starting_index = i;
							if (i + list_container->GetChildrenCount() >= ordered_players.Num())
							starting_index = (ordered_players.Num() - list_container->GetChildrenCount());
							selected_index = (i - starting_index);

							// Reselect in the current list
							list_node->SetSelectedIndex(selected_index);

							break;
						}
					}
				}
			}


			if (is_injury)
			{
				int playersPerTeam = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();
				int playersOnTeamIncBench = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeamIncBench();

				//listbox->SetNumSelectableItems(playersOnTeamIncBench - playersPerTeam);
				starting_index = playersPerTeam;
			}
			else
			{

				// Refresh the displayed list
				//listbox->SetNumSelectableItems((int)ordered_players.size());

				// Need to make sure that there's no way we can exceeds the bounds of the array.
				if (starting_index + list_container->GetChildrenCount() > ordered_players.Num())
				{
					// This should be a very rare case.
					starting_index = (int)(ordered_players.Num() - list_container->GetChildrenCount());
				}
			}

			// Populate children
			for (int i = 0; i < list_container->GetChildrenCount(); ++i)
			{
				const PlayerInfo& player_info = ordered_players[starting_index + i];
				UWWUIListField* child = Cast<UWWUIListField>(list_container->GetChildAt(i));
				MABASSERT(child);

				// Set info for lua
				unsigned int list_index = (unsigned int)(starting_index + i);
				int prop_db_id = player_info.db_id;
				child->SetProperty(RUCSP_PLAYER_ID, &prop_db_id, PROPERTY_TYPE_INT);
				child->SetProperty(RUCSP_PLAYER_LIST_INDEX, &list_index, PROPERTY_TYPE_INT);

				PopulatePlayerInfo(player_info, team_index, child, list_index, i == selected_index);
			}

		}

		prev_starting_index = starting_index;
		prev_selected_index = selected_index;

		bool refreshProp = false;
		list_node->SetProperty(RUCSP_REFRESH_ON_SWAP, &refreshProp, PROPERTY_TYPE_BOOL);
	}
}

void UWWUIPopulatorInGameSquad::SetLineText(UWWUIScreenTemplate* ScreenRef, UWidget* node, const PlayerInfo& player_info, int team_index)
{
	// Set line number to be the players shirt number.
	//< Set Player Number >
	UTextBlock *child_node = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(node, WWUIScreenCareerTeamSquad_UI::TextNumber));
	ensure(child_node);
	UWWUIFunctionLibrary::SetText(child_node, FString::Printf(TEXT("%d"), player_info.shirt_number));

	//< Set Player Position >
	child_node = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(node, WWUIScreenCareerTeamSquad_UI::TextPosition));
	ensure(child_node);
	UWWUIFunctionLibrary::SetVisibility(child_node, ESlateVisibility::Visible);
	UWWUIFunctionLibrary::SetText(child_node, UWWUITranslationManager::Translate(FString(UTF8_TO_TCHAR(PlayerPositionEnum::GetPlayerPositionTextAbbreviated((PLAYER_POSITION)player_info.primary_position)))));

	//< Set Player Second >
	child_node = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(node, WWUIScreenCareerTeamSquad_UI::TextSecondPosition));
	ensure(child_node);
	UWWUIFunctionLibrary::SetText(child_node, UWWUITranslationManager::Translate(FString(UTF8_TO_TCHAR(PlayerPositionEnum::GetPlayerPositionTextAbbreviated((PLAYER_POSITION)player_info.secondary_position)))));

	FString display_name;
#ifdef ENABLE_GAME_DEBUG_MENU
	if (SIFDebug::GetCareerModeDebugSettings()->GetDisplayIDs())
	{
		FString FirstName = SIFGameHelpers::GAConvertMabStringToFString(player_info.db_player->GetFirstName());
		FString LastName = SIFGameHelpers::GAConvertMabStringToFString(player_info.db_player->GetLastName());

		if (!FirstName.IsEmpty())
		{
			display_name = FString::FromInt(player_info.db_id) + ", " + LastName + ", " + FirstName[0] + ".";
		}
		else
		{
			display_name = FString::FromInt(player_info.db_id) + ", " + LastName + ", ";
		}
		
	}
	else
#endif
	{
		FString FirstName = SIFGameHelpers::GAConvertMabStringToFString(player_info.db_player->GetFirstName());
		FString LastName = SIFGameHelpers::GAConvertMabStringToFString(player_info.db_player->GetLastName());

		if (!FirstName.IsEmpty())
		{
			display_name = LastName + ", " + FirstName[0] + ".";
		}
		else
		{
			display_name = LastName + ", ";
		}
		
	}

	MabString displayName = SIFGameHelpers::GAConvertFStringToMabString(display_name);

	RUHUDUpdater::CensorPlayerName(NULL, player_info.db_player, displayName);

	//< Set Player Name >
	child_node = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(node, WWUIScreenCareerTeamSquad_UI::TextName));
	ensure(child_node);
	UWWUIFunctionLibrary::SetVisibility(child_node, true);
	UWWUIFunctionLibrary::SetText(child_node, SIFGameHelpers::GAConvertMabStringToFText(displayName).ToUpper());

	//< Set Player Rating >
	child_node = Cast<UTextBlock>(ScreenRef->FindChildOfTemplateWidget(node, WWUIScreenCareerTeamSquad_UI::TextRating));
	ensure(child_node);
	UWWUIFunctionLibrary::SetVisibility(child_node, true);
	UWWUIFunctionLibrary::SetText(child_node, FString::FromInt(player_info.overall_rating));

	//-----------------------------
	// Set fatigue bar.
	SIFGameWorld *game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	RUTeam *team = game_world->GetTeam(team_index);
	ARugbyCharacter* game_player = team->GetPlayerByDbId(player_info.db_id);

	float fitness = 1.0f;
	float max_fitness_lost = 0.0f;
	if (game_player != NULL)
	{
		fitness = 1.0f - game_player->GetAttributes()->GetFatigue();
		max_fitness_lost = game_player->GetAttributes()->GetMaxFatigue();
	}

	UProgressBar* fatigue_bar_node = Cast<UProgressBar>(ScreenRef->FindChildOfTemplateWidget(node, WWUIScreenCareerTeamSquad_UI::FatigueBar));
	UProgressBar* max_fatigue_bar_node = Cast<UProgressBar>(ScreenRef->FindChildOfTemplateWidget(node, WWUIScreenCareerTeamSquad_UI::MaxFatigueBar));

	UWWUIFunctionLibrary::SetVisibility(fatigue_bar_node, true);
	if (fatigue_bar_node)
	{
		fatigue_bar_node->SetFillColorAndOpacity(fitness > 0.25f ? FLinearColor(PIGS_GREEN_TEXT_COLOR) : FLinearColor(PIGS_RED_TEXT_COLOR));	//	Green - Red
		fatigue_bar_node->SetPercent(fitness);
	}
	else
	{
		ensure(fatigue_bar_node);
	}

	if (CVarShowMaxStaminaLoss.GetValueOnGameThread() != 0)
	{
		if (max_fatigue_bar_node)
		{
			max_fatigue_bar_node->SetPercent(max_fitness_lost);
		}
		else
		{
			ensure(max_fatigue_bar_node);
		}
	}
}

// Static functions.
inline bool ListContainsInterchange(MabVector< RU_INTERCHANGE_EVENT_TYPE >& interchange_list, RU_INTERCHANGE_EVENT_TYPE interchange_type)
{
	return std::find(interchange_list.begin(), interchange_list.end(), interchange_type) != interchange_list.end();
}

void UWWUIPopulatorInGameSquad::PopulatePlayerInfo(const PlayerInfo& player_info, int team_index, UWWUIListField* node, unsigned int list_index, bool selected)
{
	// Get child node that should be populated
	if (node)
	{

		// Setup marked and swapped players.
		int playersOnTeamIncBench = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeamIncBench();

		bool marked = (player_info.injury_period > 0 || player_info.suspension_period > 0) && list_index <= (unsigned int)playersOnTeamIncBench;		// NB. list_index starts at 1.

		bool swapped = player_to_swap == player_info.db_id;

		UWidget* pChild = ScreenRef->FindChildOfTemplateWidget(node, WWUIScreenCareerTeamSquad_UI::CareerSquadText);

		if (pChild)
		{

			UWWUIUserWidgetCareerSquadText* pSquadText = Cast< UWWUIUserWidgetCareerSquadText>(pChild);

			if (pSquadText)
			{
				pSquadText->SetSquadTextColor(PIGS_DEFAULT_TEXT_COLOR, true);


				if (marked)
				{
					pSquadText->SetSquadTextColor(FLinearColor(PIGS_MARKED_TEXT_COLOR));
				}

				else if (swapped)
				{
					pSquadText->SetSquadTextColor(PIGS_SWAP_TEXT_COLOR);
				}

			}
			else
			{
				ensure(pSquadText);
			}

			// Setup the divider
			UImage* pSquadDividerImage = Cast<UImage>(ScreenRef->FindChildOfTemplateWidget(node, "ImageDivider"));

			int playersPerTeam = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();

			if (pSquadDividerImage)
			{
				pSquadDividerImage->SetVisibility(((int)list_index == playersPerTeam || (int)list_index == playersOnTeamIncBench) ?
					ESlateVisibility::Visible :
					ESlateVisibility::Collapsed);
			}

			// Turn on indicators and get the status

			UWidget* captain_node = ScreenRef->FindChildOfTemplateWidget(pChild, WWUIScreenCareerTeamSquad_UI::RoleIconCaptain);
			UWidget* goalkicker_node = ScreenRef->FindChildOfTemplateWidget(pChild, WWUIScreenCareerTeamSquad_UI::RoleIconGoalKicker);
			UWidget* playkicker_node = ScreenRef->FindChildOfTemplateWidget(pChild, WWUIScreenCareerTeamSquad_UI::RoleIconPlayKicker);
			UWidget* yellow_card_node = ScreenRef->FindChildOfTemplateWidget(pChild, WWUIScreenCareerTeamSquad_UI::SizeBoxYellowCard);
			UWidget* red_card_node = ScreenRef->FindChildOfTemplateWidget(pChild, WWUIScreenCareerTeamSquad_UI::SizeBoxSuspended);
			UWidget* injury_node = ScreenRef->FindChildOfTemplateWidget(pChild, WWUIScreenCareerTeamSquad_UI::SizeBoxInjured);

			UWidget* subarrow_on = ScreenRef->FindChildOfTemplateWidget(node, WWUIScreenCareerTeamSquad_UI::OverlaySwapIn);
			UWidget* subarrow_on2 = ScreenRef->FindChildOfTemplateWidget(node, WWUIScreenCareerTeamSquad_UI::SizeBoxSwapIn);
			UWidget* subarrow_off = ScreenRef->FindChildOfTemplateWidget(node, WWUIScreenCareerTeamSquad_UI::OverlaySwapOut);
			UWidget* subarrow_off2 = ScreenRef->FindChildOfTemplateWidget(node, WWUIScreenCareerTeamSquad_UI::SizeBoxSwapOut);


			bool bPointerCheck = captain_node && goalkicker_node && playkicker_node && yellow_card_node && red_card_node && injury_node && subarrow_on && subarrow_on2 && subarrow_off && subarrow_off2;
			if (!bPointerCheck)
			{
				ensure(bPointerCheck);
				return;
			}

			captain_node->SetVisibility(ESlateVisibility::Collapsed);
			goalkicker_node->SetVisibility(ESlateVisibility::Collapsed);
			playkicker_node->SetVisibility(ESlateVisibility::Collapsed);
			yellow_card_node->SetVisibility(ESlateVisibility::Collapsed);
			red_card_node->SetVisibility(ESlateVisibility::Collapsed);
			injury_node->SetVisibility(ESlateVisibility::Collapsed);

			MabString status("");

			//float xoffset = 0.0f;
			//const int STANDARD_DEF_WIDTH = 720;
			//const float DELTA_HD = 30.0f;
			//const float DELTA_SD = 18.0f;
			//int raster_width = SIFApplication::GetApplication()->GetApplicationParameters().raster_width;
			//float icon_delta = (raster_width <= STANDARD_DEF_WIDTH) ? DELTA_SD : DELTA_HD;
			//
			if (player_info.db_id == captain_player_id)
			{
				captain_node->SetVisibility(ESlateVisibility::Visible);
			//	captain_node->SetPosition(MabVector3(xoffset, 0.0f, 0.0f));
			//	xoffset += icon_delta;
			}
			if (player_info.db_id == goalkicker_player_id)
			{
				goalkicker_node->SetVisibility(ESlateVisibility::Visible);
			//	goalkicker_node->SetPosition(MabVector3(xoffset, 0.0f, 0.0f));
			//	xoffset += icon_delta;
			}
			if (player_info.db_id == playkicker_player_id)
			{
				playkicker_node->SetVisibility(ESlateVisibility::Visible);
			//	playkicker_node->SetPosition(MabVector3(xoffset, 0.0f, 0.0f));
			//	xoffset += icon_delta;
			}

			// Turn on children 
			//for (size_t i = 0; i < node->GetNumChildren(); i++)
			//	node->GetChildByIndex(i)->SetVisibility(ESlateVisibility::Visible);

			// Turn off background bar... (Done after 'Turn on children'!)
			//MabUINode* bar_node = unselected_node->GetChildByContext("SubBar");
			//bar_node->SetVisible(false);

			subarrow_on->SetVisibility(ESlateVisibility::Collapsed);
			subarrow_on2->SetVisibility(ESlateVisibility::Collapsed);
			subarrow_off->SetVisibility(ESlateVisibility::Collapsed);
			subarrow_off2->SetVisibility(ESlateVisibility::Collapsed);

			RUSubstitutionManager* substitution_manager = SIFApplication::GetApplication()->GetActiveGameWorld()->GetSubstitutionManager();
			MabVector< RU_INTERCHANGE_EVENT_TYPE > player_interchange_events;

			/// Has player already been substuted off? (can't be sub'd on again\ - except for specific rules)
			if (substitution_manager->AlreadySubstituted(player_info.db_id, team_index))
			{
				pSquadText->SetSquadTextColor(PIGS_GREY_TEXT_COLOR);
			}

			//-------------------------------

			player_interchange_events.clear();
			substitution_manager->GetPlayerPendingInterchangeEvents(player_interchange_events, player_info.db_id, team_index);
			const bool player_pending_exchange = ListContainsInterchange(player_interchange_events, RU_INTERCHANGE_EXCHANGE);

			if (player_pending_exchange)
			{
				if (substitution_manager->IsPlayerOnField(player_info.db_id, team_index))
				{
					subarrow_off->SetVisibility(ESlateVisibility::Visible);
					subarrow_off2->SetVisibility(ESlateVisibility::Visible);
				}
				else
				{
					subarrow_on->SetVisibility(ESlateVisibility::Visible);
					subarrow_on2->SetVisibility(ESlateVisibility::Visible);
				}
			}

			player_interchange_events.clear();
			substitution_manager->GetPlayerFinishedInterchangeEvents(player_interchange_events, player_info.db_id, team_index);

			// Player has already been exchanged.
			const bool player_exchanged = ListContainsInterchange(player_interchange_events, RU_INTERCHANGE_EXCHANGE);
			if (player_exchanged)
			{
			}

			const bool player_sentoff = ListContainsInterchange(player_interchange_events, RU_INTERCHANGE_SENTOFF);
			if (player_sentoff)
			{
				pSquadText->SetSquadTextColor(PIGS_GREY_TEXT_COLOR);
				red_card_node->SetVisibility(ESlateVisibility::Visible);
				//red_card_node->SetPosition(MabVector3(xoffset, 0.0f, 0.0f));
				//xoffset += icon_delta;
			}

			const bool player_sinbin = ListContainsInterchange(player_interchange_events, RU_INTERCHANGE_SINBIN_OFF);	// && !ListContainsInterchange( player_interchange_events, RU_INTERCHANGE_SINBIN_RETURN );
			if (player_sinbin && !player_sentoff)
			{
				yellow_card_node->SetVisibility(ESlateVisibility::Visible);
				//yellow_card_node->SetPosition(MabVector3(xoffset, 0.0f, 0.0f));
				//xoffset += icon_delta;
			}
			
			const bool player_injured = ListContainsInterchange(player_interchange_events, RU_INTERCHANGE_INJURY);
			if (player_injured)
			{
				injury_node->SetVisibility(ESlateVisibility::Visible);
				//injury_node->SetPosition(MabVector3(xoffset, 0.0f, 0.0f));
				//xoffset += icon_delta;
			}


			//GGS SRA: Testing out new way of displaying injured player status
			SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
			RUTeam* team = game_world->GetTeam(team_index);
			ARugbyCharacter* game_player = team->GetPlayerByDbId(player_info.db_id);
			bool injured = false;

			if (game_player != NULL)
			{
				injured = game_player->GetAttributes()->IsInjured();

				if (injured)
				{
					injury_node->SetVisibility(ESlateVisibility::Visible);
				}
			}

			SetLineText(ScreenRef, pChild, player_info, team_index);
		}
		else
		{
			ensure(pChild);
		}
	}
	else
	{
		ensure(node);
	}
}

void UWWUIPopulatorInGameSquad::GenerateOrderedPlayerList(RUTeam* db_team, int team_index)
{
	RU_TEAM_SETTINGS::LineupList& lineup_array = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().team_settings[team_index].lineup;

	ordered_players.Empty();
	int playersOnTeamIncBench = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeamIncBench();

	for (int lineup_index = 0; lineup_index < playersOnTeamIncBench; lineup_index++)
	{
		RUDB_PLAYER *db_player = &lineup_array[lineup_index];

		PlayerInfo pinfo;
		pinfo.db_id = db_player->GetDbId();
		pinfo.db_player = db_player;
		pinfo.shirt_number = (short)db_team->GetShirtNumberFromPlayerDbId(pinfo.db_id);

		// MULTI_POSITION_CATEGORY_CHANGE
		// Nick WWS 7s to Womens 13s //
		/*
		if (SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_settings.GameModeIsR7())
		{
			pinfo.primary_position = db_player->GetPositionCategoryR7(0);
			pinfo.secondary_position = db_player->GetPositionCategoryR7(1);
		}
		else
		{ */
			pinfo.primary_position = db_player->GetPositionCategoryR13(0);
			pinfo.secondary_position = db_player->GetPositionCategoryR13(1);
		//}

		pinfo.overall_rating = (short)db_player->GetOverallRating();

		ordered_players.Add(pinfo);
	}

	//------------------------------------------------------
	// Encode player states as a string to send to lua.

	RUSubstitutionManager* substitution_manager = SIFApplication::GetApplication()->GetActiveGameWorld()->GetSubstitutionManager();
	MabVector< RU_INTERCHANGE_EVENT_TYPE > player_interchange_events;
	player_states.Empty();

	for (int i = 0; i < ordered_players.Num(); i++)
	{
		const PlayerInfo& player_info = ordered_players[i];

		if (substitution_manager->IsPlayerSentOff(player_info.db_id, team_index))
		{
			player_states.Add(EPlayerState::X);
		}
		else
		{
			player_interchange_events.clear();
			substitution_manager->GetPlayerFinishedInterchangeEvents(player_interchange_events, player_info.db_id, team_index);
			const bool player_sinbin = ListContainsInterchange(player_interchange_events, RU_INTERCHANGE_SINBIN_OFF) && !ListContainsInterchange(player_interchange_events, RU_INTERCHANGE_SINBIN_RETURN);
			if (player_sinbin)
			{
				player_states.Add(EPlayerState::Y);
			}
			else
			{
				player_states.Add(EPlayerState::O);
			}
		}
	}

	//--------------------------------------------------------
	// Encode jersey numbers and send to lua

	FString jersey_numbers = "";
	for (int i = 0; i < ordered_players.Num(); i++)
	{
		const PlayerInfo& player_info = ordered_players[i];
		jersey_numbers += (char)(65 + player_info.shirt_number);
	}

	list_node->SetProperty(RUCSP_JERSEY_NUMBERS_PROPERTY, &jersey_numbers, PROPERTY_TYPE_FSTRING);
}

void UWWUIPopulatorInGameSquad::RepopulatePlayerInfoByIds(UWidget* node, int team_index, int player1_db_id, int player2_db_id)
{
	MABASSERT(list_node);

	// Get selected index
	int selected_index = list_node->GetSelectedIndex();
	if (selected_index < 0)
		return;

	// Check all children
	for (int i = 0; i < list_container->GetChildrenCount(); ++i)
	{
		const PlayerInfo& player_info = ordered_players[i];

		if (player_info.db_id != player1_db_id && player_info.db_id != player2_db_id)
			continue;

		// Repopulate
		UWWUIListField* child = Cast<UWWUIListField>(list_container->GetChildAt(i));
		MABASSERT(child);
		unsigned int list_index = (unsigned int)(i);
		PopulatePlayerInfo(player_info, team_index, child, list_index, (int)i == selected_index);
	}
}

UWWUIPopulatorInGameSquad::InGameSquadCreationCallback::InGameSquadCreationCallback(UWidget* containerToPopulate)
{
	container = Cast<UPanelWidget>(containerToPopulate);
}

void UWWUIPopulatorInGameSquad::InGameSquadCreationCallback::Callback(UUserWidget* widget)
{
	FString string_buffer = FString::Printf(TEXT("CareerPlayerDraft_%d"), item_index++);
	//widget->SetName(string_buffer);

	container->AddChild(widget);
}
