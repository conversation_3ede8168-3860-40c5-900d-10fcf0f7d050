/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Tournament.h"

#include "Databases/RUGameDatabaseManager.h"
#include "Match/Debug/RUProfilerService.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3CompetitionHelper.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DBTeam.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Database.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3FixtureHelper.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3TournamentConstants.h"
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeDebugSettings.h"
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "Match/RugbyUnion/RUDatabaseConstants.h"
#include "Match/RugbyUnion/RUSubstitutionManager.h"
#include "Match/RugbyUnion/Statistics/RUStatisticsSystem.h"
#include "Match/RugbyUnion/Statistics/RUStatsTracker.h"
#include "Match/SIFGameWorld.h"


//#rc3_legacy_include #include "RL3StatisticsHelper.h"
//#rc3_legacy_include #include "SIFDebug.h"

#include "RugbyGameInstance.h"

#if defined ENABLE_GAME_DEBUG_MENU
#include "Match/RugbyUnion/RUProModeDebugSettings.h"
#endif

const int DB_INVALID_INT = -1;

// number of players who can be involved
//static const int TOTAL_NUM_PLAYERS_PER_TEAM = NUM_STAT_PLAYERS;
static const float COMPETITION_STARTING_TEAM_CONFIDENCE = 0.5f;

///------------------------------------------------------------------
/// Constructor
///------------------------------------------------------------------

RL3Tournament::RL3Tournament(const RUGameSettings& gameSettings, RUCareerModeManager *manager, RL3Database* ddatabase, RUStatisticsSystem* sstatistics, RL3FixtureHelper* ffixture_helper, RL3CompetitionHelper *ccompetition_helper)
	: mGameSettings(gameSettings)
{
	database = ddatabase;
	statistics = sstatistics;
	fixture_helper = ffixture_helper;
	competition_helper = ccompetition_helper;
	tournament_manager = manager;

	Reset();
}

///------------------------------------------------------------------
/// Destructor
///------------------------------------------------------------------

RL3Tournament::~RL3Tournament()
{
	// RC2_TODO...
}

///------------------------------------------------------------------
/// Reset.
///------------------------------------------------------------------

void RL3Tournament::Reset()
{
	data.last_years_nrl_winner_db_id = 0;
	data.last_years_sl_winner_db_id = 0;
	data.num_competitions = 0;
	have_setup_season_matches = false;
}


///------------------------------------------------------------------
/// Returns the next chronological match
///------------------------------------------------------------------

RL3_SEASON_MATCH RL3Tournament::GetNextMatch()
{
	RL3_SEASON_MATCHES	season_matches = GetSeasonMatches();
	RL3_SEASON_MATCH	match;

	int current_match_index = season_matches.GetCurrentMatchIndex();

	if(current_match_index == season_matches.GetNumMatches() )
		return match;			// NULL.

	match = season_matches.GetMatch(current_match_index);
	return match;
}

///------------------------------------------------------------------
/// Gets the db index of the franchise definition for this tournament
///------------------------------------------------------------------

int RL3Tournament::GetFranchiseDefinitionIndex()
{
	return SIFApplication::GetApplication()->GetCareerModeManager()->GetFranchiseDefinitionIndex();
}


///------------------------------------------------------------------
/// Get franchise region.
///------------------------------------------------------------------

unsigned char RL3Tournament::GetFranchiseRegion()
{
	return database->GetFranchiseDefinition( GetFranchiseDefinitionIndex() ).GetFranchiseRegion();
}

///------------------------------------------------------------------
/// Sets up a competition being run in the Tournament with the given definition
/// which is stored in the competitions array at competition_index
///------------------------------------------------------------------

void RL3Tournament::SetupCompetition( RL3DB_COMPETITION_DEFINITION competition_definition, int competition_index, int year, bool customised )
{
	MABLOGDEBUG("Setting up comp %i, index: %i, (%s), Law: %s, in year %i. Primary comp: %i", competition_definition.GetDbId(), competition_index, (competition_definition.GetGameLawVariation() == GAME_LAW_NORMAL ? "Normal" : "NRC"), competition_definition.GetName(), year, tournament_manager->GetPrimaryCompDefinitionId());

	MabDate start_date = competition_definition.GetStartDate();
	if (customised)
	{
		// Only use release year if it is greater than the first match in our database or custom competitions will crash.
		start_date.SetYear((RELEASE_YEAR > year) ? RELEASE_YEAR : year);
	}
	else
	{
		start_date.SetYear(year);
	}

	// Create this competition instance
	RL3DB_COMPETITION_INSTANCE competition_instance = RL3DB_COMPETITION_INSTANCE::CreateNew(competition_definition,start_date);
	data.competition_ids[competition_index] = competition_instance.GetDbId();

	for( int i = 0; i < competition_definition.GetNumTeams(); i++ )
	{
		unsigned short team_index = competition_definition.GetTeam(i).team_db_id;
		competition_instance.AddTeam(team_index);
	}

	// We need to generate fixtures for this competition

	fixture_helper->GenerateInitialFixtures( competition_definition, competition_instance, customised, year );

	// Create the team and player statistics

	// Only creates player statistics for players in internationals or the primary competition.
	bool ourPrimary = tournament_manager->IsPrimaryCompetition(competition_instance);
	bool isInt = static_cast<COMPETITION_TYPE>(competition_definition.GetCompetitionType())==COMPETITION_INTERNATIONAL;
	bool is_primary_comp = ourPrimary || isInt;

	// Get the number of satelites for this competition
	int numSats = 1;
	if(competition_definition.GetPreliminaryFormat() == PRELIMINARIES_SATELLITE)
		numSats = competition_definition.GetPreliminaryAttribute(FORMAT_SEVENS_NUM_SATELLITES);

	for ( int i = 0; i < competition_instance.GetNumTeams(); i++ )
	{
		RL3DB_TEAM team = competition_instance.GetDbTeam(i);

		bool is_player_team = tournament_manager->IsTeamPlayerControlled((int) team.GetDbId());

		/// Setup stats for team in this competition.
		team.DeleteCompetitionStats(competition_instance.GetDbId());

		// For the number of satellites we have in this tournament create stats for this team. If it's not a satellite tournament, numSats == 0
		for(int s = 0; s < numSats; s ++)
		{
			team.CreateCompetitionStats(competition_instance.GetDbId(), is_player_team, s);

			if(!team.HasCompetitionStats(competition_definition.GetDbId()+COMPINST_TEAM_RECORD_DB_OFFSET, s))
			{
				team.CreateCompetitionStats(competition_definition.GetDbId()+COMPINST_TEAM_RECORD_DB_OFFSET, is_player_team, s);
			}

			team.SetCompetitionConfidence(competition_instance.GetDbId(), s, COMPETITION_STARTING_TEAM_CONFIDENCE);
		}

		// Create player stats.
		int num_players = team.GetNumPlayers();
		for(int plr_idx=0;plr_idx<num_players;plr_idx++)
		{
			RL3DB_CONTRACT contract = team.GetPlayer(plr_idx);
			RL3DB_PLAYER player((unsigned short)contract.GetPlayerID());

			player.DeleteCompetitionStats(competition_instance.GetDbId());
			if(is_primary_comp)
			{
				player.CreateCompetitionStats(competition_instance.GetDbId());
				if(!player.HasCompetitionStats(competition_definition.GetDbId()+COMPINST_PERSONAL_BEST_DB_OFFSET))
				{
					player.CreateCompetitionStats(competition_definition.GetDbId()+COMPINST_PERSONAL_BEST_DB_OFFSET);
				}
			}
		}
	}

	data.num_competitions++;

	/// Add preseason friendlies, if primary competition...

	if(tournament_manager->IsInFranchise())
	{
		if(tournament_manager->IsPrimaryCompetition(competition_instance))
		{
			start_date = competition_instance.GetStartDate();

			if ((tournament_manager->GetIsCareerGameModeR7() && start_date.GetYear() == RELEASE_YEAR) || tournament_manager->GetIsCareerGameModeR13())
			{
				start_date.IncrementDate(start_date,-(6*7));				// 6 weeks before...

				fixture_helper->GeneratePreseasonFriendlies( start_date, 4);
			}
		}
	}
}

///------------------------------------------------------------------
/// When playing pro career, and we change teams, our primary competition will change, therefore we wont have stats for that comp. We create them here
/// This is similar to the SetupCompetition code, except we only do the second half for the stats
///------------------------------------------------------------------

void RL3Tournament::RecreatePlayerStatsForProCareer( RL3DB_COMPETITION_INSTANCE competition_instance, RL3DB_COMPETITION_DEFINITION competition_definition )
{
	// Get the number of satelites for this competition
	int numSats = 1;
	if(competition_definition.GetPreliminaryFormat() == PRELIMINARIES_SATELLITE)
		numSats = competition_definition.GetPreliminaryAttribute(FORMAT_SEVENS_NUM_SATELLITES);

	// Go through the instance of the competition, and check all the teams, create the stats
	for ( int i = 0; i < competition_instance.GetNumTeams(); i++ )
	{
		RL3DB_TEAM team = competition_instance.GetDbTeam(i);

		bool is_player_team = tournament_manager->IsTeamPlayerControlled((int) team.GetDbId());

		/// Setup stats for team in this competition.
		team.DeleteCompetitionStats(competition_instance.GetDbId());

		// For the number of satellites we have in this tournament create stats for this team. If it's not a satellite tournament, numSats == 0
		for(int s = 0; s < numSats; s ++)
		{
			team.CreateCompetitionStats(competition_instance.GetDbId(), is_player_team, s);

			if(!team.HasCompetitionStats(competition_definition.GetDbId()+COMPINST_TEAM_RECORD_DB_OFFSET, s))
			{
				team.CreateCompetitionStats(competition_definition.GetDbId()+COMPINST_TEAM_RECORD_DB_OFFSET, is_player_team, s);
			}

			team.SetCompetitionConfidence(competition_instance.GetDbId(), s, COMPETITION_STARTING_TEAM_CONFIDENCE);
		}

		// Create player stats.
		int num_players = team.GetNumPlayers();
		for(int plr_idx=0;plr_idx<num_players;plr_idx++)
		{
			RL3DB_CONTRACT contract = team.GetPlayer(plr_idx);
			RL3DB_PLAYER player((unsigned short)contract.GetPlayerID());

			player.DeleteCompetitionStats(competition_instance.GetDbId());
			//if(is_primary_comp)
			{
				player.CreateCompetitionStats(competition_instance.GetDbId());
				if(!player.HasCompetitionStats(competition_definition.GetDbId()+COMPINST_PERSONAL_BEST_DB_OFFSET))
				{
					player.CreateCompetitionStats(competition_definition.GetDbId()+COMPINST_PERSONAL_BEST_DB_OFFSET);
				}
			}
		}

		unsigned short proID = (unsigned short)SIFApplication::GetApplication()->GetCareerModeManager()->GetProID();
		bool hasContract = team.HasContractByPlayerID(proID);
		MABLOGDEBUG("Has contract with pro: %s", (hasContract ? "true" : "false"));

		bool hasSignedContract = team.HasSignedContractByPlayerID(proID);
		MABLOGDEBUG("Has signed contract with pro: %s", (hasSignedContract ? "true" : "false"));

		// So we know that our pro player should be playing for this team, so create the stats now
		if(hasContract || hasSignedContract)
		{
			RL3DB_PLAYER player(proID);

			player.DeleteCompetitionStats(competition_instance.GetDbId());
			//if(is_primary_comp)
			{
				player.CreateCompetitionStats(competition_instance.GetDbId());
				if(!player.HasCompetitionStats(competition_definition.GetDbId()+COMPINST_PERSONAL_BEST_DB_OFFSET))
				{
					player.CreateCompetitionStats(competition_definition.GetDbId()+COMPINST_PERSONAL_BEST_DB_OFFSET);
				}
			}
		}
	}
}

///------------------------------------------------------------------
/// Update sliding window of active competitions.
///  Loads competitions in advance of current date.
///------------------------------------------------------------------

void RL3Tournament::UpdateCompetitions( MabDate current_date, bool bIsInitialising/*= false*/)
{
	bool modified = false;

	// Get current_match.
	RL3_SEASON_MATCHES season_matches = GetSeasonMatches();
	unsigned short	current_match_comp_id = 0;
	unsigned short	current_match_round_no = 0;
	unsigned short	current_match_match_no = 0;

	if(season_matches.GetCurrentMatchIndex() < season_matches.GetNumMatches())
	{
		RL3_SEASON_MATCH current_match = season_matches.GetMatch( season_matches.GetCurrentMatchIndex() );
		current_match_comp_id = data.competition_ids[current_match.GetCompIndex()];
		current_match_round_no = current_match.GetRoundNum();
		current_match_match_no = current_match.GetMatchNum();
	}

	// 0. Call OnCompetitionBegin

	for(int i=0;i<data.num_competitions;i++)
	{
		RL3DB_COMPETITION_INSTANCE competition(data.competition_ids[i]);

		if(competition.GetStartDate().GetDayOfYear()==current_date.GetDayOfYear() && competition.GetStartDate().GetYear()==current_date.GetYear())
		{
			MABASSERT(competition.GetNextMatchIndex()==0);
			tournament_manager->OnCompetitionBegin(i);
		}
	}

	// 1. Delete any completed competitions.

	for(int comp_idx=0;comp_idx<data.num_competitions;comp_idx++)
	{
		RL3DB_COMPETITION_INSTANCE competition(data.competition_ids[comp_idx]);
		if(competition.GetNextMatchIndex() >= competition.GetNumMatches() && competition.GetNumMatches() != 0)
		{
			// i) Remove from data.competitions[]
			data.num_competitions--;
			for(int j=comp_idx;j<data.num_competitions;j++)
			{
				data.competition_ids[j] = data.competition_ids[j+1];
			}

			// Moved to with in OnCompetitionEnd()
			//UpdateCompetitionRecords(competition, update_alltime_stats);

			RL3DB_COMPETITION_DEFINITION comp_def = GetCompetitionDefinition( competition.GetCompetitionId() );
			//bool is_primary_comp_or_international = tournament_manager->IsPrimaryCompetition(competition) || static_cast<COMPETITION_TYPE>(comp_def.GetCompetitionType())==COMPETITION_INTERNATIONAL;

			// ii) Delete stats...

			for ( int team_idx = 0; team_idx < competition.GetNumTeams(); team_idx++ )
			{
				RL3DB_TEAM team = competition.GetDbTeam(team_idx);

				if(!tournament_manager->IsTeamPlayerControlled((int)team.GetDbId()) || comp_def.GetIsFriendly())
				{
					team.DeleteCompetitionStats(competition.GetDbId());
				}
			}

			int num_players = database->GetTotalNumPlayers();
			for(int plr_idx=0;plr_idx<num_players;plr_idx++)
			{
				RL3DB_PLAYER player = database->GetPlayerByIndex( plr_idx );
				player.DeleteCompetitionStats(competition.GetDbId());
			}

			// iii) Delete from database.

			competition.DeleteAllRounds();
			competition.DeleteAllTeams();
			database->GetCompInstanceCache()->DeleteEntry(competition.GetDbId());

			// Update CareerModeManager->current_match, as indexes will now have gone out of sync.

			RL3_SEASON_MATCH current_match = tournament_manager->GetCurrentMatch();
			MABASSERT(current_match.comp_index!=comp_idx);
			if(current_match.comp_index>comp_idx)
			{
				current_match.comp_index--;
				tournament_manager->SetCurrentMatch(current_match);
			}

			// And must update season_matches now.
			modified = true;
			comp_idx--;
		}
	}

	// 2. Add new competitions. (Look ahead 12 months + not already one running (this seasons))
	const int ONE_YEAR = 365;
	// Required due to HES request to keep 2019 world cup comp but have other competitions start in 2020.
	// If we are doing first initialisation of career then we want to need to look multiple years ahead or we risk not initialising any competitions
	// that the users teams are involved in, causing issues ranging from crashes to getting stuck simulating to find a match infinitely.
	const int COMPETITION_ACTIVATION_LOOKAHEAD_DAYS = !bIsInitialising ? ONE_YEAR : ONE_YEAR * 3; 

	MabDate activation_date = current_date;
	activation_date.IncrementDate(activation_date, COMPETITION_ACTIVATION_LOOKAHEAD_DAYS);

	RL3DB_FRANCHISE_DEFINITION franchise = database->GetFranchiseDefinition(GetFranchiseDefinitionIndex());
	int num_comps_in_franchise = franchise.GetNumCompetitions();
	//MABLOGDEBUG("Number of comps in franchise: %i", num_comps_in_franchise);

	for (int comp = 0; comp < num_comps_in_franchise; comp++)
	{
		RL3DB_TOURNAMENT_COMPETITION_INSTANCE fran_comp = franchise.GetCompetition(comp);
		RL3DB_COMPETITION_DEFINITION com_def = GetCompetitionDefinition(fran_comp.GetCompetitionDefinitionId());

		MabDate comp_start_date = fran_comp.GetStartDate();
		//MABLOGDEBUG("fran_comp: %i, start date: %i/%i/%i", fran_comp.GetDbId(), comp_start_date.GetDayNumber(), comp_start_date.GetMonth(), comp_start_date.GetYear());

		if (comp_start_date < activation_date && comp_start_date >= current_date)
		{
			bool still_running_this_years = false;

			// Check to see if running this year competition...
			unsigned short comp_def_id = fran_comp.GetCompetitionDefinitionId();
			for (int i = 0; i < data.num_competitions; i++)
			{
				RL3DB_COMPETITION_INSTANCE comp_inst(data.competition_ids[i]);
				if (comp_inst.GetCompetitionId() == comp_def_id)
				{
					still_running_this_years = true;
					break;
				}
			}

			//-----------------------------
			// HAK:- Euronations and Euro Challenge Cup, should be ignored during fast simulate.
			// Stop stats being generated for career if you choose a european club + a european international.
			if (this->tournament_manager->IsFastSimulating())
			{
				if (comp_def_id == DB_COMPID_EURONATIONS_CUP || comp_def_id == DB_COMPID_EURONATIONS)
				{
					still_running_this_years = true;
				}
			}
			//-----------------------------

			if (!still_running_this_years)
			{
				// Active competition...

				if (data.num_competitions >= DB_NUM_COMPETITION_INSTANCES_PER_FRANCHISE)
				{
					MABBREAKMSG("Too many competitions in one year, increase DB_NUM_COMPETITION_INSTANCES_PER_FRANCHISE");
					break;
				}
					
				UE_LOG(LogTemp, Display, TEXT("Adding Competition: %s"), ANSI_TO_TCHAR(com_def.GetName()));

				SetupCompetition(GetCompetitionDefinition(fran_comp.GetCompetitionDefinitionId()), data.num_competitions, comp_start_date.GetYear(), false);

				// And must update season_matches now.
				modified = true;
			}
		}
	}

	// 3. If added comps, need to call SetupSeasonMatches to refresh +
	//    update current_index to reflect deleted matches from previous competition.

	if(modified)
	{
		// Re-generate season matches.
		SetupSeasonMatches();

		// Update index so still points to current match.
		season_matches = GetSeasonMatches();
		for(int i=0;i<season_matches.GetNumMatches();i++)
		{
			RL3_SEASON_MATCH match = season_matches.GetMatch(i);
			if(match.GetMatchNum()==current_match_match_no && match.GetRoundNum()==current_match_round_no)
			{
				if(GetCompetition(match.GetCompIndex()).GetDbId() == current_match_comp_id )
				{
					int index = 0;

					//Hacky fix for ticket #12239. Friendlies for sevens career were being ignored for some reason. The match index was being set to 4
					// which would skip them. Now if it's sevens and the current year is 2014 the index is set to 0 so the users can player friendlies
					if (this->tournament_manager->GetIsCareerGameModeR7())
					{
						if (this->tournament_manager->GetCurrentYear() != (RELEASE_YEAR - 1))
						{
							index = i;
						}
					}
					else
					{
						index = i;
					}

					season_matches.SetCurrentMatchIndex(index);
					return;
				}
			}
		}

		if(current_match_comp_id==0)
		{
			season_matches.SetCurrentMatchIndex(0);
		}
		else
		{
			// Haven't found current match in new season matches!!!! (Should be there)
			MABBREAKMSG("UpdateCompetitions: Error");
		}
	}
}

///------------------------------------------------------------------
/// Competition has finished, update competition records.
///------------------------------------------------------------------

void RL3Tournament::UpdateCompetitionRecords(RL3DB_COMPETITION_INSTANCE competition, bool update_alltime_stats)
{
	RL3DB_COMPETITION_DEFINITION comp_def = GetCompetitionDefinition( competition.GetCompetitionId() );
	bool is_primary_comp_or_international = tournament_manager->IsPrimaryCompetition(competition) || static_cast<COMPETITION_TYPE>(comp_def.GetCompetitionType())==COMPETITION_INTERNATIONAL;

	// i.0) Update the all time top tens for this competition, before deleting...
	if(is_primary_comp_or_international && update_alltime_stats)
	{
		RL3CompetitionHelper::UpdateAllTimeTopTen(competition.GetDbId());
	}

	// ii) Update personal bests, competition records etc...

	for ( int team_idx = 0; team_idx < competition.GetNumTeams(); team_idx++ )
	{
		RL3DB_TEAM team = competition.GetDbTeam(team_idx);

		if(is_primary_comp_or_international)
		{
			team.UpdateCompetitionRecords(competition.GetDbId(), competition.GetCurrentSatellite());
		}

		// Update personal bests for players.

		int num_players = team.GetNumPlayers();
		for(int plr_idx=0;plr_idx<num_players;plr_idx++)
		{
			RL3DB_CONTRACT contract = team.GetPlayer(plr_idx);
			RL3DB_PLAYER player((unsigned short)contract.GetPlayerID());
			if(is_primary_comp_or_international)
			{
				player.UpdateCompetitionPBs(competition.GetDbId());
			}
		}
	}
}

///------------------------------------------------------------------
/// Is competition 'comp_def_db_id' going to start in the next year?
///------------------------------------------------------------------

bool RL3Tournament::IsCompetitionStartingInNextYear(const MabDate &current_date, unsigned short comp_def_id)
{
	const int COMPETITION_ACTIVATION_LOOKAHEAD_DAYS = 365;			// A year

	MabDate activation_date = current_date;
	activation_date.IncrementDate(activation_date,COMPETITION_ACTIVATION_LOOKAHEAD_DAYS);

	RL3DB_FRANCHISE_DEFINITION franchise = database->GetFranchiseDefinition( GetFranchiseDefinitionIndex() );
	int num_comps_in_franchise = franchise.GetNumCompetitions();

	for( int comp = 0; comp < num_comps_in_franchise; comp++ )
	{
		RL3DB_TOURNAMENT_COMPETITION_INSTANCE fran_comp = franchise.GetCompetition(comp);

		if(fran_comp.GetCompetitionDefinitionId()==comp_def_id)
		{
			MabDate comp_start_date = fran_comp.GetStartDate();
			if( comp_start_date < activation_date && comp_start_date>=current_date)
			{
				return true;
			}
		}
	}

	return false;
}

///------------------------------------------------------------------
/// Update end of comp stats for competition (Competition mode only!!!)
///------------------------------------------------------------------

void RL3Tournament::OnCompetitionModeComplete()
{
	MABASSERT(!tournament_manager->IsInFranchise());

	// Done by UpdateCompetitionRecords() now.

	//const int comp_idx = 0;
	//RL3DB_COMPETITION_INSTANCE competition = GetCompetition(comp_idx);
	//RL3DB_COMPETITION_DEFINITION comp_def = GetCompetitionDefinition( competition.GetCompetitionId() );
	//bool is_primary_comp_or_international = tournament_manager->IsPrimaryCompetition(competition) || static_cast<COMPETITION_TYPE>(comp_def.GetCompetitionType())==COMPETITION_INTERNATIONAL;

	//// Update the all time top tens for this competition, before deleting...
	//RL3CompetitionHelper::UpdateAllTimeTopTen(competition.GetDbId());

	//// Update personal bests, competition records etc...
	//for ( int team_idx = 0; team_idx < competition.GetNumTeams(); team_idx++ )
	//{
	//	RL3DB_TEAM team = competition.GetDbTeam(team_idx);
	//	if(is_primary_comp_or_international)
	//	{
	//		team.UpdateCompetitionRecords(competition.GetDbId());
	//	}

	//	// Update personal bests for players.

	//	int num_players = team.GetNumPlayers();
	//	for(int plr_idx=0;plr_idx<num_players;plr_idx++)
	//	{
	//		RL3DB_CONTRACT contract = team.GetPlayer(plr_idx);
	//		RL3DB_PLAYER player((unsigned short)contract.GetPlayerID());
	//		if(is_primary_comp_or_international)
	//		{
	//			player.UpdateCompetitionPBs(competition.GetDbId());
	//		}
	//	}
	//}
}



///------------------------------------------------------------------
/// Sets up the competitions and matches for the passed year in franchise mode
///------------------------------------------------------------------

#if 0
void RL3Tournament::SetupSeasonCompetitions( int year )
{
	data.num_competitions = 0;

	RL3DB_FRANCHISE_DEFINITION franchise = database->GetFranchiseDefinition( GetFranchiseDefinitionIndex() );
	int num_comps_in_franchise = franchise.GetNumCompetitions();

	for( int comp = 0; comp < num_comps_in_franchise; comp++ )
	{
		RL3DB_TOURNAMENT_COMPETITION_INSTANCE fran_comp = franchise.GetCompetition(comp);

		if( fran_comp.GetStartDate().GetYear() == year )
		{
			if( data.num_competitions >= DB_NUM_COMPETITION_INSTANCES_PER_FRANCHISE )
			{
				MABBREAKMSG( "Too many competitions in one year, increase DB_NUM_COMPETITION_INSTANCES_PER_FRANCHISE" );
				break;
			}
			SetupCompetition( GetCompetitionDefinition( fran_comp.GetCompetitionDefinitionId() ), data.num_competitions, year, false, comp );
		}
	}

	// Now setup the matches for this season
	SetupSeasonMatches();
}
#endif


///------------------------------------------------------------------
/// Called when loading an existing competition. (Sets up data.competitions[])
///------------------------------------------------------------------

void RL3Tournament::ReloadCompetitionInstances()
{
	data.num_competitions = 0;

	MabString statement = MabString(0,"SELECT id FROM RUDB_COMP_INST");
	if(database->RunSql(statement))
	{
		while(database->result.Next())
		{
			unsigned short db_id = database->result.GetColumnAsUShort(0);
			data.competition_ids[data.num_competitions] = db_id;
			data.num_competitions++;
		}
	}
}

#if 0
///------------------------------------------------------------------
/// Delete all competition instances (and related matches etc...) from database.
/// Done at end of season.
///------------------------------------------------------------------

void RL3Tournament::DeleteAllCompetitions()
{
	for(int i=0;i<GetNumCompetitions();i++)
	{
		RL3DB_COMPETITION_INSTANCE comp_inst = GetCompetition(i);

		comp_inst.DeleteAllRounds();
		comp_inst.DeleteAllTeams();
		database->GetCompInstanceCache()->DeleteEntry(comp_inst.GetDbId());
	}

	data.num_competitions = 0;
}
#endif


///------------------------------------------------------------------------------
///------------------------------------------------------------------------------

struct TEMP_SEASON_MATCH
{
	TEMP_SEASON_MATCH()
	{
		comp_index =0;
		round_num = 0;
		match_num = 0;
		date = MabDate();
	}
	TEMP_SEASON_MATCH(const TEMP_SEASON_MATCH& other)
	{
		comp_index = other.comp_index;
		round_num = other.round_num;
		match_num = other.match_num;
		date = other.date;
	}
	const TEMP_SEASON_MATCH& operator=(const TEMP_SEASON_MATCH& other)
	{
		comp_index = other.comp_index;
		round_num = other.round_num;
		match_num = other.match_num;
		date = other.date;
		return *this;
	}


	unsigned char		comp_index = 0;
	unsigned char		round_num = 0;
	unsigned char		match_num = 0;
	MabDate				date;
};

struct SeasonMatchSort
{
	bool operator()(TEMP_SEASON_MATCH& match, TEMP_SEASON_MATCH& other)
	{
		if( match.date == other.date )
		{
			if( match.comp_index == other.comp_index )
			{
				if( match.round_num == other.round_num )
				{
					if( match.match_num == other.match_num )
					{
						MABBREAKMSG("2 Matches with the exact same details! FAIL");
						return true;
					}
					return match.match_num < other.match_num;
				}
				return match.round_num < other.round_num;
			}
			return match.comp_index < other.comp_index;
		}
		return match.date < other.date;
	}
};

bool SeasonMatchSortFunc(const TEMP_SEASON_MATCH& match, const TEMP_SEASON_MATCH& other)
{
	if (match.date == other.date)
	{
		if (match.comp_index == other.comp_index)
		{
			if (match.round_num == other.round_num)
			{
				if (match.match_num == other.match_num)
				{
					MABBREAKMSG("2 Matches with the exact same details! FAIL");
					return true;
				}
				return match.match_num < other.match_num;
			}
			return match.round_num < other.round_num;
		}
		return match.comp_index < other.comp_index;
	}
	return match.date < other.date;
}


///------------------------------------------------------------------------------
/// Fill in the season_matches struct with chronologically ordered matches
///------------------------------------------------------------------------------

void RL3Tournament::SetupSeasonMatches()
{
	//MabVector<TEMP_SEASON_MATCH> season_matches;
	TArray<TEMP_SEASON_MATCH> season_matches;

	// Start by putting all the matches for each competition into the list

	for( int comp = 0; comp < data.num_competitions; comp++ )
	{
		RL3DB_COMPETITION_INSTANCE competition = GetCompetition(comp);

		//***** BLEDISLOE_SUB_COMPETITION_HACK ****** (search for this to get all related code)
		bool remove_bledisloe_matches = false; // tournament_manager->IsInFranchise() && competition.GetCompetitionId() == DB_COMPID_BLEDISLOE;
		int bled_start_round = 1;

		// Go through each round.
		int num_rounds =  competition.GetNumRounds();

		if (remove_bledisloe_matches && num_rounds == 2)
			bled_start_round = 0;

		for( int round_idx = 0; round_idx < num_rounds; round_idx++ )
		{
			// Go through each match.

			RL3DB_ROUND round = competition.GetRound(round_idx);
			int num_matches = round.GetNumMatches();

			// If bledisloe cup and in franchise mode, don't add the first two rounds to 'season_matches' as these will be added by the quad nations tournament.
			//***** BLEDISLOE_SUB_COMPETITION_HACK ****** (search for this to get all related code)
			if(!remove_bledisloe_matches || (remove_bledisloe_matches && round_idx>bled_start_round))
			{
				for( int match_idx = 0; match_idx < num_matches; match_idx++ )
				{
					RL3DB_MATCH match = round.GetMatch(match_idx);
					TEMP_SEASON_MATCH season_match;

					season_match.comp_index = (unsigned char)comp;
					season_match.round_num = (unsigned char)round_idx;
					season_match.match_num = (unsigned char)match_idx;
					season_match.date = match.GetDate();

					season_matches.Push(season_match);
				}
			}
		}
	}

	// Then sort them

	//std::sort( season_matches.begin(), season_matches.end(), SeasonMatchSortFunc );
	season_matches.Sort(SeasonMatchSortFunc);


	// And store.

	season_match_store.clear();
	season_match_store.reserve(season_matches.Num());

	for(int match_idx=0; match_idx<season_matches.Num(); match_idx++)
	{
		TEMP_SEASON_MATCH season_match = season_matches[match_idx];

		SeasonMatchEntry entry;
		entry.comp_index = season_match.comp_index;
		entry.match_num = season_match.match_num;
		entry.round_num = season_match.round_num;
		entry.pad = 0;

		season_match_store.push_back(entry);
	}

	have_setup_season_matches = true;
}

///------------------------------------------------------------------
///------------------------------------------------------------------

int RL3Tournament::GetNumSeasonMatches()
{
	if(!have_setup_season_matches)
		SetupSeasonMatches();

	return (int)season_match_store.size();
}

///------------------------------------------------------------------
///------------------------------------------------------------------

int RL3Tournament::GetCurrentSeasonMatchIndex()
{
	return SIFApplication::GetApplication()->GetCareerModeManager()->GetCurrentSeasonMatchIndex();
}

///------------------------------------------------------------------
///------------------------------------------------------------------

void RL3Tournament::SetCurrentSeasonMatchIndex(int value)
{
	SIFApplication::GetApplication()->GetCareerModeManager()->SetCurrentSeasonMatchIndex(value);
}

///------------------------------------------------------------------
///------------------------------------------------------------------

RL3_SEASON_MATCH RL3Tournament::GetSeasonMatch(int index)
{
	if(!have_setup_season_matches)
		SetupSeasonMatches();

	RL3_SEASON_MATCH match;

	if(index<0 || index>=(int)season_match_store.size())
		return match;

	SeasonMatchEntry entry = season_match_store[index];

	match.comp_index = entry.comp_index;
	match.round_num = entry.round_num;
	match.match_num = entry.match_num;

	// Theres a crash around here reported by HES Trac ticket #3311
	RL3DB_COMPETITION_INSTANCE competition = GetCompetition(match.comp_index);
	RL3DB_ROUND round = competition.GetRound(match.round_num);
	match.match = round.GetMatch(match.match_num);

	return match;
}

///------------------------------------------------------------------
/// Called when current_match is finished either playing or simulating
///------------------------------------------------------------------

void RL3Tournament::OnMatchEnd( const RL3_SEASON_MATCH match, bool is_fast_simulate )
{
	UpdateConfidencesAndStreaksAfterMatch( match );

	// First we'll post the result
	PostResult( match,is_fast_simulate );

	//Then we need to see if any new fixtures can be generated

	RL3DB_COMPETITION_INSTANCE comp_inst =  GetCompetition(match.GetCompIndex());
	RL3DB_COMPETITION_DEFINITION comp_def = GetCompetitionDefinition( comp_inst.GetCompetitionId() );

	fixture_helper->GenerateNewFixtures( comp_def, comp_inst );
}

///------------------------------------------------------------------
/// Increment competition+season match index.
///------------------------------------------------------------------

void RL3Tournament::IncrementMatchIndexes( const RL3_SEASON_MATCH season_match )
{
	RL3DB_COMPETITION_INSTANCE comp_inst = GetCompetition(season_match.GetCompIndex());

	// Increment competitions match index.
	// (Actually recalculates current match index, then increments - Stops sync errors between season and comp indexes)
	int match_idx = comp_inst.GetIndexForMatch(season_match.GetMatch());
	comp_inst.SetNextMatchIndex(match_idx+1);

	// Increment seasons match index.

	RL3_SEASON_MATCHES	season_matches = GetSeasonMatches();
	season_matches.SetCurrentMatchIndex(season_matches.GetCurrentMatchIndex()+1);

	//***** BLEDISLOE_SUB_COMPETITION_HACK ****** (search for this to get all related code)
	// If oz v nz and quad nations then increment the bledisloe 'next match' index.
	if(IsBledisloeMatchInQuadNations(comp_inst, season_match))
	{
		unsigned short bled_db_id = GetBledisloeCompetition();
		MABASSERT(bled_db_id);
		if(bled_db_id!=0)
		{
			RL3DB_COMPETITION_INSTANCE competition( bled_db_id );
			match_idx = competition.GetNextMatchIndex();
			competition.SetNextMatchIndex(match_idx+1);
		}
	}
}

///------------------------------------------------------------------
/// Returns true if this is a bledisloe cup match in the quad nations competition. (+ in franchise mode!!!)
/// ***** BLEDISLOE_SUB_COMPETITION_HACK ****** (search for this to get all related code)
///------------------------------------------------------------------

bool RL3Tournament::IsBledisloeMatchInQuadNations(RL3DB_COMPETITION_INSTANCE comp_inst, RL3_SEASON_MATCH season_match)
{
	//Nick WW DB
	return false;

	RUCareerModeManager* career_manager = SIFApplication::GetApplication()->GetCareerModeManager();
	RL3DB_MATCH match = season_match.GetMatch();
	RL3DB_TEAM home_team = comp_inst.GetDbTeam(match.GetHomeTeam() );
	RL3DB_TEAM away_team = comp_inst.GetDbTeam(match.GetAwayTeam() );

	if(career_manager->IsInFranchise() && comp_inst.GetCompetitionId()==DB_COMPID_QUADNATIONS &&
		((home_team.GetDbId()==DB_TEAMID_NZ && away_team.GetDbId()==DB_TEAMID_AUSTRALIA) ||
		(home_team.GetDbId()==DB_TEAMID_AUSTRALIA && away_team.GetDbId()==DB_TEAMID_NZ)) )
	{
		return true;
	}

	return false;
}

///------------------------------------------------------------------
/// Helper function to get current active bledisloe cup competition.
/// ***** BLEDISLOE_SUB_COMPETITION_HACK ****** (search for this to get all related code)
///------------------------------------------------------------------

unsigned short RL3Tournament::GetBledisloeCompetition()
{
	return 0u;
	for(int comp_idx=0;comp_idx<data.num_competitions;comp_idx++)
	{
		RL3DB_COMPETITION_INSTANCE competition = GetCompetition(comp_idx);
		if(0)//competition.GetCompetitionId()==DB_COMPID_BLEDISLOE)
		{
			return competition.GetDbId();
		}
	}
	return 0u;
}

///------------------------------------------------------------------
/// Helper function to update IRB rankings for teams after a match.
/// http://www.irb.com/rankings/explain/index.html
/// http://fr.wikipedia.org/wiki/Classement_IRB_des_%C3%A9quipes_nationales_de_rugby_%C3%A0_XV#Calcul	(Based on this, but it's in french!)
/// http://www.lassen.co.nz/pagmisc.php#hrh
///------------------------------------------------------------------

void IRBRatingModifier(RL3DB_TEAM home_team, RL3DB_TEAM away_team, int score_home, int score_away, bool is_worldcup)
{
	int home_ranking = (int)home_team.GetRanking();
	int away_ranking = (int)away_team.GetRanking();

	const float RANKING_SCALE = 100.0;
	const float HOMESIDE_RATING_ADVANTAGE = 3.0f;
	const int   LARGE_SCORE_DIFFERENCE = 15;
	const int MAX_RANKING = 20000;

	float rating_home = (float)home_ranking/RANKING_SCALE;
	float rating_away = (float)away_ranking/RANKING_SCALE;

	float	D = rating_home - rating_away + HOMESIDE_RATING_ADVANTAGE;
	MabMath::Clamp(D,-10.0f,10.0f);
	D /= 10.0f;

	float C = 1.0f;
	if(score_home>score_away+LARGE_SCORE_DIFFERENCE || score_away>score_home+LARGE_SCORE_DIFFERENCE)
	{
		C = 1.5f;
	}
	if(is_worldcup)
	{
		C *= 2.0f;
	}

	float home_side_delta_rating  = 0.0f;
	if(score_home>score_away)
	{
		home_side_delta_rating  = C * (1.0f - D);
	}
	else if(score_home<score_away)
	{
		home_side_delta_rating  = - C * (1.0f + D);
	}
	else
	{	// Draw.
		home_side_delta_rating  = - C * D;
	}

//	MABLOGDEBUG("IRBRatingModifier: home = %.2f away = %.2f,  score = %d -- %d,   new ratings = %.2f, %.2f",
//		rating_home, rating_away, score_home, score_away, rating_home + home_side_delta_rating, rating_away - home_side_delta_rating);

	int delta_rating = (int)(home_side_delta_rating * RANKING_SCALE);
	home_ranking += delta_rating;
	away_ranking -= delta_rating;

	MabMath::Clamp(away_ranking,0,MAX_RANKING);
	MabMath::Clamp(home_ranking,0,MAX_RANKING);

	home_team.SetRanking( (unsigned short)home_ranking );
	away_team.SetRanking( (unsigned short)away_ranking );
}


///------------------------------------------------------------------
/// Helper function to Post match results to a given competition.
/// Split off from PostResult to reduce duplicate code.
///------------------------------------------------------------------

void PostResultHelper(RL3DB_TEAM home_team, RL3DB_TEAM away_team, RL3DB_COMPETITION_INSTANCE comp_inst, int score_a, int score_b, int /*round_num*/, int match_num,  bool is_fast_simulate)
{
	//RUCareerModeManager* career_manager = SIFApplication::GetApplication()->GetCareerModeManager();
	//MABLOGDEBUG("Post Result, %s (%i, %i) for start date %i-%i-%i, current date: %i-%i-%i", comp_inst.GetName(),
	//	home_team.GetDbId(), away_team.GetDbId(),
	//	comp_inst.GetStartDate().GetDayNumber(), comp_inst.GetStartDate().GetMonth(), comp_inst.GetStartDate().GetYear(),
	//	career_manager->GetCurrentDate()->GetDayNumber(), career_manager->GetCurrentDate()->GetMonth(), career_manager->GetCurrentDate()->GetYear());

	unsigned short comp_db_id = comp_inst.GetDbId();
	RL3DB_COMPETITION_DEFINITION comp_def(comp_inst.GetCompetitionId());

	bool trials     = comp_def.GetIsFriendly();
	int win_points	= comp_def.GetWinPoints();
	int loss_points	= comp_def.GetLossPoints();
	int draw_points = comp_def.GetDrawPoints();

	bool is_preliminaries = comp_inst.GetIsPrelimRound();//( round_num < comp_def.GetNumPreliminaryRounds() );

	const int BONUS_POINTS = (int) comp_def.GetBonusPoints();
	const int bonus_defending_threshold = (int)comp_def.GetBonusDefendingThreshold();
	const int bonus_attacking_threshold = (int)comp_def.GetBonusAttackingThreshold();
	const bool bonus_attacking_is_relative = comp_def.GetBonusAttackingIsRelative();

	RUStatisticsSystem* statistics = SIFApplication::GetApplication()->GetStatisticsSystem();
	int home_tries = is_fast_simulate ? 0 : statistics->GetCurrentMatchStat (SIDE_A, &RUDB_STATS_TEAM::tries_scored );
	int away_tries = is_fast_simulate ? 0 : statistics->GetCurrentMatchStat (SIDE_B, &RUDB_STATS_TEAM::tries_scored );

	bool home_team_bonus = false;
	bool away_team_bonus = false;

	home_team.IncrementCompetitionGamesPlayed(comp_db_id, comp_inst.GetCurrentSatellite(),1);
	away_team.IncrementCompetitionGamesPlayed(comp_db_id, comp_inst.GetCurrentSatellite(),1);

	if ( score_a > score_b )
	{	// Home team wins.
		home_team.IncrementCompetitionGamesWon(comp_db_id, comp_inst.GetCurrentSatellite(),1);
		away_team.IncrementCompetitionGamesLost(comp_db_id, comp_inst.GetCurrentSatellite(),1);
		if( !trials )
		{
			home_team.IncrementSeasonGamesWon(1);
			away_team.IncrementSeasonGamesLost(1);
		}

		// Defending bonus for away team
		if(score_b >= score_a - bonus_defending_threshold)
		{
			away_team_bonus = true;
		}
		// Attacking bonus for home team
		if((!bonus_attacking_is_relative && home_tries>=bonus_attacking_threshold) ||
			(bonus_attacking_is_relative && home_tries>=(away_tries+bonus_attacking_threshold)))
		{
			home_team_bonus = true;
		}
	}
	else if ( score_b > score_a )
	{	// Away team wins.
		away_team.IncrementCompetitionGamesWon(comp_db_id, comp_inst.GetCurrentSatellite(),1);
		home_team.IncrementCompetitionGamesLost(comp_db_id, comp_inst.GetCurrentSatellite(),1);
		if( !trials )
		{
			away_team.IncrementSeasonGamesWon(1);
			home_team.IncrementSeasonGamesLost(1);
		}

		// Defending bonus for home team
		if(score_a >= score_b - bonus_defending_threshold)
		{
			home_team_bonus = true;
		}
		// Attacking bonus for away team
		if((!bonus_attacking_is_relative && away_tries>=bonus_attacking_threshold) ||
			(bonus_attacking_is_relative && away_tries>=(home_tries+bonus_attacking_threshold)))
		{
			away_team_bonus = true;
		}
	}
	else
	{
		home_team.IncrementCompetitionGamesDrawn(comp_db_id, comp_inst.GetCurrentSatellite(),1);
		away_team.IncrementCompetitionGamesDrawn(comp_db_id, comp_inst.GetCurrentSatellite(),1);

		if( !trials )
		{
			home_team.IncrementSeasonGamesDrawn(1);
			away_team.IncrementSeasonGamesDrawn(1);
		}
	}

	home_team.IncrementCompetitionPointsFor(comp_db_id, comp_inst.GetCurrentSatellite(), (int)score_a );
	home_team.IncrementCompetitionPointsAgainst(comp_db_id, comp_inst.GetCurrentSatellite(), (int)score_b );
	away_team.IncrementCompetitionPointsFor(comp_db_id, comp_inst.GetCurrentSatellite(), (int)score_b );
	away_team.IncrementCompetitionPointsAgainst(comp_db_id, comp_inst.GetCurrentSatellite(), (int)score_a );

	//Check if this match is in the preliminaries and track prelim stats if so
	if ( is_preliminaries )
	{
		home_team.IncrementCompetitionPreliminaryGamesPlayed(comp_db_id, comp_inst.GetCurrentSatellite(),1);
		away_team.IncrementCompetitionPreliminaryGamesPlayed(comp_db_id, comp_inst.GetCurrentSatellite(),1);

		if ( score_a > score_b )
		{
			home_team.IncrementCompetitionPreliminaryGamesWon(comp_db_id, comp_inst.GetCurrentSatellite(),1);
			away_team.IncrementCompetitionPreliminaryGamesLost(comp_db_id, comp_inst.GetCurrentSatellite(),1);

			// Add the competition points
			home_team.IncrementCompetitionPreliminaryPoints(comp_db_id, comp_inst.GetCurrentSatellite(), win_points);
			away_team.IncrementCompetitionPreliminaryPoints(comp_db_id, comp_inst.GetCurrentSatellite(), loss_points);
		}
		else if ( score_b > score_a )
		{
			away_team.IncrementCompetitionPreliminaryGamesWon(comp_db_id, comp_inst.GetCurrentSatellite(),1);
			home_team.IncrementCompetitionPreliminaryGamesLost(comp_db_id, comp_inst.GetCurrentSatellite(),1);

			// Add the competition points
			away_team.IncrementCompetitionPreliminaryPoints(comp_db_id, comp_inst.GetCurrentSatellite(), win_points);
			home_team.IncrementCompetitionPreliminaryPoints(comp_db_id, comp_inst.GetCurrentSatellite(), loss_points);
		}
		else
		{
			home_team.IncrementCompetitionPreliminaryGamesDrawn(comp_db_id, comp_inst.GetCurrentSatellite(),1);
			away_team.IncrementCompetitionPreliminaryGamesDrawn(comp_db_id, comp_inst.GetCurrentSatellite(),1);

			// Add the competition points
			home_team.IncrementCompetitionPreliminaryPoints(comp_db_id, comp_inst.GetCurrentSatellite(), draw_points);
			away_team.IncrementCompetitionPreliminaryPoints(comp_db_id, comp_inst.GetCurrentSatellite(), draw_points);
		}

		home_team.IncrementCompetitionPreliminaryPointsFor(comp_db_id, comp_inst.GetCurrentSatellite(),  score_a );
		home_team.IncrementCompetitionPreliminaryPointsAgainst(comp_db_id, comp_inst.GetCurrentSatellite(),  score_b );
		away_team.IncrementCompetitionPreliminaryPointsFor(comp_db_id, comp_inst.GetCurrentSatellite(),  score_b );
		away_team.IncrementCompetitionPreliminaryPointsAgainst(comp_db_id, comp_inst.GetCurrentSatellite(),  score_a );

		if( home_team_bonus )
		{
			home_team.IncrementCompetitionPreliminaryPoints(comp_db_id, comp_inst.GetCurrentSatellite(), BONUS_POINTS);
			home_team.IncrementCompetitionPreliminaryBonusPoints(comp_db_id, comp_inst.GetCurrentSatellite(), BONUS_POINTS);
		}
		if( away_team_bonus )
		{
			away_team.IncrementCompetitionPreliminaryPoints(comp_db_id, comp_inst.GetCurrentSatellite(), BONUS_POINTS);
			away_team.IncrementCompetitionPreliminaryBonusPoints(comp_db_id, comp_inst.GetCurrentSatellite(), BONUS_POINTS);
		}
	}
	else
	{
		// If we're playing the Sevens world series, they work a little different
		// 2015 GOLD COAST SEVENS ACTUAL RESULTS:
		// FIJI 7S				22	- Won Cup finals
		// SAMOA 7S				19	- Lost Cup finals
		// ENGLAND 7S			17	- Won 3rd place match
		// SOUTH AFRICA 7S		15	- Lost 3rd place match
		// NEW ZEALAND 7S		13	- Won plate finals
		// ARGENTINA 7S			12	- Lost Plate Finals
		// WALES 7S				10	- Played Plate Semi finals
		// AUSTRALIA 7S			10	- Played Plate Semi finals
		// USA 7S				8	- Won Bowl Finals
		// FRANCE 7S			7	- Lost Bowl Finals
		// SCOTLAND 7S			5	- Played Bowl Semi Finals
		// PORTUGAL 7S			5	- Played Bowl Semi Finals
		// CANADA 7S			3	- Won Shield
		// KENYA 7S				2	- Lost Shield
		// JAPAN 7S				1	- Played Shield Semi finals
		// AMERICAN SAMOA 7S	1	- Played Shield Semi finals

		if(comp_def.GetFinalsFormat() == FINALS_SEVENS_SERIES && comp_def.GetPreliminaryFormat() == PRELIMINARIES_SATELLITE)
		{
			int currentRound = comp_inst.GetCurrentSatelliteRelativeRound();
			switch (currentRound)
			{
			case 3: //BQF
				MABLOGDEBUG("Finished a match in BQF, match: %i between %i and %i", match_num, home_team.GetDbId(), away_team.GetDbId());
				win_points = 0;
				loss_points = 0;
				draw_points = 0;
				// No points
				break;
			case 4: //CQF
				MABLOGDEBUG("Finished a match in CQF, match: %i between %i and %i", match_num, home_team.GetDbId(), away_team.GetDbId());;
				win_points = 0;
				loss_points = 0;
				draw_points = 0;
				// No points
				break;

			case 5: //ShSF
				MABLOGDEBUG("Finished a match in ShSF, match: %i between %i and %i", match_num, home_team.GetDbId(), away_team.GetDbId());
				// Loser of game 33 = 1 pts
				// Loser of game 34 = 1 pts
				if(match_num == 0)
				{
					win_points = 0;
					loss_points = 1;
					draw_points = 0;
				}
				else
				{
					win_points = 0;
					loss_points = 1;
					draw_points = 0;
				}
				break;

			case 6: //BSF
				MABLOGDEBUG("Finished a match in BSF, match: %i between %i and %i", match_num, home_team.GetDbId(), away_team.GetDbId());
				// Loser of game 35 = 5 pts
				// Loser of game 36 = 5 pts
				if(match_num == 0)
				{
					win_points = 0;
					loss_points = 5;
					draw_points = 0;
				}
				else
				{
					win_points = 0;
					loss_points = 5;
					draw_points = 0;
				}
				break;

			case 7: //PSF
				MABLOGDEBUG("Finished a match in PSF, match: %i between %i and %i", match_num, home_team.GetDbId(), away_team.GetDbId());
				// Loser of game 37 = 10 pts
				// Loser of game 38 = 10 pts
				if(match_num == 0)
				{
					win_points = 0;
					loss_points = 10;
					draw_points = 0;
				}
				else
				{
					win_points = 0;
					loss_points = 10;
					draw_points = 0;
				}
				break;

			case 8: //CSF
				MABLOGDEBUG("Finished a match in CSF, match: %i between %i and %i", match_num, home_team.GetDbId(), away_team.GetDbId());
				win_points = 0;
				loss_points = 0;
				draw_points = 0;
				// No points
				break;

			case 9: //ShF
				MABLOGDEBUG("Finished a match in ShF, match: %i between %i and %i", match_num, home_team.GetDbId(), away_team.GetDbId());
				// Loser of game 41 = 2 pts
				// Winner of game 41 = 3 pts
				if(match_num == 0)
				{
					win_points = 3;
					loss_points = 2;
					draw_points = 0;
				}
				else
				{
					win_points = 0;
					loss_points = 0;
					draw_points = 0;
				}
				break;

			case 10: //BF
				MABLOGDEBUG("Finished a match in BF, match: %i between %i and %i", match_num, home_team.GetDbId(), away_team.GetDbId());
				// Loser of game 42 = 7 pts
				// Winner of game 42 = 8 pts
				if(match_num == 0)
				{
					win_points = 8;
					loss_points = 7;
					draw_points = 0;
				}
				else
				{
					win_points = 0;
					loss_points = 0;
					draw_points = 0;
				}
				break;

			case 11: //PF
				MABLOGDEBUG("Finished a match in PF, match: %i between %i and %i", match_num, home_team.GetDbId(), away_team.GetDbId());
				// Loser of game 43 = 12 points
				// Winner of game 43 = 13 points
				if(match_num == 0)
				{
					win_points = 13;
					loss_points = 12;
					draw_points = 0;
				}
				else
				{
					win_points = 0;
					loss_points = 0;
					draw_points = 0;
				}
				break;

			case 12: //C 3rd
				MABLOGDEBUG("Finished a match in C 3rd Place, match: %i between %i and %i", match_num, home_team.GetDbId(), away_team.GetDbId());
				// Loser of game 44 = 15 pts
				// Winner of game 44 = 17 pts
				if(match_num == 0)
				{
					win_points = 17;
					loss_points = 15;
					draw_points = 0;
				}
				break;

			case 13: //CF
				MABLOGDEBUG("Finished a match in CF, match: %i", match_num);

				// Loser of game 45 = 19 pts
				// Winner of game 45 = 22 pts
				if(match_num == 0)
				{
					win_points = 22;
					loss_points = 19;
					draw_points = 0;
				}
				break;

			default:
				break;
			}
		}
		else
		{
		}

		if ( score_a > score_b )
		{
			home_team.IncrementCompetitionFinalsPoints(comp_db_id, comp_inst.GetCurrentSatellite(), win_points);
			away_team.IncrementCompetitionFinalsPoints(comp_db_id, comp_inst.GetCurrentSatellite(), loss_points);
		}
		else if ( score_b > score_a )
		{
			away_team.IncrementCompetitionFinalsPoints(comp_db_id, comp_inst.GetCurrentSatellite(), win_points);
			home_team.IncrementCompetitionFinalsPoints(comp_db_id, comp_inst.GetCurrentSatellite(), loss_points);
		}
		else
		{
			away_team.IncrementCompetitionFinalsPoints(comp_db_id, comp_inst.GetCurrentSatellite(), draw_points);
			home_team.IncrementCompetitionFinalsPoints(comp_db_id, comp_inst.GetCurrentSatellite(), draw_points);
		}
	}
}

///------------------------------------------------------------------
///------------------------------------------------------------------

void RL3Tournament::PostResult( RL3_SEASON_MATCH season_match, bool is_fast_simulate )
{
	RUCareerModeManager* career_manager = SIFApplication::GetApplication()->GetCareerModeManager();

	MABASSERT( season_match.GetMatch().GetResult().side_a_score != DB_INVALID_INT );
	MABASSERT( season_match.GetMatch().GetResult().side_b_score != DB_INVALID_INT );

	// Make some local variables for ease of use
	RL3DB_MATCH match = season_match.GetMatch();
	RL3DB_COMPETITION_INSTANCE comp = GetCompetition( season_match.GetCompIndex() );
	RL3DB_COMPETITION_DEFINITION comp_def(comp.GetCompetitionId());

	int round_num	= season_match.GetRoundNum();
	int match_num	= season_match.GetMatchNum();
	int score_a		= match.GetResult().side_a_score;
	int score_b		= match.GetResult().side_b_score;

	// Update the statistics, both in the competition and the team

	RL3DB_TEAM home_team = comp.GetDbTeam(match.GetHomeTeam() );
	RL3DB_TEAM away_team = comp.GetDbTeam(match.GetAwayTeam() );

	// If friendly, or is lions tour and not an australia match then don't post any results.
	if(comp_def.GetIsFriendly() || (career_manager->IsLionsTour() && !(home_team.GetDbId()==DB_TEAMID_AUSTRALIA || away_team.GetDbId()==DB_TEAMID_AUSTRALIA)))
	{
		return;
	}

	// Update IRB ratings for team.
	if(career_manager->IsActive())
	{
		IRBRatingModifier(home_team, away_team, score_a, score_b, comp_def.GetDbId()==DB_COMPID_WORLDCUP);
	}

	PostResultHelper(home_team, away_team, comp, score_a, score_b, round_num, match_num, is_fast_simulate);

	//***** BLEDISLOE_SUB_COMPETITION_HACK ****** (search for this to get all related code)
	// If oz v nz and quad nations then update stats for bledisloe cup competition as well.
	if(IsBledisloeMatchInQuadNations(comp,season_match))
	{
		if(comp.GetCompetitionId()==DB_COMPID_QUADNATIONS)
		{
			unsigned short bled_db_id = GetBledisloeCompetition();
			MABASSERT(bled_db_id);
			if(bled_db_id)
			{
				RL3DB_COMPETITION_INSTANCE competition(bled_db_id);
				PostResultHelper(home_team, away_team, competition, score_a, score_b, (int)competition.GetCurrentRound(), match_num, is_fast_simulate);
			}
		}
	}

	//------------------------------------
	// Update ranfurly shield holder.

	if( score_b > score_a && career_manager->IsActive() && career_manager->IsCurrentMatchRanfurlyShieldChallenge())
	{
		career_manager->SetRanfurlyShieldHolder( away_team.GetDbId() );
	}
}

///------------------------------------------------------------------
/// Records a bye and gives out the required points
///------------------------------------------------------------------

void RL3Tournament::PostBye( RL3_SEASON_MATCH match )
{
	// Get the competition relative id of the team
	unsigned short bye_team_index = match.GetMatch().GetHomeTeam();

	// Get the competition this match is from
	RL3DB_COMPETITION_INSTANCE comp = GetCompetition( match.GetCompIndex() );
	RL3DB_COMPETITION_DEFINITION def = GetCompetitionDefinition( comp.GetCompetitionId() );

	RL3DB_TEAM bye_team = database->GetTeam(comp.GetTeam(bye_team_index).index);

	// Add the bye to the count

	unsigned short comp_db_id = comp.GetDbId();
	bye_team.IncrementCompetitionByes(comp_db_id, comp.GetCurrentSatellite(), 1);

	unsigned short home_team_db_id = GetCompetition( match.GetCompIndex()).GetTeam( match.GetMatch().GetHomeTeam() ).index;

	database->GetTeam( home_team_db_id ).IncrementSeasonByes(1);

	// Check if we are in the preliminaries
	//if ( match.GetRoundNum() < def.GetNumPreliminaryRounds() )
	if(comp.GetIsPrelimRound())
	{
		bye_team.IncrementCompetitionPreliminaryByes(comp_db_id, comp.GetCurrentSatellite(),1);

		// Award the bye points
		bye_team.IncrementCompetitionPreliminaryPoints(comp_db_id, comp.GetCurrentSatellite(),def.GetByePoints());
	}
	else
	{
		bye_team.IncrementCompetitionFinalsPoints(comp_db_id, comp.GetCurrentSatellite(),def.GetByePoints());
	}
}





///------------------------------------------------------------------
/// Updates the two teams confidences based on the matches result.
/// Split off from UpdateConfidencesAndStreaksAfterMatch to reduce duplicate code.
///------------------------------------------------------------------

void UpdateConfidencesAndStreaksAfterMatchHelper( RL3_SEASON_MATCH match, RL3DB_COMPETITION_INSTANCE comp, RL3DB_COMPETITION_INSTANCE match_comp, RUCareerModeManager* manager )
{
	RL3DB_MATCH db_match = match.GetMatch();
	unsigned short comp_db_id = comp.GetDbId();

	// A bye does nothing of course
	if( db_match.GetHomeTeam() == db_match.GetAwayTeam() ) return;

	uint32 HomeTeamMatchID = db_match.GetHomeTeam();
	uint32 AwayTeamMatchID = db_match.GetAwayTeam();

	ensureMsgf(HomeTeamMatchID != SHRT_MAX, TEXT("There was a problem with the fixture for match id: %d, home team ID is invalid!"), db_match.GetDbId());
	ensureMsgf(AwayTeamMatchID != SHRT_MAX, TEXT("There was a problem with the fixture for match id: %d, away team ID is invalid!"), db_match.GetDbId());

	RL3DB_TEAM home_team = match_comp.GetDbTeam( HomeTeamMatchID );
	RL3DB_TEAM away_team = match_comp.GetDbTeam( AwayTeamMatchID );
	RL3DB_RESULT result = db_match.GetResult();

	// Don't update if lions tour friendly (not playing oz)
	if((manager->IsLionsTour() && !(home_team.GetDbId()==DB_TEAMID_AUSTRALIA || away_team.GetDbId()==DB_TEAMID_AUSTRALIA)))
	{
		return;
	}

	// A Draw has no effect on confidences
	if( result.side_a_score ==  result.side_b_score )
	{
		// A draw resets their streaks
		away_team.SetCompetitionMatchStreak(comp_db_id, comp.GetCurrentSatellite(),0);
		home_team.SetCompetitionMatchStreak(comp_db_id, comp.GetCurrentSatellite(),0);
		return;
	}

	RL3DB_TEAM winner = (result.side_a_score > result.side_b_score) ? home_team : away_team;

	// Deal with the confidences
	for( int i = 0; i < 2; i ++ )
	{
		RL3DB_TEAM team = (i == 0) ? home_team : away_team;

		int match_streak = team.GetCompetitionMatchStreak(comp_db_id, comp.GetCurrentSatellite());
		float confidence = team.GetCompetitionConfidence(comp_db_id, comp.GetCurrentSatellite());

		// If we won the match
		if( team == winner )
		{
			//==========================================================
			// Figure out how their streak is doing

			// If they've been on a losing streak this ends it. Or if this is their first win
			if( match_streak <= 0 )
			{
				// They won this match so thats a streak of 1

				match_streak = 1;
				team.SetCompetitionStreakStartDate(comp_db_id, comp.GetCurrentSatellite(), db_match.GetDate());

			}
			else
			{
				// Otherwise this continues their winning streak
				match_streak++;
			}

			//==========================================================
			// See if their confidence changes based on the new streak

			// If their confidence is above 0.5f they need a 5 match streak to increase confidence

			if( confidence > 0.5f )
			{
				if( match_streak >=5 && confidence <= 0.9f )
				{
					confidence += 0.1f;
				}

			}
			else
			{
				// If their confidence is below or equal to 0.5, they only need a 2 match streak to increase confidence
				if( match_streak >= 2 )
				{
					confidence += 0.1f;
				}
			}

		}	//End if( team == winner )

		else
			// They Lost
		{

			//==========================================================
			// Figure out how their streak is doing

			// If they've been on a winning streak this ends it
			if( match_streak >= 0 )
			{
				// They lost this match so that's a streak of -1
				match_streak = -1;
			}
			else
			{
				// Else this continues their losing streak
				match_streak--;
			}

			//==========================================================
			// See if their confidence changes based on the new streak

			// If their confidence is above 0.5f they only need a 2 match streak to decrease confidence
			if( confidence > 0.5f )
			{
				if( match_streak <= -2 )
				{
					confidence -= 0.1f;
				}

			}
			else
			{
				// If they're confidence is below or equal to 0.5, they need a 5 match losing streak to lose confidence
				if( match_streak <= -5 && confidence >= 0.1f )
				{
					confidence -= 0.1f;
				}
			}

		}	// End Else they lost

		team.SetCompetitionMatchStreak(comp_db_id, comp.GetCurrentSatellite(), match_streak);
		team.SetCompetitionConfidence(comp_db_id, comp.GetCurrentSatellite(), confidence);

		if(match_streak > team.GetCompetitionLongestWinningStreak(comp_db_id, comp.GetCurrentSatellite()))
			team.SetCompetitionLongestWinningStreak(comp_db_id, comp.GetCurrentSatellite(), match_streak);

		if((-match_streak) > team.GetCompetitionLongestLosingStreak(comp_db_id, comp.GetCurrentSatellite()))
			team.SetCompetitionLongestLosingStreak(comp_db_id, comp.GetCurrentSatellite(), -match_streak);

	}	//End for each team

}

///------------------------------------------------------------------
/// Updates the two teams confidences based on the matches result
///------------------------------------------------------------------

void RL3Tournament::UpdateConfidencesAndStreaksAfterMatch( RL3_SEASON_MATCH season_match )
{
	RL3DB_COMPETITION_INSTANCE comp_inst = GetCompetition(season_match.GetCompIndex());
	UpdateConfidencesAndStreaksAfterMatchHelper(season_match,comp_inst,comp_inst,tournament_manager);

	//***** BLEDISLOE_SUB_COMPETITION_HACK ****** (search for this to get all related code)
	// If oz v nz and quad nations then update streaks for bledisloe cup competition as well.
	/* Nick WW DB get rid of this shit...
	if(IsBledisloeMatchInQuadNations(comp_inst, season_match))
	{
		unsigned short bled_db_id = GetBledisloeCompetition();
		MABASSERT(bled_db_id);
		if(bled_db_id)
		{
			RL3DB_COMPETITION_INSTANCE competition(bled_db_id);
			UpdateConfidencesAndStreaksAfterMatchHelper(season_match,competition,comp_inst,tournament_manager);
		}
	}
	*/
}


///------------------------------------------------------------------
/// Returns the database index of the home team in the given season match
///------------------------------------------------------------------

unsigned short RL3Tournament::GetHomeTeamDbId( const RL3_SEASON_MATCH match)
{
	MABASSERT( match.GetCompIndex() < data.num_competitions );
	MABASSERT( match.GetMatch().GetHomeTeam() != SHRT_MAX );

	// Convert competition relative index to db_id
	return GetCompetition( match.GetCompIndex() ).GetTeam(match.GetMatch().GetHomeTeam()).index;
}

///------------------------------------------------------------------
// Returns the database index of the away team in the given season match
///------------------------------------------------------------------

unsigned short RL3Tournament::GetAwayTeamDbId( const RL3_SEASON_MATCH match)
{
	MABASSERT( match.GetCompIndex() < data.num_competitions );
	RL3DB_MATCH currentmatch = match.GetMatch();

	MABASSERT( currentmatch.GetAwayTeam() != SHRT_MAX );

	// Convert competition relative index to db_id
	return GetCompetition( match.GetCompIndex() ).GetTeam(match.GetMatch().GetAwayTeam()).index;
}

///------------------------------------------------------------------
// Returns a vector of the matches on the given date, or an empty vector if there isn't one
///------------------------------------------------------------------

MabVector<RL3_SEASON_MATCH> RL3Tournament::GetMatchesOnDate( const MabDate& date )
{
	RL3_SEASON_MATCHES season_matches = GetSeasonMatches();

	MabVector<RL3_SEASON_MATCH> matches;

	for( int i = 0; i < season_matches.GetNumMatches(); i++ )
	{
		RL3_SEASON_MATCH match = season_matches.GetMatch(i);
		MabDate match_date = match.GetMatch().GetDate();

		if( match_date.GetYear() == date.GetYear() &&
			match_date.GetMonth() == date.GetMonth() &&
			match_date.GetDayNumber() == date.GetDayNumber() )
		{
			matches.push_back( match );
		}
		// If we're past the date then we can return our vector
		else if( match_date > date )
		{
			return matches;
		}
	}

	return matches;
}

///------------------------------------------------------------------
/// Get a season matches accessor. (RL3_SEASON_MATCHES)
///------------------------------------------------------------------

RL3_SEASON_MATCHES RL3Tournament::GetSeasonMatches()
{
	RL3_SEASON_MATCHES matches(this);
	return matches;
}

///------------------------------------------------------------------
/// Returns the number of competitions in this season (Will always be 1 in tournament mode)
///------------------------------------------------------------------

int RL3Tournament::GetNumCompetitions() const
{
	return data.num_competitions;
}

///------------------------------------------------------------------
/// Returns the competition instance with the given index
///------------------------------------------------------------------

RL3DB_COMPETITION_INSTANCE RL3Tournament::GetCompetition( int comp_index ) const
{
	MABASSERTMSG(comp_index < DB_NUM_COMPETITION_INSTANCES_PER_FRANCHISE, MabString(0, "Trying to get comp ID %i, higher than %i", comp_index, DB_NUM_COMPETITION_INSTANCES_PER_FRANCHISE).c_str());
	RL3DB_COMPETITION_INSTANCE comp_inst(data.competition_ids[comp_index]);
	return comp_inst;
}

///------------------------------------------------------------------
/// Returns the competition instance with the given db id
///------------------------------------------------------------------

RL3DB_COMPETITION_INSTANCE RL3Tournament::GetCompetitionFromDBID( unsigned short comp_db_id ) const
{
	for(int i = 0; i < data.num_competitions; i++)
	{
		if( data.competition_ids[i] == DB_INVALID_ID ) continue;
		RL3DB_COMPETITION_INSTANCE comp_inst(data.competition_ids[i]);

		if( comp_inst.GetDbId() == DB_INVALID_ID ) continue;
		RL3DB_COMPETITION_DEFINITION comp_def = GetCompetitionDefinition( comp_inst.GetCompetitionId() );

		if(comp_def.GetDbId() == comp_db_id)
			return comp_inst;
	}

	return NULL;
}

///------------------------------------------------------------------
// Gets the competition definition from either our data or the database
///------------------------------------------------------------------

RL3DB_COMPETITION_DEFINITION RL3Tournament::GetCompetitionDefinition( int competition_definition_index ) const
{
	return database->GetCompetitionDefinition( competition_definition_index );
}

///------------------------------------------------------------------
/// Set last years nrl winner (RC2_TODO... Remove)
///------------------------------------------------------------------

void RL3Tournament::SetLastYearsNRLWinner( int team_db_id )
{
	data.last_years_nrl_winner_db_id = (unsigned short) team_db_id;
}

///------------------------------------------------------------------
/// Set last years sl winner (RC2_TODO... Remove)
///------------------------------------------------------------------

void RL3Tournament::SetLastYearsSLWinner( int team_db_id )
{
	data.last_years_sl_winner_db_id = (unsigned short) team_db_id;
}


///------------------------------------------------------------------
///------------------------------------------------------------------

void RL3Tournament::SimulateMatch( const bool match_in_progress, const bool partial_sim, const float partial_sim_perc, const RL3_SEASON_MATCH season_match, bool home_team_player_controlled, bool away_team_player_controlled )
{
	MAB_PROFILE_SECTION_START(profile0, "TSim(0)");

	//MABLOGDEBUG(MabString(0, "Simulating a match %s", (match_in_progress ? "still in progress" : "from the CareerHUB")).c_str());

	enum { HOME_TEAM, AWAY_TEAM };
	enum { STAR_PLAYERS, SUPER_STAR_PLAYERS, NORMAL_PLAYERS };

	// Used to sim current matches.
	SIFGameWorld* game = SIFApplication::GetApplication()->GetActiveGameWorld();
	SSGameTimer* timer			= game->GetGameTimer();
	int gameLength				= tournament_manager->GetGameLength();

	RL3DB_MATCH match = season_match.GetMatch();
	RL3DB_COMPETITION_INSTANCE competition_instance = GetCompetition( season_match.GetCompIndex() );
	RL3DB_COMPETITION_DEFINITION competition_definition = GetCompetitionDefinition( competition_instance.GetCompetitionId() );

	// Sort out our home/away teams based on how we're simming
	unsigned short home_team_db_id = match_in_progress ? game->GetTeam(HOME_TEAM)->GetDbTeam().GetDbId() : fixture_helper->GetTeamDbId( competition_instance, match.GetHomeTeam() );
	unsigned short away_team_db_id = match_in_progress ? game->GetTeam(AWAY_TEAM)->GetDbTeam().GetDbId() : fixture_helper->GetTeamDbId( competition_instance, match.GetAwayTeam() );
	RL3DB_TEAM home_team = database->GetTeam( home_team_db_id );
	RL3DB_TEAM away_team = database->GetTeam( away_team_db_id );
	RL3DB_TEAM current_team;

	int playersPerTeam = mGameSettings.game_limits.GetNumberOfPlayersPerTeam();
	int playersOnBench = mGameSettings.game_limits.GetNumberOfBenchPlayers();
	int playersOnTeamIncBench = playersPerTeam + playersOnBench;

	unsigned short team_ids[ 2 ] = { home_team_db_id, away_team_db_id };

//	MABLOGMSG(LOGCHANNEL_ALWAYS, LOGTYPE_INFO, "RL3Tournament::SimulateMatch comp: %d, round: %d, match: %d, date: %d/%d/%d\t%.02f",
//		season_match.GetCompIndex(), season_match.GetRoundNum(), season_match.GetMatchNum(),
//		match.GetDate().GetDayNumber(), match.GetDate().GetMonth(), match.GetDate().GetYear(), MabTime::GetCurrentTime() );


	// Allow draw if in prelim-rounds and more than two teams...
	//bool allow_draw = (competition_instance.GetCurrentRound() < competition_definition.GetNumPreliminaryRounds()) && competition_instance.GetNumTeams()>2;
	bool allow_draw = competition_instance.GetIsPrelimRound() && competition_instance.GetNumTeams()>2;

	// Store what our scores were before we simulated
	int existing_scores[2] = { 0, 0 };

	// If our match is still in progress, check the stats system for our score
	if(match_in_progress)
	{
		existing_scores[HOME_TEAM] = statistics->GetCurrentMatchStat(game->GetTeam(HOME_TEAM), &RUDB_STATS_TEAM::score);
		existing_scores[AWAY_TEAM] = statistics->GetCurrentMatchStat(game->GetTeam(AWAY_TEAM), &RUDB_STATS_TEAM::score);
	}

	// Debug our current team playing lineup, later on we need to access players to add stats, currently it just grabs ID's from the team lineup, but that could have changed during play (subs, injuries, penalties).
	MabVector<unsigned short> on_field_players[2];

	// Getting the lineup the way the in game squad editor does, since i want an up to date modified version of the lineup...
	const auto& home_lineup_array = mGameSettings.team_settings[HOME_TEAM].lineup;
	const auto& away_lineup_array = mGameSettings.team_settings[AWAY_TEAM].lineup;

	// Basically make an array of players that we are going to allow to get points from simulation
	for(int team_index = 0; team_index < 2; team_index++)
	{
		// When simming mid match, we only want players that are on field
		if(match_in_progress)
		{
			const auto& lineup = team_index == 0 ? home_lineup_array : away_lineup_array;
			for(int pp = 0; pp < playersOnTeamIncBench; pp ++)
			{
				const auto& db_player = lineup[pp];

				if(game->GetSubstitutionManager()->GetNumPlayerInSinbin(SSTEAMSIDE(team_index)) > 0)
				{
					if(game->GetSubstitutionManager()->IsPlayerInSinBin((int)db_player.GetDbId(), SSTEAMSIDE(team_index)))
						continue;
				}

				if(game->GetSubstitutionManager()->IsPlayerInjured((int)db_player.GetDbId(), team_index))
					continue;

				if(game->GetSubstitutionManager()->IsPlayerSentOff((int)db_player.GetDbId(), team_index))
					continue;

				// HES - when simulating we dont want the pro player to play anymore. Essentially benched.

#if defined ENABLE_GAME_DEBUG_MENU
				// For debugging we can turn off the restriction for not getting shit from simulations
				if(!SIFDebug::GetProModeDebugSettings()->GetEnableSimulationStats() &&
#else
				if (
#endif
					mGameSettings.game_settings.GetIsAProMode() &&
					SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(db_player.GetDbId()))
				{
					continue;
				}

				MABASSERT(db_player.GetDbId() != DB_INVALID_ID);
				on_field_players[team_index].push_back(db_player.GetDbId());

				// We don't use 'playersPerTeam' because we may have someone sent off at this stage resulting in an array not full enough...
				if((int)on_field_players[team_index].size() >= playersPerTeam)
					break;
			}
		}
		// Simming from the HUD, we take the starting lineup.
		else
		{
			current_team = team_index == 0 ? home_team : away_team;
			/*
			int num_players = MabMath::Min(current_team.GetNumPlayers(), playersOnTeamIncBench);

			for( int player_index = 0; player_index < num_players; player_index++ )
			{
				on_field_players[team_index].push_back(current_team.GetPlayer(player_index).index);
			}*/

			MabVector< unsigned short > starting_player_ids;
			current_team.GetStartingPlayersInPositionOrder( starting_player_ids, false );
			if ((int)starting_player_ids.size() > 0/*playersOnTeamIncBench*/)
			{
				for( MabVector< unsigned short >::const_iterator iter = starting_player_ids.begin(); iter != starting_player_ids.end(); ++iter )
				{
					// We cannot have the pro player getting simulated since they should be benched!
					//MABASSERT(!tournament_manager->IsProPlayer(*iter));
					on_field_players[team_index].push_back((*iter));
				}
			}
			else
			{
				MABASSERT((int)starting_player_ids.size() > 0);	// shouldn't really happen
				int num_players = MabMath::Min(current_team.GetNumPlayers(), playersOnTeamIncBench);
				for( int player_index = 0; player_index < num_players; player_index++ )
				{
					on_field_players[team_index].push_back(current_team.GetPlayer(player_index).index);
				}
			}
		}

		//on_field_players->clear();
		int num_players = MabMath::Min((int)on_field_players[team_index].size(), playersPerTeam);
		MABASSERT(num_players == playersPerTeam);
		if (num_players != playersPerTeam)
		{
			//Team doesn't have enough players.
			return;
		}
	}

	// DEBUGZ sanity check
	/*for(int team_index = 0; team_index < 2; team_index++)
	{
		MABLOGDEBUG("Players available for simming from team %s", (team_index == 0 ? game->GetTeam(HOME_TEAM)->GetDbTeam().GetName() : game->GetTeam(AWAY_TEAM)->GetDbTeam().GetName()));
		for(size_t pp = 0; pp < on_field_players[team_index].size(); pp ++)
		{
			RL3DB_PLAYER playa(on_field_players[team_index][pp]);
			MABLOGDEBUG("player(%i): %s %s", on_field_players[team_index][pp], playa.GetName().c_str(), (tournament_manager->IsProPlayer(on_field_players[team_index][pp]) ? "[PRO]" : ""));
		}
	}*/


	//-------------------------- Territory and Possession -----------------------------------

	// This is the total we already have (if post first half)
	int existing_possession_percentage[]	= { 0, 0 };
	int existing_territory_percentage[]		= { 0, 0 };

	// Only check the existing possession/territory if we're simming a match in progress
	if(match_in_progress)
	{
		float home_possession = game->GetStatsTracker()->GetBallPossession(SIDE_A)
			+ statistics->GetCurrentMatchStat(game->GetTeam(AWAY_TEAM), &RUDB_STATS_TEAM::possession);

		float away_possession = game->GetStatsTracker()->GetBallPossession( SIDE_B)
			+ statistics->GetCurrentMatchStat(game->GetTeam(HOME_TEAM), &RUDB_STATS_TEAM::possession);

		float total_possession = home_possession + away_possession;
		if(total_possession > 0.0f)
		{
			existing_possession_percentage[HOME_TEAM] = (int)((home_possession / total_possession) * 100.0f);
			existing_possession_percentage[AWAY_TEAM] = (int)((away_possession / total_possession) * 100.0f);
		}

		float home_territory = game->GetStatsTracker()->GetTerritory(SIDE_A)
			+ statistics->GetCurrentMatchStat(game->GetTeam(AWAY_TEAM), &RUDB_STATS_TEAM::territory);

		float away_territory = game->GetStatsTracker()->GetTerritory( SIDE_B)
			+ statistics->GetCurrentMatchStat(game->GetTeam(HOME_TEAM), &RUDB_STATS_TEAM::territory);

		float total_territory = home_territory + away_territory;
		if(total_territory > 0.0f)
		{
			existing_territory_percentage[HOME_TEAM] = (int)((home_territory / total_territory) * 100.0f);
			existing_territory_percentage[AWAY_TEAM] = (int)((away_territory / total_territory) * 100.0f);
		}

		// Work out what the possession/territory is for the current half in the game
		/*float pA = statistics->GetCurrentMatchStat(game->GetTeam(HOME_TEAM), &RUDB_STATS_TEAM::possession);
		float pB = statistics->GetCurrentMatchStat(game->GetTeam(AWAY_TEAM), &RUDB_STATS_TEAM::possession);
		float pTotal = pA + pB;
		if( pTotal >= 99.0f )
		{
			existing_possession_percentage[HOME_TEAM] = (int)((pA / pTotal) * 100.0f);
			existing_possession_percentage[AWAY_TEAM] = 100 - existing_possession_percentage[HOME_TEAM];
		}

		float tA = statistics->GetCurrentMatchStat(game->GetTeam(HOME_TEAM), &RUDB_STATS_TEAM::territory);
		float tB = statistics->GetCurrentMatchStat(game->GetTeam(AWAY_TEAM), &RUDB_STATS_TEAM::territory);
		float tTotal = tA + tB;
		if( tTotal >= 99.0f )
		{
			existing_territory_percentage[HOME_TEAM] = (int)((tA / tTotal) * 100.0f);
			existing_territory_percentage[AWAY_TEAM] = 100 - existing_territory_percentage[HOME_TEAM];
		}*/

		MABLOGDEBUG("Team A possession: %i, Team B possession: %i", existing_possession_percentage[HOME_TEAM], existing_possession_percentage[AWAY_TEAM]);
		MABLOGDEBUG("Team A territory: %i, Team B territory: %i", existing_territory_percentage[HOME_TEAM], existing_territory_percentage[AWAY_TEAM]);
	}

	//-----------------------------------------------------------------------------------------

	// Work out how much time of match is remaining, and scale back our max values that we'll use in the sim.
	float percMatchRemaining = 1.0f; // We will use this value to scale back all of our simmed values.
	if(match_in_progress)
	{
		// If we're doing a partial sim, we dont even have to worry about that!
		if(partial_sim)
		{
			percMatchRemaining = partial_sim_perc;
		}
		else
		{
			if(timer->GetIsInExtraTime())
			{
				MABBREAKMSG("We're simulating during extra time, we can skip most of the simulation since we only care about a single point being scored in any way possible");
			}


			int actualLengthInMins		= game->GetGameSettings().game_limits.GetActualGameLength();
			int actualHalfLengthInMins	= game->GetGameSettings().game_limits.GetActualHalfLengths();
			int actualLengthInSecs		= actualLengthInMins * 60;

			int elapsedMinsScaled		= timer->GetScaledMinutesElapsed();
			int elapsedSecsScaled		= timer->GetScaledSecondsElapsed();

			// if we're in over time, scaled the time back so that the elapsed perc is always at either 50% or 100%
			if(timer->GetIsInOverTime())
			{
				if(timer->GetCurrentHalf() == FIRST_HALF)
					elapsedMinsScaled = actualHalfLengthInMins;
				else
					elapsedMinsScaled = actualLengthInMins;

				elapsedSecsScaled = 0;
			}

			float elapsedPerc = (float)((elapsedMinsScaled * 60) + elapsedSecsScaled) / (float)actualLengthInSecs;
			percMatchRemaining -= elapsedPerc; // We will use this value to scale back all of our simmed values.
		}
	}

	// Setup our difficulty modifier. Higher makes the game easier
	float difficulty_modifier = 0.0f;
	DIFFICULTY diff = tournament_manager->IsInFranchise() ? tournament_manager->GetManagerDifficulty() : tournament_manager->GetGameDifficulty();
	switch ( diff )
	{
	case DIF_VERYEASY :	difficulty_modifier = 0.15f; break;
	case DIF_EASY :		difficulty_modifier = 0.0f; break;
	case DIF_NORMAL :	difficulty_modifier = -0.1f; break;
	case DIF_HARD :		difficulty_modifier = -0.16f; break;
	case DIF_PRO :
	default :			difficulty_modifier = -0.2f; break;
	}

	//float	max_total_attack[2] = { 0 };	// One for each team. We'll be increasing it by the maximum possible to keep track of what the total max will be
	//float	max_total_defence[2] = { 0 };	// One for each team. We'll be increasing it by the maximum possible to keep track of what the total max will be

	float	total_attack[2] = { 0, 0 };
	float	total_defence[2] = { 0, 0 };
	float	team_confidence[2] = { 0, 0 };
	float	team_attack_factor[2] = { 0, 0 };	// Range from 0.0f to 1.0f
	float	team_defence_factor[2] = { 0, 0 }; // Range from 0.0f to 1.0f


	// 2D array. First dimension = 1 for each team. 2nd dimension: star, super star, and normal players.
	// Basically this is storing all the players in each team based on if they are a star or not
	// Stores their db_indices
	MabVector< int > players[2][3];
	const int STAR_RATING = static_cast<int> ( 8.5f * MAX_PLAYER_ATTRIBUTE_VALUE );
	const int SUPER_STAR_RATING = static_cast<int> ( 9.2f * MAX_PLAYER_ATTRIBUTE_VALUE );

	//==========================================================================================================
	// ADD TOGETHER ALL THE PLAYERS STATS FOR EACH TEAM
	//==========================================================================================================

	for( int team_index = 0; team_index < 2; team_index++ )
	{
		current_team = team_index == 0 ? home_team : away_team;

		int num_players = MabMath::Min((int)on_field_players[team_index].size(), playersOnTeamIncBench);

		// Work out several things about the team
		for( int player_index = 0; player_index < num_players; player_index++ )
		{
			unsigned short player_db_id = on_field_players[team_index][player_index];
			rudb_player_row *player_data = (rudb_player_row*)database->GetPlayerCache()->GetRowStart( player_db_id );

			//RL3DB_PLAYER current_player = database->GetPlayer( current_team.GetPlayer(player_index).index );

			// Add all this players stats together
			int total_stats = player_data->acceleration +
				player_data->agility +
				player_data->aggressiveness +
				player_data->break_tackle_ability +
				player_data->catch_ability +
				player_data->fitness +
				player_data->general_kick_accuracy +
				player_data->goal_kick_accuracy +
				player_data->offload_ability +
				player_data->pass_accuracy +
				player_data->speed +
				player_data->tackle_ability;

			// See how good a player they are based on this and store them in our vector
			if ( total_stats >= SUPER_STAR_RATING )
			{
				players[team_index][SUPER_STAR_PLAYERS].push_back( player_db_id );
			}
			else if ( total_stats >= STAR_RATING )
			{
				players[team_index][STAR_PLAYERS].push_back( player_db_id );
			}
			else
			{
				players[team_index][NORMAL_PLAYERS].push_back( player_db_id );
			}

			// Add this players attacking stats to the teams total attacking stats
			total_attack[team_index] +=	player_data->break_tackle_ability +
				player_data->speed +
				player_data->acceleration +
				player_data->fitness +
				player_data->agility;

			// Max would be a player with max in all 5 attack stats
			//max_total_attack[team_index] += 5 * MAX_PLAYER_ATTRIBUTE_VALUE;

			// Add this players defending stats to the teams total defending stats
			total_defence[team_index] +=	player_data->tackle_ability +
				player_data->speed +
				player_data->acceleration +
				player_data->fitness +
				player_data->agility;

			// Max would be a player with max in all 5 defence stats
			//max_total_defence[team_index] += 5 * MAX_PLAYER_ATTRIBUTE_VALUE;
		}
		//MABLOGDEBUG( "%s (Before stars), Total Attack %0.2f, Total Defence %0.2f", current_team.GetName(), total_attack[team_index], total_defence[team_index] );
	}

	// Add extra attack and defence for star players
	for( int team_index = 0; team_index < 2; team_index++ )
	{
		current_team = team_index == 0 ? home_team : away_team;

		total_attack[team_index] += (players[team_index][SUPER_STAR_PLAYERS].size() * 3000.0f );
		total_attack[team_index] += (players[team_index][STAR_PLAYERS].size() * 1500.0f );

		// Max would be a team of all superstars
		//max_total_attack[team] += 3000 * (NUM_PLAYERS_PER_TEAM + NUM_BENCH_PLAYERS);

		total_defence[team_index] += (players[team_index][SUPER_STAR_PLAYERS].size() * 3000.0f );
		total_defence[team_index] += (players[team_index][STAR_PLAYERS].size() * 1500.0f );

		// Max would be a team of all superstars
		//max_total_defence[team] += 3000 * (NUM_PLAYERS_PER_TEAM + NUM_BENCH_PLAYERS);

		//MABLOGDEBUG( "%s (After stars), Total Attack %0.2f, Total Defence %0.2f", current_team.GetName(), total_attack[team_index], total_defence[team_index] );
	}


	//==========================================================================================================
	// START MODIFYING TEAMS STATS
	//==========================================================================================================

	// We need to copy these stats to make our calculations from so our modifiers are NOT cumulative
	float base_total_attack[2] = { total_attack[HOME_TEAM], total_attack[AWAY_TEAM] };
	float base_total_defence[2] = { total_defence[HOME_TEAM], total_defence[AWAY_TEAM] };

	//float base_max_attack[2] = { max_total_attack[HOME_TEAM], max_total_attack[AWAY_TEAM] };
	//float base_max_defence[2] = { max_total_defence[HOME_TEAM], max_total_defence[AWAY_TEAM] };

	//// Modify the home teams attack/defence
	//total_attack[HOME_TEAM]  += 0.08f * base_total_attack[HOME_TEAM];
	//total_defence[HOME_TEAM] += 0.08f * base_total_defence[HOME_TEAM];

	// The maximum bonus here is if you are the home team. Sounds harsh to apply this to the away team, but since
	// we're working with ratios, only adding it to the home teams max won't have any effect
	//for( int team_index = 0; team_index < 2; team_index++ )
	//{
	//max_total_attack[team] += 0.08f * base_max_attack[team];
	//max_total_defence[team] += 0.08f * base_max_defence[team];
	//}

	//==========================================================================================================
	// MAKE CHANGE BASED ON DIFFICULTY HERE

	//MABLOGDEBUG( "%s (Before), Total Attack %0.2f, Total Defence %0.2f", home_team.GetName(), total_attack[0], total_defence[0] );
	//MABLOGDEBUG( "%s (Before), Total Attack %0.2f, Total Defence %0.2f", away_team.GetName(), total_attack[1], total_defence[1] );

	// If one team is human and the other not, change the stats of the human team
	// Strangely enough we don't modify max_attack here. This is because for it to have an effect of difficulty,
	// you need the stats to go up if its easier. If you increase your max stats as well, this won't help!
	if ( home_team_player_controlled && !away_team_player_controlled )
	{
		total_attack[HOME_TEAM]	 += difficulty_modifier * base_total_attack[HOME_TEAM];
		total_defence[HOME_TEAM] += difficulty_modifier * base_total_defence[HOME_TEAM];
	}
	else if ( away_team_player_controlled && !home_team_player_controlled )
	{
		total_attack[AWAY_TEAM]	 += difficulty_modifier * base_total_attack[AWAY_TEAM];
		total_defence[AWAY_TEAM] += difficulty_modifier * base_total_defence[AWAY_TEAM];
	}

	MAB_PROFILE_SECTION_END(profile0);
	MAB_PROFILE_SECTION_START(profile1, "TSim(1)");

	//==========================================================================================================

	// Modify the total attack/defence based on the team attack/defence stat values
	// if their stat is > 60% of max, they get a boost. Otherwise nothing. Max stat will get 20% boost. 70% will get 5% boost

	float threshold = 0.6f;

	for( int team_index = 0; team_index < 2; team_index++ )
	{
		current_team = team_index == 0 ? home_team : away_team;

		if( current_team.GetNormalisedAttack() > threshold)
		{
			total_attack[ team_index ] += base_total_attack[team_index] * ( 0.2f - ( 0.2f * ( 1.0f - current_team.GetNormalisedAttack() ) / ( 1.0f - threshold ) ) ) ;
		}

		if( current_team.GetNormalisedDefence() > threshold)
		{
			total_defence[ team_index ] += base_total_defence[team_index] * ( 0.2f - ( 0.2f * ( 1.0f - current_team.GetNormalisedDefence() ) / ( 1.0f - threshold ) ) ) ;
		}

		// Max would be a 20% bonus
		//max_total_attack[team] += 0.2f * base_max_attack[team];
		//max_total_defence[team] += 0.2f * base_max_defence[team];

		//MABLOGDEBUG( "Team %s has %d superstars, %d stars and %d normal players", current_team->name, players[team][SUPER_STAR_PLAYERS].size(), players[team][STAR_PLAYERS].size(), players[team][NORMAL_PLAYERS].size() );
	}

	// We want to scale our values across these numbers
	// So a team with min_total_attack, will get an attack factor of ~0
	const int MAX_TOTAL_ATTACK = 1100000;
	const int MAX_TOTAL_DEFENCE = 1100000;
	const int MIN_TOTAL_ATTACK = (int) ( 0.3f * MAX_TOTAL_ATTACK );
	const int MIN_TOTAL_DEFENCE = (int) ( 0.3f * MAX_TOTAL_DEFENCE );

	// Confidence ranges from 0 -> 1.0f. If you have max confidence you will get a 30% boost to attack and defence.
	// If you have 0 confidence you will lose 30% attack and defence. If you have 0.5f confidence nothing happens
	team_confidence[0] = home_team.GetCompetitionConfidence(competition_instance.GetDbId(), competition_instance.GetCurrentSatellite());
	team_confidence[1] = away_team.GetCompetitionConfidence(competition_instance.GetDbId(), competition_instance.GetCurrentSatellite());
	for( int team_index = 0; team_index < 2; team_index++ )
	{
		current_team = team_index == 0? home_team : away_team;

		//MABLOGDEBUG( "%s, Total Attack %0.2f, Total Defence %0.2f", current_team->name, total_attack[j], total_defence[j] );
		//MABLOGDEBUG( "%s, Confidence %0.2f", current_team->name, team_confidence[j] );

		// Add confidence boost
		total_attack[team_index] += ((team_confidence[team_index] - 0.5f) * 0.60f ) * base_total_attack[team_index];
		// Max is 30%
		//max_total_attack[team_index] += 0.3f * base_max_attack[team_index];
		// Calculate team_attack factor
		team_attack_factor[team_index] = ( total_attack[team_index] - MIN_TOTAL_ATTACK ) / ( MAX_TOTAL_ATTACK - MIN_TOTAL_ATTACK );

		MabMath::Clamp( team_attack_factor[team_index], 0.0f, 1.0f );

		// Same for defence
		total_defence[team_index] += ((team_confidence[team_index] - 0.5f) * 0.60f ) * base_total_defence[team_index];;
		// Max is 30%
		//max_total_defence[team_index] += 0.3f * base_max_defence[team_index];
		// Calculate team_defence factor
		team_defence_factor[team_index] = ( total_defence[team_index] - MIN_TOTAL_DEFENCE ) / ( MAX_TOTAL_DEFENCE - MIN_TOTAL_DEFENCE );

		MabMath::Clamp( team_defence_factor[team_index], 0.0f, 1.0f );

		//MABLOGDEBUG( "%s (After), Total Attack %0.2f, Total Defence %0.2f", current_team.GetName(), total_attack[team_index], total_defence[team_index] );
	}

	//==========================================================================================================
	// DONE CALCULATING STATS, NOW WE'LL USE THEM TO SIMULATE THE MATCH!
	// Attack and defence factors are numbers from 0 -> 1 as a ratio of their total attack/defence out of the
	// maximum possible attack/defence. A perfect team will have a factor of 1.0f
	//==========================================================================================================

	// How much we want to scale the stats by (so that they'll match the player's games) CUT. Just make it like real life
	// float stats_scale = MabMath::Max( 1.0f, tournament_manager->GetGameLength() / 10.0f );

	// Scale the number of tries given based on game length
	int MAX_GIVEN_TRIES = 0;
	int MAX_PENALTIES = 0;
	int MAX_EXTRA_TRIES = 0;
	float FIELD_GOAL_CHANCE = 0.1f;

	// These are the max given tries depending on how long our match length is.
	// we should probably change these up depending on the game mode as well?
	switch ( gameLength )
	{
	case 5:		MAX_GIVEN_TRIES = 8;	MAX_EXTRA_TRIES = 3; MAX_PENALTIES = 2; break;
	default:
	case 80:	MAX_GIVEN_TRIES = 10;	MAX_EXTRA_TRIES = 5; MAX_PENALTIES = 4; break;

	}

	// Scale back our max values depending on how much time of the match is left over. We only want to sim these numbers, and then add them on top of what we already have.
	MAX_GIVEN_TRIES		= (int)(percMatchRemaining * (float)MAX_GIVEN_TRIES);
	MAX_EXTRA_TRIES		= (int)(percMatchRemaining * (float)MAX_EXTRA_TRIES);
	MAX_PENALTIES		= (int)(percMatchRemaining * (float)MAX_PENALTIES);
	FIELD_GOAL_CHANCE	= percMatchRemaining * FIELD_GOAL_CHANCE;

	int given_tries[2] = { 0, 0 };
	int n_possible_extra_tries[2] = { 0, 0 };

	// Give the teams some tries based on both teams attack and defense
	for (int team_index = 0; team_index < 2; team_index++)
	{
		// This formula takes the teams attack, and the opposite teams defence difference, and adds a half to it
		// It then multiplies it by the max number of tries possible
		// So to get the MAX number of tries, a team needs an attack/defence difference of >= 0.5
		// A team whose attack is lower than the opponents defence by 0.5 or more, will get zero tries (they might get somre more later though)
		given_tries[team_index] = MabMath::Round( ( 0.5f + team_attack_factor[team_index] - team_defence_factor[1 - team_index] ) * MAX_GIVEN_TRIES );
		MabMath::Clamp( given_tries[team_index], 0, MAX_GIVEN_TRIES );

		n_possible_extra_tries[team_index] = MabMath::Max(1, MabMath::Round( team_attack_factor[team_index] * MAX_EXTRA_TRIES ));

		// Scale the possible extra tries with time remaining.
		n_possible_extra_tries[team_index] = MabMath::Round((float)n_possible_extra_tries[team_index] * percMatchRemaining);
	}

	// Give more tries based on how much better each team is than the other team
	float dominance[2] = { 0, 0 };
	dominance[HOME_TEAM] = team_attack_factor[HOME_TEAM] - team_defence_factor[AWAY_TEAM];
	dominance[AWAY_TEAM] = team_attack_factor[AWAY_TEAM] - team_defence_factor[HOME_TEAM];

	// Put in some randomness for kicks. Add between -0.1 and 0.1f to our number
	dominance[HOME_TEAM] += ( MabMath::Rand(0.2f) - 0.1f );
	dominance[AWAY_TEAM] += ( MabMath::Rand(0.2f) - 0.1f );

	int n_tries[2] = { 0, 0 };
	for (int team_index = 0; team_index < 2; team_index++)
	{
		n_tries[team_index] = given_tries[team_index];

		n_tries[team_index] += MabMath::Max(0, MabMath::Round( dominance[team_index] * ((float)n_possible_extra_tries[team_index]) ));

		// Awesome random action. The team will get between 50 and 100% of the tries they've been allocated
		float try_modifier = 0.5f + MabMath::Rand( 0.5f );
		n_tries[team_index] = (int) ( n_tries[team_index] * try_modifier );

		// Keep some balancing in here. Each team has a 24% chance of getting at least 1 try if they don't have any yet
		if (n_tries[team_index] < 1 && MabMath::Rand(1.0f) < (0.2f * percMatchRemaining))
		{
			n_tries[team_index]++;
		}
		// And a 4% chance of getting 2 if they had none, or 20% chance of getting 2 if they had 1
		if (n_tries[team_index] < 2 && MabMath::Rand(1.0f) < (0.2f * percMatchRemaining))
		{
			n_tries[team_index]++;
		}

		// Going to comment out stopping of higher scores. They're coming out too close
		//if (n_tries[team] > 10 && MabMath::Rand(1.0f) < 0.8f)
		//{
		//	n_tries[team]--;
		//}
		//if (n_tries[team] > 8 && MabMath::Rand(1.0f) < 0.7f)
		//{
		//	n_tries[team]--;
		//}
	}

#ifdef ENABLE_GAME_DEBUG_MENU
	if(SIFDebug::GetCareerModeDebugSettings()->GetWinAllMatchesEnabled())
	{
		if ( home_team_player_controlled || away_team_player_controlled )
		{
			// ensure that the human team scored at least three more tries than the computer team
			int human_index = home_team_player_controlled ? HOME_TEAM : AWAY_TEAM;
			MabMath::ClampLower( n_tries[human_index], 3 );
			MabMath::ClampUpper( n_tries[1-human_index], n_tries[human_index] - 3 );
		}
	}
	else if(SIFDebug::GetCareerModeDebugSettings()->GetLoseAllMatchesEnabled())
	{
		if ( home_team_player_controlled || away_team_player_controlled )
		{
			// ensure that the computer scored more tries
			int other_index = home_team_player_controlled ? AWAY_TEAM : HOME_TEAM;
			MabMath::ClampLower( n_tries[other_index], 3 );
			MabMath::ClampUpper( n_tries[1-other_index], n_tries[other_index] - 3 );
		}
	}
#endif


#if 0	// RC2_TODO...
	if (
		( data.tournament_mode == TM_FRANCHISE && SIFApplication::GetApplication()->GetUnlockableManager()->IsUnlocked( "RL3_CHEAT_WIN_ALL_SIMULATED_MATCH" ) )
#ifdef ENABLE_TOURNAMENT_DEBUG_MENU
		|| ( GetWinAllSimulatedMatches() )
#endif
		)
	{
		// the win every sim match cheat is on, and this is a franchise (not competition)
		if ( home_team_player_controlled || away_team_player_controlled )
		{
			// ensure that the human team scored at least three more tries than the computer team
			int human_index = home_team_player_controlled ? HOME_TEAM : AWAY_TEAM;
			MabMath::ClampLower( n_tries[human_index], 3 );
			MabMath::ClampUpper( n_tries[1-human_index], n_tries[human_index] - 3 );
		}
	}
#endif
	// These are team relative indices, for each team in the match.
	int goal_kicker_index[NUM_TEAMS_INIT] = { -1, -1 };
	int play_kicker_index[NUM_TEAMS_INIT] = { -1, -1 };


	const int NUM_GOAL_KICKERS = 1;			// RC2: Was 2 in rl3.
	const int NUM_PLAY_KICKERS = 1;

	// Find the teams goal kickers and play kickers. If the player is not in the starting lineup, then choose the player
	// with the highest kick_accuracy instead

	const int numTeams = mGameSettings.game_limits.GetNumberOfTeams();

	for( int team_index = 0; team_index < numTeams /*NUM_TEAMS*/; team_index++ )
	{
		current_team = team_index == 0 ? home_team : away_team;

		int num_players = MabMath::Min((int)on_field_players[team_index].size(), playersPerTeam);
		MABASSERT(num_players == playersPerTeam/*NUM_PLAYERS_PER_TEAM*/);

		// 20% chance of using the secondary goal kicker for this match (disabled for RC2 , as only one)
		bool use_secondary_goal_kicker = false;		//MabMath::Rand( 1.0f ) <= 0.2f;

		// Find the goal kicker for the team
		goal_kicker_index[team_index] = -1;

		//////////ARH - This does nothing because of the  if ( goal_kicker_id >= 0 && goal_kicker_id < DB_INVALID_ID ) always being false.
		//So I guess the goals are always assigned to guy with highest kicking stat instead of kicker?

		//for( int kicker_index = 0; kicker_index < NUM_GOAL_KICKERS; kicker_index++ )
		//{
		//	int goal_kicker_to_use = use_secondary_goal_kicker ? 1 - kicker_index : kicker_index;
		//	int goal_kicker_id = current_team.GetGoalKicker(goal_kicker_to_use);

		//	if ( goal_kicker_id >= 0 && goal_kicker_id < DB_INVALID_ID )
		//	{
		//		// Try and find this kicker in the starting lineup
		//		for( int player_index = 0; player_index < num_players; player_index++ )
		//		{
		//			if ( on_field_players[team_index][player_index] == goal_kicker_id )
		//			{
		//				goal_kicker_index[team_index] = player_index;
		//				break;
		//			}
		//		}
		//		if ( goal_kicker_index[team_index] != -1 )
		//			break;
		//	}
		//}
		/////////////////

		if ( goal_kicker_index[team_index] == -1 )
		{
			int max_kick_ability = -1;
			for( int player_index = 0; player_index < num_players; player_index++ )
			{
				RL3DB_PLAYER current_player(on_field_players[team_index][player_index]);
				unsigned short goal_kick_accuracy = current_player.GetGoalKickAccuracy();
				if ( goal_kick_accuracy > max_kick_ability )
				{
					goal_kicker_index[team_index] = player_index;
					max_kick_ability = goal_kick_accuracy;
				}
			}
		}

		// 35% chance of using the secondary play kicker for this match (disabled for RC2, as only one kicker)
		bool use_secondary_play_kicker = false;			/// MabMath::Rand( 1.0f ) <= 0.35f;

		// Find the play kicker for the team
		play_kicker_index[team_index] = -1;
		for( int player = 0; player < NUM_PLAY_KICKERS; player++ )
		{
			int play_kicker_to_use = use_secondary_play_kicker ? 1 - player : player;

			unsigned short player_kicker_id = current_team.GetPlayKicker(play_kicker_to_use);

			if ( player_kicker_id != DB_INVALID_ID )
			{
				// Try and find this kicker in the starting lineup
				for( int player_index = 0; player_index < num_players; player_index++ )
				{
					if ( on_field_players[team_index][player_index] == player_kicker_id )
					{
						play_kicker_index[team_index] = player_index;
						break;
					}
				}
				if ( play_kicker_index[team_index] != -1 )
					break;
			}
		}

		if ( play_kicker_index[team_index] == -1 )
		{
			int max_kick_ability = -1;
			for( int player_index = 0; player_index < num_players; player_index++ )
			{
				RL3DB_PLAYER current_player(on_field_players[team_index][player_index]);
				unsigned short kick_accuracy = current_player.GetGeneralKickAccuracy();
				if ( kick_accuracy > max_kick_ability )
				{
					play_kicker_index[team_index] = player_index;
					max_kick_ability = kick_accuracy;
				}
			}
		}

	}

	// Work out the conversions
	int n_conversions[2] = { 0, 0 };
	float goal_kicker_perc[2] = { 0, 0 };
	for( int team_index = 0; team_index < 2; team_index++ )
	{
		current_team = team_index == 0 ? home_team : away_team;

		if (goal_kicker_index[team_index] < 0 || ((int)(on_field_players[team_index].size()) < (int)(goal_kicker_index[team_index] + 1)))
		{
			MABASSERT((int)(on_field_players[team_index].size()) >= (int)(goal_kicker_index[team_index] + 1));
			continue;
		}
		RL3DB_PLAYER current_player(on_field_players[team_index][goal_kicker_index[team_index]]);
		float kicker_rating = ( (float) current_player.GetGoalKickAccuracy() ) / MAX_PLAYER_ATTRIBUTE_VALUE;
		// Kicker will be kicking between 70% and 100% of their kicking stat value
		goal_kicker_perc[team_index] = kicker_rating * ( 0.7f + MabMath::Rand( 0.3f ) );
		MabMath::Clamp( goal_kicker_perc[team_index], 0.0f, 1.0f );
		n_conversions[team_index] = MabMath::Round( goal_kicker_perc[team_index] * ((float) n_tries[team_index] ) );
	}

	// Work out the penalty kicks
	int n_penalty_kicks[2] = { 0, 0 };
	int n_successful_penalty_kicks[2] = { 0, 0 };
	for( int team_index = 0; team_index < 2; team_index++ )
	{
		n_penalty_kicks[team_index] = MabMath::RandInt( MAX_PENALTIES + 1 );

		n_successful_penalty_kicks[team_index] = MabMath::Round( goal_kicker_perc[team_index] * ((float) n_penalty_kicks[team_index] ) );
	}

	// work out the drop goals (only if the scores are really close)
	int current_scores[2] = { 0, 0 };

	const int conversion_score = mGameSettings.GetConversionScore();
	const int penalty_score = mGameSettings.GetPenaltyScore();
	const int drop_goal_score = mGameSettings.GetDropGoalScore(false);
	const int try_score = mGameSettings.GetTryScore();

	current_scores[HOME_TEAM] = existing_scores[HOME_TEAM] + n_tries[HOME_TEAM] * try_score + n_conversions[HOME_TEAM] * conversion_score + n_successful_penalty_kicks[HOME_TEAM] * penalty_score;
	current_scores[AWAY_TEAM] = existing_scores[AWAY_TEAM] + n_tries[AWAY_TEAM] * try_score + n_conversions[AWAY_TEAM] * conversion_score + n_successful_penalty_kicks[AWAY_TEAM] * penalty_score;
	int points_difference = current_scores[HOME_TEAM] - current_scores[AWAY_TEAM];

	int n_drop_goals[2] = { 0, 0 };

	// Throw in the occasional field goal (we're not getting enough over the course of a franchise)
	// However we don't want weird scores like 54-1, so only give the losing team a chance if they're losing by <= 8
	for( int team_index = 0; team_index < 2; ++team_index )
	{
		if( MabMath::Abs( points_difference ) <= 8 || current_scores[ team_index ] > current_scores[ 1 - team_index ] )
		{
			if( MabMath::Rand( 1.0f ) < FIELD_GOAL_CHANCE )
			{
				++n_drop_goals[team_index];
			}
		}
	}

	current_scores[HOME_TEAM] = n_tries[HOME_TEAM] * try_score + n_conversions[HOME_TEAM] * conversion_score + n_successful_penalty_kicks[HOME_TEAM] * penalty_score + n_drop_goals[HOME_TEAM] * drop_goal_score;
	current_scores[AWAY_TEAM] = n_tries[AWAY_TEAM] * try_score + n_conversions[AWAY_TEAM] * conversion_score + n_successful_penalty_kicks[AWAY_TEAM] * penalty_score + n_drop_goals[AWAY_TEAM] * drop_goal_score;
	points_difference = current_scores[HOME_TEAM] - current_scores[AWAY_TEAM];

	if ( MabMath::Abs(points_difference) == 6 )
	{
		int drop_goal_team = current_scores[HOME_TEAM] > current_scores[AWAY_TEAM] ? HOME_TEAM : AWAY_TEAM;

		// 20% chance of a field goal by the winning team if they're 6 ahead
		if ( MabMath::Rand( 1.0f ) < 0.2f ) n_drop_goals[drop_goal_team]++;

	}
	else if( MabMath::Abs(points_difference) == 0 )
	{
		int drop_goal_team;
		// 70% chance of the home team winning a draw
		if( MabMath::Rand( 1.0f ) < 0.7f )
		{
			drop_goal_team = HOME_TEAM;
		}
		else
		{
			drop_goal_team = AWAY_TEAM;
		}

		// 65% chance they actually get a field goal
		if ( MabMath::Rand( 1.0f ) < 0.65f ) n_drop_goals[drop_goal_team]++;

	}

	current_scores[HOME_TEAM] = n_tries[HOME_TEAM] * try_score + n_conversions[HOME_TEAM] * conversion_score + n_successful_penalty_kicks[HOME_TEAM] * penalty_score + n_drop_goals[HOME_TEAM] * drop_goal_score + existing_scores[HOME_TEAM];
	current_scores[AWAY_TEAM] = n_tries[AWAY_TEAM] * try_score + n_conversions[AWAY_TEAM] * conversion_score + n_successful_penalty_kicks[AWAY_TEAM] * penalty_score + n_drop_goals[AWAY_TEAM] * drop_goal_score + existing_scores[AWAY_TEAM];
	points_difference = current_scores[HOME_TEAM] - current_scores[AWAY_TEAM];

	// If it's still a draw
	if (points_difference == 0 && !allow_draw)
	{
		// TODO: Replace with OT regulations for pools/league versus knockout.
		//// 5% chance of it ending in a draw if its limited golden point
		//if ( competition_helper->GetGoldenPointType( competition_definition, competition_instance ) == GOLDEN_POINT_LIMITED )
		//{
		//	if( MabMath::Rand( 1.0f ) < 0.95f )
		//	{
		//		int drop_goal_team;
		//		if( MabMath::Rand( 1.0f ) < 0.7f )
		//		{
		//			drop_goal_team = HOME_TEAM;
		//		}
		//		else
		//		{
		//			drop_goal_team = AWAY_TEAM;
		//		}

		//		n_drop_goals[drop_goal_team]++;

		//	}
		//}
		//// Unlimited must have a winner
		//else if ( competition_helper->GetGoldenPointType( competition_definition, competition_instance ) == GOLDEN_POINT_UNLIMITED )
		{
			int winning_team;
			if( MabMath::Rand( 1.0f ) < 0.7f )
			{
				winning_team = HOME_TEAM;
			}
			else
			{
				winning_team = AWAY_TEAM;
			}

			if( MabMath::Rand( 1.0f ) < 0.3f )
			{
				n_tries[winning_team]++;
			}
			else
			{
				n_drop_goals[winning_team]++;
			}

		}
	}

	//|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	// MORE BALANCING
	// We have decided that there are too many higher end scores.
	// To combat this, if BOTH teams score >= 24 points, we will subtract a max of 12 points from each team.
	// We will always subtract the same amount from both teams.
	// 50% chance of this occurring
	// It could be possible that the 50% chance occurs, but the way the scores work out means that we can't subtract any points fairly and easily
	//|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||

	int home_team_score = (n_tries[HOME_TEAM] * try_score + n_conversions[HOME_TEAM] * conversion_score + n_successful_penalty_kicks[HOME_TEAM] * penalty_score + n_drop_goals[HOME_TEAM] * drop_goal_score);
	int away_team_score = (n_tries[AWAY_TEAM] * try_score + n_conversions[AWAY_TEAM] * conversion_score + n_successful_penalty_kicks[AWAY_TEAM] * penalty_score + n_drop_goals[AWAY_TEAM] * drop_goal_score);
	int ceilingScore = 20;

	// There's generally a lot more scoring during sevens games, so we'll increase the ceiling a bit more
	// Nick WWS 7s to Womens 13s //
	//if(game->GetGameSettings().game_settings.GameModeIsR7())
	//	ceilingScore = 35;

	// Careful, we probably dont want to do this while the match is in progress. Otherwise we might scale back some scores that have already been achieved.
	if( !match_in_progress && home_team_score >= ceilingScore && away_team_score >= ceilingScore && MabMath::Rand( 1.0f ) < 0.6f )
	{
		bool change_made = false;

		// Ideally remove a converted try and 3 penalties
		if( n_tries[HOME_TEAM] >= 1 && n_tries[AWAY_TEAM] >= 1 )
		{
			if( n_conversions[HOME_TEAM] >= 1 && n_conversions[AWAY_TEAM] >= 1 )
			{
				if( n_successful_penalty_kicks[HOME_TEAM] >= 3 && n_successful_penalty_kicks[AWAY_TEAM] >= 3 )
				{
					n_tries[HOME_TEAM] -= 1;
					n_tries[AWAY_TEAM] -= 1;
					n_conversions[HOME_TEAM] -= 1;
					n_conversions[AWAY_TEAM] -= 1;
					n_successful_penalty_kicks[HOME_TEAM] -= 3;
					n_successful_penalty_kicks[AWAY_TEAM] -= 3;
					change_made = true;
				}
			}
		}

		// If we haven't succeeded, try to remove 2 converted tries
		if( !change_made && n_tries[HOME_TEAM] >= 2 && n_tries[AWAY_TEAM] >= 2 )
		{
			if( n_conversions[HOME_TEAM] >= 2 && n_conversions[AWAY_TEAM] >= 2 )
			{
				n_tries[HOME_TEAM] -= 2;
				n_tries[AWAY_TEAM] -= 2;
				n_conversions[HOME_TEAM] -= 2;
				n_conversions[AWAY_TEAM] -= 2;
				change_made = true;
			}

		}

		// If we STILL haven't succeeded we'll remove a bunch of penalties instead.
		if( !change_made && n_successful_penalty_kicks[HOME_TEAM] >= 8 && n_successful_penalty_kicks[AWAY_TEAM] >= 8 )
		{
			n_successful_penalty_kicks[HOME_TEAM] -= 6;
			n_successful_penalty_kicks[AWAY_TEAM] -= 6;
			change_made = true;
		}

		// How about just a converted try each?
		if( !change_made && n_tries[HOME_TEAM] >= 1 && n_tries[AWAY_TEAM] >= 1 )
		{
			if( n_conversions[HOME_TEAM] >= 1 && n_conversions[AWAY_TEAM] >= 1 )
			{
				n_tries[HOME_TEAM] -= 1;
				n_tries[AWAY_TEAM] -= 1;
				n_conversions[HOME_TEAM] -= 1;
				n_conversions[AWAY_TEAM] -= 1;
				change_made = true;
			}

		}

		// Srsly? Still nothing? Fine. Just remove a try each, surely we can do THAT
		if( !change_made && n_tries[HOME_TEAM] >= 1 && n_tries[AWAY_TEAM] >= 1 )
		{
			n_tries[HOME_TEAM] -= 1;
			n_tries[AWAY_TEAM] -= 1;
			change_made = true;
		}

		// Otherwise nevermind. Give up.

	}


	//=================================================================================
	// Add all of the necessary scores etc... for both teams
	//=================================================================================

	// RC2 version...
	// Create new  game stats if we're not in a match in progress
	if (!match_in_progress) 
	{
		statistics->NewGame(database->GetTeam(team_ids[HOME_TEAM]), database->GetTeam(team_ids[AWAY_TEAM]), competition_instance.GetDbId(), match.GetDbId());
	}

	// This is the second half, 0 would be the first
	// TODO:
	//statistics->SetHalf( 1 );
	// set the teams
	// TODO:
	//statistics->SetTeams( team_ids[HOME_TEAM], team_ids[AWAY_TEAM], 80 );	// Stats are based on an 80 minute game

	short team_scores[2] = { 0, 0 };
	SSTEAMSIDE team_sides[2] = { SIDE_A, SIDE_B };

	for( int team_index = 0; team_index < 2; team_index++ )
	{
		current_team = database->GetTeam( team_ids[team_index] );
		SSTEAMSIDE team_id = team_sides[team_index];
		SSTEAMSIDE opposing_team_id = team_sides[(team_index+1)&1];

		// Add tries and conversions
		for( int i = 0; i < n_tries[team_index]; i++ )
		{
			// Pick a random player to score the try
			playersPerTeam = (int)on_field_players[team_index].size();
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			const unsigned short player_id = on_field_players[team_index][player_index];

			statistics->AddTry(team_id, opposing_team_id, player_id, true);
			team_scores[team_index] += try_score;
			if ( i < n_conversions[team_index] )
			{
				statistics->AddConversion( team_id, opposing_team_id, on_field_players[team_index][goal_kicker_index[team_index]], true );
				team_scores[team_index] += (short)conversion_score;
			}
			else
			{
				statistics->AddConversion( team_id, opposing_team_id, on_field_players[team_index][goal_kicker_index[team_index]], false );
			}
		}

		// Add penalties
		for( int i = 0; i < n_penalty_kicks[team_index]; i++ )
		{
			if ( i < n_successful_penalty_kicks[team_index] )
			{
				statistics->AddPenaltyGoal(team_id, opposing_team_id, on_field_players[team_index][goal_kicker_index[team_index]], true );
				team_scores[team_index] += (short)penalty_score;
			}
			else
			{
				statistics->AddPenaltyGoal(team_id, opposing_team_id, on_field_players[team_index][goal_kicker_index[team_index]], false );
			}
		}

		// Add drop goals
		for( int i = 0; i < n_drop_goals[team_index]; i++ )
		{
			unsigned short kicker_id;
			if ( MabMath::Rand( 1.0f ) < 0.7f )
			{
				kicker_id = on_field_players[team_index][goal_kicker_index[team_index]];
			}
			else
			{
				kicker_id = on_field_players[team_index][play_kicker_index[team_index]];
			}
			statistics->AddDropGoal(team_id, opposing_team_id, kicker_id, true);
			team_scores[team_index] += (short)drop_goal_score;
		}
	}

	season_match.GetMatch().SetResult( team_scores[ 0 ], team_scores[ 1 ]);

	//MABLOGDEBUG( "HAttack: %f ADefence: %f HTries: %d HConv: %d HPen: %d HFg: %d Total: %d", team_attack_factor[0], team_defence_factor[1], n_tries[0], n_conversions[0], n_successful_penalty_kicks[0], n_drop_goals[0], team_scores[0] );
	//MABLOGDEBUG( "AAttack: %f HDefence: %f ATries: %d AConv: %d APen: %d AFg: %d Total: %d", team_attack_factor[1], team_defence_factor[0], n_tries[1], n_conversions[1], n_successful_penalty_kicks[1], n_drop_goals[1], team_scores[1] );

	//------------------------------------------------------------
	// Generate other game stats
	//------------------------------------------------------------

	int n_penalties[2] = {0,0};
	//int n_six_tackle_sets[2] = {0,0};
	//int n_six_tackle_sets_completed[2] = {0,0};
	int n_tackle_attempts[2] = {0,0};
	int n_successful_tackle_attempts[2] = {0,0};
	int n_hitups[2] = {0,0};
	int n_scrums[2] = {0,0};
	int n_line_breaks[2] = {0,0};
	int n_offloads[2] = {0,0};
	int n_handling_errors[2] = {0,0};
	int n_kicks[2] = {0,0};
	//int n_dummy_half_runs[2] = {0,0};
	int n_yellow_cards[2] = {0,0};
	int n_red_cards[2] = {0,0};
	int n_injuries[2] = {0,0};
	int possession_percentage[2] = {0,0};
	int territory_percentage[2] = {0,0};

	int n_breakdowns[2] = {0,0};
	int n_breakdowns_turnover[2] = {0,0};
	int n_lineouts[2] = {0,0};
	int n_lineouts_stolen[2] = {0,0};

	RL3TournamentConstants* tournament_constants = tournament_manager->GetTournamentConstants();

	int sim_min_penalties					= (int)((float)tournament_constants->GetSimMinPenalties(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_penalties					= (int)((float)tournament_constants->GetSimMaxPenalties(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	int sim_min_tackles						= (int)((float)tournament_constants->GetSimMinTackles(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_tackles						= (int)((float)tournament_constants->GetSimMaxTackles(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	float sim_successful_tackles_variance	= tournament_constants->GetSimSuccessfulTacklesVariance(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining;

	int sim_min_hitups						= (int)((float)tournament_constants->GetSimMinHitups(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_hitups						= (int)((float)tournament_constants->GetSimMaxHitups(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	int sim_min_line_breaks					= (int)((float)tournament_constants->GetSimMinLineBreaks(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_line_breaks					= (int)((float)tournament_constants->GetSimMaxLineBreaks(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	int sim_min_offloads					= (int)((float)tournament_constants->GetSimMinOffloads(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_offloads					= (int)((float)tournament_constants->GetSimMaxOffloads(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	int sim_min_handling_errors				= (int)((float)tournament_constants->GetSimMinHandlingErrors(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_handling_errors				= (int)((float)tournament_constants->GetSimMaxHandlingErrors(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	int sim_min_kicks						= (int)((float)tournament_constants->GetSimMinKicks(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_kicks						= (int)((float)tournament_constants->GetSimMaxKicks(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	float sim_yellow_card_chance			= tournament_constants->GetSimYellowCardChance(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining;
	float sim_red_card_chance				= tournament_constants->GetSimRedCardChance(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining;

	float sim_first_injury_chance			= tournament_constants->GetSimFirstInjuryChance(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining;
	float sim_second_injury_chance			= tournament_constants->GetSimSecondInjuryChance(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining;

	int	sim_min_run_metres_gained			= (int)((float)tournament_constants->GetSimMinRunMetresGained(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_run_metres_gained			= (int)((float)tournament_constants->GetSimMaxRunMetresGained(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	int sim_min_kick_metres_gained_per_kick = (int)((float)tournament_constants->GetSimMinKickMetresGainedPerKick(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_kick_metres_gained_per_kick = (int)((float)tournament_constants->GetSimMaxKickMetresGainedPerKick(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);


	float sim_breakdown_chance				= tournament_constants->GetBreakdownChance(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining;
	float sim_breakdown_turnover_chance		= tournament_constants->GetBreakdownTurnoverChance(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining;

	int sim_min_lineouts					= (int)((float)tournament_constants->GetSimMinLineouts(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_lineouts					= (int)((float)tournament_constants->GetSimMaxLineouts(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

#ifdef ENABLE_GAME_DEBUG_MENU
	if(SIFDebug::GetCareerModeDebugSettings()->GetNoSimulationCards())
	{
		sim_yellow_card_chance = 0.0f;
		sim_red_card_chance = 0.0f;
	}
	if(SIFDebug::GetCareerModeDebugSettings()->GetNoSimulationInjuries())
	{
		sim_first_injury_chance = 0.0f;
		sim_second_injury_chance = 0.0f;
	}
#endif

	/// Enable/Disable injuries/suspension depending on custom game settings.

	if(competition_definition.GetIsFriendly())
	{
		sim_first_injury_chance = 0.0f;
		sim_second_injury_chance = 0.0f;
		sim_yellow_card_chance = 0.0f;
		sim_red_card_chance = 0.0f;
	}
	else
	{
		if (!mGameSettings.game_settings.custom_rule_injuries)
		{
			sim_first_injury_chance = 0.0f;
			sim_second_injury_chance = 0.0f;
		}

		if(!mGameSettings.game_settings.custom_rule_sendoffs_enabled)
		{
			sim_red_card_chance = 0.0f;
		}

		if(!mGameSettings.game_settings.custom_rule_sinbins_enabled)
		{
			sim_yellow_card_chance = 0.0f;
		}
	}


	// Calculate the possession and territory
	int first_choice_percentage_team = MabMath::RandInt( 2 );
	int other_team = 1 - first_choice_percentage_team;

	possession_percentage[first_choice_percentage_team] = 40 + (int)( MabMath::RandInt(40) * team_attack_factor[first_choice_percentage_team] );
	territory_percentage[first_choice_percentage_team] = 40 + (int)( MabMath::RandInt(40) * team_attack_factor[first_choice_percentage_team] );

	// If we sim a match in progress, we need to add on the existing percentages and scale
	if(match_in_progress)
	{
		// We should already have some stats for the game we're playing. Add it on to the simmed stats.
		possession_percentage[first_choice_percentage_team]		+= existing_possession_percentage[first_choice_percentage_team];
		territory_percentage[first_choice_percentage_team]		+= existing_territory_percentage[first_choice_percentage_team];

		// Scale it back since we added another 100%
		possession_percentage[first_choice_percentage_team]		= (int)((float)possession_percentage[first_choice_percentage_team] / 2.0f);
		territory_percentage[first_choice_percentage_team]		= (int)((float)territory_percentage[first_choice_percentage_team]  / 2.0f);
	}

	possession_percentage[other_team] = 100 - possession_percentage[first_choice_percentage_team];
	territory_percentage[other_team] = 100 - territory_percentage[first_choice_percentage_team];

	for( int team_index = 0; team_index < 2; team_index++ )
	{
		// Generate the team totals for everything
		n_penalties[team_index] = sim_min_penalties + MabMath::RandInt( sim_max_penalties - sim_min_penalties + 1 );
		//n_six_tackle_sets[team_index] = sim_min_six_tackle_sets + MabMath::RandInt( sim_max_six_tackle_sets - sim_min_six_tackle_sets + 1 );
		//n_six_tackle_sets_completed[team_index] = MabMath::RandInt( n_six_tackle_sets[team_index] + 1 );
		//MabMath::ClampLower( n_six_tackle_sets_completed[team_index], (int)(n_six_tackle_sets[team_index] * 0.2f) );
	}

	for( int team_index = 0; team_index < 2; team_index++ )
	{
		// Tackles
		n_successful_tackle_attempts[team_index] = sim_min_tackles + MabMath::RandInt( sim_max_tackles - sim_min_tackles + 1 );
		// Make sure we don't get less tackles than possible with the oppositions 6 tackle sets. Add 20 for good measure =D
		//MabMath::ClampLower( n_successful_tackle_attempts[team_index], ( n_six_tackle_sets[1-team_index] * 6 ) + 20 );
		// Now generate the number of attempted tackles from this
		n_tackle_attempts[team_index] =
			// Add to the number of tackles we succeeded in
			n_successful_tackle_attempts[team_index] +
			(int)
			(
			// We're taking the inverse of the defence factor to see how many they missed
			( ( 1 - team_defence_factor[team_index] ) +
			( MabMath::Rand( sim_successful_tackles_variance ) - sim_successful_tackles_variance / 2.0f )
			// Divide by 3 cos we don't want too many missed tackles
			) / 3.0f *
			n_successful_tackle_attempts[team_index]
		);

		n_breakdowns[team_index]			= (int)((float)n_successful_tackle_attempts[team_index] * sim_breakdown_chance);
		n_breakdowns_turnover[team_index]	= (int)((float)n_breakdowns[team_index]* sim_breakdown_turnover_chance);

		// Number of lineouts this team took
		n_lineouts[team_index]				= sim_min_lineouts + MabMath::RandInt( sim_max_lineouts - sim_min_lineouts + 1 );

		// Number of those linout throws that got stolen
		// Let's say 20% can get stolen, but scale that depending on the inverse of the teams defending factor...
		n_lineouts_stolen[team_index]		= (int)((float)n_lineouts[team_index] * (0.2f * (1.0f - team_defence_factor[team_index])));

		// Hitups
		n_hitups[team_index] = sim_min_hitups + (int)(team_attack_factor[team_index] * MabMath::RandInt( sim_max_hitups - sim_min_hitups + 1 ) );

		// TODO: make something up for this
		//n_scrums[team_index] = MabMath::RandInt( n_six_tackle_sets_completed[team_index] );

		n_line_breaks[team_index] = sim_min_line_breaks + (int)(team_attack_factor[team_index] * MabMath::RandInt( sim_max_line_breaks - sim_min_line_breaks + 1 ) );

		n_offloads[team_index] = sim_min_offloads + (int)(team_attack_factor[team_index] * MabMath::RandInt( sim_max_offloads - sim_min_offloads + 1 ) );

		n_handling_errors[team_index] = sim_min_handling_errors + (int)( ( 1 - team_attack_factor[team_index] ) * MabMath::RandInt( sim_max_handling_errors - sim_min_handling_errors + 1 ) );

		n_kicks[team_index] = sim_min_kicks + (int)(team_attack_factor[team_index] * (1 - team_defence_factor[1 - team_index]) * MabMath::RandInt( sim_max_kicks - sim_min_kicks + 1 ) );

		//n_dummy_half_runs[team_index] = sim_min_dummy_runs + (int)(team_attack_factor[team_index] * MabMath::RandInt( sim_max_dummy_runs - sim_min_dummy_runs + 1 ) );

		n_yellow_cards[team_index] = 0;
		if ( MabMath::Rand( 1.0f ) < sim_yellow_card_chance ) n_yellow_cards[team_index]++;
		if ( MabMath::Rand( 1.0f ) < sim_yellow_card_chance ) n_yellow_cards[team_index]++;

		n_red_cards[team_index] = 0;
		if ( MabMath::Rand( 1.0f ) < sim_red_card_chance ) n_red_cards[team_index]++;
		if ( MabMath::Rand( 1.0f ) < sim_red_card_chance ) n_red_cards[team_index]++;

		n_injuries[team_index] = 0;
		if ( MabMath::Rand( 1.0f ) < sim_first_injury_chance ) n_injuries[team_index]++;
		if ( MabMath::Rand( 1.0f ) < sim_second_injury_chance ) n_injuries[team_index]++;


		SSTEAMSIDE team_id = team_sides[team_index];		//match_teams[team_index].GetDbId();

		// Add scrums
		for( int k = 0; k < n_scrums[team_index]; k++ )
		{
			statistics->AddScrum( team_id );
		}
		// Set the first half possession and territory percentages
		//statistics->SetHalf( FIRST_HALF );
		statistics->AddHalf(team_id, 0, possession_percentage[ team_index ] / 100.0f, territory_percentage[ team_index ] / 100.0f);
		// Set the second half possession and territory percentages
		//statistics->SetHalf( SECOND_HALF );
		//statistics->AddHalf(team_id, 0, possession_percentage[ team_index ] / 100.0f, territory_percentage[ team_index ] / 100.0f);
	}

	MAB_PROFILE_SECTION_END(profile1);
	MAB_PROFILE_SECTION_START(profile2, "TSim(2)");

	// Now add them to the stats system
	for (int team_index = 0; team_index < 2; team_index++)
	{
		RL3DB_TEAM team = database->GetTeam( team_ids[team_index] );
		unsigned short player_db_id;
		SSTEAMSIDE team_side = team_sides[team_index];			//current_team.GetDbId();

		playersPerTeam = (int)on_field_players[team_index].size();
		//Sanity check to see who we're simming for
		for(int pp = 0; pp < playersPerTeam; pp ++)
		{
			//unsigned short player_id = team.GetPlayer(pp).index;
			unsigned short player_id = on_field_players[team_index][pp];
			RL3DB_PLAYER playa(player_id);
			//MABLOGDEBUG("Simming for team %i's player(%i): %s", team_index, player_id, playa.GetName().c_str());
		}

		// Add tackles
		for( int k = 0; k < n_tackle_attempts[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			unsigned short player_id = on_field_players[team_index][player_index];

			if ( k < n_successful_tackle_attempts[team_index] )
			{
				statistics->AddTackle( team_side, player_id, true, false );
			}
			else
			{
				statistics->AddTackle( team_side, player_id, false, false );
			}
		}

		// Add Breakdown participation
		MABASSERT(n_breakdowns_turnover[team_index] <= n_breakdowns[team_index]);
		for( int k = 0; k < n_breakdowns[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			unsigned short player_id = on_field_players[team_index][player_index];

			statistics->AddRuckEnter( team_side, player_id );

			// turnovers, add them
			if ( k < n_breakdowns_turnover[team_index] )
			{
				statistics->AddRuckTurnOver( team_side, player_id );
			}
		}

		// Add Lineout stolen
		for( int k = 0; k < n_lineouts[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			unsigned short player_id = on_field_players[team_index][player_index];


			// turnovers, add them
			if ( k < n_lineouts_stolen[team_index] )
			{
				statistics->AddLineout( team_side, player_id, true );
			}
			else
			{
				statistics->AddLineout( team_side, player_id, false );
			}
		}

		//  Add penalties
		for( int k = 0; k < n_penalties[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			unsigned short player_id = on_field_players[team_index][player_index];

			statistics->AddPenalty( team_side, player_id );
		}

		// Add hitups
		for( int k = 0; k < n_hitups[team_index]; k++ )
		{
			int	player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			unsigned short player_id = on_field_players[team_index][player_index];

			statistics->AddHitup( team_side, player_id );
		}

		// Add Line Breaks
		for( int k = 0; k < n_line_breaks[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			unsigned short player_id = on_field_players[team_index][player_index];

			statistics->AddLineBreak( team_side, player_id );
		}

		// Add offloads
		for( int k = 0; k < n_offloads[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			unsigned short player_id = on_field_players[team_index][player_index];

			statistics->AddPass(team_side, player_id, true, true);
		}

		// Add handling error
		for( int k = 0; k < n_handling_errors[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			unsigned short player_id = on_field_players[team_index][player_index];

			statistics->AddHandlingError( team_side, player_id );
		}

		// Add kicks
		for ( int k = 0; k < n_kicks[team_index]; k++ )
		{
			// See which player will perform this kick:
			int player_index = -1;
			int random_kicker_id = MabMath::RandInt(10);
			if (random_kicker_id < 3)		// Full back
			{
				player_index = PlayerPositionEnum::GetStartingJerseyNumberFromPlayerPosition(PP_FULLBACK) - 1;
			}
			else if (random_kicker_id < 5)	// Half back
			{
				player_index = PlayerPositionEnum::GetStartingJerseyNumberFromPlayerPosition(PP_SCRUM_HALF) - 1;
			}
			else if (random_kicker_id < 7)	// Five-eighth
			{
				player_index = PlayerPositionEnum::GetStartingJerseyNumberFromPlayerPosition(PP_FLY_HALF_STAND_OFF) - 1;
			}
			else if (random_kicker_id < 9)	// Play kicker
			{
				player_index = play_kicker_index[team_index];
			}

			if (player_index == -1)
			{
				player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			}

			unsigned short player_id = on_field_players[team_index][player_index];
			statistics->AddKick( team_side, player_id );

			// Add some kicking metres gained
			RL3DB_PLAYER player(player_id );
			float metres_gained = sim_min_kick_metres_gained_per_kick + static_cast<float>( player.GetGeneralKickAccuracy() ) / MAX_PLAYER_ATTRIBUTE_VALUE * MabMath::RandInt( sim_max_kick_metres_gained_per_kick - sim_min_kick_metres_gained_per_kick + 1);
			statistics->AddKickingMetresGained( team_side, player_id, metres_gained );
		}

		// TODO: RL specific?
		//// Add dummy half runs
		//for ( int k = 0; k < n_dummy_half_runs[team_index]; k++ )
		//{
		//	unsigned short player_id = GetRandomPlayerId(current_players);
		//	statistics->AddDummyHalfRun( team_id, player_id );
		//}

		// Add running metres gained
		for ( int k = 0; k < playersPerTeam; k++ )
		{
			player_db_id = on_field_players[team_index][k];

			RL3DB_PLAYER player = database->GetPlayer( player_db_id );
			float charge_stats = static_cast<float>( player.GetAgility() + player.GetAggressiveness() + player.GetBreakTackleAbility() + player.GetSpeed() );
			float metres_gained = sim_min_run_metres_gained + ( charge_stats / (float) ( 4 * MAX_PLAYER_ATTRIBUTE_VALUE ) ) * MabMath::RandInt( sim_max_run_metres_gained - sim_min_run_metres_gained + 1 );
			statistics->AddRunningMetresGained( team_sides[team_index], player_db_id, metres_gained );
		}

		// Add injuries (do before suspensions)
		for( int k = 0; k < n_injuries[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			player_db_id = on_field_players[team_index][player_index];

			statistics->AddInjury( team_sides[team_index], player_db_id );

			RL3DB_PLAYER player = database->GetPlayer( player_db_id );

			switch (MabMath::RandInt(3))
			{
			case 0	:	player.SetInjury(INJURY_PENDING_UPPER); break;
			case 1	:	player.SetInjury(INJURY_PENDING_MID); break;
			case 2	:	player.SetInjury(INJURY_PENDING_LOWER); break;
			}
		}

		// Add yellow cards
		for( int k = 0; k < n_yellow_cards[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			player_db_id = on_field_players[team_index][player_index];

			statistics->AddYellowCard( team_sides[team_index], player_db_id );
		}

		// Add red cards
		for( int k = 0; k < n_red_cards[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			player_db_id = on_field_players[team_index][player_index];

			statistics->AddRedCard( team_sides[team_index], player_db_id );

			RL3DB_PLAYER player = database->GetPlayer( player_db_id );
			player.SetSuspension(SUSP_PENDING_RANDOM);
		}
	}

	MAB_PROFILE_SECTION_END(profile2);
	MAB_PROFILE_SECTION_START(profile3, "TSim(3)");

	// Add the stats to the permanent system, but not if it's a trial match
	if( !competition_definition.GetIsFriendly() && !match_in_progress)
	{
		if(!tournament_manager->IsLionsTour() || (tournament_manager->IsLionsTour() && (home_team.GetDbId()==DB_TEAMID_AUSTRALIA || away_team.GetDbId()==DB_TEAMID_AUSTRALIA)))
		{
			//bool is_preliminary_round = competition_instance.GetCurrentRound() < competition_definition.GetNumPreliminaryRounds();
			bool is_preliminary_round = competition_instance.GetIsPrelimRound();

			statistics->CalculatePlayerPerformances();
			statistics->AddCompetitionStats(competition_instance.GetDbId(), is_preliminary_round);
		}
	}
	MAB_PROFILE_SECTION_END(profile3);
}

///------------------------------------------------------------------
///------------------------------------------------------------------

void RL3Tournament::SimulateCurrentMatch( const RL3_SEASON_MATCH season_match, bool home_team_player_controlled, bool away_team_player_controlled )
{
	MABBREAKMSG("DEPRECATED");

	MABUNUSED(season_match);
	MABUNUSED(home_team_player_controlled);
	MABUNUSED(away_team_player_controlled);

#ifdef USE_OLD_CURRENT_MATCH_SIM_CODE
	MAB_PROFILE_SECTION_START(profile0, "TSim(0)");
	enum { HOME_TEAM, AWAY_TEAM };
	enum { STAR_PLAYERS, SUPER_STAR_PLAYERS, NORMAL_PLAYERS };
	SIFGameWorld* game = SIFApplication::GetApplication()->GetGameWorld();
	int gameLength				= tournament_manager->GetGameLength();

	SSGameTimer* timer			= game->GetGameTimer();

	int playersPerTeam = SIFApplication::GetApplication()->GetGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();
	int playersOnBench = SIFApplication::GetApplication()->GetGameWorld()->GetGameSettings().game_limits.GetNumberOfBenchPlayers();
	int playersOnTeamIncBench = playersPerTeam + playersOnBench;

	//RL3DB_MATCH match = season_match.GetMatch();

//	MABLOGMSG(LOGCHANNEL_ALWAYS, LOGTYPE_INFO, "RL3Tournament::SimulateMatch comp: %d, round: %d, match: %d, date: %d/%d/%d\t%.02f",
//		season_match.GetCompIndex(), season_match.GetRoundNum(), season_match.GetMatchNum(),
//		match.GetDate().GetDayNumber(), match.GetDate().GetMonth(), match.GetDate().GetYear(), MabTime::GetCurrentTime() );

	RL3DB_COMPETITION_INSTANCE competition_instance = GetCompetition( season_match.GetCompIndex() );
	RL3DB_COMPETITION_DEFINITION competition_definition = GetCompetitionDefinition( competition_instance.GetCompetitionId() );

	RL3DB_TEAM home_team = database->GetTeam( game->GetTeam(HOME_TEAM)->GetDbTeam().GetDbId() );//fixture_helper->GetTeamDbId( competition_instance, match.GetHomeTeam() ) );
	RL3DB_TEAM away_team = database->GetTeam( game->GetTeam(AWAY_TEAM)->GetDbTeam().GetDbId() );//fixture_helper->GetTeamDbId( competition_instance, match.GetAwayTeam() ) );
	RL3DB_TEAM current_team;

	// Allow draw if in prelim-rounds and more than two teams...
	//bool allow_draw = (competition_instance.GetCurrentRound() < competition_definition.GetNumPreliminaryRounds()) && competition_instance.GetNumTeams()>2;
	bool allow_draw = competition_instance.GetIsPrelimRound() && competition_instance.GetNumTeams()>2;

	// Store what our scores were before we simulated
	int existing_scores[2] = { 0, 0 };
	existing_scores[HOME_TEAM] = statistics->GetCurrentMatchStat(game->GetTeam(HOME_TEAM), &RUDB_STATS_TEAM::score);
	existing_scores[AWAY_TEAM] = statistics->GetCurrentMatchStat(game->GetTeam(AWAY_TEAM), &RUDB_STATS_TEAM::score);

	// Debug our current team playing lineup, later on we need to access players to add stats, currently it just grabs ID's from the team lineup, but that could have changed during play (subs, injuries, penalties).
	MabVector<unsigned short> on_field_players[2];

	// Getting the lineup the way the in game squad editor does, since i want an up to date modified version of the lineup...
	RUGameSettings* game_settings = SIFApplication::GetApplication()->GetMatchGameSettings();
	RU_TEAM_SETTINGS::LineupList& home_lineup_array = game_settings->team_settings[HOME_TEAM].lineup;
	RU_TEAM_SETTINGS::LineupList& away_lineup_array = game_settings->team_settings[AWAY_TEAM].lineup;

	// We're adding the players to our on field array, but only if they can continue playing, ignore players who are injured, or red carded. For now accept yellow carded players
	// We might have to do some smarter sims later if needed for players who shouldn't be subbed back on again?
	for(size_t pp = 0; pp < home_lineup_array.size(); pp ++)
	{
		RUDB_PLAYER *db_player = &home_lineup_array[pp];

		// Only do the sin bin check if the team has at least a single player sin binned.
		if(game->GetSubstitutionManager()->GetNumPlayerInSinbin(SIDE_A) > 0)
		{
			// This player is in the sin bin, don't add them to the available players to sim
			if(game->GetSubstitutionManager()->IsPlayerInSinBin((int)db_player->GetDbId(), (int)game->GetTeam(HOME_TEAM)->GetDbTeam().GetDbId()))
			{
				continue;
			}
		}

		// Don't want to sim more data for an injured player either.
		if(game->GetSubstitutionManager()->IsPlayerInjured((int)db_player->GetDbId(), SIDE_A))
		{
			continue;
		}

		// Apparently this checks if they are sent off for good...
		if(game->GetSubstitutionManager()->IsPlayerSentOff((int)db_player->GetDbId(), SIDE_A))
		{
			continue;
		}

		on_field_players[HOME_TEAM].push_back(db_player->GetDbId());
	}

	for(size_t pp = 0; pp < away_lineup_array.size(); pp ++)
	{
		RUDB_PLAYER *db_player = &away_lineup_array[pp];

		// Only do the sin bin check if the team has at least a single player sin binned.
		if(game->GetSubstitutionManager()->GetNumPlayerInSinbin(SIDE_B) > 0)
		{
			// This player is in the sin bin, don't add them to the available players to sim
			if(game->GetSubstitutionManager()->IsPlayerInSinBin((int)db_player->GetDbId(), (int)game->GetTeam(AWAY_TEAM)->GetDbTeam().GetDbId()))
			{
				continue;
			}
		}

		// Don't want to sim more data for an injured player either.
		if(game->GetSubstitutionManager()->IsPlayerInjured((int)db_player->GetDbId(), SIDE_B))
		{
			continue;
		}

		// Apparently this checks if they are sent off for good...
		if(game->GetSubstitutionManager()->IsPlayerSentOff((int)db_player->GetDbId(), SIDE_B))
		{
			continue;
		}

		on_field_players[AWAY_TEAM].push_back(db_player->GetDbId());
	}

	// DEBUGZ sanity check
	for(int i = 0; i < 2; i ++)
	{
		MABLOGDEBUG("Players available for simming from team %s", (i == 0 ? game->GetTeam(HOME_TEAM)->GetShortName().c_str() : game->GetTeam(AWAY_TEAM)->GetShortName().c_str()));
		for(size_t pp = 0; pp < on_field_players[i].size(); pp ++)
		{
			RL3DB_PLAYER playa(on_field_players[i][pp]);
			MABLOGDEBUG("player(%i): %s", on_field_players[i][pp], playa.GetName().c_str());
		}
	}


	//-------------------------- Territory and Possession -----------------------------------

	// This is the total we already have (if post first half)
	int existing_possession_percentage[]	= { 0, 0 };
	int existing_territory_percentage[]		= { 0, 0 };

	// Work out what the possession/territory is for the current half in the game
	//RUStatsTracker* tracker = game->GetStatsTracker();
	float pA = statistics->GetCurrentMatchStat(game->GetTeam(HOME_TEAM), &RUDB_STATS_TEAM::possession);
	float pB = statistics->GetCurrentMatchStat(game->GetTeam(AWAY_TEAM), &RUDB_STATS_TEAM::possession);
	float pTotal = pA + pB;
	existing_possession_percentage[HOME_TEAM] = (int)((pA / pTotal) * 100.0f);
	existing_possession_percentage[AWAY_TEAM] = 100 - existing_possession_percentage[HOME_TEAM];

	float tA = statistics->GetCurrentMatchStat(game->GetTeam(HOME_TEAM), &RUDB_STATS_TEAM::territory);
	float tB = statistics->GetCurrentMatchStat(game->GetTeam(AWAY_TEAM), &RUDB_STATS_TEAM::territory);
	float tTotal = tA + tB;
	existing_territory_percentage[HOME_TEAM] = (int)((tA / tTotal) * 100.0f);
	existing_territory_percentage[AWAY_TEAM] = 100 - existing_territory_percentage[HOME_TEAM];

	MABLOGDEBUG("Team A possession: %i, Team B possession: %i", existing_possession_percentage[HOME_TEAM], existing_possession_percentage[AWAY_TEAM]);
	MABLOGDEBUG("Team A territory: %i, Team B territory: %i", existing_territory_percentage[HOME_TEAM], existing_territory_percentage[AWAY_TEAM]);

	//-----------------------------------------------------------------------------------------

	// Work out how much time of match is remaining, and scale back our max values that we'll use in the sim.
	if(timer->GetIsInExtraTime())
	{
		MABBREAKMSG("We're simulating during extra time, we can skip most of the simulation since we only care about a single point being scored in any way possible");
	}


	int actualLengthInMins		= game->GetGameSettings().game_limits.GetActualGameLength();
	int actualHalfLengthInMins	= game->GetGameSettings().game_limits.GetActualHalfLengths();
	int actualLengthInSecs		= actualLengthInMins * 60;

	int elapsedMinsScaled		= timer->GetScaledMinutesElapsed();
	int elapsedSecsScaled		= timer->GetScaledSecondsElapsed();

	// if we're in over time, scaled the time back so that the elapsed perc is always at either 50% or 100%
	if(timer->GetIsInOverTime())
	{
		if(timer->GetCurrentHalf() == FIRST_HALF)
			elapsedMinsScaled = actualHalfLengthInMins;
		else
			elapsedMinsScaled = actualLengthInMins;

		elapsedSecsScaled = 0;
	}

	float elapsedPerc = (float)((elapsedMinsScaled * 60) + elapsedSecsScaled) / (float)actualLengthInSecs;
	float percMatchRemaining = 1.0f - elapsedPerc; // We will use this value to scale back all of our simmed values.




	// Setup our difficulty modifier. Higher makes the game easier
	float difficulty_modifier = 0.0f;
	DIFFICULTY diff = tournament_manager->IsInFranchise() ? tournament_manager->GetManagerDifficulty() : tournament_manager->GetGameDifficulty();
	switch ( diff )
	{
	case DIF_VERYEASY :	difficulty_modifier = 0.15f; break;
	case DIF_EASY :		difficulty_modifier = 0.0f; break;
	case DIF_NORMAL :	difficulty_modifier = -0.1f; break;
	case DIF_HARD :		difficulty_modifier = -0.16f; break;
	case DIF_PRO :
	default :			difficulty_modifier = -0.2f; break;
	}

	unsigned short team_ids[ 2 ] = { home_team.GetDbId(), away_team.GetDbId() };

	//float	max_total_attack[2] = { 0 };	// One for each team. We'll be increasing it by the maximum possible to keep track of what the total max will be
	//float	max_total_defence[2] = { 0 };	// One for each team. We'll be increasing it by the maximum possible to keep track of what the total max will be

	float	total_attack[2] = { 0, 0 };
	float	total_defence[2] = { 0, 0 };
	float	team_confidence[2] = { 0, 0 };
	float	team_attack_factor[2] = { 0, 0 };	// Range from 0.0f to 1.0f
	float	team_defence_factor[2] = { 0, 0 }; // Range from 0.0f to 1.0f


	// 2D array. First dimension = 1 for each team. 2nd dimension: star, super star, and normal players.
	// Basically this is storing all the players in each team based on if they are a star or not
	// Stores their db_indices
	MabVector< int > players[2][3];
	const int STAR_RATING = static_cast<int> ( 8.5f * MAX_PLAYER_ATTRIBUTE_VALUE );
	const int SUPER_STAR_RATING = static_cast<int> ( 9.2f * MAX_PLAYER_ATTRIBUTE_VALUE );

	//==========================================================================================================
	// ADD TOGETHER ALL THE PLAYERS STATS FOR EACH TEAM
	//==========================================================================================================

	for( int team_index = 0; team_index < 2; team_index++ )
	{
		current_team = team_index == 0 ? home_team : away_team;

		int num_players = MabMath::Min((int)on_field_players[team_index].size(), playersOnTeamIncBench);

		// Work out several things about the team
		for( int player_index = 0; player_index < num_players; player_index++ )
		{
			//unsigned short player_db_id = current_team.GetPlayer(player_index).index;
			unsigned short player_db_id = on_field_players[team_index][player_index];
			rudb_player_row *player_data = (rudb_player_row*)database->GetPlayerCache()->GetRowStart( player_db_id );

			//RL3DB_PLAYER current_player = database->GetPlayer( current_team.GetPlayer(player_index).index );

			// Add all this players stats together
			int total_stats = player_data->acceleration +
				player_data->agility +
				player_data->aggressiveness +
				player_data->break_tackle_ability +
				player_data->catch_ability +
				player_data->fitness +
				player_data->general_kick_accuracy +
				player_data->goal_kick_accuracy +
				player_data->offload_ability +
				player_data->pass_accuracy +
				player_data->speed +
				player_data->tackle_ability;

			// See how good a player they are based on this and store them in our vector
			if ( total_stats >= SUPER_STAR_RATING )
			{
				players[team_index][SUPER_STAR_PLAYERS].push_back( player_db_id );
			}
			else if ( total_stats >= STAR_RATING )
			{
				players[team_index][STAR_PLAYERS].push_back( player_db_id );
			}
			else
			{
				players[team_index][NORMAL_PLAYERS].push_back( player_db_id );
			}

			// Add this players attacking stats to the teams total attacking stats
			total_attack[team_index] +=	player_data->break_tackle_ability +
				player_data->speed +
				player_data->acceleration +
				player_data->fitness +
				player_data->agility;

			// Max would be a player with max in all 5 attack stats
			//max_total_attack[team_index] += 5 * MAX_PLAYER_ATTRIBUTE_VALUE;

			// Add this players defending stats to the teams total defending stats
			total_defence[team_index] +=	player_data->tackle_ability +
				player_data->speed +
				player_data->acceleration +
				player_data->fitness +
				player_data->agility;

			// Max would be a player with max in all 5 defence stats
			//max_total_defence[team_index] += 5 * MAX_PLAYER_ATTRIBUTE_VALUE;
		}
		//MABLOGDEBUG( "%s (Before stars), Total Attack %0.2f, Total Defence %0.2f", current_team.GetName(), total_attack[team_index], total_defence[team_index] );
	}

	// Add extra attack and defence for star players
	for( int team_index = 0; team_index < 2; team_index++ )
	{
		current_team = team_index == 0 ? home_team : away_team;

		total_attack[team_index] += (players[team_index][SUPER_STAR_PLAYERS].size() * 3000.0f );
		total_attack[team_index] += (players[team_index][STAR_PLAYERS].size() * 1500.0f );

		// Max would be a team of all superstars
		//max_total_attack[team] += 3000 * (NUM_PLAYERS_PER_TEAM + NUM_BENCH_PLAYERS);

		total_defence[team_index] += (players[team_index][SUPER_STAR_PLAYERS].size() * 3000.0f );
		total_defence[team_index] += (players[team_index][STAR_PLAYERS].size() * 1500.0f );

		// Max would be a team of all superstars
		//max_total_defence[team] += 3000 * (NUM_PLAYERS_PER_TEAM + NUM_BENCH_PLAYERS);

		//MABLOGDEBUG( "%s (After stars), Total Attack %0.2f, Total Defence %0.2f", current_team.GetName(), total_attack[team_index], total_defence[team_index] );
	}


	//==========================================================================================================
	// START MODIFYING TEAMS STATS
	//==========================================================================================================

	// We need to copy these stats to make our calculations from so our modifiers are NOT cumulative
	float base_total_attack[2] = { total_attack[HOME_TEAM], total_attack[AWAY_TEAM] };
	float base_total_defence[2] = { total_defence[HOME_TEAM], total_defence[AWAY_TEAM] };

	//float base_max_attack[2] = { max_total_attack[HOME_TEAM], max_total_attack[AWAY_TEAM] };
	//float base_max_defence[2] = { max_total_defence[HOME_TEAM], max_total_defence[AWAY_TEAM] };

	//// Modify the home teams attack/defence
	//total_attack[HOME_TEAM]  += 0.08f * base_total_attack[HOME_TEAM];
	//total_defence[HOME_TEAM] += 0.08f * base_total_defence[HOME_TEAM];

	// The maximum bonus here is if you are the home team. Sounds harsh to apply this to the away team, but since
	// we're working with ratios, only adding it to the home teams max won't have any effect
	//for( int team_index = 0; team_index < 2; team_index++ )
	//{
	//max_total_attack[team] += 0.08f * base_max_attack[team];
	//max_total_defence[team] += 0.08f * base_max_defence[team];
	//}

	//==========================================================================================================
	// MAKE CHANGE BASED ON DIFFICULTY HERE

	//MABLOGDEBUG( "%s (Before), Total Attack %0.2f, Total Defence %0.2f", home_team.GetName(), total_attack[0], total_defence[0] );
	//MABLOGDEBUG( "%s (Before), Total Attack %0.2f, Total Defence %0.2f", away_team.GetName(), total_attack[1], total_defence[1] );

	// If one team is human and the other not, change the stats of the human team
	// Strangely enough we don't modify max_attack here. This is because for it to have an effect of difficulty,
	// you need the stats to go up if its easier. If you increase your max stats as well, this won't help!
	if ( home_team_player_controlled && !away_team_player_controlled )
	{
		total_attack[HOME_TEAM]	 += difficulty_modifier * base_total_attack[HOME_TEAM];
		total_defence[HOME_TEAM] += difficulty_modifier * base_total_defence[HOME_TEAM];
	}
	else if ( away_team_player_controlled && !home_team_player_controlled )
	{
		total_attack[AWAY_TEAM]	 += difficulty_modifier * base_total_attack[AWAY_TEAM];
		total_defence[AWAY_TEAM] += difficulty_modifier * base_total_defence[AWAY_TEAM];
	}

	MAB_PROFILE_SECTION_END(profile0);
	MAB_PROFILE_SECTION_START(profile1, "TSim(1)");

	//==========================================================================================================

	// Modify the total attack/defence based on the team attack/defence stat values
	// if their stat is > 60% of max, they get a boost. Otherwise nothing. Max stat will get 20% boost. 70% will get 5% boost

	float threshold = 0.6f;

	for( int team_index = 0; team_index < 2; team_index++ )
	{
		current_team = team_index == 0 ? home_team : away_team;

		if( current_team.GetNormalisedAttack() > threshold)
		{
			total_attack[ team_index ] += base_total_attack[team_index] * ( 0.2f - ( 0.2f * ( 1.0f - current_team.GetNormalisedAttack() ) / ( 1.0f - threshold ) ) ) ;
		}

		if( current_team.GetNormalisedDefence() > threshold)
		{
			total_defence[ team_index ] += base_total_defence[team_index] * ( 0.2f - ( 0.2f * ( 1.0f - current_team.GetNormalisedDefence() ) / ( 1.0f - threshold ) ) ) ;
		}

		// Max would be a 20% bonus
		//max_total_attack[team] += 0.2f * base_max_attack[team];
		//max_total_defence[team] += 0.2f * base_max_defence[team];

		//MABLOGDEBUG( "Team %s has %d superstars, %d stars and %d normal players", current_team->name, players[team][SUPER_STAR_PLAYERS].size(), players[team][STAR_PLAYERS].size(), players[team][NORMAL_PLAYERS].size() );
	}

	// We want to scale our values across these numbers
	// So a team with min_total_attack, will get an attack factor of ~0
	const int MAX_TOTAL_ATTACK = 1100000;
	const int MAX_TOTAL_DEFENCE = 1100000;
	const int MIN_TOTAL_ATTACK = (int) ( 0.3f * MAX_TOTAL_ATTACK );
	const int MIN_TOTAL_DEFENCE = (int) ( 0.3f * MAX_TOTAL_DEFENCE );

	// Confidence ranges from 0 -> 1.0f. If you have max confidence you will get a 30% boost to attack and defence.
	// If you have 0 confidence you will lose 30% attack and defence. If you have 0.5f confidence nothing happens
	team_confidence[0] = home_team.GetCompetitionConfidence(competition_instance.GetDbId(), competition_instance.GetCurrentSatellite());
	team_confidence[1] = away_team.GetCompetitionConfidence(competition_instance.GetDbId(), competition_instance.GetCurrentSatellite());
	for( int team_index = 0; team_index < 2; team_index++ )
	{
		current_team = team_index == 0? home_team : away_team;

		//MABLOGDEBUG( "%s, Total Attack %0.2f, Total Defence %0.2f", current_team->name, total_attack[j], total_defence[j] );
		//MABLOGDEBUG( "%s, Confidence %0.2f", current_team->name, team_confidence[j] );

		// Add confidence boost
		total_attack[team_index] += ((team_confidence[team_index] - 0.5f) * 0.60f ) * base_total_attack[team_index];
		// Max is 30%
		//max_total_attack[team_index] += 0.3f * base_max_attack[team_index];
		// Calculate team_attack factor
		team_attack_factor[team_index] = ( total_attack[team_index] - MIN_TOTAL_ATTACK ) / ( MAX_TOTAL_ATTACK - MIN_TOTAL_ATTACK );

		MabMath::Clamp( team_attack_factor[team_index], 0.0f, 1.0f );

		// Same for defence
		total_defence[team_index] += ((team_confidence[team_index] - 0.5f) * 0.60f ) * base_total_defence[team_index];;
		// Max is 30%
		//max_total_defence[team_index] += 0.3f * base_max_defence[team_index];
		// Calculate team_defence factor
		team_defence_factor[team_index] = ( total_defence[team_index] - MIN_TOTAL_DEFENCE ) / ( MAX_TOTAL_DEFENCE - MIN_TOTAL_DEFENCE );

		MabMath::Clamp( team_defence_factor[team_index], 0.0f, 1.0f );

		//MABLOGDEBUG( "%s (After), Total Attack %0.2f, Total Defence %0.2f", current_team.GetName(), total_attack[team_index], total_defence[team_index] );
	}

	//==========================================================================================================
	// DONE CALCULATING STATS, NOW WE'LL USE THEM TO SIMULATE THE MATCH!
	// Attack and defence factors are numbers from 0 -> 1 as a ratio of their total attack/defence out of the
	// maximum possible attack/defence. A perfect team will have a factor of 1.0f
	//==========================================================================================================

	// How much we want to scale the stats by (so that they'll match the player's games) CUT. Just make it like real life
	// float stats_scale = MabMath::Max( 1.0f, tournament_manager->GetGameLength() / 10.0f );

	// Scale the number of tries given based on game length
	int MAX_GIVEN_TRIES = 0;
	int MAX_PENALTIES = 0;
	int MAX_EXTRA_TRIES = 0;
	float FIELD_GOAL_CHANCE = 0.1f;

	// Set up some values for the max tries and penalties we can sim
	switch ( gameLength )
	{
		case 5:
			MAX_GIVEN_TRIES = 8;	MAX_EXTRA_TRIES = 3; MAX_PENALTIES = 2; break;
		default:
		case 80:
			MAX_GIVEN_TRIES = 10;	MAX_EXTRA_TRIES = 5; MAX_PENALTIES = 4; break;
	}

	// Scale back our max values depending on how much time of the match is left over. We only want to sim these numbers, and then add them on top of what we already have.
	MAX_GIVEN_TRIES		= (int)(percMatchRemaining * (float)MAX_GIVEN_TRIES);
	MAX_EXTRA_TRIES		= (int)(percMatchRemaining * (float)MAX_EXTRA_TRIES);
	MAX_PENALTIES		= (int)(percMatchRemaining * (float)MAX_PENALTIES);
	FIELD_GOAL_CHANCE	= percMatchRemaining * FIELD_GOAL_CHANCE;

	int given_tries[2] = { 0, 0 };
	int n_possible_extra_tries[2] = { 0, 0 };

	// Give the teams some tries based on both teams attack and defense
	for (int team_index = 0; team_index < 2; team_index++)
	{
		// This formula takes the teams attack, and the opposite teams defence difference, and adds a half to it
		// It then multiplies it by the max number of tries possible
		// So to get the MAX number of tries, a team needs an attack/defence difference of >= 0.5
		// A team whose attack is lower than the opponents defence by 0.5 or more, will get zero tries (they might get somre more later though)
		given_tries[team_index] = MabMath::Round( ( 0.5f + team_attack_factor[team_index] - team_defence_factor[1 - team_index] ) * MAX_GIVEN_TRIES );
		MabMath::Clamp( given_tries[team_index], 0, MAX_GIVEN_TRIES );

		n_possible_extra_tries[team_index] = MabMath::Max(1, MabMath::Round( team_attack_factor[team_index] * MAX_EXTRA_TRIES ));

		// Scale the possible extra tries with time remaining.
		n_possible_extra_tries[team_index] = MabMath::Round((float)n_possible_extra_tries[team_index] * percMatchRemaining);
	}

	// Give more tries based on how much better each team is than the other team
	float dominance[2] = { 0, 0 };
	dominance[HOME_TEAM] = team_attack_factor[HOME_TEAM] - team_defence_factor[AWAY_TEAM];
	dominance[AWAY_TEAM] = team_attack_factor[AWAY_TEAM] - team_defence_factor[HOME_TEAM];

	// Put in some randomness for kicks. Add between -0.1 and 0.1f to our number
	dominance[HOME_TEAM] += ( MabMath::Rand(0.2f) - 0.1f );
	dominance[AWAY_TEAM] += ( MabMath::Rand(0.2f) - 0.1f );

	int n_tries[2] = { 0, 0 };
	for (int team_index = 0; team_index < 2; team_index++)
	{
		n_tries[team_index] = given_tries[team_index];

		n_tries[team_index] += MabMath::Max(0, MabMath::Round( dominance[team_index] * ((float)n_possible_extra_tries[team_index]) ));

		// Awesome random action. The team will get between 50 and 100% of the tries they've been allocated
		float try_modifier = 0.5f + MabMath::Rand( 0.5f );
		n_tries[team_index] = (int) ( n_tries[team_index] * try_modifier );

		// Keep some balancing in here. Each team has a 24% chance of getting at least 1 try if they don't have any yet
		if (n_tries[team_index] < 1 && MabMath::Rand(1.0f) < (0.2f * percMatchRemaining))
		{
			n_tries[team_index]++;
		}
		// And a 4% chance of getting 2 if they had none, or 20% chance of getting 2 if they had 1
		if (n_tries[team_index] < 2 && MabMath::Rand(1.0f) < (0.2f * percMatchRemaining))
		{
			n_tries[team_index]++;
		}
	}

#ifdef ENABLE_GAME_DEBUG_MENU
	if(SIFDebug::GetCareerModeDebugSettings()->GetWinAllMatchesEnabled())
	{
		if ( home_team_player_controlled || away_team_player_controlled )
		{
			// ensure that the human team scored at least three more tries than the computer team
			int human_index = home_team_player_controlled ? HOME_TEAM : AWAY_TEAM;
			MabMath::ClampLower( n_tries[human_index], 3 );
			MabMath::ClampUpper( n_tries[1-human_index], n_tries[human_index] - 3 );
		}
	}
	else if(SIFDebug::GetCareerModeDebugSettings()->GetLoseAllMatchesEnabled())
	{
		if ( home_team_player_controlled || away_team_player_controlled )
		{
			// ensure that the computer scored more tries
			int other_index = home_team_player_controlled ? AWAY_TEAM : HOME_TEAM;
			MabMath::ClampLower( n_tries[other_index], 3 );
			MabMath::ClampUpper( n_tries[1-other_index], n_tries[other_index] - 3 );
		}
	}
#endif


#if 0	// RC2_TODO...
	if (
		( data.tournament_mode == TM_FRANCHISE && SIFApplication::GetApplication()->GetUnlockableManager()->IsUnlocked( "RL3_CHEAT_WIN_ALL_SIMULATED_MATCH" ) )
#ifdef ENABLE_TOURNAMENT_DEBUG_MENU
		|| ( GetWinAllSimulatedMatches() )
#endif
		)
	{
		// the win every sim match cheat is on, and this is a franchise (not competition)
		if ( home_team_player_controlled || away_team_player_controlled )
		{
			// ensure that the human team scored at least three more tries than the computer team
			int human_index = home_team_player_controlled ? HOME_TEAM : AWAY_TEAM;
			MabMath::ClampLower( n_tries[human_index], 3 );
			MabMath::ClampUpper( n_tries[1-human_index], n_tries[human_index] - 3 );
		}
	}
#endif
	// These are team relative indices, for each team in the match.
	int goal_kicker_index[NUM_TEAMS_INIT] = { -1, -1 };
	int play_kicker_index[NUM_TEAMS_INIT] = { -1, -1 };


	// Find the teams goal kickers and play kickers. If the player is not in the starting lineup, then choose the player
	// with the highest kick_accuracy instead

	int numTeams = SIFApplication::GetApplication()->GetGameWorld()->GetGameSettings().game_limits.GetNumberOfTeams();

	for( int team_index = 0; team_index < numTeams /*NUM_TEAMS*/; team_index++ )
	{
		current_team = team_index == 0 ? home_team : away_team;

		int num_players = MabMath::Min((int)on_field_players[team_index].size(), playersPerTeam);
		MABASSERT(num_players == playersPerTeam/*NUM_PLAYERS_PER_TEAM*/);

		// Find the goal kicker for the team
		goal_kicker_index[team_index] = -1;

		int goal_kicker_id = current_team.GetGoalKicker(0);
		if ( goal_kicker_id >= 0 && goal_kicker_id < DB_INVALID_ID )
		{
			// Try and find this kicker in the starting lineup
			for( int player_index = 0; player_index < num_players; player_index++ )
			{
				//if ( current_team.GetPlayer(player_index).index == goal_kicker_id )
				if ( on_field_players[team_index][player_index] == goal_kicker_id )
				{
					goal_kicker_index[team_index] = player_index;
					break;
				}
			}
		}

		if ( goal_kicker_index[team_index] == -1 )
		{
			int max_kick_ability = -1;
			for( int player_index = 0; player_index < num_players; player_index++ )
			{
				//RL3DB_PLAYER current_player = database->GetPlayer( current_team.GetPlayer(player_index).index );
				RL3DB_PLAYER current_player(on_field_players[team_index][player_index]);
				unsigned short goal_kick_accuracy = current_player.GetGoalKickAccuracy();
				if ( goal_kick_accuracy > max_kick_ability )
				{
					goal_kicker_index[team_index] = player_index;
					max_kick_ability = goal_kick_accuracy;
				}
			}
		}

		MABASSERTMSG(goal_kicker_index[team_index] != -1, "We can't use -1 as the index for our goal kicker...");

		// Find the play kicker for the team
		play_kicker_index[team_index] = -1;
		unsigned short player_kicker_id = current_team.GetPlayKicker(0);

		if ( player_kicker_id != DB_INVALID_ID )
		{
			// Try and find this kicker in the starting lineup
			for( int player_index = 0; player_index < num_players; player_index++ )
			{
				//if ( current_team.GetPlayer(player_index).index == player_kicker_id )
				if ( on_field_players[team_index][player_index] == player_kicker_id )
				{
					play_kicker_index[team_index] = player_index;
					break;
				}
			}
		}

		if ( play_kicker_index[team_index] == -1 )
		{
			int max_kick_ability = -1;
			for( int player_index = 0; player_index < num_players; player_index++ )
			{
				//RL3DB_PLAYER current_player = database->GetPlayer( current_team.GetPlayer(player_index).index );
				RL3DB_PLAYER current_player(on_field_players[team_index][player_index]);
				unsigned short kick_accuracy = current_player.GetGeneralKickAccuracy();
				if ( kick_accuracy > max_kick_ability )
				{
					play_kicker_index[team_index] = player_index;
					max_kick_ability = kick_accuracy;
				}
			}
		}

	}

	// Work out the conversions
	int n_conversions[2] = { 0, 0 };
	float goal_kicker_perc[2] = { 0, 0 };
	for( int team_index = 0; team_index < 2; team_index++ )
	{
		current_team = team_index == 0 ? home_team : away_team;

		//float kicker_rating = ( (float) database->GetPlayer( current_team.GetPlayer( goal_kicker_index[team_index] ).index ).GetGoalKickAccuracy() ) / MAX_PLAYER_ATTRIBUTE_VALUE;
		RL3DB_PLAYER current_player(on_field_players[team_index][goal_kicker_index[team_index]]);
		float kicker_rating = ( (float) current_player.GetGoalKickAccuracy() ) / MAX_PLAYER_ATTRIBUTE_VALUE;
		// Kicker will be kicking between 70% and 100% of their kicking stat value
		goal_kicker_perc[team_index] = kicker_rating * ( 0.7f + MabMath::Rand( 0.3f ) );
		MabMath::Clamp( goal_kicker_perc[team_index], 0.0f, 1.0f );
		n_conversions[team_index] = MabMath::Round( goal_kicker_perc[team_index] * ((float) n_tries[team_index] ) );
	}

	// Work out the penalty kicks
	int n_penalty_kicks[2] = { 0, 0 };
	int n_successful_penalty_kicks[2] = { 0, 0 };
	for( int team_index = 0; team_index < 2; team_index++ )
	{
		n_penalty_kicks[team_index] = MabMath::RandInt( MAX_PENALTIES + 1 );

		n_successful_penalty_kicks[team_index] = MabMath::Round( goal_kicker_perc[team_index] * ((float) n_penalty_kicks[team_index] ) );
	}

	// work out the drop goals (only if the scores are really close)
	int current_scores[2] = { 0, 0 };

	int conversion_score = SIFApplication::GetApplication()->GetMatchGameSettings().GetConversionScore();
	int penalty_score = SIFApplication::GetApplication()->GetMatchGameSettings().GetPenaltyScore();
	int drop_goal_score = SIFApplication::GetApplication()->GetMatchGameSettings().GetDropGoalScore();

	current_scores[HOME_TEAM] = existing_scores[HOME_TEAM] + n_tries[HOME_TEAM] * try_score + n_conversions[HOME_TEAM] * conversion_score + n_successful_penalty_kicks[HOME_TEAM] * penalty_score;
	current_scores[AWAY_TEAM] = existing_scores[AWAY_TEAM] + n_tries[AWAY_TEAM] * try_score + n_conversions[AWAY_TEAM] * conversion_score + n_successful_penalty_kicks[AWAY_TEAM] * penalty_score;
	int points_difference = current_scores[HOME_TEAM] - current_scores[AWAY_TEAM];

	int n_drop_goals[2] = { 0, 0 };

	// Throw in the occasional field goal (we're not getting enough over the course of a franchise)
	// However we don't want weird scores like 54-1, so only give the losing team a chance if they're losing by <= 8
	for( int team_index = 0; team_index < 2; ++team_index )
	{
		if( MabMath::Abs( points_difference ) <= 8 || current_scores[ team_index ] > current_scores[ 1 - team_index ] )
		{
			if( MabMath::Rand( 1.0f ) < FIELD_GOAL_CHANCE )
			{
				++n_drop_goals[team_index];
			}
		}
	}

	current_scores[HOME_TEAM] = existing_scores[HOME_TEAM] + n_tries[HOME_TEAM] * try_score + n_conversions[HOME_TEAM] * conversion_score + n_successful_penalty_kicks[HOME_TEAM] * penalty_score + n_drop_goals[HOME_TEAM] * drop_goal_score;
	current_scores[AWAY_TEAM] = existing_scores[AWAY_TEAM] + n_tries[AWAY_TEAM] * try_score + n_conversions[AWAY_TEAM] * conversion_score + n_successful_penalty_kicks[AWAY_TEAM] * penalty_score + n_drop_goals[AWAY_TEAM] * drop_goal_score;
	points_difference = current_scores[HOME_TEAM] - current_scores[AWAY_TEAM];

	if ( MabMath::Abs(points_difference) == 6 )
	{
		int drop_goal_team = current_scores[HOME_TEAM] > current_scores[AWAY_TEAM] ? HOME_TEAM : AWAY_TEAM;

		// 20% chance of a field goal by the winning team if they're 6 ahead
		if ( MabMath::Rand( 1.0f ) < 0.2f ) n_drop_goals[drop_goal_team]++;

	}
	else if( MabMath::Abs(points_difference) == 0 )
	{
		int drop_goal_team;
		// 70% chance of the home team winning a draw
		if( MabMath::Rand( 1.0f ) < 0.7f )
		{
			drop_goal_team = HOME_TEAM;
		}
		else
		{
			drop_goal_team = AWAY_TEAM;
		}

		// 65% chance they actually get a field goal
		if ( MabMath::Rand( 1.0f ) < 0.65f ) n_drop_goals[drop_goal_team]++;

	}

	current_scores[HOME_TEAM] = existing_scores[HOME_TEAM] + n_tries[HOME_TEAM] * try_score + n_conversions[HOME_TEAM] * conversion_score + n_successful_penalty_kicks[HOME_TEAM] * penalty_score + n_drop_goals[HOME_TEAM] * drop_goal_score;
	current_scores[AWAY_TEAM] = existing_scores[AWAY_TEAM] + n_tries[AWAY_TEAM] * try_score + n_conversions[AWAY_TEAM] * conversion_score + n_successful_penalty_kicks[AWAY_TEAM] * penalty_score + n_drop_goals[AWAY_TEAM] * drop_goal_score;
	points_difference = current_scores[HOME_TEAM] - current_scores[AWAY_TEAM];

	// If it's still a draw
	if (points_difference == 0 && !allow_draw)
	{
		// TODO: Replace with OT regulations for pools/league versus knockout.
		//// 5% chance of it ending in a draw if its limited golden point
		//if ( competition_helper->GetGoldenPointType( competition_definition, competition_instance ) == GOLDEN_POINT_LIMITED )
		//{
		//	if( MabMath::Rand( 1.0f ) < 0.95f )
		//	{
		//		int drop_goal_team;
		//		if( MabMath::Rand( 1.0f ) < 0.7f )
		//		{
		//			drop_goal_team = HOME_TEAM;
		//		}
		//		else
		//		{
		//			drop_goal_team = AWAY_TEAM;
		//		}

		//		n_drop_goals[drop_goal_team]++;

		//	}
		//}
		//// Unlimited must have a winner
		//else if ( competition_helper->GetGoldenPointType( competition_definition, competition_instance ) == GOLDEN_POINT_UNLIMITED )
		{
			int winning_team;
			if( MabMath::Rand( 1.0f ) < 0.7f )
			{
				winning_team = HOME_TEAM;
			}
			else
			{
				winning_team = AWAY_TEAM;
			}

			if( MabMath::Rand( 1.0f ) < 0.3f )
			{
				n_tries[winning_team]++;
			}
			else
			{
				n_drop_goals[winning_team]++;
			}

		}
	}


	//|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
	// MORE BALANCING
	// We have decided that there are too many higher end scores.
	// To combat this, if BOTH teams score >= 24 points, we will subtract a max of 12 points from each team.
	// We will always subtract the same amount from both teams.
	// 50% chance of this occurring
	// It could be possible that the 50% chance occurs, but the way the scores work out means that we can't subtract any points fairly and easily
	//|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||

	int home_team_score = (existing_scores[HOME_TEAM] + n_tries[HOME_TEAM] * try_score + n_conversions[HOME_TEAM] * conversion_score + n_successful_penalty_kicks[HOME_TEAM] * penalty_score + n_drop_goals[HOME_TEAM] * drop_goal_score);
	int away_team_score = (existing_scores[AWAY_TEAM] + n_tries[AWAY_TEAM] * try_score + n_conversions[AWAY_TEAM] * conversion_score + n_successful_penalty_kicks[AWAY_TEAM] * penalty_score + n_drop_goals[AWAY_TEAM] * drop_goal_score);
	int ceilingScore = 20;

	// There's generally a lot more scoring during sevens games, so we'll increase the ceiling a bit more
	// Nick WWS 7s to Womens 13s //
	//if(game->GetGameSettings().game_settings.GameModeIsR7())
	//	ceilingScore = 35;

	if( home_team_score >= ceilingScore && away_team_score >= ceilingScore && MabMath::Rand( 1.0f ) < 0.6f )
	{
		bool change_made = false;

		// Ideally remove a converted try and 3 penalties
		if( n_tries[HOME_TEAM] >= 1 && n_tries[AWAY_TEAM] >= 1 )
		{
			if( n_conversions[HOME_TEAM] >= 1 && n_conversions[AWAY_TEAM] >= 1 )
			{
				if( n_successful_penalty_kicks[HOME_TEAM] >= 3 && n_successful_penalty_kicks[AWAY_TEAM] >= 3 )
				{
					n_tries[HOME_TEAM] -= 1;
					n_tries[AWAY_TEAM] -= 1;
					n_conversions[HOME_TEAM] -= 1;
					n_conversions[AWAY_TEAM] -= 1;
					n_successful_penalty_kicks[HOME_TEAM] -= 3;
					n_successful_penalty_kicks[AWAY_TEAM] -= 3;
					change_made = true;
				}
			}
		}

		// If we haven't succeeded, try to remove 2 converted tries
		if( !change_made && n_tries[HOME_TEAM] >= 2 && n_tries[AWAY_TEAM] >= 2 )
		{
			if( n_conversions[HOME_TEAM] >= 2 && n_conversions[AWAY_TEAM] >= 2 )
			{
				n_tries[HOME_TEAM] -= 2;
				n_tries[AWAY_TEAM] -= 2;
				n_conversions[HOME_TEAM] -= 2;
				n_conversions[AWAY_TEAM] -= 2;
				change_made = true;
			}

		}

		// If we STILL haven't succeeded we'll remove a bunch of penalties instead.
		if( !change_made && n_successful_penalty_kicks[HOME_TEAM] >= 8 && n_successful_penalty_kicks[AWAY_TEAM] >= 8 )
		{
			n_successful_penalty_kicks[HOME_TEAM] -= 6;
			n_successful_penalty_kicks[AWAY_TEAM] -= 6;
			change_made = true;
		}

		// How about just a converted try each?
		if( !change_made && n_tries[HOME_TEAM] >= 1 && n_tries[AWAY_TEAM] >= 1 )
		{
			if( n_conversions[HOME_TEAM] >= 1 && n_conversions[AWAY_TEAM] >= 1 )
			{
				n_tries[HOME_TEAM] -= 1;
				n_tries[AWAY_TEAM] -= 1;
				n_conversions[HOME_TEAM] -= 1;
				n_conversions[AWAY_TEAM] -= 1;
				change_made = true;
			}

		}

		// Srsly? Still nothing? Fine. Just remove a try each, surely we can do THAT
		if( !change_made && n_tries[HOME_TEAM] >= 1 && n_tries[AWAY_TEAM] >= 1 )
		{
			n_tries[HOME_TEAM] -= 1;
			n_tries[AWAY_TEAM] -= 1;
			change_made = true;
		}

		// Otherwise nevermind. Give up.

	}


	//=================================================================================
	// Add all of the necessary scores etc... for both teams
	//=================================================================================

	// RC2 version...

	// We dont want to start a new game for the stats, since we're simming a current game, we're just going to add on top of the existing data.
	//statistics->NewGame( database->GetTeam(team_ids[HOME_TEAM]), database->GetTeam(team_ids[AWAY_TEAM]), competition_instance.GetDbId(), match.GetDbId());

	// This is the second half, 0 would be the first
	// TODO:
	//statistics->SetHalf( 1 );
	// set the teams
	// TODO:
	//statistics->SetTeams( team_ids[HOME_TEAM], team_ids[AWAY_TEAM], 80 );	// Stats are based on an 80 minute game

	short team_scores[2] = { 0, 0 };
	SSTEAMSIDE team_sides[2] = { SIDE_A, SIDE_B };

	for( int team_index = 0; team_index < 2; team_index++ )
	{
		RL3DB_TEAM current_team = database->GetTeam( team_ids[team_index] );
		SSTEAMSIDE team_id = team_sides[team_index];
		SSTEAMSIDE opposing_team_id = team_sides[(team_index+1)&1];

		// Add tries and conversions
		for( int i = 0; i < n_tries[team_index]; i++ )
		{
			// Pick a random player to score the try
			int playersPerTeam = (int)on_field_players[team_index].size();//SIFApplication::GetApplication()->GetGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			//const unsigned short player_id = current_team.GetPlayer(player_index).index;
			const unsigned short player_id = on_field_players[team_index][player_index];

			statistics->AddTry(team_id, opposing_team_id, player_id, true);
			team_scores[team_index] += try_score;
			if ( i < n_conversions[team_index] )
			{
				//statistics->AddConversion( team_id, opposing_team_id, current_team.GetPlayer(goal_kicker_index[team_index]).index, true );
				statistics->AddConversion( team_id, opposing_team_id, on_field_players[team_index][goal_kicker_index[team_index]], true );
				team_scores[team_index] += conversion_score;
			}
			else
			{
				//statistics->AddConversion(team_id, opposing_team_id, current_team.GetPlayer(goal_kicker_index[team_index]).index, false );
				statistics->AddConversion( team_id, opposing_team_id, on_field_players[team_index][goal_kicker_index[team_index]], false );
			}
		}

		// Add penalties
		for( int i = 0; i < n_penalty_kicks[team_index]; i++ )
		{
			if ( i < n_successful_penalty_kicks[team_index] )
			{
				//statistics->AddPenaltyGoal(team_id, opposing_team_id, current_team.GetPlayer(goal_kicker_index[team_index]).index, true );
				statistics->AddPenaltyGoal(team_id, opposing_team_id, on_field_players[team_index][goal_kicker_index[team_index]], true );
				team_scores[team_index] += penalty_score;
			}
			else
			{
				//statistics->AddPenaltyGoal(team_id, opposing_team_id, current_team.GetPlayer(goal_kicker_index[team_index]).index, false );
				statistics->AddPenaltyGoal(team_id, opposing_team_id, on_field_players[team_index][goal_kicker_index[team_index]], false );
			}
		}

		// Add drop goals
		for( int i = 0; i < n_drop_goals[team_index]; i++ )
		{
			unsigned short kicker_id;
			if ( MabMath::Rand( 1.0f ) < 0.7f )
			{
				//kicker_id = current_team.GetPlayer(goal_kicker_index[team_index]).index;
				kicker_id = on_field_players[team_index][goal_kicker_index[team_index]];
			}
			else
			{
				//kicker_id = current_team.GetPlayer(play_kicker_index[team_index]).index;
				kicker_id = on_field_players[team_index][play_kicker_index[team_index]];
			}
			statistics->AddDropGoal(team_id, opposing_team_id, kicker_id, true);
			team_scores[team_index] += drop_goal_score;
		}
	}

	season_match.GetMatch().SetResult( team_scores[ 0 ], team_scores[ 1 ]);

	//	MABLOGDEBUG( "HAttack: %f ADefence: %f HTries: %d HConv: %d HPen: %d HFg: %d Total: %d", team_attack_factor[0], team_defence_factor[1], n_tries[0], n_conversions[0], n_successful_penalty_kicks[0], n_drop_goals[0], team_scores[0] );
	//	MABLOGDEBUG( "AAttack: %f HDefence: %f ATries: %d AConv: %d APen: %d AFg: %d Total: %d", team_attack_factor[1], team_defence_factor[0], n_tries[1], n_conversions[1], n_successful_penalty_kicks[1], n_drop_goals[1], team_scores[1] );


	//------------------------------------------------------------
	// Generate other game stats
	//------------------------------------------------------------

	int n_penalties[2] = {0,0};
	//int n_six_tackle_sets[2] = {0,0};
	//int n_six_tackle_sets_completed[2] = {0,0};
	int n_tackle_attempts[2] = {0,0};
	int n_successful_tackle_attempts[2] = {0,0};
	int n_hitups[2] = {0,0};
	int n_scrums[2] = {0,0};
	int n_line_breaks[2] = {0,0};
	int n_offloads[2] = {0,0};
	int n_handling_errors[2] = {0,0};
	int n_kicks[2] = {0,0};
	//int n_dummy_half_runs[2] = {0,0};
	int n_yellow_cards[2] = {0,0};
	int n_red_cards[2] = {0,0};
	int n_injuries[2] = {0,0};
	int possession_percentage[2] = {0,0};
	int territory_percentage[2] = {0,0};

	RL3TournamentConstants* tournament_constants = tournament_manager->GetTournamentConstants();

	int sim_min_penalties					= (int)((float)tournament_constants->GetSimMinPenalties(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_penalties					= (int)((float)tournament_constants->GetSimMaxPenalties(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	int sim_min_tackles						= (int)((float)tournament_constants->GetSimMinTackles(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_tackles						= (int)((float)tournament_constants->GetSimMaxTackles(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	float sim_successful_tackles_variance	= tournament_constants->GetSimSuccessfulTacklesVariance(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining;

	int sim_min_hitups						= (int)((float)tournament_constants->GetSimMinHitups(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_hitups						= (int)((float)tournament_constants->GetSimMaxHitups(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	int sim_min_line_breaks					= (int)((float)tournament_constants->GetSimMinLineBreaks(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_line_breaks					= (int)((float)tournament_constants->GetSimMaxLineBreaks(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	int sim_min_offloads					= (int)((float)tournament_constants->GetSimMinOffloads(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_offloads					= (int)((float)tournament_constants->GetSimMaxOffloads(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	int sim_min_handling_errors				= (int)((float)tournament_constants->GetSimMinHandlingErrors(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_handling_errors				= (int)((float)tournament_constants->GetSimMaxHandlingErrors(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	int sim_min_kicks						= (int)((float)tournament_constants->GetSimMinKicks(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_kicks						= (int)((float)tournament_constants->GetSimMaxKicks(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	float sim_yellow_card_chance			= tournament_constants->GetSimYellowCardChance(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining;
	float sim_red_card_chance				= tournament_constants->GetSimRedCardChance(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining;

	float sim_first_injury_chance			= tournament_constants->GetSimFirstInjuryChance(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining;
	float sim_second_injury_chance			= tournament_constants->GetSimSecondInjuryChance(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining;

	int	sim_min_run_metres_gained			= (int)((float)tournament_constants->GetSimMinRunMetresGained(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_run_metres_gained			= (int)((float)tournament_constants->GetSimMaxRunMetresGained(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

	int sim_min_kick_metres_gained_per_kick = (int)((float)tournament_constants->GetSimMinKickMetresGainedPerKick(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);
	int sim_max_kick_metres_gained_per_kick = (int)((float)tournament_constants->GetSimMaxKickMetresGainedPerKick(tournament_manager->GetIsCareerGameModeR13()) * percMatchRemaining);

#ifdef ENABLE_GAME_DEBUG_MENU
	if(SIFDebug::GetCareerModeDebugSettings()->GetNoSimulationCards())
	{
		sim_yellow_card_chance = 0.0f;
		sim_red_card_chance = 0.0f;
	}
	if(SIFDebug::GetCareerModeDebugSettings()->GetNoSimulationInjuries())
	{
		sim_first_injury_chance = 0.0f;
		sim_second_injury_chance = 0.0f;
	}
#endif

	/// Enable/Disable injuries/suspension depending on custom game settings.

	if(competition_definition.GetIsFriendly())
	{
		sim_first_injury_chance = 0.0f;
		sim_second_injury_chance = 0.0f;
		sim_yellow_card_chance = 0.0f;
		sim_red_card_chance = 0.0f;
	}
	else
	{
		RUGameSettings *game_settings = SIFApplication::GetApplication()->GetMatchGameSettings();
		if(!game_settings->game_settings.custom_rule_injuries)
		{
			sim_first_injury_chance = 0.0f;
			sim_second_injury_chance = 0.0f;
		}

		if(!game_settings->game_settings.custom_rule_sendoffs_enabled)
		{
			sim_red_card_chance = 0.0f;
		}

		if(!game_settings->game_settings.custom_rule_sinbins_enabled)
		{
			sim_yellow_card_chance = 0.0f;
		}
	}


	// Calculate the possession and territory for the current half we're playing

	int first_choice_percentage_team = MabMath::RandInt( 2 );
	int other_team = 1 - first_choice_percentage_team;

	possession_percentage[first_choice_percentage_team] = 40 + (int)( MabMath::RandInt(40) * team_attack_factor[first_choice_percentage_team] );
	territory_percentage[first_choice_percentage_team] = 40 + (int)( MabMath::RandInt(40) * team_attack_factor[first_choice_percentage_team] );

	// We should already have some stats for the game we're playing. Add it on to the simmed stats.
	possession_percentage[first_choice_percentage_team]		+= existing_possession_percentage[first_choice_percentage_team];
	territory_percentage[first_choice_percentage_team]		+= existing_territory_percentage[first_choice_percentage_team];

	// Scale it back since we added another 100%
	possession_percentage[first_choice_percentage_team]		= (int)((float)possession_percentage[first_choice_percentage_team] / 2.0f);
	territory_percentage[first_choice_percentage_team]		= (int)((float)territory_percentage[first_choice_percentage_team]  / 2.0f);

	// Work out the other team's stats
	possession_percentage[other_team] = 100 - possession_percentage[first_choice_percentage_team];
	territory_percentage[other_team] = 100 - territory_percentage[first_choice_percentage_team];

	for( int team_index = 0; team_index < 2; team_index++ )
	{
		// Generate the team totals for everything
		n_penalties[team_index] = sim_min_penalties + MabMath::RandInt( sim_max_penalties - sim_min_penalties + 1 );
		//n_six_tackle_sets[team_index] = sim_min_six_tackle_sets + MabMath::RandInt( sim_max_six_tackle_sets - sim_min_six_tackle_sets + 1 );
		//n_six_tackle_sets_completed[team_index] = MabMath::RandInt( n_six_tackle_sets[team_index] + 1 );
		//MabMath::ClampLower( n_six_tackle_sets_completed[team_index], (int)(n_six_tackle_sets[team_index] * 0.2f) );
	}

	for( int team_index = 0; team_index < 2; team_index++ )
	{
		// Tackles
		n_successful_tackle_attempts[team_index] = sim_min_tackles + MabMath::RandInt( sim_max_tackles - sim_min_tackles + 1 );
		// Make sure we don't get less tackles than possible with the oppositions 6 tackle sets. Add 20 for good measure =D
		//MabMath::ClampLower( n_successful_tackle_attempts[team_index], ( n_six_tackle_sets[1-team_index] * 6 ) + 20 );
		// Now generate the number of attempted tackles from this
		n_tackle_attempts[team_index] =
			// Add to the number of tackles we succeeded in
			n_successful_tackle_attempts[team_index] +
			(int)
			(
			// We're taking the inverse of the defence factor to see how many they missed
			( ( 1 - team_defence_factor[team_index] ) +
			( MabMath::Rand( sim_successful_tackles_variance ) - sim_successful_tackles_variance / 2.0f )
			// Divide by 3 cos we don't want too many missed tackles
			) / 3.0f *
			n_successful_tackle_attempts[team_index]
		);

		// Hitups
		n_hitups[team_index] = sim_min_hitups + (int)(team_attack_factor[team_index] * MabMath::RandInt( sim_max_hitups - sim_min_hitups + 1 ) );

		// TODO: make something up for this
		//n_scrums[team_index] = MabMath::RandInt( n_six_tackle_sets_completed[team_index] );

		n_line_breaks[team_index] = sim_min_line_breaks + (int)(team_attack_factor[team_index] * MabMath::RandInt( sim_max_line_breaks - sim_min_line_breaks + 1 ) );

		n_offloads[team_index] = sim_min_offloads + (int)(team_attack_factor[team_index] * MabMath::RandInt( sim_max_offloads - sim_min_offloads + 1 ) );

		n_handling_errors[team_index] = sim_min_handling_errors + (int)( ( 1 - team_attack_factor[team_index] ) * MabMath::RandInt( sim_max_handling_errors - sim_min_handling_errors + 1 ) );

		n_kicks[team_index] = sim_min_kicks + (int)(team_attack_factor[team_index] * (1 - team_defence_factor[1 - team_index]) * MabMath::RandInt( sim_max_kicks - sim_min_kicks + 1 ) );

		//n_dummy_half_runs[team_index] = sim_min_dummy_runs + (int)(team_attack_factor[team_index] * MabMath::RandInt( sim_max_dummy_runs - sim_min_dummy_runs + 1 ) );

		n_yellow_cards[team_index] = 0;
		if ( MabMath::Rand( 1.0f ) < sim_yellow_card_chance ) n_yellow_cards[team_index]++;
		if ( MabMath::Rand( 1.0f ) < sim_yellow_card_chance ) n_yellow_cards[team_index]++;

		n_red_cards[team_index] = 0;
		if ( MabMath::Rand( 1.0f ) < sim_red_card_chance ) n_red_cards[team_index]++;
		if ( MabMath::Rand( 1.0f ) < sim_red_card_chance ) n_red_cards[team_index]++;

		n_injuries[team_index] = 0;
		if ( MabMath::Rand( 1.0f ) < sim_first_injury_chance ) n_injuries[team_index]++;
		if ( MabMath::Rand( 1.0f ) < sim_second_injury_chance ) n_injuries[team_index]++;


		SSTEAMSIDE team_id = team_sides[team_index];		//match_teams[team_index].GetDbId();

		// Add scrums
		for( int k = 0; k < n_scrums[team_index]; k++ )
		{
			statistics->AddScrum( team_id );
		}
		// Set the first half possession and territory percentages
		//statistics->SetHalf( FIRST_HALF );
		statistics->AddHalf(team_id, 0, possession_percentage[ team_index ] / 100.0f, territory_percentage[ team_index ] / 100.0f);
		// Set the second half possession and territory percentages
		//statistics->SetHalf( SECOND_HALF );
		//statistics->AddHalf(team_id, 0, possession_percentage[ team_index ] / 100.0f, territory_percentage[ team_index ] / 100.0f);
	}

	MAB_PROFILE_SECTION_END(profile1);
	MAB_PROFILE_SECTION_START(profile2, "TSim(2)");

	// Now add them to the stats system
	for (int team_index = 0; team_index < 2; team_index++)
	{
		RL3DB_TEAM team = database->GetTeam( team_ids[team_index] );
		unsigned short player_db_id;
		SSTEAMSIDE team_side = team_sides[team_index];			//current_team.GetDbId();

		int playersPerTeam = (int)on_field_players[team_index].size();
		//Sanity check to see who we're simming for
		for(int pp = 0; pp < playersPerTeam; pp ++)
		{
			//unsigned short player_id = team.GetPlayer(pp).index;
			unsigned short player_id = on_field_players[team_index][pp];
			RL3DB_PLAYER playa(player_id);
			MABLOGDEBUG("Simming for team %i's player(%i): %s", team_index, player_id, playa.GetName().c_str());
		}

		// Add tackles
		for( int k = 0; k < n_tackle_attempts[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			//unsigned short player_id = team.GetPlayer(player_index).index;
			unsigned short player_id = on_field_players[team_index][player_index];

			if ( k < n_successful_tackle_attempts[team_index] )
			{
				statistics->AddTackle( team_side, player_id, true, false );
			}
			else
			{
				statistics->AddTackle( team_side, player_id, false, false );
			}
		}

		//  Add penalties
		for( int k = 0; k < n_penalties[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			//unsigned short player_id = team.GetPlayer(player_index).index;
			unsigned short player_id = on_field_players[team_index][player_index];

			statistics->AddPenalty( team_side, player_id );
		}

		// Add hitups
		for( int k = 0; k < n_hitups[team_index]; k++ )
		{
			int	player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			//unsigned short player_id = team.GetPlayer(player_index).index;
			unsigned short player_id = on_field_players[team_index][player_index];

			statistics->AddHitup( team_side, player_id );
		}

		// Add Line Breaks
		for( int k = 0; k < n_line_breaks[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			//unsigned short player_id = team.GetPlayer(player_index).index;
			unsigned short player_id = on_field_players[team_index][player_index];

			statistics->AddLineBreak( team_side, player_id );
		}

		// Add offloads
		for( int k = 0; k < n_offloads[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			//unsigned short player_id = team.GetPlayer(player_index).index;
			unsigned short player_id = on_field_players[team_index][player_index];

			statistics->AddPass(team_side, player_id, true, true);
		}

		// Add handling error
		for( int k = 0; k < n_handling_errors[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			//unsigned short player_id = team.GetPlayer(player_index).index;
			unsigned short player_id = on_field_players[team_index][player_index];

			statistics->AddHandlingError( team_side, player_id );
		}

		// Add kicks
		for ( int k = 0; k < n_kicks[team_index]; k++ )
		{
			// See which player will perform this kick:
			int player_index = -1;
			int random_kicker_id = MabMath::RandInt(10);
			if (random_kicker_id < 3)		// Full back
			{
				player_index = PlayerPositionEnum::GetStartingJerseyNumberFromPlayerPosition(PP_FULLBACK) - 1;
			}
			else if (random_kicker_id < 5)	// Half back
			{
				player_index = PlayerPositionEnum::GetStartingJerseyNumberFromPlayerPosition(PP_SCRUM_HALF) - 1;
			}
			else if (random_kicker_id < 7)	// Five-eighth
			{
				player_index = PlayerPositionEnum::GetStartingJerseyNumberFromPlayerPosition(PP_FLY_HALF_STAND_OFF) - 1;
			}
			else if (random_kicker_id < 9)	// Play kicker
			{
				player_index = play_kicker_index[team_index];
			}

			if (player_index == -1)
			{
				player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			}

			//unsigned short player_id = team.GetPlayer(player_index).index;
			unsigned short player_id = on_field_players[team_index][player_index];
			statistics->AddKick( team_side, player_id );

			// Add some kicking metres gained
			RL3DB_PLAYER player(player_id );
			float metres_gained = sim_min_kick_metres_gained_per_kick + static_cast<float>( player.GetGeneralKickAccuracy() ) / MAX_PLAYER_ATTRIBUTE_VALUE * MabMath::RandInt( sim_max_kick_metres_gained_per_kick - sim_min_kick_metres_gained_per_kick + 1);
			statistics->AddKickingMetresGained( team_side, player_id, metres_gained );
		}

		// TODO: RL specific?
		//// Add dummy half runs
		//for ( int k = 0; k < n_dummy_half_runs[team_index]; k++ )
		//{
		//	unsigned short player_id = GetRandomPlayerId(current_players);
		//	statistics->AddDummyHalfRun( team_id, player_id );
		//}

		// Add running metres gained
		for ( int k = 0; k < playersPerTeam; k++ )
		{
			//player_db_id = team.GetPlayer(k).index;
			player_db_id = on_field_players[team_index][k];

			RL3DB_PLAYER player = database->GetPlayer( player_db_id );
			float charge_stats = static_cast<float>( player.GetAgility() + player.GetAggressiveness() + player.GetBreakTackleAbility() + player.GetSpeed() );
			float metres_gained = sim_min_run_metres_gained + ( charge_stats / (float) ( 4 * MAX_PLAYER_ATTRIBUTE_VALUE ) ) * MabMath::RandInt( sim_max_run_metres_gained - sim_min_run_metres_gained + 1 );
			statistics->AddRunningMetresGained( team_sides[team_index], player_db_id, metres_gained );
		}

		// Add injuries (do before suspensions)
		for( int k = 0; k < n_injuries[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			//player_db_id = team.GetPlayer(player_index).index;
			player_db_id = on_field_players[team_index][player_index];

			statistics->AddInjury( team_sides[team_index], player_db_id );

			RL3DB_PLAYER player = database->GetPlayer( player_db_id );

			switch (MabMath::RandInt(3))
			{
			case 0	:	player.SetInjury(INJURY_PENDING_UPPER); break;
			case 1	:	player.SetInjury(INJURY_PENDING_MID); break;
			case 2	:	player.SetInjury(INJURY_PENDING_LOWER); break;
			}
		}

		// Add yellow cards
		for( int k = 0; k < n_yellow_cards[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			//player_db_id = team.GetPlayer(player_index).index;
			player_db_id = on_field_players[team_index][player_index];

			statistics->AddYellowCard( team_sides[team_index], player_db_id );
		}

		// Add red cards
		for( int k = 0; k < n_red_cards[team_index]; k++ )
		{
			int player_index = MabMath::RandInt( playersPerTeam + 1 ) % playersPerTeam;
			//player_db_id = team.GetPlayer(player_index).index;
			player_db_id = on_field_players[team_index][player_index];

			statistics->AddRedCard( team_sides[team_index], player_db_id );

			RL3DB_PLAYER player = database->GetPlayer( player_db_id );

			player.SetSuspension(SUSP_PENDING_RANDOM);
		}
	}

	MAB_PROFILE_SECTION_END(profile2);
	MAB_PROFILE_SECTION_START(profile3, "TSim(3)");

	// Add the stats to the permanent system, but not if it's a trial match
	/*if( !competition_definition.GetIsFriendly())
	{
		if(!tournament_manager->IsLionsTour() || (tournament_manager->IsLionsTour() && (home_team.GetDbId()==DB_TEAMID_AUSTRALIA || away_team.GetDbId()==DB_TEAMID_AUSTRALIA)))
		{
			bool is_preliminary_round = competition_instance.GetCurrentRound() < competition_definition.GetNumPreliminaryRounds();

			statistics->CalculatePlayerPerformances();
			statistics->AddCompetitionStats(competition_instance.GetDbId(), is_preliminary_round);
		}
	}*/
	MAB_PROFILE_SECTION_END(profile3);
#endif
}










///******************************************************************************************************************
///******************************************************************************************************************



RL3_SEASON_MATCH::RL3_SEASON_MATCH()
{
	comp_index = 0xff;
	round_num = 0;
	match_num = 0;
}

bool RL3_SEASON_MATCH::IsNull() const
{
	return comp_index==0xff;
}

RL3DB_MATCH RL3_SEASON_MATCH::GetMatch() const
{
	MABASSERT(comp_index!=0xff);
	return match;
}

/// The index into the competitions array in the Tournament object that this match comes from
unsigned char RL3_SEASON_MATCH::GetCompIndex() const
{
	MABASSERT(comp_index!=0xff);
	return comp_index;
}

/// The round number this match is from
unsigned char RL3_SEASON_MATCH::GetRoundNum() const
{
	MABASSERT(comp_index!=0xff);
	return round_num;
}

/// The match number in this round.
unsigned char RL3_SEASON_MATCH::GetMatchNum() const
{
	MABASSERT(comp_index!=0xff);
	return match_num;
}




///******************************************************************************************************************
///******************************************************************************************************************




RL3_SEASON_MATCHES::RL3_SEASON_MATCHES(RL3Tournament *ttournament)
{
	tournament = ttournament;
}

int RL3_SEASON_MATCHES::GetNumMatches()
{
	return tournament->GetNumSeasonMatches();
}

int RL3_SEASON_MATCHES::GetCurrentMatchIndex()
{
	return tournament->GetCurrentSeasonMatchIndex();
}

void RL3_SEASON_MATCHES::SetCurrentMatchIndex(int value)
{
	tournament->SetCurrentSeasonMatchIndex(value);
}

RL3_SEASON_MATCH RL3_SEASON_MATCHES::GetMatch(int index)
{
	return tournament->GetSeasonMatch(index);
}
