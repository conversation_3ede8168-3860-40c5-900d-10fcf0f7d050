/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/Rules/Triggers/RURuleTriggerAdvantage.h"

#include "Match/Ball/SSBall.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Debug/RUGameDebugSettings.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameGetToBall.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUInputPhaseDecision.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/Offside/RUOffsideIndicator.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/RugbyUnion/Rules/RURulesDebugSettings.h"
#include "Match/RugbyUnion/Rules/RURulesDebugSettings.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSSpatialHelper.h"
#include "Utility/RURandomNumberGenerator.h"
#include "Utility/Helpers/SIFGameHelpers.h"


#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
#include "Rugby/Utility/consoleVars.h"
#include <DrawDebugHelpers.h>
#endif

//#rc3_legacy_include #include "SIFDebug.h"

MABRUNTIMETYPE_IMP1(RURuleTriggerAdvantage, RURuleTrigger);

const static float ADV_OVER_MIN_DIST_TRAVELLED_FOR_PENALTY = 10.0f;
const static float ADV_OVER_MIN_DIST_FORWARD_FOR_PENALTY = 8.0f;
const static float ADV_OVER_MIN_DIST_TRAVELLED_FOR_KNOCK_ON = 5.0f;
const static float ADV_OVER_MIN_DIST_FORWARD_FOR_KNOCK_ON = 4.0f;
// const static float ADV_OVER_MIN_DIST_FORWARD_MIN = 10.0f;
// const static float ADV_OVER_MIN_DIST_FORWARD_MAX = 15.0f;
const static float ADV_INFRINGEMENT_TIMER = 1.0f;

RURuleTriggerAdvantage::RURuleTriggerAdvantage(RURules* rules)
: RURuleTrigger(rules)
, player_info_delay()
, restart_position(0.0f, 0.0f, 0.0f)
, restart_player(NULL)
, offending_player(NULL)
, breakdown_winning_team( NULL )
, is_active( false )
, state( ADVANTAGE_INVALID )
, notify_started(false)
, from_forward_pass(false)
, from_offside(false)
{
}

RURuleTriggerAdvantage::~RURuleTriggerAdvantage()
{
}

void RURuleTriggerAdvantage::AttachMonitors()
{
	RUGameEvents* events = game->GetEvents();
	MABASSERTMSG(events,"we need events here");
	events->knock_on.Add(this, &RURuleTriggerAdvantage::KnockOn);
	events->try_awarded.Add(this, &RURuleTriggerAdvantage::TryScore);
	events->kick.Add(this, &RURuleTriggerAdvantage::Kick);
	events->pass.Add(this, &RURuleTriggerAdvantage::Pass);
	events->caught.Add(this, &RURuleTriggerAdvantage::Caught);
	events->tackle.Add(this, &RURuleTriggerAdvantage::Tackle);
	events->maul_formed.Add( this, &RURuleTriggerAdvantage::MaulFormed );
	events->ruck_formed.Add( this, &RURuleTriggerAdvantage::RuckFormed );
	events->ruck_ball_released.Add( this, &RURuleTriggerAdvantage::RuckBallReleased );
	events->maul_ball_released.Add( this, &RURuleTriggerAdvantage::MaulBallReleased );
	events->tacklee_on_ground.Add(this, &RURuleTriggerAdvantage::OnTackleeOnGround);
	events->try_denied_from_advantage.Add(this, &RURuleTriggerAdvantage::OnDenyTry);
	events->ball_out_detected.Add(this, &RURuleTriggerAdvantage::BallOut);
}

void RURuleTriggerAdvantage::DeattachMonitors()
{
	RUGameEvents* events = game->GetEvents();
	if (events)
	{
		events->knock_on.Remove(this, &RURuleTriggerAdvantage::KnockOn);
		events->try_awarded.Remove(this, &RURuleTriggerAdvantage::TryScore);
		events->kick.Remove(this, &RURuleTriggerAdvantage::Kick);
		events->pass.Remove(this, &RURuleTriggerAdvantage::Pass);
		events->caught.Remove(this, &RURuleTriggerAdvantage::Caught);
		events->tackle.Remove(this, &RURuleTriggerAdvantage::Tackle);
		events->maul_formed.Remove( this, &RURuleTriggerAdvantage::MaulFormed );
		events->ruck_formed.Remove( this, &RURuleTriggerAdvantage::RuckFormed );
		events->ruck_ball_released.Remove( this, &RURuleTriggerAdvantage::RuckBallReleased );
		events->maul_ball_released.Remove( this, &RURuleTriggerAdvantage::MaulBallReleased );
		events->tacklee_on_ground.Remove(this, &RURuleTriggerAdvantage::OnTackleeOnGround);
		events->try_denied_from_advantage.Remove(this, &RURuleTriggerAdvantage::OnDenyTry);
		events->ball_out_detected.Remove(this, &RURuleTriggerAdvantage::BallOut);
	}
}

void RURuleTriggerAdvantage::Enter()
{
	if(!is_active)
	{
		is_active = true;
		state = ADVANTAGE_FIRST_INFRINGEMENT;
		//ADV_OVER_MIN_DIST_FORWARD = ADV_OVER_MIN_DIST_FORWARD_MIN + game->GetRNG()->RAND_CALL(float) * (ADV_OVER_MIN_DIST_FORWARD_MAX - ADV_OVER_MIN_DIST_FORWARD_MIN);

		/// Set The advantage hud elements
		game->GetEvents()->advantage_to(restart_team);

#ifdef ENABLE_OSD
		RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
		MabString activation_string( 256, "Advantage to %s at %0.1fs", restart_team->GetDbTeam().GetName(), game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds() );
		settings->PushDebugString( game, RUGameDebugSettings::DP_KNOCKON, activation_string.c_str() );
		MABLOGDEBUG( activation_string.c_str() );
#endif


		/// indicate that we need to notify that we've started advantage on the next update
		/// as notifying inside another game event can cause ordering issues for commentary
		/// that listens to both events (it was receiving the advantage started event before the knock on event)
		notify_started = true;

		const MabLockStepTimeSource* time_step = rules->GetGame()->GetSimTime();
		timer.Initialise( time_step, ADV_INFRINGEMENT_TIMER);
		timer.SetEnabled( true );
		player_info_delay.Initialise( time_step, 4.0f );
		player_info_delay.SetEnabled(false);

#ifdef ENABLE_DEBUG_RULE_MONITORING
		//SIF_DEBUG_DRAW( SetText( 13241234, 100, 100, "ADVANTAGE" ) );
		MABLOGDEBUG( "RURuleTriggerAdvatage::Enter: team(%s), state(%d) at %0.1fs", restart_team->GetDbTeam().GetName(), ADVANTAGE_FIRST_INFRINGEMENT, game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds());
#endif
	}
}

void RURuleTriggerAdvantage::Exit()
{
	if (state != ADVANTAGE_OVER && state != ADVANTAGE_INVALID)
	{
		#ifdef ENABLE_OSD
				RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
				MabString activation_string( 256, "Advantage exit at %0.1fs", game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds() );
				settings->PushDebugString( game, RUGameDebugSettings::DP_KNOCKON, activation_string.c_str() );
				MABLOGDEBUG( activation_string.c_str() );
		#endif

		game->GetEvents()->advantage_ended(offending_player);
	}

	Reset();
	state = ADVANTAGE_INVALID;
	is_active = false;
	notify_started = false;

	offending_player = NULL;

	player_info_delay.Reset();
	player_info_delay.SetEnabled( false );

	/// Remove the advantage hud elements
#ifdef ENABLE_DEBUG_RULE_MONITORING
	//SIF_DEBUG_DRAW( RemoveText( 13241234 ) );
#endif
}

void RURuleTriggerAdvantage::Update(float)
{
	if ( !restart_team )
	{
		// Following logic depends on a valid restart_team. Bail.
		Reset();
		return;
	}

	if(state == ADVANTAGE_FIRST_INFRINGEMENT && notify_started)
	{
		MABLOGDEBUG( "RURuleTriggerAdvatage::Update: team(%s), state(%d)", restart_team->GetDbTeam().GetName(), state);

#ifdef ENABLE_OSD
		RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
		MabString activation_string( 256, "Advantage Started at %0.1fs", game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds() );
		settings->PushDebugString( game, RUGameDebugSettings::DP_KNOCKON, activation_string.c_str() );
		MABLOGDEBUG( activation_string.c_str() );
#endif

		game->GetEvents()->advantage_started(offending_player);
		notify_started = false;
	}

	// if there has been no advantage, or another infringement then start the consequence
	if (state == ADVANTAGE_NOT_TAKEN && timer.GetNumTimerEventsRaised() > 1)
	{
		advantage_timer_elapsed = true;
	}
	if(state == ADVANTAGE_SECOND_INFRINGEMENT /*&& timer.GetNumTimerEventsRaised() > 1*/)
	{
		DoAdvantageConsequence("RURuleTriggerAdvatage::Update");
		return;
	}
	else if( player_info_delay.GetNumTimerEventsRaised() > 0 && player_info_delay.GetEnabled() )
	{
		player_info_delay.Acknowledge();
		player_info_delay.Reset();
		player_info_delay.SetEnabled( false );

		MabString message( 64, "[ID_KNOCKED_ON_BY] %s", game->GetGameState()->GetLastOffendingPlayer()->GetAttributes()->GetTeam()->GetDbTeam().GetShortName() );

		if (game->GetHUDUpdater())
			game->GetHUDUpdater()->SetGenericInfo(SIFGameHelpers::GAConvertMabStringToFString(message), game->GetGameState()->GetLastOffendingPlayer()->GetAttributes()->GetTeam(), "[ID_SCRUM_AWARDED]" );

		rules->RemoveTrigger( this );
		return;
	}

	///----------------------------------------------------------------------------------------------------------
	/// WORK OUT IF WE HAVE NOT GOT AN ADVANTAGE
	/// After 15 secs no territorial advantage was made, or the offending team played the ball.
	///----------------------------------------------------------------------------------------------------------

	ERugbyPlayDirection pd = restart_team->GetPlayDirection();

	bool offending_team_has_ball = game->GetGameState()->GetBallHolder() && game->GetGameState()->GetBallHolder()->GetAttributes()->GetTeam() != restart_team;
	bool in_advantageous_position =	IsInAdvantageousPosition();


#if defined ENABLE_SIF_DEBUG_DRAW && defined ENABLE_GAME_DEBUG_MENU
	//#rc3_legacy_debu_draw 
	/*if( SIFDebug::GetRulesDebugSettings()->GetShowDebugInfo() )
	{
		SIF_DEBUG_DRAW(SetText(545646542, 200, 200, MabString(0, "Advantage T: %i", timer.GetNumTimerEventsRaised()).c_str(), MabColour::White));
	}*/
#endif

	static const int ADVANTAGE_MAX_TIME = 15;
	if (state == ADVANTAGE_FIRST_INFRINGEMENT && offending_team_has_ball)
	{
		MABLOGDEBUG("RURuleTriggerAdvatage::Update: team(%s), state(%d -> %d), timer events=%d, at %0.1fs", restart_team->GetDbTeam().GetName(), ADVANTAGE_FIRST_INFRINGEMENT, ADVANTAGE_NOT_TAKEN, timer.GetNumTimerEventsRaised(), game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds());
		state = ADVANTAGE_NOT_TAKEN;
		timer.Reset(ADV_INFRINGEMENT_TIMER);
		timer.SetEnabled(true);
		game->GetEvents()->no_advantage();
#ifdef ENABLE_OSD
		RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
		MabString activation_string(256, "No advantage from timer or change possession at %0.1fs", game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds());
		settings->PushDebugString(game, RUGameDebugSettings::DP_KNOCKON, activation_string.c_str());
		MABLOGDEBUG(activation_string.c_str());
#endif

		game->GetHUDUpdaterContextual()->Stop2DContextualDisplay();

		DoAdvantageConsequence("RURuleTriggerAdvatage::Offending team has ball");

#ifdef ENABLE_DEBUG_RULE_MONITORING
		//SIF_DEBUG_DRAW( SetText( 13241234, 100, 100, "NO_ADVANTAGE" ) );
#endif
		return;
	}
	if( state == ADVANTAGE_FIRST_INFRINGEMENT && (!in_advantageous_position && timer.GetNumTimerEventsRaised() > ADVANTAGE_MAX_TIME))
	{
		MABLOGDEBUG( "RURuleTriggerAdvatage::Update: team(%s), state(%d -> %d), timer events=%d, at %0.1fs", restart_team->GetDbTeam().GetName(), ADVANTAGE_FIRST_INFRINGEMENT, ADVANTAGE_NOT_TAKEN, timer.GetNumTimerEventsRaised(), game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds() );
		state = ADVANTAGE_NOT_TAKEN;
		timer.Reset(ADV_INFRINGEMENT_TIMER);
		timer.SetEnabled( true );
		game->GetEvents()->no_advantage();
		#ifdef ENABLE_OSD
		RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
		MabString activation_string( 256, "No advantage from timer or change possession at %0.1fs", game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds() );
		settings->PushDebugString( game, RUGameDebugSettings::DP_KNOCKON, activation_string.c_str() );
		MABLOGDEBUG( activation_string.c_str() );
		#endif

		game->GetHUDUpdaterContextual()->Stop2DContextualDisplay();

#ifdef ENABLE_DEBUG_RULE_MONITORING
		//SIF_DEBUG_DRAW( SetText( 13241234, 100, 100, "NO_ADVANTAGE" ) );
#endif
		return;
	}

	FVector ball_pos = game->GetBall()->GetCurrentPosition();

#if defined ENABLE_SIF_DEBUG_DRAW && defined ENABLE_GAME_DEBUG_MENU
	//#rc3_legacy_debug_draw
	/*if( SIFDebug::GetRulesDebugSettings()->GetShowDebugInfo() )
	{
		if(state == ADVANTAGE_FIRST_INFRINGEMENT)
		{
			FVector adv_start_l(-FIELD_WIDTH/2.0f, 0.05f, restart_position.z);
			FVector adv_start_r(FIELD_WIDTH/2.0f, 0.05f, restart_position.z);
			SIF_DEBUG_DRAW(Set3DLine(*********, adv_start_l, adv_start_r, MabColour::Red, MabColour::Red));

			FVector adv_end_l(-FIELD_WIDTH/2.0f, 0.05f, restart_position.z + ADV_OVER_MIN_DIST_TRAVELLED * pd);
			FVector adv_end_r(FIELD_WIDTH/2.0f, 0.05f, restart_position.z + ADV_OVER_MIN_DIST_TRAVELLED * pd);
			SIF_DEBUG_DRAW(Set3DLine(*********, adv_end_l, adv_end_r, MabColour::Green, MabColour::Green));
		}
	}*/
#endif

	
	// the ball has moved 10m and forward at least 5m, a territorial advantage has been taken
	// Updated so that more territorial advantage must be made before a penalty advantage is deemed worthwhile.
	const float ADV_OVER_MIN_DIST_TRAVELLED = from_offside ? ADV_OVER_MIN_DIST_TRAVELLED_FOR_PENALTY : ADV_OVER_MIN_DIST_TRAVELLED_FOR_KNOCK_ON;
	const float ADV_OVER_MIN_DIST_FORWARD = from_offside ? ADV_OVER_MIN_DIST_FORWARD_FOR_PENALTY : ADV_OVER_MIN_DIST_FORWARD_FOR_KNOCK_ON;

	float dist_travelled_forward = (ball_pos.z - restart_position.z) * pd;

	// TODO: Add in some tactical advantages.
	if( ( ball_pos - restart_position ).Magnitude() > ADV_OVER_MIN_DIST_TRAVELLED
		&& dist_travelled_forward > ADV_OVER_MIN_DIST_FORWARD
		&& (state == ADVANTAGE_FIRST_INFRINGEMENT || state == ADVANTAGE_NOT_TAKEN))
	{
		bool ball_in_possession = game->GetGameState()->GetBallHolder() && game->GetGameState()->GetBallHolder()->GetAttributes()->GetTeam() == restart_team;
		if (!offensive_kick_performed || ball_in_possession)
		{
			DoAdvantageOver("RURuleTriggerAdvatage::Update");
			return;
		}
	}
}

void RURuleTriggerAdvantage::SecondInfringement()
{
	if (is_active)
	{
		if (state == ADVANTAGE_FIRST_INFRINGEMENT || state == ADVANTAGE_NOT_TAKEN)
		{
#ifdef ENABLE_DEBUG_RULE_MONITORING
			MABLOGMSG(LOGCHANNEL_DEBUG, LOGTYPE_INFO, "RURuleTriggerAdvantage::SecondInfringement: Setting state to SecondInfringement");
#endif
			state = ADVANTAGE_SECOND_INFRINGEMENT;
			timer.Reset(ADV_INFRINGEMENT_TIMER);
			timer.SetEnabled(true);
		}
		else
		{
#ifdef ENABLE_DEBUG_RULE_MONITORING
			MABLOGMSG(LOGCHANNEL_DEBUG, LOGTYPE_INFO, "RURuleTriggerAdvantage::SecondInfringement: Can't set state due to already being in a conflicting state: %d", state);
#endif
		}
	}
}

void RURuleTriggerAdvantage::BallOut(ARugbyCharacter*, const FVector&, bool)
{
	if (is_active && (state == ADVANTAGE_FIRST_INFRINGEMENT || state == ADVANTAGE_NOT_TAKEN))
	{
		DoAdvantageConsequence("RURuleTriggerAdvatage:: Ball is out");
	}
}

void RURuleTriggerAdvantage::KnockOn(ARugbyCharacter* Inoffending_player, bool /*from_tackle*/, const FVector& position)
{
#ifdef ENABLE_DEBUG_RULE_MONITORING
	MABLOGMSG(LOGCHANNEL_DEBUG, LOGTYPE_INFO, "RulesTrigger Call: RURuleTriggerKnockOn::KnockOn: team(%s), active(%d)", Inoffending_player->GetAttributes()->GetOppositionTeam()->GetDbTeam().GetName(), is_active);
#endif
#ifdef ENABLE_OSD
	RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
	MabString activation_string( 256, "KNOCKON BY: %s at %0.1fs", Inoffending_player->GetAttributes()->GetTeam()->GetDbTeam().GetName(), game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds() );
	settings->PushDebugString( game, RUGameDebugSettings::DP_KNOCKON, activation_string.c_str() );
	MABLOGDEBUG( activation_string.c_str() );
#endif


	// if we already have a knock on we are tracking then we keep that information
	if(!is_active)
	{

		if ( game->GetRules()->IsPlaySuspended() || !Inoffending_player)
			return;

		game->GetGameState()->SetLastOffendingPlayer(Inoffending_player);

		restart_team = Inoffending_player->GetAttributes()->GetOppositionTeam();
		restart_position = position;

		FieldExtents extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
		MabMath::Clamp( restart_position.z, -(extents.y/2.0f - 5.0f), (extents.y/2.0f - 5.0f) );
		MabMath::Clamp( restart_position.x, -(extents.x/2.0f - 5.0f), (extents.x/2.0f - 5.0f) );

		from_forward_pass = false;
		from_offside = false;

		#ifdef ENABLE_OSD
				RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
				MabString activation_string( 256, "KNOCKON SetTrigger: %s at %0.1f", Inoffending_player->GetAttributes()->GetTeam()->GetDbTeam().GetName(), game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds() );
				settings->PushDebugString( game, RUGameDebugSettings::DP_KNOCKON, activation_string.c_str() );
				MABLOGDEBUG( activation_string.c_str() );
		#endif

		consequence = RUC_SCRUM;
		this->offending_player = Inoffending_player;
		rules->SetTrigger(this);
	}
	else
	{
		if(Inoffending_player && Inoffending_player->GetAttributes()->GetTeam() == restart_team )
		{
			#ifdef ENABLE_OSD
						RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
						MabString activation_string( 256, "KNOCKON Second infringement at %0.1f", game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds() );
						settings->PushDebugString( game, RUGameDebugSettings::DP_KNOCKON, activation_string.c_str() );
						MABLOGDEBUG( activation_string.c_str() );
			#endif
			SecondInfringement(); // force the last infringement to start
		}
	}
}


void RURuleTriggerAdvantage::Caught(ARugbyCharacter* catching_player, bool /*player_was_in_air*/, int /*number_of_bounces*/)
{
	// Some new experimental code for forward passes, calculated when we catch the ball now, instead of when we pass.
#if defined(EXPERIMENTAL_FORWARD_PASS)

#ifdef ENABLE_DEBUG_RULE_MONITORING
	MABLOGMSG(LOGCHANNEL_DEBUG, LOGTYPE_INFO, "RulesTrigger Call: RURuleTriggerForwardPass::Caught");
#endif

	const BallFreeInfo& bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	MABASSERT(bfi.last_player);

	// Make sure advantage consequences are triggered if we kick the ball and turn it over. 
	// Previously, advantage was not being triggered in some cases where the opponent team marked the ball.
	if (bfi.event == BFE_KICK && is_active && (state == ADVANTAGE_FIRST_INFRINGEMENT || state == ADVANTAGE_NOT_TAKEN))
	{
		bool offending_team_has_ball = catching_player->GetAttributes()->GetTeam() != restart_team;
		if (offending_team_has_ball)
		{
			DoAdvantageConsequence("RURuleTriggerAdvatage::Opponent Marked Ball");
			return;
		}
	}

	// Only care about receiving passes
	if( bfi.event != BFE_PASS )
		return;

	ARugbyCharacter* passing_player = bfi.last_player;

	// Make sure we caught a ball from our team mate, otherwise it was an intercept. No forward passing for that!
	if( bfi.last_player->GetAttributes()->GetTeam() != catching_player->GetAttributes()->GetTeam())
		return;

	if( !is_active && passing_player )
	{
		bool force_forward_pass = false;
#ifdef ENABLE_TRIGGER_RULES_DEBUG_SETTINGS
		force_forward_pass = SIFDebug::GetRulesDebugSettings()->GetNextPassIsForward();
#endif

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
		const int32 ForwardPassDebugVal = CForwardPassDebug.GetValueOnGameThread();
		if (ForwardPassDebugVal)
		{
			force_forward_pass = true;
		}
#endif
		// Apply custom rules.
		if(!force_forward_pass && !game->GetGameSettings().game_settings.custom_rule_forward_pass)
			return;

		if(game->GetGameState()->IsGameInStandardPlay())
		{
			const static float FORWARD_PASS_MARGIN = 0.1f;	// add a margin to account for throwing from infront of you

			ERugbyPlayDirection pass_play_dir = passing_player->GetAttributes()->GetTeam()->GetPlayDirection();

			// Debug draw where we passed from, and where we received from.
			FVector pass_position = bfi.pos;
			pass_position.y = 0.0f;
			pass_position.z += FORWARD_PASS_MARGIN * pass_play_dir;
			FVector receive_position = catching_player->GetMovement()->GetCurrentPosition();

// 			int KEY = *********;
// 			SETDEBUGLINE( KEY++, pass_position, receive_position, MabColour::Red, MabColour::Red );
// 			SETDEBUGLINE( KEY++, pass_position - FVector(5.0f, 0.0f, 0.0f), pass_position + FVector(5.0f, 0.0f, 0.0f), MabColour::White, MabColour::White );

			// Our start and end positions of this pass.
			float pass_start_z = pass_position.z;
			float pass_end_z = receive_position.z;
			float diff_z = (pass_end_z - pass_start_z) * pass_play_dir;

			// forward pass is great than zero ground gained
			if(diff_z > 0.0f || force_forward_pass)
			{
				// this pass is forward, but has the ref noticed
				// TODO: a referee stat here would be cool
				// depending on how forward the pass is the more likely the ref will be to notice
				// good passes are never mistaken for forward ones (this is too annoying)

				// base the forwardness on the angle
				float diff_x = MabMath::Abs(pass_position.x -  receive_position.x);
				float pass_angle = MabMath::ATan2(diff_z, diff_x);

				const static float MAX_FORWARD_PASS_ACCEPTABLE_ANGLE = MabMath::Deg2Rad(10.0f); // anything forward of here is definitely a forward pass, from here to 0 is a maybe
				float forward_pass_called_chance = pass_angle / MAX_FORWARD_PASS_ACCEPTABLE_ANGLE;
				MabMath::Clamp(forward_pass_called_chance, 0.0f, 1.0f);

				if (game->GetRNG()->RAND_CALL(float) < forward_pass_called_chance || force_forward_pass)
				{
					if ( game->GetRules()->IsPlaySuspended() )
						return;

					from_forward_pass = true;
					from_offside = false;

					game->GetGameState()->SetLastOffendingPlayer(passing_player);

					restart_team = passing_player->GetAttributes()->GetOppositionTeam();
					restart_position = pass_position;

					game->GetEvents()->forward_pass( passing_player, catching_player );

					FieldExtents extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
					MabMath::Clamp( restart_position.z, -(extents.y/2.0f - 5.0f), (extents.y/2.0f - 5.0f) );
					MabMath::Clamp( restart_position.x, -(extents.x/2.0f - 5.0f), (extents.x/2.0f - 5.0f) );

					consequence = RUC_SCRUM;
					this->offending_player = passing_player;
					rules->SetTrigger(this);

					state = ADVANTAGE_FIRST_INFRINGEMENT;
					DoAdvantageConsequence("RURuleTriggerAdvatage::Forward Pass");
					//ADV_OVER_MIN_DIST_FORWARD = ADV_OVER_MIN_DIST_FORWARD_MIN + game->GetRNG()->RAND_CALL(float) * (ADV_OVER_MIN_DIST_FORWARD_MAX - ADV_OVER_MIN_DIST_FORWARD_MIN);
				}
			}
		}
	}
	else
	{
		if( offending_player->GetAttributes()->GetTeam() == restart_team )
		{
#ifdef ENABLE_OSD
			RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
			MabString activation_string( 256, "2nd infringment from pass a5 %0.1fs", game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds() );
			settings->PushDebugString( game, RUGameDebugSettings::DP_KNOCKON, activation_string.c_str() );
			MABLOGDEBUG( activation_string.c_str() );
#endif

			SecondInfringement(); // force the last infringement to start
		}
	}

#else

	MABUNUSED(catching_player);

#endif //EXPERIMENTAL_FORWARD_PASS
}

void RURuleTriggerAdvantage::Pass(ARugbyCharacter* passing_player, ARugbyCharacter* player_passed_to,
								   const FVector& or_target_position, PASS_TYPE /*type_of_pass*/, bool /*success*/)
{
#if !defined(EXPERIMENTAL_FORWARD_PASS)

#ifdef ENABLE_DEBUG_RULE_MONITORING
	MABLOGMSG(LOGCHANNEL_DEBUG, LOGTYPE_INFO, "RulesTrigger Call: RURuleTriggerForwardPass::Pass");
#endif

	if( !is_active && passing_player )
	{
		bool force_forward_pass = false;

#ifdef ENABLE_GAME_DEBUG_MENU
	#ifdef ENABLE_TRIGGER_RULES_DEBUG_SETTINGS
		force_forward_pass = SIFDebug::GetRulesDebugSettings()->GetNextPassIsForward();
	#endif
#endif
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
		const int32 ForwardPassDebugVal = CForwardPassDebug.GetValueOnGameThread();
		if (ForwardPassDebugVal)
		{
			force_forward_pass = true;
		}
#endif

//  		int KEY = 324745532;
//  		SETDEBUGLINE( KEY++, passing_player->GetMovement()->GetCurrentPosition(), or_target_position, MabColour::Red, MabColour::Red );
//  		SETDEBUGLINE( KEY++, passing_player->GetMovement()->GetCurrentPosition() - FVector(5.0f, 0.0f, 0.0f), passing_player->GetMovement()->GetCurrentPosition() + FVector(5.0f, 0.0f, 0.0f), MabColour::White, MabColour::White );

		ARugbyCharacter* bh = game->GetGameState()->GetBallHolder();
		if(bh)
		{
			MABLOGDEBUG("bh: %s", bh->GetAttributes()->GetCombinedName().c_str());
		}

		// Apply custom rules.
		if(!force_forward_pass && !game->GetGameSettings().game_settings.custom_rule_forward_pass)
			return;

		if(game->GetGameState()->IsGameInStandardPlay())
		{
			const static float FORWARD_PASS_MARGIN = 0.1f;	// add a margin to account for throwing from infront of you

			float pass_start_z = passing_player->GetMovement()->GetCurrentPosition().z + FORWARD_PASS_MARGIN * passing_player->GetAttributes()->GetTeam()->GetPlayDirection();
			float pass_end_z = or_target_position.z;
			float diff_z = (pass_end_z - pass_start_z) * passing_player->GetAttributes()->GetTeam()->GetPlayDirection();

			// forward pass is great than zero ground gained
			if(diff_z > 0.0f || force_forward_pass)
			{
				// this pass is forward, but has the ref noticed
				// TODO: a referee stat here would be cool
				// depending on how forward the pass is the more likely the ref will be to notice
				// good passes are never mistaken for forward ones (this is too annoying)

				// base the forwardness on the angle
				float diff_x = MabMath::Abs(passing_player->GetMovement()->GetCurrentPosition().x -  or_target_position.x);
				float pass_angle = MabMath::ATan2(diff_z, diff_x);

				const static float MAX_FORWARD_PASS_ACCEPTABLE_ANGLE = MabMath::Deg2Rad(10.0f); // anything forward of here is definitely a forward pass, from here to 0 is a maybe
				float forward_pass_called_chance = pass_angle / MAX_FORWARD_PASS_ACCEPTABLE_ANGLE;
				MabMath::Clamp(forward_pass_called_chance, 0.0f, 1.0f);

				//Also compare total z distance.
				bool too_far_forward = diff_z > 1.0f;

				if (game->GetRNG()->RAND_CALL(float) < forward_pass_called_chance || too_far_forward || force_forward_pass)
				{
					if ( game->GetRules()->IsPlaySuspended() )
						return;

					from_forward_pass = true;
					from_offside = false;

					game->GetGameState()->SetLastOffendingPlayer(passing_player);

					restart_team = passing_player->GetAttributes()->GetOppositionTeam();
					restart_position = passing_player->GetMabPosition();

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)					
					if (ForwardPassDebugVal == 2)
					{
						player_passed_to = passing_player->GetAttributes()->GetOppositionTeam()->GetCaptain(); //pass to the captain of opposite team the forward pass
					}
#endif
					game->GetEvents()->forward_pass( passing_player, player_passed_to );

					FieldExtents extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
					MabMath::Clamp( restart_position.z, -(extents.y/2.0f - 5.0f), (extents.y/2.0f - 5.0f) );
					MabMath::Clamp( restart_position.x, -(extents.x/2.0f - 5.0f), (extents.x/2.0f - 5.0f) );

					consequence = RUC_SCRUM;
					this->offending_player = passing_player;
					rules->SetTrigger(this);

					state = ADVANTAGE_FIRST_INFRINGEMENT;
					//ADV_OVER_MIN_DIST_FORWARD = ADV_OVER_MIN_DIST_FORWARD_MIN + game->GetRNG()->RAND_CALL(float) * (ADV_OVER_MIN_DIST_FORWARD_MAX - ADV_OVER_MIN_DIST_FORWARD_MIN);
				}
			}
		}
	}
	else
	{
		if( offending_player->GetAttributes()->GetTeam() == restart_team )
		{
			#ifdef ENABLE_OSD
						RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
						MabString activation_string( 256, "2nd infringment from pass a5 %0.1fs", game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds() );
						settings->PushDebugString( game, RUGameDebugSettings::DP_KNOCKON, activation_string.c_str() );
						MABLOGDEBUG( activation_string.c_str() );
			#endif

			SecondInfringement(); // force the last infringement to start
		}
	}

#else

	MABUNUSED(passing_player);
	MABUNUSED(player_passed_to);
	MABUNUSED(or_target_position);

#endif //EXPERIMENTAL_FORWARD_PASS
}

void RURuleTriggerAdvantage::TryScore( )
{
#ifdef ENABLE_DEBUG_RULE_MONITORING
	MABLOGDEBUG( "RURuleTriggerAdvatage::TryScore: state(%d)", state );
#endif

	if( state == ADVANTAGE_SECOND_INFRINGEMENT )
		rules->RemoveTrigger( this );
}

void RURuleTriggerAdvantage::Kick( ARugbyCharacter* kicker, KickContext kick_context, KickType kick_type, const FVector& )
{
#ifdef ENABLE_DEBUG_RULE_MONITORING
	MABLOGDEBUG( "RURuleTriggerAdvatage::Kick: state(%d)", state );
#endif

	//Mark the kick an offensive kick if the advantage is from a penalty
	if (from_offside)
	{
		offensive_kick_performed = true;
	}

	// HES #12628, they want the advantage to trigger if the offending team grubbers it along.
	// Since you can toe kick it all the way to the opposite side and dive on the ball to score (even though it at that time calls advantage)
	if( offending_player && offending_player->GetAttributes()->GetTeam() == kicker->GetAttributes()->GetTeam() &&
		kick_context == KC_FREEBALL_KICK &&
		kick_type == KICKTYPE_FREEBALLKICK)
	{
		DoAdvantageConsequence( "RURuleTriggerAdvantage::Kick" );
	}
	
}

void RURuleTriggerAdvantage::Tackle (const RUTackleResult& result)
{
	// HES #12813, bug where tackling thin air still awards an advantage.
	if( result.tackle_result == TRT_DIVE_MISS
		//|| result.tackle_result == TRT_NO_TACKLE // Not 100% sure about this condition
		//|| result.state_machine == TSM_FAILED  // I think the state will be failed if the tackle is incomplete (but contact is still made)
		//|| result.state_machine == TSM_UNKNOWN // Not 100% sure about this condition
		)
	{
		return;
	}

	/// Apply custom rules.
	if(game->GetGameSettings().game_settings.custom_rule_offside)
	{
		bool force_offside = false;

#ifdef ENABLE_GAME_DEBUG_MENU
		#ifdef ENABLE_TRIGGER_RULES_DEBUG_SETTINGS
		force_offside = SIFDebug::GetRulesDebugSettings()->IsNextTackleOffside();
		#endif
#endif

		for(int i = 0; i < MAX_TACKLERS; ++i)
		{
			if(result.tacklers[i] != NULL)
			{
				ARugbyCharacter* player = result.tacklers[i];
				float offside_test = 0.6f + ( game->GetRNG()->RAND_RANGED_CALL( float, 0.4f ) - 0.2f );
				
				if (player != NULL && (( player->GetOffSideIndicator()->GetOffsideValue() > offside_test) ||
					force_offside ))
				{
					if ( is_active && from_offside )
					{
						// Already playing advantage for offside, trigger the previous offside consequence instead of overwriting it.
						SecondInfringement();
						return;
					}
				
					if ( player->GetAttributes()->GetTeam() == game->GetGameState()->GetBallHolder()->GetAttributes()->GetTeam() )
					{
						MABBREAKMSG( "SHOULD NOT BE POSSIBLE!" );
					}
				
					game->GetEvents()->offside( player );
				
					this->offending_player = player;
				
					game->GetGameState()->SetLastOffendingPlayer( player );
					game->GetGameState()->SetLastOffendedPlayer( result.tacklee );
				
					MABASSERT( player != NULL );
				
					restart_team = player->GetAttributes()->GetOppositionTeam();
					restart_position = player->GetMabPosition();
				
					FieldExtents extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
					MabMath::Clamp( restart_position.z, -(extents.y/2.0f - 5.0f), (extents.y/2.0f - 5.0f) );
					MabMath::Clamp( restart_position.x, -(extents.x/2.0f - 5.0f), (extents.x/2.0f - 5.0f) );
				
					rules->SetTrigger(this);
				
					from_forward_pass = false;
					from_offside = true;
				
					state = ADVANTAGE_FIRST_INFRINGEMENT;
					//ADV_OVER_MIN_DIST_FORWARD = ADV_OVER_MIN_DIST_FORWARD_MIN + game->GetRNG()->RAND_CALL(float) * (ADV_OVER_MIN_DIST_FORWARD_MAX - ADV_OVER_MIN_DIST_FORWARD_MIN);
				
					break;
				}
			}
		}
	}
}

void RURuleTriggerAdvantage::MaulFormed( ARugbyCharacter*, ARugbyCharacter* )
{
	breakdown_winning_team = NULL;

	if (advantage_timer_elapsed)
	{
		state = ADVANTAGE_SECOND_INFRINGEMENT;
		advantage_timer_elapsed = false;
	}
}

void RURuleTriggerAdvantage::RuckFormed( ARugbyCharacter*, SIFRugbyCharacterList* )
{
	breakdown_winning_team = NULL;

	if (advantage_timer_elapsed)
	{
		state = ADVANTAGE_SECOND_INFRINGEMENT;
		advantage_timer_elapsed = false;
	}
}

void RURuleTriggerAdvantage::Reset()
{
	restart_position = FVector(0.0f, 0.0f, 0.0f);
	restart_team = NULL;
	restart_player = NULL;
	offending_player = NULL;
	advantage_timer_elapsed = false;
	state = ADVANTAGE_INVALID;
	timer.SetEnabled( false );
	#ifdef ENABLE_OSD
		RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
		MabString activation_string( 256, "RURuleTriggerAdvatage::Reset(): State %d at 0.1fs", state, game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds() );
		settings->PushDebugString( game, RUGameDebugSettings::DP_KNOCKON, activation_string.c_str() );
		MABLOGDEBUG( activation_string.c_str() );
	#endif
	is_active = false;

	from_forward_pass = false;
	from_offside = false;
	breakdown_winning_team = NULL;
}

void RURuleTriggerAdvantage::RuckBallReleased( RUTeam *team, const FVector & /*position*/, float /*time*/ )
{
	breakdown_winning_team = team;

	// If the ball is turned over at the breakdown - there is no advantage so call it
	if ( is_active )
	{
		if ( breakdown_winning_team != restart_team )
			DoAdvantageConsequence( "RURuleTriggerAdvantage::RuckBallReleased" );

		// HES #11403, requested that rucks not call advantage over when the ball gets released.
		/*else if ( !from_offside )	// Should be from a knockon or forward pass - advantage is over if we clear through one ruck
		{
			DoAdvantageOver( "RURuleTriggerAdvantage::RuckBallReleased" );
		}*/
	}
}

void RURuleTriggerAdvantage::MaulBallReleased( RUTeam *team, const FVector & /*position*/, float /*time*/ )
{
	breakdown_winning_team = team;

	// If the ball is turned over at the breakdown - there is no advantage so call it
	if ( is_active )
	{
		if ( breakdown_winning_team != restart_team )
			DoAdvantageConsequence( "RURuleTriggerAdvantage::MaulBallReleased" );
		else if ( !from_offside )	// Should be from a knockon or forward pass - advantage is over if we clear through one ruck
		{
			DoAdvantageOver( "RURuleTriggerAdvantage::MaulBallReleased" );
		}
	}
}

//===============================================================================
//===============================================================================
void RURuleTriggerAdvantage::OnTackleeOnGround(const RUTackleResult& tackleResult)
{
	if (is_active)
	{
		if (!game->GetBall()->IsValidRuckPosition() && tackleResult.tacklee->GetAttributes()->GetTeam() != restart_team)
		{
			DoAdvantageConsequence("RURuleTriggerAdvantage::OnTackleeOnGround");
		}
	}
}

//===============================================================================
//===============================================================================
void RURuleTriggerAdvantage::OnDenyTry()
{
	if (is_active)
	{
		DoAdvantageConsequence("RURuleTriggerAdvantage::OnDenyTry");
	}
}

/// Trying to think of the situations where we are still in an advantageous position if time is up.
///		1. We have the ball and we will make ground past the max advantage point
///		2. We don't have the ball and we will get to the ball before the opposition and collect it before they do
///		3. We have the ball and we have an overlap
///		4. Advantageous position from ruck win

bool RURuleTriggerAdvantage::IsInAdvantageousPosition() const
{
	bool advantage_team_has_ball = game->GetGameState()->GetBallHolder() && game->GetGameState()->GetBallHolder()->GetAttributes()->GetTeam() == restart_team;

	ERugbyPlayDirection pd = restart_team->GetPlayDirection();

	const PlayInfo & pi = game->GetStrategyHelper()->GetPlayInfo();
	const float MIN_FORWARD_PROGRESS = from_offside ? ADV_OVER_MIN_DIST_FORWARD_FOR_PENALTY : ADV_OVER_MIN_DIST_FORWARD_FOR_KNOCK_ON;

	/// Utilise play info to tell us if it's advantageous with ball in hand
	bool advantageous_ball_in_hand = advantage_team_has_ball &&
		(
		pi.bh_in_clear ||									// In the clear
		pi.bh_forward_progress >= MIN_FORWARD_PROGRESS ||	// Or we will make some ground forward
		((pi.bh_intercept_result.intercept_point.z - restart_position.z) * pd) > MIN_FORWARD_PROGRESS || // Or where we will get intercepted is past the advantage point
		pi.bh_likely_to_score								// We will likely score
		);

	/// Utilise GameGTB to tell us if it's advantageous with ball free
	RUGameGetToBall* gtb = game->GetGameGTB();
	const RUGameGetToBall::GTBArray& gtb_arrival = gtb->GetGTBInfoByArrival();

	bool advantageous_ball_chase = false;

	if ( game->GetGameState()->GetBallHolder() == NULL && gtb->IsActive() && !gtb_arrival.empty() )
	{
		const GTB_INFO& gtb_info = gtb_arrival[0];
		advantageous_ball_chase = gtb_info.player->GetAttributes()->GetTeam() == restart_team && ((gtb_info.ball_position_at_contact_time.z - restart_position.z) * pd) > MIN_FORWARD_PROGRESS;
	}

	/// No implementation for this at the moment
	bool advantageous_by_overlap = false;

	/// For rucks and mauls we wait for the event which tells us who won before deciding whether to call advantage over or not
	/// So here we *say*  we are in an advatnageous position if the ruck or maul has not yet been resolved
	bool advantageous_in_ruck_or_maul = (game->GetGameState()->GetPhase() == RUGamePhase::RUCK || game->GetGameState()->GetPhase() == RUGamePhase::MAUL) && breakdown_winning_team != restart_team->GetOppositionTeam();
	bool in_advantageous_position = advantageous_ball_in_hand || advantageous_ball_chase || advantageous_by_overlap || advantageous_in_ruck_or_maul;

	//{	// DEBUG
	//	MabColour col = in_advantageous_position ? MabColour::Green : MabColour::Red;

	//	const int KEY = 324745532;
	//	if ( advantage_team_has_ball )
	//	{
	//		SETDEBUGLINE( KEY, pi.bh_intercept_result.intercept_point, pi.bh_intercept_result.intercept_point + FVector::Y_AXIS * 2.0f, col, col );
	//	}
	//	else if ( game->GetGameState()->GetBallHolder() == NULL && gtb->IsActive() && !gtb_arrival.empty() )
	//	{
	//		const GTB_INFO* info = gtb->GetPlayerByTeamArrival( restart_team, 0 );
	//		if ( info )
	//		{
	//			SETDEBUGLINE( KEY, info->ball_position_at_contact_time, info->ball_position_at_contact_time + FVector::Y_AXIS * 2.0f, col, col );
	//		}
	//	}
	//}

	return in_advantageous_position;
}

void RURuleTriggerAdvantage::DoAdvantageConsequence( const char* triggered_from_text )
{
	

#if defined ENABLE_SIF_DEBUG_DRAW && defined ENABLE_GAME_DEBUG_MENU
	//#rc3_legacy_debug_draw
	/*if( SIFDebug::GetRulesDebugSettings()->GetShowDebugInfo() )
	{
		SIF_DEBUG_DRAW(Remove3DLine(*********));
		SIF_DEBUG_DRAW(Remove3DLine(*********));
		SIF_DEBUG_DRAW(RemoveText(545646542));
	}*/
#endif
	MABUNUSED( triggered_from_text );	// If Debug is off
	MABLOGDEBUG( "%s: team(%s), state(%d) at %0.1fs", triggered_from_text, restart_team->GetDbTeam().GetName(), state, game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds());

	RUGameState* game_state = game->GetGameState();
	game_state->SetPlayRestartTeam(restart_team);
	game_state->SetPlayRestartPosition(restart_position);

	if (from_forward_pass)
	{
		MABLOGDEBUG("Restart team for forward pass: %s", restart_team->GetDbTeam().GetName());
		game->GetEvents()->cutscene_forward_pass((RUTeam*)restart_team->GetOppositionTeam());
	}
	else if ( from_offside )
	{
		game->GetEvents()->penalty_from_advantage( offending_player, game->GetGameState()->GetLastOffendedPlayer(), restart_position, PENALTY_REASON_OFFSIDE );
		return; // Penalty event ends the advantage by overriding this rule trigger with itself, so just return (no clean up required).
	}
	else
	{
#ifdef ENABLE_OSD
		RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
		MabString activation_string( 256, "Knockon awarded at %0.1fs", game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds() );
		settings->PushDebugString( game, RUGameDebugSettings::DP_KNOCKON, activation_string.c_str() );
		MABLOGDEBUG( activation_string.c_str() );
#endif

		game->GetEvents()->knock_on_awarded();
		//Glen: indicate that the ref has called for a knock-on. May be more useful
		//closer to the ref actually doing something.
		game->GetEvents()->knock_on_called( offending_player );
	}

	timer.Reset();
	timer.SetEnabled( false );

	player_info_delay.SetEnabled( true );
	state = ADVANTAGE_OVER;

// 	MABASSERT( offending_player != NULL );
	game->GetEvents()->advantage_ended(offending_player);

#ifdef ENABLE_OSD
	RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
	MabString activation_string( 256, "Advantage ended from timer or second infringement at %0.1fs", game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds() );
	settings->PushDebugString( game, RUGameDebugSettings::DP_KNOCKON, activation_string.c_str() );
	MABLOGDEBUG( activation_string.c_str() );
#endif

	rules->RemoveTrigger(this);
	//Reset();
};

void RURuleTriggerAdvantage::DoAdvantageOver( const char* triggered_from_text )
{

#if defined ENABLE_SIF_DEBUG_DRAW && defined ENABLE_GAME_DEBUG_MENU
	//#rc3_legacy_debug_draw
	/*if( SIFDebug::GetRulesDebugSettings()->GetShowDebugInfo() )
	{
		SIF_DEBUG_DRAW(Remove3DLine(*********));
		SIF_DEBUG_DRAW(Remove3DLine(*********));
		SIF_DEBUG_DRAW(RemoveText(545646542));
	}*/
#endif

	MABUNUSED( triggered_from_text );	// If Debug is off
	MABLOGDEBUG( "%s: team(%s), state(%d -> %d) ar %0.1fs", triggered_from_text, restart_team->GetDbTeam().GetName(), state, ADVANTAGE_OVER, game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds());

	state = ADVANTAGE_OVER;
	game->GetEvents()->advantage_over(offending_player);

#ifdef ENABLE_OSD
	RUGameDebugSettings* settings = SIFDebug::GetGameDebugSettings();
	MabString activation_string( 256, "Advantage over - ground made %0.1fs" );
	settings->PushDebugString( game, RUGameDebugSettings::DP_KNOCKON, activation_string.c_str(), game->GetSimTimeNonConst()->GetAbsoluteTime().ToSeconds() );
	MABLOGDEBUG( activation_string.c_str() );
#endif

#ifdef ENABLE_DEBUG_RULE_MONITORING
	//SIF_DEBUG_DRAW( SetText( 13241234, 100, 100, "ADVANTAGE_OVER" ) );
#endif
	RUGameEvents* events = game->GetEvents();
	events->advantage_ended(offending_player);

	rules->RemoveTrigger(this);

}
