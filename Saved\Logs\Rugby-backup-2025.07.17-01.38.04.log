﻿Log file open, 07/17/25 11:38:01
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogConsoleResponse: Display: Failed to find resolution value strings in scalability ini. Falling back to default.
LogConsoleResponse: Display: Failed to find resolution value strings in scalability ini. Falling back to default.
LogInit: Display: Running engine for game: Rugby
LogPlatformFile: Not using cached read wrapper
LogTaskGraph: Started task graph with 5 named threads and 71 total threads with 3 sets of task threads.
LogStats: Stats thread started at 0.242325
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +10:00, Platform Override: ''
LogInit: Display: Loading text-based GConfig....
LogPluginManager: Mounting plugin MeshPainting
LogPluginManager: Mounting plugin MorphTools
LogPluginManager: Mounting plugin RMAMirrorAnimation
LogPluginManager: Mounting plugin WWUITool
LogPluginManager: Mounting plugin XGEController
LogPluginManager: Mounting plugin LiveLink
LogPluginManager: Mounting plugin AISupport
LogPluginManager: Mounting plugin EnvironmentQueryEditor
LogPluginManager: Mounting plugin LightPropagationVolume
LogPluginManager: Mounting plugin CameraShakePreviewer
LogPluginManager: Mounting plugin OodleNetwork
LogPluginManager: Mounting plugin GameplayCameras
LogPluginManager: Mounting plugin AnimationSharing
LogPluginManager: Mounting plugin OodleData
LogPluginManager: Mounting plugin AssetManagerEditor
LogPluginManager: Mounting plugin CurveEditorTools
LogPluginManager: Mounting plugin FacialAnimation
LogPluginManager: Mounting plugin CryptoKeys
LogPluginManager: Mounting plugin GameplayTagsEditor
LogPluginManager: Mounting plugin GeometryMode
LogPluginManager: Mounting plugin PixWinPlugin
LogPluginManager: Mounting plugin MaterialAnalyzer
LogPluginManager: Mounting plugin PluginUtils
LogPluginManager: Mounting plugin PropertyAccessNode
LogPluginManager: Mounting plugin RiderSourceCodeAccess
LogPluginManager: Mounting plugin PlasticSourceControl
LogPluginManager: Mounting plugin SubversionSourceControl
LogPluginManager: Mounting plugin PluginBrowser
LogPluginManager: Mounting plugin UObjectPlugin
LogPluginManager: Mounting plugin VariantManagerContent
LogPluginManager: Mounting plugin TextureFormatOodle
LogPluginManager: Mounting plugin AutomationUtils
LogPluginManager: Mounting plugin ChaosCloth
LogPluginManager: Mounting plugin ChaosEditor
LogPluginManager: Mounting plugin ChaosClothEditor
LogPluginManager: Mounting plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting plugin ChaosSolverPlugin
LogPluginManager: Mounting plugin ChaosNiagara
LogPluginManager: Mounting plugin GeometryCollectionPlugin
LogPluginManager: Mounting plugin Niagara
LogPluginManager: Mounting plugin GeometryCache
LogPluginManager: Mounting plugin Gauntlet
LogPluginManager: Mounting plugin GeometryProcessing
LogPluginManager: Mounting plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting plugin OpenImageDenoise
LogPluginManager: Mounting plugin PlanarCut
LogPluginManager: Mounting plugin PlatformCrypto
LogPluginManager: Mounting plugin MotoSynth
LogPluginManager: Mounting plugin ProxyLODPlugin
LogPluginManager: Mounting plugin PythonScriptPlugin
LogPluginManager: Mounting plugin LuminPlatformFeatures
LogPluginManager: Mounting plugin SkeletalReduction
LogPluginManager: Mounting plugin MagicLeapPassableWorld
LogPluginManager: Mounting plugin MLSDK
LogPluginManager: Mounting plugin UdpMessaging
LogPluginManager: Mounting plugin TcpMessaging
LogPluginManager: Mounting plugin ActorSequence
LogPluginManager: Mounting plugin LevelSequenceEditor
LogPluginManager: Mounting plugin TemplateSequence
LogPluginManager: Mounting plugin OnlineSubsystemNull
LogPluginManager: Mounting plugin OnlineSubsystem
LogPluginManager: Mounting plugin OnlineSubsystemSteam
LogPluginManager: Mounting plugin OnlineSubsystemUtils
LogPluginManager: Mounting plugin WWSteamNetworkCommunicationP2P
LogPluginManager: Mounting plugin ActorLayerUtilities
LogPluginManager: Mounting plugin AnimationBudgetAllocator
LogPluginManager: Mounting plugin AssetTags
LogPluginManager: Mounting plugin AudioSynesthesia
LogPluginManager: Mounting plugin CableComponent
LogPluginManager: Mounting plugin ChunkDownloader
LogPluginManager: Mounting plugin CustomMeshComponent
LogPluginManager: Mounting plugin EditableMesh
LogPluginManager: Mounting plugin GooglePAD
LogPluginManager: Mounting plugin OpenXR
LogPluginManager: Mounting plugin OpenXREyeTracker
LogPluginManager: Mounting plugin OpenXRHandTracking
LogPluginManager: Mounting plugin PropertyAccessEditor
LogPluginManager: Mounting plugin ProceduralMeshComponent
LogPluginManager: Mounting plugin PostSplashScreen
LogPluginManager: Mounting plugin RuntimePhysXCooking
LogPluginManager: Mounting plugin SignificanceManager
LogPluginManager: Mounting plugin SoundFields
LogPluginManager: Mounting plugin Synthesis
LogPluginManager: Mounting plugin ScreenshotTools
LogPluginManager: Mounting plugin Takes
LogPluginManager: Mounting plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting plugin ContentBrowserFileDataSource
LogPluginManager: Mounting plugin ContentBrowserClassDataSource
LogPluginManager: Mounting plugin SteamShared
LogPluginManager: Mounting plugin NpadController
LogPluginManager: Mounting plugin OnlineSubsystemSwitch
LogPluginManager: Mounting plugin FMODStudio
LogPluginManager: Mounting plugin wwHeadImport
LogPluginManager: Mounting plugin wwStadiumTools
LogPluginManager: Mounting plugin wwHttpModule
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogInit: Using libcurl 7.55.1-DEV
LogInit:  - built for x86_64-pc-win32
LogInit:  - supports SSL with OpenSSL/1.1.1
LogInit:  - supports HTTP deflate (compression) using libz 1.2.8
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_IDN
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogSteamShared: Display: Loading Steam SDK 1.51
LogSteamShared: Steam SDK Loaded!
LogOnline: OSS: Creating online subsystem instance for: Steam
LogOnline: Display: STEAM: OnlineSubsystemSteam::Shutdown()
LogOnline: OSS: Unable to create OnlineSubsystem module Steam
LogOnline: OSS: Creating online subsystem instance for: NULL
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for module [NULL]
LogInit: Build: ++UE4+Release-4.27-CL-0
LogInit: Engine Version: 4.27.2-0+++UE4+Release-4.27
LogInit: Compatible Engine Version: 4.27.0-0+++UE4+Release-4.27
LogInit: Net CL: 0
LogInit: OS: Windows 10 (Release 2009) (), CPU: AMD Ryzen 9 5900X 12-Core Processor            , GPU: NVIDIA GeForce RTX 2070 SUPER
LogInit: Compiled (64-bit): Jul  2 2025 12:32:38
LogInit: Compiled with Visual C++: 19.29.30159.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE4+Release-4.27
LogInit: Command Line:  -skipcompile
LogInit: Base Directory: W:/Engine/Engine/Binaries/Win64/
LogInit: Allocator: TBB
LogInit: Installed Engine Build: 0
LogDevObjectVersion: Number of dev versions registered: 29
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 14
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 45
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 47
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 1
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC, pre-allocating 0 bytes for permanent pool.
LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [W:/NRL/Saved/Config/Windows/Engine.ini]
LogConfig: Setting CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
LogConfig: Setting CVar [[s.AsyncLoadingThreadEnabled:1]]
LogConfig: Setting CVar [[s.EventDrivenLoaderEnabled:1]]
LogConfig: Setting CVar [[s.WarnIfTimeLimitExceeded:0]]
LogConfig: Setting CVar [[s.TimeLimitExceededMultiplier:1.5]]
LogConfig: Setting CVar [[s.TimeLimitExceededMinTime:0.005]]
LogConfig: Setting CVar [[s.UseBackgroundLevelStreaming:1]]
LogConfig: Setting CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
LogConfig: Setting CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
LogConfig: Setting CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
LogConfig: Setting CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
LogConfig: Setting CVar [[s.UnregisterComponentsTimeLimit:1.0]]
LogConfig: Setting CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
LogConfig: Setting CVar [[s.FlushStreamingOnExit:1]]
LogInit: Object subsystem initialized
LogConfig: Setting CVar [[con.DebugEarlyDefault:1]]
LogConfig: Setting CVar [[r.setres:1280x720]]
[2025.07.17-01.38.01:735][  0]LogConfig: Setting CVar [[r.VSync:0]]
[2025.07.17-01.38.01:735][  0]LogConfig: Setting CVar [[r.RHICmdBypass:0]]
[2025.07.17-01.38.01:735][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [W:/NRL/Saved/Config/Windows/Engine.ini]
[2025.07.17-01.38.01:735][  0]LogConfig: Setting CVar [[r.GPUCrashDebugging:0]]
[2025.07.17-01.38.01:735][  0]LogConfig: Setting CVar [[r.DefaultFeature.MotionBlur:1]]
[2025.07.17-01.38.01:735][  0]LogConfig: Setting CVar [[r.DefaultFeature.AutoExposure:0]]
[2025.07.17-01.38.01:735][  0]LogConfig: Setting CVar [[r.DefaultFeature.AmbientOcclusionStaticFraction:0]]
[2025.07.17-01.38.01:735][  0]LogConfig: Setting CVar [[r.DefaultFeature.AmbientOcclusion:1]]
[2025.07.17-01.38.01:735][  0]LogConfig: Setting CVar [[r.DefaultFeature.Bloom:1]]
[2025.07.17-01.38.01:735][  0]LogConfig: Setting CVar [[r.DefaultFeature.AntiAliasing:1]]
[2025.07.17-01.38.01:735][  0]LogConfig: Setting CVar [[r.GenerateMeshDistanceFields:0]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.GenerateLandscapeGIData:1]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.TemporalAA.Upsampling:1]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.MinScreenRadiusForCSMDepth:0.100000]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.DBuffer:0]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.CustomDepth:3]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.CustomDepthTemporalAAJitter:0]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.GPUSkin.Limit2BoneInfluences:0]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.EarlyZPassOnlyMaterialMasking:0]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.AllowOcclusionQueries:0]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.DefaultFeature.LensFlare:1]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.TextureStreaming:1]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.MinScreenRadiusForDepthPrepass:0.090000]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.EarlyZPass:0]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.GBufferFormat:1]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.DistanceFieldBuild.Compress:1]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.StencilForLODDither:0]]
[2025.07.17-01.38.01:736][  0]LogConfig: Setting CVar [[r.ClearSceneMethod:1]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[r.DefaultBackBufferPixelFormat:4]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[r.DiscardUnusedQuality:1]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[r.AllowStaticLighting:0]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[r.SeparateTranslucency:0]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[r.HZBOcclusion:0]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[r.PostProcessAAQuality:3]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[r.FinishCurrentFrame:1]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[r.SceneColorFormat:2]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[r.rhicmdbypass:0]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[r.SceneColorFringeQuality:0]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[r.LightShaftDownSampleFactor:4]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[r.Streaming.FramesForFullUpdate:0]]
[2025.07.17-01.38.01:737][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [W:/NRL/Saved/Config/Windows/Engine.ini]
[2025.07.17-01.38.01:737][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [W:/NRL/Saved/Config/Windows/Engine.ini]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.07.17-01.38.01:737][  0]LogConfig: Setting CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[s.FlushStreamingOnExit:1]]
[2025.07.17-01.38.01:738][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [W:/NRL/Saved/Config/Windows/Engine.ini]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[gc.SizeOfPermanentObjectPool:0]]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[gc.FlushStreamingOnGC:0]]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[gc.AllowParallelGC:1]]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.07.17-01.38.01:738][  0]LogConfig: Setting CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.07.17-01.38.01:739][  0]LogConfig: Setting CVar [[gc.CreateGCClusters:1]]
[2025.07.17-01.38.01:739][  0]LogConfig: Setting CVar [[gc.MinGCClusterSize:5]]
[2025.07.17-01.38.01:739][  0]LogConfig: Setting CVar [[gc.ActorClusteringEnabled:0]]
[2025.07.17-01.38.01:739][  0]LogConfig: Setting CVar [[gc.BlueprintClusteringEnabled:0]]
[2025.07.17-01.38.01:739][  0]LogConfig: Setting CVar [[gc.UseDisregardForGCOnDedicatedServers:0]]
[2025.07.17-01.38.01:739][  0]LogConfig: Setting CVar [[gc.MultithreadedDestructionEnabled:1]]
[2025.07.17-01.38.01:740][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [W:/NRL/Saved/Config/Windows/Engine.ini]
[2025.07.17-01.38.01:740][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [W:/NRL/Saved/Config/Windows/Engine.ini]
[2025.07.17-01.38.01:762][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:762][  0]LogConfig: Setting CVar [[r.SkeletalMeshLODBias:0]]
[2025.07.17-01.38.01:762][  0]LogConfig: Setting CVar [[r.ViewDistanceScale:1.0]]
[2025.07.17-01.38.01:762][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:762][  0]LogConfig: Setting CVar [[r.PostProcessAAQuality:4]]
[2025.07.17-01.38.01:762][  0]LogConsoleManager: Warning: Setting the console variable 'r.PostProcessAAQuality' with 'SetByScalability' was ignored as it is lower priority than the previous 'SetByProjectSetting'. Value remains '3'
[2025.07.17-01.38.01:762][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:762][  0]LogConfig: Setting CVar [[r.LightFunctionQuality:1]]
[2025.07.17-01.38.01:762][  0]LogConfig: Setting CVar [[r.ShadowQuality:5]]
[2025.07.17-01.38.01:762][  0]LogConfig: Setting CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.Shadow.MaxResolution:2048]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.Shadow.RadiusThreshold:0.005]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.Shadow.DistanceScale:1.0]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.DistanceFieldShadowing:1]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.DistanceFieldAO:1]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.AOQuality:2]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.VolumetricFog:1]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.CapsuleShadows:1]]
[2025.07.17-01.38.01:763][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.MotionBlurQuality:4]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.AmbientOcclusionLevels:-1]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.DepthOfFieldQuality:2]]
[2025.07.17-01.38.01:763][  0]LogConfig: Setting CVar [[r.RenderTargetPoolMin:400]]
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.LensFlareQuality:2]]
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.SceneColorFringeQuality:1]]
[2025.07.17-01.38.01:764][  0]LogConsoleManager: Warning: Setting the console variable 'r.SceneColorFringeQuality' with 'SetByScalability' was ignored as it is lower priority than the previous 'SetByProjectSetting'. Value remains '0'
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.EyeAdaptationQuality:2]]
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.BloomQuality:5]]
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.FastBlurThreshold:100]]
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.Upscale.Quality:3]]
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.Tonemapper.GrainQuantization:1]]
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.LightShaftQuality:1]]
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.Filter.SizeScale:1]]
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.Tonemapper.Quality:5]]
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.07.17-01.38.01:764][  0]LogConfig: Setting CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.07.17-01.38.01:765][  0]LogConfig: Setting CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.07.17-01.38.01:765][  0]LogConfig: Setting CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.07.17-01.38.01:765][  0]LogConfig: Setting CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.07.17-01.38.01:765][  0]LogConfig: Setting CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.07.17-01.38.01:765][  0]LogConfig: Setting CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.07.17-01.38.01:765][  0]LogConfig: Setting CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.07.17-01.38.01:765][  0]LogConfig: Setting CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.07.17-01.38.01:765][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:765][  0]LogConfig: Setting CVar [[r.Streaming.MipBias:0]]
[2025.07.17-01.38.01:765][  0]LogConfig: Setting CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.07.17-01.38.01:765][  0]LogConfig: Setting CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.07.17-01.38.01:765][  0]LogConfig: Setting CVar [[r.Streaming.Boost:1]]
[2025.07.17-01.38.01:765][  0]LogConfig: Setting CVar [[r.MaxAnisotropy:8]]
[2025.07.17-01.38.01:766][  0]LogConfig: Setting CVar [[r.VT.MaxAnisotropy:8]]
[2025.07.17-01.38.01:766][  0]LogConfig: Setting CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.07.17-01.38.01:766][  0]LogConfig: Setting CVar [[r.Streaming.PoolSize:1000]]
[2025.07.17-01.38.01:766][  0]LogConfig: Setting CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.07.17-01.38.01:766][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:766][  0]LogConfig: Setting CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.07.17-01.38.01:766][  0]LogConfig: Setting CVar [[r.RefractionQuality:2]]
[2025.07.17-01.38.01:766][  0]LogConfig: Setting CVar [[r.SSR.Quality:3]]
[2025.07.17-01.38.01:766][  0]LogConfig: Setting CVar [[r.SSR.HalfResSceneColor:0]]
[2025.07.17-01.38.01:766][  0]LogConfig: Setting CVar [[r.SceneColorFormat:4]]
[2025.07.17-01.38.01:766][  0]LogConsoleManager: Warning: Setting the console variable 'r.SceneColorFormat' with 'SetByScalability' was ignored as it is lower priority than the previous 'SetByProjectSetting'. Value remains '2'
[2025.07.17-01.38.01:766][  0]LogConfig: Setting CVar [[r.DetailMode:2]]
[2025.07.17-01.38.01:766][  0]LogConfig: Setting CVar [[r.TranslucencyVolumeBlur:1]]
[2025.07.17-01.38.01:766][  0]LogConfig: Setting CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.AnisotropicMaterials:1]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.SSS.Scale:1]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.SSS.SampleSet:2]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.SSS.Quality:1]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.SSS.HalfRes:0]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.SSGI.Quality:3]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.ParticleLightQuality:2]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.07.17-01.38.01:767][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.07.17-01.38.01:768][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.07.17-01.38.01:768][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.07.17-01.38.01:768][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.07.17-01.38.01:768][  0]LogConfig: Setting CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.07.17-01.38.01:768][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:768][  0]LogConfig: Setting CVar [[foliage.DensityScale:1.0]]
[2025.07.17-01.38.01:768][  0]LogConfig: Setting CVar [[grass.DensityScale:1.0]]
[2025.07.17-01.38.01:768][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:768][  0]LogConfig: Setting CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.07.17-01.38.01:768][  0]LogConfig: Setting CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.07.17-01.38.01:768][  0]LogConfig: Setting CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.07.17-01.38.01:768][  0]LogModuleManager: Warning: ModuleManager: Unable to load module 'ExampleDeviceProfileSelector'  - 0 instances of that module name found.
[2025.07.17-01.38.01:768][  0]LogInit: Applying CVar settings loaded from the selected device profile: [Windows]
[2025.07.17-01.38.01:768][  0]LogHAL: Display: Platform has ~ 32 GB [34302164992 / 34359738368 / 32], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.07.17-01.38.01:768][  0]LogConfig: Setting CVar [[r.DynamicRes.OperationMode:2 ; force on]]
[2025.07.17-01.38.01:768][  0]LogConfig: Setting CVar [[r.DynamicRes.FrameTimeBudget:33.33;]]
[2025.07.17-01.38.01:768][  0]LogConfig: Setting CVar [[r.DynamicRes.MaxScreenPercentage:100;1080p]]
[2025.07.17-01.38.01:769][  0]LogConfig: Setting CVar [[r.DynamicRes.MinScreenPercentage:66.66;720p]]
[2025.07.17-01.38.01:769][  0]LogConfig: Setting CVar [[r.BloomQuality:5 ; must have this for dynamic res due to unreal engine bug.]]
[2025.07.17-01.38.01:769][  0]LogConfig: Setting CVar [[r.Vsync:1]]
[2025.07.17-01.38.01:769][  0]LogConfig: Setting CVar [[r.TemporalAA.Upsampling:1]]
[2025.07.17-01.38.01:769][  0]LogConfig: Setting CVar [[r.SecondaryScreenPercentage.GameViewport:100;83.33 playstation seems capeable of native res]]
[2025.07.17-01.38.01:769][  0]LogInit: Warning: Creating unregistered Device Profile CVar: [[wwgrf.MenuScreenPercentage:100]]
[2025.07.17-01.38.01:769][  0]LogInit: Warning: Creating unregistered Device Profile CVar: [[wwgrf.InGameScreenPercentage:100]]
[2025.07.17-01.38.01:769][  0]LogInit: Warning: Creating unregistered Device Profile CVar: [[wwui.EnableAutoSlateLayoutCaching:1;This will toggle Slate.EnableLayoutCaching on and off when creating ui elements]]
[2025.07.17-01.38.01:769][  0]LogConfig: Setting CVar [[r.StaticMesh.StripDistanceFieldDataDuringLoad:1]]
[2025.07.17-01.38.01:769][  0]LogConfig: Setting CVar [[sg.EffectsQuality:1]]
[2025.07.17-01.38.01:769][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@1] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:769][  0]LogConfig: Setting CVar [[r.TranslucencyLightingVolumeDim:32]]
[2025.07.17-01.38.01:769][  0]LogConfig: Setting CVar [[r.RefractionQuality:0]]
[2025.07.17-01.38.01:769][  0]LogConfig: Setting CVar [[r.SSR.Quality:0]]
[2025.07.17-01.38.01:769][  0]LogConfig: Setting CVar [[r.SSR.HalfResSceneColor:1]]
[2025.07.17-01.38.01:769][  0]LogConfig: Setting CVar [[r.SceneColorFormat:3]]
[2025.07.17-01.38.01:769][  0]LogConsoleManager: Warning: Setting the console variable 'r.SceneColorFormat' with 'SetByScalability' was ignored as it is lower priority than the previous 'SetByProjectSetting'. Value remains '2'
[2025.07.17-01.38.01:769][  0]LogConfig: Setting CVar [[r.DetailMode:1]]
[2025.07.17-01.38.01:769][  0]LogConfig: Setting CVar [[r.TranslucencyVolumeBlur:0]]
[2025.07.17-01.38.01:769][  0]LogConfig: Setting CVar [[r.MaterialQualityLevel:2 ; Medium quality]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.AnisotropicMaterials:0]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.SSS.Scale:0.75]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.SSS.SampleSet:0]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.SSS.Quality:0]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.SSS.HalfRes:1]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.SSGI.Quality:1]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.EmitterSpawnRateScale:0.25]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.ParticleLightQuality:0]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:1]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:32.0]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.SampleCountMax:32.0]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.07.17-01.38.01:770][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.SkyLight.RealTimeReflectionCapture:0]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[sg.TextureQuality:2]]
[2025.07.17-01.38.01:771][  0]LogConfig: Applying CVar settings from Section [TextureQuality@2] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.Streaming.MipBias:0]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.Streaming.Boost:1]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.MaxAnisotropy:4]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.VT.MaxAnisotropy:8]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.Streaming.LimitPoolSizeToVRAM:1]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.Streaming.PoolSize:800]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.Streaming.PoolSize:1500]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.CapsuleMaxIndirectOcclusionDistance:100;was 200]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.CapsuleMaxDirectOcclusionDistance:750;was 400]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.shadow.UseOctreeForculling:0;]]
[2025.07.17-01.38.01:771][  0]LogConfig: Setting CVar [[r.CapsuleIndirectConeAngle:.7]]
[2025.07.17-01.38.01:771][  0]LogInit: Going up to parent DeviceProfile [PS4]
[2025.07.17-01.38.01:771][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:772][  0]LogConfig: Setting CVar [[r.SkeletalMeshLODBias:0]]
[2025.07.17-01.38.01:772][  0]LogConfig: Setting CVar [[r.ViewDistanceScale:1.0]]
[2025.07.17-01.38.01:772][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@Cine] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:772][  0]LogConfig: Setting CVar [[r.PostProcessAAQuality:6]]
[2025.07.17-01.38.01:772][  0]LogConsoleManager: Warning: Setting the console variable 'r.PostProcessAAQuality' with 'SetByScalability' was ignored as it is lower priority than the previous 'SetByProjectSetting'. Value remains '3'
[2025.07.17-01.38.01:772][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:772][  0]LogConfig: Setting CVar [[r.LightFunctionQuality:1]]
[2025.07.17-01.38.01:772][  0]LogConfig: Setting CVar [[r.ShadowQuality:5]]
[2025.07.17-01.38.01:772][  0]LogConfig: Setting CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.07.17-01.38.01:772][  0]LogConfig: Setting CVar [[r.Shadow.MaxResolution:2048]]
[2025.07.17-01.38.01:772][  0]LogConfig: Setting CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.07.17-01.38.01:772][  0]LogConfig: Setting CVar [[r.Shadow.RadiusThreshold:0.005]]
[2025.07.17-01.38.01:772][  0]LogConfig: Setting CVar [[r.Shadow.DistanceScale:1.0]]
[2025.07.17-01.38.01:772][  0]LogConfig: Setting CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.07.17-01.38.01:772][  0]LogConfig: Setting CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.07.17-01.38.01:772][  0]LogConfig: Setting CVar [[r.DistanceFieldShadowing:1]]
[2025.07.17-01.38.01:772][  0]LogConfig: Setting CVar [[r.DistanceFieldAO:1]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.AOQuality:2]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.VolumetricFog:1]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.CapsuleShadows:1]]
[2025.07.17-01.38.01:773][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.MotionBlurQuality:4]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.AmbientOcclusionLevels:-1]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.DepthOfFieldQuality:2]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.RenderTargetPoolMin:400]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.LensFlareQuality:2]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.SceneColorFringeQuality:1]]
[2025.07.17-01.38.01:773][  0]LogConsoleManager: Warning: Setting the console variable 'r.SceneColorFringeQuality' with 'SetByScalability' was ignored as it is lower priority than the previous 'SetByProjectSetting'. Value remains '0'
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.EyeAdaptationQuality:2]]
[2025.07.17-01.38.01:773][  0]LogConfig: Setting CVar [[r.BloomQuality:5]]
[2025.07.17-01.38.01:773][  0]LogConsoleManager: Setting the console variable 'r.BloomQuality' with 'SetByScalability' was ignored as it is lower priority than the previous 'SetByDeviceProfile'. Value remains '5'
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.FastBlurThreshold:100]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.Upscale.Quality:3]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.Tonemapper.GrainQuantization:1]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.LightShaftQuality:1]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.Filter.SizeScale:1]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.Tonemapper.Quality:5]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.07.17-01.38.01:774][  0]LogConfig: Setting CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.07.17-01.38.01:775][  0]LogConsoleManager: Setting the console variable 'sg.TextureQuality' with 'SetByScalability' was ignored as it is lower priority than the previous 'SetByDeviceProfile'. Value remains '2'
[2025.07.17-01.38.01:775][  0]LogConsoleManager: Setting the console variable 'sg.EffectsQuality' with 'SetByScalability' was ignored as it is lower priority than the previous 'SetByDeviceProfile'. Value remains '1'
[2025.07.17-01.38.01:775][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:775][  0]LogConfig: Setting CVar [[foliage.DensityScale:1.0]]
[2025.07.17-01.38.01:775][  0]LogConfig: Setting CVar [[grass.DensityScale:1.0]]
[2025.07.17-01.38.01:775][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [W:/NRL/Saved/Config/Windows/Scalability.ini]
[2025.07.17-01.38.01:775][  0]LogConfig: Setting CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.07.17-01.38.01:775][  0]LogConfig: Setting CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.07.17-01.38.01:775][  0]LogConfig: Setting CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.07.17-01.38.01:775][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.07.17-01.38.01:775][  0]LogConfig: Setting CVar [[net.UseAdaptiveNetUpdateFrequency:0]]
[2025.07.17-01.38.01:776][  0]LogConfig: Setting CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.07.17-01.38.01:776][  0]LogConfig: Setting CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.07.17-01.38.01:776][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [W:/NRL/Saved/Config/Windows/Engine.ini]
[2025.07.17-01.38.01:776][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [W:/NRL/Saved/Config/Windows/Editor.ini]
[2025.07.17-01.38.01:776][  0]LogInit: Computer: ALEXANDER-PC
[2025.07.17-01.38.01:776][  0]LogInit: User: Alexander
[2025.07.17-01.38.01:776][  0]LogInit: CPU Page size=4096, Cores=12
[2025.07.17-01.38.01:776][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.07.17-01.38.01:776][  0]LogMemory: Memory total: Physical=31.9GB (32GB approx)
[2025.07.17-01.38.01:776][  0]LogMemory: Platform Memory Stats for Windows
[2025.07.17-01.38.01:776][  0]LogMemory: Process Physical Memory: 140.55 MB used, 140.55 MB peak
[2025.07.17-01.38.01:776][  0]LogMemory: Process Virtual Memory: 124.09 MB used, 124.09 MB peak
[2025.07.17-01.38.01:776][  0]LogMemory: Physical Memory: 14956.11 MB used,  17756.99 MB free, 32713.09 MB total
[2025.07.17-01.38.01:777][  0]LogMemory: Virtual Memory: 134193784.00 MB used,  23947.02 MB free, 134217728.00 MB total
[2025.07.17-01.38.01:799][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.07.17-01.38.01:911][  0]LogInit: Physics initialised using underlying interface: PhysX
[2025.07.17-01.38.01:912][  0]LogInit: Using OS detected language (en-GB).
[2025.07.17-01.38.01:912][  0]LogInit: Using OS detected locale (en-AU).
[2025.07.17-01.38.01:914][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so the 'en' localization will be used.
[2025.07.17-01.38.02:159][  0]LogInit: Setting process to per monitor DPI aware
[2025.07.17-01.38.02:176][  0]LogWindowsTextInputMethodSystem: Display: IME system deactivated.
[2025.07.17-01.38.02:208][  0]LogSlate: New Slate User Created.  User Index 0, Is Virtual User: 0
[2025.07.17-01.38.02:209][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.07.17-01.38.02:643][  0]LogD3D11RHI: Loaded GFSDK_Aftermath_Lib.x64.dll
[2025.07.17-01.38.02:648][  0]LogHMD: Failed to enumerate extensions. Please check that you have a valid OpenXR runtime installed.
[2025.07.17-01.38.02:648][  0]LogD3D11RHI: D3D11 min allowed feature level: 11_0
[2025.07.17-01.38.02:650][  0]LogD3D11RHI: D3D11 max allowed feature level: 11_0
[2025.07.17-01.38.02:650][  0]LogD3D11RHI: D3D11 adapters:
[2025.07.17-01.38.02:843][  0]LogD3D11RHI:    0. 'NVIDIA GeForce RTX 2070 SUPER' (Feature Level 11_0)
[2025.07.17-01.38.02:843][  0]LogD3D11RHI:       7989/0/16356 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:2, VendorId:0x10de
[2025.07.17-01.38.02:848][  0]LogD3D11RHI:    1. 'Microsoft Basic Render Driver' (Feature Level 11_0)
[2025.07.17-01.38.02:848][  0]LogD3D11RHI:       0/0/16356 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:0, VendorId:0x1414
[2025.07.17-01.38.02:848][  0]LogD3D11RHI: Chosen D3D11 Adapter:
[2025.07.17-01.38.02:848][  0]LogD3D11RHI:     Description : NVIDIA GeForce RTX 2070 SUPER
[2025.07.17-01.38.02:848][  0]LogD3D11RHI:     VendorId    : 10de
[2025.07.17-01.38.02:848][  0]LogD3D11RHI:     DeviceId    : 1e84
[2025.07.17-01.38.02:848][  0]LogD3D11RHI:     SubSysId    : c7291462
[2025.07.17-01.38.02:848][  0]LogD3D11RHI:     Revision    : 00a1
[2025.07.17-01.38.02:848][  0]LogD3D11RHI:     DedicatedVideoMemory : 8377073664 bytes
[2025.07.17-01.38.02:848][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.07.17-01.38.02:848][  0]LogD3D11RHI:     SharedSystemMemory : 17151082496 bytes
[2025.07.17-01.38.02:849][  0]LogD3D11RHI:     AdapterLuid : 0 74046
[2025.07.17-01.38.02:858][  0]LogD3D11RHI: Creating new Direct3DDevice
[2025.07.17-01.38.02:858][  0]LogD3D11RHI:     GPU DeviceId: 0x1e84 (for the marketing name, search the web for "GPU Device Id")
[2025.07.17-01.38.02:858][  0]LogWindows: EnumDisplayDevices:
[2025.07.17-01.38.02:858][  0]LogWindows:    0. 'NVIDIA GeForce RTX 2070 SUPER' (P:1 D:1)
[2025.07.17-01.38.02:858][  0]LogWindows:    1. 'NVIDIA GeForce RTX 2070 SUPER' (P:0 D:1)
[2025.07.17-01.38.02:859][  0]LogWindows:    2. 'NVIDIA GeForce RTX 2070 SUPER' (P:0 D:0)
[2025.07.17-01.38.02:859][  0]LogWindows:    3. 'NVIDIA GeForce RTX 2070 SUPER' (P:0 D:0)
[2025.07.17-01.38.02:859][  0]LogWindows: DebugString: FoundDriverCount:4 
[2025.07.17-01.38.02:859][  0]LogD3D11RHI:     Adapter Name: NVIDIA GeForce RTX 2070 SUPER
[2025.07.17-01.38.02:859][  0]LogD3D11RHI:   Driver Version: 576.80 (internal:32.0.15.7680, unified:576.80)
[2025.07.17-01.38.02:859][  0]LogD3D11RHI:      Driver Date: 6-12-2025
[2025.07.17-01.38.02:859][  0]LogRHI: Texture pool is 5592 MB (70% of 7989 MB)
[2025.07.17-01.38.02:859][  0]LogD3D11RHI: Creating D3DDevice using adapter:
[2025.07.17-01.38.02:859][  0]LogD3D11RHI:     Description : NVIDIA GeForce RTX 2070 SUPER
[2025.07.17-01.38.02:860][  0]LogD3D11RHI:     VendorId    : 10de
[2025.07.17-01.38.02:860][  0]LogD3D11RHI:     DeviceId    : 1e84
[2025.07.17-01.38.02:860][  0]LogD3D11RHI:     SubSysId    : c7291462
[2025.07.17-01.38.02:860][  0]LogD3D11RHI:     Revision    : 00a1
[2025.07.17-01.38.02:860][  0]LogD3D11RHI:     DedicatedVideoMemory : 8377073664 bytes
[2025.07.17-01.38.02:860][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.07.17-01.38.02:860][  0]LogD3D11RHI:     SharedSystemMemory : 17151082496 bytes
[2025.07.17-01.38.02:860][  0]LogD3D11RHI:     AdapterLuid : 0 74046
[2025.07.17-01.38.02:987][  0]LogD3D11RHI: RHI has support for 64 bit atomics
[2025.07.17-01.38.02:988][  0]LogD3D11RHI: Async texture creation enabled
[2025.07.17-01.38.02:988][  0]LogD3D11RHI: Array index from any shader is supported
[2025.07.17-01.38.02:990][  0]LogD3D11RHI: HDR output is supported on display 0 (NvId: 0x80061081).
[2025.07.17-01.38.03:006][  0]LogD3D11RHI: GPU Timing Frequency: 1000.000000 (Debug: 2 2)
[2025.07.17-01.38.03:039][  0]LogRHI: GeForceNow SDK initialized: 1
[2025.07.17-01.38.03:230][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AllDesktop'
[2025.07.17-01.38.03:258][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.07.17-01.38.03:258][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.07.17-01.38.03:258][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.07.17-01.38.03:258][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.07.17-01.38.03:258][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.07.17-01.38.03:258][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.07.17-01.38.03:258][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.07.17-01.38.03:258][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.07.17-01.38.03:258][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.07.17-01.38.03:258][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.07.17-01.38.03:299][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.07.17-01.38.03:299][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.07.17-01.38.03:326][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Lumin'
[2025.07.17-01.38.03:326][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LuminClient'
[2025.07.17-01.38.03:341][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacNoEditor'
[2025.07.17-01.38.03:353][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.07.17-01.38.03:365][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.07.17-01.38.03:377][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.07.17-01.38.03:402][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.07.17-01.38.03:402][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.07.17-01.38.03:419][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsNoEditor'
[2025.07.17-01.38.03:431][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.07.17-01.38.03:444][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.07.17-01.38.03:457][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.07.17-01.38.03:482][  0]LogSwitchPlatform: Display: Built without Switch device support.
[2025.07.17-01.38.03:482][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Switch'
[2025.07.17-01.38.03:483][  0]LogTargetPlatformManager: Display: Building Assets For Windows
[2025.07.17-01.38.03:508][  0]LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
[2025.07.17-01.38.03:623][  0]LogTextureFormatOodle: Display: Oodle Texture 2.9.0 init RDO Off
[2025.07.17-01.38.03:745][  0]LogRendererCore: Ray tracing is disabled. Reason: r.RayTracing=0.
[2025.07.17-01.38.03:745][  0]LogShaderCompilers: Guid format shader working directory is 34 characters bigger than the processId version (../../../../NRL/Intermediate/Shaders/WorkingDirectory/29216/).
[2025.07.17-01.38.03:746][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/85766572449F250121F09D87AF8B51FB/'.
[2025.07.17-01.38.03:746][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.07.17-01.38.03:746][  0]LogShaderCompilers: Cannot use XGE Shader Compiler as Incredibuild is not installed on this machine.
[2025.07.17-01.38.03:746][  0]LogShaderCompilers: Display: Using Local Shader Compiler.
[2025.07.17-01.38.04:339][  0]LogDerivedDataCache: Display: Max Cache Size: 2048 MB
[2025.07.17-01.38.04:370][  0]LogDerivedDataCache: Loaded boot cache 0.03s 54MB ../../../../NRL/DerivedDataCache/Boot.ddc.
[2025.07.17-01.38.04:371][  0]LogDerivedDataCache: Display: Loaded Boot cache: ../../../../NRL/DerivedDataCache/Boot.ddc
[2025.07.17-01.38.04:371][  0]LogDerivedDataCache: FDerivedDataBackendGraph:  Pak pak cache file ../../../../NRL/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.07.17-01.38.04:371][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchical cache Hierarchy.
[2025.07.17-01.38.04:371][  0]LogDerivedDataCache: FDerivedDataBackendGraph:  EnginePak pak cache file ../../../Engine/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.07.17-01.38.04:371][  0]LogDerivedDataCache: Unable to find inner node EnginePak for hierarchical cache Hierarchy.
[2025.07.17-01.38.04:378][  0]LogDerivedDataCache: Speed tests for ../../../Engine/DerivedDataCache took 0.01 seconds
[2025.07.17-01.38.04:378][  0]LogDerivedDataCache: Display: Performance to ../../../Engine/DerivedDataCache: Latency=0.02ms. RandomReadSpeed=320.86MBs, RandomWriteSpeed=281.95MBs. Assigned SpeedClass 'Local'
[2025.07.17-01.38.04:380][  0]LogDerivedDataCache: Using Local data cache path ../../../Engine/DerivedDataCache: Writable
