// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIScreenCareerTeamSquad.h"

#include "Rugby/RugbyGameInstance.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"

#include "Rugby/UI/GeneratedHeaders/WWUIScreenCareerTeamSquad_UI_Namespace.h"
#include "Rugby/UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "Rugby/UI/WWUICareerGlobal.h"

#include "WWUITranslationManager.h"

#include "Rugby/Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DBTeam.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3CompetitionTeamHelper.h"

#include "Rugby/Match/RugbyUnion/CompetitionMode/RUDBHelperInterface.h"

#include "Rugby/Utility/Helpers/SIFGameHelpers.h"
#include "Rugby/Utility/Helpers/SIFInGameHelpers.h"
#include "Rugby/Utility/Helpers/SIFUIHelpers.h"

#include "UI/Components/WWUIUserWidgetPlayerProfile.h"
#include "UI/Components/WWUIUserWidgetSquadLayout.h"
#include "UI/Components/WWUICustomScrollbar.h"
#include "UI/Screens/Modals/WWUIModalWarningMessage.h"

#include "Rugby/UI/Populators/WWUIPopulatorCareerTeamSquad.h"
#include "Rugby/UI/Populators/WWUIPopulatorInGameSquad.h"
#include "Rugby/UI/Screens/WWUIScreenCareerPlayerProfile.h"

#include "WWUITabSwitcher.h"
//#include "WWUILegendBox.h"
#include "WWUIScrollBox.h"
#include "WWUIListField.h"

#include "TextBlock.h"
#include "VerticalBox.h"
#include "HorizontalBox.h"
#include "WidgetTree.h"
#include "ProgressBar.h"
#include "Image.h"
#include "WWUIRichTextBlockWithTranslate.h"
#include "Match/HUD/RUHUDUpdater.h"

#define TRIGGER_DELAY_TIME		(1.0f)

static void PopulateTargetPlayerName(unsigned short target_id, MabString& name_out)
{
	if (target_id != DB_INVALID_ID)
	{
		RU_PlayerDB_Data player(target_id);

		// We are going to list both the first and last names.
		// We've got tons of room and it's clearer.
		name_out = MabString(0, "%s %s", player.GetFirstName().c_str(), player.GetLastName().c_str());
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::Startup(UWWUIStateScreenData* InData)
{
	// Get the career mode manager
	UWorld* pWorld = GetWorld();
	if (pWorld)
	{
		pRugbyGameInstance = Cast<URugbyGameInstance>(pWorld->GetGameInstance());
		if (pRugbyGameInstance)
		{
			pCareerModeManager = pRugbyGameInstance->GetCareerModeManager();
		}
	}

	if (!pCareerModeManager)
	{
		ensureMsgf(pCareerModeManager, TEXT("CareerSetup: Cannot get the career manager, this should not occur!"));
		return;
	}

	if (!pRugbyGameInstance)
	{
		ensureMsgf(pRugbyGameInstance, TEXT("CareerSetup: Cannot get the rugby game instance, this should not occur!"));
		return;
	}

	CurrentTab = ETeamSquadTab::TST_SQUAD;

	bFromInGame = false;
	CareerTeamSquadbReadOnly = false;
	InGameTeamSquadBenchStart = 7;

	bIsSomeAccountRestricted = SIFApplication::GetApplication()->IsAnyUserRestricted();
	bIsNonPrimaryRestricted = SIFApplication::GetApplication()->IsNonPrimaryUserRestricted();

	UWWUIStateScreenCareerTeamSquadData* pCareerTeamSquadData = Cast<UWWUIStateScreenCareerTeamSquadData>(InData);

	if (pCareerTeamSquadData)
	{
		TeamIndex = pCareerTeamSquadData->TeamIndex;
		TeamID = pCareerTeamSquadData->TeamID;
		CareerTeamSquadAtlasID = pCareerTeamSquadData->AtlasID;
		CareerTeamSquadbReadOnly = pCareerTeamSquadData->bReadOnly;
		bFromInGame = pCareerTeamSquadData->bInGame;
		mControllingPlayer = pCareerTeamSquadData->ControllingPlayer;
	}

#ifdef ENABLE_ANALYTICS
	if (CareerTeamSquadbReadOnly) 
	{
		pRugbyGameInstance->GetPlayerAnalyticsData().MarkMenuUsed(wwAnalyticsInGameMenuOption::OppositionTeamSquad);
	}
	else
	{ 
		pRugbyGameInstance->GetPlayerAnalyticsData().MarkMenuUsed(wwAnalyticsInGameMenuOption::MyTeamSquad);
	}
#endif
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::RegisterFunctions()
{
	AddInputAction("UI_Back", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerTeamSquad::OnBack), false, true); // B

	AddInputAction("RU_UI_ACTION_TABRIGHT", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerTeamSquad::IncrementTab)); 
	AddInputAction("RU_UI_ACTION_TABLEFT", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerTeamSquad::DecrementTab)); 

#if PLATFORM_SWITCH 
	AddInputAxis("UI_Triggers_Axis", FWWUIScreenAxisDelegate::CreateUObject(this, &UWWUIScreenCareerTeamSquad::OnRotationInput));
#else
	AddInputAction("UI_LeftTrigger", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerTeamSquad::CareerTeamSquadOnSetRoles));
#endif

	AddInputAction("RU_UI_ACTION_PLAYER_PROFILE", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerTeamSquad::CareerTeamSquadOnPlayerProfile)); // B
	
	// Now done in ExecuteTableFunction, this will mean we use enter to swap players with KB instead of 1
	//AddInputAction("RU_UI_ACTION_SWAP_PLAYER", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerTeamSquad::CareerTeamSquadOnSwapPlayer)); // B

	AddInputAction("RU_UI_ACTION_CAREERMODE_TEAM_AUTOFILL", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerTeamSquad::CareerTeamSquadOnTeamAutoFill)); // B

	//Set role inputs
	AddInputAction("RU_UI_ACTION_SET_CAPTAIN", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerTeamSquad::CareerTeamSquadOnSetCaptain));
	AddInputAction("RU_UI_ACTION_SET_KICKER", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerTeamSquad::CareerTeamSquadOnSetKicker));
	AddInputAction("RU_UI_ACTION_SET_PLAY_KICKER", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCareerTeamSquad::CareerTeamSquadOnSetPlayKicker));
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::OnInFocus()
{
	m_doneFadeIn = false;

	ugcRestrictionHandle = SIFApplication::GetApplication()->OnUGCRestrictionChanged.AddUObject(this, &UWWUIScreenCareerTeamSquad::HandleUGCRestrictionChange);

	OnNewTab(CurrentTab);

	UWidget* pPlayerScrollBoxWidget = FindChildWidget(WWUIScreenCareerTeamSquad_UI::PlayerScrollBox);
	if (bFromInGame)
	{
		pPlayerScrollBoxWidget = FindChildWidget(WWUIScreenCareerTeamSquad_UI::InGameScrollBox);
	}

	if (pPlayerScrollBoxWidget)
	{
		SetInitialFocus(pPlayerScrollBoxWidget);
	}

	//Initialise the custom scrollbar
	UWidget* pScrollbar = FindChildWidget(WWUIScreenCareerTeamSquad_UI::BP_CustomScrollbar);
	if (pScrollbar)
	{
		if (UWWUICustomScrollbar * customScrollbar = Cast<UWWUICustomScrollbar>(pScrollbar))
		{
			customScrollbar->InitialiseScrollbar();
		}
	}
	m_canTriggerAxis = true;
}

void UWWUIScreenCareerTeamSquad::OnOutFocus(bool ShouldOutFocus)
{
	SIFApplication::GetApplication()->OnUGCRestrictionChanged.Remove(ugcRestrictionHandle);

	if (m_triggerAxisHandle.IsValid())
	{
		UWWUIFunctionLibrary::StopTimer(m_triggerAxisHandle);
	}
}

//===============================================================================
//===============================================================================


void UWWUIScreenCareerTeamSquad::ExecuteTableFunction(FString InTableId, int InIdx, FString InString, FString InActionString)
{
	//context checks done inside the functions
	CareerTeamSquadOnSwapPlayer(nullptr);
	CareerTeamSquadOnSetCaptain(nullptr);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::TableOnSelectionChange(FString InTableId, int OldIdx, int NewIdx)
{
	CareerTeamSquadEventUpdateSquadList(NewIdx);
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerTeamSquad::OnSystemEvent(WWUINodeProperty& eventProperty)
{
	FString EventName = eventProperty.GetStringProperty("system_event");

	if (EventName == "career_squad_list_update")
	{
		FString ScrollBoxString = bFromInGame ? WWUIScreenCareerTeamSquad_UI::InGameScrollBox : WWUIScreenCareerTeamSquad_UI::PlayerScrollBox;
		UWWUIScrollBox* pPlayerListScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(ScrollBoxString));

		if (pPlayerListScrollBox)
		{
			CareerTeamSquadEventUpdateSquadList(pPlayerListScrollBox->GetSelectedIndex());
		}
	}

	return true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::OnBack(APlayerController* OwningPlayer)
{
	bool bSquadCanLeave = true;

	if (CurrentTab == ETeamSquadTab::TST_SQUAD)
	{
		if (CareerTeamSquadSwapPlayerIndex == -1)
		{
			if (!CareerTeamSquadbReadOnly)
			{
				int PositionBitField = SIFGameHelpers::GACompetitionGetMissingTitledPlayers(TeamID);
				if (PositionBitField != 0)
				{
					FString UnassignedTitlePopupString = SIFGameHelpers::GAGetMissingTitledPlayerStringFromBitField(PositionBitField);
					
					UWWUIModalWarningMessageData* modalData = NewObject<UWWUIModalWarningMessageData>();
					TArray<FModalButtonInfo> ButtonData;

					FWWUIModalDelegate ExitDelegate;
					ExitDelegate.BindLambda([this](APlayerController*) {m_canTriggerAxis = true; return true; });

					ButtonData.Add(FModalButtonInfo("[ID_POPUP_OK]", ExitDelegate));

					FString LegendString = "[ID_MAIN_MENU_HELP]";

					modalData->WarningDialogue = UnassignedTitlePopupString;
					modalData->LegendString = LegendString;
					modalData->ButtonData = ButtonData;

					modalData->CloseOnBackButton = true;
					modalData->CloseOnSelectButton = true;

					modalData->OnBackDelegate = ExitDelegate;


					pRugbyGameInstance->DealMenuAction(SCREEN_PUSH_MODAL, Screens_UI::WarningMessage, modalData);
					bSquadCanLeave = false;
					return;
				}
			}
		}
		else
		{
			CareerTeamSquadSwapPlayerIndex = -1;
			CareerTeamSquadSwapPlayerDatabaseID = 0;

			if (bFromInGame)
			{
				InGameTeamSquadRefreshPlayerList();
			}
			else
			{
				CareerTeamSquadRefreshPlayerList();
			}

			bSquadCanLeave = false;
		}

		//if (CareerTeamSquadbRolesSelectMode)
		//{
		//	CareerTeamSquadOnSetRoles(OwningPlayer);
		//	return;
		//}
	}

	if (pRugbyGameInstance && bSquadCanLeave)
	{
		pRugbyGameInstance->DealMenuAction(SCREEN_CANCEL, Screens_UI::CareerTeamSquad);
	}
}
//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::IncrementTab(APlayerController* OwningPlayer)
{
	UWWUITabSwitcher* pTabSwitcher = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::TabContainer));

	if (pTabSwitcher)
	{
		pTabSwitcher->IncrementTab();
		OnNewTab((ETeamSquadTab)pTabSwitcher->GetActiveTabID());
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::DecrementTab(APlayerController* OwningPlayer)
{
	UWWUITabSwitcher* pTabSwitcher = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::TabContainer));

	if (pTabSwitcher)
	{
		pTabSwitcher->DecrementTab();
		OnNewTab((ETeamSquadTab)pTabSwitcher->GetActiveTabID());
	}
}


//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::SetInitialFocus(UWidget* pScrollBoxWidget)
{
	if (pScrollBoxWidget)
	{
		//Find the first element of the main menu and set focus
		UWWUIScrollBox* pCurrentScrollBox = Cast<UWWUIScrollBox>(pScrollBoxWidget);

		if (pCurrentScrollBox)
		{
			APlayerController * playerController = SIFApplication::GetApplication()->GetPlayerControllerFromControllerId(mControllingPlayer);
			if (playerController)
			{
				pCurrentScrollBox->FocusFirstListField(playerController);
				pCurrentScrollBox->SetControllingPlayer(playerController);
			}
			else
			{
				pCurrentScrollBox->FocusFirstListField(SIFApplication::GetApplication()->GetMasterPlayerController());
				pCurrentScrollBox->SetControllingPlayer(SIFApplication::GetApplication()->GetMasterPlayerController());
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::OnNewTab(ETeamSquadTab NewMode)
{
	CurrentTab = NewMode;

	OnWindowEnter();
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::OnWindowEnter()
{
	UWidget* pHeaderSubtitleWidget = FindChildWidget(WWUIScreenCareerTeamSquad_UI::Subtitle);

	if (pHeaderSubtitleWidget && !bFromInGame)
	{
		WWUICareerGlobal::SetBreadCrumbFromTeamID(pHeaderSubtitleWidget, TeamID, true);
	}

	switch (CurrentTab)
	{
	case ETeamSquadTab::TST_SQUAD:
	{
		CareerTeamSquadOnWindowEnter();
	}
	break;
	case ETeamSquadTab::TST_PROFILE:
	{
		CareerTeamProfileOnWindowEnter();
	}
	break;
	default:
	{
		ensureMsgf(false, TEXT("Career Team Squad: Was passed an invalid new mode for OnNewTab."));
	}
	break;
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamProfileOnWindowEnter()
{
	/*if (bFromInGame)
	{
		DatabaseTeamData = SIFApplication::GetApplication()->GetActiveGameWorld()->GetTeam(TeamID);
	}
	else*/
	{
		DatabaseTeamData = SIFApplication::GetApplication()->GetGameDBHelper()->LoadTeamDBData(TeamID);
	}

	CareerTeamProfilePopulateTeamProfile();

	CareerTeamProfilePopulateTeamInfo();

	UWWUIRichTextBlockWithTranslate* pLegend = Cast< UWWUIRichTextBlockWithTranslate>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::LegendText));
	if (pLegend)
	{
		pLegend->SetText("[ID_COMPETITION_BACK_HELP]");
	}
	else
	{
		ensure(pLegend);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamProfilePopulateTeamInfo()
{
	if (DatabaseTeamData)
	{
		MabString TeamName = DatabaseTeamData->GetName();

		RUHUDUpdater::CensorTeamName(DatabaseTeamData->GetDbId(), TeamName);

		bool customTeam = SIFGameHelpers::GAGetTeamIsCustom(DatabaseTeamData->GetDbId());

		UTextBlock* pTeamNameText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::TextTeamName));
		
		if (pTeamNameText)
		{
			if (customTeam && ((bIsSomeAccountRestricted && DatabaseTeamData->GetDownloadIdUser() != "") || bIsNonPrimaryRestricted))
			{
				SetWidgetText(pTeamNameText, FText::FromString(UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]")).ToUpper());
			}
			else
			{
				SetWidgetText(pTeamNameText, FText::FromString(SIFGameHelpers::GAConvertMabStringToFString(TeamName)).ToUpper());
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamProfilePopulateTeamProfile()
{
	// Team profile will come from in Data
	// ProfileView LeftPanel

	// Logo
	UImage* pLogoImage = Cast<UImage>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::ImageTeamLogo));

	if (pLogoImage)
	{
		UTexture2D* pTexture = nullptr;

		MabString pathString = SIFGameHelpers::GAGetTeamLogoAssetPath(DatabaseTeamData->GetDbId());
		FString name = FString(pathString.c_str());
		pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name));

		if (pTexture)
		{
			pLogoImage->SetBrushFromTexture(pTexture, true);
		}
	}
	
	//logo bg
	UImage* pBackgroundImage = Cast<UImage>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::ImageTeamLogoBkgd));
	SIFUIHelpers::SetImageColourFromTeamColour(pBackgroundImage, DatabaseTeamData->GetDbId());

	// Location
	UTextBlock* pLocationText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::TextLocation));
	
	if (pLocationText)
	{
		unsigned short CountryID = DatabaseTeamData->GetAssociatedCountryId();
		if (CountryID != DB_INVALID_ID)
		{
			RL3DB_COUNTRY Country(CountryID);
			SetWidgetText(pLocationText, FText::FromString(Country.GetName()).ToUpper());
		}
	}

	// Set stadium names
	UVerticalBox* pGroundsVerticalBox = Cast<UVerticalBox>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::VerticalBoxGroundsContent));
	
	if (pGroundsVerticalBox)
	{
		int NumHomeStadiums = DatabaseTeamData->GetNumHomeStadiums();

		for (int i = 0; i < pGroundsVerticalBox->GetChildrenCount(); i++)
		{
			UTextBlock* pCurrentGroundText = Cast<UTextBlock>(pGroundsVerticalBox->GetChildAt(i));
			
			if (pCurrentGroundText)
			{
				if (i < NumHomeStadiums)
				{
					unsigned short StadiumDatabaseID = DatabaseTeamData->GetHomeStadium(i);
					RL3DB_STADIUM Stadium(StadiumDatabaseID);

					SetWidgetText(pCurrentGroundText, FText::FromString(Stadium.GetName()).ToUpper());
				}
				else
				{
					pCurrentGroundText->SetVisibility(ESlateVisibility::Hidden);
				}
			}
		}
	}

	// ProfileView MiddlePanel
	UTextBlock* pRatingText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::TextTeamStarRatingValue));
	UTextBlock* pCaptainText = Cast<UTextBlock>(FindChildOfTemplateWidget(FindChildWidget(WWUIScreenCareerTeamSquad_UI::RoleCaptain), WWUIScreenCareerTeamSquad_UI::TextRolePlayer));
	UTextBlock* pGoalKickerText = Cast<UTextBlock>(FindChildOfTemplateWidget(FindChildWidget(WWUIScreenCareerTeamSquad_UI::RoleGoalKicker), WWUIScreenCareerTeamSquad_UI::TextRolePlayer));
	UTextBlock* pPlayKickerText = Cast<UTextBlock>(FindChildOfTemplateWidget(FindChildWidget(WWUIScreenCareerTeamSquad_UI::RolePlayKicker), WWUIScreenCareerTeamSquad_UI::TextRolePlayer));

	if (pRatingText && pCaptainText != NULL && pGoalKickerText != NULL && pPlayKickerText != NULL)
	{
		RL3DB_TEAM RL3_Team((unsigned short)DatabaseTeamData->GetDbId());
		FString OpponentTeamRating = FString::Printf(TEXT("%0.2f"), RL3_Team.GetNormalisedRanking() * 100.0f);
		pRatingText->SetText(FText::FromString(OpponentTeamRating));

		FString CaptainString = CareerTeamProfileGetTeamPlayerName(DatabaseTeamData->GetCaptain());
		FString GoalKickerString = CareerTeamProfileGetTeamPlayerName(DatabaseTeamData->GetGoalKicker(0));
		FString PlayKickerString = CareerTeamProfileGetTeamPlayerName(DatabaseTeamData->GetPlayKicker(0));

		pCaptainText->SetText(FText::FromString(CaptainString).ToUpper());
		pGoalKickerText->SetText(FText::FromString(GoalKickerString).ToUpper());
		pPlayKickerText->SetText(FText::FromString(PlayKickerString).ToUpper());
	}

	UTextBlock* pScrumWeightText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::TextScrumWeight));

	if (pScrumWeightText != NULL)
	{
		const float ScrumWeightTotal = MabMath::FRound(CareerTeamProfileGetTeamRanking()); //Nick WWS Changed from ScrumWeight to Team Ranking
		pScrumWeightText->SetText(FText::FromString(FString::FromInt(ScrumWeightTotal))); // +UWWUITranslationManager::Translate("[ID_KILOGRAMS_ABBR]")).ToUpper());
	}

	// Set confidence

	UTextBlock* pConfidenceText = Cast<UTextBlock>(FindChildOfTemplateWidget(FindChildWidget(WWUIScreenCareerTeamSquad_UI::ConfidenceStatHorizontal), WWUIScreenCareerTeamSquad_UI::TextStatValue));

	UProgressBar* pConfidenceProgressBar = Cast<UProgressBar>(FindChildOfTemplateWidget(FindChildWidget(WWUIScreenCareerTeamSquad_UI::ConfidenceStatHorizontal), WWUIScreenCareerTeamSquad_UI::ProgressBarStat));
	
	if (pConfidenceText && pConfidenceProgressBar)
	{
		//Default to 50% if not in a career game
		float ConfidenceValue = 0.5f;

		if (SIFApplication::GetApplication()->GetCareerModeManager()->IsActive())
		{
			unsigned short instance_id = (unsigned short)SIFApplication::GetApplication()->GetCareerModeManager()->GetPlayersActiveCompetition()->GetInstanceId();

			RL3DB_COMPETITION_INSTANCE comp_inst(instance_id);
			ConfidenceValue = DatabaseTeamData->GetCompetitionConfidence(instance_id, comp_inst.GetCurrentSatellite());
		}

		SetWidgetText(pConfidenceText, FText::FromString(FString::FromInt(FMath::RoundToInt(ConfidenceValue * 100.0f))));
		pConfidenceProgressBar->SetPercent(ConfidenceValue);
	}

	// Right panel
	UHorizontalBox* pTeamStatsHorizontalBox = Cast<UHorizontalBox>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::HorizontalBoxTeamStats));

	if (pTeamStatsHorizontalBox)
	{
		for (int i = 0; i < pTeamStatsHorizontalBox->GetChildrenCount(); ++i)
		{
			UWidget* pCurrentWidget = pTeamStatsHorizontalBox->GetChildAt(i);
			UProgressBar* pCurrentStatBar = Cast<UProgressBar>(FindChildOfTemplateWidget(pCurrentWidget, WWUIScreenCareerTeamSquad_UI::ProgressBarCurrentPlayer));

			UTextBlock* pCurrentStatText = Cast<UTextBlock>(FindChildOfTemplateWidget(pCurrentWidget, WWUIScreenCareerTeamSquad_UI::TextStatValue));

			if (pCurrentStatBar && pCurrentStatText)
			{
				pCurrentWidget->SetVisibility(ESlateVisibility::Visible);

				float NormalizeDataValue = CareerTeamProfileGetDatabaseTeamStat(ETeamStat(i));

				float RoundedStatValue = FMath::RoundToInt(NormalizeDataValue * 100.0f);

				pCurrentStatBar->SetPercent(NormalizeDataValue);

				pCurrentStatText->SetText(FText::FromString(FString::FromInt(RoundedStatValue)));
			}
			else
			{
				pCurrentWidget->SetVisibility(ESlateVisibility::Collapsed);
			}
		}
	}
}

//===============================================================================
//===============================================================================

FString UWWUIScreenCareerTeamSquad::CareerTeamProfileGetTeamPlayerName(unsigned short InID)
{
	MabString name = "";

	PopulateTargetPlayerName(InID, name);

	bool isCustom = false;

	if(bIsSomeAccountRestricted)
	{
		RU_PlayerDB_Data player(InID);
		if (player.GetIsCustom())
		{
			isCustom = true;
		}
	}

	if (isCustom && ((bIsSomeAccountRestricted && DatabaseTeamData->GetDownloadIdUser() != "") || bIsNonPrimaryRestricted))
	{
		return UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]");
	}
	else
	{
		return SIFGameHelpers::GAConvertMabStringToFString(name);
	}
}

//===============================================================================
//===============================================================================

float UWWUIScreenCareerTeamSquad::CareerTeamProfileGetTeamRanking()
{
	// Nick WWS was GetScrum Weight, Chnaged to Get TeamRanking for RL

	// The scrum weight is the combined weight of all the forward positions.
	// According to http://en.wikipedia.org/wiki/File:Rugby_formation.svg we can tell if a player is a forward
	// if they have a position between 1 and 8.
	return (float)SIFApplication::GetApplication()->GetCareerModeManager()->GetCompetitionTeamHelper()->RecalculateTeamRanking((unsigned short)DatabaseTeamData->GetDbId());
}

//===============================================================================
//===============================================================================

float UWWUIScreenCareerTeamSquad::CareerTeamProfileGetDatabaseTeamStat(ETeamStat InStat)
{
	float AttributeValue = 0.0f;

	// Just going to do a big lookup table.

	switch (InStat)
	{
	case ETeamStat::TS_ATTACK:
	{
		AttributeValue = DatabaseTeamData->GetNormalisedAttack();
	}
		break;
	case ETeamStat::TS_DEFENCE:
	{
		AttributeValue = DatabaseTeamData->GetNormalisedDefence();
	}
		break;
	case ETeamStat::TS_RUCK:
	{
		//AttributeValue = DatabaseTeamData->GetNormalisedRuckAbility();
		AttributeValue = (float)SIFApplication::GetApplication()->GetCareerModeManager()->GetCompetitionTeamHelper()->GetTeamAgility((unsigned short)DatabaseTeamData->GetDbId()) / MAX_TEAM_ATTRIBUTE_VALUE;
	}
		break;
	case ETeamStat::TS_SCRUM:
	{
		//AttributeValue = DatabaseTeamData->GetNormalisedMaulAbility();
		AttributeValue = (float)SIFApplication::GetApplication()->GetCareerModeManager()->GetCompetitionTeamHelper()->GetTeamSpeed((unsigned short)DatabaseTeamData->GetDbId()) / MAX_TEAM_ATTRIBUTE_VALUE;
	}
		break;
	case ETeamStat::TS_MAUL:
	{
		//AttributeValue = DatabaseTeamData->GetNormalisedScrumAbility();
		AttributeValue = (float)SIFApplication::GetApplication()->GetCareerModeManager()->GetCompetitionTeamHelper()->GetTeamTackleAbility((unsigned short)DatabaseTeamData->GetDbId()) / MAX_TEAM_ATTRIBUTE_VALUE;
	}
		break;
	case ETeamStat::TS_LINE:
	{
		//AttributeValue = DatabaseTeamData->GetNormalisedLineoutAbility();
		AttributeValue = (float)SIFApplication::GetApplication()->GetCareerModeManager()->GetCompetitionTeamHelper()->GetTeamPassAccuracy((unsigned short)DatabaseTeamData->GetDbId()) / MAX_TEAM_ATTRIBUTE_VALUE;
	}
		break;
	}

	return AttributeValue;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamSquadOnWindowEnter()
{
	// Nick  WWS 7s to Womens //
	/*if (SIFGameHelpers::GAGetGameMode() == 1)
	{
		InGameTeamSquadTotalPlayers = 12;
		InGameTeamSquadBenchStart = 7;
	}
	else
	{ */
		InGameTeamSquadTotalPlayers = 17;
		InGameTeamSquadBenchStart = 13;
	//}

	CareerTeamSquadbRolesSelectMode = false;

	if (SIFGameHelpers::GAGetIsAProMode() && bFromInGame)
	{
		CareerTeamSquadbReadOnly = true;
	}

	if (!bFromInGame)
	{
		// load correct team into helper db_team.
		RUDBHelperInterface* pDatabaseHelper = pRugbyGameInstance->GetGameDBHelper();

		if (pDatabaseHelper)
		{
			pDatabaseHelper->LoadTeamDBData(TeamID);
		}
	}
	else
	{
		// Swap to the in game populator scrollbox.
		UWidgetSwitcher* pInGameWidgetSwitcher = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::WidgetSwitcherInGame));

		if (pInGameWidgetSwitcher)
		{
			pInGameWidgetSwitcher->SetActiveWidgetIndex((int32)ETeamSquadInGameWidgetIndex::IN_GAME);
		}

		UWidget* pFatigueHeader = FindChildWidget(WWUIScreenCareerTeamSquad_UI::TextFatigue);
		UWidget* pStatusHeader = FindChildWidget(WWUIScreenCareerTeamSquad_UI::TextStatus);

		if (pFatigueHeader && pStatusHeader)
		{
			pFatigueHeader->SetVisibility(ESlateVisibility::Visible);
			pStatusHeader->SetVisibility(ESlateVisibility::Visible);
		}
		else
		{
			ensure(pFatigueHeader && pStatusHeader);
		}

	}

	SIFGameHelpers::GASetTeamFaceGenerationEnabled(true);

	CareerTeamSquadSwapPlayerIndex = -1;
	CareerTeamSquadSwapPlayerDatabaseID = 0;

	// this gets generated on demand using data from populator
	//for i = 1, InGameTeamSquad.total do
	//	InGameTeamSquad.jersey_numbers[i] = i;
	//end

	UWWUIScrollBox* pInGameScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::InGameScrollBox));

	if (pInGameScrollBox)
	{
		UWWUIPopulatorInGameSquad* pInGamePopulator = Cast<UWWUIPopulatorInGameSquad>(pInGameScrollBox->GetPopulator());

		if (pInGamePopulator)
		{
			pInGamePopulator->SetListNode(pInGameScrollBox);
		}

		pInGameScrollBox->SetProperty(RUCSP_TEAM_INDEX, &TeamIndex, PROPERTY_TYPE_INT);
	}

	if (bFromInGame)
	{
		InGameTeamSquadRefreshPlayerList();
	}
	else
	{
		// Populators
		CareerTeamSquadRefreshPlayerList();
	}

	if (SIFGameHelpers::GAGetIsAProMode())
	{
		CareerTeamSquadbReadOnly = true;
	}

	CareerTeamSquadUpdateLegend();
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamSquadOnSetCaptain(APlayerController* OwningPlayer)
{
	if (!CareerTeamSquadbRolesSelectMode || CurrentTab != ETeamSquadTab::TST_SQUAD)
	{
		return;
	}

	if (bFromInGame)
	{
		if (CareerTeamSquadSelectedPlayerDatabaseID != 0 && CareerTeamSquadSelectedPlayerIndex < InGameTeamSquadBenchStart)
		{
			SIFInGameHelpers::SetAsCaptain(TeamIndex, CareerTeamSquadSelectedPlayerDatabaseID);

			InGameTeamSquadRefreshPlayerList();
		}
	}
	else
	{
		RUDBHelperInterface* pDatabaseHelper = pRugbyGameInstance->GetGameDBHelper();

		if (pDatabaseHelper)
		{
			RU_TeamDB_Data* pTeamDatabaseData = pDatabaseHelper->LoadTeamDBData(TeamID);

			if (CareerTeamSquadSelectedPlayerEligableForRoleSwap())
			{
				if (CareerTeamSquadSelectedPlayerDatabaseID != pTeamDatabaseData->GetCaptain())
				{
					pTeamDatabaseData->SetCaptain(CareerTeamSquadSelectedPlayerDatabaseID);

					CareerTeamSquadRefreshPlayerList();
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamSquadOnSetKicker(APlayerController* OwningPlayer)
{
	if (!CareerTeamSquadbRolesSelectMode || CurrentTab != ETeamSquadTab::TST_SQUAD)
	{
		return;
	}

	if (bFromInGame)
	{
		if (CareerTeamSquadSelectedPlayerDatabaseID != 0 && CareerTeamSquadSelectedPlayerIndex < InGameTeamSquadBenchStart)
		{
			SIFInGameHelpers::SetAsKicker(TeamIndex, CareerTeamSquadSelectedPlayerDatabaseID);

			InGameTeamSquadRefreshPlayerList();
		}
	}
	else
	{
		RUDBHelperInterface* pDatabaseHelper = pRugbyGameInstance->GetGameDBHelper();

		if (pDatabaseHelper)
		{
			RU_TeamDB_Data* pTeamDatabaseData = pDatabaseHelper->LoadTeamDBData(TeamID);

			if (CareerTeamSquadSelectedPlayerEligableForRoleSwap())
			{
				if (CareerTeamSquadSelectedPlayerDatabaseID != pTeamDatabaseData->GetGoalKicker(0))
				{
					pTeamDatabaseData->SetGoalKicker(0, CareerTeamSquadSelectedPlayerDatabaseID);

					CareerTeamSquadRefreshPlayerList();
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamSquadOnSetPlayKicker(APlayerController* OwningPlayer)
{
	if (!CareerTeamSquadbRolesSelectMode || CurrentTab != ETeamSquadTab::TST_SQUAD)
	{
		return;
	}

	if (bFromInGame)
	{
		if (CareerTeamSquadSelectedPlayerDatabaseID != 0 && CareerTeamSquadSelectedPlayerIndex < InGameTeamSquadBenchStart)
		{
			SIFInGameHelpers::SetAsPlayKicker(TeamIndex, CareerTeamSquadSelectedPlayerDatabaseID);

			InGameTeamSquadRefreshPlayerList();
		}
	}
	else
	{
		RUDBHelperInterface* pDatabaseHelper = pRugbyGameInstance->GetGameDBHelper();

		if (pDatabaseHelper)
		{
			RU_TeamDB_Data* pTeamDatabaseData = pDatabaseHelper->LoadTeamDBData(TeamID);

			if (CareerTeamSquadSelectedPlayerEligableForRoleSwap())
			{
				if (CareerTeamSquadSelectedPlayerDatabaseID != pTeamDatabaseData->GetPlayKicker(0))
				{
					pTeamDatabaseData->SetPlayKicker(0, CareerTeamSquadSelectedPlayerDatabaseID);

					CareerTeamSquadRefreshPlayerList();
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamSquadOnSetRoles(APlayerController* OwningPlayer)
{
	if (CurrentTab == ETeamSquadTab::TST_SQUAD)
	{
		if (CareerTeamSquadSwapPlayerIndex == -1 && !CareerTeamSquadbReadOnly)
		{
			CareerTeamSquadbRolesSelectMode = !CareerTeamSquadbRolesSelectMode;

			CareerTeamSquadUpdateLegend();
		}
	}

	m_triggerAxisHandle = UWWUIFunctionLibrary::OnTimer(TRIGGER_DELAY_TIME, FTimerDelegate::CreateUObject(this, &UWWUIScreenCareerTeamSquad::ResetAxisTrigger), false);
	m_canTriggerAxis = false;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamSquadOnPlayerProfile(APlayerController* OwningPlayer)
{
	if (CareerTeamSquadbRolesSelectMode || CurrentTab != ETeamSquadTab::TST_SQUAD || CareerTeamSquadSwapPlayerIndex != -1)
	{
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("CareerHUB.MySquadOptionOnClick"));

	//uint32 TeamID_temp = pCareerModeManager->GetPlayerControlledTeamIdOfNextMatch();

	//Team ID that was passed into this screen
	uint32 TeamID_temp = TeamID;

	if (bFromInGame)
	{
		TeamID_temp = SIFInGameHelpers::GetTeamId(TeamIndex);
	}

	if (TeamID_temp == 0)
	{
		return;
	}

	if (!bFromInGame)
	{
		SIFGameHelpers::GARequestFaceTeamRender(0, TeamID_temp, true, false);

		uint32 CompetitionID = pCareerModeManager->GetActiveCompetition()->GetInstanceId();
		// Message("GetCurrentPlayerTeamId 3");
		if (CompetitionID == 0)
		{
			return;
		}
	}

	UWWUIStateScreenCareerPlayerProfileData* inData = NewObject<UWWUIStateScreenCareerPlayerProfileData>();

	inData->PlayerIndex = CareerTeamSquadSelectedPlayerIndex;
	inData->PlayerDatabaseID = CareerTeamSquadSelectedPlayerDatabaseID;
	inData->TeamID = TeamID_temp;
	inData->AtlasID = CareerTeamSquadAtlasID; // For the faces
	inData->IsInGame = bFromInGame;

	if (bFromInGame)
	{
		inData->BreadCrumbOverride = "[ID_MANAGE_TEAM]";
	}

	SIFApplication::GetApplication()->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::CareerPlayerProfile, inData);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamSquadOnSwapPlayer(APlayerController* OwningPlayer)
{
	if (CareerTeamSquadbRolesSelectMode || CurrentTab != ETeamSquadTab::TST_SQUAD)
	{
		return;
	}

	if (!CareerTeamSquadbReadOnly)
	{
		if (CareerTeamSquadSwapPlayerIndex == -1)
		{
			CareerTeamSquadSwapPlayerDatabaseID = CareerTeamSquadSelectedPlayerDatabaseID;
			CareerTeamSquadSwapPlayerIndex = CareerTeamSquadSelectedPlayerIndex;

			if (bFromInGame)
			{
				InGameTeamSquadRefreshPlayerList();
			}
			else
			{
				CareerTeamSquadRefreshPlayerList();
			}
		}
		else
		{
			if (CareerTeamSquadSelectedPlayerDatabaseID != CareerTeamSquadSwapPlayerDatabaseID)
			{
				if (bFromInGame)
				{
					EValidInterchangeReason bValidSwap = EValidInterchangeReason::VIR_MAX;

					if (CareerTeamSquadSwapPlayerIndex < InGameTeamSquadBenchStart && CareerTeamSquadSelectedPlayerIndex < InGameTeamSquadBenchStart)
					{
						// -- Both players in first 15, can swap positions regardless of state.
						bValidSwap = SIFInGameHelpers::RequestPlayerSubstitution(TeamIndex, CareerTeamSquadSelectedPlayerDatabaseID, CareerTeamSquadSwapPlayerDatabaseID, CareerTeamSquadSelectedPlayerIndex, CareerTeamSquadSwapPlayerIndex);
					}
					else if (CareerTeamSquadSwapPlayerIndex < InGameTeamSquadBenchStart || CareerTeamSquadSelectedPlayerIndex < InGameTeamSquadBenchStart)
					{
						// -- Simplify situation, make 'swap_player' alway be the bench player.
						if (CareerTeamSquadSwapPlayerIndex < InGameTeamSquadBenchStart)
						{
							int32 temp = CareerTeamSquadSwapPlayerIndex;
							CareerTeamSquadSwapPlayerIndex = CareerTeamSquadSelectedPlayerIndex;
							CareerTeamSquadSelectedPlayerIndex = temp;

							temp = CareerTeamSquadSwapPlayerDatabaseID;
							CareerTeamSquadSwapPlayerDatabaseID = CareerTeamSquadSelectedPlayerDatabaseID;
							CareerTeamSquadSelectedPlayerDatabaseID = temp;
						}

						if (SIFInGameHelpers::IsFieldPlayerAValidSubstitution(CareerTeamSquadSelectedPlayerDatabaseID, TeamIndex))
						{
							if (SIFInGameHelpers::IsPlayerAValidSubstitution(CareerTeamSquadSwapPlayerDatabaseID, TeamIndex))
							{
								bValidSwap = SIFInGameHelpers::RequestPlayerSubstitution(TeamIndex, CareerTeamSquadSelectedPlayerDatabaseID, CareerTeamSquadSwapPlayerDatabaseID, CareerTeamSquadSelectedPlayerIndex, CareerTeamSquadSwapPlayerIndex);
							}
						}
					}

					if (bValidSwap == EValidInterchangeReason::VIR_VALID)
					{
						UWWUIScrollBox* pInGameScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::InGameScrollBox));

						if (pInGameScrollBox)
						{
							bool bIsTrue = true;
							pInGameScrollBox->SetProperty(RUCSP_REFRESH_ON_SWAP, &bIsTrue, PROPERTY_TYPE_BOOL);
						}
					}
					else
					{
						FString reasonText;

						switch (bValidSwap)
						{
						case EValidInterchangeReason::VIR_ALREADY_SWAPPED:
							reasonText = "[ID_INVALID_SUBSTITUTION_PLAYER_BEING_SWAPPED]";
							break;
						case EValidInterchangeReason::VIR_LIMIT_REACHED:
							reasonText = "[ID_INVALID_SUBSTITUTION_LIMIT_REACHED]";
							break;
						case EValidInterchangeReason::VIR_18TH_NOT_READY:
							reasonText = "[ID_INVALID_SUBSTITUTION_CANT_USE_18TH_YET]";
							break;
						case EValidInterchangeReason::VIR_MAX:
						default:
							reasonText = "[ID_INVALID_SUBSTITUTION_UNDEFINED_REASON]";
							break;
						}

						FTimerDelegate TimerDelegate;
						TimerDelegate.BindLambda([reasonText]() {
							TArray<FModalButtonInfo> ButtonInfo;
							ButtonInfo.Add(FModalButtonInfo("[ID_POPUP_OK]"));
							SIFUIHelpers::LaunchWarningPopup(reasonText, "[ID_POPUP_HELPTEXT_BACK]", ButtonInfo);
						});
						UWWUIFunctionLibrary::OnFrameDelay(1, TimerDelegate);
					}

					CareerTeamSquadSwapPlayerIndex = -1;
					CareerTeamSquadSwapPlayerDatabaseID = 0;

					InGameTeamSquadRefreshPlayerList();
				}
				else
				{
					pCareerModeManager->SwapPlayers(TeamID, CareerTeamSquadSwapPlayerDatabaseID, CareerTeamSquadSwapPlayerIndex, CareerTeamSquadSelectedPlayerDatabaseID, CareerTeamSquadSelectedPlayerIndex);

					// Populators
					UWWUIScrollBox* pPlayerListScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::PlayerScrollBox));

					if (pPlayerListScrollBox)
					{
						UWWUIPopulatorCareerTeamSquad* pCareerSquadPopulator = Cast<UWWUIPopulatorCareerTeamSquad>(pPlayerListScrollBox->GetPopulator());

						if (pCareerSquadPopulator)
						{
							pCareerSquadPopulator->SetRefreshOnSwap(true);
							pCareerSquadPopulator->SetSwapPlayerID(0);
							pCareerSquadPopulator->SetSelectedPlayerID(CareerTeamSquadSelectedPlayerDatabaseID);
							pCareerSquadPopulator->SetFromInGame(bFromInGame);
						}

						// Set selected to the swap player.
						CareerTeamSquadSelectedPlayerDatabaseID = CareerTeamSquadSwapPlayerDatabaseID;
						CareerTeamSquadSelectedPlayerIndex = CareerTeamSquadSwapPlayerIndex;

						// And clear the swap.
						CareerTeamSquadSwapPlayerIndex = -1;
						CareerTeamSquadSwapPlayerDatabaseID = 0;

						CareerTeamSquadRefreshPlayerList();
					}
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamSquadOnTeamAutoFill(APlayerController* OwningPlayer)
{
	if (CareerTeamSquadbRolesSelectMode || CurrentTab != ETeamSquadTab::TST_SQUAD || bFromInGame)
	{
		return;
	}

	if (!CareerTeamSquadbReadOnly)
	{
		pCareerModeManager->AutoGenerateSquadLineup(TeamID);

		CareerTeamSquadSetRefreshOnSwap(true);

		CareerTeamSquadRefreshPlayerList();
	}
}

void UWWUIScreenCareerTeamSquad::OnRotationInput(float AxisValue, APlayerController* OwningPlayer)
{
	if (m_canTriggerAxis)
	{
		if (AxisValue < 0.0f)
		{
			CareerTeamSquadOnSetRoles(OwningPlayer);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamSquadInitSquadLayout()
{
	// Message("CareerTeamSquad.InitSquadLayout");

	int32 Total = 0;

	// Nick  WWS 7s to Womens //
	/*if (SIFGameHelpers::GAGetGameMode() == 1)
	{
		Total = 13;
	}
	else
	{*/
		Total = 24;
	//}

	UWWUIUserWidgetSquadLayout* pSquadLayout = Cast<UWWUIUserWidgetSquadLayout>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::SquadField));

	if (pSquadLayout)
	{
		TArray<EPlayerState> PlayerStates;

		if (bFromInGame)
		{
			UWWUIScrollBox* pScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::InGameScrollBox));
			if (pScrollbox)
			{
				TArray<int32> jersey_numbers;
				jersey_numbers.SetNumZeroed(InGameTeamSquadTotalPlayers);

				FString jersey_numbers_str = pScrollbox->GetStringProperty(RUCSP_JERSEY_NUMBERS_PROPERTY);

				for (int32 i = 0; i < InGameTeamSquadTotalPlayers; i++)
				{
					jersey_numbers[i] = i + 1;
					if (jersey_numbers_str != "")
					{
						if (i < jersey_numbers_str.Len())
						{
							char ascii_code = jersey_numbers_str[i];
							jersey_numbers[i] = (int32)ascii_code - 65;	// -- 'A'
						}
					}
				}

				pSquadLayout->InitSquadLayout(jersey_numbers);
			}
			else
			{
				ensure(pScrollbox);
			}


			// Populators
			UWWUIScrollBox* pPlayerListScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::InGameScrollBox));

			if (pPlayerListScrollBox)
			{
				UWWUIPopulatorInGameSquad* pInGameSquadPopulator = Cast<UWWUIPopulatorInGameSquad>(pPlayerListScrollBox->GetPopulator());

				if (pInGameSquadPopulator)
				{
					PlayerStates = pInGameSquadPopulator->GetPlayerStates();
				}
			}
		}
		else
		{
			pSquadLayout->InitSquadLayout(TArray<int32>());
			// Populators
			UWWUIScrollBox* pPlayerListScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::PlayerScrollBox));

			if (pPlayerListScrollBox)
			{
				UWWUIPopulatorCareerTeamSquad* pCareerSquadPopulator = Cast<UWWUIPopulatorCareerTeamSquad>(pPlayerListScrollBox->GetPopulator());

				if (pCareerSquadPopulator)
				{
					PlayerStates = pCareerSquadPopulator->GetPlayerStates();
				}
			}
		}

		// ----------------------------------------------------
		// Read encoded player states from game(RUCareerDraftPopulator::Refresh)
		int32 NumPlayers = 40;
		int32 RemainingHasExpired = 0;

		for (int i = 0; i < NumPlayers; i++)
		{
			int PlayerState = 0;

			if (PlayerStates.Num() > 0)
			{
				if (i < PlayerStates.Num())
				{
					if (PlayerStates[i] == EPlayerState::X)
					{
						PlayerState = 1;
					}

					if (PlayerStates[i] == EPlayerState::Y)
					{
						PlayerState = bFromInGame ? 10 : 2;
					}
				}
			}

			if (i < Total)
			{
				pSquadLayout->Mark(i, PlayerState);
			}
			else if (!bFromInGame)
			{
				if (!PlayerState)
				{
					RemainingHasExpired = 1;
				}
				else if (PlayerState && RemainingHasExpired != 1)
				{
					RemainingHasExpired = 2;
				}
			}
		}

		if (bFromInGame)
		{
			pSquadLayout->UpdateSinbin();
		}
		else
		{
			pSquadLayout->Mark(Total, RemainingHasExpired);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamSquadUpdateLegend()
{
	FString AssistanceString = "";


	if (bFromInGame)
	{
		UTextBlock* pAssistanceText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::TextAssistance));

		if (pAssistanceText)
		{
			int subsRemaining = SIFInGameHelpers::GetNumberOfRemainingSubstitutions(TeamIndex);
			FString prefix_str = UWWUITranslationManager::Translate(FString("[ID_TEAM_MANAGEMENT_SUBSTITUTIONS_PER_MATCH]"));
			AssistanceString = FString::Printf(*prefix_str, subsRemaining);
			UWWUIFunctionLibrary::SetText(pAssistanceText, AssistanceString);
		}
	}
	else
	{
		int32 Total = 0;
		int32 FieldPlayers = 0;
		// Nick  WWS 7s to Womens //
		/*
		if (SIFGameHelpers::GAGetGameMode() == 1)
		{
			Total = 12;
			FieldPlayers = 7;
		}
		else
		{ */
			Total = 17;
			FieldPlayers = 13;
		//}

		if (CareerTeamSquadSelectedPlayerIndex < FieldPlayers)
		{
			uint32 Position = 1 << CareerTeamSquadSelectedPlayerIndex;
			AssistanceString = SIFGameHelpers::GAGetPositionText(Position);
		}
		else if (CareerTeamSquadSelectedPlayerIndex <= Total)
		{
			AssistanceString = "[ID_SUBSTITUTE]";
		}

		FString help_string = "";

		if (CareerTeamSquadbReadOnly)
		{
			help_string = "[ID_COMP_TEAM_MANAGEMENT_READ_ONLY_HELP]";
		}
		else if (CareerTeamSquadSwapPlayerIndex == -1)
		{
			if (CareerTeamSquadbRolesSelectMode)
			{
				if (CareerTeamSquadSelectedPlayerIndex < FieldPlayers)
				{
					help_string = "[ID_COMP_TEAM_MANAGEMENT_HELP_SETROLES]";
				}
				else
				{
					help_string = "[ID_COMP_TEAM_MANAGEMENT_HELP_SETROLES_BENCH]";
				}
			}
			else
			{
				if (CareerTeamSquadSelectedPlayerDatabaseID == 0)
				{
					help_string = bFromInGame ? "[ID_INGAME_TEAM_MANAGEMENT_HELP_NOPROFILE]" : "[ID_COMP_TEAM_MANAGEMENT_HELP_NOPROFILE]";
				}
				else
				{
					help_string = bFromInGame ? "[ID_INGAME_TEAM_MANAGEMENT_HELP]" : "[ID_COMP_TEAM_MANAGEMENT_HELP]";
				}
			}
		}
		else
		{
			help_string = "[ID_COMP_TEAM_MANAGEMENT_SWAP_HELP]";
			AssistanceString = "[ID_TEAM_MANAGEMENT_SWAP_PLAYER_HINT]";

			if (CareerTeamSquadSwapPlayerIndex >= 0 && CareerTeamSquadSelectedPlayerIndex < FieldPlayers)
			{
				uint32 Position = 1 << CareerTeamSquadSelectedPlayerIndex;

				AssistanceString = AssistanceString + " " + FString(SIFGameHelpers::GAGetPositionText(Position)); //
			}
		}

		UWWUIRichTextBlockWithTranslate* pLegend = Cast< UWWUIRichTextBlockWithTranslate>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::LegendText));
		if (pLegend)
		{
			pLegend->SetText(help_string);
		}
		else
		{
			ensure(pLegend);
		}

		UTextBlock* pAssistanceText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::TextAssistance));

		if (pAssistanceText)
		{
			SetWidgetText(pAssistanceText, FText::FromString(UWWUITranslationManager::Translate(AssistanceString)).ToUpper());
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamSquadEventUpdateSquadList(int32 NewIdx)
{
	UWWUIScrollBox* pPlayerListScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::PlayerScrollBox));

	if (bFromInGame)
	{
		pPlayerListScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::InGameScrollBox));
	}

	if (pPlayerListScrollBox)
	{
		UWWUIListField* pSelectedListField = Cast<UWWUIListField>(pPlayerListScrollBox->GetListField(NewIdx));

		if (pSelectedListField)
		{
			// Get player id from selected node
			CareerTeamSquadSelectedPlayerDatabaseID = pSelectedListField->GetIntProperty(RUCSP_PLAYER_ID);
			CareerTeamSquadSelectedPlayerIndex = pSelectedListField->GetIntProperty(RUCSP_PLAYER_LIST_INDEX);

			if (CareerTeamSquadSelectedPlayerIndex == MAX_int32 || CareerTeamSquadSelectedPlayerDatabaseID == MAX_int32)
			{
				return;
			}

			int32 atlas_index = CareerTeamSquadAtlasID; // 0
			// if CareerTeamSquad.read_only then
			// atlas_index = 1
			// end

			// Update player stat
			UWidget* pProfileContainer = FindChildWidget(WWUIScreenCareerTeamSquad_UI::BP_PlayerProfileAndScrollboxTemplate);
			if (pProfileContainer)
			{
				UWWUIUserWidgetPlayerProfile* pPlayerProfile = Cast<UWWUIUserWidgetPlayerProfile>(FindChildOfTemplateWidget(pProfileContainer, WWUIScreenCareerTeamSquad_UI::PlayerProfile));

				if (pPlayerProfile)
				{
					if (CareerTeamSquadSwapPlayerIndex == -1)
					{
						//PlayerProfile.PopulateShortProfile(CareerTeamSquad.player_stats, CareerTeamSquad.TeamId, CareerTeamSquad.selected_player_db_id, CareerTeamSquad.selected_player_index, 0, atlas_index);

						pPlayerProfile->PopulateShortProfile(TeamID, CareerTeamSquadSelectedPlayerDatabaseID, 0, CareerTeamSquadSelectedPlayerIndex, false, atlas_index);
					}
					else
					{
						pPlayerProfile->PopulateShortProfile(TeamID, CareerTeamSquadSwapPlayerDatabaseID, CareerTeamSquadSelectedPlayerDatabaseID, CareerTeamSquadSelectedPlayerIndex, false, atlas_index);
					}
				}
				else { ensure(pPlayerProfile); }
			}
			else { ensure(pProfileContainer); }

			// Update squad layout
			UWWUIUserWidgetSquadLayout* pSquadLayout = Cast<UWWUIUserWidgetSquadLayout>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::SquadField));

			if (pSquadLayout)
			{
				CareerTeamSquadInitSquadLayout();
				pSquadLayout->SwapPlayer(CareerTeamSquadSwapPlayerIndex);
				pSquadLayout->Select(CareerTeamSquadSelectedPlayerIndex);
			}

			// Update help tip
			CareerTeamSquadUpdateLegend();
		}
	}
}

//===============================================================================
//===============================================================================

int32 UWWUIScreenCareerTeamSquad::CareerTeamSquadGetBenchStart()
{
	return 13; // Nick  WWS 7s to Womens // (SIFGameHelpers::GAGetGameMode() == 1 ? 7 : 13);
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCareerTeamSquad::CareerTeamSquadSelectedPlayerEligableForRoleSwap()
{
	return (CareerTeamSquadSelectedPlayerDatabaseID != 0 && CareerTeamSquadSelectedPlayerIndex < CareerTeamSquadGetBenchStart() && !CareerTeamSquadbReadOnly);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamSquadRefreshPlayerList()
{
	// Populators
	UWWUIScrollBox* pPlayerListScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::PlayerScrollBox));

	if (pPlayerListScrollBox)
	{
		UWWUIPopulatorCareerTeamSquad* pCareerSquadPopulator = Cast<UWWUIPopulatorCareerTeamSquad>(pPlayerListScrollBox->GetPopulator());

		if (pCareerSquadPopulator)
		{
			pCareerSquadPopulator->SetSwapPlayerID(CareerTeamSquadSwapPlayerDatabaseID);
			pCareerSquadPopulator->SetSelectedPlayerID(CareerTeamSquadSelectedPlayerDatabaseID);
			pCareerSquadPopulator->SetFromInGame(bFromInGame);
		}

		pPlayerListScrollBox->PopulateAndRefresh();

		pPlayerListScrollBox->SetSelectedIndex(CareerTeamSquadSelectedPlayerIndex);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::CareerTeamSquadSetRefreshOnSwap(bool NewVal)
{
	// Populators
	UWWUIScrollBox* pPlayerListScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::PlayerScrollBox));

	if (pPlayerListScrollBox)
	{
		UWWUIPopulatorCareerTeamSquad* pCareerSquadPopulator = Cast<UWWUIPopulatorCareerTeamSquad>(pPlayerListScrollBox->GetPopulator());

		if (pCareerSquadPopulator)
		{
			pCareerSquadPopulator->SetRefreshOnSwap(NewVal);
			pCareerSquadPopulator->SetFromInGame(bFromInGame);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCareerTeamSquad::InGameTeamSquadRefreshPlayerList()
{
	// Populators
	UWWUIScrollBox* pPlayerListScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::InGameScrollBox));

	if (pPlayerListScrollBox)
	{
		//UWWUIPopulatorCareerTeamSquad* pCareerSquadPopulator = Cast<UWWUIPopulatorCareerTeamSquad>(pPlayerListScrollBox->GetPopulator());

		//if (pCareerSquadPopulator)
		//{
		//	pCareerSquadPopulator->SetSwapPlayerID(CareerTeamSquadSwapPlayerDatabaseID);
		//	pCareerSquadPopulator->SetSelectedPlayerID(CareerTeamSquadSelectedPlayerDatabaseID);
		//	pCareerSquadPopulator->SetFromInGame(bFromInGame);
		//}

		pPlayerListScrollBox->SetProperty(RUCSP_TEAM_INDEX, &TeamIndex, PROPERTY_TYPE_INT);

		pPlayerListScrollBox->SetProperty(RUCSP_SWAP_PLAYER_ID, &CareerTeamSquadSwapPlayerDatabaseID, PROPERTY_TYPE_INT);
		pPlayerListScrollBox->SetProperty(RUCSP_PLAYER_ID, &CareerTeamSquadSelectedPlayerDatabaseID, PROPERTY_TYPE_INT);

		pPlayerListScrollBox->PopulateAndRefresh();

		pPlayerListScrollBox->SetSelectedIndex(CareerTeamSquadSelectedPlayerIndex);
	}
}

void UWWUIScreenCareerTeamSquad::ResetAxisTrigger()
{
	if (m_triggerAxisHandle.IsValid())
	{
		UWWUIFunctionLibrary::StopTimer(m_triggerAxisHandle);
		m_canTriggerAxis = true;
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenCareerTeamSquad::HandleUGCRestrictionChange(bool bIsRestricted)
{
	bIsSomeAccountRestricted = SIFApplication::GetApplication()->IsAnyUserRestricted();
	bIsNonPrimaryRestricted = SIFApplication::GetApplication()->IsNonPrimaryUserRestricted();
	
	UWWUITabSwitcher* pTabSwitcher = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenCareerTeamSquad_UI::TabContainer));

	if (pTabSwitcher)
	{
		OnNewTab((ETeamSquadTab)pTabSwitcher->GetActiveTabID());
	}
}