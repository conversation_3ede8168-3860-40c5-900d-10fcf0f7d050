/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#include "Mab/MabDebug.h"
#include "Match/Ball/SSBall.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/RUPlayerState.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Match/Cutscenes/SSCutSceneTags.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/Input/SSInputManager.h"
#include "Match/RUCoinTossEnums.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3ActiveCompetition.h"
#include "Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DBTeam.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseExtraTimeToss.h"
#include "Match/RugbyUnion/RUDatabaseConstants.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUInputPhaseDecision.h"
#include "Match/RugbyUnion/RUStadiumManager.h"
#include "Match/RugbyUnion/RUSubstitutionManager.h"
#include "Match/RugbyUnion/Rules/RURuleTriggerEnum.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/RugbyUnion/Statistics/RUStatisticsSystem.h"
#include "Match/SIFGameWorld.h"
#include "Match/SIFUIConstants.h"
#include "Match/SSGameTimer.h"
#include "Match/SSGameTimerHalfObserver.h"
#include "Match/SSSpatialHelper.h"
#include "Utility/RURandomNumberGenerator.h"
#include "Utility/Helpers/SIFGameHelpers.h"
#include "Utility/Helpers/SIFUIHelpers.h"
#include "Utility/Helpers/SIFInGameHelpers.h"
#include "Runtime/LevelSequence/Public/LevelSequencePlayer.h"
#include "Databases/RUGameDatabaseManager.h"
#include "Character/RugbyPlayerController.h"
#include "GameModes/RugbyGameState.h"
#include "CutSceneManager.h"
#include "FormationsManager.h"
#include "RugbyGameInstance.h"
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "FlowNodes/TrainingFlowNode.h"
#include "SIFFlowConstants.h"
#include "FlowNodes/FlowControlManager.h"
#include "Match/Audio/RUCrowdAudioReactionManager.h"
#include "Commentary/RUCommentary.h"
#include "RUPMPDirector.h"
#include "UI/Screens/WWUIScreenTrainingField.h"

#include "Utility/Soak/SIFSoakManager.h"
#include "Match/Debug/SIFDebug.h"
#include "Match/RugbyUnion/Rules/RURulesDebugSettings.h"

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
#include "Utility/consoleVars.h"
#include "DrawDebugHelpers.h"
#endif
#include "../Components/RUPlayerAttributes.h"


#ifdef INFINITE_REPLAY
static const float TRY_MAX_REWIND_TIME = 128.0f;
#else
static const float TRY_MAX_REWIND_TIME = 16.0f;
#endif

static const float PENALTY_MAX_REWIND_TIME = 13.0f;
static const float PENALTY_DURATION = 5.0f;
static const float KNOCKON_MAX_REWIND_TIME = 13.0f;
static const float KNOCKON_DURATION = 5.0f;
static const float FORWARD_PASS_MAX_REWIND_TIME = 13.0f;
static const float FORWARD_PASS_DURATION = 5.0f;
static const float DROPGOAL_MAX_REWIND_TIME = 6.0f;
static const float ADVANTAGE_MAX_REWIND_TIME = 6.0f;
static const float SUCCESSFUL_4020_KICK_TIME = 10.0f;

/// Default interchange position (if no locator found), note default z = 0.0f;
static const float INTERCHANGE_DEFAULT_X = -35.0f;
static const float INTERCHANGE_DEFAULT_ANGLE = -90.0f;

static const float CUTSCENE_SAFETY_CLAMP_DISTANCE = 5.0f;

static const int NUM_REPLAYS_TRY = 1;
static const int NUM_REPLAYS_TMO = 1;

static const bool DISABLE_SIMULATION = true;
static const bool ENABLE_SIMULATION = false;
static const bool DISABLE_SKIP = false;
static const bool ENABLE_SKIP = true;
static const bool SHOW_NON_ACTORS = true;	//false;
static const bool HIDE_NON_ACTORS = true;

static const bool SHOW_NON_ACTORS_APPROVED = false;

/// Default distance to offset negative reactions.
static const float NEGATIVE_REACTION_DISTANCE = 5.0f;

RUTeam* SSCutSceneManager::s_pRestartTeam = nullptr;

const char *CUTSCENE_START_CALLBACK = "CutsceneStart";

#ifdef ENABLE_SEVENS_MODE
const char *START_EXTRA_TIME_COIN_TOSS_GAME_PHASE_CALLBACK = "StartExtraTimeCoinTossPhase";
const char *START_POST_EXTRA_TIME_COIN_TOSS_GAME_PHASE_CALLBACK = "StartPostExtraTimeCoinTossPhase";
const char *COINTOSS_DECISION_WINDOW_CALLBACK = "CoinTossDecisionWindow";
//const char *COINTOSS_RESULT_WINDOW_CALLBACK = "CoinTossResultWindow";
#endif

const char *CUTSCENE_END_CALLBACK = "CutsceneEnd";
const char *MARK_AWARDED_CALLBACK = "MarkAwarded";
const char *FOURTYTWENTY_AWARDED_CALLBACK = "FourtyTwentyAwarded";
const char *PENALTY_CALLBACK = "Penalty";
const char *RESTART_ONFULL_CALLBACK = "RestartOutOnFull";
const char *BALLDEAD_CALLBACK = "BallDead";
const char *KNOCKON_CALLBACK = "Knockon";
const char* START_LINEOUT_CALLBACK = "StartLineout";
const char* START_TOUCH_SCRUM_CALLBACK = "StartTouchScrum";
const char *INJURY_END_CALLBACK = "InjuryEnd";
const char *START_PENALTY_SHOOT_CALLBACK = "StartKickPenaltyShoot";
const char *START_DROPOUT_CALLBACK = "StartDropout";
const char *END_DROPOUT_CALLBACK = "EndDropout";
const char *POP_MUSIC_CALLBACK = "PopMusicCallback";
const char *PUSH_MUSIC_CALLBACK = "PushMusicCallback";
const char *SKIP_COMMENTARY_CALLBACK = "SkipCommentaryCallback";
const char *DISALLOW_TRY_TMO_CALLBACK = "DisallowTryAfterTMO";
const char *COMMENTARY_TMO_TRY_DISALLOWED = "CommentaryTMOTryDisallowed";
const char *AWARD_TRY_TMO_CALLBACK = "AwardTryAfterTMO";
const char *START_TRY_PHASE_CALLBACK = "StartTryCutscenePhase";
const char *CONVERSION_BALL_PLACEMENT_CALLBACK = "ConversionBallPlacement";
const char *START_CONVERSION_PHASE_CALLBACK = "StartConversionPhase";
const char *START_POST_CONVERSION_PHASE_CALLBACK = "StartPostConversionPhase";
const char *START_PRE_KICKOFF_PHASE_CALLBACK = "StartPreKickOffPhase";
const char *SHOW_INTERCHANGE_HUD_CALLBACK = "ShowInterchangeHUD";
const char *NOTIFY_INTERCHANGE_STARTED_CALLBACK = "NotifyInterchangeStarted";
const char *START_SUBSTITION_LOAD_CALLBACK = "StartSubstituteLoad";
const char *MID_SUBSTITUTION_CALLBACK = "MidSubstitution";
const char *DO_SUBSTITUTION_CALLBACK = "DoSubstitution";
const char *PERFORM_REMAINING_INTERS_CALLBACK = "PerformRemainingInterchanges";
const char *NOTIFY_MID_INTERCHANGE_CALLBACK = "NotifyMidInterchange";
const char *SINBIN_RETURN_START_CALLBACK = "SinbinReturnStart";
const char *HOLD_SCREENWIPE_CALLBACK = "HoldScreenWipe";
const char *FULLTIME_WINDOW_CALLBACK = "FullTimeWindow";
const char *START_POST_GAME_PHASE_CALLBACK = "StartPostGamePhase";
const char *GAME_SIMULATION_WINDOW_CALLBACK		= "StartSimulationGamePhase";
const char *HALFTIME_WINDOW_CALLBACK = "HalfTimeWindow";
const char *RESTART_KICK_CALLBACK = "RestartKick";
const char *SET_PRE_KICKOFF_PHASE_CALLBACK = "SetPreKickOffPhase";
const char *TEAM_MANAGEMENT_CALLBACK = "TeamManagement";
const char *PRE_HAKA_CALLBACK = "PreHaka";
const char *PLAYER_WALKON_CALLBACK = "PlayerWalkon";
const char *POST_HAKA_CALLBACK = "PostHakak";
const char *START_KICKOFF_PHASE_CALLBACK = "StartKickOffPhase";
const char *SENT_OFF_CALLBACK = "SentOffEnd";
const char *FORWARD_PASS_CALLBACK = "ForwardPassCallback";
const char *INJURY_SELECT_CALLBACK = "SelectInjuryReplacementCallback";
const char *GRAND_FINAL_START_CALLBACK = "GrandFinalStartCallback";
const char *SETKICK_OFF_KICKER_CALLBACK = "SetKickOffKickerCallback";
const char *PRO_SENT_OFF_CALLBACK = "ProSentOffCallback";

const float STANDARD_BG_CUTSCENE_SEPARATION = 10.0f;


///-------------------------------------------------------------------------------
/// Helper function: Convert trophy name to ID.
///-------------------------------------------------------------------------------

static TROPHY_CUPIDS TranslateCupNameToId(const char* trophy_name)
{
	static const char *trophy_names[CUPIDX_COUNT] =
	{
		"wcup",			//CUPIDX_WORLD
		"aviva",		//CUPIDX_AVIVA
		"bled",			//CUPIDX_BLED
		"itm",			//CUPIDX_ITM
		"ranf",			//CUPIDX_RANF
		"gencup1",		//CUPIDX_GENCUP1
		"gencup2",		//CUPIDX_GENCUP2
		"ui_plate",		//CUPIDX_GENPLATE
		"ui_shield",	//CUPIDX_GENSHIELD
		"gentrophy",	//CUPIDX_GENTROPHY
		"genbowl",		//CUPIDX_GENBOWL
		"nrc",			//CUPIDX_NRC
		"acc",			//CUPIDX_ACC
		"rabodirect",	//CUPIDX_PRO12,			(NOW the RaboDirect Pro12 something or other)
		"prod2",		//CUPIDX_PROD2
		"lions",		//CUPIDX_LIONS,			// Lions tour (australia matches).
		"top14",		//CUPIDX_TOP14,
		"gencup3",		//CUPIDX_GENCUP3
		"rug"			//CUPIDX_SUPER15		// Super Rugby (rug for legacy and not breaking file paths)
	};

	for(int i=0; i<=static_cast<int>(CUPIDX_COUNT) - 1; i++)
	{
		if (strcmp(trophy_names[i], trophy_name) == 0)
		{
			return (TROPHY_CUPIDS)i;
		}
	}
	return CUPIDX_GENCUP1;
}


///-------------------------------------------------------------------------------
/// Helper function: Offset a transform for negative reactions (-zdist in local space).
///-------------------------------------------------------------------------------

static void OffsetNegativeReaction(MabMatrix &transform_restart, float zdist)
{
	FVector offset = FVector(0, 0, -zdist);
	transform_restart = MabMatrix::TransMatrix(offset) * transform_restart;
}

///-------------------------------------------------------------------------------
/// Set the cup to show during ui screens. (active competition hasn't been setup yet)
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetCompetitionUICup(int competition_db_id)
{
	RUGameDatabaseManager *database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();

	RUDB_COMP_DEF competition_definition;
	if (database_manager->LoadData(competition_definition, competition_db_id))
	{
		RUDB_COMP_TROPHY trophy_def;
		if (database_manager->LoadData(trophy_def, competition_definition.trophy_id))
		{
			competition_ui_trophy_id = (int)TranslateCupNameToId(trophy_def.GetFilename());
		}
	}
}

///-------------------------------------------------------------------------------
/// StartTutorialCutscene: Start a tutorial cutscene.
///-------------------------------------------------------------------------------

void SSCutSceneManager::StartTutorialCutscene(bool success, const MabMatrix &transform, bool hide_non_actors)
{
	SetUICutscene(CSEVENT_UNUSED);
	AddCutsceneAsyncPreloadElement(false);

	AddCutsceneCinematicElement(RUGamePhase::PLAY, DISABLE_SKIP, SWIPE_FADE, hide_non_actors);
	AddCinematicTLE(success ? CSEVENT_TUTORIAL_WIN : CSEVENT_TUTORIAL_LOSE, transform, SIDE_NONE);

	AddCutsceneCinematicElement(RUGamePhase::PLAY, DISABLE_SKIP, SWIPE_FADE, hide_non_actors);
	AddCinematicTLE(CSEVENT_TUTORIAL_PAN, MabMatrix::IDENTITY, SIDE_NONE);
}

///-------------------------------------------------------------------------------
/// StartNamedCutscene: Start a named cutscene.
///-------------------------------------------------------------------------------

void SSCutSceneManager::StartNamedCutscene(const char *cutscene_name, const MabMatrix &transform, bool hide_non_actors)
{
	AddCutsceneAsyncPreloadElement(false);

	AddCutsceneCinematicElement(RUGamePhase::PLAY, ENABLE_SKIP, SWIPE_FADE, hide_non_actors);
	AddNamedCinematicTLE(cutscene_name, transform, SIDE_NONE);

}

///-------------------------------------------------------------------------------
/// UIRequestCameraTransition: Handle UI requests. (UIBackground/Intro).
///-------------------------------------------------------------------------------

void SSCutSceneManager::UIRequestCameraTransition(const char* destination_window_name)
{

	MABLOGDEBUG("UI Camera Transition Request: %s", destination_window_name);

	if (strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_RUNON_WINDOW_NAME) == 0)
	{
		if (game && game->GetEvents())
		{
			//Hide the hud here as it shouldn't be showing but a cutscene can turn it on or off as needed.
			game->GetEvents()->hide_hud_tactics();
		}

		SetupPreGameCutScenes();
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_MATCH_RULINGS_SETTINGS_WINDOW_NAME) == 0 ||
			strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_GRAPHICS_SETTINGS_WINDOW_NAME) == 0 ||
			strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_SOUND_SETTINGS_WINDOW_NAME) == 0 ||
			strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_CAMERA_AND_VISUALS_SETTINGS_WINDOW_NAME) == 0 ||
			strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_CONTROLS_WINDOW_NAME) == 0 ||
			strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_LANGUAGE_SETTINGS_WINDOW_NAME) == 0
		)
	{
		if (SIFApplication::GetApplication()->GetActiveGameWorld()->IsSandbox()) return;

		SetUICutscene(CSEVENT_UI_COMPETITION_SELECT);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_MAIN_MENU_WINDOW_NAME) == 0 ||
			strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_CAREER_SETUP_WINDOW_NAME) == 0 ||
			strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_CUSTOMISE_EDIT_PLAYER_WINDOW_NAME) == 0)
	{
		SetUICutscene(CSEVENT_UI_MAIN_MENU, UI_CUTSCENE_CHANGE_RESTORE_STRIPS);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_SPLASH_TITLE_WINDOW_NAME) == 0)
	{
		//StartTitleIntro();
		SetUICutscene(CSEVENT_UI_TITLE);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_TRUBLU_LOGO_WINDOW_NAME) == 0)
	{
		SetUICutscene(CSEVENT_UI_MAIN_MENU);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_CUSTOMISE_TEAM_SELECT_TEAM_WINDOW_NAME) == 0)
	{
		SetUICutscene(CSEVENT_UI_MAIN_MENU, UI_CUTSCENE_CHANGE_RESTORE_STRIPS);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_SELECT_TEAMS_WINDOW_NAME) == 0)
	{
		SetUICutscene(CSEVENT_UI_QUICKMATCH_TEAMSELECT);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_ASSIGN_CONTROLLERS_WINDOW_NAME) == 0)
	{
		SetUICutscene(CSEVENT_UI_QUICKMATCH_CONTROLLER_SELECT);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_TRAINING_FIELD_WINDOW_NAME) == 0)
	{
		UWWUIScreenTrainingField* trainingField = Cast<UWWUIScreenTrainingField>(SIFApplication::GetApplication()->GetCurrentScreenTemplate());
		if (trainingField)
		{
			if (trainingField->EntryFromTutorialPauseMenu) return;
		}

		UFlowControlManager* flow_manager = SIFApplication::GetApplication()->GetFlowControlManager();
		UTrainingFlowNode* flow_node = flow_manager ? Cast<UTrainingFlowNode>(flow_manager->FetchNode(RugbyFlowNodeNames::SIF_TRAINING_FLOWNAME)) : nullptr;
		if (flow_node)
		{
			if (flow_node->GetTrainingPrompt() == TrainingPrompt::RunAround)
			{
				SetUICutscene(CSEVENT_HALFTIME_PAN, UI_CUTSCENE_CHANGE_SANDBOX_START);
			}
			else if (flow_node->GetTrainingPrompt() == TrainingPrompt::Tutorial)
			{
				//#DH, this is kind of gross, but running out of time.
				// https://wickedwitchdev.atlassian.net/browse/RC4-3617
				// Popups are causing loss of focus, then refocus is playing a BG cutscene
				if (trainingField && trainingField->IsDemoActive())
				{
					int currentTutorialState = SIFGameHelpers::GAGetCurrentTutorialState();

					if (currentTutorialState != 2) // Not in demo.
					{
						SIFGameHelpers::GARestartTutorial();
					}
					else // In demo.
					{
						SIFGameHelpers::GAStartTutorialDemo();
					}
					return;
				}

				// Request load of non-perm players after the pan has been loaded.
				load_non_permanent_players = true;
				SetUICutscene(CSEVENT_TUTORIAL_PAN, UI_CUTSCENE_CHANGE_TRAINING_START);
			}
			else if (flow_node->GetTrainingPrompt() == NetworkRunAround)
			{
				Reset();
			}
		}
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_MULTIPLAYER_LOBBY_WINDOW_NAME) == 0)
	{
		UFlowControlManager* flow_manager = SIFApplication::GetApplication()->GetFlowControlManager();
		UTrainingFlowNode* flow_node = flow_manager ? Cast<UTrainingFlowNode>(flow_manager->FetchNode(RugbyFlowNodeNames::SIF_TRAINING_FLOWNAME)) : nullptr;
		if (flow_node)
		{
			if (flow_node->GetTrainingPrompt() == NetworkRunAround)
			{
				//Reset();
				//StopAllCutScenes();
			}
		}
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_ONLINE_TRAINING_LOBBY_WINDOW_NAME) == 0
	//	|| strcmp(destination_window_name, RugbyUIWindowNames::RUUI_ONLINE_SELECT_TEAMS_WINDOW_NAME) == 0 		// Change from MultiplayerTrainingField (wasn't getting called).
#if PLATFORM_XBOXONE
		// I feel dirty for this, but Xbox One is having some technical difficulties complying with the rest of the games logic.
		|| strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_JOIN_INVITED_GAME_WINDOW_NAME) == 0
#endif
		)
	{
		SetUICutscene(CSEVENT_HALFTIME_PAN, UI_CUTSCENE_CHANGE_ONLINE_LOBBY_START);
	}
	else if (strstr(destination_window_name, RugbyUIWindowNames::RUUI_CUSTOMISE_PLAYER_STANDARD_IDENTIFIER)
		|| strstr(destination_window_name, RugbyUIWindowNames::RUUI_CUSTOMISE_KIT_STANDARD_IDENTIFIER))
	{
		SetupPlayerCustomisationCutscene(destination_window_name);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_COMPETITION_SELECT_WINDOW_NAME) == 0)
	{
		SetUICutscene(CSEVENT_UI_COMPETITION_SELECT);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_COMPETITION_CAREER_INITIAL_SAVE_WINDOW_NAME) == 0
		|| strcmp(destination_window_name, RugbyUIWindowNames::RUUI_COMPETITION_COMPETITION_INITIAL_SAVE_WINDOW_NAME) == 0)
	{
		StopAllCutScenes(); // Hack: I know we're coming from a fade, and we don't want the current cut scene to play out.
		SetUICutscene(CSEVENT_HALFTIME_PAN);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_COMPETITION_HUB_WINDOW_NAME) == 0
		||strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_COMPETITION_STANDINGS_WINDOW_NAME) == 0
		|| strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_COMPETITION_SIMULATE_MATCH_WINDOW_NAME) == 0
		|| strcmp(destination_window_name, RugbyUIWindowNames::SIFUI_PRO_CAREER_POST_MATCH_WINDOW_NAME) == 0)
	{
		SetupCompetitionHubCutscene();
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_COMPETITION_TEAM_MANAGEMENT_WINDOW_NAME) == 0)
	{
		SetUICutscene(CSEVENT_UI_COMPETITION_TEAM_PROFILE);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_COMPETITION_TEAM_SELECT_WINDOW_NAME) == 0)
	{
		int csevent = CSEVENT_UI_TROPHY_WORLDCUP + competition_ui_trophy_id;
		MabMath::Clamp(csevent, (int)CSEVENT_UI_TROPHY_WORLDCUP, (int)CSEVENT_TRY_CELEBRATION - 1);
		SetUICutscene(csevent);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_RUGBY_STORE_MOVIE_WINDOW_NAME) == 0
		|| strcmp(destination_window_name, RugbyUIWindowNames::RUUI_TRAINING_RULES_WINDOW_NAME) == 0)
	{
		// Change cutscene to one that will give us a decent framerate while the video is playing
		//SetUICutscene(CSEVENT_UI_COMPETITION_SELECT); //commented in RC3
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_RUGBY_STORE_WINDOW_NAME) == 0)
	{
		SetUICutscene(CSEVENT_UI_MAIN_MENU);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_START_ASYNC_LOADING_WINDOW_NAME) == 0)
	{
		SetUICutscene(CSEVENT_HALFTIME_PAN, UI_CUTSCENE_CHANGE_START_ASYNC_LOADING);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_LAUNCH_PENDING_LEVEL_WINDOW_NAME) == 0)
	{
		SetUICutscene(CSEVENT_HALFTIME_PAN, UI_CUTSCENE_CHANGE_LAUNCH_PENDING_LEVEL);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_CUSTOMISE_TEAM_DETAILS_WINDOW_NAME) == 0
	|| strcmp(destination_window_name, RugbyUIWindowNames::RUUI_CUSTOMISE_SELECT_KIT_WINDOW_NAME) == 0)
	{
		SetUICutscene(CSEVENT_UI_CUSTOMIZE_TEAM);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_CUSTOMISE_DATA_RESET_WINDOW_NAME) == 0)
	{
		SetUICutscene(CSEVENT_UI_MAIN_MENU, UI_CUTSCENE_CHANGE_RESTORE_STRIPS);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_INGAME_TROPHY_CEREMONY_WINDOW_NAME) == 0)
	{
		//SIFGameWorld *game = SIFApplication::GetApplication()->GetActiveGameWorld();
		if(game) game->GetCutSceneManager()->StartGrandFinalCelebrations();
	}
#ifdef APPBUILD_RUGBY_CHALLENGE_3
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_LIST_PLAYERS_WINDOW_NAME) == 0)
	{
		SetupPlayerCustomisationCutscene(destination_window_name);
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_CAREER_SELECT_TEAM_WINDOW_NAME) == 0)
	{
		SetUICutscene(CSEVENT_UI_MAIN_MENU);
	}
	/*else if(strcmp(destination_window_name,"ListTeams")==0) //commented in RC3
	{
	SetUICutscene(CSEVENT_UI_LIST_TEAMS);
	}*/
#endif
}

///-------------------------------------------------------------------------------
/// Setup the background UI cutscene for the competition hub.
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetupCompetitionHubCutscene()
{
	RUCareerModeManager* career_manager = SIFApplication::GetApplication()->GetCareerModeManager();

	int cs_event = CSEVENT_HALFTIME_PAN; //#rc4_no_likeness_comp_hub CSEVENT_UI_COMPETITION_HUB;

#if TEAM_LIKENESS_IN_COMP_UI
	/// If end of competition show either 'WIN' or 'LOSS' version of HUB cutscene.
	if (career_manager->IsActive() && career_manager->IsCareerModeOver() && !career_manager->IsInFranchise())
	{
		int winning_team_id = SIFGameHelpers::GACompetitionGetWinningTeamId();
		if (career_manager->IsTeamPlayerControlled(winning_team_id))
			cs_event = CSEVENT_UI_COMPETITION_HUB_WIN;
		else
			cs_event = CSEVENT_UI_COMPETITION_HUB_LOSS;
	}
#endif

	SetUICutscene(cs_event);
}

///-------------------------------------------------------------------------------
/// Setup the pre-game cutscenes.
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetupPreGameCutScenes()
{
	if (cinematics_enabled)
	{
		AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);
		AddCutsceneCallbackElement(SSCutSceneManager::PlayerWalkonCallback, PLAYER_WALKON_CALLBACK);
		AddCutsceneCallbackElement(SSCutSceneManager::PushMusicCallback, PUSH_MUSIC_CALLBACK,
			(int)SIFApplication::GetApplication()->GetRUAudio()->GetEventID("event:/music/run_on_music"));

		{
			MabMatrix transform = MabMatrix::IDENTITY;
			FVector position = FVector::ZERO;
			float	angle = 0.0f;

			if (game->GetStadiumManager()->GetStadiumCutsceneLocator(position, angle, RUNON_LOCATOR_TYPE))
			{
				MABLOGDEBUG("RUNON_LOCATOR: (%f,%f) : angle=%f", position.x, position.z, angle);
				MabMatrix nr = MabMatrix::RotMatrixY(MabMath::Deg2Rad(angle + 90.0f));
				MabMatrix nm = MabMatrix::TransMatrix(position);
				transform = nr * nm;
			}

			AddCutsceneCinematicElement(RUGamePhase::PRE_GAME, ENABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);
			AddCinematicTLE(CSEVENT_PREGAME_STADIUM_PAN, MabMatrix::IDENTITY, SIDE_NONE);
			AddCinematicTLE(CSEVENT_FIREWORKS_PREGAME, MabMatrix::IDENTITY, SIDE_NONE);


#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST) //tunnel cutscene test code

			if (CVarCutsceneName.GetValueOnGameThread() > 1)
			{
				// Nick  WWS 7s to Womens //
				//if (game->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_RU13)
				//{
					AddCutsceneCinematicElement(RUGamePhase::PRE_GAME, ENABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);
					AddCinematicTLE(CSEVENT_PREGAME_TEAM1_WALKON, MabMatrix::IDENTITY, SIDE_A);
				//}
			}
#endif
			AddCutsceneCinematicElement(RUGamePhase::PRE_GAME, ENABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);

			if (game->GetTeam(SIDE_A)->GetNumHumanPlayers() > 0)
			{
				AddCinematicTLE(CSEVENT_PREGAME_TEAM1_ONFIELD_WALKON, transform, SIDE_A);
				AddCinematicTLE(CSEVENT_PREGAME_TEAM2_ONFIELD_WALKON, transform, SIDE_B);
			}
			else
			{
				AddCinematicTLE(CSEVENT_PREGAME_TEAM2_ONFIELD_WALKON, transform, SIDE_B);
				AddCinematicTLE(CSEVENT_PREGAME_TEAM1_ONFIELD_WALKON, transform, SIDE_A);
			}

			AddCinematicTLE(CSEVENT_FIREWORKS_PREGAME, MabMatrix::IDENTITY, SIDE_NONE);

			AddCutsceneCallbackElement(SSCutSceneManager::PopMusicCallback, POP_MUSIC_CALLBACK);
		}

		AddUserSkipPointElement();
		AddCutsceneCallbackElement(SSCutSceneManager::TeamManagementCallback, TEAM_MANAGEMENT_CALLBACK);

		const RUDB_TEAM *team0 = &game->GetTeam(SIDE_A)->GetDbTeam();
		const RUDB_TEAM *team1 = &game->GetTeam(SIDE_B)->GetDbTeam();

		MabInt32 music_id = -1;
		MabInt32 music_id2 = -1;
		MabString anthem(72, "event:/music/anthems/%s", team0->GetName());
		MabString anthem2(72, "event:/music/anthems/%s", team1->GetName());


		//if the anthem path contains 7s...
		if (anthem.find(" R7", 0) != MabString::npos || anthem.find(" W7s", 0) != MabString::npos)
		{
			RL3DB_COUNTRY team0_country(team0->GetAssociatedCountryId());
			anthem.sprintf("event:/music/anthems/%s", team0_country.GetName());
		}
		if (anthem2.find(" R7", 0) != MabString::npos || anthem2.find(" W7s", 0) != MabString::npos)
		{
			RL3DB_COUNTRY team1_country(team1->GetAssociatedCountryId());
			anthem2.sprintf("event:/music/anthems/%s", team1_country.GetName());
		}

		if (SIFApplication::GetApplication()->GetRUAudio()->HasEvent(anthem.c_str()))
		{
			music_id = SIFApplication::GetApplication()->GetRUAudio()->GetEventID(anthem.c_str());
		}
		else
		{
			music_id = SIFApplication::GetApplication()->GetRUAudio()->GetEventID("event:/music/line_up_music");
		}

		if (SIFApplication::GetApplication()->GetRUAudio()->HasEvent(anthem2.c_str()))
		{
			music_id2 = SIFApplication::GetApplication()->GetRUAudio()->GetEventID(anthem2.c_str());
		}
		else
		{
			music_id2 = SIFApplication::GetApplication()->GetRUAudio()->GetEventID("event:/music/line_up_music");
		}

		//push anthrem2 song first, so when we push the anthem1 song after, anthem1 will be played first.
		AddCutsceneCallbackElement(SSCutSceneManager::PushMusicCallback, PUSH_MUSIC_CALLBACK, (int)music_id2);

		AddCutsceneCallbackElement(SSCutSceneManager::PushMusicCallback ,PUSH_MUSIC_CALLBACK, (int)music_id);
		AddCutsceneCinematicElement(RUGamePhase::PRE_GAME, DISABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS );

		MabString name(team0->GetName());
		MabString other_name(team1->GetName());

		if (name.find(" R7", 0) != std::string::npos)
		{
			RL3DB_COUNTRY team_country(team0->GetAssociatedCountryId());
			name = team_country.GetName();
		}

		if (other_name.find(" R7", 0) != std::string::npos)
		{
			RL3DB_COUNTRY other_team_country(team1->GetAssociatedCountryId());
			other_name = other_team_country.GetName();
		}

		bool has_anthem = SIFApplication::GetApplication()->GetRUAudio()->HasAnthemEvent(name.c_str(), other_name.c_str());
		if(has_anthem)
		{
			AddCinematicTLE(CSEVENT_PREGAME_TEAM_NATIONAL_ANTHEM, MabMatrix::IDENTITY, SIDE_NONE);
		}
		else
		{
			AddCinematicTLE(game->GetStadiumManager()->GetUseSmallPans() ? CSEVENT_PREGAME_TEAM_SELECT_SMALL : CSEVENT_PREGAME_TEAM_SELECT, MabMatrix::IDENTITY, SIDE_NONE);
		}

		// Should clear, because if there is an anthem it will need stopping
		AddCutsceneCallbackElement(SSCutSceneManager::ClearMusicCallback, POP_MUSIC_CALLBACK);
		AddCutsceneCallbackElement(SSCutSceneManager::SkipCommentaryCallback, SKIP_COMMENTARY_CALLBACK);
		will_do_haka = false;

		// Should only play haka if NZ is playing and NOT a network game, cause Luke said.		
		if(!game->GetGameSettings().game_settings.network_game && ( CanTeamDoHaka(game->GetTeam(SIDE_A)) || CanTeamDoHaka(game->GetTeam(SIDE_B)) ))
		{
			will_do_haka = true; // GGS RUGBY ERIC REMOVE HAKA CUTSCENE - Nick Put the Haka back in...

			AddCutsceneCallbackElement(SSCutSceneManager::PreHakaCallback, PRE_HAKA_CALLBACK);

			AddCutsceneCinematicElement(RUGamePhase::PRE_GAME, ENABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);
			AddCinematicTLE(CSEVENT_PREGAME_HAKA, MabMatrix::IDENTITY, SIDE_NONE);
		}

		//AddUserSkipPointElement(); //commented in RC3
		AddCutsceneCallbackElement(SSCutSceneManager::PostHakaCallback, POST_HAKA_CALLBACK);
		SetupPreKickOffCutscene();
	}
	else
	{
		AddCutsceneCallbackElement(SSCutSceneManager::TeamManagementCallback, TEAM_MANAGEMENT_CALLBACK);

		AddCutsceneCinematicElement(RUGamePhase::PRE_GAME, DISABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);
		AddCinematicTLE(game->GetStadiumManager()->GetUseSmallPans() ? CSEVENT_PREGAME_TEAM_SELECT_SMALL : CSEVENT_PREGAME_TEAM_SELECT, MabMatrix::IDENTITY, SIDE_NONE);

		AddCutsceneCallbackElement(SSCutSceneManager::StartKickOffPhaseCallback, START_KICKOFF_PHASE_CALLBACK);
	}
}

///-------------------------------------------------------------------------------
/// Helper function: Can a particular team do the haka?
///-------------------------------------------------------------------------------

bool SSCutSceneManager::CanTeamDoHaka(RUTeam *team)
{
	if (team->GetDbTeam().GetDbId() != DB_TEAMID_NZ)
		return false;


	/// If strip not NZ then can't.

	const RUDB_TEAM_STRIP& db_strip = team->GetDbTeamStrip();
	unsigned short strip_id = db_strip.GetDbId();
	if (strip_id != DB_STRIPID_NZ_HOME && strip_id != DB_STRIPID_NZ_AWAY)
		return false;

	/// Check for an approved haka leader to be present.

	/*SIFRugbyCharacterList players = team->GetPlayers();
	for (SIFRugbyCharacterList::iterator iter = players.begin(); iter != players.end(); iter++)
	{
		ARugbyCharacter* player = *iter;

		/// Only these players can do haka lead. If none of these guys is present then no haka is played.
		switch (player->GetAttributes()->GetDbId())
		{
		case DB_PLAYERID_TJ_PERENARA:
		case DB_PLAYERID_AARON_SMITH:
		case DB_PLAYERID_KIERAN_READ:
		case DB_PLAYERID_NEHE_MILNER_SKUDDER:
			return true;
		default:
			break;
		}
	}

	return false;*/

	//SRA: If none of the 4 players listed above was not present, the haka won't play. 
	//Code in SSCutSceneManager ensures that if these players aren't present, 
	//the captain will lead the haka instead, which is acceptable.
	return true;
}

///-------------------------------------------------------------------------------
/// Setup player customisation cutscene + transforms...
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetupPlayerCustomisationCutscene(const char* destination_window_name)
{
	// Temporarily switch on our input string and play a specific cutscene.

	//if (SIFApplication::GetApplication())
	//{
	//	int CutsceneEvent = CSEVENT_UI_CUSTOMIZE_PLAYER_BODY;
	//	if (strncmp(destination_window_name, RugbyUIWindowNames::RUUI_CUSTOMISE_PLAYER_HEAD_WINDOW_NAME, strlen(RugbyUIWindowNames::RUUI_CUSTOMISE_PLAYER_HEAD_WINDOW_NAME)) == 0)
	//	{
	//		CutsceneEvent = CSEVENT_UI_CUSTOMIZE_PLAYER_HEAD;
	//	}

	//	if (SIFApplication::GetApplication()->GetMenuGameWorld()->GetCutSceneManager()->GetCutsceneState() != CutsceneEvent)
	//	{
	//		SIFApplication::GetApplication()->GetMenuGameWorld()->GetCutSceneManager()->SetUICutscene(CutsceneEvent, SSCutSceneManager::UI_CUTSCENE_LOAD_CUSTOMISATION_PLAYER);
	//		SIFApplication::GetApplication()->GetMenuGameWorld()->GetCutSceneManager()->SkipCutScene();
	//	}
	//}

	bool do_blend = true;

	//static const float custom_camera_offsets_16_9[] = {
	//	/// Davids settings.
	//	-0.2f,	0.0f,	0.15f,		0.0f,			// BODY
	//	-0.08f,	0.725f,	-4.2f,		0.0f,			// HEAD
	//	-0.35f,	0.55f,	-3.2f,		30.0f,			// DETAILS
	//	-0.75f,	0.0f,	0.0f,		15.0f,			// ATTRIBUTES
	//	-0.2f,	0.725f,	-4.2f,		0.0f,			// FACEHAIR
	//	-0.5f,	0.0f,	0.0f,		0.0f,			// ACCESSORIES
	//	-0.35f,	0.55f,	-2.8f,		0.0f,			// VIEW PLAYERS
	//	-0.35f,	-0.30f,	-2.7f,		80.0f,			// TATTOO LEFT LEG
	//	-0.35f,	-0.30f,	-2.7f,		-80.0f,			// TATTOO RIGHT LEG
	//	-0.35f,	0.15f,	-2.7f,		80.0f,			// TATTOO LEFT ARM
	//	-0.35f,	0.15f,	-2.7f,		-80.0f			// TATTOO RIGH ARM
	//};

	const float HeadX = 115.0f;
	const float BodyX = 600.0f;

	static const float custom_camera_offsets_16_9[] = {
		/// Davids settings.
		///X		Y			Z			Angle
		BodyX,		90.0f,		100.0f,		0.0f,			// BODY
		HeadX,		12.0f,		165.0f,		0.0f,			// HEAD
		300.0f,		40.0f,		140.0f,		-20.0f,			// DETAILS
		600.0f,		90.0f,		105.0f,		-15.0f,			// ATTRIBUTES
		HeadX,		12.0f,		165.0f,		0.0f,			// FACEHAIR
		BodyX,		90.0f,		100.0f,		0.0f,			// ACCESSORIES
		250.0f,		40.0f,		145.0f,		0.0f,			// VIEW PLAYERS
		180.0f,		25.0f,		102.5f,		-90.0f,			// TATTOO LEFT ARM
		180.0f,		25.0f,		102.5f,		60.0f,			// TATTOO RIGHT ARM
		165.0f,		25.0f,		55.0f,		-90.0f,			// TATTOO LEFT LEG
		165.0f,		25.0f,		55.0f,		60.0f,			// TATTOO RIGHT LEG
		HeadX,		20.0f,		20.0f,		0.0f,			// SHOES
		400.0f,		40.0f,		85.0f,		-20.0f,			// KIT
	};

	//// Select table based on aspect ratio. (At the moment works for both)
	float *offsets = (float*)custom_camera_offsets_16_9;

	//MabApplicationParameters* params = MabApplicationParameters::GetInstance();
	//float aspect = params->raster_height / (float)params->raster_width;
	//if(aspect>=0.75f)
	//	(float*)custom_camera_offsets_4_3;
	//MABLOGDEBUG("%f",aspect);

	if (FString(destination_window_name) == "CustomisePlayer" || FString(destination_window_name)  == "CustomiseKit")
	{
		return;
	}

	bool with_height_offset = true;

	if (strncmp(destination_window_name, RugbyUIWindowNames::RUUI_CUSTOMISE_PLAYER_HEAD_WINDOW_NAME, strlen(RugbyUIWindowNames::RUUI_CUSTOMISE_PLAYER_HEAD_WINDOW_NAME)) == 0)
	{
		offsets += 4;
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_CUSTOMISE_PLAYER_DETAILS_WINDOW_NAME) == 0)
	{
		offsets += 8;
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_CUSTOMISE_PLAYER_ATTRIBUTES_WINDOW_NAME) == 0)
	{
		offsets += 12;
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_CUSTOMISE_PLAYER_FACEHAIR_WINDOW_NAME) == 0)
	{
		offsets += 16;
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_CUSTOMISE_PLAYER_ACCESSORIES_WINDOW_NAME) == 0)
	{
		offsets += 20;
	}
#ifdef APPBUILD_RUGBY_CHALLENGE_3
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_LIST_PLAYERS_WINDOW_NAME) == 0)
	{
		offsets += 24;
		do_blend = false;
	}
	else if (strstr(destination_window_name, RugbyUIWindowNames::RUUI_TATTOO_WINDOW_NAME))
	{
		const char* cam_id;
		cam_id = strstr(destination_window_name, "_");

		if (cam_id)
		{
			++cam_id;

			int case_cam_id = atoi(cam_id);

			switch (case_cam_id)
			{
			case 0:
				offsets += 28;
				break;
			case 1:
				offsets += 32;
				break;
			case 2:
				offsets += 36;
				break;
			case 3:
				offsets += 40;
				break;
			default:
				break;
			}
		}
	}
	else if (strstr(destination_window_name, RugbyUIWindowNames::RUUI_CUSTOMISE_PLAYER_SHOE_WINDOW_NAME)
		|| strstr(destination_window_name, RugbyUIWindowNames::RUUI_CUSTOMISE_KIT_BOOTS_WINDOW_NAME))
	{
		offsets += 44;
		with_height_offset = false;
	}
	else if (strcmp(destination_window_name, RugbyUIWindowNames::RUUI_CUSTOMISE_KIT_KIT_WINDOW_NAME) == 0)
	{
		offsets += 48;
	}
#endif

	// Debug: Enable artists to create the offsets to put in the table...
#ifdef ENABLE_GAME_DEBUG_MENU
	//#rc3_legacy_debug_cutscene 
	/*if (SIFDebug::GetCutsceneDebugSettings()->GetCutSceneCustomisationModeEnabled())
	{
		SetPlayerCreatorAngle(SIFDebug::GetCutsceneDebugSettings()->GetCutSceneCustomisationAngle());
		SetPlayerCreatorCameraOffset(SIFDebug::GetCutsceneDebugSettings()->GetCutSceneCustomisationOffset(), do_blend);
	}
	else*/
	{
#endif
		SetPlayerCreatorAngle(offsets[3], do_blend);
		SetPlayerCreatorCameraOffset(FVector(offsets[0], offsets[1], offsets[2]), with_height_offset, do_blend);
#ifdef ENABLE_GAME_DEBUG_MENU
	}
#endif

	/// Give the human a team, so input is enabled (Left/Right triggers).
	SSHumanPlayer* human = game->GetFirstHumanPlayer();
	if (human)
	{
		human->SetTeam(game->GetTeam(SIDE_A));
	}
}

///-------------------------------------------------------------------------------
/// Setup the pre-kickoff cutscene...
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetupPreKickOffCutscene()
{
	next_half = FIRST_HALF;
	next_extratime_mode = NOT_EXTRA_TIME;
	AddPreKickOffCutScene(next_half);
}

///-------------------------------------------------------------------------------
/// Half-time - walkoff
///-------------------------------------------------------------------------------

void SSCutSceneManager::StartHalfTimeWalkOff()
{
	/// If screenwipe already running then hold/pause on fully faded.

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);
	AddCutsceneCallbackElement(SSCutSceneManager::HoldScreenWipeCallback, HOLD_SCREENWIPE_CALLBACK, 100);

	AddCutsceneAsyncPreloadElement(false);
	AddCutsceneCallbackElement(SSCutSceneManager::StartPostGamePhaseCallback, START_POST_GAME_PHASE_CALLBACK);
	AddScreenWipeElement(SWIPE_CUT);		// Will continue any paused screen wipes.

	SetCelebratingTeamBasedOnScore();

	if (cinematics_enabled)
	{
		MabMatrix transform = MabMatrix::IDENTITY;
		FVector position = FVector::ZERO;
		float	angle = 0.0f;

		if (game->GetStadiumManager()->GetStadiumCutsceneLocator(position, angle, RUNON_LOCATOR_TYPE))
		{
			MABLOGDEBUG("RUNON_LOCATOR: (%f,%f) : angle=%f", position.x, position.z, angle);
			MabMatrix nr = MabMatrix::RotMatrixY(MabMath::Deg2Rad(angle + 90.0f));
			MabMatrix nm = MabMatrix::TransMatrix(position);
			transform = nr * nm;
		}

		AddCutsceneCinematicElement(RUGamePhase::HALF_TIME, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
		AddCinematicTLE(CSEVENT_HALFTIME_WALKOFF, transform, GetCelebratingTeamSide());

		AddUserSkipPointElement();
	}
	AddCutsceneCallbackElement(SSCutSceneManager::HalfTimeWindowCallback, HALFTIME_WINDOW_CALLBACK);

	AddCutsceneCinematicElement(RUGamePhase::HALF_TIME, DISABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
	AddCinematicTLE(game->GetStadiumManager()->GetUseSmallPans() ? CSEVENT_HALFTIME_PAN_SMALL : CSEVENT_HALFTIME_PAN, MabMatrix::IDENTITY, SIDE_NONE);

	//if(cinematics_enabled) //dont enable. Commented from RC3
	//{
	//	AddCutsceneCinematicElement( CSEVENT_HALFTIME_WALKON, MabMatrix::IDENTITY, GetCelebratingTeamSide(), DISABLE_SIMULATION, ENABLE_SKIP, SWIPE_FADE );
	//	AddUserSkipPointElement();
	//}

	next_half = SECOND_HALF;
	next_extratime_mode = NOT_EXTRA_TIME;

	AddPreKickOffCutScene(next_half);
}

///-------------------------------------------------------------------------------
/// Setup coin toss cutscene.
///-------------------------------------------------------------------------------

void SSCutSceneManager::StartCoinTossCutScene()
{
	/// If screenwipe already running then hold/pause on fully faded.
	AddCutsceneCallbackElement( SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK );
	AddCutsceneCallbackElement( SSCutSceneManager::HoldScreenWipeCallback, HOLD_SCREENWIPE_CALLBACK, 100 );

	AddCutsceneCallbackElement( SSCutSceneManager::StartExtraTimeCoinTossPhaseCallback, START_EXTRA_TIME_COIN_TOSS_GAME_PHASE_CALLBACK);
	//AddCutsceneCallbackElement( SSCutSceneManager::StartPostExtraTimeCoinTossPhaseCallback, START_POST_EXTRA_TIME_COIN_TOSS_GAME_PHASE_CALLBACK ); //commented in RC3
	//AddCutsceneCallbackElement( SSCutSceneManager::CoinTossDecisionWindowCallback, COINTOSS_DECISION_WINDOW_CALLBACK );

	AddCutsceneCinematicElement(RUGamePhase::EXTRA_TIME_TOSS, DISABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS );
	AddCinematicTLE(game->GetStadiumManager()->GetUseSmallPans() ? CSEVENT_HALFTIME_PAN_SMALL : CSEVENT_HALFTIME_PAN, MabMatrix::IDENTITY, SIDE_NONE);
}

///-------------------------------------------------------------------------------
/// Setup simulation cutscene.
///-------------------------------------------------------------------------------

void SSCutSceneManager::StartSimulationCutScene()
{
	/// If screenwipe already running then hold/pause on fully faded.
	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);
	AddCutsceneCallbackElement(SSCutSceneManager::HoldScreenWipeCallback, HOLD_SCREENWIPE_CALLBACK, 100);

	AddCutsceneAsyncPreloadElement(false);
	AddCutsceneCallbackElement( SSCutSceneManager::StartSimulationPhaseCallback, GAME_SIMULATION_WINDOW_CALLBACK );
	AddScreenWipeElement(SWIPE_CUT);		// Will continue any paused screen wipes.

	AddCutsceneCinematicElement(RUGamePhase::SIMULATION, DISABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
	AddCinematicTLE(game->GetStadiumManager()->GetUseSmallPans() ? CSEVENT_HALFTIME_PAN_SMALL : CSEVENT_HALFTIME_PAN, MabMatrix::IDENTITY, SIDE_NONE);
}

///-------------------------------------------------------------------------------
/// Setup pro sent off cutscene.
///-------------------------------------------------------------------------------

void SSCutSceneManager::StartProSentOffCutScene()
{
	/// If screenwipe already running then hold/pause on fully faded.
	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);
	AddCutsceneCallbackElement(SSCutSceneManager::HoldScreenWipeCallback, HOLD_SCREENWIPE_CALLBACK, 100);

	AddCutsceneAsyncPreloadElement(false);
	AddCutsceneCallbackElement( SSCutSceneManager::ProSentOffWindowCallback, PRO_SENT_OFF_CALLBACK );
	AddScreenWipeElement(SWIPE_CUT);		// Will continue any paused screen wipes.

	AddCutsceneCinematicElement(RUGamePhase::HALF_TIME, DISABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
	AddCinematicTLE(game->GetStadiumManager()->GetUseSmallPans() ? CSEVENT_HALFTIME_PAN_SMALL : CSEVENT_HALFTIME_PAN, MabMatrix::IDENTITY, SIDE_NONE);

	AddUserSkipPointElement();
	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);
}

///-------------------------------------------------------------------------------
/// ResumeCutsceneFromProSendOff - When our pro gets sent off, we are first prompted with an option to watch the game or sim the game, if we want to watch, we should continue any interupted cutscenes
/// i.e. if we were sent off after a penalty, we would probably go on to do the penalty conscequences etc.
///-------------------------------------------------------------------------------

void SSCutSceneManager::ResumeCutsceneFromProSendOff()
{
	MABLOGDEBUG("We're attempting to resume the cutscene callbacks from where we left off before the pro send off cutscene");
	AddCutsceneCallbackElement(suspended_callback_from_pro_send_off, suspended_callback_from_pro_send_off_debug_name);
}

///-------------------------------------------------------------------------------
/// AddPreKickOffSevensExtraTimeCutScene: Helper function to setup a pre-kick off cutscene, but specifically for rugby sevens games.
///-------------------------------------------------------------------------------

void SSCutSceneManager::AddPreKickOffSevensExtraTimeCutScene(SSGT_HALF)
{
	/// Setup the restart team based on half + direction.

	//RUGameSettings *settings = game->GetGameSettings();
	RUGameState *state = game->GetGameState();
	RUGamePhaseExtraTimeToss* phase = game->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();

	// So instead of getting the direction, and restarting team from the settings, we need to either pass it in here, or retrieve it from the coin toss phase

	//SSTEAMSIDE restart_team_side = settings->game_settings.initial_kickoff_team;
	SSTEAMSIDE restart_team_side = phase->GetKickOffTeam();

	//int restart_team_direction = (int)settings->game_settings.initial_play_direction[restart_team_side];
	int restart_team_direction = phase->GetKickDir();

	{
		/// Set the teams play direction now, so that pre-kick off cutscene formations are on correct half.
		//int direction = (half==SECOND_HALF)?-1:1;
		const std::vector<std::unique_ptr<RUTeam>> &teams = game->GetTeams();
		for (size_t i = 0; i < teams.size(); ++i)
		{
			ERugbyPlayDirection newDirection;
			if (teams[i]->GetSide() == restart_team_side)
				newDirection = static_cast<ERugbyPlayDirection>(phase->GetKickDir());
			else
				newDirection = static_cast<ERugbyPlayDirection>(phase->GetReceiveDir());

			teams[i]->SetPlayDirection(newDirection);
			teams[i]->Reset();
		}
	}

	state->SetPlayRestartPosition(FVector::ZERO);

	RUTeam *restart_team = game->GetTeam(restart_team_side);
	state->SetAttackingTeam(restart_team);
	state->SetPlayRestartTeam(restart_team);

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);
	AddCutsceneCallbackElement(SSCutSceneManager::SetPreKickOffPhaseCallback, SET_PRE_KICKOFF_PHASE_CALLBACK);

	if (cinematics_enabled)
	{
		float angle = (restart_team_direction>0) ? 0.0f : 180.0f;
		MabMatrix transform = MabMatrix::RotMatrixY(MabMath::Deg2Rad(angle));

		AddCutsceneCallbackElement(SSCutSceneManager::SetKickOffKickerCallback, SETKICK_OFF_KICKER_CALLBACK);
		AddCutsceneCinematicElement(RUGamePhase::KICK_OFF, ENABLE_SKIP, SWIPE_CUT, bg_cutscenes_enabled ? SHOW_NON_ACTORS_APPROVED : SHOW_NON_ACTORS);			// kickoff - ok to show
		AddCinematicTLE(CSEVENT_PRE_KICKOFF, transform, restart_team_side);

		AddUserSkipPointElement();
	}

	AddCutsceneCallbackElement(SSCutSceneManager::RestartKickCallback, RESTART_KICK_CALLBACK);		// Does integral call to CutSceneEndCallback.
}

///-------------------------------------------------------------------------------
/// AddPreKickOffCutScene: Helper function to setup a pre-kick off cutscene.
///-------------------------------------------------------------------------------

void SSCutSceneManager::AddPreKickOffCutScene(SSGT_HALF half)
{
	/// Setup the restart team based on half + direction.

	RUGameSettings *settings = &game->GetGameSettings();
	RUGameState *state = game->GetGameState();

	SSTEAMSIDE restart_team_side = settings->game_settings.initial_kickoff_team;
	if (half == SECOND_HALF)
	{
		restart_team_side = (restart_team_side == SIDE_A) ? SIDE_B : SIDE_A;
	}
	int restart_team_direction = (int)settings->game_settings.initial_play_direction[restart_team_side];
	restart_team_direction *= (half == SECOND_HALF) ? -1 : 1;

	{
		/// Set the teams play direction now, so that pre-kick off cutscene formations are on correct half.
		int direction = (half == SECOND_HALF) ? -1 : 1;
		const std::vector<std::unique_ptr<RUTeam>> &teams = game->GetTeams();
		for (size_t i = 0; i < teams.size(); ++i)
		{
			teams[i]->SetPlayDirection(ERugbyPlayDirection(int(settings->game_settings.initial_play_direction[i]) * direction));
			teams[i]->Reset();
		}
	}

	state->SetPlayRestartPosition(FVector::ZERO);

	RUTeam *restart_team = game->GetTeam(restart_team_side);
	state->SetAttackingTeam(restart_team);
	state->SetPlayRestartTeam(restart_team);

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);
	AddCutsceneCallbackElement(SSCutSceneManager::SetPreKickOffPhaseCallback, SET_PRE_KICKOFF_PHASE_CALLBACK);

	if (cinematics_enabled)
	{
		float angle = (restart_team_direction>0) ? 0.0f : 180.0f;
		MabMatrix transform = MabMatrix::RotMatrixY(MabMath::Deg2Rad(angle));

		AddCutsceneCallbackElement(SSCutSceneManager::SetKickOffKickerCallback, SETKICK_OFF_KICKER_CALLBACK);
		AddCutsceneCinematicElement(RUGamePhase::KICK_OFF, ENABLE_SKIP, SWIPE_CUT, bg_cutscenes_enabled ? SHOW_NON_ACTORS_APPROVED : SHOW_NON_ACTORS);			// kickoff - ok to show
		AddCinematicTLE(CSEVENT_PRE_KICKOFF, transform, restart_team_side);

		AddUserSkipPointElement();
	}

	AddCutsceneCallbackElement(SSCutSceneManager::RestartKickCallback, RESTART_KICK_CALLBACK);		// Does integral call to CutSceneEndCallback.
}

///-------------------------------------------------------------------------------
/// Callback: Set the restart kicker (focus player) - has to be done as callback as substitution
/// can occur after elements are setup. (Ticket #47494)
///-------------------------------------------------------------------------------

bool SSCutSceneManager::SetKickOffKickerCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState  *game_state = game->GetGameState();
	RUTeam		*restart_team = game_state->GetPlayRestartTeam();

	manager->SetFocusPlayer(restart_team->GetPlayKicker(), restart_team->GetSide());

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: Just sets the game phase to GAME_PHASE_PRE_KICK_OFF
///-------------------------------------------------------------------------------

bool SSCutSceneManager::SetPreKickOffPhaseCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState  *game_state = game->GetGameState();

	game_state->SetBallHolder(game_state->GetPlayRestartTeam()->GetPlayKicker());
	game_state->SetPhase(RUGamePhase::PRE_KICK_OFF);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: Just sets the game phase to GAME_PHASE_FREE_KICK
///-------------------------------------------------------------------------------

bool SSCutSceneManager::SetFreeKickPhaseCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState  *game_state = game->GetGameState();

	game_state->SetBallHolder(game_state->GetAttackingTeam()->GetPlayKicker());

	game_state->SetKickRestartKickType(KICKTYPE_FREEKICK);

	game_state->SetPhase(RUGamePhase::FREE_KICK);

	return true;
}

///-------------------------------------------------------------------------------
/// Set the celebrating team, based on score.
/// If draw then:
///		if ai 'v' ai or humans on both team it is random, else the
///		human team is the celebrator.
/// - returns false if draw.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::SetCelebratingTeamBasedOnScore()
{
	RUStatisticsSystem *stats = SIFApplication::GetApplication()->GetStatisticsSystem();

	RUTeam *team0 = game->GetTeam(SIDE_A);
	RUTeam *team1 = game->GetTeam(SIDE_B);

	int score0 = stats->GetCurrentMatchStat(team0, &RUDB_STATS_TEAM::score);
	int score1 = stats->GetCurrentMatchStat(team1, &RUDB_STATS_TEAM::score);

	if (score0 == score1)
	{
		int nh0 = team0->GetNumHumanPlayers();
		int nh1 = team1->GetNumHumanPlayers();

		if ((nh0 == 0 && nh1 == 0) || (nh0 != 0 && nh1 != 0))
		{
			SetCelebratingTeam(game->GetRNG()->RAND_CALL(float)>0.5f ? team0 : team1);
		}
		else
		{
			SetCelebratingTeam(team0->GetNumHumanPlayers()>0 ? team0 : team1);		// Bias human side.
		}

		return false;
	}
	else
	{
		SetCelebratingTeam(score0>score1 ? team0 : team1);
	}

	return true;
}

///-------------------------------------------------------------------------------
/// Start walk on after half time.
///-------------------------------------------------------------------------------

void SSCutSceneManager::StartHalfTimeWalkOn()
{
	AddCutsceneCallbackElement(SSCutSceneManager::SkipCommentaryCallback, SKIP_COMMENTARY_CALLBACK);
	RequestElementFinish();
	if (SIFApplication::GetApplication())
	{
		UWWUIScreenManager* pScreenManager = SIFApplication::GetApplication()->GetUIScreenManager();
		if (!pScreenManager)
		{
			ensure(pScreenManager);
			return;
		}

		if (pScreenManager->FindScreenTemplate(Screens_UI::InGameHud) == -1)
		{
			SIFApplication::GetApplication()->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::InGameHud);   //#MB - now pops back to this screen rather than reloading it.
		}
		else
		{
			return;
		}
	}
}

///-------------------------------------------------------------------------------
/// Start full time celebration/commiseration etc...
///-------------------------------------------------------------------------------

void SSCutSceneManager::StartFullTimeCutScene()
{
	/*if(showSimulationBeforeFullTime)
	{
	//Hijack!
	game->GetEvents()->cutscene_simulation();
	showSimulationBeforeFullTime = false;

	return;
	}*/

	bool extra_time_enabled = game->GetGameSettings().game_settings.extra_time_enabled;

#ifdef ENABLE_GAME_DEBUG_MENU
	if (SIFDebug::GetRulesDebugSettings()->IsForceExtraTime())
		extra_time_enabled = true;
#endif

	if (extra_time_enabled)
	{
		RUStatisticsSystem *stats = SIFApplication::GetApplication()->GetStatisticsSystem();
		RUTeam *team0 = game->GetTeam(SIDE_A);
		RUTeam *team1 = game->GetTeam(SIDE_B);
		int score0 = stats->GetCurrentMatchStat(team0, &RUDB_STATS_TEAM::score);
		int score1 = stats->GetCurrentMatchStat(team1, &RUDB_STATS_TEAM::score);

#ifdef ENABLE_SEVENS_MODE
		bool scoredInExtraDuringSevens = game->GetGameSettings().game_settings.GetGameMode() == false; // Nick  WWS 7s to Womens // GAME_MODE_SEVENS && (score0 != score1);

		if (game->GetGameTimer()->GetExtraTimeMode() == FIRST_EXTRA_TIME && !scoredInExtraDuringSevens)
#else
		if (game->GetGameTimer()->GetExtraTimeMode() == FIRST_EXTRA_TIME)
#endif
		{
			next_half = SECOND_HALF;
			next_extratime_mode = SECOND_EXTRA_TIME;		// Will turn into golden points in time added on.
			SetupExtraTimeHalfTimeCutscene();
		}
		else
		{
			if (score0 == score1)
			{
				MABASSERT(game->GetGameTimer()->GetExtraTimeMode() != SECOND_EXTRA_TIME);		// SHOULD NEVER HAPPEN!!!

																								//if extra time mode is still NOT_EXTRA_TIME then it means we just finished 2nd half and going into extra time
				if (game->GetGameTimer()->GetExtraTimeMode() == NOT_EXTRA_TIME)
				{
					next_half = FIRST_HALF;
					next_extratime_mode = FIRST_EXTRA_TIME;

					SetupExtraTimeStartCutscene();
				}
			   // DH - fix going into golden point AFTER the second extra time expires.
				else if(game->GetGameTimer()->GetExtraTimeMode() == SECOND_EXTRA_TIME)
				{
					game->GetGameTimer()->SetExtraTimeMode(GOLDEN_POINT);
				}
			}
			else
			{
				SetupFullTimeCutscene();
			}
		}
	}
	else
	{
		SetupFullTimeCutscene();
	}
}

void SSCutSceneManager::SetupGoldenPoint()
{
	bool extra_time_enabled = game->GetGameSettings().game_settings.extra_time_enabled;

#ifdef ENABLE_GAME_DEBUG_MENU
	if (SIFDebug::GetRulesDebugSettings()->IsForceExtraTime())
		extra_time_enabled = true;
#endif

	if (extra_time_enabled)
	{
		RUStatisticsSystem *stats = SIFApplication::GetApplication()->GetStatisticsSystem();
		RUTeam *team0 = game->GetTeam(SIDE_A);
		RUTeam *team1 = game->GetTeam(SIDE_B);
		int score0 = stats->GetCurrentMatchStat(team0, &RUDB_STATS_TEAM::score);
		int score1 = stats->GetCurrentMatchStat(team1, &RUDB_STATS_TEAM::score);

#ifdef ENABLE_SEVENS_MODE
		bool scoredInExtraDuringSevens = game->GetGameSettings().game_settings.GetGameMode() == false; // Nick  WWS 7s to Womens // GAME_MODE_SEVENS && (score0 != score1);

		if (game->GetGameTimer()->GetExtraTimeMode() == SECOND_EXTRA_TIME && !scoredInExtraDuringSevens)
#else
		if (game->GetGameTimer()->GetExtraTimeMode() == SECOND_EXTRA_TIME)
#endif
		{
			if (score0 == score1)
			{
				game->GetGameTimer()->SetExtraTimeMode(GOLDEN_POINT);
			}
		}
		//	Don't know if this is needed.
// 		else
// 		{
// 			SetupFullTimeCutscene();
// 		}
	}
	//	Don't know if this is needed.
// 	else
// 	{
// 		SetupFullTimeCutscene();
// 	}
}

///-------------------------------------------------------------------------------
/// Setup extra time half-time cutscene - doesn't go back to UI.
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetupExtraTimeHalfTimeCutscene()
{	/// TODO...
	SetupExtraTimeStartCutscene();
}

///-------------------------------------------------------------------------------
/// Setup extra time start cutscene - doesn't go back to UI.
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetupExtraTimeStartCutscene()
{
	AddCutsceneCallbackElement(SSCutSceneManager::HoldScreenWipeCallback, HOLD_SCREENWIPE_CALLBACK, 100);

	AddCutsceneAsyncPreloadElement(false);
	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

#ifdef ENABLE_SEVENS_MODE
	// In a sevens game, we should show the coin toss cutscene
	// We only want the extra time coin toss for the first half, the second half and golden point should function as normal
	// Nick  WWS 7s to Womens //if(game->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_SEVENS && next_extratime_mode == FIRST_EXTRA_TIME)
	//{
	//	StartCoinTossCutScene();
	//}
	// Rugby 15 we only show the extra time huddle, then move onto the kick off
	// Or in subsequent halves for R7 games
	//else
#endif
	{
		AddCutsceneCallbackElement(SSCutSceneManager::StartPostGamePhaseCallback, START_POST_GAME_PHASE_CALLBACK);
		AddScreenWipeElement();

		if (cinematics_enabled)
		{
			AddCutsceneCinematicElement(RUGamePhase::PRE_KICK_OFF, ENABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);
			AddCinematicTLE(CSEVENT_ENDGAME_EXTRA_TIME, MabMatrix::IDENTITY, SIDE_A);
			AddUserSkipPointElement();
		}

		AddPreKickOffCutScene(next_half);
	}
}

#ifdef ENABLE_SEVENS_MODE
///-------------------------------------------------------------------------------
///  When the coin toss was won/lost
///-------------------------------------------------------------------------------

void SSCutSceneManager::OnCoinTossWonLost(RUTeam* winningTeam)
{
	MABBREAKMSG("DEPRECATED");
	//SIFUIHelpers::SetCurrentWindow("GameWindow");

	SetCelebratingTeam(winningTeam);

	AddCutsceneCallbackElement(SSCutSceneManager::HoldScreenWipeCallback, HOLD_SCREENWIPE_CALLBACK, 100);

	AddCutsceneAsyncPreloadElement(false);
	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);
	//AddCutsceneCallbackElement(SSCutSceneManager::CoinTossResultWindowCallback, COINTOSS_RESULT_WINDOW_CALLBACK);

	// In a sevens game, we should show the coin toss cutscene
	if (false) // Nick  WWS 7s to Womens // game->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_SEVENS)
	{
		//AddCutsceneCallbackElement( SSCutSceneManager::StartExtraTimeCoinTossPhaseCallback, START_EXTRA_TIME_COIN_TOSS_GAME_PHASE_CALLBACK);
		AddScreenWipeElement();

		if (cinematics_enabled)
		{
			AddCutsceneCinematicElement(RUGamePhase::EXTRA_TIME_TOSS, ENABLE_SKIP, SWIPE_CUT, SHOW_NON_ACTORS);
			AddCinematicTLE(CSEVENT_ENDGAME_COINTOSS_POST_WIN, MabMatrix::IDENTITY, SIDE_NONE);

			AddUserSkipPointElement();

			AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);
			AddCutsceneCallbackElement(SSCutSceneManager::StartPostExtraTimeCoinTossPhaseCallback, START_POST_EXTRA_TIME_COIN_TOSS_GAME_PHASE_CALLBACK);

			AddCutsceneCallbackElement(SSCutSceneManager::CoinTossDecisionWindowCallback, COINTOSS_DECISION_WINDOW_CALLBACK);
			AddCinematicTLE(game->GetStadiumManager()->GetUseSmallPans() ? CSEVENT_HALFTIME_PAN_SMALL : CSEVENT_HALFTIME_PAN, MabMatrix::IDENTITY, SIDE_NONE);
		}
	}
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void SSCutSceneManager::OnCoinTossDecideToKick(RUTeam* kickedTeam)
{
	MABBREAKMSG("DEPRECATED");
	//SIFUIHelpers::SetCurrentWindow("GameWindow");

	SetCelebratingTeam(kickedTeam);

	AddCutsceneCallbackElement(SSCutSceneManager::HoldScreenWipeCallback, HOLD_SCREENWIPE_CALLBACK, 100);

	AddCutsceneAsyncPreloadElement(false);
	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);
	//AddCutsceneCallbackElement(SSCutSceneManager::CoinTossResultWindowCallback, COINTOSS_RESULT_WINDOW_CALLBACK);

	// In a sevens game, we should show the coin toss cutscene
	if (false) // Nick  WWS 7s to Womens //game->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_SEVENS)
	{
		//AddCutsceneCallbackElement( SSCutSceneManager::StartExtraTimeCoinTossPhaseCallback, START_EXTRA_TIME_COIN_TOSS_GAME_PHASE_CALLBACK);
		AddScreenWipeElement();

		if (cinematics_enabled)
		{
			AddCutsceneCinematicElement(RUGamePhase::EXTRA_TIME_TOSS, ENABLE_SKIP, SWIPE_CUT, SHOW_NON_ACTORS);
			AddCinematicTLE(CSEVENT_ENDGAME_COINTOSS_POST_DECISION1, MabMatrix::IDENTITY, SIDE_NONE);

			AddUserSkipPointElement();

			AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);
			AddCutsceneCallbackElement(SSCutSceneManager::StartPostExtraTimeCoinTossPhaseCallback, START_POST_EXTRA_TIME_COIN_TOSS_GAME_PHASE_CALLBACK);

			AddCutsceneCallbackElement(SSCutSceneManager::CoinTossDecisionWindowCallback, COINTOSS_DECISION_WINDOW_CALLBACK);
			AddCinematicTLE(game->GetStadiumManager()->GetUseSmallPans() ? CSEVENT_HALFTIME_PAN_SMALL : CSEVENT_HALFTIME_PAN, MabMatrix::IDENTITY, SIDE_NONE);
		}
	}
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void SSCutSceneManager::OnCoinTossDecideDirection(RUTeam* directingTeam, int)
{
	MABBREAKMSG("DEPRECATED");
	//SIFUIHelpers::SetCurrentWindow("GameWindow");

	SetCelebratingTeam(directingTeam);

	AddCutsceneCallbackElement(SSCutSceneManager::HoldScreenWipeCallback, HOLD_SCREENWIPE_CALLBACK, 100);

	AddCutsceneAsyncPreloadElement(false);
	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);
	//AddCutsceneCallbackElement(SSCutSceneManager::CoinTossResultWindowCallback, COINTOSS_RESULT_WINDOW_CALLBACK);

	// In a sevens game, we should show the coin toss cutscene
	if (false) // Nick  WWS 7s to Womens //(game->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_SEVENS)
	{
		//AddCutsceneCallbackElement( SSCutSceneManager::StartExtraTimeCoinTossPhaseCallback, START_EXTRA_TIME_COIN_TOSS_GAME_PHASE_CALLBACK);
		AddScreenWipeElement();

		if (cinematics_enabled)
		{
			AddCutsceneCinematicElement(RUGamePhase::EXTRA_TIME_TOSS, ENABLE_SKIP, SWIPE_CUT, SHOW_NON_ACTORS);
			AddCinematicTLE(CSEVENT_ENDGAME_COINTOSS_POST_DECISION2, MabMatrix::IDENTITY, SIDE_NONE);

			AddUserSkipPointElement();

			AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);
			AddCutsceneCallbackElement(SSCutSceneManager::StartPostExtraTimeCoinTossPhaseCallback, START_POST_EXTRA_TIME_COIN_TOSS_GAME_PHASE_CALLBACK);

			//AddCutsceneCallbackElement( SSCutSceneManager::CoinTossHorTWindowCallback, COINTOSS_DECISION_WINDOW_CALLBACK );//dont enable. Commented from RC3.
			//AddCinematicTLE(game->GetStadiumManager()->GetUseSmallPans() ? CSEVENT_HALFTIME_PAN_SMALL : CSEVENT_HALFTIME_PAN, MabMatrix::IDENTITY, SIDE_NONE);
		}
	}
}

///-------------------------------------------------------------------------------
///
///-------------------------------------------------------------------------------

void SSCutSceneManager::OnCoinTossFinished()
{
//	SIFUIHelpers::SetCurrentWindow("GameWindow");
//
// 	AddScreenWipeElement();

	// This should kill any remaining cutscene, and then set the game window up
	game->GetCutSceneManager()->StartHalfTimeWalkOn();

	AddPreKickOffSevensExtraTimeCutScene(next_half);
}
#endif

///-------------------------------------------------------------------------------
/// Setup full time cutscene.
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetupFullTimeCutscene()
{
	//#rc3_legacy RU_NETWORK_REPLAY_FINISH_GAME();

	// Always play the fulltime cutscenes.
#if 0 // PLATFORM_SWITCH
	if (game->GetGameSettings().game_settings.network_game)
	{
		cinematics_enabled = true;
	}
#endif

	bool is_draw = !SetCelebratingTeamBasedOnScore();		// returns false if draw.

	AddCutsceneCallbackElement(SSCutSceneManager::HoldScreenWipeCallback, HOLD_SCREENWIPE_CALLBACK, 100);

	AddCutsceneAsyncPreloadElement(false);
	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);
	AddCutsceneCallbackElement(SSCutSceneManager::StartPostGamePhaseCallback, START_POST_GAME_PHASE_CALLBACK);

	AddScreenWipeElement();

	if (cinematics_enabled)
	{
		if (!is_draw)
		{
			AddCutsceneCinematicElement(RUGamePhase::HALF_TIME, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
			AddCinematicTLE(CSEVENT_ENDGAME_WIN, MabMatrix::IDENTITY, GetCelebratingTeamSide());
			AddBackgroundCinematicTLE(CSEVENT_ENDGAME_BACKGROUND, MabMatrix::IDENTITY, GetCelebratingTeamSide(), 0.0f, 0.0f);

			AddCutSceneStopAsyncPreloadElement();			// Will only preload- async load up to here.
			AddCutsceneCinematicElement(RUGamePhase::HALF_TIME, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
			AddCinematicTLE(CSEVENT_ENDGAME_B, MabMatrix::IDENTITY, GetCelebratingTeamSide());
			AddBackgroundCinematicTLE(CSEVENT_ENDGAME_BACKGROUND, MabMatrix::IDENTITY, GetCelebratingTeamSide(), 0.0f, 0.0f);

			AddCutsceneCinematicElement(RUGamePhase::HALF_TIME, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
			AddCinematicTLE(CSEVENT_ENDGAME_C, MabMatrix::IDENTITY, GetCelebratingTeamSide());
			AddBackgroundCinematicTLE(CSEVENT_ENDGAME_BACKGROUND, MabMatrix::IDENTITY, GetCelebratingTeamSide(), 0.0f, 0.0f);
		}
		else
		{
			AddCutsceneCinematicElement(RUGamePhase::HALF_TIME, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
			AddCinematicTLE(CSEVENT_ENDGAME_DRAW, MabMatrix::IDENTITY, GetCelebratingTeamSide());
		}

		AddUserSkipPointElement();
	}

	AddCutsceneCallbackElement(SSCutSceneManager::FullTimeWindowCallback, FULLTIME_WINDOW_CALLBACK);

	AddCutsceneCinematicElement(RUGamePhase::HALF_TIME, DISABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
	AddCinematicTLE(game->GetStadiumManager()->GetUseSmallPans() ? CSEVENT_HALFTIME_PAN_SMALL : CSEVENT_HALFTIME_PAN, MabMatrix::IDENTITY, SIDE_NONE);
}

///-------------------------------------------------------------------------------
/// StartGrandFinalCelebrations:
///-------------------------------------------------------------------------------

void SSCutSceneManager::StartGrandFinalCelebrations()
{
	int cutscene_debug_comp = 0;
#ifdef ENABLE_GAME_DEBUG_MENU
	//#rc3_legacy_debug_cutscene cutscene_debug_comp = SIFDebug::GetCutsceneDebugSettings()->GetCutsceneDebugCompetition();
#endif

	RUCareerModeManager *career_manager = SIFApplication::GetApplication()->GetCareerModeManager();
	RL3ActiveCompetition* active_competition = NULL;
	if (career_manager && career_manager->IsActive())
		active_competition = (RL3ActiveCompetition*)career_manager->GetActiveCompetition();

	int trophy_csevent = CSEVENT_TROPHY_WIN_GENCUP1;
	if (cutscene_debug_comp>0)
	{
		trophy_csevent = CSEVENT_TROPHY_WIN_WORLDCUP + (cutscene_debug_comp - 1);
	}
	if (active_competition)
	{
		// Can't have a Ranfurly challenge in a finals, so if this is true we've got a shield trophy cutscene to show
		if (active_competition->IsCurrentMatchRanfurlyChallenge())
			trophy_csevent = CSEVENT_TROPHY_WIN_WORLDCUP + CUPIDX_RANF;
		else
		{
			const char* cupName = active_competition->GetCupName();

			// When we're in a trophy final, work out which trophy it is, so we can get the correct trophy name
			if(active_competition->GetPreliminaryFormat() == PRELIMINARIES_SATELLITE)
			{
				// Make sure we're actually in a trophy final
				MABASSERT(active_competition->IsCurrentMatchSevensTrophyFinal());

				int trophyID = active_competition->GetSatelliteTrophyId(active_competition->GetCurrentMatchSevensFinalTrophyType());
				cupName = active_competition->GetTrophyName(trophyID);
	}

			MABLOGDEBUG("Running cutscene for cup name: %s", cupName);

			trophy_csevent = CSEVENT_TROPHY_WIN_WORLDCUP + static_cast< int >( TranslateCupNameToId( cupName ) );
		}
	}

	MabMath::Clamp(trophy_csevent, (int)CSEVENT_TROPHY_WIN_WORLDCUP, (int)CSEVENT_TROPHY_LOSE_A - 1);

	unsigned short league_winner_db_id = 0;
	if (active_competition != NULL && active_competition->GetIsLeagueGrandFinalsMatch(league_winner_db_id))
	{
		if (league_winner_db_id != 0)
		{
			// Not bledisloe, but league and one of the teams has definitely won the league (+ is their last match of comp)
			RUTeam* team_a = game->GetTeam(SIDE_A);
			RUTeam* team_b = game->GetTeam(SIDE_B);
			if (team_a->GetDbTeam().GetDbId() == league_winner_db_id)
				SetCelebratingTeam(team_a);
			else
				SetCelebratingTeam(team_b);
		}
		else
		{
			SetBledisloeWinningTeam(active_competition);
		}
	}
	else
	{
		SetCelebratingTeamBasedOnScore();
	}

	RUTeam *cel_team = GetCelebratingTeam();
	RUTeam *com_team = (RUTeam*)cel_team->GetOppositionTeam();

	MabMatrix transform = MabMatrix::IDENTITY;

	StopAllCutScenes();
	game->GetScreenWipeManager()->StartFadeFromBlack(DEFAULT_SCREEN_WIPE_TIME);
	game->GetScreenWipeManager()->HoldScreenWipe(100.0f);

	AddCutsceneAsyncPreloadElement(true);
	AddCutsceneCallbackElement(&SSCutSceneManager::GrandFinalCelebrationStartCallback, GRAND_FINAL_START_CALLBACK);

	AddScreenWipeElement();
	AddCutsceneCinematicElement(RUGamePhase::HALF_TIME, DISABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);
	AddCinematicTLE(trophy_csevent, transform, cel_team->GetSide());
	AddCinematicTLE(CSEVENT_TROPHY_LOSE_A, transform, com_team->GetSide());

	// HACK!!! - Saves creating another cutscene.
	MabMatrix restofteam_transform = transform;
	if (trophy_csevent == CSEVENT_TROPHY_WIN_RANF || trophy_csevent == CSEVENT_TROPHY_WIN_ITM)
	{	/// If ranfurly/itm, captain cutscene is on ground and therefore need to lower the other players cutscene.
		const float LOWER_OTHER_PLAYERS_DISTANCE = -1.345f;
		MabMatrix trans_matrix = MabMatrix::TransMatrix(0.0f, LOWER_OTHER_PLAYERS_DISTANCE, 0.0f);
		restofteam_transform *= trans_matrix;
	}
	AddCinematicTLE(CSEVENT_TROPHY_B, restofteam_transform, cel_team->GetSide());

	/// Setup fireworks.

#if 1		// Disable fireworks for grand final until leaks are sorted.
	switch (trophy_csevent)
	{
	case CSEVENT_TROPHY_WIN_WORLDCUP:
		AddCinematicTLE(CSEVENT_FIREWORKS_TROPHY_WIN_CONFETTI, MabMatrix::IDENTITY, SIDE_NONE);
		AddCinematicTLE(CSEVENT_FIREWORKS_TROPHY_WIN_GROUND, MabMatrix::IDENTITY, SIDE_NONE);
		AddCinematicTLE(CSEVENT_FIREWORKS_TROPHY_WIN_SKY, MabMatrix::IDENTITY, SIDE_NONE);
		break;

	case CSEVENT_TROPHY_WIN_ITM:
		AddCinematicTLE(CSEVENT_FIREWORKS_TROPHY_WIN_GROUND, MabMatrix::IDENTITY, SIDE_NONE);
		break;

	case CSEVENT_TROPHY_WIN_RANF:
		break;

	case CSEVENT_TROPHY_WIN_NRC:
		AddCinematicTLE(CSEVENT_FIREWORKS_TROPHY_WIN_GROUND, MabMatrix::IDENTITY, SIDE_NONE);
		break;

	case CSEVENT_TROPHY_WIN_BLED:
	case CSEVENT_TROPHY_WIN_GENPLATE:
		AddCinematicTLE(CSEVENT_FIREWORKS_TROPHY_WIN_GROUND, MabMatrix::IDENTITY, SIDE_NONE);
		AddCinematicTLE(CSEVENT_FIREWORKS_TROPHY_WIN_SKY, MabMatrix::IDENTITY, SIDE_NONE);
		break;

	case CSEVENT_TROPHY_WIN_SUPER15:
	case CSEVENT_TROPHY_WIN_ACC:
	case CSEVENT_TROPHY_WIN_AVIVA:
	case CSEVENT_TROPHY_WIN_GENCUP1:
	case CSEVENT_TROPHY_WIN_GENCUP2:
	case CSEVENT_TROPHY_WIN_GENCUP3:
	case CSEVENT_TROPHY_WIN_GENSHIELD:
	case CSEVENT_TROPHY_WIN_GENTROPHY:
	case CSEVENT_TROPHY_WIN_GENBOWL:
	case CSEVENT_TROPHY_WIN_TOP14:
	case CSEVENT_TROPHY_WIN_PRO12:
	case CSEVENT_TROPHY_WIN_PROD2:
	case CSEVENT_TROPHY_WIN_LIONS:
		AddCinematicTLE(CSEVENT_FIREWORKS_TROPHY_WIN_CONFETTI, MabMatrix::IDENTITY, SIDE_NONE);
		AddCinematicTLE(CSEVENT_FIREWORKS_TROPHY_WIN_GROUND, MabMatrix::IDENTITY, SIDE_NONE);
		break;

	default:
		break;
	}
#endif

	AddUserSkipPointElement();

	//Don't actually need this since the particles are cleared when freeing any allocated game world memory.
	// This is only run when viewing a grand final celebration and exiting a match will clear particles anyway.
	//AddCutsceneCallbackElement( SSCutSceneManager::KillAllParticlesCallback, "Kill All Particles!" );

	AddCutsceneCinematicElement(RUGamePhase::HALF_TIME, DISABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);
	AddCinematicTLE(game->GetStadiumManager()->GetUseSmallPans() ? CSEVENT_HALFTIME_PAN_SMALL : CSEVENT_HALFTIME_PAN, MabMatrix::IDENTITY, SIDE_NONE);
}

void SSCutSceneManager::StartTitleIntro()
{
	int trophy_csevent = CSEVENT_TROPHY_WIN_GENCUP1;
	
	MabMatrix transform = MabMatrix::IDENTITY;

	StopAllCutScenes();
	game->GetScreenWipeManager()->StartFadeFromBlack(DEFAULT_SCREEN_WIPE_TIME);
	game->GetScreenWipeManager()->HoldScreenWipe(100.0f);

	AddCutsceneAsyncPreloadElement(true);
	//AddCutsceneCallbackElement(&SSCutSceneManager::GrandFinalCelebrationStartCallback, GRAND_FINAL_START_CALLBACK);

	//AddScreenWipeElement();
	AddCutsceneCinematicElement(RUGamePhase::PLAY, false, SWIPE_NONE, true);
	AddCinematicTLE(CSEVENT_UI_TITLE, transform, SSTEAMSIDE::SIDE_NONE);

	//AddUserSkipPointElement();
	//
	//
	//AddCutsceneCinematicElement(RUGamePhase::PLAY, true, SWIPE_NONE, true);
	//AddCinematicTLE(CSEVENT_UI_TITLE, MabMatrix::IDENTITY, SIDE_NONE);
}

/// Hack method to set the "winning team"--aka the team that should be raising a trophy--to the team that's won the Bledisloe.
void SSCutSceneManager::SetBledisloeWinningTeam(const RUActiveCompetitionBase* active_competition)
{
	if (active_competition == NULL)
	{
		MABBREAKMSG("Where is our active competition?");
		return;
	}

	RUTeam* team_a = game->GetTeam(SIDE_A);
	RUTeam* team_b = game->GetTeam(SIDE_B);

	// First we check to see if either team has already won two matches. If they have, they're winning the Bledisloe.
	//

	// Iterate through all teams...
	// (Jordan makes the excellent point that GetNumTeams will always return 2, and because of the hack below with the number of wins,
	//	we're not able to handle any changes to this method; it's tied to the Bledisloe.
	//	But! I'm keeping it in here, because that way you know without having to read this long comment
	//	what the otherwise-magic-number 2 refers to (ie. the number of teams.))
	for (int team_index = 0; team_index < active_competition->GetNumTeams(); ++team_index)
	{
		const unsigned short this_db_team_id = active_competition->GetTeamIdFromIndex(team_index);
		RUTeam* this_team((team_a->GetDbTeam().GetDbId() == this_db_team_id) ? team_a : team_b);

		// Has a team already won two matches? If so, they won the competition and will be the team that raises the trophy.
		if (active_competition->GetTeamStatsForTeamId(this_db_team_id).games_won >= 2u)
		{
			SetCelebratingTeam(this_team);
			return;
		}
	}

	// Means that neither team has won two matches, and so we'll set the celebrating team based on their performance in *this* match.

	RUStatisticsSystem *stats = SIFApplication::GetApplication()->GetStatisticsSystem();

	int score0 = stats->GetCurrentMatchStat(team_a, &RUDB_STATS_TEAM::score);
	int score1 = stats->GetCurrentMatchStat(team_b, &RUDB_STATS_TEAM::score);

	if (score0 == score1)
	{
		// 3 Draws in Bledisloe cup!! Damn, must now use the same calculationn as RL3ActiveCompetition::GetStandings()
		// Unfortunately this match hasn't been added to the database stats yet, so must duplicate calculations here.
		// (Can't use SetCelebratingTeamBasedOnScore())

		RL3DB_TEAM db_team_a(team_a->GetDbTeam().GetDbId());
		RL3DB_TEAM db_team_b(team_b->GetDbTeam().GetDbId());

		// Get stats, not including this match.
		const RUDB_COMP_INST_TEAM_STATS &team_a_stats = active_competition->GetTeamStatsForTeamId(team_a->GetDbTeam().GetDbId());
		int team_a_points_for = team_a_stats.prelim_points_for;
		int team_a_points_against = team_a_stats.prelim_points_against;
		int team_a_tries_conceded = team_a_stats.prelim_tries_conceded;
		int team_a_tries_scored = team_a_stats.prelim_tries_scored;
		int team_a_penalties_conceded = team_a_stats.prelim_penalties_conceded;

		MABASSERT(team_a_stats.games_played == 2);		// Just check that we're doing this correctly (ie. 2 games played, not including this one)

		const RUDB_COMP_INST_TEAM_STATS &team_b_stats = active_competition->GetTeamStatsForTeamId(team_b->GetDbTeam().GetDbId());
		int team_b_points_for = team_b_stats.prelim_points_for;
		int team_b_points_against = team_b_stats.prelim_points_against;
		int team_b_tries_conceded = team_b_stats.prelim_tries_conceded;
		int team_b_tries_scored = team_b_stats.prelim_tries_scored;
		int team_b_penalties_conceded = team_b_stats.prelim_penalties_conceded;

		// Add this matches stats to comp stats.
		team_a_points_for += score0;
		team_a_points_against += score1;
		team_a_tries_conceded += stats->GetCurrentMatchStat(team_a, &RUDB_STATS_TEAM::tries_conceded);
		team_a_tries_scored += stats->GetCurrentMatchStat(team_a, &RUDB_STATS_TEAM::tries_scored);
		team_a_penalties_conceded += stats->GetCurrentMatchStat(team_a, &RUDB_STATS_TEAM::penalties_conceded);

		team_b_points_for += score1;
		team_b_points_against += score0;
		team_b_tries_conceded += stats->GetCurrentMatchStat(team_b, &RUDB_STATS_TEAM::tries_conceded);
		team_b_tries_scored += stats->GetCurrentMatchStat(team_b, &RUDB_STATS_TEAM::tries_scored);
		team_b_penalties_conceded += stats->GetCurrentMatchStat(team_b, &RUDB_STATS_TEAM::penalties_conceded);

		// Points differential
		int team_a_points_differential = team_a_points_for - team_a_points_against;
		int team_b_points_differential = team_b_points_for - team_b_points_against;
		if (team_a_points_differential > team_b_points_differential)
		{
			SetCelebratingTeam(team_a);
			return;
		}
		else if (team_b_points_differential > team_a_points_differential)
		{
			SetCelebratingTeam(team_b);
			return;
		}

		// tries conceded
		if (team_a_tries_conceded > team_b_tries_conceded)
		{
			SetCelebratingTeam(team_a);
			return;
		}
		else if (team_b_tries_conceded > team_a_tries_conceded)
		{
			SetCelebratingTeam(team_b);
			return;
		}

		// tries scored
		if (team_a_tries_scored > team_b_tries_scored)
		{
			SetCelebratingTeam(team_a);
			return;
		}
		else if (team_b_tries_scored > team_a_tries_scored)
		{
			SetCelebratingTeam(team_b);
			return;
		}

		// penalties conceded
		if (team_a_penalties_conceded > team_b_penalties_conceded)
		{
			SetCelebratingTeam(team_a);
			return;
		}
		else if (team_b_penalties_conceded > team_a_penalties_conceded)
		{
			SetCelebratingTeam(team_b);
			return;
		}

		// Last resort, name comparison
		if (strcmp(db_team_a.GetName(), db_team_b.GetName()) > 0)
		{
			SetCelebratingTeam(team_a);
		}
		else
		{
			SetCelebratingTeam(team_b);
		}
	}
	else
	{
		SetCelebratingTeam(score0>score1 ? team_a : team_b);
	}
}


///-------------------------------------------------------------------------------
/// Async load has fininshed - so tell window system to continue.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::GrandFinalCelebrationStartCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	/* #rc3_legacy #WindowSystem
	SIFWindowSystem *window_system = SIFApplication::GetApplication()->GetWindowSystem();
	if (window_system) 
		window_system->LoadPendingCompleted(SIFWindowSystem::PENDING_WINDOW_CUT_SCENE);
	*/

	manager->game->GetEvents()->cutscene_trophy_ceremony();
	return true;
}

///-------------------------------------------------------------------------------
/// Calculate angle from 'pos' to the interchange locator for the stadium.
///-------------------------------------------------------------------------------

float SSCutSceneManager::CalculateAngleToInterchangeLocator(FVector pos)
{
	FVector position = FVector::ZERO;
	float	angle = 0.0f;
	if (!game->GetStadiumManager()->GetStadiumCutsceneLocator(position, angle, INTERCHANGE_LOCATOR_TYPE))
	{
		position.x = INTERCHANGE_DEFAULT_X;
	}

	position = pos - position;
	angle = MabMath::ATan2(-position.x, -position.z);
	return angle;
}

///-------------------------------------------------------------------------------
/// Setup an interchange (private, can be used to insert into other sequences).
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetupInterchange()
{
	MABLOGDEBUG("SetupInterchanges()::");
	if (disable_substitutions)		// Don't enable interchanges if one has already been put in the cutscene_elements list.
		return;

	bool allowInterchangeCutscene = false;

	RUSubstitutionManager *sub_manager = game->GetSubstitutionManager();

	//MABASSERT(sub_manager->GetNumQueuedEvents()>0);

	/// Loop over all pending interchanges.

	int num_pending = sub_manager->GetNumQueuedEvents();

	if (num_pending>0)
	{
		// Only play cutscene for the first event in the queue.
		const RUInterchangeEvent *entry = sub_manager->GetHighestPriorityPendingEvent();
		MABLOGDEBUG("SetupInterchanges():: Interchange type %d", entry->GetType());
		/// Don't do exchange (tactical substitutions in extra time).

		if (entry->GetType() == RU_INTERCHANGE_EXCHANGE)
		{
			AddCutsceneCallbackElement(SSCutSceneManager::ShowInterchangeHUDCallback, SHOW_INTERCHANGE_HUD_CALLBACK);
			AddCutsceneCallbackElement(SSCutSceneManager::NotifyInterchangeStartedCallback, NOTIFY_INTERCHANGE_STARTED_CALLBACK);

			AddCutsceneCallbackElement(SSCutSceneManager::StartSubstituteLoadCallback, START_SUBSTITION_LOAD_CALLBACK, entry->GetUID());

			if (cinematics_enabled && allowInterchangeCutscene)
			{
				FVector pos = entry->GetPrimaryPlayer()->GetMovement()->GetCurrentPosition();
				MabMatrix transform = MabMatrix::TransMatrix(pos);

				/// Rotate cutscene so player walks in direction of interchange locator.
				float angle_to_interchange_locator = CalculateAngleToInterchangeLocator(pos);
				MabMatrix nr = MabMatrix::RotMatrixY(angle_to_interchange_locator);
				transform = nr * transform;

				ClampTransform(transform, CUTSCENE_SAFETY_CLAMP_DISTANCE);

				AddCutsceneCinematicElement(RUGamePhase::PLAY, DISABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);
				AddCinematicTLE(CSEVENT_TACTICAL_SUBSTITUTION_A, transform, SIDE_NONE);

				/// Setup background cutscenes for 'non-actors'.

				FVector cspos(0, 0, 0);
				cspos = transform.TransformPos(cspos);
				SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

				AddUserSkipPointElement();
			}

			AddCutsceneCallbackElement(SSCutSceneManager::MidSubstitutionCallback, MID_SUBSTITUTION_CALLBACK, entry->GetUID());

			AddCutsceneCallbackElement(SSCutSceneManager::NotifyMidInterchangeCallback, NOTIFY_MID_INTERCHANGE_CALLBACK);

			if (cinematics_enabled && allowInterchangeCutscene)
			{
				MabMatrix transform = MabMatrix::IDENTITY;
				FVector position = FVector::ZERO;
				float	angle = 0.0f;

				if (!game->GetStadiumManager()->GetStadiumCutsceneLocator(position, angle, INTERCHANGE_LOCATOR_TYPE))
				{
					position.x = INTERCHANGE_DEFAULT_X;
					angle = INTERCHANGE_DEFAULT_ANGLE;
				}
				MabMatrix nr = MabMatrix::RotMatrixY(MabMath::Deg2Rad(angle));
				MabMatrix nm = MabMatrix::TransMatrix(position);
				transform = nr * nm;

				AddCutsceneCinematicElement(RUGamePhase::PLAY, ENABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);
				AddCinematicTLE(CSEVENT_TACTICAL_SUBSTITUTION_B, transform, SIDE_NONE);

				/// Setup background cutscenes for 'non-actors'.

				FVector cspos(0, 0, 0);
				cspos = transform.TransformPos(cspos);
				SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

				AddUserSkipPointElement();
			}

			AddCutsceneCallbackElement(SSCutSceneManager::DoSubstitutionCallback, DO_SUBSTITUTION_CALLBACK, entry->GetUID());
			AddCutsceneCallbackElement(SSCutSceneManager::PerformRemainingInterchangesCallback, PERFORM_REMAINING_INTERS_CALLBACK, entry->GetUID());

			// Pro mode, if our pro is sent off, throw up a message saying so.
			//if(sub_manager->GetIsProPlayerSendOffEventQueued()) //commented in RC3
			//	StartSimulationCutScene();


			disable_substitutions = true;
		}
		else if (entry->GetType() == RU_INTERCHANGE_SENTOFF || entry->GetType() == RU_INTERCHANGE_SINBIN_OFF)
		{
			AddCutsceneCallbackElement(SSCutSceneManager::NotifyMidInterchangeCallback, NOTIFY_MID_INTERCHANGE_CALLBACK);
			AddCutsceneCallbackElement(SSCutSceneManager::SentOffEndCallback, SENT_OFF_CALLBACK, entry->GetUID());
			AddCutsceneCallbackElement(SSCutSceneManager::PerformRemainingInterchangesCallback, PERFORM_REMAINING_INTERS_CALLBACK, entry->GetUID());

			// Pro mode, if our pro is sent off, throw up a message saying so. //commented in RC3
			//if(sub_manager->GetIsProPlayerSendOffEventQueued())
			//	StartSimulationCutScene();

			disable_substitutions = true;
		}
		else if (entry->GetType() == RU_INTERCHANGE_SINBIN_RETURN)
		{
			MABLOGDEBUG("SetupInterchanges():: Return from sinbin triggered");

			AddCutsceneCallbackElement(SSCutSceneManager::ShowInterchangeHUDCallback, SHOW_INTERCHANGE_HUD_CALLBACK);
			AddCutsceneCallbackElement(SSCutSceneManager::SinbinReturnStartCallback, SINBIN_RETURN_START_CALLBACK, entry->GetUID());

			AddCutsceneCallbackElement(SSCutSceneManager::NotifyMidInterchangeCallback, NOTIFY_MID_INTERCHANGE_CALLBACK);
			if (cinematics_enabled)
			{
				MabMatrix transform = MabMatrix::IDENTITY;
				FVector position = FVector::ZERO;
				float	angle = 0.0f;

				if (!game->GetStadiumManager()->GetStadiumCutsceneLocator(position, angle, INTERCHANGE_LOCATOR_TYPE))
				{
					position.x = INTERCHANGE_DEFAULT_X;
					angle = INTERCHANGE_DEFAULT_ANGLE;
				}

				MabMatrix nr = MabMatrix::RotMatrixY(MabMath::Deg2Rad(angle + 90.0f));
				MabMatrix nm = MabMatrix::TransMatrix(position);
				transform = nr * nm;

				ClampTransform(transform, CUTSCENE_SAFETY_CLAMP_DISTANCE);

				AddCutsceneCinematicElement(RUGamePhase::PLAY, ENABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);
				AddCinematicTLE(CSEVENT_SINBIN_RETURN, transform, SIDE_NONE);

				/// Setup background cutscenes for 'non-actors'.

				FVector cspos(0, 0, 0);
				cspos = transform.TransformPos(cspos);
				SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

				AddUserSkipPointElement();
			}
			AddCutsceneCallbackElement(SSCutSceneManager::PerformRemainingInterchangesCallback, PERFORM_REMAINING_INTERS_CALLBACK, entry->GetUID());

			disable_substitutions = true;
		}

		// Pro mode, if our pro is sent off, throw up a message saying so.
		if (sub_manager->GetIsProPlayerSendOffEventQueued())
		{
			MABLOGDEBUG("SetupInterchanges():: Our pro player has been queued up for being sent off");
			pro_player_sent_off_during_interchanges = true;
		}
	}
}

///-------------------------------------------------------------------------------
/// Called on event 'game_events->conversion_finish'
///-------------------------------------------------------------------------------

void SSCutSceneManager::ConversionCutSceneBegin(bool success)
{
	ConversionCutSceneBegin(success, false);
}

///-------------------------------------------------------------------------------
/// Called on event 'game_events->penalty_goal_finish'
///-------------------------------------------------------------------------------

void SSCutSceneManager::PenaltyCutSceneBegin(bool success, const FVector & /*crossed_goal_position*/)
{
	//we only play the conversion result cutscene for successful penalty kicks
	//if the penalty kick is unsuccessful then play will continue, and we don't want
	//any cutscenes
	if (success && game->GetGameTimer()->GetExtraTimeMode() != GOLDEN_POINT)
	{
		ConversionCutSceneBegin(success, false);
	}
}

void SSCutSceneManager::ConversionCutSceneBegin(bool success, bool is_dropgoal)
{
	//RUGameState *game_state = game->GetGameState();

	MABLOGDEBUG("*** ConversionCutSceneBegin: %s", success ? "ok" : "fail");

	AddCutsceneDelayElement(15);
	AddScreenWipeElement();
	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	if (!is_dropgoal)
		AddCutsceneCallbackElement(SSCutSceneManager::StartPostConversionPhaseCallback, START_POST_CONVERSION_PHASE_CALLBACK);


	const float KICKER_TEAM_BG_Z_POSITION = -20.0f;
	const float OPPOSITION_TEAM_BG_Z_OFFSET = 12.0f; // Nick WWS 7s to Womens 13s // game->GetGameSettings().game_settings.GameModeIsR7() ? 2.0f : 12.0f;		//55.0f;

	ARugbyCharacter* kicker = game->GetGameState()->GetLastKicker();
	MABASSERT(kicker);
	if (kicker && cinematics_enabled)
	{
		SSTEAMSIDE teamside = kicker->GetAttributes()->GetTeamSide();
		RUTeam *kicker_team = kicker->GetAttributes()->GetTeam();

		SetCelebratingTeam(kicker_team);		// Set celebrating team regardless of success/failure.

		RUTeam *opposition_team = (RUTeam*)kicker_team->GetOppositionTeam();
		float play_dir = (float)kicker_team->GetPlayDirection();

		MabMatrix nr = MabMatrix::IDENTITY;
		MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);
		MabMatrix transform = MabMatrix::IDENTITY;
		MabMatrix transform_bg = MabMatrix::IDENTITY;
		MabMatrix opposition_transform_bg = MabMatrix::IDENTITY;

		if (kicker_team->GetPlayDirection() == ERugbyPlayDirection::SOUTH)
			nr = rot_transform;

		FVector cspos(0, 0, 0);
		cspos = kicker->GetMovement()->GetCurrentPosition();
		transform = nr * MabMatrix::TransMatrix(cspos);

		cspos.x = 0.0f;
		cspos.z += play_dir * OPPOSITION_TEAM_BG_Z_OFFSET;
		opposition_transform_bg = nr * rot_transform * MabMatrix::TransMatrix(cspos);

		cspos.x = 0.0f;
		cspos.z = play_dir * KICKER_TEAM_BG_Z_POSITION;
		transform_bg = nr * rot_transform * MabMatrix::TransMatrix(cspos);

		int cs_event;
		if (is_dropgoal)
		{
			MABASSERT(success);
			cs_event = CSEVENT_DROPGOAL_SUCCESS;
		}
		else
		{
			cs_event = success ? CSEVENT_KICKFORPOINTS_SUCCESS : CSEVENT_KICKFORPOINTS_FAIL;
		}

		
		bool should_play_music = RUCrowdAudioReactionManager::ChanceOfMusic(game->GetGameSettings()) > game->GetRNG()->RAND_CALL(float);
		AddCutsceneCallbackElement(SSCutSceneManager::PushMusicCallback, PUSH_MUSIC_CALLBACK,
			!should_play_music ? 0 : (int)SIFApplication::GetApplication()->GetRUAudio()->GetEventID("event:/music/conversion_positive"));

		AddCutsceneCinematicElement((is_dropgoal ? RUGamePhase::PLAY : RUGamePhase::CONVERSION), ENABLE_SKIP, (is_dropgoal && replays_enabled) ? SWIPE_WIPE : SWIPE_CUT, HIDE_NON_ACTORS);
		AddCinematicTLE(cs_event, transform, teamside);

		/// Set background cutscenes

		AddBackgroundCinematicTLE(CSEVENT_KICKFORPOINTS_BG, opposition_transform_bg, opposition_team->GetSide(), 0.0f, 0.0f);
		AddBackgroundCinematicTLE(CSEVENT_GENERIC_REACTION_POSITIVE_BG, transform_bg, kicker_team->GetSide(), 0.0f, 0.0f);


		AddUserSkipPointElement();
		AddCutsceneCallbackElement(SSCutSceneManager::PopMusicCallback, POP_MUSIC_CALLBACK);
	}

	AddCutsceneCallbackElement(SSCutSceneManager::StartPreKickOffPhaseCallback, START_PRE_KICKOFF_PHASE_CALLBACK);
}

///-------------------------------------------------------------------------------
/// Called on event 'game_events->restart_kick_start'
///-------------------------------------------------------------------------------

void SSCutSceneManager::KickoffCutSceneBegin()
{
}


///-------------------------------------------------------------------------------
/// Start Runon cutscene.
///-------------------------------------------------------------------------------

void SSCutSceneManager::RunonCutSceneBegin()
{
	if (game->IsMatch())
	{
		if (will_do_haka)
		{
			haka_commentary_delay = 0.25f;
			total_haka_comentary_delay = 0.0f;
		}
		else
		{
			MABLOGDEBUG("RunonCutSceneBegin");
			RequestElementFinish();			// Stop looping stadium pan...
		}
	}
}

///-------------------------------------------------------------------------------
/// Called on event 'game_events->try_result'
///-------------------------------------------------------------------------------

void SSCutSceneManager::LaunchPreTryCutscene(bool success, bool penalty_try, ARugbyCharacter* player)
{
#if 0		/// Disabled due to players running through fireworks!!
	if (game->GetWorldId() != WORLD_ID::SANDBOX)
	{
		if (success && !penalty_try)
		{
			MabMatrix transform = MabMatrix::IDENTITY;
			if (player->GetMovement()->GetCurrentPosition().z < 0.0f)
			{
				transform = MabMatrix::RotMatrixY(PI);
			}

			AddCutsceneAsyncPreloadElement(true);
			AddBGCutsceneCinematicElement(CSEVENT_FIREWORKS_PRE_TRY, transform);
			AddCutSceneStopAsyncPreloadElement();
		}
	}
#else
	MABUNUSED(success);
	MABUNUSED(penalty_try);
	MABUNUSED(player);
#endif
}


///-------------------------------------------------------------------------------
/// Called on event 'game_events->try_success'
///-------------------------------------------------------------------------------

static const float TRY_CELEBRATION_Z_POSITION = 50.0f;
static const float TRY_COMMISERATION_Z_POSITION = 55.0f;

static const float TRY_REACTION_Z_POSITION = 25.0f;
static const float TRY_REACTION_POSITIVE_BG_POSITION = 0.0f;
static const float TRY_REACTION_KICKFORPOINTS_POSITIVE_BG_POSITION = -20.0f;

static const float TRY_COMMISERATION_BG_Z_POSITION = 55.0f;
static const float TRY_CELEBRATION_Z_BG_POSITION = 30.0f;

void SSCutSceneManager::LaunchTryCutscene()
{
	ARugbyCharacter* player = game->GetGameState()->GetLastTryScorer();
	MABASSERT(player != NULL);
	if (!player)
		return;

	if (!game->IsMatch())
		return;

	//--------------------------------------------------------------------
	// turn off game rules now! - stop other rules firing in load delay.

	game->GetRules()->EnableConsequences(false);
	game->GetRules()->EnableTriggers(RURT_NONE);
	game->GetRules()->SuspendPlay(true, "SSCutSceneManager::LaunchTryCutscene");

	//----------------------

	SetCelebratingTeam(player->GetAttributes()->GetTeam());
	SetFocusPlayer(player, player->GetAttributes()->GetTeam()->GetSide());

	SSTEAMSIDE side = GetCelebratingTeamSide();

	// Calculate cutscene transform.

	MabMatrix transform = MabMatrix::IDENTITY;
	MabMatrix transform2 = MabMatrix::IDENTITY;
	MabMatrix transform_bg = MabMatrix::IDENTITY;
	MabMatrix transform2_bg = MabMatrix::IDENTITY;
	MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);
	FVector cspos(0, 0, 0);

	cspos = player->GetMovement()->GetCurrentPosition();
	cspos.y = 0.0f;
	float field_direction = (cspos.z<0.0f) ? -1.0f : 1.0f;

	MabMatrix nr = MabMatrix::IDENTITY;
	if (cspos.z<0.0f)
		nr = rot_transform;

	cspos.z = TRY_CELEBRATION_Z_POSITION * field_direction;
	transform = nr * MabMatrix::TransMatrix(cspos);

	cspos.x = 0.0f;			// Always run commiseration between posts.
	cspos.z = TRY_COMMISERATION_Z_POSITION * field_direction;
	transform2 = nr * MabMatrix::TransMatrix(cspos);

	cspos.x = 0.0f;		// for clarity as done above.
	cspos.z = TRY_CELEBRATION_Z_BG_POSITION * field_direction;
	transform_bg = nr * MabMatrix::TransMatrix(cspos);

	cspos.x = 0.0f;		// for clarity as done above.
	cspos.z = TRY_COMMISERATION_BG_Z_POSITION * field_direction;
	transform2_bg = nr * MabMatrix::TransMatrix(cspos);


	ClampTransform(transform, CUTSCENE_SAFETY_CLAMP_DISTANCE);
	ClampTransform(transform2, CUTSCENE_SAFETY_CLAMP_DISTANCE);

	//-------------------------

	//	AddCutsceneAsyncPreloadElement(false);
	AddCutsceneDelayElement(15);
	AddScreenWipeElement((!cinematics_enabled && replays_enabled) ? SWIPE_WIPE : SWIPE_CUT);
	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	SSTEAMSIDE dual_side = (side == SIDE_A) ? SIDE_B : SIDE_A;

	if (cinematics_enabled)
	{
		AddCutsceneCallbackElement(SSCutSceneManager::StartTryCutscenePhaseCallback, START_TRY_PHASE_CALLBACK);
		bool should_play_music = RUCrowdAudioReactionManager::ChanceOfMusic(game->GetGameSettings()) > game->GetRNG()->RAND_CALL(float);
		AddCutsceneCallbackElement(SSCutSceneManager::PushMusicCallback, PUSH_MUSIC_CALLBACK,
			!should_play_music ? 0 : (int)SIFApplication::GetApplication()->GetRUAudio()->GetEventID("event:/music/try_score_music"));

		AddCutsceneCinematicElement(RUGamePhase::TRY_CUTSCENE, ENABLE_SKIP, SWIPE_WIPE, HIDE_NON_ACTORS);
		if (player->GetAttributes()->GetTeam()->GetNumHumanPlayers()>0)
		{
			AddCinematicTLE(CSEVENT_TRY_CELEBRATION, transform, side);
			AddCinematicTLE(CSEVENT_TRY_COMMISERATION, transform2, dual_side);
			if (bg_cutscenes_enabled)
			{
				AddBackgroundCinematicTLE(CSEVENT_TRY_CELEBRATION_BG, transform_bg, side, 0.0f, 0.0f);
				AddBackgroundCinematicTLE(CSEVENT_TRY_COMMISERATION_BG, transform2_bg, dual_side, 0.0f, 0.0f);
			}
		}
		else
		{
			AddCinematicTLE(CSEVENT_TRY_COMMISERATION, transform2, dual_side);
			AddCinematicTLE(CSEVENT_TRY_CELEBRATION, transform, side);
			if (bg_cutscenes_enabled)
			{
				AddBackgroundCinematicTLE(CSEVENT_TRY_COMMISERATION_BG, transform2_bg, dual_side, 0.0f, 0.0f);
				AddBackgroundCinematicTLE(CSEVENT_TRY_CELEBRATION_BG, transform_bg, side, 0.0f, 0.0f);
			}
		}

		AddCinematicTLE(CSEVENT_FIREWORKS_TRY, nr, SIDE_NONE);

		AddUserSkipPointElement();

		if (replays_enabled)
		{
			AddCutsceneReplayElement(NULL, REPLAY_CAMERA_MODE_MAIN, TRY_MAX_REWIND_TIME, RT_TRY, SWIPE_WIPE, NUM_REPLAYS_TRY);
			AddUserSkipPointElement();
		}
	}
	else
	{
		//AddCutsceneDelayElement(30); //dont enable

		if (replays_enabled)
		{
			AddCutsceneReplayElement(NULL, REPLAY_CAMERA_MODE_MAIN, TRY_MAX_REWIND_TIME, RT_TRY, SWIPE_WIPE, NUM_REPLAYS_TRY);
			AddUserSkipPointElement();
		}
	}

	if (game->GetGameTimer()->GetExtraTimeMode() != GOLDEN_POINT)
	{
		if (cinematics_enabled)
		{
			transform2_bg = rot_transform * transform2_bg;

			ARugbyCharacter* goal_kicker = player->GetAttributes()->GetTeam()->GetGoalKicker();
			if (player != goal_kicker)		// Don't do try reaction if he's the goal kicker.
			{
				MabMatrix reaction_transform;

				cspos.z = TRY_REACTION_Z_POSITION * field_direction;
				reaction_transform = nr * MabMatrix::TransMatrix(cspos);

				cspos.x = 0.0f;
				cspos.z = TRY_REACTION_POSITIVE_BG_POSITION * field_direction;
				transform_bg = nr * rot_transform * MabMatrix::TransMatrix(cspos);

				AddCutsceneCinematicElement(RUGamePhase::TRY_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);						// NOT SURE!!!
				AddCinematicTLE(CSEVENT_TRY_REACTION, reaction_transform, side);

				/// Setup non actor cutscenes.
				if (bg_cutscenes_enabled)
				{
					AddBackgroundCinematicTLE(CSEVENT_GENERIC_REACTION_POSITIVE_BG, transform_bg, side, 0.0f, 0.0f);
					AddBackgroundCinematicTLE(CSEVENT_GENERIC_REACTION_NEGATIVE_BG, transform2_bg, dual_side, 0.0f, 0.0f);
				}
			}

			FVector position = game->GetGameState()->GetPlayRestartPosition();
			MabMatrix kfp_transform = nr * MabMatrix::TransMatrix(position);

			position.x = 0.0f;
			position.z = TRY_REACTION_KICKFORPOINTS_POSITIVE_BG_POSITION * field_direction;
			transform_bg = nr * rot_transform * MabMatrix::TransMatrix(position);

			AddHUDInfoCallbackElement(goal_kicker->GetAttributes()->GetDbId(), "[ID_CONVERSION_KICKER]", goal_kicker->GetAttributes()->GetTeam());
			AddCutsceneCallbackElement(SSCutSceneManager::ConversionBallPlacementCallback, CONVERSION_BALL_PLACEMENT_CALLBACK);
			AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);				// pre-kick for points - ok to show.

			AddCinematicTLE(CSEVENT_PRE_KICKFORPOINTS, kfp_transform, SIDE_NONE);

			/// Setup non actor cutscenes.
			if (bg_cutscenes_enabled)
			{
				AddBackgroundCinematicTLE(CSEVENT_GENERIC_REACTION_POSITIVE_BG, transform_bg, side, 0.0f, 0.0f);
				AddBackgroundCinematicTLE(CSEVENT_GENERIC_REACTION_NEGATIVE_BG, transform2_bg, dual_side, 0.0f, 0.0f);
			}

			AddUserSkipPointElement();
		}

		AddCutsceneCallbackElement(SSCutSceneManager::StartConversionPhaseCallback, START_CONVERSION_PHASE_CALLBACK);
	}

	AddCutsceneCallbackElement(SSCutSceneManager::PopMusicCallback, POP_MUSIC_CALLBACK);
	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);

}

///-------------------------------------------------------------------------------
/// Launch video ref. (game_events->video_referee_deliberation)
///-------------------------------------------------------------------------------

static const float TRY_TMO_CELEBRATION_Z_POSITION = 50.0f - 15.0f;
static const float TRY_TMO_COMMISERATION_Z_POSITION = 55.0f;
static const float TRY_TMO_REFEREE_Z_POSITION = 47.0f;

void SSCutSceneManager::LaunchTMOCutscene()
{
	ARugbyCharacter* player = game->GetGameState()->GetLastTryAttempter();
	MABASSERT(player != NULL);
	if (!player)
		return;

	if (!game->IsMatch())
		return;

	///---------------------
	// turn off game rules.

	game->GetRules()->EnableConsequences(false);
	game->GetRules()->EnableTriggers(RURT_NONE);

	///---------------------

	SetCelebratingTeam(player->GetAttributes()->GetTeam());
	SetFocusPlayer(player, player->GetAttributes()->GetTeam()->GetSide());

	SSTEAMSIDE side = GetCelebratingTeamSide();

	// Calculate cutscene transform.

	FVector cspos(0, 0, 0);
	cspos = player->GetMovement()->GetCurrentPosition();
	cspos.y = 0.0f;

	float field_direction = (cspos.z<0.0f) ? -1.0f : 1.0f;

	MabMatrix nr = MabMatrix::IDENTITY;
	if (cspos.z<0.0f)
		nr = MabMatrix::RotMatrixY(PI);

	//-------------------------
	// Decide wether to award try or not.

	float probability = game->GetGameState()->GetVideoRefTryProbability();

	bool award_try = game->GetRNG()->RAND_RANGED_CALL(float, 1.0f)<probability;

	/// Swap celebrating team if not awarding try - do here so SetupBGReactionCutscenes works.
	if (!award_try) 
	{
		SetCelebratingTeam((RUTeam*)player->GetAttributes()->GetTeam()->GetOppositionTeam());
	}

	//-------------------------

	game->GetGameState()->SetPhase(RUGamePhase::TRY_REACTION);

	AddCutsceneAsyncPreloadElement(false);
	AddCutsceneDelayElement(90);	//30);
	AddScreenWipeElement(SWIPE_CUT);
	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	if(cinematics_enabled)		// Always show TMO, so user knows!
	{
		MabMatrix transform = MabMatrix::IDENTITY;

		cspos.x = game->GetGameState()->GetTryAttemptPosition().x;
		cspos.z = TRY_TMO_REFEREE_Z_POSITION * field_direction;
		transform = nr * MabMatrix::TransMatrix(cspos);

		ClampTransform(transform, CUTSCENE_SAFETY_CLAMP_DISTANCE);

		AddHUDInfoCallbackElement(0, "[ID_TMO_TITLE]", NULL, "[ID_DECISION_PENDING]");
		AddCutsceneCinematicElement(RUGamePhase::TRY_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
		AddCinematicTLE(CSEVENT_TRY_TMO, transform, side);

		/// Setup background cutscenes for 'non-actors'.

		cspos = transform.TransformPos(cspos);
		SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

		AddUserSkipPointElement();

		if (replays_enabled)
		{
			AddCutsceneReplayElement(NULL, REPLAY_CAMERA_MODE_MAIN, TRY_MAX_REWIND_TIME, RT_VIDEO_REF, SWIPE_WIPE, NUM_REPLAYS_TMO);
			AddUserSkipPointElement();
		}
	}
	else  // cinematics disabled.
	{
		AddHUDInfoCallbackElement(0, "[ID_TMO_TITLE]", NULL, "[ID_DECISION_PENDING]");
	}

	{
		MabMatrix transform = MabMatrix::IDENTITY;
		MabMatrix transform2 = MabMatrix::IDENTITY;
		MabMatrix transform_bg = MabMatrix::IDENTITY;
		MabMatrix transform2_bg = MabMatrix::IDENTITY;
		FVector brpos;

		cspos.z = TRY_TMO_CELEBRATION_Z_POSITION * field_direction;
		transform = nr * MabMatrix::TransMatrix(cspos);
		brpos = cspos;

		cspos.x = 0.0f;			// Always run commiseration between posts.
		cspos.z = TRY_TMO_COMMISERATION_Z_POSITION * field_direction;
		transform2 = nr * MabMatrix::TransMatrix(cspos);

		cspos.x = 0.0f;		// for clarity as done above.
		cspos.z = TRY_CELEBRATION_Z_BG_POSITION * field_direction;
		transform_bg = nr * MabMatrix::TransMatrix(cspos);

		cspos.x = 0.0f;		// for clarity as done above.
		cspos.z = TRY_COMMISERATION_BG_Z_POSITION * field_direction;
		transform2_bg = nr * MabMatrix::TransMatrix(cspos);

		ClampTransform(transform, CUTSCENE_SAFETY_CLAMP_DISTANCE);
		ClampTransform(transform2, CUTSCENE_SAFETY_CLAMP_DISTANCE);
		ClampTransform(transform_bg, CUTSCENE_SAFETY_CLAMP_DISTANCE);
		ClampTransform(transform2_bg, CUTSCENE_SAFETY_CLAMP_DISTANCE);


		if (award_try)
		{
			AddCutsceneCallbackElement(SSCutSceneManager::AwardTryAfterTMOCallback, AWARD_TRY_TMO_CALLBACK);

			if (cinematics_enabled)
			{
				SSTEAMSIDE dual_side = (side == SIDE_A) ? SIDE_B : SIDE_A;

				AddCutsceneCallbackElement(SSCutSceneManager::StartTryCutscenePhaseCallback, START_TRY_PHASE_CALLBACK);
				bool should_play_music = RUCrowdAudioReactionManager::ChanceOfMusic(game->GetGameSettings()) > game->GetRNG()->RAND_CALL(float);
				AddCutsceneCallbackElement(SSCutSceneManager::PushMusicCallback, PUSH_MUSIC_CALLBACK,
					!should_play_music ? 0 : (int)SIFApplication::GetApplication()->GetRUAudio()->GetEventID("event:/music/try_score_music"));
				
				AddCutsceneCinematicElement(RUGamePhase::TRY_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
				if (player->GetAttributes()->GetTeam()->GetNumHumanPlayers()>0)
				{
					AddCinematicTLE(CSEVENT_TRY_CELEBRATION, transform, side);
					AddCinematicTLE(CSEVENT_TRY_COMMISERATION, transform2, dual_side);
					if (bg_cutscenes_enabled)
					{
						AddBackgroundCinematicTLE(CSEVENT_TRY_CELEBRATION_BG, transform, side, 0.0f, 0.0f);
						AddBackgroundCinematicTLE(CSEVENT_TRY_COMMISERATION_BG, transform2_bg, dual_side, 0.0f, 0.0f);
					}
				}
				else
				{
					AddCinematicTLE(CSEVENT_TRY_COMMISERATION, transform2, dual_side);
					AddCinematicTLE(CSEVENT_TRY_CELEBRATION, transform, side);
					if (bg_cutscenes_enabled)
					{
						AddBackgroundCinematicTLE(CSEVENT_TRY_COMMISERATION_BG, transform2_bg, dual_side, 0.0f, 0.0f);
						AddBackgroundCinematicTLE(CSEVENT_TRY_CELEBRATION_BG, transform_bg, side, 0.0f, 0.0f);
					}
				}
				AddCinematicTLE(CSEVENT_FIREWORKS_TRY, nr, SIDE_NONE);

				AddUserSkipPointElement();
			}

			AddCutsceneCallbackElement(SSCutSceneManager::StartConversionPhaseCallback, START_CONVERSION_PHASE_CALLBACK);
			AddCutsceneCallbackElement(SSCutSceneManager::PopMusicCallback, POP_MUSIC_CALLBACK);
			AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);
		}
		else
		{	/// Try disallowed.

			AddHUDInfoCallbackElement(0, "[ID_5M_SCRUM_AWARDED]", NULL, "[ID_NO_TRY]");

			AddCutsceneCallbackElement(SSCutSceneManager::CommentaryTMOTryDisallowed, COMMENTARY_TMO_TRY_DISALLOWED);

			if (cinematics_enabled)
			{
				AddCutsceneCinematicElement(RUGamePhase::TRY_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
				AddCinematicTLE(CSEVENT_GENERIC_REACTION_NEGATIVE, transform, side);

				SetupBGReactionCutscenes(brpos, 6.0f, 10.0f);

				AddUserSkipPointElement();
			}

			AddCutsceneCallbackElement(SSCutSceneManager::DisallowTryAfterTMOCallback, DISALLOW_TRY_TMO_CALLBACK);
			AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);
		}
	}
}




///-------------------------------------------------------------------------------
/// Called on event 'game_events->penalty_detected'
///-------------------------------------------------------------------------------

void SSCutSceneManager::LaunchPenaltyCutscene(ARugbyCharacter* offender, ARugbyCharacter* /*offended_player*/, const FVector& position, PENALTY_REASON reason, SUSPENSION_RESULT suspension_result)
{
	MabMatrix transform_restart = MabMatrix::TransMatrix(position);
	MabMatrix transform_offending;

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	RUTeam *commiserating_team = (RUTeam*)game->GetGameState()->GetPlayRestartTeam()->GetOppositionTeam();

	/// Check for suspension (red/yellow card).
	/// N.B. Only tackler gets put into tackle_result.tacklers.

	bool suspend_player = false;
	bool is_second_yellow_card = false;

	/// Check for second yellow card.
	if (suspension_result == SUSPENSION_YELLOW_CARD)
	{
#ifdef ENABLE_GAME_DEBUG_MENU
		if (SIFDebug::GetRulesDebugSettings()->IsYellowCardIsSecondYellowCard())
			is_second_yellow_card = true;
#endif

		RUStatisticsSystem *statistics_system = SIFApplication::GetApplication()->GetStatisticsSystem();
		int total_yellow_cards = statistics_system->GetCurrentMatchStat(offender, &RUDB_STATS_PLAYER::yellow_cards);
		if (total_yellow_cards>1)
			is_second_yellow_card = true;
	}

	/// Apply custom rules.

	if ((suspension_result == SUSPENSION_RED_CARD || (suspension_result == SUSPENSION_YELLOW_CARD && is_second_yellow_card))
		&& !game->GetGameSettings().game_settings.custom_rule_sendoffs_enabled)
	{
		suspension_result = SUSPENSION_NONE;
	}

	if (suspension_result == SUSPENSION_YELLOW_CARD && !game->GetGameSettings().game_settings.custom_rule_sinbins_enabled)
	{
		suspension_result = SUSPENSION_NONE;
	}

	// Do send offs/sinbins here....


	if (suspension_result != SUSPENSION_NONE)
	{
		game->GetGameState()->NotifySuspension(offender, suspension_result);

		if (suspension_result == SUSPENSION_RED_CARD || (suspension_result == SUSPENSION_YELLOW_CARD && is_second_yellow_card))
		{
			suspend_player = true;
		}

		RUInterchangeEvent interchange_event(commiserating_team, offender, offender->GetAttributes()->GetDbId(), -1, suspend_player ? RU_INTERCHANGE_SENTOFF : RU_INTERCHANGE_SINBIN_OFF);
		game->GetSubstitutionManager()->AddInterchangeEvent(interchange_event);

		// Show HUD.
		AddCutsceneCallbackElement(SSCutSceneManager::ShowInterchangeHUDCallback, SHOW_INTERCHANGE_HUD_CALLBACK);
	}

	if (cinematics_enabled)
	{
		AddCutsceneAsyncPreloadElement(false);
		AddScreenWipeElement(SWIPE_CUT);

		// Setup filters for cutscene.

		RUGameState *game_state = game->GetGameState();

		MABLOGDEBUG("LaunchPenaltyCutscene");

		game_state->ResetPhases(true);
		game_state->SetPhase(RUGamePhase::REACTION_CUTSCENE);

		RUTeam *restart_team = game_state->GetPlayRestartTeam();
		MABASSERT(restart_team != NULL);

		MABASSERT(offender != NULL);
		MABASSERT(commiserating_team != restart_team);

		SetCelebratingTeam(restart_team);
		SetFocusPlayer(offender, commiserating_team->GetSide());

		ARugbyCharacter* celebrating_player = game->GetSpatialHelper()->FindClosestPlayer(offender, &restart_team->GetPlayers());
		MABASSERT(celebrating_player != NULL);
		SetFocusPlayer(celebrating_player, restart_team->GetSide());

		MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);

		if (restart_team->GetPlayDirection() == ERugbyPlayDirection::NORTH)
			transform_restart = rot_transform * transform_restart;

		transform_offending = rot_transform * transform_restart;

		SSTEAMSIDE restart_side = restart_team->GetSide();
		SSTEAMSIDE offending_side = (restart_side == SIDE_A) ? SIDE_B : SIDE_A;

		ClampTransform(transform_offending, CUTSCENE_SAFETY_CLAMP_DISTANCE);

		if (reason != PENALTY_REASON_SLOW_LINEOUT)
		{
			OffsetNegativeReaction(transform_restart, NEGATIVE_REACTION_DISTANCE);
		}
		ClampTransform(transform_restart, CUTSCENE_SAFETY_CLAMP_DISTANCE);

		SWIPE_TYPE end_swipe = (replays_enabled && reason != PENALTY_REASON_SLOW_LINEOUT) ? SWIPE_WIPE : SWIPE_CUT;
		if (suspension_result == SUSPENSION_RED_CARD)
		{
			AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, end_swipe, HIDE_NON_ACTORS);
			AddCinematicTLE(CSEVENT_RED_CARD, transform_offending, offending_side);
		}
		else if (suspension_result == SUSPENSION_YELLOW_CARD)
		{
			if (is_second_yellow_card)
			{
				AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, end_swipe, HIDE_NON_ACTORS);
				AddCinematicTLE(CSEVENT_DOUBLE_YELLOW_CARD, transform_offending, offending_side);
			}
			else
			{
				AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, end_swipe, HIDE_NON_ACTORS);
				AddCinematicTLE(CSEVENT_YELLOW_CARD, transform_offending, offending_side);
			}
		}
		else
		{
			AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, end_swipe, HIDE_NON_ACTORS);
			AddCinematicTLE(CSEVENT_GENERIC_REACTION_NEGATIVE, transform_restart, offending_side);
			AddCinematicTLE(CSEVENT_REFEREE_PENALTY, transform_offending, SIDE_NONE);
		}

		/// Setup background cutscenes for 'non-actors'.

		FVector cspos(0, 0, 0);
		cspos = transform_offending.TransformPos(cspos);
		SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION + NEGATIVE_REACTION_DISTANCE);

		//--------------------

		AddUserSkipPointElement();

		if (replays_enabled && reason != PENALTY_REASON_SLOW_LINEOUT)
		{
			AddInfringementReplayElement(PENALTY_MAX_REWIND_TIME, PENALTY_DURATION, RT_PENALTY);
			//AddUserSkipPointElement();
		}
	}
	else
	{
		if (replays_enabled && reason != PENALTY_REASON_SLOW_LINEOUT)
		{
			//AddScreenWipeElement(SWIPE_WIPE);
			AddInfringementReplayElement(PENALTY_MAX_REWIND_TIME, PENALTY_DURATION, RT_PENALTY);
			//AddUserSkipPointElement();
		}
	}

	/// Do a substitution if one is pending.
	if (game->GetSubstitutionManager()->GetNumQueuedEvents() > 0)
	{
		MABLOGDEBUG("%s: Setting up interchange", __FUNCTION__);
		SetupInterchange();
	}

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);

	// check if our pro was sent off, if he was then don't care about the penalty callback, do a pro sent off callback instead
	if (pro_player_sent_off_during_interchanges)
	{
		AddCutsceneCallbackElement(SSCutSceneManager::ProSentOffCallback, PRO_SENT_OFF_CALLBACK);
		suspended_callback_from_pro_send_off = SSCutSceneManager::PenaltyCallback;
		suspended_callback_from_pro_send_off_debug_name = PENALTY_CALLBACK;
	}
	else
		AddCutsceneCallbackElement(SSCutSceneManager::PenaltyCallback, PENALTY_CALLBACK);	// Must be called after 'CutsceneEndCallback' as rules need to be enabled.
}

///-------------------------------------------------------------------------------
/// Called on event 'game_events->cutscene_forward_pass'
///-------------------------------------------------------------------------------

void SSCutSceneManager::ForwardPassCutsceneBegin(RUTeam* offending_team)
{
	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	if (!game->GetGameTimer()->ScrumCancelledByHalfOver() && !game->GetGameState()->IsFifthTackle())
	{
		MabString teamName = offending_team->GetDbTeam().GetShortName();
		RUHUDUpdater::CensorTeamName(&offending_team->GetDbTeam(), teamName);
		
		AddHUDInfoCallbackElement(0, "[ID_SCRUM_AWARDED]", offending_team, MabString(0, "[ID_FORWARD_PASS_BY] %s", teamName.c_str()));
	}
	else if (!game->GetGameTimer()->ScrumCancelledByHalfOver() && game->GetGameState()->IsFifthTackle())
	{
		MabString teamName = offending_team->GetDbTeam().GetShortName();
		RUHUDUpdater::CensorTeamName(&offending_team->GetDbTeam(), teamName);

		AddHUDInfoCallbackElement(0, "[ID_HANDOVER_SIGNALLED]", offending_team, MabString(0, "[ID_FORWARD_PASS_BY] %s", teamName.c_str()));
	}

	if (cinematics_enabled)
	{
		MabMatrix cutscene_transform = MabMatrix::IDENTITY;
		MabMatrix rot_matrix = MabMatrix::RotMatrixY(PI);

		// rotate the cutscene to face the direction the team who forward passed
		if (offending_team->GetPlayDirection() == ERugbyPlayDirection::SOUTH)
			cutscene_transform = cutscene_transform * rot_matrix;

		cutscene_transform.SetTranslation(game->GetStrategyHelper()->GetLastBallFreeInfo().pos);

		ClampTransform(cutscene_transform, CUTSCENE_SAFETY_CLAMP_DISTANCE);

		SetCelebratingTeam(game->GetGameState()->GetPlayRestartTeam());

		// look at the last guy to pass
		ARugbyCharacter* bad_passer = game->GetStrategyHelper()->GetLastBallFreeInfo().last_player;
		
		ensureAlways(bad_passer->GetAttributes()->GetTeam() == offending_team);
		
		ensureAlways(game->GetGameState()->GetPlayRestartTeam() != offending_team);

		//this is a scenerio where the the badpasser is not the same as the last_ball_free_info.last_player (player who passed the ball) since last_player may change when PlayRestartTeam has an advantage
		//so better we use the GetLastOffendingPlayer.
		if (bad_passer && bad_passer->GetAttributes()->GetTeam() != offending_team)
		{
			bad_passer = game->GetGameState()->GetLastOffendingPlayer();
		}

		//in case we have no bad_passer or bad_passer doesnot belong to offending_team, dont set the focus player, cutscene logic will pick someone randomly from the offending_team
		if (bad_passer && (bad_passer->GetAttributes()->GetTeam() == offending_team))
		{
			SetFocusPlayer(bad_passer, offending_team->GetSide());
		}

		AddScreenWipeElement(SWIPE_CUT);
		AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, replays_enabled ? SWIPE_WIPE : SWIPE_CUT, HIDE_NON_ACTORS);
		AddCinematicTLE(CSEVENT_GENERIC_REACTION_NEGATIVE, cutscene_transform, offending_team->GetSide());

		/// Setup background cutscenes for 'non-actors'.

		FVector cspos(0, 0, 0);
		cspos = cutscene_transform.TransformPos(cspos);
		SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

		//--------------------

		AddUserSkipPointElement();

		if (replays_enabled)
		{
			AddInfringementReplayElement(FORWARD_PASS_MAX_REWIND_TIME, FORWARD_PASS_DURATION, RT_FORWARD_PASS);
			//AddUserSkipPointElement();
		}
	}
	else
	{
		if (replays_enabled)
		{
			//AddScreenWipeElement(SWIPE_WIPE);
			AddInfringementReplayElement(FORWARD_PASS_MAX_REWIND_TIME, FORWARD_PASS_DURATION, RT_FORWARD_PASS);
			//AddUserSkipPointElement();
		}
		else
		{
			// no replays, no cutscenes do a fade while the scrum sets up
			AddScreenWipeElement(SWIPE_CUT);
		}
	}

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);
	AddCutsceneCallbackElement(SSCutSceneManager::ForwardPassCallback, FORWARD_PASS_CALLBACK);		// Must be called after 'CutsceneEndCallback' as rules need to be enabled.

}

///-------------------------------------------------------------------------------
/// Handles storing the replay for advantage
///-------------------------------------------------------------------------------
void SSCutSceneManager::AdvantageStart(ARugbyCharacter*)
{
	advantage_last_started_time = game->GetSimTime()->GetAbsoluteTime().ToSeconds();
	/// TYRONE : These were disconnected but need this method now
	//game->GetEvents()->replay_saved.Add(this, &SSCutSceneManager::ReplaySaved);
	//game->GetReplayManager()->SaveReplayOnNextUpdate( ADVANTAGE_REPLAY_LENGTH, 2.0f );
}

void SSCutSceneManager::AdvantageEnd(ARugbyCharacter*)
{
	//advantage_end_countdown = 15; //Horrible horrible hack full of dangers and fears - ABROWN
	//game->GetEvents()->replay_saved.Remove(this, &SSCutSceneManager::ReplaySaved);
}

void SSCutSceneManager::ReplaySaved(SSReplayBlock* replay)
{
	// Always keep advantage replay... because it keeps alloc/free nice & neat.
	// Use bool to decide whether to use advantage replay in infraction
	if (advantage_replay) game->GetReplayManager()->FreeSavedReplay(advantage_replay);
	advantage_replay = replay;

	advantage_inaffect = (advantage_replay != NULL);
	game->GetEvents()->replay_saved.Remove(this, &SSCutSceneManager::ReplaySaved);
}

///-------------------------------------------------------------------------------
/// Called on event 'game_events->mark_awarded'
///-------------------------------------------------------------------------------

void SSCutSceneManager::LaunchMarkAwardedCutscene(const FVector& position, ARugbyCharacter* marking_player)
{
	MABLOGDEBUG("LaunchMarkAwardedCutscene");

	MabMatrix transform_restart = MabMatrix::TransMatrix(position);
	MabMatrix transform_marking_player;

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	if (cinematics_enabled)
	{
		AddCutsceneAsyncPreloadElement(false);
		AddScreenWipeElement(SWIPE_CUT);

		// Setup filters for cutscene.

		RUGameState *game_state = game->GetGameState();

		game_state->ResetPhases(true);
		game_state->SetPhase(RUGamePhase::REACTION_CUTSCENE);

		RUTeam *restart_team = game_state->GetPlayRestartTeam();
		MABASSERT(restart_team != NULL);
		SetCelebratingTeam(restart_team);
		SetFocusPlayer(marking_player, marking_player->GetAttributes()->GetTeam()->GetSide());

		MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);

		if (restart_team->GetPlayDirection() == ERugbyPlayDirection::NORTH)
			transform_restart = rot_transform * transform_restart;

		transform_marking_player = rot_transform * transform_restart;

		ClampTransform(transform_marking_player, CUTSCENE_SAFETY_CLAMP_DISTANCE);

		AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
		AddCinematicTLE(CSEVENT_TUTORIAL_WIN, transform_marking_player, restart_team->GetSide());

		/// Setup background cutscenes for 'non-actors'.

		FVector cspos(0, 0, 0);
		cspos = transform_marking_player.TransformPos(cspos);
		SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

		//AddCutsceneCinematicElement(CSEVENT_MARK_AWARDED, transform_offending, marking_player, ENABLE_SIMULATION, ENABLE_SKIP, SWIPE_FADE);

		AddUserSkipPointElement();
	}
	else
	{
		AddScreenWipeElement(SWIPE_CUT);
		AddScreenWipeElement(SWIPE_CUT);
	}

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);
	AddCutsceneCallbackElement(SSCutSceneManager::MarkAwardedCallback, MARK_AWARDED_CALLBACK);		// Must be called after 'CutsceneEndCallback' as rules need to be enabled.
}

///-------------------------------------------------------------------------------
/// Called on event 'game_events->fourtytwenty_kick_awarded'
///-------------------------------------------------------------------------------
#pragma optimize ("", off)
void SSCutSceneManager::LaunchFourtyTwentyKickAwardedCutscene(const FVector& position, ARugbyCharacter* kicking_player, const KickContext& kickContext)
{
	MABLOGDEBUG("LaunchFTAwarrdCutscene");

	MabMatrix transform_restart = MabMatrix::TransMatrix(position);
	MabMatrix transform_kicking_player;

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	if (cinematics_enabled)
	{
		AddCutsceneAsyncPreloadElement(false);
		AddScreenWipeElement(SWIPE_CUT);

		// Setup filters for cutscene.

		RUGameState* game_state = game->GetGameState();

		game_state->ResetPhases(true);
		game_state->SetPhase(RUGamePhase::REACTION_CUTSCENE);

		RUTeam* restart_team = game_state->GetPlayRestartTeam();
		MABASSERT(restart_team != NULL);
		SetCelebratingTeam(restart_team);
		SetFocusPlayer(kicking_player, kicking_player->GetAttributes()->GetTeam()->GetSide());

		MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);

		if (restart_team->GetPlayDirection() == ERugbyPlayDirection::NORTH)
			transform_restart = rot_transform * transform_restart;

		transform_kicking_player = rot_transform * transform_restart;

		ClampTransform(transform_kicking_player, CUTSCENE_SAFETY_CLAMP_DISTANCE);

		if (replays_enabled)
		{
			AddCutsceneReplayElement(NULL, REPLAY_CAMERA_MODE_MAIN, SUCCESSFUL_4020_KICK_TIME, RT_TRY, SWIPE_WIPE, NUM_REPLAYS_TRY);
			AddUserSkipPointElement();
		}
		// TODO calc position here to see if we are 4020 or 2040 ? then assign ID_.._AWARDED
		MabString context = kickContext == KickContext::KC_FOURTYTWENTY ? "[ID_4020_AWARDED]" : "[ID_2040_AWARDED]";
		AddHUDInfoCallbackElement(kicking_player->GetAttributes()->GetDbId(), context, kicking_player->GetAttributes()->GetTeam());
		AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
		AddCinematicTLE(CSEVENT_BALLOUT_POSITIVE, transform_kicking_player, restart_team->GetSide());

		/// Setup background cutscenes for 'non-actors'.

		FVector cspos(0, 0, 0);
		cspos = transform_kicking_player.TransformPos(cspos);
		SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

		//AddCutsceneCinematicElement(CSEVENT_MARK_AWARDED, transform_offending, marking_player, ENABLE_SIMULATION, ENABLE_SKIP, SWIPE_FADE);

		AddUserSkipPointElement();
	}
	else
	{
		AddScreenWipeElement(SWIPE_CUT);
	}

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);
	AddCutsceneCallbackElement(SSCutSceneManager::FourtyTwentyKickAwardedCallback, FOURTYTWENTY_AWARDED_CALLBACK);		// Must be called after 'CutsceneEndCallback' as rules need to be enabled.
}
#pragma optimize ("", on)
///-------------------------------------------------------------------------------
/// Called on event 'game_events->restart_kickoff_offside'
///-------------------------------------------------------------------------------

void SSCutSceneManager::LaunchFreeKickOffsideKickOffCutscene(ARugbyCharacter* restart_out_causing_player)
{
	MABBREAKMSG("Not being used anymore?");
	MABLOGDEBUG("LaunchFreeKickOffsideKickOffCutscene");

	BallFreeInfo bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	FVector ball_pos = bfi.pos;
	ball_pos.y = 0.0f;			// Lets keep the cutscene on the ground!
	MabMatrix transform_restart = MabMatrix::TransMatrix(ball_pos);
	MabMatrix transform_causing_player;

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	if (cinematics_enabled)
	{
		AddCutsceneAsyncPreloadElement(false);
		AddScreenWipeElement(SWIPE_CUT);

		// Setup filters for cutscene.
		RUGameState *game_state = game->GetGameState();

		game_state->ResetPhases(true);
		game_state->SetPhase(RUGamePhase::REACTION_CUTSCENE);

		RUTeam *offending_team = restart_out_causing_player->GetAttributes()->GetTeam();
		RUTeam *restart_team = game_state->GetPlayRestartTeam();
		MABASSERT(restart_team != NULL);
		SetCelebratingTeam(static_cast< RUTeam*>(restart_out_causing_player->GetAttributes()->GetTeam()->GetOppositionTeam()));
		SetFocusPlayer(restart_out_causing_player, restart_out_causing_player->GetAttributes()->GetTeam()->GetSide());

		MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);

		if (restart_team->GetPlayDirection() == ERugbyPlayDirection::NORTH)
			transform_restart = rot_transform * transform_restart;

		transform_causing_player = transform_restart;	//rot_transform * transform_restart;

		OffsetNegativeReaction(transform_causing_player, NEGATIVE_REACTION_DISTANCE);
		ClampTransform(transform_causing_player, CUTSCENE_SAFETY_CLAMP_DISTANCE);

		AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
		AddCinematicTLE(CSEVENT_GENERIC_REACTION_NEGATIVE, transform_causing_player, offending_team->GetSide());

		/// Setup background cutscenes for 'non-actors'.

		FVector cspos(0, 0, 0);
		cspos = transform_causing_player.TransformPos(cspos);
		SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

		//AddCutsceneCinematicElement(CSEVENT_MARK_AWARDED, transform_offending, marking_player, ENABLE_SIMULATION, ENABLE_SKIP, SWIPE_FADE);

		AddUserSkipPointElement();
	}
	else
	{
		AddScreenWipeElement(SWIPE_CUT);
	}

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_START_CALLBACK);
	AddCutsceneCallbackElement(SSCutSceneManager::RestartFreeKickR7KickOffPenaltyCallback, RESTART_ONFULL_CALLBACK);	// MUST be called after 'CutsceneEndCallback' as rules need to be enabled.
}

#ifdef ENABLE_SEVENS_MODE
///-------------------------------------------------------------------------------
/// Called on event 'game_events->restart_not_10m'
///-------------------------------------------------------------------------------

void SSCutSceneManager::LaunchFreeKickCollected10mCutscene(ARugbyCharacter* restart_out_causing_player)
{
	MABLOGDEBUG("LaunchFreeKickCollected10mCutscene");

	//We dont want these cutscenes for R15 games
	/*if(game->GetGameSettings().game_settings.GetGameMode() != GAME_MODE_SEVENS)
	{
		return;
	}*/

	BallFreeInfo bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	FVector ball_pos = bfi.pos;
	ball_pos.y = 0.0f;			// Lets keep the cutscene on the ground!
	MabMatrix transform_restart = MabMatrix::TransMatrix( ball_pos );
	MabMatrix transform_causig_player;

	AddCutsceneCallbackElement( SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK );

	if(cinematics_enabled)
	{
		AddCutsceneAsyncPreloadElement(false);
		AddScreenWipeElement(SWIPE_CUT);

		// Setup filters for cutscene.

		RUGameState *game_state = game->GetGameState();

		game_state->ResetPhases(true);
		game_state->SetPhase(RUGamePhase::REACTION_CUTSCENE );

		RUTeam *offending_team = restart_out_causing_player->GetAttributes()->GetTeam();
		RUTeam *restart_team = game_state->GetPlayRestartTeam();
		MABASSERT(restart_team!=NULL);
		SetCelebratingTeam( static_cast< RUTeam*>( restart_out_causing_player->GetAttributes()->GetTeam()->GetOppositionTeam() ) );
		SetFocusPlayer(restart_out_causing_player, restart_out_causing_player->GetAttributes()->GetTeam()->GetSide());

		MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);

		if (restart_team->GetPlayDirection() == ERugbyPlayDirection::NORTH)
			transform_restart = rot_transform * transform_restart;

		transform_causig_player = transform_restart;	//rot_transform * transform_restart;

		OffsetNegativeReaction(transform_causig_player, NEGATIVE_REACTION_DISTANCE);
		ClampTransform(transform_causig_player, CUTSCENE_SAFETY_CLAMP_DISTANCE);

		AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
		AddCinematicTLE(CSEVENT_GENERIC_REACTION_NEGATIVE, transform_causig_player, offending_team->GetSide());

		/// Setup background cutscenes for 'non-actors'.

		FVector cspos(0,0,0);
		cspos = transform_causig_player.TransformPos(cspos);
		SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION );

		//AddCutsceneCinematicElement(CSEVENT_MARK_AWARDED, transform_offending, marking_player, ENABLE_SIMULATION, ENABLE_SKIP, SWIPE_FADE);

		AddUserSkipPointElement();
	}
	else
	{
		AddScreenWipeElement(SWIPE_CUT);
	}

	AddCutsceneCallbackElement( SSCutSceneManager::CutsceneEndCallback, CUTSCENE_START_CALLBACK );
	AddCutsceneCallbackElement(SSCutSceneManager::RestartFreeKickR7KickOffPenaltyCallback, RESTART_ONFULL_CALLBACK );	// MUST be called after 'CutsceneEndCallback' as rules need to be enabled.
	//AddCutsceneCallbackElement(SSCutSceneManager::RestartOutOnFullCallback, RESTART_ONFULL_CALLBACK );	// MUST be called after 'CutsceneEndCallback' as rules need to be enabled.
	//RestartFreeKickR7KickOffPenaltyCallback





	//We dont want these cutscenes for R15 games
	//if(game->GetGameSettings().GetGameMode() != GAME_MODE_SEVENS)
	//return;

	/*MABBREAKMSG("Not being used anymore?");
	MABLOGDEBUG("LaunchFreeKickCollected10mCutscene");

	BallFreeInfo bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	FVector ball_pos = bfi.pos;
	ball_pos.y = 0.0f;			// Lets keep the cutscene on the ground!
	MabMatrix transform_restart = MabMatrix::TransMatrix(ball_pos);
	MabMatrix transform_causing_player;

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	if (cinematics_enabled)
	{
		AddCutsceneAsyncPreloadElement(false);
		AddScreenWipeElement(SWIPE_CUT);

		// Setup filters for cutscene.
		RUGameState *game_state = game->GetGameState();

		game_state->ResetPhases(true);
		game_state->SetPhase(RUGamePhase::REACTION_CUTSCENE);

		RUTeam *offending_team = restart_out_causing_player->GetAttributes()->GetTeam();
		RUTeam *restart_team = game_state->GetPlayRestartTeam();
		MABASSERT(restart_team != NULL);
		SetCelebratingTeam(static_cast< RUTeam*>(restart_out_causing_player->GetAttributes()->GetTeam()->GetOppositionTeam()));
		SetFocusPlayer(restart_out_causing_player, restart_out_causing_player->GetAttributes()->GetTeam()->GetSide());

		MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);

		if (restart_team->GetPlayDirection()>0)
			transform_restart = rot_transform * transform_restart;

		transform_causing_player = transform_restart;	//rot_transform * transform_restart;

		OffsetNegativeReaction(transform_causing_player, NEGATIVE_REACTION_DISTANCE);
		ClampTransform(transform_causing_player, CUTSCENE_SAFETY_CLAMP_DISTANCE);

		AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
		AddCinematicTLE(CSEVENT_GENERIC_REACTION_NEGATIVE, transform_causing_player, offending_team->GetSide());

		/// Setup background cutscenes for 'non-actors'.

		FVector cspos(0, 0, 0);
		cspos = transform_causing_player.TransformPos(cspos);
		SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

		//AddCutsceneCinematicElement(CSEVENT_MARK_AWARDED, transform_offending, marking_player, ENABLE_SIMULATION, ENABLE_SKIP, SWIPE_FADE);

		AddUserSkipPointElement();
	}
	else
	{
		AddScreenWipeElement(SWIPE_CUT);
	}

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_START_CALLBACK);
	AddCutsceneCallbackElement( SSCutSceneManager::RestartFreeKickR7KickOffPenaltyCallback, RESTART_ONFULL_CALLBACK );	// MUST be called after 'CutsceneEndCallback' as rules need to be enabled.*/
}
#endif
///-------------------------------------------------------------------------------
/// Called on event 'game_events->restart_kickoff_out_on_the_full'
///-------------------------------------------------------------------------------

void SSCutSceneManager::LaunchFreeKickOutOnFullCutscene(ARugbyCharacter* restart_out_causing_player)
{
	MABBREAKMSG("Not being used anymore?");
	MABLOGDEBUG("LaunchFreeKickOutOnFullCutscene");

	BallFreeInfo bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	FVector ball_pos = bfi.pos;
	ball_pos.y = 0.0f;			// Lets keep the cutscene on the ground!
	MabMatrix transform_restart = MabMatrix::TransMatrix(ball_pos);
	MabMatrix transform_causing_player;

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	if (cinematics_enabled)
	{
		AddCutsceneAsyncPreloadElement(false);
		AddScreenWipeElement(SWIPE_CUT);

		// Setup filters for cutscene.
		RUGameState *game_state = game->GetGameState();

		game_state->ResetPhases(true);
		game_state->SetPhase(RUGamePhase::REACTION_CUTSCENE);

		RUTeam *offending_team = restart_out_causing_player->GetAttributes()->GetTeam();
		RUTeam *restart_team = game_state->GetPlayRestartTeam();
		MABASSERT(restart_team != NULL);
		SetCelebratingTeam(static_cast< RUTeam*>(restart_out_causing_player->GetAttributes()->GetTeam()->GetOppositionTeam()));
		SetFocusPlayer(restart_out_causing_player, restart_out_causing_player->GetAttributes()->GetTeam()->GetSide());

		MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);

		if (restart_team->GetPlayDirection() == ERugbyPlayDirection::NORTH)
			transform_restart = rot_transform * transform_restart;

		transform_causing_player = transform_restart;	//rot_transform * transform_restart;

		OffsetNegativeReaction(transform_causing_player, NEGATIVE_REACTION_DISTANCE);
		ClampTransform(transform_causing_player, CUTSCENE_SAFETY_CLAMP_DISTANCE);

		AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
		AddCinematicTLE(CSEVENT_GENERIC_REACTION_NEGATIVE, transform_causing_player, offending_team->GetSide());

		/// Setup background cutscenes for 'non-actors'.

		FVector cspos(0, 0, 0);
		cspos = transform_causing_player.TransformPos(cspos);
		SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

		//AddCutsceneCinematicElement(CSEVENT_MARK_AWARDED, transform_offending, marking_player, ENABLE_SIMULATION, ENABLE_SKIP, SWIPE_FADE);

		AddUserSkipPointElement();
	}
	else
	{
		AddScreenWipeElement(SWIPE_CUT);
	}

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_START_CALLBACK);
	AddCutsceneCallbackElement(SSCutSceneManager::RestartFreeKickR7KickOffPenaltyCallback, RESTART_ONFULL_CALLBACK);	// MUST be called after 'CutsceneEndCallback' as rules need to be enabled.
}


///-------------------------------------------------------------------------------
/// Called on event 'game_events->restart_out_on_full'
///-------------------------------------------------------------------------------

void SSCutSceneManager::LaunchRestartOutOnFullCutscene(const FVector& /*position*/, ARugbyCharacter* restart_out_causing_player)
{
	MABLOGDEBUG("LaunchRestartOutOnFullCutscene");

	BallFreeInfo bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	FVector ball_pos = bfi.pos;
	ball_pos.y = 0.0f;			// Lets keep the cutscene on the ground!
	MabMatrix transform_restart = MabMatrix::TransMatrix(ball_pos);
	MabMatrix transform_causig_player;

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	if (cinematics_enabled)
	{
		AddCutsceneAsyncPreloadElement(false);
		AddScreenWipeElement(SWIPE_CUT);

		// Setup filters for cutscene.

		RUGameState *game_state = game->GetGameState();

		game_state->ResetPhases(true);
		game_state->SetPhase(RUGamePhase::REACTION_CUTSCENE);

		RUTeam *offending_team = restart_out_causing_player->GetAttributes()->GetTeam();
		RUTeam *restart_team = game_state->GetPlayRestartTeam();
		MABASSERT(restart_team != NULL);
		SetCelebratingTeam(static_cast< RUTeam*>(restart_out_causing_player->GetAttributes()->GetTeam()->GetOppositionTeam()));
		SetFocusPlayer(restart_out_causing_player, restart_out_causing_player->GetAttributes()->GetTeam()->GetSide());

		MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);

		if (restart_team->GetPlayDirection() == ERugbyPlayDirection::NORTH)
			transform_restart = rot_transform * transform_restart;

		transform_causig_player = transform_restart;	//rot_transform * transform_restart;

		OffsetNegativeReaction(transform_causig_player, NEGATIVE_REACTION_DISTANCE);
		ClampTransform(transform_causig_player, CUTSCENE_SAFETY_CLAMP_DISTANCE);

		AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
		AddCinematicTLE(CSEVENT_GENERIC_REACTION_NEGATIVE, transform_causig_player, offending_team->GetSide());

		/// Setup background cutscenes for 'non-actors'.

		FVector cspos(0, 0, 0);
		cspos = transform_causig_player.TransformPos(cspos);
		SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

		//AddCutsceneCinematicElement(CSEVENT_MARK_AWARDED, transform_offending, marking_player, ENABLE_SIMULATION, ENABLE_SKIP, SWIPE_FADE);

		AddUserSkipPointElement();
	}
	else
	{
		AddScreenWipeElement(SWIPE_CUT);
	}

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_START_CALLBACK);
	AddCutsceneCallbackElement(SSCutSceneManager::RestartOutOnFullCallback, RESTART_ONFULL_CALLBACK);	// MUST be called after 'CutsceneEndCallback' as rules need to be enabled.
}

///-------------------------------------------------------------------------------
/// Called on event 'game_events->restart_out_on_full'
///-------------------------------------------------------------------------------

void SSCutSceneManager::LaunchRestartKickOffIntoGoalCutscene( ARugbyCharacter* restart_out_causing_player)
{
	MABLOGDEBUG("LaunchRestartKickOffIntoGoalCutscene");

	BallFreeInfo bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	FVector ball_pos = bfi.pos;
	ball_pos.y = 0.0f;			// Lets keep the cutscene on the ground!
	MabMatrix transform_restart = MabMatrix::TransMatrix( ball_pos );
	MabMatrix transform_causig_player;

	AddCutsceneCallbackElement( SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK );

	if(cinematics_enabled)
	{
		AddCutsceneAsyncPreloadElement(false);
		AddScreenWipeElement(SWIPE_CUT);

		// Setup filters for cutscene.

		RUGameState *game_state = game->GetGameState();

		game_state->ResetPhases(true);
		game_state->SetPhase(RUGamePhase::REACTION_CUTSCENE );

		RUTeam *offending_team = restart_out_causing_player->GetAttributes()->GetTeam();
		RUTeam *restart_team = game_state->GetPlayRestartTeam();
		MABASSERT(restart_team!=NULL);
		SetCelebratingTeam( static_cast< RUTeam*>( restart_out_causing_player->GetAttributes()->GetTeam()->GetOppositionTeam() ) );
		SetFocusPlayer(restart_out_causing_player, restart_out_causing_player->GetAttributes()->GetTeam()->GetSide());

		MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);

		if (restart_team->GetPlayDirection() == ERugbyPlayDirection::NORTH)
			transform_restart = rot_transform * transform_restart;

		transform_causig_player = transform_restart;	//rot_transform * transform_restart;

		OffsetNegativeReaction(transform_causig_player, NEGATIVE_REACTION_DISTANCE);
		ClampTransform(transform_causig_player, CUTSCENE_SAFETY_CLAMP_DISTANCE);

		AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
		AddCinematicTLE(CSEVENT_GENERIC_REACTION_NEGATIVE, transform_causig_player, offending_team->GetSide());

		/// Setup background cutscenes for 'non-actors'.

		FVector cspos(0,0,0);
		cspos = transform_causig_player.TransformPos(cspos);
		SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION );

		//AddCutsceneCinematicElement(CSEVENT_MARK_AWARDED, transform_offending, marking_player, ENABLE_SIMULATION, ENABLE_SKIP, SWIPE_FADE);

		AddUserSkipPointElement();
	}
	else
	{
		AddScreenWipeElement(SWIPE_CUT);
	}

	AddCutsceneCallbackElement( SSCutSceneManager::CutsceneEndCallback, CUTSCENE_START_CALLBACK );
	AddCutsceneCallbackElement(SSCutSceneManager::RestartOutOnFullCallback, RESTART_ONFULL_CALLBACK );	// MUST be called after 'CutsceneEndCallback' as rules need to be enabled.
}

///-------------------------------------------------------------------------------
/// Called on event 'game_events->restart_out_on_full'
///-------------------------------------------------------------------------------

void SSCutSceneManager::LaunchBallDeadCutscene( ARugbyCharacter* last_played_by_player, const FVector& /*pos*/, RURuleConsequence consequence )
{
	/// There is a nasty bug, which triggers event 'rule_trigger_ball_dead' over and over again.
	/// this if not trapped here, ends up with hundreds of queued cutscenes which crashes the game.
	/// The probable cause for this is rules being enabled during another cutscene, but time is short so
	/// will prevent the crash here.

	MABASSERT(!have_queued_ball_dead_cutscene);
	if (have_queued_ball_dead_cutscene)
	{
		return;
	}
	have_queued_ball_dead_cutscene = true;			/// Cleared by 'BallDeadCallback' which happens after cutscene plays.

													// turn off game rules now! - stop other rules firing in load delay.

	game->GetRules()->EnableConsequences(false);
	game->GetRules()->EnableTriggers(RURT_NONE);
	game->GetRules()->SuspendPlay(true, "LaunchBallDeadCutscene");

	//---------------------------------------

	MABLOGDEBUG("LaunchBallDeadCutscene");

	MabMatrix transform_restart = MabMatrix::TransMatrix(last_played_by_player->GetMovement()->GetCurrentPosition());
	MabMatrix transform_causig_player;

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	RUTeam* team_at_fault = last_played_by_player->GetAttributes()->GetTeam();
	MabString teamName = team_at_fault->GetDbTeam().GetShortName();
	RUHUDUpdater::CensorTeamName(&team_at_fault->GetDbTeam(), teamName);
	
	if (game->GetHUDUpdater())
	{
		MabString genericString = MabString(0, "[ID_DEAD_BALL_BY] %s", teamName.c_str());
		game->GetHUDUpdater()->SetGenericInfo(SIFGameHelpers::GAConvertMabStringToFString(genericString), team_at_fault, "[ID_DEAD_BALL]");
	}

	if (cinematics_enabled)
	{
		AddCutsceneAsyncPreloadElement(false);
		AddScreenWipeElement(SWIPE_CUT);

		// Setup filters for cutscene.

		RUGameState *game_state = game->GetGameState();

		game_state->ResetPhases(true);
		game_state->SetPhase(RUGamePhase::REACTION_CUTSCENE);

		RUTeam *offending_team = last_played_by_player->GetAttributes()->GetTeam();
		RUTeam *restart_team = game_state->GetPlayRestartTeam();
		MABASSERT(restart_team != NULL);
		SetCelebratingTeam(static_cast< RUTeam*>(last_played_by_player->GetAttributes()->GetOppositionTeam()));
		SetFocusPlayer(last_played_by_player, last_played_by_player->GetAttributes()->GetTeam()->GetSide());

		MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);

		if (restart_team->GetPlayDirection() == ERugbyPlayDirection::NORTH)
			transform_restart = rot_transform * transform_restart;

		transform_causig_player = transform_restart;		//rot_transform * transform_restart;

		ClampTransform(transform_causig_player, CUTSCENE_SAFETY_CLAMP_DISTANCE);

		AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
		AddCinematicTLE(CSEVENT_BALLOUT_NEGATIVE, transform_causig_player, offending_team->GetSide());

		/// Setup background cutscenes for 'non-actors'.

		FVector cspos(0, 0, 0);
		cspos = transform_causig_player.TransformPos(cspos);
		SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

		//AddCutsceneCinematicElement(CSEVENT_MARK_AWARDED, transform_offending, marking_player, ENABLE_SIMULATION, ENABLE_SKIP, SWIPE_FADE);

		AddUserSkipPointElement();
	}

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);
	AddCutsceneCallbackElement(SSCutSceneManager::BallDeadCallback, BALLDEAD_CALLBACK, consequence);	// MUST be called after 'CutsceneEndCallback' as rules need to be enabled.
}

///-------------------------------------------------------------------------------
/// Called on event 'game_events->maul_heldup'
///-------------------------------------------------------------------------------

void SSCutSceneManager::LaunchMaulHeldUpCutscene(ARugbyCharacter* holding_player, const FVector& restart_position, bool defense_to_feed)
{
	MABLOGDEBUG("LaunchMaulHeldUpCutscene");

	// No maul cutscenes in sandbox!
	if (!game->IsMatch())
		return;

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	if (cinematics_enabled)
	{
		AddCutsceneAsyncPreloadElement(false);
		AddScreenWipeElement();

		RUGameState *game_state = game->GetGameState();

		if (holding_player)
		{
			if (game->GetHUDUpdater())
			{
				RUTeam* holding_team = holding_player->GetAttributes()->GetTeam();
				MabString teamName = holding_team->GetDbTeam().GetShortName();
				RUHUDUpdater::CensorTeamName(&holding_team->GetDbTeam(), teamName);
				
				MabString genericString = MabString(0, "[ID_MAUL_BALL_NOT_RELEASED] %s", teamName.c_str());
				game->GetHUDUpdater()->SetGenericInfo(SIFGameHelpers::GAConvertMabStringToFString(genericString), holding_team, "[ID_SCRUM_AWARDED]");
			}

		}
		RUTeam* restart_team = NULL;
		if (defense_to_feed)
			restart_team = game->GetGameState()->GetDefendingTeam();
		else
			restart_team = game->GetGameState()->GetAttackingTeam();
		game_state->SetPlayRestartPosition(restart_position);
		game_state->SetPlayRestartTeam(restart_team);
		game_state->SetAttackingTeam(restart_team);

		game_state->SetPhase(RUGamePhase::REACTION_CUTSCENE);

		SetCelebratingTeam(restart_team);

		MabMatrix transform = MabMatrix::TransMatrix(restart_position);
		MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);

		if (restart_team->GetPlayDirection() == ERugbyPlayDirection::NORTH)
			transform = rot_transform * transform;

		ClampTransform(transform, CUTSCENE_SAFETY_CLAMP_DISTANCE);

		AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS);
		AddCinematicTLE(CSEVENT_REFEREE_PENALTY, transform, SIDE_OFFICIALS);

		/// Setup background cutscenes for 'non-actors'.

		FVector cspos(0, 0, 0);
		cspos = transform.TransformPos(cspos);
		SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

		//------------------------

		AddUserSkipPointElement();
	}

	/// Do a substitution if one is pending.
	if (game->GetSubstitutionManager()->GetNumQueuedEvents()>0 && !game->GetGameTimer()->IsExpired())
	{
		SetupInterchange();
	}

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);

	// check if our pro was sent off, if he was then don't care about the penalty callback, do a pro sent off callback instead
	if (pro_player_sent_off_during_interchanges)
	{
		AddCutsceneCallbackElement(SSCutSceneManager::ProSentOffCallback, PRO_SENT_OFF_CALLBACK);
		suspended_callback_from_pro_send_off = SSCutSceneManager::KnockonCallback;
		suspended_callback_from_pro_send_off_debug_name = KNOCKON_CALLBACK;
	}
	else
		AddCutsceneCallbackElement(SSCutSceneManager::KnockonCallback, KNOCKON_CALLBACK);	// MUST be called after 'CutsceneEndCallback' as rules need to be enabled.
}

///-------------------------------------------------------------------------------
/// Called on event 'game_events->knockon_awarded'
///-------------------------------------------------------------------------------

void SSCutSceneManager::LaunchKnockonCutscene()
{
	MABLOGDEBUG("LaunchKnockonCutscene");

	RUGameState *game_state = game->GetGameState();
	ARugbyCharacter* offender = game_state->GetLastOffendingPlayer();
	MABASSERT(offender != NULL);
	RUTeam* offending_team = offender->GetAttributes()->GetTeam();

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);
	MabString teamName = offending_team->GetDbTeam().GetShortName();
	RUHUDUpdater::CensorTeamName(&offending_team->GetDbTeam(),teamName);

	AddHUDInfoCallbackElement(0, "[ID_ADVANTAGE_OVER]", offending_team, MabString(0, "[ID_KNOCKED_ON_BY] %s", teamName.c_str()));

	if (!game->GetGameTimer()->ScrumCancelledByHalfOver() && !game->GetGameState()->IsFifthTackle())
	{
		teamName = offender->GetAttributes()->GetOppositionTeam()->GetDbTeam().GetShortName();
		RUHUDUpdater::CensorTeamName(&offender->GetAttributes()->GetOppositionTeam()->GetDbTeam(), teamName);

		AddHUDInfoCallbackElement(0, "[ID_SCRUM_AWARDED]", offending_team, MabString(0, "[ID_SCRUM_SET_BY] %s", teamName.c_str()));
	}
	else if (!game->GetGameTimer()->ScrumCancelledByHalfOver() && game->GetGameState()->IsFifthTackle())
	{
		MabString teamName = offending_team->GetDbTeam().GetShortName();
		RUHUDUpdater::CensorTeamName(&offending_team->GetDbTeam(), teamName);

		AddHUDInfoCallbackElement(0, "[ID_HANDOVER_SIGNALLED]", offending_team, MabString(0, "[ID_KNOCKED_ON_BY] %s", teamName.c_str()));
	}

	if (cinematics_enabled)
	{
		AddCutsceneAsyncPreloadElement(false);
		AddScreenWipeElement(SWIPE_CUT);

		game_state->SetPhase(RUGamePhase::REACTION_CUTSCENE);

		FVector position = offender->GetMovement()->GetCurrentPosition();
		MabMatrix transform = MabMatrix::TransMatrix(position);
		MabMatrix transform_offending;

		// Setup filters for cutscene.

		RUTeam *restart_team = game_state->GetPlayRestartTeam();
		MABASSERT(restart_team != NULL);

		RUTeam *commiserating_team = offending_team;
		MABASSERT(commiserating_team != restart_team);

		SetCelebratingTeam(restart_team);	

		//in case we have no offender or offender doesnot belong to offending_team, dont set the focus player, cutscene logic will pick someone randomly from the offending_team
		ensureAlways(offender->GetAttributes()->GetTeam() == offending_team);
		if (offender && (offender->GetAttributes()->GetTeam() == offending_team))
		{
			SetFocusPlayer(offender, commiserating_team->GetSide());
		}

		ARugbyCharacter* celebrating_player = game->GetSpatialHelper()->FindClosestPlayer(offender, &restart_team->GetPlayers());
		MABASSERT(celebrating_player != NULL);
		SetFocusPlayer(celebrating_player, restart_team->GetSide());

		MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);

		if (restart_team->GetPlayDirection() == ERugbyPlayDirection::NORTH)
			transform = rot_transform * transform;

		transform_offending = rot_transform * transform;

		SSTEAMSIDE restart_side = restart_team->GetSide();
		SSTEAMSIDE offending_side = (restart_side == SIDE_A) ? SIDE_B : SIDE_A;

		OffsetNegativeReaction(transform, NEGATIVE_REACTION_DISTANCE);

		ClampTransform(transform, CUTSCENE_SAFETY_CLAMP_DISTANCE);
		ClampTransform(transform_offending, CUTSCENE_SAFETY_CLAMP_DISTANCE);

		AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, replays_enabled ? SWIPE_WIPE : SWIPE_CUT, HIDE_NON_ACTORS);
		AddCinematicTLE(CSEVENT_GENERIC_REACTION_NEGATIVE, transform, restart_side);
		AddCinematicTLE(CSEVENT_GENERIC_REACTION_POSITIVE, transform_offending, offending_side);
		AddCinematicTLE(CSEVENT_REFEREE_SCRUM, transform_offending, SIDE_NONE);

		/// Setup background cutscenes for 'non-actors'.

		FVector cspos(0, 0, 0);
		cspos = transform.TransformPos(cspos);
		SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION + NEGATIVE_REACTION_DISTANCE);

		AddUserSkipPointElement();

		if (replays_enabled)
		{
			AddInfringementReplayElement(KNOCKON_MAX_REWIND_TIME, KNOCKON_DURATION, RT_KNOCK_ON);
			//AddUserSkipPointElement();
		}
	}
	else
	{
		if (replays_enabled)
		{
			//AddScreenWipeElement(SWIPE_WIPE);
			AddInfringementReplayElement(KNOCKON_MAX_REWIND_TIME, KNOCKON_DURATION, RT_KNOCK_ON);
			//AddUserSkipPointElement();
		}
	}


	/// Do a substitution if one is pending.
	if (game->GetSubstitutionManager()->GetNumQueuedEvents()>0 && !game->GetGameTimer()->IsExpired())
	{
		SetupInterchange();
	}

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);

	// check if our pro was sent off, if he was then don't care about the penalty callback, do a pro sent off callback instead
	if (pro_player_sent_off_during_interchanges)
	{
		AddCutsceneCallbackElement(SSCutSceneManager::ProSentOffCallback, PRO_SENT_OFF_CALLBACK);
		suspended_callback_from_pro_send_off = SSCutSceneManager::KnockonCallback;
		suspended_callback_from_pro_send_off_debug_name = KNOCKON_CALLBACK;
	}
	else
		AddCutsceneCallbackElement(SSCutSceneManager::KnockonCallback, KNOCKON_CALLBACK);		// MUST be called after 'CutsceneEndCallback' as rules need to be enabled.
}

///-------------------------------------------------------------------------------
/// SetupBGReactionCutscenes
///-------------------------------------------------------------------------------

void SSCutSceneManager::SetupBGReactionCutscenes(const FVector &position, float positive_z_offset, float negative_z_offset, bool force_x_zero)
{
	if (bg_cutscenes_enabled)
	{
		RUTeam	  *positive_team = GetCelebratingTeam();
		MabMatrix transform;
		MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);

		if (positive_team == NULL)
			positive_team = game->GetTeam(SIDE_A);

		float xp = force_x_zero ? 0.0f : position.x;
		float zp = position.z;

		MabMatrix transform_negative_bg;
		MabMatrix transform_positive_bg;

		float zp_pos = zp;
		float zp_neg = zp;

		if (positive_team->GetPlayDirection() == ERugbyPlayDirection::SOUTH)
		{
			zp_pos += positive_z_offset;
			zp_neg -= negative_z_offset;
		}
		else
		{
			zp_pos -= positive_z_offset;
			zp_neg += negative_z_offset;
		}

		/// Keep in field + setup xclip range.

		const float REACTION_CUTSCENE_ZEXTENT = 5.0f;
		const float XCLIP_RANGE = 10.0f;
		const float NO_CLAMP_XCLIP_RANGE = 4.0f;

		float pos_xclip_range = NO_CLAMP_XCLIP_RANGE;
		float neg_xclip_range = NO_CLAMP_XCLIP_RANGE;

		FieldExtents fe = game->GetSpatialHelper()->GetFieldExtents();
		fe.y *= 0.5f;
		float min_z = -fe.y + REACTION_CUTSCENE_ZEXTENT;
		float max_z = fe.y - REACTION_CUTSCENE_ZEXTENT;

		/// Keep positive in field z.
		if (zp_pos<min_z)
		{
			zp_pos = min_z;
			pos_xclip_range = XCLIP_RANGE;
		}
		if (zp_pos>max_z)
		{
			zp_pos = max_z;
			pos_xclip_range = XCLIP_RANGE;
		}

		/// Keep negative in field z.
		if (zp_neg<min_z)
		{
			zp_neg = min_z;
			neg_xclip_range = XCLIP_RANGE;
		}
		if (zp_neg>max_z)
		{
			zp_neg = max_z;
			neg_xclip_range = XCLIP_RANGE;
		}

		transform_positive_bg = MabMatrix::TransMatrix(xp, 0.0f, zp_pos);
		transform_negative_bg = MabMatrix::TransMatrix(xp, 0.0f, zp_neg);

		if (positive_team->GetPlayDirection() == ERugbyPlayDirection::SOUTH)
			transform_positive_bg = rot_transform * transform_positive_bg;
		else
			transform_negative_bg = rot_transform * transform_negative_bg;

		AddBackgroundCinematicTLE(
			CSEVENT_GENERIC_REACTION_NEGATIVE_BG,
			transform_negative_bg,
			positive_team->GetOppositionTeam()->GetSide(),
			position.x - neg_xclip_range,
			position.x + neg_xclip_range);

		AddBackgroundCinematicTLE(
			CSEVENT_GENERIC_REACTION_POSITIVE_BG,
			rot_transform * transform_positive_bg,
			positive_team->GetSide(),
			position.x - pos_xclip_range,
			position.x + pos_xclip_range);
	}
}


///-------------------------------------------------------------------------------
/// GameEvent: LineoutSignalled 'game_events->lineout_signalled'
///-------------------------------------------------------------------------------
void SSCutSceneManager::LineoutSignalled(FVector restart_position, bool from_decision)
{
	MABLOGDEBUG("%s", __FUNCTION__);
	// Ignore sandbox lineout signals (Can happen and will crash game).
	if (!game->IsMatch())
		return;

	const float GOOD_KICK_TO_TOUCH_DISTANCE = 1.0f;		// Just have to have gained ground.

	/// Code has been changed to give additional context for when the ball is carried out after a kick for touch
	/// and now happens further down in this function.
	///
	///bool validLineout = !game->GetGameTimer()->IsExpired();
	///
	///// This is no longer an NRC specific rule, and is in the standard ruleset.
	/////if(game->GetGameSettings().game_law == GAME_LAW_NRC)
	///{
	///	const BallFreeInfo& ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();
	///	BALL_FREE_EVENT evt = ball_free_info.event;
	///	RUGamePhase previous_phase = ball_free_info.game_phase_when_released;
	///
	///	if(evt == BFE_KICK && previous_phase == RUGamePhase::PENALTY_KICK_FOR_TOUCH)
	///	{
	///		validLineout = true;
	///	}
	///}
	///
	///if( !validLineout && !from_decision)
	///	return;

	BallFreeInfo bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	BallCollectInfo bci = game->GetStrategyHelper()->GetLastBallCollectInfo();
	MABUNUSED(bci);

	// Logic handled by RURuleTriggerBallout. No point in duplicating it.
	RUTeam* restart_team = game->GetGameState()->GetPlayRestartTeam();

	ARugbyCharacter* offending_player = bfi.last_player;
	bool was_kick_for_touch = (bfi.event == BFE_KICK && bfi.game_phase_when_released == RUGamePhase::PENALTY_KICK_FOR_TOUCH);
	bool was_carried_out = false;

	ARugbyCharacter* ball_holder = game->GetGameState()->GetBallHolder();
	if (ball_holder)
	{
		was_carried_out = ball_holder->GetAttributes()->GetTeam() != restart_team && game->GetGameState()->GetLastLineoutCarriedOut();

		// The ball holder is being held responsible
		if (was_carried_out)
		{
			offending_player = ball_holder;
			was_kick_for_touch = false;
		}
	}

	// RC4-5609: Prevent an NMA due to the offending player being NULL in some cases where we have forced a random lineout.
	if (offending_player == nullptr)
	{
		RUTeam* pOffendingTeam = NULL;
		if (restart_team == game->GetGameState()->GetNorthTeam())
		{
			pOffendingTeam = game->GetGameState()->GetSouthTeam();
		}
		else
		{
			pOffendingTeam = game->GetGameState()->GetNorthTeam();
		}
		offending_player = pOffendingTeam->GetPlayerByPosition(PLAYER_POSITION::PP_HOOKER);
	}

	bool time_expired = game->GetGameTimer()->IsExpired();

	/// The response here must mirror SSGameTimerHalfObserver::NotifyLineOut to avoid NMA
	if (time_expired && !was_kick_for_touch && !from_decision)
	{
		return;
	}

	// skip the cutscenes if we can't find a player for some reason
	// or if this is from a decision
	if (!offending_player || game->GetGameState()->GetPhase() == RUGamePhase::DECISION_PENALTY || game->GetGameState()->GetPhase() == RUGamePhase::QUICK_LINEOUT)
	{
		MABLOGDEBUG("%s: No offending player found", __FUNCTION__);
		AddScreenWipeElement(SWIPE_CUT);
		AddCutsceneCallbackElement(SSCutSceneManager::StartLineoutCallback, START_LINEOUT_CALLBACK);
		return;
	}

	MabMatrix cutscene_transform = MabMatrix::TransMatrix(offending_player->GetMabPosition());

	RUTeam* offending_team = offending_player->GetAttributes()->GetTeam();

	s_pRestartTeam = restart_team;

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	// Removing, because logic from above dictates that these two will be identical
	//if (was_kick_for_touch)
	//{
	//	// Special consequence if the ball was kicked out during a penalty kick for touch.
	//	// In this situation, the team that put it out was actually the kicking team, but we only want to change the text.
	//	// The actual consequence logic should be the same as if the opposition put it out.
	//	RUTeam* actual_offending_team = bfi.last_player->GetAttributes()->GetTeam(); // static_cast<RUTeam*>( offending_team->GetOppositionTeam() );
	//	MabString teamName = actual_offending_team->GetDbTeam().GetShortName();
	//	RUHUDUpdater::CensorTeamName(&actual_offending_team->GetDbTeam(),teamName);
	//	AddHUDInfoCallbackElement(0, "[ID_LINEOUT_SIGNALLED]", actual_offending_team, MabString(0, "[ID_BALL_IN_TOUCH] %s", teamName.c_str()));
	//}
	//else
	{
		MabString teamName = offending_team->GetDbTeam().GetShortName();
		RUHUDUpdater::CensorTeamName(&offending_team->GetDbTeam(), teamName);

		AddHUDInfoCallbackElement(0, "[ID_LINEOUT_SIGNALLED]", offending_team, MabString(0, "[ID_BALL_IN_TOUCH] %s", teamName.c_str()));
	}

	AddScreenWipeElement(SWIPE_CUT);

	/// Do a reaction cutscene based on how ball went in to touch.

	if (!game->GetGameTimer()->IsExpired())
	{
		MABLOGDEBUG("%s: Checking substitutions", __FUNCTION__);
		if (cinematics_enabled)
		{
			int csevent = CSEVENT_BALLOUT_NEGATIVE;

			if (!was_carried_out)
			{
				if (bfi.event == BFE_KICK)
				{
					float play_dir = (float)offending_team->GetPlayDirection();
					float zdiff = (restart_position.z - bfi.pos.z) * play_dir;
					MABLOGDEBUG("kick distance = %f", zdiff);

					FVector kick_pos = bfi.pos;
					kick_pos.y = 0.0f;
					cutscene_transform = MabMatrix::TransMatrix(kick_pos);

					if (zdiff > GOOD_KICK_TO_TOUCH_DISTANCE)
						csevent = CSEVENT_BALLOUT_POSITIVE;
				}
			}

			if (csevent == CSEVENT_BALLOUT_POSITIVE)
				SetCelebratingTeam(offending_team);
			else
				SetCelebratingTeam((RUTeam*)offending_team->GetOppositionTeam());

			SetFocusPlayer(offending_player, offending_team->GetSide());

			if (offending_team->GetPlayDirection() == ERugbyPlayDirection::SOUTH)
				cutscene_transform = MabMatrix::RotMatrixY(PI) * cutscene_transform;

			ClampTransform(cutscene_transform, CUTSCENE_SAFETY_CLAMP_DISTANCE);

			AddCutsceneCinematicElement(RUGamePhase::PLAY, ENABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);
			AddCinematicTLE(csevent, cutscene_transform, offending_team->GetSide());

			/// Setup background cutscenes for 'non-actors'.

			FVector cspos(0, 0, 0);
			cspos = cutscene_transform.TransformPos(cspos);
			SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

			AddUserSkipPointElement();
		}

		/// Do a substitution if one is pending.
		if (game->GetSubstitutionManager()->GetNumQueuedEvents()>0)
		{
			MABLOGDEBUG("LineoutSignalled: Do pending interchanges");

			AddScreenWipeElement(SWIPE_FADE);
			SetupInterchange();
		}
	}
	else
	{
		MABLOGDEBUG("%s: Half timer expired, skipping interchanges", __FUNCTION__);
	}

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);

	// check if our pro was sent off, if he was then don't care about the penalty callback, do a pro sent off callback instead
	if (pro_player_sent_off_during_interchanges)
	{
		AddCutsceneCallbackElement(SSCutSceneManager::ProSentOffCallback, PRO_SENT_OFF_CALLBACK);
		suspended_callback_from_pro_send_off = SSCutSceneManager::StartLineoutCallback;
		suspended_callback_from_pro_send_off_debug_name = START_LINEOUT_CALLBACK;
	}
	else
		AddCutsceneCallbackElement(SSCutSceneManager::StartLineoutCallback, START_LINEOUT_CALLBACK);
}

///-------------------------------------------------------------------------------
/// GameEvent: TouchSignalled 'game_events->touch_signalled'
///-------------------------------------------------------------------------------
void SSCutSceneManager::TouchScrumSignalled(FVector restart_position, bool from_decision)
{
	MABLOGDEBUG("%s", __FUNCTION__);
	// Ignore sandbox lineout signals (Can happen and will crash game).
	
	if (!game->IsMatch())
	{
		MABLOGDEBUG("%s ismatch() early out ", __FUNCTION__);
		return;
	}

	const float GOOD_KICK_TO_TOUCH_DISTANCE = 1.0f;		// Just have to have gained ground.

	BallFreeInfo bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	BallCollectInfo bci = game->GetStrategyHelper()->GetLastBallCollectInfo();
	MABUNUSED(bci);

	// Logic handled by RURuleTriggerBallout. No point in duplicating it.
	RUTeam* restart_team = game->GetGameState()->GetPlayRestartTeam();

	ARugbyCharacter* offending_player = bfi.last_player;
	bool was_kick_for_touch = (bfi.event == BFE_KICK && bfi.game_phase_when_released == RUGamePhase::PENALTY_KICK_FOR_TOUCH);
	bool was_carried_out = false;

	ARugbyCharacter* ball_holder = game->GetGameState()->GetBallHolder();
	if (ball_holder)
	{
		MABLOGDEBUG("%s has ball holder ", __FUNCTION__);
		was_carried_out = ball_holder->GetAttributes()->GetTeam() != restart_team && game->GetGameState()->GetLastLineoutCarriedOut();

		// The ball holder is being held responsible
		if (was_carried_out)
		{
			offending_player = ball_holder;
			was_kick_for_touch = false;
		}
	}

	// RC4-5609: Prevent an NMA due to the offending player being NULL in some cases where we have forced a random lineout.
	if (offending_player == nullptr)
	{
		MABLOGDEBUG("%s no offending player ", __FUNCTION__);
		RUTeam* pOffendingTeam = NULL;
		if (restart_team == game->GetGameState()->GetNorthTeam())
		{
			pOffendingTeam = game->GetGameState()->GetSouthTeam();
		}
		else
		{
			pOffendingTeam = game->GetGameState()->GetNorthTeam();
		}

		offending_player = pOffendingTeam->GetPlayerByPosition(PLAYER_POSITION::PP_HOOKER);

		MABLOGDEBUG("%s offending player set to %x ", __FUNCTION__, offending_player);
	}

	bool time_expired = game->GetGameTimer()->IsExpired();

	/// The response here must mirror SSGameTimerHalfObserver::NotifyTouchScrum to avoid NMA
	if (time_expired && !was_kick_for_touch && !from_decision)
	{
		MABLOGDEBUG("%s time expired", __FUNCTION__ );
		return;
	}

	// skip the cutscenes if we can't find a player for some reason
	// or if this is from a decision
	if (!offending_player || game->GetGameState()->GetPhase() == RUGamePhase::DECISION_PENALTY)
	{
		MABLOGDEBUG("%s: No offending player found", __FUNCTION__);
		AddScreenWipeElement(SWIPE_CUT);

		//todo StartScrumCallback?
		AddCutsceneCallbackElement(SSCutSceneManager::StartTouchScrumCallback, START_TOUCH_SCRUM_CALLBACK);
		return;
	}

	MabMatrix	cutscene_transform	= MabMatrix::TransMatrix(offending_player->GetMabPosition());
	RUTeam*		offending_team		= offending_player->GetAttributes()->GetTeam();

	//add scrum_restart_team??
	 s_pRestartTeam = restart_team;

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	MabString teamName = offending_team->GetDbTeam().GetShortName();
	RUHUDUpdater::CensorTeamName(&offending_team->GetDbTeam(), teamName);

	if (game->GetGameState()->GetHandoverType() == EHandoverType::OUTONFULL || game->GetGameState()->GetHandoverType() == EHandoverType::INTOUCH)
	{
		// GGS JZ String added handover string for UI
		AddHUDInfoCallbackElement(0, "[ID_HANDOVER_SIGNALLED]", offending_team, MabString(0, "[ID_BALL_IN_TOUCH] %s", teamName.c_str()));
	}
	else
	{
		// WJS RLC String SCRUM_SIGNALLED Added to UE string table todo translate??
		AddHUDInfoCallbackElement(0, "[ID_SCRUM_SIGNALLED]", offending_team, MabString(0, "[ID_BALL_IN_TOUCH] %s", teamName.c_str()));
	}
	
	AddScreenWipeElement(SWIPE_CUT);

	/// Do a reaction cutscene based on how ball went in to touch.
	if (!game->GetGameTimer()->IsExpired())
	{
		MABLOGDEBUG("%s: Checking substitutions", __FUNCTION__);
		if (cinematics_enabled)
		{
			int csevent = CSEVENT_BALLOUT_NEGATIVE;

			if (!was_carried_out && game->GetGameState()->GetHandoverType() != EHandoverType::OUTONFULL && game->GetGameState()->GetHandoverType() != EHandoverType::INTOUCH)
			{
				if (bfi.event == BFE_KICK)
				{
					float play_dir = (float)offending_team->GetPlayDirection();
					float zdiff = (restart_position.z - bfi.pos.z) * play_dir;
					MABLOGDEBUG("kick distance = %f", zdiff);

					FVector kick_pos = bfi.pos;
					kick_pos.y = 0.0f;
					cutscene_transform = MabMatrix::TransMatrix(kick_pos);

					if (zdiff > GOOD_KICK_TO_TOUCH_DISTANCE)
						csevent = CSEVENT_BALLOUT_POSITIVE;
				}
			}

			if (csevent == CSEVENT_BALLOUT_POSITIVE)
				SetCelebratingTeam(offending_team);
			else
				SetCelebratingTeam((RUTeam*)offending_team->GetOppositionTeam());

			SetFocusPlayer(offending_player, offending_team->GetSide());

			if (offending_team->GetPlayDirection() == ERugbyPlayDirection::SOUTH)
				cutscene_transform = MabMatrix::RotMatrixY(PI) * cutscene_transform;

			ClampTransform(cutscene_transform, CUTSCENE_SAFETY_CLAMP_DISTANCE);

			AddCutsceneCinematicElement(RUGamePhase::PLAY, ENABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);
			AddCinematicTLE(csevent, cutscene_transform, offending_team->GetSide());

			/// Setup background cutscenes for 'non-actors'.

			FVector cspos(0, 0, 0);
			cspos = cutscene_transform.TransformPos(cspos);
			SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

			AddUserSkipPointElement();
		}

		if (game->GetSubstitutionManager()->GetNumQueuedEvents() > 0)
		{
			MABLOGDEBUG("%s: There are %d substutions pending in scrum, is it ok to ignore? WJS", __FUNCTION__, game->GetSubstitutionManager()->GetNumQueuedEvents());
		}

		// WJS RLC Not needed??
		/*
		/// Do a substitution if one is pending.
		if (game->GetSubstitutionManager()->GetNumQueuedEvents() > 0)
		{
			MABLOGDEBUG("LineoutSignalled: Do pending interchanges");

			AddScreenWipeElement(SWIPE_FADE);
			SetupInterchange();
		}
		//*/
	}
	else
	{
		MABLOGDEBUG("%s: Half timer expired, skipping interchanges", __FUNCTION__);
	}

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);

	// WJS RLC NOT NEEDED??
	/*
	// check if our pro was sent off, if he was then don't care about the penalty callback, do a pro sent off callback instead
	if (pro_player_sent_off_during_interchanges)
	{
		AddCutsceneCallbackElement(SSCutSceneManager::ProSentOffCallback, PRO_SENT_OFF_CALLBACK);
		suspended_callback_from_pro_send_off = SSCutSceneManager::StartTouchScrumCallback;
		suspended_callback_from_pro_send_off_debug_name = START_TOUCH_SCRUM_CALLBACK;
	}
	else
	//*/
		AddCutsceneCallbackElement(SSCutSceneManager::StartTouchScrumCallback, START_TOUCH_SCRUM_CALLBACK);
}


///-------------------------------------------------------------------------------
/// Called on event 'game_events->cutscene_injury'
/// N.B:	Injuries and penalties CAN NOT occur at the same time!
///-------------------------------------------------------------------------------

void SSCutSceneManager::StartInjuryCutScene(ARugbyCharacter* Ininjured_player, TACKLE_INJURY_TYPE injury_type)
{
	MABLOGDEBUG("StartInjuryCutScene");

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);
	AddCutsceneAsyncPreloadElement(false);
	AddScreenWipeElement(SWIPE_CUT);

	FVector pos = Ininjured_player->GetMovement()->GetCurrentPosition();
	MabMatrix transform = MabMatrix::TransMatrix(pos);

	int cs_event_a = CSEVENT_INJURY_SUBSTITUTION_A_TOP;
	int cs_event_b = CSEVENT_INJURY_SUBSTITUTION_B_TOP;

	if (injury_type == TIT_KNEE)
	{
		cs_event_a = CSEVENT_INJURY_SUBSTITUTION_A_BTM;
		cs_event_b = CSEVENT_INJURY_SUBSTITUTION_B_BTM;
	}
	else if (injury_type == TIT_STOMACH)
	{
		cs_event_a = CSEVENT_INJURY_SUBSTITUTION_A_MID;
		cs_event_b = CSEVENT_INJURY_SUBSTITUTION_B_MID;
	}

	AddHUDInfoCallbackElement(Ininjured_player->GetAttributes()->GetDbId(), "[ID_RU_INTERCHANGE_INJURY]", Ininjured_player->GetAttributes()->GetTeam(), "", Ininjured_player);

	if (cinematics_enabled)
	{
		SetCelebratingTeam((RUTeam*)Ininjured_player->GetAttributes()->GetTeam());

		/// Rotate cutscene so player walks in direction of interchange locator.
		float angle_to_interchange_locator = CalculateAngleToInterchangeLocator(pos);
		MabMatrix nr = MabMatrix::RotMatrixY(angle_to_interchange_locator);
		transform = nr * transform;

		AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);
		AddCinematicTLE(cs_event_a, transform, SIDE_NONE);

		/// Setup background cutscenes for 'non-actors'.

		FVector cspos(0, 0, 0);
		cspos = transform.TransformPos(cspos);
		SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

		AddUserSkipPointElement();
	}

	SetInterchangeType(RU_INTERCHANGE_INJURY);
	SetInjuredPlayer(Ininjured_player);

	AddCutsceneCallbackElement(SSCutSceneManager::SelectInjuryReplacementCallback, INJURY_SELECT_CALLBACK, cs_event_b);
}

///-------------------------------------------------------------------------------
/// SelectInjuryReplacementCallback: Setup UI for player replacement...
///-------------------------------------------------------------------------------

bool SSCutSceneManager::SelectInjuryReplacementCallback(SSCutSceneManager *manager, CutsceneCallbackElement *element)
{
	return manager->SelectInjuryReplacementCallbackNonStatic(element);
}

bool SSCutSceneManager::SelectInjuryReplacementCallbackNonStatic(CutsceneCallbackElement *element)
{
	RUSubstitutionManager *sub_manager = game->GetSubstitutionManager();
	if (injured_player == NULL)		// Something very odd has happened (overlapped injury/substitution???)
	{
		return true;
	}

	RUTeam *injured_team = injured_player->GetAttributes()->GetTeam();
	int		best_replacement_id = -1;
	int		cs_event_b = element->user_info;

	if (element->callback_state == 0)
	{
		/// First time calling this callback - Bring up injury UI if required.

		best_replacement_id = sub_manager->GetBestReplacingPlayer(injured_player);

#ifdef ENABLE_SOAK_TEST
		if (!SIFApplication::GetApplication()->GetSoakManager()->IsRunning())
		{
#endif
			if (game->GetGameSettings().substitution_mode == SUB_MANUAL)
			{
				// If we're playing a pro career, we never want the option to choose which players to substitute - we're not the coach.
				bool is_pro_career = SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro();

				if (injured_team->GetNumHumanPlayers() > 0 && !is_pro_career)
				{
					injury_hud_selected_player = -1;			// Set by UI when complete.

					game->GetScreenWipeManager()->HoldScreenWipe(100.0f);
					SIFInGameHelpers::RaiseInjurySubstitutionMenu(injured_team->GetIndex(), best_replacement_id);
					element->callback_state++;
					return false;
				}
			}
#ifdef ENABLE_SOAK_TEST
		}
#endif

	}
	else
	{
		/// Waiting for injury hud to complete.

		if (injury_hud_selected_player != -1)
		{
			game->GetScreenWipeManager()->StartFadeFromBlack(DEFAULT_SCREEN_WIPE_TIME);			// Continue screen wipe.
			best_replacement_id = injury_hud_selected_player;
			element->callback_state++;
		}
		else
			return false;
	}

	RUInterchangeEvent *entry = NULL;

	MABASSERT(best_replacement_id>0);
	if (best_replacement_id>0)			// Just abort if no replacement found (should never happen).
	{
		/// Create the interchange event.
		RUInterchangeEvent interchange_event(injured_team, injured_player, injured_player->GetAttributes()->GetDbId(), best_replacement_id, RU_INTERCHANGE_INJURY);
		MABVERIFY(sub_manager->AddInterchangeEvent(interchange_event) == EValidInterchangeReason::VIR_VALID);

		entry = sub_manager->GetHighestPriorityPendingEvent();		// Get actual entry back from sub_manager
		MABASSERT(entry != NULL);
	}

	if (entry)
	{
		disable_substitutions = true;

		///------------------------------------------

		AddCutsceneCallbackElement(SSCutSceneManager::NotifyInterchangeStartedCallback, NOTIFY_INTERCHANGE_STARTED_CALLBACK);
		AddCutsceneCallbackElement(SSCutSceneManager::ShowInterchangeHUDCallback, SHOW_INTERCHANGE_HUD_CALLBACK);
		AddCutsceneCallbackElement(SSCutSceneManager::StartSubstituteLoadCallback, START_SUBSTITION_LOAD_CALLBACK, entry->GetUID());

		if (cinematics_enabled)
		{
			/// Calculate transform for second pre-substitutions cutscene.

			FVector pos = injured_player->GetMovement()->GetCurrentPosition();
			MabMatrix transform = MabMatrix::TransMatrix(pos);
			float angle_to_interchange_locator = CalculateAngleToInterchangeLocator(pos);
			MabMatrix nr = MabMatrix::RotMatrixY(angle_to_interchange_locator);
			transform = nr * transform;

			ClampTransform(transform, CUTSCENE_SAFETY_CLAMP_DISTANCE);

			AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);
			AddCinematicTLE(cs_event_b, transform, SIDE_NONE);

			/// Setup background cutscenes for 'non-actors'.

			FVector cspos(0, 0, 0);
			cspos = transform.TransformPos(cspos);
			SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

			AddUserSkipPointElement();
		}

		AddCutsceneCallbackElement(SSCutSceneManager::NotifyMidInterchangeCallback, NOTIFY_MID_INTERCHANGE_CALLBACK);
		AddCutsceneCallbackElement(SSCutSceneManager::MidSubstitutionCallback, MID_SUBSTITUTION_CALLBACK, entry->GetUID());

		if (cinematics_enabled)
		{
			MabMatrix transform = MabMatrix::IDENTITY;
			FVector position = FVector::ZERO;
			float	angle = 0.0f;

			if (!game->GetStadiumManager()->GetStadiumCutsceneLocator(position, angle, INTERCHANGE_LOCATOR_TYPE))
			{
				position.x = INTERCHANGE_DEFAULT_X;
				angle = INTERCHANGE_DEFAULT_ANGLE;
			}
			MabMatrix nr = MabMatrix::RotMatrixY(MabMath::Deg2Rad(angle));
			MabMatrix nm = MabMatrix::TransMatrix(position);
			transform = nr * nm;

			AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, SWIPE_FADE, HIDE_NON_ACTORS);
			AddCinematicTLE(CSEVENT_INJURY_SUBSTITUTION_C, transform, SIDE_NONE);

			/// Setup background cutscenes for 'non-actors'.

			FVector cspos(0, 0, 0);
			cspos = transform.TransformPos(cspos);
			SetupBGReactionCutscenes(cspos, STANDARD_BG_CUTSCENE_SEPARATION, STANDARD_BG_CUTSCENE_SEPARATION);

			AddUserSkipPointElement();
		}

		AddCutsceneCallbackElement(SSCutSceneManager::DoSubstitutionCallback, DO_SUBSTITUTION_CALLBACK, entry->GetUID());
		AddCutsceneCallbackElement(SSCutSceneManager::PerformRemainingInterchangesCallback, PERFORM_REMAINING_INTERS_CALLBACK, entry->GetUID());

	}

	game->GetGameState()->SetPlayRestartTeam(injured_player->GetAttributes()->GetTeam());

	// Set restart position for scrum, keep from sidelines + touch.

	const float INJURY_RESTART_FIELD_CLAMP_X = 5.0f;
	const float INJURY_RESTART_FIELD_CLAMP_Z = 5.0f;

	FVector restart_pos = injured_player->GetMovement()->GetCurrentPosition();
	game->GetSpatialHelper()->ClampWaypointToTryLine(restart_pos, INJURY_RESTART_FIELD_CLAMP_X);
	game->GetSpatialHelper()->ClampWaypointToSideLine(restart_pos, INJURY_RESTART_FIELD_CLAMP_Z);

	game->GetGameState()->SetPlayRestartPosition(restart_pos);

	//------------------------

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);

	// check if our pro was sent off, if he was then don't care about the penalty callback, do a pro sent off callback instead
    if ((SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro() &&
        SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(injured_player)) || sub_manager->GetIsProPlayerSendOffEventQueued())
	{
		pro_player_sent_off_during_interchanges = true;
		AddCutsceneCallbackElement(SSCutSceneManager::ProSentOffCallback, PRO_SENT_OFF_CALLBACK);
		suspended_callback_from_pro_send_off = SSCutSceneManager::InjuryEndCallback;
		suspended_callback_from_pro_send_off_debug_name = INJURY_END_CALLBACK;
	}
	else
		AddCutsceneCallbackElement(SSCutSceneManager::InjuryEndCallback, INJURY_END_CALLBACK);

	return true;
}

///-------------------------------------------------------------------------------
/// Called on event 'game_events->cutscene_kickforpoints'
///-------------------------------------------------------------------------------

void SSCutSceneManager::StartKickForPointsCutscene(RUTeam *team)
{
	MABLOGDEBUG("StartKickForPointsCutscene: %s", team->GetDbTeam().GetShortName());

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);

	if (cinematics_enabled)
	{
		FVector position = game->GetGameState()->GetPlayRestartPosition();
		MabMatrix transform = MabMatrix::TransMatrix(position);
		MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);

		if (team->GetPlayDirection() == ERugbyPlayDirection::SOUTH)
			transform = rot_transform * transform;

		AddScreenWipeElement();

		AddCutsceneCinematicElement(RUGamePhase::REACTION_CUTSCENE, ENABLE_SKIP, SWIPE_CUT, SHOW_NON_ACTORS);			// kick for points - not sure yet.

		AddCinematicTLE(CSEVENT_PRE_KICKFORPOINTS, transform, SIDE_NONE);

		AddUserSkipPointElement();
	}

	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);
	AddCutsceneCallbackElement(SSCutSceneManager::StartKickPenaltyShootForGoalCallback, START_PENALTY_SHOOT_CALLBACK);
}

///-------------------------------------------------------------------------------
/// Called on event 'game_events->drop_goal_detected'
///-------------------------------------------------------------------------------

void SSCutSceneManager::DropGoalSuccessCutsceneBegin()
{
	ConversionCutSceneBegin(true, true);

	if (replays_enabled)
	{
		AddScreenWipeElement(SWIPE_WIPE);
		AddCutsceneReplayElement(NULL, REPLAY_CAMERA_MODE_MAIN, DROPGOAL_MAX_REWIND_TIME, RT_DROP_GOAL, SWIPE_WIPE);
		AddUserSkipPointElement();
	}
}

///-------------------------------------------------------------------------------
/// Called on event 'game_events->cutscene_dropout'
///-------------------------------------------------------------------------------

void SSCutSceneManager::DropOutCutsceneBegin()
{
	if (game->GetGameTimer()->IsExpired() && !game->GetGameTimer()->GetHalfObserver()->GetIgnoreNextDropOut())
	{
		// See SSGameTimerHalfObserver::NotifyDropOut(), same if statement so HalfEnd() will have been called.
		// Don't play the pre-dropout cutscene if we are at half or full time.
		return;
	}

	{
		AddScreenWipeElement(SWIPE_CUT);
		AddCutsceneCallbackElement(SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK);
		AddCutsceneCallbackElement(SSCutSceneManager::StartDropoutCallback, START_DROPOUT_CALLBACK);

		if (cinematics_enabled)
		{
			RUGameState *game_state = game->GetGameState();
			RUTeam *restart_team = game_state->GetPlayRestartTeam();

			FVector position = game->GetGameState()->GetPlayRestartPosition();
			MabMatrix transform = MabMatrix::TransMatrix(position);
			MabMatrix rot_transform = MabMatrix::RotMatrixY(PI);

			if (restart_team->GetPlayDirection() == ERugbyPlayDirection::SOUTH)
				transform = rot_transform * transform;

			AddCutsceneCinematicElement(RUGamePhase::DROPOUT, ENABLE_SKIP, SWIPE_CUT, bg_cutscenes_enabled ? SHOW_NON_ACTORS_APPROVED : SHOW_NON_ACTORS);		// ok.
			AddCinematicTLE(CSEVENT_PRE_DROPOUT, transform, restart_team->GetSide());

			AddUserSkipPointElement();
		}
		AddCutsceneCallbackElement(SSCutSceneManager::EndDropoutCallback, END_DROPOUT_CALLBACK);
	}
	AddCutsceneCallbackElement(SSCutSceneManager::CutsceneEndCallback, CUTSCENE_END_CALLBACK);
}




///***************************************************************************************************************************************************
///***************************************************************************************************************************************************
///***************************************************************************************************************************************************


///-------------------------------------------------------------------------------
/// Callback : Try awarded after TMO.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::AwardTryAfterTMOCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->GetGame();
	RUGameEvents *game_events = game->GetEvents();
	RUGameState *game_state = game->GetGameState();

	///---------------------------------

	ARugbyCharacter* scorer = game_state->GetLastTryAttempter();

	game_state->SetLastTryScorer(scorer);
	game_state->SetTryScorePosition(game_state->GetTryAttemptPosition());
	game_state->SetLastTryScorerHuman(game_state->GetLastTryAttempterHuman());

	MABLOGDEBUG("AwardTryAfterTMOCallback - SetShootForGoalHuman");
	game_state->SetShootForGoalHuman(game_state->GetLastTryAttempterHuman());

	RUTeam *restart_team = scorer->GetAttributes()->GetTeam(); // gotta do a conversion
	float play_dir = (float)restart_team->GetPlayDirection();

	FieldExtents extents_nig = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	extents_nig.y *= 0.5f;
	FVector restart_position = FVector(game_state->GetTryScorePosition().x, 0.0f, (extents_nig.y - 22.0f) * play_dir);
	game_state->SetPlayRestartPosition(restart_position);
	game_state->SetPlayRestartTeam(restart_team);

	///---------------------------------

	game_events->try_result(true, false, scorer);

	game_events->try_awarded.Remove(manager, &SSCutSceneManager::LaunchTryCutscene);			// Don't want to fire off try cutscene.
	game_events->try_awarded();
	game_events->try_awarded.Add(manager, &SSCutSceneManager::LaunchTryCutscene);

	game_events->video_referee_decision(true);

	game->GetBall()->ResetTryCheck();

	return true;
}


bool SSCutSceneManager::CommentaryTMOTryDisallowed(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->GetGame();
	RUGameEvents *game_events = game->GetEvents();
	game_events->video_referee_decision(false);

	// Set defending team so cutscene plays on correct team. (using Try commiseration)
	game->GetGameState()->SetAttackingTeam(game->GetGameState()->GetDefendingTeam());

	return true;
}

///-------------------------------------------------------------------------------
/// Callback : Try not awarded after TMO, 5m scrum.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::DisallowTryAfterTMOCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->GetGame();
	RUGameState *game_state = game->GetGameState();

	ARugbyCharacter* attempter = game_state->GetLastTryAttempter();

	// When a player carrying the ball is held up in the in-goal so that the player cannot ground
	//			the ball, the ball is dead. A 5-metre scrum is formed. This would apply if play similar to a
	//			maul takes place in in-goal. The attacking team throws in the ball.*/

	FVector position = game_state->GetTryAttemptPosition();
	FieldExtents extents_nig = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	extents_nig.y *= 0.5f;
	MabMath::Clamp(position.z, -(extents_nig.y - 5.0f), (extents_nig.y - 5.0f));

	game_state->SetPlayRestartPosition(position);

	/// If the ball was knocked on the the last try attempter then the other team gets the feed
	game_state->SetPlayRestartTeam(attempter->GetAttributes()->GetTeam());
	if (game->GetGameState()->GetBallHolder() == NULL)
	{
		BallFreeInfo bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
		bool was_knocked_on_by_scorer = bfi.last_player == attempter && bfi.event == BFE_KNOCKON;
		if (was_knocked_on_by_scorer)
			game_state->SetPlayRestartTeam(attempter->GetAttributes()->GetOppositionTeam());
	}

	// GGs JZ on fith tackle scrum should be a handover, 
	// We can't check if 5th tackle in scrum becuase set attacking team resets it,
	// So if it is 5th tackle set the team and count back up to 5th tackle
	if (!game_state->IsFifthTackle())
	{
		game_state->SetAttackingTeam(game_state->GetPlayRestartTeam());
	}
	else
	{
		game_state->SetAttackingTeam(game_state->GetPlayRestartTeam());
		for (int i = 0; i < 5; i++)
		{
			game_state->IncrementTackleCount();
		}
		game_state->SetHandoverType(EHandoverType::PENALTY);
	}
	///---------------------
	// turn on game rules.

	game->GetRules()->SuspendPlay(false, "SSCutSceneManager::DisallowTryAfterTMOCallback");
	game->GetRules()->EnableConsequences(true);
	game->GetRules()->EnableTriggers(RURT_MAINGAMERULES);

	game->GetRules()->StartConsequence(RUC_SCRUM);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback : Called to start 22 dropout.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::StartDropoutCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState *game_state = game->GetGameState();

	game->GetStrategyHelper()->ClearRoles(game->GetTeam(0));
	game->GetStrategyHelper()->ClearRoles(game->GetTeam(1));

	/// Set the focus player to the kicker.

	RUTeam *restart_team = game_state->GetPlayRestartTeam();
	manager->SetFocusPlayer(restart_team->GetPlayKicker(), restart_team->GetSide());

	game_state->SetPhase(RUGamePhase::PRE_DROPOUT);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback : Called to start 22 dropout.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::EndDropoutCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState *game_state = game->GetGameState();

	game_state->SetPhase(RUGamePhase::DROPOUT);
	game->GetEvents()->kick_restart();

	return true;
}



///-------------------------------------------------------------------------------
/// Callback : Called to start penalty shoot for goal.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::StartKickPenaltyShootForGoalCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState *game_state = game->GetGameState();

	game_state->SetPhase(RUGamePhase::PENALTY_SHOOT_FOR_GOAL);

	return true;
}


///-------------------------------------------------------------------------------
/// Callback : Called before the players walkon
///-------------------------------------------------------------------------------

bool SSCutSceneManager::PlayerWalkonCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	MABUNUSED(manager);

	/// Disable boundry check for walkons.
	manager->SetEnableBoundryCheck(false);

	SIFApplication::GetApplication()->GetActiveGameWorld()->GetEvents()->player_walk_on();

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: HoldScreenWipeCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::HoldScreenWipeCallback(SSCutSceneManager *manager, CutsceneCallbackElement *element)
{
	manager->GetGame()->GetScreenWipeManager()->HoldScreenWipe((float)element->user_info * 0.5f);
	return true;
}

///-------------------------------------------------------------------------------
/// Callback: CutsceneEndCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::CutsceneEndCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	wwNETWORK_TRACE_JG("SSCutSceneManager::CutsceneEndCallback");
	manager->OnCutSceneEnd();
	return true;
}

///-------------------------------------------------------------------------------
/// Callback: CutsceneStartCallback
///-------------------------------------------------------------------------------


bool SSCutSceneManager::CutsceneStartCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	manager->OnCutSceneStart();
	return true;
}

///-------------------------------------------------------------------------------
/// Callback: StartConversionPhase
///-------------------------------------------------------------------------------

bool SSCutSceneManager::ConversionBallPlacementCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState *game_state = game->GetGameState();

	game->GetEvents()->commentary_conversion_transition(game_state->GetPlayRestartTeam()->GetGoalKicker());
	return true;
}

///-------------------------------------------------------------------------------
/// Callback: StartConversionPhase
///-------------------------------------------------------------------------------

bool SSCutSceneManager::StartConversionPhaseCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState *game_state = game->GetGameState();

	game_state->SetBallHolder(game_state->GetPlayRestartTeam()->GetGoalKicker());
	game_state->SetPhase(RUGamePhase::CONVERSION);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: StartTryCutscenePhaseCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::StartTryCutscenePhaseCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState *game_state = game->GetGameState();
	game_state->SetPhase(RUGamePhase::TRY_CUTSCENE);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: StartPostConversionPhaseCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::StartPostConversionPhaseCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState  *game_state = game->GetGameState();

	game_state->SetPhase(RUGamePhase::POST_CONVERSION);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: StartPreKickOffPhaseCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::StartPreKickOffPhaseCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState  *game_state = game->GetGameState();
	RUTeam *restart_team = game_state->GetPlayRestartTeam();

	game_state->SetAttackingTeam(restart_team);
	game_state->SetPhase(RUGamePhase::PRE_KICK_OFF);

	///------------------------------------------------------

	if (!game->GetGameTimer()->IsExpired())
	{
		if (game->GetSubstitutionManager()->GetNumQueuedEvents()>0)
		{
			manager->SetupInterchange();
		}

		if (manager->cinematics_enabled)
		{
			const float angle = restart_team->GetPlayDirection() == ERugbyPlayDirection::NORTH ? 0.0f : 180.0f;
			MabMatrix transform = MabMatrix::RotMatrixY(MabMath::Deg2Rad(angle));

			manager->AddCutsceneCallbackElement(SSCutSceneManager::SetKickOffKickerCallback, SETKICK_OFF_KICKER_CALLBACK);
			manager->AddCutsceneCinematicElement(RUGamePhase::KICK_OFF, ENABLE_SKIP, SWIPE_CUT, manager->bg_cutscenes_enabled ? SHOW_NON_ACTORS_APPROVED : SHOW_NON_ACTORS);		// pre-kickoff - ok.
			manager->AddCinematicTLE(CSEVENT_PRE_KICKOFF, transform, restart_team->GetSide());
		}

		manager->AddUserSkipPointElement();
	}

	/// Hold screen wipe on fully faded for 1 second, to give halftime a chance to fire.
	manager->AddCutsceneCallbackElement(SSCutSceneManager::HoldScreenWipeCallback, HOLD_SCREENWIPE_CALLBACK, 1);

	// check if our pro was sent off, if he was then don't care about the penalty callback, do a pro sent off callback instead
	if (manager->pro_player_sent_off_during_interchanges)
	{
		manager->AddCutsceneCallbackElement(SSCutSceneManager::ProSentOffCallback, PRO_SENT_OFF_CALLBACK);
		manager->suspended_callback_from_pro_send_off = SSCutSceneManager::StartKickOffPhaseCallback;
		manager->suspended_callback_from_pro_send_off_debug_name = START_KICKOFF_PHASE_CALLBACK;
	}
	else
		manager->AddCutsceneCallbackElement(SSCutSceneManager::StartKickOffPhaseCallback, START_KICKOFF_PHASE_CALLBACK);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: StartConversionPhase
///-------------------------------------------------------------------------------

bool SSCutSceneManager::StartKickOffPhaseCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState *game_state = game->GetGameState();

	game_state->SetPhase(RUGamePhase::KICK_OFF);
	game->GetEvents()->kick_restart_start();

	CutsceneEndCallback(manager, NULL);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: StartPostGamePhaseCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::StartPostGamePhaseCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState *game_state = game->GetGameState();

	game_state->SetPhase(RUGamePhase::POST_GAME);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: StartSimulationPhaseCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::StartSimulationPhaseCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState *game_state = game->GetGameState();

	game_state->SetPhase(RUGamePhase::SIMULATION);

	SIFUIHelpers::SetCurrentWindow("IngameSimulation");

	return true;
}

#ifdef ENABLE_SEVENS_MODE
///-------------------------------------------------------------------------------
/// Callback: StartExtraTimeCoinTossPhaseCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::StartExtraTimeCoinTossPhaseCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	// Step 1, set the game phase
	SIFGameWorld *game = manager->game;
	RUGameState *game_state = game->GetGameState();

	game_state->SetPhase(RUGamePhase::EXTRA_TIME_TOSS);

	// Step 3, launch the window to display stuff
	SIFUIHelpers::SetCurrentWindow("CoinTossDecision");

	// Step 2, Start up the next phase
	RUGamePhaseExtraTimeToss* phase = game->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();
	phase->InitialiseDecisionMaker();
	//phase->StartNextPhase();

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: StartPostExtraTimeCoinTossPhaseCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::StartPostExtraTimeCoinTossPhaseCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	MABBREAKMSG("DEPRECATED");
	SIFGameWorld *game = manager->game;
	RUGamePhaseExtraTimeToss* phase = game->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();
	phase->StartNextPhase();

	if (phase->GetPhase() == CTP_LAST)
	{
		game->GetEvents()->cutscene_extra_time_coin_toss_finished();
	}
	else
	{
		//game->GetEvents()->cutscene_extra_time_coin_toss_start_context();
		//game->GetEvents()->change_to_camera( GAME_CAM_EXTRA_TIME_COIN_TOSS_H_OR_T );

		SSHumanPlayer* att_human = NULL;
		SSHumanPlayer* def_human = NULL;
		SSHumanPlayer* nextPlayer = NULL;

		// Get the the defending and the attacking team
		RUTeam* def_team = game->GetGameState()->GetDefendingTeam();
		RUTeam* att_team = game->GetGameState()->GetAttackingTeam();

		// Go through all of the human players in each of the respective teams and pick the first human player
		def_human = NULL;
		if (def_team)
		{
			for (int i = 0; i < NUM_HUMAN_PLAYERS_PER_TEAM; ++i)
			{
				SSHumanPlayer* tmp_human = def_team->GetHumanPlayer(i);
				if (tmp_human && !tmp_human->IsNetworkPlayer())
				{
					def_human = tmp_human;
					break;
				}
			}
		}

		att_human = NULL;
		if (att_team)
		{
			for (int i = 0; i < NUM_HUMAN_PLAYERS_PER_TEAM; ++i)
			{
				SSHumanPlayer* tmp_human = att_team->GetHumanPlayer(i);
				if (tmp_human && !tmp_human->IsNetworkPlayer())
				{
					att_human = tmp_human;
					break;
				}
			}
		}

		bool hasHumanPlayer = def_human != NULL || att_human != NULL;

		// Work out who should make the next decision
		SSTEAMSIDE nextDecisionMaker = SIDE_NONE;

		// CPU is playing alone, don't need a decision maker or screen context
		if (!hasHumanPlayer)
		{
			// Even if there are no human players, we still need a side
			nextDecisionMaker = phase->GetDecisionMakerForNextPhase();
			if (nextDecisionMaker == SIDE_NONE) nextDecisionMaker = SIDE_A;

			phase->SetCurrentDecisionMaker(nextDecisionMaker/*, true*/);
		}
		else
		{
			// Work out who we think should make the next decision
			nextDecisionMaker = phase->GetDecisionMakerForNextPhase();
			MABASSERT(nextDecisionMaker == SIDE_A || nextDecisionMaker == SIDE_B || nextDecisionMaker == SIDE_NONE);

			// next decision maker is no one, just pick the first human player
			if (nextDecisionMaker == SIDE_NONE)
			{
				if (att_human != NULL)
					nextPlayer = att_human;
				else
					nextPlayer = def_human;

				// Get a non NONE side from the real next player
				nextDecisionMaker = nextPlayer->GetTeam()->GetSide();
			}
			else
			{
				// Which human was the next decision maker side? And are they actually human
				if (att_human != NULL && att_human->GetTeam()->GetSide() == nextDecisionMaker)
					nextPlayer = att_human;
				else if (def_human != NULL && def_human->GetTeam()->GetSide() == nextDecisionMaker)
					nextPlayer = def_human;
			}

			// Use the data we have and set the decision maker and the context
			phase->SetCurrentDecisionMaker(nextDecisionMaker/*, nextPlayer == NULL*/);
		}
	}
	return true;
}

///-------------------------------------------------------------------------------
/// Callback: CoinTossDecisionWindowCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::CoinTossDecisionWindowCallback(SSCutSceneManager *, CutsceneCallbackElement *)
{
	//MABBREAKMSG("DEPRECATED");
	SIFUIHelpers::SetCurrentWindow("CoinTossDecision");

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: CoinTossResultWindowCallback
///-------------------------------------------------------------------------------

/*bool SSCutSceneManager::CoinTossResultWindowCallback(SSCutSceneManager *, CutsceneCallbackElement *)
{
	MABBREAKMSG("DEPRECATED");
	//Commenting this out because it never seems to get used, but I'd like to avoid it happening anyway
	//SIFUIHelpers::SetCurrentWindow("CoinTossResult");

	return true;
}*/
#endif //ENABLE_SEVENS_MODE

///-------------------------------------------------------------------------------
/// Callback: FullTimeWindowCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::FullTimeWindowCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	//< Prevents info panels reappearing fulltime through an animation when closing the halftime window. >
	if (manager && manager->game && manager->game->GetHUDUpdater())
	{
		manager->game->GetHUDUpdater()->CancelActiveInfoPanels();
	}

	if (SIFApplication::GetApplication())
	{
		SIFApplication::GetApplication()->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::FulltimeWindow);
	}

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: HalfTimeWindowCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::HalfTimeWindowCallback(SSCutSceneManager *manager, CutsceneCallbackElement *callback_element)
{
	//< Prevents info panels reappearing halfway through an animation when closing the halftime window. >
	if (manager && manager->game && manager->game->GetHUDUpdater())
	{
		manager->game->GetHUDUpdater()->CancelActiveInfoPanels();
	}

	//< Push halftime window. >
	if (SIFApplication::GetApplication())
	{
		if (manager && manager->game)
		{
			manager->game->GetCutSceneManager()->SetDisableCutSceneSkip(true);
		}

		SIFApplication::GetApplication()->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::HalftimeWindow);
	}

	// Reset player fatigue during the half-time break.
	SIFGameWorld* game = manager->GetGame();
	size_t player_idx = 0;
	while (ARugbyCharacter* player = game->GetPlayer((int)player_idx))
	{
		const float MAXIMUM_STAMINA_GAIN = 0.5f;
		const float MINIMUM_STAMINA_GAIN = 0.2f;
		const float STAMINA_RANGE = MAXIMUM_STAMINA_GAIN - MINIMUM_STAMINA_GAIN;
		const float MAX_FITNESS = 10000.0f;

		float fitness = 5000.0f;
		if (player && player->GetAttributes() && player->GetAttributes()->GetDBPlayer())
		{
			fitness = player->GetAttributes()->GetDBPlayer()->fitness;
		}

		float stamina_gain = MINIMUM_STAMINA_GAIN + ((fitness / MAX_FITNESS) * STAMINA_RANGE);

		player->GetAttributes()->IncreaseStamina(stamina_gain, 0.0f, false);
		++player_idx;
	}

	RUSubstitutionManager *sub_manager = game->GetSubstitutionManager();
	if (callback_element->callback_state == 0)
	{
		sub_manager->StartAllPendingInterchanges(true);
		callback_element->callback_state++;
	}

	return sub_manager->HavePendingInterchangesCompleted();
}

///-------------------------------------------------------------------------------
/// Callback: ProSentOffWindowCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::ProSentOffWindowCallback(SSCutSceneManager *, CutsceneCallbackElement *)
{
	//SIFUIHelpers::SetCurrentWindow("ProSentOffWindow");
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::ProSentOffWindow);
	}

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: StartFreeKickCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::StartFreeKickCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	manager->game->GetEvents()->free_kick_awarded(NULL);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: RestartKickCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::RestartKickCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	///--------------------------------------------------
	// Safety, clear all cutscene elements.

	manager->ClearCutSceneElements();

	///--------------------------------------------------

	// Re-enable boundry checks after walkon.
	manager->SetEnableBoundryCheck(true);

	SIFGameWorld *game = manager->game;

	RUGameSettings* settings = &game->GetGameSettings();
	RUGameState* state = game->GetGameState();

	// Nick  WWS 7s to Womens //
/*#ifdef ENABLE_SEVENS_MODE
	// We'll deem this a rugby sevens game, if it's a R7 game mode, and if we're in extra time mode, since thats the only time we care about the other data
	bool isRugbySevensGame = game->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_SEVENS &&
		(manager->next_extratime_mode == FIRST_EXTRA_TIME || manager->next_extratime_mode == SECOND_EXTRA_TIME || manager->next_extratime_mode == GOLDEN_POINT);

	// Rugby sevens we have the initial team stored in a different variable, because the initial values will be different depending on coin toss
	SSTEAMSIDE restart_team_side = isRugbySevensGame ? settings->game_settings.initial_kickoff_team_extra_time_coin_toss : settings->game_settings.initial_kickoff_team;
#else*/
	SSTEAMSIDE restart_team_side = settings->game_settings.initial_kickoff_team;
//#endif

	if (manager->next_half == SECOND_HALF)
	{
		restart_team_side = OtherTeam(restart_team_side);
	}
	state->SetPlayRestartTeam(game->GetTeam(restart_team_side));

	ARugbyCharacter* restart_kicker = state->GetPlayRestartTeam()->GetPlayKicker();
	state->SetBallHolder(restart_kicker);
	state->SetPlayRestartPosition(FVector(0.0f, 0.0f, 0.0f));

	
	// Update sin bin timers to account for added on time from the previous half. This needs to be done before the timers get reset.
	if (RUSubstitutionManager *sub_manager = game->GetSubstitutionManager())
	{
		sub_manager->RollOverSinBinTimers();
	}

	/// Setup game timer for next half.

	SSGameTimer *timer = game->GetGameTimer();

	if (manager->next_extratime_mode == FIRST_EXTRA_TIME)
		timer->ResetTotalTime();					// Starts time from zero again.
	else
		timer->Reset();

	timer->SetHalf(manager->next_half);
	timer->SetExtraTimeMode(manager->next_extratime_mode);
	timer->SetHalfLength(game->GetGameSettings().game_settings.game_length, manager->next_extratime_mode);

	if (timer->GetExtraTimeMode() == SECOND_EXTRA_TIME)
	{	/// Normally done by UI, but no UI in extra time :(
		timer->RollBackTimerToHalfLength();
	}

	const int direction = (manager->next_half == SECOND_HALF) ? -1 : 1;
	const std::vector<std::unique_ptr<RUTeam>> &teams = game->GetTeams();
	for (size_t i = 0; i < teams.size(); ++i)
	{
		// Rugby sevens we have the initial team stored in a different variable, because the initial values will be different depending on coin toss
		const ERugbyPlayDirection dir = settings->game_settings.initial_play_direction[i]; // Nick  WWS 7s to Womens // isRugbySevensGame ? settings->game_settings.initial_play_direction_extra_time_coin_toss[i] : settings->game_settings.initial_play_direction[i];

		teams[i]->SetPlayDirection(ERugbyPlayDirection(int(dir) * direction));
		teams[i]->Reset();
	}

	CutsceneEndCallback(manager, NULL);

	game->RestartKick();

#if PLATFORM_SWITCH
	if (game->GetGameSettings().game_settings.network_game)
	{
		manager->cinematics_enabled = false;
	}
#endif
	
	return true;
}

///-------------------------------------------------------------------------------
/// Callback: TeamManagementCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::TeamManagementCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	// #TeamLineupDisableHere
	if (SIFApplication::GetApplication())
	{
		if (manager && manager->game)
		{
			manager->game->GetCutSceneManager()->SetDisableCutSceneSkip(true);
		}
		SIFApplication::GetApplication()->DealMenuAction(SCREEN_OPEN_MENUSCREEN, FString("TeamLineup"));
	}
	return true;
}

///-------------------------------------------------------------------------------
/// Callback: PreHakaCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::PreHakaCallback(SSCutSceneManager *manager, CutsceneCallbackElement*)
{
	manager->StartHakaCutsceneTimer();
#ifndef DISABLE_COMMENTARY
	SIFApplication::GetApplication()->GetCommentarySystem()->GetPreMidPostDirector()->ProgressPreMatch(RUPMPDirector::HAKA);
#endif
	return true;
}


///-------------------------------------------------------------------------------
/// Callback: PostHakaCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::PostHakaCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
#ifndef DISABLE_COMMENTARY
	SIFApplication::GetApplication()->GetCommentarySystem()->GetPreMidPostDirector()->ProgressPreMatch(RUPMPDirector::POST_HAKA);
#endif
	manager->SetPlayingHaka(false);
	manager->ClearHakaCutsceneTimer();
	return true;
}

///-------------------------------------------------------------------------------
/// Callback: PenaltyCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::PenaltyCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;

	// Start up interface, then use the AI decision
	// UI Handled by Rules System, centralising it

	RUGameState *game_state = game->GetGameState();

	game_state->SetAttackingTeam(game_state->GetPlayRestartTeam());
	game->GetInputManager()->GetDecisionInterface()->SetRenderTillResolved(game->GetRules(), game->GetGameState()->GetPlayRestartTeam(), true);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: ForwardPassCallback - start scrum after forward pass cutscene.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::ForwardPassCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState* state = game->GetGameState();

	// GGs JZ on fith tackle scrum should be a handover, 
	// We can't check if 5th tackle in scrum becuase set attacking team resets it,
	// So if it is 5th tackle set the team and count back up to 5th tackle
	if (!state->IsFifthTackle())
	{
		state->SetAttackingTeam(state->GetPlayRestartTeam());
	}
	else
	{
		state->SetAttackingTeam(state->GetPlayRestartTeam());
		for (int i = 0; i < 5; i++)
		{
			state->IncrementTackleCount();
		}
		state->SetHandoverType(EHandoverType::PENALTY);
	}

	game->GetRules()->StartConsequence(RUC_SCRUM);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: MarkAwardedCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::MarkAwardedCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;

	// Start up interface, then use the AI decision
	// UI Handled by Rules System, centralising it

	RUGameState *game_state = game->GetGameState();

	game_state->SetAttackingTeam(game_state->GetPlayRestartTeam());
	game->GetInputManager()->GetDecisionInterface()->SetRenderTillResolved(game->GetRules(), game->GetGameState()->GetPlayRestartTeam(), true);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: FourtyTwenty / TwentyFourty callback - cutscene is the same for both
///-------------------------------------------------------------------------------
bool SSCutSceneManager::FourtyTwentyKickAwardedCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	SIFGameWorld* game = manager->game;

	RUGameState* game_state = game->GetGameState();

	// NEED TO GO INTO TAP RESTART HERE !
	game_state->SetAttackingTeam(game_state->GetPlayRestartTeam());
	game->GetRules()->StartConsequence(RUC_PENALTY_TAP);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: RestartOutOnFullCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::RestartOutOnFullCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;

	// Start up interface, then use the AI decision
	// UI Handled by Rules System, centralising it

	RUGameState *game_state = game->GetGameState();

	RUTeam* restart_team = game_state->GetPlayRestartTeam();
	ARugbyCharacter* restart_play_kicker = restart_team->GetPlayKicker();
	game->GetGameState()->SetBallHolder(restart_play_kicker);

	game_state->SetAttackingTeam(restart_team);
	game->GetInputManager()->GetDecisionInterface()->SetRenderTillResolved(game->GetRules(), restart_team, true);

	return true;
}


///-------------------------------------------------------------------------------
/// Callback: RestartKickOffIntoGoalCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::RestartKickOffIntoGoalCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;

	// Start up interface, then use the AI decision
	// UI Handled by Rules System, centralising it

	RUGameState *game_state = game->GetGameState();

	RUTeam* restart_team = game_state->GetPlayRestartTeam();
	ARugbyCharacter* restart_play_kicker = restart_team->GetPlayKicker();
	game->GetGameState()->SetBallHolder( restart_play_kicker );

	game_state->SetAttackingTeam( restart_team );
	game->GetInputManager()->GetDecisionInterface()->SetRenderTillResolved(game->GetRules(), restart_team, true);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: RestartFreeKickOutOnFullCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::RestartFreeKickR7KickOffPenaltyCallback(SSCutSceneManager* manager, CutsceneCallbackElement* )
{
	SIFGameWorld *game = manager->game;

	// Start up interface, then use the AI decision
	// UI Handled by Rules System, centralising it

	RUGameState *game_state = game->GetGameState();

	RUTeam* restart_team = game_state->GetPlayRestartTeam();
	ARugbyCharacter* restart_play_kicker = restart_team->GetPlayKicker();
	game->GetGameState()->SetBallHolder( restart_play_kicker );

	game_state->SetAttackingTeam( restart_team );
	game->GetInputManager()->GetDecisionInterface()->SetRenderTillResolved(game->GetRules(), restart_team, true);

	return true;

	/*SIFGameWorld *game = manager->game;
	//MABASSERT(game->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_SEVENS);
	const BallFreeInfo& bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();

	// Just make sure it was actually from a kick off >_>
	MABASSERT(bfi.sub_type == KICKTYPE_KICKOFF);

	RUTeam* kicker_team = bfi.last_player->GetAttributes()->GetTeam();
	RUTeam* restart_team = bfi.last_player->GetAttributes()->GetOppositionTeam();
	FVector restart_position = FVector::ZERO;// Always from half way

	RUGameEvents* events = game->GetEvents();

	RUInputPhaseDecision* decisions = game->GetInputManager()->GetDecisionInterface();
	RUDecisionConsequence con_scrum;
	RUDecisionConsequence con_rekick;
	RUDecisionConsequence con_tap;

	bool sevens = game->GetGameSettings().game_settings.GameModeIsR7();
	// HES #9887, want the decisions back in again, but also add tap
	if (sevens == GAME_MODE_SEVENS)
	{
		// TAP DECISION
		con_tap.decision = RUC_PENALTY_TAP;
		con_tap.restart_team = restart_team;
		/// Tap should be on the halfway
		con_tap.restart_position = restart_position;
		con_tap.restart_player = NULL;
		con_tap.decision_event = &events->free_kick_tap_decision;
		decisions->AddConsequence(con_tap);
	}

	{	// SCRUM DECISION
		con_scrum.decision = RUC_SCRUM;
		con_scrum.restart_team = restart_team;
		/// Scrum should be on the halfway
		con_scrum.restart_position = restart_position;
		con_scrum.restart_player = NULL;
		con_scrum.decision_event = &events->restart_out_full_scrum_decision;
		decisions->AddConsequence(con_scrum);
	}

	{	// REKICK DECISION
		con_rekick.decision = RUC_REKICK;
		con_rekick.restart_team = sevens ? restart_team : kicker_team;
		con_rekick.restart_position = restart_position;
		con_rekick.restart_player = NULL;
		con_rekick.decision_event = &events->restart_out_full_rekick_decision;
		decisions->AddConsequence(con_rekick);
	}

	//decision_required = true;

	//rules->SetTrigger(this);

	game->GetGameState()->SetAttackingTeam(restart_team);
	game->GetInputManager()->GetDecisionInterface()->SetRenderTillResolved(game->GetRules(), restart_team, true);

	return true;*/
}


///-------------------------------------------------------------------------------
/// Callback: BallDeadCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::BallDeadCallback(SSCutSceneManager* manager, CutsceneCallbackElement* elem)
{
	SIFGameWorld *game = manager->game;

	manager->have_queued_ball_dead_cutscene = false;			// Can now queue another ball dead cutscene.

																// Start up interface, then use the AI decision
																// UI Handled by Rules System, centralising it
	RUGameState *game_state = game->GetGameState();

	RUInputPhaseDecision* decisions = game->GetInputManager()->GetDecisionInterface();
	if (decisions->GetNumConsequences() > 0)
	{
		RUTeam* restart_team = game_state->GetPlayRestartTeam();
		ARugbyCharacter* restart_play_kicker = restart_team->GetPlayKicker();
		game->GetGameState()->SetBallHolder(restart_play_kicker);
		game_state->SetAttackingTeam(restart_team);
		decisions->SetRenderTillResolved(game->GetRules(), restart_team, true);
	}
	else
	{
		RURuleConsequence consequence = (RURuleConsequence)elem->user_info;
		game->GetRules()->StartConsequence(consequence);
	}

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: Start scrum after knockon.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::KnockonCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->game;
	RUGameState* state = game->GetGameState();

	// GGs JZ on fith tackle scrum should be a handover, 
	// We can't check if 5th tackle in scrum becuase set attacking team resets it,
	// So if it is 5th tackle set the team and count back up to 5th tackle
	if (!state->IsFifthTackle())
	{
		state->SetAttackingTeam(state->GetPlayRestartTeam());
	}
	else
	{
		state->SetAttackingTeam(state->GetPlayRestartTeam());
		for (int i = 0; i < 5; i++)
		{
			state->IncrementTackleCount();
		}
		state->SetHandoverType(EHandoverType::PENALTY);
	}

	game->GetRules()->StartConsequence(RUC_SCRUM);

	return true;
}

///-------------------------------------------------------------------------------
/// Callback: Start a lineout.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::StartLineoutCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	// WJS RLC ##### Should never go to line out in
	// a league game
	MABASSERT(0);

	SIFGameWorld *game = manager->game;
	RUGameState* state = game->GetGameState();

	//Ensure restart team stays the same as the decision made in RURuleTriggerBallout.
	//Exception is decisions forced by bad throw ins, penalties, etc.
	if (s_pRestartTeam)
	{
		game->GetGameState()->SetPlayRestartTeam(s_pRestartTeam);
	}

	game->GetGameState()->SetAttackingTeam(state->GetPlayRestartTeam());

	/// Lineout camera is a bit slow and is out of position for a frame or two, so hold the
	/// screen wipe.
	game->GetScreenWipeManager()->HoldScreenWipe(0.5f);

	if (s_pRestartTeam && s_pRestartTeam->GetNumHumanPlayers() > 0)
	{
		//In a sevens game, just go straight to the line out, we dont want a decision
		// Nick  WWS 7s to Womens //
/*#ifdef ENABLE_SEVENS_MODE
		if (game->GetGameSettings().game_settings.GetGameMode() == GAME_MODE_SEVENS)
			state->SetPhase(RUGamePhase::LINEOUT);
		else
#endif*/
			state->SetPhase(RUGamePhase::DECIDE_LINEOUT_NUMBERS);
	}
	else
	{
		state->SetPhase(RUGamePhase::LINEOUT);
	}

	if (game->GetHUDUpdater()) 
	{ 
		game->GetHUDUpdater()->HideHUDInfo(); 
	}

	MabString teamName = state->GetAttackingTeam()->GetDbTeam().GetShortName();
	RUHUDUpdater::CensorTeamName(&state->GetAttackingTeam()->GetDbTeam(), teamName);

	// XXX :jb this looks untranslated to me
	MabString message(32, "%s will now throw in", teamName.c_str());
	if (game->GetHUDUpdater())
	{
		game->GetHUDUpdater()->SetScreenMessage("[ID_LINE_OUT]", "[ID_LINE_OUT]", message.c_str(), 1.0f, 3.0f);
	}

	ARugbyCharacter* hooker = state->GetAttackingTeam()->GetPlayerByPosition(PP_HOOKER);
	// TODO_ISMAEL: Ismael. If these lineout features ever get implemented, here are the events bro.
	//game->GetEvents()->lineout_quick_decision
	//game->GetEvents()->lineout_short_decision
	game->GetEvents()->lineout_full_decision(hooker);

	// Decision on which team to throw in is only valid once. Fixes a bug with decisions not being respected for invalid throw ins, penalties, etc.
	s_pRestartTeam = nullptr;

	return true;
}


///-------------------------------------------------------------------------------
/// Callback: Start a scrum
///-------------------------------------------------------------------------------
bool SSCutSceneManager::StartTouchScrumCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	SIFGameWorld* game = manager->game;
	RUGameState* state = game->GetGameState();

	//Ensure restart team stays the same as the decision made in RURuleTriggerBallout.
	//Exception is decisions forced by bad throw ins, penalties, etc.
	if (s_pRestartTeam)
	{
		game->GetGameState()->SetPlayRestartTeam(s_pRestartTeam);
	}

	game->GetGameState()->SetAttackingTeam(state->GetPlayRestartTeam());

	/// Lineout camera is a bit slow and is out of position for a frame or two, so hold the
	/// screen wipe.
	game->GetScreenWipeManager()->HoldScreenWipe(0.5f);

	// GGs JZ do a handover instead of scrum for kicks on the full
	if (game->GetGameState()->GetHandoverType() == EHandoverType::OUTONFULL || game->GetGameState()->GetHandoverType() == EHandoverType::INTOUCH)
	{
		state->SetPhase(RUGamePhase::PLAY_THE_BALL);
	}
	else
	{
		state->SetPhase(RUGamePhase::SCRUM);
	}
	//game->GetRules()->StartConsequence(RUC_SCRUM);

	if (game->GetHUDUpdater())
	{
		game->GetHUDUpdater()->HideHUDInfo();
	}

	MabString teamName = state->GetAttackingTeam()->GetDbTeam().GetShortName();
	RUHUDUpdater::CensorTeamName(&state->GetAttackingTeam()->GetDbTeam(), teamName);

	// Decision on which team to throw in is only valid once. Fixes a bug with decisions not being respected for invalid throw ins, penalties, etc.
	s_pRestartTeam = nullptr;

	

	return true;
}




///-------------------------------------------------------------------------------
/// Callback: ShowInterchangeHUDCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::ShowInterchangeHUDCallback(SSCutSceneManager* manager, CutsceneCallbackElement *)
{
	SIFGameWorld *game = manager->GetGame();
	RUSubstitutionManager *sub_manager = game->GetSubstitutionManager();

	// Show HUD for interchanges.

	int num_pending = sub_manager->GetNumQueuedEvents();
	for (int i = 0; i<num_pending; i++)
	{
		const RUInterchangeEvent *entry = sub_manager->GetQueuedEvent(i);
		if (entry->GetType() == RU_INTERCHANGE_EXCHANGE || entry->GetType() == RU_INTERCHANGE_INJURY)
		{
			const RUDB_PLAYER& primary_player = *entry->GetTeam()->GetDbPlayerById(entry->GetPrimary());
			const RUDB_PLAYER& secondary_player = *entry->GetTeam()->GetDbPlayerById(entry->GetSecondary());
			if (game->GetHUDUpdater())
			{
				game->GetHUDUpdater()->AddSubstitutionInfo(*entry->GetTeam(), primary_player, secondary_player);
			}
		}
	}

	return true;
}

bool SSCutSceneManager::NotifyInterchangeStartedCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	SIFGameWorld *game = manager->GetGame();
	game->GetEvents()->commentary_interchange_started();

	return true;
}

///-------------------------------------------------------------------------------
/// CallBack: StartSubstituteLoadCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::StartSubstituteLoadCallback(SSCutSceneManager* manager, CutsceneCallbackElement *element)
{
	MABLOGDEBUG("StartSubstituteLoadCallback");

	SIFGameWorld *game = manager->GetGame();
	RUSubstitutionManager *sub_manager = game->GetSubstitutionManager();

	RUInterchangeEvent *entry = sub_manager->GetQueuedEventByUID(element->user_info);
	MABASSERT(entry != NULL);
	if (entry)
	{
		manager->SetInterchangeType(entry->GetType());
		manager->SetGoingOffPlayer(entry->GetPrimaryPlayer());
		manager->SetGoingOnPlayer(NULL);
		sub_manager->PerformPendingInterchange(entry, SSCutSceneManager::AsyncLoadSubDone, manager, false);
	}

	return true;
}

///-------------------------------------------------------------------------------
/// CallBack:  MidSubstitutionCallback: Check if sub has been loaded and take action if not. (TODO)
///-------------------------------------------------------------------------------

bool SSCutSceneManager::MidSubstitutionCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	MABLOGDEBUG("MidSubstitutionCallback");

	if (manager->going_on_player == NULL)
	{
		return false;		// Wait. (TODO... force screen wipe or disable rendering???)
	}

	return true;
}

///-------------------------------------------------------------------------------
/// CallBack: DoSubstitutionCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::DoSubstitutionCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	MABLOGDEBUG("DoSubstitutionCallback");

	SIFGameWorld *game = manager->GetGame();
	RUSubstitutionManager *sub_manager = game->GetSubstitutionManager();

	/// Do the swap, and delete the old player.
	sub_manager->SwapPlayerOntoField(manager->GetGoingOnPlayer(), manager->GetGoingOffPlayer());
	manager->SetGoingOffPlayer(NULL);

	return true;
}

///-------------------------------------------------------------------------------
/// Call back from substitution manager, when the player has loaded.
///-------------------------------------------------------------------------------

void SSCutSceneManager::AsyncLoadSubDone(void *user_data, ARugbyCharacter* player, ARugbyCharacter* old_player, bool is_abort)
{
	MABLOGDEBUG("AsyncLoadSubDone");

	MABASSERT(!is_abort);
	SSCutSceneManager *manager = (SSCutSceneManager*)user_data;
	if (!is_abort)
	{
		MABASSERT(player);
		MABASSERT(old_player);

		manager->GetGame()->AppendPlayer(player);	// Enable update of components.

		player->GetState()->SyncUpdate();			// Force dirt update.

		manager->SetGoingOffPlayer(old_player);
		manager->SetGoingOnPlayer(player);
	}
}

///-------------------------------------------------------------------------------
/// Callback: Start of sinbin return, setup player.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::SinbinReturnStartCallback(SSCutSceneManager* manager, CutsceneCallbackElement *element)
{
	SIFGameWorld *game = manager->GetGame();
	RUSubstitutionManager *sub_manager = game->GetSubstitutionManager();

	RUInterchangeEvent *entry = sub_manager->GetQueuedEventByUID(element->user_info);
	MABASSERT(entry != NULL);
	if (entry)
	{
		manager->SetInterchangeType(entry->GetType());
		manager->SetGoingOnPlayer(entry->GetPrimaryPlayer());
		manager->SetGoingOffPlayer(NULL);
		sub_manager->PerformPendingInterchange(entry, NULL, manager);
	}

	return true;
}

///-------------------------------------------------------------------------------
/// Notify commentry system.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::NotifyMidInterchangeCallback(SSCutSceneManager* manager, CutsceneCallbackElement* /*element*/)
{
	manager->GetGame()->GetEvents()->commentary_interchange_made(static_cast<RU_INTERCHANGE_EVENT_TYPE>(manager->GetInterchangeType()), manager->GetGoingOffPlayer(), manager->GetGoingOnPlayer());
	return true;
}

///-------------------------------------------------------------------------------
/// Callback: End of sentoff callback - perform pending substitution,
///-------------------------------------------------------------------------------

bool SSCutSceneManager::SentOffEndCallback(SSCutSceneManager* manager, CutsceneCallbackElement *element)
{
	SIFGameWorld *game = manager->GetGame();
	RUSubstitutionManager *sub_manager = game->GetSubstitutionManager();

	RUInterchangeEvent *entry = sub_manager->GetQueuedEventByUID(element->user_info);
	MABASSERT(entry != NULL);
	if (entry)
	{
		sub_manager->PerformPendingInterchange(entry, NULL, manager);
	}

	return true;
}

///-------------------------------------------------------------------------------
/// Tell substitution manager to do all remaining interchanges.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::PerformRemainingInterchangesCallback(SSCutSceneManager* manager, CutsceneCallbackElement *element)
{
	MABLOGDEBUG("PerformRemainingInterchangesCallback: %d", element->callback_state);

	SIFGameWorld *game = manager->GetGame();
	RUSubstitutionManager *sub_manager = game->GetSubstitutionManager();

	if (element->callback_state == 0)
	{
		sub_manager->StartAllPendingInterchanges(true);		// Have to do async - otherwise will run out of memory.
		element->callback_state++;
	}
	else
	{
		if (sub_manager->HavePendingInterchangesCompleted())
		{
			game->GetScreenWipeManager()->StartFadeFromBlack(0.3f);

			manager->disable_substitutions = false;				// Re-enable substitution cutscenes

			return true;
		}
	}

	/// Disable rendering till finished!

	manager->DisableRendering(3);

	return false;
}

///-------------------------------------------------------------------------------
/// Callback: Injury cutscene has ended, restart game.
///-------------------------------------------------------------------------------

bool SSCutSceneManager::InjuryEndCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	// Safety, clear all cutscene elements.
	manager->ClearCutSceneElements();

	/// Start a scrum where the injury occurred.

	SIFGameWorld *game = manager->game;
	RUGameState* state = game->GetGameState();

	// GGs JZ on fith tackle scrum should be a handover, 
	// We can't check if 5th tackle in scrum becuase set attacking team resets it,
	// So if it is 5th tackle set the team and count back up to 5th tackle
	if (!state->IsFifthTackle())
	{
		state->SetAttackingTeam(state->GetPlayRestartTeam());
	}
	else
	{
		state->SetAttackingTeam(state->GetPlayRestartTeam());
		for (int i = 0; i < 5; i++)
		{
			state->IncrementTackleCount();
		}
		state->SetHandoverType(EHandoverType::PENALTY);
	}

	game->GetRules()->StartConsequence(RUC_SCRUM);

	manager->game->GetEvents()->injury_cutscene_ended();
	return true;
}


///-------------------------------------------------------------------------------
/// Music callbacks
///-------------------------------------------------------------------------------


bool SSCutSceneManager::PushMusicCallback(SSCutSceneManager*, CutsceneCallbackElement *element)
{
	MabUInt32 event_id = (MabUInt32)element->user_info;
	RUAudio* audio = SIFApplication::GetApplication()->GetRUAudio();
	audio->PushMusic(audio->GetEventName(event_id).c_str());
	return true;
}

bool SSCutSceneManager::PopMusicCallback(SSCutSceneManager*, CutsceneCallbackElement*)
{
	SIFApplication::GetApplication()->GetRUAudio()->PopMusic();
	return true;
}

bool SSCutSceneManager::ClearMusicCallback(SSCutSceneManager* manager, CutsceneCallbackElement *element)
{
	SIFApplication::GetApplication()->GetRUAudio()->ClearMusic();
	return true;
}

bool SSCutSceneManager::SkipCommentaryCallback(SSCutSceneManager*, CutsceneCallbackElement*)
{
#ifndef DISABLE_COMMENTARY
	SIFApplication::GetApplication()->GetCommentarySystem()->Skip();
#endif
	return true;
}


///-------------------------------------------------------------------------------
/// Deferred Player Info Callback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::ShowNextPlayerInfo(SSCutSceneManager* manager, CutsceneCallbackElement* callback_element)
{
	if (!manager->game->GetHUDUpdater())
		return true;

	// cast the callback to our hud info type (it had better be!)
	HUDInfoMessageCallbackElement* hud_info = (HUDInfoMessageCallbackElement*)callback_element;

	// pull the next item out of the queue and pass it to the hud updater to display it
	if (hud_info->player_db_id != 0)
		manager->game->GetHUDUpdater()->SetPlayerInfo(hud_info->player_db_id, hud_info->team, hud_info->additional_info, hud_info->player);
	else
		manager->game->GetHUDUpdater()->SetGenericInfo(SIFGameHelpers::GAConvertMabStringToFString(hud_info->info), hud_info->team, SIFGameHelpers::GAConvertMabStringToFString(hud_info->additional_info));
	return true;
}

///-------------------------------------------------------------------------------
/// Kill all particle systems now!
///-------------------------------------------------------------------------------

bool SSCutSceneManager::KillAllParticlesCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	MABLOGDEBUG("KILL ALL PARTICLES!");
	/*if (manager && manager->GetGame() && manager->GetGame()->GetParticlesManager())//#rc3_legacy_Particle
	{
		manager->GetGame()->GetParticlesManager()->ClearAllParticles();
	}
	*/
	return true;
}

///-------------------------------------------------------------------------------
/// Callback: ProSentOffCallback
///-------------------------------------------------------------------------------

bool SSCutSceneManager::ProSentOffCallback(SSCutSceneManager* manager, CutsceneCallbackElement*)
{
	// For now just start the simulation of the match when we lose our pro.
	//SIFGameHelpers::GAStartSimulateRemainingMatch();
	MABASSERT(manager->pro_player_sent_off_during_interchanges);

	manager->pro_player_sent_off_during_interchanges = false;
	manager->GetGame()->GetEvents()->cutscene_pro_sent_off();

	//dont enable this code. Commented from RC3.
	/*AddCutsceneCallbackElement( SSCutSceneManager::CutsceneStartCallback, CUTSCENE_START_CALLBACK );
	AddCutsceneCallbackElement( SSCutSceneManager::HoldScreenWipeCallback, HOLD_SCREENWIPE_CALLBACK, 100 );

	AddCutsceneAsyncPreloadElement( false );
	AddCutsceneCallbackElement( SSCutSceneManager::StartPostGamePhaseCallback, START_POST_GAME_PHASE_CALLBACK );
	AddScreenWipeElement(SWIPE_CUT);		// Will continue any paused screen wipes.


	//AddCutsceneCallbackElement( SSCutSceneManager::HalfTimeWindowCallback, HALFTIME_WINDOW_CALLBACK );

	AddCutsceneCinematicElement( GAME_PHASE_SIMULATION, DISABLE_SKIP, SWIPE_CUT, HIDE_NON_ACTORS );
	AddCinematicTLE(game->GetStadiumManager()->GetUseSmallPans() ? CSEVENT_HALFTIME_PAN_SMALL : CSEVENT_HALFTIME_PAN, MabMatrix::IDENTITY, SIDE_NONE);*/

	return true;
}

