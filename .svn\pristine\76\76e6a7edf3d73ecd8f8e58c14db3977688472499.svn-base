{"ButtonDefault": [{"name": "UpDefault", "position": 0, "text": [{"name": "private: Text", "textx": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "texty": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "textz": {"points": [{"x": 0.0, "y": 3.5, "g0": 0.0, "g1": 0.0, "pointType": 0}]}}], "x": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "y": {"points": [{"x": 0.0, "y": 2.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "z": null}, {"name": "LeftDefault", "position": 2, "text": [{"name": "private: Text", "textx": {"points": [{"x": 0.0, "y": 3.5, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "texty": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "textz": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}}], "x": {"points": [{"x": 0.0, "y": 2.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "y": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "z": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "position": 3, "text": [{"name": "private: Text", "textx": {"points": [{"x": 0.0, "y": -3.5, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "texty": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "textz": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}}], "x": {"points": [{"x": 0.0, "y": -2.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "y": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "z": null}, {"name": "DownDefault", "position": 1, "text": [{"name": "private: Text", "textx": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "texty": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "textz": {"points": [{"x": 0.0, "y": -3.5, "g0": 0.0, "g1": 0.0, "pointType": 0}]}}], "x": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "y": {"points": [{"x": 0.0, "y": -2.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "z": null}, {"name": "AnalogDefault", "position": 4, "text": [{"name": "private: Text", "textx": {"points": []}, "texty": {"points": []}, "textz": {"points": []}}], "x": {"points": [{"x": 0.0, "y": 1.5, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "y": {"points": []}, "z": null}, {"name": "RuckNort<PERSON>Default", "position": 5, "text": [{"name": "private: Text", "textx": {"points": []}, "texty": {"points": []}, "textz": {"points": [{"x": 0.0, "y": 6.5, "g0": 0.0, "g1": 0.0, "pointType": 0}]}}], "x": {"points": []}, "y": {"points": [{"x": 0.0, "y": 5.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "z": null}, {"name": "RuckSouthDefault", "position": 8, "text": [{"name": "private: Text", "textx": {"points": []}, "texty": {"points": []}, "textz": {"points": [{"x": 0.0, "y": -6.5, "g0": 0.0, "g1": 0.0, "pointType": 0}]}}], "x": {"points": []}, "y": {"points": [{"x": 0.0, "y": -5.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "z": null}, {"name": "RuckEastDefault", "position": 7, "text": [{"name": "private: Text", "textx": {"points": [{"x": 0.0, "y": -5.5, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "texty": {"points": []}, "textz": {"points": []}}], "x": {"points": [{"x": 0.0, "y": -4.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "y": {"points": []}, "z": null}, {"name": "RuckWestDefault", "position": 6, "text": [{"name": "private: Text", "textx": {"points": [{"x": 0.0, "y": 5.5, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "texty": {"points": []}, "textz": {"points": []}}], "x": {"points": [{"x": 0.0, "y": 4.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "y": {"points": []}, "z": null}, {"name": "LineoutSelectFront", "position": 9, "text": [{"name": "private: Text", "textx": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "texty": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "textz": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}}], "x": {"points": []}, "y": {"points": [{"x": 0.0, "y": -2.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "z": null}, {"name": "LineoutSelectMiddle", "position": 10, "text": [{"name": "private: Text", "textx": {"points": []}, "texty": {"points": []}, "textz": {"points": []}}], "x": {"points": []}, "y": {"points": [{"x": 0.0, "y": -2.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "z": null}, {"name": "LineoutSelectBack", "position": 11, "text": [{"name": "private: Text", "textx": {"points": []}, "texty": {"points": []}, "textz": {"points": []}}], "x": {"points": []}, "y": {"points": [{"x": 0.0, "y": -2.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "z": null}], "ContextHelperState": [{"name": "ContextNone", "state": 0, "target": 0, "numTargets": 1, "context2DName": "0", "buttons": [], "text2d": []}, {"name": "ContextTest", "state": 1, "target": 1, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 21, "position": 0, "text": "", "Offset": [{"name": "private: Offset", "platform": 2, "text": [{"name": "private: Text", "textx": {"points": []}, "texty": {"points": []}, "textz": {"points": []}}], "x": {"points": [{"x": 0.0, "y": 1.879617, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "y": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "z": {"points": [{"x": 0.0, "y": 1.212432, "g0": 0.0, "g1": 0.0, "pointType": 0}]}}]}, {"name": "private: <PERSON><PERSON>", "action": 28, "position": 3, "text": "", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 2, "position": 1, "text": "", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 25, "position": 2, "text": "", "Offset": [{"name": "private: Offset", "platform": 0, "text": [{"name": "private: Text", "textx": {"points": []}, "texty": {"points": []}, "textz": {"points": []}}], "x": {"points": [{"x": 0.0, "y": -1.393887, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "y": {"points": [{"x": 0.0, "y": 0.0, "g0": 0.0, "g1": 0.0, "pointType": 0}]}, "z": {"points": [{"x": 0.0, "y": -1.599495, "g0": 0.0, "g1": 0.0, "pointType": 0}]}}]}], "text2d": []}, {"name": "ContextRuckFormed", "state": 2, "target": 2, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 34, "position": 1, "text": "[ID_CH_RUCK_FORMED_ADD]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 35, "position": 3, "text": "[ID_CH_RUCK_FORMED_ADD_AGGRESSIVE]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 99, "position": 0, "text": "[ID_CH_RUCK_FORMED_LEAVE]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 100, "position": 2, "text": "[ID_CH_RUCK_FORMED_CONTEST]", "Offset": []}], "text2d": []}, {"name": "ContextRuckFormedNoContest", "state": 31, "target": 2, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 34, "position": 1, "text": "[ID_CH_RUCK_FORMED_ADD]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 35, "position": 3, "text": "[ID_CH_RUCK_FORMED_ADD_AGGRESSIVE]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 99, "position": 0, "text": "[ID_CH_RUCK_FORMED_LEAVE]", "Offset": []}], "text2d": []}, {"name": "ContextRuckFormedProMode", "state": 23, "target": 2, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "Join <PERSON>", "action": 108, "position": 2, "text": "[ID_CH_RUCK_FORMED_ADD_RUCK_HALF]", "Offset": []}, {"name": "Hard Bind", "action": 35, "position": 3, "text": "[ID_CH_RUCK_FORMED_ADD_AGGRESSIVE]", "Offset": []}, {"name": "Quick Bind", "action": 34, "position": 1, "text": "[ID_CH_RUCK_FORMED_ADD]", "Offset": []}], "text2d": []}, {"name": "ContextRuckFormedProModeRuckHalf", "state": 25, "target": 2, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "Leave RH", "action": 109, "position": 0, "text": "[ID_CH_RUCK_FORMED_LEAVE_RUCK_HALF]", "Offset": []}], "text2d": []}, {"name": "ContextRuckFormedProModeNonRuckHalf", "state": 24, "target": 2, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "Leave Ruck", "action": 99, "position": 0, "text": "[ID_CH_RUCK_FORMED_LEAVE]", "Offset": []}, {"name": "Ruck Contest", "action": 100, "position": 2, "text": "[ID_CH_RUCK_FORMED_CONTEST]", "Offset": []}], "text2d": []}, {"name": "ContextRuckFormedProModeNonRuckHalfNoContest", "state": 32, "target": 2, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "Leave Ruck", "action": 99, "position": 0, "text": "[ID_CH_RUCK_FORMED_LEAVE]", "Offset": []}], "text2d": []}, {"name": "ContextRuckFormedProModeTackled", "state": 33, "target": 0, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "Contest", "action": 100, "position": 0, "text": "[ID_CH_RUCK_FORMED_CONTEST]", "Offset": []}], "text2d": []}, {"name": "ContextRuckBallReleased", "state": 3, "target": 0, "numTargets": 1, "context2DName": "[ID_CH_2D_ATTACK]", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 56, "position": 0, "text": "[ID_CH_RUCK_BALL_RELEASE_BOX]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 115, "position": 12, "text": "[ID_CONTROLS_SET_PLAY]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 66, "position": 13, "text": "[SETPLAY1]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 67, "position": 14, "text": "[SETPLAY2]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 68, "position": 15, "text": "[SETPLAY3]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 69, "position": 16, "text": "[SETPLAY4]", "Offset": []}], "text2d": []}, {"name": "ContextFreeBallStatic", "state": 4, "target": 2, "numTargets": 1, "context2DName": "0", "buttons": [], "text2d": []}, {"name": "ContextFreeBallDynamic", "state": 5, "target": 0, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 32, "position": 3, "text": "[ID_CH_FREE_BALL_DYNAMIC_DIVE]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 33, "position": 0, "text": "[ID_CH_FREE_BALL_DYNAMIC_KICK]", "Offset": []}], "text2d": []}, {"name": "ContextKickRecieve", "state": 7, "target": 2, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 31, "position": 3, "text": "[ID_CH_KICK_RECIEVE_JUMP]", "Offset": []}], "text2d": []}, {"name": "ContextKickRecieveBehind22", "state": 6, "target": 2, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 31, "position": 3, "text": "[ID_CH_KICK_RECIEVE_BEHIND_22_MARK]", "Offset": []}], "text2d": []}, {"name": "ContextDefence", "state": 8, "target": 0, "numTargets": 1, "context2DName": "[ID_CH_2D_DEFENCE]", "buttons": [], "text2d": []}, {"name": "ContextBallHolder", "state": 9, "target": 1, "numTargets": 1, "context2DName": "[ID_CH_2D_ATTACK]", "buttons": [], "text2d": []}, {"name": "ContextTutLHS", "state": 10, "target": 3, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 5, "position": 4, "text": "", "Offset": []}], "text2d": []}, {"name": "ContextLineoutThrow", "state": 11, "target": 1, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 49, "position": 1, "text": "[ID_CH_LINEOUT_THROW]", "Offset": []}], "text2d": []}, {"name": "ContextLineoutMove", "state": 12, "target": 4, "numTargets": 3, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 43, "position": 9, "text": "SelectFront", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 44, "position": 10, "text": "SelectMiddle", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 45, "position": 11, "text": "SelectBack", "Offset": []}], "text2d": []}, {"name": "ContextLineoutTop", "state": 15, "target": 3, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 48, "position": 3, "text": "[ID_CH_LINEOUT_PASS]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 59, "position": 2, "text": "[ID_CH_LINEOUT_MAUL]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 86, "position": 1, "text": "[ID_CH_LINEOUT_JUMP]", "Offset": []}], "text2d": []}, {"name": "TackleContested", "state": 16, "target": 0, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 87, "position": 2, "text": "[ID_CH_MAUL_PUSH]", "Offset": []}], "text2d": []}, {"name": "ContextScrumBallReleased", "state": 14, "target": 0, "numTargets": 1, "context2DName": "[ID_CH_2D_ATTACK]", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 102, "position": 1, "text": "[ID_CH_SCRUM_NUMBER_EIGHT_PICKUP]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 115, "position": 12, "text": "[ID_CONTROLS_SET_PLAY]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 66, "position": 13, "text": "[SETPLAY1]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 67, "position": 14, "text": "[SETPLAY2]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 68, "position": 15, "text": "[SETPLAY3]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 69, "position": 16, "text": "[SETPLAY4]", "Offset": []}], "text2d": []}, {"name": "ContextScrumBallReleasedSevens", "state": 35, "target": 0, "numTargets": 1, "context2DName": "[ID_CH_2D_ATTACK]", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 56, "position": 0, "text": "[ID_CH_RUCK_BALL_RELEASE_BOX]", "Offset": []}], "text2d": []}, {"name": "ContextPlayTheBallStarted", "state": 37, "target": 0, "numTargets": 1, "context2DName": "[ID_CH_2D_ATTACK]", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 115, "position": 12, "text": "[ID_CONTROLS_SET_PLAY]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 66, "position": 13, "text": "[SETPLAY1]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 67, "position": 14, "text": "[SETPLAY2]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 68, "position": 15, "text": "[SETPLAY3]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 69, "position": 16, "text": "[SETPLAY4]", "Offset": []}], "text2d": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "state": 17, "target": 3, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 34, "position": 1, "text": "[ID_CH_RUCK_FORMED_ADD]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 87, "position": 2, "text": "[ID_CH_MAUL_PUSH]", "Offset": []}], "text2d": []}, {"name": "MaulFormedAndUse", "state": 18, "target": 3, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 87, "position": 2, "text": "[ID_CH_MAUL_PUSH]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 60, "position": 1, "text": "[ID_CH_MAUL_OFFLOAD]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 34, "position": 0, "text": "[ID_CH_RUCK_FORMED_ADD]", "Offset": []}], "text2d": []}, {"name": "MaulProFormedInside", "state": 26, "target": 3, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 87, "position": 2, "text": "[ID_CH_MAUL_PUSH]", "Offset": []}], "text2d": []}, {"name": "MaulProFormedOutside", "state": 27, "target": 3, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 34, "position": 1, "text": "[ID_CH_RUCK_FORMED_ADD]", "Offset": []}], "text2d": []}, {"name": "MaulProFormedAndUse", "state": 28, "target": 3, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 87, "position": 2, "text": "[ID_CH_MAUL_PUSH]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 60, "position": 1, "text": "[ID_CH_MAUL_OFFLOAD]", "Offset": []}], "text2d": []}, {"name": "CanForceBall", "state": 19, "target": 1, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 98, "position": 0, "text": "[ID_FORCE_BALL]", "Offset": []}], "text2d": []}, {"name": "ExtraTimeCoinTossHeadsTails", "state": 20, "target": 3, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 69, "position": 3, "text": "[ID_HEADS]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 67, "position": 2, "text": "[ID_TAILS]", "Offset": []}], "text2d": []}, {"name": "ExtraTimeCoinTossKickReceive", "state": 21, "target": 3, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 69, "position": 2, "text": "[ID_KICK]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 67, "position": 3, "text": "[ID_RECEIVE]", "Offset": []}], "text2d": []}, {"name": "ExtraTimeCoinTossNorthSouth", "state": 22, "target": 3, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 69, "position": 2, "text": "[ID_NORTH]", "Offset": []}, {"name": "private: <PERSON><PERSON>", "action": 67, "position": 3, "text": "[ID_SOUTH]", "Offset": []}], "text2d": []}, {"name": "ProWithoutBallAtt", "state": 29, "target": 3, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "CallForKickIntoTouch", "action": 112, "position": 2, "text": "[ID_KICKINTOTOUCH]", "Offset": []}, {"name": "CallForKickDownField", "action": 111, "position": 0, "text": "[ID_KICKDOWNFIELD]", "Offset": []}, {"name": "CallForPass", "action": 110, "position": 3, "text": "[ID_REQUESTPASS]", "Offset": []}], "text2d": []}, {"name": "QuickConversion", "state": 34, "target": 3, "numTargets": 1, "context2DName": "0", "buttons": [{"name": "private: <PERSON><PERSON>", "action": 114, "position": 2, "text": "[ID_QUICK_CONVERSION]", "Offset": []}], "text2d": []}]}