// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIPopulatorTeamSelectList.h"
#include "Rugby/Utility/Helpers/SIFGameHelpers.h"
#include "Rugby/Match/SIFUIConstants.h"
#include "Rugby/RugbyGameInstance.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"

#include "Rugby/Databases/RUGameDatabaseManager.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3Database.h"
#include "Rugby/Match/RugbyUnion/CompetitionMode/RL3Conversion/RL3DBTeam.h"
#include "Rugby/Match/RugbyUnion/RUCustomTeams.h"

#include "WWUIListField.h"
#include "WidgetTree.h"
#include "ScrollBox.h"
#include "Match/RugbyUnion/CompetitionMode/RUCompetitionCustomisationHelper.h"
#include "Match/RugbyUnion/CompetitionMode/RUDBCompetitionTypes.h"
#include "WWUITranslationManager.h"

const char RU_TEAM_SELECT_LIST_POPULATED[] = "team_select_list_populated";
const unsigned short HARDCODED_EXCLUDE_COMP_LIST[] = {
	//DB_COMPID_QUADNATIONS,			// quad nations
	//DB_COMPID_EURONATIONS,			// euro nations
	DB_COMPID_EURO_RUGBY_CLUB,		// euro rugby club championship
};

// Career mode requires lock down on the competition lists in two seperate ways.
// Here we hardcode the competition id's for each.
const unsigned short CAREER_INTERNATIONAL_ONLY_LIST[] = {
	DB_COMPID_WORLDCUP				// rwc
	//DB_COMPID_RL_ELITE,
	//DB_COMPID_RL_FRENCH_1,
	//DB_COMPID_RL_NZ
};

const unsigned short CAREER_CLUB_ONLY_LIST[] = {
	
	DB_COMPID_RL_ARL,
	DB_COMPID_RL_NSW,
	DB_COMPID_RL_QUEENSLAND,
	DB_COMPID_RL_VICTORIAN,
	DB_COMPID_RL_WA,
	DB_COMPID_RL_SA,
	DB_COMPID_RL_NT,
	DB_COMPID_RL_ELITE,
	DB_COMPID_RL_CHALLENGER,
	DB_COMPID_RL_FRENCH_1,
	DB_COMPID_RL_FRENCH_2,
	DB_COMPID_RL_NZ,
	DB_COMPID_RL_NZ_CHALLENGER
};

// This should match up with RUCareerModeManager::R15_CLUB_COMPS_TIER_0
const unsigned short CAREER_PRO_FIFTEENS_CLUB_ONLY_LIST[] = {
	DB_COMPID_RL_NSW,
	DB_COMPID_RL_QUEENSLAND,
	DB_COMPID_RL_VICTORIAN,
	DB_COMPID_RL_WA,
	DB_COMPID_RL_SA,
	DB_COMPID_RL_NT,
	DB_COMPID_RL_CHALLENGER,
	DB_COMPID_RL_FRENCH_2,
	DB_COMPID_RL_NZ_CHALLENGER
};

//===============================================================================
//===============================================================================

UWWUIPopulatorTeamSelectList::UWWUIPopulatorTeamSelectList()
{
	//Populate();
}

// we need a constructor that does nothing so that we can use functions for PopulatorCompTeamList
UWWUIPopulatorTeamSelectList::UWWUIPopulatorTeamSelectList(bool b)
{

}

//===============================================================================
//===============================================================================

void UWWUIPopulatorTeamSelectList::Populate()
{
	if (!SIFApplication::GetApplication())
	{
		return;
	}

	CompetitionList.Empty();

	// We have to create one child for each competition in the database.
	// We get this from the competition manager.
	//RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();

	bool team_link_is_r7 = false;

	if (bTeamLink)
	{
		uint8 team_link_gender = PLAYER_GENDER_FLAG_MALE;

		if (TeamLinkID != DB_INVALID_ID)
		{
			RL3DB_TEAM db_team((unsigned short)TeamLinkID);
			team_link_is_r7 = db_team.GetIsR7Exclusive();
			team_link_gender = db_team.GetGenderPermissionFlags();
		}

		// Nick  WWS 7s to Womens //

		if (team_link_is_r7)
		{
			TeamLimit = TEAM_SELECT_CUSTOM_SEVENS;
		}
		else
		{ 
			TeamLimit = TEAM_SELECT_RC3_NO_SEVENS;
		}

		GenderPermissionFlags = team_link_gender;
	}
	// Nick  WWS 7s to Womens //
	
#ifdef ENABLE_SEVENS_MODE
	bool isSevensGame = CustomGameMode == COMP_CUTSOMISE_SEVENS ? true : SIFGameHelpers::GAGetGameMode() == GAME_MODE_RU13W;

	if (isSevensGame || team_link_is_r7)
	{
		CompetitionList.Empty();

		CompetitionList.Add(DB_COMPID_RL_W_ARL);
		CompetitionList.Add(DB_COMPID_RL_W_ELITE);
		CompetitionList.Add(DB_COMPID_RL_W_WORLDCUP);


		if (TeamLimit == TEAM_SELECT_CUSTOM_SEVENS)
			CompetitionList.Add(RUUI_CUSTOM_TEAMS_SEVENS);
	}
	else
#endif

	{
		if (TeamLimit == CAREER_TEAM_LIMIT_CLUB)
		{
			bool isProFifteensCareer = SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() &&
				SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro() &&
				SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerGameModeR13();

			// When we play fifteens pro career, we want to limit the choices that we have
			if (isProFifteensCareer)
			{
				for (size_t i = 0; i < StaticArraySize(CAREER_PRO_FIFTEENS_CLUB_ONLY_LIST); ++i)
				{
					CompetitionList.Add(CAREER_PRO_FIFTEENS_CLUB_ONLY_LIST[i]);
				}
			}
			else
			{
				// career team select, club only
				for (size_t i = 0; i < StaticArraySize(CAREER_CLUB_ONLY_LIST); ++i)
				{
					CompetitionList.Add(CAREER_CLUB_ONLY_LIST[i]);
				}
			}
		}
		else if (TeamLimit == CAREER_TEAM_LIMIT_INT)
		{
			CompetitionList.Add(RUUI_INTERNATIONAL_TEAMS_LIST_ID);
		}
		else if (TeamLimit == CAREER_TEAM_LIMIT_CUSTOM_DELETE)
		{
			CompetitionList.Add(RUUI_CUSTOM_TEAMS_SEVENS);
			CompetitionList.Add(RUUI_CUSTOM_TEAMS_DELETE_LIST_ID);
		}
		else if (TeamLimit == STANDALONE_PLAYER_SELECT)
		{
			CompetitionList.Add(RUUI_CUSTOM_EDIT_PLAYERS_SEVENS_LIST_ID);
			CompetitionList.Add(RUUI_CUSTOM_EDIT_PLAYERS_LIST_ID);
		}
		else if (TeamLimit == TEAM_SELECT_SEARCH)
		{
			if (!SearchTeamName.IsEmpty())
			{
				CompetitionList.Add(RUUI_CUSTOM_SEARCH_TEAM_LIST_ID);
			}
		}
		else
		{
			if (GenderPermissionFlags == 0 || (PLAYER_GENDER_FLAG_MALE & GenderPermissionFlags) == PLAYER_GENDER_FLAG_MALE)
			{
				CompetitionList.Add(RUUI_INTERNATIONAL_TEAMS_LIST_ID);

				for (size_t i = 0; i < StaticArraySize(CAREER_CLUB_ONLY_LIST); ++i)
				{
					CompetitionList.Add(CAREER_CLUB_ONLY_LIST[i]);
				}

				// We need to check if we should allow custom teams for this populator.
				if (!bNoCustomTeams)
				{
					// Add on the "custom team" hacked list
					//CompetitionList.Add(RUUI_CUSTOM_TEAMS_LIST_ID);
					CompetitionList.Add(RUUI_CUSTOM_EDIT_PLAYERS_LIST_ID);
				}
			}

			if (strstr(TCHAR_TO_ANSI(*TeamLimit), TEAM_SELECT_RC3) || (!bIncludeCustomTeams && !bNoCustomTeams))
			{
				if (TeamLimit != TEAM_SELECT_RC3_NO_SEVENS)
				{
					CompetitionList.Add(DB_COMPID_RL_W_ARL);
					CompetitionList.Add(DB_COMPID_RL_W_ELITE);
					CompetitionList.Add(DB_COMPID_RL_W_WORLDCUP);

					CompetitionList.Add(RUUI_CUSTOM_TEAMS_SEVENS);
				}

				CompetitionList.Add(RUUI_CUSTOM_TEAMS_DELETE_LIST_ID);
			}
			else if (TeamLimit == EDIT_PLAYER_SELECT)
			{
				CompetitionList.Add(RUUI_CUSTOM_EDIT_PLAYERS_SEVENS_LIST_ID);
				CompetitionList.Add(RUUI_CUSTOM_EDIT_PLAYERS_LIST_ID);

				CompetitionList.Add(DB_COMPID_RL_W_ARL);
				CompetitionList.Add(DB_COMPID_RL_W_ELITE);
				CompetitionList.Add(DB_COMPID_RL_W_WORLDCUP);

			}
		}
	}

	PopulateTeamList();
}

//===============================================================================
//===============================================================================

void UWWUIPopulatorTeamSelectList::Populate(UWidget* widget)
{
	if (inPreConstruct)
	{
		preConstructOwner = widget;
	}

	Clear(widget);

	int32 NumItems = dataList.ArrayOption.Num();

	if (!inPreConstruct)
	{
		if (CompetitionList.Num() || bForceRefresh)
		{
			Populate();
			bForceRefresh = false;
		}

		if (CompetitionList.IsValidIndex(CurrentCompetitionIndex))
		{
			NumItems = CompetitionList[CurrentCompetitionIndex].Teams.Num();
		}
	}

	CustomiseSelectTeamDataFileCreationNodeCallback callbackObject(widget, dataList.ArrayOption, CompetitionList, CurrentCompetitionIndex);

	CreateNodesFromTemplate(dataList.TemplateName, NumItems, &callbackObject);

	if (ScreenRef)
	{
#ifdef UI_USING_UMG
		ScreenRef->StoreChildWidgets();
#else
		if (ScreenRef && ScreenRef->GetStateScreen())
		{
			ScreenRef->GetStateScreen()->StoreChildWidgets();
		}
#endif
	}

	if (!inPreConstruct)
	{
		if (CompetitionList.Num())
		{
			UScrollBox* scrollbox = Cast<UScrollBox>(widget);
			PrefixSelectionIcon(scrollbox);
		}
	}
}


//===============================================================================
//===============================================================================

void UWWUIPopulatorTeamSelectList::PopulateTeamList()
{
	for (auto& CurrentCompetition : CompetitionList)
	{
		// We were unable to retrieve the data from the cache,
		// Looks like we'll have to pull it from the database.
		// We need to include the custom teams, so we need to check for that as well.
		TArray<uint32> TeamIDList;
		if (CurrentCompetition.CompetitionDatabaseID == (unsigned short)RUUI_CUSTOM_TEAMS_LIST_ID)
		{
			PopulateCustomTeamsList(TeamIDList, false, false, GenderPermissionFlags);
			// Store off the comp name.
			CurrentCompetition.CompetitionName = "[ID_TEAM_SELECT_CUSTOM]";
		}
		else if (CurrentCompetition.CompetitionDatabaseID == (unsigned short)RUUI_CUSTOM_TEAMS_DELETE_LIST_ID || CurrentCompetition.CompetitionDatabaseID == (unsigned short)RUUI_CUSTOM_EDIT_PLAYERS_LIST_ID
			|| CurrentCompetition.CompetitionDatabaseID == (unsigned short)RUUI_CUSTOM_TEAMS_SEVENS || CurrentCompetition.CompetitionDatabaseID == (unsigned short)RUUI_CUSTOM_EDIT_PLAYERS_SEVENS_LIST_ID)
		{
			bool is_sevens = false;
			CurrentCompetition.CompetitionName = "[ID_TEAM_SELECT_USER_CUSTOM]";

			if (CurrentCompetition.CompetitionDatabaseID == (unsigned short)RUUI_CUSTOM_TEAMS_SEVENS || CurrentCompetition.CompetitionDatabaseID == (unsigned short)RUUI_CUSTOM_EDIT_PLAYERS_SEVENS_LIST_ID)
			{
				CurrentCompetition.CompetitionName = "[ID_TEAM_SELECT_SEVENS_CUSTOM]";
				is_sevens = true;
			}

			PopulateCustomTeamsList(TeamIDList, true, is_sevens, GenderPermissionFlags);
			// Store off the comp name.
			//comp_names[RUUI_CUSTOM_TEAMS_DELETE_LIST_ID] = MabString(0, "[ID_TEAM_SELECT_USER_CUSTOM]");
		}
		else if (CurrentCompetition.CompetitionDatabaseID == (unsigned short)RUUI_INTERNATIONAL_TEAMS_LIST_ID)
		{
			CurrentCompetition.CompetitionName = PopulateInternationalTeamsList(TeamIDList);
		}
		else if (CurrentCompetition.CompetitionDatabaseID == (unsigned short)RUUI_TOUR_TEAMS_LIST_ID)
		{
			CurrentCompetition.CompetitionName = PopulateToursTeamsList(TeamIDList);
		}
		else if (CurrentCompetition.CompetitionDatabaseID == (unsigned short)RUUI_CUSTOM_SEARCH_TEAM_LIST_ID)
		{
			PopulateSearchTeams(TeamIDList);

			CurrentCompetition.CompetitionName = "SEARCH";

			// This is handled elsewhere
			//if (TeamIDList.Num() == 0)
			//{
			//	// SIFUIHelpers::LaunchPopUpByName(SEARCH_EMPTY_POPUP);
			//}
		}
		else
		{
			CurrentCompetition.CompetitionName = PopulateTeamsFromCompId(TeamIDList, CurrentCompetition.CompetitionDatabaseID);
		}

		PopulateTeamsFromDatabase(CurrentCompetition.Teams, TeamIDList, CurrentCompetition.CompetitionDatabaseID);

		// Sort
		CurrentCompetition.Teams.Sort(UWWUIPopulatorTeamSelectList::ByTeamName);
		//std::sort(CurrentCompetition.Teams.begin(), CurrentCompetition.Teams.end(), UWWUIPopulatorTeamSelectList::ByTeamName);

		if (CurrentCompetition.CompetitionDatabaseID == (unsigned short)RUUI_CUSTOM_EDIT_PLAYERS_LIST_ID || CurrentCompetition.CompetitionDatabaseID == (unsigned short)RUUI_CUSTOM_EDIT_PLAYERS_SEVENS_LIST_ID)
		{
			FPopulatedTeam custom_players = FPopulatedTeam("Custom Players", 0);
			CurrentCompetition.Teams.Insert(custom_players, 0);

			if (CurrentCompetition.CompetitionDatabaseID == (unsigned short)RUUI_CUSTOM_EDIT_PLAYERS_LIST_ID)
			{
				CurrentCompetition.CompetitionName = "[ID_TEAM_SELECT_USER_CUSTOM]";
			}
			else
			{
				CurrentCompetition.CompetitionName = "[ID_TEAM_SELECT_SEVENS_CUSTOM]";
			}
		}
	}
}

//===============================================================================
//===============================================================================

FString UWWUIPopulatorTeamSelectList::PopulateTeamsFromCompId(TArray<uint32>& TeamIDs, const unsigned int comp_db_id)
{
	// We need to create enough children for each team in the competition.
// Time to pull out the competition from the database.
	RUDB_COMP_DEF comp;
	RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();
	database_manager->LoadData(comp, comp_db_id);

	// Store off the comp name.
	//comp_names[comp_db_id] = MabString(0, "%s", comp.GetName());

	for (int i = 0; i < comp.GetNumTeams(); ++i)
	{
		unsigned short teamID = comp.GetTeamIdFromIndex(i);

		TeamIDs.Add(teamID);
	}

	return comp.GetName();
}

//===============================================================================
//===============================================================================

void UWWUIPopulatorTeamSelectList::PopulateCustomTeamsList(TArray<uint32>& TeamIDs, bool custom_only, bool is_sevens, uint8 permitted_gender)
{
	if (custom_only)	// user created only
	{
		MabVector<unsigned short> team_id_list;

		// We need to create enough children for each team in the competition.
		// Time to pull out the competition from the database.
		RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();
		MabVector<const char*> columns(SIFHEAP_DYNAMIC);
		MabVector<int> values(SIFHEAP_DYNAMIC);
		columns.push_back("custom");
		values.push_back(1);
		columns.push_back("r7_exclusive");
		values.push_back(is_sevens ? 1 : 0);
		
		if (permitted_gender == PLAYER_GENDER_FLAG_MALE || permitted_gender == PLAYER_GENDER_FLAG_FEMALE)
		{
			columns.push_back("permission_flags_gender");
			values.push_back(permitted_gender);
		}

		if (!database_manager->LoadIdListConditional< RUDB_TEAM_LITE >(columns, values, team_id_list))
		{
			MABBREAKMSG("Conditional LoadIdList failed based on the custom field!");
		}

		for (unsigned int i = 0; i < team_id_list.size(); i++)
		{
			TeamIDs.Add(team_id_list[i]);
		}
	}
	else
	{
		// add the EXTRA_CUSTOM_TEAMS_THAT_ARENT_REALLY_CUSTOM_BUT_NEED_TO_BE_IN_THE_LIST_ANYWAY
		for (int i = 0; i < RUCustomTeams::GetNumCustomTeams(); i++)
		{
			unsigned short team_id = RUCustomTeams::GetCustomTeamIdByIndex(i);

			if (RUCustomTeams::IsCustomTeamAvailable(i))
			{
				if (permitted_gender == PLAYER_GENDER_FLAG_MALE || permitted_gender == PLAYER_GENDER_FLAG_FEMALE)
				{
					RL3DB_TEAM custom_team(team_id);
					uint8 team_gender = custom_team.GetGenderPermissionFlags();
					if ((team_gender & permitted_gender) != team_gender)
					{
						continue;
					}
				}

				TeamIDs.Add(team_id);
			}
		}
	}
}

//===============================================================================
//===============================================================================

FString UWWUIPopulatorTeamSelectList::PopulateInternationalTeamsList(TArray<uint32>& TeamIDs)
{
	RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();
	RL3Database* database = database_manager->GetRL3Database();

	bool isProFifteensCareer = SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() &&
		SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro() &&
		SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerGameModeR13();

	RL3DB_PLAYER player;

	MabVector<unsigned short> team_id_list;

	if (isProFifteensCareer)
	{
		player = RL3DB_PLAYER((unsigned short)SIFApplication::GetApplication()->GetCareerModeManager()->GetProID());
	}

	int num_teams = database->GetTotalNumTeams();

	for (int team_idx = 0; team_idx < num_teams; team_idx++)
	{
		RL3DB_TEAM team = database->GetTeamByIndex(team_idx);

		if (team.IsInternational()) // Nick DB && team.GetDbId() != DB_TEAMID_BARBARIANS && team.GetDbId() != DB_TEAMID_LIONS)
		{
			// Check if our pro player is from this teams' rep area. If not, dont include it in the list.
			unsigned short team_rep_area = team.GetRepAreaId();
			if (isProFifteensCareer && !player.IsFromRepArea(team_rep_area))
			{
				continue;
			}

			TeamIDs.Add(team.GetDbId());
		}
	}

	return "[ID_TEAM_SELECT_INTERNATIONALS]";
}

//===============================================================================
//===============================================================================

FString UWWUIPopulatorTeamSelectList::PopulateToursTeamsList(TArray<uint32>& TeamIDs)
{
	// Nick DB
	//TeamIDs.Add(DB_TEAMID_LIONS);
	//TeamIDs.Add(DB_TEAMID_BARBARIANS);
	return "[ID_TEAM_SELECT_TOURS]";
}

//===============================================================================
//===============================================================================

void UWWUIPopulatorTeamSelectList::PopulateSearchTeams(TArray<uint32>& TeamIDs)
{
	RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();
	RL3Database* database = database_manager->GetRL3Database();

	int num_teams = database->GetTotalNumTeams();
	for (int team_idx = 0; team_idx < num_teams; team_idx++)
	{
		RL3DB_TEAM team = database->GetTeamByIndex(team_idx);
		//MABLOGDEBUG("Team name: %s", team.GetName());

		// ToUpper() needs to be used on FText to be able to convert international characters.
		SearchTeamName = FText::FromString(SearchTeamName).ToUpper().ToString();

		if (team.GetDbId() // Nick DB != DB_TEAMID_BARBARIANS && team.GetDbId() != DB_TEAMID_LIONS
			//&& team.GetDbId() != DB_TEAMID_SIDHE_A && team.GetDbId() != DB_TEAMID_SIDHE_B
			&& team.GetDbId() != DB_TEAMID_NETWORK_FIFTEENS //&& team.GetDbId() != DB_TEAMID_NETWORK_SEVENS
			&& !team.GetIsOfficials()
			&& strstr(MabStringHelper::ToUpper(team.GetName()).c_str(), TCHAR_TO_ANSI(*SearchTeamName)) != NULL)
		{
			//MABLOGDEBUG("Team Matches search name");
#ifndef CHARACTER_CREATOR_BUILD
			if (RUCustomTeams::IsTeamAvailable((unsigned short)team.GetDbId()))
				TeamIDs.Add(team.GetDbId());
#else
			if (team.IsCustom())
				TeamIDs.Add(team.GetDbId());
#endif
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIPopulatorTeamSelectList::PopulateTeamsFromDatabase(TArray<FPopulatedTeam>& OutList, TArray<uint32>& TeamIDs, const unsigned int comp_db_id)
{
	// Now we need to go through each team id, add them to the out_list and the cache.
	RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();
	for (auto& CurrentID : TeamIDs)
	{
		// The team is not in the cache.
		RUDB_TEAM_LITE db_team;
		database_manager->LoadData(db_team, CurrentID);

		FPopulatedTeam team = FPopulatedTeam(UTF8_TO_TCHAR(db_team.GetName()), CurrentID);
		
		if (SIFGameHelpers::GAGetTeamIsCustom(CurrentID) && ((SIFApplication::GetApplication()->IsAnyUserRestricted() && SIFGameHelpers::GAGetTeamDownloadUser(CurrentID) != "") || SIFApplication::GetApplication()->IsNonPrimaryUserRestricted()))
		{
			team.TeamName = UWWUITranslationManager::Translate("[ID_XBOX_CENSORED_NAME]").ToUpper();
		}

		// And finally we'll add it to the out_list - because that's what the user wanted this whole time.
		OutList.Add(team);
	}
}

//===============================================================================
//===============================================================================

FString UWWUIPopulatorTeamSelectList::IncrementFilterCompetition(int32 Direction)
{
	if (CompetitionList.Num() == 0 || bForceRefresh)
	{
		Populate();
	}

	CurrentCompetitionIndex = FMath::ClampWrap<int32>(CurrentCompetitionIndex + Direction, 0, GetCompetitionCount() - 1);

	if (CompetitionIndexIsValid(CurrentCompetitionIndex))
	{
		return CompetitionList[CurrentCompetitionIndex].CompetitionName;
	}

	return "INVALID";
}

//===============================================================================
//===============================================================================

bool UWWUIPopulatorTeamSelectList::CompetitionTeamIndexIsValid(uint32 CompetitionIndex, uint32 TeamIndex)
{
	if (CompetitionIndexIsValid(CompetitionIndex))
	{
		return CompetitionList[CompetitionIndex].Teams.IsValidIndex(TeamIndex);
	}

	return false;
}

//===============================================================================
//===============================================================================

uint32 UWWUIPopulatorTeamSelectList::GetCompetitionTeamCount(uint32 CompetitionIndex)
{
	if (CompetitionIndexIsValid(CompetitionIndex))
	{
		return CompetitionList[CompetitionIndex].Teams.Num();
	}

	return 0;
}

//===============================================================================
//===============================================================================

uint32 UWWUIPopulatorTeamSelectList::GetCompetitionDefaultTeam(uint32 CompetitionIndex)
{
	if (CompetitionIndexIsValid(CompetitionIndex))
	{
		if (CompetitionList[CompetitionIndex].Teams.Num() > 0)
		{
			return CompetitionList[CompetitionIndex].Teams[0].TeamDatabaseID;
		}
	}

	return 0;
}

//===============================================================================
//===============================================================================

uint32 UWWUIPopulatorTeamSelectList::GetCompetitionDatabaseID(uint32 CompetitionIndex)
{
	if (CompetitionIndexIsValid(CompetitionIndex))
	{
		return CompetitionList[CompetitionIndex].CompetitionDatabaseID;
	}

	return 0;
}

//===============================================================================
//===============================================================================

uint32 UWWUIPopulatorTeamSelectList::GetCompetitionTeamDatabaseID(uint32 CompetitionIndex, uint32 TeamIndex)
{
	if (CompetitionIndexIsValid(CompetitionIndex))
	{
		if (CompetitionTeamIndexIsValid(CompetitionIndex, TeamIndex))
		{
			return CompetitionList[CompetitionIndex].Teams[TeamIndex].TeamDatabaseID;
		}
	}

	return 0;
}

//===============================================================================
//===============================================================================

FString UWWUIPopulatorTeamSelectList::GetCompetitionName(uint32 CompetitionIndex)
{
	if (CompetitionIndexIsValid(CompetitionIndex))
	{
		return CompetitionList[CompetitionIndex].CompetitionName;
	}

	return "";
}

//===============================================================================
//===============================================================================

FString UWWUIPopulatorTeamSelectList::GetCompetitionTeamName(uint32 CompetitionIndex, uint32 TeamIndex)
{
	if (CompetitionIndexIsValid(CompetitionIndex))
	{
		if (CompetitionTeamIndexIsValid(CompetitionIndex, TeamIndex))
		{
			return CompetitionList[CompetitionIndex].Teams[TeamIndex].TeamName;
		}
	}

	return "";
}

//===============================================================================
//===============================================================================
bool UWWUIPopulatorTeamSelectList::GetIsCustomTeamList(uint32 CompetitionIndex)
{
	//Either find out if the currently looked at comp is custom, or check if a particular custom comp is
	if (CompetitionIndex == MAX_uint32)
	{
		return CompetitionList[CurrentCompetitionIndex].CompetitionDatabaseID == RUUI_CUSTOM_TEAMS_DELETE_LIST_ID
			|| CompetitionList[CurrentCompetitionIndex].CompetitionDatabaseID == RUUI_CUSTOM_EDIT_PLAYERS_LIST_ID
			|| CompetitionList[CurrentCompetitionIndex].CompetitionDatabaseID == RUUI_CUSTOM_TEAMS_SEVENS
			|| CompetitionList[CurrentCompetitionIndex].CompetitionDatabaseID == RUUI_CUSTOM_EDIT_PLAYERS_SEVENS_LIST_ID;
	}
	else
	{
		return CompetitionList[CompetitionIndex].CompetitionDatabaseID == RUUI_CUSTOM_TEAMS_DELETE_LIST_ID
			|| CompetitionList[CompetitionIndex].CompetitionDatabaseID == RUUI_CUSTOM_EDIT_PLAYERS_LIST_ID
			|| CompetitionList[CompetitionIndex].CompetitionDatabaseID == RUUI_CUSTOM_TEAMS_SEVENS
			|| CompetitionList[CompetitionIndex].CompetitionDatabaseID == RUUI_CUSTOM_EDIT_PLAYERS_SEVENS_LIST_ID;
	}
}

//===============================================================================
//===============================================================================
void UWWUIPopulatorTeamSelectList::SelectDefaultComp(UScrollBox* _Scrollbox, const int comp_def_id)
{
	CurrentCompetitionIndex = 0;

	for (int i = 0; i < CompetitionList.Num(); i++)
	{
		if (CompetitionList[i].CompetitionDatabaseID == comp_def_id)
		{
			CurrentCompetitionIndex = i;
			PopulateAndRefresh(_Scrollbox);
			break;
		}
	}

	PrefixSelectionIcon(_Scrollbox);
}

void UWWUIPopulatorTeamSelectList::PrefixSelectionIcon(UScrollBox* _Scrollbox)
{
	//< We need to compare current teams in the comp to teams in the team select list. >
	/// Get list of teams in the comp.
	const MabVector<RUDB_COMP_DEF_TEAM>* team_selection = NULL;
	if (SIFApplication::GetApplication()->GetCompetitionCustomisationHelper()->GetCurrentCompDefinition())
	{
		team_selection = &SIFApplication::GetApplication()->GetCompetitionCustomisationHelper()->GetCurrentCompDefinition()->teams;
	}
	if (!team_selection || !_Scrollbox) return;


	/// Loop through the teams in the team select list.
	FPopulateCompetition current_comp = CompetitionList[CurrentCompetitionIndex];
	for (int i = 0; i < _Scrollbox->GetChildrenCount() && current_comp.Teams.IsValidIndex(i); i++)
	{
		/// Search the comp for the current team.
		UWWUIListField* current_field	= Cast<UWWUIListField>(_Scrollbox->GetChildAt(i));
		FPopulatedTeam current_team		= current_comp.Teams[i];

		bool found = false;
		for (int j = 0; j < team_selection->size(); j++)
		{
			if ((*team_selection)[j].team_id == current_team.TeamDatabaseID)
			{
				/// Found a match.
				found = true;
				break;
			}
		}

		/// Update field to be enabled/disabled based on it being found.
		current_field->SetIsEnabled(!found);
	}
}

//===============================================================================
//===============================================================================

UWWUIPopulatorTeamSelectList::CustomiseSelectTeamDataFileCreationNodeCallback::CustomiseSelectTeamDataFileCreationNodeCallback(UWidget* containerToPopulate, TArray<FWWUIScreenTemplateDataOption>& inDataOptions, TArray<FPopulateCompetition> InCompetitionList, int32 InCompetitionIndex) :
	container(),
	dataOptions(inDataOptions)
{
	container = Cast<UPanelWidget>(containerToPopulate);

	CurrentIndex = 0;

	if (InCompetitionList.IsValidIndex(InCompetitionIndex))
	{
		CurrentCompetition = InCompetitionList[InCompetitionIndex];
	}

	if (!container)
	{
		FString errorString = containerToPopulate != nullptr ? *containerToPopulate->GetPathName() : FString("NULL");
		//UE_LOG(LogWWUI, Error, TEXT("Cast to scroll box failed while attempting DataFileCreationNodeCallback on node %s"), *errorString);
	}
}

//===============================================================================
//===============================================================================

void UWWUIPopulatorTeamSelectList::CustomiseSelectTeamDataFileCreationNodeCallback::Callback(UUserWidget* widget)
{
	int currentWidget = container->GetChildrenCount();
	UWWUIListField* pListField = Cast<UWWUIListField>(widget);
	if (pListField)
	{
		if (CurrentCompetition.Teams.IsValidIndex(CurrentIndex))
		{
			FPopulatedTeam CurrentTeam = CurrentCompetition.Teams[CurrentIndex];

			// Set up the node properties
			pListField->SetProperty("team_db_id", &CurrentTeam.TeamDatabaseID, PROPERTY_TYPE_INT);
			pListField->SetProperty("comp_db_id", &CurrentCompetition.CompetitionDatabaseID, PROPERTY_TYPE_INT);

			pListField->SetProperty("team_db_name", &CurrentTeam.TeamName, PROPERTY_TYPE_FSTRING);
			pListField->SetProperty("comp_db_name", &CurrentCompetition.CompetitionName, PROPERTY_TYPE_FSTRING);

			FString TeamTitle = CurrentTeam.TeamName;

			UTextBlock* pTitleTextBlock = Cast<UTextBlock>(pListField->WidgetTree->FindWidget(TEXT("Title")));
			if (pTitleTextBlock)
			{
				pTitleTextBlock->SetText(FText::FromString(TeamTitle).ToUpper());
			}
		}
	}
	container->AddChild(widget);
	CurrentIndex++;
}
