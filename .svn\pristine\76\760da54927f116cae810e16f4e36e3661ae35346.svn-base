/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/ContextBucket/RUContextBucketBasicEvents.h"

#include "Match/AI/Actions/RUActionPass.h"
#include "Match/AI/Roles/Competitors/RURoleGetTheBall.h"
#include "Match/AI/Roles/Competitors/RURoleShootForGoal.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleMaulHalfback.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuck.h"
#include "Match/AI/Roles/Competitors/SetPlays/RURoleRuckScrumHalf.h"
#include "Match/Ball/SSBall.h"
#include "Match/Components/RUActionManager.h"
#include "Character/RugbyPlayerController.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseMaul.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseScrum.h"
#include "Match/RugbyUnion/RUEmotionEngineManager.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTackleHelper.h"
#include "Match/RugbyUnion/Statistics/RUStatisticsSystem.h"
#include "Match/RugbyUnion/Statistics/RUStatsConstants.h"
#include "Match/RugbyUnion/Statistics/RUStatsTracker.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSGameTimerHalfObserver.h"
#include "Match/SSPlayerFilter.h"
#include "Match/SSSpatialHelper.h"

#include "RugbyGameInstance.h"
#include "Commentary/RUCommentary.h"

//Glen: TODO: allow these parameters to be tweaked with the debug menu.
static const float LONG_PASS_MIN_DISTANCE = 20.0f;
static const float MEDIUM_PASS_MIN_DISTANCE = 10.0f;


static const float BACK_PASS_THRESHOLD_ANGLE = MabMath::Deg2Rad(25.0f);
static const float BACK_PASS_MIN_DISTANCE = 4.0f;

static const float FLAT_PASS_THRESHOLD_ANGLE = MabMath::Deg2Rad(18.0f);

static const float TIME_EVENT_TICK_PERIOD = 6.0f;

static const float PENALTY_GOAL_ANTICIPATE_DELAY = 0.4f;
static const float CONVERSION_ANTICIPATE_DELAY = 0.4f;

static const float YELLOW_CARD_DELAY_TIME = 3.0f;
static const float RED_CARD_DELAY_TIME = 3.0f;

MABRUNTIMETYPE_IMP1(RUCBBrokenTackleEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBTryEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBTryFollowUpEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBGenericFollowUpEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBRuckEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBRuckResultEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBKickEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBKickResultEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBPassEvent, SSContextBucketEvent);

MABRUNTIMETYPE_IMP1(RUCBLineoutSignalledEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBLineoutDecisionEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBLineoutReadyEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBLineoutThrownEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBLineoutMissEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBLineoutCatchEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBLineoutToMaulEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBLineoutToRunEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBLineoutToPassEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBLineoutSlapDownEvent, SSContextBucketEvent);

MABRUNTIMETYPE_IMP1(RUCBKickAtPostsReadyEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBNewPhaseEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBFistPumpEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBPenaltyEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBPenaltyGoalKickDecidedEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBPenaltyTouchKickDecidedEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBPenaltyTapDecidedEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBBallDeadDecisionEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBTackleEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBMarkEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBCatchEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBCollectedEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBPickedUpEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBFumbleEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBKnockOnEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBTimeKeepingEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBFowardPassEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBBallInTouchEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBOffsideEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBStoppageEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBTouchdownOwnGoal, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBScrumCalledEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBScrumEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBScrumPushingEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBScrumResultEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBLineBreakEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBTerritoryKickEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBCarriedOutOfPlayEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBBallDeadEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBKickOffReady, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBKickOffWhistle, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBSideStepEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBTryAttemptEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBTryLikelyEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBBallHolderRunningEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBCrowdIntensityEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBPlayerWalkOnEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBKickAndChaseEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBRuckPlayerJoinEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBRuckScootEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBRuckSlowEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBAdvantageEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBMaulEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBPreMidPostEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBInjuryEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(SSCBCutsceneEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBInterchangeEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBDistractedEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBTelevisionMatchOfficialEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBBasicPlayerEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBQuickThrowInEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBSetPlayEvent, SSContextBucketEvent);
MABRUNTIMETYPE_IMP1(RUCBHandoverEvent, SSContextBucketEvent);

//----------------------- RUContextBucketBasicEvents ----------------------------------
RUContextBucketBasicEvents::RUContextBucketBasicEvents( SIFGameWorld* game_world, SSContextBucket* context_bucket ): game( game_world )
{
	bucket = context_bucket;
	time_elapsed_since_last_time_tick = 0.0f;
	AttachMonitors();
}

RUContextBucketBasicEvents::~RUContextBucketBasicEvents()
{
	DetachMonitors();
}

void RUContextBucketBasicEvents::Reset()
{
	time_elapsed_since_last_time_tick = 0.0f;
}

void RUContextBucketBasicEvents::AttachMonitors()
{
	RUGameEvents* game_events = game->GetEvents();
	game_events->pass.Add( this, &RUContextBucketBasicEvents::StorePass );
	game_events->commentary_dummy_pass.Add( this, &RUContextBucketBasicEvents::StoreDummyPass );
	game_events->forward_pass.Add(this, &RUContextBucketBasicEvents::StoreForwardPass);
	game_events->kick.Add( this, &RUContextBucketBasicEvents::StoreKick );
	game_events->kick_chase.Add( this, &RUContextBucketBasicEvents::StoreKickAndChase );
//	game_events->predict_restart_out_on_full.Add( this, &RUContextBucketBasicEvents::StoreKickOffOutOnFull );
	game_events->restart_out_on_full.Add( this, &RUContextBucketBasicEvents::StoreKickOffOutOnFull );
	game_events->ball_through_posts_of_wrong_type.Add(this, &RUContextBucketBasicEvents::StoreBallOverPostsWrongType);
	game_events->ruck_formed.Add( this, &RUContextBucketBasicEvents::StoreRuck );
	game_events->ruck_player_joined.Add( this, &RUContextBucketBasicEvents::StoreRuckPlayerJoin );
	game_events->ruck_ball_released.Add( this, &RUContextBucketBasicEvents::StoreRuckResult );
	game_events->ruck_scoot.Add( this, &RUContextBucketBasicEvents::StoreRuckScoot );
	game_events->ruck_slow.Add( this, &RUContextBucketBasicEvents::StoreRuckSlow );
	game_events->crowd_intensity.Add( this, &RUContextBucketBasicEvents::StoreCrowdIntensityEvent );
	game_events->player_walk_on.Add( this, &RUContextBucketBasicEvents::StorePlayerWalkOnEvent );
	game_events->try_result.Add( this, &RUContextBucketBasicEvents::StoreTry );
	game_events->try_attempt.Add( this, &RUContextBucketBasicEvents::StoreTryAttempt );
	game_events->try_likely.Add( this, &RUContextBucketBasicEvents::StoreTryLikely );
	game_events->ball_holder_running_event.Add(this, &RUContextBucketBasicEvents::StoreBallHolderRunningEvent );

	game_events->breakdown_contest.Add(this, &RUContextBucketBasicEvents::StoreBreakdownContest );
	game_events->breakdown_holding_on.Add(this, &RUContextBucketBasicEvents::StoreBreakdownHoldingOn );
	game_events->breakdown_holding_on_illegal.Add(this, &RUContextBucketBasicEvents::StoreBreakdownHoldingOnIllegal );
	game_events->breakdown_should_release.Add(this, &RUContextBucketBasicEvents::StoreBreakdownShouldRelease );
	game_events->breakdown_should_release_illegal.Add(this, &RUContextBucketBasicEvents::StoreBreakdownShouldReleaseIllegal );
	game_events->breakdown_turnover.Add(this, &RUContextBucketBasicEvents::StoreBreakdownTurnover );

	game_events->lineout_signalled.Add( this, &RUContextBucketBasicEvents::StoreLineoutSignalled );
	game_events->lineout_quick_decision.Add( this, &RUContextBucketBasicEvents::StoreLineoutQuickDecision );
	game_events->lineout_short_decision.Add( this, &RUContextBucketBasicEvents::StoreLineoutQuickDecision );
	game_events->lineout_full_decision.Add( this, &RUContextBucketBasicEvents::StoreLineoutFullDecision );
	game_events->lineout_ready.Add( this, &RUContextBucketBasicEvents::StoreLineoutReady );
	game_events->lineout_throw.Add( this, &RUContextBucketBasicEvents::StoreLineoutThrow );
	game_events->lineout_throw_nrc.Add( this, &RUContextBucketBasicEvents::StoreLineoutThrow );
	game_events->lineout_miss.Add( this, &RUContextBucketBasicEvents::StoreLineoutMiss );
	game_events->lineout_catch.Add( this, &RUContextBucketBasicEvents::StoreLineoutCatch );
	game_events->go_to_maul.Add( this, &RUContextBucketBasicEvents::StoreLineoutMaul );
	game_events->lineout_to_run.Add( this, &RUContextBucketBasicEvents::StoreLineoutRun );
	game_events->lineout_to_pass.Add( this, &RUContextBucketBasicEvents::StoreLineoutPass );
	game_events->lineout_slap_down.Add( this, &RUContextBucketBasicEvents::StoreLineoutSlapDown );
	game_events->lineout_faulty_throw_awarded.Add( this, &RUContextBucketBasicEvents::StoreLineoutFault );
	game_events->lineout_quick_throw_denied.Add( this, &RUContextBucketBasicEvents::StoreLineoutQuickThrowDenied );

	game_events->commentary_conversion_transition.Add( this, &RUContextBucketBasicEvents::StoreConversionTransition );
	game_events->kick_at_posts_ready.Add( this, &RUContextBucketBasicEvents::StoreKickAtPostsReady );

	game_events->conversion.Add( this, &RUContextBucketBasicEvents::StoreConversionAnticipation );
	game_events->commentary_conversion_result.Add( this, &RUContextBucketBasicEvents::StoreConversionResult );
	game_events->commentary_drop_goal_result.Add( this, &RUContextBucketBasicEvents::StoreDropGoalResult );
	game_events->penalty_goal_kicked.Add( this, &RUContextBucketBasicEvents::StorePenaltyGoalAnticipate );
	game_events->penalty_goal_finish.Add(this, &RUContextBucketBasicEvents::StorePenaltyGoalResult);


	game_events->broken_tackle.Add( this, &RUContextBucketBasicEvents::StoreBrokenTackle );
	game_events->possession_change.Add( this, &RUContextBucketBasicEvents::StoreNewPhase );
	game_events->fist_pump.Add( this, &RUContextBucketBasicEvents::StoreFistPump );

	game_events->penalty_detected.Add( this, &RUContextBucketBasicEvents::StorePenalty );
	game_events->penalty_touch_decision.Add( this, &RUContextBucketBasicEvents::StorePenaltyDecision<RUCBPenaltyTouchKickDecidedEvent> );
	game_events->penalty_tap_decision.Add( this, &RUContextBucketBasicEvents::StorePenaltyDecision<RUCBPenaltyTapDecidedEvent> );
	game_events->penalty_goal_decision.Add( this, &RUContextBucketBasicEvents::StorePenaltyDecision<RUCBPenaltyGoalKickDecidedEvent> );

	game_events->ball_dead_dropout_decision.Add( this, &RUContextBucketBasicEvents::StoreBallDeadDecision< SSCB_EVENT_BALL_DEAD_DROP_OUT>);
	game_events->ball_dead_scrum_decision.Add( this, &RUContextBucketBasicEvents::StoreBallDeadDecision< SSCB_EVENT_BALL_DEAD_SCRUM>);
	game_events->restart_out_full_scrum_decision.Add( this, &RUContextBucketBasicEvents::StoreBallDeadDecision< SSCB_EVENT_ON_FULL_DECIDE_SCRUM>);
	game_events->restart_out_full_rekick_decision.Add( this, &RUContextBucketBasicEvents::StoreBallDeadDecision< SSCB_EVENT_ON_FULL_DECIDE_REKICK>);
	game_events->restart_out_full_lineout_decision.Add( this, &RUContextBucketBasicEvents::StoreBallDeadDecision< SSCB_EVENT_ON_FULL_DECIDE_LINEOUT>);

	game_events->tap_restart.Add(this,& RUContextBucketBasicEvents::StoreTapRestart);

	game_events->tackle.Add( this, &RUContextBucketBasicEvents::StoreTackle );
	game_events->tacklee_on_ground.Add( this, &RUContextBucketBasicEvents::StoreTackleeOnGround );
	game_events->injury.Add(this, &RUContextBucketBasicEvents::StoreInjury);
	game_events->mark_catch.Add(this, &RUContextBucketBasicEvents::StoreMark );
	game_events->caught.Add( this, &RUContextBucketBasicEvents::StoreCatch );
	game_events->collected.Add( this, &RUContextBucketBasicEvents::StoreCollected );
	game_events->ruck_scrum_ball_picked_up.Add( this, &RUContextBucketBasicEvents::StoreRuckScrumBallPickedUp );
	game_events->fumble.Add( this, &RUContextBucketBasicEvents::StoreFumble );
	game_events->knock_on.Add( this, &RUContextBucketBasicEvents::StoreKnockOn );
	game_events->offside.Add( this, &RUContextBucketBasicEvents::StoreOffside );
	game_events->offside_from_kick.Add( this, &RUContextBucketBasicEvents::StoreKickOffside );
	game_events->scrum.Add(this, &RUContextBucketBasicEvents::StoreScrumCalled);
	game_events->scrum_start.Add( this, &RUContextBucketBasicEvents::StoreScrumStart );
	game_events->scrum_ball_in.Add( this, &RUContextBucketBasicEvents::StoreScrumBallIn );
	game_events->scrum_pushing.Add( this, &RUContextBucketBasicEvents::StoreScrumPushing );
	game_events->scrum_ball_out.Add( this, &RUContextBucketBasicEvents::StoreScrumBallOut );
	game_events->scrum_reset.Add( this, &RUContextBucketBasicEvents::StoreScrumReset );
	game_events->rule_trigger_ball_out.Add( this, &RUContextBucketBasicEvents::HandleBallOutRuleTrigger );
	game_events->ball_dead_detected.Add( this, &RUContextBucketBasicEvents::HandleBallDeadDetected );
	game_events->line_break.Add(this, &RUContextBucketBasicEvents::StoreLineBreak );
	game_events->number_eight_pickup.Add(this, &RUContextBucketBasicEvents::StoreNumberEightPickup );
	game_events->commentary_kick_off_wait_for_ref.Add( this, &RUContextBucketBasicEvents::StoreKickOffReady );
	game_events->commentary_kick_off_ref_whistle.Add( this, &RUContextBucketBasicEvents::StoreKickOffWhistle );
	game_events->commentary_kick_slow.Add(this, &RUContextBucketBasicEvents::StoreTimeWasting<SSCB_EVENT_KICK_SLOW>);
	game_events->commentary_kick_timed_out.Add(this, &RUContextBucketBasicEvents::StoreTimeWasting<SSCB_EVENT_KICK_TIMED_OUT>);

	game_events->commentary_side_step.Add(this, &RUContextBucketBasicEvents::StoreSideStep);

	game_events->advantage_started.Add( this, &RUContextBucketBasicEvents::StoreAdvantageStarted );
	game_events->advantage_over.Add( this, &RUContextBucketBasicEvents::StoreAdvantageEnded );
	game_events->knock_on_called.Add( this, &RUContextBucketBasicEvents::StoreAdvantageScrumAwarded );
	game_events->cutscene_forward_pass.Add( this, &RUContextBucketBasicEvents::StoreAdvantageScrumAwarded );
	game_events->penalty_from_advantage.Add( this, &RUContextBucketBasicEvents::StoreAdvantagePenaltyAwarded );
	game_events->commentary_ball_dead.Add(this, &RUContextBucketBasicEvents::StoreDeadBall);
	game_events->commentary_ball_forced.Add(this, &RUContextBucketBasicEvents::StoreBallForced);

	game_events->maul_formed.Add(this, &RUContextBucketBasicEvents::StoreMaulFormedEvent);
	game_events->maul_ball_released.Add(this, &RUContextBucketBasicEvents::StoreMaulBallReleased);
	game_events->maul_to_ground.Add(this, &RUContextBucketBasicEvents::StoreMaulCollapsed);
	game_events->maul_heldup.Add(this, &RUContextBucketBasicEvents::StoreMaulHeldUp);
	game_events->commentary_maul_periodic_progress.Add(this, &RUContextBucketBasicEvents::StoreMaulProgress);
	game_events->commentary_maul_looks_halted.Add(this, &RUContextBucketBasicEvents::StoreMaulLooksHalted);


	// timing handlers
	game_events->first_half_start.Add( this, &RUContextBucketBasicEvents::StoreTimeKeepingEvent<TKC_FIRST_HALF_START> );
	game_events->pre_half_time.Add( this, &RUContextBucketBasicEvents::StoreTimeKeepingEvent<TKC_FIRST_HALF_SIREN> );
	game_events->half_time.Add( this, &RUContextBucketBasicEvents::StoreTimeKeepingEvent<TKC_FIRST_HALF_END> );
	game_events->second_half_start.Add( this, &RUContextBucketBasicEvents::StoreTimeKeepingEvent<TKC_SECOND_HALF_START> );
	game_events->pre_full_time.Add( this, &RUContextBucketBasicEvents::StoreTimeKeepingEvent<TKC_SECOND_HALF_SIREN> );
	game_events->full_time.Add( this, &RUContextBucketBasicEvents::StoreTimeKeepingEvent<TKC_SECOND_HALF_END> );

	game_events->yellow_card.Add(this, &RUContextBucketBasicEvents::StoreYellowCard);
	game_events->red_card.Add(this, &RUContextBucketBasicEvents::StoreRedCard);

	game_events->commentary_interchange_started.Add(this, &RUContextBucketBasicEvents::StoreInterchangeStart);
	game_events->commentary_interchange_made.Add(this, &RUContextBucketBasicEvents::StoreInterchangeMade);

	game_events->commentary_distraction_effect.Add(this, &RUContextBucketBasicEvents::StoreDistraction);

	game_events->video_referee_deliberation.Add(this, &RUContextBucketBasicEvents::StoreTMODeliberation);
	game_events->video_referee_decision.Add(this, &RUContextBucketBasicEvents::StoreTMODecision);

	//cutscene handlers
	game_events->commentary_replay_started.Add( this, &RUContextBucketBasicEvents::StoreReplayStarted);
	game_events->cutscene_trophy_ceremony.Add( this, &RUContextBucketBasicEvents::StoreTrophyCeremony);

	//setplays
	game_events->set_play_started.Add(this, &RUContextBucketBasicEvents::StoreSetplayStart);

	game_events->handover.Add(this, &RUContextBucketBasicEvents::StoreHandover);
}

void RUContextBucketBasicEvents::DetachMonitors()
{
	RUGameEvents* game_events = game->GetEvents();
	game_events->pass.Remove( this, &RUContextBucketBasicEvents::StorePass );
	game_events->commentary_dummy_pass.Remove( this, &RUContextBucketBasicEvents::StoreDummyPass );
	game_events->forward_pass.Remove(this, &RUContextBucketBasicEvents::StoreForwardPass);
	game_events->kick.Remove( this, &RUContextBucketBasicEvents::StoreKick );
	game_events->kick_chase.Remove( this, &RUContextBucketBasicEvents::StoreKickAndChase );
//	game_events->predict_restart_out_on_full.Remove( this, &RUContextBucketBasicEvents::StoreKickOffOutOnFull );
	game_events->restart_out_on_full.Remove( this, &RUContextBucketBasicEvents::StoreKickOffOutOnFull );
	game_events->ball_through_posts_of_wrong_type.Remove(this, &RUContextBucketBasicEvents::StoreBallOverPostsWrongType);
	game_events->ruck_formed.Remove( this, &RUContextBucketBasicEvents::StoreRuck );
	game_events->ruck_player_joined.Remove( this, &RUContextBucketBasicEvents::StoreRuckPlayerJoin );
	game_events->ruck_ball_released.Remove( this, &RUContextBucketBasicEvents::StoreRuckResult );
	game_events->ruck_scoot.Remove( this, &RUContextBucketBasicEvents::StoreRuckScoot );
	game_events->ruck_slow.Remove( this, &RUContextBucketBasicEvents::StoreRuckSlow );
	game_events->crowd_intensity.Remove( this, &RUContextBucketBasicEvents::StoreCrowdIntensityEvent );
	game_events->player_walk_on.Remove( this, &RUContextBucketBasicEvents::StorePlayerWalkOnEvent );
	game_events->try_result.Remove( this, &RUContextBucketBasicEvents::StoreTry );
	game_events->try_attempt.Remove( this, &RUContextBucketBasicEvents::StoreTryAttempt );
	game_events->try_likely.Remove( this, &RUContextBucketBasicEvents::StoreTryLikely );
	game_events->ball_holder_running_event.Remove(this, &RUContextBucketBasicEvents::StoreBallHolderRunningEvent );

	game_events->breakdown_contest.Remove(this, &RUContextBucketBasicEvents::StoreBreakdownContest );
	game_events->breakdown_holding_on.Remove(this, &RUContextBucketBasicEvents::StoreBreakdownHoldingOn );
	game_events->breakdown_holding_on_illegal.Remove(this, &RUContextBucketBasicEvents::StoreBreakdownHoldingOnIllegal );
	game_events->breakdown_should_release.Remove(this, &RUContextBucketBasicEvents::StoreBreakdownShouldRelease );
	game_events->breakdown_should_release_illegal.Remove(this, &RUContextBucketBasicEvents::StoreBreakdownShouldReleaseIllegal );
	game_events->breakdown_turnover.Remove(this, &RUContextBucketBasicEvents::StoreBreakdownTurnover );

	game_events->lineout_signalled.Remove( this, &RUContextBucketBasicEvents::StoreLineoutSignalled );
	game_events->lineout_quick_decision.Remove( this, &RUContextBucketBasicEvents::StoreLineoutQuickDecision );
	game_events->lineout_short_decision.Remove( this, &RUContextBucketBasicEvents::StoreLineoutQuickDecision );
	game_events->lineout_full_decision.Remove( this, &RUContextBucketBasicEvents::StoreLineoutFullDecision );
	game_events->lineout_ready.Remove( this, &RUContextBucketBasicEvents::StoreLineoutReady );
	game_events->lineout_throw.Remove( this, &RUContextBucketBasicEvents::StoreLineoutThrow );
	game_events->lineout_throw_nrc.Remove( this, &RUContextBucketBasicEvents::StoreLineoutThrow );
	game_events->lineout_miss.Remove( this, &RUContextBucketBasicEvents::StoreLineoutMiss );
	game_events->lineout_catch.Remove( this, &RUContextBucketBasicEvents::StoreLineoutCatch );
	game_events->go_to_maul.Remove( this, &RUContextBucketBasicEvents::StoreLineoutMaul );
	game_events->lineout_to_run.Remove( this, &RUContextBucketBasicEvents::StoreLineoutRun );
	game_events->lineout_to_pass.Remove( this, &RUContextBucketBasicEvents::StoreLineoutPass );
	game_events->lineout_slap_down.Remove( this, &RUContextBucketBasicEvents::StoreLineoutSlapDown );
	game_events->lineout_faulty_throw_awarded.Remove( this, &RUContextBucketBasicEvents::StoreLineoutFault );
	game_events->lineout_quick_throw_denied.Remove( this, &RUContextBucketBasicEvents::StoreLineoutQuickThrowDenied );

	game_events->commentary_conversion_transition.Remove( this, &RUContextBucketBasicEvents::StoreConversionTransition );
	game_events->kick_at_posts_ready.Remove( this, &RUContextBucketBasicEvents::StoreKickAtPostsReady );
	game_events->conversion.Remove( this, &RUContextBucketBasicEvents::StoreConversionAnticipation );
	game_events->commentary_conversion_result.Remove( this, &RUContextBucketBasicEvents::StoreConversionResult );
	game_events->commentary_drop_goal_result.Remove( this, &RUContextBucketBasicEvents::StoreDropGoalResult );
	game_events->penalty_goal_kicked.Remove( this, &RUContextBucketBasicEvents::StorePenaltyGoalAnticipate );
	game_events->penalty_goal_finish.Remove(this, &RUContextBucketBasicEvents::StorePenaltyGoalResult);

	game_events->broken_tackle.Remove( this, &RUContextBucketBasicEvents::StoreBrokenTackle );
	game_events->possession_change.Remove( this, &RUContextBucketBasicEvents::StoreNewPhase );
	game_events->fist_pump.Remove( this, &RUContextBucketBasicEvents::StoreFistPump );
	game_events->penalty_detected.Remove( this, &RUContextBucketBasicEvents::StorePenalty );

	game_events->penalty_touch_decision.Remove( this, &RUContextBucketBasicEvents::StorePenaltyDecision<RUCBPenaltyTouchKickDecidedEvent> );
	game_events->penalty_tap_decision.Remove( this, &RUContextBucketBasicEvents::StorePenaltyDecision<RUCBPenaltyTapDecidedEvent> );
	game_events->penalty_goal_decision.Remove( this, &RUContextBucketBasicEvents::StorePenaltyDecision<RUCBPenaltyGoalKickDecidedEvent> );

	game_events->ball_dead_dropout_decision.Remove( this, &RUContextBucketBasicEvents::StoreBallDeadDecision< SSCB_EVENT_BALL_DEAD_DROP_OUT>);
	game_events->ball_dead_scrum_decision.Remove( this, &RUContextBucketBasicEvents::StoreBallDeadDecision< SSCB_EVENT_BALL_DEAD_SCRUM>);

	game_events->restart_out_full_scrum_decision.Remove( this, &RUContextBucketBasicEvents::StoreBallDeadDecision< SSCB_EVENT_ON_FULL_DECIDE_SCRUM>);
	game_events->restart_out_full_rekick_decision.Remove( this, &RUContextBucketBasicEvents::StoreBallDeadDecision< SSCB_EVENT_ON_FULL_DECIDE_REKICK>);
	game_events->restart_out_full_lineout_decision.Remove( this, &RUContextBucketBasicEvents::StoreBallDeadDecision< SSCB_EVENT_ON_FULL_DECIDE_LINEOUT>);

	game_events->tap_restart.Remove(this,& RUContextBucketBasicEvents::StoreTapRestart);

	game_events->tackle.Remove ( this, &RUContextBucketBasicEvents::StoreTackle );
	game_events->tacklee_on_ground.Remove( this, &RUContextBucketBasicEvents::StoreTackleeOnGround );
	game_events->injury.Remove(this, &RUContextBucketBasicEvents::StoreInjury);
	game_events->mark_catch.Remove(this, &RUContextBucketBasicEvents::StoreMark );
	game_events->caught.Remove( this, &RUContextBucketBasicEvents::StoreCatch );
	game_events->collected.Remove( this, &RUContextBucketBasicEvents::StoreCollected );
	game_events->ruck_scrum_ball_picked_up.Remove( this, &RUContextBucketBasicEvents::StoreRuckScrumBallPickedUp );
	game_events->fumble.Remove( this, &RUContextBucketBasicEvents::StoreFumble );
	game_events->knock_on.Remove( this, &RUContextBucketBasicEvents::StoreKnockOn );
	game_events->offside.Remove( this, &RUContextBucketBasicEvents::StoreOffside );
	game_events->offside_from_kick.Remove( this, &RUContextBucketBasicEvents::StoreKickOffside );
	game_events->scrum.Remove(this, &RUContextBucketBasicEvents::StoreScrumCalled);
	game_events->scrum_start.Remove( this, &RUContextBucketBasicEvents::StoreScrumStart );
	game_events->scrum_ball_in.Remove( this, &RUContextBucketBasicEvents::StoreScrumBallIn );
	game_events->scrum_pushing.Remove( this, &RUContextBucketBasicEvents::StoreScrumPushing );
	game_events->scrum_ball_out.Remove( this, &RUContextBucketBasicEvents::StoreScrumBallOut );
	game_events->scrum_reset.Remove( this, &RUContextBucketBasicEvents::StoreScrumReset );
	game_events->line_break.Remove(this, &RUContextBucketBasicEvents::StoreLineBreak );
	game_events->number_eight_pickup.Add(this, &RUContextBucketBasicEvents::StoreNumberEightPickup );
	game_events->ball_dead_detected.Remove( this, &RUContextBucketBasicEvents::HandleBallDeadDetected );
	game_events->rule_trigger_ball_out.Remove( this, &RUContextBucketBasicEvents::HandleBallOutRuleTrigger );
	game_events->commentary_kick_off_wait_for_ref.Remove( this, &RUContextBucketBasicEvents::StoreKickOffReady );
	game_events->commentary_kick_off_ref_whistle.Remove( this, &RUContextBucketBasicEvents::StoreKickOffWhistle );
	game_events->commentary_kick_slow.Remove(this, &RUContextBucketBasicEvents::StoreTimeWasting<SSCB_EVENT_KICK_SLOW>);
	game_events->commentary_kick_timed_out.Remove(this, &RUContextBucketBasicEvents::StoreTimeWasting<SSCB_EVENT_KICK_TIMED_OUT>);

	game_events->commentary_side_step.Remove(this, &RUContextBucketBasicEvents::StoreSideStep);

	game_events->advantage_started.Remove( this, &RUContextBucketBasicEvents::StoreAdvantageStarted );
	game_events->advantage_over.Remove( this, &RUContextBucketBasicEvents::StoreAdvantageEnded );
	game_events->knock_on_called.Remove( this, &RUContextBucketBasicEvents::StoreAdvantageScrumAwarded );
	game_events->cutscene_forward_pass.Remove( this, &RUContextBucketBasicEvents::StoreAdvantageScrumAwarded );
	game_events->penalty_from_advantage.Remove( this, &RUContextBucketBasicEvents::StoreAdvantagePenaltyAwarded );
	game_events->commentary_ball_dead.Remove(this, &RUContextBucketBasicEvents::StoreDeadBall);
	game_events->commentary_ball_forced.Remove(this, &RUContextBucketBasicEvents::StoreBallForced);

	game_events->maul_formed.Remove(this, &RUContextBucketBasicEvents::StoreMaulFormedEvent);
	game_events->maul_ball_released.Remove(this, &RUContextBucketBasicEvents::StoreMaulBallReleased);
	game_events->maul_to_ground.Remove(this, &RUContextBucketBasicEvents::StoreMaulCollapsed);
	game_events->maul_heldup.Remove(this, &RUContextBucketBasicEvents::StoreMaulHeldUp);
	game_events->commentary_maul_periodic_progress.Remove(this, &RUContextBucketBasicEvents::StoreMaulProgress);
	game_events->commentary_maul_looks_halted.Remove(this, &RUContextBucketBasicEvents::StoreMaulLooksHalted);

	// timing events
	game_events->first_half_start.Remove( this, &RUContextBucketBasicEvents::StoreTimeKeepingEvent<TKC_FIRST_HALF_START> );
	game_events->pre_half_time.Remove( this, &RUContextBucketBasicEvents::StoreTimeKeepingEvent<TKC_FIRST_HALF_SIREN> );
	game_events->half_time.Remove( this, &RUContextBucketBasicEvents::StoreTimeKeepingEvent<TKC_FIRST_HALF_END> );
	game_events->second_half_start.Remove( this, &RUContextBucketBasicEvents::StoreTimeKeepingEvent<TKC_SECOND_HALF_START> );
	game_events->pre_full_time.Remove( this, &RUContextBucketBasicEvents::StoreTimeKeepingEvent<TKC_SECOND_HALF_SIREN> );
	game_events->full_time.Remove( this, &RUContextBucketBasicEvents::StoreTimeKeepingEvent<TKC_SECOND_HALF_END> );

	game_events->yellow_card.Remove(this, &RUContextBucketBasicEvents::StoreYellowCard);
	game_events->red_card.Remove(this, &RUContextBucketBasicEvents::StoreRedCard);

	game_events->commentary_interchange_started.Remove(this, &RUContextBucketBasicEvents::StoreInterchangeStart);
	game_events->commentary_interchange_made.Remove(this, &RUContextBucketBasicEvents::StoreInterchangeMade);
	game_events->commentary_distraction_effect.Remove(this, &RUContextBucketBasicEvents::StoreDistraction);

	game_events->video_referee_deliberation.Remove(this, &RUContextBucketBasicEvents::StoreTMODeliberation);
	game_events->video_referee_decision.Remove(this, &RUContextBucketBasicEvents::StoreTMODecision);

	//cutscene handlers
	game_events->commentary_replay_started.Remove( this, &RUContextBucketBasicEvents::StoreReplayStarted);
	game_events->cutscene_trophy_ceremony.Remove( this, &RUContextBucketBasicEvents::StoreTrophyCeremony);

	//setplays
	game_events->set_play_started.Remove(this, &RUContextBucketBasicEvents::StoreSetplayStart);

	game_events->handover.Remove(this, &RUContextBucketBasicEvents::StoreHandover);

}

void RUContextBucketBasicEvents::Update(const MabTimeStep& time_step)
{
	time_elapsed_since_last_time_tick += time_step.delta_time.ToSeconds();
	if(time_elapsed_since_last_time_tick >= TIME_EVENT_TICK_PERIOD)
	{
		time_elapsed_since_last_time_tick = 0.0f;
		StoreTimeTickEvent();
	}
}

template<TimeKeepingContext T>
void RUContextBucketBasicEvents::StoreTimeKeepingEvent()
{
	RUCBTimeKeepingEvent time_ev( game->GetSimTime()->GetAbsoluteStepCount(), T );
	bucket->AddEvent( time_ev );
}

template<class T>
void RUContextBucketBasicEvents::StorePenaltyDecision( ARugbyCharacter* player, const FVector& restart_position )
{
	MABASSERT( player );
	if( !player )
		return;

	RUPlayerAttributes* attrs = player->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	T ev( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, restart_position );
	bucket->AddEvent( ev );
}


template<SSCB_TYPE event_type>
void RUContextBucketBasicEvents::StoreBallDeadDecision( ARugbyCharacter* player, const FVector& /*restart_position*/ )
{
	int player_idx = player ? player->GetAttributes()->GetDBPlayer()->GetDbId() : -1;

	BallFreeInfo bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	FVector last_play_position = bfi.pos;
	RUCBBallDeadDecisionEvent ev( game->GetSimTime()->GetAbsoluteStepCount(), event_type, player_idx, last_play_position );
	bucket->AddEvent( ev );
}


void RUContextBucketBasicEvents::StoreTapRestart( ARugbyCharacter* player, const FVector& /*location*/)
{
	SSCB_TYPE event_type = game->GetGameState()->GetPhase() == RUGamePhase::QUICK_TAP_PENALTY ? SSCB_EVENT_QUICK_TAP : SSCB_EVENT_TAP_RESTART;
	RUCBBasicPlayerEvent event_record(game->GetSimTime()->GetAbsoluteStepCount(), event_type, player->GetAttributes()->GetDbId());
	bucket->AddEvent( event_record );
}

void RUContextBucketBasicEvents::StoreLineoutQuickDecision( ARugbyCharacter* ball_thrower )
{
	MABASSERT(ball_thrower);
	if(!ball_thrower)
		return;

	RUPlayerAttributes* attrs = ball_thrower->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	RUCBQuickThrowInEvent ev2( game->GetSimTime()->GetAbsoluteStepCount(), SSCB_EVENT_QUICK_THROW_IN, player_idx, team_idx );
	bucket->AddEvent( ev2 );

	RUCBLineoutDecisionEvent ev( game->GetSimTime()->GetAbsoluteStepCount(), LDC_QUICK_THROW_IN, player_idx, team_idx );
	bucket->AddEvent( ev );
}

void RUContextBucketBasicEvents::StoreLineoutQuickThrowDenied( ARugbyCharacter* ball_thrower )
{
	MABASSERT(ball_thrower);
	if(!ball_thrower)
		return;

	RUPlayerAttributes* attrs = ball_thrower->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	RUCBQuickThrowInEvent ev( game->GetSimTime()->GetAbsoluteStepCount(), SSCB_EVENT_QUICK_THROW_IN_DENIED, player_idx, team_idx );
	bucket->AddEvent( ev );
}

void RUContextBucketBasicEvents::StoreLineoutShortDecision( ARugbyCharacter* ball_thrower )
{
	MABASSERT(ball_thrower);
	if(!ball_thrower)
		return;

	RUPlayerAttributes* attrs = ball_thrower->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	RUCBLineoutDecisionEvent ev( game->GetSimTime()->GetAbsoluteStepCount(), LDC_SHORT_LINEOUT, player_idx, team_idx );
	bucket->AddEvent( ev );
}

void RUContextBucketBasicEvents::StoreLineoutFullDecision( ARugbyCharacter* ball_thrower )
{
	MABASSERT(ball_thrower);
	if(!ball_thrower)
		return;

	RUPlayerAttributes* attrs = ball_thrower->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	RUCBLineoutDecisionEvent ev( game->GetSimTime()->GetAbsoluteStepCount(), LDC_FULL_LINEOUT, player_idx, team_idx );
	bucket->AddEvent( ev );
}

void RUContextBucketBasicEvents::StoreLineoutThrow( bool illegal_throw, const FVector& target_position, ARugbyCharacter* ball_thrower, THROW_TYPE throw_type, ARugbyCharacter* quick_throw_in_reciever )
{
	MABASSERT(ball_thrower);

	if(!ball_thrower)
		return;

	float field_width = game->GetSpatialHelper()->GetFieldExtents().x * 0.5f;
	float x_travel_distance = field_width - MabMath::Abs( target_position.x );
	MABASSERT( x_travel_distance > 0 );

	RUPlayerAttributes* attrs = ball_thrower->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	MABASSERT( (throw_type == THROW_QUICK && quick_throw_in_reciever) || (throw_type != THROW_QUICK && !quick_throw_in_reciever) );
	int player2_idx = -1;

	if ( throw_type == THROW_QUICK && quick_throw_in_reciever )
	{
		player2_idx = quick_throw_in_reciever->GetAttributes()->GetDBPlayer()->GetDbId();
	}

	RUCBLineoutThrownEvent ev( game->GetSimTime()->GetAbsoluteStepCount(), throw_type, !illegal_throw, x_travel_distance, player_idx, team_idx, player2_idx );
	bucket->AddEvent( ev );
}

void RUContextBucketBasicEvents::StoreLineoutReady( ARugbyCharacter* ball_thrower )
{
	MABASSERT(ball_thrower);
	if(!ball_thrower)
		return;

	RUPlayerAttributes* attrs = ball_thrower->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	RUCBLineoutReadyEvent ev( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx );
	bucket->AddEvent( ev );
}

void RUContextBucketBasicEvents::StoreLineoutCatch( ARugbyCharacter* catcher, bool )
{
	MABASSERT(catcher);

	if(!catcher)
		return;

	RUPlayerAttributes* attrs = catcher->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	RUCBLineoutThrownEvent* lineout_thrown_event = MabCast< RUCBLineoutThrownEvent >( bucket->GetLastEvent(SSCB_EVENT_LINEOUT_THROW) );
	MABASSERT( lineout_thrown_event );
	if( !lineout_thrown_event )
		return;
	bool turnover = lineout_thrown_event->team_index != team_idx;

	RUCBLineoutCatchEvent ev( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, turnover );
	bucket->AddEvent( ev );
}

void RUContextBucketBasicEvents::StoreLineoutMiss()
{
	SSContextBucketEvent* start_event = bucket->GetEvent(0);
	MABASSERT(start_event);
	if(!start_event)
		return;

	RUCBLineoutThrownEvent* lineout_thrown_event = MabCast< RUCBLineoutThrownEvent >( bucket->GetLastEvent(SSCB_EVENT_LINEOUT_THROW) );
	MABASSERT( lineout_thrown_event );
	if( !lineout_thrown_event )
		return;

	// if we cant find a previous miss event before hitting the lineout throw event, then this is the first miss
	SSContextBucketEvent* first_miss_event =  bucket->GetLastEvent( start_event->record_id, SSCB_EVENT_LINEOUT_MISS, SSCB_EVENT_LINEOUT_THROW );
	bool both_teams_miss = first_miss_event != NULL;
	int miss_count = first_miss_event ? ((RUCBLineoutMissEvent*)first_miss_event)->miss_count + 1 : 1;
	RUCBLineoutMissEvent miss_event( game->GetSimTime()->GetAbsoluteStepCount(), lineout_thrown_event->team_index, both_teams_miss, miss_count );
	bucket->AddEvent( miss_event );
}

void RUContextBucketBasicEvents::StoreLineoutMaul( ARugbyCharacter* catcher )
{
	MABASSERT(catcher);
	if(!catcher)
		return;

	RUPlayerAttributes* attrs = catcher->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	RUCBLineoutToMaulEvent ev( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx );
	bucket->AddEvent( ev );
}

void RUContextBucketBasicEvents::StoreLineoutRun( ARugbyCharacter* catcher )
{
	MABASSERT(catcher);
	if(!catcher)
		return;

	RUPlayerAttributes* attrs = catcher->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	RUCBLineoutToRunEvent ev( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx );
	bucket->AddEvent( ev );
}

void RUContextBucketBasicEvents::StoreLineoutPass( ARugbyCharacter* catcher, ARugbyCharacter* pass_reciever )
{
	MABASSERT(catcher && pass_reciever);
	if(!catcher || !pass_reciever)
		return;

	int player_idx = catcher->GetAttributes()->GetDBPlayer()->GetDbId();
	int player2_idx = pass_reciever->GetAttributes()->GetDBPlayer()->GetDbId();

	RUCBLineoutToPassEvent ev( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, player2_idx );
	bucket->AddEvent( ev );
}

void RUContextBucketBasicEvents::StoreLineoutSlapDown( ARugbyCharacter* slapper, ARugbyCharacter* slapped_ball_receiver )
{
	MABASSERT(slapper);
	if(!slapper)
		return;

	RUPlayerAttributes* attrs = slapper->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int player2_idx = slapped_ball_receiver ?  slapped_ball_receiver->GetAttributes()->GetDBPlayer()->GetDbId() : -1;

	RUCBLineoutSlapDownEvent ev( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, player2_idx );
	bucket->AddEvent( ev );
}

void RUContextBucketBasicEvents::StoreLineoutFault( ARugbyCharacter* thrower, const FVector & /*lineout_position*/)
{
	if(!thrower)
		return;

	RUCBBasicPlayerEvent lineout_fault( game->GetSimTime()->GetAbsoluteStepCount(), SSCB_EVENT_LINEOUT_FAULT, thrower->GetAttributes()->GetDbId());
	bucket->AddEvent(lineout_fault);
}

void RUContextBucketBasicEvents::StoreTouchdownOwnIngoal(  RURuleConsequence consequence )
{

	if(game->GetGameTimer()->GetHalfObserver()->GetTimeExpired())
	{
		//the game is lying through its damn teeth. There will be no dropout, the ref is just getting a bit
		//confused and pretends to be calling for a dropout, where in fact when he gets off his fat lazy
		//ass he's going to blow his whistle for the end of half.
		return;
	}



	ARugbyCharacter* player = game->GetGameState()->GetBallHolder();

	if(!player)
		return;

	bool ball_received_in_goal = consequence == RUC_22_DROP_OUT;

	RUPlayerAttributes* attrs = player->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	int player2_index = -1;
	bool received_from_penalty_goal = false;

	//check back to see if there was an in-air event.
	MabVector<SSCB_TYPE> previous_types;
	previous_types.reserve(8);
	previous_types.push_back(SSCB_EVENT_KICK);
	previous_types.push_back(SSCB_EVENT_PASS);
	previous_types.push_back(SSCB_EVENT_KNOCK_ON);
	SSContextBucketEvent *previous_in_air_event = bucket->GetLastEvent(previous_types, SSCB_EVENT_STOPPAGE);

	if(previous_in_air_event && previous_in_air_event->event_type == SSCB_EVENT_KICK)
	{
		RUCBKickEvent *kick_event = MabCast<RUCBKickEvent>( previous_in_air_event );
		MABASSERT(kick_event);

		player2_index = kick_event->player_index;

		if(kick_event->kick_context == KC_PENALTY_GOAL)
		{
			received_from_penalty_goal = true;
		}
	}

	RUCBTouchdownOwnGoal touchdown_owngoal_ev(
		game->GetSimTime()->GetAbsoluteStepCount(),
		player_idx,
		team_idx,
		player2_index,
		ball_received_in_goal,
		received_from_penalty_goal);

	bucket->AddEvent( touchdown_owngoal_ev );
}

void RUContextBucketBasicEvents::StoreDeadBall( ARugbyCharacter* /*player*/, RURuleConsequence /*consequence*/)
{
	//StoreTouchdownOwnIngoal( consequence );
}

void RUContextBucketBasicEvents::StoreBallForced( ARugbyCharacter* /*player*/, RURuleConsequence consequence)
{
	StoreTouchdownOwnIngoal( consequence );
}

void RUContextBucketBasicEvents::StorePenalty( ARugbyCharacter* player, ARugbyCharacter* offended_player, const FVector& position, PENALTY_REASON penalty_reason, SUSPENSION_RESULT )
{
	MABUNUSED(offended_player);

	//if (penalty_reason == PENALTY_REASON_OFFSIDE)
	//{
	//	MABASSERT(false);
// 	//	// RyT - Advantage has been played, this should comment on going back for the penalty
	//	//StoreAdvantagePenaltyAwarded(player, offended_player, position, penalty_reason);
	//}
	//else
	//{
		// A little hacksy - These reasons are not penalties. ABROWN
		if( penalty_reason != PENALTY_REASON_LINEOUT_FAULT &&
			penalty_reason != PENALTY_REASON_SLOW_LINEOUT )
		{
			RUCBPenaltyEvent ev_penalty(
				game->GetSimTime()->GetAbsoluteStepCount(),
				player->GetAttributes()->GetDBPlayer()->GetDbId(),
				player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId(),
				penalty_reason,
				position );

			bucket->AddEvent( ev_penalty );
		}
		StoreStoppage();
	//}
}

void RUContextBucketBasicEvents::StoreFumble( ARugbyCharacter* player, bool from_tackle, const FVector& position )
{
	MABASSERT(player);
	if(!player)
		return;

	RUCBFumbleEvent ev_fumble( game->GetSimTime()->GetAbsoluteStepCount(),
		player->GetAttributes()->GetDBPlayer()->GetDbId(),
		player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId(),
		position,
		from_tackle);

	bucket->AddEvent( ev_fumble );
}

void RUContextBucketBasicEvents::StoreFowardPass( ARugbyCharacter* passer, ARugbyCharacter* reciever )
{
	MABASSERT( passer );
	if(!passer)
		return;
	MABASSERT( reciever );
	if(!reciever)
		return;

	int player_idx = passer->GetAttributes()->GetDBPlayer()->GetDbId();
	int reciever_idx = reciever->GetAttributes()->GetDBPlayer()->GetDbId();
	int team_idx = passer->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();

	RUCBFowardPassEvent foward_pass_ev( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, reciever_idx, team_idx );
	bucket->AddEvent( foward_pass_ev );
}

void RUContextBucketBasicEvents::StoreKnockOn( ARugbyCharacter* player, bool from_tackle, const FVector& position )
{
	MABASSERT(player);
	if(!player)
		return;

	RUCBKnockOnEvent ev_knock_on( game->GetSimTime()->GetAbsoluteStepCount(),
		player->GetAttributes()->GetDBPlayer()->GetDbId(),
		player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId(),
		position,
		from_tackle);

	bucket->AddEvent( ev_knock_on );
}

void RUContextBucketBasicEvents::StoreTackle( const RUTackleResult& tackle_result )
{
	StoreTackleInternal(tackle_result, SSCB_EVENT_TACKLE);
}

void RUContextBucketBasicEvents::StoreTackleeOnGround( const RUTackleResult &tackle_result)
{
	StoreTackleInternal(tackle_result, SSCB_EVENT_TACKLEE_ON_GROUND);
}

void RUContextBucketBasicEvents::StoreTackleInternal( const RUTackleResult& tackle_result, SSCB_TYPE tackle_event_type)
{
// if the ball is going to be dropped then don't bother
	if( tackle_result.drop_ball_type != TDT_NONE )
		return;

	//determine how impactful the tackle is
	float total_tackle_impetus = tackle_result.actual_tacklers_impetus + tackle_result.actual_tacklee_impetus;
	TackleImpactContext tackle_impact = TIC_LOW;
	if(total_tackle_impetus > 0.9f)
		tackle_impact = TIC_MEDIUM;
	if(total_tackle_impetus > 1.4f)
		tackle_impact = TIC_HIGH;


	const PlayInfo &play_info = game->GetStrategyHelper()->GetPlayInfo();
	float ground_made = play_info.bh_metres_gained;

	MABLOGDEBUG("RUContextBucketBasicEvents::StoreTackle() impetus: %f, impact: %d", total_tackle_impetus, tackle_impact);
	// TODO: Make sure that tackle enumeration changes are correct and remove
	// commented out old code.
	if((tackle_result.tackle_result == TRT_FEND2 && tackle_result.fend_tackle_type == FTT_FEND_SUCCESS)
		|| (tackle_result.tackle_result == TRT_SIDESTEP && (tackle_result.ss_tackle_type == SSTT_FOOLED || tackle_result.ss_tackle_type == SSTT_PARTIAL_FAIL))
		)
	{
		MABASSERT( !tackle_result.successful );
	}

	//determine if this player is the ball  holder
	if(game->GetGameState()->GetBallHolder() != tackle_result.tacklee)
	{
		//Glen: TODO: just leave this as a parameter on the tackle
		//indicating that the player no longer has the ball, or potentially never had the ball.
		return;
	}

		RUCBTackleEvent ev_tackle(
		tackle_event_type,
		game->GetSimTime()->GetAbsoluteStepCount(),
		tackle_result.tackle_result,
		tackle_result.successful,
		(int)tackle_result.dominance,
		tackle_result.tacklee->GetAttributes()->GetDBPlayer()->GetDbId(),
		tackle_result.tacklee->GetAttributes()->GetTeam()->GetDbTeam().GetDbId(),
		tackle_result.tacklers[0]->GetAttributes()->GetDBPlayer()->GetDbId(),
		tackle_result.tacklers[0]->GetAttributes()->GetTeam()->GetDbTeam().GetDbId(),
		tackle_result.tacklee->GetMabPosition(),
		tackle_impact,
		tackle_result.n_tacklers,
		(int)ground_made,
		play_info.bh_num_broken_tackles,
		(int)tackle_result.try_tackle_type);

	bucket->AddEvent( ev_tackle );
}

void RUContextBucketBasicEvents::StoreInjury(  ARugbyCharacter* player, TACKLE_INJURY_TYPE injury)
{
	RUCBInjuryEvent new_event(
		game->GetSimTime()->GetAbsoluteStepCount(),
		player->GetAttributes()->GetDBPlayer()->GetDbId(),
		injury
		);

	bucket->AddEvent( new_event );
}

void RUContextBucketBasicEvents::StoreFistPump( int player_index, int team_index )
{
	RUCBFistPumpEvent ev_fist_pump( game->GetSimTime()->GetAbsoluteStepCount(), player_index, team_index );
	//MABLOGDEBUG("size of fist pump object %i", sizeof(ev_fist_pump));
	bucket->AddEvent( ev_fist_pump );
}

void RUContextBucketBasicEvents::StoreNewPhase( RUTeam* team )
{
	MABASSERT(team);
	if(!team)
		return;

	RUCBNewPhaseEvent ev_new_phase( game->GetSimTime()->GetAbsoluteStepCount(), team->GetDbTeam().GetDbId() );
	//MABLOGDEBUG("size of new phase event object %i", sizeof(ev_new_phase));
	bucket->AddEvent( ev_new_phase );
}

void RUContextBucketBasicEvents::StoreBrokenTackle( const RUTackleResult& result )
{
	RUCBBrokenTackleEvent ev_tackle( game->GetSimTime()->GetAbsoluteStepCount(), result.tackle_result );
	//MABLOGDEBUG("size of broken tackle event object %i", sizeof(ev_tackle));
	bucket->AddEvent( ev_tackle );
}

void RUContextBucketBasicEvents::StoreKick(
	ARugbyCharacter* player,
	KickContext kick_context,
	KickType kick_type,
	const FVector& position )
{
	MABASSERT(player);
	if(!player)
		return;
	//MABLOGDEBUG( "StoreKick game step = %i", game->GetSimTime()->GetAbsoluteStepCount() );

	MABUNUSED( position );

	RUPlayerAttributes* attrs = player->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	FVector first_bounce( FVector::ZeroVector );
	float first_bounce_time = 0.0f;
	game->GetBall()->GetBouncePosition(1, first_bounce, first_bounce_time );
	FVector kick_distance( first_bounce - player->GetMabPosition() );
	kick_distance = kick_distance.GetAbs();

	//see if we can find the final position of the ball, within a certain number of bounces to determine if
	//the kick is likely to bounce out.
	float subsequent_bounce_time = 0.0f;
	FVector subsequent_bounce_position = FVector::ZeroVector;
	for(int i = 2;
		i <= 3 && game->GetBall()->GetBouncePosition(i, subsequent_bounce_position, subsequent_bounce_time ) && subsequent_bounce_time - first_bounce_time < 3.0f;
		i++)
	{
	}

	int pressure = int(game->GetStrategyHelper()->GetDefensivePressure( game->GetGameState()->GetAttackingTeam(), 4, 10.0f, player ) * 100.0f );

	SSSpatialHelper* spatial_helper = game->GetSpatialHelper();
	ARugbyCharacter* closest_opposition = spatial_helper->FindClosestPlayer(player, &attrs->GetOppositionTeam()->GetPlayers() );
	int player2_idx = closest_opposition->GetAttributes()->GetDBPlayer()->GetDbId();

	int first_bounce_field_position = (int)(first_bounce.z * (int)attrs->GetTeam()->GetPlayDirection() + FIELD_LENGTH * 0.5f);

	bool out_on_full = false;
	bool bounce_out_probable = false;
	out_on_full = !game->GetStrategyHelper()->IsPointInFieldOfPlay( first_bounce.x, first_bounce.z);

	if(!out_on_full)
	{
		bounce_out_probable = MabMath::Fabs(subsequent_bounce_position.x) > FIELD_WIDTH / 2.0f
			&& MabMath::Fabs(subsequent_bounce_position.z) < (FIELD_LENGTH) / 2.0f;
	}

	RUCBKickEvent ev_kick( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, player2_idx,
						   player->GetMabPosition(),
						   kick_distance,
						   kick_type,
						   kick_context,
						   pressure,
						   out_on_full,
						   bounce_out_probable,
						   first_bounce_field_position,
						   (int)MabMath::Fabs(first_bounce.x) );
	bucket->AddEvent( ev_kick );
}

void RUContextBucketBasicEvents::StoreKickOffOutOnFull(const FVector& position, ARugbyCharacter* offending_player)
{
	MABUNUSED(position);
	MABUNUSED(offending_player);

	StoreGenericFollowUp(-1, -1, SSCB_EVENT_KICKOFF_OUT_ON_FULL);
}

void RUContextBucketBasicEvents::StoreKickAndChase( ARugbyCharacter* closest_player )
{
	MABASSERT(closest_player);
	if(!closest_player)
		return;

	//see if we can find the final position of the ball, within a certain number of bounces to determine if
	//the kick is likely to bounce out.
	float subsequent_bounce_time = 0.0f;
	FVector subsequent_bounce_position = FVector::ZeroVector;
	for(int i = 2;
		i <= 4 && game->GetBall()->GetBouncePosition(i, subsequent_bounce_position, subsequent_bounce_time );
		i++)
	{
	}

	//if the ball is likely to bounce out don't bother with a kick and chase event
	if(!game->GetStrategyHelper()->IsPointInFieldOfPlay(subsequent_bounce_position.x, subsequent_bounce_position.z))
	{
		return;
	}

	RUCBKickAndChaseEvent ev( game->GetSimTime()->GetAbsoluteStepCount(),
		closest_player->GetAttributes()->GetDBPlayer()->GetDbId(),
		closest_player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId() );

	bucket->AddEvent( ev );
}

void RUContextBucketBasicEvents::StoreConversionResult( bool success, const FVector& ball_crossed_goal_position  )
{
	//MABLOGDEBUG( "StoreKickResult game step = %i", game->GetSimTime()->GetAbsoluteStepCount() );
	MabVector< RUCBKickResultEvent* > results;
	// get the last kick record and copy its info
	SSContextBucketEvent* kick_ev = bucket->GetLastEvent( SSCB_EVENT_KICK );
	if( kick_ev )
	{
		RUCBKickEvent* kick_event = (RUCBKickEvent*)kick_ev;
		int player_idx = kick_event->player_index;
		int team_idx = kick_event->team_index;
		KickContext kick_context = (KickContext)kick_event->kick_context;
		MABASSERT( kick_context == KC_CONVERSION || kick_context == KC_PENALTY_GOAL );

		int player2_index = -1;
		SSContextBucketEvent* try_ev = bucket->GetLastEvent( SSCB_EVENT_TRY );
		if( try_ev )
		{
			RUCBTryEvent* try_event = (RUCBTryEvent*)try_ev;
			player2_index = try_event->player_index;

			RUCBKickResultEvent ev_kick_result(
				game->GetSimTime()->GetAbsoluteStepCount(),
				SSCB_EVENT_KICK_RESULT,
				success,
				player_idx,
				team_idx,
				ball_crossed_goal_position,
				kick_context,
				player2_index,
				game->GetBall()->GetNumPostStrikes());

			bucket->AddEvent( ev_kick_result );
		}
	}
}

void RUContextBucketBasicEvents::StorePenaltyGoalAnticipate( bool success, const FVector &ball_crossed_goal_position )
{
	MabVector< RUCBKickResultEvent* > results;
	// get the last kick record and copy its info
	SSContextBucketEvent* kick_ev = bucket->GetLastEvent( SSCB_EVENT_KICK );
	if( kick_ev )
	{
		RUCBKickEvent* kick_event = (RUCBKickEvent*)kick_ev;
		int player_idx = kick_event->player_index;
		int team_idx = kick_event->team_index;
		KickContext kick_context = (KickContext)kick_event->kick_context;
		MABASSERT( kick_context == KC_CONVERSION || kick_context == KC_PENALTY_GOAL );

		int player2_index = -1;
		RUCBKickResultEvent ev_kick_result(
			game->GetSimTime()->GetAbsoluteStepCount() + (int)(game->GetSimTime()->GetTimeStepRate() * PENALTY_GOAL_ANTICIPATE_DELAY) ,
			SSCB_EVENT_KICK_RESULT_ANTICIPATE,
			success,
			player_idx,
			team_idx,
			ball_crossed_goal_position,
			kick_context,
			player2_index,
			game->GetBall()->GetNumPostStrikes());

		bucket->AddEventDelayed( ev_kick_result );
	}
}

void RUContextBucketBasicEvents::StorePenaltyGoalResult(bool success, const FVector &ball_crossed_goal_position )
{
	MabVector< RUCBKickResultEvent* > results;
	// get the last kick record and copy its info
	SSContextBucketEvent* kick_ev = bucket->GetLastEvent( SSCB_EVENT_KICK );
	if( kick_ev )
	{
		RUCBKickEvent* kick_event = (RUCBKickEvent*)kick_ev;
		int player_idx = kick_event->player_index;
		int team_idx = kick_event->team_index;
		KickContext kick_context = (KickContext)kick_event->kick_context;
		MABASSERT( kick_context == KC_CONVERSION || kick_context == KC_PENALTY_GOAL );

		int player2_index = -1;
		RUCBKickResultEvent ev_kick_result(
			game->GetSimTime()->GetAbsoluteStepCount(),
			SSCB_EVENT_KICK_RESULT,
			success,
			player_idx,
			team_idx,
			ball_crossed_goal_position,
			kick_context,
			player2_index,
			game->GetBall()->GetNumPostStrikes());

		bucket->AddEvent( ev_kick_result );
	}
}

void RUContextBucketBasicEvents::StoreConversionTransition( ARugbyCharacter* conversion_kicker )
{
	MABASSERT( conversion_kicker );
	if( !conversion_kicker )
		return;

	RUPlayerAttributes* attrs = conversion_kicker->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	int abs_x_position = (int)MabMath::Abs( game->GetGameState()->GetPlayRestartPosition().x );

	int kicker_team_score = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( conversion_kicker->GetAttributes()->GetTeam(), &RUDB_STATS_TEAM::score);
	int other_team_score = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( conversion_kicker->GetAttributes()->GetOppositionTeam(), &RUDB_STATS_TEAM::score);
	int score_difference = kicker_team_score - other_team_score;


	RUCBKickAtPostsReadyEvent ev_conversion_transition( SSCB_CONVERSION_TRANSITION, game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, abs_x_position, score_difference, true, false );
	bucket->AddEvent(ev_conversion_transition);
}

void RUContextBucketBasicEvents::StoreConversionAnticipation( bool success, const FVector &ball_crossed_goal_position)
{
	//MABLOGDEBUG( "StoreKickResult game step = %i", game->GetSimTime()->GetAbsoluteStepCount() );
	MabVector< RUCBKickResultEvent* > results;

	// get the last kick record and copy its info
	SSContextBucketEvent* kick_ev = bucket->GetLastEvent( SSCB_EVENT_KICK );
	ASSBall *ball = game->GetBall();
	if( kick_ev )
	{
		RUCBKickEvent* kick_event = (RUCBKickEvent*)kick_ev;
		int player_idx = kick_event->player_index;
		int team_idx = kick_event->team_index;
		KickContext kick_context = (KickContext)kick_event->kick_context;
		MABASSERT( kick_context == KC_CONVERSION || kick_context == KC_PENALTY_GOAL );

		int player2_index = -1;
		SSContextBucketEvent* try_ev = bucket->GetLastEvent( SSCB_EVENT_TRY );
		if( try_ev )
		{
			RUCBTryEvent* try_event = (RUCBTryEvent*)try_ev;
			player2_index = try_event->player_index;

			RUCBKickResultEvent ev_kick_result(
				game->GetSimTime()->GetAbsoluteStepCount() + (int)(game->GetSimTime()->GetTimeStepRate() * CONVERSION_ANTICIPATE_DELAY),
				SSCB_EVENT_KICK_RESULT_ANTICIPATE,
				success,
				player_idx,
				team_idx,
				ball_crossed_goal_position,
				kick_context,
				player2_index,
				ball->GetNumPostStrikes());

			bucket->AddEventDelayed( ev_kick_result );
		}
	}
}

void RUContextBucketBasicEvents::StoreDropGoalResult( bool success, const FVector& position )
{
	//MABLOGDEBUG( "StoreKickResult game step = %i", game->GetSimTime()->GetAbsoluteStepCount() );
	MabVector< RUCBKickResultEvent* > results;

// 	RUCBBallInTouchEvent* touch_ev = (RUCBBallInTouchEvent*)bucket->GetLastEvent(SSCB_EVENT_BALL_IN_TOUCH);
// 	if (touch_ev && MabMath::Abs(touch_ev->position_z) < 50.0f)
// 	{
// 		// RyT - Dont report on drop goal after it has gone so wide
// 		return;
// 	}

	// get the last kick record and copy its info
	SSContextBucketEvent* kick_ev = bucket->GetLastEvent( SSCB_EVENT_KICK );

	ASSBall *ball = game->GetBall();
	if( kick_ev )
	{
		RUCBKickEvent* kick_event = (RUCBKickEvent*)kick_ev;
		int player_idx = kick_event->player_index;
		int team_idx = kick_event->team_index;
		KickContext kick_context = (KickContext)kick_event->kick_context;
		MABASSERT( kick_context == KC_DROPGOAL );
		RUCBKickResultEvent ev_kick_result(
			game->GetSimTime()->GetAbsoluteStepCount(),
			SSCB_EVENT_KICK_RESULT,
			success,
			player_idx,
			team_idx,
			position,
			kick_context,
			-1,
			ball->GetNumPostStrikes()
			);

		bucket->AddEvent( ev_kick_result );
	}

}

void RUContextBucketBasicEvents::StorePass( ARugbyCharacter* player_from,
											ARugbyCharacter* player_to,
											const FVector& target_position,
											PASS_TYPE pass_type,
											bool success )
{
	if ( game->GetGameState()->GetPhase() == RUGamePhase::QUICK_LINEOUT )
	{
		// If this is from a quick lineout, we don't want to play the pass commentary, we want the quick lineout taken commentary instead.
		return;
	}

	MABASSERT( player_from );
	if( !player_from )
		return;

	int player_from_idx = -1;
	int player_to_idx = -1;
	int team_from_idx = -1;
	int team_to_idx = -1;

	if(player_from)
	{
		RUPlayerAttributes* player_from_attrs = player_from->GetAttributes();
		player_from_idx =  player_from_attrs->GetDBPlayer()->GetDbId();
		team_from_idx = player_from_attrs->GetTeam()->GetDbTeam().GetDbId();
	}

	if(player_to)
	{
		RUPlayerAttributes* player_to_attrs = player_to->GetAttributes();
		player_to_idx = player_to_attrs->GetDBPlayer()->GetDbId();
		team_to_idx = player_to_attrs->GetTeam()->GetDbTeam().GetDbId();
	}

	RUCommentaryPassLength commentary_pass_length = PL_SHORT_PASS;

	FVector relative_location;
	if(player_from && player_to)
	{
		relative_location = player_to->GetMabPosition() - player_from->GetMabPosition();
	}
	else
	{
		relative_location = target_position;
	}

	float relative_distance_squared = relative_location.SquaredMagnitude();

	if(relative_distance_squared > LONG_PASS_MIN_DISTANCE * LONG_PASS_MIN_DISTANCE && pass_type != PT_PLAYTHEBALL)
	{
		commentary_pass_length = PL_LONG_PASS;
	}
	else if(relative_distance_squared > MEDIUM_PASS_MIN_DISTANCE * MEDIUM_PASS_MIN_DISTANCE && pass_type != PT_PLAYTHEBALL)
	{
		commentary_pass_length = PL_MEDIUM_PASS;
	}

	//fill out the pass type
	ARugbyCharacter* player_cutout = NULL;
	bool bad_pass = false;
	RUCommentaryPassType commentary_pass_type = CalculatePassType( player_from, player_to, relative_location, pass_type, success, &player_cutout, &bad_pass);

	int player_cutout_idx = player_cutout ? player_cutout->GetAttributes()->GetDBPlayer()->GetDbId() : -1;

	RUCommentaryPassSource pass_source = PS_NORMAL;

	if( player_from->GetRole()->RTTGetType() == RURoleRuckScrumHalf::RTTGetStaticType())
		pass_source = PS_FROM_RUCK;
	else if( player_from->GetRole()->RTTGetType() == RURoleMaulHalfback::RTTGetStaticType())
		pass_source = PS_FROM_MAUL;

	RUCBPassEvent ev_pass( game->GetSimTime()->GetAbsoluteStepCount(),
						   player_from_idx,
						   player_to_idx,
						   player_cutout_idx,
						   team_from_idx,
						   team_to_idx,
						   FVector( target_position.x, 0, target_position.z /*rel_z_pos*/ ),
						   relative_location.x,
						   commentary_pass_type,
						   commentary_pass_length,
						   pass_source,
						   bad_pass);

	MABLOGDEBUG("size of pass event object %i, size of biggest event %i", sizeof(ev_pass), sizeof(SSCBBiggestEvent));
	bucket->AddEvent( ev_pass );
}

void RUContextBucketBasicEvents::StoreDummyPass( ARugbyCharacter* player)
{
	FVector player_position = game->GetBall()->GetVisualPosition();
	if(player)
	{
		player_position = player->GetMabPosition();
	}

	RUCBPassEvent ev_pass( game->GetSimTime()->GetAbsoluteStepCount(),
		player ? player->GetAttributes()->GetDbId() : -1,
		-1,
		-1,
		player ? player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId() : -1,
		-1,
		FVector( player_position.x, 0, player_position.z ),
		0.0f,
		CPT_DUMMY_PASS,
		PL_SHORT_PASS,
		PS_NORMAL,
		false);

	bucket->AddEvent( ev_pass );
}

RUCommentaryPassType RUContextBucketBasicEvents::CalculatePassType(
	ARugbyCharacter* player_from,
	ARugbyCharacter* player_to,
	const FVector &relative_location,
	PASS_TYPE pass_type,
	bool success,
	ARugbyCharacter* *player_cutout,
	bool *bad_pass_out)
{
	//Glen: figure out where to store the type enumerations, it may be used by other users of
	//the context bucket
	*player_cutout = NULL;

	*bad_pass_out = (!success || !player_to) && pass_type != PT_PLAYTHEBALL;

	if(pass_type == PT_OFFLOAD)
	{
		return CPT_OFFLOAD_PASS;
	}

	if (pass_type == PT_PLAYTHEBALL)
	{
		return CPT_PLAYTHEBALL;
	}

	if(pass_type == PT_DUMMY)
	{
		return CPT_DUMMY_PASS;
	}

	if(pass_type == PT_DUMMYHALF)
	{
		return CPT_DUMMYHALF_PASS;
	}

	if(pass_type == PT_LINEOUT)
	{
		return CPT_LINEOUT_PASS;
	}

	//see if its another type of pass

	//Glen: TODO: a pass may fulfil the criteria for multiple categories
	//we should let the commentary system determine which category to react to
	//rather than deciding some arbitrary exclusive priority order here.


	//check for a supporting player pass
	if(game->GetStrategyHelper()->GetCurrentLineBreakDetails().type != LB_NONE)
	{
		//do the calculation for determining if there is currently a line break, which will determine
		//if this is a supporting pass.

		//Glen: TODO: need to make sure that the receiver is actually in a supporting position
		return CPT_SUPPORT_PLAYER_PASS;
	}

	//Do a check to see if this is an inside pass.
	//need to look back through the context bucket and look for a previous pass for this passage of play
	//and if the pass direction has changed, then it is an 'inside' pass.
	MabVector<SSContextBucketEvent*> events;
	int num_events = bucket->GetEvents( events, 10, (MabUInt32)(SIMULATION_RATE * 30.0f), false, SSCB_CLASS_NONE, SSCB_EVENT_TYPE_NONE );

	//look back through the list of events, if we find a pass before we find an event that marks an interruption in the smooth
	//flow of passes, then we can consider that pass to be indicating the current direction of play
	bool inside = false;
	if(num_events > 0)
	{
		for(int i = num_events - 1; i >= 0; i--)
		{
			SSContextBucketEvent *event = events[i];
			if(event->event_type == SSCB_EVENT_PASS)
			{
				//see if the direction of passing is different to the current
				//pass direction
				RUCBPassEvent *pass_event = MabCast<RUCBPassEvent>(event);
				inside = MabMath::Sign( pass_event->pass_direction ) != MabMath::Sign( relative_location.x);
				break;
			}
			else if(event->event_type == SSCB_EVENT_RUCK
				|| event->event_type == SSCB_TIMING_NEW_PHASE
				|| event->event_type == SSCB_EVENT_LINEOUT_SIGNALLED
				|| event->event_type == SSCB_EVENT_SCRUM_TOUCH_SIGNALLED
				|| event->event_type == SSCB_EVENT_KICK
				|| event->event_type == SSCB_EVENT_PENALTY
				|| event->event_type == SSCB_EVENT_COLLECTED
				|| event->event_type == SSCB_EVENT_FUMBLE
				|| event->event_type == SSCB_EVENT_KNOCK_ON)
			{
				//we've encountered a disruption to passing play before finding
				//the previous pass, so we won't be able to determine if this pass is 'inside'
				break;
			}
		}
	}

	if(inside)
	{
		return CPT_INSIDE_PASS;
	}

	float relative_distance_squared = relative_location.SquaredMagnitude();

	//check for a back pass
	if( relative_location.z != 0.0f
		&& MabMath::Tan(BACK_PASS_THRESHOLD_ANGLE) > MabMath::Fabs(relative_location.x / relative_location.z)
		&& relative_distance_squared > BACK_PASS_MIN_DISTANCE * BACK_PASS_MIN_DISTANCE)
	{
		return CPT_BACK_PASS;
	}

	//check for an over the top pass
	//PT_OVER_THE_TOP_PASS

	//check for a cutout pass
	bool cutout_pass = false;
	if (player_to != nullptr)
	{
		TArray<ARugbyCharacter*> receivers;
		int pass_inclusion_role_count = 0;
		RUActionPass::CalculateReceiversInPassDirection(player_from, int(FMath::Sign(relative_location.x)), receivers, pass_inclusion_role_count);

		int separating_players = 0;
		for (ARugbyCharacter* receiver : receivers)
		{
			if (receiver == player_to)
			{
				//only mark this as a cutout pass when we encounter the receiver. It is possible
				//that we've failed to find the receiver, in which case we shouldn't count it as
				//a cutout.
				cutout_pass = separating_players > 0;
				break;
			}

			//if it isn't the receiver then they may be between the passer and
			//receiver.
			++separating_players;
			if(! (*player_cutout))
			{
				*player_cutout = receiver;
			}
		}
	}

	if(cutout_pass)
	{
		return CPT_CUTOUT_PASS;
	}

	//check for a flat pass
	if( relative_location.x != 0.0f
		&& MabMath::Tan(FLAT_PASS_THRESHOLD_ANGLE) > MabMath::Fabs(relative_location.z / relative_location.x))
	{
		//the relative location will have a low z value compared to x value
		return CPT_FLAT_PASS;
	}

	//check for a 'flick' pass
	//PT_FLICK_PASS


	return CPT_NORMAL_PASS;
}

void RUContextBucketBasicEvents::StoreForwardPass(
	ARugbyCharacter* player_from,
	ARugbyCharacter* player_to)
{
	MABASSERT( player_from );
	if( !player_from )
		return;

	MABASSERT( player_to);
	if(!player_to)
		return;

	int player_from_idx = -1;
	int player_to_idx = -1;
	int team_from_idx = -1;
	int team_to_idx = -1;

	RUPlayerAttributes* player_from_attrs = player_from->GetAttributes();
	player_from_idx =  player_from_attrs->GetDBPlayer()->GetDbId();
	team_from_idx = player_from_attrs->GetTeam()->GetDbTeam().GetDbId();

	RUPlayerAttributes* player_to_attrs = player_to->GetAttributes();
	player_to_idx = player_to_attrs->GetDBPlayer()->GetDbId();
	team_to_idx = player_to_attrs->GetTeam()->GetDbTeam().GetDbId();

	RUCommentaryPassLength commentary_pass_length = PL_SHORT_PASS;

	FVector relative_location = player_to->GetMabPosition() - player_from->GetMabPosition();

	float relative_distance_squared = relative_location.SquaredMagnitude();

	if(relative_distance_squared > LONG_PASS_MIN_DISTANCE * LONG_PASS_MIN_DISTANCE)
	{
		commentary_pass_length = PL_LONG_PASS;
	}
	else if(relative_distance_squared > MEDIUM_PASS_MIN_DISTANCE * MEDIUM_PASS_MIN_DISTANCE)
	{
		commentary_pass_length = PL_MEDIUM_PASS;
	}

	//fill out the pass type
	RUCommentaryPassType commentary_pass_type = CPT_FORWARD_PASS;
	RUCommentaryPassSource pass_source = PS_NORMAL;

	if( player_from->GetRole()->RTTGetType() == RURoleRuckScrumHalf::RTTGetStaticType())
		pass_source = PS_FROM_RUCK;
	else if( player_from->GetRole()->RTTGetType() == RURoleMaulHalfback::RTTGetStaticType())
		pass_source = PS_FROM_MAUL;

	RUCBPassEvent ev_pass( game->GetSimTime()->GetAbsoluteStepCount(),
		player_from_idx,
		player_to_idx,
		-1,
		team_from_idx,
		team_to_idx,
		player_to->GetMabPosition(),
		relative_location.x,
		commentary_pass_type,
		commentary_pass_length,
		pass_source,
		false);

	MABLOGDEBUG("size of pass event object %i, size of biggest event %i", sizeof(ev_pass), sizeof(SSCBBiggestEvent));
	bucket->AddEvent( ev_pass );
}

bool RUContextBucketBasicEvents::HandleTerritoryKick(
	ARugbyCharacter*  /*holder*/,
	const FVector & /*touch_position*/,
	const FVector &restart_position,
	bool on_the_full )
{
	SSContextBucketEvent* ev = bucket->GetLastEvent( SSCB_EVENT_KICK );
	MABASSERT( ev );
	if(!ev)
		return false;

	RUCBKickEvent* kick_event = (RUCBKickEvent*)ev;

	switch ((KickContext)kick_event->kick_context)
	{
		// reject obvious kick contexts where a gain in territory cant occur
	case KC_NONE:
	case KC_KICKOFF:
	case KC_DROPOUT:
	case KC_DROPGOAL:
	case KC_PENALTY_GOAL:
	case KC_CONVERSION:
		return false;
		break;
	case KC_FREEPLAY:
	case KC_FREEBALL_KICK:
	case KC_PENALTY_TOUCH:
	case KC_FREEKICK:
	case KC_BOXKICK:
	default:
		break;
	}

	const BallFreeInfo& ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();
	float z_difference = ball_free_info.pos.z - restart_position.z;

	RUCBTerritoryKickEvent territory_kick_event( game->GetSimTime()->GetAbsoluteStepCount(), kick_event->player_index, kick_event->team_index, MabMath::Abs( z_difference ), on_the_full );
	bucket->AddEvent( territory_kick_event );
	return true;
}

void RUContextBucketBasicEvents::HandleBallHolderInTouch( ARugbyCharacter* holder, const FVector& touch_position )
{
	MABASSERT( holder );
	if(!holder)
		return;

	bool tackled_into_touch = holder->GetActionManager()->IsActionRunning(ACTION_TACKLEE);

	RUPlayerAttributes* player_from_attrs = holder->GetAttributes();
	int player_from_idx =  player_from_attrs->GetDBPlayer()->GetDbId();
	int team_from_idx = player_from_attrs->GetTeam()->GetDbTeam().GetDbId();

	RUCBCarriedOutOfPlayEvent carried_out_of_play_event( game->GetSimTime()->GetAbsoluteStepCount(), player_from_idx, team_from_idx, tackled_into_touch, touch_position );
	bucket->AddEvent( carried_out_of_play_event );
}

void RUContextBucketBasicEvents::HandleBallDeadDetected( ARugbyCharacter* holder, const FVector& dead_position, bool on_the_full )
{
	MABUNUSED(on_the_full);

	SSContextBucketEvent* ev = bucket->GetLastEvent( SSCB_EVENT_KICK );
	if(ev /*&& on_the_full*/)
	{
		RUCBKickEvent* kick_event = (RUCBKickEvent*)ev;

		if ((KickContext)kick_event->kick_context == KC_DROPGOAL)
		{
			// RyT - Hack to stop dead ball comment firing before missing drop kick comment
			return;
		}
	}

	int player_from_idx = -1;
	if(holder)
	{
		player_from_idx = holder->GetAttributes()->GetDbId();
	}
	else if(game->GetGameState()->GetLastBallHolder())
	{
		//grab the player from the kick event, or the last ball holder from the game.
		player_from_idx = game->GetGameState()->GetLastBallHolder()->GetAttributes()->GetDbId();
	}

	const BallFreeInfo &ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();

	if( game->GetGameState()->IsGameInStandardPlay() )
	{
		RUCBBallDeadEvent ball_dead_event( game->GetSimTime()->GetAbsoluteStepCount(), player_from_idx, dead_position, ball_free_info.pos, holder != NULL );
		bucket->AddEvent( ball_dead_event );
	}
}

void RUContextBucketBasicEvents::HandleBallOutRuleTrigger(
	ARugbyCharacter* holder,
	const FVector& touch_position,
	const FVector& restart_position,
	bool on_the_full,
	bool is_carried_out )
{
	SSContextBucketEvent* ev = bucket->GetLastEvent( SSCB_EVENT_CATCH );
	if (ev)
	{
		RUCBCatchEvent* catch_event = (RUCBCatchEvent*)ev;
		if(catch_event->catch_type == CBCT_FROM_PENALTY_TOUCH)
		{
			// RyT - Hack to fix in touch comments firing straight after bad kick to touch comments,
			// happens when a player catches the ball inside the field and
			// lands outside the field during a penalty kick to touch
			return;
		}
	}

	const BallFreeInfo &ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();
	BALL_FREE_EVENT ball_free_event = ball_free_info.event;
	if( !is_carried_out && ball_free_event == BFE_KICK )
	{
		if( !HandleTerritoryKick(holder, touch_position, restart_position, on_the_full ) )
		{
			holder = ball_free_info.last_player;
			MABASSERT( holder );
			// was not a good kick, generate generic touch event
			if(holder)
				StoreGenericTouchEvent( holder, touch_position );
		}
	}
	else if( is_carried_out )
	{
		HandleBallHolderInTouch( holder, touch_position );
	}
	else
	{
		if(!holder)
			holder = ball_free_info.last_player;
		MABASSERT(holder);
		if(holder)
			StoreGenericTouchEvent( holder, touch_position );
	}
}

void RUContextBucketBasicEvents::StoreGenericTouchEvent( ARugbyCharacter* holder, const FVector& touch_position )
{
	MABASSERT(holder);
	if(!holder)
		return;

	// use generic touch record
	RUPlayerAttributes* player_from_attrs = holder->GetAttributes();
	int player_from_idx =  player_from_attrs->GetDBPlayer()->GetDbId();
	int team_from_idx = player_from_attrs->GetTeam()->GetDbTeam().GetDbId();

	RUCBBallInTouchEvent touch_ev( game->GetSimTime()->GetAbsoluteStepCount(), player_from_idx, team_from_idx, touch_position );
	bucket->AddEvent( touch_ev );
}

RUCBCatchType RUContextBucketBasicEvents::CalculateCatchType(
	int team_index,
	int &player_from_out,
	KickContext &kick_context_out,
	KickType &kick_type_out,
	bool &marked_catch_out,
	int bounces )
{
	player_from_out = -1;

	kick_context_out = KC_NONE;
	kick_type_out = KICKTYPE_NONE;

	//find the previous event that may have gotten the ball into the air
	MabVector<SSContextBucketEvent*> events;
	int num_events = bucket->GetEvents( events, 10, (MabUInt32)(SIMULATION_RATE * 30.0f), false, SSCB_CLASS_GAMEPLAY, SSCB_EVENT_TYPE_NONE );
	if(num_events > 0)
	{
		for(int i = num_events - 1; i >= 0; i--)
		{
			SSContextBucketEvent *event = events[i];
			if(event->event_type == SSCB_EVENT_MARK)
			{
				marked_catch_out = true;
			}
			else if (event->event_type == SSCB_EVENT_LINEOUT_THROW )
			{
				return CBCT_FROM_LINEOUT;
			}
			else if(event->event_type == SSCB_EVENT_PASS)
			{
				RUCBPassEvent *pass_event = MabCast<RUCBPassEvent>(event);
				if(pass_event->action_type == CPT_DUMMY_PASS)
				{
					//this was a dummy pass, so we couldn't have caught from this.
					continue;
				}

				//cannot mark a catch from a pass
				marked_catch_out = false;

				player_from_out = pass_event->player_index;

				if(pass_event->action_type == CPT_LINEOUT_PASS)
				{
					return CBCT_FROM_LINEOUT;
				}
				else if( pass_event->team_index == team_index )
				{
					return CBCT_FROM_PASS;
				}
				else
				{
					return CBCT_FROM_PASS_INTERCEPT;
				}
			}
			else if( event->event_type == SSCB_EVENT_KICK)
			{
				//check to see if this is a kickoff kick, or some other type of
				//generic kick
				RUCBKickEvent *kick_event = MabCast<RUCBKickEvent>(event);
				player_from_out = kick_event->player_index;

				kick_context_out = (KickContext) kick_event->kick_context;
				kick_type_out = (KickType) kick_event->kick_type;

				if(kick_event->kick_context == KC_KICKOFF)
				{
					return CBCT_FROM_KICKOFF;
				}
				else if(kick_event->kick_context == KC_PENALTY_GOAL)
				{
					return CBCT_FROM_PENALTY_GOAL;
				}
				else if(kick_event->kick_context == KC_PENALTY_TOUCH)
				{
					return CBCT_FROM_PENALTY_TOUCH;
				}
				else if( bounces == 1 )
				{
					return CBCT_FROM_BOUNCE;
				}
				else if(kick_event->kick_type == KICKTYPE_UPANDUNDER)
				{
					return CBCT_FROM_BOMB;
				}
				else
				{
					return CBCT_FROM_KICK;
				}

				break;
			}
			else if ( event->event_type == SSCB_EVENT_QUICK_THROW_IN )
			{
				return CBCT_FROM_LINEOUT;
			}
		}
	}

	//Glen: TODO: see how this ball got into the air, and add detection logic for it.
MABBREAK();

	return CBCT_UNKNOWN;
}

void RUContextBucketBasicEvents::StoreCrowdIntensityEvent()
{
	//Glen: TODO: this event gets fired quite frequently.
	bucket->AddEvent( RUCBCrowdIntensityEvent( game->GetSimTime()->GetAbsoluteStepCount() ) );
}

void RUContextBucketBasicEvents::StorePlayerWalkOnEvent()
{
	bucket->AddEvent( RUCBPlayerWalkOnEvent( game->GetSimTime()->GetAbsoluteStepCount() ) );
}

void RUContextBucketBasicEvents::StoreTryLikely( ARugbyCharacter* player )
{
	MABASSERT( player );
	if(!player)
		return;

	RUPlayerAttributes* attrs = player->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	RUCBTryLikelyEvent ev_try_likely( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx );
	//MABLOGDEBUG("size of try event object %i", sizeof(ev_try));
	bucket->AddEvent( ev_try_likely );
}

void RUContextBucketBasicEvents::StoreBallHolderRunningEvent( ARugbyCharacter* player, BallHolderRunningTypes running_type)
{
	MABASSERT( player );
	if(!player)
		return;

	//no running events if the ballholder isn't running
	if(game->GetGameState()->GetPhase() != RUGamePhase::PLAY)
		return;

	RUPlayerAttributes* attrs = player->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	ARugbyCharacter* supporting_player = game->GetStrategyHelper()->GetPlayInfo().bh_supporting_player;
	int player2_idx = supporting_player ? supporting_player->GetAttributes()->GetDBPlayer()->GetDbId() : -1;

	RUCBBallHolderRunningEvent running_event( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, player2_idx, team_idx, running_type );
	bucket->AddEvent( running_event );
}

void RUContextBucketBasicEvents::StoreBreakdownContest( ARugbyCharacter*, ARugbyCharacter* )
{
	bucket->AddEvent( SSContextBucketEvent( SSCB_CLASS_GAMEPLAY, SSCB_EVENT_BREAKDOWN_CONTEST, game->GetSimTime()->GetAbsoluteStepCount() ) );
}

void RUContextBucketBasicEvents::StoreBreakdownHoldingOn( ARugbyCharacter*, ARugbyCharacter* )
{
	bucket->AddEvent( SSContextBucketEvent( SSCB_CLASS_GAMEPLAY, SSCB_EVENT_BREAKDOWN_HOLDING_ON, game->GetSimTime()->GetAbsoluteStepCount() ) );
}

void RUContextBucketBasicEvents::StoreBreakdownHoldingOnIllegal( ARugbyCharacter*, ARugbyCharacter* )
{
	bucket->AddEvent( SSContextBucketEvent( SSCB_CLASS_GAMEPLAY, SSCB_EVENT_BREAKDOWN_HOLDING_ON_ILLEGAL, game->GetSimTime()->GetAbsoluteStepCount() ) );
}

void RUContextBucketBasicEvents::StoreBreakdownShouldRelease( ARugbyCharacter*, ARugbyCharacter* )
{
	bucket->AddEvent( SSContextBucketEvent( SSCB_CLASS_GAMEPLAY, SSCB_EVENT_BREAKDOWN_SHOULD_RELEASE, game->GetSimTime()->GetAbsoluteStepCount() ) );
}

void RUContextBucketBasicEvents::StoreBreakdownShouldReleaseIllegal( ARugbyCharacter*, ARugbyCharacter* )
{
	bucket->AddEvent( SSContextBucketEvent( SSCB_CLASS_GAMEPLAY, SSCB_EVENT_BREAKDOWN_SHOULD_RELEASE_ILLEGAL, game->GetSimTime()->GetAbsoluteStepCount() ) );
}

void RUContextBucketBasicEvents::StoreBreakdownTurnover( ARugbyCharacter*, ARugbyCharacter* )
{
	bucket->AddEvent( SSContextBucketEvent( SSCB_CLASS_GAMEPLAY, SSCB_EVENT_BREAKDOWN_TURNOVER, game->GetSimTime()->GetAbsoluteStepCount() ) );
}

void RUContextBucketBasicEvents::StoreTryAttempt( ARugbyCharacter* player, bool is_diving)
{
	MABASSERT( player );
	if(!player)
		return;

	RUPlayerAttributes* attrs = player->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	RUCBTryAttemptEvent ev_try_attempt( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, is_diving );
	//MABLOGDEBUG("size of try event object %i", sizeof(ev_try));
	bucket->AddEvent( ev_try_attempt );
}

void RUContextBucketBasicEvents::StoreTry( bool success, bool penalty, ARugbyCharacter* player )
{
	MABASSERT( player );
	if(!player)
		return;

	MABUNUSED( penalty );

	RUPlayerAttributes* attrs = player->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	TryLocationContext location_context = TLC_UNDER_POSTS_TRY;
	FVector ball_position( game->GetBall()->GetCurrentPosition(true) );
	float ball_position_x( MabMath::Abs( ball_position.x ) );
	const float WIDE_TRY_POSITION = 25.0f;
	const float GENERAL_TRY_POSITION = 2.25f;
	if( ball_position_x > WIDE_TRY_POSITION )
		location_context = TLC_WIDE_TRY;
	else if( ball_position_x > GENERAL_TRY_POSITION )
		location_context = TLC_GENERAL_TRY;

	float meters_gained_team = game->GetStrategyHelper()->GetPlayInfo().metres_gained;
	float meters_gained_player = game->GetStrategyHelper()->GetPlayInfo().bh_metres_gained;


	int half = game->GetGameTimer()->GetHalf();
	int minute_of_scoring = game->GetGameTimer()->GetScaledMinutesElapsed();
	int minutes_since_scoring_last_this_period = 0;
	unsigned int record = 1; // start from record 1, record 0 should be the try
	if( !game->GetGameTimer()->IsInExtraTime() )
		for( ;; )
		{
			SSContextBucketEvent* ev = bucket->GetEvent( record );
			if( !ev )
				break;

			if( ev->event_type != SSCB_EVENT_TRY )
			{
				++record;
				continue;
			}

			RUCBTryEvent* try_ev = (RUCBTryEvent*)ev;
			if( try_ev->team_index != team_idx )
			{
				++record;
				continue;
			}

			if( try_ev->half != half )
				break;

			minutes_since_scoring_last_this_period = minute_of_scoring - try_ev->minute_of_scoring;
			break;
		}

	int defensive_pressure = int(game->GetStrategyHelper()->GetDefensivePressure( game->GetGameState()->GetAttackingTeam() ) * 100.0f);
	RUCBTryEvent ev_try(
		game->GetSimTime()->GetAbsoluteStepCount(),
		success,
		penalty,
		player_idx,
		team_idx,
		location_context,
		SSGT_HALF(half),
		minute_of_scoring,
		minutes_since_scoring_last_this_period,
		meters_gained_team,
		meters_gained_player,
		defensive_pressure
		);

	//MABLOGDEBUG("size of try event object %i", sizeof(ev_try));
	bucket->AddEvent( ev_try );
}

void RUContextBucketBasicEvents::StoreGenericFollowUp( int player1_id, int player2_id, SSCB_TYPE event_type, float delay)
{
	RUCommentary* commentary = SIFApplication::GetApplication()->GetCommentarySystem();
	if (commentary && commentary->IsColourDisabled())
	{
		return;
	}

	RUCBGenericFollowUpEvent event_record(
		event_type,
		game->GetSimTime()->GetAbsoluteStepCount() + (int)(game->GetSimTime()->GetTimeStepRate() * delay),
		player1_id,
		player2_id);

	if(delay > 0.0f)
		bucket->AddEventDelayed( event_record);
	else
		bucket->AddEvent( event_record );
}

void RUContextBucketBasicEvents::StoreKickAtPostsReady( ARugbyCharacter* kicker, bool placeKick )
{
	MABUNUSED(placeKick);
	MABASSERT( kicker );
	if(!kicker)
		return;

	RUPlayerAttributes* attrs = kicker->GetAttributes();
	int player_idx = attrs->GetDBPlayer()->GetDbId();
	int team_idx = attrs->GetTeam()->GetDbTeam().GetDbId();

	// for now only if the conversion is human controlled is a conversion record generated. this is because
	// there is no pause for a commentary line on AI kickers, they just start kicking immediately. human players
	// allow time for a commentary line as they do input - though they can spam it :/
	//if( kicker->GetHumanPlayer() == NULL )
	//	return;

	int abs_x_position = (int)MabMath::Abs( game->GetGameState()->GetPlayRestartPosition().x );

	int kicker_team_score = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( kicker->GetAttributes()->GetTeam(), &RUDB_STATS_TEAM::score);
	int other_team_score = SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat( kicker->GetAttributes()->GetOppositionTeam(), &RUDB_STATS_TEAM::score);
	int score_difference = kicker_team_score - other_team_score;

	RUGamePhase phase = game->GetGameState()->GetPhase();
	MABASSERT( phase == RUGamePhase::CONVERSION || phase == RUGamePhase::PENALTY_SHOOT_FOR_GOAL );

	RUCBKickAtPostsReadyEvent ev_conversion( SSCB_EVENT_KICK_AT_POSTS_READY, game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, abs_x_position, score_difference, phase == RUGamePhase::CONVERSION, phase == RUGamePhase::PENALTY_SHOOT_FOR_GOAL );
	//MABLOGDEBUG("size of conversion event object %i", sizeof(ev_conversion));
	bucket->AddEvent( ev_conversion );
}

void RUContextBucketBasicEvents::StoreLineoutSignalled( FVector lineout_position, bool )
{
	if(game->GetGameTimer()->GetHalfObserver()->GetTimeExpired())
	{
		//the game is lying through its damn teeth. There will be no lineout, the ref is just getting a bit
		//confused and pretends to be calling for a lineout, where in fact when he gets off his fat lazy
		//ass he's going to blow his whistle for full time.

		bool lineoutValid = false;

		// This is no longer an NRC specific rule, and is in the standard ruleset.
		//if(game->GetGameSettings().game_law == GAME_LAW_NRC)
		{
			const BallFreeInfo& ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();
			BALL_FREE_EVENT evt = ball_free_info.event;
			RUGamePhase previous_phase = ball_free_info.game_phase_when_released;

			if(evt == BFE_KICK && previous_phase == RUGamePhase::PENALTY_KICK_FOR_TOUCH)
			{
				lineoutValid = true;
			}
		}

		if(!lineoutValid)
			return;
	}

	if ( game->GetGameState()->GetPhase() == RUGamePhase::QUICK_LINEOUT )
	{
		// If this is from a failed quick lineout, we don't want to play the lineout formed commentary because it overrides the quick lineout failed commentary.
		return;
	}

	RUGameState *game_state = game->GetGameState();

	RUTeam *restart_team = game_state->GetPlayRestartTeam();
	unsigned short team_idx = restart_team->GetDbTeam().GetDbId();

	RUCBLineoutSignalledEvent ev_lineout( game->GetSimTime()->GetAbsoluteStepCount(),team_idx, lineout_position );
	//MABLOGDEBUG("size of lineout event object %i", sizeof(ev_lineout));
	bucket->AddEvent( ev_lineout );
}

void RUContextBucketBasicEvents::StoreRuckScoot( ARugbyCharacter* scooter )
{
	MABASSERT( scooter );
	if(!scooter)
		return;

	SSContextBucketEvent* ev = bucket->GetLastEvent( SSCB_EVENT_BALL_IN_TOUCH );
	if(ev)
	{
		// RyT - Hack to stop ruck comments occurring after the ball has been taken into touch
		return;
	}

	int player_idx = scooter->GetAttributes()->GetDBPlayer()->GetDbId();
	int team_idx = scooter->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();

	RUCBRuckScootEvent ev_ruck_scoot( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, scooter->GetMabPosition() );
	bucket->AddEvent( ev_ruck_scoot );
}

void RUContextBucketBasicEvents::StoreRuck( ARugbyCharacter* ball_holder, SIFRugbyCharacterList* /*tacklers*/ )
{
	MABASSERT( ball_holder );
	if(!ball_holder)
		return;

	SSContextBucketEvent* ev = bucket->GetLastEvent( SSCB_EVENT_BALL_IN_TOUCH );
	if(ev)
	{
		// RyT - Hack to stop ruck comments occurring after the ball has been taken into touch
		return;
	}

	int player_idx = ball_holder->GetAttributes()->GetDBPlayer()->GetDbId();
	int team_idx = ball_holder->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();

	// calculate support at the break down by measuring distance of lazy forwards
	const float POOR_SUPPORT_DISTANCE = 15.0f;

	// set up filter search to return ball chasers pretty close to the ball
	RLPResultList players;
	RLP_FILTERPARAMETERS filter_params;

	filter_params.filters = RLP_FILTER_TEAM;
	filter_params.team = ball_holder->GetAttributes()->GetTeam();

	//Roles are not unassigned yet
	//filter_params.filters |= RLP_FILTER_UPDATER_TYPE_ID;
	//filter_params.required_updater_type_id = RURoleRuck::RTTGetStaticType();

	filter_params.filters |= RLP_FILTER_EXCLUDE_PLAYER;
	filter_params.exclude_player = ball_holder;

	filter_params.filters |= RLP_FILTER_MAX_Z_DIST;
	filter_params.max_z_dist = POOR_SUPPORT_DISTANCE;
	filter_params.max_z_ref = ball_holder->GetMabPosition().z;

	game->GetPlayerFilters()->GetPlayerPlayerDistanceSort()->SetReferencePlayer(ball_holder);
	game->GetFilteredPlayerList( players, filter_params, game->GetPlayerFilters()->GetPlayerPlayerDistanceSort() );

	bool isolated = players.empty();

	unsigned int num_phases = game->GetStatsTracker()->GetNumberOfPhases();

	RUCBRuckEvent ev_ruck( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, ball_holder->GetMabPosition(), isolated, num_phases );
	//MABLOGDEBUG("size of ruck event object %i", sizeof(ev_ruck));
	bucket->AddEvent( ev_ruck );
}

void RUContextBucketBasicEvents::StoreRuckPlayerJoin( ARugbyCharacter* player, const FVector&, RUZoneJoinType join_type )
{
	MABASSERT( player );
	if(!player)
		return;

	SSContextBucketEvent* ev = bucket->GetLastEvent( SSCB_EVENT_BALL_IN_TOUCH );
	if(ev)
	{
		// RyT - Hack to stop ruck comments occurring after the ball has been taken into touch
		return;
	}

	int player_idx = player->GetAttributes()->GetDBPlayer()->GetDbId();
	int team_idx = player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();

	//grab the ruck object
	RUGamePhaseRuck *ruck_phase = (RUGamePhaseRuck *)game->GetGameState()->GetPhaseHandler(RUGamePhase::RUCK);
	MABASSERT(ruck_phase);
	if(!ruck_phase)
		return;

	//grab the player counts for each team in the ruck
	int attackers_count = (int) ruck_phase->GetCurrentAttackingTeamState().commital.bound.size();
	int defenders_count = (int) ruck_phase->GetCurrentDefendingTeamState().commital.bound.size();

	SIFRugbyCharacterList scrum_half;
	game->GetStrategyHelper()->GetTeamRoles( player->GetAttributes()->GetTeam(), RURoleRuckScrumHalf::RTTGetStaticType(), scrum_half );
	// wtf does multiple scrum halfs for a team mean
	//MABASSERT( scrum_half.size() == 1 );	//not necessarily true now because of the setplay scrumhalf role
	if( scrum_half.empty() )
		return;
	int scrum_half_id = scrum_half.front()->GetAttributes()->GetDBPlayer()->GetDbId();

	// get the last ruck event, which will be for the current ruck
	RUCBRuckEvent* ruck_event = (RUCBRuckEvent*)bucket->GetLastEvent( SSCB_EVENT_RUCK );
	if( !ruck_event )
	{
		// RyT - This may happen now due to ball in touch event canceling ruck comments
		return;
	}

	bool added_after_ruck_result = false;

	//see if we've had a ruck result event, we can ignore men added after we've gotten the ball out
	if(bucket->GetLastEvent( SSCB_EVENT_RUCK_RESULT, SSCB_EVENT_RUCK ))
	{
		added_after_ruck_result = true;
	}

	int joiners_side_count = attackers_count;
	//determine if the joiner is on the attackers team or not.
	if(player->GetAttributes()->GetTeam() !=  ruck_phase->GetCurrentRuckAttackingTeam())
	{
		joiners_side_count = defenders_count;
	}

	float power_bar_position = ruck_phase->GetRuckPowerContest()->GetPowerbarPosition();
	float power_bar_velocity = ruck_phase->GetRuckPowerContest()->GetPowerbarVelocity();

	int ruck_age = game->GetSimTime()->GetAbsoluteStepCount() - ruck_event->event_time;
	const float time_step_real_time = 1.0f / game->GetSimTime()->GetTimeStepRate();
	ruck_age = int(ruck_age * time_step_real_time);

	//MABLOGDEBUG( "~~~~~~~~~~~~~~~~! Ruck Age: %d, Team: %d, Number: %d ", ruck_age, team_idx, side_ruck_count );

	RUCBRuckPlayerJoinEvent ev_ruck_join(
		game->GetSimTime()->GetAbsoluteStepCount(),
		player_idx,
		scrum_half_id,
		team_idx,
		join_type,
		joiners_side_count,
		attackers_count,
		defenders_count,
		ruck_age,
		added_after_ruck_result );

	ev_ruck_join.power_bar_position = power_bar_position;
	ev_ruck_join.power_bar_velocity = power_bar_velocity;

	bucket->AddEvent( ev_ruck_join );
}

void RUContextBucketBasicEvents::StoreRuckResult( RUTeam* team, const FVector& ruck_center, float /*time*/ )
{
	MABASSERT( team );
	if(!team)
		return;

	SSContextBucketEvent* ev = bucket->GetLastEvent( SSCB_EVENT_BALL_IN_TOUCH );
	if(ev)
	{
		// RyT - Hack to stop ruck comments occurring after the ball has been taken into touch
		return;
	}

	ev = NULL;

	int team_idx = team->GetDbTeam().GetDbId();
	ev = bucket->GetLastEvent( SSCB_EVENT_RUCK );
	if (!ev)
	{
		// RyT - This may happen now due to ball in touch event canceling ruck comments
		return;
	}

	int feeding_team_idx = ((RUCBRuckEvent*)ev)->team_index;

	SIFRugbyCharacterList scrum_half;
	game->GetStrategyHelper()->GetTeamRoles( team, RURoleRuckScrumHalf::RTTGetStaticType(), scrum_half );
	// wtf does multiple scrum halfs for a team mean
	MABASSERT( scrum_half.size() == 1 );

	// failsafe
	if( scrum_half.empty() )
		return;

	int scrum_half_id = scrum_half.front()->GetAttributes()->GetDBPlayer()->GetDbId();

	RuckResultContext ruck_result_context = team_idx == feeding_team_idx ? RRC_RETAINED : RRC_TURNOVER;

	RUCBRuckResultEvent ev_ruck_result( game->GetSimTime()->GetAbsoluteStepCount(), scrum_half_id, team_idx, ruck_result_context, ruck_center );
	bucket->AddEvent( ev_ruck_result );
}

void RUContextBucketBasicEvents::StoreRuckSlow()
{
	SSContextBucketEvent* ev = bucket->GetLastEvent( SSCB_EVENT_BALL_IN_TOUCH );
	if(ev)
	{
		// RyT - Hack to stop ruck comments occurring after the ball has been taken into touch
		return;
	}

	RUTeam* attacking_team = game->GetGameState()->GetAttackingTeam();
	SIFRugbyCharacterList scrum_half;
	game->GetStrategyHelper()->GetTeamRoles( attacking_team, RURoleRuckScrumHalf::RTTGetStaticType(), scrum_half );
	// wtf does multiple scrum halfs for a team mean
	MABASSERT( scrum_half.size() == 1 );
	if( scrum_half.empty() )
		return;
	int scrum_half_id = scrum_half.front()->GetAttributes()->GetDBPlayer()->GetDbId();
	int attacking_team_id = attacking_team->GetDbTeam().GetDbId();

	RUCBRuckSlowEvent ev_ruck_slow( game->GetSimTime()->GetAbsoluteStepCount(), scrum_half_id, attacking_team_id );
	bucket->AddEvent( ev_ruck_slow );
}

void RUContextBucketBasicEvents::StoreMark( ARugbyCharacter* holder, bool /*success*/ )
{
	MABASSERT( holder );
	if(!holder)
		return;

	int player_idx = holder->GetAttributes()->GetDBPlayer()->GetDbId();
	int team_idx = holder->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();

	RUCBMarkEvent stored_event(
		game->GetSimTime()->GetAbsoluteStepCount(),
		player_idx,
		team_idx,
		holder->GetMabPosition() );

	bucket->AddEvent( stored_event );
}

void RUContextBucketBasicEvents::StoreCatch( ARugbyCharacter* holder, bool player_was_in_air, int bounces )
{
	MABASSERT( holder );
	if(!holder)
		return;

	//Find the event that got the ball into the air in the first place, and categorise
	//the catch as such, CAUGHT_FROM_KICKOFF, CAUGHT_FROM_PASS etc.
	//Additionally store the db id of the player that got the ball into the air.

	int player_idx = holder->GetAttributes()->GetDBPlayer()->GetDbId();
	int player2_idx = -1;
	bool was_marked_catch = false;
	KickContext kick_context;
	KickType kick_type;
	RUCBCatchType catch_type = CalculateCatchType(holder->GetAttributes()->GetTeam()->GetDbTeam().GetDbId(), player2_idx,kick_context, kick_type, was_marked_catch, bounces);

	bool on_full = game->GetBall()->IsOnTheFull();

	//determine if this catch was from a penalty goal.
	RUCBCatchEvent ev_catch(
		game->GetSimTime()->GetAbsoluteStepCount(),
		catch_type,
		player_was_in_air,
		was_marked_catch,
		on_full,
		player_idx,
		player2_idx,
		holder->GetMabPosition() );

	bucket->AddEvent( ev_catch );
}



void RUContextBucketBasicEvents::StoreCollected( ARugbyCharacter* holder, bool ball_in_motion )
{
	MABASSERT( holder );
	if(!holder)
		return;

	int player_idx = holder->GetAttributes()->GetDBPlayer()->GetDbId();

	SSContextBucketEvent* ev = bucket->GetNewestEvent( SSCB_CLASS_GAMEPLAY );
	SSCB_TYPE preceding_event = ev ? (SSCB_TYPE)ev->event_type : SSCB_EVENT_TYPE_NONE;

	ARugbyCharacter* prev_holder = game->GetStrategyHelper()->GetLastBallFreeInfo().last_player;
	int prev_player_idx = prev_holder ? prev_holder->GetAttributes()->GetDBPlayer()->GetDbId() : -1;

	const BallFreeInfo& ball_free_info = game->GetStrategyHelper()->GetLastBallFreeInfo();
	bool fumble_recover = ball_free_info.event == BFE_FUMBLE && ball_free_info.last_player == holder;

	bool dive_collect = false;
	if ( holder->GetActionManager()->IsActionRunning( ACTION_GETTHEBALL ) )
	{
		RUActionGetTheBall* gtb_action = holder->GetActionManager()->GetAction< RUActionGetTheBall >();
		dive_collect = gtb_action->GetCollectType() == GTB_ACTION::DIVE || gtb_action->GetCollectType() == GTB_ACTION::CRADLE_DIVE;
	}

	RUCBCollectedEvent ev_collect( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, preceding_event, prev_player_idx, holder->GetMabPosition(), fumble_recover, ball_in_motion, dive_collect );
	bucket->AddEvent( ev_collect );
}

void RUContextBucketBasicEvents::StoreRuckScrumBallPickedUp( ARugbyCharacter* holder, PickedUpContext picked_up_context)
{
	MABASSERT( holder );
	if(!holder)
		return;

	SSContextBucketEvent* ev = bucket->GetLastEvent( SSCB_EVENT_BALL_IN_TOUCH );
	if(ev)
	{
		// RyT - Hack to stop ruck comments occurring after the ball has been taken into touch
		return;
	}

	int player_idx = holder->GetAttributes()->GetDBPlayer()->GetDbId();

	RUCBPickedUpEvent ev_picked_up( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, picked_up_context, holder->GetMabPosition() );
	bucket->AddEvent( ev_picked_up );
}

void RUContextBucketBasicEvents::StoreKickOffside( ARugbyCharacter* player )
{
	MABASSERT( player );
	if(!player)
		return;

	int player_idx = player->GetAttributes()->GetDBPlayer()->GetDbId();
// 	int team_idx = player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();
// 	RUCBOffsideEvent offside_event( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, OC_KICK );
// 	bucket->AddEvent( offside_event );

	// RyT - This needs to comment on offsides while the umpire calls advantage,
	// but nisbo has no offside comments,
	// so he will comment on advantage and marshal will comment on offside
	StoreGenericFollowUp(player_idx, -1, SSCB_NISBO_OFFSIDE_ADVANTAGE);
// 	StoreGenericFollowUp(player_idx, -1, SSCB_MARSHALL_PENALTY, 0.5f);		// RyT - Moved to ProcessPhraseCompletion
// 	StoreGenericFollowUp(player_idx, -1, SSCB_MARSHALL_PENALTY_FOLLOWUP, 0.25f);
}

void RUContextBucketBasicEvents::StoreOffside( ARugbyCharacter* player )
{
	MABASSERT( player );
	if(!player)
		return;

	int player_idx = player->GetAttributes()->GetDBPlayer()->GetDbId();
// 	int team_idx = player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();
// 	RUCBOffsideEvent offside_event( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, OC_GENERAL );
// 	bucket->AddEvent( offside_event );

	// RyT - This needs to comment on offsides while the umpire calls advantage,
	// but nisbo has no offside comments,
	// so he will comment on advantage and marshal will comment on offside
	StoreGenericFollowUp(player_idx, -1, SSCB_NISBO_OFFSIDE_ADVANTAGE);
// 	StoreGenericFollowUp(player_idx, -1, SSCB_MARSHALL_PENALTY, 0.5f);		// RyT - Moved to ProcessPhraseCompletion
// 	StoreGenericFollowUp(player_idx, -1, SSCB_MARSHALL_PENALTY_FOLLOWUP, 0.25f);
}

void RUContextBucketBasicEvents::StoreStoppage( )
{
	ARugbyCharacter* player  = game->GetGameState()->GetBallHolder();
	if(!player)
		player = game->GetGameState()->GetLastBallHolder();
	MABASSERT(player);
	if(!player)
		return;

	int player_idx = player ? player->GetAttributes()->GetDBPlayer()->GetDbId() : -1;
	int team_idx = player ? player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId() : -1 ;

	RUCBStoppageEvent stoppage_event( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx);
	bucket->AddEvent( stoppage_event );
}

void RUContextBucketBasicEvents::StoreScrumCalled( RUTeam *awarded_team )
{
	MABASSERT( awarded_team );
	if(!awarded_team)
		return;

	if(game->GetGameTimer()->GetHalfObserver()->GetTimeExpired())
	{
		//the game is lying through its damn teeth. There will be no scrum, the ref is just getting a bit
		//confused and pretends to be calling for a scrum, where in fact when he gets off his fat lazy
		//ass he's going to blow his whistle for full time.
		return;
	}

	int team_idx = awarded_team->GetDbTeam().GetDbId();
	FVector position = game->GetGameState()->GetPlayRestartPosition();

	RUCBScrumCalledEvent scrum_event( game->GetSimTime()->GetAbsoluteStepCount(), team_idx, position );
	bucket->AddEvent( scrum_event );
}

void RUContextBucketBasicEvents::StoreScrumStart( RUTeam* feeding_team, ARugbyCharacter* feeding_player )
{
	MABASSERT( feeding_team );
	if(!feeding_team)
		return;
	MABASSERT( feeding_player );
	if(!feeding_player)
		return;

	int player_idx = feeding_player->GetAttributes()->GetDBPlayer()->GetDbId();
	int team_idx = feeding_team->GetDbTeam().GetDbId();
	FVector position = feeding_player->GetMabPosition();

	RUCBScrumEvent scrum_event( SSCB_EVENT_SCRUM, game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, position );
	bucket->AddEvent( scrum_event );
}

void RUContextBucketBasicEvents::StoreScrumBallIn( RUTeam* feeding_team, ARugbyCharacter* feeding_player )
{
	MABASSERT( feeding_team );
	if(!feeding_team)
		return;
	MABASSERT( feeding_player );
	if(!feeding_player)
		return;

	int player_idx = feeding_player->GetAttributes()->GetDBPlayer()->GetDbId();
	int team_idx = feeding_team->GetDbTeam().GetDbId();
	FVector position = feeding_player->GetMabPosition();

	RUCBScrumEvent scrum_event( SSCB_EVENT_SCRUM_BALL_IN, game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, position );
	bucket->AddEvent( scrum_event );
}

void RUContextBucketBasicEvents::StoreScrumPushing( RUTeam* feeding_team, float relative_power )
{
	MABASSERT( feeding_team );
	if(!feeding_team)
		return;

	int team_idx = feeding_team->GetDbTeam().GetDbId();
	FVector position = game->GetBall()->GetCurrentPosition();

	//grab the push scores from both teams.
	RUGamePhaseScrum *scrum_phase = (RUGamePhaseScrum *)game->GetGameState()->GetPhaseHandler(RUGamePhase::SCRUM);
	MABASSERT(scrum_phase);
	if(!scrum_phase)
		return;

	//grab the push count.
	//Glen: TODO: it may be better to not to reach around the event like this, as it limits where we can fire the event from
	//all dependencies should be passed through the event, especially if we want the capacity to fire these events off when
	//we please
	int shunt_count = scrum_phase->GetCurrentScrum().shunt_count;
	float attacking_team_power = scrum_phase->GetCurrentScrum().attacking.push_score;
	float defending_team_power = scrum_phase->GetCurrentScrum().defending.push_score;


	MABLOGDEBUG("RUContextBucketBasicEvents::StoreScrumPushing() count: %d, attack: %f, defence:%f", shunt_count, attacking_team_power, defending_team_power);

	RUCBScrumPushingEvent scrum_event(
		game->GetSimTime()->GetAbsoluteStepCount(),
		team_idx,
		relative_power,
		position,
		shunt_count,
		attacking_team_power,
		defending_team_power);

	bucket->AddEvent( scrum_event );
}

void RUContextBucketBasicEvents::StoreScrumBallOut( RUTeam* winning_team )
{
	MABASSERT( winning_team );
	if(!winning_team)
		return;

	int team_idx = winning_team->GetDbTeam().GetDbId();

	RUGamePhaseScrum* scrum_phase = (RUGamePhaseScrum*)( game->GetGameState()->GetPhaseHandler(RUGamePhase::SCRUM) );
	RUTeam *scrum_feeding_team = scrum_phase->GetCurrentScrum().attacking.team;

	ScrumResultContext scrum_result_context = winning_team == scrum_feeding_team ? SRC_RETAINED : SRC_TURNOVER;

	float ground_made_for_feeding_team = scrum_phase->GetCurrentScrum().scrum_ground_gained;

	RUCBScrumResultEvent scrum_result_event( game->GetSimTime()->GetAbsoluteStepCount(), team_idx, scrum_result_context, ground_made_for_feeding_team );
	bucket->AddEvent( scrum_result_event );
}

void RUContextBucketBasicEvents::StoreScrumReset( RUTeam* attacking_team, ScrumResetContext reset_context )
{
	MABASSERT( attacking_team );
	if(!attacking_team)
		return;

	int team_idx = attacking_team->GetDbTeam().GetDbId();

	RUCBScrumResultEvent scrum_result_event( game->GetSimTime()->GetAbsoluteStepCount(), team_idx, SRC_RESTART, 0.0f, reset_context );
	bucket->AddEvent( scrum_result_event );
}

void RUContextBucketBasicEvents::StoreLineBreak( ARugbyCharacter* player, LINE_BREAK_TYPE break_type )
{
	MABASSERT( player );
	if(!player)
		return;

	int player_idx = player->GetAttributes()->GetDBPlayer()->GetDbId();
	int team_idx = player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();

	FVector location = player->GetMabPosition();

	RUCBLineBreakEvent line_break_event( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, (int) break_type, location);
	bucket->AddEvent( line_break_event );
}

void RUContextBucketBasicEvents::StoreNumberEightPickup( ARugbyCharacter* player )
{
	StoreGenericFollowUp(player->GetAttributes()->GetIndex(), -1, SSCB_EVENT_NUMBER_EIGHT_PICKUP);
}

void RUContextBucketBasicEvents::StoreKickOffWhistle()
{
	if( game->GetGameState()->GetPhase() != RUGamePhase::KICK_OFF )
		return;

	ARugbyCharacter* player = game->GetGameState()->GetBallHolder();
	if( !player )
		return;

	TimeKeepingContext tkc = TKC_NONE;
	SSGameTimer* timer = game->GetGameTimer();
	if( !timer )
		return;

	//grab the previous kickoff ready event, and grab the TimeKeepingContext from that.
	SSContextBucketEvent *prior_event = bucket->GetLastEvent(SSCB_EVENT_KICKOFF_READY, SSCB_EVENT_KICKOFF_WHISTLE);

	if(prior_event)
	{
		RUCBKickOffReady *kickoff_ready_event = (RUCBKickOffReady *)prior_event;

		tkc = (TimeKeepingContext)kickoff_ready_event->time_keeping_context;
	}

	int player_idx = player->GetAttributes()->GetDBPlayer()->GetDbId();
	int team_idx = player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();

	RUCBKickOffWhistle kick_off_whistle( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, tkc );
	bucket->AddEvent( kick_off_whistle );
}

template <SSCB_TYPE event_type>
void RUContextBucketBasicEvents::StoreTimeWasting()
{
	StoreGenericFollowUp(-1,-1,event_type);
}

void RUContextBucketBasicEvents::StoreSideStep( ARugbyCharacter* sidestepping_player, ARugbyCharacter* closest_opponent)
{
	int player_index = sidestepping_player->GetAttributes()->GetDBPlayer()->GetDbId();
	int player2_index = closest_opponent ? closest_opponent->GetAttributes()->GetDBPlayer()->GetDbId() : -1;

	RUCBSideStepEvent side_step(game->GetSimTime()->GetAbsoluteStepCount(), player_index, player2_index);
	bucket->AddEvent(side_step);
}

void RUContextBucketBasicEvents::StoreMaulFormedEvent( ARugbyCharacter* attacking_player, ARugbyCharacter* defending_player)
{
	MABASSERT(attacking_player);
	if(!attacking_player)
		return;

	MABASSERT(defending_player);
	if(!defending_player)
		return;

	RUCBMaulEvent event( SSCB_EVENT_MAUL_FORMED,
		game->GetSimTime()->GetAbsoluteStepCount(),
		attacking_player->GetAttributes()->GetDBPlayer()->GetDbId(),
		attacking_player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId(),
		defending_player->GetAttributes()->GetDBPlayer()->GetDbId(),
		defending_player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId(),
		attacking_player->GetMabPosition()
		);

	bucket->AddEvent( event );
}

void RUContextBucketBasicEvents::StoreMaulBallReleased(RUTeam *team, const FVector& handoff_position, float handoff_time)
{
	MABUNUSED(team);
	MABUNUSED(handoff_time);

	//find the maul formed event, to find the players
	RUCBMaulEvent* maul_formed_event = MabCast< RUCBMaulEvent >( bucket->GetLastEvent( SSCB_EVENT_MAUL_FORMED ));

	MABASSERT(maul_formed_event);
	if(!maul_formed_event)
		return;

	RUCBMaulEvent event( SSCB_EVENT_MAUL_BALL_RELEASED,
		game->GetSimTime()->GetAbsoluteStepCount(),
		maul_formed_event->player_index,
		maul_formed_event->team_index,
		maul_formed_event->player2_index,
		maul_formed_event->team2_index,
		handoff_position
		);

	bucket->AddEvent( event );
}

void RUContextBucketBasicEvents::StoreMaulCollapsed()
{
	//find the maul formed event, to find the players
	RUCBMaulEvent* maul_formed_event = MabCast< RUCBMaulEvent >( bucket->GetLastEvent( SSCB_EVENT_MAUL_FORMED ));

	MABASSERT(maul_formed_event);
	if(!maul_formed_event)
		return;

	RUCBMaulEvent event( SSCB_EVENT_MAUL_COLLAPSED,
		game->GetSimTime()->GetAbsoluteStepCount(),
		maul_formed_event->player_index,
		maul_formed_event->team_index,
		maul_formed_event->player2_index,
		maul_formed_event->team2_index,
		game->GetBall()->GetCurrentPosition()
		);

	bucket->AddEvent( event );
}

void RUContextBucketBasicEvents::StoreMaulHeldUp(ARugbyCharacter*  /*ball_holder*/, const FVector &position, bool /*held_up*/)
{
	//find the maul formed event, to find the players
	RUCBMaulEvent* maul_formed_event = MabCast< RUCBMaulEvent >( bucket->GetLastEvent( SSCB_EVENT_MAUL_FORMED ));

	MABASSERT(maul_formed_event);
	if(!maul_formed_event)
		return;

	//extract the progress and velocity from the current maul
	RUGamePhaseMaul *maul_phase = static_cast< RUGamePhaseMaul *>(game->GetGameState()->GetPhaseHandler( RUGamePhase::MAUL) );
	MABASSERT(maul_phase);
	if(!maul_phase)
		return;

	const RUMaulState &maul_state = maul_phase->GetCurrentMaulState();
	float progress = maul_state.maul_ground_gained;
	float velocity = maul_state.maul_velocity;

	RUCBMaulEvent event( SSCB_EVENT_MAUL_HELD_UP,
		game->GetSimTime()->GetAbsoluteStepCount(),
		maul_formed_event->player_index,
		maul_formed_event->team_index,
		maul_formed_event->player2_index,
		maul_formed_event->team2_index,
		position
		);

	event.velocity = velocity;
	event.ground_made = progress;
	bucket->AddEvent( event );
}

void RUContextBucketBasicEvents::StoreMaulProgress()
{
	//find the maul formed event, to find the players
	RUCBMaulEvent* maul_formed_event = MabCast< RUCBMaulEvent >( bucket->GetLastEvent( SSCB_EVENT_MAUL_FORMED ));

	MABASSERT(maul_formed_event);
	if(!maul_formed_event)
		return;

	//extract the progress and velocity from the current maul
	RUGamePhaseMaul *maul_phase = static_cast< RUGamePhaseMaul *>(game->GetGameState()->GetPhaseHandler( RUGamePhase::MAUL) );
	MABASSERT(maul_phase);
	if(!maul_phase)
		return;

	const RUMaulState &maul_state = maul_phase->GetCurrentMaulState();
	float progress = maul_state.maul_ground_gained;
	float velocity = maul_state.maul_velocity;

	RUCBMaulEvent event( SSCB_EVENT_MAUL_PROGRESS,
		game->GetSimTime()->GetAbsoluteStepCount(),
		maul_formed_event->player_index,
		maul_formed_event->team_index,
		maul_formed_event->player2_index,
		maul_formed_event->team2_index,
		game->GetBall()->GetCurrentPosition()
		);

	event.velocity = velocity;
	event.ground_made = progress;
	bucket->AddEvent( event );
}

void RUContextBucketBasicEvents::StoreMaulLooksHalted()
{
	//find the maul formed event, to find the players
	RUCBMaulEvent* maul_formed_event = MabCast< RUCBMaulEvent >( bucket->GetLastEvent( SSCB_EVENT_MAUL_FORMED ));

	MABASSERT(maul_formed_event);
	if(!maul_formed_event)
		return;

	RUCBMaulEvent event( SSCB_EVENT_MAUL_LOOKS_HALTED,
		game->GetSimTime()->GetAbsoluteStepCount(),
		maul_formed_event->player_index,
		maul_formed_event->team_index,
		maul_formed_event->player2_index,
		maul_formed_event->team2_index,
		game->GetBall()->GetCurrentPosition()
		);

	bucket->AddEvent( event );
}

void RUContextBucketBasicEvents::StoreTimeTickEvent()
{
	SSContextBucketEvent time_tick_event(
		SSCB_CLASS_GAMEPLAY,
		SSCB_EVENT_TIME_TICK,
		game->GetSimTime()->GetAbsoluteStepCount());

	bucket->AddEvent( time_tick_event );
}

void RUContextBucketBasicEvents::StoreAdvantageStarted(ARugbyCharacter* offending_player)
{
	int player_idx = offending_player->GetAttributes()->GetDBPlayer()->GetDbId();
	int team_idx = offending_player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();

	RUCBAdvantageEvent context_event(game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, AET_ADVANTAGE_STARTED );
	bucket->AddEvent( context_event );
}

void RUContextBucketBasicEvents::StoreAdvantageEnded(ARugbyCharacter* offending_player)
{
	int player_idx = offending_player->GetAttributes()->GetDBPlayer()->GetDbId();
	int team_idx = offending_player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();

	RUCBAdvantageEvent context_event(game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, AET_ADVANTAGE_STOPPED );
	bucket->AddEvent( context_event );
}

void RUContextBucketBasicEvents::StoreAdvantageScrumAwarded(ARugbyCharacter* offending_player)
{
	if(game->GetGameTimer()->GetHalfObserver()->GetTimeExpired())
	{
		//the game is lying through its damn teeth. There will be no scrum, the ref is just getting a bit
		//confused and pretends to be calling for a scrum, where in fact when he gets off his fat lazy
		//ass he's going to blow his whistle for full time.
		return;
	}

	int player_idx = offending_player->GetAttributes()->GetDBPlayer()->GetDbId();
	int team_idx = offending_player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();

	RUCBAdvantageEvent context_event(game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, AET_ADVANTAGE_TO_SCRUM );
	bucket->AddEvent( context_event );
}

void RUContextBucketBasicEvents::StoreAdvantageScrumAwarded(RUTeam* offending_team)
{
	if(game->GetGameTimer()->GetHalfObserver()->GetTimeExpired())
	{
		//the game is lying through its damn teeth. There will be no scrum, the ref is just getting a bit
		//confused and pretends to be calling for a scrum, where in fact when he gets off his fat lazy
		//ass he's going to blow his whistle for full time.
		return;
	}

	int team_idx = offending_team->GetDbTeam().GetDbId();

	RUCBAdvantageEvent context_event(game->GetSimTime()->GetAbsoluteStepCount(), -1, team_idx, AET_ADVANTAGE_TO_SCRUM );
	bucket->AddEvent( context_event );
}

void RUContextBucketBasicEvents::StoreAdvantagePenaltyAwarded(ARugbyCharacter* offending_plyr, ARugbyCharacter* offended_player, const FVector& position, PENALTY_REASON _reason)
{
	MABUNUSED(offended_player);
	MABUNUSED(position);
	MABUNUSED(_reason);

	if(game->GetGameTimer()->GetHalfObserver()->GetTimeExpired())
	{
		//the game is lying through its damn teeth. There will be no scrum, the ref is just getting a bit
		//confused and pretends to be calling for a scrum, where in fact when he gets off his fat lazy
		//ass he's going to blow his whistle for full time.
		return;
	}

	int player_idx = offending_plyr->GetAttributes()->GetDBPlayer()->GetDbId();
	int team_idx = offending_plyr->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();

	RUCBAdvantageEvent context_event(game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, AET_ADVANTAGE_TO_PENALTY);
	bucket->AddEvent( context_event );
}

void RUContextBucketBasicEvents::StoreKickOffReady( ARugbyCharacter* player )
{
	MABASSERT( player );
	if(!player)
		return;

	if( game->GetGameState()->GetPhase() != RUGamePhase::KICK_OFF )
		return;

	int player_idx = player->GetAttributes()->GetDBPlayer()->GetDbId();
	int team_idx = player->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();

	TimeKeepingContext tkc = TKC_NONE;
	SSGameTimer* timer = game->GetGameTimer();
	if( !timer )
		return;

	// for now only if the kick off is human controlled is a kick off ready record generated. this is because
	// there is no pause for a commentary line on AI kickers, they just start kicking immediately
	//if( player->GetHumanPlayer() == NULL )
	//	return;

	//determine if we are starting the second half, or if the second half is pending.
	SSGT_HALF half = timer->GetHalf();
	if(half == FIRST_HALF && timer->GetPendingHalfChange())
	{
		half = SECOND_HALF;
	}

	if( !timer->IsInExtraTime() && half == FIRST_HALF && timer->GetTimeElapsed() == 0.0f )
	{
		tkc = TKC_FIRST_HALF_START;
	}
	else if( !timer->IsInExtraTime() && half == SECOND_HALF && timer->GetTimeElapsed() == 0.0f )
	{
		tkc = TKC_SECOND_HALF_START;
	}
	else if( timer->IsInExtraTime() && timer->GetExtraTimeMode() == FIRST_EXTRA_TIME && timer->GetTimeElapsed() == 0.0f )
	{
		tkc = TKC_FIRST_EXTRA_TIME_START;
	}
	else if( timer->IsInExtraTime() && timer->GetExtraTimeMode() == SECOND_EXTRA_TIME && timer->GetTimeElapsed() == 0.0f )
	{
		tkc = TKC_SECOND_EXTRA_TIME_START;
	}

	RUCBKickOffReady kick_off_ready_event( game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx, tkc );
	bucket->AddEvent( kick_off_ready_event );
}

template <SSCB_TYPE EventType>
void RUContextBucketBasicEvents::StoreCutsceneEvent(REPLAY_TYPE replay_type, float delay /*= 0.0f*/)
{
	int sim_t = game->GetSimTime()->GetAbsoluteStepCount();
	if (delay > 0.0f)
	{
		sim_t = game->GetSimTime()->GetAbsoluteStepCount() + (int)(game->GetSimTime()->GetTimeStepRate() * delay);
	}

	SSCBCutsceneEvent new_event(sim_t , EventType);
	new_event.replay_type = replay_type;

	if (delay > 0.0f)
	{
		bucket->AddEventDelayed( new_event );
	}
	else
	{
		bucket->AddEvent( new_event );
	}
}

void RUContextBucketBasicEvents::StoreReplayStarted(REPLAY_TYPE replay_type, int played_count)
{
	//don't do any commentary for play repeats
	if(played_count > 0)
		return;

	if(replay_type == RT_TRY)
	{
		StoreCutsceneEvent<SSCB_EVENT_REPLAY_TRY>(replay_type, 5.0f);
	}
	else
	{
		StoreCutsceneEvent<SSCB_EVENT_REPLAY>(replay_type);
	}
}

void RUContextBucketBasicEvents::StoreTrophyCeremony()
{
	StoreGenericFollowUp(-1, -1, SSCB_PMP_TROPHY_CEREMONY);
	StoreGenericFollowUp(-1, -1, SSCB_PMP_TROPHY_AWARDED, 2.0f);
}

void RUContextBucketBasicEvents::StoreYellowCard( ARugbyCharacter* offending_player)
{
	StoreGenericFollowUp(offending_player->GetAttributes()->GetDbId(), -1, SSCB_EVENT_YELLOW_CARD, YELLOW_CARD_DELAY_TIME);
}

void RUContextBucketBasicEvents::StoreRedCard( ARugbyCharacter* offending_player)
{
	StoreGenericFollowUp(offending_player->GetAttributes()->GetDbId(), -1, SSCB_EVENT_RED_CARD, RED_CARD_DELAY_TIME);
}

void RUContextBucketBasicEvents::StoreInterchangeStart()
{
	//determine if this is an actual interchange as opposed to an injury or a send off
	RUSubstitutionManager *sub_manager = game->GetSubstitutionManager();
	int num_pending = sub_manager->GetNumQueuedEvents();

	if(num_pending <= 0)
		return;

	for(int i = 0; i < num_pending; i++)
	{
		const RUInterchangeEvent *entry = sub_manager->GetQueuedEvent(i);

		if(i == 0 && entry->GetType() == RU_INTERCHANGE_INJURY)
		{
			//store an injury started event.
			StoreGenericFollowUp(entry->GetPrimary(), entry->GetSecondary(), SSCB_EVENT_INJURY_STARTED);
		}

		if(entry->GetType() != RU_INTERCHANGE_EXCHANGE)
		{
			//we can't handle complex situations at this stage
			//we can really only introduce regular interchanges.
			return;
		}
	}

	//now grab the first, and make that team the subject of the exchange.
	const RUInterchangeEvent *entry = sub_manager->GetQueuedEvent(0);
	StoreGenericFollowUp(entry->GetPrimary(), entry->GetSecondary(), SSCB_EVENT_INTERCHANGE_STARTED);

}

void RUContextBucketBasicEvents::StoreInterchangeMade(RU_INTERCHANGE_EVENT_TYPE interchange_type, ARugbyCharacter* leaving_player, ARugbyCharacter* arriving_player)
{
	//store an interchange event
	RUCBInterchangeEvent new_event(
		game->GetSimTime()->GetAbsoluteStepCount(),
		leaving_player ? leaving_player->GetAttributes()->GetDbId() : -1,
		arriving_player ? arriving_player->GetAttributes()->GetDbId() : -1,
		interchange_type
	);

	bucket->AddEvent( new_event );
}

void RUContextBucketBasicEvents::StoreDistraction(ARugbyCharacter* distracted_player, DISTRACTION_LEVEL distraction_level)
{
	//grab the previous pass event and see whether it was a dummy pass
	RUCBPassEvent *pass_event = MabCast<RUCBPassEvent>( bucket->GetLastEvent( SSCB_EVENT_PASS ));
	MABASSERT(pass_event);

	int passing_player_id = -1;
	if(pass_event && pass_event->action_type == CPT_DUMMY_PASS)
	{
		passing_player_id = pass_event->player_index;
	}

	//store an interchange event
	RUCBDistractedEvent new_event(
		game->GetSimTime()->GetAbsoluteStepCount(),
		passing_player_id,
		distracted_player ? distracted_player->GetAttributes()->GetDbId() : -1,
		distraction_level
		);

	bucket->AddEvent( new_event );
}

void RUContextBucketBasicEvents::StoreTMODeliberation()
{
	//grab the player that was attempting the try.
	/*
	RUCBTryAttemptEvent *try_attempt = MabCast<RUCBTryAttemptEvent>( bucket->GetLastEvent(SSCB_EVENT_TRY_ATTEMPT));
	MABASSERT(try_attempt);
	if(!try_attempt)
		return;
	*/
	ARugbyCharacter* ball_holder = game->GetGameState()->GetBallHolder();
	if(!ball_holder)
		ball_holder = game->GetGameState()->GetLastBallHolder();

	if(!ball_holder)
		return;

	RUCBTelevisionMatchOfficialEvent new_event(
		game->GetSimTime()->GetAbsoluteStepCount(),
		SSCB_EVENT_TMO_DELIBERATION,
		ball_holder->GetAttributes()->GetDbId(),
		false,
		false);

	bucket->AddEvent( new_event );
}

void RUContextBucketBasicEvents::StoreTMODecision(bool try_awarded)
{
	//grab the deliberation event
	RUCBTelevisionMatchOfficialEvent *deliberation_event = MabCast<RUCBTelevisionMatchOfficialEvent>( bucket->GetLastEvent(SSCB_EVENT_TMO_DELIBERATION));
	MABASSERT(deliberation_event);
	if(!deliberation_event)
		return;

	//grab the previous tackle event, and see if it indicates that the player has
	//been held up, which may effect how we interpret the try not awarded result
	bool possibly_held_up = false;

	RUCBTackleEvent *tackle_event = MabCast<RUCBTackleEvent>( bucket->GetLastEvent(SSCB_EVENT_TACKLEE_ON_GROUND));
	if(tackle_event
		&& tackle_event->player_index == deliberation_event->player_index
		&& MabMath::Fabs(tackle_event->position_z) > 50.0f
		/*&& tackle_event->try_tackle_type == TTT_HELDUP*/)
	{
		possibly_held_up = true;
	}

	RUCBTelevisionMatchOfficialEvent new_event(
		game->GetSimTime()->GetAbsoluteStepCount(),
		SSCB_EVENT_TMO_DECISION,
		deliberation_event->player_index,
		try_awarded,
		possibly_held_up);

	bucket->AddEvent( new_event );
}

void RUContextBucketBasicEvents::StoreBallOverPostsWrongType( const FVector & /*crossed_goal_location*/)
{
	RUCBKickEvent *kick_event = static_cast<RUCBKickEvent *>(bucket->GetLastEvent(SSCB_EVENT_KICK));
	if(!kick_event)
		return;

	RUCBBasicPlayerEvent over_post_event(
		game->GetSimTime()->GetAbsoluteStepCount(),
		SSCB_EVENT_CROSSED_POST_WRONG_TYPE,
		kick_event->player_index);

	bucket->AddEvent(over_post_event);
}

void RUContextBucketBasicEvents::StoreSetplayStart(int setplayindex, FString setplayname, ARugbyCharacter* player)
{
	int player_idx = 0;//attrs->GetDBPlayer()->GetDbId(); //#2fix
	int team_idx = game->GetGameState()->GetAttackingTeam()->GetDbTeam().GetDbId();

	//store event
	RUCBSetPlayEvent new_event(game->GetSimTime()->GetAbsoluteStepCount(), SSCB_SETPLAY_EVENT_PRE, player_idx, team_idx); 
	bucket->AddEvent(new_event);
}

void RUContextBucketBasicEvents::StoreHandover()
{
	int player_idx = 0;
	int team_idx = 0;
	if (game->GetGameState()->GetBallHolder())
	{
		player_idx = game->GetGameState()->GetBallHolder()->GetAttributes()->GetDBPlayer()->GetDbId();
		team_idx = game->GetGameState()->GetBallHolder()->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();
	}
	else if(game->GetGameState()->GetLastBallHolder())
	{
		player_idx = game->GetGameState()->GetLastBallHolder()->GetAttributes()->GetDBPlayer()->GetDbId();
		team_idx = game->GetGameState()->GetLastBallHolder()->GetAttributes()->GetTeam()->GetDbTeam().GetDbId();
	}

	RUCBHandoverEvent context_event(game->GetSimTime()->GetAbsoluteStepCount(), player_idx, team_idx);
	bucket->AddEvent(context_event);

	SSRoleReferee::ref_tackle_count(6);
}

//------------------------ RUCBPenaltyEvent ------------------------------
RUCBPenaltyEvent::RUCBPenaltyEvent(
	int simt,
	int player_idx,
	int team_idx,
	PENALTY_REASON penalty_reason,
	const FVector& position )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_PENALTY, simt),
	player_index(player_idx),
	team_index(team_idx),
	penalty_reason(penalty_reason)
{
	position_x = position.x;
	position_z = position.z;
}


void RUCBPenaltyEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBPenaltyEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyEvent, penalty_reason);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyEvent, position_z);
}

//------------------------ RUCBPenaltyGoalKickDecidedEvent ------------------------------
RUCBPenaltyGoalKickDecidedEvent::RUCBPenaltyGoalKickDecidedEvent( int simt,
								   int player_idx,
								   int team_idx,
								   const FVector& position ):
SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_PENALTY_GOAL_KICK_DECIDED, simt),
player_index(player_idx),
team_index(team_idx)

{
	position_x = position.x;
	position_z = position.z;
}

void RUCBPenaltyGoalKickDecidedEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBPenaltyGoalKickDecidedEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyGoalKickDecidedEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyGoalKickDecidedEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyGoalKickDecidedEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyGoalKickDecidedEvent, position_z);
}

//------------------------ RUCBPenaltyTouchKickDecidedEvent ------------------------------
RUCBPenaltyTouchKickDecidedEvent::RUCBPenaltyTouchKickDecidedEvent( int simt,
								   int player_idx,
								   int team_idx,
								   const FVector& position ):
SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_PENALTY_TOUCH_KICK_DECIDED, simt),
player_index(player_idx),
team_index(team_idx)

{
	position_x = position.x;
	position_z = position.z;
}

void RUCBPenaltyTouchKickDecidedEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBPenaltyTouchKickDecidedEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyTouchKickDecidedEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyTouchKickDecidedEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyTouchKickDecidedEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyTouchKickDecidedEvent, position_z);
}

//------------------------ RUCBPenaltyTapDecidedEvent ------------------------------
RUCBPenaltyTapDecidedEvent::RUCBPenaltyTapDecidedEvent( int simt,
								   int player_idx,
								   int team_idx,
								   const FVector& position ):
SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_PENALTY_TAP_DECIDED, simt),
player_index(player_idx),
team_index(team_idx)

{
	position_x = position.x;
	position_z = position.z;
}

void RUCBPenaltyTapDecidedEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBPenaltyTapDecidedEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyTapDecidedEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyTapDecidedEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyTapDecidedEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPenaltyTapDecidedEvent, position_z);
}

//------------------------ RUCBBallDeadDecisionEvent ------------------------------
RUCBBallDeadDecisionEvent::RUCBBallDeadDecisionEvent(
	int simt,
	SSCB_TYPE event_type,
	int player_idx,
	const FVector& position )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, event_type, simt),
	player_index(player_idx)
{
	position_x = position.x;
	position_z = position.z;
}

void RUCBBallDeadDecisionEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBBallDeadDecisionEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallDeadDecisionEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallDeadDecisionEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallDeadDecisionEvent, position_z);
}

//-------------------------RUCBFumbleEvent--------------------------------
RUCBFumbleEvent::RUCBFumbleEvent(
	int simt,
	int player_idx,
	int team_idx,
	const FVector& position,
	bool from_tackle )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_FUMBLE, simt),
	player_index(player_idx),
	team_index(team_idx),
	from_tackle(from_tackle)
{
	position_x = position.x;
	position_z = position.z;
}

void RUCBFumbleEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBFumbleEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBFumbleEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBFumbleEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBFumbleEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBFumbleEvent, position_z);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBFumbleEvent, from_tackle);
}


//---------------------------RUCBFumbleEvent-------------------------------
RUCBKnockOnEvent::RUCBKnockOnEvent(
	int simt,
	int player_idx,
	int team_idx,
	const FVector& position,
	bool from_tackle)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_KNOCK_ON, simt),
	player_index(player_idx),
	team_index(team_idx),
	from_tackle(from_tackle)
{
	position_x = position.x;
	position_z = position.z;
}

void RUCBKnockOnEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBKnockOnEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKnockOnEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKnockOnEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKnockOnEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKnockOnEvent, position_z);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKnockOnEvent, from_tackle);
}

//------------------------ RUCBFistPumpEvent ------------------------------
RUCBFistPumpEvent::RUCBFistPumpEvent( int simt, int player_idx, int team_idx ):
							SSContextBucketEvent(SSCB_CLASS_GAMEPLAY_DERIVED, SSCB_EVENT_FIST_PUMP, simt),
							player_index(player_idx),
							team_index(team_idx)
{
}

void RUCBFistPumpEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBFistPumpEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBFistPumpEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBFistPumpEvent, team_index);
}

//------------------------ RUCBCollectedEvent ------------------------------
RUCBCollectedEvent::RUCBCollectedEvent( int simt, int player_idx, SSCB_TYPE preceding_event, int previous_holder, const FVector& collect_position, bool fumble_recover, bool ball_in_motion, bool dive_recover ):
								SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_COLLECTED, simt),
								player_index(player_idx),
								preceding_event(preceding_event),
								prev_player_index(previous_holder),
								position_x(collect_position.x),
								position_z(collect_position.z),
								fumble_recover(fumble_recover),
								ball_in_motion(ball_in_motion),
								dive_recover(dive_recover)
{
}

void RUCBCollectedEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBCollectedEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCollectedEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCollectedEvent, preceding_event);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCollectedEvent, prev_player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCollectedEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCollectedEvent, position_z);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCollectedEvent, fumble_recover);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCollectedEvent, ball_in_motion);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCollectedEvent, dive_recover);
}

//------------------------ RUCBPickedUpEvent ------------------------------
RUCBPickedUpEvent::RUCBPickedUpEvent( int simt, int player_idx, PickedUpContext picked_up_context, const FVector& collect_position ):
SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_PICKED_UP, simt),
player_index(player_idx),
picked_up_context(picked_up_context),
position_x(collect_position.x),
position_z(collect_position.z)
{
}

void RUCBPickedUpEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBPickedUpEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPickedUpEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPickedUpEvent, picked_up_context);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPickedUpEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPickedUpEvent, position_z);
}


//------------------------ RUCBPossessionChangeEvent ------------------------------
RUCBNewPhaseEvent::RUCBNewPhaseEvent( int simt, int team_idx ):
							SSContextBucketEvent(SSCB_CLASS_GAME_TIMING, SSCB_TIMING_NEW_PHASE, simt),
							team_index(team_idx)
{
}

void RUCBNewPhaseEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBNewPhaseEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBNewPhaseEvent, team_index);
}

//------------------------ RUCBBrokenTackleEvent ------------------------------
RUCBBrokenTackleEvent::RUCBBrokenTackleEvent( int simt, int action_result ):
									SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_BROKEN_TACKLE, simt),
									action_result(action_result)
{
}

void RUCBBrokenTackleEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBBrokenTackleEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBrokenTackleEvent, action_result);
}

//--------------------------- RUCBKickEvent ---------------------------------
RUCBKickEvent::RUCBKickEvent(
	int simt,
	int player_idx,
	int team_idx,
	int player2_index,
	const FVector& position,
	const FVector& kick_distance,
	KickType kick_t,
	KickContext kick_c,
	int pressure,
	bool out_on_full,
	bool bounce_out_probable,
	int first_bounce_field_position,
	int first_bounce_x_absolute)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_KICK, simt),
	player_index(player_idx),
	team_index(team_idx),
	player2_index(player2_index),
	position_x(position.x),
	position_z(position.z),
	distance_x(kick_distance.x),
	distance_z(kick_distance.z),
	kick_type(kick_t),
	kick_context(kick_c),
	pressure(pressure),
	first_bounce_field_position(first_bounce_field_position),
	first_bounce_x_absolute(first_bounce_x_absolute),
	out_on_full(out_on_full),
	bounce_out_probable(bounce_out_probable)
{
}

void RUCBKickEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBKickEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickEvent, player2_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickEvent, position_z);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickEvent, distance_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickEvent, distance_z);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickEvent, kick_type);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickEvent, kick_context);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickEvent, pressure);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickEvent, first_bounce_field_position);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickEvent, first_bounce_x_absolute);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickEvent, out_on_full);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickEvent, bounce_out_probable);
}

void RUCBKickEvent::PrintDebug()
{
	char message[256];
	MabStringHelper::Sprintf( message, "position %f,%f", position_x, position_z );
	MABLOGDEBUG( message );
}

//--------------------------- RUCBKickResultEvent ---------------------------------
RUCBKickResultEvent::RUCBKickResultEvent(
	int simt,
	SSCB_TYPE event_type,
	bool success,
	int players_idx,
	int team_idx,
	const FVector& target_pos,
	KickContext kick_c,
	int player2_index,
	int num_post_strikes )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, event_type, simt),
	success(success),
	player_index(players_idx),
	team_index(team_idx),
	position_x(target_pos.x),
	position_z(target_pos.z),
	kick_context(kick_c),
	player2_index(player2_index),
	num_post_strikes(num_post_strikes)
{

}

void RUCBKickResultEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBKickResultEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickResultEvent, success);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickResultEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickResultEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickResultEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickResultEvent, position_z);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickResultEvent, kick_context);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickResultEvent, player2_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickResultEvent, num_post_strikes);
}

//---------------------------- RUCBPassEvent -------------------------------
RUCBPassEvent::RUCBPassEvent(
	int simt,
	int player_from_idx,
	int player_to_idx,
	int player_cutout_idx,
	int team_from_idx,
	int team_to_idx,
	const FVector& target_pos,
	float pass_direction,
	RUCommentaryPassType pass_t,
	RUCommentaryPassLength pass_length,
	RUCommentaryPassSource pass_source,
	bool bad_pass )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_PASS, simt),
	player_index(player_from_idx),
	player2_index(player_to_idx),
	player3_index(player_cutout_idx),
	team_index(team_from_idx),
	team2_index(team_to_idx),
	pass_direction(pass_direction),
	action_type(pass_t),
	pass_length(pass_length),
	pass_source(pass_source),
	bad_pass(bad_pass)
{
	position_x = target_pos.x;
	position_z = target_pos.z;
}

void RUCBPassEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBPassEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPassEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPassEvent, player2_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPassEvent, player3_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPassEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPassEvent, team2_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPassEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPassEvent, position_z);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPassEvent, pass_direction);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPassEvent, action_type);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPassEvent, pass_length);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPassEvent, pass_source);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPassEvent, bad_pass);
}

//------------------------- RUCBTryEvent ----------------------------
RUCBTryEvent::RUCBTryEvent(
	int simt,
	bool success,
	bool penalty,
	int player_idx,
	int team_idx,
	TryLocationContext try_location_context,
	SSGT_HALF half,
	int minute_of_scoring,
	int minutes_since_scoring_last_this_period,
	float meters_gained_team,
	float meters_gained_player,
	int defensive_pressure )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_TRY, simt),
	success(success),
	penalty(penalty),
	player_index(player_idx),
	team_index(team_idx),
	try_location_context(try_location_context),
	half(half),
	minute_of_scoring(minute_of_scoring),
	minutes_since_scoring_last_this_period(minutes_since_scoring_last_this_period),
	meters_gained(meters_gained_team),
	meters_gained_player(meters_gained_player),
	defensive_pressure(defensive_pressure)
{
}

void RUCBTryEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBTryEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryEvent, success);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryEvent, penalty);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryEvent, try_location_context);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryEvent, half);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryEvent, minute_of_scoring);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryEvent, minutes_since_scoring_last_this_period);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryEvent, meters_gained);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryEvent, meters_gained_player);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryEvent, defensive_pressure);
}


//------------------------- RUCBTryFollowUpEvent ----------------------------
RUCBTryFollowUpEvent::RUCBTryFollowUpEvent(
	int simt,
	int player_idx,
	int team_idx)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_TRY_FOLLOW_UP, simt),
	player_index(player_idx),
	team_index(team_idx)
{
}

void RUCBTryFollowUpEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBTryFollowUpEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryFollowUpEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryFollowUpEvent, team_index);
}


//------------------------- RUCBGenericFollowUpEvent ----------------------------
RUCBGenericFollowUpEvent::RUCBGenericFollowUpEvent(
	SSCB_TYPE event_type,
	int simt,
	int player1_idx,
	int player2_idx)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, event_type, simt),
	player_index(player1_idx),
	player2_index(player2_idx)
{
}

void RUCBGenericFollowUpEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBGenericFollowUpEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBGenericFollowUpEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBGenericFollowUpEvent, player2_index);
}



//------------------------ RUCBTouchdownOwnGoal ------------------------------
RUCBTouchdownOwnGoal::RUCBTouchdownOwnGoal(
	int simt,
	int player_idx,
	int team_idx,
	int player2_idx,
	bool ball_received_in_goal,
	bool received_from_penalty_goal )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_TOUCHDOWN_OWN_GOAL, simt),
	player_index(player_idx),
	team_index(team_idx),
	player2_index(player2_idx),
	ball_received_in_goal(ball_received_in_goal),
	received_from_penalty_goal(received_from_penalty_goal)
{
}

void RUCBTouchdownOwnGoal::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBTouchdownOwnGoal, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTouchdownOwnGoal, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTouchdownOwnGoal, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTouchdownOwnGoal, player2_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTouchdownOwnGoal, ball_received_in_goal);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTouchdownOwnGoal, received_from_penalty_goal);
}

//------------------------ RUCBKickAtPostsReadyEvent ------------------------------
RUCBKickAtPostsReadyEvent::RUCBKickAtPostsReadyEvent(
	SSCB_TYPE event_type,
	int simt,
	int player_idx,
	int team_idx,
	int abs_x_kick_position,
	int score_difference,
	bool is_conversion,
	bool is_penalty_goal )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, event_type, simt),
	player_index(player_idx),
	team_index(team_idx),
	abs_x_kick_position(abs_x_kick_position),
	score_difference(score_difference),
	is_conversion(is_conversion),
	is_penalty(is_penalty_goal)
{
}

void RUCBKickAtPostsReadyEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBKickAtPostsReadyEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickAtPostsReadyEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickAtPostsReadyEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickAtPostsReadyEvent, abs_x_kick_position);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickAtPostsReadyEvent, score_difference);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickAtPostsReadyEvent, is_conversion);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickAtPostsReadyEvent, is_penalty);
}

//------------------------- RUCBLineoutEvent ------------------------
RUCBLineoutSignalledEvent::RUCBLineoutSignalledEvent( int simt, int team_index, const FVector& line_out_position  )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_LINEOUT_SIGNALLED, simt),
	team_index(team_index),
	position_x( line_out_position.x ),
	position_z( line_out_position.z )
{
}

void RUCBLineoutSignalledEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBLineoutSignalledEvent, SSContextBucketEvent);

	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutSignalledEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutSignalledEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutSignalledEvent, position_z);
}

//------------------------- RUCBLineoutDecisionEvent ------------------------
RUCBLineoutDecisionEvent::RUCBLineoutDecisionEvent( int simt, LineoutDecisionContext decision_context, int player_idx, int team_idx  ):
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_LINEOUT_DECISION, simt),
	decision( decision_context ),
	player_index( player_idx ),
	team_index( team_idx )
{
}

void RUCBLineoutDecisionEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBLineoutDecisionEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutDecisionEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutDecisionEvent, team_index);
}

//------------------------- RUCBLineoutReadyEvent ------------------------
RUCBLineoutReadyEvent::RUCBLineoutReadyEvent(  int simt, int player_idx, int team_idx ):
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_LINEOUT_READY, simt),
	player_index(player_idx),
	team_index(team_idx)
{
}

void RUCBLineoutReadyEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBLineoutReadyEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutReadyEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutReadyEvent, team_index);
}

//------------------------- RUCBLineoutThrownEvent ------------------------
RUCBLineoutThrownEvent::RUCBLineoutThrownEvent(  int simt, int throw_type, bool legal_throw_in, float travel_distance_x_absolute, int player_idx, int team_idx, int player2_idx ):
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_LINEOUT_THROW, simt),
	throw_type(throw_type),
	legal_throw(legal_throw_in),
	travel_distance_x_absolute(travel_distance_x_absolute),
	player_index(player_idx),
	team_index(team_idx),
	player2_index(player2_idx)
{
}

void RUCBLineoutThrownEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBLineoutThrownEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutThrownEvent, throw_type);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutThrownEvent, legal_throw);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutThrownEvent, travel_distance_x_absolute);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutThrownEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutThrownEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutThrownEvent, player2_index);
}

//------------------------- RUCBLineoutCatchEvent ------------------------
RUCBLineoutCatchEvent::RUCBLineoutCatchEvent(  int simt, int player_idx, int team_idx, bool turnover ):
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_LINEOUT_CATCH, simt),
	player_index(player_idx),
	team_index(team_idx),
	turnover(turnover)
{
}

void RUCBLineoutCatchEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBLineoutCatchEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutCatchEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutCatchEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutCatchEvent, turnover);
}

//------------------------- RUCBLineoutMissEvent ------------------------
RUCBLineoutMissEvent::RUCBLineoutMissEvent(  int simt, int team_idx, bool both_teams_miss, int miss_count ):
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_LINEOUT_MISS, simt),
	team_index(team_idx),
	both_teams_miss(both_teams_miss),
	miss_count(miss_count)
{
}

void RUCBLineoutMissEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBLineoutMissEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutMissEvent, both_teams_miss);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutMissEvent, team_index);
}

//------------------------- RUCBLineoutToMaulEvent ------------------------
RUCBLineoutToMaulEvent::RUCBLineoutToMaulEvent(  int simt, int player_idx, int team_idx ):
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_LINEOUT_MAUL, simt),
	player_index(player_idx),
	team_index(team_idx)
{
}

void RUCBLineoutToMaulEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBLineoutToMaulEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutToMaulEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutToMaulEvent, team_index);
}



//------------------------- RUCBMaulEvent ------------------------
RUCBMaulEvent::RUCBMaulEvent(SSCB_TYPE event_type,  int simt, int player_idx, int team_idx, int player2_idx, int team2_idx, const FVector &position):
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, event_type, simt),
	player_index(player_idx),
	team_index(team_idx),
	player2_index(player2_idx),
	team2_index(team2_idx),
	velocity(0.0f),
	ground_made(0.0f)
{
	position_x = position.x;
	position_z = position.z;
}

void RUCBMaulEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBMaulEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBMaulEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBMaulEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBMaulEvent, player2_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBMaulEvent, team2_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBMaulEvent, velocity);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBMaulEvent, ground_made);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBMaulEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBMaulEvent, position_z);
}


//------------------------- RUCBLineoutToRunEvent ------------------------
RUCBLineoutToRunEvent::RUCBLineoutToRunEvent(  int simt, int player_idx, int team_idx ):
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_LINEOUT_RUN, simt),
	player_index(player_idx),
	team_index(team_idx)
{
}

void RUCBLineoutToRunEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBLineoutToRunEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutToRunEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutToRunEvent, team_index);
}

//------------------------- RUCBLineoutToPassEvent ------------------------
RUCBLineoutToPassEvent::RUCBLineoutToPassEvent(  int simt, int player_idx, int player2_idx ):
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_LINEOUT_PASS, simt),
	player_index(player_idx),
	player2_index(player2_idx)
{
}

void RUCBLineoutToPassEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBLineoutToPassEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutToPassEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutToPassEvent, player2_index);
}

//------------------------- RUCBLineoutSlapDownEvent ------------------------
RUCBLineoutSlapDownEvent::RUCBLineoutSlapDownEvent(  int simt, int player_idx, int player2_idx  )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_LINEOUT_SLAP_DOWN, simt),
	player_index(player_idx),
	player2_index(player2_idx)
{
}

void RUCBLineoutSlapDownEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBLineoutSlapDownEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutSlapDownEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineoutSlapDownEvent, player2_index);
}

//---------------------- RUCBRuckEvent --------------------------------
RUCBRuckEvent::RUCBRuckEvent(
	int simt, int player_idx, int team_idx, const FVector& position, bool isolated, int num_phases )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_RUCK, simt),
	player_index(player_idx),
	team_index(team_idx),
	position_x(position.x),
	position_z(position.z),
	isolated(isolated),
	num_phases(num_phases)
{
}

void RUCBRuckEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBRuckEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckEvent, position_z);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckEvent, isolated);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckEvent, num_phases);
}

//---------------------- RUCBTackleEvent --------------------------------
RUCBTackleEvent::RUCBTackleEvent(
	SSCB_TYPE tackle_event_type,
	int simt,
	TACKLE_RESULT_TYPE tackle_type,
	bool action_result,
	int tackle_dominance,
	int player_index,
	int tacklee_team_index,
	int tackler_index,
	int tackler_team_index,
	const FVector& position,
	int impact,
	int num_tacklers,
	int ground_made,
	int previous_breaks,
	int try_tackle_type)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, tackle_event_type, simt ),
	tackle_type(tackle_type),
	tackle_dominance((int) tackle_dominance),
	player_index(player_index),
	player2_index(tackler_index),
	tacklee_team_index(tacklee_team_index),
	tackler_team_index(tackler_team_index),
	position_x(position.x),
	position_z(position.z),
	impact(impact),
	num_tacklers(num_tacklers),
	ground_made(ground_made),
	previous_breaks(previous_breaks),
	try_tackle_type(try_tackle_type),
	action_result(action_result)
{
}

void RUCBTackleEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBTackleEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTackleEvent, tackle_type);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTackleEvent, action_result);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTackleEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTackleEvent, player2_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTackleEvent, tacklee_team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTackleEvent, tackler_team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTackleEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTackleEvent, position_z);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTackleEvent, impact);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTackleEvent, num_tacklers);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTackleEvent, ground_made);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTackleEvent, previous_breaks);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTackleEvent, try_tackle_type);
}

//--------------------------RUCBMarkEvent-------------------------------

RUCBMarkEvent::RUCBMarkEvent(
	int simt,
	int player_index,
	int team_index,
	const FVector& position )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_MARK, simt),
	player_index(player_index),
	team_index(team_index),
	position_x(position.x),
	position_z(position.z)
{

}

void RUCBMarkEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBMarkEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBMarkEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBMarkEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBMarkEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBMarkEvent, position_z);
}


//--------------------------RUCBCatchEvent-------------------------------
RUCBCatchEvent::RUCBCatchEvent(
	int simt,
	RUCBCatchType catch_type,
	bool catch_in_air,
	bool catch_marked,
	bool on_full,
	int player_index,
	int player2_index,
	const FVector& catch_position)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_CATCH, simt),
	catch_type(catch_type),
	catch_in_air(catch_in_air),
	catch_marked(catch_marked),
	on_full(on_full),
	player_index(player_index),
	player2_index(player2_index),
	position_x(catch_position.x),
	position_z(catch_position.z)
{
}

void RUCBCatchEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBCatchEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCatchEvent, catch_type);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCatchEvent, catch_in_air);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCatchEvent, catch_marked);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCatchEvent, on_full);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCatchEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCatchEvent, player2_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCatchEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCatchEvent, position_z);
}

//-------------------------- RUCBTimeKeepingEvent -------------------------------
RUCBTimeKeepingEvent::RUCBTimeKeepingEvent( int simt, TimeKeepingContext time_keeping_context )
:
	SSContextBucketEvent(SSCB_CLASS_GAME_TIMING, SSCB_EVENT_TIME_KEEPING, simt),
	time_keeping_context(time_keeping_context)
{}

void RUCBTimeKeepingEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBTimeKeepingEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTimeKeepingEvent, time_keeping_context);
}

//-------------------------- RUCBFowardPassEvent -------------------------------
RUCBFowardPassEvent::RUCBFowardPassEvent( int simt, int passer_idx, int reciever_idx, int team_idx )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_FOWARD_PASS, simt),
	passer_index(passer_idx),
	reciever_index(reciever_idx),
	team_index(team_idx)
{

}

void RUCBFowardPassEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBFowardPassEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBFowardPassEvent, passer_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBFowardPassEvent, reciever_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBFowardPassEvent, team_index);
}
//---------------------------- RUCBOffside -------------------------------
RUCBOffsideEvent::RUCBOffsideEvent( int simt, int player_idx, int team_idx, OffsideContext offside_context )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_OFFSIDE, simt),
	player_index(player_idx),
	team_index(team_idx),
	offside_context(offside_context)
{

}

void RUCBOffsideEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBOffsideEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBOffsideEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBOffsideEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBOffsideEvent, offside_context);
}



//---------------------------- RUCBStoppageEvent -------------------------------
RUCBStoppageEvent::RUCBStoppageEvent( int simt, int player_idx, int team_idx )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_STOPPAGE, simt),
	player_index(player_idx),
	team_index(team_idx)
{
}

void RUCBStoppageEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBStoppageEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBOffsideEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBOffsideEvent, team_index);
}


//---------------------------- RUCBTryLikelyEvent -------------------------------
RUCBTryLikelyEvent::RUCBTryLikelyEvent( int simt, int player_idx, int team_idx )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_TRY_LIKELY, simt),
	player_index(player_idx),
	team_index(team_idx)
{

}

void RUCBTryLikelyEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBTryLikelyEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryLikelyEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryLikelyEvent, team_index);
}


//---------------------------- RUCBBallHolderRunningEvent -------------------------------
RUCBBallHolderRunningEvent::RUCBBallHolderRunningEvent( int simt, int player_idx, int player2_idx, int team_idx, BallHolderRunningTypes running_event_type  )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_BALL_HOLDER_RUN, simt),
	player_index(player_idx),
	player2_index(player2_idx),
	team_index(team_idx),
	running_event_type(running_event_type)
{

}

void RUCBBallHolderRunningEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBBallHolderRunningEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallHolderRunningEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallHolderRunningEvent, player2_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallHolderRunningEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallHolderRunningEvent, running_event_type);
}

//---------------------------- RUCBTryAttempt -------------------------------
RUCBTryAttemptEvent::RUCBTryAttemptEvent( int simt, int player_idx, int team_idx, bool is_diving)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_TRY_ATTEMPT, simt),
	player_index(player_idx),
	team_index(team_idx),
	is_diving(is_diving)
{

}

void RUCBTryAttemptEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBTryAttemptEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryAttemptEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryAttemptEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTryAttemptEvent, is_diving);

}



//---------------------------- RUCBScrumCalledEvent -------------------------------
RUCBScrumCalledEvent::RUCBScrumCalledEvent( int simt, int team_idx, FVector position )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_SCRUM_CALLED, simt),
	team_index(team_idx),
	position_x(position.x),
	position_z(position.z)
{

}

void RUCBScrumCalledEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBScrumCalledEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumCalledEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumCalledEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumCalledEvent, position_z);
}

//---------------------------- RUCBScrum -------------------------------
RUCBScrumEvent::RUCBScrumEvent(SSCB_TYPE scrum_event_type, int simt, int player_idx, int team_idx, FVector position )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, scrum_event_type, simt),
	player_index(player_idx),
	team_index(team_idx),
	position_x(position.x),
	position_z(position.z)
{

}

void RUCBScrumEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBScrumEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumEvent, position_z);
}



//---------------------------- RUCBScrumPushingEvent -------------------------------
RUCBScrumPushingEvent::RUCBScrumPushingEvent(
	int simt,
	int team_idx,
	float relative_power,
	FVector position,
	int shunt_count,
	float attacking_team_power,
	float defending_team_power )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_SCRUM_PUSHING, simt),
	team_index(team_idx),
	relative_power(relative_power),
	position_x(position.x),
	position_z(position.z),
	shunt_count(shunt_count),
	attacking_team_power(attacking_team_power),
	defending_team_power(defending_team_power)
{

}

void RUCBScrumPushingEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBScrumPushingEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumPushingEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumPushingEvent, relative_power);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumPushingEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumPushingEvent, position_z);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumPushingEvent, shunt_count);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumPushingEvent, attacking_team_power);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumPushingEvent, defending_team_power);
}

//---------------------------- RUCBScrumResult -------------------------------
RUCBScrumResultEvent::RUCBScrumResultEvent(
	int simt,
	int team_idx,
	ScrumResultContext scrum_context,
	float meters_gained,
	ScrumResetContext reset_context )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_SCRUM_RESULT, simt),
	team_index(team_idx),
	scrum_result_context(scrum_context),
	meters_gained(meters_gained),
	reset_context(reset_context)
{

}

void RUCBScrumResultEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBScrumResultEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumResultEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumResultEvent, scrum_result_context);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumResultEvent, meters_gained);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBScrumResultEvent, reset_context);
}



//---------------------------- RUCBLineBreakEvent -------------------------------
RUCBLineBreakEvent::RUCBLineBreakEvent(
	int simt,
	int player_index,
	int team_index,
	int line_break_type,
	const FVector& location
	)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_LINE_BREAK, simt),
	player_index( player_index ),
	team_index(team_index),
	line_break_type(line_break_type),
	position_x(location.x),
	position_z(location.z)
{
}

void RUCBLineBreakEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBLineBreakEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineBreakEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineBreakEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineBreakEvent, line_break_type);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineBreakEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBLineBreakEvent, position_z);
}

//---------------------------- RUCBRuckResultEvent -------------------------------
RUCBRuckResultEvent::RUCBRuckResultEvent( int simt, int player_index, int team_idx, RuckResultContext ruck_context, const FVector& ruck_center )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_RUCK_RESULT, simt),
	player_index(player_index),
	team_index(team_idx),
	ruck_result_context(ruck_context),
	position_x(ruck_center.x),
	position_z(ruck_center.z)
{

}

void RUCBRuckResultEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBRuckResultEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckResultEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckResultEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckResultEvent, ruck_result_context);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckResultEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckResultEvent, position_z);
}

//---------------------------- RUCBKickedOutEvent -------------------------------
RUCBTerritoryKickEvent::RUCBTerritoryKickEvent( int simt, int player_idx, int team_idx, float territory_gained, bool on_full )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_KICK_TERRITORY, simt),
	player_index(player_idx),
	team_index(team_idx),
	territory_gained(territory_gained),
	on_full(on_full)
{

}

void RUCBTerritoryKickEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBTerritoryKickEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTerritoryKickEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTerritoryKickEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTerritoryKickEvent, territory_gained);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTerritoryKickEvent, on_full);
}

//---------------------------- RUCBCarriedOutOfPlayEvent -------------------------------
RUCBCarriedOutOfPlayEvent::RUCBCarriedOutOfPlayEvent( int simt, int player_idx, int team_idx, bool from_tackle, const FVector& out_of_play_position )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_CARRIED_INTO_TOUCH, simt),
	player_index(player_idx),
	team_index(team_idx),
	from_tackle(from_tackle),
	position_x(out_of_play_position.x),
	position_z(out_of_play_position.z)
{

}

void RUCBCarriedOutOfPlayEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBCarriedOutOfPlayEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCarriedOutOfPlayEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCarriedOutOfPlayEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCarriedOutOfPlayEvent, from_tackle);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCarriedOutOfPlayEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBCarriedOutOfPlayEvent, position_z);
}

//---------------------------- RUCBBallInTouchEvent -------------------------------
RUCBBallInTouchEvent::RUCBBallInTouchEvent( int simt, int player_idx, int team_idx, const FVector& out_of_play_position )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_BALL_IN_TOUCH, simt),
	player_index(player_idx),
	team_index(team_idx),
	position_x(out_of_play_position.x),
	position_z(out_of_play_position.z)
{

}

void RUCBBallInTouchEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBBallInTouchEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallInTouchEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallInTouchEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallInTouchEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallInTouchEvent, position_z);
}

//---------------------------- RUCBBallDeadEvent -------------------------------
RUCBBallDeadEvent::RUCBBallDeadEvent( int simt, int player_idx, const FVector& out_of_play_position, const FVector &ball_free_position, bool carried )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_BALL_DEAD, simt),
	player_index(player_idx),
	position_x(out_of_play_position.x),
	position_z(out_of_play_position.z),
	ball_free_position_z(ball_free_position.z),
	carried(carried)
{

}

void RUCBBallDeadEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBBallDeadEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallDeadEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallDeadEvent, position_x);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallDeadEvent, position_z);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallDeadEvent, ball_free_position_z);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBallDeadEvent, carried);
}

//---------------------------- RUCBKickOffReady -------------------------------
RUCBKickOffReady::RUCBKickOffReady(
	int simt,
	int player_index,
	int team_index,
	TimeKeepingContext time_keeping_context
	)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_KICKOFF_READY, simt),
	player_index( player_index ),
	team_index(team_index),
	time_keeping_context(time_keeping_context)
{
}

void RUCBKickOffReady::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBKickOffReady, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickOffReady, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickOffReady, team_index);
}

//---------------------------- RUCBKickOffWhistle -------------------------------
RUCBKickOffWhistle::RUCBKickOffWhistle(
	int simt,
	int player_index,
	int team_index,
	TimeKeepingContext time_keeping_context
	)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_KICKOFF_WHISTLE, simt),
	player_index( player_index ),
	team_index(team_index),
	time_keeping_context(time_keeping_context)
{
}

void RUCBKickOffWhistle::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBKickOffWhistle, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickOffWhistle, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickOffWhistle, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickOffWhistle, time_keeping_context);
}



//---------------------------- RUCBSideStepEvent -------------------------------
RUCBSideStepEvent::RUCBSideStepEvent(
	int simt,
	int player_index,
	int player2_index)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_SIDE_STEP, simt),
	player_index( player_index ),
	player2_index( player2_index)
{
}

void RUCBSideStepEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBSideStepEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBSideStepEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBSideStepEvent, player2_index);
}
//---------------------------- RUCBTryLikelyEvent -------------------------------
RUCBCrowdIntensityEvent::RUCBCrowdIntensityEvent( int simt )
:
SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_CROWD_INTENSITY, simt)
{

}

void RUCBCrowdIntensityEvent::DefineMabCentralInterfaces()
{
	MABCENTRAL_TYPE2(RUCBCrowdIntensityEvent, SSContextBucketEvent);
}

//---------------------------- RUCBPlayerWalkOnEvent -------------------------------
RUCBPlayerWalkOnEvent::RUCBPlayerWalkOnEvent( int simt )
:
SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_PLAYER_WALK_ON, simt)
{

}

void RUCBPlayerWalkOnEvent::DefineMabCentralInterfaces()
{
	MABCENTRAL_TYPE2(RUCBPlayerWalkOnEvent, SSContextBucketEvent);
}


//---------------------------- RUCBKickAndChaseEvent -------------------------------
RUCBKickAndChaseEvent::RUCBKickAndChaseEvent(int simt,
									   int player_index,
									   int team_index
									   )
									   :
SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_KICK_AND_CHASE, simt),
player_index( player_index ),
team_index(team_index)
{
}

void RUCBKickAndChaseEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBKickAndChaseEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickAndChaseEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBKickAndChaseEvent, team_index);
}

//---------------------------- RUCBRuckPlayerJoinEvent -------------------------------
RUCBRuckPlayerJoinEvent::RUCBRuckPlayerJoinEvent(
	int simt,
	int player_index,
	int player2_index,
	int team_index,
	RUZoneJoinType join_type,
	int joiners_count,
	int attackers_count,
	int defenders_count,
	int ruck_age,
	bool added_after_result
	)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_RUCK_JOIN, simt),
	player_index( player_index ),
	player2_index( player2_index ),
	team_index(team_index),
	join_type(join_type),
	joiners_count(joiners_count),
	attackers_count(attackers_count),
	defenders_count(defenders_count),
	ruck_age(ruck_age),
	power_bar_position(0.0f),
	power_bar_velocity(0.0f),
	added_after_result(added_after_result)
{
}

void RUCBRuckPlayerJoinEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBRuckPlayerJoinEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckPlayerJoinEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckPlayerJoinEvent, player2_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckPlayerJoinEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckPlayerJoinEvent, join_type);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckPlayerJoinEvent, joiners_count);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckPlayerJoinEvent, attackers_count);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckPlayerJoinEvent, defenders_count);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckPlayerJoinEvent, ruck_age);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckPlayerJoinEvent, power_bar_position);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckPlayerJoinEvent, power_bar_velocity);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckPlayerJoinEvent, added_after_result);
}

//---------------------------- RUCBRuckScootEvent -------------------------------
RUCBRuckScootEvent::RUCBRuckScootEvent(
	int simt,
	int player_index,
	int team_index,
	const FVector& position
	)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_RUCK_SCOOT, simt),
	player_index( player_index ),
	team_index(team_index),
	position_z(position.z)
{
}

void RUCBRuckScootEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBRuckScootEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckScootEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckScootEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckScootEvent, position_z);
}

//---------------------------- RUCBRuckSlowEvent -------------------------------
RUCBRuckSlowEvent::RUCBRuckSlowEvent(int simt, int scrum_half_player_index, int attacking_team_index )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_RUCK_SLOW, simt),
	player_index(scrum_half_player_index),
	team_index(attacking_team_index)
{
}

void RUCBRuckSlowEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBRuckSlowEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckSlowEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBRuckSlowEvent, team_index);
}

//---------------------------- RUCBAdvantageEvent -------------------------------
RUCBAdvantageEvent::RUCBAdvantageEvent( int simt, int offending_player_index, int offending_team_index, RUCBAdvantageEventType advantage_event_type  )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_ADVANTAGE, simt),
	player_index(offending_player_index),
	team_index(offending_team_index),
	advantage_event_type(advantage_event_type)
{
}

void RUCBAdvantageEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBAdvantageEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBAdvantageEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBAdvantageEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBAdvantageEvent, advantage_event_type);
}

//---------------------------- RUCBPreMidPostEvent -------------------------------
RUCBPreMidPostEvent::RUCBPreMidPostEvent(
	int simt,
	SSCB_TYPE event_type,
	int team_index,
	int team2_index )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, event_type, simt),
	team_index(team_index),
	team2_index(team2_index)
{
}

void RUCBPreMidPostEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBPreMidPostEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPreMidPostEvent, team_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBPreMidPostEvent, team2_index);
}


//---------------------------- RUCBInjuryEvent -------------------------------
RUCBInjuryEvent::RUCBInjuryEvent(
	int simt,
	int player_index,
	int injury_type )
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_INJURY, simt),
	player_index(player_index),
	injury_type(injury_type)
{
}

void RUCBInjuryEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBInjuryEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBInjuryEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBInjuryEvent, injury_type);
}


//---------------------------- SSCBCutsceneEvent -------------------------------
SSCBCutsceneEvent::SSCBCutsceneEvent(
	int simt,
	SSCB_TYPE event_type
	)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, event_type, simt),
	replay_type(0)
{
}

void SSCBCutsceneEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(SSCBCutsceneEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, SSCBCutsceneEvent, replay_type);
}


//---------------------------- RUCBInterchangeEvent -------------------------------
RUCBInterchangeEvent::RUCBInterchangeEvent(
	int simt,
	int player_index,
	int player2_index,
	unsigned int interchange_type)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_INTERCHANGE_MADE, simt),
	player_index(player_index),
	player2_index(player2_index),
	interchange_type(interchange_type)
{
}

void RUCBInterchangeEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBInterchangeEvent, SSContextBucketEvent);

	MABCENTRAL_ATTRIBUTE(type_id, RUCBInterchangeEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBInterchangeEvent, player2_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBInterchangeEvent, interchange_type);
}

//---------------------------- RUCBDistractedEvent -------------------------------
RUCBDistractedEvent::RUCBDistractedEvent( int simt, int player_index, int player2_index, DISTRACTION_LEVEL distraction_level)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_DISTRACTION, simt),
	player_index(player_index),
	player2_index(player2_index),
	distraction_level(distraction_level)
{
}

void RUCBDistractedEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBDistractedEvent, SSContextBucketEvent);

	MABCENTRAL_ATTRIBUTE(type_id, RUCBDistractedEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBDistractedEvent, player2_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBDistractedEvent, distraction_level);
}


//---------------------------- RUCBTelevisionMatchOfficialEvent -------------------------------
RUCBTelevisionMatchOfficialEvent::RUCBTelevisionMatchOfficialEvent(
	int simt,
	SSCB_TYPE event_type,
	int player_index,
	bool try_awarded,
	bool possibly_held_up)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, event_type, simt),
	player_index(player_index),
	try_awarded(try_awarded),
	possibly_held_up(possibly_held_up)
{
}

void RUCBTelevisionMatchOfficialEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBTelevisionMatchOfficialEvent, SSContextBucketEvent);

	MABCENTRAL_ATTRIBUTE(type_id, RUCBTelevisionMatchOfficialEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTelevisionMatchOfficialEvent, try_awarded);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBTelevisionMatchOfficialEvent, possibly_held_up);
}


//---------------------------- RUCBBasicPlayerEvent -------------------------------
RUCBBasicPlayerEvent::RUCBBasicPlayerEvent(
	int simt,
	SSCB_TYPE event_type,
	int player_index,
	int player2_index)
:
	SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, event_type, simt),
	player_index(player_index),
	player2_index(player2_index)
{
}

void RUCBBasicPlayerEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBBasicPlayerEvent, SSContextBucketEvent);

	MABCENTRAL_ATTRIBUTE(type_id, RUCBBasicPlayerEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBBasicPlayerEvent, player2_index);
}

RUCBQuickThrowInEvent::RUCBQuickThrowInEvent( int simt, SSCB_TYPE event_type, int player_idx, int team_idx )
: SSContextBucketEvent( SSCB_CLASS_GAMEPLAY, event_type, simt ) //event_type SSCB_EVENT_QUICK_THROW_IN
, player_index( player_idx )
, team_index( team_idx )
{

}

void RUCBQuickThrowInEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBQuickThrowInEvent, SSContextBucketEvent);

	MABCENTRAL_ATTRIBUTE(type_id, RUCBQuickThrowInEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBQuickThrowInEvent, team_index);
}

RUCBSetPlayEvent::RUCBSetPlayEvent(int simt, SSCB_TYPE event_type, int player_idx, int team_idx)
	: SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, event_type, simt)
	, player_index(player_idx)
	, team_index(team_idx)
{

}

void RUCBSetPlayEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBSetPlayEvent, SSContextBucketEvent);

	MABCENTRAL_ATTRIBUTE(type_id, RUCBSetPlayEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBSetPlayEvent, team_index);
}

// GGs JZ TODO SSCB_EVENT_TACKLECOUNT_6 is currently being used for all handover commentary, no tackle lines have played so it can be used for generalised atm
// We do have SSCB_EVENT_HANDOVER that could be used to split the lines into context in the future if need be.
//---------------------------- RUCBHandoverEvent -------------------------------
RUCBHandoverEvent::RUCBHandoverEvent(int simt, int offending_player_index, int offending_team_index)
	: SSContextBucketEvent(SSCB_CLASS_GAMEPLAY, SSCB_EVENT_TACKLECOUNT_6, simt)
	, player_index(offending_player_index)
	, team_index(offending_team_index)
{

}

void RUCBHandoverEvent::DefineMabCentralInterfaces()
{
	MabObjectType type_id = MABCENTRAL_TYPE2(RUCBHandoverEvent, SSContextBucketEvent);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBHandoverEvent, player_index);
	MABCENTRAL_ATTRIBUTE(type_id, RUCBHandoverEvent, team_index);
}