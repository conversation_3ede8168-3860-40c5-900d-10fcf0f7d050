// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "WWUIScreenCustomiseSelectTeam.h"

#include "RugbyGameInstance.h"
#include "RUUIDatabaseQueryManager.h"
#include "WWUITranslationManager.h"
#include "FanHub/WWRugbyFanHubService.h"

// Utility
#include "Utility/Helpers/SIFGameHelpers.h"
#include "Utility/Helpers/SIFUIHelpers.h"
#include "Utility/Helpers/SIFAudioHelpers.h"

// Databases
#include "Databases/RUGameDatabaseManager.h"

// Rugby Union
#include "Match/RugbyUnion/RUDatabaseTypes.h"

// WW UI
#include "UI/GeneratedHeaders/WWUIScreenCustomiseSelectTeam_UI_Namespace.h"
#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "UI/Populators/WWUIPopulatorTeamSelectList.h"
#include "UI/Screens/WWUIScreenTeamDetails.h"
#include "UI/Screens/WWUIScreenSignIn.h"

// WW UI Engine
#include "WWUIScrollBox.h"
#include "WWUITabSwitcher.h"
#include "WWUIListField.h"
#include "WWUIRichTextBlockWithTranslate.h"

// UE4 Engine
#include "Image.h"
#include "WWUIEditableTextBox.h"

#define TRIGGER_DELAY_TIME		(1.0f)

#define SEARCH_MAX_TEXT_LENGTH	(32)
#define SEARCH_MIN_TEXT_LENGTH	(3)

void UWWUIScreenCustomiseSelectTeam::Startup(UWWUIStateScreenData* InData /*= nullptr*/)
{
	bFirstFocus = true;

	bIsTeamLink = false;
	bIsSearch = false;
	bIsEditTeamSelect = false;
	TeamLinkTeamID = 0;
	SearchTeamString = "";
	TeamDatabaseID = 0;
	TeamDeleteID = 0;

	if (InData)
	{
		UWWUIStateScreenCustomiseSelectTeamData * pSelectTeamInData = Cast<UWWUIStateScreenCustomiseSelectTeamData>(InData);

		if (pSelectTeamInData)
		{
			UTextBlock* pBreadCrumbText = Cast<UTextBlock>(FindChildOfTemplateWidget(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::HeaderWidget), WWUIScreenCustomiseSelectTeam_UI::Subtitle));

			if (pBreadCrumbText)
			{
				pBreadCrumbText->SetText(FText::FromString(UWWUITranslationManager::Translate(pSelectTeamInData->BreadCrumbKey)));
			}

			bIsTeamLink = pSelectTeamInData->TeamLinkbIsTeamLink;

			if (bIsTeamLink)
			{
				TeamLinkTeamID = pSelectTeamInData->TeamLinkTeamID;
				TeamLinkDelegates = pSelectTeamInData->TeamLinkDelegates;
			}
		}
	}

	UWidget* pSearchTextWidget = FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::EditableTextBoxSearchTextEntry);

	UWWUIEditableTextBox* pSearchTextBox = Cast<UWWUIEditableTextBox>(pSearchTextWidget);
	if (pSearchTextBox)
	{
		pSearchTextBox->SetMaxCharLength(SEARCH_MAX_TEXT_LENGTH);
		pSearchTextBox->SetMinCharLength(SEARCH_MIN_TEXT_LENGTH);
		pSearchTextBox->SetCheckIsEmpty(false);

		pSearchTextBox->ClearKeyboardFocusOnCommit = false;
		pSearchTextBox->SynchronizeProperties();
	}

	if (bIsEditTeamSelect)
	{
		SIFGameHelpers::GASetGameMode(GAME_MODE_RU13);
	}

	UWWUITabSwitcher* pTabSwitcher = Cast<UWWUITabSwitcher>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::TabContainer));
	UTextBlock* pBreadCrumbText = Cast<UTextBlock>(FindChildOfTemplateWidget(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::HeaderWidget), WWUIScreenCustomiseSelectTeam_UI::Subtitle));
	UWWUIRichTextBlockWithTranslate* pLegendRichText = Cast<UWWUIRichTextBlockWithTranslate>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::LegendText));

	UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::TeamListScrollbox));
	UWWUIPopulatorTeamSelectList* pTeamSelectListPopulator = nullptr;

	if (pTeamListScrollbox)
	{
		pTeamSelectListPopulator = Cast<UWWUIPopulatorTeamSelectList>(pTeamListScrollbox->GetPopulator());
	}

	if (pTabSwitcher)
	{
		pTabSwitcher->SetTabText(0, FText::FromString(UWWUITranslationManager::Translate("[ID_CUSTOMISE_TEAM_SELECT]")));
	}

	if (pLegendRichText)
	{
		pLegendRichText->SetText("[ID_RC3_CUSTOMISE_TEAM]");
	}

	if (pTeamSelectListPopulator)
	{
		pTeamSelectListPopulator->SetGenderPermissionFlags(~0);

		if (SIFGameHelpers::GAIsCharacterCreator())
		{
			pTeamSelectListPopulator->SetTeamLimit(CAREER_TEAM_LIMIT_CUSTOM_DELETE);
		}
		else
		{
			pTeamSelectListPopulator->SetTeamLimit(TEAM_SELECT_RC3);
		}
	}

	if (bIsTeamLink)
	{
		if (pBreadCrumbText)
		{
			pBreadCrumbText->SetText(FText::FromString(UWWUITranslationManager::Translate("[ID_CUSTOMISE_OPTION_MENU_ITEM]")));
		}

		if (pTabSwitcher)
		{
			pTabSwitcher->SetTabText(0, FText::FromString(UWWUITranslationManager::Translate("[ID_LINK_TEAM_TITLE]")));
		}

		UE_LOG(LogTemp, Display, TEXT("%s"), *(FString("TeamLink.team_db_id = ") + FString::FromInt(TeamLinkTeamID)));

		if (pTeamSelectListPopulator)
		{
			pTeamSelectListPopulator->SetIsTeamLink(bIsTeamLink);
			pTeamSelectListPopulator->SetTeamLinkID(TeamLinkTeamID);
		}
	}

	if (!bSelectedSearchTeam)
	{
		// Increment the filter position with no direction to update the filter text. This will also do an initial populate of the scrollbox.
		IncrementFilterCompetition(0);
	}

	ugcUsernameCheckCompleteHandle = SIFApplication::GetApplication()->OnUGCUsernameCheckComplete.AddUObject(this, &UWWUIScreenCustomiseSelectTeam::HandleUGCUsernameCheckComplete);

	m_pCustomLimitBox = FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::CustomLimitBorder);
	if (m_pCustomLimitBox)
	{
		m_pCustomLimitBox->SetVisibility(ESlateVisibility::Collapsed);
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenCustomiseSelectTeam::Shutdown()
{
	Super::Shutdown();
	SIFApplication::GetApplication()->OnUGCUsernameCheckComplete.Remove(ugcUsernameCheckCompleteHandle);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::OnInFocus()
{
	OnWindowEnter();

	// Refresh when returning to the screen in case a team name has been changed.
	if (!bFirstFocus)
	{
		UWWUIScrollBox* pScrollBox;
		if (bIsSearch)
		{
			pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::SearchScrollBox));
		}
		else
		{
			pScrollBox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::TeamListScrollbox));
		}

		if (pScrollBox)
		{
			pScrollBox->PopulateAndRefresh();
			pScrollBox->SetSelectedIndex(CachedSelectedIndex);
		}
		else
		{
			ensure(pScrollBox);
		}
	}
	if (UWidget* pTextEntryWidget = FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::BorderLinesSearchBox))
	{
		pTextEntryWidget->SetVisibility(ESlateVisibility::Visible);
		pTextEntryWidget->SetRenderOpacity(0.0f);
	}

	bFirstFocus = false;
	m_canTriggerAxis = true;
}

void UWWUIScreenCustomiseSelectTeam::OnOutFocus(bool ShouldOutFocus)
{
	if (m_triggerAxisHandle.IsValid())
	{
		m_canTriggerAxis = true;
		UWWUIFunctionLibrary::StopTimer(m_triggerAxisHandle);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::RegisterFunctions()
{
	AddInputAction("UI_Back", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseSelectTeam::OnBack)); // B

#if PLATFORM_SWITCH 
	AddInputAxis("UI_Triggers_Axis", FWWUIScreenAxisDelegate::CreateUObject(this, &UWWUIScreenCustomiseSelectTeam::OnRotationInput));
#else
	AddInputAction("UI_LeftTrigger", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseSelectTeam::OnLeftTrigger)); // B
	AddInputAction("UI_RightTrigger", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseSelectTeam::OnRightTrigger)); // B
#endif

	AddInputAction("RU_UI_ACTION_CHARACTER_CREATOR_DELETE", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseSelectTeam::OnDelete)); // B
	AddInputAction("RU_UI_ACTION_CHANGE_STRIP", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseSelectTeam::OnUploadPressed)); // B

	AddInputAction("RU_UI_ACTION_SELECT", FWWUIScreenDelegate::CreateUObject(this, &UWWUIScreenCustomiseSelectTeam::OnOpenTextEntry)); // B
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::TableOnSelectionChange(FString InTableId, int OldIdx, int NewIdx)
{
	CachedSelectedIndex = NewIdx;
	UpdateSelectedTeam(NewIdx);
	UpdateTeamInfo();
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::ExecuteTableFunction(FString InTableId, int InIdx, FString InString, FString InActionString)
{
	if (!GetInputEnabled())
	{
		return;
	}

	if (bIsTeamLink)
	{
		if (TeamDatabaseID != 0)
		{
			if (!SIFGameHelpers::GAIsCreatedTeam(TeamDatabaseID))
			{
				if (SIFGameHelpers::GACheckTeamLink(TeamDatabaseID))
				{
					TArray<FModalButtonInfo> ButtonData;
					ButtonData.Add(FModalButtonInfo("[ID_YES]", FWWUIModalDelegate::CreateLambda([=](APlayerController* OwningController) {TeamLinkDelegates.ConfirmRemoveAndLinkDelegate.ExecuteIfBound(TeamDatabaseID); return true; })));
					ButtonData.Add(FModalButtonInfo("[ID_NO]"));
					SIFUIHelpers::LaunchWarningPopup("[ID_LINK_TEAM_ALREADY_LINKED]", "[ID_ASSIGN_CONTROLLER_NEUTRAL_HELP]", ButtonData);
				}
				else
				{
					TArray<FModalButtonInfo> ButtonData;
					ButtonData.Add(FModalButtonInfo("[ID_YES]", FWWUIModalDelegate::CreateLambda([=](APlayerController* OwningController) {TeamLinkDelegates.ConfirmLinkTeamDelegate.ExecuteIfBound(TeamDatabaseID); return true; })));
					ButtonData.Add(FModalButtonInfo("[ID_NO]"));
					SIFUIHelpers::LaunchWarningPopup("[ID_LINK_TEAM]", "[ID_ASSIGN_CONTROLLER_NEUTRAL_HELP]", ButtonData);
				}
			}
			else
			{
				TArray<FModalButtonInfo> ButtonData;
				ButtonData.Add(FModalButtonInfo("[ID_POPUP_OK]"));
				SIFUIHelpers::LaunchWarningPopup("[ID_LINK_CANT_TO_TEAM_CUSTOM]", "[ID_MAIN_MENU_HELP]", ButtonData);
			}
		}
		else if (!bIsSearch)
		{
			UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::TeamListScrollbox));

			if (pTeamListScrollbox)
			{
				if (pTeamListScrollbox->GetListLength() == 0)
				{
					return;
				}
			}
		}

		SetInputEnabled(false);
	}
	else
	{
		ProceedToTeamCreator();
	}
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCustomiseSelectTeam::OnSystemEvent(WWUINodeProperty& eventParams)
{
	FString EventName = eventParams.GetStringProperty("system_event");

	if (!GetInputEnabled())
	{
		return false;
	}

	// This system event triggers after we delete a player and the database has finished saving.
	if (EventName.Compare(GAME_DB_SAVE_OK_NAME) == 0 || EventName.Compare(GAME_DB_SAVE_FAIL_NAME) == 0)
	{
		SIFUIHelpers::ShowSavingOverlay(false);

		switch (CurrentSaveState)
		{

		case ECustomiseSelectTeamSaveState::NONE:
		{
			UE_LOG(LogTemp, Warning, TEXT("UWWUIScreenCustomiseSelectTeam::OnSystemEvent Game finished saving but no save state was set!"));
		}
		break;
		case ECustomiseSelectTeamSaveState::UPLOAD_TEAM:
		{
			FinishUploadTeam();
		}
		break;
		case ECustomiseSelectTeamSaveState::DELETE_TEAM:
		{
			FinishDeleteTeam();
		}
		break;
		default:
			break;
		}

		CurrentSaveState = ECustomiseSelectTeamSaveState::NONE;
	}

	return false;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::ProceedToTeamCreator()
{
	UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::TeamListScrollbox));
	bSelectedSearchTeam = false;

	// If it's search we want to get the search list instead.
	if (bIsSearch)
	{
		pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::SearchScrollBox));
		bSelectedSearchTeam = true;
	}

	if (pTeamListScrollbox)
	{
		// --Get index of competition && team so when we return we can select them
		// #rc3_legacy
		// TeamCreatorExitConfirm.is_return_to_edit = true;
		RUUIDatabaseQueryManager* pQueryManager = SIFUIHelpers::GetQueryManager();
		if (pQueryManager)
		{
			pQueryManager->LoadTeamData(TeamDatabaseID);

			RUDB_TEAM* pDatabaseTeam = pQueryManager->GetTeamData();

			SIFGameHelpers::GASetCustomTeamCinematicPrimaryStrip(pDatabaseTeam->GetStripId(0));
			SIFGameHelpers::GASetCustomTeamCinematicAlternateStrip(pDatabaseTeam->GetStripId(1));

			URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

			if (pRugbyGameInstance)
			{
				UWWUITeamDetailsScreenData* pTeamDetailsScreenData = NewObject<UWWUITeamDetailsScreenData>();

				if (pTeamDetailsScreenData)
				{
					pTeamDetailsScreenData->bIsEdit = true;
					pTeamDetailsScreenData->bIsDownload = false;
					pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::TeamDetails, pTeamDetailsScreenData);
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::SetInitialFocus(UWidget* pScrollBoxWidget)
{
	if (pScrollBoxWidget)
	{
		//Find the first element of the main menu and set focus
		UWWUIScrollBox* pCurrentScrollBox = Cast<UWWUIScrollBox>(pScrollBoxWidget);

		if (pCurrentScrollBox)
		{
			pCurrentScrollBox->FocusFirstListField(SIFApplication::GetApplication()->GetMasterPlayerController());
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::OnWindowEnter()
{
	UpdateTeamInfo();
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::OnBack(APlayerController* OwningPlayer)
{
	if (PC_KEYBOARD_FOCUS)
	{
		UnbindPCTextEntry();
		ExitTextEntry(OwningPlayer);
		return;
	}

	if (bIsSearch)
	{
		bIsSearch = false;
		SwitchSearch(bIsSearch);
		bSelectedSearchTeam = false;
		UpdateSelectedTeam(0);

		UpdateLegend();
		return;
	}
	else if (bIsTeamLink)
	{
		bSelectedSearchTeam = false;
	}

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		pRugbyGameInstance->DealMenuAction(SCREEN_CANCEL_FADE, Screens_UI::CustomiseSelectTeam);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::OnDelete(APlayerController* OwningPlayer)
{
	UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::TeamListScrollbox));

	if (pTeamListScrollbox)
	{
		if (SIFGameHelpers::GAGetTeamIsCustom(TeamDatabaseID) && pTeamListScrollbox->GetListLength() > 0)
		{
			int32 compdef_id = SIFGameHelpers::GACustomiseCanDeleteTeam(TeamDatabaseID);

			if (compdef_id == 0)
			{
				// --This is more for safety.It shouldn't really hit.
				if (SIFGameHelpers::GACheckTeamLink(TeamDatabaseID))
				{
					TArray<FModalButtonInfo> ButtonData;

					FWWUIModalDelegate SaveDelegate;
					SaveDelegate.BindUObject(this, &UWWUIScreenCustomiseSelectTeam::DeleteLinkOptionOnClick);
					ButtonData.Add(FModalButtonInfo("[ID_YES]", SaveDelegate));
					ButtonData.Add(FModalButtonInfo("[ID_NO]"));

					SIFUIHelpers::LaunchWarningPopup("[ID_LINK_DELETE_TEAM]", "[ID_ASSIGN_CONTROLLER_NEUTRAL_HELP]", ButtonData);
				}
				else
				{
					//CustomiseTeamDelete.team_id = CustomiseTeamSelectTeam.team_db_id
					TArray<FModalButtonInfo> ButtonData;

					FWWUIModalDelegate SaveDelegate;
					SaveDelegate.BindUObject(this, &UWWUIScreenCustomiseSelectTeam::DeleteOptionOnClick);
					ButtonData.Add(FModalButtonInfo("[ID_YES]", SaveDelegate));
					ButtonData.Add(FModalButtonInfo("[ID_NO]"));

					SIFUIHelpers::LaunchWarningPopup("[ID_CUSTOMISE_TEAM_DELETE_POPUP]", "[ID_ASSIGN_CONTROLLER_NEUTRAL_HELP]", ButtonData);
				}
			}
			else
			{
				if (compdef_id > 0)
				{
					FString text = "[ID_CUSTOMISE_TEAM_CANT_DELETE_INCOMP] \n" + SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetCompetitionDefinitionName(compdef_id));
					SIFUIHelpers::LaunchWarningPopup(text, "", TArray<FModalButtonInfo>());
				}
				else
				{
					SIFUIHelpers::LaunchWarningPopup("[ID_CUSTOMISE_TEAM_CANT_DELETE]", "", TArray<FModalButtonInfo>());
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::OnUploadPressed(APlayerController* OwningPlayer)
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		if (TeamDatabaseID != 0 && SIFGameHelpers::GAGetTeamIsCustom(TeamDatabaseID))
		{
			pRugbyGameInstance->CreateNewOnlinePrivilegeTask(EUserPrivileges::CanUseUserGeneratedContent, "LaunchFanHubSignInCheck", this, false, false);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::OnRightTrigger(APlayerController* OwningPlayer)
{
	if (!bIsSearch)
	{
		IncrementFilterCompetition(+1);

		m_triggerAxisHandle = UWWUIFunctionLibrary::OnTimer(TRIGGER_DELAY_TIME, FTimerDelegate::CreateUObject(this, &UWWUIScreenCustomiseSelectTeam::ResetAxisTrigger), false);
		m_canTriggerAxis = false;
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::OnLeftTrigger(APlayerController* OwningPlayer)
{
	if (!bIsSearch)
	{
		IncrementFilterCompetition(-1);

		m_triggerAxisHandle = UWWUIFunctionLibrary::OnTimer(TRIGGER_DELAY_TIME, FTimerDelegate::CreateUObject(this, &UWWUIScreenCustomiseSelectTeam::ResetAxisTrigger), false);
		m_canTriggerAxis = false;
	}
}

void UWWUIScreenCustomiseSelectTeam::OnRotationInput(float AxisValue, APlayerController* OwningPlayer)
{
	if (m_canTriggerAxis)
	{
		if (AxisValue < 0.0f)
		{
			OnLeftTrigger(OwningPlayer);
		}
		else if (AxisValue > 0.0f)
		{
			OnRightTrigger(OwningPlayer);
		}
	}
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCustomiseSelectTeam::DeleteOptionOnClick(APlayerController* OwningPlayer)
{
	CurrentSaveState = ECustomiseSelectTeamSaveState::DELETE_TEAM;

	TeamDeleteID = TeamDatabaseID;

	UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::TeamListScrollbox));

	if (pTeamListScrollbox)
	{
		int selectedIndex = pTeamListScrollbox->GetSelectedIndex();

		SIFGameHelpers::GACustomiseDeleteTeam(TeamDatabaseID); //This also saves the custom database.


		if (selectedIndex > 0)
		{
			selectedIndex--;
		}

		UpdateSelectedTeam(selectedIndex);

		UpdateTeamInfo();
	}

	return true;
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCustomiseSelectTeam::DeleteLinkOptionOnClick(APlayerController* OwningPlayer)
{
	SIFGameHelpers::GAResetEditedCustomData(RCDT_TEAM, TeamDatabaseID);
	return true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::IncrementFilterCompetition(int32 Direction)
{
	UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::TeamListScrollbox));

	if (pTeamListScrollbox)
	{
		UWWUIPopulatorTeamSelectList* pTeamSelectListPopulator = Cast<UWWUIPopulatorTeamSelectList>(pTeamListScrollbox->GetPopulator());

		if (pTeamSelectListPopulator)
		{
			FString NewCompName = pTeamSelectListPopulator->IncrementFilterCompetition(Direction);

			UWidget* pCompetitionCategorySelectorWidget = FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::CompetitionListSwitcher);

			if (pCompetitionCategorySelectorWidget)
			{
				UTextBlock* pTitleText = Cast<UTextBlock>(FindChildOfTemplateWidget(pCompetitionCategorySelectorWidget, WWUIScreenCustomiseSelectTeam_UI::TextCategoryName));

				if (pTitleText)
				{
					SetWidgetText(pTitleText, FText::FromString(UWWUITranslationManager::Translate(NewCompName)));
				}

				FString NumberText = FString::FromInt(pTeamSelectListPopulator->GetCurrentCompetitionIndex() + 1) + " / " + FString::FromInt(pTeamSelectListPopulator->GetCompetitionCount());

				UTextBlock* pNumCategoriesText = Cast<UTextBlock>(FindChildOfTemplateWidget(pCompetitionCategorySelectorWidget, WWUIScreenCustomiseSelectTeam_UI::TextNumCategories));

				if (pNumCategoriesText)
				{
					SetWidgetText(pNumCategoriesText, FText::FromString(NumberText));
				}
			}
			
			//UpdateCustomLimitText();
		}


		pTeamListScrollbox->PopulateAndRefresh();

		SetInitialFocus(pTeamListScrollbox);

		UpdateSelectedTeam(pTeamListScrollbox->GetSelectedIndex());
		UpdateTeamInfo();
	}

	//SRA: If we're in the custom team lists, show the TeamLimitBorder element
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::UpdateSelectedTeam(int32 NewIdx)
{
	UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::TeamListScrollbox));

	// If it's search we want to get the search list instead.
	if (bIsSearch)
	{
		pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::SearchScrollBox));
	}

	if (pTeamListScrollbox)
	{
		UWWUIListField* pSelectedListField = pTeamListScrollbox->GetListField(NewIdx);

		if (pSelectedListField)
		{
			// Grab the the team db id set up when the list was populated.
			TeamDatabaseID = pSelectedListField->GetIntProperty("team_db_id");
		}
	}

	if (TeamDatabaseID == TeamDeleteID)
	{
		TeamDatabaseID = 0;
	}

	UE_LOG(LogTemp, Display, TEXT("%s"), *(FString("team id && delete team id = ") + FString::FromInt(TeamDatabaseID) + " , " + FString::FromInt(TeamDeleteID)));

	UpdateLegend();
	UpdateCustomLimitText();
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::UpdateTeamInfo()
{
	if (TeamDatabaseID == 0)
	{
		return;
	}

	MabString LogoPath = SIFGameHelpers::GAGetTeamLogoAssetPath(TeamDatabaseID);

	UImage* pLogoImage = Cast<UImage>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::ImageTeamLogo));

	if (pLogoImage)
	{
		// The opposition is still TBD
		UTexture2D* pTexture = nullptr;

		FString name = FString(LogoPath.c_str());
		pTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *name));

		if (pTexture)
		{
			pLogoImage->SetBrushFromTexture(pTexture, true);
			pLogoImage->SetVisibility(ESlateVisibility::Visible);
		}
	}

	UImage* logoBackground = Cast<UImage>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::teamBkgd));
	SIFUIHelpers::SetImageColourFromTeamColour(logoBackground, TeamDatabaseID);

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		RUDB_TEAM db_team;
		RUGameDatabaseManager* database_manager = pRugbyGameInstance->GetGameDatabaseManager();

		if (database_manager)
		{
			database_manager->LoadData(db_team, TeamDatabaseID);
			MabColour team_colour;
			db_team.GetPrimaryColour(team_colour);

			UpdateTeamRating(db_team.GetNormaliseRanking() * 100.0f);

			UpdateTeamCreator(db_team);
		}
	}

	//UpdateCustomLimitText();

	// #rc3_legacy
	//UpdateTeamRating(FMath::RoundToInt(pNewTeamData->ranking));
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::SwitchSearch(bool bInIsSearch)
{
	UWidgetSwitcher* pWidgetSwitcherScrollBox = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::WidgetSwitcherScrollBox));
	// #CareerNeedFix
	UWidgetSwitcher* pWidgetSwitcherSelector = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::WidgetSwitcherScrollBox));

	if (pWidgetSwitcherScrollBox && pWidgetSwitcherSelector)
	{
		if (!bIsSearch)
		{
			pWidgetSwitcherScrollBox->SetActiveWidgetIndex((int)ETeamListType::TEAM_LIST);
			pWidgetSwitcherSelector->SetActiveWidgetIndex((int)ETeamListType::TEAM_LIST);

			// update filter text
			IncrementFilterCompetition(0);
		}
		else
		{
			pWidgetSwitcherScrollBox->SetActiveWidgetIndex((int)ETeamListType::SEARCH);
			pWidgetSwitcherSelector->SetActiveWidgetIndex((int)ETeamListType::SEARCH);

			// set filter text to search
			UWidget* pCompetitionCategorySelectorWidget = FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::CompetitionListSwitcher);
			if (pCompetitionCategorySelectorWidget)
			{
				UTextBlock* pTitleText = Cast<UTextBlock>(FindChildOfTemplateWidget(pCompetitionCategorySelectorWidget, WWUIScreenCustomiseSelectTeam_UI::TextCategoryName));
				if (pTitleText)
				{
					SetWidgetText(pTitleText, FText::FromString(UWWUITranslationManager::Translate("[ID_SEARCH]")));
				}

				UTextBlock* pNumCategoriesText = Cast<UTextBlock>(FindChildOfTemplateWidget(pCompetitionCategorySelectorWidget, WWUIScreenCustomiseSelectTeam_UI::TextNumCategories));
				if (pNumCategoriesText)
				{
					SetWidgetText(pNumCategoriesText, FText::FromString("1 / 1"));
				}
			}
		}

		SetInitialFocus(pWidgetSwitcherScrollBox->GetActiveWidget());
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::LaunchFanHubSignInCheck()
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		UWWUISignInScreenData* pSignInScreenData = NewObject<UWWUISignInScreenData>();

		if (pSignInScreenData)
		{
			pSignInScreenData->SignInFor = ESignInFor::GENERIC;
			pSignInScreenData->OnClosedDelegate.BindUObject(this, &UWWUIScreenCustomiseSelectTeam::OnFanHubSignInCheckComplete);
			pRugbyGameInstance->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::SignIn, pSignInScreenData);
		}
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::OnFanHubSignInCheckComplete(bool bSuccess)
{
	if (bSuccess)
	{
		LaunchUploadPopup();
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::LaunchUploadPopup()
{
	TArray<FModalButtonInfo> ButtonData;
	FString PopupString = "[ID_UPLOAD_TEAM_POPUP_TITLE]";
	FString LegendString = "[ID_ASSIGN_CONTROLLER_NEUTRAL_HELP]";

	FWWUIModalDelegate UploadDelegate;
	UploadDelegate.BindLambda([this](APlayerController* OwningController) {UploadTeam(false); return true; });
	ButtonData.Add(FModalButtonInfo("[ID_UPLOAD_PLAYER]", UploadDelegate));

	FWWUIModalDelegate UploadAndShareDelegate;
	UploadAndShareDelegate.BindLambda([this](APlayerController* OwningController) {UploadTeam(true); return true; });
	ButtonData.Add(FModalButtonInfo("[ID_UPLOAD_PLAYER_SHARE]", UploadAndShareDelegate));


	ButtonData.Add(FModalButtonInfo("[ID_NO]"));

	SIFUIHelpers::LaunchWarningPopup(PopupString, LegendString, ButtonData);
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::UploadTeam(bool bShare)
{
	CurrentSaveState = ECustomiseSelectTeamSaveState::UPLOAD_TEAM;
#ifdef ENABLE_ANALYTICS
	if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
	{
		pRugbyGameInstance->GetPlayerAnalyticsData().AddFanHubTeamUploaded();
	}
#endif
#if defined (FANHUB_ENABLED)
	UE_LOG(LogTemp, Display, TEXT("UWWUIScreenCustomiseSelectTeam::UploadTeam Starting team upload - Share team: %d"), bShare);
	SIFGameHelpers::GAAsyncUploadTeam(TeamDatabaseID, bShare, false);
#endif
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::UpdateLegend()
{
	UWWUIRichTextBlockWithTranslate* pLegendRichText = Cast<UWWUIRichTextBlockWithTranslate>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::LegendText));

	FString LegendString = "";

	if (TeamDatabaseID == TeamDeleteID)
	{
		TeamDatabaseID = 0;
	}

	UE_LOG(LogTemp, Display, TEXT("%s"), *(FString("team id && delete team id = ") + FString::FromInt(TeamDatabaseID) + " , " + FString::FromInt(TeamDeleteID)));

	FString SignedInString = "TRUE";

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	// #rc3_legacy
	if (pRugbyGameInstance && !SIFGameHelpers::GAIsConsole())
	{
		UWWRugbyFanHubService* pFanHubService = Cast<UWWRugbyFanHubService>(pRugbyGameInstance->GetFanHubService());

		if (pFanHubService && !pFanHubService->IsActive())
		{
			SignedInString = "FALSE";
		}
	}

	if (PC_KEYBOARD_FOCUS)
	{
		LegendString = "[ID_MATCH_SETTINGS_HELP]";
	}
	else if (bIsSearch)
	{
		if (SIFGameHelpers::GAGetTeamIsCustom(TeamDatabaseID))
		{
			LegendString = "[ID_SELECT_TEAM_STANDALONE_SEARCH_CUSTOM_" + SignedInString + "]";
		}
		else
		{
			LegendString = "[ID_SELECT_TEAM_STANDALONE_SEARCH_" + SignedInString + "]";
		}
	}
	else
	{
		if (SIFGameHelpers::GAGetTeamIsCustom(TeamDatabaseID))
		{
			LegendString = "[ID_SELECT_TEAM_STANDALONE_SIGNED_IN_" + SignedInString + "]";
		}
		else
		{
			LegendString = "[ID_SELECT_TEAM_OPTION_HELP_" + SignedInString + "]";
		}
	}

	if (bIsTeamLink) 
	{
		// this is in StringtableProjEnglish data table, but it contains wrong legend icons. so we move it here.
		// LegendString = "[ID_LINK_RECRUIT_HELP_TIP]"; 
		LegendString = "[INP_GetAction(RU_UI_ACTION_SELECT)] SEARCH [INP_GetAction(RU_UI_ACTION_ACCEPT)] LINK [INP_GetAction(RU_UI_ACTION_RETURN)] BACK";
	}

	if (pLegendRichText)
	{
		pLegendRichText->SetText(LegendString);
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::UpdateTeamRating(float NewRating)
{
	//Update ranking
	FString rankingString = FString::Printf(TEXT("%.2f"), NewRating);
	SetWidgetText(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::TextRating), FText::FromString(rankingString));
}

//===============================================================================
//===============================================================================
void UWWUIScreenCustomiseSelectTeam::UpdateTeamCreator(RUDB_TEAM &db_team)
{
	UWidget* pCreatorBox = FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::HorizontalBoxCreator);
	if (pCreatorBox)
	{
#ifdef SHOW_UGC_CREATOR
		UTextBlock* pCreatedByText = Cast<UTextBlock>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::TextCreator));
		FString creatorName = "";
		FString uploaderName = "";
		
		if (db_team.IsCustom() && SIFGameHelpers::GAGetTeamDownloadUser(db_team.GetDbId()) != "")
		{
			FString downloadInfo = SIFGameHelpers::GAConvertMabStringToFString(SIFGameHelpers::GAGetTeamDownloadUser(db_team.GetDbId()));
			creatorName = downloadInfo;

			downloadInfo.Split(",", &creatorName, &uploaderName);
		}

		if(pCreatedByText && creatorName != "")
		{
			FString CreatedByString = "[ID_CREATED_BY]: " + creatorName + "\n[ID_UPLOADED_BY]: " + uploaderName;
			SetWidgetText(pCreatedByText, FText::FromString(UWWUITranslationManager::Translate(CreatedByString)));
			pCreatorBox->SetVisibility(ESlateVisibility::Visible);			
		}
		else
#endif
		{
			pCreatorBox->SetVisibility(ESlateVisibility::Collapsed);
		}
	}
}

//===============================================================================
//===============================================================================
void UWWUIScreenCustomiseSelectTeam::UpdateCustomLimitText()
{
	if (m_pCustomLimitBox)
	{
		m_pCustomLimitBox->SetVisibility(ESlateVisibility::Collapsed);
		
#ifdef SHOW_CUSTOM_CONTENT_LIMITS
		UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::TeamListScrollbox));
		if (pTeamListScrollbox)
		{
			UWWUIPopulatorTeamSelectList* pTeamSelectListPopulator = Cast<UWWUIPopulatorTeamSelectList>(pTeamListScrollbox->GetPopulator());
			if (pTeamSelectListPopulator)
			{
				if (pTeamSelectListPopulator->GetIsCustomTeamList())
				{
					m_pCustomLimitBox->SetVisibility(ESlateVisibility::Visible);

					UTextBlock* teamLimitText = Cast<UTextBlock>(UWWUIFunctionLibrary::FindChildWidget(this, WWUIScreenCustomiseSelectTeam_UI::CustomLimitText));
					if (teamLimitText)
					{
						int customKits = SIFGameHelpers::GAGetDBCustomTeamCount();

						FString prefix_str = UWWUITranslationManager::Translate(FString("[ID_CUSTOM_CONTENT_COUNTER]"));
						FString contentType = UWWUITranslationManager::Translate(FString("[ID_CUSTOMISE_TEAM_TITLE]")); //[ID_COMP_SETUP_COMPETITION]
						FString result = FString::Printf(*prefix_str, *contentType, customKits, CUSTOM_TEAM_DB_LIMIT);

						UWWUIFunctionLibrary::SetText(teamLimitText, result);
					}
				}
			}
		}
#endif
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::ResetAxisTrigger()
{
	if (m_triggerAxisHandle.IsValid())
	{
		UWWUIFunctionLibrary::StopTimer(m_triggerAxisHandle);
		m_canTriggerAxis = true;
	}
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::FinishUploadTeam()
{
	FString ModalTitle = "[ID_UPLOAD_SUCCESS]";

	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		UWWRugbyFanHubService* pFanHub = Cast<UWWRugbyFanHubService>(pRugbyGameInstance->GetFanHubService());

		if (pFanHub)
		{
			if (pFanHub->GetLastUploadWasUpdate())
			{
				ModalTitle = "[ID_UPDATE_TEAM_SUCCESS]";
			}
		}
	}

	SIFUIHelpers::LaunchServerResponsePopup(ModalTitle);

	
	if (pRugbyGameInstance)
	{
		RUDB_TEAM db_team;
		RUGameDatabaseManager* database_manager = pRugbyGameInstance->GetGameDatabaseManager();

		if (database_manager)
		{
			database_manager->LoadData(db_team, TeamDatabaseID);
			UpdateTeamCreator(db_team);
		}
	}
	
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::FinishDeleteTeam()
{
	UWWUIScrollBox* pTeamListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::TeamListScrollbox));

	if (pTeamListScrollbox)
	{
		pTeamListScrollbox->PopulateAndRefresh();

		if (!bIsSearch)
		{
			pTeamListScrollbox->SetSelectedIndex(FMath::Max(0, CachedSelectedIndex - 1));
		}
	}

	if (bIsSearch)
	{
		UWWUIScrollBox* pSearchListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::SearchScrollBox));

		if (pSearchListScrollbox)
		{
			pSearchListScrollbox->PopulateAndRefresh();

			pSearchListScrollbox->SetSelectedIndex(FMath::Max(0, CachedSelectedIndex - 1));
		}
	}

	UpdateSelectedTeam(FMath::Max(0, CachedSelectedIndex));

	UpdateTeamInfo();
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::ApplyEnteredText(FString _String)
{
	_String = _String.TrimStart();
	// --  validate the input
	MabString NewText = TCHAR_TO_UTF8(*_String);

	SearchTeamString = SIFGameHelpers::GAConvertMabStringToFString(NewText);
	UWWUIScrollBox* pSearchListScrollbox = Cast<UWWUIScrollBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::SearchScrollBox));

	if (pSearchListScrollbox)
	{
		UWWUIPopulatorTeamSelectList* pSearchSelectListPopulator = Cast<UWWUIPopulatorTeamSelectList>(pSearchListScrollbox->GetPopulator());

		if (pSearchSelectListPopulator)
		{
			pSearchSelectListPopulator->SetSearchTeamName(SearchTeamString);
			pSearchSelectListPopulator->SetTeamLimit(TEAM_SELECT_SEARCH);
		}

		pSearchListScrollbox->PopulateAndRefresh();

		if (pSearchListScrollbox->GetListLength() == 0)
		{
			SIFUIHelpers::ListenToAllControllers();
			UnbindPCTextEntry();
			UnbindConsoleTextEntry();
			ExitTextEntry(nullptr);

			SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::NO_RESULTS, FWWUIModalDelegate());
			return;
		}

		bIsSearch = true;
		SwitchSearch(bIsSearch);
	}

	ExitTextEntry(nullptr);
}

void UWWUIScreenCustomiseSelectTeam::OnPCConfirmTextEntry(const FText& _Text, ETextCommit::Type _CommitMethod)
{
	if (_CommitMethod == ETextCommit::OnCleared)
	{
		//< Return out, allowing the screen to deal with this on key UP. >
		return;
	}

	UnbindPCTextEntry();
	ApplyEnteredText(_Text.ToString());
}

void UWWUIScreenCustomiseSelectTeam::OnConsoleConfirmTextEntry(const FText& _Text, ETextCommit::Type _CommitMethod)
{
	if (_CommitMethod == ETextCommit::OnCleared)
	{
		//< Exit text entry. >
		UnbindConsoleTextEntry();
		ExitTextEntry(nullptr);
		return;
	}

	UnbindConsoleTextEntry();
	ApplyEnteredText(_Text.ToString());
}

void UWWUIScreenCustomiseSelectTeam::HandleTextChangedFilterResult(FString _String, EWWUITextFilterResult _FilterResult)
{
	HandleTextFilterResult(_String, _FilterResult);
}

void UWWUIScreenCustomiseSelectTeam::HandleTextCommittedFilterResult(FString _String, EWWUITextFilterResult _FilterResult, ETextCommit::Type _CommitMethod)
{
	if (_CommitMethod == ETextCommit::OnCleared)
	{
#if PLATFORM_WINDOWS
		//< Return out, allowing the screen to deal with this on key UP. >
#else
		//< Exit text entry. >
		UnbindConsoleTextEntry();
		UnbindPCTextEntry();
		ExitTextEntry(nullptr);
#endif
		return;
	}

	HandleTextFilterResult(_String, _FilterResult);
}

void UWWUIScreenCustomiseSelectTeam::HandleTextFilterResult(FString _String, EWWUITextFilterResult _FilterResult)
{
	SIFUIHelpers::ListenToAllControllers();
	UnbindPCTextEntry();
	UnbindConsoleTextEntry();
	ExitTextEntry(nullptr);

	switch (_FilterResult)
	{
#if !PLATFORM_WINDOWS //< PS4, XBONE & SWITCH >
	case EWWUITextFilterResult::EMPTY:
	case EWWUITextFilterResult::PRECEDED_CHAR_LIMIT:
	{
		SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::INVALID_STRING_LENGTH, FWWUIModalDelegate());
		break;
	}
	case EWWUITextFilterResult::PROFANITY:
	{
		FString PopupString = "[ID_PROFANITY_ERROR]";
		FString LegendString = "[ID_COPYRIGHT_HELP]";
		SIFUIHelpers::LaunchWarningPopup(PopupString, LegendString, FWWUIModalDelegate());
		break;
	}
	default: SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::INVALID_NAME, FWWUIModalDelegate());
#else //< PC >
	case EWWUITextFilterResult::EMPTY:
	case EWWUITextFilterResult::PRECEDED_CHAR_LIMIT:
	{
		SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::INVALID_STRING_LENGTH, FWWUIModalDelegate());
		break;
	}
	case EWWUITextFilterResult::PROFANITY:
	{
		FString PopupString = "[ID_PROFANITY_ERROR]";
		FString LegendString = "[ID_COPYRIGHT_HELP]";
		SIFUIHelpers::LaunchWarningPopup(PopupString, LegendString, FWWUIModalDelegate());
		break;
	}
	default: SIFUIHelpers::LaunchTextEntryErrorPopup(ETextEntryErrorType::INVALID_NAME, FWWUIModalDelegate());
#endif
	}
}

//===============================================================================
//===============================================================================

bool UWWUIScreenCustomiseSelectTeam::ExitTextEntry(APlayerController* OwningPlayer)
{
	PC_KEYBOARD_FOCUS = false;
	SIFAudioHelpers::PlayInputSoundEvent("event:/ui/help_tips/close_popup");
	SIFUIHelpers::ListenToAllControllers();

#if PLATFORM_WINDOWS
	//< Hide search box for PC. >
	if (UWidget* pTextEntryWidget = FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::BorderLinesSearchBox))
		pTextEntryWidget->SetRenderOpacity(0.0f);
#endif

	if(UWidgetSwitcher* pWidgetSwitcherScrollBox = Cast<UWidgetSwitcher>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::WidgetSwitcherScrollBox)))	
		SetInitialFocus(pWidgetSwitcherScrollBox->GetWidgetAtIndex(bIsSearch ? (int)ETeamListType::SEARCH : (int)ETeamListType::TEAM_LIST));

	UpdateLegend();
	return true;
}

bool UWWUIScreenCustomiseSelectTeam::ReopenTextEntry(APlayerController* OwningPlayer)
{
	PC_KEYBOARD_FOCUS = false;

	OnOpenTextEntry(SIFApplication::GetApplication()->GetMasterPlayerController());
	return true;
}

//===============================================================================
//===============================================================================

void UWWUIScreenCustomiseSelectTeam::OnOpenTextEntry(APlayerController* OwningPlayer)
{
#if PLATFORM_WINDOWS //< PC >
	if (!PC_KEYBOARD_FOCUS) {

		//< Display Text Box >
		if (UWidget* pTextEntryWidget = FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::BorderLinesSearchBox))
			pTextEntryWidget->SetRenderOpacity(1.0f);
#endif

		/// Play popup audio.
		SIFAudioHelpers::PlayInputSoundEvent("event:/ui/help_tips/open_popup");

		//< Apply text & focus. >
		if (UWWUIEditableTextBox* pEnterTextBox = Cast<UWWUIEditableTextBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::EditableTextBoxSearchTextEntry)))
		{
			pEnterTextBox->SetText(FText::FromString(""));
			if (pEnterTextBox->EnterTextEditMode(OwningPlayer))
			{
				//< Setup callbacks for all platforms. >
#if !PLATFORM_WINDOWS //< PS4, XBONE & SWITCH >
				BindConsoleTextEntry();
				SIFUIHelpers::ListenToNoControllers();
#else //< PC >
				BindPCTextEntry();
				PC_KEYBOARD_FOCUS = true;
#endif
			}
			}

#if PLATFORM_WINDOWS //< PC >
	}
#endif

	UpdateLegend();
}

void UWWUIScreenCustomiseSelectTeam::BindPCTextEntry()
{
#if PLATFORM_WINDOWS //< PC >
	if (UWWUIEditableTextBox* pEnterTextBox = Cast<UWWUIEditableTextBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::EditableTextBoxSearchTextEntry)))
	{
		if (!pEnterTextBox->OnTextCommitted.Contains(this, FName("OnPCConfirmTextEntry")))
		{
			pEnterTextBox->OnTextCommitted.AddDynamic(this, &UWWUIScreenCustomiseSelectTeam::OnPCConfirmTextEntry);
		}
		if (!pEnterTextBox->GetTextCommittedFilterCallback().IsBound())
		{
			pEnterTextBox->GetTextCommittedFilterCallback().BindUObject(this, &UWWUIScreenCustomiseSelectTeam::HandleTextCommittedFilterResult);
		}
	}
#endif
}

void UWWUIScreenCustomiseSelectTeam::UnbindPCTextEntry()
{
#if PLATFORM_WINDOWS //< PC >
	if (UWWUIEditableTextBox* pEnterTextBox = Cast<UWWUIEditableTextBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::EditableTextBoxSearchTextEntry)))
	{
		if (pEnterTextBox->OnTextCommitted.Contains(this, FName("OnPCConfirmTextEntry")))
		{
			pEnterTextBox->OnTextCommitted.RemoveDynamic(this, &UWWUIScreenCustomiseSelectTeam::OnPCConfirmTextEntry);
		}
		if (pEnterTextBox->GetTextCommittedFilterCallback().IsBound())
		{
			pEnterTextBox->GetTextCommittedFilterCallback().Unbind();
		}
	}
#endif
}

void UWWUIScreenCustomiseSelectTeam::BindConsoleTextEntry()
{
#if !PLATFORM_WINDOWS //< PS4, XBONE & SWITCH >
	if (UWWUIEditableTextBox* pEnterTextBox = Cast<UWWUIEditableTextBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::EditableTextBoxSearchTextEntry)))
	{
		//< Setup callbacks for all platforms. >
		if (!pEnterTextBox->OnTextCommitted.Contains(this, FName("OnConsoleConfirmTextEntry")))
		{
			pEnterTextBox->OnTextCommitted.AddDynamic(this, &UWWUIScreenCustomiseSelectTeam::OnConsoleConfirmTextEntry);
		}
		if (!pEnterTextBox->GetTextCommittedFilterCallback().IsBound())
		{
			pEnterTextBox->GetTextCommittedFilterCallback().BindUObject(this, &UWWUIScreenCustomiseSelectTeam::HandleTextCommittedFilterResult);
		}
	}
#endif
}

void UWWUIScreenCustomiseSelectTeam::UnbindConsoleTextEntry()
{
	//Controller disconencted while keyboard was open would change focusing causing text entry to unbind
	//this is to prevent it from unbinding so when our controller comes back the text input still works
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();
	if (pRugbyGameInstance)
	{
		if (pRugbyGameInstance->GetIsVirtualKeyboardShowing())
		{
			return;
		}
	}

#if !PLATFORM_WINDOWS //< PS4, XBONE & SWITCH >
	if (UWWUIEditableTextBox* pEnterTextBox = Cast<UWWUIEditableTextBox>(FindChildWidget(WWUIScreenCustomiseSelectTeam_UI::EditableTextBoxSearchTextEntry)))
	{
		if (pEnterTextBox->OnTextCommitted.Contains(this, FName("OnConsoleConfirmTextEntry")))
		{
			pEnterTextBox->OnTextCommitted.RemoveDynamic(this, &UWWUIScreenCustomiseSelectTeam::OnConsoleConfirmTextEntry);
		}
		if (pEnterTextBox->GetTextCommittedFilterCallback().IsBound())
		{
			pEnterTextBox->GetTextCommittedFilterCallback().Unbind();
		}
	}
#endif
}

//===============================================================================
//===============================================================================
void UWWUIScreenCustomiseSelectTeam::HandleUGCUsernameCheckComplete(bool val)
{
	URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication();

	if (pRugbyGameInstance)
	{
		RUDB_TEAM db_team;
		RUGameDatabaseManager* database_manager = pRugbyGameInstance->GetGameDatabaseManager();

		if (database_manager)
		{
			database_manager->LoadData(db_team, TeamDatabaseID);
			UpdateTeamCreator(db_team);
		}
	}
}
