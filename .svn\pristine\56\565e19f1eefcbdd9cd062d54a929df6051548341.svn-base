/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/RUGameSettings.h"

#include "Mab/Utility/MabStringHelper.h"

#include "Databases/RUGameDatabaseManager.h"
#include "Match/RugbyUnion/CompetitionMode/RUActiveCompetition.h"
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#include "Match/RugbyUnion/RUDatabaseConstants.h"
#include "Match/RugbyUnion/RUStadiumManager.h"
#include "Match/RugbyUnion/Rules/RURuleTriggerEnum.h"
#include "Match/SIFGameWorld.h"
#include "RULimits.h"

//#rc3_legacy_include #include "SIFGameHelpers.h"
#include "Networking/SIFOnlineConstants.h"

#include "RugbyGameInstance.h"
#include "Utility/consoleVars.h"
#include "Utility/Helpers/SIFGameHelpers.h"
#include "RUGameSettingsTeamSettings.h"

static const float AWAY_MODIFIER = 0.25f;

char FRugbyStadiumSettings::stadium_abbr_cstr[];

FRugbyGameSettings::FRugbyGameSettings()
:
#ifdef ENABLE_PRO_MODE
is_be_a_pro(false),	// Pro stuff here for now until i work out a better data structure, and probably better place in the code for it all to go.
pro_jersey_number(-1),	// Most likely in profile? Depends on how we handle things.
#endif
gameMode(GAME_MODE_RU13),
is_grand_final(false),
game_type(GAME_STANDARD),
extra_time_enabled(false),
num_interchanges(NUM_SUBSTITUTIONS_ALLOWED),
game_length(0),
initial_kickoff_team(SIDE_A),
initial_play_direction(),
enabled_triggers(static_cast<MabUInt64>(RURT_MAINGAMERULES)),
change_favourite_team(false),
//
custom_rule_forward_pass(true),
custom_rule_offside(true),
custom_rule_sendoffs_enabled(true),
custom_rule_sinbins_enabled(true),
custom_rule_fatigue_enabled(true),
custom_rule_offloads(true),
custom_rule_injuries(true),
custom_rule_video_ref(true),
custom_rule_slo_mo(true),
custom_rule_knock_ons(true),
//
network_game(false),
network_replays(false),
network_cutscenes(false),
private_match(true),
network_balanced_stats(true),
//
slider_injury_frequency(0.0f),
slider_high_tackle_frequency(0.0f),
slider_offload_frequency(0.0f),
slider_offload_success_rate(1.0f), // changed default value to 1 to make sure passes work while profile saves aren't working.
slider_pass_success_rate(1.0f),
slider_break_tackle_ability(0.0f),
slider_tackle_ability(0.0f),
slider_attacking_urgency(0.0f),
slider_defensive_urgency(0.0f),
slider_intercept_frequency(0.0f),
slider_offload_chance(0.0f),
slider_ruck_tackle_bonus(0.0f)
{

	//
}

FRugbyWeatherSettings::FRugbyWeatherSettings()
: lighting_conditions(LIGHTING_CONDITIONS_CLEAR)
, wind_strength(0.0f)
, wind_direction(FVector::ZeroVector)
, raining(false)
, time_of_day(TIME_DAY)
, overcast_selected(false)
{}

FRugbyCrowdSettings::FRugbyCrowdSettings()
: crowd_size(CROWD_SIZE_NONE)
, crowd_density(0)
{}

FRugbyStadiumSettings::FRugbyStadiumSettings()
: stadium_abbr(TEXT("crok")) //Nick WWS change from "eden" to "crok"
, field_signage(FIELD_NRL_PREMIERSHIP)
{}

FRugbyHumanPlayerSettings::FRugbyHumanPlayerSettings()
: team(-1)
, player_id(-1)
, controller_id(-1)
, peer_id(-1)
{}

FRugbyCompetitionSettings::FRugbyCompetitionSettings()
: competition_definition_id(0)
, team_id(0)
{}

URugbyGameWorldSettings::URugbyGameWorldSettings( /*RL3Database* database*/ )
: game_settings(),
  game_limits(this),
  weather_settings(),
  crowd_settings(),
  stadium_settings(),
  team_settings(),
  human_player_settings(),
  difficulty(DIF_NORMAL),
  game_law(GAME_LAW_NORMAL),
  substitution_mode(SUB_MANUAL),
  num_active_teams(0),
  initial_random_seed(0)
{
	MABLOGDEBUG("URugbyGameWorldSettings::URugbyGameWorldSettings init seed = %u",initial_random_seed);
}

void URugbyGameWorldSettings::printState() const
{
#if defined _DEBUG || wwDEBUG_JG
	MABLOGDEBUG("GSET: mode=%d pro=%d jump=%d final=%d type=%d len=%d extra=%d interch=%d kick=%d toss=%d trig=%u chtm=%d",
		game_settings.GetGameMode(),game_settings.GetIsAProMode(),0/*game_settings.pro_jersey_number*/,
		game_settings.is_grand_final,game_settings.game_type,game_settings.game_length,game_settings.extra_time_enabled,
		game_settings.num_interchanges,game_settings.initial_kickoff_team,
		game_settings.initial_kickoff_team_extra_time_coin_toss,(unsigned int)game_settings.enabled_triggers,
		game_settings.change_favourite_team);
	MABLOGDEBUG("fwd=%d off=%d sendoff=%d sin=%d tred=%d offl=%d inj=%d vref=%d slo=%d knock=%d nw=%d repl=%d cut=%d pvt=%d",
		game_settings.custom_rule_forward_pass,game_settings.custom_rule_offside,game_settings.custom_rule_sendoffs_enabled,
		game_settings.custom_rule_sinbins_enabled,game_settings.custom_rule_fatigue_enabled,game_settings.custom_rule_offloads,
		game_settings.custom_rule_injuries,game_settings.custom_rule_video_ref,game_settings.custom_rule_slo_mo,
		game_settings.custom_rule_knock_ons,game_settings.network_game,game_settings.network_replays,
		game_settings.network_cutscenes,game_settings.private_match);
	MABLOGDEBUG("stat=%d inj=%f high=%f ofl=%f,%f pass=%f tcl=%f,%f tac=%f def=%f int=%f ofl=%f bonus=%f",
		game_settings.network_balanced_stats,game_settings.slider_injury_frequency,
		game_settings.slider_high_tackle_frequency,game_settings.slider_offload_frequency,
		game_settings.slider_offload_success_rate,game_settings.slider_pass_success_rate,
		game_settings.slider_break_tackle_ability,game_settings.slider_tackle_ability,
		game_settings.slider_attacking_urgency,game_settings.slider_defensive_urgency,
		game_settings.slider_intercept_frequency,game_settings.slider_offload_chance,game_settings.slider_ruck_tackle_bonus);
	MABLOGDEBUG("WET: %d %f (%f,%f,%f) %d %d %d",
		weather_settings.lighting_conditions,weather_settings.wind_strength,
		weather_settings.wind_direction.x,weather_settings.wind_direction.y,weather_settings.wind_direction.z,
		weather_settings.raining,weather_settings.time_of_day,
		weather_settings.overcast_selected);
	MABLOGDEBUG("CRD: %d %d", crowd_settings.crowd_size, crowd_settings.crowd_density);
	MABLOGDEBUG("STD: %s %d", SIFGameHelpers::GAConvertFStringToMabString(stadium_settings.stadium_abbr).c_str(), stadium_settings.field_signage);
	for (auto& hit : human_player_settings)
		MABLOGDEBUG("HMN: %d %d %d", hit.controller_id, hit.team, hit.peer_id);
	MABLOGDEBUG("CMP: %d %d", competition_settings.competition_definition_id, competition_settings.team_id);
	//for (auto& tit : team_settings)
	//	MABLOGDEBUG("team %d", tit->GetObjectID());
	MABLOGDEBUG("diff=%d law=%d sub=%d num=%d seed%d toss=%u dir=%u",
		difficulty, game_law, substitution_mode, num_active_teams, initial_random_seed,
		(unsigned int)game_settings.initial_play_direction_extra_time_coin_toss.size(),
		(unsigned int)game_settings.initial_play_direction.size());
#endif
}

void URugbyGameWorldSettings::Reset( MabUInt32 initial_seed )
{


	/// Annoying hacks ABROWN HACK
	Clear();
	int numTeams = RU_GAME_MODE_LIMITS::GetNumberOfTeams(false); // game_settings.GameModeIsR7());
	//#rc3_legacy
	//int numTeams = NUM_TEAMS_INIT;
	//if(game != NULL)
	//	numTeams = game->GetGameSettings().game_limits.GetNumberOfTeams();

	int numTeamsPlusOfficials = RU_GAME_MODE_LIMITS::GetNumberOfTeamsPlusOfficials(false);// game_settings.GameModeIsR7());
	//#rc3_legacy
	//int numTeamsPlusOfficias = NUM_TEAMS_PLUS_OFFICIALS_INIT;
	//if(game != NULL)
	//	numTeamsPlusOfficias = game->GetGameSettings().game_limits.GetNumberOfTeams() + 1;

	game_settings.initial_play_direction.reserve(numTeams/*NUM_TEAMS*/);
	for(int i=0; i<numTeams/*NUM_TEAMS*/; ++i)
		game_settings.initial_play_direction.push_back(ERugbyPlayDirection::UNKNOWN);

	team_settings.Reserve(numTeamsPlusOfficials/*NUM_TEAMS_PLUS_OFFICIALS*/);
	for(int i=0; i<numTeamsPlusOfficials/*NUM_TEAMS_PLUS_OFFICIALS*/; ++i)
	{
		FRugbyTeamSettings NewTeam = FRugbyTeamSettings();
		team_settings.Add(NewTeam);

		if(i >= numTeams)
			team_settings[team_settings.Num() - 1].isOfficialsTeam = true;
	}

	human_player_settings.Reserve(NUM_HUMAN_PLAYERS);
	for(int i=0; i<NUM_HUMAN_PLAYERS; ++i)
		human_player_settings.Add(RU_HUMAN_PLAYER_SETTINGS());

	MABLOGDEBUG("URugbyGameWorldSettings::Reset initial_seed %u -> %u",initial_random_seed,initial_seed);
	initial_random_seed = initial_seed;

	// Randomise some GameSettings
	game_settings.initial_kickoff_team = initial_seed % 2 ? SIDE_A : SIDE_B;
#if wwDEBUG_SRA
	game_settings.initial_kickoff_team = SIDE_A;
#endif
	ERugbyPlayDirection kickoff_dir = (initial_seed >> 2) % 2 ? ERugbyPlayDirection::NORTH : ERugbyPlayDirection::SOUTH;

	int32 debugLevel = -1;
	FParse::Value(FCommandLine::Get(), TEXT("InitialKickoffTeam="), debugLevel);
	if (debugLevel == -1)
	{
		debugLevel = CVarInitialKickoffTeam.GetValueOnGameThread();
	}
	if (debugLevel >= 0)
	{
		game_settings.initial_kickoff_team = (SSTEAMSIDE)debugLevel;
	}


	game_settings.initial_play_direction[game_settings.initial_kickoff_team] = kickoff_dir;
	game_settings.initial_play_direction[OtherTeam(game_settings.initial_kickoff_team)] = OtherDirection(kickoff_dir);

	// Reset the team setttings
	for( size_t i=0; i < team_settings.Num(); ++i)
		team_settings[i].Reset();

	// Set up the players.
	for ( int i = 0; i < NUM_HUMAN_PLAYERS; ++i )
	{
		//#rc3_legacy_controller human_player_settings[i].controller_id = i;
		human_player_settings[i].player_id = -1;
		human_player_settings[i].team = -1;
	}

	unsigned short teamA = SIFApplication::GetApplication()->GetDefaultTeamA();
	unsigned short teamB = SIFApplication::GetApplication()->GetDefaultTeamB();
	// Nasty hack, time time is of the essence!
	// Team Lomu is excluded in career mode, so that team won't exist when we leave a match!
	//#rc4_no_lomu 
	/*RUGameDatabaseManager* database_manager = SIFApplication::GetApplication()->GetGameDatabaseManager();
	if( database_manager->IsCareerCoachDatabaseActive() || database_manager->IsCareerProDatabaseActive() )
	{
		if(teamA == DB_TEAMID_LOMU_ALLSTARS)
			teamA = DB_TEAMID_SOUTHAFRICA; // Because I have the power.

		if(teamB == DB_TEAMID_LOMU_ALLSTARS)
			teamB = DB_TEAMID_AUSTRALIA;
	}*/

	// Ensure that teams in DB are not null.
	team_settings[ SIDE_A ].LoadTeam( teamA );
	team_settings[ SIDE_B ].LoadTeam( teamB );
	team_settings[ SIDE_OFFICIALS ].LoadTeam( DB_TEAMID_OFFICIALS_1 );
}

void URugbyGameWorldSettings::Clear( bool dont_clear_teams )
{
	/// Annoying hacks ABROWN HACK
	game_settings.initial_play_direction.clear();
	if(  !dont_clear_teams )
		team_settings.Empty();
	human_player_settings.Empty();
}

int URugbyGameWorldSettings::GetGameLength( int game_length_index )
{
	return static_cast<int>( MabMath::Pow(2.0f, static_cast<float>(game_length_index+1)) * 5.0f );
}

int URugbyGameWorldSettings::GetSingleGameLength( int game_length_index )
{
	return static_cast<int>( MabMath::Pow(2.0f, static_cast<float>(game_length_index)) * 5.0f );
}

int URugbyGameWorldSettings::GetGameLengthIndex( int game_length )
{
	int game_length_index = static_cast<int>( MabMath::LogBase2( static_cast<int>(game_length/5.0f) ) );
	// 5 min taken out
	game_length_index--;
	return game_length_index;
}

void URugbyGameWorldSettings::CalculateCrowdSize()
{
	// Load team data
	RUDB_TEAM& team_a = team_settings[0].team;
	RUDB_TEAM& team_b = team_settings[1].team;

	// Load stadium data
	RUDB_STADIUM stadium;
	SIFApplication::GetApplication()->GetGameDatabaseManager()->LoadData( stadium, stadium_settings.GetStadiumID() );
	float stadium_size_relativity_boost = 1.0f;
	switch(stadium.size)
	{
		case RUDB_STADIUM::STADIUM_SIZE_SMALL:	stadium_size_relativity_boost = 1.2f; break;
		case RUDB_STADIUM::STADIUM_SIZE_MED:	stadium_size_relativity_boost = 1.1f; break;
		case RUDB_STADIUM::STADIUM_SIZE_LARGE:	stadium_size_relativity_boost = 1.0f; break;
		default: break;
	}

	//! Weather factor
	float weather_boost = 1.1f;
	if(weather_settings.raining) weather_boost -= 0.2f;
	if(weather_settings.overcast_selected) weather_boost -= 0.1f;

	//! Calculate team power balance (Closer the match, the more excitement)
	const float team_a_rank = team_a.GetNormaliseRanking();
	const float team_b_rank = team_b.GetNormaliseRanking();
	const float team_rank_total = (team_a_rank + team_b_rank) / 2.0f;

	//! Calculate Team type factor
	bool someone_is_international = team_a.IsInternational() || team_b.IsInternational();

	//! Match importance
	float match_importance = 0.2f;
	
	if(SIFGameHelpers::GACompetitionIsCompetitionActive())
	{
		if (SIFGameHelpers::GACompetitionIsInGrandFinals())
		{
			match_importance = 0.7f;
		}
		else if (SIFGameHelpers::GACompetitionIsInFinals())
		{
			match_importance = 0.4f;
		}
		else
		{
			match_importance = 0.0f;
		}
	}

	//! Piece together factors
	float crowd_density = 0.0f;
	crowd_density += match_importance;
	crowd_density += someone_is_international ? 0.5f : 0.2f;
	crowd_density += 0.4f * team_rank_total;
	//GG WW AFL ERICTOTEST static cast unsigned int to float 
	crowd_density += -0.2f * (static_cast<float>(initial_random_seed) / static_cast<float>(MabUInt32MAXVAL));
	crowd_density *= stadium_size_relativity_boost;
	crowd_density *= weather_boost;

	//! Set value
	MabMath::Clamp(crowd_density, 0.0f, 1.0f);
	crowd_settings.crowd_density = (int)(crowd_density * 100.0f);

	// Set the crowd size as an enum for easy generalisaition (ie Commentary)
	if( crowd_settings.crowd_density < 1)			crowd_settings.crowd_size = CROWD_SIZE_NONE;
	else if( crowd_settings.crowd_density < 20 )	crowd_settings.crowd_size = CROWD_SIZE_VERY_SMALL;
	else if( crowd_settings.crowd_density < 40 )	crowd_settings.crowd_size = CROWD_SIZE_SMALL;
	else if( crowd_settings.crowd_density < 60 )	crowd_settings.crowd_size = CROWD_SIZE_MEDIUM;
	else if( crowd_settings.crowd_density < 80 )	crowd_settings.crowd_size = CROWD_SIZE_GOOD;
	else											crowd_settings.crowd_size = CROWD_SIZE_CAPACITY;
}

void URugbyGameWorldSettings::SetupOfficials()
{
	const int NUM_OFFICIALS_TEAMS = 4;
	static unsigned short officials_teams[NUM_OFFICIALS_TEAMS] = {
			DB_TEAMID_OFFICIALS_1,
			DB_TEAMID_OFFICIALS_2,
			DB_TEAMID_OFFICIALS_3,
			DB_TEAMID_OFFICIALS_1
		};
	unsigned short db_id = officials_teams[ initial_random_seed % NUM_OFFICIALS_TEAMS ];

	// Top14 uses this team.
	// HES request ticket #47785
	RUCareerModeManager *career_manager = SIFApplication::GetApplication()->GetCareerModeManager();
	if(career_manager && career_manager->IsActive())
	{
		RUActiveCompetitionBase* active_competition = career_manager->GetActiveCompetition();
		//if(active_competition && active_competition->GetDefinitionId() == DB_COMPID_TOP14)
		//	db_id = DB_TEAMID_OFFICIALS_4;
	}

	team_settings[ SIDE_OFFICIALS ].LoadTeam( db_id );
}

void URugbyGameWorldSettings::NormaliseStats()
{
	// We don't have network teams for the demo
	RU_TEAM_SETTINGS normal_team;
	// Nick WWS 7s to Womens //
	//if( game_settings.GameModeIsR13() )
	//{
		normal_team.LoadTeam( NETWORK_STATS_FIFTEENS_TEAM_ID );
	//}
	//else
	//{
	//	normal_team.LoadTeam( NETWORK_STATS_SEVENS_TEAM_ID );
	//}

	int numTeams = game_limits.GetNumberOfTeams();

	for(int i=0; i< numTeams/*NUM_TEAMS*/; ++i)
	{
		RU_TEAM_SETTINGS& settings = team_settings[i];
		settings.team.OverrideStats( normal_team.team );

		for(size_t j=0; j<settings.lineup.size() && j<normal_team.lineup.size(); ++j)
		{
			RUDB_PLAYER& player = settings.lineup[j];
			player.OverrideStats( normal_team.lineup[j] );

			// Make sure all players in a network game can kick goals, in case they are the kicker
			player.goal_kick_accuracy = 8500;

			//if we are in a multiplayer game and we are the teams play kicker make sure that
			//the given player can perform the role, as we're using the English teams stats, and the
			//English player in the given position may not be the flashest.
			if(settings.team.play_kicker_id == player.GetDbId())
			{
				player.general_kick_accuracy = 8500;
			}
		}
	}
}

// Get the conversion score for the current game law
int URugbyGameWorldSettings::GetConversionScore() const
{
	return GetConversionScore( game_law );
}

int URugbyGameWorldSettings::GetConversionScore( GAME_LAW for_law ) const
{
	// WJS RLC Are we having different laws?
	// WJS RLC No according to Cole March 2025
	return LEAGUE_CONVERSION_SCORE;
	/*
	switch ( for_law )
	{
	case GAME_LAW_NORMAL:
		return IRB_CONVERSION_SCORE;
	case GAME_LAW_NRC:
		return NRC_CONVERSION_SCORE;
	default:
		return IRB_CONVERSION_SCORE;
	}
	//*/
}

// Get the penalty score for the current game law
int URugbyGameWorldSettings::GetPenaltyScore() const
{
	return GetPenaltyScore( game_law );
}

int URugbyGameWorldSettings::GetPenaltyScore( GAME_LAW for_law ) const
{
	// WJS RLC Are we having different laws?
	return LEAGUE_PENALTY_GOAL_SCORE;
	/*
	switch ( for_law )
	{
	case GAME_LAW_NORMAL:
		return IRB_PENALTY_SCORE;
	case GAME_LAW_NRC:
		return NRC_PENALTY_SCORE;
	default:
		return IRB_PENALTY_SCORE;
	}
	//*/
}

// Get the penalty score for the current game law
int URugbyGameWorldSettings::GetTryScore() const
{
	return GetTryScore(game_law);
}

int URugbyGameWorldSettings::GetTryScore(GAME_LAW for_law) const
{
	// WJS RLC Are we having different laws?
	return LEAGUE_TRY_SCORE;
	/*
	switch ( for_law )
	{
	case GAME_LAW_NORMAL:
		return IRB_PENALTY_SCORE;
	case GAME_LAW_NRC:
		return NRC_PENALTY_SCORE;
	default:
		return IRB_PENALTY_SCORE;
	}
	//*/
}

// Get the drop goal score for the current game law
int URugbyGameWorldSettings::GetDropGoalScore( bool beyond40 ) const
{
	return GetDropGoalScore( game_law, beyond40 );
}

int URugbyGameWorldSettings::GetDropGoalScore( GAME_LAW for_law, bool beyond40 ) const
{
	// WJS RLC Are we having different laws?
	if (beyond40)
	{
		return LEAGUE_DROP_GOAL_40M_SCORE;
	}

	return LEAGUE_DROP_GOAL_SCORE;
	/*	switch ( for_law )
	{
	case GAME_LAW_NORMAL:
		return IRB_DROP_GOAL_SCORE;
	case GAME_LAW_NRC:
		return NRC_DROP_GOAL_SCORE;
	default:
		return IRB_DROP_GOAL_SCORE;
	}
	//*/
}

struct PairSortBySecondDecreasing
{
	 bool operator()(std::pair<unsigned short, unsigned short> player_pair_one, std::pair<unsigned short, unsigned short> player_pair_two )
	 {
		 return player_pair_one.first > player_pair_two.first;
	 }
};

struct GoalScorersSort
{
	 bool operator()(std::pair<unsigned short, std::pair<unsigned short, unsigned short> > one, std::pair<unsigned short, std::pair<unsigned short, unsigned short> > two )
	 {
		 return one.second.second > two.second.second;
	 }
};

void URugbyGameWorldSettings::PopulateStatsWindow( bool full_time )
{
	MABUNUSED(full_time);
}

FRugbyHumanPlayerSettings* URugbyGameWorldSettings::GetHumanPlayerSettings(EHumanPlayerSlot playerSlot)
{
	int slotId = (int)playerSlot;
	if (slotId >= 0 && slotId < NUM_HUMAN_PLAYERS)
	{
		return &human_player_settings[slotId];
	}

	MABLOGMSG(LOGCHANNEL_ALWAYS, LOGTYPE_INFO, "No applicable human settings found.");
	return nullptr;
}

FRugbyHumanPlayerSettings* URugbyGameWorldSettings::GetHumanPlayerSettingsFromPlayerId(int playerId)
{
	for (int i = 0; i < NUM_HUMAN_PLAYERS; ++i)
	{
		if (human_player_settings[i].player_id == playerId)
		{
			return &human_player_settings[i];
		}
	}

	MABLOGMSG(LOGCHANNEL_ALWAYS, LOGTYPE_INFO, "No applicable human settings found.");
	return nullptr;
}

FRugbyHumanPlayerSettings* URugbyGameWorldSettings::GetHumanPlayerSettingsFromControllerId(int controllerId)
{
	for (int i = 0; i < NUM_HUMAN_PLAYERS; ++i)
	{
		if (human_player_settings[i].controller_id == controllerId)
		{
			return &human_player_settings[i];
		}
	}

	MABLOGMSG(LOGCHANNEL_ALWAYS, LOGTYPE_INFO, "No applicable human settings found.");
	return nullptr;
}

//void URugbyGameWorldSettings::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
//{
//	Super::GetLifetimeReplicatedProps(OutLifetimeProps);
//
//	//DOREPLIFETIME(URugbyGameWorldSettings, game_settings);
//	//DOREPLIFETIME(URugbyGameWorldSettings, weather_settings);
//	//DOREPLIFETIME(URugbyGameWorldSettings, crowd_settings);
//	//DOREPLIFETIME(URugbyGameWorldSettings, stadium_settings);
//	//DOREPLIFETIME(URugbyGameWorldSettings, team_settings);
//	//DOREPLIFETIME(URugbyGameWorldSettings, human_player_settings);
//}

const char* FRugbyStadiumSettings::GetStadiumAbbreviation() const
{
	const char* ansi_abbr = TCHAR_TO_ANSI(*stadium_abbr);
	MabStringHelper::Strcpy(stadium_abbr_cstr, ansi_abbr);
	return stadium_abbr_cstr;
}

unsigned short FRugbyStadiumSettings::GetStadiumID() const
{
	return SIFApplication::GetApplication()->GetMatchStadiumManager()->GetIDFromAbbreviation( GetStadiumAbbreviation() );
}

RUDB_STADIUM::STADIUM_SIZE_TYPE FRugbyStadiumSettings::GetStadiumSize() const
{
	RUStadiumManager* stadium_man = SIFApplication::GetApplication()->GetMatchStadiumManager();
	unsigned short stadium_ID = GetStadiumID();
	const RUDB_STADIUM* stadium = stadium_man->GetStadiumFromID(stadium_ID);
	return stadium->size;
}

bool FRugbyGameModeLimits::IsGameModeR7() const
{
	return false; // SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.GetGameMode() == GAME_MODE_RU13W; // Nick  WWS 7s to Womens // GAME_MODE_SEVENS;
}

bool FRugbyGameModeLimits::IsGrandFinal() const
{
	//return mGameSettings == nullptr ? false : mGameSettings->game_settings.is_grand_final;
	return SIFApplication::GetApplication()->GetMatchGameSettings()->game_settings.is_grand_final;
}
