/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/RUGameSettingsTeamSettings.h"

#include "Databases/RUGameDatabaseManager.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/SIFGameWorld.h"

#include "RugbyGameInstance.h"

//MABRUNTIMETYPE_IMP1(FRugbyTeamSettings, MabObject);

FRugbyTeamSettings::FRugbyTeamSettings()
: team()
, strip_list()
, can_be_human_controlled(true)
, confidence(0.5f)
, lineup()
, strip_index(0) // was -1
, isOfficialsTeam (false)
{}

void FRugbyTeamSettings::Reset()
{
	// Load Team
	team.Reset();

	strip_list.clear();
	strip_list.reserve(RUDB_TEAM::MAX_STRIPS);
	for (size_t i=0; i<RUDB_TEAM::MAX_STRIPS; ++i)
		strip_list.push_back(RUDB_TEAM_STRIP());
	
	lineup.clear();

	// Russell : I've changed the behavior below to resize the array to a constant rather than a variable number of entries.
	//           It doesn't seem wise to rely on team.GetIsR7Exclusive in this Reset function, especially when the
	//           LoadTeam function below calls this prior to loading team data.
	lineup.resize(NUM_PLAYERS_PER_TEAM_INC_BENCH_INIT, RUDB_PLAYER());
	/* See old code below:
	RUGameSettings* gameSettings = SIFApplication::GetApplication()->GetMatchGameSettings();
	int playersOnTeamIncBench = NUM_PLAYERS_PER_TEAM_INC_BENCH_INIT;
	if (gameSettings != nullptr)
	{
		playersOnTeamIncBench = gameSettings->game_limits.GetNumberOfPlayersPerTeamIncBenchR13();

		if(team.GetIsR7Exclusive())
			playersOnTeamIncBench = gameSettings->game_limits.GetNumberOfPlayersPerTeamIncBenchR7();
	}

	lineup.reserve(playersOnTeamIncBench);
	for (int i=0; i< playersOnTeamIncBench; ++i)
		lineup.push_back(RUDB_PLAYER());
	// */

	can_be_human_controlled = true;
	confidence = 0.5f;
	strip_index = 0;
}


void FRugbyTeamSettings::LoadTeam(unsigned short db_id, TMap<int, unsigned short> playerOverrides)
{
	RUGameDatabaseManager* database = SIFApplication::GetApplication()->GetGameDatabaseManager();
	MABASSERT(database);
	if (!database) return;

	MABASSERT(database->IsDatabaseLoaded());
	if (!database->IsDatabaseLoaded()) return;

	//MABLOGDEBUG("Load Team: %d", db_id);

	Reset();

	if (db_id <= 0)
		return;

	// Tell database manager to wait for database if not loaded (this thread only)
	database->SetLoadThread();

	// Load Team
	MABVERIFYMSG(database->LoadData(team, db_id), "Failed to load RUDB_TEAM");

	// Load strips
	for (size_t i = 0; i < RUDB_TEAM::MAX_STRIPS; ++i)
	{
		unsigned short strip_id = team.GetStripId(i);
		if (strip_id != SQLITEMAB_INVALID_ID)
		{
			MABVERIFYMSG(database->LoadData(strip_list[i], strip_id), "Failed to load RUDB_TEAM_STRIP");
		}
	}

	RUGameSettings* gameSettings = SIFApplication::GetApplication()->GetMatchGameSettings();

	// RussellD : Removing the lineup sorting. The lineup order should be appropriately managed elsewhere,
	//  and the only effect from calling this is to mess up the order of subs.
	// Load lineup players
	// team.SortLineup();

	int playersOnTeamIncBench = NUM_PLAYERS_PER_TEAM_INC_BENCH_INIT;
	if (gameSettings != nullptr)
	{
		playersOnTeamIncBench = gameSettings->game_limits.GetNumberOfPlayersPerTeamIncBenchR13();

		// Nick WWS &s to Womens 13s //
		//if (team.GetIsR7Exclusive())
		//	playersOnTeamIncBench = gameSettings->game_limits.GetNumberOfPlayersPerTeamIncBenchR7();
	}

	int numOfficials = NUM_OFFICIALS_INIT;
	if (gameSettings != nullptr)
		numOfficials = gameSettings->game_limits.GetNumberOfOfficials();

	//if(gameSettings != nullptr)
	//	teamLoadForMode = gameSettings->game_settings.GetGameMode();
	//else
	//	teamLoadForMode = 0;
	
	// Russell : I've added this call to force the lineup down to the right size now that we've loaded the team data
	//           and know whether it will be used for sevens or fifteens.
	lineup.resize(playersOnTeamIncBench, RUDB_PLAYER());

	for (int i = 0; i < playersOnTeamIncBench/*NUM_PLAYERS_PER_TEAM_INC_BENCH*/; ++i)
	{
		if (isOfficialsTeam && i >= numOfficials)
			continue;

		if (i < team.GetNumLineups())
		{
			unsigned short playerID = 0;

			if (playerOverrides.Contains(i))
			{
				playerID = playerOverrides[i];
			}

			if (playerID == 0)
			{
				playerID = team.GetLineup(i).player_id;
			}

			MABVERIFYMSG(database->LoadData(lineup[i], playerID), "Failed to load RUDB_PLAYER");
			//MABLOGDEBUG("Load Team: ID: %i: Player %i", i, playerID);
		}
	}

	database->ClearLoadThread();
}

