// Copyright (c) 2016-2019 Wicked Witch Software Pty. Ltd.

#include "RUContextualStateMachine.h"
#include "RUContextualHelper.h"

#include "RUContextualState.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/Ball/SSBall.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/SSSpatialHelper.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseLineOut.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseExtraTimeToss.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseMaul.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseScrum.h"

#ifdef ENABLE_PRO_MODE
#include "Match/RugbyUnion/CompetitionMode/RUCareerModeManager.h"
#endif

#include "Match/Ai/Roles/Competitors/SetPlays/RURoleRuckScrumHalf.h"
#include "Match/Ai/Roles/Competitors/SetPlays/RURoleRuck.h"
#include "Match/Components/RUActionManager.h"
#include "RugbyGameInstance.h"

RUContextualStateMachine::RUContextualStateMachine( RUContextualHelper* hhelper ):
helper( hhelper ),
is_active( false )
, can_force_ball_contextual_displayed( false )
{ 

}

RUContextualStateMachine::~RUContextualStateMachine()
{
	TurnOff(true);
}

void RUContextualStateMachine::TurnOn()
{
	if ( !is_active )
	{
		// Setup game event listeners
		RUGameEvents* game_events = helper->GetGame()->GetEvents();

		game_events->caught.Add( this, &RUContextualStateMachine::OnBallCaught );
		game_events->collected.Add( this, &RUContextualStateMachine::OnBallCollected );

		game_events->possession_change.Add( this, &RUContextualStateMachine::OnPossessionChanged );
		game_events->ball_holder_changed.Add( this, &RUContextualStateMachine::OnBallHolderChanged );
		game_events->kick.Add( this, &RUContextualStateMachine::OnKick );
		
		game_events->ruck_ball_released.Add( this, &RUContextualStateMachine::OnRuckBallReleased );
		game_events->ruck_half_deciding.Add(this, &RUContextualStateMachine::OnRuckHalfDeciding);

		game_events->ruck_formed.Add(this, &RUContextualStateMachine::OnRuckFormed);
		game_events->ruck_start.Add(this, &RUContextualStateMachine::OnRuckStart);
		game_events->ruck_finished.Add(this, &RUContextualStateMachine::OnRuckFinished);

		game_events->set_play_started.Add(this, &RUContextualStateMachine::OnSetPlay);

#ifdef ENABLE_PRO_MODE
		game_events->ruck_pro_join_half_ruck.Add( this, &RUContextualStateMachine::OnRuckProJoinedRuckHalf );
		game_events->ruck_pro_leave_half_ruck.Add( this, &RUContextualStateMachine::OnRuckProLeaveRuckHalf );
		game_events->ruck_pro_joined_ruck.Add( this, &RUContextualStateMachine::OnRuckProJoinRuck );
		game_events->ruck_pro_joining_ruck.Add( this, &RUContextualStateMachine::OnRuckProJoiningRuck );
		game_events->ruck_pro_leave_ruck.Add( this, &RUContextualStateMachine::OnRuckProLeaveRuck );
		game_events->ruck_player_left.Add( this, &RUContextualStateMachine::OnPlayerLeftRuck );
#endif
		game_events->quick_conversion_start.Add( this, &RUContextualStateMachine::OnQuickConversionStart );
		game_events->quick_conversion_stop.Add( this, &RUContextualStateMachine::OnQuickConversionStop );
		
		game_events->ball_bounce.Add( this, &RUContextualStateMachine::OnBallBounce );
		
		game_events->try_result.Add( this, &RUContextualStateMachine::OnTry );
		game_events->half_time.Add( this, &RUContextualStateMachine::OnHalfTime );
		
		game_events->lineout_ready.Add( this, &RUContextualStateMachine::OnLineoutReady );
		game_events->lineout_refresh.Add(this, &RUContextualStateMachine::OnLineoutReady);
		game_events->lineout_throw.Add( this, &RUContextualStateMachine::OnLineoutThrow );
		game_events->lineout_throw_nrc.Add( this, &RUContextualStateMachine::OnLineoutThrow );
		game_events->lineout_time_out.Add ( this, &RUContextualStateMachine::OnLineoutTimeout );
		game_events->lineout_slowmo.Add( this, &RUContextualStateMachine::OnLineoutSlowmo );
		game_events->lineout_finished.Add( this, &RUContextualStateMachine::OnLineoutFinish );
		game_events->lineout_select_pod.Add ( this, &RUContextualStateMachine::OnLineoutSelectPod );

		game_events->play_the_ball_started.Add ( this, &RUContextualStateMachine::OnPlayTheBallStarted );
		game_events->play_the_ball_finished.Add ( this, &RUContextualStateMachine::OnPlayTheBallFinished );

		game_events->scrum.Add( this, &RUContextualStateMachine::OnScrumSignalled );
		game_events->scrum_start.Add( this, &RUContextualStateMachine::OnScrumStart );
		game_events->scrum_finish.Add(this, &RUContextualStateMachine::OnScrumFinish);

		game_events->scrum_ball_release_possible.Add( this, &RUContextualStateMachine::OnScrumBallReleasePossible );

		game_events->tackle.Add(this, &RUContextualStateMachine::OnTackleStart );
		game_events->tacklee_action_exit.Add(this, &RUContextualStateMachine::OnTackleeExit );

		game_events->tackle_contested.Add( this, &RUContextualStateMachine::OnTackleContested );
		game_events->maul_formed.Add( this, &RUContextualStateMachine::OnMaulFormed );
		game_events->maul_handing_off.Add( this, &RUContextualStateMachine::OnMaulHandingOff );
		game_events->maul_ball_released.Add( this, &RUContextualStateMachine::OnMaulBallReleased );
		game_events->maul_finished.Add( this, &RUContextualStateMachine::OnMaulFinished );
		
		game_events->maul_player_joined.Add( this, &RUContextualStateMachine::OnMaulPlayerJoined);

		game_events->cutscene_half_time.Add ( this, &RUContextualStateMachine::OnHalfTime);
		game_events->cutscene_full_time.Add ( this, &RUContextualStateMachine::OnHalfTime );
		game_events->cutscene_extra_time.Add ( this, &RUContextualStateMachine::OnHalfTime );
		game_events->cutscene_simulation.Add ( this, &RUContextualStateMachine::OnSimulation);

		//game_events->screen_wipe_begin.Add( this, &RUContextualStateMachine::OnScreenTransitionStart );
		//game_events->screen_wipe_end.Add( this, &RUContextualStateMachine::OnScreenTransitionEnd );

		game_events->ballholder_can_force_ball.Add( this, &RUContextualStateMachine::OnBallholderCanForceBallStateChange );

		game_events->cutscene_extra_time_coin_toss_start_context.Add(this, &RUContextualStateMachine::OnCoinTossStarted );


		// Reset the state
		helper->SetState( CS_NONE, NULL );

		is_active = true;
	}
}

void RUContextualStateMachine::TurnOff(bool is_destructor)
{
	if ( is_active )
	{
		// Setup game event listeners
		RUGameEvents* game_events = helper->GetGame()->GetEvents();


		game_events->ruck_ball_released.Remove( this, &RUContextualStateMachine::OnRuckBallReleased );
		game_events->ruck_half_deciding.Remove( this, &RUContextualStateMachine::OnRuckHalfDeciding );

		game_events->ruck_formed.Remove(this, &RUContextualStateMachine::OnRuckFormed);
		game_events->ruck_start.Remove(this, &RUContextualStateMachine::OnRuckStart);
		game_events->ruck_finished.Remove(this, &RUContextualStateMachine::OnRuckFinished);

#ifdef ENABLE_PRO_MODE		
		game_events->ruck_pro_join_half_ruck.Remove( this, &RUContextualStateMachine::OnRuckProJoinedRuckHalf );
		game_events->ruck_pro_leave_half_ruck.Remove( this, &RUContextualStateMachine::OnRuckProLeaveRuckHalf );
		game_events->ruck_pro_joined_ruck.Remove( this, &RUContextualStateMachine::OnRuckProJoinRuck );
		game_events->ruck_pro_leave_ruck.Remove( this, &RUContextualStateMachine::OnRuckProLeaveRuck );
		game_events->ruck_pro_joining_ruck.Remove( this, &RUContextualStateMachine::OnRuckProJoiningRuck );
		game_events->ruck_player_left.Remove( this, &RUContextualStateMachine::OnPlayerLeftRuck );
#endif		
		game_events->quick_conversion_start.Remove( this, &RUContextualStateMachine::OnQuickConversionStart );
		game_events->quick_conversion_stop.Remove( this, &RUContextualStateMachine::OnQuickConversionStop );

		game_events->play_the_ball_started.Remove(this, &RUContextualStateMachine::OnPlayTheBallStarted);
		game_events->play_the_ball_finished.Remove(this, &RUContextualStateMachine::OnPlayTheBallFinished);

		game_events->caught.Remove( this, &RUContextualStateMachine::OnBallCaught );
		game_events->collected.Remove( this, &RUContextualStateMachine::OnBallCollected );

		game_events->possession_change.Remove( this, &RUContextualStateMachine::OnPossessionChanged );
		game_events->ball_holder_changed.Remove( this, &RUContextualStateMachine::OnBallHolderChanged );
		game_events->kick.Remove( this, &RUContextualStateMachine::OnKick );
		
		game_events->ball_bounce.Remove( this, &RUContextualStateMachine::OnBallBounce );
		game_events->try_result.Remove( this, &RUContextualStateMachine::OnTry );
		game_events->half_time.Remove( this, &RUContextualStateMachine::OnHalfTime );
		
		game_events->lineout_ready.Remove( this, &RUContextualStateMachine::OnLineoutReady );
		game_events->lineout_refresh.Remove(this, &RUContextualStateMachine::OnLineoutReady);
		game_events->lineout_throw.Remove( this, &RUContextualStateMachine::OnLineoutThrow );
		game_events->lineout_throw_nrc.Remove( this, &RUContextualStateMachine::OnLineoutThrow );
		game_events->lineout_time_out.Remove ( this, &RUContextualStateMachine::OnLineoutTimeout );
		game_events->lineout_slowmo.Remove( this, &RUContextualStateMachine::OnLineoutSlowmo );
		game_events->lineout_finished.Remove( this, &RUContextualStateMachine::OnLineoutFinish );
		game_events->lineout_select_pod.Remove ( this, &RUContextualStateMachine::OnLineoutSelectPod );

		game_events->scrum.Remove( this, &RUContextualStateMachine::OnScrumSignalled );
		game_events->scrum_start.Remove( this, &RUContextualStateMachine::OnScrumStart );
		game_events->scrum_finish.Remove( this, &RUContextualStateMachine::OnScrumFinish );
		game_events->scrum_ball_release_possible.Remove( this, &RUContextualStateMachine::OnScrumBallReleasePossible );

		game_events->tackle.Remove(this, &RUContextualStateMachine::OnTackleStart );
		game_events->tacklee_action_exit.Remove(this, &RUContextualStateMachine::OnTackleeExit );

		game_events->tackle_contested.Remove( this, &RUContextualStateMachine::OnTackleContested );
		game_events->maul_formed.Remove( this, &RUContextualStateMachine::OnMaulFormed );
		game_events->maul_handing_off.Remove( this, &RUContextualStateMachine::OnMaulHandingOff );
		game_events->maul_ball_released.Remove( this, &RUContextualStateMachine::OnMaulBallReleased );
		game_events->maul_finished.Remove( this, &RUContextualStateMachine::OnMaulFinished );
		
		game_events->maul_player_joined.Remove( this, &RUContextualStateMachine::OnMaulPlayerJoined);

		game_events->cutscene_half_time.Remove ( this, &RUContextualStateMachine::OnHalfTime);
		game_events->cutscene_full_time.Remove ( this, &RUContextualStateMachine::OnHalfTime );
		game_events->cutscene_extra_time.Remove ( this, &RUContextualStateMachine::OnHalfTime );
		game_events->cutscene_simulation.Remove ( this, &RUContextualStateMachine::OnSimulation);

		//game_events->screen_wipe_begin.Remove( this, &RUContextualStateMachine::OnScreenTransitionStart );
		//game_events->screen_wipe_end.Remove( this, &RUContextualStateMachine::OnScreenTransitionEnd );

		game_events->ballholder_can_force_ball.Remove( this, &RUContextualStateMachine::OnBallholderCanForceBallStateChange );

		game_events->cutscene_extra_time_coin_toss_start_context.Remove(this, &RUContextualStateMachine::OnCoinTossStarted );

		is_active = false;
		can_force_ball_contextual_displayed = false;
	}

	if(!is_destructor)
	{
		// Make sure state is set to null
		helper->SetState( CS_NONE, NULL );
	}
}

void RUContextualStateMachine::OnKick(ARugbyCharacter *player, KickContext, KickType type, const FVector &position )
{
	float time_till_bounce;
	FVector bounce_position;
	SIFGameWorld* game = helper->GetGame();
	helper->GetGame()->GetBall()->GetBouncePosition( 1, bounce_position, time_till_bounce );

	const static float MIN_IN_AIR_TIME_FOR_CONTEXT_HELP = 0.7f;

	//If ball is heading out before bounce no hud.
	if ( game->GetSpatialHelper()->IsJointOutEnds( bounce_position ) || game->GetSpatialHelper()->IsJointOutSides( bounce_position ) || time_till_bounce < MIN_IN_AIR_TIME_FOR_CONTEXT_HELP )
	{
		helper->SetState( CS_NONE, NULL );
		return;
	}

	// Check for human players
	RUTeam* def_team = game->GetGameState()->GetDefendingTeam();
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;
	helper->GetHumanPlayers(att_human, def_human);

	// Order of setting goes 1player	primary = def human behind 22, def human receive, att human receive
	//						 2player	primary = defence state, secondary =  def kick behind 22, def kick receive
	bool two_player = def_human != NULL && att_human != NULL;
	BallFreeInfo bfi = game->GetStrategyHelper()->GetLastBallFreeInfo();
	bool in_22 = game->GetSpatialHelper()->IsInDef20( bounce_position, def_team->GetPlayDirection() ) && bfi.game_phase_when_released == RUGamePhase::PLAY;

	if ( !two_player )
	{
		if ( def_human != NULL )
			helper->SetState( in_22 ? CS_KICK_RECIEVE_BEHIND_22 : CS_KICK_RECIEVE, def_human, true );
		else if ( att_human != NULL )
			helper->SetState( CS_KICK_RECIEVE, att_human, true );
		else 
		{
			helper->SetState( CS_NONE, NULL );
			return;
		}

		// no secondary state
		helper->SetState( CS_NONE, NULL, false );
	}
	else
	{
		if ( in_22 )
			helper->SetState( CS_KICK_RECIEVE_BEHIND_22, def_human, true );
		else
			helper->SetState( CS_KICK_RECIEVE, def_human, true );

		helper->SetState( CS_KICK_RECIEVE, att_human, false );
	}

	helper->SetStaticPosition( bounce_position );
}

void RUContextualStateMachine::OnBallBounce( const FVector& position, const FVector& velocity )
{
	SIFGameWorld* game = helper->GetGame();
	// if ball is out on bounce
	if ( game->GetSpatialHelper()->IsJointOutEnds( position ) || helper->GetGame()->GetSpatialHelper()->IsJointOutSides( position ) )
	{	
		helper->SetState( CS_NONE, NULL );
		return;
	}

	// Check for human players
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;
	helper->GetHumanPlayers(att_human, def_human);

	// Order of setting goes 1player	primary = free ball dynamic
	//						 2player	primary = free ball dynamic, secondary defence state
	bool two_player = def_human != NULL && att_human != NULL;

	if ( !two_player )
	{
		if ( def_human != NULL )
			helper->SetState( CS_FREE_BALL_DYNAMIC, def_human, true );
		else if ( att_human != NULL )
			helper->SetState( CS_FREE_BALL_DYNAMIC, att_human, true );
		else // No humans
			helper->SetState( CS_NONE, NULL );

		// no secondary state
		helper->SetState( CS_NONE, NULL, false );
	}
	else
	{
		helper->SetState( CS_FREE_BALL_DYNAMIC, att_human, true );
		helper->SetState( CS_DEFENCE, def_human, false );
	}	
}

void RUContextualStateMachine::OnRuckBallReleased(RUTeam* team, const FVector& position, float time)
{
	helper->SetState( CS_NONE, NULL, true );
	helper->SetState( CS_NONE, NULL, false );
}

void RUContextualStateMachine::OnRuckHalfDeciding(ARugbyCharacter* player )
{
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;

	RUGamePhaseRuck* phase = (helper->GetGame()->GetGameState()->GetPhase() != RUGamePhase::RUCK) ? nullptr :
		helper->GetGame()->GetGameState()->GetPhaseHandler<RUGamePhaseRuck>();
	if (phase)
	{
		helper->GetHumanPlayers(att_human, def_human, *phase);
	}

	// When we play pro games, we only want to set the contextual state when we are the ruck half (only does a box kick at the moment)
	bool proGame = SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && 
		SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro();
	if(proGame && att_human && att_human->GetRugbyCharacter())
	{
		// If we're not the ruck half, we dont want to show anything. Once the ball is collected, we should change
		RURoleRuckScrumHalf* sHalf = MabCast<RURoleRuckScrumHalf>(att_human->GetRugbyCharacter()->GetRole());
		if(!sHalf)
		{
			helper->SetState( CS_NONE, NULL, true );
			helper->SetState( CS_NONE, NULL, false );
			return;
		}
	}

	if ( att_human )
	{
		helper->SetState( CS_RUCK_BALL_RELEASED, att_human, true );
		helper->SetState( CS_NONE, NULL, false );
	}
	else
		helper->SetState( CS_NONE, NULL, true );
}

void RUContextualStateMachine::OnRuckFormed(ARugbyCharacter* tacklee, SIFRugbyCharacterList* tacklers)
{
}

void RUContextualStateMachine::OnRuckStart(int)
{
	SIFGameWorld* game = helper->GetGame();
	FVector state_pos = game->GetBall()->GetCurrentPosition();
	state_pos.y = 0.0f;
	helper->SetStaticPosition( state_pos );

	//Get human players
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;

	RUGamePhaseRuck* phase = (helper->GetGame()->GetGameState()->GetPhase() != RUGamePhase::RUCK) ? nullptr :
		helper->GetGame()->GetGameState()->GetPhaseHandler<RUGamePhaseRuck>();
	if (phase)
	{
		helper->GetHumanPlayers(att_human, def_human, *phase);
	}

	// Vaughan: TODO: Assert here is stupid as there are situations where both might be null check with Aaron when back
	//MABASSERT(att_human != NULL || def_human != NULL);

	// Check for human players only need one to fire
	RUContextStates humanState = CS_NONE;
	SSHumanPlayer * hPlayer = NULL;
	bool isPrimary = false;

	if ( att_human ) 
	{
		humanState = CS_RUCK_FORMED;
		hPlayer = att_human;
		isPrimary = true;
	}
	else if ( def_human ) 
	{
		humanState = CS_RUCK_FORMED;
		hPlayer = def_human;
		isPrimary = true;
	}
	else 
	{
		humanState = CS_NONE;
		hPlayer = NULL;
		isPrimary = true;
	}

#ifdef ENABLE_PRO_MODE
	// If we're playing pro mode, we want to use pro specific contextual help
	RUCareerModeManager* proModeMan = SIFApplication::GetApplication()->GetCareerModeManager();
	bool proMode = proModeMan->IsActive() && proModeMan->GetIsCareerModePro();
	if(proMode)
	{
		//RUGamePhaseRuck* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseRuck>();
		
		// When the ruck was formed, the pro player was inside the ruck and we can contest
		if(phase->GetIsProFirstInRuck())
		{
			humanState = CS_RUCK_FORMED_PRO_MODE_TACKLED;
		}
		// Otherwise, show default ruck formed context
		else
			humanState = CS_RUCK_FORMED_PRO_MODE;
	}
#endif

	helper->SetState( humanState, hPlayer, isPrimary );
}

#ifdef ENABLE_PRO_MODE
void RUContextualStateMachine::OnRuckProJoinedRuckHalf( ARugbyCharacter* proPlayer )
{
	MABASSERT( proPlayer ); // Make sure we actually have a pro player
	MABASSERT( proPlayer->GetHumanPlayer() ); // when we have a pro player, it should be a human one

	SIFGameWorld* game = helper->GetGame();
	FVector state_pos = game->GetBall()->GetCurrentPosition();
	state_pos.y = 0.0f;
	helper->SetStaticPosition( state_pos );
	
	// Set the state to a pro mode ruck half context, using our pro as the human
	helper->SetState( CS_RUCK_FORMED_PRO_MODE_RUCK_HALF, proPlayer->GetHumanPlayer(), true );
}

void RUContextualStateMachine::OnRuckProLeaveRuckHalf( ARugbyCharacter* proPlayer )
{
	MABASSERT( proPlayer ); // Make sure we actually have a pro player
	MABASSERT( proPlayer->GetHumanPlayer() ); // when we have a pro player, it should be a human one

	SIFGameWorld* game = helper->GetGame();
	FVector state_pos = game->GetBall()->GetCurrentPosition();
	state_pos.y = 0.0f;
	helper->SetStaticPosition( state_pos );
	
	// When leaving, return the context to the default out of ruck state
	helper->SetState( CS_RUCK_FORMED_PRO_MODE, proPlayer->GetHumanPlayer(), true );
}

void RUContextualStateMachine::OnRuckProJoinRuck( ARugbyCharacter* proPlayer )
{
	MABASSERT( proPlayer ); // Make sure we actually have a pro player
	MABASSERT( proPlayer->GetHumanPlayer() ); // when we have a pro player, it should be a human one

	SIFGameWorld* game = helper->GetGame();
	FVector state_pos = game->GetBall()->GetCurrentPosition();
	state_pos.y = 0.0f;
	helper->SetStaticPosition( state_pos );
	
	// Set the state to a pro mode ruck half context, using our pro as the human
	//helper->SetState( CS_RUCK_FORMED_PRO_MODE_NON_RUCK_HALF, proPlayer->GetHumanPlayer(), true );

	// Our pro player wasn't the first in the ruck, so don't allow contest?
	helper->SetState( CS_RUCK_FORMED_PRO_MODE_NO_CONTEST, proPlayer->GetHumanPlayer(), true );
}

void RUContextualStateMachine::OnRuckProJoiningRuck( ARugbyCharacter* proPlayer )
{
	MABASSERT( proPlayer ); // Make sure we actually have a pro player
	MABASSERT( proPlayer->GetHumanPlayer() ); // when we have a pro player, it should be a human one

	SIFGameWorld* game = helper->GetGame();
	FVector state_pos = game->GetBall()->GetCurrentPosition();
	state_pos.y = 0.0f;
	helper->SetStaticPosition( state_pos );
	
	// While we join, don't show any context
	helper->SetState( CS_NONE, proPlayer->GetHumanPlayer(), true );
}

void RUContextualStateMachine::OnRuckProLeaveRuck( ARugbyCharacter* proPlayer )
{
	MABASSERT( proPlayer ); // Make sure we actually have a pro player
	MABASSERT( proPlayer->GetHumanPlayer() ); // when we have a pro player, it should be a human one

	SIFGameWorld* game = helper->GetGame();
	FVector state_pos = game->GetBall()->GetCurrentPosition();
	state_pos.y = 0.0f;
	helper->SetStaticPosition( state_pos );
	
	// When leaving, return the context to the default out of ruck state
	helper->SetState( CS_RUCK_FORMED_PRO_MODE, proPlayer->GetHumanPlayer(), true );
}

void RUContextualStateMachine::OnPlayerLeftRuck( ARugbyCharacter* player )
{
	MABASSERT(player);
	if(!player)
		return;// Just in case

	RUCareerModeManager* proModeMan = SIFApplication::GetApplication()->GetCareerModeManager();
	bool proMode = proModeMan->IsActive() && proModeMan->GetIsCareerModePro();

	if(!proMode)
		return;

	// If our pro player just stood up, and we can actually request ball actions, set up the context

	if(!SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(player))
		return;

	// Leaving during the ruck state is handled elsewhere. 
	if(SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhase() == RUGamePhase::RUCK)
		return;

	SIFGameWorld* game = helper->GetGame();
	if(game->GetStrategyHelper()->CanProPlayerRequestAction())
	{
		// Human playing?
		SSHumanPlayer* att_human = NULL;
		SSHumanPlayer* def_human = NULL;
		helper->GetHumanPlayers(att_human, def_human);

		// no attacking human player, don't care.
		if(!att_human)
			return;

		helper->SetState( CS_PRO_WITHOUT_BALL_ATT, att_human, true );
	}
}
#endif // ENABLE_PRO_MODE

void RUContextualStateMachine::OnRuckFinished(int)
{
	// Order of setting goes 1player	primary = if att ball holder if def defence
	//						 2player	primary = att ball holder secondary defence
	// Check for human players
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;
	helper->GetHumanPlayers(att_human, def_human);

	if ( att_human )
	{
		helper->SetState( CS_BALL_HOLDER, att_human, true );
	}

	if ( att_human && def_human )
	{
		helper->SetState( CS_DEFENCE, def_human, false );
		return;
	}
	else if ( def_human )
		helper->SetState( CS_DEFENCE, def_human, true );
	else if ( !att_human ) // no players
	{
		helper->SetState( CS_NONE, NULL, true );
		return;
	}

	// only one player games get here no secondary state
	helper->SetState( CS_NONE, NULL, false );
}

void RUContextualStateMachine::OnSetPlay(int set_play_index, FString set_play_name, ARugbyCharacter* player)
{
	// Order of setting goes 1player	primary = if att ball holder if def defence
	//						 2player	primary = att ball holder secondary defence
	// Check for human players
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;
	helper->GetHumanPlayers(att_human, def_human);

	if (att_human)
	{
		helper->SetState(CS_SETPLAY_STARTED, att_human, true);
	}

	// only one player games get here no secondary state
	//helper->SetState(CS_NONE, NULL, false);
}

void RUContextualStateMachine::OnPlayTheBallStarted()
{
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;
	helper->GetHumanPlayers(att_human, def_human);

	if (att_human)
	{
		helper->SetState(CS_PLAY_THE_BALL_STARTED, att_human, true);
		// Only display the set play UI if there is an attacking human player
		if (helper->GetGame()->GetHUDUpdaterContextual())
			helper->GetGame()->GetHUDUpdaterContextual()->Start2DContextualDisplay();
	}
}

void RUContextualStateMachine::OnPlayTheBallFinished()
{
	if (helper->GetPrimaryState()->GetState() == CS_SETPLAY_STARTED)
		return;

	helper->SetState(CS_NONE, NULL, true);
	helper->SetState(CS_NONE, NULL, false);
}


// When the conversion starts, we set up the quick conversion button
void RUContextualStateMachine::OnQuickConversionStart( ARugbyCharacter* kicker, bool isPlaceKick )
{
	// Dont care about fifteens at the moment, Mark_HES only wants the quick conversion for sevens since its more important
	if(isPlaceKick)
		return;

	// Check for human players
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;
	helper->GetHumanPlayers(att_human, def_human);
	RUCareerModeManager* manager = SIFApplication::GetApplication()->GetCareerModeManager();

	if ( att_human )
	{
		if (manager->IsActive() && manager->GetIsCareerModePro())
		{
			if (manager->IsProPlayer(kicker->GetAttributes()->GetDbId()))
			{
				helper->SetState( CS_QUICK_CONVERSION, att_human, true );
				return;
			}
		}
		else
		{
			helper->SetState( CS_QUICK_CONVERSION, att_human, true );
			return;
		}
	}
	//else

	helper->SetState( CS_NONE, NULL, true );
}

void RUContextualStateMachine::OnQuickConversionStop( ARugbyCharacter* kicker, bool isPlaceKick )
{
	MABUNUSED(kicker);

	// Dont care about fifteens at the moment, Mark_HES only wants the quick conversion for sevens since its more important
	if(isPlaceKick)
		return;

	// Just get rid of the context yo
	helper->SetState( CS_NONE, NULL, true );
}

void RUContextualStateMachine::OnBallCaught(ARugbyCharacter* catcher, bool, int)
{
	// Not in game phase, don't care.
	if(helper->GetGame()->GetGameState()->GetPhase() != RUGamePhase::PLAY)
		return;

	// Human playing?
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;
	helper->GetHumanPlayers(att_human, def_human);

	// no attacking human player, don't care.
	if(!att_human)
		return;

	// Not playing pro career, so don't care
	if(!SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() || 
		(SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && 
		SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModeCoach()))
		return;

	if(!SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayerOnField())
		return;

	// Our team mate just caught the ball! Show some non ball holding context stuff.
	if(catcher->GetAttributes()->GetTeam() == SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer()->GetAttributes()->GetTeam())
	{
		helper->SetState( CS_PRO_WITHOUT_BALL_ATT, att_human, true );
	}
}

void RUContextualStateMachine::OnBallCollected(ARugbyCharacter* collector, bool)
{
	// Not in game phase, don't care.
	if(helper->GetGame()->GetGameState()->GetPhase() != RUGamePhase::PLAY)
		return;

	// Human playing?
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;
	helper->GetHumanPlayers(att_human, def_human);

	// no attacking human player, don't care.
	if(!att_human)
		return;

	// Not playing pro career, so don't care
	if(!SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() || 
		(SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && 
		SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModeCoach()))
		return;

	if(!SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayerOnField())
		return;

	// Our team mate just collected the ball! Show some non ball holding context stuff.
	if(collector->GetAttributes()->GetTeam() == SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer()->GetAttributes()->GetTeam())
	{
		helper->SetState( CS_PRO_WITHOUT_BALL_ATT, att_human, true );
	}
}

void RUContextualStateMachine::OnPossessionChanged(RUTeam* newteam)
{
}


void RUContextualStateMachine::OnBallHolderChanged(ARugbyCharacter* new_holder, ARugbyCharacter* old_holder )
{

	// Order of setting goes 1player	primary = if att ball holder if def defence
	//						 2player	primary = att ball holder secondary defence
	// Check for human players
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;
	helper->GetHumanPlayers(att_human, def_human);

	// Special case here for pro mode. The old logic was failing when the primary state was set to NONE, causing it not to get set.
	if ( new_holder != NULL && att_human)
	{
		RUCareerModeManager* proModeMan = SIFApplication::GetApplication()->GetCareerModeManager();
		bool proMode = proModeMan->IsActive() && proModeMan->GetIsCareerModePro();

		if(proMode && SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayerOnField())
		{
			// If we're holding the ball, then we should show the ball holder context
			if(SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(new_holder))
				helper->SetState( CS_BALL_HOLDER, att_human, true );
			// Otherwise show the pro non holder context
			else
			{
				ARugbyCharacter* pro_player = SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer();
				SSRole* player_role = pro_player->GetRole();
				bool bound_to_ruck = false;

				// Don't show the pro request context when we're still in the ruck while the possession changes.
				if(player_role->RTTGetType() == RURoleRuck::RTTGetStaticType())
				{
					RURoleRuck* ruck_role = MabCast<RURoleRuck>( player_role );
					bound_to_ruck = ruck_role->IsBound();
				}

				MABLOGDEBUG("Current phase: %s", (helper->GetGame()->GetGameState()->GetPhaseName(helper->GetGame()->GetGameState()->GetPhase())));
				bool badState = helper->GetGame()->GetGameState()->GetPhase() == RUGamePhase::KICK_OFF ||
					helper->GetGame()->GetGameState()->GetPhase() == RUGamePhase::LINEOUT ||
					helper->GetGame()->GetGameState()->GetPhase() == RUGamePhase::SCRUM ||
					helper->GetGame()->GetGameState()->GetPhase() == RUGamePhase::MAUL;

				if(!bound_to_ruck && !badState && !pro_player->GetActionManager()->IsActionRunning(ACTION_TACKLEE))
				{
					if(att_human->GetPlayerController()->GetRugbyCharacter())
						MABLOGDEBUG("Going to set context to without ball attack for player: %s", att_human->GetPlayerController()->GetRugbyCharacter()->GetAttributes()->GetCombinedName().c_str());
					helper->SetState( CS_PRO_WITHOUT_BALL_ATT, att_human, true );
				}
			}

			// If we dont return here, it falls through to the end and sets the primary state back to CS_NONE
			return;
		}
	}

	if ( helper->GetPrimaryState() && helper->GetPrimaryState()->GetState() != CS_NONE && new_holder != NULL )
	{
		if ( att_human && helper->GetGame()->GetGameState()->GetPhase() != RUGamePhase::LINEOUT )
		{
			helper->SetState( CS_BALL_HOLDER, att_human, true );
		}

		if ( att_human && def_human )
		{
			helper->SetState( CS_DEFENCE, def_human, false );
			return;
		}
		else if ( def_human )
		{
			helper->SetState( CS_DEFENCE, def_human, true );
		}
		else if ( !att_human ) // no players
		{
			helper->SetState( CS_NONE, NULL, true );
			return;
		}

		// only one player games get here no secondary state
		helper->SetState( CS_NONE, NULL, false );
	}	
}

void RUContextualStateMachine::OnTry( bool /*success*/, bool /*penalty*/, ARugbyCharacter* /*try_scorer*/ )
{
	helper->SetState( CS_NONE, NULL, true );
}

void RUContextualStateMachine::OnHalfTime()
{
	helper->SetState( CS_NONE, NULL, true );
}

void RUContextualStateMachine::OnSimulation()
{
	helper->SetState( CS_NONE, NULL, true );
}

void RUContextualStateMachine::OnLineoutSelectPod (ARugbyCharacter* old_selection, ARugbyCharacter* new_selection)
{
}

void RUContextualStateMachine::OnLineoutReady(ARugbyCharacter*)
{
	// First do a pro game check to see if our pro player is even in the lineout
	bool proGame = SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro();
	RUGamePhaseLineOut* lineoutPhase = helper->GetGame()->GetGameState()->GetPhaseHandler<RUGamePhaseLineOut>();
	
	if(proGame && !lineoutPhase->GetIsProPlayerInLineout())
	{
		helper->SetState( CS_NONE, NULL, true );
		return;
	}


	// Check for human players
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;

	helper->GetHumanPlayers(att_human, def_human, *lineoutPhase);

	// Order of setting goes 1player	primary = def human behind 22, def human recieve, att human recieve
	//						 2player	primary = defence state, secondary =  def kick behind 22, def kick recieve
	bool two_player = def_human != NULL && att_human != NULL;

	if ( !two_player )
	{
		// No longer needed for the defending team
		/*if ( def_human != NULL )
		{
			helper->SetState( CS_LINEOUT_MOVE, def_human, true );
		}
		else */
		if ( att_human != NULL )
		{
			helper->SetState( CS_LINEOUT_MOVE, att_human, true );
		}
		else
		{
			helper->SetState( CS_NONE, NULL, true );
		}
	}
	else
	{
		helper->SetState( CS_LINEOUT_MOVE, att_human, true );
		helper->SetState( CS_LINEOUT_MOVE, def_human, false );
	}
}

void RUContextualStateMachine::OnLineoutTimeout()
{
	helper->SetState( CS_NONE, NULL, true );
	helper->SetState( CS_NONE, NULL, false );
}

void RUContextualStateMachine::OnLineoutThrow( bool, const FVector&, ARugbyCharacter*, THROW_TYPE, ARugbyCharacter* )
{
	helper->SetState( CS_NONE, NULL, true );
	helper->SetState( CS_NONE, NULL, false );
}

void RUContextualStateMachine::OnLineoutSlowmo (bool activate)
{
	
	// First do a pro game check to see if our pro player is even in the lineout
	bool proGame = SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro();
	RUGamePhaseLineOut* lineoutPhase = helper->GetGame()->GetGameState()->GetPhaseHandler<RUGamePhaseLineOut>();
	
	if(proGame && !lineoutPhase->GetIsProPlayerInLineout())
	{
		helper->SetState( CS_NONE, NULL, true );
		helper->SetState( CS_NONE, NULL, false );
		return;
	}

	if ( !activate )
	{
		helper->SetState( CS_NONE, NULL, true );
		helper->SetState( CS_NONE, NULL, false );

		return;
	}

	SIFGameWorld* game = helper->GetGame();

	MABASSERT(game->GetGameState()->GetPhase() == RUGamePhase::LINEOUT);
	RUGamePhaseLineOut* lineout_phase = game->GetGameState()->GetPhaseHandler<RUGamePhaseLineOut>();
	bool def_team_winning = false, att_team_winning = false;

	// Check for human players
	RUTeam* def_team = game->GetGameState()->GetDefendingTeam();
	RUTeam* att_team = game->GetGameState()->GetAttackingTeam();
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;
	helper->GetHumanPlayers(att_human, def_human, *lineout_phase);

	if ( def_team )
	{
		def_team_winning = lineout_phase->WillProbablyGetTheBall(def_team->GetIndex());
	}

	if ( att_team )
	{
		att_team_winning = lineout_phase->WillProbablyGetTheBall(att_team->GetIndex());
	}

	helper->GetGame()->GetHUDUpdaterContextual()->Reset();

	if ( def_human != NULL ) // && def_team_winning )
	{
		helper->SetState( CS_LINEOUT_TOP, def_human, true );
	}
	else if ( att_human != NULL ) //&& att_team_winning )
	{
		helper->SetState( CS_LINEOUT_TOP, att_human, true );
	}
	else
	{
		helper->SetState( CS_NONE, NULL, true );
	}
}

void RUContextualStateMachine::OnLineoutFinish()
{
	helper->SetState( CS_NONE, NULL, true );
	helper->SetState( CS_NONE, NULL, false );
}

void RUContextualStateMachine::OnScrumSignalled(RUTeam*)
{
	// Get rid off the state that we might have.
	// HES bug fix where the pro request context was showing when going into a scrum
	helper->SetState( CS_NONE, NULL, true );
}

void RUContextualStateMachine::OnScrumStart(RUTeam*, ARugbyCharacter*)
{
	// TODO: get rid of this callback
	// Do nothing, there's currently no context during the main phase of scrum
	UE_LOG(LogTemp, Display, TEXT("okay cool"));
}

void RUContextualStateMachine::OnScrumFinish()
{
	helper->SetState( CS_NONE, NULL, true );
}

void RUContextualStateMachine::OnScrumBallReleasePossible(RUTeam*)
{
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;

	RUGamePhaseScrum* phase = (helper->GetGame()->GetGameState()->GetPhase() != RUGamePhase::SCRUM) ? nullptr :
		helper->GetGame()->GetGameState()->GetPhaseHandler<RUGamePhaseScrum>();
	if (phase)
	{
		helper->GetHumanPlayers(att_human, def_human, *phase);
	}

	bool proGame = SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro();
	//RUGamePhaseScrum* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseScrum>();
	
	// In pro games, we only want to load this context if we're the scrum ruck half.
	if(proGame && SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer())
	{
		//SSRole* role = SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer()->GetRole();
		//MabTypeID player_role_id = role->RTTGetType();
		//if(player_role_id != RURoleRuckScrumHalf::RTTGetStaticType())
		//	return;
	}

	// Change the context depending on game mode, since sevens we dont have a number 8 for pickup
	RUContextStates context = // Nick WWS 7s to Womens 13s //SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_settings.GameModeIsR7() ? CS_SCRUM_BALL_RELEASED_SEVENS : 
		CS_SCRUM_BALL_RELEASED;

	if (att_human)
		helper->SetState( context, att_human, true );
	if (def_human)
		helper->SetState( context, def_human, false );
}

void RUContextualStateMachine::OnTackleeExit( ARugbyCharacter* tacklee )
{
	MABASSERT(tacklee);
	if(!tacklee)
		return;// Just in case

	RUCareerModeManager* proModeMan = SIFApplication::GetApplication()->GetCareerModeManager();
	bool proMode = proModeMan->IsActive() && proModeMan->GetIsCareerModePro();

	if(!proMode)
		return;

	// If our pro player just stood up, and we can actually request ball actions, set up the context

	if(!SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(tacklee))
		return;

	SIFGameWorld* game = helper->GetGame();
	if(game->GetStrategyHelper()->CanProPlayerRequestAction())
	{
		// Human playing?
		SSHumanPlayer* att_human = NULL;
		SSHumanPlayer* def_human = NULL;
		helper->GetHumanPlayers(att_human, def_human);

		// no attacking human player, don't care.
		if(!att_human)
			return;

		helper->SetState( CS_PRO_WITHOUT_BALL_ATT, att_human, true );
	}
}

void RUContextualStateMachine::OnTackleStart( const RUTackleResult& tackle_result)
{

	RUCareerModeManager* proModeMan = SIFApplication::GetApplication()->GetCareerModeManager();
	bool proMode = proModeMan->IsActive() && proModeMan->GetIsCareerModePro();

	if(!proMode)
		return;

	if(!SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayerOnField())
		return;

	if(tackle_result.tacklee == SIFApplication::GetApplication()->GetCareerModeManager()->GetProPlayer())
	{
		helper->SetState( CS_NONE, NULL, true );
		helper->SetState( CS_NONE, NULL, false );
	}
}

void RUContextualStateMachine::OnTackleContested( const RUTackleResult& /*tackle_result*/)
{
	// Check for human players
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;
	helper->GetHumanPlayers(att_human, def_human);

	bool two_player = def_human != NULL && att_human != NULL;

	if ( !two_player )
	{
		if ( def_human != NULL )
			helper->SetState( CS_TACKLE_CONSTESTED, def_human, true );
		else if ( att_human != NULL )
			helper->SetState( CS_TACKLE_CONSTESTED, att_human, true );
		else
			helper->SetState( CS_NONE, NULL, true );
	}
	else
	{
		helper->SetState( CS_TACKLE_CONSTESTED, att_human, true );
		helper->SetState( CS_TACKLE_CONSTESTED, def_human, false );
	}
}

void RUContextualStateMachine::OnMaulFormed(ARugbyCharacter*, ARugbyCharacter*)
{
	// Check for human players
	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;

	RUGamePhaseMaul* phase = (helper->GetGame()->GetGameState()->GetPhase() != RUGamePhase::MAUL) ? nullptr :
		helper->GetGame()->GetGameState()->GetPhaseHandler<RUGamePhaseMaul>();
	if (phase)
	{
		helper->GetHumanPlayers(att_human, def_human, *phase);
	}

	bool two_player = def_human != NULL && att_human != NULL;

	if ( !two_player )
	{
		RUContextStates defState = CS_MAUL_FORMED;
		RUContextStates attState = CS_MAUL_FORMED_CAN_RELEASE;
		
		bool proGame = SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro();

		if(proGame)
		{
			//RUGamePhaseMaul* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseMaul>();

			// Now check if the pro is inside or outside
			RUMaulState state = phase->GetCurrentMaulState();

			// Our pro is inside the defending team state
			if(state.defending_team_state.GetIsProPlayerInMaul())
				defState = CS_MAUL_PRO_FORMED_INSIDE;
			else
				defState = CS_MAUL_PRO_FORMED_OUTSIDE;
			
			// Our pro is inside the attacking team state
			if(state.attacking_team_state.GetIsProPlayerInMaul())
				attState = CS_MAUL_PRO_FORMED_CAN_RELEASE;
			else
				attState = CS_MAUL_PRO_FORMED_OUTSIDE;
		}
		
		if ( def_human != NULL )
			helper->SetState( defState, def_human, true );
		else if ( att_human != NULL )
			helper->SetState( attState, att_human, true );
		else
			helper->SetState( CS_NONE, NULL, true );
	}
	else
	{
		helper->SetState( CS_MAUL_FORMED_CAN_RELEASE, att_human, true );
		helper->SetState( CS_MAUL_FORMED, def_human, false );
	}
}

void RUContextualStateMachine::OnMaulBallReleased(RUTeam*, const FVector&, float )
{
	helper->SetState( CS_NONE, NULL, true );
}

void RUContextualStateMachine::OnMaulHandingOff()
{
	helper->SetState( CS_NONE, NULL, true );
}

void RUContextualStateMachine::OnMaulFinished( int /*maul_index*/ )
{
	helper->SetState( CS_NONE, NULL, true );
}

void RUContextualStateMachine::OnMaulPlayerJoined(ARugbyCharacter* player, RUZoneJoinType /*join_type*/)
{
	// we only care about the pro player right now
	if(!SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(player))
		return;

	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;
	helper->GetHumanPlayers(att_human, def_human);
	
	/*RUGamePhaseMaul* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseMaul>();
	RUMaulState state = phase->GetCurrentMaulState();
	bool proInsideAttacking = SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(state.attacking_team_state.team->GetProPlayer());*/

	RUContextStates defState = CS_MAUL_PRO_FORMED_INSIDE;
	RUContextStates attState = CS_MAUL_PRO_FORMED_CAN_RELEASE;
		
	if ( def_human != NULL )
		helper->SetState( defState, def_human, true );
	else if ( att_human != NULL )
		helper->SetState( attState, att_human, true );
	else
		helper->SetState( CS_NONE, NULL, true );
}

void RUContextualStateMachine::OnProTeamGetHoldBall(ARugbyCharacter* ball_holder)
{
	//MABASSERT(SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro());
	if(!SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() || !SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro())
		return;
	
	// Dont want to do this if we're the one getting the ball
	if(SIFApplication::GetApplication()->GetCareerModeManager()->IsProPlayer(ball_holder))
		return;

	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;
	helper->GetHumanPlayers(att_human, def_human);
	
	if ( def_human != NULL )
		helper->SetState( CS_PRO_WITHOUT_BALL_ATT, def_human, true );
	else if ( att_human != NULL )
		helper->SetState( CS_PRO_WITHOUT_BALL_ATT, att_human, true );
	else
		helper->SetState( CS_NONE, NULL, true );
}

void RUContextualStateMachine::OnProTeamGetReleaseBall()
{
	//MABASSERT(SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() && SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro());
	if(!SIFApplication::GetApplication()->GetCareerModeManager()->IsActive() || !SIFApplication::GetApplication()->GetCareerModeManager()->GetIsCareerModePro())
		return;
	
	helper->SetState( CS_NONE, NULL, true );
}

void RUContextualStateMachine::OnScreenTransitionStart()
{
	// hide 3d context while the screen is fading or transitioning
	if (helper->GetGame()->GetHUDUpdaterContextual())
	{
		helper->GetGame()->GetHUDUpdaterContextual()->Set3DDisplayHidden(true);
	}
}

void RUContextualStateMachine::OnScreenTransitionEnd()
{
	// hide 3d context while the screen is fading or transitioning
	if (helper->GetGame()->GetHUDUpdaterContextual())
	{
		helper->GetGame()->GetHUDUpdaterContextual()->Set3DDisplayHidden(false);
	}
}

void RUContextualStateMachine::OnBallholderCanForceBallStateChange( ARugbyCharacter* ball_holder, bool can_force_ball )
{
	if ( ball_holder && ball_holder->GetHumanPlayer() && can_force_ball && !can_force_ball_contextual_displayed )
	{
		helper->SetState( CS_PLAYER_CAN_FORCE_BALL, ball_holder->GetHumanPlayer() );
		can_force_ball_contextual_displayed = true;
	}
	else if ( !can_force_ball && can_force_ball_contextual_displayed )
	{
		helper->SetState( CS_NONE, NULL );
		can_force_ball_contextual_displayed = false;
	}
}

void RUContextualStateMachine::OnCoinTossStarted()
{
	MABBREAKMSG("DEPRECATED");

	SSHumanPlayer* att_human = NULL;
	SSHumanPlayer* def_human = NULL;
	SSHumanPlayer* nextPlayer = NULL;
	helper->GetHumanPlayers(att_human, def_human);
	bool hasHumanPlayer = def_human != NULL || att_human != NULL;

	RUContextStates state = CS_EXTRA_TIME_COIN_TOSS_HEADS_TAILS;

	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();

	// This is our next phase in the coin toss
	if(phase->GetPhase() == CTP_COIN_TOSS_HEADS_OR_TAILS)
		state = CS_EXTRA_TIME_COIN_TOSS_HEADS_TAILS;
	else  if(phase->GetPhase() == CTP_COIN_TOSS_KICK_OR_RECEIVE)
		state = CS_EXTRA_TIME_COIN_TOSS_KICK_RECEIVE;
	else  if(phase->GetPhase() == CTP_COIN_TOSS_NORTH_OR_SOUTH)
		state = CS_EXTRA_TIME_COIN_TOSS_NORTH_SOUTH;

	// Work out who should make the next decision
	SSTEAMSIDE nextDecisionMaker = SIDE_NONE;

	// CPU is playing alone, don't need a decision maker or screen context
	if(!hasHumanPlayer)
	{
		// Even if there are no human players, we still need a side
		nextDecisionMaker = phase->GetDecisionMakerForNextPhase();
		if(nextDecisionMaker == SIDE_NONE) nextDecisionMaker = SIDE_A;

		phase->SetCurrentDecisionMaker(nextDecisionMaker/*, true*/);
		helper->SetState( CS_NONE, NULL, true ); // Still want no context
	}
	else
	{
		// Work out who we think should make the next decision
		nextDecisionMaker = phase->GetDecisionMakerForNextPhase();
		MABASSERT(nextDecisionMaker == SIDE_A || nextDecisionMaker == SIDE_B || nextDecisionMaker == SIDE_NONE);

		// next decision maker is no one, just pick the first human player
		if(nextDecisionMaker == SIDE_NONE)
		{
			if(att_human != NULL)
				nextPlayer = att_human;
			else
				nextPlayer = def_human;

			// Get a non NONE side from the real next player
			nextDecisionMaker = nextPlayer->GetTeam()->GetSide();
		}
		else
		{
			// Which human was the next decision maker side? And are they actually human
			if(att_human != NULL && att_human->GetTeam()->GetSide() == nextDecisionMaker)
				nextPlayer = att_human;
			else if(def_human != NULL && def_human->GetTeam()->GetSide() == nextDecisionMaker)
				nextPlayer = def_human;
		}

		// Use the data we have and set the decision maker and the context
		phase->SetCurrentDecisionMaker(nextDecisionMaker/*, nextPlayer == NULL*/);
		helper->SetState( nextPlayer != NULL ? state : CS_NONE, nextPlayer, true );
	}
}