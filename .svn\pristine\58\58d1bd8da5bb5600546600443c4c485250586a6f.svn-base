/*--------------------------------------------------------------
|        Copyright (C) 1997-2008 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

// SIF
#include "WWUILeaderboard.h"
#include "UI/Populators/WWUILeaderboardPopulator.h"
#include "WWUIListField.h"
#include "Runtime/Core/Public/Internationalization/CulturePointer.h"
#include "Runtime/Core/Public/Internationalization/Culture.h"
#include "Runtime/Core/Public/Internationalization/FastDecimalFormat.h"
#include "PanelWidget.h"
#include "TextBlock.h"

//#include "SIFLanguage.h"



#if PLATFORM_WINDOWS
#undef GetNumberFormat //THANKS MS!!!
#endif

// The initial leaderboard filter -- currently set to FRIEND.
static const UWWUILeaderboard::FILTER STARTING_FILTER( UWWUILeaderboard::FRIEND );

UWWUILeaderboard::UWWUILeaderboard() :
	leaderboard_id(),
	title(),
	custom_columns(TArray<MabString>()),
	populator(),
	full_col_name_list(),
	//field_formatter( _field_formatter ),
	current_filter(),
	num_overall_entries(),
	display_num(),
	row_data(),
	my_rank()
{
}

UWWUILeaderboard::UWWUILeaderboard(
			LeaderboardIDType& _leaderboard_id, 
			FString& _title, 
			TArray< LeaderboardColumnDataType > _custom_columns, 
			UWWUILeaderboardPopulator* _populator, 
			TArray< FString >* const _full_name_col_list
			//RULeaderboardFieldFormatter* _field_formatter
		)
	:
		leaderboard_id( _leaderboard_id ),
		title( _title ),
		custom_columns( _custom_columns ),
		populator ( _populator ),
		full_col_name_list( ( _full_name_col_list == NULL ) ? TArray< FString >() : ( *_full_name_col_list ) ),
		//field_formatter( _field_formatter ),
		current_filter ( STARTING_FILTER ),
		num_overall_entries ( 0 ),
		display_num ( 0 ),
		row_data(),
		my_rank( 0 )
{
	if (populator != NULL)
	{
		leaderboardDelegate.BindUObject(populator, &UWWUILeaderboardPopulator::Update);
	}
}

UWWUILeaderboard::~UWWUILeaderboard()
{
	if (populator != NULL) 
	{
		leaderboardDelegate.Unbind();
	}
}

// Convert this row to a 1D vector of strings
void UWWUILeaderboardDataRow::To1DVector( TArray< FString >& vector_out ) const
{
	FCultureRef current_locale = FInternationalization::Get().GetCurrentLocale();
	FDecimalNumberFormattingRules modified_format = current_locale.Get().GetDecimalNumberFormattingRules();
	modified_format.SecondaryGroupingSize = 0;  //#MB - check if this is the number of decimal places
	vector_out.Add(FString::FromInt(static_cast<int>(rank)));
	vector_out.Add(player_name);
	for (FString str : addl_strings)
	{
		vector_out.Add(str);
	}
}

// \brief Request specified stats from this leaderboard
bool UWWUILeaderboard::RequestData( int min_display_index, UWWUILeaderboard::FILTER filter_in, const FString& ui_elem_name, bool switch_on_non_default_filter )
{
#ifdef ENABLE_NO_STATS
	// Just don't do anything.
	return true;
#else
	MABASSERTMSG( populator != NULL && display_num != 0, "Leaderboard is insufficiently prepared for a RequestData call!" );
	if ( populator == NULL || display_num == 0 ) return false;

	// Communicates if we need to make a stats request or, alternatively, if the data we're requesting is cached
	int max_display_index = 0;
	int min_request_index = -1;

	// If we've left it to the default, then use the currently-selected filter; if the client is explicitly changing this, then
	//	it means there's a newly-selected filter
	if ( filter_in == NUM_FILTERS )
	{
		filter_in = current_filter;
	} else if ( switch_on_non_default_filter ) {
		current_filter = filter_in;
	}

	// Now we're checking to see if we've already performed a read on this filter and know that we have no results.
	//	If so, we can synchronously send an empty message.
	MabMap< FILTER, bool >::const_iterator has_no_results_iter = has_no_results.find( current_filter );
	if ( has_no_results_iter != has_no_results.end() && ( has_no_results_iter->second ) )
	{
		TArray<FString> data;
		leaderboardDelegate.Execute(UWWUILeaderboardPopulatorMessage( leaderboard_id, 0, 0, current_filter, data, ui_elem_name ));
		return true;
	}

	// Check first to see if we're already collecting data on this set of info. If so, don't do anything, just return false
	if ( HasDuplicateRequest( current_filter, min_display_index ) )
	{
		return false;
	}

	// filter_in here is only ever going to be FRIEND or OVERALL unless switch_on_non_default_filter is true,
	//	i.e. we're actively switching to My Score.
	switch ( filter_in )
	{
	case OVERALL:
#if /*(PLATFORM_XBOXONE) &&*/ defined(ENABLE_SEVENS_MODE) && !NRL_USE_13S_MODE
	case OVERALL_SEVEN:
#endif
	case FRIEND:
#if /*(PLATFORM_XBOXONE) &&*/ defined(ENABLE_SEVENS_MODE) && !NRL_USE_13S_MODE
	case FRIEND_SEVEN:
#endif
		// If we've collected no data for the current filter, we'll set the requested min_index to the displayed min_index.
		if ( row_data.find( current_filter ) == row_data.end() )
		{
			min_request_index = min_display_index;
		}

		// If we've left it to the default, then use the currently-selected row; if the client is explicitly changing this, then
		//	it means there's a newly-selected top row
		if ( min_display_index == -1 && selected_top_row.find( current_filter ) != selected_top_row.end() )
		{
			min_display_index = selected_top_row[ current_filter ];
		} else {
			// If it's left to default but selected_top_row hasn't been set, set it to the top-most element
			if ( min_display_index == -1 )
			{
				min_display_index = 0;
			}
			selected_top_row[ current_filter ] = min_display_index;
		}

		// max_display_index is just min + the number of entries to display, off-by-one.
		max_display_index = min_display_index + display_num - 1;
		break;

	case MY_SCORE:
#if /*(PLATFORM_XBOXONE) &&*/ defined(ENABLE_SEVENS_MODE) && !NRL_USE_13S_MODE
	case MY_SCORE_SEVEN:
#endif
		{ 
		if( my_rank == 0 )
		{
			min_request_index = 0;
		}
		else 
		{ 
			int tempNum = num_overall_entries - display_num ; 
			MabMath::ClampLower(tempNum, 0 );
			// Perform a fairly convoluted calculation to determine the rank of the top row. Off-by-one.
			min_display_index =    MabMath::Min( my_rank - 1, static_cast< unsigned int >(tempNum) );
															// The min of either the serial rank OR...
														// ...the min of display_num ranks up from the
														//	max number of ranks, or 1. (Used to take care
														//	of the case when my rank's within the last few rows.
		}

		// Max index needs to be capped at the total number of entries. Off-by-one.
		// NOTE: Might be performed below, but hooray for pedantry!
			max_display_index = MabMath::Min( ( min_display_index + static_cast< unsigned int >( display_num ) ), num_overall_entries ) - 1;
		}
		break;
	default:
		MABBREAKMSG( "Unhandled filter in RequestData!" );
	}

	// Now check to see if our index is already covered by an existing request
	// NOTE: At the moment we don't want to check on max value--if we're in progress on elements 0 through 100, and we
	//	start looking for element 1, we don't want to perform a new request just because we don't have element 101. 
	
	// Only perform this if we haven't already specified an index to request
	if ( min_request_index == -1 )
	{
		// Discover if the sought-after element already exists without a search
		int index_ctr = 0, tmp_index = min_display_index;
		
		int iteration_max = max_display_index; 
		// Merged from Shatter - probably a fix for PS3 specific limitation regarding
		// reading a '50 friend at once' request limit. This logic will constrain us to
		// display 50 friends maximum on PS3. It is possible that this will cause problems
		// on Xbox 360, however it is unlikely. Consult Jeremy, Mark and Antony all at once
		// to fully understand this if you are not one of them.
		if( (current_filter == FRIEND) 
#if /*(PLATFORM_XBOXONE) &&*/ defined(ENABLE_SEVENS_MODE) && !NRL_USE_13S_MODE
			|| (current_filter == FRIEND_SEVEN)
#endif
			)
			iteration_max = (int) row_data[ current_filter ].size(); 
		// for the indices between min and max index...
		for ( ; tmp_index <= iteration_max; ++tmp_index, ++index_ctr )
		{
			// determine first if the filter is already mapped, then if this index is already mapped
			if ( row_data.find( current_filter ) != row_data.end() )
			{
				// It's also possible that we've already mapped the filter, and we know there are fewer entries
				//	available on the leaderboard than the requested max_index. The behaviour will be different for OVERALL versus
				//	FRIEND; for friends we assume we've seen all the available rows, and for OVERALL we don't
				//	assume that, we check against num_overall_entries.
				if (	( current_filter == FRIEND && (int) row_data[ FRIEND ].size() <= tmp_index ) ||
						( ( current_filter == OVERALL || current_filter == MY_SCORE ) && (int) num_overall_entries <= tmp_index )
#if /*(PLATFORM_XBOXONE) &&*/ defined(ENABLE_SEVENS_MODE) && !NRL_USE_13S_MODE
						|| ( current_filter == FRIEND_SEVEN && (int) row_data[ FRIEND_SEVEN ].size() <= tmp_index ) ||
						( ( current_filter == OVERALL_SEVEN || current_filter == MY_SCORE_SEVEN ) && (int) num_overall_entries <= tmp_index )
#endif
						 )
				{
					// We first of all need to specify that our max_display_index is no longer the requested max_display_index
					//	it's the index we're trying to fulfill but failed on, minus 1.
					max_display_index = tmp_index - 1;
					MabMath::ClampLower(max_display_index, min_display_index);  // Safety net.
					break;
				}

				// We now know that we're not maxed out and that we've already mapped this filter. Now determine if we've
				//	mapped this specific field.
				// NOTE: the secondary key into row_data is rank, which is off-by-one index.
				if ( row_data[ current_filter ].find( tmp_index + 1) == row_data[ current_filter ].end() )
				{
					MABASSERTMSG( current_filter != FRIEND, "Unexpected behaviour: It's assumed that all of your friends reads will be performed once and then cached; we appear to have missed one on our previous friends request." );
					if ( current_filter == FRIEND
#if /*(PLATFORM_XBOXONE) &&*/ defined(ENABLE_SEVENS_MODE) && !NRL_USE_13S_MODE
						|| current_filter == FRIEND_SEVEN
#endif
						) 
					{
						// The only way this might happen is if you add a friend during a leaderboard session; 
						//	that's fine, just clear all of your cached friends reads.
						ClearCache( current_filter );
						min_request_index = 0;
					} else {
						// In the case of OVERALL and My Score, however, we want to maximise the size of the read
						//	we're making, and so we should change the min value read to this rank, the first rank
						//	that isn't found.
						min_request_index = tmp_index;
					}
					break;
				}
			}
		}

		// If we still haven't found a reason to make a request....
		if ( min_request_index == -1 )
		{
			// We now know that our request is complete because of our cacheing. Sort our data if we're on the 
			//	friend filter, convert from indices to UWWUILeaderboardDataRows and transform those into strings.
			TArray< FString > tmp_response_list;
			MabMath::ClampLower(max_display_index, min_display_index); 

			for ( int index_ctr2 = min_display_index; index_ctr2 <= max_display_index; ++index_ctr2 )
			{
				RankToRowMap &rank_row_map = row_data[ current_filter ];
				UWWUILeaderboardDataRow *row = rank_row_map[index_ctr2 + 1 ];

				if(row != NULL)
				{
					row->To1DVector( tmp_response_list );
				}
				else
				{
					MABBREAKMSG("null rows should never be in here. something has most likely looked up a row index that didn't exist. maps in this situation will create a null pointer.");
					rank_row_map.erase(index_ctr2 + 1);
					max_display_index = MabMath::Min(max_display_index, (int)rank_row_map.size());
				}
			}

			UWWUILeaderboardPopulatorMessage cached_message = UWWUILeaderboardPopulatorMessage( leaderboard_id, min_display_index, max_display_index, current_filter, tmp_response_list, ui_elem_name );
			leaderboardDelegate.Execute(cached_message); 
			return true;
		}
	}

	// ---------- At this point we know we'll have to query the stats database. ----------------
	RequestTuple tmp_req_tuple( current_filter, min_display_index, max_display_index, ui_elem_name, filter_in );

	// filter_in here is only ever going to be FRIEND or OVERALL unless switch_on_non_default_filter is true,
	//	i.e. we're actively switching to My Score.
	switch ( filter_in )
	{
	case OVERALL:
#if /*(PLATFORM_XBOXONE) &&*/ defined(ENABLE_SEVENS_MODE) && !NRL_USE_13S_MODE
	case OVERALL_SEVEN:
#endif
		OverallDataRequestHelper( min_request_index, tmp_req_tuple );
		break;
	case FRIEND:
#if /*(PLATFORM_XBOXONE) &&*/ defined(ENABLE_SEVENS_MODE) && !NRL_USE_13S_MODE
	case FRIEND_SEVEN:
#endif
		FriendDataRequestHelper( min_request_index, tmp_req_tuple );
		break;
	case MY_SCORE:
#if /*(PLATFORM_XBOXONE) &&*/ defined(ENABLE_SEVENS_MODE) && !NRL_USE_13S_MODE
	case MY_SCORE_SEVEN:
#endif
		if( my_rank == 0 )
		{
			MyScoreDataRequestHelper( tmp_req_tuple );
		} else {
			OverallDataRequestHelper( min_request_index, tmp_req_tuple );
		}
		break;
	default:
		MABBREAKMSG( "Unhandled case in RequestData!" );
		break;
	}
	return false;
#endif
}

// \brief Set the player's filter to the next filter, where "next" is defined by the order in enum FILTER above.
UWWUILeaderboard::FILTER UWWUILeaderboard::IncrementFilter( bool clear_cache, bool start_from_top, bool* const is_sync_out, const FString& ui_elem_name )
{
	int starting_index = ( start_from_top ) ? 0 : -1;
	FILTER new_filter = (FILTER) ( ( (unsigned int) current_filter + 1 ) % (unsigned int) NUM_FILTERS );
	current_filter = new_filter;
	if ( clear_cache ) ClearCache( current_filter );
	bool tmp_bool = RequestData( starting_index, NUM_FILTERS, ui_elem_name );
	if ( is_sync_out != NULL ) *is_sync_out = tmp_bool;
	return new_filter;
}

UWWUILeaderboard::FILTER UWWUILeaderboard::NextFilter(int dir)
{
	int current_filter_num = current_filter;
	FILTER new_filter = (FILTER)0;
	if (dir < 0)
	{
		--current_filter_num;
		new_filter = (FILTER)((current_filter_num < 0) ? (unsigned int)NUM_FILTERS - 1 : current_filter_num);
	}
	else
	{
		current_filter_num++;
		new_filter = (FILTER)(current_filter_num % (unsigned int)NUM_FILTERS);
	}
	return(new_filter);
}

// \brief Set the player's filter to the previous filter, where "previous" is defined by the order in enum FILTER above.
UWWUILeaderboard::FILTER UWWUILeaderboard::DecrementFilter( bool clear_cache, bool start_from_top, bool* const is_sync_out, const FString& ui_elem_name )
{
	int starting_index = ( start_from_top ) ? 0 : -1;
	int current_filter_num = (int) current_filter - 1;
	FILTER new_filter = ( current_filter_num < 0 ) ? (FILTER) ( ( unsigned int ) NUM_FILTERS - 1 ) : (FILTER) current_filter_num;
	current_filter = new_filter;
	if ( clear_cache ) ClearCache( current_filter );
	bool tmp_bool = RequestData( starting_index, NUM_FILTERS, ui_elem_name );
	if ( is_sync_out != NULL ) *is_sync_out = tmp_bool;
	return new_filter;
}

// \brief Set the player's filter to the given value.
UWWUILeaderboard::FILTER UWWUILeaderboard::SetFilter( UWWUILeaderboard::FILTER _new_filter, bool clear_cache, bool start_from_top, bool* const is_sync_out, const FString& ui_elem_name )
{
	//ASSERTMSG( _new_filter != NUM_FILTERS, "SetFilter cannot be set to NUM_FILTERS!" );
	if ( _new_filter == NUM_FILTERS ) return NUM_FILTERS;
	int starting_index = ( start_from_top ) ? 0 : -1;
	current_filter = _new_filter;
	if ( clear_cache ) ClearCache( _new_filter );
	bool tmp_bool = RequestData( starting_index, NUM_FILTERS, ui_elem_name );
	if ( is_sync_out != NULL ) *is_sync_out = tmp_bool;
	return current_filter;
}

// \brief Clear cache for specified filter
void UWWUILeaderboard::ClearCache( UWWUILeaderboard::FILTER filter_in )
{
	if ( filter_in == NUM_FILTERS )
	{
		FilterToRowMap::iterator iter1 = row_data.begin();
		while ( iter1 != row_data.end() )
		{
			RankToRowMap::iterator iter2 = iter1->second.begin();
			while( iter2 != iter1->second.end() )
			{
				MabMemDelete( iter2->second );
				iter1->second.erase( iter2->first );
				iter2 = iter1->second.begin();
			}
			row_data.erase( iter1->first );
			iter1 = row_data.begin();
		}
		has_no_results.clear();
	} else {
		RankToRowMap::iterator iter = row_data[ filter_in ].begin();
		while ( iter != row_data[ filter_in ].end() )
		{
			MabMemDelete( iter->second );
			row_data[ filter_in ].erase( iter->first );
			iter = row_data[ filter_in ].begin();
		}
		has_no_results[ filter_in ] = false;
	}

	// Always clear the rank variable
	my_rank = 0;
}

unsigned int UWWUILeaderboard::GetNumTotalEntries( FILTER requested_filter ) const
{
	if ( requested_filter == NUM_FILTERS ) requested_filter = current_filter;

	// Note: We're taking this assert out for now, but it should be put back in, with the nuance that
	//	we should cache zero-result friends reads and distinguish them from unread data. See #10189.
	//MABASSERTMSG( current_filter_internal != FRIEND || data_index_mapping.find( current_filter_internal ) != data_index_mapping.end(), "Calling GetNumTotalEntries on a FRIEND filter before the friend data read has completed!" );
	if ( requested_filter == FRIEND
#if /*(PLATFORM_XBOXONE) &&*/ defined(ENABLE_SEVENS_MODE) && !NRL_USE_13S_MODE
	|| requested_filter == FRIEND_SEVEN
#endif
		)
	{
		FilterToRowMap::const_iterator filter_find = row_data.find( requested_filter );
		if ( filter_find == row_data.end() )
			// Check to see if there's a request already in-progress
			if ( HasDuplicateRequest( requested_filter, 0 ) )
			{
				// Maxint
				return ~(0u);
			} else {
				return 0;
			}
		else
			return static_cast< unsigned int >( filter_find->second.size() );
	} else {
		return num_overall_entries;
	}
}


void UWWUILeaderboard::SetStatusMessage(UWWUIListField* field, FString& text ) const
{
	if (field && field->ScreenRef)
	{
		// Get the status item
		UTextBlock* widget = Cast<UTextBlock>(field->ScreenRef->FindChildOfTemplateWidget(field, "StatusText"));

		if (widget)
		{
			// Set widget text
			widget->SetText(FText::FromString(text));
		}
	}
}

FString UWWUILeaderboard::GetStatusMessage(UWWUIListField* field) const
{
	if (field && field->ScreenRef)
	{
		// Get the status item
		UTextBlock* widget = Cast<UTextBlock>(field->ScreenRef->FindChildOfTemplateWidget(field, "StatusText"));

		if (widget)
		{
			return widget->Text.ToString();
		}
	}
	return "";
}


// ------------------- UWWUILeaderboardQueue -------------------
UWWUILeaderboardQueue::UWWUILeaderboardQueue(UWWUILeaderboardPopulator* _populator )
	:	populator ( _populator ),
		current_leaderboard ( NULL ),
		current_leaderboard_index ( 0 ),
		display_num ( 0 )
{
	leaderboard_queue = TArray< UWWUILeaderboard*>();
}

void UWWUILeaderboardQueue::CleanUp()
{
	leaderboard_queue.Empty();
}

// Set the Leaderboard populator not only of yourself but of all the leaderboards in the queue
void UWWUILeaderboardQueue::SetLeaderboardPopulator( UWWUILeaderboardPopulator* _populator )
{
	MABASSERTMSG( _populator != NULL, "The populator is being set to NULL!" );
	populator = _populator;
	if (leaderboard_queue.IsValidIndex(0))
	{
		for (UWWUILeaderboard* board_iter : leaderboard_queue)
		{
			board_iter->populator = populator;
			board_iter->leaderboardDelegate.BindUObject(populator, &UWWUILeaderboardPopulator::Update);
		}
	}
}

// Set the display_num not only of yourself but of all the leaderboards in the queue
void UWWUILeaderboardQueue::SetDisplayNum( int _display_num )
{
	MABASSERTMSG( _display_num > 0, "Display num is being set to a non-sensical value!" );
	display_num = _display_num;
	if (leaderboard_queue.IsValidIndex(0))
	{
		for (UWWUILeaderboard* board_iter : leaderboard_queue)
		{
			board_iter->display_num = display_num;
		}
	}
}

// \brief Set the leaderboard to a given leaderboard
UWWUILeaderboard* UWWUILeaderboardQueue::SetLeaderboard( UWWUILeaderboard* const new_leaderboard, bool clear_cache, bool start_from_top, bool* const is_sync_out, const FString& ui_elem_name )
{
	MABASSERTMSG( current_leaderboard != NULL && leaderboard_queue.Num() > 0, "Trying to SetLeaderboard before Queue has been initialised!" );
	if( current_leaderboard == NULL ) return NULL;

	UWWUILeaderboard::FILTER old_filter = UWWUILeaderboard::NUM_FILTERS;
	if ( clear_cache ) current_leaderboard->ClearCache();

	int i = 0;
	if (leaderboard_queue.IsValidIndex(0))
	{
		for (UWWUILeaderboard* iter : leaderboard_queue)
		{
			if (*iter == *new_leaderboard)
			{
				old_filter = current_leaderboard->GetFilter();
				current_leaderboard_index = i;
				current_leaderboard = iter;
				break;
			}
		}
	}

	//MABASSERTMSG( iter != leaderboard_queue.end(), "Leaderboard specified in SetLeaderboard could not be found in leaderboard_queue!" );
	//if ( iter == leaderboard_queue.end() ) return NULL;

	return LeaderboardChangeHelper( old_filter, start_from_top, is_sync_out, ui_elem_name );
}

// Set the leaderboard to the leaderboard with the given index
UWWUILeaderboard* UWWUILeaderboardQueue::SetLeaderboard( unsigned int new_leaderboard_index, bool clear_cache, bool start_from_top, bool* const is_sync_out, const FString& ui_elem_name )
{
	MABASSERTMSG( (int)new_leaderboard_index < leaderboard_queue.Num(), "Specified index does not exist in leaderboard_queue!" );
	if ((int)new_leaderboard_index >= leaderboard_queue.Num())
	{
		return NULL;
	}

	if (clear_cache && current_leaderboard)
	{
		current_leaderboard->ClearCache();
	}

	UWWUILeaderboard::FILTER old_filter = current_leaderboard ? current_leaderboard->GetFilter() : UWWUILeaderboard::FRIEND;
	current_leaderboard_index = (int) new_leaderboard_index;
	if (leaderboard_queue.IsValidIndex(current_leaderboard_index))
	{
		current_leaderboard = leaderboard_queue[current_leaderboard_index];
	}
	return LeaderboardChangeHelper( old_filter, start_from_top, is_sync_out, ui_elem_name );
}

// \brief Helper function for all methods involving changing leaderboards
UWWUILeaderboard* UWWUILeaderboardQueue::LeaderboardChangeHelper( UWWUILeaderboard::FILTER old_filter, bool start_from_top, bool* const is_sync_out, const FString& ui_elem_name )
{
	if ( display_num == 0 || populator == NULL ) return NULL;

	// If start_from_top is true, then we start from the first index
	//	This field will be overridden for the MY_SCORE filter
	unsigned int top_index = ( start_from_top ) ? 0 : -1;

	// Note that we overwrite the filter with old_filter rather than calling SetFilter, because we potentially
	//	may also need to set top_index.
	bool tmp_bool = current_leaderboard->RequestData( top_index, old_filter, ui_elem_name );
	if ( is_sync_out != NULL ) *is_sync_out = tmp_bool;
	return current_leaderboard;
}

void UWWUILeaderboardQueue::FlushAllCaches()
{
	if (leaderboard_queue.IsValidIndex(0))
	{
		for (UWWUILeaderboard* iter : leaderboard_queue)
		{
			iter->ClearCache();
		}
	}
}

void UWWUILeaderboardQueue::ResetAllFilters()
{
	if (leaderboard_queue.IsValidIndex(0))
	{
		for (UWWUILeaderboard* iter : leaderboard_queue)
		{
			// Note that this is actually pretty hacky--the filter field shouldn't be able to be set by anyone but the
			//	application But the friending preexists this hack, and it saves us from having to 
			//	set up a "IgnoreRequest" flag along with the SetFilter.
			iter->current_filter = STARTING_FILTER;
		}
	}
}

bool UWWUILeaderboard::FriendSortComparator::operator()(const UWWUILeaderboardDataRow& left, const UWWUILeaderboardDataRow& right)
{
	return left.rank < right.rank;
}

MabUInt32 UWWUILeaderboard::RequestTuple::getLeaderboardID()
{
	switch (filter)
	{
	case FRIEND:
	case MY_SCORE:
	case OVERALL:
		return 0;
		break;
#if defined ENABLE_SEVENS_MODE && !NRL_USE_13S_MODE
	case FRIEND_SEVEN:
	case MY_SCORE_SEVEN:
	case OVERALL_SEVEN:
		return 1;
		break;
#endif
	default:
		return 0;
	}
}
