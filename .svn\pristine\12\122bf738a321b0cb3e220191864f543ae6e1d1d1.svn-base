/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/
//#rc3_legacy_pch #include "precompiled.h"
#include "SIFInGameHelpers.h"

// Debug includes
#ifdef BUILD_DEBUG
#include "SIFGameFlowNode.h"
#include "SIFFlowConstants.h"
#include "SIFWindow.h"
#endif

// Mab includes
/*#rc3_legacy_include
#include "MabLuaTypeDatabase.h"
#include <MabUINode.h>
*/
#include "Mab/Net/MabNetworkManager.h"

//SIF
#include "RugbyGameInstance.h"
#include "Character/RugbyPlayerController.h"
#include "Match/SIFGameWorld.h"
#include "Match/SIFUIConstants.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Match/HUD/RUHUDUpdater.h"
#include "Match/HUD/RU3DHUDManager.h"
#include "Match/RugbyUnion/RUStadiumManager.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/PhaseHandlers/RUGamePhaseExtraTimeToss.h"
#include "Match/RugbyUnion/Statistics/RUStatisticsSystem.h"
#include "Match/PlayerProfile/SIFPlayerProfileManager.h"
#include "Match/PlayerProfile/SIFPlayerProfilePropertyDefs.h"
#include "Networking/SIFOnlineConstants.h"

//Local
#include "SIFAudioHelpers.h"
#include "SIFGameHelpers.h"
#include "SIFUIHelpers.h"

//UI
#include "UI/Screens/WWUIScreenInGameInjury.h"

#include "UI/GeneratedHeaders/ScreenKeys_UI_Namespace.h"
#include "Commentary/RUCommentary.h"
#include "RUPMPDirector.h"
#include "Mab/Utility/MabTranslationManager.h"
#include "FlowNodes/FlowControlManager.h"
#include "UI/Screens/WWUIScreenPauseMenu.h"
#include "FlowNodes/FlowNode.h"
#include "Rugby/SIFFlowConstants.h"

/*#rc3_legacy_include
#include "SIFMatchmakingHelpers.h"

#include "SIFWindowSystem.h"
#include "SSGameTimer.h"


#include "RUSubstitutionManager.h"
#include "RUPlayerAttributes.h"

#include "SIFLevelPostSettings.h"
#include "SSPostEffectsManager.h"

#include "SIFAchievementChecker.h"
#include "RURugbyDollarsChecker.h"

#include "SIFUIInputAdapter.h"

#include "MabTranslationManager.h"

#if PLATFORM_XBOXONE
#include "XboxOneMabMatchMaking.h"
#endif
//*/


void SIFInGameHelpers::RegisterFunctions()
{
	/*#rc3_legacy_registerlua
	MabLuaTypeDatabase::RegisterFunction( "IGIsPauseMenuAvailable",	"bool",	"",		MABLUA_FUNCTION_PTR( SIFInGameHelpers::IsPauseMenuAvailable ) );
	MabLuaTypeDatabase::RegisterFunction( "IGShowPauseDisabledIcon",	"void",	"", MABLUA_FUNCTION_PTR( SIFInGameHelpers::ShowPauseDisabledIcon ) );
	MabLuaTypeDatabase::RegisterFunction( "IGRaisePauseMenu",	"void",	"",			MABLUA_FUNCTION_PTR( SIFInGameHelpers::RaisePauseMenu ) );
	MabLuaTypeDatabase::RegisterFunction( "IGDismissPauseMenu",	"void",	"",			MABLUA_FUNCTION_PTR( SIFInGameHelpers::DismissPauseMenu ) );

	MabLuaTypeDatabase::RegisterFunction( "IGRaiseInjurySubstitutionMenu",			RaiseInjurySubstitutionMenu );
	MabLuaTypeDatabase::RegisterFunction( "IGDismissInjurySubstitutionMenu",		DismissInjurySubstitutionMenu );

	MabLuaTypeDatabase::RegisterFunction( "IGIsGameOver", "bool", "",				MABLUA_FUNCTION_PTR( SIFInGameHelpers::IsGameOver ) );

	MabLuaTypeDatabase::RegisterFunction( "IGSetPlayerTeam",						SetPlayerTeam );
	MabLuaTypeDatabase::RegisterFunction( "IGSetOnlinePlayerTeam",					SetOnlinePlayerTeam );
	MabLuaTypeDatabase::RegisterFunction( "IGGetPlayerTeam",						GetPlayerTeam );
	MabLuaTypeDatabase::RegisterFunction( "IGClearPlayerTeams",						ClearPlayerTeams );
	MabLuaTypeDatabase::RegisterFunction( "IGSetRunningPlayerTeam",					SetRunningPlayerTeam );
	MabLuaTypeDatabase::RegisterFunction( "IGGetRunningPlayerTeam",					GetRunningPlayerTeam );
    MabLuaTypeDatabase::RegisterFunction( "IGGetRunningPlayerTeamId",				GetRunningPlayerTeamId );

	MabLuaTypeDatabase::RegisterFunction( "IGGetNumHumanPlayersOnTeam",				GetNumHumanPlayersOnTeam );
	MabLuaTypeDatabase::RegisterFunction( "IGIsHumanPlayersOnBothTeams",			IsHumanPlayersOnBothTeams );
	MabLuaTypeDatabase::RegisterFunction( "IGLockAllHumanPlayerInputs",				LockAllHumanPlayerInputs );	

	// Replays
	MabLuaTypeDatabase::RegisterFunction( "IGPlayReplay",							PlayReplay );
	MabLuaTypeDatabase::RegisterFunction( "IGRewindReplay",							RewindReplay );
	MabLuaTypeDatabase::RegisterFunction( "IGFastForwardReplay",					FastForwardReplay );
	MabLuaTypeDatabase::RegisterFunction( "IGStopReplay",							StopReplay );

	// UI team management
	MabLuaTypeDatabase::RegisterFunction( "IGUpdateTeamLineup",						UpdateTeamLineup );
	MabLuaTypeDatabase::RegisterFunction( "IGTeamLineupComplete",					TeamLineupComplete );
	MabLuaTypeDatabase::RegisterFunction( "IGGetPlayerName",						GetPlayerName );
	MabLuaTypeDatabase::RegisterFunction( "IGGetPlayerDisplayName",					GetPlayerDisplayName );
	MabLuaTypeDatabase::RegisterFunction( "IGGetPlayerNumber",						GetPlayerNumber );
	MabLuaTypeDatabase::RegisterFunction( "IGGetTeamName",							GetTeamName );
	MabLuaTypeDatabase::RegisterFunction( "IGGetTeamShortName",						GetTeamShortName );
	MabLuaTypeDatabase::RegisterFunction( "IGGetTeamColour",						GetTeamColour );
	MabLuaTypeDatabase::RegisterFunction( "IGGetTeamId",							GetTeamId );
	MabLuaTypeDatabase::RegisterFunction( "IGGetTeamScore",							GetTeamScore );
	MabLuaTypeDatabase::RegisterFunction( "IGUpdatePlayerDetail",					UpdatePlayerDetail );
	MabLuaTypeDatabase::RegisterFunction( "IGSwapPlayerPosition",					SwapPlayerPosition );
	MabLuaTypeDatabase::RegisterFunction( "IGSwapPlayerFieldBench",					SwapPlayerFieldBench );
	MabLuaTypeDatabase::RegisterFunction( "IGGetInjuredPlayerPosition",				GetInjuredPlayerPosition );
	MabLuaTypeDatabase::RegisterFunction( "IGGetInjuredPlayerName",					GetInjuredPlayerName );
		
	MabLuaTypeDatabase::RegisterFunction( "IGSetAsCaptain",							SetAsCaptain );
	MabLuaTypeDatabase::RegisterFunction( "IGSetAsKicker",							SetAsKicker );
	MabLuaTypeDatabase::RegisterFunction( "IGSetAsPlayKicker",						SetAsPlayKicker );

	MabLuaTypeDatabase::RegisterFunction( "IGGetCaptainId",							GetCaptainId );
	MabLuaTypeDatabase::RegisterFunction( "IGGetKickerId",							GetKickerId );
	MabLuaTypeDatabase::RegisterFunction( "IGGetPlayKickerId",						GetPlayKickerId );

	MabLuaTypeDatabase::RegisterFunction( "IGIsCaptain",							IsCaptain );
	MabLuaTypeDatabase::RegisterFunction( "IGIsKicker",								IsKicker );
	MabLuaTypeDatabase::RegisterFunction( "IGIsPlayKicker",							IsPlayKicker );

	MabLuaTypeDatabase::RegisterFunction( "IGIsPlayerOnField",						IsPlayerOnField );

	MabLuaTypeDatabase::RegisterFunction( "IGSetDisplayOffscreenPlayerMarker",		SetDisplayOffscreenPlayerMarker );
	MabLuaTypeDatabase::RegisterFunction( "IGGetFieldPlayerWithBestGoalKicking",	GetFieldPlayerWithBestGoalKicking );
	MabLuaTypeDatabase::RegisterFunction( "IGGetFieldPlayerWithBestPlayKicking",	GetFieldPlayerWithBestPlayKicking );
	MabLuaTypeDatabase::RegisterFunction( "IGGetFieldPlayerWithBestMentalAgility",	GetFieldPlayerWithBestMentalAgility );
	MabLuaTypeDatabase::RegisterFunction( "IGEnsureCaptainAndKickerOnField",		EnsureCaptainAndKickerOnField );

	// Substitutions.
	MabLuaTypeDatabase::RegisterFunction( "IGRequestPlayerSubstitution",			RequestPlayerSubstitution );
	MabLuaTypeDatabase::RegisterFunction( "IGGetNumberOfPendingSubstitutions",		GetNumberOfPendingSubstitutions );
    MabLuaTypeDatabase::RegisterFunction( "IGGetNumberOfRemainingSubstitutions",	GetNumberOfRemainingSubstitutions );
	MabLuaTypeDatabase::RegisterFunction( "IGGetSubstitutionTeamIndex",				GetSubstitutionTeamIndex );
	MabLuaTypeDatabase::RegisterFunction( "IGGetSubstitutionPlayerDatabaseIndex",	GetSubstitutionPlayerDatabaseIndex );
	MabLuaTypeDatabase::RegisterFunction( "IGStartAllPendingSubstitutions",			StartAllPendingSubstitutions );
	MabLuaTypeDatabase::RegisterFunction( "IGHavePendingSubstitutionsCompleted",	HavePendingSubstitutionsCompleted );
	MabLuaTypeDatabase::RegisterFunction( "IGIsPlayerAValidSubstitution",			IsPlayerAValidSubstitution );
	MabLuaTypeDatabase::RegisterFunction( "IGIsFieldPlayerAValidSubstitution",		IsFieldPlayerAValidSubstitution );
	MabLuaTypeDatabase::RegisterFunction( "IGApplyInjurySubstitution",				ApplyInjurySubstitution );	
	MabLuaTypeDatabase::RegisterFunction( "IGIsPlayerOnFieldAndLoaded",				IsPlayerOnFieldAndLoaded );

	MabLuaTypeDatabase::RegisterFunction( "IGStartNextHalf",						StartNextHalf );

	MabLuaTypeDatabase::RegisterFunction( "IGEnableMenuPostEffects",				EnableMenuPostEffects );
	MabLuaTypeDatabase::RegisterFunction( "IGDisableMenuPostEffects",				DisableMenuPostEffects );

	MabLuaTypeDatabase::RegisterFunction( "IGSetTeamInfo", IGSetTeamInfo );

	MabLuaTypeDatabase::RegisterFunction( "IGStartPreMatchCommentary",				StartPreMatchCommentary );

	// Network
	MabLuaTypeDatabase::RegisterFunction( "IGNotifyPlayerDisconnection",			NotifyPlayerDisconnection);

	MabLuaTypeDatabase::RegisterFunction( "OnDismissControllerMissingPopup",		OnDismissControllerMissingPopup);

#ifdef ENABLE_SEVENS_MODE
	// Coin toss stuff for extra time R7 games
	MabLuaTypeDatabase::RegisterFunction( "IGGetCoinTossDecisionMakerSide",			GetCoinTossDecisionMakerSide);
	MabLuaTypeDatabase::RegisterFunction( "IGGetCoinTossDecisionMakerIsCPU",		GetCoinTossDecisionMakerIsCPU);

	MabLuaTypeDatabase::RegisterFunction( "IGCoinTossMakeDecision1",				CoinTossMakeDecision1);
	MabLuaTypeDatabase::RegisterFunction( "IGCoinTossMakeDecision2",				CoinTossMakeDecision2);

	MabLuaTypeDatabase::RegisterFunction( "IGGetCoinTossDecisionDescriptionString",	GetCoinTossDecisionDescriptionString);
	MabLuaTypeDatabase::RegisterFunction( "IGGetCoinTossResultDescriptionString",	GetCoinTossResultDescriptionString);
	MabLuaTypeDatabase::RegisterFunction( "IGGetCoinTossDecisionMakerName",			GetCoinTossDecisionMakerName);
	MabLuaTypeDatabase::RegisterFunction( "IGCoinTossStartNextPhase",				CoinTossStartNextPhase);


	MabLuaTypeDatabase::RegisterFunction( "IGGetCoinTossState",						GetCoinTossState);
	MabLuaTypeDatabase::RegisterFunction( "IGGetCoinTossWinnerTeamName",			GetCoinTossWinnerTeamName);
	MabLuaTypeDatabase::RegisterFunction( "IGGetKickOffTeamName",					GetKickOffTeamName);
	MabLuaTypeDatabase::RegisterFunction( "IGGetKickingDirectionString",			GetKickingDirectionString);
	MabLuaTypeDatabase::RegisterFunction( "IGGetNonKickingDirectionString",			GetNonKickingDirectionString);
	MabLuaTypeDatabase::RegisterFunction( "IGGetNorSDecisionMaker",					GetNorthOrSouthDecisionMakerTeamName);
	MabLuaTypeDatabase::RegisterFunction( "IGGetKorRDecisionMaker",					GetKickOrReceiveDecisionMakerTeamName);
	MabLuaTypeDatabase::RegisterFunction( "IGGetKickOrReceiveDecision",				GetKickOrReceiveDecision);
	MabLuaTypeDatabase::RegisterFunction( "IGGetNorthOrSouthDecision",				GetNorthOrSouthDecision);
#endif
	//*/
}

bool SIFInGameHelpers::IsPauseMenuAvailable()
{
	SIFGameWorld* game = SIFApplication::GetApplication()->GetActiveGameWorld();

	// Does one (or both) teams contain only ai players? If so, allow pause.
	MABASSERT( game->GetTeam(SIDE_A) && game->GetTeam(SIDE_B) );

	if (!game)
	{
		return false;
	}

	if (!game->GetTeam(SIDE_A) || !game->GetTeam(SIDE_B))
	{
		return false;
	}

	bool playing_against_ai = game->GetTeam(SIDE_A)->GetNumHumanPlayers() == 0 || game->GetTeam(SIDE_B)->GetNumHumanPlayers() == 0;
	
	if(game && game->IsMatch())
	{		
		SSScreenWipeManager *wipe_manager = game->GetScreenWipeManager();
		if(wipe_manager->IsWipeRunning() && wipe_manager->GetWipeType()==SWIPE_WIPE)
			return false;

		// Only allow pause menu to appear if:
		//	- Not InGame or Network match
		//	- Not in various key game phases
		// OR if we are playing against a team containing only AI players

		if(!playing_against_ai/* && !game->GetGameSettings().game_settings.network_game */)
		{
			switch(game->GetGameState()->GetPhase())
			{
				case RUGamePhase::PENALTY_KICK_FOR_TOUCH:
				case RUGamePhase::PENALTY_SHOOT_FOR_GOAL:
				case RUGamePhase::SCRUM:
				case RUGamePhase::CONVERSION:
				// WJS RLC case RUGamePhase::LINEOUT:
				case RUGamePhase::FREE_KICK:
				case RUGamePhase::PRE_GAME:
					return false;
				default: break;
			}
		}
	}
	return true;
}

void SIFInGameHelpers::ShowPauseDisabledIcon()
{
	SIFGameWorld* game = SIFApplication::GetApplication()->GetActiveGameWorld();
	if(game && game->IsMatch())
	{
		game->GetHUDUpdater()->ShowPauseDisabledIcon();
	}
}

void SIFInGameHelpers::RaisePauseMenu()
{
#ifdef BUILD_DEBUG
	USIFGameFlowNode *game_flow_node = static_cast<USIFGameFlowNode*>( SIFApplication::GetApplication()->GetFlowControlManager()->FetchNode( SIF_INGAME_FLOWNAME ) );
	MABASSERT(game_flow_node && game_flow_node == SIFApplication::GetApplication()->GetFlowControlManager()->GetActiveNode());
		
	SIFWindow *window = SIFApplication::GetApplication()->GetWindowSystem()->GetCurrentWindow();
	MABASSERT( window && window->GetWindow()->GetName() == SIFUI_GAME_WINDOW_NAME );
#endif

	SIFAudioHelpers::PauseAndMuteInGameSFX( true );
	//#rc3_legacy SIFUIHelpers::SetCurrentWindow(RugbyUIWindowNames::SIFUI_IN_GAME_MENU_WINDOW_NAME);

	UWWUIScreenPauseMenuData * inData = NewObject<UWWUIScreenPauseMenuData>();
	UFlowControlManager* pFlowManager = SIFApplication::GetApplication()->GetFlowControlManager();
	if (pFlowManager)
	{
		UFlowNode* pActiveFlowNode = pFlowManager->GetActiveNode();

		if (pActiveFlowNode)
		{
			// If we are training, check the screen to see if we should pause, otherwise, just show the menu.
			if (pActiveFlowNode->GetName() == RugbyFlowNodeNames::SIF_TRAINING_FLOWNAME)
			{
				inData->in_training = true;
			}
		}
	}

	SIFApplication::GetApplication()->DealMenuAction(SCREEN_OPEN_MENUSCREEN, Screens_UI::PauseMenu, inData);
	SIFGameHelpers::GAPauseGame();

	SIFInGameHelpers::EnableMenuPostEffects();
	SIFAudioHelpers::PlaySoundEvent( RU_SOUND_PAUSE_MENU_LAUNCH );
}

void SIFInGameHelpers::EnableMenuPostEffects()
{
	// Enable DOF
	//#rc3_legacy_post_effects SIFApplication::GetApplication()->GetGameWorld()->GetPostEffectsManager()->PushPauseSettings();
}

void SIFInGameHelpers::DisableMenuPostEffects()
{
	//#rc3_legacy_post_effects SIFApplication::GetApplication()->GetGameWorld()->GetPostEffectsManager()->PopPauseSettings();
}

void SIFInGameHelpers::OnDismissControllerMissingPopup()
{
	/*#rc3_legacy_ui
	SIFUIInputAdapter* input = (SIFUIInputAdapter*)MabUIControllerAdapter::GetInstance();
	input->SetForceAllControllersEnabled(false);
	//*/
}

void SIFInGameHelpers::DismissPauseMenu()
{
#ifdef BUILD_DEBUG
	USIFGameFlowNode *game_flow_node = static_cast<USIFGameFlowNode*>( SIFApplication::GetApplication()->GetFlowControlManager()->FetchNode( SIF_INGAME_FLOWNAME ) );
	MABASSERT(game_flow_node && game_flow_node == SIFApplication::GetApplication()->GetFlowControlManager()->GetActiveNode());
		
	SIFWindow *window = SIFApplication::GetApplication()->GetWindowSystem()->GetCurrentWindow();
	MABASSERT( window && window->GetWindow()->GetName() == SIFUI_IN_GAME_MENU_WINDOW_NAME );
#endif
	SIFAudioHelpers::PauseAndMuteInGameSFX( false );
//	SIFUIHelpers::SetCurrentWindow( RugbyUIWindowNames::SIFUI_GAME_WINDOW_NAME );
	SIFGameHelpers::GAResumeGame();

	SIFInGameHelpers::DisableMenuPostEffects();
	SIFAudioHelpers::PlaySoundEvent( RU_SOUND_PAUSE_MENU_DISMISS );
}

void SIFInGameHelpers::RaiseInjurySubstitutionMenu( const int team_index, const int selected_player_id )
{
	// Set injured player db_id + index in first 15...
	SIFGameWorld* world = SIFApplication::GetApplication()->GetActiveGameWorld();
	RUTeam* team = world->GetTeam(team_index);
	ARugbyCharacter* injured_player = world->GetCutSceneManager()->GetInjuredPlayer();

	int injured_index = team->GetPlayerIndex(injured_player);
	int injured_db_id = injured_player->GetAttributes()->GetDbId();

	//< Setup data for the injury window. >
	UWWUIScreenInGameInjuryData* inData = NewObject<UWWUIScreenInGameInjuryData>();
	inData->TeamIndex					= team_index;
	inData->DefaultSelectedPlayerID		= selected_player_id;
	inData->PlayerDatabaseID			= injured_db_id;
	inData->PlayerIndex					= injured_index;
	inData->IsInjurySubstitution		= true;



	//Open the in game injury window
	SIFApplication::GetApplication()->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::InGameInjury, inData);
	//SIFApplication::GetApplication()->DealMenuAction(SCREEN_OPEN_MENUSCREEN_FADE, Screens_UI::TeamLineup);

	// We need to set a property on the substitution node so that it knows that
	// it needs to behave as the injury substitution.
	//#MB Eli you will need to reimplement this.
	//team_management_node->SetProperty( RUUI_TEAM_MANAGEMENT_IS_INJURY_SUBSTITUTION, MabString(0, "true" ));
	//team_management_node->SetProperty( RUUI_TEAM_MANAGEMENT_SELECTED_PLAYER_ID, MabString(0, "%i", selected_player_id ));
	//team_management_node->SetProperty( RUUI_TEAM_MANAGEMENT_INJURY_TEAM_INDEX, MabString(0, "%i", team_index ));

	const char *INJURY_PLAYER_INDEX = "injured_player_index";
	const char *INJURY_PLAYER_DB_ID = "injured_player_db_id";

	//team_management_node->SetProperty( INJURY_PLAYER_INDEX, MabString(0, "%i", injured_index ));
	//team_management_node->SetProperty( INJURY_PLAYER_DB_ID, MabString(0, "%i", injured_db_id ));

	// Displaying the substitution window will take focus away from the game.
	// This means we should apply all effects from the pause menu.
	SIFAudioHelpers::PauseAndMuteInGameSFX( true );
	SIFGameHelpers::GAPauseGame();
	SIFInGameHelpers::EnableMenuPostEffects();
	SIFAudioHelpers::PlaySoundEvent( RU_SOUND_PAUSE_MENU_LAUNCH );
}

void SIFInGameHelpers::DismissInjurySubstitutionMenu()
{
	SIFApplication::GetApplication()->DealMenuAction(SCREEN_CANCEL_FADE, Screens_UI::InGameInjury);

	SIFAudioHelpers::PauseAndMuteInGameSFX( false );
	SIFGameHelpers::GAResumeGame();

	SIFInGameHelpers::DisableMenuPostEffects();
	SIFAudioHelpers::PlaySoundEvent( RU_SOUND_PAUSE_MENU_DISMISS );
}

int SIFInGameHelpers::GetRunningPlayerTeam(int controller_index)
{
	SIFGameWorld* game = SIFApplication::GetApplication()->GetActiveGameWorld();
	//const SIFGameWorld::HumanPlayers& hps = game->GetHumanPlayers();
	const MabVector<SSHumanPlayer*>& hps = game->GetHumanPlayers();

	for (size_t i = 0; i < hps.size(); ++i)
	{
		SSHumanPlayer* hp = hps[i];
		if (hp->GetControllerIndex() == controller_index)
			return hp->GetTeam() == NULL ? -1 : hp->GetTeam()->GetIndex();
	}

	return -1;
}


int SIFInGameHelpers::GetRunningPlayerTeamId(int player_index)
{
    SIFGameWorld* game = SIFApplication::GetApplication()->GetActiveGameWorld();
	//const SIFGameWorld::HumanPlayers& hps = game->GetHumanPlayers();
	const MabVector<SSHumanPlayer*>& hps = game->GetHumanPlayers();
	
	for (size_t i = 0; i < hps.size(); ++i)
	{
		SSHumanPlayer* hp = hps[i];
		if (hp->GetControllerIndex() == player_index)
		{
			if (hp->GetTeam())
				return hp->GetTeam()->GetDbTeam().GetDbId();
		}
	}
	return -1;
}

void SIFInGameHelpers::ClearPlayerTeams()
{
	RUGameSettings* settings = SIFApplication::GetApplication()->GetMatchGameSettings();
	for (int i = 0; i < NUM_HUMAN_PLAYERS; ++i)
	{
		//#rc3_legacy_humans settings->human_player_settings[i].controller_index = i;
		settings->human_player_settings[i].team = -1;
	}
}

void SIFInGameHelpers::SetRunningPlayerTeam(EHumanPlayerSlot player_slot, int player_index, int team_index)
{
	SIFGameWorld* game = SIFApplication::GetApplication()->GetActiveGameWorld();
	if(game)
	{
		SSHumanPlayer* human_player = game->GetHumanPlayer(player_slot);
		MABASSERT(human_player != NULL);
		if(human_player)
		{
			// Dewald WW - Discovered a bug, SetTeam was getting called before the controller index was getting set,
					// this caused issues with the team_assignments_changed callback since it will still have the old controller index for the human player changing teams.

					//if ( human_player->GetPlayerIndex() == -1 )
			{
				human_player->SetPlayerIndex(player_index);
			}
			/*else
			{
				if (team_index == -1)
				{
					human_player->SetPlayerIndex(-1);
				}
			}*/

			human_player->SetTeam(team_index == -1 ? nullptr : game->GetTeam((SSTEAMSIDE)team_index));
		}
	}
}

void SIFInGameHelpers::SetMatchWorldPlayerTeam(EHumanPlayerSlot player_slot, int player_index, int team_index)
{
	SIFGameWorld* game = SIFApplication::GetApplication()->GetMatchGameWorld();

	if (game)
	{
		SSHumanPlayer* human_player = game->GetHumanPlayer(player_slot);
		MABASSERT(human_player != NULL);

		if (human_player)
		{
			human_player->SetPlayerIndex(player_index);

			human_player->SetTeam(team_index == -1 ? nullptr : game->GetTeam((SSTEAMSIDE)team_index));
		}
	}
}

void SIFInGameHelpers::SetPlayerTeam(EHumanPlayerSlot player_slot, int player_index,int ctrlr_id, int team_index)
{
	RUGameSettings* settings = SIFApplication::GetApplication()->GetMatchGameSettings();
	if(settings)
	{
		RUGameSettings::RU_HUMAN_PLAYER_SETTINGS* humanPlayerSettings = settings->GetHumanPlayerSettings(player_slot);
		if (humanPlayerSettings)
		{
			humanPlayerSettings->team = team_index;
			humanPlayerSettings->player_id = player_index;
			humanPlayerSettings->controller_id = ctrlr_id;
			humanPlayerSettings->peer_id = SIFApplication::GetApplication()->GetNetworkManager()->GetLocalNodeId();
		}
	}
}

/// Sets a controller to a particular team
void SIFInGameHelpers::SetOnlinePlayerTeam(EHumanPlayerSlot player_slot, int player_index, int team_index, int peer_id, int player_id)
{
	UE_LOG(LogNetwork, Display, TEXT("Setting slot for player slot %d player_index: %d team_index: %d peer_id %d"), (int32)player_slot, player_index, team_index, peer_id);
	RUGameSettings* settings = SIFApplication::GetApplication()->GetMatchGameSettings();
	if (settings)
	{
		RUGameSettings::RU_HUMAN_PLAYER_SETTINGS* humanPlayerSettings = settings->GetHumanPlayerSettings(player_slot);
		if (humanPlayerSettings)
		{
			humanPlayerSettings->team = team_index;
			humanPlayerSettings->controller_id = player_index;
			humanPlayerSettings->peer_id = peer_id;
			humanPlayerSettings->player_id = player_id;
		}
	}
}

int SIFInGameHelpers::GetPlayerTeam(int player_index)
{
	MABASSERTMSG(SIFApplication::GetApplication() && SIFApplication::GetApplication()->GetMatchGameSettings(),"we kinda need settings here");
	RUGameSettings* settings = SIFApplication::GetApplication()->GetMatchGameSettings();

	RUGameSettings::RU_HUMAN_PLAYER_SETTINGS* humanPlayerSettings = settings->GetHumanPlayerSettingsFromPlayerId(player_index);
	if (!humanPlayerSettings)
		return(-1);	// AI I believe, :jb
	return humanPlayerSettings->team;
}

bool SIFInGameHelpers::IsHumanPlayersOnBothTeams()
{
	int human_controlled_team = -1;
	RUGameSettings* settings = SIFApplication::GetApplication()->GetMatchGameSettings();
	for (int i = 0; i < NUM_HUMAN_PLAYERS; ++i)
	{
		if (settings->human_player_settings[i].team != -1)
		{
			if (human_controlled_team == -1)
			{
				human_controlled_team = settings->human_player_settings[i].team;
			}
			if (human_controlled_team != -1 && human_controlled_team != settings->human_player_settings[i].team)
			{
				return true;
			}
		}
	}
	return false;
}

int SIFInGameHelpers::GetNumHumanPlayersOnTeam( int team_index )
{
	int num_players = 0;
	RUGameSettings* settings = SIFApplication::GetApplication()->GetMatchGameSettings();
	for (int i = 0; i < NUM_HUMAN_PLAYERS; ++i)
	{
		if (settings->human_player_settings[i].team == team_index)
		{
			num_players++;
		}
	}
	return num_players;
}

void SIFInGameHelpers::LockAllHumanPlayerInputs( bool input_locked )
{
	SIFGameWorld* game = SIFApplication::GetApplication()->GetActiveGameWorld();
	const int num_human_players = (int)game->GetHumanPlayers().size();

	/*#rc3_legacy_input
	for ( int i = 0; i < num_human_players; ++i )
	{
		SSHumanPlayer* human_player = game->GetHumanPlayer( i );
		human_player->DisableInput( input_locked );
	}
	*/
}

void SIFInGameHelpers::PlayReplay()
{
//	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetGameWorld();
//	SSReplaySystem* replay_system = game_world->GetReplaySystem();
//	replay_system->ResumeGame(game_world->GetSimTime()->GetAbsoluteStepCount());
//
//	SIFGameHelpers::GAResumeGame();
}

void SIFInGameHelpers::RewindReplay()
{
//	SIFGameHelpers::GAPauseGame();
//	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetGameWorld();
//	SSReplaySystem* replay_system = game_world->GetReplaySystem();
//	size_t frame_id = game_world->GetSimTime()->GetAbsoluteStepCount() - 1;
//	if (frame_id > 0)
//		replay_system->RestoreFrame(frame_id);
}

void SIFInGameHelpers::FastForwardReplay()
{
//	SIFGameHelpers::GAPauseGame();
//	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetGameWorld();
//	SSReplaySystem* replay_system = game_world->GetReplaySystem();
//	size_t frame_id = game_world->GetSimTime()->GetAbsoluteStepCount() + 1;
//	if (frame_id < replay_system->GetHighestFrame())
//		replay_system->RestoreFrame(frame_id);
}

void SIFInGameHelpers::StopReplay()
{
//	SIFGameHelpers::GAPauseGame();
}

// this function is used by the stats display to know wether it should transition to an exit after displaying the stats
bool SIFInGameHelpers::IsGameOver()
{
	SSGameTimer* timer = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameTimer();
	if(timer->IsRunning() == false)
	{
		if(timer->GetHalf() == SECOND_HALF)
		{
			int actual_game_length = SIFApplication::GetApplication()->GetMatchGameSettings()->game_limits.GetActualGameLength();
			if(timer->GetScaledMinutesElapsed() > actual_game_length/*ACTUAL_GAME_LENGTH*/)
			{
				return(true);	
			}
		}
	}
	return(false);
}

void SIFInGameHelpers::UpdateTeamLineup( int team_index )
{
	SIFApplication::GetApplication()->GetActiveGameWorld()->GetHUDUpdater()->UpdateTeamLineup(team_index);
}

void SIFInGameHelpers::TeamLineupComplete()
{
	SIFApplication::GetApplication()->GetActiveGameWorld()->TeamLineupComplete();
}

MabString SIFInGameHelpers::GetPlayerName( int team_index, int player_position )
{
	RUTeam* team = SIFApplication::GetApplication()->GetActiveGameWorld()->GetTeam((SSTEAMSIDE)team_index);
	if(!team) return "";

	ARugbyCharacter* player = team->GetPlayerByPosition((PLAYER_POSITION)(1 << player_position));
	if(!player) return "";

	MabString ret = player->GetAttributes()->GetCombinedName();
	RUHUDUpdater::CensorPlayerName(&team->GetDbTeam(),player->GetAttributes()->GetDBPlayer(),ret);

	return(ret);
}

MabString SIFInGameHelpers::GetPlayerDisplayName( int team_index, int player_position )
{
	RUTeam* team = SIFApplication::GetApplication()->GetActiveGameWorld()->GetTeam((SSTEAMSIDE)team_index);
	if(!team) return "";

	ARugbyCharacter* player = team->GetPlayerByPosition((PLAYER_POSITION)(1 << player_position));
	if(!player) return "";

	MabString ret = player->GetAttributes()->GetDisplayName();
	RUHUDUpdater::CensorPlayerName(&team->GetDbTeam(),player->GetAttributes()->GetDBPlayer(),ret);

	return(ret);
}

MabString SIFInGameHelpers::GetPlayerNumber( int team_index, int player_position )
{
	RUTeam* team = SIFApplication::GetApplication()->GetActiveGameWorld()->GetTeam((SSTEAMSIDE)team_index);
	return MabString(0, "%d", team->GetPlayerByPosition((PLAYER_POSITION)(1 << player_position))->GetAttributes()->GetNumber());
}

MabString SIFInGameHelpers::GetTeamName( int team_index )
{
#if PLATFORM_XBOXONE || PLATFORM_PS4
	return SIFGameHelpers::GAGetSideTeamName(team_index);
#else
	RUTeam* team = SIFApplication::GetApplication()->GetActiveGameWorld()->GetTeam((SSTEAMSIDE)team_index);
	return team->GetDbTeam().GetName();
#endif
}

MabString SIFInGameHelpers::GetTeamShortName( int team_index )
{
#if PLATFORM_XBOXONE || PLATFORM_PS4
	return SIFGameHelpers::GAGetSideShortName(team_index);
#else
	RUTeam* team = SIFApplication::GetApplication()->GetActiveGameWorld()->GetTeam((SSTEAMSIDE)team_index);
	return team->GetDbTeam().GetShortName();
#endif
}

MabColour SIFInGameHelpers::GetTeamColour( int team_index )
{
	RUTeam* team = SIFApplication::GetApplication()->GetActiveGameWorld()->GetTeam((SSTEAMSIDE)team_index);

	MabColour to_go_out;
	team->GetDbTeam().GetPrimaryColour(to_go_out);
	return to_go_out;
}

int SIFInGameHelpers::GetTeamId( int team_index )
{
	RUTeam* team = SIFApplication::GetApplication()->GetActiveGameWorld()->GetTeam((SSTEAMSIDE)team_index);
	return team->GetDbTeam().GetDbId();
}

int SIFInGameHelpers::GetTeamScore( int team_index )
{
	RUTeam* team = SIFApplication::GetApplication()->GetActiveGameWorld()->GetTeam((SSTEAMSIDE)team_index);
	return SIFApplication::GetApplication()->GetStatisticsSystem()->GetCurrentMatchStat(team, &RUDB_STATS_TEAM::score);
}

void SIFInGameHelpers::UpdatePlayerDetail( int db_id )
{
	ARugbyCharacter* player = SIFApplication::GetApplication()->GetActiveGameWorld()->GetPlayerFromDB(db_id);
	MABASSERT(player);
	SIFApplication::GetApplication()->GetActiveGameWorld()->GetHUDUpdater()->UpdatePlayerDetail(player);
}

void SIFInGameHelpers::SwapPlayerPosition( int /*team_index*/ ,int player_a_db_id, int player_b_db_id )
{
	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	//RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);
	ARugbyCharacter* player_a = game_world->GetPlayerFromDB(player_a_db_id);
	ARugbyCharacter* player_b = game_world->GetPlayerFromDB(player_b_db_id);
	MABASSERT(player_a->GetAttributes()->GetTeam() == player_b->GetAttributes()->GetTeam());

	PLAYER_POSITION player_a_position = player_a->GetAttributes()->GetPlayerPosition();
	PLAYER_POSITION player_b_position = player_b->GetAttributes()->GetPlayerPosition();
	player_a->GetAttributes()->SetPlayerPosition(player_b_position);
	player_b->GetAttributes()->SetPlayerPosition(player_a_position);
}

void SIFInGameHelpers::SwapPlayerFieldBench( int /*team_index*/, int /*field_player_db_id*/, int /*bench_player_db_id*/ )
{
	MABBREAKMSG("Obsolete function!!!");

	//SIFGameWorld* game_world = SIFApplication::GetApplication()->GetGameWorld();
	//RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);
	//ARugbyCharacter* field_player = game_world->GetPlayerFromDB(field_player_db_id);
	//ARugbyCharacter* bench_player = NULL;
	//for (size_t i = 0; i < team->GetBenchPlayers().size(); ++i)
	//{
	//	if (team->GetBenchPlayers()[i]->GetAttributes()->GetDBPlayer()->GetDbId() == bench_player_db_id)
	//	{
	//		bench_player = team->GetBenchPlayers()[i];
	//		break;
	//	}
	//}
	//MABASSERT(bench_player);
	//MABASSERT(field_player->GetAttributes()->GetTeam() == bench_player->GetAttributes()->GetTeam());

	//PLAYER_POSITION field_player_position = field_player->GetAttributes()->GetPlayerPosition();
	//bench_player->GetAttributes()->SetPlayerPosition(field_player_position);
	//field_player->GetAttributes()->SetPlayerPosition(PP_NONE);

	//team->MoveToBench(field_player, PS_BENCH);
	//team->MoveToField(bench_player);

	//game_world->RebuildPlayerList();
}

MabString SIFInGameHelpers::GetInjuredPlayerPosition()
{
	MabString position_text = "";
	ARugbyCharacter* injured_player = SIFApplication::GetApplication()->GetActiveGameWorld()->GetCutSceneManager()->GetInjuredPlayer();

	// There may not be an injured player, so we need to check.
	if ( injured_player )
	{
		const PLAYER_POSITION position = injured_player->GetAttributes()->GetPlayerPosition();
		position_text = PlayerPositionEnum::GetPlayerPositionText( position );
	}

	return position_text;
}

MabString SIFInGameHelpers::GetInjuredPlayerName()
{
	MabString player_name = "";
	ARugbyCharacter* injured_player = SIFApplication::GetApplication()->GetActiveGameWorld()->GetCutSceneManager()->GetInjuredPlayer();

	// There may not be an injured player, so we need to check.
	if ( injured_player )
	{
		player_name = injured_player->GetAttributes()->GetDisplayName();

		RUDB_PLAYER *p = injured_player->GetAttributes()->GetDBPlayer();
		RUHUDUpdater::CensorPlayerName(NULL,p,player_name);
	}

	return player_name;
}

void SIFInGameHelpers::SetAsCaptain( int team_index, int player_db_id )
{
	MABLOGDEBUG("SetAsCaptain: Team %d:  %d",team_index, player_db_id);

	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	if(game_world->IsMatch())
	{
		RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);
		ARugbyCharacter *player = team->GetPlayerByDbId(player_db_id);
		team->SetCaptain(player);
	}
}

void SIFInGameHelpers::SetAsKicker( int team_index, int player_db_id )
{
	MABLOGDEBUG("SetAsKicker: Team %d:  %d",team_index, player_db_id);

	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	if(game_world->IsMatch())
	{
		RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);
		ARugbyCharacter *player = team->GetPlayerByDbId(player_db_id);
		team->SetOverrideKicker(player);
	}
}

void SIFInGameHelpers::SetAsPlayKicker( int team_index, int player_db_id )
{
	MABLOGDEBUG("SetAsPlayKicker: Team %d:  %d",team_index, player_db_id);

	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	if(game_world->IsMatch())
	{
		RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);
		ARugbyCharacter *player = team->GetPlayerByDbId(player_db_id);
		team->SetOverridePlayKicker(player);
	}
}

int SIFInGameHelpers::GetCaptainId( int team_index )
{
	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);

	return team->GetDbTeam().captain_id;
}

int SIFInGameHelpers::GetKickerId( int team_index )
{
	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);

	return team->GetDbTeam().goal_kicker_id;
}

int SIFInGameHelpers::GetPlayKickerId( int team_index )
{
	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);

	return team->GetDbTeam().play_kicker_id;
}

bool SIFInGameHelpers::IsCaptain( int team_index, int player_db_id )
{
	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);

	return team->GetDbTeam().captain_id == player_db_id; 
}

bool SIFInGameHelpers::IsKicker( int team_index, int player_db_id )
{
	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);

	return team->GetDbTeam().goal_kicker_id == player_db_id; 
}

bool SIFInGameHelpers::IsPlayKicker( int team_index, int player_db_id )
{
	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);

	return team->GetDbTeam().play_kicker_id == player_db_id; 
}

bool SIFInGameHelpers::IsPlayerOnField( int team_index, int player_db_id )
{
	// There are multiple teams on the field. We need to pull out the one that relates to the request team index.
	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);

	// The easiest way to tell if the player is set to a field player is to get a list of all the field players
	// and check if any of them have the database ID of the requested player.
	bool player_on_field = false;
	const SIFRugbyCharacterList& field_players = team->GetPlayers();
	for (SIFRugbyCharacterList::const_iterator iter = field_players.begin(); iter != field_players.end(); ++iter )
	{
		const ARugbyCharacter* player = *iter;
		if ( player->GetAttributes()->GetDbId() == player_db_id )
		{
			player_on_field = true;
			break;
		}
	}

	return player_on_field;
}

int SIFInGameHelpers::GetFieldPlayerWithBestGoalKicking( int team_index )
{
	// There are multiple teams on the field. We need to pull out the one that relates to the request team index.
	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);

	float current_highest_kicking = -1.0f;
	int current_best_player = SQLITEMAB_INVALID_ID;
	const SIFRugbyCharacterList& field_players = team->GetPlayers();
	for (SIFRugbyCharacterList::const_iterator iter = field_players.begin(); iter != field_players.end(); ++iter )
	{
		const ARugbyCharacter* player = *iter;
		if ( player->GetAttributes()->GetGoalKickAccuracy() > current_highest_kicking )
		{
			// We've found a new best!
			current_best_player = player->GetAttributes()->GetDbId();
			current_highest_kicking = player->GetAttributes()->GetGoalKickAccuracy();
		}
	}

	return current_best_player;
}

int SIFInGameHelpers::GetFieldPlayerWithBestPlayKicking( int team_index )
{
	// There are multiple teams on the field. We need to pull out the one that relates to the request team index.
	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);

	float current_highest_kicking = -1.0f;
	int current_best_player = SQLITEMAB_INVALID_ID;
	const SIFRugbyCharacterList& field_players = team->GetPlayers();
	for (SIFRugbyCharacterList::const_iterator iter = field_players.begin(); iter != field_players.end(); ++iter )
	{
		const ARugbyCharacter* player = *iter;
		if ( player->GetAttributes()->GetGeneralKickAccuracy() > current_highest_kicking )
		{
			// We've found a new best!
			current_best_player = player->GetAttributes()->GetDbId();
			current_highest_kicking = player->GetAttributes()->GetGeneralKickAccuracy();
		}
	}

	return current_best_player;
}
int SIFInGameHelpers::GetFieldPlayerWithBestMentalAgility( int team_index )
{
	// There are multiple teams on the field. We need to pull out the one that relates to the request team index.
	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);

	float current_highest_mental = -1.0f;
	int current_best_player = SQLITEMAB_INVALID_ID;
	const SIFRugbyCharacterList& field_players = team->GetPlayers();
	for (SIFRugbyCharacterList::const_iterator iter = field_players.begin(); iter != field_players.end(); ++iter )
	{
		const ARugbyCharacter* player = *iter;
		if ( player->GetAttributes()->GetMentalAgility() > current_highest_mental )
		{
			// We've found a new best!
			current_best_player = player->GetAttributes()->GetDbId();
			current_highest_mental = player->GetAttributes()->GetMentalAgility();
		}
	}

	return current_best_player;
}

void SIFInGameHelpers::EnsureCaptainAndKickerOnField( int team_index )
{
	// Captain.
	if ( !IsPlayerOnField( team_index, GetCaptainId( team_index) ))
	{
		const int new_best_captain = GetFieldPlayerWithBestMentalAgility( team_index );
		SetAsCaptain( team_index, new_best_captain );
	}

	// Goal Kicker.
	if ( !IsPlayerOnField( team_index, GetKickerId( team_index) ))
	{
		const int new_best_kicker = GetFieldPlayerWithBestGoalKicking( team_index );
		SetAsKicker( team_index, new_best_kicker );
	}

	// Play Kicker.
	if ( !IsPlayerOnField( team_index, GetPlayKickerId( team_index) ))
	{
		const int new_best_kicker = GetFieldPlayerWithBestPlayKicking( team_index );
		SetAsPlayKicker( team_index, new_best_kicker );
	}

}

void SIFInGameHelpers::SetDisplayOffscreenPlayerMarker(EHumanPlayerSlot player, bool should_display )
{
	RU3DHUDManager* hud_manager = SIFApplication::GetApplication()->GetActiveGameWorld()->Get3DHudManager();
	hud_manager->SetDisplayOffScreenPlayerMarker(player, should_display );
}

EValidInterchangeReason SIFInGameHelpers::RequestPlayerSubstitution( const int team_index, int player_a_db_id, int player_b_db_id, int player_a_ui_index, int player_b_ui_index)
{
	MABLOGDEBUG("RequestPlayerSubstitution: Team %d:  %d->%d",team_index, player_a_db_id, player_b_db_id );

	SIFApplication *application = SIFApplication::GetApplication();

	SIFGameWorld* game_world = application->GetActiveGameWorld();
	if(game_world->IsMatch())
	{
		// Live game...

		RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);
		ARugbyCharacter *player = team->GetPlayerByDbId(player_a_db_id);
		ARugbyCharacter *player2 = team->GetPlayerByDbId(player_b_db_id);

		// If player==NULL or player2==NULL, either could be the 'running on' player.
		// So check to see if this is the case.
		// and return early if so (this stops duplicate players appearing on the field).

		if(player==NULL || player2==NULL)
		{
			SSCutSceneManager *cut_manager = game_world->GetCutSceneManager();
			ARugbyCharacter *going_on_player = cut_manager->GetGoingOnPlayer();

			if(going_on_player!=NULL)
			{
				if (player == NULL && going_on_player->GetAttributes()->GetDbId() == player_a_db_id)
					return EValidInterchangeReason::VIR_MAX;

				if (player2==NULL && going_on_player->GetAttributes()->GetDbId()==player_b_db_id)
					return EValidInterchangeReason::VIR_MAX;
			}
		}

		// We want to make sure that the player on the field is player_a.
		if (player == NULL && player2 != NULL)
		{
			std::swap(player, player2);
			std::swap(player_a_db_id, player_b_db_id);
		}

//		const int player_a_shirt_num	= team->GetShirtNumberFromPlayerDbId(player_a_db_id);
//		const int player_b_shirt_num	= team->GetShirtNumberFromPlayerDbId(player_b_db_id);
//		MABUNUSED(player_a_shirt_num);
//		MABUNUSED(player_b_shirt_num);

		if (player == NULL)
			return EValidInterchangeReason::VIR_MAX;			// This can happen when sub'ing a player 

		if(player2==NULL)		
		{
			// On bench - do a tactical substitution as soon as possible.
			return game_world->GetSubstitutionManager()->QueueTacticalSubsitution(team,player,player_b_db_id);
		}
		else
		{
			// Just swap field positions of players.
            RUGameSettings* game_settings = SIFApplication::GetApplication()->GetMatchGameSettings();
            RU_TEAM_SETTINGS::LineupList& lineup_array = game_settings->team_settings[team_index].lineup;

            std::swap(lineup_array[player_a_ui_index], lineup_array[player_b_ui_index]);
		
			PLAYER_POSITION position = player->GetAttributes()->GetPlayerPosition();
			PLAYER_POSITION position2 = player2->GetAttributes()->GetPlayerPosition();

			player->GetAttributes()->SetPlayerPosition(position2);
			player2->GetAttributes()->SetPlayerPosition(position);
			return EValidInterchangeReason::VIR_VALID;
		}
	}
	return EValidInterchangeReason::VIR_MAX;
}

/// Retrieves the number of substitutions that are yet to be applied to the match.
int SIFInGameHelpers::GetNumberOfPendingSubstitutions()
{
	SIFApplication *application = SIFApplication::GetApplication();
	SIFGameWorld* game_world = application->GetActiveGameWorld();

	if(game_world->IsMatch())
	{
		return game_world->GetSubstitutionManager()->GetNumQueuedEvents();
	}
	return 0;
}

/// Retrieves the number of substitutions that are yet to be applied to the match.
int SIFInGameHelpers::GetNumberOfRemainingSubstitutions(const int team_index)
{
    SIFApplication *application = SIFApplication::GetApplication();
    SIFGameWorld* game_world = application->GetActiveGameWorld();
    RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_index);

    if(game_world->IsMatch())
    {
        return game_world->GetSubstitutionManager()->GetNumRemainingInterchanges(team);
    }
    return 0;
}


/// Retrieves the team index (SIDE_A or SIDE_B) that a specific substitution applies for.
int SIFInGameHelpers::GetSubstitutionTeamIndex(const int substitution_index )
{
	SIFApplication *application = SIFApplication::GetApplication();
	SIFGameWorld* game_world = application->GetActiveGameWorld();

	if(game_world->IsMatch())
	{
		return game_world->GetSubstitutionManager()->GetQueuedEventTeam(substitution_index)->GetIndex();
	}
	return 0;
}

/// Retrieves the player's databse ID for a specific substitution.
/// The player_index determines which player to retrieve the ID for. A substitution involves two players
/// so this value must be either 0 or 1.
int SIFInGameHelpers::GetSubstitutionPlayerDatabaseIndex(const int substitution_index, const int player_index)
{
	SIFApplication *application = SIFApplication::GetApplication();
	SIFGameWorld* game_world = application->GetActiveGameWorld();

	if(game_world->IsMatch())
	{
		int db_player0,db_player1;
		game_world->GetSubstitutionManager()->GetQueuedEventPlayerIds(substitution_index, &db_player0, &db_player1);

		return (player_index==0) ? db_player0 : db_player1;
	}

	return 0;
}

/// Fire off all pending substitutions at half-time.
void SIFInGameHelpers::StartAllPendingSubstitutions()
{
	SIFApplication *application	= SIFApplication::GetApplication();
	SIFGameWorld* game_world	= application->GetActiveGameWorld();

	if(game_world->IsMatch())
	{
		game_world->GetSubstitutionManager()->StartAllPendingInterchanges(true);
	}
}

/// Have all pending substitutions completed?
bool SIFInGameHelpers::HavePendingSubstitutionsCompleted()
{
	SIFApplication *application	= SIFApplication::GetApplication();
	SIFGameWorld* game_world	= application->GetActiveGameWorld();
	bool pending_subs_completed	= false;

	if(game_world->IsMatch())
	{
		pending_subs_completed	= game_world->GetSubstitutionManager()->HavePendingInterchangesCompleted();
	}

	return pending_subs_completed;
}

bool SIFInGameHelpers::IsPlayerAValidSubstitution( const int player_db_id, int team_idx )
{
	SIFApplication *application	= SIFApplication::GetApplication();
	SIFGameWorld* game_world	= application->GetActiveGameWorld();
	return game_world->GetSubstitutionManager()->CanBeInjurySubdOn(player_db_id, team_idx);
}

bool SIFInGameHelpers::IsFieldPlayerAValidSubstitution( const int player_db_id, int team_idx )
{
	RUSubstitutionManager* substitution_manager = SIFApplication::GetApplication()->GetActiveGameWorld()->GetSubstitutionManager();
	// Check for sin bins.
	if ( substitution_manager->IsPlayerInSinBin( player_db_id, team_idx ))
	{
		// Early out.
		return false;
	}

	// Check for red cards.
	MabVector< RU_INTERCHANGE_EVENT_TYPE > player_interchange_events;
	substitution_manager->GetPlayerFinishedInterchangeEvents(player_interchange_events, player_db_id, team_idx);
	MabVector< RU_INTERCHANGE_EVENT_TYPE >::const_iterator iter_sendoff =
		std::find( player_interchange_events.begin(), player_interchange_events.end(), RU_INTERCHANGE_SENTOFF );
	if ( iter_sendoff != player_interchange_events.end() )
	{
		// The player has a red card.
		return false;
	}

	return true;

}

void SIFInGameHelpers::ApplyInjurySubstitution( const int player_db_id )
{
	SIFApplication *application	= SIFApplication::GetApplication();
	SIFGameWorld* game_world	= application->GetActiveGameWorld();

	MABASSERT(player_db_id!=-1);
	game_world->GetCutSceneManager()->SetInjuryReplacement(player_db_id);
}

bool SIFInGameHelpers::IsPlayerOnFieldAndLoaded( int player_db_id, int team_idx )
{
	SIFApplication *application = SIFApplication::GetApplication();
	SIFGameWorld* game_world = application->GetActiveGameWorld();

	bool player_on_field_and_loaded = false;
	if(game_world->IsMatch())
	{
		// Live game...
		RUTeam* team = game_world->GetTeam((SSTEAMSIDE)team_idx);
		ARugbyCharacter *player = team->GetPlayerByDbId(player_db_id);
		int playersPerTeam = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameSettings().game_limits.GetNumberOfPlayersPerTeam();

		const bool is_field_player = team->GetLineupIndexFromDbPlayer( player_db_id ) < playersPerTeam/*NUM_PLAYERS_PER_TEAM*/;
		player_on_field_and_loaded = is_field_player && player == NULL;
	}

	return player_on_field_and_loaded;

}

void SIFInGameHelpers::StartNextHalf()
{
	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	game_world->GetCutSceneManager()->StartHalfTimeWalkOn();

	// We will also roll back the game clock so it starts at the right amount
	// in case the first half went into overtime.
	game_world->GetGameTimer()->RollBackTimerToHalfLength();

	// The HUD also needs to be updated so the correct time is displayed to the screen.
	// It is important that we pass in a delta tick of 0.0 or else it will try and move
	// the game timer forward that amount - all we want if for the elements on the HUD to update.
	game_world->GetHUDUpdater()->Update(0.0f);
}

void SIFInGameHelpers::IGSetTeamInfo()
{
	// What stadium are we playing on?
	SIFGameWorld* game_world = SIFApplication::GetApplication()->GetActiveGameWorld();
	RUStadiumManager* stadium_manager = SIFApplication::GetApplication()->GetMatchStadiumManager();

	const RUDB_STADIUM* stadium = stadium_manager->GetStadiumFromID( static_cast<unsigned short>( SIFGameHelpers::GAGetStadium() ));
	MabString stadium_name = MabStringHelper::ToUpper( MabString(stadium->GetName()) );
	
	if(game_world->GetHUDUpdater() && game_world->GetTeam(0) && game_world->GetTeam(1))
		game_world->GetHUDUpdater()->DisplaySetTeamInfo(game_world->GetTeam(0)->GetDbTeam(), game_world->GetTeam(1)->GetDbTeam(), stadium_name);
}

/// Starts the pre-match commentary sequence
void SIFInGameHelpers::StartPreMatchCommentary()
{
	RUCommentary *commentary = SIFApplication::GetApplication()->GetCommentarySystem();
	if(commentary)
	{
		commentary->GetPreMidPostDirector()->StartPreMatch(commentary->IsColourDisabled());
	}
}

void SIFInGameHelpers::NotifyPlayerDisconnection (const char* message)
{	
	/*rc3_legacy_rugbydollars
	bool remote_disconnection;

	// The opponent has quit
	if ( strcmp(message, SIF_MATCHMAKING_OPPONENT_QUIT_POPUP_NAME) == 0 || 
		 strcmp(message, SIF_MATCHMAKING_OPPONENT_DISCONNECT_POPUP_NAME) == 0 )
		remote_disconnection = true;
	else
		remote_disconnection = false;
	
	SIFApplication::GetApplication()->GetRugbyDollarsChecker()->PlayerDisconnection(remote_disconnection);
	*/
}

#ifdef ENABLE_SEVENS_MODE
int SIFInGameHelpers::GetCoinTossDecisionMakerSide()
{
	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();
	return (int)phase->GetDecisionMaker();
}

bool SIFInGameHelpers::GetCoinTossDecisionMakerIsCPU()
{
	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();
	return phase->GetDecisionMakerIsCPU();
}


bool SIFInGameHelpers::CoinTossMakeDecision1()
{
	/*MABLOGDEBUG("LUA told us to choose the left decision for coin toss");

	MABASSERT( SIFApplication::GetApplication()->GetGameWorld()->GetGameState()->GetPhase() == EXTRA_TIME_TOSS );
	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();

	// This shouldn't happen, but if the AI is making a decision, we shouldn't be able to make a decision
	if(phase->IsDecisionMakerCPU())
		return false;

	phase->Choose1();*/

	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();
	phase->Choose1();

	return true;
}

bool SIFInGameHelpers::CoinTossMakeDecision2()
{
	/*MABLOGDEBUG("LUA told us to choose the right decision for coin toss");

	MABASSERT( SIFApplication::GetApplication()->GetGameWorld()->GetGameState()->GetPhase() == EXTRA_TIME_TOSS );
	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();

	// This shouldn't happen, but if the AI is making a decision, we shouldn't be able to make a decision
	if(phase->IsDecisionMakerCPU())
		return false;

	phase->Choose2();*/



	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();
	phase->Choose2();

	return true;
}

void SIFInGameHelpers::CoinTossStartNextPhase()
{
	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();
	phase->StartNextPhase();
}

// Returns the string we will show in the coin toss window the the current decision that needs to be made
MabString SIFInGameHelpers::GetCoinTossDecisionDescriptionString(int forPhase)
{
	MabGlobalTranslationManager* translator = MabGlobalTranslationManager::GetInstance();
	MabString translated_string;

	switch((COIN_TOSS_PHASE)forPhase)
	{
	case CTP_NONE:
		MABBREAK();
		break;
	case CTP_COIN_TOSS_HEADS_OR_TAILS:
		translated_string = translator->Translate(MabString("[ID_COIN_TOSS_DESC_1]"), SIFHEAP_DYNAMIC);
		break;

	case CTP_COIN_TOSS_KICK_OR_RECEIVE:
		translated_string = translator->Translate(MabString("[ID_COIN_TOSS_DESC_2]"), SIFHEAP_DYNAMIC);
		break;

	case CTP_COIN_TOSS_NORTH_OR_SOUTH:
		translated_string = translator->Translate(MabString("[ID_COIN_TOSS_DESC_3]"), SIFHEAP_DYNAMIC);
		break;

	case CTP_LAST:
		{
			MABBREAK();
		}
		break;
	}

	return translated_string;
}

MabString SIFInGameHelpers::GetCoinTossResultDescriptionString(int forPhase)
{
	MabGlobalTranslationManager* translator = MabGlobalTranslationManager::GetInstance();
	MabString translated_string;

	switch((COIN_TOSS_PHASE)forPhase)
	{
	case CTP_NONE:
		MABBREAK();
		break;
	case CTP_COIN_TOSS_HEADS_OR_TAILS:
		translated_string = translator->Translate(MabString("[ID_COIN_TOSS_RESULT_1]"), SIFHEAP_DYNAMIC);
		break;

	case CTP_COIN_TOSS_KICK_OR_RECEIVE:
		translated_string = translator->Translate(MabString("[ID_COIN_TOSS_RESULT_2]"), SIFHEAP_DYNAMIC);
		break;

	case CTP_COIN_TOSS_NORTH_OR_SOUTH:
		translated_string = translator->Translate(MabString("[ID_COIN_TOSS_RESULT_3]"), SIFHEAP_DYNAMIC);
		break;

	case CTP_LAST:
		{
			MABBREAK();
		}
		break;
	}

	return translated_string;
}

MabString SIFInGameHelpers::GetCoinTossDecisionMakerName()
{
	MABASSERT( SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhase() == RUGamePhase::EXTRA_TIME_TOSS );
	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();
	return phase->GetDecisionMakerTeamName();
}

int SIFInGameHelpers::GetCoinTossState()
{
	MABASSERT( SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhase() == RUGamePhase::EXTRA_TIME_TOSS);
	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();

	COIN_TOSS_PHASE state = phase->GetPhase();
	MABASSERT(state > CTP_NONE && state < CTP_LAST);

	return (int)state;
}

const char* SIFInGameHelpers::GetKickingDirectionString()
{
	MABASSERT( SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhase() == RUGamePhase::EXTRA_TIME_TOSS);
	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();

	return phase->GetKickDirString();
}

const char* SIFInGameHelpers::GetNonKickingDirectionString()
{
	MABASSERT( SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhase() == RUGamePhase::EXTRA_TIME_TOSS);
	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();

	return phase->GetReceiveDirString();
}

const char* SIFInGameHelpers::GetKickOrReceiveDecisionMakerTeamName()
{
	MABASSERT( SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhase() == RUGamePhase::EXTRA_TIME_TOSS);
	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();

	return (SIFApplication::GetApplication()->GetActiveGameWorld()->GetTeam(phase->GetTeamDecideKorR())->GetDbTeam().GetName());
}

const char* SIFInGameHelpers::GetNorthOrSouthDecisionMakerTeamName()
{
	MABASSERT( SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhase() == RUGamePhase::EXTRA_TIME_TOSS);
	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();

	return (SIFApplication::GetApplication()->GetActiveGameWorld()->GetTeam(phase->GetTeamDecideNorS())->GetDbTeam().GetName());
}

const char* SIFInGameHelpers::GetKickOrReceiveDecision()
{
	MABASSERT( SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhase() == RUGamePhase::EXTRA_TIME_TOSS);
	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();

	return (phase->GetChoiceKorRString());
}

const char* SIFInGameHelpers::GetNorthOrSouthDecision()
{
	MABASSERT( SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhase() == RUGamePhase::EXTRA_TIME_TOSS);
	RUGamePhaseExtraTimeToss* phase = SIFApplication::GetApplication()->GetActiveGameWorld()->GetGameState()->GetPhaseHandler<RUGamePhaseExtraTimeToss>();

	return (phase->GetChoiceNorSString());
}
#endif