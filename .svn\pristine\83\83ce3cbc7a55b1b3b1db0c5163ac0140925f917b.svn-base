#include "Animation/RugbyAnimationRecords.h"
#include "RugbyAnimNotify.h"
#include "Animation/RugbyAnimNotifyState.h"
#include "AssetRegistryModule.h"
#include "DataTables/AnimationFilesDescriptor.h"
#include "Rugby/Character/RugbyCharacterAnimInstance.h"
#include <Animation/AnimSequence.h>
#include <Animation/AnimMontage.h>
#include <ConstructorHelpers.h>
#include "RugbyEnums.h"
#include "Utility/TransformUtility.h"
#include "DataTables/AnimationDescriptor.h"

#if !UE_BUILD_SHIPPING
#include "Rugby.h"
#endif

#define wwANIMATION_ASSET_DT_PATH			"/Game/Rugby/DataTables/Animations/DTGameAnimationsAssets"
#define wwCUTSCENE_ANIMATION_ASSET_DT_PATH	"/Game/Rugby/DataTables/Animations/DTCutsceneAnimationAssets"
#define wwANIMLIB_PICKUPANIM_DT_PATH		"/Game/Rugby/DataTables/Animations/DTPickupAnimations"
#define wwANIMLIB_BLEND_N_ANIM_DT_PATH		"/Game/Rugby/DataTables/Animations/DTBlendN"
#define wwANIMLIB_BLEND_SWITCH_ANIM_DT_PATH	"/Game/Rugby/DataTables/Animations/DTBlendSwitch"
#define wwANIMLIB_BLEND_TARGET_ANIM_DT_PATH	"/Game/Rugby/DataTables/Animations/DTBlendTargets"
#define wwANIMLIB_CONTACT_ANIM_GTB_DT_PATH	"/Game/Rugby/DataTables/Animations/DTContact"
#define wwANIMLIB_IDLE_ANIM_DT_PATH			"/Game/Rugby/DataTables/Animations/DTIdleAnims"
#define wwANIMLIB_RUCKMAUL_ANIM_DT_PATH		"/Game/Rugby/DataTables/Animations/DTRuckMaulScrum"
#define wwANIMLIB_FBTRY_ANIM_DT_PATH		"/Game/Rugby/DataTables/Animations/DTTryAnim"
#define wwANIMLIB_FB_ANIM_DT_PATH			"/Game/Rugby/DataTables/Animations/DTFullBodyActions"
#define wwANIMLIB_TACKLE_ANIM_DT_PATH		"/Game/Rugby/DataTables/Animations/DTTackleBlend"
#define wwANIMLIB_UB_ACTIONS_ANIM_DT_PATH	"/Game/Rugby/DataTables/Animations/DTUpperBodyActions"
#define wwANIMLIB_FB_PASS_ANIM_DT_PATH		"/Game/Rugby/DataTables/Animations/DTFullBodyPassing"
#define wwANIMLIB_CLOSEST_ANIM_DT_PATH		"/Game/Rugby/DataTables/Animations/DTClosestAnimations"
#define wwANIMLIB_CLOSEST_ANIM_TYPE_DT_PATH	"/Game/Rugby/DataTables/Animations/DTClosestAnimationTypes"

#define wwANIMLIB_FACE_ANIM_SUFFIX			("_f")


//#define wwANIMATION_ASSET_BASE_PATH			"/Game/Rugby/cmn_con/player/animations/"
//#define wwANIMLIB_REQUEST_ANIM_DT_PATH		"/Game/Rugby/DataTables/Animations/DTRequest"

//===============================================================================
//===============================================================================
FRugbyAnimRecBase::FRugbyAnimRecBase()
	: m_pAnimSequence(nullptr)
	, m_animName()
	, m_bMirrored(false)
	, m_targetSlot(ANIM_TARGET_SLOT::FULL_BODY)
	, m_playRate(1.0f)
	, m_bEnabled(true)
	, m_loopAlways(false)
	, m_headLookMask(0)
{	
}

//===============================================================================
//===============================================================================

FRugbyAnimRecBase::~FRugbyAnimRecBase()
{
	if (m_pAnimSequence && m_pAnimSequence->IsValidLowLevel())
	{
		m_pAnimSequence->RemoveFromRoot();
		m_pAnimSequence = nullptr;
	}
}

//===============================================================================
//===============================================================================

FRugbyRequestAnimRec::FRugbyRequestAnimRec()
	: FRugbyAnimRecBase()
	, m_id(-1)
	, m_requestType(wwDB_DTREQUESTTYPE_ENUM::MAX)
	, m_stateMachinePath()
{	
}

//===============================================================================
//===============================================================================

FRugbyRequestAnimRec::~FRugbyRequestAnimRec()
{
}

//===============================================================================
//===============================================================================

FRugbyRequestTypeRec::FRugbyRequestTypeRec()
	: m_requestType(wwDB_DTREQUESTTYPE_ENUM::MAX)
	, m_animVariants()
{	
}

//===============================================================================
//===============================================================================

FRugbyRequestTypeRec::~FRugbyRequestTypeRec()
{
	m_animVariants.Empty();
}

//===============================================================================

FRugbyFullBodyAnimRec::FRugbyFullBodyAnimRec()
	: FRugbyAnimRecBase()
	, m_FBType(ERugbyAnim_Mode_FullBodyActions::MAX)
	, m_Subtype()
	, m_TypeName(wwDB_DTREQUESTTYPE_ENUM::MAX)
	, m_stateMachinePath()
{	
}

//===============================================================================

FRugbyFullBodyAnimRec::~FRugbyFullBodyAnimRec()
{

}

//===============================================================================
//===============================================================================

FRugbyFBTypeRec::FRugbyFBTypeRec()
	: m_Type(ERugbyAnim_Mode_FullBodyActions::MAX)
	, m_animVariants()
{

}

//===============================================================================
//===============================================================================

FRugbyFBTypeRec::~FRugbyFBTypeRec()
{
	m_animVariants.Empty();
}


//===============================================================================
//===============================================================================

FRugbyContactAnimRecord::FRugbyContactAnimRecord()
	: FRugbyAnimRecBase()
	, m_id(-1)
	, m_contactType(CONTACT_TYPE::MAX)
	, m_gtbAction(GTB_ACTION::MAX)
	, m_IkBitMask(0)
	, m_bStopping(false)
{	
}

//===============================================================================
//===============================================================================

FRugbyContactAnimRecord::~FRugbyContactAnimRecord()
{	
}

//===============================================================================
//===============================================================================

FRugbyContactTypeRecord::FRugbyContactTypeRecord()
	: m_contactType(CONTACT_TYPE::MAX)
	, m_animVariants()
{	
}

//===============================================================================
//===============================================================================
FRugbyContactTypeRecord::~FRugbyContactTypeRecord()
{
	m_animVariants.Empty();
}

//===============================================================================
//===============================================================================
FRugbyClosestAnimRecord::FRugbyClosestAnimRecord()
	: FRugbyAnimRecBase()
	, m_type(CLOSEST_ANIM_TYPE::MAX)
	, m_stateMachinePath()
{
}

//===============================================================================
//===============================================================================
FRugbyClosestAnimRecord::~FRugbyClosestAnimRecord()
{
}

//===============================================================================
//===============================================================================

FRugbyClosestAnimTypeRecord::FRugbyClosestAnimTypeRecord()
	: m_type(CLOSEST_ANIM_TYPE::MAX)
	, m_animVariants()
	, m_blendDuration(0.25f)
	, m_orientationWeighting(0.5f)
	, m_rootRotationAxis(0.0f, 1.0f, 0.0f)
	, m_useRootRotationBlending(true)
	, m_useVelocity(false)
	, m_boneWeights()
{
}

//===============================================================================
//===============================================================================
FRugbyClosestAnimTypeRecord::~FRugbyClosestAnimTypeRecord()
{
	m_animVariants.Empty();
}

//===============================================================================
//===============================================================================
FRugbyRuckMaulScrumAnimRecord::FRugbyRuckMaulScrumAnimRecord()
	: FRugbyAnimRecBase()
	, m_Type(RUCKMAULSCRUM_TYPE::MAX)
	, m_Subtype(0)
	, m_ImpactType(IMPACT_TYPE::MAX)
	, m_Position(0)
	, m_Facing(0)
	, m_NodeID(0)
	, m_IsAggresive(false)
{	
}

//===============================================================================
//===============================================================================
FRugbyRuckMaulScrumAnimRecord::~FRugbyRuckMaulScrumAnimRecord()
{	
}

//===============================================================================
//===============================================================================
FRugbyRuckMaulScrumTypeRecord::FRugbyRuckMaulScrumTypeRecord()
	: m_Type(RUCKMAULSCRUM_TYPE::MAX)
	, m_animVariants()
{	
}

//===============================================================================
//===============================================================================

FRugbyRuckMaulScrumTypeRecord::~FRugbyRuckMaulScrumTypeRecord()
{
	m_animVariants.Empty();
}

//===============================================================================
//===============================================================================

FRugbyFBActionsTryRecord::FRugbyFBActionsTryRecord()
	: FRugbyAnimRecBase()
	, m_Type(TryGroupType::MAX)
	, m_isSliding (false)
	, m_tryStartVectors(FVector::ZeroVector)
	, m_tryEndVectors(FVector::ZeroVector)
{	
}

//===============================================================================
//===============================================================================
FRugbyFBActionsTryRecord::~FRugbyFBActionsTryRecord()
{	
}

FRugbyFBActionsTryTypeRecord::FRugbyFBActionsTryTypeRecord()
	: m_Type(TryGroupType::MAX)
{	
}

//===============================================================================
//===============================================================================
FRugbyFBActionsTryTypeRecord::~FRugbyFBActionsTryTypeRecord()
{
	m_animVariants.Empty();
}

//===============================================================================
//===============================================================================
FRugbyTackleBlendAnimRecord::FRugbyTackleBlendAnimRecord()
	: FRugbyAnimRecBase()
	, m_Type (ERugbyAnim_Mode_Tackles::null)
	, m_SubType()	
	, m_Dominance (TACKLE_DOMINANCE::TDOM_UNKNOWN)	
    , m_TryTackleType (TRY_TACKLE_TYPE::TTT_UNKNOWN)
	, m_ImpactArea (TACKLE_BODY_POSITION::TBP_UNKNOWN)
	, m_IsGround(false)
	, m_tryStartVectors (FVector::ZeroVector)
	, m_tryEndVectors (FVector::ZeroVector)
{	
}

//===============================================================================
//===============================================================================
FRugbyTackleBlendAnimRecord::~FRugbyTackleBlendAnimRecord()
{	
}

//===============================================================================
//===============================================================================
FRugbyTackleTypeRecord::FRugbyTackleTypeRecord()
	: m_TackleType(ERugbyAnim_Mode_Tackles::MAX)
	, m_animVariants()
{	
}

//===============================================================================
//===============================================================================
FRugbyTackleTypeRecord::~FRugbyTackleTypeRecord()
{
	m_animVariants.Empty();
}

//===============================================================================
//===============================================================================
FRugbyBlendNRecord::FRugbyBlendNRecord()
	: m_type(wwDB_DTBLENDNTYPE_ENUM::MAX)
	, m_uniqueId()
	, m_playRate(1.0f)
	, m_positionId(-1)
	, m_weightVar()
	, m_weightVarSmoothTime(0.0f)
	, m_targetWeight(0.0f)
	, m_bUseGlobalTime(false)
{
}

//===============================================================================
//===============================================================================
bool FRugbyBlendNRecord::Set(const int32 idx, const FBlendNRecordDef* pSourceRec)
{
	if (pSourceRec)
	{
		m_type					= pSourceRec->type;
		m_uniqueId				= pSourceRec->uniqueId;
		m_connectionIDs			= pSourceRec->connectionIDs;
		m_playRate				= pSourceRec->playRate;
		m_positionId			= pSourceRec->position;
		m_weightVar				= pSourceRec->weightVar;
		m_weightVarSmoothTime	= pSourceRec->weightVarSmoothTime;
		m_targetWeight			= pSourceRec->weight;
		m_bUseGlobalTime		= pSourceRec->globalTime;

#if !UE_BUILD_SHIPPING
		nodeName				= pSourceRec->nodeName;
#endif
		return true;
	}
	return false;
}

//===============================================================================
//===============================================================================
FRugbyBlendSwitchRecord::FRugbyBlendSwitchRecord()
	: m_uniqueId()
	, m_playRate(1.0f)
	, m_indexVar()
	, m_indexVarOffset(0)
	, m_targetWeight(1.0f)
	, m_bIdle(false)
	, m_bUseGlobalTime(false)
{
}

//===============================================================================
//===============================================================================
bool FRugbyBlendSwitchRecord::Set(const int32 idx, const FBlendSwitchRecordDef* pSourceRec)
{
	if (pSourceRec)
	{
		m_uniqueId			= pSourceRec->uniqueId;
		m_connectionIDs		= pSourceRec->connectionIDs;
		m_playRate			= pSourceRec->playRate;
		m_indexVar			= pSourceRec->indexVar;
		m_indexVarOffset	= pSourceRec->indexVarOffset;
		m_targetWeight		= pSourceRec->weight;
		m_bIdle				= pSourceRec->idle;
		m_bUseGlobalTime	= pSourceRec->globalTime;

#if !UE_BUILD_SHIPPING
		nodeName			= pSourceRec->nodeName;
#endif
		return true;
	}
	return false;
}

//===============================================================================
//===============================================================================
FRugbyBlendTargetRecord::FRugbyBlendTargetRecord()
	: m_uniqueId()
	, m_targetWeight(0.0f)
	, m_playRate(1.0f)
	, m_bUseGlobalTime(false)
	, m_phaseOffset(0.0f)
{	
}

//===============================================================================
//===============================================================================
FRugbyBlendTargetRecord::~FRugbyBlendTargetRecord()
{	
}


//===============================================================================
//===============================================================================
bool FRugbyBlendTargetRecord::Set(const int32 idx, const FBlendTargetRecordDef* pRecord)
{
	if (pRecord)
	{
		m_animName = pRecord->animName;

		m_bMirrored = pRecord->mirror;
		m_targetSlot = ANIM_TARGET_SLOT::FULL_BODY;
		m_bEnabled = true;
		m_loopAlways = true;

		m_uniqueId = pRecord->uniqueId;
		m_targetWeight = pRecord->weight;
		m_playRate = pRecord->playRate;
		m_bUseGlobalTime = pRecord->globalTime;
		m_phaseOffset = pRecord->phaseOffset;
#if !UE_BUILD_SHIPPING
		m_nodeName = pRecord->nodeName;
#endif
		return true;
	}
	return false;
}

//===============================================================================
//===============================================================================
FRugbyBlendNTypeRecord::FRugbyBlendNTypeRecord()
	: m_type(wwDB_DTBLENDNTYPE_ENUM::MAX)
	, m_animVariants()
{
}

//===============================================================================
//===============================================================================
//===============================================================================
FRugbyBlendNTypeRecord::~FRugbyBlendNTypeRecord()
{
	m_animVariants.Empty();
}

//===============================================================================
//===============================================================================
FRugbyUpperBodyActionsFBPassAnimRecord::FRugbyUpperBodyActionsFBPassAnimRecord()
	: FRugbyAnimRecBase()	
	, m_Type(ERugbyAnim_Mode_UBActionsFBPass::NullPassThrough)
	, m_SubType(UpperBodyActionFBPassSubTypes::Null)	
{	
}

//===============================================================================
//===============================================================================
FRugbyUpperBodyActionsFBPassAnimRecord::~FRugbyUpperBodyActionsFBPassAnimRecord()
{	
}

//===============================================================================
//===============================================================================
FRugbyUpperBodyActionsFBPassTypeRecord::FRugbyUpperBodyActionsFBPassTypeRecord()
	: m_Anim_MachineModeType(ERugbyAnim_Mode_UBActionsFBPass::MAX)
	, m_animVariants()
{	
}
//===============================================================================
//===============================================================================
FRugbyUpperBodyActionsFBPassTypeRecord::~FRugbyUpperBodyActionsFBPassTypeRecord()
{
	m_animVariants.Empty();
}

//===============================================================================
//===============================================================================
//===============================================================================
FRugbyUpperBodyActionsUBPassAnimRecord::FRugbyUpperBodyActionsUBPassAnimRecord()
	: FRugbyAnimRecBase()
	, m_Type(ERugbyAnim_Mode_UBActions::NullPassThrough)
	, m_SubType(UpperBodyActionUBPassSubTypes::Null)
	, m_PassType(UpperBodyPassType::Null)
{
}

//===============================================================================
//===============================================================================
FRugbyUpperBodyActionsUBPassAnimRecord::~FRugbyUpperBodyActionsUBPassAnimRecord()
{
}

//===============================================================================
//===============================================================================
FRugbyUpperBodyActionsUBPassTypeRecord::FRugbyUpperBodyActionsUBPassTypeRecord()
	: m_Anim_MachineModeType(ERugbyAnim_Mode_UBActions::MAX)
	, m_animVariants()
{
}

//===============================================================================
//===============================================================================
FRugbyUpperBodyActionsUBPassTypeRecord::~FRugbyUpperBodyActionsUBPassTypeRecord()
{
	m_animVariants.Empty();
}

//===============================================================================
//===============================================================================
FRugbyPickUpAnimRecord::FRugbyPickUpAnimRecord()
	: FRugbyAnimRecBase()
	, m_stateMachinePath()
{
}

//===============================================================================
//===============================================================================
FRugbyPickUpAnimRecord::~FRugbyPickUpAnimRecord()
{
	
}

//===============================================================================
//===============================================================================
FRugbyIdleAnimRec::FRugbyIdleAnimRec()
	: FRugbyAnimRecBase()
	, m_stateMachinePath()
	, m_hasLocomotion(false)
	, m_animType(wwDB_IDLE_ENUM::NONE)
	, m_IdleVector (FVector::ZeroVector)
{

}

//===============================================================================
//===============================================================================
FRugbyIdleAnimRec::~FRugbyIdleAnimRec()
{

}

//===============================================================================
//===============================================================================
FRugbyIdleAnimTypeRec::FRugbyIdleAnimTypeRec()
{
	m_IdleAnimVariants.Empty();
}

//===============================================================================
//===============================================================================
FRugbyIdleAnimTypeRec::~FRugbyIdleAnimTypeRec()
{

}

//===============================================================================
//===============================================================================
//===============================================================================
FRugbyAnimationLibrary::FRugbyAnimationLibrary()
{
	// Map cutscene face animations first for the sake of one of the checks being done.
	MapCutsceneFaceAnimations();

	PopulateExistingAssetMap();

	ensureAlways (m_animationSequenceAssetMap.Num() > 0);

	if (m_animationSequenceAssetMap.Num() > 0 )
	{
		LoadContactAnimations();

		LoadClosestAnimations();

		LoadBlendNAnimations();

		LoadRuckMaulScrumAnimations();

		LoadTryAnimations();

		LoadTackleAnimations();

		LoadUpperBodyActionsFBPassAnimations();

		LoadUpperBodyActionsPasses();

		LoadFullBodyAnimations();		
		
		LoadPickUpAnimations();

		LoadIdleAnimations();
	}
}


//===============================================================================
//===============================================================================
FRugbyAnimationLibrary::~FRugbyAnimationLibrary()
{
	m_contactTypeArray.Empty();
	m_contactAnimRecArray.Empty();
	m_closestAnimTypeRecArray.Empty();
	m_closestAnimRecArray.Empty();
	m_blendNTypeArray.Empty();
	m_blendNArray.Empty();
	m_blendTargetArray.Empty();
	m_blendNMap.Empty();
	m_blendTargetMap.Empty();
	m_RuckMaulScrumTypeArray.Empty();
	m_RuckMaulScrumAnimArray.Empty();
	m_TackleTypeArray.Empty();
	m_TackleBlendAnimArray.Empty();
	m_FBPassTypeArray.Empty();
	m_FBPassAnimArray.Empty();
	m_IdleTypeArray.Empty();
	m_IdleAnimArray.Empty();
	m_requestTypeArray.Empty();
	m_FBTypeArray.Empty();
	m_requestAnimRecArray.Empty();

	m_animationSequenceAssetMap.Empty();
	m_animationNameToIdMap.Empty();
	m_animationNameArray.Empty();

	// remove face animations from root so we don't leak memory.
	for (auto& pair : m_faceAnimMap)
	{
		if (pair.Value)
		{
			if (UAnimSequence* pAnim = Cast<UAnimSequence>(pair.Value))
			{
				if (pAnim->IsValidLowLevel())
				{
					pAnim->RemoveFromRoot();
					pAnim = nullptr;
				}
			}
		}
	}
	m_faceAnimMap.Empty();
}

//===============================================================================
//===============================================================================
template <class T>
uint32 FRugbyAnimationLibrary::GetDataTableRows(FString pathName, TArray<T*>& outArray)
{
	outArray.Empty();
	UDataTable* pExcelTable;
	pExcelTable = ConstructorHelpersInternal::FindOrLoadObject<UDataTable>(pathName);
	if (pExcelTable)
	{
		FString ContextString;
		pExcelTable->GetAllRows(ContextString, outArray);
		pExcelTable->RemoveFromRoot();
	}
	return outArray.Num();
}

//===============================================================================
//===============================================================================
void FRugbyAnimationLibrary::PopulateExistingAssetMap()
{
	m_animationSequenceAssetMap.Empty();
	m_animationNameToIdMap.Empty();
	m_animationNameArray.Empty();

	FString pathName = wwANIMATION_ASSET_DT_PATH; 
	UDataTable* ExcelTable = ConstructorHelpersInternal::FindOrLoadObject<UDataTable>(pathName);
	if (ExcelTable)
	{
		FString ContextString;
		TArray<FName> RowNames;
		RowNames = ExcelTable->GetRowNames();
		int rowcount = 0;

		for (const auto& name : RowNames)
		{
			FAnimationFilesDescriptor* row = ExcelTable->FindRow<FAnimationFilesDescriptor> ( name, ContextString );
			if ( row )
			{
				FSoftObjectPath	SoftPath(*row->Animation_Name);
				if (UObject *pDataObject = SoftPath.TryLoad())
				{
					m_animationNameToIdMap.Add(FName(*pDataObject->GetName()), rowcount);
					m_animationNameArray.Add(FName(*pDataObject->GetName()));
					m_animationSequenceAssetMap.Add(FName(*pDataObject->GetName()), pDataObject);
					TryAddFaceAnim(row->Animation_Name);
				}
			}
			rowcount++;
		}	
		ExcelTable->RemoveFromRoot();
	}
	else
	{
		UE_LOG(LogTemp, Log, TEXT("DATA TABLE FAILED TO LOAD"));
	}
	
}

FString GetStringWithoutPath(const FString originalPathString)
{
	FString outString = originalPathString;
	int32 lastSlashIndx = -1;
	if (outString.FindLastChar(TEXT('/'), lastSlashIndx))
	{
		return outString.RightChop(lastSlashIndx + 1);
	}
	return outString;
};

//===============================================================================
//===============================================================================
void FRugbyAnimationLibrary::MapCutsceneFaceAnimations()
{
	FString pathName = wwCUTSCENE_ANIMATION_ASSET_DT_PATH;
	UDataTable* ExcelTable = ConstructorHelpersInternal::FindOrLoadObject<UDataTable>(pathName);
	if (ExcelTable)
	{
		FString ContextString;
		TArray<FName> RowNames;
		RowNames = ExcelTable->GetRowNames();
		int rowcount = 0;

#if !UE_BUILD_SHIPPING
		TArray<FName> cutsceneAnimNameArray;
#endif
		for (const auto& name : RowNames)
		{
			FAnimationFilesDescriptor* row = ExcelTable->FindRow<FAnimationFilesDescriptor>(name, ContextString);
			if (row)
			{
				TryAddFaceAnim(row->Animation_Name);

#if !UE_BUILD_SHIPPING
				cutsceneAnimNameArray.Add(*GetStringWithoutPath(row->Animation_Name));
#endif
			}
			rowcount++;
		}

#if !UE_BUILD_SHIPPING
		TArray<FName> faceAnimKeyList;
		m_faceAnimMap.GetKeys(faceAnimKeyList);
		for (FName animKey : faceAnimKeyList)
		{
			if (!cutsceneAnimNameArray.Contains(animKey))
			{
				UE_LOG(LogTemp, Error, TEXT("Found a face animation with no body: %s"), *animKey.ToString());
			}
		}
#endif
		ExcelTable->RemoveFromRoot();
	}
	else
	{
		UE_LOG(LogTemp, Log, TEXT("DATA TABLE FAILED TO LOAD"));
	}
}

//===============================================================================
//===============================================================================
void FRugbyAnimationLibrary::TryAddFaceAnim(FString animationPath)
{
	static const FString faceAnimSuffix = wwANIMLIB_FACE_ANIM_SUFFIX;
	FString animName = animationPath;
	if (animName.EndsWith(faceAnimSuffix))
	{
		// convert to full body anim name so that we can look this up later
		animName = animName.LeftChop(faceAnimSuffix.Len());
		animName = GetStringWithoutPath(animName);

		if (animName.StartsWith("scm"))
		{
			int32 subStrIdx = animName.Find("_no", ESearchCase::IgnoreCase, ESearchDir::FromEnd);
			if (subStrIdx > 0)
			{
				animName = animName.LeftChop(animName.Len() - subStrIdx);
			}
		}

		// Objects loaded using FindOrLoadObject will be added to root set and need to be removed on cleanup
		if (UAnimSequence* pAnimSequence = ConstructorHelpersInternal::FindOrLoadObject<UAnimSequence>(animationPath))
		{
			m_faceAnimMap.Add(FName(*animName), pAnimSequence);
		}
	}
}

//===============================================================================
//===============================================================================
UAnimSequence* FRugbyAnimationLibrary::GetAnimSequenceByName(const FString animSequenceName, const bool findMirror) const
{
	if (animSequenceName.Len() > 0)
	{
		FString searchName = animSequenceName;
		if (findMirror)
		{
			searchName += "_mirrored";
		}

		FAssetData const* ppData = m_animationSequenceAssetMap.Find(*searchName);
		if (ppData)
		{
			FString sequencePathString = ppData->ObjectPath.ToString();
			// Objects loaded using FindOrLoadObject will be added to root set and need to be removed on cleanup
			return ConstructorHelpersInternal::FindOrLoadObject<UAnimSequence>(sequencePathString);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Missing animation trying to be used: %s"), *searchName);
		}
	}
	return nullptr;
}


//===============================================================================
//===============================================================================
const int32 FRugbyAnimationLibrary::GetAnimSequenceIndex(FName animationName) const
{
	const int32* pData = m_animationNameToIdMap.Find(animationName);
	if (pData)
	{
		return *pData;
	}
	return -1;
}

//===============================================================================
//===============================================================================
const FName FRugbyAnimationLibrary::GetAnimSequenceNameByIndex(int32 ID) const
{
	if (ID > 0 && ID < m_animationNameArray.Num())
	{
		return m_animationNameArray[ID];
	}
// 	const FName* pName = m_animationNameToIdMap.FindKey(ID);
// 	if (pName)
// 	{
// 		return *pName;
// 	}
	return "";
}

//===============================================================================
//===============================================================================
UAnimSequence* FRugbyAnimationLibrary::GetAnimSequenceByIndex(int32 animationID) const
{
	if (animationID >= 0 && animationID < m_animationNameArray.Num())
	{
		return GetAnimSequenceByName(m_animationNameArray[animationID].ToString(), false);
	}
	return nullptr;
}

//===============================================================================
//===============================================================================
UAnimSequence* FRugbyAnimationLibrary::GetFaceAnimByParentName(const FName parentAnimName) const
{
	if (parentAnimName != NAME_None)
	{
		UAnimSequence* const* ppAnimSequence = m_faceAnimMap.Find(parentAnimName);
		if (ppAnimSequence)
		{
			return *ppAnimSequence;
		}
	}
	return nullptr;
}

//
////===============================================================================
// no longer used, but retained here if needed for debugging purpose in future
////===============================================================================
//void FRugbyAnimationLibrary::LoadRequestAnimations()
//{
//	UE_LOG(LogTemp, Log, TEXT("Loading Request Animations........................"));
//
//#if !UE_BUILD_SHIPPING
//	uint32 missingCount = 0;
//	TArray<FString> missingAnimList;
//#endif
//
//	// Get list of anim sequence files that exist in Unreal to link to anim recs
//	//TArray<FAssetData> animSequenceAssetList;
//	//GetExistingAnimSequenceAssetList(animSequenceAssetList);
//
//	// Get data from data table
//	TArray<FRugbyRequestAnimRecordDef*> animRecDefList;
//	const uint32 entryCount = GetDataTableRows<FRugbyRequestAnimRecordDef>(wwANIMLIB_REQUEST_ANIM_DT_PATH, animRecDefList);
//
//	// Need to preallocate arrays otherwise they will be resized while populating it which will mess up the pointers to the recs that get stored in the type records. 
//	m_requestTypeArray.Empty((uint32)wwDB_DTREQUESTTYPE_ENUM::MAX);
//	m_requestTypeArray.SetNum((uint32)wwDB_DTREQUESTTYPE_ENUM::MAX);
//	m_requestAnimRecArray.Empty(entryCount);
//	m_requestAnimRecArray.SetNum(entryCount);
//
//	if (entryCount > 0)
//	{
//		// populate runtime animation records
//		for (uint32 index = 0; index < entryCount; index++)
//		{
//			FRugbyRequestAnimRecordDef& currentRecDef = *animRecDefList[index];
//			FRugbyRequestAnimRec& newRequestRec = m_requestAnimRecArray[index];
//
//			newRequestRec.m_id = index;
//			newRequestRec.m_requestType = (wwDB_DTREQUESTTYPE_ENUM)currentRecDef.TypeId;
//			newRequestRec.m_animName			= currentRecDef.AnimName;
//			newRequestRec.m_bMirrored			= currentRecDef.Mirrored;
//			newRequestRec.m_bUpperBody			= currentRecDef.UpperBody;
//			newRequestRec.m_bEnabled			= currentRecDef.Enabled;
//			newRequestRec.m_stateMachinePath	= currentRecDef.StateMachinePath;
//
//			newRequestRec.m_pAnimSequence = GetAnimSequenceByName(currentRecDef.AnimName, newRequestRec.m_bMirrored);
//			if (!newRequestRec.m_pAnimSequence)
//			{
//				
//				newRequestRec.m_bEnabled = false;	// disable animation if required sequence could not be found
//
//#if !UE_BUILD_SHIPPING
//				missingCount++;
//				missingAnimList.Add(currentRecDef.AnimName + (currentRecDef.Mirrored ? "_mirror" : ""));
//#endif
//			}
//			
//			m_requestTypeArray[(uint32)newRequestRec.m_requestType].m_requestType = newRequestRec.m_requestType;
//			m_requestTypeArray[(uint32)newRequestRec.m_requestType].m_animVariants.Add(&newRequestRec);		
//		}
//
//#if !UE_BUILD_SHIPPING
//		if (missingCount > 0)
//		{
//			UE_LOG(LogTemp, Error, TEXT("Missing request animation count: %d / %d"), missingCount, entryCount);
//			for (FString animName : missingAnimList)
//			{
//				UE_LOG(LogTemp, Error, TEXT("%s"), *animName);
//			}
//		}
//#endif
//	}    
//}

//=====================================================================================================================================
//=====================================================================================================================================

void FRugbyAnimationLibrary::LoadIdleAnimations()
{

	UE_LOG(LogTemp, Log, TEXT("Loading Idle Animations........................"));
	FTransform rootDeltaTransform = FTransform::Identity;
	// Dump any old data
	m_IdleTypeArray.Empty((uint32)wwDB_IDLE_ENUM::MAX);
	m_IdleTypeArray.SetNum((uint32)wwDB_IDLE_ENUM::MAX);
	m_IdleAnimArray.Empty();

	// Get data from data table
	TArray<FRugbyIdleAnimRecordDef*> IdleAnimRecDefList;
	const uint32 AnimEntryCount = GetDataTableRows<FRugbyIdleAnimRecordDef>(wwANIMLIB_IDLE_ANIM_DT_PATH, IdleAnimRecDefList);

	// Need to preallocate this array otherwise it will be resized while populating it which will mess up the pointers to the recs that get stored in the type records. 
	m_IdleAnimArray.SetNum(AnimEntryCount);

	if (AnimEntryCount > 0)
	{
		// populate runtime animation records
		for (uint32 index = 0; index < AnimEntryCount; index++)
		{			
			FRugbyIdleAnimRecordDef& currentRecDef = *IdleAnimRecDefList[index];
			FRugbyIdleAnimRec& newRequestRec = m_IdleAnimArray[index];

			newRequestRec.m_animType = (wwDB_IDLE_ENUM)currentRecDef.TypeName;
			newRequestRec.m_animName = currentRecDef.AnimName;
			newRequestRec.m_stateMachinePath = currentRecDef.StateMachinePath;
			newRequestRec.m_bMirrored = currentRecDef.Mirrored;
			newRequestRec.m_hasLocomotion = currentRecDef.Locomotion;
			newRequestRec.m_headLookMask = currentRecDef.HeadLookMask;
			newRequestRec.m_pAnimSequence = GetAnimSequenceByName(currentRecDef.AnimName, newRequestRec.m_bMirrored);

			if (newRequestRec.m_pAnimSequence)
			{
				rootDeltaTransform = newRequestRec.m_pAnimSequence->ExtractRootMotionFromRange(0.0f, newRequestRec.m_pAnimSequence->GetPlayLength());
				MabMatrix finalMabMtx = MabMatrix::IDENTITY;
				TransformUtility::ConvertUnrealTransformForMab(rootDeltaTransform, finalMabMtx);
				newRequestRec.m_IdleVector = finalMabMtx.GetTranslation();
			}

			m_IdleTypeArray[(uint32)newRequestRec.m_animType].m_IdleAnimType = newRequestRec.m_animType;			
			m_IdleTypeArray[(uint32)newRequestRec.m_animType].m_IdleAnimVariants.Add(&newRequestRec);

		}
	}
}

//=====================================================================================================================================
//=====================================================================================================================================

void FRugbyAnimationLibrary::LoadPickUpAnimations()
{
	UE_LOG(LogTemp, Log, TEXT("Loading PickUp Animations........................"));

	m_PickUpAnimNameArray.Empty();

	// Get data from data table
	TArray<FRugbyPickUpAnimRecordDef*> PickUpAnimRecNameList;
	const uint32 PickUpAnimRecordCount = GetDataTableRows<FRugbyPickUpAnimRecordDef>(wwANIMLIB_PICKUPANIM_DT_PATH, PickUpAnimRecNameList);

	// Need to preallocate this array otherwise it will be resized while populating it which will mess up the pointers to the recs that get stored in the type records. 
	m_PickUpAnimNameArray.SetNum(PickUpAnimRecordCount);

	if (PickUpAnimRecordCount > 0)
	{
		// populate runtime animation records
		for (uint32 index = 0; index < PickUpAnimRecordCount; index++)
		{
			FRugbyPickUpAnimRecordDef& currentRecDef = *PickUpAnimRecNameList[index];
			FRugbyPickUpAnimRecord& newRequestRec = m_PickUpAnimNameArray[index];

			newRequestRec.m_animName = currentRecDef.AnimName;
			newRequestRec.m_bMirrored = currentRecDef.Mirrored;
			
			newRequestRec.m_stateMachinePath = currentRecDef.StateMachinePath;

			newRequestRec.m_pAnimSequence = GetAnimSequenceByName(currentRecDef.AnimName, newRequestRec.m_bMirrored);			
		}
	}	
}

//===============================================================================
//===============================================================================
FRugbyContactFrame FRugbyAnimationLibrary::ComputeStaticContactFrame(UAnimSequence* pSequence, FName boneName, float sampleTime, const bool localSpace, const float playRate /*= 1.0f*/)
{
	FTransform rootDeltaTransform = FTransform::Identity;
	if (!localSpace)
	{
		rootDeltaTransform = pSequence->ExtractRootMotionFromRange(0.0f, sampleTime * playRate);
	}

	FTransform boneTransform;
	URugbyCharacterAnimInstance::GetAnimBoneTransformAtTime(boneTransform, pSequence, boneName, sampleTime * playRate, true);

	FVector accumulatedPos = rootDeltaTransform.TransformPosition(boneTransform.GetTranslation());
	FQuat accumulationRot = rootDeltaTransform.TransformRotation(boneTransform.GetRotation());
	
	FTransform accumulatedBoneTransform(accumulationRot, accumulatedPos);

	MabMatrix finalMabMtx;
	TransformUtility::ConvertUnrealTransformForMab(accumulatedBoneTransform, finalMabMtx);

// 	if (invertedContactData.Contains(pSequence->GetName()) || invertedContactData.Contains(pSequence->GetName().LeftChop(9)))
// 		finalMabMtx = finalMabMtx.Inverse();

	FRugbyContactFrame result;
	result.translation = finalMabMtx.GetTranslation();
	result.rotation = finalMabMtx.ToQuat();
	result.time = sampleTime;
	return result;
}

//===============================================================================
//===============================================================================
FRugbyContactFrame FRugbyAnimationLibrary::ComputeAnimatedContactFrame(UAnimSequence* pSequence, FName boneName, float sampleTime, const float playRate /*= 1.0f*/, const bool invert /*= false*/)
{
	FTransform boneTransform;
	URugbyCharacterAnimInstance::GetAnimBoneTransformAtTime(boneTransform, pSequence, boneName, sampleTime * playRate, true);

	MabMatrix finalMabMtx;
	TransformUtility::ConvertUnrealTransformForMab(boneTransform, finalMabMtx);

	if (invert)
	{
		finalMabMtx = finalMabMtx.Inverse();
	}

	FRugbyContactFrame result;
	result.translation = finalMabMtx.GetTranslation();
	result.rotation = finalMabMtx.ToQuat();
	result.time = sampleTime;
	return result;
};

//===============================================================================
//===============================================================================
FVector FRugbyAnimationLibrary::ComputeTryVector(UAnimSequence* pSequence, FName boneName, float sampleTime)
{
	return ComputeStaticContactFrame(pSequence, boneName, sampleTime, false).translation;
}

//===============================================================================
//===============================================================================
void FRugbyAnimationLibrary::LoadContactAnimations()
{
	UE_LOG(LogTemp, Log, TEXT("Loading Contact Animations........................"));

#if !UE_BUILD_SHIPPING
	uint32 missingCount = 0;
	TArray<FString> missingAnimList;

	uint32 noContactEventCount = 0;
	TArray<FString> noContactEventAnimList;
#endif

	// Get data from data table
	TArray<FRugbyContactAnimRecordDef*> tableRows;
	const uint32 entryCount = GetDataTableRows(wwANIMLIB_CONTACT_ANIM_GTB_DT_PATH, tableRows);

	// Get list of anim sequence files that exist in Unreal to link to anim recs
	//TArray<FAssetData> animSequenceAssetList;
	//GetExistingAnimSequenceAssetList(animSequenceAssetList);

	// Need to preallocate arrays otherwise they will be resized while populating it which will mess up the pointers to the recs that get stored in the type records. 
	m_contactTypeArray.Empty((uint32)CONTACT_TYPE::MAX);
	m_contactTypeArray.SetNum((uint32)CONTACT_TYPE::MAX);
	m_contactAnimRecArray.Empty(entryCount);
	m_contactAnimRecArray.SetNum(entryCount);

	for (uint32 recordIdx = 0; recordIdx < entryCount; recordIdx++)
	{
		FRugbyContactAnimRecordDef& currentRecDef = *tableRows[recordIdx];
		FRugbyContactAnimRecord& record = m_contactAnimRecArray[recordIdx];

		// Copy data we care about from rec
		record.m_id				= recordIdx;
		record.m_contactType	= currentRecDef.Contact;
		record.m_gtbAction		= NAME_TO_ENUM(GTB_ACTION, currentRecDef.Action);
		record.m_animName		= currentRecDef.AnimName;
		record.m_bMirrored		= currentRecDef.Mirrored;
		record.m_IkBitMask		= currentRecDef.IkBitMask;
		record.m_headLookMask	= currentRecDef.HeadLookMask;
		record.m_bStopping		= currentRecDef.GtbStop;
		record.m_targetSlot		= currentRecDef.UpperBody ? ANIM_TARGET_SLOT::UPPER_BODY : ANIM_TARGET_SLOT::FULL_BODY;
		record.m_playRate		= currentRecDef.PlaySpeed;
		record.m_bEnabled		= currentRecDef.Enabled;

		m_contactTypeArray[(uint32)record.m_contactType].m_contactType = record.m_contactType;
		m_contactTypeArray[(uint32)record.m_contactType].m_animVariants.Add(&record);

		// Disable the record if the required sequence could not be loaded.
		UAnimSequence* pSequence = GetAnimSequenceByName(currentRecDef.AnimName, record.m_bMirrored);
		if (pSequence == nullptr)
		{
			UE_LOG(LogTemp, Error, TEXT("No animation found for contact rec matching name: %s"), *currentRecDef.AnimName);
			record.m_bEnabled = false;

#if !UE_BUILD_SHIPPING
			missingCount++;
			missingAnimList.Add(currentRecDef.AnimName);
#endif
		}
		else
		{
			// Ensure there is a valid contact event.
			float animLength = 0.0f;
			const float contactTime = GetContactEventTime(pSequence, record.m_gtbAction == GTB_ACTION::KICK ? "BallKick" : "BallContact", animLength);
			if (contactTime < 0.0f)
			{
				UE_LOG(LogTemp, Error, TEXT("Could not find a valid contact event track for %s"), *currentRecDef.AnimName);
				record.m_bEnabled = false;

#if !UE_BUILD_SHIPPING
				noContactEventCount++;
				noContactEventAnimList.Add(currentRecDef.AnimName);
#endif
			}
			else
			{
#if !UE_BUILD_SHIPPING
				if (pSequence->GetName() == "nbrspul02")
				{
					wwDO_NOTHING;
				}
#endif
				//FString contactInfo = pSequence->GetName();

				// Derive contact data for this animation.
				static const FName ballBoneName("ball");
				record.m_pAnimSequence = pSequence;
				record.m_staticContact = ComputeStaticContactFrame(pSequence, ballBoneName, contactTime, false, record.m_playRate);

				// see RC3's ContactAnimDef::calculateAnimatedContacts
				constexpr const float INTERVAL = 0.1f;

				// assuming the old code wanted contactTime in seconds here
				const int32 numAnimatedContacts = FMath::FloorToInt(contactTime / INTERVAL);

				record.m_animatedContactList.Reserve(numAnimatedContacts);

				//contactInfo += "," + record.m_staticContact.translation.ToString();
				for (int32 sampleIndex = 0; sampleIndex <= numAnimatedContacts; ++sampleIndex)
				{
					const float sampleTime = INTERVAL * float(sampleIndex);
					record.m_animatedContactList.Add(ComputeAnimatedContactFrame(pSequence, ballBoneName, sampleTime, record.m_playRate));
					//contactInfo += "," + record.m_animatedContactList.Last().translation.ToString();
				}

				// I think this should always be true?
				if (contactTime - INTERVAL * float(numAnimatedContacts) > 0.f)
				{
					record.m_animatedContactList.Add(ComputeAnimatedContactFrame(pSequence, ballBoneName, contactTime, record.m_playRate));
					//contactInfo += "," + record.m_animatedContactList.Last().translation.ToString();
				}
			}
		}
	}

#if !UE_BUILD_SHIPPING
	if (missingCount > 0)
	{
		UE_LOG(LogTemp, Error, TEXT("Missing request animation count: %d / %d"), missingCount, entryCount);
		for (FString animName : missingAnimList)
		{
			UE_LOG(LogTemp, Error, TEXT("%s"), *animName);
		}
	}

	if (noContactEventCount > 0)
	{
		UE_LOG(LogTemp, Error, TEXT("Missing contact event animation count: %d / %d"), noContactEventCount, entryCount);
		for (FString animName : noContactEventAnimList)
		{
			UE_LOG(LogTemp, Error, TEXT("%s"), *animName);
		}
	}
#endif
}

//===============================================================================
//===============================================================================
void FRugbyAnimationLibrary::LoadClosestAnimations()
{
	UE_LOG(LogTemp, Log, TEXT("Loading Closest Animations........................"));

	// * Setup the animation types *
	TArray<FRugbyClosestAnimType*> typeTableRows;
	const uint32 typeTableRowCount = GetDataTableRows(wwANIMLIB_CLOSEST_ANIM_TYPE_DT_PATH, typeTableRows);
	ensureAlways(typeTableRowCount == (uint32)CLOSEST_ANIM_TYPE::MAX);

	// Need to preallocate arrays otherwise they will be resized while populating it which will mess up the pointers to the recs that get stored in the type records.
	m_closestAnimTypeRecArray.Empty((uint32)CLOSEST_ANIM_TYPE::MAX);
	m_closestAnimTypeRecArray.SetNum((uint32)CLOSEST_ANIM_TYPE::MAX);

	for (uint32 recordIdx = 0; recordIdx < typeTableRowCount; recordIdx++)
	{
		FRugbyClosestAnimType& dataTableRec = *typeTableRows[recordIdx];
		FRugbyClosestAnimTypeRecord& currentRec = m_closestAnimTypeRecArray[(uint32)dataTableRec.Type];

		currentRec.m_type = dataTableRec.Type;
		currentRec.m_blendDuration = dataTableRec.BlendDuration;
		currentRec.m_orientationWeighting = dataTableRec.OrientationWeighting;
		currentRec.m_useRootRotationBlending = dataTableRec.UseRootRotationBlending;
		currentRec.m_useVelocity = dataTableRec.UseVelocity;
		currentRec.m_boneWeights = dataTableRec.BoneWeights;
	}

	// * Setup the animations *
	TArray<FRugbyClosestAnim*> animTableRows;
	const uint32 animTableRowCount = GetDataTableRows(wwANIMLIB_CLOSEST_ANIM_DT_PATH, animTableRows);
	ensureAlways(animTableRowCount > 0);

	// Need to preallocate arrays otherwise they will be resized while populating it which will mess up the pointers to the recs that get stored in the type records.
	m_closestAnimRecArray.Empty(animTableRowCount);
	m_closestAnimRecArray.SetNum(animTableRowCount);

	for (uint32 recordIdx = 0; recordIdx < animTableRowCount; recordIdx++)
	{
		FRugbyClosestAnim& currentRecDef = *animTableRows[recordIdx];
		FRugbyClosestAnimRecord& record = m_closestAnimRecArray[recordIdx];

		// Copy data we care about from rec		
		record.m_type = currentRecDef.Type;
		//record.m_subType = FName(*currentRecDef.SubType);
		record.m_bMirrored = currentRecDef.Mirrored;
		record.m_targetSlot = currentRecDef.IsTackleNode ? ANIM_TARGET_SLOT::TACKLE : ANIM_TARGET_SLOT::FULL_BODY;
		record.m_animName = currentRecDef.AnimName;
		record.m_pAnimSequence = GetAnimSequenceByName(currentRecDef.AnimName, record.m_bMirrored);

		FRugbyClosestAnimTypeRecord& typeRec = m_closestAnimTypeRecArray[(uint32)record.m_type];
		ensureAlwaysMsgf(typeRec.m_type == record.m_type, TEXT("Closest anim type %d not defined in type data table"), (uint32)record.m_type);
		typeRec.m_type = record.m_type;
		typeRec.m_animVariants.Add(&record);

		if (nullptr == record.m_pAnimSequence)
		{
			record.m_bEnabled = false;
			UE_LOG(LogTemp, Error, TEXT("LoadClosestAnimations: Missing animation %s"), *currentRecDef.AnimName);
		}
	}
}

//===============================================================================
//===============================================================================
void FRugbyAnimationLibrary::LoadBlendNAnimations()
{
	UE_LOG(LogTemp, Log, TEXT("Loading BlendN Animations........................"));

	//////////////////////////////////////////////////////////////////////////
	// Custom behaviour to assign animation to blend target record that was just created and then map the record using its unique ID
	auto OnBlendTargetCreated = [this](const int32 idx, FRugbyBlendTargetRecord& createdRecord) -> void
	{
		createdRecord.m_pAnimSequence = GetAnimSequenceByName(createdRecord.m_animName, createdRecord.m_bMirrored);
		check(createdRecord.m_pAnimSequence);

		m_blendTargetMap.Add(createdRecord.m_uniqueId, &createdRecord);
	};
	// Load blend target records from data table
	LoadFromDataTable<FBlendTargetRecordDef, FRugbyBlendTargetRecord>(
		wwANIMLIB_BLEND_TARGET_ANIM_DT_PATH, 
		m_blendTargetArray,
		OnBlendTargetCreated);

	//////////////////////////////////////////////////////////////////////////
	// Custom behavior once data has been read. Should just need to add to map
	auto OnBlendSwitchCreated = [this](const int32 idx, FRugbyBlendSwitchRecord& createdRecord) -> void
	{
		m_blendSwitchMap.Add(createdRecord.m_uniqueId, &createdRecord);
	};
	// Load blend switch records from data table
	LoadFromDataTable<FBlendSwitchRecordDef, FRugbyBlendSwitchRecord>(
		wwANIMLIB_BLEND_SWITCH_ANIM_DT_PATH,
		m_blendSwitchArray,
		OnBlendSwitchCreated);

	//////////////////////////////////////////////////////////////////////////
	// Initialise blend N type arrays to correct size
	m_blendNTypeArray.Empty((uint32)wwDB_DTBLENDNTYPE_ENUM::MAX);
	m_blendNTypeArray.SetNum((uint32)wwDB_DTBLENDNTYPE_ENUM::MAX);

	// Custom behaviour to add blend N records to type records and map them to unique IDs
	auto OnBlendNCreated = [this](const int32 idx, FRugbyBlendNRecord& createdRecord) -> void
	{
		m_blendNTypeArray[(uint32)createdRecord.m_type].m_type = createdRecord.m_type;
		m_blendNTypeArray[(uint32)createdRecord.m_type].m_animVariants.Add(&createdRecord);

		m_blendNMap.Add(createdRecord.m_uniqueId, &createdRecord);
	};

	// Load blend N rec data
	LoadFromDataTable<FBlendNRecordDef, FRugbyBlendNRecord>(
		wwANIMLIB_BLEND_N_ANIM_DT_PATH,
		m_blendNArray,
		OnBlendNCreated);
}

//===============================================================================
//===============================================================================
float FRugbyAnimationLibrary::GetContactEventTime(UAnimSequence* pSequence, FName eventName, float& outAnimLengthSecs) const
{
	outAnimLengthSecs = 0.0f;
	if (pSequence == nullptr || !pSequence->IsValidLowLevel())
	{
		UE_LOG(LogTemp, Error, TEXT("ComputeAnimEvents:: animation in montage is not a sequence"));
		UE_LOG(LogTemp, Display, TEXT("ComputeAnimEvents:: Name %s"), *eventName.ToString());
		return -1.0f;
	}

	const auto predicate = [&](const FAnimNotifyEvent& event) -> bool
	{
		if (event.Notify)
		{
			const auto rugbyNotify = Cast<URugbyAnimNotify>(event.Notify);
			return rugbyNotify && rugbyNotify->EventName == eventName;
		}
		return false;
	};

	outAnimLengthSecs = pSequence->GetPlayLength();

	const auto contactNotify = pSequence->Notifies.FindByPredicate(predicate);
	if (contactNotify == nullptr)
	{
		// Based on use this is not an error and should not be spamming the log as though it is.
		// UE_LOG(LogTemp, Error, TEXT("Could not find a valid contact event track"));
		return -1.0f;
	}

	return contactNotify->GetTime();
}

//===============================================================================
//===============================================================================
float FRugbyAnimationLibrary::GetDurationEventTime (UAnimSequence* pSequence, FName eventName, float& outStartTime, float& outEndTime) const
{	

	if ( pSequence == nullptr )
	{
		UE_LOG(LogTemp, Error, TEXT("ComputeAnimEvents:: animation in montage is not a sequence"));
		UE_LOG(LogTemp, Display, TEXT("ComputeAnimEvents:: Name %s"), *eventName.ToString());
		return -1.0f;
	}

	const auto predicate = [&](const FAnimNotifyEvent& event) -> bool
	{
		const auto rugbyNotify = Cast<URugbyAnimNotifyState>(event.NotifyStateClass);
		return rugbyNotify && rugbyNotify->EventName == eventName;
	};

	const auto DurationEventNotify = pSequence->Notifies.FindByPredicate(predicate);
	if (DurationEventNotify == nullptr)
	{
		// Based on use this is not an error and should not be spamming the log as though it is.
		// UE_LOG(LogTemp, Error, TEXT("Could not find a valid duration event track"));
		return -1.0f;
	}

	outStartTime = DurationEventNotify->GetTime();
	outEndTime = DurationEventNotify->GetTime() + DurationEventNotify->GetDuration();

	return pSequence->GetPlayLength();
}


//===============================================================================
//===============================================================================
const FRugbyContactTypeRecord* FRugbyAnimationLibrary::GetContactTypeRec(CONTACT_TYPE contactType) const
{
	const int32 typeIdx = (int32)contactType;
	ensureAlwaysMsgf(typeIdx < m_contactTypeArray.Num(), TEXT("Contact type %d is not valid."), typeIdx);
	if (typeIdx < m_contactTypeArray.Num())
	{
		return &m_contactTypeArray[typeIdx];
	}
	return nullptr;
}

//===============================================================================
//===============================================================================
const FRugbyContactAnimRecord* FRugbyAnimationLibrary::GetContactAnimRec(uint32 idx) const
{
	ensureAlwaysMsgf((int32)idx < m_contactAnimRecArray.Num(), TEXT("Contact animation idx %d is out of valid array (max %d)."), idx, m_contactAnimRecArray.Num() - 1);
	if ((int32)idx < m_contactAnimRecArray.Num())
	{
		return &m_contactAnimRecArray[idx];
	}
	return nullptr;
}

//===============================================================================
//===============================================================================
const FRugbyClosestAnimTypeRecord* FRugbyAnimationLibrary::GetClosestAnimTypeRec(CLOSEST_ANIM_TYPE type) const
{
	const int32 idx = (int32)type;
	ensureAlwaysMsgf(idx < m_closestAnimTypeRecArray.Num(), TEXT("Closest anim type %d is not valid."), idx);
	if (idx < m_closestAnimTypeRecArray.Num())
	{
		return &m_closestAnimTypeRecArray[idx];
	}
	return nullptr;
}

//===============================================================================
//===============================================================================
const FRugbyClosestAnimRecord* FRugbyAnimationLibrary::GetClosestAnimRec(uint32 idx) const
{
	ensureAlwaysMsgf((int32)idx < m_closestAnimRecArray.Num(), TEXT("Closest animation idx %d is out of valid array (max %d)."), idx, m_contactAnimRecArray.Num() - 1);
	if ((int32)idx < m_closestAnimRecArray.Num())
	{
		return &m_closestAnimRecArray[idx];
	}
	return nullptr;
}

//===============================================================================
//===============================================================================
const FRugbyTackleTypeRecord* FRugbyAnimationLibrary::GetTackleTypeRec(ERugbyAnim_Mode_Tackles TackleType) const
{
	const int32 typeIdx = (int32)TackleType;
	ensureAlwaysMsgf(typeIdx < m_TackleTypeArray.Num(), TEXT("Tackle type %d is not valid."), typeIdx);
	if (typeIdx < m_TackleTypeArray.Num())
	{
		return &m_TackleTypeArray[typeIdx];
	}
	return nullptr;
}

//===============================================================================
//===============================================================================
const FRugbyTackleBlendAnimRecord* FRugbyAnimationLibrary::GetTackleAnimRec(uint32 idx) const
{
	ensureMsgf((int32)idx < m_TackleBlendAnimArray.Num(), TEXT("Tackle animation idx %d is out of valid array (max %d)."), idx, m_TackleBlendAnimArray.Num() - 1);
	if (idx < (uint32)m_TackleBlendAnimArray.Num())
	{
		return &m_TackleBlendAnimArray[idx];
	}
	return nullptr;
}


//===============================================================================
//===============================================================================
const FRugbyBlendNTypeRecord* FRugbyAnimationLibrary::GetBlendNTypeRec(wwDB_DTBLENDNTYPE_ENUM blendType) const
{
	const int32 typeIdx = (int32)blendType;
	ensureAlwaysMsgf(typeIdx < m_blendNTypeArray.Num(), TEXT("Blend N type %d is not valid."), typeIdx);
	if (typeIdx < m_blendNTypeArray.Num())
	{
		return &m_blendNTypeArray[typeIdx];
	}
	return nullptr;
}


//===============================================================================
//===============================================================================
const FRugbyBlendNRecord* FRugbyAnimationLibrary::GetBlendNRec(const int32 idx) const
{
	ensureAlwaysMsgf((int32)idx < m_blendNArray.Num(), TEXT("Blend N idx %d is out of valid array (max %d)."), idx, m_blendNArray.Num() - 1);
	if ((int32)idx < m_blendNArray.Num())
	{
		return &m_blendNArray[idx];
	}
	return nullptr;
}

//===============================================================================
//===============================================================================
const FRugbyBlendNRecord* FRugbyAnimationLibrary::GetBlendNRec(const FName uniqueId) const
{
	// We don't want to throw an error if key doesn't exist, we may want to fail them move on to check the blend target map instead
	FRugbyBlendNRecord* const* ppMappedRec = m_blendNMap.Find(uniqueId);
	if (ppMappedRec) // need to check that we got a valid pointer before attempting to dereference it
	{
		return *ppMappedRec;
	}
	return nullptr;
}

//===============================================================================
//===============================================================================
const FRugbyBlendSwitchRecord* FRugbyAnimationLibrary::GetBlendSwitchRec(const FName uniqueId) const
{
	// We don't want to throw an error if key doesn't exist, we may want to fail them move on to check the blend n map instead
	FRugbyBlendSwitchRecord* const* ppMappedRec = m_blendSwitchMap.Find(uniqueId);
	if (ppMappedRec) // need to check that we got a valid pointer before attempting to dereference it
	{
		return *ppMappedRec;
	}
	return nullptr;
}

//===============================================================================
//===============================================================================
const FRugbyBlendTargetRecord* FRugbyAnimationLibrary::GetBlendTargetRec(const int32 idx) const
{
	ensureAlwaysMsgf((int32)idx < m_blendTargetArray.Num(), TEXT("Blend target idx %d is out of valid array (max %d)."), idx, m_blendTargetArray.Num() - 1);
	if ((int32)idx < m_blendTargetArray.Num())
	{
		return &m_blendTargetArray[idx];
	}
	return nullptr;
}

//===============================================================================
//===============================================================================
const FRugbyBlendTargetRecord* FRugbyAnimationLibrary::GetBlendTargetRec(const FName uniqueId) const
{
	// We don't want to throw an error if key doesn't exist, we may want to fail them move on to check the blend n map instead
	FRugbyBlendTargetRecord* const* ppMappedRec = m_blendTargetMap.Find(uniqueId);
	if (ppMappedRec) // need to check that we got a valid pointer before attempting to dereference it
	{
		return *ppMappedRec;
	}
	return nullptr;
}

//===============================================================================
//===============================================================================
const FRugbyRuckMaulScrumTypeRecord*	FRugbyAnimationLibrary::GetRuckMaulScrumTypeRec(RUCKMAULSCRUM_TYPE Type) const
{
	const int32 typeIdx = (int32)Type;
	ensureAlwaysMsgf(typeIdx < m_RuckMaulScrumTypeArray.Num(), TEXT("RuckMaulScrum type %d is not valid."), typeIdx);
	if (typeIdx < m_RuckMaulScrumTypeArray.Num())
	{
		return &m_RuckMaulScrumTypeArray[typeIdx];
	}
	return nullptr;
}

//===============================================================================
//===============================================================================
const FRugbyRuckMaulScrumAnimRecord* FRugbyAnimationLibrary::GetRuckMaulScrumAnimRec(uint32 idx) const
{
	ensureAlwaysMsgf((int32)idx < m_RuckMaulScrumAnimArray.Num(), TEXT("Tackle animation idx %d is out of valid array (max %d)."), idx, m_RuckMaulScrumAnimArray.Num() - 1);
	if ((int32)idx < m_RuckMaulScrumAnimArray.Num())
	{
		return &m_RuckMaulScrumAnimArray[idx];
	}
	return nullptr;
}

//===============================================================================
//===============================================================================

const FRugbyFBTypeRec* FRugbyAnimationLibrary::GetFBActionTypeRec(ERugbyAnim_Mode_FullBodyActions Anim_ModeType) const
{
	const int32 typeIdx = (int32)Anim_ModeType;
	ensureAlwaysMsgf(typeIdx < m_FBTypeArray.Num(), TEXT("GetFBActionTypeRec type %d is not valid."), typeIdx);
	if (typeIdx < m_FBTypeArray.Num())
	{
		return &m_FBTypeArray[typeIdx];
	}
	return nullptr;
}

//===============================================================================
//===============================================================================

const FRugbyFullBodyAnimRec* FRugbyAnimationLibrary::GetFBActionAnimRec(uint32 idx) const
{
	ensureAlwaysMsgf((int32)idx < m_FBAnimRecArray.Num(), TEXT("GetFBActionAnimRec animation idx %d is out of valid array (max %d)."), idx, m_FBAnimRecArray.Num() - 1);
	if ((int32)idx < m_FBAnimRecArray.Num())
	{
		return &m_FBAnimRecArray[idx];
	}
	return nullptr;
}

//===============================================================================
//===============================================================================

const FRugbyUpperBodyActionsFBPassTypeRecord*	FRugbyAnimationLibrary::GetFBPassTypeRec(ERugbyAnim_Mode_UBActionsFBPass Anim_ModeType) const
{
	const int32 typeIdx = (int32)Anim_ModeType;
	ensureAlwaysMsgf(typeIdx < m_FBPassTypeArray.Num(), TEXT("GetFBPassTypeRec type %d is not valid."), typeIdx);
	if (typeIdx < m_FBPassTypeArray.Num())
	{
		return &m_FBPassTypeArray[typeIdx];
	}
	return nullptr;
}

//===============================================================================
//===============================================================================

const FRugbyUpperBodyActionsFBPassAnimRecord*	FRugbyAnimationLibrary::GetFBPassAnimRec(uint32 idx) const
{
	ensureAlwaysMsgf((int32)idx < m_FBPassAnimArray.Num(), TEXT("GetFBPassAnimRec animation idx %d is out of valid array (max %d)."), idx, m_FBPassAnimArray.Num() - 1);
	if ((int32)idx < m_FBPassAnimArray.Num())
	{
		return &m_FBPassAnimArray[idx];
	}
	return nullptr;
}

//===============================================================================
//===============================================================================

const FRugbyUpperBodyActionsUBPassTypeRecord*	FRugbyAnimationLibrary::GetUpperBodyTypeRec(ERugbyAnim_Mode_UBActions Anim_ModeType) const
{
	const int32 typeIdx = (int32)Anim_ModeType;
	ensureAlwaysMsgf(typeIdx < m_UpperBodyActionsPassTypeArray.Num(), TEXT("GetUpperBodyTypeRec type %d is not valid."), typeIdx);
	if (typeIdx < m_UpperBodyActionsPassTypeArray.Num())
	{
		return &m_UpperBodyActionsPassTypeArray[typeIdx];
	}
	return nullptr;
}

//===============================================================================
//===============================================================================

const FRugbyUpperBodyActionsUBPassAnimRecord*	FRugbyAnimationLibrary::GetUpperBodyAnimRec(uint32 idx) const
{
	ensureAlwaysMsgf((int32)idx < m_UpperBodyActionsPassAnimArray.Num(), TEXT("GetUpperBodyAnimRec animation idx %d is out of valid array (max %d)."), idx, m_UpperBodyActionsPassAnimArray.Num() - 1);
	if ((int32)idx < m_UpperBodyActionsPassAnimArray.Num())
	{
		return &m_UpperBodyActionsPassAnimArray[idx];
	}
	return nullptr;
}


//===============================================================================
//===============================================================================

const FRugbyFBActionsTryRecord* FRugbyAnimationLibrary::GetFBActionsTryRec(uint32 idx) const
{
	ensureAlwaysMsgf((int32)idx < m_FBActionsTryRecordArray.Num(), TEXT("GetFBActionsTryRec animation idx %d is out of valid array (max %d)."), idx, m_FBActionsTryRecordArray.Num() - 1);
	if ((int32)idx < m_FBActionsTryRecordArray.Num())
	{
		return &m_FBActionsTryRecordArray[idx];
	}
	return nullptr;	
}

//===============================================================================
//===============================================================================

const FRugbyFBActionsTryTypeRecord* FRugbyAnimationLibrary::GetFBActionsTryTypeRec(TryGroupType TryType) const
{
	const int32 typeIdx = (int32)TryType;
	ensureAlwaysMsgf(typeIdx < m_FBActionsTryTypeRecordArray.Num(), TEXT("GetFBActionsTryTypeRec type %d is not valid."), typeIdx);
	if (typeIdx < m_FBActionsTryTypeRecordArray.Num())
	{
		return &m_FBActionsTryTypeRecordArray[typeIdx];
	}
	return nullptr;
}

//===============================================================================
//===============================================================================
void FRugbyAnimationLibrary::LoadTryAnimations()
{
	UE_LOG(LogTemp, Log, TEXT("Loading Try Animations........................"));

	// Get data from data table
	TArray<FRugbyFBTryAnimDef*> tableRows;
	const uint32 entryCount = GetDataTableRows(wwANIMLIB_FBTRY_ANIM_DT_PATH, tableRows);
	// Ensure there is a valid contact event.
	float eventStartTime = -1.0f;
	float eventEndTime = -1.0f;

	// Need to preallocate arrays otherwise they will be resized while populating it which will mess up the pointers to the recs that get stored in the type records. 		
	m_FBActionsTryRecordArray.Empty(entryCount);
	m_FBActionsTryRecordArray.SetNum(entryCount);
	m_FBActionsTryTypeRecordArray.Empty((uint32)TryGroupType::MAX);
	m_FBActionsTryTypeRecordArray.SetNum((uint32)TryGroupType::MAX);

	for (uint32 recordIdx = 0; recordIdx < entryCount; recordIdx++)
	{
		FRugbyFBTryAnimDef& currentRecDef = *tableRows[recordIdx];
		FRugbyFBActionsTryRecord& record = m_FBActionsTryRecordArray[recordIdx];

		// Copy data we care about from rec		
		record.m_Type = currentRecDef.Type;			
		record.m_bMirrored = currentRecDef.Mirrored;
		record.m_animName = currentRecDef.SourceNode;		
		record.m_pAnimSequence = GetAnimSequenceByName(currentRecDef.AnimName, record.m_bMirrored);
		record.m_isSliding = GetDurationEventTime(record.m_pAnimSequence, "sliding", eventStartTime, eventEndTime) == -1.0f ? false : true;

		m_FBActionsTryTypeRecordArray[(uint32)record.m_Type].m_Type = record.m_Type;
		m_FBActionsTryTypeRecordArray[(uint32)record.m_Type].m_animVariants.Add(&record);		
		{
			// Ensure there is a valid contact event.
			eventStartTime = -1.0f;
			eventEndTime = -1.0f;
			
			GetDurationEventTime ( record.m_pAnimSequence, "TouchingDown", eventStartTime, eventEndTime );//Touching_Down

			if (eventStartTime < 0.0f || eventEndTime < 0.0f)
			{
				UE_LOG(LogTemp, Error, TEXT("Could not find a valid duration event track for %s"), *currentRecDef.AnimName);
				record.m_bEnabled = false;
			}
			else
			{
				// Derive contact data for this animation.
				static const FName BoneName("ball");

				record.m_tryStartVectors = ComputeTryVector(record.m_pAnimSequence, BoneName, eventStartTime);
				record.m_tryEndVectors = ComputeTryVector(record.m_pAnimSequence, BoneName, eventEndTime);
			}
		}

		if (nullptr == record.m_pAnimSequence)
		{
			record.m_bEnabled = false;
			UE_LOG(LogTemp, Error, TEXT("LoadTryAnimations: Missing animation %s"), *currentRecDef.AnimName);

		}
	}
}


//===============================================================================
//===============================================================================
void FRugbyAnimationLibrary::LoadFullBodyAnimations()
{
	UE_LOG(LogTemp, Log, TEXT("Loading FullBodyActions Animations........................"));

	// Get data from data table
	TArray<FRugbyFBAnimDef*> tableRows;
	const uint32 entryCount = GetDataTableRows(wwANIMLIB_FB_ANIM_DT_PATH, tableRows);

	ensureAlways ( entryCount > 0 );

	// Need to preallocate arrays otherwise they will be resized while populating it which will mess up the pointers to the recs that get stored in the type records. 		
	m_FBAnimRecArray.Empty(entryCount);
	m_FBAnimRecArray.SetNum(entryCount);
	m_FBTypeArray.Empty((uint32)ERugbyAnim_Mode_FullBodyActions::MAX);
	m_FBTypeArray.SetNum((uint32)ERugbyAnim_Mode_FullBodyActions::MAX);
	
	for (uint32 recordIdx = 0; recordIdx < entryCount; recordIdx++)
	{
		FRugbyFBAnimDef& currentRecDef = *tableRows[recordIdx];
		FRugbyFullBodyAnimRec& record = m_FBAnimRecArray[recordIdx];

		// Copy data we care about from rec		
		record.m_FBType = currentRecDef.Type;
		record.m_Subtype = FName (*currentRecDef.SubType);
		record.m_TypeName = currentRecDef.TypeName;
		record.m_bMirrored = currentRecDef.Mirrored;
		record.m_IkBitMask = currentRecDef.IkBitMask;
		record.m_targetSlot = currentRecDef.UpperBody ? ANIM_TARGET_SLOT::UPPER_BODY : ANIM_TARGET_SLOT::FULL_BODY;
		record.m_animName = currentRecDef.StateMachinePath;
		record.m_pAnimSequence = GetAnimSequenceByName(currentRecDef.AnimName, record.m_bMirrored);

		m_FBTypeArray[(uint32)record.m_FBType].m_Type = record.m_FBType;
		m_FBTypeArray[(uint32)record.m_FBType].m_animVariants.Add(&record);		
		
		if (nullptr == record.m_pAnimSequence)
		{
			record.m_bEnabled = false;
			UE_LOG(LogTemp, Error, TEXT("LoadFullBodyAnimations: Missing animation %s"), *currentRecDef.AnimName);

		}
		else 
		{
			//logic to find contact point by event for kick for goal. This is specific to one animation penalty_kick_right_03 which has the footstep event.
			//this will be used in ShootForGoal logic.
			if (((record.m_FBType == ERugbyAnim_Mode_FullBodyActions::KickLeftFoot) || (record.m_FBType == ERugbyAnim_Mode_FullBodyActions::KickRightFoot)) &&			
				(record.m_Subtype == "penalty_kick_right_03"))
			{
				// Ensure there is a valid contact event.
				float animLength = 0.0f;
				const float contactTime = GetContactEventTime(record.m_pAnimSequence, "Footsteps", animLength);

				if (contactTime >= 0.0f )
				{
					// Derive contact data
					static const FName ballBoneName("ball");
				
					if (record.m_FBType == ERugbyAnim_Mode_FullBodyActions::KickLeftFoot)
					{
						m_LeftPenaltyKickContact = ComputeStaticContactFrame(record.m_pAnimSequence, ballBoneName, contactTime, false, record.m_playRate);
					}
					else //must be right
					{
						m_RightPenaltyKickContact = ComputeStaticContactFrame(record.m_pAnimSequence, ballBoneName, contactTime, false, record.m_playRate);
					}
				}
				else
				{
					UE_DEBUG_BREAK(); //we should not reach here, if we do check the animation file wbpenaltykick03 has any footstep event at the start...
				}
			}
		}
	}
}

//===============================================================================
//===============================================================================
void FRugbyAnimationLibrary::LoadRuckMaulScrumAnimations()
{
	UE_LOG(LogTemp, Log, TEXT("Loading RuckMaulScrum Animations........................"));

	// Get data from data table
	TArray<FRugbyRuckMaulScrumRecordDef*> tableRows;
	const uint32 entryCount = GetDataTableRows(wwANIMLIB_RUCKMAUL_ANIM_DT_PATH, tableRows);		

	// Need to preallocate arrays otherwise they will be resized while populating it which will mess up the pointers to the recs that get stored in the type records. 	
	m_RuckMaulScrumTypeArray.Empty((uint32)RUCKMAULSCRUM_TYPE::MAX);
	m_RuckMaulScrumTypeArray.SetNum((uint32)RUCKMAULSCRUM_TYPE::MAX);
	m_RuckMaulScrumAnimArray.Empty(entryCount);
	m_RuckMaulScrumAnimArray.SetNum(entryCount);

	for (uint32 recordIdx = 0; recordIdx < entryCount; recordIdx++)
	{
		FRugbyRuckMaulScrumRecordDef& currentRecDef = *tableRows[recordIdx];
		FRugbyRuckMaulScrumAnimRecord& record = m_RuckMaulScrumAnimArray[recordIdx];

		// Copy data we care about from rec		
		record.m_Type = currentRecDef.Type;
		record.m_Subtype = currentRecDef.SubType;		
		record.m_ImpactType = currentRecDef.ImpactType;
		record.m_Position = currentRecDef.Position;
		record.m_Facing = currentRecDef.Facing;
		record.m_animName = currentRecDef.SourceNode;		
		record.m_loopAlways = currentRecDef.IsLoop;		
		record.m_bMirrored = currentRecDef.Mirrored;
		record.m_NodeID = currentRecDef.NodeID;
		record.m_IsAggresive = currentRecDef.IsAggresive;
		record.m_pAnimSequence = GetAnimSequenceByName(currentRecDef.AnimName, record.m_bMirrored);

		m_RuckMaulScrumTypeArray[(uint32)record.m_Type].m_Type = record.m_Type;
		m_RuckMaulScrumTypeArray[(uint32)record.m_Type].m_animVariants.Add(&record);

		if (nullptr == record.m_pAnimSequence)
		{
			record.m_bEnabled = false;
			UE_LOG(LogTemp, Error, TEXT("LoadRuckMaulAnimations: Missing animation %s"), *currentRecDef.AnimName);
		}
		else
		{
			if (currentRecDef.IsContactNode)
			{
				// Ensure there is a valid contact event.
				float animLength = 0.0f;
				const float contactTime = GetContactEventTime(record.m_pAnimSequence, "ruck_contact", animLength);

				if (contactTime < 0.0f)
				{
					UE_LOG(LogTemp, Error, TEXT("Could not find a valid contact event track for %s"), *currentRecDef.AnimName);
					record.m_bEnabled = false;
				}
				else
				{
					// Derive contact data for this animation.
					static const FName BoneName("tacklerAttach");

					// see RC3's ContactAnimDef::calculateAnimatedContacts
					constexpr const float INTERVAL = 0.02f;

					// assuming the old code wanted contactTime in seconds here
					const int32 numAnimatedContacts = FMath::FloorToInt(contactTime / INTERVAL);

					record.m_animatedContactList.Reserve(numAnimatedContacts);

					for (int32 sampleIndex = 0; sampleIndex <= numAnimatedContacts; ++sampleIndex)
					{
						const float sampleTime = INTERVAL * float(sampleIndex);
						record.m_animatedContactList.Add(ComputeAnimatedContactFrame(record.m_pAnimSequence, BoneName, sampleTime, 1.0f, true));
					}

					// I think this should always be true?
					if (contactTime - INTERVAL * float(numAnimatedContacts) > 0.f)
					{
						record.m_animatedContactList.Add(ComputeAnimatedContactFrame(record.m_pAnimSequence, BoneName, contactTime, 1.0f, true));
					}
				}
			}
		}
		
	}
}
#pragma optimize("", off)
//===============================================================================
//===============================================================================
void FRugbyAnimationLibrary::LoadTackleAnimations()
{
	UE_LOG(LogTemp, Log, TEXT("Loading Tackle Animations........................"));
		
	// Get data from data table
	TArray<FRugbyTackleBlendRecordDef*> tableRows;
	const uint32 entryCount = GetDataTableRows(wwANIMLIB_TACKLE_ANIM_DT_PATH, tableRows);

	float eventStartTime = -1.0f;
	float eventEndTime = -1.0f;
	
	// Need to preallocate arrays otherwise they will be resized while populating it which will mess up the pointers to the recs that get stored in the type records. 
	m_TackleTypeArray.Empty((uint32)ERugbyAnim_Mode_Tackles::MAX);
	m_TackleTypeArray.SetNum((uint32)ERugbyAnim_Mode_Tackles::MAX);
	m_TackleBlendAnimArray.Empty(entryCount);
	m_TackleBlendAnimArray.SetNum(entryCount);


	for (uint32 recordIdx = 0; recordIdx < entryCount; recordIdx++)
	{
		FRugbyTackleBlendRecordDef& currentRecDef = *tableRows[recordIdx];
		FRugbyTackleBlendAnimRecord& record = m_TackleBlendAnimArray[recordIdx];

		// Copy data we care about from rec		
		record.m_Type = currentRecDef.Type;		
		record.m_SubType = currentRecDef.SubType;
		record.m_TryTackleType = currentRecDef.TryTackleType;
		record.m_Dominance = currentRecDef.Dominance;		
		record.m_bMirrored = currentRecDef.Mirrored;
		record.m_ImpactArea = currentRecDef.ImpactArea;
		record.m_IsGround = currentRecDef.IsGround;
		record.m_animName = currentRecDef.AnimName;
		record.m_stateMachinePath = currentRecDef.SourceNode;
		record.m_targetSlot = ANIM_TARGET_SLOT::TACKLE;

		record.m_pAnimSequence = GetAnimSequenceByName(currentRecDef.AnimName, record.m_bMirrored);

		m_TackleTypeArray[(uint32)record.m_Type].m_TackleType = record.m_Type;
		m_TackleTypeArray[(uint32)record.m_Type].m_animVariants.Add(&record);

		if (nullptr == record.m_pAnimSequence)
		{
			record.m_bEnabled = false;
			UE_LOG(LogTemp, Error, TEXT("LoadTackleAnimations: Missing animation %s"), *currentRecDef.AnimName);
		}
		else
		{
#if !UE_BUILD_SHIPPING
			const bool bFailedAnkleTap = record.m_Type == ERugbyAnim_Mode_Tackles::ankle_tap_tacklee && !record.m_pAnimSequence->GetName().Contains("_success");
			if (record.m_pAnimSequence->GetName().StartsWith("te") && !record.m_pAnimSequence->GetName().Contains("struggle") && 
				record.m_Type != ERugbyAnim_Mode_Tackles::contested_tacklee && record.m_Type != ERugbyAnim_Mode_Tackles::fend_success_tacklee && 
				record.m_Type != ERugbyAnim_Mode_Tackles::sidestep_success_tacklee && record.m_Type != ERugbyAnim_Mode_Tackles::standard_fail_tacklee &&
				!bFailedAnkleTap)
			{
				const bool bHasOnGroundEvent = URugbyCharacterAnimInstance::GetNotifyFromAnimSequence(record.m_pAnimSequence, ERugbyAnimEvent::ON_GROUND_DURATION_EVENT) != nullptr;
				if (!bHasOnGroundEvent)
				{
					UE_LOG(LogTemp, Error, TEXT("No On Ground Event for %s.   Type is: %s"), *currentRecDef.AnimName, *ENUM_TO_FSTRING(ERugbyAnim_Mode_Tackles, record.m_Type));
				}
			}
#endif

			if (currentRecDef.IsContactNode)
			{
				// Ensure there is a valid contact event.
				float animLength = 0.0f;
			
				FName EventName("TackleContact");
				FName BoneName("tacklerAttach");			

				const float contactTime = GetContactEventTime(record.m_pAnimSequence, EventName, animLength);

				if (contactTime < 0.0f)
				{
					if (record.m_Type != ERugbyAnim_Mode_Tackles::dive_miss_tackler)
					{
						UE_LOG(LogTemp, Error, TEXT("Could not find a valid contact event track for %s,   Event: %s,   Type: %s"), *currentRecDef.AnimName, *EventName.ToString(), *ENUM_TO_FSTRING(ERugbyAnim_Mode_Tackles, record.m_Type));
						record.m_bEnabled = false;
					}
				}
				else
				{
					// Derive contact data for this animation.
					BoneName = "tacklerAttach";
					record.m_staticContact = ComputeStaticContactFrame(record.m_pAnimSequence, BoneName, 0.0, true);
					record.m_staticContact.time = contactTime;

					// see RC3's ContactAnimDef::calculateAnimatedContacts
					constexpr const float INTERVAL = 0.1f;

					// assuming the old code wanted contactTime in seconds here
					const int32 numAnimatedContacts = FMath::FloorToInt(contactTime / INTERVAL);

					record.m_animatedContactList.Reserve(numAnimatedContacts);

					for (int32 sampleIndex = 0; sampleIndex <= numAnimatedContacts; ++sampleIndex)
					{
						const float sampleTime = INTERVAL * float(sampleIndex);
						record.m_animatedContactList.Add(ComputeAnimatedContactFrame(record.m_pAnimSequence, BoneName, sampleTime));
					}
				
					if (contactTime - INTERVAL * float(numAnimatedContacts) > 0.f)
					{
						record.m_animatedContactList.Add(ComputeAnimatedContactFrame(record.m_pAnimSequence, BoneName, contactTime));
					}
				}
				
				static const FName BallBoneName("ball");

				//for try node only...
				//RugNode_160_TryTackle
				if (record.m_Type == ERugbyAnim_Mode_Tackles::Try || record.m_Type == ERugbyAnim_Mode_Tackles::try_corner)
				{
					// Ensure there is a valid contact event.
					eventStartTime = -1.0f;
					eventEndTime = -1.0f;
					/*
					00: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|try|LikelyTryTackle
					01: player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|try|MarginalTryTackle
					*/

					if (record.m_Type == ERugbyAnim_Mode_Tackles::try_corner) //for try corner there are no events. Noy sure why???
					{
						EventName = "TouchingDown";
					}
					else if (record.m_Type == ERugbyAnim_Mode_Tackles::Try)
					{
						if ((TRY_TACKLE_TYPE::TTT_SUCCESS_LIKELY == record.m_TryTackleType) || (TRY_TACKLE_TYPE::TTT_SUCCESS_MARGINAL == record.m_TryTackleType))
						{
							EventName = "TouchingDown";
						}
						//player_cinematicRef|null|player_tacklesRef|tackle_blend|tackles|try|UnlikelyTryTackle
						else if (TRY_TACKLE_TYPE::TTT_FAIL_LIKELY == record.m_TryTackleType)
						{
							EventName = "TackleReleaseBall"; //"BallRelease";
						}
						else if (TRY_TACKLE_TYPE::TTT_HELDUP == record.m_TryTackleType)
						{
							EventName = "held_up";
						}
						else
						{
							UE_DEBUG_BREAK();
						}
					}

					GetDurationEventTime(record.m_pAnimSequence, EventName, eventStartTime, eventEndTime);//Touching_Down

					if (eventStartTime < 0.0f || eventEndTime < 0.0f)
					{
						// This is potentially fine, don't spam the logs with it. Only print an error if we fail the second check
						//UE_LOG(LogTemp, Warning, TEXT("Could not find a valid duration event track for %s,    Event: %s,    Type: %s"), *currentRecDef.AnimName, *EventName.ToString(), *ENUM_TO_FSTRING(ERugbyAnim_Mode_Tackles, record.m_Type));
						
						//may be just event not duration event
						//Then sent end to zero
						//eventLength = ( eventTrack->type() == EVENT_TRACK_DURATION ) ? eventTrack->getEvent(0)->length() : 0.0f;
						const float contactTime2 = GetContactEventTime(record.m_pAnimSequence, EventName, animLength);

						if (contactTime2 < 0.0f)
						{
							if (!currentRecDef.AnimName.StartsWith("tr"))
							{
								// second check failed, print an error
								UE_LOG(LogTemp, Error, TEXT("Could not find a valid contact event track for %s,    Event: %s,    Type: %s"), *currentRecDef.AnimName, *EventName.ToString(), *ENUM_TO_FSTRING(ERugbyAnim_Mode_Tackles, record.m_Type));
								record.m_bEnabled = false;
							}
						}
						else
						{
							eventStartTime = eventEndTime = contactTime2;
						}
						// Derive contact data for this animation.						
						record.m_tryStartVectors = ComputeTryVector(record.m_pAnimSequence, BallBoneName, eventStartTime);
						record.m_tryEndVectors = ComputeTryVector(record.m_pAnimSequence, BallBoneName, eventEndTime);
					}
					else
					{
						// Derive contact data for this animation.						
						record.m_tryStartVectors = ComputeTryVector(record.m_pAnimSequence, BallBoneName, eventStartTime);
						record.m_tryEndVectors = ComputeTryVector(record.m_pAnimSequence, BallBoneName, eventEndTime);
					}
				}
			}
		}
	}
	wwDO_NOTHING;
}
#pragma optimize("", on)

//===============================================================================
//===============================================================================
void FRugbyAnimationLibrary::LoadUpperBodyActionsFBPassAnimations() //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|fullbody_passing
{
	// Get data from data table
	TArray<FRugbyFullBodyPassDef*> tableRows;
	const uint32 entryCount = GetDataTableRows(wwANIMLIB_FB_PASS_ANIM_DT_PATH, tableRows);

	// Need to preallocate arrays otherwise they will be resized while populating it which will mess up the pointers to the recs that get stored in the type records. 
	m_FBPassTypeArray.Empty((uint32)ERugbyAnim_Mode_UBActionsFBPass::MAX);
	m_FBPassTypeArray.SetNum((uint32)ERugbyAnim_Mode_UBActionsFBPass::MAX);
	m_FBPassAnimArray.Empty(entryCount);
	m_FBPassAnimArray.SetNum(entryCount);


	for (uint32 recordIdx = 0; recordIdx < entryCount; recordIdx++)
	{
		FRugbyFullBodyPassDef& currentRecDef = *tableRows[recordIdx];
		FRugbyUpperBodyActionsFBPassAnimRecord& record = m_FBPassAnimArray[recordIdx];

		// Copy data we care about from rec				
		record.m_Type = currentRecDef.Type;
		record.m_SubType = currentRecDef.SubType;		
		record.m_bMirrored = currentRecDef.Mirrored;
		record.m_targetSlot = ANIM_TARGET_SLOT::FULL_BODY_PASSING;
		record.m_animName = currentRecDef.SourceNode;
		record.m_pAnimSequence = GetAnimSequenceByName(currentRecDef.AnimName, record.m_bMirrored);
		record.m_loopAlways = currentRecDef.IsLoop;
		m_FBPassTypeArray[(uint32)record.m_Type].m_Anim_MachineModeType = record.m_Type;
		m_FBPassTypeArray[(uint32)record.m_Type].m_animVariants.Add(&record);

		if (nullptr == record.m_pAnimSequence)
		{
			record.m_bEnabled = false;
			UE_LOG(LogTemp, Error, TEXT("LoadUpperBodyActionsAnimations: Missing animation %s"), *currentRecDef.AnimName);
		}
	}
}


//===============================================================================
//===============================================================================
void FRugbyAnimationLibrary::LoadUpperBodyActionsPasses() //player_cinematicRef|null|player_tacklesRef|player_upperbodyRef|upper_body_actions|actions
{
	// Get data from data table
	TArray<FRugbyUpperBodyActionsPassDef*> tableRows;
	const uint32 entryCount = GetDataTableRows(wwANIMLIB_UB_ACTIONS_ANIM_DT_PATH, tableRows);

	// Need to preallocate arrays otherwise they will be resized while populating it which will mess up the pointers to the recs that get stored in the type records. 
	m_UpperBodyActionsPassTypeArray.Empty((uint32)ERugbyAnim_Mode_UBActions::MAX);
	m_UpperBodyActionsPassTypeArray.SetNum((uint32)ERugbyAnim_Mode_UBActions::MAX);
	m_UpperBodyActionsPassAnimArray.Empty(entryCount);
	m_UpperBodyActionsPassAnimArray.SetNum(entryCount);


	for (uint32 recordIdx = 0; recordIdx < entryCount; recordIdx++)
	{
		FRugbyUpperBodyActionsPassDef& currentRecDef = *tableRows[recordIdx];
		FRugbyUpperBodyActionsUBPassAnimRecord& record = m_UpperBodyActionsPassAnimArray[recordIdx];

		// Copy data we care about from rec				
		record.m_Type = currentRecDef.Type;
		record.m_SubType = currentRecDef.SubType;
		record.m_PassType = currentRecDef.PassType;
		record.m_bMirrored = currentRecDef.Mirrored;
		record.m_targetSlot = currentRecDef.UpperBody ? ANIM_TARGET_SLOT::UPPER_BODY : ANIM_TARGET_SLOT::FULL_BODY;
		record.m_animName = currentRecDef.SourceNode;
		record.m_pAnimSequence = GetAnimSequenceByName(currentRecDef.AnimName, record.m_bMirrored);
		record.m_loopAlways = currentRecDef.IsLoop;
		m_UpperBodyActionsPassTypeArray[(uint32)record.m_Type].m_Anim_MachineModeType = record.m_Type;
		m_UpperBodyActionsPassTypeArray[(uint32)record.m_Type].m_animVariants.Add(&record);

		if (nullptr == record.m_pAnimSequence)
		{
			record.m_bEnabled = false;
			UE_LOG(LogTemp, Error, TEXT("LoadUpperBodyActionsAnimations: Missing animation %s"), *currentRecDef.AnimName);
		}
	}
}

//===============================================================================
//===============================================================================
const TArray<FRugbyAnimRecBase*> FRugbyAnimationLibrary::GetAnimationsOfClosestAnimType(CLOSEST_ANIM_TYPE type) const
{
	TArray<FRugbyAnimRecBase*> usableAnimationList;

	if (type < CLOSEST_ANIM_TYPE::MAX)
	{
		const FRugbyClosestAnimTypeRecord* pTypeRec = GetClosestAnimTypeRec(type);
		if (pTypeRec && pTypeRec->m_animVariants.Num() > 0)
		{
			for (FRugbyClosestAnimRecord* pAnimRec : pTypeRec->m_animVariants)
			{
				if (pAnimRec->m_bEnabled && pAnimRec->m_pAnimSequence)
				{
					usableAnimationList.Add(pAnimRec);
				}
			}

			ensureAlways(usableAnimationList.Num() > 0);
		}
	}

	return usableAnimationList;
}

//===============================================================================
//This function is no longer used, but retained here in case needed for debugging...
//===============================================================================
//const FRugbyRequestAnimRec* FRugbyAnimationLibrary::GetRandomRequestAnim(wwDB_DTREQUESTTYPE_ENUM animType) const
//{
//	if (animType < wwDB_DTREQUESTTYPE_ENUM::MAX)
//	{
//		const FRugbyRequestTypeRec* pRequestTypeRec = &m_requestTypeArray[(uint32)animType];
//		if (pRequestTypeRec && pRequestTypeRec->m_animVariants.Num() > 0)
//		{
//			// hacky bullshit required to not randomly select animations that are disabled or don't currently exist.
//			TArray<FRugbyRequestAnimRec*> usableAnimationList;
//			for (FRugbyRequestAnimRec* pAnimRec : pRequestTypeRec->m_animVariants)
//			{
//				if (pAnimRec->m_bEnabled && pAnimRec->m_pAnimSequence)
//				{
//					usableAnimationList.Add(pAnimRec);
//				}
//			}
//			ensureAlways(usableAnimationList.Num() > 0);
//
//			if (usableAnimationList.Num() > 0)
//			{
//				return usableAnimationList[rand() % usableAnimationList.Num()];
//			}
//		}
//	}
//
//	return nullptr;
//}

//===============================================================================
//===============================================================================
const FRugbyIdleAnimRec* FRugbyAnimationLibrary::FindIdleAnimation(wwDB_IDLE_ENUM animType, float randIdxOverride, bool bUseRand, FVector idleCenter, float idleRadius) const
{
	if (animType < wwDB_IDLE_ENUM::MAX)
	{
		const FRugbyIdleAnimTypeRec* pRequestTypeRec = &m_IdleTypeArray[(uint32)animType];
		if (pRequestTypeRec && pRequestTypeRec->m_IdleAnimVariants.Num() > 0)
		{			
			TArray<FRugbyIdleAnimRec*> usableAnimationList;
			for (FRugbyIdleAnimRec* pAnimRec : pRequestTypeRec->m_IdleAnimVariants)
			{
				if (pAnimRec->m_pAnimSequence && (pAnimRec->m_animType == animType))
				{
					usableAnimationList.Add(pAnimRec);
				}
			}

			ensureAlways(usableAnimationList.Num() > 0);

			if (usableAnimationList.Num() > 0)
			{
				if (bUseRand) //this is for dummy_half
				{
					int32 randIdx = FMath::RoundToInt(randIdxOverride * usableAnimationList.Num());
					return usableAnimationList[randIdx % usableAnimationList.Num()];
				}
				else //other idle
				{					
					int32 randIdx = FRugbyAnimationLibrary::GetIdleIndex(randIdxOverride, idleCenter, idleRadius, usableAnimationList);

					if (randIdx >= 0 && randIdx < usableAnimationList.Num())
					{
						return usableAnimationList[randIdx];
					}
					else
					{
						return usableAnimationList[0];
					}
				}
			}
		}
	}
	return nullptr;
}

//===============================================================================
//===============================================================================
/*static*/ int FRugbyAnimationLibrary::GetIdleIndex(float random, const FVector& centre, float radius, TArray <FRugbyIdleAnimRec*> usableAnimationList)
{		
	int numNodes = usableAnimationList.Num();

	if ( numNodes <= 0 )
		return 0;

	int initialIndex = FMath::Clamp(int(FMath::FloorToFloat(FMath::Clamp(random, 0.0f, 1.0f) * float(numNodes))), 0, numNodes - 1);
	int index = initialIndex;
	while (index < numNodes && (centre - usableAnimationList[index]->m_IdleVector).Magnitude() > radius)
	{
		++index;
	}

	if (index >= numNodes)
	{
		index = 0;
		while (index < initialIndex && (centre - usableAnimationList[index]->m_IdleVector).Magnitude() > radius)
		{
			++index;
		}
	}

	return index;
}

////===============================================================================
////===============================================================================
//void FRugbyAnimationLibrary::GetAnimDatabyPrefix(const char** animationPrefix, TArray <const FRugbyRequestAnimRec*> &AnimRec) const
//{
//	// Iterate over all nodes
//	for (auto&it : m_requestAnimRecArray)
//	{	
//		{
//			// Iterate over all prefixes
//			const char** animation_prefix = animationPrefix;
//			while (*animation_prefix)
//			{
//				FString AnimPrefix (*animation_prefix);
//				FString node_name = it.m_stateMachinePath;
//				UE_LOG(LogTemp, Display, TEXT("GetAnimDatabyPrefix:: Anim Name %s"), *node_name);
//				if (node_name.Contains(AnimPrefix))
//				{
//					AnimRec.Add(&it);
//				}
//				++animation_prefix;
//			}
//		}
//	}	
//}
